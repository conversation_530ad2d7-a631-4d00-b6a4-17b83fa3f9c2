<template>
  <div
    class="step"
    :id="stepData.id"
    :class="{ mask: +stepData.sortNum > 0, active: stepData.showMask }"
  >
    <div class="close-icon" @click="closeFn" v-show="+stepData.sortNum > 0">
      <b class="icon-guanbi"></b>
    </div>
    <half-design-title2
      v-show="+stepData.sortNum > 0"
      :valueName="stepData.onlyOneChild"
      :stepNum="stepData.sortNum"
      :title="stepData.stepTitle"
      v-on="$listeners"
    >
      <span
        slot="suffix"
        class="suffixStyle"
        :class="{ sockSuffix: isSocks }"
        @click="suffixClickFn"
        >{{ suffixText }}</span
      >
    </half-design-title2>
    <div
      v-for="item in stepData.attributeCategoryList"
      :key="item.id"
      :id="item.id"
      v-show="showStep(item)"
      :data-id="item.id"
    >
      <components
        :is="item.styleClass"
        :stepData="item"
        :data-name="`${item.styleClass + '_' + item.id}`"
        :moreUpload="needMoreUpload"
        :needUploadDialog="needUploadDialog"
        v-on="$listeners"
        @disabledNextBtn="disabledNextBtn"
      >
        <template v-if="(item.isShowPrompt||canShowStepText) && item.promptRemark" v-slot:stepText>
          <div class="stepText">{{ item.promptRemark }}</div>
        </template>
      </components>
    </div>
    <div
      class="next"
      :id="`mobileNextBtn${stepData.sortNum}`"
      :disabled="disabledBtn"
      v-if="stepData.sortNum != lastStep && !isMedals && !isCoins && !isMbNext"
      :class="[
        {
          nextBtn2: theme == '10',
          nextBtn: theme != '10' && theme != '11',
          noNextBtn: theme == '11',
          luggagetags: isLuggagetags,
        },
      ]"
      @click="goNext"
    >
      {{ langSemiCustom.next }}
    </div>
    <div
      class="nextStep"
      v-else-if="isMedals && needNextStep.includes(stepData.id)"
      :disabled="!canNextStep"
      @click="nextStep"
    >
      <nextStep :style="{ opacity: canNextStep ? 1 : 0.5 }"></nextStep>
    </div>
    <modalHalfDetailComponentsSockSizeDialog
      v-if="isSocks && hasSizeStep"
      :showDialog.sync="socksSizeTip"
    >
    </modalHalfDetailComponentsSockSizeDialog>
  </div>
</template>

<script>
import nextStep from "@/components/HalfDesign/customMedals/common/nextStep";
import HalfDesignCustomCoinsPrintStyle1 from "@/components/HalfDesign/customCoins/print/Style1";
import halfDesignCustomCoinsUploadDesignText from "@/components/HalfDesign/customCoins/upload/designText";
import HalfDesignCustomCoinsDiscountStyle1 from "@/components/HalfDesign/customCoins/discount/Style1";
export default {
  props: {
    stepData: {
      type: Object,
      default: {},
    },
    isMedals: {
      type: Boolean,
      default: false,
    },
    isCoins: {
      type: Boolean,
      default: false,
    },
    isOrnaments: {
      type: Boolean,
      default: false,
    },
    isSocks: {
      type: Boolean,
      default: false,
    },
    isBeltBuckles: {
      type: Boolean,
      default: false,
    },
    isPokerChips: {
      type: Boolean,
      default: false,
    },
    isMbNext: {
      type: Boolean,
      default: false,
    },
    needNextStep: {
      type: Array,
      default: [],
    },
    paramsArr: {
      type: Array,
      default: [],
    },
    needNextStepObj: {
      type: Object,
      default: {},
    },
    errStep: {
      type: Array,
      default: [],
    },
  },
  inject: ["getEmailLater", "getStep3Show"],
  components: {
    nextStep,
    HalfDesignCustomCoinsPrintStyle1,
    halfDesignCustomCoinsUploadDesignText,
    HalfDesignCustomCoinsDiscountStyle1,
  },
  data() {
    return {
      theme: 0,
      disabledBtn: false,
      socksSizeTip: false,
    };
  },
  computed: {
    langSemiCustom() {
      return this.$store.getters.lang?.semiCustom;
    },
    langQuote() {
      return this.$store.getters.lang.quote || {};
    },
    emailLater() {
      return this.getEmailLater();
    },
    step3Show() {
      return this.getStep3Show();
    },
    lastStep() {
      return this.$store.state.halfDesign.lastStep;
    },
    isLuggagetags() {
      return this.$store.state.halfDesign.isLuggagetags;
    },
    modeType() {
      return this.$store.state.halfDesign.modeType;
    },
    canShowPrintColorPrice() {
      return this.$store.state.halfDesign.canShowPrintColorPrice;
    },
    canNextStep() {
      for (const item of this.errStep) {
        const result = this.checkIdCanNextFn(item);
        if (!result) return false;
      }
      return true;
    },
    suffixText() {
      if (this.isSocks && this.hasSizeStep) return this.langSemiCustom.sockSizeTip;
      return "";
    },
    hasSizeStep() {
      if (
        this.stepData &&
        this.stepData.attributeCategoryList &&
        this.stepData.attributeCategoryList.length > 0
      ) {
        return this.stepData.attributeCategoryList.some(
          (item) => item.attributeFlag == "size"
        );
      }
      return false;
    },
    hasArtworkStep() {
      if (
        this.stepData &&
        this.stepData.attributeCategoryList &&
        this.stepData.attributeCategoryList.length > 0
      ) {
        return this.stepData.attributeCategoryList.some(
          (item) => item.attributeFlag == "artwork"
        );
      }
      return false;
    },
    areaIsHideStep() {
      return this.$store.state.halfDesign.areaIsHideStep;
    },
    needMoreUpload() {
      if (this.isBeltBuckles && this.hasArtworkStep) return true;
      return false;
    },
    needUploadDialog() {
      if (this.isPokerChips && this.hasArtworkStep) return true;
      return false;
    },
    canShowStepText() {
      return this.$store.state.halfDesign.showStepText;
    },
  },
  mounted() {
    try {
      const element = document.querySelector("#modalHeader");
      this.theme = element.getAttribute("theme");
    } catch (e) {}
  },
  methods: {
    disabledNextBtn(bool) {
      this.disabledBtn = bool;
    },
    goNext() {
      this.$emit("toNext", this.stepData);
    },
    showStep(item) {
      //当前方位上开启隐藏，步骤上也开启需要隐藏，则隐藏
      if (this.areaIsHideStep && item.isHide) return false;
      if (item.nowShow) return true;
      if (item.noShow) return false;
      //modeType简易模式(1||2) 不展示 printing    并且去掉printing 步骤的minTitle + 去掉printColor步骤的minTitle
      if (
        item.attributeFlag != "printing" &&
        item.attributeFlag != "area" &&
        item.attributeFlag != "printColor"
      ) {
        return true;
      } else if (
        (item.attributeFlag == "area" || item.attributeFlag == "printColor") &&
        !this.emailLater
      ) {
        if (item.attributeFlag == "printColor") return this.canShowPrintColorPrice;
        //area 简易模式(1||2)  如果只有一个方位就隐藏  2025/2/26新增逻辑，以前全隐藏
        if (
          (this.modeType == 1 || this.modeType == 2) &&
          item.productParamList &&
          item.productParamList.length <= 1
        ) {
          return false;
        }
        return true;
      } else if (item.attributeFlag == "printing" && !this.emailLater && this.step3Show) {
        return true;
      } else if (
        (item.attributeFlag == "printing" ||
          item.attributeFlag == "area" ||
          item.attributeFlag == "printColor") &&
        this.emailLater
      ) {
        return false;
      }
    },
    closeFn() {
      this.$emit("closeMask", this.stepData.id, false);
    },
    checkIdCanNextFn(id) {
      const tempId = ("" + id).replace(/copy/g, "");
      for (const [key, value] of Object.entries(this.needNextStepObj)) {
        if (+key == +this.stepData.id) {
          if (value.includes(+tempId)) {
            return false;
          }
        }
      }
      return true;
    },
    nextStep() {
      this.$emit("nextStep");
    },
    suffixClickFn() {
      if (this.isSocks) {
        this.socksSizeTip = true;
      }
    },
  },
};
</script>

<style scoped lang="scss">
@import "~assets/css/half.scss";

.luggagetags {
  background: linear-gradient(90deg, #f8430d, #fea33a) !important;
}

.step {
  margin-bottom: 30px;

  @include respond-to(mb) {
    margin-bottom: 0;
  }

  &.mask {
    position: relative;

    &.active {
      padding: 40px 30px 30px;
      background: #fff;
      border-radius: 10px;
      z-index: 100002;

      .close-icon {
        display: flex;
      }

      @include respond-to(mb) {
        padding: 20px 10px;
        border-radius: 6px;

        .close-icon {
          display: flex;
          transform: none;
          box-shadow: none;
          border-radius: 6px;
        }
      }
    }

    .close-icon {
      display: none;
      width: 3em;
      height: 3em;
      font-size: 12px;
      aspect-ratio: 1/1;
      align-items: center;
      justify-content: center;
      position: absolute;
      top: 0;
      right: 0;
      transform: translate(50%, -50%);
      background: #fff;
      box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.3);
      border-radius: 50%;
      z-index: 10;
      cursor: pointer;
    }
  }
}

.next[disabled],
.nextStep[disabled] {
  pointer-events: none;
  background: #e2e2e2;
}

.nextStep {
  width: fit-content;
  margin: 30px auto 0;
}

.suffixStyle {
  cursor: pointer;
  font-size: 14px;

  &.sockSuffix {
    color: $color-primary;
    text-decoration: underline;
  }
}

.stepText {
  font-size: 16px;
  margin-bottom: 15px;
  white-space: normal;
  width: 100%;
  word-break: break-word;
  color: rgb(135 132 132);
  @include respond-to(mb){
    font-size: 14px;
  }
}
</style>
