<template>
	<div class="modal-box exhibition-detail">
		<GSJJBreadcrumb :items="breadcrumb" />

		<div class="exhibition-detail__main">
			<div class="left">
				<div class="prod_info_title_mb" v-if="isMobile">
					<h1>{{ productData.galleryName }}</h1>
				</div>
				<div class="note">{{ lang.notes }}</div>

				<GSJJSwiper :imgList="imgList" />
			</div>

			<div class="right" v-if="!isMobile">
				<div class="prod_info_case">{{ lang.customCase }}</div>
				<div class="prod_info_title">
					<h1>{{ productData.galleryName }}</h1>
				</div>
				<div class="prod_info_brife" v-html="productData.newBriefDescription"></div>
				<div class="prod_info_btns">
					<a v-if="productData.designUrl" :href="productData.designUrl" target="_blank" :title="lang.designQuote">
						{{ lang.designQuote }}
					</a>
					<a v-else href="mailto:<EMAIL>" :title="lang.contactUs">
						{{ lang.contactUs }}
					</a>

					<a :href="productData.learMoreUrl" :title="lang.learnMore">
						{{ lang.learnMore }}
					</a>
				</div>
				<div class="prod_info_service_box">
					<div>{{ lang.featuredService }}</div>
					<div>
						<div v-for="item in list" :key="item.text">
							<img :src="item.url" :alt="item.alt" :title="item.alt" />
							<span>{{ item.alt }}</span>
						</div>
					</div>
				</div>
			</div>

			<div class="prod_info_brife_mb" v-if="isMobile && productData.newBriefDescription" v-html="productData.newBriefDescription"></div>

			<div class="prod_info_btns_mb" v-if="isMobile">
				<a v-if="productData.designUrl" :href="productData.designUrl" target="_blank" :title="lang.designQuote">
					{{ lang.designQuote }}
				</a>
				<a v-else href="mailto:<EMAIL>" :title="lang.contactUs">
					{{ lang.contactUs }}
				</a>

				<a :href="productData.learMoreUrl" :title="lang.learnMore">
					{{ lang.learnMore }}
				</a>
			</div>
		</div>

		<div class="exhibition-detail__description">
			<div class="pd_title">
				<span>{{ lang.description }}</span>
			</div>
			<div class="pd_content" v-html="productData.description"></div>
		</div>
	</div>
</template>

<script>
import GSJJBreadcrumb from "./components/-GSJJBreadcrumb.vue";
import GSJJSwiper from "./components/-GSJJSwiper.vue";
import { getGalleryDetail } from "@/api/web";

export default {
	components: { GSJJBreadcrumb, GSJJSwiper },
	async asyncData({ store, error, route }) {
		try {
			const { data } = await getGalleryDetail({
				pageUrl: route.params.cateName,
			});

			const imgList = [];
			const productData = data;
			const keyMap = ["picPathZero", "picPathOne", "picPathTwo", "picPathThree", "picPathFour"];
			keyMap.forEach((key) => {
				if (productData[key]) {
					imgList.push({
						url: productData[key],
						alt: productData.galleryName,
					});
				}
			});

			return {
				productData,
				imgList,
			};
		} catch (e) {
			return error({ statusCode: 404 });
		}
	},
	head() {
		let meta = [
			{
				hid: "description",
				name: "description",
				content: this.productData.seoDescription,
			},
			{
				hid: "keywords",
				name: "keywords",
				content: this.productData.seoKeyword,
			},
		];
		return {
			title: this.productData.seoTitle,
			meta: meta,
		};
	},
	computed: {
		lang() {
			return this.$store.getters.lang?.exhibition;
		},

		list() {
			return [
				{
					url: "https://static-oss.gs-souvenir.com/web/20250515/u-FREE_SHIPPING_20250515sfFbpm.png",
					alt: this.lang.freeShipping,
				},
				{
					url: "https://static-oss.gs-souvenir.com/web/20250515/u-FREE_ART_DESIGN_20250515NCjGCh.png",
					alt: this.lang.freeArtDesign,
				},
				{
					url: "https://static-oss.gs-souvenir.com/web/20250515/u-NO_MOQ_20250515mD4Wym.png",
					alt: this.lang.noMoq,
				},
				{
					url: "https://static-oss.gs-souvenir.com/web/20250515/u-FACTORY_DIRECT_20250515kZwwpW.png",
					alt: this.lang.factoryDirect,
				},
			];
		},

		isMobile() {
			return this.$store.getters.isMobile;
		},

		breadcrumb() {
			const { galleryCate } = this.productData;
			const list = [];

			// 添加基础导航项
			const baseNavItems = [
				{ text: "Home", link: "/" },
				{ text: "Gallery", link: "/Exhibition" },
			];

			// 提取构建导航项URL的方法
			const buildCategoryUrl = (category) => `/${category.pageUrlPrefix}/s/${category.pageUrl}`;

			// 添加分类导航项
			if (galleryCate) {
				// 主分类
				list.push({
					text: galleryCate.cateName,
					link: buildCategoryUrl(galleryCate),
				});

				// 子分类（如果存在）
				const firstChild = galleryCate.childList?.[0];
				if (firstChild) {
					list.push({
						text: firstChild.cateName,
						link: buildCategoryUrl(firstChild),
					});
				}
			}

			return [...baseNavItems, ...list];
		},
	},
};
</script>

<style lang="scss" scoped>
.exhibition-detail {
	padding-top: 1rem;
	z-index: auto;

	@include respond-to(mb) {
		background-color: #f2f2f2;
	}
}

.exhibition-detail__main {
	display: grid;
	grid-template-columns: 40% 35%;
	justify-content: space-evenly;

	.note {
		padding: 0.3125rem 0;
		line-height: 1.625rem;
		font-size: 1.5rem;
		font-weight: 700;
		font-style: italic;
		color: #fff;
		text-align: center;
		background-color: #ff6600;

		@include respond-to(mb) {
			font-size: 16px;
		}
	}

	.prod_info_case {
		font-size: 0.875rem;
		font-weight: 700;

		@include respond-to(mb) {
			display: none;
		}
	}

	.prod_info_title {
		border-bottom: 1px solid rgb(51, 51, 51);

		h1 {
			padding: 5px 0;
			font-size: 1.25rem;
			font-weight: 700;
			color: #333;
		}
	}

	.prod_info_title_mb {
		padding-bottom: 5px;
		h1 {
			text-align: center;
		}
	}

	.prod_info_brife {
		min-height: 270px;
		border-bottom: 1px solid rgb(51, 51, 51);
	}

	.prod_info_brife_mb {
		margin-top: 2vw;
		padding: 2vw;
		background-color: #fff;
	}

	.prod_info_btns {
		margin-top: 10px;
		display: flex;
		gap: 10px;
		flex-direction: column;

		a {
			display: block;
			padding: 10px;
			font-size: 18px;
			font-weight: 700;
			text-align: center;
			border-radius: 6px;
			&:first-child {
				color: #fff;
				background-color: #0066cc;
			}
			&:first-child[href^="mailto:"] {
				background-color: #9cc334;
			}
			&:last-child {
				color: #000;
				border: 2px solid #434343;
			}
		}
	}

	.prod_info_btns_mb {
		position: fixed;
		z-index: 998;
		left: 0;
		bottom: 8px;
		padding: 4px;
		width: 100%;
		display: flex;
		gap: 4px;
		background-color: #eeeeee;
		a {
			flex: 1;
			display: block;
			height: 50px;
			line-height: 50px;
			font-size: 16px;
			text-align: center;
			background-color: #fff;
			&:first-child {
				color: #fff;
				background-color: #0066cc;
			}
			&:first-child[href^="mailto:"] {
				background-color: #9cc334;
			}
			&:last-child {
				color: #000;
				border: 1px solid #434343;
			}
		}
	}

	.prod_info_service_box {
		margin-top: 30px;

		> div:first-child {
			padding: 5px 0;
			font-size: 18px;
			font-weight: 700;
			border-bottom: 2px solid #303030;
		}

		> div:last-child {
			margin-top: 10px;
			padding: 10px;
			display: flex;
			flex-wrap: wrap;
			background-color: #f2f2f2;

			> div {
				flex: 50%;
				display: flex;
				align-items: center;
				padding: 0 10px;
			}

			img {
				width: 30px;
			}
		}
	}

	@include respond-to(mb) {
		grid-template-columns: 100%;
	}
}

.left {
	background-color: #fff;
	@include respond-to(mb) {
		padding: 2vw;
	}
}

.exhibition-detail__description {
	margin-top: 2vw;
	background-color: #fff;

	.pd_title {
		display: flex;
		height: 36px;
		line-height: 36px;
		font-size: 14px;

		span {
			padding: 0 14px;
			border: 1px solid #ddd;
			border-bottom: 0;
			border-radius: 4px 4px 0 0;
			background-color: #fff;
		}

		&::after {
			flex: 1;
			content: "";
			border-bottom: 1px solid #ddd;
		}
	}

	.pd_content {
		padding: 5px;
	}

	@include respond-to(mb) {
		padding: 2vw;

		.pd_title {
			span {
				padding: 0;
				font-size: 18px;
				font-weight: 700;
				border: none;
			}

			&::after {
				border-bottom: none;
			}
		}

		.pd_content {
			padding: 0;
			font-size: 16px;
		}
	}
}
</style>
