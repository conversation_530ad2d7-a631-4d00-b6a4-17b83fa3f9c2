<template>
	<div flex class="modal-box twin-box" :style="modal.style" :class="modal.class">
		<component v-if="leftModal.name" @mouseover.native="mouseoverFun('outer')" :is="'modal' + leftModal.name" :id="leftModal.id"  :data.sync="leftModal">
		</component>
		<component v-if="leftModal.name"  @mouseover.native="mouseoverFun('list')"  :is="'modal' + rightModal.name" :id="rightModal.id"  :data.sync="rightModal">
		</component>
	</div>
</template>


<script>
export default {
	name: "modalTwins",
	props: {
		data: {
			type: Object,
			default: {},
		}
	},
	data() {
		return {
			modal: {
				style: {},
				...this.data,
				type:{}
			},
			leftModal: {
				id: this.data.id + 'left',
				...this.data.outer[0],
				list: this.data.outer?.slice(1),
				type:{}
			},
			rightModal: {
				id: this.data.id + 'right',
				...this.data.list[0],
				list: this.data.list?.slice(1),
				type:{}
			},
		};
	},
	watch: {
		data:{
			handler(val) {
				if(!!this.$store.state.twinsLeftOrRight){
					if(this.$store.state.twinsLeftOrRight =='outer'){
						this.leftModal.list = val.outer?.slice(1)
					}else{
						this.rightModal.list = val.list?.slice(1)
					}
				}
			},
			immediate: true,
			deep: true
		},
		modal: {
			handler(val) {
				if (process.env.isManage) this.$emit("update:data", {
					...val,
					outer: [
						this.data.outer[0],
						...this.leftModal.list
					],
					list: [
						this.data.list[0],
						...this.rightModal.list
					]
				});
			},
			immediate: true,
			deep: true
		},
		"rightModal.type":{
			handler(val) {
				if(!val.clickType) return
				this.modal.type = val
				this.rightModal.type={}
			},
			immediate: true,
			deep: true
		},
		"leftModal.type":{
			handler(val) {
				if(!val.clickType) return
				this.modal.type = val
				this.leftModal.type={}
			},
			immediate: true,
			deep: true
		}
	},
	methods:{
		mouseoverFun(leftOrRight=null,event){
			this.$store.commit('setTwinsLeftOrRight',leftOrRight)
		},
	},
};
</script>


<style lang="scss" scoped>
[theme] .modal-box.twin-box {
	justify-content: space-between;

	.modal-box {
		flex: 1;
		padding: 0;
	}
}



// mb端样式
@media screen and (max-width: $mb-width) {
	[theme] .modal-box.twin-box {
		flex-direction: column;
	}
}
</style>
