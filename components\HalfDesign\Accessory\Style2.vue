<template>
	<div class="mb-4" :class="stepData.styleName">
		<half-design-select-tip :customText="stepData.attributeTitle" :selectedValue="shape"></half-design-select-tip>
		<slot name="stepText"></slot>
		<div class="step-content">
			<div class="step-item" :class="{active:index===selectIndex}" v-for="(step,index) in stepData.productParamList"
					 :key="index"
					 @click="selectStep(step,index)">
				<div class="imgWrap" :class="{active:index===selectIndex}">
					<img :src="step.imgDetail" :alt="step.valueName" :title="step.valueName">
					<v-btn
						small
						icon
						color="#9E9E9E"
						class="absolute-top-right zoom-icon"
						@click.stop="zoomPic(step.imgDetail)"
					>
						<b class="icon-a-tgsc-addzhuanhuan"></b>
					</v-btn>
				</div>
				<div class="d-flex-center pa-1 name">
					<half-design-check-box class="mr-2"></half-design-check-box>
					<div>
						<div class="text-truncate">
							{{ step.valueName }}
						</div>
						<div class="price">
							{{ step.remark }}
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="errorTip">
			<v-alert
				dense
				outlined
				type="error"
			>
				{{ langSemiCustom.errorTip }}
			</v-alert>
		</div>
	</div>
</template>
<script>
export default {
	props: {
		stepData: {
			type: Object,
			default: {}
		}
	},
	data() {
		return {
			selectIndex: -1,
			selectItem: null
		}
	},
	computed: {
		shape() {
			return this.selectItem?.valueName
		},
		langSemiCustom() {
			return this.$store.getters.lang?.semiCustom;
		}
	},
	methods: {
		printColorData(state, areaIndex) {
		  this.selectIndex = areaIndex;
		  this.selectItem = null;
		  if (areaIndex < 0) return;
		  if (this.stepData && this.stepData.productParamList && this.stepData.productParamList.length > 0) {
		  	let item = this.stepData.productParamList[areaIndex];
		  	this.selectStep(item, areaIndex, state);
		  }
		},
		selectStep(item, index) {
			this.selectIndex = index;
			this.selectItem = item;
			this.$emit('selectStep', {
				type: this.stepData.attributeFlag,
				data: item,
				parent: this.stepData,
				id: this.stepData.id
			})
		},
		zoomPic(img) {
			this.$viewerApi({
				images: [img]
			})
		}
	},
	mounted() {
		this.$Bus.$on("printColor",this.printColorData);
    this.$Bus.$on("selectDefaultPrintColorStep",this.printColorData);
	},
	beforeDestroy(){
		this.$Bus.$off("printColor",this.printColorData);
    this.$Bus.$off("selectDefaultPrintColorStep",this.printColorData);
	}
}
</script>
<style scoped lang="scss">
@import "~assets/css/half.scss";

.step-content {
	display: grid;

	.step-item {
		position: relative;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		padding: 0;
		min-width: 0;
		box-shadow: 0 0 0 1px $border-color;
		@include radius-response;
		cursor: pointer;

		&:hover {
			box-shadow: 0 0 0 2px $color-primary;
		}

		&.active {
			box-shadow: 0 0 0 2px $color-primary;
		}

		.price {
			font-size: 14px;
			@include respond-to(mb) {
				font-size: 12px;
			}
		}

		.check-box {
			display: none;
		}

		.zoom-icon {
			display: none;
		}
	}
}

.style1 .step-content {
	grid-template-columns: repeat(4, 1fr);
	grid-gap: 10px;

	.step-item {
		overflow: hidden;

		.check-box {
			display: flex;
		}

		.name {
			width: 100%;
			background-color: $background-color;
		}

		.imgWrap {
			width: 100%;
			@include flex-center;
			height: 140px;

			img {
				max-width: 100%;
				max-height: 100%;
				object-fit: contain;
			}
		}

		@media (any-hover: hover) {
			&:hover {
				padding: 0;
				color: #ffffff;

				::v-deep .check-box {
					border-color: #ffffff;

					.check-box-inner {
						background-color: #ffffff;
					}
				}

				.name {
					background-color: $color-primary;
				}

			}
		}
	}

	.step-item.active {
		color: #ffffff;

		::v-deep .check-box {
			border-color: #ffffff;

			.check-box-inner {
				background-color: #ffffff;
			}
		}

		.name {
			background-color: $color-primary;
		}
	}

	@include respond-to(mb) {
		grid-template-columns: repeat(3, 1fr);
		grid-gap: 5px;

		.step-item {
			padding: 0;

			.imgWrap {
				padding: 5px;
				height: 100px;
			}
		}
	}
}

.style2 .step-content {
	grid-template-columns: repeat(4, 1fr);
	grid-gap: 10px;

	.step-item {
		box-shadow: none;

		.zoom-icon {
			display: block;
		}

		.name {
			text-align: center;
		}

		.imgWrap {
			width: 100%;
			position: relative;
			@include flex-center;
			@include step-default;
			height: 140px;

			img {
				max-width: 100%;
				max-height: 100%;
				object-fit: contain;
			}
		}
	}

	@include respond-to(mb) {
		grid-template-columns: repeat(2, 1fr);
		grid-gap: 5px;

		.step-item {
			padding: 0;

			.check-box {
				display: flex;
			}

			.imgWrap {
				width: 100%;
			}
		}

		.step-item.active {

			::v-deep .check-box {
				border-color: $color-primary;

				.check-box-inner {
					background-color: $color-primary;
				}
			}
		}
	}
}
</style>
