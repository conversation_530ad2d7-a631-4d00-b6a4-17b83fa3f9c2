<template>
	<div class="step-item-title">
		<span>{{ step }}:</span>
		<span>{{ title }}</span>
		<slot name="suffix"></slot>
	</div>
</template>

<script>
export default {
	props: {
		step: {
			type: String,
			default: "1",
			required: true,
		},
		title: {
			type: String,
			default: "",
			required: true,
		},
	},
};
</script>

<style lang="scss" scoped>
.step-item-title {
	display: flex;
	align-items: center;
	margin-bottom: 0.7em;
	font-size: 1.125em;
	font-weight: 700;

	@include respond-to(mb) {
		font-size: 1.17em;
		margin-bottom: 1em;
	}

	& > span:first-child {
		margin-right: 8px;
		color: $color-primary;
		font-weight: 700;
		text-transform: uppercase;
		flex-shrink: 0;
	}

	& > span:nth-child(2) {
		margin-right: 4px;
		flex-shrink: 0;
	}
}
</style>
