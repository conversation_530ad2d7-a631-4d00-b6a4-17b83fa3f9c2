<template>
	<BaseDialog :value="patchesColorDialog" @update="$emit('update:patchesColorDialog', $event)" persistent :width="device != 'mb' ? '700px' : '90%'">
		<div class="content">
			<div class="header">
				{{ itemData.alias }}
			</div>
			<div class="body">
				<div class="imgWrap">
					<img loading="lazy" :src="JSON.parse(itemData.imageJson)[0].url" :alt="itemData.alias" :title="itemData.alias" />
					<span>{{ lang.p18 }} {{ itemData.alias }}</span>
				</div>
				<div class="colorQty">
					<span class="small-title">{{ lang.GlowintheDark }}</span>
					<div class="colorQtyContent">
						<div class="item-wrap" v-for="(item, index) in 4" :key="index" @click="selectQty(item)" :class="{ active: itemData.selectQty == item }">
							<div class="item normalBorder">
								<medals-check-icon class="absolute-top-right"></medals-check-icon>
								<div class="imgWrap">
									<span v-if="item == 1">{{ item }} {{ lang.Color }}</span>
									<span v-else>{{ item }} {{ lang.Colors }}</span>
								</div>
								<div class="textWrap">
									<div>+ {{ getPrice(index) }}</div>
									<div>of unit price</div>
								</div>
								<div class="circle">
									<div class="inner-circle"></div>
								</div>
							</div>
						</div>
						<div class="item-wrap selfInput" @click="selectCustomQty" :class="{ active: itemData.inputQty }">
							<div class="item normalBorder">
								<medals-check-icon class="absolute-top-right"></medals-check-icon>
								<div class="imgWrap">
									<el-input-number v-model="itemData.inputQty" @change="updateQty" size="small" :precision="0" :min="0" :max="colorNumberList.length" :controls="false" :placeholder="lang.customQty"></el-input-number>
									<b class="icon-edit--fill editIcon"></b>
								</div>
								<div class="textWrap">
									<span>+ {{ getPrice(itemData.inputQty - 1) }}</span>
									<span>{{ lang.ofUnitPrice }}</span>
								</div>
								<div class="circle">
									<div class="inner-circle"></div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="colorNumber" v-if="itemData.selectQty || itemData.inputQty">
					<span class="small-title">{{ lang.sn }}</span>
					<div class="colorNumberContent">
						<div class="itemWrap" v-for="(item, index) in colorNumberList" :key="index" @click="selectColor(item)" :class="{ active: hasActive(item) }">
							<medals-check-icon class="absolute-top-right"></medals-check-icon>
							<span class="code">{{ item.code }}</span>
							<img :src="item.imgUrl" :alt="item.code" :title="item.code" />
						</div>
					</div>
				</div>
				<div class="comments">
					<span class="small-title">{{ lang.Comments }}</span>
					<el-input type="textarea" v-model="itemData.remark" :placeholder="getInputPlaceHolder(itemData.paramName)"></el-input>
				</div>
			</div>
			<div class="footer">
				<QuoteBtn :disabled="disabledBtn()" @click.native="confirm('next')">
					{{ lang.SelectNextStep }}
				</QuoteBtn>
				<QuoteBtn :disabled="disabledBtn()" @click.native="confirm" moreHover type2 :tips="lang.p27_1">
					{{ lang.SelectGoBack }}
				</QuoteBtn>
			</div>
		</div>
	</BaseDialog>
</template>

<script>
import BaseDialog from "@/components/Quote/BaseDialog.vue";
import QuoteBtn from "@/components/Quote/QuoteBtn.vue";
import { exchangeRates } from "@/utils/utils";
import MedalsCheckIcon from "@/components/Medals/CheckIcon.vue";
export default {
	props: {
		patchesColorDialog: {
			type: Boolean,
		},
		itemData: {
			type: Object,
		},
		colorNumberList: {
			type: Array,
		},
	},
	components: { QuoteBtn, BaseDialog, MedalsCheckIcon },
	computed: {
		device() {
			return this.$store.state.device;
		},
		lang() {
			return this.$store.getters.lang.quote || {};
		},
		currencySymbol() {
			return this.$store.getters.currencySymbol;
		},
		currencyRate() {
			return this.$store.getters.currencyRate;
		},
		morePriceData() {
			return this.$store.getters.morePriceData;
		},
	},
	methods: {
		getInputPlaceHolder(name) {
			if (name === "Glow in the Dark Thread") {
				return this.lang.egd;
			} else if (name === "Fluorescent Thread") {
				return this.lang.ef;
			}
		},
		disabledBtn() {
			return (!this.itemData.selectQty && !this.itemData.inputQty) || !this.itemData.colorList.length;
		},
		confirm(type) {
			if (this.itemData.selectQty) {
				this.itemData.inputNum = this.itemData.selectQty;
			} else {
				this.itemData.inputNum = this.itemData.inputQty;
			}
			if (!this.itemData.inputNum) {
				return false;
			}
			if (type == "next") {
				this.$emit("qtyAndBtnNext", this.itemData);
			} else {
				this.$emit("qtyAndBtnConfirm", this.itemData);
			}
		},
		selectCustomQty() {
			if (!this.itemData.inputQty) {
				this.itemData.inputQty = 1;
			}
			this.itemData.selectQty = undefined;
			this.itemData.colorList = [];
		},
		hasActive(item) {
			let colorList = this.itemData.colorList;
			let findIndex = colorList.findIndex((i) => {
				return i.code === item.code;
			});
			return findIndex > -1;
		},
		selectColor(item) {
			let colorList = this.itemData.colorList,
				maxColor = this.itemData.inputQty || this.itemData.selectQty || 0;
			let findIndex = colorList.findIndex((i) => {
				return i.code === item.code;
			});
			if (findIndex > -1) {
				colorList.splice(findIndex, 1);
			} else {
				if (colorList.length >= maxColor) {
					return false;
				}
				colorList.push(item);
			}
		},
		selectQty(val) {
			this.itemData.selectQty = val;
			this.itemData.inputQty = undefined;
			this.itemData.colorList = [];
		},
		updateQty(val) {
			if (!val) {
				this.itemData.selectQty = undefined;
			}
		},
		exchangeRates(price, rate) {
			return exchangeRates(price, rate);
		},
		getPriceFun(dialogItem, count, currencyRate) {
			let temp;
			if (count <= 0 || !count) {
				temp = this.exchangeRates(0, currencyRate);
			} else if (dialogItem.priceInfo.increasePrice) {
				temp = this.upPriceFun(dialogItem.priceInfo.increasePrice, count, currencyRate) * count;
			} else if (dialogItem.priceInfo.unitPrice) {
				temp = this.exchangeRates(dialogItem.priceInfo.unitPrice, currencyRate) * count;
			} else {
				temp = this.exchangeRates(0, currencyRate) * count;
			}
			return ((temp + this.morePriceData.plusAdditionalItemPrice) * this.morePriceData.multiAdditionalItemPrice).toFixed(2);
		},
		getPrice(colorQty) {
			if (colorQty === "" || colorQty === undefined || isNaN(colorQty)) {
				return "0%";
			}
			let increasePrice = JSON.parse(this.itemData.priceInfo.increasePrice);
			return ((Number(increasePrice.initPercent) + Number(colorQty * increasePrice.increasePercent)) * 100).toFixed(0) + "%";
		},
	},
	mounted() {
		console.log(this.itemData);
		console.log("弹窗底层", this.patchesColorDialog, this.itemData, this.colorNumberList);
	},
};
</script>

<style lang="scss" scoped>
.content {
	padding: 20px;

	@include respond-to(mb) {
		padding: 10px;
	}

	.header {
		margin-bottom: 10px;
		font-weight: 600;
		font-size: 18px;
		text-align: left;
		@include respond-to(mb) {
			font-size: 14px;
		}
	}

	.body {
		.small-title {
			display: inline-block;
			margin-bottom: 10px;
			font-weight: 600;
			font-size: 16px;

			@include respond-to(mb) {
				font-size: 12px;
			}

			&::before {
				content: "";
				display: inline-block;
				width: 6px;
				height: 6px;
				margin-right: 8px;
				background: #333333;
				border-radius: 50%;
				vertical-align: middle;
			}
		}

		& > .imgWrap {
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			height: 250px;
			margin-bottom: 20px;
			@include respond-to(mb) {
				height: 200px;
				margin-bottom: 10px;
			}

			img {
				width: auto;
				height: 0;
				flex: 1;
				margin-bottom: 10px;
				object-fit: contain;
			}

			span {
				color: #999999;
				font-weight: 600;

				@include respond-to(mb) {
					font-size: 12px;
				}
			}
		}

		.colorQty {
			margin-bottom: 30px;

			.colorQtyContent {
				display: grid;
				grid-template-columns: repeat(5, 1fr);
				row-gap: 5px;
				column-gap: 10px;
				font-size: 14px;
				justify-content: space-between;
				@include respond-to(mb) {
					grid-template-columns: repeat(3, 1fr);
					font-size: 12px;
					row-gap: 20px;
				}
			}

			.item-wrap.normalBorder {
				background: #f4f5f5;
			}

			.item-wrap {
				position: relative;

				.item {
					background-color: #fff !important;
					border: 1px solid #d9d9d9;
					border-radius: 4px;
					position: relative;
					display: flex;
					flex-direction: column;
					justify-content: space-between;
					align-items: center;
					cursor: pointer;
					transition: all 0.3s;

					.checkIcon {
						width: 20px;
						height: 20px;
						border-radius: 0 0 0 6px;
						display: none;
						z-index: 1;
						@include respond-to(mb) {
							width: 18px;
							height: 18px;
							::v-deep .v-icon.v-icon {
								font-size: 14px !important;
							}
						}
					}

					.imgWrap {
						flex-shrink: 0;
						width: 100%;
						height: 40px;
						padding: 10px;
						border-bottom: 1px dashed #e9ecf0;
						display: flex;
						justify-content: center;
						align-items: center;

						@include respond-to(mb) {
							height: 35px;
						}

						::v-deep .el-input-number {
							width: 100%;
						}
					}

					.textWrap {
						display: flex;
						flex-direction: column;
						align-items: center;
						justify-content: center;
						flex: 1;
						text-align: center;
						padding: 5px 0 15px;
					}

					.circle {
						position: absolute;
						left: 50%;
						bottom: 0;
						transform: translate(-50%, 100%);
						width: 28px;
						height: 15px;
						border: 1px solid #e9ecf0;
						border-top: none;
						background: #ffffff;
						border-radius: 0 0 16px 16px;
						z-index: 10;
						transition: all 0.3s;

						.inner-circle {
							position: absolute;
							left: 50%;
							top: 0;
							transform: translate(-50%, -50%);
							width: 18px;
							height: 18px;
							border-radius: 50%;
							background: #fff;
							border: 1px solid #aaaeb3;
							transition: all 0.3s;

							&::after {
								content: "";
								position: absolute;
								left: 50%;
								top: 50%;
								transform: translate(-50%, -50%);
								width: 6px;
								height: 6px;
								background-color: #aaaeb3;
								border-radius: 50%;
								transition: all 0.3s;
							}
						}
					}
				}

				&.active,
				&:hover {
					.item {
						border-color: $color-primary;

						.checkIcon {
							display: flex;
						}

						.editIcon {
							display: none;
						}

						.circle {
							border-color: $color-primary;

							.inner-circle {
								border-color: $color-primary;

								&::after {
									background-color: $color-primary;
								}
							}
						}
					}
				}
			}

			.selfInput.item-wrap {
				.item {
					.imgWrap {
						padding: 0;

						::v-deep .el-input-number {
							width: 100%;

							.el-input__inner {
								padding: 0;
								border: none;
							}
						}

						.editIcon {
							flex-shrink: 0;
							color: #b3b3b3;
							@include respond-to(mb) {
								padding-right: 5px;
							}
						}
					}
				}
			}
		}

		.colorNumber {
			margin-bottom: 20px;

			.colorNumberContent {
				display: grid;
				grid-template-columns: repeat(7, 1fr);
				gap: 10px;
				font-size: 14px;

				@include respond-to(mb) {
					grid-template-columns: repeat(4, 1fr);
					font-size: 12px;
				}

				.itemWrap {
					overflow: hidden;
					position: relative;
					display: flex;
					flex-direction: column;
					justify-content: center;
					align-items: center;
					min-width: 0;
					padding: 15px 5px 5px;
					background: #ffffff;
					border-radius: 10px;
					border: 1px solid #dcdfe6;
					cursor: pointer;

					@include respond-to(mb) {
						padding-top: 5px;
						border-radius: 4px;
					}

					.checkIcon {
						width: 20px;
						height: 20px;
						border-radius: 0 0 0 6px;
						display: none;
						z-index: 1;
						@include respond-to(mb) {
							width: 18px;
							height: 18px;
							::v-deep .v-icon.v-icon {
								font-size: 14px !important;
							}
						}
					}

					&.active,
					&:hover {
						border-color: $color-primary;

						.checkIcon {
							display: flex;
						}
					}

					img {
						height: 40px;
						border-radius: 10px;
						@include respond-to(mb) {
							height: 25px;
							border-radius: 4px;
						}
					}

					.code {
						margin-bottom: 14px;
						@include respond-to(mb) {
							margin-bottom: 5px;
						}
					}
				}
			}
		}

		.comments {
			::v-deep .el-textarea__inner:focus {
				border-color: $color-primary;
			}
		}
	}

	.footer {
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 20px 20px 0;

		button {
			margin: 0 10px;
		}
	}
}
</style>
