<template>
	<div id="wristband"
		 v-bind="$attrs">
		<CanvasFactory ref="CanvasFactory"
					   class="canvasFactory"
					   :lanyardType="lanyardType"
					   :printingData="printingData"
					   :tabsName="tabsName"
					   :canvasData="canvasData"
					   :tampInfo="selectedData['Design Your Printing']"
					   @canvasToImage="canvasToImageFun"></CanvasFactory>
		<QuoteNav :pid="pid"
				  title="We provide these wristband types for you."
				  style="margin-bottom: 10px"></QuoteNav>
		<QuoteTitle :h1-text="lang.wristband.h1"></QuoteTitle>
		<article class="content">
			<div class="leftArea"
				 id="leftArea">
				<div class="advertisingBanner"
					 v-if="isFullReductionActivity">
					<div>
						{{ lang.lanyard.FullCopy1 }} {{ satisfiedQuantity }} {{ lang.lanyard.FullCopy7 }}
						<span>{{ lang.lanyard.FullCopy11 }} {{ giftQuantity1 }} {{ lang.lanyard.FullCopy8 }} </span>
					</div>
				</div>
				<div v-for="(item, index) in filterShowGeneralData"
					 :key="item.id"
					 class="kk"
					 :class="{ type1: picDialog }">
					<!-- Band Size -->
					<div v-if="item.paramName == 'Band Size'" class="part BandSize" :id="item.paramName" :class="{ 'step-active': item.paramName == maskName }">
						<StepTitle :title="getTitle(item)"
								   :step="getStep(item)">
							<template #suffix>
								<ToolTip :itemData="item"
										 style="font-size: 16px"></ToolTip>
							</template>
						</StepTitle>
						<div class="boxContent">
							<BookCheckBox v-for="itemChild in item.childList" :key="itemChild.id" :bindValue="itemChild" :selectedData="selectedData" :bindName="item.paramName" customShadow @clickFun="selectFun(item, $event)" @picDialogFun="picDialogFun"></BookCheckBox>
						</div>
					</div>
					<!-- Select Band Color & Quantity -->
					<TabsStep v-if="item.paramName == 'Band Color & Quantity'"
							  :selectedData="selectedData"
							  :stepData="item"
							  :colorList="colorList"
							  :maskName.sync="maskName"
							  :customColor="customColor"
							  @nextStepFun="nextStepFun"
							  @customColorAdd="customColorAdd"
							  @updateColor="updateColor"
							  @clickFun="selectFun(item, $event)"></TabsStep>
					<!-- Select Message Style -->
					<div v-if="item.paramName == 'Message Style'" class="part MessageStyle" :id="item.paramName" :class="{ 'step-active': item.paramName == maskName }">
						<StepTitle :title="getTitle(item)"
								   :step="getStep(item)">
							<template #suffix>
								<ToolTip :itemData="item"
										 style="font-size: 16px"></ToolTip>
							</template>
						</StepTitle>
						<div class="boxContent">
							<BookCheckBox v-for="itemChild in item.childList" :key="itemChild.id" :bindValue="itemChild" :selectedData="selectedData" :bindName="item.paramName" customShadow @clickFun="selectFun(item, $event)" @picDialogFun="picDialogFun"></BookCheckBox>
						</div>
					</div>
					<!-- Design Your Band -->
					<DesignStep v-if="item.paramName == 'Design Your Band'"
								:selectedData="selectedData"
								:stepData="item"
								:maskName.sync="maskName"
								:colorList="colorList"
								:copyCanvasData.sync="copyCanvasData"
								:comments.sync="comments"
								:fontImgCustom.sync="fontImgCustom"
								@nextStepFun="nextStepFun"
								@updateUploadList="updateUploadList"
								@canvasFontForm="canvasFontFormFun"
								@clickFun="selectFun(item, $event)">
					</DesignStep>
					<!-- More Options -->
					<div v-if="item.paramName == 'More Options'" class="part MoreOptions 	" :id="item.paramName" :class="{ 'step-active': item.paramName == maskName }">
						<StepTitle :title="getTitle(item)"
								   :step="getStep(item)">
							<template #suffix>
								<ToolTip :itemData="item"
										 style="font-size: 16px"></ToolTip>
							</template>
						</StepTitle>
						<div class="boxContent">
							<BookCheckBox v-for="itemChild in item.childList" :key="itemChild.id" :bindValue="itemChild" :selectedData="selectedData" :bindName="item.paramName" customShadow @clickFun="selectFun(item, $event)" @picDialogFun="picDialogFun" showPrice></BookCheckBox>
						</div>
					</div>
					<!-- Select Packaging Options -->
					<div v-if="item.paramName == 'Packaging Options'" class="part PackagingOptions" :id="item.paramName" :class="{ 'step-active': item.paramName == maskName }">
						<StepTitle :title="getTitle(item)"
								   :step="getStep(item)">
							<template #suffix>
								<ToolTip :itemData="item"
										 style="font-size: 16px"></ToolTip>
							</template>
						</StepTitle>
						<div class="boxContent">
							<BookCheckBox v-for="itemChild in item.childList" :key="itemChild.id" :bindValue="itemChild" :selectedData="selectedData" :bindName="item.paramName" customShadow @clickFun="selectFun(item, $event)" @picDialogFun="picDialogFun" showPrice></BookCheckBox>
						</div>
					</div>
					<template v-if="item.paramName === 'Turnaround Time'">
						<StepTime class="step-item step-date"
								  :class="{ mask: maskName === item.paramName }"
								  :id="item.paramName"
								  :cateData="cateData"
								  :pid="pid"
								  :customQty="customQty"
								  :index="item.customIndex"
								  :itemData="item"
								  :priceInfo="priceInfo"
								  :previewMode="previewMode"
								  :selectedParams="selectedData"
								  @showMaskFn="showMaskFn"
								  @selectItem="selectFun($event.item, $event.citem)"
								  @closeMask="closeMask"
								  :key="index"></StepTime>
					</template>
				</div>
			</div>
			<div class="rightArea"
				 :class="{active: maskName === 'Design Your Band'}"
				 id="rightAreaCustom">
				<div class="stickyType step-active">
					<transition enter-active-class="animate__animated animate__faster animate__fadeInBottomLeft"
								leave-active-class="animate__animated animate__faster animate__fadeOutBottomLeft">
						<article class="canvas"
								 v-show="showRightArea">
							<div class="direction"
								 @click="changeDirection">
                <span class="front"
					  v-show="!canvasData.isBack">{{ lang.wristband.front }}</span>
								<span class="front"
									  v-show="canvasData.isBack">{{ lang.wristband.back }}</span>
								<b class="icon-a-Vector1"></b>
							</div>
							<div class="img-container"
								 style="width: 100%">
								<img :src="canvasToImage"
									 loading="lazy"/>
							</div>
						</article>
					</transition>
				</div>
			</div>
		</article>
		<article class="mFooter"
				 id="foot">
			<div class="medalsDetails">
				<WristbandDetails @submitInquiry="submitInquiry"
								  @addToCart="addToCart"
								  @jump="jump"
								  :showQty="true"
								  :currencyList="currencyList"
								  :copyCanvasData="copyCanvasData"
								  :calculateData="calculateData"
								  :defaultData="defaultData"
								  :textInfo="textInfo"
								  :totalQuantity="totalQuantity"
								  :uploadList="uploadList"
								  :hasComment="hasComment"
								  :showMore.sync="showMore"
								  :selectedData="selectedData"
								  :generalData="generalData"
								  footer
								  filter
								  :presentedQuantity="presentedQuantity"
								  :satisfiedQuantity="satisfiedQuantity"
								  :giftQuantity1="giftQuantity1">
					<article class="canvas">
						<div class="direction"
							 @click="changeDirection">
              <span class="front"
					v-show="!canvasData.isBack">{{ lang.wristband.front }}</span>
							<span class="front"
								  v-show="canvasData.isBack">{{ lang.wristband.back }}</span>
							<b class="icon-a-Vector1"></b>
						</div>
						<div class="img-container"
							 style="width: 100%">
							<img :src="canvasToImage"
								 loading="lazy"/>
						</div>
					</article>
				</WristbandDetails>
			</div>
		</article>
		<!--    遮罩-->
		<myMask :maskName.sync="maskName"></myMask>
		<el-dialog :destroy-on-close="true"
				   :show-close="false"
				   custom-class="commentDialog"
				   :visible.sync="commentDialogVisible"
				   :width="'300px'">
			<i class="el-icon-close"
			   @click="shutComment"></i>
			<div class="isComment">
				<div class="textWrap">
					<div class="circle2"></div>
					<div>
						<p class="normal-text">{{ lang.RecommendedSize }}</p>
					</div>
				</div>
				<el-input type="textarea"
						  :rows="4"
						  :placeholder="lang.p23"
						  v-model="commentTarget.comment"></el-input>
				<button type="button"
						@click="commentSubmit">{{ lang.ok }}
				</button>
			</div>
		</el-dialog>
		<el-dialog :visible.sync="dialogTips"
				   :width="device == 'mb' ? '80%' : '20%'"
				   :show-close="false"
				   :close-on-press-escape="false"
				   :close-on-click-modal="false">
			<span>{{ lang.lanyard.tips }} </span>
			<template #footer>
        <span class="dialog-footer">
          <el-button type="primary"
					 @click="dialogTipsFun">{{ lang.ok }}</el-button>
        </span>
			</template>
		</el-dialog>
		<el-drawer :modal-append-to-body="false"
				   :visible.sync="showDetails"
				   :with-header="false"
				   custom-class="drawDialog"
				   :size="device === 'mb' ? '80%' : '800px'">
			<div class="rightArea rightFixedArea">
				<WristbandDetails @submitInquiry="submitInquiry"
								  @addToCart="addToCart"
								  @jump="jump"
								  :showQty="false"
								  :currencyList="currencyList"
								  :showMore.sync="showMore"
								  :calculateData="calculateData"
								  :defaultData="defaultData"
								  :textInfo="textInfo"
								  :totalQuantity="totalQuantity"
								  :copyCanvasData="copyCanvasData"
								  :uploadList="uploadList"
								  :hasComment="hasComment"
								  :selectedData="selectedData"
								  :generalData="generalData"
								  noDetails
								  filter>
					<article class="canvas">
						<img :src="canvasToImage"
							 alt=""
							 loading="lazy"/>
					</article>
				</WristbandDetails>
				<div class="fold-icon"
					 @click="showDetails = false"><i class="el-icon-caret-left arrow"></i></div>
			</div>
		</el-drawer>
		<PreviewBtn :showArea.sync="showDetails"></PreviewBtn>
		<BaseDialog v-model="showOtoDialog"
					:width="device != 'mb' ? '570px' : '90%'">
			<div class="otoWrap">
				<img src="@/assets/images/oto.png"
					 alt=""
					 loading="lazy"/>
				<p>{{ lang.p1 }}</p>
				<h3>{{ lang.p2 }}</h3>
				<p style="color: #666666">
					{{ lang.p3 }}<br/>
					{{ lang.p4 }}.
				</p>

				<div class="box">
					<p class="t1">
						{{ lang.p5 }} <br/>
						{{ lang.p6 }}!
					</p>
					<a href="https://www.o2o.co/previewWeb"
					   style="text-decoration: none">
						<el-button type="primary">{{ lang.p7 }}</el-button>
					</a>
				</div>
			</div>
		</BaseDialog>
		<BaseDialog v-model="picDialog"
					:width="device == 'ipad' ? '500px' : device != 'mb' ? '800px' : '90%'">
			<div class="picWrap"
				 :style="{ 'aspect-ratio': zoomAspectRatio }">
				<template v-if="getFileSuffix(zoomPic) === '.mp4'">
					<VideoPlayer disabledMouse
								 :options="getVideoOptions(zoomPic, 2)"></VideoPlayer>
				</template>
				<template v-else>
					<img :src="zoomPic"
						 alt=""
						 loading="lazy"/>
				</template>
				<DialogBM :dialogItem="dialogItem"
						  @dialogNextStep="dialogNextStep"
						  v-if="tempType == 'video'"></DialogBM>
				<QtyAndBtn v-else
						   :dialogItem.sync="dialogItem"
						   @qtyAndBtnConfirm="qtyAndBtnConfirm"></QtyAndBtn>
			</div>
		</BaseDialog>
		<infoDialog :infoDialogVisible.sync="infoDialogVisible"
					:otherUpload="cardBoxFile ? fontImgCustom.concat(cardBoxFile) : fontImgCustom"
					:uploadList.sync="uploadList"
					@getValue="getValueFun"></infoDialog>
		<input type="file"
			   ref="upload1"
			   :accept="acceptFileType"
			   @change="uploadPic"
			   hidden/>
		<input type="file"
			   ref="upload2"
			   :accept="acceptFileType"
			   @change="uploadPic2"
			   hidden/>
		<input type="file"
			   ref="upload3"
			   :accept="acceptFileType"
			   @change="uploadPicClipart"
			   hidden/>
		<BaseDialog v-model="noFileDialog"
					:width="device != 'mb' ? '485px' : '90%'"
					:model="false">
			<template #closeIcon>
				<div style="display: none"></div>
			</template>
			<infoUpload :infoUploadList="infoUploadList"
						@pushInfoList="pushInfoList"
						@delInfoList="delInfoList"
						@updateInquiry="updateInquiry"
						@closeInfoDialog="closeInfoDialog"></infoUpload>
		</BaseDialog>
	</div>
</template>
<script>
import "@/plugins/element";
import BaseDialog from "@/components/Quote/BaseDialog";
import VideoPlayer from "@/components/Quote/VideoPlayer.vue";
import DialogBM from "@/components/Medals/DialogBM";
import QtyAndBtn from "@/components/Medals/QtyAndBtn";
import Corner from "@/components/Medals/Corner";
import DateBox from "@/components/Medals/DateBox";

import StepTime from "@/components/Quote/StepTime.vue";
import PublicStep from "@/components/Quote/PublicStep/index.vue";
import myMask from "@/components/Quote/Mask.vue";
import PreviewBtn from "@/components/Quote/PreviewBtn.vue";

import infoDialog from "@/components/Medals/infoDialog";
import infoUpload from "@/components/Medals/infoUpload";
import WristbandDetails from "@/components/WristBand/WristbandDetails";
import BookCheckBox from "@/components/WristBand/BookCheckBox.vue";
import CanvasFactory from "@/components/WristBand/CanvasFactory";
import DesignStep from "@/components/WristBand/DesignStep";
import TabsStep from "@/components/WristBand/TabsStep";
import quoteBanChoiceMixins from "@/mixins/quoteBanChoice";
import quoteMixin from "@/mixins/quote";
//识图
import {
	debounce,
	deepClone,
	getFileSuffix,
	scrollToViewTop,
} from "@/utils/utils";
import {uploadFile} from "@/utils/oss";
import {medalsApi} from "@/api/medals/medals";
import {indexApi} from "@/api/lanyardQuote";
import {Loading} from "element-ui";
import {
	otoAddCart,
	otoEditInquiry,
	setInquiry,
	otoEditCart,
} from "@/api/pins";
import {checkFile, acceptFileType} from "@/utils/validate";
import {getQuoteConfig} from "@/assets/js/quoteConfig";
import QuoteNav from "@/components/Medals/QuoteNav.vue";
import {dictModel} from "./entity/wristband";
import LanyardCheckBox from "@/components/Lanyard/LanyardCheckBox.vue";
import ToolTip from "@/components/Quote/ToolTip.vue";
import StepTitle from "@/components/Quote/PublicStep/StepTitle.vue";

export default {
	components: {
		StepTitle, ToolTip,
		LanyardCheckBox,
		QuoteNav,
		BaseDialog,
		VideoPlayer,
		DialogBM,
		QtyAndBtn,
		Corner,
		CanvasFactory,
		WristbandDetails,
		TabsStep,
		DateBox,
		infoDialog,
		infoUpload,
		StepTime,
		PublicStep,
		DesignStep,
		myMask,
		PreviewBtn,
		BookCheckBox
	},
	mixins: [quoteBanChoiceMixins, quoteMixin],
	data() {
		const config = getQuoteConfig.call(this, this.$route.name);
		return {
			dictModel: new dictModel(),
			colorList: [],
			acceptFileType,
			hideContent: false, //隐藏分类选择
			colorKey: 0,
			showAlert: false,
			// presentedQuantity:null,
			checked: false,
			isIframe: false,
			hidePreview: false,
			isUpload: false,
			// 比较大类id(来自不可选参数)
			compareParentFlag: true,
			customColor: [],
			comments: "",

			ropFontFamily1: "",
			ropFontFamily2: "",
			ropeText1: "",
			ropeText2: "",
			ropeFontFillStyleName1: "",
			ropeFontFillStyleName2: "",

			isActive: null,
			showRightArea: true,
			infoDialogVisible: false,
			beforeTempLogo: "",
			afterTempLogo: "",
			uploadList: [],
			uploadList2: [],
			dialogTips: false,
			fontBeforeImg: "",
			fontAfterImg: "",
			replaceIndex: -1,
			productsCategoriesName: "",
			totalColorNum: 0,
			lanyardBadgeHolderOptionsArr: [],
			badgeReelsArr: [],
			beforeAfter: "",
			canvasToImage: "",
			currentPopularCardItem: {},
			currentPopularCardList: [],
			currentCustomCardItem: {},
			fontImgCustom: [],
			cardUploadList: [],

			colorCardParam: {},
			copyCanvasData: {
				sizeName: "1/4 Inch",
				rowsLimit: 1,
				frontTextPositionX: 450,
				frontTextPositionY: 430,
				insideTextPositionX: 410,
				insideTextPositionY: 310,
				baseImg:
					"https://static-oss.gs-souvenir.com/web/quoteManage/20250308/outline_20250308iGHW4T.png", // 底图
				shadowImg:
					"https://static-oss.gs-souvenir.com/web/quoteManage/20250224/1_AO_00006_202502204nTW3y_1_20250224eFjTzb.png",
				colorImg:
					"https://static-oss.gs-souvenir.com/web/quoteManage/20250220/20220215Nh36GFJB_20250220t3ejfM.png",

				messageStyle: "front",
				beforeTempLogo: null,
				afterTempLogo: null,
				canvasWdith: 1000,
				canvasHeight: 600,
				colorStyle: "Solid",
				basefontSize: 80,
				innerHeight: 150,
				isBack: false,
				clipPoints: [
					{
						x: 58,
						y: 150,
					},
					{
						x: 938,
						y: 150,
					},
					{
						x: 938,
						y: 190,
					},
					{
						x: 860,
						y: 300,
					},
					{
						x: 938,
						y: 310,
					},
					{
						x: 938,
						y: 480,
					},
					{
						x: 58,
						y: 480,
					},
					{
						x: 58,
						y: 310,
					},
					{
						x: 130,
						y: 300,
					},
					{
						x: 58,
						y: 190,
					},
				],
				frontTextGroup: [
					{
						id: Date.now(),
						text: "",
						family: "",
						weight: false,
						italic: false,
						color: "",
						colorShow: false,
						textLimit: 30,
					},
				],
				backTextGroup: [
					{
						id: Date.now() + 1,
						text: "",
						family: "",
						weight: false,
						italic: false,
						color: "",
						colorShow: false,
						textLimit: 30,
					},
				],
				insideTextGroup: [
					{
						id: Date.now() + 2,
						text: "",
						family: "",
						weight: false,
						italic: false,
						color: "",
						colorShow: false,
						textLimit: 60,
					},
				],
				aroundTextGroup: [
					{
						id: Date.now() + 3,
						text: "",
						family: "",
						weight: false,
						italic: false,
						color: "",
						colorShow: false,
						textLimit: 80,
					},
				],
				frontTextPositionX: 500,
				frontTextPositionY: 420,
				insideTextPositionX: 500,
				insideTextPositionY: 220,
				backTextPositionX: 500,
				backTextPositionY: 420,
				aroundTextPositionX: 500,
				aroundTextPositionY: 420,

				frontStartClipartValue: 1,
				frontEndClipartValue: 1,
				backStartClipartValue: 1,
				backEndClipartValue: 1,
				aroundStartClipartValue: 1,
				aroundEndClipartValue: 1,

				frontStartImg: "",
				frontEndImg: "",
				backStartImg: "",
				backEndImg: "",
				aroundStartImg: "",
				aroundEndImg: "",
			},

			isBack: false,
			tabsName: "POPULAR",
			defActiveAttachment: "",
			printingData: null,
			canvasData: {},
			currencyData: {},
			RibbonBindValue: {},
			showMore: true,
			selectedObj: {},
			zoomAspectRatio: 1,
			tempType: null,
			tempItem: null,
			dialogItem: {},
			zoomPic: "",
			picDialog: false,
			hasComment: false,
			totalQuantity: 0,
			showOtoDialog: false,
			showDetails: false,
			debounceCalcPrice: "",
			defaultData: {},
			remark: "",
			calculateData: {},
			cateId: null,
			commentTarget: {},
			commentDialogVisible: false,
			currencyList: [],
			going: false, //step模式
			selectedData: {},
			generalData: [], //总纲
			wristbandStyleData: {
				stepIndex: 1,
				childList: [],
				paramName: "Wristband Style",
			}, //大类
			fontLogo:
				"http://res.cloudinary.com/gs-jj-cloud/image/upload/v1678673484/gs-jj/eod0cz3u4txokf6xpvzp.png",
			logo: "https://res.cloudinary.com/gs-jj-cloud/image/upload/v1677139759/gs-jj/soikei6sst8uo5r6cvbl.png", //logo
			noFileDialog: false,
			infoUploadList: [],
			inquiryId: 0,
			cardBoxFile: [],
			presentedQuantity: 0, //贈送總數
			noQuantityId: -1,
			lanyardType: "",
			...config,
		};
	},

	methods: {
		getTitle(item) {
			return this.lang.Select + " " + item.alias;
		},
		getStep(item) {
			return this.lang.step + " " + item.customIndex;
		},

		selectDefParam() {
			this.selectedData["Design Your Band"] = [
				this.getInfo("Design Your Band").childList[0],
			];
		},

		parseJSON(str) {
			return str
				? JSON.parse(str)
				: [
					{
						url: "",
					},
				];
		},
		imgUrl(row, index = 0) {
			return this.parseJSON(row.imageJson)[index].url;
		},

		async customColorAdd(color) {
			const url = await this.drawColorToImage(color.code); // 生成颜色图片
			// 检查是否已经存在相同的 id
			const existingColorIndex = this.customColor.findIndex(
				(item) => item.id === color.id
			);
			if (existingColorIndex !== -1) {
				// 如果存在，则更新该颜色对象
				this.$set(this.customColor, existingColorIndex, {...color, img: url});
				this.$set(
					this.selectedData["Band Color & Quantity"],
					existingColorIndex,
					{...color, img: url}
				);
				this.updateColor({...color, img: url});
			} else {
				// 如果不存在，则新增
				this.customColor = [...this.customColor, {...color, img: url}];
				this.updateColor({...color, img: url});
			}
		},

		// 更新手环颜色
		updateColor(item) {
			if (item.img && item.type != "Solid") return;
			if (item.img) {
				this.copyCanvasData.colorImg = item.img;
			} else {
				this.copyCanvasData.colorImg = this.imgUrl(item, 1);
			}
			this.copyCanvasData.colorStyle = item?.type;
			this.$nextTick(() => {
				this.debounceCanvasInit();
			});
		},

		// 修改文字设计参数
		canvasFontFormFun(obj) {
			//   this.copyCanvasData = Object.assign(this.copyCanvasData, obj);
			if (this.copyCanvasData.commentsImgLater === true) {
				this.fontImgCustom = [];
			}
			this.$nextTick(() => {
				this.debounceCanvasInit();
				this.debounceCalcPrice();
			});
		},

		/**
		 * 将逗号分隔的颜色字符串绘制成 PNG 图片
		 * @param {string} colorCode - 逗号分隔的颜色字符串
		 * @returns {Promise<string>} - 返回 PNG 图片的 Base64 URL
		 */
		drawColorToImage(colorCode) {
			return new Promise((resolve, reject) => {
				console.log("drawColorToImage", colorCode);
				// try {
				// 解析颜色字符串
				const colors = colorCode.split(",");

				// 创建 Canvas 画布
				const canvas = document.createElement("canvas");
				canvas.width = 500;
				canvas.height = 500;
				const ctx = canvas.getContext("2d");

				// 计算每个颜色块的宽度
				const colorWidth = canvas.width / colors.length;

				// 绘制颜色块
				colors.forEach((color, index) => {
					ctx.fillStyle = color.trim(); // 去除空格并设置颜色
					ctx.fillRect(index * colorWidth, 0, colorWidth, canvas.height);
				});

				// 生成 PNG 图片
				const imageUrl = canvas.toDataURL("image/png");
				resolve(imageUrl);
				// } catch (error) {
				//   reject(error);
				// }
			});
		},

		// 切换手环方向
		changeDirection() {
			this.copyCanvasData.isBack = !this.copyCanvasData.isBack;
			this.$nextTick(() => {
				this.debounceCanvasInit();
			});
		},
		formatSubIndex(index, subIndex) {
			return String(index).padStart(2, "0") + "-" + subIndex;
		},
		pushInfoList(data) {
			this.infoUploadList.push(data);
		},
		delInfoList(index) {
			this.infoUploadList.splice(index, 1);
		},
		updateInquiry() {
			if (!this.inquiryId) {
				this.closeInfoDialog(false);
			}
			let newPicPath = this.infoUploadList.map((item) => item.secure_url);
			let data = {
				id: this.inquiryId,
				newPicPath: newPicPath,
			};
			setInquiry(data).then(() => {
				this.closeInfoDialog(false);
			});
		},
		closeInfoDialog(data) {
			this.infoUploadList = [];
			this.noFileDialog = data;
			if (this.isIframe) {
				let targetWindow = window.opener || window.parent; // 目标窗口，可以是父窗口或打开当前窗口的窗口
				targetWindow.postMessage(
					{
						type: "toHome",
					},
					window.origin
				); // 发送消息
			} else {
				window.location.href = "/";
			}
		},
		updateUploadListCard(item) {
			this.cardBoxFile = [item];
			this.$set(
				this.selectedData["Select Your Card"][0],
				"uploadImg",
				item.secure_url
			);
		},
		updateUploadList(item) {
			this.fontImgCustom.push(item);
		},

		async dialogTipsFun() {
			this.dialogTips = false;
			let target = this.getInfo("Lanyard Style");
			this.selectFun(
				{
					key: target.paramName,
					value: target.childList[0],
				},
				0
			);
		},
		convertCanvasToImage(canvas) {
			return new Promise((resolve, reject) => {
				var userName = "canvasImg";
				var fileName = userName + ".jpg"; //vm.addUserName
				var firstName = fileName.charAt(0);
				var dataurl = canvas.toDataURL("image/png", 1.0);
				var arr = dataurl.split(","),
					mime = arr[0].match(/:(.*?);/)[1],
					bstr = atob(arr[1]),
					n = bstr.length,
					u8arr = new Uint8Array(n);
				while (n--) {
					u8arr[n] = bstr.charCodeAt(n);
				}
				var file = new File([u8arr], fileName, {
					type: mime,
				});
				// file.lastModifiedDate = new Date();
				uploadFile(file).then((res) => {
					resolve(res);
				});
			});
		},
		beforeAfterFun(val) {
			this.beforeAfter = val;
		},
		canvasToImageFun(val) {
			this.canvasToImage = val;
		},
		selectPritingMethodFun(obj) {
			this.selectedData["Select Printing Method"] = [obj];
		},
		getInfo(name, dataName = "filterShowGeneralData") {
			return this[dataName].find((x) => {
				return x.paramName == name;
			});
		},
		getTabsName(val) {
			this.tabsName = val;
		},
		printingDataFun(val) {
			this.printingData = val;
		},
		//所有色卡
		findAll() {
			indexApi.findAll().then((res) => {
				this.colorList = res.data;
			});
		},
		qtyAndBtnConfirm(val) {
			this.picDialog = false;
			this.selectFun(this.selectedObj);
		},
		dialogNextStep() {
			this.picDialog = false;
			this.selectFun(this.selectedObj);
		},
		getVideoOptions(path, type, poster) {
			if (type === 1) {
				return {
					autoplay: true,
					controls: false,
					muted: true,
					loop: true,
					fill: true,
					sources: [
						{
							src: path,
							type: "video/mp4",
						},
					],
				};
			} else if (type === 2) {
				return {
					autoplay: true,
					controls: true,
					muted: false,
					loop: true,
					fill: true,
					sources: [
						{
							src: path,
							type: "video/mp4",
						},
					],
				};
			} else if (type === 3) {
				return {
					autoplay: true,
					controls: false,
					muted: true,
					loop: true,
					fill: true,
					sources: [
						{
							src: path,
							type: "video/mp4",
						},
					],
				};
			} else if (type === 4) {
				return {
					autoplay: false,
					controls: false,
					muted: true,
					loop: true,
					fill: true,
					sources: [
						{
							src: path,
							type: "video/mp4",
						},
					],
					poster: poster,
				};
			}
		},
		getFileSuffix(url) {
			return getFileSuffix(url);
		},
		shutComment() {
			this.commentDialogVisible = false;
			this.checkForm();
		},
		commentSubmit() {
			this.commentDialogVisible = false;
			this.checkForm();
		},
		zoomPicFun($event, val, type) {
			this.tempType = type;
			this.picDialog = true;
			this.selectedObj = $event;
			this.dialogItem = val;

			if (this.tempType == "video") {
				this.zoomPic = val.videoPath ? val.videoPath : val.priceInfo.videoPath;
			} else {
				this.zoomPic =
					val.priceInfo?.videoPath ||
					val.priceInfo.imagePath ||
					JSON.parse(val.imageJson)[0].url;
			}
		},
		//购物车
		async addCart() {
			if (this.$store.state.isTemporary) {
				this.$store.commit("setMask", "temporary");
				return false;
			}
			this.$gl.show();
			let quoteParam = await this.getQuoteParam();
			let priceParam = await this.getPriceParam();
			let data = {
				proId: this.proId,
				uuid: this.isLogin ? null : this.userUUID,
				userId: this.isLogin ? this.userId : null,
				buyType: 7,
				quoteCateId: this.pid,
				quoteCateChildId: this.cateId,
				quotePriceParam: JSON.stringify(priceParam),
				quoteParam: JSON.stringify(quoteParam),
				isOneDollarPens: 0,
			};
			if (this.$route.query.isBack) {
				data.id = this.$route.query.id;
				otoEditCart(data)
					.then((res) => {
						this.$toast.success(res.message);
						let targetWindow = window.opener || window.parent;
						targetWindow.postMessage(
							{
								type: "closeDialog",
								data: {
									isUpdate: 1,
								},
							},
							window.origin
						);
					})
					.finally(() => {
						setTimeout(() => {
							this.loadAddCart = false;
						}, 1000);
					});
			} else {
				otoAddCart(data, this.priceData).then((res) => {
					this.$toast.success(res.message);
					this.pageQuoteDialog && this.$store.commit("setMask", false);
					if (this.isIframe) {
						let targetWindow = window.opener || window.parent; // 目标窗口，可以是父窗口或打开当前窗口的窗口
						const message = {type: "toCart"};
						targetWindow.postMessage(message, window.origin);
					} else {
						this.$router.push({
							path: "/cart",
						});
					}
					this.$gl.hide();
				});
			}
		},
		//添加询盘
		async addInquiry() {
			//如果是临时项目
			if (this.$store.state.isTemporary) {
				this.$store.commit("setMask", "temporary");
				return false;
			}
			this.infoDialogVisible = true;
		},
		async getValueFun(val) {
			this.$gl.show();
			let quoteParam = await this.getQuoteParam();
			let priceParam = await this.getPriceParam(); ///
			//询盘如果有期待时间，finaData手动添加数据
			if (val.expectTime) {
				quoteParam.finaData.push({
					parentId: 0,
					alias: "Expected Delivery Date",
					childList: [
						{
							parentId: 10000,
							alias: val.expectTime,
						},
					],
				});
			}
			let data = {
				platformProductId: this.platformProductId,
				proId: this.proId,
				email: "",
				productsName: "LandYard",
				quoteCateId: this.pid,
				quoteCateChildId: this.cateId,
				isMobile: this.device === "mb" ? 1 : 0,
				buyType: 7,
				quotePriceParam: JSON.stringify(priceParam), ///
				quoteParam: JSON.stringify(quoteParam),
				notes: "",
				...val,
				telephone: val.areaCode + "-" + val.telephone,
				giftQuantityTotal: this.presentedQuantity, //赠送总数
			};
			//经销商
			// data.email = val.email;
			// data.firstName = val.firstName;
			// data.lastName = val.lastName;
			// data.telephone = val.telephone;
			this.otoEditInquiry(data);
		},
		otoEditInquiry(data) {
			otoEditInquiry(data).then((res) => {
				this.inquiryId = res.data;
				this.$gl.hide();
				// if(this.uploadList.length==0&&this.fontImgCustom.length==0&&this.cardBoxFile.length==0){
				//  this.noFileDialog=true
				// }else{
				this.$confirm(this.lang.p22, this.lang.p21, {
					confirmButtonText: this.lang.Confirm,
					type: "success",
					showCancelButton: false,
					center: true,
					customClass: "inquirySuccess",
					confirmButtonClass: "inquirySuccessBtn",
				}).finally(() => {
					if (this.isIframe) {
						let targetWindow = window.opener || window.parent; // 目标窗口，可以是父窗口或打开当前窗口的窗口
						const message = {
							type: this.$route.query.isBack ? "toCart" : "toHome",
						};
						if (this.$route.query.isBack) message.data = {isUpdate: 1};
						targetWindow.postMessage(message, window.origin); // 发送消息
					} else {
						window.location.href = "/";
					}
				});
				// }
			});
		},

		processTextData(data, isReverse = false) {
			// 正向解析：从原始格式转换为 textList
			const mapTextGroup = (textGroup, area) => {
				return textGroup
					.filter((x) => x.text.trim().length > 0) // 过滤掉空文本
					.map((x) => ({
						fontWeight: x.weight ? "bold" : "normal",
						fontFamily: x.fontFamily,
						text: x.text,
						fontStyle: x.italic ? "italic" : "normal",
						color: x.color,
						area,
					}));
			};

			// 逆向解析：从 textList 转换为原始格式
			const reverseParseTextList = (textList) => {
				const result = {};

				textList.forEach((item) => {
					const {area, ...rest} = item;

					// 拼接字段名，例如 "front" -> "frontTextGroup"
					const fieldName = `${area}TextGroup`;

					if (!result[fieldName]) {
						result[fieldName] = [];
					}
					result[fieldName].push({
						weight: rest.fontWeight === "bold",
						fontFamily: rest.fontFamily || "",
						text: rest.text,
						italic: rest.fontStyle === "italic",
						color: rest.color,
						colorShow: false,
						textLimit: area == "inside" ? 60 : area == "around" ? 80 : 30,
					});
				});

				return result;
			};

			// 根据 isReverse 参数决定执行正向解析还是逆向解析
			if (isReverse) {
				return reverseParseTextList(data);
			} else {
				const areas = ["front", "back", "inside", "around"];
				const textGroups = areas.map((area) => ({
					group: data[`${area}TextGroup`],
					area,
				}));

				return textGroups.flatMap(({group, area}) =>
					mapTextGroup(group, area)
				);
			}
		},

		//询盘数据
		getQuoteParam() {
			return new Promise(async (resolve, reject) => {
				let copyGeneralData = JSON.parse(JSON.stringify(this.generalData));
				let copySelectedData = JSON.parse(JSON.stringify(this.selectedData));
				let theCanvas = document.getElementById("canvas");
				let listKey = Object.keys(copySelectedData);
				// 过滤掉 paramName 为 'Wristband Style' 的项
				copyGeneralData = copyGeneralData.filter(
					(item) => item.paramName !== "Wristband Style"
				);
				// 按照 listKey 的顺序重新排列
				const matchedData = listKey
					.map((key) => copyGeneralData.find((item) => item.paramName === key))
					.filter(Boolean);
				const unmatchedData = copyGeneralData.filter(
					(item) => !listKey.includes(item.paramName)
				);
				copyGeneralData = matchedData.concat(unmatchedData);
				for (let x in copySelectedData) {
					copyGeneralData.forEach((item) => {
						if (item.paramName == x) {
							if (item.paramName == "Band Color & Quantity") {
								const mergedData = {};
								copySelectedData[x].forEach((y) => {
									const {adult, youth, typeAlias, alias} = y;

									// 生成唯一键（typeAlias + alias）
									const key = `${typeAlias || ""}:${alias}`;

									// 如果键已存在，合并 adult 和 youth
									if (mergedData[key]) {
										mergedData[key].adult += adult || 0;
										mergedData[key].youth += youth || 0;
									} else {
										// 如果键不存在，初始化数据
										mergedData[key] = {
											...y,
											adult: adult || 0,
											youth: youth || 0,
										};
									}
								});
								// 将合并后的数据转换回数组
								const result = Object.values(mergedData);
								// 更新 alias 标签
								result.forEach((y) => {
									const {adult, youth, typeAlias, alias} = y;
									const labels = [];
									adult &&
									labels.push(`${this.lang.wristband.adultQty}：${adult}`);
									youth &&
									labels.push(`${this.lang.wristband.youthQty}：${youth}`);
									y.alias = `${
										typeAlias ? typeAlias + "：" : ""
									}${alias}(${labels.join(", ")})`;
								});
								// 将结果赋值回 copySelectedData[x]
								copySelectedData[x] = result;
							} else if (item.paramName == "Design Your Band") {
								copySelectedData[x].childList = [];
							}
							item.childList = copySelectedData[x];
						}
					});
				}

				this.convertCanvasToImage(theCanvas).then((res) => {
					let files = [];
					let artworkList = this.fontImgCustom.concat(this.uploadList);
					if (artworkList.length > 0) {
						artworkList.forEach((item) => {
							files.push(item.secure_url);
						});
					}
					// 文字参数 正向解析
					let textList = this.processTextData(this.copyCanvasData);
					resolve({
						classificationData: copySelectedData["Wristband Style"][0],
						finaData: copyGeneralData,
						textList,
						fontData: {
							frontStartImg: this.copyCanvasData.frontStartImg,
							frontEndImg: this.copyCanvasData.frontEndImg,
							backStartImg: this.copyCanvasData.backStartImg,
							backEndImg: this.copyCanvasData.backEndImg,
							aroundStartImg: this.copyCanvasData.aroundStartImg,
							aroundEndImg: this.copyCanvasData.aroundEndImg,
							frontStartClipartValue:
							this.copyCanvasData.frontStartClipartValue,
							frontEndClipartValue: this.copyCanvasData.frontEndClipartValue,
							backStartClipartValue: this.copyCanvasData.backStartClipartValue,
							backEndClipartValue: this.copyCanvasData.backEndClipartValue,
							aroundStartClipartValue:
							this.copyCanvasData.aroundStartClipartValue,
							aroundEndClipartValue: this.copyCanvasData.aroundEndClipartValue,
							comments: this.comments ? this.comments : "",
							fontImgCustom: files || [], //自定义图片
						},
						canvasData: {
							url: res,
						},
					});
				});
			});
		},
		//价格数据
		getPriceParam() {
			return new Promise((resolve, reject) => {
				let accessoriesDetailDTOS = [],
					qtyDetailDTOS = [],
					paramIdList = [],
					upgradesQtyDTO = [];

				for (let i in this.selectedData) {
					if (i == "Band Color & Quantity") {
						this.selectedData[i].forEach((item) => {
							if (item.cateId) {
								let tempObj = {
									quantity: (item.adult || 0) + (item.youth || 0),
									paramType: "COLOR",
									paramId: item.priceInfo.id,
									paramValue: item.paramCode,
									giftQuantity: item.giftQuantity ? item.giftQuantity : 0,
									isQuoteQuantity: item.giftQuantity ? 1 : 0,
								};
								qtyDetailDTOS.push(tempObj);
							} else {
								let tempObj = {
									quantity: (item.adult || 0) + (item.youth || 0),
									paramType: "COLORCARD",
									paramId: this.colorCardParam.priceInfo?.id,
									paramValue: item.code,
									giftQuantity: item.giftQuantity ? item.giftQuantity : 0,
									isQuoteQuantity: item.giftQuantity ? 1 : 0,
								};
								qtyDetailDTOS.push(tempObj);
							}
						});
					} else if (i == "Message Style") {
						this.selectedData[i].forEach((item) => {
							paramIdList.push(item.priceInfo.id);
						});
					} else if (i == "Design Your Band") {
						this.selectedData[i].forEach((item) => {
							paramIdList.push(item.priceInfo.id);
						});
						// inside加价
						if (this.copyCanvasData.insideTextGroup[0].text.trim().length) {
							const insideItem = this.selectedData[i][0].childList[0];
							paramIdList.push(insideItem.priceInfo.id);
						}
					} else if (i == "More Options") {
						this.selectedData[i].forEach((item) => {
							paramIdList.push(item.priceInfo.id);
						});
					} else if (i == "Packaging Options") {
						this.selectedData[i].forEach((item) => {
							paramIdList.push(item.priceInfo.id);
						});
					}
				}

				resolve({
					qtyDetailDTOS: qtyDetailDTOS, //ok
					accessoriesDetailDTOS: accessoriesDetailDTOS, //ok
					cateId: this.cateId, //ok
					paramIdList: paramIdList, //
					projectName: this.projectName, //ok
					sizeId:
						this.selectedData["Band Size"] &&
						this.selectedData["Band Size"][0]?.priceInfo.id, //ok
					discountId: this.selectedData["Turnaround Time"][0]?.priceInfo.id, //ok
					upgradesQtyDTO: upgradesQtyDTO, //
				});
			});
		},
		//算价格
		async calculate() {
			if (this.selectedData["Band Size"].length) {
				let postData = await this.getPriceParam();
				if (!postData.discountId) {
					return;
				}
				indexApi.calculate(postData).then((res) => {
					let {data} = res;
					this.calculateData = data;
				});
			}
		},
		replayUpload() {
			this.$store.commit("setSizeDialog", false);
			this.$refs.upload3.click();
		},
		uploadPic(event) {
			this.$gl.show();
			let files = event.target.files;
			uploadFile(files[0])
				.then((res) => {
					let temp = {
						original_filename: files[0].name,
						secure_url: res,
					};
					if (!this.selectedData["Ribbon"][0].uploadList) {
						this.selectedData["Ribbon"][0].uploadList = [];
					}
					let tempArr = JSON.parse(
						JSON.stringify(this.selectedData["Ribbon"][0].uploadList)
					);
					this.selectedData["Ribbon"][0].uploadList = [];
					tempArr ? tempArr.push(temp) : (tempArr = [temp]);
					this.selectedData["Ribbon"][0].uploadList = tempArr;
					this.$refs.upload1.value = "";
					this.$forceUpdate();
				})
				.finally(() => {
					this.$gl.hide();
				});
		},
		uploadPic2(event) {
			this.$gl.show();
			let files = event.target.files;
			uploadFile(files[0])
				.then((res) => {
					let temp = {
						original_filename: files[0].name,
						secure_url: res,
					};
					this.selectedData["Upload Artwork & Comments"][0].uploadList
						? this.selectedData["Upload Artwork & Comments"][0].uploadList.push(
							temp
						)
						: (this.selectedData["Upload Artwork & Comments"][0].uploadList = [
							temp,
						]);
					this.$refs.upload2.value = "";
					this.$forceUpdate();
				})
				.finally(() => {
					this.$gl.hide();
				});
		},
		//Design Your Printing 上传
		uploadPicClipart(event, type = "upload") {
			this.$gl.show();
			let files = type === "upload" ? event.target.files : event;
			let fileResult = checkFile(Array.from(files), this.acceptFileType, 80);
			if (!fileResult) {
				this.$toast.error("File type error");
				this.$gl.hide();
				return false;
			}
			if (fileResult.nomalSize.length == 0) {
				this.$gl.hide();
				this.$store.commit("setSizeDialog", true);
				this.$store.commit("setInputRefName", "uploadPicClipart");
				this.$store.commit("setOverSizeList", fileResult.overSize);
				this.$refs.upload3.value = "";
				return false;
			}
			uploadFile(fileResult.nomalSize[0])
				.then((res) => {
					let temp = {
						original_filename: files[0].name,
						secure_url: res,
					};
					if (this.clipartType === "start") {
						//图片传给后台
						this.selectedData["Design Your Printing"][0].fontBeforeImg
							? this.selectedData["Design Your Printing"][0].fontBeforeImg.push(
								temp
							)
							: (this.selectedData["Design Your Printing"][0].fontBeforeImg = [
								temp,
							]);
						//图片回显
						this.fontBeforeImg = temp.secure_url;
					} else {
						this.selectedData["Design Your Printing"][0].fontAfterImg
							? this.selectedData["Design Your Printing"][0].fontAfterImg.push(
								temp
							)
							: (this.selectedData["Design Your Printing"][0].fontAfterImg = [
								temp,
							]);
						this.fontAfterImg = temp.secure_url;
					}
					this.$refs.upload3.value = "";
					this.$forceUpdate();
				})
				.finally(() => {
					this.$gl.hide();
					this.$toast.success("success");
				});
		},
		jump(val) {
			let target = val;
			this.maskName = target;
			this.scrollFun(target);
			this.showDetails = false;
		},
		nextStepFun(name) {
			this.checkForm().then((res) => {
				if (res) {
					this.scrollFun("foot");
					this.maskName = false;
				}
			});
			if (this.maskName == name) {
				this.errorMessage(name);
			}
		},
		errorMessage(name) {
			if (name == "Ribbon") {
				this.$toast.error("Please choose the ribbon options.");
			} else if (name == "Upload Artwork & Comments") {
				this.$toast.error("Please Upload your Artwork.");
			} else if (name == "Additional Upgrades (Options)") {
				this.$toast.error("Please Select Your Additional Upgrades.");
			} else if (name == "Medal Size") {
				this.$toast.error("Please Select Your Medal Size.");
			} else if (name == "Metal Finish") {
				this.$toast.error("Please Select Your Metal Finish.");
			} else if (name == "Lanyard Attachment") {
				this.$toast.error("Please Select Your Lanyard Attachment.");
			} else if (name == "Select Printing Method") {
				this.$toast.error("Please Select Your Printing Method");
			}
		},
		//校验
		checkForm(obj) {
			return new Promise(async (resolve) => {
				let boo = true;
				let that = this;
				let noPass = function (obj, key) {
					if (!obj) {
						that.scrollFun(key); //根据key滚动
					}
					that.maskName = obj ? obj : key; //下一步步骤名
					return false;
				};
				for (let key in this.selectedData) {
					const arr = this.selectedData[key];
					const item = arr.length ? arr[0] : {};
					if (key == "Design Your Band" && item.paramName == "Upload Artwork") {
						if (!this.fontImgCustom.length) {
							boo = noPass(obj, key);
							break;
						}
					} else if (
						key == "Design Your Band" &&
						item.paramName == "Text/Cliparts"
					) {
						if (
							this.copyCanvasData.messageStyle == "front" &&
							!this.copyCanvasData.frontTextGroup[0].text.trim().length
						) {
							boo = noPass(obj, key);
							break;
						}
						if (
							this.copyCanvasData.messageStyle == "frontback" &&
							(!this.copyCanvasData.backTextGroup[0].text.trim().length ||
								!this.copyCanvasData.frontTextGroup[0].text.trim().length)
						) {
							boo = noPass(obj, key);
							break;
						}
						if (
							this.copyCanvasData.messageStyle == "around" &&
							!this.copyCanvasData.aroundTextGroup[0].text.trim().length
						) {
							boo = noPass(obj, key);
							break;
						}
					} else if (arr.length == 0) {
						boo = noPass(obj, key);
						break;
					}
				}
				resolve(boo);
			});
		},
		//滚动
		scrollFun(target) {
			const element = document.getElementById(target);
			if (element) {
				let offset = 0;
				if (target === "foot") {
					offset = -100;
				}
				scrollToViewTop(element, offset);
			}
		},
		//通用
		async selectFun(item, citem) {
			this.selectedData[item.paramName] = Array.isArray(citem)
				? citem
				: [citem];
			if (item.paramName == "Band Size") {
				const currentSizeInfo = this.dictModel.sizeList.find(
					(x) => x.sizeName == citem.paramName
				);
				this.copyCanvasData.sizeName = currentSizeInfo.sizeName;
			} else if (item.paramName == "Message Style") {
				this.copyCanvasData.messageStyle = citem.paramCode;
			}

			this.$nextTick(() => {
				this.debounceCanvasInit();
			});
			await this.filterStepsFun().then((res) => (this.noChoiceData = res));
			if (
				item.paramName == "Band Color & Quantity" ||
				item.paramName == "Design Your Band"
			) {
				this.checkForm(item.paramName).then((res) => {
					if (res) {
						this.maskName = null;
					}
				});
			} else {
				this.checkForm().then((res) => {
					if (res) {
						this.scrollFun("foot");
						this.maskName = null;
					}
				});
			}

			this.debounceCalcPrice();
		},
		//询盘
		submitInquiry() {
			this.checkForm().then((res) => {
				if (res) {
					this.addInquiry();
				}
			});
		},
		//购物车
		addToCart() {
			this.checkForm().then((res) => {
				if (res) {
					this.addCart();
				}
			});
		},
		async getByPId() {
			await medalsApi
				.getByPId({
					pid: this.pid,
				})
				.then(async (res) => {
					this.wristbandStyleData.childList = res.data;
					this.cateId = this.wristbandStyleData.childList[0].id;
					this.lanyardStyleName = this.wristbandStyleData.childList[0].cateName;
					this.defaultData = this.wristbandStyleData.childList[0];
					await this.getCateParamRelationByCateId();
					await this.selectDefParam();
					this.getBanParamList(res.data[0].id);
					if (this.isIframe) {
						let targetWindow = window.opener || window.parent; // 目标窗口，可以是父窗口或打开当前窗口的窗口
						targetWindow.postMessage(
							{
								type: "selectStyle",
							},
							window.origin
						); // 发送消息
					}
					if (this.wristbandStyleData.childList.length === 1) {
						this.selectedData[this.wristbandStyleData.paramName] = [
							res.data[0],
						];
					}
				});
		},
		movearritem(arr, key, item) {
			for (var i = 0; i < arr.length; i++) {
				if (arr[i] == key) {
					arr.splice(arr.indexOf(item) + 1, 0, arr[i]); //index:元素需要放置的位置索引，从0开始
					arr.splice(i + 1, 1); //移除原来位置上的该元素
					break;
				}
			}
			return arr;
		},

		setIndex(data) {
			if (!data) {
				return false;
			}
			let index = 0;
			for (let i = 0; i < data.length; i++) {
				let item = data[i];
				if (item) {
					index += 1;
					item.customIndex = index;
				}
			}
			return data;
		},
		sortByKey(array, key) {
			return array.sort(function (a, b) {
				let x = a[key]; //如果要从大到小,把x,y互换就好
				let y = b[key];
				return x < y ? -1 : x > y ? 1 : 0;
			});
		},
		getCateParamRelationByCateId() {
			this.closeMask();
			return new Promise((resolve, reject) => {
				let loadingInstance = Loading.service({
					target: "#leftArea",
				});
				medalsApi
					.getCateParamRelationByCateId({
						cateId: this.cateId,
					})
					.then((res) => {
						this.generalData = res.data;
						this.generalData = this.setIndex(
							this.sortByKey(this.generalData, "stepIndex")
						);
						this.generalData = this.generalData.concat(this.wristbandStyleData);
						this.colorCardParam = this.generalData.splice(
							this.generalData.findIndex((x) => x.paramName == "color card"),
							1
						)[0];
						//副本
						let selectedData = JSON.parse(JSON.stringify(this.selectedData));
						let tempObj = {},
							tempObj2 = {},
							tempArr = [];

						this.generalData.forEach((item, index) => {
							item.childList.forEach((x) => {
								x.inputNum = undefined;
							});
							if (item.paramName == "Upload Artwork & Comments") {
								tempObj[item.paramName] = [item];
							} else {
								tempObj[item.paramName] = [];
							}
							tempArr.push(item.paramName);
						});
						tempArr.forEach((item) => {
							tempObj2[item] = tempObj[item];
						});

						for (let item in tempObj2) {
							if (selectedData[item]) {
								tempObj2[item] = selectedData[item];
							}
						}

						//副本赋值
						this.selectedData = tempObj2;

						this.$nextTick(() => {
							this.debounceCanvasInit();
						});
						this.$nextTick(() => {
							loadingInstance.close();
						});
						resolve(res);
					});
			});
		},
		debounce(func, delay = 1000, immediate = false) {
			//闭包
			let timer = null;
			//不能用箭头函数
			return function () {
				if (timer) {
					clearTimeout(timer);
				}
				if (immediate && !timer) {
					func.apply(this, arguments);
				}
				timer = setTimeout(() => {
					func.apply(this, arguments);
				}, delay);
			};
		},
		canvasInitFun() {
			this.canvasData = Object.assign({}, this.copyCanvasData);
			this.$nextTick(() => {
				try {
					this.$refs.CanvasFactory.canvasInit();
				} catch (e) {
				}
			});
		},
		backFillByCart() {
			try {
				if (!sessionStorage.getItem("quoteBackParam")) return;
				let cartData = JSON.parse(sessionStorage.getItem("quoteBackParam"));
				this.selectedData["quoteCategory"] = [cartData.classificationData];
				cartData.finaData.forEach((item) => {
					this.selectedData[item.paramName] = item.childList || [];
				});
				// 回填文字和图标
				const textList = this.processTextData(cartData.textList, true);
				this.copyCanvasData = Object.assign(
					this.copyCanvasData,
					cartData.fontData,
					textList
				);
				// 回填颜色，有 type 为自定义
				const bcQ = this.getInfo("Band Color & Quantity");
				this.selectedData["Band Color & Quantity"].forEach((x) => {
					if (x.type) {
						// 处理自定义颜色
						const aliasParts = x.alias?.split("：")[1]?.split("(")[0]?.trim();
						if (aliasParts) {
							x.alias = aliasParts;
							this.customColor.push(x);
						}
					} else {
						// 处理非自定义颜色
						const parent = bcQ.childList.find((y) => y.id === x.parentId);
						if (parent) {
							const child = parent.childList.find((o) => o.id === x.id);
							if (child) {
								x.alias = x.alias.split("(")[0].trim(); // 添加对 x.alias 的截取
								child.alias = x.alias;
								if (x.adult) child.adult = x.adult;
								if (x.youth) child.youth = x.youth;
							}
						}
					}
				});
				// 回填文件
				if (
					cartData.fontData.fontImgCustom &&
					cartData.fontData.fontImgCustom.length
				) {
					cartData.fontData.fontImgCustom.forEach((url) => {
						this.fontImgCustom.push({
							secure_url: url,
							original_filename: this.getImageNameFromURL(url),
						});
					});
				}
				const images = JSON.parse(cartData.imageJson);
				if (images.length) {
					images.forEach((item) => {
						if (!item.hasOwnProperty("isDesignImg")) {
							this.fontImgCustom.push({
								secure_url: item.url,
								original_filename: this.getImageNameFromURL(item.url),
							});
						}
					});
				}

				// 回填备注
				this.comments = cartData.comments || "";
			} catch (err) {
				console.log("backFillError", err);
			}
		},

		getImageNameFromURL(imageURL) {
			const lastIndex = imageURL.lastIndexOf("/");
			return imageURL.substring(lastIndex + 1);
		},
	},
	computed: {
		proType() {
			return this.$store.state.proType;
		},
		textInfo() {
			return {
				inquiryTip: this.lang.inquiryTip,
				addCartTip: this.lang.addCartTip,
				emailText:
					this.lang.emailText + " " + this.$store.state.proSystem.email,
				email: this.lang.mailto + this.$store.state.proSystem.email,
			};
		},
		device() {
			return this.$store.state.device;
		},
		projectName() {
			if (this.previewMode) {
				return "o2o-site-style";
			} else {
				return this.$store.state.proName;
			}
		},
		lang() {
			return this.$store.getters.lang.quote || {};
		},
	},
	watch: {
		copyCanvasData: {
			handler(val) {
				this.$nextTick(() => {
					this.debounceCanvasInit();
					this.debounceCalcPrice();
				});
			},
			deep: true,
		},
	},
	created() {
		this.debounceCalcPrice = debounce(this.calculate, 300);
		this.debounceCanvasInit = this.debounce(this.canvasInitFun, 300, false);
		this.findAll();
	},
	async mounted() {
		let _this = this;
		await this.getByPId();
		this.$Bus.$on("uploadPicClipart", this.replayUpload);
		this.isIframe = !!this.$route.query.type;
		if (this.$route.query.isBack) {
			//isBackParams为true 代表参数正在回填
			this.isBackParams = true;
			await this.backFillByCart();
			setTimeout(() => {
				this.isBackParams = false;
			}, 3000);
		}

		// 针对iframe弹框报价
		window.addEventListener("message", function (event) {
			if (event.origin === window.origin) {
				// 验证消息来源
				let data = event.data;
				if (data.type === "closeDialog") {
					_this
						.$confirm(_this.lang.tip, _this.lang.hint, {
							confirmButtonText: _this.lang.Confirm,
							cancelButtonText: _this.lang.Cancel,
							type: "warning",
						})
						.then(() => {
							let targetWindow = window.opener || window.parent;
							targetWindow.postMessage(
								{
									type: "closeDialog",
								},
								window.origin
							);
						})
						.catch(() => {
						});
				}
			}
		});
	},
	beforeDestroy() {
		this.$Bus.$off("uploadPicClipart");
	},
};
</script>
<style>
.el-select .el-input__inner {
	height: 40px;
	background-color: #f5f5f5;
	font-size: 1em;
	font-family: Calibri;
	border: none;
}

.el-select-dropdown__item.selected {
	color: var(--color-primary);
}
</style>
<style scoped lang="scss">
@import url("https://fonts.googleapis.com/css2?family=Oswald:wght@200&display=swap");

.lightUpLanyards {
	::v-deep .colorPicker .el-tabs__nav-wrap {
		display: none;
	}

	::v-deep .colorPicker .el-tabs__header {
		margin: 0 !important;
	}
}

#wristband {
	--bgColor: #f1f5f8;
	font-family: Calibri, Arial, serif;
	padding-top: 20px;
	background-color: var(--bgColor);

	::v-deep .price-box {
		display: none;
	}

	::v-deep .el-drawer__header {
		margin-bottom: 0;
		padding: 5px;
	}

	/* 修改选中项的字体颜色 */

	::v-deep .el-drawer {
		background-color: #f3f4f5;
	}

	//   ::v-deep .el-input__inner:focus {
	//     border-color: $color-primary;
	//   }
	::v-deep img {
		object-fit: contain !important;
	}

	::v-deep video {
		object-fit: contain;
	}

	::v-deep ul {
		margin-left: 0;
	}

	::v-deep .video-js,
	::v-deep .vjs-poster {
		background-color: white !important;
	}

	@media screen and (max-width: 767px) {
		font-size: 12px;
		font-family: Arial;
		.h1Wrap {
			display: none;
		}
	}

	.canvasFactory {
		display: none;
	}

	::v-deep .StepBox {
		&.active {
			border-color: $color-primary !important;
		}

		@media (any-hover: hover) {
			&:hover {
				.zoomIcon {
					color: $color-primary;
				}

				.product-info {
					.radio-beauty {
						background-color: $color-primary;
						border-color: $color-primary;

						&::after {
							background-color: white;
						}
					}

					.title {
						color: $color-primary;
					}
				}
			}
		}
	}

	.picWrap {
		display: flex;
		justify-content: center;
		align-items: center;
		flex-direction: column;
		width: 100%;
		padding: 0px !important;
	}

	.text-center {
		text-align: center;
	}

	.myBtn {
		background: $color-primary;
		border-radius: 10px;
		font-size: 18px;
		font-weight: 400;
		color: #ffffff;
		padding: 10px 65px;

		@media screen and (max-width: 767px) {
			width: 125px;
			height: 30px;
			background: $color-primary;
			border-radius: 5px;
			font-size: 12px;
			font-family: Arial;
			font-weight: 400;
			color: #ffffff;
			margin-top: 9.5px;
			padding: 0;
		}
	}

	.header {
		background-color: #fff;
		display: grid;
		grid-template-columns: repeat(48, 1fr);
		padding-bottom: 20px;
		grid-column: 2/49;

		h1 {
			position: relative;
			font-size: 48px;
			// font-family: Times CG ATT, calibri, serif;
			font-weight: bold;
			color: #333333;
			text-align: center;
			z-index: 0;
			grid-column: 1/49;
			text-align: center;
			padding-left: 10px;
			font-weight: normal;
		}

		@media screen and (max-width: 767px) {
			grid-template-columns: 1fr;

			h1 {
				padding-left: 20px;
				margin-bottom: 0;
				font-size: 21px;
				// grid-column: 2/33;
			}
		}
	}

	.priceTable {
		grid-column: 2/49;
		margin-bottom: 20px;
	}

	.content {
		position: relative;
		display: grid;
		grid-template-columns: repeat(48, 1fr);
		background-color: var(--bgColor);

		.leftArea {
			grid-column: 2/30;

			@media screen and (max-width: 1500px) {
				grid-column: 2/48;
			}

			@media screen and (max-width: 768px) {
				grid-column: 1/49;
			}

			.advertisingBanner {
				background: url("http://customed-center.oss-us-west-1.aliyuncs.com/web/public/picStore/20240314/Custom_LanyardsPC_20240314azFA62.png") no-repeat;
				height: 60px;
				background-size: 100%;
				margin: 10px 0 0 30px;
				line-height: 60px;

				div {
					font-family: Calibri;
					font-weight: bold;
					font-size: 24px;
					color: #ffffff;
					text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.63);
					text-align: center;

					span {
						color: #fff000;
					}
				}

				@media screen and (max-width: 767px) {
					background: url("http://customed-center.oss-us-west-1.aliyuncs.com/web/public/picStore/20240326/Custom_LanyardsMb_20240326kstXY3.png") no-repeat;
					height: 18.3333vw;
					background-size: 100%;
					margin: 0;
					line-height: 1;
					padding: 0.5667vw 3.4vw;

					div {
						font-size: 5vw;
						line-height: 6vw;
						padding-top: 9px;
					}
				}
			}

			.step-title {
				font-size: 27px;
				font-weight: bold;
				color: #202428;
				margin-bottom: 15px;

				@media screen and (max-width: 767px) {
					margin-bottom: 15px;
					font-size: 16px;
				}

				// > span {
				// 	color: $color-primary;
				// }

				&.hasTips {
					margin-bottom: 0;
				}
			}

			.tips {
				margin-bottom: 23px;

				@media screen and (max-width: 767px) {
					margin-bottom: 8.5px;
				}
			}

			.kk {
				.stepWrap {
					@media screen and(max-width: 768px) {
						padding: 20px 7px;
						margin-bottom: 5px;
						border-radius: 5px;
					}
				}

				.part {
					padding: 40px 30px 30px 30px;
					background: #ffffff;
					border-radius: 10px;
					position: relative;
					z-index: 0;
					margin-bottom: 20px;

					@media screen and (max-width: 767px) {
						padding: 20px 7px;
						border-bottom: 5px solid #f3f4f5;
						border-radius: 0;
						margin-bottom: 0;
					}

					.boxContent {
						display: grid;
						justify-content: space-between;
						grid-template-columns: repeat(1, 1fr);
						grid-gap: 22px;
						gap: 22px;

						@media screen and (max-width: 768px) {
							display: block;
						}
					}

					&.BandSize {
						@media screen and (max-width: 767px) {
							padding-bottom: 15px;
						}

						.boxContent {
							grid-template-columns: repeat(6, 1fr);
							column-gap: 30px;
							display: grid;

							::v-deep .StepBox {
								padding: 0;

								&.active {
									.el-image {
										border-color: $color-primary !important;
									}
								}

								.item {

									.swiper {
										img {
											height: 163px;
											object-fit: contain;
										}

										@media screen and (max-width: 767px) {
											&::before,
											&::after {
												content: "";
												position: absolute;
												z-index: -1;
												bottom: 12px;
												left: 5px;
												width: 50%;
												height: 20%;
												box-shadow: 0 14px 7px #d9dbdd;
												transform: rotate(-3deg);
											}

											&::after {
												right: 5px;
												left: auto;
												transform: rotate(3deg);
											}
										}
									}
								}
							}

							@media screen and (max-width: 767px) {
								grid-template-columns: repeat(3, 1fr);
								column-gap: 5px;
								row-gap: 7.5px;

								::v-deep .StepBox {
									.se .swiper img {
										height: 90px;
									}
								}
							}
						}
					}

					&.MessageStyle {
						@media screen and (max-width: 767px) {
							padding-bottom: 15px;
						}

						.boxContent {
							grid-template-columns: repeat(3, 1fr);
							column-gap: 30px;
							display: grid;

							::v-deep .StepBox {
								padding: 0;

								&.active {
									.el-image {
										border-color: $color-primary !important;
									}
								}

								.item {
									padding-bottom: 10px;

									.swiper {
										img {
											object-fit: contain;
										}

										@media screen and (max-width: 767px) {
											&::before,
											&::after {
												content: "";
												position: absolute;
												z-index: -1;
												bottom: 12px;
												left: 5px;
												width: 50%;
												height: 20%;
												box-shadow: 0 14px 7px #d9dbdd;
												transform: rotate(-3deg);
											}

											&::after {
												right: 5px;
												left: auto;
												transform: rotate(3deg);
											}
										}
									}
								}
							}

							@media screen and (max-width: 767px) {
								grid-template-columns: repeat(2, 1fr);
								column-gap: 5px;
								row-gap: 7.5px;

								::v-deep .StepBox {
									.se .swiper img {
										height: 90px;
									}
								}
							}
						}
					}

					&.MoreOptions {
						@media screen and (max-width: 767px) {
							padding-bottom: 15px;
						}

						.boxContent {
							grid-template-columns: repeat(3, 1fr);
							column-gap: 30px;
							display: grid;

							::v-deep .StepBox {
								padding: 0;

								&.active {
									.el-image {
										border-color: $color-primary !important;
									}
								}

								.item {
									padding-bottom: 10px;

									.swiper {
										img {
											object-fit: contain;
										}

										@media screen and (max-width: 767px) {
											&::before,
											&::after {
												content: "";
												position: absolute;
												z-index: -1;
												bottom: 12px;
												left: 5px;
												width: 50%;
												height: 20%;
												box-shadow: 0 14px 7px #d9dbdd;
												transform: rotate(-3deg);
											}

											&::after {
												right: 5px;
												left: auto;
												transform: rotate(3deg);
											}
										}
									}
								}
							}

							@media screen and (max-width: 767px) {
								grid-template-columns: repeat(2, 1fr);
								column-gap: 5px;
								row-gap: 7.5px;

								::v-deep .StepBox {
									.se .swiper img {
										height: 90px;
									}
								}
							}
						}
					}

					&.PackagingOptions {
						@media screen and (max-width: 767px) {
							padding-bottom: 15px;
						}

						.boxContent {
							grid-template-columns: repeat(3, 1fr);
							column-gap: 30px;
							display: grid;

							::v-deep .StepBox {
								padding: 0;

								&.active {
									.el-image {
										border-color: $color-primary !important;
									}
								}

								.item {
									padding-bottom: 10px;

									.swiper {
										img {
											object-fit: contain;
										}

										@media screen and (max-width: 767px) {
											&::before,
											&::after {
												content: "";
												position: absolute;
												z-index: -1;
												bottom: 12px;
												left: 5px;
												width: 50%;
												height: 20%;
												box-shadow: 0 14px 7px #d9dbdd;
												transform: rotate(-3deg);
											}

											&::after {
												right: 5px;
												left: auto;
												transform: rotate(3deg);
											}
										}
									}
								}
							}

							@media screen and (max-width: 767px) {
								grid-template-columns: repeat(2, 1fr);
								column-gap: 5px;
								row-gap: 7.5px;

								::v-deep .StepBox {
									.se .swiper img {
										height: 90px;
									}
								}
							}
						}
					}

					&.Delivery-Date {
						border-bottom: 0;

						::v-deep .dateBox .item-wrap {
							@media screen and (max-width: 767px) {
								padding: 15px !important;

								.bottom {
									// margin: 8px 0 0 27px;
								}

								.top span {
									font-size: 14px;
								}
							}
						}

						@media screen and (max-width: 767px) {
							::v-deep
							.dateBox
							.el-popover__reference-wrapper
							.el-popover__reference
							.active {
								background: $color-primary;
								border: 1px solid $color-primary !important;
								color: #ffffff;

								.label {
									color: #ffffff !important;
								}

								.info {
									color: #ffffff !important;
								}

								.circle2 {
									border-color: #fff;
									background-color: $color-primary;
								}
							}
						}

						::v-deep .dateBox .item-wrap.discount-item0,
						::v-deep .dateBox .item-wrap.discount-item1,
						::v-deep .dateBox .item-wrap.discount-item2,
						::v-deep .dateBox .item-wrap.discount-item3 {
							border: 1px solid #e9ecf0;

							&:hover {
								border-color: $color-primary;
							}
						}

						.step-text {
							font-size: 16px;
							font-weight: 400;
							color: #666666;
							text-align: left;
							margin-bottom: 20px;

							@media screen and (max-width: 767px) {
								font-size: 12px;
								color: #999999;
							}
						}
					}

					&.step-active {
						z-index: 100;
					}
				}


			}

			.kk.type1 {
				.part {
					position: static;
				}
			}
		}

		.rightArea {
			position: relative;
			grid-column: 30/48;
			margin-left: 30px;

			@media screen and (max-width: 768px) {
				display: none;
				&.active {
					display: block;
					position: fixed;
					margin-left: 0;
					left: 0;
					top: 0;
					z-index: 999;
				}
			}

			.stickyType {
				position: sticky;
				background-color: #ffffff;
				border-radius: 10px;
				bottom: auto;
				top: 10px;
				font-size: 14px;

				.canvas {
					padding: 2em;
					height: 100%;
					display: flex;
					flex-direction: column;

					.direction {
						text-align: center;
						color: var(--btn-primary);
						font-weight: bold;
						font-size: calc(2em - 2px);
						cursor: pointer;
						display: grid;
						width: 5em;
					}
				}

				@media screen and(max-width: 768px) {
					border-radius: 0;
					.canvas {
						.direction {
							font-size: 1em;
						}
					}
				}

				&.step-active {
					z-index: 100;
				}
			}
		}
	}

	.mFooter {
		display: grid;
		grid-template-columns: 850px;
		justify-content: center;
		padding: 20px;
		background: #eef2f5;

		@media screen and (max-width: 767px) {
			grid-template-columns: 1fr;
			padding: 0;
		}

		.medalsDetails {
			::v-deep .btnGroup {
				display: flex;
				flex-wrap: nowrap;
				flex-direction: initial;

				.btn:first-child {
					margin-bottom: 0;
				}
			}

			::v-deep .canvas {
				padding: 2em;
				height: 100%;
				display: flex;
				flex-direction: column;

				.direction {
					text-align: center;
					color: var(--btn-primary);
					font-weight: bold;
					font-size: calc(1.5em - 2px);
					cursor: pointer;
					display: grid;
					width: 5em;
				}
			}

			@media screen and(max-width: 768px) {
				border-radius: 0;
				.canvas {
					.direction {
						font-size: 1em;
					}
				}
			}
		}
	}

	.shadowMask {
		position: relative;

		&::after {
			content: "";
			position: absolute;
			inset: 0;
			background-color: rgba(0, 0, 0, 0.3);
			border-radius: 6px;
		}
	}

	::v-deep .commentDialog {
		width: 100%;
		border: 1px solid #ccc;
		// padding: 10px;
		border-radius: 4px;
		box-shadow: 0 2px 4px rgb(0 0 0 / 12%), 0 0 6px rgb(0 0 0 / 4%);
		text-align: center;

		.el-dialog__header {
			display: none;
		}

		.el-dialog__body {
			padding: 0;
		}

		.isComment {
			// width: 100%;
			height: 100%;
			// border: 1px solid #ccc;
			padding: 10px;
			border-radius: 4px;
			// box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
			text-align: center;

			.textWrap {
				display: flex;
				align-items: center;
				justify-content: flex-start;
				margin-top: 0 !important;
				font-size: 18px;
			}

			.circle2 {
				border-color: $color-primary;
				background-color: $color-primary;
				flex-shrink: 0;
				width: 18px;
				height: 18px;
				border-radius: 50%;
				margin-right: 10px;
				display: flex;
				justify-content: center;
				align-items: center;

				&::after {
					background-color: #ffffff;
					content: "";
					width: 6px;
					height: 6px;
					border-radius: 50%;
				}
			}

			.el-textarea {
				flex: 1;
				margin: 10px 0;

				textarea {
					height: 100%;
					background-color: #f5f5f5 !important;
				}
			}

			button {
				background-color: $color-primary;
				color: #fff;
				padding: 4px;
				width: 100px;
				outline: none;
				border: none;
				border-radius: 4px;
				font-size: 14px;
				text-align: center;
			}
		}
	}

	.el-icon-close {
		display: flex;
		justify-content: center;
		align-items: center;
		font-weight: 700;
		position: absolute;
		top: 0;
		right: 0;
		transform: translate(50%, -50%);
		width: 40px;
		height: 40px;
		cursor: pointer;
		background: #ffffff;
		box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.3);
		border-radius: 50%;
		z-index: 10;

		@media screen and (max-width: 800px) {
			transform: translate(0, 0);
			box-shadow: none;
		}
	}

	.preview {
		display: none;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		position: fixed;
		right: 0;
		top: 30%;
		padding: 5px 10px;
		transform: translateY(-50%);
		opacity: 0.9;
		z-index: 1000;
		border-radius: 10px;
		background-color: #fff0e9;
		cursor: pointer;
		-webkit-user-select: none;
		-moz-user-select: none;
		user-select: none;

		@media screen and (max-width: 1500px) {
			display: flex;
		}

		b {
			font-size: 30px;
			color: #c95c28;
		}

		span {
			font-size: 12px;
			color: #c95c28;
			font-weight: 700;
		}
	}
}
</style>
<style lang="scss">
.drawDialog {
	.rightArea.rightFixedArea {
		position: relative;

		.fold-icon {
			position: absolute;
			left: 0;
			top: 50%;
			transform: translateY(-50%);
			height: 100px;
			width: 9px;
			background: #dedbdb;
			display: flex;
			align-items: center;
			justify-content: center;
			z-index: 1;
			cursor: pointer;

			.arrow {
				transform: rotate(180deg);
				font-size: 10px;
				color: #fff;
			}
		}
	}
}

.closeBtn {
	position: relative;

	.btn-close {
		font-size: 12px;
		position: absolute;
		right: -15px;
		top: -15px;
		left: auto;
		border-radius: 50%;
		box-shadow: 0 0.5rem 1rem rgb(0 0 0 / 15%);
		width: 36px;
		height: 36px;
		background: transparent url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23000'%3e%3cpath d='M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z'/%3e%3c/svg%3e") (center / 1em) auto no-repeat;
		background-color: #fff;
		background-size: 0.7em;
		opacity: 1;
		z-index: 2;

		@media screen and (max-width: 768px) {
			right: -5px;
		}
	}
}
</style>
