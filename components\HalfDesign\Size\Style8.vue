<template>
	<div class="mb-4" :class="stepData.styleName">
		<half-design-litter-title :index="stepData.id" :data-name="`${stepData.styleClass + '_' + stepData.id}`"
			style="margin: 4px 0" :stepTitle="stepData.minStepTitle" v-show="stepData.attributeTitle">{{
				stepData.attributeTitle }} <div class="dialogIcon" v-show="stepData.showDialog" @click="showDialogFn">
				<b class="icon-wenhao1"></b>
			</div>
		</half-design-litter-title>
		<slot name="stepText"></slot>
		<div class="step-content">
			<div class="step-wrap">
				<div class="step-item" :class="{ active: index === selectIndex }"
					v-for="(step, index) in stepData.productParamList" :key="index" @click="selectStep(step, index)">
					<div class="sizeContent">
						<circleBox :active="index === selectIndex"></circleBox>
						<div class="prepend">{{ step.valueName }}
							<div class="priceText" v-show="step.addSizePrice">
								({{ step.addSizePrice }})
							</div>
						</div>

					</div>
				</div>
			</div>
		</div>
		<modalHalfDetailComponentsBeltBuckleSizeDialog v-if="stepData.showDialog && showDialog"
			:showDialog.sync="showDialog" :stepData="stepData" :nowSelectIndex.sync="selectIndex" @selectSize="selectStep">
		</modalHalfDetailComponentsBeltBuckleSizeDialog>
		<!-- <div class="errorTip" ref="errorTip">
			<v-alert dense outlined type="error"> {{ langSemiCustom.miniQty }} {{ productInfo.lowestPurchaseQuantity ||
				1 }}
			</v-alert>
		</div> -->
	</div>
</template>
<script>
import { round2 } from '@/utils/utils'
import circleBox from '@/components/HalfDesign/common/circleBox'
export default {
	inject: ['getProductInfo',"getCustomPriceData"],
	components: {
		circleBox
	},
	props: {
		stepData: {
			type: Object,
			default: () => ({})
		}
	},
	data() {
		return {
			nowinputNum: 0,
			selectIndex: -1,
			selectItem: null,
			showDialog: false
		}
	},
	watch: {
		nowinputNum(newVal, oldVal) {
			if (newVal!==oldVal) {
				this.stepData.productParamList.forEach((item, index) => {
					let data=this.getPrice(item, newVal);
					item.addSizePrice=data.show? data.t:null
				})
				this.$forceUpdate()
			}
		}
	},
	computed: {
		langSemiCustom() {
			return this.$store.getters.lang?.semiCustom
		},
		symbolCode() {
			return this.$store.state.currency.symbol
		},
		rate() {
			return this.$store.state.currency.rate
		},
		productInfo() {
			return this.getProductInfo()
		},
		customPriceData() {
		return this.getCustomPriceData();
		},
	},
	methods: {
		selectStep(item, index, state=false) {
			if (item.stock<=0) {
				this.$toast('mo stock')
				return
			}
			this.selectIndex=index
			this.selectItem=item
			this.setSizeData(this.nowinputNum)
			this.$emit('selectStep', {
				type: this.stepData.attributeFlag,
				data: item,
				id: this.stepData.id,
				firstSelect: state,
			})
		},
		formatNum(step) {
			if (!step.inputNum) {
				return undefined
			}
			step.inputNum=step.inputNum.replace(/[^\d]/g, '')
			if (step.stock&&step.stock>0&&step.inputNum>step.stock) {
				step.inputNum=String(step.stock)
			}
			if (step.stock<=0) {
				step.inputNum=''
			}
		},
		getIncreasePrice(num=0, increase) {
			let newNum=parseInt(num)
			if (!increase) {
				return 0
			}
			let increaseArr=JSON.parse(increase),
				len=increaseArr.length,
				findItem=increaseArr[0]
			if (len===1) {
				return increaseArr[0].unitPrice
			}
			for (let i=0; i<len; i++) {
				let item=increaseArr[i],
					nextItem=increaseArr[i+1]
				if (newNum>=item.quantity&&(nextItem? newNum<nextItem.quantity:true)) {
					findItem=item
					break
				}
			}
			return findItem.unitPrice
		},
		getPrice(step, num) {
			let priceType=step.priceType,
				code=this.symbolCode,
				price=0,
				rate=this.rate
			const plusBasicUnitPrice = +(this.customPriceData?.plusBasicUnitPrice || 0);
			const multiBasicUnitPrice = +(this.customPriceData?.multiBasicUnitPrice || 1);
			if (priceType===1) {
				price=round2((+step.unitPrice+plusBasicUnitPrice) * multiBasicUnitPrice * rate);
				if(step.unitPrice==0) price=0
				return {
					t: `+${code}${price}`,
					show: price&&price>0
				}
			} else if (priceType===2) {
				price=round2(step.totalPrice*rate)
				return {
					t: `+${code}${price}`,
					show: price&&price>0
				}
			} else if (priceType===3) {
				return {
					t: `+${round2(+step.unitPercent*multiBasicUnitPrice)}%`,
					show: step.unitPercent&&step.unitPercent>0
				}
			} else if (priceType===4) {
				return {
					t: `+${step.totalPercent}%`,
					show: step.totalPercent&&step.totalPercent>0
				}
			} else if (priceType===5) {
				let getPrice=+this.getIncreasePrice(num, step.increasePrice)
				price=round2((getPrice+plusBasicUnitPrice)*multiBasicUnitPrice*rate)
				if(getPrice==0) price=0
				return {
					t: `+${code}${price}`,
					show: price&&price>0
				}
			} else {
				return {
					show: false
				}
			}
		},
		selectDefault() {
			if (this.stepData.productParamList.length) {
				this.selectStep(this.stepData.productParamList[0], 0, true)
			}
		},
		setSizeData(num) {
			this.stepData.productParamList.forEach((item, index) => {
				item.inputNum=''
				if (this.selectIndex===index) {
					item.inputNum=num
				}

			})
		},
		setInputNum(num, type=false, noSelect=true) {
			let once=true
			this.stepData.productParamList.forEach((item, index) => {
				if (once) {
					if (item.stock&&item.stock>0) {
						item.inputNum=num+''
						once=false
						this.formatNum(item)
					}
				}
			})
			this.nowinputNum=num
			this.$forceUpdate()
			this.selectDefault()
		},
		setQuantityNum(data) {
			if (data) {
				this.nowinputNum=data.inputNum
				this.setSizeData(this.nowinputNum)
				this.$emit('updatePrice')
			}
		},
		showDialogFn() {
			this.showDialog=true
		},
	},
	mounted() {
		this.$Bus.$on('selectDefaultSizeStep', this.setInputNum)
		// 将quantity 设置的数量设置给当前不可以编辑的size
		this.$Bus.$on('quantityUpdateQty', this.setQuantityNum)
	},
	beforeDestroy() {
		this.$Bus.$off('selectDefaultSizeStep', this.setInputNum)
		this.$Bus.$off('quantityUpdateQty', this.setQuantityNum)
	}
}
</script>
<style scoped lang="scss">
@import '~assets/css/half.scss';

input[disabled] {
	background: rgba(0, 0, 0, 0.03);
}

.step-content {
	display: grid;
	grid-template-columns: 1fr;
	grid-gap: 10px;

	.step-wrap {
		display: grid;
		grid-gap: 10px;

		.step-item {
			white-space: nowrap;
			// overflow: hidden;
		}
	}

	@include respond-to(mb) {
		grid-gap: 5px;

		.step-wrap {
			grid-gap: 5px;

			.step-item {}
		}
	}
}

.style1 .step-content {
	.step-wrap {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		gap: 18px;

		.step-item {
			min-width: 0;
			cursor: pointer;
			background: #f5f5f5;
			border: 1px solid transparent;
			border-radius: 6px;
			overflow: hidden;

			&.active {
				border: 1px solid $color-primary;
			}

			.sizeContent {
				display: flex;
				align-items: center;
				justify-content: center;
				padding: 0.7em 0.6em;
				gap: 0.6em;
				overflow: hidden;
				height: 100%;

				.prepend {
					width: fit-content;
					text-align: left;
					overflow-wrap: break-word;
					word-break: break-word;
					white-space: normal;
					display: flex;
					align-items: center;
					gap: 0.6em;

					.priceText {
						flex-shrink: 0;
					}
				}
			}
		}

		.hint-text {
			margin-top: 4px;
			color: $gray-text;
			text-align: right;
			font-size: 14px;
		}
	}

	@include respond-to(mb) {
		.step-wrap {
			gap: 10px;
			.step-item {
				.sizeContent {
					.prepend {
						display: block;
					}
				}
			}
		}
	}
}


.dialogIcon {
	margin-left: 6px;

	b {
		cursor: pointer;
		font-size: 0.9em;
		color: $color-primary;
	}
	@include respond-to(mb) {
		font-size: 1.2em;
	}
}
</style>
