<template>
	<!-- 模板外层的盒子上写theme属性，用来分别写每一套的样式，详见下方style -->
	<div>
		<div class="banner-box" :class="modal.wrapClass">
			<div style="height: 100%;" v-for="(l, li) in modal.list" :style="modal.style" :key="li">
				<div v-if="!isManage" class="right-part" id="right-part" :class="{ unfixed: cancelFixed, cur: showMask }"
					 :style="{ top: upTop }" ref="rightPart">
					<StepBar class="stepBar" :checked="statusNum" @statusChecked="statusChecked($event)"></StepBar>
					<!--					选择尺寸数量-->
					<div v-show="statusNum === 1" class="paramList">
						<div class="parameters">
							<div class="materialsList">
								<span class="listTitle"
									  v-if="materialsData.data === undefined || materialsData.data.length != 1">{{
										lang.selectSize.materials
									}}</span>
								<div class="radioGroup"
									 v-if="materialsData.data === undefined || materialsData.data.length != 1">
									<div v-for="(item, index) in materialsData.data" :key="index">
										<input type="radio" :name="item.cateName" :value="item.id" :id="item.id"
											   v-model="cateId" @change="changeMmaterials(index)"/>
										<img v-if="item.materialIcon" :src="item.materialIcon" alt=""
											 style="width: 32px;height: 32px;vertical-align: middle">
										<label :for="item.id">
											<!--			for 属性规定 label 绑定的表单元素。-->
											{{ item.cateNameQuote ? item.cateNameQuote : item.cateName }}
										</label>
									</div>
								</div>
							</div>
							<div class="sizeSelectList">
								<span class="listTitle">{{ lang.selectSize.sizeItem }}</span>
								<div class="radioGroup sizeRadio">
									<div v-for="(item, index) in sizeList" :key="index">
										<input type="radio" name="sizeParams" :value="item.id" :id="item.id"
											   v-model="sizeId" @change="changeSize(item)"/>
										<label :for="item.id">
											<!--			for 属性规定 label 绑定的表单元素。-->
											{{ item.width + "” x " + item.height + "”" }}
										</label>
									</div>
									<div class="custom-size" v-if="htmlShow()">
										<div>
											<input type="radio" name="sizeParams" :label="lang.customSize" value="custom"
												   v-model="sizeId" @change="changeSize"/>
											<label>{{ lang.selectSize.customSize }}</label>
										</div>
										<div flex>
											<size-input v-model="sizeW" ref="sizeWInput" :disabled="!customSize"
														:min="priceConfig.min_width" :max="priceConfig.max_width" :required="true"
														:placeholder="lang.selectSize.width + '”'"
														@validateResult="quotationCalculator"></size-input>
											<!--										-->
											<div class="mx-3" style="flex: 1">X</div>
											<size-input v-model="sizeH" ref="sizeHInput" :disabled="!customSize"
														:min="priceConfig.min_height" :max="priceConfig.max_height" :required="true"
														:placeholder="lang.selectSize.height + '”'"
														@validateResult="quotationCalculator"></size-input>
											<!--										-->
										</div>
									</div>
								</div>
							</div>
							<div class="quantityList">
								<span class="listTitle">{{ lang.selectSize.quantityItem }}</span>
								<div class="radioGroup quantityRadio">
									<div flex class="quantitySelect" v-for="(item, index) in quotation" :key="index">
										<label>
											<input type="radio" name="quantity" :value="item.quantity" :id="item.id"
												   v-model="selectQty" @change="changeSelectQty(selectQty)"/>
											<span class="qty">{{ item.quantity }}</span>
										</label>
										<CCYRate :price="item.totalPrice"></CCYRate>
										<label>
											<CCYRate :price="item.unitPrice"></CCYRate>
											{{ lang.selectSize.each }}
										</label>
									</div>
									<div flex class="quantitySelect">
										<label flex>
											<input type="radio" name="quantity" value="customQty" v-model="selectQty"
												   @change="changeSelectQty(selectQty)"/>
											<Qty-input class="flex-1" v-model="qty" ref="qtyInput"
													   :min="priceConfig.min_quantity" :max="priceConfig.max_quantity"
													   :required="true" @validateResult="debounceQuotationCalculator"></Qty-input>
											<!---->
										</label>
										<CCYRate :price="customQtyPrice"></CCYRate>
										<label>
											<CCYRate :price="customQtyUnitPrice"></CCYRate>
											{{ lang.selectSize.each }}
										</label>
									</div>
								</div>
							</div>
						</div>
						<div flex class="free">
							<div flex v-for="i in lang.selectSize.freeList" :key="i.icon">
								<b class="icon-jxsht-zdgl-dg"></b><strong>{{ lang.selectSize.free }}</strong>{{ i.text }}
							</div>
						</div>
						<button class="to-checkout" @click="verifyParams">
							{{ lang.continue }}<b class="icon-jxsht-jcsz-jr"></b>
						</button>
					</div>

					<!--					选择产品参数-->
					<div v-show="statusNum === 3" class="pro-paramList info">
						<div class="title2">PLEASE SELECT THE OPTIONS BELOW</div>
						<div :model="selectedData" ref="step3Form" label-width="100px" label-position="top"
							 class="demo-ruleForm samePart">
							<div class="formItem" :id="item.paramName" :label="item.paramName"
								 :class="{ cur: item.paramName == currencyStep }" v-for="item in otherSelectList"
								 :key="item.id" :prop="item.paramName" :rules="[
									{
										required: true,
										message: item.paramName + ' is required',
										trigger: 'change',
									},
								]">
								<span class="title"> {{ item.paramName }} </span>
								<div class="grid_box">
									<myCheckBox v-for="itemChild in item.childList" :key="itemChild.id" :parentValue="item"
												:bindValue="itemChild" :bindName="item.paramName"
												@clickFun="selectOptionsFun($event, item)" :imgUrl="itemChild.imageJson | filterImg"
												:imgName="itemChild.imageJson | filterName">
										<template slot="info">{{ itemChild.alias }}</template>
									</myCheckBox>
								</div>
								<b class="close-icon  shutBtn icon-a-uspp-tc-gbzhuanhuan" @click="shutMask($event)"
								   ref="mask"></b>
							</div>
							<div class="detail">
								<div>Transport</div>
								<div>
									{{
										selectedData["Proof Requirement"] &&
										selectedData["Proof Requirement"].length > 0
											? selectedData["Proof Requirement"][0].paramName
											: ""
									}}
								</div>
								<div>Size</div>
								<div>{{sizeName}}</div>
								<div>Quantity</div>
								<div>{{qty ? qty :selectQty}}</div>
								<div>Subtotal</div>
								<div>
									<CCYRate class="ps" :price="priceInfo.totalPrice"></CCYRate>
									{{ $store.state.currency.code }}
								</div>
							</div>
							<div class="continue">
								<button class="to-checkout" @click="toCart" :disabled="loadContinue">
									{{ lang.continue }}<b class="icon-jxsht-jcsz-jr"></b>
								</button>
							</div>
							<div class="back">
								<button class="to-checkout" @click="verifyParams">
									{{ lang.goBack }}
								</button>
							</div>
						</div>
					</div>
				</div>
				<div class="imgWrap category" ref="category" @click="setModalType(l.banner, modal.list, 'banner')"
					 v-if="l.banner && l.banner.value">
					<pic :src="l.banner.value" :alt="l.banner.alt" :title="l.banner.alt"
						 :style="{ ...modal.homeImgStyle, ...l.banner.style }"/>
				</div>
				<div class="imgWrap" @click="setModalType(l.video, modal.list, 'video')" :title="l.video.alt"
					 v-else-if="l.video && l.video.value">
					<video :src="l.video.value" autoplay loop muted playsinline :style="{ ...l.video.style }"></video>
				</div>
				<div class="bps-container containerImg">
					<div v-for="(item, index) in l.itemIcon">
						<pic :src="item.href.value" :alt="item.title.value" :title="item.title.value"
							 @click="setModalType(l.itemIcon[index].href, modal.list, 'img')"/>
						<div style="display: inline-block">
							<EditDiv tagName="p" v-model:content="item.title.value"
									 @click="setModalType(l.itemIcon[index].title, modal.list, 'text')" />
							<EditDiv tagName="span" v-model:content="item.subTitle.value"
									 @click="setModalType(l.itemIcon[index].subTitle, modal.list, 'text')" />
						</div>
					</div>
				</div>
				<div class="bps-container">
					<EditDiv v-if="l.title" tagName="h1" v-model:content="l.title.value"
							 @click="setModalType(l.title, modal.list, 'text')" />
					<EditDiv v-if="l.subTitle" class="des" :class="modal.colorSticker" v-model:content="l.subTitle.value"
							 @click="setModalType(l.subTitle, modal.list, 'text')" />
					<EditDiv v-if="l.text" class="text" v-model:content="l.text.value"
							 @click="setModalType(l.text, modal.list, 'text')" />
					<div v-if="l.icon" style="width: 500px">
						<a :href="l.url.value" v-if="l.url" target="_blank">
							<div class="sicker_xingxing" style="display: inline-block;vertical-align: middle"
								 v-for="(aitem, aindex) in l.icon" :key="aindex">
								<pic :src="aitem.img.value" @click="setModalType(aitem.img, modal.list, 'img')"/>
							</div>
							<div class="sicker_Reviews" style="display: inline-block;vertical-align: middle;color: #333">
								<EditDiv v-if="l.spanTitle" :class="modal.colorSticker" tagName="p"
										 v-model:content="l.spanTitle.value"
										 @click="setModalType(l.spanTitle, modal.list, 'text')" />
							</div>
							<div class="sicker_xingxing" style="display: inline-block;vertical-align: middle;width: 25%">
								<pic :src="l.img?.value" @click="setModalType(l.img, modal.list, 'img')"/>
							</div>
						</a>
					</div>
					<div class="btnWrap" @click="setModalType(l.button, modal.list, 'button')" v-if="l.button">
						<a :href="l.button.url" :title="l.button.alt" :target="l.button.target || '_self'"
						   class="default-button bps-button" :style="{ ...l.button.style }">
							{{ l.button.value }}
							<b class="icon-bps-sanjiao"></b>
						</a>
					</div>
				</div>
				<!--				报价Banner-->

			</div>
			<div flex class="upload" ref="upload" v-show="uploadImg">
				<StepBar class="stepBar" :checked="stepsNum"></StepBar>
				<div class="tips">{{ lang.selectImage.upload }}</div>
				<div class="imgUpload">
					<input  type="file" multiple ref="upFile" @change="uploadPic" accept=".jpg,.png,.jpeg,.gif"/>
					<b class="uploadIcon icon-a-ChangeLogo"></b>
					<p v-if="uploadList.length < 1" class="no-file">{{ lang.selectImage.noFiles }}</p>
				</div>
				<div class="imgWrap_grid" v-if="uploadList">
					<div class="imgDiv" v-for="(item,index) in this.uploadList">
						<p class="delBtn" @click="delUploadImg(index)">×</p>
						<pic class="upload-img" :src="item.secure_url" alt="upload"/>
					</div>
				</div>
				<div class="skip-tips" @click="toSelectParams" v-if="uploadList.length < 1">
					{{ lang.selectImage.or }}<span class="upload-later">{{ lang.selectImage.skip }}</span>
				</div>
				<div v-if="uploadList.length > 0" class="comments">
					<div class="commentTip">{{ lang.selectImage.comments }}</div>
					<textarea v-model="remark" :placeholder="lang.selectImage.commentsTip"></textarea>
				</div>
			</div>
			<p flex class="price" v-show="stepsNum === 2" ref="bottomPart" >
				{{ lang.subtotal }}:&nbsp;<CCYRate :price="priceInfo.totalPrice"></CCYRate>
				<button :class="{ 'disabled': isDisabled }" :disabled="isDisabled" primary @click="toSelectParams">{{
						lang.continue
					}}
				</button>
				<button primary style="background: var(--color-primary);border: 1px solid var(--color-primary);margin-left: 0" @click.stop="goBack">
					<svg t="1700531185234" class="icon" viewBox="0 0 1482 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="70891" width="15" height="15"><path d="M584.884 272.715V0L0 477.153l584.884 477.074v-279.41c406.12 0 690.568 108.992 893.588 347.53-81.27-340.757-324.927-681.355-893.588-749.632" fill="#467CFD" p-id="70892"></path></svg>
					Go Back
				</button>
			</p>
			<div
				class="mask"
				:class="{ show: showMask }"
				@click.self="shutMask($event)"
				ref="mask"
			></div>
		</div>
	</div>
</template>

<script>
import {validateSize, validateQty, validateImageAttribute} from "@/utils/validate";
import {debounce} from '@/utils/utils'
import myCheckBox from "@/components/myCheckBox.vue";
import Currency from "../../Currency";
import { uploadFile } from "@/utils/oss";

export default {
	head() {
		return {
			title: this.title,
			meta: [
				{
					hid: "description",
					name: "description",
					content: this.description,
				},
				{
					hid: "keywords",
					name: "keywords",
					content: this.keyword,
				},
			],
			script: [
				{
					type: "application/ld+json",
					innerHTML: this.structuring || "",
				},
			],
		};
	},
	components: {
		Currency,
		myCheckBox,
	},
	props: {
		preview: {
			type: Boolean,
			default: false,
		},
		data: {
			type: Object,
			default: {},
		}
	},
	data() {
		return {
			isManage: process.env.isManage,
			modal: {
				style: {},
				type: {index: 0, clickPosition: 'outer'},
				...this.data
			},
			finaQuantity: null,
			showMask: false,
			calculateData: {
				totalPrice: 0,
			},
			optionsData: [],
			selectedData: {},
			isDisabled:true,
			currencyStep: {},
			finaSize: "4",
			projectName: "bps",
			stickerSize: {},
			cateId: null,
			materialsData: [],
			pid: null,
			//-------------
			cateName: "",
			priceConfig: "",
			quotation: [],
			sizeW: "",
			sizeH: "",
			sizeName:'',
			sizeId: 0,
			qty: "",
			quantityIndex: "",
			statusNum: 1,
			stepsNum: 0,
			uploadImg: false,
			cancelFixed: false,
			uploadHeight: "",
			upTop: "",
			remark: "",
			selectQty: 100,
			selectW: 2,
			selectH: 2,
			selectedSize: {},
			customQtyPrice: 0,
			customQtyUnitPrice: 0,
			showCustomQtyInput: true,
			customSize: false,
			debounceQuotationCalculator: "",

			otherSelectList: [],
			paramData: {
				idList: [],
				nameList: {},
			},

			allowAddToCart: true,
			priceInfo: {
				unitPrice: 0,
				totalPrice: 0,
			},
			activeShape: "",
			uploadImgFile: "",
			picPath: "",
			uploadList:[],
			cartTemp: "",
			materialsBoxWidth: 0,
			materialIndex: 0,
			showMaterials: false,
			showOccasions: false,
			loadContinue: false,
			tips: "",
			parentCate: {},
		};
	},
	filters: {
		filterImg: function (value) {
			if (!value) return "";
			return JSON.parse(value)[0].url;
		},
		filterName: function (value) {
			if (!value) return "";
			return JSON.parse(value)[0].name;
		},
	},
	watch: {
		showMask: {
			handler(val) {
				console.log(val, 'mask')
			}
		},
		modal: {
			handler(val) {
				if (process.env.isManage) this.$emit("update:data", val);
			},
			immediate: true,
			deep: true
		},
		uploadImg(val) {
			if (val) this.$refs.upload.style.height = this.categoryHeight + 30 + "px";
		},
	},

	async mounted() {
		await this.getProductConfig()
		await this.getByPId();
		this.initSize();
		this.sizeList.forEach((item,index) => {
			if (index == this.sizeId) {
				this.sizeName = item.name
			}
		})
		//默认选中第一个数量
		let pathname = window.location.pathname;
		switch (pathname) {
			case "/die-cut-stickers":
				this.pid = 74;
				break;
			case "/kiss-cut-stickers":
				this.pid = 68;
				break;
			case "/circle-stickers":
				this.pid = 78;
				break;
			case "/oval-stickers":
				this.pid = 82;
				break;
			case "/clear-vinyl":
				this.pid = 99;
				break;
			case "/static-cling-stickers":
				this.pid = 102;
				break;
			case "/white-vinyl":
				this.pid = 104;
				break;
		}

		await this.getInfo();

		this.shapeList = JSON.parse(this.priceConfig.shape);
		this.activeShape = this.shapeList.length ? this.shapeList[0] : "";

		this.debounceQuotationCalculator = debounce(this.quotationCalculator, 300);

		if (this.$route.query.designPic) {
			this.uploadList.push({
				secure_url:this.$route.query.designPic
			})
            this.isDisabled = false;
		}

		if (this.$route.query.size) {
			let size = JSON.parse(this.$route.query.size);
			let sizeList = this.sizeList;
			let findSize = sizeList.find(item => {
				return item.width == size.w && item.height == size.h
			})
			if (findSize) {
				this.sizeId = findSize.id;
				this.changeSize(findSize);
			} else {
				this.sizeId = 'custom';
				this.customSize = true;
				this.selectW = "";
				this.selectH = "";
				this.sizeW = size.w;
				this.sizeH = size.h;
			}
		}
	},
	computed: {
		lang() {
			return this.$store.getters.lang?.order || {selectSize: {}, selectImage: {}};
		},
		quoteLang(){
			return this.$store.getters.lang?.quote || {}
		},
		langObj() {
			return this.$store.state.language || {}
		},
		proId() {
			if (this.previewMode) {
				return 148
			} else {
				return this.$store.state.proId
			}
		},
		userUUID() {
			return this.$store.state.userUUID
		},
		isLogin() {
			return this.$store.getters.isLogin
		},
		userId() {
			return this.$store.state.userInfo?.id
		},
		categoryHeight() {
			return this.$refs.category.offsetTop + this.$refs.category.scrollHeight;
		},
		device() {
			return this.$store.state.device
		},
		proType() {
      		return this.$store.state.proType;
    	},
		sizeList() {
			try {
				let list = this.priceConfig.size_restrict;
				list.forEach((item, index) => {
					item.name = `${item.width}"x${item.height}"`;
					item.id = index;
				});
				return list;
			} catch (error) {
			}
		},
	},
	methods: {
		setModalType(target, targetArray, clickType, event, other) {
			this.$setModal(this, target, targetArray, clickType, event, other)
		},
		goBack(){
			this.uploadImg = false;
			this.stepsNum = 1
			this.stickerSize.childList = []
		},
		getProductConfig() {
			return new Promise((resolve, reject) => {
				this.priceConfig = {
					"max_width": "24",
					"min_width": "1",
					"max_height": "24",
					"min_height": "1",
					"custom_size": 1,
					"max_quantity": "200000",
					"min_quantity": "1",
					"size_restrict": [
						{
							"width": 1,
							"height": 1,
							"name": "1\"x1\"",
							"id": 0
						},
						{
							"width": 2,
							"height": 2,
							"name": "2\"x2\"",
							"id": 1
						},
						{
							"width": 3,
							"height": 3,
							"name": "3\"x3\"",
							"id": 2
						},
						{
							"width": 4,
							"height": 4,
							"name": "4\"x4\"",
							"id": 3
						},
						{
							"width": 5,
							"height": 5,
							"name": "5\"x5\"",
							"id": 4
						}
					],
					"pic_path": "https://sticker-static.oss-accelerate.aliyuncs.com/image/uploads/20210429/71c6fe1a02ab8633cac654a1252f862b.jpg",
					"style_type": 3,
					"shape": "[\"Circle\"]"
				};
				resolve()
			});
		},
		filterDate(arr) {
			let newArr = [];
			if (this.proType !== 0) {
				arr.forEach((item) => {
				if (item.isRetailerPro === 2 || item.isRetailerPro === 3) {
					newArr.push(item);
				}
				});
			} else {
				arr.forEach((item) => {
				if (item.isRetailerPro === 1 || item.isRetailerPro === 3) {
					newArr.push(item);
				}
				});
			}
			return newArr;
		},

		shutMask(e) {
			this.showMask = false;
			this.currencyStep = null;
		},
		findById(quantity) {
			return this.quotation.find((x) => {
				return x.quantity == quantity;
			});
		},
		//询盘数据
		getQuoteParam() {
			return new Promise((resolve, reject) => {
				let copyOptionsData = JSON.parse(JSON.stringify(this.otherSelectList));
				let copySelectedData = JSON.parse(JSON.stringify(this.selectedData));
				let tempData = this.materialsData.data.find((x) => {
					return x.id == this.cateId;
				});
				copyOptionsData.forEach((item) => {
					for (let key in copySelectedData) {
						if (item.paramName == key) {
							item.childList = copySelectedData[key];
						}
					}
				});
				let files = this.picPath;
				// if (this.fileList.length > 0) {
				//   this.fileList.forEach((item) => {
				//     files.push(item.secure_url);
				//   });
				// }
				//
				// copyOptionsData.push(this.stickerSize)
				copyOptionsData.push(this.stickerSize)

				let material = {}, childList = []
				material.parentId = 0;
				material.alias = this.quoteLang.ornament.paramName;
				childList.push({
					parentId: 10000,
					alias: tempData.cateName
				})
				material.childList = childList;
				//material插入到copyOptionsData数组第一个
				copyOptionsData.unshift(material);
				let arr = []
				this.uploadList.forEach((item,index) => {
					arr.push(item.secure_url)
				})
				resolve({
					classificationData: this.parentCate,
					finaData: copyOptionsData,
					fontData: {
						fontImgCustom: arr,
						comments: this.remark,
					},
				});
			});
		},
		//价格数据
		getPriceParam() {
			return new Promise((resolve, reject) => {
				let arr = [];
				for (let x in this.selectedData) {
					if (x == "Proof Requirement") {
					} else {
						arr.push(this.selectedData[x][0].priceInfo.id);
					}
				}
				resolve({
					// qtyDetailDTOS: [], //类似 lanyards color
					accessoriesDetailDTOS: [],
					cateId: this.cateId,
					paramIdList: arr, //常规参数
					projectName: this.$store.state.proName,
					sizeId: null,
					discountId: this.selectedData["Proof Requirement"][0].priceInfo.id,
					upgradesQtyDTO: [], //升级参数
					stickerSizeId: this.stickerSize.priceInfo.id,
					sizeWidth:this.sizeId == "custom" ? Number(this.sizeW):this.selectW,
					sizeHeight:this.sizeId == "custom" ? Number(this.sizeH):this.selectH,
					stickerArea: this.sizeId == "custom" ? this.sizeW * this.sizeH : this.selectW * this.selectH,
					quantity: this.qty ? this.qty : this.selectQty,
				});
			});
		},
		changeMmaterials(index) {
			let sizeData = this.materialsData.data
			let arr = []
			sizeData.forEach((eitem, eindex) => {
				if (index == eindex) {
					if (eitem.stickerSizeJson) {
						JSON.parse(eitem.stickerSizeJson).forEach((citem, cindex) => {
							arr.push({
								"width": Number(citem.stickWidth),
								"height": Number(citem.stickLength),
								"name": citem.stickWidth + '"' + "x" + citem.stickLength + '"',
								"id": Number(cindex)
							})
						})
					} else {
						arr = [
							{
								"width": 1,
								"height": 1,
								"name": "1\"x1\"",
								"id": 0
							},
							{
								"width": "2",
								"height": 2,
								"name": "2\"x2\"",
								"id": 1
							},
							{
								"width": 3,
								"height": 3,
								"name": "3\"x3\"",
								"id": 2
							},
							{
								"width": 4,
								"height": 4,
								"name": "4\"x4\"",
								"id": 3
							},
							{
								"width": 5,
								"height": 5,
								"name": "5\"x5\"",
								"id": 4
							}
						]
					}
				}
			})
			this.priceConfig.size_restrict = arr
			this.selectedData = {};
			this.getCateParamRelationByCateId();
		},
		getCateParamRelationByCateId() {
			return new Promise((resolve, reject) => {
				this.$axios
					.$get(
						process.env.baseUrl + "/quote/cate/getCateParamRelationByCateId",
						{
							params: {
								cateId: this.cateId,
								language: this.langObj.language + '-' + this.langObj.countryCode
							},
						}
					)
					.then(async (res) => {
						this.stickerSize = res.data[0];
						this.otherSelectList = res.data;
						// 区分平台显示交期
						this.otherSelectList.forEach(x=>{
							if(x.paramName === 'Proof Requirement'){
								x.childList = this.filterDate(x.childList)
							}
						})

						this.otherSelectList.splice(0, 1);
						this.otherSelectList.forEach((item) => {
							this.selectedData[item.paramName] = [];
						});
						let res2 = await this.quotationCalculator();
						//默认选中第一个数量
						if (res2.length) {
							this.selectQty = 100;
						}
						resolve();
					});
			});
		},
		getByPId() {
			return new Promise((resolve, reject) => {
				this.$axios
					.$get(process.env.baseUrl + "/quote/cate/getByPId", {
						params: {
							pid: this.modal.pid,
							language: this.langObj.language + '-' + this.langObj.countryCode,
						},
					})
					.then(async (res) => {
						this.materialsData = res;
						let arr = []
						res.data.forEach((item, index) => {
							if (index == 0) {
								if (item.stickerSizeJson) {
									JSON.parse(item.stickerSizeJson).forEach((eitem, eindex) => {
										arr.push({
											"width": Number(eitem.stickWidth),
											"height": Number(eitem.stickLength),
											"name": eitem.stickWidth + '"' + "x" + eitem.stickLength + '"',
											"id": Number(eindex)
										})
									})
								} else {
									arr = [
										{
											"width": 1,
											"height": 1,
											"name": "1\"x1\"",
											"id": 0
										},
										{
											"width": "2",
											"height": 2,
											"name": "2\"x2\"",
											"id": 1
										},
										{
											"width": 3,
											"height": 3,
											"name": "3\"x3\"",
											"id": 2
										},
										{
											"width": 4,
											"height": 4,
											"name": "4\"x4\"",
											"id": 3
										},
										{
											"width": 5,
											"height": 5,
											"name": "5\"x5\"",
											"id": 4
										}
									]
								}
							}
						})
						this.priceConfig.size_restrict = arr
						this.cateId = this.materialsData.data[0].id;
						this.getCateParamRelationByCateId();
						resolve();
					});
			});
		},
		getInfo() {
			return new Promise((resolve, reject) => {
				this.$axios
					.$get(process.env.baseUrl + "/quote/cate/getInfo", {
						params: {
							id: this.modal.pid,
						},
					})
					.then(async (res) => {
						this.parentCate = res.data;
						resolve();
					});
			});
		},
		checkForm() {
			let boo = true;
			for (let key in this.selectedData) {
				if (this.selectedData[key].length == 0) {
					this.currencyStep = key;
					this.showMask = true;
					boo = false;
					break;
				}
			}
			return boo;
		},
		selectOptionsFun(obj, item) {
			this.selectedData[obj.key].splice(0, 1, obj.value);
			if (this.checkForm()) {
				this.showMask = false;
				this.currencyStep = null;
				console.log(1111);
			}
			this.$forceUpdate();
			this.priceCalculate();
		},

		initSize() {
			this.selectW = this.sizeList[0].width;
			this.selectH = this.sizeList[0].height;
		},

		changeSize(item) {
			this.sizeName = item.name
			if (this.sizeId === "custom") {
				this.customSize = true;
				this.selectW = "";
				this.selectH = "";
			} else {
				this.sizeW = "";
				this.sizeH = "";
				this.selectW = item.width;
				this.selectH = item.height;
				this.customSize = false;
			}
			this.debounceQuotationCalculator();
		},

		changeSelectQty(val) {
			console.log(val);
			if (val === "customQty") {
				this.showCustomQtyInput = true;
			} else {
				this.qty = "";
				this.$nextTick(() => {
				});
				this.showCustomQtyInput = false;
				this.quotationCalculator()
			}
		},
		//尺寸算价格
		quotationCalculator() {
			return new Promise((resolve) => {
				let postData = {
					quantity: this.qty ? this.qty : undefined,
					stickerArea:
						this.sizeId == "custom"
							? this.sizeW * this.sizeH
							: this.selectW * this.selectH,
					projectName: this.$store.state.proName,
					cateId: this.cateId,
					stickerSizeId: this.stickerSize.priceInfo.id,
				};

				this.$axios
					.$post(
						process.env.baseUrl + "/quote/quoteCalculate/getStickerPriceTable",
						postData
					)
					.then((res) => {
						let list = res.data;
						if (list.length >= 9) {
							this.customQtyPrice = list[list.length - 1].totalPrice;
							this.customQtyUnitPrice = list[list.length - 1].unitPrice;
							this.quotation = list.slice(0, list.length - 1);
							this.tips = list[list.length - 1].tips;
						} else {
							this.quotation = list.slice(0, list.length);
						}
						let myQty = this.selectQty === 'customQty' ? this.qty : this.selectQty;
						let findPrice = res.data.find(item => {
							return item.quantity == myQty
						})
						if (findPrice) {
							this.priceInfo.totalPrice = findPrice.totalPrice;
						}
						resolve(list);
					});
			});
		},

		toUpload() {
			this.stepsNum = 2;
			this.statusNum = 1;
			this.uploadImg = true;
			// this.upTop = this.partTwoToTop + this.categoryHeight + "px";
			this.priceCalculate();
			this.$nextTick(() => window.scrollTo({top: 0, behavior: "smooth"})); // instant:瞬间置顶; smooth:平滑置顶
		},

		async verifyParams() {
			let width = this.sizeW ? this.sizeW : this.selectW;
			let height = this.sizeH ? this.sizeH : this.selectH;
			let priceConfig = this.priceConfig;
			let quantity = this.qty ? this.qty : this.selectQty;
			let validateSizeW = validateSize(
				width,
				priceConfig.min_width,
				priceConfig.max_width
			);
			let validateSizeH = validateSize(
				height,
				priceConfig.min_height,
				priceConfig.max_height
			);
			if (quantity === "customQty") {
				this.qty === "" ? (quantity = "") : (quantity = this.qty);
			}
			let validateQuantity = validateQty(
				quantity,
				priceConfig.min_quantity,
				priceConfig.max_quantity
			);
			// if(!validateSizeW.isValid || !validateSizeH.isValid || !validateQuantity.isValid) return this.$toast.error('please select')
			if (!validateSizeW.isValid) return this.$toast.error(validateSizeW.message);
			if (!validateSizeH.isValid) return this.$toast.error(validateSizeH.message);
			if (!validateQuantity.isValid) return this.$toast.error(validateQuantity.message);
			if (this.selectQty === "customQty") {
				if (this.tips) {
					this.$toast.error(this.lang.toast.minimumPrice);
				}
			}
			//-----
			this.selectQty == "custom"
				? (this.finaQuantity = this.qty)
				: (this.finaQuantity = this.selectQty);

			// this.priceData = await this.findById(this.finaQuantity);
			let ParamChild = JSON.parse(JSON.stringify(this.stickerSize));
			ParamChild.paramName = this.sizeId == "custom" ? this.sizeW + '”x' + this.sizeH + '”' : this.selectW + '”x' + this.selectH + '”';
			ParamChild.alias = this.sizeId == "custom" ? this.sizeW + '”x' + this.sizeH + '”' : this.selectW + '”x' + this.selectH + '”';
			ParamChild.parentId = 1;
			this.stickerSize.childList.push(ParamChild)
			console.log(this.stickerSize)
			// this.stickerSize.childList[0].paramName = 	this.sizeId == "custom" ? this.sizeW + 'x' + this.sizeH  : this.selectW + 'x' + this.selectH;
			// 	this.stickerSize.childList[0].alias = this.sizeId == "custom" ? this.sizeW + 'x' + this.sizeH  : this.selectW + 'x' + this.selectH
			this.toUpload();
		},

		changeSelect(e) {
			this.paramData = e;
			this.priceCalculate();
		},
		//算总价
		async priceCalculate() {
			for (let i in this.selectedData) {
				if (this.selectedData[i].length > 0) {
					continue;
				} else {
					return;
				}
			}
			let postData = await this.getPriceParam();
			this.$axios
				.post(
					process.env.baseUrl + "/quote/quoteCalculate/calculate",
					postData
				)
				.then((res) => {
					this.priceInfo.totalPrice = res.data.data.totalPrice;
				});
		},
		htmlShow(){
			if (this.cateId) {
				if (this.cateId == 192) {
					return false
				}else if (this.cateId == 194) {
					return false
				}else if (this.cateId == 196) {
					return false
				}else if (this.cateId == 198) {
					return false
				} else {
					return true
				}
			}
		},
		toSelectParams() {
			this.statusNum = 3;
			this.stepsNum = -1;
			this.uploadImg = false;
			this.upTop = "";
		},
		async toCart() {
			this.loadContinue = true;
			if (!this.checkedAllParams()) {
				this.loadContinue = false;
				return false;
			}
			if (this.$store.state.userName) {
				await this.next(2);
				let cartTemp = this.$store.state.cartTemp;
				let formData = new FormData();
				formData.append("product_category_id", this.product_category_id);
				formData.append("quantity", cartTemp.quantity);
				formData.append("width", cartTemp.width);
				formData.append("height", cartTemp.height);
				formData.append("price", cartTemp.totalPrice);
				formData.append("select_params", cartTemp.select_params);
				formData.append("param_id_list", cartTemp.param_id_list);
				formData.append("pic_path", cartTemp.pic_path);
				formData.append("currency", this.$store.state.currency.code);
				formData.append("remark", this.remark);
				formData.append("quote_param", cartTemp.quote_param);
				formData.append("quote_price_param", cartTemp.quote_price_param);
				formData.append("old_quote_param", cartTemp.old_quote_param);
				formData.append("old_quote_price_param", cartTemp.old_quote_price_param);
				formData.append("buy_type", cartTemp.buy_type);
				formData.append("product_cate_name", cartTemp.product_cate_name);

				this.$axios
					.post("/api/Cart/addCart", formData)
					.then((res) => {
					})
					.then(() => {
						this.$axios.$post("/api/Cart/getUserCart").then((res) => {
							let errCart = [],
								successCart = [];
							//校验购物车数据合法性
							res.forEach((item, index) => {
								let result = this.isJSON(item.select_params);
								if (result.msg === 1) {
									errCart.push(item.id);
								} else {
									if (typeof item.accessory == "string" && item.accessory) {
										item.accessory = JSON.parse(item.accessory);
									}
									successCart.push(item);
								}
							});
							if (errCart.length) {
								try {
									this.$store.dispatch("getUserInfo");
									this.$store.commit(
										"SET_CART_ONLINE",
										JSON.parse(JSON.stringify(successCart))
									);
								} catch {
									this.$store.commit(
										"SET_CART_ONLINE",
										JSON.parse(JSON.stringify(successCart))
									);
								}
							} else {
								this.$store.commit(
									"SET_CART_ONLINE",
									JSON.parse(JSON.stringify(successCart))
								);
							}
							this.$router.push({path: "/cart"});
						});
					});
			} else {
				await this.next(1);
				this.$router.push({
					path: "/cart",
				});
			}
		},
		isJSON(jsonContent) {
			if (typeof jsonContent == "string") {
				try {
					let obj = JSON.parse(jsonContent);
					if (jsonContent.indexOf("{") > -1) {
						return {
							msg: 0,
							error_code: "JSON SUCCESS",
						};
					} else {
						return {
							msg: 1,
							error_code: "JSON ERROR",
						};
					}
				} catch (e) {
					return {
						msg: 1,
						error_code: "JSON ERROR",
					};
				}
			}
			return {
				msg: 1,
				error_code: "JSON ERROR",
			};
		},
		getCartTempData(type, res) {
			return new Promise(async (resolve, reject) => {
				let priceInfo = this.priceInfo;
				let data = Object.assign(
					{},
					{
						// 购物车传参
						proId: this.proId,
						uuid: this.isLogin ? null : this.userUUID,
						userId: this.isLogin ? this.userId : null,
						buyType: 7,
						quoteCateId: this.modal.pid,
						isMobile: this.device === "mb" ? 1 : 0,
						quoteCateChildId: this.cateId,
						quantity: this.qty ? this.qty : this.selectQty,
						quotePriceParam: JSON.stringify(await this.getPriceParam()),
						quoteParam: JSON.stringify(await this.getQuoteParam()),
						language: this.$store.state.language.language + '-' + this.$store.state.language.countryCode,
					}
				);
				if (type === 1) {
					data = Object.assign({}, data, {
						// remark: this.remark,
						// id: this.userUUID,
					});
				}
				resolve(data);
			});
		},
		next(type) {
			return new Promise((resolve, reject) => {

				Promise.all([this.getQuoteParam(), this.getPriceParam()]).then(async (res) => {
					let partData = await this.getCartTempData(type, res);
					if (type === 1) {
						// 未登录
						this.$axios
							.$post(
								process.env.baseUrl + "/app/cart/addCart",
								partData
							)
							.then((res2) => {
								let data = Object.assign({}, partData, res2, {
									id: this.userUUID,
									old_quote_param: JSON.stringify(res[0]),
									old_quote_price_param: JSON.stringify(res[1]),
									// strict: {
									//   min_quantity: 1,
									//   max_quantity: 999999,
									// },
								});
								this.$store.commit("ADD_CART", data);
								resolve();
								// this.$router.push({
								//   path: "/cart",
								// });
							});
						// this.$store.commit("ADD_CART", await this.getCartTempData(type,res));
					} else if (type === 2) {
						// 登录
						this.$store.commit("ADD_CART_TEMP", partData);
						this.cartTemp = this.$store.state.cartTemp;
						resolve();
					}
				});
			});
		},

		//综合校验
		checkedAllParams() {
			// let paramLength = [];
			// this.otherSelectList.map((i) => {
			//   paramLength.push(i.param_name);
			//   if (i.data && i.data[0].data)
			//     paramLength.push(i.data[0].data[0].param_name);
			// });

			// let errParam = paramLength.find(
			//   (i) => Object.keys(this.paramData.nameList).indexOf(i) == -1
			// );

			if (!this.checkForm()) {
				// this.$toast.error(this.lang.toast.required);
				return false;
			} else return true;
		},

		uploadOnChange(e) {
			let file = this.$refs.upFile.files[0];
			if (!file) {
				return false;
			}
			let validateResult = validateImageAttribute(file);
			if (!validateResult.isValid) {
				this.$toast.error(validateResult.message);
				this.$refs.upFile.value = "";
				return false;
			}
			this.uploadImgFile = file;

            uploadFile(this.uploadImgFile).then((url) => {
				this.picPath = url;
			});
			let reader = new FileReader();
			reader.readAsDataURL(this.$refs.upFile.files[0]);
			reader.onload = (e) => {
				this.picPath = e.target.result;
			};
		},
		uploadPic(event) {
			console.log(event.target.files.length)
			if ((event.target.files.length + this.uploadList.length) > 10) {
				this.$toast.error("Tips: Your files have exceeded the specified limit; you can upload a maximum of 10 files.");
			}else {
				let files = event.target.files;
				let uploadPromises = [];
				Array.from(files).forEach((file) => {
					let size = file.size;
					if (size / 1020 / 1024 > 30) {
						this.$message.error("File size cannot exceed 30m.");
						this.$refs.upload.value = "";
						loading.close();
						return;
					}
					let promise = uploadFile(file).then((res) => {
						this.uploadList.push({
							original_filename: file.name,
							secure_url: res,
						});
					});
					uploadPromises.push(promise);
				});
				Promise.all(uploadPromises).then(() => {
					this.$refs.upload.value = "";
				});
				console.log(files)
				if (!this.uploadList.lastItem) {
					this.isDisabled = false
				}
			}
		},
		delUploadImg(index) {
			this.$refs.upFile.value = "";
			this.uploadImgFile = "";
			this.uploadList.splice(index,1)
			if (this.uploadList <= 0) {
				this.isDisabled = true
			}
		},

		statusChecked(val) {
			this.statusNum = val;
			this.showChange(val);
		},

		showChange(e) {
			if (e === 1) {
				this.statusNum = 1;
				this.stepsNum = 0;
				this.uploadImg = false;
				this.cancelFixed = false;
			}
			if (e === 2) {
				this.toUpload();
			}

			if (e === 3) {
				this.toSelectParams();
			}
		},

	},
};
</script>

<style lang="scss" scoped>
.mb_container {
	display: none;
}

.colorSticker {
	color: #fff !important;
}

.mask {
	display: none;
	background: black;
	position: fixed;
	opacity: .5;
	z-index: 1;
	bottom: 0;
	right: 0;
	left: 0;
	top: 0;

	&.show {
		display: block;
	}
}

.right-part {
	width: 36.2vw;
	right: 9vw;
	position: absolute;
	top: 1vmax;

	.cur {
		position: absolute;
	}

	.paramList {
		margin-top: 5%;
		border-radius: 5px;
		background: #ffffff;
		border: 1px solid #dcdfe6;

		.parameters {
			padding: 0 1.8%;
			display: grid;
			grid-template-columns: repeat(10, 1fr);
			gap: 10px;

			.listTitle {
				font-size: 16px;
				line-height: 3em;
				font-weight: bold;
				margin-left: 10px;
			}

			.radioGroup {
				padding: 0.5rem;
				line-height: 2.5em;
				background: #fbfbfb;
				border-radius: 10px;

				div {
					cursor: pointer;

					input {
						accent-color: $color-primary;
					}
				}
			}

			.materialsList {
				grid-column: 1/11;

				.radioGroup {
					display: grid;
					grid-template-columns: repeat(3, 1fr);

					@media screen and (max-device-width: $pad-width) {
						grid-template-columns: repeat(2, 1fr);
					}

					div {
						input {
							accent-color: $color-primary;
						}
					}
				}
			}

			.sizeSelectList {
				grid-column: 1/4;

				label {
					padding-left: 8px;
				}
			}

			.quantityList {
				grid-column: 4/11;

				.quantitySelect {
					font-size: 0.7vmax;

					@media screen and (max-device-width: $pad-width) {
						font-size: 1vmax;
					}

					label {
						flex: 4;
						align-items: center !important;

						&:first-child {
							input:first-child {
								margin-right: 5px;
							}
						}
					}
				}
			}
		}
	}

	.free {
		flex-wrap: wrap;
		margin: 0 2vmax;
		justify-content: space-between;

		> div {
			color: #333;
			width: 45%;
			margin-top: 20px;

			span {
				border-radius: 50%;
				border: 1px solid #68BD2C;
				color: #68BD2C;
				display: inline-block;
				text-align: center;
				width: 19px;
				height: 19px;
				line-height: 19px;
				margin-right: 10px;
			}
		}

		b {
			color: #68BD2C;
			display: inline-block;
			margin-right: 5px;
		}

		strong {
			margin-right: 4px;
		}
	}

	.pro-paramList {
		margin-top: 5%;
		border-radius: 5px;
		background: white;
		border: 1px solid #dcdfe6;

		.fillTips {
			padding: 3% 0;
			font-weight: bold;
			text-align: center;
			font-size: 0.95vmax;
			border-bottom: 1px solid #dcdfe6;
		}

		.selectParams {
			> div {
				padding-right: 3%;
			}

			.otherItem {
				display: flex;
				margin-top: 4%;
				font-size: 0.8vmax;

				label {
					width: 30%;
					text-align: right;
					margin-right: 2vmax;
				}

				> span {
					flex: 1;
					padding: 0 1em;
				}
			}
		}

		&.info {
			margin-top: 14px;
			border: 1px solid #dcdfe6;
			border-radius: 5px;
			display: grid;
			grid-template-columns: repeat(5, 1fr);
			padding: 0 0 10px;
			grid-gap: 0;
			gap: 0;

			.title2 {
				font-size: 18px;
				font-family: Roboto;
				font-weight: 700;
				grid-column-start: 1;
				grid-column-end: 6;
				border-bottom: 1px solid #ccc;
				padding: 10px 0;
				text-align: center;
			}

			.samePart {
				grid-column-start: 1;
				grid-column-end: 6;
				position: relative;

				.formItem {
					padding: 10px;
					max-height: 400px;
					overflow: auto;

					.title {
						font-size: 18px;
						font-weight: 700;
						color: #000;
						line-height: normal;
						padding-bottom: 19px;
						float: none;
						display: inline-block;
						text-align: left;
						padding: 0 0 10px;
					}

					.grid_box {
						display: grid;
						grid-template-columns: 1fr 1fr;
						grid-gap: 13px;
						gap: 13px;
					}
				}

				.shutBtn {
					display: none;
					width: 28px;
					height: 28px;
					background: #ffffff;
					box-shadow: 0px 2px 7px 0px rgba(4, 0, 0, 0.16);
					border-radius: 50%;
					font-weight: bold;
					align-items: center;
					justify-content: center;
					position: absolute;
					right: 0px;
					top: 0px;
					cursor: pointer;
				}

				.cur {
					z-index: 2;
					background-color: white;
					position: relative;

					.shutBtn {
						display: flex;
					}
				}
			}

			.detail {
				display: grid;
				grid-template-columns: 1fr 1fr;
				grid-column-start: 1;
				grid-column-end: 6;
				padding: 10px;
				grid-gap: 20px;
				gap: 10px;
				font-size: 16px;
				align-items: center;

				> div:nth-child(2n) {
					font-size: 14px;
				}

				.ps {
					color: red;
					font-size: 18px;
					font-weight: 700;
				}
			}

			.continue {
				margin: 0 20px;
				padding-top: 10px;
				grid-column-start: 1;
				grid-column-end: 6;

				.to-checkout {
					margin: 0;
					width: 100%;
				}
			}

			.back {
				margin: 0 20px;
				padding-top: 10px;
				grid-column-start: 1;
				grid-column-end: 6;

				button {
					width: 100%;
					background-color: #fff;
					border: 1px solid $color-primary;
					font-size: 18px;
					color: $color-primary;
					line-height: 2.5em;
					padding-left: 10%;
					padding-right: 5%;
					border-radius: 25px;
				}

				.to-checkout {
					margin: 0 !important;
				}
			}
		}
	}

	.to-checkout {
		width: 94%;
		margin: 5% 3%;
		color: white;
		font-size: 18px;
		line-height: 2.5em;
		padding-left: 10%;
		padding-right: 5%;
		border-radius: 25px;
		background: $color-primary;

		b {
			float: right;
			font-size: 26px;
		}
	}
}

.upload {
	width: 100vw;
	padding-top: 5%;
	background: white;
	align-items: center;
	flex-direction: column;
	position: absolute;
	z-index: 9;
	top: 0;
	height: 100%;

	.tips,
	.commentTip {
		margin: 30px 0 15px;
		font-size: 1vmax;
	}

	.imgUpload {
		position: relative;
		background: #fbfbfb;
		width: 11.6vw;
		height: 11.6vw;

		input[type="file"] {
			display: block;
			height: 100%;
			width: 100%;
			opacity: 0;
			position: absolute;
			top: 0;
			left: 0;
			z-index: 10;
			cursor: pointer;
		}

		b {
			position: absolute;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
			font-size: 6rem;
		}

		.delBtn {
			position: absolute;
			top: 0;
			left: 85%;
			width: 2vw;
			height: 2vw;
			z-index: 11;
			color: $color-primary;
			background: #ffffff;
			border-radius: 50%;
			display: flex;
			justify-content: center;
			border: 2px solid $color-primary;
			cursor: pointer;

			b {
				font-size: 1rem;
			}
		}

		.uploadIcon {
			font-size: 4vmax;
			color: #4493de;
		}

		.no-file {
			position: absolute;
			text-align: center;
			top: 65%;
			left: 50%;
			transform: translatex(-50%);
			margin-top: 10px;
			color: #4493de;
			margin-top: 20px;
		}
	}
	.imgWrap_grid {
		width: 100%;
		text-align: center;
		margin-top: 20px;
		.imgDiv {
			width: 100px;
			height: 100px;
			display: inline-block;
			margin-right: 5px;
			position: relative;
			img {
				width: 100%;
				height: 100%;
			}
			.delBtn {
				position: absolute;
				width: 20px;
				height: 20px;
				text-align: center;
				line-height: 15px;
				background-color: #ccc;
				color: #fff;
				border-radius: 0 0 0 100%;
				top: 0;
				right: 0;
				cursor: pointer;
			}
		}
	}
	textarea {
		padding: 3%;
		width: 28.1vw;
		height: 8.3vw;
		background: #ffffff;
		border: 1px solid #dcdcdc;
		border-radius: 10px;
		outline: 0;
	}

	.skip-tips {
		margin-top: 1%;
		cursor: pointer;
		font-size: 0.8vw;

		.upload-later {
			color: #333;
		}
	}
}

.price {
	width: 100vw;
	font-size: 24px;
	background: $bg-primary;
	justify-content: center;
	position: fixed;
	z-index: 999;
	bottom: 0;
	align-items: center !important;

	button {
		height: 2.5em;
		min-width: 6.5em !important;
		margin: 1.8vmax;
		border-radius: 25px;
	}
	.disabled {
		opacity: 0.5;
		pointer-events: none;
		background-color: #ccc;
		color: #333;
	}
}

.stepBar {
	width: 100%;
	margin: 0 auto;
	max-width: 36vw;
	align-items: center !important;

	.stepItem {
		flex: 1;
		align-items: center !important;

		&:not(:first-child):not(:last-child) {
			flex: 2;
			align-items: center !important;
		}

		.steps {
			width: 20px;
			color: #6c757d;
			line-height: 20px;
			text-align: center;
			background: $line-primary;
			border-radius: 50%;
			cursor: pointer;
		}

		.stepLine {
			flex: 1;
			border: 2px solid $line-primary;
		}

		.checkedColor {
			border: 2px solid #5daf34 !important;
		}

		.checked {
			width: 20px;
			color: white;
			line-height: 20px;
			text-align: center;
			background: #5daf34;
			border-radius: 50%;
			cursor: pointer;
		}
	}
}

.imgWrap {
	height: 100%;

	img,
	video {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}
}

.btnWrap {
	display: inline-block;
	color: #ffffff;
}

.Transfer_Stickers {
	.bps-container {
		.des {
			color: #fff !important;
		}

		div {
			a {
				.sicker_Reviews {
					p {
						color: #fff !important;
					}
				}
			}
		}
	}
}

.homeBanner {
	position: relative;

	.containerImg {
		position: inherit !important;
		left: 0 !important;
		transform: none !important;
		display: grid !important;
		grid-template-columns: repeat(4, 1fr);
		width: 100% !important;

		div {
			padding: 23px 0;

			img {
				width: auto !important;
				vertical-align: middle;
				margin-right: 15px;
			}

			div {
				vertical-align: middle;

				p {
					font-size: 14px;
					font-family: Arial;
					font-weight: bold;
					color: #333333;
				}

				span {
					font-size: 14px;
					font-family: Arial;
					font-weight: 400;
					color: #999999;
				}
			}
		}
	}

	.imgWrap {
		height: 100%;

		img {
			width: 100%;
			height: 100%;
			object-fit: cover;
		}
	}

	.bps-container {
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		position: absolute;
		left: 8%;
		top: 5%;
		width: 35%;
		color: #fff;

		.container_icon {
			img {
				display: inline-block;
				width: 20px;
				height: 20px;
				vertical-align: middle;
			}

			span {
				vertical-align: middle;
				display: inline-block;
				margin-left: 12px;
				color: #333;
			}
		}

		h1 {
			width: 696px;
			font-size: 48px;
			font-weight: bold;
			color: $color-primary;
			text-shadow: 0px 9px 24px rgba(122, 99, 58, 0.35);
		}

		.des {
			width: 434px;
			margin: 17px 0 22px;
			font-size: 18px;
			font-weight: 400;
			color: #333333;
			line-height: 30px;
			text-shadow: 0px 9px 24px rgba(122, 99, 58, 0.35);
		}
	}
}

.termsBanner {
	position: relative;
	max-width: 1170px;
	height: 99px;
	margin: 40px auto;

	.bps-container {
		position: absolute;
		left: 50%;
		top: 50%;
		transform: translate(-50%, -50%);

		h1 {
			font-size: 36px;
		}
	}

	.imgWrap {
		width: 100%;
		height: 100%;

		img {
			object-fit: cover;
			width: 100%;
			height: 100%;
		}
	}
}

.policeBanner {
	position: relative;
	height: 420px;

	.bps-container {
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		position: absolute;
		left: 50%;
		top: 50%;
		transform: translate(-50%, -50%);
		color: #ffffff;

		.text {
			order: -1;
			margin-bottom: 20px;
			font-size: 24px;
		}

		h1 {
			width: 510px;
			font-size: 48px;
			font-weight: bold;
		}

		.des {
			width: 235px;
			margin: 20px 0 30px;
			font-size: 16px;
			font-weight: 400;
			line-height: 30px;
			text-shadow: 0px 9px 24px rgba(122, 99, 58, 0.35);
		}
	}
}

.aboutBanner {
	position: relative;
	height: 26.1458vw;

	.imgWrap {
		height: 100%;

		img {
			width: 100%;
			height: 100%;
			object-fit: cover;
		}
	}

	.bps-container {
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		position: absolute;
		left: 27.5%;
		top: 50%;
		transform: translate(-50%, -50%);
		width: 30%;

		h1 {
			font-size: 2.5vw;
			font-weight: bold;
		}

		.des {
			margin: 1.3542vw 0 2.6042vw;
			font-size: 1.25vw;
			font-weight: 400;
			line-height: 1.5625vw;
		}
	}
}

@media screen and (min-width: $mb-width) and (max-width: $pad-width) {
	.homeBanner {
		position: relative;

		.bps-container {
			h1 {
				width: 522px;
				font-size: 36px;
			}

			.des {
				width: 415px;
				font-size: 16px;
				line-height: 24px;
				margin: 18px 0 28px;
			}
		}
	}
}

@media screen and (max-width: $mb-width) {
	.mb_container {
		display: block !important;
		position: relative;

		h1 {
			position: absolute;
			font-size: 24px;
			color: $color-primary;
			top: 20px;
			left: 25px;
		}

		.des {
			position: absolute;
			font-size: 12px;
			left: 25px;
			top: 60px;
			width: 60%;
		}

		.mb_sticker {
			position: absolute;
			top: 90px;
			left: 25px;

			a {
				.sicker_img {
					img {
						width: 100% !important;
					}
				}

				div {
					img {
						height: 12px;
						width: 12px;
					}

					p {
						font-size: 12px;
					}
				}
			}
		}
	}

	.category {
		display: none;
	}

	.upload {
		right: 0;
		left: 0;

		.tips,
		.commentTip {
			font-size: 5.9vw;
		}

		.stepBar {
			max-width: 85%;
		}

		.imgUpload {
			width: 62.5vw;
			height: 62.5vw;

			.uploadIcon {
				font-size: 18vw;
			}
		}

		.comments {
			textarea {
				width: 70vw;
				height: 25vw;
			}
		}

		.skip-tips {
			font-size: 4.3vw;
			margin-top: 1%;
		}
	}

	.price {
		font-size: 18px !important;
		right: 0;
		left: 0;
	}

	.right-part {
		position: relative !important;
		width: 100% !important;
		right: auto !important;

		.stepBar {
			margin: 0 !important;
			max-width: none !important;
		}

		.paramList {
			.parameters {
				.sizeSelectList {
					grid-column: 1/11 !important;

					label {
						font-size: 2vmax;
					}
				}

				.quantityList {
					grid-column: 1/11 !important;
				}

				.materialsList {
					.listTitle {
						line-height: 0;
					}

					.radioGroup {
						div {
							label {
								font-size: 2vmax;
							}
						}
					}
				}
			}
		}

		.free {
			> div {
				color: #333;
				width: 45%;
				margin-top: 20px;
				font-size: 12px;
			}

			b {
				color: #68BD2C;
				display: inline-block;
				margin-right: 5px;
			}

			strong {
				margin-right: 4px;
			}
		}
	}

	.homeBanner {
		.bps-container {
			display: none;
			width: 100%;
		}

		.containerImg {
			grid-template-columns: repeat(2, 1fr);
			width: 100% !important;

			div {
				padding: 10px 0;

				img {
					margin-right: 3px;
				}

				div {

					p,
					span {
						font-size: 12px;
					}
				}
			}
		}

		height: auto;
		padding: 10px 17px;

		.imgWrap {
			overflow: hidden;
			position: relative;
			height: 225px;
			border-radius: 10px;

			img {
				object-position: right top;
			}
		}

		.bps-container {
			position: static;
			left: auto;
			top: auto;
			transform: none;
			padding: 0;

			h1 {
				width: 100%;
				font-size: 21px;
				text-shadow: none;
				color: #333333;
				margin-top: 30px;
			}

			.des {
				width: auto;
				margin: 20px 0 17px;
				font-size: 12px;
				color: #333333;
				line-height: 18px;
				text-shadow: none;
			}
		}
	}

	.policeBanner {
		height: auto;
		padding: 10px 17px;

		.imgWrap {
			overflow: hidden;
			position: relative;
			height: 150px;
			border-radius: 10px;

			img {
				object-position: right top;
			}
		}

		.bps-container {
			position: static;
			left: auto;
			top: auto;
			transform: none;
			padding: 0;
			color: #333333;

			.text {
				margin: 15px 0 10px;
				font-size: 12px;
				color: #666666;
			}

			h1 {
				width: 100%;
				font-size: 21px;
				text-shadow: none;
				margin-top: 20px;
			}

			.des {
				width: auto;
				margin: 10px 0;
				font-size: 12px;
				line-height: 18px;
				text-shadow: none;
				color: #666666;
			}
		}
	}

	.termsBanner {
		max-width: 100%;
		margin: 10px 10px 24px;
		height: 50px;

		.bps-container {
			h1 {
				font-size: 21px;
			}
		}
	}

	.aboutBanner {
		height: auto;
		padding: 2.9333vw 4.5333vw;

		.imgWrap {
			overflow: hidden;
			position: relative;
			height: 51.6vw;
			border-radius: 1.3333vw;

			img {
				object-position: 75% 100%;
			}
		}

		.bps-container {
			position: static;
			left: auto;
			top: auto;
			transform: none;
			padding: 0;
			width: 100%;

			.bps-button {
				height: 9.0667vw;
				border-radius: .8vw;
				font-size: 3.2vw;

				b.icon-bps-sanjiao {
					margin-left: 1.3333vw;
					font-size: 3.2vw;
				}
			}

			h1 {
				width: 100%;
				font-size: 5.6vw;
				text-shadow: none;
				color: #333333;
				margin-top: 4vw;
			}

			.des {
				width: auto;
				margin: 2.6667vw 0 5.6vw;
				font-size: 3.2vw;
				color: #333333;
				line-height: 18px;
				text-shadow: none;
			}
		}
	}

}
</style>
