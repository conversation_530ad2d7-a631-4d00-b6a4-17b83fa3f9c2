<template>
  <div class="fontParam">
    <div class="iconInput"
      v-if="!noText">
      <b class="icon-edit"
        v-show="!value['Your Text'] && !spelling"></b>
      <el-input v-if="isText && textDesign"
        class="nTextArea"
        type="textarea"
        ref="textArea"
        :autosize="{ minRows: 3, maxRows: 5}"
        :placeholder="value.placeholder"
        v-model="value['Your Text']"
        @input="changeInput"
        @compositionstart="spelling = true"
        @compositionend="spelling = false"
        resize="none">
      </el-input>
      <el-input v-else
        class="text-input text-single"
        v-model="value['Your Text']"
        :placeholder="value.placeholder"
        @input="changeInput"
        @compositionstart="spelling = true"
        @compositionend="spelling = false"
        type="text" />
      <span class="warning"
        v-if="textRelLength()">
        {{ textDesign ? lang.neon.overFontNums:lang.neon.warning }}
      </span>
    </div>

    <!-- :style="{visibility:!showOther?'hidden':'visible'}" -->
    <div class="fontAndColor"
      v-if="value['Your Text'] || noText || fdNeonType">
      <div class="select-box font-box">
        <el-popover popper-class="popo-row"
          placement="bottom"
          ref="popover1"
		  :offset="offsetOptions.font"
          :width="device == 'mb' ? '345':'516'"
          @show="font_active = true"
          @hide="font_active = false"
          v-model="popover1Value"
          :append-to-body="false"
          trigger="click">
          <ChooseFont v-if="popover1Value"
            :font_list="filterFont(dataMap.fontList)"
            v-model="value['Select Font']"
            @change="changeFontOption"
            @shutPopoverFont="popover1Value = false" />
          <div class="option-input"
            slot="reference"
            :style="{'border-color':font_active ? 'var(--neon-light-color)':'#dbdbdb'}">
            <div>
              <p v-if="!value['Select Font']"
                style="color:#ccc">{{ lang.neon.font }}</p>
              <p v-else>
                {{ value['Select Font']?.paramName }}
              </p>
              <!-- <img v-else
                :src="formatImage(value['Select Font']?.imageJson)"> -->
            </div>
            <i class="el-icon-caret-top"
              :class="{'icon-active':font_active}"></i>
          </div>
        </el-popover>
      </div>
      <div class="select-box color-box" v-if="!fdNeonType">
        <el-popover popper-class="popo-row"
          placement="bottom"
          ref="popover2"
          :width="device == 'mb' ? '360':'485'"
		  :offset="offsetOptions.color"
          @show="color_active = true"
          @hide="color_active = false"
          v-model="popover2Value"
          :append-to-body="false"
          trigger="click">
          <SelectColor v-if="popover2Value"
            :color_list="filterColor(dataMap.colorList)"
            @shutPopoverColor="popover2Value = false"
            v-model="value['Select Color']"
            :cols_num="device == 'pc' ? 7:4"
            @change="changeFontOption" />
          <div class="option-input color-input"
            slot="reference"
            :style="{'border-color':color_active ? 'var(--neon-light-color)':'#dbdbdb'}">
            <span class="color-ball"
              v-if="value['Select Color']?.paramName != 'RGB Color' && !value['Select Color']?.isNeonColorful"
              :style="{ '--color':value['Select Color']?.paramCode}"></span>
            <span class="color-ball img-ball"
              v-else
              :style="{ backgroundImage: 'url('+filterImg(value['Select Color'])?.url+')' }"></span>
            <i class="el-icon-caret-top"
              :class="{'icon-active':color_active}"></i>
          </div>
        </el-popover>
      </div>
      <div class="align-box"
        v-if="!noText"
        @click="changeAlignType">
        <span v-for="item in alignBtns"
          :class="{'icon-active':item.id == value.textAlign}"
          :id="item.id"
          :key="item.id"><b :class="item.icon"></b></span>
      </div>
	  <div class="fd-color-input" v-if="fdNeonType">
		  <p>{{ '1-2 ' + lang.neon.choseYourFavoriteColor }}</p>
		  <SelectColor :color_list="filterColor(dataMap.colorList)" v-model="value['Select Color']" :cols_num="device == 'pc' ? 7:4" @change="changeFontOption" />
	  </div>
      <div class="tube-box">
        <div v-if="!fdNeonType" class="tube-input" :style="{'border-color':tube_active ? 'var(--neon-light-color)':'#dbdbdb'}">
          <p onselectstart="return false;">{{ lang.neon.neonSignsTubeOff }} {{ value.tubeColor?.alt }} <span
              :class="{'icon-active':tube_active}"
              @click="tube_active = !tube_active">{{ lang.neon.change }} <i class="el-icon-arrow-up"></i></span> </p>
		</div>
	    <div v-else>
		    {{ '1-3 ' + lang.neon.neonSignsTubeOff }} {{ value.tubeColor?.alt }}
	    </div>
<!--		  <p>{{ '1-3 Select Tube Color When Off: White Tube Variation' }}</p>-->
        <SampleImgCard class="sampleImgCard"
          v-show="fdNeonType || tube_active"
          :data_list="forMatTubeColor(value['Select Color'])"
          v-model="value.tubeColor"
          @change="changeFontOption" />
      </div>
    </div>
  </div>
</template>

<script>
import ChooseFont from "./ChooseFont.vue";
import SampleImgCard from "./SampleImgCard.vue";
import SelectColor from "./SelectColor.vue";
export default {
  name: "FontParam",
  model: {
    prop: "value",
    event: ["change"],
  },
  watch: {},
  components: { ChooseFont, SelectColor, SampleImgCard },
  props: {
    fdNeonType: {
		type: String,
		default: "",
    },
    value: {
      type: Object,
      default: () => {},
    },
    // 总纲
    data_list: {
      type: Array,
      default: () => [],
    },
    // 模板？
    isText: {
      type: Boolean,
    },
    // 询盘界面的选颜色和字体(不需要文字,颜色不需要过滤)
    noText: {
      tpye: Boolean,
      default: () => false,
    },
    textDesign: {
      type: Boolean,
      default: () => false,
    },
	offsetOptions: {
	  type: Object,
	  default: () => {
		return {
			font: 0,
			color: 0
		}
	  }
	}
  },
  data() {
    return {
      alignBtns: [
        {
          id: "left",
          icon: "icon-a-lrgl-text-leftzhuanhuan",
        },
        {
          id: "center",
          icon: "icon-a-lrgl-text-centerzhuanhuan",
        },
        {
          id: "right",
          icon: "icon-a-lrgl-text-rightzhuanhuan",
        },
      ],
      popover1Value: false,
      popover2Value: false,

      showOther: false,
      dataMap: {
        fontList: [],
        colorList: [],
        sizeList: [],
      },
      font_active: false,
      color_active: false,
      tube_active: false,
      // 输入法正在拼写
      spelling: false,
      defTube: {
        url: "https://oss-static-cn.liyi.co/web/quoteManage/20231019/ffffff_2052taQmnT.png",
        alt: "White Tube",
        name: "darkWhite",
        color: "#ffffff",
      },
      // 灯皮
      tubeList: [],
    };
  },
  computed: {
    lang() {
      return this.$store.getters.lang.quote || {};
    },
    device() {
      return this.$store.state.device;
    },
  },
  methods: {
    // 计算文字真实长度（去掉换行符和首尾空格）
    textRelLength() {
      let noN = "";
      if (this.value["Your Text"]) {
        noN = this.value["Your Text"].replace(/\s/g, "");
        return noN.length > Number(this.value.textCount);
      }
      return false;
    },
    forMatTubeColor(val) {
      let arr = [];
      let others = [];
      this.defTube.alt = this.lang.neon.defTubeAlt;
      arr.push(this.defTube);
      if (val && val.imageJsonColor) {
        let imageJsonColor = JSON.parse(val.imageJsonColor).filter(
          (x) => x.alt && x.name && x.url
        );
        others = imageJsonColor.map((x) => {
          return {
            ...x,
            id: val.priceInfo.id,
            color: val?.paramCodeDark || val?.paramCode || null,
          };
        });
      }
      arr = arr.concat(others);
      this.tubeList = arr;
      return arr;
    },
    // 非纯文字暂时过滤掉rgb和Gradient Changing Color
    filterColor(colors) {
      if (this.isText || this.noText) {
		  if (this.fdNeonType === 'static') {
			  return colors.filter(
				  (x) => x.paramName != "RGB Color" && !x.isNeonColorful
			  );
		  } else if(this.fdNeonType === 'rgbChanging') {
			  return colors.filter(
				  (x) => x.paramName === 'RGB Color'
			  )
		  } else if(this.fdNeonType === 'rgbGradient') {
			  return colors.filter(
				  (x) => x.isNeonColorful
			  )
		  } else{
			  return colors;
		  }
      } else {
        return colors.filter(
          (x) => x.paramName != "RGB Color" && !x.isNeonColorful
        );
      }
    },
    filterFont(arr) {
      if (this.noText) {
        return arr;
      } else {
        return arr.filter((x) => !x.onlyAddInquiry);
      }
    },
    changeAlignType(e) {
      if (e.target.className == "align-box") return;
      this.$emit("setTextAlign", e.target.id);
    },
    init() {
      // 获取字体相关选项参数
      this.data_list.forEach((x) => {
        if (x.paramName === "Select Font") this.dataMap.fontList = x.childList;
        if (x.paramName === "Select Color")
          this.dataMap.colorList = x.childList;
      });

      // 字体和颜色默认选中
      // this.value["Select Font"] = this.dataMap.fontList[0];
      // this.value["Select Color"] = this.dataMap.colorList[0];
    },

    show_font() {
      this.font_active = true;
    },

    filterImg(val) {
      if (val.imageJson) {
        let arr = JSON.parse(val.imageJson);
        return arr.find((x) => {
          return x.name == "RGB Color";
        });
      }
    },

    changeInput(val) {
      // 过滤emoji字符和.
      this.value["Your Text"] = val.replace(
        /[\uD83D\uD83C][\uDC00-\uDFFF]|\uD83D[\uDC00-\uDE4F]|\./g,
        ""
      );
      this.$emit("change", this.value);
      this.$emit("input");
    },

    changeFontOption(val) {
      if (val.paramType === "FONT") {
        this.value["Select Font"] = val;
        this.$emit("changeFont", val);
        if (this.device == "mb") this.popover1Value = false;
      } else if (val.paramType == "COLOR") {
        this.value["Select Color"] = val;
        // 换色默认选第一个灯皮
        if (!this.noText) {
          this.value.tubeColor = this.tubeList[0];
        } else this.value.tubeColor = this.defTube;
        this.tube_active = false;
        this.$emit("changeColor", val);
        if (this.device == "mb") this.popover2Value = false;
      }
      this.$emit("change", this.value);
    },

    formatImage(val) {
      if (val) {
        return JSON.parse(val)[0].url;
      } else {
        return "";
      }
    },

    // 用于父组件点击遮罩关闭弹框
    closeAll() {
      this.popover1Value = false;
      this.popover2Value = false;
    },
  },
  created() {
    this.init();
  },
};
</script>

<style lang="scss">
.fontParam {
  margin-top: 10px;
  .iconInput {
    position: relative;
    .warning {
      color: red;
      font-size: 0.875em !important;
      display: inline-block;
      margin-top: 3px;
      padding-inline: 5px;
    }
    b {
      display: block;
      top: 8px;
      font-size: 0.875em;
      left: 17px;
      position: absolute;
      color: #999999;
      z-index: 10;
    }
  }
  .nTextArea {
    .el-textarea__inner {
      font-size: 16px;
	  border-color: var(--neon-light-color);
	  background-color: #f4f4f4;
      &::placeholder {
        text-indent: 1.3em;
      }
    }
  }
  .text-input {
    .el-input__inner {
      border-radius: 6px;
      min-height: 30px;
      height: 30px;
      padding: 0 11px;
      border: 1px solid #dbdbdb;
      line-height: 30px;
      width: 100%;
      font-size: 1em;
	  border-color: var(--neon-light-color);
	  background-color: #f4f4f4;
	  @media screen and(max-width: 768px) {
		font-size: 16px;
	  }
      &::placeholder {
        text-indent: 1.5em;
      }
    }
    &::placeholder {
      text-indent: 1.5em;
      color: #999999;
    }
    &.text-area {
      resize: none;
      overflow-y: hidden;
      min-height: 80px;
      &:focus {
        border: 1px solid var(--neon-light-color);
      }
    }
  }
}

.fontAndColor {
  @media screen and(max-width:768px) {
    display: grid;
    // grid-template-columns: 0.206fr 0.134fr 0.132fr;
  }
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 6px;

  .font-box {
    min-width: 100px;
  }
  .color-box {
    width: 91px;
  }
  .align-box {
    width: 90px;
    height: 30px;
    padding: 0 5px;
    display: grid;
    align-items: center;
    gap: 10px;
    font-size: 16px;
    grid-template-columns: repeat(3, 1fr);
    @media screen and(max-width: 768px) {
      display: none;
    }
    span {
      cursor: pointer;
      &.icon-active {
        color: #b61ee8;
      }
      b {
        pointer-events: none;
      }
    }
  }
	.fd-color-input{
		.color-container{
			.select-color{
				.subTitle,.example-line{
					display: none;
				}
				.select-content{
					margin-top: 10px;
				}
			}
		}
	}
  .tube-box {
    width: 100%;
    @media screen and(max-width:768px) {
      grid-column-end: span 3;
    }
    .sampleImgCard {
      margin-top: 11px;
    }
  }
}

.option-input {
  height: 30px;
  border-radius: 6px;
  padding: 0 13px;
  color: #ccc;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #000;
  border: 1px solid #dbdbdb;
  cursor: pointer;
  &.color-input {
    padding: 0 6px;
  }
  .color-ball {
    height: 20px;
    width: 20px;
    border-radius: 3px;
    object-fit: contain;
    background-position: center;
    background-color: var(--color);
    &.img-ball {
      width: 42px;
      aspect-ratio: 48 / 14;
      object-fit: contain;
      background-size: cover;
    }
  }
  i {
    transition: transform 0.3s ease-in-out;
    color: #282929;
    &.icon-active {
      transform: rotate(180deg);
      transition: all 0.3s ease-in-out;
    }
  }
}
.tube-input {
  display: flex;
  font-size: 14px;
  white-space: break-spaces;
  @media screen and(max-width:768px) {
    font-size: 12px;
  }
  span {
    cursor: pointer;
    color: #8d20fc;
    text-decoration: underline;
    i {
      font-weight: 600;
      transition: transform 0.3s ease-in-out;
    }
    &.icon-active {
      i {
        transform: rotate(180deg);
        transition: all 0.3s ease-in-out;
      }
    }
  }
}

.popo-row[x-placement^="bottom"] .popper__arrow {
  border-bottom-color: var(--neon-light-color) !important;
}

.popo-row[x-placement^="top"] .popper__arrow {
  border-top-color: var(--neon-light-color) !important;
}

.popo-row {
  border: 1.5px solid var(--neon-light-color) !important;
}
</style>