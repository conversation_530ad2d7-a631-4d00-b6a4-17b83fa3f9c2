<template>
	<div class="infoDialog">
		<el-dialog :title="lang.getQuote" :lock-scroll="false" :width="device == 'mb' ? '95%' : '460px'" :visible.sync="infoDialogVisible" :before-close="closeFun" :append-to-body="appendTobody">
			<el-form :model="dynamicValidateForm" ref="dynamicValidateForm" :rules="rules" :label-position="device == 'mb' ? 'top' : 'top'" label-width="110px" class="demo-dynamic">
				<el-row :gutter="10">
					<el-col :span="12">
						<el-form-item prop="firstName" :label="lang.firstName" :rules="[{ required: true, message: lang.p28, trigger: 'blur' }]">
							<el-input v-model="dynamicValidateForm.firstName" :placeholder="lang.inquiryPlaceholder.firstName"></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item prop="lastName" :label="lang.lastName" :rules="[{ required: true, message: lang.p29, trigger: 'blur' }]">
							<el-input v-model="dynamicValidateForm.lastName" :placeholder="lang.inquiryPlaceholder.lastName"></el-input>
						</el-form-item>
					</el-col>
				</el-row>
				<el-form-item
					prop="email"
					:label="lang.email"
					:rules="[
						{ required: true, message: lang.p30, trigger: 'blur' },
						{
							type: 'email',
							message: lang.p31,
							trigger: ['blur', 'change'],
						},
					]"
				>
					<el-input v-model="dynamicValidateForm.email" @blur="getSmsSubscriptionByMail" :placeholder="lang.inquiryPlaceholder.email"></el-input>
				</el-form-item>
				<el-form-item prop="telephone">
					<template #label>
						<span><span style="color: #f57272">*</span> {{ lang.telephone }}</span>
					</template>
					<div class="phont">
						<div>
							<img src="https://static-oss.gs-souvenir.com/web/quoteManage/20231228/image-removebg-preview_24_20634Zmnah.png" />
						</div>
						<el-input v-model="dynamicValidateForm.areaCode" @input="handleInputAreaCode">
							<template slot="prefix">
								<span>+</span>
							</template>
						</el-input>
						<el-input v-model="dynamicValidateForm.telephone" @input="handleInput" :placeholder="lang.inquiryPlaceholder.telephone"></el-input>
					</div>
				</el-form-item>
				<div class="allowMsg" v-show="showSMStext" @click="handleAllowSendMsg">
					<div class="allow-header">
						<div class="allow-checkbox" :class="{ active: dynamicValidateForm.isSmsSubscriptions }"></div>
						<span class="allow-title">{{ lang.allowMsgTitle }}</span>
					</div>
					<p class="allow-content">
						{{ lang.allowMsgContentBefore + projectName + lang.allowMsgContentAfter }}
					</p>
				</div>
				<!-- <el-form-item prop="inquiryBudget" :label="lang.budget">
                    <el-select class="selectBox" value-key="start" :placeholder="lang.inquiryPlaceholder.budget" v-model="dynamicValidateForm.inquiryBudget">
                        <el-option v-for="item in budgetRange" :key="item.start" :label="getLable(item)" :value="item">
                            <div style="display: flex; align-items: center" class="inquiryText"><span class="round"></span>{{ getLable(item) }}</div>
                        </el-option>
                    </el-select>
                </el-form-item> -->
				<el-form-item prop="subject" :label="lang.title" v-if="!noTitle">
					<el-input v-model="dynamicValidateForm.subject" :placeholder="lang.inquiryPlaceholder.title"></el-input>
				</el-form-item>

				<el-form-item prop="expectTime" :label="lang.expectedDate" v-if="isGS">
					<el-date-picker v-model="dynamicValidateForm.expectTime" type="date" format="MM/dd/yyyy" value-format="MM/dd/yyyy" :picker-options="pickerOptions" :append-to-body="false" :clearable="false" :placeholder="lang.inquiryPlaceholder.deliveryDate"> </el-date-picker>
				</el-form-item>
				<!-- 自定义区域 -->
				<slot name="custom"></slot>
				<!--                :rules="[{ required: true, message: lang.p34, trigger: 'blur' }]"-->
				<!-- v-if="storeUploadSwitch" -->
				<el-form-item>
					<div style="line-height: normal">
						<span v-show="uploadLists.length <= 0">{{ lang.inquiryUpload }}</span
						><span @click="clickUpload" class="clickUploadText">{{ lang.inquiryUpload2 }}</span>
					</div>
					<div class="text_upload_box" v-if="uploadLists.length > 0">
						<!-- <p style="line-height: 1.5; word-break: normal">
                            You have not uploaded your artwork. <span class="click_text" @click="clickUpload">Click here</span> to upload, or send an email to <a class="click_text" :href="`mailto:${userEmail}`">{{ userEmail }}</a
                            >.
                        </p> -->
						<div class="uploadList">
							<template v-if="uploadLists.length > 0">
								<ul style="margin-left: 0; padding-left: 0">
									<li v-for="citem in recomendUpload" class="uploadItem" :key="citem">
										<span>{{ getImageNameFromURL(citem) }}</span>
										<div>
											<b class="icon-check" style="color: #0cbd5f"></b>
											<el-button style="border: none; padding: 5px; opacity: 0" icon="el-icon-delete"></el-button>
										</div>
									</li>
									<li v-for="(citem, cindex) in otherUploads" class="uploadItem" :key="citem.secure_url">
										<span>{{ citem.original_filename }}</span>
										<div>
											<b class="icon-check" style="color: #0cbd5f"></b>
											<el-button style="border: none; padding: 5px; opacity: 0" icon="el-icon-delete"></el-button>
										</div>
									</li>
									<li v-for="(citem, cindex) in uploadList" class="uploadItem" :key="citem.secure_url">
										<span>{{ citem.original_filename }}</span>
										<div>
											<b class="icon-check" style="color: #0cbd5f"></b>
											<el-button style="border: none; padding: 5px" icon="el-icon-delete" @click.stop="delUploadImg(cindex)"></el-button>
										</div>
									</li>
								</ul>
							</template>
						</div>
					</div>
				</el-form-item>

				<el-form-item>
					<div class="btn-box" style="display: flex; align-items: center; justify-content: center">
						<el-button type="primary" @click="debounceAddInquiry">{{ lang.Submit }}</el-button>
						<el-button class="cancelBtn" @click="cancelFun">{{ lang.Cancel }}</el-button>
					</div>
				</el-form-item>
			</el-form>
		</el-dialog>
		<input type="file" ref="uploadInfoDialog" :accept="acceptFileType" multiple @change="uploadPic2" hidden />
	</div>
</template>

<script>
import { uploadFile } from "@/utils/oss";
import { debounce } from "@/utils/utils";
import { checkFile, acceptFileType } from "@/utils/validate";
import { getSmsSubscriptionState, getSmsSubscriptionByMail } from "@/api/web";
export default {
	name: "infoDialog",
	model: {
		prop: "customComment",
		event: "changeCustomComment",
	},
	props: {
		infoDialogVisible: {
			type: Boolean,
			default: false,
		},
		otherUpload: {
			type: Array,
			default: () => [],
		},
		recomendUpload: {
			type: Array,
			default: () => [],
		},
		noTitle: {
			type: Boolean,
			default: () => false,
		},
		appendTobody: {
			type: Boolean,
			default: () => false,
		},
	},
	data() {
		var validatePhone = (rule, value, callback) => {
			if (!value) {
				return callback(new Error(this.lang.telephoneRequired));
			} else if (!this.dynamicValidateForm.areaCode) {
				return callback(new Error(this.lang.areaCode));
			} else if (this.dynamicValidateForm.telephone.length < 7) {
				return callback(new Error(this.lang.phoneNoMinLength));
			}
			return callback();
		};
		return {
			acceptFileType,
			dynamicValidateForm: {
				email: "",
				firstName: "",
				lastName: "",
				telephone: null,
				inquiryBudget: null,
				areaCode: null,
				isSmsSubscriptions: 0,
				expectTime: "",
			},
			uploadList: [],
			budgetRange: [
				{
					start: 100,
					end: 500,
				},
				{
					start: 600,
					end: 1000,
				},
				{
					start: 1100,
					end: 2000,
				},
				{
					start: 2100,
					end: 5000,
				},
				{
					start: 5100,
					end: 10000,
				},
				{
					start: -1,
					end: 10000,
				},
			],
			rules: {
				telephone: [{ validator: validatePhone, trigger: "blur" }],
			},
			showSMStext: false,
			pickerOptions: {
				disabledDate(time) {
					// 获取今天的开始时间（00:00:00）
					const now = new Date();
					now.setHours(0, 0, 0, 0);
					return time.getTime() < now.getTime();
				},
			},
			debounceAddInquiry: null,
		};
	},
	methods: {
		//是否短信订阅(登录状态)
		getSmsSubscriptionLogin() {
			getSmsSubscriptionState().then((res) => {
				if (!res.data) this.showSMStext = true;
				else {
					if (res.data.isSmsSubscriptions == 0) {
						this.showSMStext = true;
					} else {
						this.showSMStext = false;
					}
				}
			});
		},

		//是否短信订阅(未登录状态)
		getSmsSubscriptionByMail() {
			if (!this.dynamicValidateForm.email.trim()) return;
			const params = {
				email: this.dynamicValidateForm.email,
			};
			getSmsSubscriptionByMail(params).then((res) => {
				if (!res.data) this.showSMStext = true;
				else {
					if (res.data.isSmsSubscriptions == 0) {
						this.showSMStext = true;
					} else {
						this.showSMStext = false;
					}
				}
			});
		},

		getImageNameFromURL(imageURL) {
			// 获取最后一个斜杠的索引
			const lastIndex = imageURL.lastIndexOf("/");
			// 提取文件名部分
			const imageName = imageURL.substring(lastIndex + 1);
			// 返回图片名称
			return imageName;
		},
		getFileName(name) {
			// 获取到文件名
			let pos = name.lastIndexOf("/"); // 查找最后一个/的位置
			return name.substring(pos + 1); // 截取最后一个/位置到字符长度，也就是截取文件名
		},
		//手机号码包含-
		validateTelephone(rule, value, callback) {
			if (value && !/^[\d-]+$/.test(value)) {
				callback(new Error(this.lang.p32));
			} else {
				callback();
			}
		},
		handleInput(event) {
			// 只允许输入数字和短横线
			this.dynamicValidateForm.telephone = event.replace(/[^\d-]/g, "");
		},
		handleInputAreaCode(event) {
			// 只允许输入数字
			this.dynamicValidateForm.areaCode = event.replace(/[^\d]/g, "");
		},
		handleAllowSendMsg() {
			let numBoolean = this.dynamicValidateForm.isSmsSubscriptions ? 0 : 1;
			this.dynamicValidateForm.isSmsSubscriptions = numBoolean;
		},
		delUploadImg(index) {
			this.uploadList.splice(index, 1);
		},
		clickUpload() {
			this.$refs.uploadInfoDialog.click();
		},
		infoDialog() {
			this.clickUpload();
			this.$store.commit("setSizeDialog", false);
		},
		uploadPic2(event, type = "upload") {
			this.$gl.show();
			let files = type === "upload" ? event.target.files : event;
			let fileResult = checkFile(Array.from(files), this.acceptFileType, 80);
			if (!fileResult) {
				this.$toast.error("File type error");
				this.$gl.hide();
				return false;
			}
			if (fileResult.nomalSize.length == 0) {
				this.$gl.hide();
				this.$store.commit("setSizeDialog", true);
				this.$store.commit("setInputRefName", "infoDialog");
				this.$store.commit("setOverSizeList", fileResult.overSize);
				this.$refs.uploadInfoDialog.value = "";
				return false;
			}
			let uploadPromises = [];
			Array.from(fileResult.nomalSize).forEach((file) => {
				let promise = uploadFile(file).then((res) => {
					let temp = {
						original_filename: file.name,
						secure_url: res,
					};
					// this.uploadArtworkList.push(temp);
					// this.uploadList.push(temp);
					this.$set(this.uploadList, this.uploadList.length, temp);
				});
				uploadPromises.push(promise);
			});
			Promise.all(uploadPromises).then(() => {
				this.$gl.hide();
				this.$refs.uploadInfoDialog.value = "";
				if (fileResult.overSize.length > 0) {
					this.$store.commit("setSizeDialog", true);
					this.$store.commit("setInputRefName", "infoDialog");
					this.$store.commit("setOverSizeList", fileResult.overSize);
				}
			});
		},

		closeFun() {
			this.$emit("update:infoDialogVisible", false);
		},
		confirmFun() {
			this.$refs["dynamicValidateForm"].validate((valid) => {
				if (valid) {
					this.$emit("update:infoDialogVisible", false);
					// this.dynamicValidateForm.inquiryBudget = JSON.stringify(this.dynamicValidateForm.inquiryBudget);
					this.$emit("getValue", this.dynamicValidateForm);
				} else {
					console.log("error submit!!");
					return false;
				}
			});
		},
		cancelFun() {
			this.$emit("update:infoDialogVisible", false);
		},
		getLable(item) {
			if (item.start > 0) {
				return `${this.symbol}${item.start} - ${this.symbol}${item.end}`;
			}
			return `Above${this.symbol}${item.end}`;
		},
	},
	computed: {
		projectName() {
			return this.$store.state.proName;
		},
		lang() {
			return this.$store.getters.lang.quote || {};
		},
		device() {
			return this.$store.state.device;
		},
		userInfo() {
			return this.$store.state.userInfo;
		},
		isLogin() {
			return this.$store.getters.isLogin;
		},
		proId() {
			return this.$store.state.proId;
		},
		proType() {
			return this.$store.state.proType;
		},
		isGS() {
			//GS体系 proType=0 且不是FD和POP
			return this.proType == 0 && this.proId != 9 && this.proId != 10;
		},
		emailText() {
			let proType = this.$store.state.proType;
			let str = proType == 0 ? "<EMAIL>" : proType == 1 ? "<EMAIL>" : "<EMAIL>";
			return str;
		},
		storeUploadSwitch() {
			return this.$store.state.storeUploadSwitch;
		},
		userEmail() {
			return this.$store.state.proSystem.email;
		},
		symbol() {
			return this.$store.state.currency.symbol || "$";
		},
		uploadLists() {
			return this.otherUpload.concat(this.uploadList).concat(this.recomendUpload);
		},
		otherUploads() {
			let newArr = JSON.parse(JSON.stringify(this.otherUpload));
			if (Object.prototype.toString.call(this.otherUpload[0]) != "[object Object]") {
				let newArr2 = newArr.map((item) => {
					return {
						secure_url: item,
						original_filename: this.getFileName(item),
					};
				});
				newArr = newArr2;
			}
			return newArr;
		},
		//获取区号
		areaCodes() {
			return this.$store.state.areaCode;
		},
	},
	watch: {
		infoDialogVisible: function (val) {
			// if (!val) {
			//   this.uploadList = [];
			// }
			if (val) {
				if (this.userInfo && this.userInfo.telephone) {
					let telephone, areaCode;
					if (this.userInfo.telephone) {
						if (this.userInfo.telephone.includes("-")) {
							telephone = this.userInfo.telephone.split("-")[1];
							areaCode = this.userInfo.telephone.split("-")[0];
						} else {
							telephone = this.userInfo.telephone;
							areaCode = this.areaCodes;
						}
					}
					this.dynamicValidateForm = {
						email: this.userInfo.email,
						firstName: this.userInfo.firstName,
						lastName: this.userInfo.lastName,
						areaCode: this.userInfo.telephone ? areaCode : "",
						telephone: this.userInfo.telephone ? telephone : "",
						isSmsSubscriptions: this.userInfo.isSmsSubscriptions,
					};
				} else {
					this.dynamicValidateForm.areaCode = this.areaCodes;
				}
			}
		},
		// infoDialogVisible(newVal){
		//   if(!newVal)
		// },
		uploadList: {
			deep: true,
			// immediate: true,
			handler(newVal) {
				// console.log('newVal>>>>>>>>>>>>', newVal);
				// this.$emit("update:uploadArtworkList", newVal);
				// this.$emit("update:uploadArtworkLater", newVal.length == 0); //list有值 switch开关置为false

				this.$emit("update:uploadList", newVal);
				// this.$emit("update:isUpload", newVal.length == 0); //list有值 switch开关置为false
			},
		},
	},
	mounted() {
		this.debounceAddInquiry = debounce(() => {
			this.confirmFun();
		}, 500);
		this.otherUpload.forEach((item) => {
			item.noself = true;
		});
		if (this.isLogin) this.getSmsSubscriptionLogin();
		this.$Bus.$on("infoDialog", this.infoDialog);
	},
	beforeDestroy() {
		this.$Bus.$off("infoDialog");
	},
};
</script>

<style scoped lang="scss">
.phont {
	display: flex;

	::v-deep .el-input--prefix {
		width: 16%;

		.el-input__prefix {
			font-size: 20px;

			span {
				position: relative;
				top: -2px;
				left: 5px;
				color: #333;
			}
		}

		.el-input__inner {
			padding: 0 9px 0 22px;
			border-radius: 5px 0 0 5px !important;
		}
	}

	::v-deep .el-input:last-child {
		.el-input__inner {
			border-radius: 0 5px 5px 0 !important;
		}
	}

	img {
		position: absolute;
		z-index: 1;
		width: 13px;
		left: 44px;
		bottom: 0;
	}
}

.allowMsg {
	padding: 10px;
	margin-block: 10px 15px;
	background-color: #fff1ea;
	cursor: pointer;
	.allow-header {
		display: flex;
		align-items: center;
		font-family: Cambria, Cochin, Georgia, Times, "Times New Roman", serif;
		.allow-checkbox {
			height: 2em;
			width: 2em;
			background-color: #fff;
			border-radius: 2px;
			border: 1px solid #d24600;
			position: relative;
			&.active::before {
				content: "";
				position: absolute;
				left: 50%;
				top: 50%;
				height: 18px;
				width: 8px;
				transform: translate(-50%, -60%) rotate(45deg);
				border-right: 1px solid #d24600;
				border-bottom: 1px solid #d24600;
			}
		}
		.allow-title {
			font-weight: 400;
			font-size: 16px;
			margin-left: 10px;
		}
	}
	.allow-content {
		font-size: 11px;
		margin-top: 5px;
		line-height: 1.5;
	}
}

.infoDialog::v-deep {
	.el-dialog {
		font-family: Calibri, Arial, serif;
		border-radius: 10px;

		.el-dialog__title {
			font-weight: bold;
		}

		.el-dialog__body {
			// padding: 10px 20px 10px 0;
			padding: 10px 20px;

			.el-form-item {
				margin-bottom: 15px;
			}

			.el-form-item__label {
				//   padding: 0 5px 0 0;
				padding: 0;
				line-height: 30px;
			}

			.selectBox.el-select {
				width: 100%;
			}
		}

		.el-button {
			padding: 7px 37px;
			margin: 6px;
		}

	}
	.el-date-editor {
		width: 100%;
		&.el-input--prefix .el-input__inner {
			padding-left: 10px;
		}
		.el-input__prefix {
			right: 5px;
			left: initial;
			i {
				font-size: 20px;
			}
		}
	}
}

.clickUploadText {
	cursor: pointer;
	color: var(--color-bright);
}

.text_upload_box {
	// border: 1px dashed #e9ecf0;
	padding: 8px 10px;
}

.uploadList {
	width: 100%;
	min-height: 40px;
	max-height: 120px;
	overflow: auto;
	margin-bottom: 10px;
	text-align: center;
	background: #fafafa;
	border-radius: 4px;

	.uploadItem {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 0 5px;
		font-size: 14px;
		line-height: normal;

		span {
			width: 75%;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
			text-align: left;
			text-decoration: underline;
		}
	}

	.myIcon {
		margin: 0 4px;
	}
}

.click_text {
	color: #007aff;
	text-decoration: underline;
	cursor: pointer;
}

.round {
	width: 18px;
	height: 18px;
	border: 1px solid #d9d9d9;
	border-radius: 50%;
	margin-right: 6px;
}

.el-select-dropdown__item.hover {
	background-color: var(--color-second);

	.inquiryText {
		color: var(--color-bright);

		.round {
			border: 1px solid transparent;
			box-shadow: 0px 0px 0px 4px inset var(--color-bright);
		}
	}
}

.el-select-dropdown__item.selected {
	.inquiryText {
		color: var(--color-bright);

		.round {
			border: 1px solid transparent;
			box-shadow: 0px 0px 0px 4px inset var(--color-bright);
		}
	}
}

.el-select-dropdown__item {
	color: #333333;
}

.el-select-dropdown__item.hover {
	color: #333333;
}

.cancelBtn {
	color: #333333;
	background: #e6e6e6;
}
</style>
