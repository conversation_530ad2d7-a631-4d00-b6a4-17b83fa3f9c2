import { request } from "~/utils/request";

// 获取某个网站的所有经销商用户
export function getUserListByProId() {
	return request({
		url: "/retailer/user/getUserListByProId",
		method: "get",
	});
}

// 获取所有页面(非树结构)
export function getPageList() {
	return request({
		url: "/retailer/page/listAllPagesNoTree",
		method: "post",
	});
}

// 获取页面编辑日志列表
export function getPageRowLogList(data) {
	return request({
		url: "/retailer/page/pageRowDraft/getPageRowLogList",
		method: "post",
		data,
	});
}
