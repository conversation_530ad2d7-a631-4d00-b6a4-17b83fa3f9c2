<script>
import freeTip from "@/components/Quote/freeTip.vue";
import ToolTip from "@/components/HalfDesign/customMedals/common/ToolTip";
import { getTaxByPrice } from "@/api/web";
export default {
	data() {
		return {
			countryName: "",
			IncludingVAT: "yes",
			textPrice: {},
			continentName: "",
		};
	},
	props: {
		isStockPage: {
			type: Number,
		},
		productInfo: {
			type: Object,
		},
		priceInfo: {
			type: Object,
		},
		halfCateDTO: { type: Object },
		isLuggagetags: {
			type: Boolean,
		},
		isCufflinks: {
			type: Boolean,
		},
		selectOne: {
			type: [Number, String],
		},
		noInquiry: {
			type: Boolean,
			default: false,
		},
	},
	computed: {
		isMobile() {
			return this.$store.state.device === "mb";
		},
		theme() {
			return this.$store.state.proTheme;
		},
		langSemiCustom() {
			return this.$store.getters.lang?.semiCustom;
		},
		langCart() {
			return this.$store.getters.lang?.cart;
		},
		langQuote() {
			return this.$store.getters.lang.quote || {};
		},
		langLogin() {
			return this.$store.getters.lang.login || {};
		},
		text2() {
			if (this.priceInfo.discount < 1) {
				return "-";
			} else if (this.priceInfo.discount == 1) {
				return "";
			} else {
				return "+";
			}
		},
		deliveryFee() {
			let totalPriceInt = Math.round(this.priceInfo.totalPrice * 100);
			let foundationUnitPriceInt = Math.round(this.priceInfo.foundationUnitPrice * 100);
			let toolingChargeInt = Math.round(this.priceInfo.toolingCharge * 100);
			let intermediateResult = totalPriceInt - foundationUnitPriceInt * this.priceInfo.totalQuantity - toolingChargeInt;
			return Math.abs(intermediateResult / 100).toFixed(2);
		},
		proId() {
			return this.$store.state.proId;
		},
		isFd() {
			return this.proId == 9;
		},
		showDes() {
			return !this.halfCateDTO.categoryType == 1 && !this.halfCateDTO.categoryType == 2;
		},
	},
	components: { freeTip, ToolTip },
	methods: {
		//欧洲国家文案
		getTax(type) {
			if (type == 1) {
				return "Including VAT";
			} else if (type == 2) {
				return "Subtotal incl. VAT";
			} else if (type == 3) {
				return "Subtotal excl. VAT";
			}
		},
		addCart(type) {
			this.$emit("addCart", type);
		},
		//税费开关开启，小计加上税费
		getTotalPrice(totalPrice) {
			if (this.IncludingVAT == "no" || this.continentName != "Europe") {
				this.$emit("totalPricee", totalPrice, this.IncludingVAT, this.countryName);
				return totalPrice;
			} else {
				let t = (Math.round(this.textPrice.rate * totalPrice * 100) / 100).toFixed(2);
				this.$emit("totalPricee", totalPrice + Number(t), this.IncludingVAT, this.countryName);
				return totalPrice + Number(t);
			}
		},
		addInquiryDialog() {
			this.$emit("openInquiryBox");
		},
	},
	mounted() {
		//提前算税费
		getTaxByPrice().then((res) => {
			this.textPrice = res.data;
			this.countryName = res.data?.res?.country?.names.en; //国家
			this.continentName = res.data?.res?.continent?.names.en; //洲
		});
	},
};
</script>

<template>
	<div class="subtotal">
		<div class="sub-detail">
			<div class="subtotal-left">
				<div class="sub-item">
					<div style="font-weight: bold; font-size: 18px; color: #333333">
						{{ langQuote.orderSummary }}
					</div>
				</div>
				<div class="sub-item">
					<div class="sub-item-left">{{ langSemiCustom.quantity }}:</div>
					<div class="sbu-item-right">
						{{ priceInfo.totalQuantity || 0 }}
						{{ productInfo.category.unit }}
					</div>
				</div>
				<div class="sub-item">
					<div class="sub-item-left">{{ langSemiCustom.unitPrice }}:</div>
					<div class="sbu-item-right">
						<CCYRate :price="priceInfo.foundationUnitPrice"></CCYRate>
					</div>
				</div>
				<div class="sub-item" v-if="productInfo.isDevise != 1">
					<div class="sub-item-left">{{ langSemiCustom.setupCharge }}:</div>
					<div class="sbu-item-right">
						<CCYRate :price="priceInfo.toolingCharge"></CCYRate>
					</div>
				</div>
				<!-- 减价不展示 小于1就是折扣，大于1就是加价 -->
				<!-- 新增减价展示 -->
				<div class="sub-item" v-show="Math.abs(priceInfo.deliveryFee) > 0">
					<div class="sub-item-left" v-show="priceInfo.discount >=1 ">{{ langSemiCustom.rushDelivery }}:</div>
					<div class="sub-item-left" v-show="priceInfo.discount < 1">{{ langSemiCustom.TurnaroundDiscount }}:</div>
					<div class="sbu-item-right">
						{{ text2 }}
						<CCYRate :price="Math.abs(priceInfo.deliveryFee)"></CCYRate>
						<span v-show="priceInfo.discount!==1">( {{ (Math.abs(1 - priceInfo.discount) * 100).toFixed(0) }}% {{ langSemiCustom.forUnitPrice }} )</span>
					</div>
				</div>
			</div>
			<div class="subtotal-right">
				<div class="totalPriceBox" v-if="continentName == 'Europe' && textPrice.rate > 0" style="display: flex; align-items: center; justify-content: center">
					<strong class="subTotalText" style="margin-right: 10px">{{ getTax(1) }}:</strong>
					<v-switch v-model="IncludingVAT" class="operation" hide-details false-value="no" true-value="yes" :label="IncludingVAT" inset></v-switch>
				</div>

				<div class="totalPriceBox">
					<strong v-if="theme == '10' || theme == '11'" class="totalPriceText">{{ langSemiCustom.totalPrice }}:</strong>

					<!-- 欧洲国家显示的文案 -->
					<strong v-if="!isStockPage && continentName == 'Europe'" class="totalPriceText"> {{ IncludingVAT == "yes" ? getTax(2) : getTax(3) }}: </strong>
					<!-- 美国显示的文案 -->
					<strong class="subTotalText" v-if="!isStockPage && continentName != 'Europe'">{{ langSemiCustom.subTotal }}: </strong>
					<CCYRate class="final-price" :price="getTotalPrice(priceInfo.totalPrice)"></CCYRate>
				</div>
				<div>
					<freeTip v-if="proId !== 446"></freeTip>
					<button type="button" id="inquiryBtnBox" class="checkOutBtn" :class="{ luggagetags: isLuggagetags }" @click="addInquiryDialog" v-if="isFd">
						{{ langQuote.submitInquiry }}
					</button>
					<button type="button" id="addCartBtn" class="checkOutBtn" :class="{ luggagetags: isLuggagetags }" @click="addCart" v-else-if="selectOne == 2">
						{{ langCart.checkOut }}
					</button>
					<div class="addCartBtnBox" v-else :class="{ luggagetags: isLuggagetags, isCufflinks: isCufflinks }">
						<button type="button" id="addCartBtn" @click="addCart" v-if="$route.query.orderNow == 1">
							{{ langSemiCustom.orderNow }}
						</button>
						<div class="buyNow item" @click="addCart('checkOut')" v-if="$route.query.orderNow != 1 && !isCufflinks">
							{{ langSemiCustom.buyNow }}
						</div>
						<div class="buyNow item" @click="addInquiryDialog" v-if="$route.query.orderNow != 1 && isCufflinks">{{ langSemiCustom.SubmitInquiry }}<ToolTip class="uploadTip2" :titleContent="langQuote.inquiryTip" v-show="isCufflinks"></ToolTip></div>
						<div class="addToCart item" @click="addCart('addAndNoCheckOut')" v-if="$route.query.orderNow != 1">{{ langSemiCustom.addToCart2 }}<ToolTip class="uploadTip2" :titleContent="langQuote.addCartTip" v-show="isCufflinks"></ToolTip></div>
					</div>
					<div class="inquiryBtnBox" v-show="!isFd && !noInquiry">
						<span style="color: #000">{{ langLogin.or + " " }}</span>
						<span class="inquiryBtn" title="custom product inquiry" @click="addInquiryDialog">
							{{ langSemiCustom.detailInquiry }}
						</span>
					</div>
				</div>
			</div>
		</div>
		<!-- medals coins 不展示 -->
		<div class="page-des" v-show="showDes" :style="{ textAlign: 'left' }">
			<div class="productionTime">
				{{ langSemiCustom.productionTime }}
			</div>
			<div class="lookingFor" style="margin-left: -4px">
				<span style="user-select: none; opacity: 0; visibility: hidden">*</span>
				{{ langSemiCustom.lookingFor }}
				<a :href="'mailto:' + $store.state.proSystem.email">{{ langSemiCustom.contactUs }}</a>
			</div>
		</div>
		<div class="teaAddCart" v-show="isStockPage && theme == '11'">
			<button type="button" primary @click="addCart('addCart')">
				{{ langSemiCustom.addCart }}
			</button>
			<div class="inquiryBtnBox" v-show="!noInquiry">
				<span style="color: #000">{{ langLogin.or + " " }}</span>
				<span class="inquiryBtn" title="custom product inquiry" @click="addInquiryDialog">
					{{ langSemiCustom.detailInquiry }}
				</span>
			</div>
		</div>
	</div>
</template>

<style scoped lang="scss">
::v-deep .v-input--selection-controls {
	margin-top: 0 !important;
	padding-top: 0 !important;
}

.subtotal {
	.sub-detail {
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		align-items: center;
		border-radius: 10px;
		padding: 31px 19px;
		background: #f6f6f6;
		grid-gap: 20px;

		.subtotal-left {
			.sub-item {
				display: flex;
				align-items: center;
				margin-bottom: 10px;

				&:last-child {
					margin-bottom: 0;
				}

				.sub-item-left {
					flex-shrink: 0;
					white-space: normal;
					width: 130px;
					margin-right: 38px;
				}
			}
		}

		.subtotal-right {
			text-align: center;

			.totalPriceBox {
				.subTotalText {
					font-weight: 400;
					color: #333333;
					line-height: 36px;
				}
			}

			button {
				width: 100%;
				height: 3em;
				border: none;
				color: #fff;
				outline: none;
				min-width: 14em;
				line-height: 3em;
				font-weight: bold;
				font-size: 1.125em;
				border-radius: 6px;
				background: $color-primary;
				text-transform: uppercase;

				&:hover {
					opacity: 0.9;
				}
			}

			.addCartBtnBox {
				display: flex;
				align-items: center;
				justify-content: center;
				cursor: pointer;
				height: 3em;
				border-radius: 6px;
				&.isCufflinks {
					.item {
						font-size: 0.9em;
					}
				}
				&.luggagetags {
					.addToCart {
						background: linear-gradient(90deg, #f8430d, #fea33a) !important;
					}
				}
				.item {
					width: 50%;
					height: 100%;
					font-weight: bold;
					font-size: 1.125em;
					display: flex;
					align-items: center;
					justify-content: center;
					overflow: hidden;
					line-height: initial;
				}
				.buyNow::v-deep {
					border-radius: 6px 0 0 6px;
					color: $color-primary;
					border: 1px solid $color-primary;
					border-right: none;
					background: #fff;
					.uploadTip2 {
						i {
							font-weight: 400;
							color: $color-primary !important;
						}
					}
				}
				.addToCart::v-deep {
					border-radius: 0 6px 6px 0;
					color: #fff;
					background: linear-gradient(90deg, $color-primary, var(--color-primary-opacity));
					.uploadTip2 {
						i {
							font-weight: 400;
							color: #fff !important;
						}
					}
				}
				@include respond-to(mb) {
				}
			}

			.final-price {
				margin: 0 10px;
				font-size: 1.5em;
				font-weight: bold;
				color: #e6252e;
			}

			.price {
				text-decoration: line-through;
			}

			.inquiryBtnBox {
				font-size: 14px;
				margin-top: 10px;

				.inquiryBtn {
					cursor: pointer;
					text-decoration: underline;
					color: $color-primary;
				}
			}
		}
	}

  @include respond-to(mb) {
        margin-bottom: 10px;
        background: #ebebeb;

		.sub-detail {
			border-radius: 4px;
			grid-template-columns: repeat(1, 1fr);
			grid-gap: 10px;
			padding: 20px 10px;

			.subtotal-left {
				.sub-item {
					margin-bottom: 10px;
					justify-content: space-between;

					.sub-item-left {
						margin-right: 10px;
					}
				}
			}

			.subtotal-right {
				.totalPriceBox {
					.subTotalText {
						font-weight: bold;
						font-size: 14px;
						color: #222222;
						line-height: 30px;
					}
				}

				button {
					height: 50px;
					border-radius: 4px;
					line-height: 50px;
					width: 75%;
					font-size: 14px;
				}

				.final-price {
					font-size: 18px;
				}

				.inquiryBtnBox {
					font-size: 12px;
				}
			}
		}
	}
}

.page-des {
	font-size: 14px;
	color: #999999;
	line-height: 24px;
	margin-top: 18px;
	text-align: right;

	.lookingFor {
		margin-top: 2px;

		a {
			text-decoration: underline;
		}
	}

	a {
		color: $color-primary;
	}

	@media screen and (max-width: $mb-width) {
		display: none;
		font-size: 12px;
		margin-top: 10px;
	}
}

[theme="10"] {
	.sub-detail {
		background-image: url("https://oss-static-cn.liyi.co/web/quoteManage/20230921/%E5%9B%BE%E5%B1%82_24_20230921pX6f5r.png"), url("https://oss-static-cn.liyi.co/web/quoteManage/20230921/%E5%9B%BE%E5%B1%82_25_2023092186enj4.png");
		background-position: left bottom, top right;
		background-repeat: no-repeat;
		background-color: $bg-primary !important;
		padding: 31px 19px 31px 84px;

		@include respond-to(mb) {
			padding: 20px 10px;
		}

		.sub-item-left {
			width: 90px !important;
		}

		.subtotal-right {
			.totalPriceText {
				font-size: 20px;
				color: #333;

				@include respond-to(mb) {
					font-size: 16px;
					color: $color-primary;
				}
			}

			.final-price {
				color: $color-primary !important;
				font-weight: bold;

				@include respond-to(mb) {
					font-size: 18px !important;
					font-weight: bold;
					color: $color-primary !important;
				}
			}

			button {
				background: linear-gradient(-23deg, $color-primary, $btn-primary) !important;
				box-shadow: 0px 2px 8px 0px $color-dark;
			}
		}
	}
}

[theme="11"] {
	.subtotal {
		background-color: transparent;
	}

	.sub-detail {
		background-color: #e3ebe5 !important;
		border-radius: 10px !important;
		margin-top: 0 !important;

		@include respond-to(mb) {
			grid-template-columns: repeat(2, 1fr) !important;

			.subtotal-left {
				.sub-item {
					.sub-item-left {
						width: 45px !important;
					}
				}
			}
		}

		.subtotal-left {
			.sub-item {
				.sub-item-left {
					width: 60px !important;
				}
			}
		}

		.sbu-item-right {
			font-weight: bold;
		}

		.subtotal-right {
			button {
				display: none;
			}

			.totalPriceBox {
				display: flex;
				align-items: center;
			}

			.final-price {
				font-size: 24px;
				font-weight: bold;
				font-family: Source Han Sans CN;
				color: $color-primary !important;

				@include respond-to(mb) {
					font-size: 18px !important;
				}
			}
		}
	}

	.page-des {
		display: none !important;
	}

	.teaAddCart {
		margin-top: 20px;
		display: flex;
		justify-content: flex-end;

		button {
			color: #fff;
			font-size: 20px;
			font-family: Source Han Sans CN;
			font-weight: 500;
		}

		.inquiryBtnBox {
			font-size: 14px;
			margin-top: 10px;

			.inquiryBtn {
				cursor: pointer;
				text-decoration: underline;
				color: $color-primary;
			}
		}

		@include respond-to(mb) {
			button {
				font-size: 14px;
			}

			.inquiryBtnBox {
				font-size: 12px;
			}
		}
	}
}

.luggagetags {
	//   background: linear-gradient(90deg, #f8430d, #fea33a) !important;
}

.checkOutBtn {
	font-size: 18px;

	@include respond-to(mb) {
		font-size: 14px;
	}
}

::v-deep .el-switch__label * {
	line-height: 1;
	font-size: 13px;
	display: inline-block;
}

::v-deep .el-switch__label {
	position: absolute;
	display: none;
	color: #fff !important;
	//   font-size: 13px !important;
}

::v-deep .el-switch__label--right {
	z-index: 1;
	right: 20px !important;
	margin-left: 0px;
}

::v-deep .el-switch__label--left {
	z-index: 1;
	left: 20px !important;
	margin-right: 0px;
}

::v-deep .el-switch__label.is-active {
	display: block;
}

::v-deep .el-switch .el-switch__core,
.el-switch .el-switch__label {
	width: 50px !important;
}
</style>
