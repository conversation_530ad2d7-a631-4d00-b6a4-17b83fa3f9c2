<template>
	<div class="litterTitle">
		<div class="title">
			<slot>Description</slot>
			<div class="borderBox"></div>
		</div>
		<span class="block"></span>
	</div>
</template>

<script>
export default {
	name: "litterTitle",
	components: {},
	data() {
		return {};
	},
	watch: {},
	computed: {},
	methods: {},
	created() {},
	mounted() {},
};
</script>

<style scoped lang="scss">
.litterTitle * {
	font-family: Calibri;
}
.litterTitle {
	display: flex;
	align-items: flex-end;
	justify-content: space-between;
	column-gap: 10px;
	color: #333333;
	font-family: Calibri;
	.title {
		flex: 1;
		display: flex;
		row-gap: 4px;
		flex-direction: column;
		.borderBox {
			width: 100%;
			height: 3px;
			// background: #fbcd64;
			border-bottom: 1px solid #dfdfdf;
			position: relative;
			&::before {
				content: "";
				position: absolute;
				left: 0;
				width: 60px;
				height: 3px;
				background: #fbcd64;
			}
		}
	}
	.block {
		flex-shrink: 0;
		width: 20px;
		height: 16px;
		aspect-ratio: 1/1;
		position: relative;
		margin-right: 10px;
		&::after,
		&::before {
			content: "";
			position: absolute;
			width: 40%;
			height: 100%;
			background: #fbcd64;
			transform: skew(-24deg);
		}
		&::before {
			left: 0;
		}
		&::after {
			right: 0;
		}
	}
}
</style>
