<template>
	<div class="mb-4" :class="stepData.styleName">
	    <slot name="stepText"></slot>
		<div class="step-content">
			<div class="step-item" :class="{ active: index === selectIndex }" v-for="(step, index) in stepData.productParamList" :key="index" @click="selectStep(step, index)">
				<div class="imgWrap">
					<img :src="getImg(step)" :alt="step.colorAlias" :title="step.colorAlias" />
				</div>
				<div class="text-truncate name">{{ theme == 10 || theme == 11 ? step.valueName : step.colorAlias }}</div>

				<half-design-check-icon2 class="absolute-topOut-right" v-if="theme == 10 || theme == 11" iconStyle="noBack" iconName="icon-xuanzhong" iconSize="medium"> </half-design-check-icon2>
				<half-design-check-icon class="absolute-top-right" v-else></half-design-check-icon>
			</div>
		</div>
		<div class="errorTip">
			<v-alert dense outlined type="error">
				{{ langSemiCustom.errorTip }}
			</v-alert>
		</div>
	</div>
</template>
<script>
export default {
	props: {
		stepData: {
			type: Object,
			default: {},
		},
	},
	data() {
		return {
			selectIndex: -1,
			selectItem: null,
			theme: 0,
		};
	},
	computed: {
		langSemiCustom() {
			return this.$store.getters.lang?.semiCustom;
		},
	},
	methods: {
		selectStep(item, index, state = false) {
			this.selectIndex = index;
			this.selectItem = item;
			this.$emit("selectStep", {
				type: this.stepData.attributeFlag,
				data: item,
				parent: this.stepData,
				id: this.stepData.id,
				firstSelect: state,
				colorIndex: index,
			});
		},
		getImg(item) {
			if (this.theme == 10 || this.theme == 11) {
				return item.imgDetail;
			}
			if (!item.imgJson) {
				return "";
			}
			return JSON.parse(item.imgJson)[0].url;
		},
		selectDefault() {
			if (this.selectIndex <= -1) {
				this.selectStep(this.stepData.productParamList[0], 0, true);
			}
		},
		setActiveProductColor() {
			let newColorList = this.stepData.productParamList.filter((color) => {
				return color.isActivity == 1;
			});
			if (newColorList.length == 0) {
				return;
			}
			this.stepData.productParamList = newColorList;
		},
	},
	mounted() {
        try {
            const element = document.querySelector("#modalHeader");
            this.theme = element.getAttribute("theme");
        } catch (e) {
        }
		this.$Bus.$on("selectDefaultColorStep", this.selectDefault);
		this.$Bus.$on("setActiveProductColor", this.setActiveProductColor);
	},
	beforeDestroy() {
		this.$Bus.$off("selectDefaultColorStep", this.selectDefault);
		this.$Bus.$off("setActiveProductColor", this.setActiveProductColor);
	},
};
</script>
<style scoped lang="scss">
@import "~assets/css/half.scss";
[theme="10"],
[theme="11"] {
	.style1 .step-content {
		grid-template-columns: repeat(4, 1fr);
		grid-gap: 12px;
	}
	.style1 .step-content .step-item.active {
		@include respond-to(mb) {
			padding: 2px 4px;
		}
	}
	.style1 .step-content .step-item {
		.imgWrap {
			width: 100%;
			img {
				object-fit: cover;
			}
			@include respond-to(mb) {
				height: 36px;
				img {
					object-fit: cover;
				}
			}
		}
		@include respond-to(mb) {
			padding: 2px 4px;
		}
	}
}
.style1 .step-content {
	display: grid;
	grid-template-columns: repeat(auto-fill, 100px);
	grid-gap: 10px;

	.step-item {
		min-width: 0;
		position: relative;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		@include step-default;
		cursor: pointer;

		.imgWrap {
			display: flex;
			justify-content: center;
			align-items: center;
			height: 60px;

			img {
				width: 100%;
				height: 100%;
				object-fit: contain;
			}
		}

		.checkIcon {
			display: none;
		}

		& > .name {
			width: 100%;
			margin-top: 4px;
			text-align: center;
		}
	}

	.step-item.active {
		.checkIcon {
			display: flex;
		}
	}
}

@include respond-to(mb) {
	.style1 .step-content {
		grid-template-columns: repeat(4, 1fr);
		grid-gap: 5px;
	}
}
</style>
