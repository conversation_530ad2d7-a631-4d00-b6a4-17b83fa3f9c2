<template>
	<article class="quoteNav" v-if="this.cateList.length">
		<p class="title">{{ title }}</p>
		<div class="mySwiper1">
			<div class="swiper" ref="mySwiper1">
				<div class="swiper-wrapper">
					<div class="swiper-slide" v-for="item in cateList" :key="item.id" :disabled="item.id === pid ? true : false">
						<div class="quoteCard" :class="[item.id === pid ? 'cardActiveMB' : '', noPreview ? '' : 'preview-nav']" @click="linkTo(item)">
							<div class="card-box">
								<img :src="item.recommendPhoto"  alt="" :class="item.id === pid ? 'imgActive' : ''"/>
								<div class="stitle" :class="item.id === pid ? 'stitleActive' : ''">
									<strong>{{ item.cateName }}</strong>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="swiper-button-prev pagingIcon" slot="button-prev"></div>
				<div class="swiper-button-next pagingIcon" slot="button-next"></div>
			</div>
		</div>
	</article>
</template>
<script>
import { medalsApi } from "@/api/medals/medals";

export default {
	data() {
		return {
			cateList: [],
			swiper1: null,
		};
	},
	props: {
		pid: {
			type: [Number, String],
		},
		title: {
			type: String,
		},
		customData: {
			type: Object,
			default: () => {
				return {};
			},
		},
		noPreview: {
			type: Boolean,
			default: false,
		},
		config: {
			type: Object,
			default: () => {},
		},
	},
	computed: {
		lang() {
			return this.$store.getters.lang.quote || {};
		},
		proId() {
			return this.$store.state.proId;
		},
		device() {
			return this.$store.state.device;
		},
		isMobile() {
			return this.$store.getters.isMobile;
		},
	},
	methods: {
		linkTo(val) {
			if (val.id == "-1") {
				window.open(val.customHref);
				return;
			}
			this.$router.push({
				path: val.quoteRoutingName,
				query: this.$route.query,
			});
		},
		getAppRecommendCateList() {
			medalsApi.getAppRecommendCateList({ proId: this.proId, id: this.pid }).then((res) => {
				this.cateList = res.data;
				if (Object.keys(this.customData).length > 0) {
					this.cateList.push(this.customData);
				}
				this.swiperInit();
			});
		},
		swiperInit() {
			this.$nextTick(() => {
				this.swiper1 = new Swiper(this.$refs.mySwiper1, {
					slidesPerView: 8,
					spaceBetween: 6,
					slidesPerGroup: 3,
					loop: false,
					loopFillGroupWithBlank: true,
					navigation: {
						nextEl: ".mySwiper1 .swiper-button-next",
						prevEl: ".mySwiper1 .swiper-button-prev",
					},
					...this.config,
					breakpoints: {
						1000: {
							slidesPerView: this.config?.slidesPerView || 8,
						},
						300: {
							slidesPerView: 3,
						},
					},
				});
			});
		},
	},
	mounted() {
		this.getAppRecommendCateList();
	},
};
</script>
<style lang="scss" scoped>
[disabled] {
	pointer-events: none;

	button {
		opacity: 0.5;
	}
}

.stitleActive {
	color: #537cf2;
}

.cardActiveMB {
	text-align: center;
	position: relative;
}
.imgActive{
    border:2px solid $color-primary !important;
	border-radius: 10px;
}

.quoteNav {
	grid-column: 2/49;
	padding-bottom: 20px;
	font-family: Calibri;

	.quoteCard {
		position: relative;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		cursor: pointer;

		.stitle {
			font-weight: bold;
			@include respond-to(mb) {
				text-align: center;
                font-size: 12px;
			}
		}

		.card-box {
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
		}

		.card-hover {
			display: none;
		}

		img {
			width: 100% !important;
			min-height: 146px;
			border: 2px solid transparent;
			padding: 2px;
			object-fit: contain;
            @include respond-to(mb){
                min-height: auto;
            }
		}

		button {
			border: 1px solid $color-primary;
			color: #333333;
			padding: 5px 35px;
			background: #f2f5f7;
			@media screen and (max-width: 767px) {
				font-size: 12px;
				padding: 5px 15px;
			}
		}

		@media (any-hover: hover) {
			&.preview-nav:hover {
				.card-hover {
					position: absolute;
					inset: 0;
					display: block;
				}

				button {
					color: white;
					border-color: transparent;
					border-radius: 6px;
					background: $color-primary;
				}
			}
		}
	}

	.title {
		margin-bottom: 20px;
		font-size: 20px;
		font-weight: bold;
		text-align: center;
		position: relative;

        @include respond-to(mb){
            font-size: 16px;
        }

		&::after {
			background-color: #333;
			bottom: -10px;
			content: "";
			height: 2px;
			position: absolute;
			width: 124px;
			left: 50%;
			transform: translate(-50%, 0);
		}
	}

	.mySwiper1 {
		position: relative;
		padding: 0 65px;
		@include respond-to(mb) {
			padding: 0 5px;
		}
	}

	.pagingIcon {
		z-index: 10;
		width: 3.5em;
		height: 3.5em;
		line-height: 3.5em;
		text-align: center;
		background: #ffffff;
		box-shadow: 0 2px 6px 0 rgba(23, 29, 67, 0.3);
		border-radius: 50%;
		border: 1px solid #dbdbdb;
		position: absolute;
		top: 40%;
		color: #000;
		@include respond-to(mb) {
			width: 24px;
			height: 24px;
			z-index: 10;
			cursor: pointer;
			display: flex;
			align-items: center;
			justify-content: center;
			color: #ffffff;
			background-color: rgba(0, 0, 0, 0.5);
			box-shadow: none;
			border: none;
			padding: 2px;
		}
	}

	.pagingIcon::after {
		font-size: 1.5em;
		font-weight: 800;
		@include respond-to(mb) {
			font-size: 1em;
		}
	}

	.swiper-button-prev {
	}
}
</style>
