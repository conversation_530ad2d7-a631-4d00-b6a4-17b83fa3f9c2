<template>
	<v-app>
		<div class="bps-container py-2 py-md-10" :style="proId == 444 ? 'background-color: #f2eee4;' : 'background-color: #fff;'">
			<div style="font-size: 14px" class="containerTitle">{{ langCart.position }}</div>
			<template v-if="cartList.length">
				<div class="containertext" :style="proId == 444 ? 'background-color: #f2eee4;' : 'background-color: #fff;'">
					<div class="title" flex>
						<h1 class="text-body-2 font-weight-bold text-lg-h5 text-uppercase containerTitle">{{ langCart.title }}</h1>
						<div class="title_switch" v-if="openDesignerActivity">
							<span @click="switchDesignerShoppingCart">Switch</span>
							<v-tooltip top color="#FFFFFF" class="tooltip-custom">
								<template v-slot:activator="{ on, attrs }">
									<span class="wenhao">
										<b class="icon-wenhao" v-bind="attrs" v-on="on"></b>
									</span>
								</template>
								<span style="color: black" class="wenhaoText" v-html="langCart.wenhaoText" />
							</v-tooltip>
						</div>
					</div>
					<div class="d-flex justify-space-between align-center top">
						<div class="d-flex align-items-center py-4">
							<v-checkbox class="mt-0 pt-0" v-model="selectAll" hide-details @change="changeStatus"></v-checkbox>
							<span>{{ langCart.items[0] }}</span>
							<span class="pl-2" v-if="selectItemNum > 0">({{ selectItemNum }} {{ langCart.items[1] }})</span>
						</div>
						<div>
							<v-btn icon v-for="(item, index) in btnList" :key="index" :class="isbtn == index ? 'buJu2' : 'buJu1'" @click="typeClick(item)">
								<b :class="item.name"></b>
							</v-btn>
							<v-btn icon color="#B3B3B3" @click="delCart(selectCartList)">
								<v-icon>mdi-trash-can-outline</v-icon>
							</v-btn>
						</div>
					</div>
					<div class="py-3" :class="{ 'dark-theme': isActive }">
						<div style="position: relative" v-for="(item, index1) in cartList" :key="item.id">
							<div class="topName" :style="{ cursor: item.buyType == 9 ? 'pointer' : '' }" v-if="isActive" @click="toProDetail(item.quoteCateChildId, item.buyType)">
								<strong class="mr-2">{{ item.cateName }}</strong>
								<v-btn color="#0066cc" style="color: #ffffff" small @click.stop="editCart(item)" v-if="item.updateCart && !item.isFastQuote && !item.isBannerQuote">
									<v-icon small>mdi-square-edit-outline </v-icon>
									Edit
								</v-btn>
							</div>
							<div class="cart-item" :class="{ grayBack: item.isStock == 1, center: isbtn === 0 }">
								<div class="item-p t1" :class="{ pb: item.imageJson && item.imageJson.length > 0 && !item.isBlank }">
									<!--									<div class="t"></div>-->
									<div class="c check">
										<v-checkbox :disabled="item.isStock == 1" class="mt-0 pt-0 c_checkbox" v-model="item.select" @change="changeSelect(item, index1)" hide-details></v-checkbox>
									</div>
								</div>
								<div class="item-p t2" style="text-align: center">
									<div class="t img-cateName" style="line-height: 20px; cursor: pointer; height: inherit; cursor: pointer">
										<!-- <template v-if="item.buyType === 7">
                                                {{ item.cateName }}
                                            </template> -->
										<template v-if="item.buyType === 8">
											<div class="t_check">
												<v-checkbox :disabled="item.isStock == 1" class="mt-0 pt-0 c_checkbox" v-model="item.select" @change="changeSelect(item, index1)" hide-details></v-checkbox>
												<span @click="toProDetail(item.quoteCateChildId, item.buyType)">{{ item.productName }}</span>
											</div>
										</template>
										<template v-else>
											<div class="t_check">
												<v-checkbox :disabled="item.isStock == 1" class="mt-0 pt-0 c_checkbox" v-model="item.select" @change="changeSelect(item, index1)" hide-details></v-checkbox>
												<span @click="toProDetail(item.quoteCateChildId, item.buyType)">{{ item.cateName }}</span>
											</div>
										</template>
									</div>
									<DraftUpload :uploadId="index1" :dataList="item.imageJson ? JSON.parse(item.imageJson) : []" @updateCartList="updateCartList($event, index1)" :propsData="item.propsData" :listType="isbtn" :index="index1"></DraftUpload>
								</div>

								<div class="item-p t3" v-show="!isActive">
									<div :class="item.viewMore ? '' : 'max-height'">
										<div class="t mb-2" :style="{ cursor: item.buyType == 9 ? 'pointer' : '' }" @click="toProDetail(item.quoteCateChildId, item.buyType)">
											<div style="display: flex; align-items: center">
												<strong class="mr-2">{{ item.cateName }}</strong>
												<v-btn color="#0066cc" style="color: #ffffff" small @click.stop="editCart(item)" v-if="item.updateCart && !item.isFastQuote && !item.isBannerQuote">
													<v-icon small>mdi-square-edit-outline </v-icon>
													Edit
												</v-btn>
											</div>
											<div class="mt-1" v-if="item.buyType == 9">{{ langCart.itemNo }}.: {{ langCart.item }}{{ item.sku }}</div>
										</div>
										<div class="c">
											<div class="d-flex mb-2 param-item" v-for="(item, key, index2) in item.params" :key="index2">
												<div class="label">{{ key }}:</div>
												<template v-if="item.length > 0 && item[0].img">
													<div class="con">
														<div class="pic-item" v-for="(citem, index3) in item">
															<span>{{ langCart.file }}{{ index3 + 1 }}:</span>
															<img :src="citem.img" alt="" @click="zoomPic(citem.img)" v-if="isImageType(citem.img)" />
															<a href="javascript:;" :key="index3" @click="zoomPic(citem.img)" v-else>
																{{ citem.img }}
															</a>
														</div>
													</div>
												</template>
												<template v-else>
													<div class="con">
														<template v-for="(citem, index4) in item">
															<a v-if="item[0].isEmailLater" :href="`mailto:${siteEmail}`" style="color: #4280d3; text-decoration: underline">{{ citem.paramName }}</a>
															<span v-else :key="index4"> {{ citem.paramName }} </span>
															<br />
														</template>
													</div>
												</template>
											</div>
											<div class="d-flex param-item" v-if="item.viewMore">
												<div class="label">{{ langCart.comments }}:</div>
												<div class="comments-textarea">
													<v-textarea class="body-2" v-model="item.comments" outlined rows="4" hide-details @change="changeComments($event, item)">
														<template v-slot:prepend-inner>
															<v-icon size="14">mdi-pencil</v-icon>
														</template>
													</v-textarea>
												</div>
											</div>
										</div>
									</div>
									<div class="viewMore" v-show="showButton">
										<span @click="item.viewMore = !item.viewMore">
											{{ item.viewMore ? langTips.ViewLess : langTips.ViewMore }}
											<b class="icon-right"></b>
										</span>
									</div>
								</div>

								<div class="item-p t4" :class="{ rush: item.discount < 0 }">
									<div class="showMb">
										<div class="cateName img-cateName2">
											{{ item.cateName }}
										</div>
										<div class="cateName_div">
											<span>{{ langCart.unitPrice }}: </span>
											<div>
												<!-- item.unitPrice  是折前单价 -->
												<span>
													<CCYRate v-if="item.discount > 0" class="originUnitPrice" :class="{ 'text-decoration-line-through': item.discount }" :price="item.unitPrice"></CCYRate>
													<CCYRate v-if="item.discount >= 0" class="discountUnitPrice" :price="item.unitPrice * (1 - item.discount / 100)"></CCYRate>
													<CCYRate v-if="item.discount < 0" class="discountUnitPrice" :price="item.unitPrice"></CCYRate>
												</span>
												<div class="discount" v-if="item.discount > 0">(-{{ Number(item.discount) }}{{ langCart.percent }})</div>
											</div>
										</div>
										<!--											v-show="$store.getters.isMobile"-->
										<div v-if="$store.getters.isMobile && !item.isRadioQuantity && item.quantityList.length > 1 && isbtn !== 2" class="cateName_div" @click="debounceChangeQty(1, item, index1, null)">
											<!--												<v-text-field readonly v-model="item.quantity" type="number" dense solo hide-details append-icon="mdi-chevron-down"></v-text-field>-->
											<v-menu v-model="menuStates[index1]" @input="handleMenuOpen($event, item, index1)" auto bottom :offset-y="offset" :close-on-content-click="false" :ref="'qtyMenu' + index1">
												<template v-slot:activator="{ on, attrs }">
													<div v-bind="attrs" v-on="on" class="d-flex-center justify-space-between" style="width: 100%">
														<span>{{ langCart.quantity }}:</span>
														<div class="c d-flex-center">
															<!--																<span>{{ item.quantity }}</span>-->
															<v-text-field height="27" readonly v-model="item.quantity" :value="item.quantity" oninput="if(value.length>6)value=value.slice(0,6)" type="number" dense solo hide-details outlined>
																<template v-slot:append>
																	<v-icon size="14">mdi-chevron-down</v-icon>
																</template>
															</v-text-field>
															<span class="freeText" v-if="item.isFullReductionActivity && item.quantity >= item.satisfiedQuantity && item.giftQuantityTotal">+{{ item.giftQuantityTotal }} {{ langCart.free1 }}</span>
														</div>
													</div>
												</template>

												<v-card class="qty-card m-3" :class="{ giftWidth: item.isFullReductionActivity }" :style="mutipelType && (mutipelType == 3 || mutipelType == 4) ? 'width:460px' : 'width: 375px'">
													<strong class="text-left">{{ langCart.editQty }}:</strong>
													<div v-for="(ele, i) in item.quantityList" class="qty-list d-flex align-items-center mt-2">
														<label>{{ ele.paramName }}: </label>
														<!--													{{ele.quantity}}-->
														<div class="qty gift-input" :class="{ unvalid: qtyUnvalid }">
															<v-text-field v-model="ele.quantity" :value="ele.quantity" oninput="if(value.length>6)value=value.slice(0,6)" type="number" dense solo hide-details @change="changeQtyItem(item, ele)" outlined>
																<template v-slot:prepend-inner>
																	<v-icon size="14">mdi-pencil</v-icon>
																</template>
															</v-text-field>
														</div>
														<div v-if="mutipelType == 3 || mutipelType == 4" class="d-flex-center gift-input gift" :class="{ unvalid: giftUnvalid }">
															&nbsp+ {{ langCart.free }}:&nbsp
															<v-text-field v-model="ele.giftQuantity" :value="ele.giftQuantity" oninput="if(value.length>6)value=value.slice(0,6)" type="number" dense solo hide-details placeholder="giveaway" @change="changeItemGiftQty(item, ele)" outlined>
																<template v-slot:prepend-inner>
																	<v-icon size="14">mdi-pencil</v-icon>
																</template>
															</v-text-field>
														</div>
														<div class="ml-2 unit" :class="{ hasGift: mutipelType !== 3 && mutipelType !== 4 }">
															{{ ele.quantityUnit }}
														</div>
													</div>
													<div class="mt-2">
														<div v-if="qtyUnvalid" style="color: #f01e1e">{{ langCart.qtyUnvalid[0] }}{{ changeItem.satisfiedQuantity }}{{ langCart.qtyUnvalid[1] }}{{ changeItem.giftQuantity }}{{ langCart.qtyUnvalid[2] }}</div>
														<span v-if="mutipelType == 1" style="color: #f01e1e"> {{ langCart.reminder[0] }}{{ changeItem.satisfiedQuantity }}{{ langCart.reminder[1] }}{{ changeItem.giftQuantityTotal }}{{ langCart.reminder[2] }} </span>
														<span v-if="mutipelType == 2" style="color: #29bc75"> {{ langCart.congratulations[0] }}{{ changeItem.satisfiedQuantity }}{{ langCart.congratulations[1] }}{{ changeItem.giftQuantityTotal }}{{ langCart.congratulations[2] }}</span>
														<span v-if="mutipelType == 3" style="color: #29bc75"> {{ langCart.congratulations[0] }}{{ changeItem.satisfiedQuantity }}{{ langCart.congratulations[1] }}{{ changeItem.giftQuantity }}{{ langCart.congratulations[2] }}{{ langCart.congratulations[3] }} </span>
														<div v-if="giftUnvalid" style="color: #f01e1e">{{ langCart.exceeds[0] }}{{ changeItem.giftQuantity }}{{ langCart.exceeds[1] }}</div>
													</div>
													<div v-if="!mutipelType" class="d-flex-center mt-3">
														<v-btn width="60" small outlined depressed style="color: #3d3d3d; border: 1px solid #ababab" @click="closeMenu(item, index1)">
															{{ langDesign.cancel }}
														</v-btn>
														<v-btn color="#2D81D5" width="60" small dark depressed class="ml-5" @click="debounceChangeQty(1, item, index1)">
															{{ langDesign.ok }}
														</v-btn>
													</div>
													<div v-if="mutipelType == 1 || mutipelType == 2" class="d-flex-center mt-3">
														<v-btn width="60" small outlined depressed style="color: #3d3d3d; border: 1px solid #ababab" @click="closeMenu(item, index1)">
															{{ langDesign.cancel }}
														</v-btn>
														<v-btn color="#2D81D5" width="60" small dark depressed class="ml-5" @click="toChangeQty">
															{{ langDesign.yes }}
														</v-btn>
													</div>
													<div v-if="mutipelType == 3 || mutipelType == 4" class="d-flex-center mt-3">
														<v-btn width="120" small outlined depressed style="color: #3d3d3d; border: 1px solid #ababab" @click="closeMenu(item, index1, 1)">
															{{ langCart.freeDialogText[7] }}
														</v-btn>
														<v-btn :disabled="qtyUnvalid || giftUnvalid" color="#2D81D5" small depressed width="120" class="ml-5 white--text" @click="submitCartItem(item)">
															{{ langCart.freeDialogText[8] }}
														</v-btn>
													</div>
												</v-card>
											</v-menu>
										</div>
										<!--											 多选但只有一条数据时按照单选显示-->
										<div v-if="(item.isRadioQuantity && isbtn !== 2) || (!item.isRadioQuantity && item.quantityList.length == 1 && isbtn !== 2)" class="cateName_div">
											<span>{{ langCart.quantity }}:</span>
											<div class="c">
												<!--													<span>{{ item.quantity }}</span>-->

												<v-text-field v-model="item.quantityEdit" :value="item.quantityEdit" oninput="if(value.length>6)value=value.slice(0,6)" type="number" dense solo hide-details @change="debounceChangeQty(0, item, index1)" outlined>
													<template v-slot:prepend-inner>
														<v-icon size="14">mdi-pencil</v-icon>
													</template>
												</v-text-field>
											</div>
										</div>
										<div v-if="isbtn == 2" class="cateName_div">
											<span>{{ langCart.quantity }}:</span>
											<div class="c">
												<span>{{ item.quantity }}</span>
											</div>
										</div>
										<div class="cateName_div">
											<span>{{ langCart.modelPrice }}:</span>
											<div class="c">
												<CCYRate :price="item.mouldPrice"></CCYRate>
											</div>
										</div>
										<div class="cateName_div">
											<span>
												{{ langCart.semiModelPrice }}
											</span>
											<div class="c">
												<CCYRate :price="item.setupCharge"></CCYRate>
											</div>
										</div>
										<div class="cateName_div" v-if="showRushFee">
											<span>{{ langCart.deliveryFee }}:</span>
											<div class="c" v-if="item.rushFee > 0">
												<CCYRate :price="item.rushFee"></CCYRate>
											</div>
											<div class="c" v-else-if="item.discount < 0">
												<CCYRate :price="item.totalPrice - item.unitPrice * item.quantity - item.mouldPrice - item.setupCharge"></CCYRate>
												<!-- ${{ (item.totalPrice - item.unitPrice * item.quantity - item.mouldPrice).toFixed(2) }} -->
												<div>(+{{ -Number(item.discount) }}% {{ langCart.t }})</div>
											</div>
											<div class="c" v-else>
												<CCYRate :price="0"></CCYRate>
											</div>
										</div>
										<div class="cateName_div">
											<span>{{ langCart.subtotal }}:</span>
											<div class="c">
												<CCYRate class="totalPrice" :price="item.totalPrice"></CCYRate>
											</div>
										</div>
									</div>
									<div class="showPc">
										<div class="t">
											{{ langCart.unitPrice }}
										</div>
										<!-- item.unitPrice  是折前单价 -->
										<div class="c">
											<span>
												<!--													折扣大于零为减价的时候才显示划线价格 只有加价才显示rush Fee-->
												<CCYRate v-if="item.discount > 0" class="originUnitPrice" :class="{ 'text-decoration-line-through': item.discount }" :price="item.unitPrice"></CCYRate>
												<CCYRate v-if="item.discount >= 0" class="discountUnitPrice" :price="item.unitPrice * (1 - item.discount / 100)"></CCYRate>
												<CCYRate v-if="item.discount < 0" class="discountUnitPrice" :price="item.unitPrice"></CCYRate>
											</span>
											<div class="discount" v-if="item.discount > 0">(-{{ Number(item.discount) }}{{ langCart.percent }})</div>
										</div>
									</div>
								</div>
								<div class="item-p t6">
									<div class="t">
										{{ langCart.quantity }}
									</div>
									<div v-if="isbtn == 2" class="c d-flex" style="justify-content: center">
										<span>{{ item.quantity }}</span>
										<span class="freeText" v-if="item.isFullReductionActivity && item.quantity >= item.satisfiedQuantity && item.giftQuantityTotal">+{{ item.giftQuantityTotal }} {{ langCart.free1 }}</span>
									</div>
									<div v-else class="c d-flex" style="justify-content: center">
										<!--											多选-->
										<div v-if="!$store.getters.isMobile && !item.isRadioQuantity && item.quantityList.length > 1" @click="debounceChangeQty(1, item, index1)">
											<!--												<v-text-field readonly v-model="item.quantity" type="number" dense solo hide-details append-icon="mdi-chevron-down"></v-text-field>-->
											<v-menu content-class="qtyMenuContent" v-model="menuStates[index1]" @input="handleMenuOpen($event, item, index1)" auto bottom :offset-y="offset" :close-on-content-click="false" :ref="'qtyMenu' + index1">
												<template v-slot:activator="{ on, attrs }">
													<v-row v-bind="attrs" v-on="on" style="height: 45px; margin: 0">
														<!--															限制再打输入六位-->
														<v-text-field readonly v-model="item.quantity" :value="item.quantity" oninput="if(value.length>6)value=value.slice(0,6)" type="number" dense solo hide-details outlined>
															<template v-slot:append>
																<v-icon size="14">mdi-chevron-down</v-icon>
															</template>
														</v-text-field>
													</v-row>
												</template>
												<v-card class="qty-card m-3" :class="{ giftWidth: item.isFullReductionActivity }" :style="mutipelType && (mutipelType == 3 || mutipelType == 4) ? 'width:460px' : 'width: 375px'">
													<strong class="text-left">{{ langCart.editQty }}:</strong>
													<div v-for="(ele, i) in item.quantityList" class="qty-list d-flex align-items-center mt-2">
														<label>{{ ele.paramName }}: </label>
														<div class="qty gift-input" :class="{ unvalid: qtyUnvalid }">
															<v-text-field outlined v-model="ele.quantity" :value="ele.quantity" oninput="if(value.length>6)value=value.slice(0,6)" type="number" dense solo hide-details @change="changeQtyItem(item, ele)">
																<template v-slot:prepend-inner>
																	<v-icon size="14">mdi-pencil</v-icon>
																</template>
															</v-text-field>
														</div>
														<div v-if="mutipelType == 3 || mutipelType == 4" class="d-flex-center gift-input gift" :class="{ unvalid: giftUnvalid }">
															&nbsp;+ {{ langCart.free }}:&nbsp;
															<v-text-field outlined v-model="ele.giftQuantity" :value="ele.giftQuantity" oninput="if(value.length>6)value=value.slice(0,6)" type="number" dense solo hide-details placeholder="giveaway" @change="changeItemGiftQty(item, ele)">
																<template v-slot:prepend-inner>
																	<v-icon size="14">mdi-pencil</v-icon>
																</template>
															</v-text-field>
														</div>
														<div class="unit" :class="{ hasGift: mutipelType !== 3 && mutipelType !== 4 }">
															{{ ele.quantityUnit }}
														</div>
													</div>
													<div class="mt-2">
														<div v-if="qtyUnvalid" style="color: #f01e1e">{{ langCart.qtyUnvalid[0] }}{{ changeItem.satisfiedQuantity }}{{ langCart.qtyUnvalid[1] }}{{ changeItem.giftQuantity }}{{ langCart.qtyUnvalid[2] }}</div>
														<span v-if="mutipelType == 1" style="color: #f01e1e"> {{ langCart.reminder[0] }}{{ changeItem.satisfiedQuantity }}{{ langCart.reminder[1] }}{{ changeItem.giftQuantityTotal }}{{ langCart.reminder[2] }} </span>
														<span v-if="mutipelType == 2" style="color: #29bc75"> {{ langCart.congratulations[0] }}{{ changeItem.satisfiedQuantity }}{{ langCart.congratulations[1] }}{{ changeItem.giftQuantityTotal }}{{ langCart.congratulations[2] }}</span>
														<span v-if="mutipelType == 3" style="color: #29bc75"> {{ langCart.congratulations[0] }}{{ changeItem.satisfiedQuantity }}{{ langCart.congratulations[1] }}{{ changeItem.giftQuantity }}{{ langCart.congratulations[2] }}{{ langCart.congratulations[3] }} </span>
														<div v-if="giftUnvalid" style="color: #f01e1e">{{ langCart.exceeds[0] }}{{ changeItem.giftQuantity }}{{ langCart.exceeds[1] }}</div>
													</div>
													<div v-if="!mutipelType" class="d-flex-center mt-3">
														<v-btn width="60" small outlined depressed style="color: #3d3d3d; border: 1px solid #ababab" @click="closeMenu(item, index1)">
															{{ langDesign.cancel }}
														</v-btn>
														<v-btn color="#2D81D5" width="60" small dark depressed class="ml-5" @click="debounceChangeQty(1, item, index1)">
															{{ langDesign.ok }}
														</v-btn>
													</div>
													<div v-if="mutipelType == 1 || mutipelType == 2" class="d-flex-center mt-3">
														<v-btn width="60" small outlined depressed style="color: #3d3d3d; border: 1px solid #ababab" @click="closeMenu(item, index1)">
															{{ langDesign.cancel }}
														</v-btn>
														<v-btn color="#2D81D5" width="60" small dark depressed class="ml-5" @click="toChangeQty">
															{{ langDesign.yes }}
														</v-btn>
													</div>
													<div v-if="mutipelType == 3 || mutipelType == 4" class="d-flex-center mt-3">
														<v-btn width="120" small outlined depressed style="color: #3d3d3d; border: 1px solid #ababab" @click="closeMenu(item, index1, 1)">
															{{ langCart.freeDialogText[7] }}
														</v-btn>
														<v-btn :disabled="qtyUnvalid || giftUnvalid" color="#2D81D5" small depressed width="120" class="ml-5 white--text" @click="submitCartItem(item)">
															{{ langCart.freeDialogText[8] }}
														</v-btn>
													</div>
												</v-card>
											</v-menu>
										</div>
										<!--											单选-->

										<v-text-field v-else v-model="item.quantityEdit" :value="item.quantityEdit" oninput="if(value.length>6)value=value.slice(0,6)" type="number" dense solo hide-details outlined @change="debounceChangeQty(0, item, index1)">
											<template v-slot:prepend-inner>
												<v-icon size="14">mdi-pencil</v-icon>
											</template>
										</v-text-field>
										<span class="freeText1" v-if="item.isFullReductionActivity && item.quantity >= item.satisfiedQuantity && item.giftQuantityTotal">+{{ item.giftQuantityTotal }} {{ langCart.free1 }}</span>
									</div>
								</div>
								<div class="item-p t5" :class="{ tDark: isbtn == 2 }">
									<div class="t">
										{{ langCart.modelPrice }}
									</div>
									<div class="c">
										<CCYRate :price="item.mouldPrice"></CCYRate>
									</div>
								</div>
								<div class="item-p t5" :class="{ tDark: isbtn == 2 }">
									<div class="t">
										{{ langCart.semiModelPrice }}
									</div>
									<div class="c">
										<CCYRate :price="item.setupCharge"></CCYRate>
									</div>
								</div>
								<div class="item-p tRush" v-if="showRushFee">
									<div>
										<div class="t">
											{{ langCart.deliveryFee }}
										</div>
										<div class="c" v-if="item.rushFee > 0">
											<CCYRate :price="item.rushFee"></CCYRate>
										</div>
										<div class="c" v-else-if="item.discount < 0">
											<CCYRate :price="item.totalPrice - item.unitPrice * item.quantity - item.mouldPrice - item.setupCharge"></CCYRate>
											<!-- ${{ (item.totalPrice - item.unitPrice * item.quantity - item.mouldPrice - item.setupCharge).toFixed(2) }} -->
											<div>(+{{ -Number(item.discount) }}% {{ langCart.t }})</div>
										</div>
										<div class="c" v-else>
											<CCYRate :price="0"></CCYRate>
										</div>
									</div>
								</div>
								<div class="item-p t7">
									<div class="t">
										{{ langCart.subtotal }}
									</div>
									<div class="c">
										<strong class="totalPrice">
											<span>
												<CCYRate v-if="item.discount > 0" class="originUnitPrice" :class="{ 'text-decoration-line-through': item.discount }" :price="item.unitPrice * item.quantity + item.mouldPrice + item.setupCharge"> </CCYRate>
												<CCYRate :price="item.totalPrice"></CCYRate>
											</span>
										</strong>
									</div>
								</div>
								<div class="item-p t8" :class="{ btn3: isbtn == 2 }">
									<div class="t"></div>
									<div class="c">
										<v-btn small icon color="#B3B3B3" @click="delCart([item])">
											<v-icon>mdi-trash-can-outline</v-icon>
										</v-btn>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<!-- 霓虹灯网站赠品 -->
				<div class="neonGiveaway" v-if="isopenFreebie === 1">
					<div class="neonGiveaway_banner">
						<!-- 金额满足$200 -->
						<div class="neonGiveaway_banner_satisfy" v-if="productPrice >= 200"></div>
						<!-- 金额不满足$200 -->
						<div class="neonGiveaway_banner_dissatisfy" v-else>
							{{ langCart.add }}<span>${{ (200 - productPrice).toFixed(2) }}</span
							>{{ langCart.toYourCart }} <span>{{ langCart.free1 }}</span
							>{{ langCart.christmasgift }}
						</div>
					</div>
					<div class="neonGiveaway_outer">
						<div class="neonGiveaway_list">
							<div class="swiper-container" @touchmove="disableSwiper" :style="fontSelectListId && device !== 'mb' ? 'padding: 10px 0 200px;' : !fontSelectListId && device === 'mb' ? 'padding: 10px 0 20px;' : 'padding: 10px 0 110px;'">
								<div class="swiper-wrapper">
									<div class="swiper-slide" v-for="(item, index) in filteredSwiperList" :key="index" @click="neonGiveawayClick(item, 'sList')" :style="item.id === showActive ? 'border:1px solid #539271;' : 'border:1px solid #E0E0E0'">
										<b class="icon-check" v-show="item.id === showActive"></b>
										<div class="slide-img">
											<img :src="device !== 'mb' ? item.imgPc : item.imgMb" />
										</div>
										<div class="slide-text" :style="item.type === 1 && device !== 'mb' ? 'padding: 16px 20px 23px;' : item.type === 0 && device === 'mb' ? 'padding: 12px 0 12px 10px;' : 'padding: 16px 20px 64px;'">
											<div class="slide-text-name">{{ item.name }}</div>
											<div :style="item.type === 1 ? 'display:flex;justify-content: space-between;' : ''">
												<div class="slide-text-price">
													{{ langCart.price }}:
													<span>$0</span>
													<span>${{ item.price }}</span>
												</div>
												<div class="slide-text-size">{{ langCart.size }}: {{ item.param }}</div>
											</div>
											<div class="customization" v-if="item.type === 1">
												<div class="customization_text">
													{{ langCart.Text }}:&nbsp;&nbsp;
													<v-text-field v-model="item.neonCustomText" dense solo hide-details outlined :error="!item.customizationRequired && !item.neonCustomText" label="Max 5 Letters" oninput="if(value.length>5)value=value.slice(0,5)" :disabled="productPrice < 200 ? true : false" @focus="onFocus(item)" @blur="onBlur(item)"> </v-text-field>
												</div>
												<div class="customization_color">
													<!-- :style="!item.customizationRequired && (!item.neonCustomColor || !defaultColor) ? 'line-height: 27px;border: 2px solid #ff5252;' : 'border-color:#8E8E8E'" -->
													<div @click.stop="colorSelectClick(item, 'color')" class="colorSelect">
														<span :style="mergedStyles(item, 'color')"></span>
														<b class="icon-zuojiantou1" :style="colorSelectListId === item.id ? 'transform: rotate(269deg);' : 'transform: rotate(88deg);'"></b>
													</div>
													<div class="colorSelectList" v-if="colorSelectListId === item.id">
														<div style="display: flex; justify-content: space-between">
															<div style="flex-wrap: wrap; flex: 1" @click.stop>{{ langCart.SelectColor }}</div>
															<b class="icon-cha" @click.stop="colorSelectListId = 0"></b>
														</div>
														<div class="list_div">
															<div class="color_div" v-for="(i, index) in neonColorList" :key="index" :class="neonColorId === i.id ? 'color_activeBorder' : 'color_defaultBorder'" @click.stop="colorListClick(i, item, 'color')">
																<div :style="{ backgroundColor: i.paramCode, width: '100%', height: '100%', borderRadius: '5px' }" :title="i.paramNameZh"></div>
																<div v-show="neonColorId === i.id" class="icon"><b class="icon-cc-yes-crude"></b></div>
															</div>
														</div>
													</div>
												</div>
											</div>
											<div v-if="item.type === 1" class="customization_font">
												<div class="customization_color">
													<span>{{ langCart.Font }}:&nbsp;</span>
													<!-- :style="!item.customizationRequired && (!item.neonCustomFont || !defaultFont) ? 'line-height: 27px;border: 2px solid #ff5252;' : 'border-color:#8E8E8E'" -->
													<div @click.stop="colorSelectClick(item, 'font')" class="colorSelect">
														<img :src="mergedStyles(item, 'font')" class="font_img" />
														<b class="icon-zuojiantou1" :style="fontSelectListId === item.id ? 'transform: rotate(269deg);' : 'transform: rotate(88deg);'"></b>
													</div>
													<div class="colorSelectList" v-if="fontSelectListId === item.id">
														<div style="display: flex; justify-content: space-between">
															<div style="flex-wrap: wrap; flex: 1" @click.stop>{{ langCart.Choosewant }}</div>
															<b class="icon-cha" @click.stop="fontSelectListId = 0"></b>
														</div>
														<div class="list_div">
															<div class="color_div" v-for="(i, index) in customizationFontitems" :key="index" :class="neonFontId === i.id ? 'color_activeBorder' : 'color_defaultBorder'" @click.stop="colorListClick(i, item, 'font')">
																<div><img :src="getImgJson(i)" /></div>
																<div v-show="neonFontId === i.id" class="icon"><b class="icon-cc-yes-crude"></b></div>
															</div>
														</div>
													</div>
												</div>
											</div>
											<div class="slide-text-qty">{{ langCart.Qty }}: {{ item.quantity }}</div>
										</div>
									</div>
								</div>
								<div class="iconMb">
									<div class="neonGiveaway_jianto" :class="swiperJianto === 'left' ? 'jianto-bg-blue' : 'jianto-bg'" @click="leftClick" v-if="swiperList.length > 6"></div>
									<div class="swiper-pagination"></div>
									<div class="neonGiveaway_jianto" :class="swiperJianto === 'right' ? 'jianto-bg2-blue' : 'jianto-bg2'" @click="rightClick" v-if="swiperList.length > 6"></div>
								</div>
							</div>
						</div>
						<div class="swiper-button-prev"></div>
						<div class="swiper-button-next"></div>
					</div>
					<div v-if="device === 'mb'">
						<div v-for="(item, index) in filtered2SwiperList" :key="index" class="customizationMb" @click="neonGiveawayClick(item, 'list')" :style="item.id === showActive ? 'border:1px solid #539271;' : 'border:1px solid #E0E0E0'">
							<b class="icon-check" v-show="item.id === showActive"></b>
							<div class="slide-img">
								<img :src="item.imgMb" />
							</div>
							<div class="slide-text">
								<div class="slide-text-name">{{ item.name }}</div>
								<div class="slide-text-price">
									{{ langCart.price }}:
									<span>$0</span>
									<span style="">${{ item.price }}</span>
								</div>
								<div class="slide-text-size">{{ langCart.size }}: {{ item.param }}</div>
								<div class="customization">
									<!-- value = value.replace(/[^a-zA-Z0-9+-]/g, '').slice(0, 5) -->
									<div class="customization_text">
										{{ langCart.Text }}:&nbsp;
										<v-text-field v-model="item.neonCustomText" dense solo hide-details outlined :error="!item.customizationRequired && !item.neonCustomText" label="Max 5 Letters" oninput="if(value.length>5)value=value.slice(0,5)" :disabled="productPrice < 200 ? true : false" @focus="onFocus(item)" @blur="onBlur(item)"> </v-text-field>
									</div>
									<div class="customization_color">
										<!-- :style="!item.customizationRequired && item.neonCustomColor === undefined ? 'line-height: 27px;border: 2px solid #ff5252;' : 'border-color:#8E8E8E'" -->
										<div @click.stop="colorSelectClick(item, 'color')" class="colorSelect">
											<span :style="mergedStyles(item, 'color')"></span>
											<b class="icon-zuojiantou1" :style="colorSelectListId === item.id ? 'transform: rotate(269deg);' : 'transform: rotate(88deg);'"></b>
										</div>
										<div class="colorSelectList" v-if="colorSelectListId === item.id">
											<div style="display: flex; justify-content: space-between">
												<div style="flex-wrap: wrap; flex: 1" @click.stop>{{ langCart.SelectColor }}</div>
												<b class="icon-cha" @click.stop="colorSelectListId = 0"></b>
											</div>
											<div class="list_div">
												<div class="color_div" v-for="(i, index) in neonColorList" :key="index" :class="neonColorId === i.id ? 'color_activeBorder' : 'color_defaultBorder'" @click.stop="colorListClick(i, item, 'color')">
													<div :style="{ backgroundColor: i.paramCode, width: '100%', height: '100%', borderRadius: '5px' }"></div>
													<div v-show="neonColorId === i.id" class="icon"><b class="icon-cc-yes-crude"></b></div>
												</div>
											</div>
										</div>
									</div>
								</div>
								<div v-if="item.type === 1" class="customization_font">
									<!-- :style="!item.customizationRequired && (!item.neonCustomFont || !defaultFont) ? 'line-height: 27px;border: 2px solid #ff5252;' : 'border-color:#8E8E8E'" -->
									<div class="customization_color">
										<span>{{ langCart.Font }}:&nbsp;</span>
										<div @click.stop="colorSelectClick(item, 'font')" class="colorSelect">
											<img :src="mergedStyles(item, 'font')" class="font_img" />
											<b class="icon-zuojiantou1" :style="fontSelectListId === item.id ? 'transform: rotate(269deg);' : 'transform: rotate(88deg);'"></b>
										</div>
										<div class="colorSelectList" v-if="fontSelectListId === item.id">
											<div style="display: flex; justify-content: space-between">
												<div style="flex-wrap: wrap; flex: 1" @click.stop>{{ langCart.Choosewant }}</div>
												<b class="icon-cha" @click.stop="fontSelectListId = 0"></b>
											</div>
											<div class="list_div">
												<div class="color_div" v-for="(i, index) in customizationFontitems" :key="index" :class="neonFontId === i.id ? 'color_activeBorder' : 'color_defaultBorder'" @click.stop="colorListClick(i, item, 'font')">
													<div><img :src="getImgJson(i)" /></div>
													<div v-show="neonFontId === i.id" class="icon"><b class="icon-cc-yes-crude"></b></div>
												</div>
											</div>
										</div>
									</div>
								</div>
								<div class="slide-text-qty">{{ langCart.Qty }}: {{ item.quantity }}</div>
							</div>
						</div>
					</div>
				</div>
				<div id="observeDom"></div>
				<div id="subtotalBtnGroup" class="subtotal-con" :style="proId == 444 ? 'background-color: #f2eee4;' : 'background-color: #fff;'">
					<div class="subtotal" :style="proId == 444 ? 'background-color: #f2eee4;' : device !== 'mb' ? 'background-color: #f2f4f5;' : 'background-color: #fff;'">
						<div class="sub-item">
							<div class="label">
								{{ langCart.subtotal }}
							</div>
							<div class="con">
								<CCYRate :price="productPrice"></CCYRate>
							</div>
						</div>
						<!--   提前算税  -->
						<div class="sub-item">
							<div class="label">{{ langCart.shipping }}</div>
							<div class="con" style="color: #29bc75">{{ langCart.free }}</div>
						</div>
						<div v-if="continentName == 'Europe'">
							<div class="sub-item">
								<div class="label">{{ showtaxType == true ? taxType : getTax(1) }}-{{ (textPrice * 100).toFixed(2) }}% ({{ zipCode1 }})</div>
								<div class="con" style="color: #de3500">
									+
									<CCYRate :price="textPrice * totalPrice"></CCYRate>
								</div>
							</div>
							<div @click="zIPCodeClick" v-show="showZIPCode == false" style="cursor: pointer; margin-bottom: 10px">
								<span style="color: #4d82bb">{{ getTax(2) }}</span>
							</div>
							<div v-show="showZIPCode == true" class="sub-item">
								<div class="label">
									{{ getTax(3) }}
									<v-tooltip top color="#FFFFFF">
										<template v-slot:activator="{ on, attrs }">
											<span class="wenhao">
												<b class="icon-wenhao1" v-bind="attrs" v-on="on"></b>
											</span>
										</template>
										<span style="color: black" class="wenhaoText">{{ getTax(4) }}</span>
									</v-tooltip>
								</div>
								<div class="con">
									<div style="display: flex; align-items: center; flex: 0.5; justify-content: right">
										<v-text-field v-model="zipCode" type="number" dense solo hide-details class="codeInput"></v-text-field>
										<v-btn type="primary" class="codeButton" @click="applyClick">{{ langCart.apply }} </v-btn>
									</div>
									<div style="color: red" v-show="showZIPCodeRules">{{ langCart.pleasecode }}</div>
								</div>
							</div>
						</div>

						<!--   首次订购折扣  -->
						<div class="sub-item" v-if="Number(firstDiscountPrice)">
							<div>{{ langCart.fod }}({{ firstDiscount * 100 }}{{ langCart.off }}):</div>
							<div class="con" style="color: #de3500">
								-
								<CCYRate :price="firstDiscountPrice"></CCYRate>
							</div>
						</div>
						<!--订单折扣-->
						<!--							v-if="Number(orderDiscountPrice)"-->
						<div class="sub-item" v-if="Number(orderDiscountPrice)">
							<div>{{ langCart.orderDiscount }}({{ (orderDiscount * 100).toFixed(0) }} {{ langCart.off }}):</div>
							<div class="con" style="color: #de3500">
								-
								<CCYRate :price="orderDiscountPrice"></CCYRate>
							</div>
						</div>
						<!--额外费用(佣金)-->
						<div class="sub-item" v-if="Number(payAdditionalFee)">
							<div class="label">{{ langCart.additionalFee }}:</div>
							<div class="con">
								+
								<CCYRate :price="payAdditionalFee"></CCYRate>
							</div>
						</div>
						<!--  额外折扣  -->
						<!--							v-if="Number(promotionPrice)"        :class="this.proType === 0 ? 'border-bottom' : ''" -->
						<!-- <div class="sub-item mb-2 mb-lg-3" v-if="Number(promotionPrice)">
                            <div>{{ langCart.extraDiscount }}({{ (promotionDiscount * 100).toFixed(0) }} {{ langCart.off }}):</div>
                            <div class="con" style="color: #de3500">
                                -
                                <CCYRate :price="promotionPrice"></CCYRate>
                            </div>
                        </div> -->
						<div class="sub-item mb-2 mb-lg-3" v-for="(item, index) in ordersPromotionDiscounts" :key="index">
							<div style="flex: 5">{{ item.activityName }}</div>
							<div class="con" style="color: #de3500">
								-
								<CCYRate :price="item.discountPrice"></CCYRate>
							</div>
						</div>

						<!-- 促销中心额外收费 -->
						<div class="sub-item" v-if="Number(promotionCenterAdditionalPrice)">
							<div class="label">{{ langCart.PromotionCenterAdditionalPrice }}:</div>
							<div class="con">
								+
								<CCYRate :price="promotionCenterAdditionalPrice"></CCYRate>
							</div>
						</div>

						<!--用户等级折扣 -->
						<div class="sub-item" v-if="Number(userLevelDiscountPrice)">
							<div class="label">{{ langCart["userDiscount" + userLevelId] }} ({{ (userLevelDiscount * 100).toFixed(0) }} {{ langCart.off }}):</div>
							<div class="con" style="color: #de3500">
								-
								<CCYRate :price="userLevelDiscountPrice"></CCYRate>
							</div>
						</div>

						<!--优惠码-->
						<v-radio-group v-if="couponOption.length > 0 || voucherOption.length > 0 || pointMaxPriceOrNoPointTotalPrice > 0" dense v-model="radioGroup" multiple :style="couponOption.length > 0 || voucherOption.length > 0 || pointMaxPriceOrNoPointTotalPrice > 0 ? 'border-top: 1px dashed #DBDBDB;margin-top: -1px;padding-top: 20px;' : 'border-top: 0px'">
							<!--优惠券-->
							<div class="d-flex align-items-center discount-item mb-2" v-if="couponOption.length && !isPensSite && !isStickerSite && !isCouponAllowed">
								<v-radio :value="2">
									<template #label> {{ langCart.couponDiscount }}:</template>
								</v-radio>
								<div class="discount">
									<div class="d-flex align-center">
										<v-autocomplete :filter="filterName" v-model="couponId" :items="couponOption" clearable dense solo hide-details item-text="title" item-value="uniqueId" @change="changeDiscount($event, 2)"> </v-autocomplete>
									</div>
									<div class="con" style="color: #de3500" v-show="showPrice === true">
										-
										<CCYRate :price="couponDiscountPrice"></CCYRate>
									</div>
								</div>
							</div>

							<!-- pens网站 或者  贴纸网站-->
							<div class="pens-discount" v-if="isPensSite || isStickerSite || isCouponAllowed">
								<v-radio-group hide-details dense v-model="radioGroup" multiple style="margin-top: 0">
									<div class="discount-item-div d-flex align-center">
										<v-radio :value="1">
											<template #label> {{ langCart.enterCouponCode }}:</template>
										</v-radio>
										<div class="" v-if="radioGroup.includes(1)" style="width: 100%">
											<!-- 优惠金额不为空，为空回显金额 -->
											<div class="discount d-flex align-center" style="justify-content: space-between">
												<!-- 展示选中的优惠卷名称 -->
												<div @click="showcoupon = !showcoupon" class="d-flex align-center">
													<div>
														<span v-html="getText()"></span>
													</div>

													<div>
														<v-icon v-show="showcoupon === true" class="couponicon"> mdi-chevron-down </v-icon>
														<v-icon v-show="showcoupon === false" class="couponicon"> mdi-chevron-right </v-icon>
													</div>
												</div>
												<div class="con" style="color: rgb(222, 53, 0)" v-show="showPrice">
													-
													<CCYRate :price="couponType === 2 ? crowdDiscountPrice : couponDiscountPrice"></CCYRate>
												</div>
											</div>
										</div>
									</div>
									<div class="radio-group-div" v-show="showcoupon">
										<v-radio-group v-model="couponId">
											<v-radio :value="item.uniqueId" v-for="(item, index) in couponList" :key="index" @click="couponRadio">
												<template v-slot:label>
													<span>
														{{ item.couponType === 4 ? item.conditionDescription : item.title }}
													</span>
													<span style="color: #666666" v-show="item.validityEndTime"> ( {{ langCart.deadline }}:{{ item.validityEndTime }} ) </span>
												</template>
											</v-radio>
										</v-radio-group>
									</div>
								</v-radio-group>
							</div>

							<!--代金券-->
							<div class="d-flex align-items-center discount-item mb-2" v-if="voucherOption.length">
								<v-radio :value="3">
									<template #label> {{ langCart.voucherDiscount }}:</template>
								</v-radio>
								<div class="discount">
									<div class="d-flex align-center">
										<v-autocomplete :filter="filterName" v-model="voucherId" :items="voucherOption" clearable dense solo hide-details item-value="id" @change="changeDiscount($event, 3)">
											<template v-slot:selection="data">
												<CCYRate :price="data.item.cashValue"></CCYRate>
											</template>
											<template v-slot:item="data">
												<CCYRate :price="data.item.cashValue"></CCYRate>
											</template>
										</v-autocomplete>
									</div>
									<div class="con" style="color: #de3500">
										-
										<CCYRate :price="voucherPrice"></CCYRate>
									</div>
								</div>
							</div>
							<!--优惠积分-->
							<div class="d-flex align-items-center discount-item mb-2" v-if="pointMaxPriceOrNoPointTotalPrice">
								<v-radio :value="4">
									<template #label> {{ langCart.pointDeduction }}:</template>
								</v-radio>
								<div class="discount">
									<div class="d-flex align-center">
										<v-text-field v-model="pointDiscountPrice" type="number" dense solo hide-details append-icon="mdi-square-edit-outline" :placeholder="langCart.maxinum + $store.state.currency.symbol + pointMaxPriceOrNoPointTotalPrice"></v-text-field>
									</div>
									<div class="con" style="color: #de3500">
										-
										<CCYRate :price="pointDiscountPrice / $store.state.currency.rate"></CCYRate>
									</div>
								</div>
							</div>
						</v-radio-group>
						<div v-if="isLogin && stickerCouponListType && isStickerSite" style="color: #de3500">
							<!-- 优惠劵的货币是欧元显示德语，其他都显示英语，货币符号变换 -->
							<div v-if="currencySymbol === 'EUR'">
								<div>Einlösebedingungen:</div>
								<div>Der €91 Gutschein kann nur für Stanzaufkleber verwendet werden, und die maximale Größe beträgt 3"x3".</div>
							</div>
							<div v-else>
								<div>Redemption Limits :</div>
								<div>
									The <span>{{ getcurrencySymbol() }}</span>91 credit coupon can only be used for Die-Cut Stickers, and the maximum size is 3”x3”.
								</div>
							</div>
						</div>
						<div class="totle">
							<div class="totleName">
								<div class="font-weight-bold tt">{{ langCart.total }}:</div>
								<div class="con">
									<span>{{ symbolCode }}</span>
									<CCYRate class="totalPrice" :price="getTotal(textPrice, totalPrice)"></CCYRate>
								</div>
							</div>
							<div class="tips-text" v-if="(isPensSite || isStickerSite || isCouponAllowed) && couponList.length > 0">
								{{ langCart.tipsText }}
							</div>

							<div class="but" style="display: flex">
								<button v-ripple class="shoppingBtn" @click="toShopping">
									{{ langCart.continueShopping }}
								</button>
								<button v-ripple class="checkoutBtn" :class="{ disabled: !selectCartList.length }" @click="toCheckout">
									{{ langCart.checkOut }}
								</button>
							</div>
						</div>
					</div>
				</div>
			</template>
			<template v-else-if="!cartList.length && !isLoading">
				<div class="title" flex>
					<h1 class="text-body-2 font-weight-bold text-lg-h5 text-uppercase">{{ langCart.title }}</h1>
					<div class="title_switch" v-if="openDesignerActivity" style="margin-left: 20px">
						<span @click="switchDesignerShoppingCart">Switch</span>
						<v-tooltip top color="#FFFFFF" class="tooltip-custom">
							<template v-slot:activator="{ on, attrs }">
								<span class="wenhao">
									<b class="icon-wenhao" v-bind="attrs" v-on="on"></b>
								</span>
							</template>
							<span style="color: black" class="wenhaoText" v-html="langCart.wenhaoText" />
						</v-tooltip>
					</div>
				</div>

				<v-card class="noCart mt-5" outlined>
					<v-card-title>{{ langCart.emptyTitle }}</v-card-title>
					<v-card-text>
						{{ langCart.emptyDes }}
					</v-card-text>
					<v-card-actions>
						<v-btn href="/" text primary class="white--text">{{ langCart.continueShopping }}</v-btn>
					</v-card-actions>
				</v-card>
			</template>
			<v-dialog v-model="delDialog" width="350" max-width="472" style="z-index: 3000">
				<v-card>
					<v-card-title class="text-h5">
						{{ langCart.tips }}
					</v-card-title>
					<v-card-text v-if="isopenFreebie !== 1">
						<div>{{ langCart.confirmDelete }}</div>
					</v-card-text>
					<v-card-text v-else>
						<div v-if="neonGiveawayPrice <= 0">{{ langCart.confirmDelete }}</div>
						<div class="neonGiveaway_text" v-else>
							{{ langCart.removeText1 }} <span style="color: red">${{ neonGiveawayPrice }}</span> {{ langCart.removeText2 }} <span style="color: #000">{{ langCart.removeText3 }}</span> {{ langCart.removeText4 }}
						</div>
					</v-card-text>
					<v-card-actions>
						<v-spacer></v-spacer>
						<v-btn color="primary" outlined @click="delDialog = false">
							{{ langCart.cancel }}
						</v-btn>
						<v-btn color="primary" @click="confirmDel">
							{{ langCart.remove }}
						</v-btn>
					</v-card-actions>
				</v-card>
			</v-dialog>
			<v-dialog v-model="freeDialog" width="350" max-width="472" style="z-index: 3000">
				<!--				报价有满送数量-->
				<v-card class="pb-2">
					<v-card-title v-if="freeType == 1" class="text-h5">
						{{ langCart.freeDialogText[0] }}
					</v-card-title>
					<v-card-text v-if="freeType == 1"> {{ langCart.freeDialogText[1] }} {{ changeItem.satisfiedQuantity }}{{ langCart.freeDialogText[2] }}{{ changeItem.giftQuantity }}{{ langCart.freeDialogText[3] }} </v-card-text>
					<v-card-text class="pt-5" v-if="freeType == 2">
						{{ langCart.freeDialogText[4] }} {{ changeItem.satisfiedQuantity }}{{ langCart.freeDialogText[2] }} {{ changeItem.giftQuantity }}
						{{ langCart.freeDialogText[5] }}
					</v-card-text>
					<v-card-text class="pt-5" v-if="freeType == 3">
						{{ langCart.freeDialogText[4] }} {{ changeItem.satisfiedQuantity }}{{ langCart.freeDialogText[2] }} {{ changeItem.giftQuantity }} {{ langCart.freeDialogText[5] }}
						{{ langCart.freeDialogText[6] }}
					</v-card-text>
					<v-card-actions v-if="freeType == 3">
						<v-spacer></v-spacer>
						<v-btn color="primary" outlined @click="notGift">
							{{ langCart.freeDialogText[7] }}
						</v-btn>
						<v-btn color="primary" @click="needGift">
							{{ langCart.freeDialogText[8] }}
						</v-btn>
					</v-card-actions>
					<v-card-actions v-else>
						<v-spacer></v-spacer>
						<v-btn color="primary" outlined @click="cancelFreePop">
							{{ langDesign.cancel }}
						</v-btn>
						<v-btn color="primary" @click="confirmFreePop">
							{{ langDesign.yes }}
						</v-btn>
					</v-card-actions>
				</v-card>
			</v-dialog>

			<!-- 霓虹灯网站赠送活动弹窗 -->
			<v-dialog v-model="neonGiveawayDelDialog" width="385" style="z-index: 3000">
				<v-card class="neonGiveaway_card">
					<div class="neonGiveaway_title">
						<img src="https://static-oss.gs-souvenir.com/web/quoteManage/20241101/bell_203501eCpfkD.png" width="21px" height="21px" />
						<div>{{ langCart.reminderText }}</div>
						<b class="icon-guanbi" @click="neonGiveawayCancel"></b>
					</div>
					<div class="neonGiveaway_text">
						{{ langCart.removeText1 }} <span style="color: red">${{ neonGiveawayPrice }}</span> {{ langCart.removeText2 }} <span style="color: #000">{{ langCart.removeText3 }}</span> {{ langCart.removeText4 }}
					</div>

					<div class="neonGiveaway_btn">
						<v-btn color="primary" outlined @click="neonGiveawayCancel">
							{{ langDesign.cancel }}
						</v-btn>
						<v-btn color="primary" @click="neonGiveawayYes">
							{{ langDesign.yes }}
						</v-btn>
					</div>
				</v-card>
			</v-dialog>
		</div>
	</v-app>
</template>

<script>
import { getCartList, delCartByIds, editCartComments, checkoutPrice, checkoutPriceNotLogin, getPayTypeList, goProDeatil, changeCartQuantity, getTaxByPrice, getFreebieList, getFreebieNeonColor, getFreebieNeonFont } from "@/api/web";
import { getUserCouponList, getUserUsableVoucherList, getUserReferPointsInfo } from "@/api/coupon";
import { debounce, isImageType, deepClone } from "@/utils/utils";
import DraftUpload from "@/components/DraftUpload";

export default {
	head() {
		return {
			title: this.langCart.cartT,
			meta: [
				{ hid: "description", name: "description", content: this.langCart.cartDescription },
				{ hid: "keywords", name: "keywords", content: this.langCart.cartKeywords },
			],
		};
	},
	name: "cart",
	components: { DraftUpload },
	data() {
		return {
			currencySymbol:"",

			defaultColor: {},
			defaultFont: {},
			colorChildId: 0,
			fontChildId: 0,
			customizationFontitems: [],
			tooltipText: "",
			fontSelectListId: 0,
			colorSelectListId: 0,
			neonColorId: 14402,
			neonFontId: 13682,
			neonColorList: [],

			stickerCouponList: [],
			stickerCouponListType: false,
			giveawayItem: {},
			isFixed: true,
			lastScrollTop: 0,

			neonBuyType: [],
			neonQuoteCateId: null,
			swiper: null,
			swiperJianto: "",
			neonGiveawayItemId: {},
			neonGiveawayPrice: 0,
			neonGiveawayDelDialog: false,
			showActive: 0,
			swiperList: [],
			showPrice: false,
			productList: [],
			ordersPromotionDiscounts: [],

			showtaxType: false,
			taxType: "",

			codeName: "",
			continentName: "",
			countryName: "",
			showZIPCodeRules: false,
			zipCode1: null,
			zipCode: null,
			showZIPCode: false,
			textPrice: null,

			couponType: null,
			couponList: [],
			showcoupon: false,
			menuStates: [], // 用来记录每个菜单的打开状态
			offset: true,
			changedQtyItem: {},
			paymentMethodId: 750,
			btnList: [
				{
					id: 0,
					name: "icon-type1",
				},
				{
					id: 1,
					name: "icon-type2",
				},
				{
					id: 2,
					name: "icon-shixinyingyong",
				},
			],
			isbtn: 0,
			radioGroup: [],
			totalPrice: 0,
			firstname: "",
			voucherId: "",
			couponId: "",
			debounceCalcPrice: null,
			pointDiscountPrice: "",
			firstDiscountPrice: "",
			voucherPrice: "",
			couponDiscountPrice: "",
			promotionPrice: "",
			orderDiscountPrice: "",
			productPrice: 0,
			value_type: "",
			couponOption: [],
			voucherOption: [],
			promotionDiscount: "",
			userLevelDiscount: "", //用户等级折扣
			userLevelDiscountPrice: "", //用户等级折扣
			userLevelId: null,
			orderDiscount: "",
			firstDiscount: "",
			couponDiscount: "",
			referPointsInfo: "",
			showCoupon: false,
			isActive: false,
			showButton: true,
			viewMore: false,
			selectAll: false,
			cartList: [],
			delDialog: false,
			delArr: [],
			payAdditionalFee: "",
			promotionCenterAdditionalPrice: "",
			showRushFee: true,
			// showQtyCard:false
			freeType: null,
			mutipelType: null,
			freeDialog: false,
			changeIndex: null,
			changeItem: null,
			qtyUnvalid: false,
			giftUnvalid: false,
			changeHasGift: false,
			isLoading: true,
			checkoutVal: false,
		};
	},
	computed: {
		filteredSwiperList() {
			return this.swiperList.filter((item) => (this.device === "mb" && item.type === 0) || this.device === "pc" || this.device === "ipad");
		},
		filtered2SwiperList() {
			return this.swiperList.filter((item) => this.device === "mb" && item.type === 1);
		},
		//是否开启设计师活动
		openDesignerActivity() {
			return this.$store.state.projectComment.openDesignerActivity;
		},
		//是否开启赠品
		isopenFreebie() {
			return this.$store.state.projectComment.openFreebie;
		},
		//是否开启一美金活动
		isCouponAllowed(){
			return this.$store.state.projectComment.isCouponAllowed;
		},
		isPensSite() {
			return this.proId === 317;
		},
		isStickerSite() {
			return this.proId === 2;
		},
		device() {
			return this.$store.state.device;
		},
		selectItemNum() {
			let item = this.cartList.filter((e) => e.select === true);
			return item.length;
		},
		siteEmail() {
			return this.$store.state.proSystem.email;
		},
		deliveryFee() {
			return this.$store.state.projectComment.cartShowDeliveryFee;
		},
		closeIndexList() {
			return JSON.parse(localStorage.getItem("closeTips"));
		},
		//用户uuid
		cartUuid() {
			if (this.isBuyNow) {
				return this.$route.query.uuid;
			} else {
				if (this.isLogin) {
					return null;
				} else {
					return this.$store.state.userUUID;
				}
			}
		},
		langCart() {
			return this.$store.getters.lang?.cart;
		},
		langTips() {
			return this.$store.getters.lang?.quote;
		},
		langDesign() {
			return this.$store.getters.lang?.design;
		},
		proType() {
			return this.$store.state.proType;
		},
		breadDetailItems() {
			return [
				{
					text: "Home",
					disabled: false,
					href: "/",
				},
				{
					text: "Continue Shopping",
					disabled: true,
					href: "/cart",
				},
			];
		},
		proId() {
			return this.$store.state.proId;
		},
		userUUID() {
			return this.$store.state.userUUID;
		},
		userId() {
			return this.$store.state.userInfo?.id;
		},
		isLogin() {
			return this.$store.getters.isLogin;
		},
		selectCartList() {
			let arr = [];
			this.cartList.forEach((item) => {
				if (item.select) {
					arr.push(item);
				}
			});
			return arr;
		},
		modelPrice() {
			let list = this.selectCartList;
			if (!list.length) {
				return;
			}
			let price = 0;
			list.forEach((item) => {
				price += item.mouldPrice;
			});
			return price.toFixed(2);
		},
		symbolCode() {
			return this.$store.state.currency.code;
		},
		// 积分最大可抵扣金额
		pointMaxPrice() {
			if (!this.referPointsInfo?.pointToCash || !this.referPointsInfo?.activeReferPoints) return 0;
			return parseInt(this.referPointsInfo.activeReferPoints / this.referPointsInfo.pointToCash);
		},
		// 未抵扣积分时的总价
		noPointTotalPrice() {
			return Math.max(this.productPrice - this.firstDiscountPrice - this.couponDiscountPrice - this.voucherPrice - this.crowdDiscountPrice - this.promotionPrice, 0);
		},
		//如果积分抵扣高于订单金额,则最大是订单金额
		pointMaxPriceOrNoPointTotalPrice() {
			if (!this.referPointsInfo?.pointToCash || !this.referPointsInfo?.activeReferPoints) return 0;
			//积分可抵扣金额
			let pointMaxPrice = this.pointMaxPrice;
			//未抵扣积分时的总价
			//如果积分抵扣高于订单金额,则最大是订单金额.如果小于,则最大输入是积分抵现金额
			let noPointTotalPrice = this.noPointTotalPrice;
			return Math.round((pointMaxPrice > noPointTotalPrice ? noPointTotalPrice * this.$store.state.currency.rate : pointMaxPrice * this.$store.state.currency.rate) * 100) / 100;
		},
	},
	watch: {
		couponId() {
			console.log("优惠券改动");
			this.debounceCalcPrice();
		},
		voucherId() {
			console.log("代金券改动");
			this.debounceCalcPrice();
		},
		// 积分可抵扣金额不超过订单总价
		pointDiscountPrice(val) {
			this.changeDiscount(val, 4);
			this.$nextTick(() => {
				if (Number(val) > Number(this.pointMaxPriceOrNoPointTotalPrice)) {
					this.pointDiscountPrice = this.pointMaxPriceOrNoPointTotalPrice;
				}
				this.debounceCalcPrice();
			});
		},
		cartList: {
			handler(newVal) {
				let findItem = newVal.find((item) => {
					return item.select === false;
				});
				if (findItem) {
					this.selectAll = false;
				} else {
					this.selectAll = true;
				}
			},
			deep: true,
			immediate: true,
		},
		async isLogin(val) {
			await this.getCartList();
			if (this.proType === 0 && val) {
				//默认加载全选列表
				this.cartList.forEach((item) => {
					let type;
					if (item.buyType === 7) {
						type = 3;
					} else if (item.buyType === 9) {
						type = 2;
					}
					this.productList.push({
						cateType: type,
						id: item.quoteCateChildId,
						name: item.cateName,
						quantity: item.quantity,
						tag: `${type}-${item.quoteCateChildId}`,
						price: item.totalPrice,
					});
				});
				await this.fetchUserCouponList();

				Promise.all([getUserUsableVoucherList(), getUserReferPointsInfo()])
					.then((res) => {
						this.voucherOption = res[0].data;
						this.referPointsInfo = res[1].data;

						this.debounceCalcPrice();
						location.reload();
					})
					.catch((err) => {
						console.log(err);
					});
			}
		},
		symbolCode:{
			handler(newVal){
				this.debounceCalcPrice();
			}
		}
	},
	methods: {
		getcurrencySymbol(){
			if(this.currencySymbol === 'CAD'){
				return 'CA$'
			}else if(this.currencySymbol === 'GBP'){
				return '￡'
			}else if(this.currencySymbol === 'AUD'){
				return 'AU$'
			}else{
				return '$'
			}
		},
		getImgJson(i) {
			return JSON.parse(i.imageJson)[0].url;
		},
		mergedStyles(item, type) {
			if (type === "color") {
				if (item.neonCustomColor && item.neonCustomColor[item.id]) {
					const backgroundColor = item.neonCustomColor && item.neonCustomColor[item.id] ? item.neonCustomColor[item.id].paramCode : "";
					const borderStyle = backgroundColor ? "1px solid #e9e9e9" : "";
					return {
						backgroundColor,
						border: borderStyle,
					};
				} else {
					let find = this.neonColorList.find((res) => res.paramCode === "#275ffc");
					if (find) {
						this.defaultColor = find;
						const backgroundColor = find.paramCode;
						const borderStyle = backgroundColor ? "1px solid #e9e9e9" : "";
						return {
							backgroundColor,
							border: borderStyle,
						};
					}
				}
			} else if (type === "font") {
				if (item.neonCustomFont && item.neonCustomFont.imageJson) {
					let t = item.neonCustomFont && item.neonCustomFont.imageJson ? JSON.parse(item.neonCustomFont.imageJson)[0].url : "";
					return t;
				} else {
					let find = this.customizationFontitems.find((res) => res.paramName === "Rocket");
					if (find) {
						this.defaultFont = find;
						return JSON.parse(find.imageJson)[0].url;
					}
				}
			}
		},
		onFocus(item) {
			this.showActive = item.id;
			this.disableSwiper();
		},
		onBlur(item) {
			this.showActive = item.id;
			this.enableSwiper();
			if (item.neonCustomText) {
				this.giveawayItem = item;
				this.giveawayItem.defaultFont = this.defaultFont;
				this.giveawayItem.defaultColor = this.defaultColor;
				localStorage.setItem("giveawayItem", JSON.stringify(item));
			}
		},
		disableSwiper() {
			if (this.device === "mb") {
				return;
			}
			//禁止 swiper 滑动
			const swiperContainer = this.$el.querySelector(".swiper-container");
			swiperContainer.style.pointerEvents = "none"; // 禁止 swiper 滑动
		},
		enableSwiper() {
			const swiperContainer = this.$el.querySelector(".swiper-container");
			swiperContainer.style.pointerEvents = "auto"; // 允许 swiper 滑动
		},
		//选择颜色\字体
		colorSelectClick(item, type) {
			if (this.productPrice >= 200) {
				if (type === "color") {
					this.fontSelectListId = 0;
					this.colorSelectListId = item.id;
					this.colorChildId = item.quoteCateChildId;
				} else if (type === "font") {
					this.colorSelectListId = 0;
					this.fontSelectListId = item.id;
					this.fontChildId = item.quoteCateChildId;
				}
			}
		},
		//获取颜色
		getFreebieNeonColor() {
			let find = this.swiperList.find((item) => item.type === 1);
			if (find) {
				this.colorChildId = find.quoteCateChildId;
			} else {
				return false;
			}
			let params = {
				childId: this.colorChildId,
			};
			getFreebieNeonColor(params).then((res) => {
				this.neonColorList = res.data;
			});
		},
		//获取字体
		getFreebieNeonFont() {
			let find = this.swiperList.find((item) => item.type === 1);
			if (find) {
				this.fontChildId = find.quoteCateChildId;
			} else {
				return false;
			}
			let params = {
				childId: this.fontChildId,
			};
			getFreebieNeonFont(params).then((res) => {
				this.customizationFontitems = res.data;
			});
		},

		//选择颜色列表
		colorListClick(i, item, type) {
			if (type === "color") {
				this.neonColorId = i.id;
				if (!item.neonCustomColor) {
					item.neonCustomColor = {};
				}
				item.neonCustomColor[item.id] = i;
				this.tooltipText = i.paramNameZh;
				this.colorSelectListId = 0;
				if (item.neonCustomColor[item.id]) {
					this.giveawayItem = item;
					if (this.defaultColor) {
						this.giveawayItem.defaultColor = {};
					}
					localStorage.setItem("giveawayItem", JSON.stringify(item));
				}
			} else if (type === "font") {
				this.neonFontId = i.id;
				item.neonCustomFont = i;
				this.fontSelectListId = 0;
				if (item.neonCustomFont) {
					this.giveawayItem = item;
					if (this.defaultFont) {
						this.giveawayItem.defaultFont = {};
					}
					localStorage.setItem("giveawayItem", JSON.stringify(item));
				}
			}
		},
		//切换设计师购物车
		switchDesignerShoppingCart() {
			this.$router.push({
				path: "/stylistCart",
			});
			this.$toast.info("Switch to the designer shopping cart");
		},
		//手机端赠品列表箭头
		updateArrowHighlight() {
			if (this.device !== "mb") {
				return false;
			}
			if (this.swiper.isBeginning) {
				this.swiperJianto = "";
			} else if (this.swiper.isEnd) {
				this.swiperJianto = "right";
			} else {
				// 根据当前活动幻灯片的索引来更新箭头状态
				this.swiperJianto = this.swiper.activeIndex > this.previousIndex ? "right" : "left";
			}
			this.previousIndex = this.swiper.activeIndex; // 更新上一个索引
		},

		//获取霓虹灯赠品列表
		getFreebie() {
			return new Promise((resolve) => {
				let params = {
					cateIdList: this.neonBuyType,
					page: 1,
					pageSize: 9999,
				};
				getFreebieList(params).then((res) => {
					let list = res.data.records;
					list.forEach((item) => {
						item.customizationRequired = true;
					});
					this.swiperList = list;
					resolve();
				});
			});
		},
		leftClick() {
			if (this.swiper) {
				this.swiper.slidePrev();
				this.swiperJianto = "left";
			}
		},
		rightClick() {
			if (this.swiper) {
				this.swiper.slideNext();
				this.swiperJianto = "right";
			}
		},
		async neonGiveawayCancel() {
			this.showActive = 0;
			this.neonGiveawayDelDialog = false;
			if (this.checkoutVal === false) {
				//全选
				this.cartList.forEach((res) => {
					res.select = true;
					this.neonBuyType.push(res.buyType + "-" + res.quoteCateId);
				});
				await this.getFreebie();
				this.debounceCalcPrice();
			} else {
				//单选
				this.selectCartList.push(this.neonGiveawayItemId);
				let find = this.selectCartList.find((res) => res.id === this.neonGiveawayItemId.id);
				if (find) {
					find.select = true;
				}
				// this.selectCartList.forEach((res)=>{
				// 	res.select = true;
				// })
				this.debounceCalcPrice();
			}

			if (this.changeItem) {
				//改数量的时候
				let data = {
					proId: this.proId,
					cartId: this.changeItem.id,
					isRadioQuantity: this.changeItem.isRadioQuantity,
					cartQuantityDTOList: [],
				};
				let obj = this.changeItem.quantityList[0];
				obj.quantity = this.changeItem.quantity;
				data.cartQuantityDTOList.push(obj);
				changeCartQuantity(data)
					.then((res) => {
						if (res.code !== 200) {
							this.$toast.error(res.message);
							this.changeItem.quantityEdit = this.changeItem.quantity;
							this.cartList.splice(this.changeIndex, 1, this.changeItem);
						}
						return this.updateCartItem([this.changeItem.id], this.changeItem, this.changeIndex);
					})
					.then(() => {});
			}
		},
		neonGiveawayYes() {
			this.showActive = 0;
			this.neonColorId = 14402;
			this.neonFontId = 13682;
			this.debounceCalcPrice();
			this.neonGiveawayDelDialog = false;
			localStorage.removeItem("giveawayItem");
		},
		neonGiveawayClick(item, type) {
			if (this.productPrice >= 200) {
				// 如果当前已经选中该项，则取消选中
				if (this.showActive === item.id && type === "sList") {
					let find = this.swiperList.find((i) => i.id === item.id);
					if (find) {
						find.neonCustomText = "";
						find.neonCustomColor = {};
					}
					this.giveawayItem = {};
					this.showActive = null;
					this.neonColorId = 14402;
					this.neonFontId = 13682;
					localStorage.removeItem("giveawayItem");
				} else {
					//手机端点击非定制产品的时候清空定制产品输入的值
					if (this.device === "mb" && type === "sList") {
						let find = this.swiperList.find((i) => i.type === 1);
						if (find) {
							find.neonCustomText = "";
							find.neonCustomColor = {};
						}
					}
					this.giveawayItem = item;
					this.giveawayItem.defaultFont = this.defaultFont;
					this.giveawayItem.defaultColor = this.defaultColor;
					this.showActive = item.id;
					localStorage.setItem("giveawayItem", JSON.stringify(item));
				}
			}
		},
		//欧洲国家文案
		getTax(type) {
			if (type == 1) {
				return "VAT";
			} else if (type == 2) {
				return this.langCart.ChangeDeliveryPostalcode;
			} else if (type == 3) {
				return this.langCart.PostalCode;
			} else if (type == 4) {
				return this.langCart.Enterpostalcodeoptions;
			}
		},
		//zipCode---邮政编码校验
		applyClick() {
			getTaxByPrice({
				shippingZipCode: this.zipCode,
			}).then((res) => {
				let countryName = res.data.res ? res.data.res.country.names.en : null;

				if (res.data.res !== null) {
					this.$toast.success(res.message);
					this.zipCode1 = this.zipCode;
					this.textPrice = res.data.rate;
					this.showZIPCode = false;
					this.showtaxType = true;
					this.taxType = countryName === "United States" ? "EST.Tax" : countryName === "Australia" ? "GST" : "VAT";
				} else {
					this.showZIPCodeRules = true;
					// 处理邮政编码不符合规则的情况
					// 可以显示错误信息或采取其他操作
				}
			});
		},
		zIPCodeClick() {
			this.showZIPCode = !this.showZIPCode;
			this.zipCode = this.zipCode1;
		},
		getTotal(textPrice, totalPrice) {
			if (this.continentName != "Europe") {
				return totalPrice;
			} else {
				let p = (Math.round(textPrice * totalPrice * 100) / 100).toFixed(2);
				let t = totalPrice + parseFloat(p);
				return t;
			}
		},

		//选择优惠劵，隐藏优惠劵列表
		couponRadio() {
			this.showcoupon = false;
		},

		getText() {
			let find = this.couponList.find((item) => item.uniqueId === this.couponId);
			if (find) {
				if (find.couponType === 4) {
					this.showPrice = true;
					if (find.validityEndTime) {
						let span = `<span>${find.conditionDescription}</span>&nbsp;<span style="color:#666666">( Deadline:${find.validityEndTime})</span>`;
						return span;
					} else if (!find.validityEndTime) {
						let span = `<span>${find.conditionDescription}</span>`;
						return span;
					}
					this.currencySymbol = find.currency;
					return "";
				} else {
					this.showPrice = false;
					return find.title;
				}
			}
			return "";
		},
		handleMenuOpen(isOpen, item, index) {
			console.log("菜单打开状态变化：", isOpen);
			// 处理菜单打开状态变化事件的逻辑
			// 变更的数组，要更改的元素的索引值，更改的内容
			item.quantityList = JSON.parse(JSON.stringify(item.quantityListEdit));
			this.cartList.splice(index, 1, item);
			this.clearPopVal();
		},
		changeQtyItem(item, ele) {
			this.changeItem = item;
			let hasGiftQty = item.quantityList.find((ele) => ele.isQuoteQuantity);
			let quantity = item.quantityList.reduce(function (total, currentValue, currentIndex, arr) {
				return total + Number(currentValue.quantity);
			}, 0);
			if (this.mutipelType == 1) {
				if (quantity >= item.satisfiedQuantity) {
					this.mutipelType = 2;
				}
			} else if (this.mutipelType == 2) {
				if (quantity < item.satisfiedQuantity) {
					this.mutipelType = 1;
				}
			} else if (this.mutipelType == 3 || this.mutipelType == 4) {
				if (quantity < item.satisfiedQuantity) {
					this.qtyUnvalid = true;
					this.mutipelType = 4;
				} else {
					this.qtyUnvalid = false;
					// this.mutipelType = null;
				}
			}
		},
		changeItemGiftQty(item, ele) {
			this.changeItem = item;
			this.giftUnvalid = false;
			let quantity = item.quantityList.reduce(function (total, currentValue, currentIndex, arr) {
				return total + Number(currentValue.giftQuantity);
			}, 0);
			console.log(2222222, item.quantityList, quantity, item.giftQuantity);
			if (quantity > item.giftQuantity) {
				//	 赠送数量超出满赠数额
				this.giftUnvalid = true;
				this.mutipelType = 4;
			} else {
				this.giftUnvalid = false;
			}
		},
		clearPopVal() {
			this.freeType = null;
			this.mutipelType = null;
			this.qtyUnvalid = false;
			this.giftUnvalid = false;
		},
		closeMenu(item, index, type) {
			if (type) {
				this.freeType = null;
				this.mutipelType = null;
				this.qtyUnvalid = false;
				this.giftUnvalid = false;
				this.changeCartItem2(1);
			}

			// item.quantityList = item.quantityListEdit;
			// this.$set(item, "quantityList", item.quantityListEdit);
			//取消时改动的数量复原显示;
			if (this.$refs[`qtyMenu${index}`]) {
				this.$refs[`qtyMenu${index}`].forEach((item) => {
					if (item.isActive) item.isActive = false;
				});
				// 变更的数组，要更改的元素的索引值，更改的内容
				item.quantityList = JSON.parse(JSON.stringify(item.quantityListEdit));
				this.cartList.splice(index, 1, item);
			}
		},
		notGift() {
			this.freeDialog = false;
			this.changeCartItem(1);
		},
		needGift() {
			this.freeDialog = false;
			this.changeCartItem(2);
		},
		cancelFreePop() {
			// if(this.freeType == 1){
			this.changeItem.quantityEdit = this.changeItem.quantity;
			// }
			this.freeDialog = false;
		},
		confirmFreePop() {
			this.mutipelType = null;
			this.changeCartItem();
		},
		toCancelMenu() {
			this.closeMenu(this.changeItem, this.changeIndex);
		},
		toChangeQty() {
			if (this.mutipelType == 1 || this.mutipelType == 2) {
				this.changeCartItem2();
			}
		},
		submitCartItem(item) {
			this.changeItem = item;
			this.changeCartItem2();
		},
		changeCartItem(needGift) {
			let itemId = [];
			itemId.push(this.changeItem.id);
			let data = {
				proId: this.proId,
				cartId: this.changeItem.id,
				isRadioQuantity: this.changeItem.isRadioQuantity,
				cartQuantityDTOList: [],
			};
			let obj = this.changeItem.quantityList[0];
			obj.quantity = this.changeItem.quantityEdit;
			if (needGift == 2) {
				obj.giftQuantity = this.changeItem.giftQuantity;
			} else if (needGift == 1) {
				obj.giftQuantity = 0;
			}

			data.cartQuantityDTOList.push(obj);
			changeCartQuantity(data).then((res) => {
				if (res.code !== 200) {
					this.$toast.error(res.message);
					this.changeItem.quantityEdit = this.changeItem.quantity;
					this.cartList.splice(this.changeIndex, 1, this.changeItem);
				}
				this.updateCartItem(itemId, this.changeItem, this.changeIndex);
				this.freeDialog = false;
			});
		},
		changeCartItem2(needGift) {
			let itemId = [];
			itemId.push(this.changeItem.id);
			if (needGift == 1) {
				this.changeItem.quantityList.forEach((item) => {
					if (item.giftQuantity > 0) {
						item.giftQuantity = 0;
					}
				});
			}
			if (this.changeHasGift) {
				this.changeItem.quantityList.forEach((item) => {
					if (item.giftQuantity > 0) {
						item.isQuoteQuantity = 1;
					}
				});
			} else {
				this.changeItem.quantityList.forEach((item) => {
					if (item.giftQuantity <= 0) {
						item.isQuoteQuantity = 0;
					}
				});
			}
			let data = {
				proId: this.proId,
				cartId: this.changeItem.id,
				isRadioQuantity: this.changeItem.isRadioQuantity,
				cartQuantityDTOList: [],
			};
			data.cartQuantityDTOList = this.changeItem.quantityList;
			data.cartQuantityDTOList.forEach((ele) => {
				ele.giftQuantity = ele.giftQuantity ? ele.giftQuantity : 0;
			});
			changeCartQuantity(data).then((res) => {
				if (res.code !== 200) {
					this.$toast.error(res.message);
					this.closeMenu(this.changeItem, this.changeIndex);
				}
				this.updateCartItem(itemId, this.changeItem, this.changeIndex);
			});
		},
		// 霓虹灯满$200赠品活动
		getNeonGiveaway() {
			if (this.productPrice < 200) {
				this.neonGiveawayDelDialog = true;
				this.neonGiveawayPrice = (200 - this.productPrice).toFixed(2);
			}
		},
		async debounceChangeQty(val, item, index) {
			console.log(item, 1212);
			this.mutipelType = null;
			this.changeIndex = index;
			this.changeItem = item;
			let itemId = [];
			itemId.push(item.id);
			let data = {
				proId: this.proId,
				cartId: item.id,
				isRadioQuantity: item.isRadioQuantity,
				cartQuantityDTOList: [],
			};
			if (item.isFullReductionActivity) {
				console.log("开启满送");
				if (val) {
					//多选
					// 报价有带赠送
					let hasGiftQty = item.quantityList.find((ele) => ele.isQuoteQuantity);
					let quantity = item.quantityList.reduce(function (total, currentValue, currentIndex, arr) {
						return total + Number(currentValue.quantity);
					}, 0);
					if (hasGiftQty) {
						if (item.quantity >= item.satisfiedQuantity) {
							if (quantity >= item.satisfiedQuantity) {
								this.changeCartItem2();
							} else {
								this.mutipelType = 1;
							}
						} else {
							if (quantity >= item.satisfiedQuantity) {
								this.mutipelType = 2;
								this.changeItem.giftQuantityTotal = item.quantityList.reduce(function (total, currentValue, currentIndex, arr) {
									return total + Number(currentValue.giftQuantity);
								}, 0);
							} else {
								this.changeCartItem2();
							}
						}
					} else {
						if (quantity >= item.satisfiedQuantity) {
							this.mutipelType = 3;
							this.changeHasGift = true;
						} else {
							this.changeCartItem2();
						}
					}
				} else {
					// 报价有带赠送
					let hasGiftQty = item.quantityList.find((ele) => ele.isQuoteQuantity);
					if (hasGiftQty) {
						if (item.quantity >= item.satisfiedQuantity) {
							this.qtyUnvalid = false;
							if (item.quantityEdit >= item.satisfiedQuantity) {
								this.changeCartItem();
							} else {
								this.freeType = 1;
								this.freeDialog = true;
							}
						} else {
							if (item.quantityEdit > item.satisfiedQuantity) {
								this.freeType = 2;
								this.freeDialog = true;
							} else {
								this.changeCartItem();
							}
						}
					} else {
						if (item.quantityEdit >= item.satisfiedQuantity) {
							this.freeType = 3;
							this.freeDialog = true;
						} else {
							this.changeCartItem();
						}
					}
				}
			} else {
				/*		let itemId = [];
                        itemId.push(item.id)
                        let data = {
                            proId: this.proId,
                            cartId: item.id,
                            isRadioQuantity: item.isRadioQuantity,
                            cartQuantityDTOList:[]
                        }*/
				if (val) {
					//多选
					// data.isRadioQuantity = 0;
					data.cartQuantityDTOList = item.quantityList;
					changeCartQuantity(data).then((res) => {
						if (res.code !== 200) {
							this.$toast.error(res.message);
							this.closeMenu(item, index);
						}
						this.updateCartItem(itemId, item, index);
					});
				} else {
					// 单选
					// data.isRadioQuantity = 1;
					let obj = item.quantityList[0];
					obj.quantity = item.quantityEdit;
					data.cartQuantityDTOList.push(obj);
					changeCartQuantity(data)
						.then((res) => {
							if (res.code !== 200) {
								this.$toast.error(res.message);
								item.quantityEdit = item.quantity;
								this.cartList.splice(index, 1, item);
							}
							return this.updateCartItem(itemId, item, index);
						})
						.then(() => {
							if (this.isopenFreebie === 1 && this.changeItem.totalPrice >= 200) {
								this.getNeonGiveaway();
							}
						});
				}
			}
		},
		updateCartItem(id, item, index) {
			return new Promise((resolve, reject) => {
				if (this.$refs[`qtyMenu${index}`]) {
					this.$refs[`qtyMenu${index}`].forEach((item) => {
						if (item.isActive) item.isActive = false;
					});
				}
				getCartList({
					proId: this.proId,
					uuid: this.isLogin ? null : this.userUUID,
					userId: this.isLogin ? this.userId : null,
					cartIds: id,
				})
					.then((res) => {
						console.log(this.selectCartList, res, "res");
						let obj = res.data[0];
						obj.propsData = {
							id: obj.id,
							proId: obj.proId,
							isSemiStock: obj.isSemiStock,
							isBlank: obj.isBlank,
							isEmailLater: obj.isEmailLater,
							isSemiProduct: obj.buyType === 9 ? true : false, // 半定制产品,
							closeTips: this.closeIndexList && this.closeIndexList.some((i) => i == obj.id) ? true : false,
						};
						obj.select = true;
						if (obj.isStock == 1) obj.select = false;
						obj.viewMore = false;
						// 暂存编辑的数量 防止改数量引起rushFee负数
						obj.quantityEdit = obj.quantity;
						// obj.giftQuantityTotal = obj.quantityList.reduce(function (total, currentValue, currentIndex, arr) {
						// 	return total + Number(currentValue.giftQuantity);
						// }, 0);
						obj.quantityListEdit = JSON.parse(JSON.stringify(obj.quantityList));
						// 判断报价是否带来赠送数量
						item.hasGiftQty = item.quantityList.find((ele) => ele.isQuoteQuantity) ? true : false;
						// 变更的数组，要更改的元素的索引值，更改的内容
						this.cartList.splice(index, 1, obj);
						// this.$set(this.desserts,index,item)
						return this.calcPrice();
						// this.debounceCalcPrice = debounce(this.calcPrice, 500);
					})
					.then(() => {
						resolve();
					})
					.catch((err) => {
						reject(err);
					});
			});
		},
		updateCartList(val, i) {
			// console.log(*********,val,i);
			this.cartList[i].imageJson = JSON.stringify(val);
		},
		toProDetail(id, buyType) {
			// 只有安定制跳转
			if (buyType !== 9) {
				return;
			}
			goProDeatil({ productId: id }).then((res) => {
				this.$router.push({
					path: res.data.productRouting,
				});
			});
		},
		zoomPic(img) {
			if (this.isImageType(img)) {
				this.$viewerApi({
					images: [img],
				});
			} else {
				window.open(img);
			}
		},
		isImageType(img) {
			if (!img) {
				return "";
			}
			return isImageType(img.split("?")[0]);
		},
		//自动选中
		changeDiscount(val, type) {
			if (val) {
				!this.radioGroup.includes(type) ? this.radioGroup.push(type) : "";
			} else {
				let findIndex = this.radioGroup.findIndex((item) => {
					return item === type;
				});
				if (findIndex >= 0) {
					this.radioGroup.splice(findIndex, 1);
				}
			}
			let find = this.couponOption.find((r) => r.uniqueId == val);
			if (find) {
				if (find.isNew === 0) {
					this.showPrice = true;
				} else {
					this.showPrice = false;
				}
			}
		},

		async calcPrice() {
			//订单图稿过来的不用调用此接口
			if (this.selectCartList.length <= 0) {
				this.productPrice = 0;
				this.totalPrice = 0;
				return;
			}
			let res;
			let data = {
				inquiryId: "",
				email: this.inputEmail,
				cartUuid: this.cartUuid,
				cartIdList: this.selectCartList.map((item) => item.id),
				paymentMethodId: this.paymentMethodId,
				// couponId: this.stickerCouponListType ? '' : this.couponId,
				voucherId: this.voucherId,
				pointPrice: Math.round((this.pointDiscountPrice / this.$store.state.currency.rate) * 100) / 100,
				referCode: this.nCouponCode,
			};
			let find = this.couponOption.find((t) => t.uniqueId === this.couponId);
			if (find) {
				//isNew:1 新优惠劵；isNew:0 旧优惠劵
				if (find.isNew === 1) {
					data.uniqueId = find.uniqueId;
					data.activityId = find.activityId;
				}
			}

			if (this.isPensSite || this.isStickerSite || this.isCouponAllowed) {
				if (this.couponId !== 4) {
					data.couponApplyType = 2;
				}
				data.currency = this.symbolCode;
				data.couponId=!this.couponList?.length ? '' : this.couponId;
			}else{
				data.couponId=this.couponId;
			}
			if (this.isLogin) {
				res = await checkoutPrice(data);
			} else {
				res = await checkoutPriceNotLogin(data);
			}
			this.updatePrice(res.data);
		},

		updatePrice(data) {
			this.orderDiscount = data.orderDiscount;
			this.orderDiscountPrice = data.orderDiscountPrice;
			this.productPrice = data.productPrice;
			this.totalPrice = data.totalPrice;
			this.taxPrice = data.taxPrice;
			this.apoShippingPrice = data.apoShippingPrice;
			this.payAdditionalFee = data.payAdditionalFee;
			this.discountPrice = data.discountPrice;
			this.pointPrice = data.pointPrice;
			//人群码优惠
			this.crowdDiscount = data.crowdDiscount;
			this.crowdDiscountPrice = data.crowdDiscountPrice;
			//第一次下单折扣
			this.firstDiscount = data.firstDiscount;
			this.firstDiscountPrice = data.firstDiscountPrice;
			//优惠券
			this.couponDiscount = data.couponDiscount;
			this.couponDiscountPrice = data.couponDiscountPrice;
			//代金券
			this.voucherPrice = data.voucherPrice;
			//额外折扣
			this.promotionPrice = data.promotionPrice;
			this.promotionDiscount = data.promotionDiscount;
			//用户等级折扣
			this.userLevelDiscount = data.ordersDiscount !== null ? data.ordersDiscount.userLevelDiscount : data.userLevelDiscount;
			this.userLevelDiscountPrice = data.ordersDiscount !== null ? data.ordersDiscount.userLevelDiscountPrice : data.userLevelDiscountPrice;
			this.couponAdditionalCosts = data.couponAdditionalCosts;
			this.userLevelId = data.userLevelId;

			this.ordersPromotionDiscounts = data.ordersPromotionDiscounts;
			this.promotionCenterAdditionalPrice = data.promotionCenterAdditionalPrice;
		},
		filterName(item, queryText, itemText) {
			return itemText.toLocaleLowerCase().startsWith(queryText.toLocaleLowerCase());
		},

		typeClick(item) {
			this.isbtn = item.id;
			localStorage.setItem("typeIndex", JSON.stringify(item));
			if (this.isbtn === 0) {
				this.showButton = true;
				this.cartList.forEach((item) => {
					item.viewMore = false;
				});
				this.isActive = false;
			} else if (this.isbtn === 1) {
				this.showButton = true;
				this.cartList.forEach((item) => {
					item.viewMore = true;
				});
				this.isActive = false;
			} else {
				this.isActive = true;
			}
		},
		toShopping() {
			//google记录 继续购物
			try {
				if (window.$nuxt.$store.state.advertConfig.googleConfig && window.gtag) {
					gtag("event", "select_content", {
						content_type: "continueShopping",
						content_value: "Continue Shopping",
					});
				}
			} catch (error) {}
			this.$router.push({
				path: "/",
			});
		},
		toCheckout() {
			if (this.giveawayItem.type === 1 && this.giveawayItem.neonCustomText === undefined) {
				let list = this.device !== "mb" ? this.filteredSwiperList : this.swiperList;
				let find = list.find((res) => res.id === this.giveawayItem.id);
				if (find) {
					find.customizationRequired = false;
				}
				this.$toast.error("Please enter the custom product content or color");
				return;
			}
			// checkout页面返回购物车
			if (this.giveawayItem === undefined || Object.keys(this.giveawayItem).length === 0) {
				localStorage.removeItem("giveawayItem");
			}
			//google记录 结算
			try {
				if (window.$nuxt.$store.state.advertConfig.googleConfig && window.gtag) {
					gtag("event", "select_content", {
						content_type: "checkOut",
						content_value: "Check Out",
					});
				}
			} catch (error) {}
			localStorage.setItem("tempCart", JSON.stringify(this.selectCartList));
			let queryObj = Object.assign({}, this.couponLis?.length && this.couponId ? { couponId: this.couponId } : {}, this.voucherId ? { voucherId: this.voucherId } : {}, this.pointDiscountPrice ? { pointDiscountPrice: this.pointDiscountPrice } : {});

			this.$router.push({
				path: "/checkout",
				query: { ...queryObj, isClick: 1 },
			});
		},
		changeComments(val, item) {
			editCartComments({
				cartId: item.id,
				comments: item.comments,
			}).then((res) => {
				this.typeClick();
				this.getCartList();
			});
		},
		//全选复选框
		async changeStatus(val) {
			this.checkoutVal = val;
			if (val) {
				this.cartList.forEach((item) => {
					item.select = true;
					if (item.isStock == 1) {
						item.select = false;
					}

					let type;
					if (item.buyType === 7) {
						type = 3;
					} else if (item.buyType === 9) {
						type = 2;
					}
					//获取选中的数据
					this.productList.push({
						cateType: type,
						id: item.quoteCateChildId,
						name: item.cateName,
						quantity: item.quantity,
						tag: `${type}-${item.quoteCateChildId}`,
						price: item.totalPrice,
					});
					this.neonBuyType.push(item.buyType + "-" + item.quoteCateId);
				});
				if (this.isLogin) {
					//重新调用获取优惠劵列表，获取对应的产品的优惠劵
					await this.fetchUserCouponList();
					if (this.isStickerSite) {
						this.getCouponSticker();
					}
				}
			} else {
				this.cartList.forEach((item) => {
					item.select = false;
				});

				if (this.isopenFreebie === 1) {
					this.neonBuyType = [];
					this.neonGiveawayDelDialog = true;
					this.neonGiveawayPrice = 200;
				}
				this.productList = [];
				this.ordersPromotionDiscounts = [];
				this.couponList = [];
				this.showPrice = false;
			}
			if (this.isopenFreebie === 1) {
				this.showActive = 0;
				await this.getFreebie();
			}
			this.debounceCalcPrice();
		},
		//单选复选框
		async changeSelect(item, index) {
			if (!this.isPensSite || !this.isStickerSite || this.isCouponAllowed) {
				//如果是选中状态
				if (item.select === true) {
					let type;
					if (item.buyType === 7) {
						type = 3;
					} else if (item.buyType === 9) {
						type = 2;
					}

					const isDuplicate = this.productList.some((product) => product.id === item.quoteCateChildId);

					// 如果不存在相同数据，则追加
					if (!isDuplicate) {
						this.productList.push({
							cateType: type,
							id: item.quoteCateChildId,
							name: item.cateName,
							quantity: item.quantity,
							tag: `${type}-${item.quoteCateChildId}`,
							price: item.totalPrice,
						});
					}
				} else {
					//去除未选中的数据
					let findIndex = this.productList.findIndex((i) => i.id === item.quoteCateChildId);
					if (findIndex !== -1) {
						this.productList.splice(findIndex, 1);
					}
				}
			}
			if (this.isLogin) {
				await this.fetchUserCouponList();
				if (this.isStickerSite) {
					this.getCouponSticker();
				}
			}

			if (this.isopenFreebie === 1) {
				//霓虹灯网站圣诞赠品活动
				this.showActive = 0;
				this.neonBuyType = [];
				this.neonGiveawayItemId = item;
				let price = 0;
				this.selectCartList.forEach((res) => {
					if (res.select === true) {
						price += res.totalPrice;
					}
					this.neonBuyType.push(res.buyType + "-" + res.quoteCateId);
				});
				await this.getFreebie();
				if (item.select === false) {
					if (price < 200) {
						this.neonGiveawayDelDialog = true;
						this.neonGiveawayPrice = (200 - price).toFixed(2);
					}
				} else {
					this.debounceCalcPrice();
				}
			} else {
				this.debounceCalcPrice();
			}

			this.$nextTick(async () => {
				if (this.cartList.some((i) => i.select === false)) {
					this.ordersPromotionDiscounts = [];
				}
			});
		},
		confirmDel() {
			let cartIds = this.delArr.map((item) => item.id);
			delCartByIds({
				cartIds: cartIds,
			})
				.then((res) => {
					this.$toast.success(res.message);
					this.$store.dispatch("updateHeadFootPages");
					this.getCartList().then(() => {
						this.debounceCalcPrice();
					});
				})
				.finally(() => {
					this.delDialog = false;
				});
		},
		getCartList() {
			return new Promise((resolve, reject) => {
				this.isLoading = true;
				this.$gl.show();
				getCartList({
					proId: this.proId,
					uuid: this.isLogin ? null : this.userUUID,
					userId: this.isLogin ? this.userId : null,
				})
					.then((res) => {
						let cartList = res.data;
						cartList.forEach((item, index) => {
							// 是否显示满赠数量
							// 判断报价是否带来赠送数量
							item.hasGiftQty = item.quantityList.find((ele) => ele.isQuoteQuantity) ? true : false;
							// item.giftQuantityTotal = item.quantityList.reduce(function (total, currentValue, currentIndex, arr) {
							// 	return total + Number(currentValue.giftQuantity);
							// }, 0);
							// item.showFreeQty = item.giftQuantity>0;
							// 暂存编辑的数量 防止改数量引起rushFee负数
							item.quantityEdit = item.quantity;
							//另存数据便于取消更改时数量复原显示
							item.quantityListEdit = JSON.parse(JSON.stringify(item.quantityList));
							let obj = {};
							item.propsData = {
								id: item.id,
								proId: item.proId,
								isSemiStock: item.isSemiStock,
								isBlank: item.isBlank,
								isEmailLater: item.isEmailLater,
								isSemiProduct: item.buyType === 9 ? true : false, // 半定制产品,
								closeTips: this.closeIndexList && this.closeIndexList.some((i) => i == item.id) ? true : false,
							};
							item.select = true;
							if (item.isStock == 1) item.select = false;
							item.viewMore = false;
						});
						// 购物车是否存在加价产品
						let addDiscount = cartList.find((e) => e.discount < 0 || e.rushFee > 0);
						this.showRushFee = addDiscount;
						this.cartList = cartList;
						resolve();
					})
					.finally(() => {
						this.$gl.hide();
						this.isLoading = false;
					});
			});
		},
		editCart(row) {
			let url = row.quoteRoutingName;
			const data = {
				quoteUrl: url.includes("?") ? `${url}&type=quoteIframe&isBack=1&id=${row.id}` : `${url}?type=quoteIframe&isBack=1&id=${row.id}`,
				modal: "modalQuoteDialog",
			};
			//购物车回填--小刚到此一游 sessionStorage.getItem("quoteBackParam");
			sessionStorage.setItem("quoteBackParam", JSON.stringify(Object.assign({}, JSON.parse(row.quoteParam), { quantity: row.quantity, comments: row.comments, imageJson: row.imageJson, qtyChecked: row.giftQuantityTotal === 30 ? true : false })));
			this.$store.commit("setMask", { ...data });
		},
		delCart(arr) {
			if (!arr.length) {
				this.$toast.error(this.langTips.PleaseSelect);
				return;
			}
			this.delArr = arr;
			this.delDialog = true;

			let price = 0,
				arrPrice = 0;
			this.selectCartList.forEach((res) => {
				price += res.totalPrice;
			});
			arr.forEach((i) => {
				arrPrice += i.totalPrice;
			});
			let e = price - arrPrice;
			this.neonGiveawayPrice = (200 - e).toFixed(2);
		},
		// sticker网站获取用户所有的优惠劵
		getCouponSticker() {
			let params = {
				isCartCheck: 0,
			};
			getUserCouponList(params).then((res) => {
				this.stickerCouponList = res.data;
			});
		},
		//获取优惠劵列表
		fetchUserCouponList() {
			return new Promise((resolve, reject) => {
				let params;
				if (this.isPensSite || this.isStickerSite || this.isCouponAllowed) {
					params = {
						type: 1,
						cash: this.productPrice,
						cartIdList: this.selectCartList.map((item) => item.id),
						userId: this.checkoutVal === false ? null : this.userId,
						productList: this.productList,
					};
				} else {
					params = {
						type: 1,
						cash: this.productPrice,
						productList: this.productList,
					};
				}
				getUserCouponList(params).then((res) => {
					let list = res.data;
					list.forEach((r) => {
						if (r.isNew === 0) {
							r.uniqueId = r.id;
						}
					});
					this.couponOption = list;
					this.debounceCalcPrice();

					if (this.isPensSite || this.isStickerSite || this.isCouponAllowed) {
						if (res.data.length) {
							let list = res.data;
							list.push({
								id: 4,
								uniqueId: 4,
								conditionDescription: "I don't want to use it",
								title: "I don't want to use it",
							});
							list.forEach((res) => {
								//一美金优惠劵:有id并且isNew等于1
								//普通老优惠劵：有id并且isNew等于0
								if ((res.id && res.isNew === 1) || (res.id && res.isNew === 0)) {
									res.uniqueId = res.id;
								} else {
									//促销新优惠劵
									return res.uniqueId;
								}
							});
							this.couponList = list;
							//默认选中第一个优惠劵
							this.radioGroup.push(1);
							this.couponId = this.couponList[0].uniqueId;
						} else {
							this.couponList = res.data;
						}
						if (this.isStickerSite) {
							if (this.couponList.every((i) => i.couponType !== 4) && this.stickerCouponList.some((i) => i.couponType === 4)) {
								this.stickerCouponListType = true;
							} else {
								this.stickerCouponListType = false;
							}
						}
					}
					resolve();
				});
			});
		},

		observeBtn() {
			if (this.device !== "mb" || !this.cartList.length) {
				return;
			}
			const ele = document.querySelector("#subtotalBtnGroup"),
				observeDom1 = document.querySelector("#observeDom"),
				observeDom2 = document.querySelector("#modalFooter, #myFooter, #footer");
			const observer = new IntersectionObserver(
				(entries) => {
					entries.forEach((entry) => {
						if (['modalFooter','myFooter','footer'].includes(entry.target.id) ) {
							if (entry.isIntersecting) {
								ele.classList.remove("fixed");
								ele.classList.add("notFixed");
							} else {
								ele.classList.remove("notFixed");
							}
							return;
						}
						if (entry.target.id === "observeDom") {
							if (entry.isIntersecting) {
								ele.classList.remove("fixed");
							} else {
								ele.classList.add("fixed");
							}
						}
					});
				},
				{
					threshold: 0.1,
				}
			);
			observer.observe(observeDom1);
			observer.observe(observeDom2);
		},
		reloadPage() {
			window.location.reload();
		},
	},
	async mounted() {
		document.body.classList.add('uni-body');
		await this.getCartList();
		if (this.isopenFreebie === 1) {
			this.cartList.forEach((item) => {
				this.neonBuyType.push(item.buyType + "-" + item.quoteCateId);
			});
			await this.getFreebie().then(() => {
				this.$nextTick(() => {
					this.swiper = new Swiper(".swiper-container", {
						// loop: true,
						speed: 2500,
						watchSlidesProgress: true,

						navigation: {
							nextEl: ".swiper-button-next",
							prevEl: ".swiper-button-prev",
						},
						pagination: {
							el: ".swiper-pagination",
							clickable: true,
						},
						breakpoints: {
							320: {
								slidesPerView: 3,
								spaceBetween: 5,
								grid: {
									rows: 2,
									fill: "row",
								},
							},
							750: {
								slidesPerView: 3,
								spaceBetween: 5,
								grid: {
									rows: 2,
									fill: "row",
								},
							},
							1000: {
								slidesPerView: 3,
								spaceBetween: 10,
								grid: {
									rows: 2,
									fill: "row",
								},
							},
							1200: {
								slidesPerView: 4,
								spaceBetween: 10,
								grid: {
									rows: 1,
									fill: "row",
								},
							},
							1400: {
								slidesPerView: 4,
								spaceBetween: 20,
								grid: {
									rows: 1,
									fill: "row",
								},
							},
							1920: {
								slidesPerView: 4,
								spaceBetween: 20,
								grid: {
									rows: 1,
									fill: "row",
								},
							},
						},
						on: {
							slideChange: () => {
								this.updateArrowHighlight();
							},
						},
					});
				});
			});
			this.getFreebieNeonColor();
			this.getFreebieNeonFont();
		}

		if (localStorage.getItem("typeIndex")) {
			let typelocal = JSON.parse(localStorage.getItem("typeIndex"));
			this.typeClick(typelocal);
		}
		let paylist = await getPayTypeList();
		this.paymentMethodId = paylist.data.payList[0].id;

		this.debounceCalcPrice = debounce(this.calcPrice, 500);

		if (this.proType === 0 && this.isLogin) {
			if (this.isStickerSite) {
				this.getCouponSticker();
			}

			//默认加载全选列表
			this.cartList.forEach((item) => {
				let type;
				if (item.buyType === 7) {
					type = 3;
				} else if (item.buyType === 9) {
					type = 2;
				}
				this.productList.push({
					cateType: type,
					id: item.quoteCateChildId,
					name: item.cateName,
					quantity: item.quantity,
					tag: `${type}-${item.quoteCateChildId}`,
					price: item.totalPrice,
				});
			});
			await this.fetchUserCouponList();

			Promise.all([getUserUsableVoucherList(), getUserReferPointsInfo()])
				.then((res) => {
					this.voucherOption = res[0].data;
					this.referPointsInfo = res[1].data || {};
					this.debounceCalcPrice();
				})
				.catch((err) => {
					console.log(err);
				});
		} else {
			this.debounceCalcPrice();
		}

		this.menuStates = Array(this.cartList.length).fill(false);

		//提前算税费
		getTaxByPrice().then((res) => {
			this.textPrice = res.data.rate;
			this.zipCode1 = res.data?.res?.postal?.code;
			this.countryName = res.data?.res?.country?.names.en;
			this.continentName = res.data?.res?.continent?.names.en; //洲
		});
		this.observeBtn();
		this.$Bus.$on("updateCartList", this.reloadPage);
	},
	created() {
		this.$store.dispatch("updateHeadFootPages");
		
		/* setInterval(() => {
			if (this.cartList?.length > 0) {
				let b = JSON.parse(sessionStorage.getItem("quoteBackParam"));
				console.log("购物车", this.cartList[0].params, b);
			}
		}, 8000); */
	},
	destroyed() {
		this.$Bus.$off("updateCartList");
	},
	beforeDestroy (){
		document.body.classList.remove('uni-body');
	}
};
</script>

<style scoped lang="scss">
.fixed {
	display: none;

	@include respond-to(mb) {
		position: fixed; /* 固定定位 */
		bottom: 0; /* 固定到页面底部10px */
		left: 0;
		right: 0;
		z-index: 999;
		border-radius: 0 !important;
		box-shadow: 0 -9px 30px 5px #333333cf;
		transition: bottom 0.3s ease;
	}
}

.fixed.notFixed {
	@include respond-to(mb) {
		position: static;
		box-shadow: none;
		z-index: auto;
		border-radius: 3px;
	}
}

.neonGiveaway_card {
	.neonGiveaway_title {
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 26px 0 21px;
		border-bottom: 1px solid #ededed;
		img {
			width: 21px;
			margin-right: 10px;
		}
		div {
			font-family: Google Sans;
			font-weight: 400;
			font-size: 24px;
			color: #000000;
		}
		b {
			position: absolute;
			top: 14px;
			right: 14px;
			font-size: 14px;
			color: #8e8e8e;
		}
	}
	.neonGiveaway_text {
		font-family: Google Sans;
		font-weight: 400;
		font-size: 16px;
		color: #000000;
		padding: 24px;
		text-align: center;
		span {
			font-weight: bold;
		}
	}
	.neonGiveaway_btn {
		padding: 5px 21px 15px;
		.v-btn {
			width: 100%;
			margin-bottom: 10px;
		}
	}
}

.swiper-container {
	width: 100%;
	height: auto;
	padding: 10px 0 110px;
	margin-left: auto;
	margin-right: auto;
	position: relative;
	z-index: 0;
}
.swiper-slide {
	text-align: center;
	font-size: 18px;
	background: #fff;
	height: 100%;

	/* Center slide text vertically */
	display: -webkit-box;
	display: -ms-flexbox;
	display: -webkit-flex;
	display: flex;
	-webkit-box-pack: center;
	-ms-flex-pack: center;
	-webkit-justify-content: center;
	justify-content: center;
	-webkit-box-align: center;
	-ms-flex-align: center;
	-webkit-align-items: center;
	align-items: center;
	transition-property: all;
}
.swiper-pagination {
	display: none;
}

.swiper-button-prev,
.swiper-button-next {
	position: absolute;
	top: 31%;
	transform: translateY(-50%);
	z-index: 10;
	width: 17px;
	height: 27px;
	color: #bbbbbb;

	&::after {
		font-size: 27px;
		font-weight: bold;
	}
}

.swiper-button-prev {
	left: 10px;
}

.swiper-button-next {
	right: 10px;
}

.neonGiveaway {
	position: relative;

	.neonGiveaway_banner {
		.neonGiveaway_banner_satisfy {
			background: url("https://static-oss.gs-souvenir.com/web/quoteManage/20241031/Christmas_Activity_Banner2-PC_2065WK4h7r.png") no-repeat;
			height: 57px;
			background-size: cover;
		}
		.neonGiveaway_banner_dissatisfy {
			background: url("https://static-oss.gs-souvenir.com/web/quoteManage/20241031/Christmas_Activity_Banner1-PC_2065pkZsat.png") no-repeat;
			background-size: cover;
			height: 57px;
			color: #fff;
			display: flex;
			justify-content: center;
			align-items: center;
			font-family: Google Sans;
			font-weight: 400;
			font-size: 22px;
			padding-left: 95px;

			span {
				font-family: Impact;
				font-weight: 400;
				font-size: 39px;
				background: linear-gradient(176deg, rgba(255, 234, 0), #ffd436, rgba(255, 162, 0));
				-webkit-background-clip: text;
				-webkit-text-fill-color: transparent;
				margin: 0 10px;
			}
		}
	}
	.neonGiveaway_outer {
		position: relative;
		width: 100%;
		margin: 0 auto;
		overflow: hidden;

		.neonGiveaway_list {
			position: relative;
			width: 100%; /* 设置父容器宽度为100% */
			max-width: 1300px; /* 设置最大宽度为1300px */
			margin: 0 auto;
			overflow: hidden;

			.iconMb {
				display: none;
			}

			.swiper-slide {
				display: flex;
				flex-wrap: wrap;
				justify-content: left;

				.slide-img {
					background-color: #539271;
					padding: 7px;
					width: 100%;
					img {
						max-height: 260px;
						height: 260px;
						object-fit: contain;
					}
				}
				.slide-text {
					width: 100%;
					text-align: left;
					padding: 16px 20px 23px;
					font-family: Google Sans;
					font-weight: 400;
					font-size: 16px;
					color: #333333;
					line-height: 30px;

					.slide-text-name {
						font-family: Google Sans;
						font-weight: 400;
						font-size: 18px;
						color: #000000;
					}
					.slide-text-price {
						display: flex;

						span:first-child {
							color: red;
						}
						span:last-child {
							color: #8e8e8e;
							text-decoration: line-through;
						}
					}

					.customization {
						display: flex;
						justify-content: space-between;
						.customization_text {
							display: flex;
							width: 175px;
						}
						.customization_color {
							position: relative;

							.colorSelect {
								width: 80px;
								border: 1px solid #8e8e8e;
								border-radius: 3px;
								display: flex;
								justify-content: space-between;
								padding: 0 6px;
								align-items: center;

								span {
									width: 20px;
									height: 20px;
								}
								// .icon-zuojiantou1{
								// 	transform: rotate(269deg);
								// }
							}
						}
					}
					.customization_font {
						display: flex;
						margin-top: 5px;
						::v-deep .v-text-field__details {
							display: none;
						}
						.customization_color {
							position: relative;
							display: flex;

							.colorSelect {
								// width: 227px;
								border: 1px solid #8e8e8e;
								border-radius: 3px;
								display: flex;
								justify-content: space-between;
								padding: 0 6px;
								align-items: center;

								span {
									width: 20px;
									height: 20px;
								}
								// .icon-zuojiantou1{
								// 	transform: rotate(269deg);
								// }
							}

							.font_img {
								height: 25px;
								width: 100px;
								object-fit: contain;
								text-align: left;
							}
						}
						.colorSelectList {
							left: -19px;
							.list_div {
								grid-template-columns: repeat(3, 1fr);
								height: 162px;

								.color_div {
									div {
										padding: 0 2px;
									}
								}

								.icon {
									right: -1px;
									background: $color-primary;
									width: 21px;
									height: 15px !important;
									top: 0;
									left: auto;
									line-height: 13px;
									border-radius: 0 5px;
									text-align: center;
									b {
										font-size: 10px;
									}
								}
							}
							// .icon-cha{
							// 	left: 80px;
							// 	bottom: 10px;
							// }

							&::before {
								content: "";
								width: 0;
								height: 0;
								transform: translateX(-50%);
								left: 51%;
								position: absolute;
								top: -20px;
								border: 10px transparent solid;
								border-bottom-color: $color-primary;
								box-shadow: rgba(4, 0, 0, 0.13);
							}

							&::after {
								content: "";
								width: 0;
								height: 0;
								transform: translateX(-50%);
								left: 51%;
								position: absolute;
								top: -17px;
								border: 9px transparent solid;
								border-bottom-color: #fff;
							}
						}
					}
				}

				.icon-check {
					padding: 4px 11px;
					position: absolute;
					top: 0;
					right: 0;
					background: #3b55ca;
					color: #fff;
					border-radius: 0 0 0 10px;
				}
			}

			.swiper-slide:hover {
				border: 1px solid #539271;
			}
		}
	}

	.colorSelectList {
		width: 306px;
		position: absolute;
		left: -207px;
		top: 123%;
		z-index: 10;
		background: #ffff;
		border: 1px solid $color-primary;
		padding: 10px;
		border-radius: 3px;
		box-shadow: 0px 5px 21px 1px #ccc;

		.icon-cha {
			// position: relative;
			// left: 41px;
			// bottom: 10px;
			font-size: 10px;
			color: #999999;
			cursor: pointer;
		}

		.color_div {
			border: 1px solid;
			padding: 1px;
			width: 100%;
			height: 100%;
			border-radius: 5px;
			position: relative;
			cursor: pointer;
			div {
				height: 27px !important;
			}
			.icon {
				position: absolute;
				top: 2%;
				left: 24%;

				.icon-cc-yes-crude {
					color: #fff;
				}
			}
		}
		.color_defaultBorder {
			border-color: #e9e9e9;
		}
		.color_activeBorder {
			border-color: $color-primary;
		}

		.list_div {
			display: grid;
			grid-template-columns: repeat(7, 1fr);
			row-gap: 10px;
			column-gap: 10px;
			margin-top: 10px;
			height: 72px;
			overflow: auto;
		}

		// div:first-child{
		// 	text-align: center;
		// }

		&::before {
			content: "";
			width: 0;
			height: 0;
			transform: translateX(-50%);
			left: 79%;
			position: absolute;
			top: -20px;
			border: 10px transparent solid;
			border-bottom-color: $color-primary;
			box-shadow: rgba(4, 0, 0, 0.13);
		}

		&::after {
			content: "";
			width: 0;
			height: 0;
			transform: translateX(-50%);
			left: 79%;
			position: absolute;
			top: -17px;
			border: 9px transparent solid;
			border-bottom-color: #fff;
		}
	}
}

.tips-text {
	font-size: 16px;
	color: #de3500;
	text-align: center;
	margin-bottom: 12px;
	@include respond-to(mb) {
		font-size: 12px;
	}
}

::v-deep .v-text-field--outlined.v-input--dense.v-text-field--single-line > .v-input__control > .v-input__slot,
.v-text-field--outlined.v-input--dense.v-text-field--outlined > .v-input__control > .v-input__slot {
	min-height: 27px !important;
	@include respond-to(mb) {
		min-height: 20px !important;
	}
}

.comments-textarea {
	::v-deep .v-textarea textarea {
		font-size: 14px !important;
	}

	::v-deep .v-input__slot {
		min-height: 150px;
		width: 350px;
	}

	@include respond-to(mb) {
		::v-deep .v-textarea textarea {
			font-size: 12px !important;
		}
		::v-deep .v-input__slot {
			min-height: 110px;
			width: 59vw;
		}
	}
}

.icon-wenhao1 {
	color: rgb(157, 154, 154);
	position: relative;
	display: inline-block;
}

.tooltip-text {
	visibility: hidden;
	background-color: rgba(0, 0, 0, 0.7);
	color: #fff;
	text-align: center;
	border-radius: 6px;
	padding: 5px;
	position: absolute;
	z-index: 1;
	bottom: 125%;
	left: 50%;
	margin-left: -60px;
	opacity: 0;
	transition: opacity 0.3s;
}

.icon-wenhao1:hover .tooltip-text {
	visibility: visible;
	opacity: 1;
	width: 350px;
}

.codeButton {
	border-radius: 0px 5px 5px 0;
	height: 34px !important;
	background: #3491ee !important;
	color: #fff;
	// margin-left: 20px;
	@include respond-to(mb) {
		min-height: 30px !important;
		line-height: 4px;
		width: 75px;
	}
}

.codeInput {
	width: 200px;
	position: relative;
	left: 5px;
	flex: 0.4;

	::v-deep .v-input__control {
		min-height: 34px !important;
	}

	::v-deep .el-input__inner {
		border-radius: 5px 0 0 5px;
	}

	@include respond-to(mb) {
		width: 120px;
		::v-deep .el-input__inner {
			border-radius: 5px 0 0 5px;
			height: 30px;
		}
	}
}

.pens-discount {
	.discount-item-div {
		display: flex;

		.discount {
			display: flex;
			align-items: center;

			& > div:first-child {
				flex: 0 0 380px;
			}
		}

		.con {
			color: #de3500;
			width: 100%;
			text-align: right;
		}

		& > div:first-child {
			flex: 0 0 187px;
		}

		@include respond-to(mb) {
			flex-wrap: wrap;
			& > div:first-child {
				flex: 0 0 210px;
			}

			.discount {
				font-size: 12px;

				& > div:first-child {
					flex: 0 0 280px;
				}

				.con {
					text-align: right;
				}
			}
		}
	}

	.discount {
		& > div:first-child {
			flex: 0 0 314px;
		}
	}

	.radio-group-div {
		background: #fff;
		padding: 10px;
		border: 1px solid #d0d5dd;
		border-radius: 4px;
		margin-top: 10px;

		::v-deep .v-input--selection-controls {
			margin-top: 0;
		}

		.v-radio {
			::v-deep .v-label {
				display: block;
			}
		}

		@media screen and (max-width: $mb-width) {
			background: #f2f4f5;
			border-color: #d0d5dd;

			.v-radio {
				::v-deep .v-label {
					display: block;
				}
			}
		}
	}
}

.grayBack {
	background-color: #a7a7a7;
	filter: grayscale(1);
}

.freeText {
	white-space: nowrap;
	color: #f01e1e;
	font-size: 12px;
	line-height: 1.6em;
	//padding-top: 0.3em;
}

.freeText1 {
	white-space: nowrap;
	color: #f01e1e;
	font-size: 13px;
	line-height: 2.4em;
	//padding-top: 0.3em;
}

.qty-card::-webkit-scrollbar {
	width: 5px;
	height: 100%;
}

.qty-card::-webkit-scrollbar-thumb {
	width: 5px;
	height: 50px;
	background: #dbdbdb;
	border-radius: 3px;
}

.qty-card {
	background: #fff;
	min-width: 200px;
	padding: 15px;
	border-radius: 5px;
	left: 50%;
	transform: translateX(-50%);
	//min-height: 15vw;
	overflow-y: auto;

	&::before {
		content: "";
		width: 0;
		height: 0;
		transform: translateX(-50%);
		left: 50%;
		position: absolute;
		top: -20px;
		border: 10px transparent solid;
		border-bottom-color: #e8e8e8;
		box-shadow: rgba(4, 0, 0, 0.13);
	}

	&::after {
		content: "";
		width: 0;
		height: 0;
		transform: translateX(-50%);
		left: 50%;
		position: absolute;
		top: -16px;
		border: 8px transparent solid;
		border-bottom-color: #ffffff;
	}

	label {
		flex: 1;
		min-width: 150px;
		text-align: right;
		margin-right: 8px;
	}

	.qty-list {
		width: 100%;

		.unit {
			min-width: 12%;
			text-align: left;
			padding-left: 10px;

			&.hasGift {
				min-width: 25%;
			}
		}

		.qty {
			width: 70px;
		}

		.gift-input {
			&.unvalid {
				::v-deep .v-input__slot {
					border: 1px solid #f01e1e !important;
				}
			}
		}
	}

	::v-deep {
		.v-btn .v-btn__content {
			text-transform: capitalize;
		}

		.v-text-field {
			width: 4vw;
		}

		.v-text-field.v-text-field--solo.v-input--dense > .v-input__control {
			min-height: 27px;
			max-width: 80px;

			.v-input__icon {
				width: 10px;
				min-width: 0;
			}
		}

		.v-text-field.v-text-field--solo:not(.v-text-field--solo-flat) > .v-input__control > .v-input__slot {
			padding: 0 5px;
			border: 1px solid #ababab;
			box-shadow: none;

			input[type="number"] {
				border: none !important;
			}
		}

		.v-text-field.v-text-field--solo.v-input--dense > .v-input__control .v-input__icon i {
			font-size: 14px;
		}
	}
}

::v-deep .v-text-field__slot {
	font-size: 16px;
	@include respond-to(mb) {
		font-size: 14px;
	}
}

.pic-item {
	display: flex;
	align-items: flex-start;

	span {
		flex: 0 0 40px;
		margin-right: 4px;
	}

	img {
		max-width: 80px;
		max-height: 80px;
		object-fit: contain;
		cursor: zoom-in;
	}

	a {
		color: $color-primary;

		&:hover {
			text-decoration: underline;
		}
	}
}

.bps-container {
	min-height: 60vh;

	::v-deep {
		.v-radio .v-label {
			color: #333333;
		}

		.v-text-field.v-text-field--solo.v-input--dense > .v-input__control {
			min-height: 27px;
		}

		.v-text-field.v-text-field--solo:not(.v-text-field--solo-flat) > .v-input__control > .v-input__slot {
			box-shadow: 0px 2px 4px 0px rgba(204, 204, 204, 0.35);
			// border: 1px solid #d0d5dd;
		}
	}
}

.viewMore {
	font-size: 15px;
	display: flex;
	justify-content: left;
	color: #0066cc;
	padding-right: 130px;
	margin-top: 10px;

	&:hover {
		cursor: pointer;
	}

	b {
		font-size: 12px;
	}
}

.buJu1 {
	color: #969cb5;
}

.buJu2 {
	color: #0066cc !important;
}

.totle {
	border-top: 1px solid #dbdbdb;
	padding-top: 11px;
	font-size: 24px;

	.totleName {
		display: flex;
		justify-content: center;
		margin-bottom: 10px;
	}

	span {
		font-size: 18px;
		font-weight: bold;
		margin-left: 10px;
	}
}

.dark-theme {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	grid-column-gap: 42px;

	& > div {
		position: relative;
	}

	.topName {
		display: flex;
		align-items: center;
		align-items: flex-end;
		color: #171719;
		padding-left: 4.5%;
		max-width: 650px;
		word-break: break-word;
		min-height: 38px;
	}

	.cart-item .item-p.t8 {
		position: absolute;
		top: 0;
		right: 0;
		.t {
			position: absolute;
			clip: rect(0, 0, 0, 0);
		}
	}

	.cart-item {
		padding-bottom: 10px;
		margin-bottom: 0px;
		border-bottom: 1px solid #e5e5e5;
		align-items: center;

		.t {
			text-align: center;
			word-break: break-all;
			height: 38px;
		}

		&:last-child {
			padding-bottom: 10px;
			margin-bottom: 0px;
			border-bottom: 1px solid #e5e5e5;
		}

		.item-p.t2 img {
			/* height: 100%; */
		}

		.t1 {
			width: 4% !important;
		}

		.t2 {
			width: 23% !important;
		}

		.t4 {
			width: 12% !important;
		}

		.t6 {
			width: 12% !important;
		}

		.tRush {
			width: 14% !important;
		}

		.t7 {
			width: 10% !important;
		}
	}

	.item-p:not(.t3) {
		.c {
			height: 45px;
		}
	}
}

::v-deep.dark-theme .content .swiper__content {
	width: 150px;
}

.max-height {
	max-height: 100px;
	overflow: hidden;
}

.container {
	padding: 12px 0;
}

.title {
	padding: 10px 0;
	border-bottom: 1px solid $color-primary;

	.title_switch {
		margin-left: 50px;
		font-size: 15px;
		cursor: pointer;

		span:first-child {
			flex: 1;
			background: #ccc;
			border-radius: 30px;
			padding: 2px 6px;
		}

		b {
			color: #d1754a;
			font-size: 20px;
			position: relative;
			top: 2px;
			left: 5px;
		}

		.tooltip-custom {
			opacity: 1;
			border: 1px solid;
			width: 100px;
		}

		.wenhaoText {
			opacity: 1;
			border: 1px solid;
			width: 100px;
		}
	}
}

.top {
	border-bottom: 1px solid #e5e5e5;
}

.cart-item {
	display: flex;
	justify-content: space-between;
	padding-bottom: 30px;
	margin-bottom: 20px;
	border-bottom: 1px solid #e5e5e5;

	.t_check span {
		display: none;
	}

	.item-p {
		.content {
			display: block;
		}

		.img-cateName {
			.c_checkbox {
				display: none;
			}
		}

		&:last-child {
			margin-right: 0;
		}

		&.pb {
			padding-bottom: 3%;
		}

		&.t1 {
			width: 1%;
			display: flex;
			align-items: flex-start;
			padding-top: 4.55%;
		}

		&.listType2 {
			//align-items: start;
			//padding-top: 4.55%;
		}

		&.t2 {
			//width: 20%;
			width: 12%;

			img {
				width: 100%;
				height: 138px;
				object-fit: contain;
			}

			.itemNo {
				cursor: pointer;
				margin-top: 10px;
				text-align: center;
				color: #ccc;
			}
		}

		&.t3 {
			width: 25%;
			word-break: break-word;

			.t {
				//padding-left: 100px;
				text-align: left;
			}
		}

		&.t4 {
			width: 8%;

			.showMb {
				display: none;
			}

			.cateName {
				display: none;
			}
		}

		&.t5 {
			width: 10%;
		}

		&.tDark {
			width: 15% !important;
		}

		&.t6 {
			width: 6%;
		}

		&.t7 {
			width: 7%;
		}

		&.t8 {
			width: 3%;
			.c {
				text-align: right;
			}
		}
	}

	.item-p:not(.t3) {
		.check {
			height: unset !important;
		}

		.c {
			margin-top: 5px;

			.discount {
				white-space: pre;
			}
		}
	}

	.tRush {
		width: 7%;
	}

	.t {
		text-align: center;
	}

	.c {
		text-align: center;
	}

	.totalPrice {
		color: #de3500;

		label {
			white-space: nowrap;
		}
	}

	.param-item {
		font-size: 14px;
		text-align: left;

		.label {
			flex-shrink: 0;
			width: 130px;
			margin-right: 10px;
			text-align: left;
			color: #999999;
		}
	}

	.originUnitPrice {
		font-size: 14px;
		color: #999999;
		font-weight: 400;
	}

	.discountUnitPrice {
		font-size: 16px;
		margin-left: 4px;
	}

	.discount {
		color: #de3500;
	}
}

.cart-item .item-p.t6 {
	.c {
		position: relative;

		::v-deep {
			.v-text-field.v-text-field--solo.v-input--dense > .v-input__control {
				min-height: 27px;
				min-width: 70px;

				.v-input__icon {
					width: 10px;
					min-width: 0;

					i {
						font-size: 14px;
					}
				}
			}
		}

		.qtyCard {
			position: absolute;
			background: #fff;
			min-width: 200px;
			padding: 15px;
			border: 2px solid #c1c1c1;
			border-radius: 5px;
			left: 50%;
			transform: translateX(-50%);

			label {
				min-width: 150px;
				white-space: nowrap;
				text-align: right;
				margin-right: 5px;
			}
		}
	}
}

.subtotal-con {
	display: flex;
	flex-direction: column;
	align-items: flex-end;
	font-size: 18px;

	.subtotal {
		width: 696px;
		// background: #f2f4f5;
		padding: 14px 19px;
	}

	.shoppingBtn {
		width: 314px;
		height: 40px;
		background: linear-gradient(180deg, #ffffff 0%, #5daeff 0%, #1279df 100%);
		border-radius: 4px;
		color: #fff;
		margin-left: 14px;
		font-size: 18px;

		&.disabled {
			background: #ccc;
			pointer-events: none;
			cursor: not-allowed;
		}
	}

	.checkoutBtn {
		width: 314px;
		height: 40px;
		background: linear-gradient(180deg, #ffffff 0%, #f9a128 0%, #ff6c00 100%);
		border-radius: 4px;
		color: #fff;
		margin-left: 14px;
		font-size: 18px;

		&.disabled {
			background: #ccc;
			pointer-events: none;
			cursor: not-allowed;
		}
	}

	.sub-item {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding-bottom: 10px;

		.label {
			// width: 150px;
			flex-shrink: 0;
			text-align: left;
		}

		.con {
			flex: 1;
			text-align: right;
		}
	}

	.discount-item {
		.v-radio {
			flex: 0 0 170px;
			margin-bottom: 0;
			margin-right: 10px;
		}

		.discount {
			display: flex;
			justify-content: space-between;
			align-items: center;
			flex: 1;

			& > div:first-child {
				flex: 0 0 270px;
			}
		}
	}

	.totalPrice {
		color: #de3500;
		font-size: 24px;
	}
}

.border-bottom {
	border-bottom: 1px solid #e5e5e5;
}

@media screen and (max-width: $pad-width) {
	.cart-item {
		.item-p {
			&.t2 {
				width: 15%;
			}
		}
	}
}

@media screen and (max-width: $mb-width) {
	.customizationMb {
		display: flex;
		border: 1px solid #e0e0e0;
		margin-top: 15px;

		.icon-check {
			padding: 4px 11px;
			position: absolute;
			right: 0;
			background: #3b55ca;
			color: #fff;
			border-radius: 0 0 0 10px;
		}

		.slide-img {
			flex: 0.6;
			img {
				width: 100%;
				height: 100%;
				object-fit: contain;
			}
		}
		.slide-text {
			flex: 1;
			padding: 3.4667vw 3.2vw;
			color: #333333;
			line-height: 20px;
			font-size: 11px;

			.slide-text-name {
				font-family: Google Sans;
				font-weight: 400;
				font-size: 13px;
				color: #000000;
				line-height: 20px;
			}

			.slide-text-price {
				span:first-child {
					color: red;
				}
				span:last-child {
					color: #8e8e8e;
					text-decoration: line-through;
				}
			}

			.customization {
				display: flex;
				justify-content: space-between;
				margin: 5px 0;
				.customization_text {
					display: flex;
					align-items: center;
					width: 40.3333vw;
				}
				.customization_color {
					position: relative;

					.colorSelect {
						width: 13.6667vw;
						height: 32px;
						border: 1px solid #8e8e8e;
						border-radius: 3px;
						display: flex;
						justify-content: space-between;
						padding: 0 6px;
						align-items: center;

						span {
							width: 20px;
							height: 20px;
						}
						.icon-zuojiantou1 {
							transform: rotate(269deg);
						}
					}
				}

				.colorSelectList {
					width: 300px;
					position: absolute;
					left: inherit;
					right: 0;
					top: 43px;
					z-index: 10;
					background: #ffff;
					border: 1px solid $color-primary;
					padding: 10px;
					border-radius: 3px;
					box-shadow: 0px 5px 21px 1px #ccc;
					// .icon-cha{
					// 	position: relative;
					// 	left: 97px;
					// 	bottom: 4px;
					// }

					.color_div {
						border: 1px solid;
						padding: 1px;
						width: 100%;
						height: 100%;
						border-radius: 5px;
						position: relative;
						div {
							height: 27px !important;
						}
						.icon {
							position: absolute;
							top: 15%;
							left: 24%;

							.icon-cc-yes-crude {
								color: #fff;
								font-size: 16px;
							}
						}
					}
					.color_defaultBorder {
						border-color: #e9e9e9;
					}
					.color_activeBorder {
						border-color: $color-primary;
					}

					.list_div {
						display: grid;
						grid-template-columns: repeat(7, 1fr);
						row-gap: 10px;
						column-gap: 10px;
						margin-top: 10px;
						height: 72px;
						overflow: auto;
					}

					// div:first-child{
					// 	text-align: center;
					// }

					&::before {
						content: "";
						width: 0;
						height: 0;
						transform: translateX(-50%);
						left: 91%;
						position: absolute;
						top: -20px;
						border: 10px transparent solid;
						border-bottom-color: $color-primary;
						box-shadow: rgba(4, 0, 0, 0.13);
					}

					&::after {
						content: "";
						width: 0;
						height: 0;
						transform: translateX(-50%);
						left: 91%;
						position: absolute;
						top: -17px;
						border: 9px transparent solid;
						border-bottom-color: #fff;
					}
				}
			}
			.customization_font {
				display: flex;
				margin-top: 5px;
				::v-deep .v-text-field__details {
					display: none;
				}
				.customization_color {
					position: relative;
					display: flex;
					align-items: center;

					.colorSelect {
						width: 100%;
						border: 1px solid #8e8e8e;
						border-radius: 3px;
						display: flex;
						justify-content: space-between;
						padding: 2px 6px;
						align-items: center;

						span {
							width: 20px;
							height: 20px;
						}
						.icon-zuojiantou1 {
							margin-left: 10px;
						}
					}

					.font_img {
						height: 25px;
						width: 100px;
						object-fit: contain;
						text-align: left;
					}
				}
				.colorSelectList {
					left: auto;
					right: -19px;
					top: 130%;

					.list_div {
						grid-template-columns: repeat(3, 1fr);
						height: 162px;

						.color_div {
							div {
								padding: 0 2px;
								img {
									object-fit: contain;
								}
							}
						}

						.icon {
							right: -1px;
							background: $color-primary;
							width: 21px;
							height: 15px !important;
							top: 0;
							left: auto;
							line-height: 13px;
							border-radius: 0 5px;
							text-align: center;
							b {
								font-size: 10px;
							}
						}
					}
					// .icon-cha{
					// 	left: 133px;
					// 	bottom: 7px;
					// }

					&::before {
						content: "";
						width: 0;
						height: 0;
						transform: translateX(-50%);
						left: 58%;
						position: absolute;
						top: -20px;
						border: 10px transparent solid;
						border-bottom-color: $color-primary;
						box-shadow: rgba(4, 0, 0, 0.13);
					}

					&::after {
						content: "";
						width: 0;
						height: 0;
						transform: translateX(-50%);
						left: 58%;
						position: absolute;
						top: -17px;
						border: 9px transparent solid;
						border-bottom-color: #fff;
					}
				}
			}
		}
	}
	.title {
		align-items: center;
		.title_switch {
			margin-left: 0;
		}
	}
	.swiper-pagination {
		display: block;
		// width: auto !important;
	}
	.swiper-container {
		padding: 10px 0 20px;
	}
	.swiper-button-prev,
	.swiper-button-next {
		position: absolute;
		top: 98%;
		transform: translateY(-50%);
		z-index: 10;
		width: 17px;
		height: 27px;
		color: #bbbbbb;

		&::after {
			display: none;
			font-size: 27px;
			font-weight: bold;
		}
	}

	.neonGiveaway {
		.neonGiveaway_banner {
			.neonGiveaway_banner_satisfy {
				background: url("https://static-oss.gs-souvenir.com/web/quoteManage/20241101/Christmas_Activity_Banner2-MB_2035018BmnMH.png") no-repeat;
				height: 8.5333vw;
				width: 100%;
				background-size: contain;
			}
			.neonGiveaway_banner_dissatisfy {
				background: url("https://static-oss.gs-souvenir.com/web/quoteManage/20241101/Christmas_Activity_Banner1-MB_203501cKK7pE.png") no-repeat;
				background-size: cover;
				height: 8.5333vw;
				width: 100%;
				font-size: 2.9333vw;
				padding-left: 0;

				span {
					font-size: 5.3333vw;
					margin: 0 1.3333vw;
				}
			}
		}
		.neonGiveaway_outer {
			position: relative;
			width: 100%;
			margin: 0 auto;
			overflow: hidden;

			.neonGiveaway_list {
				position: relative;
				width: 100%; /* 设置父容器宽度为100% */
				max-width: 1300px; /* 设置最大宽度为1300px */
				margin: 0 auto;
				overflow: hidden;

				.iconMb {
					display: flex;
					justify-content: center;
					align-items: center;
					position: relative;
					top: 20px;

					.neonGiveaway_jianto {
						height: 30px;
						width: 30px;
						margin: 0 10px;
						position: relative;
						z-index: 11;
					}
					.swiper-pagination {
						position: static;
						width: auto;
					}
					.jianto-bg {
						background: url("https://static-oss.gs-souvenir.com/web/quoteManage/20241102/%E7%AE%AD%E5%A4%B4_41_203502yKajEQ.png") no-repeat;
					}
					.jianto-bg2 {
						background: url("https://static-oss.gs-souvenir.com/web/quoteManage/20241102/%E7%AE%AD%E5%A4%B4_41_203502yKajEQ.png") no-repeat;
						transform: rotate(180deg);
					}
					.jianto-bg-blue {
						background: url("https://static-oss.gs-souvenir.com/web/quoteManage/20241102/333333_203502MNccAA.png") no-repeat;
						transform: rotate(180deg);
						background-size: cover;
						position: relative;
						z-index: 11;
					}
					.jianto-bg2-blue {
						background: url("https://static-oss.gs-souvenir.com/web/quoteManage/20241102/333333_203502MNccAA.png") no-repeat;
						transform: rotate(0);
						background-size: cover;
						position: relative;
						z-index: 11;
					}
				}

				.swiper-slide {
					border: 1px solid #e0e0e0;

					.slide-img {
						background-color: #fff;
						padding: 0;
						width: 100%;
						img {
							max-height: 250px;
							height: 95px;
							object-fit: cover;
						}
					}
					.slide-text {
						width: 100%;
						text-align: left;
						padding: 12px 0 12px 10px;
						font-family: Google Sans;
						font-weight: 400;
						font-size: 16px;
						color: #333333;
						line-height: 20px;

						.slide-text-name {
							font-family: Google Sans;
							font-weight: 400;
							font-size: 13px;
							color: #000000;
							line-height: 20px;
						}
						.slide-text-price {
							font-size: 11px;

							span:first-child {
								color: red;
							}
							span:last-child {
								color: #8e8e8e;
								text-decoration: line-through;
							}
						}
						.slide-text-size {
							font-size: 11px;
						}
						.slide-text-qty {
							font-size: 11px;
						}
					}
					.icon-check {
						padding: 1px 6px;
						position: absolute;
						top: 0px;
						right: 0px;
						border-radius: 0 0 0 10px;
						font-size: 14px;
					}
				}

				.swiper-slide:hover {
					border: 1px solid #539271;
				}
			}
		}
	}

	.wenhaoText {
		font-size: 3.2vw;
	}
	.qty-card {
		label {
			min-width: 28vw;
		}

		.gift {
			white-space: nowrap;
		}

		::v-deep {
			.v-text-field {
				width: 21vw;
			}
		}
	}
	.qty-list {
		width: 100%;

		.unit {
			padding-left: 0 !important;
			text-align: center;
		}

		.qty {
			width: 70px;
		}

		.unit {
			&.hasGift {
				min-width: 50px !important;
			}
		}
	}
	.cateName_div {
		::v-deep {
			.v-text-field.v-text-field--solo.v-input--dense > .v-input__control {
				min-height: 24px;
				width: 70px;

				.v-input__icon {
					width: 10px;
					min-width: 0;

					i {
						font-size: 12px;
					}
				}

				.v-input__slot input {
					text-align: center;
				}
			}
		}
	}

	.img-cateName2 {
		display: none !important;
	}

	.img-cateName {
		cursor: pointer;
		display: block !important;
		width: 251%;
		color: #171719;
		font-size: 13.5px;
		margin-bottom: 10px;
		text-align: initial !important;

		.t_check {
			display: flex;
			//margin-top: 20px;
			span {
				display: block;
				white-space: nowrap;
				max-width: 300px;
				overflow: hidden;
				text-overflow: ellipsis;
			}
		}

		.c_checkbox {
			display: flex !important;
		}
	}

	#app {
		min-height: auto;
	}

	::v-deep .v-main__wrap {
		background: #dfdfdf;
	}

	.containertext {
		// background-color: white;
		border-radius: 5px;
		padding: 0 10px;
	}

	.bps-container {
		padding: 0 5.5px;
	}

	.v-application {
		.py-10 {
			background: #dfdfdf;
		}

		.py-5 {
			padding-bottom: 15.5px !important;
			/* padding-top: 0 !important; */
		}
	}

	.containerTitle {
		display: none;
	}

	::v-deep .bps-container .v-radio .v-label {
		font-size: 12px;
	}

	.top {
		//border-bottom: 1px solid #e5e5e5;
		> div button {
			width: 30px;
			height: 30px;
		}
	}

	.dark-theme {
		display: grid;
		grid-template-columns: repeat(1, 1fr);
		grid-column-gap: 0px;

		::v-deep {
			.content {
				.swiper__content {
					width: 155px;
					height: 120px;
				}
			}
		}

		.topName {
			display: none;
		}

		.cart-item {
			padding-bottom: 0px;
			margin-bottom: 14px !important;

			//&:last-child {
			//	padding-bottom: 20px !important;
			//}

			.item-p {
				&.t1 {
					width: 7%;
					order: 1;
					align-self: center;
				}

				&.t2 {
					width: 35% !important;
					padding-bottom: 10px;

					img {
						height: 84px;
						width: 100%;
						height: 100%;
						border-radius: 5px;
					}

					.c {
						padding: 10px 5.3333vw;
					}
				}

				&.t3 {
					order: 5;
					width: 100%;
					text-align: left;
					//margin-left: 9.6vw;

					.t {
						padding-left: 0;
						margin: 14px 0;
						font-size: 14px;
					}

					.c {
						.param-item {
							font-size: 12px;

							.label {
								width: 110px;
								text-align: left;
							}
						}
					}
				}

				&.t4 {
					width: 55% !important;
					order: 3;
					font-size: 12px;
					/* padding-left: 10px; */
					align-self: center;
					position: relative;
					top: 28px !important;

					&.rush {
						top: 33px !important;
					}

					.cateName {
						position: relative;
						right: 28.7333vw;
					}

					.showPc {
						display: none;
					}

					.showMb {
						display: block;
						margin-bottom: 0px;
					}

					.cateName_div {
						/* text-align: left; */
						display: flex;
						justify-content: space-between;

						div {
							text-align: right;

							/* .nu{
                                                                                                                                                      text-align: left;
                                                                                                                                                  } */
						}
					}
				}

				&.t5 {
					order: 6;
					display: none;
				}

				&.t6 {
					order: 7;
					display: none;
				}

				&.t7 {
					order: 8;
					display: none;
				}

				&.t9 {
					display: none;
				}

				&.tRush {
					display: none;
				}
			}

			.t4 {
				width: 21% !important;
			}
		}
	}

	.totle {
		border-top: 1px solid #dbdbdb;
		padding-top: 11px;
		display: block;
		font-size: 3.6vw;

		.totleName {
			display: flex;
			justify-content: center;
			align-items: center;

			.tt {
				margin-top: 1px;
			}

			span {
				margin-left: 5px;
			}
		}

		.but {
			grid-column-start: 1;
			grid-column-end: 3;
			text-align: center;
		}

		span {
			font-size: 2.9333vw;
			font-weight: bold;
		}
	}

	.viewMore {
		display: block;
		font-size: 12px;
		color: #0066cc;
		margin-left: 3px;
		margin-top: 0px;
	}

	.more,
	.less {
		transform: rotate(0deg);
		position: relative;
		top: 1px;
		margin-left: 0px;
	}

	.max-height {
		max-height: 0px;
		overflow: hidden;
	}

	.cart-item {
		flex-wrap: wrap;
		font-size: 12px;
		padding: 0 0 10px 0;
		margin-bottom: 14px;

		.discountUnitPrice {
			font-size: 14px;
		}

		.originUnitPrice {
			font-size: 12px;
		}

		.item-p {
			.img-cateName {
				margin-bottom: 0;
			}

			.content {
				display: flex !important;
				justify-content: center;
			}

			&.t1 {
				width: 7%;
				order: 1;
				align-self: center;
				display: none;
			}

			&.t2 {
				width: 37%;
				order: 2;
				align-self: center;
				//overflow: vi;
				overflow: visible;

				.itemNo {
					font-size: 12px;
					// overflow: hidden;
					// text-overflow: ellipsis;
					// white-space: nowrap;
				}

				.c {
					display: flex;
					justify-content: center;
					background: #fafafa;
					border: 1px solid #e5e5e5;
					border-radius: 4px;
					padding: 10px 5.3333vw;
				}

				img {
					width: 100%;
					height: 100%;
					border-radius: 5px;
				}

				.t {
					display: none;
				}
			}

			&.t3 {
				order: 5;
				width: 100%;
				text-align: left;
				//margin-left: 9.6vw;
				//padding-bottom: 10px;
				//margin-top: 10px;
				padding-bottom: 0px;
				margin-top: 0px;

				.t {
					padding-left: 0;
					margin: 14px 0;
					font-size: 14px;
				}

				.c {
					.param-item {
						font-size: 12px;

						.label {
							width: 110px;
							text-align: left;
						}
					}
				}
			}

			&.t4 {
				//width: 48%;
				width: 57%;
				order: 3;
				font-size: 12px;
				align-self: center;
				margin-right: 10px;
				position: relative;
				top: 60px;
				margin-bottom:10%;

				&.rush {
					top: 60px;
				}

				.showPc {
					display: none;
				}

				.showMb {
					display: block;
					margin-bottom: 20px;
				}

				.cateName {
					display: block;
					font-size: 14px;
					font-weight: 700;
					margin-bottom: 6px;
					position: relative;
					right: 29.7333vw;
					bottom: 1.6667vw;
					width: 211px;
				}

				.cateName_div {
					/* text-align: left; */
					display: flex;
					justify-content: space-between;
					align-items: baseline;

					div {
						text-align: right;

						/* .nu{
                                                                                                                                                      text-align: left;
                                                                                                                                                  } */
					}
				}
			}

			&.t5 {
				order: 6;
				display: none;
			}

			&.t6 {
				order: 7;
				display: none;
			}

			&.t7 {
				order: 8;
				display: none;
			}

			&.t8 {
				position: absolute;
				right: 0;
                width: auto;

				.t {
					position: absolute;
					clip: rect(0, 0, 0, 0);
				}
				.c {
					margin: 0;
				}
			}

			&.t9 {
				display: none;
			}

			&.tRush {
				display: none;
			}
		}

		.item-p:not(.t3) {
			.check {
				height: unset !important;
			}

			.c {
				height: unset;

				.discount {
				}
			}
		}
	}

	.subtotal-con {
		transition: none;
		padding: 15px;
		border-radius: 5px;
		border: none;
		margin-top: 5px;
		font-size: 12px;
		// background-color: #ffffff;

		.discount-item .discount .con {
			text-align: right;
			flex: 0 0 50px;
			font-size: 12px;
		}

		.discount-item .v-radio {
			flex: 0 0 35.3333vw;
			margin-bottom: 0;
		}

		.discount-item .discount > div:first-child {
			flex: 0 1 165px;
		}

		.discount-item .discount .v-input {
			font-size: 12px;
		}

		.subtotal {
			width: 100%;
			// background-color: #ffffff;
			padding: 0;
		}

		.shoppingBtn {
			height: 35px;
			margin-left: 0;
			font-size: 12px;
			margin-right: 5px;
		}

		.checkoutBtn {
			height: 35px;
			margin-left: 0;
			font-size: 12px;
		}

		.sub-item {
			padding-bottom: 5px;
		}

		.totalPrice {
			font-size: 4.2667vw;
		}
	}
}

::v-deep .v-application--wrap {
	min-height: auto;
}
</style>
<style lang="scss">
.qtyMenuContent {
	//min-height: 16vw;
	max-height: none !important;
	left: 48% !important;
	box-shadow: none;
	overflow: visible;
	border: 1px solid #ababab;
	@include respond-to(mb) {
		//top: 800px !important;
		left: 2.5% !important;
		max-width: 95%;
	}
}
</style>
