import {calculate} from "@/api/pins";
import {findSelectDiscount, getQuoteTime} from "assets/js/QuotePublic";

const getImageNameFromURL = (imageURL) => {
	// 获取最后一个斜杠的索引
	const lastIndex = imageURL.lastIndexOf("/");
	// 提取文件名部分
	// 返回图片名称
	return imageURL.substring(lastIndex + 1);
};

export const quoteBack = async (that) => {
	try {
		if (!sessionStorage.getItem("quoteBackParam")) {
			return false;
		}
		let cartData = JSON.parse(sessionStorage.getItem("quoteBackParam")),
			selectedData = that.selectedData;
		//回填分类信息
		selectedData["quoteCategory"] = [cartData.classificationData];
		that.generalData = await that.getCateParam(cartData.classificationData.id);
		//回填参数信息
		cartData.finaData.forEach((item) => {
			item.childList.forEach((c) => {
				c.remark = "";
				c.noShowDetail = false;
				c.isHidden = false; //不可选属性
				c.files = [];
				// c.inputNum = undefined;
			});
			try {
				if(["50% Embroidery","75% Embroidery","100% Embroidery"].includes(item.childList[0].paramName)){
					that.$store.commit("setEmbroideryCoverageValue",item.childList[0].paramName)
				}
			}catch (e) {

			}
			//回填尺寸值
			if (item.paramType === "SIZE") {
				that.$store.commit("setQuoteSizeName", item.childList[0].paramName);
				that.$store.commit("setQuoteSizeValue", item.childList[0].paramCode);
			}
			//回填email later
			if (item.paramName === "Upload Artwork & Comments") {
				that.isUpload = item.later || false;
			}
			selectedData[item.paramName] = item.childList || [];
		});
		//回填数量
		that.customQty = String(cartData.quantity);
		//棒球满赠活动回填
		that.qtyChecked = cartData.qtyChecked;
		//回填文件
		if (cartData.imageJson) {
			let imageJson = JSON.parse(cartData.imageJson),
				uploadArtworkList = [];
			imageJson.forEach((item) => {
				uploadArtworkList.push({
					secure_url: item.url,
					original_filename: getImageNameFromURL(item.url),
				});
			});
			that.uploadArtworkList = uploadArtworkList;
		}
		//回填remark
		that.remark = cartData.comments || "";

		/**
		 * 处理切换语言后的交期回填逻辑
		 * 判断当前选中的交期是否在展示的交期列表内，如果没找到，清空当前选中的交互，让用户重新选择
		 */
		//获取交期列表(经过小重量判断之后的)
		let priceParam = that.getPriceParam();
		let discountParams = that.generalData.find(item=>{
			return item.paramType === 'DISCOUNT'
		})
		let list = discountParams?.childList,name = discountParams?.paramName
		let result = await calculate(priceParam);
		//当前展示的交期列表
		let timeList = getQuoteTime(list,result.data,that.proType);
		//currentSelectTime 当前选中的交期
		let currentSelectTime = findSelectDiscount(that.selectedData);
		let find = timeList.arr.find(item=>{
			return item.id === currentSelectTime.id
		})
		if(!find){
			//清空交期
			if (name) {
				that.selectedData[name] = [];
			}
			console.log("clear time")
		}
	}catch (e) {
		console.log(e)
	}
};
