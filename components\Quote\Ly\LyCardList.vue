<template>
	<div class="lyCardList">
		<div v-if="!model.loading && model.keyword">
			<div v-if="model.list.length == 0" class="empty">0 Results for "{{ model.keyword }}"</div>
		</div>
		<div v-if="model.list.length >= 0" class="list">
			<div v-for="(item, index) in model.list" :key="index" class="li" @click.stop="openDetail(item.router, item)">
				<div v-if="item.imgJson" class="imgDiv">
					<img :src="item.imgJson.src || item.imgJson.url" :alt="item.imgJson.name" :title="item.imgJson.name" loading="lazy" />
					<div v-if="item.isTop == 1" class="ad">Ad</div>
				</div>
				<div class="rowFont">
					<div v-if="item.title" class="rowOne">{{ item.title }}</div>
					<div v-if="item.price" class="rowTwo">{{ baseModel.model.symbol }}{{ getPrice(item.price || 0) }}</div>
					<div v-if="item.salesVolume != null" class="rowThree">
						<span class="p1">{{ getSalesVolume(item.salesVolume) }}</span
						><span class="p2">sold recently</span>
					</div>
				</div>

				<!--右下购物车-->
				<div class="shopping" @click.stop="shop(item)" :disabled="!item.productStock">
					<!-- 	<b class="icon-a-Shoppingcart icon" /> -->
					<img :src="baseModel.shoppingModel.img" loading="lazy" />
				</div>

				<!--左上标签-->
				<div v-if="item.labelModel" class="label" @click.stop="aaa">
					{{ item.labelModel.title }}
				</div>
				<!--右上收藏v-if="isLogin"-->
				<div v-if="isLogin" class="collect">
					<i v-if="!item.isCollection" class="v-icon mdi mdi-heart-outline theme--light" @click.stop="collectFunc(item)" /><!--收藏-->

					<i v-if="item.isCollection" class="v-icon notranslate collectionXin isActive mdi mdi-heart theme--light iconOn" @click.stop="collectNotFunc(item)" /><!--取消收藏-->
				</div>
			</div>
		</div>
		<div class="move">
			<div class="moveTitle">You've viewed {{ getNum(model) }} of {{ model.total }} products</div>
			<div class="progress"><el-progress :percentage="getPro(model)"></el-progress></div>
			<div v-if="model.loading" class="moveLoading">
				<Loading></Loading>
			</div>
			<div v-if="model.isMove" class="btnDiv" @click="move()">
				<div class="btn">Load More</div>
			</div>
		</div>
	</div>
</template>
<script>
/*
	LyCardList 卡片列表
	更多文档: https://www.yuque.com/chaojigang-eu86m/adyym3/nevpaf4aqaxtmrl8
	语雀密码：itv8
*/
import { addShoppingCartNotLogin, addShoppingCart, designerAddCollection, removeCollectionById } from "@/api/web.js";
import { DataProcessing } from "@/utils/dataProcessing";
import { Common } from "@/utils/common";
import { baseModel } from "@/assets/quote/entity/LyCardList";
import "@/plugins/element";
let that;
export default {
	components: {},
	props: {
		model: {
			default: {
				list: [],
				isMove: false,
				total: 0,
				page: 1,
				pageSize: 10,
				loading: false,
				keyword: undefined,
			},
			type: Object,
		},
	},
	/* 	watch: {
		model: {
			handler(newModel, oldModel) {
				console.log("加载", newModel, oldModel);
			},
			immediate: true,
		},
	}, */
	watch: {
		"$store.state.currency": {
			handler(newValue) {
				this.baseModel.model = newValue;
				this.$forceUpdate();
			},
			immediate: true,
		},
	},
	computed: {
		isLogin() {
			return this.$store.getters.isLogin;
		},
		userUUID() {
			return this.$store.state.userUUID;
		},
		userId() {
			return this.$store.state.userInfo?.id;
		},
		proId() {
			return this.$store.state.proId;
		},
	},
	data() {
		return {
			baseModel: new baseModel(),
		};
	},
	mounted() {},
	created() {
		that = this;
		let cur = window.$nuxt.$store.state.currency;
		if (cur) {
			this.baseModel.model = cur;
		}
		console.log("感谢天LyCardList", cur);
	},
	methods: {
		//更多
		move() {
			this.model.loading = true;
			this.$forceUpdate();
			this.$emit("move", this.model);
		},
		openDetail(routing, item) {
			//库存0  提示不执行
			if (item?.productStock <= 0) {
				this.$toast.error("Insufficient inventory");
				return;
			}
			//isPublish   1上架    0下架    下架不允许跳转详情
			if (item?.isPublish == 0) {
				this.$toast.error("SOLD OUT");
				return;
			}

			this.$emit("openDetail", routing);
		},
		//跳转
		jump(str) {
			if (str) {
				// this.$router.push({
				// 	path: "/" + str,
				// 	//query: data,
				// });
				//gsjj.o2o.co
				if (this.proId == 1) {
					let targetWindow = window.opener || window.parent;
					targetWindow.postMessage({ type: "toCart" }, window.origin);
				} else {
					let targetWindow = window.opener || window.parent;
					targetWindow.postMessage({ type: "toStyleListCart" }, window.origin);
				}
			}
		},
		//计算数量
		getNum(m) {
			if (m.page * m.pageSize <= m.total) {
				let s = parseFloat(m.page * m.pageSize);
				return s;
			} else {
				return m.total;
			}
		},
		//计算百分比
		getPro(m) {
			if (m.page * m.pageSize <= m.total) {
				let s1 = DataProcessing.floatRide(m.page, m.pageSize);
				let s = (s1 / m.total) * 100;
				s = s.toFixed(0);
				s = parseFloat(s);
				return s;
			} else {
				return 100;
			}
		},
		//购物车
		shop: Common.fd((item) => {
			console.log("滴滴答答", item.productStock);
			if (item) {
				//库存0  提示不执行
				if (item?.productStock <= 0) {
					that.$toast.error("Insufficient inventory");
					return;
				}

				let m = {
					proId: that.proId,
					newShoppingCartListItem: [
						{
							//	id: item.categoryId,
							proId: that.proId,
							cateId: item.categoryId,
							productId: item.id,
							productNum: 1,
						},
					],
				};
				//showLoginBoxMethod()
				console.log("去购物车", m, that.isLogin);
				if (that.isLogin) {
					m.userId = that.userId;
					addShoppingCart(m).then((res) => {
						if (res) {
							that.jump("stylistCart");
						}
					});
				} else {
					m.cartUuid = that.userUUID;
					addShoppingCartNotLogin(m).then((res) => {
						if (res) {
							that.jump("stylistCart");
						}
					});
				}
			}
		}, 1000),

		//收藏
		collectFunc: Common.fd((item) => {
			let m = {
				userId: that.userId,
				proId: item.proId,
				productId: item.id,
				cateId: item.categoryId,
			};
			designerAddCollection(m).then((res) => {
				if (res) {
					that.$toast.success(res.message);
					if (res.message == "success") {
						item.isCollection = !item.isCollection;
						item.collectionId = res.data;
					}
					this.$forceUpdate();
					that.$emit("collect", item, true);
				}
			});
		}, 1000),
		//取消收藏
		collectNotFunc: Common.fd((item) => {
			console.log("怎么没有", item);
			let m = {
				id: item.collectionId,
			};
			removeCollectionById(m).then((res) => {
				if (res) {
					that.$toast.success(res.message);
					if (res.message == "success") {
						item.isCollection = !item.isCollection;
						item.collectionId = res.data;
					}
					this.$forceUpdate();
					that.$emit("collect", item, false);
				}
			});
		}, 1000),

		//左上角标签待定
		aaa() {},
		//获取加号
		getSalesVolume(num) {
			//num = 0; //99 401 499
			if (num < 100) {
				return num;
			}
			let a = parseInt(num / 100) * 100;
			return a + "+";
		},
		//金额汇率计算
		getPrice(num) {
			let s = (num * this.baseModel.model.rate).toFixed(2);
			return s;
		},
	},
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
$themeColor: #d24600;
@mixin centerCenter {
	display: flex;
	justify-content: center;
	align-items: center;
}
@mixin oneRowDian {
	display: -webkit-box;
	overflow: hidden;
	white-space: inherit;
	text-overflow: ellipsis;
	-webkit-line-clamp: 1;
	-webkit-box-orient: vertical;
	word-break: break-all;
}
.lyCardList {
	.empty {
		height: 47px;
		background-color: #000;
		@include centerCenter;
		font-size: 1.3em;
		font-weight: bold;
		line-height: 47px;
		letter-spacing: 0em;
		color: #fff;
		padding: 0 26px;
		justify-content: flex-start;
		margin-bottom: 20px;
	}
	.list {
		display: grid;
		grid-template-columns: 1fr 1fr 1fr 1fr;
		grid-row-gap: 25px;
		grid-column-gap: 25px;
		margin-bottom: 20px;
		.li:hover {
			box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.14);
		}
		.li {
			border-radius: 21px;
			position: relative;
			border: 1px solid #e1e1e1;
			cursor: pointer;
			transition: all ease 0.5s;
			.imgDiv {
				position: relative;
				.ad {
					position: absolute;
					right: 0.8em;
					bottom: 0.8em;
					width: 2.2em;
					height: 1.6em;
					background: rgba(0, 0, 0, 0.36);
					padding: 0;
					color: #ffffff;
					font-size: 14px;
					border-radius: 0px 6px 0px 6px;
					@include centerCenter;
				}
				img {
					background: #e1e1e1;
					border-radius: 21px 21px 0 0;
				}
			}
			.rowFont {
				padding: 13px 15px;
				margin-bottom: 6px;
				.rowOne {
					color: #333;
					font-size: 1.2em;
					font-weight: bold;
					margin-bottom: 10px;
					@include oneRowDian;
				}
				.rowTwo {
					font-size: 1.1em;
					color: $themeColor;
					font-weight: bold;
					margin-bottom: 5px;
					width: calc(100% - 36px);
					@include oneRowDian;
				}
				.rowThree {
					font-size: 0.9em;
					font-weight: 400;
					width: calc(100% - 36px);
					@include oneRowDian;
					.p1 {
						color: #f77a3c;
						margin-right: 4px;
					}
					.p2 {
						color: #999;
					}
				}
			}
			.shopping {
				position: absolute;
				bottom: 20px;
				right: 16px;
				width: 36px;
				height: 36px;
				z-index: 149;
				@include centerCenter;
				border-radius: 6px;
				background: $themeColor;
				cursor: pointer;
				img {
					width: 100%;
					height: 100%;
				}

				&[disabled] {
					background-color: #cccccc;
				}
				.icon {
					color: #fff;
				}
			}
			.label {
				position: absolute;
				top: 16px;
				left: 16px;
				background: #fce2d6;
				border-radius: 6px;
				padding: 4px 8px;
				color: $themeColor;
				font-weight: bold;
				@include centerCenter;
				font-size: 0.9em;
				line-height: 1;
			}
			.collect {
				position: absolute;
				right: 16px;
				top: 16px;
				.iconOn {
					color: $themeColor;
				}
			}
		}
	}
	.move {
		.moveTitle {
			color: #333;
			font-weight: 400;
			font-size: 1.8em;
			text-align: center;
			margin-top: 40px;
		}
		.progress {
			width: 75%;
			margin: 10px auto 0 auto;
		}
		.btnDiv {
			margin: 30px auto 80px auto;
			@include centerCenter;
			cursor: pointer;
			.btn {
				padding: 9px 40px;
				font-size: 1.3em;
				color: $themeColor;
				border: 1px solid $themeColor;
				border-radius: 4px;
				width: max-content;
				@include centerCenter;
			}
		}
		.moveLoading {
			@include centerCenter;
			margin-top: 20px;
		}
	}
}
@include respond-to(mb) {
	.lyCardList {
		.list {
			grid-template-columns: 1fr 1fr;
			grid-row-gap: 11px;
			grid-column-gap: 11px;
			margin-bottom: 12px;
		}
		.li {
			border-radius: 10px !important;
			.imgDiv {
				img {
					border-radius: 10px 10px 0 0 !important;
				}
			}
			.rowFont {
				padding: 2px 10px !important;
				.rowOne {
					margin-bottom: 5px !important;
				}
				.rowTwo {
					margin-bottom: 0 !important;
				}
			}
			.shopping {
				width: 28px !important;
				height: 28px !important;
				right: 8px !important;
			}
			.label {
				top: 8px !important;
				left: 8px !important;
				border-radius: 3px !important;
			}
			.collect {
				right: 8px !important;
				top: 8px !important;
			}
		}
		.move {
			.moveTitle {
				margin-top: 20px;
				font-size: 1.4em;
				opacity: 0.9;
			}
			.progress {
				margin: 5px auto 0 auto;
			}
			.btnDiv {
				margin: 15px auto 40px auto;
			}
		}
	}
}
</style>

<style rel="stylesheet/scss" lang="scss">
$themeColor: #d24600;
.lyCardList {
	.move {
		.progress {
			.el-progress-bar__inner {
				background-color: $themeColor;
			}
		}
	}
}
</style>
