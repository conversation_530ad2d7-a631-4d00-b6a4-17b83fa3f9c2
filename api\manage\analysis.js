import { request } from '~/utils/request'

// 获取 所有 数据
export function getAnalysisPageAllDate(data) {
  return request({
    url: '/retailer/statistic/all',
    method: 'post',
    data: data,
  })
}

// 筛选数据
export function getAnalysisPageDataByCondition(data) {
  return request({
    url: '/retailer/statistic/bar-chart',
    method: 'post',
    data: data,
  })
}

// 获取产品类型下拉数据
export function getProductTypePullDown(params) {
  return request({
    url: 'retailer/statistic/product/options',
    method: 'get',
    params: params,
  })
}

// Analysis的分页
export function getTableDataByPaging(data) {
  return request({
    url: '/retailer/statistic/top-selling',
    method: 'post',
    data: data,
  })
}
