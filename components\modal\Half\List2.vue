<!--
    用途：https://www.luggagetags.com/us/leather-luggage-tags
    预览图: https://static-oss.gs-souvenir.com/web/quoteManage/20250225/企业微信截图_17404552216026_202502255z2ybQ.png
-->
<template>
  <div class="wrap List2" :style="modal.style">
    <template v-if="isManage">
      <v-card height="300">
        <v-row justify="center">
          <v-overlay :absolute="true" :value="true">
            <v-chip>半定制列表模板2</v-chip>
          </v-overlay>
        </v-row>
      </v-card>
    </template>
    <div v-else>
      <div class="bps-container">
        <div class="topBar" :class="{ sticky: breadCrumbsHeight > 0 }">
          <div class="top-left">
            <div class="breadCrumbs" style="margin: 0">
              <div class="bread-item is-link" @click="toCate">
                <span
                  style="font-size: 15px; text-decoration: underline; white-space: nowrap"
                  >{{ langSemiCustom.allProducts }}</span
                >
                <b class="icon-right"></b>
              </div>
              <div class="bread-item">
                <span
                  class="bread-item-span"
                  style="
                    font-size: 13px;
                    font-weight: 400;
                    text-decoration: underline;
                    white-space: nowrap;
                  "
                >
                  {{ fatherCateName }}
                </span>
                <b class="icon-right"></b>
              </div>
            </div>
          </div>
          <div class="top-right">
            <div class="filterSearch" :class="{ textSearch: !isMobile }">
              <form class="search-area" @submit="submitForm">
                <div class="inputBox">
                  <span @click="submitForm"><v-icon>mdi-magnify</v-icon></span>
                  <input
                    v-model="keyword"
                    @keyup.enter="submitForm"
                    type="text"
                    :placeholder="langSemiCustom.ProductKeywords"
                  />
                </div>
              </form>
            </div>
            <div class="filter-area">
              <div class="filter1 extend-click-area" @click="toggleFilter">
                <b class="icon-a-HideFilters"></b>
                <span
                  >{{ showFilter ? `${langSemiCustom.hide}` : `${langSemiCustom.show}` }}
                  {{ langSemiCustom.filters }}</span
                >
              </div>
              <div class="filter2">
                <span class="t1">{{ langSemiCustom.sort }}:</span>
                <v-menu offset-y>
                  <template v-slot:activator="{ on, attrs }">
                    <v-btn text v-bind="attrs" v-on="on" :small="isMobile">
                      {{ sortList[sorts] }}
                      <b class="icon-Down" style="font-size: 12px"></b>
                    </v-btn>
                  </template>
                  <v-list>
                    <v-list-item
                      v-for="(item, key) in sortList"
                      :key="key"
                      @click="sortProduct(key)"
                    >
                      <v-list-item-title>{{ item }} </v-list-item-title>
                    </v-list-item>
                  </v-list>
                </v-menu>
              </div>
            </div>
          </div>
        </div>
        <div class="not-find" v-if="productList.length === 0 && !$fetchState.pending">
          <div class="result-tip">
            <span>{{ `${langSemiCustom.resultsFor} "${keyword}"` }}</span>
          </div>
          <div class="result-box" style="display: block">
            <div class="result-main" style="display: block">
              <div class="main-tip1">
                {{
                  `${langSemiCustom.sorrySearch}"${keyword}"
								${langSemiCustom.notMatchPro}`
                }}
              </div>
              <div class="main-tip2">
                <div class="tip2">{{ langSemiCustom.seachTips }}</div>
                <ul>
                  <li>{{ langSemiCustom.checkSpelling }}</li>
                  <li>{{ langSemiCustom.useFewerWords }}</li>
                  <li>{{ langSemiCustom.useDifferentWords }}</li>
                </ul>
              </div>
              <div class="main-tip3">
                <div class="tip3">{{ langSemiCustom.needHelp }}</div>
                <div class="tip31">{{ langSemiCustom.within24Hours }}</div>
                <div class="tip31">
                  <span>{{ langSemiCustom.orEmail }}</span>
                  <a :href="'mailto:' + $store.state.proSystem.email">{{
                    $store.state.proSystem.email
                  }}</a>
                  <span>{{ langSemiCustom.weHelpyou }}</span>
                </div>
              </div>
              <div>
                <v-btn color="primary" @click="goInquiry">
                  {{ langSemiCustom.si }}
                  <v-icon right large> mdi-menu-right</v-icon>
                </v-btn>
              </div>
            </div>
          </div>
        </div>
        <div
          class="content"
          id="proContent"
          ref="proContent"
          :class="{ noFilter: !showFilter }"
          v-if="productList.length && !$fetchState.pending"
        >
          <div
            class="leftBar"
            :style="{
              maxHeight: isMobile
                ? `${screenHeight}px`
                : screenHeight - selectLabelHeight - 10 + 'px',
            }"
            :disabled="loadLabel"
            ref="leftBar"
            v-show="showFilter"
          >
            <div class="overlay changeLoad" v-show="loadLabel" style="z-index: 1">
              <div class="loader"></div>
            </div>
            <div class="leftBar-header" v-if="isMobile">
              <p class="filter">{{ langSemiCustom.filters }}</p>
              <div @click="toggleFilter"><b class="iconfont icon-hxsht-xp-gb"></b></div>
            </div>
            <div class="filter-mb" v-if="isMobile && !isStockPage">
              <div class="filter-mb-title" @click="clickTitle({ id: -1 })">
                <div>{{ langSemiCustom.sort }}:</div>
                <b class="icon-Down" :class="{ active: activeNames.includes(-1) }"></b>
              </div>
              <div class="con" v-show="activeNames.includes(-1)">
                <div
                  class="con-radio"
                  v-for="(item, key) in sortList"
                  :key="key"
                  @click.stop="sortProduct(key)"
                >
                  <label>
                    <input type="radio" name="sort" checked />
                    {{ item }}
                    <span class="custom-radio"></span>
                  </label>
                </div>
              </div>
            </div>
            <div
              class="collapse"
              :style="{
                maxHeight: isMobile
                  ? `${screenHeight - headerHeight - 80}px`
                  : screenHeight - selectLabelHeight - 10 + 'px',
              }"
            >
              <div class="collapse-item" v-for="item in labelData" :key="item.id">
                <div class="collapse-item-title" @click="clickTitle(item)">
                  <strong class="text-truncate">{{ item.nameEn }}</strong>
                  <b
                    class="icon-Down"
                    :class="{ active: activeNames.includes(item.id) }"
                  ></b>
                </div>
                <v-expand-transition>
                  <div class="con" v-show="activeNames.includes(item.id)">
                    <template v-if="getLabelType(item.attributeType) === 'range'">
                      <template v-for="citem in item.attributeList">
                        <div class="price-range-box">
                          <v-btn icon v-show="showPriceRange">
                            <v-icon @click="clearRange"> mdi-trash-can</v-icon>
                          </v-btn>
                          <v-range-slider
                            class="custom-slider"
                            @change="changeRange"
                            v-model="priceRange"
                            thumb-label
                            :min="citem.valueName"
                            :max="citem.remark"
                            hide-details
                            :step="0.01"
                          ></v-range-slider>
                        </div>
                        <div class="price-des">
                          <div>
                            <CCYRate :price="citem.valueName"></CCYRate>
                          </div>
                          <div>
                            <CCYRate :price="citem.remark"></CCYRate>
                          </div>
                        </div>
                      </template>
                    </template>
                    <template v-else-if="getLabelType(item.attributeType) === 'color'">
                      <div class="box color-box">
                        <div class="color-wrap">
                          <template v-for="citem in item.attributeList">
                            <v-tooltip bottom :disabled="isMobile">
                              <template v-slot:activator="{ on, attrs }">
                                <div
                                  v-bind="attrs"
                                  v-on="on"
                                  class="color-item"
                                  :class="{ active: citem.id === colorItem.id }"
                                  @click="toggleColor(citem)"
                                >
                                  <img
                                    v-if="citem.imgUrl"
                                    :src="citem.imgUrl"
                                    :alt="citem.valueName"
                                    :title="citem.valueName"
                                  />
                                  <span
                                    v-else
                                    :style="{ backgroundColor: citem.valueName }"
                                  ></span>
                                </div>
                              </template>
                              <span style="font-size: 12px"
                                >{{ citem.remark }} ({{ citem.productCount }})</span
                              >
                            </v-tooltip>
                          </template>
                        </div>
                      </div>
                    </template>
                    <template
                      v-else-if="
                        getLabelType(item.attributeType) === 'checkbox-style1' ||
                        getLabelType(item.attributeType) === 'checkbox-style2'
                      "
                    >
                      <div :class="getLabelType(item.attributeType)" v-if="!isMobile">
                        <v-checkbox
                          v-model="selectedParams"
                          @change="updateTag(citem)"
                          :label="`${citem.valueName}(${citem.productCount})`"
                          :value="citem.id"
                          v-for="citem in showMoreArr(item)"
                          :key="citem.id"
                          hide-details
                          dense
                        ></v-checkbox>
                        <div
                          class="showBtn"
                          ref="showBtn"
                          v-show="item.isMore && item.attributeList.length > 8"
                          @click="showMore(item)"
                        >
                          {{
                            item.moreText
                              ? langSemiCustom.ShowMore
                              : langSemiCustom.ShowLess
                          }}
                        </div>
                      </div>
                      <div
                        :class="getLabelType(item.attributeType)"
                        v-else="isMobile && isStockPage && $store.state.proTheme == '11'"
                      >
                        <div class="teaMobileBox">
                          <div
                            class="teaMobileStyle"
                            v-for="citem in showMoreArr(item)"
                            :key="citem.id"
                            :class="{ active: teaMobileActive(citem.id) }"
                          >
                            <div></div>
                            <input
                              type="checkbox"
                              class="teaCheckbox"
                              v-model="selectedParams"
                              @change="updateTag(citem)"
                              :value="citem.id"
                              :id="'checkbox-' + citem.id"
                            />
                            <label :for="'checkbox-' + citem.id">
                              {{ `${citem.valueName}(${citem.productCount})` }}
                            </label>
                          </div>
                        </div>
                        <div
                          class="showBtn"
                          ref="showBtn"
                          v-show="item.isMore && item.attributeList.length > 8"
                          @click="showMore(item)"
                        >
                          {{
                            item.moreText
                              ? langSemiCustom.ShowMore
                              : langSemiCustom.ShowLess
                          }}
                        </div>
                      </div>
                    </template>
                    <template v-else-if="getLabelType(item.attributeType) === 'switch'">
                      <v-switch
                        v-model="selectedParams"
                        @change="updateTag(citem)"
                        :label="`${citem.valueName}(${citem.productCount})`"
                        :value="citem.id"
                        v-for="citem in item.attributeList"
                        :key="citem.id"
                        hide-details
                        dense
                      ></v-switch>
                    </template>
                  </div>
                </v-expand-transition>
              </div>
            </div>
          </div>
          <div style="width: 100%; min-width: 0">
            <div class="productCateNameBox" v-show="recommentList.length > 0">
              <h2 class="leftTitle productCateName">{{ langSemiCustom.TopSelling }}</h2>
              <h2 class="productCateName">
                {{ fatherCateName + " " }}{{ langSemiCustom.Personalized }}
              </h2>
            </div>
            <div class="recommendProduct" v-if="recommentList.length > 0">
              <div
                v-for="item in recommentList"
                :key="item.id"
                class="recomItem"
                v-if="item.sellingSort < 4"
                :class="{ doubleItem: item.sellingSort == 1 }"
              >
                <img class="backImg" v-if="!isMobile" :src="item.backgroundImg" />
                <img class="backImg" v-if="isMobile" :src="item.mbBackgroundImg" />
                <div class="recomH3 recomH3_2" v-if="!isMobile && item.sellingSort > 1">
                  {{ item.name }}
                </div>
                <div class="flexBox">
                  <div
                    class="itemBox"
                    :class="{
                      halfBox: item.sellingSort == 1,
                      halfBox2: item.sellingSort > 1,
                    }"
                  >
                    <div class="recomH3" v-show="item.sellingSort == 1">
                      {{ item.name }}
                    </div>
                    <div class="recomH3" v-show="isMobile && item.sellingSort > 1">
                      {{ item.name }}
                    </div>
                    <div class="recomText">
                      {{ langQuote.neon.AsLowAs
                      }}<span class="boldFont">
                        <CCYRate :price="item.lowestDiscountPrice"></CCYRate> </span
                      >{{ langSemiCustom.ea }}
                    </div>
                    <div class="ulFlex">
                      <ul v-show="!isMobile">
                        <li class="recomLi" v-html="item.sellingRich"></li>
                      </ul>
                      <ul v-show="isMobile">
                        <li class="recomLi" v-html="item.mbSellingRich"></li>
                      </ul>
                      <div class="detailBtn" @click="openMaskDetail(item.productRouting)">
                        <span
                          :alt="langSemiCustom.leatherTags"
                          :title="langSemiCustom.leatherTags"
                          >{{ langSemiCustom.orderNow }}</span
                        >
                      </div>
                    </div>
                  </div>
                  <div class="itemBox2">
                    <img
                      :class="{
                        absoluteImg: item.sellingSort == 1,
                        absoluteImg2: item.sellingSort > 1,
                      }"
                      :style="{ 'aspect-ratio': 710 / 295 }"
                      v-show="!isMobile"
                      :src="item.sellingProductImg[0].url"
                      :alt="item.sellingProductImg[0].name"
                      :title="item.sellingProductImg[0].name"
                    />
                    <img
                      :class="{
                        absoluteImg: item.sellingSort == 1,
                        absoluteImg2: item.sellingSort > 1,
                      }"
                      v-show="isMobile"
                      :src="item.mbSellingProductImg[0].url"
                      :alt="item.sellingProductImg[0].name"
                      :title="item.sellingProductImg[0].name"
                    />
                  </div>
                </div>
              </div>

              <div class="recomItem doubleItem" v-if="!isMobile && recommentLast">
                <img
                  class="backImg"
                  :src="recommentLast.backgroundImg"
                  style="user-select: none"
                />
                <div class="itemBox invert">
                  <div class="itemLeft">
                    <div class="swiper myswiper1" ref="swiper1">
                      <div class="swiper-wrapper">
                        <div
                          class="swiper-slide"
                          v-for="(item, index) in recommentLast.sellingProductImg"
                          :key="item.url"
                        >
                          <div class="smallImg" v-if="isImageType(item.url)">
                            <client-only>
                              <template>
                                <img
                                  :src="item.url"
                                  :alt="item.name"
                                  :title="item.name"
                                  style="width: 100%; height: 100%"
                                />
                              </template>
                            </client-only>
                          </div>
                          <VideoPlayer
                            class="swiper-no-swiping"
                            v-else
                            :ref="'videoPlayer' + index"
                            disabled-mouse
                            :options="getVideoOptions(item, 1)"
                          ></VideoPlayer>
                        </div>
                      </div>
                      <div class="swiper-button-prev" @click.stop></div>
                      <!--左箭头。如果放置在swiper外面，需要自定义样式。-->
                      <div class="swiper-button-next" @click.stop></div>
                      <!--右箭头。如果放置在swiper外面，需要自定义样式。-->
                    </div>
                  </div>
                  <div class="itemRight">
                    <div style="width: 100%">
                      <div class="recomH3">{{ recommentLast.name }}</div>
                      <div class="itemRight_textBox">
                        <div class="recomText">
                          {{ langQuote.neon.AsLowAs
                          }}<span class="boldFont">
                            <CCYRate :price="recommentLast.lowestDiscountPrice"></CCYRate>
                          </span>
                          {{ langSemiCustom.ea }}
                        </div>
                        <ul>
                          <li class="recomLi" v-html="recommentLast.sellingRich"></li>
                        </ul>
                        <div
                          class="detailBtn"
                          @click="openMaskDetail(recommentLast.productRouting)"
                        >
                          <span
                            :alt="langSemiCustom.leatherTags"
                            :title="langSemiCustom.leatherTags"
                            >{{ langSemiCustom.orderNow }}</span
                          >
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="recomItem"
                v-show="isMobile && recommentLast"
                @click="openMaskDetail(recommentLast.productRouting)"
              >
                <img
                  class="backImg"
                  v-show="isMobile"
                  :src="recommentLast.mbBackgroundImg"
                />
                <div class="flexBox">
                  <div class="itemBox halfBox2">
                    <div class="recomH3" v-show="isMobile">{{ recommentLast.name }}</div>
                    <div class="recomText">
                      {{ langQuote.neon.AsLowAs
                      }}<span class="boldFont">
                        <CCYRate :price="recommentLast.lowestDiscountPrice"></CCYRate>
                      </span>
                      {{ langSemiCustom.ea }}
                    </div>
                    <div class="ulFlex">
                      <ul>
                        <li class="recomLi" v-html="recommentLast.mbSellingRich"></li>
                      </ul>
                      <div
                        class="detailBtn"
                        @click="openMaskDetail(recommentLast.productRouting)"
                      >
                        <span
                          :alt="langSemiCustom.leatherTags"
                          :title="langSemiCustom.leatherTags"
                          >{{ langSemiCustom.orderNow }}</span
                        >
                      </div>
                    </div>
                  </div>
                  <div class="itemBox3">
                    <img
                      class="absoluteImg2"
                      v-show="isMobile"
                      :src="recommentLast.mbSellingProductImg[0].url"
                      :alt="recommentLast.mbSellingProductImg[0].name"
                      :title="recommentLast.mbSellingProductImg[0].name"
                    />
                  </div>
                </div>
              </div>
            </div>
            <div class="marginBottom" :class="{ sticky: selectLabelHeight > 0 }"></div>
            <h2
              class="productCateName marginTop"
              :class="{ sticky: selectLabelHeight > 0 }"
            >
              {{ langSemiCustom.Stylish + " " }}{{ fatherCateName }}
            </h2>
            <div
              class="selectLabel"
              id="selectLabel"
              :class="{ sticky: selectLabelHeight > 0 }"
              style="z-index: 900"
            >
              <div class="filter">
                <div class="clearFilter" v-show="isMobile">
                  <a href="javascript:;" @click="delAllTag">
                    {{ langSemiCustom.clearFilters }}
                  </a>
                  <span class="total"
                    >({{ totalResult }} {{ langSemiCustom.results }})</span
                  >
                </div>
                <div class="top">
                  <div class="clearFilter" v-show="!isMobile">
                    <a href="javascript:;" @click="delAllTag">
                      {{ langSemiCustom.clearFilters }}
                    </a>
                    <span class="total"
                      >({{ totalResult }} {{ langSemiCustom.results }})</span
                    >
                  </div>
                  <template v-if="colorItem">
                    <div class="category-item tag">
                      <span v-show="!isStockPage"
                        >Color:&nbsp;{{ colorItem.valueName }}</span
                      >
                      <span v-show="isStockPage && $store.state.proTheme == '11'">
                        包装形态:&nbsp;{{ colorItem.valueName }}</span
                      >
                      <div @click="delColor(colorItem)">
                        <v-icon small style="margin-left: 4px">mdi-close</v-icon>
                      </div>
                    </div>
                  </template>
                  <template v-for="(item, key, index) in selectedParamsObj">
                    <div class="category-item tag" v-for="citem in item" :key="citem.id">
                      <span
                        >{{ getLabel(citem["fatherId"]) }}:&nbsp;{{
                          citem.valueName
                        }}</span
                      >
                      <div @click="delTag(citem)">
                        <v-icon small style="margin-left: 4px">mdi-close</v-icon>
                      </div>
                    </div>
                  </template>
                  <template v-for="(citem, cindex) in newCateList">
                    <div
                      class="category-item"
                      :class="{ active: activeCateName(citem) }"
                      v-show="!isStockPage && cindex < showMoreIndex"
                      @click="changeCategory(citem)"
                    >
                      <span
                        >{{ citem.name }}<b class="icon-right" v-show="false"></b
                      ></span>
                    </div>
                  </template>
                  <div class="viewMore" v-show="false">
                    <span @click="showMoreIndex = 999" v-if="showMoreIndex === 10"
                      >{{ langQuote.ViewMore }}<b class="icon-Down"></b
                    ></span>
                    <span @click="showMoreIndex = 10" v-if="showMoreIndex === 999"
                      >{{ langQuote.ViewLess }}<b class="icon-Up"></b
                    ></span>
                  </div>
                </div>
              </div>
            </div>
            <div class="rightPart" id="productList">
              <div class="rightContent">
                <div class="productWrap">
                  <product
                    class="productGoods"
                    v-for="(item, index) in productList"
                    :key="index"
                    :class="{ inserted_element: item.booth == 2 }"
                    :parentCateId="parentCateId"
                    :cateId="cateId"
                    :productData="item"
                    :itemIndex="index"
                    :isStockPage="isStockPage"
                    :adBoxHeight="adBoxHeight"
                    :productList="productList"
                    :isDiv="getProductIsDiv"
                    @toDetail="toDetail"
                  ></product>
                </div>
              </div>
            </div>
            <div class="loadProgress" v-show="loadingProduct">
              <Loading></Loading>
            </div>
          </div>
        </div>
        <div class="loadBtnMainBox" v-show="!loadingProduct && totalResult > 0">
          <div class="loadMoreBtn">
            <div class="loadMoreBtnText">
              <span>
                {{
                  `${langSemiCustom.viewed} ${productList.length - usePosition} ${
                    langSemiCustom.of
                  } ${totalResult}
								${langSemiCustom.products} `
                }}
              </span>
            </div>
            <v-progress-linear
              height="6"
              :style="{
                'background-color': '#fff',
              }"
              rounded
              :value="numPercent"
            >
            </v-progress-linear>
            <div class="loadBtnBox">
              <span class="loadBtn" @click="loadMoreData" v-show="showLoadBtn">
                {{ langSemiCustom.loadMore }}
              </span>
              <!-- <span class="loadBtn" @click="listScrollTop(false)" v-show=" !showLoadBtn ">
                  {{ langSemiCustom.loadLess }}
                </span> -->
            </div>
          </div>
        </div>
      </div>
    </div>
    <noParamInquiryBox
      :infoDialogVisible.sync="infoDialogVisible"
      :keyword="keyword"
      :fatherCateName="fatherCateName"
      :cateName="cateName"
      :parentCateId="parentCateId"
      :cateId="cateId"
    ></noParamInquiryBox>
  </div>
</template>
<script>
import {
  addCollection,
  deleteConllectionByUserId,
  getAdvertList,
  getAppLabelAttributeList,
  getProductList,
  listAllCategoryByParentId,
  getSellingProduct,
} from "@/api/web";
import {
    debounce,
    scrollToViewTop,
    isImageType,
    isVideoType,
    generateUUID, deepClone,
} from "@/utils/utils";
import noParamInquiryBox from "@/components/modal/Half/List/noParamInquiryBox.vue";
import product from "@/components/modal/Half/List/product.vue";
import { halfListMixins } from "@/mixins/halfList";

export default {
  mixins: [halfListMixins],
  props: [
    "cateId",
    "cateType",
    "data",
    "parentCateId",
    "halfCateDTO",
    "isStockPage",
    "name",
    "selectLabelData",
  ],
  components: { noParamInquiryBox, product },
  provide() {
    return {
      isDialog: this.isDialog,
    };
  },
  data() {
    return {
      showMoreIndex: 10,
      last_scroll: 0,
      hide_on_load: false,
      debounceSearchProduct: null,
      infoDialogVisible: false,
      modal: {
        style: {},
        type: {},
        ...this.data,
      },
      showPriceRange: false,
      priceRange: [-990000, 990000],
      halfName: "",
      goodList: [],
      showColorNum: 7,
      keyword: "",
      sorts: 2,
      labelData: [],
      cateList: [],
      activeNames: [],
      selectedParams: [],
      showFilter: true,
      page: 1,
      pages: 0,
      pageSize: 20 * 3,
      totalResult: 0,
      productList: [],
      colorItem: "",
      priceKey: 0,
      quantityKey: 0,
      screenHeight: 0,
      screenWidth: 0,
      loadingProduct: false,
      cateName: "",
      isManage: false,
      attributeValueIds: [],
      selectedParamsObj: {},
      loadLabel: false,
      adData: null,
      advert: [],
      adNum: 0,
      useAdNum: 0,
      doubleAd: 0,
      usePosition: 0,
      fatherCateName: "",
      headerHeight: 0,
      breadCrumbsHeight: 0,
      selectLabelHeight: 0,
      nowTimeStamp: "",
      cancelTokenSource: null,
      firstCate: false,
      recommentList: [],
      recommentLast: {},
      fristWatch: false,
      adBoxHeight: "",
    };
  },
  async fetch() {
    if (process.env.isManage) {
      this.isManage = true;
      return false;
    }
    this.page = parseInt(this.$route.query.page) || 1;
    await this.loadThreeData(true);
    //获取推荐产品
    let recommendData = await getSellingProduct({
      categoryId: this.parentCateId || this.cateId,
      isPcSellingSort: 1,
    });
    if (recommendData.data && recommendData.data.length > 0) {
      recommendData.data.forEach((item) => {
        item.sellingProductImg =
          item.sellingProductImg && JSON.parse(item.sellingProductImg);
        item.mbSellingProductImg =
          item.mbSellingProductImg && JSON.parse(item.mbSellingProductImg);
        if (item.sellingSort == 4) this.recommentLast = item;
      });
      this.recommentList = recommendData.data;
    }
  },
  watch: {
    selectedParams(oldVal, newVal) {
      if (!this.fristWatch) return;
      this.useAdNum = 0;
      this.doubleAd = 0;
      this.usePosition = 0;
      this.updateLabel();
      this.searchProduct();
    },
    "$fetchState.pending"(newVal, oldVal) {
      if (!newVal && oldVal) {
        // 仅在从pending变为非pending时执行
        this.$nextTick(() => {
          // 这里可以安全地操作DOM或执行依赖于DOM的操作
          this.setSticky();
        });
      }
    },
  },
  computed: {
      newCateList(){
          function swapElements(arr, index1, index2) {
              [arr[index1], arr[index2]] = [arr[index2], arr[index1]];
              return arr;
          }
          let cateList = deepClone(this.cateList);
          let findIndex = cateList.findIndex(item=>this.activeCateName(item));
          if(findIndex>-1 && this.isMobile){
              swapElements(cateList,0,findIndex)
          }
          return cateList
      },
    sortList() {
      return {
        1: this.langSemiCustom.newest,
        2: this.langSemiCustom.recommended,
        3: this.langSemiCustom.bestSeller,
        4: this.langSemiCustom.popular,
      };
    },
    projectName() {
      return this.$store.state.proName;
    },
    langQuote() {
      return this.$store.getters.lang.quote || {};
    },
    langSemiCustom() {
      return this.$store.getters.lang?.semiCustom;
    },
    userId() {
      return this.$store.state.userInfo?.id;
    },
    isLogin() {
      return this.$store.getters.isLogin;
    },
    isMobile() {
      return this.$store.state.device === "mb";
    },
    numPercent() {
      return Math.round(
        ((this.productList.length - this.usePosition) / this.totalResult) * 100
      );
    },
    showLoadBtn() {
      return !(this.page == this.pages);
    },
    proId() {
      return this.$store.state.proId;
    },
    isDialog() {
      return !!this.name;
    },
    getProductIsDiv() {
      return (!this.isKeychainsCo || this.isDialog) && !this.isLuggageSite;
    },
    isLuggageSite() {
      return this.proId === 475;
    },
    isKeychainsCo() {
      return this.proId === 354;
    },
  },
  methods: {
    toDetail(url) {
      this.$emit("toDetail", url);
    },
    openMaskDetail(url) {
      if (this.isDialog) {
        this.$emit("toDetail", url);
      } else {
        let data = {
          modal: "modalQuoteHalfDetail",
          name: url,
        };
        this.$store.commit("setMask", data);
      }
    },
    showMoreArr(item) {
      if (item.moreText && item.attributeList.length > 8) {
        return item.attributeList.slice(0, 8);
      } else {
        return item.attributeList;
      }
    },
    showMore(item) {
      item.moreText = !item.moreText;
    },
    updateTag(item) {
      let val = this.selectedParamsObj[item.fatherId];
      if (!val) {
        this.selectedParamsObj[item.fatherId] = [item];
      } else {
        let findIndex = val.findIndex((v) => {
          return v.id == item.id;
        });
        if (findIndex > -1) {
          val.splice(findIndex, 1);
        } else {
          val.push(item);
        }
      }
    },
    updateLabel() {
      this.loadLabel = true;
      this.getAppLabelAttributeList()
        .then((res) => {
          let labelData = res.data;
          labelData.forEach((item) => {
            if (item.isMore) item.moreText = true;
            if (item.isExpand) {
              this.activeNames.push(item.id);
            }
          });
        //   if (this.cateType == 2) {
        //     labelData = labelData.filter((item) => {
        //       return item.nameEn !== "MATERIAL" && item.nameEn !== "GENDER";
        //     });
        //   }
          this.activeNames = [...new Set([...this.activeNames])];
          this.labelData = labelData;
        })
        .finally(() => {
          this.listScrollTop();
          this.loadLabel = false;
        });
    },
    getAppLabelAttributeList() {
      return new Promise((resolve) => {
        getAppLabelAttributeList({
          categoryId: this.parentCateId || this.cateId,
          childCategoryId: this.parentCateId ? this.cateId : null,
          isLabel: 1,
          attributeValueIds: this.getLabelAttributeValueIds(),
          keyWord: this.keyword,
        }).then((res) => {
          resolve(res);
        });
      });
    },
    getSrc(item) {
      if (item.selectedColorIndex > -1) {
        return item.showImgSrc + "?x-oss-process=image/resize,p_40";
      } else {
        return item.showImgSrc;
      }
    },
    goInquiry() {
      this.infoDialogVisible = true;
    },
    toCate() {
      if (this.halfCateDTO) {
        let path =
          this.isStockPage == "1"
            ? this.halfCateDTO.shopRouting
            : this.halfCateDTO.customRouting;
        this.$router.push({
          path: path,
        });
      }
    },
    preLoadImg(arr) {
      arr.forEach((item) => {
        let img = new Image();
        img.src = item;
      });
    },
    getLabelType(type) {
      //标签类型 1.单选颜色样式，2.多选一列样式，3.多选两列样式，4.仅勾选是/否样式，5.拖动条样式），如果类型是拖动样式，属性值名称是最小值，属性值备注是最小值
      const obj = {
        1: "color",
        2: "checkbox-style1",
        3: "checkbox-style2",
        4: "switch",
        5: "range",
      };
      return obj[type];
    },

    parseJSON(str) {
      return str ? JSON.parse(str) : [];
    },

    toSceneImg(e, item) {
      e.stopPropagation();
      e.preventDefault();
      item.showImgSrc = item.sceneImg;
      item.selectedColorIndex = -1;
    },

    selectGoodColor(e, ind1, ind2, citem) {
      e.stopPropagation();
      e.preventDefault();
      this.$set(this.productList[ind1], "selectedColorIndex", ind2);
      this.$set(
        this.productList[ind1],
        "showImgSrc",
        this.parseJSON(citem.imgJson)[0]?.url
      );
    },
    toDetail2(item) {
      const routeOptions = {
        path: item.productRouting,
      };
      const url = this.$router.resolve(routeOptions).href;
      window.open(url, "_blank");
    },

    changeRange() {
      this.showPriceRange = true;
      this.useAdNum = 0;
      this.doubleAd = 0;
      this.usePosition = 0;
      this.searchProduct();
      this.updateLabel();
    },

    clearRange() {
      this.priceRange = [-10000, 10000];
      this.showPriceRange = false;
      this.useAdNum = 0;
      this.doubleAd = 0;
      this.usePosition = 0;
      this.searchProduct();
      this.updateLabel();
    },

    goCollection(e, item) {
      e.stopPropagation();
      e.preventDefault();
      let isCollection = item.isCollection;
      if (!this.isLogin) {
        this.$store.commit("setLogin", "login");
        return;
      }
      if (isCollection) {
        deleteConllectionByUserId({
          userId: this.userId,
          productId: item.id,
        }).then((res) => {
          item.isCollection = false;
          this.$forceUpdate();
        });
      } else {
        let productData = {
          data: { item_id: item.productSku, item_name: item.name },
          value: 0,
        };
        addCollection(
          {
            userId: this.userId,
            website: 1,
            cateId: this.parentCateId || this.cateId,
            productId: item.id,
          },
          productData
        ).then((res) => {
          item.isCollection = true;
          this.$forceUpdate();
        });
      }
    },

    delColor() {
      this.colorItem = "";
      this.useAdNum = 0;
      this.doubleAd = 0;
      this.usePosition = 0;
      this.searchProduct();
      this.updateLabel();
    },

    delTag(item) {
      let id = item.id;
      let ind = this.selectedParams.findIndex((item) => item === id);
      if (ind >= 0) {
        this.selectedParams.splice(ind, 1);
      }
      this.updateTag(item);
    },

    delAllTag() {
      this.selectedParams = [];
      this.selectedParamsObj = {};
      this.colorItem = "";
      this.page = 1;
      this.keyword = "";
      this.priceRange = [-990000, 990000];
      this.showPriceRange = false;
    },

    submitForm(e) {
        e.preventDefault();
        this.useAdNum = 0;
        this.doubleAd = 0;
        this.usePosition = 0;
        this.searchProduct();
        this.updateLabel();
    },

    toggleColor(item) {
      if (this.colorItem.id === item.id) {
        this.colorItem = "";
      } else {
        this.colorItem = item;
      }
      this.useAdNum = 0;
      this.doubleAd = 0;
      this.usePosition = 0;
      this.searchProduct();
      this.updateLabel();
    },

    formatUrlName(str) {
      return str.substring(1, str.length);
    },

    changeCategory(item) {
      let path = this.isStockPage == "1" ? item.shopRouting : item.customRouting;
      this.$router.push({
        path: path,
      });
    },

    getLabelAttributeValueIds() {
      let arr = [];
      for (let i in this.selectedParamsObj) {
        arr.push({
          parentId: i,
          childIds: this.selectedParamsObj[i].map((item) => item.id),
        });
      }
      if (this.colorItem) {
        arr.push({
          parentId: this.colorItem.fatherId,
          childIds: [this.colorItem.id],
        });
      }
      return arr;
    },

    getAttributeValueIds() {
      let str = "";
      for (let i in this.selectedParamsObj) {
        let val = this.selectedParamsObj[i].map((item) => item.id);
        if (val.length > 0) {
          str += val.join(",") + "-";
        }
      }
      if (this.colorItem) {
        str += this.colorItem.id;
      }
      str = str.replace(/^-*|-*$/g, "");
      return str;
    },

    getProduct(customPage) {
      return new Promise((resolve) => {
        getProductList({
          categoryId: this.parentCateId || this.cateId,
          childCategoryId: this.parentCateId ? this.cateId : null,
          page: customPage || this.page,
          pageSize: this.pageSize,
          keyWord: this.keyword,
          attributeValueIds: this.getAttributeValueIds(),
          userId: this.isLogin ? this.userId : null,
          sorts: this.sorts,
          priceStart: this.priceRange[0],
          priceEnd: this.priceRange[1],
          productType: this.isStockPage,
        })
          .then((res) => {
            this.totalResult = res.data.total;
            this.pages = res.data.pages;
            this.page = res.data.current;
            this.firstCate = false;
            //过滤掉推荐产品
            let isSelling = 0;
            let data = res.data.records.filter((item) => {
              if (item.isSelling == 1) isSelling++;
              return item.isSelling == 0;
            });
            this.totalResult = this.totalResult - isSelling;
            resolve(data);
          })
          .catch((error) => {
            if (error.name === "AbortError") {
              console.log("Request canceled:", error.message);
            }
          });
      });
    },

    searchProduct() {
      this.loadingProduct = true;
      this.page = 1;
      this.getProduct()
        .then((res) => {
          this.productList = this.setDefaultShowImg(res);
          //插入广告
          this.insertAdPosition();
        })
        .finally(() => {
          this.loadingProduct = false;
        });
    },

    setDefaultShowImg(list) {
      if (!list) {
        return;
      }
      if (this.isStockPage && this.$store.state.proTheme == "11") {
        list.forEach((item) => {
          item.showImgSrc = item.sceneImg;
          item.selectedColorIndex = -1;
        });
        return list;
      }
      let colorItem = this.colorItem,
        colorId;
      if (colorItem) {
        colorId = colorItem.id;
      }
      list.forEach((item) => {
        if (item.productParamList && item.productParamList.length > 0) {
          let productParamList = item.productParamList;
          let findColorIndex = productParamList.findIndex(
            (citem) => citem.attributeValueId === colorId
          );
          if (findColorIndex > -1) {
            try {
              item.showImgSrc = JSON.parse(
                productParamList[findColorIndex].imgJson
              )[0]?.url;
              item.selectedColorIndex = findColorIndex;
            } catch (e) {}
          } else {
            if (item.sceneImg) {
              item.showImgSrc = item.sceneImg;
              item.selectedColorIndex = -1;
            } else {
              try {
                item.showImgSrc = JSON.parse(productParamList[0].imgJson)[0]?.url;
                item.selectedColorIndex = 0;
              } catch (e) {}
            }
          }
        } else {
          item.showImgSrc = item.sceneImg;
          item.selectedColorIndex = -1;
        }
      });
      return list;
    },

    sortProduct(command) {
      this.sorts = command;
      this.useAdNum = 0;
      this.doubleAd = 0;
      this.usePosition = 0;
      this.searchProduct();
      if (command == 1) this.listScrollTop();
      if (command == 2) {
        window.scrollTo({
          behavior: "smooth",
          top: 0,
        });
      }
    },

    handleAllowSendMsg() {
      let numBoolean = this.dynamicValidateForm.isSmsSubscriptions ? 0 : 1;
      this.dynamicValidateForm.isSmsSubscriptions = numBoolean;
    },

    toggleFilter() {
	  this.setAdHeight();
      this.showFilter = !this.showFilter;
      let loadBtnMainBox = document.querySelector(".loadBtnMainBox");
      if (loadBtnMainBox && !this.isMobile) {
        if (!this.showFilter) {
          loadBtnMainBox.style.width = "100%";
          loadBtnMainBox.style.marginLeft = "0";
        } else {
          loadBtnMainBox.style.width = "calc(100% - 265px)";
          loadBtnMainBox.style.marginLeft = "265px";
        }
      }
      if (this.isMobile) {
        let leftBar = document.querySelector(".leftBar");
        leftBar.style.top = 0;
      }
    },

    clickTitle(val) {
      let findInd = this.activeNames.findIndex((item) => val.id === item);
      if (findInd >= 0) {
        this.activeNames.splice(findInd, 1);
      } else {
        this.activeNames.push(val.id);
      }
    },

    handleResize() {
      let header = document.querySelector("#modalHeader");
      this.headerHeight =
        typeof header?.offsetHeight === "number" ? header.offsetHeight : 0;
      this.setSticky();
      this.screenHeight = window.innerHeight;
      let needLoad = false;
      if (this.screenWidth > window.innerWidth) {
        if (this.screenWidth > 1400 && window.innerWidth < 1400) {
          this.screenWidth = window.innerWidth;
          needLoad = true;
        }
      } else {
        if (this.screenWidth < 1400 && window.innerWidth > 1400) {
          this.screenWidth = window.innerWidth;
          needLoad = true;
        }
      }
      if (needLoad) {
        this.useAdNum = 0;
        this.doubleAd = 0;
        this.usePosition = 0;
        this.debounceSearchProduct();
      }
    },

    //根据行数选择插入的广告位置
    insertAdPosition() {
      if (!this.adData || !this.productList.length) {
        return false;
      }
	  if(this.page == 1){
		this.useAdNum = 0;
        this.doubleAd = 0;
        this.usePosition = 0;
	  }
      let type = this.$store.state.device === "pc" ? "pc" : "mb";

      // 计算当前页的起始位置和结束位置
      const startIndex = (this.page - 1) * this.pageSize + 1; // 起始位置从1开始
      let endIndex = this.page * this.pageSize;
      if (this.productList.length - this.useAdNum < endIndex)
        endIndex = this.productList.length - this.useAdNum;
      // 插入广告的位置数组，每个元素表示一个广告位的位置
      const adPositions = [];
      var innerIndex;
      // 循环遍历每个广告位
      for (let i = 1; i <= this.adNum; i++) {
        if (this.adNum > this.advert.length) {
          innerIndex = (i - 1) % this.advert.length;
        } else {
          innerIndex = this.usePosition;
        }
        if (i % 2 != 0) {
          let firstIndex;
          if (type == "pc") {
            firstIndex = 16 * ((i + 1) / 2) - 13;
          } else if (type == "mb") {
            firstIndex = 3 - 1 + ((i - 1) / 2) * 12;
          }
          if (firstIndex >= startIndex && firstIndex <= endIndex) {
            firstIndex = firstIndex - this.doubleAd;
            if (this.advert[innerIndex].booth == 2) {
              firstIndex--;
              this.doubleAd++;
            }

            adPositions.push(firstIndex);
            this.usePosition++;
          }
        } else {
          let secondIndex;
          if (type == "pc") {
            secondIndex = 16 * Math.floor((i - 1) / 2) + 9 - 1;
          } else if (type == "mb") {
            secondIndex = 7 - 1 + ((i - 2) / 2) * 12;
          }

          if (secondIndex >= startIndex && secondIndex <= endIndex) {
            secondIndex = secondIndex - this.doubleAd;
            if (this.advert[innerIndex].booth == 2) {
              this.doubleAd++;
            }
            adPositions.push(secondIndex);
            this.usePosition++;
          }
        }
      }
      if (adPositions.length != 0) {
        this.insertAd(adPositions);
      }
	  this.setAdHeight();
    },
    //插入广告
    insertAd(indexArray) {
      if (this.productList.length != 0) {
        //广告条数少于广告位
        if (this.advert.length < this.adNum) {
          for (let i = 0; i < indexArray.length; i++) {
            if (this.useAdNum == this.advert.length) this.useAdNum = 0;
            if (i >= this.advert.length) {
              //当广告位多与广告时,循环增加广告到广告位
              const innerIndex = i % this.advert.length;
              let copyAd = JSON.parse(JSON.stringify(this.advert[innerIndex]));
              copyAd.id = generateUUID();
              this.productList.splice(indexArray[i], 0, copyAd);
            } else {
              let adUseList = this.advert.slice(this.useAdNum);
              this.productList.splice(indexArray[i], 0, adUseList[0]);
            }
            this.useAdNum++;
          }
        } else {
          //广告条数多于广告位
          for (let j = 0; j < indexArray.length; j++) {
            let adUseList = this.advert.slice(this.useAdNum);
            this.productList.splice(indexArray[j], 0, adUseList[0]);
            this.useAdNum++;
          }
        }
      }
    },
    //点击广告跳转链接
    goAdLink(url) {
      window.open(url);
    },
    //获取左边分类标签大类
    getLabel(fatherId) {
      return this.labelData.find((item) => item.id == fatherId).nameEn;
    },
    teaMobileActive(id) {
      let index = this.selectedParams.indexOf(id);
      if (index >= 0) {
        return true;
      }
      return false;
    },
    setSticky() {
      let topBar = document.querySelector(".topBar");
      let marginTop = document.querySelector(".marginTop");
      let marginBottom = document.querySelector(".marginBottom");
      this.breadCrumbsHeight = this.headerHeight;
      this.selectLabelHeight =
        this.breadCrumbsHeight +
        (typeof topBar?.offsetHeight === "number" ? topBar.offsetHeight : 0);
      if (topBar) {
        topBar.style.top = this.breadCrumbsHeight + "px";
      }
      let selectLabel = document.querySelector("#selectLabel");
      if (selectLabel) {
        selectLabel.style.top =
          this.selectLabelHeight +
          12 +
          (typeof marginTop?.offsetHeight === "number" ? marginTop.offsetHeight : 0) +
          "px";
      }
      if (marginBottom) {
        marginBottom.style.top = this.selectLabelHeight + 10 + "px";
      }
      if (marginTop) {
        marginTop.style.top = this.selectLabelHeight + 12 + "px";
      }
      this.$nextTick(() => {
        topBar.classList.add("litterMock");
        if (marginTop) marginTop.classList.add("litterMock2");
      });
      if (!this.isMobile) {
        let _this = this;
        this.myswiper1 = new Swiper(this.$refs.swiper1, {
          slidesPerView: 1,
          spaceBetween: 10,
          autoplay: false,
          loop: true,
          grabCursor: true,
          observer: true,
          observeParents: true,
          navigation: {
            nextEl: ".myswiper1 .swiper-button-next",
            prevEl: ".myswiper1 .swiper-button-prev",
          },
          on: {
            slideChangeTransitionEnd: function (value) {
              let swiperData = _this.isMobile
                ? _this.recommentLast.mbSellingProductImg
                : _this.recommentLast.sellingProductImg;
              swiperData.forEach(function (v, i) {
                if (i === value.activeIndex) {
                  if (_this.$refs["videoPlayer" + i]) {
                    _this.$refs["videoPlayer" + i][0].player.play();
                  }
                } else {
                  if (_this.$refs["videoPlayer" + i]) {
                    _this.$refs["videoPlayer" + i][0].player.pause();
                  }
                }
              });
            },
          },
        });
        let leftBar = document.querySelector(".leftBar");
        if (leftBar) {
          leftBar.style.top = this.selectLabelHeight + 10 + "px";
        }
      } else {
        if (selectLabel) {
          selectLabel.style.top =
            this.selectLabelHeight +
            (typeof marginTop?.offsetHeight === "number" ? marginTop.offsetHeight : 0) +
            "px";
        }
        if (marginBottom) {
          marginBottom.style.top = this.selectLabelHeight + "px";
        }
        if (marginTop) {
          marginTop.style.top = this.selectLabelHeight + "px";
        }
      }
	  this.setAdHeight();
    },
    async loadMoreData() {
      await this.loadThreeData(false);
      this.insertAdPosition();
    },
    async loadThreeData(type = false) {
      let num = this.page;
      if (!type) {
        num = this.page + 1;
        if (this.totalResult === 0 || this.pages < num || this.page <= 0) {
          return false;
        }
      }
      this.loadingProduct = true;
      let addProduct = await this.getProduct(num).then((res) => {
        return this.setDefaultShowImg(res);
      });
      this.productList = this.productList.concat(addProduct);
      this.loadingProduct = false;
    },
    listScrollTop() {
      let scrollTop =
        window.pageYOffset ||
        document.documentElement.scrollTop ||
        document.body.scrollTop;
      try {
        let marginTop = document.querySelector(".marginTop");
        let productList = document.querySelector("#productList");
        let selectLabel = document.querySelector("#selectLabel");
        let rect = productList.getBoundingClientRect();
        let scrollToPosition =
          rect.top +
          scrollTop -
          (this.selectLabelHeight +
            12 +
            (typeof marginTop?.offsetHeight === "number" ? marginTop.offsetHeight : 0) +
            (typeof selectLabel?.offsetHeight === "number"
              ? selectLabel.offsetHeight
              : 0));
        window.scrollTo({
          top: scrollToPosition,
          behavior: "smooth",
        });
      } catch (error) {}
    },
      activeCateName(item) {
          let data = this.isStockPage == "1" ? item.shopRouting : item.customRouting;
          return data && this.formatUrlName(data) === this.cateName;
      },
    getVideoOptions(item, type) {
      if (type === 1) {
        return {
          autoplay: true,
          controls: true,
          muted: true,
          loop: true,
          fill: true,
          preload: "auto",
          poster: item.imgUrl,
          sources: [
            {
              src: item.url,
              type: "video/mp4",
            },
          ],
        };
      } else if (type === 4) {
        return {
          autoplay: false,
          controls: false,
          muted: true,
          loop: true,
          fill: true,
          sources: [
            {
              src: path,
              type: "video/mp4",
            },
          ],
        };
      }
    },
    isImageType(url) {
      return isImageType(url);
    },
    setAdHeight() {
      this.adBoxHeight = "0px";
      this.$nextTick(() => {
        try {
          let productGoods = document.querySelector(".productGoods");
          this.adBoxHeight = productGoods.getBoundingClientRect().height + "px";
        } catch (error) {
          console.log(error);
        }
      });
    },
  },
  async mounted() {
    if (process.env.isManage) {
      this.isManage = true;
      return false;
    }
    if (this.isMobile) {
      this.showFilter = false;
    }
    let btnBox = document.querySelectorAll(".btn-box");
    Array.from(btnBox).forEach((item) => {
      let button = item.querySelector("button");
      if (button) {
        button.addEventListener("click", () => {
          this.listScrollTop();
        });
      }
    });
    let header = document.querySelector("#modalHeader");
    this.headerHeight =
      typeof header?.offsetHeight === "number" ? header.offsetHeight : 0;
    this.halfName = this.$route.params.halfDesign;
    this.debounceSearchProduct = debounce(this.searchProduct, 600);
    this.screenHeight = window.innerHeight;
    this.screenWidth = window.innerWidth;
    this.cateName = this.$route.params.pathMatch;
    window.onresize = debounce(this.handleResize,300);
    Promise.all([
      this.getAppLabelAttributeList(),
      listAllCategoryByParentId({
        id: this.parentCateId || this.cateId,
        onlyGetStockCate: this.isStockPage,
      }),
      getAdvertList({ categoryId: this.parentCateId || this.cateId }),
    ]).then((result) => {
      let labelData = result[0].data,
        cateList = result[1].data;
      labelData.forEach((item) => {
        if (item.isMore) item.moreText = true;
        if (item.isExpand) {
          this.activeNames.push(item.id);
        }
      });
    //   if (this.cateType == 2) {
    //     labelData = labelData.filter((item) => {
    //       return item.nameEn !== "MATERIAL" && item.nameEn !== "GENDER";
    //     });
    //   }
      this.activeNames.push(-1);
      this.activeNames = [...new Set([...this.activeNames])];
      this.labelData = labelData;
      //左侧选择栏没有数据关闭
      if (!this.labelData || this.labelData.length == 0) this.showFilter = false;
      this.cateList = cateList;
      let name = this.cateList.find((item) => {
        return item.id == this.cateId;
      });
      if (name) {
        this.fatherCateName = name.name;
        this.$store.commit("halfDesign/setIsFastQuote", Boolean(name.isFastQuote));
        this.cateList = cateList.filter((item) => {
          return item.id == this.cateId;
        });
		if(name.attributeValueId){
			if (!this.labelData || this.labelData.length == 0) return
			let colorData=this.labelData.find((item) => {
				return item.attributeFlag=='color'
			})
			if(colorData){
				colorData?.attributeList.forEach((item) => {
					if(item.id==name.attributeValueId) this.toggleColor(item)
				})
			}
		}
      } else {
        this.$store.commit("halfDesign/setIsFastQuote", false);
        this.fatherCateName = this.halfCateDTO.name;
      }
      //获取广告数据
      let adData = result[2].data;
      if (adData.length && adData[0]?.advertiseList?.length) {
        this.adData = adData[0];
        this.advert = adData[0].advertiseList.filter((obj) => obj.isEnable == 1);
        this.adNum = Math.floor((Number(adData[0].advertiseNum) + 1) / 2);
      }
      this.$nextTick(() => {
        this.fristWatch = true;
        //插入广告
        this.insertAdPosition();
        this.setSticky();
      });
      this.callFn1();
    });
  },
  beforeDestroy() {
    window.onresize = null;
  },
};
</script>

<style scoped lang="scss">
[theme="6"] {
  .bps-container {
    line-height: 1.3;
    padding: 4.25em max(calc(50% - 850px), 1.5vw) !important;
	padding-top: 0 !important;
  }
}
::v-deep .custom-slider .v-range-slider__track-background {
  background-color: $color-second !important;
  /* 设置轨道背景颜色 */
}

// ::v-deep .custom-slider .v-slider__thumb {
// 	background-color: $color-second !important; /* 设置滑动块滑动后的颜色 */
// }
::v-deep .custom-slider .v-range-slider__track-fill {
  background-color: $color-primary !important;
  /* 设置你想要的背景颜色 */
}

.bps-container {
  @include respond-to(mb) {
    font-size: 12px;
  }
}

.loadFirstData {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.find-agent {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-bottom: 10px;
  width: 100%;
  height: 100%;
  padding: 20px;
  text-align: center;
  background: url(https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20221018/2050Ntxm6NPD.png)
    center/cover no-repeat;

  .c-title {
    font-size: 48px;
    color: #00156b;
  }

  .des {
    max-width: 680px;
    margin: 10px 0 20px;
    font-size: 32px;
    color: #00156b;
  }

  .v-btn {
    width: 200px;
    margin: 0 10px;
  }

  @media screen and (max-width: $mb-width) {
    .c-title {
      font-size: 24px;
    }

    .des {
      font-size: 16px;
    }

    .v-btn {
      width: 80%;
      margin-bottom: 10px;
    }
  }
}

.not-find {
  width: 100%;

  .result-tip {
    height: 47px;
    background-color: #000;

    span {
      font-size: 18px;
      font-weight: bold;
      line-height: 47px;
      letter-spacing: 0em;
      color: #ffffff;
      padding-left: 26px;
    }
  }

  .result-box {
    width: 100%;
    height: 424px;
    overflow: hidden;
    background-color: #fff;
    margin: 10px auto;

    .result-main {
      width: 58%;
      padding: 33px 0;
      margin: 0 auto;

      .main-tip1 {
        font-size: 24px;
        font-weight: 500;
        line-height: 30px;
        letter-spacing: 0em;
        color: #3d3d3d;
      }

      .main-tip2 {
        margin: 30px 0;

        .tip2 {
          font-weight: 900;
          font-size: 18px;
          line-height: 30px;
          color: #3d3d3d;
        }

        ul {
          li {
            line-height: 30px;
            font-weight: 300;
            font-size: 18px;
            list-style: disc;
          }
        }
      }

      .main-tip3 {
        margin-bottom: 20px;

        .tip3 {
          font-weight: 900;
          font-size: 18px;
          line-height: 30px;
          color: #3d3d3d;
        }

        .tip31 {
          line-height: 30px;
          font-weight: 300;
          font-size: 18px;

          a {
            font-weight: 500;
            color: $color-primary;
            text-decoration: underline;
          }
        }
      }
    }
  }

  @include respond-to(mb) {
    .result-tip {
      span {
        font-size: 14px;
      }
    }

    .result-box {
      height: 474px;

      .result-main {
        width: 92%;

        .main-tip1 {
          font-size: 16px;
        }

        .main-tip2 {
          margin: 10px 0;

          .tip2 {
            font-size: 14px;
          }

          ul li {
            font-size: 14px;
          }
        }

        .main-tip3 {
          .tip3 {
            font-size: 14px;
          }

          .tip31 {
            font-size: 14px;
          }
        }
      }
    }
  }
}

.wrap {
  display: flex;
  flex-direction: column;
  // background-color: #f2f2f2;
  background-color: #fff;

  @include respond-to(mb) {
    padding-bottom: 10px;
  }
}

img {
  width: auto;
  max-width: 100%;
  max-height: 100%;
  vertical-align: middle;
}

.breadCrumbs {
  display: flex;
  margin: 20px 0;

  b {
    margin: 0 4px;
    font-size: 12px;
  }

  .bread-item {
    display: flex;
    align-items: center;
    cursor: pointer;

    .bread-item-span {
      font-size: 1em;
      font-weight: 400;
      cursor: auto;
    }
  }

  .bread-item.is-link span {
    color: $color-primary;
  }

  .bread-item:last-child b {
    display: none;
  }

  @include respond-to(mb) {
    margin: 10px 0;
    font-size: 14px;

    .bread-item-span {
      font-size: 12px !important;
    }
  }
}

.topBar {
  display: grid;
  grid-template-columns: 265px 1fr;
  grid-column-gap: 20px;
  height: 50px;
  background: #ffffff;
  box-shadow: 1px 1px 4px 0px rgba(0, 0, 0, 0.06);
  border-radius: 4px;
  margin: 0 0 10px 0;

  &.sticky {
    position: sticky;
    top: 0;
    z-index: 4;
  }

  .top-left {
    justify-self: flex-start;
    align-self: center;
    font-size: 18px;
    padding: 0 4px;
  }

  .top-right {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .filterSearch {
      flex: 1;
      display: flex;
      align-items: center;

      &.textSearch {
        // justify-content: center;
      }
    }

    .search-area {
      position: relative;

      input {
        min-width: 400px;
        background-color: #f3f3f3;
        border-radius: 18px;
        height: 36px;
        line-height: 36px;
        outline: none;
        border: none;
        padding: 0 50px 0 20px;
      }

      i {
        position: absolute;
        right: 20px;
        top: 50%;
        transform: translateY(-50%);
        font-size: 30px;
        color: #9b9b9b;
          cursor: pointer;
      }
    }

    .filter-area {
      display: flex;
      justify-content: space-between;
      align-items: center;

      & > div {
        margin: 0 20px;
      }

      .filter1 {
        display: flex;
        align-items: center;
        cursor: pointer;

        b {
          margin-right: 4px;
          font-size: 18px;
        }
      }

      .filter2 {
        display: flex;
        align-items: center;

        .t1 {
          margin-right: 4px;
        }

        b {
          font-size: 12px;
        }
      }

      .filter3 {
        display: flex;
        align-items: center;
        border: 2px solid $color-primary;
        border-radius: 4px;

        svg {
          color: $color-primary;
        }

        & > div {
          padding: 4px 8px;
          cursor: pointer;
        }

        & > div.active {
          background-color: $color-primary;

          svg {
            color: #ffffff;
          }
        }
      }

      .v-btn {
        text-transform: capitalize;
      }
    }
  }

  @include respond-to(mb) {
    grid-template-columns: 1fr;
    font-size: 14px;
    min-height: 50px;
    width: 100%;
    overflow: hidden;

    .top-left {
      display: none;
    }

    .top-right {
      flex: 1;
      // height: 80px;
      background: #ffffff;
      border-radius: 4px;
      box-shadow: 1px 1px 4px 0px rgba(0, 0, 0, 0.06);
      flex-direction: row-reverse;
      justify-content: center;
      width: 100%;

      .search-area {
        position: relative;
        width: 100%;
        overflow: hidden;

        .inputBox {
          width: 90%;
          margin: 0 auto;
          text-align: center;
        }

        input {
          min-width: 100%;
          background-color: #f3f3f3;
          border-radius: 18px;
          height: 30px;
          line-height: 30px;
          outline: none;
          border: none;
          padding: 0;
          padding-left: 20px;
        }

        i {
          position: absolute;
          right: 30px;
          top: 50%;
          transform: translateY(-50%);
          font-size: 30px;
          color: #9b9b9b;
        }
      }

      .filter-area {
        font-size: 12px;
        width: 35%;
        //width: 0;

        .filter2 {
          display: none;
        }

        & > div {
          margin: 0 10px;
        }
      }
    }

    .breadCrumbs {
      display: flex;
    }
  }
}

.content {
  position: relative;
  display: grid;
  align-items: flex-start;
  grid-column-gap: 20px;
  grid-template-columns: 265px 1fr;
  padding-bottom: 50px;

  .loadProgress {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    padding: 10px 0;
  }

  .load {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
    padding: 20px;
  }

  &.noFilter {
    grid-template-columns: 1fr;
    grid-column-gap: 18px;
    grid-row-gap: 0px;
    padding-bottom: 50px;

    .rightPart {
      .rightContent {
      }
    }
  }

  .leftBar {
    position: sticky;
    top: 0;
    overflow: hidden;
    // overflow: hidden auto;
    max-height: 800px;
    display: block;
    width: 265px;
    padding: 10px 10px 40px;
    border-radius: 4px;
    border: 1px solid #f6f6f6;
    background-color: #ffffff;
    box-shadow: 1px 1px 4px 0px rgba(0, 0, 0, 0.06);

    &::-webkit-scrollbar {
      display: none;
    }

    .filter {
      font-size: 14px;

      .top {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;

        a {
          color: $color-primary;
          text-decoration: underline;
        }

        span {
          color: #c4c4c4;
          font-style: italic;
        }
      }
    }

    .collapse {
      overflow: hidden auto;
      padding: 10px 10px 40px;

      &::-webkit-scrollbar {
        display: none;
      }

      .collapse-item {
        .sizeScroll {
          max-height: 145px;
          overflow-y: auto;
        }
      }

      .collapse-item-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 50px;
        font-size: 16px;
        cursor: pointer;
        text-transform: uppercase;

        b {
          font-size: 12px;
        }

        .active {
          transform: rotate(-180deg);
        }
      }

      .con {
        ::v-deep .v-label {
          font-size: 14px;
        }

        .showBtn {
          font-size: 14px;
          color: $color-primary;
          margin: 5px 0;
          text-decoration: underline;
          cursor: pointer;
        }

        .price-range-box {
          padding: 0 10px;
          text-align: right;

          i {
            font-size: 16px;
            color: #666666;
            margin-right: -8px;
            cursor: pointer;
          }
        }

        .price-des {
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-size: 14px;
        }

        .color-box {
          .color-wrap {
            display: grid;
            grid-template-columns: repeat(6, 1fr);
            grid-column-gap: 4px;
            grid-row-gap: 4px;

            .color-item {
              display: flex;
              justify-content: center;
              align-items: center;
              width: 35px;
              height: 35px;
              background: #ffffff;
              border: 1px solid #cccccc;
              border-radius: 50%;
              cursor: pointer;

              &:hover {
                border-color: $color-primary;
                border-width: 2px;
              }

              &.active {
                border-color: $color-primary;
                border-width: 2px;
              }

              span {
                display: inline-block;
                width: 27px;
                height: 27px;
                border-radius: 50%;
                border: 1px solid #ccc;
              }

              img {
                width: 27px;
                height: 27px;
                border-radius: 50%;
              }
            }
          }
        }

        .checkbox-style2 ::v-deep {
          display: grid;
          grid-template-columns: repeat(2, 1fr);
        }
      }
    }
  }

  .leftBar[disabled] {
    pointer-events: none;
  }

  .productCateNameBox {
    width: 100%;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 8px;

    .leftTitle {
      position: absolute;
      left: 0;
      top: 0;
    }

    @include respond-to(mb) {
      margin: 10px 0;

      .leftTitle {
        position: relative;
      }
    }
  }

  .productCateName {
    font-weight: 400;
    font-size: 22px;
    color: #333333;
    z-index: 2;
    &::before {
      content: "";
      width: 100%;
      height: 4px;
      position: absolute;
      left: 0;
      top: -4px;
      background-color: #fff;
    }

    &.marginTop {
      padding: 20px 0 0 0;
      background-color: #fff;
    }

    @include respond-to(mb) {
      font-size: 14px;
    }
  }

  .recommendProduct {
    margin-bottom: 20px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 14px;
    overflow: hidden;

    .recomItem {
      position: relative;
      // background: #F6F6F6;
      // box-shadow: 1px 1px 4px 0px rgba(0, 0, 0, 0.06);
      height: auto;
      min-height: 450px;
      overflow: hidden;
      display: flex;
      flex-direction: column;

      .flexBox {
        overflow: hidden;
        flex: 1;
        display: flex;
      }

      .backImg {
        position: absolute;
        inset: 0;
        width: 100%;
        height: 100%;
        // object-fit: fill;
      }

      .absoluteImg2 {
        position: absolute;
        right: 0;
        bottom: 0;
      }

      &.doubleItem {
        grid-column: span 2;
        height: auto;
        min-height: 320px;
      }

      .itemBox {
        position: relative;
        padding: 30px;
        width: 100%;
        height: 100%;
        z-index: 1;
        display: flex;
        flex-direction: column;
        align-items: flex-start;

        .ulFlex {
          flex: 1;
          display: flex;
          flex-direction: column;
          row-gap: 20px;

          .detailBtn {
            flex-shrink: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            width: fit-content;

            span {
              cursor: pointer;
              border-radius: 4px;
              background: linear-gradient(90deg, #f8430d, #fea33a);
              font-weight: bold;
              font-size: 16px;
              color: #ffffff;
              padding: 0.6em 2em;
            }
          }
        }

        &.halfBox {
          width: 43%;
        }

        &.halfBox2 {
          padding: 0;
          padding-left: 20px;
          width: 50%;
        }

        &.invert {
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          column-gap: 10px;

          .itemLeft {
            width: 50%;
          }

          .itemRight {
            width: 50%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;

            .itemRight_textBox {
              width: max-content;
              max-width: 50%;
              text-align: left;
              margin: 0 auto;
              overflow: hidden;

              .detailBtn {
                margin-top: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                width: fit-content;

                span {
                  cursor: pointer;
                  border-radius: 4px;
                  background: linear-gradient(90deg, #f8430d, #fea33a);
                  font-weight: bold;
                  font-size: 16px;
                  color: #ffffff;
                  padding: 0.6em 2em;
                }
              }
            }

            ul {
              width: 100%;
              text-align: left;
              overflow: hidden;
            }
          }
        }
      }

      .itemBox2 {
        position: relative;
        flex: 1;
        z-index: 1;

        img {
          width: 100%;
          height: 100%;
          object-fit: contain;
        }
      }

      .itemBox3 {
        position: relative;
        flex: 1;
        z-index: 1;

        img {
          width: 100%;
          height: 100%;
          object-fit: contain;
        }
      }

      .recomH3 {
        z-index: 1;
        font-weight: 400;
        font-size: 22px;
        margin-bottom: 10px;

        &.recomH3_2 {
          padding: 20px 0 0 20px;
        }
      }

      .recomText {
        font-weight: 400;
        font-size: 18px;
        margin-bottom: 10px;

        .boldFont {
          font-size: 22px;
          font-weight: bold;
        }
      }

      ul {
        margin-left: 0;
        padding-left: 0;
      }

      .recomLi {
        list-style: none;
        font-weight: 400;
        font-size: 16px;
        color: #666666;
        position: relative;

        ::v-deep p {
          margin: 0 !important;
        }

        span {
          position: relative;
          padding-left: 10px;

          &::before {
            content: "";
            position: absolute;
            left: 0;
            top: 0.5em;
            width: 4px;
            height: 4px;
            background-color: #666666;
          }
        }
      }
    }

    @include respond-to(mb) {
      grid-template-columns: 1fr;
      grid-auto-rows: minmax(auto, 1fr);
      align-items: stretch;

      .recomItem {
        height: auto;
        min-height: auto;

        .itemBox {
          padding: 10px;
          width: 55% !important;

          &.halfBox {
            width: 55% !important;
          }
        }

        &.doubleItem {
          height: auto;
          min-height: auto;
          grid-column: auto;
        }

        .flexBox {
          width: 100%;
          height: 100%;
          flex-direction: row-reverse;

          .itemBox {
            padding: 0 10px;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            justify-content: center;
            text-align: left;
            overflow: hidden;

            .ulFlex {
              flex: initial;
              width: 100%;
              row-gap: normal;

              .detailBtn {
                margin-top: 8px;
                span {
                  font-size: 14px;
                }
              }
            }

            .recomH3 {
              font-size: 14px;
              margin-bottom: 4px;
            }

            .recomText {
              font-size: 14px;
              margin-bottom: 4px;
              align-self: flex-start;

              .boldFont {
                font-size: 16px;
              }
            }

            .recomLi {
              font-size: 12px;
            }
          }

          .itemBox3 {
            display: flex;
            align-items: center;
            justify-content: center;

            .absoluteImg2 {
              position: relative;
            }
          }
        }
      }
    }
  }

  .selectLabel {
    width: 100%;
    padding: 10px;
    margin-bottom: 10px;
    background: #ffffff;
    // box-shadow: 1px 1px 4px 0px rgba(0, 0, 0, 0.06);
    overflow: hidden;

    .filter {
      font-size: 14px;

      .clearFilter {
        margin: 2px;
        padding: 5px 15px;

        a {
          color: $color-primary;
          text-decoration: underline;
        }
      }

      .top {
        display: flex;
        flex-wrap: wrap;

        .total {
          color: #c4c4c4;
          font-style: italic;
          margin: 1em 0 0;
        }

        .category-item {
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 2px;
          padding: 5px 15px;
          border-radius: 14px;
          // cursor: pointer;

          &.active {
            // color: $color-primary;
            color: #000;
            // font-weight: bold;
          }

          &.active:hover {
            // color: #ffffff;
          }

          &.tag {
            // font-weight: 700;
          }

          &:hover {
            // color: #ffffff;
            // background: $color-primary;

            .v-icon {
              // color: #ffffff;
              cursor: pointer;
            }
          }

          span {
            b {
              font-size: 10px;
            }
          }
        }

        .viewMore {
          display: flex;
          align-items: center;
          margin: 2px;
          padding: 5px 15px;
          cursor: pointer;
          color: $color-primary;
          white-space: nowrap;

          b {
            font-size: 10px;
          }

          &:hover {
            opacity: 0.8;
            text-decoration: underline;
          }
        }
      }
    }

    @include respond-to(mb) {
      margin-bottom: 10px;
      background: #ffffff;
      box-shadow: 1px 1px 4px 0px rgba(0, 0, 0, 0.06);

      .filter {
        font-size: 14px;

        .clearFilter {
          padding: 5px;
        }

        .top {
          width: 100%;
          overflow-x: auto;
          flex-wrap: nowrap;
          padding-bottom: 5px;

          .customLabel {
            height: 2em;
          }

          .label {
            padding-left: 0;
          }

          .category-item {
            white-space: nowrap;
            padding: 5px;
          }
        }
      }
    }
  }

  .rightPart {
    position: relative;
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;

    .rightContent {
      flex: 1;
      width: 100%;
      height: 0;
      position: relative;

      .productWrap {
        // align-items: flex-start;
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        grid-gap: 10px;

        @include respond-to(pad) {
          grid-template-columns: repeat(3, 1fr);
        }

        @include respond-to(mb) {
          grid-template-columns: repeat(1, 1fr);
          grid-gap: 10px;
        }
      }

      .inserted_element {
        grid-column: span 2;
        grid-row: span 1;

        @include respond-to(mb) {
          grid-column: span 1;
        }
      }

      @media (any-hover: hover) {
        // .goods:hover .good-info,
        // .goods:hover .good-img .good-color {
        // 	background-color: var(--color-second);
        // }
        .good-item:hover .good-back {
          background-color: var(--color-second);
          background-color: #fff7f2;
        }
      }
    }
  }

  @include respond-to(mb) {
    grid-template-columns: 1fr;
    padding: 0;

    .leftBar {
      position: fixed;
      width: 100%;
      height: 100%;
      top: 0;
      left: 0;
      z-index: 100000;
      padding: 0 10px 40px;
      // max-height: 400px !important;

      .leftBar-header {
        position: sticky;
        top: 0;
        margin-left: -10px;
        width: calc(100% + 20px);
        height: 50px;
        background-color: $color-primary;
        color: #fff;
        z-index: 99;

        .filter {
          text-align: center;
          font-weight: bold;
          font-size: 1.5em;
          line-height: 50px;
          margin: 0;
        }

        b {
          position: absolute;
          top: 0;
          right: 14px;
          line-height: 50px;
          z-index: 2;
          font-size: 14px;
        }
      }

      .filter-mb {
        .filter-mb-title {
          height: 50px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          cursor: pointer;
          text-transform: uppercase;
          font-weight: bold;
          font-size: 16px;
          color: #000;
          line-height: 50px;

          b {
            font-size: 12px;
          }

          .active {
            transform: rotate(-180deg);
          }
        }

        .con {
          height: 30px;
          display: flex;
          align-items: center;
          // justify-content: center;
          font-size: 14px;

          .con-radio {
            margin: 0 30px 0 10px;
          }

          label {
            display: flex;
            align-items: center;
            flex-direction: row-reverse;

            span {
              margin-right: 10px;
            }
          }

          /* 未选中状态下的样式 */
          input[type="radio"] {
            /* 隐藏原始的单选按钮 */
            display: none;
          }

          /* 自定义样式 */
          .custom-radio {
            display: inline-block;
            width: 14px;
            height: 14px;
            border-radius: 50%;
            border: 1px solid #333;
            position: relative;
            cursor: pointer;
          }

          /* 选中状态下的样式 */
          .custom-radio:before {
            content: "";
            display: block;
            width: 6px;
            height: 6px;
            background-color: #fff;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            border-radius: 50%;
          }

          /* 选中状态下的外圈样式 */
          input[type="radio"]:checked + .custom-radio {
            background-color: $color-primary !important;
            border: 1px solid $color-primary;
          }
        }
      }

      .collapse {
        .collapse-item {
          .collapse-item-title {
            font-size: 14px;
          }
        }
      }
    }
  }
}

.allowMsg {
  padding: 10px;
  margin-block: 10px 15px;
  background-color: #fff1ea;
  cursor: pointer;

  .allow-header {
    display: flex;
    align-items: center;

    .allow-checkbox {
      height: 2em;
      width: 2em;
      background-color: #fff;
      border-radius: 2px;
      border: 1px solid #d24600;
      position: relative;

      &.active::before {
        content: "";
        position: absolute;
        left: 50%;
        top: 50%;
        height: 18px;
        width: 8px;
        transform: translate(-50%, -60%) rotate(45deg);
        border-right: 1px solid #d24600;
        border-bottom: 1px solid #d24600;
      }
    }

    .allow-title {
      font-weight: 400;
      font-size: 16px;
      margin-left: 10px;
    }
  }

  .allow-content {
    font-size: 11px;
    margin-top: 5px;
    line-height: 1.5;
  }
}

.loadBtnMainBox {
  width: calc(100% - 265px);
  margin-left: 265px;

  @include respond-to(mb) {
    width: 100%;
    margin-left: 0;
  }
}

.loadMoreBtn {
  width: 30%;
  min-width: 350px;
  margin: 0 auto;
  margin-bottom: 50px;
  text-align: center;
  font-weight: 400;
  font-size: 22px;
  color: #333333;

  .loadMoreBtnText {
    margin-bottom: 20px;
  }

  .loadBtnBox {
    margin-top: 20px;
    color: $color-primary;
    cursor: pointer;
    font-size: 16px;
    font-weight: bold;

    .loadBtn {
      display: inline-block;
      border: 2px solid $color-primary;
      padding: 8px 40px;
      border-radius: 5px;
      transition: 0.3s;

      &:hover {
        background-color: $color-second;
      }
    }
  }

  @include respond-to(mb) {
    min-width: 300px;
    font-size: 18px;

    .loadMoreBtnText {
      margin-bottom: 10px;
    }

    .loadBtnBox {
      margin-top: 10px;
      font-size: 14px;
    }
  }
}

.sticky {
  position: sticky;
  top: 0;
  z-index: 1;
}

.litterMock::before {
  content: "";
  width: 100%;
  height: 10px;
  position: absolute;
  bottom: -10px;
  left: 0;
  background-color: inherit;
}

.litterMock::after {
  content: "";
  width: 100%;
  // height: 10px;
  position: absolute;
  top: -10px;
  left: 0;
  background-color: inherit;
}

.litterMock2::after {
  content: "";
  width: 100%;
  height: 12px;
  position: absolute;
  bottom: -12px;
  left: 0;
  background-color: inherit;
}

.changeLoad {
  position: absolute;
  inset: 0;
  width: 100%;
  // height: 100vh;
}

.overlay {
  align-items: center;
  border-radius: inherit;
  display: flex;
  justify-content: center;
  pointer-events: auto;
  width: 100%;
  background-color: rgba(0, 0, 0, 0.1);
}

/* HTML: <div class="loader"></div> */
.loader {
  width: 50px;
  aspect-ratio: 1;
  display: grid;
  border: 4px solid #0000;
  border-radius: 50%;
  border-right-color: $color-primary;
  animation: l15 1s infinite linear;
}

.loader::before,
.loader::after {
  content: "";
  grid-area: 1/1;
  margin: 2px;
  border: inherit;
  border-radius: 50%;
  animation: l15 2s infinite;
}

.loader::after {
  margin: 8px;
  animation-duration: 3s;
}

@keyframes l15 {
  100% {
    transform: rotate(1turn);
  }
}

.swiper.myswiper1 {
  --swiper-navigation-color: #ffffff;
  /* 单独设置按钮颜色 */
  --swiper-navigation-size: 20px;

  /* 设置按钮大小 */
  .swiper-button-next,
  .swiper-button-prev {
    width: 46px;
    height: 46px;
    background: #000000;
    opacity: 0.4;
    border-radius: 50%;
    transform: translateY(-50%);
    margin-top: 0;
  }
}

.myswiper1 {
  height: 100%;

  @media screen and (max-width: $mb-width) {
    .zoom {
      position: absolute;
      top: 0;
      right: 0;
      cursor: pointer;
      z-index: 100;

      b {
        font-size: 22px;
        margin-right: 4px;
      }

      &:hover {
        b {
          color: $color-primary;
        }
      }
    }

    .swiper-slide {
      display: flex;
      justify-content: center;
      align-items: center;

      .smallImg {
        position: relative;
        width: 100%;
        height: 100%;
        text-align: center;
      }
    }

    img {
      max-width: 100%;
      max-height: 100%;
      object-fit: contain;
    }
  }

  .zoom {
    position: absolute;
    top: 0;
    right: 0;
    cursor: pointer;
    z-index: 100;

    b {
      font-size: 22px;
      margin-right: 4px;
    }

    &:hover {
      b {
        color: $color-primary;
      }
    }
  }

  .swiper-slide {
    display: flex;
    justify-content: center;
    align-items: center;

    .smallImg {
      position: relative;
      width: 100%;
      height: 100%;
      text-align: center;

      img {
        object-fit: contain;
      }
    }
  }

  img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
  }
}

.marginBottom {
  width: 100%;
  height: 1px;
  border-bottom: 2px solid #f6f6f6;
  margin-top: 20px;
}
</style>
