<template>
    <div :theme="$store.state.proTheme" class="modal-box" :class="modal.class" :style="modal.style"
        @click.self="setModalType({}, {}, 'template_list')">
        <template v-for="(o, oi) in modal.outer">
            <div class="title">
                <div class="title_h1">
                    <EditDiv tagName="h2" :style="modal.titleStyle" v-model:content="o.title.value" v-if="o.title"
                        @click="setModalType(o.title, modal.outer, 'text')" />

                    <div class="sub-title" 
                        :style="{
                            textAlign: modal.float ? 'left' : '',
                            ...modal.subTitleStyle,
                            marginBottom: !modal.isNew && isMobile ? '0' : '1.5em'
                        }"
                        :hidden="!o.title && !o.subTitle" :pointer="o.subTitle?.event"
                        @click="setModalType(o.subTitle, modal.outer, 'text', o.subTitle.event)">
                        <EditDiv v-model:content="o.subTitle.value" v-if="o.subTitle" />
                    </div>
                </div>
            </div>

            <div class="templates1">
                <div class="templates_fenlei" :style="modal.fenleiStyle"
                    :class="{ 'swiperClass': modal.childTemplate && parentTag.id, 'swiperClass2': modal.childTemplate }">
                    <div class="title_Templates" v-show="modal.parentTemplate && !isMobile">
                        <div>
                            <span @click="templatesTitle">{{ lang.templates }}</span>
                            <span>> {{ parentTag.templateTypeName }}</span>
                            <span style="color:#999"
                                v-if="modal.childIndex && modal.parentIndex && parentTag.childList"> >
                                {{ parentTag.childList.find(i => i.id == modal.childIndex).templateTypeName }}</span>
                        </div>
                        <div class="btn">
                            <button icon v-for="(item, index) in btnList" :key="index"
                                :class="isbtn == index ? 'buJu2' : 'buJu1'" @click="typeClick(item, index)">
                                <b :class="item.Icon"></b>
                            </button>
                        </div>
                    </div>
                 <hr v-if="modal.childTemplate && !isMobile" />
                    <div v-show="modal.childTemplate" class="templates_sorting"
                        :class="{ 'templates_sorting_h': modal.childTemplate, 'templates_sorting_h2': modal.childTemplate && parentTag.id }">

                        <b pointer v-if="modal.showArrow && !isMobile && getlength"
                            :class="modal.showArrow" :disabled="scrollIndex == 0"
				            :style="{ ...modal.outArrowStyle, ...modal.outArrow1Style }"
                            @click.stop="scrollBox(-1)"></b>

                            <div class="templates_c" :scrollbar="modal.scroll">
                                <!-- pc端父级导航 -->
                                <div class="templates_div" 
                                v-show="modal.parentTemplate && !parentTag.id && !isMobile">
                                    <div>{{ lang.all }}</div>

                                    <div v-for="(item, parentIndex) in parentSortList"
                                    :key="parentIndex"
                                    :class="modal.isNew && isTemplateTypeName === item.id ? 'classNameActive' : 'className'"
                                        @click="templatesClick(item)">
                                        {{ item.templateTypeName }}
                                    </div>
                                </div>
                                <!-- mb端父级导航 -->
                                <!-- 没有子级id，是手机端 -->
                               <div class="templates_div_Mb" :style="!parentTag.id && isMobile ? 'height:17vw' : ''">
                                    <div class="swiper mySwiper1" ref="swiper1"
                                        v-show="isMobile && isActiveSwiper && modal.parentTemplate && !parentTag.id">
                                        <div class="swiper-wrapper">
                                            <div class="swiper-slide"
                                            :class="modal.isNew && isTemplateTypeName === item.id ? 'className2Active' : 'className2'"
                                            v-for="(item, parentIndex) in parentSortList"
                                                :key="parentIndex" @click="templatesClick(item)">
                                                {{ item.templateTypeName }}
                                            </div>
                                        </div>
                                        <div class="swiper-pagination" v-if="!modal.isNew"></div>
                                    </div>
                                    <div class="swiper-button-prev" v-if="modal.isNew && isMobile && !parentTag.id"></div>
                                    <div class="swiper-button-next" v-if="modal.isNew && isMobile && !parentTag.id"></div>
                               </div>

                                <div class="templates_isActive" v-show="parentTag.id">
                                    <div class="select3" v-show="modal.parentTemplate">
                                        <div :class="isActive ? 'select2' : ''">
                                            <div @click="selectClick">
                                                <span>{{ parentTag.templateTypeName }}</span>
                                                <b class="icon-Down" v-if="!isActive"></b>
                                                <b class="icon-Up" v-if="isActive"></b>
                                            </div>
                                            <ul v-if="isActive">
                                                <li v-for="(li, parentIndex) in parentSortList" :key="parentIndex"
                                                    @click="templatesClick(li)">
                                                    <span>{{ li.templateTypeName }}</span>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                    <!-- pc端子级导航 -->
                                    <div :class="modal.parentTemplate ? 'className_div' : 'className_div2'" v-show="!isMobile">
                                        <div v-for="(i, childIndex) in parentTag.childList" :key="childIndex" class="className"
                                            :class="{ select: i.id == modal.childIndex && modal.isNew !== true,'classNameActive': modal.isNew && i.id == modal.childIndex }"
                                            @click="subclassificationClick(i)">
                                            {{ i.templateTypeName }} ({{ i.bandParamIdList.split(',').length }})
                                        </div>
                                    </div>

                                    <!-- MB端子级导航 -->
                                    <!-- (旧模版，没有父级，子级长度超过3个；或者 旧模版，有父级)这是有轮播分页的情况加高度-->
                                    <!-- (旧模版，只有子级)这是没有轮播分页的情况加上边距-->
                                    <div :style="(!modal.isNew && !modal.parentTemplate && parentTag?.childList?.length > 3) || (!modal.isNew && modal.parentTemplate) ? 'height: 15vw' : !modal.isNew ? 'margin-top:5vw;' : ''" v-show="isMobile">
                                        <div class="swiper mySwiper2" ref="swiper">
                                            <div class="swiper-wrapper">
                                                <div class="swiper-slide className2"
                                                    v-for="(i, childIndex) in parentTag.childList" :key="childIndex"
                                                    :class="{ select: i.id == modal.childIndex && modal.isNew !== true,'classNameActive': modal.isNew && i.id == modal.childIndex }"
                                                    @click="subclassificationClick(i)">
                                                    {{ i.templateTypeName }} ({{ i.bandParamIdList.split(',').length }})
                                                </div>
                                            </div>
                                            <div class="swiper-pagination"></div>
                                        </div>
                                        <div class="swiper-button-prev" v-if="modal.isNew && isMobile"></div>
                                        <div class="swiper-button-next" v-if="modal.isNew && isMobile"></div>
                                    </div>
                                </div>

                            </div>
                        <b pointer v-if="modal.showArrow && !isMobile && getlength"
                            :class="modal.showArrow" :disabled="scrollIndex == modal.column - 1"
				            :style="{ ...modal.outArrowStyle, ...modal.outArrow2Style }"
                            @click.stop="scrollBox(1)"></b>
                    </div>
                </div>
                <div class="mobanPCMb"
                    :class="{ moban: templatesActive, moban1: templatesActive1, moban2: templatesActive2 }"
                    :style="modal.mobanStyle">
                    <div v-for="(item, index) in templatesList" :key="index" :cornerLabel="item.cornerPosition || 'left'">
                        <pic :src="item.showImgObj" :alt="item.alias" class="imgStyle" />

						<div v-if="item.cornerLabel?.id" flex class="corner-label" :onlyImg="!item.cornerLabel.name"
							 :style="{ color: item.cornerLabel.color, backgroundImage: 'url(' + item.cornerLabel.bgImg + ')' }">
							<pic v-if="item.cornerLabel.icon" :src="item.cornerLabel.icon" :alt="item.cornerLabel.name" />
							{{ item.cornerLabel.name }}
						</div>

                        <div class="moban_neirong">
                            <div class="aliasStyle">{{ item.alias }}</div>
                            <div class="price-box">
                                <div>{{ lang.from }}&nbsp; </div>
                                <CCYRate class="price" :price="item.price * (1 - (item.websiteDiscount || 0))">
                                </CCYRate>
                                <del>
                                    <CCYRate :price="item.price"></CCYRate>
                                </del>
                            </div>
                            <div>({{ lang.up }} {{ (item.websiteDiscount || 0) * 100 }}% {{ lang.off }})</div>
                            <div class="moban_neirong_div3">
                                <!-- <div>{{ item.paramCode.commentLevel ? item.paramCode.commentLevel : 0 }}&nbsp;&nbsp;</div>
                                <div class="star-box"
                                    :style="{ backgroundImage: 'linear-gradient(90deg,#FFCC00 ' + item.paramCode.commentLevel * 20 + '%,white 0)' }">
                                    <b v-for="s in 5" class="icon-star" :key="s"></b>
                                </div>
                                <div>&nbsp;({{ item.paramCode.comments ? item.paramCode.comments : 0 }})</div> -->
                                <!-- <div @click="collectClick(item)">
                                <img src="http://customed-center.oss-us-west-1.aliyuncs.com/web/quoteManage/20230730/hui_20230730MRRw7Q.png"
                                    :style="item.collect ? 'display: none;' : 'display: block;'" />
                                <img src="http://customed-center.oss-us-west-1.aliyuncs.com/web/quoteManage/20230730/jianbian_20230730GDnPCB.png"
                                    :style="item.collect ? 'display: block;' : 'display: none;'" />
                            </div> -->
                            </div>
                            <button primary :style="modal.cardBtnStyle" :title="o.button.alt" v-if="o.button?.value"
                                @click="quoteButton(o.button, item)">
                                <EditDiv class="btnStyle" tagName="label" v-model:content="o.button.value" />
                                <b :class="o.button.icon" v-show="o.button.icon"></b>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="container" v-if="loading">{{ $store.getters.lang.semiCustom.loadMore }}...</div>

                <button primary :style="modal.cardBtn1Style" @click="getPage" :title="o.button1.alt"
                    v-if="modal.viewMore && o.button1 && page < totalPages && templatesList.length < 50">
                    <EditDiv v-show="o.button1.value" tagName="label" v-model:content="o.button1.value"
                        @click="setModalType(o.button1, modal.outer, 'button')" />
                    <b :class="o.button1.icon" v-show="o.button1.icon"></b>
                </button>
                <button primary :style="modal.cardBtn1Style" :title="o.button2.alt"
                    v-if="modal.viewMore && o.button2 && page < totalPages && templatesList.length >= 50">
                    <EditDiv v-show="o.button2.value" tagName="label" v-model:content="o.button2.value"
                        @click="setModalType(o.button2, modal.outer, 'button', o.button2)" />
                    <b :class="o.button2.icon" v-show="o.button2.icon"></b>
                </button>

            </div>
        </template>
    </div>
</template>

<script>
import { getTemplates, getAppParamType } from "@/api/classification";
export default {
    name: "modalTemplate",
    props: {
        data: {
            type: Object,
            default: {},
        }
    },
    data() {
        return {
            scrollIndex: 0,
            isTemplateTypeName:0,
            isActiveSwiper: true,
            loading: false,
            keyword: "",
            templatesActive: true,
            templatesActive1: false,
            templatesActive2: false,
            isActive: false,
            values: "",
            parentSortList: [],
            templatesList: [],
            modal: {
                style: {},
                type: {},
                ...this.data
            },
            isbtn: 0,
            btnList: [
                {
                    id: 0,
                    Icon: "icon-a-6huise",
                },
                {
                    id: 1,
                    Icon: "icon-a-4huise",
                },
                {
                    id: 2,
                    Icon: "icon-a-3huise",
                },
            ],
            page: 1,
            pageSize: 12,
            totalPages: 0,
            getPageing: false,
            maxLength: 30
        };
    },
    computed: {
        getlength(){
            if(this.parentSortList.length && this.parentTag?.childList?.length){
                return this.parentTag?.childList?.length > 8
            }else{
                return this.parentSortList.length > 8
            }
        },
        isMobile() {
            return this.$store.state.device === "mb";
        },
        lang() {
            return this.$store.getters.lang?.template || {};
        },
        parentTag() {
            return this.parentSortList.find(i => i.id == this.modal.parentIndex) || { id: 0, templateTypeName: this.lang.all };
        },
        cardBoxDom() {
			return document.querySelector(`#${this.modal.id} .templates_c${this.modal.scroll ? '' : '>div'}`);
		},
    },
    watch: {
        modal: {
            handler(val) {
                if (process.env.isManage) this.$emit("update:data", val);
            },
            immediate: true,
            deep: true
        },
        scrollIndex(val) {
			if (val > this.modal.column - 1) this.scrollIndex = this.modal.column - 1;
			else if (val < 0) this.scrollIndex = 0;
		},
    },
    created() {
        if (this.$route.query?.parentIndex) this.modal.parentIndex = this.$route.query?.parentIndex;
        if (this.$route.query?.childIndex) this.modal.childIndex = this.$route.query?.childIndex;

        if (process.browser && !this.modal.viewMore) document.addEventListener("scroll", this.getPage)

        this.getTemplatesList();
    },
    async mounted() {
        await this.getParentSortList();

        //只显示子级的页面，else显示父级
        if (this.modal.childTemplate && this.parentTag.id) {
            this.myswiper2 = new Swiper(this.$refs.swiper, {
                slidesPerView: 4,
                spaceBetween: 10,
                watchSlidesVisibility: true,
                grabCursor: true,
                pagination: {
                    el: ".swiper-pagination",
                    clickable: true
                },
                navigation: {
                    nextEl: '.swiper-button-next',
                    prevEl: '.swiper-button-prev',
                },
                direction: 'horizontal',
                breakpoints: {
                    320: {
                        slidesPerView: 3,
                        spaceBetween: 5,
                        grid: {
                            rows: this.modal.isNew ? 2 : 1,
                            fill: "row",
                        },
                    },
                    750: {
                        slidesPerView: this.modal.isNew ? 3 : 5,
                        spaceBetween: 5,
                        grid: {
                            rows: this.modal.isNew ? 2 : 1,
                            fill: "row",
                        },
                    },
                    1000: {
                        slidesPerView: 6,
                        spaceBetween: 10,
                    },
                },
            });

        } else {
            this.myswiper1 = new Swiper(this.$refs.swiper1, {
                slidesPerView: "7",
                spaceBetween: 10,
                watchSlidesVisibility: true,
                grabCursor: true,
                navigation: {
                    nextEl: '.swiper-button-next',
                    prevEl: '.swiper-button-prev',
                },
                pagination: {
                    el: ".swiper-pagination",
                    clickable: true
                },
                breakpoints: {
                    320: {
                        slidesPerView: 3,
                        spaceBetween: 5,
                        grid: {
                            rows: this.modal.isNew ? 2 : 1,
                            fill: "row",
                        },
                    },
                    750: {
                        slidesPerView: this.modal.isNew ? 3 : 5,
                        spaceBetween: 5,
                        grid: {
                            rows: this.modal.isNew ? 2 : 1,
                            fill: "row",
                        },
                    },
                    1000: {
                        slidesPerView: 7,
                        spaceBetween: 10,
                    },
                },
            });
        }
    },
    methods: {
        scrollBox(index) {
            console.log(index,"index");
			// 点击滚动（父盒子总宽度 / 总屏数 * 滚几屏）
			this.scrollIndex += index;
			if (this.cardBoxDom) this.cardBoxDom.scrollLeft = this.cardBoxDom.scrollWidth / this.modal.column * this.scrollIndex;
		},

        truncateAlias(alias) {
            if (alias.length > this.maxLength) {
                return alias.substring(0, this.maxLength) + "...";
            } else {
                return alias;
            }
        },

        setModalType(target, targetArray, clickType, event, other) {
            this.$setModal(this, target, targetArray, clickType, event, other)
        },
        //跳报价
        quoteButton(target, item) {
            target.method = {
                ...target.method,
                queryId: item.id
            }
            this.setModalType(target, this.modal.outer, 'button', target)
        },
        // collectClick(item) {
        //     item.collect = !item.collect;
        // },
        templatesTitle() {
            this.templatesList.forEach((item) => {
                item.paramCode.commentLevel = ""
            })
            this.modal.childIndex = 0;
            this.modal.parentIndex = 0;
            this.isActiveSwiper = true;
            this.getTemplatesList();
        },
        //点击子分类
        subclassificationClick(i, index) {
            this.modal.childIndex = i.id;
            this.page = 1;
            this.getTemplatesList();
        },
        //点击父分类
        templatesClick(item) {
            console.log(this.modal.isNew,"this.modal.isNew");
            if (this.modal.isNew) {
                // this.modal.parentIndex = item.id;
                this.isTemplateTypeName = item.id;
                this.getTemplatesList();
            } else {

                this.isActiveSwiper = false;
                this.isActive = false;
                this.page = 1;
                this.modal.childIndex = 0;
                this.modal.parentIndex = item.id;
                this.getTemplatesList();

                //子级Swiper
                const totalSlidesInCurrentPage = this.parentTag.childList.length;
                const slidesPerPage = Math.min(4, totalSlidesInCurrentPage);

                // 销毁旧的 Swiper 实例
                if (this.mySwiper2) {
                    this.mySwiper2.destroy();
                }
                this.mySwiper2 = new Swiper(this.$refs.swiper, {
                    slidesPerView: slidesPerPage,
                    spaceBetween: 10,
                    watchSlidesVisibility: true,
                    grabCursor: true,
                    pagination: {
                        el: ".swiper-pagination",
                        clickable: true
                    },
                    direction: 'horizontal',
                    breakpoints: {
                        320: {
                            slidesPerView: 3,
                            spaceBetween: 5,
                            grid: {
                                rows: this.modal.isNew ? 2 : 1,
                                fill: "row",
                            },
                        },
                        750: {
                            slidesPerView: this.modal.isNew ? 3 : 5,
                            spaceBetween: 5,
                            grid: {
                                rows: this.modal.isNew ? 2 : 1,
                                fill: "row",
                            },
                        },
                        1000: {
                            slidesPerView: 6,
                            spaceBetween: 10,
                        },
                    },
                });

                // 根据幻灯片数量动态控制滑动功能
                this.mySwiper2.allowSlideNext = totalSlidesInCurrentPage > 4;
                this.mySwiper2.allowSlidePrev = totalSlidesInCurrentPage > 4;
            }

        },
        selectNameClick(li) {
            this.modal.parentIndex = li.id;
            this.isActive = !this.isActive;
        },
        //展示下拉
        selectClick() {
            this.isActive = !this.isActive
        },
        //模版样式切换
        typeClick(item, index) {
            this.isbtn = index,
                this.isbtn = item.id;
            if (this.isbtn === 0) {
                this.templatesActive = true;
                this.templatesActive1 = false;
                this.templatesActive2 = false;
            } else if (this.isbtn === 1) {
                this.templatesActive = false;
                this.templatesActive1 = true;
                this.templatesActive2 = false;
            } else if (this.isbtn === 2) {
                this.templatesActive = false;
                this.templatesActive1 = false;
                this.templatesActive2 = true;
            }
        },

        //获取父子分类
        async getParentSortList() {
            const res = await getAppParamType({
                paramType: "NEONSAMPLE"
            });
            this.parentSortList = res.data.map(i => {
                i.childList.forEach(child => child.bandParamIdListLength = child.bandParamIdList.split(',').length);
                return i;
            });
            // console.log(this.parentSortList, " this.parentSortList");
        },
        // getParentSortList() {
        //         getAppParamType({
        //             paramType: "NEONSAMPLE"
        //         }).then((res) => {
        //             this.parentSortList = res.data.map(i => {
        //                 i.childList.forEach(child => child.bandParamIdListLength = child.bandParamIdList.split(',').length)
        //                 return i;
        //             });
        //         })
        //     })
        // },
        //获取模版
        getTemplatesList() {
            this.loading = true
            let params = {
                cateId: 67,
                keyword: this.keyword,
                paramTypeId: this.modal.childIndex || this.modal.parentIndex || this.isTemplateTypeName || null,
                paramType: "NEONSAMPLE",
                page: this.page,
                pageSize: this.pageSize,
            }
            getTemplates(params).then((res) => {
                this.totalPages = res.data.totalPages;
                let content = res.data.content;
                content.forEach(item => {
                    if (item.imageJson) {
                        item.imageJson = JSON.parse(item.imageJson);
                        let findImg = item.imageJson.find(item => item.name === 'templates') || item.imageJson[0];
                        item.showImgObj = findImg && findImg.url;
                    }
                    item.collect = false;
                    if (item.paramCode == "") {
                        return
                    } else {
                        item.paramCode = JSON.parse(item.paramCode);
                    }
                })

                if (this.page == 1) {
                    this.templatesList = content;
                } else {
                    this.templatesList.push(...content);
                }
                this.$nextTick(() => this.getPageing = false)
                this.loading = false
            })
        },

        //触底翻页
        getPage() {
            if (this.page < this.totalPages) {
                if (this.modal.viewMore) {
                    this.page++;
                    this.getTemplatesList();
                }
                else if (!this.getPageing && (document.body.clientHeight - window.scrollY - window.innerHeight) < document.querySelector('footer').offsetHeight + 50) {
                    this.getPageing = true;
                    this.page++;
                    this.getTemplatesList();
                }
            }
        }
    },

    beforeDestroy() {
        document.removeEventListener("scroll", this.getPage);
    },
};
</script>

<style lang="scss" scoped>
.swiper {
    width: 100%;
    height: 100%;
    // margin-top: 15px;
    position: relative;
    top: 0;
}

.swiper-container {
      width: 100%;
      height: auto;
	  padding:20px 0;
      margin-left: auto;
      margin-right: auto;
	  overflow: hidden;
    }
    .swiper-wrapper{
        display: flex;
        align-items: center;
    }

.swiper-slide {
    color: #333;
    display: flex;
    height: 4.4vw;
    font-size: calc(1em + 2px);
    background: #fff;
    text-align: center;
    align-items: center;
    justify-content: center;
}

.swiper-pagination {
    bottom: 0 !important;
    ::v-deep .swiper-pagination-bullet {
        background: linear-gradient(90deg, var(--color-bright) -60%, var(--btn-primary) 140%);
    }
}
.swiper-button-next,
.swiper-button-prev{
    top: 52%;
    &::after{
        font-size: 20px;
        font-weight: bold;
    }
}


.btnStyle {
    // width: 200px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.templates_fenlei {
    
    position: sticky;
    z-index: 5;
    top: 0;

    .title_Templates {
        margin-top: 0 !important;
    }

    .templates_sorting {
        margin-top: 10px;
    }
}


.container {
    margin-top: 20px;
}

.mobanPCMb {
    line-height: 1.8em;
    margin-top: 1.5vw;

    .imgStyle {
        object-fit: contain;
        border-radius: 7px;
    }

    .moban_neirong {
        margin-top: 0.458vw;

        div:first-child {
            font-weight: 400;
            font-size: 1.25em;
        }

        .price-box {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;

            .price {
                font-size: 1.25em;
            }

            del {
                opacity: 0.7;
                padding: 0 0.3em;
            }
        }

        .moban_neirong_div3 {
            display: flex;
            justify-content: center;
            align-items: center;

            div:nth-of-type(1) {
                font-size: 1.06em;
                height: auto;
            }

            div:nth-of-type(3) {
                font-size: 1em;
                opacity: 0.8;
            }

            div:nth-of-type(4) {
                margin-left: 1.3021vw;
            }

            .star-box {
                background-clip: text;

                b {
                    font-size: 1.2em;
                    margin-left: 0.1em;
                    color: transparent;
                    -webkit-text-stroke: 1px #FFCC00;
                }
            }
        }

        button {
            padding: 0;
            font-size: 1em;
            height: 2.625em;
            margin-top: .5208vw;
            width: 80%;
            // min-width: 13.0208vw;

        }

        .btn2 {
            color: #333;
            background: #fff;
        }

        img {
            width: 1.5625vw;
            height: 1.5625vw;
            object-fit: cover;
        }
    }

    >div:hover .imgStyle {
        filter: hue-rotate(0deg);
        animation: neon-img 1s linear infinite alternate;
    }

    >div:not(:hover) button {
        background: white;
        color: $text-primary;
        border: 1px solid #D2D2D2;
    }

	>div {
		position: relative;

		.corner-label {
			grid-gap: 0.25em;
			align-items: center;
			padding: 0.3em 1em;
			font-size: calc(1rem - 2px);
			background-size: 100% 100%;
			position: absolute;
			top: 0.75em;

			img {
				width: auto;
				height: 1.75em;
			}

			&[onlyImg] {
				padding: 0;

				img {
					height: 2em;
				}
			}
		}

		&[cornerLabel~='left'] .corner-label {
			left: 1em;
		}

		&[cornerLabel~='right'] .corner-label {
			right: 1em;
		}

		&[cornerLabel~='edge'] {

			.corner-label {
				top: 0;
			}

			&[cornerLabel~='left'] .corner-label {
				left: 0;
			}

			&[cornerLabel~='right'] .corner-label {
				right: 0;
			}
		}

		&[cornerLabel~='out'] {
			overflow: unset;

			.corner-label {
				top: -0.5em;

				&[onlyImg] img {
					width: 4.8em;
					height: auto;
				}
			}

			&[cornerLabel~='left'] .corner-label {
				left: -0.5em;
			}

			&[cornerLabel~='right'] .corner-label {
				right: -0.5em;
			}
		}
	}
}

.moban {
    display: grid;
    font-size: calc(1em - 2px);
    grid-template-columns: repeat(6, minmax(0, 1fr));
    grid-gap: 2em 1%;

    .imgStyle {
        object-fit: contain;
        border-radius: 7px;
    }

    .moban_neirong {
        margin-top: .375vw;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        .aliasStyle {
            width: 13.0208vw;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        button {
            width: 100%;
            font-size: 1.2em;
            border: 1px solid #D2D2D2;
            border-radius: .2604vw;
            padding: .625vw 0;
            margin-top: 0.3375vw;
        }
    }
}

.moban1 {
    display: grid;
    grid-template-columns: repeat(4, minmax(0, 1fr));
    grid-gap: 3em 2%;
}

.moban2 {
    display: grid;
    line-height: 2.235em;
    font-size: calc(1em + 2px);
    grid-template-columns: repeat(3, minmax(0, 1fr));
    grid-gap: 4em 3%;
	margin-top: 1em;

    .imgStyle {
        object-fit: contain;
        border-radius: 7px;
    }

    .moban_neirong {

        button {
            width: 80%;
            height: 2.7604vw;
            font-size: 1.2em;
            padding: 0;
            margin-top: .7292vw;
        }
    }
}

.buJu1 {
    color: #969cb5;
}

.buJu2 {
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    filter: saturate(70%) brightness(130%);
    background-image: linear-gradient(90deg, var(--color-primary), var(--btn-primary) 120%);
}

.modal-box {
    text-align: center;

    .title {
        @media screen and (max-width: $mb-width){
            .sub-title{
                margin-bottom: 0;
            }
        }
        

        // .title_Templates {
        //     font-size: 0.93em;
        //     display: flex;
        //     justify-content: space-between;
        //     align-items: center;
        //     margin: 2.0313vw 0 10px;

        //     &:hover {
        //         cursor: pointer;
        //     }

        //     .btn {
        //         display: block;

        //         b {
        //             font-size: 1.44em;
        //         }
        //     }

        //     button {
        //         margin-right: 1.0417vw;
        //     }
        // }
    }

    .templates1 {
        margin-top: .625vw;

        .title_Templates {
            font-size: 0.93em;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 2.0313vw 0 10px;

            &:hover {
                cursor: pointer;
            }

            .btn {
                display: block;

                b {
                    font-size: 1.44em;
                }
            }

            button {
                margin-right: 1.0417vw;
            }
        }

        .className {
            display: flex;
            color: #333333;
            background: #fff;
            font-size: 0.875em;
            border-radius: 3em;
            padding: .3563vw .5813vw;
            border: 1px solid #D0D0D0;

            &:hover,
            &.select {
                cursor: pointer;
                color: #fff !important;
                background: linear-gradient(90deg, var(--color-bright) -60%, var(--btn-primary) 140%);
            }
        }

        .templates_div {
            // width: 75.5208vw;
            //  margin: 0 auto;
            display: flex;
            flex-wrap: wrap;

            cursor: pointer;
            grid-gap: 0.5208vw;
            font-size: 0.875em;
            align-items: center;

            div:first-child {
                font-size: 1em;
                font-weight: 400;
                // color: #333333;
                padding-right: .5208vw;
                // border-right: 1px solid #E3E3E3;
                line-height: 1.3542vw;
            }
        }

        .templates_isActive {
            display: flex;
            grid-gap: 0.5208vw;
            font-size: 0.875em;
            align-items: flex-start;
            position: relative;

            .select3 {
                font-size: 1em;
                font-weight: 400;
                // color: #333333;
                line-height: 1.6667vw;

                &:hover {
                    cursor: pointer;
                }

                .select2 {
                    border-bottom: 0;
                    border-radius: 5px;
                }

                div:nth-of-type(1) {
                    height: 1.8229vw;
                    justify-content: space-between;
                    align-items: center;

                    b {
                        transform: scale(0.6);
                    }
                }

                ul {
                    z-index: 2;
                    background: #fff;
                    padding: .5125vw .3646vw;
                    margin-left: 0;
                    position: relative;
                    bottom: 2px;
                    border-radius: 0 0 5px 5px;
                    box-shadow: 0 5px 10px #f1f1f1;

                    li {
                        list-style: none;
                        cursor: pointer;
                        text-align: left;
                        line-height: 1.5542vw;
                    }
                }
            }

            .className_div {
                display: flex;
                flex-wrap: wrap;
                grid-gap: 0.5208vw;
                padding-left: .5208vw;
                border-left: 1px solid #ccc;
            }

            .className_div2 {
                border-left: 0;
                display: flex;
                flex-wrap: wrap;
                .className{
                    margin-right: 10px;
                }
            }
        }

        >button b {
            transform: scale(0.5);
        }
    }
}


// 新模版样式
.modal-box-new{
   ::v-deep  .swiper-pagination{
        display: none !important;
    }
    .title_Templates{
        display: none !important;
    }
    hr{
        display: none !important;
    }
    .templates_sorting{
        display: flex;
        justify-content: space-between;
        align-items: center;
        .icon-left{
            color:#fff;
        }
        .templates_c{
            max-width: 1600px;
            overflow: hidden;
            margin: 0 auto;

            .templates_div{
				grid-gap: 1.25em;
                flex-wrap: nowrap;
				// justify-content: center;
                align-content: center;

                .className {
                    padding: 0.5em 2.1em;
                    margin-right: 10px;
                    background: transparent;
                    color: #fff;
                    white-space: nowrap;
                    line-height: 1.6em;
                    font-size: 1.125rem;

                    &:hover,
                    &.select {
                        cursor: pointer;
                        color: var(--color-dark) !important;
                        background: #fff;
                    }
                }

                .classNameActive{
                    background: #fff;
                    color: var(--color-dark);
                    font-size: 1.25rem;
                    border-radius: 3em;
                    padding: 0 2em;
                    border: 1px solid #D0D0D0;
                    height: 3em;
                    line-height: 2.8em;
                    margin-right: 10px;
                    white-space: nowrap;

                    // &:hover {
                    //     cursor: pointer;
                    //     color: var(--color-dark) !important;
                    // }
                }

                // .className {
                //     padding: 0.3em 1em;
				// 	min-width: 7.778rem;
				// 	justify-content: center;
                //     background: transparent !important;
                //     color:#fff !important;
                //     white-space: nowrap;
                //     font-size: 1.125rem;
                //     line-height: 1.18em;

                //     &:hover,
                //     &.select {
                //         cursor: pointer;
                //         color: var(--color-dark) !important;
                //         background: #fff;
                //     }
                // }
                // .classNameActive{
				// 	height: 3em;
				// 	display: flex;
				// 	min-width: 7.778rem;
				// 	align-items: center;
                //     white-space: nowrap;
                //     padding: 0.3em 0.7em;
				// 	justify-content: center;
                //     background: #fff !important;
                //     color: var(--color-dark) !important;
                //     font-size: 1.25rem !important;
                // }

                div:first-child{
                    display: none;
                }
            }
        }
    }

    .select3{
        display: none;
    }
    .className_div{
        border-left: 0 !important;
    }
    .templates1 {
        margin-top: .625vw;

        .title_Templates {
            font-size: 0.93em;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 2.0313vw 0 10px;

            &:hover {
                cursor: pointer;
            }

            .btn {
                display: block;

                b {
                    font-size: 1.44em;
                }
            }

            button {
                margin-right: 1.0417vw;
            }
        }

        .className {
            padding: 0.6em 2.1em;
            margin-right: 10px;
            background: transparent;
            color: #fff;
            white-space: nowrap;
            line-height: 1.6em;
            font-size: 1.125rem;

            &:hover,
            &.select {
                cursor: pointer;
                color: var(--color-dark) !important;
                background: #fff;
            }
        }

        .classNameActive{
            background: #fff;
            color: var(--color-dark);
            font-size: 1.25rem;
            border-radius: 3em;
            padding: 0px 2em;
            border: 1px solid #D0D0D0;
            height: 3em;
            line-height: 2.8em;
            margin-right: 10px;
            white-space: nowrap;

            // &:hover {
            //     cursor: pointer;
            //     color: var(--color-dark) !important;
            // }
        }

        .templates_div {
            // width: 75.5208vw;
            //  margin: 0 auto;
            display: flex;
            flex-wrap: nowrap !important;
            cursor: pointer;
            grid-gap: 0.5208vw;
            font-size: 0.875em;
            align-items: center;

            div:first-child {
                font-size: 1em;
                font-weight: 400;
                // color: #333333;
                padding-right: .5208vw;
                // border-right: 1px solid #E3E3E3;
                line-height: 1.3542vw;
            }
        }

        .templates_isActive {
            display: flex;
            grid-gap: 0.5208vw;
            font-size: 0.875em;
            align-items: flex-start;
            position: relative;


            .className_div {
                display: flex;
                flex-wrap: nowrap;
                grid-gap: 0.5208vw;
                padding-left: .5208vw;
                border-left: 1px solid #ccc;
                align-items: center;
            }

            .className_div2 {
                border-left: 0;
                display: flex;
                flex-wrap: nowrap;
                align-items: center;
            }
        }

        .mySwiper2{
            width: 80%;
        }

        >button b {
            transform: scale(0.5);
        }
    }
    .templates_fenlei {
        position: sticky;
        top: 0;
    }
}


@media screen and (max-width: $mb-width) {
    //新模版样式
    .modal-box-new{

        .swiper-button-next{
            top: 62%;
            right: -4px;
            z-index: 15;
            color: #fff;

            &::after{
                font-size: calc(1em + 2px);
            }
        }
        .swiper-button-prev{
            top: 62%;
            left: -4px;
            color: #fff;
            z-index: 15;

            &::after{
                font-size: calc(1em + 2px);
            }
        }

        ::v-deep  .swiper-pagination{
            display: none !important;
        }

        .templates_div_Mb{
            .mySwiper1{
                width: 83%;
            }

        }
        .className2 {
            background: transparent !important;
            color: #fff;
            border-radius: 3em;
        }

        .className2Active{
            background: #fff;
            color: var(--color-dark);
            font-size: 1em;
            border-radius: 3em;
            padding: 1.5vw 2.3vw;
            border: 1px solid #d0d0d0;
            height: 7.5vw;
            line-height: 4.5vw;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            display: block;
        }
        .templates1{
                .classNameActive{
                background: #fff !important;
                color: var(--color-dark);
                font-size: 3.2vw;
                border-radius: 3em;
                padding: 0;
                border: 1px solid #D0D0D0;
                height: 7.5vw;
                line-height: 7.5vw;
            }
        }

}

    .templates_sorting_h {
        height: 16vw;
    }

    .templates_sorting_h2 {
        height: 100%;
    }

    .swiperClass {
        height: 29vw !important;
        // padding-bottom: 1.5em; 
    }

    .swiperClass2 {
        height: 100% !important;
    }

    .className2 {
        box-sizing: border-box;
        font-size: 3.2vw;
        padding: 1.5vw 2.3333vw;
        border: 1px solid #D0D0D0;
        border-radius: 16px;
        margin-left: 0;
        display: block;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        height: 7.5vw;

        &:hover {
            cursor: pointer;
            color: #fff;
            background: linear-gradient(90deg, var(--color-bright) -60%, var(--btn-primary) 140%);
        }
    }

    .templates_fenlei {
        height: 25vw;

        .templates_sorting {
            margin-top: 0;
            //  height: 29vw;
        }
    }

    .btnStyle {
        width: 150px;
    }

    .mobanPCMb {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        grid-column-gap: 2.4vw;
        grid-row-gap: 3.8vw;
        // margin-top: 9.9333vw;
        line-height: 4.8vw;
        margin-top: 4vw;

        .imgStyle {
            object-fit: contain;
            border-radius: 7px;
        }

        .moban_neirong {
            margin-top: .9333vw;
            line-height: 4.9667vw;

            div:first-child {
                font-size: 3.4667vw;
            }

            .price-box {
                font-size: 3.2vw;
                flex-wrap: wrap;

                >span {
                    display: block;
                }
            }

            button {
                width: 42vw;
                height: 8vw;
                font-size: 3.2vw;
                border: 1px solid #D2D2D2;
                border-radius: .6667vw;
                margin-top: 2.6667vw;
                padding: 0;
            }

            .moban_neirong_div3 {
                display: flex;
                justify-content: center;
                align-items: center;

                div:nth-of-type(1) {
                    font-size: 3.2vw;
                }

                div:nth-of-type(3) {
                    font-size: 3.2vw;
                    opacity: 0.8;
                }

                div:nth-of-type(4) {
                    margin-left: 3.0667vw;
                }
            }

            .aliasStyle {
                width: 45.0208vw;
            }
        }
    }

    .modal-box {
        .title .title_Templates {
            font-size: 3.2vw;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-bottom: 1.3333vw;
            border-bottom: 0;
            margin: 4.4vw 0 1.3333vw;

            &:hover {
                cursor: pointer;
            }

            .btn {
                display: none;
            }

            button {
                margin-right: 20px;
            }
        }

        .templates1 {
            // margin-top: 12px;

            .className {
                font-size: 3.2vw;
                padding: 1.5vw 2.3333vw;
                border: 1px solid #D0D0D0;
                border-radius: 16px;
                margin-left: 0;
                display: block;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;

                // line-height: 0;

                &:hover {
                    cursor: pointer;
                    color: #fff;
                    background: linear-gradient(90deg, var(--color-bright) -60%, var(--btn-primary) 140%);
                }
            }

            .templates_isActive {
                display: block;
                position: relative;
                margin-top: 10px;

                .select2 {
                    border: 1px solid #E3E3E3;
                    border-radius: 5px;
                }

                .select3 {
                    height: 8.8vw;
                    background: #F4F3F3;
                    border-radius: 5px;
                    font-size: 16px;
                    font-weight: 400;
                    color: #333333;
                    padding-right: 0;
                    line-height: 8.8vw;

                    div:nth-of-type(1) {
                        height: auto !important;
                        padding: 0;
                        height: 8.8229vw;

                        div {
                            font-size: 3.2vw;
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                            padding: 0;

                            span {
                                margin-left: 4vw;
                            }

                            b {
                                margin-left: 1.3333vw;
                                margin-right: 4.2667vw;
                                transform: scale(0.6);
                            }
                        }
                    }

                    ul {
                        font-size: 3.2vw;
                        position: relative;
                        z-index: 2;
                        border-top: 1px solid #ccc;
                        background: #fff;
                        padding: 2.3333vw 4vw;
                        margin-left: 0;

                        li {
                            list-style: none;
                            cursor: pointer;
                            text-align: left;
                            line-height: 6.4667vw;
                        }
                    }
                }

                .className_div {
                    display: flex;
                    flex-wrap: wrap;
                    grid-column-gap: 3.7333vw;
                    grid-row-gap: 2.9333vw;
                    border-left: 0;
                    margin-top: 3.4667vw;

                    .className {
                        font-size: 3.2vw;
                    }
                }

                .className_div2 {
                    .className {
                        font-size: 3.2vw;
                        margin-right: 10px;
                        margin-bottom: 10px;
                    }
                }
            }
        }
    }
}

.customneonsigns {
    .templates1 {
        .className {
            border: none;
            background: #EBEBEB;

            &:hover,
            &.select {
                background: var(--btn-primary);
            }
        }
    }
}
</style>
