<template>
	<div class="newDesign-style6" :class="stepData.styleName">
	    <slot name="stepText"></slot>
		<div class="stepContent">
			<div class="step-item" v-for="(item, index) in stepData.productParamList" :key="item.id" :class="{ active: selectIndex == index || item.inputNum > 0 }" @click="selectStep(item, index)">
				<div class="imgBox">
					<img :src="item.sizeImg" :alt="item.imgAltTitle" :title="item.imgAltTitle" />
				</div>
				<span class="introText">{{ item.colorAlias }}</span>
				<div class="inputMain">
					<button @click.stop="subNum(item, index)" class="subBtn"><v-icon>mdi-minus</v-icon></button>
					<div class="inputBox">
						<input class="priceInputStyle6" @blur="selectIndex = -1" :ref="`myIunputStyle6${index}`" :disabled="!item.stock" type="text" v-model="item.inputNum" @keyup="formatNum(item)" @change="updatePrice" placeholder="0" />
					</div>
					<button @click.stop="addNum(item, index)" class="addBtn"><v-icon>mdi-plus</v-icon></button>
				</div>

				<half-design-check-icon class="absolute-top-right check-icon"></half-design-check-icon>
			</div>
		</div>
		<div class="errorTip" ref="errorTip">
			<v-alert dense outlined type="error">{{ langSemiCustom.miniQty }} {{ productInfo.lowestPurchaseQuantity || 1 }} </v-alert>
		</div>
	</div>
</template>

<script>
export default {
	inject: ["getProductInfo"],
	name: "Style6",
	props: {
		stepData: {
			type: Object,
			default: () => ({}),
		},
	},
	data() {
		return {
			lastIndex: -2,
			lastIndex2: -2,
			selectIndex: -1,
			selectItem: null,
		};
	},
	computed: {
		productInfo() {
			return this.getProductInfo();
		},
		langSemiCustom() {
			return this.$store.getters.lang?.semiCustom;
		},
	},
	methods: {
		subNum(item, index) {
			if (!item.inputNum) item.inputNum = 0;
			if (item.inputNum <= 0) return;
			item.inputNum = +item.inputNum - 1;
			this.selectIndex = index;
			this.lastIndex = index;
			this.selectItem = item;
			this.updatePrice();
			this.$forceUpdate();
		},
		addNum(item, index) {
			if (!item.inputNum) item.inputNum = 0;
			if (item.inputNum >= item.stock) return;
			item.inputNum = +item.inputNum + 1;
			this.selectIndex = index;
			this.lastIndex = index;
			this.selectItem = item;
			this.updatePrice();
			this.$forceUpdate();
		},
		formatNum(item) {
			item.inputNum = (item.inputNum + "").replace(/[^\d]/g, "");
			if (item.stock && item.stock > 0 && item.inputNum > item.stock) {
				item.inputNum = String(item.stock);
			}
			if (item.stock <= 0) {
				item.inputNum = "";
			}
		},
		async updatePrice() {
			this.$forceUpdate();
			this.$nextTick(() => {
				let priceInputs = document.getElementsByClassName("priceInputStyle6");
				let sum = Array.from(priceInputs).reduce((acc, input) => acc + parseInt(input.value || 0), 0);
                let errDom = this.$refs.errorTip;
				if (sum < this.productInfo.lowestPurchaseQuantity) {
					errDom.style.display = "block";
				} else {
					errDom.style.display = "none";
				}
				if (this.stepData.productParamList[this.lastIndex].inputNum > 0 && this.lastIndex == this.lastIndex2) {
					//为 true 判断小重量交期
					this.$emit("updatePrice");
					this.$emit("checkParam")
					return;
				}
				this.lastIndex2 = this.selectIndex;
				this.selectIndex = -1;
				//选择最后输入价格的
				let data = null;
				if (this.selectItem.inputNum && this.selectItem.inputNum > 0) {
					data = this.selectItem;
				} else {
					let index = this.stepData.productParamList.findIndex((item) => {
						return item.inputNum && item.inputNum > 0;
					});
					if (index >= 0) data = this.stepData.productParamList[index];
					this.lastIndex2 = -2;
				}
				this.$emit("selectStep", {
					type: this.stepData.attributeFlag,
					data: data || this.stepData.productParamList[0],
					id: this.stepData.id,
					colorIndex: this.lastIndex,
					firstSelect: false,
					cancel: !(sum > this.productInfo.lowestPurchaseQuantity),
				});
				// this.$emit("updatePrice");
			});
		},
		selectStep(item, index) {
			if (item.stock <= 0) {
				return;
			}
			this.lastIndex = index;
			this.selectIndex = index;
			this.selectItem = item;
			this.$refs[`myIunputStyle6${index}`][0].focus();
			this.updatePrice();
		},
		setInputNum(num) {
			this.stepData.productParamList[0].inputNum = num;
			this.selectItem = this.stepData.productParamList[0];
			this.$forceUpdate();
			this.lastIndex = 0;
			this.selectIndex = 0;
			this.$emit("selectStep", {
				type: this.stepData.attributeFlag,
				data: this.stepData.productParamList[0],
				id: this.stepData.id,
				colorIndex: 0,
				firstSelect: true,
			});
		},
	},
	created() {},
	mounted() {
		//默认渲染的是color 步骤
		this.$Bus.$on("selectDefaultSizeStep", this.setInputNum);
	},
	beforeDestroy() {
		this.$Bus.$off("selectDefaultSizeStep", this.setInputNum);
	},
};
</script>

<style lang="scss" scoped>
@import "~assets/css/half.scss";
.newDesign-style6 {
	width: 100%;
	height: 100%;
	overflow: hidden;
	position: relative;
	.stepContent {
		width: 100%;
		height: 100%;
		box-sizing: border-box;
		display: grid;
		grid-template-columns: repeat(5, 1fr);
		gap: 8px;
		.step-item {
			@include step-default;
			position: relative;
			display: flex;
			align-items: center;
			justify-content: center;
			flex-direction: column;
			row-gap: 8px;
			background-color: #f7f7f7;

			.check-icon {
				display: none;
			}

			&.active {
				background-color: #fff;
				.check-icon {
					display: flex;
				}
				.introText {
					font-weight: 700;
				}
			}

			.imgBox {
				@include flex-center;
				max-height: 65px;
			}

			.introText {
				font-weight: 400;
				font-size: 13px;
				text-align: center;
			}

			.inputMain {
				display: flex;
				align-items: center;
				justify-content: space-between;
				background: #f0f0f0;
				border-radius: 4px;
				border: 1px solid transparent;
				&:focus-within {
					border: 1px solid $color-primary;
				}

				.addBtn,
				.subBtn {
					padding: 2px 4px;
					cursor: pointer;
					.v-icon {
						font-size: 20px;
					}
				}
				.subBtn:active,
				.addBtn:active {
					.v-icon {
						color: $color-primary;
					}
				}
				.inputBox {
					input {
						line-height: 20px;
						max-width: 100%;
						min-width: 0;
						width: 100%;
						text-align: center;
						font-weight: 400;
						font-size: 16px;
						color: #333333;

						&:focus {
							color: $color-primary;
							&::placeholder {
								opacity: 0;
							}
						}
					}
				}
			}
		}
	}
}

@include respond-to(mb) {
	.newDesign-style6 {
		.stepContent {
			grid-template-columns: repeat(4, 1fr);
			gap: 4px;
			.step-item {
				padding: 6px;
				row-gap: 2px;
				&.active {
					padding: 5px;
				}
				@media (any-hover: hover) {
					&:hover {
						padding: 5px;
					}
				}

				.introText {
					font-size: 12px;
				}
				.addBtn,
				.subBtn {
					.v-icon {
						font-size: 16px !important;
					}
				}
				.inputMain {
					.inputBox {
						input {
							font-size: 13px;
						}
					}
				}
			}
		}
	}
}
</style>
