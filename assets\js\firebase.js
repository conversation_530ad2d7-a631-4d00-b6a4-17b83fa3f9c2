import {cartMerge, thirdPartyLogin} from "@/api/web"

export default function (){
	let that = this;
	function getCookie(name) {
		const cookies = document.cookie.split(';');
		for (let i = 0; i < cookies.length; i++) {
			const cookie = cookies[i].trim();
			if (cookie.startsWith(name + '=')) {
				return cookie.substring(name.length + 1);
			}
		}
		return null;
	}

	function loadScript(url) {
		return new Promise(function (resolve, reject) {
			let script = document.createElement('script');
			script.src = url;
			script.onload = function () {
				setTimeout(() => {
					resolve();
				}, 300)
			};
			script.onerror = function () {
				reject(new Error('Failed to load script: ' + url));
			};
			document.body.appendChild(script);
		});
	}

	function handleThirdLogin(data) {
		//第三方登录
		thirdPartyLogin(data).then(async res => {
			let data = res.data;
			await that.$store.commit('setUserInfo', data);
			that.$store.commit('setLogin', false);
			await cartMerge({
				uuid: that.$store.state.userUUID
			});
		}).then(() => {
			that.$store.dispatch('updateHeadFootPages')
		})
	}

	function handleGoogleLogin(result) {
		// 用户登录成功
		let additionalUserInfo = result.additionalUserInfo;
		// 进行下一步操作，如导航到其他页面或显示用户欢迎信息;
		let data = {
			type: 1,
			typ: "firebaseAuth"
		}, profile = additionalUserInfo.profile;
		if (!profile.email) {
			that.$toast.error("Please first go to Google account binding email")
			return false;
		}
		data.googleId = profile.id ? profile.id : profile.sub;
		data.email = profile.email;
		data.firstName = profile.given_name;
		data.lastName = profile.family_name;
		data.proId = that.$store.state.proId;
		handleThirdLogin(data);
	}

	//如果已登录不执行下面的代码
	if (getCookie('token')) {
		return false;
	}
	let fConfig = that.$store.getters.projectComment?.firebase?JSON.parse(that.$store.getters.projectComment.firebase):null;
	if (!fConfig) {
		return false;
	}
	Promise.all([loadScript('https://www.gstatic.com/firebasejs/8.10.0/firebase-app.js'), loadScript('https://www.gstatic.com/firebasejs/8.10.0/firebase-auth.js'), loadScript('https://accounts.google.com/gsi/client')]).then(() => {
		let firebaseConfig = fConfig.firebaseConfig;
		firebase.initializeApp(firebaseConfig);
		// 获取 Google 登录按钮元素
		var googleLoginButton = Array.from(document.getElementsByClassName('google-login-button'));
		if (!googleLoginButton && !googleLoginButton.length) {
			return false;
		}
		// 创建 Google 登录提供商对象
		var provider = new firebase.auth.GoogleAuthProvider();
		provider.setCustomParameters({
			prompt: 'select_account'
		});
		googleLoginButton.forEach(item => {
			// 处理 Google 登录按钮点击事件
			item.addEventListener('click', function () {
				// 使用 Firebase 身份验证方法处理 Google 登录
				firebase.auth().signInWithPopup(provider)
					.then(function (result) {
						console.log(result)
						handleGoogleLogin(result)
					})
					.catch(function (error) {
						console.log(error)
					});
			});
		})
		// 获取 Facebook 登录按钮元素
		var facebookLoginButton = Array.from(document.getElementsByClassName('facebook-login-button'));
		// 创建 Facebook 登录提供商对象
		var provider2 = new firebase.auth.FacebookAuthProvider();
		// 处理 Facebook 登录按钮点击事件
		facebookLoginButton.forEach(item => {
			item.addEventListener('click', function () {
				// 使用 Firebase 身份验证方法处理 Facebook 登录
				firebase.auth().signInWithPopup(provider2)
					.then(function (result) {
						// 用户登录成功
						let additionalUserInfo = result.additionalUserInfo;
						// 进行下一步操作，如导航到其他页面或显示用户欢迎信息
						let profile = additionalUserInfo.profile;
						if (!profile.email) {
							that.$toast.error("Please first go to Facebook account binding email")
							return false;
						}
						let data = {
							type: 2,
							typ: "firebaseAuth"
						};
						data.facebookId = profile.id ? profile.id : profile.sub;
						data.email = profile.email;
						data.firstName = profile.first_name;
						data.lastName = profile.last_name;
						data.proId = that.$store.state.proId;
						handleThirdLogin(data);
					})
					.catch(function (error) {
						// 处理登录错误
						var errorCode = error.code;
						var errorMessage = error.message;
						console.error('failed:', errorCode, errorMessage);
						// 显示错误信息给用户或执行其他逻辑
					});
			});
		})
		google.accounts.id.initialize({
			client_id: fConfig.client_id,
			callback: handleOneTapCallback
		});
		google.accounts.id.prompt();

		function handleOneTapCallback(response) {
			if (response.credential) {
				const credential = firebase.auth.GoogleAuthProvider.credential(response.credential);
				firebase.auth().signInWithCredential(credential)
					.then((result) => {
						handleGoogleLogin(result)
					})
					.catch((error) => {
					});
			} else {
			}
		}
	})
};
