<template>
	<div class="Tabs">
		<el-tabs v-model="activeName" class="tabs" type="card" @tab-click="tabClickFun">
			<template v-for="(item, index) in bindValue.childList">
				<el-tab-pane :label="item.alias" :name="item.paramName" class="left" :key="item.id">
					<template #label>
						<div class="flex justify-content-center align-items-center">
							<el-image lazy :src="JSON.parse(item.imageJson)[0].url" alt="" />
						</div>
						<span>
							{{ item.alias }}
						</span>
					</template>
					<div class="box custom-scrollbar">
						<LanyardCheckBox v-for="citem in item.childList" :showPrice="true" :key="citem.id" :bindValue="citem" :freeTitle="citem.priceInfo.priceType === 1" noBorder :selectedData="selectedData" :aspectRatio="'aspect-ratio: 230/100'" activeType="child" :bindName="bindName" @clickFun="selectFun($event, item)"></LanyardCheckBox>
					</div>
				</el-tab-pane>
			</template>
		</el-tabs>
	</div>
</template>
<script>
import LanyardCheckBox from "@/components/Lanyard/LanyardCheckBox";
import PriceText from "@/components/Quote/PriceText"

export default {
	props: {
		bindValue: {
			type: Object,
		},
		bindName: {
			type: String,
		},
		selectedData: {
			type: [Array, Object],
		},
		sameAsTheLeftAttachement: {
			type: Boolean,
		},
		defActiveName: {
			type: String
		}
	},
	components: {
		LanyardCheckBox,
		PriceText
	},
	data() {
		return {
			activeName: "",
			tempArr: [null, null],
		};
	},
	methods: {
		tabClickFun(val) {
			const selectedParent = this.bindValue.childList.find(x => x.paramName == val._props.label)
			this.selectFun(null, selectedParent) // 选择父参数子参数置空
		},
		selectFun(val, parentVal) {
			if(val) {
				this.$emit('clickFun', Object.assign({}, parentVal, { childList: [val.value] }))
			}else{
				this.$emit('clickFun', Object.assign({}, parentVal, { childList: [] }))
			}
		},
	},
	computed: {
		lang() {
			return this.$store.getters.lang.quote || {};
		},
	},
	watch: {
		defActiveName: {
			handler(newV){
				if(newV) this.activeName = newV
			},
			immediate: true
		}
	},
	created() {},
	mounted() {},
};
</script>

<style scoped lang="scss">
::v-deep .el-tabs--card > .el-tabs__header .el-tabs__nav {
	border: 0 !important;
}

::v-deep .el-tabs--card > .el-tabs__header .el-tabs__item:first-child {
	border-left: 1px solid #f0f1f2;
}

.Tabs ::v-deep {
	width: 100%;

	.left,
	.right{
		float: none !important;
	}

	.el-tabs__nav {
		display: grid;
		grid-template-columns: repeat(2, 1fr);

		// grid-auto-rows: 192px;
		column-gap: 11px;
		width: 100%;
		border: none;

		@media screen and (max-width: 768px) {
			grid-auto-rows: auto;
		}

		.el-tabs__item {
			font-size: 18px;
			font-weight: 400;
			color: #333333;
			text-align: center;
			height: 100%;
			background: #f5f6f7;
			border: 1px solid #f0f1f2;
			border-radius: 10px 10px 0px 0px;
			display: flex;
			flex-direction: column;
			justify-content: center;
			padding: 20px 0;
			line-height: 1em;

			// padding-top: 5px;
			@media screen and (max-width: 1280px) and (min-width: 769px) {
				padding: 20px 0;
			}

			@media screen and (max-width: 768px) {
				font-size: 12px;
				line-height: 2em;
				padding: 10px 0;
				overflow: hidden;
			}

			span {
				padding-top: 5px;
			}

			.el-image {
				width: 321px;
				height: 111px;
				margin-bottom: 20px;

				// height: 106px;
				@media screen and (max-width: 1280px) and (min-width: 769px) {
					max-height: 106px;
					margin-bottom: 5px;
				}

				@media screen and (max-width: 768px) {
					max-height: 50px;
					margin-bottom: 5px;
					max-width: 321px;
				}

				img {
					width: 100%;
					height: 100%;
				}
			}
		}

		.el-tabs__item:hover {
			border-color: $color-primary;
		}

		.el-tabs__item:nth-child(1) {
			margin-right: -1px;
		}

		.el-tabs__item:nth-child(2) {
			margin-left: 2px;
		}
	}

	.el-tabs__content {
		border: 1px solid #ebebeb;
		border-top: none;
	}

	.el-tabs__header {
		margin: 0;
	}

	.el-tabs--card > .el-tabs__header .el-tabs__item {
		@media screen and (max-width: 767px) {
			padding: 5px;
		}
	}

	.el-tabs--card > .el-tabs__header .el-tabs__item.is-active {
		background: #ffffff;
		border-color: $color-primary;
		border-bottom: transparent;

		&::before {
			position: absolute;
			content: url("~/assets/lanyardQuote/svg/bgh_Selected.svg");
			left: 0;
			top: 0;
			width: 50px;
			height: 50px;
			z-index: 1;
		}

		&::after {
			position: absolute;
			content: url("~/assets/lanyardQuote/svg/Selected.svg");
			left: 8px;
			top: 5px;
			width: 19px;
			height: 20px;
			display: flex;
			align-items: center;
			z-index: 2;
		}
	}

	.custom-radio {
		.label {
			font-size: 16px;
			font-weight: 400;
			color: #333333;

			@media screen and (max-width: 768px) {
				font-size: 12px;
			}
		}
	}

	.price {
		line-height: 1em;
		font-size: 16px;
		font-weight: 400;

		@media screen and (max-width: 768px) {
			font-size: 12px;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
			max-width: 78.34px;
			margin-top: 5px;
		}
	}

	.left {
		float: none; //twenty twenty 样式冲突
		.box {
			display: grid;
			grid-template-columns: repeat(4, 1fr);

			// grid-template-rows: repeat(auto-fill,180px);
			justify-content: space-between;
			gap: 17px;
			padding: 17px 20px;

			@media screen and (max-width: 1919px) {
				grid-template-columns: repeat(auto-fill, 175px);
				column-gap: 15px;
			}

			@media screen and (max-width: 1679px) {
				grid-template-columns: repeat(4, 1fr);
				column-gap: 10px;
				padding: 15px;
			}

			@media screen and (max-width: 1280px) and (min-width: 769px) {
				grid-template-columns: repeat(auto-fill, 175px);
				column-gap: normal;
			}

			@media screen and (max-width: 768px) {
				grid-template-columns: repeat(2, 1fr);
				justify-content: space-between;
				gap: 8px;
				padding: 10px;
			}

			@media screen and (max-width: 500px) {
				grid-template-columns: repeat(2, 1fr);
				justify-content: space-between;
				gap: 8px;
				padding: 5px;
			}
		}
	}

	.right {
		.asSameCheckBox {
			display: grid;
			grid-template-columns: repeat(2, 1fr);
			border-bottom: 1px solid #f5f5f5;
			padding: 0 0 5px 0;

			label {
				grid-column: 2/3;
				text-align: center;

				.el-checkbox__label {
					margin-top: 5px;
					font-size: 16px !important;
					color: #333;

					@media screen and (max-width: 768px) {
						font-size: 12px !important;
					}
				}
			}
		}

		.box {
			display: grid;
			grid-template-columns: repeat(5, 1fr);

			// grid-template-rows: repeat(auto-fill,180px);
			justify-content: space-between;
			gap: 17px;
			padding: 17px 20px;

			@media screen and (max-width: 1919px) {
				grid-template-columns: repeat(auto-fill, 175px);
				column-gap: 15px;
			}

			@media screen and (max-width: 1679px) {
				grid-template-columns: repeat(4, 1fr);
				column-gap: 10px;
				padding: 15px;
			}

			@media screen and (max-width: 1280px) and (min-width: 769px) {
				grid-template-columns: repeat(auto-fill, 175px);
				column-gap: normal;
			}

			@media screen and (max-width: 768px) {
				grid-template-columns: repeat(4, 1fr);
				justify-content: space-between;
				gap: 8px;
				padding: 10px;
			}

			@media screen and (max-width: 500px) {
				grid-template-columns: repeat(3, 1fr);
				justify-content: space-between;
				gap: 8px;
				padding: 5px;
			}

			.item {
				.el-image {
					@media screen and (max-width: 1280px) and (min-width: 769px) {
						height: 165px;
					}
				}
			}
		}

		.double-box .left {
			&::after {
				position: absolute;
				content: "";
				width: 1px;
				height: 100%;
				right: 0;
				top: 0;
				background-color: #f5f5f5;
			}
		}

		.double-box {
			display: grid;
			grid-template-columns: repeat(2, 1fr);

			.left,
			.right {
				position: relative;

				.title {
					font-size: 15px;
					font-weight: bold;
					color: #333333;
					margin-left: 20px;
					margin-top: 10px;
				}

				.box2 {
					display: grid;
					grid-template-columns: repeat(2, 1fr);

					// grid-template-rows: repeat(auto-fill,180px);
					justify-content: space-between;
					-moz-column-gap: 5px;
					column-gap: 10px;
					row-gap: 10px;
					max-height: 450px;
					overflow-y: scroll;
					padding: 12px 15px;

					@media screen and (max-width: 1919px) {
						grid-template-columns: repeat(2, 1fr);
					}

					@media screen and (max-width: 1280px) and (min-width: 1246px) {
						grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
					}

					@media screen and (max-width: 1245px) and (min-width: 972px) {
						grid-template-columns: repeat(3, 1fr);
					}

					@media screen and (max-width: 971px) {
						grid-template-columns: repeat(2, 1fr);
						padding: 5px;
						.title {
							font-size: 12px;
							margin-left: 10px;
						}
					}

					@media screen and (max-width: 500px) {
						.title {
							font-size: 12px;
							margin-left: 10px;
						}

						grid-template-columns: repeat(1, 1fr);
					}

					.item {
						height: auto;
					}
				}
			}
		}
	}
}
</style>