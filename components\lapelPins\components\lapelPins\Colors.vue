<template>
	<div class="colors">
		<div class="colorsGrid" :class="itemData.styleName">
			<div class="colorsBox" v-for="(item, index) in itemData.childList" :key="index" :class="{ selected: item.selected }">
				<div class="imgWrap">
					<img :src="getImg(item)" :style="{ aspectRatio: aspectRatio + '', ...imgStyleObj }" alt="" class="img" />
					<b class="icon-fangda4" @click="fangdaImg(index)"></b>
				</div>
				<div class="colorsInfo">
					<div class="colorsName">{{ item.alias2 || item.alias }}</div>
					<div class="colorsPrice" :class="{ free: citem == 'Free' }" v-for="(citem, index) in getPrice(item)" :key="index">{{ citem }}</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	name: "colors",
	components: {},
	props: {
		itemData: {
			type: Object,
			default: () => {},
		},
		aspectRatio: {
			type: [Number, String],
			default: "",
		},
		imgStyleObj: {
			type: Object,
			default: () => {},
		},
	},
	data() {
		return {};
	},
	methods: {
		getImg(item) {
			if (item.imageJson) {
				try {
					let imgUrl = JSON.parse(item.imageJson);
					return imgUrl[0].url;
				} catch (e) {
					console.error("Error parsing imageJson:", e);
					return item.imageUrl || "";
				}
			} else {
				return item.imageUrl || "";
			}
		},
		getPrice(item) {
			if (item.priceInfo && item.priceInfo?.priceShow) {
				return item.priceInfo["priceShow"];
			}
			return "Free";
		},
		selectOption(item) {
			// 实现选择逻辑
			this.$emit("option-selected", item);
		},
		fangdaImg(index) {
			let zoomNum = 2;
			if (this.itemData.paramName.includes("BackStamp")) zoomNum = 1.5;
			if (this.itemData.paramName.includes("Outdoor-fd")) zoomNum = 1.2;
			if (this.isMobile) {
				zoomNum = 1;
				if (this.itemData.paramName.includes("Outdoor-fd")) zoomNum = 0.5;
			}
			const $viewer = this.$viewerApi({
				images: this.viewImgList,
				options: {
					initialViewIndex: index,
					initialCoverage: 0,
					viewed: function () {
						$viewer.zoomTo(zoomNum);
					},
				},
			});
			return $viewer;
		},
	},
	created() {},
	mounted() {},
	computed: {
		isMobile() {
			return this.$store.getters.isMobile;
		},
		viewImgList() {
			return this.itemData.childList.map((item) => {
				return this.getImg(item);
			});
		},
	},
	watch: {},
};
</script>
<style scoped lang="scss">
.colors {
	width: 100%;

	.colorsGrid {
		width: 100%;
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		gap: 20px;
		padding: 0 15%;
		&.style2_2 {
			grid-template-columns: repeat(2, 1fr);
			padding: 0 24%;
		}

		.colorsBox {
			position: relative;
			background: #ffffff;
			border-radius: 8px;
			border: 1px solid #f0f0f0;
			display: flex;
			flex-direction: column;
			align-items: center;
			gap: 10px;
			cursor: pointer;
			transition: all 0.3s ease;
			padding-bottom: 10px;

			&.selected {
				border: 2px solid #4a90e2;
			}
			.imgWrap {
				width: 100%;
				display: flex;
				align-items: center;
				justify-content: center;
				border-radius: 8px 8px 0 0;
				overflow: hidden;

				.img {
					aspect-ratio: 220/160;
					max-width: 100%;
					max-height: 100%;
					object-fit: cover !important;
				}
				b {
					cursor: pointer;
					position: absolute;
					top: 10px;
					right: 10px;
					color: #ccc;
				}
			}

			.colorsInfo {
				width: 100%;
				display: flex;
				flex-direction: column;
				align-items: center;
				gap: 5px;
				padding: 0 4px;

				.colorsName {
					font-size: 18px;
					color: #333333;
				}

				.colorsPrice {
					font-size: 16px;
					color: #666666;
					text-align: center;

					&.free {
						color: #d81e06;
					}
				}
			}
		}

		@include respond-to(mb) {
			&.style2_2 {
				grid-template-columns: repeat(2, 1fr);
				padding: 0;
				.colorsBox {
					border: 1px solid #f0f0f0;
				}
			}
			padding: 0;
			gap: 10px;
			.colorsBox {
				gap: 6px;
				border: none;
				background: transparent;
				padding-bottom: 6px;
				text-align: center;
				.imgWrap {
					b {
						top: 5px;
						right: 5px;
					}
				}
				.colorsInfo {
					.colorsName {
						font-size: 14px;
						font-weight: 700;
					}

					.colorsPrice {
						font-size: 12px;
					}
				}
			}
		}
	}
}
</style>
