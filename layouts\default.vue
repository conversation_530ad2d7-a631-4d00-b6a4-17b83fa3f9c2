<template>
	<div id="o2oApp" :class="{ neonAi: isNeonAi }">
		<modalHeader :data="layout[0]" :class="{ sticky: isModal || isNeonQuote, neonQuote: isNeonQuote }"
			v-if="!isInvoice && !isDesignSystem && (!isUserCenter || !$store.getters.isMobile) && !isQuoteIframe && !isNeonAi">
		</modalHeader>

		<div class="nuxtContent" :theme="$store.state.proTheme" :class="{ teaBgImg: $store.state.proTheme == 10 }">
			<div v-if="isUserCenter" id="userContainer"></div>
			<nuxt v-else />
		</div>

		<modalFooter :data="layout[1]"
			v-if="!isInvoice && !isDesignSystem && !isNeonQuote && !isQuoteIframe && !isNeonAi">
		</modalFooter>

		<!-- 回到顶部按钮 -->
		<b class="icon-a-icon-jt3zhuanhuan to-top" hidden :title="lang.toTop" @click="scrollTop"
			v-if="!isInvoice && !showMask.modal"></b>



		<modalDiscountDialogNew v-if="isHome" :theme="$store.state.proTheme"></modalDiscountDialogNew>
		<QuoteTopDialog v-model="$store.state.showSizeDialog" :model="false" :width="device != 'mb' ? '457px' : '90%'"
			:close="closeFn">
			<replay-size-upload></replay-size-upload>
		</QuoteTopDialog>



		<div flex class="mask" :class="{ quote: isQuoteComponent(showMask.modal) }" v-show="showMask">
			<div class="temporary-tips" v-if="showMask === 'temporary'">
				<strong>{{ lang.temporary }}</strong>
				<div>{{ lang.temporaryTip }}</div>
				<button primary @click="goBuildWeb">{{ lang.temporaryBtn }}</button>
			</div>
			<template v-else-if="typeof (showMask) == 'object'">
				<component v-if="showMask.modal" class="mask-content" :data="showMask" :is="showMask.modal" />

				<div v-else-if="showMask.video" class="mask-content">
					<b pointer class="icon-jxsht-zdgl-xx" @click="$store.commit('setMask', false)"></b>
					<video :src="showMask.value" autoplay controls loop playsinline></video>
				</div>

				<div v-else-if="showMask.img" class="mask-content pic">
					<b pointer class="icon-jxsht-zdgl-xx" @click="$store.commit('setMask', false)"></b>
					<pic :src="showMask.value"></pic>
				</div>
			</template>
		</div>
		<div flex class="mask" v-show="showLogin">
			<Login v-show="showLogin == 'login'"></Login>
			<PensLogin v-if="showLogin == 'pensLogin'"></PensLogin>
		</div>
	</div>
</template>

<script>
import { getCodeByIp, getThirdContent, turnaroundTimeCheckStatus } from "~/api/web"
import { deepClone, lockScroll, setOpacity, unlockScroll } from "@/utils/utils";
import QuoteTopDialog from "~/components/Quote/TopDialog.vue";
import ReplaySizeUpload from "~/components/replaySizeUpload.vue";
export default {
	head() {
		let themeObj = '', theme = { ...this.$store.state.themeFontConfig };
		theme['color-primary-opacity'] = theme['color-primary'] + 'CC';
		theme['color-primary-lighten'] = this.$store.getters.shadeColor(theme['color-primary'], 20);
		theme['color-primary-darken'] = this.$store.getters.shadeColor(theme['color-primary'], -20);
		if (theme['tag-color'] && theme['tag-color'].length > 0) theme['tag-color-lighten'] = setOpacity(theme['tag-color'], 0.12);
		theme['scroll-padding-top'] = (process.browser && document.querySelector('#modalHeader')?.offsetHeight || 68) + 'px';
		for (let key in theme) {
			themeObj += `--${key}:${theme[key]};` //主题色
		}
		let lang; // lang处理 uk不符合国际标准 2023/10/30 修改
		if (this.$store.state.language.countryCode === 'uk') lang = this.$store.state.language.language + '-' + 'GB';
		else lang = this.$store.state.language.language + '-' + (this.$store.state.language.countryCode && this.$store.state.language.countryCode.toUpperCase());

		//结构化数据空值处理
		let structuringScript = [];
		if (this.$store.state?.structuringList) {
			let list = JSON.parse(this.$store.state.structuringList);
			list.forEach((item) => {
				structuringScript.push({
					type: "application/ld+json",
					innerHTML: item || "",
				});
			});
		}
		return {
			htmlAttrs: {
				lang: lang,
				style: themeObj
			},
			meta: [
				{
					hid: 'google-site-verification',
					name: 'google-site-verification',
					content: 'tzkz32YFkUVUQpjIXX1xoRMV4MpFz4TLSCXjmrFABaQ',
				},
			],
			link: [{ rel: 'icon', type: 'image/x-icon', href: this.$store.state.proIcon }],
			script: structuringScript
		};
	},
	watch: {
		showMask(val) {
			if (val) {
				setTimeout(() => {
					lockScroll()
				}, 100)
			} else {
				unlockScroll()
			}
		}
	},
	components:{
		QuoteTopDialog,
		ReplaySizeUpload
	},
	computed: {
		lang() {
			return this.$store.getters.lang.layout || {};
		},
		device() {
			return this.$store.state.device;
		},
		showMask() {
			return this.$store.state.showMask
		},
		showLogin() {
			return this.$store.state.showLogin
		},
		layout() {
			let data = [...this.$store.state.layoutInfo.pc];
			if (this.$store.getters.isMobile) data = this.$store.getters.deepMerge(deepClone(data), deepClone(this.$store.state.layoutInfo.mb));
			return data;
		},
		isModal() {
			return !this.isUserCenter && (this.$route.name == 'lang-home' || this.$route.name == 'lang-challenge-coin-gallery' || this.$route.name == 'lang-all');
		},
		isQuoteIframe() {
			return this.$route.query.type === 'quoteIframe' || this.$route.query.type === 'iframe'
		},
		isUserCenter() {
			return this.$store.state.pagePath.includes('/user/') || this.$store.state.pagePath == '/user';
		},
		isInvoice() {
			return this.$store.state.pagePath.includes('/invoice');
		},
		isDesignSystem() {
			return this.$store.state.pagePath.includes('/design/');
		},
		isNeonAi() {
			let path = this.$store.state.pagePath? this.$store.state.pagePath.toLowerCase():"";
			return path.includes('/neonai');
		},
		isNeonQuote() {
			return this.$store.state.pagePath.includes('/quote/neon-signs');
		},
		isHome() {
			return this.$store.state.pagePath === "/";
		},
	},
	beforeCreate() {
		// // 重写 route.push()
		// const push = this.$router.push,
		// 	lang = this.$store.state.language.lang;
		// this.$router.push = function (location) {
		// 	location.path = decodeURI(location.path).replace(/ /g, '-');

		// 	if (lang && !location.path.startsWith('/user/') && location.path.includes('lang-') && !location.path.startsWith(lang + '/')) location.path = lang + location.path;

		// 	if (location.path == this.currentRoute.path) return;
		// 	else return push.call(this, location);
		// };

		// 广告参数存储
		this.$route.query.fbclid && this.$cookies.set('fbclid', this.$route.query.fbclid, {
			path: '/',
			maxAge: 60 * 60 * 24 * 30
		})
		this.$route.query.rdt_cid && this.$cookies.set('rdt_cid', this.$route.query.rdt_cid, {
			path: '/',
			maxAge: 60 * 60 * 24 * 30
		})


		// 生产环境清除console.log
		// if (process.env.NODE_ENV === 'production') console.log = () => false;


		if (process.browser && !process.env.isManage) {
			// 乾坤引入个人中心
			const { registerMicroApps, initGlobalState, start } = require('qiankun');
			registerMicroApps([
				{
					name: 'user',
					activeRule: '/user',
					container: '#userContainer',
					entry: process.env.entry
				},
			]);
			window.qiankunActions = initGlobalState({
				proId: this.$store.state.proId,
				language: this.$store.state.language,
				currency: this.$store.state.currency,
			});
			start();


			// 禁止用户在页面上使用ctrl+鼠标滚轮和 ctrl + “+” “-” 进行页面缩放
			// document.addEventListener('mousewheel', e => {
			// 	e = e || window.event;
			// 	if ((e.wheelDelta && event.ctrlKey) || e.detail) event.preventDefault();
			// }, { capture: false, passive: false });
			// document.addEventListener('keydown', event => {
			// 	if ((event.ctrlKey === true || event.metaKey === true) && (event.keyCode === 61 || event.keyCode === 107 || event.keyCode === 173 || event.keyCode === 109 || event.keyCode === 187 || event.keyCode === 189)) event.preventDefault();
			// }, false);
		}
	},
	mounted() {
		if (process.browser) {
			// 作为iframe时与父页面通信，禁用点击
			if (this.$route.query.isPreview) {
				document.documentElement.style.setProperty('pointer-events', 'none');

				window.parent.postMessage(document.body.scrollHeight, '*');
				window.onresize = () => window.parent.postMessage(document.body.scrollHeight, '*');
				window.addEventListener('message', ({ data }) => {
					if (typeof (data) == 'object') this.$store.commit('setProSystem', data)
				});
			}

			this.$nextTick(() => {
				// 往下滚动一屏后出现回到顶部按钮
				document.addEventListener('scroll', () => {
					if (document.querySelector('.to-top')) document.querySelector('.to-top').hidden = window.scrollY < window.innerHeight;
				});

				this.$store.dispatch('setProDom');

				// 清除第一次进入网站的不存在链接报404
				setTimeout(this.$store.commit('setLanguageUrl', this.$store.state.domainFull), 500);

				try {
					//设置vuetify主题色
					this.$vuetify.theme.themes.light.primary = this.$store.state.themeFontConfig['color-primary'];
					this.$vuetify.theme.themes.dark.primary = this.$store.state.themeFontConfig['color-primary'];
				} catch (e) { }
			})
		}
		//获取区号
		getCodeByIp().then((res) => {
			this.$store.commit('set_areaCode', res.data);
		})
		//获取第三方代码
		getThirdContent().then(res => {
			let thirdPartyCode = res.data && res.data.thirdPartyCode && JSON.parse(res.data.thirdPartyCode) || [];
			if (!thirdPartyCode) {
				return
			}
			let scriptList = [], noscript = [];
			// 开发环境 不加载第三方插件
			if (process.env.NODE_ENV === "production") {
				thirdPartyCode.forEach(item => {
					if (!item.hasOwnProperty('dataType') || item.dataType === 0) {
						if (!this.isQuoteIframe) {
							scriptList.push(item)
						} else {
							//如果是报价iframe弹窗，只加载广告代码
							const keywords = ['gtag', 'criteo', 'fbq', 'ttq', 'rdt', 'lintrk', 'twq'];
							if (keywords.some(key => item?.src?.includes(key) || item?.innerHTML?.includes(key))) {
								scriptList.push(item);
							}
						}
					} else if (item.dataType === 1) noscript.push(item);
				});
				scriptList.forEach(item => {
					let script = document.createElement("script");
					for (let i in item) {
						script[i] = item[i]
					}
					document.head.appendChild(script);
				})
				noscript.forEach(item => {
					let noscript = document.createElement("noscript");
					for (let i in item) {
						noscript[i] = item[i]
					}
					document.head.appendChild(noscript);
				})
			}
		})
		turnaroundTimeCheckStatus().then(res => {
			this.$store.commit('set_turnaroundTimeCheckStatus', res.data.enableTurnaroundTimeCheck);
		})
		if (this.$route.query.flag == "email" && !this.$store.state.token && !sessionStorage.getItem("pensLogin")) {
			setTimeout(() => {
				this.$store.commit('setMask', "pensLogin");
				sessionStorage.setItem("pensLogin", true);
			}, 2000)
		}
	},
	methods: {
		isQuoteComponent(str) {
			if (typeof str !== "string") {
				return false;
			}
            if(str === 'modalQuoteQuickQuoteDialog'){
                return false;
            }
			return str.startsWith('modalQuote')
		},
		scrollTop() {
			document.body.scrollIntoView({ behavior: 'smooth' });
		},
		goBuildWeb() {
			window.open('https://www.o2o.co/previewWeb');
			this.$store.commit('setMask', false);
		},
		closeFn() {
			this.$store.commit("setSizeDialog", false);
		}
	}
}
</script>

<style lang="scss" scoped>
#o2oApp {
	display: flex;
	min-height: 100vh;
	align-items: stretch;
	flex-direction: column;

	&.neonAi {
		background-color: #0F1115;
	}
}

#modalHeader {
	z-index: 999;
}

#modalFooter {
	z-index: 2;
}

.sticky {
	background: white;
	position: sticky;
	top: 0;
}

.neonQuote ::v-deep .tips {
	display: none;
}

.nuxtContent {
	flex: 1;

	&.teaBgImg {
		background: url(https://sticker-static.oss-accelerate.aliyuncs.com/image/uploads/20231009/background.jpg) bottom no-repeat;
		background-size: 100vw auto;
	}
}

.qiankun-box {
	min-height: 46vh;

	>div {
		background: white;
	}
}

.to-top {
	color: white;
	cursor: pointer;
	padding: 0.6em 0.7em;
	border-radius: 0.5em;
	background: rgba($color: #000, $alpha: .4);
	transform: rotate(-90deg);
	position: fixed;
	z-index: 99999;
	right: 1.56em;
	bottom: 9%;
	@include respond-to(mb){
		right: 28px;
		bottom: 80px;
	}
}



.mask {
	height: 100vh;
	max-height: 1500px;
	align-items: center;
	justify-content: center;
	background: rgba($color: black, $alpha: 0.6);
	position: fixed;
	z-index: 6000;
	right: 0;
	left: 0;
	top: 0;

	.mask-content {
		max-width: 98vw;
		max-height: 96vh;
		position: relative;

		@include respond-to(mb) {
			max-width: 95vw;
		}

		>*:not(b) {
			overflow: hidden;
			object-fit: contain;
			border-radius: 0.5em;
		}

		&:not(.quote) b {
			text-shadow: 0 0 0.25em #333;
		}

		::v-deep>.icon-jxsht-zdgl-xx {
			color: white;
			line-height: 1;
			font-size: 2.5em;
			background-image: radial-gradient(#333 50%, transparent 0);
			position: absolute;
			right: -0.4em;
			top: -0.4em;
			z-index: 999;
		}

		::v-deep>.icon-guanbi {
			width: 2em;
			line-height: 2em;
			text-align: center;
			border-radius: 50%;
			background: white;
			position: absolute;
			margin: 5px;
			transform: translate(30%, -30%);
			right: 0;
			top: 0;
			z-index: 100;

			@include respond-to(mb) {
				transform: translate(0%, 0%);
				border-radius: 0;
				background: transparent;
			}
		}

		>img,
		>video {
			width: auto;
			margin: auto;
			display: block;
			min-width: 30vw;
			max-width: 90vw;
			min-height: 20vh;
			max-height: 90vh;
		}
	}

	&.quote {
		align-items: flex-end;

		.mask-content {
			width: 98vw;
			height: 96vh;

			::v-deep>.close-icon {
				width: 2em;
				line-height: 2em;
				text-align: center;
				border-radius: 50%;
				background: white;
				position: absolute;
				margin: 5px;
				transform: translate(50%, -50%);
				right: 0;
				top: 0;
				z-index: 100;
				cursor: pointer;

				@include respond-to(mb) {
					transform: translate(0%, 0%);
					border-radius: 0;
					background: transparent;
				}
			}

			@include respond-to(mb) {
				overflow: hidden;
				position: absolute;
				top: 20px;
				left: 0;
				right: 0;
				bottom: 0;
				max-width: 100vw;
				max-height: calc(100dvh - 20px);
				height: 100%;
				width: 100%;
			}
		}
	}
}

.temporary-tips {
	color: white;
	text-align: center;

	strong {
		font-size: 2.2vmax;
	}

	div {
		margin: 1vmax 0 2.5vmax;
	}

	button {
		border-radius: 2em;
		background: #287BE9;
	}
}
</style>
