<!--
multiAdditionalItemPrice:附加项加价百分比
multiBasicGrindingPrice:基础模具费加价百分比
multiEntiretyPrice:附加模具费加价百分比
multiBasicUnitPrice:基础单价加价百分比
plusAdditionalitemPrice:附加项加价
plusBasicGrindingPrice: 基础磨具费加价
plusBasicUnitPrice: 基础单价加价
plusEntiretyPrice:附加模具费加价
priceInfo.priceType 加价类型
更多文档：https://www.yuque.com/chaojigang-eu86m/adyym3/aa6w0i13nzqeowza
-->

<!--let a = (uni  + plusBasicUnitPrice) *multiBasicUnitPrice-->
<!--(a+plusAdditionalitemPrice) * multiAdditionalItemPrice-->

<template>
	<div v-if="onlyAddInquiry !== 1 && !isPriceRemind" class="PriceText">
		<!--        加单价-->
		<template v-if="priceType === 1">
			<p class="normal-text" v-if="getFinaPrice > 0 || getMoldPrice > 0 || getSetUpFee > 0 || getQuoteSetUpFee>0" :style="{...css.normaltext, ...css?.[`${nowDevice}Normaltext`]}">
				<span v-if="getFinaPrice > 0">
					+ <CCYRate :price="getFinaPrice"></CCYRate>
					<slot name="unit"> /{{ getUnit() }} </slot>
					<span v-if="isFd && fdPriceDiscountCode"> ({{ fdPriceDiscountCode }}) </span>
				    <template v-if="getMoldPrice > 0">,</template>
				</span>
                <span v-if="getMoldPrice > 0">
					+ <slot name="mold" v-if="moldText === 0">{{ lang.moldFee }}:</slot>
					<slot name="mold" v-if="moldText === 1">{{ lang2.extra }}:</slot>
					<CCYRate :price="getMoldPrice"></CCYRate>
					<slot name="mold" v-if="moldText === 3">{{ lang3.semiModelPrice }} + {{ lang.moldFee }}</slot>
					<slot name="mold" v-if="moldText === 4">Set up fee</slot>
					<span v-if="isFd && priceInfo.fdMoldPriceDiscountCode"> ({{ priceInfo.fdMoldPriceDiscountCode }}) </span>
				</span>
                <!-- fd设置费-->
                <span v-if="getSetUpFee > 0">
					{{lang.setUpFee}}:<CCYRate :price="getSetUpFee"></CCYRate> <span v-if="isFd && priceInfo.fdSetUpPriceDiscountCode">
						({{ priceInfo.fdSetUpPriceDiscountCode }}) </span> / each order
				</span>
                <!--普通报价设置费-->
                <span v-if="getQuoteSetUpFee > 0">
					{{lang.setUpFee}}:<CCYRate :price="getQuoteSetUpFee"></CCYRate>
				</span>
			</p>
			<p class="tip-text" :style="css.tipTextStyle" v-else>{{ free }}</p>
		</template>
		<!--        单价百分比-->
		<!--        价格分层 单价百分比 *  multiAdditionalItemPrice（附加项加价百分比）-->
		<template v-if="priceType === 3">
			<p class="normal-text" :style="{...css.normaltext, ...css?.[`${nowDevice}Normaltext`]}">
				<span>+ {{ (priceInfo.unitPercent * morePriceData.multiAdditionalItemPrice * 100).toFixed(0) }}% {{
                        lang.ofUnitPrice }}</span>
                <span v-if="getMoldPrice > 0">
					+ {{ lang.moldFee }}:
					<CCYRate :price="getMoldPrice"></CCYRate>
					<span v-if="isFd && priceInfo.fdMoldPriceDiscountCode"> ({{ priceInfo.fdMoldPriceDiscountCode }}) </span>
				</span>
                <!-- 设置费-->
                <span v-if="getSetUpFee > 0">
					{{lang.setUpFee}}:<CCYRate :price="getSetUpFee"></CCYRate> <span v-if="isFd && priceInfo.fdSetUpPriceDiscountCode">
						({{ priceInfo.fdSetUpPriceDiscountCode }}) </span> / each order
				</span>
                <!--普通报价设置费-->
                <span v-if="getQuoteSetUpFee > 0">
					{{lang.setUpFee}}:<CCYRate :price="getQuoteSetUpFee"></CCYRate>
				</span>
			</p>
		</template>
		<!--      5-递增价格 6-组合价格 11-组合价格 16-双面刺绣钥匙扣加价 17-数量加价-->
		<template v-if="[5,6,11,16,17].includes(priceType)">
			<p class="normal-text" :style="{...css.normaltext, ...css?.[`${nowDevice}Normaltext`]}">
				<span>
					+ <CCYRate :price="getFinaPrice"></CCYRate> <template v-if="priceType!=5">/</template>{{ getUnit() }}
					<span v-if="isFd && fdPriceDiscountCode"> ({{ fdPriceDiscountCode }}) </span>
				</span>
                <span v-if="getMoldPrice > 0">
					+ {{ lang.moldFee }}:
					<CCYRate :price="getMoldPrice"></CCYRate>
					<span v-if="isFd && priceInfo.fdMoldPriceDiscountCode"> ({{ priceInfo.fdMoldPriceDiscountCode }}) </span>
				</span>
                <!-- 设置费-->
                <span v-if="getSetUpFee > 0">
					{{lang.setUpFee}}:<CCYRate :price="getSetUpFee"></CCYRate> <span v-if="isFd && priceInfo.fdSetUpPriceDiscountCode">
						({{ priceInfo.fdSetUpPriceDiscountCode }}) </span> / each order
				</span>
                <!--普通报价设置费-->
                <span v-if="getQuoteSetUpFee > 0">
					{{lang.setUpFee}}:<CCYRate :price="getQuoteSetUpFee"></CCYRate>
				</span>
            </p>
        </template>
    </div>
</template>

<script>
export default {
	name: "PriceText",
	props: {
        morePriceDataOrigin:{
            type: Object,
            default: ()=>{
                return {}
            }
        },
		paramData: {
			type: Object,
		},
		freeText: {
			type: String,
		},
		//PVCpatches报价模具费文案
		moldText: {
			type: Number,
			default: 0,
		},
		css: {
			type: Object,
			default: function () {
				return {
					normaltext: "",
				};
			},
		},
		isFd: {
			type: Boolean,
			default: false,
		},
        customizeModel:{
            type: Object
        }
	},
	data() {
		return {};
	},
	computed: {
		device() {
			return this.$store.state.device;
		},
		nowDevice() {
			return this.device === "mb" ? "mb" : "pc";
		},
        embroideryCoverageValue(){
            return this.$store.state.embroideryCoverageValue;
        },
        sizeValue() {
            return this.$store.state.quoteSizeValue;
        },
        sizeName() {
            return this.$store.state.quoteSizeName;
        },
        quoteQuantity() {
            return this.$store.state.quoteQuantity || 0;
        },
        free() {
            return this.freeText || this.lang.free;
        },
        lang() {
            return this.$store.getters.lang.quote || {};
        },
        lang2() {
            return this.$store.getters.lang.confirm || {};
        },
        lang3() {
            return this.$store.getters.lang.cart || {};
        },
        priceType() {
            return this.priceInfo?.priceType || 0;
        },
        currencySymbol() {
            return this.$store.getters.currencySymbol;
        },
        currencyRate() {
            return this.$store.getters.currencyRate;
        },
        morePriceData() {
            if(Object.keys(this.morePriceDataOrigin).length){
                return this.morePriceDataOrigin
            }
            return this.$store.getters.morePriceData || {
                        multiAdditionalItemPrice: 1,
                        multiBasicGrindingPrice: 1,
                        multiEntiretyPrice: 1,
                        multiBasicUnitPrice: 1,
                        plusAdditionalitemPrice: 0,
                        plusBasicGrindingPrice: 0,
                        plusBasicUnitPrice: 0,
                        plusEntiretyPrice: 0,
                    };
        },
        priceInfo() {
            return this.paramData?.priceInfo;
        },
        onlyAddInquiry() {
            return this.paramData?.onlyAddInquiry;
        },
        paramType() {
            return this.paramData?.paramType;
        },
        inputNum() {
            return this.paramData?.inputNum;
        },
        isPriceRemind() {
            return this.paramData?.isPriceRemind;
        },
        getMoldPrice() {
            let moldPrice = 0;
            if (this.isFd) {
                if (this.priceInfo.fdEnableMoldPrice) {
                    moldPrice = this.priceInfo.fdMoldPrice;
                }
            } else {
                moldPrice = this.priceInfo.moldPrice;
            }
            if (this.paramType === "SIZE") {
                //fd尺寸价格补全，直接返回
                if(this.customizeModel && this.customizeModel.moldFee){
                    moldPrice = this.customizeModel.moldFee
                    return moldPrice
                }
                return (this.morePriceData.multiBasicGrindingPrice * (moldPrice + this.morePriceData.plusBasicGrindingPrice)).toFixed(2);
            } else {
                // moldPriceType 1固定值 2 组合加价
                if (this.priceInfo.moldPriceType == 1) {
                    return (this.morePriceData.multiEntiretyPrice * (moldPrice + this.morePriceData.plusEntiretyPrice)).toFixed(2);
                } else if (this.priceInfo.moldPriceType == 2) {
                    return (this.morePriceData.multiEntiretyPrice * (this.getPackagePrice(this.priceInfo.composeMoldPrice)+ this.morePriceData.plusEntiretyPrice)).toFixed(2);
                }
            }
        },
        getFinaPrice() {
            if (this.priceType === 1) {
                return ((this.priceInfo.unitPrice + this.morePriceData.plusAdditionalItemPrice) * this.morePriceData.multiAdditionalItemPrice).toFixed(2);
            } else if (this.priceType === 5) {
                if (this.paramType === "SIZE") {
                    return ((this.getIncreasePrice(this.inputNum, this.priceInfo.increasePrice) + this.morePriceData.plusBasicUnitPrice) * this.morePriceData.multiBasicUnitPrice).toFixed(2);
                } else {
                    return ((this.getIncreasePrice(this.inputNum, this.priceInfo.increasePrice) + this.morePriceData.plusAdditionalItemPrice) * this.morePriceData.multiAdditionalItemPrice).toFixed(2);
                }
            } else if (this.priceType === 6) {
                return ((this.getPackagePrice(this.priceInfo.composeUnitPrice) + this.morePriceData.plusAdditionalItemPrice) * this.morePriceData.multiAdditionalItemPrice).toFixed(2);
            } else if (this.priceType === 11) {
                return ((this.getQtyAndPrice(this.priceInfo.composeUnitPrice) + this.morePriceData.plusAdditionalItemPrice) * this.morePriceData.multiAdditionalItemPrice).toFixed(2);
            }else if(this.priceType === 16){
                let embroideredKeychainsPrice = JSON.parse(this.priceInfo.embroideredKeychainsPrice);
                let findPrice = embroideredKeychainsPrice.find(item=>{
                    return item.embroideryCoverage.includes(this.embroideryCoverageValue);
                })
                if(findPrice){
                    return ((this.getEmbroideredKeychainsPrice(JSON.stringify(findPrice.config)) + this.morePriceData.plusAdditionalItemPrice) * this.morePriceData.multiAdditionalItemPrice).toFixed(2);
                }
            }else if(this.priceType === 17){
                return ((this.getIncreasePrice(this.quoteQuantity, this.priceInfo.increasePrice) + this.morePriceData.plusAdditionalItemPrice) * this.morePriceData.multiAdditionalItemPrice).toFixed(2);
            }
        },
        getQuoteSetUpFee(){
            let setupCharge = 0;
            setupCharge = this.priceInfo.setupCharge;
            if (this.paramType === "SIZE") {
                return (this.morePriceData.multiBasicGrindingPrice * (setupCharge + this.morePriceData.plusBasicGrindingPrice)).toFixed(2);
            } else {
                // setupChargeType 1固定值
                if (this.priceInfo.setupChargeType == 1) {
                    return (this.morePriceData.multiEntiretyPrice * setupCharge).toFixed(2);
                }else if (this.priceInfo.setupChargeType == 2) {
                    return (this.morePriceData.multiEntiretyPrice * this.getPackagePrice(this.priceInfo.composeMoldPrice)).toFixed(2);
                }
            }
        },
        getSetUpFee() {
            let setUpFee = 0;
            if (this.isFd && this.priceInfo.fdEnableSetUpPrice) {
                setUpFee = this.priceInfo.fdSetUpPrice;
            }
            return setUpFee;
        },
        fdPriceDiscountCode() {
            if (!this.isFd) {
                return "";
            }
            if (this.priceType === 1) {
                return this.priceInfo?.fdIncreasePriceCode;
            } else if (this.priceType === 5) {
                let increasePrice = this.priceInfo.increasePrice ? JSON.parse(this.priceInfo.increasePrice) : "";
                if (increasePrice && increasePrice.length) {
                    return increasePrice[0]?.code?.charAt(0);
                }
            } else if (this.priceType === 6) {
                let composeUnitPrice = this.priceInfo.composeUnitPrice ? JSON.parse(this.priceInfo.composeUnitPrice) : "";
                if (composeUnitPrice && composeUnitPrice.length) {
                    return composeUnitPrice[0]?.code?.charAt(0);
                }
            } else if (this.priceType === 11) {
                let composeUnitPrice = this.priceInfo.composeUnitPrice ? JSON.parse(this.priceInfo.composeUnitPrice) : "";
                if (composeUnitPrice && composeUnitPrice.length) {
                    return composeUnitPrice[0]?.increasePrice[0]?.code.charAt(0);
                }
            }
        },
    },
    methods: {
        getUnit() {
            if (this.paramData.quantityUnit) {
                return this.paramData.quantityUnit;
            }
            let paramType = this.paramType;
            let priceType = this.priceInfo.priceType;
            if (paramType === "QUANTITY") {
                if (priceType == 1) {
                    return this.lang.unit1;
                } else if (priceType == 5) {
                    return this.lang.unit2;
                } else {
                    return this.lang.pc;
                }
            } else {
                return this.lang.pc;
            }
        },
        getEmbroideredKeychainsPrice(composeUnitPrice){
            let arr = JSON.parse(composeUnitPrice),
                    increasePrice = arr[this.sizeName],
                    priceItem;
            if(!increasePrice){
                return 0
            }
            priceItem = increasePrice[increasePrice.length - 1];
            if(!this.quoteQuantity){
                return Number(priceItem.unitPrice);
            }
            for (let i = increasePrice.length - 1; i >= 0; i--) {
                if (i === 0) {
                    priceItem = increasePrice[i];
                    break;
                } else if (Number(this.quoteQuantity) >= Number(increasePrice[i].quantity)) {
                    priceItem = increasePrice[i];
                    break;
                }
            }
            return Number(priceItem.unitPrice);
        },
        getQtyAndPrice(composeUnitPrice) {
            let arr = JSON.parse(composeUnitPrice),
                    sizeItem = arr[arr.length - 1],
                    increasePrice,
                    priceItem;
            //获取尺寸所在区间
            for (let i = arr.length - 1; i >= 0; i--) {
                if (i === 0) {
                    sizeItem = arr[i];
                    break;
                } else if (Number(this.sizeValue) >= Number(arr[i].paramValue)) {
                    sizeItem = arr[i];
                    break;
                }
            }
            if (sizeItem) {
                //获取数量区间单价
                increasePrice = sizeItem.increasePrice;
                priceItem = increasePrice[increasePrice.length - 1];
                for (let i = increasePrice.length - 1; i >= 0; i--) {
                    if (i === 0) {
                        priceItem = increasePrice[i];
                        break;
                    } else if (Number(this.quoteQuantity) >= Number(increasePrice[i].num)) {
                        priceItem = increasePrice[i];
                        break;
                    }
                }
                return Number(priceItem.price);
            }
            return 0;
        },
        getPackagePrice(composeUnitPrice) {
            if (this.isFd) {
                let increaseArr = JSON.parse(composeUnitPrice),
                        findItem;
                for (let i = increaseArr.length - 1; i >= 0; i--) {
                    if (i == 0) {
                        findItem = increaseArr[i];
                        break;
                    } else if (Number(this.sizeValue) >= Number(increaseArr[i].paramValue)) {
                        findItem = increaseArr[i];
                        break;
                    }
                }
                if (findItem) {
                    return Number(findItem.price);
                } else {
                    return Number(increaseArr[increaseArr.length - 1].price);
                }
            } else {
                let arr = JSON.parse(composeUnitPrice);
                let findItem = arr.find((item) => {
                    return item.paramValue == this.sizeValue;
                });
                if (findItem) {
                    return Number(findItem.price);
                } else {
                    return 0;
                }
            }
        },
        getIncreasePrice(num = 0, increase) {
            let increaseArr = JSON.parse(increase),
                    findItem;
            for (let i = increaseArr.length - 1; i >= 0; i--) {
                if (i == 0) {
                    findItem = increaseArr[i];
                    break;
                } else if (Number(num) >= Number(increaseArr[i].quantity)) {
                    findItem = increaseArr[i];
                    break;
                }
            }
            if (findItem) {
                return Number(findItem.unitPrice);
            } else {
                return Number(increaseArr[increaseArr.length - 1]?.unitPrice);
            }
        },
    },
};
</script>

<style scoped lang="scss">
.PriceText {
    .normal-text {
        display: flex;
        flex-direction: column;
        flex-wrap: wrap;
        justify-content: center;
    }

    .normal-text,
    .tip-text {
        text-align: center;
        font-size: 1em;
    }

    .tip-text {
        color: #de3500;
    }

    @include respond-to(mb){
        .normal-text,
        .tip-text {
            text-align: center;
        }
    }
}
</style>