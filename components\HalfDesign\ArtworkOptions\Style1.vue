<template>
	<div class="mb-4" :class="stepData.styleName">
	    <slot name="stepText"></slot>
		<div class="step-content">
			<div class="step-item" :class="{ active: index === selectIndex }" v-for="(step, index) in stepData.productParamList" :key="index" @click="selectStep(step, index)">
				<!--				<img :src="step.imgDetail" :alt="step.valueName" :title="step.valueName">-->
				<b class="icon-jxs-daxingxi" v-show="index === 0"></b>
				<b class="icon-jxsht-ybp-xg1" v-show="index === 1"></b>
				<span>{{ step.valueName }}</span>
				<v-tooltip bottom>
					<template v-slot:activator="{ on, attrs }">
						<v-icon v-bind="attrs" v-on="on" size="18px"> mdi-help-circle-outline </v-icon>
					</template>
					<div class="text-center" style="display: flex; align-items: start">
						<div v-if="step.imgDetail" style="max-width: 150px; max-height: 150px; margin-right: 10px">
							<img :src="step.imgDetail" :alt="step.valueName" :title="step.valueName" style="object-fit: contain" />
						</div>
						<div style="text-align: left; color: #fff; line-height: 22px; font-size: 13px; width: 250px; word-break: break-word; white-space: pre-line">{{ step.remark }}</div>
					</div>
				</v-tooltip>
			</div>
		</div>
		<div class="errorTip">
			<v-alert dense outlined type="error">
				{{ langSemiCustom.errorTip }}
			</v-alert>
		</div>
	</div>
</template>
<script>
export default {
	props: {
		stepData: {
			type: Object,
			default: {},
		},
	},
	data() {
		return {
			selectIndex: -1,
			selectItem: null,
		};
	},
	computed: {
		langSemiCustom() {
			return this.$store.getters.lang?.semiCustom;
		},
	},
	methods: {
		selectStep(item, index,state=false) {
			this.selectIndex = index;
			this.selectItem = item;
			this.$emit("selectStep", {
				type: this.stepData.attributeFlag,
				data: item,
				parent: this.stepData,
				id: this.stepData.id,
				firstSelect:state
			});
		},
		selectDefault() {
			if (this.selectIndex <= -1) {
				let findIndex = this.stepData.productParamList.findIndex((item) => {
					return item.isBlank === 0;
				});
				if (findIndex > -1) {
					this.selectStep(this.stepData.productParamList[findIndex], findIndex,true);
				}
			}
		},
	},
	mounted() {
		this.$Bus.$on("selectDefaultEmailStep", this.selectDefault);
	},
	beforeDestroy() {
		this.$Bus.$off("selectDefaultEmailStep",this.selectDefault);
	},
};
</script>
<style scoped lang="scss">
@import "~assets/css/half.scss";

.style1 .step-content {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	grid-gap: 10px;

	.step-item {
		@include flex-center;
		outline: 1px dashed $border-color;
		padding: 10px;
		@include radius-response;
		background-color: $background-color;
		cursor: pointer;

		b {
			font-size: 20px;
			margin-right: 4px;
		}

		img {
			height: 30px;
			object-fit: contain;
			margin-right: 10px;
		}
		span {
			margin-right: 5px;
		}

		@media (any-hover: hover) {
			&:hover {
				outline-color: $color-primary;
				outline-width: 2px;
				background-color: var(--color-second);
			}
		}

		&.active {
			outline-color: $color-primary;
			outline-width: 2px;
			background-color: var(--color-second);
			color: $color-primary;
		}
	}

	@include respond-to(mb) {
		grid-gap: 5px;

		.step-item {
			position: relative;
			flex-direction: column;
			span {
				margin-right: 0;
			}
			::v-deep .v-icon {
				position: absolute;
				right: 6px;
				top: 6px;
			}

			padding: 2px;

			img {
				height: 20px;
				margin-right: 0;
				margin-bottom: 5px;
			}
		}
	}
}
</style>
