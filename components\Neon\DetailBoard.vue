<template>
  <section class="detailBoard">
    <div class="detailTitle">
      {{ lang.orderSummary }}
    </div>
    <div class="detailContent">
      <div class="detailList scrollBar custom-scrollbar"
        :style="{'--foldHeight':`300px`,'overflow-y':viewMore ? 'hidden':'scroll'}">
        <div class="single"
          v-for="(val, key, index) in filterList(list)"
          :key="index"
          :style="{'display':key=='Designs Or Upload Your Artwork'?'none':''}"
          @click="jump(key)">
          <div v-if="key == 'Your Text'"
            class="leftDetail">
            <template v-for="(it,idx) in list[key] || 1">
              <div class="innermargin"
                :key="idx">
                <p v-if="tabName == 'Enter Your Text'">{{ lang.neon.detailTextLine + (idx+1) }}</p>
                <p><span v-if="tabName == 'Enter Your Text'">
                    {{ lang.neon.detailLine + (idx+1) }} </span>{{ lang.neon.detailFontColor }}</p>
              </div>
            </template>
          </div>
          <div v-else
            class="leftDetail">
            <p>{{ filterShowKey(key)}}</p>
          </div>
          <div v-if="key == 'Your Text'"
            class="middle textmiddle">
            <template v-for="(it,idx) in list[key]">
              <div class="innermargin"
                :key="idx">
                <p v-if="tabName == 'Enter Your Text'">{{ it['Your Text'] || it.placeholder }}</p>
                <p class="mid-content">
                  <span v-if="it['Select Font']?.paramName"
                    :style="{fontFamily:it['Select Font']
                      ?.paramName}">{{it['Select Font']?.paramName}}</span>
                  <span v-if="it['Select Color']?.alias">{{';' + it['Select Color']?.alias + ';'}}</span>
                  <span>{{ it.tubeColor ? it.tubeColor?.alt:'' }}</span>
                </p>
                <!-- <p class="mid-content"><img :src="filterImg(it['Select Font'])"
                      alt=""><span> {{';' + it['Select Color']?.alias }}</span></p> -->
              </div>
            </template>
          </div>
          <div v-else
            class="middle">
            <template v-if="key=='Select Size'">{{ (val?.alias || '') + formatSize}}</template>
            <template v-else-if="key=='What Size Would You Like?'">{{ (val?.alias || '') + formatSize}}</template>
            <template v-else-if="key=='Line'">{{ val?.alias || val?.paramName || val}}
              {{ val?.selectedSize?.paramValue ? formatInch(val?.selectedSize?.paramValue):'' }}</template>
            <template v-else-if="key=='Select Type'">
              <p v-for="(item,index) in val"
                :key="index">
                {{ item.alias }}
              </p>
            </template>
            <template v-else>{{ val?.alias || val?.paramName || val}}</template>
          </div>
          <div class="rightDetail">
            <div v-if="key != 'Your Text'">
              <b class="icon-bianji"></b>
            </div>
            <div v-else
              class="mult-icon">
              <template v-if="tabName == 'Upload'">
                <b class="icon-bianji"></b>
              </template>
              <template v-else>
                <b v-for="i in val && val.length * 2" :key="i" class="icon-bianji"></b>
              </template>
            </div>
          </div>
        </div>
      </div>
      <div class="viewMore"
        @click="viewMore = !viewMore">
        <span class="btn">{{ lang.ViewMore }} </span>
        <i class="el-icon-d-arrow-right"
          :style="{'--rotateDeg':viewMore ? '90deg':'-90deg'}"></i>
      </div>
      <div class="hr"></div>

      <div class="price-range">
        <div class="priceList"
          v-if="!onlyInquiry">
          <div>
            {{ lang.neon.totalQua  }}
          </div>
          <div>
            1
          </div>
          <div>

          </div>
          <template v-if="discountPrice != 0">
            <div>
              {{ text1 }}
            </div>
            <div>
			  <template v-if="!isFd">
				{{ text2 }}
				<CCYRate :price="discountPrice"></CCYRate>
				<span v-if="priceData.discount<1">
					({{(Math.abs(1 - priceData.discount) * 100).toFixed(0)}}% OFF)
				</span>
			  </template>
            </div>
            <div></div>
          </template>
          <div>
            {{ lang.neon.Subtotal }}
          </div>
          <div class="subTotal">
			<template v-if="isFd">
				QUR
			</template>
			<template v-else>
				<CCY-rate :price="priceData.totalPrice?priceData.totalPrice:0"></CCY-rate>
				<CCY-rate :price="priceData.foundationUnitPrice?priceData.foundationUnitPrice:0"
				class="thrPrice"></CCY-rate>
			</template>
          </div>
          <div>
          </div>
          <!-- <div>
            {{ lang.neon.Discount }}
          </div>
          <div>
            -<CCY-rate
              :price="priceData.totalPrice?priceData.totalPrice - priceData.totalPrice*(1-projectComment.promotionDiscount) :0"></CCY-rate>
            <template v-if="priceData.totalPrice">({{projectComment.promotionDiscount*100}}% off)</template>
          </div> -->
          <div>
          </div>
        </div>

        <div class="priceList"
          v-else>
          <div>
            {{ lang.neon.totalQua }}
          </div>
          <div>
            1
          </div>
          <div>
          </div>
        </div>

        <div class="hr"></div>
        <div class="btnList">
          <div class="sub">
            <div v-if="!onlyInquiry"
              class="totalBox">

              <!-- 美国和澳大利亚不展示提前算税 -->
              <!-- <div :style="countryName != 'United States' && countryName != 'Australia' && textPrice > 0  ? 'display: flex;justify-content: space-between;width: 100%;padding: 0 10px;' : 'display: flex;justify-content: center;'"
              v-show="!onlyInquiry">
                <div class="currencyText">
                  {{ lang.Currency }}:
                  <el-select v-model="currencyId" :placeholder="lang.PleaseSelect" @change="changeCurrency" size="small" style="width: 100px">
                    <el-option v-for="item in currencyList" :key="item.id" :label="item.code" :value="item.id" />
                  </el-select>
                </div>
                <div v-if="countryName != 'United States'  && countryName != 'Australia' && textPrice > 0"
                  :style="countryName != 'United States'  && countryName != 'Australia' && textPrice > 0 ? 'display: flex;align-items: center;justify-content: center;' :'display:none'">
                  <strong class="subTotalText">{{getTax(1)}}:</strong>
                  <el-switch  v-model="IncludingVAT" :active-value="1" :inactive-value="0" active-text="Yes" inactive-text="No" class="operation"></el-switch>
                </div>
            </div> -->

              <div
                :style="continentName == 'Europe' && textPrice > 0  ? 'display: flex;justify-content: space-between;width: 100%;padding: 0 10px;' : 'display: flex;justify-content: center;'">
                <div>
                  <span>{{ lang.Currency }}:</span>
                  <el-dropdown class="currency-select">
                    <span class="el-dropdown-link">
                      {{ currentCurrency.code }} <i class="el-icon-caret-bottom"></i>
                    </span>
                    <el-dropdown-menu slot="dropdown"
                      class="select-option">
                      <el-dropdown-item v-for="item in currencyList"
                        :key="item.id"
                        @click.native="changeCurrency(item.id)">
                        {{ item.code }}
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </el-dropdown>
                </div>

                <div v-if="continentName == 'Europe' && textPrice > 0"
                  :style="continentName == 'Europe' && textPrice > 0 ? 'display: flex;align-items: center;justify-content: center;margin-left: 10px' :'display:none'">
                  <strong class="subTotalText">{{getTax(1)}}:</strong>
                  <el-switch v-model="IncludingVAT"
                    :active-value="1"
                    :inactive-value="0"
                    active-text="Yes"
                    inactive-text="No"
                    class="operation"></el-switch>
                </div>
              </div>

              <div v-if="priceData.totalPrice">
                <span v-if="continentName != 'Europe'">{{ lang.total }}:</span>
                <span v-else>{{ IncludingVAT == 1 ? getTax(2) : getTax(3) }}:</span>
                <span class="price">
                  <CCY-rate :decimal="2"
                    :price="getTotalPrice(priceData.totalPrice)"></CCY-rate>
                </span>
                <span style="padding-right:10px;">{{ lang.neon.totalAmoSub }}</span>
              </div>
            </div>
            <div v-else
              class="uploadTips">
              {{ lang.neon.uploadTipsForDetail }}
            </div>
          </div>
          <div class="miniNeonTips"
            v-if="isMiniNeon">
            {{ lang.neon.neonSignsSmallTitleBottom }}
          </div>
        </div>
      </div>
    </div>
    <template v-if="showDetails && device!='mb'">
      <b @click="shutFun"
        v-if="!noClose"
        class="icon-bps-guanbi"></b>
    </template>
    <slot></slot>
  </section>
</template>
<script>
import { filterObj, round2 } from "~/utils/utils";
import { getTaxByPrice } from "@/api/web";
export default {
  props: {
    noClose: {
      type: Boolean,
      default: false,
    },
    arr: {
      type: Array,
      default: [],
    },
    list: {
      type: Object,
    },
    priceData: {
      type: Object,
    },
    showDetails: {
      type: Boolean,
    },
    finaHeight: {
      type: [Number, String],
    },
    finaWidth: {
      type: [Number, String],
    },
    isImgTemplate: {
      type: Number,
    },
    tabName: {
      type: String,
    },
    neonDiscount: {
      type: Object,
    },
    onlyInquiry: {
      type: Boolean,
      default: false,
    },
    unitSign: {
      type: String,
    },
    unitConvertRatio: {
      type: Number,
      default: () => 0.3937,
    },
    hiddenSize: {
      type: Boolean,
      default: () => false,
    },
    isMiniNeon: {
      type: Boolean,
      default: () => false,
    },
	isFd: {
	  type: Boolean,
	  default: false
	}
  },
  data() {
    return {
      list2: [
        {
          name: "Total Quantity",
          value: "1",
        },
        {
          name: "Delivery Discount",
          value: "+$0",
        },
      ],
      viewMore: true,
      countryName: "",
      textPrice: null,
      IncludingVAT: 1,
      currencyId: "",
      continentName: "",
    };
  },
  methods: {
	formatInch(val) {
		if(this.unitSign === 'cm'){
			console.log('val', val)
			return (Number(val) / 0.3937 * this.unitConvertRatio).toFixed(0) + 'cm'
		}else{
			return val + '"'
		}
	},
    filterList(list) {
      if (this.tabName == "Upload") {
        return filterObj(list, ["Select Size", "Your Text"]);
      } else if (this.isImgTemplate) {
        return filterObj(
          list,
          this.onlyInquiry
            ? ["Your Text"]
            : ["Your Text", "What Size Would You Like?", "Select Type"]
        );
      } else {
        // 确保Select Size步骤保持原来的位置
        const arrList = Object.entries(list);
        const delIndex = arrList.findIndex((x) => x[0] == "Select Size");
		if(delIndex > -1){
			const findSize = arrList.splice(delIndex, 1);
			arrList.splice(1, 0, findSize[0]);
		}
        const newList = Object.fromEntries(arrList);
        return filterObj(
          newList,
          this.onlyInquiry
            ? ["Select Size", "What Size Would You Like?"]
            : ["What Size Would You Like?", "Size", "Select Type"]
        );
      }
    },
    getTax(type) {
      if (type == 1) {
        return "Including VAT";
      } else if (type == 2) {
        return "Subtotal incl. VAT";
      } else if (type == 3) {
        return "Subtotal excl. VAT";
      }
    },
    jump(val) {
      this.$emit("jump", val);
    },
    shutFun() {
      this.$emit("update:showDetails", false);
    },
    filterShowKey(key) {
      let temp = this.arr.find((x) => {
        return x.paramName == key;
      });
      return temp?.alias;
    },
    filterImg(val) {
      if (val?.imageJson) {
        let list = JSON.parse(val.imageJson);
        return list.length ? list[0].url : "";
      } else {
        return "";
      }
    },
    // 修改货币
    changeCurrency(val) {
      let findC = this.currencyList.find((item) => {
        return item.id === val;
      });
      if (findC) {
        this.$store.commit("setCurrency", findC);
      }
    },
    //税费开关开启，小计加上税费
    getTotalPrice(totalPrice) {
      if (this.IncludingVAT == 0 || this.continentName != "Europe") {
        return totalPrice;
      } else {
        let t = (Math.round(this.textPrice * totalPrice * 100) / 100).toFixed(
          2
        );
        return totalPrice + Number(t);
      }
    },
  },
  computed: {
    text1() {
      let discountName = "";
      //加急费，重量加价
      if (this.priceData.discountPrice) {
        return this.lang.rushDelivery;
      }
      if (this.priceData.discount > 1) {
        return this.lang.rushDelivery;
      } else if (this.priceData.discount < 1) {
        discountName = this.lang.neon.deliveryDis;
      }
      return discountName;
    },
    text2() {
      let ac;
      if (this.priceData.totalPrice > this.subtotal) {
        ac = "+";
      } else {
        ac = "-";
      }
      return ac;
    },
    subtotal() {
      return round2(
        round2(
          this.priceData.foundationUnitPrice * this.priceData.totalQuantity
        ) +
          this.priceData.toolingCharge +
          (this.priceData.setupCharge || 0)
      );
    },
    discountPrice() {
      if (this.priceData) {
        return `${Math.abs(this.priceData.totalPrice - this.subtotal)}`;
      } else {
        return 0;
      }
    },
    projectComment() {
      return this.$store.getters.projectComment;
    },
    device() {
      return this.$store.state.device;
    },
    lang() {
      return this.$store.getters.lang.quote || {};
    },
    currencyList() {
      return this.$store.state.currencyList;
    },
    currentCurrency() {
      return this.$store.state.currency;
    },
    formatSize() {
      return ` L (${(
        (Number(this.finaWidth) / 0.3937) *
        this.unitConvertRatio
      ).toFixed(0)})${this.unitSign} * H (${(
        (Number(this.finaHeight) / 0.3937) *
        this.unitConvertRatio
      ).toFixed(0)})${this.unitSign} `;
    },
  },
  watch: {
    "$store.state.currency": {
      handler(newValue) {
        this.currencyId = newValue.id;
      },
      immediate: true,
    },
  },
  created() {
    //提前算税费
    getTaxByPrice().then((res) => {
      this.textPrice = res.data.rate;
      this.countryName = res.data?.res?.country?.names.en;
      this.continentName = res.data?.res?.continent?.names.en; //洲
    });
  },
  mounted() {},
};
</script>
<style lang="scss">
.detailBoard {
  // padding: 40px 30px;
  background-color: #f2f2f2;
  display: flex;
  flex-direction: column;
  padding-top: 40px;
  height: auto;
  .detailTitle {
    font-size: 1.5em;
    max-height: 30px;
    font-weight: bold;
    margin-bottom: 20px;
    padding-inline: 30px;
  }
  .detailContent {
    padding: 0 30px;
    flex: 1;
    position: relative;

    .detailList {
      display: grid;
      grid-template-columns: 1fr;
      gap: 15px;
      cursor: pointer;
      height: var(--foldHeight);
      overflow-y: scroll;
      // overflow-y: scroll;
      // max-height: 300px;
      @media screen and(max-width: 768px) {
        margin-bottom: 16px;
        gap: 10px;
        // overflow-y: auto;
        // max-height: none;
      }
      .single {
        display: grid;
        grid-template-columns: 1fr 1fr 50px;
        align-items: center;
        gap: 5px;
        @media screen and (max-width: 768px) {
          grid-template-columns: 1fr 1fr 15px;
        }
        // &.neon-boder {
        //   border: 1px solid;
        //   border-image-slice: 1;
        //   border-image-source: linear-gradient(
        //       90deg,
        //       rgb(74, 72, 255) 0%,
        //       rgb(176, 18, 251) 100%
        //     )
        // }
        .leftDetail {
          font-size: 0.875em;
          font-family: Google Sans;
          font-weight: 400;
          color: #666666;
        }
        .middle {
          font-size: 1em;
          font-family: Google Sans;
          font-weight: 400;
          color: #333333;
          &.textmiddle {
            img {
              width: 41px;
              aspect-ratio: 41 / 12;
              object-fit: contain;
            }
          }
        }
        .rightDetail {
          color: #b3b3b3;
          text-align: right;
          font-size: 12px;
          div {
            text-align: center;
          }
          .mult-icon {
            display: flex;
            flex-direction: column;
            gap: 20px;
            @media screen and (max-width: 768px) {
              gap: 14px;
            }
          }
        }
      }
      // // 字体参数内部添加边距
      .innermargin {
        display: flex;
        flex-direction: column;
        gap: 15px;
        &:nth-child(n + 2) {
          margin-top: 15px;
          @media screen and (max-width: 768px) {
            margin-top: 10px;
          }
        }
        p {
          min-height: 18px;
        }
        @media screen and(max-width: 768px) {
          gap: 10px;
        }
      }
    }
    .viewMore {
      margin: 0 auto;
      max-height: 18px;
      width: fit-content;
      text-align: center;
      font-size: 0.9em;
      cursor: pointer;
      @media screen and(max-width: 768px) {
        display: none;
      }
      .btn {
        background-image: linear-gradient(
          to right,
          #20aeff,
          #b61ee8 90.6005859375%
        );
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      i {
        color: #b61ee8;
        transform: rotate(var(--rotateDeg));
        transition: transform 0.3s ease-in-out;
      }
    }
    .price-range {
      background: #f2f2f2;
      padding-top: 15px;
      .priceList {
        display: grid;
        grid-template-columns: 1fr 1fr 50px;
        gap: 20px;
        max-height: 120px;
        margin-top: 24px;
        margin-bottom: 24px;
        margin-block: 1em;
        @media screen and(max-width: 768px) {
          grid-template-columns: 1fr 1fr 15px;
          font-size: 14px;
          gap: 10px;
        }
        .subTotal {
          display: flex;
          gap: 10px;
          .thrPrice {
            font-size: 0.8em;
            line-height: 1.6;
            text-decoration: line-through;
          }
        }
      }
      .btnList {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding-bottom: 20px;
        @media screen and(max-width: 768px) {
          padding-bottom: 0;
        }

        .sub {
          display: flex;
          align-items: center;
          margin: 5px 0 12px;
          font-size: 1em;
          .totalBox {
            display: flex;
            flex-direction: column;
            align-items: center;
          }
          .price {
            font-size: 1.5em;
            font-family: Calibri;
            font-weight: bold;
            margin: 0 10px;
            margin-right: 5px;
            color: red;
          }
          .uploadTips {
            color: red;
            text-align: center;
          }
          .currency-select {
            border-radius: 6px;
            padding: 10px 13px;
            background-color: #fff;
            font-size: 0.875em;
            font-family: Calibri;
            font-weight: 400;
            color: #333333;
          }
          @media screen and(max-width: 768px) {
            margin: 5px;
            font-size: 12px;
          }
        }

        .tip {
          background: #ebebeb;
          border-radius: 3px;
          .tip-icon {
            width: 14px;
            height: 14px;
          }
          span {
            font-size: 12px;
            font-family: Arial;
          }
          .text-pc {
            display: none;
          }

          .text-mb {
            display: inline-block;
          }
        }

        .miniNeonTips {
          color: #de3500;
          font-size: 1em;
        }
      }
    }
    .editIcon {
      flex-shrink: 0;
      margin-left: 10px;
      color: #b3b3b3;
      font-size: 12px;
    }
    .hr {
      background-color: #dcdfe6;
      position: relative;
      display: block;
      height: 1px;
      width: 100%;
      margin: 10px 0;
    }
    @media screen and(max-width: 768px) {
      padding: 10px;
      .price-range {
        padding-top: 0;
      }
    }
  }
  .icon-bps-guanbi {
    cursor: pointer;
    position: absolute;
    right: 0;
    top: 0;
    padding: 15px;
    font-size: 17px;
  }
  @media screen and(max-width: 768px) {
    padding-top: 10px;
    .detailTitle {
      margin-bottom: 0;
      padding-left: 10px;
    }
  }
}
</style>
<style lang="scss">
.select-option {
  .el-dropdown-menu__item:focus,
  .el-dropdown-menu__item:not(.is-disabled):hover {
    -webkit-text-fill-color: transparent;
    -webkit-background-clip: text;
    background-image: linear-gradient(90deg, #20aeff, #b61ee8 90.6005859375%);
  }
}
.el-dropdown-menu {
  z-index: 9999 !important;
}
</style>

<style scoped lang="scss">
::v-deep .el-switch__label * {
  line-height: 1;
  font-size: 13px;
  display: inline-block;
}

::v-deep .el-switch__label {
  position: absolute;
  display: none;
  color: #fff !important;
  //   font-size: 13px !important;
}

::v-deep .el-switch__label--right {
  z-index: 1;
  right: 20px !important;
  margin-left: 0px;
}

::v-deep .el-switch__label--left {
  z-index: 1;
  left: 20px !important;
  margin-right: 0px;
}

::v-deep .el-switch__label.is-active {
  display: block;
}

::v-deep .el-switch .el-switch__core,
.el-switch .el-switch__label {
  width: 50px !important;
}

.el-switch {
  margin-left: 10px;
}
</style>
