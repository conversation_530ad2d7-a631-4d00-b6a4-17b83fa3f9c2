<template>
    <div>
        <base-dialog :value="saveDialogDialog" @update="update" :width="device !== 'mb' ? '510px' : '90%'">
            <div class="design" v-show="saveShow === 1">
                <div class="close" style="cursor: pointer" @click="closeClick"><b class="icon-a-icon-qxzhuanhuan"></b></div>
                <b class="icon-icon-save"></b>
                <p>{{ lang.saveYourDesign }}</p>
                <el-input v-model="filename" :placeholder="lang.enterName" @input="handleInputChange"></el-input><br />
                <el-button type="primary" :disabled="disabled" @click="saveDesign">{{ lang.saveDesign }}</el-button>
                <div class="design-text">
                    {{ lang.designText }} <a href="/info/privacy-policy">{{ lang.privacy }}</a>.
                </div>
            </div>
            <div class="design" v-show="saveShow === 2">
                <div class="close" @click="closeClick"><b class="icon-a-icon-qxzhuanhuan"></b></div>
                <b class="icon-icon-save"></b>
                <p>{{ lang.saveChanges }} "{{ filename }}"</p>
                <div class="text">{{ lang.emailAddress }}</div>
                <div class="radio">
                    <el-radio-group v-model="radio">
                        <el-radio :label="0">{{ lang.update }}</el-radio>
                        <el-radio :label="1">{{ lang.saveNew }}</el-radio>
                    </el-radio-group>
                </div>
                <el-input v-model="filename2" :placeholder="lang.enterName" v-show="radio === 1"></el-input><br />
                <el-button type="primary" @click="continueClick">{{ lang.continue }}</el-button>
            </div>
            <div class="design" v-show="saveShow === 3">
                <div class="close" @click="closeClick"><b class="icon-a-icon-qxzhuanhuan"></b></div>
                <b class="icon-icon-save"></b>
                <p>{{ lang.continueSave }} "{{ filename }}"</p>
                <el-input v-model="filename3" :placeholder="lang.enterEmailAddress"></el-input><br />
                <div class="emailText">{{ lang.yourEmail }}</div>
                <el-button type="primary" @click="saveDesignBefore">{{ lang.saveDesign }}</el-button>
            </div>
        </base-dialog>
    </div>
</template>

<script>
import {
    editUserTemplates,
    templateNameExist
} from "@/api/newDzxt"
import BaseDialog from "~/components/Quote/BaseDialog.vue";
import dzMixin from "@/mixins/dzMixin";
export default {
    props: ["saveDialogDialog", "saveShow"],
    mixins: [dzMixin],
    data() {
        return {
            filename: "",
            filename2: "",
            filename3: "",
            disabled: true,
            radio: 0,
            templatesId: null,
        }
    },
    components: {
        BaseDialog
    },
    watch: {
        isLogin(val) {
            if (val) {
                //判断是否在等待登录
                if (this.waitLogin) {
                    this.templateExist();
                }
            }
        }
    },
    computed: {
        isLogin() {
            return this.$store.getters.isLogin;
        },
        device() {
            return this.$store.state.device;
        },
        lang() {
            return this.$store.getters.lang.design;
        },
    },

    methods: {
        //未登录状态下出现登录弹窗
        saveDesignBefore() {
            this.waitLogin = true;
            this.$store.commit('setLogin', 'login');
            this.$store.commit('setLoginEmail', this.filename3);
        },

        //关闭弹窗
        closeClick() {
            this.$emit('update:saveDialogDialog', false);
        },

        //按钮状态
        handleInputChange(val) {
            this.disabled = !val;
        },

        //保存
        saveDesign() {
            if (this.isLogin) {
                this.templateExist();
            } else {
                this.$emit('update:saveShow', 3)
            }
        },

        //查找保存的模版名是否存在
        templateExist() {
            this.$gl.show();
			setTimeout(()=>{
                this.$gl.hide();
			},3000)
            templateNameExist({
                name: this.filename
            }).then((res) => {
                if (res.data === null) {
                    this.saveTemplates();
                } else {
                    this.templatesId = res.data;
                    this.$emit('update:saveShow', 2)
                }
            })
        },

        continueClick() {
            this.saveTemplates();
        },

        //上传、更新模版
        async saveTemplates() {
			let specification = null;
			if (this.canvas.isSticker) {
				if(!this.canvas.checkStickerSize()){
					return false;
				}
				specification = JSON.stringify({
					width: this.canvas.stickerSize.w,
					height: this.canvas.stickerSize.h
				});
			}
            let templateFile = this.canvas.getTemplateData();
            let templateUrl = await this.canvas.getTemplatePicPath();
            let params = {
                categoryId: this.$store.state.design?.pageInfo?.id,
                templateName: this.radio === 0 ? this.filename : this.filename2,
                templateFile: JSON.stringify(templateFile),
                templateUrl: templateUrl,
				specification
            }
            if (this.radio === 0) {
                if (this.templatesId) params['id'] = this.templatesId;
            }
			this.$emit('update:saveDialogDialog', false);
			await editUserTemplates(params);
			this.filename = "";
			this.filename2 = "";
			this.radio = 0;
			this.waitLogin = false;
            this.templatesId = null;
            this.$toast.success("success");
        },
        update(val) {
            this.$emit('update:saveDialogDialog', val);
        },
    }
}
</script>

<style scoped lang="scss">
@import "@/assets/css/dzxt_theme";

::v-deep .base-dialog .base-dialog-model-con .el-icon-close {
    display: none;
}

::v-deep .base-dialog .base-dialog-model-con {
    border-radius: 0;
}

.design {
    text-align: center;
    padding: 10px 0 30px;

    .close {
        text-align: right;
        padding: 0 10px 20px;

        b {
            font-weight: bold;
            font-size: 11px;
        }
    }

    b {
        font-size: 52px;
    }

    p {
        font-size: 18px;
        font-family: Calibri;
        font-weight: bold;
        margin: 19px 0;
    }

    .pp {
        font-size: 18px;
        font-family: Calibri;
        font-weight: bold;
        margin: 0;
    }

    .el-input {
        width: 377px;
        margin-bottom: 10px;
    }

    .el-button {
        width: 180px;
        height: 40px;
        font-size: 16px;
        font-family: Calibri;
        font-weight: bold;
    }

    .text {
        margin-bottom: 30px;
    }

    .radio {
        margin-bottom: 21px;
    }

    .emailText {
        text-align: left;
        margin-bottom: 13px;
        margin-left: 70px;
    }

    img {
        object-fit: contain;
    }

    .design-text {
        padding: 0 50px;
        margin-top: 23px;

        a {
            color: #2996FB;
        }
    }

    .closeBtn {
        width: auto;
        margin-top: 15px;
    }
}

@include respond-to(mb) {
    .design-text {
        padding: 0 35px;
        margin-top: 23px;

        a {
            color: #2996FB;
        }
    }

    .design {
        .el-input {
            width: 90%;
            margin-bottom: 10px;
        }

        .text {
            margin: 0 20px 30px;
        }
    }
}
</style>
