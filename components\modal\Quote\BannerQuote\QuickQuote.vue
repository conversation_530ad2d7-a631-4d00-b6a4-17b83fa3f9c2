<template>
	<div class="bannerQuoteWrap" nodebounce>
		<template v-if="$store.getters.isManage">
			<div class="quoteBoxContent stepList">Quick Quote Area</div>
		</template>
		<template v-else>
			<div class="quoteBoxContent" :style="{ ...(configStyle?.quoteContentStyle || {}) }">
				<div class="textTip" v-show="configStyle?.showTextTip && !$store.getters.isManage && currentStep === 1">
					*{{ lang.bannerQuote.selectForyou }}: <span class="proStyle">{{ lang.bannerQuote.satinLabels }}</span
					>. {{ lang.bannerQuote.pleaseFeel }}
					<span class="goPage" @click="goPage">{{ lang.bannerQuote.changeMaterials }}</span>
					{{ lang.bannerQuote.inPage }}.
				</div>
				<div class="return" v-if="currentStep > 1">
					<span @click.stop="prevStep"><b class="icon-Return"></b>{{ lang.return }}</span>
				</div>
				<div class="stepList">
					<template v-for="(item, index) in generalData">
						<div class="step-item step-size" :id="item.customStepName + '_' + item.id" :key="index" v-if="showFn(item, 'size')">
							<StepTitle :title="getTitle(item)" :step="getStep(item)">
								<template #suffix v-if="configStyle?.mbSizeHideHelp !== true">
									<span @click="sizeDialog = true" class="help">{{ lang.bannerQuote.sizeHelp }} <b class="icon-info"></b></span>
								</template>
							</StepTitle>
							<StepSize :hideHelp="configStyle?.sizeHideHelp === true" :itemData="item" :selectedData="selectedData" @showSizeDialog="showSizeDialog" @selectQuoteParams="selectQuoteParams($event.item, $event.citem)"></StepSize>
						</div>
						<div class="step-item step-qty" :id="item.customStepName + '_' + item.id" :key="index" v-if="showFn(item, 'qty')">
							<StepTitle :title="getTitle(item)" :step="getStep(item)"></StepTitle>
							<StepQty :qtyList="qtyList" :isCustom.sync="isCustom" :selectedQtyInd.sync="selectedQtyInd" :customNumber.sync="customNumber" :customNumberUnitPrice="customNumberUnitPrice" :customNumberPrice="customNumberPrice" @selectQtyList="selectQtyList" @filterCustomNumber="filterCustomNumber" :hideSavePrice="device === 'mb'"></StepQty>
							<component :is="configStyle?.FreeTip || 'FreeTip'"></component>
							<div class="nextBtn" v-show="currentStep === 1">
								<button primary @click="nextStep" :style="{ ...(configStyle?.buttonStyleObj?.buttonStyle || {}) }" :title="lang.continue" :disabled="customQty <= 0">{{ lang.continue }} <b class="icon-xiayige"></b></button>
								<p class="tip" :class="{ hidden: configStyle?.hideNextText === true }">{{ nextText }}</p>
							</div>
						</div>
						<div class="step-item step-plating" :id="item.customStepName + '_' + item.id" v-if="showFn(item, 'plating')">
							<StepTitle :title="getTitle(item)" :step="getStep(item)"></StepTitle>
							<div class="step-item-params">

								<template v-for="(citem, cindex) in item.childList">
									<div
										class="param-item"
										v-if="(item.childList.length > 4 && cindex < 3) || (item.childList.length <= 4 )"	
										:key="citem.id"
										:class="{
											active: hasId(citem.id, selectedData[item.paramName]),
										}"
										@click="selectQuoteParams(item, citem)"
									>
										<img loading="lazy" :src="citem.imageJson[1] ? citem.imageJson[1].url : citem.imageJson[0].url" :alt="citem.imageJson[0].alt" :title="citem.imageJson[0].alt" />
										<span>{{ citem.alias }}</span>
									</div>
								</template>
								<template v-if="item.childList.length > 4">
									<div class="param-item more" @click="seeMore(item)">
										<span>See More</span>
										<b class="icon-shuangcengjiantou-xia"></b>
									</div>
								</template>
							</div>
						</div>
						<div class="step-item step-colorAttachment" :id="item.customStepName + '_' + item.id" v-if="showFn(item, 'colorAttachment')">
							<StepTitle :title="getTitle(item)" :step="getStep(item)"></StepTitle>
							<div class="step-item-params">
								<template v-for="(citem, cindex) in item.childList">
									<div
										class="param-item"
										v-if="cindex < 5"
										:key="citem.id"
										:class="{
											active: hasId(citem.id, selectedData[item.paramName]),
										}"
										@click="selectQuoteParams(item, citem)"
									>
										<img loading="lazy" :src="citem.imageJson[1] ? citem.imageJson[1].url : citem.imageJson[0].url" :alt="citem.imageJson[0].alt" :title="citem.imageJson[0].alt" />
										<span>{{ citem.alias }}</span>
										<PriceText :paramData="citem"></PriceText>
									</div>
								</template>
								<template v-if="item.childList.length >= 6">
									<div class="param-item more" @click="seeMore(item)">
										<span>See More</span>
										<b class="icon-shuangcengjiantou-xia"></b>
									</div>
								</template>
							</div>
						</div>

						<div class="step-item step-keyChainsAttachment" :id="item.paramName + '_' + item.id" v-if="showFn(item, 'keyChainsAttachment')">
							<StepTitle :title="getTitle(item)" :step="getStep(item)"></StepTitle>
							<div class="step-item-params">
								<template v-for="(citem, cindex) in item.childList">
									<div
										class="param-item"
										v-if="cindex < 3"
										:key="citem.id"
										:class="{
											active: hasId(citem.id, selectedData[item.paramName]),
										}"
										@click="selectQuoteParams(item, citem)"
									>
										<img loading="lazy" :src="citem.imageJson[1] ? citem.imageJson[1].url : citem.imageJson[0].url" :alt="citem.imageJson[0].alt" :title="citem.imageJson[0].alt" />
										<span>{{ citem.alias }}</span>
										<PriceText :paramData="citem"></PriceText>
									</div>
								</template>
								<template v-if="item.childList.length >= 4">
									<div class="param-item more" @click="seeMore(item)">
										<span>See More</span>
										<b class="icon-shuangcengjiantou-xia"></b>
									</div>
								</template>
							</div>
						</div>

						<div class="step-item step-color" :id="item.customStepName + '_' + item.id" v-if="showFn(item, 'color')">
							<StepTitle :title="getTitle(item)" :step="getStep(item)"></StepTitle>
							<div class="step-item-params">
								<div
									class="param-item"
									v-for="citem in item.childList"
									:key="citem.id"
									:class="{
										active: hasId(citem.id, selectedData[item.paramName]) && device !== 'mb',
									}"
									@click="selectQuoteParams(item, citem)"
								>
									<span v-show="device !== 'mb'">{{ citem.alias }}</span>
									<div class="imgBox" :class="{ active: hasId(citem.id, selectedData[item.paramName]) && device == 'mb' }">
										<img loading="lazy" :src="citem.imageJson[1] ? citem.imageJson[1].url : citem.imageJson[0].url" :alt="citem.imageJson[0].alt" :title="citem.imageJson[0].alt" />
									</div>
									<span v-show="device == 'mb'">{{ citem.alias }}</span>
								</div>
							</div>
						</div>
						<div class="step-item step-fold-type" :id="item.customStepName + '_' + item.id" v-if="showFn(item, 'foldType')">
							<StepTitle :title="getTitle(item)" :step="getStep(item)"></StepTitle>
							<div class="step-item-params">
								<div class="param-item" v-for="citem in item.childList" :key="citem.id" @click="selectQuoteParams(item, citem)">
									<div class="imgBox" :class="{ active: hasId(citem.id, selectedData[item.paramName]) }">
										<img loading="lazy" :src="citem.imageJson[1] ? citem.imageJson[1].url : citem.imageJson[0].url" :alt="citem.imageJson[0].alt" :title="citem.imageJson[0].alt" />
									</div>
									<span>{{ citem.alias }}</span>
								</div>
							</div>
						</div>
						<div class="step-item step-ribbon" :id="item.customStepName + '_' + item.id" v-if="showFn(item, 'attachment')">
							<StepTitle :title="getTitle(item)" :step="getStep(item)"></StepTitle>
							<div class="step-item-params">
								<div
									v-for="citem in item.childList"
									:key="citem.id"
									class="param-item"
									:class="{
										active: hasId(citem.id, selectedData[item.paramName]),
									}"
									@click="selectQuoteParams(item, citem)"
								>
									<span>{{ citem.alias }}</span>
									<img loading="lazy" :src="citem.imageJson[1] ? citem.imageJson[1].url : citem.imageJson[0].url" :alt="citem.imageJson[0].alt" :title="citem.imageJson[0].alt" />
								</div>
							</div>
						</div>
						<div class="step-item step-upload" :id="item.customStepName + '_' + item.id" v-if="showFn(item, 'upload')">
							<StepTitle :title="getTitle(item)" :step="getStep(item)"></StepTitle>
							<div class="step-item-params" :class="{ 'param-item-hasUpload': uploadArtworkList.length }">
								<template v-if="!uploadArtworkList.length">
									<div class="param-item" @click="openUpload">
										<div class="uploadWrap">
											<b class="icon-shangchuan uploadIcon"></b>
											<span class="brow">{{ lang.browse }}</span>
											<el-tooltip popper-class="cusToolTip" effect="light">
												<div slot="content">
													<div>{{ lang.maxFile }}</div>
													<div>{{ item.tips }}</div>
												</div>
												<b class="icon-wenhao2" @click.stop></b>
											</el-tooltip>
										</div>
									</div>
								</template>
								<template v-else>
									<div class="param-item-hasUpload-left">
										<span class="upload-name">{{ uploadArtworkList[0].original_filename }}</span>
										<b class="icon-icon_Preview myIcon" @click.stop="previewImg(uploadArtworkList[0].secure_url)"></b>
										<b class="icon-check" style="color: #0cbd5f"></b>
										<b class="icon-shanchu2" style="color: #b6b0b0" @click="deleteUpload(0)"></b>
										<div v-click-outside="() => (showExtend = false)">
											<div class="icon" :class="{ active: showExtend }" style="color: #eb7e1a" @click="showExtend = !showExtend" v-if="uploadArtworkList.length > 1">
												<b class="icon-bps-xiaozhankai"></b>
											</div>
											<div class="extendUpload" v-show="showExtend">
												<div class="arrow"></div>
												<div class="uploadFileWrap">
													<div class="upload-item" v-for="(item, index) in uploadArtworkList" :key="index">
														<span class="upload-name">{{ item.original_filename }}</span>
														<b class="icon-icon_Preview myIcon" @click.stop="previewImg(item.secure_url)"></b>
														<b class="icon-check" style="color: #0cbd5f"></b>
														<b class="icon-shanchu2" style="color: #b6b0b0" @click="deleteUpload(index)"></b>
													</div>
												</div>
											</div>
										</div>
									</div>
									<div class="uploadWrap" @click="openUpload">
										<b class="icon-shangchuan uploadIcon"></b>
										<span class="brow">{{ lang.browse }}</span>
										<el-tooltip popper-class="cusToolTip" effect="light">
											<div slot="content">
												<div>{{ lang.maxFile }}</div>
												<div>{{ item.tips }}</div>
											</div>
											<b class="icon-wenhao2" @click.stop></b>
										</el-tooltip>
									</div>
								</template>
								<input type="file" ref="upload" :accept="acceptFileType" multiple @change="uploadPic($event,'upload',item)" />
							</div>
						</div>
						<div class="step-item step-time" :id="item.customStepName + '_' + item.id" :class="{ [configStyle?.timeStepStyle || '']: true }" v-if="showFn(item, 'discount')">
							<StepTitle :title="getTitle(item)" :step="getStep(item)"></StepTitle>
							<div class="step-item-params">
								<div
									class="param-item-wrap"
									v-for="citem in getNewDiscountList(item)"
									:key="citem.id"
									:class="{
										active: hasId(citem.id, selectedData[item.paramName]),
									}"
									@click="selectQuoteParams(item, citem)"
								>
									<div class="param-item">
										<DiscountText :itemData="citem"></DiscountText>
									</div>
									<div class="des">{{ citem.alias }}</div>
								</div>
							</div>
						</div>
					</template>
				</div>
				<div style="flex: 1"></div>
				<div class="quoteBoxFooter" v-show="currentStep > 1">
					<div>
						<span>{{ lang.quantity }}: {{ customQty }} Pcs</span>
						<span
							>{{ lang.subtotal }}:
							<CCYRate :price="finalPrice" class="finalPrice"></CCYRate>
						</span>
					</div>
					<button primary class="addCartBtn" v-if="currentStep == maxStep" :style="{ ...(configStyle?.buttonStyleObj?.buttonStyle || {}) }" :title="lang.addToCart" @click="addCart">{{ lang.addToCart }} <b class="icon-xiayige"></b></button>
					<button primary @click="nextStep" v-if="currentStep > 1 && currentStep < maxStep" :title="lang.continue">{{ lang.continue }}&nbsp;&nbsp;<b class="icon-xiayige"></b></button>
				</div>
				<KeychainsPj v-if="showTemplateDialog" :visible.sync="showTemplateDialog" :templateParams="templateParams" :selectedData="selectedData" @selectQuoteParams="selectQuoteParams($event.parentItem, $event.item)"></KeychainsPj>
			</div>
			<SizeDialog :size-dialog.sync="sizeDialog" previewImg="https://static-oss.gs-souvenir.com/web/quoteManage/20240920/Legend_-_Custom_Metal_keychain_size_reference_202409205faCKi.jpg"></SizeDialog>
			<base-dialog v-model="sizeDialog2" class="golfSizePreviewDialog" :width="device !== 'mb' ? '610px' : '95%'">
				<div class="sizePreviewDialogContent">
					<div class="body">
						{{ lang.sizeTip }}
					</div>
				</div>
			</base-dialog>
		</template>
	</div>
</template>

<script>
import { calculate, calculateAll, getInfo, getPriceData, otoAddCart } from "~/api/pins";
import { getCateParamRelationByCateId } from "~/api/web";
import CustomCircle from "~/components/Quote/customCircle.vue";
import CCYRate from "~/components/CCYRate.vue";
import { debounce, deepClone, isImageType } from "~/utils/utils";
import { uploadFile } from "~/utils/oss";
import "@/plugins/element";
import { acceptFileType, checkFile } from "@/utils/validate";
import StepTitle from "@/components/modal/Quote/QuoteComponents/StepTitle.vue";
import StepSize from "@/components/modal/Quote/QuoteComponents/StepSize.vue";
import StepQty from "@/components/modal/Quote/QuoteComponents/StepQty.vue";
import FreeTip from "@/components/modal/Quote/QuoteComponents/FreeTip.vue";
import FreeTip2 from "@/components/modal/Quote/QuoteComponents/FreeTip2.vue";
import SizeDialog from "@/components/modal/Quote/QuoteComponents/SizeDialog.vue";
import DiscountText from "@/components/modal/Quote/QuoteComponents/DicountText.vue";
import BaseDialog from "@/components/Quote/BaseDialog.vue";
import { getQuickQuoteConfigByPidAndCateId } from "assets/js/quickQuoteConfig";
import PriceText from "@/components/Quote/PriceText.vue";
import KeychainsPj from "@/components/Quote/QuickQuote/KeychainsPj.vue";
import { findSelectDiscount, getIsSmallQty, getQuoteTime } from "@/assets/js/QuotePublic";

const setIndex = (data) => {
	if (!data) {
		return false;
	}
	let index = 0;
	data.forEach((item) => {
		if (item) {
			if (!item.noShowDetail) {
				index += 1;
			}
			item.customIndex = index;
		}
	});
	return data;
};
const sortByKey = (array, key) => {
	return array.sort(function (a, b) {
		let x = a.priceInfo[key]; //如果要从大到小,把x,y互换就好
		let y = b.priceInfo[key];
		return x < y ? -1 : x > y ? 1 : 0;
	});
};

const getMyCustomStepName = (item) => {
	let paramName = item.paramName,
		type = item.paramType;
	let map = {
		qtyNameArr: {
			value: ["qty", "Quantity"],
			alias: "qty",
		},
		attachNameArr: {
			value: ["Design Areas", "Keychain Design Areas", "Keychain Printed Area"],
			alias: "attachment",
		},
		uploadNameArr: {
			value: ["Upload Artwork & Comments"],
			alias: "upload",
		},
		keyChainsPlatingArr: {
			value: ["Link & Chain Options","Select Link & Chain Options"],
			alias: "keyChainsAttachment",
		},
		colorAttachmentArr: {
			value: ["PVC Keychain Color"],
			alias: "colorAttachment",
		},
		platingNameArr: {
			value: ["Link & Chain Options", "Plating", "2D or 3D", "Embroidery Coverage"],
			alias: "plating",
		},
		colorNameArr: {
			value: ["Label Color"],
			alias: "color",
		},
		foldTypeNameArr: {
			value: ["Label Fold Type"],
			alias: "foldType",
		},
	};
	if (type === "SIZE") {
		return "size";
	}
	if (type === "DISCOUNT") {
		return "discount";
	}
	let name = "";
	for (let i in map) {
		if (map[i].value.includes(paramName)) {
			name = map[i].alias;
			break;
		}
	}
	return name;
};

const parseJSON = (str) => {
	return str
		? JSON.parse(str)
		: [
				{
					url: "",
				},
		  ];
};
const addCustomProperty = (data) => {
	let handle = (list) => {
		for (let i = 0; i < list.length; i++) {
			let item = list[i];
			item.remark = "";
			item.noShowDetail = false;
			item.isHidden = false; //不可选属性
			item.files = [];
			item.inputNum = undefined;
			item.imageJson = parseJSON(item.imageJson);
			if (item.childList && item.childList.length) {
				handle(item.childList);
			}
			item.customStepName = getMyCustomStepName(item);
			//特殊处理
			if (item.paramName === "Upload Artwork & Comments") {
				item.alias = "Upload Your Artwork";
			}
		}
	};
	handle(data);
	return data;
};
export default {
	props: {
		cateId: {
			type: [String, Number],
			required: true,
		},
		pid: {
			type: [String, Number],
			required: true,
		},
		showQuickStep: {
			type: Boolean,
		},
		configStyle: {
			type: Object,
			default: () => ({}),
		},
	},
	data() {
        const config = getQuickQuoteConfigByPidAndCateId.call(this,this.pid, this.cateId);
		return {
            showSmallPrice: false,
			swiperOption: {
				slidesPerView: 4,
				breakpoints: {
					1000: {
						slidesPerView: 4,
					},
					300: {
						slidesPerView: 2,
					},
				},
			},
			templateParams: {},
			showTemplateDialog: false,
			maxStep: 2,
			allStepConfig: {
				size: {
					showStep: 1,
				},
				qty: {
					showStep: 1,
				},
				plating: {
					showStep: 2,
				},
				color: {
					showStep: 2,
				},
				foldType: {
					showStep: 2,
				},
				attachment: {
					showStep: 2,
				},
				upload: {
					showStep: 2,
				},
				discount: {
					showStep: 2,
				},
			},
			sizeDialog2: false,
			sizeDialog: false,
			acceptFileType,
			isMixed: false,
			finishedDialogTitle: "",
			showFinishedDialog: false,
			finishedList: [],
			showExtend: false,
			uploadArtworkList: [],
			customNumber: "",
			selectedParamsValueParent: {},
			selectedParamsValue: {},
			isCustom: false,
			isDs: 0,
			isFastQuote: 1,
			loadAddCart: false,
			debounceCalcPrice: null,
			stepName: "",
			cateInfo: {},
			generalData: [],
			isLater: false,
			qtyList: [],
			priceInfo: {
				isSmallWeight: 1,
			},
			selectedData: {},
			selectedQtyInd: -1,
			currentStep: 1,
			...config,
		};
	},
	watch: {
		selectedData: {
			handler() {
				this.debounceCalcPrice();
			},
			deep: true,
		},
	},
	components: {
		KeychainsPj,
		PriceText,
		BaseDialog,
		DiscountText,
		SizeDialog,
		FreeTip,
		FreeTip2,
		StepQty,
		StepSize,
		StepTitle,
		CustomCircle,
		CCYRate,
	},
	computed: {
		finalPrice() {
			return this.priceInfo.totalPrice;
		},
		originPrice() {
			let priceInfo = this.priceInfo;
			return priceInfo.foundationUnitPrice * priceInfo.totalQuantity + priceInfo.toolingCharge;
		},
		customNumberPrice() {
			return this.isCustom && this.customNumber ? this.priceInfo.totalPrice : 0;
		},
		customNumberUnitPrice() {
			return this.isCustom && this.customNumber ? this.priceInfo.foundationUnitPrice : 0;
		},
		discountPrice() {
			return this.priceInfo ? `${Math.abs(this.priceInfo.totalPrice - (this.priceInfo.foundationUnitPrice * this.priceInfo.totalQuantity + this.priceInfo.toolingCharge))}` : `0`;
		},
		sizeValue() {
			let findSize = this.generalData.find((item) => {
				return item.paramType === "SIZE";
			});
			if (!findSize) {
				return "";
			}
			return this.selectedData[findSize.paramName] && this.selectedData[findSize.paramName][0]?.paramCode;
		},
		proId() {
			if (this.previewMode) {
				return 148;
			} else {
				return this.$store.state.proId;
			}
		},
		projectName() {
			if (this.previewMode) {
				return "o2o-site-style";
			} else {
				return this.$store.state.proName;
			}
		},
		previewMode() {
			return process.env.VUE_APP_MODE === "Preview";
		},
		proType() {
			return this.$store.state.proType;
		},
		lang() {
			return this.$store.getters.lang.quote || {};
		},
		langSemi() {
			return this.$store.getters.lang.semiCustom || {};
		},
		userUUID() {
			return this.$store.state.userUUID;
		},
		userId() {
			return this.$store.state.userInfo?.id;
		},
		isLogin() {
			return this.$store.getters.isLogin;
		},
		userInfo() {
			return this.$store.state.userInfo;
		},
		device() {
			return this.$store.state.device;
		},
		customQty() {
			if (this.isCustom) {
				return parseInt(this.customNumber) || 0;
			} else {
				return parseInt((this.qtyList && this.qtyList.length && this.qtyList[this.selectedQtyInd]?.totalQuantity) || 0);
			}
		},
		nextText() {
			return this.configStyle.nextText ? this.lang.bannerQuote[this.configStyle.nextText] : this.lang.bannerQuote.next;
		},
	},
	methods: {
		seeMore(item) {
			this.templateParams = item;
			this.showTemplateDialog = true;
		},
		showFn(item, type) {
			return item.customStepName === type && this.currentStep === this.allStepConfig[item.customStepName]?.showStep;
		},
		previewImg(img) {
			console.log(img);
			if (isImageType(img)) {
				this.$viewerApi({
					images: [img],
				});
			} else {
				window.open(img);
			}
		},
		showSizeDialog(bool) {
			if (this.pid === 692) {
				this.sizeDialog2 = bool;
			} else {
				this.sizeDialog = bool;
			}
		},
		getTitle(item) {
			if (item.customStepName === "upload") {
				return item.alias;
			} else {
				return this.lang.Select + " " + item.alias;
			}
		},
		getStep(item) {
			return this.lang.step + " " + item.customIndex;
		},
		clearField(e = "DISCOUNT") {
			let findDiscount = this.generalData.find((item) => item.paramType === e);
			if (findDiscount) {
				let name = findDiscount.paramName;
				this.selectedData[name] = [];
			}
		},
		getNewDiscountList(itemData) {
			let result = getQuoteTime(itemData.childList, this.priceInfo, this.proType),
				originShowSmallPrice = this.showSmallPrice;
            this.showSmallPrice = result.newShowSmallPrice
			if (result.newShowSmallPrice !== originShowSmallPrice) {
				this.clearField("DISCOUNT");
			}
			return result.arr;
		},
		openUpload() {
			this.$refs.upload[0].click();
		},
		deleteUpload(ind) {
			this.uploadArtworkList.splice(ind, 1);
			if (this.uploadArtworkList.length <= 1) {
				this.showExtend = false;
			}
		},
		prevStep() {
			this.currentStep -= 1;
			this.$emit("update:showQuickStep", false);
		},
		nextStep() {
			this.currentStep += 1;
			this.$emit("update:showQuickStep", true);
		},
		handleWeightDiscount() {
			//获取算价格参数
			let priceParam = this.getPriceParam();
			//获取折扣参数列表
			let discountList = this.generalData
				.find((item) => {
					return item.paramType === "DISCOUNT";
				})
				?.childList.filter((item) => item.priceInfo.priceType === 10);
			if (!discountList?.length) {
				return;
			}
			discountList.forEach((item) => {
				if (priceParam.sizeId && priceParam.cateId) {
					if (priceParam.sizeId && priceParam.cateId && (priceParam.quantity || priceParam.qtyDetailDTOS?.length)) {
						calculate(Object.assign({}, priceParam, { discountId: item.priceInfo.id })).then((res) => {
							this.$set(item.priceInfo, "customWeightPrice", res.data.discountPrice);
						});
					}
				}
			});
		},
		selectQtyList() {
			this.debounceCalcPrice();
			this.handleWeightDiscount();
		},
		filterCustomNumber() {
			this.debounceCalcPrice();
			this.handleWeightDiscount();
		},
		//判断是否选中
		hasId(id, arr) {
			if (!arr) {
				return false;
			}
			return !!arr.find((item) => {
				return item.id === id;
			});
		},
		//参数选中事件
		async selectQuoteParams(item, citem) {
			this.selectedParamsValueParent = item;
			this.selectedParamsValue = citem;
			this.$set(this.selectedData, item.paramName, [citem]);
			if (item.customStepName === "color") {
				if (citem.paramName === "Mixed") {
					let findItem = item.childList.find((item) => item.paramName === "Mixed");
					this.finishedDialogTitle = findItem.alias;
					this.showFinishedDialog = true;
					this.finishedList = item.childList;
					this.isMixed = true;
				} else {
					this.isMixed = false;
				}
			}
            this.deleteErrorTipsById(document.querySelector(`#${item.customStepName+'_'+item.id}`))
			//如果是重量相关参数
			this.handleWeightDiscount();
		},
		replayUpload() {
			this.openUpload();
			this.$store.commit("setSizeDialog", false);
		},
        deleteErrorTipsById(dom){
            if (dom.querySelector(".quickErrorTips")) {
                dom.querySelector(".quickErrorTips").remove();
            }
        },
		uploadPic(event, type = "upload",item) {
			this.$gl.show();
			let files = type === "upload" ? event.target.files : event;
			let fileResult = checkFile(Array.from(files), this.acceptFileType, 80);
			if (!fileResult) {
				this.$toast.error("File type error");
				this.$gl.hide();
				return false;
			}
			if (!fileResult.nomalSize.length) {
				this.$gl.hide();
				this.$store.commit("setSizeDialog", true);
				this.$store.commit("setInputRefName", "replayUpload");
				this.$store.commit("setOverSizeList", fileResult.overSize);
				this.$refs.upload[0].value = "";
				return false;
			}
			let uploadPromises = [];
			fileResult.nomalSize.forEach((file) => {
				let promise = uploadFile(file).then((res) => {
					this.uploadArtworkList.push({
						original_filename: file.name,
						secure_url: res,
						size: (file.size / 1024).toFixed(1),
					});
				});
				uploadPromises.push(promise);
			});
			Promise.all(uploadPromises).then(() => {
				this.$refs.upload[0].value = "";
				if (fileResult.overSize.length > 0) {
					this.$store.commit("setSizeDialog", true);
					this.$store.commit("setInputRefName", "replayUpload");
					this.$store.commit("setOverSizeList", fileResult.overSize);
				}
                this.deleteErrorTipsById(document.querySelector(`#${item.customStepName+'_'+item.id}`))
				this.$gl.hide();
			});
		},
		getQuoteParam(type = "inquiry") {
			let selectedData = deepClone(this.selectedData),
				finaData = [],
				generalData = deepClone(this.generalData);
			for (let i = 0; i < generalData.length; i++) {
				let item = generalData[i];
				if (item.customStepName === "qty") {
					continue;
				}
				if (item.customStepName === "upload") {
					item.childList = [];
					item.later = this.isLater;
					finaData.push(item);
					continue;
				}
				//将选中参数里面的childlist置空
				if (selectedData[item.paramName] && selectedData[item.paramName].length) {
					selectedData[item.paramName].forEach((c) => {
						c.childList = [];
						c.quantity = c.inputNum;
						if (!c.giftQuantity) {
							c.giftQuantity = 0;
						}
					});
				}
				item.childList = selectedData[item.paramName] || [];
				finaData.push(item);
			}
			let files = [];
			let artworkList = this.uploadArtworkList;
			if (artworkList.length > 0) {
				artworkList.forEach((item) => {
					item.secure_url && files.push(item.secure_url);
				});
			}
			return {
				classificationData: this.cateInfo,
				finaData: finaData,
				fontData: {
					fontImgCustom: files,
					comments: this.remark,
				},
				designCanvas: [],
			};
		},
		getPriceParam(qty) {
			const data = {
				cateId: this.cateId,
				discountId: "",
				packingIdList: [],
				paramIdList: [],
				projectName: this.projectName,
				quantity: "",
				sizeId: "",
				upgradesQtyDTO: [],
				qtyDetailDTOS: [],
			};
			let generalData = this.generalData,
				selectedData = this.selectedData;
			for (let i = 0; i < generalData.length; i++) {
				let item = generalData[i];
				let paramName = item.paramName;
				let selectedVal = selectedData[paramName];
				if (item.customStepName === "qty") {
					data.quantity = qty || Number(this.customQty);
					continue;
				}
				if (item.customStepName === "upload") {
					continue;
				}
				if (!selectedVal) {
					continue;
				}
				selectedVal.forEach((citem) => {
					let paramType = citem.paramType;
					switch (paramType) {
						case "NORMAL":
							data.paramIdList.push(citem?.priceInfo?.id);
							break;
						case "DISCOUNT":
							data.discountId = citem?.priceInfo?.id;
							break;
						case "SIZE":
							data.sizeId = citem?.priceInfo?.id;
							break;
						default:
							break;
					}
				});
			}
			if (!data.qtyDetailDTOS.length) {
				delete data.qtyDetailDTOS;
			}
			return data;
		},
		calcPrice() {
			let priceParam = this.getPriceParam();
			if (priceParam.sizeId && priceParam.cateId) {
				calculateAll(priceParam).then((res) => {
					let qtyList = res.data.filter((item) => item.isFastQuote);
					let prevFoundationUnitPrice = qtyList[0]?.foundationUnitPrice;
					qtyList.forEach((item, index) => {
						if (index > 0) {
							item.save = (((prevFoundationUnitPrice - item.foundationUnitPrice) / prevFoundationUnitPrice) * 100).toFixed(0);
						}
					});
					this.qtyList = qtyList;
				});
			}
			if (priceParam.sizeId && priceParam.cateId && (priceParam.quantity || priceParam.qtyDetailDTOS?.length)) {
				calculate(priceParam).then((res) => {
					this.priceInfo = res.data;
				});
			}
		},
		checkParams() {
			let verify = true,
				stepName = "",
				selectedData = this.selectedData,
				generateData = this.generalData;
			for (let i = 0; i < generateData.length; i++) {
				let item = generateData[i];
				//参数名称
				let paramName = item.paramName;
				//选中的参数值
				let selectedVal = selectedData[paramName];
				if (item.customStepName === "upload") {
					if (!this.uploadArtworkList.length && !this.isLater) {
						stepName = item.customStepName + "_" + item.id;
						verify = false;
						break;
					}
				} else if (item.customStepName === "qty") {
					if (!this.customQty || this.customQty <= 0) {
						stepName = item.customStepName + "_" + item.id;
						verify = false;
						break;
					}
				} else if (item.customStepName === "color") {
					let temp = selectedVal;
					if (!temp || !temp.length) {
						stepName = item.customStepName + "_" + item.id;
						verify = false;
						break;
					}
					if (temp[0].paramName === "Mixed") {
						let qty = 0,
							totalQty = this.customQty;
						let findColor = generateData.find((item) => item.paramType === "COLOR");
						findColor.childList.forEach((item) => {
							qty += parseInt(item.inputNum) || 0;
						});
						if (qty !== totalQty) {
							this.$toast.error(this.lang.qtyTip);
							stepName = item.customStepName + "_" + item.id;
							verify = false;
							//显示数量弹窗
							let findItem = findColor.childList.find((item) => item.paramName === "Mixed");
							this.finishedDialogTitle = findItem.alias;
							this.showFinishedDialog = true;
							this.finishedList = findColor.childList;
							break;
						}
					}
				} else {
					if (!selectedVal || selectedVal.length === 0) {
						stepName = item.customStepName + "_" + item.id;
						verify = false;
						break;
					}
				}
			}
			this.stepName = stepName;
			return verify;
		},
		async addCart() {
			if (!this.checkParams()) {
				const errorStepDom = document.querySelector(`#${this.stepName}`);
				if (!errorStepDom.querySelector(".quickErrorTips")) {
					const newDivHTML = `
                        <div class="quickErrorTips" style="color:red;margin-top: 0.5em;text-align: left">
                           ${this.lang.paramTip}
                        </div>
                    `;
					document.querySelector(`#${this.stepName}`).insertAdjacentHTML("beforeend", newDivHTML);
				}
				return false;
			}
			let quickErrorTipsArr = Array.from(document.querySelectorAll(".quickErrorTips"));
			if (quickErrorTipsArr.length) {
				quickErrorTipsArr.forEach((item) => {
					item.remove();
				});
			}
			//如果是临时项目
			if (this.$store.state.isTemporary) {
				this.$store.commit("setMask", "temporary");
				return false;
			}
			if (this.loadAddCart) {
				return false;
			}
			this.loadAddCart = true;
			let quoteParam = this.getQuoteParam("cart");
			let priceParam = this.getPriceParam();
			let data = {
				proId: this.proId,
				uuid: this.isLogin ? null : this.userUUID,
				userId: this.isLogin ? this.userId : null,
				buyType: 7,
				quoteCateId: this.pid,
				isMobile: this.device === "mb" ? 1 : 0,
				quoteCateChildId: priceParam.cateId,
				quantity: this.customQty,
				quotePriceParam: JSON.stringify(priceParam),
				quoteParam: JSON.stringify(quoteParam),
				isDs: this.isDs,
				isFastQuote: this.isFastQuote,
				isSmallQty: getIsSmallQty(findSelectDiscount(this.selectedData), this.$store.state.enableTurnaroundTimeCheck),
			};
			otoAddCart(data)
				.then((res) => {
					this.$toast.success(res.message);
					this.$router.push({
						path: "/cart",
					});
				})
				.finally(() => {
					setTimeout(() => {
						this.loadAddCart = false;
					}, 1000);
				});
		},
		goPage() {
			this.$router.push({
				path: "/quote/custom-printed-care-labels",
			});
		},
		//价格分层参数
		getPriceData() {
			getPriceData({ buyType: 7, productCateId: this.pid }).then((res) => {
				this.$store.commit("setMorePriceData", res.data);
			});
		},
	},
	async created() {
		this.debounceCalcPrice = debounce(this.calcPrice, 300);
	},
	async mounted() {
		if (this.$store.getters.isManage) {
			return false;
		}
		try {
			this.$Bus.$on("replayUpload", this.replayUpload);
			this.getPriceData();
			let result = await Promise.all([
				getInfo({ id: this.cateId }),
				getCateParamRelationByCateId({
					cateId: this.cateId,
					isFastQuote: 1,
				}),
			]);
			this.cateInfo = result[0]?.data || {};
			let data1 = result[1]?.data;
			let selectedData = {},
				isLater = false,
				selectedQtyInd = -1,
				selectedQty = 0,
				generalData = setIndex(sortByKey(addCustomProperty(data1), "fastQuoteStepIndex"));
			//默认选中参数
			if (generalData?.length) {
				isLater = true;
				generalData.forEach((item) => {
					let customStepName = item.customStepName;
					if (customStepName !== "qty" && customStepName !== "upload") {
						let findDefault = item.childList.find((c) => c.priceInfo.isFastQuoteSelected);
						if (findDefault) {
							selectedData[item.paramName] = [findDefault];
							//选中尺寸下的默认数量
							if (customStepName === "size") {
								try {
									let priceInfo = findDefault.priceInfo;
									let increasePrice = JSON.parse(priceInfo.increasePrice).filter((item) => item.isFastQuote);
									let defaultQtyIndex = increasePrice.findIndex((q) => q.isFastQuoteSelected);
									if (defaultQtyIndex > -1) {
										selectedQtyInd = defaultQtyIndex;
										selectedQty = increasePrice[defaultQtyIndex].quantity;
									}
								} catch (e) {}
							}
						}
					}
				});
			}
			this.selectedData = selectedData;
			this.generalData = generalData;
			this.isLater = false;
			this.selectedQtyInd = selectedQtyInd;
			let priceParam = this.getPriceParam(selectedQty),
				priceResult;
			priceResult = await Promise.all([calculateAll(priceParam), calculate(priceParam)]);
			let qtyList = (priceResult && priceResult[0].data.filter((item) => item.isFastQuote)) || [];
			let prevFoundationUnitPrice = qtyList[0]?.foundationUnitPrice;
			qtyList.forEach((item, index) => {
				if (index > 0) {
					item.save = (((prevFoundationUnitPrice - item.foundationUnitPrice) / prevFoundationUnitPrice) * 100).toFixed(0);
				}
			});
			this.qtyList = qtyList;
			this.priceInfo = (priceResult && priceResult[1].data) || {};
			this.handleWeightDiscount();
		} catch (e) {
			console.log(e);
		}
	},
};
</script>

<style lang="scss" scoped>
@mixin selectedStyle {
	border-color: $color-primary;
	&::after {
		content: "\e82c";
		position: absolute;
		width: 1rem;
		height: 1rem;
		line-height: 1rem;
		right: 0;
		top: 0;
		border-bottom-left-radius: 70%;
		text-align: center;
		color: #ffffff;
		background-color: $color-primary;
		font-family: "modalicon";
		font-size: 0.6rem;
	}

	@include respond-to(mb) {
		&::after {
			width: 1.25rem;
			height: 1.25rem;
			line-height: 1.25rem;
		}
	}
}

.icon-wenhao2 {
	font-size: 0.88rem;

	@include respond-to(mb) {
		font-size: 0.91rem;
	}
}

.quoteBoxFooter {
	overflow: hidden;
	display: flex;
	justify-content: space-between;
	align-items: center;
	height: 4.5rem;
	margin: 1em -1em -1em;
	padding: 0 1.88rem 0 2.63rem;
	background-color: #eaeaea;

	::v-deep .finalPrice {
		font-weight: 700;
		color: #d24600;
		font-size: 1.13rem;
	}

	& > div:first-child span {
		margin-right: 2rem;
	}

	.addCartBtn {
		width: 19.38rem;
		height: 3.13rem;
		border-radius: 0.63rem;

		@include respond-to(mb) {
			width: 13rem;
		}
	}

	@include respond-to(mb) {
		& > div span {
			display: block;
		}
		::v-deep .finalPrice {
			font-size: 1rem;
		}
		& > div:first-child span {
			margin-right: 0;
		}
	}
}

.quoteBoxContent {
	display: flex;
	flex-direction: column;
	position: relative;
	min-width: 0;
	width: 47.5rem;
	min-height: 34rem;
	padding: 1em;
	border-radius: 10px;
	background: #fff;
	border: 1px solid #dcdfe6;

	@include respond-to(mb) {
		width: 100%;
		height: 100%;
		min-height: auto;
		padding: 1.2rem;
		background: #ffffff;
		border-radius: 0.42rem;
		border: 1px solid #d9d9d9;
	}

	.return {
		margin-bottom: 0.5em;
		font-weight: 700;
		cursor: pointer;

		b {
			margin-right: 0.5rem;
		}
	}
}

.textTip {
	grid-area: c;
	font-weight: 400;
	font-size: calc(1rem - 1px);
	color: #999999;
	text-align: left;
	text-transform: none;
	white-space: nowrap;
	text-overflow: ellipsis;
	overflow: hidden;
	min-height: 1.2rem;

	.proStyle {
		color: $color-primary;
	}

	.goPage {
		color: #1d7bfd;
		cursor: pointer;
		text-decoration: underline;
	}

	@include respond-to(mb) {
		white-space: normal;
		overflow: initial;
		min-height: auto;
		grid-area: initial;
		font-size: 1rem;
		margin-bottom: 0.5rem;
	}
}

.stepList {
	display: grid;
	grid-template-columns: 1fr 2fr;
	grid-template-areas:
		"c c"
		"a b";
	align-content: flex-start;
	gap: 0.8rem;

	@include respond-to(mb) {
		grid-template-columns: repeat(1, 1fr);
		grid-template-areas: none;
		column-gap: 0;
	}

	.step-item {
		font-size: 0.88rem;

		&.type3 {
			.step-item-params {
				text-align: center;
				grid-template-columns: repeat(3, 1fr);
			}
		}

		@include respond-to(mb) {
			font-size: 1rem;
			margin-bottom: 1rem;
			&.type3 {
				.step-item-params {
					grid-template-columns: repeat(3, 1fr);
				}
			}
		}

		.step-item-title {
			display: flex;
			align-items: center;
			margin-bottom: 0.7rem;
			font-size: 1rem;

			@include respond-to(mb) {
				font-size: 1.17rem;
				margin-bottom: 0.8rem;
			}

			& > span:first-child {
				margin-right: 4px;
				color: $color-primary;
				font-weight: 700;
				text-transform: uppercase;
			}

			& > span:nth-child(2) {
				margin-right: 4px;
			}
		}

		.step-item-params ::v-deep {
			.customCircle {
				position: relative;
				width: 1rem;
				margin-right: 0.63rem;
				@include respond-to(mb) {
					width: 1.33rem;
					margin-right: 0.92rem;
				}

				&::after {
					width: 40%;
				}

				&.isActive {
					background-color: $color-primary;
					border-color: $color-primary;
				}
			}
		}
	}
}

.step-size {
	grid-area: a;

	.step-item-title .help {
		display: none;
	}

	@include respond-to(mb) {
		grid-area: auto;

		.step-item-title .help {
			flex: 1;
			display: block;
			text-align: right;
			font-size: 1rem;
		}
	}
}

.step-qty {
	grid-area: b;

	@include respond-to(mb) {
		grid-area: auto;
        margin-bottom: 0 !important;
	}
}

.step-plating {
	grid-column: 1 / span 2;

	.step-item-params {
		display: grid;
		grid-template-columns: repeat(4, 1fr);
		grid-gap: 0.63rem;

		@include respond-to(mb) {
			grid-template-columns: repeat(2, 1fr);
			grid-gap: 0.42rem;
		}

		.param-item {
			overflow: hidden;
			position: relative;
			min-width: 0;
			display: flex;
			justify-content: center;
			align-items: center;
			padding: 0.2rem;
			background-color: #fafafa;
			cursor: pointer;
			border-radius: 6px;
			border: 1px solid transparent;
			transition: all 0.3s;

			&.more {
				background-color: $color-second;
				color: $color-primary;
			}

			&.active {
				@include selectedStyle;

				.edit {
					display: block;
				}
			}

			@media (any-hover: hover) {
				b:hover {
					color: $color-primary;
				}
				&:hover {
					border-color: $color-primary;
				}
			}

			img {
				width: 3.75rem;
				object-fit: contain;
				margin: 0 0.6rem;

				@include respond-to(mb) {
					width: 4.25rem;
				}
			}

			.edit {
				position: absolute;
				left: 4px;
				top: 4px;
				display: none;
			}
		}
	}
}

.step-keyChainsAttachment {
	grid-column: 1 / span 2;

	.step-item-params {
		display: grid;
		grid-template-columns: repeat(4, 1fr);
		grid-gap: 0.63rem;

		@include respond-to(mb) {
			grid-template-columns: repeat(2, 1fr);
			grid-gap: 0.42rem;
		}

		.param-item {
			overflow: hidden;
			position: relative;
			min-width: 0;
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			background-color: #fafafa;
			cursor: pointer;
			border-radius: 6px;
			border: 1px solid transparent;
			transition: all 0.3s;

			&.more {
				background-color: $color-second;
				color: $color-primary;
			}

			span {
				padding: 0.5em 0;
				text-align: center;
			}

			&.active {
				@include selectedStyle;

				.edit {
					display: block;
				}
			}

			@media (any-hover: hover) {
				b:hover {
					color: $color-primary;
				}
				&:hover {
					border-color: $color-primary;
				}
			}

			//img {
			//	width: 3.75rem;
			//	object-fit: contain;
			//	margin: 0 0.6rem;
			//
			//	@include respond-to(mb) {
			//		width: 4.25rem;
			//	}
			//}
		}
	}
}

.step-colorAttachment {
	grid-column: 1 / span 2;

	.step-item-params {
		display: grid;
		grid-template-columns: repeat(5, 1fr);
		grid-gap: 0.43rem;

		@include respond-to(mb) {
			grid-template-columns: repeat(2, 1fr);
			grid-gap: 0.42rem;
		}

		.param-item {
			overflow: hidden;
			position: relative;
			min-width: 0;
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			background-color: #fafafa;
			cursor: pointer;
			border-radius: 6px;
			border: 1px solid transparent;
			transition: all 0.3s;

			&.more {
				background-color: $color-second;
				color: $color-primary;
			}

			span {
				padding: 0.5em 0;
				text-align: center;
			}

			&.active {
				@include selectedStyle;

				.edit {
					display: block;
				}
			}

			@media (any-hover: hover) {
				b:hover {
					color: $color-primary;
				}
				&:hover {
					border-color: $color-primary;
				}
			}
		}
	}
}

.step-color {
	grid-column: 1 / span 2;

	.step-item-params {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		grid-gap: 0.63rem;

		.param-item {
			overflow: hidden;
			position: relative;
			min-width: 0;
			display: flex;
			align-items: center;
			justify-content: center;
			gap: 0.8rem;
			padding: 0.2rem;
			background-color: #fafafa;
			cursor: pointer;
			border: 1px solid transparent;
			transition: all 0.3s;
			border-radius: 6px;

			.imgBox {
				width: fit-content;
				border: 1px solid transparent;
				border-radius: 6px;

				&.active {
					@include selectedStyle;

					.edit {
						display: block;
					}
				}
			}

			&.active {
				@include selectedStyle;

				.edit {
					display: block;
				}
			}

			@media (any-hover: hover) {
				b:hover {
					color: $color-primary;
				}
				&:hover {
					border-color: $color-primary;
				}
			}

			img {
				width: fit-content;
				height: 2.25rem;
				object-fit: contain;
				border-radius: 6px;
				@include respond-to(mb) {
				}
			}

			.edit {
				position: absolute;
				left: 4px;
				top: 4px;
				display: none;
			}
		}

		@include respond-to(mb) {
			grid-template-columns: repeat(3, 1fr);
			grid-gap: 0.42rem;
			.param-item {
				background-color: transparent;
				flex-direction: column;
				padding: 0;
				gap: 0.4rem;

				.imgBox {
					padding: 0.2rem 1rem;

					img {
						max-width: 100%;
						height: 2.2rem;
					}

					width: 100%;
					background-color: #fafafa;
				}

				@media (any-hover: hover) {
					b:hover {
						color: transparent;
					}
					&:hover {
						border-color: transparent;
					}
				}
			}
		}
	}
}

.step-fold-type {
	grid-column: 1 / span 2;

	.step-item-params {
		display: grid;
		grid-template-columns: repeat(8, 1fr);
		gap: 0.4rem;

		.param-item {
			overflow: hidden;
			position: relative;
			min-width: 0;
			cursor: pointer;
			border: 1px solid transparent;
			transition: all 0.3s;
			border-radius: 6px;

			.imgBox {
				width: 100%;
				margin-bottom: 0.5rem;
				border: 1px solid transparent;
				border-radius: 6px;

				&.active {
					@include selectedStyle;

					.edit {
						display: block;
					}
				}

				@media (any-hover: hover) {
					b:hover {
						color: $color-primary;
					}
					&:hover {
						border-color: $color-primary;
					}
				}
			}

			span {
				display: block;
				text-align: center;
				font-size: 0.75rem;
				white-space: nowrap;
			}

			img {
				width: 100%;
				object-fit: contain;
				border-radius: 6px;
				@include respond-to(mb) {
				}
			}

			.edit {
				position: absolute;
				left: 4px;
				top: 4px;
				display: none;
			}
		}

		@include respond-to(mb) {
			grid-template-columns: repeat(4, 1fr);
		}
	}
}

.step-ribbon {
	grid-column: 1 / span 2;

	.step-item-params {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		grid-gap: 0.63rem;

		@include respond-to(mb) {
			grid-template-columns: repeat(2, 1fr);
			grid-gap: 0.46rem 0.83rem;
		}

		.param-item {
			overflow: hidden;
			position: relative;
			min-width: 0;
			display: flex;
			justify-content: center;
			align-items: center;
			padding: 0.2rem;
			background-color: #fafafa;
			cursor: pointer;
			border-radius: 6px;
			border: 1px solid transparent;
			transition: all 0.3s;

			@include respond-to(mb) {
				flex-direction: column;
			}

			&.active {
				@include selectedStyle;
			}

			@media (any-hover: hover) {
				b:hover {
					color: $color-primary;
				}
				&:hover {
					border-color: $color-primary;
				}
			}

			span {
				margin-right: 0.5rem;
				text-align: center;
				@include respond-to(mb) {
					order: 2;
				}
			}

			img {
				width: 5.6rem;
				object-fit: contain;
				max-height: 2.5rem;

				@include respond-to(mb) {
					order: 1;
					width: 11rem;
					margin-bottom: 0.5rem;
				}
			}
		}
	}
}

.step-upload {
	grid-column: 1 / span 2;

	.step-item-params {
		position: relative;
		display: flex;
		justify-content: center;
		align-items: center;
		width: 28rem;
		height: 3.13rem;
		background-color: #fafafa;

		@include respond-to(mb) {
			width: 100%;
			height: 5rem;
		}

		input[type="file"] {
			position: absolute;
			clip: rect(0 0 0 0);
		}

		.uploadWrap {
			position: relative;
			display: flex;
			align-items: center;
			cursor: pointer;

			.brow {
				margin: 0 4px;
				color: $color-primary;
				text-decoration-line: underline;
			}

			.uploadIcon {
				margin-right: 0.2rem;
				font-size: 1.7rem;
				color: $color-primary;

				@include respond-to(mb) {
					font-size: 1.5rem;
					margin-right: 0.4rem;
				}
			}
		}

		.param-item {
			flex: 1;
			position: relative;
			display: flex;
			justify-content: center;
			align-items: center;
			height: 100%;
			border-radius: 6px;
			cursor: pointer;
			border: 1px solid #ccc;

			@include respond-to(mb) {
				padding: 0;
			}

			@media (any-hover: hover) {
				b:hover {
					color: $color-primary;
				}

				&:hover {
					border-color: $color-primary;
				}
			}
		}
	}

	.step-item-params.param-item-hasUpload {
		justify-content: space-between;
		padding: 0.2rem;

		.param-item-hasUpload-left {
			position: relative;
			display: flex;
			align-items: center;
			padding: 0.2rem;

			.icon {
				display: flex;
				justify-content: center;
				align-items: center;
				width: 24px;
				height: 24px;
				border-radius: 50%;
				margin: 0 6px;
				background: #ebebeb;

				b {
					color: #888888;
					font-size: 12px;
					transform: rotate(90deg);
				}
			}

			.icon.active {
				b {
					color: $color-primary;
				}
			}

			.extendUpload {
				position: absolute;
				left: 0;
				right: 0;
				top: calc(100% + 10px);
				background: #fafafa;
				box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.2);
				border-radius: 6px;
				border: 1px solid $color-primary;
				padding: 10px 10px 10px 26px;
				z-index: 100;

				.arrow {
					position: absolute;
					display: block;
					transform: translateY(-100%);
					right: 10px;
					top: 0;
					width: 0;
					height: 0;
					border-color: transparent;
					border-style: solid;
					border-width: 6px;
					border-bottom-color: $color-primary;
					border-top-width: 0;

					&::after {
						content: "";
						position: absolute;
						display: block;
						width: 0;
						height: 0;
						border-color: transparent;
						border-style: solid;
						border-width: 5px;
						top: 1px;
						margin-left: -5px;
						border-top-width: 0;
						border-bottom-color: #fff;
					}
				}

				.uploadFileWrap {
					overflow: hidden auto;
					max-height: 140px;

					.upload-item {
						display: flex;
						align-items: center;
						margin-bottom: 10px;
					}

					.upload-item:last-child {
						margin-bottom: 0;
					}
				}
			}

			.upload-name {
				overflow: hidden;
				text-overflow: ellipsis;
				width: 150px;
				white-space: nowrap;
				text-decoration: underline;

				@include respond-to(mb) {
					width: 100px;
				}
			}

			b {
				margin: 0 6px;
				cursor: pointer;
			}

			b.icon-Down {
				font-size: 12px;
			}
		}
	}
}

.step-time {
	grid-column: 1 / span 2;
	min-width: 0;
	text-align: center;

	.box-border {
		display: none;
	}

	.step-item-params {
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		grid-gap: 0.63rem;

		@include respond-to(mb) {
			grid-template-columns: repeat(2, 1fr);
			grid-gap: 0.42rem;
			text-align: left;
		}

		.param-item-wrap {
			overflow: hidden;
			position: relative;
			border: 1px solid transparent;
			border-radius: 6px;
			padding: 0.63rem;
			background-color: #fafafa;
			box-sizing: border-box;
			cursor: pointer;

			&.active {
				@include selectedStyle;
			}

			.param-item {
				position: relative;
				min-width: 0;
				display: flex;
				justify-content: center;
				align-items: center;
				margin-bottom: 0.2rem;
				font-weight: 700;

				@include respond-to(mb) {
					justify-content: flex-start;
				}
			}
		}
	}
}

.nextBtn {
	display: flex;
	align-items: center;
	width: 100%;

	@include respond-to(mb) {
		flex-direction: column;
	}

	button {
		flex: 1;
		height: 3.13rem;
		margin-right: 1rem;
		@include respond-to(mb) {
			flex: auto;
			width: 100%;
			height: 2.5rem;
			margin-right: 0;
		}
	}

	.tip {
		white-space: nowrap;
		padding: 0.5rem;
		font-size: 1rem;
		color: #999999;
		text-align: center;

		&.hidden {
			opacity: 0;
			visibility: hidden;
		}
	}
}

.help {
	display: flex;
	align-items: center;
	font-size: 0.75rem;
	color: $color-primary;

	b {
		margin-left: 4px;
		font-size: 0.875rem;
		@include respond-to(mb) {
			font-size: 1rem;
		}
	}

	@include respond-to(mb) {
		display: none;
	}
}

button[disabled] {
	background: #e2e2e2;
}

.golfSizePreviewDialog {
	.body {
		padding: 2em;
	}
}
</style>
