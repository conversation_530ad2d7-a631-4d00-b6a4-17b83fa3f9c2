class quoteData {
    constructor(data) {
        let defaultData = {
            stepIndex: 1, //参数步骤排序
            childList: [],
            paramName: "quoteCategory", //参数名称
            alias: "test", //参数别名
            type: "quoteCategory", //类型
            paramType: "quoteCategory", //参数类型
        };
        Object.assign(this, defaultData, data);
    }
}

export const getQuoteStyleData = function (pid) {
	// 定义默认配置
	const DEFAULT_CONFIG = {
		stepIndex: 1,
		noShowDetail: false
	};
	// 创建配置映射表
	const PID_CONFIG_MAP = {
		"573":{
			...DEFAULT_CONFIG,
			stepIndex: 0,
			alias: this.lang.Patches.paramName1,
		},
        "25":{
			...DEFAULT_CONFIG,
            alias: this.lang.pins.paramName,
		},
        "201":{
			...DEFAULT_CONFIG,
            alias: this.lang.ornament.paramName,
		},
        "53":{
			...DEFAULT_CONFIG,
            alias: this.lang.beltBuckles.paramName,
		},
        "40":{
			...DEFAULT_CONFIG,
            alias: this.lang.coins.paramName,
		},
        "323":{
            ...DEFAULT_CONFIG,
            alias: this.lang.coins.paramName,
        },
        "324":{
            ...DEFAULT_CONFIG,
            alias: this.lang.coins.paramName,
        },
		"51":{
			...DEFAULT_CONFIG,
            alias: this.lang.tradingpins.paramName,
		},
        "45":{
			...DEFAULT_CONFIG,
            alias: this.lang.medals.paramName,
		},
		"121":{
			...DEFAULT_CONFIG,
            stepIndex: 0,
            alias: this.lang.n3DembroideredPatches.paramName,
            noShowDetail: true,
		},
		"117":{
			...DEFAULT_CONFIG,
            stepIndex: 0,
            alias: this.lang.AcrylicKeyChains.paramName,
            noShowDetail: true,
		},
		"112":{
			...DEFAULT_CONFIG,
            alias: this.lang.metalkeychains.paramName,
		},
        "399":{
            ...DEFAULT_CONFIG,
            alias: this.lang.MetalPrintingKeyChains.paramName,
            noShowDetail: true,
        },
        "118":{
            ...DEFAULT_CONFIG,
            alias: this.lang.PVCKeyChains.paramName,
            noShowDetail: true,
        },
        "110":{
            ...DEFAULT_CONFIG,
            alias: this.lang.chenillePatches.paramName,
            noShowDetail: true,
        },
        "568":{
            ...DEFAULT_CONFIG,
            alias: this.lang.TatamiFabricSiliconePatches.paramName,
            noShowDetail: true,
        },
        "107":{
            ...DEFAULT_CONFIG,
            alias: this.lang.embroideredPatches.paramName,
        },
        "122":{
            ...DEFAULT_CONFIG,
            stepIndex: 0,
            alias: this.lang.flexPatches.paramName,
            noShowDetail: true,
        },
        "111":{
            ...DEFAULT_CONFIG,
            stepIndex: 0,
            alias: this.lang.leatherPatches.paramName,
            noShowDetail: true,
        },
        "108":{
            ...DEFAULT_CONFIG,
            stepIndex: 0,
            alias: this.lang.denimPatches.paramName,
            noShowDetail: true,
        },
        "398":{
            ...DEFAULT_CONFIG,
            stepIndex: 0,
            alias: this.lang.Patches.paramName,
            noShowDetail: true,
        },
        "407":{
            ...DEFAULT_CONFIG,
            alias: this.lang.coins.paramName,
        },
        "408":{
            ...DEFAULT_CONFIG,
            alias: this.lang.coins.paramName,
        },
        "409":{
            ...DEFAULT_CONFIG,
            alias: this.lang.coins.paramName,
        },
        "109":{
            ...DEFAULT_CONFIG,
            stepIndex: 0,
            alias: this.lang.Patches.paramName,
            noShowDetail: true,
        },
        "160":{
            ...DEFAULT_CONFIG,
            stepIndex: 0,
            alias: this.lang.buttonBadges.paramName,
        },
        "159":{
            ...DEFAULT_CONFIG,
            stepIndex: 0,
            alias: this.lang.embroideredBadges.paramName,
        },
        "158":{
            ...DEFAULT_CONFIG,
            stepIndex: 0,
            alias: this.lang.pinBadges.paramName,
        },
        "55":{
            ...DEFAULT_CONFIG,
            alias: this.lang.PVCpatches.paramName,
        },
        "219":{
            ...DEFAULT_CONFIG,
            stepIndex: 0,
            alias: this.lang.embroideredKeychains.paramName,
        },
        "227":{
            ...DEFAULT_CONFIG,
            stepIndex: 0,
            alias: this.lang.fidgetSpinner.paramName,
            noShowDetail: true,
        },

        "229":{
            ...DEFAULT_CONFIG,
            stepIndex: 0,
            alias: this.lang.fidgetSpinner.paramName,
            noShowDetail: true,
        },
        "231":{
            ...DEFAULT_CONFIG,
            stepIndex: 0,
            alias: this.lang.fidgetSpinner.paramName,
            noShowDetail: true,
        },
        "233":{
            ...DEFAULT_CONFIG,
            stepIndex: 0,
            alias: this.lang.fidgetSpinner.paramName,
            noShowDetail: true,
        },
        "235":{
            ...DEFAULT_CONFIG,
            stepIndex: 0,
            alias: this.lang.fidgetSpinner.paramName,
            noShowDetail: true,
        },
        "237":{
            ...DEFAULT_CONFIG,
            stepIndex: 0,
            alias: this.lang.fidgetSpinner.paramName,
            noShowDetail: true,
        },
        "240":{
            ...DEFAULT_CONFIG,
            stepIndex: 0,
            alias: this.lang.fidgetSpinner.paramName,
            noShowDetail: true,
        },
        "763":{
            ...DEFAULT_CONFIG,
            stepIndex: -1,
            alias: this.lang.cufflinks.paramName,
            noShowDetail: true,
        },
        "764":{
            ...DEFAULT_CONFIG,
            stepIndex: -1,
            alias: this.lang.cufflinks.paramName,
            noShowDetail: true,
        },
        "762":{
            ...DEFAULT_CONFIG,
            stepIndex: -1,
            alias: this.lang.cufflinks.paramName,
            noShowDetail: true,
        },
        "759":{
            ...DEFAULT_CONFIG,
            stepIndex: -1,
            alias: this.lang.cufflinks.paramName,
            noShowDetail: true,
        },
        "761":{
            ...DEFAULT_CONFIG,
            stepIndex: -1,
            alias: this.lang.cufflinks.paramName,
            noShowDetail: true,
        },
        "760":{
            ...DEFAULT_CONFIG,
            stepIndex: -1,
            alias: this.lang.cufflinks.paramName,
            noShowDetail: true,
        },
        "261":{
            ...DEFAULT_CONFIG,
            stepIndex: 0,
            alias: this.lang.fidgetSpinner.paramName,
            noShowDetail: true,
        },
        "273":{
            ...DEFAULT_CONFIG,
            stepIndex: 0,
            alias: this.lang.fidgetSpinner.paramName,
            noShowDetail: true,
        },
        "275":{
            ...DEFAULT_CONFIG,
            stepIndex: 0,
            alias: this.lang.pins.paramName
        },
        "286":{
            ...DEFAULT_CONFIG,
            stepIndex: 0,
            alias: this.lang.fidgetSpinner.paramName,
            noShowDetail: true,
        },
        "295":{
            ...DEFAULT_CONFIG,
            stepIndex: 0,
            alias: this.lang.fidgetSpinner.paramName,
            noShowDetail: true,
        },
        "298":{
            ...DEFAULT_CONFIG,
            stepIndex: 0,
            alias: this.lang.fidgetSpinner.paramName,
            noShowDetail: true,
        },
        "300":{
            ...DEFAULT_CONFIG,
            alias: this.lang.metalBusinessCards.paramName,
        },
        "671":{
            ...DEFAULT_CONFIG,
            stepIndex: 0,
            alias: this.lang.metalBusinessCards.paramName,
            noShowDetail: true,
        },
        "310":{
            ...DEFAULT_CONFIG,
            stepIndex: 0,
            alias: this.lang.fidgetSpinner.paramName,
            noShowDetail: true,
        },
        "312":{
            ...DEFAULT_CONFIG,
            stepIndex: 0,
            alias: this.lang.fidgetSpinner.paramName,
            noShowDetail: true,
        },
        "330":{
            ...DEFAULT_CONFIG,
            stepIndex: 0,
            alias: this.lang.fidgetSpinner.paramName,
            noShowDetail: true,
        },
        "205":{
            ...DEFAULT_CONFIG,
            stepIndex: 0,
            alias: this.lang.fidgetSpinner.paramName,
            noShowDetail: true,
        },
        "433":{
            ...DEFAULT_CONFIG,
            alias: this.lang.medals.paramName,
            noShowDetail: true,
        },
        "444":{
            ...DEFAULT_CONFIG,
            stepIndex: 0,
            alias: this.lang.denimPatches.paramName,
            noShowDetail: true,
        },
        "459":{
            ...DEFAULT_CONFIG,
            alias: this.lang.care.paramName,
        },
        "472":{
            ...DEFAULT_CONFIG,
            alias: this.lang.coins.paramName,
        },
        "458":{
            ...DEFAULT_CONFIG,
            stepIndex: 1,
            alias: this.lang.heatTransferPatches.paramName,
        },
        "478":{
            ...DEFAULT_CONFIG,
            stepIndex: 0,
            alias: this.lang.siliconePatches.paramName,
        },
        "479":{
            ...DEFAULT_CONFIG,
            alias: this.lang.pins.paramName,
        },
        "489":{
            ...DEFAULT_CONFIG,
            noShowDetail: true,
            alias: this.lang.pins.paramName,
        },
        "535":{
            ...DEFAULT_CONFIG,
            alias: this.lang.pins.paramName,
        },
        "536":{
            ...DEFAULT_CONFIG,
            stepIndex: 0,
            noShowDetail: true,
            alias: this.lang.pins.paramName,
        },
        "490":{
            ...DEFAULT_CONFIG,
            noShowDetail: true,
            alias: this.lang.pins.paramName,
        },
        "491":{
            ...DEFAULT_CONFIG,
            noShowDetail: true,
            alias: this.lang.pins.paramName,
        },
        "537":{
            ...DEFAULT_CONFIG,
            noShowDetail: true,
            alias: this.lang.pins.paramName,
        },
        "460":{
            ...DEFAULT_CONFIG,
            noShowDetail: true,
            alias: this.lang.pins.paramName,
        },
        "495":{
            ...DEFAULT_CONFIG,
            alias: this.lang.pinBadges.paramName,
        },
        "502":{
            ...DEFAULT_CONFIG,
            noShowDetail: true,
            alias: this.lang.pinBadges.paramName,
        },
        "543":{
            ...DEFAULT_CONFIG,
            alias: this.lang.pinBadges.paramName,
        },
        "506":{
            ...DEFAULT_CONFIG,
            stepIndex: 0,
            noShowDetail: true,
            alias: this.lang.pinBadges.paramName,
        },
        "503":{
            ...DEFAULT_CONFIG,
            noShowDetail: true,
            alias: this.lang.pinBadges.paramName,
        },
        "504":{
            ...DEFAULT_CONFIG,
            noShowDetail: true,
            alias: this.lang.pinBadges.paramName,
        },
        "633":{
            ...DEFAULT_CONFIG,
            noShowDetail: true,
            alias: this.lang.pinBadges.paramName,
        },
        "631":{
            ...DEFAULT_CONFIG,
            noShowDetail: true,
            alias: this.lang.pinBadges.paramName,
        },
        "583":{
            ...DEFAULT_CONFIG,
            noShowDetail: true,
            alias: this.lang.pinBadges.paramName,
        },
        "581":{
            ...DEFAULT_CONFIG,
            noShowDetail: true, //报价没有父类
            alias: this.lang.pinBadges.paramName,
        },
        "561":{
            ...DEFAULT_CONFIG,
            alias: this.lang.ballMarker.paramName,
        },
        "638":{
            ...DEFAULT_CONFIG,
            alias: this.lang.ballMarker.paramName,
        },
        "644":{
            ...DEFAULT_CONFIG,
            alias: this.lang.ballMarker.paramName,
        },
        "650":{
            ...DEFAULT_CONFIG,
            alias: this.lang.ballMarker.paramName,
        },
        "661":{
            ...DEFAULT_CONFIG,
            stepIndex: 0,
            noShowDetail: true,
            alias: this.lang.pinBadges.paramName,
        },
        "557":{
            ...DEFAULT_CONFIG,
            stepIndex: 0,
            noShowDetail: true,
            alias: this.lang.ballMarker.paramName,
        },
        "619":{
            ...DEFAULT_CONFIG,
            stepIndex: 0,
            noShowDetail: true,
            alias: this.lang.lanyard.LanyardStyle,
        },
        "635":{
            ...DEFAULT_CONFIG,
            stepIndex: 0,
            noShowDetail: true,
            alias: this.lang.lanyard.LanyardStyle,
        },
        "629":{
            ...DEFAULT_CONFIG,
            stepIndex: 0,
            noShowDetail: true,
            alias: this.lang.lanyard.LanyardStyle,
        },
        "695":{
            ...DEFAULT_CONFIG,
            stepIndex: 0,
            noShowDetail: true,
            alias: this.lang.lanyard.LanyardStyle,
        },
        "700":{
            ...DEFAULT_CONFIG,
            stepIndex: 0,
            noShowDetail: true,
            alias: this.lang.lanyard.LanyardStyle,
        },
        "701":{
            ...DEFAULT_CONFIG,
            stepIndex: 0,
            noShowDetail: true,
            alias: this.lang.lanyard.LanyardStyle,
        },
        "702":{
            ...DEFAULT_CONFIG,
            stepIndex: 0,
            noShowDetail: true,
            alias: this.lang.lanyard.LanyardStyle,
        },
        "703":{
            ...DEFAULT_CONFIG,
            stepIndex: 0,
            noShowDetail: true,
            alias: this.lang.lanyard.LanyardStyle,
        },
        "704":{
            ...DEFAULT_CONFIG,
            stepIndex: 0,
            noShowDetail: true,
            alias: this.lang.lanyard.LanyardStyle,
        },
        "705":{
            ...DEFAULT_CONFIG,
            stepIndex: 0,
            noShowDetail: true,
            alias: this.lang.lanyard.LanyardStyle,
        },
        "597":{
            ...DEFAULT_CONFIG,
            stepIndex: 0,
            noShowDetail: true,
            alias: this.lang.lanyard.LanyardStyle,
        },
        "621":{
            ...DEFAULT_CONFIG,
            stepIndex: 0,
            noShowDetail: true,
            alias: this.lang.lanyard.LanyardStyle,
        },
        "602":{
            ...DEFAULT_CONFIG,
            stepIndex: 0,
            noShowDetail: true,
            alias: this.lang.lanyard.LanyardStyle,
        },
        "747":{
            ...DEFAULT_CONFIG,
            stepIndex: 0,
            noShowDetail: true,
            alias: this.lang.lanyard.LanyardStyle,
        },
        "599":{
            ...DEFAULT_CONFIG,
            stepIndex: 0,
            noShowDetail: true,
        },
        "600":{
            ...DEFAULT_CONFIG,
            stepIndex: 0,
            noShowDetail: true,
        },
        "601":{
            ...DEFAULT_CONFIG,
            stepIndex: 0,
            noShowDetail: true,
        },
        "689":{
            ...DEFAULT_CONFIG,
            stepIndex: 0,
            noShowDetail: true,
            alias: this.lang.lanyard.LanyardStyle,
        },
        "659":{
            ...DEFAULT_CONFIG,
            stepIndex: 0,
            alias: this.lang.Color,
        },
        "656":{
            ...DEFAULT_CONFIG,
            stepIndex: 0,
            noShowDetail: true,
        },
        "526":{
            ...DEFAULT_CONFIG,
            stepIndex: 0,
            alias: this.lang.luggage.step1,
        },
        "625":{
            ...DEFAULT_CONFIG,
            stepIndex: 0,
            alias: this.lang.Color,
        },
        "749":{
            ...DEFAULT_CONFIG,
            stepIndex: 0,
            noShowDetail: true,
            alias: this.lang.wristband.WristbandStyle,
        }
	}
    return new quoteData(PID_CONFIG_MAP[pid]);
};
