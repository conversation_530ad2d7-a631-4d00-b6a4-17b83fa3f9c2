<script>
export default {
	inject: ["getUnitPriceStep", "getCustomPriceData", "getProductInfo"],
	props: {
		productInfo: {
			type: Object,
			required: true,
		},
		hideBack: {
			type: Boolean,
			default: false,
		},
		showPriceTable: {
			type: Boolean,
			default: false,
		}
	},
	data() {
		return {
			cursor: false,
			selectItem: null,
			showScrollBtn: false,
		}
	},
	computed: {
		langSemiCustom() {
			return this.$store.getters.lang?.semiCustom;
		},
		unitPriceStep() {
			return this.getUnitPriceStep();
		},
		customPriceData() {
			return this.getCustomPriceData();
		},
		qtyPrice() {
			if (!this.selectItem) {
				return [];
			}
			try {
				return this.setPrice(this.productInfo.discount);
			} catch (e) {
				return [];
			}
		},
		isMobile() {
			return this.$store.state.device === "mb";
		},
	},
	methods: {
		goCollection(data) {
			this.$emit("goCollection", data);
		},
		back() {
			this.$emit("back");
		},
		setPrice(discount = 0) {
			let increasePrice = JSON.parse(this.unitPriceStep.increasePrice),
				itemIncreasePrice = JSON.parse(this.selectItem.increasePrice);
			if (this.selectItem.priceType === 5) {
				increasePrice.forEach((item) => {
					//单价
					let price = parseFloat(item.unitPrice) + parseFloat(this.findPrice(item, itemIncreasePrice).unitPrice);
					//计算价格分层
					let price2 = (price * this.customPriceData.multiBasicUnitPrice).toFixed(2);
					//计算折扣
					let price3 = Math.round(price2 * Math.abs(1 - discount) * 100) / 100;
					if (discount != 0) {
						item.discountPrice = price3.toFixed(2);
						item.unitPrice = price2;
					} else {
						item.unitPrice = price2;
					}
				});
				let isShowPrice = increasePrice.filter((item) => item.isShow == 1 || item.isShow == null);
				return isShowPrice || [];
			} else if (this.selectItem.priceType === 1) {
				increasePrice.forEach((item) => {
					//单价
					let price = parseFloat(item.unitPrice) + this.selectItem.unitPrice;
					//计算价格分层
					let price2 = (price * this.customPriceData.multiBasicUnitPrice).toFixed(2);
					//计算折扣
					let price3 = Math.round(price2 * Math.abs(1 - discount) * 100) / 100;
					if (discount != 0) {
						item.discountPrice = price3;
						item.unitPrice = price2;
					} else {
						item.unitPrice = price2;
					}
				});
				let isShowPrice = increasePrice.filter((item) => item.isShow !== 0);
				return isShowPrice || [];
			}
		},
		findPrice(item, itemIncreasePrice) {
			let len = itemIncreasePrice.length,
				newNum = parseInt(item.quantity),
				findItem = itemIncreasePrice[0];
			for (let i = 0; i < len; i++) {
				let item = itemIncreasePrice[i],
					nextItem = itemIncreasePrice[i + 1];
				if (newNum >= item.quantity && (nextItem ? newNum < nextItem.quantity : true)) {
					findItem = item;
					break;
				}
			}
			return findItem;
		},
		toRight() {
			let container = document.querySelector(".qtyWrapList");
			let startX = container.scrollLeft;
			if (container.scrollWidth == container.scrollLeft + container.clientWidth && !this.cursor) {
				this.cursor = true;
				return;
			}
			if (container.scrollWidth == container.scrollLeft + container.clientWidth && this.cursor) {
				this.cursor = false;
				container.scrollTo({ top: 0, left: 0, behavior: "smooth" });
				return;
			}
			container.scrollTo(startX + 20, 0); // 横向 纵向
		},
		setDefault(data) {
			this.selectItem = data;
		},
		openTabel() {
			this.$emit("showPriceTableFn", !this.showPriceTable);
		}
	},
	mounted() {
		let container = document.querySelector(".qtyWrapList");
		if (container) {
			addDragable(container, (scrollLeft) => {
				if (scrollLeft == 0) {
					this.cursor = true;
				} else {
					this.cursor = false;
				}
			});
		}
	},
};
</script>

<template>
	<div class="pdtInfoBox">
		<div class="pdtInfo">
			<div class="pdtToolBar">
				<div class="pdt-toolBar-left">
					<div class="back" @click="back" v-if="!hideBack">
						<b class="icon-back" style="color: rgb(203, 203, 203); font-size: 16px; margin-right: 10px"></b>
					</div>
					<div class="teaTitle">
						<h1>{{ productInfo.name }}</h1>
					</div>
				</div>
				<div class="add-favorite" @click="
					goCollection({
						isCollection: productInfo.isCollection,
						id: productInfo.id,
					})
					">
					<template v-if="!productInfo.isCollection">
						<b class="icon-shoucang"></b>
					</template>
					<template v-else>
						<b class="icon-xinxin isActive"></b>
					</template>
				</div>
			</div>
			<div class="pdtRightTitle">
				<h2 style="line-height: initial;padding-top: 0.2em;">{{ langSemiCustom.item + ": " }}{{
					productInfo.productSku + ' IN STOCK' }}
				</h2>
				<div class="xin" v-if="productInfo.commentLevel > 0">
					<v-rating half-increments readonly size="18" :value="productInfo.commentLevel"
						background-color="#cccccc" color="#FFC300" dense length="5"></v-rating>
					<span class="xinCommentLevel" v-if="productInfo.commentLevel">{{
						productInfo.commentLevel }}</span>
					<span class="xinCommentNum" v-if="productInfo.commentNum" style="color: rgb(182, 177, 177)">({{
						productInfo.commentNum }})</span>
				</div>
			</div>
			<div class="discountBox">
				<div class="discountPrice">
					<div class="oldPrice" v-if="productInfo.discount">
						<div>
							<span class="nomalText">As low</span>
							<span :class="{ underLine: true }">
								<CCYRate :price="productInfo.lowestDiscountPrice"></CCYRate>
							</span>
							<span class="highPriceText" v-show="productInfo.highestDiscountPrice">
								<CCYRate :price="productInfo.highestDiscountPrice"></CCYRate>
							</span>
						</div>
					</div>
					<div v-else class="oldPrice">
						<span class="nomalText">As low</span>
						<span class="highPriceText" v-show="productInfo.lowestDiscountPrice">
							<CCYRate :price="productInfo.lowestDiscountPrice"></CCYRate>
						</span>
					</div>
					<div class="priceBtn" v-show="productInfo.discount">
						<div>
							<span>{{ langSemiCustom.save }}</span>
							{{ (productInfo.discount * 100).toFixed(0) + " %" }}
						</div>
					</div>
				</div>
				<div class="priceCode" v-show="productInfo.category.productDiscountText && productInfo.discount">
					{{ productInfo.category.productDiscountText }}
				</div>
				<div class="showDetail" v-show="isMobile" @click="openTabel">
					{{ langSemiCustom.medals.priceDetail }} <b class="icon-Down" :class="{ active: showPriceTable }"></b>
				</div>
			</div>
		</div>
	</div>
</template>

<style scoped lang="scss">
.pdtInfo {
	.pdtToolBar {
		display: flex;
		justify-content: space-between;
		align-items: center;

		h1 {
			font-weight: bold;
			font-size: 24px;
			color: #222222;
			line-height: 36px;
			word-break: break-word;

			@include respond-to(mb) {
				font-size: 15px;
				line-height: 18px;
			}
		}

		.pdt-toolBar-left {
			display: flex;
			align-items: center;

			.back {
				display: none;
			}
		}

		.add-favorite {
			cursor: pointer;

			b {
				font-size: 24px;

				@include respond-to(mb) {
					font-size: 16px;
				}
			}

			b.isActive {
				color: $color-primary;
			}
		}
	}

	.pdtRightTitle {
		margin: 4px 0 4px;
		display: flex;
		align-items: center;

		h2 {
			font-weight: bold;
			font-size: 18px;
			color: $color-primary;
			line-height: initial;

			@include respond-to(mb) {
				font-size: 13px;
			}
		}

		.xin {
			display: flex;
			align-items: center;
			margin-left: 6px;
			column-gap: 4px;

			&>div {
				display: flex;
				align-items: center;
			}

			.xinCommentLevel,
			.xinCommentNum {
				font-size: 14px;

				@include respond-to(mb) {
					font-size: 12px;
					padding-top: 4px;
				}
			}
		}
	}

	.discountBox {
		display: flex;
		align-items: center;
		justify-content: space-between;

		.discountPrice {
			display: flex;
			align-items: center;
			column-gap: 16px;

			.nowPrice {
				font-weight: bold;
				font-size: 14px;
				color: #f07b18;

				@include respond-to(mb) {
					font-size: 12px;
				}

				span {
					font-weight: bold;
				}

				label {
					font-weight: bold;
				}
			}

			.priceBtn {
				flex-shrink: 0;
				height: 28px;
				background: linear-gradient(60deg, #ee4113 0%, #f3aa1e 100%);
				border-radius: 0px 10px 0px 10px;
				display: flex;
				align-items: center;
				justify-content: space-evenly;
				font-size: 14px;
				font-weight: bold;
				color: #fff;
				width: fit-content;
				column-gap: 8px;
				padding: 0 8px;

				@include respond-to(mb) {
					font-size: 12px;
				}

				.circle {
					flex-shrink: 0;
					position: relative;
					width: 20px;
					height: 20px;
					border-radius: 50%;
					background-color: #ffffff;

					&::before {
						content: "%";
						font-size: 14px;
						position: absolute;
						top: 50%;
						left: 50%;
						transform: translate(-50%, -50%);
						color: $color-primary;
					}
				}

				@include respond-to(mb) {
					width: fit-content;
					height: 20px;
					border-radius: 0 8px 0 8px;
				}
			}
		}

		.oldPrice {
			color: #333;
			font-weight: 400;
			font-size: 14px;

			@include respond-to(mb) {
				font-size: 12px;
			}

			& .underLine {
				text-decoration: line-through;
			}

			.nomalText {
				&.underLine {
					text-decoration: none;
				}
			}

			.highPriceText {
				margin-left: 0.4em;
				font-size: 16px;
				color: $color-primary;
				font-weight: bold;
			}
		}

		.priceCode {
			font-size: 14px;
			color: $color-primary;
		}

		.showDetail {
			font-size: 12px;
			text-decoration: underline;
			cursor: pointer;

			b {
				font-size: 10px;

				&.active {
					transform: rotate(-180deg);
				}
			}
		}
	}
}


.printPriceList {
	position: relative;
	display: flex;

	.rightBox {
		display: flex;
		align-items: center;
		column-gap: 2px;
	}

	.right-btn {
		height: 100%;
		display: none;
		justify-content: center;
		align-items: center;

		&.cursor {
			cursor: no-drop;
		}

		b {
			font-size: 12px;
		}
	}

	.letter {
		margin-left: 4px;
		align-self: flex-end;
		width: 14px;
		font-family: Roboto, Roboto;
		font-weight: 400;
		font-size: 14px;
		color: #333333;
		line-height: 17px;
		text-align: center;
		font-style: normal;
		text-transform: none;
	}
}

.printPriceList.showBtn {
	.right-btn {
		display: flex;
	}
}

.qtyWrapList {
	position: relative;
	cursor: move;

	.qty-item {
		box-shadow: inset 0 0 20px 20px rgba(0, 0, 0, 0.02);

		.arrowBox {
			color: red;

			.v-icon {
				font-size: 14px;
				color: red;
			}
		}

		.line {
			text-decoration: line-through;
			color: #bab6b6;
		}
	}
}
</style>