<template>
	<div class="wrap">
		<div class="content" id="pdfDom">
			<div class="con content1">
				<div>
					<img src="http://192.168.1.54:50045/_nuxt/static/img/logo.png" alt="" style="width: max-content;">
				</div>
				<div style="display: flex;margin-top: 30px">
					<div style="flex: 1">
						<p style="margin: 0;font-weight: bold">o2o.co</p>
						<p style="margin: 0;">5880 Hampton Place, Unit 27 Vancouver,<br/>BC V6T 2E9 Canada</p>
					</div>
					<div style="flex: 1">
						<p style="margin: 0;font-weight: bold">Issused to:</p>
<!--						<p style="margin: 0;">enamelpins inc =4061 Paso Fino Way Yorba <br>LindaCA, United Stated</p>-->
						<p style="margin: 0;"> {{ detailInfo.proName }} <br> {{ detailInfo.url }}</p>

					</div>
				</div>

				<div style="margin-top: 165px">
					<p style="font-size: 16px;font-weight: bold">Invoice #{{ detailInfo.addedBillId }} <span class="mx-6">|</span>
						{{ detailInfo.lastMonAndDay }} <span class="mx-6">|</span> {{ detailInfo.lastPaidYear }} Paid</p>
				</div>
				<div style="margin-top: 20px">
					<table style="width: 100%;;font-size:16px;border-bottom: 1px solid #ccc;">
						<tr style="background-color: #F2F2F2;font-weight: bold">
							<td colspan="2" align="center" style="padding: 15px 0">Description</td>
							<td colspan="2" align="center">Site</td>
							<td colspan="2" align="left" >Billing Period</td>
							<td colspan="2" align="center">Quantity</td>
							<td colspan="2" align="center">Amount</td>
						</tr>
						<tr height = "100">
							<td colspan="2" align="center">{{ detailInfo.subscriptionType }}
								<!--								<br>Business Unlimited-->
							</td>
							<td colspan="2" align="center">{{ detailInfo.proName }}</td>
							<td colspan="2" align="left" width="150">{{ detailInfo.billingCycle }} <br>{{ detailInfo.lastMonAndDay }}, {{detailInfo.lastPaidYear}} - {{ detailInfo.nextMonAndDay }}, {{detailInfo.nextPaidYear}}</td>
							<td colspan="2" align="center">1</td>
							<td colspan="2" align="center">${{ detailInfo.lastPaymentAmount ? (detailInfo.lastPaymentAmount).toFixed(2) : '' }}</td>
						</tr>
					</table>
				</div>
				<div style="display: flex;margin-top: 120px">
					<div style="flex: 1">
						<p style="margin: 0;font-weight: bold">Payment Method: {{ detailInfo.cardType }}...{{ detailInfo.card?detailInfo.card.substr(detailInfo.card.length - 4):'' }}</p>
					</div>
					<div style="flex: 1">
						<p style="margin: 0;overflow: hidden;border-bottom: 1px solid #ccc;padding-bottom: 35px">
							<span>Subtotal:</span>
							<span style="float: right">${{ detailInfo.lastPaymentAmount ? detailInfo.lastPaymentAmount.toFixed(2) : '' }}</span>
						</p>
						<p style="margin: 0;font-weight: bold;overflow:hidden;margin-top: 35px">
							<span>Total:</span>
							<span style="float: right">${{ detailInfo.lastPaymentAmount ? detailInfo.lastPaymentAmount.toFixed(2) : '' }}</span>
						</p>
					</div>
				</div>
				<div style="margin-top: 340px;padding: 15px 0;display: flex">
<!--					<div style="flex: 1;overflow: hidden">-->
<!--						<span>Welcome to contact us</span>-->
<!--						<span style="margin-left: 20px">O2O.com/support</span>-->
<!--					</div>-->
<!--					<div style="flex: 1;overflow:hidden;">-->
<!--						<span style="margin-left: 150px">555-555-5555</span>-->
<!--						<span style="float: right">O2O.com/contact</span>-->
<!--					</div>-->
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import {getSubscribeDetail} from "~/api/manage/subscription";
import {creditCardType} from "@/utils/utils";

export default {
	data() {
		return {
			detailInfo:''
		};
	},
	methods: {
		getDetailInfo() {
			getSubscribeDetail(this.$route.query.id).then(res => {
				this.detailInfo = res.data;
				let card = creditCardType(this.detailInfo.card);
				this.detailInfo.cardType = card.cardName;
				let pro = res.data.project?res.data.project:'';
				this.detailInfo.proName = pro.proName?pro.proName:'';
				this.detailInfo.url = pro.url?pro.url:'';

				let date = new Date(this.detailInfo.lastPaymentTime);
				this.detailInfo.lastPaidYear = date.toDateString().split( " " )[3]
				this.detailInfo.lastMonAndDay = date.toDateString().split( " " )[1] + ' ' + date.toDateString().split( " " )[2]

				let date2 = new Date(this.detailInfo.nextPaymentTime);
				this.detailInfo.nextPaidYear = date.toDateString().split( " " )[3]
				this.detailInfo.nextMonAndDay = date.toDateString().split( " " )[1] + ' ' + date.toDateString().split( " " )[2]

				//
				// this.detailInfo.lastPaymentTime = this.detailInfo.lastPaymentTime.split( " " )[0];
				// this.detailInfo.nextPaymentTime = this.detailInfo.nextPaymentTime.split( " " )[0]
				// let date2 = new Date(this.detailInfo.nextPaymentTime);

				this.detailInfo.engNextTime = date2.toDateString();
				setTimeout(()=>{
					window.print()
				},1000)
			})
		},
	},
	created() {

	},
	mounted() {

			// this.logoUrl = 'https://sticker-static.oss-accelerate.aliyuncs.com/image/uploads/20210429/9ac3067dceb678f31a3785091793cc0e.png';
			this.getDetailInfo()
	}
};
</script>
<style lang="scss" scoped>
.wrap {
	font-size: 16px;
	color: #333333;
}

.content {
	width: 910px;
	margin: 20px auto;
}

.con {
	width: 100%;
	background: #FFFFFF;
	padding: 25px;
	font-size: 16px;
	color: #333333;
	page-break-after: always;

	table {
		border-collapse: collapse;
		border-color: #D9D9D9;
	}

	.bg {
		background-color: #f2f2f2;
	}
}
</style>
