<template>
	<!-- 图片截取弹窗 -->
	<base-dialog :value="cropperDialog" @update="update" :width="width" class="cropDialog">
		<div class="crop-area">
			<div class="title">{{ lang.cropArea }}</div>
			<div class="cropper-icon">
				<el-button @click="changeScale(1)">
					<svg t="1748575547403" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="9538" id="mx_n_1748575547404" width="16" height="16"><path d="M1007.937675 935.78296L830.041436 757.886721a465.454545 465.454545 0 1 0-364.078059 175.459305 461.55545 461.55545 0 0 0 292.432176-102.838649l176.921466 177.408853a51.175631 51.175631 0 0 0 73.108043 0 51.663018 51.663018 0 0 0-0.487387-72.13327zM103.834867 466.916706a363.590671 363.590671 0 1 1 726.206569 0 363.590671 363.590671 0 1 1-726.693956 0z" p-id="9539" fill="#ffffff"></path><path d="M635.086652 422.077106h-122.821513V298.280819a44.8396 44.8396 0 1 0-89.679201 0v123.796287H298.789651a44.8396 44.8396 0 1 0 0 89.679201h123.796287V633.603046a44.8396 44.8396 0 1 0 89.679201 0v-121.846739H635.086652a44.8396 44.8396 0 1 0 0-89.679201z" p-id="9540" fill="#ffffff"></path></svg>
				</el-button>
				<el-button @click="changeScale(-1)">
					<svg t="1748575567669" class="icon" viewBox="0 0 1025 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="9799" width="16" height="16"><path d="M1009.649371 935.740952L830.693181 758.247619a466.16381 466.16381 0 1 0-364.251429 175.542857 463.725714 463.725714 0 0 0 292.571429-102.887619l177.493333 177.493333a51.687619 51.687619 0 0 0 73.142857 0 52.175238 52.175238 0 0 0 0-72.655238zM104.1408 467.139048A364.251429 364.251429 0 1 1 830.693181 466.651429a364.251429 364.251429 0 1 1-728.015238 0z" p-id="9800" fill="#ffffff"></path><path d="M623.455086 404.23619H286.022705a48.761905 48.761905 0 0 0 0 94.598096h337.432381a48.761905 48.761905 0 0 0 46.323809-48.761905 48.761905 48.761905 0 0 0-46.323809-45.836191z" p-id="9801" fill="#ffffff"></path></svg>
				</el-button>
			</div>
			<div class="cropper">
				<vue-cropper
					ref="cropper"
					:img="imageSrc"
					:output-width="300"
					:output-height="300"
					:output-type="'png'"
					:guides="true"
					:viewMode="1"
					:dragMode="'move'"
					:background="false"
					:rotatable="true"
					:scalable="true"
					:zoomable="true"
					:cropBoxMovable="true"
					:cropBoxResizable="true"
					:aspect-ratio="2"
					:auto-crop="true"
				></vue-cropper>
			</div>
			<slot></slot>
			<div class="cropper-btn">
				<el-button @click="update(false)">{{ lang.cancel }}</el-button>
				<el-button type="primary" @click="cropImage">{{ lang.continue }}</el-button>
			</div>
		</div>
	</base-dialog>
</template>
<script>

import BaseDialog from "@/components/Quote/BaseDialog.vue";

export default {
	props: {
		'cropperDialog': {
			type: Boolean
		},
		'imageSrc': {
			type: String
		},
		'width': {
			type: String,
			default: '50%'
		}
	},
	components: {BaseDialog},
	data() {
		return {}
	},
	computed: {
		lang() {
			return this.$store.getters.lang?.design;
		},
	},
	methods: {
		cropImage() {
			this.$refs.cropper.getCropData(async data => {
				this.$emit('cropImage',data)
			})
			this.$emit('update:cropperDialog', false);
		},
		update(val) {
			this.$emit('update:cropperDialog', false);
			!val && this.$emit('cancelCrop')
		},
		changeScale(val) {
			val = val || 1
			this.$refs.cropper.changeScale(val)
		}
	}
}
</script>
<style scoped lang="scss">
.cropDialog ::v-deep{
	@include respond-to(mb){
		.base-dialog-model-con{
			width: 100% !important;
			max-width: 100% !important;
			height: 100%;
			border-radius: 0;

			.crop-area{
				display: flex;
				flex-direction: column;
				height: 100%;
				.cropper{
					flex:1;
				}
			}
		}
	}
}
.crop-area {
	position: relative;

	.button {
		text-align: center;
	}

	.title {
		font-size: 20px;
		border-bottom: 1px solid;
		padding: 11px 20px;
		font-weight: bold;
	}

	.cropper-icon {
		display: flex;
		position: absolute !important;
		z-index: 10 !important;
		top: 70px;
		right: 25px;

		.el-button {
			border-radius: 50%;
			background-color: #909399;
			padding: 10px;
			aspect-ratio: 1;
			color: white;
			display: flex;
			justify-content: center;
			border: 1px solid #909399;
		}
	}

	.cropper-btn {
		text-align: right;
		padding: 20px;
	}

	.cropper {
		height: 600px !important;
		padding: 20px;

		.vue-cropper {
			background-repeat: initial !important;
		}
	}
}
</style>
