<template>
	<div class="mb-4" :class="stepData.styleName">
		<half-design-select-tip :customText="stepData.attributeTitle" :selectedValue="shape"></half-design-select-tip>
		<slot name="stepText"></slot>
		<div class="step-content">
			<div class="step-item" :class="{ active: index === selectIndex }" v-for="(step, index) in stepData.productParamList" :key="index" @click="selectStep(step, index)">
				<div class="d-flex-center text-center pa-1">
					<div class="text-truncate">
						{{ step.valueName }}
					</div>
				</div>
			</div>
		</div>
		<template v-if="selectIndex === 1 || selectIndex === 2">
			<div class="textWrap">
				<div class="text-item">
					<div class="input-area">
						<v-text-field :value="myTextProperty.text" solo flat outlined dense hide-details label="Add Text" @input="addText">
							<template #append>
								<v-menu v-model="show" :close-on-content-click="false" offset-y left min-width="300">
									<template v-slot:activator="{ on, attrs }">
										<div v-bind="attrs" v-on="on">
											<img src="~/static/img/icon_color.png" alt="Color Wheel" title="Color Wheel" style="width: 25px; height: 25px" />
										</div>
									</template>
									<v-card class="color-picker-wrap" color="#ffffff">
										<div class="color-picker-title">{{ langSemiCustom.editColors }}</div>
										<div class="color-picker-list">
											<div class="color-item" :class="{ active: item.code === myTextProperty.fill }" v-for="item in colorList" :key="item.id" :style="{ backgroundColor: item.code }" :title="item.pantone" @click="changeTextProperty(item, 'fill')">
												<v-icon color="#ffffff" small> mdi-check</v-icon>
											</div>
										</div>
									</v-card>
								</v-menu>
							</template>
						</v-text-field>
					</div>
					<div class="font-bold" :class="{ active: myTextProperty.fontWeight === 'bold' }" @click="changeTextProperty(myTextProperty.fontWeight === 'normal' || myTextProperty.fontWeight === 400 ? 'bold' : 'normal', 'fontWeight')">{{ langSemiCustom.Bletter }}</div>
					<div class="font-style" :class="{ active: myTextProperty.fontStyle === 'italic' }" @click="changeTextProperty(myTextProperty.fontStyle === 'normal' || !myTextProperty.fontStyle ? 'italic' : 'normal', 'fontStyle')">{{ langSemiCustom.Iletter }}</div>
					<div class="fontFamilyWrap">
						<v-select :value="myTextProperty.fontFamily" :menu-props="{ bottom: true, offsetY: true }" solo flat outlined dense hide-details :items="fontsData" item-text="name" item-value="name" label="Font Family" @change="changeTextProperty($event, 'fontFamily')">
							<template #item="{ item }">
								<span :style="{ fontFamily: item.name }">{{ item.name }}</span>
							</template>
						</v-select>
					</div>
				</div>
			</div>
		</template>
		<template v-if="selectIndex === 0 || selectIndex === 2">
			<div class="imageWrap" v-show="!uploadImg.url">
				<div class="uploadBtn" @click="triggerUpload('upload')">
					{{ langSemiCustom.uploadLogo }}
					<input type="file" accept=".jpg,.jpeg,.png,.gif,.bmp" ref="upload" @click.stop @change="uploadPic" />
				</div>
			</div>
			<div class="box-img" v-show="uploadImg.url">
				<div class="text-center">
					<div class="uploadImg">
						<img :src="uploadImg.url" alt="upload" title="upload" />
						<v-icon class="close" small @click="delImg">mdi-close-circle</v-icon>
					</div>
				</div>
				<div class="changeImg" @click="triggerUpload('change')">
					<v-icon color="#ffffff" class="mr-2">mdi-plus-circle-outline</v-icon>
					{{ langSemiCustom.changeLogo }}
				</div>
			</div>
			<pic-color-change :oneColor.sync="oneColor" :filterColor="filterColor" :uploadImg="uploadImg" :picColorList="picColorList" :oldColor="oldColor" :newColor="newColor" :colorList="colorList" :copyPicColorList="copyPicColorList" @changeOneColor="changeOneColor" @selectPicColor="selectPicColor" @changePicColor="changePicColor" @filterPicColorBefore="filterPicColorBefore"> </pic-color-change>
		</template>
	</div>
</template>
<script>
import { designMixin } from "@/mixins/halfDesign";

export default {
	mixins: [designMixin],
	computed: {
		fontsData() {
			return require("@/assets/json/fontList.json");
		}
	},
};
</script>
<style scoped lang="scss">
@import "~assets/css/half.scss";

.style1 .step-content {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	grid-gap: 10px;

	.step-item {
		position: relative;
		@include step-default;
		min-width: 0;
		cursor: pointer;
		background-color: $background-color2;

		&::after {
			display: none;
			content: "";
			position: absolute;
			bottom: -10px;
			left: 50%;
			transform: translateX(-50%);
			width: 0;
			height: 0;
			border-width: 10px 10px 0;
			border-style: solid;
			border-color: $color-primary transparent transparent;
		}

		@media (any-hover: hover) {
			&:hover {
				color: #ffffff;
				border-color: $color-primary;
				background-color: $color-primary;
			}
		}
	}

	.step-item.active {
		color: #ffffff;
		border-color: $color-primary;
		background-color: $color-primary;

		&::after {
			display: block;
		}
	}
}

.textWrap {
	padding: 30px 0 0 0;
}

.text-item {
	display: flex;
	align-items: center;
	margin-bottom: 10px;

	.input-area {
		flex: 1;
	}

	&:last-child {
		margin-bottom: 0;
	}

	& > div {
		margin-right: 10px;

		&:last-child {
			margin-right: 0;
		}
	}
}

.font-bold,
.font-style {
	flex-shrink: 0;
	display: flex;
	justify-content: center;
	align-items: center;
	width: 40px;
	height: 40px;
	background: #fff;
	border: 1px solid rgba(0, 0, 0, 0.38);
	border-radius: 5px;
	cursor: pointer;
	transition-duration: 0.15s;
	transition-property: color;
	transition-timing-function: cubic-bezier(0.25, 0.8, 0.25, 1);

	&:hover {
		border-color: #333333;
	}

	&.active {
		border-color: $color-primary;
		border-width: 2px;
		color: $color-primary;
	}
}

.imageWrap {
	margin-top: 20px;

	.uploadBtn {
		position: relative;
		width: 300px;
		padding: 10px;
		border-radius: $border-color;
		background-color: $background-color;
		cursor: pointer;

		input[type="file"] {
			position: absolute;
			top: 0;
			left: 0;
			z-index: -1;
			opacity: 0;
			height: 0;
		}

		&:hover {
			color: #ffffff;
			background-color: $color-primary;
		}
	}
}

.box-img {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 10px;
	margin-top: 20px;
	background-color: $background-color;

	.uploadImg {
		position: relative;
		width: 150px;
		height: 100px;
		text-align: center;
		background-color: $background-color2;

		img {
			width: 100%;
			height: 100%;
			object-fit: contain;
		}

		.close {
			position: absolute;
			top: 5px;
			right: 5px;
			cursor: pointer;
		}
	}

	.changeImg {
		@include flex-center;
		@include step-default;
		background-color: #cdcdcd;
		border-radius: $step-border-radius;
		cursor: pointer;
		color: #ffffff;
	}
}

.fontFamilyWrap {
	flex: 0 0 250px;
}

@include respond-to(mb) {
	.style1 .step-content {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		grid-gap: 5px;

		.step-item {
			.imgWrap {
				@include flex-center;
				height: 80px;
			}

			&::after {
				bottom: -5px;
				border-width: 5px 5px 0;
			}
		}
	}
	.textWrap {
		padding-top: 20px;

		.text-item {
			flex-wrap: wrap;

			.input-area {
				flex: auto;
				width: 100%;
				margin-bottom: 10px;
				margin-right: 0;
			}
		}

		.font-bold,
		.font-style {
			margin-right: 5px;
		}
	}
	.fontFamilyWrap {
		width: 150px;
		max-width: 150px;
	}
	.box-img {
		margin-top: 10px;
		font-size: 12px;
	}
}
</style>
