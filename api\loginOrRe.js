import { request } from "@/utils/request";
//验证码登录
export function loginByPassword(email,code) {
	return request({
		url: "/app/member/retailer/loginByCode",
		method: "post",
		data: {
			email,
			code,
		},
	});
}

//注册
export function getRegister(data) {
	return request({
		url: "/member/retailer/register",
		method: "post",
		data,
	});
}
//发送验证码
export function sendResetPasswordCode(email) {
	return request({
		url: "/app/member/retailer/sendLoginCode",
		method: "get",
		params: {
			email,
		},
	});
}






