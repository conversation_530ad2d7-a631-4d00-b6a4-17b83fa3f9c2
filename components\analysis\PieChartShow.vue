<template>
	<div id="pie-chart-show">
		<div class="top"></div>
		<div class="main">
			<div class="main-top">
				<div class="left"></div>
				<div class="right"></div>
			</div>
			<div class="main-pie-area" v-for="item in 3" :key="item">
				<analysis-PieChartShowItem />
			</div>
		</div>
	</div>
</template>

<script>
export default {
	name: "PieChartShow",
	data() {
		return {};
	},
};
</script>

<style lang="scss" scoped>
#pie-chart-show {
	width: 100%;
}
.top {
	width: 23.69791667vw;
	height:1.5625vw;
	margin-left: 0.98958333vw;
	background: #e5e5e5;
	border-radius: 0.3125vw;
	margin-bottom: 0.8854vw;
}
.main {
	height: 45.9375vw;
	background: #eeeeee;
	border-radius: 0.5208vw;
	.main-top {
		display: flex;
		height: 3.125vw;
		padding: 0 1.0417vw;
		.left {
			width: 23.69791667vw;
			height: 1.0417vw;
			margin-top: 1.4583vw;
			margin-right: 5.9375vw;
			background: #e5e5e5;
			border-radius: 0.3125vw;
		}
		.right {
			width: 5.7292vw;
			height: 2.0833vw;
			background: #e5e5e5;
			border-radius: 0.3125vw;
			margin-top: 0.9896vw;
		}
	}
	.main-pie-area {
		height: 10.2083vw;
		/* padding: 2.3438vw 3.3854vw 0 3.4896vw; */
    padding-left: 3.6458vw;
    padding-right: 3.3854vw;
    margin: 1.97916667vw 0;
    display: inline-block;

	}
}
</style>
