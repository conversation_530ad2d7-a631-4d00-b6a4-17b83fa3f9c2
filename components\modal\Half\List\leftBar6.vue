<template>
  <div class="leftBar6 leftBar" :disabled="loadLabel">
    <div class="mbClickBox" @click="toggleFilter"></div>
    <div class="leftBarHeader">
      <div class="breadCrumbs">
        <div class="breadCrumbsBox">
          <div class="bread-item is-link" @click="toCate">
            <span style="font-size: 15px">{{ langSemiCustom.allProducts }}</span>
            <b class="icon-right"></b>
          </div>
          <div class="bread-item">
            <span class="bread-item-span" style="font-size: 13px; font-weight: 400">{{ fatherCateName }}</span>
            <b class="icon-right"></b>
          </div>
        </div>
        <div v-if="isMobile" @click="toggleFilter">
          <b class="iconfont icon-hxsht-xp-gb"></b>
        </div>
      </div>
      <div class="filterBox">
        <div class="filterHeader">
          <div class="filterTitle">
            {{ langSemiCustom.filters }}
            <span class="filterTotal">({{ totalResult }} {{ langSemiCustom.results }})</span>
          </div>
        </div>
        <div class="filterContent" v-show="Object.keys(selectedParamsObj).length > 0 || colorItem">
          <template v-if="colorItem">
            <div class="category-item tag">
              <span>{{ colorItem.valueName }}</span>
              <div @click="delColor(colorItem)" class="delIconBox">
                <v-icon small style="margin-left: 4px">mdi-close</v-icon>
              </div>
            </div>
          </template>
          <div class="tagBox" v-for="(item, key, index) in selectedParamsObj" :key="key">
            <div class="category-item tag" v-for="citem in item" :key="citem.id">
              <span>{{ citem.valueName }}</span>
              <div @click="delTag(citem, key)" class="delIconBox">
                <v-icon small style="margin-left: 4px">mdi-close</v-icon>
              </div>
            </div>
          </div>
        </div>
        <div class="clearFilter" @click="delAllTag" v-show="Object.keys(selectedParamsObj).length > 0 || colorItem">
          <v-icon style="font-size: 20px">mdi-trash-can-outline</v-icon>
          <a href="javascript:;">
            {{ langDesign.clearAll }}
          </a>
        </div>
      </div>
    </div>
    <div class="overlay changeLoad" v-show="loadLabel" style="z-index: 1">
      <div class="loader"></div>
    </div>
    <div class="collapse">
      <div class="filter-mb" v-if="isMobile && !isStockPage">
        <div class="filter-mb-title" @click="clickTitle({ id: -1 })">
          <strong>{{ langSemiCustom.sort }}:</strong>
          <b class="icon-Down" :class="{ active: activeNames.includes(-1) }"></b>
        </div>
        <div class="con" v-show="activeNames.includes(-1)">
          <div class="con-radio" v-for="(item,key) in sortList" :key="key" @click.stop="sortProduct(key)">
            <label>
              <input type="radio" name="sort" checked />
              {{item}}
              <span class="custom-radio"></span>
            </label>
          </div>
        </div>
      </div>
      <div class="collapse-item" v-for="(item, index) in labelData" :key="item.id">
        <div class="collapse-item-title" @click="clickTitle(item)">
          <strong v-show="!isCoins" class="text-truncate">{{ item.nameEn }}</strong>
          <div v-show="isCoins" class="text-truncate" style="font-weight: bold;text-transform:initial">{{
	capitalizeWords(item.nameEn) }}</div>
          <b class="icon-Down" :class="{ active: activeNames.includes(item.id) }"></b>
        </div>
        <v-expand-transition>
          <div class="con" v-show="activeNames.includes(item.id)">
            <template v-if="getLabelType(item.attributeType) === 'range'">
              <template v-for="citem in item.attributeList">
                <div class="price-range-box">
                  <v-btn icon v-show="showPriceRange">
                    <v-icon @click="clearRange([+citem.valueName, +citem.remark])">
                      mdi-trash-can</v-icon>
                  </v-btn>
                  <v-range-slider class="custom-slider" @change="changeRange" v-model="copyPriceRange" thumb-label :min="citem.valueName" :max="citem.remark" hide-details :step="0.01"></v-range-slider>
                </div>
                <div class="price-des">
                  <div>
                    <CCYRate :price="citem.valueName"></CCYRate>
                  </div>
                  <div>
                    <CCYRate :price="citem.remark"></CCYRate>
                  </div>
                </div>
              </template>
              <div class="borderBox" v-show="index !== labelData.length - 1" style="margin-top: 20px">
              </div>
            </template>
            <template v-else-if="getLabelType(item.attributeType) === 'color'">
              <div class="box color-box">
                <div class="color-wrap">
                  <div v-for="citem in item.attributeList" :key="citem.id" style="width: 100%; height: 100%">
                    <v-tooltip bottom :disabled="isMobile">
                      <template v-slot:activator="{ on, attrs }">
                        <div v-bind="attrs" v-on="on" class="color-item" :class="{ active: citem.id === colorItem.id }" @click="toggleColor(citem)">
                          <img v-if="citem.imgUrl" :src="citem.imgUrl" :alt="citem.valueName" :title="citem.valueName" />
                          <span v-else :style="{ backgroundColor: citem.valueName }"></span>
                        </div>
                      </template>
                      <span style="font-size: 12px">{{ citem.remark }} ({{ citem.productCount
												}})</span>
                    </v-tooltip>
                  </div>
                </div>
                <div class="borderBox" v-show="index !== labelData.length - 1" style="margin-top: 20px">
                </div>
              </div>
            </template>
            <template v-else-if="getLabelType(item.attributeType) === 'checkbox-style1' || getLabelType(item.attributeType) === 'checkbox-style2'">
              <div :class="getLabelType(item.attributeType)">
                <div class="labelCheckbox" v-for="citem in showMoreArr(item)" :key="citem.id">
                  <v-checkbox v-model="selectedParams" @change="updateTag(citem)" :label="`${citem.valueName} (${citem.productCount})`" :value="citem.id" hide-details dense></v-checkbox>
                </div>
                <div class="showBtn" ref="showBtn" v-show="item.isMore && item.attributeList.length > 8" @click="showMore(item)">
                  {{ item.moreText ? langSemiCustom.ShowMore : langSemiCustom.ShowLess }}
                </div>
                <div class="borderBox" v-show="index !== labelData.length - 1" style="margin-top: 20px">
                </div>
              </div>
            </template>
            <template v-else-if="getLabelType(item.attributeType) === 'switch'">
              <v-switch v-model="selectedParams" @change="updateTag(citem)" :label="`${citem.valueName} (${citem.productCount})`" :value="citem.id" v-for="citem in item.attributeList" :key="citem.id" hide-details dense></v-switch>
              <div class="borderBox" v-show="index !== labelData.length - 1" style="margin-top: 20px">
              </div>
            </template>
            <template v-else-if="item.attributeType === 'cateList'">
              <div class="cateListBox">
                <div class="labelRadioBox" v-for="(citem, index) in item.attributeList" :key="index">
                  <div class="radioBoxTitle" @click="clickCateTitle(citem)">
                    <strong v-show="!isCoins" class="text-truncate">{{ citem.nameEn }}</strong>
                    <div v-show="isCoins" class="text-truncate" style="font-weight: bold;text-transform:initial">{{
											capitalizeWords(citem.nameEn) }}</div>
                    <b class="icon-jxsht-3d-tj" v-show="!cateTitleList.includes(citem.id)"></b>
                    <b class="icon-jianshao" v-show="cateTitleList.includes(citem.id)"></b>
                  </div>
                  <div class="con-radio-box" v-show="cateTitleList.includes(citem.id)" v-for="(ccitem, index2) in citem.attributeList" :key="ccitem.id">
                    <div class="con-radio2">
                      <label @click="changeCate(ccitem)">
                        <input type="radio" :checked="selectIndex == ccitem.id" name="cateId" :value="ccitem.id" v-model="selectIndex" />
                        <span class="custom-radio" @click.stop></span>
                        <span @click.stop>
                          {{ ccitem.valueName }} ({{ ccitem.productCount || 0 }})
                        </span>
                      </label>
                    </div>
                  </div>
                </div>
                <div class="borderBox" v-show="index !== labelData.length - 1" style="margin-top: 20px">
                </div>
              </div>
            </template>
          </div>
        </v-expand-transition>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'leftBar6',
  props: {
    isStockPage: {},
    loadLabel: {
      type: Boolean,
      default: false
    },
    labelData: {
      type: Array,
      default: () => []
    },
    totalResult: {
      type: [Number, String],
      default: 0
    },
    colorItem: {},
    activeNames: {
      type: Array,
      default: () => []
    },
    fatherCateName: {
      type: String,
      default: ''
    },
    showPriceRange: {
      type: Boolean,
      default: false
    },
    fristWatch: {
      type: Boolean,
      default: false
    },
    priceRange: {
      type: Array,
      default: () => [-990000, 990000]
    },
    cateId: {
      type: [Number, String]
    },
    parentOneChildCateId: {
      type: [Number, String]
    },
    parentCateId: {
      type: [Number, String]
    },
    isCoins: {
      type: Boolean,
      default: false
    }
  },
  components: {},
  data() {
    return {
      copyPriceRange: [...this.priceRange],
      selectedParamsObj: {},
      selectedParams: [],
      addTag: false,
      productTag: '',
      cateTitleList: [],
      selectIndex: -99
    }
  },
  watch: {
    priceRange: {
      handler(newVal, oldVal) {
        if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
          this.copyPriceRange = newVal.slice()
        }
      },
      immediate: true // 立即执行一次 handler
    },
    selectedParams() {
      if (!this.fristWatch) return
      this.$emit('updateFn')
    },
    labelData() {
      this.labelData.forEach(item => {
        if (item.attributeType == 'cateList' && item.attributeList.length > 0) {
          item.attributeList.forEach(citem => {
            const id = +citem.id
            if (!this.cateTitleList.includes(id)) {
              this.cateTitleList.push(id)
            }
          })
        }
      })
    }
  },
  computed: {
      sortList(){
          return{
              1:this.langSemiCustom.newest,
              2:this.langSemiCustom.recommended,
              3:this.langSemiCustom.bestSeller,
              4:this.langSemiCustom.popular
          }
      },
    isMobile() {
      return this.$store.state.device === 'mb'
    },
    isDialog() {
      return !!this.name
    },
    langSemiCustom() {
      return this.$store.getters.lang?.semiCustom
    },
    langDesign() {
      return this.$store.getters.lang?.design
    }
  },
  methods: {
    getLabelType(type) {
      //标签类型 1.单选颜色样式，2.多选一列样式，3.多选两列样式，4.仅勾选是/否样式，5.拖动条样式），如果类型是拖动样式，属性值名称是最小值，属性值备注是最小值
      const obj = {
        1: 'color',
        2: 'checkbox-style1',
        3: 'checkbox-style2',
        4: 'switch',
        5: 'range',
        6: 'cateList'
      }
      return obj[type]
    },
    showMoreArr(item) {
      if (item.moreText && item.attributeList.length > 8) {
        return item.attributeList.slice(0, 8)
      } else {
        return item.attributeList
      }
    },
    showMore(item) {
      item.moreText = !item.moreText
    },
    updateTag(item) {
      let val = this.selectedParamsObj[item.fatherId]
      if (!val) {
        this.selectedParamsObj[item.fatherId] = [item]
        this.addTag = true
        this.productTag = item.valueName
      } else {
        let findIndex = val.findIndex(v => {
          return v.id == item.id
        })
        if (findIndex > -1) {
          val.splice(findIndex, 1)
          this.addTag = false
          this.productTag = ''
        } else {
          this.addTag = true
          this.productTag = item.valueName
          val.push(item)
        }
        if (val.length == 0) delete this.selectedParamsObj[item.fatherId]
      }
    },
    getLabelIds() {
      let arr = []
      for (let i in this.selectedParamsObj) {
        arr.push({
          parentId: i,
          childIds: this.selectedParamsObj[i].map(item => item.id)
        })
      }
      return arr
    },
    getAttributeValueStr() {
      let str = ''
      for (let i in this.selectedParamsObj) {
        let val = this.selectedParamsObj[i].map(item => item.id)
        if (val.length > 0) {
          str += val.join(',') + '-'
        }
      }
      return str
    },
    toggleFilter() {
      this.$emit('toggleFilter')
    },
    delAllTag() {
      this.selectedParams = []
      this.selectedParamsObj = {}
      this.addTag = false
      this.productTag = ''
      this.labelData.forEach(item => (item.radio = ''))
      this.$emit('delAllTag')
    },
    delColor() {
      let tagData = this.addTag ? this.productTag : ''
      this.$emit('delColor', tagData)
    },
    delTag(item, id) {
      let ids = item.id
      let ind = this.selectedParams.findIndex(item => item === ids)
      if (ind >= 0) {
        this.selectedParams.splice(ind, 1)
      }
      let data = this.labelData.find(item => item.id == id)
      if (data) data.radio = ''
      this.updateTag(item)
    },
    clickTitle(val) {
      this.$emit('clickTitle', val)
    },
    clickCateTitle(val) {
      let exists = this.cateTitleList.includes(val.id)
      if (exists) {
        this.cateTitleList = this.cateTitleList.filter(item => item !== val.id)
      } else {
        this.cateTitleList.push(val.id)
      }
    },
    toggleColor(item) {
      let tagData = this.addTag ? this.productTag : ''
      this.$emit('toggleColor', item, tagData)
    },
    toCate() {
      this.$emit('toCate')
    },
    changeRange() {
      if (JSON.stringify(this.copyPriceRange) == JSON.stringify(this.priceRange)) return
      this.$emit('update:priceRange', [...this.copyPriceRange])
      let tagData = this.addTag ? this.productTag : ''
      this.$emit('changeRange', tagData)
    },
    clearRange(moData) {
      let tagData = this.addTag ? this.productTag : ''
      this.$emit('clearRange', moData, tagData)
    },
    sortProduct(command) {
      this.$emit('sortProduct', command)
    },
    changeCate(data) {
      this.$emit('newToCate', data)
    },
    capitalizeWords(str) {
      return str.toLowerCase().replace(/\b(\w)/g, s => s.toUpperCase())
    }
  },
  created() {
    if (this.cateId && this.parentCateId) {
      //只有cateId 是一级  有cateId和parentCateId 是二级  cateId是二级id 有cateId和parentCateId和parentOneChildCateId  是三级 cateId是三级id
      let copyParentOneChildCateId = this.parentOneChildCateId
      if (!copyParentOneChildCateId) copyParentOneChildCateId = this.cateId
      this.$emit('update:activeNames', [+this.parentCateId])
      this.cateTitleList.push(+copyParentOneChildCateId)
      this.selectIndex = +this.cateId
    }
  },
  mounted() {}
}
</script>

<style scoped lang="scss">
.loader {
  width: 50px;
  aspect-ratio: 1;
  display: grid;
  border: 4px solid #0000;
  border-radius: 50%;
  border-right-color: $color-primary;
  animation: l15 1s infinite linear;
}

.loader::before,
.loader::after {
  content: '';
  grid-area: 1/1;
  margin: 2px;
  border: inherit;
  border-radius: 50%;
  animation: l15 2s infinite;
}

.loader::after {
  margin: 8px;
  animation-duration: 3s;
}

@keyframes l15 {
  100% {
    transform: rotate(1turn);
  }
}

.changeLoad {
  position: absolute;
  inset: 0;
  width: 100%;
  // height: 100vh;
}

.overlay {
  align-items: center;
  border-radius: inherit;
  display: flex;
  justify-content: center;
  pointer-events: auto;
  width: 100%;
  background-color: rgba(0, 0, 0, 0.05);
}

.category-item {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 14px;
  font-size: 14px;
  cursor: pointer;
  background: #e8e9eb;
  border: 1px solid transparent;
  flex-shrink: 0;

  &.cateTag {
    span {
      padding: 5px 15px;
    }
  }

  &.active {
    font-weight: bold;
    color: var(--tag-color, var(--color-primary));
    background-color: var(--tag-color-lighten, var(--color-second));
    border: 1px solid var(--color-primary-lighten);
  }

  &.tag {
    padding: 3px 10px;
    border-radius: 0;
    color: $color-primary;
    color: var(--tag-color, var(--color-primary));
    background-color: var(--tag-color-lighten, var(--color-second));

    .delIconBox {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    &:hover {
      border: 1px solid var(--color-primary-lighten);

      .v-icon {
        color: #666666;
      }
    }
  }

  &:hover {
    color: var(--tag-color, var(--color-primary));
    background-color: var(--tag-color-lighten, var(--color-second));
    border: 1px solid var(--color-primary-lighten);

    .v-icon {
      color: #ffffff;
    }
  }

  span {
    b {
      font-size: 10px;
    }
  }

  @include respond-to(mb) {
    white-space: nowrap;
  }
}

.borderBox {
  width: 100%;
  height: 0px;
  border: 1px solid #f0f0f0;
}

.leftBarHeader {
  padding: 10px 0;

  .breadCrumbs {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 0px 0 10px;

    .breadCrumbsBox {
      display: flex;
      align-items: center;
      line-height: initial;
    }

    .icon-hxsht-xp-gb {
      font-size: 18px;
    }

    b {
      margin: 0 4px;
      font-size: 12px;
    }

    .bread-item {
      display: flex;
      align-items: center;
      cursor: pointer;

      .bread-item-span {
        font-size: 1em;
        font-weight: 400;
        cursor: auto;
        line-height: initial;
      }
    }

    .bread-item.is-link span {
      color: $color-primary;
    }

    .bread-item:last-child b {
      display: none;
    }

    @include respond-to(mb) {
      margin: 10px 0;

      .bread-item {
        display: flex;
        align-items: center;
        cursor: pointer;

        span {
          font-size: 16px !important;
        }

        .bread-item-span {
          font-size: 14px !important;
        }
      }
    }
  }

  @include respond-to(mb) {
    padding-top: 30px;
  }
}

.filterBox {
  display: flex;
  flex-direction: column;

  .filterHeader {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .filterTitle {
      font-weight: 700;
      font-size: 20px;
      color: #000000;

      .filterTotal {
        font-weight: normal;
        font-style: italic;
        font-size: 16px;
        color: #666666;
        margin-left: 10px;
      }
    }
  }

  .filterContent {
    margin: 10px 0;
    overflow: hidden;
    display: flex;
    flex-wrap: wrap;
    gap: 4px;

    .tagBox {
      display: flex;
      flex-wrap: wrap;
      gap: 4px;
    }
  }

  .clearFilter {
    display: flex;
    align-items: center;
    font-weight: 400;
    font-size: 14px;
    color: #333333;

    ::v-deep .v-icon {
      margin-left: -4px;
    }

    a {
      line-height: normal;
    }

    @include respond-to(mb) {
    }
  }
}

.leftBar6[disabled] {
  pointer-events: none;
}

.leftBar6 {
  position: sticky;
  top: 0;
  overflow: hidden;
  max-height: 800px;
  display: flex;
  flex-direction: column;
  width: 265px;
  padding: 10px 0px 40px;
  background-color: #ffffff;

  .mbClickBox {
    display: none;
  }

  &::-webkit-scrollbar {
    display: none;
  }

  .filter {
    font-size: 14px;

    .top {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;

      a {
        color: $color-primary;
        text-decoration: underline;
      }

      span {
        color: #c4c4c4;
        font-style: italic;
      }
    }
  }

  .collapse {
    flex: 1;
    overflow: hidden auto;
    padding: 10px 0px 40px;
    scrollbar-width: none;

    &::-webkit-scrollbar {
      display: none;
    }

    .collapse-item {
      .sizeScroll {
        max-height: 145px;
        overflow-y: auto;
      }
    }

    .collapse-item-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 30px;
      font-size: 16px;
      cursor: pointer;
      text-transform: uppercase;

      b {
        font-size: 10px;
      }

      .active {
        transform: rotate(-180deg);
      }
    }

    .con {
      ::v-deep .v-label {
        font-size: 14px;
      }

      ::v-deep .v-input--selection-controls.v-input--dense .v-input--selection-controls__ripple {
        top: calc(50% - 18px);
        height: 22px;
        left: -5px;
        width: 22px;
      }

      .showBtn {
        font-size: 14px;
        color: $color-primary;
        margin: 5px 0;
        text-decoration: underline;
        cursor: pointer;
      }

      .price-range-box {
        padding: 0 10px;
        text-align: right;

        i {
          font-size: 16px;
          color: #666666;
          margin-right: -8px;
          cursor: pointer;
        }
      }

      .price-des {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 14px;
      }

      .color-box {
        .color-wrap {
          display: grid;
          grid-template-columns: repeat(8, calc((100% - (8 - 1) * 4px) / 8));
          grid-column-gap: 4px;
          grid-row-gap: 4px;

          .color-item {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            height: 100%;
            aspect-ratio: 1/1;
            background: #ffffff;
            // border: 1px solid #cccccc;
            border: 2px solid #f2f2f2;
            border-radius: 50%;
            cursor: pointer;

            &:hover {
              border-color: $color-primary;
              border-width: 2px;
            }

            &.active {
              border-color: $color-primary;
              border-width: 2px;
            }

            span {
              width: 100%;
              height: 100%;
              display: inline-block;
              border-radius: 50%;
              border: 2px solid #f2f2f2;
            }

            img {
              border-radius: 50%;
            }
          }
        }
      }

      .checkbox-style2 ::v-deep {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
      }
    }
  }

  @include respond-to(mb) {
    position: fixed;
    width: calc(100% - 10vw);
    height: 100%;
    top: 0;
    left: 0;
    z-index: 100000;
    padding: 0 20px 40px;

    .mbClickBox {
      display: block;
      position: fixed;
      top: 0;
      right: 0;
      content: '';
      width: 10vw;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
    }

    // max-height: 400px !important;

    .leftBar-header {
      position: sticky;
      top: 0;
      margin-left: -10px;
      width: calc(100% + 20px);
      height: 50px;
      background-color: $color-primary;
      color: #fff;
      z-index: 99;

      .filter {
        text-align: center;
        font-weight: bold;
        font-size: 1.5em;
        line-height: 50px;
        margin: 0;
      }

      b {
        position: absolute;
        top: 0;
        right: 14px;
        line-height: 50px;
        z-index: 2;
        font-size: 14px;
      }
    }

    .filter-mb {
      margin-bottom: 20px;

      .filter-mb-title {
        height: 30px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        cursor: pointer;
        text-transform: uppercase;
        font-size: 16px;
        color: #333;
        line-height: 50px;

        b {
          font-weight: 400;
          font-size: 12px;
        }

        .active {
          transform: rotate(-180deg);
        }
      }

      .con {
        display: flex;
        align-items: flex-start;
        gap: 10px;
        flex-direction: column;
        font-size: 14px;

        label {
          display: flex;
          align-items: center;
          flex-direction: row-reverse;

          span {
            margin-right: 10px;
          }
        }

        /* 未选中状态下的样式 */
        input[type='radio'] {
          /* 隐藏原始的单选按钮 */
          display: none;
        }

        /* 自定义样式 */
        .custom-radio {
          display: inline-block;
          width: 14px;
          height: 14px;
          border-radius: 50%;
          border: 2px solid #333;
          position: relative;
          cursor: pointer;
        }

        /* 选中状态下的样式 */
        .custom-radio:before {
          content: '';
          display: block;
          width: 6px;
          height: 6px;
          background-color: #fff;
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          border-radius: 50%;
        }

        /* 选中状态下的外圈样式 */
        input[type='radio']:checked + .custom-radio {
          background-color: $color-primary !important;
          border: 1px solid $color-primary;
        }
      }
    }

    .collapse {
      .collapse-item {
        .collapse-item-title {
          font-size: 14px;
        }

        .con {
          .color-box {
            .color-wrap {
              align-items: center;
              justify-items: self-start;
            }

            .color-item {
              max-width: 32px;
              max-height: 32px;
            }
          }
        }
      }
    }
  }
}

.cateListBox {
  .labelRadioBox {
    padding-left: 4px;
    padding-right: 2px;

    .radioBoxTitle {
      height: 30px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 15px;
      cursor: pointer;
      text-transform: uppercase;

      .text-truncate {
        font-size: 14px;
      }
    }

    b {
      font-size: 16px;
    }

    .con-radio-box {
      padding-left: 4px;

      .con-radio2 {
        font-size: 14px;
        margin-top: 10px;
        color: rgba(0, 0, 0, 0.6);
      }
    }
  }
}

.con-radio2 {
  label {
    display: flex;
    align-items: center;
    gap: 7px;
    cursor: pointer;
  }

  /* 未选中状态下的样式 */
  input[type='radio'] {
    /* 隐藏原始的单选按钮 */
    display: none;
  }

  /* 自定义样式 */
  .custom-radio {
    display: inline-block;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    border: 1px solid #333;
    position: relative;
    cursor: pointer;
  }

  /* 选中状态下的样式 */
  .custom-radio:before {
    content: '';
    display: block;
    width: 0.5em;
    height: 0.5em;
    background-color: #fff;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    border-radius: 50%;
  }

  /* 选中状态下的外圈样式 */
  input[type='radio']:checked + .custom-radio {
    background-color: $color-primary !important;
    border: 1px solid $color-primary;
  }
}
</style>
