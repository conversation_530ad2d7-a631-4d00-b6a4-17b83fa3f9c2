<template>
	<div class="mb-4" :class="stepData.styleName">
		<half-design-select-tip :customText="stepData.attributeTitle" :selectedValue="shape"></half-design-select-tip>
		<slot name="stepText"></slot>
		<div class="step-content">
			<div class="area">
				<div class="step-item" :style="{ pointerEvents: canClick ? 'auto' :  'none' }" :class="{ active: index === selectIndex }" v-for="(step, index) in stepData.productParamList" :key="index" @click="selectStep(step, index)">
					<div class="upText">
						{{ getUpText(step.valueName) }}
					</div>
					<div class="d-flex-center text-center name">
						<div class="text-truncate">
							{{ step.valueName.toUpperCase() }}
						</div>
					</div>
				</div>
			</div>
			<div class="area-img">
				<img :src="shapeImg" alt="shape" title="shape" />
			</div>
		</div>
		<div class="errorTip">
			<v-alert dense outlined type="error">
				{{ langSemiCustom.errorTip }}
			</v-alert>
		</div>
	</div>
</template>
<script>
export default {
	props: {
		stepData: {
			type: Object,
			default: {},
		},
	},
	data() {
		return {
			selectIndex: -1,
			selectItem: null,
		};
	},
	computed: {
		shape() {
			return this.selectItem?.valueName;
		},
		shapeImg() {
			return this.selectItem?.imgDetail || this.stepData.productParamList[0].imgDetail;
		},
		langSemiCustom() {
			return this.$store.getters.lang?.semiCustom;
		},
		canClick(){
			return this.$store.state.halfDesign.canClickBtn;
		}
	},
	methods: {
		clearData(state,areaIndex) {
			this.selectIndex = areaIndex;
			this.selectItem = null;
			if(areaIndex<0) return
			if (this.stepData && this.stepData.productParamList && this.stepData.productParamList.length > 0) {
				let item=this.stepData.productParamList[areaIndex];
				this.selectStep(item, areaIndex, state);
			}
		},
		selectStep(item, index, state = false) {
			this.selectIndex = index;
			this.selectItem = item;
			this.$emit("selectStep", {
				type: this.stepData.attributeFlag,
				data: item,
				parent: this.stepData,
				id: this.stepData.id,
				firstSelect: state,
			});
		},
		zoomPic(img) {
			this.$viewerApi({
				images: [img],
			});
		},
		getFirst(str) {
			return str.substring(0, 1).toUpperCase();
		},
		getUpText(name) {
			let arr = name.replace(".", " ").split(" "),
				str = "";
			arr.forEach((item) => {
				str += this.getFirst(item);
			});
			return str;
		},
	},
	mounted() {
		this.$Bus.$on("clearArea", this.clearData);
		this.$Bus.$on("selectDefaultAreaStep",this.clearData);
	},
	beforeDestroy() {
		this.$Bus.$off("clearArea",this.clearData);
		this.$Bus.$off("selectDefaultAreaStep",this.clearData);
	},
};
</script>
<style scoped lang="scss">
@import "~assets/css/half.scss";

.step-content {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	grid-gap: 40px;
	align-items: center;

	.area {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		grid-gap: 10px;

		.step-item {
			position: relative;
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			border: none;
			padding: 10px;
			@include radius-response;
			background-color: $background-color;
			min-width: 0;
			cursor: pointer;

			.name {
				width: 80px;
				font-size: 12px;
			}

			.upText {
				margin-bottom: 10px;
			}

			@media (any-hover: hover) {
				&:hover {
					background-color: $color-primary;
					color: #ffffff;
				}
			}
		}

		.step-item.active {
			background-color: $color-primary;
			color: #ffffff;
		}
	}

	.area-img {
		@include flex-center;
		border: 2px solid $border-color;
		border-radius: $step-border-radius;
		height: 165px;
		padding: 10px;

		img {
			width: 100%;
			height: 100%;
			object-fit: contain;
		}
	}

	@include respond-to(mb) {
		grid-template-columns: repeat(1, 1fr);
		grid-gap: 10px;
	}
}
</style>
