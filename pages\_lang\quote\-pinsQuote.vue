<template>
	<div class="quoteWrap">
		<Preloader v-if="isLoading"></Preloader>
		<template v-else>
			<div class="content">
				<QuoteNav :pid="pid" title="We provide these Pins types for you."></QuoteNav>
				<QuoteTitle :h1-text="`${lang.cy} ${cateData.cateName}`" :prompts-text="lang.pins.prompts"></QuoteTitle>
				<div class="leftArea">
					<template v-for="(item, index) in filterShowGeneralData">
						<PublicStep v-if="item.paramName == 'quoteCategory' && !quoteStyleData.noShowDetail" :isFullReductionActivity="isFullReductionActivity" :generalData="generalData" :selectedData="selectedData" :config="allStepConfig[item.paramName]" :stepData="item" :maskName.sync="maskName" @clickFun="selectQuoteParams(item, $event)" @showMaskFn="showMaskFn" @viewVideo="viewVideo"></PublicStep>

						<PinsSizeSelect v-if="item.paramName == 'Pin Size' || item.paramName == 'Select Trading Pin Size'" :generalData="generalData" :selectedData="selectedData" :stepData="item" :maskName="maskName" :smallTitle="allStepConfig[item.paramName].smallTitle" :sizeImgP1="allStepConfig[item.paramName].sizeImgP1" @clickFun="selectQuoteParams(item, $event)" @closeMask="closeMask" @showMaskFn="showMaskFn"></PinsSizeSelect>

						<PublicStep v-if="item.paramName == 'Select Shapes'" :selectedData="selectedData" :config="allStepConfig[item.paramName]" :stepData="item" :maskName.sync="maskName" @clickFun="selectQuoteParams(item, $event)" @showMaskFn="showMaskFn"></PublicStep>

						<PublicStep v-if="item.paramName == 'Rush Pin Size' || item.paramName == 'Select Rush Pin Size'" :selectedData="selectedData" :config="allStepConfig[item.paramName]" :stepData="item" :maskName.sync="maskName" @clickFun="selectQuoteParams(item, $event)" @showMaskFn="showMaskFn"></PublicStep>

						<PublicStep v-if="item.paramName == 'Select Metal Finish'" :selectedData="selectedData" :stepData="item" :config="allStepConfig[item.paramName]" :maskName.sync="maskName" @clickFun="selectQuoteParams(item, $event)"></PublicStep>

						<PublicStep v-if="item.paramName == 'Select Amount of Colors for Pin'" :selectedData="selectedData" :stepData="item" :config="allStepConfig[item.paramName]" :maskName.sync="maskName" @clickFun="selectQuoteParams(item, $event)"></PublicStep>

						<PublicStep v-if="item.paramName == 'Additional Upgrades (Optional)'" :selectedData="selectedData" :stepData="item" :config="allStepConfig[item.paramName]" :maskName.sync="maskName" @clickFun="selectQuoteParams(item, $event)" @viewVideo="viewAdditionalVideo" @showMaskFn="showMaskFn"></PublicStep>

						<PublicStep v-if="item.paramName == 'Select Attachment'" :selectedData="selectedData" :stepData="item" :config="allStepConfig[item.paramName]" :maskName.sync="maskName" @clickFun="selectQuoteParams(item, $event)" @showLessBtn="showLessBtn(item.paramName)"></PublicStep>

						<PublicStep v-if="item.paramName == 'Back Side Option'" :selectedData="selectedData" :stepData="item" :config="allStepConfig[item.paramName]" :maskName.sync="maskName" @clickFun="selectQuoteParams(item, $event)"></PublicStep>

						<PublicStep v-if="item.paramName == 'Select Packaging'" :selectedData="selectedData" :stepData="item" :config="allStepConfig[item.paramName]" :maskName.sync="maskName" @clickFun="selectQuoteParams(item, $event)"></PublicStep>

						<template v-if="item.paramName === 'Upload Artwork & Comments'">
							<StepUpload class="step-upload" :class="{ mask: maskName === item.paramName }" :id="item.paramName" :index="item.customIndex" :itemData="item" :uploadList.sync="uploadArtworkList" :remark.sync="remark" :isUpload.sync="isUpload" @closeMask="closeMask" @showMaskFn="showMaskFn" :key="index"></StepUpload>
						</template>

						<template v-if="item.paramName === 'qty' || item.paramName === 'Quantity'">
                            <StepQty class="step-qty" :class="{ mask: maskName === item.paramName }" :id="item.paramName" :giftQuantity1="giftQuantity1" :qtyChecked.sync="qtyChecked" :restaurants="restaurants" :index="item.customIndex" :itemData="item" :isFullReductionActivity="isFullReductionActivity" :customQty.sync="customQty" @closeMask="closeMask" @showMaskFn="showMaskFn" @calcPrice="debounceCalcPrice" @changeQty="changeQty" @qtyCheckedChange="qtyCheckedChange"></StepQty>
                        </template>

						<template v-if="item.paramName === 'Select Turnaround Time'">
							<StepTime class="step-date" :class="{ mask: maskName === item.paramName }" :id="item.paramName" :cateData="cateData" :pid="pid" :customQty="customQty" :index="item.customIndex" :itemData="item" :priceInfo="priceInfo" :previewMode="previewMode" :selectedParams="selectedData" @showMaskFn="showMaskFn" @selectItem="selectQuoteParams($event.item, $event.citem)" @closeMask="closeMask"></StepTime>
						</template>
					</template>
				</div>
				<div class="rightArea" id="rightAreaCustom">
					<transition enter-active-class="animate__animated animate__faster animate__fadeInBottomLeft" leave-active-class="animate__animated animate__faster animate__fadeOutBottomLeft">
						<Detail v-show="showRightArea" class="type1" :class="{ mask: maskName }" :key="1" :absoluteImg="comeFromDZ.absoluteImg" :selectedData="selectedData" :priceInfo="priceInfo" :customQty="customQty" :textInfo="textInfo" :generalData="generalData" :attachment="attachment" @toPosition="toPosition" @addInquiry="debounceAddInquiry" @addCart="addCart"></Detail>
					</transition>
				</div>
			</div>
			<div class="footer" id="foot">
				<Detail class="type2" :key="3" :absoluteImg="comeFromDZ.absoluteImg" :selectedData="selectedData" :priceInfo="priceInfo" :customQty="customQty" :textInfo="textInfo" :generalData="generalData" :attachment="attachment" @toPosition="toPosition" @addInquiry="debounceAddInquiry" @addCart="addCart"></Detail>
			</div>
			<el-drawer :visible.sync="showArea" custom-class="drawDialog" :size="device === 'ipad' ? '728px' : '85%'">
				<Detail class="type3" :key="2" :absoluteImg="comeFromDZ.absoluteImg" :selectedData="selectedData" :priceInfo="priceInfo" :customQty="customQty" :textInfo="textInfo" :generalData="generalData" :attachment="attachment" @toPosition="toPosition" @addInquiry="debounceAddInquiry" @addCart="addCart"></Detail>
			</el-drawer>
			<!-- 建议弹窗-->
			<RecomendDialog :commentsDialog.sync="commentsDialog" :paramValue="selectedParamsValue" @next="recomendNext"></RecomendDialog>
			<!-- 预览弹窗-->
            <VideoPreviewDialog :pic-dialog.sync="picDialog" :zoom-pic="zoomPic" :temp-type="tempType" :selected-params-value="selectedParamsValue" @qtyAndBtnConfirm="qtyAndBtnConfirm" @qtyAndBtnNext="qtyAndBtnNext" @dialogNextStep="dialogNextStep"></VideoPreviewDialog>
			<!-- 升级数量弹窗-->
			<BaseDialog v-model="upgradeDialog" :width="device != 'mb' ? '640px' : '320px'">
				<Upgrade @closeUpgrade="upgradeDialog = false" :tipArr="tipArr" :typeName="cateData.cateName" @upgradeLevel="upgradeLevel"></Upgrade>
			</BaseDialog>
			<!-- o2o预览页面弹窗-->
			<BaseDialog v-model="showOtoDialog" :width="device != 'mb' ? '570px' : '90%'">
				<div class="otoWrap">
					<img src="~/assets/images/oto.png" alt="" />
					<p>{{ lang.p1 }}</p>
					<h3>{{ lang.p2 }}</h3>
					<p style="color: #666666">
						{{ lang.p3 }}<br />
						{{ lang.p4 }}.
					</p>

					<div class="box">
						<p class="t1">
							{{ lang.p5 }} <br />
							{{ lang.p6 }}!
						</p>
						<a href="https://www.o2o.co/previewWeb" style="text-decoration: none">
							<el-button type="primary">{{ lang.p7 }}</el-button>
						</a>
					</div>
				</div>
			</BaseDialog>
			<!--			询盘弹窗-->
			<infoDialog :infoDialogVisible.sync="infoDialogVisible" :uploadList.sync="uploadList" :otherUpload="uploadArtworkList" :recomendUpload="recomendUpload" @getValue="getValueFun"></infoDialog>
			<!--    遮罩-->
			<myMask :maskName.sync="maskName"></myMask>
			<!--  手机，ipad端预览-->
			<PreviewBtn :showArea.sync="showArea"></PreviewBtn>
			<BaseDialog v-model="noFileDialog" :width="device != 'mb' ? '485px' : '90%'" :model="false">
				<template #closeIcon>
					<div style="display: none"></div>
				</template>
				<infoUpload :infoUploadList="infoUploadList" @pushInfoList="pushInfoList" @delInfoList="delInfoList" @updateInquiry="updateInquiry" @closeInfoDialog="closeInfoDialog"></infoUpload>
			</BaseDialog>
		</template>
	</div>
</template>

<script>
import "@/plugins/element";
import VideoPlayer from "@/components/Quote/VideoPlayer.vue";
import myMask from "@/components/Quote/Mask";
import Detail from "@/components/Quote/Detail";
import BaseDialog from "@/components/Quote/BaseDialog";
import Upgrade from "@/components/Quote/Upgrade";
import Preloader from "@/components/Quote/Preloader";
import PriceText from "@/components/Quote/PriceText";
import StepUpload from "@/components/Quote/StepUpload";
import StepTime from "@/components/Quote/StepTime";
import StepQty from "@/components/Quote/StepQty";
import infoDialog from "@/components/Medals/infoDialog";
import RecomendDialog from "@/components/Quote/RecomendDialog.vue";
import quoteMixin from "@/mixins/quote";
import quoteBanChoiceMixin from "@/mixins/quoteBanChoice";
import QuoteTitle from "@/components/Quote/QuoteTitle.vue";
import PreviewBtn from "@/components/Quote/PreviewBtn.vue";
import { getQuoteConfig } from "@/assets/js/quote/quoteConfig";
import PinsSizeSelect from "~/components/Quote/PinsSizeSelect.vue";
import PublicStep from "~/components/Quote/PublicStep/index.vue";
import QuoteNav from "@/components/Medals/QuoteNav";
import VideoPreviewDialog from "@/components/Quote/VideoPreviewDialog.vue";

export default {
	head() {
		return {
			title: "Quote page",
		};
	},
	components: {
        VideoPreviewDialog,
		PreviewBtn,
		QuoteTitle,
		Detail,
		BaseDialog,
		Upgrade,
		myMask,
		VideoPlayer,
		Preloader,
		PriceText,
		StepUpload,
		StepTime,
		StepQty,
		infoDialog,
		RecomendDialog,
		PinsSizeSelect,
		PublicStep,
		QuoteNav,
	},
	mixins: [quoteMixin, quoteBanChoiceMixin],
	data() {
        const config = getQuoteConfig.call(this, this.$route.name);
		return {
			...config,
		};
	},
	methods:{
		viewAdditionalVideo() {
			this.selectQuoteParams(arguments[0], arguments[1])
		},
	},
	mounted() {
		this.applyMediaQueries(this.allStepConfig);
	},
};
</script>
<style scoped lang="scss">
@import "@/assets/css/quotePublic";
</style>
