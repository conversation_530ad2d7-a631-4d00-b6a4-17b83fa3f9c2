<template>
	<div :style="modal.style">
		<div class="bps-container">
			<div class="productContent" id="productList">
				<div class="fp-products" @click.self="setModalType({}, modal.list, 'product_crowd')">
					<div class="title" v-if="modal.list.length > 0">
						<div class="title-left">
							<b :class="modal.outer[0].icon.value" v-if="modal.outer[0].icon" @click="setModalType(modal.outer[0].icon, modal.outer, 'icon')"></b>
							<EditDiv tagName="h1" v-model:content="modal.outer[0].title.value" @click="setModalType(modal.outer[0].title, modal.outer, 'text')" />
						</div>
						<div class="title-right">
							<div class="search-area" @mouseenter="mouseEnter" @mouseleave="mouseLeave">
								<div class="search-input" :class="{ isFocus: focusInput }" @click="mouseEnter">
									<label v-if="!keyword && !focusInput">
										{{ customLabel }}
									</label>
									<input type="text" v-model="keyword" @focus="focusInput = true" @blur="focusInput = false" @input="searchProduct" />
									<div class="search-icon">
										<b class="icon-a-uspp-sousuozhuanhuan"></b>
									</div>
								</div>
								<div class="search-extend" v-show="showSearchExtend">
									<div class="top-text">
										{{ lang.topText }}
									</div>
									<div class="recommendList custom-scrollbar">
										<div class="re-item" v-for="item in sideList" :key="item.id" @click="selectProduct(item.nodeName)">
											{{ item.nodeName }}
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div class="content contentPart" @click.self="setModalType({}, modal.list, 'product_crowd')" :contentHoverIndex="0" hoverList="list">
						<div class="part2 productsType" style="position: relative" v-for="(l, li) in searchArr" :key="li" @click="setModalType(l.product_crowd, modal.list, 'product_crowd')" :childHoverIndex="li">
							<div class="item" @click="linkPage(l)" v-show="li < defaultNum">
								<div class="imgWrap">
									<pic v-if="l.product_crowd.img" :src="l.product_crowd.img.value" :alt="l.product_crowd.alt && l.product_crowd.alt.value" />
								</div>
								<h2 class="smallTitle" v-if="l.product_crowd.title">
									{{ l.product_crowd.title.value }}
								</h2>
								<div class="desText" v-if="l.product_crowd.disabled.value">Stay Tuned</div>
								<div class="desText" v-if="l.product_crowd.subTitle && l.product_crowd.price && !l.product_crowd.disabled.value">
									<div class="price">
										{{ l.product_crowd.subTitle.value || "As Low As" }}
										<CCYRate class="priceText" :price="l.product_crowd.price.value"></CCYRate>
										/pc
									</div>
								</div>
								<div class="hover-div">
									<a href="javascript:;">{{ l.product_crowd.button.value }}</a>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import { getAllProduct } from "@/api/web";
import { debounce } from "@/utils/utils";

export default {
	name: "modalProducts",
	props: {
		data: {
			type: Object,
			default: {},
		},
	},
	data() {
		return {
			focusInput: false,
			keyword: "",
			defaultNum: 8,
			showExtendIndex: -1,
			modal: {
				style: {},
				type: { index: 0, clickPosition: "outer" },
				...this.data,
			},
			sideList: [],
			loadingProduct: false,
			totalResult: 0,
			windowHeight: 0,
			showSearchExtend: false,
			timed: null,
			searchArr: [],
			customLabel: "Search",
			customLabelIndex: 0,
			debounceSearchProduct: null,
		};
	},
	computed: {
		lang() {
			return this.$store.getters.lang?.products;
		},
		list() {
			return [
				{
					name: this.lang.home,
					iconClass: "icon-right",
					to: {
						path: "/",
					},
				},
				{
					name: this.lang.allProducts,
					iconClass: "icon-right",
				},
			];
		},
	},
	watch: {
		modal: {
			handler(val) {
				if (process.env.isManage) this.$emit("update:data", val);
			},
			immediate: true,
			deep: true,
		},
	},
	methods: {
		searchProduct() {
			let productList = this.modal.list,
				name = this.keyword.toLowerCase(),
				searchArr = [];
			productList.forEach((item) => {
				let title = item.product_crowd.title.value.toLowerCase();
				if (title.indexOf(name) > -1 || name.indexOf(title)>-1) {
					searchArr.push(item);
				}
			});
			this.searchArr = searchArr;
		},
		selectProduct(name) {
			this.keyword = name;
			this.showSearchExtend = false;
			this.debounceSearchProduct();
		},
		mouseEnter() {
			clearTimeout(this.timed);
			this.focusInput = true;
			this.showSearchExtend = true;
		},
		mouseLeave() {
			if (!this.keyword) {
				this.focusInput = false;
			}
			this.timed = setTimeout(() => {
				this.showSearchExtend = false;
			}, 300);
		},
		setModalType(target, targetArray, clickType, event, other) {
			this.$setModal(this, target, targetArray, clickType, event, other)
		},
		loadMore() {
			this.loadingProduct = true;
			setTimeout(() => {
				this.defaultNum = Number.MAX_SAFE_INTEGER;
				this.loadingProduct = false;
			}, 500);
		},
		handleScroll() {
			try {
				let productList = document.getElementById("productList");
				let rect = productList.getBoundingClientRect();
				let targetTop = rect.top,
					targetHeight = rect.height;
				let windowHeight = this.windowHeight; //页面高度
				let scrollBarTop = document.body.scrollTop; // 滚动条离页面顶端的距离
				let threshold = 20; // 可以指定提前加载的距离
				if (targetTop + targetHeight < windowHeight + scrollBarTop + threshold) {
					//加载数据
					if (this.loadingProduct) {
						return false;
					}
					this.loadMore();
				}
			} catch (e) {}
		},
		linkPage(l) {
			if (process.env.isManage) {
				return false;
			}
            let url = l.product_crowd.link.value
			if (l.product_crowd.disabled && l.product_crowd.disabled.value) {
				l.showDisabledText = !l.showDisabledText;
				this.$forceUpdate();
				return;
			}
            if (url.startsWith('#')) location.href = url;
            else if (url.startsWith('http') || url.startsWith('tel') || url.startsWith('mailto')) location.href = url;
            else this.$router.push({ path: this.$store.state.language.lang + url })
		},
	},
	created() {
		this.debounceSearchProduct = debounce(this.searchProduct, 300);
		getAllProduct(process.env.isManage ? { proId: this.$store.getters["manage/getProId"] } : null).then((res) => {
			this.sideList = res.data;
		});
	},
	mounted() {
		this.searchArr = this.modal.list;
		this.windowHeight = document.documentElement.clientHeight;
		window.addEventListener("scroll", this.handleScroll, true);
		if (!process.isManage) {
			const myInterval = (fn, time) => {
				// 定义一个递归函数持续调用定时器
				let executor = (fn, time) => {
					this.priceTextTimed = setTimeout(() => {
						fn();
						executor(fn, time);
					}, time);
				};
				executor(fn, time);
			};
			myInterval(() => {
				this.customLabel = this.modal.list[this.customLabelIndex].product_crowd.title.value;
				if (this.customLabelIndex >= this.modal.list.length - 1) {
					this.customLabelIndex = 0;
				} else {
					this.customLabelIndex++;
				}
			}, 3000);
		}
	},
	beforeDestroy() {
		clearInterval(this.priceTextTimed);
		window.removeEventListener("scroll", this.handleScroll);
	},
};
</script>

<style scoped lang="scss">
.bps-container {
	.productContent {
		position: relative;
		padding: 20px;

		.fp-products {
			position: relative;
			min-height: 200px;

			& > .title {
				display: flex;
				align-items: center;
				justify-content: space-between;
				margin-bottom: 25px;

				.title-left {
					display: flex;
					align-items: center;

					b {
						margin-right: 18px;
						font-size: 26px;
						color: $color-primary;
					}

					h1 {
						font-size: 24px;
						font-weight: bold;
					}
				}
			}

			.content {
				display: grid;
				grid-template-columns: repeat(4, 1fr);
				grid-gap: 20px;

				.item {
					padding: 10px;
					text-align: center;
					transition: all 0.3s;
					cursor: pointer;

					@media (any-hover: hover) {
						&:hover {
							box-shadow: 0px 0px 40px 6px rgba(210, 210, 210, 0.45);
							transform: translateY(-8px);
						}
					}

					.imgWrap {
						position: relative;
						aspect-ratio: 312/287;

						img {
							width: 100%;
							height: 100%;
							object-fit: contain;
						}
					}

					.hover-div {
						display: flex;
						justify-content: center;
						align-items: center;

						a {
							overflow: hidden;
							display: inline-block;
							min-width: 100px;
							max-width: 150px;
							height: 25px;
							line-height: 25px;
							background: $color-primary;
							border-radius: 20px 20px 20px 20px;
							padding: 0 10px;
							color: #ffffff;
							font-size: 14px;
							text-overflow: ellipsis;
							white-space: nowrap;
							transition: background-color 0.3s;
							text-align: center;

							&:hover {
								background-color: var(--color-primary-lighten);
							}
						}
					}

					.desText {
						margin: 10px 0;
						font-size: 14px;
					}

					.smallTitle {
						margin-top: 10px;
						font-size: 20px;
						font-weight: 400;
					}
				}

				.item.disabled {
					.hover-div {
						a {
							color: #999999;
						}
					}
				}
			}
		}
	}
}

@media screen and (min-width: $mb-width) and (max-width: $pad-width) {
	.bps-container {
		.productContent {
			.fp-products {
				.content {
					display: grid;
					grid-template-columns: repeat(3, 1fr);
					grid-gap: 30px 25px;
				}
			}
		}
	}
}

@media screen and (max-width: $mb-width) {
	.bps-container {
		padding: 10px 15px;
		position: relative;
		max-width: 100%;

		.productContent {
			padding: 0;

			.fp-products {
				& > .title {
					flex-direction: column;
					align-items: flex-start;
					margin-bottom: 15px;

					.title-left {
						margin-bottom: 10px;

						b {
							margin-right: 10px;
							font-size: 14px;
						}

						h1 {
							font-size: 15px;
						}
					}

					.title-right {
						width: 100%;
					}
				}

				.content {
					grid-template-columns: repeat(2, 1fr);
					grid-gap: 5px;

					.item {
						display: block;
						position: relative;
						box-shadow: 0px 0px 19px 2px rgba(182, 182, 182, 0.46);
						border-radius: 5px;

						.desText {
							margin: 5px 0;
							font-size: 12px;

							.priceText {
								color: $color-primary;
							}
						}

						.smallTitle {
							margin-top: 10px;
							font-size: 12px;
							color: #000000;
						}

						.hover-div {
							display: none;
						}
					}
				}
			}
		}
	}
}

.search-area {
	position: relative;

	.search-input {
		overflow: hidden;
		border-radius: 20px;
		position: relative;
		cursor: pointer;
		border: 1px solid transparent;
		transition: all 0.3s;

		&.isFocus {
			border-color: $color-primary;

			input {
				background-color: #ffffff;
			}

			.search-icon {
				b {
					color: #333333;
				}
			}
		}

		label {
			position: absolute;
			left: 15px;
			top: 50%;
			transform: translateY(-50%);
			color: #777777;
		}

		.search-icon {
			position: absolute;
			right: 15px;
			top: 50%;
			transform: translateY(-50%);

			b {
				font-size: 17px;
				color: #777777;
			}
		}

		input {
			width: 360px;
			height: 40px;
			padding: 0 40px 0 15px;
			background-color: #eeeeee;
			transition: all 0.3s;
		}
	}

	.search-extend {
		position: absolute;
		top: calc(100% + 4px);
		left: 0;
		right: 0;
		padding: 15px 0;
		background: rgba(255, 255, 255, 0.7);
		-webkit-backdrop-filter: blur(10px);
		backdrop-filter: blur(10px);
		transition: all 0.3s;
		border: 1px solid $color-primary;
		border-radius: 20px;
		z-index: 10;

		.top-text {
			text-transform: uppercase;
			font-size: 12px;
			padding: 0 15px;
		}

		.recommendList {
			overflow: hidden auto;
			max-height: 300px;

			.re-item {
				height: 40px;
				line-height: 40px;
				padding: 0 15px;
				cursor: pointer;
				transition: background-color 0.3s;

				&:hover {
					background-color: $color-primary;
				}
			}
		}
	}

	@media screen and (max-width: $mb-width) {
		.search-input {
			border-radius: 10px;

			label {
				left: 10px;
				font-size: 12px;
			}

			.search-icon {
				right: 10px;

				b {
					font-size: 14px;
				}
			}

			input {
				width: 100%;
				height: 30px;
				padding: 0 30px 0 10px;
				font-size: 12px;
			}
		}

		.search-extend {
			border-radius: 10px;

			.top-text {
				text-transform: uppercase;
				font-size: 12px;
				padding: 0 15px;
			}

			.recommendList {
				max-height: 300px;

				.re-item {
					height: 30px;
					line-height: 30px;
					font-size: 14px;
				}
			}
		}
	}
}
</style>
