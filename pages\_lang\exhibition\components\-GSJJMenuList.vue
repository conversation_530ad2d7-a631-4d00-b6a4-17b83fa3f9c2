<template>
	<div class="category">
		<div class="cate_title">{{ lang.relatedCategories }}</div>

		<div class="cate_menu">
			<GSJJMenuItem class="first" v-for="(item, index) in cateList" :data="item" :key="index" />
		</div>
	</div>
</template>

<script>
import GSJJMenuItem from "./-GSJJMenuItem.vue";

export default {
	name: "GSJJMenuList",

	components: {
		GSJJMenuItem,
	},

	props: {
		cateList: { type: Array, default: () => [] },
		staticPath: { type: String, default: "s" },
	},

	computed: {
		lang() {
			return this.$store.getters.lang?.exhibition
		},
	},

	provide() {
		return {
			staticPath: this.staticPath,
		};
	}
};
</script>

<style lang="scss" scoped>
.category {
	border: 1px solid #d5d5d5;
	background-color: #fff;

	.cate_title {
		padding-left: 16px;
		height: 28px;
		line-height: 28px;
		font-size: 12px;
		font-weight: 700;
		background-color: #e6e6e6;
	}

	.cate_menu {
		padding: 0 4px;
	}
}
</style>
