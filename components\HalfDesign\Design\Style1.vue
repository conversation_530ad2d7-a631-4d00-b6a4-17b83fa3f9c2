<template>
	<div class="mb-4" :class="stepData.styleName">
		<half-design-select-tip :customText="stepData.attributeTitle" :selectedValue="shape"></half-design-select-tip>
		<slot name="stepText"></slot>
		<div class="step-content">
			<div class="step-item" :class="{ active: index === selectIndex }" v-for="(step, index) in stepData.productParamList" :key="index" @click="selectStep(step, index)">
				<div class="imgWrap">
					<img :src="step.imgDetail" :alt="step.valueName" :title="step.valueName" />
				</div>
				<div class="d-flex-center text-center name">
					<div class="text-truncate">
						{{ step.valueName }}
					</div>
				</div>
				<half-design-check-icon class="absolute-top-right"></half-design-check-icon>
			</div>
		</div>
		<template v-if="selectIndex === 0 || selectIndex === 2">
			<div class="imgArea" v-show="!uploadImg.url" @click="triggerUpload('upload')">
				<div class="iconWrap">
					<v-icon size="60">mdi-cloud-upload</v-icon>
				</div>
				<div class="tipText">
					{{ langSemiCustom.fileDrag }} <strong>{{ langSemiCustom.fileBrowse }}</strong
					><br />
					{{ langSemiCustom.fileMax30 }}<br />
					<span>{{ langSemiCustom.fileType }}</span>
				</div>
				<input type="file" accept=".jpg,.jpeg,.png,.gif,.bmp" ref="upload" @click.stop @change="uploadPic" />
			</div>
			<div class="uploadImg" v-show="uploadImg.url">
				<div class="imgWrap">
					<img :src="uploadImg.url" alt="upload" title="upload" />
					<v-icon class="close" small @click="delImg">mdi-close-circle</v-icon>
				</div>
				<div class="imgName">
					{{ uploadImg.name }}
				</div>

				<div class="changeImg" @click="triggerUpload('change')">
					<v-icon color="primary" large>mdi-cloud-upload</v-icon>
					<span>Change Logo</span>
				</div>
			</div>

			<pic-color-change :oneColor.sync="oneColor" :removeBgColor.sync="removeBgColor" :filterColor="filterColor" :uploadImg="uploadImg" :picColorList="picColorList" :oldColor="oldColor" :newColor="newColor" :colorList="colorList" :copyPicColorList="copyPicColorList" @selectPicColor="selectPicColor" @changeOneColor="changeOneColor" @changePicColor="changePicColor" @filterPicColorBefore="filterPicColorBefore" @removeBg="removeBg"> </pic-color-change>
		</template>
		<template v-if="selectIndex === 1 || selectIndex === 2">
			<div class="textArea">
				<div>
					<div>
						<v-text-field id="myInput" :value="myTextProperty.text" solo flat outlined dense hide-details label="Add Text" @input="addText"></v-text-field>
					</div>
					<div>
						<v-select :value="myTextProperty.fontFamily" :menu-props="{ bottom: true, offsetY: true }" solo flat outlined dense hide-details :items="fontsData" item-text="name" item-value="name" label="Font Family" @change="changeTextProperty($event, 'fontFamily')">
							<template #item="{ item }">
								<span :style="{ fontFamily: item.name }">{{ item.name }}</span>
							</template>
						</v-select>
					</div>
				</div>
				<div>
					<div>
						<v-menu v-model="show" :close-on-content-click="false" offset-y min-width="300">
							<template v-slot:activator="{ on, attrs }">
								<div class="color-picker" tabindex="0" v-bind="attrs" v-on="on">
									<div class="prepend">
										<div class="colorCircle" :style="{ backgroundColor: myTextProperty.fill || '#000000' }"></div>
									</div>
									<div class="con">
										{{ myTextProperty.colorName || "black" }}
									</div>
									<div class="append">
										<img src="~/static/img/icon_color.png" alt="Color Wheel" title="Color Wheel" />
									</div>
								</div>
							</template>
							<v-card class="color-picker-wrap" color="#ffffff">
								<div class="color-picker-title">Edit Colors</div>
								<div class="color-picker-list">
									<div class="color-item" :class="{ active: item.code === myTextProperty.fill }" v-for="item in colorList" :key="item.id" :style="{ backgroundColor: item.code }" :title="item.pantone" @click="changeTextProperty(item, 'fill')">
										<v-icon color="#ffffff" small> mdi-check</v-icon>
									</div>
								</div>
							</v-card>
						</v-menu>
					</div>
					<div class="d-flex">
						<div class="font-bold" :class="{ active: myTextProperty.fontWeight === 'bold' }" @click="changeTextProperty(myTextProperty.fontWeight === 'normal' || myTextProperty.fontWeight === 400 ? 'bold' : 'normal', 'fontWeight')">B</div>
						<div class="font-style" :class="{ active: myTextProperty.fontStyle === 'italic' }" @click="changeTextProperty(myTextProperty.fontStyle === 'normal' || !myTextProperty.fontStyle ? 'italic' : 'normal', 'fontStyle')">I</div>
					</div>
				</div>
			</div>
		</template>
	</div>
</template>
<script>
import { designMixin } from "@/mixins/halfDesign";

export default {
	mixins: [designMixin],
	computed: {
		fontsData() {
      		return require("@/assets/json/fontList.json");
		}
	}
};
</script>
<style scoped lang="scss">
@import "~assets/css/half.scss";

.colorCircle {
	width: 25px;
	height: 25px;
	border-radius: 50%;
	@media screen and (max-width: $mb-width) {
		width: 20px;
		height: 20px;
	}
}

.append {
	img {
		width: 25px;
		height: 25px;
	}

	@media screen and (max-width: $mb-width) {
		img {
			width: 20px;
			height: 20px;
		}
	}
}

.style1 .step-content {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	grid-gap: 10px;

	.step-item {
		position: relative;
		@include step-default;
		min-width: 0;
		cursor: pointer;

		.checkIcon {
			display: none;
		}

		.name {
			margin-top: 4px;
		}

		@media (any-hover: hover) {
			&:hover {
				border-color: $color-primary;
			}
		}

		.imgWrap {
			@include flex-center;
			height: 150px;
		}
	}

	.step-item.active {
		border-color: $color-primary;

		.checkIcon {
			display: flex;
		}
	}
}

.imgArea {
	position: relative;
	margin: 20px 0;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	text-align: center;
	border-radius: $step-border-radius;
	border: 1px dashed $border-color;
	background-color: $background-color;
	padding: 20px 10px;
	cursor: pointer;

	input[type="file"] {
		position: absolute;
		top: 0;
		left: 0;
		z-index: -1;
		opacity: 0;
		height: 0;
	}

	&:hover {
		border-color: $color-primary;
	}

	strong {
		color: $color-primary;
	}

	span {
		color: $gray-text;
	}
}

.uploadImg {
	display: flex;
	align-items: center;
	margin-top: 20px;

	.imgWrap {
		position: relative;
		width: 150px;
		height: 100px;
		padding: 10px;
		@include step-default;

		.close {
			position: absolute;
			top: 0;
			right: 0;
			transform: translate(50%, -50%);
			cursor: pointer;
		}

		img {
			width: 100%;
			height: 100%;
			object-fit: contain;
		}
	}

	.imgName {
		flex: 1;
		padding: 0 10px;
	}

	.changeImg {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		color: $color-primary;
		cursor: pointer;
	}
}

.textArea {
	& > div {
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		grid-gap: 20px;
		margin: 10px 0;
	}

	.font-bold,
	.font-style {
		display: flex;
		justify-content: center;
		align-items: center;
		width: 40px;
		height: 40px;
		background: #fff;
		border: 1px solid rgba(0, 0, 0, 0.38);
		border-radius: 5px;
		margin-right: 10px;
		cursor: pointer;
		transition-duration: 0.15s;
		transition-property: color;
		transition-timing-function: cubic-bezier(0.25, 0.8, 0.25, 1);

		&:hover {
			border-color: #333333;
		}

		&.active {
			border-color: $color-primary;
			border-width: 2px;
			color: $color-primary;
		}
	}
}

.color-picker {
	display: flex;
	align-items: center;
	width: 100%;
	padding: 0 12px;
	border: 1px solid rgba(0, 0, 0, 0.38);
	border-radius: 4px;
	min-height: 40px;
	cursor: pointer;
	transition-duration: 0.15s;
	transition-property: color;
	transition-timing-function: cubic-bezier(0.25, 0.8, 0.25, 1);

	&:hover {
		border-color: #333333;
	}

	&:focus {
		border-color: $color-primary !important;
		border-width: 2px;
		padding: 0 11px;
	}

	.con {
		flex: 1;
		padding: 0 10px;
	}
}

@include respond-to(mb) {
	.v-input ::v-deep {
		.v-label {
			font-size: 14px;
		}

		input {
			font-size: 14px;
		}
	}
	.v-select ::v-deep {
		.v-select__selection {
			font-size: 14px;
		}
	}
	.style1 .step-content {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		grid-gap: 5px;

		.step-item {
			.imgWrap {
				@include flex-center;
				height: 80px;
			}
		}
	}
	.imgArea {
		margin: 10px 0;
		padding: 10px;
		font-size: 12px;
	}
	.uploadImg {
		margin-top: 10px;

		.imgWrap {
			width: 80px;
			height: 80px;
			padding: 5px;
		}

		.imgName {
			flex: 1;
			padding: 0 10px;
			font-size: 12px;
		}

		.changeImg {
			font-size: 12px;
		}
	}
	.textArea {
		& > div {
			grid-gap: 5px;
			margin: 10px 0;
		}

		.font-bold,
		.font-style {
			margin-right: 5px;
		}
	}
}
</style>
