<template>
	<div>
		<div class="swiper myswiper1" ref="swiper1">
			<div class="swiper-wrapper">
				<div class="swiper-slide" v-for="(item, index) in imgList" :key="index">
					<img :src="item.url" :alt="item.alt" :title="item.alt" />
				</div>
			</div>
		</div>
		<div class="thumbs-box">
			<div class="thumbs-box__button swiper-button-prev"></div>
			<div class="swiper myswiper2" ref="swiper2">
				<div class="swiper-wrapper">
					<div class="swiper-slide" v-for="(item, index) in imgList" :key="index">
						<img :src="item.url" :alt="item.alt" :title="item.alt" />
					</div>
				</div>
			</div>
			<div class="thumbs-box__button swiper-button-next"></div>
		</div>
	</div>
</template>

<script>
export default {
	name: "GSJJSwiper",

    props: {
		imgList: Array,
	},

	methods: {
		initSwiper() {
			// 初始化缩略图轮播
			this.thumbnailSwiper = new Swiper(this.$refs.swiper2, {
				slidesPerView: 3,
				spaceBetween: 20,
				watchSlidesVisibility: true, //防止不可点击
			});

			// 初始化主轮播,并关联缩略图
			this.mainSwiper = new Swiper(this.$refs.swiper1, {
				grabCursor: true,
				navigation: {
					prevEl: ".swiper-button-prev",
					nextEl: ".swiper-button-next",
				},
				thumbs: {
					swiper: this.thumbnailSwiper,
				},
			});
		},
	},

	mounted() {
		this.initSwiper();
	},
};
</script>

<style lang="scss" scoped>
.thumbs-box {
	--swiper-navigation-size: 1rem;
	position: relative;
	display: flex;
	align-items: center;
	margin: 1rem auto;
	width: 22.5rem;
	height: 5.625rem;

	.thumbs-box__button {
		display: flex !important;
		align-items: center;
		margin-top: unset;
		position: unset;
		flex-shrink: 0;
		width: 1.5rem;
		height: 100%;
		font-weight: 700;
		color: var(--text-primary);
	}

	.swiper {
		flex: 1;
		padding: 0 0.625rem;
		border-left: 2px solid #c9c9c9;
	}

	.swiper-slide {
		cursor: pointer;
		&::before {
			content: "";
			margin: 0 auto 2px;
			display: block;
			border-style: solid;
			border-width: 0 5.5px 5px 5.5px;
			border-color: transparent transparent rgb(51, 51, 51);
			width: 0;
			height: 0;
			visibility: hidden;
		}
		&.swiper-slide-thumb-active::before {
			visibility: visible;
		}
	}

	img {
		width: 5.25rem;
		height: 5.25rem;
	}

	@include respond-to(mb) {
		display: none;
	}
}
</style>
