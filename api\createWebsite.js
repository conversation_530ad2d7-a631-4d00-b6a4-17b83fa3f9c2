//获取风格列表
import {request} from "@/utils/request";

export function listAllWebsiteStyle(data) {
    return request({
        url: '/app/retailer/websiteStyle/listWebsiteStyle',
        method: 'post',
        data,
    })
}

// 获取风格主题颜色
export function websiteStyleColor(data) {
    return request({
        url: '/app/retailer/websiteStyle/websiteStyleColor/listThemeColorByWSId',
        method: 'post',
        data,
    })
}

// 未登录创建Site
export function createNewSite(data) {
    return request({
        url: '/retailer/webSite/addTemporary',
        method: 'post',
        data,
    })
}

// 已登录创建Site
export function addNewSite(data) {
    return request({
        url: '/retailer/webSite/add',
        method: 'post',
        data,
    })
}


// 页面初始化
export function initWebsitePage(data) {
    return request({
        url: '/app/retailer/page/projectLayout/initWebsitePage',
        method: 'post',
        data,
    })
}

// 获取页面详情
export function getWebSiteInfo(id) {
    return request({
        url: '/retailer/webSite/getWebSiteInfo?id=' + id,
        method: 'get'
    })
}


// 初始化网站所有者信息
export function initOwnerInfo(data) {
    return request({
        url: '/retailer/projectOwner/initSysProOwner',
        method: 'post',
        data
    })
}



// 通过site名字查询site
export function searchBySiteName(name) {
    return request({
        url: '/app/systemProject/getProByName?proName=' + name,
        method: 'get',
    })
}

