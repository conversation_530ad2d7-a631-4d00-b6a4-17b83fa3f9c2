<template>
	<BaseDialog :value="editLogoDialog" @update="updateDialog" :persistent="true" :width="device != 'mb' ? '800px' : '90%'">
		<div class="editWrap">
			<div class="editTitle">Edit Logo & Color</div>
			<div class="content">
				<v-overlay :value="overlay" :absolute="true">
					<v-progress-circular indeterminate size="64"></v-progress-circular>
				</v-overlay>
				<div id="swiperLogo">
					<div class="swiper" ref="swiper">
						<div class="swiper-wrapper">
							<div class="swiper-slide" v-for="(item, index) in newLogoList" :key="index">
								<canvas :id="`logoCanvas${index}`"></canvas>
								<span>{{ item.original_filename }}</span>
							</div>
						</div>
					</div>
					<div class="swiper-button-prev"></div>
					<div class="swiper-button-next"></div>
				</div>
				<div class="undoAndRedo">
					<b class="icon-Undo" :class="{ active: historyManager.currentIndex > 0 }" @click="undo"></b>
					<b class="icon-Undo redo" :class="{ active: historyManager.currentIndex < historyManager.history.length - 1 }" @click="redo"></b>
				</div>
			</div>
			<div v-if="newLogoList.length" class="edit-area" :disabled="overlay">
				<div class="item">
					<div class="left-label">{{ langSemiCustom.removeBackground }}</div>
					<div class="right-con">
						<v-switch dense hide-details v-model="newLogoList[currentIndex].removeBg" @change="removeBg"></v-switch>
					</div>
				</div>
				<div class="item editColor">
					<div class="left-label">{{ langSemiCustom.editColors }}</div>
					<v-menu content-class="changeColorMenu" :close-on-content-click="false" offset-y>
						<template v-slot:activator="{ on, attrs }">
							<div class="right-con" v-bind="attrs" v-on="on">
								<div class="color-item" v-show="index < 2" v-for="(item, index) in picColorList" :key="index" :style="{ backgroundColor: item.color }" @click="selectPicColor(item)"></div>
								<div class="moreColor" v-show="picColorList.length >= 2">
									<span>...</span>
								</div>
								<v-icon size="30">mdi-chevron-right</v-icon>
							</div>
						</template>
						<v-card class="changeColor-picker-wrap">
							<div class="color-picker-title">{{ langSemiCustom.chooseChange }}</div>
							<div class="color-picker-list">
								<div class="color-item" :class="{ active: item.color === oldColor }" v-for="(item, index) in picColorList" :key="index" :style="{ backgroundColor: item.color }" @click="selectPicColor(item)">
									<v-icon color="#ffffff" small>mdi-check</v-icon>
								</div>
							</div>
							<div class="color-picker-title" v-show="oldColor">{{ langSemiCustom.chooseColor }}</div>
							<div class="color-picker-list" v-show="oldColor">
								<div class="color-item" :class="{ active: newColor === item.code }" v-for="item in colorList" :key="item.id" :style="{ backgroundColor: item.code }" :title="item.pantone" @click="changePicColor(item)">
									<v-icon color="#ffffff" small>mdi-check</v-icon>
								</div>
							</div>
						</v-card>
					</v-menu>
				</div>
				<div class="item">
					<div class="left-label">Invert Colors</div>
					<div class="right-con">
						<v-switch dense hide-details v-model="newLogoList[currentIndex].invert" @change="changeFilter"></v-switch>
					</div>
				</div>
				<div class="contrast">
					<div class="label">Adjust slider to change your logo's contrast</div>
					<div style="margin: 10px">
						<v-slider v-model="newLogoList[currentIndex].contrast" rever color="#929292" thumb-color="var(--color-primary)" track-color="#929292" hide-details max="200" min="0" @change="changeFilter"></v-slider>
						<div class="slide-labels">
							<span class="label-item" v-for="item in sliceConfig.labels">
								{{ item }}
							</span>
						</div>
					</div>
				</div>
			</div>
			<div class="foot">
				<v-btn depressed color="primary" style="text-transform: capitalize" @click="save">Save & Continue </v-btn>
			</div>
		</div>
	</BaseDialog>
</template>
<script>
import BaseDialog from "@/components/Quote/BaseDialog.vue";
import { changeImgColor, colorRgb, deepClone, loadImage, analyzeImageColor, removeImageColor, HistoryManager } from "@/utils/utils";
import canvas from "assets/js/halfDesign/canvas";

export default {
	name: "EditLogo",
	props: {
		editLogoDialog: {
			type: Boolean,
		},
		defaultIndex: {
			type: Number,
			default: 0,
		},
	},
	data() {
		return {
			oldColor: "",
			newColor: "",
			picColorList: [],
			overlay: false,
			currentIndex: 0,
			newLogoList: [],
			myswiper: null,
			ignoreColor: [],
		};
	},
	computed: {
		device() {
			return this.$store.state.device;
		},
		sliceConfig() {
			if (this.device === "pc") {
				return {
					labels: ["0%", "10%", "20%", "30%", "40%", "50%", "60%", "70%", "80%", "90%", "100%"],
					step: 10,
				};
			} else {
				return {
					labels: ["0%", "20%", "40%", "60%", "80%", "100%"],
					step: 20,
				};
			}
		},
		langSemiCustom() {
			return this.$store.getters.lang?.semiCustom;
		},
		colorList() {
			return this.$store.state.colorList;
		},
		logoList() {
			return deepClone(this.$store.state.halfDesign.editLogoList);
		},
		currentItem() {
			return this.newLogoList[this.currentIndex];
		},
		currentUrl() {
			return this.currentItem.secure_url;
		},
		historyManager() {
			return this.currentItem?.historyManager || { currentIndex: 0, history: [] };
		},
	},
	components: { BaseDialog },
	methods: {
		getState() {
			let currentItem = this.currentItem;
			return {
				secure_url: currentItem.secure_url,
				removeBg: currentItem.removeBg,
				oldColor: this.oldColor,
				newColor: this.newColor,
				invert: currentItem.invert,
				contrast: currentItem.contrast,
			};
		},
		updateState(newState) {
			let currentState = deepClone(newState);
			this.historyManager.addState(currentState);
		},
		undo() {
			const prevState = this.historyManager.undo();
			if (prevState) {
				let currentState = deepClone(prevState);
				// 更新 UI 或执行相应操作
				this.oldColor = currentState.oldColor;
				this.newColor = currentState.newColor;
				this.currentItem.contrast = currentState.contrast;
				this.currentItem.invert = currentState.invert;
				this.currentItem.removeBg = currentState.removeBg;
				this.currentItem.secure_url = currentState.secure_url;
				this.imgToCanvas(this.currentItem, this.currentIndex).then(() => {
					this.setCanvasFilter();
				});
			}
		},
		redo() {
			const nextState = this.historyManager.redo();
			if (nextState) {
				let currentState = deepClone(nextState);
				this.oldColor = currentState.oldColor;
				this.newColor = currentState.newColor;
				this.currentItem.contrast = currentState.contrast;
				this.currentItem.invert = currentState.invert;
				this.currentItem.removeBg = currentState.removeBg;
				this.currentItem.secure_url = currentState.secure_url;
				this.imgToCanvas(this.currentItem, this.currentIndex).then(() => {
					this.setCanvasFilter();
				});
			}
		},
		save() {
			let picArr = canvas.getElementByType("image");
			let objArr = [];
			this.newLogoList.forEach((item, index) => {
				let myCanvas = document.querySelector(`#logoCanvas${index}`);
				item.secure_url = myCanvas.toDataURL();
				//替换画布中的图片,同步更新vuex中的图片列表
				if (item.hasChange) {
					let id = item.id;
					let findImg = picArr.find((img) => img.id === id);
					if (findImg) {
						canvas.replaceImg(item.secure_url, findImg);
					}
					this.$store.commit("halfDesign/updateImgItem", item);
					this.$Bus.$emit("selectImgColorEdit", {
						input: {},
						upload: item,
					});
				}
			});
			this.$store.commit("halfDesign/setEditLogoDialog", false);
		},
		changeFilter() {
			this.setCanvasFilter().then(() => {
				this.updateState(this.getState());
			});
		},
		setCanvasFilter() {
			return new Promise((resolve) => {
				const canvas = document.querySelector(`#logoCanvas${this.currentIndex}`);
				const context = canvas.getContext("2d");
				let invert = this.currentItem.invert;
				let contrast = this.currentItem.contrast;
				this.currentItem.hasChange = true;
				loadImage(this.currentUrl).then((image) => {
					context.drawImage(image, 0, 0);
					context.filter = `${invert ? "invert(100%)" : "invert(0%)"} contrast(${Math.abs(contrast - 200)}%)`;
					context.drawImage(image, 0, 0);
					resolve();
				});
			});
		},
		analyzeImg(url) {
			return new Promise((resolve) => {
				analyzeImageColor(url, {
					ignoreArr: this.ignoreColor,
					keepCount: 9,
					removeSimilarColors: true,
				}).then((res) => {
					this.picColorList = res;
					resolve(res);
				});
			});
		},
		selectPicColor(item) {
			this.oldColor = item.color;
		},
		changePicColor(item) {
			if (this.overlay) {
				return false;
			}
			this.overlay = true;
			this.newColor = item.code;
			changeImgColor(
				this.currentUrl,
				{
					oldColor: this.oldColor,
					newColor: item.code,
				},
				async (base64) => {
					this.oldColor = colorRgb(item.code);
					this.currentItem.secure_url = base64;
					this.currentItem.hasChange = true;
					await this.imgToCanvas(this.currentItem, this.currentIndex);
					this.setCanvasFilter();
					this.analyzeImg(base64);
					this.overlay = false;
					this.updateState(this.getState());
				}
			);
		},
		updateDialog(val) {
			this.$store.commit("halfDesign/setEditLogoDialog", val);
		},
		async removeBg(val) {
			if (!val) {
				this.currentItem.secure_url = this.currentItem.removeBgBeforeLink;
				await this.imgToCanvas(this.currentItem, this.currentIndex);
				this.setCanvasFilter();
				this.analyzeImg(this.currentUrl);
				this.updateState(this.getState());
				return;
			}
			this.currentItem.removeBgBeforeLink = this.currentUrl;
			this.currentItem.hasChange = true;
			try {
				this.currentItem.secure_url = await removeImageColor(this.currentUrl);
				await this.imgToCanvas(this.currentItem, this.currentIndex);
				this.setCanvasFilter();
				this.analyzeImg(this.currentUrl);
				this.updateState(this.getState());
			} catch (e) {}
		},
		imgToCanvas(item, index) {
			return new Promise((resolve) => {
				// 获取 Canvas 元素和上下文
				let canvas = document.querySelector(`#logoCanvas${index}`);
				let ctx = canvas.getContext("2d");
				// 创建一个图像元素
				let img = new Image();
				// 图像加载完成后执行绘制操作
				img.onload = function () {
					// 设置 Canvas 尺寸与图像尺寸一致
					canvas.width = img.width;
					canvas.height = img.height;
					// 将图像绘制到 Canvas
					ctx.drawImage(img, 0, 0);
					resolve();
				};
				img.crossOrigin = "anonymous";
				// 设置图像源
				img.src = item.secure_url;
			});
		},
		initCanvas() {
			let len = this.newLogoList.length;
			for (let i = 0; i < len; i++) {
				let item = this.newLogoList[i];
				this.imgToCanvas(item, i);
			}
		},
		async initComponent() {
			let that = this;
			this.newLogoList = this.logoList.map((item) => {
				return Object.assign({}, item, {
					// removeBg: false,
					invert: false,
					contrast: 100,
					// removeBgBeforeLink: "",
					hasChange: false,
					historyManager: new HistoryManager(),
				});
			});
			await this.$nextTick();
			this.myswiper = new Swiper(this.$refs.swiper, {
				slidesPerView: 1,
				spaceBetween: 0,
				watchSlidesVisibility: true, //防止不可点击
				grabCursor: true,
				navigation: {
					nextEl: "#swiperLogo .swiper-button-next",
					prevEl: "#swiperLogo .swiper-button-prev",
				},
				initialSlide: that.defaultIndex,
				on: {
					slideChange: function () {
						that.currentIndex = this.activeIndex;
						that.analyzeImg(that.newLogoList[that.currentIndex].secure_url);
					},
				},
			});
			this.analyzeImg(that.newLogoList[that.currentIndex].secure_url);
			this.initCanvas();
			this.updateState(this.getState());
		},
	},
	mounted() {
		this.initComponent();
	},
};
</script>

<style scoped lang="scss">
@import "~assets/css/half.scss";

.changeColorMenu {
	.changeColor-picker-wrap {
		overflow: auto;
		max-height: 300px;
		background-color: #ffffff;
		width: 480px;

		.color-picker-title {
			text-align: left;
		}

		.color-picker-list {
			display: grid;
			grid-template-columns: repeat(auto-fill, 30px);
			grid-gap: 10px;
			padding: 10px;
		}
	}
}

#swiperLogo {
	--swiper-navigation-color: #4d4d4d;
	/* 单独设置按钮颜色 */
	--swiper-navigation-size: 30px;
	/* 设置按钮大小 */
	.swiper-button-next,
	.swiper-button-prev {
		width: 46px;
		height: 46px;
		font-weight: 700;
	}
}

.editWrap {
	padding: 20px 10px;

	@include respond-to(mb) {
		font-size: 12px;
	}

	::v-deep .v-slider__thumb {
		z-index: 10;
	}

	.editTitle {
		margin-bottom: 10px;
		text-align: center;
		font-weight: 700;
		@include respond-to(mb) {
			font-size: 14px;
		}
	}

	.content {
		position: relative;
		background-color: #f7f7f7;

		.undoAndRedo {
			position: absolute;
			right: 10px;
			top: 10px;
			font-size: 20px;
			z-index: 1;
			cursor: pointer;
			user-select: none;

			b {
				margin: 0 4px;
				color: #999999;

				&.active {
					color: #333333;
				}
			}

			.redo {
				transform: scaleX(-1);
			}
		}

		#swiperLogo {
			position: relative;
			height: 300px;

			@include respond-to(mb) {
				height: 178px;
			}

			.swiper {
				height: 100%;

				.swiper-slide {
					display: flex;
					flex-direction: column;
					justify-content: center;
					align-items: center;

					canvas {
						max-height: 80%;
					}

					span {
						margin-top: 10px;
					}
				}
			}
		}
	}

	.foot {
		margin-top: 20px;
		text-align: center;

		.v-btn {
			width: 220px;
		}
	}

	.edit-area {
		.item {
			display: flex;
			align-items: center;
			height: 56px;
			padding: 10px 0;
			border-bottom: 1px solid $border-color;

			.left-label {
				flex: 1;
			}

			.right-con {
				flex: 0 1 auto;
				display: flex;
				align-items: center;

				::v-deep .v-input {
					margin-top: 0;
					padding-top: 0;
				}
			}
		}

		.contrast {
			padding: 10px 0 20px;
			border-bottom: 1px solid $border-color;

			.slide-labels {
				display: flex;
				justify-content: space-between;
				align-items: center;
				font-size: 12px;

				.label-item {
					position: relative;
				}

				.label-item::before {
					content: "";
					position: absolute;
					top: -18px;
					left: 50%;
					transform: translateX(-50%);
					display: inline-block;
					width: 4px;
					height: 4px;
					background-color: rgba(0, 0, 0, 0.5);
					transition: 0.3s cubic-bezier(0.25, 0.8, 0.5, 1);
					border-radius: 0;
					pointer-events: none;
				}

				.label-item:last-child::before {
					transform: translateX(100%);
				}
			}
		}

		.editColor {
			.right-con {
				.color-item {
					display: inline-block;
					width: 26px;
					height: 26px;
					margin-left: 5px;
					border: 1px solid rgba(0, 0, 0, 0.1);
					border-radius: 50%;
					cursor: pointer;

					&:hover {
						border: 1px solid rgba(0, 0, 0, 0.4);
					}
				}

				.moreColor {
					position: relative;
					display: inline-block;
					width: 15px;
					height: 26px;
					cursor: pointer;

					span {
						position: absolute;
						left: 50%;
						transform: translateX(-50%);
						bottom: 0;
					}
				}
			}
		}
	}
}
</style>
