import { request } from '~/utils/request'

//获取下拉框
export function getRetailerAllHalfCate(data) {
	return request({
		url: '/retailer/product/getRetailerAllHalfCate',
		method: 'get',
		params:data
	})
}

//获取列表
export function getRetailerTwoNameSemiProductList(data) {
	return request({
		url: '/retailer/product/getRetailerTwoNameSemiProductList',
		method: 'get',
		params:data
	})
}

//编辑回显
export function getProductById(data) {
	return request({
		url: '/retailer/product/getProductById',
		method: 'get',
		params:data
	})
}

//编辑保存
export function saveOrUpdateRetailerTwoProductName(data) {
	return request({
		url: '/retailer/product/saveOrUpdateRetailerTwoProductName',
		method: 'post',
		data:data
	})
}

//重新翻译
export function reTranslateProductInfo(data) {
	return request({
		url: '/retailer/product/reTranslateProductInfo',
		method: 'post',
		data:data
	})
}