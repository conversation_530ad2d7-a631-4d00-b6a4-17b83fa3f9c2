<template>
	<v-dialog
		:value="customerDialog"
		@input="$emit('update:customerDialog',false)"
		width="633"
	>
		<v-card class="customerDialogCard">
			<div class="cus-title">
				Customer Management
			</div>
			<div>
				<div class="box" :class="{active:isVip===2}" @click="confirmVip(2)">
					<div class="box-left">
						<div>
							<b class="icon-a-jxs-djszhuanhuan" style="color: #f1ad19;margin-right: 4px;"></b>
						</div>
						<div class="text">VIP & Loyal Customers</div>
					</div>
					<ul class="box-con">
						<li class="mb-3">Pay monthly, reach an offline payment agreement, pay by installments,
							pay by CC and cash on delivery.
						</li>

						<li>If the customer confirms that the artwork does not pay, 020 will
							automatically
						</li>
					</ul>
					<div class="circle">
						<v-icon small color="white">mdi-check</v-icon>
					</div>
				</div>
				<div class="box" :class="{active:isVip===1}" @click="confirmVip(1)">
					<div class="box-left">
						<div class="text">General Customers</div>
					</div>
					<ul class="box-con">
						<li>Online payment must be successful, we deduct the purchase cost to start
							production.
						</li>
					</ul>
					<div class="circle">
						<v-icon small color="white">mdi-check</v-icon>
					</div>
				</div>
			</div>
		</v-card>
	</v-dialog>
</template>

<script>
export default {
	props:['customerDialog','isVip'],
	methods:{
		confirmVip(val){
			this.$emit('confirmVip',val)
		}
	}
}
</script>

<style scoped lang="scss">
.customerDialogCard {
	padding: 30px 16px 10px;

	.cus-title {
		text-align: center;
		font-size: 18px;
		color: #333333;
		margin-bottom: 13px;
	}

	.box {
		position: relative;
		display: flex;
		height: 110px;
		background: #FFFFFF;
		border: 1px solid #E6E6E6;
		border-radius: 6px;
		margin-bottom: 10px;
		font-size: 12px;
		padding: 1px;
		cursor: pointer;

		.circle {
			display: flex;
			justify-content: center;
			align-items: center;
			position: absolute;
			top: 10px;
			right: 10px;
			width: 18px;
			height: 18px;
			background: #FFFFFF;
			border: 1px solid #CCCCCC;
			border-radius: 50%;
		}

		&.active {
			border-color: var(--v-primary-base);

			.box-left {
				background-color: var(--v-primary-base);
				color: #ffffff;

				&::after {
					border-top: 14px solid transparent;
					border-left: 14px solid var(--v-primary-base);
					border-bottom: 14px solid transparent;
				}
			}

			.circle {
				border-color: var(--v-primary-base);
				background-color: var(--v-primary-base);
			}
		}

		&:hover {
			border-color: var(--v-primary-base)
		}

		.box-left {
			position: relative;
			flex-shrink: 0;
			display: flex;
			justify-content: center;
			align-items: center;
			width: 100px;
			background: #E5E5E5;
			border-radius: 6px 0 0 6px;
			text-align: center;
			padding: 10px;

			&::after {
				content: '';
				position: absolute;
				right: -13px;
				width: 0;
				height: 0;
				border-top: 14px solid transparent;
				border-left: 14px solid #e5e5e5;
				border-bottom: 14px solid transparent;
			}
		}

		.box-con {
			display: flex;
			flex-direction: column;
			justify-content: center;
			padding-left: 28px;
			padding-right: 80px;
			flex: 1;
		}
	}
}
</style>
