<template>
	<EnamelPinsExhibition
		v-if="proId === 3"
		v-bind="{
			productList,
			total,
			pages,
			formData,
			cateList,
			currentCate,
		}"
	/>

	<GSJJExhibition v-else v-bind="{ galleryList, cateList, currentCate }" />
</template>

<script>
import { getCateList, getProductGalleryList, getGalleryList } from "@/api/web";
import { findInTreeArray } from "@/utils/utils";

import EnamelPinsExhibition from "./components/-EnamelPinsExhibition.vue";
import GSJJExhibition from "./components/-GSJJExhibition.vue";

export default {
	watchQuery: ["Keyword"],

	components: { EnamelPinsExhibition, GSJJExhibition },

	async asyncData({ store, error, route }) {
		try {
			if (store.state.proId === 3) {
				let cateListData = await getCateList();
				let cateList = cateListData.data;
				let formData = {
					page: 1,
					pageSize: 20,
					cateId: 0,
				};
				if (route?.params?.cateName) {
					formData.cateId = cateList.find((item) => {
						return item.categoryEn.replace(/\s+/g, "-").toLowerCase() === route?.params?.cateName;
					}).id;
				}
				let currentCate = cateList.find((item) => {
					return item.id == formData.cateId;
				});
				let listData = await getProductGalleryList(formData);
				let productList = listData.data.records;
				return {
					productList,
					total: listData.data.total,
					pages: listData.data.pages,
					formData,
					cateList,
					currentCate,
				};
			} else {
				const params = {
					page: route.query.Keyword ? 1 : route.params.page || 1,
					pageSize: 27,
					pageUrl: route.params.cateName,
					keyWord: route.query.Keyword,
				};
				const {
					data: { galleryList, cateList },
				} = await getGalleryList(params);
				let currentCate = null;
				if (route.params.cateName) {
					currentCate = findInTreeArray(
						cateList,
						(item) => {
							return item.pageUrl === route.params.cateName;
						},
						"childList",
					);
				}
				return {
					galleryList,
					cateList,
					currentCate,
				};
			}
		} catch (e) {
			return error({ statusCode: 404 });
		}
	},

	head() {
		// 默认SEO配置
		const defaultSeoConfig = {
			3: {
				description:
					"Browse our exhibition to get wonderful design ideas for your own custom enamel pins. EnamelPins.com have Automated Quotation System and Online Designing System, It's very advanced.",
				keywords:
					"enamel pins, custom enamel pins, hard enamel pins, soft enamel pins, sandblast pins, 3d or cutout pins, vintage pins, offset printed pins",
				title: "Exhibition All Prototype of Custom Enamel Pins | EnamelPins.com",
			},
			1: {
				description:
					"Browse our exhibition to get wonderful design ideas for your own custom pins. GS-JJ is a trusted pin maker in lapel pins, Medals, Challenge Coins and more",
				keywords: "Custom pins, Exhibition, pin gallery, custom lapel pins, lapel pins, medals, challenge coins, lanyards, key chains",
				title: "Exhibition | Pin Gallery | Custom Pins - GS-JJ.com ®",
			},
		};

		// 如果不是特定产品页面，返回默认标题
		if (![1, 3].includes(this.proId)) {
			return { title: "Exhibition" };
		}

		// 获取当前分类的SEO信息或使用默认配置
		const seoConfig = {
			seoDescriptionEn: this.currentCate?.seoDescriptionEn || this.currentCate?.seoDescription || defaultSeoConfig[this.proId].description,
			seoKeywordEn: this.currentCate?.seoKeywordEn || this.currentCate?.seoKeyword || defaultSeoConfig[this.proId].keywords,
			seoTitleEn: this.currentCate?.seoTitleEn || this.currentCate?.seoTitle || defaultSeoConfig[this.proId].title,
		};

		// 构建meta标签
		const meta = [
			{
				hid: "description",
				name: "description",
				content: seoConfig.seoDescriptionEn,
			},
			{
				hid: "keywords",
				name: "keywords",
				content: seoConfig.seoKeywordEn,
			},
		];

		return {
			title: seoConfig.seoTitleEn,
			meta,
		};
	},

	computed: {
		proId() {
			return this.$store.state.proId;
		},
	},
};
</script>
