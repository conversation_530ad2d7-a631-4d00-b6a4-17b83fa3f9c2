<template>
	<div>
		<div class="box-border">
			<i class="el-icon-close" @click="closeMask"></i>
		</div>
		<h3 class="step-title">
			<span> {{ index }}</span>
			{{ itemData.alias ? itemData.alias : itemData.paramName }}
		</h3>
		<div class="step-box">
			<div class="uploadArea">
				<div @click="openUpload" style="cursor: pointer">
					<p class="t1">
						<span
							class="circle2"
							:class="{
								active: uploadList.length > 0,
							}"
						></span
						><b class="icon-shangchuan uploadIcon"></b>&nbsp;{{ lang.UploadFile }}
					</p>
					<p class="t2">{{ lang.notes }}</p>
					<div class="upload-box" ref="uploadBox" :class="{ dropActive: dropActive }">
						<div class="uploadList">
							<template v-if="uploadList.length">
								<ul>
									<li v-for="(item, index) in uploadList" class="uploadItem" :key="item.secure_url">
										<span>{{ item.original_filename }}</span>
										<div>
											<b class="icon-check myIcon" style="color: #0cbd5f"></b>
											<span @click.stop="delUploadImg(index)">
												<b class="icon-shanchu2 myIcon" style="color: #b6b0b0"></b>
											</span>
										</div>
									</li>
								</ul>
							</template>
							<template v-else>
								<b class="icon-shangchuan uploadIcon"></b>
							</template>
						</div>
						<div>
							<div class="upload-btn" style="text-align: center">
								<div class="uploadBtnWrap">
									<button type="button" :disabled="isUpload" :class="{ isDisabled: isUpload }">
										{{ lang.Browse }}
									</button>
									<input type="file" ref="upload" :accept="acceptFileType" multiple @change="uploadPic" />
								</div>
							</div>
							<div class="tips" v-if="!no">
								<div>
									{{ lang.p9 }}.
									<el-tooltip popper-class="cusToolTip" effect="light" v-if="itemData.tips" :content="itemData.tips" placement="top-start">
										<b class="icon-wenhao1"></b>
									</el-tooltip>
								</div>
								<div>{{ lang.p10 }},</div>
								<div>
									{{ lang.p11 }}
									<el-button @click="toDZ" style="color: #0066cc; text-decoration: underline" type="text">
										{{ lang.p12 }}
									</el-button>
								</div>
							</div>
							<div class="tips" v-else>
								<div>
									{{ lang.p9 }}.
									<el-tooltip v-if="itemData.tips" popper-class="cusToolTip" effect="light" :content="itemData.tips" placement="top-start">
										<b class="icon-wenhao1"></b>
									</el-tooltip>
								</div>
							</div>
						</div>
					</div>
				</div>

				<div class="later" @click="updateCheckBox($event)">
					<span
						class="circle2"
						:class="{
							active: isUpload,
						}"
					></span>
					<span class="later-text">{{ lang.Uploadbyemaillater }}</span>
					<!-- <MySwitch :active="isUpload" style="padding-left: 5px; padding-right: 5px"></MySwitch> -->
					<!-- <span v-show="isUpload">({{ lang.p26 }})</span> -->
				</div>
				<!-- v-show="isUpload" -->
				<span class="t2">
					{{ lang.p26 }}
					<a @click.stop="" class="click_text" :href="`mailto:${userEmail}`">
						{{ userEmail }} </a
					>.
				</span>
				<div v-show="!isUpload" style="height: 22px"></div>
			</div>
			<div class="editArea">
				<p class="t1">{{ lang.OrderComments }}</p>
				<div style="flex: 1">
					<div class="pcInput" style="height: 100%">
						<el-input type="textarea" :value="remark" @input="updateRemark" :placeholder="lang.placeholder1"></el-input>
					</div>
					<div class="mbInput">
						<el-input type="textarea" :rows="4" :value="remark" @input="updateRemark" :placeholder="lang.placeholder1"></el-input>
					</div>
				</div>
			</div>
		</div>
		<div class="confirmBtnWrap">
			<QuoteBtn @click.native="showMaskFn(itemData.paramName)">{{ lang.next }}</QuoteBtn>
		</div>
	</div>
</template>

<script>
import PriceText from "@/components/Quote/PriceText";
import QuoteBtn from "@/components/Quote/QuoteBtn";
import MySwitch from "@/components/Medals/MySwitch";
import { uploadFile } from "@/utils/oss";
import {acceptFileType, checkFile} from "@/utils/validate";

export default {
	props: {
		itemData: Object,
		index: Number,
		uploadList: Array,
		isUpload: Boolean,
		remark: String,
		no: {
			type: Boolean,
			default: true,
		},
	},
	data() {
		return {
			acceptFileType,
			dropActive: false,
			fileList: [],
		};
	},
	components: {
		PriceText,
		QuoteBtn,
		MySwitch,
	},
	methods: {
		toDZ() {
			this.$emit("toDZ");
		},
		updateRemark(val) {
			this.$emit("update:remark", val);
		},
		updateCheckBox(e) {
			e.preventDefault();
			this.$store.commit("SET_UploadSwitch", !this.isUpload);
			if (this.isUpload) {
				if (this.fileList.length > 0) {
					this.$emit("update:uploadList", [...this.fileList]);
				} else {
					this.$emit("update:uploadList", []);
				}
			} else {
				this.$emit("update:uploadList", []);
			} 
			this.$emit("update:isUpload", !this.isUpload);
		},
		closeMask() {
			this.$emit("closeMask");
		},
		delUploadImg(index) {
			this.uploadList.splice(index, 1);
			this.fileList.splice(index, 1);
		},
		showMaskFn(name) {
			this.$emit("showMaskFn", name);
		},
		openUpload() {
			this.$refs.upload.click();
			if (this.fileList.length > 0) {
				this.$emit("update:uploadList", [...this.fileList]);
			}
			this.$emit("update:isUpload", false);
		},
		replayUpload() {
			this.openUpload();
			this.$store.commit("setSizeDialog", false);
		},
		uploadPic(event, type = "upload") {
			this.$gl.show();
			let files = type === "upload" ? event.target.files : event;
			let fileResult = checkFile(Array.from(files), this.acceptFileType, 80);
			if (!fileResult) {
				this.$toast.error("File type error");
				this.$gl.hide();
				return false;
			}
			if (fileResult.nomalSize.length == 0) {
                this.$gl.hide();
				this.$store.commit("setSizeDialog", true);
				this.$store.commit("setInputRefName", "replayUpload");
				this.$store.commit("setOverSizeList", fileResult.overSize);
				this.$refs.upload.value = "";
				return false;
			}
			let uploadPromises = [];
			fileResult.nomalSize.forEach((file) => {
				let promise = uploadFile(file).then((res) => {
					this.uploadList.push({
						original_filename: file.name,
						secure_url: res,
						size: (file.size / 1024).toFixed(1),
					});
					this.fileList = [...this.uploadList];
				});
				uploadPromises.push(promise);
			});
			Promise.all(uploadPromises).then(() => {
                this.$gl.hide();
				this.$refs.upload.value = "";
				if (fileResult.overSize.length > 0) {
					this.$store.commit("setSizeDialog", true);
					this.$store.commit("setInputRefName", "replayUpload");
					this.$store.commit("setOverSizeList", fileResult.overSize);
				}
			});
		},
		dragleave(e) {
			e.stopPropagation();
			e.preventDefault();
			this.dropActive = false;
		},
		dragenter(e) {
			e.stopPropagation();
			e.preventDefault();
			this.dropActive = true;
		},
		dragover(e) {
			e.stopPropagation();
			e.preventDefault();
			this.dropActive = true;
		},
		drop(e) {
			e.stopPropagation();
			e.preventDefault();
			let files = e.dataTransfer.files;
			this.uploadPic(files, "drop");
		},
	},
	computed: {
		lang() {
			return this.$store.getters.lang.quote || {};
		},
		userEmail() {
			return this.$store.state.proSystem.email;
		},
	},
	mounted() {
		console.log(this.isPageQuote, "isPageQuote");
		const dropArea = this.$refs.uploadBox;
		dropArea.addEventListener("drop", this.drop, false);
		dropArea.addEventListener("dragleave", this.dragleave, false);
		dropArea.addEventListener("dragenter", this.dragenter, false);
		dropArea.addEventListener("dragover", this.dragover, false);
		this.$Bus.$on("replayUpload", this.replayUpload);
	},
	beforeDestroy() {
		const dropArea = this.$refs.uploadBox;
		dropArea.removeEventListener("drop", this.drop);
		dropArea.removeEventListener("dragleave", this.dragleave);
		dropArea.removeEventListener("dragenter", this.dragenter);
		dropArea.removeEventListener("dragover", this.dragover);
		this.$Bus.$off("replayUpload");
	},
};
</script>

<style scoped lang="scss">
.step-upload {
	.confirmBtnWrap {
		display: none;
		margin-top: 33px;

		@media screen and (max-width: 767px) {
			margin-top: 20px;
		}
	}

	&.mask {
		.confirmBtnWrap {
			display: flex;
			justify-content: center;
			align-items: center;
		}
	}
}

.upload-box.dropActive {
	border-color: $color-primary !important;
}

.circle2 {
	display: flex;
	justify-content: center;
	align-items: center;
	border-color: $color-primary;
	background-color: $color-primary;
	flex-shrink: 0;
	width: 18px;
	height: 18px;
	border: 1px solid #afb1b3;
	border-radius: 50%;
	margin-right: 10px;
	background-color: #fff;

	&::after {
		background-color: #ffffff;
		content: "";
		width: 6px;
		height: 6px;
		background: #d4d7d9;
		border-radius: 50%;
	}

	@media screen and (max-width: 767px) {
		width: 15px;
		height: 15px;
		&::after {
			width: 5px;
			height: 5px;
		}
	}

	&.active {
		background-color: $color-primary !important;
		border-color: $color-primary !important;

		&::after {
			background-color: #ffffff !important;
		}
	}
}

.t1 {
	display: flex;
	align-items: center;
	font-weight: bold;
}

.later {
	cursor: pointer;
	display: flex;
	align-items: center;
	padding-top: 20px;
	font-size: 18px;
	font-weight: bold;
	@media screen and (max-width: 767px) {
		font-size: 12px;
	}

	::v-deep .el-switch.is-checked {
		.el-switch__core {
			border-color: $color-primary !important;
			background-color: $color-primary !important;
		}
	}

	.later-text {
		display: flex;
		column-gap: 7px;
		align-items: center;

		&::before {
			content: "\e7a1";
			font-family: "modalicon";
			color: #9e9e9e;
		}
	}
}

.t2 {
	color: #333333;
	font-size: 14px;
	margin-bottom: 10px;
	padding-left: 29px;
	@media screen and (max-width: 767px) {
		padding-left: 23px;
	}
}

.uploadBtnWrap {
	position: relative;

	input[type="file"] {
		position: absolute;
		left: 0;
		top: 0;
		right: 0;
		bottom: 0;
		opacity: 0;
		z-index: -1;
		width: 0;
		height: 0;
	}

	button.isDisabled {
		background: $color-primary !important;
		opacity: 0.5;
	}
}

::v-deep .el-checkbox__input.is-checked .el-checkbox__inner {
	background-color: $color-primary;
	border-color: $color-primary;
}

::v-deep .el-checkbox__input.is-checked + .el-checkbox__label {
	color: $color-primary;
}

.click_text {
	color: #007aff;
	text-decoration: underline;
	cursor: pointer;
}

.step-upload {
	position: relative;
	margin-bottom: 16px;
	background-color: #fff;
	padding: 40px 30px;
	border-radius: 10px;

	.step-title {
		> span {
			color: $color-primary;
		}
	}

	.step-box {
		display: grid;
		grid-template-columns: 2fr 1.7fr;
		column-gap: 10px;

		.t1 {
			margin-bottom: 10px;
			font-size: 18px;
			color: #333333;
			font-weight: bold;

			.icon-shangchuan {
				color: #9e9e9e;
			}
		}

		.t2 {
			margin-bottom: 10px;
			font-size: 14px;
			color: #333333;
		}

		.upload-box {
			display: flex;
			flex-direction: column;
			justify-content: space-between;
			align-items: center;
			margin-top: 10px;
			padding: 10px;
			background: #ffffff;
			border: 1px dashed #e9ecf0;
			border-radius: 4px;
			cursor: pointer;
			transition: all 0.3s;
			height: 200px;

			.el-checkbox__label {
				font-size: 18px;
			}

			@media (any-hover: hover) {
				&:hover {
					border-color: $color-primary;
				}
			}

			.uploadList {
				width: 100%;
				height: 100px;
				overflow: auto;
				margin-bottom: 10px;
				text-align: center;

				.uploadIcon {
					width: 68px;
					height: 55px;
					margin-top: 15px;
					font-size: 52px;
					color: #ccc;
				}

				.uploadItem {
					display: flex;
					justify-content: space-between;
					align-items: center;
					padding: 5px;
					font-size: 14px;
				}

				.myIcon {
					margin: 0 4px;
				}
			}

			.upload-btn {
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;

				button {
					width: 242px;
					height: 40px;
					margin-bottom: 10px;
					background: $color-primary;
					border-radius: 10px;
					border: none;
					color: #fff;
					font-size: 18px;
					font-weight: bold;
				}

				.el-checkbox__label {
					font-size: 16px;
				}
			}

			.tips {
				font-size: 15px;
				color: #b3b3b3;
				text-align: center;
			}
		}

		.editArea {
			display: flex;
			flex-direction: column;

			.el-textarea {
				height: 100%;

				::v-deep .el-textarea__inner {
					height: 100%;
				}
			}

			.mbInput {
				display: none;
			}
		}

		@media screen and (min-width: 768px) and (max-width: 1499px) {
			grid-template-columns: 2fr 1.4fr;
			column-gap: 20px;
		}

		@media screen and (max-width: 767px) {
			grid-template-columns: repeat(1, 1fr);
			height: auto;

			.t1 {
				margin-bottom: 5px;
				font-size: 12px;
			}

			.t2 {
				margin-bottom: 5px;
				font-size: 12px;
			}

			.upload-box {
				height: 150px;

				.el-checkbox__label {
					font-size: 12px !important;
				}

				.uploadList {
					height: 80px;

					.uploadItem {
						font-size: 12px;
					}

					.uploadIcon {
						width: 51px;
						height: 41px;
						margin-top: 0;
						font-size: 48px !important;
					}
				}

				.upload-btn button {
					width: 147px;
					height: 30px;
					margin-bottom: 10px;
					font-size: 14px;
					border-radius: 4px;
				}

				.upload-btn {
					.el-checkbox__inner {
						width: 15.8px;
						height: 15.8px;

						&::after {
							left: 5px;
							top: 2px;
						}
					}
				}

				.tips {
					font-size: 12px;
				}
			}

			.editArea {
				margin-top: 10px;

				.tip {
					margin: 5px 0;
					font-size: 12px;
				}

				.pcInput {
					display: none;
				}

				.mbInput {
					display: block;
				}
			}
		}
	}

	.box-border {
		display: none;

		.el-icon-close {
			display: flex;
			justify-content: center;
			align-items: center;
			position: absolute;
			font-weight: 700;
			top: 0;
			right: 0;
			width: 40px;
			height: 40px;
			transform: translate(50%, -50%);
			cursor: pointer;
			background: #ffffff;
			border-radius: 50%;
			box-shadow: 1px 2px 20px 0 rgba(0, 0, 0, 0.2);
			z-index: 10;
		}
	}

	&.mask {
		position: relative;
		z-index: 101;

		.confirmBtnWrap {
			position: relative;
		}

		.box-border {
			position: absolute;
			left: 0;
			right: 0;
			top: 0;
			bottom: 0;
			display: block;
			background-color: #fff;
			border: 1px solid #d9dbdd;
		}

		.step-title {
			position: relative;
		}

		.step-box {
			position: relative;
		}
	}

	.step-title {
		font-size: 24px;
		font-weight: 700;
		color: #333333;
		margin-bottom: 23px;

		.step-title-icon {
			width: 21px;
			margin-left: 4px;
			cursor: pointer;
			vertical-align: middle;
		}
	}

	@media screen and (max-width: 767px) {
		margin-bottom: 10px;
		background-color: #fff;
		border-radius: 5px;
		padding: 20px 7px;

		&.mask {
			.box-border {
				.el-icon-close {
					width: 30px;
					height: 30px;
					transform: translate(0, 0);
					box-shadow: none;
				}
			}
		}

		.step-title {
			margin-bottom: 10px;
			font-size: 14px;
			font-weight: bold;
			color: #171719;

			.step-title-icon {
				width: 17px;
				margin-left: 5px;
			}
		}
	}
}
</style>