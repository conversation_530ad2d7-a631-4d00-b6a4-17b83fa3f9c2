<template>
	<!-- 模板外层的盒子上写theme属性，用来分别写每一套的样式，详见下方style -->
	<div class="banner-box" :class="modal.wrapClass">
		<div style="height: 100%;" v-for="(l, li) in modal.list" :style="modal.style" :key="li">
			<div class="imgWrap" @click="setModalType(l.banner,modal.list,'banner')" v-if="l.banner && l.banner.value">
				<pic :src="l.banner.value" :alt="l.banner.alt" :title="l.banner.alt"
						 :style="{ ...modal.homeImgStyle, ...l.banner.style }"/>
			</div>
			<div class="imgWrap" @click="modal.type.clickTarget = 'video'" v-else-if="l.video && l.video.value">
				<video :src="l.video.value" :title="l.video.alt" autoplay loop muted playsinline
							 :style="{ ...l.video.style }"></video>
			</div>
			<div class="bps-container">
				<EditDiv v-if="l.title" class="des" v-model:content="l.title.value"
								 @click="setModalType(l.title,modal.list,'text')" />
				<EditDiv v-if="l.subTitle" class="des" v-model:content="l.subTitle.value"
								 @click="setModalType(l.subTitle,modal.list,'text')" />
				<div class="btnWrap" @click="setModalType(l.button,modal.list,'button')"  v-if="l.button">
					<nuxt-link :to="l.button.url" :title="l.button.alt" :target="l.button.target || '_self'"
						 class="default-button bps-button" :style="{...l.button.style }">
						{{ l.button.value }}
						<b class="icon-bps-sanjiao"></b>
					</nuxt-link>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
	export default {
		name: "Banner",
		props: {
			preview: {
				type: Boolean,
				default: false,
			},
			data: {
				type: Object,
				default: {},
			}
		},
		data() {
			return {
				modal: {
					style: {},
					type: {index: 0, clickPosition: 'outer'},
					...this.data
				},
			};
		},
		watch: {
			modal: {
				handler(val) {
					if (process.env.isManage) this.$emit("update:data", val);
				},
				immediate: true,
				deep: true
			}
		},
		methods: {
			setModalType(target, targetArray, clickType, event, other) {
				this.$setModal(this, target, targetArray, clickType, event, other)
			},
		}
	};
</script>

<style lang="scss" scoped>
	.imgWrap {
		height: 100%;
		position: relative;

		img, video {
			width: 100%;
			height: 100%;
			object-fit: cover;
		}
	}

	.btnWrap {
		display: inline-block;
		color: #ffffff;
		position: absolute;
		left: 90%;
		top: 35%;
	}

	.solutions-part6 {
		position: relative;

		.imgWrap {
			height: 100%;

			img {
				width: 100%;
				height: 100%;
				object-fit: cover;
			}
		}

		.bps-container {
			display: flex;
			flex-direction: column;
			align-items: flex-start;
			position: absolute;
			left: 50%;
			top: 50%;
			transform: translate(-50%, -50%);
			color: #ffffff;

			h1 {
				width: 696px;
				font-size: 48px;
				font-weight: bold;
				color: #ffffff;
				text-shadow: 0px 9px 24px rgba(122, 99, 58, 0.35);
			}

			.des {
				font-size: 30px;
				margin-top: 10px;
				font-weight: 400;
				color: #333333;
				line-height: 30px;
				text-shadow: 0px 9px 24px rgba(122, 99, 58, 0.35);
				&:nth-child(2) {
					color: #C19952;
				}
			}
		}
	}

	.termsBanner {
		position: relative;
		max-width: 1170px;
		height: 99px;
		margin: 40px auto;

		.bps-container {
			position: absolute;
			left: 50%;
			top: 50%;
			transform: translate(-50%, -50%);

			h1 {
				font-size: 36px;
			}
		}

		.imgWrap {
			width: 100%;
			height: 100%;

			img {
				object-fit: cover;
				width: 100%;
				height: 100%;
			}
		}
	}

	@media screen and (min-width: $mb-width) and (max-width: $pad-width) {
		.solutions-part6 {
			position: relative;

			.bps-container {
				h1 {
					width: 522px;
					font-size: 36px;
				}

				.des {
					width: 415px;
					font-size: 16px;
					line-height: 24px;
					margin: 18px 0 28px;
				}
			}
		}
	}

	@media screen and (max-width: $mb-width) {
		.solutions-part6 {
			height: auto;
			padding: 10px 17px;

			.imgWrap {
				overflow: hidden;
				position: relative;
				height: 225px;
				border-radius: 10px;
				img{
					object-position: right top;
				}
			}

			.bps-container {
				position: absolute;
				left: 50%;
				top: 45%;
				width: 85%;
				transform: translate(-50%, -50%);
				padding: 0;

				h1 {
					width: 100%;
					font-size: 21px;
					text-shadow: none;
					color: #333333;
					margin-top: 30px;
				}

				.des {
					width: auto;
					font-size: 15px;
					font-weight: bold;
					text-align: center;
					color: #333333;
					line-height: 18px;
					text-shadow: none;
					margin: auto;
					&:nth-child(2) {
						width: 80%;
						margin: 15px auto;
					}
				}
			}
			.btnWrap {
				left: 50%;
				transform: translateX(-50%);
				top: 100%;
			}
		}
		.termsBanner {
			max-width: 100%;
			margin: 10px 10px 24px;
			height: 50px;

			.bps-container {
				h1 {
					font-size: 21px;
				}
			}
		}
	}
</style>
