<template>
	<div id="briefly-show-empty" :class="{ bgc1: !haveData }">
		<div class="icon" :class="{ bgc2: !haveData }"></div>
		<div class="strip1" :class="{ bgc3: !haveData }"></div>
		<div class="strip2" :class="{ bgc4: !haveData }"></div>
	</div>
</template>

<script>
export default {
	name: "BrieflyShowEmpty",
	props: {
		haveData: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {};
	},
};
</script>

<style lang="scss" scoped>
.bgc1 {
	background: #EEEEEE;;
}
.bgc2 {
	background: #E5E5E5;;
}
.bgc3 {
	background: #E5E5E5;;
}
.bgc4 {
	background: #D9D9D9;;
}
#briefly-show-empty {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
	border-radius: .5208vw;
	.icon {
		width: 3.2292vw;
		height: 3.2292vw;

		border-radius: 1.6146vw;
		margin-top: 1.0417vw;
		margin-bottom: 0.5208vw;
	}
	.strip1,
	.strip2 {
		width: 4.3229vw;
		height: 1.0417vw;
		border-radius: 0.3125vw;
	}
	.strip1 {
		margin-bottom: 0.2083vw;
	}
}
</style>
