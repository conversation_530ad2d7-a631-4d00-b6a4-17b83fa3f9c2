<template>
	<el-button class="viewMore" type="text" @click="showMoreBtn($event)">
		{{ lang.ViewMore }}
		<i class="el-icon-d-arrow-right"></i>
	</el-button>
</template>

<script>
export default {
	props: {
		name: {
			type: String,
		},
	},
	data() {
		return {};
	},
	methods: {
		showMoreBtn(e) {
			e.srcElement.style.display = "none";
			e.srcElement.style.height = "0";
			this.$nextTick(() => {
				this.$refs[this.name][0].style.overflow = "visible";
				this.$refs[this.name][0].style.maxHeight = "100%";
			});
		},
	},
	computed: {
		lang() {
			return this.$store.getters.lang.quote || {};
		},
	},
};
</script>

<style scoped lang="scss">
.viewMore {
	position: relative;
	left: 50%;
	transform: translateX(-50%);

	i {
		transform: rotate(90deg);
	}
}
</style>