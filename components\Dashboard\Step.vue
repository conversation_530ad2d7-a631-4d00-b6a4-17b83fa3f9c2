<template>
	<div class="dashboard-step-wrap">
		<dashboard-move-to-trash v-model="toTrashDialog"></dashboard-move-to-trash>
		<dashboard-plan v-model="planDialog"></dashboard-plan>
		<dashboard-upgrade-site v-model="upgradeDialog"></dashboard-upgrade-site>
		<dashboard-duplicate-site v-model="duplicateDialog"></dashboard-duplicate-site>
		<v-card color="#fff" class="pa-6 mb-6" flat>
			<div class="d-flex align-center pa-6" style="background-color: #F5F6FA">
				<div class="d-flex">
					<div>
						<div>
							Domain: https://{{ currentSite.url }}
							<a :href="'https://'+currentSite.url" target="_blank" class="ml-1"><b
								class="icon-jxsht-ybp-fx primary--text body-2"></b></a>
						</div>
						<div><a href="javascript:;">Connect Your Own Domain </a></div>
					</div>
					<div class="ml-8" v-if="currentSite&&currentSite.proLevel.isFree===1">
						<div>Plan: Free</div>
						<nuxt-link to="/manage/account/premiumPlan">Upgrade to
							Start Selling
						</nuxt-link>
					</div>
					<div class="ml-8" v-else>
						<div>Plan: premium</div>
						<nuxt-link to="/manage/account/premiumPlan">Upgrade to
							Start Selling
						</nuxt-link>
					</div>
				</div>
				<v-spacer></v-spacer>
				<div>
					<v-menu bottom offset-y rounded>
						<template v-slot:activator="{ on, attrs }">
							<v-btn width="170" color="primary" rounded v-bind="attrs" v-on="on">
								<span>Site Actions</span>
								<v-icon right dark>
									mdi-chevron-down
								</v-icon>
							</v-btn>
						</template>
						<v-list>
							<template v-for="(item, index) in list">
								<v-list-item @click="handleClick(item.value)">
									<v-list-item-title>
										<b :class="item.iconName" style="color:#999999;"></b>
										<span class="ml-2 body-2">{{ item.name }}</span>
									</v-list-item-title>
								</v-list-item>
								<v-divider v-if="index!==list.length-1"></v-divider>
							</template>
						</v-list>
					</v-menu>
					<v-btn width="128" color="primary" :to="{path:'/manage/siteEdit/contentEdit'}" rounded class="ml-6">
						Edit Site
					</v-btn>
				</div>
			</div>
		</v-card>
		<v-card color="#fff" class="pa-6 mb-6" flat>
			<div class="font-weight-bold d-flex align-center" style="font-size: 18px;">
				{{currentSite.proName}} business info
				<v-tooltip top max-width="300">
					<template v-slot:activator="{ on, attrs }">
						<v-icon class="ml-1" color="#1A73E8" size="18" v-bind="attrs" v-on="on">
							mdi-help-circle-outline
						</v-icon>
					</template>
					<span>Your business and site details are what people will see on your site, search results, invoices, chat and more.</span>
				</v-tooltip>
			</div>
			<v-divider class="my-6"></v-divider>
			<div class="d-flex align-center">
				<div class="mr-10">
					<nuxt-link to="/manage/set" class="upload">
						<template v-if="!currentSite.proLogo">
							<v-icon color="#1A73E8" size="16">mdi-plus</v-icon>
							<span>Add<br>Logo</span>
						</template>
						<template v-else>
							<v-img
								:src="currentSite.proLogo"
								contain width="100%" height="100%"></v-img>
						</template>
					</nuxt-link>
				</div>
				<div class="mr-10">
					<div>
						<div class="mb-4">
							<v-icon color="#333" class="mr-1 body-1">mdi-email</v-icon>
							E-mail
						</div>
						<div class="secondary--text">{{ siteOwnerForm.email || '<EMAIL>' }}</div>
					</div>
				</div>
				<div class="mr-10">
					<div>
						<div class="mb-4"><b class="icon-hxsht-xp-sj mr-1 body-1"></b>Phone</div>
						<div class="secondary--text">{{ siteOwnerForm.phone || '************' }}</div>
					</div>
				</div>
				<div class="mr-10">
					<div>
						<div class="mb-4"><b class="icon-jxs-xdw mr-1 body-1"></b>Address</div>
						<div class="secondary--text">{{ siteOwnerForm.address || 'e.g., 1419 Westwood Blvd, Los Angeles, CA' }}</div>
					</div>
				</div>
				<div class="d-flex justify-flex-end" style="align-self: flex-start">
					<div>
						<nuxt-link to="/manage/set"><b class="icon-edit mr-1 body-1"></b>Edit Info
						</nuxt-link>
					</div>
					<div>

					</div>
				</div>
				<div class="d-flex justify-center mx-16">
					<v-divider vertical style="height: 60px"></v-divider>
				</div>
				<div class="mr-10">
					<div>
						<div class="mb-4"><b class="icon-jxsht-ybp-wl mr-1 body-1"></b>Site language</div>
						<div class="secondary--text">English</div>
					</div>
				</div>
				<div>
					<div>
						<div class="mb-4"><b class="icon-jxsht-ybp-qb mr-1 body-1"></b>Currency</div>
						<div class="secondary--text">USD ($)</div>
					</div>
				</div>
			</div>
		</v-card>
		<v-card color="success" class="mt-5 mb-6 pa-6 pr-12" v-if="currentSite&&currentSite.proLevel.isFree===1">
			<div class="d-flex align-center">
				<div style="color:#fff;">
					<div style="font-size: 18px">Upgrade Your Site with a Premium Plan. Give your business time to
						grow.
					</div>
					<div style="font-size: 15px">7 Day Money Back Guarantee on All Premium Plans</div>
				</div>
				<v-spacer></v-spacer>
				<v-btn width="156" color="#ffffff" rounded class="success--text" depressed to="/manage/account/premiumPlan">
					Select a plan
				</v-btn>
			</div>
		</v-card>
		<v-card color="#fff" class="mb-6 pa-6" flat v-if="!proStepAllComplete" v-permission="'p-1'">
			<v-row>
				<v-col cols="2">
					<div class="d-flex align-center">
						<div>
							<v-progress-circular
								:rotate="270"
								:size="64"
								:width="4"
								:value="step*stepValue"
								color="primary">
								{{ step }}/{{ stepList.length }}
							</v-progress-circular>
						</div>
						<div class="d-flex flex-column ml-4">
							<strong class="text-h7" style="font-size: 18px;">Set up guide</strong>
							<a href="javascript:;" @click="viewAllStep">{{showAllStep?'Hide Finished Steps':'View All Steps'}} </a>
						</div>
					</div>
				</v-col>
				<v-col cols="10">
					<dashboard-step-list :stepList="stepList" :showAllStep="showAllStep"></dashboard-step-list>
				</v-col>
			</v-row>
		</v-card>
	</div>
</template>

<script>
import {getSysProOwnerByProId} from "@/api/manage/set";

export default {
	name: "Step",
	data() {
		return {
			siteOwnerForm: '',
			stepValue: 100 / 7,
			alert: true,
			list: [
				// {
				// 	name: 'Upgrade Site',
				// 	value: 1,
				// 	iconName: 'icon-jxsht-ybp-xg1',
				// },
				// {
				// 	name: 'Get Feedback',
				// 	value: 2,
				// 	iconName: 'icon-jxsht-ybp-xx',
				// },
				{
					name: 'View Live Site',
					value: 3,
					iconName: 'icon-jxsht-ybp-yj',
				},
				{
					name: 'Add Team User',
					value: 4,
					iconName: 'icon-jxsht-ybp-yh',
				},
				// {
				// 	name: 'Duplicate Site',
				// 	value: 5,
				// 	iconName: 'icon-jxsht-ybp-fz',
				// },
				// {
				// 	name: 'Move to Trash',
				// 	value: 6,
				// 	iconName: 'icon-jxsht-ybp-sc',
				// },
				{
					name: 'Create New Site',
					value: 7,
					iconName: 'icon-jxsht-ybp-sj',
				}
			],
			planDialog: false,
			toTrashDialog: false,
			upgradeDialog: false,
			duplicateDialog: false,
			showAllStep:false,
		}
	},
	computed: {
		step() {
			let len = 0
			this.stepList.forEach(item => {
				if (item.status>=1) {
					len += 1
				}
			})
			return len
		},
		currentSite() {
			return this.$store.state.manage.currentSite || {}
		},
		stepList(){
			return this.$store.state.manage.stepList
		},
		proStepAllComplete(){
			return this.$store.getters["manage/proStepAllComplete"]
		}
	},
	methods: {
		handleClick(val) {
			switch (val) {
				case 1:
					this.upgradeDialog = true;
					break;
				case 2:
					break;
				case 3:
					window.open(`https://${this.currentSite.url}`, '_blank')
					break;
				case 4:
					this.$router.push({
						path: '/manage/set',
						query: {
							tabs: 1
						}
					})
					break;
				case 5:
					this.duplicateDialog = true;
					break;
				case 6:
					this.toTrashDialog = true;
					break;
				case 7:
					window.open('https://www.o2o.co/previewWeb', '_blank')
					break
			}
		},
		async getInfo() {
			let res = await getSysProOwnerByProId({
				proId: this.$store.getters["manage/getProId"],
			})
			this.siteOwnerForm = res.data || {};
		},
		viewAllStep() {
			this.showAllStep = !this.showAllStep
		}
	},
	created() {
		this.getInfo()
	}
}
</script>

<style scoped lang="scss">
.upload {
	display: flex;
	justify-content: center;
	align-items: center;
	width: 73px;
	height: 73px;
	background: #F5F6FA;
	border: 1px dashed #D4D4D4;
	border-radius: 10px;
	color: #1A73E8;

	span {
		line-height: normal;
		font-size: 15px;
	}
}

.dashboard-step-wrap {
	position: relative;

	.alertTip {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
	}
}
</style>
