<template>
	<div class="imprintBox">
		<div class="head">{{ lang.fdPins.userInterface }}</div>
		<div class="headBtnBox">
			<div class="tipText" style="line-height: 1.3; font-size: 14px; padding-left: 4px">
				<div class="textHead" style="font-weight: bold; font-size: 16px; margin-bottom: 0.5em">{{ lang.fdPins.toolTip }}:</div>
				{{ lang.fdPins.toolTip1 }}<br />
				{{ lang.fdPins.toolTip2 }}<br />
				{{ lang.fdPins.toolTip3 }}<br />
			</div>
			<!-- <div class="headTitle">{{ lang.fdPins.toolsWindow }}:</div> -->
			<div class="headBtn">
				<div class="btnItem" @click="downLoadPic"><b class="icon-Download"></b>{{ langConfirm.download }}</div>
				<div class="btnItem" @click="imprintPic"><b class="icon-Imprint"></b>{{ lang.fdPins.imprint }}</div>
			</div>
		</div>
		<div class="importContent" :ref="refs" :class="{ hiddenEdit: beginDownload }">
			<div class="proDetail">
				<div class="proImg">
					<img :src="showImg" alt="" />
					<div class="uploadBtn" @click="clickInput">
						{{ lang.Upload }} {{ lang.fdPins.Image }}
						<input type="file" ref="imprintBoxUpload" :accept="acceptFileType" @change="uploadPic" />
					</div>
				</div>
				<div class="proIntro">
					<div class="proDescription proIntroItem" :class="{ hasDashed: filterDescription.length > 0, editing: beginEdit }">
						<div class="layer" v-show="editTipDialog"></div>
						<div class="remind" v-show="editTipDialog">
							<div class="remindHeader">{{ lang.fdPins.exitTip }}</div>
							<div class="editBtn">
								<div class="cancelBtn" @click.stop="handleClose('close')">{{ lang.Cancel }}</div>
								<div class="saveBtn" @click="handleSave(1)">{{ lang.fdPins.save }}</div>
							</div>
						</div>
						<span class="cancelIcon" v-if="filterDescription.length > 0 && filterDescription[0].proValue.value.length > 0" @click="cancelFn('one')">
							<span v-show="beginEdit && hasEdit1">{{ lang.Cancel }}<b class="icon-back"></b></span>
						</span>
						<span class="editIcon" v-if="filterDescription.length > 0 && filterDescription[0].proValue.value.length > 0" @click="editFn('one')">
							<span v-show="!beginEdit">
								{{ lang.Edit }}
								<b class="icon-a-bianji4"></b>
							</span>
							<span v-show="beginEdit">{{ lang.fdPins.save }}<b class="icon-check"></b></span>
						</span>
						<div class="proNameBox">
							<H1 class="cateName">{{ cateName }}</H1>
							<div class="sku">{{ lang.fdPins.Item }} {{ proSku }}</div>
						</div>
						<div class="canEditDiv" :contenteditable="beginEdit" @blur="updateContent($event, filterDescription[0].type, 'proValue', 1)" :data-old-value="filterDescription[0].proValue.value" :data-edit-type="filterDescription[0].type" v-if="filterDescription.length > 0 && filterDescription[0].proValue.value.length > 0" v-html="filterDescription[0].proValue.value"></div>
					</div>
					<div class="proPriceIntro proIntroItem" :class="{ editing: beginEdit2 }">
						<div class="layer" v-show="editTipDialog2"></div>
						<div class="remind" v-show="editTipDialog2">
							<div class="remindHeader">{{ lang.fdPins.exitTip }}</div>
							<div class="editBtn">
								<div class="cancelBtn" @click.stop="handleClose('close2')">{{ lang.Cancel }}</div>
								<div class="saveBtn" @click="handleSave(2)">{{ lang.fdPins.save }}</div>
							</div>
						</div>
						<span class="cancelIcon" v-if="noDescription.length > 0 && noDescription[0].proValue.value.length > 0" @click="cancelFn('two')">
							<span v-show="beginEdit2 && hasEdit2">{{ lang.Cancel }}<b class="icon-back"></b></span>
						</span>
						<span class="editIcon" @click="editFn('two')" v-if="noDescription.length > 0">
							<span v-show="!beginEdit2">{{ lang.Edit }}<b class="icon-a-bianji4"></b></span> <span v-show="beginEdit2">{{ lang.fdPins.save }}<b class="icon-check"></b></span>
						</span>
						<div class="canEditDivBox" v-for="item in noDescription" :key="item.type">
							<div class="canEditDiv titleBox" :data-old-value="item.title.value" :contenteditable="beginEdit2" @blur="updateContent($event, item.type, 'title', 2)" v-html="item.title.value" @input="debounceInput($event, item, 'title', 2)"></div>
							<div class="canEditDiv" :data-edit-type="item.type" :contenteditable="beginEdit2" @blur="updateContent($event, item.type, 'proValue', 2)" @input="debounceInput($event, item, 'proValue', 2)" :data-old-value="item.proValue.value" v-html="item.proValue.value"></div>
						</div>
					</div>
				</div>
			</div>
			<div class="priceTableBox">
				<div class="tableMain">
					<div class="tableHead">
						<span>{{ lang.fdPins.pricingList }}</span>
						<span class="editIcon" @click="editPriceSize">{{ lang.Edit }}<b class="icon-a-bianji4"></b></span>
					</div>
					<table cellspacing="0" cellpadding="0">
						<thead>
							<tr class="flex-Boxs">
								<th class="noWrap">{{ langCart.itemNo }}.</th>
								<th class="noWrap2">{{ lang.neon.Size }} (inch)</th>
								<th class="noWrap3">{{ lang.fdPins.thickness }} (mm)</th>
								<th v-for="(number, index) in tableData[0].quantityPrice" :key="index">{{ number.totalQuantity }}</th>
								<th class="lastNoWrap">{{ lang.fdPins.moldCharge }}</th>
							</tr>
						</thead>
						<tbody>
							<tr class="flex-Boxs tableItem" v-for="(item, index) in copyTableData2" :key="index">
								<td class="noWrap normal">{{ item.itemNo }}</td>
								<td class="noWrap2 normal">{{ item.sizeName }}</td>
								<td class="noWrap3 normal">{{ item.thickness }}</td>
								<td class="headItem" v-for="(item2, index2) in item.quantityPrice" :key="index2 + '_' + index">
									<CCYRate :price="item2.foundationUnitPrice"></CCYRate>
								</td>
								<td class="lastNoWrap normal">
									<CCYRate :price="item.moldCharge"></CCYRate>
								</td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>
		</div>
		<TopDialog v-model="showEditDialog" :model="false" :width="!isMobile ? '500px' : '90%'" :maskColor="'rgba(0,0,0,0.1)'">
			<div class="editPriceBox">
				<div class="priceEditHead">
					<h3>{{ lang.fdPins.bySize }}</h3>
				</div>
				<div class="checkBox">
					<div class="checkItem" :class="{ active: checkSizeData.includes(item.sizeCode) }" v-for="(item, index) in copyTableData" :key="index">
						<label>
							<input type="checkbox" class="custom-class" v-model="checkSizeData" :value="item.sizeCode" />
							<span>{{ item.alias }}</span>
						</label>
					</div>
				</div>
				<div class="checkAllItem">
					<label @click="checkAllSize">
						<div class="hideBox" @click.stop="hideClick"></div>
						<input type="checkbox" :disabled="true" :checked="selectAllStatus" />
						<span class="checkAllText">{{ lang.fdPins.selectAllSize }}</span>
					</label>
				</div>
				<div class="confirmBox" :class="{ active: checkSizeData.length > 0 }" @click="saveEidt">{{ lang.Confirm }}</div>
			</div>
		</TopDialog>
	</div>
</template>

<script>
import { debounce } from "@/utils/utils";
import { Common } from "@/utils/common";
import { uploadFile } from "@/utils/oss";
import { checkFile } from "@/utils/validate";
import TopDialog from "@/components/Quote/TopDialog.vue";
import { downloadFdPdf } from "@/api/quote/fdCommon.js";
export default {
	name: "ImprintBox",
	props: {
		tableData: {
			type: Array,
			default: () => [],
		},
		listData: {
			type: Array,
			default: () => [],
		},
		nowSiperImg: {
			type: String,
			default: "",
		},
		cateName: {
			type: String,
			default: "",
		},
		proSku: {
			type: String,
			default: "",
		},
	},
	components: { TopDialog },
	data() {
		return {
			html2canvas: null,
			uploadedImg: "",
			acceptFileType: ".jpg,.jpeg,.gif,.png,.bmp,.webp,.svg",
			refs: "imprintRef",
			copyListData: [],
			beginDownload: false,
			beginEdit: false,
			beginEdit2: false,
			showEditDialog: false,
			editTipDialog: false,
			editTipDialog2: false,
			copyTableData: [],
			checkSizeData: [],
			correctCheckSizeData: [],
			hasEdit1: false,
			hasEdit2: false,
			needOpenTip: true,
			needOpenTip2: true,
			debounceInput: null,
		};
	},
	watch: {},
	computed: {
		lang() {
			return this.$store.getters.lang.quote || {};
		},
		langCart() {
			return this.$store.getters.lang.cart || {};
		},
		langConfirm() {
			return this.$store.getters.lang.confirm || {};
		},
		filterDescription() {
			return this.copyListData.filter((item) => item.type == "Description");
		},
		noDescription() {
			return this.copyListData.filter((item) => {
				return item.type !== "Description";
			});
		},
		showImg() {
			return this.uploadedImg || this.nowSiperImg;
		},
		isMobile() {
			return this.$store.getters.isMobile;
		},
		copyTableData2() {
			if (this.correctCheckSizeData.length > 0) {
				return this.copyTableData.filter((item) => {
					return this.correctCheckSizeData.includes(item.sizeCode);
				});
			}
			return this.copyTableData;
		},
		selectAllStatus() {
			return this.checkSizeData.length >= this.copyTableData.length;
		},
	},
	methods: {
		imprintUpload() {
			this.clickInput();
			this.$store.commit("setSizeDialog", false);
		},
		clickInput() {
			this.$refs["imprintBoxUpload"].click();
		},
		uploadPic(event, type = "upload") {
			this.$emit("switchLoading", "open");
			let files = type === "upload" ? event.target.files : event;
			let fileResult = checkFile(Array.from(files), this.acceptFileType, 80);
			if (!fileResult) {
				this.$toast.error("File type error");
				this.$emit("switchLoading", "close");
				return false;
			}
			if (fileResult.nomalSize.length == 0 && fileResult.overSize.length > 0) {
				this.$emit("switchLoading", "close");
				this.$store.commit("setSizeDialog", true);
				this.$store.commit("setInputRefName", "imprintUpload");
				this.$store.commit("setOverSizeList", fileResult.overSize);
				this.$refs.imprintBoxUpload.value = "";
				return false;
			}
			let uploadPromise = [];
			fileResult.nomalSize.forEach((file) => {
				let promise = uploadFile(file).then((res) => {
					let temp = {
						original_filename: file.name,
						secure_url: res,
						size: (file.size / 1024).toFixed(1),
					};
					this.uploadedImg = temp.secure_url;
					// this.$emit("pushInfoList", temp);
				});
				uploadPromise.push(promise);
			});
			Promise.all(uploadPromise).then(() => {
				this.$emit("switchLoading", "close");
				this.$refs.imprintBoxUpload.value = "";
				if (fileResult.overSize.length > 0) {
					this.$store.commit("setSizeDialog", true);
					this.$store.commit("setInputRefName", "imprintUpload");
					this.$store.commit("setOverSizeList", fileResult.overSize);
				}
			});
		},
		downLoadPic() {
			if (this.beginEdit) {
				this.openEditTipDialog(1);
				return;
			}
			if (this.beginEdit2) {
				this.openEditTipDialog(2);
				return;
			}
			this.$emit("switchLoading", "open");
			this.beginDownload = true;
			this.$nextTick(() => {
				setTimeout(() => {
					Common.cutpic(this, document, true, false, "projectInfo" + +new Date(), this.refs, () => {
						this.$emit("switchLoading", "close");
						this.beginDownload = false;
					});
				}, 0);
			});
		},
		imprintPic() {
			if (this.beginEdit) {
				this.openEditTipDialog(1);
				return;
			}
			if (this.beginEdit2) {
				this.openEditTipDialog(2);
				return;
			}
			this.$emit("switchLoading", "open");
			this.beginDownload = true;
			this.$nextTick(() => {
				setTimeout(() => {
					if (!this.html2canvas) return;
					this.html2canvas(this.$refs.imprintRef, {
						useCORS: true,
						allowTaint: false,
					})
						.then((canvas) => {
							let dataURL = canvas.toDataURL("image/png");
							console.log(dataURL, "456654");
							downloadFdPdf({ fileName: `projectInfo.png`, base64Str: dataURL.split(";base64,")[1] }).then((res) => {
								if (res.data) window.open(res.data);
								this.$emit("switchLoading", "close");
								this.beginDownload = false;
							});
						})
						.finally(() => {
							this.$emit("switchLoading", "close");
							this.beginDownload = false;
						});
				}, 0);
			});
		},
		normalizeText(text) {
			if (!text) return "";
			text = text.replace(/<br\s*\/?>/gi, "<br>");
			//去除所有空白字符
			return text;
		},
		editInputFn(event, item, name, num) {
			const newValue = event.target.innerHTML;
			if (this.normalizeText(newValue) === this.normalizeText(item[name].oldValue)) return;
			if (num === 1) this.hasEdit1 = true;
			if (num === 2) this.hasEdit2 = true;
		},
		updateContent(event, type, name, num) {
			const newValue = event.target.innerHTML;
			const oldValue = event.target.getAttribute("data-old-value");
			if (this.normalizeText(newValue) === this.normalizeText(oldValue)) return;
			this.copyListData = this.copyListData.map((item) => {
				if (item.type === type) {
					if (typeof item[name] === "object" && item[name] !== null) {
						return { ...item, [name]: { ...item[name], value: newValue } };
					}
					return { ...item, [name]: newValue };
				}
				return item;
			});
		},
		cancelFn(type) {
			if (type == "one") {
				this.copyListData.forEach((item) => {
					if (item.type == "Description") {
						item.proValue.value = item.proValue.oldValue;
					}
				});
				this.hasEdit1 = false;
			} else {
				this.copyListData.forEach((item) => {
					if (item.type !== "Description") {
						item.title.value = item.title.oldValue;
						item.proValue.value = item.proValue.oldValue;
					}
				});
				this.hasEdit2 = false;
			}
		},
		editFn(type) {
			let focusCanEditBox = null;
			if (type == "one") {
				this.beginEdit = !this.beginEdit;
				if (this.beginEdit) {
					let firstFocus = this.filterDescription[0].type;
					focusCanEditBox = document.querySelector(`.canEditDiv[data-edit-type='${firstFocus}']`);
				} else {
					//保存
					this.needOpenTip = false;
				}
			} else {
				this.beginEdit2 = !this.beginEdit2;
				if (this.beginEdit2) {
					let twoFocus = this.noDescription[0].type;
					focusCanEditBox = document.querySelector(`.canEditDiv[data-edit-type='${twoFocus}']`);
				} else {
					this.needOpenTip2 = false;
				}
			}
			if (focusCanEditBox) {
				this.$nextTick(() => {
					focusCanEditBox.focus();
				});
			}
		},
		editPriceSize() {
			this.checkSizeData = this.correctCheckSizeData;
			this.showEditDialog = true;
		},
		hideClick() {
			this.checkAllSize();
		},
		checkAllSize() {
			if (this.selectAllStatus) {
				this.checkSizeData = [];
			} else {
				this.checkSizeData = this.copyTableData.map((item) => {
					return item.sizeCode;
				});
			}
		},
		saveEidt() {
			if (this.checkSizeData.length == 0) return;
			this.correctCheckSizeData = this.checkSizeData;
			this.showEditDialog = false;
		},
		openEditTipDialog(num) {
			if (num === 1) {
				this.editTipDialog = true;
			}
			if (num === 2) {
				this.editTipDialog2 = true;
			}
		},
		handleClose(type) {
			if (type == "close") {
				this.cancelFn("one");
				this.beginEdit = false;
				this.editTipDialog = false;
			}
			if (type == "close2") {
				this.cancelFn("two");
				this.beginEdit2 = false;
				this.editTipDialog2 = false;
			}
		},
		handleSave(num) {
			if (num === 1) {
				this.beginEdit = false;
				this.editTipDialog = false;
			}
			if (num === 2) {
				this.beginEdit2 = false;
				this.editTipDialog2 = false;
			}
		},
	},
	created() {},
	async mounted() {
		if (process.client) {
			Common.init();
			this.html2canvas = (await import("html2canvas")).default;
		}
		const imprintBox = document.querySelector(".imprintBox");
		if (imprintBox) {
			imprintBox.addEventListener("click", (e) => {
				if (!this.beginEdit && !this.beginEdit2) return;
				if (this.beginEdit) {
					if (!e.target.closest(".proDescription")) {
						this.openEditTipDialog(1);
					}
				}
				if (this.beginEdit2) {
					if (!e.target.closest(".proPriceIntro")) {
						this.openEditTipDialog(2);
					}
				}
			});
		}
		this.debounceInput = debounce(this.editInputFn, 300);
		this.copyListData = JSON.parse(JSON.stringify(this.listData))
			.filter((item) => {
				return item.title && item.title.value.length > 0 && item.proValue && item.proValue.value.length > 0;
			})
			.map((item) => {
				item.title.oldValue = item.title.value;
				item.proValue.oldValue = item.proValue.value;
				return item;
			});
		this.copyTableData = JSON.parse(JSON.stringify(this.tableData));
		this.$Bus.$on("imprintUpload", this.imprintUpload);
	},
	beforeDestroy() {
		this.$Bus.$off("imprintUpload", this.imprintUpload);
	},
};
</script>

<style scoped lang="scss">
.imprintBox * {
	font-family: Calibri;
}
.imprintBox {
	padding: 15px;
	color: #333333;
	font-family: Calibri;
	.head {
		font-weight: bold;
		font-size: 20px;
		text-align: center;
	}
	.headBtnBox {
		margin: 20px 0;
		display: flex;
		align-items: center;
		justify-content: space-between;
		.headTitle {
			font-weight: bold;
			font-size: 18px;
		}
		.headBtn {
			display: flex;
			align-items: center;
			column-gap: 14px;
			.btnItem {
				width: 150px;
				height: 45px;
				display: flex;
				align-items: center;
				justify-content: center;
				border-radius: 4px;
				font-weight: 400;
				font-size: 16px;
				cursor: pointer;
				background: #f8f8f8;
				border: 1px solid #0066cc;
				b {
					font-size: 20px;
					color: #0066cc;
					margin-right: 4px;
				}
			}
		}
	}
	.importContent {
		background: #f8f8f8;
		border-radius: 8px;
		&.hiddenEdit {
			.uploadBtn {
				display: none;
			}
			.editIcon {
				display: none;
			}
			.cancelIcon {
				display: none;
			}
		}
		.proDetail {
			padding: 30px 20px;
			display: flex;
			column-gap: 4%;
			.proImg {
				position: relative;
				min-width: 0;
				width: 44%;
				height: 44%;
				aspect-ratio: 1 / 1;
				border-radius: 8px;
				img {
					width: 100%;
					height: 100%;
					object-fit: cover;
					border-radius: 8px;
					aspect-ratio: 1 / 1;
				}
				.uploadBtn {
					position: absolute;
					top: 50%;
					left: 50%;
					transform: translate(-50%, -50%);
					padding: 0.6em 1.2em;
					font-weight: bold;
					font-size: 16px;
					color: #ffffff;
					background: #0066cc;
					border-radius: 10px;
					border: 1px solid #ffffff;
					white-space: nowrap;
					cursor: pointer;
					input {
						display: none;
					}
				}
			}
			.proIntro {
				min-width: 0;
				flex: 1;
				display: flex;
				flex-direction: column;
				row-gap: 10px;
				.canEditDiv {
					outline: none;
					line-height: 1.8;
					&.titleBox {
						font-weight: bold;
						font-size: 18px;
						padding-right: 40px;
					}
				}
				.proIntroItem {
					padding: 8px;
					border-radius: 8px;
					position: relative;
					font-size: 14px;
					.cancelIcon {
						position: absolute;
						top: 6px;
						right: 60px;
						cursor: pointer;
						font-size: 12px;
						b {
							font-size: 12px;
							margin-left: 4px;
						}
					}
					.editIcon {
						position: absolute;
						top: 4px;
						right: 4px;
						cursor: pointer;
						font-size: 12px;
						b {
							font-size: 14px;
							margin-left: 4px;
						}
					}
				}
				.proDescription {
					&.hasDashed {
						border: 1px dashed #9a9a9a;
					}
					.proNameBox {
						.cateName {
							font-size: 24px;
							font-weight: bold;
							padding-right: 40px;
						}
						.sku {
							margin: 10px 0;
							font-weight: bold;
							font-size: 18px;
							color: #0066cc;
						}
					}
				}
				.proPriceIntro {
					border: 1px dashed #9a9a9a;
					.canEditDivBox {
						&:last-child {
							margin-bottom: 0;
						}
					}
				}
			}
		}

		.priceTableBox {
			padding: 10px 10px 30px;
		}

		.flex-Boxs {
			.headItem {
				position: relative;
				label {
					padding: 0 4px;
				}
				span {
					&:first-child {
						display: inline-block;
						height: 100%;
						::v-deep .el-popover__reference-wrapper {
							width: 100%;
							height: 100%;
							display: inline-block;
							.el-popover__reference {
								display: flex;
								align-items: center;
								justify-content: center;
								width: fit-content;
								height: 100%;
							}
						}
					}
				}
			}
			.normal {
				height: 100%;
			}
			.noWrap {
				min-width: 90px;
			}
			.noWrap2 {
				width: 60px;
				min-width: 60px;
				text-align: center;
			}
			.noWrap3 {
				width: 80px;
				min-width: 80px;
				text-align: center;
			}
			.lastNoWrap {
				width: 100px;
				min-width: 100px;
			}
		}

		.tableMain {
			max-width: 100%;
			overflow: auto hidden;
			width: 100%;
			font-size: 14px;
			padding-bottom: 60px;
			border: 1px dashed #9a9a9a;
			padding: 10px;
			border-radius: 8px;
			.tableHead {
				display: flex;
				justify-content: space-between;
				font-weight: bold;
				font-size: 18px;
				margin-bottom: 20px;
				.editIcon {
					cursor: pointer;
					font-size: 12px;
					font-weight: 400;
					b {
						font-size: 14px;
						margin: 0 4px;
					}
				}
			}

			table {
				min-width: 100%;
				width: fit-content;
				thead {
					height: 50px;
					background: #969eb5;
					font-size: 14px;
					font-weight: bold;
					color: #ffffff;
				}
				tr {
					th,
					td {
						min-width: 42px;
						text-align: center;
					}
					td {
						height: 46px;
						line-height: 46px;
						white-space: nowrap;
						border-bottom: 1px solid #dfdfdf;
						&:first-child {
							border-left: 1px solid #dfdfdf;
						}
						&:last-child {
							border-right: 1px solid #dfdfdf;
						}
					}
				}
			}

			.tableItem {
				height: 46px;
				&:nth-child(odd) {
					background-color: #ffffff;
				}
				&:nth-child(even) {
					background-color: #f7f7f7;
				}
				&:last-child {
				}
			}
		}
	}

	@keyframes ants {
		to {
			background-position: 100%;
		}
	}

	.editing {
		border: 1px solid transparent !important;
		background: linear-gradient(#f8f8f8, #f8f8f8) padding-box, repeating-linear-gradient(-45deg, black 0, black 25%, #f8f8f8 0, #f8f8f8 50%) border-box 0 /0.6em 0.6em;
		animation: ants 30s linear infinite;
	}

	.editPriceBox {
		padding: 20px 20px 30px;
		.priceEditHead {
			h3 {
				font-weight: bold;
				font-size: 18px;
			}
		}
		.checkBox {
			margin-top: 20px;
			display: grid;
			grid-template-columns: repeat(3, 1fr);
			gap: 10px;
		}
		.checkItem,
		.checkAllItem {
			padding: 6px 10px;
			height: 42px;
			background: #f8f8f8;
			border-radius: 6px;
			border: 1px solid transparent;
			display: flex;
			align-items: center;
			cursor: pointer;
			&.active {
				background: #d5e6f6;
				border: 1px solid #0066cc;
			}
			&:hover {
				background: #d5e6f6;
				border: 1px solid #0066cc;
			}
			label {
				width: 100%;
				height: 100%;
				display: flex;
				align-items: center;
				column-gap: 4px;
				font-weight: 400;
				font-size: 15px;
				cursor: pointer;
				input[type="checkbox"] {
					appearance: none;
					-webkit-appearance: none;
					-moz-appearance: none;
					width: 18px;
					height: 18px;
					border: 1px solid #9a9a9a;
					border-radius: 4px;
					outline: none;
					cursor: pointer;
					position: relative;
					&:checked {
						border: 1px solid #0066cc;
					}
				}
				input[type="checkbox"]:checked::before {
					content: "\e849";
					font-family: "modalicon";
					width: 100%;
					height: 100%;
					position: absolute;
					top: 50%;
					left: 50%;
					transform: translate(-50%, -50%);
					color: #fff;
					font-size: 14px;
					background-color: #0066cc;
				}
				span {
					line-height: initial;
				}
			}
		}
		.checkAllItem {
			margin-top: 10px;
			padding: 0;
			background: transparent;
			&.active {
				background: transparent;
				border: 1px solid transparent;
			}
			&:hover {
				background: transparent;
				border: 1px solid transparent;
			}
			label {
				width: fit-content;
				column-gap: 8px;
				position: relative;
				.hideBox {
					position: absolute;
					width: 20px;
					height: 20px;
					z-index: 1;
				}
				input {
					border: 1px solid #0066cc;
				}
				input[type="checkbox"]:checked::before {
					color: #0066cc;
					background-color: transparent;
				}
			}
			.checkAllText {
				font-weight: bold;
				font-size: 16px;
			}
		}
		.confirmBox {
			margin: 0 auto;
			margin-top: 20px;
			width: 160px;
			height: 40px;
			display: flex;
			align-items: center;
			justify-content: center;
			font-family: Calibri;
			font-weight: bold;
			font-size: 16px;
			color: #ffffff;
			background: #ccc;
			border-radius: 10px;
			cursor: pointer;
			&.active {
				background: #ff6600;
			}
		}
	}
}

.layer {
	position: absolute;
	inset: 0;
	background: rgba(0, 0, 0, 0.5);
}
.remind {
	font-family: Calibri;
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	width: 80%;
	padding: 1em 2em;
	background-color: #fff;
	border-radius: 8px;
	display: flex;
	flex-direction: column;
	align-items: center;
	row-gap: 10px;
	color: #333333;
	@include respond-to(mb) {
		width: 100%;
		top: 50%;
		right: 50%;
		transform: translate(50%, -50%);
		padding: 10px 20px 20px;
	}

	.remindHeader {
		font-size: 16px;
		font-weight: bold;
	}
	.remindContent {
		font-size: 14px;
	}
	.editBtn {
		margin-top: 10px;
		display: flex;
		align-items: center;
		gap: 10px;
		font-size: 12px;
		.cancelBtn {
			color: #000;
			padding: 0.5em 3em;
			border-radius: 8px;
			cursor: pointer;
			line-height: initial;
			border: 1px solid #000;
		}
		.saveBtn {
			color: #fff;
			padding: 0.5em 3em;
			background: #ff6600;
			border-radius: 8px;
			border: 1px solid #ff6600;
			cursor: pointer;
			line-height: initial;
		}
	}

	.closeIcon {
		display: block !important;
		position: absolute;
		top: 0.5em;
		right: 0.5em;
		width: 2em;
		height: 2em;
		padding: 0.5em;
		cursor: pointer;
		color: #000 !important;
	}
}
</style>
