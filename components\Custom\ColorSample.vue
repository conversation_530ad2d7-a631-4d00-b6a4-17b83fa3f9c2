<template>
	<div>
		<div :style="{ ...o.style }">
			<EditDiv
				v-if="o.title?.value"
				:tagName="o.title.tagName || 'h2'"
				v-model:content="o.title.value"
				:style="modal.titleStyle"
				@click="setModalType(o.title, modal.outer, 'text')"
			/>

			<div
				class="sub-title"
				:pointer="o.subTitle?.event"
				:style="modal.subTitleStyle"
				:hidden="!o.title?.value && !o.subTitle?.value"
				@click="setModalType(o.subTitle, modal.outer, 'text', o.subTitle.event)"
			>
				<EditDiv v-if="o.subTitle?.value" v-model:content="o.subTitle.value" />
			</div>

			<EditDiv
				v-if="o.text?.value"
				:style="modal.textStyle"
				v-model:content="o.text.value"
				@click="setModalType(o.text, modal.outer, 'text')"
			/>

			<div class="search-box" v-if="o.search" :style="modal.searchStyle">
				<input type="text" v-model.trim="searchKeyword" :placeholder="o.search.value" />
				<b :class="o.search.icon"></b>
			</div>
		</div>

		<div class="card-box" :style="{ ...modal.cardBoxStyle }">
			<div flex :style="{ ...modal.boxStyle }">
				<div flex class="card" v-if="o.thead?.length" :style="{ ...modal.cardStyle, ...modal.theadStyle }">
					<div v-for="(th, thi) in o.thead" :key="thi" :style="th.style">{{ th.value }}</div>
				</div>

				<div
					flex
					class="card"
					v-for="(l, li) in displayList"
					:key="li"
					:style="{ ...modal.cardStyle, backgroundColor: isPantone ? l.backColor : null }"
				>
					<Pic v-if="l.image" :src="l.image" :alt="l.textOne" :style="{ ...modal.cardImgStyle }" />

					<div class="content" :style="{ ...modal.cardContentStyle, ...l.contentStyle }">
						<div class="card-subtitle" :style="{ ...modal.cardSubTitleStyle, color: l.textOneColor }">
							{{ l.textOne }}
						</div>

						<Pic v-if="l.image" :src="l.image" :alt="l.textOne" :style="{ ...modal.cardImgListStyle }" />

						<div
							class="card-text"
							:style="{ ...modal.cardTextStyle, color: l.textTwoColor, background: l.backColor }"
						>
							{{ l.textTwo }}
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	name: "CustomColorSample",

	props: {
		modal: { type: Object, default: () => ({}) },
		list: { type: Array, default: () => [] },
	},

	data() {
		return {
			searchKeyword: "",
		};
	},

	computed: {
		o() {
			return this.modal.outer[0] || {};
		},

		isPantone() {
			return this.modal.apiParams?.type === 4;
		},

		displayList() {
			// 如果没有搜索关键词则返回完整列表
			if (!this.searchKeyword) return this.list;

			// 转换搜索关键词为小写以进行不区分大小写的搜索
			const keyword = this.searchKeyword.toLowerCase();

			// 使用 Array.filter 过滤匹配项
			return this.list.filter(({ textOne = "", textTwo = "" }) => {
				const normalizedTextOne = textOne.toLowerCase();
				const normalizedTextTwo = textTwo.toLowerCase();

				// 检查文本是否包含搜索关键词
				return normalizedTextOne.includes(keyword) || normalizedTextTwo.includes(keyword);
			});
		},
	},

	methods: {
		setModalType(target, targetArray, clickType, event, other) {
			this.$setModal(this, target, targetArray, clickType, event, other);
		},
	},
};
</script>

<style lang="scss" scoped>
.search-box {
	display: flex;
	height: 2.5em;
	padding: 0 1em;
	width: 20.125em;
	align-items: center;
	border-radius: 1.25em;
	border: 1px solid #cbcbcb;

	input {
		flex: 1;
		font-size: calc(1em - 2px);
	}
}

.hover-img-show .card {
	&:nth-child(even) {
		background-color: #f9f9f9;
	}

	&:hover {
		background-color: #fff5f1;

		> img {
			display: block !important;
		}
	}
}
</style>
