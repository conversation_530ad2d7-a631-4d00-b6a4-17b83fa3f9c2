import { request } from "@/utils/request";

//生成图片
export function generateImg(data) {
	return request({
		url: "/aicenter/neonsign/generateImg",
		method: "post",
		data,
	});
}

//查询任务
export function queryImg(data) {
	return request({
		url: "/aicenter/neonsign/queryImg",
		method: "get",
		params: data,
	});
}

//预览图片
export function previewImg(data) {
	return request({
		url: "/aicenter/neonsign/previewImg",
		method: "get",
		params: data,
		responseType: "blob",
	});
}

//验签
export function checkSign(data) {
	return request({
		url: "/aicenter/neonsign/t0",
		method: "post",
		data,
	});
}

//图片生成记录
export function getHistory(data) {
	return request({
		url: "/aicenter/aiimagerecord/search",
		method: "post",
		data,
	});
}

export function deleteRecord(data) {
	return request({
		url: "/aicenter/aiimagerecord/deleteRecord",
		method: "post",
		params:data,
	});
}

export function getThemeList(data) {
	return request({
		url: "/aicenter/aiimagerecord/getThemeList",
		method: "get",
		params:data,
	});
}

export function getByImgFileName(data) {
	return request({
		url: "/aicenter/aiimagerecord/getByImgFileName",
		method: "get",
		params:data,
	});
}

export function uploadImage(data,cb) {
	return request({
		url: "/aicenter/neonsign/uploadImage",
		method: "post",
		data,
		headers: {
			'Content-Type': 'multipart/form-data'
		},
		onUploadProgress: progressEvent => {
			if(typeof cb ==='function'){
				cb(progressEvent)
			}
		}
	});
}

