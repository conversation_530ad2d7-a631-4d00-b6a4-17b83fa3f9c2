<template>
	<div class="quote-wrap modal-box" :theme="$store.state.proTheme"
		@click.self="setModalType({}, modal.list, 'quote_table')"
		:class="[modal.class, (styleConfig?.nextIsUpload && currentStep > 1) ? 'isUpload' : '']" :style="modal.style">
		<div class="quote-title">
			<div>
				<nuxt-link to="/" style="color: #999999">{{ langLayout.home }} ></nuxt-link>
				<span v-if="!hasCustomCateName">{{ cateInfo.cateName }}</span>
				<EditDiv v-for="(item, index) in modal.list" :key="index" :tagName="item.title.tagName"
					v-model:content="item.title.value" v-if="item.title.value && item.type === 'cateName'"
					@click="setModalType(item.title, modal.list, 'text')" />
			</div>
			<div>
				<div flex class="img-box">
					<a href="https://www.facebook.com/enamelpinsinc/" target="_blank">
						<img src="https://static-oss.gs-souvenir.com/web/2024/other/20240903/729enamel-pins-inc.-facebook.png"
							alt="enamel pins inc.-facebook" title="enamel pins inc.-facebook" decoding="async" loading="lazy" />
					</a>
					<a href="https://x.com/EnamelpinsC" target="_blank"><img
							src="https://static-oss.gs-souvenir.com/web/2024/other/20240903/161enamel-pins-inc.-tweets.png"
							alt="enamel pins inc.-tweets" title="enamel pins inc.-tweets" decoding="async" loading="lazy" /></a>
					<a href="https://www.instagram.com/gsjj_official/" target="_blank"><img
							src="https://static-oss.gs-souvenir.com/web/2024/other/20240903/912enamel-pins-inc.-Instagram.png"
							alt="enamel pins inc.-Instagram" title="enamel pins inc.-Instagram" decoding="async" loading="lazy" /></a>
					<a href="https://www.youtube.com/channel/UCEbai5QbNkaP35UU1Jeh-pQ/featured" target="_blank"><img
							src="https://static-oss.gs-souvenir.com/web/2024/other/20240903/350enamel-pins-inc.-youtube.png"
							alt="enamel pins inc.-youtube" title="enamel pins inc.-youtube" decoding="async" loading="lazy" /></a>
				</div>
			</div>
		</div>
		<div class="content">
			<div class="left">
				<div class="swiper-area"
					:class="{ showThumbs: styleConfig?.mbHasThumbs, paginationHidn: styleConfig?.mbPaginationHidn }">
					<div class="swiper myswiper1" ref="swiper1">
						<div class="swiper-wrapper">
							<div class="swiper-slide" v-for="(item, index) in thumbsImgList" :key="index">
								<img :src="item.url" :alt="item.alt" :title="item.alt" />
							</div>
							<div class="swiper-slide" alt="Custom Pins" title="Custom Pins" v-if="showOpenGalleryBtn"
								@click="openGallery">
								<div class="openGalleryBox" @click.stop :alt="imgList[imgList.length - 1]?.alt"
									:title="imgList[imgList.length - 1]?.alt">
									<!-- <span>{{ lang.photo }}</span>
									<span>{{ lang.gallery }}</span>
									<span>>>></span> -->
									<EditDiv v-for="(item, index) in modal.list" :key="index" :tagName="item.title.tagName"
										v-model:content="item.title.value" v-if="item.title.value && item.type === 'openGalleryBtn'"
										@click="setModalType(item.title, modal.list, 'text')" />
								</div>
							</div>
						</div>
					</div>
					<div class="swiper myswiper2" ref="swiper2">
						<div class="swiper-wrapper">
							<div class="swiper-slide" v-for="(item, index) in imgList" :key="index">
								<img :src="item.url" :alt="item.alt" :title="item.alt" style="width: 100%; height: 100%" />
							</div>
						</div>
						<div class="swiper-pagination"></div>
					</div>
				</div>
				<div class="des-info">
					<div class="des-title">
						<EditDiv v-for="(item, index) in modal.list" :key="index" :tagName="item.title.tagName"
							v-model:content="item.title.value" class="des-t" v-if="item.title.value && item.type === 'descHeadTitle'"
							@click="setModalType(item.title, modal.list, 'text')" />
						<div>
							<EditDiv v-for="(item, index) in modal.list" :key="index" :tagName="item.title.tagName"
								v-model:content="item.title.value" class="tag" v-if="item.title.value && item.type === 'descHead'"
								@click="setModalType(item.title, modal.list, 'text')" />
						</div>
					</div>
					<div class="des">
						<EditDiv v-for="(item, index) in modal.list" :key="index" :tagName="item.title.tagName"
							v-model:content="item.title.value" v-if="item.title.value && item.type === 'descText'"
							@click="setModalType(item.title, modal.list, 'text')" />
					</div>
				</div>
			</div>
			<div class="quote-review">
				<EditDiv class="title" style="display: flex; align-items: center" v-for="(item, index) in modal.list"
					:key="index" :tagName="item.title.tagName" v-model:content="item.title.value"
					v-if="currentStep === 1 && item.title.value && item.type === 'cateNameTitle'"
					@click="setModalType(item.title, modal.list, 'text')" />
				<div class="excellent">
					<span>{{ langLayout.excellent }} <b class="icon-star" v-for="item in 5"></b> 40,000+ {{ langSemi.reviews
						}}</span>
					<span><span class="text-primary">20+ {{ lang.views }}</span> {{ lang.in24h }}</span>
				</div>
			</div>
			<div class="right">
				<div class="right-con">
					<div class="quote-tip" v-if="currentStep === 1 && !(styleConfig?.quoteTipHidn === true)">
						<EditDiv v-for=" (item, index) in modal.list" :key="index" :tagName="item.title.tagName"
						v-model:content="item.title.value" v-if="item.title.value && item.type === 'quoteText'"
						@click="setModalType(item.title, modal.list, 'text')" />
					<div style="display: flex; flex-direction: column; align-items: center; row-gap: 10px">
						<nuxt-link :to="cateInfo?.quoteRoutingName || '/quote/pins-quote'" primary class="getQuoteBtn"
							:alt="quoteTitle" :title="quoteTitle"> {{
								lang.getProfessionalQuote }} <b class="icon-bps-xiaozhankai"></b></nuxt-link>
						<EditDiv style="display: flex; align-items: center" v-for="(item, index) in modal.list" :key="index"
							:tagName="item.title.tagName" v-model:content="item.title.value"
							v-if="isManage && item.title.value && item.type === 'quoteTitle'"
							@click="setModalType(item.title, modal.list, 'text')" />
					</div>
				</div>
				<div class="step-wrap" :class="{ back: currentStep > 1 }">
					<div class="return" @click="prevStep" v-show="currentStep > 1"><b class="icon-Return"></b>{{ lang.return }}
					</div>
					<step-bar :active-step="currentStep" :step="2" color="#EBEBEB"></step-bar>
				</div>
				<div class="stepList">
					<template v-for="(item, index) in generalData">
						<div class="step-item step-size" :key="index" v-if="item.customStepName === 'size' && currentStep === 1">
							<StepTitle :title="getTitle(item)" :step="getStep(item)"></StepTitle>
							<StepSize :style="{ ...(styleConfig?.stepItemStyle || {}) }" :itemData="item" :hideHelp="true"
								:selectedData="selectedData" @selectQuoteParams="selectQuoteParams($event.item, $event.citem)">
							</StepSize>
						</div>
						<div class="step-item step-qty" :key="index" v-if="item.customStepName === 'qty' && currentStep === 1">
							<StepTitle :title="getTitle(item)" :step="getStep(item)"></StepTitle>
							<StepQty :style="{ ...(styleConfig?.stepItemStyle || {}) }" :qtyList="qtyList" :hidePriceTableTitle="true"
								:isCustom.sync="isCustom" :selectedQtyInd.sync="selectedQtyInd" :customNumber.sync="customNumber"
								:customNumberUnitPrice="customNumberUnitPrice" :customNumberPrice="customNumberPrice"
								@filterCustomNumber="filterCustomNumber" @selectQtyList="selectQtyList"
								:hideSavePrice="styleConfig?.savePriceHidn === true"></StepQty>
						</div>
						<div class="step-item step-plating" v-if="item.customStepName === 'plating' && currentStep === 2">
							<StepTitle :title="getTitle(item)" :step="getStep(item)"></StepTitle>
							<div class="step-item-params">
								<div class="param-item" :style="{ ...(styleConfig?.stepItemStyle || {}) }"
									v-for="citem in item.childList" :key="citem.id" :class="{
										active: hasId(citem.id, selectedData[item.paramName]),
									}" @click="selectQuoteParams(item, citem)">
									<div class="imgBox">
										<img loading="lazy"
											:src="parseJSON(citem.imageJson)[1] ? parseJSON(citem.imageJson)[1].url : parseJSON(citem.imageJson)[0].url"
											:alt="parseJSON(citem.imageJson)[0].alt" :title="parseJSON(citem.imageJson)[0].alt" />
									</div>
									<span>{{ citem.alias }}</span>
								</div>
							</div>
						</div>
						<div class="step-item step-ribbon" v-if="item.customStepName === 'attachment' && currentStep === 2">
							<StepTitle :title="getTitle(item)" :step="getStep(item)"></StepTitle>
							<div class="step-item-params">
								<div v-for="citem in item.childList" :key="citem.id" :style="{ ...(styleConfig?.stepItemStyle || {}) }"
									class="param-item" :class="{
										active: hasId(citem.id, selectedData[item.paramName]),
									}" @click="selectQuoteParams(item, citem)">
									<img loading="lazy" :src="parseJSON(citem.imageJson)[0].url" :alt="parseJSON(citem.imageJson)[0].alt"
										:title="parseJSON(citem.imageJson)[0].alt" />
									<span>{{ citem.alias }}</span>
								</div>
							</div>
						</div>
						<div class="step-item step-upload" v-if="item.customStepName === 'upload' && currentStep === 2">
							<StepTitle :title="getTitle(item)" :step="getStep(item)"></StepTitle>
							<div class="step-item-params" :class="{ 'param-item-hasUpload': uploadArtworkList.length }">
								<template v-if="!uploadArtworkList.length">
									<div class="param-item" @click="openUpload"
										:style="{ ...(styleConfig?.stepItemStyle || {}), ...(styleConfig?.uploadStepItemStyle || {}) }">
										<div class="selfUpload" v-for="(item, index) in modal.list" :key="index"
											v-if="item.type === 'uploadBtnIcon'">
											<b v-if="item.icon && item.type === 'uploadBtnIcon' && hasUploadBtnIcon" class="selfUploadIcon"
												:class="item.icon.value" @click="setModalType(item.icon, modal.list, 'icon')"></b>
											<EditDiv tagName="span" class="uploadText" v-model:content="item.title.value"
												v-if="item.title.value && item.type === 'uploadBtnIcon'"
												@click="setModalType(item.title, modal.list, 'text')" />
										</div>
										<span v-show="!hasUploadBtnIcon">{{ lang.uploadFiles }}</span>
										<el-tooltip v-if="!hasUploadBtnIcon2" popper-class="cusToolTip" effect="light">
											<b style="margin-left:4px;" class="icon-wenhao" @click.stop></b>
											<div slot="content">
												<div>{{ lang.maxFile }}</div>
												<div>{{ item.tips }}</div>
											</div>
										</el-tooltip>
										<el-tooltip v-else popper-class="cusToolTip" effect="light">
											<span v-for="(item, index) in modal.list" :key="index" v-if="item.type === 'uploadBtnIcon2'">
												<b v-if="item.icon && item.type === 'uploadBtnIcon2' && hasUploadBtnIcon2"
													class="selfUploadIcon2" :class="item.icon.value"
													@click="setModalType(item.icon, modal.list, 'icon')" @click.stop></b>
												<EditDiv tagName="span" style="margin-left: 0.2rem" v-model:content="item.title.value"
													v-if="isManage && item.title.value && item.type === 'uploadBtnIcon2'"
													@click="setModalType(item.title, modal.list, 'text')" />
											</span>
											<div slot="content">
												<div>{{ lang.maxFile }}</div>
												<div>{{ item.tips }}</div>
											</div>
										</el-tooltip>
									</div>
									<div class="param-item" :class="{ active: isLater }"
										:style="{ ...(styleConfig?.stepItemStyle || {}), ...(styleConfig?.uploadStepItemStyle || {}) }"
										@click="setIsLater(true)">
										<span>{{ lang.emailUsLater }}</span>
									</div>
								</template>
								<template v-else>
									<div class="param-item-hasUpload-left">
										<span class="upload-name">{{ uploadArtworkList[0].original_filename }}</span>
										<b class="icon-icon_Preview myIcon" @click.stop="previewImg(uploadArtworkList[0].secure_url)"></b>
										<b class="icon-check" style="color: #0cbd5f"></b>
										<b class="icon-shanchu2" style="color: #b6b0b0" @click="deleteUpload(0)"></b>
										<div v-click-outside="() => (showExtend = false)">
											<div class="icon" :class="{ active: showExtend }" style="color: #eb7e1a"
												@click="showExtend = !showExtend" v-if="uploadArtworkList.length > 1">
												<b class="icon-bps-xiaozhankai"></b>
											</div>
											<div class="extendUpload" v-show="showExtend">
												<div class="arrow"></div>
												<div class="uploadFileWrap">
													<div class="upload-item" v-for="(item, index) in uploadArtworkList" :key="index">
														<span class="upload-name">{{ item.original_filename }}</span>
														<b class="icon-icon_Preview myIcon" @click.stop="previewImg(item.secure_url)"></b>
														<b class="icon-check" style="color: #0cbd5f"></b>
														<b class="icon-shanchu2" style="color: #b6b0b0" @click="deleteUpload(index)"></b>
													</div>
												</div>
											</div>
										</div>
									</div>
									<div class="uploadWrap" @click="openUpload">
										<b class="icon-shangchuan uploadIcon"></b>
										<span class="brow">{{ lang.browse }}</span>
										<el-tooltip v-if="!hasUploadBtnIcon2" popper-class="cusToolTip" effect="light">
											<b style="margin-left:4px;" class="icon-wenhao" @click.stop></b>
											<div slot="content">
												<div>{{ lang.maxFile }}</div>
												<div>{{ item.tips }}</div>
											</div>
										</el-tooltip>
										<el-tooltip v-else popper-class="cusToolTip" effect="light">
											<span v-for="(item, index) in modal.list" :key="index" v-if="item.type === 'uploadBtnIcon2'">
												<b v-if="item.icon && item.type === 'uploadBtnIcon2' && hasUploadBtnIcon2"
													class="selfUploadIcon2" :class="item.icon.value"
													@click="setModalType(item.icon, modal.list, 'icon')" @click.stop></b>
											</span>
											<div slot="content">
												<div>{{ lang.maxFile }}</div>
												<div>{{ item.tips }}</div>
											</div>
										</el-tooltip>
									</div>
								</template>
								<input type="file" ref="upload" :accept="acceptFileType" multiple @change="uploadPic" />
							</div>
						</div>
						<div class="step-item step-time" :class="{ [styleConfig?.timeStepStyle || '']: true }"
							v-if="item.customStepName === 'discount' && currentStep === 2">
							<StepTitle :title="getTitle(item)" :step="getStep(item)"></StepTitle>
							<EditDiv v-for=" (item, index) in modal.list" class="timeStepTip" :key="index"
								:tagName="item.title.tagName" v-model:content="item.title.value"
								v-if="item.title.value && item.type === 'timeStepTip'"
								@click="setModalType(item.title, modal.list, 'text')" />
							<div class="step-item-params">
								<div class="param-item-wrap" :style="{ ...(styleConfig?.stepItemStyle || {}) }"
									v-for="citem in getNewDiscountList(item)" :key="citem.id" :class="{
										active: hasId(citem.id, selectedData[item.paramName]),
									}" @click="selectQuoteParams(item, citem)">
									<div class="param-item">
										<DiscountText
											:primaryWeightPrice="styleConfig?.primaryWeightPrice && citem.priceInfo.priceType === 10"
											:itemData="citem" :textConfig="{ freeText: lang.NoDiscount }">
										</DiscountText>
									</div>
									<div class="des">{{ citem.alias }}</div>
								</div>
							</div>
						</div>
					</template>
					<div v-show="currentStep === 1 && device !== 'mb'"></div>
					<div class="free-tip2" v-show="currentStep === 1">
						<span v-for="(item, index) in modal.list" :key="index" v-if="item.type === 'iconAndText2'">
							<b v-if="item.icon && item.type === 'iconAndText2'" :class="item.icon.value"
								@click="setModalType(item.icon, modal.list, 'icon')"></b>
							<EditDiv tagName="span" v-model:content="item.title.value"
								v-if="item.title.value && item.type === 'iconAndText2'"
								@click="setModalType(item.title, modal.list, 'text')" />
						</span>
					</div>
					<div class="nextBtn" v-show="currentStep === 1">
						<button primary :style="{ ...(styleConfig?.buttonStyle || {}) }" @click="nextStep" :alt="continueTitle"
							:title="continueTitle" :disabled="customQty <= 0">
							{{ lang.continue }}
						</button>
						<EditDiv style="display: flex; align-items: center" v-for="(item, index) in modal.list" :key="index"
							:tagName="item.title.tagName" v-model:content="item.title.value"
							v-if="isManage && item.title.value && item.type === 'continueTitle'"
							@click="setModalType(item.title, modal.list, 'text')" />
						<p class="tip" :class="{ hidden: styleConfig?.hideNextText === true }">{{ nextText }}</p>
					</div>
				</div>
				<div class="subtotal" v-if="currentStep > 1 && !styleConfig?.nextIsUpload">
					<div class="subtotal-detail">
						<div class="subtotal-left">
							<div class="sub-item">
								<div class="sub-item-left">{{ lang.Quantity }}:</div>
								<div class="sbu-item-right">
									{{ customQty || 0 }}<span v-if="presentedQuantity > 0 && presentedQuantity <= giftQuantity"> +
									</span><span style="color: #ff0000"
										v-if="presentedQuantity > 0 && presentedQuantity <= giftQuantity">{{
											presentedQuantity }} {{ lang.free }}</span>
								</div>
							</div>
							<div class="sub-item" v-show="!onlyAddInquiry">
								<div class="sub-item-left">{{ lang.moldPrice }}:</div>
								<div class="sbu-item-right">
									<CCYRate :price="priceInfo.toolingCharge"></CCYRate>
								</div>
							</div>
							<div class="sub-item" v-show="!onlyAddInquiry">
								<div class="sub-item-left">{{ lang.unitPrice }}:</div>
								<div class="sbu-item-right">
									<CCYRate :price="priceInfo.foundationUnitPrice"></CCYRate>
								</div>
							</div>
							<div class="sub-item" v-show="!onlyAddInquiry && discountPrice != 0">
								<div class="sub-item-left">{{ text1 }}</div>
								<div class="sbu-item-right">
									<strong class="text-primary">
										{{ text2 }}
										<CCYRate :price="discountPrice"></CCYRate>
									</strong>
								</div>
							</div>
							<div class="sub-item">
								<div class="sub-item-left">
									<strong class="final-price">{{ lang.subtotal }}:</strong>
								</div>
								<div class="sbu-item-right">
									<CCYRate class="final-price" :price="finalPrice"></CCYRate>
									<CCYRate class="before-discount-price" :price="originPrice" v-if="finalPrice !== originPrice">
									</CCYRate>
								</div>
							</div>
						</div>
						<div class="subtotal-right">
							<div class="free-tip">
								<span v-for="(item, index) in modal.list" :key="index" v-if="item.type === 'iconAndText'">
									<b v-if="item.icon && item.type === 'iconAndText'" :class="item.icon.value"
										@click="setModalType(item.icon, modal.list, 'icon')"></b>
									<EditDiv tagName="span" v-model:content="item.title.value"
										v-if="item.title.value && item.type === 'iconAndText'"
										@click="setModalType(item.title, modal.list, 'text')" />
								</span>
							</div>
							<div class="btnGroup">
								<EditDiv v-for="(item, index) in modal.list" :key="index" :tagName="item.title.tagName"
									v-model:content="item.title.value" v-if="isManage && item.title.value && item.type === 'submitTitle'"
									@click="setModalType(item.title, modal.list, 'text')" />
								<button :style="{ ...(styleConfig?.buttonStyle || {}) }" primary plain @click="addInquiry"
									:alt="submitTitle" :title="submitTitle">
									{{ lang.submitInquiry }}
								</button>
								<button :style="{ ...(styleConfig?.buttonStyle || {}) }" primary
									v-if="onlyAddInquiry === 0 || !onlyAddInquiry" @click="addCart" :title="addCartTitle"
									:alt="addCartTitle">
									{{ lang.addToCart }}
									<b class="icon-a-Rectangle34626599Stroke tip-icon" @click.stop></b>
								</button>
								<EditDiv v-for="(item, index) in modal.list" :key="index" :tagName="item.title.tagName"
									v-model:content="item.title.value" v-if="isManage && item.title.value && item.type === 'addCartTitle'"
									@click="setModalType(item.title, modal.list, 'text')" />
							</div>
						</div>
					</div>
					<EditDiv class="des" v-for="(item, index) in modal.list" :key="index" :tagName="item.title.tagName"
						v-model:content="item.title.value" v-if="item.title.value && item.type === 'descText2'"
						@click="setModalType(item.title, modal.list, 'text')" />
				</div>
			</div>
		</div>
		<div class="des-info">
			<div class="des-title">
				<EditDiv v-for="(item, index) in modal.list" :key="index" :tagName="item.title.tagName"
					v-model:content="item.title.value" class="des-t" v-if="item.title.value && item.type === 'descHeadTitle'"
					@click="setModalType(item.title, modal.list, 'text')" />
				<div>
					<EditDiv v-for="(item, index) in modal.list" :key="index" :tagName="item.title.tagName"
						v-model:content="item.title.value" class="tag" v-if="item.title.value && item.type === 'descHead'"
						@click="setModalType(item.title, modal.list, 'text')" />
				</div>
			</div>
			<div class="des">
				<EditDiv v-for="(item, index) in modal.list" :key="index" :tagName="item.title.tagName"
					v-model:content="item.title.value" v-if="item.title.value && item.type === 'descText'"
					@click="setModalType(item.title, modal.list, 'text')" />
			</div>
		</div>
	</div>
	<!--			询盘弹窗-->
	<infoDialog :infoDialogVisible.sync="infoDialogVisible" :uploadList.sync="uploadList" :otherUpload="uploadArtworkList"
		@getValue="getValueFun"></infoDialog>
	<BaseDialog v-model="noFileDialog" :width="device != 'mb' ? '485px' : '90%'" :model="false">
		<template #closeIcon>
			<div style="display: none"></div>
		</template>
		<infoUpload :infoUploadList="infoUploadList" @pushInfoList="pushInfoList" @delInfoList="delInfoList"
			@updateInquiry="updateInquiry" @closeInfoDialog="closeInfoDialog"> </infoUpload>
	</BaseDialog>
	<UploadArea v-show="showUpload" :uploadList="uploadArtworkList" @back="back"></UploadArea>
	</div>
</template>

<script>
import "@/plugins/element";
import StepBar from "@/components/modal/Quote/QuoteComponents/StepBar.vue";
import StepTitle from "@/components/modal/Quote/QuoteComponents/StepTitle.vue";
import StepSize from "@/components/modal/Quote/QuoteComponents/StepSize.vue";
import StepQty from "@/components/modal/Quote/QuoteComponents/StepQty.vue";
import FreeTip from "@/components/modal/Quote/QuoteComponents/FreeTip.vue";
import DiscountText from "@/components/modal/Quote/QuoteComponents/DicountText.vue";
import {debounce, deepClone, isImageType, round2, scrollToViewTop} from "@/utils/utils";
import { calculate, calculateAll, getInfo, otoAddCart, otoEditInquiry, setInquiry } from "@/api/pins";
import { getCateParamRelationByCateId } from "@/api/web";
import { acceptFileType, checkFile } from "@/utils/validate";
import { uploadFile } from "@/utils/oss";
import QuoteBtn from "@/components/Quote/QuoteBtn.vue";
import InfoDialog from "@/components/Medals/infoDialog.vue";
import infoUpload from "@/components/Medals/infoUpload.vue";
import BaseDialog from "@/components/Quote/BaseDialog.vue";
import UploadArea from "@/components/modal/Quote/BannerQuote/UploadArea.vue";
import {findSelectDiscount, getIsSmallQty, getQuoteTime} from "@/assets/js/QuotePublic";
const setIndex = (data) => {
	if (!data) {
		return false;
	}
	let index = 0;
	data.forEach((item) => {
		if (item) {
			if (!item.noShowDetail) {
				index += 1;
			}
			item.customIndex = index;
		}
	});
	return data;
};
const sortByKey = (array, key) => {
	return array.sort(function (a, b) {
		let x = a.priceInfo[key]; //如果要从大到小,把x,y互换就好
		let y = b.priceInfo[key];
		return x < y ? -1 : x > y ? 1 : 0;
	});
};

const getMyCustomStepName = (item) => {
	let paramName = item.paramName,
		type = item.paramType;
	let qtyNameArr = ["qty", "Quantity"],
		attachNameArr = ["Select Attachment"],
		platingNameArr = ["Select Metal Finish"],
		uploadNameArr = ["Upload Artwork & Comments"];
	if (type === "SIZE") {
		return "size";
	}
	if (type === "COLOR") {
		return "color";
	}
	if (type === "DISCOUNT") {
		return "discount";
	}
	if (qtyNameArr.includes(paramName)) {
		return "qty";
	}
	if (attachNameArr.includes(paramName)) {
		return "attachment";
	}
	if (uploadNameArr.includes(paramName)) {
		return "upload";
	}
	if (platingNameArr.includes(paramName)) {
		return "plating";
	}
};
const addCustomProperty = (data) => {
	let handle = (list) => {
		for (let i = 0; i < list.length; i++) {
			let item = list[i];
			item.remark = "";
			item.noShowDetail = false;
			item.isHidden = false; //不可选属性
			item.files = [];
			item.inputNum = undefined;
			if (item.childList && item.childList.length) {
				handle(item.childList);
			}
			item.customStepName = getMyCustomStepName(item);
			//特殊处理
			if (item.paramName === "Upload Artwork & Comments") {
				item.alias = "Upload Your Artwork";
			}
		}
	};
	handle(data);
	return data;
};
export default {
	props: {
		data: {
			type: Object,
			default: {},
		},
	},
	components: {
		BaseDialog,
		infoUpload,
		InfoDialog,
		QuoteBtn,
		DiscountText,
		FreeTip,
		StepQty,
		StepSize,
		StepTitle,
		StepBar,
		UploadArea
	},
	data() {
		return {
			isManage: false,
			inquiryId: "",
			infoUploadList: [],
			noFileDialog: false,
			uploadList: [],
			infoDialogVisible: false,
			mapMessage: {
				upload: "Please Upload your Artwork.",
				size: "Please Select Your Size.",
				qty: "Please Select Your Qty.",
				attachment: "Please Select Your Attachment.",
				discount: "Please Select your discount.",
			},
			giftQuantity: 0,
			onlyAddInquiry: 0,
			modal: {
				style: {},
				type: {},
				...this.data,
			},
			acceptFileType,
			showFinishedDialog: false,
			showExtend: false,
			uploadArtworkList: [],
			customNumber: "",
			selectedParamsValueParent: {},
			selectedParamsValue: {},
			isCustom: false,
			isDs: 0,
			isBannerQuote: 1,
			loadAddCart: false,
			debounceCalcPrice: null,
			stepName: "",
			cateInfo: {},
			generalData: [],
			isLater: false,
			qtyList: [],
			priceInfo: {
                isSmallWeight: 1
            },
			selectedData: {},
			selectedQtyInd: -1,
			currentStep: 1,
			imgList: [],
			showUpload: false
		};
	},
	watch: {
		selectedData: {
			handler() {
				this.debounceCalcPrice();
			},
			deep: true,
		},
		modal: {
			handler(val) {
				if (process.env.isManage) this.$emit("update:data", val);
			},
			immediate: true,
			deep: true,
		},
	},
	computed: {
		text1() {
			let discountName = "";
            //加急费，重量加价
            if (this.priceInfo.discountPrice) {
                return this.lang.rushDelivery;
            }
			if (this.discountPrice > 0) {
				discountName = this.lang.rushDelivery;
			} else if (this.discountPrice < 0) {
				discountName = this.lang.discount;
			}
			return discountName + ":";
		},
        text2() {
            let ac;
            if (this.priceInfo.totalPrice > this.subtotal) {
                ac = "+";
            } else {
                ac = "-";
            }
            return ac;
        },
		presentedQuantity() {
			return this.selectedData["Lanyard Popular Colors"] && this.selectedData["Lanyard Popular Colors"].reduce((accumulator, currentValue) => Number(accumulator) + Number(currentValue?.giftQuantity || 0), 0);
		},
		finalPrice() {
			return this.priceInfo.totalPrice;
		},
		originPrice() {
			let priceInfo = this.priceInfo;
			return priceInfo.foundationUnitPrice * priceInfo.totalQuantity + priceInfo.toolingCharge;
		},
		customNumberPrice() {
			return this.isCustom && this.customNumber ? this.priceInfo.totalPrice : 0;
		},
		customNumberUnitPrice() {
			return this.isCustom && this.customNumber ? this.priceInfo.foundationUnitPrice : 0;
		},
        subtotal() {
            return round2(round2(this.priceInfo.foundationUnitPrice * this.priceInfo.totalQuantity) + this.priceInfo.toolingCharge + (this.priceInfo.setupCharge || 0));
        },
        discountPrice() {
            if (this.priceInfo) {
                return `${Math.abs(this.priceInfo.totalPrice - this.subtotal)}`;
            } else {
                return 0;
            }
        },
		sizeValue() {
			let findSize = this.generalData.find((item) => {
				return item.paramType === "SIZE";
			});
			if (!findSize) {
				return "";
			}
			return this.selectedData[findSize.paramName] && this.selectedData[findSize.paramName][0]?.paramCode;
		},
		proId() {
			if (this.previewMode) {
				return 148;
			} else {
				return this.$store.state.proId;
			}
		},
		projectName() {
			if (this.previewMode) {
				return "o2o-site-style";
			} else {
				return this.$store.state.proName;
			}
		},
		previewMode() {
			return process.env.VUE_APP_MODE === "Preview";
		},
		proType() {
			return this.$store.state.proType;
		},
		lang() {
			return this.$store.getters.lang.quote || {};
		},
		langSemi() {
			return this.$store.getters.lang.semiCustom || {};
		},
		langLayout() {
			return this.$store.getters.lang.layout || {};
		},
		userUUID() {
			return this.$store.state.userUUID;
		},
		userId() {
			return this.$store.state.userInfo?.id;
		},
		isLogin() {
			return this.$store.getters.isLogin;
		},
		userInfo() {
			return this.$store.state.userInfo;
		},
		device() {
			return this.$store.state.device;
		},
		customQty() {
			if (this.isCustom) {
				return parseInt(this.customNumber) || 0;
			} else {
				return parseInt((this.qtyList && this.qtyList.length && this.qtyList[this.selectedQtyInd]?.totalQuantity) || 0);
			}
		},
		cateId() {
			return this.modal.quoteCateId;
		},
		quoteTitle() {
			const item = this.modal.list.find((item) => item.type === "quoteTitle");
			return item ? item.title.value : "";
		},
		continueTitle() {
			const item = this.modal.list.find((item) => item.type === "continueTitle");
			return item ? item.title.value : "";
		},
		submitTitle() {
			const item = this.modal.list.find((item) => item.type === "submitTitle");
			return item ? item.title.value : "";
		},
		addCartTitle() {
			const item = this.modal.list.find((item) => item.type === "addCartTitle");
			return item ? item.title.value : "";
		},
		styleConfig() {
			return this.modal.styleConfig || {};
		},
		thumbsImgList() {
			let newImgList = this.imgList
			if (this.showOpenGalleryBtn) {
				newImgList = this.imgList?.slice(0, this.imgList.length - 1)
			}
			return newImgList
		},
		hasCustomCateName() {
			let selfCateName = this.modal.list.find(item => item.type === 'cateName')
			if (!selfCateName) return false
			return (selfCateName.title?.value || '').replace(/<br[^>]*>/gi, '').trim().length > 0
		},
		showOpenGalleryBtn() {
			let selfopenGalleryBtn = this.modal.list.find(item => item.type === 'openGalleryBtn')
			if (!selfopenGalleryBtn) return false
			return (selfopenGalleryBtn.title?.value || '').replace(/<br[^>]*>/gi, '').trim().length > 0
		},
		hasUploadBtnIcon() {
			let uploadBtnIcon = this.modal.list.find(item => item.type === 'uploadBtnIcon')
			if (!uploadBtnIcon) return false
			if (!(uploadBtnIcon.icon?.value)) return false
			return (uploadBtnIcon.title?.value || '').replace(/<br[^>]*>/gi, '').trim().length > 0
		},
		hasUploadBtnIcon2() {
			let uploadBtnIcon2 = this.modal.list.find(item => item.type === 'uploadBtnIcon2')
			if (!uploadBtnIcon2) return false
			if (!(uploadBtnIcon2.icon?.value)) return false
			return (uploadBtnIcon2.title?.value || '').replace(/<br[^>]*>/gi, '').trim().length > 0
		},
		nextText() {
			return this.styleConfig?.nextText ? this.lang.bannerQuote[this.styleConfig.nextText] : ''
		},
	},
	methods: {
		closeInfoDialog(data) {
			this.infoUploadList = [];
			this.noFileDialog = data;
			if (this.isIframe) {
				let targetWindow = window.opener || window.parent; // 目标窗口，可以是父窗口或打开当前窗口的窗口
				targetWindow.postMessage(
					{
						type: "toHome",
					},
					window.origin
				); // 发送消息
			} else {
				window.location.href = "/";
			}
		},
		updateInquiry() {
			if (!this.inquiryId) {
				this.closeInfoDialog(false);
			}
			let newPicPath = this.infoUploadList.map((item) => item.secure_url);
			let data = {
				id: this.inquiryId,
				newPicPath: newPicPath,
			};
			setInquiry(data).then(() => {
				this.closeInfoDialog(false);
			});
		},
		pushInfoList(data) {
			this.infoUploadList.push(data);
		},
		delInfoList(index) {
			this.infoUploadList.splice(index, 1);
		},
		setIsLater(bool) {
			this.isLater = bool;
		},
		//参数选中事件
		async selectQuoteParams(item, citem) {
			this.selectedParamsValueParent = item;
			this.selectedParamsValue = citem;
			this.$set(this.selectedData, item.paramName, [citem]);
            this.handleWeightDiscount();
		},
		getTitle(item) {
			if (item.customStepName === "upload") {
				return item.alias;
			} else {
				return this.lang.Select + " " + item.alias;
			}
		},
		getStep(item) {
			return this.lang.step + " " + item.customIndex;
		},
		initSwiper() {
			let _this = this
			this.myswiper1 = new Swiper(this.$refs.swiper1, {
				slidesPerView: 5,
				direction: "vertical",
				spaceBetween: this.device === 'mb' ? 0 : 8,
				watchSlidesVisibility: true, //防止不可点击
				grabCursor: true,
			});
			this.myswiper2 = new Swiper(this.$refs.swiper2, {
				slidesPerView: 1,
				spaceBetween: 10,
				autoplay: false,
				grabCursor: true,
				observer: true,
				observeParents: true,
				thumbs: {
					swiper: this.myswiper1,
				},
				pagination: {
					el: ".swiper-pagination",
					clickable: true,
					renderBullet: function (index, className) {
						return '<span class="' + className + '">' + "</span>";
					},
				},
				on: {
					slideChangeTransitionStart: function (swiper) {
						const activeIndex = swiper.activeIndex;
						const previousIndex = swiper.previousIndex
						if (+activeIndex == +_this.imgList.length - 1 && _this.showOpenGalleryBtn) {
							swiper.slideTo(previousIndex)
							_this.openGallery()
						}
						// swiper.allowSlideNext = false;
						// swiper.allowSlidePrev = false;
					},
				}
			});
		},
		previewImg(img) {
			console.log(img);
			if (isImageType(img)) {
				this.$viewerApi({
					images: [img],
				});
			} else {
				window.open(img);
			}
		},
        clearField(e = "DISCOUNT") {
            let findDiscount = this.generalData.find((item) => item.paramType === e);
            if (findDiscount) {
                let name = findDiscount.paramName;
                this.selectedData[name] = [];
            }
        },
        getNewDiscountList(itemData) {
            let result = getQuoteTime(itemData.childList,this.priceInfo,this.proType),
                    originShowSmallPrice =  this.$store.state.showSmallPrice;
            this.$store.commit("setShowSmallPrice", result.newShowSmallPrice);
            if(result.newShowSmallPrice !== originShowSmallPrice){
                this.clearField("DISCOUNT")
            }
            return result.arr;
        },
		openUpload() {
			this.$refs.upload[0].click();
			this.setIsLater(false);
		},
		deleteUpload(ind) {
			this.uploadArtworkList.splice(ind, 1);
			if (this.uploadArtworkList.length <= 1) {
				this.showExtend = false;
			}
		},
		prevStep() {
			this.currentStep -= 1;
			if (this.styleConfig?.nextIsUpload) this.showUpload = false
			this.$emit("update:showQuickStep", false);
		},
		nextStep() {
			this.currentStep += 1;
			if (this.styleConfig?.nextIsUpload) this.showUpload = true
			this.$emit("update:showQuickStep", true);
		},
		back() {
			if (this.styleConfig?.nextIsUpload) this.showUpload = false
			this.currentStep = 1;
		},
		//判断是否选中
		hasId(id, arr) {
			if (!arr) {
				return false;
			}
			return !!arr.find((item) => {
				return item.id === id;
			});
		},
		parseJSON(str) {
			return str
				? JSON.parse(str)
				: [
					{
						url: "",
					},
				];
		},
		//参数选中事件
		replayUpload() {
			this.openUpload();
			this.$store.commit("setSizeDialog", false);
		},
		uploadPic(event, type = "upload") {
			this.$gl.show();
			let files = type === "upload" ? event.target.files : event;
			let fileResult = checkFile(Array.from(files), this.acceptFileType, 80);
			if (!fileResult) {
				this.$toast.error("File type error");
				this.$gl.hide();
				return false;
			}
			if (!fileResult.nomalSize.length) {
				this.$gl.hide();
				this.$store.commit("setSizeDialog", true);
				this.$store.commit("setInputRefName", "replayUpload");
				this.$store.commit("setOverSizeList", fileResult.overSize);
				this.$refs.upload[0].value = "";
				return false;
			}
			let uploadPromises = [];
			fileResult.nomalSize.forEach((file) => {
				let promise = uploadFile(file).then((res) => {
					this.uploadArtworkList.push({
						original_filename: file.name,
						secure_url: res,
						size: (file.size / 1024).toFixed(1),
					});
				});
				uploadPromises.push(promise);
			});
			Promise.all(uploadPromises).then(() => {
				this.$refs.upload[0].value = "";
				if (fileResult.overSize.length > 0) {
					this.$store.commit("setSizeDialog", true);
					this.$store.commit("setInputRefName", "replayUpload");
					this.$store.commit("setOverSizeList", fileResult.overSize);
				}
				this.$gl.hide();
			});
		},
		getQuoteParam(type = "inquiry") {
			let selectedData = deepClone(this.selectedData),
				finaData = [],
				generalData = deepClone(this.generalData);
			for (let i = 0; i < generalData.length; i++) {
				let item = generalData[i];
				if (item.customStepName === "qty") {
					continue;
				}
				if (item.customStepName === "upload") {
					item.childList = [];
					item.later = this.isLater;
					finaData.push(item);
					continue;
				}
				//将选中参数里面的childlist置空
				if (selectedData[item.paramName] && selectedData[item.paramName].length) {
					selectedData[item.paramName].forEach((c) => {
						c.childList = [];
						c.quantity = c.inputNum;
						if (!c.giftQuantity) {
							c.giftQuantity = 0;
						}
					});
				}
				item.childList = selectedData[item.paramName] || [];
				finaData.push(item);
			}
			let files = [];
			let artworkList = this.uploadArtworkList;
			if (artworkList.length > 0) {
				artworkList.forEach((item) => {
					item.secure_url && files.push(item.secure_url);
				});
			}
			return {
				classificationData: this.cateInfo,
				finaData: finaData,
				fontData: {
					fontImgCustom: files,
					comments: this.remark,
				},
				designCanvas: [],
			};
		},
		getPriceParam(qty) {
			const data = {
				cateId: this.cateId,
				discountId: "",
				packingIdList: [],
				paramIdList: [],
				projectName: this.projectName,
				quantity: "",
				sizeId: "",
				upgradesQtyDTO: [],
				qtyDetailDTOS: [],
			};
			let generalData = this.generalData,
				selectedData = this.selectedData;
			for (let i = 0; i < generalData.length; i++) {
				let item = generalData[i];
				let paramName = item.paramName;
				let selectedVal = selectedData[paramName];
				if (item.customStepName === "qty") {
					data.quantity = qty || Number(this.customQty);
					continue;
				}
				if (item.customStepName === "upload") {
					continue;
				}
				if (!selectedVal) {
					continue;
				}
				selectedVal.forEach((citem) => {
					let paramType = citem.paramType;
					switch (paramType) {
						case "NORMAL":
							data.paramIdList.push(citem?.priceInfo?.id);
							break;
						case "DISCOUNT":
							data.discountId = citem?.priceInfo?.id;
							break;
						case "SIZE":
							data.sizeId = citem?.priceInfo?.id;
							break;
						default:
							break;
					}
				});
			}
			if (!data.qtyDetailDTOS.length) {
				delete data.qtyDetailDTOS;
			}
			return data;
		},
		calcPrice() {
			let priceParam = this.getPriceParam();
			if (priceParam.sizeId && priceParam.cateId) {
				calculateAll(priceParam).then((res) => {
					let qtyList = res.data.filter((item) => item.isBannerQuote);
					let prevFoundationUnitPrice = qtyList[0]?.foundationUnitPrice;
					qtyList.forEach((item, index) => {
						if (index > 0) {
							item.save = (((prevFoundationUnitPrice - item.foundationUnitPrice) / prevFoundationUnitPrice) * 100).toFixed(0);
						}
					});
					this.qtyList = qtyList;
				});
			}
			if (priceParam.sizeId && priceParam.cateId && (priceParam.quantity || priceParam.qtyDetailDTOS?.length)) {
				calculate(priceParam).then((res) => {
					this.priceInfo = res.data;
				});
			}
		},
		checkParams() {
			let verify = true,
				stepName = "",
				selectedData = this.selectedData,
				generateData = this.generalData;
			for (let i = 0; i < generateData.length; i++) {
				let item = generateData[i];
				//参数名称
				let paramName = item.paramName;
				//选中的参数值
				let selectedVal = selectedData[paramName];
				if (item.customStepName === "upload") {
					if (!this.uploadArtworkList.length && !this.isLater) {
						stepName = item.customStepName;
						verify = false;
						break;
					}
				} else if (item.customStepName === "qty") {
					if (!this.customQty || this.customQty <= 0) {
						stepName = item.customStepName;
						verify = false;
						break;
					}
				} else {
					if (!selectedVal || selectedVal.length === 0) {
						stepName = item.customStepName;
						verify = false;
						break;
					}
				}
			}
			this.stepName = stepName;
			return verify;
		},
		getValueFun(val) {
			let quoteParam = this.getQuoteParam();
			let priceParam = this.getPriceParam();
			//询盘如果有期待时间，finaData手动添加数据
			if (val.expectTime) {
				quoteParam.finaData.push({
					parentId: 0,
					alias: "Expected Delivery Date",
					childList: [
						{
							parentId: 10000,
							alias: val.expectTime,
						},
					],
				});
			}
			let data = {
				platformProductId: this.cateInfo.platformProductId,
				proId: this.proId,
				email: "",
				productsName: this.projectName,
				quoteCateId: this.modal.quotePid,
				quoteCateChildId: priceParam.cateId,
				isMobile: this.device === "mb" ? 1 : 0,
				buyType: 7,
				quotePriceParam: JSON.stringify(priceParam),
				quoteParam: JSON.stringify(quoteParam),
				...val,
				telephone: val.areaCode + "-" + val.telephone,
				isDs: this.isDs,
				isBannerQuote: this.isBannerQuote,
                isSmallQty: getIsSmallQty(findSelectDiscount(this.selectedData),this.$store.state.enableTurnaroundTimeCheck)
			};
			this.otoEditInquiry(data);
		},

		otoEditInquiry(data) {
			if (this.isInquiry) {
				return false;
			}
			this.isInquiry = true;
			otoEditInquiry(data)
				.then((res) => {
					this.inquiryId = res.data;
					if (!this.uploadArtworkList.length && !this.uploadList.length) {
						this.noFileDialog = true;
					} else {
						this.$confirm(this.lang.p22, this.lang.p21, {
							confirmButtonText: this.lang.Confirm,
							type: "success",
							showCancelButton: false,
							center: true,
							customClass: "inquirySuccess",
							confirmButtonClass: "inquirySuccessBtn",
						}).finally(() => {
							window.location.href = "/";
						});
					}
				})
				.finally(() => {
					this.isInquiry = false;
				});
		},
		addInquiry() {
			if (!this.checkParams()) {
				this.$toast.error(this.mapMessage[this.stepName] || "Please improve the parameters");
				scrollToViewTop(document.getElementById(`${this.stepName}`));
				return false;
			}
			//如果是临时项目
			if (this.$store.state.isTemporary) {
				this.$store.commit("setMask", "temporary");
				return false;
			}
			this.infoDialogVisible = true;
		},
		async addCart() {
			if (!this.checkParams()) {
				this.$toast.error(this.lang.paramTip);
				scrollToViewTop(document.getElementById(`${this.stepName}`));
				return false;
			}
			//如果是临时项目
			if (this.$store.state.isTemporary) {
				this.$store.commit("setMask", "temporary");
				return false;
			}
			if (this.loadAddCart) {
				return false;
			}
			this.loadAddCart = true;
			let quoteParam = this.getQuoteParam("cart");
			let priceParam = this.getPriceParam();
			let data = {
				proId: this.proId,
				uuid: this.isLogin ? null : this.userUUID,
				userId: this.isLogin ? this.userId : null,
				buyType: 7,
				quoteCateId: this.modal.quotePid,
				isMobile: this.device === "mb" ? 1 : 0,
				quoteCateChildId: priceParam.cateId,
				quantity: this.customQty,
				quotePriceParam: JSON.stringify(priceParam),
				quoteParam: JSON.stringify(quoteParam),
				isDs: this.isDs,
				isBannerQuote: this.isBannerQuote,
                isSmallQty: getIsSmallQty(findSelectDiscount(this.selectedData),this.$store.state.enableTurnaroundTimeCheck)
			};
			otoAddCart(data)
				.then((res) => {
					this.$toast.success(res.message);
					this.$router.push({
						path: "/cart",
					});
				})
				.finally(() => {
					setTimeout(() => {
						this.loadAddCart = false;
					}, 1000);
				});
		},
		addToCart(data) {
			this.uploadArtworkList = data.uploadList;
			this.remark = data.remark;
			this.addCart();
		},
		skip() {
			this.isLater = true;
			this.addCart();
		},
		setModalType(target, targetArray, clickType, event, other) {
			this.$setModal(this, target, targetArray, clickType, event, other);
		},
		handleWeightDiscount() {
			//获取算价格参数
			let priceParam = this.getPriceParam();
			//获取折扣参数列表
			let discountList = this.generalData
				.find((item) => {
					return item.paramType === "DISCOUNT";
				})
				?.childList.filter((item) => item.priceInfo.priceType === 10);
			console.log(discountList);
			if (!discountList?.length) {
				return;
			}
			discountList.forEach((item) => {
				if (priceParam.sizeId && priceParam.cateId) {
					if (priceParam.sizeId && priceParam.cateId && (priceParam.quantity || priceParam.qtyDetailDTOS?.length)) {
						calculate(Object.assign({}, priceParam, { discountId: item.priceInfo.id })).then((res) => {
							this.$set(item.priceInfo, "customWeightPrice", res.data.discountPrice);
						});
					}
				}
			});
		},
		selectQtyList() {
			this.debounceCalcPrice();
			this.handleWeightDiscount();
		},
		filterCustomNumber() {
			this.debounceCalcPrice();
			this.handleWeightDiscount();
		},
		openGallery() {
			let data = {
				modal: "modalCustom",
				api: "listPageImgByPageId",
				showArrow: "icon-a-icon-jt4zhuanhuan",
				class: "img-swiper",
				showIndexPage: true,
				showTab: true,
				dialog: true,
				scroll: 1
			}
			this.$store.commit("setMask", data);
		}
	},
	async created() {
		this.debounceCalcPrice = debounce(this.calcPrice, 300);
	},
	async mounted() {
		this.$Bus.$on("skip", this.skip);
		this.$Bus.$on("addToCart", this.addToCart);
		if (process.env.isManage) {
			this.isManage = true;
		}
		try {
			this.$Bus.$on("replayUpload", this.replayUpload);
			let result = await Promise.all([
				getInfo({ id: this.cateId }),
				getCateParamRelationByCateId({
					cateId: this.cateId,
					isBannerQuote: 1,
				}),
			]);
			this.cateInfo = result[0]?.data || {};
			this.imgList = JSON.parse(this.cateInfo.imageJson);
			this.$nextTick(() => {
				this.initSwiper();
			});
			let data1 = result[1]?.data;
			let selectedData = {},
				isLater = false,
				selectedQtyInd = -1,
				selectedQty = 0,
				generalData = setIndex(sortByKey(addCustomProperty(data1), "bannerQuoteStepIndex"));
			//默认选中参数
			if (generalData?.length) {
				isLater = true;
				generalData.forEach((item) => {
					let customStepName = item.customStepName;
					if (customStepName !== "qty" && customStepName !== "upload") {
						let findDefault = item.childList.find((c) => c.priceInfo.isBannerQuoteSelected);
						console.log(findDefault);
						if (findDefault) {
							selectedData[item.paramName] = [findDefault];
							//选中尺寸下的默认数量
							if (customStepName === "size") {
								try {
									let priceInfo = findDefault.priceInfo;
									let increasePrice = JSON.parse(priceInfo.increasePrice).filter((item) => item.isBannerQuote);
									let defaultQtyIndex = increasePrice.findIndex((q) => q.isBannerQuoteSelected);
									if (defaultQtyIndex > -1) {
										selectedQtyInd = defaultQtyIndex;
										selectedQty = increasePrice[defaultQtyIndex].quantity;
									}
								} catch (e) { }
							}
						}
					}
				});
			}
			this.selectedData = selectedData;
			this.generalData = generalData;
			this.isLater = false;
			this.selectedQtyInd = selectedQtyInd;
			let priceParam = this.getPriceParam(selectedQty),
				priceResult;
			priceResult = await Promise.all([calculateAll(priceParam), calculate(priceParam)]);
			let qtyList = (priceResult && priceResult[0].data.filter((item) => item.isBannerQuote)) || [];
			let prevFoundationUnitPrice = qtyList[0]?.foundationUnitPrice;
			qtyList.forEach((item, index) => {
				if (index > 0) {
					item.save = (((prevFoundationUnitPrice - item.foundationUnitPrice) / prevFoundationUnitPrice) * 100).toFixed(0);
				}
			});
			console.log(qtyList);
			this.qtyList = qtyList;
			this.priceInfo = (priceResult && priceResult[1].data) || {};
			this.handleWeightDiscount();
		} catch (e) {
			console.log(e);
		}
	},
	beforeDestroy() {
		this.$Bus.$off("skip", this.skip);
		this.$Bus.$off("addToCart", this.addToCart);
	},
};
</script>

<style lang="scss" scoped>
@mixin selectedStyle {
	border-color: $color-primary !important;
	background-color: var(--bg-primary) !important;
}

.quote-wrap.modal-box {
	padding-top: 1.5rem !important;
	padding-bottom: 3.5rem !important;
	background: linear-gradient(to top, #ffffff 0%, #f9f2ea 100%);
	z-index: auto;

	@include respond-to(mb) {
		padding: 0 !important;
	}
}

.quote-wrap {
	&.isUpload {
		height: 40rem;

		@include respond-to(mb) {}
	}
}

.quote-title {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 0.75rem;
	font-size: 0.75rem;

	@include respond-to(mb) {
		display: none;
	}

	.img-box {
		gap: 1rem;

		img {
			width: 1.5rem;
		}
	}
}

.quote-review {
	order: -1;
	grid-column-start: span 2;
	margin-bottom: 1.1rem;
	display: flex;
	justify-content: space-between;
	align-items: center;

	.title {
		font-size: 1.5em
	}

	.excellent {
		display: flex;
		justify-content: space-between;
		align-items: baseline;
		column-gap: 0.5rem;
		font-size: calc(1em - 2px);
		margin-right: 1.72em;

		// padding: 0.75rem 1.5rem;
		@include respond-to(mb) {
			margin-right: 0;
			order: -1;
			column-gap: 2.8em;
			// padding: 0.8rem 1rem;
			margin-top: 0.8em;
		}

		b.icon-star {
			color: $color-primary;
		}
	}

	@include respond-to(mb) {
		margin-bottom: 0;
		grid-column-start: span 1;
		order: unset;
		flex-direction: column;

		.title {
			width: 100%;
			padding: 0.9em 1rem 0;
			font-size: 1.25em;
		}
	}
}

.content {
	display: grid;
	align-items: flex-start;
	grid-template-columns: 1fr 1.4fr;
	column-gap: 2.5rem;

	.des-info {
		display: none;

		.des-title {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin: 1rem 0;

			@include respond-to(mb) {
				flex-direction: column;
				justify-content: flex-start;
				align-items: flex-start;
				padding: 0 1rem;
			}

			.des-t {
				font-size: 1.13rem;

				@include respond-to(mb) {
					order: 1;
					margin-top: 1rem;
				}
			}

			.tag {
				padding: 0.4rem 0.8rem;
				margin-right: 0.44rem;
				background: #ffde9f;
				border-radius: 2.63rem;
				font-size: 0.88rem;

				&:last-child {
					margin-right: 0;
				}

				@include respond-to(mb) {
					order: 2;
				}
			}
		}

		.des {
			color: #6c655e;
			font-size: 0.88rem;

			@include respond-to(mb) {
				padding: 0 1rem;
			}

			p {
				margin-bottom: 0.5rem;

				&:last-child {
					margin-bottom: 0;
				}
			}
		}
	}

	@include respond-to(mb) {
		grid-template-columns: 1fr;
		gap: 0;
		padding-bottom: 1.5rem;

		&>.des-info {
			display: block;
		}
	}

	.left {
		min-width: 0;

		.des-info {
			display: block;
		}

		@include respond-to(mb) {
			.des-info {
				display: none;
			}
		}

		.swiper-area {
			display: flex;
			gap: 0.5rem;
			height: 30.13rem;

			@include respond-to(mb) {
				height: 31.25rem;

				&.showThumbs {
					margin: 1rem;
					height: 24.28rem;

					.myswiper1 {
						display: block;
						flex-basis: 4.5rem;

						img {
							border-radius: 0.5rem;
						}

						.openGalleryBox {
							border-radius: 0.5rem;
							font-size: 0.75rem;
						}
					}

					.myswiper2 {
						img {
							border-radius: 0.8rem;
						}
					}

				}

				&.paginationHidn {
					.myswiper2 {
						.swiper-pagination {
							display: none;
						}
					}
				}
			}

			.myswiper1 {
				flex-basis: 5.63rem;

				@include respond-to(mb) {
					display: none;
				}

				img {
					border-radius: 1.25rem;
				}

				.swiper-slide-thumb-active img {
					border: 2px solid #f96a00;
				}

				.openGalleryBox {
					aspect-ratio: 1/1;
					width: 100%;
					background: #F96A00;
					border-radius: 1.25rem;
					padding: 0.5rem;
					text-align: center;
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;
					color: #fff;
					font-size: 1rem;
					cursor: pointer;
					overflow: hidden;
				}
			}

			.myswiper2 {
				flex: 1;

				.swiper-pagination {
					display: none;

					@include respond-to(mb) {
						display: block;

						::v-deep .swiper-pagination-bullet {
							min-width: 3rem;
							height: 0.17rem;
							background: rgba(0, 0, 0, 0.24);
							border-radius: 0.25rem;

							&.swiper-pagination-bullet-active {
								background-color: $color-primary;
							}
						}
					}
				}

				img {
					border-radius: 1.25rem;

					@include respond-to(mb) {
						border-radius: 0;
					}
				}
			}
		}
	}

	.right {
		overflow: hidden;
		min-width: 0;
		background: #ffffff;
		box-shadow: 0rem 0.13rem 0.19rem 0rem rgba(69, 43, 13, 0.1), 0rem 0.38rem 0.38rem 0rem rgba(69, 43, 13, 0.09), 0rem 0.88rem 0.5rem 0rem rgba(69, 43, 13, 0.05), 0rem 1.56rem 0.63rem 0rem rgba(69, 43, 13, 0.01), 0rem 2.44rem 0.69rem 0rem rgba(69, 43, 13, 0);
		border-radius: 1.5rem 1.5rem 1.5rem 1.5rem;

		@include respond-to(mb) {
			margin: 1rem;
		}


		.right-con {
			padding: 1.5rem;

			@include respond-to(mb) {
				padding: 0;
			}

			.quote-tip {
				display: flex;
				justify-content: space-between;
				align-items: center;
				gap: 1rem;
				padding: 0.75rem 1rem;
				background-color: #d7dbff;
				border-radius: 0.75rem;
				font-size: 0.88rem;
				margin-bottom: 1.1rem;

				@include respond-to(mb) {
					flex-direction: column;
					gap: 0.67rem;
					border-radius: 0;
					text-align: center;
					margin-bottom: 0;
				}

				a.getQuoteBtn {
					flex-shrink: 0;
					width: 16rem;
					height: 2.5rem;
					background: #3a42ff;
					color: #ffffff;

					@include respond-to(mb) {
						width: auto;
					}
				}
			}

			.step-wrap {
				display: flex;
				justify-content: space-between;
				align-items: center;
				gap: 1rem;
				margin-bottom: 0.6rem;

				@include respond-to(mb) {
					padding: 1rem 1rem 0;
					margin-bottom: 0;

					::v-deep {
						.stepBar {
							display: none;
						}
					}
				}

				&.back {
					margin: 0 0 1rem;
					gap: 4rem;

					@include respond-to(mb) {
						margin-bottom: 0;
					}
				}

				h1 {
					width: 15.31rem;
					font-size: 1.5rem;

					@include respond-to(mb) {
						width: auto;
						font-size: 1.17rem;
					}
				}

				.return {
					margin-top: -0.4rem;
					cursor: pointer;

					b {
						margin-right: 0.5rem;
					}
				}

				.stepBar {
					// flex: 1;

					::v-deep {
						.stepLine {
							width: 13.31rem;
						}
					}
				}
			}
		}

		.stepList {
			display: grid;
			grid-template-columns: 1fr 2fr;
			grid-template-areas: "a b";
			gap: 1.2rem;

			/* 可选：设置行和列之间的间距 */


			@include respond-to(mb) {
				grid-template-columns: 1fr 1.5fr;
				gap: 0.58rem;
				align-items: flex-start;
				padding: 1rem;


				.step-item-title {
					font-size: 1rem;
				}
			}

			.step-item {
				font-size: 0.88rem;

				&.type2 {
					.step-item-params {
						grid-template-columns: repeat(2, 1fr);
					}
				}

				@include respond-to(mb) {
					font-size: 1rem;
					margin-bottom: 1.5rem;

					&.type2 {
						.step-item-params {
							grid-template-columns: repeat(2, 1fr);
						}
					}
				}
			}

			.free-tip2 {
				display: grid;
				grid-template-columns: repeat(2, 1fr);
				gap: 0.8rem;
				font-size: 0.88rem;
				font-weight: 700;

				b {
					margin-right: 0.3rem;
					color: #68bd2b;
				}

				@include respond-to(mb) {
					margin-bottom: 1.5rem;
					grid-column: span 2;
				}
			}

			.nextBtn {
				grid-column: 2 / span 1;
				display: flex;
				align-items: center;
				row-gap: 12px;
				column-gap: 20px;
				min-width: 0;

				@include respond-to(mb) {
					grid-column: span 2;
					flex-direction: column;
				}

				.tip {
					white-space: nowrap;
					font-size: 1rem;
					color: #999999;
					text-align: center;

					&.hidden {
						opacity: 0;
						visibility: hidden;
					}
				}
			}
		}

		.step-size {
			grid-area: a;

			.step-item-title .help {
				display: none;
			}

			@include respond-to(mb) {
				padding: 1rem 0.5rem;
				background-color: #fafafa;
				border-radius: 1rem;

				.step-size-params {
					grid-template-columns: repeat(1, 1fr);
				}
			}
		}

		.step-qty {
			grid-area: b;

			@include respond-to(mb) {
				padding: 1rem 0.5rem;
				background-color: #fafafa;
				border-radius: 1rem;

				::v-deep .step-qty-params .param-item.custom-qty {
					display: flex;
					gap: 0.5rem;
				}

				::v-deep .step-qty-params .param-item {
					grid-template-columns: 1.3fr 1fr 1fr;
				}

				::v-deep .step-qty-params .param-item:not(.custom-qty) .totalPrice {
					display: none;
				}

				::v-deep .step-qty-params .param-item .each {
					display: none;
				}

				::v-deep .step-qty-params .param-item.custom-qty .unitPrice {
					display: none;
				}
			}
		}

		.step-ribbon {
			grid-column: 1 / span 2;

			.step-item-params {
				display: grid;
				grid-template-columns: repeat(6, 1fr);
				grid-gap: 0.75rem;

				@include respond-to(mb) {
					grid-template-columns: repeat(2, 1fr);
					grid-gap: 0.46rem 0.83rem;
				}

				.param-item {
					overflow: hidden;
					display: flex;
					flex-direction: column;
					justify-content: center;
					align-items: center;
					position: relative;
					min-width: 0;
					background-color: #f6f6f6;
					padding: 0.2rem;
					cursor: pointer;
					border-radius: 0.75rem;
					border: 1px solid transparent;
					transition: all 0.3s;

					@include respond-to(mb) {
						padding: 0.5rem;
						flex-direction: row;
						border-radius: 0.5rem;
					}

					&.active {
						@include selectedStyle;
					}

					@media (any-hover: hover) {
						b:hover {
							color: $color-primary;
						}

						&:hover {
							border-color: $color-primary;
						}
					}

					span {
						overflow: hidden;
						white-space: nowrap;
						margin-right: 0.5rem;
						text-align: center;
					}

					img {
						height: 3rem;
						object-fit: contain;
						margin-bottom: 0.5rem;

						@include respond-to(mb) {
							width: auto;
							height: 1.67rem;
							margin-bottom: 0;
						}
					}
				}
			}
		}

		.step-plating {
			grid-column: 1 / span 2;

			.step-item-params {
				display: grid;
				grid-template-columns: repeat(3, 1fr);
				grid-gap: 0.63rem;

				.param-item {
					overflow: hidden;
					position: relative;
					min-width: 0;
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;
					gap: 0.2rem;
					padding: 0.2rem 1rem;
					background-color: #fafafa;
					cursor: pointer;
					border: 1px solid transparent;
					transition: all 0.3s;
					border-radius: 0.75rem;

					.imgBox {
						width: fit-content;
						border: 1px solid transparent;
						display: flex;
						align-items: center;
						justify-content: center;
						border-radius: 0.75rem;

						&.active {
							@include selectedStyle;

							.edit {
								display: block;
							}
						}
					}

					&.active {
						@include selectedStyle;

						.edit {
							display: block;
						}
					}

					@media (any-hover: hover) {
						b:hover {
							color: $color-primary;
						}

						&:hover {
							border-color: $color-primary;
						}
					}

					img {
						width: fit-content;
						object-fit: contain;
						max-height: 3rem;

						@include respond-to(mb) {
							max-width: 100%;
							height: 2.2rem;
							border-radius: 0.4rem;
						}
					}

					.edit {
						position: absolute;
						left: 4px;
						top: 4px;
						display: none;
					}
				}

				@include respond-to(mb) {
					grid-template-columns: repeat(3, 1fr);
					grid-gap: 0.42rem;

					.param-item {
						background-color: transparent;
						flex-direction: column;
						padding: 0.4rem 1rem;

						.imgBox {
							border-radius: 0.4rem;
							width: 100%;
							background-color: #fafafa;
						}
					}
				}
			}
		}

		.step-upload {
			grid-column: 1 / span 2;

			.selfUpload {
				display: flex;
				align-items: center;

				.selfUploadIcon {
					margin-right: 0.6rem;
					font-size: 1.4rem;
					color: $color-primary;

					@include respond-to(mb) {
						font-size: 1.2rem;
						margin-right: 0.4rem;
					}
				}

				.uploadText {
					color: $color-primary;
					text-decoration: underline;
				}

			}

			.selfUploadIcon2 {
				margin-left: 0.6rem;
				color: #666666;
				font-size: 1rem;

				@include respond-to(mb) {
					margin-right: 0.4rem;
				}

				@media (any-hover: hover) {
					&:hover {
						color: #666666 !important;
						border-color: #666666 !important;
					}
				}
			}

			.step-item-params {
				position: relative;
				display: flex;
				gap: 0.75rem;

				input[type="file"] {
					position: absolute;
					clip: rect(0 0 0 0);
				}

				.uploadWrap {
					position: relative;
					display: flex;
					align-items: center;
					cursor: pointer;

					.brow {
						margin: 0 4px;
						color: $color-primary;
						text-decoration-line: underline;
					}

					.icon-wenhao {
						color: $color-primary;
						font-size: 1rem;
					}

					.uploadIcon {
						margin-right: 0.2rem;
						font-size: 1.7rem;
						color: $color-primary;

						@include respond-to(mb) {
							font-size: 1.5rem;
							margin-right: 0.4rem;
						}
					}
				}

				.param-item {
					position: relative;
					display: flex;
					justify-content: center;
					align-items: center;
					width: 14.75rem;
					height: 2.63rem;
					border-radius: 0.75rem;
					cursor: pointer;
					border: 1px solid #ccc;

					.icon-wenhao {
						color: $color-primary;
						font-size: 1rem;
					}

					@include respond-to(mb) {
						padding: 0;
						border-radius: 0.5rem;
					}

					@media (any-hover: hover) {
						b:hover {
							color: $color-primary;
						}

						&:hover {
							border-color: $color-primary;
						}
					}

					&.active {
						@include selectedStyle;
					}
				}
			}

			.step-item-params.param-item-hasUpload {
				justify-content: space-between;
				padding: 0.2rem;

				.param-item-hasUpload-left {
					position: relative;
					display: flex;
					align-items: center;
					padding: 0.2rem;

					.icon {
						display: flex;
						justify-content: center;
						align-items: center;
						width: 24px;
						height: 24px;
						border-radius: 50%;
						margin: 0 6px;
						background: #ebebeb;

						b {
							color: #888888;
							font-size: 12px;
							transform: rotate(90deg);
						}
					}

					.icon.active {
						b {
							color: $color-primary;
						}
					}

					.extendUpload {
						position: absolute;
						left: 0;
						right: 0;
						top: calc(100% + 10px);
						background: #fafafa;
						box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.2);
						border-radius: 6px;
						border: 1px solid $color-primary;
						padding: 10px 10px 10px 26px;
						z-index: 100;

						.arrow {
							position: absolute;
							display: block;
							transform: translateY(-100%);
							right: 10px;
							top: 0;
							width: 0;
							height: 0;
							border-color: transparent;
							border-style: solid;
							border-width: 6px;
							border-bottom-color: $color-primary;
							border-top-width: 0;

							&::after {
								content: "";
								position: absolute;
								display: block;
								width: 0;
								height: 0;
								border-color: transparent;
								border-style: solid;
								border-width: 5px;
								top: 1px;
								margin-left: -5px;
								border-top-width: 0;
								border-bottom-color: #fff;
							}
						}

						.uploadFileWrap {
							overflow: hidden auto;
							max-height: 140px;

							.upload-item {
								display: flex;
								align-items: center;
								margin-bottom: 10px;
							}

							.upload-item:last-child {
								margin-bottom: 0;
							}
						}
					}

					.upload-name {
						overflow: hidden;
						text-overflow: ellipsis;
						width: 150px;
						white-space: nowrap;
						text-decoration: underline;

						@include respond-to(mb) {
							width: 100px;
						}
					}

					b {
						margin: 0 6px;
						cursor: pointer;
					}

					b.icon-Down {
						font-size: 12px;
					}
				}
			}
		}

		.step-time {
			grid-column: 1 / span 2;
			min-width: 0;

			.box-border {
				display: none;
			}

			.timeStepTip {
				font-weight: 400;
				font-size: 12px;
				color: #6C655E;
				text-align: left;
				text-transform: none;
				margin-bottom: 0.8rem;

				@include respond-to(mb) {}
			}

			.step-item-params {
				display: grid;
				grid-template-columns: repeat(4, 1fr);
				grid-gap: 0.75rem;

				@include respond-to(mb) {
					grid-template-columns: repeat(2, 1fr);
					grid-gap: 0.42rem;
					text-align: left;
				}

				.param-item-wrap {
					overflow: hidden;
					position: relative;
					border: 1px solid transparent;
					border-radius: 0.75rem;
					padding: 0.63rem;
					background-color: #fafafa;
					box-sizing: border-box;
					cursor: pointer;

					&.active {
						@include selectedStyle;
					}

					.param-item {
						position: relative;
						min-width: 0;
						margin-bottom: 0.2rem;
						font-weight: 700;

						@include respond-to(mb) {
							justify-content: flex-start;
						}
					}
				}
			}
		}

		.subtotal {
			padding: 1.25rem 1.5rem;
			background-color: #f6f6f6;
			margin: 1.5rem -1.5rem -1.5rem;

			@include respond-to(mb) {
				grid-template-columns: repeat(1, 1fr);
				grid-gap: 5px;
				border-radius: 0;
				margin: 0;
				padding: 1rem 0.83rem;
			}

			.des {
				margin-top: 0.63rem;
				color: #999;
				font-size: 0.75rem;

				@include respond-to(mb) {
					display: none;
				}
			}

			.subtotal-detail {
				display: grid;
				grid-template-columns: 1fr 1.3fr;
				align-items: center;
				grid-gap: 2.5rem;

				@include respond-to(mb) {
					grid-template-columns: 1fr;
					gap: 1rem;
				}

				.subtotal-left {
					min-width: 0;

					.sub-item {
						display: flex;
						align-items: center;
						justify-content: space-between;
						margin-bottom: 0.4rem;

						.sub-item-left {
							color: #6c655e;
						}

						@include respond-to(mb) {
							justify-content: space-between;
						}

						&:last-child {
							margin-bottom: 0;
						}
					}

					.final-price {
						font-size: 1.5rem;
						color: #333333;
						font-weight: 700;

						@include respond-to(mb) {
							font-size: 1.33rem;
						}
					}

					.before-discount-price {
						text-decoration: line-through;
						color: #6c655e;
					}
				}

				.subtotal-right {
					min-width: 0;
					text-align: center;

					.free-tip {
						display: flex;
						justify-content: center;
						align-items: center;
						gap: 1.44rem;
						font-size: 0.88rem;

						b {
							margin-right: 0.3rem;
							color: #68bd2b;
						}

						@include respond-to(mb) {
							display: none;
						}
					}

					.btnGroup {
						margin-top: 10px;

						@include respond-to(mb) {
							display: flex;
							gap: 0.58rem;
							margin-top: 0;
						}

						button {
							width: 24.25rem;
							margin: 0.3rem 0;
							border-radius: 4px;

							@include respond-to(mb) {
								border-radius: 0.67rem;
							}

							b {
								margin-left: 4px;
								color: #ffffff;
							}
						}
					}
				}
			}
		}
	}
}

.text-primary {
	color: $color-primary;
}

button[disabled] {
	background: #e2e2e2 !important;
}

button[primary][plain] {
	background-image: none;
	background-color: #ffffff;
	color: $color-primary;
	border: 1px solid $color-primary;
}

.quote-wrap.modal-box ::v-deep .uploadArea {
	position: absolute;
	inset: 0;
	align-items: center;
	background: linear-gradient(to top, #ffffff 0%, #f9f2ea 100%) !important;
	padding-top: 1.5rem !important;
	padding-bottom: 1.5rem !important;
	padding: 5em max(calc(50% - 700px), 1.5vw) !important;
	z-index: 1 !important;

	input[type='file'] {
		display: none;
	}

	.upload-box {
		width: 600px !important;
		height: 220px !important;

		.uploadList {
			margin: 6px 0 !important;

			.uploadIcon {
				margin-top: 0 !important;
				width: fit-content !important;
				height: fit-content !important;
			}
		}

		.upload-btn {
			button {
				width: 16rem !important;
				height: 48px !important;
				text-transform: uppercase !important;
				background-image: linear-gradient(to right, $btn-primary, $btn-second) !important;
				border-radius: 12px 12px 12px 12px !important;
			}
		}

		@include respond-to(mb) {
			width: 100% !important;
			height: 180px !important;
			margin-bottom: 1rem !important;

			.uploadList .uploadIcon {
				width: fit-content !important;
				height: fit-content !important;
			}

			.upload-btn {
				button {
					width: 14rem !important;
					height: 42px !important;
					border-radius: 10px !important;
				}
			}

		}

	}

	.t {
		width: 100% !important;
		margin: 0 0 1rem 0 !important;
		font-size: 1.5rem !important;
		font-weight: 700 !important;
	}

	.t .back {
		color: #333;
		font-weight: 400 !important;

		b {
			color: #2C2C2C !important;
		}

		top: 1.5rem !important;
		left: max(calc(50% - 700px), 1.5vw) !important;
	}

	.cartBtnWrap {
		button {
			width: 16rem !important;
			height: 48px !important;
			border-radius: 10px !important;
		}

		button[disabled] {
			background: #CDCDCD !important;
		}
	}

	@include respond-to(mb) {
		.mbTitle {
			display: none !important;

			.back {
				flex-direction: row !important;
				align-items: center !important;
				gap: 1rem !important;
				color: #333 !important;
				font-size: 1.2rem !important;

				b {
					font-size: 1.4rem !important;
					color: #2C2C2C !important;
				}
			}

			.step-item-title {
				display: none !important;
			}
		}

		.t {
			display: block !important;
		}

		.cartBtnWrap {
			button {
				width: 14rem !important;
				height: 42px !important;
				border-radius: 10px !important;
			}

			button[disabled] {
				background: #CDCDCD !important;
			}
		}
	}
}
</style>

/**
styleConfig:{} //传入组件里面的样式对象
timeStepStyle --- 步骤paramName+Step+Style
quoteTipHidn --- 隐藏dom 类名+Hidn ---手机端 mb+类名+Hidn
*/
