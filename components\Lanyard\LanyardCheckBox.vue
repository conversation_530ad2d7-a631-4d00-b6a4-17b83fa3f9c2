<template>
	<!-- :class="{ 'custom-shadow': customShadow, active: isActive() }" -->
	<div class="StepBox loadingBox" :class="{ 'custom-shadow': customShadow, active: activeType == 'parent' ? isActive : isActiveChild }" v-loading="loading">
		<label class="item pointer hover-type" :class="{ 'innerBorder': innerBorder }" :for="'chooseYourLanyardAttachmentLeft' + bindValue.id">
			<img v-if="freeTitle" src="http://customed-center.oss-us-west-1.aliyuncs.com/web/public/picStore/20231129/icon-free_20632WAdnA.png" :class="bindValue && bindValue.priceInfo && bindValue.priceInfo.unitPrice ? 'noShow' : 'freeTitle'" />

			<div class="flex justify-content-center align-items-center">
				<div class="se" @click.stop="changeInput($event)">
					<div class="swiper flex justify-content-center align-items-center">
						<div>
							<img :src="filterImage(bindValue.imageJson)" style="width: 100% !important" :style="aspectRatio" loading="lazy" />
						</div>
					</div>
					<span class="product-info">
						<label :for="bindValue.cateName + bindValue.id" class="radio-beauty"> </label>
						<span class="bTitle">{{ bindValue.alias }}</span>
					</span>
					<PriceText :paramData="bindValue" v-if="showPrice"></PriceText>
				</div>
			</div>
		</label>
	</div>
</template>
<script>
import VideoPlayer from "@/components/Quote/VideoPlayer.vue";
import PriceText from "@/components/Quote/PriceText";
export default {
	components: {
		VideoPlayer,
		PriceText,
	},
	props: {
		customShadow: {
			type: Boolean,
			default: false,
		},
		aspectRatio: {
			type: String,
		},
		//绑定值
		bindValue: {
			type: Object,
		},
		freeTitle: {
			type: Boolean,
			default: false,
		},
		innerBorder: {
			type: Boolean,
			default: false,
		},
		showPrice: {
			type: Boolean,
			default: false,
		},
		bindName: {
			type: String,
		},
		selectedData: {
			type: [Array, Object],
		},
		needLoading: {
			type: Boolean,
			default: false,
		},
		totalQuantity: {
			type: [String, Number],
			default: 0,
		},
		/**
		 * active parent / child
		 * child 激活为子项
		 */
		activeType: {
			type: String,
			default: "parent",
		},
	},
	data() {
		return {
			windowWidth: document.documentElement.clientWidth,
			picDialog: false,
			zoomPic: "",
			loadTarget: "",
			// activeData: {
			//   addComment: null,
			//   cateId: null,
			//   childList: [],
			//   chooseNum: null,
			//   createTime: null,
			//   id: null,
			//   imageJson: "",
			//   isModelShow: null,
			//   isRadio: null,
			//   modelPosition: null,
			//   onlyAddInquiry: null,
			//   paramCode: null,
			//   paramName: null,
			//   paramType: null,
			//   parentId: null,
			//   priceInfo: {},
			//   stepIndex: null,
			// },
		};
	},
	watch: {
		totalQuantity(val) {
			this.$set(this.bindValue, "inputNum", val);
		},
	},
	methods: {
		parseJSON(str) {
			return str
				? JSON.parse(str)
				: [
						{
							url: "",
						},
				  ];
		},
		activeFun(val) {
			this.bindValue.paramName = val.paramName;
			this.bindValue.imageJson = val.imageJson;
		},
		isJSON(str) {
			if (typeof str == "string") {
				try {
					var obj = JSON.parse(str);
					if (typeof obj == "object" && obj) {
						return true;
					} else {
						return false;
					}
				} catch (e) {
					return false;
				}
			} else {
				return false;
			}
		},
		filterImage(val, hasSec = false) {
			if (this.isJSON(val)) {
				if (hasSec) {
					return JSON.parse(val)[1] ? JSON.parse(val)[1].url : JSON.parse(val)[0].url;
				} else {
					return JSON.parse(val)[0].url;
				}
			} else {
				return val.url;
			}
		},
		filterPrice(val) {
			if (!val.unitPrice && !val.moldPrice) {
				return this.lang.free;
			} else if (!val.unitPrice && val.moldPrice) {
				return `+ ${this.lang.moldFee}: $${val.moldPrice.toFixed(0)}`;
			} else {
				return val.moldPrice ? `+ $${val.moldPrice.toFixed(0)} ${this.lang.Setup},+ $${val.unitPrice.toFixed(2)}/${this.lang.pc}` : `+ $${val.unitPrice.toFixed(2)}/${this.lang.pc}`;
			}
		},
		changeInput(e, type) {
			this.$emit("clickFun", {
				key: this.bindName,
				value: this.bindValue,
			});
			this.$emit("picDialogFun", false);
		},
		getVideoOptions(path, type, poster) {
			if (type === 1) {
				return {
					autoplay: true,
					controls: false,
					muted: true,
					loop: true,
					fill: true,
					sources: [
						{
							src: path,
							type: "video/mp4",
						},
					],
				};
			} else if (type === 2) {
				return {
					autoplay: true,
					controls: true,
					muted: false,
					loop: true,
					fill: true,
					sources: [
						{
							src: path,
							type: "video/mp4",
						},
					],
				};
			} else if (type === 3) {
				return {
					autoplay: true,
					controls: false,
					muted: true,
					loop: true,
					fill: true,
					sources: [
						{
							src: path,
							type: "video/mp4",
						},
					],
				};
			} else if (type === 4) {
				return {
					autoplay: false,
					controls: false,
					muted: true,
					loop: true,
					fill: true,
					sources: [
						{
							src: path,
							type: "video/mp4",
						},
					],
					poster: poster,
				};
			}
		},
	},
	computed: {
		loading() {
			return this.needLoading ? this.$store.state.canvasLoading : false;
		},
		lang() {
			return this.$store.getters.lang.quote || {};
		},
		isActive() {
			if (this.inputVal.length > 0) {
				return this.inputVal.some((item) => {
					return item.id == this.bindValue.id;
				});
			}
		},
		isActiveChild() {
			if (this.inputVal.length > 0 && this.inputVal[0].childList.length) {
				return this.inputVal[0].childList.some((item) => {
					return item.id == this.bindValue.id;
				});
			}
		},
		inputVal() {
			return this.selectedData[this.bindName] || [];
		},
		device() {
			return this.$store.state.device;
		},
	},
	mounted() {
		// if (this.hasChildList) {
		//   let arr = JSON.parse(JSON.stringify(this.bindValue.childList[0]));
		//   for (var key in arr) {
		//     if (this.activeData.hasOwnProperty(key)) {
		//       this.activeData[key] = arr[key];
		//     }
		//   }
		// }
	},
	created() {},
};
</script>

<style scoped lang="scss">
.StepBox {
	::v-deep .el-image__inner {
		vertical-align: middle;
		border-radius: 10px 10px 0 0;
	}
	$bg: #afb1b3;
	$bgc: white;
	$bgc2: $color-primary;
	cursor: pointer;
	display: flex;
	flex-direction: column;
	align-items: center;
	position: relative;
	@media screen and (max-width: 767px) {
		padding: 0 0 5.5px 0;
		overflow: hidden;
	}
	.freeTitle {
		position: absolute;
		left: -2px;
		top: -1px;
		z-index: 2;
		width: 25% !important;
		@media screen and (min-width: 767px) and (max-width: 1500px) {
			width: 20% !important;
		}
	}
	.noShow {
		overflow: hidden;
		width: 0;
		height: 0;
		position: absolute;
		opacity: 0;
	}
	.item {
		overflow: hidden;
		border-radius: 10px;
		padding: 0 0 10px 0;
		box-sizing: border-box;
		transition: all 0.2s;
		width: 100%;
		height: 100%;
		display: block;
		border: 1px solid #e6e6e6;
		margin-bottom: 0;

		@media (any-hover: hover) {
			&:hover {
				border-color: $color-primary !important;
				// box-shadow: 0 3px 4px 0 #ccc;
			}
		}
		@media screen and (max-width: 1921px) and (min-width: 1919px) {
		}

		@media screen and (max-width: 500px) {
			padding: 5px 0;
		}
		@media screen and (max-width: 767px) {
			border: 1px solid transparent;
			padding: 5px;
		}

		position: relative;

		> div {
			> .el-image img {
				// height: 150px;
				width: 100%;
				height: 100%;
				object-fit: contain;
			}
		}
	}
	.tooltip {
		position: absolute;
		top: 10px;
		right: 10px;
		z-index: 2;
		svg {
			fill: $color-primary;
		}
	}

	.number-input {
		margin-top: 0;
		width: 140px;
		// height: 34px;
		background: #ffffff;
		border-radius: 4px;
		@media screen and (max-width: 767px) {
			// height: 25px;
		}
		::v-deep .el-input-number.is-controls-right .el-input-number__decrease {
			// line-height: 15px;
			// bottom: -1px;
		}
		::v-deep .el-input-number__increase {
			// top: 3px;
			width: 24px;
			// line-height: 16px;
		}
		::v-deep .el-input-number__decrease {
			// bottom: -1px;
			width: 24px;
			// line-height: 15px;
		}
		::v-deep .el-input__inner {
			// height: 34px;
			// line-height: 34px;
			@media screen and (max-width: 767px) {
				// height: 25px;
				// line-height: 25px;
				text-align: center;
			}
		}
	}
	.se {
		cursor: pointer;
		width: 100%;
		height: 100%;
		display: flex;
		flex-direction: column;
		align-items: center;
		position: relative;
		// padding: 10px 13px;
		// @media screen and (max-width: 767px) {
		//   padding: 0;
		// }
		.zoomIcon {
			position: absolute;
			top: 10px;
			right: 10px;
			color: #ffffff;
			font-size: 20px;
			z-index: 2;
			transition: font-size 0.2s;
			&:hover {
				font-size: 24px;
				color: $color-primary;
			}
		}
		.swiper {
			position: relative;
			width: 100%;
		}
		.product-info {
			display: flex;
			align-items: center;
			justify-content: center;
			margin-top: 10px;
			@media screen and (max-width: 767px) {
				flex-direction: column;
				width: 100%;
			}
			.radio-beauty {
				width: 18px;
				height: 18px;
				min-width: 18px;
				box-sizing: border-box;
				display: inline-block;
				border: 1px solid $bg;
				vertical-align: middle;
				margin: 0 6px 0 3px;
				border-radius: 50%;
				background-color: $bgc;
				background-clip: content-box;
				position: relative;
				cursor: pointer;
				&::after {
					content: "";
					position: absolute;
					border-radius: 50%;
					left: 50%;
					top: 50%;
					transform: translate(-50%, -50%);
					width: 6px;
					height: 6px;
					background-color: $bg;
				}
				@media screen and (max-width: 767px) {
					margin: 0;
					width: 14px;
					height: 14px;
					min-width: 14px;
					margin-bottom: 5px;
					&::after {
						width: 5px;
						height: 5px;
					}
				}
			}
			.bTitle {
				white-space: nowrap;
				@media screen and (max-width: 767px) {
					white-space: normal;
					text-align: center;
				}
			}
			.type2-price {
				display: flex;
				flex-direction: column;
			}
		}
		.product-price {
			font-size: 16px;
			font-weight: 400;
			color: #333333;
			text-align: center;
			@media screen and (max-width: 767px) {
				margin-bottom: 0;
				font-size: 12px;
			}
			&.free {
				color: #de3500;
			}
		}
	}
	&.custom-shadow {
		.item .flex .se .swiper{
			&::before,
			&::after {
				content: "";
				position: absolute;
				z-index: -1;
				bottom: 14px;
				left: 0px;
				width: 50%;
				height: 20%;
				box-shadow: 0 14px 7px #d9dbdd;
				transform: rotate(-3deg);
			}

			&::after {
				right: 0;
				left: auto;
				transform: rotate(3deg);
			}
		}
	}
	.item {
		&.innerBorder{
			border-radius: 0;
			border: none;
			.flex{
				.se{
					.swiper{
						overflow: inherit;
						div {
							border: 1px solid #e6e6e6;
							width: 100%;
							background: #ffffff;
							border-radius: 5px;
							overflow: auto;
						}
					}
				}
			}
		}
	}
	&.active {
		.product-info .radio-beauty {
			background-color: $color-primary;
			border-color: $color-primary;
			&::after {
				background-color: $bgc;
			}
		}
		.product-info .bTitle {
			color: $color-primary;
		}
		.item {
			border-color: $color-primary !important;
			box-shadow: 0 3px 4px 0 #ccc;
			@media screen and (max-width: 767px) {
				box-shadow: none;
			}
			&.innerBorder{
				border-color: transparent !important;
				box-shadow: none;
				.flex .se .swiper div{
					border-color: $color-primary !important;
				}
			}

		}
	}
}
</style>
