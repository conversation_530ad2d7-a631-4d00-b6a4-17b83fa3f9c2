<template>
	<div v-loading="baseModel.loading" class="quoteFdSoftEnamelLapelpin">
		<!--导航栏-->
		<div class="navBanner">
			<div class="leftModel" :alt="navBannerModel.leftModel.alt" :title="navBannerModel.leftModel.title">Customize</div>
			<div class="centerModel">
				<LyStepBar id="lyStepBar" :model="navBannerModel.centerModel" />
			</div>
			<div class="rightModel" :alt="navBannerModel.rightModel.alt" :title="navBannerModel.rightModel.title">Create your sales sheet</div>
		</div>

		<!--标题-->
		<div class="navTitle">
			<div class="on">Customize</div>
			-
			<div class="off">{{ summaryModel.desc }}</div>
		</div>

		<div class="main">
			<div v-for="(item, index) in baseModel.modelList" :key="index" :id="item.paramName" class="listView">
				<!--左右子步骤布局  Pin Size-->
				<div v-if="leftRightLayout(item)" class="list">
					<div class="title">
						<b class="iconfont icon-buzhou" />
						<div class="on">{{ lang.step }} {{ item.stepIndex }}：</div>
						{{ lang.Select }} {{ item.alias ? item.alias : item.paramName }}
					</div>
					<div class="main mainL lyCardListTabs" :class="isBracelet ? 'mainLBracelet' : ''">
						<div class="titleFont1">{{ item.stepIndex }}-{{ getTitleOne(0, item.childList) }}</div>
						<div class="titleFont2" style="opacity: 0"><!-- {{ lang.fd.patches.stepSizeTitle4 }} -->&nbsp;</div>
						<div v-for="(j, jIndex) in item.childList" :key="jIndex">
							<div v-if="j.paramName == 'Pin Size' || j.paramName == 'Size'">
								<LyCardListTabs v-if="!isImg(j)" :model="getCardModel(j)" @change="cardListTabsChange" />
							</div>
							<div v-if="j.paramName == 'Pin Shape Size' || j.paramName == 'Shape Size'">
								<LyTableList ref="step1LyTableList" v-if="isImg(j)" :domId="'lyTableListLeftRight' + index + jIndex" :model="getLyTableList(item.imageJsonArray)" @change="tablieListStep1" style="overflow: auto" />
							</div>
						</div>
						<div class="titleFont3" v-if="false">
							{{ lang.fd.patches.stepSizeDesc2 }}
							<div class="on" @click="inquiryFunc()">{{ lang.fd.patches.stepSizeDesc3 }}</div>
							{{ lang.fd.patches.stepSizeDesc4 }}
						</div>
						<!-- 			<div class="titleFont3 titleFont3Not">{{ lang.fd.patches.stepSizeDesc1 }}</div> -->
					</div>
					<div class="main mainR lyFdTable" :class="isBracelet ? 'mainRBracelet' : ''">
						<div class="titleFont1 titleFont1Right">{{ item.stepIndex }}-{{ getTitleOne(1, item.childList) }}</div>
						<div class="titleFont2" style="height: 20px; margin-bottom: 20px">
							<span v-if="summaryModel.moneyModel.quantityColor" style="color: red">{{ lang.fd.patches.stepSizeDesc1 }}</span>
							<span v-if="!summaryModel.moneyModel.quantityColor">{{ lang.fd.patches.stepSizeDesc1 }}</span>
						</div>
						<div style="width: 98%">
							<LyFdTable ref="lyFdTable" :model="quantityModel" @change="fdTableChange" @input="fdTableInput" />
						</div>
					</div>
				</div>
				<!--上下布局-->
				<div v-if="!leftRightLayout(item)" class="list">
					<div class="title">
						<b class="iconfont icon-buzhou" />
						<div class="on">{{ lang.step }} {{ item.stepIndex }}：</div>
						<!-- {{ lang.Select }} -->
						{{ item.alias ? item.alias : item.paramName }}
					</div>
					<div class="main lyTableList">
						<!-- 	<div v-if="item.stepIndex == 2" class="titleFont2 titleFont1Right">{{ lang.fd.stepTitleDesc2 }}</div>
						<div v-if="item.stepIndex == 3" class="titleFont2 titleFont1Right">{{ lang.fd.stepTitleDesc3 }}</div>
						<div v-if="item.stepIndex == 5" class="titleFont2 titleFont1Right">{{ lang.fd.stepTitleDesc5 }}</div> -->
						<div v-if="baseModel.loadingOk">
							<!--核心代码-->
							<LyTableList :domId="'lyTableList' + index" :model="tableListModel.tableList[index]" @change="tablieList" @input="tableListInput" />
						</div>
					</div>
				</div>
			</div>
			<div class="mainRight">
				<div class="fixed">
					<!--悬浮框-->
					<div class="summary summaryFloat">
						<div class="title">Product Summary</div>
						<div class="imgDiv">
							<div class="top">
								<img :src="summaryModel.img" />
							</div>
							<div class="font">{{ summaryModel.desc }}</div>
						</div>
						<div class="rowOne">Customize:</div>
						<div v-for="(item, index) in summaryModel.list" :key="index" class="rowTwo" v-if="item.value">
							<div class="left">{{ item.label || "" }}</div>
							<div class="left right">
								<div class="rP1">
									{{ item.value }}
									<b class="iconfont icon-a-WebsiteEditor" @click="summaryIcon(item)" />
								</div>
								<div class="rP2">
									<div v-if="item.object?.inputModel?.value" class="l">{{ item.object?.inputModel?.value }} *</div>
									<div class="r">
										<span v-if="item.object?.priceInfo.isQur">QUR</span>
										<PriceText v-if="!item.object?.priceInfo.isQur" :customizeModel="customizeModel" :paramData="item.object" :css="baseModel.priceTextCss || {}" :isFd="true" />
									</div>
								</div>
							</div>
						</div>
						<div style="height: 10px" />
						<div class="form">
							<div class="left">
								<div class="lTitle">Currency:</div>
								<el-select v-model="summaryModel.moneyModel.currency" :placeholder="lang.fd.patches.stepSizeTitle4" style="width: 100px" @change="currencyChange">
									<el-option v-for="item in selectModel.currencyOptions" :key="item.id" :label="item.code" :value="item.code"> </el-option>
								</el-select>
							</div>
							<div class="right">
								<div class="li">
									<div class="fontTitle">Quantity:</div>
									<div class="fontPrice">{{ summaryModel.moneyModel.quantity }} pc</div>
								</div>
								<div class="li">
									<div class="fontTitle">Unit Price:</div>
									<div class="fontPrice">
										<CCYRate :price="summaryModel.moneyModel?.unitPriceTotal || 0.0" />
										each ({{ summaryModel.moneyModel.fdUnitPriceDiscountCode || "?" }})
									</div>
								</div>
								<div class="li">
									<div class="fontTitle">Mold Fee:</div>
									<div class="fontPrice">
										<CCYRate :price="summaryModel.moneyModel.moldFee || 0.0" />
										<span v-if="summaryModel.moneyModel.moldFeeUnit">({{ summaryModel.moneyModel.moldFeeUnit }})</span>
									</div>
								</div>

								<div class="li">
									<div class="fontTitle" style="flex: 6">Set Up Fee:</div>
									<div class="fontPrice">
										<CCYRate :price="summaryModel.moneyModel.fdSetupFee || 0.0" />
										<span v-if="summaryModel.moneyModel.fdSetupFeeDiscountCode">({{ summaryModel.moneyModel.fdSetupFeeDiscountCode }})</span>
									</div>
								</div>

								<div class="li">
									<div class="fontTitle">Subtotal:</div>
									<div class="fontPrice red">
										<CCYRate :price="summaryModel.moneyModel.subtotal || 0.0" />
									</div>
								</div>
							</div>
						</div>

						<div class="button" @click="save()">
							Create a Sales Sheet
							<el-tooltip :content="lang.fd.btn.mark" placement="bottom" effect="light">
								<b class="iconfont el-tooltip icon-wenhao1 wenhao" />
							</el-tooltip>
						</div>
					</div>
				</div>
			</div>
		</div>
		<!--计算框-->
		<div class="summary">
			<div class="title">Product Summary</div>
			<div class="imgDiv">
				<div class="top">
					<img :src="summaryModel.img" />
				</div>
				<div class="font">{{ summaryModel.desc }}</div>
			</div>
			<div class="rowOne">Customize:</div>
			<div v-for="(item, index) in summaryModel.list" :key="index" class="rowTwo" v-if="item.value">
				<div class="left">{{ item.label || "" }}</div>
				<div class="left right">
					<div class="rP1">
						{{ item.value }}
						<b class="iconfont icon-a-WebsiteEditor" @click="summaryIcon(item)" />
					</div>
					<div class="rP2">
						<div v-if="item.object?.inputModel?.value" class="l">{{ item.object?.inputModel?.value }} *</div>
						<div class="r">
							<span v-if="item.object?.priceInfo.isQur">QUR</span>
							<PriceText v-if="!item.object?.priceInfo.isQur" :customizeModel="customizeModel" :paramData="item.object" :css="baseModel.priceTextCss || {}" :isFd="true" />
						</div>
					</div>
				</div>
			</div>
			<div style="height: 10px" />
			<div class="form">
				<div class="left">
					<div class="lTitle">Currency:</div>
					<el-select v-model="summaryModel.moneyModel.currency" :placeholder="lang.fd.patches.stepSizeTitle4" style="width: 100px" @change="currencyChange">
						<el-option v-for="item in selectModel.currencyOptions" :key="item.id" :label="item.code" :value="item.code"> </el-option>
					</el-select>
				</div>

				<div class="right">
					<div class="li">
						<div class="fontTitle">Quantity:</div>
						<div class="fontPrice">{{ summaryModel.moneyModel.quantity }} pc</div>
					</div>
					<div class="li">
						<div class="fontTitle">Unit Price:</div>
						<div class="fontPrice">
							<CCYRate :price="summaryModel.moneyModel?.unitPriceTotal || 0.0" />
							each ({{ summaryModel.moneyModel.fdUnitPriceDiscountCode || "?" }})
						</div>
					</div>
					<div class="li">
						<div class="fontTitle">Mold Fee:</div>
						<div class="fontPrice">
							<CCYRate :price="summaryModel.moneyModel.moldFee || 0.0" />
							<span v-if="summaryModel.moneyModel.moldFeeUnit"> ({{ summaryModel.moneyModel.moldFeeUnit }}) </span>
						</div>
					</div>
					<div class="li">
						<div class="fontTitle">Set Up Fee:</div>
						<div class="fontPrice">
							<CCYRate :price="summaryModel.moneyModel.fdSetupFee || 0.0" />
							<span v-if="summaryModel.moneyModel.fdSetupFeeDiscountCode">({{ summaryModel.moneyModel.fdSetupFeeDiscountCode }})</span>
						</div>
					</div>
					<div class="li">
						<div class="fontTitle">Subtotal:</div>
						<div class="fontPrice red">
							<CCYRate :price="summaryModel.moneyModel.subtotal || 0.0" />
						</div>
					</div>
				</div>
			</div>

			<div class="button" @click="save()">
				Create a Sales Sheet
				<el-tooltip :content="lang.fd.btn.mark" placement="bottom" effect="light">
					<b class="iconfont el-tooltip icon-wenhao1 wenhao" />
				</el-tooltip>
			</div>
		</div>

		<!--遮罩层-->
		<myMask class="myMaskMedals" :maskName.sync="baseModel.maskName" />

		<!--仪表盘弹框-->
		<TopDialog class="topDialog1" v-model="inquiryModel.showInquiry" :model="false" :width="!isMobile ? '410px' : '90%'" margin="20px 0">
			<pinsInquiryBox ref="pinsInquiryBox" :pinsForm="inquiryModel.pinsForm" :titlePlaceholder="lang.fdPins.describe" :commentPlaceholder="lang.fdPins.provideAssistance" @cancelForm="cancelForm" @submitForm="submitForm">
				{{ layoutLang.contactUs }}
			</pinsInquiryBox>
		</TopDialog>

		<!-- <div v-if="!isMobile" class="sticky" @click="sticky">
			<b class="iconfont icon-zuojiantou1" />
			TOP OF PAGE
		</div> -->
	</div>
</template>
<script>
import "@/plugins/element";

import LyStepBar from "@/components/Quote/Ly/LyStepBar";
import LyCardListTabs from "@/components/Quote/Ly/LyCardListTabs";
import LyFdTable from "@/components/Quote/Ly/LyFdTable";
import LyTableList from "@/components/Quote/Ly/LyTableList";
import { DataProcessing } from "@/utils/dataProcessing";
import { navBannerModel, cardModel, quantityModel, tableListModel, selectModel, summaryModel, summaryImgModel, baseModel, inquiryModel, summaryModelOld, cacheModel, customizeModel } from "./entity/_fdLapelpin";
import { quoteCalculateCalculateAll, quoteCalculateCalculate, quoteCalculateCalculateQty, quoteCalculateGetChildCateList, fdEditInquiry } from "@/api/quote/fdCommon";
import { getCateParamRelationByCateId, getCurrency } from "@/api/pins";
import { scrollToViewTop } from "@/utils/utils";
import myMask from "@/components/Quote/Mask.vue";
import PriceText from "@/components/Quote/PriceText";
import { calculateFunc } from "@/utils/system";

import pinsInquiryBox from "@/components/lapelPins/pinsInquiryBox";
import TopDialog from "@/components/Quote/TopDialog";
import { getPriceData } from "@/api/pins";
import CCYRate from "@/components/CCYRate";
export default {
	head() {
		return {
			title: "fd-soft-enamel-lapelpin",
		};
	},
	components: { LyStepBar, LyCardListTabs, LyFdTable, LyTableList, myMask, PriceText, pinsInquiryBox, TopDialog, CCYRate },
	mixins: [],
	data() {
		return {
			//导航
			navBannerModel: new navBannerModel(),
			//卡片
			cardModel: new cardModel(),
			//带图文的卡片
			summaryImgModel: new summaryImgModel(),

			//数量表格
			quantityModel: new quantityModel(),
			//图文列表
			tableListModel: new tableListModel(),
			//产品目录
			summaryModel: new summaryModel(),
			summaryModelOld: new summaryModelOld(),
			//价格组件
			customizeModel: new customizeModel(),
			selectModel: new selectModel(),
			baseModel: new baseModel(),
			inquiryModel: new inquiryModel(),
			//缓存数据
			cacheModel: new cacheModel(),
			isBracelet: false, //是否是手环报价    默认false  true||是手环 样式发生变化
		};
	},
	watch: {
		showInquiry: function (val) {
			if (val) {
				if (this.userInfo && this.userInfo.telephone) {
					let telephone, areaCode;
					if (this.userInfo.telephone) {
						if (this.userInfo.telephone.includes("-")) {
							telephone = this.userInfo.telephone.split("-")[1];
							areaCode = this.userInfo.telephone.split("-")[0];
						} else {
							telephone = this.userInfo.telephone;
							areaCode = this.areaCodes;
						}
					}
					this.pinsForm = {
						email: this.userInfo.email,
						firstName: this.userInfo.firstName,
						lastName: this.userInfo.lastName,
						areaCode: this.userInfo.telephone ? areaCode : "",
						telephone: this.userInfo.telephone ? telephone : "",
					};
				} else {
					this.pinsForm.areaCode = this.areaCodes;
				}
			}
		},
		"$store.state.currency": {
			handler(newValue) {
				this.summaryModel.moneyModel.currency = newValue.code;
				this.currencyChange(newValue.code);
			},
			immediate: true,
		},
	},
	computed: {
		userInfo() {
			return this.$store.state.userInfo;
		},
		//获取区号
		areaCodes() {
			return this.$store.state.areaCode;
		},
		lang() {
			return this.$store.getters.lang.quote || {};
		},
		layoutLang() {
			return this.$store.getters.lang.layout || {};
		},
		isMobile() {
			return this.$store.getters.isMobile;
		},
		//获取国家语言
		currency() {
			return this.$store.state.currency;
		},
	},
	created() {
		/* setInterval(() => {
			console.log("亲爱的", this.tableListModel.tableList);
		}, 3000); */
		this.getPriceData(); //仪表盘弹框
		this.init();
		this.initCss();
	},
	methods: {
		//初始化样式
		initCss() {
			if (this.$route.path.indexOf("printed-silicone-wristbands") > -1) {
				this.isBracelet = true;
			} else {
				this.isBracelet = false;
			}
		},
		//价格分层参数
		getPriceData() {
			getPriceData({ buyType: 7, productCateId: this.baseModel.base.pid }).then((res) => {
				this.$store.commit("setMorePriceData", res.data);
			});
		},
		init() {
			let { cateId, cateChildId, alert } = this.$route.query;
			this.baseModel.windowParent = this.$route.query;
			this.baseModel.base.pid = cateId;
			this.baseModel.base.cateId = cateChildId;
			this.baseModel.base.alert = alert;

			this.getCateParamRelationByCateIdFunc();

			//获取国家USD
			this.getCurrencyFunc();
			//this.cardListTabsChange(); //获取数量

			if (cateId && cateChildId) {
				//获取报价分类
				this.quoteCalculateGetChildCateListFunc();
			} else {
				console.log("错误", "cateId和cateChildId没有传");
			}
		},
		cancelForm() {
			for (let key in this.inquiryModel.pinsForm) {
				if (this.inquiryModel.pinsForm.hasOwnProperty(key)) {
					let element = this.pinsForm[key];
					if (element === null || element === undefined) {
						continue;
					}
					if (Array.isArray(element)) {
						element.forEach((item) => {
							if (item && typeof item === "object") {
								item.value = "";
							}
						});
					} else {
						if (this.userInfo.telephone && (key == "areaCode" || key == "telephone")) continue;
						this.inquiryModel.pinsForm[key] = "";
					}
				}
			}
		},
		submitForm() {
			let obj = this.inquiryModel.pinsForm.groupNumber.reduce((pre, next, index) => {
				if (next.value) {
					pre[next.name] = next.value;
				}
				return pre;
			}, {});

			this.inquiryModel.pinsForm.socialCode = JSON.stringify(obj);
			this.inquiryModel.pinsForm.quoteParam = JSON.stringify({ fontData: { fontImgCustom: [], comments: `FDCate:${this.cateName},UserComments:${this.inquiryModel.pinsForm.remark} }` } });
			//到时候正式服 要去掉proId:9
			fdEditInquiry(Object.assign(this.inquiryModel.pinsForm, { quoteCateld: +this.baseModel.windowParent.cateId, quoteCateChildld: +this.baseModel.windowParent.cateChildId, buyType: 7, productsName: "fd", isMobile: this.isMobile ? 1 : 0 }))
				.then((res) => {
					this.inquiryModel.showInquiry = false;
					this.$toast.success("add inquiry success");
				})
				.finally(() => {
					this.inquiryModel.showInquiry = false;
				});
		},
		//获取国家
		getCurrencyFunc() {
			getCurrency({}).then((res) => {
				if (res) {
					this.selectModel.currencyOptions = res.data || [];
					this.currencyChange(this.summaryModel.moneyModel.currency);
				}
			});
		},
		getCateParamRelationByCateIdFunc() {
			let m = {
				...this.baseModel.base,
			};
			getCateParamRelationByCateId(m).then((res) => {
				if (res) {
					res = this.dataHandleOne(res.data);
					res = res.sort(function (a, b) {
						return a.stepIndex - b.stepIndex;
					});
					console.log("ass'==>", res);
					res = this.dataHandleOnes(res);

					this.baseModel.modelList = res || [];

					this.baseModel.loadingOk = true;
					this.$forceUpdate();
				}
			});
		},
		//获取报价分类
		quoteCalculateGetChildCateListFunc() {
			let m = {
				cateId: this.baseModel.windowParent.cateId,
			};
			quoteCalculateGetChildCateList(m).then((res) => {
				console.log("鬼子进村", res);
				if (res) {
					//匹配数据
					if (Array.isArray(res.data?.childList)) {
						res.data.childList.map((item) => {
							if (item.id == this.baseModel.windowParent.cateChildId) {
								console.log("鬼子来了", item);
								this.summaryModel.desc = item.cateName;
								let j = JSON.parse(item.imageJson);
								if (j?.length > 0) {
									this.summaryModel.img = j[0].url;
								}

								this.baseModel.data.prodTime = item.prodTime;
								this.baseModel.data.rushTime = item.rushTime;
							}
						});
					}
				}
			});
		},
		//报价计算All
		quoteCalculateCalculateAllFunc(f) {
			let model = {
				...this.baseModel.base,
				sizeId: this.baseModel.sizeSelectedModel.priceInfo.id,
			};
			quoteCalculateCalculateAll(model).then((res) => {
				if (res) {
					this.quantityModel.list = res.data;
					this.baseModel.quantityModel = this.quantityModel;
					if (f) {
						//同步数量组件
						if (this.$refs.lyFdTable?.length > 0) {
							this.$refs.lyFdTable[0].setRowOne();
						}

						//同步价格
						if (res?.list?.length > 0) {
							this.quoteCalculateCalculateFunc(item, (res) => {
								this.valSummaryModel(res?.list[0]);
							});
						}

						f(res);
					}
				}
			});
		},
		//报价计算
		quoteCalculateCalculateFunc(e, f) {
			if (e.totalQuantity == null || e.totalQuantity == "") {
				return;
			}
			if (this.summaryModel?.list) {
				let copyObj = this.summaryModel.list[0].object;
				this.summaryModel.list[0].object = {
					...copyObj,
					inputNum: this.summaryModel.moneyModel.quantity,
				};
			}
			//summaryModel.list
			let model = {
				...this.baseModel.base,
				quantity: e.totalQuantity || 0,
				sizeId: this.baseModel.sizeSelectedModel.priceInfo.id,
			};
			//请求参数
			let m = {
				...new baseModel().calculateModelNew,
				...model,
			};
			//赋值全局变量
			this.baseModel.calculateModel = {
				...this.baseModel.calculateModel,
				...model,
			};

			this.summaryModel.list[0].object.inputNum = m.quantity;
			this.$forceUpdate();

			//刷新数量表格价格
			quoteCalculateCalculateQty(m).then((res) => {
				if (res) {
					//168提示
					if (res.data.isLowestPrice == 1) {
						let sum = res.data.totalPrice || 168;
						let num = DataProcessing.floatRide(sum, this.baseModel.rate);
						num = parseFloat(num).toFixed(2);
						this.$toast.error("The total list price for the minimum order amount is " + this.baseModel.unit + num + " " + this.baseModel.code + ".");
					}
					if (this.baseModel.calculateModel.paramIdList.length == 0 && this.baseModel.calculateModel.upgradesQtyDTO.length == 0) {
						this.quantityModel.initModel = res.data;
					}
					//如果是输入框则回显
					if (e.isInput) {
						if (this.$refs.lyFdTable?.length > 0) {
							this.quantityModel.initModel = res.data;
							this.$refs.lyFdTable[0].init();
						}
					}
					//提交时.带参数的价格
					/* if (this.baseModel.model) {
						this.baseModel.model.quantity = m.quantity;
						this.baseModel.model.sizeId = m.sizeId;
						quoteCalculateCalculate(this.baseModel.model).then((res) => {
							if (res) {
								if (this.baseModel.calculateModel.paramIdList.length == 0 && this.baseModel.calculateModel.upgradesQtyDTO.length == 0) {
									//this.quantityModel.initModel = res.data;
									//summaryModel.list
									this.summaryBind(res);
								}
								if (f) {
									f(res);
								}
							}
						});
					} */
					this.systemCalculate({});
					this.$store.commit("quoteQuantity", this.baseModel.calculateModel.quantity);
				}
			});
		},
		//计算框渲染  res||接口返回对象   state||状态  readOnly||只读不修改
		summaryBind(res, state) {
			let item = res.data;
			item.totalPrice = parseFloat(item.totalPrice).toFixed(2);

			this.summaryModel.moneyModel = {
				currency: this.summaryModel.moneyModel.currency, //国家下拉
				quantity: item.totalQuantity, //数量
				unitPrice: item.unitPrice, //单价
				unitPriceTotal: item.foundationUnitPrice || 0, //单价总数
				//foundationUnitPrice: item.foundationUnitPrice,
				moldFee: item.fdToolingCharge, //磨具费
				moldFeeUnit: item.fdToolingChargeDiscountCode, //磨具费单位
				fdUnitPriceDiscountCode: item.fdUnitPriceDiscountCode, //unitPrice单位
				fdSetupFee: item.fdSetupFee, //set fee
				fdSetupFeeDiscountCode: item.fdSetupFeeDiscountCode, //set fee单位
				subtotal: item.totalPrice, //总价
				paramIdMapping: item.paramIdMapping,
				increaseMap: item.increaseMap,
				setupFeeMap: item.setupFeeMap,
				packingIdListJson: item.packingIdListJson || [],
			};
			//支持三级
			this.summaryModel.moneyModel.paramIdMapping.map((n) => {
				if (n.parentObject.id == null) {
					this.summaryModel.list.map((m) => {
						if (n.parentId === m.object.parentId) {
							n.parentObject = m.parentObject;
							n.stepName = m.parentObject.alias;
						}
					});
				}
			});
			console.log("超级赛亚人", this.summaryModel);
			//支持三级end
			this.summaryModelOld = JSON.parse(JSON.stringify(this.summaryModel)); //解决数据污染
		},

		//第一步数据处理
		dataHandleOne(res) {
			res.map((i) => {
				if (i.fdIncreasePriceDiscountCode) {
					i.fdIncreasePriceDiscountCode = JSON.parse(i.fdIncreasePriceDiscountCode);
					i.neonSampleData = JSON.parse(i.neonSampleData);
				}
				if (i.childList) {
					i.childList.map((j) => {
						if (j.imageJson) {
							j.imageJson = JSON.parse(j.imageJson);
						}

						if (j.neonSampleData) {
							j.neonSampleData = JSON.parse(j.neonSampleData);
						}
					});
				}
			});
			//图文拼接非第一步
			res.map((i) => {
				let objArr = [];
				i.childList.map((j) => {
					if (j.imageJson && j.imageJson != "") {
						j.imageJson.map((z) => {
							objArr.push({
								...j,
								img: z.url,
								hideFree: true,
							});
						});
					} else {
						let path = j?.priceInfo?.imagePath;
						if (path) {
							let imagePath = JSON.parse(path);
							if (Array.isArray(imagePath)) {
								if (imagePath && imagePath.length) {
									objArr.push({
										...j,
										img: imagePath[0].path,
										hideFree: true,
									});
								} else {
									objArr.push({
										...j,
										img: this.baseModel.defaultImg,
										hideFree: true,
									});
								}
							}
						} else {
							objArr.push({
								...j,
								img: this.baseModel.defaultImg,
								hideFree: true,
							});
						}
					}
				});
				this.summaryImgModel = new summaryImgModel();
				this.summaryImgModel.list = objArr;
				i.imageJsonArray = this.summaryImgModel;
			});
			return res;
		},
		//大于第一步数据处理
		dataHandleOnes(res) {
			let tableList = new tableListModel().tableList;

			res.map((i, index) => {
				let oArr = [];
				i.imageJsonArray.list.map((j) => {
					j.inputNum = undefined;
					j.childList.map((z) => {
						z.inputNum = undefined;
					});

					let obj = {
						...j,
						img: j.img,
						hideFree: false,
					};
					//QUANTITY才有输入框 QUANTITY
					if (j.paramType == "QUANTITY") {
						obj.inputModel = {
							placeholder: "Enter " + j.paramName + " Quantity",
							type: "number",
							value: undefined,
						};
					}
					oArr.push(obj);
				});
				if (this.tableListModel.tableList.length <= index) {
					return;
				}
				this.tableListModel.tableList[index].list = oArr;

				this.tableListModel.tableList[index].title = i.alias;
				this.tableListModel.tableList[index].desc = i.paramName;
				if (i.paramName === "Select Pin Packaging") {
					this.tableListModel.tableList[index].css.rowColumns = 5;
					this.tableListModel.tableList[index].mobileCss.rowColumns = 2;
				} else if (i.paramName === "Select Pin BackStamp") {
					this.tableListModel.tableList[index].css.rowColumns = 4;
					this.tableListModel.tableList[index].mobileCss.rowColumns = 2;
				} else if (i.paramName === "Select Pin Upgrades") {
					this.tableListModel.tableList[index].css.rowColumns = 4;
					this.tableListModel.tableList[index].mobileCss.rowColumns = 2;
				} else if (i.paramName === "Select Pin Backing Attachments") {
					this.tableListModel.tableList[index].css.rowColumns = 6;
					this.tableListModel.tableList[index].mobileCss.rowColumns = 3;
				} else if (i.paramName === "Select Amount of Colors") {
					this.tableListModel.tableList[index].css.rowColumns = 5;
					this.tableListModel.tableList[index].mobileCss.rowColumns = 3;
				} else if (i.paramName === "Pin Plating / Finish Options") {
					this.tableListModel.tableList[index].css.rowColumns = 5;
					this.tableListModel.tableList[index].mobileCss.rowColumns = 3;
				} else if (i.paramName === "Select Attachment") {
					this.tableListModel.tableList[index].css.rowColumns = 2;
					this.tableListModel.tableList[index].mobileCss.rowColumns = 2;
					this.tableListModel.tableList[index].css.topImgImgStyle = {
						border: "1px solid #eaeaea",
						borderRadius: "6px",
						background: "#f5f6f7",
					};
					this.tableListModel.tableList[index].isCarousel = false;
				} else {
					this.tableListModel.tableList[index].css.rowColumns = 5;
					this.tableListModel.tableList[index].mobileCss.rowColumns = 3;
				}

				this.baseModel.maskNameKeyArr.push(i.paramName);
			});
			return res;
		},
		//卡片选中回调
		cardListTabsChange(item, index) {
			this.baseModel.sizeSelectedModel = item;
			if (this.baseModel.sizeSelectedIndex != index) {
				this.baseModel.sizeSelectedIndex = index;
				this.$store.commit("setQuoteSizeName", item.paramName);
				this.$store.commit("setQuoteSizeValue", item.paramCode);
				this.quoteCalculateCalculateAllFunc((res) => {
					this.$nextTick(() => {
						this.summaryModel.list[0] = {
							label: this.summaryModel.list[0].label,
							value: item.alias,
							object: item,
							parentObject: this.baseModel.modelList[0],
						};
						this.$forceUpdate();
					});
				});
			}
		},
		fdTableChange(item, index) {
			this.quoteCalculateCalculateFunc(item, (res) => {
				this.valSummaryModel(res);
			});
		},
		//给价格弹窗赋值
		valSummaryModel(res) {
			let item = res.data;

			this.summaryModel.moneyModel = {
				currency: this.summaryModel.moneyModel.currency, //国家下拉
				quantity: item.totalQuantity, //数量
				unitPrice: item.unitPrice, //单价
				unitPriceTotal: item.foundationUnitPrice || 0, //单价总数
				//	foundationUnitPrice: item.foundationUnitPrice,
				moldFee: item.fdToolingCharge, //磨具费
				moldFeeUnit: item.fdToolingChargeDiscountCode, //磨具费单位
				fdUnitPriceDiscountCode: item.fdUnitPriceDiscountCode, //unitPrice单位
				fdSetupFee: item.fdSetupFee, //set fee
				fdSetupFeeDiscountCode: item.fdSetupFeeDiscountCode, //set fee单位
				subtotal: item.totalPrice, //总价
				paramIdMapping: item.paramIdMapping,
				increaseMap: item.increaseMap,
				setupFeeMap: item.setupFeeMap,
			};

			this.summaryModelOld = JSON.parse(JSON.stringify(this.summaryModel)); //解决数据污染

			this.summaryModel.list[0].object.inputNum = this.summaryModel.moneyModel.quantity;
			this.$forceUpdate();
		},
		//计算校验 数量如果没有则 遮罩高亮数量步骤
		checkCalculate() {
			if (this.baseModel.calculateModel?.quantity) {
				return true;
			} else {
				return false;
			}
		},
		//列表输入回调  当前选中对象  选中下标  父盒子id标识
		tableListInput(item, index, domId) {
			this.$nextTick(() => {
				let i = this.tableListBind(item, index, domId);
				this.$forceUpdate();
			});
		},
		//第一步列表选中回调
		tablieListStep1(item, index, domId) {
			if (item.imageJsonJson) {
				//图片
				this.cardListTabsChange(item, index);
			} else {
				//尺寸
			}
		},
		getCust() {
			let moldArr = [];
			this.summaryModel.list.map((item) => {
				if (item.parentObject.paramName != "Size & Quantity") {
					if (item.object?.id) {
						if (item.object.priceInfo?.id) {
							console.log("怎么不对劲", item.object.priceInfo.fdMoldPrice, item.object);
							let moldPrice = item.object.priceInfo.fdMoldPrice;
							let fdEnableMoldPrice = item.object.priceInfo.fdEnableMoldPrice;
							let isQur = item.object.priceInfo.isQur;
							if (fdEnableMoldPrice == 1) {
								if (moldPrice) {
									if (isQur == 0) {
										moldArr.push(moldPrice); //不是QUR 就计算
									}
								}
							}
						}
					}
				}
			});
			let sum = this.summaryModel.moneyModel.moldFee;
			moldArr.map((item) => {
				sum = sum - item;
			});
			console.log("asd", moldArr);
			console.log("最后", sum, this.summaryModel.moneyModel.moldFee);
			this.customizeModel.moldFee = sum;
		},

		//核心计算价格方法
		systemCalculate(item = {}) {
			console.log("核动力的牛马", item);
			calculateFunc(
				this.baseModel.calculateModel,
				item,
				this.baseModel.modelList,
				(res, model) => {
					res.data.packingIdListJson = model.packingIdListJson;
					this.baseModel.model = model;
					this.summaryBind(res);
					let copy = this.quantityModel.initModel;
					this.quantityModel.initModel = {
						...res.data,
					};
					this.currencyChange(this.summaryModel.moneyModel.currency); //更新国家汇率
					this.getCust();
				},
				"fd"
			);
		},
		//列表选中回调  当前选中对象  选中下标  父盒子id标识
		tablieList(item, index, domId) {
			this.summaryModel.moneyModel.quantityColor = false;
			console.log("列表选中回调", item, index, domId);
			this.$nextTick(() => {
				if (this.checkCalculate()) {
					this.systemCalculate(item);
				} else {
					let aName = this.baseModel.maskNameKeyArr[0];
					this.startMask(aName);
					//第一步高亮
					this.summaryModel.moneyModel.quantityColor = true;
					return;
				}
				let i = this.tableListBind(item, index, domId);
				//全部取消
				this.cannerMask();
				if (i >= this.summaryModel.list.length - 1) {
					//最后一个断开
					this.baseModel.maskName = null;
					this.check();
					return;
				} else {
					//任意步骤则下钻
					if (i) {
						if (!this.summaryModel.list[i + 1].parentObject?.paramName) {
							this.startMask(this.baseModel.maskNameKeyArr[i + 1]);
						}
					}
				}
				this.summaryModelOld = JSON.parse(JSON.stringify(this.summaryModel)); //解决数据污染
				this.check();

				this.$forceUpdate();
			});
		},
		//显示仪表盘弹框
		inquiryFunc() {
			this.inquiryModel.pinsForm.isSample = 0;
			this.inquiryModel.showInquiry = true;
		},
		//回调绑定  因为步骤序号不准确 自己手动做下标
		tableListBind(item, index, domId) {
			let i = 0;
			if (domId == "lyTableList1") {
				i = 1;
			} else if (domId == "lyTableList2") {
				i = 2;
			} else if (domId == "lyTableList3") {
				i = 3;
			} else if (domId == "lyTableList4") {
				i = 4;
			} else if (domId == "lyTableList5") {
				i = 5;
			} else if (domId == "lyTableList6") {
				i = 6;
			} else if (domId == "lyTableList7") {
				i = 7;
			} else {
				return;
			}
			//summaryModel.list
			this.summaryModel.list[i] = {
				label: this.summaryModel.list[i].label,
				value: item.alias,
				object: item,
				parentObject: this.baseModel.modelList[i],
			};
			//this.summaryModel.list[i].object.value = 111;
			this.summaryModelOld = JSON.parse(JSON.stringify(this.summaryModel)); //解决数据污染
			return i;
		},

		/* 数量表格--输入回调事件 */
		fdTableInput(e) {
			e.isInput = true;

			this.quoteCalculateCalculateFunc(e, (res) => {
				this.valSummaryModel(res);
			});
		},
		//校验步骤
		save() {
			let m = JSON.stringify(this.summaryModel);
			if (this.check()) {
				if (!this.summaryModel.moneyModel.currency) {
					this.$toast.error(this.lang.fd.currencyCheck);
					return;
				}

				if (this.summaryModel.moneyModel.subtotal > 0 && this.summaryModel.moneyModel.quantity > 0 && this.summaryModel.moneyModel?.unitPriceTotal > 0) {
					this.summaryModel.moneyModel.unit = this.baseModel.unit; //单位
					this.summaryModel.moneyModel.rate = this.baseModel.rate; //倍率
					this.summaryModel.moneyModel.data = this.baseModel.data;

					let m = JSON.stringify(this.summaryModel);
					sessionStorage.setItem(this.baseModel.constantQuery, m);
					console.log("呼啦啦", this.summaryModel);
					this.jump("/quote/fd-soft-enamel-lapelpin-detail");
				} else {
					this.startMask(this.baseModel.maskNameKeyArr[0]);
				}
			}
		},
		check() {
			let op = true;
			for (let i = 0; i < this.summaryModel.list.length; i++) {
				let item = this.summaryModel.list[i];
				this.cannerMask();
				if (item.parentObject?.paramName) {
					//有值校验ok
					this.cannerMask();
				} else {
					//没值校验失败并瞄点
					let str = this.getMaskName(item);

					if (str) {
						if (this.startMask(str)) {
							op = false;
							break;
						}
					}
				}
			}
			if (!op) {
				return false;
			}
			//校验都通过后.
			let myMaskMedals = document.getElementsByClassName("myMaskMedals");
			for (let i = 0; i < myMaskMedals.length; i++) {
				myMaskMedals[i].setAttribute("style", "display:none;"); //z-index:1!important;
			}
			return true;
		},
		//获取遮罩标签
		getMaskName(item) {
			let str = "";
			if (item.label == "Plating/Finish (pc):") {
				str = this.baseModel.maskNameKeyArr[1]; //第三步
			} else if (item.label == "Amount of Colors:") {
				str = this.baseModel.maskNameKeyArr[2]; //第4步
			} else if (item.label == "Backing Attachment (pc):") {
				str = this.baseModel.maskNameKeyArr[3]; //第5步
			} else if (item.label == "Special Options:") {
				str = this.baseModel.maskNameKeyArr[4]; //第6步
			} else if (item.label == "Pin Back Stamp (pc):") {
				str = this.baseModel.maskNameKeyArr[5]; //第7步
			} else if (item.label == "Pin Packaging (pc):") {
				str = this.baseModel.maskNameKeyArr[6]; //第8步
			} else if (item.label == "Select Packaging-fd:") {
				str = this.baseModel.maskNameKeyArr[7]; //第9步
			}
			return str;
		},
		//启动遮罩  返回true||有回调执行业务    默认false||不需要回调
		startMask(str) {
			let thisListView = document.getElementById(str);
			if (thisListView) {
				thisListView.setAttribute("style", "z-index:999!important");
				scrollToViewTop(thisListView);
				this.baseModel.maskName = str; //开启遮罩
				return true;
			}
			return false;
		},
		//全部取消遮罩
		cannerMask() {
			let listView = document.getElementsByClassName("listView");
			//	myMaskMedals
			for (let i = 0; i < listView.length; i++) {
				listView[i].setAttribute("style", "z-index:1");
			}
		},
		//跳转
		jump(jString, data) {
			this.$router.push({
				path: jString,
				query: data,
			});
		},
		//左右布局
		leftRightLayout(item) {
			let arr = [
				"Size & Quantity", //卡片
				//"Select Metal Finish", //图文
			];
			if (DataProcessing.validIsArrFind(item.paramName, arr)) {
				return true;
			} else {
				return false;
			}
		},
		//卡片列表获取--数据源
		getCardModel(item) {
			//this.cardModel = new cardModel();
			this.cardModel.list = item.childList || [];
			return this.cardModel;
		},
		//是否有图片源
		isImg(item) {
			//item.paramName = Pin Size||尺寸      Pin Shape Size||图文
			if (item.paramName == "Pin Shape Size") {
				return true;
			}
			return false;
		},
		//获取标题
		getTitleOne(e, childList) {
			let str = "";
			childList.map((i) => {
				if (e == 0) {
					if (i.paramName == "Pin Shape Size" || i.paramName == "Pin Size" || i.paramName == "Size") {
						str = i.stepIndex + " Select " + i.alias; //1-1
					}
				}
				if (e == 1 && i.paramName == "qty") {
					str = (i.stepIndex || 1) + " Choose " + i.alias; //1-2
				}
			});
			return str + "：";
		},
		//国家选中下拉框
		currencyChange(e) {
			this.selectModel.currencyOptions.map((item) => {
				if (item.code == e) {
					this.summaryModel.moneyModel.unit = item.symbol;
					this.baseModel.unit = item.symbol;
					this.baseModel.rate = item.rate;
					this.baseModel.code = item.code;
					this.baseModel.currencyId = item.id;
					/* if (this.summaryModelOld.moneyModel?.unitPriceTotal) {
						this.summaryModel.moneyModel.unitPriceTotal = DataProcessing.floatRide(this.summaryModelOld.moneyModel?.unitPriceTotal || 0, item.rate);
						this.summaryModel.moneyModel.unitPriceTotal = parseFloat(this.summaryModel.moneyModel.unitPriceTotal).toFixed(2);
					} */
					/* 	if (this.summaryModelOld.moneyModel?.moldFee) {
						this.summaryModel.moneyModel.moldFee = DataProcessing.floatRide(this.summaryModelOld.moneyModel?.moldFee, item.rate);
						this.summaryModel.moneyModel.moldFee = parseFloat(this.summaryModel.moneyModel.moldFee).toFixed(2);
					} */
					/* 
					if (this.summaryModelOld.moneyModel?.subtotal) {
						this.summaryModel.moneyModel.subtotal = DataProcessing.floatRide(this.summaryModelOld.moneyModel.subtotal, item.rate);
						this.summaryModel.moneyModel.subtotal = parseFloat(this.summaryModel.moneyModel.subtotal).toFixed(2);
					} */

					/* 	if (this.summaryModelOld.moneyModel?.fdSetupFee) {
						this.summaryModel.moneyModel.fdSetupFee = DataProcessing.floatRide(this.summaryModelOld.moneyModel?.fdSetupFee, item.rate);
						this.summaryModel.moneyModel.fdSetupFee = parseFloat(this.summaryModel.moneyModel.fdSetupFee).toFixed(2);
					} */

					//	this.summaryModel.moneyModel.subtotal = parseFloat(this.summaryModel.moneyModel.subtotal).toFixed(2);

					this.quantityModel.currency = item;
					this.$forceUpdate();
				}
			});
			//国家数据与头部双向数据绑定
			let currencyList = this.$store.state.currencyList;
			currencyList.map((item) => {
				if (item.code == e) {
					this.$store.commit("setCurrency", item);
				}
			});
		},
		//国家修改图标点击
		summaryIcon(item) {
			this.$nextTick(() => {
				this.cannerMask();
				if (item.parentObject?.paramName != null) {
					//选中了则修改
					this.startMask(item.parentObject.paramName);
				} else {
					//没选中 给值
					item.parentObject.paramName = this.getMaskName(item);
				}
				this.startMask(item.parentObject.paramName);
			});
		},
		sticky() {
			this.$nextTick(() => window.scrollTo({ top: 0, behavior: "smooth" }));
		},
		//获取getQuantityModel
		getQuantityModel() {
			setTimeout(() => {
				return this.quantityModel;
			}, 500);
		},
		//拼接inputNum
		/* 	getPriceText(item) {
			item.object.inputNum = item.object?.inputModel?.value;
			console.log("我承诺先啦", item);
			//console.log(this.summaryModel.list);
			return item.object;
		}, */
		//左上角 图文拼接
		getLyTableList(obj) {
			let objArr = obj;
			obj.list.map((i) => {
				if (i.paramName == "Pin Shape Size") {
					//图文
					i.childList.map((j) => {
						//j.img =
						if (j.imageJson) {
							j.imageJsonJson = JSON.parse(j.imageJson);
							if (j.imageJsonJson?.length > 0) {
								j.img = j.imageJsonJson[0].url;
							}
						}
					});
					objArr.list = i.childList;
				}
			});
			objArr.list.map((item) => {
				item.hideFree = true;
			});
			objArr.css = this.tableListModel.tableList[0].css;
			objArr.mobileCss = this.tableListModel.tableList[0].mobileCss;
			//选中第一个并调用数量接口
			if (objArr.list?.length > 0) {
				setTimeout(() => {
					if (this.quantityModel.list == 0) {
						let i = 0;
						let item = objArr.list[i]; //选中第一个
						if (this.$refs.step1LyTableList?.length > 0) {
							this.$refs.step1LyTableList[0].tabsLi(item, i);
						} else {
							this.$refs.step1LyTableList.tabsLi(item, i);
						}
					}
				}, 500);
			}
			return objArr;
		},
	},
};
</script>

<style scoped lang="scss">
@mixin centerCenter {
	display: flex;
	justify-content: center;
	align-items: center;
}
@mixin parentDiv {
	margin: 0 40px;
}
@mixin parentMBigDiv {
	padding: 0 25px;
}
@mixin parentMDiv {
	margin: 0 10px;
}
@mixin imgCss {
	width: 100%;
	height: 100%;
}
@mixin fontBold {
	color: #333333;
	font-weight: Bold;
}
$imgServer: "https://static-oss.gs-souvenir.com/web/";

.quoteFdSoftEnamelLapelpin {
	background: #f2f5f7;
	padding-bottom: 15px;
	::v-deep .topDialog {
		.close-icon-wrapper {
			color: #8a8a8a !important;
		}
		&.topDialog1 {
			position: relative;
			z-index: 10;
			.dialogContent {
				border-radius: 8px;
			}
			.close-icon-wrapper {
				top: 10px;
				right: 10px;
			}
		}
	}

	.main {
		position: relative;
		width: calc(100% - 500px);
		.lyCardListTabs {
			::v-deep .lyTableList .list {
				height: 505px;
				@include respond-to(mb) {
					height: 520px;
				}
			}
		}

		.mainRight {
			width: 500px;
			height: 100%;
			position: absolute;
			right: -485px;
			top: 0;
			.fixed {
				position: sticky;
				top: 40px;
				border-radius: 10px;
				bottom: auto;
				font-size: 14px;
				box-shadow: -8px 0px 10px -10px rgba(0, 0, 0, 0.25);
				.summary {
					margin: 0px auto 0 auto;
					border: 0;
					padding-bottom: 10px;
					width: 100%;

					.rowTwo:hover {
						background: #fff;
					}
				}
			}
		}
	}
	.navBanner {
		height: 80px;
		background: #c6d2df;
		background: url($imgServer + "quoteManage/20240912/sales_sheet_bar_20240912dGdHzi.png");
		@include centerCenter;
		@mixin leftModel {
			display: flex;
			justify-content: flex-end;
			align-items: center;
			font-size: 15px;
			font-weight: Bold;
			padding-right: 20px;
			color: #fff;
			cursor: pointer;
		}
		.leftModel {
			width: 163px;
			height: 57px;
			@include leftModel;
			background: url($imgServer + "quoteManage/20240912/Customize_20240912SQ2WiP.png");
		}
		.rightModel {
			width: 258px;
			height: 57px;
			@include leftModel;
			background: url($imgServer + "quoteManage/20240912/2Create_your_sales_sheet_20240912ja8Nds.png");
		}
		.leftModel:hover,
		.rightModel:hover {
			//color: rgba(255, 255, 255, 0.7);
		}
	}
	.navTitle {
		@include parentDiv;
		@include centerCenter;
		font-size: 14px;
		height: 40px;
		justify-content: flex-start;

		.on {
			color: #ff6600;
			margin-right: 5px;
			font-weight: Bold;
		}
		.off {
			color: #666666;
			margin-left: 5px;
		}
	}

	.listView {
		display: flex;
		flex-wrap: wrap;
		@include parentDiv;
		background: #fff;
		border-radius: 10px;
		margin-bottom: 10px;
		position: relative;
		#lyFdTable1 {
			position: relative;
		}
		.list {
			padding: 25px 30px;
			display: flex;
			flex-wrap: wrap;
			width: 100%;
			.title {
				font-size: 19px;
				font-weight: Bold;
				color: #333;
				display: flex;
				margin-bottom: 5px;
				width: 100%;
				align-items: center;
				line-height: 1.4;
				b {
					color: $color-blue;
					border: 0;
					margin-right: 5px;
				}
				.on {
					color: $color-blue;
				}
			}
			.main {
				flex: 1;
			}
			.mainL {
				flex: 5;
				margin-right: 70px;
			}
			.mainLBracelet {
				flex: 6;
				margin-right: 50px;
			}
			.mainR {
				flex: 5;
				padding-right: 50px;
				//	margin-right: 50px;
			}
			.mainRBracelet {
				flex: 4;
			}
			@include respond-to(ipad) {
				.mainL {
					margin-right: 0px;
					flex: 5.5;
				}
				.mainR {
					flex: 4.5;
					padding-right: 0;
				}
			}
			.titleFont1 {
				color: #333;
				font-size: 16px;
				font-weight: Bold;
				margin-bottom: 5px;
			}
			.titleFont1Right {
				margin-bottom: 5px;
				height: 20px;
			}
			.titleFont2 {
				font-size: 14px;
				color: #666;
				margin-bottom: 10px;
			}
			.titleFont3 {
				font-size: 14px;
				color: #666;
				display: flex;
				border-bottom: 1px solid #f8f8f8;
				min-height: 30px;
				align-items: center;
				flex-wrap: wrap;
				padding: 8px 0;
				.on {
					color: $color-blue;
					font-weight: Bold;
					margin: 0 5px;
					text-decoration: underline;
					cursor: pointer;
				}
			}
			.titleFont3Not {
				border-bottom: 0;
			}
		}
	}
	.summary {
		::v-deep .normal-text {
			justify-content: flex-end;
		}
		@include parentDiv;
		background: #fff;
		width: 534px;
		border: 1px solid #dfdfdf;
		border-radius: 10px;
		margin: 25px auto 0 auto;

		.title {
			font-size: 20px;
			background: #f8f8f8;
			padding: 0 40px;
			height: 55px;
			display: flex;
			align-items: center;
			border-radius: 10px 10px 0px 0px;
			@include fontBold;
		}
		.imgDiv {
			.top {
				width: 200px;
				margin: 0 auto;
				padding: 20px 0 12px 0;
				text-align: center;
				img {
					@include imgCss;
					border-radius: 10px;
				}
			}
			.font {
				color: #666;
				font-size: 14px;
				text-align: center;
			}
		}
		.rowOne {
			color: #333;
			font-size: 15px;
			font-weight: Bold;
			padding: 0 40px;
			height: 25px;
			margin-top: 10px;
		}
		.rowTwo {
			padding: 0 40px;
			color: #333;
			font-size: 14px;
			display: flex;
			min-height: 25px;
			margin-bottom: 5px; //1
			.left {
				flex: 4;
			}
			.right {
				flex: 6;
				text-align: right;
				b {
					margin-left: 20px;
					color: #666;
					font-size: 14px;
					position: absolute;
					right: 0;
					top: 1px;
				}
				.rP1 {
					position: relative;
					color: #666;
					display: -webkit-box;
					//display: flex;
					overflow: hidden;
					white-space: inherit;
					text-overflow: ellipsis;
					-webkit-line-clamp: 2;
					-webkit-box-orient: vertical;
					word-break: break-all;
					padding-right: 34px;
				}
				.rP2 {
					display: flex;
					justify-content: flex-end;
					padding-right: 34px;
					margin-bottom: 5px;
					position: relative;
					color: #666;
					.l {
						color: #000;
					}
					.r {
						color: #666;
					}
				}
			}
		}
		.rowTwo:hover {
			background: #eef2f5;
			cursor: pointer;
		}
		.form {
			position: relative;
			@include parentDiv;
			display: flex;
			padding-top: 20px;
			border-top: 1px solid #f5f5f5;
			.left {
				flex: 5;
				font-size: 15px;
				display: flex;
				@include fontBold;
				position: relative;
				.lTitle {
					padding-top: 3px;
				}
			}
			.right {
				flex: 5;
				.li {
					height: 25px;
					display: flex;
					margin-bottom: 10px;
					.fontTitle {
						font-size: 15px;
						flex: 4.5;
						display: flex;
						align-items: center;
						@include fontBold;
					}
					.fontPrice {
						color: #666;
						font-size: 13px;
						flex: 5.5;
						text-align: left;
						display: flex;
						align-items: center;
						justify-content: flex-end;
					}
					.red {
						color: #f32b11;
						font-weight: Bold;
						font-size: 13px !important;
					}
				}
			}
		}
		.button {
			background: #ff6600;
			color: #fff;
			font-weight: Bold;
			font-size: 15px;
			border-radius: 10px;
			margin: 10px auto 30px auto;
			width: 220px;
			height: 40px;
			@include centerCenter;
			cursor: pointer;
			b {
				margin-left: 10px;
				font-weight: initial;
				//color: var(--color-primary);
			}
		}
	}
	.summaryFloat {
		.title {
			height: 40px;
			font-size: 18px;
			padding: 0 32px;
		}
		.button {
			margin-bottom: 15px;
		}
		.form {
			.left {
				.lTitle {
					font-size: 14px;
				}
			}
			.right {
				.li {
					margin-bottom: 0px;
					.fontTitle {
						font-size: 14px;
					}
					.red {
						font-size: 16px !important;
					}
				}
			}
		}
		.rowOne {
			font-size: 14px;
		}
		.rowTwo {
			margin-bottom: 1px;
		}
		.imgDiv {
			.top {
				img {
					width: 120px;
				}
			}
		}
	}
	.sticky {
		width: 100%;
		height: 60px;
		position: relative;
		@include centerCenter;
		background: #e4e7ef;
		font-weight: Bold;
		color: #333;
		font-size: 15px;
		margin-top: 44px;
		cursor: pointer;
		padding-top: 15px;
		b {
			position: absolute;
			top: 12px;
			left: 50%;
			font-size: 12px;
			transform: rotate(90deg);
		}
	}
	::v-deep .myMaskMedals {
		z-index: 99 !important;
	}
}

@media screen and (max-width: 1000px) {
	.quoteFdSoftEnamelLapelpin {
		.navBanner {
			display: none;
		}
		.navTitle {
			@include parentMDiv;
		}
		.listView {
			@include parentMDiv;
			margin-bottom: 10px;
			.list {
				padding: 15px 10px;
				display: inline-block;
				.title {
					font-size: 16px;
					margin-bottom: 2px;
				}
				.titleFont1 {
					font-size: 14px;
					margin-top: 5px;
				}
				.titleFont2 {
					font-size: 12px;
					margin-bottom: 10px;
				}
				.titleFont3 {
					color: #999;
					font-size: 12px;
					padding: 8px 0;
				}
				.mainL {
					margin-right: 0;
				}
				.mainR {
					margin-right: 0;
					padding-right: 0;
				}
			}
		}
		.ul .li .rowOne {
			font-size: 13px;
		}
		.ul .li .rowTwo {
			font-size: 12px;
		}
		.summary {
			width: calc(100% - 30px);

			.title {
				padding: 0 25px;
				height: 50px;
			}
			.imgDiv {
				.font {
					font-size: 18px;
				}
			}
			.rowOne {
				@include parentMBigDiv;
				margin-bottom: 10px;
			}
			.rowTwo {
				@include parentMBigDiv;
				.left {
					font-size: 12px;
				}
				.right {
					b {
						margin-left: 10px;
					}
					.rP2 {
						padding-right: 25px;
					}
				}
			}
			.form {
				padding: 20px 25px 0 25px;
				margin: 0;
				.left {
					font-size: 12px;
					.el-select {
						width: 70px !important;
					}
					.el-select .el-input .el-select__caret {
						display: flex;
						align-items: center;
						justify-content: center;
					}
					.el-select:hover .el-input__inner {
						height: 25px;
						line-height: 25px;
						font-size: 12px;
						border-radius: 2px;
					}
					.el-input__suffix {
						padding-top: 32px;
					}
					.el-select__caret.is-reverse {
					}
					.el-input__icon {
						line-height: 0px;
						width: 25px;
					}
				}
				.right {
					.li {
						height: auto;
						.fontTitle {
							font-size: 12px;
						}
						.fontPrice {
							font-size: 12px;
						}
					}
				}
			}
		}
	}
}
@media screen and (max-width: 1500px) {
	.quoteFdSoftEnamelLapelpin {
		.main {
			width: 100%;
			.mainRight {
				display: none;
			}
		}
	}
}
</style>

<style lang="scss">
.quoteFdSoftEnamelLapelpin .summary .form .left {
	.el-select {
		margin-left: 10px;
	}
	.el-input--suffix .el-input__inner {
		height: 30px;
		line-height: 30px;
		font-weight: 100;
		font-size: 12px;
	}
	.el-input__suffix {
		top: 0px;
		position: absolute;
		display: flex;
		align-items: center;
		justify-content: center;
	}
}
</style>
