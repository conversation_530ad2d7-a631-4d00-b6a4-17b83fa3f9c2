<template>
  <v-sheet class="out" :theme="$store.state.proTheme">
    <v-overlay :value="overlay">
      <v-progress-circular indeterminate size="64"></v-progress-circular>
    </v-overlay>



    <mHeader @windowSizeChange="windowSizeChange" @SAVE="SAVE" @preview="preview" @backFun="backFun"
      @changeLng="changeLng" @witchPageForHeader="witchPageForHeaderFun" @isGlobal="isGlobalFun" @layOut="layOutChange"
      @redo="redo" @undo="undo" :windowPreview="windowPreview" :pageInfo.sync="pageInfo" :language.sync="computeLang">
    </mHeader>



    <v-window v-model="windowPreview" @change="fontCssChangeFun" class="elevation-1" vertical>
      <v-window-item :key="0">
        <v-row no-gutters class="custom-scrollbar">
          <v-col scrollbar fluid :manageMobile="manageMobile" class="out-col" id="container" ref="scrollBox"
            @mouseleave="mouseoutFun">
            <article class="content" id="content" v-if="re">

              <div v-show="manageMobile">
                <div style="text-align: center;">
                  <iframe name="iframeMB" id="iframeMB" src="/iframeMB" width="375px" height="1080px"
                    frameborder="0"></iframe>
                </div>
              </div>

              <div v-show="!manageMobile">
                <v-card class="part item tools hover-tag" flat :ripple="false" v-for="(item, index) in pageRowDraftList"
                   @mouseover="mouseoverFun($event, index, item)" @click.stop="clickFun($event, item, index)" :class="{
                    justLine: item.isHidden ||item.isHiddenLang,
                    borderWhite: item.isHidden ||item.isHiddenLang,
                    updown: !(item.isHidden ||item.isHiddenLang),
                    forHidden: item.isHidden ||item.isHiddenLang,
                  }" :key="item.id" :index="index">
                  <template v-if="item.sampleData.name == 'template'">
                    <v-card class="template" @click.stop="templateClick(item, index)" :ripple="false">
                      <div>{{ item.sampleData.message }}</div>
                    </v-card>
                  </template>
                  <template v-else-if="item.isHidden || item.isHiddenLang"> </template>
                  <template v-else>
                    <div v-if="currentLan == 'en-us'" class="transFlag"
                      :style="`background-color:${item.isTranslate ? '#1e88e5' : '#ccc'}`"
                      @click.stop="changeTranslateStatus(item, index)">
                      <b class="icon-ENG"></b>
                    </div>
					<div v-if="item.sample&&item.sample.sortIndex" :style="{top:currentLan != 'en-us'?'0':'2em'}" class="transFlag">
						<div>{{ item.sample.sortIndex }}</div>
					</div>
                    <component :key="item.randomKey" :is="'modal' + item.sampleData.name" :data.sync="item.sampleData"
                      :id="item.sampleData.id" :data-theme="item.theme" :ref="'component' + item.randomKey">
                    </component>
                  </template>
                </v-card>
              </div>
            </article>
          </v-col>
          <v-col style="max-width: 421px">
            <mNavigation ref="mNavigation" :clickTarget="clickTarget" :clickPosition="clickPosition"
              :itemData="itemData" @setItemData="setItemData" :panels.sync="panels" :otherData="otherData"
              :font.sync="font" :canEdit.sync="canEdit" @getNewItems="getNewItems" @cleanImg="cleanImg"
              :themeColorDraft.sync="themeColorDraft" :pageInfo="pageInfo" :listAllColorData="listAllColorData"
              :shareData.sync="shareData" :canVuex="canVuex" :bannerArr="bannerArr"
              :expansionPanels.sync="expansionPanels" :pageName="pageName" :bannerImg_Video.sync="bannerImg_Video"
              @changeBannerPageFun="changeBannerPageFun" @bannerBack="getBannerData" @previewImage="previewImage"
              @getIframeMBDataFromNavigationFun="getIframeMBDataFromNavigationFun" :loading.sync="loading"
              :audienceId="audienceId" :cateId="cateId" :cateType="cateType" :pageType="pageType" :logImportExport="logImportExport"
              :totalCustomList="totalCustomList" @updateTotalCustomList="updateTotalCustomList" @refreshComponents="comKeyChange"></mNavigation>
          </v-col>
        </v-row>
      </v-window-item>


      <v-window-item :key="1">
        <v-row no-gutters class="custom-scrollbar">
          <v-col scrollbar fluid class="out-col" id="web">
            <v-card flat class="part item tools hover-tag" v-for="(item, index) in pageRowDraftList" :key="item.id"
              :index="index">
              <component :key="item.randomKey" :is="'modal' + item.sampleData.name" :data.sync="item.sampleData"
                :id="item.sampleData.id" :data-theme="item.theme"></component>
            </v-card>
          </v-col>
        </v-row>
      </v-window-item>
    </v-window>



    <v-dialog v-model="addDialog" scrollable max-width="52.0833vw">
      <v-card class="addCard">
        <v-form v-model="valid" class="addFrom" ref="form">
          <div @click="delClick" class="del">
            <v-icon>mdi-close</v-icon>
          </div>
          <ul>
            <li>
              <span style="color: red">*</span><span>Page Name:</span>
              <v-text-field v-model="addForm.pageName" outlined clearable :rules="nameRules" dense></v-text-field>
            </li>
            <li>
              <span>Crowd:</span>
              <v-autocomplete v-model="addForm.crowdId" :items="crowdItems" item-text="name" item-value="id" chips
                small-chips solo flat label="Please select" outlined dense>
              </v-autocomplete>
            </li>
            <li>
              <span>Title:</span>
              <v-text-field v-model="addForm.title" outlined clearable dense></v-text-field>
            </li>
            <li>
              <span>Keywords:</span>
              <v-text-field v-model="addForm.keywords" outlined clearable dense></v-text-field>
            </li>
            <li>
              <span>Router Name:</span>
              <v-text-field v-model="addForm.routingName" :value="addForm.routingName" outlined clearable dense
                oninput="value=value.trim().toLowerCase().replace(/[^a-z0-9-/]/g, '').replace(/\-+/g, '-').replace(/\/+/g, '/')"></v-text-field>
            </li>
            <li>
							<span>Router Name Lang:</span>
							<!--							//去首尾空格  转小写字符  去特殊字符 多个横杠合并成一个横杠 多个斜杠合并成一个斜杠-->
							<v-text-field
								v-model="addForm.routingNameLang"
								:value="addForm.routingNameLang"
								outlined
								clearable
                dense
								oninput="value=value.trim().toLowerCase().replace(/[^a-z0-9-/]/g, '').replace(/\-+/g, '-').replace(/\/+/g, '/')"
							></v-text-field>
						</li>
            <li class="gg">
              <span>Description:</span>
              <v-textarea v-model="addForm.description" outlined dense></v-textarea>
            </li>
            <li class="gg">
              <span>Structuring:</span>
              <div>
                <div v-for="(item, index) in structuringList" :key="index" style="display: flex;">
                  <v-textarea outlined v-model="item.value"></v-textarea>
                  <v-icon style="height:10%;align-items: baseline;"
                    @click="delStructuring(index)">mdi-trash-can-outline</v-icon>
                </div>
                <div style="text-align: center;">
                  <v-btn @click="addStructuring" style="width: 50%;margin-bottom: 30px;" color="primary">+ New
                    structure</v-btn>
                </div>
              </div>
            </li>
            <li class="addBtn gg">
              <v-btn elevation="2" @click="delClick">cancel</v-btn>
              <v-btn elevation="2" color="primary" @click="add">confirm</v-btn>
            </li>
          </ul>
        </v-form>
      </v-card>
    </v-dialog>

    <v-dialog v-model="dialog2" max-width="500" attach=".out-col">
      <v-card>
        <v-card-title class="text-h5">Hide this section?
        </v-card-title>

        <v-card-text>
          this section and its contents will be displayed from current
          page
        </v-card-text>

        <v-card-actions>
          <v-spacer></v-spacer>

          <v-btn color="#e6e6e6" depressed light @click="dialog2 = false">
            Cancel
          </v-btn>

          <v-btn color="#1a73e8" depressed dark @click="hiddenFun">
            Hide
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <v-dialog v-model="deleteParentDialog" max-width="500" attach=".out-col">
      <v-card>
        <v-card-title class="text-h5">delete this section?
        </v-card-title>
        <v-card-actions>
          <v-spacer></v-spacer>

          <v-btn color="#e6e6e6" depressed light @click="deleteParentDialog = false">
            Cancel
          </v-btn>

          <v-btn color="#1a73e8" depressed dark @click="deleteParentFun">
            Delete
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <v-dialog v-model="deleteChildDialog" max-width="500" attach=".out-col">
      <v-card>
        <v-card-title class="text-h5">delete this section? May cause a reset of the phone style, please operate with caution!
        </v-card-title>
        <v-card-actions>
          <v-spacer></v-spacer>

          <v-btn color="#e6e6e6" depressed light @click="deleteChildDialog = false">
            Cancel
          </v-btn>

          <v-btn color="#1a73e8" depressed dark @click="deleteChildFun">
            Delete
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <v-dialog attach="#container" v-model="dialog3" scrollable max-width="300px">
      <v-card v-if="pageInfo && pageInfo.pageModel">
        <v-card-title>Select Page</v-card-title>
        <v-divider></v-divider>
		<v-text-field
			v-model.trim="selectPageFilter"
			label="Filter By Name"
			style="padding: 0 10px;"
			clearable
			hide-details
			outlined
			dense
			/>
        <v-card-text>
          <v-radio-group hide-details v-model="moveTarget" column>
            <v-radio :disabled="item.id == pageInfo.pageModel.id" v-for="item in filterAllPage" :key="item.id"
              :label="item.pageName" :value="item"></v-radio>
          </v-radio-group>
        </v-card-text>
        <v-divider></v-divider>
        <v-card-actions>
          <v-btn color="blue darken-1" text @click="moveTemplateFun" :disabled="!moveTarget">
            Move
          </v-btn>
          <v-btn color="blue darken-1" text @click="dialog3 = false">
            Cancel
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <v-dialog v-model="dialog4" max-width="500">
      <v-card v-if="dialog4" class="pa-10" style="display: flex;
        flex-direction: column;
        align-items: center;">
        <v-img height="150" width="150" :src="moveSuccess ? successImg : errorImg"></v-img>
        <v-card-text style="
          text-align: center;
          display: flex;
          flex-direction: column;">
          <template v-if="moveSuccess">
            <strong class="mt-5">Success</strong>
            <span class="mt-5">You have moved {{ tt.sampleExampleName }} section to
              {{ moveTarget.pageName }} page</span>
          </template>
          <template v-else>
            <strong class="mt-3">Failure</strong>
            <span class="mt-5">Sorry, the transfer operation failed. Please try
              again.</span>
          </template>
        </v-card-text>
      </v-card>
    </v-dialog>

	<v-dialog v-model="lanFlag" scrollable max-width="40vw">
      <v-card  outlined tile>
		<v-card-text style="display: flex;flex-wrap: wrap;justify-content: space-between;flex-direction: row;padding-bottom: 0;padding-top: 10px;">
			<div v-for="(val,key) in languageFlagList" :key="key" style="width: 18%;box-sizing: border-box;padding: 10px;">
			<v-chip
			    style="width:100%;text-align: center;display: inline-block"
				class="ma-0"
				:color="manageMobile?(val.isEditMb?'red':'blue') : (val.isEdit?'red':'blue')"
				label
				outlined
				>
				{{ val.language}}
			</v-chip>
		</div>
		</v-card-text>
		<v-card-actions >
          <v-spacer></v-spacer>
          <v-btn
            color="primary"
            text
            @click="lanFlag = false"
          >
            NEXT
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
	<div flex class="mask" v-show="dialog5">
		<div  class="mask-content">
			<b pointer class="icon-jxsht-zdgl-xx" @click="dialog5 = false"></b>
			<pic :src="previewImageUrl"></pic>
		</div>
	</div>
  </v-sheet>
</template>

<script>
// import Vue from "vue";
import mNavigation from "@/components/buildWeb/mNavigation.vue";
import mHeader from "@/components/buildWeb/mHeader.vue";

import showTools from "@/components/tools";
import TinyEditor from "@/components/TinyEditor.vue";
import editDiv from "@/components/editDiv.vue";
import { uploadFile } from "@/utils/oss.js";

import { getBase64, getDate, debounce, dataURLtoFile,deepClone } from "@/utils/utils";

import { getLanguageByProId } from "@/api/manage/set";

import {
  editPagesAllSample,
  getPagesDraftById,
  listAllStyle,
  listAllPages,
  listAllColor,
  getHeadFootPagesDraft,
  editPageThemeFont,
  movePageSampleToOtherPage,
  getHeadFootPageSampleList,
  listRetailerSampleBanner,
  listVideo,
  setProLogo,
  addCustomPage,
  getAudienceList,
  getPageRowEditTagById
} from "@/api/manage/buildWeb";
const recordsList = [];
const recordsListBack = [];

export default {
  layout: "defaultBuildWeb",
  name: "web",
  components: {
    mNavigation,
    mHeader,
    TinyEditor,
    editDiv,
  },
  data: () => ({
	selectPageFilter:"",
	deepCopyAllPage:null,
	filterAllPage:[],
	dialog5:false,
	previewImageUrl:"https://static-oss.gs-souvenir.com/web/quoteManage/********/image_2049mFDtWS.png",
	lanFlag:false,
	languageFlagList:[],
	moveFlag : false,
    computeLang: {},
    structuringList: [],
    themeDataCopy: null,
    totalCustomList: [],
    spaMB: false,
    audienceId: 0,
    crowdItems: [],
    addForm: {
      name: "",
      description: "",
      keywords: "",
      title: "",
      crowdId: "",
      structuring: "",
      routingNameLang:"",
      pageName:"",
    },
    addDialog: false,
    valid: false,
    nameRules: [(v) => !!v || "Name is required"],
    loading: false,
    bannerImg_Video: {
      page: 1,
      pageSize: 3,
      pageLength: 1,
    },
    canEdit: true,
    expansionPanels: [],
    itemDataIndex: -1,
    bannerArr: [],
    cateId: null, //分类id
    parentCateId: "",
    isStockPage: -1,
    cateType: null,
    websiteStyleId: null,
    pageName: "",
    pageType: null,
    canVuex: false,
    sysProOwnerDTO: null,
    hoverItem: null,
    queryId: null,
    queryType: null,
    tt: {}, //移动模块 选中的模块
    moveSuccess: true,
    successImg: require("/assets/images/threeD/payment successful.png"),
    errorImg: require("/assets/images/threeD/payment failed.png"),
    allIndex: 0, //鼠标hover的index
    theIndex: 0,
    themeColorDraft: null, //当前主题色
    thePage: null,
    headerFooterId: null, //页头页尾id
    contentId: null, //页身id
    shareData: null,
    listAllColorData: [],
    layoutList: null,
    layOutModel: null,
    allPage: null,
    pageModel: null,
    window: null,
    windowPreview: 0,
    font: null,
    ss: "https://sticker-static.oss-accelerate.aliyuncs.com/image/uploads/20220707/Full-color-Lanyards.png",
    cropperImg: "",
    changeImageWindow: 0,
    contentIndex: null, //若组件中有多个循环列表
    hoverIndex: null,
    hoverList: "list", //list
    dialogIndex: null,
    childHoverIndex: null,
    carouselModel: null,
    otherData: {
      dialog1: false,
      clean: false,
    },
    dialog: false,
    clickData: {}, //myTools点击选中的数据
    panelsFilterData: [
      {
        type: "banner",
        value: "changeBanner",
      },
      {
        type: "video",
        value: "changeVideo",
      },
      {
        type: "title",
        value: "editText",
      },
      {
        type: "text",
        value: "editText",
      },
      {
        type: "subTitle",
        value: "editText",
      },
      {
        type: "button",
        value: "button",
      },
      {
        type: "img",
        value: "changeImage",
      },
    //   {
    //     type: "logo",
    //     value: "imageLogoSetting",
    //   },
      {
        type: "share",
        value: "socialMedia",
      },
      {
        type: "contact",
        value: "contactInformation",
      },
      {
        type: "icon",
        value: "icons",
      },
      {
        type: "product_crowd",
        value: "product_crowd",
      },
      {
        type: "sticker_crowd",
        value: "product_crowd",
      },
      {
        type: "navigation",
        value: "navigation",
      },
      {
        type: "swiper",
        value: "swiper",
      },
      {
        type: "img_list",
        value: "img_list",
      },
      {
        type: "nav_list",
        value: "nav_list",
      },
      {
        type: "price_list",
        value: "price_list",
      },
      {
        type: "custom_list",
        value: "custom_list",
      },
      {
        type: "modal_data",
        value: "modal_data",
      },
      {
        type: "reviews",
        value: "reviews",
      },
      {
        type: "template_list",
        value: "template_list",
      },
      {
        type: "reviews_product",
        value: "reviews_product",
      },
	  {
        type: "tag_list",
        value: "tag_list",
      },{
		type:'quote_table',
		value:'quote_table'
	  },{
		type:'card_list',
		value:'card_list'
	  }
    ],
    clickTarget: null,
    clickPosition: null,
    vModel: "bannerH1",
    itemData: {
      no: true,
    },
    panels: "",
    newItems: {},
    pageRowDraftList: [],
    pageRowDraftListMB: [],
    pageInfo: {
      layoutList: [],
      layOutModel: null,
      allPage: [],
      pageModel: null,
      websiteStyleIdDraft: null,
      languageList: [],
    },
    timed: null,
    templateDom: {
      sampleData: {
        name: "template",
        message: "Choose a section and drop it anywhere on the page....",
      },
    },
    items: [
      { title: "Dashboard", icon: "mdi-view-dashboard" },
      { title: "Photos", icon: "mdi-image" },
      { title: "About", icon: "mdi-help-box" },
    ],
    v: {},
    deleteChildDialog: false,
    deleteParentDialog: false,
    dialog2: false,
    dialog3: false,
    dialog4: false,
    moveTarget: null, //移动富文本 目标页信息
    selectedItem: null,
    toggle_multiple: null,
    window: null,
    targetDom: null,
    targetIndex: null,
    targetType: null,
    templateClickIndex: null,
    isMobile: false,
    pageData: [],
    editPageThemeFontData: {
      proId: null,
      pageIdList: [],
      websiteStyleIdDraft: null,
      fontDraft: null,
      themeColorIdDraft: "",
      isGlobal: 1,
    },
    proId: null,
    re: true,
    overlay: false,
    findObj: {},
	eventType:'',
	pageHeadFooterId:[],
  }),
  computed: {
    manageMobile() {
      return this.$store.getters.getManageMobile;
    },
    buildWebLoading() {
      return this.$store.getters.getBuildWebLoading;
    },
    currentLan() {
    //   const lan = JSON.parse(localStorage.getItem('bwLng'))
      return this.$route.query.language || null
    },
	logImportExport(){
		return this.$store.getters['manage/getBtnPermission'].includes('logImportExport')
	}
  },
  watch: {
    "$route.query.id": function (val) {
      location.reload();
    },
    "$route.query.language": function (newVal,oldVal) {
	  if(newVal != oldVal) location.reload();
    },
    windowPreview: {
      handler(val) {
        this.fontCssChangeFun();
      },
    },
    font: {
      handler(val) {
        this.editPageThemeFontData.fontDraft = val;
        this.fontCssChangeFun();
      },
    },
    themeColorDraft: {
      handler(val) {
        if (val) {
          this.$store.getters.setTheme(JSON.parse(val.themeData));
          this.editPageThemeFontData.themeColorIdDraft = val.id;
          if (window.frames.iframeMB && window.frames.iframeMB.window) window.frames.iframeMB.window.postMessage({
            type: "SET_THEMECOLOR",
            payload: val.themeData,
          }, "*")
        }
      },
      immediate: true,
      deep: true,
    },
    hoverIndex: {
      handler(val) {
		//当鼠标移入所选的模版时，分为移动端和pc端进行相应处理
		if(this.manageMobile && window.frames.iframeMB && window.frames.iframeMB.window){
			this.killTools();
			$(window.frames.iframeMB.$('.part')[val]).addClass("hover-type");
            this.filterNavigation(window.frames.iframeMB.$('.part')[this.allIndex]);
		}else{
			this.killTools();
			$($(".part")[val]).addClass("hover-type");
			this.filterNavigation($(".part")[this.allIndex]);
		}
      },
    },
    childHoverIndex: {
      handler(val) {
        this.killTools2();
        let parent;
        if (this.contentIndex) {
          parent = $($($(".part")[this.allIndex]).find(".contentPart")[this.contentIndex]).find(".part2")[val];
        } else {
		  if(val&&val.includes('_')){
			parent = $($(".part")[this.allIndex]).find(`.bpsFaq[childHoverIndex=${val}]`)[0]
		  }else{
			if(this.hoverItem&&this.hoverItem?.sampleData.name=='Twins'&&this.$store.state.twinsLeftOrRight){
				let id
				if(this.$store.state.twinsLeftOrRight == 'list'){
					id = this.hoverItem.sampleData.id + 'right'
					parent =$($(".part")[this.allIndex]).find(`#${id} .part2`)[val]
				}else if(this.$store.state.twinsLeftOrRight == 'outer'){
                    id = this.hoverItem.sampleData.id + 'left'
					parent =$($(".part")[this.allIndex]).find(`#${id} .part2`)[val]
				}
			}else{
				parent = $($(".part")[this.allIndex]).find(".part2")[val];
			}
		  }
        }
        $(parent).addClass("hover-type");
        this.filterNavigation(parent);
      },
    },
    dialog2: {
      handler(val) {
        if (val) this.dialogIndex = JSON.parse(JSON.stringify(this.hoverIndex));
      },
    },
    deleteParentDialog: {
      handler(val) {
        if (val) this.dialogIndex = JSON.parse(JSON.stringify(this.hoverIndex));
      },
    },
    manageMobile: {
      handler(val) { },
    },
	selectPageFilter:{
		handler(val){
			debounce(this.filterPage(val),300)
		}
	}
  },
  provide() {
    return { stepRecord: this.stepRecord };
  },
  methods: {
    filterPage(keyword,num = true){
		let arr = deepClone(this.pageInfo.allPage) || []
        if(!keyword) this.filterAllPage = arr || []
  		else {
			if(num)this.moveTarget = null
			const pattern = new RegExp(keyword.split('').join('.*'), 'i')
			this.filterAllPage = arr.filter(item => pattern.test(item.pageName))
		}
	},
	previewImage(url){
		this.previewImageUrl = url
		this.dialog5 = true
	},
    comKeyChange() {
      this.$refs[`component${this.itemData.randomKey}`][0].$forceUpdate()
    },
    delStructuring(index) {
      this.structuringList.splice(index, 1)
    },
    addStructuring() {
      this.structuringList.push({ value: "" })
    },
    delClick() {
      this.overlay = false;
      this.addDialog = false;
      this.addForm = {
        name: "",
        description: "",
        keywords: "",
        title: "",
        crowdId: "",
        structuring: "",
      };
      this.structuringList = [];
    },
    //撤回
    undo() {
      const index = recordsList.length - 2;
      if (recordsList.length == 1) return;

      this.re = false;
      this.pageRowDraftList = recordsList[index];
      const temp = recordsList.pop();
      recordsListBack.push(temp);

      this.expansionPanels = [];
      this.panels = "";
      this.$nextTick(() => this.re = true);
    },
    //恢复
    redo() {
      if (recordsListBack.length == 0) return;
      else {
        this.re = false;
        this.pageRowDraftList = recordsListBack.pop();
        this.stepRecord();
      }
      this.expansionPanels = [];
      this.panels = "";
      this.$nextTick(() => this.re = true);
    },

    //接受navigation iframe数据
    getIframeMBDataFromNavigationFun(val) {
      if (window.frames.iframeMB && window.frames.iframeMB.window) {
        if (val.type == "img") window.frames.iframeMB.window.postMessage({
          type: "SET_IMAGE",
          payload: { url: val.data, type: this.spaMB },
        }, "*");
        else if (val.type == "banner" || val.type == "video") window.frames.iframeMB.window.postMessage({
          type: "SET_BANNER",
          payload: { url: val.data, type: this.spaMB },
        }, "*");
      }
    },
    // 记录操作步骤 文字 模板
    stepRecord() {
      recordsList.push(JSON.parse(JSON.stringify(this.pageRowDraftList)));
    },
	setItemData (obj) {
		if(obj.type == "dialog") {
			this.itemData.sampleData.type.customData.method = obj.val
			return
		}
		let val = obj.val
		this.itemData = val
		// 在手机端的时候需要将实时数据传给iframe
		if (process.browser && this.manageMobile) {
			let flag = obj.type
			//事件分为点击触发事件"click"，鼠标移入并点击事件菜单触发的事件"moseover"
			switch (flag) {
				case "All":
				// 需要对点击触发事件进行进一步判断
					if (val?.sampleData?.type?.clickType == "video" && this.eventType == "click") this.getIframeMBDataFun(val.sampleData.type.customData, "SET_VIDEO")
					break;
				case "Color":
					this.getIframeMBDataFun(val.sampleData.style, "SET_COLORVALUE")
					break;
				case "BoxStyle":
				    this.getIframeMBDataFun(val.sampleData, "SET_BOXSTYLEVALUE")
				break;
				case "MBNavList":
				    this.getIframeMBDataFun(val.sampleData.outer, "SET_NAVLISTVALUE")
				break;
				case "MBImgList":
				    this.getIframeMBDataFun(obj, "SET_IMGLISTVALUE")
				break;
			}
		}
	},
	/**
	 * 当itemData改变时，根据条件进行更新操作
	 * @param {*} val 传给iframe的数据
	 * @param {string} type 传给iframe的事件类型
	 */
	getIframeMBDataFun(val,type) {
		if (window.frames.iframeMB && window.frames.iframeMB.window) {
			window.frames.iframeMB.window.postMessage({
				type,
				payload: { url: val, type: this.spaMB },
			}, "*");
        }
    },
    changeBannerPageFun() {
      this.getBannerData();
    },
    fontCssChangeFun() {
	  if(this.font == null||this.manageMobile) return
      this.$nextTick(() => {
        let targetId = $("#container");
        let targetId2 = $("#web");
        targetId.css("font-family", this.font);
		targetId[0].style.setProperty("--text-family",this.font)
        targetId2.css("font-family", this.font);
		// let font = this.font
		// $.each($(".custom-scrollbar>.col>.content .v-card"), function(){
		// this.style.setProperty("--text-family",font)
		// });
        ``;
      });
    },
    cleanImg() {
      this.changeImageWindow = 0;
    },
    //根据网站风格改变header footer
    getHeadFootPageSampleList(id) {
      let postData = {
        websiteStyleId: id,
      };
      getHeadFootPageSampleList(postData).then((res) => {
        let temp = res.data;
        //转JSON
        temp.forEach((item) => {
          item.randomKey = Math.random();
          item.sampleData = JSON.parse(item.sampleData);
        });
        //页头页尾赋值
        this.$set(this.pageRowDraftList, 0, temp[0]);
        this.$set(
          this.pageRowDraftList,
          this.pageRowDraftList.length - 1,
          temp[1]
        );
      });
    },
    //header 改变网站风格
    layOutChange(val) {
      this.getHeadFootPageSampleList(val);
    },
    // filterIndexById(id) {
    //   return this.pageInfo.allPage.findIndex((x) => {
    //     return x.id == id;
    //   });
    // },

    findObjectById(id, array = this.pageInfo.allPage) {
      for (const item of array) {
        if (Number(item.id) === Number(id)) return item;

        if (item.childList) {
          const foundObject = this.findObjectById(id, item.childList);
          if (foundObject) return foundObject;
        }
      }

      return null;
    },
    //移动模板
    moveTemplateFun() {
      this.movePageSampleToOtherPage();
    },
    movePageSampleToOtherPage() {
      let postData = {
        otherPageId: this.moveTarget.id,
        pageRowDraft: {
          id: this.itemData.id,
          proId: this.proId,
          sampleId: this.itemData.sampleId,
          sampleData: JSON.stringify(this.itemData.sampleData),
          mbSampleData: (typeof this.itemData.mbSampleData === 'string') ? this.itemData.mbSampleData : JSON.stringify(this.itemData.mbSampleData),
        },
      };
      movePageSampleToOtherPage(postData).then(() => {
        this.dialog3 = false;
        this.dialog4 = true;
        this.pageRowDraftList.splice(this.templateClickIndex, 1);
        this.$nextTick(() => this.dialog4 = false);
      });
    },
    isGlobalFun(val) {
      this.editPageThemeFontData.isGlobal = val;
    },
    witchPageForHeaderFun(val) {
      this.editPageThemeFontData.pageIdList = [];
      val.forEach((item) => this.editPageThemeFontData.pageIdList.push(item.id));
    },
    //总模板数据
    getPageTemplateFun() {
      //初始化页面id
      this.editPageThemeFontData.pageIdList = [];
      this.editPageThemeFontData.pageIdList.push(this.pageInfo.pageModel.id);
      this.pageHeadFooterId = []
      Promise.all([
        getHeadFootPagesDraft({ proId: this.proId }),
        getPagesDraftById({ id: this.pageInfo.pageModel.id }),
      ]).then((res) => {
		let headerFooterInfo = res[0].data;
        let contentInfo = res[1].data;
        this.headerFooterId = headerFooterInfo.id;
        this.contentId = contentInfo.id;
        this.pageName = contentInfo.pageName;
        this.pageType = contentInfo.pageType;
        this.websiteStyleId = contentInfo.websiteStyleId;
        this.cateId = contentInfo.cateId;
        this.parentCateId = contentInfo.parentCateId;
        this.isStockPage = contentInfo.isStockPage;
        this.cateType = contentInfo.cateType;
        this.audienceId = contentInfo.audienceId;
        this.themeDataCopy = headerFooterInfo.themeColorDraft.themeData;

        this.pageInfo.websiteStyleIdDraft = contentInfo.websiteStyleIdDraft;
        if (headerFooterInfo.pageRowDraftList.length > 0) {
          this.pageRowDraftList = headerFooterInfo.pageRowDraftList;
          this.sysProOwnerDTO = {
            logo: headerFooterInfo.proLogo,
            logoFooter: headerFooterInfo.proLogoFooter,
            ...headerFooterInfo.sysProOwnerDTO,
          };
          headerFooterInfo.pageRowDraftList.map((item, index) => {
            if (index == 0) this.sysProOwnerDTO.proTheme = JSON.parse(item.sampleData).theme;
            this.pageHeadFooterId.push(item.id)
          })
          //setProSystem
          this.$store.commit("setProSystem", this.sysProOwnerDTO);
          this.canVuex = true;
          // 设置pageRowDraftList的其余页面数据
          contentInfo.pageRowDraftList.forEach((item, index) => {
            this.pageRowDraftList.splice(index + 1, 0, item);
          });
          //获取页面样式数据
          //使用头部主题色
          let themeColorData = headerFooterInfo.themeColorDraft;
          themeColorData.themeData = this.themeDataCopy;
          this.themeColorDraft = themeColorData;
          this.font = contentInfo.fontDraft || headerFooterInfo.fontDraft;
          //1:反序列化sampleData并添加随机key  2:pageRowDraftListMB深拷贝,sampleData合并mbSampleData
          this.pageRowDraftList.forEach((item,index) => {
            item.sampleData = JSON.parse(item.sampleData) || {};
            item.randomKey = Math.random();
			//3:{}补齐手机端里面的list,outer数据
			if(item.mbSampleData){
				let tempMbSample = JSON.parse(item.mbSampleData)
				if(item.sampleData &&item.sampleData.list){
					if(tempMbSample.list && item.sampleData.list.length > tempMbSample.list.length){
						item.sampleData.list.forEach((samVal,samInd)=>{
							if(tempMbSample.list[samInd] === undefined){
								tempMbSample.list[samInd] = {}
							}
						})
					}
				}
				if(item.sampleData &&item.sampleData.outer){
					if(tempMbSample.outer && item.sampleData.outer.length > tempMbSample.outer.length){
						item.sampleData.outer.forEach((samVal,samInd)=>{
							if(tempMbSample.outer[samInd] === undefined) tempMbSample.outer[samInd] = {}
						})
					}
				}
				item.mbSampleData = JSON.stringify(tempMbSample)
			}
            this.pageRowDraftListMB.push(JSON.parse(JSON.stringify(item)));
			this.pageRowDraftListMB[index].sampleData = this.$store.getters.deepMerge(
              [this.pageRowDraftListMB[index].sampleData],
              [JSON.parse(this.pageRowDraftListMB[index].mbSampleData)]
            )[0];
			// if(JSON.parse(this.pageRowDraftListMB[index].mbSampleData))
          });
          this.stepRecord();
        }
        //手机端模板数据传值给iframe
        if (window.frames.iframeMB && window.frames.iframeMB.window) {
          window.frames.iframeMB.window.postMessage({ type: "SET_IFRAME_LIST", payload: this.pageRowDraftListMB}, "*");
        }
        //重置iframe vuex
        if (window.frames.iframeMB && window.frames.iframeMB.window) {
          window.frames.iframeMB.window.postMessage({ type: "SET_VUEX_STORE", payload: this.$store.state }, "*");
        }

        //设置iframe主题色
        if (window.frames.iframeMB && window.frames.iframeMB.window) {
          window.frames.iframeMB.window.postMessage({ type: "SET_THEMECOLOR", payload: this.themeDataCopy, }, "*");
        }
        // this.getScale();
        this.listAllStyleFun();
        this.getBannerData();
      });
    },
    getBannerData() {
      let postData1 = {
        keyWord: this.cateId ? null : this.pageName,
        websiteStyleId: this.websiteStyleId,
        cateId: this.cateId,
        page: this.bannerImg_Video.page,
        pageSize: this.bannerImg_Video.pageSize,
        proId: this.proId,
        cateType: this.cateType,
      };
      let postData2 = {
        page: this.bannerImg_Video.page,
        pageSize: this.bannerImg_Video.pageSize,
      };
      Promise.all([
        listRetailerSampleBanner(postData1),
        listVideo(postData2),
      ]).then((res) => {
        let data1 = res[0].data.records;
        data1.forEach((x) => {
          x.clickTarget = "banner";
        });
        let data2 = [];

        if (this.pageName == "home") {
          data2 = res[1].data.records;
          data2.forEach((x) => {
            x.clickTarget = "video";
          });
        }
        this.bannerImg_Video.pageLength = Math.max(
          res[0].data.pages,
          this.pageName == "home" ? 0 : res[1].data.pages
        );
        this.bannerArr = [...data1, ...data2];
        if (this.bannerArr.length == 0 && this.bannerImg_Video.page != 1) {
          this.bannerImg_Video.page = 1;
          this.getBannerData();
        }
      });
    },
    listAllStyleFun() {
      listAllStyle({ proId: this.proId }).then((res) => {
        this.pageInfo.layoutList = res.data;
        this.pageInfo.layOutModel = this.pageInfo.layoutList.find((x) => {
          return x.id == this.pageInfo.websiteStyleIdDraft;
        });
        // this.editPageThemeFont.pageIdList = this.pageInfo.layOutModel;
      });
    },
    listAllPagesFun() {
      if (this.queryType == 0) {
        let postData = {
          proId: this.proId,
          isPageSample: 0,
        };
        listAllPages(postData).then(() => {
          let postData2 = {
            id: this.queryId,
          };
          getPagesDraftById(postData2).then((res) => {
            let list = [];
            list.push(res.data);
            this.pageInfo.allPage = list;
            // this.theIndex = this.queryId
            //   ? this.filterIndexById(this.queryId)
            //   : 0;
            this.findObj = this.findObjectById(this.queryId) || this.pageInfo.allPage[0];
            this.pageInfo.pageModel = res.data;
            this.getPageTemplateFun();
          });
        });
      } else if (this.queryType == 1) {
        let postData = {
          proId: this.proId,
          isPageSample: 0,
        };
        listAllPages(postData).then((res) => {
          this.pageInfo.allPage = res.data;
          // this.theIndex = this.queryId ? this.filterIndexById(this.queryId) : 0;
          this.findObj = this.findObjectById(this.queryId) || this.pageInfo.allPage[0];
          // this.pageInfo.pageModel = res.data[this.theIndex];
          this.pageInfo.pageModel = this.findObj;
          this.getPageTemplateFun();
        });
      }
    },
    // 修改翻译状态（1为翻译，0为不翻译）
    async changeTranslateStatus(val, ind) {
	  this.getLangTransList(!val.isTranslate,val)
      this.pageRowDraftList[ind].isTranslate = val.isTranslate ? 0 : 1;
    },
    backFun() {
      if (this.windowPreview == 1) {
        this.windowPreview = 0;
      } else {
        this.$router.push({ path: "/manage/siteEdit/contentEdit" });
      }
    },
    preview(val) {
      val == 1 ? (this.windowPreview = 0) : (this.windowPreview = 1);
    },
    //模板替换
    getNewItems(val) {
      if (val.sampleData) {
        this.pageRowDraftList.forEach((item, index) => {
          if (this.templateClickIndex == index) {
            val.randomKey = Math.random();
            val.sampleId = JSON.parse(JSON.stringify(val.id));
			if(val.isReplace){
				if(this.computeLang.language == 'en'&&this.computeLang.countryCode == 'us'){
					val.id = null;
					val.isReplace = true
				}else{
					val.id = item.id
					val.isReplace = true
				}
			}else{
				val.id = null;
				val.isReplace = null
			}

            let temp = JSON.parse(JSON.stringify(val));
			// 补齐对象操作
			if(temp.mbSampleStyle){
				let tempMbSample = JSON.parse(temp.mbSampleStyle)
				if(temp.sampleData &&temp.sampleData.list){
					if(tempMbSample.list && temp.sampleData.list.length > tempMbSample.list.length){
						temp.sampleData.list.forEach((samVal,samInd)=>{
							if(tempMbSample.list[samInd] === undefined){
								tempMbSample.list[samInd] = {}
							}
						})
					}
				}
				if(temp.sampleData &&temp.sampleData.outer){
					if(tempMbSample.outer && temp.sampleData.outer.length > tempMbSample.outer.length){
						temp.sampleData.outer.forEach((samVal,samInd)=>{
							if(tempMbSample.outer[samInd] === undefined) tempMbSample.outer[samInd] = {}
						})
					}
				}
				temp.mbSampleData = JSON.stringify(tempMbSample)
				delete temp.mbSampleStyle
			}
			this.pageRowDraftList.splice(index, 1, temp);
			// 替换模版只将当前手机端数据替换（en-us全部替换），新增模版全部同步最新的手机端不需要操作
			if(val.isReplace){
				this.pageRowDraftListMB.splice(index, 1, JSON.parse(JSON.stringify(temp)));
				this.pageRowDraftListMB[index].sampleData = this.$store.getters.deepMerge(
				[this.pageRowDraftListMB[index].sampleData],
				[JSON.parse(this.pageRowDraftListMB[index].mbSampleData)]
				)[0];
			}
          }
        });
        this.expansionPanels = [];
        this.stepRecord();
      }
      this.panels = "";
      this.templateClickIndex = null;
    },
    //mHeader
    windowSizeChange(val) {
      //强制loading
      this.$store.commit("setBuildWebLoading", true);

      //刷新
      location.reload();
    },
    clickFun(e, item, index) {
      if(item.sampleData.type?.clickType=="video"){
        if(item.sampleData.type.customData){
          if (!item.sampleData.type.customData.poster) {
            item.sampleData.type.customData.poster = item.sampleData.type.customData.poste || ""
          }
        }
      }
      this.expansionPanels = [];
      this.itemData = {
        no: true,
      };

      setTimeout(() => {
        let f;
        this.spaMB =
          (item.sampleData.type?.clickType == "product_crowd" ||
            item.sampleData.type?.clickType == "sticker_crowd") &&
            this.manageMobile
            ? true
            : false;
        // this.clickData = this.pageRowDraftList[index];
        this.panels = "";

        this.templateClickIndex = index;
        this.itemData = item;
        this.eventType = "click"
        if (this.spaMB) {
          f = {
            value: "changeImageForMB",
          };
        } else {
          f = this.panelsFilterData.find((x) => {
            return x.type == item.sampleData.type?.clickType;
          });
        }

        if (f) {
          this.panels = f.value;
        }


        this.$nextTick(() => {
          try {
            setTimeout(() => {
              if (this.spaMB) {
                this.$refs.mNavigation.$refs.cutImage5.comeFromGF();
              } else if (item.sampleData.name == "template" || (item.isHidden || item.isHiddenLang)) {
                return;
              } else if (item.sampleData.type.clickType == "share") {
                this.shareData = item.sampleData.type.customData;
              } else if (
                item.sampleData.type.clickType == "product_crowd" ||
                item.sampleData.type.clickType == "sticker_crowd"
              ) {
                if (this.manageMobile) {
                  this.$refs.mNavigation.$refs.cutImage.comeFromGF();
                } else {
                  if (this.itemData.sampleData.type.customData) {
                    //编辑
                    this.$refs.mNavigation.comeProductForm();
                  } else {
                    //新增
                    this.$refs.mNavigation.needNewProduct();
                  }
                }
              } else if (item.sampleData.type.clickType == "swiper") {
                this.$refs.mNavigation.comeSwiperForm();
              } else if (item.sampleData.type.clickType == "img_list") {
                this.$refs.mNavigation.comeImgListForm();
              } else if (item.sampleData.type.clickType == "nav_list") {
                this.$refs.mNavigation.comeNavForm();
              } else if (item.sampleData.type.clickType == "card_list") {
                this.$refs.mNavigation.cardListHandler();
              }else if (item.sampleData.type.clickType == "price_list") {
                this.$refs.mNavigation.comePriceForm();
              } else if (item.sampleData.type.clickType == "img"||item.sampleData.type.clickType == "video") {
				this.$refs.mNavigation.imgVideoHandler()
              }
			//   else if (item.sampleData.type.clickType == "video") {
            //     this.$refs.mNavigation.$refs.cutVideoImage.comeFromGF();
            //   }
			  else if (item.sampleData.type.clickType == "custom_list") {
                this.totalCustomList = item.sampleData.customList;
                setTimeout(() => {
                  this.$refs.mNavigation.getSemiQuoteProductList();
                }, 500);
              } else if (item.sampleData.type.clickType == "template_list") {
                setTimeout(() => {
                  this.$refs.mNavigation.getParentSortList();
                }, 500);
              } else if (item.sampleData.type.clickType == "tag_list") {
					this.$refs.mNavigation.ringCateAndTagList();
              }else if (item.sampleData.type.clickType == "quote_table") {
					this.$refs.mNavigation.quoteTableEdit();
              }
			//   else if (item.sampleData.type.clickType == "logo") {
                // this.$refs.mNavigation.$refs.cutImage2.comeFromGF();
            //   }
            }, 1);
          } catch (error) {
            console.log(error);
          }
        });
      }, 100);
    },
    updateTotalCustomList(val) {
      this.totalCustomList = val;
      this.itemData.sampleData.customList = val;
    },
    templateClick(item, index) {
      this.panels = "";
      this.itemData = {
        no: true,
      };
      this.templateClickIndex = index;
      this.hasExpansionPanels('click');
    },
    hasExpansionPanels(type ='replace') {
      if (this.expansionPanels.indexOf(2) == -1) {
        this.expansionPanels.push(2);
      }
      this.loading = true;
      try {
        this.$nextTick(() => {
          if (this.$refs.mNavigation) {
            this.$refs.mNavigation.changePageFun(type);
          }
        });
      } catch (error) {
        console.log(error);
      }
    },
    plusCardIndex(item, index) {
      if (index + 1 == this.pageRowDraftList.length) {
        return;
      } else {
        let target = this.pageRowDraftList[index + 1];
        this.$set(this.pageRowDraftList, index, target);
        this.$set(this.pageRowDraftList, index + 1, item);
      }
    },
    removeItem(index) {
      this.pageRowDraftList.splice(index, 1);
    },
    moveItem(val, self, target) {
      this.pageRowDraftList.splice(self, 1);
      this.pageRowDraftList.splice(target, 0, val);
    },
    newBoxFun(type, index) {
      this.dialog1 = true;
      this.targetDom = type;
      this.targetIndex = index;
    },
	deleteItemPublicFun(hoverType){
		let flag = this.pageRowDraftList[this.v.parentIndex].sampleData.name=='Twins'&& this.$store.state.twinsLeftOrRight,index = this.v.childIndex
		if(flag) index = this.v.childIndex*1 + 1
		// 当mb端存在样式时需要删除对应的移动端样式
		if(this.pageRowDraftList[this.v.parentIndex].mbSampleData){
			let mb = JSON.parse(this.pageRowDraftList[this.v.parentIndex].mbSampleData),mbData
			if(flag) mbData = mb[this.$store.state.twinsLeftOrRight]
			else mbData = mb[hoverType]
			if(mbData && mbData.length >= index){
				mbData.splice(index,1)
				this.pageRowDraftList[this.v.parentIndex].mbSampleData = JSON.stringify(mb)
			}
		}
		//该段代码原为对新增模版的处理，后新增模版方法修改后续无误删除
		// if(this.pageRowDraftList[this.v.parentIndex].mbSampleStyle){
		// 	let mb = JSON.parse(this.pageRowDraftList[this.v.parentIndex].mbSampleStyle),mbStyle
		// 	if(flag){
		// 		mbStyle = mb[this.$store.state.twinsLeftOrRight]
		// 		if(mbStyle && mbStyle.length >= this.v.childIndex){
		// 			mbStyle.splice(this.v.childIndex*1+1,1)
		// 			this.pageRowDraftList[this.v.parentIndex].mbSampleData = JSON.stringify(mb)
		// 		}
		// 	}else{
		// 		mbStyle = mb[hoverType]
		// 		if(mbStyle && mbStyle.length >= this.v.childIndex){
		// 			mbStyle.splice(this.v.childIndex,1)
		// 			this.pageRowDraftList[this.v.parentIndex].mbSampleData = JSON.stringify(mb)
		// 		}
		// 	}
		// 	if(mbStyle && mbStyle.length >= this.v.childIndex){
		// 		mbStyle.splice(this.v.childIndex,1)
		// 		this.pageRowDraftList[this.v.parentIndex].mbSampleStyle = JSON.stringify(mb)
		// 	}
		// }
		if(flag){
			this.pageRowDraftList[this.v.parentIndex].sampleData[this.$store.state.twinsLeftOrRight].splice(index, 1);
		}else{
			this.pageRowDraftList[this.v.parentIndex].sampleData[hoverType].splice(index, 1);
		}
		if(hoverType == 'imgOuter'){
			this.deleteItemPublicFun('titleList')
		}
	},
	// 删除小item对应操作
    deleteChildFun() {
		try {
		  	this.panels = "";
		  	// 如果编辑了，并且没翻译新增一个参数
		  	this.pageRowDraftList[this.v.parentIndex].isSyncMb = 1
		  	if(this.v.childIndex && this.v.childIndex.includes('_')){
				let tempIndex = this.v.childIndex.split('_')
				this.pageRowDraftList[this.v.parentIndex].sampleData['titleList'][tempIndex[0]]['list'].splice(tempIndex[1], 1)
		  	}else{
				this.deleteItemPublicFun(this.hoverList)
		    }
		  	this.deleteChildDialog = false;
		} catch (error) {
		  	this.$message.info('Error:Please contact the development center')
		}
    },
    deleteParentFun() {
      this.pageRowDraftList.splice(this.dialogIndex, 1);
      this.deleteParentDialog = false;
    },
    hiddenFun() {
      this.pageRowDraftList[this.dialogIndex].isHidden = true;
      this.dialog2 = false;
    },
    //过滤右上角操作框
    filterNavigation(event) {
      if ($(event).hasClass("forHidden")) showTools(event, { forHidden: true });
      else if ($(event).hasClass("part")) {
		if(this.manageMobile){
            if(this.hoverItem.sampleData.name == "Header") showTools(event, {
				after: !this.manageMobile,
				layout: true,
				moreOptions: true,
				header: true,
				manageMobile:true
        	})
		    else showTools(event, {
              layout: true,
              moreOptions: true,
			  manageMobile:true
            });
		}
        else if (this.hoverItem.sampleData.name == "Header") showTools(event, {
          after: !this.manageMobile,
          layout: true,
          moreOptions: true,
          header: true,
		  logImportExport:this.logImportExport
        });
		else if (this.hoverItem.sampleData.name == "Footer" && this.hoverItem.sampleData?.id.startsWith('layout')) showTools(event, {
		  before: !this.manageMobile,
          moreOptions: true,
          Footer: true,
		  logImportExport:this.logImportExport
        });
        else if (this.hoverItem.sampleData.name == "template") showTools(event, {
			templateList:true,
            moreOptions: true
        });
        else if (this.hoverItem.sampleData.id == "layout_02") showTools(event, {
          before: true,
          layout: true,
          moreOptions: true,
		  logImportExport:this.logImportExport
        });
        else showTools(event, {
          after: true,
          before: true,
          plusZone: true,
          minusZone: true,
          moreOptions: true,
		  logImportExport:this.logImportExport&&!!this.hoverItem.id
        });
      }else if($(event).hasClass("bpsFaq")&&$(event).hasClass("part2")){
		showTools(event, {
		noChildTools:false,
        leftRight: false,
        delChildCardZone: true,
        newChildCardZone: $(event).hasClass("productsType") ? false : true,
		isBpsFaq:true
        });
	  }
      else if ($(event).hasClass("part2")){
		// noChildTools表示Tools类型,默认为右上角，false表示是child的Tools
		showTools(event, {
		noChildTools:false,
        leftRight: true,
        delChildCardZone: true,
		isCard:this.hoverItem.sampleData.name == "Card",
		isSwiper:this.hoverItem.sampleData.name == "Swiper",
        newChildCardZone: $(event).hasClass("productsType") ? false : true
        });
	  }
    },
    mouseoverFun(event, index, item) {
      this.hoverItem = item;
      this.allIndex = index;
      if ($(event.target).attr("index")) {
        this.hoverIndex = $(event.target).attr("index");
      } else if ($($(event.target).parents(".part")).attr("index")) {
        this.hoverIndex = $($(event.target).parents(".part")).attr("index");
      } else {
        this.hoverIndex = null;
        this.killTools();
      }
      let defaultArray = (!item.sampleData.list || item.sampleData.name == "Footer") ? 'outer' : 'list';
      if ($(event.target).attr("childHoverIndex")) {
        this.childHoverIndex = $(event.target).attr("childHoverIndex");
        this.contentIndex = $(event.target).parents(".contentPart").attr("contenthoverindex");
        this.hoverList = $(event.target).parents(".contentPart").attr("hoverlist") ? $(event.target).parents(".contentPart").attr("hoverlist") : defaultArray;
       } else if ($($(event.target).parents(".part2")).attr("childHoverIndex")) {
        this.childHoverIndex = $($(event.target).parents(".part2")).attr("childHoverIndex");
        this.contentIndex = $(event.target).parents(".contentPart").attr("contenthoverindex");
		if(this.hoverItem.sampleData&&this.hoverItem.sampleData.name == "BPSFaq"){
			this.hoverList = 'imgOuter'
		}else{
			this.hoverList = $(event.target).parents(".contentPart").attr("hoverlist") ? $(event.target).parents(".contentPart").attr("hoverlist") : defaultArray;
		}
	   } else {
        this.childHoverIndex = null;
        this.contentIndex = null;
        this.hoverList = defaultArray;
        this.killTools2();
      }
    },
    mouseoutFun(event) {
      if (!this.dialog3) {
        this.hoverIndex = null;
        this.killTools();
      }
    },
    dialog1Skip(url) {
      let temp = url.split("?")[0].split("/");
      let fileName = temp[temp.length - 1];
      if (this.changeImageWindow == 1) {
        getBase64(this.$refs.nCropper.cropUrl, (res) => {
          let file = dataURLtoFile(res, fileName);
          uploadFile(file, { isRandomName: false, date: getDate() }).then(
            (res) => {
              this.itemData.sampleData[
                this.itemData.sampleData.type.clickPosition
              ][this.itemData.sampleData.type.index][
                this.itemData.sampleData.type.clickTarget
              ].value = res + "?" + Math.random();
              this.stepRecord();
            }
          );
        });
        this.otherData.dialog1 = false;
      } else {
        this.otherData.dialog1 = false;
      }
    },
    dialog1Submit(url) {
      if (this.changeImageWindow == 1) {
        this.submitUpload(url);
        this.otherData.dialog1 = false;
      } else {
        this.otherData.dialog1 = false;
      }
    },
    submitUpload(url) {
      let temp = url.split("?")[0].split("/");
      let fileName = temp[temp.length - 1];
      this.$refs.nCropper.$refs.cropper.getCropData((data) => {
        // do something
        let file = dataURLtoFile(data, fileName);
        uploadFile(file, { isRandomName: false, date: getDate() }).then(
          (res) => {
            this.itemData.sampleData[
              this.itemData.sampleData.type.clickPosition
            ][this.itemData.sampleData.type.index][
              this.itemData.sampleData.type.clickTarget
            ].value = res + "?" + Math.random();
            this.stepRecord();
          }
        );
      });
      // this.$refs.cropper.emitRealImg();
    },
    getFile(e) {
      //上传图片
      var file = e.target.files[0];
      if (!/\.(gif|jpg|jpeg|png|bmp|GIF|JPG|PNG)$/.test(e.target.value)) {
        return false;
      }
      var reader = new FileReader();
      reader.onload = (e) => {
        let data;
        if (typeof e.target.result === "object") {
          // 把Array Buffer转化为blob 如果是base64不需要
          data = window.URL.createObjectURL(new Blob([e.target.result]));
        } else {
          data = e.target.result;
        }
      };
      // 转化为base64
      reader.readAsDataURL(file);
      // 转化为blob
      uploadFile(file, { isRandomName: false, date: getDate() }).then((res) => {
        this.cropperImg = res + "?" + Math.random();
        this.changeImageWindow = 1;
        this.$refs.uploads1.value = "";
      });
    },
    killTools() {
		// 移动端的mytools挂在window.frame.iframeMB上面
		if(process.browser && this.manageMobile && window.frames.iframeMB && window.frames.iframeMB.$){
			window.frames.iframeMB?.$('.myTools')?.remove()
		}else{
			$(".myTools").remove();
		}
    },

    killTools2() {
      $(".part2 .myTools *").hide();
    },

   async add() {
      let valid = this.$refs["form"].validate();
      if (valid) {
        let tempArr = [];
        let temp1 = [];
        this.pageRowDraftList.forEach((item) => {
          if (item.sampleData.name != "template") {
            tempArr.push({
              sampleId: item.sampleId,
              sampleData: JSON.stringify(item.sampleData),
              isHidden: item.isHidden,
              isHiddenLang: item.isHiddenLang,
              isHiddenLangAll: item.isHiddenLangAll,
              isTranslate: item.isTranslate,
			  isReplace:!!item.isReplace?1:0,
              id: item.id,
            });
          }
        });
        tempArr = JSON.parse(JSON.stringify(tempArr));
        temp1.push(tempArr.shift());
        temp1.push(tempArr.pop());
		// 页头强校验
		let flag1 = temp1.every((item, ind) => item.id == this.pageHeadFooterId[ind])
		if (!flag1) {
			localStorage.setItem('errorInfo', JSON.stringify({ temp1, pageRowDraftList: this.pageRowDraftList, pageRowDraftListMB: this.pageRowDraftListMB, tempArr }))
			throw (new Error('ERROR:PageTemplates Incorrect'))
		}
        this.sysProOwnerDTO = { ...this.$store.state.proSystem };

        let value;
        let arrayValue = this.structuringList.map(item => item.value), check = true;
        for (let i = 0; i < arrayValue.length; i++) {
          let item = arrayValue[i];
          if (!item) {
            check = false;
            continue
          }
          try {
            if (typeof JSON.parse(item) !== 'object' || item.trim()[0] !== '{') {
              check = false;
              continue
            }
          } catch (error) {
            check = false;
            continue
          }
        }
        if (!check) {
          this.$toast.error('improper format');
          return;
        }
        value = JSON.stringify(arrayValue)
        let postData = {
          id: this.headerFooterId,
          proId: this.proId,
          pageRowDraftList: temp1,
          sysProOwnerDTO: this.sysProOwnerDTO,
          platform: this.manageMobile ? 2 : 1,
        };

        let postData3 = {
          proId: this.proId,
          pageIdList: this.editPageThemeFontData.pageIdList,
          websiteStyleIdDraft: this.pageInfo.websiteStyleIdDraft,
          fontDraft: this.editPageThemeFontData.fontDraft,
          themeColorIdDraft: this.editPageThemeFontData.themeColorIdDraft,
          isGlobal: this.editPageThemeFontData.isGlobal,
        };
        let postData4 = {
          proId: this.proId,
          proLogo: this.sysProOwnerDTO.logo,
          proLogoFooter: this.sysProOwnerDTO.logoFooter,
        };

        let postData5 = {
          proId: this.proId,
          parentId: "0",
          pageType: "2",
          pageName: this.addForm.pageName,
          title: this.addForm.title,
          audienceId: this.addForm.crowdId,
          structuring: value,
          keyword: this.addForm.keywords,
          description: this.addForm.description,
          routingName: this.addForm.routingName,
          routingNameLang:this.addForm.routingNameLang,
          pageRowDraftList: tempArr,
        };
		try {
			const res = await editPagesAllSample(postData)
			const res1 = await addCustomPage(postData5)
			const res2 = await editPageThemeFont(postData3)
			const res3 = await setProLogo(postData4)
			if(res.code ==200 &&res1.code ==200&&res2.code ==200&&res3.code ==200){
				this.$toast.success(res.message);
				this.delClick();
				location.reload();
				this.$router.go(-1);
			}
		} catch (error) {
			console.log(error)
		}
      }
    },

    //查询人群
    getCrowd() {
      let data = {
        keyword: "",
      };
      getAudienceList(data).then((res) => {
        this.crowdItems = res.data;
      });
    },
	// 校验是不是字符串和正常对象
	isJSON(val,type= 0) {
		try {
			if (type ==1) {
				// 为undefined时为新加模板不需要校验直接通行
			    if(val === undefined) return true
				let obj=JSON.parse(val);
				if (typeof obj == 'object' && obj)return true;
				else return false;
			}else{
				if (typeof val == 'object' && val)return true;
				else return false;
			}
		} catch (error) {
			return false
		}
	},
	// 校验js值的类型
	checkJSType (value) {
		return Object.prototype.toString.call(value).slice(8, -1)
	},
	// 保存时过滤outer和List，保存style和图片地址
	filterFun (arr) {
		return new Promise((resolve, reject) => {
			let filterData = []
			if (arr && arr.length) {
				arr.map(item => {
					let obj = {}
					this.recursionFun(item, obj, filterData)
					filterData.push(obj)
				})
			}
			resolve(filterData)
		})
	},

	// 需要考虑更深层级，递归过滤
	recursionFun (item, obj, filterData) {
		if (this.checkJSType(item) == 'Object') {
			let pattern = /^https?:\/\/.*$/
			Object.keys(item).map(key => {
				if (key == 'product_crowd') {
					let cx = item.product_crowd
					let tempObj = {}
					for (let cKey in cx) {
						if (cKey && cx[cKey].value && pattern.test(cx[cKey].value))
							tempObj[cKey] = { value: cx[cKey].value }
					}
					filterData.push(tempObj)
				} else if (key.includes('tyle')||(key == 'value' && pattern.test(item[key])) || key == 'column'|| key=="margin") obj[key] = item[key]
				else if (this.checkJSType(item[key]) == 'Object') {
					obj[key] = {}
					if(key=="method")delete obj[key]
					else{
						this.recursionFun(item[key], obj[key], filterData)
						if (Object.keys(obj[key]).length == 0) delete obj[key] // 删除空对象
					}
				} else if (this.checkJSType(item[key]) == 'Array') {
					obj[key] = []
					item[key].map((x, index) => {
						if (this.checkJSType(x) == 'Object') {
							this.recursionFun(item[key][index], (obj[key][index] = {}), filterData)
						} else if (this.checkJSType(x) == 'Array') {
							obj[key][index] = []
							x.map((y, ind) => {
								this.recursionFun(y, (obj[key][index][ind] = {}), filterData)
							})
						}
					})
					let flag = obj[key].find(element => Object.keys(element).length != 0) // 清除多余数组
					//当数组全为空对象时才能删除
					if (!flag) delete obj[key]
				}
			})
		}
	},
    async SAVE () {
			this.overlay = true;
			if (this.queryType == 0) {
				this.addDialog = true;
				this.getCrowd();
			} else {
				// temp1为页头页尾中间参数,tempArr为全部参数数组集合
				let tempArr = [], temp1 = []
				//默认为pc端参数，当为移动端时需要给tempArr传入mbSampleData
				this.pageRowDraftList.forEach((item) => {
					if (item.sampleData.name != "template") {
						// 校验mbSampleData和sampleData是否是正常的值
						if (!this.isJSON(item.mbSampleData, 1) || !this.isJSON(item.sampleData)) {
							throw (new Error('ERROR:PageTemplates Incorrect'))
						}
						delete item.sampleData.type    //type为setModal方法添加操作参数，保存时去掉
						// 如果移动并且点击了同步，根据isTranslate赋值item.sampleData.isSyncMb
						if (item.isSyncMb) {
							item.sampleData.isSyncMb = item.isTranslate ? 0 : 1
							tempArr.push({
								sampleId: item.sampleId,
								sampleData: JSON.stringify(item.sampleData),
								isHidden: item.isHidden,
								isHiddenLang: item.isHiddenLang,
								isHiddenLangAll: item.isHiddenLangAll,
								isReplace:!!item.isReplace?1:0,
								isTranslate: item.isTranslate,
								id: item.id,
								mbSampleData: !!item.mbSampleData ? item.mbSampleData : item.mbSampleStyle,
								isSyncMb: 1
							});
						} else {
							// 否则就是没有移动,判断之前是否移动且没有同步过
							if (item.isTranslate && item.sampleData.isSyncMb) {
								item.sampleData.isSyncMb = 0
								tempArr.push({
									sampleId: item.sampleId,
									sampleData: JSON.stringify(item.sampleData),
									isHidden: item.isHidden,
									isHiddenLang: item.isHiddenLang,
									isHiddenLangAll: item.isHiddenLangAll,
									isTranslate: item.isTranslate,
									isReplace:!!item.isReplace?1:0,
									id: item.id,
									mbSampleData: !!item.mbSampleData ? item.mbSampleData : item.mbSampleStyle,
									isSyncMb: 1
								});
							} else {
								tempArr.push({
									sampleId: item.sampleId,
									sampleData: JSON.stringify(item.sampleData),
									isHidden: item.isHidden,
									isHiddenLang: item.isHiddenLang,
									isHiddenLangAll: item.isHiddenLangAll,
									isTranslate: item.isTranslate,
									isReplace:!!item.isReplace?1:0,
									id: item.id,
									mbSampleData: !!item.mbSampleData ? item.mbSampleData : item.mbSampleStyle,
									isSyncMb: 0
								});
							}
						}
					}
				})
				if (this.manageMobile) {
					// 如果是手机端对pageRowDraftListMB的sampleData进行操作，保存至mbsampleData
					let MBarr = JSON.parse(JSON.stringify(this.pageRowDraftListMB))
					await Promise.allSettled(
						MBarr.map(async (item, index) => {
							let outer = item.sampleData.outer,
								list = item.sampleData.list,
								mbSampleData = JSON.parse(item.mbSampleData);
							// 移动端全体模版都有对style的修改，以及头部对boxstyle有修改，需要保存
							mbSampleData.style = item.sampleData.style || {};
							if (item.sampleData.name == 'Header') mbSampleData.boxStyle = item.sampleData.boxStyle || {};
							mbSampleData.outer = await this.filterFun(outer);
							mbSampleData.list = await this.filterFun(list);
							// 需要给请求参数添加mbSampleData
							tempArr[index].mbSampleData = JSON.stringify(this.$store.getters.deepMerge(
								[JSON.parse(this.pageRowDraftList[index].mbSampleData)],
								[mbSampleData]
							)[0])
							tempArr[index].isTranslate = item.isTranslate || 0
						})
					)
				}
				temp1.push(tempArr.shift())
				temp1.push(tempArr.pop())
				// 进行强校验，防止页头页尾不为同一个
				let flag1 = temp1.every((item, ind) => item.id == this.pageHeadFooterId[ind])
				if (!flag1) {
					localStorage.setItem('errorInfo', JSON.stringify({ temp1, pageRowDraftList: this.pageRowDraftList, pageRowDraftListMB: this.pageRowDraftListMB, tempArr }))
					throw (new Error('ERROR:PageTemplates Incorrect'))
				}
				this.sysProOwnerDTO = { ...this.$store.state.proSystem }
				let postData = {
					id: this.headerFooterId,
					proId: this.proId,
					pageRowDraftList: temp1,
					sysProOwnerDTO: this.sysProOwnerDTO,
					platform: this.manageMobile ? 2 : 1
				}
				let postData2 = {
					id: this.contentId,
					proId: this.proId,
					pageRowDraftList: tempArr,
					platform: this.manageMobile ? 2 : 1
				}
				this.saveRequest(postData, postData2)
			}
		},

    // 保存时候发送的请求
   async saveRequest (postData, postData2) {
      let postData3 = {
        proId: this.proId,
        pageIdList: this.editPageThemeFontData.pageIdList,
        websiteStyleIdDraft: this.pageInfo.websiteStyleIdDraft,
        fontDraft: this.editPageThemeFontData.fontDraft,
        themeColorIdDraft: this.editPageThemeFontData.themeColorIdDraft,
        isGlobal: this.editPageThemeFontData.isGlobal
      }
	  let postData4 = {
		proId: this.proId,
		proLogo: this.sysProOwnerDTO.logo,
		proLogoFooter: this.sysProOwnerDTO.logoFooter
	  }
	  const res = await editPagesAllSample(postData)
	  const res1 = await editPagesAllSample(postData2)
	  const res2 = await editPageThemeFont(postData3)
	  //该接口含有对原始logo的保存，需要在setProLogo接口之前调用
	  const res3 = await setProLogo(postData4)
	  if(res.code == 200 && res1.code == 200 && res2.code == 200 && res3.code == 200){
		this.$toast.success(res.message);
        location.reload();
	  }
    },

    // 更改翻译语言
    changeLng(val) {
      if (val) {
		let languageCode
		if(val.language && val.countryCode !== undefined){
			languageCode = val.language + '-' + val.countryCode
		}else{
			languageCode = "en-us"
		}
		this.computeLang = val;
		const newQuery = { ...this.$route.query, language: languageCode };
        this.$router.push({ path: '/manage/siteEdit/buildWeb', query: newQuery });
      }
    },

    // 给语言选择合适值、
	chooseLan(val = "en-us",type = 1) {
	  const store = this.$route.query.language || null
      let lngCom, tempLanguage;
      if (store) {
        lngCom = store;
        tempLanguage = this.pageInfo.languageList.find((x) => {
          const combo = x.language + "-" + x.countryCode;
          return combo == lngCom;
        });
        if (!tempLanguage) {
          tempLanguage = this.pageInfo.languageList.find((x) => {
            const combo = x.language + "-" + x.countryCode;
            return combo == "en-us";
          });
          if (!tempLanguage) (tempLanguage = this.pageInfo.languageList[0]) || {}
        }
        localStorage.setItem('bwLng', JSON.stringify(tempLanguage))
      } else {
        tempLanguage = this.pageInfo.languageList.find((x) => {
          const combo = x.language + "-" + x.countryCode;
          return combo == val;
        });
		if(!!tempLanguage){
			lngCom = val
			localStorage.setItem('bwLng', JSON.stringify(tempLanguage))
		}else{
			lngCom = this.pageInfo.languageList[0].language + '-' + this.pageInfo.languageList[0].countryCode
			localStorage.setItem('bwLng', JSON.stringify(this.pageInfo.languageList[0]))
		}
		if(type == 1){
			let newQuery = { ...this.$route.query, language: lngCom };
            this.$router.push({ path: '/manage/siteEdit/buildWeb', query: newQuery });
		}
      }
      this.computeLang = tempLanguage;
    },

    listAllColor() {
      listAllColor().then((res) => {
        this.listAllColorData = res.data;
      });
    },

    onscrollHiddenToolBar() {
      let targetList = document.querySelectorAll(".mce-edit-focus");
      targetList.forEach((item) => {
        item.blur();
      });
    },
    getScale() {
      let contentDom = document.querySelector("#content");

      if (!contentDom) return;
      else if (this.manageMobile) {
        contentDom.style.width = "auto";
        contentDom.style.height = "auto";
        contentDom.style.transform = `scale(1)`;
      } else {
        contentDom.style.width = "100vw";
        contentDom.style.height = "100vh";
        let divWidth = contentDom.offsetWidth - 10 - 421; //24 padding  421 navigation
        let windowWidth = document.documentElement.clientWidth;
        contentDom.style.transform = `scale(${divWidth / windowWidth})`;
      }
    },
	async getLangTransList (flag, val) {
		this.languageFlagList = []
		if (flag) {
			let params = {
				proId: this.proId,
				id: val.id
			}
			const { data } = await getPageRowEditTagById(params)
			if (data && data.length > 1) {
				this.languageFlagList = data
				this.lanFlag = true
			}
		} else {
			this.lanFlag = false
		}
	},
	async operateToolTip(message){
		if(!this.moveFlag){
			await this.$messageBox.show({
				title: 'WARNING',
				content: 'Moving, deleting, and adding will cause the mobile version to reset. Please operate with caution.'
				,confirmText: 'Next'
			}).then(() => {
				this.moveFlag = true
			}).catch(() => {
				this.$message.success(message)
			})
		}
	},
	newChildCardHandler(v,hoverType){
		let parent,t1,flag = this.pageRowDraftList[v.parentIndex].sampleData.name=='Twins'&& this.$store.state.twinsLeftOrRight
		let index = v.childIndex*1
		if(flag){
			index = index + 1
			parent = this.pageRowDraftList[v.parentIndex].sampleData[this.$store.state.twinsLeftOrRight];
		}else parent = this.pageRowDraftList[v.parentIndex].sampleData[hoverType];
		t1 = parent[index];
		  // 如果编辑了，并且没翻译新增一个参数
		this.pageRowDraftList[v.parentIndex].isSyncMb = 1
		if(this.pageRowDraftList[v.parentIndex].mbSampleData){
			let mb = JSON.parse(this.pageRowDraftList[v.parentIndex].mbSampleData),mbData
			if(flag)mbData = mb[this.$store.state.twinsLeftOrRight]
            else mbData = mb[hoverType]
			if(mbData && mbData.length > 0){
				if(parent.length>mbData.length){
					let arr = new Array(parent.length-mbData.length).fill(null).map(()=>new Object)
					mbData = mbData.concat(arr)
				}
				let temp = mbData[index]
				mbData.splice(index, 0, JSON.parse(JSON.stringify(temp)))
				this.pageRowDraftList[v.parentIndex].mbSampleData = JSON.stringify(mb)
			}
		}
		// if(this.pageRowDraftList[v.parentIndex].mbSampleStyle){
		// 	let mb = JSON.parse(this.pageRowDraftList[v.parentIndex].mbSampleStyle),mbStyle
		// 	if(flag)mbStyle = mb[this.$store.state.twinsLeftOrRight]
        //     else mbStyle = mb[hoverType]
		// 	if(mbStyle && mbStyle.length > 0){
		// 		if(parent.length>mbStyle.length){
		// 			let arr = new Array(parent.length-mbStyle.length).fill(null).map(()=>new Object)
		// 			mbStyle = mbStyle.concat(arr)
		// 		}
		// 		if(flag){
		// 			let temp = mbStyle[v.childIndex*1 + 1] || {}
		// 			mbStyle.splice(v.childIndex*1 + 1, 0, JSON.parse(JSON.stringify(temp)))
		// 		}else{
		// 			let temp = mbStyle[v.childIndex] || {}
		// 			mbStyle.splice(v.childIndex, 0, JSON.parse(JSON.stringify(temp)))
		// 		}
		// 		this.pageRowDraftList[v.parentIndex].mbSampleStyle = JSON.stringify(mb)
		// 	}
		// }
		let temp = JSON.parse(JSON.stringify(t1));
		parent.splice(index, 0, temp);
		if(hoverType == 'imgOuter'){
			this.newChildCardHandler(v,'titleList')
		}
	}
  },

  mounted() {
    // this.listAllLanguage();
    //获取所有语言，需要在其余接口调用时获取正确的语言数据，加载到接口请求拦截器。
    getLanguageByProId().then((res) => {
      let arr = [];
      res.data.forEach((item) => {
        item.childList.forEach((x) => {
          arr.push(x);
        });
      });
      this.pageInfo.languageList = arr;
      this.chooseLan();
      this.listAllColor();
      this.fontCssChangeFun();
      this.listAllPagesFun();
    });
    let that = this;
    const scrollview = this.$refs["scrollBox"];
    scrollview.addEventListener("scroll", this.onscrollHiddenToolBar, true);

    this.getScale();
    window.onresize = function () {
      that.getScale();
    };
    this.$nextTick(() => {
      this.$store.commit("setBuildWebLoading", false);
    });
	//接受iframe数据
    window.addEventListener(
      "message",
      (e) => {
        let obj = e.data;
        if (obj.type == "click-banner") {
          this.clickFun(null, obj.item, obj.index);
        } else if (obj.type == "click-img") {
          this.clickFun(null, obj.item, obj.index);
        } else if (obj.type == "MB-list") {
          this.pageRowDraftListMB = obj.item;
        } else if (obj.type == "MB-Translate") {
			this.getLangTransList(obj.flag.isTranslate,obj.flag)
			this.pageRowDraftListMB = obj.item;
		} else if (obj.type == "click-product") {
          this.clickFun(null, obj.item, obj.index);
        } else if (obj.type == "mouseover-list") {
	      //移动端的移动事件
			if(window.frames.iframeMB && window.frames.iframeMB.document && this.manageMobile){
				let iframeObj = {}
				iframeObj.target = window.frames.iframeMB.document.querySelector('* [data-mouseover]')
				this.mouseoverFun(iframeObj,obj.index,obj.item);
			}
		} else if (obj.type =="click-navList"){
			this.clickFun(null, obj.item, obj.index);
		} else if (obj.type =="click-imgList"){
			this.clickFun(null, obj.item, obj.index);
		}
      },
      false
    );
    //上
    this.$Bus.$on("minusCardIndex", (v) => {
      this.panels = "";
      v = Number(v);
      if (v == 0) {
        return;
      } else if (v - 1 == 0) {
        return;
      } else if (
        this.pageRowDraftList[v - 1].sampleData.name == "Banner" ||
        this.pageRowDraftList[v - 1].sampleData.name == "BannerTemplate1"
      ) {
        return;
      } else {
        let t1 = this.pageRowDraftList[v];
        this.pageRowDraftList.splice(v, 1);
        this.pageRowDraftList.splice(v - 1, 0, t1);
        this.killTools();
      }
    });
    //下
    this.$Bus.$on("plusCardIndex", (v) => {
      this.panels = "";
      v = Number(v);
      if (v == this.pageRowDraftList.length) {
        return;
      } else if (v + 2 == this.pageRowDraftList.length) {
        return;
      } else if (
        this.pageRowDraftList[v + 2].sampleData.name == "Banner" ||
        this.pageRowDraftList[v + 2].sampleData.name == "BannerTemplate1"
      ) {
        return;
      } else {
        let t1 = this.pageRowDraftList[v];
        this.pageRowDraftList.splice(
          v + 2 > this.pageRowDraftList.length
            ? this.pageRowDraftList.length
            : v + 2,
          0,
          t1
        );
        this.pageRowDraftList.splice(v, 1);
        this.killTools();
      }
    });
    this.$Bus.$on("leftCardIndex", async (v) => {
		await this.operateToolTip('Cancel MOVING')
		if(!this.moveFlag) return
		this.panels = "";
		if (v.childIndex == 0)  return;
		else {
			try {
				let parent,t1,flag = this.pageRowDraftList[v.parentIndex].sampleData.name=='Twins'&& this.$store.state.twinsLeftOrRight
				let childIndex = Number(v.childIndex)
				if(flag){
					childIndex = childIndex + 1
					parent = this.pageRowDraftList[v.parentIndex].sampleData[this.$store.state.twinsLeftOrRight];
				}else{
					parent = this.pageRowDraftList[v.parentIndex].sampleData[this.hoverList]
				}
				t1 = parent[childIndex];
				this.pageRowDraftList[v.parentIndex].isSyncMb = 1
				if(this.pageRowDraftList[v.parentIndex].mbSampleData){
					let mb = JSON.parse(this.pageRowDraftList[v.parentIndex].mbSampleData),mbData
					if(flag)mbData = mb[this.$store.state.twinsLeftOrRight]
            		else mbData = mb[this.hoverList]
					if (mbData && mbData.length > 0) {
						// 当pc大于mb长度时，需要用{}补齐t2
						if(parent.length>mbData.length){
							let arr = new Array(parent.length-mbData.length).fill(null).map(()=>new Object)
							mbData = mbData.concat(arr)
						}
						let t2 = mbData[childIndex]
						if (t2) {
							mbData.splice(Number(childIndex), 1);
							mbData.splice(Number(childIndex - 1), 0, t2);
							this.pageRowDraftList[v.parentIndex].mbSampleData = JSON.stringify(mb)
						}
					}
				}
				// if(this.pageRowDraftList[v.parentIndex].mbSampleStyle){
				// 	let mb = JSON.parse(this.pageRowDraftList[v.parentIndex].mbSampleStyle)
				// 	if (mb[this.hoverList] && mb[this.hoverList].length > 0) {
				// 		if(parent.length>mb[this.hoverList].length){
				// 			let arr = new Array(parent.length-mb[this.hoverList].length).fill(null).map(()=>new Object)
				// 			mb[this.hoverList] = mb[this.hoverList].concat(arr)
				// 		}
				// 		let t2 = mb[this.hoverList][v.childIndex];
				// 		if (t2) {
				// 			mb[this.hoverList].splice(Number(v.childIndex), 1);
				// 			mb[this.hoverList].splice(Number(v.childIndex - 1), 0, t2);
				// 			this.pageRowDraftList[v.parentIndex].mbSampleStyle = JSON.stringify(mb)
				// 		}
				// 	}
				// }
				parent.splice(childIndex, 1);
				parent.splice(childIndex - 1, 0, t1);
				this.killTools();
			} catch (error) {
				this.$message.info('切换失败，请及时与开发中心联系')
			}
		}
	});
    this.$Bus.$on("rightCardIndex", async (v) => {
		await this.operateToolTip('Cancel MOVING')
		if(!this.moveFlag) return
		this.panels = "";
		let flag = this.pageRowDraftList[v.parentIndex].sampleData.name=='Twins'&& this.$store.state.twinsLeftOrRight
		if (!flag&&(v.childIndex == this.pageRowDraftList[v.parentIndex].sampleData[this.hoverList].length - 1)) return;
		else if(flag&&v.childIndex == this.pageRowDraftList[v.parentIndex].sampleData[this.$store.state.twinsLeftOrRight].length - 2) return
		else {
			try {
				let childIndex = Number(v.childIndex),parent,t1
				if(flag){
					childIndex = childIndex + 1
					parent = this.pageRowDraftList[v.parentIndex].sampleData[this.$store.state.twinsLeftOrRight];
				}else{
					parent = this.pageRowDraftList[v.parentIndex].sampleData[this.hoverList];
				}
				t1 = parent[childIndex];
				this.pageRowDraftList[v.parentIndex].isSyncMb = 1
				if(this.pageRowDraftList[v.parentIndex].mbSampleData){
					let mb = JSON.parse(this.pageRowDraftList[v.parentIndex].mbSampleData),mbData
					if(flag)mbData = mb[this.$store.state.twinsLeftOrRight]
					else mbData = mb[this.hoverList]
					if(mbData && mbData.length > 0){
						if(parent.length>mbData.length){
							let arr = new Array(parent.length-mbData.length).fill(null).map(()=>new Object)
							mbData = mbData.concat(arr)
						}
						let t2 = mbData[childIndex];
						if(t2){
							mbData.splice(childIndex + 2 > parent.length ? parent.length : childIndex + 2,0,t2);
							mbData.splice(childIndex,1);
							this.pageRowDraftList[v.parentIndex].mbSampleData = JSON.stringify(mb)
						}
					}
				}
				// 新增模版时使用的是模版数据的mbSampleStyle字段，后改新增方法，试行两个月无误,删
				// if(this.pageRowDraftList[v.parentIndex].mbSampleStyle){
				// 	let mb = JSON.parse(this.pageRowDraftList[v.parentIndex].mbSampleStyle)
				// 	if(mb[this.hoverList] && mb[this.hoverList].length > 0){
				// 		if(parent.length>mb[this.hoverList].length){
				// 			let arr = new Array(parent.length-mb[this.hoverList].length).fill(null).map(()=>new Object)
				// 			mb[this.hoverList] = mb[this.hoverList].concat(arr)
				// 		}
				// 		let t2 = mb[this.hoverList][v.childIndex];
				// 		if(t2){
				// 			mb[this.hoverList].splice(childIndex + 2 > parent.length ? parent.length : childIndex + 2,0,t2);
				// 			mb[this.hoverList].splice(childIndex,1);
				// 			this.pageRowDraftList[v.parentIndex].mbSampleStyle = JSON.stringify(mb)
				// 		}
				// 	}
				// }
				parent.splice(childIndex + 2 > parent.length ? parent.length : childIndex + 2,0,t1);
				parent.splice(childIndex,1);
				this.killTools();
			} catch (error) {
				this.$message.info('Error:Please contact the development center')
			}
		}
	});
    //根据子组件tools传来的index 给modalData添加一个临时template
    this.$Bus.$on("newCardBeforeFun", async (v) => {
      this.itemData = {
        no: true,
      };
      this.panels = "";
      this.templateClickIndex = v;
      // this.panels = [6];
      this.pageRowDraftList.splice(v, 0, this.templateDom);
      this.stepRecord();
    });
    this.$Bus.$on("newCardAfterFun", async (v) => {
      this.itemData = {
        no: true,
      };
      this.panels = "";
      this.templateClickIndex = v;
      this.pageRowDraftList.splice(Number(v) + 1, 0, this.templateDom);
      this.stepRecord();
    });

	// 新增小item
	this.$Bus.$on("newChildCardIndex",async (v) => {
		await this.operateToolTip('Cancel ADDING')
		if(!this.moveFlag) return
		try {
		  this.panels = "";
		  this.newChildCardHandler(v,this.hoverList)
		} catch (error) {
		  this.$message.error('Error:Addition Failed')
		}
	  });

	this.$Bus.$on("BpsFaqAdd", async (v) => {
		await this.operateToolTip('Cancel ADDING')
		if(!this.moveFlag) return
		try {
		  this.panels = "";
		  let tempIndex = v.childIndex.split('_')
		  let parent = this.pageRowDraftList[v.parentIndex].sampleData['titleList'];
		  let t = parent[tempIndex[0]]['list']
		  let t1 = t[tempIndex[1]];
		  // 如果编辑了，并且没翻译新增一个参数
		  this.pageRowDraftList[v.parentIndex].isSyncMb = 1
		  let temp = JSON.parse(JSON.stringify(t1));
		  t.splice(tempIndex[1], 0, temp);
		} catch (error) {
		  this.$message.error('Error:Addition Failed')
		}
    });

	// 删除小item触发
    this.$Bus.$on("delChildCardIndex", (v) => {
      this.panels = "";
      this.v = v;
      this.deleteChildDialog = true;
    });

	//添加角标
	this.$Bus.$on("addCornerLabel",async(v)=>{
		this.itemData = this.hoverItem;
		this.hoverIndex = this.allIndex;
		this.panels = "";
		this.$refs.mNavigation.addCornerLabel(v);
	})

	this.$Bus.$on("uploadDownload",async(v)=>{
		this.itemData = this.hoverItem;
		this.hoverIndex = this.allIndex;
		this.panels = "";
		this.$refs.mNavigation.uploadDownload(v);
	})

    //复制 移动 隐藏 删除
    this.$Bus.$on("changeSelectedItem", (v) => {
      this.itemData = this.hoverItem;
      this.hoverIndex = this.allIndex;
	  this.eventType = 'mouseover'
	  //当移动端时把hoverindex传递给iframe
	  if(this.manageMobile) this.getIframeMBDataFun(this.hoverIndex, "SET_HOVERITEMDATA")
      this.templateClickIndex = this.allIndex;
      this.$nextTick(() => {
        try {
          this.panels = "";
          let copy = JSON.parse(JSON.stringify(this.pageRowDraftList));
          this.tt = copy[this.hoverIndex];
          this.tt.randomKey = Math.random();
          this.tt.id = null;
          if (v.title == "Duplicate Section") {
            this.pageRowDraftList.splice(this.hoverIndex, 0, this.tt);
            // setTimeout(() => {}, 1000);
          } else if (v.title == "Move Section") {
			this.filterPage(this.selectPageFilter,false)
            this.dialog3 = true;
            // this.pageRowDraftList.splice(this.hoverIndex, 0);
          } else if (v.title == "Hide Section") {
            // this.dialog2 = true;
			this.$refs.mNavigation.hideSectionHandler();
          } else if (v.title == "Delete Section") {
            this.deleteParentDialog = true;
          } else if (v.title == "Replace Section") {
            this.hasExpansionPanels();
          } else if (v.title == "New Banner") {
            this.hasExpansionPanels('newBanner');
          }else if (v.title == "Edit Background") {
            this.$refs.mNavigation.cleanColorPickerFun();
            this.expansionPanels = [];
          } else if (v.title == "Edit Font Color") {
            this.$refs.mNavigation.cleanFontColorPickerFun();
            this.expansionPanels = [];
          } else if (v.title == "Edit Tip Background"){//修改header区域的颜色
			this.$refs.mNavigation.cleanColorPickerFun(true);
            this.expansionPanels = [];
		  } else if (v.title == "Edit Tip Font Color") {
            this.$refs.mNavigation.cleanFontColorPickerFun(true);
            this.expansionPanels = [];
          } else if (v.title == "Edit Padding") {
            this.$refs.mNavigation.editPadding();
            this.expansionPanels = [];
          }
        } catch (error) {
          console.log(error);
        }
      });
    });
    this.$Bus.$on("showFun", (v) => {
      this.panels = "";
      if (v.title == "Show") {
        if (this.hoverIndex) {
          this.pageRowDraftList[this.hoverIndex].isHidden = false;
          this.pageRowDraftList[this.hoverIndex].isHiddenLang = false;
        }
      } else if (v.title == "Delete") {
        this.deleteParentDialog = true;
      }
    });
  },

  created() {
    this.proId = this.$store.getters["manage/getProId"];
    this.queryId = this.$route.query.id;
    this.queryType = this.$route.query.type;
  }
};
</script>
<style scoped lang="scss">
.mask {
	height: 100vh;
	max-height: 1500px;
	align-items: center;
	justify-content: center;
	background: rgba($color: black, $alpha: 0.6);
	position: fixed;
	z-index: 6000;
	right: 0;
	left: 0;
	top: 0;
}

.mask-content {
	width:auto;
	max-height: 95vh;
	position: relative;
	::v-deep>.icon-jxsht-zdgl-xx {
		color: white;
		font-size: 2.5em;
		background-image: radial-gradient(#333 50%, transparent 0);
		position: absolute;
		right: -1.3em;
		top: -0.3em;
		z-index: 999;
	}
	>img,
	>video {
		width: auto;
		margin: auto;
		display: block;
		min-width: 30vw;
		max-width: 90vw;
		min-height: 50vh;
		max-height: 90vh;
	}
}
.addCard {
  height: 100%;
  padding: 3.6458vw 2.6042vw 2vw;

  .addFrom {
    .del {
      display: flex;
      justify-content: flex-end;
      position: relative;
      bottom: 1.5625vw;
    }

    ul {
      padding: 0;
      margin: 0;
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      grid-gap: 0 2.0833vw;

      li {
        list-style: none;
        line-height: 25px;
      }

      .gg {
        grid-column-start: 1;
        grid-column-end: 3;
      }

      .addBtn {
        text-align: center;

        .v-btn {
          width: 150px;
          height: 50px;
          font-size: 20px;
          margin: 0 20px;
        }
      }
    }
  }
}

::v-deep .card-box {
  min-width: fit-content;

  .content {
    min-width: 9em;
  }
}

::v-deep .part2:not(.isSwiper) {
  position: relative;
}

::v-deep .myRadio input {
  display: none;
}

.custom-dialog {
  background-color: black;

  .dash-border {
    border: 1px dashed #cccccc;
  }

  .custom-btn1 {
    width: 176px;
    height: 40px;
    background: #1a73e8;
    border-radius: 10px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 16px;
    font-family: Roboto;
    font-weight: 400;
    color: #ffffff;
  }
}

.w-100 {
  width: 100%;
}

@layer {
  .hover-tag {
    border: 2px solid transparent;

    &:hover {
      border-color: #1a73e8 !important;
    }
  }
}

.out {
  ::v-deep a {
    color: inherit;

    &[primary] {
      color: white !important;
    }
  }

  ::v-deep p {
    margin: 0;
  }

  ::v-deep .register,
  ::v-deep .login,
  ::v-deep a,
  #preview div {
    pointer-events: none;
  }

  .custom-scrollbar {
    >.col {
      max-height: 100vh;
      overflow-x: hidden;
      transition: max-width 0.3s;

      >.content {
        padding-left: 10px;
        transform-origin: 0 0;

        ::v-deep .v-card {
          color: $text-primary;
          background-color: $bg-page;
          font-family: $text-family, Roboto, Arial, serif;
        }
      }
    }

    .justLine {
      background: repeating-linear-gradient(135deg, #d6d6d6, #d6d6d6 15px, white 0, white 30px);
    }

    ::v-deep .banner {
      .content {
        left: 5%;
        top: 50%;
        transform: translateY(-50%) scale(0.8);
      }

      .edit-text[contenteditable="true"] {
        display: block;
        max-width: 300px;
        max-height: 100px;
        min-height: 2em;
        overflow: hidden;
      }
    }

    ::v-deep .position-relative {
      position: relative;
    }

    ::v-deep .position-absolute {
      position: absolute;
    }

    ::v-deep footer>div {
      position: relative;
    }

    ::v-deep .content {
      .tools {
        min-height: 15px;
        position: relative;

        .transFlag {
          position: absolute;
          margin: 10px;
          font-size: 18px;
          z-index: 999;
          padding: 0 3px;
          background-color: #ccc;
          color: #fff;
          border-radius: 5px;
          transition: 0.3s ease;

          &:hover {
            transform: scale(1.2);
          }
        }
      }

      .hover-tag {
        transition: all 0.3s;

        .template {
          background-color: #d9e6f7;
          padding: 14px;
          height: 220px;

          >div {
            border: 1px dashed #9fbee6;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
          }
        }
      }

      .hover-tag.borderWhite {
        border-color: white;
      }

      .item {
        position: relative;

        .grid-box {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(200px, 380px));
          justify-content: space-around;
          gap: 20px;
        }
      }
    }

    ::v-deep button:hover {
      transform: none;
    }
  }

  img {
    object-fit: contain;
  }

  p {
    margin: 0;
  }

  ::v-deep .blue--text {
    color: #1a73e8 !important;
    caret-color: #1a73e8 !important;
  }

  ::v-deep .blue.lighten-1 {
    background-color: #1a73e8 !important;
    border-color: #1a73e8 !important;
  }

  //右侧选项卡
  .mNavigation {
    .m-active-class {
      .v-btn:before {
        opacity: 0;
      }

      .v-btn:after {
        background-color: #1a73e8;
        border-radius: inherit;
        bottom: -5px;
        color: inherit;
        content: "";
        pointer-events: none;
        position: absolute;
        width: 100%;
        height: 2px;
        transition: opacity 0.2s cubic-bezier(0.4, 0, 0.6, 1);
      }
    }

    ::v-deep .m-switch-class {
      .v-input__control {
        .v-input__slot {
          display: flex;

          .v-input--selection-controls__input {
            order: 2;
          }

          .v-label {
            order: 1;
          }
        }
      }
    }

    ::v-deep .m-chip-class {
      .v-chip__content {
        justify-content: center;
      }

      .v-chip.v-size--default {
        width: 32px;
      }

      .v-chip:before {
        opacity: 0;
      }

      .v-icon {
        opacity: 0;
      }

      .m-chip-acitve {
        .v-icon {
          opacity: 1;
        }
      }
    }

    ::v-deep .btn-updown {
      .v-btn__content {
        flex-direction: column;
      }
    }
  }
}
</style>
