<template>
	<div class="RadioBox" :class="{ active: active }" :style="{ backgroundColor: backgroundColor }">
		<div class="point">
			<div class="inner-point"></div>
		</div>
		<div class="textWrap">
			<div flex>
				<div class="point2"></div>
				<p class="normal-text">{{ paramName }}</p>
			</div>
		</div>
		<div>
			<template v-if="onlyAddInquiry !== 1 && paramData && paramData.priceInfo">
				<PriceText :paramData="paramData" :sizeValue="sizeValue">
					<template slot="unit">
						<slot> </slot>
					</template>
				</PriceText>
			</template>
		</div>
	</div>
</template>

<script>
import PriceText from "@/components/Quote/PriceText";

export default {
	name: "RadioBox",
	props: ["paramName", "paramData", "sizeValue", "backgroundColor", "active"],
	computed: {
		onlyAddInquiry() {
			return this.paramData?.onlyAddInquiry;
		},
	},
	components: {
		PriceText,
	},
};
</script>

<style scoped lang="scss">
.RadioBox {
	padding: 10px 5px;

	@media screen and (max-width: 767px) {
		padding: 6px 5px;
	}

	.point {
		display: none;
		position: absolute;
		left: 50%;
		bottom: 0;
		transform: translate(-50%, 100%);
		width: 28px;
		height: 15px;
		border: 1px solid #e9ecf0;
		border-top: none;
		background: #edf1f5;
		border-radius: 0 0 16px 16px;
		z-index: 10;
		transition: all 0.3s;

		.inner-point {
			position: absolute;
			left: 50%;
			top: 0;
			transform: translate(-50%, -50%);
			width: 18px;
			height: 18px;
			border-radius: 50%;
			background: #ffffff;
			border: 1px solid #aaaeb3;
			transition: all 0.3s;

			&:after {
				content: "";
				position: absolute;
				left: 50%;
				top: 50%;
				transform: translate(-50%, -50%);
				width: 6px;
				height: 6px;
				background-color: #aaaeb3;
				border-radius: 50%;
				transition: all 0.3s;
			}
		}
	}

	.textWrap {
		> div {
			display: flex;
			justify-content: center;
			align-items: center;

			.point2 {
				display: flex;
				justify-content: center;
				align-items: center;
				min-width: 18px;
				width: 18px;
				height: 18px;
				border: 1px solid #afb1b3;
				border-radius: 50%;
				margin-right: 10px;
				background-color: #fff;
				transition: all 0.3s;
				@media screen and (max-width: 767px) {
					margin-right: 3px;
				}

				&::after {
					content: "";
					width: 6px;
					height: 6px;
					background: #d4d7d9;
					border-radius: 50%;
				}
			}
		}
	}

	::v-deep .tip-text {
		text-align: center;
	}

	::v-deep .normal-text {
		text-align: center;
		white-space: nowrap;
	}

	&.active {
		.textWrap {
			.point2 {
				border-color: $color-primary;
				background: $color-primary;

				&::after {
					background: #fff;
				}
			}
		}
	}
}
</style>