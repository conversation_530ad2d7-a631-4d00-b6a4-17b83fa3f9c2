<template>
	<div class="DialogBM">
		<div class="textWrap">
			<div class="ad" v-if="dialogItem.starJson">
				<div class="ad-item" v-for="(citem, cindex) in JSON.parse(dialogItem.starJson)" :key="cindex">
					<span>{{ citem.key }}:</span>
					<Star v-model="citem.value"></Star>
					<strong>{{ citem.value }}</strong>
				</div>
			</div>
			<div>
				<div class="point3 rounded-point"></div>
				<template v-if="dialogItem.isRushCate === 1">
					<p class="normal-text">
						{{ dialogItem.cateName ? dialogItem.cateName : dialogItem.alias }}<span>({{ lang.hours }})</span>
					</p>
				</template>
				<template v-else>
					<p class="normal-text">
						{{ dialogItem.cateName ? dialogItem.cateName : dialogItem.alias }}
					</p>
				</template>
			</div>
		</div>
		<div>
			<QuoteBtn @click.native="dialogNextStep">{{ lang.ConfirmNextStep }} </QuoteBtn>
		</div>
	</div>
</template>

<script>
import QuoteBtn from "@/components/Quote/QuoteBtn";
import Star from "@/components/Quote/Star";

export default {
	name: "DialogBM",
	props: ["dialogItem"],
	components: {
		QuoteBtn,
		Star,
	},
	computed: {
		lang() {
			return this.$store.getters.lang.quote || {};
		},
	},
	methods: {
		dialogNextStep() {
			this.$emit("dialogNextStep");
		},
	},
};
</script>

<style scoped lang="scss">
.DialogBM {
	display: flex;
	align-items: center;
	flex-direction: column;
	width: 100%;
	justify-content: space-between;
	padding: 10px;
	@media screen and (max-width: 767px) {
		padding: 0 0 10px 0;
		flex-direction: column;
		justify-content: center;
	}

	.textWrap {
		flex-direction: column;
		cursor: pointer;
		margin-top: 0;
		margin-bottom: 10px;
		font-size: 14px;
		@media screen and (max-width: 767px) {
			margin-top: 10px;
		}

		& > div {
			display: flex;
			align-items: center;
			justify-content: center;
			@media screen and (max-width: 767px) {
				justify-content: center;
			}
		}

		p {
			color: $color-primary;
		}

		.ad {
			margin-bottom: 8px;

			.ad-item {
				display: flex;
				align-items: center;
				border-right: 1px solid #ccc;
				color: #333333;

				&:last-child {
					border-right: none;
				}
			}

			.ad-item:not(:first-child) {
				padding: 0 10px;
			}

			.ad-item:first-child {
				padding-right: 10px;
			}
		}

		.normal-text {
			font-size: 24px;
			font-weight: bold;
			@media screen and (max-width: 767px) {
				font-size: 12px;
			}
		}

		.point3 {
			display: flex;
			justify-content: center;
			align-items: center;
			width: 18px;
			height: 18px;
			min-width: 18px;
			border: 1px solid $color-primary;
			background-color: $color-primary;
			margin-right: 6.5px;
			transition: all 0.3s;
			position: relative;
			border-radius: 50%;

			&::after {
				background: white;
				position: absolute;
				content: "";
				left: 50%;
				top: 50%;
				transform: translate(-50%, -50%);
				height: 6px;
				width: 6px;
				border-radius: 50%;
			}
		}
	}
}
</style>