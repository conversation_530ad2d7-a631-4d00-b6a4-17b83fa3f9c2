<template>
	<div class="DialogBM">
		<div class="textWrap">
			<div class="ad" v-if="starJson.length">
				<div class="ad-item" v-for="(citem, cindex) in starJson" :key="cindex">
					<span>{{ citem.key }}:</span>
					<Star v-model="citem.value"></Star>
					<strong>{{ citem.value }}</strong>
				</div>
			</div>
			<div>
				<CustomCircle :isActive="true" :circle-type="1"> </CustomCircle>
				<template v-if="dialogItem.isRushCate === 1">
					<p class="normal-text">
						{{ dialogItem.cateName ? dialogItem.cateName : dialogItem.alias }}<span>({{ lang.hours }})</span>
					</p>
				</template>
				<template v-else>
					<p class="normal-text">
						{{ dialogItem.cateName ? dialogItem.cateName : dialogItem.alias }}
					</p>
				</template>
			</div>
		</div>
		<div>
			<QuoteBtn @click.native="dialogNextStep">{{ lang.ConfirmNextStep }} </QuoteBtn>
		</div>
	</div>
</template>

<script>
import QuoteBtn from "@/components/Quote/QuoteBtn";
import Star from "@/components/Quote/Star";
import CustomCircle from "@/components/Quote/customCircle.vue";

export default {
	name: "DialogBM",
	props: ["dialogItem"],
	components: {
		CustomCircle,
		QuoteBtn,
		Star,
	},
	computed: {
        starJson(){
            let starJson = this.dialogItem.starJson;
            if(!starJson){
                return  []
            }
            try {
                starJson = (JSON.parse(this.itemData.starJson)).filter(item=>item.value);
                return starJson
            }catch (e) {
                return  []
            }
        },
		lang() {
			return this.$store.getters.lang.quote || {};
		},
	},
	methods: {
		dialogNextStep() {
			this.$emit("dialogNextStep");
		},
	},
};
</script>

<style scoped lang="scss">
.DialogBM {
	display: flex;
	align-items: center;
	flex-direction: column;
	width: 100%;
	justify-content: space-between;
	padding: 10px;

	.customCircle1{
		width: 1.4em;

		@include respond-to(mb){
			width: 1em;
		}
	}

    .gs-quote-button{
        @include respond-to(mb){
            min-width: 175px;
        }
    }

	@media screen and (max-width: 767px) {
		padding: 0 0 10px 0;
		flex-direction: column;
		justify-content: center;
	}

	.textWrap {
		flex-direction: column;
		cursor: pointer;
		margin-top: 0;
		margin-bottom: 10px;
		font-size: 14px;
		@media screen and (max-width: 767px) {
			margin-top: 10px;
		}

		& > div {
			display: flex;
			align-items: center;
			justify-content: center;
			@media screen and (max-width: 767px) {
				justify-content: center;
			}
		}

		p {
			color: $color-primary;
		}

		.ad {
			margin-bottom: 8px;

			.ad-item {
				display: flex;
				align-items: center;
				border-right: 1px solid #ccc;
				color: #333333;

				&:last-child {
					border-right: none;
				}
			}

			.ad-item:not(:first-child) {
				padding: 0 10px;
			}

			.ad-item:first-child {
				padding-right: 10px;
			}
		}

		.normal-text {
			font-size: 24px;
			font-weight: bold;
			@media screen and (max-width: 767px) {
				font-size: 12px;
			}
		}
	}
}
</style>
