const moveImgBase64 = "data:image/png;base64,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";
const deleteImgBase64 = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyJpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuMy1jMDExIDY2LjE0NTY2MSwgMjAxMi8wMi8wNi0xNDo1NjoyNyAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENTNiAoV2luZG93cykiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6QzMzNTREQzI1OTgzMTFFQjkwRDRBQjhFNDgxM0YyNzUiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6QzMzNTREQzM1OTgzMTFFQjkwRDRBQjhFNDgxM0YyNzUiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDpDMzM1NERDMDU5ODMxMUVCOTBENEFCOEU0ODEzRjI3NSIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDpDMzM1NERDMTU5ODMxMUVCOTBENEFCOEU0ODEzRjI3NSIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/PnysnwwAAAN3SURBVHjarFbNaxNREJ/9SJNoYkINemlTW7SiokX8gHoQDz0JHooVaeofoCcRvSj1ohURWnrwUBFEKG1KqVXRg61gFfEDxIOgIEbTNkkvDUlMTE12N9ldZ17S2E02TQQHNvvy3sxvJvN+MxMuGAxCNVFVdaemad08z3eKorgX35tpH/fi+Xz+M77f494jQRC+VcPgzBygYRuCX7fZbD6HwwF2ux2sVisg2Oo5yLIM2WwWVlZWQJIkPzq5iufzNR1gZH0Y7UhjY6PT5XIBx3Gwnui6DqlUChKJRBptz6Ht+Npzfu2XXC53EaMea25udrrd7prgLELUIV2yIVvCMHWA3n2YisGmpiawWCzwr0I2ZEsYhGVIEebUi5EstLS08PgTDYby5CjIT6aAa7AaU5PLgfWkD6zdp8tTDKFQSMPUteKdhPkiW4Y8Hk8FOElm+AZo8RgIu/eB0L6r8OzpADU0D9nbtyr0CYOwCJN9x8U2zF0PXWgpig/vQF0MUjigp35CQ9dxsBzrAh0Zw362wwlaZAGUl89BfjwJIMkgtG0H8fARdk5YyWSyR1GUVnLgczqdhiiUF89AujMM4v5DIB7shNzbV5D/+gX02HLBgWcr6MkEiB0HQLo/Auqnj2A7e6HkgIQwo9FoHwQCgZlMJqOXi5ZK6tK0X1fmZnUt/UvPjt7V1cUf7KE17dEZ6ZBuuRAmYs9S3tvNWMNtcrHolLkZlpLM4DXQEnH20Jr26Ix0SNeMVYi9Q8Sb3oJVaM5xclysXs6+ATCs0rpAcr6gYyKEicz0iOuSm8BXi618Xaiyv+tqEFgDUbxo+N9CmHgVMR4LI4DlXYeJXmVtLoSJ2N+pIF5TV1yn2RQ6aHQZdEVhD63XnpkJYSL2Gx4vw59Op821sNC4IgE29t8ES+dR9tCa4dMZ6pgJYSL2uIgfi9jPH2DLNVQzSwQvgDR2j7GHc7khM3C5VMm/r5wH+eEEayHlQu2bMJGq86Vmh3Rd8Hq9hn4kT0+A8nQKqSAU2IKDpsQiWmsqNJw4hU2v19DswuEwQmqs2ZUGDrVY7Enj1HL5GtSrJjTplpaWKHoaWn7DPKANvJhLpFAfqypZQ7aEsQpeMdEwZ0Po/UwkEkljN2TjsCZ5UYd0yYZsCaPeoT+AKeutc+hPIFn66xr6Nf62eIoOYvX+bfkjwAAVSSNh7Hbp+AAAAABJRU5ErkJggg==";
const zoomImgBase64 = "data:image/png;base64,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";
const rotateImgBase64 = "data:image/png;base64,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";

const moveImgBase64_blue = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABoAAAAaCAYAAACpSkzOAAAAAXNSR0IArs4c6QAAAARzQklUCAgICHwIZIgAAAGuSURBVEhLYzQIfvifgQ6AcdQickOZpKDj5WJk2DVbhuHvPwYGz/QnDJ+/ER+9RFsEsmR2owSDuiIr2FM37/9mSK1/QbRlRFvUXybK4GDGyfDg6R+wRQrSLAybD3xlqJvylqjQJNoiR6AlIBDtzQeml279BKb3n/pOXYtgps1pFAczU+pfEmUBTBHRPoJpyAjjZ3j26g/DJmCwkQKwWgSK+MYcEWAcfCE6aEBB6+vAw1A/5Q3WBIJhEXLqKup6jWGRnwM3AyhRgxICMgBZ1AdMMLhSI4ZFRxbLMnBzMjLcB6autx/+gs3avP8LOKhAljTmCIPF6oGpDSQGCkpjbQ6wmLAAM4MiMDV+/faPwSbuCYpDcFoESsYwi85c/cEwY9VHYIrjZShJFESxCCTmaMYFtwiU7L9+/89gE/sYv0WgoJvTJMGgpsDKgC3oQAZ//voPIzHAgu7Wg98MKXWYGRlnYmgCJoZNJCYGP2BiqCM2MRBKsiAf8XIzgYOSFEByPqJ5hqVbEUS3QhU5NYLiBlfqwhVvJMURyLLtM2XAZtGs4iMlhWFTS5KPKLFs+FkEAMSG+ykvTk+2AAAAAElFTkSuQmCC"
const zoomImgBase64_blue = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABoAAAAaCAYAAACpSkzOAAAAAXNSR0IArs4c6QAAAARzQklUCAgICHwIZIgAAAEuSURBVEhLYzQIfvifgQ6AcdQickMZI+gOL5Jl4OFiRDHv5v3fDBGlz8m1A6wPw6Lza+TAEp+//mNYtvUzmH3zwS+G/ae+U9+iL9/+g321af9XhvqpbymyAKYZq49mrvrIICXGwuDrwE01y3BaNANoWVOOMNUsw7BoZY8kw4xVH+BxQi3LiMqw1LCMKItAEQqzLLX+JcOZqz9JTiBEWwQy2USbnSxLsOYjYp0qJcrM8Oz1X2KVY2ZYYnSCLNk6XZqkpE9S0CE7gtQEQrZFyAkEuQRxNONkSA8VwCgbKbIIm2UZYfwM6UBsGPIIJRYotgjdsuev/9DOImTLQKU+LzcT9X0EihM1eTZwMEX78IItAQGqBx2obFRTYEWJD1A1Yxv3mPpxREzeo0piGJkWAQBvM8IpZhe+kQAAAABJRU5ErkJggg=="
const deleteImgBase64_blue = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABoAAAAaCAYAAACpSkzOAAAAAXNSR0IArs4c6QAAAARzQklUCAgICHwIZIgAAAGSSURBVEhLYzQIfvifgQ6AcdQickMZZ9BJiTIzrOiRZODlZiLK7M9f/zFElDxnePb6L1b1eC1qyhEhyhKYoropb0i3iCQbiFCM4iNQcEmKsoC1CQswMbz98I8IIxBKkPU8f/0HxXcoFq0ExomaAitJhuNSfOvBb4ZwYJzBAIpFjmacDOoKbAxSYiwMvg7cDM+BEbtp/xeiLPZz5AGGBjPD5gNfGZ69+sNw88Evhv2nvmO3CCZqos3OMLtRnOHs1Z8MKfUvwcJ+QIsdzLgYirpeg/n9ZaJAg74xbAIaDAJzgOqNgfpSgerPAPWhA6ypDptFMIMMQx6BzTi/Rg7FIaMWgYNlNOiomurUgRnZRJuDYenWz+DgjfbmBSbjH8D88pv85M3LxchwaJEsSvIllGthydsu7jHD52+YlTbO0humsX7KW3imxGUZyLegKgVUknhlPsWqDKdFMM0gXc9e/QUWkH9wegqUSkEAVGogFzvIGvC2GUCWZYQJAIseTrwhBypAl275hNfno40TQokPpzzdgg4AViZGOMv7tocAAAAASUVORK5CYII="
const rotateImgBase64_blue = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABoAAAAaCAYAAACpSkzOAAAAAXNSR0IArs4c6QAAAARzQklUCAgICHwIZIgAAAIWSURBVEhL7VU7SIJhFL1a9lLpTVkQuuTQ0hCOobPoElQUBEJRgUthjYluPaClKHFwMnpNWrPNUTQ12KIRWSkVphYZPe794Q/zf2lIUHThW+T6nfude875Zd195+/wAyX7B/ouyxzqtpY04N97gMBBhrlTXSMDi0kFPV1VeCpBrZQzvx+dPkPo8BGCoTSkHqXXzAE62e0Az3YS1vEMm9Uw3l/LXJ7Gy8KR7OeD9LoKUOEQqcwbDpYCz05S9LG8QBv7KbxEDlaTEq4Sr7Dou8PpnzgXWY1KmBioA01zGQRCGXCu3gqC8QLRlPSKY6Rnaj4uSg1R67Y3gdFQDTTgou+eF4wXiDrPoi8wOnddEP/UT7vt1CpgzHnD7C+/BIHyG50rt58C4Ru5DenbX2tnQAhMEsiEFOi1FZzGAKorhvsSq+XZZobC3pELDhMlTQYSh8veyEtfSYHIZ15XCyyhIPwojNz6nUBFUTdjq0cDlsP0QqLoWGPFYJ685AiHQ50bl2nBpQr5QQhdjx7aRC+R/wYcV9LyZv0Qi7/CoCNWkGEpHbyuVtDrFCDkN14xUJg6kMJw5AUpjIv6JxeEImuUx6z0PEHV0a6GEJBN5+DBV8MSgNFQA9RHuSgVWaLyppSYsTUw6UxFoGHcgRqTnWhiSyxM2R5JH9HkJpycDn2DWFCiKRzNMh9JqWgSpa5obUv8QfJFpQL8e0AfDg0oOIzC9FEAAAAASUVORK5CYII="


export const initControlIcon = function (c, theme) {
	let dataImage = [zoomImgBase64, deleteImgBase64, rotateImgBase64, moveImgBase64];
	switch(theme){
		case "blue":
			dataImage[0] = zoomImgBase64_blue
			dataImage[1] = deleteImgBase64_blue
			dataImage[2] = rotateImgBase64_blue
			dataImage[3] = moveImgBase64_blue
		break;
	}
	let scaleImg = document.createElement("img");
	scaleImg.src = dataImage[0];
	var deleteImg = document.createElement("img");
	deleteImg.src = dataImage[1];
	let rotateImg = document.createElement("img");
	rotateImg.src = dataImage[2];
	let moveImg = document.createElement("img");
	moveImg.src = dataImage[3];

	fabric.Object.prototype.controls.deleteControl = new fabric.Control({
		x: -0.5,
		y: 0.5,
		cursorStyle: "pointer",
		mouseUpHandler: deleteObject,
		render: renderIcon(deleteImg),
		cornerSize: 24,
	});

	fabric.Object.prototype.controls.rotateControl = new fabric.Control({
		x: 0.5,
		y: -0.5,
		cursorStyle: "crosshair",
		actionHandler: fabric.controlsUtils.rotationWithSnapping,
		actionName: "rotate",
		render: renderIcon(rotateImg),
		cornerSize: 24,
		withConnection: true,
	});

	fabric.Object.prototype.controls.scaleControl = new fabric.Control({
		x: 0.5,
		y: 0.5,
		cursorStyle: "se-resize",
		actionHandler: fabric.controlsUtils.scalingEqually,
		actionName: "scale",
		render: renderIcon(scaleImg),
		cornerSize: 24,
		withConnection: true,
	});

	fabric.Object.prototype.controls.moveControl = new fabric.Control({
		x: -0.5,
		y: -0.5,
		cursorStyle: "move",
		actionHandler: fabric.controlsUtils.dragHandler,
		actionName: "move",
		render: renderIcon(moveImg),
		cornerSize: 24,
	});

	function deleteObject(eventData, transform) {
		var canvas = transform.target.canvas;
		let selectedEle = canvas.getActiveObject();
		if (selectedEle.type === "activeSelection") {
			selectedEle.canvas = canvas;
			selectedEle.forEachObject(function (obj) {
				canvas.remove(obj);
			});
			selectedEle.setCoords();
		} else {
			canvas.remove(selectedEle);
		}
		canvas.discardActiveObject();
		canvas.requestRenderAll();
	}

	function renderIcon(icon) {
		return function renderIcon(ctx, left, top, styleOverride, fabricObject) {
			var size = this.cornerSize;
			ctx.save();
			ctx.translate(left, top);
			ctx.rotate(fabric.util.degreesToRadians(fabricObject.angle));
			ctx.drawImage(icon, -size / 2, -size / 2, size, size);
			ctx.restore();
		};
	}
};
