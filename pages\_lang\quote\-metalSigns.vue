<template>
	<div id="metalSigns">
		<Preloader v-if="isLoading"></Preloader>
		<template v-else>
			<div class="content">
				<div class="leftArea" id="leftArea">
					<SwiperArea :imgList="carouselList" v-show="!onlyAddInquiry"></SwiperArea>
					<img class="cateImg" src="https://static-oss.gs-souvenir.com/web/quoteManage/20240828/metal_signs_personalized_20240828GCZA3h.png" alt="cateImg" v-show="onlyAddInquiry" />
				</div>
				<div class="rightArea" id="rightAreaCustom">
					<div class="backToDesign" v-if="onlyAddInquiry">
						<i class="el-icon-close" @click="showDialog"></i>
					</div>
					<template v-if="!onlyAddInquiry">
						<div class="opts">
							<div class="tips">
								<p>{{ lang.uploadYourOwn }}</p>
								<button class="btn" @click="triggerUpload">{{ lang.Upload }}</button>
							</div>
						</div>
						<h1>{{ currentCate.cateName }}</h1>
						<div class="price">
							<template v-if="!priceInfo">
								<ccy-rate :price="currentCate.ringTemplatePrice * (1 - currentCate.ringTemplateDiscount)"></ccy-rate>
								<ccy-rate :price="currentCate.ringTemplatePrice"></ccy-rate>
							</template>
							<template v-else>
								<ccy-rate :price="priceInfo.totalPrice"></ccy-rate>
								<ccy-rate :price="priceInfo.foundationUnitPrice * priceInfo.totalQuantity" v-show="priceInfo.totalPrice !== priceInfo.foundationUnitPrice * priceInfo.totalQuantity"></ccy-rate>
							</template>
						</div>
					</template>
					<div class="paramsWrap">
						<template v-for="(item, index) in filterShowGeneralData">
							<template v-if="item.paramName === 'Design Your Metal Signs'">
								<div class="step-item step-design" :class="{ mask: maskName === item.paramName }" :id="item.paramName" :key="index">
									<div class="box-border">
										<i class="el-icon-close" @click="closeMask"></i>
									</div>
									<h3 class="step-title">
										<span>{{ lang.Step }} {{ item.customIndex }}</span>
										<template v-if="!onlyAddInquiry">
											{{ item.alias }}
										</template>
										<template v-else>{{ lang.neon.uploadTitle }}</template>
									</h3>
									<div class="step-box step-design-box">
										<template v-if="!onlyAddInquiry">
											<div class="custom" v-for="(textItem, index) in textConfig" :key="index">
												<div class="t">{{ lang.neon.changeTxt }} {{ index + 1 }}</div>
												<el-input :placeholder="textItem.text" v-model="textItem.customText" @input="checkText($event, textItem)" @focus="focusInput(item)">
													<i slot="prefix" class="el-input__icon el-icon-edit"></i>
												</el-input>
												<span class="warning" v-if="textItem.showWarning"> {{ lang.neon.warning }} </span>
											</div>
										</template>
										<template v-else>
											<div class="upload-box" ref="uploadBox">
												<div class="uploadList" v-if="uploadList.length">
													<ul>
														<li v-for="(item, index) in uploadList" class="uploadItem" :key="item.secure_url">
															<span>{{ item.original_filename }}</span>
															<div>
																<b class="icon-icon_Preview myIcon" @click="previewImg(item.secure_url)"></b>
																<b class="icon-check myIcon" style="color: #0cbd5f"></b>
																<span @click.stop="delUploadImg(index)">
																	<b class="icon-shanchu2 myIcon" style="color: #b6b0b0"></b>
																</span>
															</div>
														</li>
													</ul>
												</div>
												<div>
													<div class="upload-btn" style="text-align: center" @click="triggerUpload">
														<div class="uploadBtnWrap" v-if="uploadList.length">
															<button type="button" :disabled="isUpload" :class="{ isDisabled: isUpload }">
																{{ lang.Upload }}
															</button>
														</div>
														<div v-else class="uploadIcon">
															<b class="icon-shangchuan"></b>
															<span>{{ lang.Upload }}</span>
														</div>
													</div>
													<div class="tips">
														<div>
															{{ lang.p9 }}.
															<p>{{ item.tips }}</p>
														</div>
													</div>
												</div>
											</div>
										</template>
										<div class="other">
											<div class="t">
												{{ onlyAddInquiry ? lang.OrderComments : lang.neon.anyOtherNote }}
											</div>
											<el-input type="textarea" prefix-icon="el-icon-edit" rows="3" :placeholder="lang.neon.anyOtherNoteTips" v-model="remark" @focus="focusInput(item)"></el-input>
										</div>
										<div class="tip">{{ lang.notes }}</div>
									</div>
									<button type="button" class="nextBtn" :disabled="!checkDesignStep()" @click="showMaskFn(item.paramName)">
										{{ lang.next }}
									</button>
								</div>
							</template>
							<template v-if="item.paramName === 'Select Size'">
								<div class="step-item step-size" :class="{ mask: maskName === item.paramName }" :id="item.paramName" :key="index">
									<div class="box-border">
										<i class="el-icon-close" @click="closeMask"></i>
									</div>
									<h3 class="step-title">
										<span>{{ lang.Step }} {{ item.customIndex }}</span>
										{{ lang.Select }}
										{{ item.alias ? item.alias : item.paramName }}
									</h3>
									<div class="step-box step-size-box">
										<div class="item-wrap" v-for="citem in item.childList" :key="citem.id" @click="selectQuoteParams(item, citem)" :class="{ active: hasId(citem.id, selectedData[item.paramName]) }">
											<div class="item">
												<div class="textWrap">
													<div>
														<p class="normal-text">
															{{ citem.alias ? citem.alias : citem.paramName }}
														</p>
													</div>
												</div>
											</div>
										</div>
									</div>
								</div>
							</template>
							<template v-if="item.paramName === 'Select Color'">
								<div class="step-item step-color" :class="{ mask: maskName === item.paramName }" :id="item.paramName" :key="index">
									<div class="box-border">
										<i class="el-icon-close" @click="closeMask"></i>
									</div>
									<h3 class="step-title">
										<span>{{ lang.Step }} {{ item.customIndex }}</span>
										{{ lang.Select }}
										{{ item.alias ? item.alias : item.paramName }}
									</h3>
									<div class="step-box step-color-box">
										<div class="item-wrap" v-for="citem in item.childList" :key="citem.id" @click="selectQuoteParams(item, citem)" :class="{ active: hasId(citem.id, selectedData[item.paramName]) }">
											<div class="item">
												<div class="imgWrap">
													<img loading="lazy" :src="parseJSON(citem.imageJson)[0].url" :alt="parseJSON(citem.imageJson)[0].alt" style="aspect-ratio: 1/1" />
												</div>
											</div>
											<div class="textWrap">
												<div>
													<p class="normal-text">
														{{ citem.alias ? citem.alias : citem.paramName }}
													</p>
												</div>
											</div>
										</div>
									</div>
								</div>
							</template>
							<!--                步骤九-->
							<template v-if="item.paramName === 'qty'">
								<StepQty class="step-item step-qty" placeholder="Text Quantity" :class="{ mask: maskName === item.paramName }" :id="item.paramName" :index="item.customIndex" :itemData="item" :customQty.sync="customQty" :restaurants="restaurants" @closeMask="closeMask" @showMaskFn="showMaskFn" @calcPrice="debounceCalcPrice" :key="index">
									<template v-slot:title>
										<h3 class="step-title">
											<span>{{ lang.Step }} {{ item.customIndex }}</span>
											{{ lang.Select }}
											{{ item.alias ? item.alias : item.paramName }}
										</h3>
									</template>
								</StepQty>
							</template>
							<!--                步骤十-->
							<template v-if="item.paramName === 'Select Turnaround Time'">
								<StepTime class="step-item step-date" :class="{ mask: maskName === item.paramName }" :id="item.paramName" :index="item.customIndex" :itemData="item" :priceInfo="priceInfo" :previewMode="previewMode" :selectedParams="selectedData" @showMaskFn="showMaskFn" @selectItem="selectQuoteParams($event.item, $event.citem)" @closeMask="closeMask" :key="index">
									<template v-slot:title>
										<h3 class="step-title">
											<span>{{ lang.Step }} {{ item.customIndex }}</span>
											{{ lang.Select }}
											{{ item.alias ? item.alias : item.paramName }}
										</h3>
									</template>
								</StepTime>
							</template>
						</template>
					</div>
					<div id="foot" class="subtotal" :class="{ hideAddCart: onlyAddInquiry }">
						<div class="totalPriceBox">
							<strong class="totalPriceText">{{ langSemi.totalPrice }}:</strong>
							<ccy-rate class="final-price" :price="priceInfo.totalPrice"></ccy-rate>
						</div>
						<freeTip></freeTip>
						<div class="btnGroup">
							<button type="button" class="inquiryBtn" @click="addInquiry">{{ lang.submitInquiry }}</button>
							<button type="button" class="addCartBtn" @click="addCart">{{ lang.addToCart }}</button>
						</div>
					</div>
				</div>
			</div>
			<!--    遮罩-->
			<myMask :maskName.sync="maskName"></myMask>
			<!-- 建议弹窗-->
			<RecomendDialog :commentsDialog.sync="commentsDialog" :paramValue="selectedParamsValue" @next="recomendNext" :showUpload="false"></RecomendDialog>
			<BaseDialog v-model="upgradeDialog" :width="device != 'mb' ? '640px' : '320px'">
				<Upgrade @closeUpgrade="upgradeDialog = false" :tipArr="tipArr" :typeName="cateData.cateName" unit="pairs" @upgradeLevel="upgradeLevel"></Upgrade>
			</BaseDialog>
			<!--小类别标签弹窗-->
			<DialogForShop :shopDialog.sync="shopDialog" :tagList="tagList" :shopList="shopList" @changeTag="getAppRingTemplatesList" @selectProduct="selectProduct"></DialogForShop>
			<infoDialog :infoDialogVisible.sync="infoDialogVisible" @getValue="getValueFun" :otherUpload="uploadArtworkList" :recomendUpload="recomendUpload" :uploadList.sync="uploadList"></infoDialog>
			<BaseDialog v-model="noFileDialog" :width="device != 'mb' ? '485px' : '90%'" :model="false">
				<template #closeIcon>
					<div style="display: none"></div>
				</template>
				<infoUpload :infoUploadList="infoUploadList" @pushInfoList="pushInfoList" @delInfoList="delInfoList" @updateInquiry="updateInquiry" @closeInfoDialog="closeInfoDialog"></infoUpload>
			</BaseDialog>
			<input type="file" ref="upload" :accept="acceptFileType" hidden multiple @change="uploadPic" />
		</template>
	</div>
</template>
<script>
import "@/plugins/element";
import BaseDialog from "@/components/Quote/BaseDialog";
import Upgrade from "@/components/Quote/Upgrade";
import infoDialog from "@/components/Medals/infoDialog";
import quoteMixin from "@/mixins/quote";
import quoteBanChoiceMixins from "@/mixins/quoteBanChoice";
import RecomendDialog from "@/components/Quote/RecomendDialog.vue";
import myMask from "@/components/Quote/Mask.vue";
import Preloader from "@/components/Quote/Preloader.vue";
import SwiperArea from "@/components/Quote/MetalSigns/SwiperArea.vue";
import { getFileSuffix, isImageType } from "@/utils/utils";
import CcyRate from "@/components/CCYRate.vue";
import StepQty from "@/components/Quote/StepQty.vue";
import StepTime from "@/components/Quote/StepTime.vue";
import freeTip from "@/components/Quote/freeTip.vue";
import { getAppRingTagList, getAppRingTemplatesList } from "@/api/web";
import { uploadFile } from "@/utils/oss";
import DialogForShop from "@/components/Quote/MetalSigns/DialogForShop.vue";
import { checkFile, metalSignsFileType } from "@/utils/validate";

export default {
	head() {
		return {
			title: "Quote page",
		};
	},
	components: {
		freeTip,
		StepTime,
		StepQty,
		CcyRate,
		Preloader,
		myMask,
		RecomendDialog,
		BaseDialog,
		infoDialog,
		Upgrade,
		SwiperArea,
		DialogForShop,
	},
	mixins: [quoteMixin, quoteBanChoiceMixins],
	data() {
		return {
			shopList: [],
			tagList: [],
			shopDialog: false,
			onlyAddInquiry: false,
			acceptFileType: metalSignsFileType,
			hideAddCart: false,
			currentCate: {},
			textConfig: [],
			otherText: "",
			customText: "",
			tipNum: 1,
			pid: 330,
			restaurants: [],
			customQty: "1",
		};
	},
	computed: {
		langSemi() {
			return this.$store.getters.lang.semiCustom || {};
		},
	},
	watch: {
		initData() {
			getAppRingTemplatesList({
				cateId: this.pid,
				ringTagId: null,
				page: 1,
				pageSize: 10000,
			}).then((res) => {
				let find = res.data.content.find((item) => item.id === this.routingId);
				if (find) {
					this.currentCate = find;
					let textConfig = find.textConfig
						? JSON.parse(find.textConfig)
						: [
								{
									text: "",
									characterLimit: 50,
								},
						  ];
					textConfig.forEach((item) => {
						item.customText = "";
						item.showWarning = false;
						item.characterLimit = Number(item.characterLimit) || 50;
					});
					this.textConfig = textConfig;
				}
			});
		},
	},
	methods: {
		isImageType,
		selectProduct(item) {
			if (this.isIframe) {
				this.$router.push({
					path: item.quoteRoutingName,
					query: {
						type: "quoteIframe",
					},
				});
			} else {
				this.$router.push({
					path: item.quoteRoutingName,
				});
			}
		},
		getAppRingTemplatesList(id) {
			getAppRingTemplatesList({
				cateId: this.pid,
				ringTagId: id,
				page: 1,
				pageSize: 10000,
			}).then((res) => {
				this.shopList = res.data.content;
			});
		},
		showDialog() {
			this.onlyAddInquiry = false;
			this.shopDialog = true;
			getAppRingTagList({
				cateId: this.pid,
			}).then((res) => {
				this.tagList = res.data;
				this.getAppRingTemplatesList();
			});
		},
		focusInput(item) {
			this.maskName = item.paramName;
			this.toPosition(item.paramName);
		},
		checkDesignStep() {
			if (this.onlyAddInquiry) {
				return true;
			}
			let textConfig = this.textConfig;
			let find = textConfig.find((item) => {
				return !item.customText;
			});
			return !find;
		},
		checkText(val, item) {
			let characterLimit = item.characterLimit;
			item.showWarning = String(val).length > characterLimit;
		},
		previewImg(img) {
			if (this.isImageType(img)) {
				this.$viewerApi({
					images: [img],
				});
			} else {
				window.open(img);
			}
		},
		delUploadImg(index) {
			this.uploadList.splice(index, 1);
		},
		triggerUpload() {
			this.$refs.upload.click();
		},
		openUpload() {
			this.$refs.upload.click();
		},
		replayUpload() {
			this.openUpload();
			this.$store.commit("setSizeDialog", false);
		},
		uploadPic(event, type = "upload") {
			this.$gl.show();
			let files = type === "upload" ? event.target.files : event;
			let fileResult = checkFile(Array.from(files), this.acceptFileType, 80);
			if (!fileResult) {
				this.$toast.error("File type error");
				this.$gl.hide();
				return false;
			}
			if (fileResult.nomalSize.length == 0) {
				this.$gl.hide();
				this.$store.commit("setSizeDialog", true);
				this.$store.commit("setInputRefName", "replayUpload");
				this.$store.commit("setOverSizeList", fileResult.overSize);
				this.$refs.upload.value = "";
				return false;
			}
			let uploadPromises = [];
			fileResult.nomalSize.forEach((file) => {
				let promise = uploadFile(file).then((res) => {
					this.uploadList.push({
						original_filename: file.name,
						secure_url: res,
						size: (file.size / 1024).toFixed(1),
					});
				});
				uploadPromises.push(promise);
			});
			Promise.all(uploadPromises).then(() => {
				this.$gl.hide();
				this.$refs.upload.value = "";
				if (fileResult.overSize.length > 0) {
					this.$store.commit("setSizeDialog", true);
					this.$store.commit("setInputRefName", "replayUpload");
					this.$store.commit("setOverSizeList", fileResult.overSize);
				}
				this.onlyAddInquiry = true;
			});
		},
	},
	mounted() {
		/**
		 * showModal 1-模板展示模式 2-上传模式
		 */
		if (this.$route.query.showModal == 1) {
			this.showDialog();
		} else if (this.$route.query.showModal == 2) {
			this.onlyAddInquiry = true;
		}
		this.$Bus.$on("replayUpload", this.replayUpload);
	},
	beforeDestroy() {
		this.$Bus.$off("replayUpload");
	},
};
</script>
<style scoped lang="scss">
@mixin activeMixin {
	content: "";
	font-family: "modalicon";
	height: 20px;
	line-height: 20px;
	border-top-right-radius: 6px;
	border-bottom-left-radius: 6px;
	position: absolute;
	right: 0;
	text-align: center;
	top: 0;
	width: 20px;
	background: linear-gradient(90deg, #48a2ff 0%, #ed12fb 100%);
	color: #fff;

	@include respond-to(mb) {
		width: 1.15rem;
		height: 1.15rem;
		line-height: 1.15rem;
		font-size: 0.5rem;
	}
}

@mixin borderJianbian {
	border-color: transparent;
	background-clip: padding-box, border-box;
	background-origin: padding-box, border-box;
	background-image: linear-gradient(to right, #ffffff, #ffffff), linear-gradient(90deg, #8f41e9, #578aef);
}

.nextBtn {
	position: relative;
	display: block;
	width: 140px;
	margin: 20px auto 0;
	border-radius: 10px;
	color: #fff;
	padding: 9.5px 5px;
	transition: all 0.3s;
	background: linear-gradient(90deg, rgb(74, 72, 255) 0%, rgb(176, 18, 251) 100%);

	&:hover {
		transform: scale(1.05);
	}
}

button[disabled] {
	opacity: 0.5;
}

#metalSigns {
	font-family: Google Sans, Arial, serif;
	font-size: 0.88rem;
	color: #333333;

	@include respond-to(mb) {
		font-size: 1rem;
	}

	.content {
		max-width: 1400px;
		margin: 0 auto;
		padding: 50px 20px;
		position: static;
		display: grid;
		grid-template-columns: repeat(48, 1fr);

		@include respond-to(mb) {
			padding: 0;
			grid-template-columns: repeat(1, 1fr);
		}

		.leftArea {
			grid-column: 1/22;

			@include respond-to(mb) {
				grid-column: 1/1;
			}

			.cateImg {
				position: sticky;
				top: 0;
				bottom: auto;
				width: 100%;
				aspect-ratio: 1;
				background: #f9f9f9;
				border-radius: 10px;
				@include respond-to(mb) {
					border-radius: 0;
					height: 31rem;
				}
			}
		}

		.rightArea {
			grid-column: 25/49;

			@include respond-to(mb) {
				grid-column: 1/1;
			}

			.backToDesign {
				text-align: right;
				font-weight: 700;
				font-size: 1.5rem;
				cursor: pointer;
				@include respond-to(mb) {
					position: absolute;
					right: 1rem;
					margin-top: 0.8rem;
					z-index: 1;
					i {
						font-weight: 700;
					}
				}
			}

			h1 {
				font-weight: bold;
				font-size: 1.87rem;
				text-align: left;
				margin: 1.5rem 0 1.06rem;
				@include respond-to(mb) {
					padding: 1.5rem 1.5rem 0.75rem;
					margin: 0;
					font-size: 1.5rem;
				}
			}

			.price {
				@include respond-to(mb) {
					padding: 0 1.5rem;
				}

				label:first-child {
					font-size: 1.87rem;
					font-weight: 700;
					color: #de3500;
					@include respond-to(mb) {
						font-size: 1.5rem;
					}
				}

				label:last-child {
					font-size: 1.5rem;
					text-decoration: line-through;
					color: #666666;
					@include respond-to(mb) {
						font-size: 1.25rem;
					}
				}
			}

			.opts {
				font-size: 0.88rem;

				.tips {
					display: grid;
					grid-template-columns: 1fr auto;
					justify-content: space-between;
					align-items: center;
					gap: 10px;
					padding: 0.5em;
					background-color: #e8f7fa;

					.btn {
						width: 100px;
						height: 40px;
						background: #767cff;
						border-radius: 20px;
						color: #fff;
						transition: opacity 0.3s;

						&:hover {
							opacity: 0.9;
						}
					}
				}
			}

			.mask {
				z-index: 101;
				background-color: #fff;
			}
		}

		.paramsWrap {
			@include respond-to(mb) {
				padding: 0 1.5rem;
			}

			.step-item {
				position: relative;
				background-color: #fff;
				padding: 1.25rem 0;
				border-radius: 10px;

				@include respond-to(mb) {
					margin-bottom: 0;
					background-color: #fff;
					padding: 1rem 0;
					&.mask {
						::v-deep .box-border {
							.el-icon-close {
								width: 30px;
								height: 30px;
								transform: translate(0, 0);
								box-shadow: none;
							}
						}
					}
					.step-title {
						margin-bottom: 10px;
						font-weight: bold;
						color: #171719;
					}
				}

				::v-deep .box-border {
					display: none;
					position: absolute;
					left: -20px;
					right: -20px;
					top: 0;
					bottom: 0;
					background-color: #fff;
					border: 1px solid #d9dbdd;

					@include respond-to(mb) {
						left: -1.5rem;
						right: -1.5rem;
					}

					.el-icon-close {
						display: flex;
						justify-content: center;
						align-items: center;
						position: absolute;
						font-weight: 700;
						top: 0;
						right: 0;
						width: 40px;
						height: 40px;
						transform: translate(50%, -50%);
						cursor: pointer;
						background: #ffffff;
						border-radius: 50%;
						box-shadow: 1px 2px 20px 0 rgba(0, 0, 0, 0.2);
						z-index: 10;
					}
				}

				&.mask {
					position: relative;
					z-index: 101;

					.confirmBtnWrap {
						position: relative;
					}

					::v-deep .box-border {
						display: block;
					}

					.step-title {
						position: relative;
					}

					.step-box {
						position: relative;
					}
				}

				.step-title {
					font-size: 1.13rem;
					font-weight: 700;
					color: #333333;
					margin-bottom: 1.38rem;

					@include respond-to(mb) {
						font-size: 1.33rem;
					}

					& > span {
						display: inline-block;
						margin-right: 10px;
						color: #ffffff;
						background: url("https://file-cloud-static.oss-accelerate.aliyuncs.com/web/public/picStore/20230707/Pc-bg_20230707xTDWSHhd.png") center/contain no-repeat;
						font-weight: 400;
						height: 1.5rem;
						line-height: 1.5rem;
						font-size: 1rem;
						text-align: center;
						width: 5.29rem;
						text-transform: uppercase;
					}
				}

				.step-box {
					display: grid;
					justify-content: space-between;
					gap: 1.25rem;

					.item-wrap {
						position: relative;

						p.normal-text,
						p.t1 {
							transition: all 0.3s;
						}

						@media (any-hover: hover) {
							&:hover {
								p.normal-text {
									color: $color-primary;

									span {
										color: #333333;
									}
								}

								p.t1 {
									color: $color-primary;
								}

								.zoomIcon {
									color: $color-primary !important;
								}
							}
						}

						.item {
							position: relative;
							border-radius: 6px;
							display: flex;
							flex-direction: column;
							justify-content: space-between;
							align-items: center;
							cursor: pointer;
							transition: all 0.3s;
							border: 1px solid transparent;

							.imgWrap {
								display: flex;
								justify-content: center;
								align-items: center;
								height: 100%;
								width: 100%;

								img {
									object-fit: contain;
								}
							}

							@media (any-hover: hover) {
								&:hover {
									@include borderJianbian;
									box-shadow: 0 3px 4px 0 #cccccc;
								}
							}
						}

						.textWrap {
							.normal-text {
								color: #333333;
								transition: all 0.3s;
								text-align: left;
							}

							.tip-text {
								color: #de3500;
							}
						}

						&.active {
							.item {
								@include borderJianbian;
							}
						}

						& .textWrap {
							display: flex;
							justify-content: center;
							align-items: center;
							margin-top: 0;
						}
					}

					@include respond-to(mb) {
						gap: 0.5rem;

						.item-wrap {
							.item {
								border-radius: 6px;
							}

							.textWrap {
								padding-bottom: 0;

								.normal-text {
									text-align: center;
								}
							}

							& > .textWrap {
								display: flex;
							}
						}
					}
				}
			}

			.step-size {
				.step-size-box {
					grid-template-columns: repeat(4, 1fr);

					.item-wrap {
						.item {
							padding: 10px 0;
							border: 1px solid #dbdbdb;
						}

						&.active::after {
							@include activeMixin;
						}
					}
				}
			}

			.step-color {
				.step-color-box {
					grid-template-columns: repeat(4, 1fr);

					@include respond-to(mb) {
						grid-template-columns: repeat(3, 1fr);
					}

					.item-wrap {
						&.active .item::after {
							@include activeMixin;
						}
					}

					.item {
						overflow: hidden;
					}
				}
			}

			.step-qty {
				margin-bottom: 0;

				::v-deep .el-input__inner {
					border: 1px solid #dcdfe6;
					background-color: #ffffff;
					border-radius: 4px;
					font-size: 0.88rem;
				}

				::v-deep .gs-quote-button {
					background: linear-gradient(90deg, #48a2ff 0%, #ed12fb 100%);
					border: none;
					border-radius: 6px;
				}
			}

			.step-date {
				margin-bottom: 0;

				::v-deep .price-box {
					display: none;
				}

				::v-deep .discount-item {
					position: relative;
					background-color: #ffffff !important;
					border-radius: 6px;
					border: 1px solid #dbdbdb;

					&.active {
						@include borderJianbian;
					}

					@media (any-hover: hover) {
						&:hover {
							@include borderJianbian;
							box-shadow: 0 3px 4px 0 #cccccc;
						}
					}

					&.active::after {
						@include activeMixin;
					}
				}

				::v-deep .top {
					font-size: 0.88rem;

					@include respond-to(mb) {
						font-size: 1rem;
					}

					.customCircle {
						display: none;
					}
				}

				::v-deep .bottom {
					padding: 0;
					margin: 4px 0 0;
					color: #9e9e9e;
					font-size: 0.88rem;
				}

				::v-deep .step-box {
					grid-template-columns: repeat(3, 1fr);

					@include respond-to(mb) {
						grid-template-columns: repeat(2, 1fr);
					}
				}

				::v-deep .confirmBtnWrap {
					position: absolute;
					clip: rect(0, 0, 0, 0);
				}
			}

			.step-design {
				.step-design-box {
					display: block !important;

					::v-deep .el-input__inner:focus,
					::v-deep .el-textarea__inner:focus {
						border-color: #5d33fd !important;
					}

					& > div {
						margin-bottom: 10px;

						&:last-child {
							margin-bottom: 0;
						}
					}

					.t {
						margin-bottom: 10px;
						font-weight: 700;
					}

					.tip {
						color: #b2b2b2;
					}

					.warning {
						color: red;
						display: inline-block;
						font-size: 0.88rem !important;
						margin-top: 3px;
						padding-left: 5px;
						padding-right: 5px;
					}

					@include respond-to(mb) {
						.el-textarea {
							font-size: 1rem;
						}
						.el-input {
							font-size: 1rem;
						}
					}
				}

				.nextBtn {
					display: none;
				}

				&.mask {
					.nextBtn {
						display: block;
					}
				}
			}
		}

		.subtotal {
			padding: 20px;
			background-color: #ebebeb;
			border-radius: 10px;

			@include respond-to(mb) {
				border-radius: 0;
			}

			&.hideAddCart {
				.totalPriceBox {
					display: none;
				}

				.addCartBtn {
					display: none;
				}

				.freeTip {
					width: 60%;
					margin: 0 auto;
				}
			}

			.totalPriceBox {
				display: flex;
				align-items: center;
				justify-content: center;
				font-weight: 700;

				.totalPriceText {
					margin-right: 10px;
					font-size: 1.13rem;
				}

				.final-price {
					font-size: 1.5rem;
					color: #e6252e;
				}
			}

			.btnGroup {
				display: flex;
				justify-content: center;
				gap: 1.25rem;

				button {
					flex: 1;
					transition: all 0.3s;
					max-width: 326px;

					&:hover {
						transform: scale(1.05);
					}
				}

				.inquiryBtn {
					height: 3.36rem;
					border: none;
					border-radius: 6px;
					color: #ffffff;
					background: linear-gradient(90deg, #48a2ff 0%, #ed12fb 100%);
					text-transform: uppercase;
					@include respond-to(mb) {
						height: 2.83rem;
					}
				}

				.addCartBtn {
					height: 3.36rem;
					border: none;
					border-radius: 6px;
					color: #ffffff;
					background: linear-gradient(90deg, #06c0c1 0%, #23aadc 100%);
					text-transform: uppercase;
					@include respond-to(mb) {
						height: 2.83rem;
					}
				}
			}
		}
	}
}

.upload-box {
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	margin-top: 10px;
	padding: 10px;
	background: #ffffff;
	border: 1px dashed #e9ecf0;
	border-radius: 4px;
	cursor: pointer;
	transition: all 0.3s;
	height: 170px;

	@media (any-hover: hover) {
		&:hover {
			border-color: $color-primary;
		}
	}

	.uploadList {
		width: 100%;
		height: 100px;
		overflow: auto;
		margin-bottom: 10px;
		text-align: center;

		ul {
			margin-left: 0;
		}

		.uploadItem {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 5px;
			font-size: 0.88rem;
		}

		.myIcon {
			margin: 0 4px;
		}
	}

	.upload-btn {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		margin-bottom: 10px;

		.uploadIcon {
			display: flex;
			align-items: center;
			color: #ccc;

			b {
				font-size: 1.87rem;
				margin-right: 7px;
			}

			span {
				font-weight: 700;
				color: $color-primary;
			}
		}

		button {
			width: 116px;
			height: 32px;
			border-radius: 10px;
			border: none;
			color: #fff;
			background: linear-gradient(90deg, #48a2ff 0%, #ed12fb 100%);
		}
	}

	.tips {
		color: #999999;
		text-align: center;
	}

	@include respond-to(mb) {
		height: 12.54rem;

		.uploadList {
			height: 80px;

			.uploadIcon {
				width: 51px;
				height: 41px;
				margin-top: 0;
				font-size: 48px !important;
			}
		}

		.upload-btn button {
			width: 147px;
			height: 30px;
			border-radius: 4px;
		}
	}
}
</style>