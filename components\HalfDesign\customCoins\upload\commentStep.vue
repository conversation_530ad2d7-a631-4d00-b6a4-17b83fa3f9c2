<template>
  <div class="commentStep">
    <slot name="stepText"></slot>
    <comment :commentObj="commentObj" :customStyle="{ minHeight: '100px' }" :canUploadFile="false"
      :placeholder="langSemiCustom.coins.comment">
    </comment>
  </div>
</template>

<script>
import comment from "@/components/HalfDesign/customMedals/common/comment";

export default {
  inject: ["getCommentObj"],
  name: 'commentStep',
  props: {},
  components: { comment },
  data() {
    return {

    }
  },
  watch: {},
  computed: {
    commentObj() {
      return this.getCommentObj();
    },
    langSemiCustom() {
      return this.$store.getters.lang?.semiCustom;
    },
  },
  methods: {},
  created() { },
  mounted() { },
}
</script>

<style scoped lang="scss">
.commentStep {
  .textBox {
    font-size: 16px;
    margin-bottom: 10px;

    .titleText {
      margin-right: 0.2em;
      font-weight: 700;
    }

    .noteText {
      font-size: 14px;
    }

    @include respond-to(mb) {
      font-size: 14px;

      .noteText {
        font-size: 12px;
      }
    }
  }

}
</style>
