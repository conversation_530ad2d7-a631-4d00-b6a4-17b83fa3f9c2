<template>
    <v-app :disabled="isPaidOrder">
        <div class="bps-container py-2 py-lg-3 checkoutPage" id="mainContent">
            <div class="left-box">
                <div class="yourPosition py-3 mb-5 text-body-2">
                    {{ langCart.position }}
                </div>
                <div class="cusTitle text-uppercase border-bottom pb-2 pb-lg-4">
                    <nuxt-link to="/cart" v-show="!isFromDetail"><b class="icon-Undo pointer mr-2" style="color: #b8b8b8"></b></nuxt-link>
					 <div @click="toProDetail" v-show="isFromDetail"><b class="icon-Undo pointer mr-2" style="color: #b8b8b8"></b></div>
                    {{ langCart.SecureCheckout }}
					<v-tooltip bottom max-width="300" :color="'#fff'"   :content-class="'custom-Vtooltip checkoutIcon'"  v-model="isTooltipVisible"  >
						<template v-slot:activator="{ on, attrs }">
							<v-icon v-bind="attrs"  size="18px" class="selfCheckoutIcon"  @mouseenter="showTooltip" @mouseleave="hideTooltipDelayed" @click="showTooltipMb"> mdi-help-circle-outline </v-icon>
						</template>
						<div class="text-center" style="display: flex; align-items: start" @mouseenter="keepTooltipOpen" @mouseleave="hideTooltipDelayed">
							<div style="text-align: left; font-size: 13px; width: fit-content; max-width: 250px; word-break: break-word;" :style="{ color: '#333' }">{{langSemiCustom.checkoutTip}}
							<span :style="{color:'var(--color-primary)',cursor: 'pointer','text-decoration':'underline'}"  @click="handleLearnMoreClick" >{{langSemiCustom.learnMore}}</span>
							</div>
						</div>
				    </v-tooltip>
                </div>
                <!--产品列表-->
                <client-only>
                    <div class="box cart-box custom-scrollbar" :style="isPopSite ? 'max-height: 750px;' : 'max-height: 315px;'">
                        <div v-for="(item, index) in selectCartList" :key="index">
                            <div class="cart-item2">
                                <div class="cart-item">
                                    <div class="item-p t1">
                                        <div class="imgWrap">
                                            <CustomImage :url="getPicPath(item)" :controls="false"></CustomImage>
                                        </div>
                                    </div>
                                    <div class="item-p t2">
                                        <div class="giveawayClass" v-if="item.isFreebie === 1">GIFT</div>
                                        {{ item.cateName || item.productName }}
                                    </div>
                                    <div class="item-p t4">
                                        <div class="t">
                                            {{ langCart.quantity }}
                                        </div>
                                        <div class="c">
                                            {{ item.quantity }}
                                            <span v-show="item.giftQuantityTotal" style="color: red">+ {{ item.giftQuantityTotal }} FREE</span>
                                        </div>
                                    </div>
                                    <div class="item-p t5">
                                        <div class="t">
                                            {{ langCart.subtotal }}
                                        </div>
                                        <div class="c">
                                            <div v-if="item.isFreebie !== 1">
                                                <CCYRate :price="item.totalPrice" :ccy="currentCurrency"></CCYRate>
                                            </div>
                                            <div v-else>
                                                <span style="color:red"><CCYRate :price="item.totalPrice" :ccy="currentCurrency"></CCYRate></span>
                                                <span style="color: #8E8E8E;text-decoration: line-through;"><CCYRate :price="item.unitPrice" :ccy="currentCurrency"></CCYRate></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div v-for="(o, i) in item.ordersProductExtraList" :key="i" class="cart-item" :class="!isPopSite ? 'cart-item2' : ''">
                                    <div class="item-p t1">
                                        <div class="imgWrap2"></div>
                                    </div>
                                    <div class="item-p t2">
                                        {{ o.itemName }}
                                    </div>
                                    <div class="item-p t4">
                                        <div class="t">
                                            {{ langCart.quantity }}
                                        </div>
                                        <div class="c">
                                            {{ o.quantity }}
                                        </div>
                                    </div>
                                    <div class="item-p t5">
                                        <div class="t">
                                            {{ langCart.subtotal }}
                                        </div>
                                        <div class="c">
                                            <span v-if="Math.sign(o.totalPrice) < 0">-</span>
                                            <CCYRate :price="getTotalPrice(o.totalPrice)" :ccy="currentCurrency"></CCYRate>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div v-for="(i,ii) in item.childList" :key="ii"  v-show="item.childList && isPopSite" class="cart-item2">
                                <div class="cart-item ">
                                    <div class="item-p t1">
                                        <div class="imgWrap2"></div>
                                    </div>
                                    <div class="item-p t2">
                                        {{ i.productName }}
                                    </div>
                                    <div class="item-p t4">
                                        <div class="t">
                                            {{ langCart.quantity }}
                                        </div>
                                        <div class="c">
                                            {{ i.quantity }}
                                        </div>
                                    </div>
                                    <div class="item-p t5">
                                        <div class="t">
                                            {{ langCart.subtotal }}
                                        </div>
                                        <div class="c">
                                            <span v-if="Math.sign(i.totalPrice) < 0">-</span><CCYRate :price="getTotalPrice(i.totalPrice)" :ccy="currentCurrency"></CCYRate>
                                        </div>
                                    </div>
                                </div>
                                <div v-for="(o,id) in i.ordersProductExtraList"
                                     :key="id"
                                     class="cart-item"
                                >
                                    <div class="item-p t1">
                                        <div class="imgWrap2"></div>
                                    </div>
                                    <div class="item-p t2">
                                        {{ o.itemName }}
                                    </div>
                                    <div class="item-p t4">
                                        <div class="t">
                                            {{ langCart.quantity }}
                                        </div>
                                        <div class="c">
                                            {{ o.quantity }}
                                        </div>
                                    </div>
                                    <div class="item-p t5">
                                        <div class="t">
                                            {{ langCart.subtotal }}
                                        </div>
                                        <div class="c">
                                            <span v-if="Math.sign(o.totalPrice) < 0">-</span><CCYRate :price="getTotalPrice(o.totalPrice)" :ccy="currentCurrency"></CCYRate>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- 霓虹灯网站赠品--购物车过来 -->
                        <div class="cart-item2" v-if="giveawayItem && Object.keys(giveawayItem).length > 0 && !isOrder && !isArtWork">
                            <div class="cart-item">
                                <div class="item-p t1">
                                    <div class="imgWrap">
                                        <CustomImage :url="device !== 'mb' ? giveawayItem.imgPc : giveawayItem.imgMb" :controls="false"></CustomImage>
                                    </div>
                                </div>
                                <div class="item-p t2">
                                    <div class="giveawayClass">GIFT</div>
                                    {{ giveawayItem.name }}
                                </div>
                                <div class="item-p t4">
                                    <div class="t">
                                        {{ langCart.quantity }}
                                    </div>
                                    <div class="c">
                                        {{ giveawayItem.quantity }}
                                    </div>
                                </div>
                                <div class="item-p t5">
                                    <div class="t">
                                        {{ langCart.subtotal }}
                                    </div>
                                    <div class="c">
                                        <span style="color:red"><CCYRate price="0" :ccy="currentCurrency"></CCYRate></span>
                                        <span style="color: #8E8E8E;
                        text-decoration: line-through;"><CCYRate :price="giveawayItem.price" :ccy="currentCurrency"></CCYRate></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </client-only>
                <div class="box delivery-box">
                    <div class="box-title">{{ langCart.shippingDelivery }}</div>
                    <div class="mt-2 mt-lg-4 mb-lg-2">
                        <span style="color: #666666">{{ langCart.shippingMethod }}: </span>
                        <!-- 样品单文案 -->
                        <span v-if="isSampleOrder === 2 || isSampleOrder === 3">{{ langCart.freeGroundShipping }}</span>
                        <span v-else>{{ langCart.fed }}</span>
                        <span style="color: #de3500" v-if="!isPopSite && isSampleOrder !== 2 && isSampleOrder !== 3">({{ langCart.free }})</span>
                    </div>
                    <img :src="language === 'de' ? 'https://static-oss.gs-souvenir.com/web/quoteManage/20241008/%E5%BE%B7%E8%AF%AD%E7%89%88_2034087MjGw4.png' : 'https://static-oss.gs-souvenir.com/web/quoteManage/20241008/%E8%8B%B1%E8%AF%AD%E7%89%88_203408Kbm34y.png'" alt="" style="width: auto" />
                </div>
            </div>
            <div class="right-box custom-scrollbar" id="rightContent">
                <div>
                    <!--支付方式列表-->
                    <div class="box pay-box">
                        <div class="box-title">
                            {{ langCart.spm }}
                        </div>
                        <div class="my-2">
                            <!--支付方式列表-->
                            <v-radio-group dense row class="mt-0" :hide-details="true" v-model="paymentMethodId" @change="changePaymentMethod">
                                <!--支付方式列表-->
                                <template v-for="item in payTypeList">
                                    <v-radio :value="item.id" @change="payTypeId = item.payType.id">
                                        <template #label>
                                            <fieldset class="myFileset">
                                                <legend>{{ langCart[item.payType.payName] ? langCart[item.payType.payName] : item.payType.payName }}</legend>
                                                <img :src="item.payType.payImg" alt="" />
                                            </fieldset>
                                        </template>
                                    </v-radio>
                                </template>
                                <v-radio :value="0" v-if="isReputable === 2" @change="payTypeId = 0">
                                    <template #label>
                                        <fieldset class="myFileset">
                                            <legend>{{ langCart.opm }}</legend>
                                            <img src="https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20220809/20220809bzdH4kBR.png" />
                                        </fieldset>
                                    </template>
                                </v-radio>
                            </v-radio-group>
                        </div>
                        <!--延迟支付表单-->
                        <div class="payLater-box mb-2" v-show="payTypeId === 0">
                            <v-form ref="payLaterForm">
                                <v-autocomplete v-model="payLater" :rules="payLaterRules" :items="payLaterList" label="Province" hide-details="auto" dense solo flat outlined item-text="name" item-value="name"></v-autocomplete>
                            </v-form>
                        </div>
                    </div>
                    <!--非子订单或者已下单但是没地址-->
                    <div>
                        <!--fd网站隐藏地址-2024-00-02-->
                        <div class="box shipping-box mb-6" v-if="!hideAddress">
                            <div class="box-title">
                                {{ langCart.sa }}
                            </div>
                            <div class="apoText" v-show="addressStatus === 1 && showAPOText">
                                <div>{{ langCart.apoAddressText }}</div>
                                <b class="icon-a-chahao6" @click="showAPOText=!showAPOText"></b>
                            </div>
                            <v-radio-group :disabled="!disabledAddress" class="my-2 pt-0" hide-details dense v-model="addressStatus" row>
                                <v-radio :label="langCart.shippingAddress" :value="0"></v-radio>
                                <v-radio :label="langCart.apoAddress" :value="1" v-show="!hideApoAddress" @click="apoAddressClick"></v-radio>
                            </v-radio-group>
                            <div>
                                <template v-if="addressStatus === 0">
                                    <v-form ref="shippingAddressForm" :disabled="!disabledAddress">
                                        <v-row dense>
                                            <v-col cols="12" v-if="showEmailInput">
                                                <v-text-field v-model="shippingAddressForm.email" :rules="shippingAddressFormRules.email" :error-messages="emailErrorMessage" dense solo hide-details="auto" @change="isRegister">
                                                    <template #label><span class="xin">*</span>{{ langCart.email }} </template>
                                                </v-text-field>
                                            </v-col>
                                            <v-col cols="12" v-if="isLogin">
                                                <v-autocomplete autocomplete="new-password" v-model="userShippingAddress" :items="userShippingAddressList" dense solo :label="langCart.myAddress" hide-details="auto" item-value="id" @change="changeUserShippingAddress">
                                                    <template v-slot:selection="{ item }">
                                                        {{ item.customShowAddress }}
                                                    </template>
                                                    <template v-slot:item="{ item }">
                                                        {{ item.customShowAddress }}
                                                    </template>
                                                </v-autocomplete>
                                            </v-col>
                                            <v-col cols="6">
                                                <v-text-field v-model="shippingAddressForm.shippingFirstName" :rules="shippingAddressFormRules.shippingFirstName" dense solo hide-details="auto">
                                                    <template #label><span class="xin">*</span>{{ langCart.firstName }} </template>
                                                </v-text-field>
                                            </v-col>
                                            <v-col cols="6">
                                                <v-text-field v-model="shippingAddressForm.shippingLastName" :rules="!isPopSite ? shippingAddressFormRules.shippingLastName : []" dense solo hide-details="auto">
                                                    <template #label><span class="xin">*</span>{{ langCart.lastName }} </template>
                                                </v-text-field>
                                            </v-col>

                                            <!-- fd网站展示Shipping Company -->
                                            <v-col cols="12" v-if="isFdSite">
                                                <v-text-field v-model="shippingAddressForm.shippingCompany" :error-messages="errorMessageObj.shippingCompany" dense solo hide-details="auto">
                                                    <template #label><span class="xin">*</span>Shipping Company </template>
                                                </v-text-field>
                                            </v-col>

                                            <v-col cols="12" class="d-flex align-items-center">
                                                <v-combobox v-model="shippingAddressForm.shippingAddressLine1" :loading="loadGoogleAddress" :error-messages="errorMessageObj.shippingLine1" :rules="shippingAddressFormRules.shippingAddressLine1" :items="predictions" item-text="mainTextCopy" item-value="mainText" :return-object="false" hide-no-data no-filter :search-input.sync="searchText" @update:search-input="debounceGetSpecificAddress" dense solo hide-details="auto" @change="setAddress">
                                                    <template #label>
                                                        <span class="xin">*</span>{{ langCart.addressLine1 }}
                                                        <span class="d-none d-md-inline-block">({{ langCart.example }})</span>
                                                    </template>
                                                    <template #item="{ item }">
                                                        <span v-html="item.mainTextCopy"></span>
                                                    </template>
                                                </v-combobox>
                                                <v-icon color="primary" class="ml-3 pointer" v-show="!shippingAddressLine2" @click="shippingAddressLine2 = true"> mdi-plus-circle-outline </v-icon>
                                            </v-col>
                                            <v-col cols="12" v-show="shippingAddressLine2">
                                                <v-text-field v-model="shippingAddressForm.shippingAddressLine2" :rules="shippingAddressFormRules.shippingAddressLine2" dense solo hide-details="auto">
                                                    <template #label>
                                                        {{ langCart.addressLine2 }}
                                                        <span class="d-none d-md-inline-block">({{ langCart.example }})</span>
                                                    </template>
                                                </v-text-field>
                                            </v-col>
                                            <v-col cols="12" class="d-md-none hint ml-1">
                                                {{ langCart.example }}
                                            </v-col>
                                            <v-col cols="12" sm="12" lg="6">
                                                <v-text-field v-model="shippingAddressForm.shippingCity" :error-messages="errorMessageObj.shippingCity" :rules="shippingAddressFormRules.shippingCity" dense solo label="*City" hide-details="auto">
                                                    <template #label><span class="xin">*</span>{{ langCart.city }} </template>
                                                </v-text-field>
                                            </v-col>
                                            <v-col cols="6" sm="6">
                                                <v-autocomplete autocomplete="new-password" :filter="filterName" v-model="shippingAddressForm.shippingCountryId" :error-messages="errorMessageObj.shippingCountry" :rules="shippingAddressFormRules.shippingCountryId" :items="countryList" dense solo hide-details="auto" item-text="countryName" item-value="id" @change="selectShippingCountry">
                                                    <template #label><span class="xin">*</span>{{ langCart.destination }} </template>
                                                </v-autocomplete>
                                            </v-col>
                                            <v-col cols="6" sm="6" lg="4" v-if="shippingStateList && shippingStateList.length > 0 && showshippingAddressDe">
                                                <v-autocomplete autocomplete="new-password" validate-on-blur :filter="filterName" v-model="shippingAddressForm.shippingStateId" :error-messages="errorMessageObj.shippingState" @change="changeShippingState" :rules="shippingAddressFormRules.shippingStateId" :items="shippingStateList" dense solo hide-details="auto" :item-text="formatShippingState" item-value="id">
                                                    <template #label><span class="xin">*</span>{{ langCart.state }} </template>
                                                </v-autocomplete>
                                            </v-col>
                                            <v-col cols="6" sm="6" lg="4" v-else-if="showshippingAddressDe && !shippingStateList.length">
                                                <v-text-field v-model="shippingAddressForm.shippingState" :error-messages="errorMessageObj.shippingState" :rules="shippingAddressFormRules.shippingState" dense solo hide-details="auto">
                                                    <template #label><span class="xin">*</span>{{ langCart.state }} </template>
                                                </v-text-field>
                                            </v-col>
                                            <v-col cols="6" sm="6" lg="4">
                                                <v-text-field v-model="shippingAddressForm.shippingZipCode" :error-messages="errorMessageObj.shippingCode" :rules="shippingAddressFormRules.shippingZipCode" dense solo hide-details="auto" @input="inputZipPostal">
                                                    <template #label><span class="xin">*</span>{{ langCart.zipCode }} </template>
                                                </v-text-field>
                                            </v-col>
                                            <v-col cols="6" sm="6" lg="4" flex class="bullNum">
                                                <div style="position: relative; width: 27%; height: 34px">
                                                    <img src="https://static-oss.gs-souvenir.com/web/quoteManage/20231228/image-removebg-preview_24_20634Zmnah.png" />
                                                    <v-text-field v-model="shippingPrefixNumber" :rules="shippingAddressFormRules.shippingPrefixNumber" dense solo hide-details="auto" :prefix="'+'" class="bill">
                                                        <template #label><span class="xin">*</span></template>
                                                    </v-text-field>
                                                </div>
                                                <!-- @input="debounceShippingPhoneNumberInput(shippingAddressForm.shippingPhoneNumber)" -->
                                                <v-text-field v-model="shippingAddressForm.shippingPhoneNumber" :rules="shippingAddressFormRules.shippingPhoneNumber" dense solo hide-details="auto" class="bill2 sms">
                                                    <!-- <template #label><span class="xin">*</span>{{ langCart.phoneNumber }} </template> -->
                                                    <template #label>
                                                        <span class="xin">*</span><span style="font-size: 12px">{{ langCart.EnterNumber }}</span></template
                                                    >
                                                </v-text-field>
                                                <div class="SMStextPC" v-show="showSMStext === true">
                                                    <v-checkbox v-model="isSMSCheckbox" :disabled="false"></v-checkbox>
                                                    {{ langCart.SMSText }}
                                                </div>
                                            </v-col>
                                            <v-col cols="12" class="SMStextMb">
                                                <div class="SMStextMb" v-show="showSMStext === true">
                                                    <v-checkbox v-model="isSMSCheckbox"></v-checkbox>
                                                    {{ langCart.SMSText }}

                                                </div>
                                            </v-col>

                                            <!-- pop网站不显示 -->
                                            <v-col cols="12" v-if="!isPopSite">
                                                <v-checkbox style="flex-grow: 0; display: inline-block" class="mt-0" v-model="saveAddress" :label="langCart.saveAddress" hide-details :true-value="1" :false-value="0"></v-checkbox>
                                            </v-col>
                                        </v-row>
                                    </v-form>
                                </template>
                                <template v-else-if="addressStatus === 1">
                                    <v-form ref="shippingAddressForm" :disabled="!disabledAddress">
                                        <v-row dense>
                                            <v-col cols="12" v-if="showEmailInput">
                                                <v-text-field v-model="shippingAddressForm.email" :rules="shippingAddressFormRules.email" :error-messages="emailErrorMessage" dense solo hide-details="auto" @change="isRegister">
                                                    <template #label><span class="xin">*</span>{{ langCart.email }} </template>
                                                </v-text-field>
                                            </v-col>
                                            <v-col cols="12" v-if="isLogin">
                                                <v-autocomplete v-model="userApoAddress" autocomplete="new-password" :items="userApoAddressList" dense solo :label="langCart.myAddress" hide-details="auto" item-value="id" @change="changeUserApoAddress">
                                                    <template v-slot:selection="{ item }">
                                                        {{ item.customShowAddress }}
                                                    </template>
                                                    <template v-slot:item="{ item }">
                                                        {{ item.customShowAddress }}
                                                    </template>
                                                </v-autocomplete>
                                            </v-col>
                                            <v-col cols="6">
                                                <v-text-field v-model="shippingAddressForm.shippingFirstName" :rules="shippingAddressFormRules.shippingFirstName" dense solo hide-details="auto"
                                                @change="apoPaceiInput">
                                                    <template #label><span class="xin">*</span>{{ langCart.firstName }} </template>
                                                </v-text-field>
                                            </v-col>
                                            <v-col cols="6">
                                                <v-text-field v-model="shippingAddressForm.shippingLastName" :rules="shippingAddressFormRules.shippingLastName" dense solo hide-details="auto"
                                                @change="apoPaceiInput">
                                                    <template #label><span class="xin">*</span>{{ langCart.lastName }} </template>
                                                </v-text-field>
                                            </v-col>
                                            <v-col cols="12">
                                                <v-text-field v-model="shippingAddressForm.apoAddress" :rules="shippingAddressFormRules.apoAddressRules" @change="changeApoAddress" dense solo hide-details="auto"
                                                >
                                                    <template #label><span class="xin">*</span>{{ langCart.apoAddress2 }} </template>
                                                </v-text-field>
                                            </v-col>
                                            <v-col sm="6" lg="4" cols="6" flex class="bullNum bullNum2">
                                                <div style="position: relative; width: 27%; height: 34px">
                                                    <img src="https://static-oss.gs-souvenir.com/web/quoteManage/20231228/image-removebg-preview_24_20634Zmnah.png" />
                                                    <v-text-field v-model="shippingPrefixNumber" :rules="shippingAddressFormRules.shippingPrefixNumber" dense solo hide-details="auto" :prefix="'+'" class="bill3"
                                                    @change="apoPaceiInput">
                                                        <template #label><span class="xin">*</span></template>
                                                    </v-text-field>
                                                </div>

                                                <v-text-field v-model="shippingAddressForm.shippingPhoneNumber" :rules="shippingAddressFormRules.shippingPhoneNumber" dense solo hide-details="auto" class="bill2"
                                                @change="apoPaceiInput">
                                                    <template #label><span class="xin">*</span>{{ langCart.phoneNumber }} </template>
                                                </v-text-field>
                                            </v-col>
                                            <v-col sm="6" lg="4" cols="6">
                                                <v-autocomplete autocomplete="new-password" :filter="filterName" v-model="shippingAddressForm.shippingApoCity" :error-messages="errorMessageObj.shippingApoCity"
                                                :rules="shippingAddressFormRules.shippingApoCity" :items="APOCityList" dense solo hide-details="auto" item-text="name" item-value="name"
                                                @change="apoPaceiInput">
                                                    <template #label><span class="xin">*</span>{{ langCart.city }} </template>
                                                </v-autocomplete>
                                            </v-col>
                                            <v-col sm="12" lg="4">
                                                <v-autocomplete autocomplete="new-password" validate-on-blur :filter="filterName" v-model="shippingAddressForm.shippingApoState"
                                                :error-messages="errorMessageObj.shippingApoState" :rules="shippingAddressFormRules.shippingApoState" :items="APOStateList"
                                                dense solo hide-details="auto" item-text="name" item-value="name"
                                                @change="apoPaceiInput">

                                                    <template #label><span class="xin">*</span>{{ langCart.state }} </template>
                                                </v-autocomplete>
                                            </v-col>
                                            <v-col sm="6" lg="6">
                                                <v-text-field v-model="shippingAddressForm.shippingApoZip" :error-messages="errorMessageObj.shippingApoZip"
                                                :rules="shippingAddressFormRules.shippingApoZip" dense solo hide-details="auto"
                                                @change="apoPaceiInput">
                                                    <template #label><span class="xin">*</span>{{ langCart.zipCode }} </template>
                                                </v-text-field>
                                            </v-col>

                                            <v-col cols="12" v-if="!isPopSite">
                                                <v-checkbox class="mt-0" v-model="saveAddress" :label="langCart.saveAddress" hide-details :true-value="1" :false-value="0"></v-checkbox>
                                            </v-col>
                                        </v-row>
                                    </v-form>
                                </template>
                            </div>
                        </div>
                        <div class="box billing-box mb-3 pb-4" v-if="payTypeId !== 10000 && !isPopSite && !hideAddress">
                            <div class="box-title">
                                {{ langCart.billAddress }}
                            </div>
                            <v-radio-group :disabled="!disabledAddress" class="my-2 pt-0" hide-details dense v-model="billingAddressStatus" @change="changeAddressStatus" row>
                                <v-radio :label="langCart.t1" :value="0"></v-radio>
                                <v-radio :label="langCart.t2" :value="1"></v-radio>
                            </v-radio-group>
                            <div v-if="billingAddressStatus === 1">
                                <v-form ref="billAddressForm" :disabled="!disabledAddress">
                                    <v-row dense>
                                        <v-col cols="6">
                                            <v-text-field v-model="billingAddressForm.billFirstName" :rules="billingAddressFormRules.billFirstName" dense solo hide-details="auto">
                                                <template #label><span class="xin">*</span>{{ langCart.firstName }} </template>
                                            </v-text-field>
                                        </v-col>
                                        <v-col cols="6">
                                            <v-text-field v-model="billingAddressForm.billLastName" :rules="billingAddressFormRules.billLastName" dense solo hide-details="auto">
                                                <template #label><span class="xin">*</span>{{ langCart.lastName }} </template>
                                            </v-text-field>
                                        </v-col>
                                        <v-col cols="12" class="d-flex align-items-center">
                                            <v-combobox v-model="billingAddressForm.billAddressLine1" :error-messages="errorMessageObj.billingLine1" :loading="loadGoogleAddress2" :rules="billingAddressFormRules.billAddressLine1" :items="predictions2" item-text="mainTextCopy2" item-value="mainText" :return-object="false" :search-input.sync="searchText2" @update:search-input="debounceGetSpecificAddress2" hide-no-data no-filter dense solo hide-details="auto" @change="setAddress2">
                                                <template #label>
                                                    <span class="xin">*</span>{{ langCart.addressLine1 }}
                                                    <span class="d-none d-md-inline-block">({{ langCart.example }})</span>
                                                </template>
                                                <template #item="{ item }">
                                                    <span v-html="item.mainTextCopy2"></span>
                                                </template>
                                            </v-combobox>

                                            <v-icon color="primary" class="ml-3 pointer" v-show="!billingAddressLine2" @click="billingAddressLine2 = true"> mdi-plus-circle-outline </v-icon>
                                        </v-col>
                                        <v-col cols="12" v-show="billingAddressLine2">
                                            <v-text-field v-model="billingAddressForm.billAddressLine2" :rules="billingAddressFormRules.billAddressLine2" dense solo hide-details="auto">
                                                <template #label>
                                                    {{ langCart.addressLine2 }} <span class="d-none d-md-inline-block">({{ langCart.example }})</span>
                                                </template>
                                            </v-text-field>
                                        </v-col>
                                        <v-col cols="12" class="d-md-none hint ml-1">
                                            {{ langCart.example }}
                                        </v-col>
                                        <v-col cols="12" sm="12" lg="6">
                                            <v-text-field v-model="billingAddressForm.billCity" :error-messages="errorMessageObj.billingCity" :rules="billingAddressFormRules.billCity" dense solo hide-details="auto">
                                                <template #label><span class="xin">*</span>{{ langCart.city }} </template>
                                            </v-text-field>
                                        </v-col>
                                        <v-col cols="6" sm="6">
                                            <v-autocomplete autocomplete="new-password" :filter="filterName" v-model="billingAddressForm.billCountryId" :error-messages="errorMessageObj.billingCountry" :rules="billingAddressFormRules.billCountryId" :items="countryList" dense solo hide-details="auto" item-text="countryName" item-value="id" @change="selectBillingCountry">
                                                <template #label><span class="xin">*</span>{{ langCart.destination }} </template>
                                            </v-autocomplete>
                                        </v-col>
                                        <v-col cols="6" sm="6" lg="4" v-if="billStateList && billStateList.length && showBillingAddressDe">
                                            <v-autocomplete autocomplete="new-password" validate-on-blur :filter="filterName" v-model="billingAddressForm.billStateId" :error-messages="errorMessageObj.billingState" :rules="billingAddressFormRules.billStateId" :items="billStateList" dense solo hide-details="auto" :item-text="formatbillState" item-value="id">
                                                <template #label><span class="xin">*</span>{{ langCart.state }} </template>
                                            </v-autocomplete>
                                        </v-col>
                                        <v-col cols="6" sm="6" lg="4" v-else-if="showBillingAddressDe && !billStateList.length">
                                            <v-text-field v-model="billingAddressForm.billState" :rules="billingAddressFormRules.billState" :error-messages="errorMessageObj.billingState" dense solo hide-details="auto">
                                                <template #label><span class="xin">*</span>{{ langCart.state }} </template>
                                            </v-text-field>
                                        </v-col>
                                        <v-col cols="6" sm="6" lg="4">
                                            <v-text-field v-model="billingAddressForm.billZipCode" :error-messages="errorMessageObj.billingCode" :rules="billingAddressFormRules.billZipCode" dense solo hide-details="auto">
                                                <template #label><span class="xin">*</span>{{ langCart.zipCode }} </template>
                                            </v-text-field>
                                        </v-col>
                                        <v-col cols="6" sm="6" lg="4" flex class="bullNum">
                                            <div style="position: relative; width: 27%; height: 34px">
                                                <img src="https://static-oss.gs-souvenir.com/web/quoteManage/20231228/image-removebg-preview_24_20634Zmnah.png" />
                                                <v-text-field v-model="billingPrefixNumber" :rules="billingAddressFormRules.billingPrefixNumber" dense solo hide-details="auto" :prefix="'+'" class="bill">
                                                    <template #label><span class="xin">*</span></template>
                                                </v-text-field>
                                            </div>
                                            <v-text-field v-model="billingAddressForm.billPhoneNumber" :rules="billingAddressFormRules.billPhoneNumber" dense solo hide-details="auto" class="bill2">
                                                <template #label><span class="xin">*</span>{{ langCart.phoneNumber }} </template>
                                            </v-text-field>
                                        </v-col>
                                    </v-row>
                                </v-form>
                            </div>
                        </div>
                    </div>
                    <!-- 手机端 -->
                    <div class="box delivery-box">
                        <div class="box-title">{{ langCart.shippingDelivery }}</div>
                        <div class="mt-2 mt-lg-4 mb-lg-2">
                            <span style="color: #666666">{{ langCart.shippingMethod }}: </span>
                            <span v-if="isSampleOrder === 2 || isSampleOrder === 3">{{ langCart.freeGroundShipping }}</span>
                            <span v-else>{{ langCart.fed }}</span>
                            <span style="color: #de3500" v-if="!isPopSite && isSampleOrder !== 2 && isSampleOrder !== 3">({{ langCart.free }})</span>
                        </div>
                        <img :src="language === 'de' ? 'https://static-oss.gs-souvenir.com/web/quoteManage/20241008/%E5%BE%B7%E8%AF%AD%E7%89%88_2034087MjGw4.png' : 'https://static-oss.gs-souvenir.com/web/quoteManage/20241008/%E8%8B%B1%E8%AF%AD%E7%89%88_203408Kbm34y.png'" alt="" style="width: auto;max-width:100%;" />
                    </div>
                    <div class="subtotal-con" :style="showSMStext === true && addressStatus===0 ? 'margin-top: 50px;' : ''">
                        <div class="subtotal">
                            <div class="sub-item subtotalPrice">
                                <div class="label">
                                    {{ langCart.subtotal }}
                                </div>
                                <div class="con">
                                    <CCYRate :price="productPrice" :ccy="currentCurrency"></CCYRate>
                                </div>
                            </div>
                            <!-- pop额外费用  -->
                            <div class="sub-item" v-if="(isPopSite || (!isPopSite && isSampleOrder === 2)) && markupPercentage !== 0 && markupPercentage !== null">
                                <div class="label">Freight</div>
                                <div class="con">
                                    +
                                    <CCYRate :price="markupPercentage" :ccy="currentCurrency"></CCYRate>
                                </div>
                            </div>
                            <div class="sub-item" v-if="isPopSite && freightCharge !== 0 && freightCharge !== null">
                                <div class="label">
                                    Add Fee<span>({{ (addFeePercentage * 100).toFixed(2) }}%)</span>:
                                </div>
                                <div class="con">
                                    +
                                    <CCYRate :price="freightCharge" :ccy="currentCurrency"></CCYRate>
                                </div>
                            </div>
                            <!--折扣文字显示-->
                            <div class="sub-item discount" v-if="firstDiscountPrice">
                                <div class="label">{{ langCart.fod }}({{ (firstDiscount * 100).toFixed(0) }}{{ langCart.off }}):</div>
                                <div class="con">
                                    -
                                    <CCYRate :price="firstDiscountPrice" :ccy="currentCurrency"></CCYRate>
                                </div>
                            </div>
                            <!--订单折扣-->
                            <div class="sub-item" v-if="Number(orderDiscountPrice)">
                                <div class="label">{{ langCart.orderDiscount }}({{ (orderDiscount * 100).toFixed(0) }}{{ langCart.off }}):</div>
                                <div class="con">
                                    -
                                    <CCYRate :price="orderDiscountPrice" :ccy="currentCurrency"></CCYRate>
                                </div>
                            </div>
                            <!--额外费用(佣金)-->
                            <div class="sub-item" v-if="Number(payAdditionalFee)">
                                <div class="label">{{ langCart.additionalFee }}:</div>
                                <div class="con">
                                    +
                                    <CCYRate :price="payAdditionalFee" :ccy="currentCurrency"></CCYRate>
                                </div>
                            </div>
                            <!--运费-->
                            <div class="sub-item" v-if="Number(shippingChargesPrice)">
                                <div class="label">{{ langCart.shippingCharges }}:</div>
                                <div class="con">
                                    +
                                    <CCYRate :price="shippingChargesPrice" :ccy="currentCurrency"></CCYRate>
                                </div>
                            </div>
                            <!--apo运费
                                apoPaymentStatus：0；后付状态
                                后付状态：shipping address 和 apo address 都显示
                                非后付状态：apo address显示
                                fd/pop: apoShippingPrice大于0的时候显示
                                只有主单显示，子订单不显示
                            -->
                            <div class="sub-item"
                            v-if="((apoPaymentStatus === 0 && ((!isPopSite && !isFdSite && addressStatus === 1) ||
                            (!isPopSite && !isFdSite && addressStatus === 0) ||
                            (isPopSite && apoShippingPrice > 0) ||
                            (isFdSite && apoShippingPrice > 0))) ||
                            (apoPaymentStatus !== 0 && addressStatus === 1)) && isChildOrder === false"
                            >
                                <div class="label">{{ langCart.apoCharges }}:</div>
                                <div class="con" v-if="!checkFormCompletion(shippingAddressForm,properties) && addressStatus === 1">
                                    In the calculation...
                                </div>
                                <div class="con" v-else>
                                    +
                                    <CCYRate :price="apoShippingPrice" :ccy="currentCurrency"></CCYRate>
                                </div>
                            </div>

                             <!--美国关税  -->
                             <div class="sub-item"
                                v-if="((additionalTariffFees && addressStatus === 1 && showadditionalTariffFees) ||
                                    (additionalTariffFees && addressStatus === 0 && shippingAddressForm.shippingCountryId)) &&
                                    isChildOrder === false
                                ">
                                <!--								<div class="label">{{ taxName1 }}: <span v-if="VATtext">({{ langCart.vatTax }})</span></div>-->

                                <div class="label">
                                    {{ langCart.tariffSurcharge }}:
                                </div>
                                <div class="con">
                                    +
                                    <CCYRate :price="additionalTariffFees" :ccy="currentCurrency"></CCYRate>
                                </div>
                            </div>

                            <!--税费-->
                            <div class="sub-item" v-if="Number(taxPrice) && addressStatus === 0 && shippingAddressForm.shippingCountryId">
                                <!--								<div class="label">{{ taxName1 }}: <span v-if="VATtext">({{ langCart.vatTax }})</span></div>-->

                                <div class="label">
                                    {{ taxName1 }} <span v-if="VATtext">({{ langCart.vatTax }})</span><span v-if="currentTaxRate"> ({{ langCart.taxRate }} {{ (currentTaxRate * 100).toFixed(2) }}%)</span>:
                                </div>
                                <div class="con">
                                    +
                                    <CCYRate :price="taxPrice" :ccy="currentCurrency"></CCYRate>
                                </div>
                            </div>
                            <!--额外折扣-->
                            <!-- <div class="sub-item" v-if="Number(promotionPrice)">
								<div class="label">{{ langCart.extraDiscount }}({{ (promotionDiscount * 100).toFixed(0) }}{{ langCart.off }}):</div>
								<div class="con">
									-
									<CCYRate :price="promotionPrice" :ccy="currentCurrency"></CCYRate>
								</div>
							</div> -->
                            <div class="sub-item mb-2 mb-lg-3"
                                 v-for="(item,index) in ordersPromotionDiscounts"
                                 :key="index">
                                <div style="flex:5">{{ item.activityName }}</div>
                                <div class="con" style="color: #de3500">
                                    -
                                    <CCYRate :price="item.discountPrice" :ccy="currentCurrency"></CCYRate>
                                </div>
                            </div>

                            <!--用户等级折扣 -->
                            <div class="sub-item" v-if="Number(userLevelDiscountPrice)">
                                <div class="label">{{ langCart["userDiscount" + userLevelId] }}({{ (userLevelDiscount * 100).toFixed(0) }}{{ langCart.off }}):</div>
                                <div class="con">
                                    -
                                    <CCYRate :price="userLevelDiscountPrice" :ccy="currentCurrency"></CCYRate>
                                </div>
                            </div>

                            <!--额外费用-->
                            <!-- <div class="sub-item" v-if="Number(couponAdditionalCosts)">
								<div class="label">{{ langCart.additionalCosts }}:</div>
								<div class="con">
									+
									<CCYRate :price="couponAdditionalCosts" :ccy="currentCurrency"></CCYRate>
								</div>
							</div> -->
                            <!-- 促销中心额外收费 -->
                            <div class="sub-item" v-if="getadditionalPrice(ordersPromotionDiscounts,couponAdditionalCosts)">
                                <div class="label">{{ langCart.promotionCenter }}:</div>
                                <div class="con">
                                    +
                                    <CCYRate :price="getadditionalPrice(ordersPromotionDiscounts,couponAdditionalCosts)" :ccy="currentCurrency"></CCYRate>
                                </div>
                            </div>

                            <!--优惠码与优惠券-->
                            <template v-if="(showCouponCode || showCouponOption) && !isStickerSite && !isPopSite && !isPensSite && showCouponAll && !isFdSite && !isCouponAllowed">
                                <div class="border-2 my-2" v-if="showCouponCode || showCouponOption"></div>
								<div v-show="codeInvalidInfo">
									<span style="color:#DE3500">{{ langCart.subscribeTips }}</span>
								</div>
                                <v-radio-group hide-details dense v-model="radioGroup" multiple style="margin-top: 0">
                                    <div class="d-flex align-items-center discount-item" v-if="showCouponCode">
                                        <v-radio :value="1">
                                            <template #label> {{ langCart.applyOption }} : </template>
                                        </v-radio>
                                        <div class="discount" v-if="radioGroup.includes(1)">
                                            <div class="d-flex align-center">
                                                <v-text-field style="border-top-right-radius: 0; border-bottom-right-radius: 0" v-model="nCouponCodeTemp" :disabled="disabledCouponCode" dense solo placeholder="Enter coupon code" hide-details="auto"></v-text-field>
                                                <v-btn height="34" tile color="primary" :disabled="disabledCouponCode" depressed @click="useCoupon">
                                                    {{ langCart.use }}
                                                </v-btn>
                                            </div>
                                            <div class="d-flex align-center"></div>
                                            <div class="con">
                                                <!--												- -->
                                                <!--												<CCYRate :price="crowdDiscountPrice" :ccy="currentCurrency"></CCYRate>-->
                                            </div>
                                        </div>
                                    </div>

                                    <!--优惠券"-->
                                    <div class="d-flex align-items-center discount-item" v-if="showCouponOption">
                                        <v-radio :value="2">
                                            <template #label> {{ langCart.couponDiscount }}:</template>
                                        </v-radio>
                                        <div class="discount">
                                            <div class="d-flex align-center">
                                                <v-autocomplete
                                                        :filter="filterName"
                                                        v-model="couponIdAsNumber"
                                                        :items="couponOption"
                                                        v-if="!disabledCouponOption"
                                                        clearable
                                                        dense
                                                        solo
                                                        hide-details
                                                        item-text="title"
                                                        :item-value="uniqueId"
                                                        @input="onInput">
                                                </v-autocomplete>
                                            </div>
                                            <div class="con" style="text-align: right;" v-show="showPrice === true">
                                                -
                                                <CCYRate :price="couponDiscountPrice" :ccy="currentCurrency"></CCYRate>
                                            </div>
                                        </div>
                                    </div>
                                </v-radio-group>
                            </template>

                            <!--pens网站 -- 1美金pens ---->
                            <template  v-if="(showCouponOptionPens || showCouponCodeP) && (isStickerSite || isPensSite || isCouponAllowed)">
                                <div class="border-2 my-2" v-if="(showCouponOptionPens || showCouponCodeP) && showcouponJsonDiscountPrice"></div>
								<div v-show="codeInvalidInfo">
									<span style="color:#DE3500">{{ langCart.subscribeTips }}</span>
								</div>

                                <v-radio-group hide-details dense v-model="radioGroup" multiple style="margin-top: 0" v-if="showcouponJsonDiscountPrice">
                                    <div class="discount-item-div">
                                        <v-radio :value="1" style="margin-bottom:0">
                                            <template #label> Choose A Coupon or Code: </template>
                                        </v-radio>
                                        <div class="" v-if="radioGroup.includes(1)" style="width: 100%">
                                            <!-- 优惠金额不为空，为空回显金额 -->
                                            <div class="discount" v-if="!isOrder">
                                                <!-- 如果没有优惠卷或者选择的Enter coupon code -->
                                                <div class="d-flex align-center" v-if="couponList.length === 0 || couponId === 3">
                                                    <v-text-field style="border-top-right-radius: 0; border-bottom-right-radius: 0" v-model="nCouponCodeTemp" :disabled="disabledCouponCode" dense solo placeholder="Enter coupon code" hide-details="auto"> </v-text-field>
                                                    <v-btn height="34" tile color="primary" :disabled="disabledCouponCode" depressed @click.stop="useCoupon">
                                                        {{ langCart.use }}
                                                    </v-btn>
                                                </div>
                                                <!-- 展示选中的优惠卷名称 -->
                                                <div class="d-flex align-center" v-else @click="showcoupon = !showcoupon">
                                                    <span v-html="getcouponName()"></span>
                                                </div>

                                                <div v-show="couponList.length" style="width: 26%; text-align: right">
                                                    <v-icon @click="showcoupon = false" v-show="showcoupon === true" class="couponicon"> mdi-chevron-down </v-icon>
                                                    <v-icon @click="showcoupon = true" v-show="showcoupon === false" class="couponicon"> mdi-chevron-right </v-icon>
                                                </div>

                                                <div class="con" v-if="couponList.length !== 0 && couponId !== 3 && couponApplyType !== 1 && showPrice">
                                                    -
                                                    <CCYRate :price="couponDiscountPrice" :ccy="currentCurrency"></CCYRate>
                                                </div>
                                            </div>
                                            <div class="con" v-else>
                                                <div class="d-flex align-center" style="justify-content: space-between">
                                                    <div style="color: #333">
                                                        {{ couponJson }}
                                                    </div>
                                                    <div v-if="couponList.length !== 0 && couponId !== 3 && couponApplyType !== 1" style="margin-left: 20px; width: 25%">
                                                        -
                                                        <CCYRate :price="couponDiscountPrice" :ccy="currentCurrency"></CCYRate>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="radio-group-div" v-show="showcoupon">
                                        <v-radio-group v-model="couponId">
                                            <v-radio :value="item.uniqueId" v-for="(item, index) in couponList" :key="index" @click="couponRadio(item)">
                                                <template v-slot:label style="display: block">
													<span>
														{{ item.couponType === 4 ? item.conditionDescription : item.title }}
													</span>
                                                    <span style="color: #666666" v-show="item.validityEndTime"> ( {{ langCart.deadline }}:{{ item.validityEndTime }} ) </span>
                                                </template>
                                            </v-radio>
                                        </v-radio-group>
                                    </div>
                                </v-radio-group>
                            </template>


                            <!--代金券与积分抵扣-->
                            <template v-if="showVoucherOption || showPoint">
                                <div class="border-2 my-2" v-if="showVoucherOption || showPoint"></div>
                                <v-radio-group hide-details dense v-model="radioGroup" multiple style="margin-top: 0">
                                    <!--代金券-->
                                    <div class="d-flex align-items-center discount-item" v-if="showVoucherOption">
                                        <v-radio :value="3">
                                            <template #label> {{ langCart.voucherDiscount }} {{ voucherId }}:</template>
                                        </v-radio>
                                        <div class="discount">
                                            <div class="d-flex align-center">
                                                <v-autocomplete autocomplete="new-password" :filter="filterName" v-model="voucherId" :items="voucherOption" v-if="!disabledVoucherOption" clearable dense solo hide-details item-value="id">
                                                    <template v-slot:selection="data">
                                                        <CCYRate :price="data.item.cashValue" :ccy="currentCurrency"></CCYRate>
                                                    </template>
                                                    <template v-slot:item="data">
                                                        <CCYRate :price="data.item.cashValue" :ccy="currentCurrency"></CCYRate>
                                                    </template>
                                                </v-autocomplete>
                                            </div>
                                            <div class="con">
                                                -
                                                <CCYRate :price="voucherPrice" :ccy="currentCurrency"></CCYRate>
                                            </div>
                                        </div>
                                    </div>
                                    <!--优惠积分-->
                                    <div class="d-flex align-items-center discount-item" v-if="showPoint">
                                        <v-radio :value="4">
                                            <template #label> {{ langCart.pointDeduction }}:</template>
                                        </v-radio>
                                        <div class="discount">
                                            <div class="d-flex align-center">
                                                <v-text-field v-model="pointDiscountPrice" type="number" v-if="!disabledPoint" dense solo hide-details append-icon="mdi-square-edit-outline" :placeholder="langCart.maxinum + currentCurrency.symbol + pointMaxPriceOrNoPointTotalPrice"></v-text-field>
                                            </div>
                                            <div class="con" v-if="!disabledPoint">
                                                -
                                                <CCYRate :price="pointDiscountPrice / currentCurrency.rate" :ccy="currentCurrency"></CCYRate>
                                            </div>
                                            <div class="con" v-else>
                                                -
                                                <CCYRate :price="pointDiscountPriceTemp" :ccy="currentCurrency"></CCYRate>
                                            </div>
                                        </div>
                                    </div>
                                </v-radio-group>
                            </template>
                            <!-- 优惠码提示 -->
                            <div class="coupoun-tip"
                            v-if="(!isStickerSite && Number(couponAdditionalCosts)) ||
                            (isStickerSite && isNewCoupon === 0) ||
                            (!isPensSite && Number(couponAdditionalCosts)) ||
                            (isPensSite && isNewCoupon === 0) ||
                            (!isCouponAllowed && Number(couponAdditionalCosts)) ||
                            (isCouponAllowed && isNewCoupon === 0)">
                                <img src="https://sticker-static.oss-accelerate.aliyuncs.com/image/uploads/20230825/tips-png-38055.png" alt="" />
                                <!-- <span>{{ langCart.Guidelines }}: {{ langCart.Ordersupto }} ${{ Number(crowdDiscountPrice).toFixed(2) }} {{ langCart.costjust }} ${{ Number(couponAdditionalCosts).toFixed(2) }}; {{ langCart.ordersexceeding }} ${{ Number(crowdDiscountPrice).toFixed(2) }} {{ langCart.suchasordersfor }} ${{ (Number(crowdDiscountPrice) + 5).toFixed(2) }}, {{ langCart.costonly }} ${{ (5 + Number(couponAdditionalCosts)).toFixed(2) }}(${{ (Number(crowdDiscountPrice) + 5).toFixed(2) }}-${{ Number(crowdDiscountPrice).toFixed(2) }}+${{ Number(couponAdditionalCosts).toFixed(2) }}).</span> -->

                                <span v-if="currencySymbol === 'EUR'">
                                    Richtlinien für die Verwendung des €1 für €75 Gutscheins:
                                    Bestellungen bis 0,00 € kosten nur 1,00 €; Bestellungen über 0,00 €, wie z. B. Bestellungen über 5,00 €, kosten nur 6,00 € (5,00 €-$0,00+1,00 €).
                                </span>
                                <span v-else>
                                    Guidelines for using the {{ getcurrencySymbol() }}1 for {{ getcurrencySymbol() }}75 voucher:
                                    Orders up to {{ getcurrencySymbol() }}{{ Number(crowdDiscountPrice).toFixed(2) }} cost just {{ getcurrencySymbol() }}{{ Number(couponAdditionalCosts).toFixed(2) }}; orders exceeding {{ getcurrencySymbol() }}{{ Number(crowdDiscountPrice).toFixed(2) }} such as orders for {{ getcurrencySymbol() }}{{ (Number(crowdDiscountPrice) + 5).toFixed(2) }}, cost only {{ getcurrencySymbol() }}{{ (5 + Number(couponAdditionalCosts)).toFixed(2) }}({{ getcurrencySymbol() }}{{ (Number(crowdDiscountPrice) + 5).toFixed(2) }}-{{ getcurrencySymbol() }}{{ Number(crowdDiscountPrice).toFixed(2) }}+{{ getcurrencySymbol() }}{{ Number(couponAdditionalCosts).toFixed(2) }}).
                                </span>
                            </div>
                        </div>
                        <div id="messages" role="alert"></div>
                    </div>
                </div>
                <div class="payBtnWrap text-center mt-2" :disabled="load">
                    <div class="b1 d-flex align-center">
                        <!--待支付价格-->
                        <div class="label font-weight-bold">{{ langCart.total }}:</div>
                        <div class="d-flex align-baseline">
                            <div class="symbol">
                                <strong>{{ symbolCode }}</strong>
                            </div>
                            <div class="unPaidPrice">
                                <CCYRate :price="(apoPaymentStatus !== 0 && addressStatus === 0) ? (totalPrice-apoShippingPrice) : (apoPaymentStatus !== 0 && addressStatus === 1) ?  (totalPrice-taxPrice) : totalPrice" :ccy="currentCurrency"></CCYRate>
                            </div>
                            <!-- <div class="unPaidPrice">

                                <CCYRate :price="(apoPaymentStatus !== 0 && addressStatus === 0 && additionalTariffFees !== 0 && shippingAddressForm.shippingCountryId) ? (totalPrice - apoShippingPrice) :
                                (apoPaymentStatus !== 0 && addressStatus === 0 && additionalTariffFees !== 0 && !shippingAddressForm.shippingCountryId) ? (totalPrice - apoShippingPrice - additionalTariffFees - taxPrice):
                                (apoPaymentStatus !== 0 && addressStatus === 1 && additionalTariffFees !== 0 && showadditionalTariffFees === false) ? (totalPrice - apoShippingPrice - additionalTariffFees - taxPrice) :
                                (additionalTariffFees !== 0 && addressStatus === 1 && showadditionalTariffFees === false) ? (totalPrice - apoShippingPrice - additionalTariffFees - taxPrice) :
                                (taxPrice !== 0 && addressStatus === 1 && additionalTariffFees !== 0 &&  apoPaymentStatus !== 0) ?  (totalPrice - taxPrice) :
                                (taxPrice !== 0 && addressStatus === 0 && additionalTariffFees !== 0 &&  apoPaymentStatus !== 0 && !shippingAddressForm.shippingCountryId)? (totalPrice - apoShippingPrice - additionalTariffFees - taxPrice) :
                                (taxPrice !== 0 && addressStatus === 1) ? (totalPrice - taxPrice) :
                                (taxPrice !== 0 && addressStatus === 0 && !shippingAddressForm.shippingCountryId) ? (totalPrice - taxPrice) : totalPrice " :ccy="currentCurrency"></CCYRate>
                            </div> -->
                        </div>
                    </div>
                    <div class="b2 d-flex align-center px-2">
                        <div v-if="!isFdSite">
                            <button v-ripple id="payment-button" class="checkoutBtn" @click="submitOrderBefore">
                                {{ langCart.pay }}
                            </button>
                        </div>
                        <div class="fd-btn" v-else>
                            <v-btn color="primary" @click="submitOrder" v-show="payTypeId !== 10000 || isZeroOrder"> PAY NOW </v-btn>
                            <!--paypal支付按钮-->
                            <div id="paypal-button-container" v-show="payTypeId === 10000 && !isZeroOrder"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <v-dialog style="z-index: 1000" max-width="600" persistent v-model="stripeStatus" class="pa-5">
            <div style="background: white" class="d-flex justify-center align-center">
                <form id="payment-form" class="stripeForm">
                    <div id="payment-element"></div>
                    <div class="btnWrap">
                        <button type="button" class="stripeButton cancelBtn" @click.stop="toOrderDetail">
                            <div class="spinner hidden"></div>
                            <span>{{ langCart.cancel }}</span>
                        </button>
                        <button id="submit" class="stripeButton">
                            <div class="spinner hidden" id="spinner"></div>
                            <span id="button-text">{{ langCart.payNow }}</span>
                        </button>
                    </div>
                </form>
            </div>
        </v-dialog>

        <v-dialog style="z-index: 1000" max-width="600" v-model="notFreeShipping" class="pa-5">
            <div style="background: white; position: relative" class="d-flex justify-center align-center pa-8">
                <span style="position: absolute; top: 10px; right: 10px; cursor: pointer"><v-icon class="close-icon" @click="notFreeShipping = false">mdi-close</v-icon></span>
                {{ langCart.noFreeShippingTips }}
            </div>
        </v-dialog>

        <v-dialog style="z-index: 1000" eager transition="dialog-top-transition" max-width="auto" width="auto" content-class="payBeforeDialog" v-model="payBeforeDialog">
            <v-card>
                <v-card-title> Confirm Your Address</v-card-title>
                <v-divider></v-divider>
                <v-card-text>
                    <div class="py-2 mb-4 small-title">Your order will be shipped to this address.</div>
                    <div class="addressInfo" v-if="addressStatus == 0">
                        <div class="addressItem custom-row mb-2">
                            <div class="item">
                                <span>{{ langCart.firstName }}:</span>
                                <span>{{ shippingAddressForm.shippingFirstName }}</span>
                            </div>
                            <div class="item">
                                <span>{{ langCart.lastName }}:</span>
                                <span>{{ shippingAddressForm.shippingLastName }}</span>
                            </div>
                        </div>
                        <div class="addressItem addressItem">
                            <div class="item mb-2">
                                <span>{{ langCart.addressLine1 }}:</span>
                                <span>{{ shippingAddressForm.shippingAddressLine1 }}</span>
                            </div>
                        </div>
                        <div class="addressItem">
                            <div class="item mb-2" v-show="shippingAddressForm.shippingAddressLine2">
                                <span>{{ langCart.addressLine2 }}:</span>
                                <span>{{ shippingAddressForm.shippingAddressLine2 }}</span>
                            </div>
                        </div>
                        <div class="addressItem custom-row mb-2">
                            <div class="item">
                                <span>{{ langCart.city }}:</span>
                                <span>{{ shippingAddressForm.shippingCity }}</span>
                            </div>
                            <div class="item">
                                <span>{{ langCart.destination }}:</span>
                                <span>{{ currentShippingCountry.countryName }}</span>
                            </div>
                        </div>
                        <div class="addressItem custom-row2">
                            <div class="item mb-2" v-if="(shippingStateList && shippingStateList.length > 0 && showshippingAddressDe) || (showshippingAddressDe && !shippingStateList.length)">
                                <span>{{ langCart.state }}:</span>
                                <span>{{ currentShippingState?.stateName || shippingAddressForm.shippingState }}</span>
                            </div>
                            <div class="item">
                                <span>{{ langCart.zipCode }}:</span>
                                <span>{{ shippingAddressForm.shippingZipCode }}</span>
                            </div>
                            <div class="item">
                                <span>{{ langCart.phoneNumber }}:</span>
                                <span>{{ shippingPrefixNumber }}-{{ shippingAddressForm.shippingPhoneNumber }}</span>
                            </div>
                        </div>
                        <div class="addressItem custom-row2" v-if="isFdSite">
                            <div class="item">
                                <span>Shipping Company:</span>
                                <span>{{ shippingAddressForm.shippingCompany }}</span>
                            </div>
                        </div>
                    </div>
                    <div class="addressInfo" v-if="addressStatus == 1">
                        <div class="addressItem custom-row mb-2">
                            <div class="item">
                                <span>{{ langCart.firstName }}:</span>
                                <span>{{ shippingAddressForm.shippingFirstName }}</span>
                            </div>
                            <div class="item">
                                <span>{{ langCart.lastName }}:</span>
                                <span>{{ shippingAddressForm.shippingLastName }}</span>
                            </div>
                        </div>
                        <div class="addressItem">
                            <div class="item mb-2">
                                <span>{{ langCart.apoAddress2 }}:</span>
                                <span>{{ shippingAddressForm.apoAddress }}</span>
                            </div>
                        </div>
                        <div class="addressItem mb-2 custom-row2">
                            <div class="item">
                                <span>{{ langCart.phoneNumber }}:</span>
                                <span>{{ shippingPrefixNumber }}-{{ shippingAddressForm.shippingPhoneNumber }}</span>
                            </div>
                            <div class="item">
                                <span>{{ langCart.city }}:</span>
                                <span>{{ shippingAddressForm.shippingApoCity }}</span>
                            </div>
                            <div class="item">
                                <span>{{ langCart.state }}:</span>
                                <span>{{ shippingAddressForm.shippingApoState }}</span>
                            </div>
                        </div>
                        <div class="addressItem mb-2">
                            <div class="item">
                                <span>{{ langCart.zipCode }}:</span>
                                <span>{{ shippingAddressForm.shippingApoZip }}</span>
                            </div>
                        </div>
                    </div>
                </v-card-text>
                <v-card-actions class="foot">
                    <v-btn outlined @click="cancelPay()"> Back to Edit Address</v-btn>
                    <v-btn color="primary" @click="submitOrder" v-show="payTypeId !== 10000 || isZeroOrder"> Confirm Address and Go to Pay </v-btn>
                    <!--paypal支付按钮-->
                    <div id="paypal-button-container" v-show="payTypeId === 10000 && !isZeroOrder"></div>
                </v-card-actions>
            </v-card>
        </v-dialog>

        <v-dialog style="z-index: 1000" max-width="500" v-model="isBuyAgainLowTips" class="pa-5">
            <div style="background: white; position: relative" class="d-flex justify-center align-center pa-8">
                <span style="position: absolute; top: 10px; right: 10px; cursor: pointer"><v-icon class="close-icon" @click="isBuyAgainLowTips = false">mdi-close</v-icon></span>
                <span>{{ langCart.minAmountTips }}&nbsp;</span>
                <CCYRate :price="lowestPrice" :ccy="currentCurrency"></CCYRate>
            </div>
        </v-dialog>
    </v-app>
</template>

<script>
import {
	googleVerifyAddress,
	addOrder,
	addOrderNotLogin,
	autoVerifyPayment,
	checkoutPrice,
	checkoutPriceNotLogin,
	childPaymentIntent,
	editOrderAddress,
	editOrderAddressNotLogin,
	editOrderDiscount,
	getAccountId,
	getAddressList,
	getCartList,
	getCountryList,
	getLowestPrice,
	getOrderInfo,
	getOrderNotLogin,
	getPayTypeList,
	getStateList,
	getStripeKey,
	isOldUser,
	modifyPaymentMethod,
	modifyPaymentMethodNotLogin,
	orderPayFail,
	paymentIntent,
	getSmsSubscriptionByMail,
	getSmsSubscriptionState,
	getNotLoginUserCouponListByEmail,
	paymentErrorLogs,
	goProDeatil
} from "@/api/web";

import * as paypalApi from "@/assets/js/paypal-api.js";
import { loadScript } from "@paypal/paypal-js";
import { getGoogleAddress, getGoogleAddressDetail, getUserCouponList, getUserReferPointsInfo, getUserUsableVoucherList } from "@/api/coupon";
import { debounce, deepClone, round2, withRetryAsync,getDate } from "@/utils/utils";
import { loadStripe } from "@stripe/stripe-js";

export default {
    data() {
        return {
            showadditionalTariffFees:false,
            additionalTariffFees:"",
            currencySymbol:"",
            apoPaymentStatus:null,
            properties: ['shippingFirstName', 'shippingLastName', 'shippingPhoneNumber','apoAddress','shippingApoCity','shippingApoState','shippingApoZip'],
            showAPOText:true,

            POPCurrencySymbol:{},//pop/fd货币根据接口返回
			codeInvalidInfo:null,
            isSampleOrder:0,
			giveawayItem:{},

            orderType:"", //1. 直接订单 2.链接订单
            triggerInput:false,
            showCouponAll:true,

            showPrice:false,
            ordersPromotionDiscounts:[],
            showSMStext:false,//默认不显示短信营销提示文案
            isSMSCheckbox:false,//是否订阅短信

            isNewCoupon: null, //是否是新优惠卷

            markupPercentage: null,
            freightCharge: null,
            addFeePercentage: null,

            couponApplyType: null,

            APOCityList: [
                {
                    id: 0,
                    name: "APO(Army Post Office)",
                },
                {
                    id: 1,
                    name: "FPO(Fleet Post Office)",
                },
                {
                    id: 2,
                    name: "DPO(Diplomatic Post Office)",
                },
            ],
            APOStateList: [
                {
                    id: 0,
                    name: "AA(Armed Forces Americas)",
                },
                {
                    id: 1,
                    name: "AE(Armed Forces Europe)",
                },
                {
                    id: 2,
                    name: "AP(Armed Forces Pacific)",
                },
            ],

            currentTaxRate: 0,
            VATtext: false,
            couponType: null,
            couponJson: "",
            couponJsonDiscountPrice:null,
            showcouponJsonDiscountPrice:true,
            couponName: "",
            showcoupon: false,
            couponList: [],
            payBeforeDialog: false,
            loadGoogleAddress: false,
            loadGoogleAddress2: false,
            taxName1: "",
            lowestPrice: null,
            shippingPrefixNumber: "",
            billingPrefixNumber: "",
            maxRetries: 3,
            retryCount: 0,
            unfreeCountryList: [],
            clearState: true,
            searchText: null,
            searchText2: null,
            showProgress: false,
            stripeAccountId: "",
            billingAddressLine2: false,
            shippingAddressLine2: false,
            radioGroup: [],
            paymentRequest: undefined,
            load: false,
            debounceCalcPrice: null,
            debounceEditOrderDiscount: null,
            showCardBtn1: false,
            showCardBtn2: false,
            stripeApplePay: false,
            stripeGooglePay: false,
            showCode: false,
            userShippingAddress: "",
            userApoAddress: "",
            userShippingAddressList: [],
            userApoAddressList: [],
            payLaterList: ["Cash on delivery", "Payment by instalments", "Payment by cc", "Net 30 days", "Cash"],
            payLater: "Cash on delivery",
            payLaterRules: [(v) => !!v || "Payment Methods is required"],
            noLoginEmailRules: [
                (v) => {
                    if (!v) {
                        return "Email is required";
                    } else {
                        this.emailErrorMessage = "";
                        return true;
                    }
                },
                (v) => {
                    if (!/.+@.+\..+/.test(v)) {
                        return "E-mail must be valid";
                    } else {
                        this.emailErrorMessage = "";
                        return true;
                    }
                },
            ],
            countryList: [],
            shippingStateList: [],
            billStateList: [],
            billingAddressStatus: 0,
            addressStatus: 0,
            saveAddress: 0,
            shippingAddressForm: {
                shippingFirstName: "",
                shippingLastName: "",
                shippingAddressLine1: "",
                shippingAddressLine2: "",
                shippingCountryCode: "",
                shippingPhoneNumber: "",
                shippingCity: "",
                shippingState: "",
                shippingStateId: "",
                shippingCountry: "",
                shippingCountryId: "",
                shippingZipCode: "",
                apoAddress: "",
                email: "",
                shippingApoCity: "",
                shippingApoState: "",
                shippingApoZip: "",
                shippingCompany: "",
            },
            shippingAddressFormRules: {
                shippingFirstName: [(v) => !!v || "First name is required"],
                shippingLastName: [(v) => !!v || "Last name is required"],
                shippingAddressLine1: [
                    (v) => !!v || "Address is required",
                    (v) => !v || v.length <= 250 || "Max 250 characters",
                    (v) =>
                            (() => {
                                let pobox = ["p.o.box", "p.o box", "po.box", "p o box", "po box", "pobox","p.o. box"];
                                let isPobox = false;
                                if (!v) {
                                    return false;
                                }
                                pobox.map((i) => {
                                    if (v.toLowerCase().includes(i)) isPobox = true;
                                });
                                return !isPobox;
                            })() || "P.O.Box is unavailable",
                ],
                shippingAddressLine2: [(v) => !v || v.length <= 250 || "Max 250 characters"],
                shippingPhoneNumber: [(v) => !!v || "Phone is required", (v) => /^\d+$/.test(v) || "Phone must contain only digits"],
                shippingCity: [(v) => !!v || "City is required"],
                // shippingApoCity: [(v) => !!v || "City is required"],
                shippingStateId: [
                    (v) => {
                        if (!this.shippingStateList.length) {
                            return true;
                        } else {
                            return !!v || "Province is required";
                        }
                    },
                ],
                shippingState: [
                    (v) => {
                        if (this.shippingStateList.length) {
                            return true;
                        } else {
                            return !!v || "Province is required";
                        }
                    },
                ],
                shippingApoState: [
                    (v) => {
                        if (this.APOStateList.length) {
                            return true;
                        } else {
                            return !!v || "Province is required";
                        }
                    },
                ],
                shippingApoCity: [
                    (v) => {
                        if (this.APOCityList.length) {
                            return true;
                        } else {
                            return !!v || "City is required";
                        }
                    },
                ],
                shippingCountryId: [(v) => !!v || "Country is required"],
                shippingZipCode: [(v) => !!v || "Zip code is required"],
                shippingApoZip: [(v) => !!v || "Zip code is required"],
                apoAddressRules: [(v) => !!v || "APO address required"],
                email: [
                    (v) => {
                        if (!v) {
                            return "Email is required";
                        } else {
                            this.emailErrorMessage = "";
                            return true;
                        }
                    },
                    (v) => {
                        if (!/.+@.+\..+/.test(v)) {
                            return "E-mail must be valid";
                        } else {
                            this.emailErrorMessage = "";
                            return true;
                        }
                    },
                ],
                shippingPrefixNumber: [(v) => !!v || "", (v) => /^\d+$/.test(v) || ""],
            },
            billingAddressForm: {
                billFirstName: "",
                billLastName: "",
                billAddressLine1: "",
                billAddressLine2: "",
                billCountryCode: "",
                billPhoneNumber: "",
                billCity: "",
                billState: "",
                billStateId: "",
                billCountry: "",
                billCountryId: "",
                billZipCode: "",
            },
            billingAddressFormRules: {
                billFirstName: [(v) => !!v || "First name is required"],
                billLastName: [(v) => !!v || "Last name is required"],
                billAddressLine1: [(v) => !!v || "Address is required", (v) => !v || v.length <= 250 || "Max 250 characters"],
                billAddressLine2: [(v) => !v || v.length <= 250 || "Max 250 characters"],
                billPhoneNumber: [(v) => !!v || "Phone is required", (v) => /^\d+$/.test(v) || "Phone must contain only digits"],
                billCity: [(v) => !!v || "City is required"],
                billStateId: [
                    (v) => {
                        if (!this.billStateList.length) {
                            return true;
                        } else {
                            return !!v || "Province is required";
                        }
                    },
                ],
                billState: [
                    (v) => {
                        if (this.billStateList.length) {
                            return true;
                        } else {
                            return !!v || "Province is required";
                        }
                    },
                ],
                billCountryId: [(v) => !!v || "Country is required"],
                billZipCode: [(v) => !!v || "Zip code is required"],
                billingPrefixNumber: [(v) => !!v || "", (v) => /^\d+$/.test(v) || ""],
            },
            paymentTypeId: "",
            paymentMethodId: "",
            totalPrice: 0,
            taxPrice: 0,
            apoShippingPrice: 0,
            shippingChargesPrice: 0,
            productPrice: 0,
            payAdditionalFee: 0,
            discountPrice: 0,
            paypalConfig: {
                merchantId: "WPQW3YQEDPWML", //商户号
                payId: process.env.payId,
                clientId: process.env.clientId, //客户端Id
                dataPartnerAttributionId: process.env.dataPartnerAttributionId, // PayPal Partner Attribution ID.
                clientToken: "", //客户端令牌
                accessToken: "", //后台获取的token
            },
            orderId: "",
            payCardOrderId: "",
            orderProductList: [],
            isChildOrder: false,
            mainOrderId: "",
            waitPaymentOrder: "",
            paypalId: "",
            otherPayId: 0,
            payTypeList: [],
            paypal_sandBox: "",
            authorize_sandBox: "",
            nCouponDiscount: "",
            nCouponCash: "",
            firstOrderDiscount: "",
            nCouponCodeTemp: "",
            nCouponCode: "",
            couponId: "",
            voucherId: "",
            pointPrice: "",
            emailErrorMessage: "",
            pointDiscountChecked: false,
            pointDiscountPrice: "",
            pointDiscountPriceTemp: "",
            couponIdTemp: "",
            couponDiscountChecked: false,
            couponOption: [],
            voucherDiscountChecked: false,
            voucherOption: [],
            crowdDiscount: "",
            crowdDiscountPrice: "",
            firstDiscount: "",
            firstDiscountPrice: "",
            couponDiscount: "",
            couponDiscountPrice: "",
            voucherPrice: "",
            // promotionDiscount: "",
            promotionPrice: "",
            userLevelDiscount: "", //用户等级折扣
            userLevelDiscountPrice: "", //用户等级折扣
            userLevelId: null,
            couponAdditionalCosts: "",
            promotionCenterAdditionalPrice:"",
            referPointsInfo: "",
            tempCartList: [],
            clientSecret: "",
            stripe: undefined,
            element: undefined,
            stripeStatus: false,
            paymentSerialNum: "",
            stripeBtn: null,
            orderDiscount: "",
            orderDiscountPrice: "",
            stripeKey: "", // 3.9 stripe获取key
            payTypeId: "",
            userEmail: "",
            predictions: [],
            predictions2: [],
            orderGivingQuotation: "",
            orderCurrency: "",
            orderCurrencySymbol: "",
            notFreeShipping: 0,
            disabledCouponCode: false,
            disabledCouponOption: false,
            disabledVoucherOption: false,
            disabledPoint: false,
            customPaypal: "",
            isBuyAgainLowTips: false,
            isUpdateAddress: false,
            isPaidOrder: false,
            debounceGetSpecificAddress: null,
            debounceGetSpecificAddress2: null,
            errorMessageObj: {
                shippingLine1: "",
                shippingCity: "",
                shippingCountry: "",
                shippingState: "",
                shippingCode: "",

                shippingApoZip: "",
                shippingApoState: "",
                shippingApoCity: "",

                billingLine1: "",
                billingCity: "",
                billingCountry: "",
                billingState: "",
                billingCode: "",
            },
            debounceVerifyAddress: null,
            couponRadioshow: 0,
            debounceShippingPhoneNumberInput: null,
			oneDollarEmail:{},
            showAPO:false,
			isTooltipVisible:false,
			hideTimeout: null,
			productId:0,
			isFromDetail:false,
        };
    },
    watch: {
        'shippingAddressForm.shippingFirstName'(newVal, oldVal) {
                this.checkShowadditionalTariffFees();
        },
        'shippingAddressForm.shippingLastName'(newVal, oldVal) {
                this.checkShowadditionalTariffFees();
        },
        'shippingPrefixNumber'(newVal, oldVal) {
            this.checkShowadditionalTariffFees();
        },
        'shippingAddressForm.shippingPhoneNumber'(newVal, oldVal) {
            this.checkShowadditionalTariffFees();
        },
        'shippingAddressForm.apoAddress'(newVal, oldVal) {
            this.checkShowadditionalTariffFees();
        },
        'shippingAddressForm.shippingApoCity'(newVal, oldVal) {
            this.checkShowadditionalTariffFees();
        },
        'shippingAddressForm.shippingApoState'(newVal, oldVal) {
            this.checkShowadditionalTariffFees();
        },
        'shippingAddressForm.shippingApoZip'(newVal, oldVal) {
            this.checkShowadditionalTariffFees();
        },
        addressStatus() {
            this.shippingAddressForm.shippingCountryId="";
            this.shippingAddressForm.shippingFirstName="";
            this.shippingAddressForm.shippingLastName="";
            this.shippingAddressForm.shippingAddressLine1="";
            this.shippingAddressForm.shippingAddressLine2="";
            this.shippingAddressForm.shippingCountryCode="";
            this.shippingAddressForm.shippingPhoneNumber="";
            this.shippingAddressForm.shippingCity="";
            this.shippingAddressForm.shippingState="";
            this.shippingAddressForm.shippingStateId="";
            this.shippingAddressForm.shippingCountry="";
            this.shippingAddressForm.shippingCountryId="";
            this.shippingAddressForm.shippingZipCode="";
            this.shippingAddressForm.apoAddress="";
            this.shippingAddressForm.email="";
            this.shippingAddressForm.shippingApoCity="";
            this.shippingAddressForm.shippingApoState="";
            this.shippingAddressForm.shippingApoZip="";
            this.shippingAddressForm.shippingCompany="";
            this.shippingPrefixNumber = "";
            this.userApoAddress="";
            this.userShippingAddress ="";

            // this.$refs.shippingAddressForm.reset();
            this.initPaypalPaymentConfig();
            this.getStripeKey();
            this.debounceCalcPrice();

        },
        isLogin() {
            location.reload();
        },
        couponId(val) {
            console.log("优惠券改动");
            this.changeDiscount(val, 2);
            this.editOrderDiscount(false);
            this.debounceCalcPrice();
        },
        voucherId(val) {
            console.log("代金券改动");
            this.changeDiscount(val, 3);
            this.editOrderDiscount(false);
            this.debounceCalcPrice();
        },
        // 积分可抵扣金额不超过订单总价
        pointDiscountPrice(val) {
            this.changeDiscount(val, 4);
            this.$nextTick(() => {
                if (Number(val) > Number(this.pointMaxPriceOrNoPointTotalPrice)) {
                    this.pointDiscountPrice = this.pointMaxPriceOrNoPointTotalPrice;
                }
                this.debounceCalcPrice();
                this.debounceEditOrderDiscount(false);
            });
        },
        symbolCode() {
            this.initPaypalPaymentConfig(this.customPaypal);
        },
        shippingAddressForm: {
            handler() {
                this.debounceVerifyAddress("shippingAddress");
                // this.apoPaceiInput();
            },
            deep: true,
        },
        billingAddressForm: {
            handler() {
                this.debounceVerifyAddress("billingAddress");
            },
            deep: true,
        },
        currentShippingCountry() {
            this.initPaypalPaymentConfig();
            this.getStripeKey();
        },
        symbolCode:{
			handler(newVal){
				this.debounceCalcPrice();
			}
		}
    },
    computed: {
		device() {
			return this.$store.state.device;
		},
        couponIdAsNumber: {
            get() {
                return Number(this.couponId);
            },
            set(value) {
                this.couponId = Number(value);
            }
        },
        // VATtext(){
        // 	console.log(this.shippingAddressForm.shippingCountryId,"this.countryList");
        // 	let find = this.countryList.find((item)=>item.id === this.shippingAddressForm.shippingCountryId);
        // 	if(find.continentId === 5){
        // 		return true
        // 	}else{
        // 		return false
        // 	}
        // },
        customCountryId() {
            if (this.addressStatus === 1) {
                return 226;
            } else {
                return this.currentShippingCountry.id;
            }
        },
        showEmailInput() {
            return (!this.isLogin && !this.isArtWork && !this.isOrder) || ((this.isArtWork || this.isOrder) && !this.userEmail);
        },
        showVoucherOption() {
            if (this.isChildOrder) {
                return false;
            }
            return this.voucherOption.length || this.disabledVoucherOption;
        },
        showPoint() {
            if (this.isChildOrder) {
                return false;
            }
            return this.pointMaxPriceOrNoPointTotalPrice || this.pointDiscountPriceTemp;
        },
        //是否展示优惠码
        showCouponCode() {
            if (this.isChildOrder) {
                return false;
            }
            return (!this.firstDiscountPrice && this.proType === 0 && !this.couponId && this.couponId !== "0" && !this.couponIdTemp && this.couponIdTemp !== "0") || this.disabledCouponCode;
        },
        //是否展示优惠券
        showCouponOption() {
            if (this.isChildOrder) {
                return false;
            }
            return (this.couponOption && this.couponOption.length && !this.nCouponCodeTemp) || this.disabledCouponOption;
        },
        //是否展示优惠券--pens
        showCouponOptionPens() {
            if (this.isChildOrder) {
                return false;
            }
            return (this.couponList && this.couponList.length && !this.nCouponCodeTemp) || this.disabledCouponOption;;
        },
        //是否展示优惠码--pens
        showCouponCodeP() {
            if (this.isChildOrder) {
                return false;
            }
            if(this.disabledCouponOption){
                return false;
            }
            return true;
        },

        //隐藏apo地址
        hideApoAddress() {
            let idList = [74, 224, 206, 215];
            return idList.includes(this.shippingAddressForm.shippingCountryId);
        },
        //不展示发货地址中的州
        showshippingAddressDe() {
            let idList = [74, 81, 224, 206, 215,223];
            return !idList.includes(this.shippingAddressForm.shippingCountryId);
        },
        //不展示账单地址中的州
        showBillingAddressDe() {
            let idList = [74, 81, 224, 206, 215];
            return !idList.includes(this.billingAddressForm.billCountryId);
        },
        currentCurrency() {


            let currency = deepClone(this.$store.state.currency);
            /**
             * 先判断是否是子订单
             * 在判断订单信息里有无货币信息
             */
            if(this.isPopSite || this.isFdSite){
                return this.POPCurrencySymbol
            }else{
                //订单和个人中心过来
                if (this.isOrder || this.isArtWork) {
                    //如果是子订单
                    if (this.isChildOrder) {
                        //订单有返回汇率的时候优先拿订单接口的汇率
                        return {
                            rate: this.orderGivingQuotation ? this.orderGivingQuotation : currency.rate,
                            symbol: this.orderCurrencySymbol ? this.orderCurrencySymbol : currency.symbol,
                            symbolCode: this.orderCurrency ? this.orderCurrency : currency.code,
                        };
                    }else{
                        //如果订单过来的汇率等于最新汇率，返回订单
                        if(currency.code === this.orderCurrency){
                            return {
                                rate: this.orderGivingQuotation ? this.orderGivingQuotation : currency.rate,
                                symbol: this.orderCurrencySymbol ? this.orderCurrencySymbol : currency.symbol,
                                symbolCode: this.orderCurrency ? this.orderCurrency : currency.code,
                            };
                        }else{
                            return {
                                rate: currency.rate,
                                symbol: currency.symbol,
                                symbolCode: currency.code,
                            };
                        }
                    }
                }else{
                    //购物车
                    return {
                        rate: currency.rate,
                        symbol: currency.symbol,
                        symbolCode: currency.code,
                    };
                }
            }
        },

        //货币符号
        symbol() {
            return this.currentCurrency.symbol;
        },
        //货币code
        symbolCode() {
            return this.currentCurrency.symbolCode;
        },

        proId() {
            return this.$store.state.proId;
        },
        isFdSite(){
            return this.proId === 9;
        },
        isPopSite(){
            return this.proId === 10;
        },
        //是否开启一美金活动
		isCouponAllowed(){
			return this.$store.state.projectComment.isCouponAllowed;
		},
        isPensSite(){
            return this.proId == 317;
        },
        isStickerSite(){
            return this.proId == 2;
        },
        hideAddress(){
            return this.isFdSite && (this.isArtWork || this.orderType==2)
        },
        disabledAddress() {
            //是子订单或者已支付订单,禁用地址
            return !(this.isChildOrder || this.isPaidOrder || this.isPopSite);
            //有订单并且之前已填写过地址
            // return !(this.orderId && !this.noAddress);
        },
        isZeroOrder() {
            return Number(this.totalPrice) <= 0;
        },
        proType() {
            return this.$store.state.proType;
        },
        //结算产品列表
        selectCartList() {
            if (process.client) {
                if (this.isOrder || this.isArtWork) {
                    return this.orderProductList;
                } else if (this.isBuyNow) {
                    return this.tempCartList;
                } else {
                    if (!localStorage.getItem("tempCart")) {
                        return [];
                    }
                    return JSON.parse(localStorage.getItem("tempCart"));
                }
            } else {
                return [];
            }
        },
        currentShippingCountry() {
            let find = this.countryList.find((item) => item.id === this.shippingAddressForm.shippingCountryId);
            return find || {};
        },
        currentBillingCountry() {
            if (this.billingAddressStatus === 0) {
                return this.currentShippingCountry;
            }
            let find = this.countryList.find((item) => item.id === this.billingAddressForm.billCountryId);
            return find || {};
        },
        currentShippingState() {
            let findState = this.shippingStateList.find((item) => {
                return item.id === this.shippingAddressForm.shippingStateId;
            });
            return findState || {};
        },
        currentBillingState() {
            if (this.billingAddressStatus === 0) {
                return this.currentShippingState;
            }
            let findState = this.billStateList.find((item) => {
                return item.id === this.billingAddressForm.billStateId;
            });
            return findState || {};
        },
        isLogin() {
            return this.$store.getters.isLogin;
        },
        linkOrderId(){
            return this.$route.params.oid || this.$route.query.orderid || this.$route.query.orderId
        },
        isOrder() {
            return !!(this.$route.query.type === "order" && this.linkOrderId);
        },
        isArtWork() {
            return !!(this.$route.query.type === "artwork" && this.linkOrderId);
        },
        totalQty() {
            let qty = 0;
            if (!this.selectCartList.length) {
                return 0;
            }
            this.selectCartList.forEach((item) => {
                qty += item.quantity;
            });
            return qty;
        },
        isReputable() {
            if (this.isLogin) {
                return this.$store.state.userInfo.isReputable; //判断是否信誉客户 1:非信誉客户 2:信誉客户
            } else {
                return 0;
            }
        },
        //用户uuid
        cartUuid() {
            if (this.isBuyNow) {
                return this.$route.query.uuid;
            } else {
                if (this.isLogin) {
                    return null;
                } else {
                    return this.$store.state.userUUID;
                }
            }
        },
        //获取邮箱
        inputEmail() {
            if (this.isLogin) {
                return this.$store.state.userInfo.email;
            } else {
                return this.shippingAddressForm.email?.trim();
            }
        },
        //支付方式
        paymentMethodText() {
            let list = this.payTypeList;
            let pay = list.find((item) => {
                return item.id === this.paymentMethodId;
            });
            return pay ? pay.payType.payName : "";
        },
        // 积分最大可抵扣金额
        pointMaxPrice() {
            if (!this.referPointsInfo?.pointToCash || !this.referPointsInfo?.activeReferPoints) return 0;
            return parseInt(this.referPointsInfo.activeReferPoints / this.referPointsInfo.pointToCash);
        },
        // 未抵扣积分时的总价
        noPointTotalPrice() {
            return Math.max(this.productPrice - this.firstDiscountPrice - this.couponDiscountPrice - this.voucherPrice - this.crowdDiscountPrice - this.promotionPrice, 0);
        },
        //如果积分抵扣高于订单金额,则最大是订单金额
        pointMaxPriceOrNoPointTotalPrice() {
            if (!this.referPointsInfo?.pointToCash || !this.referPointsInfo?.activeReferPoints) return 0;
            //积分可抵扣金额
            let pointMaxPrice = this.pointMaxPrice;
            //未抵扣积分时的总价
            //如果积分抵扣高于订单金额,则最大是订单金额.如果小于,则最大输入是积分抵现金额
            let noPointTotalPrice = this.noPointTotalPrice;
            return Math.round((pointMaxPrice > noPointTotalPrice ? noPointTotalPrice * this.currentCurrency.rate : pointMaxPrice * this.currentCurrency.rate) * 100) / 100;
        },
        isBuyNow() {
            return this.$route.query.buyNow == 1 ? true : false;
        },
        paypalBaseUrl() {
            if (this.paypal_sandBox) {
                return "https://api-m.sandbox.paypal.com";
            }
            return "https://api-m.paypal.com";
        },
        langCart() {
            return this.$store.getters.lang?.cart;
        },
		langSemiCustom() {
            return this.$store.getters.lang?.semiCustom;
        },
        language() {
            return this.$store.state.language.language;
        },
        isDirectOrder() {
            return !this.isBuyNow && !this.isOrder && !this.isArtWork;
        },
    },
    methods: {
        checkShowadditionalTariffFees() {
            // 将检查逻辑封装成一个方法，以便在多个监听器中重用
            if(this.shippingAddressForm.shippingFirstName &&
            this.shippingAddressForm.shippingLastName &&
            this.shippingPrefixNumber &&
            this.shippingAddressForm.shippingPhoneNumber &&
            this.shippingAddressForm.apoAddress &&
            this.shippingAddressForm.shippingApoCity &&
            this.shippingAddressForm.shippingApoState &&
            this.shippingAddressForm.shippingApoZip){
                this.showadditionalTariffFees = true;
            }else{
                this.showadditionalTariffFees = false;
            }
        },
        getcurrencySymbol(){
			if(this.currencySymbol === 'CAD'){
				return 'CA$'
			}else if(this.currencySymbol === 'GBP'){
				return '￡'
			}else if(this.currencySymbol === 'AUD'){
				return 'AU$'
			}else{
				return '$'
			}
		},
        //获取订单信息
        async orderbackfill(){
            let res = await this.getMyOrderInfo({
                oid: this.linkOrderId,
                getProductInfo: true,
                getAddressInfo: true,
                getDiscountInfo: true,
            });
            // 判断是否有模具费的订单
            this.isBuyAgainLowTips = this.$route.query.buyAgain && res.data.ordersProducts.find((ele) => ele.mouldPrice && ele.mouldPrice > 0) && this.lowestPrice && this.lowestPrice > 0;
            //判断是否有未支付的订单
            let result = this.findPendingPaymentOrder(res);
            if (result) {
                //更新价格
                this.updatePrice(this.waitPaymentOrder);
                //如果apo地址存在，切换地址tab
                if (res.data.apoAddress) {
                    this.addressStatus = 1;
                }
                await this.$nextTick();
                //地址回填,邮箱回填
                this.backFillAddress(res.data);
                //设置支付方式
                this.setDefaultPaymentMethod(res.data.paymentMethodId);
                 //根据订单的货币切换页头的货币
                let find = this.$store.state.currencyList.find((i)=>i.code === res.data.currency);
                 this.$store.commit("setCurrency", find);
            } else {
                this.$toast.show("Order has been paid", {
                    duration: 0,
                    action: [
                        {
                            text: "YES",
                            onClick: (e, toastObject) => {
                                toastObject.goAway(0);
                                window.location.href = "/";
                            },
                        },
                    ],
                });
                this.isPaidOrder = true;
                this.$gl.hide();
                return false;
            }
        },
        apoAddressClick(){
            this.showAPOText = true;
            if (!this.isDirectOrder && !this.isChildOrder) {
                this.orderbackfill();
            }
        },
        checkFormCompletion(form, properties) {
            for (let i = 0; i < properties.length; i++) {
                if (!form[properties[i]]) {
                    return false;
                }
            }
            return true;
        },
        async apoPaceiInput(){
            if(this.shippingAddressForm.shippingFirstName &&
            this.shippingAddressForm.shippingLastName &&
            this.shippingPrefixNumber &&
            this.shippingAddressForm.shippingPhoneNumber &&
            this.shippingAddressForm.apoAddress &&
            this.shippingAddressForm.shippingApoCity &&
            this.shippingAddressForm.shippingApoState &&
            this.shippingAddressForm.shippingApoZip){
                this.shippingAddressForm.shippingCountryId = 226;
                if (!this.isDirectOrder && !this.isChildOrder) {
                    await this.updateAddress();
                    await this.editOrderDiscount(false);
                }else{
                    this.debounceCalcPrice();
                }
                this.showAPO=true;

            }else{
                this.shippingAddressForm.shippingCountryId = "";
                this.debounceCalcPrice();
            }

        },
        //促销手续费
        getadditionalPrice(ordersPromotionDiscounts,couponAdditionalCosts){
            let price=0;
            ordersPromotionDiscounts && ordersPromotionDiscounts.forEach((res)=>{
                price += res.additionalPrice;
            })
            return (price+couponAdditionalCosts);
        },
        uniqueId(val){
            return Number(val.uniqueId)
        },
        showSMStextIcon(){
            this.showSMStext = false;
        },
        // SMSCheckboxClick(){
        // 	if(this.isSMSCheckbox){
        // 		setTimeout(() => {
        // 			this.showSMStext = false;
        // 		},2000)
        // 	}
        // },
        // shippingPhoneNumberInput(shippingPhoneNumber){
        // 	//如果没有输入邮箱号并且没有电话号码，不显示短信营销文案
        // 	console.log(shippingPhoneNumber,this.shippingAddressForm.email);

        // 	if(this.isLogin){
        // 		if(!shippingPhoneNumber){
        // 			this.showSMStext = false;
        // 			return false;
        // 		}
        // 		if(shippingPhoneNumber){
        // 			this.getSmsSubscriptionLogin();
        // 		}
        // 	}else{
        // 		if(!shippingPhoneNumber || !this.shippingAddressForm.email){
        // 			this.showSMStext = false;
        // 			return false;
        // 		}
        // 		if(this.shippingAddressForm.email && shippingPhoneNumber){
        // 				this.getSmsSubscriptionNotLogin();
        // 		}

        // 	}
        // },
        getTotalPrice(totalPrice) {
            if (totalPrice < 0) {
                return Math.abs(totalPrice);
            } else {
                return totalPrice;
            }
        },
        //洲回填简写和全称
        formatShippingState(item) {
            if (item.acronymCode !== null) {
                return `(${item.acronymCode}) ${item.stateName}`;
            } else {
                return `${item.stateName}`;
            }
        },
        //洲回填简写和全称
        formatbillState(item) {
            if (item.acronymCode !== null) {
                return `(${item.acronymCode}) ${item.stateName}`;
            } else {
                return `${item.stateName}`;
            }
        },
        couponRadio(item) {
            this.couponId = item.uniqueId;
            this.couponRadioshow = 1;
            this.showcoupon = false;
        },

        //pens网站回填优惠劵名称过期时间
        getcouponName() {
            let find = this.couponList.find((item) => item.uniqueId === this.couponId);
            if (find) {
                if (find.isNewCoupon) {
                    this.isNewCoupon = find.isNewCoupon;
                }

                if (find.couponType === 4) {
                    //一美金优惠劵显示金额
                    this.showPrice = true;
                    if (find.validityEndTime) {
                        let span = `<span>${find.conditionDescription}</span>&nbsp;<span style="color:#666666">( Deadline:${find.validityEndTime})</span>`;
                        return span;
                    } else if (!find.validityEndTime) {
                        let span = `<span>${find.conditionDescription}</span>`;
                        return span;
                    }
                    this.currencySymbol = find.currency;
                    return "";
                } else {
                    //促销优惠劵后面不显示金额
                    this.showPrice=false;
                    return find.title;
                }
            }
            return "";
        },

        verifyAddress(type) {
            let data;
            if (type === "shippingAddress") {
                let currentShippingCountry = this.currentShippingCountry;
                let shippingState = this.currentShippingState?.stateName || this.shippingAddressForm.shippingState;
                data = {
                    addressDetail: this.shippingAddressForm.shippingAddressLine1,
                    state: shippingState,
                    city: this.shippingAddressForm.shippingCity,
                    country: currentShippingCountry?.acronym,
                    postalCode: this.shippingAddressForm.shippingZipCode,
                };
                if (!data.addressDetail || !data.country || !data.city || !data.postalCode) {
                    return;
                }
            } else {
                let currentBillingCountry = this.currentBillingCountry;
                let shippingState = this.currentBillingState?.stateName || this.billingAddressForm.billState;
                data = {
                    addressDetail: this.billingAddressForm.billAddressLine1,
                    state: shippingState,
                    city: this.billingAddressForm.billCity,
                    country: currentBillingCountry?.acronym,
                    postalCode: this.billingAddressForm.billZipCode,
                };
                if (!data.addressDetail || !data.country || !data.city || !data.postalCode) {
                    return;
                }
            }
            googleVerifyAddress(data).then((res) => {
                let arr = res.data;
                if (!arr || !arr.length) {
                    if (type === "shippingAddress") {
                        this.errorMessageObj = Object.assign({}, this.errorMessageObj, {
                            shippingLine1: "",
                            shippingCity: "",
                            shippingCountry: "",
                            shippingState: "",
                            shippingCode: "",
                        });
                    } else {
                        this.errorMessageObj = Object.assign({}, this.errorMessageObj, {
                            billingLine1: "",
                            billingCity: "",
                            billingCountry: "",
                            billingState: "",
                            billingCode: "",
                        });
                    }
                    return false;
                }
                let map;
                if (type === "shippingAddress") {
                    map = {
                        1: "shippingCountry",
                        2: "shippingState",
                        3: "shippingCity",
                        4: "shippingLine1",
                        5: "shippingCode",
                    };
                } else {
                    map = {
                        1: "billingCountry",
                        2: "billingState",
                        3: "billingCity",
                        4: "billingLine1",
                        5: "billingCode",
                    };
                }
                this.errorMessageObj = Object.assign({}, this.errorMessageObj, {
                    shippingLine1: "",
                    shippingCity: "",
                    shippingCountry: "",
                    shippingState: "",
                    shippingCode: "",
                });
                arr.forEach((item) => {
                    this.errorMessageObj[map[item.code]] = item.msg;
                });
            });
        },
        async changeShippingState() {
            if (!this.shippingAddressForm.shippingCountryId || !this.shippingAddressForm.shippingZipCode) return;
            this.debounceCalcPrice();
            if (!this.isDirectOrder && !this.isChildOrder) {
                let result = this.validateForm();
                if (!result.length) {
                    await this.updateAddress();
                    await this.editOrderDiscount(false);
                }
            }
        },
        async changeApoAddress() {
            this.debounceCalcPrice();
            if (!this.isDirectOrder && !this.isChildOrder) {
                let result = this.validateForm();
                if (!result.length) {
                    await this.updateAddress();
                    await this.editOrderDiscount(false);
                }
            }
        },
        inputZipPostal() {
            if (!this.shippingAddressForm.shippingStateId || !this.shippingAddressForm.shippingCountryId) return;
            this.debounceCalcPrice();
        },
        async selectShippingCountry(val) {
            let find = this.countryList.find((item) => item.id === this.shippingAddressForm.shippingCountryId);
            if (find) {
                this.taxName1 = find.taxName === null ? "Tax Price" : find.taxName;
                this.VATtext = find.continentId === 5 ? true : false; //欧洲国家显示税费提示文案
            }
            this.notFreeShipping = !this.countryList.find((ele) => ele.id == this.shippingAddressForm.shippingCountryId)?.isFreeShipping;
            this.shippingAddressForm.shippingStateId = "";
            this.shippingAddressForm.shippingState = "";
            this.shippingStateList = await this.getState({
                id: val,
            });
            this.shippingPrefixNumber = this.currentShippingCountry.code ? String(this.currentShippingCountry.code) : this.shippingPrefixNumber;
            this.calcPrice();
        },
        async selectBillingCountry(val) {
            this.notFreeShipping = !this.countryList.find((ele) => ele.id == this.billingAddressForm.billCountryId)?.isFreeShipping;
            this.billingAddressForm.billStateId = "";
            this.billingAddressForm.billState = "";
            this.billStateList = await this.getState({
                id: val,
            });
            this.billingPrefixNumber = this.currentBillingCountry.code ? String(this.currentBillingCountry.code) : this.billingPrefixNumber;
            this.calcPrice();
        },
        async changeUserApoAddress(val) {
            let findAddress = this.userApoAddressList.find((item) => item.id === val);
            if (!findAddress) {
                return;
            }
            let cityName, stateName;
            this.APOCityList.forEach((item) => {
                if (item.name.substring(0, 3) === findAddress.apoCity) {
                    cityName = item.name;
                }
            });
            this.APOStateList.forEach((item) => {
                if (item.name.substring(0, 2) === findAddress.apoState) {
                    stateName = item.name;
                }
            });
            this.shippingAddressForm.shippingFirstName = findAddress.firstName;
            this.shippingAddressForm.shippingLastName = findAddress.lastName;
            this.shippingAddressForm.shippingApoCity = cityName;
            this.shippingAddressForm.shippingApoState = stateName;
            this.shippingAddressForm.shippingApoZip = findAddress.apoZip;

            try {
                this.shippingAddressForm.apoAddress = JSON.parse(findAddress.addressLine)[1];
            } catch (e) {
                this.shippingAddressForm.apoAddress = findAddress.addressLine;
            }
            if (findAddress.phoneNum.includes("-")) {
                this.shippingPrefixNumber = findAddress.phoneNum.split("-")[0];
                this.shippingAddressForm.shippingPhoneNumber = findAddress.phoneNum.split("-")[1];
            } else {
                this.shippingPrefixNumber = "";
                this.shippingAddressForm.shippingPhoneNumber = findAddress.phoneNum;
            }

            //apo地址都有的情况下
            if(findAddress.firstName && findAddress.lastName && cityName && stateName && findAddress.apoZip && this.shippingPrefixNumber && this.shippingAddressForm.shippingPhoneNumber && this.shippingAddressForm.apoAddress){
                this.shippingAddressForm.shippingCountryId = 226;
            }

            this.debounceCalcPrice();
            await this.$nextTick();
            if (!this.isDirectOrder && !this.isChildOrder) {
                let result = this.validateForm();
                if (!result.length) {
                    await this.updateAddress();
                    await this.editOrderDiscount(false);
                }
            }
        },
        async changeUserShippingAddress(val) {
            let findAddress = this.userShippingAddressList.find((item) => item.id === val);
            if (!findAddress) {
                return;
            }
            this.shippingAddressForm.shippingFirstName = findAddress.firstName;
            this.shippingAddressForm.shippingLastName = findAddress.lastName;
            this.shippingAddressForm.shippingAddressLine1 = JSON.parse(findAddress.addressLine)[1];
            this.shippingAddressForm.shippingAddressLine2 = JSON.parse(findAddress.addressLine)[2];
            this.shippingAddressForm.shippingCity = findAddress.city;
            this.shippingAddressForm.shippingCountryId = findAddress.countryId;
            this.shippingAddressForm.shippingStateId = findAddress.stateId;
            this.shippingAddressForm.shippingZipCode = findAddress.zipCode;
            this.shippingAddressForm.shippingCompany = findAddress.shippingCompany;

            if (findAddress.phoneNum.includes("-")) {
                this.shippingPrefixNumber = findAddress.phoneNum.split("-")[0];
                this.shippingAddressForm.shippingPhoneNumber = findAddress.phoneNum.split("-")[1];
            } else {
                this.shippingPrefixNumber = "";
                this.shippingAddressForm.shippingPhoneNumber = findAddress.phoneNum;
            }
            this.notFreeShipping = !this.countryList.find((ele) => ele.id == findAddress.countryId)?.isFreeShipping;
            let shippingStateList = await this.getState({
                id: findAddress.countryId,
            });
            this.shippingStateList = shippingStateList;
            if (shippingStateList && shippingStateList.length > 0) {
                let findState = shippingStateList.find((item) => {
                    return item.id === findAddress.stateId;
                });
                if (findState) {
                    this.shippingAddressForm.shippingStateId = findState.id;
                } else {
                    this.shippingAddressForm.shippingState = findAddress.state;
                }
            } else {
                this.shippingAddressForm.shippingState = findAddress.state;
            }
            let find = this.countryList.find((item) => item.id === this.shippingAddressForm.shippingCountryId);
            if (find) {
                this.taxName1 = find.taxName === null ? "Tax Price" : find.taxName;
                this.VATtext = find.continentId === 5 ? true : false;
            }
            if(findAddress.phoneNum){
                this.getSmsSubscriptionLogin();
            }
            this.debounceCalcPrice();
            await this.$nextTick();
            if (!this.isDirectOrder && !this.isChildOrder) {
                let result = this.validateForm();
                if (!result.length) {
                    await this.updateAddress();
                    await this.editOrderDiscount(false);
                }
            }
        },
        editOrderDiscount(bool = false) {
            if (!this.mainOrderId) {
                return Promise.resolve();
            }
            //子订单不调用此接口
            if (this.isChildOrder) {
                return Promise.resolve();
            }
            if (!this.isOrder && !this.isArtWork) {
                return Promise.resolve();
            }
            //订单或者图稿确认过来的可以调用此接口
            return new Promise((resolve) => {
                let params = {
                    oid: this.mainOrderId,
                    isUseCoupon: bool,
                    isSmsSubscriptions: this.isSMSCheckbox === true ? 1 : 0,
                };
                if (this.nCouponCodeTemp !== "") {
                    params.crowdCode = this.nCouponCodeTemp;
                }
                let find;

                if (this.isStickerSite || this.isPensSite || this.isCouponAllowed) {
                    if (this.couponId !== "" || this.couponId === 3 || this.couponId === 4) {
                        params.couponId = this.couponId;
                    } else {
                        params.couponId = null;
                    }
                    find= this.couponList && this.couponList.find((t)=>t.uniqueId == this.couponIdAsNumber);
                }else{
                    // if (this.couponId !== "") {
                    // 	params.couponId = this.couponId;
                    // }

                    find= this.couponOption && this.couponOption.find((t)=>t.uniqueId == this.couponIdAsNumber);

                }
                if(find){
                    //isNew:1 新优惠劵；isNew:0 旧优惠劵
                    if(find.isNew === 1){
                        params.uniqueId = find.uniqueId;
                        params.activityId = find.activityId;
                    }
                }
                params.voucherId = this.voucherId;

				if(this.giveawayItem){
					params.freebieId = this.giveawayItem.id;
                    params.freebieParam = JSON.stringify({
                            Size: [{
                                    isEmailLater: 0,
                                    paramName: this.giveawayItem.param || ""
                            }],
                            Text: [{
                                    isEmailLater: 0,
                                    paramName: this.giveawayItem.neonCustomText || ""
                            }],
                            Color: [{
                                isEmailLater: 0,
                                paramName: this.giveawayItem.neonCustomColor ? this.giveawayItem.neonCustomColor[this.giveawayItem.id]?.paramNameZh : this.giveawayItem.defaultColor?.paramNameZh
                            }],
                            Font:[{
                                isEmailLater: 0,
                                paramName: this.giveawayItem.neonCustomFont ? this.giveawayItem.neonCustomFont.paramNameZh : this.giveawayItem.defaultFont?.paramName
                            }]
                    })
				}

                if (this.disabledPoint) {
                    params.pointPrice = this.pointDiscountPriceTemp;
                } else {
                    params.pointPrice = Math.round((this.pointDiscountPrice / this.currentCurrency.rate) * 100) / 100;
                }
                editOrderDiscount(params).then((res) => {
                    this.updatePrice2(res.data);
                    resolve();
                });
            });
        },
        getSpecificAddress(val) {
            this.loadGoogleAddress = true;
            setTimeout(() => {
                this.loadGoogleAddress = false;
            }, 1000);
            if (val) {
                let params = {
                    address: val,
                };
                getGoogleAddress(params).then((res) => {
                    let predictions = res.data;
                    predictions.forEach((i) => {
                        const commaIndex = i.mainText.indexOf(",");
                        if (commaIndex !== -1) {
                            const firstPart = i.mainText.substring(0, commaIndex + 1);
                            const secondPart = i.mainText.substring(commaIndex + 1);
                            i.mainTextCopy = `${firstPart}<span style="color: gray;">${secondPart}</span>`;
                        }
                    });
                    this.predictions = predictions;
                });
            } else {
                this.predictions = [];
            }
        },
        setAddress() {
            if (this.shippingAddressForm.shippingAddressLine1) {
                this.getAddressDetails();
            }
        },
        //获取国家省市区
        getAddressDetails() {
            let findPredictions = this.predictions.find((item) => item.mainText === this.shippingAddressForm.shippingAddressLine1);
            if (findPredictions) {
                let params = {
                    placeId: findPredictions.placeId,
                };
                this.$nextTick(() => {
                    this.shippingAddressForm.shippingAddressLine1 = findPredictions.detailAddress;
                });
                getGoogleAddressDetail(params).then((res) => {
                    this.shippingAddressForm.shippingCity = res.data.city;
                    let findCountry = this.countryList.find((item) => item.countryName === res.data.country);
                    if (findCountry) {
                        this.taxName1 = findCountry.taxName === null ? "Tax Price" : findCountry.taxName;
                        this.VATtext = findCountry.continentId === 5 ? true : false;
                        this.shippingPrefixNumber = findCountry.code;
                        this.shippingAddressForm.shippingCountryId = findCountry.id;
                        this.$nextTick(() => {
                            //如果是没有州的国家并且google推荐无城市,就把州回填到城市
                            if (!this.showshippingAddressDe && !this.shippingAddressForm.shippingCity) {
                                this.shippingAddressForm.shippingCity = res.data.state;
                            }
                        });
                        this.getState({
                            id: findCountry.id,
                        }).then(async (res2) => {
                            this.shippingStateList = res2;
                            let findState = this.shippingStateList.find((item) => item.stateName === res.data.state);
                            if (findState) {
                                this.shippingAddressForm.shippingStateId = findState.id;
                            } else {
                                this.shippingAddressForm.shippingState = res.data.state;
                            }
                            this.debounceCalcPrice();
                            if (!this.isDirectOrder && !this.isChildOrder) {
                                let result = this.validateForm();
                                if (!result.length) {
                                    await this.updateAddress();
                                    await this.editOrderDiscount(false);
                                }
                            }
                        });
                    }
                    this.shippingAddressForm.shippingZipCode = res.data.code;
                });
            }
        },

        //Billing Address获取具体地址
        getSpecificAddress2(val) {
            this.loadGoogleAddress2 = true;
            setTimeout(() => {
                this.loadGoogleAddress2 = false;
            }, 1000);
            if (val) {
                let params = {
                    address: val,
                };
                getGoogleAddress(params).then((res) => {
                    let predictions2 = res.data;
                    predictions2.forEach((i) => {
                        const commaIndex = i.mainText.indexOf(",");
                        if (commaIndex !== -1) {
                            const firstPart = i.mainText.substring(0, commaIndex + 1);
                            const secondPart = i.mainText.substring(commaIndex + 1);
                            i.mainTextCopy2 = `${firstPart}<span style="color: gray;">${secondPart}</span>`;
                        }
                    });
                    this.predictions2 = predictions2;
                });
            } else {
                this.predictions2 = [];
            }
        },
        setAddress2() {
            if (this.billingAddressForm.billAddressLine1) {
                this.getAddressDetails2();
            }
        },

        //获取国家省市区
        getAddressDetails2() {
            let findPredictions2 = this.predictions2.find((item) => item.mainText === this.billingAddressForm.billAddressLine1);
            if (findPredictions2) {
                let params = {
                    placeId: findPredictions2.placeId,
                };
                this.$nextTick(() => {
                    this.billingAddressForm.billAddressLine1 = findPredictions2.detailAddress;
                });
                getGoogleAddressDetail(params).then((res) => {
                    this.billingAddressForm.billCity = res.data.city;
                    let findCountry2 = this.countryList.find((item) => item.countryName === res.data.country);
                    if (findCountry2) {
                        this.billingPrefixNumber = findCountry2.code;
                        this.billingAddressForm.billCountryId = findCountry2.id;
                        this.$nextTick(() => {
                            //如果是没有州的国家并且google推荐无城市,就把州回填到城市
                            if (!this.showshippingAddressDe && !this.billingAddressForm.billCity) {
                                this.billingAddressForm.billCity = res.data.state;
                            }
                        });
                        this.getState({
                            id: findCountry2.id,
                        }).then((res2) => {
                            this.billStateList = res2;
                            let findState2 = this.billStateList.find((item) => item.stateName === res.data.state);
                            if (findState2) {
                                this.billingAddressForm.billStateId = findState2.id;
                            } else {
                                this.billingAddressForm.billState = res.data.state;
                            }
                        });
                    }
                    this.billingAddressForm.billZipCode = res.data.code;
                });
            }
        },

        // 贴纸、pens网站用户未登录，输入邮箱，邮箱是已注册状态，调用获取优惠劵
        async getUserCouponEmail() {
            let sList = this.selectCartList.map((item) => item.id);
            let olist = this.oneDollarEmail.ordersProducts && this.oneDollarEmail.ordersProducts.map((i) => i.id);
            let params = {
                type: 1,
                cash: this.productPrice ? this.productPrice : this.oneDollarEmail.productPrice,
                cartIdList: sList.length ? sList : olist,
                userId: this.userId,
                email: this.shippingAddressForm.email ? this.shippingAddressForm.email : this.oneDollarEmail.email,
                cartUuid: this.cartUuid,
            };
            if (this.isArtWork) params.oid = this.linkOrderId;
            try {
                const res = await getNotLoginUserCouponListByEmail(params);
                let list = res.data || [];
                list = list.concat([
                    {
                        id: 3,
                        uniqueId: 3,
                        conditionDescription: "Enter Coupon Code",
                        title: "Enter Coupon Code",
                    },
                    {
                        id: 4,
                        uniqueId: 4,
                        conditionDescription: "I don't want to use it",
                        title: "I don't want to use it",
                    }
                ]);
                this.couponList = list.map(res => {
                    //一美金优惠劵:有id并且isNew等于1
                    //普通老优惠劵：有id并且isNew等于0
                    if ((res.id && res.isNew === 1) || (res.id && res.isNew === 0)) {
                        return { ...res, uniqueId: res.id };
                    }
                    //促销新优惠劵
                    return res;
                });
            } catch (error) {
                console.error("Error getting coupon list:", error);
                this.couponList = [];
            }
            
            await this.debounceCalcPrice();
        },


        //获取优惠卷列表
        fetchUserCouponList() {
            if (this.proType !== 0 || !this.isLogin) {
                return Promise.resolve();
            }
            return new Promise((resolve) => {
                let type,list=[];
                this.selectCartList.forEach((item)=>{
                    if(item.buyType === 7){
                        type = 3
                    }else if(item.buyType === 9){
                        type = 2
                    }

                    list.push({
                        cateType: type,
                        id: item.quoteCateChildId,
                        name: item.cateName,
                        quantity: item.quantity,
                        tag: `${type}-${item.quoteCateChildId}`,
                        price: item.totalPrice
                    });
                })
                let params;
                if (this.isPensSite || this.isStickerSite || this.isCouponAllowed) {
                    params = {
                        type: 1,
                        cash: this.productPrice,
                        cartIdList: this.selectCartList.map((item) => item.id),
                        userId: this.userId,
                        productList:list,
                    };
                } else {
                    params = {
                        type: 1,
                        cash: this.productPrice,
                        productList:list,
                    }
                }

                Promise.all([
                    getUserCouponList(params),
                ]).then((res) => {
                    let list = res[0].data;
                    list.forEach((r)=>{
                        if(r.isNew === 0){
                            r.uniqueId = r.id
                        }
                    })

                    // 贴纸和pens网站一美金活动
                    if(this.isStickerSite || this.isPensSite || this.isCouponAllowed){
                        if(res[0].data.length){
                            let list = res[0].data;
                            list.push({
                                id:3,
                                uniqueId:3,
                                conditionDescription:"Enter Coupon Code",
                                title:"Enter Coupon Code",
                            },{
                                id:4,
                                uniqueId:4,
                                conditionDescription:"I don't want to use it",
                                title:"I don't want to use it",
                            })
                            list.forEach((res)=>{
                                //一美金优惠劵:有id并且isNew等于1
                                //普通老优惠劵：有id并且isNew等于0
                                if((res.id && res.isNew === 1) || (res.id && res.isNew === 0)){
                                    res.uniqueId = res.id;
                                }else{
                                    //促销新优惠劵
                                    return res.uniqueId
                                }
                            })

                            this.couponList = list;
                        }
                    }else{
                        this.couponOption = list;
                    }
                    resolve();
                })
            })
        },

        getAllCoupon() {
            if (this.proType !== 0 || !this.isLogin) {
                return Promise.resolve();
            }
            return new Promise((resolve) => {
                Promise.all([
                    // getUserCouponList({
                    // 	type: 1,
                    // 	cash: this.productPrice,
                    // }),
                    getUserUsableVoucherList(),
                    getUserReferPointsInfo(),
                ]).then((res) => {
                    // this.couponOption = res[0].data;
                    this.voucherOption = res[0].data;
                    this.referPointsInfo = res[1].data;

                    // if(this.isPensSite){
                    // 	if(res[0].data.length){
                    // 		let list = res[0].data;
                    // 		list.push({
                    // 			id:3,
                    // 			conditionDescription:"Enter Coupon Code",
                    // 			title:"Enter Coupon Code",
                    // 		},{
                    // 			id:4,
                    // 			conditionDescription:"I don't want to use it",
                    // 			title:"I don't want to use it",
                    // 		})
                    // 		this.couponList = list
                    // 	}
                    // }

                    resolve();
                });
            });
        },
        modifyPayMethod(data) {
            return new Promise((resolve) => {
                if (this.isLogin) {
                    modifyPaymentMethod(data).then((res) => {
                        resolve(res);
                    });
                } else {
                    modifyPaymentMethodNotLogin(data).then((res) => {
                        resolve(res);
                    });
                }
            });
        },
        async changePaymentMethod(val) {
            //如果订单已存在，先调用修改支付方式的接口,再调用更新订单折扣的接口
            //订单不存在,直接调用更新价格的接口
            if (this.orderId) {
                await this.modifyPayMethod({
                    paymentMethodId: val,
                    oid: this.orderId,
                    email: this.userEmail,
                });
                await this.editOrderDiscount(false);
            } else {
                this.debounceCalcPrice();
            }
        },
        changeAddressStatus(val) {
            let node = document.getElementById("rightContent");
            this.$nextTick(() => {
                node.scroll({
                    top: node.scrollHeight - node.clientHeight,
                    behavior: "smooth",
                });
            });
        },
        changeDiscount(val, type) {
            console.log(typeof val,"leixing");
            if (val) {
                !this.radioGroup.includes(type) ? this.radioGroup.push(type) : "";
            } else {
                let findIndex = this.radioGroup.findIndex((item) => {
                    return item === type;
                });
                if (findIndex >= 0) {
                    this.radioGroup.splice(findIndex, 1);
                }
            }
            let find;
            if (this.isPensSite || this.isStickerSite || this.isCouponAllowed) {
                find=this.couponList && this.couponList.find((r)=>r.uniqueId == val)
            }else{
                find=this.couponOption && this.couponOption.find((r)=>r.uniqueId == val)
            }

            if(find){
                if(find.isNew === 0){
                    this.showPrice = true;
                }else{
                    this.showPrice = false;
                }
            }
        },
        toOrderDetail() {
            this.stripeStatus = false;
        },
        onInput(){
            this.triggerInput = true;
        },

        updatePrice2(data) {
            this.additionalTariffFees = data.additionalTariffFees;
            this.apoPaymentStatus = data.paymentStatus;
            this.POPCurrencySymbol = {
                rate:data.givingQuotation,
                symbol:data.currencySymbol,
                symbolCode:data.currency
            };
            this.orderDiscount = data.orderDiscount;
            this.orderDiscountPrice = data.orderDiscountPrice;
            this.productPrice = data.productPrice;
            this.totalPrice = data.totalPrice;
            this.taxPrice = data.taxPrice;
            this.apoShippingPrice = data.apoShippingPrice;
            this.payAdditionalFee = data.payAdditionalFee;
            this.discountPrice = data.discountPrice;
            this.pointPrice = data.pointPrice;
            //人群码优惠
            this.crowdDiscount = data.crowdDiscount;
            this.crowdDiscountPrice = data.crowdDiscountPrice;
            //第一次下单折扣
            this.firstDiscount = data.firstDiscount;
            this.firstDiscountPrice = data.firstDiscountPrice;
            //优惠券
            this.couponDiscount = data.couponDiscount;
            this.couponDiscountPrice = data.couponDiscountPrice;

            //代金券
            this.voucherPrice = data.voucherPrice;
            //额外折扣
            this.promotionPrice = data.promotionPrice;
            // this.promotionDiscount = data.promotionDiscount;
            this.ordersPromotionDiscounts = data.ordersPromotionDiscounts;

            //用户等级折扣
            this.userLevelDiscount = data.userLevelDiscount;
            this.userLevelDiscountPrice = data.userLevelDiscountPrice;
            this.couponAdditionalCosts = data.couponAdditionalCosts;
            this.userLevelId = data.userLevelId;
            this.currentTaxRate = data.currentTaxRate;

            // 促销中心额外收费
            this.promotionCenterAdditionalPrice = data.promotionCenterAdditionalPrice;
            //样品单
            this.isSampleOrder = data.isSampleOrder;
			this.codeInvalidInfo = data.codeInvalidInfo;

            //pens网站
            if (this.isPensSite || this.isStickerSite || this.isCouponAllowed) {
                this.couponApplyType = data.couponApplyType;
                this.couponType = data.couponType;

                //等于2的时候回填优惠码
                if (data.couponType === 2) {
                    this.couponJson = data.crowdCode;
                } else {
                    //不使用优惠劵和人群码(个人中心)
                    if (data.couponJson) {
                        //一美金优惠劵
                        this.couponJson = JSON.parse(data.couponJson).conditionDescription || JSON.parse(data.couponJson).title;
                    }else if(data.ordersPromotionDiscounts.length){
                        //个人中心过来不显示优惠劵列表
                        if(this.isOrder || this.isArtWork){
                            this.showcouponJsonDiscountPrice = false;
                        }
                        // }else if(this.isArtWork){ //图稿确认过来
                        // 	let find = data.ordersPromotionDiscounts.find((res)=>res.type === 1);
                        // 	if(find){
                        // 		this.couponJson = find.activityNameEn
                        // 		this.couponJsonDiscountPrice = find.discountPrice;
                        // 	}
                        // }
                    }else if(this.isArtWork && !data.couponJson){
						this.couponId = this.couponList.length ? this.couponList[0].uniqueId : 0;
						this.radioGroup.push(1);
					}else {
                        if (!data.crowdDiscount && !data.couponDiscount) {
                            this.couponList.forEach((item, index) => {
                                if (item.id === 3) {
                                    this.couponId = 3;
                                } else if (item.id === 4) {
                                    this.couponId = 4;
                                } else {
                                    this.couponId = item.id;
                                }
                                this.couponJson = item.conditionDescription;
                                this.radioGroup.push(index + 1);
                            });
                        }
                    }
                }

                //人群码
                if (data.crowdDiscount) {
                    this.couponList = [];
                    let find = this.couponList.find((f) => f.id === 3);
                    if (find) {
                        this.couponId = find.id;
                    }
                }
            }

            // //type=3平台级优惠，type=1优惠劵，type=2优惠码，平台级优惠可以叠加优惠劵或者优惠码
            // let find = data.ordersPromotionDiscounts.every((res) => res.type === 3);
            // //个人中心、链接订单过来，未使用优惠劵都是平台级优惠或者可以选优惠卷的情况，不隐藏优惠劵列表
            // if(find || this.triggerInput === true){
            //     this.showCouponAll = true;
            // }else{
            //     //下单时使用过优惠劵，未付款  隐藏优惠劵列表，不可以更换优惠劵
            //     this.showCouponAll = false;
            // }

            //pens网站使用'PENS20'优惠码未减钱提示文案
            if(this.isPensSite && data.crowdDiscountPrice === 0 && this.nCouponCodeTemp === 'PENS20'){
                this.$toast.error(this.langCart.pensText);
            }
        },
        updatePrice(data) {
            this.additionalTariffFees=data.additionalTariffFees;
            this.apoPaymentStatus = data.paymentStatus;
            this.POPCurrencySymbol = {
                rate:data.givingQuotation,
                symbol:data.currencySymbol,
                symbolCode:data.currency
            };
            this.orderDiscount = data.orderDiscount;
            this.orderDiscountPrice = data.orderDiscountPrice;
            this.productPrice = data.productPrice;
            this.totalPrice = data.totalPrice;
            this.taxPrice = data.taxPrice;
            this.apoShippingPrice = data.apoShippingPrice;
            this.payAdditionalFee = data.payAdditionalFee;
            this.discountPrice = data.discountPrice;
            this.pointPrice = data.pointPrice;
            //人群码优惠
            this.crowdDiscount = data.crowdDiscount;
            this.crowdDiscountPrice = data.crowdDiscountPrice;
            //第一次下单折扣
            this.firstDiscount = data.firstDiscount;
            this.firstDiscountPrice = data.firstDiscountPrice;
            //优惠券
            this.couponDiscount = data.couponDiscount;
            this.couponDiscountPrice = data.couponDiscountPrice;
            //代金券
            this.voucherPrice = data.voucherPrice;
            //额外折扣
            this.promotionPrice = data.promotionPrice;
            // this.promotionDiscount = data.promotionDiscount;
            this.ordersPromotionDiscounts = data.ordersPromotionDiscounts;
            //用户等级折扣
            this.userLevelDiscount = data.ordersDiscount !== null ? data.ordersDiscount.userLevelDiscount : data.userLevelDiscount;
            this.userLevelDiscountPrice = data.ordersDiscount !== null ? data.ordersDiscount.userLevelDiscountPrice : data.userLevelDiscountPrice;
            this.couponAdditionalCosts = data.couponAdditionalCosts;
            this.userLevelId = data.userLevelId;
            this.currentTaxRate = data.currentTaxRate;
            // 促销中心额外收费
            this.promotionCenterAdditionalPrice = data.promotionCenterAdditionalPrice;
            //样品单
            this.isSampleOrder = data.isSampleOrder;
			//订阅折扣提示
			this.codeInvalidInfo = data.codeInvalidInfo;

            //pens网站
            if (this.isPensSite || this.isStickerSite || this.isCouponAllowed) {
                this.couponApplyType = data.couponApplyType;
                this.couponType = data.couponType;
                //等于2的时候回填优惠码
                if (data.couponType === 2) {
                    this.couponJson = data.crowdCode;
                } else {
                    //不使用优惠劵和人群码(个人中心)
                    if (data.couponJson) {
                        this.couponId = data.couponId;
                        this.couponJson = JSON.parse(data.couponJson).conditionDescription || JSON.parse(data.couponJson).title;
                        this.radioGroup.push(1);
                    } else {
                        //如果没有人群码并且没有优惠劵
                        if (!data.crowdDiscount && !data.couponDiscount) {
                            this.couponList.forEach((item, index) => {
                                if (item.uniqueId === this.couponId) {
                                    this.couponId = item.uniqueId;
                                    this.couponJson = item.conditionDescription; //个人中心过来回显
                                    this.radioGroup.push(index + 1);
                                }
                            });
                        }
                    }
                }
                //购物车过来,couponRadioshow：checkout是否重新切换优惠劵，路由带有couponId
                if (data.couponId === null && this.couponRadioshow === 0 && this.$route.query.couponId == "couponId=4") {
                    this.couponId = 4;
                    this.radioGroup.push(4);
                } else if (this.$route.query.couponId) {
                    //有优惠卷id
                    this.couponList.forEach((item, index) => {
                        if (this.$route.query.couponId === item.uniqueId) {
                            this.radioGroup.push(index + 1);
                        }
                    });
                } else if (this.couponRadioshow !== 0) {
                    //重新选优惠劵
                    //couponId有值表示是优惠劵，没有值表示是优惠码或者不用
                    if (data.couponId) {
                        this.couponId = data.couponId;
                        this.couponList.forEach((item, index) => {
                            if (data.couponId === item.uniqueId) {
                                this.radioGroup.push(index + 1);
                            }
                        });
                    } else {
                        this.couponList.forEach((item, index) => {
                            if (this.couponId === item.uniqueId) {
                                this.couponId = item.uniqueId;
                                this.radioGroup.push(index + 1);
                            }
                        });
                    }
                } else if (data.couponJson) {
                    this.couponList.forEach((item, index) => {
                        if (item.uniqueId === JSON.parse(data.couponJson).id) {
                            this.radioGroup.push(index + 1);
                        }
                    });
                } else {
                    //报价直接进checkout，默认选中第一个优惠劵
                    if (this.couponList.length) {
                        this.radioGroup.push(1);
                        this.couponId = this.couponList[0].uniqueId;
                    }
                }
            }

            if (this.isPensSite && data.crowdDiscountPrice === 0 && this.nCouponCodeTemp === "PENS20") {
                this.$toast.error(this.langCart.pensText);
            }
        },
        async updateAddress() {

            try {
                //判断是否是询盘生成的订单，询盘生成的订单无地址
                this.isUpdateAddress = true;
                let orderData = this.getAddOrderData();

                let res;
                if (this.isLogin) {
                    res = await editOrderAddress(
                            Object.assign({}, orderData, {
                                oid: this.orderId,
                            })
                    );
                } else {
                    res = await editOrderAddressNotLogin(
                            Object.assign({}, orderData, {
                                oid: this.orderId,
                            })
                    );
                }
                this.isUpdateAddress = false;
                this.updatePrice2(res.data);
                return res.data;
            } catch (e) {
                console.log(e);
                this.isUpdateAddress = false;
            }
        },
        getPicPath(item) {
            if (item.picPath.startsWith("[{")) {
                return JSON.parse(item.picPath)[0].picLink;
            } else {
                return item.picPath || null;
            }
        },
        //优惠卷使用
        useCoupon() {
            return new Promise(async (resolve) => {

                if(this.nCouponCodeTemp){
                    this.couponList = [];
                }else{
                    this.fetchUserCouponList();
                }
                let emailRules = this.noLoginEmailRules;
                for (let i = 0; i < emailRules.length; i++) {
                    let bool = emailRules[i](this.inputEmail);
                    if (typeof bool === "boolean" && bool) {
                        this.emailErrorMessage = "";
                    } else {
                        this.emailErrorMessage = bool;
                        break;
                    }
                }
                //未填写邮箱
                if (this.emailErrorMessage) {
                    this.$toast.error(this.langCart.pie);
                    return false;
                }
                if (!this.nCouponCodeTemp) {
                    this.nCouponCode = "";
                    this.showcoupon=true;
                    await this.debounceCalcPrice();
                    resolve();
                    return false;
                }
                this.nCouponCode = this.nCouponCodeTemp;
                this.editOrderDiscount(false);
                await this.debounceCalcPrice();
                resolve();
            });
        },
        filterName(item, queryText, itemText) {
            if (item.acronymCode) {
                const normalizedQuery = queryText.toLocaleLowerCase().replace(/\s/g, ""); //文本转换为小写并移除所有空格
                const normalizedItemText = itemText.toLocaleLowerCase().replace(/\s/g, "");
                const normalizedAcronymCode = item.acronymCode.toLocaleLowerCase().replace(/\s/g, "");
                return normalizedItemText.includes(normalizedQuery) || normalizedAcronymCode.includes(normalizedQuery);
            }
            const normalizedQuery = queryText.toLocaleLowerCase().replace(/\s/g, "");
            const normalizedItemText = itemText.toLocaleLowerCase().replace(/\s/g, "");
            return normalizedItemText.includes(normalizedQuery);
        },
        isRegister(val) {
            isOldUser({
                email: val,
            }).then((res) => {
                if (res.data === 0) {
                    this.$store.commit("setLogin", "login");
                    this.$store.commit("setLoginEmail", val);
                }
                // 不管是新用户还是老用户，未登录情况下输入邮箱号都调用获取优惠劵接口
                if (this.isStickerSite || this.isPensSite || this.isCouponAllowed) {
                    this.getUserCouponEmail();
                }
            });
            let emailRules = this.noLoginEmailRules,
                    emailErrorMessage;
            for (let i = 0; i < emailRules.length; i++) {
                let bool = emailRules[i](val);
                if (typeof bool === "boolean" && bool) {
                    emailErrorMessage = "";
                } else {
                    emailErrorMessage = bool;
                    break;
                }
            }
            if (!emailErrorMessage) {
                this.debounceCalcPrice();
            }
            if (this.addressStatus === 0) {
                this.getSmsSubscriptionNotLogin();
            }
            
        },
        getState(data) {
            return new Promise((resolve) => {
                getStateList(data).then((res) => {
                    resolve(res.data);
                });
            });
        },
        getCountry() {
            return new Promise((resolve) => {
                getCountryList().then((res) => {
                    this.countryList = res.data;
                    resolve(res.data);
                });
            });
        },
        validateForm() {
            let result = [];
            //子订单不需要校验地址，自动采用主订单的地址
            if (!this.isChildOrder) {
                let resultShipping = this.$refs.shippingAddressForm.validate();
                let resultBill = false;
                //如果是paypal支付，不需要填写账单
                if (this.payTypeId === 10000) {
                    resultBill = true;
                } else {
                    if (this.billingAddressStatus === 0) {
                        resultBill = resultShipping;
                    } else if (this.billingAddressStatus === 1) {
                        resultBill = this.$refs.billAddressForm.validate();
                    }
                }
                if (!resultShipping || !resultBill) {
                    result.push({
                        message: null,
                        type: "error",
                    });
                }
            }
            return result;
        },

        toComplete() {
            //存在子订单，拿主订单的oid
            if (this.isChildOrder) {
                this.$router
                        .replace({
                            path: `/order/complete/${this.mainOrderId}`,
                        })
                        .then(() => {
                            this.$gl.hide();
                        });
            } else {
                this.$router
                        .replace({
                            path: `/order/complete/${this.orderId}`,
                        })
                        .then(() => {
                            this.$gl.hide();
                        });
            }
        },

        //支付校验
        verifyPayment(data) {
            this.$gl.show();
            autoVerifyPayment(data).finally(() => {
                localStorage.removeItem("tempCart");
                this.toComplete();
            });
        },

        cancelPay() {
            this.payBeforeDialog = false;
        },

        handleZfBefore() {
            return new Promise(async (resolve, reject) => {
                try {
                    await this.addOrder();
                    //如果不是直接订单并且不是子订单
                    console.log(this.isDirectOrder, this.isChildOrder);
                    if (!this.isDirectOrder && !this.isChildOrder) {
                        await this.editOrderDiscount(true);
                        await this.updateAddress();
                    }
                    let resData = await this.getMyOrderInfo({
                        oid: this.mainOrderId || this.orderId,
                        getProductInfo: true,
                        getAddressInfo: true,
                        getDiscountInfo: true,
                    });
                    let result2 = this.findPendingPaymentOrder(resData);
                    if (result2) {
                        //更新价格
                        this.updatePrice(this.waitPaymentOrder);
                    }
                    this.userEmail = resData.data.email;
                    return resolve();
                } catch (e) {
                    return reject(e);
                }
            });
        },

        //提交按钮
        submitOrderBefore() {
            let find = this.countryList.find((item) => item.id === this.shippingAddressForm.shippingCountryId);
            if (find) {
                this.taxName1 = find.taxName === null ? "Tax Price" : find.taxName;
                this.VATtext = find.continentId === 5 ? true : false;
            }
            let result = this.validateForm();
            if (result.length) {
                if (result[0].message) {
                    this.$toast.error(result[0].message);
                }
                return false;
            }
            this.payBeforeDialog = true;
        },

        async submitOrder() {
            try {
                this.payBeforeDialog = false;
                this.$gl.show();
                await this.handleZfBefore();
				try {
                    // 记录Stripe唤起支付错误日志
                    let date=getDate();
                    let time = new Date().toTimeString().substring(0,8);
                    let logDate=date+' '+time
                    let errorLogsData={
                        oid: this.isChildOrder ? this.orderId : this.mainOrderId,
                        errorType:1,
                        payType: 2,
                        logDate
                    }
                    paymentErrorLogs(errorLogsData);
                }catch(e){
                    console.log(e);
                }
                if (this.isZeroOrder || this.payTypeId === 0) {
                    this.verifyPayment({
                        id: this.orderId,
                        paymentSerialNum: "",
                        cartIdList: this.selectCartList.map((item) => item.id),
                        cartUuid: this.cartUuid,
                        givingQuotation: this.currentCurrency.rate,
                        currency: this.symbolCode,
                        countryId: this.customCountryId,
                    });
                    return false;
                } else if (this.payTypeId === 10010) {
                    this.$gl.hide();
                    this.stripeStatus = true;
                    await this.$nextTick();
                    this.setLoading(true);
                    await this.getPayIntent();
                    await this.initStripePay();
                }
            } catch (e) {
                this.payBeforeDialog = false;
                this.$gl.hide();
            }
        },

        loadPayPalSDK(paypalConfig) {
            const fn = () => {
                return loadScript({
                    "client-id": paypalConfig.clientId,
                    "enable-funding": "venmo",
                    components: ["buttons", "hosted-fields", "funding-eligibility"],
                    "data-client-token": paypalConfig.clientToken,
                    currency: this.symbolCode,
                    intent: "capture",
                });
            };
            let retryLoadScript = withRetryAsync(fn, 3);
            return retryLoadScript();
        },

        async initPaypalPaymentConfig() {
            let paypalConfig = this.paypalConfig,
                    payTypeList = this.payTypeList;
            if (!payTypeList.length) {
                return;
            }
            //获取paypal的配置
            let paypalItem = payTypeList.find((item) => {
                return item.payType.id === 10000;
            });
            try {
                let res = await paypalApi.generateAccessToken(
                        {
                            payId: paypalItem.payType.id,
                            countryId: this.customCountryId,
                        },
                        this.paypalBaseUrl
                );
                paypalConfig.accessToken = res.data.access_token;
                paypalConfig.clientId = res.data.pay_config.clientId;
                paypalConfig.clientToken = await paypalApi.generateClientToken(this.paypalConfig.accessToken, this.paypalBaseUrl);
            } catch (e) {
                this.$toast.error("System Error: Please reach out to our customer service team at your earliest convenience. Thank you!");
            }
            // if (!res.data.pay_config.merchantId) {
            // 	this.$toast.show('Please bind the merchant first')
            // }
            // paypalConfig.merchantId = res.data.pay_config.merchantId || 'WPQW3YQEDPWML';
            this.loadPayPalSDK(paypalConfig)
                    .then((paypal) => {
                        var FUNDING_SOURCES = [paypal.FUNDING.PAYPAL];
                        let _this = this;
                        FUNDING_SOURCES.forEach(function (fundingSource) {
                            var button = paypal.Buttons({
                                fundingSource: fundingSource,
                                style: {
                                    layout: "vertical",
                                    color: "blue",
                                    shape: "rect",
                                    height: 36,
                                },
                                onClick: async function (data, actions) {
                                    try {
                                        _this.payBeforeDialog = false;
                                        await _this.handleZfBefore();
										try {
                                            //记录PayPel唤起支付错误日志
                                            let date=getDate();
                                            let time = new Date().toTimeString().substring(0,8);
                                            let logDate=date+' '+time
                                            let errorLogsData={
                                                oid: _this.isChildOrder ? _this.orderId : _this.mainOrderId,
                                                errorType:1,
                                                payType: 1,
                                                logDate
                                            }
                                            paymentErrorLogs(errorLogsData);
                                        }
                                        catch(e){
                                            console.log(e);
                                        }
                                        return actions.resolve();
                                    } catch (e) {
                                        console.log(e)
                                        return actions.reject();
                                    }
                                },
                                createOrder: async function (data, actions) {
                                    return actions.order.create({
                                        purchase_units: [
                                            {
                                                amount: {
                                                    currency_code: _this.symbolCode,
                                                    value: round2(_this.totalPrice * _this.currentCurrency.rate),
                                                },
                                                custom_id: `order|${_this.orderId}|${_this.customCountryId}`,
                                                invoice_id: _this.orderId.toString(),
                                            },
                                        ],
                                        application_context: { shipping_preference: "NO_SHIPPING" },
                                    });
                                },
                                onApprove: function (data, actions) {
                                    return actions.order.capture().then(function (orderData) {
                                        _this.verifyPayment({
                                            id: _this.orderId,
                                            paymentSerialNum: orderData.id,
                                            cartIdList: _this.selectCartList.map((item) => item.id),
                                            cartUuid: _this.cartUuid,
                                            givingQuotation: _this.currentCurrency.rate,
                                            currency: _this.symbolCode,
                                            countryId: _this.customCountryId,
                                        });
                                    });
                                },
                                onCancel: function () {
                                    _this.toOrderDetail();
                                },
                                onError: function (error) {
                                    //支付失败的情况
                                    let failData = {
                                        oid: _this.isChildOrder ? _this.orderId : _this.mainOrderId,
                                        paymentMethod: "Paypal",
                                        message: JSON.stringify(error),
                                    };
                                    orderPayFail(failData);
									try {
                                        //记录PayPel支付错误日志
                                        let date=getDate();
                                        let time = new Date().toTimeString().substring(0,8);
                                        let logDate=date+' '+time
                                        let errorLogsData={
                                                oid: _this.isChildOrder ? _this.orderId : _this.mainOrderId,
                                                payType: 1,
                                                errorMsg: JSON.stringify(error),
                                                errorType:2,
                                                logDate
                                        }
                                        paymentErrorLogs(errorLogsData);
                                    }catch(e){
                                        console.log(e);
                                    }
                                },
                            });
                            if (button.isEligible()) {
                                button.render("#paypal-button-container").catch(() => {});
                            }
                        });
                    })
                    .catch((error) => {
                        this.$toast.error("failed to load the PayPal JS SDK script");
                    });
        },

        // 预支付
        async getPayIntent() {
            let orderData = {
                orderId: this.orderId,
                source: "order",
                currency: this.symbolCode,
                currencyRate: this.currentCurrency.rate,
                countryId: this.customCountryId,
            };
            if (this.isChildOrder) {
                // GooglePay测试接口，金额最小
                // const {data} = await minAmountIntent(orderData);
                const { data } = await childPaymentIntent(orderData);
                this.paymentSerialNum = data.paymentSerialNum;
                this.clientSecret = data.clientSecret;
            } else {
                // GooglePay测试接口，金额最小
                // const {data} = await minAmountIntent(orderData);
                const { data } = await paymentIntent(orderData);
                this.paymentSerialNum = data.paymentSerialNum;
                this.clientSecret = data.clientSecret;
            }
        },

        // stripe初始化
        async initStripePay() {
            try {
                let stripeAccountId = this.stripeAccountId;
                // 初始化Stripe.js
                const stripe = await loadStripe(this.stripeKey, stripeAccountId ? { stripeAccount: stripeAccountId } : "");
                const options = {
                    clientSecret: this.clientSecret,
                    // 自定义样式
                    appearance: {},
                    locale: "en",
                };
                const elements = stripe.elements(options);
                this.elements = elements;
                const paymentElement = elements.create("payment");
                paymentElement.mount("#payment-element");
                let form = document.querySelector("#payment-form");
                this.$nextTick(() => {
                    this.setLoading(false);
                });
                form.addEventListener("submit", async (e) => {
                    e.preventDefault();
                    this.setLoading(true);
                    let data = {
                        id: this.orderId,
                        paymentSerialNum: this.paymentSerialNum,
                        cartIdList: this.selectCartList.map((item) => item.id),
                        cartUuid: this.cartUuid,
                        givingQuotation: this.currentCurrency.rate,
                        currency: this.symbolCode,
                        countryId: this.customCountryId,
                    };
                    const { error, paymentIntent } = await stripe.confirmPayment({
                        elements,
                        confirmParams: {
                            return_url: `${window.location.origin}/stripeComplete?data=${JSON.stringify(data)}`,
                        },
                        redirect: "if_required",
                    });
                    if (error) {
                        console.log(error);
                        let errorType = error.type;
                        if (errorType === "validation_error" || errorType === "card_error") {
                            this.$toast.error(error.message);
                        } else {
                            //stripe 支付失败
                            let failData = {
                                oid: this.isChildOrder ? this.orderId : this.mainOrderId,
                                paymentMethod: "Stripe",
                                message: JSON.stringify(error),
                            };
                            orderPayFail(failData);
                            this.$toast.error(error.message?error.message:"An unexpected error occurred.");
							try {
                                //记录stripe支付错误日志
                                let date=getDate();
                                let time = new Date().toTimeString().substring(0,8);
                                let logDate=date+' '+time
                                let errorLogsData={
                                        oid: this.isChildOrder ? this.orderId : this.mainOrderId,
                                        payType: 2,
                                        errorMsg: JSON.stringify(error),
                                        errorType:2,
                                        logDate
                                    }
                                paymentErrorLogs(errorLogsData);
                            }
                            catch(e) {
                                console.log(e);
                            }
                        }
                        this.setLoading(false);
                    }
                    if (paymentIntent) {
                        switch (paymentIntent.status) {
                            case "succeeded":
                                this.verifyPayment(data);
                                break;
                            case "processing":
                                this.verifyPayment(data);
                                break;
                            default:
                                this.$toast.error("An unexpected error occurred.");
                                this.setLoading(false);
                        }
                    }
                });
            } catch (e) {
                console.log(e);
            }
        },

        // Show a spinner on payment submission
        setLoading(isLoading) {
            if (isLoading) {
                // Disable the button and show a spinner
                document.querySelector("#submit").disabled = true;
                document.querySelector("#spinner").classList.remove("hidden");
                document.querySelector("#button-text").classList.add("hidden");
                this.showProgress = true;
            } else {
                document.querySelector("#submit").disabled = false;
                document.querySelector("#spinner").classList.add("hidden");
                document.querySelector("#button-text").classList.remove("hidden");
                this.showProgress = false;
            }
        },

        async calcPrice() {
            //订单图稿过来的不用调用此接口
            if (this.isOrder || this.isArtWork) {
                return false;
            }
            try {
                let res;
                let data = {
                    inquiryId: "",
                    email: this.inputEmail,
                    cartUuid: this.cartUuid,
                    cartIdList: this.selectCartList.map((item) => item.id),
                    paymentMethodId: this.paymentMethodId,
                    shippingStateId: this.shippingAddressForm.shippingStateId,
                    apoAddress: this.shippingAddressForm.apoAddress,
                    apoCity:this.shippingAddressForm.shippingApoCity,
                    apoState:this.shippingAddressForm.shippingApoState,
                    apoZip:this.shippingAddressForm.shippingApoZip,
                    couponId: this.couponId,
                    voucherId: this.voucherId,
                    pointPrice: Math.round((this.pointDiscountPrice / this.currentCurrency.rate) * 100) / 100,
                    referCode: this.nCouponCode,
                    shippingCountryId: this.shippingAddressForm.shippingCountryId,
                    shippingZipCode: this.shippingAddressForm.shippingZipCode,
                    isClick:this.$route.query.isClick ? this.$route.query.isClick : 0,
                };
                if (this.isPensSite || this.isStickerSite || this.isCouponAllowed) {
                    if (this.couponId !== 4 && this.couponId !== 3) {
                        data.couponApplyType = 2;
                    }
                    data.currency = this.symbolCode;
                }
                let find;
                if (this.isPensSite || this.isStickerSite || this.isCouponAllowed) {
                    find=this.couponList && this.couponList.find((t)=>t.uniqueId == this.couponId);
                }else{
                    find=this.couponOption && this.couponOption.find((t)=>t.uniqueId == this.couponId);
                }
				if(this.giveawayItem){
					data.freebieId = this.giveawayItem.id;
				}

                if(find){
                    //isNew:1 新优惠劵；isNew:0 旧优惠劵
                    if(find.isNew === 1){
                        data.uniqueId = find.uniqueId;
                        data.activityId = find.activityId;
                    }
                }
                if (this.isLogin) {
                    res = await checkoutPrice(data);
                } else {
                    res = await checkoutPriceNotLogin(data);
                }
                this.updatePrice(res.data);
                return res;
            } catch (e) {
                await this.$router.push({
                    path: "/cart",
                });
            }
        },
        getAddOrderData() {
            let data,
                    shippingAddressForm = this.shippingAddressForm,
                    billingAddressForm = this.billingAddressForm,
                    billingAddressStatus = this.billingAddressStatus;
            let saveAddress = this.saveAddress || 0;
            //如果用户没有地址，默认存储一个
            if (this.isLogin && this.userShippingAddressList.length === 0 && this.userApoAddressList.length === 0) {
                saveAddress = 1;
            }
            let shippingState = this.currentShippingState?.stateName || shippingAddressForm.shippingState;
            let billState = this.currentBillingState?.stateName || billingAddressForm.billState;
            let aa = this.shippingAddressForm.shippingFirstName &&
                    this.shippingAddressForm.shippingLastName &&
                    this.shippingPrefixNumber &&
                    this.shippingAddressForm.shippingPhoneNumber &&
                    this.shippingAddressForm.apoAddress &&
                    this.shippingAddressForm.shippingApoCity &&
                    this.shippingAddressForm.shippingApoState &&
                    this.shippingAddressForm.shippingApoZip;
            let tt;
            if(this.isDirectOrder){
                tt= shippingAddressForm.shippingCountryId;
            }else if(!this.isDirectOrder && this.addressStatus === 1 && aa){
                tt = 226;
            }else if(!this.isDirectOrder && this.addressStatus === 0){
                tt= shippingAddressForm.shippingCountryId;
            }

            data = {
                currency: this.symbolCode,
                paymentMethodId: this.paymentMethodId,
                paymentMethod: this.paymentMethodText,
                cartIdList: this.selectCartList.map((item) => item.id),
                inquiryId: "",
                email: this.inputEmail,
                cartUuid: this.cartUuid,
                saveAddress: saveAddress,
                apoAddress: shippingAddressForm.apoAddress,
                shippingFirstName: shippingAddressForm.shippingFirstName,
                shippingLastName: shippingAddressForm.shippingLastName,
                shippingAddressLine1: shippingAddressForm.shippingAddressLine1,
                shippingAddressLine2: shippingAddressForm.shippingAddressLine2,
                shippingCountryCode: this.currentShippingCountry?.code,
                shippingPhoneNumber: this.shippingPrefixNumber + "-" + shippingAddressForm.shippingPhoneNumber === "null-null" ? '' : this.shippingPrefixNumber + "-" + shippingAddressForm.shippingPhoneNumber,
                shippingCity: shippingAddressForm.shippingCity,
                shippingState: shippingState,
                shippingStateId: shippingAddressForm.shippingStateId,
                shippingCountry: this.currentShippingCountry?.countryName,
                shippingCountryId: tt,
                shippingZipCode: shippingAddressForm.shippingZipCode,
                billFirstName: billingAddressStatus === 0 ? shippingAddressForm.shippingFirstName : billingAddressForm.billFirstName,
                billLastName: billingAddressStatus === 0 ? shippingAddressForm.shippingLastName : billingAddressForm.billLastName,
                billAddressLine1: billingAddressStatus === 0 ? shippingAddressForm.shippingAddressLine1 : billingAddressForm.billAddressLine1,
                billAddressLine2: billingAddressStatus === 0 ? shippingAddressForm.shippingAddressLine2 : billingAddressForm.billAddressLine2,
                billCountryCode: billingAddressStatus === 0 ? this.currentShippingCountry?.code : this.currentBillingCountry?.code,
                billPhoneNumber: billingAddressStatus === 0 ? this.shippingPrefixNumber + "-" + shippingAddressForm.shippingPhoneNumber : this.billingPrefixNumber + "-" + billingAddressForm.billPhoneNumber,
                billCity: billingAddressStatus === 0 ? shippingAddressForm.shippingCity : billingAddressForm.billCity,
                billState: billingAddressStatus === 0 ? shippingState : billState,
                billStateId: billingAddressStatus === 0 ? shippingAddressForm.shippingStateId : billingAddressForm.billStateId,
                billCountry: billingAddressStatus === 0 ? this.currentShippingCountry?.countryName : this.currentBillingCountry?.countryName,
                billCountryId: billingAddressStatus === 0 ? shippingAddressForm.shippingCountryId : billingAddressForm.billCountryId,
                billZipCode: billingAddressStatus === 0 ? shippingAddressForm.shippingZipCode : billingAddressForm.billZipCode,
                apoZip: shippingAddressForm.shippingApoZip,
                apoCity: shippingAddressForm.shippingApoCity ? shippingAddressForm.shippingApoCity.substring(0, 3) : "",
                apoState: shippingAddressForm.shippingApoState ? shippingAddressForm.shippingApoState.substring(0, 2) : "",
                isMobile: this.$store.getters.isMobile ? 0 : 1,
                shippingCompany: shippingAddressForm.shippingCompany ? shippingAddressForm.shippingCompany : "",
                isSmsSubscriptions: this.isSMSCheckbox === true ? 1 : 0,
            };

            //apo地址清空多余属性
            if(this.addressStatus===1){
               data.shippingAddressLine1 = "";
               data.shippingAddressLine2 = "";
               data.shippingCountryCode = "";
               data.shippingCity = "";
               data.shippingCountry = "";
               data.shippingState = "";
               data.shippingStateId = "";
               data.shippingZipCode = "";
            }

            if (this.payTypeId === 0) {
                data = Object.assign({}, data, {
                    deferredPaymentStatus: this.payLater,
                });
            }

            data = Object.assign({}, data, {
                couponId: this.couponId,
                voucherId: this.voucherId,
                pointPrice: Math.round((this.pointDiscountPrice / this.currentCurrency.rate) * 100) / 100,
                referCode: this.nCouponCode,
            });
            if (data.shippingCountryId === 81 || data.shippingCountryId === 224) {
                data.shippingState = "";
                data.shippingStateId = "";
            }
            if (data.billCountryId === 81 || data.billCountryId === 224) {
                data.billState = "";
                data.billStateId = "";
            }

            if (this.isPensSite || this.isStickerSite || this.isCouponAllowed) {
                if (this.couponId !== 4 && this.couponId !== 3) {
                    data.couponApplyType = 2;
                }
            }

            //购物车选了优惠卷过来couponId有值，没有选就用couponIdAsNumber
            let id = this.couponId ? this.couponId : this.couponIdAsNumber;
            let find
            if (this.isPensSite || this.isStickerSite || this.isCouponAllowed) {
                find=this.couponList && this.couponList.find((t)=>t.uniqueId == id);
            }else{
                find=this.couponOption && this.couponOption.find((t)=>t.uniqueId == id);
            }

            if(find){
                //isNew:1 新优惠劵；isNew:0 旧优惠劵
                if(find.isNew === 1){
                    data.uniqueId = find.uniqueId;
                    data.activityId = find.activityId;
                }else if(find.isNew === 0){
                    data.couponId=find.id;
                }
            }else{
                data.couponId=this.couponId;
            }
			if(this.giveawayItem){
				data.freebieId = this.giveawayItem.id;
                data.freebieParam = JSON.stringify({
                        Size: [{
                                isEmailLater: 0,
                                paramName: this.giveawayItem.param || ""
                        }],
                        Text: [{
                                isEmailLater: 0,
                                paramName: this.giveawayItem.neonCustomText || ""
                        }],
                        Color: [{
                                isEmailLater: 0,
                                paramName: this.giveawayItem.neonCustomColor ? this.giveawayItem.neonCustomColor[this.giveawayItem.id]?.paramNameZh : this.giveawayItem.defaultColor?.paramNameZh
                        }],
                        Font:[{
                            isEmailLater: 0,
                            paramName: this.giveawayItem.neonCustomFont ? this.giveawayItem.neonCustomFont.paramNameZh : this.giveawayItem.defaultFont?.paramName
                        }]
                })
			}
            return data;
        },
        async addOrder() {
            if (this.orderId) {
                return;
            }
            let res;
            if (this.isLogin) {
                res = await addOrder(this.getAddOrderData());
                this.orderId = res.data.oid;
            } else {
                res = await addOrderNotLogin(this.getAddOrderData());
                this.orderId = res.data.orderId.oid;
            }
            this.$router.replace({
                path: this.$route.path,
                query: {
                    orderid: this.orderId,
                    type: "order",
                },
            });
        },
        getAddressList() {
            if (!this.isLogin) {
                return Promise.resolve();
            }
            let formatAddress = (item) => {
                return `${item.city},${item.state},${item.zipCode},${item.country}`;
            };
            let formatApoAddress = (item) => {
                try {
                    return JSON.parse(item.addressLine)[1];
                } catch (e) {
                    return item.addressLine;
                }
            };
            return new Promise((resolve) => {
                getAddressList().then((res) => {
                    let userShippingAddressList = res.data.shippingAddress;
                    let userApoAddressList = res.data.apoFpoAddress;
                    userShippingAddressList.forEach((item) => {
                        item.customShowAddress = formatAddress(item);
                    });
                    userApoAddressList.forEach((item) => {
                        item.customShowAddress = formatApoAddress(item);
                    });
                    this.userShippingAddressList = userShippingAddressList;
                    this.userApoAddressList = userApoAddressList;
                    resolve(res.data);
                });
            });
        },
        setDefaultPaymentMethod(id) {
            let payTypeList = this.payTypeList;
            if (!payTypeList) {
                return false;
            }
            let findPayment = payTypeList.find((item) => {
                return item.id === id;
            });
            if (findPayment) {
                this.paymentMethodId = findPayment.id;
                this.payTypeId = findPayment.payType.id;
            } else {
                this.paymentMethodId = payTypeList[0].id;
                this.payTypeId = payTypeList[0].payType.id;
            }
            this.changePaymentMethod(this.paymentMethodId);
        },
        getCartList() {
            return new Promise((resolve) => {
                getCartList({
                    uuid: this.$route.query.uuid,
                }).then((res) => {
                    resolve(res);
                });
            });
        },
        async getMyOrderInfo(data) {
            let res;
            if (this.isLogin) {
                res = await getOrderInfo(data);
            } else {
                res = await getOrderNotLogin(data);

				if (this.isStickerSite || this.isPensSite || this.isCouponAllowed) {
					this.oneDollarEmail = res.data;
					this.getUserCouponEmail();
				}
            }

            this.orderType = res.data.orderType;

            if (res.data.crowdDiscountPrice !== 0) {
                this.nCouponCodeTemp = res.data.crowdCode;
                this.disabledCouponCode = true;
                this.radioGroup.push(1);
            }
            if (res.data.couponId && res.data.couponId !== "0") {
                this.couponIdTemp = res.data.couponId;
                this.disabledCouponOption = true;
                this.radioGroup.push(2);
            }
            if (res.data.voucherPrice) {
                this.disabledVoucherOption = true;
                this.radioGroup.push(3);
            }
            if (res.data.pointPrice) {
                this.disabledPoint = true;
                this.pointDiscountPriceTemp = res.data.pointPrice;
                this.radioGroup.push(4);
            }
            let find = this.countryList.find((item) => item.id === res.data.shippingCountryId);
            if (find) {
                this.taxName1 = find.taxName === null ? "Tax Price" : find.taxName;
                this.VATtext = find.continentId === 5 ? true : false;
            }
            this.currentTaxRate = res.data.currentTaxRate;

            if (res.data.paymentStatus === 2) {
                res.data.childOrder.forEach((item) => {
                    this.freightCharge = item.addFee;
                    this.markupPercentage = item.shippingPrice;
                    this.addFeePercentage = item.addFeePercentage;
                });
            } else {
                this.freightCharge = res.data.addFee;
                this.markupPercentage = res.data.shippingPrice;
                this.addFeePercentage = res.data.addFeePercentage;
            }
            if (res.data.email) {
                let params = {
                    email: res.data.email,
                    proId: this.proId,
                };
                getSmsSubscriptionByMail(params).then((res) => {
                    if (res.data === null || (res.data && res.data.isSmsSubscriptions == 0)) {
                        this.showSMStext = true;
                    } else {
                        this.showSMStext = false;
                    }
                });
            }
            return res;
        },
        //判断是否有未支付的订单
        findPendingPaymentOrder(res) {
            //判断是否存在子订单，找到未支付的子订单
			//paymentStatus=1：已支付
            let findChildOrder;
            this.orderProductList = res.data.ordersProducts;
            if (res.data.childOrder && res.data.childOrder.length) {
                findChildOrder = res.data.childOrder.find((item) => {
                    return item.paymentStatus == 1;
                });
                console.log(findChildOrder);
                if (findChildOrder) {
                    //子订单的产品展示
                    res.data.childOrder.forEach((citem) => {
                        this.orderProductList = this.orderProductList.concat(citem.ordersProducts || []);
                    });
                    this.isChildOrder = true;
                }
            }
            //如果不存在未支付的子订单并且主订单已支付或者后付   || res.data.paymentStatus === 0
            if (!findChildOrder && res.data.paymentStatus === 2) {
                return false;
            }
            this.mainOrderId = res.data.oid;
            this.orderId = findChildOrder ? findChildOrder.oid : res.data.oid;
            this.orderGivingQuotation = res.data.givingQuotation; //订单汇率
            this.orderCurrency = res.data.currency; //订单货币code
            this.orderCurrencySymbol = res.data.currencySymbol; //订单货币符号
            this.waitPaymentOrder = findChildOrder ? findChildOrder : res.data;
            return true;
        },
        backFillAddress(data) {
            let cityName, stateName;
            this.APOCityList.forEach((item) => {
                if (item.name.substring(0, 3) === data.apoCity) {
                    cityName = item.name;
                }
            });
            this.APOStateList.forEach((item) => {
                if (item.name.substring(0, 2) === data.apoState) {
                    stateName = item.name;
                }
            });
            this.shippingAddressForm = {
                shippingFirstName: data.shippingFirstName,
                shippingLastName: data.shippingLastName,
                shippingAddressLine1: data.shippingAddressLine1,
                shippingAddressLine2: data.shippingAddressLine2,
                shippingCountryCode: data.shippingCountryCode,
                shippingPhoneNumber: data.shippingPhoneNumber,
                shippingCity: data.shippingCity,
                shippingState: data.shippingState,
                shippingStateId: data.shippingStateId,
                shippingCountry: data.shippingCountry,
                shippingCountryId: data.shippingCountryId,
                shippingZipCode: data.shippingZipCode,
                apoAddress: data.apoAddress,
                email: data.email,
                shippingApoZip: data.apoZip,
                shippingApoCity: cityName,
                shippingApoState: stateName,
                shippingCompany: data.shippingCompany,
            };
            if (data.shippingAddressLine2) {
                this.shippingAddressLine2 = true;
            }
            this.billingAddressForm = {
                billFirstName: data.billFirstName,
                billLastName: data.billLastName,
                billAddressLine1: data.billAddressLine1,
                billAddressLine2: data.billAddressLine2,
                billCountryCode: data.billCountryCode,
                billPhoneNumber: data.billPhoneNumber,
                billCity: data.billCity,
                billState: data.billState,
                billStateId: data.billStateId,
                billCountry: data.billCountry,
                billCountryId: data.billCountryId,
                billZipCode: data.billZipCode,
            };
            if (data.billAddressLine2) {
                this.billingAddressLine2 = true;
            }
            const checkAddressIsAlike = () => {
                return data.shippingAddressLine1 === data.billAddressLine1 && data.shippingAddressLine2 === data.billAddressLine2;
            };
            if (!checkAddressIsAlike()) {
                this.billingAddressStatus = 1;
            }
            try {
                if (data.shippingPhoneNumber.includes("-") || data.billPhoneNumber.includes("-")) {
                    this.shippingPrefixNumber = data.shippingPhoneNumber.split("-")[0];
                    this.shippingAddressForm.shippingPhoneNumber = data.shippingPhoneNumber.split("-")[1];
                    this.billingPrefixNumber = data.billPhoneNumber.split("-")[0];
                    this.billingAddressForm.billPhoneNumber = data.billPhoneNumber.split("-")[1];
                } else {
                    this.shippingPrefixNumber = "1";
                    this.shippingAddressForm.shippingPhoneNumber = data.shippingPhoneNumber;
                    this.billingPrefixNumber = "1";
                    this.billingAddressForm.billPhoneNumber = data.billPhoneNumber;
                }
            } catch (e) {
                console.log(e);
            }
            this.userEmail = data.email;
            this.getState({
                id: data.shippingCountryId,
            }).then((res) => {
                this.shippingStateList = res;
            });
            this.getState({
                id: data.billCountryId,
            }).then((res) => {
                this.billStateList = res;
            });
            this.$refs.shippingAddressForm?.resetValidation();
        },
        getStripeKey() {
            getStripeKey({ proId: this.proId, countryId: this.customCountryId }).then((res) => {
                this.stripeKey = res.data;
            });
        },
        //是否短信订阅(登录状态)
        getSmsSubscriptionLogin() {
            getSmsSubscriptionState().then((res) => {
                if (res.data === null || (res.data && res.data.isSmsSubscriptions == 0)) {
                    this.showSMStext = true;
                } else {
                    this.showSMStext = false;
                }
            });
        },
        //是否短信订阅(未登录状态)
        getSmsSubscriptionNotLogin() {
            let params = {
                email: this.shippingAddressForm.email,
                proId: this.proId,
            };
            getSmsSubscriptionByMail(params).then((res) => {
                if (res.data === null || (res.data && res.data.isSmsSubscriptions == 0) ) {
                    this.showSMStext = true;
                } else {
                    this.showSMStext = false;
                }
            });
        },
		showTooltip() {
			if(this.isMobile) return
			clearTimeout(this.hideTimeout);
			this.isTooltipVisible = true;
		},
		showTooltipMb(){
			this.isTooltipVisible = !this.isTooltipVisible;
		},
		hideTooltipDelayed() {
			this.hideTimeout = setTimeout(() => {
				this.isTooltipVisible = false;
			}, 500);
		},
		keepTooltipOpen() {
           clearTimeout(this.hideTimeout);
        },
		handleLearnMoreClick() {
			const currentDomain = window.location.origin;
            const targetUrl = `${currentDomain}/info/privacy-policy`;
			window.open(targetUrl, '_blank');
			this.isTooltipVisible = false;
		},
		clickOutside(elements, callback, eventType = 'click') {
			// 确保 elements 是一个数组
			if (!Array.isArray(elements)) {
				elements = [elements];
			}
			// 确保 elements 中的每个元素都是有效的 DOM 元素
			elements = elements.filter(element => element instanceof Element);
			function handleClickOutside(event) {
				// 检查事件目标是否在元素外部
				if (event.target && !elements.some(element => element.contains(event.target))) {
					callback(event);
				}
			}
			// 添加事件监听器
			document.addEventListener(eventType, handleClickOutside);
			// 返回一个函数，用于移除事件监听器
			return function removeClickListener() {
				document.removeEventListener(eventType, handleClickOutside);
			}
		},
		toProDetail(){
			goProDeatil({ productId: this.productId }).then((res) => {
				this.$router.push({
					path: res.data.productRouting,
					query:{ fromCheckout: 1}
				});
			});
		}
    },
    created() {
        this.debounceGetSpecificAddress = debounce(this.getSpecificAddress, 300);
        this.debounceGetSpecificAddress2 = debounce(this.getSpecificAddress2, 300);
        this.debounceVerifyAddress = debounce(this.verifyAddress, 300);
        // this.debounceShippingPhoneNumberInput = debounce(this.shippingPhoneNumberInput,300)

        getLowestPrice({
            oid: this.linkOrderId,
            proId: this.proId,
        }).then((res) => {
            this.lowestPrice = res.data?.lowestPrice;
        });
        this.debounceCalcPrice = debounce(this.calcPrice, 500);
        this.debounceEditOrderDiscount = debounce(this.editOrderDiscount, 500);
        this.getStripeKey();
        getAccountId().then((res) => {
            this.stripeAccountId = res.data.accountId;
        });
        if (this.isLogin) {
            this.getSmsSubscriptionLogin();
        }
		if(this.$route.query.fromDetail){
			this.isFromDetail = true;
			this.productId = this.$route.query.fromDetail;
		}
    },
    async mounted() {
        //添加类名隐藏手机端聊天框
        document.body.classList.add('uni-body');
        this.$gl.show();
        //获取国家列表和用户地址列表
        await Promise.all([this.getCountry(), this.getAddressList()]);
        if (document.getElementById("mainContent")) document.getElementById("mainContent").style.height = window.innerHeight - 68 + "px";
        //获取支付方式
        let payData = await getPayTypeList();
        this.payTypeList = payData.data.payList;
        this.paypal_sandBox = payData.data.paypal_sandBox; //paypal支付环境
        this.authorize_sandBox = payData.data.authorize_sandBox; //信用卡支付环境
        //初始化支付配置
        this.initPaypalPaymentConfig();
        if (this.isOrder || this.isArtWork) {
            await this.orderbackfill();
            // //获取订单信息
            // let res = await this.getMyOrderInfo({
            //     oid: this.linkOrderId,
            //     getProductInfo: true,
            //     getAddressInfo: true,
            //     getDiscountInfo: true,
            // });
            // // 判断是否有模具费的订单
            // this.isBuyAgainLowTips = this.$route.query.buyAgain && res.data.ordersProducts.find((ele) => ele.mouldPrice && ele.mouldPrice > 0) && this.lowestPrice && this.lowestPrice > 0;
            // //判断是否有未支付的订单
            // let result = this.findPendingPaymentOrder(res);
            // if (result) {
            //     //更新价格
            //     this.updatePrice(this.waitPaymentOrder);
            //     //如果apo地址存在，切换地址tab
            //     if (res.data.apoAddress) {
            //         this.addressStatus = 1;
            //     }
            //     await this.$nextTick();
            //     //地址回填,邮箱回填
            //     this.backFillAddress(res.data);
            //     //设置支付方式
            //     this.setDefaultPaymentMethod(res.data.paymentMethodId);
            // } else {
            //     this.$toast.show("Order has been paid", {
            //         duration: 0,
            //         action: [
            //             {
            //                 text: "YES",
            //                 onClick: (e, toastObject) => {
            //                     toastObject.goAway(0);
            //                     window.location.href = "/";
            //                 },
            //             },
            //         ],
            //     });
            //     this.isPaidOrder = true;
            //     this.$gl.hide();
            //     return false;
            // }
        } else if (this.isBuyNow) {
            let list = await this.getCartList();
            this.tempCartList = list.data;
            this.setDefaultPaymentMethod();
            //计算价格
            await this.debounceCalcPrice();
        } else {
            //普通下单
            if (!this.selectCartList.length) {
                await this.$router.push({
                    path: "/cart",
                });
                return false;
            }
            await this.debounceCalcPrice();
            this.setDefaultPaymentMethod();
        }
        await this.fetchUserCouponList();
        await this.getAllCoupon();

        this.$gl.hide();

        //type=3平台级优惠，type=1优惠劵，type=2优惠码，平台级优惠可以叠加优惠劵或者优惠码
        let find = this.ordersPromotionDiscounts && this.ordersPromotionDiscounts.every((res) => res.type === 3);
        //个人中心、链接订单过来，未使用优惠劵都是平台级优惠或者可以选优惠卷的情况，不隐藏优惠劵列表
        if(find || this.triggerInput === true){
            this.showCouponAll = true;
        }else{
            //下单时使用过优惠劵，未付款  隐藏优惠劵列表，不可以更换优惠劵
            this.showCouponAll = false;
        }


        // setTimeout(() => {
            //处理购物车过来的优惠券
            if (this.$route.query.couponId == 4) {
                this.couponId = 4;
                this.radioGroup.push(1);
            } else {
                this.couponId = this.$route.query.couponId;
                this.radioGroup.push(1);
            }

            if (this.$route.query.voucherId) {
                this.voucherId = this.$route.query.voucherId;
                this.radioGroup.push(3);
            }
            if (Number(this.$route.query.pointDiscountPrice) > 0) {
                this.pointDiscountPrice = Number(this.$route.query.pointDiscountPrice);
                this.radioGroup.push(4);
            }
        // }, 1500);


		//霓虹灯赠品
		let ll = localStorage.getItem('giveawayItem');
		this.giveawayItem = JSON.parse(ll);

        //处理德语情况
        if (this.language === "de") {
            this.shippingPrefixNumber = "49";
            this.billingPrefixNumber = "49";
            this.shippingAddressForm.shippingCountryId = 81;
            this.billingAddressForm.billCountryId = 81;
        }

		if(this.isMobile){
			let checkoutIcon = document.querySelector('.checkoutIcon.custom-Vtooltip')
			if (checkoutIcon) {
				this.clickOutside(checkoutIcon, this.hideTooltipDelayed);
			}
		}

    },
    beforeDestroy (){
        //切换页面移除类名
		document.body.classList.remove('uni-body');
	}
};
</script>

<style lang="scss">
.discount-item-div {
    display: flex;

    .discount {
        display: flex;
        align-items: center;
        justify-content: space-between;

        & > div:first-child {
            flex: 0 0 370px;
        }
    }

    .con {
        color: #de3500;
        width: 100%;
        text-align: right;
    }

    & > div:first-child {
        flex: 0 0 240px;
    }

    @include respond-to(mb) {
        flex-wrap: wrap;
        & > div:first-child {
            flex: 0 0 210px;
        }

        .discount {
            & > div:first-child {
                flex: 0 0 250px;
            }
        }
    }
}

.payBeforeDialog {
    .v-card__title {
        font-size: 20px !important;
        font-weight: 700 !important;
        @include respond-to(mb) {
            font-size: 16px !important;
        }
    }

    .v-card__text {
        font-size: 16px;
        color: #333333 !important;
        @include respond-to(mb) {
            font-size: 14px;
        }
    }

    .small-title {
        font-size: 18px;
        @include respond-to(mb) {
            font-size: 16px;
        }
    }

    .custom-row {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        grid-gap: 10px;
        align-items: center;
        @include respond-to(mb) {
            grid-template-columns: repeat(1, 1fr);
        }

        .item {
            min-width: 0;
        }

        .item:first-child span:first-child {
            width: 120px;
            text-align: right;
        }
    }

    .custom-row2 {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        grid-gap: 10px;
        align-items: center;

        .item {
            min-width: 0;
        }

        .item:first-child span:first-child {
            width: 120px;
            text-align: right;
        }

        @include respond-to(mb) {
            grid-template-columns: repeat(1, 1fr);
        }
    }

    .addressInfo .addressItem .item:first-child span:first-child {
        width: 120px;
        text-align: right;
        @include respond-to(mb) {
            width: auto;
        }
    }

    .addressInfo .item {
        display: flex;
        align-items: center;

        span:first-child {
            margin-right: 4px;
        }

        span:last-child {
            flex: 1;
            padding: 5px;
            border-radius: 2px;
            opacity: 1;
            background: #efefef;
            color: #666666;
        }
    }

    .foot {
        display: flex;
        justify-content: center;
        padding-bottom: 20px !important;

        button {
            width: 260px;
            margin: 0 10px;
            text-transform: capitalize;
            font-size: 14px;
        }

        #paypal-button-container {
            width: 260px;
        }

        @include respond-to(mb) {
            flex-direction: column;
            button {
                width: 80%;
                margin: 0 0 5px !important;
            }
            #paypal-button-container {
                width: 80%;
            }
        }
    }
}
.custom-Vtooltip.checkoutIcon{
	pointer-events: initial;
	padding: 0;
	opacity: 1;
	.text-center{
		padding: 5px 16px;
	}
}
</style>

<style scoped lang="scss">
[disabled] {
    pointer-events: none; //鼠标点击不可修改
}

.shipping-box{
    position: relative;
    .apoText{
        position: absolute;
        top: -23px;
        right: 0;
        background: #fff;
        display: flex;
        padding: 5px 7px;
        border: 1px solid #ccc;
        width: 69%;
        font-size: 14px;
        color: #3c3c3c;

        &::before {
            position: absolute;
            top: 48px;
            left: 41px;
            content: "";
            border-right: 10px transparent dashed;
            border-left: 10px transparent dashed;
            border-bottom: 10px transparent dashed;
            border-top: 10px #cccccc solid;
        }

        &::after {
            position: absolute;
            top: 46px;
            left: 41px;
            content: "";
            border-bottom: 10px transparent dashed;
            border-left: 10px transparent dashed;
            border-right: 10px transparent dashed;
            border-top: 10px #fffcfc solid;
        }
        b{
            font-size: 10px;
        }
}
}


.sms {
    ::v-deep .v-input__control .v-input__slot {
        padding: 0 !important;
    }

    ::v-deep .v-label {
        max-width: 100% !important;
    }
}

.checkoutBtn {
    width: 343px;
    height: 43px;
    background: linear-gradient(180deg, #ffffff 0%, #f9a128 0%, #ff6c00 100%);
    border-radius: 4px;
    color: #fff;
    font-size: 18px;

    &.disabled {
        background: #ccc;
        pointer-events: none;
        cursor: not-allowed;
    }
}

.SMStextMb{
    display: none;

    @include respond-to(mb) {
        display: flex;
        align-items: center;
        color: #757575;

        .icon{
            position: absolute;
            right: -7px;
            top: -7px;
            border-radius: 50%;
            border: 1px solid #959595;
            background: #fff;
            cursor: pointer;
            width: 20px;
            height: 20px;
            display: flex;
            justify-content: center;
            align-items: center;

            b{
                color:#959595;
            }
            @include respond-to(mb) {
                position: absolute;
                right: -4px;
                top: -3px;
            }
        }

        ::v-deep .v-input--checkbox {
            margin: 0;
            position: relative;
            top: 3px;

            .v-input__control .v-messages {
                display: none;
            }
        }

    }
}


.bullNum {
    position: relative;

    .SMStextPC{
        position: absolute;
        top: 47px;
        left: -80px;
        z-index: 1;
        display: flex;
        align-items: center;
        font-size: 12px;
        font-family: Calibri;
        color: #6f7070;
        // border: 1px solid #898989;
        // background-color: #fffcfc;
        // border-radius: 4px;
        // padding: 8px 7px;

        @include respond-to(mb) {
            display: none;
        }

        .icon{
            position: absolute;
            right: -7px;
            top: -7px;
            border-radius: 50%;
            border: 1px solid #959595;
            background: #fff;
            cursor: pointer;
            width: 20px;
            height: 20px;
            display: flex;
            justify-content: center;
            align-items: center;

            b{
                color:#959595;
            }
            @include respond-to(mb) {
                position: absolute;
                right: -4px;
                top: -3px;
            }
        }

        // &::before {
        // 	position: absolute;
        // 	top: -20px;
        // 	left: 155px;
        // 	content: "";
        // 	border-top: 10px transparent dashed;
        // 	border-left: 10px transparent dashed;
        // 	border-right: 10px transparent dashed;
        // 	border-bottom: 10px #757474 solid;
        // }

        // &::after {
        // 	position: absolute;
        // 	top: -19px;
        // 	left: 155px;
        // 	content: "";
        // 	border-top: 10px transparent dashed;
        // 	border-left: 10px transparent dashed;
        // 	border-right: 10px transparent dashed;
        // 	border-bottom: 10px #fffcfc solid;
        // }

        ::v-deep .v-input--checkbox {
            margin: 0;
            position: relative;
            top: 3px;

            .v-input__control .v-messages {
                display: none;
            }
        }

        // ::v-deep .v-icon.v-icon {
        // 	font-size: 35px;
        // 	@include respond-to(mb) {
        // 		font-size: 30px;
        // 	}
        // }
    }


    img {
        width: 13px;
        position: absolute;
        z-index: 1;
        bottom: 0;
        right: 0;

        @include respond-to(mb) {
            width: 11px;
        }
    }

    .bill {
        border-radius: 5px 0 0 5px;
        position: relative;

        ::v-deep .v-input__slot {
            padding: 0 5px !important;
        }

        ::v-deep .v-text-field__details {
            width: 50px;
            padding: 0 !important;
        }
    }

    .bill3_img {
        width: 13px;
        position: absolute;
        z-index: 1;
        left: 44px;
        top: 25px;
        @include respond-to(mb) {
            width: 11px;
            position: absolute;
            z-index: 1;
            left: 41px;
            top: 28px;
        }
    }

    .bill3 {
        border-radius: 5px 0 0 5px;
        position: relative;
        @include respond-to(mb) {
            ::v-deep .v-input__slot {
                padding: 0 7px;
            }
        }
    }

    .bill2 {
        border-radius: 0 5px 5px 0;
    }
}

.bullNum2 {
    .bill3 {
        ::v-deep .v-input__slot {
            padding: 0 6px !important;
        }
    }

    .bill2 {
        border-radius: 0 5px 5px 0;
        width: 55%;
    }
}

.border-bottom {
    border-bottom: 1px solid #e5e5e5;
}

.border-2 {
    height: 1px;
    border-top: 1px dashed #dbdbdb;
}

.checkoutPage {
    @include respond-to(mb) {
        .v-input {
            font-size: 12px;
        }
    }
}

.bps-container {
    overflow: hidden auto;
    display: grid;
    grid-template-columns: 5fr 7fr;
    grid-gap: 50px;
    align-items: flex-start;

    .hint {
        font-size: 12px;
        color: #999999;
    }

    ::v-deep {
        .v-radio .v-label {
            color: #333333;
            display: block;
        }

        .v-text-field.v-text-field--solo.v-input--dense > .v-input__control {
            min-height: 34px;
        }

        .v-text-field.v-text-field--solo:not(.v-text-field--solo-flat) > .v-input__control > .v-input__slot {
            box-shadow: 0px 2px 4px 0px rgba(204, 204, 204, 0.35);
            border: 1px solid #d0d5dd;
        }
    }

    .box-title {
        font-size: 18px;
        font-weight: 700;
    }

    .payBtnWrap {
        position: sticky;
        bottom: 0;
        left: 0;
        right: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 80px;
        padding: 10px 0;
        background-color: #f2f4f5;
        border-top: 1px solid #dbdbdb;
        z-index: 2;

        .label {
            margin-right: 10px;
            font-size: 24px;
        }

        .symbol {
            padding: 0 4px;
            font-size: 18px;
        }

        .unPaidPrice {
            font-size: 24px;
            color: #de3500;
        }
    }

    .left-box {
        .cusTitle {
            font-size: 24px;
			display: flex;
			align-items: center;
			font-weight: 700;
			::v-deep .selfCheckoutIcon.v-icon.v-icon{
				color:$color-primary;
				cursor: pointer;
				margin-left: 6px;
				&::after{
					content: '';
					position: absolute;
					left: 50%;
					top:auto;
					bottom:-100%;
					transform: translateX(-50%);
					width: 20px;
					height:100px;;
					background: transparent;
					z-index: 1;
				}
			}
        }
    }

    .right-box {
        display: flex;
        flex-direction: column;
        position: relative;
        max-height: 100%;
        overflow: hidden auto;
        padding: 15px 20px 0;
        background: #f2f4f5;

        .totalPrice {
            color: #de3500;
            font-size: 24px;
        }

        .delivery-box {
            display: none;
        }

        .coupoun-tip {
            img {
                width: 40px;
                margin-right: 10px;
            }

            color: #de3500;
            padding: 5px 0;
            font-size: 12px;
            display: flex;
            align-items: center;
        }
    }

    .pay-box {
        margin-bottom: 0;

        ::v-deep .v-label {
            padding-bottom: 10px;
        }

        .myFileset {
            border: 1px solid #d0d5dd;
            box-shadow: 0px 2px 4px 0px rgba(204, 204, 204, 0.35);
            border-radius: 6px;
            padding: 0 10px 10px;
            background-color: #ffffff;
            height: 54px;

            legend {
                font-size: 14px;
                padding: 0 2px;
                font-weight: 700;
                background: #ffffff;
                color: #333333;
                text-transform: uppercase;
            }

            img {
                height: 25px;
                object-fit: contain;
            }
        }

        .paymentBtn {
            flex: 0 0 33.3%;
            margin-right: 10px;
            margin-bottom: 10px;

            &:last-child {
                margin-right: 0;
            }
        }
    }

    .cart-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 10px 0;
        border-bottom: 1px solid #e5e5e5;

        .imgWrap {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 10px;
            height: 83px;
            background: #fafafa;
            border: 1px solid #e5e5e5;
            border-radius: 4px;

            img {
                object-fit: contain;
            }
        }

        .imgWrap2 {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 10px;
            height: 83px;
        }

        .item-p {
            width: 20%;
            text-align: center;

            img {
                width: 100%;
                height: 100%;
                border-radius: 5px;
            }

            &.t1 {
                .cartInfo {
                    display: none;
                }
            }

            &.t2 {
                width: 40%;
            }

            &.t3,
            &.t4,
            &.t5 {
                .t {
                    margin-bottom: 5px;
                }
            }
        }

        .c {
            text-align: center;
        }

        .originUnitPrice {
            font-size: 14px;
        }

        .discountUnitPrice {
            font-size: 18px;
            margin-left: 4px;
        }

        .discount {
            color: #de3500;
        }
    }

    .cart-item2 {
		border-bottom: 1px solid #e5e5e5;

		.giveawayClass{
			font-family: Google Sans;
			font-weight: 400;
			font-size: 14px;
			color: #FFFFFF;
			background: linear-gradient(90deg, #4A48FF 0%, #B012FB 100%);
			border-radius: 2px 18px 2px 18px;
			padding: 5px;
			width: 75px;
    		margin: 0 auto 10px;
		}

        @media screen and (max-width: $mb-width) {
            border-bottom: 1px solid #e5e5e5 !important;
            max-height: 65px;

			.giveawayClass{
				font-size: 10px;
				width: 67px;
				padding: 4px;
			}
        }
    }

    .cart-box {
        overflow: hidden auto;
        margin-bottom: 20px;
        max-height: 315px;
    }

    .cart-box,
    .delivery-box {
        border-bottom: none;
    }

    @media screen and (max-width: $mb-width) {
        height: auto !important;
        grid-template-columns: 1fr;
        grid-gap: 0;
        background-color: #e1e3e6;
        padding: 5px;
        .box-title {
            font-size: 14px;
        }
        .payBtnWrap {
            flex-wrap: wrap;
            height: auto;
            position: relative;
            padding: 10px;
            border: none;
            border-bottom-left-radius: 5px;
            border-bottom-right-radius: 5px;
            margin: 0 !important;
            font-size: 12px;
            background-color: #ffffff;

            &::before {
                content: "";
                position: absolute;
                top: 0;
                left: 10px;
                right: 10px;
                height: 1px;
                background-color: #e6e6e6;
            }

            .b1,
            .b2 {
                flex-basis: 100%;
                justify-content: center;
            }

            .label {
                margin-right: 5px;
                font-size: 14px;
            }

            .symbol {
                font-size: 12px;
            }

            .unPaidPrice {
                font-size: 16px;
            }

            .checkoutBtn {
                width: 100%;
                height: 35px;
                font-size: 12px;
                margin: 0 10px;
            }
        }
        .left-box {
            background: #ffffff;
            border-radius: 10px;
            padding: 10px;
            margin-bottom: 0;

            .cusTitle {
                font-size: 16px;
            }

            .yourPosition {
                display: none;
            }

            .delivery-box {
                display: none;
            }

            .cart-box {
                max-height: none;
            }

            .item-p.t5 {
                .c {
                    color: #de3500;
                }
            }
        }
        .right-box {
            overflow: hidden;
            padding: 0;
            background-color: transparent;
            max-height: none;

            ::v-deep .v-label {
                font-size: 12px;
            }

            .delivery-box {
                display: block;
            }

            .pay-box {
                margin-top: 0;
                margin-bottom: 5px;
            }

            .shipping-box {
                margin-bottom: 5px !important;
            }

            .billing-box {
                padding-bottom: 10px !important;
                margin-bottom: 0 !important;
            }

            .subtotal-con {
                padding: 10px;
                border: none;
                border-top-left-radius: 5px;
                border-top-right-radius: 5px;
                margin-top: 5px;
                font-size: 12px;
                background-color: #ffffff;
            }

            .box {
                padding: 10px;
                margin: 5px 0;
                border: none;
                border-radius: 5px;
                font-size: 12px;
                background-color: #ffffff;
            }

            .pay-box {
                .paymentBtn {
                    flex: 0 0 100%;
                    margin-right: 0;
                    margin-bottom: 10px;
                }
            }

            .subtotal-con .sub-item {
                font-size: 12px;
            }
        }
        .cart-item {
            font-size: 12px;

            &:last-child {
                border-bottom: none;
            }

            .item-p {
                &.t1 {
                    display: flex;

                    .imgWrap {
                        height: 52px;

                        img {
                            object-fit: contain;
                        }
                    }

                    .cartInfo {
                        display: block;
                        align-self: center;
                        margin-left: 10px;
                        font-size: 12px;
                        text-align: left;

                        .info1 {
                            font-size: 14px;
                            font-weight: 700;
                        }

                        .info2 {
                            margin: 8px 0;
                        }

                        .info3 {
                            display: flex;

                            div:first-child {
                                margin-right: 10px;
                            }
                        }
                    }
                }

                .discountUnitPrice {
                    font-size: 14px;
                }
            }
        }
        .cart-box {
            margin-bottom: 0;
        }
        .pay-box {
            .myFileset {
                padding: 0 5px 5px;

                legend {
                    font-weight: 400;
                    padding: 0;
                }

                img {
                    height: 15px;
                }
            }

            .paymentBtn {
                flex: 0 0 33.3%;
                margin-right: 10px;
                margin-bottom: 10px;

                &:last-child {
                    margin-right: 0;
                }
            }
        }
    }
}

.billing-box {
    border-bottom: 1px solid #dbdbdb;
}

.paymentBtn.active {
    border: 1px solid #1e88e5 !important;
}

/* Variables */
* {
    box-sizing: border-box;
}

.stripeForm {
    width: 100%;
    //width: 30vw;
    //min-width: 500px;
    align-self: center;
    box-shadow: 0px 0px 0px 0.5px rgba(50, 50, 93, 0.1), 0px 2px 5px 0px rgba(50, 50, 93, 0.1), 0px 1px 1.5px 0px rgba(0, 0, 0, 0.07);
    border-radius: 7px;
    padding: 40px;
}

.hidden {
    display: none;
}

#payment-form {
    box-shadow: none;
}

#payment-element {
    margin-bottom: 24px;
}

.btnWrap {
    display: flex;
    justify-content: center;
    align-items: center;
}

/* Buttons and links */
.stripeButton {
    background: #5469d4;
    font-family: Arial, sans-serif;
    color: #ffffff;
    border-radius: 4px;
    border: 0;
    padding: 12px 16px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    display: block;
    transition: all 0.2s ease;
    box-shadow: 0px 4px 5.5px 0px rgba(0, 0, 0, 0.07);
    width: 100%;
    margin: 4px;
}

.stripeButton:hover {
    filter: contrast(115%);
}

.stripeButton:disabled {
    opacity: 0.5;
    cursor: default;
}

.stripeButton.cancelBtn {
    background-color: #f5f5f5;
    color: #333333;
}

/* spinner/processing state, errors */
.spinner,
.spinner:before,
.spinner:after {
    border-radius: 50%;
}

.spinner {
    color: #ffffff;
    font-size: 22px;
    text-indent: -99999px;
    margin: 0px auto;
    position: relative;
    width: 20px;
    height: 20px;
    box-shadow: inset 0 0 0 2px;
    -webkit-transform: translateZ(0);
    -ms-transform: translateZ(0);
    transform: translateZ(0);
}

.spinner:before,
.spinner:after {
    position: absolute;
    content: "";
}

.spinner:before {
    width: 10.4px;
    height: 20.4px;
    background: #5469d4;
    border-radius: 20.4px 0 0 20.4px;
    top: -0.2px;
    left: -0.2px;
    -webkit-transform-origin: 10.4px 10.2px;
    transform-origin: 10.4px 10.2px;
    -webkit-animation: loading 2s infinite ease 1.5s;
    animation: loading 2s infinite ease 1.5s;
}

.spinner:after {
    width: 10.4px;
    height: 10.2px;
    background: #5469d4;
    border-radius: 0 10.2px 10.2px 0;
    top: -0.1px;
    left: 10.2px;
    -webkit-transform-origin: 0px 10.2px;
    transform-origin: 0px 10.2px;
    -webkit-animation: loading 2s infinite ease;
    animation: loading 2s infinite ease;
}

#payment-request-button {
    width: 200px;
    height: 50px;

    button {
        background-color: #635bff;
    }

    button {
        background: #635bff;
        border-radius: 6px;
        color: white;
        border: 0;
        padding: 12px 16px;
        margin-top: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s ease;
        display: block;
    }

    button:hover {
        filter: contrast(115%);
    }

    button:active {
        transform: translateY(0px) scale(0.98);
        filter: brightness(0.9);
    }

    button:disabled {
        opacity: 0.5;
        cursor: none;
    }
}

#messages {
    display: none;
    //background-color: #0A253C;
    color: #00d924;
    padding: 0px;
    margin: 0px;
    font-size: 1em;
    width: 300px;
}

@-webkit-keyframes loading {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

@keyframes loading {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

.xin {
    color: #de3500;
}

.subtotal-con {
    .radio-group-div {
        background: #fff;
        padding: 10px;
        border: 1px solid #d0d5dd;
        border-radius: 4px;
        margin-top: 10px;

        ::v-deep .v-input--selection-controls {
            margin-top: 0;
        }

        @media screen and (max-width: $mb-width) {
            background: #f2f4f5;
            border-color: #d0d5dd;

            .v-radio {
                ::v-deep .v-label {
                    display: block;
                }
            }
        }
    }

    .sub-item {
        display: flex;
        align-items: center;
        margin-top: 5px;
        font-size: 16px;

        .label {
            flex-basis: 75%;
            text-align: left;
        }

        .con {
            flex: 1;
            text-align: right;
            color: #de3500;
        }
    }

    .discount-item {
        height: 45px;

        .v-radio {
            flex: 0 0 170px;
            margin-bottom: 0 !important;
            margin-right: 10px;
        }

        .discount {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex: 1;

            & > div:first-child {
                flex: 0 0 270px;
            }
        }

        .con {
            color: #de3500;
            width: 100%;
            text-align: right;
        }
    }

    .discount-item.dis2 {
        height: 30px;
    }

    .sub-item.subtotalPrice .con {
        color: #333333;
    }

    @media screen and (max-width: $mb-width) {
        .discount-item {
            &:first-child {
                button {
                    font-size: 12px;
                    min-width: 37px;
                    padding: 10px;
                }
            }

            .v-radio {
                flex: 0 0 140px;
            }

            .discount {
                & > div:first-child {
                    flex: 0 1 165px;
                }

                .v-input {
                    font-size: 12px;
                }

                .con {
                    text-align: right;
                    flex: 0 0 50px;
                    font-size: 12px;

                    .couponicon {
                        font-size: 25px;
                    }
                }
            }
        }
    }
}

.card_field {
    width: 100%;
    padding: 12px;
    border: 1px solid #00000061;
    border-radius: 6px;
    box-sizing: border-box;
    resize: vertical;
    height: 40px;
    background: white;
    font-size: 16px;
    color: #333333;
}

.mask {
    background: $bg-mask;
    position: fixed;
    z-index: 16777270;
    bottom: 0;
    right: 0;
    left: 0;
    top: 0;

    .menu-box {
        width: 380px;
        min-height: 100vh;
        padding-top: 550px;
        background: white;
        flex-direction: column;

        button {
            width: 80%;
            font-size: 26px;
            margin-top: 15px;
            line-height: 46px;
            font-weight: bold;
        }

        .login {
            color: white;
            background: $color-dark;
        }

        .register {
            color: $color-dark;
            text-decoration: underline;
        }
    }
}

@media screen and (max-width: $mb-width) {
    .btnWrap {
        flex-wrap: wrap;
    }
}

::v-deep .v-application--wrap {
    min-height: auto;
}
</style>
