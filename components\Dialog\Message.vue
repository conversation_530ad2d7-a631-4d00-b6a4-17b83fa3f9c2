<template>
	<v-dialog :value="messageDialog"
			  @input="$emit('update:messageDialog',false)" width="768">
		<v-card class="messageDialogCard" color="#ffffff">
			<v-card-title class="justify-center">Message</v-card-title>
			<div class="px-6">
				<div v-for="item in messageList">
					<div class="user mb-3 d-flex align-center justify-space-between">
						<div>
							<v-avatar color="indigo" size="36">
								<template v-if="item.userInfo&&item.userInfo.picPath">
									<v-img :src="item.picPath"></v-img>
								</template>
								<template v-else>
									<v-icon dark>
										mdi-account-circle
									</v-icon>
								</template>
							</v-avatar>
							<template v-if="item.userInfo">
								<span class="ml-2">{{ item.userInfo.firstName }} {{ item.userInfo.lastName }}</span>
							</template>
							<template v-else>
								<span class="ml-2">xxx</span>
							</template>
						</div>
						<div class="secondary--text">
							{{ item.dateMsg }}
						</div>
					</div>
					<div class="content">
						<v-textarea
							readonly
							rows="3"
							outlined
							:value="item.message"
						></v-textarea>
					</div>
				</div>
			</div>
		</v-card>
	</v-dialog>
</template>

<script>
export default {
	props:['messageList','messageDialog'],
	data() {
		return {
		}
	}
}
</script>

<style scoped>

</style>
