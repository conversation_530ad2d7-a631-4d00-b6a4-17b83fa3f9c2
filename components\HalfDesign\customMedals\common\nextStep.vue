<template>
  <div class="nextStep myBtn">
    <slot>{{ lang.next }}</slot>
  </div>
</template>

<script>
export default {
  name: 'nextStep',
  components: {},
  data() {
    return {

    }
  },
  watch: {},
  computed: {
    lang() {
      return this.$store.getters.lang.quote || {};
    },
  },
  methods: {},
  created() { },
  mounted() { },
}
</script>

<style scoped lang="scss">
.nextStep {
  background: $color-primary;
  border-radius: 6px;
  font-size: 18px;
  font-weight: 400;
  color: #ffffff;
  padding: 0.4em 2em;
  cursor: pointer;

  @include respond-to(mb) {
    background: $color-primary;
    border-radius: 5px;
    font-size: 12px;
    font-weight: 400;
    color: #ffffff;
    padding: 0.75em 3em;;
  }
}
</style>
