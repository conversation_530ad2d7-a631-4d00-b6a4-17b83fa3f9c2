<template>
	<div class="mb-4" :class="stepData.styleName">
		<div class="step-content">
			<div class="step-text grey--text">
				{{ stepData.attributeName | capitalize }}
			</div>
			<div class="step-wrap">
				<div class="step-item" :class="{ active: index === selectIndex }" v-for="(step, index) in stepData.productParamList" :key="index" @click="selectStep(step, index)" v-show="step.stock != '-1'">
					<!-- <b class="icon-a-icon-checkzhuanhuan yeah"></b> -->
					<b class="icon-xuanzhong absolute-topOut-right yeah"></b>
					<div class="name">
						<div class="text-truncate">
							{{ step.valueName }}
						</div>
						<div class="tip">
							<CCYRate :price="JSON.parse(step.increasePrice)[0].unitPrice"></CCYRate>
						</div>
					</div>
				</div>
			</div>
		</div>

		<div class="errorTip">
			<v-alert dense outlined type="error">
				{{ langSemiCustom.errorTip }}
			</v-alert>
		</div>

		<div class="step-content">
			<div class="step-text grey--text">
				{{ langSemiCustom.quantity }}
			</div>
			<div class="sizeNum">
				<div class="inputBox">
					<button class="inputBtn" @click="subNum"><b class="icon-jianshao"></b></button>
					<input class="priceInput" :placeholder="getPlaceholder()" type="text" v-model="inputNum" @keyup="formatNum" @change="updatePrice" />
					<button class="inputBtn" @click="addNum"><b class="icon-jxsht-3d-tj"></b></button>
				</div>
			</div>
		</div>
		<div class="errorTip2" style="display: none; margin: 10px 0">
			<v-alert dense outlined type="error"> {{ langSemiCustom.miniQty }}{{ productInfo.lowestPurchaseQuantity || 1 }} </v-alert>
		</div>
	</div>
</template>
<script>
export default {
	inject: ["getProductInfo"],
	props: {
		stepData: {
			type: Object,
			default: {},
		},
	},
	data() {
		return {
			selectIndex: -1,
			selectItem: null,
			inputNum: "",
			stock: 999,
			teaSize: {},
			theme:''
		};
	},
	computed: {
		langSemiCustom() {
			return this.$store.getters.lang?.semiCustom;
		},
		productInfo() {
			return this.getProductInfo();
		},
	},
	methods: {
		selectStep(item, index) {
			this.selectIndex = index;
			this.selectItem = item;
			this.$emit("selectStep", {
				type: this.stepData.attributeFlag,
				data: item,
				parent: this.stepData,
				id: this.stepData.id,
			});
		},
		formatNum() {
			// if (!step.inputNum) {
			// 	this.isInput = false;
			// 	return undefined;
			// }
			this.inputNum = (this.inputNum + "").replace(/[^\d]/g, "");
			if (this.stock && this.stock > 0 && this.inputNum > this.stock) {
				this.inputNum = String(this.stock);
			}
			if (this.stock <= 0) {
				this.inputNum = "";
				setTimeout(()=>{
					this.$Bus.$emit('noStock')
				},1000)
				return
			}
			if (this.inputNum > 0) {
				// this.updatePrice();
				setTimeout(() => {
					this.updatePrice();
				}, 310);
			}
		},
		getPlaceholder() {
			if (this.stepData.styleName === "style1") {
				if (this.stock > 0) {
					if(this.productInfo.isDevise&&this.theme=='11'){
						return `${this.stock}件库存`;
					}
					return `${this.stock} in stock`;
				}
				if (this.stock == -1) {
					return;
				} else {
					if(this.productInfo.isDevise&&this.theme=='11'){
						return '已售罄';
					}
					return "Sold Out";
				}
			}
		},
		updatePrice() {
			let priceInputs = document.getElementsByClassName("priceInput");
			// let sum = Array.from(priceInputs).reduce((acc, input) => acc + parseInt(input.value || 0), 0);
			let sum = priceInputs[0].value;
			let errDom2 = document.getElementById(this.stepData.id).getElementsByClassName("errorTip2")[0];
			let errDom = document.getElementById(this.stepData.id).getElementsByClassName("errorTip")[0];
			if ( sum < this.productInfo.lowestPurchaseQuantity || sum == "0") {
				errDom2.style.display = "block";
			} else {
				errDom2.style.display = "none";
				if (this.selectIndex > 0) errDom.style.display = "none";
			}
			if(sum<=0){
				this.$Bus.$emit('noStock')
				return
			}

			if(!this.inputNum||this.stock<=0||this.inputNum<=0){
				return 
			}

			let teaData = {
				quantity: this.inputNum,
				paramType: "size",
				paramId: this.teaSize.id,
			};
			teaData = teaData.paramId ? teaData : null;
			this.$emit("updatePrice", teaData);
		},
		subNum() {
			if (this.inputNum <= 0 || !this.inputNum) {
				this.inputNum = 0;
				return;
			}
			this.inputNum--;
			this.$nextTick(() => {
				this.updatePrice();
			});
		},
		addNum() {
			if (!this.inputNum) this.inputNum = 0;
			if (this.inputNum >= this.stock) return;
			this.inputNum++;
			this.$nextTick(() => {
				this.updatePrice();
			});
		},
		uploadQty(item) {
			this.teaSize = item;
			this.stock = item.stock;
			this.formatNum();
		},
		//切换茶叶产品，更新库存数量
		updateStock() {
			if (this.selectIndex < 0) return;
			let item = this.stepData.productParamList[this.selectIndex];
			this.uploadQty(item);
		},
	},
	mounted() {
		this.$Bus.$on("uploadQty", this.uploadQty);
		this.$Bus.$on("updateQty", this.updateStock);
        try {
            const element = document.querySelector("#modalHeader");
            this.theme = element.getAttribute("theme");
        } catch (e) {
        }
	},
	beforeDestroy() {
		this.$Bus.$off("uploadQty");
		this.$Bus.$off("updateQty");
	},
};
</script>
<style scoped lang="scss">
@import "~assets/css/half.scss";
[theme="10"] {
	.quantityBox {
		display: none !important;
	}
}

.style1 .step-content {
	display: grid;
	grid-template-columns: 100px 1fr;
	grid-gap: 10px;
	margin-bottom: 10px;
	.sizeNum {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		grid-gap: 10px;
		@include respond-to(mb) {
			grid-gap: 6px;
			grid-template-columns: repeat(2, 1fr);
			.inputBox {
				height: 45px !important;
			}
		}
		.inputBox {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 100%;
			height: 50px;
			overflow: hidden;
			border-radius: 10px;
			border: 1px solid $bg-dark;
			.inputBtn {
				width: 23%;
				font-size: 14px;
				font-weight: 300;
				height: 100%;
				background-color: $bg-primary;
				color: $bg-dark;
			}
			input {
				height: 60%;
				width: 55%;
				line-height: 80%;
				flex-shrink: 0;
				margin: 0 3%;
				// border: 1px solid #e2e2e2;
				border-radius: 6px;
				padding: 4px;
				font-size: 14px;
				text-align: center;
			}

			input::placeholder {
				font-size: 14px;
			}
		}
	}
	.step-wrap {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		grid-gap: 20px;

		.step-item {
			position: relative;
			min-width: 0;
			border: 1px solid $bg-dark;
			padding: 5px;
			@include radius-response;
			cursor: pointer;
			display: flex;
			justify-content: center;
			font-size: 18px;
			font-weight: bold;
			color: #333333;
			.tip {
				font-size: 14px;
				font-weight: normal;
				color: #333333;
			}

			.yeah {
				opacity: 0;
				font-size: 24px;
				z-index: 2;
				background-color: #ffffff;
				clip-path: circle(40% at 50% 50%);
				@include respond-to(mb) {
					font-size: 22px;
				}
			}

			@media (any-hover: hover) {
				&:hover {
					border-color: $color-primary;
					color: $color-primary;
					.tip {
						color: $color-primary;
					}

					::v-deep .check-box {
						border-color: #ffffff;

						.check-box-inner {
							background-color: #ffffff;
						}
					}
				}
			}

			.name {
				word-break: break-word;

				& > div:first-child {
					margin-bottom: 5px;
					line-height: normal;
					text-align: center;
				}
			}
		}

		.step-item.active {
			border-color: $color-primary !important;
			color: $color-primary;
			.tip {
				color: $color-primary;
			}
			.yeah {
				opacity: 1;
			}
			::v-deep .check-box {
				border-color: #ffffff;

				.check-box-inner {
					background-color: #ffffff;
				}
			}
		}
	}

	@include respond-to(mb) {
		grid-template-columns: 1fr;
		grid-gap: 5px;

		.step-wrap {
			display: grid;
			grid-template-columns: repeat(4, 1fr);
			grid-gap: 12px;

			.step-item {
				.name > div:first-child {
					margin-bottom: 5px;
					font-size: 14px;
					text-align: center;
				}

				.tip {
					font-size: 12px;
				}
			}
		}
	}
}

[theme="11"] {
	.grey--text {
		color: #333 !important;
	}
	.quantityBox {
		display: none !important;
	}

	.step-content {
		@include respond-to(mb) {
			grid-gap: 10px;
			grid-template-columns: 40px 1fr;
			align-items: center;
			.step-text {
				position: relative;
				&::after {
					content: ":";
					position: absolute;
					right: 0;
					top: 42%;
					transform: translateY(-50%);
				}
			}
		}
	}

	.step-wrap {
		@include respond-to(mb) {
			grid-gap: 10px !important;
		}
	}
	.step-item {
		border: 1px solid #dcdcdc !important;
		@media (any-hover: hover) {
			&:hover {
				color: $color-primary;
				border-color: $color-primary !important;
			}
		}
	}
	.step-content .sizeNum .inputBox {
		border: 1px solid $color-primary !important;
		@include respond-to(mb){
			height:40px !important;
		}
		.inputBtn {
			background: $color-dark !important;
			color: #fff !important;
		}
		input {
			padding: 0 !important;
		}
	}
}
</style>
