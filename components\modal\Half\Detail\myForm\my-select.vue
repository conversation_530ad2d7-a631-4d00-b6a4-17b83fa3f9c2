<template>
  <div class="mySelect" :class="{ 'is-focus': showDropdown, 'error--text': notValidate }">
    <div class="select-input" @click.stop="toggleDropdown">
      <input :ref="oneProp" type="text" :class="{placeholderStyle:selectedLabel&&!showDropdown}" :placeholder="currentPlaceholder" @input="handleQueryChange" @focus="handleFocus" @blur="handleBlur" @keydown.up.prevent="navigateOptions('prev')" @keydown.down.prevent="navigateOptions('next')" @keydown.enter.prevent="selectOption(filteredOptions[hoverIndex])" @keydown.esc.prevent="closeDropdown" :readonly="!filterable" :data-required="requireIcon" />
      <span class="select-arrow" :class="{ 'is-reverse': showDropdown }"></span>
    </div>
    <transition name="fade">
      <div class="select-dropdown" :class="{noErrorText:noErrorText}" v-show="showDropdown">
        <ul class="select-dropdown__list" ref="dropdown">
          <li v-for="(option, index) in filteredOptions" :key="index" class="select-dropdown__item" :class="{ 'is-selected': option.value === value, 'hover': index === hoverIndex }" @click.stop="selectOption(option)" @mouseenter="hoverIndex = index">
            <span>{{ option.label }}</span>
          </li>
          <li v-if="loading" class="select-dropdown__loading">
            Loading...
          </li>
          <li v-else-if="filteredOptions.length === 0" class="select-dropdown__empty">
            No Data
          </li>
        </ul>
      </div>
    </transition>
    <div class="textField" v-show="!noErrorText||notValidate">
      <div class="vaildate-message" v-show="notValidate">{{ vaildateText }}</div>
    </div>
  </div>
</template>

<script>
import { getRandomString } from '@/utils/utils'
export default {
  name: 'mySelect',
  props: {
    value: {
      type: [String, Number, Object],
      default: ''
    },
    options: {
      type: Array,
      required: true
    },
    placeholder: {
      type: String,
      default: 'please select'
    },
    noErrorText: {
      type: Boolean,
      default: false
    },
    filterable: {
      type: Boolean,
      default: false
    },
    filterMethod: {
      type: Function,
      default: null
    },
    rules: {
      type: Array,
      default: () => []
    },
    requireIcon: {
      type: String,
      default: ''
    },
    remote: {
      type: Boolean,
      default: false
    },
    remoteMethod: {
      type: Function,
      default: null
    },
    loading: {
      type: Boolean,
      default: false
    },
    debounce: {
      type: Number,
      default: 300
    }
  },
  data() {
    return {
      showDropdown: false,
      query: '',
      selectedLabel: '',
      notValidate: false,
      vaildateText: '',
      vaildateStatus: true,
      needVaildate: false,
      noNextValidate: false,
      hoverIndex: -1,
      isFocused: false,
      previousQuery: '',
      timeout: null,
      isComposing: false,
      selectedIndex: -1
    }
  },
  computed: {
    oneProp() {
      return this.prop + getRandomString(3)
    },
    currentPlaceholder() {
      if (this.isFocused && this.filterable) {
        return this.query || this.selectedLabel || this.placeholder
      }
      return this.selectedLabel || this.placeholder
    },
    filteredOptions() {
      const query = this.query.trim().toLowerCase()
      if (!this.filterable || !query) {
        return this.options
      }
      if (this.filterMethod) {
        return this.filterMethod(query, this.options)
      }
      return this.options.filter(option => {
        const label = option.label.toString().toLowerCase()
        return label.includes(query)
      })
    }
  },
  watch: {
    value: {
      immediate: true,
      handler(val) {
        this.setSelected()
      }
    },
    options: {
      immediate: true,
      handler() {
        this.$nextTick(this.setSelected)
      }
    }
  },
  methods: {
    handleQueryChange(e) {
      if (this.isComposing) return
      const value = e.target.value
      this.query = value
      if (this.remote && this.remoteMethod) {
        this.handleRemoteSearch()
      } else if (this.filterable) {
        this.showDropdown = true
        this.$emit('search', value)
      }
    },
    handleFocus(event) {
      this.isFocused = true
      if (this.filterable) {
        this.query = ''
        this.$nextTick(() => {
          event.target.select()
        })
      }
    },
    handleBlur() {
      this.isFocused = false
    },
    setSelected() {
      this.selectedLabel = ''
      this.options.forEach((opt, index) => {
        if (JSON.stringify(opt.value) === JSON.stringify(this.value)) {
          this.selectedLabel = opt.label
          this.selectedIndex = index
        }
      })
    },
    selectOption(option) {
      let data = option.value
      this.$emit('input', data)
      this.selectedLabel = option.label
      this.$nextTick(async () => {
        let status = await this.validate()
        if (!status) {
          data = null
          this.$emit('input', data)
        }
        this.$emit('change', { value: data, label: data ? option.label : '', index: this.selectedIndex })
        this.query = ''
        this.inputDom.value = ''
        this.inputDom.blur()
        this.showDropdown = false
      })
    },
    toggleDropdown() {
      if (this.filterable && !this.showDropdown) {
        this.$refs[this.oneProp].focus()
      }
      this.showDropdown = !this.showDropdown
      if (this.showDropdown) {
        document.addEventListener('click', this.closeDropdown)
      } else {
        document.removeEventListener('click', this.closeDropdown)
      }
      setTimeout(() => {
        this.query = ''
        this.inputDom.value = ''
      }, 100)
    },
    closeDropdown(event) {
      if (!this.$el.contains(event.target)) {
        this.showDropdown = false
        this.isFocused = false
        this.hoverIndex = -1
        document.removeEventListener('click', this.closeDropdown)
        setTimeout(() => {
          this.query = ''
          this.inputDom.value = ''
        }, 100)
      }
    },
    reset() {
      this.notValidate = false
      this.vaildateStatus = true
      this.$emit('input', '')
      this.query = ''
      this.inputDom.value = ''
      this.selectedLabel = ''
    },
    resetValue() {
      this.notValidate = false
    },
    checkFun(error) {
      if (this.noNextValidate) return
      if (error) {
        const errorMessage = error instanceof Error ? error.message : error
        this.notValidate = true
        this.vaildateText = errorMessage
        this.vaildateStatus = false
      } else {
        this.notValidate = false
        this.vaildateStatus = true
      }
    },
    validate() {
      if (!this.needVaildate) return Promise.resolve(true)
      return new Promise(resolve => {
        if (this.rules.length > 0) {
          this.vaildateStatus = true
          this.notValidate = false
          for (let i = 0; i < this.rules.length; i++) {
            let rule = this.rules[i]
            if (rule.required && !this.value) {
              this.notValidate = true
              this.vaildateText = rule.message
              this.vaildateStatus = false
              break
            }
            if (rule.validator) {
              if (typeof rule.validator === 'function') {
                rule.validator({ ...rule }, this.value, this.checkFun)
                if (!this.vaildateStatus) break
              }
            }
          }
        }
        resolve(this.vaildateStatus)
      })
    },
    handleRemoteSearch() {
      if (this.timeout) clearTimeout(this.timeout)
      if (this.query !== this.previousQuery) {
        this.timeout = setTimeout(() => {
          this.previousQuery = this.query
          this.$emit('search', this.query)
          if (this.remoteMethod) {
            this.remoteMethod(this.query)
          }
        }, this.debounce)
      }
    },
    navigateOptions(direction) {
      if (!this.showDropdown) {
        this.showDropdown = true
        return
      }

      const optionsLength = this.filteredOptions.length
      if (optionsLength === 0) return

      if (direction === 'prev') {
        if (this.hoverIndex <= 0) {
          this.hoverIndex = optionsLength - 1
        } else {
          this.hoverIndex--
        }
      } else if (direction === 'next') {
        if (this.hoverIndex >= optionsLength - 1) {
          this.hoverIndex = 0
        } else {
          this.hoverIndex++
        }
      }

      this.$nextTick(() => {
        const dropdown = this.$refs.dropdown
        const items = dropdown.getElementsByClassName('select-dropdown__item')
        const target = items[this.hoverIndex]

        if (target) {
          const dropdownHeight = dropdown.offsetHeight
          const itemHeight = target.offsetHeight

          const scrollTop = dropdown.scrollTop
          const offsetTop = target.offsetTop

          if (offsetTop + itemHeight > scrollTop + dropdownHeight) {
            dropdown.scrollTop = offsetTop - dropdownHeight + itemHeight
          } else if (offsetTop < scrollTop) {
            dropdown.scrollTop = offsetTop
          }
        }
      })
    }
  },
  created() {},
  mounted() {
    this.inputDom = this.$refs[this.oneProp]
    if (this.rules.length > 0) {
      this.rules.forEach(rule => {
        if (rule.required) this.needVaildate = true
        if (rule.trigger && rule.message) {
          this.inputDom.addEventListener(rule.trigger, () => {
            this.notValidate = false
            this.noNextValidate = false
            if (!this.inputDom.value || !this.inputDom.value.trim()) {
              this.notValidate = true
              this.noNextValidate = true
              this.vaildateText = rule.message
            }
          })
        }
        if (rule.trigger && rule.validator) {
          if (typeof rule.validator === 'function') {
            this.inputDom.addEventListener(rule.trigger, () => {
              rule.validator({ ...rule }, this.inputDom.value, this.checkFun)
            })
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.mySelect {
  position: relative;
  width: 100%;
  margin-top: 4px;
}

.select-input {
  position: relative;
  width: 100%;
}

.select-input input {
  width: 100%;
  padding: 7px 10px;
  border-radius: 4px;
  background-color: #f5f5f5;
  border: 1px solid transparent;
  font-size: 16px;
  color: #333;
  transition: border-color 0.2s;
  cursor: pointer;
  position: relative;
  &::placeholder {
    color: #c0c4cc;
  }
  &.placeholderStyle {
    &::placeholder {
      font-size: 16px;
      color: #333;
    }
  }
  @include respond-to(mb) {
    font-size: 14px;
    &::placeholder {
      font-size: 14px;
    }
  }
}

.select-input input:focus {
  outline: none;
}

.mySelect.is-focus .select-input input {
  border: 1px solid $color-primary;
}

.mySelect.error--text {
  input {
    border: 1px solid #ff5252 !important;
  }
  .vaildate-message {
    color: #ff5252 !important;
    caret-color: #ff5252 !important;
  }
}

.select-arrow {
  position: absolute;
  top: 40%;
  right: 16px;
  width: 8px;
  height: 8px;
  border: 1px solid #333;
  border-top-color: transparent;
  border-left-color: transparent;
  background-color: transparent;
  transform: rotate(45deg) translateY(-50%);
  transition: transform 0.2s;
}

.select-arrow.is-reverse {
  top: 60%;
  transform: translateY(-50%) rotate(225deg);
}

.select-dropdown {
  position: absolute;
  top: calc(100% - 9px);
  left: 0;
  width: 100%;
  max-height: 200px;
  background-color: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  z-index: 1000;
  &.noErrorText {
    top: calc(100% + 5px);
  }
}

.select-dropdown__list {
  margin: 0;
  padding: 0;
  list-style: none;
  overflow-y: auto;
  max-height: 200px;
}

.select-dropdown__item {
  padding: 8px 12px;
  color: #606266;
  cursor: pointer;
}

.select-dropdown__item:hover {
  background-color: #f5f7fa;
}

.select-dropdown__item.is-selected {
  color: $color-primary;
  font-weight: bold;
  background-color: #f5f7fa;
}

.select-dropdown__empty {
  padding: 8px 12px;
  color: #909399;
  text-align: center;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s;
}

.fade-enter,
.fade-leave-to {
  opacity: 0;
}

.textField {
  pointer-events: none;
  margin-top: 2px;
  display: flex;
  flex: 1 0 auto;
  max-width: 100%;
  min-height: 14px;
  overflow: hidden;
  line-height: 12px;
  font-size: 12px;
  word-break: break-word;
  word-wrap: break-word;
  hyphens: auto;
}

.vaildate-message {
  color: #ff5252;
  font-size: 12px;
  line-height: 12px;
}

.select-dropdown__item.hover {
  background-color: #f5f7fa;
}

.select-dropdown__item span {
  display: inline-block;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.select-dropdown__loading {
  padding: 8px 12px;
  color: #909399;
  text-align: center;
}

/* 添加滚动条样式 */
.select-dropdown__list::-webkit-scrollbar {
  width: 6px;
}

.select-dropdown__list::-webkit-scrollbar-thumb {
  border-radius: 3px;
  background: #c0c4cc;
}

.select-dropdown__list::-webkit-scrollbar-track {
  border-radius: 3px;
  background: #f5f7fa;
}
</style>
