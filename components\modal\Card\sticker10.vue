<template>
	<div class="summary-box" :class="modal.wrapClass" :style="modal.style">
		<div class="bps-container" v-for="(o, oi) in modal.outer" :key="oi">
			<EditDiv tag-name="h2" v-model:content="modal.outer[0].title.value" @click="setModalType(o.title,modal.outer,'text')" />
			<div class="conter">
				<div v-for="(item,index) in modal.outer[0].list" :key="index">
					<div>
						<pic class="pic" :src="item.img.value" :title="item.img.alt" :alt="item.img.alt" @click="setModalType(item.img,modal.outer,'img')"/>
						<div>
							<EditDiv tag-name="p" v-model:content="item.title.value" @click="setModalType(item.title,modal.outer,'text')" />
							<EditDiv tag-name="span" v-model:content="item.subTitle.value" @click="setModalType(item.subTitle,modal.outer,'text')" />
						</div>
						<div class="btnWrap" @click="setModalType(item.button,modal.outer,'button')">
							<a :href="item.button.url" :title="item.button.alt" :target="item.button.target || '_self'"
								 class="default-button bps-button">
								{{ item.button.value }}
							</a>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	props: {
		data: {
			type: Object,
			default: {},
		}
	},
	data() {
		return {
			modal: {
				style: {},
				type: {index: 0, clickPosition: 'outer'},
				...this.data
			}
		};
	},
	watch: {
		modal: {
			handler(val) {
				if (process.env.isManage) this.$emit("update:data", val);
			},
			immediate: true,
			deep: true
		}
	},
	methods:{
		setModalType(target, targetArray, clickType, event, other) {
			this.$setModal(this, target, targetArray, clickType, event, other)
		},
	},
	mounted() {

	}
};
</script>

<style lang="scss" scoped>
.summary-box {
	padding: 120px 0;
	.bps-container {
		h2 {
			text-align: center;
			font-size: 42px;
		}
		.conter {
			display: grid;
			grid-template-columns: repeat(4,1fr);
			grid-column-gap: 20px;
			margin-top: 30px;
			div {
				div {
					div {
						text-align: center;
						p {
							text-align: center;
							font-size: 18px;
							font-weight: bold;
							margin: 10px 0;
						}
						span {
							font-size: 14px;
						}
					}
				}
				.btnWrap {
					width: 30%;
					margin: 10px auto;
				}
			}
		}
	}
}
@media screen and (max-width: $mb-width) {
	.summary-box {
		padding: 70px 0;
		.bps-container {
			h2 {
				font-size: 21px;
			}
			.conter {
				grid-template-columns: repeat(2,1fr);
				grid-column-gap: 15px;
				div {
					div {
						div {
							p {
								font-size: 14px;
								margin: 5px 0;
							}
							span {
								font-size: 12px;
							}
						}
					}
					.btnWrap {
						width: 50%;
						margin: 10px auto;
					}
				}
			}
		}
	}
}
</style>
