<template>
	<div class="mb-4" :class="stepData.styleName">
	    <slot name="stepText"></slot>
		<div class="step-content">
			<div class="step-item" :class="{ active: index === selectIndex }" v-for="(step, index) in stepData.productParamList" :key="index" @click="selectStep(step, index)">
				<half-design-check-box class="mr-2"></half-design-check-box>
				<div class="step-item-text" style="margin-right: 5px">
					<div class="text-truncate step-item-title">
						{{ step.valueName }}
					</div>
					<div class="step-item-des">
						{{ step.remark }}
					</div>
				</div>
				<v-tooltip bottom class="helpIcon">
					<template v-slot:activator="{ on, attrs }">
						<v-icon v-bind="attrs" v-on="on" size="18px"> mdi-help-circle-outline </v-icon>
					</template>
					<div class="text-center" style="display: flex; align-items: start">
						<div v-if="step.imgDetail" style="max-width: 150px; max-height: 150px; margin-right: 10px">
							<img :src="step.imgDetail" :alt="step.valueName" :title="step.valueName" style="object-fit: contain" />
						</div>
						<div style="text-align: left; color: #fff; line-height: 22px; font-size: 13px; width: 250px; word-break: break-word; white-space: pre-line">{{ step.remark }}</div>
					</div>
				</v-tooltip>
			</div>
		</div>
		<div class="errorTip">
			<v-alert dense outlined type="error">
				{{ langSemiCustom.errorTip }}
			</v-alert>
		</div>
	</div>
</template>
<script>
export default {
	props: {
		stepData: {
			type: Object,
			default: {},
		},
	},
	data() {
		return {
			selectIndex: -1,
			selectItem: null,
		};
	},
	computed: {
		langSemiCustom() {
			return this.$store.getters.lang?.semiCustom;
		},
	},
	methods: {
		selectStep(item, index,state=false) {
			this.selectIndex = index;
			this.selectItem = item;
			this.$emit("selectStep", {
				type: this.stepData.attributeFlag,
				data: item,
				parent: this.stepData,
				id: this.stepData.id,
				firstSelect:state
			});
		},
		selectDefault() {
			if (this.selectIndex <= -1) {
				let findIndex = this.stepData.productParamList.findIndex((item) => {
					return item.isBlank === 0;
				});
				if (findIndex > -1) {
					this.selectStep(this.stepData.productParamList[findIndex], findIndex,true);
				}
			}
		},
	},
	mounted() {
		this.$Bus.$on("selectDefaultEmailStep", this.selectDefault);
	},
	beforeDestroy() {
		this.$Bus.$off("selectDefaultEmailStep",this.selectDefault);
	},
};
</script>
<style scoped lang="scss">
@import "~assets/css/half.scss";

.step-content {
	display: grid;
	grid-template-columns: repeat(1, 1fr);
	grid-gap: 10px;

	.step-item {
		min-width: 0;
		display: flex;
		align-items: center;
		@include step-default;
		background-color: $background-color;
		cursor: pointer;

		.step-item-title {
			font-weight: 700;
		}

		@media (any-hover: hover) {
			&:hover {
				background-color: var(--color-second);
				color: inherit;

				::v-deep .check-box {
					border-color: $color-primary;

					.check-box-inner {
						background-color: $color-primary;
					}
				}
			}
		}

		&.active {
			background-color: var(--color-second);
			color: inherit;

			::v-deep .check-box {
				border-color: $color-primary;

				.check-box-inner {
					background-color: $color-primary;
				}
			}
		}
	}
}

.style2 .step-content {
	grid-template-columns: repeat(2, 1fr);

	.step-item-des {
		display: none;
	}

	.step-item:hover {
		background-color: $color-primary;
		color: #ffffff;
		::v-deep .mdi-help-circle-outline::before {
			color: #ffffff;
		}
		::v-deep .check-box {
			background-color: #ffffff;
		}
	}

	.step-item.active {
		background-color: $color-primary;
		color: #ffffff;
		::v-deep .mdi-help-circle-outline::before {
			color: #ffffff;
		}
		::v-deep .check-box {
			background-color: #ffffff;
		}
	}

	@include respond-to(mb) {
		grid-template-columns: repeat(1, 1fr);

		.step-item {
			justify-content: center;
		}
	}
}
</style>
