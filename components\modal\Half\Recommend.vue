<template>
	<div :style="modal.style">
		<template v-if="isManage">
			<v-card height="300">
				<v-row justify="center">
					<v-overlay :absolute="true" :value="true">
						<v-chip> 半定制推荐产品模板</v-chip>
					</v-overlay>
				</v-row>
			</v-card>
		</template>
		<template v-else>
			<div class="modal-box pdt-recommend" v-show="recommendProduct.length && $store.state.proTheme != 11">
				<h2 v-if="recommendProduct.length">{{ langSemiCustom.mayLike }}</h2>
				<div class="see-more" v-if="isMobile" @click="toCate">
					<span :title="langSemiCustom.see2">{{ langSemiCustom.see2 }}</span>
					<v-icon>mdi-arrow-right-thick</v-icon>
				</div>
				<div class="pdt-recommend-list">
					<div class="myswiper3Wrap">
						<div class="swiper-button-prev"></div>
						<div class="swiper myswiper3" ref="swiper3">
							<div class="swiper-wrapper">
								<div class="swiper-slide" v-for="(item, index) in recommendProduct" :key="index">
									<div class="good-itemWrap">
										<div class="good-item" :key="index" @click="toDetail(item)">
											<div class="good-img">
												<img :src="item.showImgSrc + '?x-oss-process=image/resize,p_30'" loading="lazy" :alt="item.name" :title="item.name" @load="imageLoaded(item.id)" />
												<div class="priceBtn" v-show="item.discount">
													<!-- <div class="circle"></div> -->
													<div>{{ langSemiCustom.save }}</div>
													<div>{{ (item.discount * 100).toFixed(0) + " %" }}</div>
												</div>
											</div>
											<div class="good-info" v-show="isImageLoaded(item.id)">
												<h3 :title="item.name">
													{{ item.name }}
												</h3>
												<div class="priceBox">
													<div class="price" v-if="!item.discount">
														<!-- <div style="height: 17px"></div> -->
														{{ langSemiCustom.priceFrom }}
														<label>
															<CCYRate :price="item.lowestPrice"></CCYRate>
														</label>
														<span v-show="item.highestPrice" style="color: #333333; font-weight: 400">{{ langSemiCustom.to }}</span>
														<label v-show="item.highestPrice">
															<CCYRate :price="item.highestPrice"></CCYRate>
														</label>
													</div>
													<div class="discountPrice" v-else>
														<!-- <div class="oldPrice">
                                                            {{ langSemiCustom.listPrices }}
                                                            <span class="price">${{ item.lowestPrice }}</span>
                                                            <span style="text-decoration: line-through" v-show="item.highestPrice">{{ langSemiCustom.to }}</span>
                                                            <span v-show="item.highestPrice">${{ item.highestPrice }}</span>
                                                        </div> -->
														<div class="nowPrice">
															<span>{{ langSemiCustom.onSale }}</span>
															{{ langSemiCustom.from }}
															<label>
																<CCYRate :price="item.lowestDiscountPrice"></CCYRate>
															</label>
															<span v-show="item.highestDiscountPrice" style="color: #333333; font-weight: 400">{{ langSemiCustom.to }}</span>
															<label v-show="item.highestDiscountPrice">
																<CCYRate :price="item.highestDiscountPrice"></CCYRate>
															</label>
														</div>
													</div>
												</div>
											</div>
											<div class="good-collection" @click.stop="goCollection(item)" v-show="isLogin">
												<b class="icon-shoucang" v-if="!item.isCollection"></b>
												<b class="icon-xinxin isActive" v-else></b>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="swiper-pagination"></div>
						<div class="swiper-button-next"></div>
					</div>
				</div>
				<div class="see-more" v-show="!isMobile" @click="toCate">
					<span :title="langSemiCustom.see2">{{ langSemiCustom.see2 }}</span>
					<v-icon>mdi-arrow-right-thick</v-icon>
				</div>
			</div>

			<modal-half-recently-view :visitRecordList="visitRecordList"></modal-half-recently-view>
			<modal-half-more-consider :hotList="hotList" :hotRoutingName="hotRoutingName"></modal-half-more-consider>
			<client-only>
				<modal-half-related-search v-show="$store.state.proTheme != 11 && !domainName" :childCategoryRoutingNameList="childCategoryRoutingNameList"> </modal-half-related-search>
			</client-only>
		</template>
	</div>
</template>

<script>
import { addCollection, deleteConllectionByUserId, findRelatedRecom, getProductInfoAndParamRelation } from "@/api/web";

export default {
	name: "modalHalfRecommend",
	props: ["halfCateDTO", "isStockPage"],
	data() {
		return {
			modal: {
				style: {},
				type: {},
				...this.data,
			},
			recommendProduct: [],
			visitRecordList: [],
			hotList: [],
			hotRoutingName: "",
			childCategoryRoutingNameList: {},
			routeName: "",
			categoryId: "",
			productId: "",
			isManage: false,
			domainName: false,
		};
	},
	computed: {
		langSemiCustom() {
			return this.$store.getters.lang?.semiCustom;
		},
		userId() {
			return this.$store.state.userInfo?.id;
		},
		isLogin() {
			return this.$store.getters.isLogin;
		},
		isMobile() {
			return this.$store.state.device === "mb";
		},
		routingName() {
			return this.$store.state.pagePath;
		},
	},
	methods: {
		imageLoaded(id) {
			const image = this.recommendProduct.find((img) => img.id == id);
			image.loaded = true;
		},
		isImageLoaded(id) {
			const image = this.recommendProduct.find((img) => img.id == id);
			return image.loaded;
		},
		toCate() {
			if (this.halfCateDTO) {
				let routePath = this.routeName ? this.routeName : this.halfCateDTO.customRouting;
				this.$router.push({
					path: routePath,
				});
			}
		},
		goCollection(item) {
			let isCollection = item.isCollection;
			if (isCollection) {
				deleteConllectionByUserId({
					userId: this.userId,
					productId: item.id,
				}).then((res) => {
					item.isCollection = false;
				});
			} else {
				addCollection({
					userId: this.userId,
					website: 1,
					cateId: item.categoryId,
					productId: item.id,
				}).then((res) => {
					item.isCollection = true;
				});
			}
		},
		findRelatedRecom() {
			findRelatedRecom({
				productId: this.productId,
				userIdStr: this.$store.state.userInfo.id ? this.$store.state.userInfo.id : this.$store.state.userUUID,
				categoryId: this.categoryId,
				productType: this.isStockPage,
			}).then((res) => {
				this.recommendProduct = this.setDefaultShowImg(res.data.recommendProductList);
				this.visitRecordList = this.setDefaultShowImg(res.data.visitRecordList);
				this.hotList = this.setDefaultShowImg(res.data.hotList);
				this.hotRoutingName = res.data.hotRoutingName;
				this.routeName = res.data.routingName;
				this.childCategoryRoutingNameList = res.data.childCategoryRoutingNameList;
			});
		},
		setDefaultShowImg(list) {
			if (!list) {
				return [];
			}
			list.forEach((item) => {
				if (item.imgJson) {
					item.showImgSrc = JSON.parse(item.imgJson)[0]?.url;
					item.selectedColorIndex = 0;
					item.loaded = false;
				} else {
					item.showImgSrc = "";
					item.selectedColorIndex = 0;
					item.loaded = false;
				}
			});
			return list;
		},
		toDetail(item) {
			this.$router.push({
				path: item.productRouting,
			});
		},
		getAllParams() {
			return new Promise((resolve) => {
				getProductInfoAndParamRelation({
					routingName: this.routingName,
					userId: this.isLogin ? this.userId : null,
					productParamId: this.$route.query.id,
				}).then(async (res) => {
					this.productId = res.data.id;
					this.categoryId = res.data.categoryId;
					this.$Bus.$emit("saveVisit", this.productId);
					resolve(res.data);
				});
			});
		},
	},
	async mounted() {
		if (process.env.isManage) {
			this.isManage = true;
			return false;
		}
		if (this.routingName) {
			this.productId = await getProductInfoAndParamRelation({
				routingName: this.routingName,
				userId: this.isLogin ? this.userId : null,
				productParamId: this.$route.query.id,
			}).then(async (res) => {
				// this.productId = res.data.id;
				this.categoryId = res.data.categoryId;
				return res.data.id;
			});
			this.recommendProduct = await findRelatedRecom({
				productId: this.productId,
				userIdStr: this.$store.state.userInfo.id ? this.$store.state.userInfo.id : this.$store.state.userUUID,
				categoryId: this.categoryId,
				productType: this.isStockPage,
			}).then((res) => {
				// this.recommendProduct = this.setDefaultShowImg(res.data.recommendProductList);
				this.visitRecordList = this.setDefaultShowImg(res.data.visitRecordList);
				this.hotList = this.setDefaultShowImg(res.data.hotList);
				this.hotRoutingName = res.data.hotRoutingName;
				this.routeName = res.data.routingName;
				this.childCategoryRoutingNameList = res.data.childCategoryRoutingNameList;
				return this.setDefaultShowImg(res.data.recommendProductList);
			});
		}
		this.myswiper3 = new Swiper(this.$refs.swiper3, {
			slidesPerView: 5,
			spaceBetween: 10,
			slidesPerGroup: 2,
			watchSlidesVisibility: true, //防止不可点击
			grabCursor: true,
			navigation: {
				nextEl: ".myswiper3Wrap .swiper-button-next",
				prevEl: ".myswiper3Wrap .swiper-button-prev",
			},
			pagination: {
				el: ".myswiper3Wrap .swiper-pagination",
			},
			breakpoints: {
				// when window width is >= 320px
				320: {
					slidesPerView: 2,
					spaceBetween: 5,
					grid: {
						rows: 1,
						fill: "row",
					},
				},
				750: {
					slidesPerView: 4,
					spaceBetween: 5,
					grid: {
						rows: 1,
						fill: "row",
					},
				},
				1000: {
					slidesPerView: 4,
					spaceBetween: 10,
					grid: {
						rows: 1,
					},
				},
				1400: {
					slidesPerView: 5,
					spaceBetween: 10,
					grid: {
						rows: 1,
					},
				},
			},
		});
		let hostname = window.location.hostname;
		this.domainName = hostname === "www.fulldesigns.com";
	},
	watch: {
		productId(newVal, oldVal) {
			if (newVal && newVal != oldVal) {
				this.$Bus.$emit("saveVisit", this.productId);
			}
		},
	},
};
</script>

<style scoped lang="scss">
.pdt-recommend {
	h2 {
		margin-bottom: 20px;
		font-size: 36px;
		font-weight: bold;
		text-align: center;
	}

	.see-more {
		margin-top: 20px;
		display: flex;
		align-items: center;
		justify-content: flex-end;
		font-size: 16px;
		// color: $color-primary;
		// text-decoration: underline;
		cursor: pointer;

		&:hover {
			span {
				color: $color-primary;
				text-decoration: underline;
			}

			.v-icon {
				color: $color-primary;
			}
		}
	}

	.myswiper3Wrap {
		position: relative;

		--swiper-navigation-color: #333333;
		/* 单独设置按钮颜色 */
		--swiper-navigation-size: 12px;

		/* 设置按钮大小 */
		.swiper-button-next,
		.swiper-button-prev {
			width: 48px;
			height: 48px;
			background: #e5e5e5;
			border-radius: 50%;
			margin-top: 0;
			transform: translateY(-50%);
			font-weight: 700;
		}

		.swiper-button-prev {
			left: -50px;
		}

		.swiper-button-next {
			right: -50px;
		}

		.swiper-pagination {
			display: none;
		}

		.myswiper3 {
			.swiper-slide {
				.good-itemWrap {
					// padding: 10px;
					height: 100%;
					border: 1px solid #e9e9e9;
				}

				.good-item {
					display: grid;
					grid-template-columns: 1fr;
					grid-template-rows: auto 1fr;
					position: relative;
					background-color: #fff;
					transition: all 0.3s;
					//box-shadow: 0px 1px 3px 0px rgba(0,0,0,0.15);
					// border: 1px solid #ccc;
					// border: 1px solid #e9e9e9;
					box-shadow: 0px 0px 5px 2px rgba(0, 0, 0, 0.05);

					.good-collection {
						position: absolute;
						top: 10px;
						left: 10px;

						b {
							color: #999999;
							font-size: 24px;
						}

						b.isActive {
							color: $color-primary;
						}
					}

					.good-img {
						// padding: 30px;
						overflow: hidden;
						position: relative;
						aspect-ratio: 1;
						display: flex;
						align-items: center;
						justify-content: center;

						img {
							max-width: 100%;
							max-height: 100%;
							object-fit: contain;
						}

						.priceBtn {
							// width: 32%;
							width: fit-content;
							padding: 0 4px;
							height: 25px;
							// background: linear-gradient(to right, var(--color-primary-darken), var(--color-bright));
							background: linear-gradient(60deg, #ee4113 0%, #f3aa1e 100%);
							border-radius: 0px 10px 0px 10px;
							position: absolute;
							left: 0;
							bottom: 10px;
							display: flex;
							column-gap: 4px;
							align-items: center;
							justify-content: space-evenly;
							font-size: 12px;
							font-weight: bold;
							color: #fff;

							.circle {
								flex-shrink: 0;
								position: relative;
								width: 20px;
								height: 20px;
								border-radius: 50%;
								background-color: #ffffff;

								&::before {
									content: "%";
									font-size: 12px;
									position: absolute;
									top: 50%;
									left: 50%;
									transform: translate(-50%, -50%);
									color: $color-primary;
								}
							}
						}
					}

					.good-info {
						display: flex;
						flex-direction: column;
						justify-content: center;
						padding: 0 10px 10px;
						transition: all 0.3s;

						h3 {
							margin: 10px 0;
							font-size: 18px;
							height: 3em;
							line-height: 1.5;
							word-break: break-word;
							text-overflow: ellipsis;
							display: -webkit-box;
							-webkit-box-orient: vertical;
							-webkit-line-clamp: 2;
							/* 这里是超出几行省略 */
							overflow: hidden;
						}

						.priceBox {
							.price {
								margin: 0 0 8px 0;
								font-size: 18px;

								label {
									color: $color-primary;
									font-size: 18px;
									font-weight: bold;
								}
							}

							.discountPrice {
								margin: 0 0 8px 0;
								font-size: 18px;

								.oldPrice {
									font-size: 14px;
									color: #636363;

									span {
										font-size: 14px;
										text-decoration: line-through;
										color: #636363;
										font-weight: 400;
									}
								}

								span {
									color: $color-primary;
									font-weight: bold;
								}

								label {
									color: $color-primary;
									font-size: 18px;
									font-weight: bold;
								}
							}
						}
					}
				}

				.good-item:hover {
					.good-info {
						background-color: var(--color-second);
					}
				}
			}
		}
	}
}

@media screen and (max-width: $mb-width) {
	.pdt-recommend {
		h2 {
			margin-bottom: 10px;
			font-size: 21px;
		}

		.see-more {
			font-size: 14px;
			margin-top: 10px;
			margin-bottom: 5px;
			// color: $color-primary;
			// text-decoration: underline;
		}

		.myswiper3Wrap {
			/* 设置按钮大小 */
			.swiper-button-next,
			.swiper-button-prev {
				display: none;
			}

			.swiper-pagination {
				display: block;
			}

			// ::v-deep .swiper-pagination .swiper-pagination-bullet {
			// 	margin: 0 4px;
			// 	margin: 0 var(--swiper-pagination-bullet-horizontal-gap, 4px);
			// }

			.swiper-pagination-bullets.swiper-pagination-horizontal,
			.swiper-pagination-custom,
			.swiper-pagination-fraction {
				bottom: -21px;
				left: 0;
				width: 100%;
			}

			.myswiper3 {
				.swiper-slide {
					.good-itemWrap {
						padding: 0;
						height: 100%;
					}

					.good-item {
						grid-template-rows: auto 1fr;

						.good-collection {
							top: 5px;
							left: 5px;

							b {
								font-size: 18px;
							}
						}

						.good-img {
							// padding: 5px;
							.priceBtn {
								bottom: auto;
								left: auto;
								right: 0;
								top: 10px;
								height: 20px;
								// width: 42%;
								width: fit-content;
								font-weight: 400;
								border-radius: 0 8px 0 8px;

								.circle {
									display: none;
								}
							}
						}

						.good-info {
							padding: 0 2px 5px;

							h3 {
								margin: 5px 0;
								font-size: 16px;
								height: 2.1em;
								line-height: 1;
							}

							.priceBox {
								.price {
									margin: 0 0 8px 0;
									font-size: 12px;

									label {
										color: $color-primary;
										font-size: 14px;
										font-weight: bold;
									}
								}

								.discountPrice {
									margin: 0 0 8px 0;
									font-size: 12px;

									.oldPrice {
										font-size: 12px;
										color: #636363;

										span {
											font-size: 14px;
											text-decoration: line-through;
											color: #636363;
											font-weight: 400;
										}
									}

									span {
										color: $color-primary;
										font-weight: bold;
									}

									label {
										color: $color-primary;
										font-size: 13px;
										font-weight: bold;
									}
								}
							}
						}
					}
				}
			}
		}
	}
}
</style>
