<template>
	<div class="swiper-area">
		<div class="myswiper2">
			<div class="swiper" ref="swiper2">
				<div class="swiper-wrapper">
					<div class="swiper-slide" v-for="(item, index) in urlList" :key="index">
						<img :src="item.url" :alt="item.alt" :title="item.alt" />
					</div>
				</div>
			</div>
            <template v-if="urlList.length && urlList.length>1">
                <div class="swiper-button-next">
                    <img src="https://static-oss.gs-souvenir.com/web/quoteManage/20240905/Right_Toggle_Button_20240905KiJQ6t.png" alt="rightBtn" />
                </div>
                <div class="swiper-button-prev">
                    <img src="https://static-oss.gs-souvenir.com/web/quoteManage/20240905/Left_Toggle_Button_20240905wjrTGT.png" alt="leftBtn" />
                </div>
            </template>
		</div>
		<div class="myswiper1">
			<div class="swiper" ref="swiper1">
				<div class="swiper-wrapper">
					<div class="swiper-slide" v-for="(item, index) in urlThumbList" :style="{backgroundColor:thumbSwiperBgColor}" :key="index">
						<img :src="item.urlThumb" :alt="item.alt" :title="item.alt" />
						<div class="check">
							<b class="icon-check"></b>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	props: {
        thumbSwiperBgColor:{
            type: String,
            default: "rgba(0, 0, 0, 0.25)"
        },
		imageJson: {
			type: Array,
		},
	},
    computed:{
        urlThumbList(){
            return this.imageJson.filter(item=>item.urlThumb)
        },
        urlList(){
            return this.imageJson.filter(item=>item.url)
        }
    },
	methods: {
		initSwiper() {
			this.myswiper1 = new Swiper(this.$refs.swiper1, {
				slidesPerView: 4,
				spaceBetween: 8,
				watchSlidesVisibility: true, //防止不可点击
				grabCursor: true,
			});
			this.myswiper2 = new Swiper(this.$refs.swiper2, {
				slidesPerView: 1,
				spaceBetween: 10,
				autoplay: false,
				grabCursor: true,
				observer: true,
				observeParents: true,
				thumbs: {
					swiper: this.myswiper1,
				},
				navigation: {
					nextEl: ".myswiper2 .swiper-button-next",
					prevEl: ".myswiper2 .swiper-button-prev",
				},
				on: {
					slideChangeTransitionEnd: (val) => {
                        this.$emit("changeSlide",val.activeIndex)
					},
				},
			});
		},
	},
	async mounted() {
        await this.$nextTick();
		this.initSwiper();
	},
};
</script>

<style lang="scss" scoped>
.swiper-area {
	margin: 1.25em 0;
	width: 100%;

	.myswiper2 {
		width: 90%;
		margin: 0 auto;
		position: relative;

        .swiper{
            width: 85%;
            @include respond-to(mb){
                width: 100%;
            }
        }

		@include respond-to(mb) {
			width: 100%;
		}

		.swiper-slide {
			display: flex;
			justify-content: center;
			align-items: center;

			::v-deep .pic-img .img-container {
				height: 20em;
				@include respond-to(mb) {
					height: 15em;
				}
			}
		}

		.swiper-button-next::after,
		.swiper-button-prev::after {
			display: none;
		}

		.swiper-button-next img,
		.swiper-button-prev img {
			width: 5em;
			@include respond-to(mb) {
				width: 2em;
			}
		}
	}

	.myswiper1 {
		margin: 1.25em auto 0;
        width: 84%;

		@include respond-to(mb) {
			padding: 0;
			width: 100%;
		}

		.swiper-slide {
			display: flex;
			justify-content: center;
			align-items: center;
			border-radius: 8px;
			border: 1px solid transparent;
			width: 100%;
			height: 5em;
			padding: 0.625em 0;
			background-color: rgba(0, 0, 0, 0.25);
			box-sizing: border-box;
			cursor: pointer;

			.check {
				display: none;
				position: absolute;
				top: 0;
				right: 0;
				width: 15px;
				height: 15px;
				line-height: 15px;
				font-size: 12px;
				text-align: center;
				background-color: $color-primary;
				color: #ffffff;
				border-radius: 0 6px 0 6px;
			}

			img {
				width: 100%;
				height: 100%;
				object-fit: contain;
			}

			@include respond-to(mb) {
				background-color: rgba(255, 255, 255, 0.25);
			}

			&.swiper-slide-thumb-active {
				border-color: $color-primary;

				.check {
					display: block;
				}
			}
		}
	}
}
</style>
