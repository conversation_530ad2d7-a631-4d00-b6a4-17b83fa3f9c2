* {
	box-sizing: border-box !important;
}

html {
	scroll-behavior: smooth;
	font-size: min(1vmax, 16px) !important;
	scroll-padding-top: var(--scroll-padding-top);
	touch-action: manipulation; //禁用双击缩放，移除 300ms 延迟
}

// 全局样式
body {
	color: $text-primary;
	height: auto !important;
	background-color: $bg-page;
	font-family: $text-family, <PERSON><PERSON>, Arial, serif;
	-webkit-tap-highlight-color: rgba(0, 0, 0, 0);
	-webkit-text-size-adjust: 100%;

	@include respond-to(mb) {
		@supports not (background: paint(xxx)) {
			::v-deep input,
			::v-deep textarea {
				font-size: 16px !important;
			}
		}
	}

	a {
		color: inherit;
		cursor: pointer;
		text-decoration: none;
	}

	b {
		font-weight: inherit;
		display: inline-block;

		&::before {
			font-weight: 400;
			display: inline-block;
			font-family: modalicon !important;
		}
	}

	s {
		text-decoration: none;
		border-bottom: 1px solid $text-primary;
	}

	hr {
		border: none;
		border-top: 1px solid $line-primary;
	}

	ul {
		margin-left: 20px;
	}

	img,
	video {
		width: 100%;
		object-fit: cover;
		vertical-align: middle;
	}

	label {
		cursor: inherit;
	}

	input,
	button,
	select,
	optgroup,
	textarea {
		border: none;
		outline: none;
		color: inherit;
		background: none;
		font-size: inherit;
		font-family: inherit;
		line-height: inherit;
	}

	input[disabled] {
		cursor: not-allowed;
	}

	input::-webkit-input-placeholder {
		color: #999;
	}

	.el-scrollbar {
		.el-scrollbar__bar {
			opacity: 1!important;
		}
	}
}

[red] {
	color: $color-red;
}

[pre] {
	white-space: pre-line;
	word-break: normal !important;
}

[flex] {
	display: flex;
	align-items: stretch;

	&[center] {
		align-items: center;
	}
}

[pointer] {
	cursor: pointer;
}

[scrollbar] {
	overflow: auto;
	scroll-behavior: smooth;
}

.flex {
	display: flex !important;
}

.justify-content-center {
	justify-content: center;
}

.justify-content-end {
	justify-content: flex-end;
}

.align-items-center {
	align-items: center;
}

.justify-content-between {
	justify-content: space-between;
}

.justify-content-end {
	justify-content: flex-end;
}

.text-center {
	text-align: center;
}

.flex-column {
	flex-direction: column;
}

//报价系统 b标签 tip-icon
.tip-icon {
	color: $color-primary;
}

//报价系统 询盘添加成功 字体设置
.inquirySuccess {
	font-family: Calibri;

	&.el-message-box {
		width: 467px;
	}

	.el-message-box__title {
		font-size: 20px;
	}

	.el-message-box__content {
		font-size: 16px;
	}

	.inquirySuccessBtn {
		font-size: 16px;
	}
}



/**
 * 交互效果
 */
// .page-enter-active,
// .page-leave-active,
.arrow::before {
	transition-duration: 0.1s;
}

// .page-enter,
// .page-leave-active {
// 	opacity: 0;
// }

.arrow {
	transform: scale(0.5);

	&.rotate::before {
		transform: rotate(-180deg);
	}
}


.toast-box {
	top: 12% !important;
	z-index: 99999999999 !important;
}

.toast-action-box.toasted-container.top-center {
	width: 100vw;
	align-items: center !important;
	top: 20% !important;
	z-index: 99999999;
}

.toast-action {
	text-align: center;
	border: none !important;
	width: 505px !important;
	min-height: 150px !important;
	display: block !important;
	max-width: 90vw !important;
	padding: 3em 2em 7em !important;
	border-radius: 8px !important;
	font-weight: normal !important;
	box-shadow: 0 0 10px $bg-mask !important;
	font-size: 16px !important;

	.action {
		min-width: 8em;
		text-align: center;
		margin: 0 !important;
		padding: 0 1em !important;
		font-size: 16px !important;
		line-height: 2.2em !important;
		border-radius: 10px !important;
		font-weight: normal !important;
		text-transform: none !important;
		border: 1px solid $color-primary;
		position: absolute;
		bottom: 2em;

		&:last-child {
			right: 15%;
			border-color: #ADADAD;
			color: $text-primary !important;
		}

		&:first-child {
			left: 15%;
			border: none;
			color: white !important;
			background: $color-primary;
		}
	}

	&.artwork .action:first-child {
		background: #0066CC;
	}

	&.artwork .action:nth-child(2) {
		background: #adadad;
	}
}


.viewer-toolbar {
	margin: 0.5vmax auto 1vmax;

	li {
		margin: 0 0.5vmax;
		transform: scale(1.1);
	}
}

.viewer-container{
	z-index: 20015;
}

.viewer-container .viewer-close {
	top: 10px;
	right: 10px;

	&::before {
		background-position: 0 !important;
		background-size: 25px !important;
		background-image: url("https://static-oss.gs-souvenir.com/web/quoteManage/20230703/20230703by5FXcwW.png") !important;
		left: 35% !important;
		bottom: 31% !important;
		height: 30px !important;
		width: 30px !important;
	}
}


// pc 和 ipad
@media screen and (min-device-width: $mb-width) {

	body,
	[scrollbar] {
		&::-webkit-scrollbar {
			width: 4px;
			height: 4px;
		}

		&::-webkit-scrollbar-thumb {
			cursor: pointer;
			border-radius: 10px;
			background-color: rgba($color: #999, $alpha: 0.5);
		}

		// &::-webkit-scrollbar-track {
		// 	// background-color: rgba($color: #000000, $alpha: 0.05);
		// }
	}
}


// mb
@media screen and (max-device-width: $mb-width) {
	html {
		font-size: min(3.2vw, 25px) !important;
	}

	.toast-action .action {
		font-size: 15px !important;

		&:first-child {
			left: 10%;
		}

		&:last-child {
			right: 7%;
		}
	}

	.uni-body #comm100-container {
		display: none;
	}
}







.bps-containerb,
.bps-containerc {
	.default-button {
		display: inline-block;
		line-height: 1;
		white-space: nowrap;
		cursor: pointer;
		background: #fff;
		border: 1px solid #D9D9D9;
		color: #606266;
		-webkit-appearance: none;
		text-align: center;
		box-sizing: border-box;
		outline: none;
		margin: 0;
		transition: .1s;
		font-weight: 400;
		-moz-user-select: none;
		-webkit-user-select: none;
		-ms-user-select: none;
		padding: 10px 20px;
		font-size: 18px;
		border-radius: 6px;
	}

	.bps-button {
		display: flex;
		justify-content: center;
		align-items: center;
		height: 48px;
		background: $color-primary;
		color: #ffffff;
		border: none;
		transition: none;
		-webkit-backface-visibility: hidden;
		-webkit-transform-style: preserve-3d;

		&:hover {
			transform: scale(1.03);
			transition-duration: .28s;
			transition-property: box-shadow, transform, opacity;
			transition-timing-function: cubic-bezier(.4, 0, .2, 1);
			background: linear-gradient(90deg, var(--color-primary-lighten), var(--color-primary), var(--color-primary-lighten));
		}

		b.icon-bps-sanjiao {
			margin-left: 10px;
			font-size: 12px;
		}

		@media screen and (max-width: $mb-width) {
			font-size: 12px;
			height: 34px;
			border-radius: 3px;

			b.icon-bps-sanjiao {
				transform: scale(.5);
			}
		}
	}
}

.bps-container {
	width: 100%;
	padding: 0 max(calc(50% - 700px), 1em);
	margin: 0 auto;


	// @media screen and (min-width: $mb-width) {
	// 	max-width: 1230px;
	// }

	// @media screen and (min-width: $pad-width) {
	// 	max-width: 1430px;
	// }

	.default-button {
		display: inline-block;
		line-height: 1;
		white-space: nowrap;
		cursor: pointer;
		background: #fff;
		border: 1px solid #D9D9D9;
		color: #606266;
		-webkit-appearance: none;
		text-align: center;
		box-sizing: border-box;
		outline: none;
		margin: 0;
		transition: .1s;
		font-weight: 400;
		-moz-user-select: none;
		-webkit-user-select: none;
		-ms-user-select: none;
		padding: 10px 20px;
		font-size: 18px;
		border-radius: 6px;
	}

	.bps-button {
		display: flex;
		justify-content: center;
		align-items: center;
		height: 48px;
		background: $color-primary;
		color: #ffffff;
		border: none;
		transition: none;
		-webkit-backface-visibility: hidden;
		-webkit-transform-style: preserve-3d;

		&:hover {
			transform: scale(1.03);
			transition-duration: .28s;
			transition-property: box-shadow, transform, opacity;
			transition-timing-function: cubic-bezier(.4, 0, .2, 1);
			background: linear-gradient(90deg, var(--color-primary-lighten), var(--color-primary), var(--color-primary-lighten));
		}

		b.icon-bps-sanjiao {
			margin-left: 10px;
			font-size: 12px;
		}

		@media screen and (max-width: $mb-width) {
			font-size: 12px;
			height: 34px;
			border-radius: 3px;

			b.icon-bps-sanjiao {
				transform: scale(.5);
			}
		}
	}
}


.order-dialog>.v-card {
	&>.start {
		font-size: 18px !important;
		font-family: Roboto;
		font-weight: 400;
		color: #333333;
		justify-content: center;
		// position: relative;
		// left: 20px;
	}
}

.custom-scrollbar::-webkit-scrollbar {
	/*滚动条整体样式*/
	width: 5px;

	/*高宽分别对应横竖滚动条的尺寸*/
	height: 1px;
}

.custom-scrollbar.csu1::-webkit-scrollbar {
	height: 50px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
	/*滚动条里面小方块*/
	border-radius: 10px;
	background: #d3d3d3;
}

.custom-scrollbar::-webkit-scrollbar-track {
	/*滚动条里面轨道*/
	border-radius: 10px;
}

.cusToolTip {
	max-width: 300px;
	font-size: 14px !important;
}

.el-message {
	max-width: 360px !important;
	min-width: 300px !important;
}

:root {
	--animate-duration: 1s;
	--animate-delay: 1s;
	--animate-repeat: 1
}

.animate__animated {
	-webkit-animation-duration: 1s;
	animation-duration: 1s;
	-webkit-animation-duration: var(--animate-duration);
	animation-duration: var(--animate-duration);
	-webkit-animation-fill-mode: both;
	animation-fill-mode: both
}

.animate__animated.animate__faster {
	-webkit-animation-duration: .5s;
	animation-duration: .5s;
	-webkit-animation-duration: calc(var(--animate-duration) / 2);
	animation-duration: calc(var(--animate-duration) / 2)
}

.animate__fadeInBottomLeft {
	-webkit-animation-name: fadeInBottomLeft;
	animation-name: fadeInBottomLeft
}

.animate__fadeOutBottomLeft {
	-webkit-animation-name: fadeOutBottomLeft;
	animation-name: fadeOutBottomLeft
}

@-webkit-keyframes fadeInBottomLeft {
	0% {
		opacity: 0;
		-webkit-transform: translate3d(-100%, 100%, 0);
		transform: translate3d(-100%, 100%, 0)
	}

	to {
		opacity: 1;
		-webkit-transform: translateZ(0);
		transform: translateZ(0)
	}
}

@keyframes fadeInBottomLeft {
	0% {
		opacity: 0;
		-webkit-transform: translate3d(-100%, 100%, 0);
		transform: translate3d(-100%, 100%, 0)
	}

	to {
		opacity: 1;
		-webkit-transform: translateZ(0);
		transform: translateZ(0)
	}
}

@-webkit-keyframes fadeOutBottomLeft {
	0% {
		opacity: 1;
		-webkit-transform: translateZ(0);
		transform: translateZ(0)
	}

	to {
		opacity: 0;
		-webkit-transform: translate3d(-100%, 100%, 0);
		transform: translate3d(-100%, 100%, 0)
	}
}

@keyframes fadeOutBottomLeft {
	0% {
		opacity: 1;
		-webkit-transform: translateZ(0);
		transform: translateZ(0)
	}

	to {
		opacity: 0;
		-webkit-transform: translate3d(-100%, 100%, 0);
		transform: translate3d(-100%, 100%, 0)
	}
}

#manageApp {
	.v-btn {
		text-transform: none !important;
	}

	.v-divider {
		border-color: #e5e5e5;
	}

	.autoSelect1 {
		font-size: 12px;

		.v-label {
			font-size: 12px;
		}

		.v-input__slot {
			min-height: auto !important;

		}

		.v-input__control {
			min-height: auto !important;
		}
	}

	.v-tooltip__content {
		font-size: 12px;
	}

	[disabled] {
		pointer-events: none; //鼠标点击不可修改
	}

	// [hidden] {
	// 	display: none !important;
	// }

	.cusDivider {
		height: 1px;
		border-top: 1px dashed #EBEBEB;
	}

	.h-100 {
		height: 100%;
	}

	.w-100 {
		width: 100%;
	}
}

.pointer {
	cursor: pointer;
}

.h-100 {
	height: 100%;
}

.w-100 {
	height: 100%;
}

.clearfix::after {
	content: "";
	display: block;
	height: 0;
	clear: both;
}

.clearfix {
	zoom: 1;
}

.d-flex-center {
	display: flex;
	justify-content: center;
	align-items: center;
}

.extend-click-area {
	position: relative;

	&::after {
		content: "";
		position: absolute;
		top: -20px;
		right: -20px;
		bottom: -20px;
		left: -20px;
	}
}

@media screen and (max-width: 750px) {
	.el-message-box__wrapper .el-message-box {
		width: 90%;
	}
}

.swiperDialog {
	.el-dialog__body {
		padding: 15px;

		@media screen and (max-width: 750px) {
			padding: 5px;
		}
	}

	.el-dialog__header {
		padding: 0;
	}

	.swiper-container-wrap .top {}
}

/* google、safari */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
	-webkit-appearance: none !important;
	margin: 0;
}

/* 火狐 */
input[type="number"] {
	-moz-appearance: textfield;
}

.nuxtContent .v-application {
	line-height: normal;

	a {
		color: inherit;
	}
}

.v-application .nuxtContent {
	a {
		color: inherit;
	}

	p {
		margin-bottom: auto;
	}

	[primary] {
		color: white;
	}
}

.viewer-open {
	overflow: visible !important;
}

[hiddenScrollBar] {
	&::-webkit-scrollbar {
		width: 0px;
	}
}

[neonScrollBar] {
	&::-webkit-scrollbar {
		width: 5px;
	}

	&::-webkit-scrollbar-track {
		// background: rgb(179, 177, 177);
		border-radius: 10px;
	}

	&::-webkit-scrollbar-thumb {
		background: rgb(136, 136, 136);
		border-radius: 10px;
	}

	&::-webkit-scrollbar-thumb:hover {
		background: rgb(100, 100, 100);
		border-radius: 10px;
	}

	&::-webkit-scrollbar-thumb:active {
		background: rgb(68, 68, 68);
		border-radius: 10px;
	}
}

[neonAiScrollBar] {
	&::-webkit-scrollbar {
		width: 5px;
	}

	&::-webkit-scrollbar-track {
		border-radius: 5px;
	}

	&::-webkit-scrollbar-thumb {
		width: 5px;
		background: #5159C4;
		border-radius: 5px;
	}

	&::-webkit-scrollbar-thumb:hover {
		background: #313bc2;
		border-radius: 5px;
	}

	&::-webkit-scrollbar-thumb:active {
		background: #313bc2;
		border-radius: 5px;
	}
}



#comm100-container>div>div {
	right: 0 !important;
	bottom: 14% !important;
	z-index: 1000 !important;
}

.account-orders-invoice #comm100-container {
	display: none;
}

.spinner {
	display: flex;
	align-items: center;
	justify-content: center;
	font-weight: bold;
	margin-top: 10px;
}

.uploadArtworkPopper {
	width: 240px;
}

.animate__fadeInBottomRight {
	-webkit-animation-name: fadeInBottomRight;
	animation-name: fadeInBottomRight
}

.animate__fadeOutBottomRight {
	-webkit-animation-name: fadeOutBottomRight;
	animation-name: fadeOutBottomRight
}
@-webkit-keyframes fadeInBottomRight {
	0% {
		opacity: 0;
		-webkit-transform: translate3d(70%, 100%, 0);
		transform: translate3d(70%, 100%, 0)
	}

	to {
		opacity: 1;
		-webkit-transform: translateZ(0);
		transform: translateZ(0)
	}
}

@keyframes fadeInBottomRight {
	0% {
		opacity: 0;
		-webkit-transform: translate3d(70%, 100%, 0);
		transform: translate3d(70%, 100%, 0)
	}

	to {
		opacity: 1;
		-webkit-transform: translateZ(0);
		transform: translateZ(0)
	}
}

@-webkit-keyframes fadeOutBottomRight {
	0% {
		opacity: 1;
		-webkit-transform: translateZ(0);
		transform: translateZ(0)
	}

	to {
		opacity: 0;
		-webkit-transform: translate3d(70%, 100%, 0);
		transform: translate3d(70%, 100%, 0)
	}
}

@keyframes fadeOutBottomRight {
	0% {
		opacity: 1;
		-webkit-transform: translateZ(0);
		transform: translateZ(0)
	}

	to {
		opacity: 0;
		-webkit-transform: translate3d(70%, 100%, 0);
		transform: translate3d(70%, 100%, 0)
	}
}

.v-menu__content{
	z-index: 10000 !important;
}

.v-tooltip__content {
	z-index: 10000 !important;
}

.drawDialog {
	.el-drawer__header {
		margin-bottom: 0;
		padding: 10px;
	}
}