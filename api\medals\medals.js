import { request } from "@/utils/request";

// pid 	父id
const getByPId = function (data) {
  return request({
    url: '/quote/cate/getByPId',
    method: 'get',
    params: data,
  })
};

//cateId
const getCateParamRelationByCateId = function (data) {
  return request({
    url: '/quote/cate/getCateParamRelationByCateId',
    method: 'get',
    params: data,
  })
};

const findAll = function (data) {
  return request({
    url: '/quote/colors/findAll',
    method: 'post',
    data: data,
  })
};
//图库
const getAll = function () {
  return request({
    url: '/quote/icons/getAll',
    method: 'post',
  })
};
//获取货币类型
const getCurrency = function () {
  return request({
    url: '/quote/currency/getAllCurrency',
    method: 'get',
  })
};
const calculate = function (data) {
  return request({
    url: '/quote/quoteCalculate/calculate',
    method: 'post',
    data: data,
  })
};

//获取大类信息
const getInfo = function (data) {
  return request({
    url: '/quote/cate/getInfo',
    method: 'get',
    data: data,
  })
}

//价格分层参数
const getPriceData = function (data) {
  return request({
    url: '/quote/websiteIncreasePrice/getPriceData',
    method: 'get',
    data: data,
  })
}

//获取某个分类的推荐分类列表
const getAppRecommendCateList = function (data) {
  return request({
    url: '/quote/cate/getAppRecommendCateList',
    method: 'get',
    data: data,
  })
}
//首单折扣
const getFirstDiscount = function (data) {
  return request({
    url: '/app/member/addSubscribes',
    method: 'post',
    data,
  })
}
export const medalsApi = {
  getByPId,
  getCateParamRelationByCateId,
  findAll,
  calculate,
  getAll,
  getCurrency,
  getInfo,
  getPriceData,
  getAppRecommendCateList,
  getFirstDiscount
};
