.quoteWrap {
  font-family: <PERSON><PERSON><PERSON>, <PERSON><PERSON>, serif;
  font-size: 16px;

  @include respond-to(mb) {
    font-size: 12px;
    /* iOS Safari only */
    @supports not (background: paint(xxx)) {
      ::v-deep input,
      ::v-deep textarea {
        font-size: 16px !important;
      }
    }
  }

  .picWrap {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    width: 100%;
    padding: 0 !important;
    @include respond-to(mb) {
      padding: 1.3333vw 1.3333vw 4vw !important;
    }
  }

  .videoBox{
    @include respond-to(mb) {
      ::v-deep .vjs-tech{
        border-radius: 10px;
        object-fit: cover !important;
      }
    }
  }

  .text-center {
    text-align: center;
  }

  .header {
    background-color: #f2f5f7;
    display: grid;
    grid-template-columns: repeat(48, 1fr);
    padding-bottom: 20px;
    grid-column: 2/49;

    h1 {
      position: relative;
      font-size: 18px;
      font-weight: 400;
      color: #333333;
      z-index: 0;
      grid-column: 1/49;
      text-align: left;
      padding-left: 10px;
    }

    @media screen and (max-width: 767px) {
      grid-template-columns: 1fr;
      padding-bottom: 10px;
      h1 {
        padding-left: 10px;
        margin-bottom: 0;
        font-size: 14px;
      }
    }
  }

  .content {
    position: relative;
    display: grid;
    grid-template-columns: repeat(48, 1fr);
    background-color: #f2f5f7;
    padding: 20px 0;

    @include respond-to(mb){
      padding: 10px 0;
    }

    .leftArea {
      grid-column: 2/33;

      @media screen and (max-width: 1500px) {
        grid-column: 2/48;
      }
    }

    .rightArea {
      grid-column: 34/49;
      margin-left: 30px;
      margin-right: 10px;

      .mask {
        z-index: 101;
        background-color: #fff;
      }

      @media screen and (max-width: 1500px) {
        display: none;
      }
    }
  }

  .footer {
    display: grid;
    grid-template-columns: 700px;
    justify-content: center;
    padding: 20px;
    background: #eef2f5;

    @include respond-to(mb) {
      grid-template-columns: 1fr;
      padding: 10px;
      background-color: #e0e0e0;
    }
  }
}