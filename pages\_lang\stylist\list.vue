<template>
	<v-app>
		<v-main>
			<div class="custom-scrollbar stylistList">
				<div class="main" v-show="pageStatus === 1">
					<!--左侧-->
					<div v-show="baseModel.isShowLeft" class="mainLeft">
						<div v-if="!isMobile" class="mainLeftNav">
							<!-- 	All Products<span class="fuhao">></span><span class="key">Pins</span> -->
						</div>
						<div class="filtersLeft">
							<div v-if="isMobile" class="exit">
								<b class="icon-back" @click="isShowLeft()" />
							</div>
							<div class="filters">
								<span class="p1">Filters</span>
								<span class="p2">({{ listModel.total || 0 }} Results)</span>
								<!-- <span v-if="!isMobile" class="pRight" @click="isShowLeft()">
									<i class="v-icon notranslate mdi mdi-close theme--light" />
								</span> -->
							</div>
							<div class="history">
								<div v-for="(item, index) in baseModel.historyJson" :key="index" class="li">
									{{ item.label }}
									<i class="v-icon notranslate mdi mdi-close theme--light xxx" @click="deleteFunc(item)" />
								</div>
								<div v-if="baseModel.historyJson.length > 0" class="liClose" @click="clearAll()">
									<i class="v-icon mdi mdi-trash-can-outline theme--light xxxDel" />
									Clear All
								</div>
							</div>

							<div v-if="baseModel.isLoading">
								<div v-if="isMobile" class="sortByM">
									<el-collapse v-model="baseModel.activeNamesSortByM">
										<el-collapse-item title="Sort By" name="1">
											<el-radio-group v-model="baseModel.dropdownCommandValue" @input="dropdownCommandM">
												<el-radio v-for="(item, index) in selectModel.sortOptions" :key="index" :label="item.label">{{ item.label }}</el-radio>
											</el-radio-group>
										</el-collapse-item>
									</el-collapse>
								</div>
								<LyFoldCheckbox ref="lyFoldCheckbox" v-loading="baseModel.lyFoldCheckboxLoading" :model="baseModel.model" @changeSlider="changeSlider" @changeCheckbox="changeCheckbox" />
							</div>
						</div>
					</div>
					<!--右侧-->
					<div v-show="getRight()" class="custom-scrollbar mainRight" :class="!baseModel.isShowLeft ? 'mainRightOff' : ''">
						<div class="mainLeftNav">
							<div class="l">
								<el-input placeholder="Product Keywords..." v-model="queryModel.keyword" class="input-with-select" @input="keywordChange">
									<el-button slot="append" icon="el-icon-search"></el-button>
								</el-input>
							</div>
							<div class="r">
								<b class="icon-a-HideFilters icon icon1" @click="isShowLeft()" />
								<div class="p1 pRight" @click="isShowLeft()">
									<span v-if="!isMobile">
										<span v-if="baseModel.isShowLeft">Hide</span>
										<span v-if="!baseModel.isShowLeft">Show</span>
									</span>
									Filters
								</div>
								<div v-if="!isMobile" class="p1">
									Sort By :
									<el-dropdown trigger="click" @command="dropdownCommand">
										<span class="el-dropdown-link">
											{{ baseModel.dropdownCommandValue }}
											<b class="icon-Down icon icon2" />
										</span>
										<el-dropdown-menu slot="dropdown">
											<el-dropdown-item v-for="(item, index) in selectModel.sortOptions" :key="index" :command="item.value">{{ item.label }}</el-dropdown-item>
										</el-dropdown-menu>
									</el-dropdown>
								</div>
							</div>
						</div>

						<div v-if="baseModel.isLoading" class="tableList">
							<LyCardList :model="listModel" @move="lyCardListMove" @collect="collectFunc" @openDetail="openDetail" />
						</div>
					</div>
				</div>
				<DesignerDetail :product-routing="productRouting" v-if="pageStatus === 2" @back="back"></DesignerDetail>
			</div>
		</v-main>
	</v-app>
</template>
<script>
import { Common } from "@/utils/common";
import LyFoldCheckbox from "@/components/Quote/Ly/LyFoldCheckbox";
import LyCardList from "@/components/Quote/Ly/LyCardList";
import { productGetAllTags, tagsGetTagsProductCount, productGetProductList } from "@/api/web.js";
import { baseModel, queryModel, listModel, selectModel } from "./entity/list";
import "@/plugins/element"; //生产环境 需要手动引入
import DesignerDetail from "@/components/DesignerDetail.vue";
let that;
export default {
	components: { DesignerDetail, LyFoldCheckbox, LyCardList },
	async asyncData({ store, error }) {
		try {
			//左侧
			let resData = await productGetAllTags({});
			//产品标签
			let resData2 = await tagsGetTagsProductCount({});

			//列表
			let m = {
				...new queryModel(),
				priceStart: resData.data?.priceInterval?.minPrice,
				priceEnd: resData.data?.priceInterval?.maxPrice,
				//userId: store.getters.userId,
			};

			if (store.getters.isMobile) {
				m.pageSize = 8;
			} else {
				m.pageSize = 12;
			}
			console.log("服务端", resData.data);
			let resData3 = await productGetProductList(m);
			return {
				resData: resData.data,
				resData2: resData2.data,
				resData3: resData3.data,
			};
		} catch (e) {
			return error({ statusCode: 404 });
		}
	},
	computed: {
		isMobile() {
			return this.$store.getters.isMobile;
		},
		userId() {
			return this.$store.state.userInfo?.id;
		},
		device() {
			return this.$store.state.device;
		},
	},
	data() {
		return {
			pageStatus: 1,
			productRouting: "",
			baseModel: new baseModel(),
			queryModel: new queryModel(),
			listModel: new listModel(),
			selectModel: new selectModel(),
		};
	},
	beforeDestroy() {
		this.$Bus.$off("changeCollectionStatus");
	},

	mounted() {
		//通知父页面iframe内容加载完毕
		let targetWindow = window.opener || window.parent;
		targetWindow.postMessage({ type: "loadComplete" }, window.origin);
		that = this;
		this.init();

		//ifr监听
		window.addEventListener("message", function (event) {
			console.log(event, "监听到了");
			if (event.origin === window.origin) {
				let data = event.data;
				if (data.type === "openDetail") {
					that.openDetail(data.value);
				}
			}
		});

		let cur = window.$nuxt.$store.state.currency;
		if (cur) {
			this.baseModel.currencyModel = cur;
		}

		//子组件返回监听
		this.$Bus.$on("changeCollectionStatus", (res) => {
			this.listModel.list.map((item) => {
				if (item.id == res.id) {
					item.isCollection = res.isCollection; //测试：!item.isCollection;
				}
			});
		});

		//根据路由参数跳转至详情页
		if (this.$route.query.productRouting) {
			this.openDetail(this.$route.query.productRouting);
		}
	},
	methods: {
		back() {
			this.pageStatus = 1;
			this.init();
		},
		openDetail(routing) {
			this.productRouting = routing;
			this.pageStatus = 2;
		},
		init() {
			if (this.isMobile) {
				//移动端 初始化则隐藏
				this.baseModel.isShowLeft = false;
				this.queryModel.pageSize = 8;
			} else {
				this.queryModel.pageSize = 12;
			}
			let { designerString, idlist } = this.$route.query;
			if (designerString) {
				if (this.queryModel.designerIdList.length == 0) {
					this.queryModel.designerIdList = designerString.split(",");
				}
			}
			if (idlist) {
				if (this.queryModel.tagsIds.length == 0) {
					this.queryModel.tagsIds = idlist.split(",");
					this.baseModel.leftRequestModel.tagsIds = this.queryModel.tagsIds;
				}
			}

			this.getProductGetAllTags();
			this.getTagsGetTagsProductCount();
			//this.getProductGetProductList();
			this.initSelect();

			//baseModel.isShowLeft
			console.log("我是移动端", this.isMobile);
		},
		//列表
		getProductGetProductList() {
			let res = this.resData3;
			if (res) {
				if (res.records.length == 0 && res.total == 0) {
					this.listModel.isMove = false;
					this.baseModel.isLoading = true;
					this.$forceUpdate();
					return;
				}

				res.records.map((item) => {
					let iArr = JSON.parse(item.imgJson);
					if (iArr?.length > 0) {
						item.imgJson = iArr[0];
					}

					item.title = item.productName;
					item.price = item.unitPrice;
					item.router = item.productRouting;
					if (item?.user) {
						item.labelModel = {
							title: item.user.firstName + item.user.lastName,
						};
					}
				});

				let listLenth = res.total;
				if (listLenth > this.queryModel.pageSize * this.queryModel.page) {
					//需要展示更多按钮
					this.listModel.isMove = true;
				} else {
					this.listModel.isMove = false;
				}

				this.listModel.list = this.listModel.list.concat(res.records);
				this.listModel.total = res.total;
				this.listModel.pageSize = res.size;
				this.listModel.page = res.current;

				this.baseModel.isLoading = true;
				this.$forceUpdate();
			}
		},
		//左侧上面2个
		getProductGetAllTags() {
			let res = this.resData;
			if (res) {
				if (res?.priceInterval) {
					this.baseModel.model.list[0].min = res.priceInterval.minPrice;
					this.baseModel.model.list[0].max = res.priceInterval.maxPrice;
					this.baseModel.model.list[0].value = [res.priceInterval.minPrice, res.priceInterval.maxPrice];
					this.baseModel.model.list[0].step = 0.01;
					this.baseModel.model.oldList[0] = this.baseModel.model.list[0];
				}
				if (res?.designer) {
					let childArray = [];
					let sum = 0;
					res.designer.map((item) => {
						let obj = {
							//...item,
							label: item.firstName + item.lastName,
							value: item.userIdStr,
							checked: false,
						};
						let { designerString } = this.$route.query;
						if (designerString) {
							let d = designerString.split(",");
							if (d.length > 0) {
								d.map((i) => {
									if (i == item.userIdStr) {
										obj.checked = true;
										sum += 1;
									}
								});
							}
						}

						childArray.push(obj);
					});
					if (sum > 0) {
						this.baseModel.model.activeNames = [0, 1, 2, 3];
					}
					this.baseModel.model.list[1].childList = childArray;
					this.baseModel.model.oldList[1] = this.baseModel.model.list[1];
				}

				this.$forceUpdate();
			}
		},
		//左侧下面2个
		getTagsGetTagsProductCount() {
			let res = this.resData2;
			if (res) {
				let list = this.baseModel.model.oldList;
				res.map((item) => {
					item.childList.map((j) => {
						if (j.checked == null) {
							j.checked = false;
						}
						j.label = j.name;
						j.value = j.id;
						j.count = j.productCount;
					});
					item.type = "checkbox";
					item.title = item.name;
				});
				this.baseModel.model.list = list.concat(res);
				this.$forceUpdate();
			}
		},
		changeSlider(e) {
			if (e?.length >= 2) {
				let e0 = (e[0] / this.baseModel.currencyModel.rate).toFixed(2);
				let e1 = (e[1] / this.baseModel.currencyModel.rate).toFixed(2);
				this.queryModel.priceStart = parseFloat(e0);
				this.queryModel.priceEnd = parseFloat(e1);
				this.initSelect();
			}
		},
		changeCheckbox(i, j) {
			if (i.title === "DESIGNERS") {
				let designerIdList = [];
				i.childList.map((item) => {
					if (item.checked) {
						designerIdList.push(item.value);
					}
				});
				this.baseModel.leftRequestModel.designerIdList = designerIdList;
				this.queryModel.designerIdList = designerIdList;

				this.tagsGetTagsProductCountFunc();
			} else if (i.title === "STYLE" || i.title === "TOPIC") {
				let tagsIds = [];
				this.baseModel.model.list.map((item) => {
					if (item.title === "STYLE" || item.title === "TOPIC") {
						item.childList.map((j) => {
							if (j.checked) {
								tagsIds.push(j.value);
							}
						});
					}
				});
				this.baseModel.leftRequestModel.tagsIds = tagsIds;
				this.queryModel.tagsIds = tagsIds;
				console.log("我都行", this.baseModel.leftRequestModel, this.queryModel);
				this.tagsGetTagsProductCountFunc();
			}
			if (j.checked) {
				this.baseModel.historyJson.push(j);
			} else {
				this.baseModel.historyJson.map((item, index) => {
					if (item.value == j.value) {
						this.baseModel.historyJson.splice(index, 1);
					}
				});
			}
		},
		//勾选后执行接口--下面2个
		tagsGetTagsProductCountFunc() {
			this.baseModel.lyFoldCheckboxLoading = true;
			console.log("请求',", this.baseModel.leftRequestModel);

			tagsGetTagsProductCount(this.baseModel.leftRequestModel).then((res) => {
				this.baseModel.lyFoldCheckboxLoading = false;
				let sum = 0;
				if (this.baseModel.leftRequestModel?.tagsIds?.length > 0) {
					this.baseModel.leftRequestModel.tagsIds.map((i) => {
						res.data.map((j) => {
							if (j.childList?.length > 0) {
								sum += 1;
							}
							j.childList.map((z) => {
								if (z.id === i) {
									z.checked = true;
								}
							});
						});
					});
				}
				if (sum == 0) {
					this.baseModel.leftRequestModel.tagsIds = []; //没有结果集 则清空搜索条件
					this.queryModel.tagsIds = [];
					//历史记录
					let hArr = [];
					this.baseModel.historyJson.map((i) => {
						this.baseModel.leftRequestModel.designerIdList.map((j) => {
							if (i.value == j) {
								hArr.push(i);
							}
						});
					});
					this.baseModel.historyJson = hArr;
				}

				this.resData2 = res.data;
				this.getTagsGetTagsProductCount(); //更新下面2个
				this.initSelect();
			});
		},
		//列表接口
		productGetProductListFunc() {
			//this.queryModel.tagsIds = this.baseModel.leftRequestModel.tagsIds;
			this.queryModel.userId = this.userId;
			this.listModel.loading = true;
			if (this.queryModel.keyword == "") {
				this.queryModel.keyword = undefined;
			}
			console.log("茉莉北极光", this.queryModel);
			productGetProductList(this.queryModel).then((res) => {
				this.listModel.loading = false;
				if (res) {
					this.resData3 = res.data;
					this.baseModel.model.list.map((i) => {
						if (i.title === "STYLE" || i.title === "TOPIC") {
							i.childList.map((j) => {
								this.queryModel.tagsIds.map((n) => {
									if (n == j.id) {
										j.checked = true;
									}
								});
							});
						}
					});
					if (this.$refs.lyFoldCheckbox) {
						this.$refs.lyFoldCheckbox.refresh();
					}
					this.getProductGetProductList();
				}
			});
		},
		//列表更多
		lyCardListMove(e) {
			this.queryModel.page += 1;
			this.productGetProductListFunc();
		},
		//初始化查询
		initSelect() {
			that.listModel = new listModel();
			that.queryModel.page = 1;
			that.listModel.keyword = that.queryModel.keyword;
			that.productGetProductListFunc(); //更新列表
		},
		//搜索关键词
		keywordChange: Common.fd((e) => {
			that.initSelect();
		}, 500),
		//是否显示左侧
		isShowLeft() {
			this.$nextTick(() => window.scrollTo({ top: 0, behavior: "smooth" }));
			this.baseModel.isShowLeft = !this.baseModel.isShowLeft;
		},
		//排序下拉
		dropdownCommand(e) {
			console.log(this.selectModel.sortOptions, e);
			this.selectModel.sortOptions.map((item) => {
				if (item.value == e) {
					this.baseModel.dropdownCommandValue = item.label;
				}
			});
			this.queryModel.sort = parseInt(e);
			this.initSelect();
		},
		//排序下拉手机端
		dropdownCommandM(e) {
			this.selectModel.sortOptions.map((item) => {
				if (item.label == e) {
					this.queryModel.sort = item.value;
				}
			});
			this.initSelect();
		},
		//历史记录删除
		deleteFunc(e) {
			this.baseModel.leftRequestModel.designerIdList.map((item, index) => {
				if (item == e.value) {
					this.baseModel.leftRequestModel.designerIdList.splice(index, 1);
				}
			});
			this.baseModel.leftRequestModel.tagsIds.map((item, index) => {
				if (item == e.value) {
					this.baseModel.leftRequestModel.tagsIds.splice(index, 1);
				}
			});
			let l = this.baseModel.model.list;
			if (l?.length > 1) {
				l[1].childList.map((item, index) => {
					if (item.label == e.label && item.value == e.value) {
						item.checked = false;
					}
				});
			}

			this.baseModel.historyJson.map((item, index) => {
				if (item.value == e.value) {
					this.baseModel.historyJson.splice(index, 1);
				}
			});

			this.tagsGetTagsProductCountFunc();
			this.$forceUpdate();
		},
		clearAll() {
			this.listModel = new listModel();
			this.baseModel.historyJson = new baseModel().historyJson;
			this.baseModel.leftRequestModel = new baseModel().leftRequestModel;
			/* this.baseModel.model.list[1].childList.map((item) => {
				item.checked = false;
			}); */
			//.childList
			this.baseModel.model.list.map((i, iIndex) => {
				if (iIndex > 0) {
					i.childList.map((j) => {
						j.checked = false;
					});
				}
			});

			console.log("脑壳痛", this.baseModel.model);
			/* 		this.getProductGetAllTags();
			this.getTagsGetTagsProductCount(); */
			this.queryModel = new queryModel();
			this.tagsGetTagsProductCountFunc();
			//this.initSelect();

			this.$forceUpdate();
		},
		//右侧是否显示隐藏
		getRight() {
			/* !baseModel.isShowLeft && isMobile */
			if (this.isMobile) {
				return !this.baseModel.isShowLeft;
			} else {
				return true;
			}
		},
		//收藏回调
		collectFunc(item, e) {
			console.log("我草", item, e);
			//	that.initSelect();
		},
	},
};
</script>
<style scoped lang="scss">
@mixin centerCenter {
	display: flex;
	justify-content: center;
	align-items: center;
}
$themeColor: #d24600;
$imgServer: "https://static-oss.gs-souvenir.com/web/";
.stylistList {
	position: relative;
	margin: 0 auto;
	height: 100%;
	max-width: 1600px;
	display: flex;
	justify-content: center;
	font-family: Calibri, Calibri;
	flex-wrap: wrap;
	.main {
		display: flex;
		width: 100%;
		padding: 20px;
		.mainLeft {
			flex: 2.5;
			max-width: 200px;
			.mainLeftNav {
				height: 80px;
				@include centerCenter;
				justify-content: flex-start;
				color: #666;
				font-size: 1.3em;
				.key {
					font-weight: 700;
				}
				.fuhao {
					padding: 0 2px;
				}
			}
			.filters {
				position: relative;
				margin-bottom: 10px;
				.p1 {
					color: #333333;
					font-size: 1.6em;
					margin-right: 10px;
					font-weight: 700;
				}
				.p2 {
					font-size: 1em;
					color: #666;
				}
			}
			.filtersLeft {
				position: sticky;
				top: 15px;
				z-index: 111;
				background: #fff;
			}
			.history {
				margin-bottom: 18px;
				.li {
					color: $themeColor;
					background: rgba(210, 70, 0, 0.12);
					padding: 5px 10px;
					border-radius: 0;
					margin-bottom: 5px;
					font-size: 1.3em;
					display: inline-flex;
					margin-right: 10px;
					cursor: pointer;
					font-weight: 700;
					i {
						font-size: 1.1em;
						margin-left: 5px;
						cursor: pointer;
					}
					.xxx {
						color: rgba(102, 102, 102, 0.8);
					}
					.xxxDel {
						font-size: 1em;
					}
				}
				.liClose {
					font-size: 1.3em;
					display: flex;
					align-items: center;
					cursor: pointer;
					font-weight: 700;
				}
			}
		}
		.mainRight {
			flex: 7.5;
			font-size: 18px;
			padding-left: 60px;
			overflow: auto;
			height: 95vh;
			//margin-bottom: 40px;
			.tableList {
				::v-deep .lyCardList .list .li .rowFont .rowOne {
					-webkit-line-clamp: 2;
				}
			}
			.mainLeftNav {
				display: flex;
				padding: 20px 0px 17px 0;
				position: sticky;
				top: -1px;
				z-index: 111;
				background: #fff;
				.l {
					flex: 3;
				}
				.r {
					flex: 7;
					display: flex;
					align-items: center;
					justify-content: flex-end;
					.p1 {
						font-size: 1.2em;
						color: #333;
						font-weight: 400;
					}
					.pRight {
						margin-right: 32px;
						cursor: pointer;
					}
					.icon {
						color: #333;
						font-weight: 700;
						cursor: pointer;
					}
					.icon1 {
						font-size: 18px;
						margin-right: 12px;
					}
					.icon2 {
						font-size: 9px;
						margin-left: 4px;
						position: relative;
						top: -2px;
					}
				}
			}
			@include respond-to(mb) {
				font-size: unset;
			}
		}
		.mainRightOff {
			padding-left: 0px;
		}
	}
}
@include respond-to(mb) {
	.stylistList {
		width: 100%;
		padding: 0;
		width: 100%;
		.main {
			width: 100%;
			padding: 0px 12px 10px 12px;
			display: inline-block;
			.mainLeft {
				max-width: none;
				min-height: calc(100vh - 75px);
				.filtersLeft {
					position: sticky;
					top: 0; //15px;
					z-index: 111;
					background: #fff;
					.exit {
						padding-top: 8px; //15px;
						b {
							color: #666;
							cursor: pointer;
							font-size: 16px;
						}
					}
				}
				.sortByM {
					::v-deep .el-collapse-item__header {
						color: #303133;
						cursor: pointer;
						font-weight: 700;
						font-size: 1.4em;
					}
					::v-deep .el-collapse-item__arrow {
						-webkit-transform: rotate(90deg);
						transform: rotate(90deg);
						font-weight: 700;
					}
					::v-deep .el-collapse-item__arrow.is-active {
						-webkit-transform: rotate(270deg);
						transform: rotate(270deg);
					}
					::v-deep .el-radio {
						width: 100%;
						margin-bottom: 22px;
						font-size: 18px;
						display: flex;
						align-items: center;
					}
					::v-deep .el-collapse-item__content {
						padding-bottom: 0;
						padding-top: 10px;
					}
					::v-deep .el-radio__input.is-checked .el-radio__inner {
						background-color: $themeColor;
						border-color: $themeColor;
					}
					::v-deep .el-radio__label {
						font-size: 16px;
					}
					::v-deep .el-radio__input.is-checked + .el-radio__label {
						color: $themeColor;
					}
					::v-deep .el-collapse {
						border-bottom: 0;
					}
				}
				.filters {
					font-size: 1.4em;
					margin-top: 10px;
					.p1 {
						font-size: 1.5em;
					}
					.pRight {
						position: absolute;
						right: 0;
						top: 0.6em;
						i {
							color: #333333;
						}
					}
				}
				.history {
					.li {
						.xxx {
							font-size: 0.7em;
							padding-top: 3px;
						}
						.xxxDel {
							font-size: 1.2em;
						}
					}
				}
			}
			::v-deep .el-checkbox__label {
				font-size: 1em;
			}
			.mainRight {
				padding-left: 0;
				margin-bottom: 40px;
				margin-top: 20px;
				::v-deep .el-input-group {
					width: calc(50vw - 0px);
				}
				.mainLeftNav {
					padding: 20px 0px 20px 0;
					::v-deep .el-input__inner {
						font-size: 1.2em;
					}
					.l {
						::v-deep .el-input-group__append,
						.el-input-group__prepend {
							padding: 0 15px 0 10px;
						}
					}
					.r {
						.icon1 {
							font-size: 0.8em;
							margin-right: 7px;
						}
						.pRight {
							margin-right: 0;
						}
						.p1 {
							font-size: 1.1em;
							color: #333;
							font-weight: 700;
							opacity: 0.7;
						}
					}
				}
				.tableList {
				}
			}
		}
	}
}
</style>
<style lang="scss">
.stylistList {
	.main {
		.mainRight {
			.mainLeftNav {
				.el-input-group__append,
				.el-input-group__prepend {
					background: unset;
					border: 1px solid #ebebeb;
					border-left: 0;
					border-radius: 0 6px 6px 0;
				}
				.el-button {
					padding: 12px;
				}

				.el-input__inner {
					height: 36px;
					line-height: 36px;
					font-size: 1.1em;
					border: 1px solid #ebebeb;
					border-right: 0;
					border-radius: 6px 0 0 6px;
				}
				.el-dropdown {
					color: #333333;
					font-size: 1.1em;
					margin-right: 10px;
				}
			}
		}
		.el-checkbox__label {
			font-size: 16px;
		}

		.el-checkbox__inner {
			width: 16px;
			height: 16px;
		}
	}
}
</style>
