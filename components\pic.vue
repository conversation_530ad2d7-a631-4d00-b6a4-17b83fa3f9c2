<template>
    <img :src="src || '/img/default.png'" :alt="alt || title || imgName" :title="title || alt || imgName" @click="$emit('click')" draggable="false" decoding="async" loading="lazy" />
</template>


<script>
export default {
    name: "pic",
    props: {
        src: {
            type: String,
            default: ''
        },
        alt: {
            type: String,
            default: ''
        },
        title: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            imgName: ''
        }
    },
    created() {
        if (this.src && !this.alt && !this.title) {
            let srcArray = this.src?.split('?')[0].split('/') || [];
            this.imgName = srcArray[srcArray.length - 1].split('.')[0].replace(/^[0-9]+/, '').replace(/_\d{8}[A-Za-z0-9]{6}$/, '').replace(/-|_|<br \/>/g, ' ');
        }
    }
};
</script>
