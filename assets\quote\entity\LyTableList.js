//基础类
export class baseModel {
	domId = undefined; //组件唯一标识
	page = 1; //当前页
	total = undefined; //总页数
	time = undefined; //定时任务
	model = {
		//数据源
		list: [
			{
				img: process.env.imgServer + "quoteManage/20240918/shiny_gold_20240918xEHTmj.png",
				isHot: true,
			},
			{ img: process.env.imgServer + "quoteManage/20240918/silver_plating_20240918zPKCDi.png", isHot: true },
			{ img: process.env.imgServer + "quoteManage/20240918/antique_gold_20240918NmQHeH.png" },
			{ img: process.env.imgServer + "quoteManage/20240918/antique_silver_20240918XDzSnK.png" },
			{ img: process.env.imgServer + "quoteManage/20240918/black_nickel_2024091883FTFt.png", isHot: true },
		],
		radioIndex: -1, //单选
		radioChildIndex: -1, //子级单选
	};
	//轮播图
	carouselModel = {
		dropTabs: 0, //点点高亮
		refDom: {
			//总盒子
			style: {
				display: "flex",
				position: "relative",
				width: undefined,
				left: "0px",
			},
		},
		refLiDom: {
			//第一个子盒子
			domId: undefined,
			style: {
				width: 0,
				height: 0,
			},
		},
		initCss: {},
	};
	priceTextCss = {
		normaltext: "font-size: 14px;", //修改价格行内样式
	};
	touch = {
		startX: 0,
		endX: 0,
	};
	freeImg = "http://customed-center.oss-us-west-1.aliyuncs.com/web/public/picStore/20231129/icon-free_20632WAdnA.png";
}
