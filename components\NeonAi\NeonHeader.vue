<template>
	<header class="neonHeader">
        <template v-if="device!=='mb'">
            <div class="left">
                <b class="icon-hanbaobao" @click="showNavBar"></b>
                <nuxt-link to="/">
                    <pic :src="proSystem.logo" :alt="$store.state.logoAltTitle || $store.state.proName" />
                </nuxt-link>
                <span class="greyText" style="margin-left: 4px;">AI Vibe</span>
            </div>
            <div class="center">
                <div class="tab">
                    <div class="tabItem greyText" v-for="(item, index) in tabList" :class="{ active: tabIndex === index }" @click="changeTab(index)">
                        {{ item.value }}
                    </div>
                </div>
            </div>
            <div class="right">
                <div flex center class="system-box">
                    <div class="user-box options-box" v-if="isLogin">
                        <b class="icon-jxsht-ybp-yh"></b>
                        <div class="options">
                            <strong class="user-title"> {{ lang.hi }}, {{ $store.state.userInfo.firstName }} </strong>
                            <div class="user-sub-title">{{ lang.track }}</div>
                            <a href="/user/account/orders" :select="path == '/user/account/orders'">
                                <b class="icon-bps-MyOrder"></b>
                                {{ lang.orders }}
                            </a>
                            <a href="/user/account/favorite" :select="path == '/user/account/favorite'">
                                <b class="icon-shoucang"></b>
                                {{ lang.favorite }}
                            </a>
                            <a href="/user/account/profile" :select="path == '/user/account/profile'">
                                <b class="icon-bps-MyProfile"></b>
                                {{ lang.profile }}
                            </a>
                            <a href="/user/account/address" :select="path == '/user/account/address'">
                                <b class="icon-bps-MyAddress"></b>
                                {{ lang.address }}
                            </a>
                            <a href="/user/account/coupons-vouchers" :select="path == '/user/account/coupons-vouchers'">
                                <b class="icon-jxsqt-cpy-jgpy"></b>
                                {{ lang.coupons }}
                            </a>
                            <button class="logout" outline @click="logOut">{{ lang.logOut }}</button>
                        </div>
                    </div>
                    <div v-else pointer class="login-box" :title="lang.login" @click="$store.commit('setLogin', 'login')"><b class="icon-a-Login1"></b> <span>Sign In</span></div>
                    <div class="lang-box options-box" v-click-outside="() => (showLang = false)">
                        <div flex center @click.stop="showLang = !showLang">
                            <pic pointer class="lang-img" :src="$store.state.language.img" :alt="$store.state.language.countryCode" />
                            <span>{{ $store.state.language.language || "en" }}</span>
                        </div>
                        <Language class="options" :hidden="!showLang"></Language>
                    </div>
                    <div class="ccy-box options-box">
                        <span>{{ $store.state.currency.symbol || "$" }}</span>
                        <span>{{ $store.state.currency.code || "USD" }}</span>
                        <div scrollbar class="options">
                            <span v-for="i in $store.state.currencyList" :select="i.id == $store.state.currency.id" @click="$store.commit('setCurrency', i)">{{ i.code }}</span>
                        </div>
                    </div>
                    <nuxt-link to="/cart" class="cart-box">
                        <b class="icon-a-Shoppingcart">
                            <label v-show="$store.state.cartQuantity">{{ $store.state.cartQuantity }}</label>
                        </b>
                    </nuxt-link>
                </div>
            </div>
        </template>
        <template v-else>
            <div class="left">
                <b class="icon-hanbaobao" @click="showNavBar"></b>
            </div>
            <div class="center">
                <nuxt-link to="/">
                    <pic :src="proSystem.logo" :alt="$store.state.logoAltTitle || $store.state.proName" />
                </nuxt-link>
                <span class="greyText" style="margin-left: .5em;">AI Vibe</span>
            </div>
            <div class="right">
                <div flex center class="system-box">
                    <div class="user-box options-box" v-if="isLogin">
                        <b class="icon-jxsht-ybp-yh"></b>
                        <div class="options">
                            <strong class="user-title"> {{ lang.hi }}, {{ $store.state.userInfo.firstName }} </strong>
                            <div class="user-sub-title">{{ lang.track }}</div>
                            <a href="/user/account/orders" :select="path == '/user/account/orders'">
                                <b class="icon-bps-MyOrder"></b>
                                {{ lang.orders }}
                            </a>
                            <a href="/user/account/favorite" :select="path == '/user/account/favorite'">
                                <b class="icon-shoucang"></b>
                                {{ lang.favorite }}
                            </a>
                            <a href="/user/account/profile" :select="path == '/user/account/profile'">
                                <b class="icon-bps-MyProfile"></b>
                                {{ lang.profile }}
                            </a>
                            <a href="/user/account/address" :select="path == '/user/account/address'">
                                <b class="icon-bps-MyAddress"></b>
                                {{ lang.address }}
                            </a>
                            <a href="/user/account/coupons-vouchers" :select="path == '/user/account/coupons-vouchers'">
                                <b class="icon-jxsqt-cpy-jgpy"></b>
                                {{ lang.coupons }}
                            </a>
                            <button class="logout" outline @click="logOut">{{ lang.logOut }}</button>
                        </div>
                    </div>
                    <div v-else pointer class="login-box" :title="lang.login" @click="$store.commit('setLogin', 'login')"><b class="icon-a-Login1"></b></div>
                    <nuxt-link to="/cart" class="cart-box">
                        <b class="icon-a-Shoppingcart"></b>
                    </nuxt-link>
                </div>
            </div>
        </template>
	</header>
</template>

<script>
import Language from "@/components/Language.vue";
import Pic from "@/components/pic.vue";

export default {
	props: {
        tabList:{
            type: Array
        },
		tabIndex: {
			type: Number,
		},
	},
	components: { Pic, Language },
	data() {
		return {
			showLang: false
		};
	},
	computed: {
		isLogin() {
			return this.$store.getters.isLogin;
		},
		lang() {
			return this.$store.getters.lang.layout || {};
		},
		path() {
			return this.$store.state.pagePath;
		},
		proSystem() {
			return this.$store.state.proSystem;
		},
        device(){
            return this.$store.state.device
        }
	},
	methods: {
        showNavBar(){
            this.$emit("toggleNav",true)
        },
		changeTab(index) {
			this.$emit("update:tabIndex", index);
		},
		logOut() {
			this.$toast.show(this.lang.confirmLogOut, {
				containerClass: "toast-action-box",
				className: "toast-action",
				duration: null, // 不自动关闭弹窗
				action: [
					{
						text: this.lang.sure,
						onClick: (e, toastObject) => {
							this.$store.commit("logOut");
							toastObject.goAway(0);
						},
					},
					{
						text: this.lang.cancel,
						onClick: (e, toastObject) => toastObject.goAway(0), // 关闭弹窗
					},
				],
			});
		},
	},
};
</script>

<style lang="scss" scoped>
.greyText {
	color: #999999;
}

.options-box {
	display: flex;
	position: relative;
	align-items: center;

	&:not(.lang-box) .options {
		display: none;
	}

	&[select] > *:not(.options),
	[select] {
		font-weight: bold;
		color: $color-primary;
	}

	&:hover {
		&:not(.system-box > .options-box) > *:not(.options),
		a:hover {
			color: $color-primary;
		}

		> b.icon-Down::before {
			transform: rotate(-180deg);
		}

		> .options {
			display: grid;
		}
	}

	a {
		padding: 0.5em 1em;

		> span[style] {
			color: white;
			font-size: 10px;
			padding: 1px 6px;
			margin-left: 0.5em;
			display: inline-block;
			border-radius: 0 1em 1em 1em;
		}
	}

	.options {
		padding: 1em;
		font-size: 1rem;
		text-align: left;
		width: max-content;
		border-radius: 5px;
		grid-row-gap: 0.5em;
		grid-column-gap: 1em;
		color: $text-primary;
		background-color: white;
		transform: translate(-50%);
		box-shadow: 0 0 10px -5px $bg-mask;
		position: absolute;
		z-index: 99;
		left: 50%;
		top: 95%;

        @include respond-to(mb){
            transform: translate(-88%);
        }

		.options {
			transform: none;
			left: 100%;
			top: 0.5em;
		}

		&::before {
			content: "";
			width: 10px;
			height: 10px;
			background: white;
			box-shadow: -3px -3px 6px -4px $bg-mask;
			transform: scale(1, 1.3) rotate(45deg);
			position: absolute;
			left: calc(50% - 5px);
			z-index: -1;
			top: -5px;

            @include respond-to(mb){
                left: calc(88% - 5px);
                font-size: 1.59em;
            }
		}
	}
}

.neonHeader {
	position: sticky;
	top: 0;
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	align-items: center;
	color: #ffffff;
	height: 3.75em;
	margin: 0.3em;
	background-color: #1c252d;
	border-radius: 0.25rem;
	padding: 0 1.88em;
	z-index: 100;

    @include respond-to(mb){
        padding: 0 1em;
        grid-template-columns: 1fr 2fr 1fr;
        margin: 0;
    }

	.left {
		display: flex;
		align-items: center;
		font-size: 0.88em;

		b {
			margin-right: 0.5em;
			font-size: 2em;
			cursor: pointer;
		}

		img {
			max-width: 13.31em;
		}
	}

	.center {
		display: flex;
        align-items: center;
		justify-content: center;
        white-space: nowrap;

		.tab {
			display: flex;
			justify-content: center;
			align-items: center;
			background-color: #0f1115;
			border-radius: 1.88em;
			padding: 0 0.25em;
			height: 3em;

			.tabItem {
				display: flex;
				align-items: center;
				padding: 0 1em;
				cursor: pointer;
				height: 85%;
				margin-right: 0.5em;
				border-radius: inherit;
				background-color: transparent;
				transition: all 0.3s;
				font-size: 0.88em;

				&:last-child {
					margin-right: 0;
				}

				&:hover {
					color: #ffffff;
				}

				&.active {
					color: #ffffff;
					background-color: #3c4c59;
				}
			}
		}
	}

	.right {
		display: flex;
		justify-content: flex-end;
		font-size: 0.88em;
		height: 100%;

		.system-box {
			height: 100%;

			b {
				margin-right: 0.2em;
				font-size: 1.25em;
                @include respond-to(mb){
                    font-size: 1.5em;
                }
			}

			& > div {
				display: flex;
				align-items: center;
				margin: 0 0.5em;
			}

			.options-box {
				position: relative;
				height: 100%;

				.options {
					position: absolute;
					top: 90%;
				}
			}

			.lang-box {
				img {
					width: 1.13em;
					height: 1.13em;
					object-fit: cover;
					margin-right: 0.2em;
				}
			}

			.cart-box {
				b {
					position: relative;

					label {
						height: 18px;
						color: white;
						font-size: 10px;
						min-width: 18px;
						line-height: 18px;
						text-align: center;
						border-radius: 50%;
						background: $color-red;
						position: absolute;
						right: -12px;
						top: -5px;
					}
				}

				> label {
					font-size: calc(1em - 2px);
				}
			}

			.ccy-box .options {
				max-height: 16em;

				> span {
					cursor: pointer;
					padding: 0.5em 1em;
				}
			}

			.lang-box {
				text-transform: uppercase;

				.lang-img {
					width: 1.35rem;
					height: 1.35rem;
					border-radius: 50%;
				}

				.options {
					transform: none;
					grid-row-gap: 0;
					right: -1em;
					left: auto;
				}
			}
		}
	}
}
</style>
