<template>
	<div id="custom-chenille-patches" class="quoteWrap customChenillePatchesNew">
		<Preloader v-if="isLoading"></Preloader>
		<template v-else>
			<article class="content">
				<QuoteNav :pid="pid" :title="lang.patchType"></QuoteNav>
				<div class="header">
					<h1>{{ lang.chenillePatches.h3 }}</h1>
				</div>
				<div class="leftArea" id="leftArea">
					<div v-for="(item, index) in filterShowGeneralData" :key="item.id" class="kk" :class="{ type1: picDialog }">
						<PublicStep v-if="item.paramName == 'quoteCategory'" :config="allStepConfig[item.paramName]" :selectedData="selectedData" :stepData="item" :maskName.sync="maskName" @clickFun="selectQuoteParamsFunc(item, $event)"></PublicStep>

						<PublicStep v-if="item.paramName == 'Intended Use'" :config="allStepConfig[item.paramName]" :selectedData="selectedData" :stepData="item" :maskName.sync="maskName" @clickFun="selectFoldType(item, $event)" @clickExtendStepFun="clickExtendStepFun(item, $event)"></PublicStep>

						<PinsSizeSelect ref="pinsSizeSelect" v-if="item.paramName == 'Metal Patches Size'" :findSizeModel="findSizeModel" :generalData="generalData" :shoppingModel="shoppingModel.sizeModel" :selectedData="selectedData" :stepData="item" :maskName="maskName" :smallTitle="lang.pins.stepSizeTitleMetal" :sizeImgP1="lang.pins.p2" @clickFun="selectQuoteParamsFunc(item, $event)" @closeMask="closeMask" @showMaskFn="showMaskFn"></PinsSizeSelect>

						<PublicStep v-if="item.paramName == 'Material'" :config="allStepConfig[item.paramName]" :selectedData="selectedData" :stepData="item" :maskName.sync="maskName" @clickFun="selectQuoteParamsFunc(item, $event)"></PublicStep>

						<PublicStep v-if="item.paramName == 'Metal Process'" :config="allStepConfig[item.paramName]" :selectedData="selectedData" :stepData="item" :maskName.sync="maskName" @clickFun="selectQuoteParamsFunc(item, $event)" @showMaskFn="showMaskFn"></PublicStep>

						<PublicStep v-if="item.paramName == 'Plating/Finish（copperl）'" :config="allStepConfig[item.paramName]" :selectedData="selectedData" :stepData="item" :maskName.sync="maskName" @clickFun="selectQuoteParamsFunc(item, $event)"></PublicStep>

						<PublicStep v-if="item.paramName == 'Plating/Finish（Aluminum）'" :config="allStepConfig[item.paramName]" :selectedData="selectedData" :stepData="item" :maskName.sync="maskName" @clickFun="selectQuoteParamsFunc(item, $event)"></PublicStep>

						<PublicStep v-if="item.paramName == 'Plating/Finish（stainless steel）'" :config="allStepConfig[item.paramName]" :selectedData="selectedData" :stepData="item" :maskName.sync="maskName" @clickFun="selectQuoteParamsFunc(item, $event)"></PublicStep>

						<PublicStep v-if="item.paramName == 'Plating/Finish（废除）'" :config="allStepConfig[item.paramName]" :selectedData="selectedData" :stepData="item" :maskName.sync="maskName" @clickFun="selectQuoteParamsFunc(item, $event)"></PublicStep>

						<template v-if="item.paramName === 'Quantity'">
							<StepQty class="step-item step-qty" :class="{ mask: maskName === item.paramName }" :id="item.paramName" :index="item.customIndex" :itemData="item" :customQty.sync="customQty" :restaurants="restaurants" @closeMask="closeMask" @showMaskFn="showMaskFn" @calcPrice="debounceCalcPrice" :key="index"></StepQty>
						</template>

						<template v-if="item.paramName === 'Turnaround Time'">
							<StepTime class="step-item step-date" :class="{ mask: maskName === item.paramName }" :id="item.paramName" :cateData="cateData" :pid="pid" :customQty="customQty" :index="item.customIndex" :freeText="lang.pins.timeNo" :itemData="item" :priceInfo="priceInfo" :previewMode="previewMode" :selectedParams="selectedData" @showMaskFn="showMaskFn" @selectItem="selectQuoteParams($event.item, $event.citem)" @closeMask="closeMask" :key="index"></StepTime>
						</template>

						<template v-if="item.paramName == 'Upload Artwork & Comments'">
							<StepUpload class="step-item step-upload" :class="{ mask: maskName === item.paramName }" :id="item.paramName" :index="item.customIndex" :itemData="item" :uploadList.sync="uploadArtworkList" :remark.sync="remark" :isUpload.sync="isUpload" @closeMask="closeMask" @showMaskFn="showMaskFn" :key="index"></StepUpload>
						</template>

						<PublicStep v-if="item.paramName == 'Assembly Method'" class="assemblyMethod" :config="allStepConfig[item.paramName]" :selectedData="selectedData" :stepData="item" :maskName.sync="maskName" @clickFun="selectFoldType(item, $event)" @clickExtendStepFun="clickExtendStepFun(item, $event)" @viewVideo="viewAssemblyVideo"></PublicStep>

						<PublicStep v-if="item.paramName == 'Metal Patches Shape'" class="shape" :config="allStepConfig[item.paramName]" :selectedData="selectedData" :stepData="item" :maskName.sync="maskName" @clickFun="selectQuoteParams(item, $event)" @showLessBtn="showLessBtn(item.paramName)"> </PublicStep>

						<PublicStep v-if="item.paramName == 'Plating/Finish'" :config="allStepConfig[item.paramName]" :selectedData="selectedData" :stepData="item" :maskName.sync="maskName" @clickFun="selectQuoteParams(item, $event)" @showLessBtn="showLessBtn(item.paramName)"></PublicStep>
					</div>
				</div>
				<div class="rightArea" id="rightAreaCustom">
					<transition enter-active-class="animate__animated animate__faster animate__fadeInBottomLeft" leave-active-class="animate__animated animate__faster animate__fadeOutBottomLeft">
						<Detail v-show="showRightArea" class="type1" :class="{ mask: maskName }" :key="1" :absoluteImg="comeFromDZ.absoluteImg" :selectedData="selectedData" :priceInfo="priceInfo" :customQty="customQty" :textInfo="textInfo" :generalData="generalData" @toPosition="toPositionFunc" @addInquiry="debounceAddInquiry" @addCart="addCart" :selectedTextures="selectedTextures"></Detail>
					</transition>
				</div>
			</article>
			<div class="footer" id="foot">
				<Detail class="type2" :key="3" :absoluteImg="comeFromDZ.absoluteImg" :selectedData="selectedData" :priceInfo="priceInfo" :customQty="customQty" :textInfo="textInfo" :generalData="generalData" @toPosition="toPositionFunc" @addInquiry="debounceAddInquiry" @addCart="addCart" :selectedTextures="selectedTextures"></Detail>
			</div>
			<el-drawer :visible.sync="showArea" :with-header="true" custom-class="drawDialog" :size="device === 'ipad' ? '728px' : '85%'">
				<Detail class="type3" :key="2" :absoluteImg="comeFromDZ.absoluteImg" :selectedData="selectedData" :priceInfo="priceInfo" :customQty="customQty" :textInfo="textInfo" :generalData="generalData" @toPosition="toPositionFunc" @addInquiry="debounceAddInquiry" @addCart="addCart" :selectedTextures="selectedTextures"></Detail>
			</el-drawer>
			<!--    遮罩-->
			<myMask :maskName.sync="maskName"></myMask>
			<!-- 建议弹窗-->
			<RecomendDialog :showUpload="false" :commentsDialog.sync="commentsDialog" :paramValue="selectedParamsValue" @next="recomendNext" :textPlaceholder="lang.placeholder2"></RecomendDialog>
			<!--  手机，ipad端预览-->
			<PreviewBtn :showArea.sync="showArea"></PreviewBtn>
			<BaseDialog v-model="showOtoDialog" :width="device != 'mb' ? '570px' : '90%'">
				<div class="otoWrap">
					<img src="@/assets/images/oto.png" alt="" />
					<p>{{ lang.p1 }}</p>
					<h3>{{ lang.p2 }}</h3>
					<p style="color: #666666">
						{{ lang.p3 }}<br />
						{{ lang.p4 }}.
					</p>

					<div class="box">
						<p class="t1">
							{{ lang.p5 }} <br />
							{{ lang.p6 }}!
						</p>
						<a href="https://www.o2o.co/previewWeb" style="text-decoration: none">
							<el-button type="primary">{{ lang.p7 }}</el-button>
						</a>
					</div>
				</div>
			</BaseDialog>
			<VideoPreviewDialog :pic-dialog.sync="picDialog" :zoom-pic="zoomPic" :temp-type="tempType" :selected-params-value="selectedParamsValue" @qtyAndBtnConfirm="qtyAndBtnConfirm" @qtyAndBtnNext="qtyAndBtnNext" @dialogNextStep="dialogNextStep"></VideoPreviewDialog>
			<BaseDialog v-model="upgradeDialog" :width="device != 'mb' ? '640px' : '320px'">
				<Upgrade @closeUpgrade="upgradeDialog = false" :tipArr="tipArr" :typeName="cateData.cateName" @upgradeLevel="upgradeLevel"></Upgrade>
			</BaseDialog>
			<infoDialog :infoDialogVisible.sync="infoDialogVisible" @getValue="getValueFun" :otherUpload="uploadArtworkList" :recomendUpload="recomendUpload" :uploadList.sync="uploadList"></infoDialog>
			<BaseDialog v-model="noFileDialog" :width="device != 'mb' ? '485px' : '90%'" :model="false">
				<template #closeIcon>
					<div style="display: none"></div>
				</template>
				<infoUpload :infoUploadList="infoUploadList" @pushInfoList="pushInfoList" @delInfoList="delInfoList" @updateInquiry="updateInquiry" @closeInfoDialog="closeInfoDialog"> </infoUpload>
			</BaseDialog>
			<PatchesAdhesive v-if="patchesAdhesiveDialog" :patchesAdhesiveDialog.sync="patchesAdhesiveDialog" :adhesiveList="adhesiveList" :itemData="selectedParamsValue" :selectedTextures="selectedTextures" @texturesBtnNext="texturesBtnNext($event)" @update:textures="handleUpdateTextures"></PatchesAdhesive>
		</template>
	</div>
</template>
<script>
import "@/plugins/element";
import MyCheckBox from "@/components/Medals/MyCheckBox";
import MyCheckBox3 from "@/components/Medals/MyCheckBox3";
import MyCheckBox4 from "@/components/Medals/MyCheckBox4";
import BaseDialog from "@/components/Quote/BaseDialog";
import VideoPlayer from "@/components/Quote/VideoPlayer.vue";
import Upgrade from "@/components/Quote/Upgrade";
import DialogBM from "@/components/Medals/DialogBM";
import QtyAndBtn from "@/components/Medals/QtyAndBtn";
import Corner from "@/components/Medals/Corner";
import infoDialog from "@/components/Medals/infoDialog";
import QuoteNav from "@/components/Medals/QuoteNav";
import QuoteBtn from "@/components/Quote/QuoteBtn";
import quoteMixin from "@/mixins/quote";
import quoteBanChoiceMixin from "@/mixins/quoteBanChoice";
import StepTime from "@/components/Quote/StepTime.vue";
import StepUpload from "@/components/Quote/StepUpload.vue";
import StepQty from "@/components/Quote/StepQty.vue";
import RecomendDialog from "@/components/Quote/RecomendDialog.vue";
import myMask from "@/components/Quote/Mask.vue";
import Detail from "@/components/Quote/Detail.vue";
import PreviewBtn from "@/components/Quote/PreviewBtn.vue";
import Preloader from "@/components/Quote/Preloader.vue";
import PublicStep from "@/components/Quote/PublicStep/index.vue";
import { getQuoteConfig } from "@/assets/js/quote/quoteConfig";
import PinsSizeSelect from "@/components/Quote/PinsSizeSelect.vue";
import StepItem from "@/components/Quote/PublicStep/StepItem.vue";
import StepTitle from "@/components/Quote/PublicStep/StepTitle.vue";
import ToolTip from "@/components/Quote/ToolTip.vue";
import { dictModel, baseModel, intendedUseModel, findSizeModel, patchesColorCssModel } from "./entity/custom-metal-patches";
import PatchesAdhesive from "@/components/Quote/PatchesAdhesive.vue";
import PatchesColor from "@/components/Quote/PatchesColor.vue";
import { scrollToViewTop } from "@/utils/utils";
import VideoPreviewDialog from "@/components/Quote/VideoPreviewDialog.vue";
let that;
export default {
	head() {
		return {
			title: "Quote page",
		};
	},
	components: {
		VideoPreviewDialog,
		ToolTip,
		StepTitle,
		StepItem,
		PinsSizeSelect,
		PublicStep,
		Preloader,
		PreviewBtn,
		Detail,
		myMask,
		RecomendDialog,
		StepQty,
		StepUpload,
		StepTime,
		MyCheckBox,
		MyCheckBox3,
		MyCheckBox4,
		BaseDialog,
		VideoPlayer,
		DialogBM,
		QtyAndBtn,
		Corner,
		infoDialog,
		Upgrade,
		QuoteNav,
		QuoteBtn,
		PatchesAdhesive,
		PatchesColor,
	},
	computed: {
		lang() {
			return this.$store.getters.lang.quote || {};
		},
	},
	mixins: [quoteMixin, quoteBanChoiceMixin],
	data() {
		const config = getQuoteConfig.call(this, this.$route.name);
		return {
			...config,
			dictModel: new dictModel(),
			baseModel: new baseModel(),
			intendedUseModel: new intendedUseModel(),
			findSizeModel: new findSizeModel(),
			patchesColorCssModel: new patchesColorCssModel(),
			shoppingModel: {
				op: 0,
				selectedData: {},
				sizeModel: {},
			},
			sizeModel: {}, //尺寸回填
		};
	},

	/* 核心    assets/js/quote/quoteConfig.js    mixins/quote.js  */
	mounted() {
		that = this;
		this.init();

		this.initShoppingFunc(this.selectedData);

		/* this.baseModel.cssTime = setInterval(() => {
			console.log("好烦", this.filterShowGeneralData);
		}, 8000); */
	},

	beforeDestroy() {
		if (this.baseModel.cssTime) {
			clearInterval(this.baseModel.cssTime); // 清除定时器
		}
	},

	methods: {
        viewAssemblyVideo() {
            this.viewVideo(arguments[0], arguments[1], "video");
        },
		init() {
			if (this.filterShowGeneralData.length == 0) {
				setTimeout(() => {
					this.init();
				}, 1000);
			} else {
				this.baseModel.oldModel = JSON.parse(JSON.stringify(this.filterShowGeneralData)); //解决数据污染;
				this.filterShowGeneralData.map((i, iIndex) => {
					if (i.paramName === "Metal Patches Size") {
						i.childList.map((j) => {
							if (j.imageJson) {
								j.imageJsonJson = JSON.parse(j.imageJson);
							}
						});
						this.baseModel.sizeAllList = i.childList;
					}
					if (i.paramName === "Plating/Finish") {
						this.baseModel.allPlatingFinish = i.childList;
					}
					if (i.paramName === "quoteCategory") {
						i.childList.map((j) => {
							if (j.configJson) {
								j.configJsonJson = JSON.parse(j.configJson);
								j.labelColor = j.configJsonJson.tagTextColor2;
								j.labelText = j.configJsonJson.tagText_translate;
							}
						});
					}
					if (i.paramName === "Intended Use") {
						this.baseModel.intendedUseNext = this.filterShowGeneralData[iIndex + 1].paramName;
					}
				});
				this.generalData = this.filterShowGeneralData;
				this.refreshOrder();
				this.$forceUpdate();
			}
		},
		//获取弹框图片
		getImg(item) {
			if (item.priceInfo) {
				if (item.priceInfo.imagePath && item.priceInfo.imagePath != "") {
					let json = JSON.parse(item.priceInfo?.imagePath);
					if (json.length > 0) {
						return json[0].path;
					}
				}
			}
			return "";
		},

		//intendedUse业务
		intendedUseFunc(parentItem, item) {
			this.intendedUseModel.paramCode = item.paramCode;
			console.log("我进来了吗", this.dictModel.sizeList, this.baseModel.sizeAllList, item);
			this.generalData.map((n) => {
				if (n.paramType == "SIZE") {
					if (this.$refs.pinsSizeSelect?.length > 0) {
						this.$refs.pinsSizeSelect[0].initShapeImg();
					}

					this.dictModel.sizeList.map((z) => {
						if (item.paramCode == z.parameter) {
							this.baseModel.sizeModel = z;
						}
					});
					let arr = [];
					this.baseModel.sizeAllList.map((n) => {
						this.baseModel.sizeModel.sizeList.map((m) => {
							if (n.paramCode == m) {
								arr.push(n);
							}
						});
					});
					n.childList = arr;
					n.childList.map((m) => {
						m.labelColor = undefined;
						m.labelText = undefined;
						if (this.baseModel.sizeModel.popular.includes(m.paramCode)) {
							m.labelColor = this.baseModel.labelModel.labelColor;
							m.labelText = this.baseModel.labelModel.labelText;
						}
					});
				}
			});
		},
		//刷新排序
		refreshOrder() {
			this.baseModel.oldModel.map((i, iIndex) => {
				this.filterShowGeneralData[iIndex] = this.orderFunc(i, this.filterShowGeneralData[iIndex]);
			});

			this.baseModel.oldModel.map((i, iIndex) => {
				this.generalData[iIndex] = this.orderFunc(i, this.generalData[iIndex]);
			});
			this.$forceUpdate();
		},
		//排序处理
		//参数1：老数据源
		//参数2：新数据源
		orderFunc(old, i) {
			if (this.baseModel.arrOrder.includes(old.paramName)) {
				i.stepIndex = old.stepIndex;
				i.customIndex = old.customIndex;
			}
			return i;
		},

		materialFunc(parentItem, item) {
			this.filterShowGeneralData.map((i) => {
				if (i.paramName === "Plating/Finish") {
					i.childList = item.childList;
				}
			});
			this.generalData.map((i) => {
				if (i.paramName === "Plating/Finish") {
					i.childList = item.childList;
				}
			});
		},

		//重写方法
		selectQuoteParamsFunc(parentItem, item, noMask, num) {
			console.log("我是重写", parentItem, item);
			if (num) {
				this.selectQuoteParams(parentItem, item, num); //继承原方法 mixins/quote.js
			} else {
				this.selectQuoteParams(parentItem, item, noMask); //继承原方法 mixins/quote.js
			}

			if (parentItem.paramName === "quoteCategory") {
				this.clearFieldName("Intended Use");
				this.clearFieldName("Style");
				this.clearFieldName("Metal Patches Size");
				this.clearField("SIZE");

				/* 		this.clearFieldName("Style");
							this.clearFieldName("Intended Use");
							this.clearField("SIZE"); */
			}

			if (parentItem.paramName === "Material") {
				this.materialFunc(parentItem, item);
			}
			if (parentItem.paramName === "Intended Use") {
				this.baseModel.isShow = true;
				this.intendedUseFunc(parentItem, item);
				setTimeout(() => {
					let id = "extendStepWrap-" + parentItem.paramName.replace(/\s+/g, "-")
					scrollToViewTop(document.getElementById(id));
				}, 500);
			}

			if (parentItem.paramName === "Assembly Method") {
				setTimeout(() => {
					let id = "extendStepWrap-" + parentItem.paramName.replace(/\s+/g, "-")
					scrollToViewTop(document.getElementById(id));
				}, 500);
			}

			if (parentItem.paramName === "Style") {
				this.showMaskFn(this.baseModel.intendedUseNext);
			}
			if (parentItem.paramName === "Metal Patches Size") {
				if (item.imageJson) {
					item.imageJsonJson = JSON.parse(item.imageJson);
					item.imageJsonJson.map((n, nIndex) => {
						if (n.alt == this.intendedUseModel.paramCode) {
							this.findSizeModel = item;
							this.findSizeModel.index = nIndex;
						}
					});
					this.$forceUpdate();
				}
			}

			setTimeout(() => {
				this.refreshOrder();
			}, 1000);

			this.$forceUpdate();
		},
		//获取子集
		selectFoldType(item, citem, func) {
			const titleMap = {
				"Intended Use": {
					extendStepTitle: this.lang.customMetalPatches.extendStepTitle,
					extendStepSmallTitle: this.lang.publicStep.selectStyle,
				},
				"Assembly Method": {
					extendStepTitle: this.lang.customMetalPatches.extendStepTitle2,
				},
			};
			let findParam = this.generalData.find((gitem) => {
				return gitem.id === item.id;
			});
			if (findParam) {
				if (citem.childList.length) {
					findParam.showExtendStep = !!citem.childList.length;
					findParam.extendStepTitle = titleMap[item.paramName]?.extendStepTitle;
					findParam.extendStepSmallTitle = titleMap[item.paramName]?.extendStepSmallTitle;
					findParam.extendStepItem = citem;
				} else {
					findParam.showExtendStep = false;
					findParam.extendStepTitle = "";
					findParam.extendStepSmallTitle = "";
					findParam.extendStepItem = null;
				}
			}
			this.selectQuoteParamsFunc(item, citem, undefined, !!citem.childList.length);
			if (func) {
				func();
			}
		},
		//子集回调
		clickExtendStepFun(item, citem) {
			let findParam = this.generalData.find((gitem) => {
				return gitem.id === item.id;
			});
			if (findParam) {
				let findChildParam = findParam.childList.find((cc) => {
					return cc.id === findParam.extendStepItem.id;
				});
				findParam.extendStepItem.extendStepValue = citem;
				findChildParam.extendStepValue = citem;
			}
			citem.checked = true;
			console.log(this.generalData);
			this.showMaskFn();
		},

		//重写summary
		toPositionFunc(e) {
			if (e === "Intended Use") {
				let intendedUse = document.getElementById(e);
				if (intendedUse == null) {
					return;
				}
			}
			this.toPosition(e);
			setTimeout(() => {
				this.refreshOrder();
			}, 1000);
		},

		//购物车回填业务
		initShoppingFunc(item) {
			//实时读取选中数据.  最多5秒则释放
			if (this.shoppingModel.op < 5) {
				setTimeout(() => {
					this.shoppingModel.op += 1;
					this.initShoppingFunc(this.selectedData);
				}, 1000);
			} else {
				if (item["Intended Use"]?.length > 0) {
					this.baseModel.isShow = true;
					if (item["Metal Patches Size"]?.length > 0) {
						this.sizeModel = item["Metal Patches Size"][0];
					}
					let cacheData = {};
					if (item["Intended Use"][0].childList.length > 0) {
						item["Intended Use"][0].childList.map((n) => {
							if (n.checked) {
								cacheData = JSON.parse(JSON.stringify(n)); //解决数据污染;
							}
						});
					}

					this.filterShowGeneralData.map((n, nIndex) => {
						if (n.paramName === "Intended Use") {
							n.childList.map((m, mIndex) => {
								if (m.id == item["Intended Use"][0].id) {
									this.selectFoldType(n, m, () => {
										if (cacheData.id) {
											this.clickExtendStepFun(n, cacheData);
										}
									});
								}
							});
						}
					});
				}
				this.shoppingModel.selectedData = item;
				if (this.sizeModel?.id) {
					let patchSizeModel = this.sizeModel;
					this.generalData.map((n) => {
						if (n.paramName === "Metal Patches Size") {
							this.shoppingModel.sizeModel = patchSizeModel;
						}
					});
					this.selectedData["Metal Patches Size"] = [this.sizeModel];
				} else {
					let obj = sessionStorage.getItem("quoteBackParam");
					if (obj) {
						obj = JSON.parse(obj);
						obj.finaData.map((n) => {
							if (n.paramName === "Metal Patches Size") {
								if (n.childList.length > 0) {
									this.shoppingModel.sizeModel = n.childList[0];
								}
							}
						});
					}
				}
			}
		},
	},
};
</script>

<style scoped lang="scss">
@import "@/assets/css/quotePublic";
.customChenillePatchesNew {
	::v-deep .video-js.vjs-fill:not(.vjs-audio-only-mode) {
		min-height: 200px !important;
	}
	@include respond-to(mb) {
		::v-deep .video-js.vjs-fill:not(.vjs-audio-only-mode) {
			min-height: 113px !important;
		}
	}

	::v-deep .stepItem .imgWrap.imgBorderStyle2 {
		border: 1px solid transparent;
	}
	::v-deep .stepItem .imgWrap.imgBorderStyle2:hover {
		border: 1px solid #d17547;
	}

	.step-box {
		grid-template-columns: repeat(3, 1fr);
		-moz-column-gap: 20px;
		column-gap: 20px;
		row-gap: 20px;
	}
	.assemblyMethod {
		@include respond-to(mb) {
			::v-deep .stepItem {
				justify-content: unset;
			}
		}
	}
	::v-deep .extendStepWrap .extendStepTitleOne {
		font-size: 18px;
		font-weight: 700;
	}

	::v-deep .stepItem .imgWrap .vjs-poster {
		background: none;
		border: none;
	}

	@include respond-to(mb) {
		::v-deep .detailList.type2 .con ul li .left .f-left,
		.detailList.type2 .priceDetail ul li .left .f-left,
		.detailList.type3 .con ul li .left .f-left,
		.detailList.type3 .priceDetail ul li .left .f-left {
			font-size: 14px;
		}
		::v-deep .sizeBox .confirmBtnWrap {
		}
		::v-deep .sizeBox .step-size .step-size-box .step-size-rightArea .shape-img {
			margin-bottom: 0px;
		}
		::v-deep .sizeBox .step-size .step-size-box .step-size-rightArea .textWrap {
			margin-bottom: 6px;
		}
		::v-deep .stepWrap .stepContent.hasViewMoreHeight {
			min-height: auto !important;
		}
	}
}
</style>
