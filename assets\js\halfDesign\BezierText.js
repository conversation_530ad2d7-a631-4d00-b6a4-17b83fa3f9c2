export class BezierText {
	constructor(options = {}) {
		const defaultOptions = {
			minFontSize: 14,
			charSpacingRatio: 0.2, // 字符间距与字体大小的比例
			maxFontSize: 30,
			baselineOffset: 0.5, // 文本基线偏移量
			curvePrecision: 1000, // 曲线长度计算的精度
			centerShortText: true, // 是否居中显示短文本
			isStraightLine: false, // 是否使用直线连接字符
			charWidthCache: new Map(),
		};
		this.options = { ...defaultOptions, ...options };
	}

	setClassOption(data) {
		this.options = Object.assign(this.options, data);
	}

	getPointOnBezier(t, p0, p1, p2, p3) {
		const iT = 1 - t;
		if (p3) {
			// 三次贝塞尔曲线
			return {
				x: iT * iT * iT * p0.x + 3 * iT * iT * t * p1.x + 3 * iT * t * t * p2.x + t * t * t * p3.x,
				y: iT * iT * iT * p0.y + 3 * iT * iT * t * p1.y + 3 * iT * t * t * p2.y + t * t * t * p3.y,
			};
		} else {
			// 二次贝塞尔曲线
			return {
				x: iT * iT * p0.x + 2 * iT * t * p1.x + t * t * p2.x,
				y: iT * iT * p0.y + 2 * iT * t * p1.y + t * t * p2.y,
			};
		}
	}

	calculateRotationAngle(t, p0, p1, p2, p3) {
		const tangent = p3
			? {
					x: 3 * (1 - t) * (1 - t) * (p1.x - p0.x) + 6 * (1 - t) * t * (p2.x - p1.x) + 3 * t * t * (p3.x - p2.x),
					y: 3 * (1 - t) * (1 - t) * (p1.y - p0.y) + 6 * (1 - t) * t * (p2.y - p1.y) + 3 * t * t * (p3.y - p2.y),
			  }
			: {
					x: 2 * (1 - t) * (p1.x - p0.x) + 2 * t * (p2.x - p1.x),
					y: 2 * (1 - t) * (p1.y - p0.y) + 2 * t * (p2.y - p1.y),
			  };
		return (Math.atan2(tangent.y, tangent.x) * 180) / Math.PI;
	}

	calculateCharWidth(char, fontSize, textOption = {}) {
		const cacheKey = `${char}-${fontSize}-${JSON.stringify(textOption)}`;
		if (this.options.charWidthCache.has(cacheKey)) {
			return this.options.charWidthCache.get(cacheKey);
		}

		const textObj = new fabric.Text(char, { fontSize, ...textOption });
		const width = textObj.width;
		this.options.charWidthCache.set(cacheKey, width);
		return width;
	}

	setTextWidth(fontSize, char) {
		const defaultSpacing = fontSize * this.options.charSpacingRatio;
		// 根据字符特点调整间距
		const specialChars = ["I", "i", "J", "j", "t", "L", "l", "d", "C"];
		const wideChars = ["M", "W"];
		// 检查字符是否为字母
		if (!/[A-Za-z]/.test(char)) {
			// 如果不是字母，则返回默认间距或不处理
			return defaultSpacing;
		}
		// 根据字符大小写调整间距
		if (/[A-Z]/.test(char)) {
			if (specialChars.includes(char)) return defaultSpacing + 0.4;
			return defaultSpacing - 1.5;
		} else if (/[a-z]/.test(char)) {
			if (specialChars.includes(char)) return defaultSpacing + 2;
			return defaultSpacing + 1;
		}
	}

	adjustSpacing(char, fontSize, curvature) {
		const defaultSpacing = fontSize * this.options.charSpacingRatio;
		if (this.options.isStraightLine) {
			this.setTextWidth(fontSize, char);
		}
		// 根据字符特点调整间距
		const specialChars = ["I", "i", "J", "j", "t", "L", "l"];
		const wideChars = ["M", "W"];

		let spacingFactor = 1;

		if (specialChars.includes(char)) {
			spacingFactor = 1.2; // 窄字符增加更多间距
		} else if (wideChars.includes(char.toUpperCase())) {
			spacingFactor = 0.8; // 宽字符减少间距
		}

		// 根据曲率进一步调整间距
		spacingFactor *= this.adjustSpacingBasedOnCurvature(curvature, defaultSpacing) / defaultSpacing;

		return defaultSpacing * spacingFactor;
	}

	adjustSpacingBasedOnCurvature(curvature, defaultSpacing) {
		const curvatureThreshold = 5; // 这个值可能需要根据实际情况调整
		const minSpacing = defaultSpacing * 0.7;
		const maxSpacing = defaultSpacing * 1.3;

		if (curvature > curvatureThreshold) {
			// 如果曲率较大，减少间距
			return Math.max(minSpacing, defaultSpacing - (curvature - curvatureThreshold));
		} else {
			// 如果曲率较小，可以适当增加间距
			return Math.min(maxSpacing, defaultSpacing + (curvatureThreshold - curvature));
		}
	}

	calculateTotalTextWidth(textArray, fontSize = 20, textOption = {}) {
		return textArray.reduce((total, char) => {
			const charWidth = this.calculateCharWidth(char, fontSize, textOption);
			let charSpacing = this.calculateCharSpacing(char, fontSize);
			return total + charWidth + charSpacing;
		}, 0);
	}

	calculateCharSpacing(char, fontSize) {
		// 不考虑曲率，仅基于字符特性调整间距
		return this.adjustSpacing(char, fontSize, 0);
	}

	adjustTextSizeToFitCurve(textArray, curveLength, textOption = {}) {
		let low = this.options.minFontSize;
		let high = this.options.maxFontSize;

		// 如果使用最大字体也能适应曲线长度，则直接返回最大字体
		const totalWidthMax = this.calculateTotalTextWidth(textArray, high, textOption);
		if (totalWidthMax <= curveLength) {
			return high;
		}
		while (high - low > 1) {
			const mid = Math.floor((low + high) / 2);
			const totalWidth = this.calculateTotalTextWidth(textArray, mid, textOption);

			if (totalWidth <= curveLength) {
				low = mid;
			} else {
				high = mid;
			}
		}
		const totalWidthLow = this.calculateTotalTextWidth(textArray, low, textOption);
		const totalWidthHigh = this.calculateTotalTextWidth(textArray, high, textOption);
		return totalWidthHigh <= curveLength ? high : totalWidthLow <= curveLength ? low : this.options.minFontSize;
	}

	calculateBezierCurveLength(p0, p1, p2, p3, n = this.options.curvePrecision || 100) {
		n = Math.max(1, n);
		const dt = 1 / n;
		let totalLength = 0;

		for (let i = 0; i < n; i++) {
			const t = i * dt;
			const tNext = Math.min((i + 1) * dt, 1); // 防止 tNext 超过 1
			const point = this.getPointOnBezier(t, p0, p1, p2, p3);
			const pointNext = this.getPointOnBezier(tNext, p0, p1, p2, p3);
			const segmentLength = Math.sqrt(Math.pow(pointNext.x - point.x, 2) + Math.pow(pointNext.y - point.y, 2));
			totalLength += segmentLength;
		}

		return totalLength;
	}

	calculateSecondDerivativeQuadratic(p0, p1, p2) {
		return {
			x: 2 * (p2.x - 2 * p1.x + p0.x),
			y: 2 * (p2.y - 2 * p1.y + p0.y),
		};
	}

	calculateSecondDerivativeCubic(t, p0, p1, p2, p3) {
		const factor1 = 6 * (1 - t);
		const factor2 = 6 * t;
		return {
			x: factor1 * (p2.x - 2 * p1.x + p0.x) + factor2 * (p3.x - 2 * p2.x + p1.x),
			y: factor1 * (p2.y - 2 * p1.y + p0.y) + factor2 * (p3.y - 2 * p2.y + p1.y),
		};
	}

	calculateSecondDerivative(t, p0, p1, p2, p3 = null) {
		if (p3 === null) {
			return this.calculateSecondDerivativeQuadratic(p0, p1, p2);
		} else {
			return this.calculateSecondDerivativeCubic(t, p0, p1, p2, p3);
		}
	}

	isCurveConvexUpwards(t, p0, p1, p2, p3 = null) {
		if (!p3) {
			return p1.y < p0.y + (p2.y - p0.y) / 2;
		} else {
			const secondDerivative = this.calculateSecondDerivative(t, p0, p1, p2, p3);
			return secondDerivative.y > 0;
		}
	}

	calculateCurvature(t, p0, p1, p2, p3 = null) {
		const secondDerivative = this.calculateSecondDerivative(t, p0, p1, p2, p3);
		return Math.sqrt(secondDerivative.x * secondDerivative.x + secondDerivative.y * secondDerivative.y);
	}

	addTextToCanvas(text, points, textOption = {}, groupOption = {}) {
		this.options.charWidthCache = new Map();

		// 根据传入点的数量确定是直线、二次贝塞尔还是三次贝塞尔
		let [startPoint, controlPoint1, controlPoint2, endPoint] = points;

		const textArray = text.split("");
		// 判断是否为直线
		this.options.isStraightLine = points.length === 3 && this.arePointsCollinear(startPoint, controlPoint1, controlPoint2);
		let curveLength = 0;
		if (this.options.isStraightLine) {
			curveLength = Math.sqrt(Math.pow(controlPoint2.x - startPoint.x, 2) + Math.pow(controlPoint2.y - startPoint.y, 2));
		} else {
			curveLength = this.calculateBezierCurveLength(startPoint, controlPoint1, controlPoint2, endPoint);
		}

		// 调整字体大小以适应曲线
		let fontSize = this.adjustTextSizeToFitCurve(textArray, curveLength, textOption);

		// 提前计算所有字符的宽度
		const charWidths = textArray.map((char) => this.calculateCharWidth(char, fontSize, textOption));

		// 计算文本总宽度（用于居中）
		let totalWidth = this.calculateTotalTextWidth(textArray, fontSize, textOption);

		// 创建一个文本组
		let textGroup = new fabric.Group([], { left: 0, top: 0, ...groupOption });

		console.log(fontSize, "fontSize");
		console.log(textOption.fontFamily, "fontFamily");
		console.log(text, text.length, "text");
		console.log(curveLength, "curveLength");
		console.log(totalWidth, "totalWidth");

		// 初始化参数
		let currentLength = 0;
		let startOffset = 0;

		// 计算总宽度和起始偏移量
		if (this.options.centerShortText && totalWidth < curveLength) {
			// 文本居中所需的偏移量
			startOffset = (curveLength - totalWidth) / 2;
			if (charWidths.length > 0 && this.options.isStraightLine) startOffset += charWidths[0] / 2;
		}

		// 只在非直线情况下应用额外的偏移量调整
		if (!this.options.isStraightLine) {
			if (text.length >= 12) startOffset += fontSize;
			if (text.length >= 24) startOffset += fontSize * 1.2;
		}

		const placeChar = (char, index) => {
			const charWidth = charWidths[index];
			let adjustedCurrentLength = currentLength + startOffset;

			if (this.options.isStraightLine) {
				// 直线情况下的字符放置逻辑
				const t = adjustedCurrentLength / curveLength;
				const x = startPoint.x + t * (controlPoint2.x - startPoint.x);
				const y = startPoint.y + t * (controlPoint2.y - startPoint.y);

				// 创建并配置文本对象
				const textObjAdjusted = new fabric.Text(char, {
					left: x,
					top: y,
					originX: "center",
					originY: "center",
					angle: 0, // 直线情况下角度为0
					...textOption,
					fontSize,
				});

				textGroup.addWithUpdate(textObjAdjusted);
				currentLength += charWidth + this.setTextWidth(fontSize, char);
			} else {
				// 曲线情况下的字符放置逻辑
				const t = (adjustedCurrentLength + charWidth / 2) / curveLength;

				// 获取曲线上的点、角度和曲率
				const point = this.getPointOnBezier(t, startPoint, controlPoint1, controlPoint2, endPoint);
				const angle = this.calculateRotationAngle(t, startPoint, controlPoint1, controlPoint2, endPoint);
				const curvature = this.calculateCurvature(t, startPoint, controlPoint1, controlPoint2, endPoint);

				// 根据字符特点和曲率调整间距
				const charSpacing = Math.max(this.adjustSpacing(char, fontSize, curvature), 1); // 确保间距至少为1

				// 调整垂直位置以对齐文本基线
				const adjustedPoint = {
					x: point.x,
					y: point.y + (this.isCurveConvexUpwards(t, startPoint, controlPoint1, controlPoint2, endPoint) ? fontSize * 0.5 + this.options.baselineOffset : -(fontSize * 0.5 + this.options.baselineOffset)),
				};

				// 创建并配置文本对象
				const textObjAdjusted = new fabric.Text(char, {
					left: adjustedPoint.x,
					top: adjustedPoint.y,
					originX: "center",
					originY: "center",
					angle,
					...textOption,
					fontSize,
				});

				// 检查是否与前一个字符重叠
				if (index > 0) {
					const prevObj = textGroup._objects[index - 1];
					if (prevObj) {
						const distanceToPrev = Math.sqrt(Math.pow(prevObj.left - adjustedPoint.x, 2) + Math.pow(prevObj.top - adjustedPoint.y, 2));

						// 如果距离小于最小安全距离，则增加间距
						const minSafeDistance = charWidth / 2 + charWidths[index - 1] / 2 + charSpacing;
						if (distanceToPrev < minSafeDistance) {
							// 增加间距以避免重叠
							currentLength += minSafeDistance - distanceToPrev;
						}
					}
				}

				textGroup.addWithUpdate(textObjAdjusted);
				currentLength += charWidth + charSpacing;
			}

			// 检查是否超出了曲线长度
			if (currentLength + startOffset > curveLength && index < textArray.length - 1) {
				console.warn("Warning: Text exceeds curve length. Truncating text.");
				return false;
			}

			return true;
		};

		// 尝试放置所有字符
		for (let i = 0; i < textArray.length; i++) {
			if (!placeChar(textArray[i], i)) {
				// 如果放置字符失败，移除最后一个添加的字符
				if (textGroup._objects.length > 0) {
					textGroup.remove(textGroup._objects[textGroup._objects.length - 1]);
				}
				break;
			}
		}

		return textGroup;
	}

	// 辅助函数：检查三点是否共线
	arePointsCollinear(p1, p2, p3) {
		// 使用向量叉积判断三点是否共线
		return Math.abs((p2.x - p1.x) * (p3.y - p1.y) - (p2.y - p1.y) * (p3.x - p1.x)) < Number.EPSILON;
	}

	createBezierCurvePath(points, options = {}) {
		// 确保 points 是一个数组并且包含 3 或 4 个点
		if (!Array.isArray(points) || ![3, 4].includes(points.length)) {
			throw new Error("Invalid number of points. A Bezier curve requires exactly 3 or 4 points.");
		}
		// 解构选项
		const { stroke = "black", fill = "", strokeWidth = 2, selectable = false, evented = false, strokeUniform = true } = options;

		// 定义 SVG 路径数据
		let pathData;
		if (points.length === 3) {
			// 二次贝塞尔曲线
			const [start, control, end] = points;
			pathData = `M ${start.x} ${start.y} Q ${control.x} ${control.y}, ${end.x} ${end.y}`;
		} else {
			// 三次贝塞尔曲线
			const [start, control1, control2, end] = points;
			pathData = `M ${start.x} ${start.y} C ${control1.x} ${control1.y}, ${control2.x} ${control2.y}, ${end.x} ${end.y}`;
		}

		// 创建并返回 Fabric Path 对象
		return new fabric.Path(pathData, {
			stroke,
			strokeWidth,
			fill,
			selectable,
			evented,
			strokeUniform,
			...options, // 允许传递其他自定义选项
		});
	}
}
