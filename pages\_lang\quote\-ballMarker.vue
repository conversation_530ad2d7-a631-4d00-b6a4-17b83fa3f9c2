<template>
	<div id="custom-embroidered-patches" class="quoteWrap">
		<Preloader v-if="isLoading"></Preloader>
		<template v-else>
			<article class="content">
				<QuoteNav2 :pid="pid" title="We provide these golf ball marker types for you."></QuoteNav2>
				<QuoteTitle :h1-text="`${lang.cy} ${cateData.cateName}`"></QuoteTitle>
				<div id="leftArea" class="leftArea">
					<div v-for="(item, index) in filterShowGeneralData" :key="item.id">
						<component v-if="renderStepComponent(item, index)" :is="renderStepComponent(item, index).is" v-bind="renderStepComponent(item, index).props" v-on="renderStepComponent(item, index).on" :class="renderStepComponent(item, index).dynamicClass" />
					</div>
				</div>
				<div id="rightAreaCustom" class="rightArea">
					<transition enter-active-class="animate__animated animate__faster animate__fadeInBottomLeft" leave-active-class="animate__animated animate__faster animate__fadeOutBottomLeft">
						<Detail v-show="showRightArea" :key="1" :absoluteImg="comeFromDZ.absoluteImg" :class="{ mask: maskName }" :customQty="customQty" :generalData="generalData" :priceInfo="priceInfo" :selectedData="selectedData" :textInfo="textInfo" class="type1" @addCart="addCart" @addInquiry="debounceAddInquiry" @toPosition="toPosition"></Detail>
					</transition>
				</div>
			</article>
			<div id="foot" class="footer">
				<Detail :key="3" :absoluteImg="comeFromDZ.absoluteImg" :customQty="customQty" :generalData="generalData" :priceInfo="priceInfo" :selectedData="selectedData" :textInfo="textInfo" class="type2" @addCart="addCart" @addInquiry="debounceAddInquiry" @toPosition="toPosition"></Detail>
			</div>
			<el-drawer :size="device === 'ipad' ? '728px' : '85%'" :visible.sync="showArea" :with-header="true" custom-class="drawDialog">
				<Detail :key="2" :absoluteImg="comeFromDZ.absoluteImg" :customQty="customQty" :generalData="generalData" :priceInfo="priceInfo" :selectedData="selectedData" :textInfo="textInfo" class="type3" @addCart="addCart" @addInquiry="debounceAddInquiry" @toPosition="toPosition"></Detail>
			</el-drawer>
			<!--    遮罩-->
			<myMask :maskName.sync="maskName"></myMask>
			<!-- 建议弹窗-->
			<RecomendDialog :commentsDialog.sync="commentsDialog" :paramValue="selectedParamsValue" :showUpload="false" :textPlaceholder="lang.placeholder2" @next="recomendNext"></RecomendDialog>
			<!--  手机，ipad端预览-->
			<PreviewBtn :showArea.sync="showArea"></PreviewBtn>
			<BaseDialog v-model="showOtoDialog" :width="device != 'mb' ? '50%' : '90%'">
				<div class="otoWrap">
					<img alt="" src="@/assets/images/oto.png" />
					<p>{{ lang.p1 }}</p>
					<h3>{{ lang.p2 }}</h3>
					<p style="color: #666666">
						{{ lang.p3 }}<br />
						{{ lang.p4 }}.
					</p>
					<div class="box">
						<p class="t1">
							{{ lang.p5 }} <br />
							{{ lang.p6 }}!
						</p>
						<a href="https://www.o2o.co/previewWeb" style="text-decoration: none">
							<el-button type="primary">{{ lang.p7 }}</el-button>
						</a>
					</div>
				</div>
			</BaseDialog>
			<VideoPreviewDialog :pic-dialog.sync="picDialog" :selected-params-value="selectedParamsValue" :temp-type="tempType" :zoom-pic="zoomPic" @dialogNextStep="dialogNextStep" @qtyAndBtnConfirm="qtyAndBtnConfirm" @qtyAndBtnNext="qtyAndBtnNext"></VideoPreviewDialog>
			<BaseDialog v-model="upgradeDialog" :width="device != 'mb' ? '640px' : '320px'">
				<Upgrade :tipArr="tipArr" :typeName="cateData.cateName" @closeUpgrade="upgradeDialog = false" @upgradeLevel="upgradeLevel"></Upgrade>
			</BaseDialog>
			<infoDialog :infoDialogVisible.sync="infoDialogVisible" :otherUpload="uploadArtworkList" :recomendUpload="recomendUpload" :uploadList.sync="uploadList" @getValue="getValueFun"></infoDialog>
			<BaseDialog v-model="noFileDialog" :model="false" :width="device != 'mb' ? '485px' : '90%'">
				<template #closeIcon>
					<div style="display: none"></div>
				</template>
				<infoUpload :infoUploadList="infoUploadList" @closeInfoDialog="closeInfoDialog" @delInfoList="delInfoList" @pushInfoList="pushInfoList" @updateInquiry="updateInquiry"></infoUpload>
			</BaseDialog>
		</template>
	</div>
</template>
<script>
import "@/plugins/element";
import BaseDialog from "@/components/Quote/BaseDialog";
import Upgrade from "@/components/Quote/Upgrade";
import infoDialog from "@/components/Medals/infoDialog";
import QuoteNav2 from "@/components/Medals/QuoteNav2";
import quoteMixin from "@/mixins/quote";
import quoteBanChoiceMixins from "@/mixins/quoteBanChoice";
import StepTime from "@/components/Quote/StepTime.vue";
import StepUpload from "@/components/Quote/StepUpload.vue";
import StepQty from "@/components/Quote/StepQty.vue";
import RecomendDialog from "@/components/Quote/RecomendDialog.vue";
import myMask from "@/components/Quote/Mask.vue";
import Detail from "@/components/Quote/Detail.vue";
import PreviewBtn from "@/components/Quote/PreviewBtn.vue";
import Preloader from "@/components/Quote/Preloader.vue";
import PublicStep from "@/components/Quote/PublicStep/index.vue";
import { getQuoteConfig } from "@/assets/js/quote/quoteConfig";
import PinsSizeSelect from "~/components/Quote/PinsSizeSelect.vue";
import VideoPreviewDialog from "@/components/Quote/VideoPreviewDialog.vue";
import QuoteTitle from "@/components/Quote/QuoteTitle.vue";
import { initStepComponentConfig } from "assets/js/quote/quoteStepComponent";

export default {
	head() {
		return {
			title: "Quote page",
		};
	},
	components: {
		QuoteTitle,
		VideoPreviewDialog,
		PublicStep,
		Preloader,
		PreviewBtn,
		Detail,
		myMask,
		RecomendDialog,
		StepQty,
		StepUpload,
		StepTime,
		BaseDialog,
		infoDialog,
		Upgrade,
		QuoteNav2,
		PinsSizeSelect,
	},
	mixins: [quoteMixin, quoteBanChoiceMixins],
	data() {
		const config = getQuoteConfig.call(this, this.$route.name);
		return {
			...config,
		};
	},
	methods: {
		renderStepComponent(item) {
			const stepComponentConfig = initStepComponentConfig.call(this, item);
			if (this.pid == 650) {
				stepComponentConfig.sizeConfig.props.sizeImgP1 = this.$store.getters.lang.quote.divottools.p1;
				stepComponentConfig.sizeConfig.props.smallTitle = this.$store.getters.lang.quote.ballMarker.stepSizeTitle;
			}
			const map = {
				quoteCategory: stepComponentConfig.defaultConfig,
				"Ball Markers Size": stepComponentConfig.sizeConfig,
				"Select Plating / Finish": stepComponentConfig.defaultConfig,
				"Select  Amount of Colors for Ball Markers": stepComponentConfig.defaultConfig,
				"Select Packaging": stepComponentConfig.defaultConfig,
				"Additional Upgrades (Optional)": stepComponentConfig.additionalConfig,
				"Upload Artwork & Comments": stepComponentConfig.uploadConfig,
				qty: stepComponentConfig.qtyConfig,
				"Select Turnaround Time": stepComponentConfig.timeConfig,
			};
			return map[item.paramName];
		},
	},
};
</script>

<style lang="scss" scoped>
@import "@/assets/css/quotePublic";

.Additional-Upgrades {
	::v-deep .zoomIcon {
		color: #ccc !important;
	}
}
</style>
