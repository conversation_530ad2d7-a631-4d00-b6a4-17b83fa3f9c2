<template>
	<div class="mb-4" :class="stepData.styleName">
        <slot name="stepText"></slot>
		<div class="step-content">
			<div class="step-item" :class="{ active: index === selectIndex }"
				v-for="(step, index) in stepData.productParamList" :key="index" @click="selectStep(step, index)">
				<half-design-check-box class="mr-1"></half-design-check-box>
				<div class="paramName text-truncate mr-2" style="vertical-align: middle">
					{{ step.valueName }}
				</div>
				<v-tooltip bottom v-if="step.remark">
					<template v-slot:activator="{ on, attrs }">
						<v-icon v-bind="attrs" v-on="on" size="18px">
							{{ iconName }}
						</v-icon>
					</template>
					<div class="text-center">
						<div
							style="text-align: left; color: #fff; line-height: 22px; font-size: 13px; width: 250px; word-break: break-word; white-space: pre-line">
							{{ step.remark }}
						</div>
						<div v-if="step.imgDetail" style="max-width: 150px; max-height: 150px">
							<img :src="step.imgDetail" :alt="step.valueName" :title="step.valueName" style="object-fit: contain" />
						</div>
					</div>
				</v-tooltip>
			</div>
		</div>
		<div class="mb-2" style="font-weight: 600" v-if="showText">
			{{ langSemiCustom.qp }}
		</div>

		<div class="priceList" :class="{ showBtn: showScrollBtn }" v-show="showQtyWrap">
			<div class="qtyWrap" v-show="qtyPrice && qtyPrice.length > 0">
				<div class="qty-item">
					<div>
						{{ qtyText }}
					</div>
					<div>{{ langSemiCustom.listPrice }}</div>
					<div v-if="productInfo.discount">
						{{ langSemiCustom.price }}
					</div>
					<!-- <div style="color: red" v-if="stepData.styleName != 'style6' && stepData.styleName != 'style8'">
                        {{ langSemiCustom.save }}
                    </div> -->
					<div style="color: red" v-if="productInfo.discount">
						{{ langSemiCustom.save }}
					</div>
				</div>
				<div class="qty-item" v-for="(item, index) in qtyPrice" :key="item.number">
					<div>{{ item.quantity || 0 }}+</div>
					<div :class="{ line: productInfo.discount }">
						<CCYRate :price="item.unitPrice"></CCYRate>
					</div>
					<div v-if="productInfo.discount">
						<CCYRate :price="item.markupUnitPrice"></CCYRate>
					</div>

					<div class="arrowBox" style="display: flex; align-items: center; justify-content: center"
						v-if="productInfo.discount">
						<v-icon>mdi-arrow-down</v-icon>
						{{ (productInfo.discount * 100).toFixed(0) + "%" }}
					</div>
					<!-- <div style="color: red" v-if="stepData.styleName != 'style6' && stepData.styleName != 'style8'">{{ savePrice(item.unitPrice, index) }}</div> -->
				</div>
			</div>
			<div class="rightBox">
				<div class="right-btn" v-show="qtyPrice && qtyPrice.length > 0" @click="toRight">
					<b :class="{ 'icon-right': !cursor, 'icon-left': cursor }"></b>
				</div>
				<div class="letter" v-show="domainName">
					{{ productInfo.discountFlag }}
				</div>
			</div>
		</div>
		<div class="errorTip">
			<v-alert dense outlined type="error">
				{{ langSemiCustom.errorTip }}
			</v-alert>
		</div>
	</div>
</template>

<script>
import { addDragable } from "@/utils/utils";

export default {
	inject: ["getUnitPriceStep", "getCustomPriceData", "getProductInfo"],
	props: {
		stepData: {
			type: Object,
			default: {},
		},
	},
	data() {
		return {
			selectIndex: -1,
			selectItem: null,
			showScrollBtn: false,
			cursor: false,
			domainName: false,
		};
	},
	computed: {
		productInfo() {
			return this.getProductInfo();
		},
		unitPriceStep() {
			return this.getUnitPriceStep();
		},
		langSemiCustom() {
			return this.$store.getters.lang?.semiCustom;
		},
		showText() {
			return (this.stepData.styleName === "style6" || this.stepData.styleName === "style8") && this.qtyPrice && this.qtyPrice.length > 0;
		},
		iconName() {
			let style = this.stepData.styleName;
			if (style === "style7" || style === "style8") {
				return "mdi-image-size-select-actual";
			} else {
				return "mdi-help-circle-outline";
			}
		},
		showQtyWrap() {
			let noShowStyle = ["style1", "style2", "style3", "style7"],
				showStyle = ["style4", "style5", "style6", "style8"];
			let style = this.stepData.styleName;
			if (noShowStyle.includes(style)) {
				return false;
			}
			if (showStyle.includes(style)) {
				return true;
			}
		},
		customPriceData() {
			return this.getCustomPriceData();
		},
		qtyPrice() {
			if (!this.selectItem) {
				return [];
			}
			try {
				// return this.setPrice(this.productInfo.discount);
				let data=this.selectItem.markupIncreasePrice&&JSON.parse(this.selectItem.markupIncreasePrice);
				return data;
			} catch (e) {
				return [];
			}
		},
		qtyText() {
			let style = this.stepData.styleName,
				text;
			switch (style) {
				case "style4":
					text = this.langSemiCustom.qty;
					break;
				case "style5":
					text = this.langSemiCustom.quantity;
					break;
				default:
					text = this.langSemiCustom.qty;
			}
			return text;
		},
	},
	methods: {
		setPrice(discount = 0) {
			let increasePrice = JSON.parse(this.unitPriceStep.increasePrice),
				itemIncreasePrice = JSON.parse(this.selectItem.increasePrice);
			if (this.selectItem.priceType === 5) {
				increasePrice.forEach((item) => {
					//单价
					let price = parseFloat(item.unitPrice) + parseFloat(this.findPrice(item, itemIncreasePrice).unitPrice);
					//计算价格分层
					let price2 = (price * this.customPriceData.multiBasicUnitPrice).toFixed(2);
					//计算折扣
					let price3 = Math.round(price2 * Math.abs(1 - discount) * 100) / 100;
					if (discount != 0) {
						item.discountPrice = price3.toFixed(2);
						item.unitPrice = price2;
					} else {
						item.unitPrice = price2;
					}
				});
				let isShowPrice = increasePrice.filter((item) => item.isShow == 1 || item.isShow == null);
				this.$Bus.$emit("priceSection", increasePrice);
				return isShowPrice || [];
			} else if (this.selectItem.priceType === 1) {
				increasePrice.forEach((item) => {
					//单价
					let price = parseFloat(item.unitPrice) + this.selectItem.unitPrice;
					//计算价格分层
					let price2 = (price * this.customPriceData.multiBasicUnitPrice).toFixed(2);
					//计算折扣
					let price3 = Math.round(price2 * Math.abs(1 - discount) * 100) / 100;
					if (discount != 0) {
						item.discountPrice = price3;
						item.unitPrice = price2;
					} else {
						item.unitPrice = price2;
					}
				});
				let isShowPrice = increasePrice.filter((item) => item.isShow !== 0);
				this.$Bus.$emit("priceSection", increasePrice);
				return isShowPrice || [];
			}
		},
		savePrice(price, index) {
			if (index == 0) return "—";
			return (((this.qtyPrice[0].unitPrice - price) / this.qtyPrice[0].unitPrice) * 100).toFixed(0) + "%";
		},
		toRight() {
			let container = document.querySelector(".qtyWrap");
			let startX = container.scrollLeft;
			if (container.scrollWidth == container.scrollLeft + container.clientWidth && !this.cursor) {
				this.cursor = true;
				return;
			}
			if (container.scrollWidth == container.scrollLeft + container.clientWidth && this.cursor) {
				this.cursor = false;
				container.scrollTo({ top: 0, left: 0, behavior: "smooth" });
				return;
			}
			container.scrollTo(startX + 20, 0); // 横向 纵向
		},

		findPrice(item, itemIncreasePrice) {
			let len = itemIncreasePrice.length,
				newNum = parseInt(item.quantity),
				findItem = itemIncreasePrice[0];
			for (let i = 0; i < len; i++) {
				let item = itemIncreasePrice[i],
					nextItem = itemIncreasePrice[i + 1];
				if (newNum >= item.quantity && (nextItem ? newNum < nextItem.quantity : true)) {
					findItem = item;
					break;
				}
			}
			return findItem;
		},
		selectStep(item, index, status = false) {
			this.selectIndex = index;
			this.selectItem = item;
			this.$emit("selectStep", {
				type: this.stepData.attributeFlag,
				data: item,
				parent: this.stepData,
				id: this.stepData.id,
				firstSelect: status,
			});
			this.$nextTick(() => {
				let container = document.querySelector(".qtyWrap");
				if (container) {
					this.showScrollBtn = container.scrollWidth > container.clientWidth;
				}
			});
		},
		selectDefault() {
			if (this.stepData.productParamList.length) {
				this.selectStep(this.stepData.productParamList[0], 0, true);
			}

			// if (this.selectIndex <= -1) {
			// 	let findIndex = this.stepData.productParamList.findIndex((item) => {
			// 		return !item.isBlank;
			// 	});
			// 	if (findIndex > -1) {
			// 		this.selectStep(this.stepData.productParamList[findIndex], findIndex, true);
			// 	}
			// }

			// console.log(this.stepData.productParamList,'22123123')
			// if (this.selectIndex <= -1) {
			// 	if(this.stepData.productParamList.length==1){
			// 		this.selectStep(this.stepData.productParamList[0], 0, true);
			// 	}
			// 	// if(this.stepData.productParamList.length > 1){
			// 	// 	let findIndex = this.stepData.productParamList.findIndex((item) => {
			// 	// 		return !item.isBlank;
			// 	// 	});
			// 	// 	if (findIndex > -1) {
			// 	// 		this.selectStep(this.stepData.productParamList[findIndex], findIndex, true);
			// 	// 	}
			// 	// }
			// }
		},
	},
	mounted() {
		this.$Bus.$on("selectDefaultPrintStep", this.selectDefault);
		let container = document.querySelector(".qtyWrap");
		if (container) {
			addDragable(container, (scrollLeft) => {
				if (scrollLeft == 0) {
					this.cursor = true;
				} else {
					this.cursor = false;
				}
			});
		}
		let hostname = window.location.hostname;
		this.domainName = hostname === "www.fulldesigns.com";
	},
	watch: {},
	beforeDestroy() {
		this.$Bus.$off("selectDefaultPrintStep", this.selectDefault);
	},
};
</script>

<style scoped lang="scss">
@import "~assets/css/half.scss";

.priceList {
	position: relative;
	display: flex;

	.rightBox {
		display: flex;
		align-items: center;
		column-gap: 2px;
	}

	.right-btn {
		height: 100%;
		display: none;
		justify-content: center;
		align-items: center;

		&.cursor {
			cursor: no-drop;
		}

		b {
			font-size: 12px;
		}
	}

	.letter {
		margin-left: 4px;
		align-self: flex-end;
		width: 14px;
		font-family: Roboto, Roboto;
		font-weight: 400;
		font-size: 14px;
		color: #333333;
		line-height: 17px;
		text-align: center;
		font-style: normal;
		text-transform: none;
	}
}

.priceList.showBtn {
	.right-btn {
		display: flex;
	}
}

.qtyWrap {
	position: relative;
	cursor: move;

	.qty-item {
		box-shadow: inset 0 0 20px 20px rgba(0, 0, 0, 0.02);

		.arrowBox {
			color: red;

			.v-icon {
				font-size: 14px;
				color: red;
			}
		}

		.line {
			text-decoration: line-through;
			color: #bab6b6;
		}
	}
}

.step-content {
	display: grid;
	margin-bottom: 10px;

	.step-item {
		min-width: 0;
		@include flex-center;
		cursor: pointer;
	}
}

.style4,
.style5 {
	.qtyWrap {
		overflow: auto;
		display: flex;
		flex-wrap: nowrap;
		// width: 100%;
		font-size: 14px;

		&::-webkit-scrollbar {
			display: none;
		}

		.qty-item {
			// flex:1;
			width: 100px;
			flex-shrink: 0;
			// max-width: 120px;
			text-align: center;
			border: 1px solid $border-color;
			margin-right: -1px;

			&>div {
				padding: 2px;
			}

			&:first-child {
				font-weight: 600;
			}

			&>div:first-child {
				border-bottom: 1px solid $border-color;
			}

			&>div:nth-child(2) {
				border-bottom: 1px solid $border-color;
			}

			&>div:nth-child(3) {
				border-bottom: 1px solid $border-color;
			}
		}
	}

	.right-btn {
		width: 20px;
		transform: none;
		background-color: $background-color2;
	}

	@include respond-to(mb) {
		.qtyWrap {
			font-size: 12px;

			.qty-item {
				// flex: 0 0 80px;
				width: 80px;
				flex-shrink: 0;
			}
		}
	}
}

.style6,
.style8 {
	.qtyWrap {
		overflow: auto;
		display: flex;
		flex-wrap: nowrap;
		// width: 100%;
		font-size: 14px;

		&>.qty-item:first-child {
			display: none;
		}

		&::-webkit-scrollbar {
			display: none;
		}

		.qty-item {
			// flex: 1;
			width: 100px;
			flex-shrink: 0;
			padding: 2px;
			margin-right: 5px;
			text-align: center;
			border: 1px solid $border-color;

			&:last-child {
				margin-right: 0;
			}

			&>div:last-child {
				color: $color-red;
			}
		}
	}

	.right-btn {
		width: 20px;
		height: 20px;
		background-color: $background-color2;
		border-radius: 50%;
	}

	@include respond-to(mb) {
		.qtyWrap {
			font-size: 12px;

			.qty-item {
				// flex: 0 0 80px;
				width: 80px;
				flex-shrink: 0;
				margin-right: 5px;
			}
		}
	}
}

.style1 .step-content,
.style4 .step-content {
	grid-template-columns: repeat(4, 1fr);
	grid-gap: 10px;

	.step-item {
		@include step-default;

		.check-box {
			display: none;
		}
	}

	@include respond-to(mb) {
		grid-template-columns: repeat(2, 1fr);
		grid-gap: 5px;

		.step-item {
			padding: 10px;
		}
	}
}

.style2 .step-content,
.style5 .step-content {
	grid-template-columns: repeat(3, 1fr);
	grid-row-gap: 10px;

	.step-item {
		min-width: 0;
		border: 1px solid $border-color;
		padding: 10px;
		border-radius: 0;
		margin-right: -1px;

		@media (any-hover: hover) {
			&:hover ::v-deep {
				.check-box {
					border-color: $color-primary;

					.check-box-inner {
						background-color: $color-primary;
					}
				}
			}
		}

		&:nth-child(3n) {
			border-top-right-radius: 6px;
			border-bottom-right-radius: 6px;
		}

		&:nth-child(3n + 1) {
			border-top-left-radius: 6px;
			border-bottom-left-radius: 6px;
		}

		&:last-child {
			border-top-right-radius: 6px;
			border-bottom-right-radius: 6px;
		}
	}

	.step-item.active ::v-deep {
		background-color: var(--color-second);
		border-color: $color-primary;
		border-width: 2px;
		padding: 9px;
		z-index: 1;

		.check-box {
			border-color: $color-primary;

			.check-box-inner {
				background-color: $color-primary;
			}
		}
	}

	@include respond-to(mb) {
		grid-row-gap: 5px;
		grid-template-columns: repeat(2, 1fr);

		.step-item {
			&:nth-child(2n) {
				border-top-right-radius: 2px;
				border-bottom-right-radius: 2px;
			}

			&:nth-child(2n + 1) {
				border-top-left-radius: 2px;
				border-bottom-left-radius: 2px;
			}

			&:last-child {
				border-top-right-radius: 2px;
				border-bottom-right-radius: 2px;
			}
		}
	}
}

.style3 .step-content,
.style6 .step-content,
.style7 .step-content,
.style8 .step-content {
	grid-template-columns: repeat(3, 1fr);
	grid-gap: 20px 10px;
	margin-bottom: 20px;

	.step-item {
		position: relative;
		padding: 10px;
		background-color: $background-color2;
		@include radius-response;

		.v-icon {
			color: #cccccc;
			transition: none;
		}

		.check-box {
			display: none;
		}

		&::after {
			content: "";
			position: absolute;
			bottom: -10px;
			left: 50%;
			transform: translateX(-50%);
			width: 0;
			height: 0;
			border-width: 10px 10px 0;
			border-style: solid;
			border-color: $background-color transparent transparent;
		}

		@media (any-hover: hover) {
			&:hover {
				background-color: $color-primary;
				color: #ffffff;

				.v-icon {
					color: #ffffff;
				}

				&::after {
					border-color: $color-primary transparent transparent;
				}
			}
		}
	}

	.step-item.active {
		background-color: $color-primary;
		color: #ffffff;

		.v-icon {
			color: #ffffff;
		}

		&::after {
			border-color: $color-primary transparent transparent;
		}
	}

	@include respond-to(mb) {
		grid-template-columns: repeat(2, 1fr);
		grid-gap: 5px;
		margin-bottom: 10px;

		.step-item {
			&::after {
				bottom: -5px;
				border-width: 5px 5px 0;
			}
		}
	}
}
</style>