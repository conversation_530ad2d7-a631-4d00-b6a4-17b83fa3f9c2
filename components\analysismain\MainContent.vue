<template>
	<div id="main-content">
		<div class="header">
			<div class="breadcrumb-nav">
				<v-breadcrumbs :items="breadItems" divider=">" large class="pa-0">
					<template v-slot:item="{ item }">
						<v-breadcrumbs-item :href="item.href" :disabled="item.disabled">
							{{ item.text }}
						</v-breadcrumbs-item>
					</template>
				</v-breadcrumbs>
			</div>
			<div class="other">
				<div class="time-filter">
					<span>Time-Filter：</span>
					<v-text-field v-model="dateRangeText" prepend-icon="mdi-calendar" readonly @click="timeSelect">
					</v-text-field>
					<v-date-picker v-model="dates" range v-if="isDatePicker" class="date-picker" @change="timeSelect()">
					</v-date-picker>
				</div>
				<div class="apply" @click="filterByTime" style="cursor: default;">Apply</div>
				<div class="print">
					<v-icon color="#1A73E8">mdi-printer-outline</v-icon>
				</div>
				<div class="download">
					<v-icon color="#1A73E8">mdi-tray-arrow-down</v-icon>
				</div>
			</div>
		</div>
		<div class="top">
			<div class="left" :key="id">
				<analysismain-StatisticTab :barData="barData" :dates="dates"/>
			</div>
			<div class="right" v-permission="'p-16'">
				<analysismain-BrieflyShow v-for="item in brieflyShowData" :key="item.type" :bgc="item.bgc"
										  :type="item.type"
										  :total="item.total" :iconName="item.iconName" :isHint="item.isHint"/>
			</div>
		</div>
		<div class="bottom">
			<div class="left" :key="id" v-permission="'p-17'">
				<analysismain-PieChartShow :inquiriesData='inquiriesData' :revenueData="revenueData"
										   :ordersData="ordersData"/>
			</div>
			<div class="right" v-permission="'p-18'">
				<analysismain-TableShow :currentPage="currentPage" :jumpPage="jumpPage" :desserts="sellTable"/>
			</div>
		</div>
		<div class="paging-container" :key="id" v-if="pages>1">
			<!--      <analysismain-Paging @pageJump="pageJump" @pageChange="pageChange" />-->
			<pagination :length="pages" @changePages="pageChange"></pagination>
		</div>
	</div>
</template>

<script>
import {getAnalysisPageAllDate, getTableDataByPaging} from '@/api/manage/analysis'
import {getTotalPageNum} from "@/utils/utils";

export default {
	name: "main-content",
	created() {
		// 给时间选择器初始化时间
		this.dates = [this.timeInit(1), this.timeInit()];
		console.log(this.dates);
		this._getAnalysisPageAllDate({
			startTime: this.dates[0],
			endTime: this.dates[1],
		})

	},
	data() {
		return {
			pages: 0,
			id: +new Date(), // 重置到初始状态
			barData: {}, // 条形图数据
			sellTable: [], // table的数据
			inquiriesData: [],  // inquiries饼图的数据
			ordersData: [], // orders饼图的数据
			revenueData: [], // revenue饼图的数据
			currentPage: 1,
			jumpPage: 1,
			brieflyShowData: [],
			tempData: ["Foo", "Bar", "Fizz", "Buzz"],
			breadItems: [
				{
					text: "Dashboard",
					disabled: false,
					href: "/manage/dashboard",
				},
				{
					text: "Statistical Analysis",
					disabled: true,
					href: "/manage/analysis",
				},
			],
			dates: [],
			isDatePicker: false,
		};
	},
	computed: {
		dateRangeText() {
			return this.dates.join(" ~ ");
		},
	},
	methods: {
		filterByTime() {
			this.id = +new Date()
			this._getAnalysisPageAllDate({
				startTime: this.dates[0],
				endTime: this.dates[1],
			})
		},
		pageJump(value) {
			this.jumpPage = value;
			console.log(this.jumpPage);
			this._getTableDataByPaging(value)
		},
		pageChange(value) {
			this.currentPage = value;
			console.log(this.currentPage);
			this._getTableDataByPaging(value)
		},
		timeSelect() {
			this.isDatePicker = !this.isDatePicker;
			console.log("timeSelect");
		},
		timeInit(month = 0) {
			let today = new Date();
			let yyyy = today.getFullYear();
			let mm = String(today.getMonth() + 1 - month).padStart(2, "0");
			let dd = String(today.getDate()).padStart(2, "0");

			return (today = yyyy + "-" + mm + "-" + dd);
		},
		// --------------------------- 函数封装区域  --------------------------
		_getTableDataByPaging(page) {
			getTableDataByPaging({
				proId: this.$store.getters['manage/getProId'],
				page,
				startTime: this.dates[0],
				endTime: this.dates[1],
			}).then(res => {
				console.log('pagingData', res);
				this.sellTable = res.data.records;
				this.pages = res.data.pages;
			})
		},
		_getAnalysisPageAllDate(
			params = {
				startTime: null,
				endTime: null,
			}) {
			getAnalysisPageAllDate({
				proId: this.$store.getters['manage/getProId'],
				...params
			}).then(res => {
				console.log('AnalysisPageAllDate');

				// 取出条形图的数据
				let barData = res.data.dataset
				this.barData = barData
				// 取出brieflyShowData 所需的数据
				let brieflyShowData = [
					{
						bgc: "#1A73E8 ",
						type: "Inquiries",
						total: res.data.inquiries,
						iconName: "icon-jxsht-ybp-a",
					},
					{
						bgc: "#FBBC04 ",
						type: "Orders",
						total: res.data.orders,
						iconName: "icon-jxsht-ybp-b",
					},
					{
						bgc: "#EA4335 ",
						type: "Revenue",
						total: "$" + res.data.revenue,
						iconName: "icon-jxsht-ybp-c",
					},
					{
						bgc: "#34A752 ",
						type: "Members",
						total: res.data.members,
						iconName: "icon-jxsht-ybp-d",
					},
					{
						bgc: "#1A73E8 ",
						type: "OrderRate",
						total: res.data.avgOrderRate,
						iconName: "icon-jsxht-tjfx-dd",
						isHint: true,
					},
					{
						bgc: "#FF727E ",
						type: "Order Value",
						total: "$" + res.data.avgOrderValue,
						iconName: "icon-jsxht-tjfx-jg",
						isHint: true,
					},
				]
				this.brieflyShowData = brieflyShowData;

				// 取出table所需的数据
				this.sellTable = res.data.topSellingData.records

				// 取出inquiries饼图的数据
				this.inquiriesData = res.data.inquiriesData
				console.log('MainContent-inquiriesData', this.inquiriesData);

				// 取出orders饼图的数据
				this.ordersData = res.data.ordersData

				// 取出revenue饼图的数据
				this.revenueData = res.data.revenueData
			})
		}
	},
};
</script>

<style lang="scss" scoped>
.header {
	display: flex;
	justify-content: space-between;
	height: 2.0833vw;
	margin-bottom: 0.5208vw;

	.other {
		display: flex;

		.time-filter {
			display: flex;
			position: relative;
			align-items: center;
			margin-right: 0.8333vw;

			span {
				margin-right: .5208vw;
			}

			.date-picker {
				position: absolute;
				top: 2.0833vw;
				left: 1.0417vw;
				z-index: 2;
			}
		}

		.apply {
			width: 4.2188vw;
			height: 1.4583vw;
			line-height: 1.4583vw;
			text-align: center;
			color: #fff;
			background: #1a73e8;
			border-radius: 0.2083vw;
			margin-right: 1.25vw;
		}

		.print,
		.download {
			width: 2.1354vw;
			height: 1.4583vw;
			background: #ffffff;
			border-radius: 0.2083vw;
			text-align: center;
		}

		.print {
			margin-right: 0.5729vw;
		}
	}
}

.top {
	display: flex;

	.left {
		width: 71.6146vw;
		height: 26.0417vw;
		margin-right: 1.0417vw;
		background-color: #fff;
		border-radius: 0.5208vw;
		padding-top: 1.3021vw;
	}

	.right {
		width: 23.0729vw;
		height: 26.0417vw;
		display: grid;
		grid-template-columns: 10.9375vw 10.9375vw;
		grid-template-rows: 8.125vw 8.125vw;
		column-gap: 1.1458vw;
		row-gap: 0.8854vw;
	}
}

.bottom {
	display: flex;
	padding-top: 1.3542vw;

	.left {
		width: 37.3958vw;
		margin-right: 1.0417vw;
	}

	.right {
		width: 56.5875vw;
	}
}

.paging-container {
	height: 2.8125vw;
	margin-top: 1.25vw;
	/* background-color: pink; */
}
</style>

<!-- 更改vuetify组件默认的样式 -->
<style lang="scss">
/* 更改面包屑导航组件的默认样式 */
.breadcrumb-nav {
	display: flex;
	width: 13.6458vw;
	height: 1.9792vw;
	margin-left: 0.7292vw;

	.v-breadcrumbs__item {
		color: #666666;
	}

	.v-breadcrumbs__item--disabled {
		color: #1a73e8 !important;
	}
}
</style>
