var call = 0;

export function pdt360DegViewer(id, picList, autoPlay, draggable) {
	var n = picList.length - 1;
	call++;
	var i = 0,
		j = 0,
		move = [],
		mainDiv = document.querySelector(`#${id}`);
	mainDiv.className = "viewer";
	mainDiv.innerHTML += `<img class="${id} ${autoPlay ? "autoPlay " : ""}${draggable ? "draggable " : ""}" draggable="false" src='${picList[i]}'>`;

	if (call == 1)
		for (var k = 0; k < n; k++) {
			document.getElementById("dummy").innerHTML += `<img src='${picList[k]}'>`;
		}

	var img = document.querySelector(`#${id} .${id}`);

	if (draggable) {
		var touch = false;
		window.matchMedia("screen and (max-width: 992px)").matches ? touchFun() : nonTouch();

		//For Touch Devices
		window.addEventListener("touchstart", function () {
			touchFun();
		});

		function touchFun() {
			touch = true;
			img.removeAttribute("draggable");
			img.addEventListener("touchmove", function (e) {
				pause = true;
				logic(this, e);
			});
			img.addEventListener("touchend", function (e) {
				move = [];
			});
		}

		//For Non-Touch Devices
		function nonTouch() {
			touch = false;
			if (draggable) {
				var drag = false;
				img.addEventListener("mousedown", function (e) {
					pause = true;
					drag = true;
					logic(this, e);
				});
				img.addEventListener("mouseup", function (e) {
					drag = false;
					move = [];
				});
				mouseEvent();
			}

			function mouseEvent() {
				img.addEventListener("mousemove", function (e) {
					drag ? logic(this, e) : null;
				});
				img.addEventListener("mouseleave", function () {
					move = [];
				});
			}
		}

		function logic(el, e) {
			j++;
			var x = touch ? e.touches[0].clientX : e.clientX;
			var coord = x - img.offsetLeft;
			move.push(coord);

			var l = move.length,
				oldMove = move[l - 2],
				newMove = move[l - 1];
			var thresh = touch ? true : !(j % 3);
			if (thresh) {
				if (newMove > oldMove) nxt(el);
				else if (newMove < oldMove) prev(el);
			}
		}
	}

	var interval,
		pause = false,
		left = false,
		right = false,
		speed = 100;

	var play = () => {
		timer(speed);
	};

	function timer(t) {
		clearInterval(interval);
		interval = setInterval(function () {
			if (!pause) {
				if (left) prev(img);
				else if (right) nxt(img);
				else nxt(img);
			}
		}, t);
	}

	function prev(e) {
		if (i <= 1) {
			i = n;
			e.src = `${picList[i]}`;
		} else {
			e.src = `${picList[i - 1]}`;
			i = i - 1;
		}
	}

	function nxt(e) {
		if (i >= n) {
			i = 0;
			e.src = `${picList[i]}`;
		} else {
			e.src = `${picList[i + 1]}`;
			i = i + 1;
		}
	}

	if (autoPlay) {
		pause = false;
		play();
	}
}
