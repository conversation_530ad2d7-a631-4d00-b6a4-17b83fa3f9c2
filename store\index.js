import { generateUUID } from "@/utils/utils";
import { getCurrency } from "@/api/pins";
import { getAllColorCard, getLayoutData, getProId, getUuid, isLogin,getProjectStructuringByProId } from "@/api/web.js";

export const state = () => ({
	twinsLeftOrRight:null,
	structuringList: null,
	neonPreview: {},
	enableTurnaroundTimeCheck: 0,
	quoteSizeValue: "", //报价选中尺寸的值
	quoteSizeName:"",
	embroideryCoverageValue:"",
	quoteEmbroideryCoverage:"",
	quoteQuantity: 0, //数量框
	orderIdentification: "", //订单号字母前缀
	proId: 0,
	proType: 0,
	proName: "",
	proIcon: "",
	proUrl: "",
	domain: "",
	domainFull: "",
	languageUrl: "",
	logoAltTitle: "",
	proTheme: 4,
	proSystem: {},
	isTemporary: 0,
	themeFontConfig: {},
	neonDialogVisable: false,
	token: "",
	userInfo: {},
	userUUID: "",
	pagePath: "",
	showMask: false,
	showSizeDialog: false,
	inputRefName: "replayUpload",
	overSizeList: [],
	tempCart: [],
	paymentList: [],

	layoutInfo: { pc: [], mb: [] },
	cartQuantity: 0,
	cartTotalAmount: 0,

	continentList: [],
	countryList: [],
	country: {},
	language: {},
	languageLink: [],
	currencyList: [],
	currency: {},

	//报价系统参数
	quoteCurrency: {},
	quoteCurrencyList: [],
	device: "",
	loginEmail: "",
	canvasLoading: false,
	colorList: [],
	manageMobile: 0, //buildweb 是否展示手机端
	buildWebLoading: true, //buildweb是否加载中
	quoteData: null,
	morePriceData: null,
	showSmallPrice: false,
	userIp: "",
	storeUploadSwitch: false,
	advertConfig: {
		googleConfig: {},
	},
	quoteTotalPrice: 0, //报价总价
	// languageItem:''
	areaCode: "",
	cateType: 0, //页面类型,
	googleSessionId: 0,

	//fd Description 信息
	fdPriceDes: "",
	fdServiceDes: "",
});

export const getters = {
	isManage: (state) => {
		return process.env.isManage;
	},
	getAdvertisementConfig: (state) => {
		return (pt) => {
			let advertConfig = state.advertConfig,
				lang = state.language.language,
				countryCode = state.country.countryCode,
				googleConfig,
				config;
			if (!advertConfig?.googleConfig) {
				return false;
			}
			try {
				googleConfig = JSON.parse(advertConfig.googleConfig);
				switch (pt) {
					case "googleInquiryConfig":
						config =
							googleConfig.Inquiry.find((item) => {
								return item?.country?.includes(countryCode);
							}) ||
							googleConfig.Inquiry.find((item) => {
								return item?.country?.includes("us");
							}) ||
							googleConfig.Inquiry[0];
						break;
					case "googlePurchaseConfig":
						config =
							googleConfig.Purchase.find((item) => {
								return item?.country?.includes(countryCode);
							}) ||
							googleConfig.Purchase.find((item) => {
								return item?.country?.includes("us");
							}) ||
							googleConfig.Purchase[0];
						break;
					case "criteoConfig":
						if (googleConfig[`criteoConfig_${lang}`]) {
							config = googleConfig[`criteoConfig_${lang}`][0];
						} else {
							config = googleConfig["criteoConfig"][0];
						}
						break;
					case "lintrkConfig":
						config = googleConfig["lintrkConfig"][0];
						break;
				}
			} catch (e) { }
			return config;
		};
	},
	lang: (state) => require(`~/assets/lang/${state.language.language || "en"}.json`),
	// langItem: (state) => require(`~/assets/lang/${state.languageItem || 'en'}.json`),
	isMobile: (state) => state.manageMobile || (process.browser ? (window.innerWidth <= 1000) : (state.device == "mb")),
	shadeColor: () => (color, percent) => {
		if (!color) return false;
		let R = parseInt((parseInt(color.substring(1, 3), 16) * (100 + percent)) / 100),
			G = parseInt((parseInt(color.substring(3, 5), 16) * (100 + percent)) / 100),
			B = parseInt((parseInt(color.substring(5, 7), 16) * (100 + percent)) / 100);
		R = R < 255 ? Math.round(R) : 255;
		G = G < 255 ? Math.round(G) : 255;
		B = B < 255 ? Math.round(B) : 255;
		let RR = R.toString(16).length == 1 ? "0" + R.toString(16) : R.toString(16),
			GG = G.toString(16).length == 1 ? "0" + G.toString(16) : G.toString(16),
			BB = B.toString(16).length == 1 ? "0" + B.toString(16) : B.toString(16);
		return "#" + RR + GG + BB;
	},
	setTheme: (state, getters) => (theme) => {
		if (process.browser && theme) {
			theme["color-primary-opacity"] = theme["color-primary"] + "CC";
			theme["color-primary-lighten"] = getters.shadeColor(theme["color-primary"], 20);
			theme["color-primary-darken"] = getters.shadeColor(theme["color-primary"], -20);
			document.documentElement.style = "";
			for (let key in theme) {
				document.documentElement.style.setProperty("--" + key, theme[key]);
			}
		}
	},
	deepMerge: () => (old, now) => {
		let deepMerge = (old, now) => {
			if (!old || !old.length) old = now;
			else if (now && now.length)
				old.map((old_item, old_index) => {
					const now_item = now[old_index] || (old_item.id && now.find((i) => old_item.id == i.id)); //找到两端对应的模板
					if (!old_item || now_item === 0 || now_item === false) old_item = now_item;
					else if (typeof now_item == "object")
						for (const key in now_item) {
							if (!old_item[key] || now_item[key] === 0 || now_item[key] === false) old_item[key] = now_item[key];
							else if (Array.isArray(old_item[key]) && Array.isArray(now_item[key])) old_item[key] = deepMerge(old_item[key], now_item[key]); // 数组继续分解
							else if (typeof old_item[key] == "object" && typeof now_item[key] == "object") Object.assign(old_item[key], now_item[key]);
							else old_item[key] = now_item[key];
						}
					else old_item = now_item;
				});
			return old;
		};
		return deepMerge(old, now);
	},
	getIcon: () => (icon) => {
		if (!icon) return "";
		let iconName;
		const reg = /(&#x)?(.*)/;
		if (reg.test(icon)) iconName = reg.exec(icon)[2];
		return String.fromCharCode(parseInt(iconName, 16));
	},
	isLogin(state) {
		return !!state.token;
	},
	currencySymbol(state) {
		return state.currency?.symbol;
	},
	currencyRate(state) {
		return state.currency?.rate;
	},
	morePriceData(state) {
		return state.morePriceData;
	},
	showSmallPrice(state) {
		return state.showSmallPrice;
	},

	projectComment(state) {
		return state.projectComment;
	},
	getManageMobile(state) {
		return state.manageMobile;
	},
	getBuildWebLoading(state) {
		return state.buildWebLoading;
	},
	getTextAlign(state) {
		return state.textAlign;
	},
};

export const actions = {
	async nuxtServerInit({ commit, state }, { req, app, route, redirect }) {
		if (!process.env.isManage) {
			try {
				// console.log('路由：', route.path, route.name);
				// 用户访问ip地址存储
				state.userIp = req.headers["x-forwarded-for"] || "";
				const domain = req && req.headers["host"];

				// 设置项目信息
				let projectData = (await getProId(domain, route.fullPath)).data;
				const project = projectData.project;
				app.$cookies.set("site_proId", project.id, { path: "/", maxAge: 86400 * 360 });
				state.orderIdentification = project.orderIdentification;
				state.proId = project.id;
				state.proUrl = project.url;
				state.proType = project.proType;
				state.proName = project.proName;
				//	state.quoteQuantity = project.quoteQuantity;
				state.proIcon = project.proIcon;
				state.isTemporary = project.isTemporary;
				state.logoAltTitle = project.logoAltTile;
				state.domain = "https://" + domain;
				state.domainFull = "https://" + domain + route.fullPath;
				state.languageUrl = projectData.languageUrl;
				state.themeFontConfig = projectData.themeFontConfig;
				state.proSystem = {
					logo: project.proLogo,
					logoFooter: project.proLogoFooter,
					...project.sysProOwner,
				};
				state.advertConfig = projectData.advertConfig;
				state.projectComment = project;

				if (process.env.NODE_ENV == "production" && state.domainFull && projectData.languageUrl && state.domainFull != projectData.languageUrl) return redirect(projectData.languageUrl);

				// 获取cookie中的语言信息
				let lang = typeof app.$cookies.get("site_language") == "string" ? app.$cookies.get("site_language").split("-") : [];
				state.language = {
					path: route.path,
					countryCode: !route.path.startsWith("/user/") ? projectData.country || lang[0] : lang[0] || projectData.country,
					language: !route.path.startsWith("/user/") ? projectData.language || lang[1] : lang[1] || projectData.language,
				};

				// 语言
				state.continentList = projectData.continentList;
				state.countryList = state.continentList.reduce((a, b) => a.concat(b.countryList), []);
				state.country = state.countryList.find((c) => c.countryCode == state.language.countryCode) || state.countryList[0];
				commit("setLanguage", state.country.childList.find((l) => l.language == state.language.language) || state.country.childList[0]);

				//公共结构化数据
				try {
					let publicProjectData =await getProjectStructuringByProId();
					state.structuringList = publicProjectData?.data?.structuring;
				}catch (e) {

				}

				// console.log('link: ', state.domainFull, state.languageUrl)
				// console.log('country: ', projectData.country, lang[0], state.language.countryCode)
				// console.log('language:', projectData.language, lang[1], state.language.language)

				// 设置用户信息
				let token = app.$cookies.get("token"),
					userUUID = app.$cookies.get("userUUID"),
					userInfo = {};
				if (token) {
					//解决token过期后进入网站不显示页头页尾问题
					try {
						const userInfoData = await isLogin();
						userInfo = userInfoData.data.userInfo;
					} catch (e) {
						token = null;
						userInfo = null;
					}
				} else if (!userUUID) {
					const userUUIDData = await getUuid();
					userUUID = userUUIDData.data.uuid;
				}
				commit("setUserInfo", { token, userUUID, userInfo });

				let result = await Promise.all([getCurrency(), getLayoutData(token ? { userId: userInfo.id } : { uuid: userUUID })]);

				// 货币
				let currency = result[0];
				state.currencyList = currency.data;
				state.currency = state.currencyList.find((i) => i.code == state.language.currency) || state.currencyList.find((i) => i.code == app.$cookies.get("site_currency")) || state.currencyList[0];


				// 页头页尾
				let layoutData = result[1];
				layoutData?.data?.pageRowList?.map((item, index) => {
					if (index == 0) state.proTheme = JSON.parse(item.sampleData).theme;
					state.layoutInfo.pc.push(JSON.parse(item.sampleData));
					state.layoutInfo.mb.push(JSON.parse(item.mbSampleData));
				});
				state.cartQuantity = layoutData?.data?.cartQuantity; //购物车数量
				state.cartTotalAmount = layoutData?.data?.cartTotalAmount;
			} catch (e) {
				console.log("err", e);
				app.$cookies.remove("site_proId");
			}
		}
		state.manageMobile = app.$cookies.get("manageMobile");
	},
	updateHeadFootPages({ state, commit }) {
		return new Promise(async (resolve) => {
			const { data } = await getLayoutData({
				userId: state.token ? state.userInfo.id : null,
				uuid: state.token ? null : state.userUUID,
			});
			commit("setCartQty", data);
			resolve(data);
		});
	},
	setProDom: ({ state, getters }) => {
		let welcome = getters.lang.default.welcomeTo + (state.logoAltTitle || (state.proUrl?.replace(/www.|.com/, "").replace(/\./g, " ") + "."));
		setTimeout(() => {
			// 模板内h2标签添加域名欢迎
			document.querySelectorAll("h2").forEach((i) => i.setAttribute("data-before", welcome));
			// 锚点滚动到顶部的距离留出页头的高度
			document.documentElement.style.setProperty("--scroll-padding-top", (document.querySelector("#modalHeader")?.offsetHeight || 68) + "px");
		}, 100);
	},
	getColorCode({ commit }) {
		getAllColorCard().then((res) => {
			commit("setColorList", res.data);
		});
	},
};

export const mutations = {
	setTwinsLeftOrRight(state,data){
       state.twinsLeftOrRight = data
	},
	// getItemLang(state,lang) {
	// 	state.languageItem = lang;
	// },
	setNeonPreview(state,data){
		state.neonPreview = data;
	},
	set_turnaroundTimeCheckStatus(state,data){
		state.enableTurnaroundTimeCheck = data;
	},
	quoteQuantity(state, data) {
		state.quoteQuantity = data;
	},
	set_currency(state, currency) {
		state.currencyList = currency;
		state.currency = state.currencyList.find((i) => i.code == state.language.currency) || state.currencyList.find((i) => i.code == app.$cookies.get("site_currency")) || state.currencyList[0];
	},
	setQuoteSizeName(state, data) {
		state.quoteSizeName = data;
	},
	setQuoteSizeValue(state, data) {
		state.quoteSizeValue = data;
	},
	setEmbroideryCoverageValue(state,data){
		state.embroideryCoverageValue = data;
	},
	set_areaCode(state, data) {
		state.areaCode = data;
	},
	SET_UploadSwitch(state, bool) {
		state.storeUploadSwitch = bool;
	},
	setBuildWebLoading(state, data) {
		state.buildWebLoading = data;
	},
	setManageMobile(state, data) {
		state.manageMobile = data;
		// this.$cookies.set("manageMobile", data, "0");
		this.$cookies.set("manageMobile", data, {
			path: "/",
			maxAge: 60 * 60 * 24 * 7,
		});
	},
	setColorList(state, data) {
		state.colorList = data;
	},

	setShowSmallPrice(state, data) {
		state.showSmallPrice = data;
	},
	setMorePriceData(state, data) {
		state.morePriceData = data;
	},
	setUserInfo(state, info) {
		if (info.token) state.token = info.token;
		if (info.token)
			this.$cookies.set("token", info.token, {
				path: "/",
				maxAge: 60 * 60 * 24 * 7,
			});

		if (info.userUUID) state.userUUID = info.userUUID;
		if (info.userUUID)
			this.$cookies.set("userUUID", info.userUUID, {
				path: "/",
				maxAge: 60 * 60 * 24 * 7,
			});

		if (info.userInfo) state.userInfo = info.userInfo;

		if (info.proId) state.proId = info.proId;
	},
	logOut(state) {
		state.token = null;
		state.userInfo = null;
		if (process.client) {
			document.cookie = "token='';expires=" + new Date(-1) + ";path=/";
			document.cookie = "userInfo='';expires=" + new Date(-1) + ";path=/";
			location.reload();
		}
	},
	setProSystem(state, proSystem) {
		if (proSystem.proTheme) state.proTheme = proSystem.proTheme;
		state.proSystem = {
			...state.proSystem,
			...proSystem,
		};
	},
	setMask(state, showMask) {
		state.showMask = state.isTemporary ? "temporary" : showMask;
	},
	setSizeDialog(state, showSizeDialog) {
		state.showSizeDialog = showSizeDialog;
	},
	setInputRefName(state, inputRefName) {
		state.inputRefName = inputRefName;
	},
	setOverSizeList(state, Array) {
		state.overSizeList = Array;
	},
	setCartQty(state, data) {
		state.cartQuantity = data.cartQuantity;
		state.cartTotalAmount = data.cartTotalAmount;
	},
	setPagePath(state, data) {
		state.pagePath = data.replace(state.language.lang + "/", "/").replace(/\/$/g, "") || "/";
		state.showMask=false
	},
	setLanguage(state, data) {
		if (!data) return;

		let link = state.languageLink?.find((i) => i.countryLanguageCode?.toLowerCase() == data.language + "-" + data.countryCode);
		let lang = [];
		// 公共域名带国家标识
		if ((link?.domainUrl || data.domainUrl) == state.proUrl) lang.push(data.countryCode);
		// 非默认语言带语言标识
		if (!data.isDefault) lang.push(data.language);
		// 单语言网站不带标识
		data.lang = (state.countryList.length > 1 || state.country.childList.length > 1) && lang.length ? "/" + lang.join("-") : "";

		this.$cookies.set("site_language", data.countryCode + "-" + data.language, { path: "/", maxAge: 86400 * 360 });
		this.$cookies.set("site_currency", data.currency, { path: "/", maxAge: 86400 * 360 });

		if (process.browser) {
			let code = !lang.length && location.pathname.startsWith("/user/") ? "/" + data.language : data.lang;
			if (process.env.NODE_ENV == "development") console.log(state.language.lang && location.pathname.startsWith(state.language.lang + "/") ? location.pathname.replace(state.language.lang, data.lang) : code + location.pathname);
			else if (link) location.href = link.href;
			else location.href = "https://" + (data.domainUrl || location.host) + (state.language.lang && location.pathname.startsWith(state.language.lang + "/") ? location.pathname.replace(state.language.lang, data.lang) : code + location.pathname) + location.search;
		} else state.language = data;
	},
	setLanguageUrl(state, data) {
		state.languageUrl = data;
	},
	setCurrency(state, data) {
		state.currency = data;
		this.$cookies.set("site_currency", data.code, { path: "/", maxAge: 86400 * 360 });
		try {
			window.qiankunActions.setGlobalState({ currency: data });
		} catch (e) { }
	},
	setNeonDialogVisable(state, data) {
		state.neonDialogVisable = data;
	},
	setCanvasLoading(state, data) {
		state.canvasLoading = data;
	},
	SetDeviceType(state, data) {
		state.device = data;
	},
	setLoginEmail(state, data) {
		state.loginEmail = data;
	},
	setTextAlign(state, data) {
		state.textAlign = data;
	},
	setCateType(state, data) {
		state.cateType = data;
	},
	setLanguageLink(state, data) {
		state.languageLink = data;
	},
	set_total(state, data) {
		state.quoteTotalPrice = data;
	},
	setGoogleUploadAction(state, data) {
		if (state.googleSessionId == 0) {
			state.googleSessionId = generateUUID();
		}
		//记录用户的选择
		try {
			if (window.$nuxt.$store.state.advertConfig.googleConfig && window.gtag) {
				gtag("event", "select_content", {
					content_type: "userSelectStep",
					sessionId: state.googleSessionId,
					userId: state.token ? state.userInfo.id : null,
					uuid: state.token ? null : state.userUUID,
					...data,
				});
			}
		} catch (error) { }
	},
	setFdTimeDes(state, data) {
		state.fdPriceDes = data.fdPriceDes;
		state.fdServiceDes = data.fdServiceDes;
	}
};
