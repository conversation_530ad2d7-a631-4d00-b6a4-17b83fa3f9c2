import { request } from '~/utils/request'

// 获取项目套餐列表
export function getWebLevel() {
	return request({
		url: '/retailer/webSiteServe/get/webLevel',
		method: 'get',
	})
}

// 获取项目套餐详情
export function getWebLevelInfo(id) {
	return request({
		url: '/retailer/webSiteServe/get/webLevelInfo?id='+ id,
		method: 'get',
	})
}

//获取额外套餐列表
export function getExtraService() {
	return request({
		url: '/retailer/webSiteServe/get/extraService',
		method: 'get',
	})
}

//查询信用卡列表
export function getCardPayList() {
	return request({
		url: '/retailer/pay/to-config/list',
		method: 'get',
	})
}

//查询信用卡列表
export function subscribeWebLevel(data) {
	return request({
		url: '/retailer/webSiteServe/subscribeWebLevel',
		method: 'post',
		data,
	})
}

// 购买额外服务
export function buyExtraServe(data) {
	return request({
		url: '/retailer/webSiteServe/buyExtraServe',
		method: 'post',
		data,
	})
}

// 购买额外服务
export function getExtraServiceInfo(id) {
	return request({
		url: '/retailer/webSiteServe/get/getExtraServiceInfo?id=' + id,
		method: 'get',
	})
}
