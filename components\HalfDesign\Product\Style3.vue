<template>
	<div class="mb-4" :class="stepData.styleName">
		<half-design-select-tip :customText="stepData.attributeTitle" :selectedValue="shape"></half-design-select-tip>
		<slot name="stepText"></slot>
		<div class="step-content">
			<div class="step-item" :class="{active:index===selectIndex}" v-for="(step,index) in stepData.productParamList"
					 :key="index"
					 @click="selectStep(step,index)">
				<div class="imgWrap">
					<img :src="step.imgDetail" :alt="step.valueName" :title="step.valueName">
				</div>
				<div class="d-flex-center text-center pa-1">
					<div class="text-truncate">
						{{ step.valueName }}
					</div>
				</div>
			</div>
		</div>
		<div class="errorTip">
			<v-alert
				dense
				outlined
				type="error"
			>
				{{ langSemiCustom.errorTip }}
			</v-alert>
		</div>
	</div>
</template>
<script>
export default {
	props: {
		stepData: {
			type: Object,
			default: {}
		}
	},
	data() {
		return {
			selectIndex: -1,
			selectItem: null
		}
	},
	computed: {
		shape() {
			return this.selectItem?.valueName
		},
		langSemiCustom() {
			return this.$store.getters.lang?.semiCustom;
		}
	},
	methods: {
		selectStep(item, index) {
			this.selectIndex = index;
			this.selectItem = item;
			this.$emit('selectStep', {
				type: this.stepData.attributeFlag,
				data: item,
				parent: this.stepData,
				id: this.stepData.id
			})
		},
	},
	mounted() {

	}
}
</script>
<style scoped lang="scss">
@import "~assets/css/half.scss";

.style1 .step-content {
	display: grid;
	grid-template-columns: repeat(5, 1fr);
	grid-gap: 10px;

	.step-item {
		min-width: 0;
		cursor: pointer;
		padding: 10px 10px 0;
		background-color: $background-color;
		border-radius: $step-border-radius;
		border: 2px dashed transparent;
		@media (any-hover: hover) {
			&:hover {
				outline-color: $color-primary;
				background-color: var(--color-second);
			}
		}

		.imgWrap {
			@include flex-center;
			padding: 10px;
			height: 100px;
			border: 2px dashed #CCCCCC;
		}
	}

	.step-item.active {
		border-color: $color-primary;
		background-color: var(--color-second);
	}
}

@include respond-to(mb) {
	.style1 .step-content {
		grid-template-columns: repeat(4, 1fr);
		grid-gap: 5px;

		.step-item {
			padding: 10px 10px 0;

			.imgWrap {
				padding: 5px;
				height: 60px;
			}
		}
	}
}
</style>
