<template>
	<div class="mb-4" :class="stepData.styleName" :id="`copy${stepData.id}`">
		<half-design-litter-title :index="stepData.id" :data-name="`${stepData.styleClass + '_' + stepData.id}`"
			style="margin: 6px 0" :stepTitle="stepData.minStepTitle">
			{{ stepData.attributeTitle }}
		</half-design-litter-title>
		<slot name="stepText"></slot>
		<template v-if="(modeType === 1 || modeType === 3) && !tile">
			<div class="textArea" v-for="(item, index) in textArr">
				<v-text-field class="inputText" :value="item.text" solo flat outlined dense hide-details
					:disabled="textFieldDisabled" :label="langSemiCustom.enterHere" @input="addText($event, item)"
					@blur="textArrBlur()" />

				<v-select :value="item.fontFamily" :menu-props="{ bottom: true, offsetY: true }" solo flat outlined
					dense hide-details :items="fontsData" item-text="name" item-value="name" :label="langSemiCustom.ff"
					@change="changeTextProperty($event, 'fontFamily', item)" @blur="textArrBlur()">
					<template #item="{ item }">
						<span :style="{ fontFamily: item.name }">{{ item.name }}</span>
					</template>
				</v-select>
				<div class="font-bold" :class="{ active: item.fontWeight === 'bold' }"
					@click="changeTextProperty(item.fontWeight === 'normal' || item.fontWeight === 400 ? 'bold' : 'normal', 'fontWeight', item)">
					B</div>
				<div class="font-style" :class="{ active: item.fontStyle === 'italic' }"
					@click="changeTextProperty(item.fontStyle === 'normal' || !item.fontStyle ? 'italic' : 'normal', 'fontStyle', item)">
					I</div>
				<v-menu :close-on-content-click="true" offset-y min-width="300">
					<template v-slot:activator="{ on, attrs }">
						<div class="font-color" :style="{ '--bg-color': item.fill }" tabindex="0" v-bind="attrs"
							v-on="on">
							<div>A</div>
						</div>
					</template>
					<v-card class="color-picker-wrap" color="#ffffff">
						<div class="color-picker-title">{{ langSemiCustom.ec }}</div>
						<div class="color-picker-list">
							<div class="color-item" :class="{ active: colorItem.code === item.fill }"
								v-for="colorItem in colorList" :key="colorItem.id"
								:style="{ backgroundColor: colorItem.code }" :title="colorItem.pantone"
								@click="changeTextProperty(colorItem, 'fill', item)">
								<v-icon color="#ffffff" small>mdi-check</v-icon>
							</div>
						</div>
					</v-card>
				</v-menu>
				<template v-if="index === 0">
					<div class="line"></div>
					<v-tooltip bottom v-model="tooltipOpen">
						<template v-slot:activator="{ on, attrs }">
							<div class="addNewLine" v-bind="attrs" v-on="on" @click="addNewText"><b
									class="icon-a-icon-dzlj-addzhuanhuan"></b>{{ langSemiCustom.add }}</div>
						</template>
						<span>{{ langSemiCustom.al }}</span>
					</v-tooltip>
				</template>
				<template v-else>
					<div class="line"></div>
					<div class="addNewLine"><b class="icon-shanchu2 extend-click-area" @click="delText(index)"></b></div>
				</template>
			</div>
		</template>
		<template v-if="modeType === 3 || modeType === 2 || modeType === 4">
			<div class="imgArea" v-show="!uploadImgList.length" @click="triggerUpload">
				<div class="iconWrap">
					<b class="icon-shangchuan"></b>
					<span>{{ langSemiCustom.up }}</span>
				</div>
				<div class="tipText">
					<span>{{ langSemiCustom.fileType }}</span>
				</div>
				<input type="file" :multiple="fileMultiple" accept=".jpg,.jpeg,.png,.svg,.gif,.bmp" ref="upload" @click.stop
					@change="uploadPic" />
			</div>
			<div class="imgArea2" v-show="!uploadImgList.length" @click="triggerUpload">
				<div class="iconWrap">
					<b class="icon-shangchuan"></b>
					<span>{{ langSemiCustom.up }}</span>
				</div>
				<div class="tipText2">
					{{ lang.maxSize80 }}
					<ToolTip class="uploadTip" :color="'rgba(0,0,0,0.9)'" :textColor="'#fff'"
						:titleContent="'Allowed extensions: EPS, AI, PDF, JPG, JPEG, GIF, PNG, BMP, TIF, SVG, WORD, EXCEL, TXT, WEBP.'">
					</ToolTip>
				</div>
				<input type="file" :multiple="fileMultiple" accept=".jpg,.jpeg,.png,.svg,.gif,.bmp" ref="upload" @click.stop
					@change="uploadPic" />
			</div>
			<div class="uploadImgArea" v-show="uploadImgList.length">
				<div class="imgListWrap">
					<div class="imgList custom-scrollbar">
						<div class="imgItem" :class="{ active: item.active }" v-for="(item, index) in uploadImgList"
							:key="index">
							<div class="imgWrap" @click="changeImgStatus(item)">
								<img :src="item.secure_url" alt="upload" title="upload" />
								<b class="icon-shanchu2 close" @click.stop="delImg(item, index)"></b>
								<b class="icon-xuanze_xuanzhong select"></b>
							</div>
							<div class="imgName" :title="item.original_filename">
								{{ item.original_filename }}
							</div>
						</div>
					</div>
				</div>
				<div class="changeImg">
					<b class="icon-a-lrgl-colorszhuanhuan" @click="editLogo" v-if="!tile"></b>
					<span class="edit" @click="editLogo" v-if="!tile">{{ langSemiCustom.elc }}</span>
                    <span v-if="tile" @click="triggerUpload">{{ langSemiCustom.chooseLogo }}</span>
					<button @click="triggerUpload">{{ langSemiCustom.up }}</button>
				</div>
			</div>
		</template>
		<div class="control-area" v-if="!tile">
			<div class="control-text" v-if="showTextControl && !showImgControl">
				<div class="control-item" v-for="item in textIcon" :key="item.val"
					@click="changeTextPosition(item.val)">
					<b :class="item.icon"></b>
				</div>
			</div>
			<div class="control-image" v-if="showImgControl && !showTextControl">
				<div class="control-item" v-for="item in imgIcon" :key="item.val"
					@click="changeImagePosition(item.val)">
					<b :class="item.icon"></b>
				</div>
			</div>
			<div class="control-all" v-if="showImgControl && showTextControl">
				<div class="control-item" v-for="item in allIcon" :key="item.val" @click="changeAllPosition(item.val)">
					<b :class="item.icon"></b>
				</div>
			</div>
		</div>
		<textarea v-show="hasMoreColor" class="myTextarea" :placeholder="langSemiCustom.dc" ref="myTextarea"
			:value="$store.state.halfDesign.colorRemark" @input="updateColorRemark"></textarea>
		<div class="errorTip">
			<v-alert dense outlined type="error">
				{{ langSemiCustom.errorTip }}
			</v-alert>
		</div>
	</div>
</template>
<script>
import {
	checkImgSize,
	checkImgType,
	deepClone,
	generateUUID,
	readFileAsBase64,
	urlAddVersion,
	analyzeImageColor,
	rgbToHex,
	removeImageColor
} from "@/utils/utils";

import canvas from "assets/js/halfDesign/canvas";
import { DataProcessing } from "@/utils/dataProcessing";
import ToolTip from '@/components/HalfDesign/customMedals/common/ToolTip'
export default {
	props: {
		stepData: {
			type: Object,
			default: {},
		},
	},
	components: { ToolTip },
	watch: {
		uploadImgList: {
			handler(newVal) {
				if (newVal.length > 0) {
					this.selectItem.files = newVal.map(item => item.secure_url)
				} else {
					this.selectItem.files = []
				}
				//咬版有使用
				if(this.stepData.attributeFlag === 'artwork') {
					this.$emit("selectStep", {
						type: this.stepData.attributeFlag,
						data: this.selectItem,
						parent: this.stepData,
						id: this.stepData.id,
						copyId: 'copy' + this.stepData.id,
						firstSelect: false,
					});
				}
			},
			deep: true
		}
	},
	data() {
		return {
            tooltipOpen: false,
			comments: "",
			textIcon: [
				{
					icon: "icon-a-lrgl-text-leftzhuanhuan",
					val: "left",
				},
				{
					icon: "icon-a-lrgl-text-centerzhuanhuan",
					val: "center",
				},
				{
					icon: "icon-a-lrgl-text-rightzhuanhuan",
					val: "right",
				},
			],
			imgIcon: [
				{
					icon: "icon-D-center",
					val: "center",
				},
				{
					icon: "icon-D-left",
					val: "left",
				},
				{
					icon: "icon-D-right",
					val: "right",
				},
			],
			allIcon: [
				{
					icon: "icon-T-right",
					val: "right",
				},
				{
					icon: "icon-T-left",
					val: "left",
				},
				{
					icon: "icon-T-down",
					val: "down",
				},
				{
					icon: "icon-T-up",
					val: "up",
				},
			],
			//颜色选项卡
			selectColorModel: {
				uploadModel: [],
				inputModel: [],
			},
			textFieldDisabled: false, //input框加载进度
		};
	},
	computed: {
        tile(){
            return this.$store.state.halfDesign.tile
        },
        fileMultiple(){
            return !this.tile
        },
		hasMoreColor() {
			return this.$store.state.halfDesign.hasMoreColor;
		},
		modeType() {
			//1.简单模式，2.复杂图片，3.照片模式
			return this.$store.state.halfDesign.modeType;
		},
		showTextControl() {
			let show = false;
			let find = this.textArr.find((item) => item.text);
			if (find) {
				show = true;
				return show && (this.modeType === 1 || this.modeType === 3);
			}
			return show && (this.modeType === 1 || this.modeType === 3);
		},
		showImgControl() {
			return this.uploadImgList.length && (this.modeType === 2 || this.modeType === 3 || this.modeType === 4);
		},
		colorList() {
			return this.$store.state.colorList;
		},
		fontsData() {
			return require("@/assets/json/fontList.json");
		},
		lang() {
			return this.$store.getters.lang.quote || {};
		},
		langSemiCustom() {
			return this.$store.getters.lang?.semiCustom;
		},
		textArr() {
			return this.$store.state.halfDesign.textArr;
		},
		uploadImgList() {
			return this.$store.state.halfDesign.uploadImgList;
		},
	},
	mounted() {
		this.init();
		this.$Bus.$on("currentArea", this.showUploadBox);
		this.$Bus.$on("selectDefaultArtworkStep", this.selectDefault);
	},
	beforeDestroy() {
		this.$Bus.$off("inputTextLoading");
		this.$Bus.$off("selectDefaultArtworkStep", this.selectDefault);
		this.$Bus.$off("currentArea", this.showUploadBox);
	},
	methods: {
		init() {
			//this.initCss();
			/* 监听color状态 */
			this.getInputTextLoading();
			/* 监听color状态end */
		},
		/**
		 * 加载输入框
		 * 兄弟组件在 components/HalfDesign/Size/Style5.vue 目录下
		 */
		getInputTextLoading() {
			this.$Bus.$off("inputTextLoading");
			this.$Bus.$on("inputTextLoading", (res) => {
				this.textFieldDisabled = true;
				//这里要等待左侧canvas对象有值情况下false.  如果超时则 3秒后自动取消
				setTimeout(() => {
					let c = canvas.getElementByType("i-text");
					this.textFieldDisabled = false;
				}, 3000);
			});
		},
		initCss() { },
		updateColorRemark(e) {
			let val = e.target.value;
			this.$store.commit("halfDesign/updateColorRemark", val);
		},
		changeImgStatus(item) {
			this.$store.commit("halfDesign/updateImgStatus", item);
			if (item.active) {
				canvas.activeElementById(item.id);
			} else {
				canvas.c.discardActiveObject();
				canvas.c.requestRenderAll();
			}
		},
		editLogo() {
			let activeImgArr = this.uploadImgList.filter((item) => item.active);
			if (!activeImgArr.length) {
				activeImgArr = this.uploadImgList;
			}
			this.$store.commit("halfDesign/editLogo", activeImgArr);
		},
		changeTextPosition(position) {
			let elementArr = canvas.getElementByType("i-text");
			elementArr.forEach((item) => {
				canvas.changeElementPosition({
					item,
					position,
				});
			});
		},
		changeImagePosition(position) {
			let activeImgArr = this.uploadImgList.filter((item) => item.active);
			let elementArr = canvas.getElementByType("image");
			if (activeImgArr.length) {
				let arr = [];
				activeImgArr.forEach((item) => {
					let find = elementArr.find((c) => c.id === item.id);
					if (find) {
						arr.push(find);
					}
				});
				elementArr = arr;
			}
			elementArr.forEach((item) => {
				canvas.changeElementPosition({
					item,
					position,
				});
			});
		},
		changeAllPosition(position) {
			let activeImgArr = this.uploadImgList.filter((item) => item.active);
			let elementArr = canvas.getElementByType("image");
			if (activeImgArr.length) {
				let arr = [];
				activeImgArr.forEach((item) => {
					let find = elementArr.find((c) => c.id === item.id);
					if (find) {
						arr.push(find);
					}
				});
				elementArr = arr;
			}
			canvas.autoPosition(position, elementArr);
		},
		addNewText() {
            setTimeout(()=>{
                this.tooltipOpen = false;
            },2000)
			this.$store.commit("halfDesign/addTextLine");
			this.textArrBlur();
		},
		delText(ind) {
			this.$store.commit("halfDesign/delTextLine", ind);
			this.textArrBlur();
		},
		addText(val, item) {
			console.log("输入框", val, item);
			if (!val.trim()) {
				this.$store.commit("halfDesign/removeTextById", item.id);
				return;
			}
			this.$emit("newAddTextBefore", {
				val,
				textItem: item,
			});
		},
		delImg(item, index) {
            this.$store.commit("halfDesign/delImg", index);
            if(this.tile){
                canvas.deEleById("tileImg");
            }
			this.bindImgSelectColorUpload();
		},
		triggerUpload() {
			this.$refs.upload.click();
		},
		changeTextProperty(val, property, item) {
			this.$emit("changeTextPropertyBefore", {
				val,
				property,
				textItem: item,
			});
			this.textArrBlur();
		},
		/**
		 * 输入框与color选项卡交互高亮
		 * 兄弟组件在 components/HalfDesign/ArtworkOptions/Style31 目录下
		 */
		jumpChildSelectImgColorInput() {
			this.$Bus.$emit("selectImgColor", this.selectColorModel);
		},
		/**
		 * 上传图片与color选项卡交互高亮
		 * 兄弟组件在 components/HalfDesign/ArtworkOptions/Style31 目录下
		 */
		jumpChildSelectImgColorUpload() {
			this.$Bus.$emit("selectImgColor", this.selectColorModel);
		},
		textArrBlur() {
			let obj = [];
			this.textArr.map((item) => {
				if (item.fill) {
					obj.push({
						key: item.fill,
						value: item.id,
					});
				}
			});
			//开始过滤
			let mergeObj = DataProcessing.mergeJson(obj);
			this.selectColorModel.inputModel = mergeObj;
			console.log("开始过滤", mergeObj);
			this.jumpChildSelectImgColorInput();
		},
		/**
		 * 解析图像中的颜色RGB值并绑定color选项卡upload高亮
		 */
		bindImgSelectColorUpload() {
			new Promise((resolve) => {
				if (this.uploadImgList.length > 0) {
					console.log("上传前", this.uploadImgList);
					let s = 0;
					let uploadImgListOld = [];
					uploadImgListOld = this.uploadImgList;
					this.uploadImgList.map((item, index) => {
						s += 1;
						analyzeImageColor(item.secure_url, {
							ignoreArr: [],
							keepCount: 9,
							removeSimilarColors: true,
						}).then((r) => {
							if (r?.length > 0) {
								r.map((j, jIndex) => {
									j.hex = rgbToHex(j.color);
								});

								uploadImgListOld[index] = {
									...item,
									colorList: r,
								};
								if (s == this.uploadImgList.length) {
									this.selectColorModel.uploadModel = uploadImgListOld;
									setTimeout(() => {
										//等图片上传完and上面计算逻辑吧。1000毫秒即可。否则报错. 支持批量上传一堆图片。 1秒后调渲染高亮方法.
										this.jumpChildSelectImgColorUpload();
									}, 1000);
								}
							}
						});
					});
				} else {
					this.jumpChildSelectImgColorUpload(); //如果删除为空[]. 则要执行一次.刷新功能
				}
			});
		},

		uploadPic(event) {
			let files = event.target.files,
				originuploadImgList = deepClone(this.uploadImgList),
				uploadPromises = [],
				uploadImgList = [];
			for (let i = 0; i < files.length; i++) {
				let file = files[i];
				if (!checkImgType(file.name)) {
					this.$toast.error("File type error");
					this.$refs.upload.value = "";
					return;
				}
				if (!checkImgSize(file.size)) {
					this.$toast.error("File size cannot exceed 80m.");
					this.$refs.upload.value = "";
					return;
				}
				uploadPromises.push(
					readFileAsBase64(file).then(async (res) => {
						let url = await removeImageColor(res);
						uploadImgList.push({
							removeBg: true,
							original_filename: file.name,
							secure_url: url,
							removeBgBeforeLink: res,
							size: (file.size / 1024).toFixed(1),
							id: generateUUID(),
							active: false,
						});
					})
				);
			}

			Promise.all(uploadPromises)
				.then(() => {
                    if(this.tile){
                        //如果是袜子的重复模式，只能上传单张图片，并且图片需要平铺在袜子上面
                        this.$store.commit("halfDesign/setUploadImgList", uploadImgList);
                        uploadImgList.forEach((item) => {
                            this.$emit("repeatImg", {
                                imgItem: item,
                            });
                        });
                    }else{
                        this.$store.commit("halfDesign/setUploadImgList", originuploadImgList.concat(uploadImgList));
                        uploadImgList.forEach((item) => {
                            this.$emit("newAddImg", {
                                imgItem: item,
								isCircle: this.isCircle,
                            });
                        });
                    }
                    this.$refs.upload.value = "";
                    //如果是建议设计并且是没有切换模式第一次上传图片，google记录
                    if (this.$store.state.halfDesign.noChangeModeType == 0) {
                        try {
                            if (window.$nuxt.$store.state.advertConfig.googleConfig && window.gtag) {
                                gtag("event", "select_content", {
                                    content_type: "modeType",
                                    content_value: this.$store.state.halfDesign.defaultModeType == 3 ? "Professional Design Mode" : "Simple Design Mode",
                                });
                            }
                        } catch (error) { }
                    }
                    this.bindImgSelectColorUpload();
				})
				.catch(() => { })
				.finally(() => { });
		},
		selectDefault() {
			if (this.stepData.productParamList.length) {
				this.selectItem = this.stepData.productParamList[0];
				this.$emit("selectStep", {
					type: this.stepData.attributeFlag,
					data: this.selectItem,
					parent: this.stepData,
					id: this.stepData.id,
					copyId: 'copy' + this.stepData.id,
					firstSelect: true,
				});
			}
		},
		showUploadBox(data){
			this.isCircle = false;
			if (data.circle && data.circle.radius && +data.circle.radius > 0) {
				this.isCircle = true;
			}
		}
	},
};
</script>
<style scoped lang="scss">
@import "~assets/css/half.scss";

.myTextarea {
	width: 100%;
	min-height: 130px;
	padding: 10px;
	border-radius: 6px;
	border: 2px solid #ede9e9;
	background-color: rgb(246, 246, 246);
	font-size: 12px;

	&:focus {
		border: 2px solid $color-primary;
	}

	&::placeholder {
		font-weight: 400;
		font-size: 16px;
		color: #999999;
	}
}

.control-area {
	display: flex;
	justify-content: flex-end;
	margin-bottom: 10px;

	.control-item {
		display: flex;
		justify-content: center;
		align-items: center;
		width: 40px;
		height: 40px;
		background: #ffffff;
		border-radius: 5px;
		border: 1px solid #cccccc;
		color: $gray-text;
		cursor: pointer;

		b.icon-T-right,
		b.icon-T-left {
			font-size: 10px;
		}

		&.active {
			border-color: $color-primary;
			color: $color-primary;
		}

		@media (any-hover: hover) {
			&:hover {
				border-color: $color-primary;
				color: $color-primary;
			}
		}

		@include respond-to(mb) {
			width: 30px;
			height: 30px;
		}
	}

	.control-text,
	.control-image,
	.control-all {
		display: flex;
		gap: 10px;
	}
}

.imgArea {
	position: relative;
	margin: 10px 0;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	text-align: center;
	border-radius: $step-border-radius;
	border: 1px dashed $border-color;
	background-color: $background-color;
	padding: 24px 10px;
	cursor: pointer;

	input[type="file"] {
		position: absolute;
		clip: rect(0 0 0 0);
	}

	@media (any-hover: hover) {
		&:hover {
			border-color: $color-primary;
		}
	}

	.iconWrap {
		display: flex;
		align-items: center;
		margin-bottom: 10px;

        @include respond-to(mb){
            margin-bottom: 5px;
        }

		b {
			font-size: 30px;
			color: $color-primary;
		}

		span {
			margin-left: 10px;
			font-weight: 700;
			color: #333333;
		}
	}

	.tipText {
		font-size: 14px;

		@include respond-to(mb) {
			font-size: 12px;
		}
	}

	strong {
		color: $color-primary;
	}

	span {
		color: $gray-text;
	}
}

.imgArea2 {
	display: none;
	position: relative;
	margin-top: 10px;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	text-align: center;
	border-radius: $step-border-radius;
	border: 1px dashed $border-color;
	cursor: pointer;
	padding: 14px 5px;
	background-color: #fff;

	.tipText2 {
		display: flex;
		align-items: center;
		justify-content: center;

		::v-deep .uploadTip {
			i {
				color: $color-primary;
			}
		}
	}

	.iconWrap {
		display: flex;
		align-items: center;

		b {
			font-size: 30px;
			color: $color-primary;
		}

		span {
			margin-left: 10px;
			font-weight: 700;
			color: #333333;
		}
	}

	@media (any-hover: hover) {
		&:hover {
			border-color: $color-primary;
		}
	}


	input[type="file"] {
		display: none;
		position: absolute;
		clip: rect(0 0 0 0);
	}
}

.uploadImgArea {
	display: flex;
	margin: 10px 0;
	border: 1px dashed $border-color;

	.imgListWrap {
		flex: 1;
		padding: 6px 10px 6px 6px;
		border-right: 1px dashed $border-color;
	}

	.imgList {
		overflow: hidden auto;
		flex: 1;
		display: grid;
		grid-template-columns: repeat(4, 1fr);
		gap: 10px 4px;
		padding: 0 10px 0 0;
		max-height: 120px;

		@include respond-to(mb) {
			grid-template-columns: repeat(3, 1fr);
			max-height: 70px;
		}

		.imgItem {
			display: flex;
			flex-direction: column;
			min-width: 0;
			height: 120px;

			@include respond-to(mb) {
				height: 70px;
			}

			&.active .imgWrap {
				border-color: $color-primary;

				.select {
					display: block;
				}
			}
		}

		.imgWrap {
			flex: 1;
			height: 0;
			position: relative;
			padding: 10px;
			background-color: #ebebeb;
			border: 1px solid transparent;

			@include respond-to(mb) {
				padding: 4px;
			}

			.select {
				display: none;
				position: absolute;
				top: 4px;
				left: 4px;
				cursor: pointer;
				font-size: 14px;
				color: $color-primary;

				@include respond-to(mb) {
					font-size: 12px;
				}
			}

			.close {
				position: absolute;
				top: 4px;
				right: 4px;
				cursor: pointer;
				font-size: 14px;

				@include respond-to(mb) {
					font-size: 12px;
				}
			}

			img {
				width: 100%;
				height: 100%;
				object-fit: contain;
			}
		}

		.imgName {
			flex-shrink: 0;
			overflow: hidden;
			max-width: 120px;
			text-overflow: ellipsis;
			padding: 0 10px;
			margin-top: 4px;
			white-space: nowrap;
			font-size: 14px;

			@include respond-to(mb) {
				font-size: 12px;
			}
		}
	}

	.changeImg {
		flex-shrink: 0;
		width: 160px;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		color: $color-primary;
		cursor: pointer;

		b {
			font-size: 25px;

			@include respond-to(mb) {
				font-size: 17px;
			}
		}

		.edit {
			font-size: 14px;
			text-decoration: underline;

			@include respond-to(mb) {
				font-size: 12px;
			}
		}

		button {
			display: flex;
			justify-content: center;
			align-items: center;
			width: 120px;
			height: 34px;
			margin-top: 10px;
			background: $color-primary;
			border-radius: 6px;
			color: #ffffff;

			@include respond-to(mb) {
				width: 90px;
				height: 25px;
				font-size: 12px;
			}
		}
	}
}

.textArea {
	display: flex;
	flex-wrap: wrap;
	align-items: center;
	gap: 4px;
	white-space: nowrap;
	margin-bottom: 10px;

	.v-input {
		overflow: hidden;
		flex: 1 0 0;
	}

	.addNewLine {
		flex: 0 0 60px;
		cursor: pointer;

		b {
			display: inline-block;
			margin-right: 8px;
			color: $color-primary;
		}

		b.icon-shanchu2 {
			color: red;
		}
	}

	.line {
		width: 1px;
		height: 24px;
		margin: 0 10px;
		background: #d3d5d7;
	}

	.font-style {
		font-style: italic;
	}

	.font-color {
		--bg-color: #cccccc;
		position: relative;
		padding: 0 5px;
		margin: 0 10px;
		text-align: center;

        @include respond-to(mb){
            font-size: 1.25em;
        }

		&::after {
			position: absolute;
			content: "";
			height: 3px;
			background-color: var(--bg-color);
			width: 15px;
			bottom: -2px;
			left: 50%;
			transform: translateX(-50%);
			box-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
		}
	}

	.font-bold,
	.font-style {
		display: flex;
		justify-content: center;
		align-items: center;
		width: 40px;
		height: 40px;
		cursor: pointer;
		border-radius: 4px;
		transition-duration: 0.15s;
		transition-property: color;
		transition-timing-function: cubic-bezier(0.25, 0.8, 0.25, 1);

        @include respond-to(mb){
            font-size: 1.25em;
        }

		@media (any-hover: hover) {
			&:hover {
				color: $color-primary;
				background-color: #efefef;
			}
		}

		&.active {
			color: $color-primary;
			background-color: #efefef;
		}
	}
}

.style2 {
	.imgArea {
		flex-direction: row;
		gap: 10px;
		padding: 14px 5px;
		background-color: #fff;

		.iconWrap {
			margin-bottom: 0;

			b {
				color: #999999;
			}

			span {
				font-size: 14px;
				color: $color-primary;
			}
		}
	}
}

.style3 {
	.imgArea {
		display: none;
	}

	.imgArea2 {
		display: flex;
	}

	.control-area {
		display: none;
	}

	@include respond-to(mb) {
		.imgArea2 {
			::v-deep .uploadTip {
				i {
					font-size: 14px !important;
				}
			}
		}
	}
}

@include respond-to(mb) {
	.v-input ::v-deep {
		.v-label {
			font-size: 14px;
		}

		input {
			font-size: 14px;
		}
	}

	.v-select ::v-deep {
		.v-select__selection {
			font-size: 14px;
		}
	}

	.imgArea,
	.imgArea2 {
		margin: 10px 0;
		padding: 5px;
		font-size: 12px;
	}

	.uploadImg {
		margin-top: 10px;

		.imgWrap {
			width: 80px;
			height: 80px;
			padding: 5px;
		}

		.imgName {
			flex: 1;
			padding: 0 10px;
			font-size: 12px;
		}

		.changeImg {
			font-size: 12px;
		}
	}

	.textArea {
		.inputText {
			flex-basis: 100%;
		}

		.font-bold,
		.font-style {
			margin-right: 5px;
		}
	}
}
</style>
