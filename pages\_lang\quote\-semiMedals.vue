<template>
	<div>
		<SemiMedals :data="dataConfig" :isGsPageQuote="true"></SemiMedals>
	</div>
</template>

<script>
import SemiMedals from "@/components/modal/Quote/SemiMedals.vue";
// 键为路由名称 值为报价相关配置
const mapObj = {
    'lang-quote-1st-place-super-cup-soccer-medals': { quotePid: 312, quoteCateId: 313 },
    'lang-quote-champion-custom-award-medals': { quotePid: 312, quoteCateId: 321 },
    'lang-quote-glitter-award-medals': { quotePid: 312, quoteCateId: 322 },
    'lang-quote-glow-in-the-dark-5k-medals': { quotePid: 312, quoteCateId: 376 },
    'lang-quote-glitter-5k-model-medals': { quotePid: 312, quoteCateId: 377 },
    'lang-quote-trefoil-walking-race-5k-medals': { quotePid: 312, quoteCateId: 378 },
    'lang-quote-personalized-rhinestone-5k-medals': { quotePid: 312, quoteCateId: 379 },
    'lang-quote-3d-soccer-custom-medals': { quotePid: 312, quoteCateId: 381 },
    'lang-quote-hexagon-sports-custom-spinner-medals': { quotePid: 312, quoteCateId: 382 },
    'lang-quote-glitter-star-openwork-finisher-medals': { quotePid: 312, quoteCateId: 383 },
    'lang-quote-customized-america-sports-medals': { quotePid: 312, quoteCateId: 384 },
    'lang-quote-custom-competitive-gear-medals': { quotePid: 312, quoteCateId: 385 },
    'lang-quote-custom-basketball-3d-medals': { quotePid: 312, quoteCateId: 386 },
    'lang-quote-unique-custom-3d-soccer-medals': { quotePid: 312, quoteCateId: 387 },
    'lang-quote-star-custom-rhinestone-soccer-medals': { quotePid: 312, quoteCateId: 388 },
    'lang-quote-dazzling-polished-custom-soccer-medal': { quotePid: 312, quoteCateId: 389 },
    'lang-quote-custom-trail-running-medals': { quotePid: 312, quoteCateId: 391 },
    'lang-quote-flag-round-running-medals': { quotePid: 312, quoteCateId: 392 },
    'lang-quote-petal-shaped-running-medals': { quotePid: 312, quoteCateId: 393 },
    'lang-quote-custom-soccer-club-medals': { quotePid: 312, quoteCateId: 394 },
    'lang-quote-octagonal-customized-soccer-medals': { quotePid: 312, quoteCateId: 395 },
    'lang-quote-stars-soccer-game-custom-medals': { quotePid: 312, quoteCateId: 396 },
    'lang-quote-trail-running-custom-medals': { quotePid: 312, quoteCateId: 397 },
    'lang-quote-shield-flag-volleyball-medals': { quotePid: 312, quoteCateId: 401 },
    'lang-quote-star-cluster-after-school-soccer-medals': { quotePid: 312, quoteCateId: 402 },
    'lang-quote-wreath-award-metal-medals': { quotePid: 312, quoteCateId: 403 },
    'lang-quote-award-3d-leaf-edge-custom-medals': { quotePid: 312, quoteCateId: 404 },
    'lang-quote-custom-uv-printed-round-medals': { quotePid: 312, quoteCateId: 430 },
    'lang-quote-custom-round-wheat-medals': { quotePid: 312, quoteCateId: 431 },
    'lang-quote-custom-star-cut-out-medal': { quotePid: 312, quoteCateId: 432 },
    'lang-quote-custom-halloween-run-glitter-medal': { quotePid: 312, quoteCateId: 437 },
    'lang-quote-custom-round-uv-printed-medals': { quotePid: 312, quoteCateId: 438 },
    'lang-quote-halloween-custom-spinner-running-medal': { quotePid: 312, quoteCateId: 439 },
    'lang-quote-first-place-star-polygon-medals': { quotePid: 312, quoteCateId: 440 },
    'lang-quote-torch-banner-with-ring-medal': { quotePid: 312, quoteCateId: 446 },
    'lang-quote-torch-branch-honours-graduation-medal': { quotePid: 312, quoteCateId: 447 },
    'lang-quote-science-shield-honour-medal': { quotePid: 312, quoteCateId: 448 },
    'lang-quote-book-banner-medal': { quotePid: 312, quoteCateId: 449 },
    'lang-quote-star-book-rush-medal': { quotePid: 312, quoteCateId: 450 },
    'lang-quote-rotating-honours-graduation-medal': { quotePid: 312, quoteCateId: 451 },
    'lang-quote-ranches-stars-universal-medal': { quotePid: 312, quoteCateId: 452 },
    'lang-quote-1st-rhinestone-star-torch-medal': { quotePid: 312, quoteCateId: 453 },
    'lang-quote-ten-stars-first-place-medal': { quotePid: 312, quoteCateId: 454 },
    'lang-quote-star-leaf-trimmed-1st-place-medal': { quotePid: 312, quoteCateId: 455 },
    'lang-quote-2nd-place-super-basketball-medals': { quotePid: 312, quoteCateId: 456 },
    'lang-quote-3rd-place-super-baseball-medal': { quotePid: 312, quoteCateId: 457 }
};
export default {
	data() {
		return {};
	},
	components: {
		SemiMedals,
	},
    computed:{
      dataConfig(){
          return mapObj[this.$route.name]
      }
    },
};
</script>

<style lang="scss" scoped></style>