<template>
	<div class="mb-4" :class="stepData.styleName">
	    <slot name="stepText"></slot>
		<div class="step-content">
			<div class="step-text grey--text">{{ stepData.attributeTitle }}</div>
			<div class="step-wrap">
				<div class="step-item" :class="{ active: index === selectIndex }"
					v-for="(step, index) in stepData.productParamList" :key="index">
					<div class="customInput">
						<div class="inputContent">
							<div class="prepend">{{ step.valueName }}</div>
							<input class="priceInput" id="myInputSize1" :disabled="step.stock <= 0" type="text" :placeholder="getPlaceholder(step)"
								v-model="step.inputNum" @keyup="formatNum(step)" @focus="focusFn(index)" @blur="blurFn" @change="updatePrice" />
						</div>
						<div class="hint-text" v-if="getPrice(step)['show']">
							{{ getPrice(step)["t"] }}
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="errorTip" ref="errorTip">
			<v-alert dense outlined type="error"> {{ langSemiCustom.miniQty }} {{ productInfo.lowestPurchaseQuantity || 1 }}
			</v-alert>
		</div>
	</div>
</template>
<script>
import { round2 } from "@/utils/utils";

export default {
	inject: ["getProductInfo","getCustomPriceData"],
	props: {
		stepData: {
			type: Object,
			default: () => ({}),
		},
	},
	data() {
		return {
			selectIndex: -1,
			selectItem: null,
			isInput: false,
			customInput:false,
		};
	},
	computed: {
		langSemiCustom() {
			return this.$store.getters.lang?.semiCustom;
		},
		symbolCode() {
			return this.$store.state.currency.symbol;
		},
		rate() {
			return this.$store.state.currency.rate;
		},
		productInfo() {
			return this.getProductInfo();
		},
		customPriceData() {
			return this.getCustomPriceData();
		},
	},
	methods: {
		updatePrice() {
			let priceInputs = document.getElementsByClassName("priceInput");
			let sum = Array.from(priceInputs).reduce((acc, input) => acc + parseInt(input.value || 0), 0);
            let errDom = this.$refs.errorTip;
			if (sum < this.productInfo.lowestPurchaseQuantity) {
				errDom.style.display = "block";
			} else {
				errDom.style.display = "none";
			}
			this.$emit("updatePrice");
		},
		formatNum(step) {
			this.customInput = false
			this.isInput = true;
			if (!step.inputNum) {
				this.isInput = false;
				return undefined;
			}
			step.inputNum = step.inputNum.replace(/[^\d]/g, "");
			if (step.stock && step.stock > 0 && step.inputNum > step.stock) {
				step.inputNum = String(step.stock);
			}
			if (step.stock <= 0) {
				step.inputNum = "";
			}
			this.isInput = false;
		},
		getPlaceholder(step) {
			if (this.stepData.styleName === "style1") {
				if (step.stock && step.stock > 0) {
					// return `${step.stock} in stock`;
					return "";
				} else {
					return "Sold Out";
				}
			}
		},
		selectStep(item, index) {
			if (this.isInput || item.stock <= 0) {
				return;
			}
			this.selectIndex = index;
			this.selectItem = item;
			this.$emit("selectStep", {
				type: this.stepData.attributeFlag,
				data: item,
				id: this.stepData.id,
			});
		},
		getIncreasePrice(num = 0, increase) {
			let newNum = parseInt(num);
			if (newNum <= 0 || isNaN(newNum)) {
				return 0;
			}
			if (!increase) {
				return 0;
			}
			let increaseArr = JSON.parse(increase),
				len = increaseArr.length,
				findItem = increaseArr[0];
			if (len === 1) {
				return increaseArr[0].unitPrice;
			}
			for (let i = 0; i < len; i++) {
				let item = increaseArr[i],
					nextItem = increaseArr[i + 1];
				if (newNum >= item.quantity && (nextItem ? newNum < nextItem.quantity : true)) {
					findItem = item;
					break;
				}
			}
			return findItem.unitPrice;
		},
		getPrice(step) {
			let priceType = step.priceType,
				code = this.symbolCode,
				price = 0,
				rate = this.rate;
			const plusBasicUnitPrice = +(this.customPriceData?.plusBasicUnitPrice || 0);
			const multiBasicUnitPrice = +(this.customPriceData?.multiBasicUnitPrice || 1);
			if (priceType === 1) {
				price = round2((+step.unitPrice+plusBasicUnitPrice) * multiBasicUnitPrice * rate);
				if(step.unitPrice==0) price=0
				return {
					t: `+${code}${price}`,
					show: price && price > 0,
				};
			} else if (priceType === 2) {
				price = round2(step.totalPrice * rate);
				return {
					t: `+${code}${price}`,
					show: price && price > 0,
				};
			} else if (priceType === 3) {
				return {
					t: `+${round2(+step.unitPercent*multiBasicUnitPrice)}%`,
					show: step.unitPercent && step.unitPercent > 0,
				};
			} else if (priceType === 4) {
				return {
					t: `+${step.totalPercent}%`,
					show: step.totalPercent && step.totalPercent > 0,
				};
			} else if (priceType === 5) {
				let getPrice=+this.getIncreasePrice(step.inputNum, step.increasePrice)
				price=round2((getPrice+plusBasicUnitPrice)*multiBasicUnitPrice*rate)
				if(getPrice==0) price=0
				return {
					t: `+${code}${price}`,
					show: price && price > 0,
				};
			} else {
				return {
					show: false,
				};
			}
		},
		updateQty(type) {
			if (this.stepData && this.stepData.productParamList.length > 0) {
				let list = this.stepData.productParamList;
				list.forEach((item) => {
					if(this.customInput&&type!='addCart'){
						item.inputNum=''
					}else{
						this.formatNum(item);
					}
					this.$forceUpdate()
				});
			}
		},
		setInputNum(num,type=false,noSelect=true){
			let once=true
				this.stepData.productParamList.forEach((item,index)=>{
					if(once){
						if (item.stock && item.stock > 0) {
							item.inputNum = num + '';
							once=false;
							this.formatNum(item)
							this.customInput=true
						}
					}
				})
				if(type){
					this.$nextTick(() => {
						if(noSelect){
							document.querySelector('#myInputSize1').readOnly = true;
							this.customInput=false
						}
				});
				}
				this.$forceUpdate()
				this.$emit("updatePrice");
		},
		focusFn(index){
			this.selectIndex = index;
		},
		blurFn(){
			this.selectIndex = -1
		}
	},
	mounted() {
		this.$Bus.$on("updateQty", this.updateQty);
		this.$Bus.$on("selectDefaultSizeStep", this.setInputNum)
	},
	beforeDestroy() {
		this.$Bus.$off("updateQty",this.updateQty);
		this.$Bus.$off("selectDefaultSizeStep", this.setInputNum)
	},
};
</script>
<style scoped lang="scss">
@import "~assets/css/half.scss";

input[disabled] {
	background: rgba(0, 0, 0, 0.03);
}

.step-content {
	display: grid;
	grid-template-columns: 100px 1fr;
	grid-gap: 10px;

	.step-wrap {
		display: grid;
		grid-gap: 10px;

		.step-item {
			white-space: nowrap;
			// overflow: hidden;
		}
	}

	@include respond-to(mb) {
		grid-template-columns: 1fr;
		grid-gap: 5px;

		.step-wrap {
			grid-gap: 5px;

			.step-item {
				// overflow: hidden;
			}
		}
	}
}

.style1 .step-content {
	.step-wrap {
		grid-template-columns: repeat(2, 1fr);

		.step-item.active {
			.customInput {
				.inputContent {
					border-color: $color-primary;
					border-width: 2px;
          .priceInput{
						caret-color: $color-primary;
					}
					.prepend {
						color: #ffffff;
						background-color: $color-primary;
					}
				}
			}
		}

		.customInput {
			display: flex;
			flex-direction: column;

			.inputContent {
				display: flex;
				height: 38px;
				border: 1px solid #cccccc;

				.prepend {
					display: flex;
					justify-content: center;
					align-items: center;
					flex: 0 0 100px;
					// width: 180px;
					word-break: break-word;
					padding: 10px;
					background-color: $background-color2;
					transition: background-color 0.3s;
					text-align: center;
				}

				input {
					flex: 1 1 auto;
					line-height: 20px;
					padding: 8px;
					max-width: 100%;
					min-width: 0;
					width: 100%;
				}
			}

			.hint-text {
				margin-top: 4px;
				color: $gray-text;
				text-align: right;
				font-size: 14px;
			}
		}
	}

	@include respond-to(mb) {
		.step-wrap {
			.customInput {
				.inputContent {
					.prepend {
						flex: 0 0 70px;
						word-break: break-word;
						padding: 0;
					}

					input {
						flex: 1 1 auto;
						line-height: 20px;
						padding: 8px;
						max-width: 100%;
						min-width: 0;
						width: 100%;
					}
				}

				.hint-text {
					color: $gray-text;
					text-align: right;
					font-size: 14px;
				}
			}
		}
	}
}

.style2 .step-content {
	.step-wrap {
		grid-template-columns: repeat(8, 1fr);

		.step-item.active {
			.customInput {
				.inputContent {
					input {
						border-width: 2px;
						border-color: $color-primary;
					}
				}
			}
		}

		.customInput {
			display: flex;
			flex-direction: column;

			.inputContent {
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;

				.prepend {
					margin-top: 4px;
					order: 2;
				}

				input {
					order: 1;
					flex: 1 1 auto;
					line-height: 20px;
					height: 40px;
					padding: 8px;
					max-width: 100%;
					min-width: 0;
					width: 100%;
					text-align: center;
					@include step-default;
				}
			}

			.hint-text {
				margin-top: 4px;
				color: $gray-text;
				text-align: center;
				font-size: 14px;
			}
		}
	}

	@include respond-to(mb) {
		.step-wrap {
			grid-template-columns: repeat(4, 1fr);

			.customInput {
				display: flex;
				flex-direction: column;

				.inputContent {
					display: flex;
					flex-direction: column;
					justify-content: center;
					align-items: center;

					.prepend {
						order: 2;
					}

					input {
						order: 1;
						flex: 1 1 auto;
						line-height: 20px;
						padding: 8px;
						max-width: 100%;
						min-width: 0;
						width: 100%;
						@include step-default;
					}
				}

				.hint-text {
					color: $gray-text;
					text-align: center;
					font-size: 14px;
				}
			}
		}
	}
}
</style>
