<template>
  <div class="ds-left-area">
    <div class="ds-left-bar">
      <div class="top-tab">
        <div class="tab-item"
          :class="{ active: status === 1 }"
          @click="showTab(1)">
          <div class="tab-icon">
            <b class="icon-draw"></b>
          </div>
          <div class="tab-text">{{ lang.draw }}</div>
        </div>
        <div class="tab-element">
          <div class="tab-item"
            :class="{ active: status === 2 }"
            @click="showTab(2)">
            <div class="tab-icon">
              <b class="icon-text"></b>
            </div>
            <div class="tab-text">{{ lang.addText }}</div>
          </div>
          <div class="tab-item"
            :class="{ active: status === 3 }"
            @click="showTab(3)">
            <div class="tab-icon">
              <b class="icon-icon"></b>
            </div>
            <div class="tab-text">{{ lang.addIcons }}</div>
          </div>
          <div class="tab-item"
            :class="{ active: status === 4 }"
            @click="showTab(4)">
            <div class="tab-icon">
              <b class="icon-baocun"></b>
            </div>
            <div class="tab-text">{{ lang.myDesign }}</div>
          </div>
        </div>
      </div>
      <!-- <div class="bottom-tab">
        <div class="tab-item"
          v-if="isLogin"
          :class="{ active: status === 7 }"
          @click="showMyDesign">
          <div class="tab-icon">
            <b class="icon-a-icon-Designzhuanhuan"></b>
          </div>
          <div class="tab-text">{{ lang.myDesign }}</div>
        </div>
      </div> -->
    </div>
    <div class="ds-left-content"
      :style="{ width: status == 4 ? '0':'343px'}">
      <div class="default-content content-box"
        v-show="status === 0">
        <h3>{{ lang.what }}</h3>
        <div class="dfc-item-wrap">
          <div class="dfc-item"
            @click="showTab(1)">
            <b class="icon-draw"></b>
            <span>{{ lang.draw }}</span>
          </div>
          <div class="dfc-item"
            @click="showTab(2)">
            <b class="icon-text"></b>
            <span>{{ lang.addText }}</span>
          </div>
          <div class="dfc-item"
            @click="showTab(3)">
            <b class="icon-icon"></b>
            <span>{{ lang.addIcons }}</span>
          </div>
        </div>
      </div>
      <div class="draw-content content-box"
        v-show="status === 1">
        <topBar :title="lang.draw"
          :show-back-icon="showTemplateBackIcon"
          @back="templateCategoryBack"
          @close="showDefault"></topBar>

        <!-- 画笔绘制 -->
        <div v-if="!showMyDesignImg" class="drawState draw-category"
          scrollbar
          v-show="templateStatus === 1 || templateStatus === 2">
          <h4 class="hight-light-title">
            {{ lang.color }}
          </h4>
          <div class="color-group">
            <template v-for="item in colorList">
              <div class="color-item"
                v-if="!item.isNeonColorful"
                @click="clickPenColor(item)"
                :key="item.id">
                <div class="color-ellipse"
                  :class="{'active': item.code == penColor }"
                  :style="{'--bg': item.paramCode}">
                </div>
                <div class="color-name">
                  {{ item.alias }}
                </div>
              </div>

              <div v-else v-show="showGradientColor" :key="item.id" class="color-item" @click="clickPenColor(item)">
                <div class="color-ellipse"
                  :class="{'active': item.code == penColor }"
                  :style="{'--bg': `url(${item.imgUrl}) center / cover no-repeat`}">
                </div>
                <div class="color-name">
                  {{ item.alias }}
                </div>
              </div>
            </template>
          </div>
        </div>

        <!-- 原始画布 generate之后显示 -->
        <div v-else class="drawState draw-raw" scrollbar>
          <h4 class="hight-light-title">
            {{ lang.myDesign }}
          </h4>
          <div class="myDesigns">
            <div class="design-item">
              <img :src="currentDesign.inputImageUrl" alt="">
            </div>
          </div>
        </div>

      </div>
      <div class="text-content content-box"
        v-show="status === 2">
        <topBar :title="textStatus == 2 ? lang.editText:lang.addText"
          :show-back-icon="showTextBackIcon"
          @back="textBack"
          @close="showDefault"></topBar>
        <div class="addText"
          v-show="textStatus === 1">
          <div class="con"
            scrollbar>
            <label class="textLabel">
              <textarea v-model="inputText"
                rows="4"
                :placeholder="lang.enterText"></textarea>
            </label>
            <div class="addTextBtnGroup">
							<button type="button" class="dzBtn addTextBtn curvedBtn" @click="addCurveText(inputText)">{{ lang.addCurvedToDesign }}</button>
              <button type="button"
                class="dzBtn addTextBtn"
                @click="addText(inputText)">{{ lang.addToDesign }}</button>
            </div>
          </div>
        </div>
        <div class="editText"
          scrollbar
          v-show="textStatus === 2">
          <div class="con"
            scrollbar>
            <label>
              <textarea :value="textProperty.text"
                rows="4"
                :placeholder="lang.enterText"
                @input="debounceChangeTextVal($event.target.value)"></textarea>
            </label>
          </div>
          <div class="editWrap">
            <div class="edit-color">
              <h4 class="hight-light-title"
                style="text-align:left">
                {{ lang.textColor }}
              </h4>

              <div class="color-group">
                <template v-for="item in colorList">
                    <div class="color-item"
                      v-if="!item.isNeonColorful"
                      @click="changeElementProperty(item.code,'fill',item,'textColor')"
                      :key="item.id">
                      <div class="color-ellipse"
                        :class="{'active': item.code == textProperty.fill }"
                        :style="{'--bg': item.paramCode}">
                      </div>
                      <div class="color-name">
                        {{ item.alias }}
                      </div>
                    </div>
                    <div v-else class="color-item" :key="item.id" @click="changeElementProperty(item.code,'fill',item,'textColor')">
                      <div class="color-ellipse"
                        :class="{'active': textProperty.fill?.type == 'linear' }"
                        :style="{'--bg': `url(${item.imgUrl}) center / cover no-repeat`}">
                      </div>
                      <div class="color-name">
                        {{ item.alias }}
                      </div>
                    </div>
                </template>
              </div>
            </div>

            <div class="edit-item edit-font"
              @click="textStatus = 3">
              <div class="edit-item-title">{{ lang.font }}</div>
              <div class="edit-item-content">
                <div :style="{ fontFamily: textProperty.fontFamily }">
                  {{ textProperty.fontFamily }}
                </div>
              </div>
              <div class="edit-item-chevron">
                <b class="icon-a-icon-arrow1zhuanhuan"></b>
              </div>
            </div>
            <div class="edit-item edit-spacing">
              <div class="edit-item-title">{{ lang.spacing }}</div>
              <div class="edit-item-content">
                <el-slider :show-tooltip="false"
                  :min="0"
                  :max="textProperty.type ==='curved-text' ? 100 : 500"
                  show-input
                  :step="textProperty.type ==='curved-text' ? 10 : 50"
                  :value="textProperty.charSpacing"
                  @input="changeElementProperty($event, 'charSpacing')"
                  :show-input-controls="false"
                  ></el-slider>
              </div>
            </div>
            <div class="edit-item edit-size">
              <div class="edit-item-title">{{ lang.textSize }}</div>
              <div class="edit-item-content">
                <el-slider :show-tooltip="false"
                  :min="16"
                  :max="200"
                  :value="textProperty.fontSize"
                  @input="changeElementProperty($event, 'fontSize')"
                  show-input
                  :show-input-controls="false"
                  :step="10"></el-slider>
              </div>
            </div>
            <!-- 去掉的判断 v-show="textProperty.type === 'curved-text'" -->
            <div class="edit-item edit-curve">
              <div class="edit-item-title">{{ lang.textCurve }}</div>
              <div class="edit-item-content">
                <el-slider :show-tooltip="false"
                  :value="textProperty.radian"
                  :min="100"
                  :max="2000"
                  @input="changeElementProperty($event, 'radian')"
                  show-input
                  :show-input-controls="false"
                  :step="100"></el-slider>
              </div>
            </div>

          </div>
        </div>
        <div class="editTextFont"
          v-show="textStatus === 3">
          <div class="con"
            scrollbar>
            <div class="font-button-wrap">
              <div class="arrow-btn"
                @click="backFontPreview">
                <b class="icon-back"></b>
                {{ lang.backPrevious }}
              </div>
            </div>
            <div class="font-wrap"
              neonAiScrollBar>
              <div class="font-item"
                v-for="item in fontList"
                :key="item"
                @click="changeFontFamily(item)">
                <div class="font-text"
                  :style="{ fontFamily: item }">
                  {{ textProperty.text }}
                </div>
                <div class="font-name">
                  {{ item }}
                </div>
              </div>
            </div>
          </div>
          <div class="editTextColor"
            v-show="textStatus === 4">
            <div class="con"
              neonAiScrollBar>
              <div class="editColor-colorPicker">
                <div class="editColor-colorPicker-title">{{ lang.selectColor }}</div>
                <div class="editColor-colorPicker-selected">
                  <ColorPickerItem :colorItem="{
										code: textProperty.fill,
									}"></ColorPickerItem>
                  <span class="color-name">{{ activeTextColorName }}</span>
                </div>
                <div class="editColor-colorPicker-title"
                  v-show="historyTextColorRecords.length!=0">
                  {{ lang.selectedHistory }}
                </div>
                <div class="editColor-colorPicker-history"
                  v-show="historyTextColorRecords.length!=0">
                  <ColorPickerItem :colorItem="item"
                    :isActive="item.code === textProperty.fill"
                    v-for="(item, index) in historyTextColorRecords"
                    :key="index"
                    @changeColor="changeElementProperty($event.code,'fill',$event,'textColor')">
                  </ColorPickerItem>
                </div>
                <div class="editColor-colorPicker-colorContainer"
                  scrollbar>
                  <div class="editColor-colorPicker-colors">
                    <!-- <ColorPickerItem :colorItem="item"
                    :isActive="item.code === textProperty.fill"
                    v-for="(item, index) in colorList"
                    :key="index"
                    @changeColor="changeElementProperty($event.code, 'fill',$event,'textColor')"></ColorPickerItem> -->
                  </div>
                </div>
              </div>
              <div class="editTextColor-foot">
                <el-button type="primary"
                  @click="textStatus = 2">{{ lang.done }}</el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="art-content content-box"
        v-show="status === 3">
        <topBar :title="lang.addIcons"
          :show-back-icon="showArtBackIcon"
          @back="artBack"
          @close="showDefault"></topBar>
        <div style="padding: 17px 25px"
          v-show="artStatus === 1 || artStatus === 2 || artStatus === 4">
          <search-input v-model="searchArtValue"
            :extend-list="artState ? artRecordList : artExtendList"
            @input="inputArtValue"
            @search="searchArt"
            @focus="artExtend"></search-input>
        </div>
        <div class="art-category"
          v-show="artStatus === 1 || artStatus === 2">
          <div class="con"
            neonAiScrollBar>
            <div class="box-item"
              v-show="artStatus === 1"
              :class="[{ active: clipartTypeId === null }, 'box-item-style' + artDisplayControl]"
              @click="selectArtCategory({ id: 908, name: lang.all })">
              <b class="icon-a-T-Allzhuanhuan"></b>
              <span class="item-name">{{ lang.all }}</span>
            </div>
            <div class="box-item"
              :class="[{ active: clipartTypeId === item.id }, 'box-item-style' + item.displayControl]"
              v-for="item in artCategoryList"
              :key="item.id"
              @click="selectArtCategory(item)">
              <div class="imgWrap">
                <img v-if="item.displayControl === 1"
                  loading="lazy"
                  :src="item.icon"
                  :alt="item.clipartTypeName" />
                <img v-if="item.displayControl === 2"
                  loading="lazy"
                  :src="item.displayDrawing"
                  :alt="item.clipartTypeName" />
              </div>
              <span class="item-name">{{ item.clipartTypeName }}</span>
            </div>
          </div>
        </div>
        <div class="editImg"
          v-show="artStatus === 3">
          <div class="con"
            neonAiScrollBar>
            <div class="currentPic">
              <div class="imgWrap linear-gradient-1">
                <img :src="imageProperty.picPath"
                  alt="" />
              </div>
              <div class="imgReplaceArea">
                <div>{{ lang.currentArt }}</div>
                <el-button type="primary"
                  @click="showReplaceDialog"><b class="icon-a-icon-Replacezhuanhuan"></b>
                  {{ lang.replace }}
                </el-button>
              </div>
            </div>
            <div class="editWrap"
              style="margin-top:0;">
              <div class="edit-item edit-angle">
                <div class="edit-item-title">{{ lang.rotation }}</div>
                <div class="edit-item-content">
                  <el-slider :show-tooltip="false"
                    :min="-360"
                    :max="360"
                    :value="imageProperty.angle"
                    @input="changeElementProperty($event, 'angle')"
                    show-input
                    :show-input-controls="false"
                    :step="100"></el-slider>
                </div>
              </div>
              <div class="edit-item edit-size">
                <div class="edit-item-title">{{ lang.size }}</div>
                <div class="edit-item-content">
                  <el-slider :show-tooltip="false"
                    :min="0.01"
                    :max="5"
                    :step="0.5"
                    :value="imageProperty.scale"
                    @input="changeElementProperty($event, 'scale')"
                    show-input
                    :show-input-controls="false"></el-slider>
                </div>
              </div>
              <!--							<div class="edit-item edit-base">-->
              <!--								<div class="edit-item-title">As a base</div>-->
              <!--								<div class="edit-item-content">-->
              <!--									<el-switch v-model="imageProperty.IsMain" :active-value="1" :inactive-value="0"-->
              <!--											   @change="changeBaseImage"></el-switch>-->
              <!--								</div>-->
              <!--							</div>-->
              <div class="edit-color"
                @click="showEditImgColor = !showEditImgColor">
                <h4 class="hight-light-title"
                  style="text-align:left">
                  {{ lang.textColor }}
                </h4>

                <div class="color-group">
                  <div class="color-item"
                    v-for="item in colorList.filter(x=> !x.isNeonColorful)"
                    @click="selectImgReplaceColor(item.code, item)"
                    :key="item.id">
                    <div class="color-ellipse"
                      :class="{'active': item.paramCode == selectedImgReplaceColor}"
                      :style="{'--bg': item.paramCode}">
                    </div>
                    <div class="color-name">
                      {{ item.alias }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="layer-content content-box"
        v-show="status === 4">
        <topBar :title="lang.myDesign"
          :show-back-icon="false"
          @close="showDefault"></topBar>
      </div>
    </div>
    <Crop :cropperDialog.sync="cropperDialog"
      :imageSrc="imageSrc"
      @cropImage="cropImage"
      width="50%"></Crop>
  </div>
</template>
<script>
import BaseDialog from "~/components/Quote/BaseDialog.vue";
import ColorPickerItem from "@/components/MyDzxt/ColorPickerItem.vue";
import ColorPickerRectItem from "@/components/MyDzxt/ColorPickerRectItem.vue";
import ColorPickerParentRectItem from "@/components/MyDzxt/ColorPickerParentRectItem.vue";
import draggable from "vuedraggable";
import Crop from "@/components/MyDzxt/Crop.vue";
import {
  changeImgColor,
  colorRgb,
  dataURLtoFile,
  debounce,
  pdfToImg,
  urlAddVersion,
} from "@/utils/utils";
import {
  getColorCardByTypeId,
  getColorCardByColorName,
  addUploadHistory,
  getArtCateList,
  getUploadHistory,
  deleteUploadHistory,
  getTemplateTypeListFromApp,
  likeAllString,
  getAllTemplateNameAndTypeName,
} from "@/api/newDzxt";
import { uploadFile } from "@/utils/oss";
import searchInput from "@/components/MyDzxt/searchInput.vue";
import topBar from "@/components/MyDzxt/topBar.vue";
import noResult from "@/components/MyDzxt/noResult.vue";
import dzMixin from "@/mixins/dzMixin";
export default {
  mixins: [dzMixin],
  components: {
    Crop,
    ColorPickerItem,
    ColorPickerRectItem,
    ColorPickerParentRectItem,
    draggable,
    searchInput,
    topBar,
    noResult,
    BaseDialog,
  },
  data() {
    const lang = this.$store.getters.lang?.design;
    return {
      metalColor: "",
      selectedColorNow: "",
      selectedColorCategoryId: -1, //选择的颜色分类
      showChild: false,
      showOops: false,
      inputColorValue: "",
      debouncedColorValue: null,
      colorCardList: [], //子级色卡数组
      historyPicColorRecords: [],
      historyTextColorRecords: [],
      imageSrc: "",
      cropperDialog: false,
      lang: lang,
      artDisplayControl: 0,
      templateDisplayControl: 0,
      searchArtValue: "",
      myDesignDialog: false,
      status: 0,
      artStatus: 1,
      textStatus: 1,
      templateStatus: 1,
      searchTemplateValue: "",
      LoginDialogStatus: false,
      showGradientColor: false,
      uploadHistoryList: [],
      selectedElement: "",
      outlineMarks: {
        0: lang.none,
        1: lang.veryThin,
        2: lang.thin,
        3: lang.medium,
        4: lang.thick,
        5: lang.veryThick,
      },

      inputText: "",
      artForm: {
        page: 1,
        pageSize: 60,
        cateId: null,
        cateName: "",
        total: 0,
        pages: 1,
      },
      debounceChangeTextVal: null,
      templateCategoryList: [],
      templateCategoryListCopy: [],
      // textProperty: {},
      // imageProperty: {},
      textShapeList: [
        {
          name: lang.normal,
          url: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20221226/2060P7Y8DpeQ.jpg",
        },
        {
          name: lang.curve,
          url: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20221226/2060P7Y8DpeQ.jpg",
        },
        {
          name: lang.arch,
          url: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20221226/2060P7Y8DpeQ.jpg",
        },
        {
          name: lang.bridge,
          url: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20221226/2060P7Y8DpeQ.jpg",
        },
        {
          name: lang.valley,
          url: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20221226/2060P7Y8DpeQ.jpg",
        },
        {
          name: lang.pinch,
          url: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20221226/2060P7Y8DpeQ.jpg",
        },
        {
          name: lang.bulge,
          url: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20221226/2060P7Y8DpeQ.jpg",
        },
        {
          name: lang.perspective,
          url: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20221226/2060P7Y8DpeQ.jpg",
        },
        {
          name: lang.pointed,
          url: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20221226/2060P7Y8DpeQ.jpg",
        },
        {
          name: lang.downward,
          url: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20221226/2060P7Y8DpeQ.jpg",
        },
        {
          name: lang.upward,
          url: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20221226/2060P7Y8DpeQ.jpg",
        },
        {
          name: lang.cone,
          url: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20221226/2060P7Y8DpeQ.jpg",
        },
      ],
      showFontFamilyHistoryStatus: false,
      fontFamilyListHistory: [],
      artCategoryList: [],
      artCategoryListCopy: [],
      showEditImgColor: false,
      selectedImgColor: "",
      selectedImgReplaceColor: "",
      layersArr: [],
      uploadLoading: false,
      templateCateId: null,
      templateCateName: "",
      clipartTypeId: null,
      clipartTypeName: "",
      artTagList: [],
      templateTagList: [],
      templateExtendList: [],
      templateRecordList: [],
      artExtendList: [],
      artRecordList: [],
      loadChangeColor: false,
      templateState: false,
      artState: false,
    };
  },
  watch: {
    isLogin() {
      this.getUploadHistory();
    },
    // showMyDesignImg(){
    //   this.status = 1
    // }
  },
  computed: {
    textProperty() {
      return this.canvas.textProperty;
    },
    penColor() {
      return this.$store.state.design.penColor;
    },
    penWidth() {
      return this.$store.state.design.penWidth;
    },
    showMyDesignImg() {
      return this.$store.state.design.showMyDesignImg;
    },
    currentDesign() {
      return this.$store.state.design.currentDesign;
    },
    imageProperty() {
      const obj = this.canvas.imageProperty;
      obj.singleColor = true;
      return obj;
    },
    langDesign() {
      return this.$store.getters.lang?.design;
    },
    selectedImgColorList() {
      return this.canvas.selectedImgColorList;
    },
    artContentTitle() {
      let obj = {
        1: this.lang.addCliparts,
        2: this.clipartTypeName,
        3: this.lang.editCliparts,
      };
      return obj[this.artStatus];
    },
    showArtBackIcon() {
      let obj = {
        1: false,
        2: true,
        3: false,
        4: true,
      };
      return obj[this.artStatus];
    },
    textContentTitle() {
      let obj = {
        1: this.lang.addText,
        2: this.lang.editText,
        3: this.lang.font,
        4: this.lang.fontColors,
        5: this.lang.textShape,
        6: this.lang.textOutline,
      };
      return obj[this.textStatus];
    },
    showTextBackIcon() {
      let obj = {
        1: false,
        2: false,
        3: true,
        4: true,
        5: true,
        6: true,
      };
      return obj[this.textStatus];
    },
    showTemplateBackIcon() {
      let obj = {
        1: false,
        2: true,
        3: true,
      };
      return obj[this.templateStatus];
    },
    isLogin() {
      return this.$store.getters.isLogin;
    },
    userId() {
      return this.$store.state.userInfo?.id;
    },
    dragOptions() {
      return {
        animation: 200,
        group: "description",
        disabled: false,
        ghostClass: "ghost",
      };
    },
    outlineLabel() {
      return this.outlineMarks[this.textProperty.strokeWidth];
    },
    fontList() {
      return this.$store.getters["design/fontList"];
    },
    currentFontList() {
      return this.showFontFamilyHistoryStatus
        ? this.fontFamilyListHistory
        : this.fontList;
    },
    activeTextColorName() {
      let findColor = this.colorList.find((item) => {
        return item.code === this.textProperty.fill;
      });
      return findColor ? findColor.pantone : "";
    },
    activeTextOutlineColorName() {
      let findColor = this.colorList.find((item) => {
        return item.code === this.textProperty.stroke;
      });
      return findColor ? findColor.pantone : "";
    },
    colorList() {
      return this.$store.state.design.colorList;
    },
    colorType() {
      return this.$store.state.design.colorType;
    },
  },
  methods: {
    // 修改当前画笔颜色
    clickPenColor(row) {
      this.$store.commit("design/set_penColor", row.code);
      let activeEle = this.canvas.c.getActiveObject();
      // 如果当前选中了一个绘制对象 就去修改它的颜色
      if (activeEle) {
        this.changeElementProperty(row.code, "stroke");
      } else {
        this.canvas.pen();
        this.canvas.changeDrawColor(row.code);
        this.canvas.changeDrawWidth(this.penWidth - 2);
        this.canvas.startDraw('pen');
      }
    },

    clickImgColor(row) {
      this.selectedImgReplaceColor = row.code;
      this.selectedImgReplaceColor();
    },

    //色卡模糊查询
    getColorCardByColorNameByRequest() {
      if (this.inputColorValue.trim() == "") {
        this.colorCardList = this.colorList;
        this.selectedColorCategoryId = -1;
        return;
      }
      getColorCardByColorName({
        colorName: this.inputColorValue,
      }).then((res) => {
        if (res.code == "200") {
          if (res.data.length == 0) {
            this.showChild = false;
            this.showOops = true;
          } else {
            this.showChild = true;
            this.showOops = false;
          }
          this.selectedColorCategoryId = -1;
          this.colorCardList = res.data;
        }
      });
    },
    //图片放大缩小
    changeScale(num) {
      num = num || 1;
      this.$refs.cropper.changeScale(num);
    },

    cropImage(data) {
      this.$store.commit("design/set_loading", true);
      let file = dataURLtoFile(data, 1);
      this.uploadFileToDesign(file);
      this.$store.commit("design/set_loading", false);
    },
    uploadOnChange(e, type = "upload") {
      let file = type === "upload" ? e.target.files[0] : e,
        name = file.name;
      let checkResult = this.beforeUpload(file);
      if (!checkResult) {
        this.$refs["upload1"].value = "";
        return false;
      }
      if (/\.(pdf)$/.test(name.toLowerCase())) {
        let reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = async (e) => {
          this.imageSrc = await pdfToImg(e.target.result);
        };
        this.cropperDialog = true;
      } else if (/\.(psd)/.test(name.toLowerCase())) {
        let reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = async (e) => {
          let PSD = require("@/assets/js/psd");
          let psd = await PSD.fromURL(e.target.result);
          this.imageSrc = psd.image.toBase64();
        };
        this.cropperDialog = true;
      } else {
        let reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = async (e) => {
          this.imageSrc = e.target.result;
        };
        this.cropperDialog = true;
      }
      this.$refs["upload1"].value = "";
    },
    handleDrop(event) {
      event.preventDefault();
      const files = event.dataTransfer.files;
      // 处理上传的文件
      this.uploadOnChange(files[0], "drop");
    },
    showMyDesign() {
      this.$emit("showMyDesign");
    },
    artBack() {
      this.artStatus = 1;
      this.artCategoryList = this.artCategoryListCopy;
    },
    textBack() {
      this.textStatus = 2;
    },
    templateCategoryBack() {
      this.templateStatus = 1;
      this.templateCategoryList = this.templateCategoryListCopy;
    },
    inputTemplateValue(val) {
      if (val && this.templateTagList.length > 0) {
        if (val.trim().length == 1) {
          this.templateExtend(true);
          return;
        }
        const regex = new RegExp(`^${val}`, "gi");
        this.templateExtendList = this.templateTagList
          .filter((item) => item.match(regex))
          .map((item) => {
            return {
              value: item,
              icon: false,
            };
          });
        this.templateState = false;
      } else {
        this.templateExtend(true);
        this.searchTemplate(val);
        this.templateExtendList = [];
      }
    },
    searchTemplate(val) {
      //将提交搜索的存localStorage
      this.setLocalStorage("templateRecord", val);
      this.templateCateId = "";
      if (!val) {
        this.templateCateId = null;
      }
      this.$Bus.$emit("searchTemplate", val);
    },

    inputArtValue(val) {
      if (val && this.artTagList.length > 0) {
        if (val.trim().length == 1) {
          this.artExtend(true);
          return;
        }
        const regex = new RegExp(`^${val}`, "gi");
        this.artExtendList = this.artTagList
          .filter((item) => item.match(regex))
          .map((item) => {
            return {
              value: item,
              icon: false,
            };
          });
        this.artState = false;
      } else {
        this.artExtend(true);
        this.searchArt(val);
        this.artExtendList = [];
      }
    },
    searchArt(val) {
      this.setLocalStorage("artRecord", val);
      this.clipartTypeId = "";
      if (!val) {
        this.clipartTypeId = null;
      }
      this.$Bus.$emit("searchArt", val);
    },
    loadImage(url) {
      return new Promise((resolve, reject) => {
        let img = new Image();
        img.onload = () => resolve(img);
        img.onerror = reject;
        img.src = urlAddVersion(url);
        img.crossOrigin = "Anonymous";
      });
    },
    async addImg(src, property) {
      this.$store.commit("design/set_loading", true);
      let imgInstance = await this.canvas.addImg(src, property);
      this.activeItem(imgInstance);
      // await this.canvas.analyzeImgColor(src);
      this.$store.commit("design/set_loading", false);
    },
    deleteAllHistory() {
      this.$confirm(this.lang.deleteAll, this.lang.tips, {
        confirmButtonText: this.lang.comfirm,
        cancelButtonText: this.lang.cancel,
        type: this.lang.warning,
      })
        .then(() => {
          deleteUploadHistory(
            this.uploadHistoryList.map((item) => item.id)
          ).then(() => {
            this.getUploadHistory();
          });
        })
        .catch(() => {});
    },
    getUploadHistory() {
      getUploadHistory({
        userId: this.userId,
      }).then((res) => {
        this.uploadHistoryList = res.data;
      });
    },
    addToUploadHistory(urlArr) {
      if (!urlArr.length || !this.isLogin) {
        return false;
      }
      if (this.isLogin) {
        addUploadHistory({
          userId: this.userId,
          picPath: urlArr[0].picPath,
        }).then((res) => {
          this.getUploadHistory();
        });
      }
    },
    delUploadHistory(id) {
      deleteUploadHistory([id]).then((res) => {
        this.getUploadHistory();
      });
    },
    beforeUpload(file) {
      const isLt10M = file.size / 1024 / 1024 < 10;
      const isUploadType = /\.(gif|jpg|jpeg|png|svg|psd|pdf|bmp|ai|)$/.test(
        file.name.toLowerCase()
      );
      if (!isLt10M) {
        this.$toast.error(this.lang.sizeError);
        return isLt10M;
      }
      if (!isUploadType) {
        this.$toast.error(this.lang.imgError);
        return isUploadType;
      }
      return true;
    },
    uploadFileToDesign(file) {
      this.uploadLoading = true;
      uploadFile(file)
        .then((res) => {
          this.addImg(res, { isUserUpload: 1 });
          this.addToUploadHistory([
            {
              picPath: res,
            },
          ]);
        })
        .finally(() => {
          this.$refs["upload1"].value = "";
          this.uploadLoading = false;
        });
    },

    triggerUpload() {
      this.$refs["upload1"].click();
    },
    showChildren(item) {
      item.showChildren = !item.showChildren;
      this.$forceUpdate();
    },
    toggleViewElement(item) {
      item.visible = !item.visible;
      this.canvas.c.requestRenderAll();
    },
    toggleLockElement(item) {
      item.selectable = !item.selectable;
      item.evented = !item.evented;
      this.canvas.c.requestRenderAll();
    },
    // selectedItem(item) {
    //   this.canvas.c.discardActiveObject();
    //   this.canvas.c.setActiveObject(item);
    //   this.canvas.c.requestRenderAll();
    //   this.activeItem(item);
    //   this.$Bus.$emit("hideTools");
    // },
    activeItem(item) {
      let activeEle = this.canvas.c.getActiveObject();
      if (activeEle !== item) {
        this.showDefault();
        return false;
      }
      this.canvas.isGetProperty = true;
      this.canvas.getObjectAttr(item);
      if (
        item.type === "text" ||
        item.type === "i-text" ||
        item.type === "curved-text"
      ) {
        this.status = 2;
        this.textStatus = 2;
      } else if (item.type === "image") {
        this.status = 3;
        this.artStatus = 3;
      } else if (item.type === "path") {
        this.status = 1;
        this.showGradientColor = true
      }
      this.$nextTick(() => {
        this.canvas.isGetProperty = false;
      });
    },
    changeLayer(e) {
      let oldIndex = e.moved.oldIndex;
      let newIndex = e.moved.newIndex;
      let num = Math.abs(newIndex - oldIndex);
      if (newIndex > oldIndex) {
        for (let i = 0; i < num; i++) {
          e.moved.element.sendBackwards();
        }
      } else {
        for (let i = 0; i < num; i++) {
          e.moved.element.bringForward();
        }
      }
      this.canvas.c.requestRenderAll();
    },
    confirmChangeImgColor() {
      if (!this.selectedImgReplaceColor) {
        this.$toast.info(this.lang.pleaseToast);
        return false;
      }
      if (this.imageProperty.singleColor) {
        this.changeElementProperty(
          [{ color: this.selectedImgReplaceColor }],
          "singleColorList"
        );
        this.selectedImgColor = this.selectedImgReplaceColor;
        let filter = new fabric.Image.filters.BlendColor({
          color: this.selectedImgReplaceColor,
          mode: "tint",
        });
        this.canvas.applyFilter(0, filter);
        return;
      }
      this.loadChangeColor = true;
      let ele = this.canvas.c.getActiveObject();
      let url = ele.getSrc();
      changeImgColor(
        url,
        {
          oldColor: this.selectedImgColor,
          newColor: this.selectedImgReplaceColor,
        },
        (base64) => {
          let file = dataURLtoFile(base64, 1);
          uploadFile(file).then(async (url) => {
            let img = await this.loadImage(url);
            ele.setElement(img);
            this.selectedImgColor = colorRgb(this.selectedImgReplaceColor);
            this.$Bus.$emit("synchroSelectedImgColor", this.selectedImgColor); //同步颜色
            this.canvas.analyzeImgColor(url);
            this.canvas.getObjectAttr(ele);
            this.canvas.c.requestRenderAll();
            this.loadChangeColor = false;
          });
        }
      );
    },

    synchroSelectedImgColorLeft(color) {
      this.selectedImgColor = color;
    },
    changeColorCategory(id) {
      this.selectedImgReplaceColor = "";
      this.showChild = true;
      this.showOops = false;
      this.selectedColorCategoryId = id;
      //展示子级分类
      getColorCardByTypeId({
        typeId: id,
      }).then((res) => {
        if (res.code == "200") {
          this.colorCardList = res.data;
        }
      });
    },
    selectImgReplaceColor(color, colorFullMsg) {
      if (this.selectedImgReplaceColor == color) return;
      this.selectedImgReplaceColor = color;
      let result = this.confirmChangeImgColor();
      if (result === false) return;
    },
    showReplaceDialog() {
      this.$store.commit("design/set_Replace", true);
    },
    changeTextShape(item) {
      let name = item.name;
      let activeEle = this.canvas.c.getActiveObject();
      if (!activeEle) {
        return false;
      }
      switch (name) {
        case "normal":
          activeEle.set("path", null);
          break;
        case "curve":
          let eleWidth = activeEle.width;
          let r = (2 * eleWidth) / (2 * Math.PI);
          let path = new fabric.Path(`M 0,0 A 1 1 0 0 1 ${2 * r},0`, {
            fill: "transparent",
          });
          activeEle.set({
            path,
            pathAlign: "center",
          });
          break;
      }
      this.canvas.c.requestRenderAll();
    },
    changeFontFamily(item) {
      this.canvas.c.getActiveObject().set("fontFamily", item);
      this.canvas.c.requestRenderAll();
      if (!this.showFontFamilyHistoryStatus) {
        this.addFamilyListHistory(item);
      }
      this.canvas.getObjectAttr(this.canvas.c.getActiveObject());
      this.canvas.updateMiniMap();
      this.canvas.saveStateToHistory();
    },
    addFamilyListHistory(data) {
      let ind,
        arr = this.fontFamilyListHistory;
      let boo = arr.some((item, index) => {
        if (item === data) {
          ind = index;
          return true;
        }
      });
      if (boo) {
        arr.splice(ind, 1);
        arr.unshift(data);
        return false;
      }
      arr.unshift(data);
    },
    // showRecentUsedFont() {
    //   this.showFontFamilyHistoryStatus = true;
    // },
    backFontPreview() {
      if (this.showFontFamilyHistoryStatus) {
        this.showFontFamilyHistoryStatus = false;
      } else {
        this.textStatus = 2;
      }
    },
    applyAllText() {
      let activeEle = this.canvas.c.getActiveObject();
      let fontFamily = activeEle.get("fontFamily"),
        fill = activeEle.get("fill"),
        strokeWidth = activeEle.get("strokeWidth"),
        stroke = activeEle.get("stroke");
      let textArr = this.canvas.c
        .getObjects()
        .filter((item) =>
          ["text", "i-text", "curved-text"].includes(item.type)
        );
      if (!textArr.length) {
        return false;
      }
      for (let i = 0; i < textArr.length; i++) {
        let textItem = textArr[i];
        textItem.set({
          fontFamily,
          fill,
          strokeWidth,
          stroke,
        });
      }
      this.canvas.c.requestRenderAll();
    },
    changeElementProperty(val, property, colorMsg, flag) {
      this.canvas.changeElementProperty(val, property);
      this.canvas.getObjectAttr();
    },
    removeOutline() {
      let activeEle = this.canvas.c.getActiveObject();
      if (!activeEle) {
        return false;
      }
      activeEle.set("strokeWidth", 0);
      activeEle.set("stroke", "transparent");
      this.canvas.c.requestRenderAll();
      this.canvas.getObjectAttr(activeEle);
      this.textStatus = 2;
    },
    changeTextVal(val) {
      let activeEle = this.canvas.c.getActiveObject();
      if (!activeEle) {
        return;
      }
      activeEle.set("text", val);
      this.canvas.c.requestRenderAll();
      this.canvas.getObjectAttr(activeEle);
    },
    async addCurveText(val) {
      const property = { fill: '#f9a2c5', fontFamily: 'Avante' }
      let Text = await this.canvas.addCurveText(val, property);
      this.inputText = "";
      this.activeItem(Text);
    },
    /*添加文字*/
    async addText(val) {
      const property = { fill: '#f9a2c5', fontFamily: 'Avante' }
      let Text = await this.canvas.addText(val, property);
      this.inputText = "";
      this.activeItem(Text);
    },
    showDefault() {
      this.status = 0;
      this.textStatus = 1;
      this.selectedImgColor = "";
      this.selectedImgReplaceColor = "";
      this.selectedElement = "";
      this.showEditImgColor = false;
      this.showImgColorPopover = false;
      this.canvas.stopDraw();
      this.$store.commit("design/set_penColor",'#f9a2c5');
      this.$store.commit("design/set_showTemplate", false);
      this.$store.commit("design/set_showArt", false);
      this.$store.commit("design/set_showDesign", false);
    },
    showTab(val) {
      this.showDefault();
      this.status = val;
      this.$store.commit("design/set_showTemplate", false);
      this.$store.commit("design/set_showArt", false);
      this.$store.commit("design/set_showDesign", false);
      this.$store.commit("design/set_showMyDesignImg", false);
      this.canvas.c.discardActiveObject().requestRenderAll();
      this.$Bus.$emit("hideTools");
      switch (val) {
        case 1:
          this.canvas.pen();
          this.canvas.changeDrawColor('#f9a2c5');
          this.canvas.changeDrawWidth(this.penWidth - 2);
          this.canvas.startDraw('pen');
          this.textStatus = 1;
          this.showGradientColor = false;
          break;
        case 2:
          this.textStatus = 1;
          break;
        case 3:
          this.selectedImgReplaceColor = "";
          this.$store.commit("design/set_showArt", true);
          this.artCategoryList = this.artCategoryListCopy;
          this.artStatus = 1;
          break;
        case 4:
          // this.$store.commit("design/set_showMyDesignImg", false)
          this.$store.commit("design/set_showDesign", true);
          break;
        case 5:
          break;
        case 6:
          break;
      }
    },
    selectArtCategory(item) {
      this.searchArtValue = "";
      this.artExtendList = [];
      this.clipartTypeId = item.id;
      this.clipartTypeName = item.clipartTypeName;
      if (item.clipartTypeList && item.clipartTypeList.length) {
        this.artStatus = 2;
        this.artCategoryList = item.clipartTypeList;
      }
      this.$Bus.$emit("selectArtCategory", item);
    },
    selectTemplateCategory(item) {
      this.searchTemplateValue = "";
      this.templateExtendList = [];
      this.templateCateId = item.id;
      this.templateCateName = item.templateTypeName;
      if (item.children && item.children.length) {
        this.templateStatus = 2;
        this.templateCategoryList = item.children;
      }
      this.$Bus.$emit("selectTemplateCategory", item);
    },
    getArtCateList() {
      getArtCateList({
        id: 1,
      }).then((res) => {
        let list = res.data.find(x => x.clipartTypeName == 'neonsigns')?.clipartTypeList;
        this.artCategoryList = this.artCategoryListCopy = list;
        this.artDisplayControl = list.length && list[0].displayControl;
      });
    },
    getTemplateTypeListFromApp() {
      return new Promise((resolve) => {
        getTemplateTypeListFromApp({
          categoryId: this.$store.state.design?.pageInfo?.id,
        }).then((res) => {
          let list = res.data;
          this.templateCategoryList = this.templateCategoryListCopy = list;
          this.templateDisplayControl =
            list.length > 0 && list[0].displayControl;
          resolve(list);
        });
      });
    },
    addArt(obj) {
      this.addImg(obj.src, {
        artId: obj.property,
        projectName: 'neon'
      });
    },
    getHistoryPicColorRecords(arr, selectedColorNow) {
      this.selectedColorNow = selectedColorNow;
      this.historyPicColorRecords = arr;
      this.selectedImgReplaceColor = this.historyPicColorRecords[0].code;
    },
    getHistoryTextColorRecords(arr) {
      this.historyTextColorRecords = arr;
      this.selectedImgReplaceColor = this.historyTextColorRecords[0].code;
    },
  },
  created() {
    this.debounceChangeTextVal = debounce(this.changeTextVal, 300);
    this.debouncedColorValue = debounce(
      this.getColorCardByColorNameByRequest,
      300
    );
    if (this.isLogin) {
      this.getUploadHistory();
    }
  },
  async mounted() {
    this.getArtCateList();
    this.getTemplateTypeListFromApp().then((list) => {
      if (this.$route.query.cid) {
        let findCate = list.find((item) => {
          return item.id == this.$route.query.cid;
        });
        findCate && this.selectTemplateCategory(findCate);
        this.showTab(1);
      }
    });
    likeAllString().then((res) => {
      this.artTagList = res.data;
    });
    getAllTemplateNameAndTypeName({
      categoryId: this.$store.state.design?.pageInfo?.id,
    }).then((res) => {
      this.templateTagList = res.data;
    });
    this.$Bus.$on("triggerAddText", this.showTab);
    this.$Bus.$on("triggerActiveItem", this.activeItem);
    this.$Bus.$on("triggerAddArt", this.addArt);
    this.$Bus.$on("triggerUpload", this.triggerUpload);
    this.$Bus.$on("showDefault", this.showDefault);
    this.$Bus.$on("getHistoryPicColorRecords", this.getHistoryPicColorRecords);
    this.$Bus.$on(
      "getHistoryTextColorRecords",
      this.getHistoryTextColorRecords
    );
    this.$Bus.$on(
      "synchroSelectedImgColorLeft",
      this.synchroSelectedImgColorLeft
    );
    this.canvas.c.on({
      "mouse:down": (e) => {
        if (this.canvas.c.isDrawingMode) return;
        if (e.target && e.button === 1) {
          this.activeItem(e.target);
          if (e.target.type === "image") {
            this.selectedImgColor = "";
            this.selectedImgReplaceColor = "";
            this.showEditImgColor = false;
          }
        } else {
          this.showDefault();
        }
      },
      "object:removed": (e) => {
        this.showDefault();
      },
      "after:render": () => {
        let count = 1;
        this.layersArr = this.canvas.getLayersArr();
        this.layersArr.forEach((item) => {
          if (item.type === "group") {
            item.count = count++;
          }
        });
      },
    });
  },
  beforeDestroy() {
    this.canvas.c.off();
    this.$Bus.$off("triggerAddText", this.showTab);
    this.$Bus.$off("triggerActiveItem", this.activeItem);
    this.$Bus.$off("triggerAddArt", this.addArt);
    this.$Bus.$off("triggerUpload", this.triggerUpload);
    this.$Bus.$off("showDefault", this.showDefault);
    this.$Bus.$off("getHistoryPicColorRecords", this.getHistoryPicColorRecords);
    this.$Bus.$off(
      "getHistoryTextColorRecords",
      this.getHistoryTextColorRecords
    );
    this.$Bus.$off(
      "synchroSelectedImgColorLeft",
      this.synchroSelectedImgColorLeft
    );
  },
};
</script>
<style scoped lang="scss">
@import "@/assets/css/dzxt_theme";
.metsil-color-div {
  width: 3.5rem;
  height: 2.75rem;
  border: 1px solid #dcdfe5;
  border-radius: 4px;
  padding: 1px;
  .metsil-color {
    width: 3.25rem;
    height: 2.5rem;
    border-radius: 4px;
    background-color: rgb(252, 220, 125);
  }
}
.activeDisabled {
  color: #e6e6e6 !important;
}

.addTextBtn {
  width: 93px;
  height: 40px;
  background: #3053e1;
  border-radius: 6px;
  font-size: 14px;
  white-space: nowrap;
  color: #fff;
}

.addTextBtn.curvedBtn {
  width: auto;
  padding: 0 10px;
  margin-right: 10px;
}

.editColor-colorPicker {
  overflow: hidden;
  display: flex;
  flex-direction: column;
  flex: 1;

  .editColor-colorPicker-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .editColor-colorPicker-selected,
  .editColor-colorPicker-history {
    display: flex;
    align-items: center;
    margin: 12px 0;

    .color-name {
      margin-left: 8px;
    }
  }

  .parentAndChildPanel {
    position: relative;
    width: 100%;
    height: 30px;

    .searchInput {
      ::v-deep {
        .highlight {
          font-weight: 700;
        }
      }

      .inputSearch {
        width: 116%;
        height: 24px;
        font-size: 14px;
        background: #ebebeb;
        border-radius: 17px;
        padding: 0 0 0 14px;
        border: 1px solid transparent;
        transition: all 0.3s;

        &:focus {
          border: 1px solid $dz-primary;
          background-color: #ffffff;
        }
      }

      .suffix-inner {
        position: absolute;
        top: 50%;
        right: -16px;
        transform: translateY(-50%);
        cursor: pointer;

        b {
          color: #a6a6a6;
        }
      }
    }
  }

  .editColor-colorPicker-colorContainer {
    flex: 1;
    overflow: hidden auto;

    .editColor-colorPicker-colors {
      display: grid;
      grid-template-columns: repeat(auto-fill, 2.875rem);
      grid-column-gap: 14px;
      grid-row-gap: 10px;
    }

    .parentGrid {
      display: flex;
      height: 2.875rem;
      // grid-column-gap: 18px;
    }

    .childrenPanel {
      grid-column-gap: 16px;
      width: 100%;
      padding: 10px 0 10px 12px;
      margin-top: -1px;
      background: #f0f0f0;
      border: 1px solid #dbdbdb;
      border-top: none;
    }

    .imgWrapOops {
      margin-top: 25px;
      display: inline-block;
      width: 500px;
      height: 100px;

      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }

    .tip {
      margin: 10px 50px 15px;
      color: #999999;
      font-size: 16px;
    }
  }

  @media screen and (max-width: 768px) {
    .editColor-colorPicker-colorContainer {
      .editColor-colorPicker-colors {
        display: grid;
        grid-template-columns: repeat(auto-fill, 34px);
        grid-column-gap: 6px;
        grid-row-gap: 10px;
      }
    }
  }
}

.ds-left-area {
  display: grid;
  align-items: flex-start;
  height: 100%;
  min-height: 0;
  grid-template-columns: 90px 1fr;
  font-family: Google Sans, Google Sans;
  .ds-left-bar {
    overflow: hidden auto;
    height: 100%;
    min-width: 90px;
    display: flex;
    justify-content: space-between;
    flex-direction: column;
    padding: 50px 0 0;
    background: linear-gradient(180deg, #dee3fa 0%, #fcdbf8 100%);
    color: #3053e1;
    font-size: 14px;

    .tab-item {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      position: relative;
      height: 90px;
      text-align: center;
      cursor: pointer;
      transition: all 0.3s;

      &::before {
        display: none;
        content: "";
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        width: 2px;
        background-color: $dz-primary;
      }

      &:hover {
        background-color: #ffffff;
        color: #333333;

        &::before {
          display: block;
        }
      }

      b {
        margin-bottom: 4px;
        font-size: 26px;
      }
    }

    .tab-item.active {
      background-color: #ffffff;
      color: #333333;

      &::before {
        display: block;
      }
    }
  }

  .ds-left-content {
    overflow: hidden;
    min-width: 0;
    width: 343px;
    // height: calc(100% - 80px);
    height: 100%;
    background: #ffffff;

    .default-content {
      height: 100%;
      padding: 40px 25px;

      h3 {
        margin-bottom: 30px;
        text-align: center;
        font-size: 24px;
        font-weight: bold;
      }

      .dfc-item-wrap {
        display: grid;
        justify-content: center;
        grid-template-columns: repeat(2, 1fr);
        grid-column-gap: 10px;
        grid-row-gap: 10px;
      }

      .dfc-item {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        background: #f4f4f4;
        border-radius: 10px;
        aspect-ratio: 180/120;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.3s;

        b {
          margin-bottom: 4px;
          font-size: 36px;
        }

        &:hover {
          color: $dz-primary;
        }
      }
    }

    .content-box {
      display: flex;
      flex-direction: column;
      height: 100%;
      .topBar {
        justify-content: left;
        padding-left: 25px;
        flex-shrink: 0;
        position: relative;
        ::v-deep {
          .close-icon,
          .back-icon {
            position: absolute;
            top: 50%;
            right: 25px;
            transform: translateY(-50%);
            cursor: pointer;

            &::before {
              content: "";
              position: absolute;
              top: -10px;
              right: -10px;
              bottom: -10px;
              left: -10px;
            }

            b {
              font-weight: 700 !important;
              cursor: pointer;
            }
          }
        }
      }

      > div {
        .hight-light-title {
          color: #3053e1;
          font-size: 16px;
          text-transform: uppercase;
          text-align: center;
          padding: 10px 0;
        }
      }

      .con {
        overflow: hidden auto;
        // flex: 1;
        padding: 10px 0;
        min-height: 146px;
      }
    }

    .draw-content {
      .drawState{
        padding: 0 25px;
      }
      .draw-category {
        .color-group {
          display: grid;
          grid-template-columns: repeat(6, 1fr);
          gap: 20px;
          .color-item {
            grid-column-start: span 2;
            cursor: pointer;
            &:nth-last-child(-n + 1) {
              grid-column-start: span 3;
              .color-ellipse {
                height: 50px;
              }
            }
            .color-ellipse {
              height: 38px;
              border-radius: 999999px;
              background-color: #fff;
              border: 1px solid #b7b7b7;
              position: relative;
              &::before {
                content: "";
                position: absolute;
                background: var(--bg);
                width: calc(100% - 8px);
                height: calc(100% - 8px);
                border-radius: 999999px;
                left: 4px;
                top: 4px;
              }
              &.active {
                border-width: 2px;
                border-color: #3053e1;
                &::after {
                  content: "";
                  position: absolute;
                  height: 8px;
                  width: 12px;
                  border-bottom: 2px solid #fff;
                  border-left: 2px solid #fff;
                  left: 50%;
                  top: 50%;
                  transform: translate(-50%, -70%) rotate(-45deg);
                }
              }
            }
            .color-name {
              margin-top: 6px;
              font-size: 14px;
              text-align: center;
            }
          }
        }
      }
      .draw-raw {
        .myDesigns{
          padding: 5px 0;
          .design-item{
            box-shadow: 1px 1px 5px #ccc;
          }
        }
      }
    }

    .text-content {
      display: flex;
      flex-direction: column;

      .con {
        padding: 17px 25px;
      }

      .addText {
        flex: 1;
        height: calc(100% - 50px);

        label.textLabel {
          display: block;
          background: #f5f5f5;
          border-radius: 10px;
          width: 100%;

          textarea {
            border: 0;
            color: inherit;
            outline-width: 0;
            -webkit-overflow-scrolling: touch;
            padding: 1rem;
            resize: none;
            white-space: pre;
            width: 100%;
            background: #f5f5f5;
            border-radius: 10px;
          }
        }

        .addTextBtnGroup {
          display: flex;
          justify-content: flex-end;
          margin-top: 10px;
        }
      }

      .editText {
        flex: 1;
        height: calc(100% - 50px);
        display: flex;
        flex-direction: column;

        .con {
          overflow: hidden auto;
          // flex: 1;

          .applyAll {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1rem;
          }
        }

        label {
          display: block;
          background: #f5f5f5;
          border-radius: 10px;
          width: 100%;
        }

        textarea {
          border: 0;
          color: inherit;
          outline-width: 0;
          -webkit-overflow-scrolling: touch;
          padding: 1rem;
          resize: none;
          white-space: pre;
          width: 100%;
          background: #f5f5f5;
          border-radius: 10px;
        }

        .editWrap {
          padding: 0 25px;
          .edit-item {
            position: relative;
            align-items: center;
            border-bottom: 1px solid rgb(216, 216, 216);
            display: flex;
            height: 3.5rem;
            flex-shrink: 0;
            font-size: 1rem;
            padding: 0 0.5rem;

            svg {
              width: 2rem;
              height: 2rem;
            }

            .edit-item-title {
              flex: 1 0 auto;
              min-width: 80px;
            }

            .edit-item-content {
              align-items: center;
              display: flex;
              flex: 0 1 auto;

              & > * {
                margin-left: 0.5rem;
              }
            }

            .edit-item-chevron {
              display: flex;
              padding-left: 0.5rem;
              flex-flow: column;
              justify-content: center;
              color: #9ea6ad;
            }
          }

          .edit-angle,
          .edit-spacing,
          .edit-size,
          .edit-curve {
            .edit-item-title {
              flex: 0 1 auto;
            }

            .edit-item-content {
              flex: 1 0 auto;

              .el-slider {
                margin-left: 1rem;
                width: 100%;
              }
            }
          }

          .edit-align,
          .edit-decoration {
            .align-icon {
              margin: 0 0.5rem;
              cursor: pointer;

              b {
                color: #666666;
              }

              &.active {
                color: $dz-primary;

                b {
                  color: inherit;
                }
              }
            }
          }

          .edit-outline {
            .color-picker {
              width: 1.5rem;
              height: 1.5rem;
              border-radius: 2px;
              border: 1px solid #f2f2f2;
            }

            .line {
              background: #4a4a4a;
              width: 0.1rem;
              height: 1.5rem;
            }
          }

          .edit-color {
            .color-group {
              display: grid;
              grid-template-columns: repeat(6, 1fr);
              gap: 20px;
              .color-item {
                grid-column-start: span 2;
                cursor: pointer;
                &:nth-last-child(-n + 1) {
                  grid-column-start: span 3;
                  .color-ellipse {
                    height: 50px;
                  }
                }
                .color-ellipse {
                  height: 38px;
                  border-radius: 999999px;
                  background-color: #fff;
                  border: 1px solid #b7b7b7;
                  position: relative;
                  &::before {
                    content: "";
                    position: absolute;
                    background: var(--bg);
                    width: calc(100% - 8px);
                    height: calc(100% - 8px);
                    border-radius: 999999px;
                    left: 4px;
                    top: 4px;
                  }
                  &.active {
                    border-width: 2px;
                    border-color: #3053e1;
                    &::after {
                      content: "";
                      position: absolute;
                      height: 8px;
                      width: 12px;
                      border-bottom: 2px solid #fff;
                      border-left: 2px solid #fff;
                      left: 50%;
                      top: 50%;
                      transform: translate(-50%, -70%) rotate(-45deg);
                    }
                  }
                }
                .color-name {
                  margin-top: 6px;
                  font-size: 14px;
                  text-align: center;
                }
              }
            }
          }

          .edit-font,
          .edit-color,
          .edit-shape,
          .edit-outline {
            cursor: pointer;

            &:hover {
              .edit-item-title {
                color: $dz-primary;
              }
            }
          }
        }
      }

      .editTextFont {
        flex: 1;
        height: calc(100% - 50px);
        display: flex;
        flex-direction: column;

        .con {
          display: flex;
          flex-direction: column;
          flex: 1;

          .font-button-wrap {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 2rem;
            color: $dz-primary;

            .arrow-btn,
            .recent-btn {
              display: flex;
              cursor: pointer;

              b {
                margin-right: 0.1rem;
                font-size: 18px;
              }
            }
          }

          .font-wrap {
            overflow: hidden auto;
            flex: 1;

            .font-item {
              display: flex;
              flex-direction: column;
              align-items: center;
              padding: 1rem;
              width: 100%;
              overflow: hidden;
              cursor: pointer;
              transition: all 0.3s;

              .font-text {
                font-size: 1.2rem;
              }

              &:hover {
                background-color: #f2f2f2;

                .font-text {
                  color: $dz-primary;
                }
              }
            }
          }
        }
      }

      .editTextColor {
        flex: 1;
        height: calc(100% - 50px);
        display: flex;
        flex-direction: column;

        .con {
          display: flex;
          flex-direction: column;
          flex: 1;
          overflow: hidden;

          .editColor-colorPicker-history {
            ::v-deep .color-item {
              margin-right: 14px;
            }
          }

          .editTextColor-foot {
            display: flex;
            justify-content: flex-end;
            align-items: center;
            padding: 1rem;
          }
        }
      }

      .editTextShape {
        .title {
          margin-bottom: 1rem;
          font-size: 18px;
          font-weight: 600;
        }

        .textShapeList {
          display: grid;
          grid-template-columns: repeat(4, 1fr);
          margin-bottom: 20px;
          border-top: 1px solid #e6e6e6;
          border-left: 1px solid #e6e6e6;

          .shape-item {
            display: flex;
            justify-content: center;
            align-items: center;
            border-right: 1px solid #e6e6e6;
            border-bottom: 1px solid #e6e6e6;
            height: 64px;
            cursor: pointer;

            &:hover {
              background-color: #f2f2f2;
            }
          }
        }

        .slider {
          padding: 1rem;
          background-color: #f2f2f2;
          border-radius: 4px;

          .slider-slider {
            padding: 0 1rem;
          }

          .slider-label {
            display: flex;
            justify-content: space-between;
          }
        }
      }

      .editTextOutline {
        flex: 1;
        height: calc(100% - 50px);
        display: flex;
        flex-direction: column;

        .con {
          overflow: hidden;
          display: flex;
          flex-direction: column;
          flex: 1;

          .outline-slider {
            padding: 1rem;
            background-color: #f2f2f2;
            border-radius: 4px;

            .slider-slider {
              padding: 0 1rem;
            }

            .slider-label {
              display: flex;
              justify-content: space-between;
            }
          }

          .outline-width-title {
            margin-bottom: 1rem;
            font-size: 18px;
            font-weight: 600;
          }

          .outline-color-title {
            font-size: 18px;
            font-weight: 600;
          }

          .outline-color {
            overflow: hidden;
            display: flex;
            flex-direction: column;
            flex: 1;
            margin-top: 1rem;
            height: 0;

            .outline-stroke-color {
              display: flex;
              flex-direction: column;
              overflow: hidden;
              flex: 1;

              .outline-stroke-color-selected {
                display: flex;
                align-items: center;
                margin: 12px 0;

                .color-name {
                  margin-left: 8px;
                }
              }

              .outline-stroke-color-colorPicker {
                display: grid;
                grid-template-columns: repeat(auto-fill, 2.875rem);
                grid-column-gap: 5px;
                grid-row-gap: 5px;
                flex: 1;
                overflow: hidden auto;
              }
            }
          }

          .outline-btn {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 1rem;

            button {
              width: 11rem;
            }
          }
        }
      }
    }

    .art-content {
      display: flex;
      flex-direction: column;

      .con {
        overflow: hidden auto;
        flex: 1;
        display: grid;
        align-content: flex-start;
        justify-content: center;
        grid-column-gap: 16px;
        grid-row-gap: 10px;
        padding: 7px 25px;

        .box-item {
          display: flex;
          justify-content: center;
          align-items: center;
          background-color: #f5f5f5;
          border-radius: 10px;
          border: 1px solid transparent;
          transition: all 0.3s;
          cursor: pointer;

          &.active {
            border-color: $dz-primary;
            box-shadow: 0 3px 5px 0 rgba(0, 0, 0, 0.24);
            background: linear-gradient(0deg, #f8fcff 0%, #eff7ff 100%);
          }

          &:hover {
            border-color: $dz-primary;
            box-shadow: 0 3px 5px 0 rgba(0, 0, 0, 0.24);
            background: linear-gradient(0deg, #f8fcff 0%, #eff7ff 100%);
          }
        }
      }

      .art-category {
        flex: 1;
        height: 0;
        display: flex;
        flex-direction: column;

        .con {
          grid-template-columns: repeat(2, 1fr);
          grid-gap: 10px 16px;

          .box-item {
            display: flex;
            flex-direction: column;
            aspect-ratio: 156/108;
            min-width: 0;
            background-color: #f5f5f5;

            .imgWrap {
              width: 34px;
              height: 34px;
              margin-top: 0;

              img {
                width: 100%;
                height: 100%;
                object-fit: contain;
              }
            }

            b {
              color: $dz-primary;
              font-size: 34px;
            }

            .item-name {
              margin-top: 10px;
              max-width: 95%;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              text-align: center;
            }
          }

          .box-item.box-item-style0 {
            aspect-ratio: 156/60;

            b {
              display: none;
            }

            .imgWrap {
              display: none;
            }

            .item-name {
              margin-top: 0;
            }
          }
        }
      }

      .editImg {
        flex: 1;
        height: calc(100% - 50px);
        display: flex;
        flex-direction: column;

        .con {
          display: block;
          overflow: hidden auto;
          flex: 1;
        }

        .currentPic {
          display: flex;

          .imgWrap {
            display: flex;
            justify-content: center;
            align-items: center;
            flex: 0 0 138px;
            height: 138px;
            aspect-ratio: 1;
            padding: 10px;

            img {
              width: 100%;
              height: 100%;
              object-fit: contain;
            }
          }

          .imgReplaceArea {
            flex: 1;
            margin-left: 24px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            & > div {
              margin-bottom: 10px;
            }

            .el-button {
              background-color: #3053e1;
              border-radius: 10px;
            }
          }
        }

        .editWrap {
          margin-top: 20px;
          // padding: 0 25px;
          .edit-item {
            position: relative;
            align-items: center;
            border-bottom: 1px solid rgb(216, 216, 216);
            display: flex;
            height: 3.5rem;
            flex-shrink: 0;
            font-size: 1rem;
            svg {
              width: 2rem;
              height: 2rem;
            }

            .edit-item-title {
              flex: 1 0 auto;
              min-width: 80px;
            }

            .edit-item-content {
              align-items: center;
              display: flex;
              flex: 0 1 auto;

              & > * {
                margin-left: 0.5rem;
              }
            }

            .edit-item-chevron {
              display: flex;
              padding-left: 0.5rem;
              flex-flow: column;
              justify-content: center;
              color: #9ea6ad;
            }
          }

          .edit-angle,
          .edit-size {
            .edit-item-title {
              flex: 0 1 auto;
            }

            .edit-item-content {
              flex: 1 0 auto;

              .el-slider {
                margin-left: 1rem;
                width: 100%;
              }
            }
          }

          .edit-color {
            .color-group {
              display: grid;
              grid-template-columns: repeat(6, 1fr);
              gap: 20px;
              .color-item {
                grid-column-start: span 2;
                cursor: pointer;
                .color-ellipse {
                  height: 38px;
                  border-radius: 999999px;
                  background-color: #fff;
                  border: 1px solid #b7b7b7;
                  position: relative;
                  &::before {
                    content: "";
                    position: absolute;
                    background: var(--bg);
                    width: calc(100% - 8px);
                    height: calc(100% - 8px);
                    border-radius: 999999px;
                    left: 4px;
                    top: 4px;
                  }
                  &.active {
                    border-width: 2px;
                    border-color: #3053e1;
                    &::after {
                      content: "";
                      position: absolute;
                      height: 8px;
                      width: 12px;
                      border-bottom: 2px solid #fff;
                      border-left: 2px solid #fff;
                      left: 50%;
                      top: 50%;
                      transform: translate(-50%, -70%) rotate(-45deg);
                    }
                  }
                }
                .color-name {
                  margin-top: 6px;
                  font-size: 14px;
                  text-align: center;
                }
              }
            }
          }

          .editImgColor {
            display: flex;
            flex-direction: column;
            padding: 0.5rem 0;
            background-color: #fafafa;

            .editColor-colorPicker-selected,
            .editColor-colorPicker-history {
              display: grid;
              grid-template-columns: repeat(auto-fill, 2.875rem);
              grid-column-gap: 18px;
              grid-row-gap: 10px;
              max-height: 110px;
              overflow: hidden auto;
            }

            .editColor-colorPicker-colorContainer {
              margin-bottom: 10px;
            }

            .editColor-colorPicker-colors {
              max-height: 170px;
              overflow: hidden auto;
            }

            .editImgColor-foot {
              padding: 1rem;
              display: flex;
              justify-content: center;
              align-items: center;
            }
          }
        }
      }
    }

    .layer-content {
      .con {
        padding: 10px;
      }

      .layer-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 60px;
        border-bottom: 1px solid #e6e6e6;

        &:hover {
          height: 60px;
          line-height: 60px;
          background-color: #f5f5f5;
        }

        &.isChildren {
          padding-left: 20px;
        }

        &.moving {
          background: #f2f2f2;
          color: transparent;
        }

        .layer-left {
          flex: 1;
          color: #999999;

          .layer-left-img {
            background-color: #f5f5f5;
            width: 70px;
            height: 50px;
            line-height: 50px;
            text-align: center;
          }

          .layer-text {
            max-width: 250px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            color: #999999;
          }

          .showChildren {
            transform: rotate(90deg);
          }

          img {
            max-width: 40px;
            max-height: 40px;
            object-fit: contain;
          }
        }

        .layer-right {
          display: flex;
          flex-shrink: 0;

          .layer-icon {
            position: relative;
            cursor: pointer;
            margin: 0 10px;

            &::before {
              content: "";
              position: absolute;
              top: -10px;
              right: -10px;
              bottom: -10px;
              left: -10px;
            }
          }

          b {
            font-size: 24px;
          }
        }
      }
    }

    .upload-content {
      .con {
        display: flex;
        flex-direction: column;
        padding: 10px;

        .topArea {
          margin-bottom: 30px;

          .upload-area {
            overflow: hidden;
            position: relative;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            aspect-ratio: 509/224;
            background: #fafbfc;
            border: 1px solid #dadcdf;
            border-radius: 10px;
            margin: 14px 0 22px 0;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;

            &:hover {
              border-color: $dz-primary;
            }

            &.dropActive {
              border-color: $dz-primary;
            }

            input[type="file"] {
              position: absolute;
              opacity: 0;
              z-index: -1;
            }

            .upload-icon {
              font-size: 50px;
              color: #cccccc;
            }

            .upload-text {
              margin: 10px 0;
              line-height: 1.4;

              span {
                color: $dz-primary;
              }
            }

            .upload-tips {
              color: #999999;
              font-size: 14px;
            }
          }

          .tips {
            font-size: 14px;
            color: #999999;
          }
        }

        .bottomArea {
          flex: 1;
          padding-top: 30px;
          border-top: 1px solid #e6e6e6;

          .title {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;

            .title-right {
              color: #999999;
              cursor: pointer;

              b {
                margin-right: 4px;
              }
            }
          }

          .upload-history {
            display: grid;
            align-content: flex-start;
            grid-template-columns: repeat(3, 1fr);
            grid-row-gap: 16px;
            grid-column-gap: 16px;

            .history-item {
              overflow: hidden;
              display: flex;
              justify-content: center;
              align-items: center;
              position: relative;
              padding: 10px;
              background: #ffffff;
              border: 1px solid #e0e0e0;
              aspect-ratio: 1;
              cursor: pointer;

              img {
                object-fit: contain;
              }

              .list-item-btn {
                display: flex;
                justify-content: center;
                align-items: center;
                opacity: 0;
                content: "";
                position: absolute;
                left: 0;
                right: 0;
                top: 0;
                bottom: 0;
                background-color: rgba(0, 0, 0, 0.25);
                transition: all 0.3s;

                & > div {
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  margin: 0 4px;
                  width: 48px;
                  height: 48px;
                  background: $dz-primary;
                  border-radius: 50%;
                  border: none;

                  b {
                    color: #ffffff;
                    font-size: 24px;
                  }
                }

                & > div:last-child {
                  background-color: #ffffff;

                  b {
                    color: #b3b3b3;
                  }
                }
              }

              &:hover {
                .list-item-btn {
                  opacity: 1;
                }
              }
            }
          }
        }
      }
    }

    .product-content {
      .con {
        display: grid;
        justify-content: center;
        align-content: flex-start;
        grid-template-columns: repeat(2, 1fr);
        grid-column-gap: 10px;
        grid-row-gap: 10px;
        padding: 10px;

        .box-item {
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          cursor: pointer;

          &.active {
            .imgWrap {
              border-color: $dz-primary;
              box-shadow: 0 3px 5px 0 rgba(0, 0, 0, 0.24);
            }
          }

          &:hover {
            .imgWrap {
              border-color: $dz-primary;
              box-shadow: 0 3px 5px 0 rgba(0, 0, 0, 0.24);
            }
          }

          .imgWrap {
            width: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            aspect-ratio: 1;
            background: #f2f2f2;
            margin-bottom: 10px;
            border: 1px solid transparent;
            transition: all 0.3s;

            img {
              object-fit: cover;
            }
          }
        }
      }
    }

    .myDesign-content {
      .no-login {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        margin-bottom: 20px;
        padding: 40px 0;

        .tip-icon {
          margin-bottom: 10px;
        }

        .tip-text {
          color: #666666;
        }

        button {
          margin-top: 20px;
        }
      }

      .loginBox {
        position: relative;
        display: flex;
        flex-direction: column;
        height: 100%;

        .topTab {
          display: flex;
          align-items: center;
          justify-content: center;
          position: relative;
          font-size: 18px;
          height: 50px;
          border-bottom: 1px solid #ccc;

          .tab-item {
            display: flex;
            justify-content: center;
            align-items: center;
            min-width: 112px;
            height: 100%;
            margin: 0 40px -1px;
            font-size: 18px;
            font-weight: 700;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.3s;

            &.active {
              color: $dz-primary;
              border-color: $dz-primary;
            }

            &:hover {
              color: $dz-primary;
              border-color: $dz-primary;
            }
          }

          .close-icon,
          .back-icon {
            position: absolute;
            top: 50%;
            right: 10px;
            transform: translateY(-50%);
            cursor: pointer;

            &::before {
              content: "";
              position: absolute;
              top: -10px;
              right: -10px;
              bottom: -10px;
              left: -10px;
            }

            b {
              font-weight: 700 !important;
              cursor: pointer;
            }
          }

          .back-icon {
            right: auto;
            left: 10px;
          }
        }

        .myTemplate,
        .myPic {
          flex: 1;
          overflow: hidden auto;
        }

        .myTemplate {
          display: flex;
          flex-direction: column;

          .template-tab {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 80px;
            flex-shrink: 0;
            cursor: pointer;

            .template-tab-item {
              width: 126px;
              height: 30px;
              line-height: 30px;
              background: #f2f2f2;
              border-radius: 15px;
              color: #333333;
              text-align: center;
              transition: all 0.3s;

              &:first-child {
                margin-right: -15px;
              }

              &.active {
                background: $dz-primary;
                color: #ffffff;
                z-index: 1;
              }

              &:hover {
                background: $dz-primary;
                color: #ffffff;
                z-index: 1;
              }
            }
          }

          .myFavoriteTemplate,
          .myCreationTemplate {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            flex: 1;
            align-content: flex-start;
            overflow: hidden auto;
            padding: 10px;
            grid-column-gap: 20px;
            grid-row-gap: 20px;

            .favorite-item {
              display: flex;
              flex-direction: column;
              justify-content: center;
              align-items: center;
              position: relative;

              .template-name {
                margin: 10px 0 0;
              }

              .template-create-time {
                color: #999999;
              }

              .imgWrap {
                position: relative;
                width: 100%;
                aspect-ratio: 2/1;
                background: #f2f2f2;

                img {
                  object-fit: contain;
                }

                .list-item-btn {
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  opacity: 0;
                  content: "";
                  position: absolute;
                  left: 0;
                  right: 0;
                  top: 0;
                  bottom: 0;
                  background-color: rgba(0, 0, 0, 0.4);
                  transition: all 0.3s;

                  & > div {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    margin: 0 4px;
                    width: 48px;
                    height: 48px;
                    background: $dz-primary;
                    border-radius: 50%;
                    cursor: pointer;

                    b {
                      color: #ffffff;
                      font-size: 24px;
                    }
                  }

                  & > div:last-child {
                    background-color: #ffffff;

                    b {
                      color: #b3b3b3;
                    }
                  }
                }
              }

              &:hover {
                .list-item-btn {
                  opacity: 1;
                }
              }
            }
          }

          .myFavoriteTemplate {
            .favorite-item {
              .imgWrap {
                .list-item-btn {
                  & > div:last-child {
                    background-color: #ce3d3a;

                    b {
                      color: #ffffff;
                    }
                  }
                }
              }
            }
          }
        }

        .myPic {
          display: flex;
          flex-direction: column;
          padding: 10px;

          .upload-area {
            position: relative;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            height: 150px;
            background: #fafbfc;
            border: 1px dashed #dadcdf;
            border-radius: 10px;
            margin: 14px 0 22px 0;
            text-align: center;
            cursor: pointer;

            input[type="file"] {
              position: absolute;
              opacity: 0;
              z-index: -1;
            }

            .upload-icon {
              font-size: 50px;
              color: #cccccc;
            }

            .upload-text {
              margin: 10px 0;
              line-height: 1.4;

              span {
                color: $dz-primary;
              }
            }
          }

          .myUpload {
            overflow: hidden auto;
            flex: 1;
            height: 0;
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            grid-column-gap: 10px;
            grid-row-gap: 10px;
            align-content: flex-start;

            .upload-item {
              min-width: 0;
              display: flex;
              flex-direction: column;
              cursor: pointer;

              .top {
                flex: 1;
                position: relative;
                display: flex;
                flex-wrap: wrap;
                justify-content: center;
                align-items: center;
                aspect-ratio: 1;
                border: 1px solid #e6e6e6;
                border-radius: 6px;
                padding: 5px;

                .imgWrap {
                  flex-basis: calc(50% - 4px);
                  flex-shrink: 0;
                  flex-grow: 0;
                  margin: 2px;
                  aspect-ratio: 1;
                  display: flex;
                  justify-content: center;
                  align-items: center;

                  img {
                    object-fit: cover;
                  }
                }
              }

              .top.noImg {
                .imgWrap {
                  width: 100%;
                  height: 100%;

                  img {
                    width: 100px;
                    height: 100px;
                  }
                }
              }

              .bottom {
                display: flex;
                justify-content: space-between;
                align-items: center;
                height: 40px;

                .folder-name {
                  flex: 1;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                }

                .more {
                  flex-shrink: 0;
                  padding: 4px;
                  margin-left: 10px;
                  background: #edf1f5;
                  border-radius: 8px;
                  cursor: pointer;
                }
              }
            }
          }
        }

        .myCustomUploadBox {
          display: flex;
          flex-direction: column;
          overflow: hidden;
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-color: #ffffff;
          z-index: 2;

          .topBar {
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            font-size: 18px;
            height: 50px;
            border-bottom: 1px solid #ccc;

            .close-icon,
            .back-icon {
              position: absolute;
              top: 50%;
              right: 10px;
              transform: translateY(-50%);
              cursor: pointer;

              &::before {
                content: "";
                position: absolute;
                top: -10px;
                right: -10px;
                bottom: -10px;
                left: -10px;
              }

              b {
                font-weight: 700 !important;
                cursor: pointer;
              }
            }

            .back-icon {
              right: auto;
              left: 10px;
            }
          }

          .con {
            display: flex;
            flex-direction: column;
            overflow: hidden;
            flex: 1;
            padding: 10px;

            .controlTab {
              display: flex;
              justify-content: space-between;
              align-items: center;
              height: 40px;
              margin-bottom: 10px;
            }

            .boxWrap {
              overflow: hidden auto;
              flex: 1;
              display: grid;
              align-content: flex-start;
              grid-template-columns: repeat(3, 1fr);
              grid-column-gap: 10px;
              grid-row-gap: 10px;

              .box-item {
                display: flex;
                justify-content: center;
                align-items: center;
                position: relative;
                cursor: pointer;
                aspect-ratio: 1;

                .list-item-btn {
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  opacity: 0;
                  content: "";
                  position: absolute;
                  left: 0;
                  right: 0;
                  top: 0;
                  bottom: 0;
                  background-color: rgba(0, 0, 0, 0.25);
                  transition: all 0.3s;

                  & > div {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    margin: 0 4px;
                    width: 48px;
                    height: 48px;
                    background: $dz-primary;
                    border-radius: 50%;
                    border: none;

                    b {
                      color: #ffffff;
                      font-size: 24px;
                    }
                  }

                  & > .del {
                    background-color: #ffffff;

                    b {
                      color: #b3b3b3;
                    }
                  }

                  & > .cancelLove {
                    background-color: #ce3d3a;
                  }
                }

                .checkbox {
                  position: absolute;
                  top: 10px;
                  right: 10px;
                }

                &:hover {
                  .list-item-btn {
                    opacity: 1;
                  }
                }
              }
            }

            .btnWrap {
              padding: 1rem;
              display: flex;
              justify-content: flex-end;
              align-items: center;
            }
          }
        }

        .myCustomUploadBox.isFavorite {
          .con {
            .controlTab {
              display: none;
            }

            .checkbox {
              display: none;
            }
          }
        }
      }
    }
  }
}
</style>
