
/**
 * OSS文件上传服务
 * @module services/ossUpload
 * <AUTHOR>
 */

import axios from 'axios';
import { generateRandomFileName, getFilePrefix } from "@/utils/utils";

// 创建axios实例
const ossApiClient = axios.create({
	baseURL: process.env.NODE_ENV === 'development' ? '' : '',
	// timeout: 10000
});

// 常量配置
const OSS_CONFIG = {
	POLICY_URL: 'https://products-api-o2o-prod.gs-souvenir.com/gyoss/oss/web/config',
	INVALID_CHARS_REGEX: /[\!\@\#\$\%\^\&\*\(\)\+\=\{\}\|\[\]\\\:\"\;\'\<\>\?\,\.\/\`\~]/gi,
	DEFAULT_DIR_CONFIG: {
		systemName: 'customOss',
		modelDir: 'public/picStore',
		isSSL: 1
	}
};

/**
 * 获取OSS签名
 * @param {string} url - 签名接口URL
 * @param {Object} data - 请求数据
 * @returns {Promise<Object>} 签名数据
 */
const getSignature = (url, data) => {
	return ossApiClient({
		url: url,
		method: 'post',
		data: data,
		headers: {
			'Content-Type': 'application/x-www-form-urlencoded'
		}
	});
};

/**
 * 上传文件到OSS
 * @param {string} url - OSS上传地址
 * @param {FormData} data - 表单数据
 * @param {Function} [progressCallback] - 上传进度回调
 * @returns {Promise} 上传请求Promise
 */
const uploadToOss = (url, data, progressCallback) => {
	return ossApiClient({
		url: url,
		method: 'post',
		data: data,
		headers: {
			'Content-Disposition': 'inline'
		},
		onUploadProgress: progressEvent => {
			if (typeof progressCallback === 'function') {
				progressCallback(progressEvent);
			}
		}
	});
};

/**
 * 生成安全的文件名
 * @param {File} file - 文件对象
 * @param {boolean} useRandomName - 是否使用随机文件名
 * @param {string|null} date - 日期字符串
 * @returns {string} 安全文件名
 */
const generateSafeFileName = (file, useRandomName = true, date = null) => {
	let prefix = getFilePrefix(file.name)
		.replace(/\s+/g, '_')
		.replace(OSS_CONFIG.INVALID_CHARS_REGEX, "");

	if (useRandomName) {
		return `${prefix}_${generateRandomFileName(file)}`;
	}
	return prefix;
};

/**
 * 构建OSS上传表单数据
 * @param {Object} signature - OSS签名对象
 * @param {string} fileName - 文件名
 * @param {File} file - 文件对象
 * @returns {FormData} 表单数据
 */
const buildFormData = (signature, fileName, file) => {
	const formData = new FormData();
	const filePath = signature.dir.endsWith("/") ? signature.dir : `${signature.dir}/`;

	// 添加OSS必需参数
	formData.append("policy", signature.policy);
	formData.append("OSSAccessKeyId", signature.accessid);
	formData.append("signature", signature.signature);
	formData.append("success_action_status", "201");
	formData.append("key", `${filePath}${fileName}`);
	formData.append("name", fileName);

	// 最后添加文件内容
	formData.append("file", file);
	return formData;
};

/**
 * 上传文件到OSS
 * @param {File} file - 要上传的文件
 * @param {Object} options - 上传选项
 * @param {boolean} [options.isRandomName=true] - 是否使用随机文件名
 * @param {string|null} [options.date=null] - 日期路径
 * @param {Function} [progressCallback] - 上传进度回调
 * @returns {Promise<string>} 文件URL
 */
export const uploadFile = async (file, options = {}, progressCallback) => {
	const { isRandomName = true, date = null } = options;

	// 准备目录配置
	const dirConfig = {
		...OSS_CONFIG.DEFAULT_DIR_CONFIG,
		modelDir: date ? `${OSS_CONFIG.DEFAULT_DIR_CONFIG.modelDir}/${date}` : OSS_CONFIG.DEFAULT_DIR_CONFIG.modelDir
	};

	// 生成表单数据
	const formData = new FormData();
	Object.entries(dirConfig).forEach(([key, value]) => {
		formData.append(key, value);
	});

	try {
		// 获取签名
		const signatureResponse = await getSignature(OSS_CONFIG.POLICY_URL, formData);
		const signature = signatureResponse.data.data;

		// 准备上传数据
		const fileName = generateSafeFileName(file, isRandomName);
		const ossData = buildFormData(signature, fileName, file);

		// 执行上传
		await uploadToOss(signature.url, ossData, progressCallback);

		// 返回完整URL
		return `${signature.url}${ossData.get("key")}`;
	} catch (error) {
		console.error("OSS文件上传失败:", error);
		throw new Error("OSS文件上传失败");
	}
};