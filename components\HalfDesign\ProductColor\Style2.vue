<template>
	<div class="mb-4" :class="stepData.styleName">
		<half-design-litter-title v-show="stepData.attributeTitle"  :index="stepData.id" :selectedValue="shape"  :data-name="`${stepData.styleClass + '_' + stepData.id}`" style="margin: 4px 0" :stepTitle="stepData.minStepTitle" >{{ stepData.attributeTitle }} </half-design-litter-title>
		<slot name="stepText"></slot>
		<div class="step-content" v-if="!stepData.isHideStep || (stepData.isHideStep && stepData.productParamList.length > 1)">
			<template v-for="(step, index) in stepData.productParamList">
				<v-tooltip top>
					<template v-slot:activator="{ on, attrs }">
						<div class="step-item" v-bind="attrs" v-on="on" :class="{ active: index === selectIndex }" :style="{ backgroundColor: step.colorCode }" :key="index" @click="selectStep(step, index)"></div>
					</template>
					<span>{{ step.colorAlias }}</span>
				</v-tooltip>
			</template>
		</div>
		<div class="errorTip">
			<v-alert dense outlined type="error">
				{{ langSemiCustom.errorTip }}
			</v-alert>
		</div>
	</div>
</template>
<script>

export default {
	props: {
		stepData: {
			type: Object,
			default: ()=>({}),
		},
	},
	data() {
		return {
			selectIndex: -1,
			selectItem: null,
		};
	},
	computed: {
		langSemiCustom() {
			return this.$store.getters.lang?.semiCustom;
		},
		shape() {
			return this.selectItem?.valueName;
		},
	},
	methods: {
		selectStep(item, index, state = false) {
			this.selectIndex = index;
			this.selectItem = item;
			this.$emit("selectStep", {
				type: this.stepData.attributeFlag,
				data: item,
				parent: this.stepData,
				id: this.stepData.id,
				firstSelect: state,
				colorIndex: index
			});
		},
		selectDefault() {
			if (this.selectIndex <= -1) {
				this.selectStep(this.stepData.productParamList[0], 0, true);
			}
		},
		setActiveProductColor() {
			let newColorList = this.stepData.productParamList.filter(color => {
				return color.isActivity == 1
			})
			if (newColorList.length == 0) {
				return
			}
			this.stepData.productParamList = newColorList
		}
	},
	mounted() {
		this.$Bus.$on("selectDefaultColorStep", this.selectDefault);
		this.$Bus.$on("setActiveProductColor", this.setActiveProductColor);
	},
	beforeDestroy() {
		this.$Bus.$off("selectDefaultColorStep", this.selectDefault);
		this.$Bus.$off("setActiveProductColor", this.setActiveProductColor);
	},
};
</script>
<style scoped lang="scss">
@import "~assets/css/half.scss";

.style1 .step-content {
	display: grid;
	grid-template-columns: repeat(auto-fill, 40px);
	grid-gap: 10px;

	.step-item {
		position: relative;
		border: 1px solid $border-color;
		cursor: pointer;
		aspect-ratio: 1;
		height: 40px;
		&::after {
			display: none;
			content: "";
			position: absolute;
			bottom: 0;
			left: 50%;
			transform: translateX(-50%);
			width: 0;
			height: 0;
			border-width: 0 5px 5px;
			border-style: solid;
			border-color: transparent transparent $color-primary;
		}

		@media (any-hover: hover) {
			&:hover {
				border-color: $color-primary;
			}
		}
	}

	.step-item.active {
		border-color: $color-primary;
		border-width: 2px;
		box-shadow: 0 3px 3px -2px rgba(0, 0, 0, 0.2), 0 3px 4px 0 rgba(0, 0, 0, 0.14), 0 1px 8px 0 rgba(0, 0, 0, 0.12);

		&::after {
			display: block;
		}
	}
}



@include respond-to(mb) {
	.style1 .step-content {
		grid-template-columns: repeat(auto-fill, 30px);
	}
}
</style>
