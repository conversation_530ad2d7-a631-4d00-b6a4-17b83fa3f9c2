<template>
	<div class="size">
		<div class="sizeBox" v-for="item in itemData.childList" :key="item.id">
			<div class="imgWrap">
				<img class="img" :class="{ active: index == 0 }" v-for="(item2, index) in item.childList" :key="item2.id" :src="getImg(item2)" alt="" />
			</div>
			<div class="textBox">
				<div class="textItem">{{ item.alias }}</div>
				<div class="textItem" v-for="item2 in item.childList" :key="item2.id">{{ item2.alias }}</div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	name: "",
	components: {},
	props: {
		itemData: {
			type: Object,
			default: () => {},
		},
	},
	data() {
		return {};
	},
	methods: {
		getImg(item) {
			if (item.imageJson) {
				let imgUrl = JSON.parse(item.imageJson);
				return imgUrl[0].url;
			} else {
				return "";
			}
		},
	},
	created() {},
	mounted() {},
	computed: {},
	watch: {},
};
</script>
<style scoped lang="scss">
.size {
	width: 100%;
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 20px;
	.sizeBox {
		padding: 16px;
		display: flex;
		gap: 10px;
		background: #ffffff;
		border-radius: 10px;
		border: 1px solid #dfdfdf;
		.imgWrap {
			flex: 1;
			display: flex;
			align-items: center;
			justify-content: center;
			.img {
				width: 100%;
				height: 100%;
				object-fit: contain !important;
				aspect-ratio: 1/1;
				display: none;
				&.active {
					display: block;
				}
			}
		}
		.textBox {
			flex: 1;
			display: flex;
			flex-direction: column;
			.textItem {
				font-size: 16px;
				color: #333333;
				text-align: center;
				border-bottom: 1px solid #dfdfdf;
				padding: 0.6em 2em;
				&:first-child {
					color: #ffffff;
					border-radius: 0 10px 0 0;
					background-color: #969eb5 !important;
					border-bottom: 1px solid #969eb5 !important;
				}
				&:last-child {
					border: none !important;
				}
				&:nth-child(even) {
					background: #fff;
				}
				&:nth-child(odd) {
					background: #f6f6f6;
				}
			}
		}
	}
	@include respond-to(mb) {
		grid-template-columns: 1fr;
		gap: 10px;
		.sizeBox {
			padding: 8px;
			.textBox {
				.textItem {
					font-size: 12px;
					padding: 0.4em 0.8em;
				}
			}
		}
	}
}
</style>
