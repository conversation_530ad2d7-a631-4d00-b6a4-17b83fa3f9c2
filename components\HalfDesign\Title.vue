<template>
	<div class="step-title">
		<div class="step">
			<v-btn icon small v-show="step > 1" @click="goPrev(step)">
				<b class="icon-back"></b>
			</v-btn>
			<template v-if="isMobile&&$store.state.proTheme!='11'"> Step {{ String(step).padStart(2, "0") }}: </template>
			<template v-else-if="isMobile&&$store.state.proTheme=='11'">{{step}}<span>.</span> </template>
			<template v-else>
				{{ step }}<span>.</span>
			</template>
		</div>
		<h2 class="text text-truncate">
			{{ title }}
		</h2>
	</div>
</template>

<script>
export default {
	props: {
		step: {
			type: [String, Number],
			default: 1,
		},
		title: {
			type: String,
			default: "Step",
		},
	},
	computed: {
		isMobile() {
			return this.$store.state.device === "mb";
		},
	},
	methods: {
		goPrev() {
			this.$emit("toPrev");
		},
	},
};
</script>

<style scoped lang="scss">
@import "~assets/css/half.scss";
[theme="10"] {
	.text {
		background-color: $bg-primary !important;
		&::before {
			display: none !important;
		}
	}
}

[theme="11"] {
	.step {
		background-color: $color-dark !important;
		border-top-left-radius:20px;
		span{
			display:inline-block;
		}
		@include respond-to(mb) {
			font-size: 16px;
			padding-left: 10px;
			color: #fff;
			height: 26px !important;
			 .v-btn{
				display:none !important;
				color: #fff !important;
				font-size: 16px !important;
			}
		}
	}
	.text {
		border-bottom-right-radius:20px !important;
		background-color: $color-dark !important;
		color: #fff !important;
		font-size: 24px;
		font-weight: 500;
		&::before {
			display: none !important;
		}
		@include respond-to(mb) {
			font-size: 16px !important;
			font-weight: 500;
		  line-height: 26px !important;
      height: 26px !important;
			padding-left: 6px !important;
		}
	}
}


.step-title {
	display: flex;
	align-items: center;
	margin: 15px 0;
}

.step {
	display: flex;
	justify-content: center;
	align-items: center;
	width: 36px;
	height: 36px;
	background: #333;
	border-radius: 4px;
	color: #fff;
	font-size: 24px;
	line-height: 0;
	z-index: 1;
	.v-btn {
		display: none;
	}
	span{
		display: none;
	}
}

.step-title .text {
	all: unset;
	flex: 1;
	line-height: 36px;
	height: 36px;
	margin-left: -4px;
	padding-left: 14px;
	background-color: $background-color;
	font-size: 18px;
	font-weight: 700;
}

@include respond-to(mb) {
	.step-title {
		margin: 10px 0;
	}
	.step {
		width: auto;
		height: auto;
		font-size: 18px;
		background-color: transparent;
		color: #333333;
		font-weight: 700;
		.v-btn {
			display: block;
			color: $color-primary;
		}
	}
	.text {
		line-height: 25px;
		height: 25px;
		font-size: 16px;
		font-weight: 400;
		background-color: transparent;
	}
}


</style>
