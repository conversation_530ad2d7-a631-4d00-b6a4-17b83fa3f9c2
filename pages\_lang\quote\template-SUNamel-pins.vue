<template>
	<div class="pinsQuoteWrap">
		<Preloader v-if="isLoading"></Preloader>
		<div class="containerWrap" v-else>
			<!-- <QuoteTitle :h1-text="lang.pins.h1" :prompts-text="lang.pins.prompts"></QuoteTitle> -->

			<div class="content">
				<div class="leftArea">
					<p class="leftArea_title">{{ lang.CustomSUNamelPins }}</p>
					<!-- 左上 <-> 右下 的消失动画 -->
					<transition enter-active-class="animate__animated animate__faster animate__fadeInBottomRight" leave-active-class="animate__animated animate__faster animate__fadeOutBottomRight">
						<SwiperDetail :imgList="carouselList" v-show="showRightArea" :selectedData="selectedData" :generalData="generalData" @toPosition="toPosition" :attachment="attachment" />
					</transition>
				</div>

				<div class="rightArea" id="rightAreaCustom">
					<div class="rightTop">
						<div class="size-item item" v-for="item in changePageList" :key="item.value" :class="{ active: item.value == 2 }" @click="linkTo(item)">
							<div class="circle2"></div>
							<div class="textWrap">
								<p class="normal-text">
									{{ item.name }}
								</p>
							</div>
						</div>
					</div>

					<template v-for="(item, index) in filterShowGeneralData">
						<!-- 步骤1  -->
						<div v-if="item.paramName == 'Select Shapes'" class="part select-shapes step-item" :id="item.paramName" :class="{ mask: item.paramName == maskName }">
							<h3 class="step-title">
								<span>{{ item.customIndex }}</span>
								{{ lang.Select }} {{ item.alias ? item.alias : item.paramName }}
							</h3>
							<div class="boxContent">
								<MyCheckBox v-for="itemChild in item.childList" :key="itemChild.id" :bindValue="itemChild" :bindName="item.paramName" :titleValue="'alias'" :imageValue="'imageJson'" :aspectRatio="'aspect-ratio: 142/107'" :likeModel="selectedData" @clickFun="selectQuoteParams(item, itemChild)"> </MyCheckBox>
							</div>
							<i v-if="item.paramName == maskName" class="el-icon-close" @click="closeMask"></i>
						</div>
						<!--   步骤2  -->
						<template v-if="item.paramName === 'Custom Shape Pins Size'">
							<div class="step-item step-size" :class="{ mask: maskName === item.paramName }" :id="item.paramName" :key="index">
								<h3 class="step-title">
									<span stepColor> {{ item.customIndex }}</span>
									{{ lang.Select }}
									{{ item.alias ? item.alias : item.paramName }}
								</h3>
								<div class="box-border">
									<i class="el-icon-close" @click="closeMask"></i>
								</div>
								<div class="step-box step-size-box">
									<div class="step-size-leftArea">
										<div class="size-area custom-shadow">
											<!-- <p class="step-size-title">
												{{ lang.pins.stepSizeTitle }}
											</p> -->
											<div class="size-item-wrap">
												<div
													class="size-item item"
													v-for="(citem, cindex) in item.childList"
													:key="citem.id"
													@click="selectQuoteParams(item, citem)"
													:class="{
														active: hasId(citem.id, selectedData[item.paramName]),
														onlyInquiry: citem.onlyAddInquiry === 1,
													}"
												>
													<div class="circle2"></div>
													<div class="textWrap">
														<p class="normal-text">
															{{ citem.alias ? citem.alias : citem.paramName }}
														</p>
														<span @click.stop v-if="citem.tips">
															<el-tooltip popper-class="cusToolTip" effect="light" :content="citem.tips" placement="top-start">
																<b class="icon-wenhao1 tip-icon"></b>
															</el-tooltip>
														</span>
													</div>
													<Corner v-if="citem.labelText" :backgroundColor="citem.labelColor" type2 position="absolute">
														{{ citem.labelText }}
													</Corner>
												</div>
											</div>
											<div class="d-flex-center confirmBtnWrap">
												<QuoteBtn
													@click.native="showMaskFn(item.paramName)"
													:style="{
														opacity: selectedData[item.paramName].length > 0 ? 1 : 0.5,
													}"
													:disabled="!selectedData[item.paramName].length > 0"
													>{{ lang.next }}
												</QuoteBtn>
											</div>
										</div>
										<div class="step-size-rightArea">
											<div class="textWrap">
												<p class="normal-text">{{ lang.pins.p1 }}</p>
											</div>
											<div class="shape-img">
												<img :src="shapeImg" alt="" />
											</div>
										</div>
									</div>
									<div class="step-size-rightArea mb-block" style="display: none">
										<div class="textWrap">
											<p class="normal-text">{{ lang.pins.p1 }}</p>
										</div>
										<div class="shape-img">
											<img :src="shapeImg" alt="" />
										</div>
										<div class="d-flex-center confirmBtnWrap">
											<QuoteBtn
												@click.native="showMaskFn(item.paramName)"
												:style="{
													opacity: selectedData[item.paramName].length > 0 ? 1 : 0.5,
												}"
												:disabled="!selectedData[item.paramName].length > 0"
												>{{ lang.next }}
											</QuoteBtn>
										</div>
									</div>
								</div>
							</div>
						</template>
						<!-- 步骤3  -->
						<template v-if="item.paramName === 'Upload Artwork & Comments'">
							<!-- <template v-if="item.paramName === 'Upload Your Design'"> -->
							<StepUpload class="step-item step-upload" :class="{ mask: maskName === item.paramName }" :id="item.paramName" :index="item.customIndex" :itemData="item" :uploadList.sync="uploadArtworkList" :remark.sync="remark" :isUpload.sync="isUpload" @closeMask="closeMask" @showMaskFn="showMaskFn" :key="index"></StepUpload>
						</template>

						<!-- 步骤 4  -->
						<template v-if="item.paramName === 'Select Metal Finish'">
							<div class="step-item step-metal" :class="{ mask: maskName === item.paramName, hideContent: $route.query.designPic }" :id="item.paramName" :key="index">
								<div class="box-border">
									<i class="el-icon-close" @click="closeMask"></i>
								</div>
								<h3 class="step-title">
									<span stepColor> {{ item.customIndex }} </span>
									{{ lang.Select }}
									{{ item.alias ? item.alias : item.paramName }}
									<span v-if="$route.query.designPic" style="color: #999999">: {{ $route.query.quotePlatingName }}</span>
								</h3>
								<div class="step-box step-metal-box">
									<div class="item-wrap" v-for="citem in item.childList" :key="citem.id" @click="selectQuoteParams(item, citem)" :class="{ active: hasId(citem.id, selectedData[item.paramName]) }">
										<div class="item">
											<div class="imgWrap">
												<img loading="lazy" :src="parseJSON(citem.imageJson)[0].url" :alt="parseJSON(citem.imageJson)[0].alt" style="aspect-ratio: 232/116" />
											</div>
											<Corner v-if="citem.labelText" :backgroundColor="citem.labelColor" type2 position="absolute">
												{{ citem.labelText }}
											</Corner>
											<div class="circle">
												<div class="inner-circle"></div>
											</div>
										</div>
										<div class="textWrap">
											<div class="circle2"></div>
											<div>
												<p class="normal-text">
													{{ citem.alias ? citem.alias : citem.paramName }}
												</p>
												<PriceText :paramData="citem"></PriceText>
											</div>
										</div>
									</div>
								</div>
							</div>
						</template>
						<!-- 步骤5 -->
						<div v-if="item.paramName == 'Back Side Options'" class="part more-options step-item" :id="item.paramName" :class="{ mask: item.paramName == maskName }">
							<h3 class="step-title">
								<span>{{ item.customIndex }}</span> {{ lang.Select }} {{ item.alias ? item.alias : item.paramName }}
							</h3>
							<div class="boxContent" ref="Patch Backing">
								<MyCheckBox v-for="itemChild in item.childList" :key="itemChild.id" :bindValue="itemChild" :bindName="item.paramName" :titleValue="'alias'" :imageValue="'imageJson'" showPrice tipsDown :aspectRatio="'aspect-ratio:293/210'" :likeModel="selectedData" @clickFun="selectQuoteParams(item, itemChild)"> </MyCheckBox>
							</div>
							<!-- <el-button class="viewMore"
									style="position: relative; left: 50%; transform: translateX(-50%)" type="text"
									@click="showMoreBtn($event, 'Patch Backing')"> {{ lang.ViewMore }}<i
										style="transform: rotate(90deg)" class="el-icon-d-arrow-right"></i></el-button> -->
							<i v-if="item.paramName == maskName" class="el-icon-close" @click="closeMask"></i>
						</div>
						<!-- 步骤6 -->
						<div v-if="item.paramName == 'Packaging Options'" class="part packaging-options step-item" :id="item.paramName" :class="{ mask: item.paramName == maskName }">
							<h3 class="step-title">
								<span>{{ item.customIndex }}</span
								>{{ lang.Select }} {{ item.alias ? item.alias : item.paramName }}
							</h3>
							<div class="boxContent">
								<MyCheckBox v-for="itemChild in item.childList" :key="itemChild.id" :bindValue="itemChild" :bindName="item.paramName" :titleValue="'alias'" :imageValue="'imageJson'" :aspectRatio="'aspect-ratio: 139/111'" :sizeValue="sizeValue" :likeModel="selectedData" @clickFun="selectQuoteParams(item, itemChild)" showPrice> </MyCheckBox>
							</div>
							<!-- <div class="text-center">
								<el-button class="myBtn" @click="showMaskFn(item.paramName)" :style="{
									opacity: selectedData[item.paramName].length > 0 ? 1 : 0.5,
								}" :disabled="!selectedData[item.paramName].length > 0">{{ lang.next }}
								</el-button>
							</div> -->
							<i v-if="item.paramName == maskName" class="el-icon-close" @click="closeMask"></i>
						</div>
						<!--  步骤7  -->
						<template v-if="item.paramName === 'Quantity'">
							<StepQty class="step-item step-qty" :class="{ mask: maskName === item.paramName }" :id="item.paramName" :index="item.customIndex" :itemData="item" :customQty.sync="customQty" :restaurants="restaurants" @closeMask="closeMask" @showMaskFn="showMaskFn" @calcPrice="debounceCalcPrice" @changeQty="changeQty" :key="index"></StepQty>
						</template>
						<!--  步骤8  -->
						<template v-if="item.paramName === 'Select Turnaround Time'">
							<StepTime class="step-item step-date" :class="{ mask: maskName === item.paramName }" :id="item.paramName" :cateData="cateData" :pid="pid" :customQty="customQty" :index="item.customIndex" :itemData="item" :priceInfo="priceInfo" :previewMode="previewMode" :selectedParams="selectedData" @showMaskFn="showMaskFn" @selectItem="selectQuoteParams($event.item, $event.citem)" @closeMask="closeMask" :key="index"></StepTime>
						</template>
					</template>
				</div>
			</div>
			<div class="footer" id="foot">
				<Detail class="type2" :key="3" :absoluteImg="comeFromDZ.absoluteImg" :selectedData="selectedData" :priceInfo="priceInfo" :customQty="customQty" :textInfo="textInfo" :generalData="generalData" :attachment="attachment" @toPosition="toPosition" @addInquiry="debounceAddInquiry" @addCart="addCart"></Detail>
			</div>
			<el-drawer :visible.sync="showArea" :with-header="true" custom-class="drawDialog" :size="device === 'ipad' ? '728px' : '85%'">
				<Detail class="type3" :key="2" :absoluteImg="comeFromDZ.absoluteImg" :selectedData="selectedData" :priceInfo="priceInfo" :customQty="customQty" :textInfo="textInfo" :generalData="generalData" :attachment="attachment" @toPosition="toPosition" @addInquiry="debounceAddInquiry" @addCart="addCart"></Detail>
			</el-drawer>
			<!-- 建议弹窗-->
			<RecomendDialog :commentsDialog.sync="commentsDialog" :paramValue="selectedParamsValue" @next="recomendNext"></RecomendDialog>
			<!-- 预览弹窗-->
			<VideoPreviewDialog :pic-dialog.sync="picDialog" :zoom-pic="zoomPic" :temp-type="tempType" :selected-params-value="selectedParamsValue" @qtyAndBtnConfirm="qtyAndBtnConfirm" @qtyAndBtnNext="qtyAndBtnNext" @dialogNextStep="dialogNextStep"></VideoPreviewDialog>
			<!-- 升级数量弹窗-->
			<BaseDialog v-model="upgradeDialog" :width="device != 'mb' ? '640px' : '320px'">
				<Upgrade @closeUpgrade="upgradeDialog = false" :tipArr="tipArr" :typeName="cateData.cateName" @upgradeLevel="upgradeLevel"></Upgrade>
			</BaseDialog>
			<!-- o2o预览页面弹窗-->
			<BaseDialog v-model="showOtoDialog" :width="device != 'mb' ? '570px' : '90%'">
				<div class="otoWrap">
					<img src="@/assets/images/oto.png" alt="" />
					<p>{{ lang.p1 }}</p>
					<h3>{{ lang.p2 }}</h3>
					<p style="color: #666666">
						{{ lang.p3 }}<br />
						{{ lang.p4 }}.
					</p>

					<div class="box">
						<p class="t1">
							{{ lang.p5 }} <br />
							{{ lang.p6 }}!
						</p>
						<a href="https://www.o2o.co/previewWeb" style="text-decoration: none">
							<el-button type="primary">{{ lang.p7 }}</el-button>
						</a>
					</div>
				</div>
			</BaseDialog>
			<!--			询盘弹窗-->
			<infoDialog :infoDialogVisible.sync="infoDialogVisible" :uploadList.sync="uploadList" :otherUpload="uploadArtworkList" :recomendUpload="recomendUpload" @getValue="getValueFun"></infoDialog>
			<!--    遮罩-->
			<myMask :maskName.sync="maskName"></myMask>
			<!--  手机，ipad端预览-->
			<PreviewBtn :showArea.sync="showArea"></PreviewBtn>
			<BaseDialog v-model="noFileDialog" :width="device != 'mb' ? '485px' : '90%'" :model="false">
				<template #closeIcon>
					<div style="display: none"></div>
				</template>
				<infoUpload :infoUploadList="infoUploadList" @pushInfoList="pushInfoList" @delInfoList="delInfoList" @updateInquiry="updateInquiry" @closeInfoDialog="closeInfoDialog"> </infoUpload>
			</BaseDialog>
		</div>
	</div>
</template>

<script>
import "@/plugins/element";
import MyCheckBox from "@/components/Medals/MyCheckBox";
import VideoPlayer from "@/components/Quote/VideoPlayer.vue";
import QuoteBtn from "@/components/Quote/QuoteBtn";
import myMask from "@/components/Quote/Mask";
import Detail from "@/components/Quote/Detail";
import BaseDialog from "@/components/Quote/BaseDialog";
import Upgrade from "@/components/Quote/Upgrade";
import Preloader from "@/components/Quote/Preloader";
import PriceText from "@/components/Quote/PriceText";
import Star from "@/components/Quote/Star";
import StepUpload from "@/components/Quote/StepUpload";
import StepTime from "@/components/Quote/StepTime";
import StepQty from "@/components/Quote/StepQty";
import Corner from "@/components/Medals/Corner";
import Pimg from "@/components/Medals/Pimg";
import QtyAndBtn from "@/components/Medals/QtyAndBtn";
import DialogBM from "@/components/Medals/DialogBM";
import infoDialog from "@/components/Medals/infoDialog";
import RecomendDialog from "@/components/Quote/RecomendDialog.vue";
import quoteMixin from "@/mixins/quote";
import quoteBanChoiceMixin from "@/mixins/quoteBanChoice";
import QuoteTitle from "@/components/Quote/QuoteTitle.vue";
import PreviewBtn from "@/components/Quote/PreviewBtn.vue";
import CustomCircle from "@/components/Quote/customCircle.vue";
import { isImageType } from "@/utils/utils";
import SwiperDetail from "@/components/Cufflinks/SwiperDetail.vue";
import VideoPreviewDialog from "@/components/Quote/VideoPreviewDialog.vue";
export default {
	head() {
		return {
			title: "Quote page",
		};
	},
	components: {
		VideoPreviewDialog,
		CustomCircle,
		PreviewBtn,
		QuoteTitle,
		DialogBM,
		QuoteBtn,
		Detail,
		BaseDialog,
		Upgrade,
		myMask,
		VideoPlayer,
		Preloader,
		PriceText,
		Star,
		StepUpload,
		StepTime,
		StepQty,
		Corner,
		Pimg,
		QtyAndBtn,
		infoDialog,
		RecomendDialog,
		SwiperDetail,
		MyCheckBox,
	},
	mixins: [quoteMixin, quoteBanChoiceMixin],
	name: "template-SUNamel-pins",
	data() {
		return {
			pid: 240,
			productsName: "Custom Shape SUNamel Pins",
			restaurants: [
				{ value: "50", address: "50" },
				{ value: "100", address: "100" },
				{ value: "200", address: "200" },
				{ value: "300", address: "300" },
				{ value: "500", address: "500" },
				{ value: "1000", address: "1000" },
				{ value: "3000", address: "3000" },
				{ value: "5000", address: "5000" },
				{ value: "10000", address: "10000" },
			],
			// cusTop: 0,
			changePageList: [
				{ name: "Custom Shape", value: 1, url: "/quote/custom-SUNamel-pins" },
				{ name: "Template Shape", value: 2, url: "/quote/template-SUNamel-pins" },
			],
		};
	},
	methods: {
		isImageType,
		// getTop() {
		// 	let el = document.getElementById("detailInfo");
		// 	if (!el) {
		// 		return false;
		// 	}
		// 	const { top, height } = el.getBoundingClientRect();
		// 	this.cusTop = (window.innerHeight - height) / 2 - 100;
		// 	// this.cusTop = (window.innerHeight - height) / 2 + 113 / 2 - 20;
		// },
		linkTo(val) {
			this.$router.push({
				path: val.url,
			});
		},
	},
	watch: {
		// isLoading(newVal){
		// 	}
	},
	mounted() {},
};
</script>

<style scoped lang="scss">
.tips.type2 {
	top: 0;
	transform: translateY(0);
	right: auto;
	left: 0;
}

.pinsQuoteWrap ::v-deep {
	font-family: Calibri, Arial, serif;
	@media screen and (max-width: 767px) {
		input,
		textarea {
			font-size: 16px !important;
		}
	}

	.viewMore {
		@media screen and (min-width: 768px) {
			display: none;
		}
	}

	.step-title {
		background-color: #f9f9f9;
		> span {
			// color: $color-primary;
			font-size: 24px;
			width: 36px;
			height: 36px;
			line-height: 36px;
			margin-right: 10px;
			text-align: center;
			display: inline-block;
			background: #333;
			color: #fff !important;
			border-radius: 4px;
		}
	}

	ul {
		margin: 0;
	}

	img {
		border: none;
		vertical-align: middle;
		max-width: 100%;
		max-height: 100%;
	}

	.custom-shadow {
		position: relative;
		background-color: #fff;
	}

	.custom-shadow:after,
	.custom-shadow:before {
		content: "";
		position: absolute;
		z-index: -1;
		bottom: 12px;
		left: 5px;
		width: 50%;
		height: 20%;
		box-shadow: 0 14px 7px #d9dbdd;
		transform: rotate(-3deg);
	}

	.custom-shadow:after {
		right: 5px;
		left: auto;
		transform: rotate(3deg);
	}

	.video-js {
		overflow: hidden;
		border-radius: 6px 6px 0 0;
	}

	.containerWrap {
		background-color: #fff;
		font-size: 18px;
		padding-top: 20px;
		.content {
			// padding: 20px 11.5vw 0;
			padding: 3.8em max(calc(50% - 700px), 1.5vw) 4.2em;
		}
		.isComment {
			width: 100%;
			height: 100%;
			border: 1px solid #ccc;
			padding: 10px;
			border-radius: 4px;
			box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
			text-align: center;

			.textWrap {
				display: flex;
				align-items: center;
				justify-content: flex-start;
				margin-top: 0 !important;
			}

			.circle2 {
				border-color: $color-primary;
				background-color: $color-primary;

				&::after {
					background-color: #ffffff;
				}
			}

			.el-textarea {
				flex: 1;
				margin: 10px 0;

				textarea {
					height: 100%;
					background-color: #f5f5f5 !important;
				}
			}

			button {
				background-color: $color-primary;
				color: #fff;
				padding: 4px;
				width: 100px;
				outline: none;
				border: none;
				border-radius: 4px;
				font-size: 14px;
				text-align: center;
			}
		}

		.content {
			position: relative;
			display: grid;
			grid-template-columns: repeat(48, 1fr);
			// grid-template-columns: 1fr 1fr;
			margin: 20px 0 0;
			.leftArea {
				grid-column: 2/24;
				// .pic_vIew{
				// 	position:sticky;
				// 	&>p{
				// 		font-size: 36px;
				// 		text-align: center;
				// 	}
				// }
				.leftArea_title {
					font-size: 36px;
					text-align: center;
				}
			}

			.rightArea {
				grid-column: 25/49;
				//padding-left: 25px;
				// margin-right: 10px;
				// grid-column: 20/48;

				// padding-right: 30px;
				.rightTop {
					padding: 0 20px 10px;
					display: grid;
					grid-template-columns: 1fr 1fr;
					column-gap: 7%;
					.size-item {
						align-items: center;
						background: #f4f5f5;
						border: 1px solid transparent;
						border-radius: 10px;
						cursor: pointer;
						display: flex;
						height: 40px;
						padding: 0 4px 0 20px;
						transition: all 0.3s;
					}
					.size-item.active {
						border-color: $color-primary;
						.circle2 {
							border-color: $color-primary;
							background: $color-primary;
							&::after {
								background-color: #fff;
							}
						}
					}
				}
				.mask {
					z-index: 101;
					background-color: #fff;
				}
			}
		}

		.picWrap {
			display: flex;
			justify-content: center;
			align-items: center;
			flex-direction: column;

			img {
				width: 100%;
				height: 100%;
				object-fit: contain;
			}
		}

		.footer {
			display: grid;
			grid-template-columns: 700px;
			justify-content: center;
			padding: 20px;
			background: #eef2f5;
		}

		.small-title {
			position: relative;
			margin-bottom: 16px;
			font-size: 16px;
			color: #666666;
		}

		.d-flex-center {
			display: flex;
			justify-content: center;
			align-items: center;
		}

		.circle2 {
			display: flex;
			justify-content: center;
			align-items: center;
			width: 20px;
			height: 20px;
			min-width: 20px;
			border: 1px solid #afb1b3;
			border-radius: 50%;
			margin-right: 8px;
			background-color: #fff;
			transition: all 0.3s;

			&::after {
				content: "";
				width: 6px;
				height: 6px;
				min-width: 6px;
				background: #d4d7d9;
				border-radius: 50%;
			}
		}

		.circle {
			position: absolute;
			left: 50%;
			bottom: 0;
			transform: translate(-50%, 100%);
			width: 28px;
			height: 15px;
			border: 1px solid #e9ecf0;
			border-top: none;
			background: #edf1f5;
			border-radius: 0 0 16px 16px;
			z-index: 10;
			transition: all 0.3s;

			.inner-circle {
				position: absolute;
				left: 50%;
				top: 0;
				transform: translate(-50%, -50%);
				width: 18px;
				height: 18px;
				border-radius: 50%;
				background: #ffffff;
				border: 1px solid #aaaeb3;
				transition: all 0.3s;

				&:after {
					content: "";
					position: absolute;
					left: 50%;
					top: 50%;
					transform: translate(-50%, -50%);
					width: 6px;
					height: 6px;
					background-color: #aaaeb3;
					border-radius: 50%;
					transition: all 0.3s;
				}
			}
		}

		.circle3 {
			display: flex;
			justify-content: center;
			align-items: center;
			width: 18px;
			height: 18px;
			min-width: 18px;
			border: 1px solid #dae0e5;
			background-color: white;
			margin-right: 6.5px;
			transition: all 0.3s;
			position: relative;

			&::after {
				position: absolute;
				content: "";
				left: 50%;
				top: 50%;
				transform: translate(-50%, -50%);
				height: 6px;
				width: 6px;
				border-radius: 50%;
				background-color: #dae0e5;
			}

			/* .checkIcon {
        font-size: 16px;
        color: #ffffff;
      } */
		}

		.confirmBtnWrap {
			margin-top: 33px;
		}

		.drawDialog {
			.el-drawer__header {
				margin-bottom: 0;
				padding: 10px;
			}
		}

		@media screen and (max-width: 1500px) {
			.containerWrap {
				padding-top: 0;
			}
			.content {
				.leftArea {
					// display: none;
					grid-column: 2/48;
				}

				.rightArea {
					// display: none;
					grid-column: 2/48;
					padding-left: 0;
					// grid-column-start: span 2;
					.rightTop {
						margin-top: 30px;
					}
				}

				.rightArea.rightFixedArea {
					overflow: hidden auto;
					display: block;
					margin: 0;

					.detailList {
						position: relative;
						top: 0 !important;
						box-shadow: none;
					}
				}
			}
		}

		@media screen and (max-width: 767px) {
			background-color: #ebebeb;
			padding-top: 15px;
			// .containerWrap{
			// 	background-color:#ebebeb !important;
			// }
			.content {
				padding: 0;
				margin: 0;
				.leftArea {
					// .pic_vIew{
					// 	.title{
					// 		font-size: 28px;
					// 		margin-bottom: 10px;
					// 	}
					// }
					.leftArea_title {
						font-size: 28px;
						margin-bottom: 10px;
					}
					.myswiper2 {
						height: initial;
					}
					.myswiper1 {
						display: none;
					}
				}
				.rightArea {
					padding-left: 0;
					.rightTop {
						padding: 0;
						margin: 15px 0 !important;
						.size-item {
							justify-content: center;
							padding: 0;
						}
					}
				}
			}

			.footer {
				grid-template-columns: 1fr;
				padding: 10px;
				background-color: #e0e0e0;
			}

			.small-title {
				font-size: 12px;
				margin-bottom: 15px;
			}

			.circle {
				width: 20px;
				height: 10px;

				.inner-circle {
					width: 14px;
					height: 14px;

					&:after {
						content: "";
						width: 5px;
						height: 5px;
					}
				}
			}

			.circle2,
			.customCircle1 {
				width: 12px;
				height: 12px;
				min-width: 12px;
				margin-right: 5px;
				line-height: 12px;

				&::after {
					width: 4px;
					height: 4px;
					min-width: 4px;
				}
			}

			.confirmBtnWrap {
				margin-top: 20px;
			}
		}
	}

	.step-item {
		position: relative;
		// margin-bottom: 16px;
		background-color: #fff;
		// padding: 40px 30px;
		padding: 20px;
		border-radius: 10px;

		&.hideContent {
			.step-box {
				display: none;
			}

			.step-title {
				margin-bottom: 0;
			}
		}

		.box-border {
			display: none;

			.el-icon-close {
				display: flex;
				justify-content: center;
				align-items: center;
				position: absolute;
				font-weight: 700;
				top: 0;
				right: 0;
				width: 40px;
				height: 40px;
				transform: translate(50%, -50%);
				cursor: pointer;
				background: #ffffff;
				border-radius: 50%;
				box-shadow: 1px 2px 20px 0 rgba(0, 0, 0, 0.2);
				z-index: 10;

				// 下一步右上角的关闭按钮 不需要了
				// display: none;
			}
		}

		&.mask {
			position: relative;
			z-index: 101;

			.confirmBtnWrap {
				position: relative;
			}

			// 下一步的
			.box-border {
				position: absolute;
				left: 0;
				right: 0;
				top: 0;
				bottom: 0;
				display: block;
				background-color: #fff;
				border: 1px solid #d9dbdd;
			}

			.step-title {
				position: relative;
				z-index: 9;
			}

			.step-box {
				position: relative;
			}
		}

		.step-title {
			font-size: 18px !important;
			font-weight: 700;
			color: #333333;
			margin-bottom: 20px;

			.step-title-icon {
				width: 21px;
				margin-left: 4px;
				cursor: pointer;
				vertical-align: middle;
			}
		}

		.step-title.title5 {
			margin-bottom: 4px;
		}

		@media screen and (min-width: 768px) and (max-width: 1499px) {
			padding: 30px 10px;

			.small-title {
				margin-bottom: 15px;
			}
		}

		@media screen and (max-width: 767px) {
			margin-bottom: 10px;
			background-color: #fff;
			border-radius: 5px;
			padding: 20px 7px;
			&.mask {
				.box-border {
					.el-icon-close {
						width: 30px;
						height: 30px;
						transform: translate(0, 0);
						box-shadow: none;
					}
				}
			}
			.step-title {
				margin-bottom: 10px;
				// font-size: 14px;
				font-weight: 400;
				color: #333;

				.step-title-icon {
					width: 17px;
					margin-left: 5px;
				}
			}
		}
	}

	.step-title {
		margin-bottom: 30px;
	}

	.step-item .step-box {
		display: grid;
		justify-content: space-between;
		column-gap: 25px;
		row-gap: 23px;

		.item-wrap {
			position: relative;
			border: 2px solid #ebebeb;
			border-radius: 10px;
			// overflow: hidden;
			cursor: pointer;
			p.normal-text,
			p.t1 {
				transition: all 0.3s;
			}

			@media (any-hover: hover) {
				&:hover {
					border-color: $color-primary;
					p.normal-text {
						// color: $color-primary;

						span {
							color: #333333;
						}
					}

					p.t1 {
						color: $color-primary;
					}

					.circle2 {
						border-color: $color-primary;
						background: $color-primary;

						&::after {
							background-color: #fff;
						}
					}

					.circle3 {
						border-color: $color-primary;
						background-color: $color-primary;

						&::after {
							background: white;
						}
					}
					.customCircle1 {
						border-color: $color-primary !important;
						background-color: $color-primary !important;
						&::after {
							background: white !important;
						}
					}
					.zoomIcon {
						color: $color-primary !important;
					}
				}
			}

			.item {
				position: relative;
				border-radius: 6px;
				display: flex;
				flex-direction: column;
				justify-content: space-between;
				align-items: center;
				cursor: pointer;
				transition: all 0.3s;
				// border: 1px solid transparent;

				.imgWrap {
					// display: flex;
					// justify-content: center;
					// align-items: center;
					height: 100%;
					width: 100%;

					img {
						object-fit: contain;
						border-radius: 6px 6px 0 0;
					}
				}

				@media (any-hover: hover) {
					&:hover {
						border-color: $color-primary;
						// box-shadow: 0 3px 4px 0 #cccccc;

						.circle {
							border-color: $color-primary;
						}
					}
				}
			}

			.item.linearBorder {
				background-image: linear-gradient(0deg, #edf1f5 0%, #f7f9fa 100%), linear-gradient(0deg, #e9ecf0, #f7f9fa);
				background-origin: border-box;
				background-clip: content-box, border-box;
				@media (any-hover: hover) {
					&:hover {
						background-image: linear-gradient(0deg, #edf1f5 0%, #f7f9fa 100%), linear-gradient(0deg, $color-primary, #f7f9fa);
						box-shadow: 0 3px 4px 0 #cccccc;
					}
				}
			}

			.textWrap {
				.normal-text {
					font-size: 16px;
					color: #333333;
					transition: all 0.3s;
					text-align: left;
				}

				.tip-text {
					color: #de3500;
					font-size: 16px;
				}
			}

			&.active {
				border-color: $color-primary;
				.circle3 {
					border-color: $color-primary;
					background-color: $color-primary;

					.checkIcon {
						color: #ffffff;
					}
				}

				.item {
					.circle {
						border-color: $color-primary;

						.inner-circle {
							border-color: $color-primary;

							&::after {
								background-color: $color-primary;
							}
						}
					}
				}

				.item.linearBorder {
					background-image: linear-gradient(0deg, #edf1f5 0%, #f7f9fa 100%), linear-gradient(0deg, $color-primary, #f7f9fa);
					box-shadow: 0 3px 4px 0 #cccccc;
				}

				.textWrap {
					.circle2 {
						border-color: $color-primary;
						background: $color-primary;

						&::after {
							background-color: #fff;
						}
					}
				}
			}

			& .textWrap {
				display: flex;
				justify-content: center;
				align-items: center;
				margin-top: 0;
				// flex-direction: column;
				// background: #f4f5f5;
				padding: 5px 0;
			}
		}
		@media screen and (min-width: 768px) and (max-width: 1499px) {
			column-gap: 12px;
			row-gap: 23px;
		}

		@media screen and (max-width: 767px) {
			column-gap: 5px;
			row-gap: 10px;

			.item-wrap {
				border-width: 1px !important;
				border-radius: 5px;
				// border-color: $color-primary;
				.item {
					@media (any-hover: hover) {
						&:hover {
							background-image: none;
							box-shadow: none;
							border-color: #e6e6e6;

							.circle {
								border-color: transparent;
							}
						}
					}
				}

				.textWrap {
					padding-bottom: 0;

					.normal-text {
						font-size: 12px;
						text-align: center;
					}

					.tip-text {
						font-size: 12px;
					}
				}

				&.active {
					.item {
						// border-color: $color-primary;
						background-image: none;
						box-shadow: none;

						.circle {
							border-color: $color-primary;

							.inner-circle {
								border-color: $color-primary;

								&::after {
									background-color: $color-primary;
								}
							}
						}
					}
				}

				& > .textWrap {
					display: flex;
				}
			}
		}
	}

	@media screen and (min-width: 768px) and (max-width: 1499px) {
		.step-metal {
			.step-box {
				column-gap: 10px;
				row-gap: 25px;

				.item-wrap {
					.item {
						height: 100px;
					}

					.textWrap {
						margin-top: 12px;
					}
				}
			}
		}
	}

	@media screen and (max-width: 767px) {
		// .step-metal {
		// 	.step-box {
		// 		grid-template-columns: repeat(3, 1fr);
		// 		column-gap: 4px;
		// 		row-gap: 10px;
		// 		height: auto;

		// 		.item-wrap {
		// 			.item {
		// 				// height: 67px;

		// 				&::after {
		// 					height: 20px;
		// 				}

		// 				.imgWrap img {
		// 					border-radius: 5px;
		// 				}
		// 			}

		// 			.circle {
		// 				display: block;
		// 			}

		// 			.textWrap {
		// 				margin-top: 15px;
		// 				text-align: center;
		// 			}

		// 			// .circle2 {
		// 			// 	display: none;
		// 			// }
		// 		}
		// 	}
		// }

		.step-title {
			margin-bottom: 15px;
		}
	}

	.otoWrap {
		font-size: 16px;
		text-align: center;
		padding: 27px;

		h3 {
			font-size: 36px;
			font-weight: 700;
			margin-top: 10px;
			margin-bottom: 15px;
			line-height: normal;
		}

		.box {
			padding: 27px;
			background: #eff2f6;
			border-radius: 10px;
			margin-top: 33px;

			.t1 {
				font-size: 18px;
				font-weight: 700;
			}

			button {
				margin-top: 20px;
				width: 266px;
				height: 45px;
				background-color: #1a73e8;
				border-color: #1a73e8;
				font-size: 16px;
				@media (any-hover: hover) {
					&:hover {
						opacity: 0.8;
					}
				}
			}
		}
	}

	.rounded-circle {
		border-radius: 50%;
	}
	.step-back {
		.step-box {
			grid-template-columns: repeat(4, 1fr);
			gap: 20px 12px;
			.item-wrap {
				.textWrap {
					padding: 5px 12px;
				}
			}
			@media screen and (max-width: 767px) {
				grid-template-columns: repeat(3, 1fr);
				gap: 11px 6px;
				.item-wrap {
					.textWrap {
						padding: 5px 12px;
					}
				}
			}
		}
	}
	.step-size {
		.step-size-box {
			.step-size-leftArea {
				display: flex;
				column-gap: 50px;
				.size-item-wrap {
					width: 220px;
				}
				.size-item {
					display: flex;
					column-gap: 8px;
					align-items: center;
					padding: 10px 20px;
					background: #f6f6f6;
					border-radius: 10px;
					border: 2px solid #ebebeb;
					margin-bottom: 16px;
					cursor: pointer;
					.normal-text {
						font-size: 16px;
					}
					.textWrap {
						display: flex;
						align-items: center;
						column-gap: 6px;
					}
				}
				.size-item:hover {
					border-color: $color-primary;
					.circle2 {
						background-color: $color-primary;
						border-color: $color-primary;
						&::after {
							background-color: #fff;
						}
					}
				}
				.size-item.active {
					border-color: $color-primary;
					.circle2 {
						background-color: $color-primary;
						border-color: $color-primary;
						&::after {
							background-color: #fff;
						}
					}
				}
				.confirmBtnWrap {
					margin-top: 54px;
				}
			}
			.step-size-rightArea {
				.normal-text {
					font-size: 18px;
					margin-bottom: 17px;
				}
			}
		}
		@media screen and (max-width: 767px) {
			.step-size-box {
				.step-size-leftArea {
					display: block;
					.size-item-wrap {
						width: 100%;
					}
					.size-item-wrap {
						display: grid;
						grid-template-columns: 1fr 1fr;
						gap: 8px;
					}
					.size-item {
						column-gap: 0px;
						padding: 12px;
						border-radius: 5px;
						border: 1px solid #ebebeb;
						margin-bottom: 0px;
						cursor: pointer;
						.normal-text {
							font-size: 12px;
						}
						.textWrap {
							b {
								font-size: 15px;
							}
						}
					}
					.confirmBtnWrap {
						display: none;
					}
					.step-size-rightArea {
						display: none;
					}
				}
				.mb-block {
					display: block !important;
					.normal-text {
						text-align: center;
						font-size: 12px;
						margin-bottom: 8px;
						margin-top: 10px;
					}
					.shape-img {
						padding: 0 13.65vw;
					}
				}
			}
		}
	}
	.step-upload {
		margin-bottom: 0;
		.confirmBtnWrap {
			display: flex;
			justify-content: center;
		}
		.step-box {
			.t1,
			.later {
				font-size: 16px;
				font-weight: bold;
			}
			.t2 {
				margin-bottom: 7px;
				color: #333;
			}
			.upload-box {
				height: 150px;
				margin-top: 7px;
				padding-top: 7px;
				margin-left: 28px;
				justify-content: flex-start;
				column-gap: 14px;
				.uploadList {
					height: 55px;
					margin-bottom: 2px;
				}
				.uploadIcon {
					margin-top: 0 !important;
					line-height: 53px !important;
					font-size: 32px !important;
					color: #9e9e9e !important;
				}
				.upload-btn {
					button {
						width: 138px;
						margin-bottom: 5px;
						outline: none;
					}
					button:focus {
						outline: none;
					}
				}
				// .tips {
				// 	color: #333;
				// }
			}
			.uploadArea {
				.t1 {
					margin-bottom: 2px;
				}
				.uploadIcon,
				.uploadIcon1 {
					display: inline-block;
					margin-right: 4px;
					color: #9e9e9e;
				}
			}
			.pcInput {
				height: 226px !important;
				border-radius: 10px;
			}
			.el-textarea__inner {
				resize: none;
			}
			@media screen and (max-width: 767px) {
				gap: 0px;
				.text-background {
					background: #faece5;
				}
				.t1,
				.later {
					margin-bottom: 0 !important;
					font-size: 12px;
					padding: 6px 11px 4px;
				}
				.t2 {
					display: block;
					margin-bottom: 6px;
					padding-left: 28px;
					padding-bottom: 9px;
				}
				.upload-box {
					height: 123px;
					margin-bottom: 7px;
				}
				.editArea {
					margin-top: 0;
					padding: 0 11px;
					.t1 {
						padding-left: 0;
					}
					textarea {
						padding: 4px 10px;
					}
					textarea::placeholder {
						font-size: 12px;
					}
				}
			}
		}
		@media screen and (max-width: 767px) {
			margin-bottom: 10px;
		}
	}
	.select-shapes,
	.more-options,
	.packaging-options {
		position: relative;
		padding: 20px;
		.el-icon-close {
			display: flex;
			justify-content: center;
			align-items: center;
			position: absolute;
			font-weight: 700;
			top: 0;
			right: 0;
			width: 40px;
			height: 40px;
			transform: translate(50%, -50%);
			cursor: pointer;
			background: #ffffff;
			border-radius: 50%;
			box-shadow: 1px 2px 20px 0 rgba(0, 0, 0, 0.2);
			z-index: 10;

			// 下一步右上角的关闭按钮 不需要了
			// display: none;
		}
		h3.step-title {
			margin-bottom: 20px;
			font-size: 18px;
		}
		.boxContent {
			display: grid;
			grid-template-columns: repeat(4, 1fr);
			gap: 20px 11px;
			.StepBox {
				border: 2px solid #ebebeb;
				border-radius: 10px;
				.product-info {
					margin-top: 0;
					padding: 4px 5px;
					.title {
						font-size: 16px;
					}
					.radio-beauty {
						width: 20px;
						height: 20px;
						margin-left: 0;
						margin-right: 8px;
					}
				}
			}
			.StepBox:hover {
				border-color: $color-primary;
				.radio-beauty {
					background-color: $color-primary !important;
					border-color: $color-primary !important;
					&::after {
						background-color: #fff !important;
					}
				}
			}
			.active {
				border-color: $color-primary;
				.product-info {
					.title {
						color: #333;
					}
				}
			}
		}
		@media screen and (max-width: 767px) {
			padding: 20px 7px;
			&.mask {
				.el-icon-close {
					width: 30px;
					height: 30px;
					transform: translate(0, 0);
					box-shadow: none;
				}
			}
			h3.step-title {
				margin-bottom: 10px;
			}
			.boxContent {
				grid-template-columns: repeat(3, 1fr);
				gap: 11px 6px;
				.StepBox {
					border: 1px solid #ebebeb;
					border-radius: 5px;
					.product-info {
						margin-top: 0;
						padding: 4px 5px;
						.title {
							font-size: 12px;
						}
						.radio-beauty {
							min-width: 12px;
							width: 12px;
							height: 12px;
							margin-right: 5px;
							&::after {
								width: 4px;
								height: 4px;
							}
						}
					}
				}
			}
		}
	}
	.more-options {
		.boxContent {
			grid-template-columns: repeat(2, 1fr);
			gap: 20px 14px;
			.StepBox {
				.se {
					align-items: flex-start;
					padding-bottom: 7px;
				}
				.product-info {
					padding: 12px 20px 3px;
				}
				.tip-text,
				.normal-text {
					padding-left: 48px;
					font-size: 14px;
				}
			}
		}
		@media screen and (max-width: 767px) {
			.boxContent {
				gap: 10px 8px;
				.StepBox {
					.se {
						padding-bottom: 3px;
					}
					border-radius: 10px;
					.product-info {
						padding: 6px 12px 0;
					}
					.tip-text,
					.normal-text {
						margin-top: 2px;
						padding-left: 29px;
						font-size: 12px;
					}
				}
			}
		}
	}
	.packaging-options {
		.boxContent {
			gap: 20px 15px;
			.StepBox {
				.se {
					align-items: flex-start;
					padding-bottom: 12px;
				}
				.product-info {
					padding: 12px 12px 0;
				}
				.tip-text,
				.normal-text {
					padding-left: 40px;
					font-size: 14px;
				}
			}
		}
		@media screen and (max-width: 767px) {
			.boxContent {
				gap: 6px;
				grid-template-columns: repeat(2, 1fr);
				.StepBox {
					.se {
						padding-bottom: 3px;
					}
					border-radius: 10px;
					.product-info {
						padding: 6px 12px 0;
					}
					.tip-text,
					.normal-text {
						margin-top: 2px;
						padding-left: 29px;
						font-size: 12px;
					}
				}
			}
		}
	}
	.step-metal {
		.step-box {
			grid-template-columns: repeat(3, 1fr);
			column-gap: 14px;
			row-gap: 20px;
			.item-wrap {
				.item {
					position: relative;
					display: flex;
					justify-content: center;
					align-items: center;
					// height: 118px;
					border: none;
					.imgWrap {
						img {
							object-fit: contain;
						}
					}
				}

				.circle {
					display: none;
				}

				.textWrap {
					padding: 7px 20px 16px;
					justify-content: start;
					margin-top: 5px;
					div:last-child {
						display: flex;
						column-gap: 8px;
					}
				}
			}
			@media screen and (max-width: 767px) {
				gap: 11px 6px;
				.item-wrap {
					.textWrap {
						margin-top: 0;
						padding: 7px 11px 11px;
						div:last-child {
							column-gap: 5px;
						}
					}
				}
			}
		}
	}
	.step-qty {
		margin-bottom: 0;
		.step-qty-box {
			.newInputNumber {
				margin-right: 20px;
			}
		}
		@media screen and (max-width: 767px) {
			margin-bottom: 10px;
			.step-qty-box {
				.newInputNumber {
					margin-right: 10px;
					.el-input input {
						&::placeholder {
							font-size: 12px;
							font-weight: 400;
						}
					}
				}
			}
		}
	}
	.step-date {
		margin-bottom: 0;
		.step-box {
			margin-bottom: 0;
			column-gap: 28px !important;
			grid-template-columns: repeat(3, 1fr);
			.item-wrap {
				padding: 13px 21px 11px 12px;
				border-color: transparent;
				.customCircle1 {
					margin-right: 8px;
				}
				.top {
					font-size: 16px;
				}
				.bottom {
					margin-top: 2px;
					font-size: 14px;
				}
			}
			@media screen and (max-width: 767px) {
				column-gap: 0px !important;
				grid-template-columns: repeat(1, 1fr);
				.item-wrap {
					padding: 5px 11px 8px;
					min-height: 65px;
					.customCircle1 {
						margin-right: 6px;
					}
					.top {
						span {
							font-weight: 400;
						}
					}
					.bottom {
						padding: 0 35px 0 18px;
					}
				}
			}
		}
	}
}
//
// 隐藏组件里的step文字
::v-deep {
	.step-upload,
	.step-qty,
	.step-date {
		.step-title {
			> span:first-of-type,
			> span:last-of-type {
				display: none;
			}
		}
	}
}
//

.myswiper1 {
	.swiper-wrapper {
		justify-content: center;
		margin-top: 10px;
		margin-left: 10px;
	}
	.swiper-slide {
		display: flex;
		justify-content: center;
		align-items: center;
		overflow: hidden;
		width: 88px;
		height: 88px;
		border: 2px solid #eeeeee;
		border-radius: 10px;
		cursor: pointer;

		&.swiper-slide-thumb-active {
			border: 2px solid $color-primary !important;
		}
	}

	&.isEdit {
		.swiper-slide.swiper-slide-thumb-active {
			border: 2px solid #eeeeee;
		}
	}

	img {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}

	.playBtn {
		display: flex;
		justify-content: center;
		align-items: center;
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		width: 30px;
		height: 30px;
		background-color: rgba(0, 0, 0, 0.7);
		border-radius: 50%;

		svg {
			fill: #ffffff;
		}
	}
}

.swiper.myswiper2 {
	--swiper-navigation-color: #ffffff;
	/* 单独设置按钮颜色 */
	--swiper-navigation-size: 20px;

	/* 设置按钮大小 */
	.swiper-button-next,
	.swiper-button-prev {
		width: 46px;
		height: 46px;
		background: #000000;
		opacity: 0.4;
		border-radius: 50%;
		transform: translateY(-50%);
		margin-top: 0;
	}
}

.myswiper2 {
	// height: 100%;
	height: 500px;

	.zoom {
		position: absolute;
		top: 0;
		right: 0;
		cursor: pointer;
		z-index: 100;

		b {
			font-size: 22px;
			margin-right: 4px;
		}

		&:hover {
			b {
				color: $color-primary;
			}
		}
	}

	.swiper-slide {
		display: flex;
		justify-content: center;
		align-items: center;

		.smallImg {
			position: relative;
			width: 500px;
			height: 100%;
			text-align: center;
		}
	}

	img {
		max-width: 100%;
		max-height: 100%;
		object-fit: contain;
	}
}

//
</style>