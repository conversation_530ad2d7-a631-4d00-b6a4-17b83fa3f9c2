export const initStepComponentConfig = function (item) {
	const commonProps = {
		stepData: item,
	};
	return {
		defaultConfig:{
			is: "PublicStep",
			props: {
				...commonProps,
				config: this.allStepConfig[item.paramName],
				maskName: this.maskName,
				selectedData: this.selectedData,
			},
			on: {
				"update:maskName": (val) => (this.maskName = val),
				clickFun: (event) => this.selectQuoteParams(item, event),
				showMaskFn: this.showMaskFn,
				viewVideo:(item,citem)=>{
					return this.viewVideo(item, citem, "video")
				},
			},
		},
		sizeConfig:{
			is: "PinsSizeSelect",
			props: {
				...commonProps,
				maskName: this.maskName,
				generalData: this.generalData,
				selectedData: this.selectedData,
				sizeImgP1: this.allStepConfig[item.paramName]?.sizeImgP1,
				smallTitle: this.allStepConfig[item.paramName]?.smallTitle,
				showMbColorImg: true,
			},
			on: {
				"update:maskName": (val) => (this.maskName = val),
				clickFun: (event) => this.selectQuoteParams(item, event),
				closeMask: this.closeMask,
				showMaskFn: this.showMaskFn,
			},
		},
		uploadConfig:{
			is: "StepUpload",
			props: {
				...commonProps,
				id: item.paramName,
				isUpload: this.isUpload,
				remark: this.remark,
				uploadList: this.uploadArtworkList,
				itemData: item,
			},
			dynamicClass: {
				"step-item": true,
				"step-upload": true,
				mask: this.maskName === item.paramName,
			},
			on: {
				"update:isUpload": (val) => (this.isUpload = val),
				"update:remark": (val) => (this.remark = val),
				"update:uploadList": (val) => (this.uploadArtworkList = val),
				closeMask: this.closeMask,
				showMaskFn: this.showMaskFn,
			}
		},
		additionalConfig:{
			is: "PublicStep",
			props: {
				...commonProps,
				config: this.allStepConfig[item.paramName],
				isShowPlayIcon: true,
				maskName: this.maskName,
				selectedData: this.selectedData,
			},
			on: {
				"update:maskName": (val) => (this.maskName = val),
				clickFun: (event) => {
					this.selectQuoteParams(item, event);
				},
				showMaskFn: this.showMaskFn,
				viewVideo: this.viewVideo,
			},
			dynamicClass: {
				"Additional-Upgrades": true,
			},
		},
		qtyConfig:{
			is: "StepQty",
			props: {
				...commonProps,
				id: item.paramName,
				customQty: this.customQty,
				itemData: item,
				restaurants: this.restaurants,
			},
			on: {
				"update:customQty": (val) => (this.customQty = val),
				calcPrice: this.debounceCalcPrice,
				closeMask: this.closeMask,
				showMaskFn: this.showMaskFn,
			},
			dynamicClass: {
				"step-qty": true,
				mask: this.maskName === item.paramName,
			},
		},
		timeConfig:{
			is: "StepTime",
			props: {
				...commonProps,
				id: item.paramName,
				cateData: this.cateData,
				customQty: this.customQty,
				itemData: item,
				pid: this.pid,
				previewMode: this.previewMode,
				priceInfo: this.priceInfo,
				selectedParams: this.selectedData,
			},
			on: {
				closeMask: this.closeMask,
				selectItem: (evt) => this.selectQuoteParams(evt.item, evt.citem),
				showMaskFn: this.showMaskFn,
			},
			dynamicClass: {
				"step-date": true,
				mask: this.maskName === item.paramName,
			},
		}
	}
}
