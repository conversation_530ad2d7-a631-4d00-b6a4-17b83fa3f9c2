<template>
	<div class="MyCheckBox4">
		<h3 class="step-title">
			<div>
				<span class="textLight">{{ lang.step }} {{ stepData.customIndex }}: </span>
				<span v-if="cssModel.titleAdd">{{ cssModel.titleAdd }}</span
				><span>{{ stepData.alias ? stepData.alias : stepData.paramName }}</span>
			</div>
			<div class="tooltip" v-if="stepData.tips">
				<el-tooltip popper-class="cusToolTip" effect="light" :content="stepData.tips" placement="top-start">
					<b class="icon-wenhao1 tip-icon"></b>
				</el-tooltip>
			</div>
		</h3>
		<p class="step-size-title" style="height: 15px">
			<span v-if="cssModel.promptVisible">{{ lang.prompt }}</span>
		</p>
		<div class="imgContent" :style="cssModel.imgContentStyle">
			<div class="imgItemBox4" v-for="item in stepData.childList" :paramName="item.paramName" :key="item.id" :class="{ active: isActive(item) }" @click.stop="activeFun(item)" :style="cssModel.imgItemBox4Style ? cssModel.imgItemBox4Style : ''">
				<div class="imgBox imgBoxM" :class="{ isOther: item.paramName == 'Others' }" :style="cssModel.imgBoxStyle">
					<img :src="filterImage(item.imageJson)" :style="aspectRatio" alt="" />
				</div>
				<span class="product-info" :class="isFontCenter ? 'isFontCenterDiv' : ''">
					<label :for="item.cateName + item.id" class="radio-beauty" :class="isFontCenter ? 'isFontCenterLabel' : ''" />
					<span class="title">{{ item[titleValue] }}</span>
				</span>
				<!--价格-->
				<PriceText :paramData="item" :sizeValue="sizeValue" v-if="showPrice"></PriceText>
			</div>
		</div>

		<!-- <el-input v-show="isOther" type="textarea" rows="4" v-model="selectItem.remark"
      :placeholder="lang.placeholder2"></el-input> -->
		<div v-if="cssModel.buttonVisible" class="text-center">
			<el-button
				class="myBtn"
				@click="next"
				:style="{
					opacity: selectedParams[stepData.paramName].length > 0 ? 1 : 0.5,
				}"
				:disabled="!selectedParams[stepData.paramName].length > 0"
				>{{ lang.next }}
			</el-button>
		</div>
	</div>
</template>

<script>
import PriceText from "@/components/Quote/PriceText";

export default {
	name: "MyCheckBox4",
	components: { PriceText },
	props: {
		sizeValue: {
			type: [Number, String],
			default: 0,
		},
		showPrice: {
			type: Boolean,
			default: false,
		},
		stepData: {
			type: Object,
			default: () => ({}),
		},
		selectedParams: {
			type: Object,
			default: () => ({}),
		},
		titleValue: {
			type: String,
		},
		aspectRatio: {
			type: String,
		},
		isFontCenter: {
			type: Boolean,
			default: false,
		},
		cssModel: {
			type: Object,
			default: () => {
				return {
					buttonVisible: true,
				};
			},
		},
	},
	data() {
		return {
			selectItem: {},
		};
	},
	watch: {},
	computed: {
		lang() {
			return this.$store.getters.lang.quote || {};
		},
		selectVal() {
			return this.selectedParams[this.stepData.paramName] || [];
		},
		device() {
			return this.$store.state.device;
		},
	},
	methods: {
		next() {
			this.$emit("next", this.stepData.paramName);
		},
		isJSON(str) {
			if (typeof str == "string") {
				try {
					var obj = JSON.parse(str);
					if (typeof obj == "object" && obj) {
						return true;
					} else {
						return false;
					}
				} catch (e) {
					return false;
				}
			} else {
				return false;
			}
		},
		filterImage(val) {
			if (this.isJSON(val)) {
				return JSON.parse(val)[0].url;
			} else {
				return val.url;
			}
		},
		isActive(data) {
			if (this.selectVal.length > 0) {
				return this.selectVal.some((item) => {
					return item.id == data.id;
				});
			}
		},
		activeFun(item) {
			this.selectItem = item;
			this.$emit("clickFun", {
				key: this.stepData,
				value: item,
			});
		},
		otherClickFn(item) {
			this.$emit("clickFun", {
				key: this.stepData,
				value: item,
			});
		},
	},
	created() {
		/* setTimeout(() => {
			console.log("业务", this.stepData);
		}, 1000 * 2); */
	},
	mounted() {},
};
</script>

<style scoped lang="scss">
.MyCheckBox4 {
	$bg: #afb1b3;
	$bgc: white;
	background: #fff;
	border-radius: 10px;
	//padding: 40px 30px 30px;
	position: relative;
	z-index: 0;
	margin-bottom: 20px;

	::v-deep .el-textarea__inner {
		border-radius: 4px;

		&:focus {
			border-color: $color-primary;
		}
	}
	.imgContent {
		position: relative;
		display: grid;
		grid-template-columns: repeat(4, 1fr);
		gap: 20px;
		margin: 10px 0 30px;
		overflow: auto;
		@media screen and (max-width: 767px) {
			grid-template-columns: repeat(2, 1fr);
		}

		.imgItemBox4 {
			.imgBox {
				border: 1px dashed $bg;
				img {
					width: 100%;
					height: 100%;
				}
				&.isOther {
					overflow: hidden;
					border-color: transparent;
					border-radius: 12px;
				}
			}
			.isFontCenterDiv {
				position: relative;
				.isFontCenterLabel {
					position: relative !important;
					left: 5px !important;
				}
			}
			.product-info {
				display: flex;
				align-items: center;
				justify-content: center;
				text-align: center;
				margin-top: 10px;
				cursor: pointer;
				font-size: 16px;

				@media screen and (max-width: 767px) {
					font-size: 12px;
				}

				.radio-beauty {
					width: 18px;
					height: 18px;
					min-width: 18px;
					box-sizing: border-box;
					display: inline-block;
					border: 1px solid $bg;
					vertical-align: middle;
					margin: 0 12px 0 3px;
					border-radius: 50%;
					background-color: $bgc;
					background-clip: content-box;
					position: relative;
					cursor: pointer;

					&::after {
						content: "";
						position: absolute;
						border-radius: 50%;
						left: 50%;
						top: 50%;
						transform: translate(-50%, -50%);
						width: 6px;
						height: 6px;
						background-color: $bg;
					}

					@media screen and (max-width: 767px) {
						width: 16px;
						height: 16px;
						min-width: 16px;

						&::after {
							width: 5px;
							height: 5px;
						}
					}
				}
			}

			&.active {
				.imgBox {
					&.isOther {
						overflow: hidden;
						border: 1px solid $color-primary;
						border-radius: 12px;
					}

					border-color: $color-primary;
				}

				.product-info .radio-beauty {
					background-color: $color-primary;
					border-color: $color-primary;

					&::after {
						background-color: $bgc;
					}
				}

				.product-info .title {
					color: $color-primary;
				}
			}

			@media (any-hover: hover) {
				&:hover {
					.zoomIcon {
						color: $color-primary;
					}

					.imgBox {
						border-color: $color-primary !important;
						box-shadow: 0 3px 4px 0 #ccc;
						&.isOther {
							overflow: hidden;
							border: 1px solid $color-primary;
						}
					}

					.product-info {
						.radio-beauty {
							background-color: $color-primary;
							border-color: $color-primary;

							&::after {
								background-color: white;
							}
						}

						.title {
							color: $color-primary;
						}
					}
				}
			}
		}
	}

	.step-title {
		font-size: 24px;
		font-weight: 700;
		color: #333333;
		margin-bottom: 4px !important;
		position: relative;
		display: flex;
		align-items: center;
		column-gap: 12px;

		@media screen and (max-width: 767px) {
			margin-bottom: 12px;
			font-size: 14px;

			.tooltip {
				font-size: 14px;
			}
		}

		.textLight {
			color: $color-primary;
		}

		&.hasTips {
			margin-bottom: 0;
		}
	}

	.step-size-title {
		margin-bottom: 12px;
		font-size: 16px;

		@media screen and (max-width: 767px) {
			margin-bottom: 10px;
			font-size: 12px;
		}
	}

	.tooltip {
		font-size: 18px;

		i {
			color: $color-primary;
		}
	}

	.text-center {
		position: relative;
		text-align: center;
	}

	.myBtn {
		background: $color-primary;
		border-radius: 10px;
		font-size: 18px;
		font-family: Calibri;
		font-weight: 400;
		color: #ffffff;
		padding: 10px 65px;
		margin-top: 20px;

		@media screen and (max-width: 767px) {
			width: 125px;
			height: 30px;
			background: $color-primary;
			border-radius: 5px;
			font-size: 12px;
			font-family: Arial;
			font-weight: 400;
			color: #ffffff;
			margin-top: 10px;
			padding: 0;
		}
	}
}
//cg  这样命名F12检查 可以直接搜索得到.方便修改
@media (max-width: 767px) {
	.MyCheckBox4 .imgContent {
		grid-template-columns: repeat(2, 1fr) !important;
	}
	.MyCheckBox4 .imgContent .imgItemBox4 {
		width: auto !important;
	}
	.MyCheckBox4 .imgContent .imgItemBox4 .imgBoxM {
		width: auto !important;
		height: auto !important;
	}
}
//cg end
</style>
