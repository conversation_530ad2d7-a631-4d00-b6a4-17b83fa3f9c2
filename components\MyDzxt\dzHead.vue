<template>
	<header class="header">
		<div class="header-left">
			<div class="header-left-menu header-menu" @click="showMenu = true">
				<b class="icon-a-icon-menuzhuanhuan"></b>
				<!-- <span>{{ langDesign.menu }}</span> -->
			</div>
			<div class="header-left-logo">
				<a href="/"> <img :src="this.$store.state.proSystem.logo" alt="logo"/></a>
			</div>
		</div>
		<div class="header-right">
			<div class="left">
				<div class="menu" @click="showMenu = true">
					<b class="icon-a-icon-menuzhuanhuan"></b>
				</div>
				<div class="email">
					<div class="left-icon">
						<b class="icon-a-icon-Infozhuanhuan"></b>
					</div>
					<div class="textCon">
						<p>
							<a :href="`tel:${this.$store.state.proSystem.phone}`">{{ this.$store.state.proSystem.phone }}</a>
						</p>
						<p>
							<a :href="`mailto:${this.$store.state.proSystem.email}`">{{ this.$store.state.proSystem.email }}</a>
						</p>
					</div>
				</div>
				<a href="tel:************" class="talk">
					<div class="left-icon">
						<b class="icon-a-icon-talkzhuanhuan"></b>
					</div>
					<div class="textCon">
						<p>{{ langDesign.talkPerson }}</p>
						<p>************</p>
					</div>
				</a>
			</div>
			<div class="right">
				<div id="chatComme100" class="chat" @click="triggerChat" v-show="$store.state.proType!==0">
					<div class="left-icon">
						<b class="icon-a-icon-chatzhuanhuan"></b>
					</div>
					<div class="textCon">{{ langDesign.chatNow }}</div>
				</div>
				<div class="cart">
					<n-link tag="a" to="/cart" class="cart-box">
						<div class="left-icon">
							<b class="icon-a-icon-cartzhuanhuan">
								<span>{{ $store.state.cartQuantity || 0 }}</span>
							</b>
						</div>
					</n-link>
				</div>
				<div v-if="$store.state.token" class="userInfo" @mouseenter="flag=true" @mouseleave="flag=false">
					<div class="img">
						<b class="iconfont icon-touxiang"></b>
					</div>
					<span class="name">{{ $store.state.userInfo.firstName }} {{ $store.state.userInfo.lastName }}</span>
					<b class="iconfont" :class="flag ? 'icon-sanjiao2' : 'icon-sanjiao1'"></b>

					<div class="options" v-show="flag">
						<a href="/user/account/orders" class=""> {{ langDesign.myOrders }} </a>
						<a href="/user/account/profile" class=""> {{ langDesign.myProfile }} </a>
						<a href="/user/account/address" class=""> {{ langDesign.myAddress }} </a>
						<a @click="logOut">{{ lang.logOut }}</a>
					</div>
				</div>
				<div v-else class="loginBtn" @click="$store.commit('setLogin', 'login')">
					<div class="left-icon">
						<b class="icon-a-icon-loginzhuanhuan"></b>
					</div>
					<div class="textCon">{{ langDesign.logIn }}</div>
				</div>
			</div>
			<div class="right-mb">
				<div class="header-menu" @click="showMyDesign" v-if="isLogin">
					<b class="icon-a-icon-Designzhuanhuan"></b>
					<span>{{ langDesign.myDesign }}</span>
				</div>
				<div class="header-menu" @click="shareClick">
					<b class="icon-a-icon-Sharezhuanhuan"></b>
				</div>
				<div class="header-menu" @click="toCart">
					<div class="left-icon">
						<b class="icon-a-icon-cartzhuanhuan">
							<span>{{ $store.state.cartQuantity || 0 }}</span>
						</b>
					</div>
				</div>
			</div>
		</div>
		<el-drawer :visible.sync="showMenu" direction="ltr" :with-header="false" :size="size" close-on-press-escape>
			<div class="sidebar">
				<Bread :data="layout[0]" class="topHeader" :class="{ statiy: !isQuote, neonQuote: isNeonQuote }"></Bread>
			</div>
		</el-drawer>
	</header>
</template>

<script>
import Bread from './Breadline'
import {deepClone} from "@/utils/utils"

export default {
	data() {
		return {
			flag: false,
			showMenu: false,
			showProduct: false,
			showChat: false,
		};
	},
	computed: {
		lang() {
			return this.$store.getters.lang.layout || {};
		},
		langDesign() {
			return this.$store.getters.lang?.design;
		},
		device() {
			return this.$store.state.device;
		},
		size() {
			return this.device !== "mb" ? "500px" : "300px";
		},
		pagePath() {
			return this.$store.state.pagePath;
		},
		userInfo() {
			return this.$store.state.userInfo;
		},
		isLogin() {
			return this.$store.getters.isLogin;
		},
		isMb() {
			return this.$store.state.device === 'mb'
		},
		layout() {
			let data = deepClone([...this.$store.state.layoutInfo.pc]);
			if (this.isMb) data = this.$store.getters.deepMerge(data, deepClone(this.$store.state.layoutInfo.mb));
			return data;
		},
		isQuote() {
			return this.$store.state.pagePath.includes('/quote');
		},
		isNeonQuote() {
			return this.$store.state.pagePath.includes('/quote/neon-signs');
		},
	},
	mounted() {
		this.$store.dispatch("updateHeadFootPages");
		let count = 0;
		let timed = setInterval(() => {
			count += 1;
			if (window.Intercom) {
				clearInterval(timed);
				let toggleInterCom = (bool) => {
					let intercomContainer = document.querySelector('#intercom-container');
					if (intercomContainer) {
						document.querySelector('#intercom-container').style.display = bool ? "block" : "none";
					}
				}
				Intercom('onHide', () => {
					this.showChat = false;
					toggleInterCom(false)
				});
				Intercom('onShow', () => {
					this.showChat = true;
					toggleInterCom(true)
				});
				let timed2 = setInterval(() => {
					if (document.querySelector('.intercom-lightweight-app')) {
						clearInterval(timed2);
						document.querySelector('.intercom-lightweight-app').style.display = "none"
					}
				}, 200)
			}
			if (count > 10) {
				clearInterval(timed);
			}
		}, 1000)
	},
	components: {
		Bread
	},
	methods: {
		shareClick() {
			this.$emit("shareClick");
		},
		showMyDesign() {
			this.$emit("showMyDesign");
		},
		toCart() {
			window.location.href = "/cart";
		},
		triggerChat() {
			if (window.Intercom) {
				if (this.showChat) {
					Intercom("hide");
				} else {
					Intercom("show");
				}
			}
		},
		logOut() {
			this.$toast.show(this.lang.confirmLogOut, {
				containerClass: "toast-action-box",
				className: "toast-action",
				duration: null, // 不自动关闭弹窗
				action: [
					{
						text: this.lang.sure,
						onClick: (e, toastObject) => {
							this.$store.commit("logOut");
							toastObject.goAway(0);
						},
					},
					{
						text: this.lang.cancel,
						onClick: (e, toastObject) => toastObject.goAway(0), // 关闭弹窗
					},
				],
			});
		},
	},
};
</script>

<style scoped lang="scss">
.header {
	display: grid;
	grid-template-columns: 130px 1fr;
	gap: 35px;
	height: 60px;
	padding: 0 20px;
	background: #171719;
	color: #ffffff;
	font-size: 14px;

	.header-left {
		min-width: 0;
		display: flex;
		justify-content: center;
		align-items: center;

		.header-left-menu {
			display: none;
		}

		.header-left-logo {
			height: 50px;
			width: 130px;
			a{
				display: inline-block;
				width: 100%;
				height: 100%;

				img{
					width: 100%;
					height: 100%;
					object-fit: contain;
				}
			}
		}
	}

	.header-right {
		display: flex;
		justify-content: space-between;

		b {
			font-size: 24px;
			cursor: pointer;
		}

		a {
			color: #ffffff;
			text-decoration: none;
		}

		.left {
			display: flex;
			align-items: center;
		}

		.right {
			display: flex;
			align-items: center;
		}

		.right-mb {
			display: none;
		}

		.header-menu,
		.email,
		.talk,
		.chat,
		.cart {
			display: flex;
			align-items: center;
			cursor: pointer;

			.left-icon {
				margin-right: 8px;

				b {
					position: relative;

					span {
						height: 18px;
						min-width: 18px;
						color: #fff;
						font-size: 12px;
						line-height: 18px;
						text-align: center;
						border-radius: 50%;
						background: #eb5757;
						position: absolute;
						right: -10px;
						top: -5px;
					}
				}
			}
		}

		.email,
		.talk,
		.cart {
			margin-left: 41px;
		}

		.loginBtn {
			height: 34px;
			margin: 0 0 0 30px;
			color: #ffffff;
			display: flex;
			align-items: center;
			cursor: pointer;

			.textCon {
				margin-left: 8px;
			}
		}

		.userInfo {
			padding: 0 1rem 0 30px;
			display: flex;
			align-items: center;
			height: 100%;
			position: relative;

			.img {
				// width: 34px;
				// height: 34px;
				border-radius: 50%;

				.icon-touxiang {
					font-size: 19px;
				}

				img {
					border-radius: 50%;
					object-fit: cover;
				}
			}

			.name {
				max-width: 100px;
				margin-left: 11px;
				font-size: 16px;
				font-weight: 400;
				color: #ffffff;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
			}

			.icon-sanjiao2,
			.icon-sanjiao1 {
				margin-left: 7px;
				font-size: 12px;
				color: #ffffff;
			}

			.options {
				display: block;
				border-radius: 5px;
				width: max-content;
				font-weight: normal;
				box-shadow: 0 0 10px -5px rgba(0, 0, 0, .25);
				position: absolute;
				z-index: 99;
				left: 14px;
				top: 4em;

				a {
					display: block;
					text-align: left;
					line-height: 1.5em;
					padding: 0.6em 1.2em;
					color: #333333;
					background: white;

					&:first-child {
						border-radius: 5px 5px 0 0;
					}

					&:last-child {
						border-radius: 0 0 5px 5px;
					}

					&:hover {
						background: #f2f2f2;
					}
				}

				&::before {
					content: "";
					width: 12px;
					height: 12px;
					background: white;
					box-shadow: -1px -1px 6px -3px rgba(0, 0, 0, .5);
					transform: scale(1, 1.3) rotate(45deg);
					position: absolute;
					z-index: -1;
					left: 20%;
					top: -6px;
				}
			}
		}
	}

	.sidebar ::v-deep {
		width: 100%;
		background-color: #fff;
		cursor: pointer;
		color: #171719;
		font-size: 14px;

		.user-box {
			display: flex;
			flex-direction: column;
			align-items: center;

			.avatar {
				width: 100px;
				height: 100px;
				border-radius: 50%;
				margin: 20px;
				border: 1px solid #D17448;
			}

			.name {
				margin-bottom: 10px;
			}

			#button {
				height: 32px;
				min-width: 180px;
				border-radius: 4px;
			}

			#button1 {
				height: 32px;
				min-width: 180px;
				border-radius: 4px;
			}
		}

		.link-box {
			padding: 10px 20px;
			display: flex;
			flex-direction: column;
			align-items: flex-start;

			b {
				color: #171719;
				margin-right: 8px;
			}

			strong {
				color: #171719;
			}

			.padding {
				padding: 10px 5px;
				width: 100%;
			}

			.arrow {
				float: right;
			}

			.options {
				display: flex;
				flex-direction: column;
				font-weight: 400;

				a {
					margin-bottom: 4px;

					&:last-child {
						margin-bottom: 0;
					}
				}

				b {
					flex: 1;
					width: 15vw;
					padding: 10px 0 10px 32px;
				}
			}

			.select {
				border-radius: 1vw;
				background: #fce0d4;
			}

			a:hover {
				background-color: #fce0d4;
				background-position: -1px;
				border-radius: 1vw;
			}
		}

		.contact-box {
			display: flex;
			flex-direction: column;
			align-items: flex-start;
			padding: 15px 20px;

			strong {
				font-weight: 700;
			}

			p {
				color: #999;
				margin: 1vw 0 0;

				a {
					color: #999;
				}
			}

			b {
				margin-right: 8px;
			}
		}
	}
}

@include respond-to(mb) {
	.header {
		height: 44px;
		font-size: 12px;
		padding: 0 10px;
		background-color: #ffffff;
		color: #333333;

		b {
			font-size: 19px !important;
		}

		.header-menu {
			display: flex;
			// flex-direction: column;
			justify-content: center;
			align-items: center;
		}

		.header-left {
			.header-left-menu {
				display: flex;
				margin-right: 16px;
			}

			.header-left-logo {
				height: 44px;
			}

			img {
				cursor: pointer;
			}
		}

		.header-right {
			justify-content: flex-end;

			.left {
				display: none;
			}

			.right {
				display: none;
			}

			.right-mb {
				display: flex;

				.header-menu {
					// margin-left: 30px;
					margin-right: 19.5px;
				}

				.header-menu:last-child {
					margin-right: 0;
				}
			}
		}

		.sidebar {
			font-size: 14px;

			b {
				font-size: 14px !important;
			}

			.link-box .options {
				width: 100%;
			}

			.link-box .options b {
				width: 100%;
			}
		}
	}
}
</style>
