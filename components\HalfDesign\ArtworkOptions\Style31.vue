<template>
	<div class="mb-4" :class="stepData.styleName">
		<half-design-litter-title :index="stepData.id" :isTitleContent="titleModel.isTitleContent" :selectedValue="titleModel.valueName || shape" :selectedValue2="selectItem?.selfPrice.data || shape2" :data-name="`${stepData.styleClass + '_' + stepData.id}`" style="margin: 4px 0" :stepTitle="stepData.minStepTitle" v-show="stepData.attributeTitle">{{ stepData.attributeTitle }} </half-design-litter-title>
		<slot name="stepText"></slot>
            <!-- //isHideStep，控制只有一个子参数的时候提上去 -->
		<div class="step-content"
			v-if="attributeFlag !== 'printColor'&&(!stepData.isHideStep || (stepData.isHideStep && stepData.productParamList.length > 1))">
			<!-- @click="selectStep(step, index)" -->
			<div class="step-item" :style="areaClick" :class="{ active: index === selectIndex }" v-for="(step, index) in stepData.productParamList" :key="index" @click="isSelectStep(step, index)">
				<div class="imgWrap" v-show="getImg(step)">
					<img :src="getImg(step)" :alt="attributeFlag == 'color' ? step.imgAltTitle : step.valueName" :title="attributeFlag == 'color' ? step.imgAltTitle : step.valueName" />
				</div>
				<div class="d-flex-center text-center name">
					<div class="text-truncate">
						<div class="nameText">
							<!-- 		{{ selectIndex }} -->
							<span class="valueNameText">{{ step[nameProperty] }} </span>
							<div class="questionMark" v-show="step.remark">
								<v-tooltip bottom>
									<template v-slot:activator="{ on, attrs }">
										<v-icon v-bind="attrs" v-on="on" size="18px"> mdi-help-circle-outline </v-icon>
									</template>
									<div class="text-center" style="display: flex; align-items: start">
										<div style="text-align: center; color: #fff; line-height: 1; font-size: 13px; word-break: break-word; white-space: normal; max-width: 250px">
											{{ step.remark }}
										</div>
									</div>
								</v-tooltip>
							</div>
						</div>
						<div class="priceText" :class="{ redText: index === selectIndex }">
							<halfPriceBox v-model="step.selfPrice" :priceData="step" :showUnit="true"></halfPriceBox>
						</div>
					</div>
				</div>
				<half-design-check-icon class="absolute-top-right check-icon"></half-design-check-icon>
			</div>
		</div>
		<div class="errorTip">
			<v-alert dense outlined type="error">
				{{ langSemiCustom.errorTip }}
			</v-alert>
		</div>
	</div>
</template>

<script>
import { round2, analyzeImageColor, rgbToHex } from "@/utils/utils";
import canvas from "assets/js/halfDesign/canvas";
export default {
	name: "Style3-1",
	inject: ["getUnit"],
	components: {},
	props: {
		stepData: {
			type: Object,
			default: () => ({}),
		},
	},
	data() {
		return {
			selectIndex: -1,
			selectItem: null,
			selectColorModel: {
				uploadModel: [],
				inputModel: [],
			},
			//当前标题对象
			titleModel: {
				valueName: "", //颜色标题
				selfPrice: { data: "", isFree: false }, //价格标题
				isTitleContent: true, //是否显示标题   true||显示  false||隐藏
			},
			selfPrice: 0,
		};
	},
	watch: {
		canShowPrintColorPrice() {
			if (this.attributeFlag === "printColor") {
				this.setItemSelfPrice();
				this.$forceUpdate();
			}
		},
	},
	computed: {
		nameProperty() {
			let attributeFlag = this.attributeFlag,
				key;
			switch (attributeFlag) {
				case "color":
					key = "colorAlias";
					break;
				default:
					key = "valueName";
					break;
			}
			return key;
		},
		attributeFlag() {
			return this.stepData.attributeFlag;
		},
		shape() {
			return this.selectItem?.valueName;
		},
		shape2() {
			if (this.selectItem) {
				if (!this.stepData.isShowPrice) return "";
				return this.selectItem.selfPrice.data;
			}
		},
		langSemiCustom() {
			return this.$store.getters.lang?.semiCustom;
		},
		langQuote() {
			return this.$store.getters.lang.quote || {};
		},
		unit() {
			return this.getUnit();
		},
		symbolCode() {
			return this.$store.state.currency.symbol;
		},
		rate() {
			return this.$store.state.currency.rate;
		},
		canClick() {
			return this.$store.state.halfDesign.canClickBtn;
		},
		areaClick() {
			if (!(this.attributeFlag == "area")) return;
			return { pointerEvents: this.canClick ? "auto" : "none" };
		},
		proId() {
			return this.$store.state.proId;
		},
		canShowPrintColorPrice() {
			return this.$store.state.halfDesign.canShowPrintColorPrice;
		},
	},
	methods: {
		getImg(step) {
			let attributeFlag = this.attributeFlag,
				key;
			switch (attributeFlag) {
				case "color":
					key = "sizeImg";
					break;
				case "area":
					key = "imgDetail";
					if (step.areaImg) {
						key = "areaImg";
					}
					break;
				default:
					key = "imgDetail";
					break;
			}
			return step[key];
		},
		clearData(state, areaIndex = -1) {
			if (!(this.attributeFlag == "area")) return;
			this.selectIndex = areaIndex;
			this.selectItem = null;
			if (areaIndex < 0) return;
			if (this.stepData && this.stepData.productParamList && this.stepData.productParamList.length > 0) {
				let item = this.stepData.productParamList[areaIndex];
				this.selectStep(item, areaIndex, state);
			}
		},
		//是否需要选择
		isSelectStep(item, index = -1, state = false) {
			if (this.attributeFlag !== "printColor") {
				if (this.attributeFlag === "tile" && index === 1) {
					canvas.clearTexts();
					canvas.clearImgs();
					this.$store.commit("halfDesign/resetTextArr");
					this.$store.commit("halfDesign/setUploadImgList", []);
					this.$store.commit("halfDesign/setTile", true);
				}
				if (this.attributeFlag === "tile" && index === 0) {
					canvas.clearTexts();
					canvas.clearImgs();
					this.$store.commit("halfDesign/resetTextArr");
					this.$store.commit("halfDesign/setUploadImgList", []);
					this.$store.commit("halfDesign/setTile", false);
				}
				this.selectStep(item, index);
			}
		},
		selectStep(item, index = -1, state = false) {
			this.$emit("selectStep", {
				type: this.stepData.attributeFlag,
				data: item,
				parent: this.stepData,
				id: this.stepData.id,
				firstSelect: state,
			});
			this.selectIndex = index;
			this.selectItem = item;
			if (this.attributeFlag === "printColor") {
				//是否打开多色备注框
				if (!state) this.$store.commit("halfDesign/setMoreColor", !item.isOneColor);
				//pens半制定的网站 定制化处理
				if (this.proId == 317) {
					//默认不选中则初始化
					if (this.selectIndex == -1) {
						this.titleModel.isTitleContent = false;
					} else {
						this.titleModel.isTitleContent = true;
					}
				}
			}
		},
		getPrice(step) {
			let stepCopy = JSON.parse(JSON.stringify(step));
			let priceType = stepCopy.priceType,
				code = this.symbolCode,
				price = 0,
				rate = this.rate;
			if (priceType === 1) {
				price = round2(stepCopy.unitPrice * rate);
				return {
					t: `+${code}${price}/${this.unit}`,
					show: price && price > 0,
				};
			} else {
				return {
					show: false,
				};
			}
		},
		selectDefault() {
			if (this.stepData.productParamList.length) {
				this.selectStep(this.stepData.productParamList[0], 0, true);
			}
			this.titleModel.isTitleContent = true;
		},
		//初始化业务
		init() {
			this.initCss();
			/* 监听color状态 */
			this.getSelectImgColor();
			this.getSelectImgColorDelete();
			this.getSelectImgColorEdit();
			this.getSelectImgColorClear();
			/* 监听color状态end */
		},
		//初始化样式
		initCss() {
			//this.setSelectImgColorRend();
		},
		/**
		 * 选项卡切换并清空
		 * 兄弟组件在 components/HalfDesign/Size/Style5.vue 目录下
		 */
		getSelectImgColorClear() {
			this.$Bus.$off("selectImgColorClear");
			this.$Bus.$on("selectImgColorClear", (res) => {
				this.selectColorModel.uploadModel = [];
				this.selectColorModel.inputModel = [];
				this.selectDefault();
			});
		},
		/**
		 * 画板弹窗同步--修改
		 * 兄弟组件在 components/HalfDesign/EditLogo.vue 目录下
		 */
		getSelectImgColorEdit() {
			this.$Bus.$off("selectImgColorEdit");
			this.$Bus.$on("selectImgColorEdit", (res) => {
				if (res) {
					if (res.upload?.id) {
						new Promise((resolve) => {
							let tentArray = [];

							this.selectColorModel.uploadModel.map((item, index) => {
								if (item.id == res.upload.id) {
									item.secure_url = res.upload.secure_url;
									tentArray.push({
										isEdit: true, //是否修改   true||是   false||否
										secure_url: res.upload.secure_url, //要修改的base64
									});
								} else {
									tentArray.push({
										isEdit: false,
										secure_url: item.secure_url, //不需要修改
									});
								}
							});

							let uploadImgList = this.selectColorModel.uploadModel;
							if (uploadImgList.length > 0) {
								let s = 0;
								let uploadImgListOld = [];
								uploadImgListOld = uploadImgList;
								uploadImgList.map((item, index) => {
									s += 1;
									if (tentArray[index].isEdit) {
										this.analyzeImageColorFunc(tentArray[index].secure_url, (r) => {
											tentArray[index].isEdit = false; //修改完后。变成否
											r.map((j, jIndex) => {
												j.hex = rgbToHex(j.color);
											});
											uploadImgListOld[index] = {
												...item,
												secure_url: res.upload.secure_url,
												colorList: r,
											};
											if (s == uploadImgList.length) {
												this.selectColorModel.uploadModel = uploadImgListOld;
												setTimeout(() => {
													//等图片上传完and上面计算逻辑吧。1000毫秒即可。否则报错. 支持批量上传一堆图片。 1秒后调渲染高亮方法.
													this.setSelectImgColorRend();
												}, 1000);
											}
										});
									}
								});
							}
						});
					}
				}
			});
		},
		/**
		 * 左侧删除 同步 右侧
		 * 兄弟组件在 assets/js/icon.js 目录下
		 */
		getSelectImgColorDelete() {
			this.$Bus.$off("selectImgColorDelete");
			this.$Bus.$on("selectImgColorDelete", (res) => {
				if (res) {
					if (res.input?.id) {
						this.selectColorModel.inputModel.map((i, iIndex) => {
							let arr = [];
							i.valueArray.map((j, jIndex) => {
								if (res.input.id == j) {
									i.valueArray.splice(jIndex, 1);
									if (i.valueArray.length == 0) {
										this.selectColorModel.inputModel.splice(iIndex, 1);
									}
								}
							});
						});
					}
					if (res.upload?.id) {
						console.log("删除上传", res.upload.id);
						new Promise((resolve) => {
							let uploadImgList = this.selectColorModel.uploadModel;
							if (uploadImgList.length > 0) {
								let s = 0;
								uploadImgList.map((item, index) => {
									s += 1;
									this.analyzeImageColorFunc(item.secure_url, (r) => {
										if (s == uploadImgList.length) {
											this.setSelectImgColorRend();
										}
									});
								});
							}
						});
					}
				}
				this.setSelectImgColorRend();
			});
		},
		//获取color总数
		analyzeImageColorFunc(base64, f) {
			analyzeImageColor(base64, {
				ignoreArr: [],
				keepCount: 9,
				removeSimilarColors: true,
			}).then((r) => {
				if (r?.length > 0) {
					if (f) {
						f(r);
					}
				}
			});
		},
		/**
		 * color选项卡交互高亮
		 * 兄弟组件在 HalfDesign/Design/style5 目录下
		 */
		getSelectImgColor() {
			this.$Bus.$off("selectImgColor");
			this.$Bus.$on("selectImgColor", (res) => {
				if (res) {
					this.selectColorModel = res;
				}
				let totalListLength = this.stepData.productParamList.length;
				if (totalListLength >= 1) {
					this.setSelectImgColorRend();
				}
			});
		},
		/**
		 * 渲染高亮color选项卡
		 */
		setSelectImgColorRend() {
			console.log("打印", this.selectColorModel);
			let totalListLength = this.stepData.productParamList.length;
			let { inputModel, uploadModel } = this.selectColorModel;
			let colorArray = [];
			//输入框业务
			if (inputModel) {
				inputModel.map((i, iIndex) => {
					colorArray.push(i.key);
				});
			}
			//图片上传业务
			if (uploadModel?.length > 0) {
				uploadModel.map((i, iIndex) => {
					i.colorList.map((j, jIndex) => {
						colorArray.push(j.hex);
					});
				});
			}
			let sum = 0;
			let rLength = [...new Set(colorArray)].length;
			if (rLength == 0) {
				sum = -1;
			} else if (rLength > totalListLength - 1) {
				//如果color数超过选项卡则选最高选项卡
				sum = totalListLength - 1;
			} else {
				sum = rLength - 1;
			}
			this.selectIndex = sum;
			//同步显示标题
			if (this.stepData.productParamList[this.selectIndex]) {
				this.titleModel.valueName = this.stepData.productParamList[this.selectIndex].valueName;
				this.titleModel.selfPrice = this.stepData.productParamList[this.selectIndex].selfPrice;
			} else {
				this.titleModel = {
					valueName: "",
					selfPrice: { data: "", isFree: false },
					isTitleContent: true,
				};
			}
			//没有情况下. 执行默认业务
			if (this.selectIndex == -1) {
				this.selectDefault(); //默认业务
			} else {
				this.titleModel.isTitleContent = true;
				this.selectStep(this.stepData.productParamList[this.selectIndex], this.selectIndex);
			}

			this.$forceUpdate();
		},
		setItemSelfPrice() {
			this.stepData.productParamList.forEach((item) => {
				this.$set(item, "selfPrice", { data: "", isFree: false });
				// if (item.copySelfPrice) {
				// 	//打印方式关闭了展示以及计算打印颜色价格
				// 	if (this.canShowPrintColorPrice) {
				// 		item.selfPrice = item.copySelfPrice;
				// 	} else {
				// 		item.selfPrice = this.langQuote.free;
				// 	}
				// 	return;
				// }
				// item.selfPrice = this.getPrice(item)["show"] ? this.getPrice(item)["t"] : this.langQuote.free;
				// item.showFree = this.getPrice(item)["show"] ? false : true;
				// item.copySelfPrice = item.selfPrice;
				// item.copyShowFree = item.showFree;
				if (this.attributeFlag === "printColor") {
					if (!this.canShowPrintColorPrice) {
						this.$set(item, "selfPrice", { data: this.langQuote.free, isFree: true });
					}
				}
			});
		},
	},
	created() {
		this.setItemSelfPrice();
	},
	mounted() {
		if (this.attributeFlag === "area") {
			this.$Bus.$on("clearArea", this.clearData);
			this.$Bus.$on("selectDefaultAreaStep", this.clearData);
		}
		if (this.attributeFlag === "printColor") {
			this.$Bus.$on("selectDefaultPrintColorStep", this.selectDefault);
			this.init();
		}
		if (this.attributeFlag === "color") {
			this.$Bus.$on("selectDefaultColorStep", this.selectDefault);
		}
		if (this.attributeFlag === "other") {
			this.$Bus.$on("selectDefaultOtherStep", this.selectDefault);
		}
		if (this.attributeFlag === "size") {
			this.$Bus.$on("selectDefaultOtherStep", this.selectDefault);
		}
	},
	beforeDestroy() {
		this.$Bus.$off("clearArea", this.clearData);
		this.$Bus.$off("selectDefaultAreaStep", this.clearData);
		this.$Bus.$off("selectDefaultPrintColorStep", this.selectDefault);
		this.$Bus.$off("selectDefaultColorStep", this.selectDefault);
		this.$Bus.$off("selectDefaultOtherStep", this.selectDefault);

		this.$Bus.$off("selectImgColor");
		this.$Bus.$off("selectImgColorDelete");
		this.$Bus.$off("selectImgColorEdit");
		this.$Bus.$off("selectImgColorClear");
	},
};
</script>

<style scoped lang="scss">
@import "~assets/css/half.scss";

.step-content {
	display: grid;

	.step-item {
		position: relative;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		@include step-default;
		min-width: 0;
		cursor: pointer;
		color: #333333;
		overflow: hidden;
		background-color: #f6f6f6;
		border-radius: 6px;

		.text-truncate {
			display: flex;
			align-items: center;
			font-weight: 400;
			font-size: 14px;

			.nameText {
				display: flex;
				align-items: center;
				overflow: hidden;
				max-width: 100%;
			}

			.valueNameText {
				white-space: normal;
				word-wrap: break-word;
			}
		}

		.name {
			margin-top: 4px;
		}

		.questionMark {
			flex-shrink: 0;
			display: none;
		}

		.priceText {
			display: none;
		}

		.check-icon {
			display: none;
		}

		.zoom-icon {
			display: none;
		}
	}

	.step-item-bg {
		background-color: #f6f6f6;
	}
}

.noStyle .step-content {
	display: none;
}

//文字
.style1 .step-content,
.style1_2 .step-content {
	grid-template-columns: repeat(4, 1fr);
	gap: 10px;

	.step-item {
		padding: 20px 10px;

		&.active {
			padding: 19px 9px;
			background: #fff;

			.priceText {
				color: #333333;
			}

			.check-icon {
				display: flex;
			}
		}

		@media (any-hover: hover) {
			&:hover {
				padding: 19px 9px;

				.priceText {
					color: #333333;
				}
			}
		}

		.imgWrap {
			display: none !important;
		}

		.text-truncate {
			flex-direction: column;
			justify-content: center;
			row-gap: 4px;

			.priceText {
				display: flex;
				color: #333333;

				&.redText {
					color: #de3500;
				}
			}
		}
	}
}

@media (max-width: 414px) {
	//文字
	.style1 .step-content,
	.style1_2 .step-content {
		grid-template-columns: repeat(2, 1fr);
		gap: 10px;
	}
}

// 文字2
.style1_2 .step-content {
	.step-item {
		.text-truncate {
			.priceText {
				display: none;
			}
		}
	}
}

.style2 .step-content {
	grid-template-columns: repeat(2, 1fr);
	gap: 10px;

	.step-item {
		&.active {
			.check-icon {
				display: flex;
			}
		}

		.imgWrap {
			@include flex-center;
			width: 100%;
			margin-bottom: 6px;
		}

		.text-truncate {
			.valueNameText {
				margin-right: 4px;
			}
		}

		.questionMark {
			display: flex;
		}
	}
}

.style3 .step-content,
.style3_2 .step-content,
.style3_3 .step-content {
	grid-template-columns: repeat(3, 1fr);
	gap: 10px;

	.step-item {
		background-color: #f7f7f7;

		&.active {
			background-color: #ffffff;

			.check-icon {
				display: flex;
			}
		}

		.imgWrap {
			@include flex-center;
		}

		.text-truncate {
			flex-direction: column;
			justify-content: center;
			row-gap: 4px;

			.priceText {
				display: flex;

				&.redText {
					color: #de3500;
				}
			}
		}

		.questionMark {
			margin-left: 4px;
			display: flex;
		}
	}
}

.style3_2 .step-content {
	.step-item {
		.questionMark {
			display: none;
		}
	}
}

.style3_3 .step-content {
	.step-item {
		.questionMark {
			display: none;
		}

		.text-truncate {
			.priceText {
				display: none;
			}
		}
	}
}

.style4 .step-content {
	grid-template-columns: repeat(4, 1fr);
	gap: 10px;

	.step-item {
		&.active {
			.check-icon {
				display: flex;
			}
		}

		.imgWrap {
			@include flex-center;
		}

		.text-truncate {
			flex-direction: column;
			justify-content: center;
			row-gap: 4px;

			.valueNameText {
				flex: 1;
				text-align: left;
			}
		}

		// .questionMark {
		// 	margin-left: 4px;
		// 	display: flex;
		// }
	}
}

.style5 .step-content {
	grid-template-columns: repeat(5, 1fr);
	gap: 10px;

	.step-item {
		&.active {
			.check-icon {
				display: flex;
			}
		}

		.imgWrap {
			@include flex-center;
		}

		.text-truncate {
			flex-direction: column;
			justify-content: center;
			row-gap: 4px;
		}

		.questionMark {
			margin-left: 4px;
			display: flex;
		}
	}
}

.style6 .step-content {
	grid-template-columns: repeat(6, 1fr);
	gap: 8px;

	.step-item {
		padding: 6px;
		background-color: #f7f7f7;

		&.active {
			padding: 4px;
			background-color: #fff;

			.check-icon {
				display: flex;
			}
		}

		@media (any-hover: hover) {
			&:hover {
				padding: 4px;

				.priceText {
					color: #333333;
				}
			}
		}

		.imgWrap {
			@include flex-center;
		}

		.text-truncate {
			flex-direction: column;
			justify-content: center;
			row-gap: 4px;

			.valueNameText {
				text-align: left;
			}
		}

		// .questionMark {
		// 	margin-left: 4px;
		// 	display: flex;
		// }
	}
}

@include respond-to(mb) {
	.step-content {
		gap: 5px !important;

		.step-item {
			padding: 6px;

			&.active {
				padding: 5px;
			}

			@media (any-hover: hover) {
				&:hover {
					padding: 5px;
				}
			}

			.text-truncate {
				font-size: 12px;
				row-gap: 2px !important;
				white-space: normal;

				.valueNameText {
					white-space: normal;
					word-wrap: break-word;
				}
			}
		}
	}

	.style1 .step-content {
		.step-item {
			padding: 10px;

			&.active {
				padding: 9px;
			}

			@media (any-hover: hover) {
				&:hover {
					padding: 9px;
				}
			}
		}
	}

	.style6 .step-content {
		grid-template-columns: repeat(3, 1fr);
		gap: 5px;
	}
}
</style>
