<template>
	<div class="quoteBox" nodebounce @click="showSelected = true">
		<template v-if="$store.getters.isManage">
			<span>Quick Quote Area</span>
		</template>
		<template v-else>
			<div class="quoteBoxHeader" v-if="showStepBar || currentStep > 1">
				<div class="return" @click="prevStep" v-show="currentStep > 1"><b class="icon-Return"></b>{{ lang.return }}</div>
				<step-bar v-if="showStepBar" :active-step="currentStep" :step="maxStep" color="#EBEBEB"></step-bar>
			</div>
			<template v-if="selectedShape">
				<div class="selectedShapeWrap" :class="{ 'animate-up': animateUp, 'animate-down': animateDown }" v-show="showSelected">
					<div class="selectedShape">
						<span>{{ shapeKey }}:</span>
						<span>{{ selectedShape }}</span>
					</div>
				</div>
			</template>
			<div class="quoteBoxContent stepList">
				<template v-for="(item, index) in filterShowGeneralData">
					<div class="step-item step-size" :id="item.customStepName + '_' + item.id" :key="index" v-if="showFn(item, 'size')">
						<StepTitle :title="getTitle(item)" :step="getStep(item)"></StepTitle>
						<StepSize :largeGap="true" :hideHelp="hideSizeHelp" :itemData="item" :selectedData="selectedData" @selectQuoteParams="selectQuoteParams($event.item, $event.citem)"></StepSize>
					</div>
					<div class="step-item step-qty" :id="item.customStepName + '_' + item.id" :key="index" v-if="showFn(item, 'qty')">
						<StepTitle :title="getTitle(item)" :step="getStep(item)"></StepTitle>
						<StepQty :hideSavePrice="device === 'mb'" :largeGap="true" :qtyList="qtyList" :isCustom.sync="isCustom" :selectedQtyInd.sync="selectedQtyInd" :customNumber.sync="customNumber" :customNumberUnitPrice="customNumberUnitPrice" :customNumberMoldPrice="customNumberMoldPrice" :customNumberPrice="customNumberPrice" @selectQtyList="selectQtyList" @filterCustomNumber="filterCustomNumber"></StepQty>
					</div>
					<QuickQuotePublicStep v-if="showPublicStepFn(item)" :id="item.customStepName + '_' + item.id" :morePriceData="morePriceData" :config="getStepConfig(item)" :stepData="item" :selectedData="selectedData" @selectQuoteParams="selectQuoteParams($event.item, $event.citem)"></QuickQuotePublicStep>
					<template v-if="uploadType === 1">
						<div class="step-item step-upload" :id="item.customStepName + '_' + item.id" v-if="showFn(item, 'upload')">
							<StepTitle :title="getTitle(item)" :step="getStep(item)"></StepTitle>
							<div class="step-item-params" :class="{ 'param-item-hasUpload': uploadArtworkList.length }">
								<template v-if="!uploadArtworkList.length">
									<div class="param-item" @click="openUpload">
										<div class="uploadWrap">
											<div flex center>
												<b class="icon-shangchuan uploadIcon"></b>
												<span class="brow">{{ lang.browse }}</span>
											</div>
											<div slot="content">
												<span style="color: #b3b3b3">{{ lang.maxFile }}</span>
												<el-tooltip popper-class="cusToolTip" effect="light">
													<div slot="content">
														<div>{{ item.tips }}</div>
													</div>
													<b class="icon-wenhao" @click.stop style="color: var(--color-primary)"></b>
												</el-tooltip>
											</div>
										</div>
									</div>
								</template>
								<template v-else>
									<div class="param-item-hasUpload-left custom-scrollbar">
										<div class="upload-item" v-for="(item, index) in uploadArtworkList" :key="index">
											<span class="upload-name">{{ item.original_filename }}</span>
											<b class="icon-check" style="color: #0cbd5f"></b>
											<b class="icon-shanchu2" style="color: #b6b0b0" @click="deleteUpload(index)"></b>
										</div>
									</div>
									<div class="uploadWrap" @click="openUpload">
										<b class="icon-shangchuan uploadIcon"></b>
										<span class="brow">{{ lang.browse }}</span>
									</div>
								</template>
								<input type="file" ref="upload" :accept="acceptFileType" multiple @change="uploadPic" />
							</div>
						</div>
					</template>
					<template v-else-if="uploadType === 2">
						<div class="step-item step-upload2" :id="item.customStepName + '_' + item.id" v-if="showFn(item, 'upload')">
							<StepTitle :title="getTitle(item)" :step="getStep(item)"></StepTitle>
							<div class="step-item-params" :class="{ 'param-item-hasUpload': uploadArtworkList.length }">
								<template v-if="!uploadArtworkList.length">
									<div class="param-item" @click="openUpload">
										<span>{{ lang.uf }}</span>
										<el-tooltip popper-class="cusToolTip" effect="light">
											<div slot="content">
												<div>{{ lang.maxFile }}</div>
												<div>{{ item.tips }}</div>
											</div>
											<b style="margin-left: 4px" class="icon-wenhao" @click.stop></b>
										</el-tooltip>
									</div>
									<div class="param-item" :class="{ active: isLater }" @click="setIsLater(true)">
										<span>{{ lang.emailUsLater }}</span>
									</div>
								</template>
								<template v-else>
									<div class="param-item-hasUpload-left">
										<span class="upload-name">{{ uploadArtworkList[0].original_filename }}</span>
										<b class="icon-icon_Preview myIcon" @click.stop="previewImg(uploadArtworkList[0].secure_url)"></b>
										<b class="icon-check" style="color: #0cbd5f"></b>
										<b class="icon-shanchu2" style="color: #b6b0b0" @click="deleteUpload(0)"></b>
										<div v-click-outside="() => (showExtend = false)">
											<div class="icon" :class="{ active: showExtend }" style="color: #eb7e1a" @click="showExtend = !showExtend" v-if="uploadArtworkList.length > 1">
												<b class="icon-bps-xiaozhankai"></b>
											</div>
											<div class="extendUpload" v-show="showExtend">
												<div class="arrow"></div>
												<div class="uploadFileWrap">
													<div class="upload-item" v-for="(item, index) in uploadArtworkList" :key="index">
														<span class="upload-name">{{ item.original_filename }}</span>
														<b class="icon-icon_Preview myIcon" @click.stop="previewImg(item.secure_url)"></b>
														<b class="icon-check" style="color: #0cbd5f"></b>
														<b class="icon-shanchu2" style="color: #b6b0b0" @click="deleteUpload(index)"></b>
													</div>
												</div>
											</div>
										</div>
									</div>
									<div class="uploadWrap" @click="openUpload">
										<b class="icon-shangchuan uploadIcon"></b>
										<span class="brow">{{ lang.browse }}</span>
										<el-tooltip popper-class="cusToolTip" effect="light">
											<div slot="content">
												<div>{{ lang.maxFile }}</div>
												<div>{{ item.tips }}</div>
											</div>
											<b class="icon-wenhao" @click.stop></b>
										</el-tooltip>
									</div>
								</template>
								<input type="file" ref="upload" :accept="acceptFileType" multiple @change="uploadPic" />
							</div>
						</div>
					</template>
					<div class="step-item step-time" :id="item.customStepName + '_' + item.id" v-if="showFn(item, 'discount')">
						<StepTitle :title="getTitle(item)" :step="getStep(item)"></StepTitle>
						<div class="step-time-tip">{{lang.pleaseNote}}</div>
						<div class="step-item-params">
							<div
								class="param-item-wrap"
								v-for="citem in getNewDiscountList(item)"
								:key="citem.id"
								:class="{
									active: hasId(citem.id, selectedData[item.paramName]),
								}"
								@click="selectQuoteParams(item, citem)"
							>
								<div class="param-item">
									<DiscountText :itemData="citem"></DiscountText>
								</div>
								<div class="des">{{ citem.alias }}</div>
							</div>
						</div>
					</div>
				</template>
			</div>
			<div class="freeTipWrap" v-show="currentStep === 1">
				<NewFreeTip :freeTipList="freeTipList"></NewFreeTip>
				<div class="nextBtn" :class="{ 'not-visibility': !showSelected }">
					<button primary :disabled="customQty <= 0" @click="nextStep" :title="lang.continue">{{ lang.continue }}&nbsp;&nbsp;<b class="icon-xiayige"></b></button>
				</div>
			</div>
			<div class="quoteBoxFooter" v-show="currentStep > 1">
				<div class="price">
					<span>{{ lang.quantity }}: {{ customQty }} Pcs</span>
                    <span>
                      {{lang.setUpFee}}: <CCYRate :price="priceInfo.setupCharge" class="setup-fee"></CCYRate>
                    </span>
					<span
						>{{ lang.subtotal }}:
						<CCYRate :price="finalPrice" class="finalPrice"></CCYRate>
					</span>
				</div>
				<button primary @click="addCart" v-if="currentStep == maxStep" :title="lang.addToCart">{{ lang.addToCart }}&nbsp;&nbsp;<b class="icon-xiayige"></b></button>
				<button primary @click="nextStep" v-if="currentStep > 1 && currentStep < maxStep" :title="lang.continue">{{ lang.continue }}&nbsp;&nbsp;<b class="icon-xiayige"></b></button>
			</div>
			<FinishedDialog :tipText="tipText" :customQty="customQty" :visible.sync="showFinishedDialog" :showFinishedConfirmDialog="showFinishedConfirmDialog" :title="finishedDialogTitle" :list="finishedList" @confirmFinished="confirmFinished" @confirmYes="confirmYes"></FinishedDialog>
			<TemplateDialog :visible.sync="showTemplateDialog" :templateParams="templateParams" :selectedData="selectedData" @selectQuoteParams="selectQuoteParams($event.parentItem, $event.item)"></TemplateDialog>
		</template>
	</div>
</template>

<script>
import {calculate, calculateAll, getInfo, getPriceData, otoAddCart} from "~/api/pins";
import { getCateParamRelationByCateId } from "~/api/web";
import CustomCircle from "~/components/Quote/customCircle.vue";
import CCYRate from "~/components/CCYRate.vue";
import { debounce, deepClone, isImageType, scrollToViewTop } from "~/utils/utils";
import { uploadFile } from "~/utils/oss";
import "@/plugins/element";
import { acceptFileType, checkFile } from "@/utils/validate";
import DiscountText from "@/components/modal/Quote/QuoteComponents/DicountText.vue";
import StepBar from "@/components/modal/Quote/QuoteComponents/StepBar.vue";
import StepSize from "@/components/modal/Quote/QuoteComponents/StepSize.vue";
import StepTitle from "@/components/modal/Quote/QuoteComponents/StepTitle.vue";
import StepQty from "@/components/modal/Quote/QuoteComponents/StepQty.vue";
import FreeTip from "@/components/modal/Quote/QuoteComponents/FreeTip.vue";
import FinishedDialog from "@/components/Quote/QuickQuote/FinishedDialog.vue";
import TemplateDialog from "@/components/Quote/QuickQuote/TemplateDialog.vue";
import { getQuickQuoteConfigByPidAndCateId } from "assets/js/quickQuoteConfig";
import { findSelectDiscount, getIsSmallQty, getQuoteTime } from "@/assets/js/QuotePublic";
import PriceText from "@/components/Quote/PriceText.vue";
import QuickQuotePublicStep from "@/components/modal/Quote/QuoteComponents/QuickQuotePublicStep.vue";
import NewFreeTip from "@/components/modal/Quote/QuoteComponents/NewFreeTip.vue";
import {getAllBanChoices} from "@/api/common/quote";

const setIndex = (data) => {
	if (!data) {
		return false;
	}
	let index = 0;
	data.forEach((item) => {
		if (item) {
			if (!item.noShowDetail) {
				index += 1;
			}
			item.customIndex = index;
		}
	});
	return data;
};
const sortByKey = (array, key) => {
	return array.sort(function (a, b) {
		let x = a.priceInfo[key]; //如果要从大到小,把x,y互换就好
		let y = b.priceInfo[key];
		return x < y ? -1 : x > y ? 1 : 0;
	});
};

const getMyCustomStepName = (item) => {
	let paramName = item.paramName,
		type = item.paramType;
	let map = {
		qtyNameArr: {
			value: ["qty", "Quantity"],
			alias: "qty",
		},
		uploadNameArr: {
			value: ["Upload Artwork & Comments", "Add Logo"],
			alias: "upload",
		},
	};
	if (type === "SIZE") {
		return "size";
	}
	if (type === "DISCOUNT") {
		return "discount";
	}
	let name = paramName;
	for (let i in map) {
		if (map[i].value.includes(paramName)) {
			name = map[i].alias;
			break;
		}
	}
	return name;
};
const parseJSON = (str) => {
	return str
		? JSON.parse(str)
		: [
				{
					url: "",
				},
		  ];
};
const addCustomProperty = function (data) {
	let handle = (list) => {
		for (let i = 0; i < list.length; i++) {
			let item = list[i];
			item.remark = "";
			item.noShowDetail = false;
			item.isHidden = false; //不可选属性
			item.files = [];
			item.inputNum = undefined;
			item.imageJson = parseJSON(item.imageJson);
			if (item.childList && item.childList.length) {
				handle(item.childList);
			}
			item.customStepName = getMyCustomStepName(item);
			//特殊处理
			if (item.paramName === "Upload Artwork & Comments") {
				item.alias = "Upload Your Artwork";
			}
			if ((this.pid == 240 || this.pid == 631) && item.paramName === "Select Shapes") {
				item.noShowDetail = true;
			}
			if ((this.pid == 312 || this.pid == 627) && item.paramName === "Medal Template") {
				item.noShowDetail = true;
			}
		}
	};
	handle(data);
	return data;
};
export default {
	props: {
		pid: {
			type: [Number, String],
			default: 312,
		},
		cateId: {
			type: [Number, String],
			default: 390,
		},
	},
	data() {
		const config = getQuickQuoteConfigByPidAndCateId.call(this, this.pid, this.cateId);
		return {
            morePriceData:{},
            compareParentFlag: false,
            // 不可选参数列表
            canNotParamComposeList: [],
            noChoiceData: [],
            // 不需要被过滤的步骤 paramName
            noFilterKey: ["Lanyard Style", "Select Metal Color"],
			isIframe: false,
			animateUp: false,
			animateDown: false,
			showSelected: true,
			tipText: "exceeds",
			hideSizeHelp: true,
			maxStep: 3,
			uploadType: 1,
			allStepConfig: {},
			isLater: false,
			acceptFileType,
			isMixed: false,
			showQuote: false,
			finishedDialogTitle: "",
			showTemplateDialog: false,
			showFinishedDialog: false,
			showFinishedConfirmDialog: false,
			finishedList: [],
			showExtend: false,
			uploadArtworkList: [],
			customNumber: "",
			selectedParamsValueParent: {},
			selectedParamsValue: {},
			isCustom: false,
			isDs: 0,
			isFastQuote: 1,
			loadAddCart: false,
			debounceCalcPrice: null,
			stepName: "",
			cateInfo: {},
			generalData: [],
			qtyList: [],
			priceInfo: {
				isSmallWeight: 1,
			},
			selectedData: {},
			selectedQtyInd: -1,
			currentStep: 1,
			...config,
		};
	},
	watch: {
        noChoiceData: {
            handler(newValue, oldValue) {
                const handFilter = (newV, obj) => {
                    obj.childList = obj.childList.map((y) => {
                        y.isHidden = 0;
                        newV.forEach((z) => {
                            if (this.compareParentFlag) {
                                if (y.id == z) {
                                    if (y.paramName == this.selectedData[obj.paramName]?.paramName) {
                                        this.selectedData[obj.paramName] = null;
                                    }
                                    y.isHidden = 1;
                                }
                            } else {
                                if (y.priceInfo?.id == z) {
                                    if (y.paramName == this.selectedData[obj.paramName]?.paramName) {
                                        this.selectedData[obj.paramName] = null;
                                    }
                                    y.isHidden = 1;
                                }
                            }
                        });
                        return y;
                    });
                    if (obj.childList && obj.childList.length > 0) {
                        obj.childList.forEach((y) => {
                            if (y.childList && y.childList.length > 0) {
                                handFilter(newValue, y);
                            }
                        });
                    }
                    return obj;
                };
                this.generalData.forEach((x) => {
                    if (x instanceof Array) {
                        x.forEach((xx) => {
                            xx = handFilter(newValue, xx);
                        });
                    } else {
                        if (!this.checkNoFilter(x.paramName)) {
                            x = handFilter(newValue, x);
                            if (x.childList && x.childList.length > 0) {
                                x.childList.forEach((y) => {
                                    if (y.childList && y.childList.length > 0) {
                                        handFilter(newValue, y);
                                    }
                                });
                            }
                        }
                    }
                });
            },
        },
		selectedData: {
			handler() {
				if (!this.showQuote) {
					return false;
				}
				this.debounceCalcPrice();
			},
			deep: true,
		},
	},
	components: {
		NewFreeTip,
		QuickQuotePublicStep,
		PriceText,
		TemplateDialog,
		FinishedDialog,
		FreeTip,
		StepQty,
		StepTitle,
		StepSize,
		StepBar,
		DiscountText,
		CustomCircle,
		CCYRate,
	},
	computed: {
		selectedShape() {
			let shapeMap = {
				240: "Select Shapes",
				631: "Select Shapes",
				312: "Medal Template",
				627: "Medal Template",
			};
			let findShape = this.selectedData[shapeMap[this.pid]];
			if (findShape && findShape.length) {
				return findShape[0]["alias"];
			}
		},
		shapeKey() {
			let shapeKey = {
				240: this.lang.selectedShape2,
				631: this.lang.selectedShape2,
				312: this.lang.selectedTemplate,
				627: this.lang.selectedTemplate,
			};
			return shapeKey[this.pid];
		},
        filterShowGeneralData() {
            const filterChildList = (childList) => {
                return childList
                        .map((y) => {
                            if (y.childList && y.childList.length > 0) {
                                return {
                                    ...y,
                                    childList: filterChildList(y.childList.filter((a) => !a.isHidden)),
                                };
                            }
                            return y;
                        })
                        .filter((y) => !y.isHidden);
            };
            return this.generalData.map((x) => {
                if (x instanceof Array) {
                    return x.map((xx) => {
                        return {
                            ...xx,
                            childList: xx?.childList.filter((y) => !y.isHidden),
                        };
                    });
                } else {
                    return {
                        ...x,
                        childList: filterChildList(x?.childList),
                    };
                }
            });
        },
		showStepBar() {
			return this.device !== "mb";
		},
		templateParams() {
			return this.generalData.find((item) => item.customStepName === "template") || {};
		},
		finalPrice() {
			return this.priceInfo.totalPrice;
		},
		customNumberPrice() {
			return this.isCustom && this.customNumber ? this.priceInfo.totalPrice : 0;
		},
		customNumberUnitPrice() {
			return this.isCustom && this.customNumber ? this.priceInfo.foundationUnitPrice : 0;
		},
		customNumberMoldPrice() {
			return this.isCustom && this.customNumber ? this.priceInfo.toolingCharge : 0;
		},
		proId() {
			if (this.previewMode) {
				return 148;
			} else {
				return this.$store.state.proId;
			}
		},
		projectName() {
			if (this.previewMode) {
				return "o2o-site-style";
			} else {
				return this.$store.state.proName;
			}
		},
		previewMode() {
			return process.env.VUE_APP_MODE === "Preview";
		},
		proType() {
			return this.$store.state.proType;
		},
		lang() {
			return this.$store.getters.lang.quote || {};
		},
		langSemi() {
			return this.$store.getters.lang.semiCustom || {};
		},
		userUUID() {
			return this.$store.state.userUUID;
		},
		userId() {
			return this.$store.state.userInfo?.id;
		},
		isLogin() {
			return this.$store.getters.isLogin;
		},
		userInfo() {
			return this.$store.state.userInfo;
		},
		device() {
			return this.$store.state.device;
		},
		customQty() {
			if (this.isCustom) {
				return parseInt(this.customNumber) || 0;
			} else {
				return parseInt((this.qtyList && this.qtyList.length && this.qtyList[this.selectedQtyInd]?.totalQuantity) || 0);
			}
		},
	},
	methods: {
        checkNoFilter(key) {
            return this.noFilterKey.includes(key);
        },
        filterStepsFun() {
            return new Promise((resolve, reject) => {
                let tempArr = [],
                        result = [];
                // 将已选参数加入tempArr数组
                for (let key in this.selectedData) {
                    const item = this.selectedData[key];
                    if (item && item instanceof Array) {
                        item.forEach((x) => {
                            // if 处理数据结构特别的lanyard报价
                            if (key == "Badge Reels" || key == "Lanyard Badge Holder Options") {
                                if (x.childList && x.childList.length > 0) {
                                    x.childList.forEach((itemC) => {
                                        itemC && itemC?.quantity > 0 && tempArr.push(itemC);
                                    });
                                }
                            } else if (key == "Select Your Card") {
                                x.selectedObj && tempArr.push(x.selectedObj);
                            } else if (key == "Lanyard Style") {
                                // lanyard分类不加入不可选（解决cateId和参数Id相同的问题)
                            } else {
                                x && tempArr.push(x);
                            }
                        });
                    } else {
                        item && tempArr.push(item);
                    }
                }
                let list = [];
                for (let i = 0; i < tempArr.length; i++) {
                    if (!tempArr[i].priceInfo) continue;
                    if (this.compareParentFlag) {
                        list.push(tempArr[i]?.id);
                    } else {
                        list.push(tempArr[i]?.priceInfo?.id);
                    }
                }
                for (let i = 0; i < this.canNotParamComposeList.length; i++) {
                    let cArr = this.compareParentFlag ? this.canNotParamComposeList[i].quoteParamEntities.map((x) => x.id) : this.canNotParamComposeList[i].canNotParamCompose.split(","),
                            bArr = cArr;
                    if (bArr.length == 1) {
                        result = result.concat(bArr);
                        break;
                    } else {
                        let data = 0;
                        let total = 0;
                        for (let j = 0; j < cArr.length; j++) {
                            if (list.indexOf(Number(bArr[j])) == -1) {
                                total++;
                                if (data == 0) {
                                    data = Number(bArr[j]);
                                }
                            }
                        }
                        if (total == 1 && data != 0) {
                            result.push(data);
                        }
                    }
                }
                resolve(result);
            });
        },
		startAnimation() {
			this.animateUp = true;
			setTimeout(() => {
				this.animateUp = false;
				this.animateDown = true;
			}, 100);
			setTimeout(() => {
				this.animateDown = false;
			}, 200);
		},
		initShowSelected() {
			this.showSelected = true;
		},
		initQuote(data) {
			this.currentStep = 1;
			this.activeStep = 1;
			let map = {
				240: "Select Shapes",
				631: "Select Shapes",
				312: "Medal Template",
				627: "Medal Template",
			};
			let key = map[this.pid] || "Coin Edge";
			let edge = this.generalData.find((item) => {
				return item.paramName === key;
			});
			if (edge) {
				let findEdge = edge.childList.find((item) => {
					return item.id == data.shapeId;
				});
				if (findEdge) {
					this.selectQuoteParams(edge, findEdge);
					// this.selectedData[key] = [findEdge]
				}
			}
		},
		zoomPic(img) {
			this.$viewerApi({
				images: [img],
			});
		},
		getImgSrc(citem) {
			return citem.imageJson[1] ? citem.imageJson[1]?.url : citem.imageJson[0]?.url;
		},
		previewImg(img) {
			if (isImageType(img)) {
				this.$viewerApi({
					images: [img],
				});
			} else {
				window.open(img);
			}
		},
		setIsLater(bool) {
			this.isLater = bool;
		},
		getStyle(item) {
			if (this.allStepConfig[item.customStepName]?.columns) {
				return `grid-template-columns:repeat(${this.allStepConfig[item.customStepName]?.columns[this.device]},1fr)`;
			}
		},
		getStepConfig(item) {
			return this.allStepConfig[item.customStepName] || {};
		},
		showPublicStepFn(item) {
			let notShowArr = ["qty", "size", "upload", "discount"];
			return !notShowArr.includes(item.customStepName) && this.currentStep === this.allStepConfig[item.customStepName]?.showStep && !item.noShowDetail;
		},
		showFn(item, type) {
			return item.customStepName === type && this.currentStep === this.allStepConfig[item.customStepName]?.showStep && !item.noShowDetail;
		},
		getTitle(item) {
			if (item.customStepName === "upload") {
				return item.alias;
			} else {
				return this.lang.Select + " " + item.alias;
			}
		},
		getStep(item) {
			return this.lang.step + " " + item.customIndex;
		},
		changeCustomQty() {
			if (this.isMixed) {
				this.showDialog();
			}
		},
		closeDialog() {
			this.showFinishedDialog = false;
		},
		confirmYes() {
			let list = this.finishedList,
				qty = 0;
			list.forEach((item) => {
				qty += parseInt(item.inputNum) || 0;
			});
			if (qty > 0) {
				this.isCustom = true;
				this.selectedQtyInd = -1;
				this.customNumber = qty;
				this.showFinishedDialog = false;
				this.showFinishedConfirmDialog = false;
				this.debounceCalcPrice();
			}
		},
		confirmFinished() {
			let totalQty = this.customQty,
				list = this.finishedList,
				qty = 0;
			list.forEach((item) => {
				qty += parseInt(item.inputNum) || 0;
			});
			if (qty !== totalQty) {
				this.showFinishedConfirmDialog = true;
				if (qty > totalQty) {
					this.tipText = "exceeds";
				} else {
					this.tipText = "is less than";
				}
				return false;
			}
			this.isCustom = true;
			this.selectedQtyInd = -1;
			this.customNumber = qty;
			this.debounceCalcPrice();
			this.showFinishedDialog = false;
		},
		clearField(e = "DISCOUNT") {
			let findDiscount = this.generalData.find((item) => item.paramType === e);
			if (findDiscount) {
				let name = findDiscount.paramName;
				this.selectedData[name] = [];
			}
		},
		getNewDiscountList(itemData) {
			let result = getQuoteTime(itemData.childList, this.priceInfo, this.proType),
				originShowSmallPrice = this.$store.state.showSmallPrice;
			this.$store.commit("setShowSmallPrice", result.newShowSmallPrice);
			if (result.newShowSmallPrice !== originShowSmallPrice) {
				this.clearField("DISCOUNT");
			}
			return result.arr;
		},
		openUpload() {
			this.$refs.upload[0].click();
		},
		deleteUpload(ind) {
			this.uploadArtworkList.splice(ind, 1);
			if (this.uploadArtworkList.length <= 1) {
				this.showExtend = false;
			}
		},
		prevStep() {
			this.currentStep -= 1;
		},
		nextStep() {
			this.currentStep += 1;
		},
		//判断是否选中
		hasId(id, arr) {
			if (!arr) {
				return false;
			}
			return !!arr.find((item) => {
				return item.id === id;
			});
		},
		showDialog() {
			//显示数量弹窗
			let findColor = this.generalData.find((item) => item.paramType === "COLOR");
			let findItem = findColor.childList.find((item) => item.paramName === "Mixed");
			this.finishedDialogTitle = findItem.alias;
			this.showFinishedDialog = true;
			this.finishedList = findColor.childList;
		},

		selectQtyList() {
			this.debounceCalcPrice();
			this.handleWeightDiscount();
		},
		filterCustomNumber() {
			this.debounceCalcPrice();
			this.handleWeightDiscount();
		},
		selectQty() {
			this.isCustom = true;
			this.selectedQtyInd = -1;
		},

		handleWeightDiscount() {
			//获取算价格参数
			let priceParam = this.getPriceParam();
			//获取折扣参数列表
			let discountList = this.generalData
				.find((item) => {
					return item.paramType === "DISCOUNT";
				})
				?.childList.filter((item) => item.priceInfo.priceType === 10);
			if (!discountList?.length) {
				return;
			}
			discountList.forEach((item) => {
				if (priceParam.sizeId && priceParam.cateId) {
					if (priceParam.sizeId && priceParam.cateId && (priceParam.quantity || priceParam.qtyDetailDTOS?.length)) {
						calculate(Object.assign({}, priceParam, { discountId: item.priceInfo.id })).then((res) => {
							this.$set(item.priceInfo, "customWeightPrice", res.data.discountPrice);
						});
					}
				}
			});
		},

		//根据paramName清空字段
		//参数1   Plating/Finish
		clearFieldName(e = "Plating/Finish") {
			let findDiscount = this.generalData.find((item) => item.paramName === e);
			if (findDiscount) {
				let name = findDiscount.paramName;
				this.selectedData[name] = [];
			}
		},

		//参数选中事件
		async selectQuoteParams(item, citem) {
			this.selectedParamsValueParent = item;
			this.selectedParamsValue = citem;
			this.$set(this.selectedData, item.paramName, [citem]);
			if (item.customStepName === "Plating" && (this.pid == 312 || this.pid == 627)) {
				if (citem.paramName === "Mixed") {
					let findItem = item.childList.find((item) => item.paramName === "Mixed");
					this.finishedDialogTitle = findItem.alias;
					this.showFinishedDialog = true;
					this.finishedList = item.childList;
					this.isMixed = true;
				} else {
					this.isMixed = false;
				}
			}
			if (item.customStepName === "size") {
				window.quoteSizeValue = citem.paramCode;
			}
            //Template Shape SUNamel Pins 选中形状要取消选中尺寸
            //Rush Printed Keychains 选中形状要取消选中尺寸
            if((this.pid == 240 || this.pid == 631) && item.paramName === "Select Shapes"){
                this.clearFieldName("Custom Shape Pins Size");
                this.clearFieldName("Keychain Size")
            }
            try {
                //不可选参数过滤
                this.filterStepsFun().then((res) => {
                    this.noChoiceData = res;
                    /**
                     * 过滤后的特殊处理
                     *  */
                    //template-SUNamel-pins报价特殊处理
                    if ((this.pid == 240 || this.pid == 631) && item.paramName === "Select Shapes") {
                        const sizeChild = this.generalData.find((x) => x.paramType === "SIZE");
                        const filterSizeChild = sizeChild.childList.filter((x) => !this.noChoiceData.includes(x.priceInfo.id));
                        filterSizeChild.length && this.selectQuoteParams(sizeChild, filterSizeChild[0], true);
                    }
                });
            }catch (e) {
                console.log(e)
            }
			this.handleWeightDiscount();
		},
		replayUpload() {
			this.openUpload();
			this.$store.commit("setSizeDialog", false);
		},
		uploadPic(event, type = "upload") {
			this.$gl.show();
			let files = type === "upload" ? event.target.files : event;
			let fileResult = checkFile(Array.from(files), this.acceptFileType, 80);
			if (!fileResult) {
				this.$toast.error("File type error");
				this.$gl.hide();
				return false;
			}
			if (!fileResult.nomalSize.length) {
				this.$gl.hide();
				this.$store.commit("setSizeDialog", true);
				this.$store.commit("setInputRefName", "replayUpload");
				this.$store.commit("setOverSizeList", fileResult.overSize);
				this.$refs.upload[0].value = "";
				return false;
			}
			let uploadPromises = [];
			fileResult.nomalSize.forEach((file) => {
				let promise = uploadFile(file).then((res) => {
					this.uploadArtworkList.push({
						original_filename: file.name,
						secure_url: res,
						size: (file.size / 1024).toFixed(1),
					});
				});
				uploadPromises.push(promise);
			});
			Promise.all(uploadPromises).then(() => {
				this.$refs.upload[0].value = "";
				if (fileResult.overSize.length > 0) {
					this.$store.commit("setSizeDialog", true);
					this.$store.commit("setInputRefName", "replayUpload");
					this.$store.commit("setOverSizeList", fileResult.overSize);
				}
				this.$gl.hide();
			});
		},
		getQuoteParam(type = "inquiry") {
			let selectedData = deepClone(this.selectedData),
				finaData = [],
				generalData = deepClone(this.generalData);
			for (let i = 0; i < generalData.length; i++) {
				let item = generalData[i];
				if (item.customStepName === "qty") {
					// item.childList = [];
					// finaData.push(item);
					continue;
				}
				if (item.customStepName === "upload") {
					item.childList = [];
					item.later = this.isLater;
					finaData.push(item);
					continue;
				}
				if (item.customStepName === "color") {
					if (selectedData[item.paramName][0].paramName === "Mixed") {
						item.childList.pop();
						item.childList.forEach((c) => {
							c.childList = [];
							c.quantity = c.inputNum;
						});
						finaData.push(item);
						continue;
					} else {
						if (selectedData[item.paramName] && selectedData[item.paramName].length) {
							selectedData[item.paramName].forEach((c) => {
								c.childList = [];
								c.quantity = this.customQty;
								if (!c.giftQuantity) {
									c.giftQuantity = 0;
								}
							});
						}
						item.childList = selectedData[item.paramName] || [];
						finaData.push(item);
						continue;
					}
				}
				//将选中参数里面的childlist置空
				if (selectedData[item.paramName] && selectedData[item.paramName].length) {
					selectedData[item.paramName].forEach((c) => {
						c.childList = [];
						c.quantity = c.inputNum;
						if (!c.giftQuantity) {
							c.giftQuantity = 0;
						}
					});
				}
				item.childList = selectedData[item.paramName] || [];
				finaData.push(item);
			}
			let files = [];
			let artworkList = this.uploadArtworkList;
			if (artworkList.length > 0) {
				artworkList.forEach((item) => {
					item.secure_url && files.push(item.secure_url);
				});
			}
			return {
				classificationData: this.cateInfo,
				finaData: finaData,
				fontData: {
					fontImgCustom: files,
					comments: this.remark,
				},
				designCanvas: [],
			};
		},
		getPriceParam(qty) {
			const data = {
				cateId: this.cateId,
				discountId: "",
				packingIdList: [],
				paramIdList: [],
				projectName: this.projectName,
				quantity: "",
				sizeId: "",
				upgradesQtyDTO: [],
				qtyDetailDTOS: [],
			};
			let generalData = this.generalData,
				selectedData = this.selectedData;
			for (let i = 0; i < generalData.length; i++) {
				let item = generalData[i];
				let paramName = item.paramName;
				let selectedVal = selectedData[paramName];
				if (this.pid != 623 && this.pid != 627 && this.pid != 312) {
					if (item.customStepName === "qty") {
						data.quantity = qty || Number(this.customQty);
						continue;
					}
				}
				if (item.customStepName === "upload") {
					continue;
				}
				if (!selectedVal) {
					continue;
				}
				selectedVal.forEach((citem) => {
					let paramType = citem.paramType;
					switch (paramType) {
						case "NORMAL":
							data.paramIdList.push(citem?.priceInfo?.id);
							break;
						case "DISCOUNT":
							data.discountId = citem?.priceInfo?.id;
							break;
						case "SIZE":
							data.sizeId = citem?.priceInfo?.id;
							break;
						case "COLOR":
							if (citem.paramName === "Mixed" && (this.pid == 623 || this.pid == 627)) {
								let findColor = generalData.find((item) => item.paramType === "COLOR");
								findColor?.childList.forEach((colorItem) => {
									if (colorItem.paramName !== "Mixed" && colorItem.inputNum > 0) {
										data.qtyDetailDTOS.push({
											quantity: colorItem.inputNum,
											paramType: "COLOR",
											paramId: colorItem?.priceInfo?.id,
											paramValue: "",
										});
									}
								});
							} else {
								data.qtyDetailDTOS.push({
									quantity: qty || Number(this.customQty),
									paramType: "COLOR",
									paramId: citem?.priceInfo?.id,
									paramValue: "",
								});
							}
							break;
						default:
							break;
					}
				});
			}
			if (!data.qtyDetailDTOS.length) {
				delete data.qtyDetailDTOS;
			}
			return data;
		},
		calcPrice() {
			let priceParam = this.getPriceParam();
			if (priceParam.sizeId && priceParam.cateId) {
				calculateAll(priceParam).then((res) => {
					let qtyList = res.data.filter((item) => item.isFastQuote);
					let prevFoundationUnitPrice = qtyList[0]?.foundationUnitPrice;
					qtyList.forEach((item, index) => {
						if (index > 0) {
							item.save = (((prevFoundationUnitPrice - item.foundationUnitPrice) / prevFoundationUnitPrice) * 100).toFixed(0);
						}
					});
					this.qtyList = qtyList;
				});
			}
			if (priceParam.sizeId && priceParam.cateId && (priceParam.quantity || priceParam.qtyDetailDTOS?.length)) {
				calculate(priceParam).then((res) => {
					this.priceInfo = res.data;
				});
			}
		},
		checkParams() {
			let verify = true,
				stepName = "",
				selectedData = this.selectedData,
				generateData = this.generalData;
			for (let i = 0; i < generateData.length; i++) {
				let item = generateData[i];
				//参数名称
				let paramName = item.paramName;
				//选中的参数值
				let selectedVal = selectedData[paramName];
				if (item.customStepName === "upload") {
					if (!this.uploadArtworkList.length && !this.isLater) {
						stepName = item.customStepName;
						verify = false;
						break;
					}
				} else if (item.customStepName === "qty") {
					if (!this.customQty || this.customQty <= 0) {
						stepName = item.customStepName;
						verify = false;
						break;
					}
				} else if (item.customStepName === "color") {
					let temp = selectedVal;
					if (!temp || !temp.length) {
						stepName = item.customStepName;
						verify = false;
						break;
					}
					if (temp[0].paramName === "Mixed") {
						let qty = 0,
							totalQty = this.customQty;
						let findColor = generateData.find((item) => item.paramType === "COLOR");
						findColor.childList.forEach((item) => {
							qty += parseInt(item.inputNum) || 0;
						});
						if (qty !== totalQty) {
							this.$toast.error(this.lang.qtyTip);
							stepName = item.customStepName;
							verify = false;
							//显示数量弹窗
							let findItem = findColor.childList.find((item) => item.paramName === "Mixed");
							this.finishedDialogTitle = findItem.alias;
							this.showFinishedDialog = true;
							this.finishedList = findColor.childList;
							break;
						}
					}
				} else {
					if (!selectedVal || selectedVal.length === 0) {
						stepName = item.customStepName;
						verify = false;
						break;
					}
				}
			}
			this.stepName = stepName;
			return verify;
		},
		async addCart() {
			if (!this.checkParams()) {
				this.$toast.error(this.lang.mapMessage[this.stepName] || this.lang.paramTip);
				scrollToViewTop(document.getElementById(`${this.stepName}`));
				return false;
			}
			//如果是临时项目
			if (this.$store.state.isTemporary) {
				this.$store.commit("setMask", "temporary");
				return false;
			}
			if (this.loadAddCart) {
				return false;
			}
			this.loadAddCart = true;
			let quoteParam = this.getQuoteParam("cart");
			let priceParam = this.getPriceParam();
			let data = {
				proId: this.proId,
				uuid: this.isLogin ? null : this.userUUID,
				userId: this.isLogin ? this.userId : null,
				buyType: 7,
				quoteCateId: this.pid,
				isMobile: this.device === "mb" ? 1 : 0,
				quoteCateChildId: priceParam.cateId,
				quantity: this.customQty,
				quotePriceParam: JSON.stringify(priceParam),
				quoteParam: JSON.stringify(quoteParam),
				isDs: this.isDs,
				isFastQuote: this.isFastQuote,
				isSmallQty: getIsSmallQty(findSelectDiscount(this.selectedData), this.$store.state.enableTurnaroundTimeCheck),
			};
			otoAddCart(data)
				.then((res) => {
					if (this.isIframe) {
						let targetWindow = window.opener || window.parent; // 目标窗口，可以是父窗口或打开当前窗口的窗口
						targetWindow.postMessage(
							{
								type: "toCart",
							},
							window.origin
						); // 发送消息
					} else {
						this.$router.push({
							path: "/cart",
						});
					}
				})
				.finally(() => {
					setTimeout(() => {
						this.loadAddCart = false;
					}, 1000);
				});
		},
        //价格分层参数
        getPriceData() {
            getPriceData({ buyType: 7, productCateId: this.pid }).then((res) => {
                this.morePriceData = res.data
            });
        },
	},
	async created() {
		this.debounceCalcPrice = debounce(this.calcPrice, 300);
	},
	async mounted() {
		if (this.$store.getters.isManage) {
			return false;
		}
		this.isIframe = !!this.$route.query.type;
		try {
			if ([312, 240, 631, 627].includes(this.pid)) {
				this.showSelected = false;
			}
			this.$Bus.$on("replayUpload", this.replayUpload);
            this.getPriceData();
			let result = await Promise.all([
				getInfo({ id: this.cateId }),
				getCateParamRelationByCateId({
					cateId: this.cateId,
					isFastQuote: 1,
				}),
                getAllBanChoices({ cateId: this.cateId })
			]);
			this.cateInfo = result[0]?.data || {};
			let data1 = result[1]?.data;
            this.canNotParamComposeList = result[2].data;
            await this.filterStepsFun().then((res) => {
                this.noChoiceData = res;
            });
			let selectedData = {},
				isLater = false,
				selectedQtyInd = -1,
				selectedQty = 0,
				generalData = setIndex(sortByKey(addCustomProperty.call(this, data1), "fastQuoteStepIndex"));
			//默认选中参数
			if (generalData?.length) {
				isLater = true;
				generalData.forEach((item) => {
					let customStepName = item.customStepName;
					if (customStepName !== "qty" && customStepName !== "upload") {
						let findDefault = item.childList.find((c) => c.priceInfo.isFastQuoteSelected);
						if (findDefault) {
							selectedData[item.paramName] = [findDefault];
							//选中尺寸下的默认数量
							if (customStepName === "size") {
								try {
									let priceInfo = findDefault.priceInfo;
									let increasePrice = JSON.parse(priceInfo.increasePrice).filter((item) => item.isFastQuote);
									let defaultQtyIndex = increasePrice.findIndex((q) => q.isFastQuoteSelected);
									if (defaultQtyIndex > -1) {
										selectedQtyInd = defaultQtyIndex;
										selectedQty = increasePrice[defaultQtyIndex].quantity;
									}
								} catch (e) {}
							}
						}
					}
				});
			}
			this.selectedData = selectedData;
			this.generalData = generalData;
			this.isLater = false;
			this.selectedQtyInd = selectedQtyInd;
			let priceParam = this.getPriceParam(selectedQty),
				priceResult;
			priceResult = await Promise.all([calculateAll(priceParam), calculate(priceParam)]);
			let qtyList = (priceResult && priceResult[0].data.filter((item) => item.isFastQuote)) || [];
			let prevFoundationUnitPrice = qtyList[0]?.foundationUnitPrice;
			qtyList.forEach((item, index) => {
				if (index > 0) {
					item.save = (((prevFoundationUnitPrice - item.foundationUnitPrice) / prevFoundationUnitPrice) * 100).toFixed(0);
				}
			});
			this.qtyList = qtyList;
			this.priceInfo = (priceResult && priceResult[1].data) || {};
			this.showQuote = true;
            this.handleWeightDiscount()
            try {
                //不可选参数过滤
                this.filterStepsFun().then((res) => {
                    this.noChoiceData = res;
                });
            }catch (e) {
                console.log(e)
            }
            this.$emit("loadComplete",true)
		} catch (e) {
			console.log(e);
		}
	},
};
</script>

<style lang="scss" scoped>
@mixin selectedStyle {
	border-color: $color-primary;
	&::after {
		content: "\e82c";
		position: absolute;
		width: 1.38em;
		height: 1.38em;
		line-height: 1.38em;
		right: 0;
		top: 0;
		border-bottom-left-radius: 50%;
		text-align: center;
		color: #ffffff;
		background-color: $color-primary;
		font-family: "modalicon";
	}

	@include respond-to(mb) {
		&::after {
			width: 1.25em;
			height: 1.25em;
			line-height: 1.25em;
		}
	}
}

.freeTipWrap {
	width: 90%;
	margin: 0 auto;
	@include respond-to(mb) {
		width: 100%;
	}
}

.icon-wenhao {
	font-size: 1.25em;
}

button[primary] {
	min-width: auto !important;
	height: 2.5em !important;
	background-color: $color-primary;
	font-size: 1.12em;
}

button .icon-xiayige {
	font-size: 0.75em;
}

button[disabled] {
	background: #e2e2e2;
}

.nextBtn button {
	width: 100%;
}

.nextBtn.not-visibility {
	visibility: hidden;
}

.quoteBoxFooter {
	flex-shrink: 0;
	overflow: hidden;
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 1em;
	background-color: #f6f6f6;
	margin: 20px -1.5em -1.5em;

	.price {
		flex: 1;
		@include respond-to(mb) {
			display: flex;
			flex-direction: column;
		}
	}

	::v-deep .finalPrice {
		font-weight: 700;
		color: $color-primary;
		font-size: 1.5em;
	}

	& > div:first-child span {
		margin-right: 2em;
	}

	@include respond-to(mb) {
		position: static;
		margin: 0 -1em -1.2em;
        padding: 0.8em;

		::v-deep .finalPrice {
			font-size: 1em;
		}
		& > div:first-child span {
			margin-right: 1em;
		}
	}
}

.quoteBox {
	overflow: hidden;
	position: relative;
	display: flex;
	flex-direction: column;
	min-width: 0;
	min-height: 42em;
	color: $text-primary;
	background: #ffffff;
	border-radius: 4px;
	padding: 1em;

	@include respond-to(mb) {
		min-height: auto;
		height: auto;
		padding: 1em 1.2em;
	}

	.quoteBoxHeader {
		display: flex;
		align-items: center;
		margin-bottom: 1em;

		.return {
			position: absolute;
			cursor: pointer;

			@include respond-to(mb) {
				position: static;
				margin-top: 0;
			}

			@media (any-hover: hover) {
				&:hover {
					font-weight: 700;
					color: $color-primary;
				}
			}

			b {
				vertical-align: 0;
				margin-right: 4px;
				@include respond-to(mb) {
					font-size: 1em;
					color: #757575;
				}
			}
		}
	}

	.stepList {
		flex: 1;
		display: grid;
		align-content: flex-start;
		grid-template-columns: 1fr 2.4fr;
		grid-template-areas: "a b";
		gap: 1em; /* 可选：设置行和列之间的间距 */

		@include respond-to(mb) {
			grid-template-columns: repeat(1, 1fr);
			grid-template-areas: none;
		}

		.step-item {
			font-size: 0.88em;
			@include respond-to(mb) {
				font-size: 1em;
				margin-bottom: 1em;
			}

			img {
				max-width: 100%;
				max-height: 100%;
				object-fit: contain;
				width: auto;
			}
		}
	}

	.step-size {
		min-width: 0;
		grid-area: a;

		.step-item-title .help {
			display: none;
		}

		@include respond-to(mb) {
			background-color: #fafafa;
			padding: 5px;
			grid-area: auto;
			margin-bottom: 0 !important;
		}
	}

	.step-qty {
		min-width: 0;
		grid-area: b;

		@include respond-to(mb) {
			background-color: #fafafa;
			padding: 5px;
			grid-area: auto;
		}
	}

	.step-upload {
		grid-column: 1 / span 2;

		.step-item-params {
			position: relative;
			display: flex;
			justify-content: center;
			align-items: center;
			height: 5.88em;
			width: 90%;
			background-color: #fafafa;

			@include respond-to(mb) {
				width: 100%;
				height: 5em;
			}

			input[type="file"] {
				position: absolute;
				clip: rect(0 0 0 0);
			}

			.uploadWrap {
				position: relative;
				display: flex;
				flex-direction: column;
				align-items: center;
				cursor: pointer;

				.brow {
					margin: 0 4px;
					color: $color-primary;
					text-decoration-line: underline;
				}

				.uploadIcon {
					margin-right: 0.2em;
					font-size: 1.7em;
					color: $color-primary;

					@include respond-to(mb) {
						font-size: 1.5em;
						margin-right: 0.4em;
					}
				}
			}

			.param-item {
				flex: 1;
				position: relative;
				display: flex;
				justify-content: center;
				align-items: center;
				height: 100%;
				border-radius: 6px;
				cursor: pointer;
				border: 1px solid #ccc;

				@include respond-to(mb) {
					padding: 0;
				}

				@media (any-hover: hover) {
					b:hover {
						color: $color-primary;
					}

					&:hover {
						border-color: $color-primary;
					}
				}
			}
		}

		.step-item-params.param-item-hasUpload {
			justify-content: space-between;
			padding: 0.2em;

			.param-item-hasUpload-left {
				overflow: auto;
				height: 100%;
				position: relative;
				padding: 0.2em;

				.icon {
					display: flex;
					justify-content: center;
					align-items: center;
					width: 24px;
					height: 24px;
					border-radius: 50%;
					margin: 0 6px;
					background: #ebebeb;

					b {
						color: #888888;
						font-size: 12px;
						transform: rotate(90deg);
					}
				}

				.icon.active {
					b {
						color: $color-primary;
					}
				}

				.upload-name {
					display: inline-block;
					overflow: hidden;
					text-overflow: ellipsis;
					width: 15em;
					white-space: nowrap;
					text-decoration: underline;

					@include respond-to(mb) {
						width: 100px;
					}
				}

				b {
					margin: 0 6px;
					cursor: pointer;
				}

				b.icon-Down {
					font-size: 12px;
				}
			}
		}
	}

	.step-upload2 {
		grid-column: 1 / span 2;

		.selfUpload {
			display: flex;
			align-items: center;

			.selfUploadIcon {
				margin-right: 0.6em;
				font-size: 1.4em;
				color: $color-primary;

				@include respond-to(mb) {
					font-size: 1.2em;
					margin-right: 0.4em;
				}
			}

			.uploadText {
				color: $color-primary;
				text-decoration: underline;
			}
		}

		.selfUploadIcon2 {
			margin-left: 0.6em;
			color: #666666;
			font-size: 1em;

			@include respond-to(mb) {
				margin-right: 0.4em;
			}

			@media (any-hover: hover) {
				&:hover {
					color: #666666 !important;
					border-color: #666666 !important;
				}
			}
		}

		.step-item-params {
			position: relative;
			display: flex;
			gap: 0.75em;

			input[type="file"] {
				position: absolute;
				clip: rect(0 0 0 0);
			}

			.uploadWrap {
				position: relative;
				display: flex;
				align-items: center;
				cursor: pointer;

				.brow {
					margin: 0 4px;
					color: $color-primary;
					text-decoration-line: underline;
				}

				.icon-wenhao {
					color: $color-primary;
					font-size: 1em;
				}

				.uploadIcon {
					margin-right: 0.2em;
					font-size: 1.7em;
					color: $color-primary;

					@include respond-to(mb) {
						font-size: 1.5em;
						margin-right: 0.4em;
					}
				}
			}

			.param-item {
				position: relative;
				display: flex;
				justify-content: center;
				align-items: center;
				width: 14.75em;
				height: 2.63em;
				border-radius: 6px;
				cursor: pointer;
				border: 1px solid #ccc;

				.icon-wenhao {
					color: $color-primary;
					font-size: 1em;
				}

				@include respond-to(mb) {
					padding: 0;
					border-radius: 0.5em;
				}

				@media (any-hover: hover) {
					b:hover {
						color: $color-primary;
					}

					&:hover {
						border-color: $color-primary;
					}
				}

				&.active {
					@include selectedStyle;
				}
			}
		}

		.step-item-params.param-item-hasUpload {
			justify-content: space-between;
			padding: 0.2em;

			.param-item-hasUpload-left {
				position: relative;
				display: flex;
				align-items: center;
				padding: 0.2em;

				.icon {
					display: flex;
					justify-content: center;
					align-items: center;
					width: 24px;
					height: 24px;
					border-radius: 50%;
					margin: 0 6px;
					background: #ebebeb;

					b {
						color: #888888;
						font-size: 12px;
						transform: rotate(90deg);
					}
				}

				.icon.active {
					b {
						color: $color-primary;
					}
				}

				.extendUpload {
					position: absolute;
					left: 0;
					right: 0;
					top: calc(100% + 10px);
					background: #fafafa;
					box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.2);
					border-radius: 6px;
					border: 1px solid $color-primary;
					padding: 10px 10px 10px 26px;
					z-index: 100;

					.arrow {
						position: absolute;
						display: block;
						transform: translateY(-100%);
						right: 10px;
						top: 0;
						width: 0;
						height: 0;
						border-color: transparent;
						border-style: solid;
						border-width: 6px;
						border-bottom-color: $color-primary;
						border-top-width: 0;

						&::after {
							content: "";
							position: absolute;
							display: block;
							width: 0;
							height: 0;
							border-color: transparent;
							border-style: solid;
							border-width: 5px;
							top: 1px;
							margin-left: -5px;
							border-top-width: 0;
							border-bottom-color: #fff;
						}
					}

					.uploadFileWrap {
						overflow: hidden auto;
						max-height: 140px;

						.upload-item {
							display: flex;
							align-items: center;
							margin-bottom: 10px;
						}

						.upload-item:last-child {
							margin-bottom: 0;
						}
					}
				}

				.upload-name {
					overflow: hidden;
					text-overflow: ellipsis;
					width: 150px;
					white-space: nowrap;
					text-decoration: underline;

					@include respond-to(mb) {
						width: 100px;
					}
				}

				b {
					margin: 0 6px;
					cursor: pointer;
				}

				b.icon-Down {
					font-size: 12px;
				}
			}
		}
	}

	.step-time {
		grid-column: 1 / span 2;
		min-width: 0;

		.box-border {
			display: none;
		}

		.step-time-tip {
			font-size: 1em;
			margin-bottom: 0.63em;
			color: #878484;
		}

		.step-item-params {
			display: grid;
			grid-template-columns: repeat(2, 1fr);
			grid-gap: 0.63em;
			width: 80%;

			@include respond-to(mb) {
				grid-template-columns: repeat(2, 1fr);
				grid-gap: 0.42em;
				width: 100%;
			}

			.param-item-wrap {
				overflow: hidden;
				position: relative;
				border: 1px solid transparent;
				border-radius: 6px;
				padding: 0.63em;
				background-color: #fafafa;
				box-sizing: border-box;
				cursor: pointer;

				&.active {
					@include selectedStyle;
				}

				.param-item {
					position: relative;
					min-width: 0;
					display: flex;
					justify-content: flex-start;
					align-items: center;
					margin-bottom: 0.2em;
					font-weight: 700;
				}
			}
		}
	}
}

.selectedShapeWrap {
	display: flex;
	align-items: center;
	height: 1.5em;
	overflow: hidden;
	font-weight: 700;
	transform: translateY(0);
	opacity: 1;
	transition: all 0.1s ease;
	margin-bottom: 1em;

	@include respond-to(mb) {
		margin-bottom: 0.7em;
	}
}

.animate-up {
	transform: translateY(-1.5em);
	opacity: 0;
}

.animate-down {
	transform: translateY(1.5em);
	opacity: 0 !important;
}
</style>