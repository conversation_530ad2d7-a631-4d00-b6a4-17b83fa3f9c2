import {request} from "@/utils/request";

//获取设计师购物车列表
export function getAllShoppingCart(data) {
	return request({
		url: "/app/designerShoppingCart/getAllShoppingCart",
		method: "post",
		data
	});
}

//未登录编辑数量
export function addShoppingCartNotLogin(data) {
	return request({
		url: "/app/designerShoppingCart/addShoppingCartNotLogin",
		method: "post",
		data
	});
}

//登录编辑数量
export function addShoppingCart(data) {
	return request({
		url: "/app/designerShoppingCart/addShoppingCart",
		method: "post",
		data
	});
}

//删除
export function delShoppingCart(data) {
	return request({
		url: "/app/designerShoppingCart/delShoppingCart",
		method: "post",
		data
	});
}

//未登录删除购物车
export function stylistDelShoppingCartNotLogin(data) {
	return request({
		url: "/app/designerShoppingCart/delShoppingCartNotLogin",
		method: "post",
		data,
	});
}

//算价格(登录)
export function stylistCheckout(data) {
	return request({
		url: "/app/designerOrders/checkout",
		method: "post",
		data
	});
}

//算价格（未登录）
export function stylistCheckoutNotLogin(data) {
	return request({
		url: "/app/designerOrders/checkoutNotLogin",
		method: "post",
		data
	});
}

//添加订单(登录)
export function stylistAddOrder(data) {
	return request({
		url: "/app/designerOrders/addOrder",
		method: "post",
		data
	});
}

//添加订单(未登录)
export function stylistAddOrderNotLogin(data) {
	return request({
		url: "/app/designerOrders/addOrderNotLogin",
		method: "post",
		data
	});
}


export function stylistGetOrderInfo(data) {
	return request({
		url: "/app/designerOrders/getOrderInfo",
		method: "post",
		data
	});
}

export function stylistGetOrderInfoNotLogin(data) {
	return request({
		url: "/app/designerOrders/getOrderInfoNotLogin",
		method: "post",
		data
	});
}

//验证支付
export function stylistAutoVerifyPayment(data) {
	return request({
		url: "/app/designerOrders/autoVerifyPayment",
		method: "post",
		data: data,
	});
}

//合并购物车
export function mergeShoppingCart(data) {
	return request({
		url: "/app/designerShoppingCart/mergeShoppingCart",
		method: "post",
		data: data,
	});
}


export function updatePaymentMethodId(data) {
	return request({
		url: "/app/designerOrders/updatePaymentMethodId",
		method: "get",
		params: data,
	});
}

export function stylistEditOrderAddress(data) {
	return request({
		url: "/app/designerOrders/editOrderAddress",
		method: "post",
		data,
	});
}