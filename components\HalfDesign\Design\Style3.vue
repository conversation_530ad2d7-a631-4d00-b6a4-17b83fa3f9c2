<template>
	<div class="mb-4" :class="stepData.styleName">
		<half-design-select-tip :customText="stepData.attributeTitle" :selectedValue="shape"></half-design-select-tip>
		<slot name="stepText"></slot>
		<div class="textWrap">
			<div class="small-title">{{ langSemiCustom.oneText }}</div>
			<div class="text-item-list">
				<div class="text-item">
					<div class="input-area" tabindex="0">
						<div style="flex: 1">
							<input id="myInput" autocomplete="off" type="text" placeholder="Add Text" :value="myTextProperty.text" @input="addText($event.target.value)" />
						</div>
						<v-menu :close-on-content-click="false" offset-y left min-width="300">
							<template v-slot:activator="{ on, attrs }">
								<div v-bind="attrs" v-on="on">
									<img src="~/static/img/icon_color.png" alt="Color Wheel" title="Color Wheel" style="width: 25px; height: 25px" />
								</div>
							</template>
							<half-design-color-picker :textProperty="myTextProperty" :colorList="colorList" @selectColor="changeTextProperty($event.data, $event.property)"></half-design-color-picker>
						</v-menu>
						<v-divider class="mx-2 mx-xl-5" vertical></v-divider>
						<div class="font-bold" :class="{ active: myTextProperty.fontWeight === 'bold' }" @click="changeTextProperty(myTextProperty.fontWeight === 'normal' || myTextProperty.fontWeight === 400 ? 'bold' : 'normal', 'fontWeight')">{{ langSemiCustom.Bletter }}</div>
						<v-divider class="mx-2 mx-xl-5" vertical></v-divider>
						<div class="font-style" :class="{ active: myTextProperty.fontStyle === 'italic' }" @click="changeTextProperty(myTextProperty.fontStyle === 'normal' || !myTextProperty.fontStyle ? 'italic' : 'normal', 'fontStyle')">{{ langSemiCustom.Iletter }}</div>
						<v-divider class="mx-2 mx-xl-5" vertical></v-divider>
						<div class="fontFamilyWrap">
							<v-menu offset-y left max-height="400">
								<template v-slot:activator="{ on, attrs }">
									<div class="fontMenu" v-bind="attrs" v-on="on">
										<span class="text-truncate">
											{{ myTextProperty.fontFamily || "Arial" }}
										</span>
										<v-icon>mdi-menu-down</v-icon>
									</div>
								</template>
								<v-list>
									<v-list-item v-for="(item, index) in fontsData" :key="index" item-text="name" item-value="name" label="Font Family" @click="changeTextProperty(item.name, 'fontFamily')">
										<v-list-item-title :style="{ fontFamily: item.name }">{{ item.name }}</v-list-item-title>
									</v-list-item>
								</v-list>
							</v-menu>
						</div>
					</div>
					<!-- <div>
						<v-btn fab x-small color="grey" depressed>
							<v-icon color="#ffffff">mdi-minus</v-icon>
						</v-btn>
					</div> -->
				</div>
				<!-- <div class="text-item add">
					<div class="input-area">
						+ Add Another Line
					</div>
					<div>

					</div>
				</div> -->
			</div>
		</div>
		<div class="imageWrap">
			<div class="small-title">{{ langSemiCustom.twoText }}</div>
			<div class="imgArea" v-show="!uploadImg.url" @click="triggerUpload('upload')">
				<div class="iconWrap">
					<v-icon size="60">mdi-cloud-upload</v-icon>
				</div>
				<div class="tipText">
					{{ langSemiCustom.fileDrag }} <strong>{{ langSemiCustom.fileBrowse }}</strong
					><br />
					{{ langSemiCustom.fileMax30 }}<br />
					<span>{{ langSemiCustom.fileType }}</span>
				</div>
				<input type="file" accept=".jpg,.jpeg,.png,.gif,.bmp" ref="upload" @click.stop @change="uploadPic" />
			</div>
			<div class="uploadImg" v-show="uploadImg.url">
				<div class="text-center">
					<div class="imgWrap">
						<img :src="uploadImg.url" alt="upload" title="upload" />
						<v-icon class="close" small @click="delImg">mdi-close-circle</v-icon>
					</div>
					<div class="imgName text-truncate mt-1">
						{{ uploadImg.name }}
					</div>
				</div>
				<div class="changeImg" @click="triggerUpload('change')">
					<v-icon color="grey" large>mdi-cloud-upload</v-icon>
					<span>Change Logo</span>
				</div>
			</div>
			<pic-color-change :removeBgColor="removeBgColor" :oneColor.sync="oneColor" :filterColor="filterColor" :uploadImg="uploadImg" :picColorList="picColorList" :oldColor="oldColor" :newColor="newColor" :colorList="colorList" :copyPicColorList="copyPicColorList" @changeOneColor="changeOneColor" @selectPicColor="selectPicColor" @changePicColor="changePicColor" @removeBg="removeBg" @filterPicColorBefore="filterPicColorBefore"> </pic-color-change>
		</div>
	</div>
</template>
<script>
import { designMixin } from "@/mixins/halfDesign";

export default {
	mixins: [designMixin],
	computed: {
		fontsData() {
			return require("@/assets/json/fontList.json");
		},
	},
	mounted() {
		this.$Bus.$on("focusDesignInput", this.focusInput);
	},
	methods: {
		focusInput() {
			let input = document.getElementById("myInput");
			if (input) {
				input.focus();
			}
		},
	},
	beforeDestroy() {
		this.$Bus.$off("focusDesignInput");
	},
};
</script>
<style scoped lang="scss">
@import "~assets/css/half.scss";

.fontMenu {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.small-title {
	font-weight: 700;
	padding: 0 0 10px 0;
	border-bottom: 1px solid $border-color;
}

.textWrap {
	.text-item-list {
		padding: 10px 0;
	}

	.text-item {
		display: grid;
		grid-template-columns: 1fr;
		grid-gap: 10px;
		align-items: center;
		margin-bottom: 10px;

		&:last-child {
			margin-bottom: 0;
		}

		.input-area {
			display: flex;
			align-items: center;
			flex: 1;
			height: 40px;
			border: 1px solid $border-color;
			padding: 5px 10px;
			border-radius: $step-border-radius;

			&:focus-within {
				border-color: $color-primary;
				border-width: 2px;
			}

			.fontFamilyWrap {
				width: 150px;
			}

			input {
				width: 100%;
			}

			.font-bold,
			.font-style {
				padding: 0 10px;
				cursor: pointer;
			}

			.font-bold.active {
				font-weight: 700;
				color: $color-primary;
			}

			.font-style.active {
				font-style: italic;
				color: $color-primary;
			}
		}
	}

	.text-item.add {
		cursor: pointer;

		&:hover {
			.input-area {
				border-color: $color-primary;
			}
		}
	}
}

.imgArea {
	position: relative;
	margin: 20px 0;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	text-align: center;
	border-radius: $step-border-radius;
	border: 1px dashed $border-color;
	background-color: $background-color;
	padding: 20px 10px;
	cursor: pointer;

	input[type="file"] {
		position: absolute;
		top: 0;
		left: 0;
		z-index: -1;
		opacity: 0;
		height: 0;
	}

	&:hover {
		border-color: $color-primary;
	}

	strong {
		color: $color-primary;
	}

	span {
		color: $gray-text;
	}
}

.uploadImg {
	position: relative;
	margin: 20px 0;
	display: flex;
	align-items: center;
	justify-content: space-between;
	text-align: center;
	border-radius: $step-border-radius;
	border: 1px dashed $border-color;
	background-color: $background-color;
	padding: 20px 10px;

	& > div {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
	}

	.imgWrap {
		position: relative;
		width: 150px;
		height: 100px;
		padding: 10px;
		background-color: $background-color2;

		.close {
			position: absolute;
			top: 0;
			right: 0;
			transform: translate(50%, -50%);
			cursor: pointer;
		}

		img {
			width: 100%;
			height: 100%;
			object-fit: contain;
		}
	}

	.imgName {
		max-width: 150px;
		flex: 1;
		padding: 0 10px;
	}

	.changeImg {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		color: $color-primary;
		cursor: pointer;
	}
}

@include respond-to(mb) {
	.imgArea {
		margin: 10px 0;
		padding: 10px;
		font-size: 12px;
	}
	.uploadImg {
		margin-top: 10px;

		.imgWrap {
			width: 80px;
			height: 80px;
			padding: 5px;
		}

		.imgName {
			flex: 1;
			padding: 0 10px;
			font-size: 12px;
		}

		.changeImg {
			font-size: 12px;
		}
	}
	.textWrap {
		.text-item {
			grid-gap: 5px;

			.input-area {
				display: flex;
				align-items: center;
				flex: 1;
				height: 40px;
				border: 1px solid $border-color;
				padding: 5px 10px;
				border-radius: $step-border-radius;

				.fontFamilyWrap {
					width: 80px;
				}
			}
		}
	}
}
</style>
