import { calculate, calculateAll, getByPId, getCateParamRelationByCateId, getInfo, getPriceData, otoAddCart, otoEditInquiry, setInquiry, getAppRingIconList, getAppRingIconTagList, otoEditCart } from "@/api/pins";
import { scrollToViewTop, getFileSuffix, deepClone, debounce, scrollToViewCenter, domScrollFn, lockScroll, unlockScroll } from "@/utils/utils";
import infoUpload from "@/components/Medals/infoUpload";
import { getQuoteStyleData } from "@/assets/js/quote/quoteStyle";
import { quoteBack } from "@/assets/js/quote/quoteBack";
import { getSpecialColorList } from "@/api/web";
import { findSelectDiscount, getIsSmallQty } from "@/assets/js/quote/quotePublic";
let quoteMixin = {
	data() {
		return {
			videoAspectRatio: "16/9",
			videoKey: 0,
			isProcessing: false, // 标志位，表示是否正在处理请求
			presentedQuantity: 0,
			tradingPinsGiftList: [],
			qtyChecked: false,
			selectedTextures: [],
			adhesiveList: [],
			inputRequired: true,
			patchesColorDialog: false,
			patchesAdhesiveDialog: false,
			colorNumberList: [],
			tagNameId: "",
			semiMedalsIconList: [],
			semiMedalsIconTagList: [],
			initData: false,
			maskType: "",
			cateData: {},
			isIframe: false,
			// showOnly:false,
			isFullReductionActivity: false,
			satisfiedQuantity: 0,
			giftQuantity1: 0,
			isInquiry: false,
			platformProductId: "",
			windowWidth: 0,
			isLoading: true,
			timer: false,
			loadAddCart: false,
			commentsDialog: false, //建议弹窗
			selectedParamsValue: {}, //当前选中的参数
			selectedParamsValueParent: {}, //选中参数的父级
			originType: "",
			comeFromDZ: {
				absoluteImg: "",
				type: "",
			},
			generalData: [], //总纲
			generalDataOld: [], //接口备份数据
			generalDataOldCateId: undefined, //接口备份数据请求id
			ringDiamondData: [],
			selectedData: {}, //选中的所有参数
			infoDialogVisible: false,
			showOtoDialog: false,
			picDialog: false,
			zoomPic: "",
			tempType: null,
			isDesignBack: false,
			isDs: 0,
			isGallery: 0,
			uploadList: [],
			uploadArtworkList: [],
			attachFileList: [],
			defaultImg: "",
			customQty: undefined,
			tipArr: [],
			isUpload: false,
			priceInfo: "",
			showArea: false,
			showRightArea: true,
			maskName: false,
			remark: "",
			upgradeDialog: false,
			qtyList: [],
			zoomAspectRatio: 1,
			debounceAddInquiry: "",
			debounceCalcPrice: "",
			isInputNumberError: false,
			attachment: false,
			copyGeneralData: [],
			noFileDialog: false,
			infoUploadList: [],
			inquiryId: 0,
			carouselList: [], //轮播图数据
			routingId: null,
			titleName: "",
			colorList: [],
			showBorderColor: false,
			switchColors: {},
			showSwitchColors: false,
			borderColorValue: {},
			canShowUpgradeDialog: false,
			showUpgradeOnce: true,
			tablePrice: [],
			selectDataObject: {},
			getQuote: false,
			isBackParams: false,
			//全局实时保存小分类数据
			smallTypeModel: {
				list: [],
				priceInfo: {
					id: undefined,
				},
			},
		};
	},
	components: {
		infoUpload,
	},
	watch: {
		picDialog(newValue) {
			if (newValue) {
				lockScroll()
			}else{
				unlockScroll();
			}
			if (this.isIframe && this.device == "mb") {
				this.toggleIframeCloseIcon(!newValue);
			}
		},
		showArea(newValue) {
			if (newValue) {
				lockScroll()
			}else{
				unlockScroll();
			}
			if (this.isIframe && this.device == "mb") {
				this.toggleIframeCloseIcon(!newValue);
			}
		},
		maskName(newValue) {
			if (this.isIframe && this.device == "mb") {
				this.toggleIframeCloseIcon(!newValue);
			}
		},
		patchesColorDialog(newValue){
			if (newValue) {
				lockScroll()
			}else{
				unlockScroll();
			}
			if (this.isIframe && this.device == "mb") {
				this.toggleIframeCloseIcon(!newValue);
			}
		},
		patchesAdhesiveDialog(newValue){
			if (newValue) {
				lockScroll()
			}else{
				unlockScroll();
			}
			if (this.isIframe && this.device == "mb") {
				this.toggleIframeCloseIcon(!newValue);
			}
		},
		commentsDialog(newValue) {
			if (newValue) {
				lockScroll()
			}else{
				unlockScroll();
			}
			if (this.isIframe && this.device == "mb") {
				this.toggleIframeCloseIcon(!newValue);
			}
		},
		selectedData: {
			handler() {
				this.debounceCalcPrice();
			},
			deep: true,
		},
		customQty(val) {
			if (val < 300) {
				this.qtyChecked = false;
			}
			this.handleWeightDiscount();
			this.$store.commit("quoteQuantity", val);
		}
	},
	computed: {
		currencyRate() {
			return this.$store.getters.currencyRate;
		},
		stepHistory() {
			return this.$store.state.stepHistory;
		},
		accept() {
			return "video/*,text/plain,image/*,.doc,.docx,.xlsx,.xls";
		},
		quoteStyleData() {
			let pid = this.pid;
			return getQuoteStyleData.call(this, pid);
		},
		recomendUpload() {
			let arr = [],
				selectedData = this.selectedData;
			for (let i in selectedData) {
				if (i == "Additional Upgrades (Optional)") {
					if (selectedData[i]) {
						selectedData[i].forEach((item) => {
							if (item && item.files && item.files.length) {
								arr = arr.concat(item.files);
							}
						});
						continue;
					}
				}
				let item = selectedData[i] && selectedData[i][0];
				if (item && item.files && item.files.length) {
					arr = arr.concat(item.files);
				}
			}
			return arr;
		},
		sizeValue() {
			let findSize = this.generalData.find((item) => {
				return item.paramType === "SIZE";
			});
			if (!findSize) {
				return "";
			}
			return this.selectedData[findSize.paramName] && this.selectedData[findSize.paramName][0]?.paramCode;
		},
		shapeImg() {
			try {
				let findSize = this.generalData.find((item) => {
					return item.paramType === "SIZE";
				});
				if (!findSize) {
					return "";
				}
				let defaultUrl = JSON.parse(findSize.childList[0].imageJson)[0].url;
				let sizeItem = this.selectedData[findSize.paramName][0];
				if (sizeItem) {
					return JSON.parse(sizeItem.imageJson)[0].url;
				} else {
					return defaultUrl;
				}
			} catch (e) {
				return "";
			}
		},
		proId() {
			if (this.previewMode) {
				return 148;
			} else {
				return this.$store.state.proId;
			}
		},
		projectName() {
			if (this.previewMode) {
				return "o2o-site-style";
			} else {
				return this.$store.state.proName;
			}
		},
		previewMode() {
			return process.env.VUE_APP_MODE === "Preview";
		},
		proType() {
			return this.$store.state.proType;
		},
		lang() {
			return this.$store.getters.lang.quote || {};
		},
		langSemi() {
			return this.$store.getters.lang.semiCustom || {};
		},
		userUUID() {
			return this.$store.state.userUUID;
		},
		userId() {
			return this.$store.state.userInfo?.id;
		},
		isLogin() {
			return this.$store.getters.isLogin;
		},
		userInfo() {
			return this.$store.state.userInfo;
		},
		device() {
			return this.$store.state.device;
		},
		textInfo() {
			return {
				inquiryTip: this.lang.inquiryTip,
				addCartTip: this.lang.addCartTip,
				emailText: this.lang.emailText + " " + this.$store.state.proSystem.email,
				email: this.lang.mailto + this.$store.state.proSystem.email,
			};
		},
		domainName() {
			let hostname = window.location.hostname;
			if (hostname === "www.gs-jj.com") return hostname + "/sc";
			return hostname;
		},
	},
	provide() {
		return { getIsDs: () => this.isDs };
	},
	methods: {
		async qtyCheckedChange() {
			this.isProcessing = true; // 开始计算，禁用 addCart
			setTimeout(() => {
				this.isProcessing = false;
			}, 2000);
			try {
				await this.debounceCalcPrice();
			} catch (error) {
			}
		},
		handleUpdateTextures(updatedTextures) {
			this.selectedTextures = updatedTextures; // 更新父组件中的材质数据
		},

		changeQty() {
			this.handleWeightDiscount();
		},
		getTitleValue(item) {
			if (item.cateNameQuote) {
				return "cateNameQuote";
			} else {
				return "cateName";
			}
		},
		pushInfoList(data) {
			this.infoUploadList.push(data);
		},
		delInfoList(index) {
			this.infoUploadList.splice(index, 1);
		},
		updateInquiry() {
			if (!this.inquiryId) {
				this.closeInfoDialog(false);
			}
			let newPicPath = this.infoUploadList.map((item) => item.secure_url);
			let data = {
				id: this.inquiryId,
				newPicPath: newPicPath,
			};
			setInquiry(data).then(() => {
				this.closeInfoDialog(false);
			});
		},
		closeInfoDialog(data) {
			this.infoUploadList = [];
			this.noFileDialog = data;
			if (this.isIframe) {
				let targetWindow = window.opener || window.parent; // 目标窗口，可以是父窗口或打开当前窗口的窗口
				if (this.$route.query.isBack) {
					targetWindow.postMessage({
						type: "closeDialog",
					});
				} else {
					targetWindow.postMessage(
						{
							type: "toHome",
						},
						window.origin
					);
				}
			} else {
				window.location.href = "/";
			}
		},
		picDialogFun(val) {
			this.picDialog = val;
			this.closeMask();
		},
		showMoreBtn(e, r) {
			e.srcElement.style.display = "none";
			e.srcElement.style.height = "0";
			this.$nextTick(() => {
				this.$refs[r][0].style.overflow = "visible";
				this.$refs[r][0].style.maxHeight = "100%";
			});
		},
		isVideoPath(val) {
			if (this.isJsonString(val)) {
				return !!(JSON.parse(val).length && JSON.parse(val)[0].path);
			} else {
				return Boolean(val);
			}
		},
		isJsonString(str) {
			try {
				const toObj = JSON.parse(str); // json字符串转对象
				/*
                判断条件 1. 排除null可能性
                         2. 确保数据是对象或数组
            */
				if (toObj && typeof toObj === "object") {
					return true;
				}
			} catch {}
			return false;
		},
		zoomPicFun($event, val, type) {
			this.tempType = type;
			this.picDialog = true;
			this.selectedObj = $event;
			this.dialogItem = val;

			if (type == "video" || type == "additional") {
				let ph = val.videoPath ? val.videoPath : val.priceInfo.videoPath;
				if (!this.isJsonString(ph)) {
					this.zoomPic = ph;
				} else {
					let tempPath = JSON.parse(ph).find((x) => {
						return x.proType == this.proType;
					});
					this.zoomPic = tempPath ? tempPath.path : JSON.parse(ph)[0].path;
				}
			} else {
				this.zoomPic = val.priceInfo.imagePath || JSON.parse(val.imageJson)[0].url;
			}
		},
		viewVideo(item, citem, type) {
			//用户点击放大视频
			this.$store.commit("setGoogleUploadAction", {
				[`STEP${item.customIndex}:${item.paramName}`]: citem.paramName,
				content_id: this.pid,
				userType: "fangdaVideo",
			});
			this.selectedParamsValueParent = item;
			this.selectedParamsValue = citem;
			this.picDialog = true;
			this.tempType = type;
			//弹窗来源,用来判断按钮事件
			this.originType = item?.paramType;
			if (type == "video" || type == "additional") {
				let ph = citem.videoPath ? citem.videoPath : citem.priceInfo.videoPath;
				if (!this.isJsonString(ph)) {
					this.zoomPic = ph;
				} else {
					let tempPath = JSON.parse(ph).find((x) => {
						return x.proType == this.proType;
					});
					this.zoomPic = tempPath ? tempPath.path : JSON.parse(ph)[0].path;
				}
			} else {
				this.zoomPic = citem.priceInfo.imagePath || JSON.parse(citem.imageJson)[0].url;
			}
		},
		getVideoOptions(path, type, poster) {
			let ph;
			if (!this.isJsonString(path)) {
				ph = path;
			} else {
				let tempPath = JSON.parse(path).find((x) => {
					return x.proType == this.proType;
				});
				if (!tempPath) {
					return false;
				}
				ph = tempPath ? tempPath.path : JSON.parse(path)[0].path;
			}
			if (type === 1) {
				return {
					autoplay: true,
					controls: false,
					muted: true,
					loop: true,
					fill: true,
					sources: [
						{
							src: ph,
							type: "video/mp4",
						},
					],
				};
			} else if (type === 2) {
				return {
					autoplay: true,
					controls: true,
					muted: false,
					loop: true,
					fill: true,
					sources: [
						{
							src: ph,
							type: "video/mp4",
						},
					],
				};
			} else if (type === 3) {
				return {
					autoplay: true,
					controls: false,
					muted: true,
					loop: true,
					fill: true,
					sources: [
						{
							src: ph,
							type: "video/mp4",
						},
					],
				};
			} else if (type === 4) {
				let newPoster = "";
				if (!this.isJsonString(poster)) {
					newPoster = poster;
				} else {
					let ph = JSON.parse(poster);
					if (ph && ph.length) {
						let tempPath = ph.find((x) => {
							return x.proType == this.proType;
						});
						newPoster = tempPath ? tempPath.path : ph[0].path;
					}
				}
				return {
					autoplay: false,
					controls: false,
					muted: true,
					loop: true,
					fill: true,
					sources: [
						{
							src: ph,
							type: "video/mp4",
						},
					],
					poster: newPoster,
				};
			}
		},

		getFileSuffix,

		closeMask() {
			// 如果有当前步骤，记录"取消"按钮点击
			if (this.maskName) {
				const currentStep=this.generalData.find(item => item.paramName===this.maskName);
				if (currentStep) {
					// 记录按钮点击
					this.$store.commit("recordButtonClick", {
						buttonName: "cancel",
						stepNumber: currentStep.customIndex,
						stepName: currentStep.paramName,
						action: "cancel"
					});
				}
			}
			this.maskName = false;
		},

		parseJSON(str) {
			return str
				? JSON.parse(str)
				: [
						{
							url: "",
						},
				  ];
		},
		showLessBtn(paramName){
			let find = this.filterShowGeneralData.find((item)=>item.paramName === paramName);
			if(find){
				this.toPosition(find.paramName);
			}
		},

		toPosition(name) {
			// 折叠步骤报价特殊处理 展开小步骤
			if (this.pid === 300 && ["Select Imprint Color Qty", "Select Material", "Select Plating Surface", "Select Thickness"].includes(name)) {
				this.$refs.stepUpload[0].expandSubStep();
			}

			this.maskName = name;
			if (!document.getElementById(`${name}`)) {
				return false;
			}
			this.$nextTick(() => {
				this.goStep();
			});
			this.showArea = false;
		},

		//判断是否选中
		hasId(id, arr) {
			if (!arr) {
				return false;
			}
			return !!arr.find((item) => {
				return item.id === id;
			});
		},

		selectQuoteAdditional(item, citem) {
			let param = this.selectedData[item.paramName];
			if (!param) {
				param = [];
			}
			//判断是否已选, 是则取消选中，否则选中
			let findInd = param.findIndex((item) => {
				return item.paramName === citem.paramName;
			});
			if (findInd >= 0) {
				// citem.inputNum = undefined;
				param.splice(findInd, 1);
			} else {
				param.push(citem);
			}
			//取消选中 No Upgrades
			let ind = param.findIndex((item) => {
				return item.chooseNum <= 1;
			});
			if (ind >= 0) {
				param[ind].inputNum = undefined;
				param.splice(ind, 1);
			}
			//如果全部取消，默认选中 No Upgrades
			if (!param.length) {
				let fItem = item.childList.find((item) => {
					return item.chooseNum <= 1;
				});
				if (fItem) {
					param = [fItem];
				}
			}
			this.selectedData[item.paramName] = param;
		},

		showUpgradeDialog() {
			//升级弹窗只提示一次
			if (!this.showUpgradeOnce) {
				return;
			}
			let qty = this.customQty,
				qtyList = this.qtyList,
				tipArr = [];
			let findInd = qtyList.findIndex((item) => {
				return item.totalQuantity > qty;
			});
			if (findInd === 0) {
				findInd += 1;
			}
			if (findInd >= 0) {
				tipArr = [this.priceInfo, qtyList[findInd]];
			}
			this.tipArr = tipArr;
			if (tipArr.length && qtyList[findInd - 1] && qtyList[findInd]) {
				try {
					if (tipArr[1].totalPrice === tipArr[0].totalPrice || tipArr[0].totalPrice <= 99) {
						return;
					}
					this.upgradeDialog = true;
					this.showUpgradeOnce = false;
				} catch (e) {}
			}
		},

		goStep() {
			try {
				if (this.device === "mb") {
					if (this.maskType === "SIZE") {
						scrollToViewTop(document.getElementById(`${this.maskName}`), -90);
					} else {
						scrollToViewTop(document.getElementById(`${this.maskName}`));
					}
				} else {
					let elArr = Array.from(document.getElementsByClassName("top_fix"));
					if (elArr.length) {
						let height = elArr[0].offsetHeight;
						scrollToViewTop(document.getElementById(`${this.maskName}`).children[0], height / 2);
					} else {
						scrollToViewTop(document.getElementById(`${this.maskName}`).children[0]);
					}
				}
			} catch (e) {}
		},

		upgradeLevel() {
			this.upgradeDialog = false;
			this.customQty = String(this.tipArr[1].totalQuantity);
			this.calcPrice();
		},

		toggleIframeCloseIcon(val) {
			let targetWindow = window.opener || window.parent;
			targetWindow.postMessage(
				{
					type: "showCloseIcon",
					showCloseIcon: val,
				},
				window.origin
			); // 发送消息
		},

		showMaskFn(typeName, clickType = "nextBtn") {
			//设计系统回填中，不进行跳转
			if (this.isDesignBack) {
				return false;
			}
			let verify = true,
				selectedData = this.selectedData,
				generateData = this.generalData;
			for (let i = 0; i < generateData.length; i++) {
				let item = generateData[i];
				//参数名称
				let paramName = item.paramName;
				//选中的参数值
				let selectedVal = selectedData[paramName];
				//不展示在界面的参数，不校验
				if (item.noShowDetail) {
					continue;
				}
				if (this.pid == 205) {
					this.activeNames = ["1", "2", "3", "4", "5", "6", "7", "8"];
				}
				if (this.pid == 205 && paramName != "Addition Engraving") {
					this.drawerTwo = false;
				}
				if (this.pid === 330 && paramName === "Design Your Metal Signs") {
					if (!this.checkDesignStep()) {
						this.maskName = paramName;
						verify = false;
						break;
					}
				} else if (paramName === "Upload Artwork & Comments" || paramName === "Customize Your Business Cards") {
					//设计系统跳转报价不校验此步骤
					// if(this.$route.query.designPic){
					// 	continue
					// }
					if (this.$data.hasOwnProperty("editActive")) {
						if ((!this.uploadArtworkList.length && !this.isUpload && this.editActive) || (!this.editActive && this.remark1.trim() == "" && this.remark2.trim() == "")) {
							if (!this.editActive && this.remark1.trim() == "" && this.remark2.trim() == "") {
								this.tipShow = true;
								this.remark1 = "";
								this.remark2 = "";
							}
							this.maskName = paramName;
							verify = false;
							break;
						}
					} else if (!this.uploadArtworkList.length && !this.isUpload) {
						this.maskName = paramName;
						verify = false;
						break;
					}
				} else if (paramName === "qty" || paramName === "Quantity" || paramName === "Select Your Quantity" || ((this.pid === 300 || this.pid === 671) && paramName === "Size")) {
					if (!this.customQty || this.customQty <= 0) {
						this.maskName = paramName;
						verify = false;
						break;
					}
				} else if (paramName === "Metal Finish" || (paramName === "Plating" && this.pid === 261) || (paramName === "Plating" && this.pid === 286)) {
					let temp = selectedVal;
					if (!temp || !temp.length) {
						this.maskName = paramName;
						verify = false;
						break;
					}
					let result = temp.every((item) => {
						return item.inputNum > 0;
					});
					if (!result) {
						this.isInputNumberError = true;
						this.maskName = paramName;
						verify = false;
						break;
					}
				} else if ((this.pid === 312 || this.pid === 45 || this.pid === 433) && paramName === "Ribbon") {
					//medals报价特殊校验
					let temp = selectedVal;
					if (temp.length == 0) {
						this.maskName = paramName;
						verify = false;
						break;
					} else {
						if (temp[0].paramName == "Stock Ribbon") {
							if (!temp[0].colorValue || !temp[0].sizeValue) {
								this.maskName = paramName;
								verify = false;
								break;
							}
						} else if (temp[0].paramName == "Custom Ribbon") {
							if (temp[0].later || temp[0].uploadList?.length || temp[0].files?.length) {
							} else {
								this.maskName = paramName;
								verify = false;
								break;
							}
						}
					}
				} else if (paramName === "Patch Border") {
					let temp = selectedVal;
					if (temp.length == 0) {
						this.maskName = paramName;
						verify = false;
						break;
					} else {
						if (temp[0].paramName == "Merrow") {
							if (temp[0].colorValue === null) {
								this.maskName = paramName;
								verify = false;
								break;
							}
						}
					}
				} else if (this.pid == 205 && paramName == "Top Design") {
					if (this.setpOneData.text1 == "" || this.setpOneData.text2 == "" || this.setpOneData.color1 == "" || this.setpOneData.color2 == "" || this.setpOneData.stone1 == "" || this.setpOneData.stone2 == "") {
						this.maskName = paramName;
						verify = false;
						break;
					}
				} else if (this.pid == 205 && paramName == "Side Design" && this.setpTwoIndex == 2) {
					if (this.setpTwoData.text1 == "" || this.setpTwoData.color1 == "" || this.setpTwoData.files.length == 0) {
						this.maskName = paramName;
						verify = false;
						break;
					}
				} else if (this.pid == 749 && paramName == "Design Your Printing" && !this.fontImgCustom.length) {
					verify = false;
					break;
				} else if ([310, 459].includes(this.pid) && paramName === "Label Fold Type") {
					//判断二级参数是否选中
					if (!selectedVal || selectedVal.length === 0) {
						this.maskName = paramName;
						verify = false;
						break;
					}
					//判断三级参数是否选中
					if (selectedVal[0].childList && selectedVal[0].childList.length) {
						if (!selectedVal[0]?.extendStepValue) {
							this.maskName = paramName;
							verify = false;
							break;
						}
					}
				} else if (([573].includes(this.pid) && paramName === "Intended Use") || paramName === "Assembly Method") {
					//判断二级参数是否选中
					if (!selectedVal || selectedVal.length === 0) {
						this.maskName = paramName;
						verify = false;
						break;
					}
					//判断三级参数是否选中
					if (selectedVal[0].childList && selectedVal[0].childList.length) {
						if (!selectedVal[0]?.extendStepValue) {
							this.maskName = paramName;
							verify = false;
							break;
						}
					}
				} else {
					if (!selectedVal || selectedVal.length === 0) {
						this.maskName = paramName;
						this.maskType = item.paramType;
						verify = false;
						break;
					}
				}
			}
			if (verify) {
				if (typeName === "Select Turnaround Time" || typeName === "Turnaround Time" || typeName==="Delivery Date") {
					this.canShowUpgradeDialog = true;
					//判断点击的是参数还是下一步按钮
					if (clickType === "clickParam") {
						return verify;
					}
				}
				if (this.pid === 312) {
					this.showSummary = true;
					this.$nextTick(() => {
						setTimeout(() => {
							domScrollFn(".fonterBtnGrap", "end");
						}, 350);
					});
				}
				if (this.pid === 330) {
					scrollToViewCenter(document.getElementById("foot"));
				} else {
					scrollToViewTop(document.getElementById("foot"));
				}
				this.maskName = false;
			} else {
				//报错提示，可优化
				if (this.maskName === "Upload Artwork & Comments" && typeName === "Upload Artwork & Comments") {
					this.$toast.error("Please Upload your Artwork.");
				} else if (this.maskName === "Pin Size" && typeName === "Pin Size") {
					this.$toast.error("Please Select Your Pin Size.");
				} else if (this.maskName === "Additional Upgrades (Optional)" && typeName === "Additional Upgrades (Optional)") {
					this.$toast.error("Please Select Your Additional Upgrades.");
				} else if (this.maskName === "Select Packaging" && typeName === "Select Packaging") {
					this.$toast.error("Please Select Your Packing.");
				} else if (this.maskName === "Select Turnaround Time" && typeName === "Select Turnaround Time") {
					this.$toast.error("Please Select Your Delivery Time.");
				} else if (this.maskName === "qty" && typeName === "qty") {
					this.$toast.error("Please Select Your Qty.");
				} else if (this.maskName === "Ribbon" && typeName === "Ribbon") {
					// this.$toast.error("Please choose the ribbon options.");
				} else if (this.maskName === "Additional Upgrades (Options)" && typeName === "Additional Upgrades (Options)") {
					this.$toast.error("Please Select Your Additional Upgrades.");
				} else if (this.maskName === "Medal Size" && typeName === "Medal Size") {
					this.$toast.error("Please Select Your Medal Size.");
				} else if (this.maskName === "Metal Finish" && typeName === "Metal Finish") {
					if (this.isInputNumberError) {
						this.$toast.error("Please enter the order quantity.");
					} else {
						this.$toast.error("Please Select Your Metal Finish.");
					}
				} else if (this.maskName === "Additional Upgrades" && typeName === "Additional Upgrades") {
					this.$toast.error("Please Select Your Additional Upgrades.");
				} else if (this.maskName === "Patch Size" && typeName === "Patch Size") {
					this.$toast.error("Please Select Your Patch Size.");
				} else if (this.maskName === "Plating" && typeName === "Plating") {
					if (this.isInputNumberError) {
						this.$toast.error("Please enter the order quantity.");
					} else {
						this.$toast.error("Please Select Your Plating.");
					}
				} else if (this.maskName === "Keychain Size" && typeName === "Keychain Size") {
					this.$toast.error("Please Select Your Keychain Size.");
				} else if (this.maskName === "Ring Finish" && typeName === "Ring Finish") {
					this.$toast.error("Please Select Your Ring Finish.");
				} else if (this.maskName === "Button Size" && typeName === "Button Size") {
					this.$toast.error("Please Select Your Button Size.");
				} else if (this.maskName === "Patch Border" && typeName === "Patch Border") {
					// this.$toast.error("Please Select Your Patch Border.");
				} else if ([599, 600, 601, 659, 656, 625].includes(this.pid)) {
					if (this.maskName === "qty") {
						this.$toast.error("Please Select or Enter Quantity");
						//咬板报价  没有输入数量，重新选择其他步骤的时候
						scrollToViewTop(document.getElementById("enterQuantity"));
						this.inputRequired = false;
					} else if (this.maskName === "Upload Artwork & Comments") {
						this.$toast.error(this.lang.ornament.promptcopy);
					} else if (this.maskName === "Ornament Process") {
						this.$toast.error("Please Select Ornament Process");
					}
				}
				setTimeout(() => {
					this.goStep();
				}, 100);
			}
			return verify;
		},

		dialogNextStep() {
			this.picDialog = false;
			if (this.originType === "quoteCategory") {
				this.selectQuoteParams(this.selectedParamsValueParent, this.selectedParamsValue);
			} else {
				this.recomendNext("next");
			}
		},
		texturesBtnNext() {
			this.picDialog = false;
			this.patchesAdhesiveDialog = false;
			this.recomendNext("next");
		},

		qtyAndBtnNext() {
			this.picDialog = false;
			this.patchesColorDialog = false;
			this.recomendNext("next");
		},

		qtyAndBtnConfirm() {
			this.picDialog = false;
			this.patchesColorDialog = false;
			this.recomendNext("more");
		},

		selectCurrentParams(item, citem) {
			this.$store.commit("recordStepEntry", {
				stepNumber: item.customIndex,
				stepName: item.paramName,
			});
			//判断可选参数数量
			if (citem.chooseNum <= 1) {
				this.selectedData[item.paramName].forEach((ccitem) => {
					if (ccitem.paramName !== citem.paramName) {
						ccitem.inputNum = undefined;
					}
				});
				this.selectedData[item.paramName] = [citem];
			} else {
				let findRecommend = this.selectedData[item.paramName].findIndex((ccitem) => {
					return ccitem.chooseNum <= 1;
				});
				let findCurrent = this.selectedData[item.paramName].findIndex((ccitem) => {
					return ccitem.paramName === citem.paramName;
				});
				if (findRecommend > -1) {
					this.selectedData[item.paramName][findRecommend].inputNum = undefined;
					this.selectedData[item.paramName].splice(findRecommend, 1);
				}
				if (findCurrent === -1) {
					if (+citem.inputNum) {
						this.selectedData[item.paramName].push(citem);
					}
				}
				if (findCurrent > -1) {
					if (!+this.selectedData[item.paramName][findCurrent].inputNum) {
						this.selectedData[item.paramName].splice(findCurrent, 1);
					}
				}
			}
			// 记录当前步骤完成
			this.$store.commit("recordStepComplete", {
				stepNumber: item.customIndex,
				stepName: item.paramName,
				paramName: citem.paramName||citem.cateName,
				isSuccess: true,
			});
		},

		handleWeightDiscount() {
			if (!this.selectedData[this.quoteStyleData?.paramName]) {
				return;
			}
			//获取算价格参数
			let priceParam = this.getPriceParam();
			//获取折扣参数列表
			let discountList = this.generalData
				.find((item) => {
					return item.paramType === "DISCOUNT";
				})
				?.childList.filter((item) => item.priceInfo.priceType === 10);
			if (!discountList?.length) {
				return;
			}
			discountList.forEach((item) => {
				if (priceParam.sizeId && priceParam.cateId) {
					if (priceParam.sizeId && priceParam.cateId && (priceParam.quantity || priceParam.qtyDetailDTOS?.length)) {
						calculate(Object.assign({}, priceParam, { discountId: item.priceInfo.id })).then((res) => {
							this.$set(item.priceInfo, "customWeightPrice", res.data.discountPrice);
						});
					}
				}
			});
		},

		//参数选中事件
		async selectQuoteParams(item, citem, noMask) {
			console.log(item, citem, noMask, "item, citem, noMask");
			let paramTypeList = ["NORMAL", "COLOR", "COLORCARD", "QUANTITY", "SIZE", "PARTS", "DISCOUNT", "PINPACK", "STICKER", "FONT", "FONTSIZE", "NEONSAMPLE", "COUNTRY"];
			let paramType = item.paramType;
			this.selectedParamsValueParent = item;
			this.selectedParamsValue = citem;
			// 记录步骤进入
			this.$store.commit("recordStepEntry", {
				stepNumber: item.customIndex,
				stepName: item.paramName,
			});
			//判断是否展示评论弹窗
			if (citem.addComment === 1 && !noMask) {
				//判断可选参数数量
				if (citem.chooseNum <= 1) {
					this.selectedData[item.paramName] = [citem];
					//记录用户的选择
					this.$store.commit("setGoogleUploadAction", {
						[`STEP${item.customIndex}:${item.paramName}`]: citem.paramName,
						content_id: this.pid,
					});
				} else {
					//判断是否已经选中,取反
					let param = this.selectedData[item.paramName];
					if (!param) {
						param = [];
					}
					let findInd = param.findIndex((item) => {
						return item.paramName === citem.paramName;
					});
					if (findInd >= 0) {
						param.splice(findInd, 1);
					} else {
						param.push(citem);
						//记录用户的选择,取反不做记录
						this.$store.commit("setGoogleUploadAction", {
							[`STEP${item.customIndex}:${item.paramName}`]: citem.paramName,
							content_id: this.pid,
						});
					}
					this.selectedData[item.paramName] = param;
				}
				this.commentsDialog = true;
			} else {
				switch (paramType) {
					case "quoteCategory":
						this.attachment = false;
						this.selectedData[item.paramName] = [citem];
						if (!this.isDs && !this.isGallery) {
							//右侧默认分类图
							this.comeFromDZ.absoluteImg = citem.imagePhoto;
						}
						this.generalData = await this.getCateParam(citem.id);
						//this.generalDataOld = JSON.parse(JSON.stringify(this.generalData)); //解决数据污染
						//特殊处理
						if (this.pid === 158 || this.pid === 25 || this.pid === 55 || this.pid === 51) {
							//清空Rush Pin Size的值
							this.selectedData["Select Rush Pin Size"] && this.selectedData["Select Rush Pin Size"].length ? (this.selectedData["Select Rush Pin Size"] = []) : "";
							this.selectedData["Rush Pin Size"] && this.selectedData["Rush Pin Size"].length ? (this.selectedData["Rush Pin Size"] = []) : "";
							if (citem.id === 66 || citem.id === 32 || citem.id === 167) {
								this.attachment = true;
							}
						}

						if (this.pid === 472) {
							//回填需要重新调接口 并处理三级联动特殊场景
							if (citem.quoteCategoryCateId) {
								this.generalDataOld = await this.getCateParam(citem.quoteCategoryCateId);
							}
							//清空Colored Area的值
							this.selectedData["Soft Enamel or Hard Enamel Coins"] && this.selectedData["Soft Enamel or Hard Enamel Coins"].length ? (this.selectedData["Soft Enamel or Hard Enamel Coins"] = []) : "";
						}
						if (this.pid === 300) {
							//默认选中唯一尺寸 其它默认参数
							const checkedSize = this.generalData.find((x) => x.paramType === "SIZE")?.childList[0];
							this.selectedData["Size"] = [checkedSize];
							this.selectedData["Select Imprint Color Qty"] = [this.generalData.find((x) => x.paramName === "Select Imprint Color Qty")?.childList.find((y) => y.paramName === "1 Color (Free)")];
							this.selectedData["Select Material"] = [this.generalData.find((x) => x.paramName === "Select Material")?.childList.find((y) => y.paramName === "Stainless Steel")];
							this.selectedData["Select Thickness"] = [this.generalData.find((x) => x.paramName === "Select Thickness")?.childList.find((y) => y.paramName === "0.5 mm")];
							this.selectedData["Select Plating Surface"] = [this.generalData.find((x) => x.paramName === "Select Plating Surface")?.childList.find((y) => y.paramName === "Natural Surface")];
							this.sizeId = checkedSize.priceInfo.id;
							this.cateId = citem.id;
							let find = this.quoteStyleData.childList.find((q) => q.id === citem.id);
							this.carouselList = JSON.parse(find.imageJson) || [];
							this.updatePriceTable();
						}
						//回填选项
						this.backQuoteParams();
						try {
							//不可选列表
							await this.getBanParamList(citem.id);
						} catch (e) {}
						//手动插入值
						if (this.attachment) {
							this.selectedData["customAttachment"] = [
								{
									parentId: 1,
									id: "-1",
									paramName: "customAttachment",
									alias: this.lang.StandardMilitaryClutch,
								},
							];
						}
						//metal-patches业务
						if (this.pid === 573) {
							this.clearFieldName("Style");
							this.clearFieldName("Intended Use");
							this.clearField("SIZE");
						}
						!noMask && this.showMaskFn(item.paramName);
						break;
					case "NORMAL":
						if (this.pid === 261 && item.paramName === "Select Ribbon") {
							this.RibbonBindValue = citem;
						}
						//medals报价特殊处理
						if ((this.pid === 312 || this.pid === 45 || this.pid === 433) && item.paramName === "Ribbon") {
							this.RibbonBindValue = citem;
						}
						//blank-parches报价
						if (this.pid === 273 && item.paramName === "Patch Border") {
							let find = item.childList.find((i) => i.id === citem.id);
							if (find) {
								this.showBorderColor = find.paramName !== "Heat Cut";
								(find.colorValue = null),
									find.childList.forEach((i) => {
										i.icon = false;
									});
								this.colorList = find.childList;
							}
						}
						//custom-metal-keychains 报价特殊处理
						if (this.pid === 112 && item.paramName === "Design Areas") {
							if (citem.paramName === "Single Sided - One Design") {
								let findBackOptionIndex = this.generalData.findIndex((ccitem) => {
									return ccitem.paramName === "Backstamp Options";
								});
								if (findBackOptionIndex > -1) {
									this.generalData[findBackOptionIndex].noShowDetail = false;
								}
							} else if (citem.paramName === "Double Sided - Different Design") {
								let findBackOptionIndex = this.generalData.findIndex((ccitem) => {
									return ccitem.paramName === "Backstamp Options";
								});
								if (findBackOptionIndex > -1) {
									this.generalData[findBackOptionIndex].noShowDetail = true;
								}
							}
							this.generalData = this.setIndex(this.generalData);
						}
						//custom-pvc-keychains 报价特殊处理
						if (this.pid === 118 && item.paramName === "Keychain Printed Area") {
							if (citem.paramName === "Single Sided - One Design") {
								let findBackOptionIndex = this.generalData.findIndex((ccitem) => {
									return ccitem.paramName === "Back Printing Options";
								});
								if (findBackOptionIndex > -1) {
									this.generalData[findBackOptionIndex].noShowDetail = false;
								}
							} else if (citem.paramName === "Double Sided - Same Design" || citem.paramName === "Double Sided - Different Design") {
								let findBackOptionIndex = this.generalData.findIndex((ccitem) => {
									return ccitem.paramName === "Back Printing Options";
								});
								if (findBackOptionIndex > -1) {
									this.generalData[findBackOptionIndex].noShowDetail = true;
								}
							}
							this.generalData = this.setIndex(this.generalData);
						}
						//medals 报价特殊处理
						if (this.pid === 45 && item.paramName === "Mold Option") {
							if (citem.paramName === "2D One Side Medal") {
								let findBackOptionIndex = this.generalData.findIndex((ccitem) => {
									return ccitem.paramName === "Back Side Option";
								});
								if (findBackOptionIndex > -1) {
									this.generalData[findBackOptionIndex].noShowDetail = false;
								}
							} else if (citem.paramName === "2D Two Sided Medal") {
								let findBackOptionIndex = this.generalData.findIndex((ccitem) => {
									return ccitem.paramName === "Back Side Option";
								});
								if (findBackOptionIndex > -1) {
									this.generalData[findBackOptionIndex].noShowDetail = true;
								}
							}
							this.generalData = this.setIndex(this.generalData);
						}
						//coins报价特殊处理
						if (this.pid === 472) {
							if (item.paramName === "Soft Enamel or Hard Enamel Coins") {
								//不是回填 则清空
								let findColorIndex = this.generalData.findIndex((item) => {
									return item.paramName === "Select Your Coin Colors";
								});
								if (findColorIndex > -1) {
									this.generalData[findColorIndex].childList = citem.childList;
								}
								if (citem.paramName === "Two Side 3D Coins / No Colors on 2 Sides" || citem.paramName === "One Side 3D Coins / No Colors on 2 Sides") {
									if (findColorIndex > -1) {
										this.generalData[findColorIndex].noShowDetail = true;
									}
								} else {
									if (findColorIndex > -1) {
										this.generalData[findColorIndex].noShowDetail = false;
									}
								}
								this.selectedData["Select Your Coin Colors"] = [];
								this.generalData = this.setIndex(this.generalData);
							}
						}
						//小刚metal-patches特殊处理
						if (this.pid === 573) {
							if (item.paramName === "Intended Use") {
								this.clearField("Style");
								this.clearField("SIZE");
							}
							if (item.paramName === "Material") {
								this.clearFieldName("Plating/Finish");
							}
							if (item.paramName === "Style") {
								this.clearField("SIZE");
							}
							/* if (citem.paramName === "Engraved" && item.paramName === "Metal Process") {
								console.log("弹窗就绪", citem);
								this.patchesColorDialog = true; //colorNumberList  //selectedParamsValue
								getSpecialColorList({
									type: citem.paramName,
								}).then((res) => {
									this.colorNumberList = res.data;
								});
							} */
						}
						//小刚metal-patches特殊处理 end
						// metal-business-cards特殊处理
						if (this.pid === 671 && item.paramName === "Upload Artwork & Comments") {
							if (citem.id != this.selectedData[item.paramName][0]?.id) {
								this.uploadArtworkList = [];
							}
							this.isUpload = false;
						}
						if (this.pid === 671 && item.paramName === "Select Metal Color") {
							this.selectedData["Select Surface Finish"] = [];
						}
						//custom-leather-patches报价Adhesive弹窗
						if ((item.paramName === "Patch Backing" || item.paramName === "Backing") && (citem.paramName === "Adhesive" || citem.paramName === "Adhesive Backing")) {
							this.patchesAdhesiveDialog = true;
							getSpecialColorList({
								type: this.device === "pc" ? "Adhesive-PC" : "Adhesive-MB",
							}).then((res) => {
								this.adhesiveList = res.data;
							});
						}

						if (["50% Embroidery", "75% Embroidery", "100% Embroidery"].includes(citem.paramName)) {
							this.$store.commit("setEmbroideryCoverageValue", citem.paramName);
						}

						//判断可选参数数量
						if (citem.chooseNum <= 1) {
							this.selectedData[item.paramName] = [citem];
							//判断这个步骤是否配置了下一步按钮，如果有就不自动跳转下一步
							if (this.allStepConfig) {
								if (this.allStepConfig[item.paramName]?.showNextBtn) {
									noMask = true;
								}
							}
							if (this.pid === 273 && item.paramName === "Patch Border") {
								noMask = true;
							}
							this.$nextTick(() => {
								!noMask && this.showMaskFn(item.paramName);
							});
						} else {
							//判断子参数类型
							this.selectQuoteAdditional(item, citem);
						}
						break;
					case "PINPACK":
						this.selectedData[item.paramName] = [citem];
						this.showMaskFn(item.paramName);
						break;
					case "QUANTITY":
						//判断可选参数数量
						if (citem.chooseNum <= 1) {
							this.selectedData[item.paramName].forEach((ccitem) => {
								if (ccitem.paramName !== citem.paramName) {
									ccitem.inputNum = undefined;
								}
							});
							this.selectedData[item.paramName] = [citem];
							this.$nextTick(() => {
								this.showMaskFn(item.paramName);
							});
						} else {
							//判断子参数类型
							let childParamType = citem.paramType;
							if (childParamType === "NORMAL") {
								this.selectQuoteAdditional(item, citem);
							} else {
								if (this.pid !== 25) {
									this.selectQuoteAdditional(item, citem);
								}
								//custom-embroidered-patches 夜光线与荧光线弹窗
								if ((citem.paramName === "Glow in the Dark Thread" || citem.paramName === "Fluorescent Thread") && citem.priceInfo.priceType === 14) {
									this.patchesColorDialog = true;
									getSpecialColorList({
										type: citem.paramName,
									}).then((res) => {
										this.colorNumberList = res.data;
									});
								} else {
									this.viewVideo(item, citem, "additional");
								}
							}
						}
						break;
					case "DISCOUNT":
						this.selectedData[item.paramName] = [citem];
						//如果手机端先选择最后一个步骤，就加滑动，如果所有步骤都有值，就去掉滑动
						!noMask && this.showMaskFn(item.paramName, "clickParam");
						break;
					case "SIZE":
						this.$store.commit("setQuoteSizeName", citem.paramName);
						this.$store.commit("setQuoteSizeValue", citem.paramCode);
						this.selectedData[item.paramName] = [citem];
						const sizePid = [158, 25, 55, 51, 491, 504];
						if (sizePid.includes(this.pid) && (item.paramName === "Rush Pin Size" || item.paramName === "Select Rush Pin Size")) {
							let findMedal = this.generalData.find((item) => {
								return item.paramName === "Select Metal Finish";
							});
							if (findMedal) {
								findMedal.childList = citem.childList;
								//清空已选中的电镀
								this.selectedData[findMedal.paramName] = [];
								this.showMaskFn(item.paramName);
							}
						}
						if (this.pid === 240 && (item.paramName === "Back Side Option" || item.paramName === "Back Side")) {
							this.showMaskFn(item.paramName);
						}
						if (this.pid === 261 && item.paramName === "Medal Size") {
							this.showMaskFn(item.paramName);
						}
						if (this.pid === 330) {
							this.showMaskFn(item.paramName);
						}
						if (this.pid === 749) {
							this.showMaskFn(item.paramName);
						}
						if (this.pid === 573) {
							this.smallTypeModel.list.map((i) => {
								if (i.paramName === "Metal Patches Size" || i.paramName === "Size") {
									i.childList.map((j) => {
										if (j.id == citem.id) {
											this.smallTypeModel.priceInfo.id = j.priceInfo.id;
										}
									});
								}
							});
						}
						break;
					case "COLOR":
						//判断可选参数数量
						if (citem.chooseNum <= 1) {
							this.selectedData[item.paramName].forEach((ccitem) => {
								if (ccitem.paramName !== citem.paramName) {
									ccitem.inputNum = undefined;
								}
							});
							this.selectedData[item.paramName] = [citem];
						} else {
							let childParamType = citem.paramType;
							if (childParamType === "NORMAL") {
								this.selectQuoteAdditional(item, citem);
							} else {
								this.selectQuoteAdditional(item, citem);
							}
						}
						break;
					default:
						this.selectedData[item.paramName] = [citem];
						!noMask && this.showMaskFn(item.paramName);
						break;
				}
				// 在显示下一步之前，记录当前步骤完成
				if (!noMask&&this.showMaskFn) {
					// 记录当前步骤完成
					this.$store.commit("recordStepComplete", {
						stepNumber: item.customIndex,
						stepName: item.paramName,
						paramName: citem.paramName||citem.cateName,
						isSuccess: true,
					});
				}
				//记录用户的选择
				this.$store.commit("setGoogleUploadAction", {
					[`STEP${item.customIndex}:${item.paramName}`]: citem.paramName,
					content_id: this.pid,
				});
			}
			try {
				//不可选参数过滤
				this.filterStepsFun().then((res) => {
					this.noChoiceData = res;
					/**
					 * 过滤后的特殊处理
					 *  */
					//template-SUNamel-pins报价特殊处理
					if (this.pid === 240 && item.paramName === "Select Shapes") {
						const sizeChild = this.generalData.find((x) => x.paramType === "SIZE");
						const filterSizeChild = sizeChild.childList.filter((x) => !this.noChoiceData.includes(x.priceInfo.id));
						filterSizeChild.length === 1 && this.selectQuoteParams(sizeChild, filterSizeChild[0], true);
					}
				});
			} catch (e) {}
			//折扣加价和重量加价并存，取消是否重量加价参数判断
			this.handleWeightDiscount();
		},
		//递归查找父级，替换图片
		borderColorClick(item) {
			if (item.paramName === "Recommend") {
				this.showColourAtla = !this.showColourAtla;
			} else {
				this.showColourAtla = false;
			}

			this.isImgStyle = true;
			// if(item.onlyAddInquiry === 1){
			// 	this.showOnly = true;
			// }else{
			// 	this.showOnly = false;
			// }
			let find = this.filterShowGeneralData.find((item) => item.paramName === "Patch Border");
			if (find) {
				this.selectQuoteParamsFunc(find, find.childList[0]);
			}
			this.selectedParamsValue.colorValue = item;
			this.colorList.forEach((r) => {
				if (r.id === item.id) {
					r.icon = true;
				} else {
					r.icon = false;
				}
			});

			this.switchColors = item;
			let parentId = item.parentId,
				parentItem;
			let findParent = (list, id) => {
				for (let i = 0; i < list.length; i++) {
					let data = list[i];
					if (data.id === id) {
						parentItem = data;
						break;
					}
					if (data.childList && data.childList.length) {
						findParent(data.childList, id);
					}
				}
			};
			findParent(this.generalData, parentId);
			let img = JSON.parse(item.imageJson)[1];
			/* 	if (parentItem == null && item.recommend) {
				parentItem = item.recommend;
			} */
			if (parentItem) {
				parentItem.imageJson = JSON.stringify([img]);
			}

			this.$forceUpdate();
			this.calcPrice();
		},

		//建议弹窗按钮事件
		recomendNext(type) {
			//判断参数可选数量
			if (this.selectedParamsValue.chooseNum <= 1) {
				this.selectedData[this.selectedParamsValueParent.paramName] = [this.selectedParamsValue];
			} else {
				//判断是否已经选中,已选中不做处理，未选中就选中
				let param = this.selectedData[this.selectedParamsValueParent.paramName];
				if (!param) {
					param = [];
				}
				let findInd = param.findIndex((item) => {
					return item.paramName === this.selectedParamsValue.paramName;
				});
				if (findInd < 0) {
					param.push(this.selectedParamsValue);
				}
				//取消选中 No Upgrades
				let ind = param.findIndex((item) => {
					return item.chooseNum <= 1;
				});
				if (ind >= 0) {
					param[ind].inputNum = undefined;
					param.splice(ind, 1);
				}
				this.selectedData[this.selectedParamsValueParent.paramName] = param;
			}
			if (type === "next") {
				// 记录当前步骤完成
				this.$store.commit("recordStepComplete", {
					stepNumber: this.selectedParamsValueParent.customIndex,
					stepName: this.selectedParamsValueParent.paramName,
					paramName: this.selectedParamsValue.paramName,
					isSuccess: true,
				});
				this.showMaskFn();
			} else if (type === "more") {
				//暂时不做处理
			}
		},
		addCustomProperty(data) {
			let handle = (list) => {
				for (let i = 0; i < list.length; i++) {
					let item = list[i];
					item.remark = "";
					item.noShowDetail = false;
					item.isHidden = false; //不可选属性
					item.files = [];
					item.colorList = [];
					item.inputNum = undefined;
					item.selectQty = undefined;
					item.inputQty = undefined;
					//扩展步骤 三级参数情况
					item.showExtendStep = false;
					item.extendStepValue = null;
					if (item.childList && item.childList.length) {
						handle(item.childList);
					}
				}
			};
			handle(data);
			return data;
		},
		//价格分层参数
		getPriceData() {
			getPriceData({ buyType: 7, productCateId: this.pid }).then((res) => {
				this.$store.commit("setMorePriceData", res.data);
			});
		},
		sortByKey(array, key) {
			return array.sort(function (a, b) {
				let x = a[key]; //如果要从大到小,把x,y互换就好
				let y = b[key];
				return x < y ? -1 : x > y ? 1 : 0;
			});
		},
		//获取小分类
		getCateParam(cateId) {
			return new Promise((resolve, reject) => {
				getCateParamRelationByCateId({
					cateId: cateId,
				}).then((res) => {
					let postData = res.data;
					if (this.pid == 205) {
						postData.forEach((item, index) => {
							if (item.paramType == "RINGDIAMOND") {
								this.ringDiamondData = item;
								this.setpOneData.stone1 = this.ringDiamondData.childList[0].alias;
								this.setpOneData.stone2 = this.ringDiamondData.childList[0].alias;
							}
							if (item.paramName == "Ring Diamond") {
								postData[0].stone = item;
								postData.splice(index, 1);
							}
						});
					}
					if (this.pid === 573) {
						this.smallTypeModel.list = postData;
					}
					let data = this.setIndex(this.sortByKey(this.addCustomProperty(postData).concat(this.quoteStyleData), "stepIndex"));
					//判断是否是第一次获取,如果不是回填其他参数比如 noShowDetail
					if (this.generalData.length) {
						data.forEach((item) => {
							let id = item.id;
							let findItem = this.generalData.find((i) => {
								return i.id === id;
							});
							if (findItem) {
								item.noShowDetail = findItem.noShowDetail;
							}
						});
					}
					resolve(data);
				});
			});
		},
		setIndex(data) {
			if (!data) {
				return false;
			}
			let index = 0;
			data.forEach((item) => {
				if (item) {
					if (!item.noShowDetail) {
						index += 1;
					}
					item.customIndex = index;
					/* 	if (item.paramName !== "quoteCategory" && this.pid === 573) {
						//metal patches 索引特殊处理
						item.customIndex = index - 1;
					} else {
						item.customIndex = index;
					} */
				}
			});
			return data;
		},
		//初始化报价数据
		getQuoteCategory() {
			return new Promise((resolve) => {
				getByPId({
					pid: this.$data.isPageQuote === true ? this.quotePid : this.pid,
				}).then(async (res) => {
					this.quoteStyleData.childList = res.data;
					this.quoteStyleData.childList.forEach((item) => {
						item.tips = item.remark;
					});
					//isPageQuote判断是否是页面报价 semiMedals页面报价
					if (this.$data.isPageQuote === true) {
						let f = this.quoteStyleData.childList.find((r) => r.id === this.quoteCateId);
						if (f) {
							this.selectedData[this.quoteStyleData.paramName] = [f];
							this.generalDataOldCateId = f.id;
							this.generalData = await this.getCateParam(f.id);
							//	this.generalDataOld = JSON.parse(JSON.stringify(this.generalData)); //解决数据污染
							this.titleName = f.cateName;
							this.carouselList = JSON.parse(f.imageJson) || []; //semi-medals报价轮播图
							this.routingId = f.id;
							//右侧默认分类图
							this.comeFromDZ.absoluteImg = res.data[0].imagePhoto;

							// business-cards 报价特殊处理
							if (this.pid == 671) {
								//默认选中唯一尺寸 其它默认参数
								const checkedSize = this.generalData.find((x) => x.paramType === "SIZE")?.childList[0];
								this.selectedData["Size"] = [checkedSize];
								const keys = this.generalData.map((x) => x?.paramName);
								const defSelectedKeys = ["Select Metal Color", "Select Imprint Color Qty", "Select Thickness", "Select Surface Finish", "Select Card Sleeves", "Select Turnaround Time & Comments"];
								defSelectedKeys.forEach((dk) => {
									if (keys.includes(dk)) {
										const step = this.generalData.find((x) => {
											return x.paramName === dk;
										});
										const stepParam = step.childList.find((x) => x.priceInfo.isCommonQuoteSelected);
										this.selectedData[dk] = stepParam ? [stepParam] : [];
									}
								});
								this.sizeId = checkedSize.priceInfo.id;
								let findIndex = this.generalData.findIndex((x) => x.paramName === "Advanced Options");
								this.generalData[findIndex].noShowDetail = true;
								if (this.$route.query.galleryPic) {
									try {
										const galleryPic = JSON.parse(this.$route.query.galleryPic);
										this.carouselList = galleryPic.map((x) => {
											return { url: x };
										});
									} catch (err) {
										this.carouselList = [{ url: this.$route.query.galleryPic }];
									}
								}
								this.updatePriceTable();
							}
						}
					} else {
						if (this.pid == 261 || this.pid == 286 || this.pid == 330) {
							let path = this.$route.path;
							let lastIndex = path.lastIndexOf("/", path.lastIndexOf("/") - 1);
							path = path.substring(lastIndex);
							let find = this.quoteStyleData.childList.find((q) => q.quoteRoutingName === path);
							if (find) {
								this.titleName = find.cateName;
								this.carouselList = JSON.parse(find.imageJson) || []; //medal-cuffinks报价轮播图
								this.routingId = find.id;
								this.selectedData[this.quoteStyleData.paramName] = [find];
								this.generalDataOldCateId = find.id;
								this.generalData = await this.getCateParam(find.id);
								//	this.generalDataOld = JSON.parse(JSON.stringify(this.generalData)); //解决数据污染
							} else {
								window.location.href = "/";
							}
						} else {
							//默认拿第一个分类的数据
							if (this.pid !== 671) {
								this.generalDataOldCateId = res.data[0].id;
								this.generalData = await this.getCateParam(res.data[0].id);
								//		this.generalDataOld = JSON.parse(JSON.stringify(this.generalData)); //解决数据污染
								//右侧默认分类图
								this.comeFromDZ.absoluteImg = res.data[0].imagePhoto;
								//分类只有一个 默认选中分类并且隐藏分类步骤
								if (this.quoteStyleData.childList.length === 1) {
									this.selectedData[this.quoteStyleData.paramName] = [res.data[0]];
								}
							}
						}
					}
					try {
						//不可选列表
						await this.getBanParamList(res.data[0].id);
					} catch (e) {}
					resolve(res.data);
				});
			});
		},

		//获取启用图标标签
		async getAppRingIconTagList() {
			const res = await getAppRingIconTagList(this.quotePid);
			this.semiMedalsIconTagList = res.data;
		},
		//获取启用图标列表
		async getSemiMedalsIconList() {
			let params = {
				cateId: this.quotePid,
				ringIconTagId: this.tagNameId ? this.tagNameId : "",
				page: 1,
				pageSize: 999,
			};
			const res = await getAppRingIconList(params);
			this.semiMedalsIconList = res.data.content;
		},

		//切换分类回填已选中过的参数
		backQuoteParams() {
			let newSelectedParams = {},
				generalData = deepClone(this.generalData),
				selectedData = deepClone(this.selectedData);
			//遍历总纲
			for (let i = 0; i < generalData.length; i++) {
				let item = generalData[i];
				let paramName = item.paramName;
				//判断是否存在选中的参数
				if (selectedData[paramName] && selectedData[paramName].length) {
					let newArr = [];
					//遍历选中的参数
					selectedData[paramName].forEach((pitem) => {
						let selectId = pitem.id;
						let findItemIndex = item.childList.findIndex((ccitem) => {
							return ccitem.id === selectId;
						});
						if (findItemIndex > -1) {
							pitem.priceInfo = item.childList[findItemIndex].priceInfo;
							item.childList[findItemIndex] = pitem;
							newArr.push(pitem);
						}
					});
					if (newArr.length) {
						newSelectedParams[paramName] = newArr;
					} else {
						newSelectedParams[paramName] = [];
					}
				} else {
					newSelectedParams[paramName] = [];
				}
			}
			//双向回填
			this.generalData = generalData;
			this.selectedData = newSelectedParams;
		},
		calcPrice() {
			if (!this.selectedData[this.quoteStyleData?.paramName]) {
				return;
			}
			this.cancelTokenSource?.cancel("cancel");
			this.cancelTokenSource2?.cancel("cancel");
			this.cancelTokenSource = this.$http.CancelToken.source();
			this.cancelTokenSource2 = this.$http.CancelToken.source();
			let priceParam = this.getPriceParam();
			let arr = [];
			if (priceParam.sizeId && priceParam.cateId) {
				arr.push({
					fn: calculateAll,
					cancelTokenSource: this.cancelTokenSource,
				});
				// if ((priceParam.quantity || priceParam.qtyDetailDTOS?.length) && priceParam.discountId) {
				// 	arr.push({
				// 		fn: calculate,
				// 		cancelTokenSource: this.cancelTokenSource2,
				// 	});
				// }
				if (priceParam.quantity || priceParam.qtyDetailDTOS?.length) {
					arr.push({
						fn: calculate,
						cancelTokenSource: this.cancelTokenSource2,
					});
				}
			}
			if (!arr.length) {
				return;
			}
			Promise.all(arr.map((fn) => fn.fn(priceParam, fn.cancelTokenSource)))
				.then((result) => {
					this.qtyList = result[0].data;
					this.priceInfo = result[1]?.data || "";
					if (this.canShowUpgradeDialog && result[1]) {
						this.showUpgradeDialog();
						this.canShowUpgradeDialog = false;
					}
				})
				.catch((err) => {
					console.log(err);
				});
		},
		getQuoteParam(type = "inquiry") {
			let selectedData = deepClone(this.selectedData),
				finaData = [],
				generalData = deepClone(this.generalData);
			if (this.attachment) {
				let index = generalData.findIndex((item) => {
					return item.paramName === "Select Packaging" || item.paramName === "Select Packing Options" || item.paramName === "Select Turnaround Time";
				});
				if (index > -1) {
					let attachmentData = {
						parentId: 0,
						paramName: "customAttachment",
						alias: this.lang.Attachment,
						childList: [
							{
								parentId: 1,
								id: "-1",
								paramName: "customAttachment",
								alias: this.lang.StandardMilitaryClutch,
							},
						],
					};
					generalData.splice(index, 0, attachmentData);
				}
			}
			for (let i = 0; i < generalData.length; i++) {
				let item = generalData[i];
				if (item.noShowDetail || item.paramName === "quoteCategory") {
					continue;
				}
				if (item.paramName === "qty" || item.paramName === "Quantity") {
					item.childList = [];
					finaData.push(item);
					continue;
				}
				if (item.paramName === "Upload Artwork & Comments") {
					item.childList = [];
					item.later = this.isUpload;
					finaData.push(item);
					continue;
				}
				//将选中参数里面的childlist置空
				if (selectedData[item.paramName] && selectedData[item.paramName].length) {
					selectedData[item.paramName].forEach((c) => {
						//尺寸选择类型报价重新赋值alias
						if (c.sizeAlias) {
							c.alias = c.sizeAlias;
						}
						c.childList = [];
						c.quantity = c.inputNum;
					});
				}
				item.childList = selectedData[item.paramName] || [];
				//medals报价特殊处理
				if ((this.pid === 45 || this.pid === 433) && item.paramName == "Ribbon") {
					item.childList.forEach((citem) => {
						citem.childList.forEach((ccitem) => {
							if (citem.paramName == "Stock Ribbon") {
								citem.sizeValue.childList[0].childList = [citem.colorValue];
								ccitem.childList = [citem.sizeValue];
							}
						});
					});
				}

				//semiMals alias 拼接颜色备注以及size备注
				if (this.pid === 312 && item.paramName == "Ribbon") {
					item.childList.forEach((citem) => {
						let alias = citem.alias;
						if (citem.paramName == "Stock Ribbon" || citem.paramName == "Custom Ribbon") {
							if (citem.colorValue && citem.colorValue.alias) {
								alias = alias + " , " + citem.colorValue.alias;
								if (citem.sizeValue && citem.sizeValue.alias) {
									alias = alias + " , " + citem.sizeValue.alias;
								}
							} else {
								if (citem.sizeValue && citem.sizeValue.alias) {
									alias = alias + " , " + citem.sizeValue.alias;
								}
							}
						}
						citem.alias = alias;
					});
				}

				if (this.pid === 330 && item.paramName === "Design Your Metal Signs") {
					if (this.onlyAddInquiry) {
						item.childList = [];
					} else {
						this.textConfig.forEach((textItem) => {
							item.childList.push({
								parentId: item.id,
								id: 0,
								alias: textItem.customText,
							});
						});
					}
				}

				// 戒指报价处理
				if (this.pid === 205 && item.paramName == "Top Design") {
					let num = 1;
					let files = this.oneUpload === true ? [] : this.setpOneData.files;
					item.childList[0].files = files;
					let map = {
						TopText1: this.setpOneData.text1,
						TopColor1: this.setpOneData.color1,
						TopText2: this.setpOneData.text2,
						TopColor2: this.setpOneData.color2,
						TopStoneColor1: this.setpOneData.stone1,
						TopStoneColor2: this.setpOneData.stone2,
					};
					for (let i in map) {
						let obj = {
							id: item.id,
							parentId: item.parentId,
							paramName: i,
							alias: i,
							childList: [
								{
									id: item.childList[0].id,
									parentId: item.childList[0].parentId,
									paramName: map[i],
									alias: map[i],
								},
							],
						};
						num += 1;
						finaData.push(obj);
					}
				} else if (this.pid === 205 && item.paramName == "Side Design") {
					if (item.childList[0].paramName == "Choose Your Design") {
						let num = 1;
						let arr = this.setpTwoData.files;
						arr = arr.filter(function (item) {
							return item.length > 0;
						});
						let filesTwo = this.twoUpload === true ? [] : arr;
						item.childList[0].files = item.childList[0].paramName == "Choose Your Design" ? filesTwo : "";
						let mapTwo = {
							SideText1: this.setpTwoData.text1,
							SideColor1: this.setpTwoData.color1,
							files: JSON.stringify(this.setpTwoData.files),
						};
						for (let i in mapTwo) {
							let obj = {
								id: item.id,
								parentId: item.parentId,
								paramName: i,
								alias: i,
								childList: [
									{
										id: item.childList[0].id,
										parentId: item.childList[0].parentId,
										paramName: mapTwo[i],
										alias: mapTwo[i],
									},
								],
							};
							num += 1;
							if (obj.paramName != "files") {
								finaData.push(obj);
							}
						}
						item.childList[0].remark = this.setpTwoData.remarks;
					}
				}
				finaData.push(item);

				//embroidered-patches 与 3d-embroidered-patches More Options步骤特殊色处理
				if ((this.pid === 107 || this.pid === 121) && item.paramName == "Additional Upgrades") {
					item.childList.forEach((citem) => {
						let tempObj = {
							id: "-1",
							parentId: "0",
							alias: `${citem.alias} ${this.lang.detail}`,
							childList: [],
						};
						if (citem.colorList && citem.colorList.length) {
							citem.colorList.forEach((ccitem) => {
								tempObj.childList.push({
									parentId: -1,
									id: "-1",
									alias: ccitem.code,
								});
							});
						}
						if (tempObj.childList.length) {
							finaData.push(tempObj);
						}
					});
				}
			}
			let files = [],
				classificationData = [],
				userUploadList = this.$route.query.userUploadList ? JSON.parse(this.$route.query.userUploadList) : [],
				artworkList = this.uploadArtworkList.concat(this.uploadList, this.attachFileList),
				designCanvas = [this.$route.query.designPic, this.$route.query.galleryPic, ...userUploadList].reduce((acc, value) => {
					if (value) {
						acc.push(value);
					}
					return acc;
				}, []);
			if (type == "cart") {
				artworkList = this.uploadArtworkList.concat(this.attachFileList);
			}
			if (artworkList.length > 0) {
				artworkList.forEach((item) => {
					if (item.secure_url === this.$route.query.designPic) {
						return;
					}
					item.secure_url && files.push(item.secure_url);
				});
			}
			if (this.pid == "205") {
				classificationData = this.templatesData.length == 0 ? this.quoteStyleData.childList[0] : this.templatesData;
			}
			//custom-engraved-cufflinks 新增textList字段属性
			let textList = [];
			if (this.pid === 763) {
				textList = this.leftTextArr.concat(this.rightTextArr).filter((item) => item.text);
			}
			return {
				classificationData: this.pid == "205" ? classificationData : selectedData[this.quoteStyleData.paramName][0],
				finaData: finaData,
				fontData: {
					fontImgCustom: files,
					comments: this.remark,
				},
				textList,
				designCanvas,
			};
		},

		getPriceParam() {
			const data = {
				cateId: this.routingId || "",
				discountId: "",
				packingIdList: [],
				paramIdList: [],
				projectName: this.projectName,
				quantity: this.pid == 205 ? "1" : "",
				sizeId: "",
				upgradesQtyDTO: [],
				qtyDetailDTOS: [],
				borderColorList: [],
			};
			let generalData = this.generalData,
				selectedData = this.selectedData;
			for (let i = 0; i < generalData.length; i++) {
				let item = generalData[i];
				let paramName = item.paramName;
				let selectedVal = selectedData[paramName];
				//顺序不能乱
				if (item.noShowDetail && item.paramName !== "quoteCategory") {
					continue;
				}
				if (paramName === "qty" || paramName === "Quantity") {
					data.quantity = Number(this.customQty);
					//棒球满增活动--gs-jj网站--tradingpins
					if (this.isFullReductionActivity && this.proId === 1) {
						data.giftQuantity = this.qtyChecked === true ? 30 : 0;
					}
					continue;
				}
				if ((this.pid === 300 || this.pid === 671) && paramName == "Size") {
					data.quantity = Number(this.customQty);
				}
				if (paramName === "Upload Artwork & Comments" && this.pid != 671) {
					continue;
				}
				if (!selectedVal?.length) {
					continue;
				}
				if (paramName === "quoteCategory") {
					data.cateId = selectedVal[0].id;
					continue;
				}
				selectedVal.forEach((citem) => {
					let paramType = citem.paramType;
					switch (paramType) {
						case "NORMAL":
							//medals报价特殊处理
							if ((this.pid === 312 || this.pid === 45 || this.pid === 433) && paramName === "Ribbon") {
								if (citem.colorValue && Object.keys(citem.colorValue).length != 0) {
									data.paramIdList.push(citem.colorValue.priceInfo.id);
								}
								if (citem.sizeValue && Object.keys(citem.sizeValue).length != 0) {
									data.paramIdList.push(citem.sizeValue.priceInfo.id);
								}
								data.paramIdList.push(citem.priceInfo.id);
							} else if (this.pid === 273 && item.paramName === "Patch Border") {
								let borderData = this.selectedParamsValue.colorValue;
								//Merrow
								if (borderData && Object.keys(borderData).length !== 0) {
									if (borderData.priceInfo) {
										data.paramIdList.push(borderData.priceInfo.id);
									}
								}else {
									//Heat Cut
									data.paramIdList.push(citem.priceInfo.id);

								}
							} else {
								data.paramIdList.push(citem?.priceInfo?.id);
								if (citem.extendStepValue) {
									data.paramIdList.push(citem?.extendStepValue?.priceInfo?.id);
								}
							}
							break;
						case "PINPACK":
							data.packingIdList.push(citem?.priceInfo?.id);
							break;
						case "QUANTITY":
							if (citem.inputNum && citem.inputNum > 0) {
								data.upgradesQtyDTO.push({
									quantity: citem.inputNum || 0,
									paramId: citem?.priceInfo?.id,
									isLanyard: 0,
								});
							}
							break;
						case "DISCOUNT":
							data.discountId = citem?.priceInfo?.id;
							break;
						case "SIZE":
							//metal-patches业务
							if (this.pid === 573) {
								data.sizeId = this.smallTypeModel.priceInfo.id;
							} else {
								data.sizeId = citem?.priceInfo?.id;
							}

							break;
						case "COLOR":
							if (citem.inputNum && citem.inputNum > 0) {
								data.qtyDetailDTOS.push({
									quantity: citem.inputNum || 0,
									paramType: "COLOR",
									paramId: citem?.priceInfo?.id,
									paramValue: "",
									giftQuantity: citem.giftQuantity ? citem.giftQuantity : 0,
								});
							}
							break;
						default:
							break;
					}
				});
			}
			if (!data.qtyDetailDTOS.length) {
				delete data.qtyDetailDTOS;
			}
			return data;
		},

		addInquiry() {
			if (this.presentedQuantity > 30) {
				scrollToViewCenter(document.getElementById("Plating"));
				return;
			}
			this.showMaskFn("addInquiry");
			if (this.maskName) {
				this.showArea = false;
				return false;
			}
			//如果是临时项目
			if (this.$store.state.isTemporary) {
				this.$store.commit("setMask", "temporary");
				return false;
			}
			this.$store.commit("setGoogleUploadAction", { addInquiry: "addInquiry", content_id: this.pid });
			this.infoDialogVisible = true;
		},

		getValueFun(val) {
			this.$gl.show();
			let quoteParam = this.getQuoteParam();
			let priceParam = this.getPriceParam();
			//询盘如果有期待时间，finaData手动添加数据
			if (val.expectTime) {
				quoteParam.finaData.push({
					parentId: 0,
					alias: "Expected Delivery Date",
					childList: [
						{
							parentId: 10000,
							alias: val.expectTime,
						},
					],
				});
			}
			let data = {
				platformProductId: this.platformProductId,
				proId: this.proId,
				email: "",
				productsName: this.productsName,
				quoteCateId: this.pid,
				quoteCateChildId: priceParam.cateId,
				isMobile: this.device === "mb" ? 1 : 0,
				buyType: 7,
				quotePriceParam: JSON.stringify(priceParam),
				quoteParam: JSON.stringify(quoteParam),
				...val,
				telephone: val.areaCode + "-" + val.telephone,
				isDs: this.isDs,
				isSmallQty: getIsSmallQty(findSelectDiscount(this.selectedData), this.$store.state.enableTurnaroundTimeCheck),
			};
			//棒球赠送总数
			if (this.isFullReductionActivity && this.proId === 1) {
				if (this.pid === 51) {
					data.giftQuantityTotal = this.qtyChecked === true ? 30 : 0;
				} else {
					data.giftQuantityTotal = this.presentedQuantity ? this.presentedQuantity : 0;
				}
			}
			this.otoEditInquiry(data);
		},

		otoEditInquiry(data) {
			if (this.isInquiry) {
				return false;
			}
			this.isInquiry = true;
			otoEditInquiry(data)
				.then((res) => {
					this.$gl.hide();
					this.inquiryId = res.data;
					this.$store.commit("setGoogleUploadAction", {
						addInquiryConfirm: "addInquiryConfirm",
						content_id: this.pid,
					});
					this.$store.commit("recordButtonClick", {
						buttonName: "submitInquiry",
						stepNumber: 999,
						stepName: "inquiry",
						action: "success"
					});
					this.$store.commit("setStepHistoryEmpty")
					let confirmFn = () => {
						this.$confirm(this.lang.p22, this.lang.p21, {
							confirmButtonText: this.lang.Confirm,
							type: "success",
							showCancelButton: false,
							center: true,
							customClass: "inquirySuccess",
							confirmButtonClass: "inquirySuccessBtn",
						}).finally(() => {
							if (this.isIframe) {
								let targetWindow = window.opener || window.parent;
								if (this.$route.query.isBack) {
									targetWindow.postMessage(
										{
											type: "closeDialog",
										},
										window.origin
									);
								} else {
									targetWindow.postMessage(
										{
											type: "toHome",
										},
										window.origin
									);
								}
							} else {
								window.location.href = "/";
							}
						});
					};
					if (this.isDs == 1) {
						confirmFn();
						return;
					}
					if (!this.uploadList.length && !this.uploadArtworkList.length && !this.recomendUpload.length) {
						let inquirySuccess = false;
						//Medals
						if (this.pid === 312 || this.pid === 45 || this.pid === 433) {
							if (this.RibbonBindValue.uploadList && this.RibbonBindValue.uploadList.length > 0) {
								inquirySuccess = true;
							}
							if (this.RibbonBindValue.files && this.RibbonBindValue.files.length > 0) {
								inquirySuccess = true;
							}
						}
						if (inquirySuccess) {
							confirmFn();
							return;
						}
						this.noFileDialog = true;
					} else {
						confirmFn();
					}
				}).catch(() => {
					this.$store.commit("recordButtonClick", {
						buttonName: "submitInquiry",
						stepNumber: 999,
						stepName: "inquiry",
						action: "failure"
					});
				})
				.finally(() => {
					this.isInquiry = false;
				});
		},

		async addCart() {
			this.showMaskFn("addCart");
			if (this.maskName) {
				this.showArea = false;
				return false;
			}
			if (this.presentedQuantity > 30) {
				scrollToViewCenter(document.getElementById("Plating"));
				return;
			}
			//如果是临时项目
			if (this.$store.state.isTemporary) {
				this.$store.commit("setMask", "temporary");
				return false;
			}
			if (this.loadAddCart) {
				return false;
			}
			this.loadAddCart = true;
			let quoteParam = this.getQuoteParam("cart");
			let priceParam = this.getPriceParam();
			let data = {
				proId: this.proId,
				uuid: this.isLogin ? null : this.userUUID,
				userId: this.isLogin ? this.userId : null,
				buyType: 7,
				quoteCateId: this.pid,
				isMobile: this.device === "mb" ? 1 : 0,
				quoteCateChildId: priceParam.cateId,
				quantity: this.customQty,
				quotePriceParam: JSON.stringify(priceParam),
				quoteParam: JSON.stringify(quoteParam),
				isDs: this.isDs,
				isOneDollarPens: 0,
				isSmallQty: getIsSmallQty(findSelectDiscount(this.selectedData), this.$store.state.enableTurnaroundTimeCheck),
			};
			//棒球满增活动
			if (this.isFullReductionActivity && this.proId === 1) {
				if (this.pid === 51) {
					data.giftQuantityTotal = this.qtyChecked === true ? 30 : 0;
				} else {
					data.giftQuantityTotal = this.presentedQuantity ? this.presentedQuantity : 0;
				}
			}
			if (this.$route.query.isBack) {
				data.id = this.$route.query.id;
				otoEditCart(data)
					.then((res) => {
						this.$toast.success(res.message);
						let targetWindow = window.opener || window.parent;
						targetWindow.postMessage(
							{
								type: "closeDialog",
								data: {
									isUpdate: 1,
								},
							},
							window.origin
						);
					})
					.finally(() => {
						setTimeout(() => {
							this.loadAddCart = false;
						}, 1000);
					});
			} else {
				otoAddCart(data, this.priceInfo)
					.then((res) => {
						this.$toast.success(res.message);
						this.$store.commit("setGoogleUploadAction", { addCart: "addCart", content_id: this.pid });
						this.$store.commit("recordButtonClick", {
							buttonName: "submitCart",
							stepNumber: 999,
							stepName: "cart",
							action: "success"
						});
						this.$store.commit("setStepHistoryEmpty")
						if (this.isIframe) {
							let targetWindow = window.opener || window.parent; // 目标窗口，可以是父窗口或打开当前窗口的窗口
							targetWindow.postMessage(
								{
									type: "toCart",
								},
								window.origin
							); // 发送消息
						} else {
							this.$router.push({
								path: "/cart",
							});
						}
					}).catch(()=>{
						this.$store.commit("recordButtonClick", {
							buttonName: "submitCart",
							stepNumber: 999,
							stepName: "cart",
							action: "failure"
						});
					})
					.finally(() => {
						setTimeout(() => {
							this.loadAddCart = false;
						}, 1000);
					});
			}
		},
		//根据paramType清空字段
		//参数1   DISCOUNT||默认   SIZE
		clearField(e = "DISCOUNT") {
			let findDiscount = this.generalData.find((item) => item.paramType === e);
			if (findDiscount) {
				let name = findDiscount.paramName;
				this.selectedData[name] = [];
			}
		},
		//根据paramName清空字段
		//参数1   Plating/Finish
		clearFieldName(e = "Plating/Finish") {
			let findDiscount = this.generalData.find((item) => item.paramName === e);
			if (findDiscount) {
				let name = findDiscount.paramName;
				this.selectedData[name] = [];
			}
		},
		clearDiscount() {
			if (this.isBackParams) {
				return;
			}
			let findDiscount = this.generalData.find((item) => item.paramType === "DISCOUNT");
			if (findDiscount) {
				let name = findDiscount.paramName;
				this.selectedData[name] = [];
			}
		},
		applyMediaQueries(allStepConfig) {
			const mediaQueries = this.extractMediaQueries(allStepConfig);
			const cssText = this.generateCSS(mediaQueries);
			const styleElement = document.createElement("style");
			styleElement.innerHTML = cssText;
			document.head.appendChild(styleElement);
		},
		extractMediaQueries(allStepConfig) {
			if (!allStepConfig) return;
			let mediaQueries = {};
			Object.entries(allStepConfig).forEach(([stepName, stepData]) => {
				if (stepData?.stepContentConfig?.mediaQueries) {
					Object.entries(stepData.stepContentConfig?.mediaQueries).forEach(([mediaQuery, rules]) => {
						if (!mediaQueries[mediaQuery]) {
							mediaQueries[mediaQuery] = [];
						}
						mediaQueries[mediaQuery].push({
							selector: `.stepWrap[id="${stepName}"] .stepContent`,
							rules: rules,
						});
					});
				}
			});

			return mediaQueries;
		},
		generateCSS(mediaQueries) {
			let cssText = "";
			Object.entries(mediaQueries).forEach(([mediaQuery, selectors]) => {
				const rulesText = selectors
					.map(({ selector, rules }) => {
						return `${selector} { ${Object.entries(rules)
							.map(([key, value]) => `${key}: ${value};`)
							.join(" ")} }`;
					})
					.join("\n");

				cssText += `${mediaQuery} {\n${rulesText}\n}\n`;
			});

			return cssText;
		},
		handleMessage(event) {
			let that = this;
			if (event.origin === window.origin) {
				// 验证消息来源
				let data = event.data;
				if (data.type === "closeDialog") {
					that.$confirm(that.lang.tip, that.lang.hint, {
						confirmButtonText: that.lang.Confirm,
						cancelButtonText: that.lang.Cancel,
						type: "warning",
						lockScroll: false,
					})
						.then(() => {
							let targetWindow = window.opener || window.parent;
							targetWindow.postMessage(
								{
									type: "closeDialog",
								},
								window.origin
							);
						})
						.catch(() => {});
				}
			}
		},
		handlePageExit() {
			this.$store.commit("recordPageExit");
		}
	},
	created() {
		this.getPriceData();
		this.debounceAddInquiry = debounce(this.addInquiry, 300);
		this.debounceCalcPrice = debounce(this.calcPrice, 300);
		getInfo({ id: this.pid }).then((res) => {
			this.cateData = res.data;
			this.platformProductId = res.data.platformProductId; //平台id
			this.carouselList = JSON.parse(res.data.imageJson) || [];
			this.isFullReductionActivity = !!res.data.isFullReductionActivity;
			this.giftQuantity1 = res.data.giftQuantity;
			this.satisfiedQuantity = res.data.satisfiedQuantity;
		});
	},
	async mounted() {
		this.$Bus.$on("clearDiscount", this.clearDiscount);
		this.isIframe = !!this.$route.query.type;
		this.windowWidth = document.documentElement.clientWidth;
		let query = this.$route.query;
		//预览
		if (process.env.VUE_APP_MODE === "Preview") {
			setTimeout(() => {
				this.showOtoDialog = true;
			}, 20000);
		}
		this.$nextTick(() => {
			const el = document.getElementById("foot");
			if (!el) {
				return false;
			}
			const swHeight = window.innerHeight;
			window.addEventListener("scroll", (e) => {
				const { top, height } = el.getBoundingClientRect();
				this.showRightArea = top + height / 2 > swHeight;
			});
		});
		//如果是织带直接返回
		const LANYARD_IDS = [12, 619, 629, 689, 695, 700, 701, 702, 703, 704, 705, 597, 621, 602, 747, 749];
		if (LANYARD_IDS.includes(this.pid)) {
			this.isLoading = false;
			return false;
		}
		await this.getQuoteCategory();
		this.getQuote = true;
		this.backQuoteParams();
		await this.getSemiMedalsIconList();
		//在不加步骤的情况下，order Summary添加attachment
		if (this.pid === 237 || this.pid === 240 || this.pid == 491 || this.pid == 504) {
			this.attachment = true;
			//手动插入值
			if (this.attachment) {
				this.selectedData["customAttachment"] = [
					{
						parentId: 1,
						id: "-1",
						paramName: "customAttachment",
						alias: this.lang.StandardMilitaryClutch,
					},
				];
			}
		}

		this.isLoading = false;
		//回填设计系统参数
		if (query.techId && query.quotePlatingId) {
			this.isDesignBack = true;
			this.isDs = 1;
			//回填工艺
			let findStyle = this.quoteStyleData.childList.find((item) => {
				return item.id == query.techId;
			});
			if (findStyle) {
				await this.selectQuoteParams(this.quoteStyleData, findStyle);
				if (this.generalData && this.generalData.length) {
					let findPlat, findPlatParent;
					this.generalData.forEach((item) => {
						if (item.childList && item.childList.length) {
							item.childList.forEach((ccitem) => {
								if (ccitem.id == query.quotePlatingId) {
									findPlat = ccitem;
									findPlatParent = item;
								}
							});
						}
					});
					if (findPlat && findPlatParent) {
						await this.selectQuoteParams(findPlatParent, findPlat);
					}
				}
			}
			this.isDesignBack = false;
		}
		//回填设计系统图片
		if (query.designPic) {
			this.isDs = 1;
			this.comeFromDZ.absoluteImg = this.$route.query.designPic;
			this.uploadArtworkList.push({
				original_filename: "deign.png",
				secure_url: this.$route.query.designPic,
			});
		}
		//回填galleryPic
		if (query.galleryPic) {
			this.isGallery = 1;
			this.comeFromDZ.absoluteImg = this.$route.query.galleryPic;
		}

		// 默认选中小类
		// 需要默认选中第一个小类报价PID
		const needCheckedChild = [300, 459];
		if (query.paramId || needCheckedChild.includes(this.pid)) {
			let id = query.paramId || this.quoteStyleData.childList[0].id;
			let findCate = this.quoteStyleData.childList.find((item) => {
				return item.id == id;
			});
			if (findCate) {
				this.isDesignBack = true;
				await this.selectQuoteParams(this.quoteStyleData, findCate);
				this.isDesignBack = false;
			}
		}
		//默认选中形状参数
		if (query.shapeId) {
			let parentParam = this.generalData.find((x) => x.paramName === "Select Shapes");
			if (parentParam) {
				let childParam = parentParam.childList.find((x) => x.id == query.shapeId);
				if (childParam) {
					await this.selectQuoteParams(parentParam, childParam, true);
				}
			}
		}
		//默认选中backing参数
		if (query.embroideredPatchesBackingId) {
			let parentParam = this.generalData.find((x) => ["Patch Backing", "Backing"].includes(x.paramName));
			if (parentParam) {
				let childParam = parentParam.childList.find((x) => x.id == query.embroideredPatchesBackingId);
				if (childParam) {
					await this.selectQuoteParams(parentParam, childParam, true);
				}
			}
		}
		//默认选中某个参数通用方法，路由传递 selectId 和 selectParentId
		if (query.selectId && query.selectParentId) {
			let parentParam = this.generalData.find((x) => x.id == query.selectParentId);
			if (parentParam) {
				let childParam = parentParam.childList.find((x) => x.id == query.selectId);
				if (childParam) {
					await this.selectQuoteParams(parentParam, childParam, true);
				}
			}
		}
		if (this.$route.query.isBack) {
			//isBackParams为true 代表参数正在回填
			this.isBackParams = true;
			await quoteBack(this);
			setTimeout(() => {
				this.isBackParams = false;
			}, 3000);
		}
		//报价初始化数据完成
		this.initData = true;
		//iframe事件监听
		window.addEventListener("message", this.handleMessage);
		// 添加页面退出监听
		window.addEventListener('beforeunload', this.handlePageExit);
		this.$store.commit("setQuoteRenderTime", new Date().getTime())
	},
	beforeDestroy() {
		this.$Bus.$off("clearDiscount");
		window.removeEventListener("message", this.handleMessage);
		// 移除页面退出监听
		window.removeEventListener('beforeunload', this.handlePageExit);
	},
};

export default quoteMixin;
