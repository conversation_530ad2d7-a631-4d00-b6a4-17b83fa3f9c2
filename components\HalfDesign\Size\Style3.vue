<template>
	<div class="mb-4" :class="stepData.styleName">
	    <slot name="stepText"></slot>
		<div class="step-content">
			<div class="step-text grey--text">{{ stepData.attributeTitle }}</div>
			<div class="step-wrap">
				<div class="step-item" :class="{ active: index === selectIndex, grayBack: stock == null || stock == 0 || parseInt(step.quantity) > parseInt(stock) }" v-for="(step, index) in priceArr" :key="index" @click="selectStep(step, index)">
					<div class="customInput">
						<div class="inputContent">
							<div>{{ step.quantity }}</div>
						</div>
						<div class="prepend"><CCYRate :price="step.discountPrice ? step.discountPrice : step.unitPrice"></CCYRate>&nbsp;{{ langSemiCustom.each }}</div>
					</div>
				</div>
				<!-- 自己输入价格 -->
				<div class="step-item" :class="{ active: selectIndex == -1 }" @click="selectStep2(-1)">
					<div class="customInput">
						<div class="inputContent">
							<input class="priceInput" :disabled="!stock" type="text" :placeholder="stock > 0 ? 'custom quantity' : 'Sold Out'" v-model="inputNum" @keyup="getInputPrice" @change="updatePrice" />
						</div>
						<div class="prepend"><CCYRate :price="inputPrice"></CCYRate>&nbsp;{{ langSemiCustom.each }}</div>
					</div>
				</div>
			</div>
		</div>
		<div class="errorTip" ref="errorTip">
			<v-alert dense outlined type="error"> {{  langSemiCustom.miniQty}} {{ productInfo.lowestPurchaseQuantity || 1 }} </v-alert>
		</div>
	</div>
</template>

<script>
export default {
	inject: ["getProductInfo", "getUnitPriceStep","getCurrentPrintMethod"],
	props: {
		stepData: {
			type: Object,
			default: () => ({}),
		},
	},
	data() {
		return {
			selectIndex: -2,
			selectItem: null,
			isInput: false,
			inputNum: "",
			inputPrice: "0.00",
			priceArr: [],
			// stock: this.stepData ? this.stepData.productParamList[0].stock : 9999,
			stock: -1,
			customInput: false
		};
	},
	watch: {
		stepData: {
			deep: true,
			handler(newVal) {
				if (newVal && this.stock == -1) {
					this.stock = this.stepData.productParamList[0].stock;
				}
			},
		},
		currentPrintMethod:{
			handler(newVal,oldVal){
				this.unitPriceStep();
				this.getInputPrice()
			}
		},
	},
	computed: {
		productInfo() {
			return this.getProductInfo();
		},
		langSemiCustom() {
			return this.$store.getters.lang?.semiCustom;
		},
		currentPrintMethod(){
			return this.getCurrentPrintMethod()
		}
	},
	methods: {
		getInputPrice() {
			this.customInput = false;
			if (!this.inputNum || this.inputNum == 0) {
				this.inputPrice = "0.00";
				return this.inputPrice;
			}
			this.formatNum();
			this.getPrice(this.priceArr);
		},
		getPrice(arr) {
			let len = arr.length;
			if(len == 0) return;
			let inputNum = parseInt(this.inputNum);
			if (inputNum > 0 && inputNum <= parseInt(arr[0].quantity)) {
				this.inputPrice = arr[0].discountPrice ? parseFloat(arr[0].discountPrice) : parseFloat(arr[0].unitPrice);
				return this.inputPrice;
			}
			if (inputNum >= parseInt(arr[len - 1].quantity)) {
				this.inputPrice = arr[len - 1].discountPrice ? parseFloat(arr[len - 1].discountPrice) : parseFloat(arr[len - 1].unitPrice);
				return this.inputPrice;
			}
			for (let i = 0; i < len; i++) {
				let num = arr[i];
				let num2 = arr[i + 1];
				if (!num2) break;
				if (inputNum >= parseInt(num.quantity) && inputNum < parseInt(num2.quantity)) {
					this.inputPrice = num.discountPrice ? parseFloat(num.discountPrice) : parseFloat(num.unitPrice);
					break;
				}
			}

			return this.inputPrice;
		},

		updatePrice() {
			let priceInputs = document.getElementsByClassName("priceInput");
			let sum = Array.from(priceInputs).reduce((acc, input) => acc + parseInt(input.value || 0), 0);
            let errDom = this.$refs.errorTip;
			if (sum < this.productInfo.lowestPurchaseQuantity) {
				errDom.style.display = "block";
			} else {
				errDom.style.display = "none";
			}
			this.stepData.productParamList[0].inputNum = this.inputNum;
			this.$emit("updatePrice");
		},
		formatNum() {
			this.inputNum = (this.inputNum + "").replace(/[^\d]/g, "");
			if (this.stock && this.stock > 0 && this.inputNum > this.stock) {
				this.inputNum = String(this.stock);
			}
			if (this.stock <= 0) {
				this.inputNum = "";
			}
			// if (this.inputNum > 0 && this.inputNum >= this.productInfo.lowestPurchaseQuantity) {
			// 	// this.updatePrice();
			// 	setTimeout(() => {
			// 		this.updatePrice();
			// 	}, 310);
			// }
		},
		selectStep(item, index) {
			// if (this.isInput || item.stock <= 0) {
			// 	return;
			// }
			if (Object.is(this.stock, null) || this.stock == 0 || parseInt(item.quantity) > this.stock) return;
			this.selectIndex = index;
			this.selectItem = item;
			this.stepData.productParamList[0].inputNum = item.quantity;
			this.$emit("selectStep", {
				type: this.stepData.attributeFlag,
				data: this.stepData.productParamList[0],
				id: this.stepData.id,
			});
		},
		selectStep2(index) {
			// if (this.isInput || item.stock <= 0) {
			// 	return;
			// }
			this.selectIndex = index;
			if (Object.is(this.stock, null) || this.stock == 0 || this.inputNum <= 0 || !this.inputNum) return;
			this.stepData.productParamList[0].inputNum = this.inputNum;
			this.$emit("selectStep", {
				type: this.stepData.attributeFlag,
				data: this.stepData.productParamList[0],
				id: this.stepData.id,
			});
		},
		getPriceSection(price) {
			this.priceArr = price;
		},
		updateQty(type) {
			if (this.stepData && this.stepData.productParamList.length > 0 && this.stepData.productParamList[0].stock) {
				this.stock = this.stepData.productParamList[0].stock;
				if(this.customInput && type != 'addCart') this.inputNum=''
				this.formatNum();
			}
		},
		unitPriceStep() {
			this.priceArr = this.currentPrintMethod&&JSON.parse(this.currentPrintMethod?.markupIncreasePrice);
		},
		setPriceArr(data) {
			this.priceArr = data;
		},
		setInputNum(num) {
			this.inputNum=num
			this.customInput=true
			this.formatNum()
			this.$forceUpdate()
			this.stepData.productParamList[0].inputNum = this.inputNum;
			this.$emit("updatePrice");
		}
	},
	created() {
	},
	mounted() {
		this.$Bus.$on("updateQty", this.updateQty);
		this.$Bus.$on("priceSection", this.getPriceSection);
		this.$Bus.$on("priceNum",this.setPriceArr);
		this.$Bus.$on("selectDefaultSizeStep",this.setInputNum)
	},
	beforeDestroy() {
		this.$Bus.$off("updateQty", this.updateQty);
		this.$Bus.$off("priceSection", this.getPriceSection);
		this.$Bus.$off("priceNum", this.setPriceArr);
		this.$Bus.$off("selectDefaultSizeStep",this.setInputNum)
	},
};
</script>

<style scoped lang="scss">
@import "~assets/css/half.scss";
input[disabled] {
	background: rgba(0, 0, 0, 0.03);
}

.grayBack {
	background-color: #a7a7a7;
	filter: grayscale(1);
	border-radius: 6px;
}
.step-content {
	display: grid;
	grid-template-columns: 100px 1fr;
	grid-gap: 10px;

	.step-wrap {
		display: grid;
		grid-gap: 10px;

		.step-item {
			overflow: hidden;
		}
	}

	@include respond-to(mb) {
		grid-template-columns: 1fr;
		grid-gap: 5px;

		.step-wrap {
			grid-gap: 5px;

			.step-item {
				overflow: hidden;
			}
		}
	}
}

.style1 .step-content {
	.step-wrap {
		grid-template-columns: repeat(5, 1fr);
		@include respond-to(ipad) {
			grid-template-columns: repeat(3, 1fr);
		}
		@include respond-to(mb) {
			grid-template-columns: repeat(3, 1fr);
		}
		.step-item.active {
			.customInput {
				.inputContent {
					color: $color-primary;
					border-color: $color-primary;
					border-width: 2px;
				}
				.prepend {
					// color: #ffffff;
					// background-color: $color-primary;
					border-color: $color-primary;
					border-width: 2px;
				}
			}
		}

		.customInput {
			display: flex;
			flex-direction: column;

			.inputContent {
				display: flex;
				justify-content: center;
				align-items: center;
				height: 33px;
				border-radius: 6px 6px 0 0;
				border: 1px solid #cccccc;

				input {
					flex: 1 1 auto;
					line-height: 20px;
					// padding: 6px;
					max-width: 100%;
					min-width: 0;
					width: 100%;
					text-align: center;
					&:focus {
						.inputContent {
							color: $color-primary;
							border-color: $color-primary;
							border-width: 2px;
						}
						.prepend {
							// color: #ffffff;
							// background-color: $color-primary;
							border-color: $color-primary;
							border-width: 2px;
						}
					}
				}
				input::placeholder {
					font-size: 12px;
				}
			}

			.prepend {
				display: flex;
				justify-content: center;
				align-items: center;
				flex: 0 0 auto;
				// width: 180px;
				word-break: break-word;
				padding: 7px;
				// background-color: $background-color2;
				transition: background-color 0.3s;
				text-align: center;
				border-bottom: 1px solid #cccccc;
				border-left: 1px solid #cccccc;
				border-right: 1px solid #cccccc;
				border-top: none;
				border-radius: 0 0 6px 6px;
				color: $color-primary;
			}

			.hint-text {
				margin-top: 4px;
				color: $gray-text;
				text-align: right;
				font-size: 14px;
			}
		}
	}
	@include respond-to(mb) {
		.step-wrap {
			.customInput {
				.inputContent {
					.prepend {
						flex: 0 0 70px;
						word-break: break-word;
						padding: 0;
					}

					input {
						flex: 1 1 auto;
						line-height: 20px;
						padding: 8px;
						max-width: 100%;
						min-width: 0;
						width: 100%;
					}
				}

				.hint-text {
					color: $gray-text;
					text-align: right;
					font-size: 14px;
				}
			}
		}
	}
}
</style>
