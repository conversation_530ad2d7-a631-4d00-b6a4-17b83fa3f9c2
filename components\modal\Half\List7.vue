<template>
	<div class="main modal-box" style="position: relative;overflow: initial !important;">
		<template v-if="isManage">
            <v-card height="300">
                <v-row justify="center" align="center" style="width: 100%; height: 100%; display: flex; margin: 0">
                    <div class="rightHeader">
                        <div v-for="(l, li) in modal.list" :key="li" :style="{ ...modal.cardBoxStyle, ...l.style }">
                            <EditDiv :tagName="l.title.tagName || 'h1'" v-model:content="l.title.value"
                                     @click="setModalType(l.title, modal.list, 'text')" v-if="l.title?.value"
                                     :style="{ ...modal.titleStyle }" />
                            <EditDiv :tagName="l.subTitle.tagName || 'p'" v-model:content="l.subTitle.value"
                                     @click="setModalType(l.subTitle, modal.list, 'text')" :style="{ ...modal.subTitleStyle }"
                                     v-if="l.subTitle?.value" />
                            <EditDiv :style="modal.textStyle" @click="setModalType(l.text, modal.list, 'text')"
                                     v-model:content="l.text.value" v-if="l.text?.value"></EditDiv>
                        </div>
                    </div>
                </v-row>
            </v-card>
        </template>
        <template v-else>
            <div class="pcTitle" v-for="(l, li) in modal.list" :key="li" :style="{ ...modal.cardBoxStyle, ...l.style }">
                <EditDiv class="title" :tagName="l.title.tagName || 'h1'" v-model:content="l.title.value"
                         @click="setModalType(l.title, modal.list, 'text')" v-if="l.title?.value"
                         :style="{ ...modal.titleStyle }" />
                <EditDiv class="subTitle" :tagName="l.subTitle.tagName || 'p'" v-model:content="l.subTitle.value"
                         @click="setModalType(l.subTitle, modal.list, 'text')" :style="{ ...modal.subTitleStyle }"
                         v-if="l.subTitle?.value" />
            </div>
            <div class="mbTitle" v-for="(l, li) in modal.list" :key="li" :style="{ ...modal.cardBoxStyle, ...l.style }">
                <EditDiv class="title"  :tagName="l.title.tagName || 'h1'" v-model:content="l.title.value"
                         @click="setModalType(l.title, modal.list, 'text')" v-if="l.title?.value"
                         :style="{ ...modal.titleStyle }" />
                <EditDiv class="subTitle" :tagName="l.subTitle.tagName || 'p'" v-model:content="l.subTitle.value"
                         @click="setModalType(l.subTitle, modal.list, 'text')" :style="{ ...modal.subTitleStyle }"
                         v-if="l.subTitle?.value" />
                <div class="mb_search">
                    <div class="right_Navigation">
                        <input type="text" placeholder="Product Keywords..." v-model="keyword">
                        <b class="icon-a-uspp-sousuozhuanhuan"></b>
                    </div>
                    <div class="left_Navigation" @click="collapse = true">
                        <svg t="1735549393288" class="icon" viewBox="0 0 1025 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="28543" width="13" height="13"><path d="M988.578182 93.843988h-113.750289a131.381583 131.381583 0 0 0-251.388136 0H35.350767a34.125086 34.125086 0 0 0-25.025063 9.668774 34.125086 34.125086 0 0 0 0 48.912624 34.125086 34.125086 0 0 0 25.025063 9.668775H625.714762a130.24408 130.24408 0 0 0 124.556566 93.843987 131.950334 131.950334 0 0 0 126.26282-93.843987h113.750288a34.693838 34.693838 0 0 0 25.025063-9.668775 34.125086 34.125086 0 0 0 0-48.912624 34.693838 34.693838 0 0 0-25.025063-9.668774z m-238.306854 93.275236a56.875144 56.875144 0 1 1 60.856404-56.875144 60.287653 60.287653 0 0 1-60.856404 56.875144zM988.578182 455.001152a34.693838 34.693838 0 0 1 25.025063 9.668775 34.125086 34.125086 0 0 1 0 48.912624 34.693838 34.693838 0 0 1-25.025063 9.668774H398.214186a131.381583 131.381583 0 0 1-251.956888 0h-113.750288A34.125086 34.125086 0 0 1 10.325704 511.876297a34.125086 34.125086 0 0 1 0-48.912624A34.125086 34.125086 0 0 1 35.350767 455.001152h113.750288A131.381583 131.381583 0 0 1 398.214186 455.001152zM273.657621 545.432632a56.875144 56.875144 0 1 0-60.856404-56.875145 59.718901 59.718901 0 0 0 60.856404 56.875145z m714.920561 316.225801a34.693838 34.693838 0 0 1 25.025063 9.668774 34.125086 34.125086 0 0 1 0 48.912624 34.693838 34.693838 0 0 1-25.025063 9.668774h-113.750289a131.381583 131.381583 0 0 1-251.956888 0H35.350767a34.125086 34.125086 0 0 1-25.025063-9.668774 34.125086 34.125086 0 0 1 0-48.912624 34.125086 34.125086 0 0 1 25.025063-9.668774H625.714762a131.381583 131.381583 0 0 1 251.388137 0z m-238.306854 93.275236a56.875144 56.875144 0 1 0-60.287653-56.875144 60.287653 60.287653 0 0 0 60.287653 56.875144z m0 0" p-id="28544"></path></svg>
                        <p>Filters</p>
                    </div>
                </div>
            </div>
            <div class="topNavigation" >
                <div class="left_Navigation">
                    <div class="left_grid">
                        <div>
                            Filters <span class="subtitle">({{hatsData.total}} Results)</span>
                        </div>
                        <div v-for="(item,index) in tableList" class="grid_div" :class="{ active:index === activeIndex }" :key="index" >
                            <div @click.stop="openSelectDiv(index)">{{item.nameEn}} <svg t="1736212108025" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="29692" width="13" height="13"><path d="M512 704c8.288 0 15.776-3.232 21.456-8.4l0.064 0.08 352-320-0.08-0.08c6.448-5.856 10.56-14.208 10.56-23.6a32 32 0 0 0-32-32c-8.288 0-15.76 3.232-21.456 8.4l-0.08-0.08L512 628.752 181.536 328.32l-0.08 0.08A31.776 31.776 0 0 0 160 320a32 32 0 0 0-32 32c0 9.376 4.112 17.744 10.544 23.6l-0.08 0.08 352 320 0.08-0.08c5.68 5.168 13.168 8.4 21.456 8.4z" p-id="29693"></path></svg></div>
                            <div class="selectDiv custom-scrollbar" v-show="showSelectDiv(index)" :class="item.nameEn == 'PRICE RANGE'?'overflow':''">
                                <div style="text-align: left;height: 100%;" class="custom-scrollbar">
                                    <!--								<label>-->
                                    <!--									<div class="checkbox__wrap">-->
                                    <!--										<input type="checkbox" id="checkAll" v-model="isAllChecked" />-->
                                    <!--										<span></span>-->
                                    <!--									</div>-->
                                    <!--									<span>all</span>-->
                                    <!--								</label>-->
                                    <div v-for="(eitem,eindex) in item.attributeList" :key="eindex">
                                        <div v-if="item.nameEn == 'PRICE RANGE'">
                                            <div style="margin-top: 50px">
                                                <v-range-slider class="custom-slider" @change="changeRange" v-model="priceRange" thumb-label :min="eitem.valueName" :max="eitem.remark" hide-details :step="0.01"></v-range-slider>
                                                <div style="display: flex">
                                                    <div style="flex: 1">
                                                        <CCYRate :price="eitem.valueName"></CCYRate>
                                                    </div>
                                                    <div style="flex: 1;text-align: end">
                                                        <CCYRate :price="eitem.remark"></CCYRate>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <label @change="selectList(eitem)" v-else>
                                            <div class="checkbox__wrap">
                                                <input type="checkbox" :id="eitem.id" v-model="selectedOptions" :value="eitem.valueName"  />
                                                <span></span>
                                            </div>
                                            <span v-if="item.nameEn == 'COLOR'">
											<img :src="eitem.imgUrl" alt="" style="width: 15px;height: 15px;vertical-align: sub"> {{ eitem.valueName }}
										</span>
                                            <span v-else>{{ eitem.valueName }}</span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <p @click.stop="closeSelectDiv(index)">
                                <svg t="1736212410884" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="30842" width="13" height="13"><path d="M1023.997 114.97 911.408 2.388 516.149 397.629 118.5 0 5.91 112.585l397.649 397.629L7.107 906.648l112.587 112.59 396.454-396.439 395.259 395.249 112.59-112.59L628.738 510.214 1023.997 114.97z" fill="#272636" p-id="30843"></path></svg>
                            </p>
                        </div>
                    </div>
                </div>
                <div class="right_Navigation">
                    <input type="text" placeholder="Product Keywords..." v-model="keyword">
                    <b class="icon-a-uspp-sousuozhuanhuan"></b>
                </div>
            </div>
            <div class="label">
                <div class="color_label" v-for="(item,index) in selectedOptions" :key="index">{{item}} <span @click="deleteSelectData(index,item)">×</span></div>
                <div v-if="selectedOptions.length > 0" style="display: inline-block;text-decoration: underline;cursor: pointer" @click="deleteSelectedOptions">Reset Filters</div>
            </div>
            <div class="bottomNavigation">
                <div class="Navigation_grid">
                    <div class="grid_div" v-for="(item,index) in hatsData.records" @click.stop="openMaskDetail(item.productRouting)" :key="item.id">
						<img v-if="item.showImgSrc" :src="item.showImgSrc" :alt="item.name" :title="item.name">
                        <img v-else :src="JSON.parse(item.productParamList[item.colorIndex].imgJson)[0].url" :alt="item.name" :title="item.name">
                        <div style="margin-top: 15px" class="bottom">
                            <div class="color_div">
                                <div class="color_grid" >
									<p v-show="item.sceneImg"
									   title="Gallery"
									   :class="{ active: item.colorIndex == -1 }"
									   class="color-circle colorYuan"
									   @click.stop="toSceneImg(item)"
									></p>
									<p class="colorYuan" :class="{ active:eindex === item.colorIndex }" v-for="(eitem,eindex) in (item.productParamList).slice(0,7)" :style="{backgroundColor:eitem.colorCode}" :key="eindex" @click.stop="changPlaces(item,eindex)"></p>
                                    <p v-if="item.productParamList.length > 7">+ {{item.productParamList.length - 7}}</p>
                                </div>
                            </div>
                            <div class="color_number">
                                {{ item.productSku }}
                            </div>
                            <p class="title">{{item.name}}</p>
                            <div class="color_bottom">
                                <div class="number">Low AS <CCYRate :price="item.lowestPrice"></CCYRate></div>
                                <div class="comment">
                                    <p><img src="https://static-oss.gs-souvenir.com/web/quoteManage/20241220/star_2056ncyJc3.png" alt=""></p>
                                    <p><img src="https://static-oss.gs-souvenir.com/web/quoteManage/20241220/star_2056ncyJc3.png" alt=""></p>
                                    <p><img src="https://static-oss.gs-souvenir.com/web/quoteManage/20241220/star_2056ncyJc3.png" alt=""></p>
                                    <p><img src="https://static-oss.gs-souvenir.com/web/quoteManage/20241220/star_2056ncyJc3.png" alt=""></p>
                                    <p><img src="https://static-oss.gs-souvenir.com/web/quoteManage/20241220/star_2056ncyJc3.png" alt=""></p>
                                    <p><span>{{ item.commentLevel }} ({{ item.commentNum }})</span></p>
                                </div>
                            </div>
                            <div class="color_number mb">
                                {{ item.productSku }}
                            </div>
                            <div class="color_div mb">
								<div class="color_div">
									<div class="color_grid" >
										<p v-show="item.sceneImg"
										   title="Gallery"
										   :class="{ active: item.colorIndex == -1 }"
										   class="color-circle colorYuan"
										   @click.stop="toSceneImg(item)"
										></p>
										<p class="colorYuan" :class="{ active:eindex === item.colorIndex }" v-for="(eitem,eindex) in (item.productParamList).slice(0,7)" :style="{backgroundColor:eitem.colorCode}" :key="eindex" @click.stop="changPlaces(item,eindex)"></p>
										<p v-if="item.productParamList.length > 7">+ {{item.productParamList.length - 7}}</p>
									</div>
								</div>
                            </div>

                        </div>
                        <div class="collect" @click.stop="goCollection($event, item)">
                            <svg v-if="!item.isCollection" t="1734680655660" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="32947" width="13" height="13"><path d="M908.8 214.4c-9.6-12.8-19.2-22.4-28.8-32-112-115.2-230.4-105.6-342.4-16-9.6 6.4-19.2 16-28.8 25.6-9.6-9.6-19.2-16-28.8-25.6-112-86.4-230.4-99.2-342.4 16-9.6 9.6-19.2 19.2-25.6 32-134.4 195.2-60.8 387.2 137.6 560 44.8 38.4 89.6 73.6 137.6 102.4 16 9.6 32 19.2 44.8 28.8 9.6 6.4 12.8 9.6 19.2 9.6 3.2 3.2 6.4 3.2 12.8 6.4 3.2 3.2 9.6 3.2 16 6.4 25.6 6.4 64 3.2 89.6-12.8 3.2 0 9.6-3.2 16-9.6 12.8-6.4 28.8-16 44.8-28.8 48-28.8 92.8-64 137.6-102.4C969.6 598.4 1043.2 406.4 908.8 214.4zM736 732.8c-41.6 35.2-86.4 70.4-131.2 99.2-16 9.6-28.8 19.2-44.8 25.6-6.4 3.2-12.8 6.4-16 9.6-6.4 3.2-16 6.4-25.6 9.6-3.2 0-6.4 0-9.6 0-6.4 0-12.8 0-16 0-3.2 0-3.2 0-3.2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0-3.2 0-3.2-3.2-3.2 0-6.4-3.2-9.6-3.2-3.2-3.2-9.6-6.4-16-9.6-12.8-6.4-28.8-16-44.8-25.6-44.8-28.8-89.6-60.8-131.2-99.2-179.2-160-243.2-323.2-131.2-489.6 6.4-9.6 16-16 22.4-25.6 89.6-96 182.4-86.4 275.2-12.8 9.6 6.4 16 12.8 22.4 19.2 0 0 0 0 0 0l28.8 32c3.2 3.2 3.2 3.2 6.4 6.4 0 0 0 0 0 0l0 0c3.2-3.2 9.6-9.6 16-16 12.8-12.8 25.6-25.6 41.6-38.4 92.8-73.6 185.6-83.2 275.2 12.8 6.4 9.6 16 16 22.4 25.6C982.4 406.4 918.4 572.8 736 732.8z" p-id="32948"></path></svg>
                            <svg v-else t="1735519444311" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="24718" width="13" height="13"><path d="M512 901.746939c-13.583673 0-26.122449-4.179592-37.093878-13.061225-8.881633-7.314286-225.697959-175.020408-312.424489-311.379592C133.746939 532.37551 94.040816 471.24898 94.040816 384.522449c0-144.718367 108.146939-262.269388 240.326531-262.269388 67.395918 0 131.657143 30.82449 177.632653 84.636735 45.453061-54.334694 109.191837-84.636735 177.110204-84.636735 132.702041 0 240.326531 117.55102 240.326531 262.269388 0 85.159184-37.093878 143.673469-67.395919 191.216327l-1.044898 1.567346c-86.726531 136.359184-303.542857 304.587755-312.424489 311.379592-10.44898 8.359184-22.987755 13.061224-36.571429 13.061225z" fill="#E5404F" p-id="24719"></path></svg>
                        </div>
                        <div v-if="item.cornerLabel?.id">
                            <p class="hot" v-if="item.cornerLabel.value == 'Hot'">Hot</p>
                            <p class="hot" v-else style="background: linear-gradient(87deg, #FF6C00, #FF9833);">Popular</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="left_collapse" v-if="collapse">
                <div style="position: relative;height: 100%;width: 100%">
                    <div class="collapse">
                        <p class="title">Filters <span>({{hatsData.total}} Results)</span></p>
                        <div class="label">
                            <div class="label_div" v-for="(item,index) in selectedOptions" :key="index">{{item}} <span @click="deleteSelectData(index,item)">×</span></div>
                            <div v-if="selectedOptions.length > 0" style="cursor: pointer;vertical-align: middle;line-height: 32px;font-family: Roboto, Roboto;font-weight: 400;font-size: 14px;color: #333333;" @click="deleteSelectedOptions">
                                <svg style="vertical-align: middle" t="1735803487608" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="28715" width="13" height="13"><path d="M202.666667 256h-42.666667a32 32 0 0 1 0-64h704a32 32 0 0 1 0 64H266.666667v565.333333a53.333333 53.333333 0 0 0 53.333333 53.333334h384a53.333333 53.333333 0 0 0 53.333333-53.333334V352a32 32 0 0 1 64 0v469.333333c0 64.8-52.533333 117.333333-117.333333 117.333334H320c-64.8 0-117.333333-52.533333-117.333333-117.333334V256z m224-106.666667a32 32 0 0 1 0-64h170.666666a32 32 0 0 1 0 64H426.666667z m-32 288a32 32 0 0 1 64 0v256a32 32 0 0 1-64 0V437.333333z m170.666666 0a32 32 0 0 1 64 0v256a32 32 0 0 1-64 0V437.333333z" fill="#000000" p-id="28716"></path></svg>
                                clear All
                            </div>
                        </div>
                        <div
                                v-for="(item, index) in tableList"
                                :key="index"
                                class="accordion-item"
                                :class="{ 'active': itemIndex === index }"
                        >
                            <p class="title" @click.stop="openSelectDiv(index)">{{item.nameEn}}</p>
                            <span class="arrow" @click="toggleAccordion(index)" v-if="itemIndex === index"><svg t="1735559349252" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="25721" width="40" height="40"><path d="M484.778667 305.194667a38.4 38.4 0 0 1 54.314666 0l307.712 307.712a38.4 38.4 0 1 1-54.272 54.314666L511.914667 386.56l-280.576 280.618667a38.4 38.4 0 1 1-54.272-54.314667l307.712-307.712z" fill="#333333" p-id="25722"></path></svg></span>
                            <span class="arrow" @click="toggleAccordion(index)" v-else><svg t="1735559387253" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="25887" width="40" height="40"><path d="M484.764309 718.806576a38.4 38.4 0 0 0 54.3058 0l307.732872-307.732871a38.4 38.4 0 1 0-54.305801-54.305801L511.887039 637.378045 231.337238 356.767904a38.4 38.4 0 0 0-54.305801 54.305801l307.732872 307.732871z" fill="#333333" p-id="25888"></path></svg></span>
                            <div v-if="itemIndex === index" class="selectDiv">
                                <div style="text-align: left">
                                    <!--								<label>-->
                                    <!--									<div class="checkbox__wrap">-->
                                    <!--										<input type="checkbox" id="checkAll" v-model="isAllChecked" />-->
                                    <!--										<span></span>-->
                                    <!--									</div>-->
                                    <!--									<span>all</span>-->
                                    <!--								</label>-->
                                    <div v-for="(eitem,eindex) in item.attributeList" :key="eindex" @change="selectList(eitem)">
                                        <div v-if="item.nameEn == 'PRICE RANGE'">
                                            <div style="margin-top: 50px">
                                                <v-range-slider class="custom-slider" @change="changeRange" v-model="priceRange" thumb-label :min="eitem.valueName" :max="eitem.remark" hide-details :step="0.01"></v-range-slider>
                                                <div style="display: flex">
                                                    <div style="flex: 1">
                                                        <CCYRate :price="eitem.valueName"></CCYRate>
                                                    </div>
                                                    <div style="flex: 1;text-align: end">
                                                        <CCYRate :price="eitem.remark"></CCYRate>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <label v-else>
                                            <div class="checkbox__wrap">
                                                <input type="checkbox" :id="eitem.id" v-model="selectedOptions" :value="eitem.valueName"  />
                                                <span></span>
                                            </div>
                                            <span v-if="item.nameEn == 'COLOR'">
											<img :src="eitem.imgUrl" alt="" style="width: 15px;height: 15px;vertical-align: sub"> {{ eitem.valueName }}
										</span>
                                            <span v-else>{{ eitem.valueName }}</span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <p style="position: absolute;top: 0.5%;right: 15%" @click="collapse = false">
                        <svg t="1735559534672" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="27693" width="25" height="25"><path d="M240.512 180.181333l271.530667 271.488 271.530666-271.488a42.666667 42.666667 0 0 1 56.32-3.541333l4.010667 3.541333a42.666667 42.666667 0 0 1 0 60.330667l-271.530667 271.530667 271.530667 271.530666a42.666667 42.666667 0 0 1-56.32 63.872l-4.010667-3.541333-271.530666-271.530667-271.530667 271.530667-4.010667 3.541333a42.666667 42.666667 0 0 1-56.32-63.872l271.488-271.530666-271.488-271.530667a42.666667 42.666667 0 0 1 60.330667-60.330667z" fill="#000000" p-id="27694"></path></svg>
                    </p>
                </div>
            </div>
            <div class="viewMore">
                <div title="view more custom hats" @click="clickViewMore">
                    VIEW MORE
                </div>
            </div>
        </template>
	</div>
</template>

<script>
import {addCollection, getAppLabelAttributeList, getProductList,deleteConllectionByUserId} from "@/api/web";
export default {
	components: {},

	props: ["cateId", "data", "parentCateId", "halfCateDTO", "isStockPage", "name"],
	data() {
		return {
			isSceneImg: false,
            isManage: false,
			selected: '',
			styleDiv:false,
			materialDiv:false,
			priceDiv:false,
			colorDiv:false,
			sortbyDiv:false,
			DeliveryDiv:false,
			colorIndex:0,
			activeIndex:null,
			pageSize:16,
			page:1,
			selectedOptions: [],
			message:'',
			customKeyword:'',
			tableList:[],
			selectData:[],
			hatsData:{},
			attributeValueIds:[],
			keyword:'',
			collapse:false,
			itemIndex: null,
			priceRange:[],
            modal: {
                style: {},
                type: {},
                ...this.data,
            }
		};
	},
	computed: {
		canvas() {
			return canvas
		},
		isDefault() {
			return this.$store.state.design.isDefault;
		},
		langDesign() {
			return this.$store.getters.lang?.design;
		},
		getSwitchProBtnText() {
			if (this.text == 1) {
				return this.langDesign.chooseProText;
			} else {
				return this.langDesign.changeProText;
			}
		},
		userId() {
			return this.$store.state.userInfo?.id;
		},
		isLogin() {
			return this.$store.getters.isLogin;
		},
		isMobile() {
			return this.$store.state.device === "mb";
		},
	},
	watch:{
		selectData(newItems, oldItems) {
			let arr = []
			if (newItems.length > 0) {
				newItems.forEach(item => {
					this.selectedOptions.forEach(eitem => {
						if (item.valueName == eitem) {
							arr.push(item.id)
						}
					})
				})
			}
			this.attributeValueIds = arr
			this.getProduct().then(res => {
				res.data.records.forEach((item) => {
					item.colorIndex = 0
				})
				let hatsData = res.data;
				hatsData.records = this.setDefaultShowImg(hatsData.records);
				this.hatsData = hatsData
			})
		},
		keyword(newItems, oldItems) {
			console.log(newItems);
			this.getProduct().then(res => {
				res.data.records.forEach((item) => {
					item.colorIndex = 0
				})
				let hatsData = res.data;
				hatsData.records = this.setDefaultShowImg(hatsData.records);
				this.hatsData = hatsData
			})
		},
	},
	created() {

	},
	methods:{
        setModalType(target, targetArray, clickType, event, other) {
            this.$setModal(this, target, targetArray, clickType, event, other);
        },
		openSelectDiv(index){
			this.activeIndex = index
			// if (index == 0) {
			// 	this.styleDiv = !this.styleDiv
			// }else if (index == 1) {
			// 	this.materialDiv = !this.materialDiv
			// }else if (index == 2) {
			// 	this.priceDiv = !this.priceDiv
			// }else if (index == 3) {
			// 	this.colorDiv = !this.colorDiv
			// }else if (index == 4) {
			// 	this.sortbyDiv = !this.sortbyDiv
			// }else if (index == 5) {
			// 	this.DeliveryDiv = !this.DeliveryDiv
			// }
		},
		closeSelectDiv(index) {
			this.activeIndex = null
		},
		showSelectDiv(index){
			if (index == 0) {
				return this.styleDiv
			}else if (index == 1) {
				return this.materialDiv
			}else if (index == 2) {
				return this.priceDiv
			}else if (index == 3) {
				return this.colorDiv
			}else if (index == 4) {
				return this.sortbyDiv
			}else if (index == 5) {
				return this.DeliveryDiv
			}
		},
		getAppLabelAttributeList(tagData = "") {
			return new Promise((resolve) => {
				getAppLabelAttributeList(
					{
						categoryId: this.parentCateId || this.cateId,
						childCategoryId: this.customKeyword ? "" : this.parentCateId ? this.cateId : null,
						isLabel: 1,
						attributeValueIds:this.attributeValueIds,
						keyWord: this.keyword,
					},
					tagData
				).then((res) => {
					resolve(res);
				});
			});
		},
		getProduct(customPage) {
			return new Promise((resolve) => {
				getProductList({
					categoryId: this.parentCateId || this.cateId,
					childCategoryId: this.customKeyword ? "" : this.parentCateId ? this.cateId : null,
					page: this.page,
					pageSize: this.pageSize,
					keyWord: this.keyword,
					attributeValueIds:this.attributeValueIds.length>0?this.attributeValueIds.join(','):'',
					userId: this.isLogin ? this.userId : null,
					sorts: this.sorts,
					priceStart: this.priceRange[0],
					priceEnd: this.priceRange[1],
					productType: this.isStockPage,
				})
					.then((res) => {
						resolve(res);
					})
					.catch((error) => {
						if (error.name === "AbortError") {
							console.log("Request canceled:", error.message);
						}
					});
			});
		},
		selectList(item){
			this.selectData.push(item)
			this.selectData = this.selectData.filter((obj, index, self) =>
				index === self.findIndex((t) => t.id === obj.id))

		},
		deleteSelectData(index,item) {
			this.selectedOptions.splice(index,1)
			this.attributeValueIds.splice(index,1)
			this.getProduct().then(res => {
				res.data.records.forEach((item) => {
					item.colorIndex = 0
				})
				let hatsData = res.data;
				hatsData.records = this.setDefaultShowImg(hatsData.records);
				this.hatsData = hatsData
			})
		},
		toSceneImg(item){
			item.showImgSrc = item.sceneImg;
			item.colorIndex = -1;
		},
		changPlaces(item,index) {
			console.log(index);
			item.showImgSrc = "";
			item.colorIndex = index
		},
		goCollection(e, item) {
			e.stopPropagation();
			e.preventDefault();
			let isCollection = item.isCollection;
			if (!this.isLogin) {
				this.$store.commit("setMask", "login");
				return;
			}
			if (isCollection) {
				deleteConllectionByUserId({
					userId: this.userId,
					productId: item.id,
				}).then((res) => {
					item.isCollection = false;
					this.$forceUpdate();
				});
			} else {
				addCollection({
					userId: this.userId,
					website: 1,
					cateId: this.parentCateId || this.cateId,
					productId: item.id,
				}).then((res) => {
					item.isCollection = true;
					this.$forceUpdate();
				});
			}
		},
		toggleAccordion(index) {
			this.itemIndex = this.itemIndex === index ? null : index;
		},
		openMaskDetail(url) {
			if(this.isDialog){
				this.$emit("toDetail",url);
			}else{
                if(location.pathname.indexOf('best-sellers')>-1||location.pathname.indexOf('personalized-keychains')>-1||location.pathname.indexOf('fast-shipping-custom-keychains')>-1){
                    window.open(url)
                }else{
					let data = {
						modal: "modalQuoteHalfDetail",
						name: url,
					};
					this.$store.commit("setMask", data);
				}
			}
		},
		changeRange(){
			this.getProduct().then(res => {
				res.data.records.forEach((item) => {
					item.colorIndex = 0
				})
				let hatsData = res.data;
				hatsData.records = this.setDefaultShowImg(hatsData.records);
				this.hatsData = hatsData
			})
		},
		deleteSelectedOptions(){
			this.selectedOptions = []
			this.attributeValueIds = []
			this.getProduct().then(res => {
				res.data.records.forEach((item) => {
					item.colorIndex = 0
				})
				let hatsData = res.data;
				hatsData.records = this.setDefaultShowImg(hatsData.records);
				this.hatsData = hatsData
			})
		},
		handleCollection(data){
			this.hatsData.records.forEach((item,index) => {
				if (item.id == data.productId) {
					data.type == "collection"?item.isCollection = true:item.isCollection = false
				}
			})
		},
		clickViewMore(){
			this.pageSize += 16
			this.getProduct().then(res => {
				res.data.records.forEach((item) => {
					item.colorIndex = 0
				})
				let hatsData = res.data;
				hatsData.records = this.setDefaultShowImg(hatsData.records);
				this.hatsData = hatsData
			})
		},
		setDefaultShowImg(list) {
			if (!list) {
				return;
			}
			if (this.isStockPage && this.$store.state.proTheme == "11") {
				list.forEach((item) => {
					item.showImgSrc = item.sceneImg;
					item.colorIndex = -1;
				});
				return list;
			}
			let colorItem = this.colorItem,
				colorId;
			if (colorItem) {
				colorId = colorItem.id;
			}
			list.forEach((item) => {
				if (item.productParamList && item.productParamList.length > 0) {
					let productParamList = item.productParamList;
					let findColorIndex = productParamList.findIndex((citem) => citem.attributeValueId === colorId);
					if (findColorIndex > -1) {
						try {
							item.showImgSrc = JSON.parse(productParamList[findColorIndex].imgJson)[0]?.url;
							item.colorIndex = findColorIndex;
						} catch (e) { }
					} else {
						if (item.sceneImg) {
							item.showImgSrc = item.sceneImg;
							item.colorIndex = -1;
						} else {
							try {
								item.showImgSrc = JSON.parse(productParamList[0].imgJson)[0]?.url;
								item.colorIndex = 0;
							} catch (e) { }
						}
					}
				} else {
					item.showImgSrc = item.sceneImg;
					item.colorIndex = -1;
				}
			});
			return list;
		},
	},
	mounted(){
		if (process.env.isManage) {
			this.isManage = true;
			return false;
		}
		this.$Bus.$on("collectionProduct", this.handleCollection);
		this.getAppLabelAttributeList().then(res => {
			this.tableList = res.data
			this.priceRange[0] = this.tableList[4].attributeList[0].valueName
			this.priceRange[1] = this.tableList[4].attributeList[0].remark
		})
		this.getProduct().then(res => {
			res.data.records.forEach((item) => {
				item.colorIndex = 0
			})
			let hatsData = res.data;
			hatsData.records = this.setDefaultShowImg(hatsData.records);
			this.hatsData = hatsData
		})
	},
}
</script>

<style scoped lang="scss">
	.main {
		margin: 0 auto;
	}
	.pcTitle {
		@include respond-to(mb) {
			display: none;
		}
		.title {
			text-align: center;
			font-family: Calibri !important;
			font-weight: bold;
			font-size: 36px !important;
			color: #333333;
		}
		.subTitle {
			font-family: Calibri;
			font-weight: 400;
			font-size: 16px;
			color: #333333;
			margin: 15px 0;
			text-align: center;
		}
	}
	.mbTitle {
		display: none;
		@include respond-to(mb) {
			display: block;
			.title {
				font-family: Roboto !important;
				font-weight: bold;
				font-size: 18px !important;
				color: #333333;
				text-align: center;
			}
			.subTitle {
				font-family: Arial;
				font-weight: 400;
				font-size: 12px;
				color: #333333;
				text-align: center;
				margin: 10px 0;
			}
			.mb_search {
				background-color: #fff;
				padding: 10px;
				.right_Navigation {
					display: inline-block;
					width: 70%;
					vertical-align: middle;
					text-align: left;
					position: relative;
					input {
						-webkit-appearance: none;
						background: #F1F2F6;
						background-image: none;
						border: 1px solid #dcdfe6;
						box-sizing: border-box;
						color: #000;
						display: inline-block;
						vertical-align: middle;
						font-size: inherit;
						height: 36px;
						outline: none;
						padding: 0 15px;
						transition: border-color .2s cubic-bezier(.645,.045,.355,1);
						width: 85%;
						font-size: 14px;
					}
					b {
						display: inline-block;
						position: absolute;
						font-size: 14px;
						right: 55px;
						top: 50%;
						transform: translateY(-50%);
					}
				}
				.left_Navigation {
					width: 25%;
					display: inline-block;
					vertical-align: middle;
					text-align: left;
					padding-left: 11%;
					p {
						font-family: Arial;
						font-weight: 400;
						font-size: 12px;
						color: #333333;
						text-align: left;
						display: inline-block;
						margin: 0;
					}
				}
			}
		}
	}
	.topNavigation {
		@include respond-to(mb) {
			display: none;
		}
		background-color: #EBEBEB;
		height: 50px;
		line-height: 50px;
		font-size: 0;
		position: sticky !important;
		z-index: 999;
		top: 0px;
		.left_Navigation {
			display: inline-block;
			width: 82%;
			vertical-align: middle;
			.left_grid {
				display: grid;
				grid-template-columns: repeat(7, 1fr);
				font-family: Calibri, Calibri;
				font-weight: 400;
				font-size: 18px;
				color: #333333;
				text-align: center;
				font-style: normal;
				text-transform: none;
				div {
					.subtitle {
						font-family: Calibri, Calibri;
						font-weight: normal;
						font-size: 14px;
						color: #A7A7A7;
						line-height: 16px;
						font-style: italic;
						text-transform: none;
					}
					span {
						font-size: 10px;
					}
				}
				.active {
					color:#D24600 !important;
					.selectDiv {
						display: block !important;
					}
					p {
						display: block !important;
					}
				}
				.grid_div {
					cursor: pointer;
					position: relative;

					.selectDiv {
						position: absolute;
						background: #FFFFFF;
						box-shadow: 2px 6px 6px 0px rgba(0,0,0,0.15);
						border-radius: 0px 0px 0px 0px;
						border: 1px solid #DBDBDB;
						padding: 20px;
						border-radius: 6px;
						left: 35px;
						width: 100%;
						top: 50px;
						z-index: 99;
						height: 300px;
						overflow-y: auto;
						&:after {
							position: absolute;
							content: '';
							display: block;
							width: 0;
							height: 0;
							border-color: transparent;
							border-style: solid;
							top: -12px;
							left: 35px;
							margin-left: -6px;
							border-top-width: 0;
							border-bottom-color: #fff;
							border-width: 6px;
							z-index: 100;
						}
						input[type="checkbox"]:checked {
							appearance: none;
						}
						input[type="checkbox"]:checked+span {
							border:none;
							background: url("https://static-oss.gs-souvenir.com/web/quoteManage/20241219/gouxuan_1_2055hiMG3i.png") no-repeat;
						}
						label {
							font-family: Calibri, Calibri;
							font-weight: 400;
							font-size: 14px;
							color: #666666;
							text-align: left;
							font-style: normal;
							text-transform: none;
							vertical-align: middle;
							span {
								margin-left: 5px;
								vertical-align: middle;
								font-family: Calibri, Calibri;
								font-weight: 400;
								font-size: 14px;
								color: #666666;
								i {
									display: inline-block;
									vertical-align: middle;
									border-radius: 50%;
									width: 15px;
									height: 15px;
								}
							}
						}
						.checkbox__wrap {
							display: inline-block;
							position: relative;
							width: 19px;
							height: 13px;
							vertical-align: middle;
						}
						.checkbox__wrap input {
							opacity: 0;
						}
						.checkbox__wrap span {
							position: absolute;
							inset: 0;
							border-radius: 2px 2px 2px 2px;
							border: 1px solid #666666;
						}
						.checkbox__wrap input:checked + span {

						}
					}
					.overflow {
						.custom-scrollbar {
							overflow-y: initial !important;
						}
					}
					p {
						position: absolute;
						font-size: 25px !important;
						right: 0;
						font-size: 22px;
						right: -25px;
						top: 40px;
						color: #333;
						z-index: 99;
						display: none;
					}
				}
			}

		}
		.right_Navigation {
			display: inline-block;
			width: 17%;
			vertical-align: middle;
			text-align: right;
			position: relative;
			input {
				-webkit-appearance: none;
				background-color: #fff;
				background-image: none;
				border-radius: 4px;
				border: 1px solid #dcdfe6;
				box-sizing: border-box;
				color: #000;
				display: inline-block;
				vertical-align: middle;
				font-size: inherit;
				height: 36px;
				outline: none;
				padding: 0 15px;
				transition: border-color .2s cubic-bezier(.645,.045,.355,1);
				width: 85%;
				border-radius: 20px;
				font-size: 14px;
			}
			b {
				display: inline-block;
				position: absolute;
				font-size: 14px;
				right: 20px;
				top: 50%;
				transform: translateY(-50%);
			}
		}
	}
	.bottomNavigation {
		.Navigation_grid {
			@include respond-to(mb) {
				grid-template-columns: repeat(1, 1fr);
			}
			display: grid;
			grid-template-columns: repeat(4, 1fr);
			gap: 15px;
			margin-top: 25px;
			.grid_div {
				@include respond-to(mb) {
					background: #FFFFFF;
					border-radius: 2px;
					border: 1px solid #E9E9E9;
					font-size: 0;
				}
				img	 {
					@include respond-to(mb) {
						display: inline-block;
						vertical-align: middle;
						width: 45%;
					}
				}
				.bottom {
					@include respond-to(mb) {
						display: inline-block;
						vertical-align: middle;
						width: 51%;
						margin-left: 3%;
					}
				}
				padding: 10px;
				border:1px solid #ccc;
				border-radius: 2px;
				position: relative;
				&:hover {
					box-shadow: 0px 8px 13px 0px rgba(0,0,0,0.09);
				}
				.color_div {
					@include respond-to(mb) {
						display: none;
					}
					display:inline-block ;
					vertical-align: middle;
					.color_grid {
						display: grid;
						grid-template-columns: repeat(8, 1fr);
						gap: 0px;
						.color-circle {
							background: url('https://oss-static-cn.liyi.co/web/quoteManage/20231021/20230619xcxWJWKD_2054DQFx75.png') no-repeat;
							background-repeat: no-repeat;
							background-size: cover;
						}
						p {
							margin: 0;
							vertical-align: middle;
						}
						.colorYuan {
							margin: 0;
							margin: 6px;
							border-radius: 50%;
							border: 1px solid #A30C00;
							width: 14px;
							height: 14px;
							position: relative;
							cursor: pointer;
						}
						.active::before {
							position: absolute;
							content: '';
							width: 20px;
							height: 20px;
							border-radius: 50%;
							border: 1px solid #A30C00;
							left: -4px;
							top: -4px;
						}
					}
				}
				.color_number {
					width: 28%;
					vertical-align: middle;
					display: inline-block;
					font-family: Poppins;
					font-weight: 400;
					font-size: 14px;
					color: #666666;
					text-align: right;
					@include respond-to(mb) {
						display: none;
					}
				}
				.mb {
					display: none;
					@include respond-to(mb) {
						display: block !important;
					}
				}
				.title {
					font-family: Calibri !important;
					font-weight: bold;
					font-size: 18px !important;
					color: #333333;
					text-align: left;
					margin: 5px 0;
					@include respond-to(mb) {
						font-family: Arial;
						font-weight: bold;
						font-size: 13px !important;
						color: #333333;
						line-height: 1.4 !important;
						margin: 0;
					}
				}
				.color_bottom {
					@include respond-to(mb) {
						margin-bottom: 5px;
					}
					margin-bottom: 15px;
					.number {
						@include respond-to(mb) {
							margin-bottom: 5px;
						}
						font-family: Calibri;
						font-weight: bold;
						font-size: 16px;
						color: #BA222F;
						display: inline-block;
						vertical-align: middle;
					}
					.comment {
						@include respond-to(mb) {
							width: 100%;
							text-align: left;
						}
						width: 63%;
						text-align: right;
						display: inline-block;
						vertical-align: bottom;
						p {
							@include respond-to(mb) {
								font-size: 12px;
							}
							display: inline-block;
							vertical-align: middle;
							margin: 0;
							img {
								width: 15px;
								height: 15px;
							}
							span {
								display: inline-block;
								vertical-align: middle;
							}
						}
					}
				}
				.collect {
					width: 25px;
					height: 25px;
					cursor: pointer;
					position: absolute;
					top: 10px;
					left: 10px;
					svg {
						display: inline-block;
						width: 25px;
						height: 25px;
					}
				}
				.hot {
					@include respond-to(mb) {
						left: 25% !important;
					}
					position: absolute;
					text-align: center;
					background: linear-gradient(87deg, #FC1717, #FE4C4C);
					border-radius: 0px 0px 0px 15px;
					height: 22px;
					line-height: 22px;
					font-family: Calibri;
					font-weight: 400;
					font-size: 14px;
					color: #FFFFFF;
					width: 70px;
					right: 0;
					top: 0;
				}
			}
		}
	}
	.label {
		margin-top: 20px;
		.color_label {
			display: inline-block;
			margin-right: 10px;
			padding: 0 20px;
			height: 30px;
			background: #FFFFFF;
			border-radius: 15px;
			border: 1px solid #D24600;
			text-align: center;
			font-family: Calibri;
			font-weight: 400;
			font-size: 14px;
			color: #D24600;
			line-height: 30px;
			cursor: pointer;
		}
	}
	.left_collapse {
		display: none;
		@include respond-to(mb) {
			display: block;
			position: absolute;
			width: 100%;
			height: 100%;
			left: 0;
			top: 0;
			z-index: 10;
			background: rgba(6, 6, 6, 0.5);
			.collapse {
				position: absolute;
				top: 0;
				left: 0;
				width: 93%;
				height: 90%;
				background-color: #fff;
				padding: 25px;
				.title {
					font-family: Poppins, Poppins !important;
					font-weight: bold;
					font-size: 24px !important;
					color: #000000;
					text-align: left;
					font-style: normal;
					text-transform: none;
					span {
						font-family: Poppins, Poppins;
						font-weight: normal;
						font-size: 14px;
						color: #666666;
						text-transform: none;
					}
				}
				.label {
					.label_div {
						padding: 10px 20px;
						background: rgba(210,70,0,0.12);
						border-radius: 2px 2px 2px 2px;
						font-family: Roboto, Roboto;
						font-weight: 400;
						font-size: 12px;
						color: #D24600;
						display: inline-block;
						margin-left: 10px;
						margin-bottom: 10px;
					}
				}
				.accordion-item {
					border-bottom: 2px solid #f0f0f0;
					padding: 10px;
					margin-bottom: 5px;
					cursor: pointer;
					position: relative;
					.title {
						font-family: Calibri !important;
						font-weight: bold;
						font-size: 18px !important;
						color: #333333;
						text-align: left;
						font-style: normal;
						text-transform: none;
					}
					.active {
						background-color: lightblue;
					}
					p {
						margin: 15px 0px;
					}
					.selectDiv {
						background: #FFFFFF;
						border-radius: 0px 0px 0px 0px;
						border-radius: 6px;
						width: 100%;
						z-index: 99;
						input[type="checkbox"]:checked {
							appearance: none;
						}
						input[type="checkbox"]:checked+span {
							border:none;
							background: url("https://static-oss.gs-souvenir.com/web/quoteManage/20241219/gouxuan_1_2055hiMG3i.png") no-repeat;
						}
						label {
							font-family: Calibri, Calibri;
							font-weight: 400;
							font-size: 14px;
							color: #666666;
							text-align: left;
							font-style: normal;
							text-transform: none;
							vertical-align: middle;
							margin-bottom: 15px;
							display: inline-block;
							span {
								font-family: Arial, Arial;
								font-weight: 400;
								font-size: 14px;
								color: #666666;
								text-align: left;
								font-style: normal;
								text-transform: none;
								margin-left: 5px;
								i {
									display: inline-block;
									vertical-align: middle;
									border-radius: 50%;
									width: 15px;
									height: 15px;
								}
							}
						}
						.checkbox__wrap {
							display: inline-block;
							position: relative;
							width: 19px;
							height: 13px;
							vertical-align: middle;
						}
						.checkbox__wrap input {
							opacity: 0;
						}
						.checkbox__wrap span {
							position: absolute;
							inset: 0;
							border-radius: 2px 2px 2px 2px;
							border: 1px solid #666666;
						}
						.checkbox__wrap input:checked + span {

						}
					}
				}
				.arrow {
					position: absolute;
					top: 15px;
					right: 10px;
					font-size: 39px;
				}
			}
		}
	}
	.viewMore {
		text-align: center;
		margin: 40px auto;
		@include respond-to(mb) {
			margin: 20px auto;
		}
		div {
			font-family: Calibri;
			font-weight: bold;
			font-size: 18px;
			color: #FFFFFF;
			background: #DE3500;
			border-radius: 8px;
			color: #fff;
			text-align: center;
			padding: 10px 0px;
			width: 15%;
			margin: auto;
			cursor: pointer;
			@include respond-to(mb) {
				background: #D24600;
				border-radius: 30px;
				font-size: 12px;
				width: 40%;
			}
		}
	}
	.custom-scrollbar::-webkit-scrollbar {
		/*滚动条整体样式*/
		width: 5px;

		/*高宽分别对应横竖滚动条的尺寸*/
		height: 1px;
	}

	.custom-scrollbar.csu1::-webkit-scrollbar {
		height: 50px;
	}

	.custom-scrollbar::-webkit-scrollbar-thumb {
		/*滚动条里面小方块*/
		border-radius: 10px;
		background: #d3d3d3;
	}

	.custom-scrollbar::-webkit-scrollbar-track {
		/*滚动条里面轨道*/
		border-radius: 10px;
	}
</style>