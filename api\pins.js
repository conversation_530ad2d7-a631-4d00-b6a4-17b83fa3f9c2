import { request } from "@/utils/request";
import { getTrackTransactionHtml } from "@/utils/criteoConfig";

//获取分类
export function getByPId(data) {
	return request({
		url: "/quote/cate/getByPId",
		method: "get",
		params: data,
	});
}

//获取分类绑定的参数
export function getCateParamRelationByCateId(data) {
	return request({
		url: "/quote/cate/getCateParamRelationByCateId",
		method: "get",
		params: data,
	});
}

export function calculate(data, cancelTokenSource) {
	return request({
		url: "/quote/quoteCalculate/calculate",
		method: "post",
		data: data,
		cancelToken: cancelTokenSource?.token,
	});
}

export function calculateAll(data, cancelTokenSource) {
	return request({
		url: "/quote/quoteCalculate/calculateAll",
		method: "post",
		data: data,
		cancelToken: cancelTokenSource?.token,
	});
}

export function getCurrency(data) {
	return request({
		url: "/quote/currency/getAllCurrency",
		method: "get",
		params: data,
	});
}

export function getLanguage(data) {
	return request({
		url: "/app/language/getLanguageByProId",
		method: "get",
		params: data,
	});
}

export function getContinentLanguage(data) {
	return request({
		url: "/app/language/getContinentCountryLanguage",
		method: "get",
		params: data,
	});
}

//添加或者编辑询盘
export function otoEditInquiry(data) {
	if (process.env.NODE_ENV === "production") {
		try {
			if (window.$nuxt.$store.state.advertConfig.googleConfig && window.gtag) {
				//网络增强型转化
				gtag("set", "user_data", {
					email: data.email,
					phone_number: data.telephone,
				});
				gtag("event", "inquiry", { style_value: data.productsName });
				let googleInquiryConfig = window.$nuxt.$store.getters.getAdvertisementConfig("googleInquiryConfig");
				if (googleInquiryConfig) {
					gtag("event", "conversion", {
						send_to: googleInquiryConfig.value,
						value: 1,
						currency: window.$nuxt.$store.state.currency.code,
						transaction_id: data.email,
					});
				}
			}
		} catch (e) {}
	}
	return request({
		url: "/app/inquiry/editInquiry",
		method: "post",
		data: { currency: window.$nuxt.$store.state.currency.code, ...data },
	}).then((res) => {
		// 广告
		try {
			if (process.env.NODE_ENV === "production") {
				if (window.$nuxt.$cookies.get("fbclid") && window.fbq) fbq("track", "CustomizeProduct");
				if (window.$nuxt.$cookies.get("rdt_cid") && window.rdt)
					rdt("track", "Custom", {
						customEventName: "inquiry",
						currency: window.$nuxt.$store.state.currency.code,
						itemCount: 1,
						transactionId: data.email,
						value: 1,
					});
				let criteoConfig = window.$nuxt.$store.getters.getAdvertisementConfig("criteoConfig");
				if (criteoConfig) {
					//Criteo Sales Tag
					let account = criteoConfig.account;
					let email = data.email;
					let zipcode = "";
					let script = window.document.createElement("script");
					let productItem = [];
					productItem.push({
						id: data.platformProductId,
						price: 1,
						quantity: 1,
					});
					function hasCommonItem(idList, productItemIdList) {
						return productItemIdList.some(item => idList.includes(item));
					}
					//判断订单产品是否存在 idList 中，如果存在，才触发 criteo 跟踪事件
					// let idList = [4,300,671,472,589,469,282,561,638,692,644,470,8,268,67,524,7];
					// let productItemIdList = productItem.filter(item=>item.id);
					// const hasMatch = hasCommonItem(idList, productItemIdList);
					// if(hasMatch){
					script.innerHTML = getTrackTransactionHtml({
						account,
						email,
						zipcode,
						id: res.data,
						productItem,
					});
					script.type = "text/javascript";
					document.getElementsByTagName("head")[0].appendChild(script);
					// }
				}
				//lintrk
				let lintrkConfig = window.$nuxt.$store.getters.getAdvertisementConfig("lintrkConfig");
				if (lintrkConfig) {
					window.lintrk("track", {
						conversion_id: lintrkConfig.inquiry_conversion_id,
						conversion_value: 1, // 设置转化价值
						custom_data: {
							id: res.data,
							email: data.email,
						},
					});
				}
				if(window.ttq){
					ttq.track('CompletePayment', {
						"contents": [String(data.platformProductId)],
						"value": 1, // number. Value of the order or items sold. Example: 100.
						"currency": "USD" // string. The 4217 currency code. Example: "USD".
					},{ event_id : res.data});
				}
				if(window.twq){
					// Insert Twitter Event ID
					twq('event', 'tw-p6azw-p6baw', {
						value: 1, // use this to pass the value of the conversion (e.g. 5.00)
						currency: "USD", // use this to pass the currency of the conversion with an ISO 4217 code (e.g. ‘USD’)
						conversion_id: res.data, // use this to pass a unique ID for the conversion event for deduplication (e.g. order id '1a2b3c')
						email_address: data.email // use this to pass a user’s email address
					});
				}
			}
			return res;
		} catch (e) {
			return res;
		}
	});
}
//编辑购物车
export function otoEditCart(data) {
	return request({
		url: "/app/cart/editCartParam",
		method: "post",
		data: data,
	});
}
//添加购物车
export function otoAddCart(data, priceInfo) {
	if (process.env.NODE_ENV === "production") {
		try {
			let quoteParam = JSON.parse(data.quoteParam);
			let quotePriceParam = JSON.parse(data.quotePriceParam);
			if (window.$nuxt.$store.state.advertConfig.googleConfig && window.gtag) {
				//网络增强型转化
				gtag("event", "add_to_cart", {
					currency: window.$nuxt.$store.state.currency.code,
					value: priceInfo.totalPrice,
					items: [
						{
							item_id: String(data.quoteCateChildId),
							item_name: quoteParam.classificationData?.cateName,
							discount: priceInfo.discount,
							item_brand: quotePriceParam.projectName?.split(".")[0],
							price: Number((priceInfo.foundationUnitPrice * priceInfo.discount).toFixed(2)),
							quantity: Number(data.quantity),
						},
					],
				});
				let googleAddCartConfig = window.$nuxt.$store.getters.getAdvertisementConfig("googleAddCartConfig");
				if (googleAddCartConfig) {
					gtag("event", "conversion", {
						send_to: googleAddCartConfig.value,
						transport_type: "beacon" // 关键参数
					});
				}
			}
		} catch (e) {}
	}
	return request({
		url: "/app/cart/addCart",
		method: "post",
		data: { currency: window.$nuxt.$store.state.currency.code, ...data },
	});
}
//价格分层参数
export function getPriceData(data) {
	return request({
		url: "/quote/websiteIncreasePrice/getPriceData",
		method: "get",
		data: data,
	});
}

//获得类似半定制价格列表
export function getPriceTable(data) {
	return request({
		url: "/quote/quoteCalculate/getPriceTable",
		method: "post",
		data: data,
	});
}

//没有上传artWork 的询盘再次上传文件
export function setInquiry(data) {
	return request({
		url: "/app/inquiry/updateInquiryPic",
		method: "post",
		data: data,
	});
}

//获取大类信息
export function getInfo(data) {
	return request({
		url: "/quote/cate/getInfo",
		method: "get",
		data: data,
	});
}

//获取启用图标标签
export function getAppRingIconTagList(cid) {
	return request({
		url: "/quote/ring/icons/getAppRingIconTagList?cateId=" + cid,
		method: "get",
		data: cid,
	});
}

//获取启用图标列表
export function getAppRingIconList(data) {
	return request({
		url: "/quote/ring/icons/getAppRingIconList",
		method: "post",
		data: data,
	});
}
