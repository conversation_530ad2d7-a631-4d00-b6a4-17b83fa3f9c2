<template>
	<div class="quoteBox" nodebounce>
		<div class="quoteBoxHeader">
			<div class="return" @click="prevStep" v-show="currentStep > 1"><b class="icon-Return"></b>{{ lang.return }}</div>
			<step-bar v-if="showStepBar" :active-step="currentStep" :step="maxStep" color="#EBEBEB"></step-bar>
		</div>
		<div class="quoteBoxContent stepList" :class="`stepList${currentStep}`">
			<template v-for="(item, index) in generalData">
				<div class="step-item step-size" :key="index" v-if="showFn(item, 'size')">
					<StepTitle :title="getTitle(item)" :step="getStep(item)"></StepTitle>
					<PatchesSize :sizeW.sync="sizeW" :sizeH.sync="sizeH" :text-config="allStepConfig[item.customStepName].sizeTextConfig" :itemData="item" :selectedData="selectedData" @clickFun="selectSize(item, $event)" @showMoreSize="showMoreSize"></PatchesSize>
				</div>
				<div class="step-item step-qty" :key="index" v-if="showFn(item, 'qty')">
					<StepTitle :title="getTitle(item)" :step="getStep(item)"></StepTitle>
					<StepQty :hidePriceTableTitle="device === 'mb'" :hideSavePrice="true" :largeGap="true" :qtyList="qtyList" :isCustom.sync="isCustom" :selectedQtyInd.sync="selectedQtyInd" :customNumber.sync="customNumber" :customNumberUnitPrice="customNumberUnitPrice" :customNumberPrice="customNumberPrice" @selectQtyList="selectQtyList" @filterCustomNumber="filterCustomNumber"></StepQty>
					<div class="freeTipWrap" v-show="currentStep === 1">
						<FreeTip2></FreeTip2>
						<div class="nextBtn">
							<button primary :disabled="customQty <= 0" @click="nextStep" :alt="lang.continue" :title="lang.continue">{{ lang.continue }}&nbsp;&nbsp;<b class="icon-xiayige"></b></button>
						</div>
						<div class="tip">* {{lang.click}} <a @click="showDialog">{{lang.here}}</a> {{lang.getPage}}</div>
					</div>
				</div>
				<div class="step-item step-shape" v-if="showFn(item, 'patchShape')">
					<StepTitle :title="getTitle(item)" :step="getStep(item)"></StepTitle>
					<div class="step-item-params">
						<div
							class="param-item"
							v-for="(citem, cindex) in item.childList"
							:key="citem.id"
							:class="{
								active: hasId(citem.id, selectedData[item.paramName]),
							}"
							@click="selectQuoteParams(item, citem)"
						>
							<div class="imgWrap">
								<img loading="lazy" :src="getImgSrc(citem)" :alt="citem.imageJson[0].alt" :title="citem.imageJson[0].alt" />
							</div>
							<span>{{ citem.alias }}</span>
						</div>
					</div>
				</div>
				<div class="step-item step-backing" v-if="showFn(item, 'patchBacking')">
					<StepTitle :title="getTitle(item)" :step="getStep(item)"></StepTitle>
					<div class="step-item-params">
						<div
							class="param-item"
							v-for="citem in item.childList"
							:key="citem.id"
							:class="{
								active: hasId(citem.id, selectedData[item.paramName]),
							}"
							@click="selectQuoteParams(item, citem)"
						>
							<div class="imgWrap">
								<img loading="lazy" :src="getImgSrc(citem)" :alt="citem.imageJson[0].alt" :title="citem.imageJson[0].alt" />
							</div>
							<span>{{ citem.alias }}</span>
							<PriceText :paramData="citem"></PriceText>
						</div>
					</div>
				</div>
				<div class="step-item step-border" v-if="showFn(item, 'patchBorder')">
					<StepTitle :title="getTitle(item)" :step="getStep(item)"></StepTitle>
					<div class="step-item-params">
						<div
							class="param-item"
							v-for="citem in item.childList"
							:key="citem.id"
							:class="{
								active: hasId(citem.id, selectedData[item.paramName]),
							}"
							@click="selectQuoteParams(item, citem)"
						>
							<div class="imgWrap">
								<img loading="lazy" :src="getImgSrc(citem)" :alt="citem.imageJson[0].alt" :title="citem.imageJson[0].alt" />
							</div>
							<span>{{ citem.alias }}</span>
							<PriceText :paramData="citem"></PriceText>
						</div>
					</div>
				</div>
				<div class="step-item step-options" v-if="showFn(item, 'patchOptions')">
					<StepTitle :title="getTitle(item)" :step="getStep(item)"></StepTitle>
					<div class="step-item-params">
						<div
							class="param-item"
							v-for="citem in item.childList"
							:key="citem.id"
							:class="{
								active: hasId(citem.id, selectedData[item.paramName]),
							}"
							@click="selectQuoteParams(item, citem)"
						>
							<div class="imgWrap">
								<img loading="lazy" :src="getImgSrc(citem)" :alt="citem.imageJson[0].alt" :title="citem.imageJson[0].alt" />
							</div>
							<span>{{ citem.alias }}</span>
							<PriceText :paramData="citem"></PriceText>
						</div>
					</div>
				</div>
				<div class="step-item step-upload" v-if="showFn(item, 'upload')">
					<StepTitle :title="getTitle(item)" :step="getStep(item)"></StepTitle>
					<div class="step-item-params" :class="{ 'param-item-hasUpload': uploadArtworkList.length }">
						<template v-if="!uploadArtworkList.length">
							<div class="param-item" @click="openUpload">
								<div class="uploadWrap">
									<div flex center>
										<b class="icon-shangchuan uploadIcon"></b>
										<span class="brow">{{ lang.browse }}</span>
										<el-tooltip popper-class="cusToolTip" effect="light">
											<div slot="content">
												<div>{{ item.tips }}</div>
											</div>
											<b class="icon-wenhao" @click.stop style="color: var(--color-primary)"></b>
										</el-tooltip>
									</div>
								</div>
							</div>
						</template>
						<template v-else>
							<div class="param-item-hasUpload-left custom-scrollbar">
								<div class="upload-item" v-for="(item, index) in uploadArtworkList" :key="index">
									<span class="upload-name">{{ item.original_filename }}</span>
									<b class="icon-check" style="color: #0cbd5f"></b>
									<b class="icon-shanchu2" style="color: #b6b0b0" @click="deleteUpload(index)"></b>
								</div>
							</div>
							<div class="uploadWrap" @click="openUpload">
								<b class="icon-shangchuan uploadIcon"></b>
								<span class="brow">{{ lang.browse }}</span>
							</div>
						</template>
						<input type="file" ref="upload" :accept="acceptFileType" multiple @change="uploadPic" />
					</div>
				</div>
				<div class="step-item step-time" v-if="showFn(item, 'discount')">
					<StepTitle :title="getTitle(item)" :step="getStep(item)"></StepTitle>
					<div class="step-item-params">
						<div
							class="param-item-wrap"
							v-for="citem in getNewDiscountList(item)"
							:key="citem.id"
							:class="{
								active: hasId(citem.id, selectedData[item.paramName]),
							}"
							@click="selectQuoteParams(item, citem)"
						>
							<div class="param-item">
								<CustomCircle :circleType="2" :isActive="hasId(citem.id, selectedData[item.paramName])"></CustomCircle>
								<DiscountText :itemData="citem"></DiscountText>
							</div>
							<div class="des">{{ citem.alias }}</div>
						</div>
					</div>
				</div>
			</template>
		</div>
		<div class="quoteBoxFooter" v-show="currentStep > 1">
			<div class="price">
				<span>{{ lang.quantity }}: {{ customQty }} Pcs</span>
				<span
					>{{ lang.subtotal }}:
					<CCYRate :price="finalPrice" class="finalPrice"></CCYRate>
				</span>
			</div>
			<button primary @click="addCart" v-if="currentStep == maxStep" :title="lang.addToCart" :alt="lang.addToCart">{{ lang.addToCart }}&nbsp;&nbsp;<b class="icon-xiayige"></b></button>
			<button primary @click="nextStep" v-if="currentStep > 1 && currentStep < maxStep" :title="lang.continue" :alt="lang.continue">{{ lang.continue }}&nbsp;&nbsp;<b class="icon-xiayige"></b></button>
		</div>
		<PatchSizeDialog :visible.sync="showPatchSizeDialog" :patchSizeKey="patchSizeKey" :sizeW="sizeW" :sizeH="sizeH" :patchSizeParams="patchSizeParams" :title="patchSizeTitle" @changeSize="changeSize"></PatchSizeDialog>
	</div>
</template>

<script>
import { calculate, calculateAll, getInfo, getPriceData, otoAddCart } from "~/api/pins";
import { getCateParamRelationByCateId } from "~/api/web";
import CustomCircle from "~/components/Quote/customCircle.vue";
import CCYRate from "~/components/CCYRate.vue";
import { debounce, deepClone, isImageType, scrollToViewTop } from "~/utils/utils";
import { uploadFile } from "~/utils/oss";
import "@/plugins/element";
import { acceptFileType, checkFile } from "@/utils/validate";
import DiscountText from "@/components/modal/Quote/QuoteComponents/DicountText.vue";
import StepBar from "@/components/modal/Quote/QuoteComponents/StepBar.vue";
import PatchesSize from "@/components/modal/Quote/QuoteComponents/PatchesSize.vue";
import StepTitle from "@/components/modal/Quote/QuoteComponents/StepTitle.vue";
import StepQty from "@/components/modal/Quote/QuoteComponents/StepQty.vue";
import PriceText from "@/components/Quote/PriceText.vue";
import PatchSizeDialog from "@/components/Quote/QuickQuote/PatchSizeDialog.vue";
import FreeTip2 from "@/components/modal/Quote/QuoteComponents/FreeTip2.vue";
import {findSelectDiscount, getIsSmallQty, getQuoteTime} from "@/assets/js/QuotePublic";
import { getQuickQuoteConfigByPidAndCateId } from "assets/js/quickQuoteConfig";
const setIndex = (data) => {
	if (!data) {
		return false;
	}
	let index = 0;
	data.forEach((item) => {
		if (item) {
			if (!item.noShowDetail) {
				index += 1;
			}
			item.customIndex = index;
		}
	});
	return data;
};
const sortByKey = (array, key) => {
	return array.sort(function (a, b) {
		let x = a.priceInfo[key]; //如果要从大到小,把x,y互换就好
		let y = b.priceInfo[key];
		return x < y ? -1 : x > y ? 1 : 0;
	});
};

const getMyCustomStepName = (item) => {
	let map = {
		size: ["SIZE"],
		qty: ["qty", "Quantity"],
		upload: ["Upload Artwork & Comments"],
		discount: ["DISCOUNT"],
		patchShape: ["Patch Shape"],
		patchBacking: ["Patch Backing"],
		patchBorder: ["Patch Border"],
		patchOptions: ["Additional Upgrades"],
	};
	let name = "";
	for (let i in map) {
		let val = map[i];
		if (val.includes(item.paramType) || val.includes(item.paramName)) {
			name = i;
			break;
		}
	}
	return name;
};
const parseJSON = (str) => {
	return str
		? JSON.parse(str)
		: [
				{
					url: "",
				},
		  ];
};
const addCustomProperty = (data) => {
	let handle = (list) => {
		for (let i = 0; i < list.length; i++) {
			let item = list[i];
			item.remark = "";
			item.noShowDetail = false;
			item.isHidden = false; //不可选属性
			item.files = [];
			item.inputNum = undefined;
			item.imageJson = parseJSON(item.imageJson);
			if (item.childList && item.childList.length) {
				handle(item.childList);
			}
			item.customStepName = getMyCustomStepName(item);
			//特殊处理
			if (item.paramName === "Upload Artwork & Comments") {
				item.alias = "Upload Your Artwork";
			}
		}
	};
	handle(data);
	return data;
};
export default {
	props: {
		pid: {
			type: [Number, String],
			default: 312,
		},
		cateId: {
			type: [Number, String],
			default: 390,
		}
	},
	data() {
        const config = getQuickQuoteConfigByPidAndCateId.call(this,this.pid, this.cateId);
		return {
            allStepConfig: {},
			sizeW: {},
			sizeH: {},
			isLater: false,
			acceptFileType,
			showQuote: false,
			showPatchSizeDialog: false,
			patchSizeKey: "",
			patchSizeTitle: "",
			showExtend: false,
			uploadArtworkList: [],
			customNumber: "",
			selectedParamsValueParent: {},
			selectedParamsValue: {},
			isCustom: false,
			isDs: 0,
			isFastQuote: 1,
			loadAddCart: false,
			debounceCalcPrice: null,
			stepName: "",
			cateInfo: {},
			generalData: [],
			qtyList: [],
			priceInfo: {
                isSmallWeight: 1
            },
			selectedData: {},
			selectedQtyInd: -1,
			currentStep: 1,
            ...config
		};
	},
	watch: {
		selectedData: {
			handler() {
				if (!this.showQuote) {
					return false;
				}
				this.debounceCalcPrice();
			},
			deep: true,
		},
	},
	components: {
		FreeTip2,
		PatchSizeDialog,
		PriceText,
		StepQty,
		StepTitle,
		PatchesSize,
		StepBar,
		DiscountText,
		CustomCircle,
		CCYRate,
	},
	computed: {
        showStepBar(){
            return this.device!=='mb'
        },
		patchSizeParams() {
			return this.generalData.find((item) => item.customStepName === "size") || {};
		},
		finalPrice() {
			return this.priceInfo.totalPrice;
		},
		customNumberPrice() {
			return this.isCustom && this.customNumber ? this.priceInfo.totalPrice : 0;
		},
		customNumberUnitPrice() {
			return this.isCustom && this.customNumber ? this.priceInfo.foundationUnitPrice : 0;
		},
		proId() {
			if (this.previewMode) {
				return 148;
			} else {
				return this.$store.state.proId;
			}
		},
		projectName() {
			if (this.previewMode) {
				return "o2o-site-style";
			} else {
				return this.$store.state.proName;
			}
		},
		previewMode() {
			return process.env.VUE_APP_MODE === "Preview";
		},
		proType() {
			return this.$store.state.proType;
		},
		lang() {
			return this.$store.getters.lang.quote || {};
		},
		langSemi() {
			return this.$store.getters.lang.semiCustom || {};
		},
		userUUID() {
			return this.$store.state.userUUID;
		},
		userId() {
			return this.$store.state.userInfo?.id;
		},
		isLogin() {
			return this.$store.getters.isLogin;
		},
		userInfo() {
			return this.$store.state.userInfo;
		},
		device() {
			return this.$store.state.device;
		},
		customQty() {
			if (this.isCustom) {
				return parseInt(this.customNumber) || 0;
			} else {
				return parseInt((this.qtyList && this.qtyList.length && this.qtyList[this.selectedQtyInd]?.totalQuantity) || 0);
			}
		},
	},
	methods: {
		showDialog(){
            this.$store.commit('setMask',{
                modal:"modalQuoteDialog",
                quoteUrl:"/quote/custom-embroidered-printed-patches?type=quoteIframe"
            })
        },
		changeSize(item) {
			this[this.patchSizeKey] = item;
		},
		showMoreSize(type) {
			this.patchSizeTitle = this.allStepConfig.size.sizeTextConfig[type];
			this.patchSizeKey = type;
			this.showPatchSizeDialog = true;
		},
		selectSize(item, citem) {
			this.selectQuoteParams(item, citem);
		},
		zoomPic(img) {
			this.$viewerApi({
				images: [img],
			});
		},
		getImgSrc(citem) {
			return citem.imageJson[1] ? citem.imageJson[1]?.url : citem.imageJson[0]?.url;
		},
		previewImg(img) {
			if (isImageType(img)) {
				this.$viewerApi({
					images: [img],
				});
			} else {
				window.open(img);
			}
		},
		setIsLater(bool) {
			this.isLater = bool;
		},
		getStyle(item) {
			return `grid-template-columns:repeat(${this.allStepConfig[item.customStepName]?.columns[this.device]},1fr)`;
		},
		showFn(item, type) {
			return item.customStepName === type && this.currentStep === this.allStepConfig[item.customStepName]?.showStep;
		},
		getTitle(item) {
			if (item.customStepName === "upload") {
				return item.alias;
			} else {
				return this.lang.Select + " " + item.alias;
			}
		},
		getStep(item) {
			return this.lang.step + " " + item.customIndex;
		},
        clearField(e = "DISCOUNT") {
            let findDiscount = this.generalData.find((item) => item.paramType === e);
            if (findDiscount) {
                let name = findDiscount.paramName;
                this.selectedData[name] = [];
            }
        },
        getNewDiscountList(itemData) {
            let result = getQuoteTime(itemData.childList,this.priceInfo,this.proType),
                    originShowSmallPrice =  this.$store.state.showSmallPrice;
            this.$store.commit("setShowSmallPrice", result.newShowSmallPrice);
            if(result.newShowSmallPrice !== originShowSmallPrice){
                this.clearField("DISCOUNT")
            }
            return result.arr;
        },
		openUpload() {
			this.$refs.upload[0].click();
		},
		deleteUpload(ind) {
			this.uploadArtworkList.splice(ind, 1);
			if (this.uploadArtworkList.length <= 1) {
				this.showExtend = false;
			}
		},
		prevStep() {
			this.currentStep -= 1;
		},
		nextStep() {
			this.currentStep += 1;
		},
		//判断是否选中
		hasId(id, arr) {
			if (!arr) {
				return false;
			}
			return !!arr.find((item) => {
				return item.id === id;
			});
		},
		selectQtyList() {
			this.debounceCalcPrice();
			this.handleWeightDiscount();
		},
		filterCustomNumber() {
			this.debounceCalcPrice();
			this.handleWeightDiscount();
		},
		selectQty() {
			this.isCustom = true;
			this.selectedQtyInd = -1;
		},

		handleWeightDiscount() {
			//获取算价格参数
			let priceParam = this.getPriceParam();
			//获取折扣参数列表
			let discountList = this.generalData
				.find((item) => {
					return item.paramType === "DISCOUNT";
				})
				?.childList.filter((item) => item.priceInfo.priceType === 10);
			if (!discountList?.length) {
				return;
			}
			discountList.forEach((item) => {
				if (priceParam.sizeId && priceParam.cateId) {
					if (priceParam.sizeId && priceParam.cateId && (priceParam.quantity || priceParam.qtyDetailDTOS?.length)) {
						calculate(Object.assign({}, priceParam, { discountId: item.priceInfo.id })).then((res) => {
							this.$set(item.priceInfo, "customWeightPrice", res.data.discountPrice);
						});
					}
				}
			});
		},

		//根据paramName清空字段
		//参数1   Plating/Finish
		clearFieldName(e = "Plating/Finish") {
			let findDiscount = this.generalData.find((item) => item.paramName === e);
			if (findDiscount) {
				let name = findDiscount.paramName;
				this.selectedData[name] = [];
			}
		},

		//参数选中事件
		async selectQuoteParams(item, citem) {
			this.selectedParamsValueParent = item;
			this.selectedParamsValue = citem;
			this.$set(this.selectedData, item.paramName, [citem]);
			if (item.customStepName === "size") {
				window.quoteSizeValue = citem.paramCode;
			}
            this.handleWeightDiscount();
		},
		replayUpload() {
			this.openUpload();
			this.$store.commit("setSizeDialog", false);
		},
		uploadPic(event, type = "upload") {
			this.$gl.show();
			let files = type === "upload" ? event.target.files : event;
			let fileResult = checkFile(Array.from(files), this.acceptFileType, 80);
			if (!fileResult) {
				this.$toast.error("File type error");
				this.$gl.hide();
				return false;
			}
			if (!fileResult.nomalSize.length) {
				this.$gl.hide();
				this.$store.commit("setSizeDialog", true);
				this.$store.commit("setInputRefName", "replayUpload");
				this.$store.commit("setOverSizeList", fileResult.overSize);
				this.$refs.upload[0].value = "";
				return false;
			}
			let uploadPromises = [];
			fileResult.nomalSize.forEach((file) => {
				let promise = uploadFile(file).then((res) => {
					this.uploadArtworkList.push({
						original_filename: file.name,
						secure_url: res,
						size: (file.size / 1024).toFixed(1),
					});
				});
				uploadPromises.push(promise);
			});
			Promise.all(uploadPromises).then(() => {
				this.$refs.upload[0].value = "";
				if (fileResult.overSize.length > 0) {
					this.$store.commit("setSizeDialog", true);
					this.$store.commit("setInputRefName", "replayUpload");
					this.$store.commit("setOverSizeList", fileResult.overSize);
				}
				this.$gl.hide();
			});
		},
		getQuoteParam(type = "inquiry") {
			let selectedData = deepClone(this.selectedData),
				finaData = [],
				generalData = deepClone(this.generalData);
			for (let i = 0; i < generalData.length; i++) {
				let item = generalData[i];
				if (item.customStepName === "qty") {
					// item.childList = [];
					// finaData.push(item);
					continue;
				}
				if (item.customStepName === "upload") {
					item.childList = [];
					item.later = this.isLater;
					finaData.push(item);
					continue;
				}
				//将选中参数里面的childlist置空
				if (selectedData[item.paramName] && selectedData[item.paramName].length) {
					selectedData[item.paramName].forEach((c) => {
						c.childList = [];
						c.quantity = c.inputNum;
						if (!c.giftQuantity) {
							c.giftQuantity = 0;
						}
					});
				}
				item.childList = selectedData[item.paramName] || [];
				finaData.push(item);
			}
			let files = [];
			let artworkList = this.uploadArtworkList;
			if (artworkList.length > 0) {
				artworkList.forEach((item) => {
					item.secure_url && files.push(item.secure_url);
				});
			}
			return {
				classificationData: this.cateInfo,
				finaData: finaData,
				fontData: {
					fontImgCustom: files,
					comments: this.remark,
				},
				designCanvas: [],
			};
		},
		getPriceParam(qty) {
			const data = {
				cateId: this.cateId,
				discountId: "",
				packingIdList: [],
				paramIdList: [],
				projectName: this.projectName,
				quantity: "",
				sizeId: "",
				upgradesQtyDTO: [],
				qtyDetailDTOS: [],
			};
			let generalData = this.generalData,
				selectedData = this.selectedData;
			for (let i = 0; i < generalData.length; i++) {
				let item = generalData[i];
				let paramName = item.paramName;
				let selectedVal = selectedData[paramName];
				if (item.customStepName === "qty") {
					data.quantity = qty || Number(this.customQty);
					continue;
				}
				if (item.customStepName === "upload") {
					continue;
				}
				if (!selectedVal) {
					continue;
				}
				selectedVal.forEach((citem) => {
					let paramType = citem.paramType;
					switch (paramType) {
						case "NORMAL":
							data.paramIdList.push(citem?.priceInfo?.id);
							break;
						case "DISCOUNT":
							data.discountId = citem?.priceInfo?.id;
							break;
						case "SIZE":
							data.sizeId = citem?.priceInfo?.id;
							break;
						default:
							break;
					}
				});
			}
			if (!data.qtyDetailDTOS.length) {
				delete data.qtyDetailDTOS;
			}
			return data;
		},
		calcPrice() {
			let priceParam = this.getPriceParam();
			if (priceParam.sizeId && priceParam.cateId) {
				calculateAll(priceParam).then((res) => {
					let qtyList = res.data.filter((item) => item.isFastQuote);
					let prevFoundationUnitPrice = qtyList[0]?.foundationUnitPrice;
					qtyList.forEach((item, index) => {
						if (index > 0) {
							item.save = (((prevFoundationUnitPrice - item.foundationUnitPrice) / prevFoundationUnitPrice) * 100).toFixed(0);
						}
					});
					this.qtyList = qtyList;
				});
			}
			if (priceParam.sizeId && priceParam.cateId && (priceParam.quantity || priceParam.qtyDetailDTOS?.length)) {
				calculate(priceParam).then((res) => {
					this.priceInfo = res.data;
				});
			}
		},
		checkParams() {
			let verify = true,
				stepName = "",
				selectedData = this.selectedData,
				generateData = this.generalData;
			for (let i = 0; i < generateData.length; i++) {
				let item = generateData[i];
				//参数名称
				let paramName = item.paramName;
				//选中的参数值
				let selectedVal = selectedData[paramName];
				if (item.customStepName === "upload") {
					if (!this.uploadArtworkList.length && !this.isLater) {
						stepName = item.customStepName;
						verify = false;
						break;
					}
				} else if (item.customStepName === "qty") {
					if (!this.customQty || this.customQty <= 0) {
						stepName = item.customStepName;
						verify = false;
						break;
					}
				} else {
					if (!selectedVal || selectedVal.length === 0) {
						stepName = item.customStepName;
						verify = false;
						break;
					}
				}
			}
			this.stepName = stepName;
			return verify;
		},
		async addCart() {
			if (!this.checkParams()) {
				this.$toast.error(this.lang.mapMessage[this.stepName] || this.lang.paramTip);
				scrollToViewTop(document.getElementById(`${this.stepName}`));
				return false;
			}
			//如果是临时项目
			if (this.$store.state.isTemporary) {
				this.$store.commit("setMask", "temporary");
				return false;
			}
			if (this.loadAddCart) {
				return false;
			}
			this.loadAddCart = true;
			let quoteParam = this.getQuoteParam("cart");
			let priceParam = this.getPriceParam();
			let data = {
				proId: this.proId,
				uuid: this.isLogin ? null : this.userUUID,
				userId: this.isLogin ? this.userId : null,
				buyType: 7,
				quoteCateId: this.pid,
				isMobile: this.device === "mb" ? 1 : 0,
				quoteCateChildId: priceParam.cateId,
				quantity: this.customQty,
				quotePriceParam: JSON.stringify(priceParam),
				quoteParam: JSON.stringify(quoteParam),
				isDs: this.isDs,
				isFastQuote: this.isFastQuote,
                isSmallQty: getIsSmallQty(findSelectDiscount(this.selectedData),this.$store.state.enableTurnaroundTimeCheck)
			};
			otoAddCart(data)
				.then((res) => {
					this.$toast.success(res.message);
					this.$router.push({
						path: "/cart",
					});
				})
				.finally(() => {
					setTimeout(() => {
						this.loadAddCart = false;
					}, 1000);
				});
		},
		//价格分层参数
		getPriceData() {
			getPriceData({ buyType: 7, productCateId: this.pid }).then((res) => {
				this.$store.commit("setMorePriceData", res.data);
			});
		},
	},
	async created() {
		this.getPriceData();
		this.debounceCalcPrice = debounce(this.calcPrice, 300);
	},
	async mounted() {
		try {
			this.$Bus.$on("replayUpload", this.replayUpload);
			let result = await Promise.all([
				getInfo({ id: this.cateId }),
				getCateParamRelationByCateId({
					cateId: this.cateId,
					isFastQuote: 1,
				}),
			]);
			this.cateInfo = result[0]?.data || {};
			let data1 = result[1]?.data;
			let selectedData = {},
				isLater = false,
				selectedQtyInd = -1,
				selectedQty = 0,
				generalData = setIndex(sortByKey(addCustomProperty(data1), "fastQuoteStepIndex"));
			//默认选中参数
			if (generalData?.length) {
				isLater = true;
				generalData.forEach((item) => {
					let customStepName = item.customStepName;
					if (customStepName !== "qty" && customStepName !== "upload") {
						let findDefault = item.childList.find((c) => c.priceInfo.isFastQuoteSelected);
						if (findDefault) {
							selectedData[item.paramName] = [findDefault];
							//选中尺寸下的默认数量
							if (customStepName === "size") {
								try {
									let priceInfo = findDefault.priceInfo;
									let increasePrice = JSON.parse(priceInfo.increasePrice).filter((item) => item.isFastQuote);
									let defaultQtyIndex = increasePrice.findIndex((q) => q.isFastQuoteSelected);
									if (defaultQtyIndex > -1) {
										selectedQtyInd = defaultQtyIndex;
										selectedQty = increasePrice[defaultQtyIndex].quantity;
									}
									this.sizeW = findDefault;
									this.sizeH = findDefault;
								} catch (e) {}
							}
						}
					}
				});
			}
			this.selectedData = selectedData;
			this.generalData = generalData;
			this.isLater = false;
			this.selectedQtyInd = selectedQtyInd;
			let priceParam = this.getPriceParam(selectedQty),
				priceResult;
			priceResult = await Promise.all([calculateAll(priceParam), calculate(priceParam)]);
			let qtyList = (priceResult && priceResult[0].data.filter((item) => item.isFastQuote)) || [];
			let prevFoundationUnitPrice = qtyList[0]?.foundationUnitPrice;
			qtyList.forEach((item, index) => {
				if (index > 0) {
					item.save = (((prevFoundationUnitPrice - item.foundationUnitPrice) / prevFoundationUnitPrice) * 100).toFixed(0);
				}
			});
			this.qtyList = qtyList;
			this.priceInfo = (priceResult && priceResult[1].data) || {};
			this.showQuote = true;
			this.handleWeightDiscount();
		} catch (e) {
			console.log(e);
		}
	},
};
</script>

<style lang="scss" scoped>
@mixin selectedStyle {
	border-color: $color-primary;
	&::after {
		content: "\e82c";
		position: absolute;
		width: 1.38em;
		height: 1.38em;
		line-height: 1.38em;
		right: 0;
		top: 0;
		border-bottom-left-radius: 50%;
		text-align: center;
		color: #ffffff;
		background-color: $color-primary;
		font-family: "modalicon";
	}

	@include respond-to(mb) {
		&::after {
			width: 1.25em;
			height: 1.25em;
			line-height: 1.25em;
		}
	}
}

.icon-wenhao {
	font-size: 1.25em;
}

button[primary] {
	min-width: auto !important;
	height: 2.5em !important;
	background-color: $color-primary;
	font-size: 1.12em;
}

button .icon-xiayige {
	font-size: 0.75em;
}

button[disabled] {
	background: #e2e2e2;
}

.nextBtn button {
	width: 100%;
}

.quoteBoxFooter {
	flex-shrink: 0;
	overflow: hidden;
	display: flex;
	justify-content: space-between;
	align-items: center;
	height: 5em;
	padding: 0 1em;
	background-color: #f6f6f6;
	margin: 20px -1.5em -1.5em;

	.price {
		display: block;
		flex: 1;
		@include respond-to(mb) {
			align-items: flex-start;
			flex-direction: column;
		}
	}

	::v-deep .finalPrice {
		font-weight: 700;
		color: $color-primary;
		font-size: 1.5em;
	}

	& > div:first-child span {
		margin-right: 2em;
	}
	button[primary] {
		font-size:1em;
	}
	@include respond-to(mb) {
		position: static;
		margin: 0 -1em -1.2em;
		height: 4.08em;

		::v-deep .finalPrice {
			font-size: 1em;
		}
		& > div:first-child span {
			margin-right: 1em;
		}
		button[primary] {
			font-size:1.12em;
		}
	}
}

.quoteBox {
	overflow: hidden;
	position: relative;
	display: flex;
	flex-direction: column;
	min-width: 0;
	min-height: 40em;
	color: $text-primary;
	background: #ffffff;
	border-radius: 4px;
	padding: 1.5em 1.25em;

	@include respond-to(mb) {
		min-height: auto;
		height: auto;
		padding: 1em 1.2em;
	}

	.quoteBoxHeader {
		display: flex;
		align-items: center;
		margin-bottom: 1em;

		.return {
			position: absolute;
			cursor: pointer;

			@include respond-to(mb) {
				position: static;
				margin-top: 0;
			}

			@media (any-hover: hover) {
				&:hover {
					font-weight: 700;
					color: $color-primary;
				}
			}

			b {
				vertical-align: 0;
				margin-right: 4px;
				@include respond-to(mb) {
					font-size: 1em;
					color: #757575;
				}
			}
		}
	}

	.stepList {
		flex: 1;
		display: grid;
		align-content: flex-start;
		grid-template-columns: 1fr 1.8fr;
		grid-template-areas: "a b";
		gap: 1em; /* 可选：设置行和列之间的间距 */

		&.stepList2 {
			grid-template-columns: 1.2fr 1fr;
			@include respond-to(mb) {
				grid-template-columns: repeat(1, 1fr);
				grid-template-areas: none;
			}
		}

		&.stepList3 {
			grid-template-columns: 1.4fr 1fr;
			grid-template-areas:
				"a b"
				"a d"
				"c d";
			grid-template-rows: auto auto 1fr; /* 定义两行，高度自适应内容 */
			@include respond-to(mb) {
				grid-template-columns: repeat(1, 1fr);
				grid-template-areas: none;
			}
		}

		@include respond-to(mb) {
			grid-template-columns: repeat(1, 1fr);
			grid-template-areas: none;
		}

		.step-item {
			font-size: 0.88em;
			@include respond-to(mb) {
				font-size: 1em;
				margin-bottom: 1em;
			}
            .param-item >span{
                font-size: 0.86em;
            }
		}
	}

	.step-size {
		min-width: 0;
		grid-area: a;

		@include respond-to(mb) {
			grid-area: auto;
			margin-bottom: 0 !important;
		}
	}

	.step-qty {
		min-width: 0;
		grid-area: b;

		@include respond-to(mb) {
			grid-area: auto;
		}
	}

	.step-backing {
		min-width: 0;
		grid-area: b;

		@include respond-to(mb) {
			grid-area: auto;
		}

		.step-item-params {
			display: grid;
			grid-template-columns: repeat(2, 1fr);
			align-items: flex-start;
			grid-gap: 0.5em;
			padding: 0.75em;
			background-color: #fafafa;

			@include respond-to(mb) {
				grid-template-columns: repeat(3, 1fr);
				background-color: transparent;
				padding: 0;
			}

			.param-item {
				overflow: hidden;
				position: relative;
				min-width: 0;
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				cursor: pointer;
				transition: all 0.3s;
				text-align: center;

				.imgWrap {
					position: relative;
					overflow: hidden;
					display: flex;
					justify-content: center;
					align-items: center;
					margin-bottom: 4px;
					border: 1px solid transparent;
					border-radius: 6px;
					font-size: 0;

					@media (any-hover: hover) {
						&:hover {
							border-color: $color-primary;
						}
					}
				}

				&.active {
					.imgWrap {
						border-color: $color-primary;
					}
				}
			}
		}
	}

	.step-shape {
		min-width: 0;
		grid-area: a;

		@include respond-to(mb) {
			grid-area: auto;
		}

		.step-item-params {
			display: grid;
			grid-template-columns: repeat(4, 1fr);
			align-items: flex-start;
			grid-gap: 0.5em;
			padding: 0.75em;
			background-color: #fafafa;

			@include respond-to(mb) {
				grid-template-columns: repeat(4, 1fr);
				background-color: transparent;
				padding: 0;
			}

			.param-item {
				overflow: hidden;
				position: relative;
				min-width: 0;
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				cursor: pointer;
				transition: all 0.3s;
				text-align: center;

				.imgWrap {
					overflow: hidden;
					display: flex;
					justify-content: center;
					align-items: center;
					margin-bottom: 4px;
					border: 1px solid transparent;
					border-radius: 6px;
					font-size: 0;

					@media (any-hover: hover) {
						&:hover {
							border-color: $color-primary;
						}
					}
				}

				&.active {
					.imgWrap {
						border-color: $color-primary;
					}
				}
			}
		}
	}

	.step-border {
		min-width: 0;
		grid-area: a;
		@include respond-to(mb) {
			grid-area: auto;
		}

		.step-item-params {
			display: grid;
			grid-template-columns: repeat(3, 1fr);
			align-items: flex-start;
			grid-gap: 0.5em;
			padding: 0.75em;
			background-color: #fafafa;

			@include respond-to(mb) {
				grid-template-columns: repeat(3, 1fr);
				padding: 0;
				background-color: transparent;
			}

			.param-item {
				overflow: hidden;
				position: relative;
				min-width: 0;
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				cursor: pointer;
				transition: all 0.3s;
				text-align: center;

				.imgWrap {
					overflow: hidden;
					display: flex;
					justify-content: center;
					align-items: center;
					margin-bottom: 4px;
					border: 1px solid transparent;
					border-radius: 6px;
					font-size: 0;

					@media (any-hover: hover) {
						&:hover {
							border-color: $color-primary;
						}
					}
				}

				&.active {
					.imgWrap {
						border-color: $color-primary;
					}
				}
			}
		}
	}

	.step-options {
		min-width: 0;
		grid-area: c;
		@include respond-to(mb) {
			grid-area: auto;
		}

		.step-item-params {
			display: grid;
			grid-template-columns: repeat(3, 1fr);
			align-items: flex-start;
			grid-gap: 0.5em;
			padding: 0.75em;
			background-color: #fafafa;

			@include respond-to(mb) {
				grid-template-columns: repeat(3, 1fr);
				padding: 0;
				background-color: transparent;
			}

			.param-item {
				overflow: hidden;
				position: relative;
				min-width: 0;
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				cursor: pointer;
				transition: all 0.3s;
				text-align: center;

				.imgWrap {
					overflow: hidden;
					display: flex;
					justify-content: center;
					align-items: center;
					margin-bottom: 4px;
					border: 1px solid transparent;
					border-radius: 6px;
					font-size: 0;

					@media (any-hover: hover) {
						&:hover {
							border-color: $color-primary;
						}
					}
				}

				&.active {
					.imgWrap {
						border-color: $color-primary;
					}
				}
			}
		}
	}

	.step-upload {
		min-width: 0;
		grid-area: b;
		@include respond-to(mb) {
			grid-area: auto;
		}

		.step-item-params {
			position: relative;
			display: flex;
			justify-content: center;
			align-items: center;
			height: 5.88em;
			background-color: #fafafa;

			@include respond-to(mb) {
				width: 100%;
				height: 5em;
			}

			input[type="file"] {
				position: absolute;
				clip: rect(0 0 0 0);
			}

			.uploadWrap {
				position: relative;
				display: flex;
				flex-direction: column;
				align-items: center;
				cursor: pointer;

				.brow {
					margin: 0 4px;
					color: $color-primary;
					text-decoration-line: underline;
				}

				.uploadIcon {
					margin-right: 0.2em;
					font-size: 1.7em;
					color: $color-primary;

					@include respond-to(mb) {
						font-size: 1.5em;
						margin-right: 0.4em;
					}
				}
			}

			.param-item {
				flex: 1;
				position: relative;
				display: flex;
				justify-content: center;
				align-items: center;
				height: 100%;
				cursor: pointer;

				@include respond-to(mb) {
					padding: 0;
				}

				@media (any-hover: hover) {
					b:hover {
						color: $color-primary;
					}

					&:hover {
						border-color: $color-primary;
					}
				}
			}
		}

		.step-item-params.param-item-hasUpload {
			justify-content: space-between;
			padding: 0.2em;

			.param-item-hasUpload-left {
				overflow: auto;
				height: 100%;
				position: relative;
				padding: 0.2em;

				.icon {
					display: flex;
					justify-content: center;
					align-items: center;
					width: 24px;
					height: 24px;
					border-radius: 50%;
					margin: 0 6px;
					background: #ebebeb;

					b {
						color: #888888;
						font-size: 12px;
						transform: rotate(90deg);
					}
				}

				.icon.active {
					b {
						color: $color-primary;
					}
				}

				.upload-name {
					display: inline-block;
					overflow: hidden;
					text-overflow: ellipsis;
					width: 8em;
					white-space: nowrap;
					text-decoration: underline;

					@include respond-to(mb) {
						width: 100px;
					}
				}

				b {
					margin: 0 6px;
					cursor: pointer;
				}

				b.icon-Down {
					font-size: 12px;
				}
			}
		}
	}

	.step-time {
		min-width: 0;
		grid-area: d;
		@include respond-to(mb) {
			grid-area: auto;
		}

		.box-border {
			display: none;
		}

		.step-time-tip {
			font-size: 0.9em;
			margin-bottom: 0.63em;
		}

		.step-item-params {
			display: grid;
			grid-template-columns: repeat(1, 1fr);
			grid-gap: 0.5em;

			@include respond-to(mb) {
				grid-template-columns: repeat(2, 1fr);
				grid-gap: 0.42em;
			}

			.param-item-wrap {
				overflow: hidden;
				position: relative;
				border: 1px solid transparent;
				border-radius: 6px;
				padding: 0.63em;
				background-color: #fafafa;
				box-sizing: border-box;
				cursor: pointer;

				&.active {
					@include selectedStyle;
				}

				.param-item {
					position: relative;
					min-width: 0;
					display: flex;
					justify-content: flex-start;
					align-items: center;
					margin-bottom: 0.2em;
					font-weight: 700;
				}
			}
		}
	}
}

.freeTipWrap {
	.tip {
		margin-top: 0.5em;

		a {
			color: $color-primary;
			text-decoration: underline;
		}
	}
}
</style>