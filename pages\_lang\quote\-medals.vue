<template>
	<div class="quoteWrap">
		<Preloader v-if="isLoading"></Preloader>
		<template v-else>
			<div class="content">
				<QuoteNav :pid="pid" title="We provide these medals types for you."></QuoteNav>
				<QuoteTitle :h1-text="`${lang.cy} ${cateData.cateName}`"></QuoteTitle>
				<div class="leftArea" id="leftArea">
					<template v-for="(item, index) in generalData">
						<PublicStep v-if="item.paramName == 'Medal Type'" :selectedData="selectedData" :config="allStepConfig[item.paramName]" :stepData="item" :maskName.sync="maskName" @clickFun="selectQuoteParams(item, $event)" @showMaskFn="showMaskFn" @viewVideo="viewMedalVideo"></PublicStep>

						<PublicStep v-if="item.paramName == 'quoteCategory' && !quoteStyleData.noShowDetail" :generalData="generalData" :selectedData="selectedData" :config="allStepConfig[item.paramName]" :stepData="item" :maskName.sync="maskName" @clickFun="selectQuoteParams(item, $event)" @showMaskFn="showMaskFn" @viewVideo="viewVideo"></PublicStep>

						<PublicStep v-if="item.paramName == 'Mold Option'" :selectedData="selectedData" :config="allStepConfig[item.paramName]" :stepData="item" :maskName.sync="maskName" @clickFun="selectQuoteParams(item, $event)" @showMaskFn="showMaskFn" @viewVideo="viewMedalVideo"></PublicStep>

						<PublicStep v-if="item.paramName == 'Medal Color'" :selectedData="selectedData" :config="allStepConfig[item.paramName]" :stepData="item" :maskName.sync="maskName" @clickFun="selectQuoteParams(item, $event)" @showMaskFn="showMaskFn" @viewVideo="viewMedalVideo"></PublicStep>

						<PinsSizeSelect v-if="item.paramName == 'Medal Size'" :generalData="generalData" :selectedData="selectedData" :stepData="item" :maskName="maskName" :smallTitle="allStepConfig[item.paramName].smallTitle" :sizeImgP1="allStepConfig[item.paramName].sizeImgP1" @clickFun="selectQuoteParams(item, $event)" @closeMask="closeMask" @showMaskFn="showMaskFn"></PinsSizeSelect>

						<PublicStep v-if="item.paramName == 'Metal Finish'" :selectedData="selectedData" :config="allStepConfig[item.paramName]" :stepData="item" :maskName.sync="maskName" @clickFun="selectQuoteParams(item, $event)" @showMaskFn="showMaskFn" @viewVideo="viewMedalVideo" @showLessBtn="showLessBtn(item.paramName)">
							<template #content="{ customStyle, shouldRenderCount, stepData}">
								<div class="stepContent itemWrap" :style="customStyle">
									<template v-for="(itemChild, childIndex) in stepData.childList">
										<MyCheckBox v-if="childIndex < shouldRenderCount" :key="itemChild.id" :childList="itemChild.childList.length > 0 ? true : false" :tipNum.sync="tipNum" :bindValue="itemChild" :bindName="stepData.paramName" :titleValue="'alias'" :imageValue="'imageJson'" :aspectRatio="'aspect-ratio: 180/146'" :likeModel="selectedData" :sizeValue="sizeValue" @clickFun="selectQuoteParams(stepData, itemChild)" @selectCurrentParams="selectCurrentParams(stepData, itemChild)" @triggerSmallQty="triggerSmallQty" showPrice numberInput></MyCheckBox>
									</template>
								</div>
							</template>
						</PublicStep>

						<PublicStep v-if="item.paramName == 'Additional Upgrades (Options)'" :selectedData="selectedData" :config="allStepConfig[item.paramName]" :stepData="item" :maskName.sync="maskName" @clickFun="selectQuoteParams(item, $event)" @showMaskFn="showMaskFn" @viewVideo="viewVideo"></PublicStep>

						<PublicStep v-if="item.paramName == 'Back Side Option'" :selectedData="selectedData" :config="allStepConfig[item.paramName]" :stepData="item" :maskName.sync="maskName" @clickFun="selectQuoteParams(item, $event)" @showMaskFn="showMaskFn" @viewVideo="viewMedalVideo"></PublicStep>

						<PublicStep v-if="item.paramName == 'Medal Loop'" :selectedData="selectedData" :config="allStepConfig[item.paramName]" :stepData="item" :maskName.sync="maskName" @clickFun="selectQuoteParams(item, $event)" @showMaskFn="showMaskFn" @viewVideo="viewMedalVideo"></PublicStep>

						<PublicStep class="ribbon" v-if="item.paramName == 'Ribbon'" :selectedData="selectedData" :config="allStepConfig[item.paramName]" :stepData="item" :maskName.sync="maskName" @clickFun="selectQuoteParams(item, $event)" @showMaskFn="showMaskFn" @viewVideo="viewMedalVideo">
							<template #content-extend="{ stepData }">
								<RibbonOptinos v-if="selectedData[stepData.paramName].length && selectedData[stepData.paramName][0].paramName !== 'No Ribbon'" :List="selectedData[stepData.paramName][0].childList" :parentName="selectedData[stepData.paramName][0].paramName" :bindValue="RibbonBindValue" @nextStepFun="showMaskFn(stepData.paramName)" @update="updateFun"></RibbonOptinos>
							</template>
						</PublicStep>

						<PublicStep v-if="item.paramName == 'Package'" :selectedData="selectedData" :config="allStepConfig[item.paramName]" :stepData="item" :maskName.sync="maskName" @clickFun="selectQuoteParams(item, $event)" @showMaskFn="showMaskFn" @viewVideo="viewMedalVideo"></PublicStep>

						<template v-if="item.paramName === 'Upload Artwork & Comments'">
							<StepUpload class="step-item step-upload" :class="{ mask: maskName === item.paramName }" :id="item.paramName" :index="item.customIndex" :pid="pid" :itemData="item" :uploadList.sync="uploadArtworkList" :remark.sync="remark" :isUpload.sync="isUpload" @closeMask="closeMask" @showMaskFn="showMaskFn" :key="index"></StepUpload>
						</template>
						<template v-if="item.paramName == 'Delivery Date'">
							<StepTime class="step-item step-date" :class="{ mask: maskName === item.paramName }" :id="item.paramName" :cateData="cateData" :customQty="customQty" :index="item.customIndex" :itemData="item" :priceInfo="priceInfo" :previewMode="previewMode" :selectedParams="selectedData" @showMaskFn="showMaskFn" @selectItem="selectQuoteParams($event.item, $event.citem)" @closeMask="closeMask" :key="index"></StepTime>
						</template>
					</template>
				</div>
				<div class="rightArea" id="rightAreaCustom">
					<transition enter-active-class="animate__animated animate__faster animate__fadeInBottomLeft" leave-active-class="animate__animated animate__faster animate__fadeOutBottomLeft">
						<Detail v-show="showRightArea" class="type1" :class="{ mask: maskName }" :key="1" :absoluteImg="comeFromDZ.absoluteImg" :selectedData="selectedData" :priceInfo="priceInfo" :customQty="customQty" :textInfo="textInfo" :generalData="generalData" @toPosition="toPosition" @addInquiry="debounceAddInquiry" @addCart="addCart"></Detail>
					</transition>
				</div>
			</div>
			<div class="footer" id="foot">
				<Detail class="type2" :key="3" :absoluteImg="comeFromDZ.absoluteImg" :selectedData="selectedData" :priceInfo="priceInfo" :customQty="customQty" :textInfo="textInfo" :generalData="generalData" @toPosition="toPosition" @addInquiry="debounceAddInquiry" @addCart="addCart"></Detail>
			</div>
			<el-drawer :visible.sync="showArea" :with-header="true" custom-class="drawDialog" :size="device === 'ipad' ? '728px' : '85%'">
				<Detail class="type3" :key="2" :absoluteImg="comeFromDZ.absoluteImg" :selectedData="selectedData" :priceInfo="priceInfo" :customQty="customQty" :textInfo="textInfo" :generalData="generalData" @toPosition="toPosition" @addInquiry="debounceAddInquiry" @addCart="addCart"></Detail>
			</el-drawer>
			<!--    遮罩-->
			<myMask :maskName.sync="maskName"></myMask>
			<!-- 建议弹窗-->
			<RecomendDialog :commentsDialog.sync="commentsDialog" :paramValue="selectedParamsValue" @next="recomendNext"></RecomendDialog>
			<!--  手机，ipad端预览-->
			<PreviewBtn :showArea.sync="showArea"></PreviewBtn>
			<BaseDialog v-model="showOtoDialog" :width="device != 'mb' ? '570px' : '90%'">
				<div class="otoWrap">
					<img src="@/assets/images/oto.png" alt="" />
					<p>{{ lang.p1 }}</p>
					<h3>{{ lang.p2 }}</h3>
					<p style="color: #666666">
						{{ lang.p3 }}<br />
						{{ lang.p4 }}.
					</p>

					<div class="box">
						<p class="t1">
							{{ lang.p5 }} <br />
							{{ lang.p6 }}!
						</p>
						<a href="https://www.o2o.co/previewWeb" style="text-decoration: none">
							<el-button type="primary">{{ lang.p7 }}</el-button>
						</a>
					</div>
				</div>
			</BaseDialog>
			<VideoPreviewDialog :pic-dialog.sync="picDialog" :zoom-pic="zoomPic" :temp-type="tempType" :selected-params-value="selectedParamsValue" @qtyAndBtnConfirm="qtyAndBtnConfirm" @qtyAndBtnNext="qtyAndBtnNext" @dialogNextStep="dialogNextStep"></VideoPreviewDialog>
			<infoDialog :infoDialogVisible.sync="infoDialogVisible" @getValue="getValueFun" :uploadList.sync="uploadList" :recomendUpload="recomendUpload" :otherUpload="(RibbonBindValue.uploadList ?? []).concat(uploadArtworkList)"></infoDialog>
			<BaseDialog v-model="noFileDialog" :width="device != 'mb' ? '485px' : '90%'" :model="false">
				<template #closeIcon>
					<div style="display: none"></div>
				</template>
				<infoUpload :infoUploadList="infoUploadList" @pushInfoList="pushInfoList" @delInfoList="delInfoList" @updateInquiry="updateInquiry" @closeInfoDialog="closeInfoDialog"></infoUpload>
			</BaseDialog>
		</template>
	</div>
</template>
<script>
import "@/plugins/element";
import MyCheckBox from "@/components/Medals/MyCheckBox";
import BaseDialog from "@/components/Quote/BaseDialog";
import RibbonOptinos from "@/components/Medals/RibbonOptinos";
import infoDialog from "@/components/Medals/infoDialog";
import quoteMixin from "@/mixins/quote";
import quoteBanChoiceMixins from "@/mixins/quoteBanChoice";
import Preloader from "@/components/Quote/Preloader";
import RecomendDialog from "@/components/Quote/RecomendDialog.vue";
import PreviewBtn from "@/components/Quote/PreviewBtn.vue";
import StepTime from "@/components/Quote/StepTime.vue";
import myMask from "@/components/Quote/Mask.vue";
import Detail from "@/components/Quote/Detail.vue";
import StepUpload from "@/components/Quote/StepUpload.vue";
import QuoteNav from "@/components/Medals/QuoteNav.vue";
import VideoPreviewDialog from "@/components/Quote/VideoPreviewDialog.vue";
import { getQuoteConfig } from "@/assets/js/quote/quoteConfig";
import QuoteTitle from "@/components/Quote/QuoteTitle.vue";
import PublicStep from "@/components/Quote/PublicStep/index.vue";
import PinsSizeSelect from "@/components/Quote/PinsSizeSelect.vue";

export default {
	head() {
		return {
			title: "Quote page",
		};
	},
	components: {
		PinsSizeSelect,
		PublicStep,
		QuoteTitle,
		VideoPreviewDialog,
		QuoteNav,
		StepUpload,
		Detail,
		myMask,
		StepTime,
		PreviewBtn,
		RecomendDialog,
		MyCheckBox,
		BaseDialog,
		RibbonOptinos,
		infoDialog,
		Preloader,
	},
	mixins: [quoteMixin, quoteBanChoiceMixins],
	data() {
		const config = getQuoteConfig.call(this, this.$route.name);
		return {
			showAll: false,
			tipNum: 1,
			update: false,
			RibbonBindValue: {},
			...config,
		};
	},

	methods: {
		viewMedalVideo() {
			this.viewVideo(arguments[0], arguments[1], "video");
		},
		updateFun() {
			this.update = !this.update;
		},
		triggerSmallQty() {
			let find = this.generalData.find((item) => item.paramName === "Metal Finish");
			let qty = 0;
			find.childList.forEach((item) => {
				qty += item?.inputNum || 0;
			});
			this.customQty = qty;
		},
	},
};
</script>

<style scoped lang="scss">
@import "@/assets/css/quotePublic";
</style>