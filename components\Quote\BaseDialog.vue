<template>
	<div class="base-dialog" v-if="value" @click.self="closeDialog('self')">
		<div class="base-dialog-model-con" :style="{ maxWidth: maxWidth, minWidth: minWidth, width: width,verticalAlign: position }" v-bind="$attrs" v-on="$listeners">
			<slot></slot>
			<slot name="closeIcon" v-if="!hideClose">
				<b class="icon-guanbi" @click.stop="closeDialog('close')"></b>
			</slot>
		</div>
	</div>
</template>
<script>
export default {
	props: {
		persistent: {
			type: Boolean,
			default: false,
		},
		value: {
			type: Boolean,
			default: false,
		},
		width: {
			type: [String, Number],
			default: "50%",
		},
		maxWidth: {
			type: [String, Number],
		},
		model: {
			type: Boolean,
			default: true,
		},
		minWidth: {
			type: [String, Number],
		},
		beforeClose: {
			type: Function,
		},
        hideClose:{
            type: <PERSON>olean,
            default: false,
        },
        position:{
            type: String,
            default: "center"
        }
	},
	model: {
		prop: "value",
		event: "update", // onchange 事件
	},
	methods: {
		closeDialog(type = "close") {
			if (!this.model && type === "self") return;
			if (type === "self" && this.persistent) {
				return;
			}
			if (typeof this.beforeClose === "function") {
				this.beforeClose();
			}
			this.$emit("update", false);
		},
	},
};
</script>
<style lang="scss" scoped>
.base-dialog {
	position: fixed;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	z-index: 2000;
	height: 100%;
	background-color: rgba(0, 0, 0, 0.5);
	overflow: auto;
	white-space: nowrap;
	text-align: center;

	&:after {
		content: "";
		display: inline-block;
		vertical-align: middle;
		height: 100%;
	}

	.base-dialog-model-con {
		display: inline-block;
		vertical-align: middle;
		position: relative;
		margin: 0 auto;
		background: #fff;
		border-radius: 6px;
		box-shadow: 1px 2px 20px 0 rgba(0, 0, 0, 0.2);
		text-align: left;
		white-space: normal;

		// margin:20px 0;
		.icon-guanbi {
			display: flex;
			justify-content: center;
			align-items: center;
			font-weight: 700;
			position: absolute;
			top: 5px;
			right: 5px;
			width: 40px;
			height: 40px;
			cursor: pointer;
			background: #ffffff;
			// box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.3);
			border-radius: 50%;
			z-index: 9999;
		}

		@media screen and (max-width: 767px) {
			.icon-guanbi {
				width: 25px;
				height: 25px;
				font-size: 14px;
				top:-6px;
				right:-7px;
			}
		}
	}
}
</style>
