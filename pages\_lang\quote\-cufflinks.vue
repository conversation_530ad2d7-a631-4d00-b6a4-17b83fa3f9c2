<template>
	<div class="quoteWrap" id="custom-cufflinks">
		<Preloader v-if="isLoading"></Preloader>
		<template v-else>
			<div class="quoteNavWrap">
				<QuoteNav :pid="pid" :config="{ slidesPerView: 6 }" title="We provide these Cufflinks types for you."></QuoteNav>
			</div>
			<div class="content">
				<div class="header">
					<h1>{{ cateData.cateName }}</h1>
				</div>
				<div class="center-content">
					<div class="leftArea" id="leftArea">
						<!-- 左上 <-> 右下 的消失动画 -->
						<transition enter-active-class="animate__animated animate__faster animate__fadeInBottomRight" leave-active-class="animate__animated animate__faster animate__fadeOutBottomRight">
							<BusinessCardDetail :imgList="carouselList" :showViewMore="false" :selectedData="selectedData" :generalData="filterShowGeneralData" @toPosition="toPosition" :attachment="attachment"></BusinessCardDetail>
						</transition>
					</div>
					<div class="rightArea" id="rightAreaCustom">
						<template v-for="(item, index) in filterShowGeneralData">
							<PublicStep v-if="item.paramName == 'Size'" titleComponentName="StepTitleCufflinks" :selectedData="selectedData" :config="{ ...allStepConfig[item.paramName], showPriceText: false }" :stepData="item" :maskName.sync="maskName" @clickFun="selectSize(item, $event)"></PublicStep>
							<PublicStep class="select-plating" v-if="item.paramName == 'Select Plating / Finish'" titleComponentName="StepTitleCufflinks" :selectedData="selectedData" :config="{ ...allStepConfig[item.paramName], showPriceText: false }" :stepData="item" :maskName.sync="maskName" @clickFun="selectQuoteParams(item, $event)"></PublicStep>
							<!-- <PublicStep  v-if="item.paramName == 'Engraving Options'" titleComponentName="StepTitleCufflinks"  :selectedData="selectedData" :config="{...allStepConfig[item.paramName],showPriceText:false}" :stepData="item" :maskName.sync="maskName" @clickFun="selectQuoteParams(item, $event)"></PublicStep> -->
							<PublicStep v-if="item.paramName == 'Engraving Options'" titleComponentName="StepTitleCufflinks" :generalData="generalData" :selectedData="selectedData" :config="allStepConfig[item.paramName]" :stepData="item" :maskName.sync="maskName">
								<template #content>
									<div class="stepContent itemWrap">
										<template v-for="(citem, cindex) in item.childList">
											<div class="stepItem" :key="citem.id" @click="selectQuoteParams(item, citem)" :class="{ active: hasId(citem.id, selectedData[item.paramName]) }">
												<div class="imgWrap">
													<img loading="lazy" :src="getImgUrl(citem)" :alt="getImgAlt(citem)" />
												</div>
												<div class="step-item-content">
													<CustomCircle :isActive="hasId(citem.id, selectedData[item.paramName])" :circle-type="2"></CustomCircle>
													<div>
														{{ citem.alias }}
													</div>
												</div>
												<el-popover placement="bottom" width="300" trigger="hover">
													<img loading="lazy" :src="getImgUrl(citem)" :alt="getImgAlt(citem)" />
													<div class="zoom" slot="reference" @click.stop>
														<i class="el-icon-zoom-in"></i>
													</div>
												</el-popover>
											</div>
										</template>
									</div>
								</template>
							</PublicStep>
							<PublicStep v-if="item.paramName == 'More Options'" titleComponentName="StepTitleCufflinks" :selectedData="selectedData" :config="allStepConfig[item.paramName]" :stepData="item" :maskName.sync="maskName">
								<template #content>
									<div class="stepContent itemWrap more-options">
										<template v-for="(citem, cindex) in item.childList">
											<div class="stepItem" :key="citem.id" @click="selectQuoteParams(item, citem)" :class="{ active: hasId(citem.id, selectedData[item.paramName]) }">
												<div class="imgWrap">
													<img loading="lazy" :src="getImgUrl(citem)" :alt="getImgAlt(citem)" />
												</div>
												<div class="step-item-content">
													<CustomCircle :isActive="hasId(citem.id, selectedData[item.paramName])" :circle-type="2"></CustomCircle>
													<div>
														{{ citem.alias }}
														<div class="price">
															<PriceText :paramData="citem"></PriceText>
														</div>
													</div>
												</div>
												<el-popover placement="bottom" width="300" trigger="hover" v-show="cindex > 0">
													<img loading="lazy" :src="getImgUrl(citem)" :alt="getImgAlt(citem)" />
													<div class="zoom" slot="reference" @click.stop>
														<i class="el-icon-zoom-in"></i>
													</div>
												</el-popover>
											</div>
										</template>
									</div>
								</template>
							</PublicStep>
							<PublicStep v-if="item.paramName == 'Upload Artwork & Comments'" titleComponentName="StepTitleCufflinks" :showTitleTips="false" :selectedData="selectedData" :config="allStepConfig[item.paramName]" :stepData="item" :maskName.sync="maskName">
								<template #content>
									<div class="textWrap" v-if="pid === 763">
										<div class="leftText">
											<div class="textDes">Left</div>
											<div class="textItemWrap">
												<div class="textItem" v-for="(tItem, tIndex) in leftTextArr" :key="tIndex">
													<el-input v-model="tItem.text" :placeholder="langSemiCustom.enterHere"></el-input>
													<el-select v-model="tItem.fontFamily" :placeholder="langSemiCustom.ff">
														<el-option v-for="fItem in fontsData" :key="fItem.name" :label="fItem.name" :value="fItem.name" :style="{ fontFamily: fItem.name }"></el-option>
													</el-select>
													<div class="font-bold" :class="{ active: tItem.fontWeight === 'bold' }" @click="changeTextProperty(tItem.fontWeight === 'normal' ? 'bold' : 'normal', 'fontWeight', tItem)">B</div>
													<div class="font-style" :class="{ active: tItem.fontStyle === 'italic' }" @click="changeTextProperty(tItem.fontStyle === 'normal' ? 'italic' : 'normal', 'fontStyle', tItem)">I</div>
													<el-popover placement="bottom" width="300" trigger="click">
														<div class="color-picker-wrap custom-scrollbar">
															<div class="color-picker-title">{{ langSemiCustom.ec }}</div>
															<div class="color-picker-list">
																<div class="color-item" :class="{ active: colorItem.code === tItem.color }" v-for="colorItem in getColorList" :key="colorItem.id" :style="{ backgroundColor: colorItem.code }" :title="colorItem.pantone" @click="changeTextProperty(colorItem.code, 'color', tItem)">
																	<i class="el-icon-check" style="color: #ffffff"></i>
																</div>
															</div>
														</div>
														<div class="font-color" :style="{ '--bg-color': tItem.color }" slot="reference">
															<div>A</div>
														</div>
													</el-popover>
													<template v-if="tIndex === 0">
														<div class="line"></div>
														<div class="addNewLine" @click="addNewLeftText"><b class="icon-a-icon-dzlj-addzhuanhuan"></b>{{ langSemiCustom.add }}</div>
													</template>
													<template v-else>
														<div class="line"></div>
														<div class="addNewLine" @click="delLeftText(tIndex)">
															<b class="icon-shanchu2"></b>
														</div>
													</template>
												</div>
											</div>
										</div>
										<div class="rightText">
											<div class="textDes">Right</div>
											<div class="textItemWrap">
												<div class="textItem" v-for="(tItem, tIndex) in rightTextArr" :key="tIndex">
													<el-input v-model="tItem.text" :placeholder="langSemiCustom.enterHere"></el-input>
													<el-select v-model="tItem.fontFamily" :placeholder="langSemiCustom.ff">
														<el-option v-for="fItem in fontsData" :key="fItem.name" :label="fItem.name" :value="fItem.name" :style="{ fontFamily: fItem.name }"></el-option>
													</el-select>
													<div class="font-bold" :class="{ active: tItem.fontWeight === 'bold' }" @click="changeTextProperty(tItem.fontWeight === 'normal' ? 'bold' : 'normal', 'fontWeight', tItem)">B</div>
													<div class="font-style" :class="{ active: tItem.fontStyle === 'italic' }" @click="changeTextProperty(tItem.fontStyle === 'normal' ? 'italic' : 'normal', 'fontStyle', tItem)">I</div>
													<el-popover placement="bottom" width="300" trigger="click">
														<div class="color-picker-wrap custom-scrollbar">
															<div class="color-picker-title">{{ langSemiCustom.ec }}</div>
															<div class="color-picker-list">
																<div class="color-item" :class="{ active: colorItem.code === item.color }" v-for="colorItem in getColorList" :key="colorItem.id" :style="{ backgroundColor: colorItem.code }" :title="colorItem.pantone" @click="changeTextProperty(colorItem.code, 'color', tItem)">
																	<i class="el-icon-check" style="color: #ffffff"></i>
																</div>
															</div>
														</div>
														<div class="font-color" :style="{ '--bg-color': tItem.color }" slot="reference">
															<div>A</div>
														</div>
													</el-popover>
													<template v-if="tIndex === 0">
														<div class="line"></div>
														<div class="addNewLine" @click="addNewRightText"><b class="icon-a-icon-dzlj-addzhuanhuan"></b>{{ langSemiCustom.add }}</div>
													</template>
													<template v-else>
														<div class="line"></div>
														<div class="addNewLine" @click="delRightText(tIndex)">
															<b class="icon-shanchu2"></b>
														</div>
													</template>
												</div>
											</div>
										</div>
									</div>
									<div class="stepContent upload">
										<div class="uploadList custom-scrollbar" v-if="uploadArtworkList.length">
											<ul>
												<li v-for="(item, index) in uploadArtworkList" class="uploadItem" :key="item.secure_url">
													<span>{{ item.original_filename }}</span>
													<div>
														<b class="icon-check myIcon" style="color: #0cbd5f"></b>
														<span @click.stop="delUploadImg(index)">
															<b class="icon-shanchu2 myIcon" style="color: #b6b0b0"></b>
														</span>
													</div>
												</li>
											</ul>
										</div>
										<div class="t1">
											<div class="upBtn" @click="openUpload(item, 'upload')">
												<b class="icon-shangchuan uploadIcon"></b>
												<span class="up">{{ lang.Upload }}</span>
											</div>
											<span class="re">{{ lang.pra }}</span>
											<el-tooltip popper-class="cusToolTip" effect="light" v-if="item.tips" :content="item.tips" placement="top-start">
												<b class="icon-wenhao1 wenhaoIcon"></b>
											</el-tooltip>
										</div>
										<div class="t2">{{ lang.notes }}</div>
										<input type="file" :accept="acceptFileType" multiple ref="upload" @change="uploadPic" />
									</div>
									<div class="stepFooter" v-show="maskName === item.paramName">
										<QuoteBtn @click.native="showMaskFn(item.paramName)">
											{{ lang.next }}
										</QuoteBtn>
									</div>
								</template>
							</PublicStep>
							<PublicStep v-if="item.paramName == 'Select Attachment'" titleComponentName="StepTitleCufflinks" :selectedData="selectedData" :config="allStepConfig[item.paramName]" :stepData="item" :maskName.sync="maskName">
								<template #content>
									<div class="stepContent itemWrap">
										<template v-for="citem in item.childList">
											<div class="stepItem" :key="citem.id" @click="selectQuoteParams(item, citem)" :class="{ active: hasId(citem.id, selectedData[item.paramName]) }">
												<div class="imgWrap">
													<img loading="lazy" :src="getImgUrl(citem)" :alt="getImgAlt(citem)" />
												</div>
												<div class="step-item-content">
													<CustomCircle :isActive="hasId(citem.id, selectedData[item.paramName])" :circle-type="2"></CustomCircle>
													<div>
														{{ citem.alias }}
														<div class="price">
															<PriceText :paramData="citem"></PriceText>
														</div>
													</div>
												</div>
												<!-- <div class="checked">
													<i class="el-icon-check"></i>
												</div> -->
												<el-popover placement="bottom" width="300" trigger="hover">
													<img loading="lazy" :src="getImgUrl(citem)" :alt="getImgAlt(citem)" />
													<div class="zoom" slot="reference" @click.stop>
														<i class="el-icon-zoom-in"></i>
													</div>
												</el-popover>
											</div>
										</template>
									</div>
								</template>
							</PublicStep>
							<PublicStep v-if="item.paramName == 'Select Packaging Options'" titleComponentName="StepTitleCufflinks" :selectedData="selectedData" :config="allStepConfig[item.paramName]" :stepData="item" :maskName.sync="maskName">
								<template #content>
									<div class="stepContent itemWrap">
										<template v-for="citem in item.childList">
											<div class="stepItem" :key="citem.id" @click="selectQuoteParams(item, citem)" :class="{ active: hasId(citem.id, selectedData[item.paramName]) }">
												<div class="imgWrap">
													<img loading="lazy" :src="getImgUrl(citem)" :alt="getImgAlt(citem)" />
												</div>
												<div class="step-item-content">
													<CustomCircle :isActive="hasId(citem.id, selectedData[item.paramName])" :circle-type="2"></CustomCircle>
													<div>
														{{ citem.alias }}
														<div class="price">
															<PriceText :paramData="citem"></PriceText>
														</div>
													</div>
												</div>
												<!-- <div class="checked">
													<i class="el-icon-check"></i>
												</div> -->
												<!-- <el-popover placement="bottom" width="300" trigger="hover">
													<img loading="lazy" :src="getImgUrl(citem)" :alt="getImgAlt(citem)" />
													<div class="zoom" slot="reference" @click.stop>
														<i class="el-icon-zoom-in"></i>
													</div>
												</el-popover> -->
											</div>
										</template>
									</div>
								</template>
							</PublicStep>
							<PublicStep v-if="item.paramName == 'Select Back Side Option'" titleComponentName="StepTitleCufflinks" :selectedData="selectedData" :config="allStepConfig[item.paramName]" :stepData="item" :maskName.sync="maskName">
								<template #content>
									<div class="stepContent itemWrap back-side">
										<template v-for="citem in item.childList">
											<div class="stepItem" :key="citem.id" @click="selectQuoteParams(item, citem)" :class="{ active: hasId(citem.id, selectedData[item.paramName]) }">
												<div class="imgWrap">
													<img loading="lazy" :src="getImgUrl(citem)" :alt="getImgAlt(citem)" />
												</div>
												<div class="step-item-content">
													<CustomCircle :isActive="hasId(citem.id, selectedData[item.paramName])" :circle-type="2"></CustomCircle>
													<div>
														{{ citem.alias }}
														<div class="price">
															<PriceText :paramData="citem"></PriceText>
														</div>
													</div>
												</div>
												<el-popover placement="bottom" width="300" trigger="hover">
													<img loading="lazy" :src="getImgUrl(citem)" :alt="getImgAlt(citem)" />
													<div class="zoom" slot="reference" @click.stop>
														<i class="el-icon-zoom-in"></i>
													</div>
												</el-popover>
											</div>
										</template>
									</div>
								</template>
							</PublicStep>
							<PublicStep v-if="item.paramName == 'Quantity'" titleComponentName="StepTitleCufflinks" :selectedData="selectedData" :config="allStepConfig[item.paramName]" :stepData="item" :maskName.sync="maskName">
								<template #content>
									<div class="inputWrap">
										<el-input :value="customQty" :placeholder="lang.nameBadges.inputModel.quantity" @input="updateQty" />
										<div class="rowOne">Pair</div>
									</div>
									<div class="stepFooter" v-show="maskName === item.paramName">
										<QuoteBtn @click.native="showMaskFn(item.paramName)" :disabled="!customQty">
											{{ lang.next }}
										</QuoteBtn>
									</div>
								</template>
							</PublicStep>
							<PublicStep class="turnaroundTime" v-if="item.paramName == 'Select Turnaround Time'" titleComponentName="StepTitleCufflinks" :selectedData="selectedData" :config="allStepConfig[item.paramName]" :stepData="item" :maskName.sync="maskName" @showMaskFn="showMaskFn">
								<template #content>
									<div class="stepContent timeWrap">
										<template v-for="citem in item.childList">
											<div class="stepItem" :key="citem.id" @click="selectQuoteParams(item, citem)" :class="{ active: hasId(citem.id, selectedData[item.paramName]) }">
												<div class="time-item-content">
													<CustomCircle :isActive="hasId(citem.id, selectedData[item.paramName])" :circle-type="2"></CustomCircle>
													<DiscountText class="discountText" :itemData="citem"></DiscountText>
												</div>
												<div class="discountDes">
													{{ citem.alias }}
												</div>
											</div>
										</template>
									</div>
									<div class="attachFile">
										<el-input class="remarkInput" rows="3" type="textarea" v-model="remark" :placeholder="lang.Comments"></el-input>
										<div class="des">
											<span v-show="!attachFileList.length">{{ langSemiCustom.noattached }}</span>
											<a href="javascript:;" @click="openUpload(item, 'uploadPic')"> + {{ langSemiCustom.attachFile }}</a>
										</div>
										<div class="fileWrap">
											<div class="file-item" v-for="(item, index) in attachFileList" :key="item.secure_url">
												<div class="fileInfo d-flex align-items-center">
													<v-icon class="mr-1">mdi-file</v-icon>
													<span class="fileName">{{ item.original_filename }}</span>
												</div>
												<div class="control">
													<b class="icon-check myIcon" style="color: #0cbd5f"></b>
													<span @click.stop="deletePic(index)">
														<b class="icon-shanchu2 myIcon" style="color: #b6b0b0"></b>
													</span>
												</div>
											</div>
										</div>
										<input type="file" :accept="acceptFileType" multiple ref="uploadPic" @change="uploadPic" />
									</div>
								</template>
							</PublicStep>
						</template>
						<div class="subtotal" id="foot">
							<div class="sub-detail">
								<div class="subtotal-left">
									<div class="sub-item summary">
										<div>
											{{ langQuote.orderSummary }}
										</div>
									</div>
									<div class="sub-item">
										<div class="sub-item-left">{{ langSemiCustom.quantity }}:</div>
										<div class="sbu-item-right">
											{{ priceInfo.totalQuantity || 0 }}
										</div>
									</div>
									<div class="sub-item">
										<div class="sub-item-left">{{ langSemiCustom.unitPrice }}:</div>
										<div class="sbu-item-right">
											<CCYRate :price="priceInfo.foundationUnitPrice"></CCYRate>
										</div>
									</div>
									<div class="sub-item" v-show="!onlyAddInquiry && priceInfo.toolingCharge && priceInfo.toolingCharge > 0">
										<div class="sub-item-left">{{ lang.moldPrice }}:</div>
										<div class="sbu-item-right">
											<CCYRate :price="priceInfo.toolingCharge"></CCYRate>
										</div>
									</div>
									<div class="sub-item" v-show="!onlyAddInquiry && priceInfo.setupCharge && priceInfo.setupCharge > 0">
										<div class="sub-item-left">{{ langSemiCustom.setupCharge }}:</div>
										<div class="sbu-item-right">
											<CCYRate :price="priceInfo.setupCharge"></CCYRate>
										</div>
									</div>
									<div class="sub-item" v-show="!onlyAddInquiry && discountPrice != 0">
										<div class="sub-item-left">{{ text1 }}</div>
										<div class="sbu-item-right">
											{{ text2 }}
											<CCYRate :price="discountPrice"></CCYRate>
										</div>
									</div>
								</div>
								<div class="subtotal-right">
									<div class="totalPriceBox">
										<span>{{ lang.subtotal }}:</span>
										<CCYRate class="final-price" :price="priceInfo.totalPrice"></CCYRate>
									</div>
									<div>
										<freeTip></freeTip>
										<div style="display: flex">
											<button type="button" id="addInquiryBtn" class="inquiryBtn" @click="addInquiry">
												{{ langSemiCustom.SubmitInquiry }}
												<span @click.stop>
													<el-tooltip popper-class="cusToolTip" effect="light" :content="textInfo.inquiryTip" placement="top-start">
														<b class="icon-wenhao3 tip-icon"></b>
													</el-tooltip>
												</span>
											</button>
											<button type="button" id="addCartBtn" @click="addCart">
												{{ langQuote.addToCart }}
												<span @click.stop>
													<el-tooltip popper-class="cusToolTip" effect="light" :content="textInfo.addCartTip" placement="top-start">
														<b class="icon-wenhao3 tip-icon2"></b>
													</el-tooltip>
												</span>
											</button>
										</div>
										<!-- <div class="inquiryBtnBox">
											<span style="color: #000">{{ langLogin.or + " " }}</span>
											<span class="inquiryBtn" title="custom product inquiry" @click="addInquiry">
												{{ langSemiCustom.detailInquiry }}
											</span>
										</div> -->
									</div>
								</div>
							</div>
							<div class="page-des" :style="{ textAlign: 'left' }">
								<div class="productionTime">
									{{ langSemiCustom.productionTime }}
								</div>
								<div class="lookingFor" style="margin-left: -4px">
									<span style="user-select: none; opacity: 0; visibility: hidden">*</span>
									{{ langSemiCustom.lookingFor }}
									<a :href="'mailto:' + $store.state.proSystem.email">{{ langSemiCustom.contactUs }}</a>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<el-drawer :visible.sync="showArea" :with-header="true" custom-class="drawDialog" :size="device === 'ipad' ? '728px' : '85%'">
				<Detail class="type3" :key="2" :absoluteImg="comeFromDZ.absoluteImg" :selectedData="selectedData" :priceInfo="priceInfo" :customQty="customQty" :textInfo="textInfo" :generalData="generalData" @toPosition="toPosition" @addInquiry="debounceAddInquiry" @addCart="addCart"></Detail>
			</el-drawer>
			<!--    遮罩-->
			<myMask :maskName.sync="maskName"></myMask>
			<!-- 建议弹窗-->
			<RecomendDialog :commentsDialog.sync="commentsDialog" :paramValue="selectedParamsValue" @next="recomendNext"></RecomendDialog>
			<!--  手机，ipad端预览-->
			<PreviewBtn :showArea.sync="showArea"></PreviewBtn>
			<BaseDialog v-model="showOtoDialog" :width="device != 'mb' ? '570px' : '90%'">
				<div class="otoWrap">
					<img src="@/assets/images/oto.png" alt="" />
					<p>{{ lang.p1 }}</p>
					<h3>{{ lang.p2 }}</h3>
					<p style="color: #666666">
						{{ lang.p3 }}<br />
						{{ lang.p4 }}.
					</p>

					<div class="box">
						<p class="t1">
							{{ lang.p5 }}<br />
							{{ lang.p6 }}!
						</p>
						<a href="https://www.o2o.co/previewWeb" style="text-decoration: none">
							<el-button type="primary">{{ lang.p7 }}</el-button>
						</a>
					</div>
				</div>
			</BaseDialog>
			<VideoPreviewDialog :pic-dialog.sync="picDialog" :zoom-pic="zoomPic" :temp-type="tempType" :selected-params-value="selectedParamsValue" @qtyAndBtnConfirm="qtyAndBtnConfirm" @qtyAndBtnNext="qtyAndBtnNext" @dialogNextStep="dialogNextStep"></VideoPreviewDialog>
			<BaseDialog v-model="upgradeDialog" :width="device != 'mb' ? '640px' : '320px'">
				<Upgrade @closeUpgrade="upgradeDialog = false" :tipArr="tipArr" :typeName="cateData.cateName" unit="pairs" @upgradeLevel="upgradeLevel"></Upgrade>
			</BaseDialog>
			<infoDialog :infoDialogVisible.sync="infoDialogVisible" @getValue="getValueFun" :otherUpload="uploadArtworkList.concat(attachFileList)" :recomendUpload="recomendUpload" :uploadList.sync="uploadList"></infoDialog>
			<BaseDialog v-model="noFileDialog" :width="device != 'mb' ? '485px' : '90%'" :model="false">
				<template #closeIcon>
					<div style="display: none"></div>
				</template>
				<infoUpload :infoUploadList="infoUploadList" @pushInfoList="pushInfoList" @delInfoList="delInfoList" @updateInquiry="updateInquiry" @closeInfoDialog="closeInfoDialog"></infoUpload>
			</BaseDialog>
		</template>
	</div>
</template>
<script>
import "@/plugins/element";
import BaseDialog from "@/components/Quote/BaseDialog";
import VideoPlayer from "@/components/Quote/VideoPlayer.vue";
import Upgrade from "@/components/Quote/Upgrade";
import infoDialog from "@/components/Medals/infoDialog";
import QuoteNav from "@/components/Medals/QuoteNav";
import QuoteBtn from "@/components/Quote/QuoteBtn";
import quoteMixin from "@/mixins/quote";
import quoteBanChoiceMixins from "@/mixins/quoteBanChoice";
import RecomendDialog from "@/components/Quote/RecomendDialog.vue";
import myMask from "@/components/Quote/Mask.vue";
import Detail from "@/components/Quote/Detail.vue";
import SwiperDetail from "@/components/Cufflinks/SwiperDetail.vue";
import PreviewBtn from "@/components/Quote/PreviewBtn.vue";
import Preloader from "@/components/Quote/Preloader.vue";
import BusinessCardDetail from "@/components/semiquote/BusinessCardDetail.vue";
import { round2 } from "@/utils/utils";
import { getQuoteConfig } from "@/assets/js/quote/quoteConfig";
import PublicStep from "@/components/Quote/PublicStep/index.vue";
import CustomCircle from "@/components/Quote/customCircle.vue";
import PriceText from "@/components/Quote/PriceText.vue";
import DiscountText from "@/components/modal/Quote/QuoteComponents/DicountText.vue";
import freeTip from "@/components/Quote/freeTip.vue";
import { getTaxByPrice } from "@/api/web";
import { acceptFileType, checkFile } from "@/utils/validate";
import { uploadFile } from "@/utils/oss";
import VideoPreviewDialog from "@/components/Quote/VideoPreviewDialog.vue";

class TextModel {
	constructor(area) {
		this.area = area;
		this.color = "#000000";
		this.fontFamily = undefined;
		this.fontStyle = "normal";
		this.fontWeight = "normal";
		this.text = undefined;
	}
}

export default {
	head() {
		return {
			title: "Quote page",
		};
	},
	components: {
		VideoPreviewDialog,
		freeTip,
		DiscountText,
		PriceText,
		CustomCircle,
		PublicStep,
		Preloader,
		PreviewBtn,
		Detail,
		SwiperDetail,
		myMask,
		RecomendDialog,
		BaseDialog,
		VideoPlayer,
		infoDialog,
		Upgrade,
		QuoteNav,
		QuoteBtn,
		BusinessCardDetail,
	},
	mixins: [quoteMixin, quoteBanChoiceMixins],
	data() {
		const config = getQuoteConfig.call(this, this.$route.name);
		return {
			leftTextArr: [new TextModel("left")],
			rightTextArr: [new TextModel("right")],
			textPrice: "",
			countryName: "",
			continentName: "",
			acceptFileType,
			currentStep: "",
			refName: "",
			isIframe: false,
			...config,
		};
	},
	methods: {
		addNewLeftText() {
			this.leftTextArr.unshift(new TextModel("left"));
		},
		delLeftText(index) {
			this.leftTextArr.splice(index, 1);
		},
		addNewRightText() {
			this.rightTextArr.unshift(new TextModel("right"));
		},
		delRightText(index) {
			this.rightTextArr.splice(index, 1);
		},
		changeTextProperty(val, property, item) {
			item[property] = val;
		},
		openUpload(item, refName) {
			this.refName = refName;
			this.currentStep = item;
			this.$refs[refName][0].click();
		},
		//判断是否选中
		hasId(id, arr) {
			if (!arr) {
				return false;
			}
			return !!arr.find((item) => {
				return item.id === id;
			});
		},
		getImgUrl(item) {
			return this.parseJSON(item.imageJson)[0].url;
		},
		getImgAlt(item) {
			return this.parseJSON(item.imageJson)[0].url;
		},
		updateQty(val) {
			val = val.replace(/[^0-9]/g, "");
			if (val > 999999) {
				val = "999999";
			}
			this.customQty = val;
			this.changeQty();
			this.debounceCalcPrice();
		},
		uploadPic(event, type = "upload") {
			this.$gl.show();
			let files = type === "upload" ? event.target.files : event;
			let fileResult = checkFile(Array.from(files), this.acceptFileType, 80);
			if (!fileResult) {
				this.$toast.error("File type error");
				this.$gl.hide();
				return false;
			}
			if (fileResult.nomalSize.length == 0) {
				this.$gl.hide();
				this.$store.commit("setSizeDialog", true);
				this.$store.commit("setInputRefName", "replayUpload");
				this.$store.commit("setOverSizeList", fileResult.overSize);
				this.$refs[this.refName][0].value = "";
				return false;
			}
			let uploadPromises = [];
			fileResult.nomalSize.forEach((file) => {
				let promise = uploadFile(file).then((res) => {
					if (this.refName === "upload") {
						this.uploadArtworkList.push({
							original_filename: file.name,
							secure_url: res,
							size: (file.size / 1024).toFixed(1),
						});
					} else if (this.refName === "uploadPic") {
						this.attachFileList.push({
							original_filename: file.name,
							secure_url: res,
							size: (file.size / 1024).toFixed(1),
						});
					}
				});
				uploadPromises.push(promise);
			});
			Promise.all(uploadPromises).then(() => {
				//google 记录用户上传动作
				let len = this.refName === "upload" ? this.uploadArtworkList.length : this.attachFileList.length;
				this.$store.commit("setGoogleUploadAction", {
					[`STEP${this.currentStep.customIndex}:${this.currentStep.paramName}`]: len,
					content_id: this.pid,
				});
				this.$gl.hide();
				this.$refs[this.refName][0].value = "";
				if (fileResult.overSize.length > 0) {
					this.$store.commit("setSizeDialog", true);
					this.$store.commit("setInputRefName", "replayUpload");
					this.$store.commit("setOverSizeList", fileResult.overSize);
				}
			});
		},
		deletePic(index) {
			this.attachFileList.splice(index, 1);
		},
		delUploadImg(index) {
			this.uploadArtworkList.splice(index, 1);
		},
		selectSize(item, citem) {
			this.selectQuoteParams(item, citem);
			this.showMaskFn();
		},
	},
	computed: {
		getColorList() {
			return this.$store.state.colorList;
		},
		fontsData() {
			return require("@/assets/json/fontList.json");
		},
		discountPrice() {
			if (this.priceInfo) {
				return `${Math.abs(this.priceInfo.totalPrice - this.subtotal)}`;
			} else {
				return 0;
			}
		},
		text1() {
			let discountName = "";
			//加急费，重量加价
			if (this.priceInfo.discountPrice) {
				return this.lang.rushDelivery;
			}
			if (this.priceInfo.discount > 1) {
				return this.lang.rushDelivery;
			} else if (this.priceInfo.discount < 1) {
				discountName = this.lang.discount;
				return `${discountName} (${this.lang.Turnaround}: ${(Math.abs(1 - this.priceInfo.discount) * 100).toFixed(0)}% ${this.lang.p8}):`;
			}
		},
		text2() {
			let ac;
			if (this.showSmallPrice) {
				ac = "+";
			} else if (this.priceInfo.discount > 1) {
				ac = "+";
			} else if (this.priceInfo.discount < 1) {
				ac = "-";
			} else {
				ac = "";
			}
			return ac;
		},
		subtotal() {
			return round2(round2(this.priceInfo.foundationUnitPrice * this.priceInfo.totalQuantity) + this.priceInfo.toolingCharge + (this.priceInfo.setupCharge || 0));
		},
		onlyAddInquiry() {
			let onlyAddInquiry = false;
			for (const stepData in this.selectedData) {
				let data = this.selectedData[stepData];
				if (data[0]) {
					if (data[0].onlyAddInquiry && data[0].onlyAddInquiry != 0) {
						onlyAddInquiry = true;
					}
				}
			}
			return onlyAddInquiry;
		},
		langSemiCustom() {
			return this.$store.getters.lang?.semiCustom;
		},
		langCart() {
			return this.$store.getters.lang?.cart;
		},
		langQuote() {
			return this.$store.getters.lang.quote || {};
		},
		langLogin() {
			return this.$store.getters.lang.login || {};
		},
	},
	created() {
		this.$store.dispatch("getColorCode");
		//提前算税费
		getTaxByPrice().then((res) => {
			this.textPrice = res.data.rate;
			this.countryName = res.data?.res?.country?.names.en;
			this.continentName = res.data?.res?.continent?.names.en; //洲
		});
	},

	mounted() {
		this.isIframe = !!this.$route.query.type;
	},
};
</script>
<style scoped lang="scss">
.tip-icon2 {
	color: #fff;
}
#custom-cufflinks {
	font-family: Calibri, Arial, serif;
	font-size: 16px;

	@include respond-to(mb) {
		font-size: 12px;
	}

	::v-deep .quoteNavWrap {
		padding: 20px max(calc(50% - 700px), 1.5vw);
		background-color: #f2f5f7;
		.quoteNav .quoteCard .card-box img {
			height: auto;
		}
	}

	.content {
		padding: 20px max(calc(50% - 700px), 1.5vw);
		background-color: #ffffff;
		h1 {
			margin-bottom: 20px;
			font-size: 28px;

			@include respond-to(mb) {
				font-size: 18px;
				margin-bottom: 0;
			}
		}
		.customCircle2 {
			width: 16px;
			height: 16px;
			border-color: #d4d7d9;
			&::after {
				width: 6px;
				height: 6px;
				background: #d4d7d9;
			}
		}
		.customCircle2.isActive,
		.stepItem:hover .customCircle2 {
			background: none;
			border-color: var(--color-primary);
			&::after {
				background: var(--color-primary);
			}
		}
		.center-content {
			display: grid;
			grid-template-columns: 1fr 1.06fr;
			gap: 60px;

			@include respond-to(mb) {
				grid-template-columns: repeat(1, 1fr);
				gap: 15px;
			}

			.leftArea {
				min-width: 0;
				padding-bottom: 20px;

				@include respond-to(mb) {
					padding-bottom: 0;
				}
			}

			.rightArea {
				min-width: 0;

				::v-deep .stepWrap {
					padding: 20px;
					margin-bottom: 0;
					@include respond-to(mb) {
						padding: 10px;
					}
				}

				.stepFooter {
					display: flex;
					justify-content: center;
					align-items: center;
					margin-top: 30px;

					@include respond-to(mb) {
						margin-top: 10px;
					}
				}

				.sizeItemWrap {
					display: grid;
					grid-template-columns: repeat(3, 1fr);
					gap: 10px;

					@include respond-to(mb) {
						gap: 7.5px;
					}

					.stepItem {
						display: flex;
						align-items: center;
						background: #f9f9f9;
						border: 1px solid transparent;
						border-radius: 6px;
						cursor: pointer;
						height: 40px;
						padding: 0 4px 0 12px;
						position: relative;
						transition: all 0.3s;

						.checked {
							display: none;
							justify-content: center;
							align-items: center;
							position: absolute;
							top: 0;
							right: 0;
							width: 20px;
							height: 20px;
							background: $color-primary;
							border-radius: 0 6px 0 6px;

							i {
								color: #ffffff;
							}
						}

						&.active {
							border-color: $color-primary;
							color: $color-primary;

							.checked {
								display: flex;
							}
						}
					}
				}
				::v-deep .select-plating .stepContent {
					@include respond-to(mb) {
						grid-template-columns: repeat(3, 1fr) !important;
					}
				}
				.itemWrap {
					display: grid;
					align-items: stretch;
					grid-template-columns: repeat(3, 1fr);
					gap: 10px;

					@include respond-to(mb) {
						grid-template-columns: repeat(2, 1fr);
						gap: 7.5px;
					}

					.stepItem {
						display: flex;
						flex-direction: column;
						align-items: center;
						background: #f9f9f9;
						border: 1px solid transparent;
						border-radius: 6px;
						cursor: pointer;
						// padding: 10px;
						position: relative;
						transition: all 0.3s;
						text-align: center;

						.step-item-content {
							display: flex;
							align-items: center;
							padding: 0.375em 0.75em 0.625em;
							.price {
								margin-top: 3px;
							}
						}
						.imgWrap {
							margin-bottom: 4px;
						}
						.checked {
							display: none;
							justify-content: center;
							align-items: center;
							position: absolute;
							top: 0;
							right: 0;
							width: 20px;
							height: 20px;
							background: $color-primary;
							border-radius: 0 6px 0 6px;

							i {
								color: #ffffff;
							}
						}

						.zoom {
							position: absolute;
							top: 10px;
							right: 6px;
							color: $color-primary;
							transition: all 0.1s;
							font-size: 20px;
							line-height: 0;
							color: #666;
							@include respond-to(mb) {
								display: none;
								.el-icon-zoom-in {
									font-size: 0.83em;
								}
							}
						}

						&.active {
							border-color: $color-primary;
							color: $color-primary;

							.checked {
								display: flex;
							}
						}
					}
				}

				.timeWrap {
					display: grid;
					grid-template-columns: repeat(2, 1fr) !important;
					gap: 10px;
					align-items: stretch;

					@include respond-to(mb) {
						gap: 7.5px;
					}

					.stepItem {
						display: flex;
						flex-direction: column;
						align-items: flex-start;
						justify-content: flex-start;
						background: #f9f9f9;
						border: 1px solid transparent;
						border-radius: 6px;
						cursor: pointer;
						padding: 10px;
						position: relative;
						transition: all 0.3s;

						.imgWrap {
							margin-bottom: 4px;
						}
						.time-item-content {
							margin-bottom: 8px;
							display: flex;
							align-items: center;
						}
						.checked {
							display: none;
							justify-content: center;
							align-items: center;
							position: absolute;
							top: 0;
							right: 0;
							width: 20px;
							height: 20px;
							background: $color-primary;
							border-radius: 0 6px 0 6px;

							i {
								color: #ffffff;
							}
						}

						&.active {
							border-color: $color-primary;
							color: $color-primary;

							.checked {
								display: flex;
							}
						}

						.discountText {
							// margin-bottom: 8px;
							font-weight: 700;
						}

						.discountDes {
							color: #808080;
						}
					}
				}

				::v-deep .stepWrap .stepContent {
					grid-template-columns: repeat(3, 1fr);
					gap: 8px;
					@include respond-to(mb) {
						grid-template-columns: repeat(2, 1fr);
					}
					.stepItem {
						background: #f4f5f5;
						border-radius: 6px;
						border: 1px solid transparent;
						.customCircle1 {
							width: 16px;
							height: 16px;
							&::after {
								width: 6px;
								height: 6px;
							}
						}
						.textWrap {
							width: 100%;
							margin: 0;
							padding: 0.625em 0.75em;
						}
						.imgWrap {
							transition: none;
						}
					}
					.stepItem.active,
					.stepItem:hover {
						border-color: var(--color-primary);
						color: var(--color-primary);
						.imgWrap {
							border: none;
							img {
								border-radius: 6px;
							}
						}
						.zoom .el-icon-zoom-in {
							color: var(--color-primary) !important;
						}
						.customCircle1 {
							background: none;
							border-color: var(--color-primary);
							&::after {
								background: var(--color-primary);
							}
						}
					}
				}
				::v-deep #Size .stepContent {
					.imgWrap {
						display: none;
					}
					.textWrap .alias {
						justify-content: start;
					}
				}
				::v-deep .more-options {
					grid-template-columns: repeat(2, 1fr) !important;
					.zoom {
						display: none;
						@include respond-to(mb) {
							display: block !important;
						}
					}
				}
				::v-deep .back-side .zoom {
					display: none;
				}
				.upload {
					display: flex;
					justify-content: center;
					align-items: center;
					flex-direction: column;
					min-height: 96px;
					padding: 10px;
					background: #ffffff;
					border-radius: 6px;
					border: 1px dashed #e9ecf0;
					gap: 0;

					.uploadList {
						width: 100%;
						max-height: 100px;
						overflow: auto;
						margin-bottom: 10px;
						text-align: center;

						.uploadIcon {
							width: 68px;
							height: 55px;
							margin-top: 15px;
							font-size: 52px;
							color: #ccc;
						}

						.uploadItem {
							display: flex;
							justify-content: space-between;
							align-items: center;
							padding: 5px;
							font-size: 14px;

							@include respond-to(mb) {
								font-size: 12px;
							}
						}

						.myIcon {
							margin: 0 4px;
						}
					}

					input[type="file"] {
						position: absolute;
						clip: rect(0, 0, 0, 0);
					}

					.t1 {
						display: flex;
						align-items: center;

						.upBtn {
							display: flex;
							align-items: center;
							cursor: pointer;

							b.uploadIcon {
								font-size: 26px;
								color: $color-primary;
							}

							.up {
								margin: 0 10px 0 8px;
								text-decoration: underline;
								color: $color-primary;
								font-weight: 700;
							}
						}

						.wenhaoIcon {
							color: $color-primary;
							margin-left: 4px;
						}
					}

					.t2 {
						color: #999999;
					}
				}

				.inputWrap {
					display: flex;
					align-items: center;

					.el-input {
						width: 250px;
						margin-right: 20px;
					}

					::v-deep .el-input__inner {
						background-color: #f5f5f5;
						font-size: 14px;
					}

					::v-deep .el-input__inner:focus {
						border-color: $color-primary;
					}
				}

				.turnaroundTime {
					::v-deep .step-item-title .title2 {
						color: #999999;
					}

					.remarkInput {
						margin: 20px 0 10px;

						::v-deep .el-textarea__inner {
							background-color: #ffffff;
							font-size: 14px;
							resize: none;
							@include respond-to(mb) {
								font-size: 12px;
							}
						}

						::v-deep .el-textarea__inner:focus {
							border-color: $color-primary;
						}
					}

					.attachFile {
						position: relative;
						color: #999999;

						input[type="file"] {
							position: absolute;
							clip: rect(0, 0, 0, 0);
						}

						a {
							color: $color-primary;
						}

						.fileWrap {
							margin-top: 10px;

							.file-item {
								display: flex;
								justify-content: space-between;

								.control {
									margin-left: 40px;

									b {
										margin: 0 4px;
										cursor: pointer;
									}
								}
							}
						}
					}
				}
				#foot {
					margin-left: 20px;
					@include respond-to(mb) {
						margin-left: 0;
					}
				}
			}
		}
	}
}

.subtotal {
	.sub-detail {
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		align-items: center;
		border-radius: 10px;
		padding: 24px 19px;
		background: #f6f6f6;
		grid-gap: 20px;

		.subtotal-left {
			.summary {
				font-weight: 700;
				font-size: 22px;
				@include respond-to(mb) {
					font-size: 12px;
				}
			}

			.sub-item {
				display: flex;
				align-items: center;
				margin-bottom: 10px;

				&:last-child {
					margin-bottom: 0;
				}

				.sub-item-left {
					flex-shrink: 0;
					white-space: normal;
					width: 130px;
					margin-right: 38px;
				}
			}
		}

		.subtotal-right {
			text-align: center;

			.totalPriceBox {
				.subTotalText {
					font-weight: 400;
					color: #333333;
					line-height: 36px;
				}
			}
			.inquiryBtn {
				width: 100%;
				height: 3em;
				border: none;
				color: $color-primary;
				outline: none;
				min-width: 11em;
				line-height: 3em;
				font-weight: bold;
				font-size: 1.125em;
				border-radius: 6px 0 0 6px;
				background: #fff;
				text-transform: uppercase;
				border: 1px solid $color-primary;
			}

			button {
				width: 100%;
				height: 3em;
				border: none;
				color: #fff;
				outline: none;
				min-width: 11em;
				line-height: 3em;
				font-weight: bold;
				font-size: 1.125em;
				border-radius: 0 6px 6px 0;
				background: $color-primary;
				text-transform: uppercase;

				&:hover {
					opacity: 0.9;
				}
			}

			.final-price {
				margin: 0 10px;
				font-size: 1.5em;
				font-weight: bold;
				color: #e6252e;
			}

			.price {
				text-decoration: line-through;
			}

			.inquiryBtnBox {
				font-size: 14px;
				margin-top: 10px;

				.inquiryBtn {
					cursor: pointer;
					text-decoration: underline;
					color: $color-primary;
				}
			}
		}
	}

	@include respond-to(mb) {
		background: #ebebeb;

		.sub-detail {
			border-radius: 4px;
			grid-template-columns: repeat(1, 1fr);
			grid-gap: 10px;
			padding: 12px;

			.subtotal-left {
				.sub-item {
					margin-bottom: 10px;
					justify-content: space-between;

					.sub-item-left {
						margin-right: 10px;
					}
				}
			}

			.subtotal-right {
				.totalPriceBox {
					.subTotalText {
						font-weight: bold;
						font-size: 14px;
						color: #222222;
						line-height: 30px;
					}
				}

				button {
					height: 3em;
					border-radius: 0 4px 4px 0;
					line-height: 3em;
					width: 75%;
					font-size: 14px;
				}
				.inquiryBtn {
					height: 3em;
					border-radius: 4px 0 0 4px;
					line-height: 3em;
					width: 75%;
					font-size: 14px;
				}

				.final-price {
					font-size: 18px;
				}

				.inquiryBtnBox {
					font-size: 12px;
				}
			}
		}
	}
}

.page-des {
	font-size: 14px;
	color: #999999;
	line-height: 24px;
	margin-top: 18px;
	text-align: right;

	.lookingFor {
		margin-top: 2px;

		a {
			text-decoration: underline;
		}
	}

	a {
		color: $color-primary;
	}

	@media screen and (max-width: $mb-width) {
		display: none;
		font-size: 12px;
		margin-top: 10px;
	}
}

.d-flex {
	display: flex;
}

.textWrap {
	.leftText,
	.rightText {
		display: flex;
		align-items: flex-start;

		@include respond-to(mb) {
			flex-direction: column;
		}

		.textDes {
			font-weight: 700;
			width: 50px;
			flex-shrink: 0;
			padding-top: 8px;
			@include respond-to(mb) {
				width: auto;
				margin-bottom: 10px;
			}
		}

		.textItemWrap {
			flex: 1;
			margin-bottom: 10px;
		}

		.textItem {
			display: grid;
			grid-template-columns: 1.2fr 1fr 30px 30px 30px 30px 80px;
			align-items: center;
			gap: 8px;
			margin-bottom: 10px;

			@include respond-to(mb) {
				grid-template-columns: 1.2fr 1fr 30px 30px 30px 30px 80px;
				.el-input {
					grid-column: 1/8;
					font-size: 12px;
				}
				.el-select {
					grid-column: 1/3;
					font-size: 12px;
				}
			}

			::v-deep .el-input__inner {
				font-size: 14px;
				@include respond-to(mb) {
					font-size: 12px;
				}
			}

			::v-deep .el-input__inner:focus {
				border-color: $color-primary;
			}

			&:last-child {
				margin-bottom: 0;
			}

			.font-bold {
				font-weight: 700;
			}

			.font-style {
				font-style: italic;
			}

			.font-color {
				--bg-color: #cccccc;
				position: relative;
				margin-bottom: 4px;

				&::after {
					position: absolute;
					content: "";
					height: 3px;
					background-color: var(--bg-color);
					width: 15px;
					bottom: -2px;
					left: 50%;
					transform: translateX(-50%);
					box-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
				}
			}

			.font-bold,
			.font-style,
			.font-color {
				text-align: center;
				cursor: pointer;

				@include respond-to(mb) {
					font-size: 16px;
				}

				&.active {
					color: $color-primary;
				}
			}

			.line {
				width: 1px;
				height: 24px;
				margin: 0 auto;
				background: #d3d5d7;
			}

			.addNewLine {
				cursor: pointer;

				b {
					margin-right: 4px;
					color: #d24600;
				}
			}
		}
	}
}

$border-color: #e2e2e2;
.color-picker-wrap {
	overflow: hidden auto;
	max-height: 300px;
}

.color-picker-title {
	position: relative;
	padding: 10px;
	text-align: center;

	.done {
		position: absolute;
		right: 10px;
		top: 50%;
		transform: translateY(-50%);
		color: $color-primary;
		cursor: pointer;
	}
}

.color-picker-list {
	display: grid;
	grid-template-columns: repeat(6, 1fr);
	grid-gap: 20px;
	padding: 10px;

	.color-item {
		position: relative;
		display: flex;
		justify-content: center;
		align-items: center;
		aspect-ratio: 1;
		border-radius: 50%;
		cursor: pointer;
		border: 1px solid $border-color;

		i.el-icon-check {
			display: none;
		}

		&::before {
			content: "";
			position: absolute;
			left: -4px;
			top: -4px;
			right: -4px;
			bottom: -4px;
			border-radius: 50%;
			border: 1px solid $border-color;
		}

		@media (any-hover: hover) {
			&:hover {
				&::before {
					border-color: $color-primary;
				}

				.v-icon {
					display: inline-block;
				}
			}
		}
	}

	.color-item.active {
		&::before {
			display: block;
			border-color: $color-primary;
		}

		i.el-icon-check {
			display: inline-block;
		}
	}
}
</style>
