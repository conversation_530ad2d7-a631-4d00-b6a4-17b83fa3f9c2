<template>
	<article class="priceTable">
		<el-tabs v-model="faqName" class="demo-tabs">
			<el-tab-pane :label="lang.PriceChart" name="first">
				<div class="table-responsive1">
					<table class="table table-bordered text-center">
						<thead class="thead-light">
						<tr class="gridBox12">
							<th>{{ lang.lanyard.SizeQty }}</th>
							<th v-for="(item, index) in quantityData" :key="index">
								{{ item.quantity }}
							</th>
						</tr>
						</thead>
						<tbody>
						<tr class="gridBox12" v-for="(item, index) in tableData" :key="index">
							<th scope="row">{{ item.size }}</th>
							<td v-for="(item2, index2) in item.priceJson" :key="index2">
								<CCYRate :price="(item2.unitPrice * morePriceData.multiBasicUnitPrice * 0.7)"></CCYRate>
							</td>
						</tr>
						</tbody>
					</table>
				</div>
			</el-tab-pane>
			<el-tab-pane :label="lang.FAQS" name="second">
				<div class="faqBorder">
					<div class="QAPart">
						<span class="Q"> {{ lang.lanyard.Q1 }} </span>
						<p class="A">
							{{ lang.lanyard.A1 }}
						</p>
					</div>
					<div class="QAPart">
            <span class="Q">
              {{ lang.lanyard.Q2 }}
            </span>
						<p class="A">
							{{ lang.lanyard.A2 }}
						</p>
					</div>
					<div class="QAPart">
						<span class="Q"> {{ lang.lanyard.Q3 }} </span>
						<p class="A">
							{{ lang.lanyard.A3 }}
						</p>
					</div>
					<div class="QAPart">
            <span class="Q">
              {{ lang.lanyard.Q4 }}
            </span>
						<p class="A">
							{{ lang.lanyard.A4 }}
						</p>
					</div>
					<div class="QAPart">
						<span class="Q"> {{ lang.lanyard.Q5 }} </span>
						<p class="A">
							{{ lang.lanyard.A5 }}
						</p>
					</div>
					<div class="QAPart">
						<span class="Q"> {{ lang.lanyard.Q6 }} </span>
						<p class="A">
							{{ lang.lanyard.A6 }}
						</p>
					</div>
				</div>
			</el-tab-pane>
		</el-tabs>
	</article>
</template>
<script>
export default {
	props: {
		quantityData: {
			type: Array,
			default: [],
		},
		tableData: {type: Array, default: []},
	},
	data() {
		return {
			faqName: "first",
		}
	},
	computed: {
		lang() {
			return this.$store.getters.lang.quote || {}
		},
		morePriceData() {
			return this.$store.getters.morePriceData
		}
	}
}
</script>

<style scoped lang="scss">
.priceTable ::v-deep {
	display: grid;
	grid-template-columns: repeat(48, 1fr);

	.el-tabs__header {
		margin: 0;
	}

	.el-tabs__content {
		overflow: inherit;
	}

	.el-tabs__item {
		border-radius: 4px 4px 0 0;
		padding: 0;
		color: #333333;
		width: 200px;
		height: 50px;
		background-color: #d9dbdd;
		font-size: 24px;
		font-weight: bold;
		margin-right: 5px;
		cursor: pointer;
		text-align: center;
		line-height: 50px;

		@media screen and (max-width: 768px) {
			font-size: 14px;
			font-weight: 400;
			color: #ffffff;
			width: 146px;
			height: 35px;
			background: #d9dbdd;
			line-height: 40px;
			border-radius: 5px 5px 0px 0px;
		}
	}

	.el-tabs__item.is-active {
		background-color: $color-primary;
		color: white;
	}

	.el-tabs__active-bar {
		display: none;
	}

	.el-tabs {
		grid-column: 1/49;
	}

	.table-responsive1 {
		box-shadow: 0px 3px 10px 0px rgb(0 0 0 / 20%);
		overflow: auto;
		font-size: 16px;
		border: 10px solid #fff;

		.gridBox12 {
			th:nth-child(1) {
				@media screen and (max-width: 768px) {
					width: auto;
					display: block;
					position: sticky;
					left: 0;
					max-width: 70px;
					border-right: 0;
				}
			}
		}

		thead,
		tbody,
		tfoot,
		tr,
		td,
		th {
			border-color: inherit;
			border-style: solid;
			border-width: 0;
			white-space: nowrap;
		}

		th {
			background-color: #f6f6f6;
			font-size: 18px;
			font-weight: 400;

			@media screen and (max-width: 768px) {
				font-size: 12px;
			}
		}

		td {
			background-color: #fff;

			@media screen and (max-width: 768px) {
				font-size: 12px;
			}
		}

		th:hover,
		td:hover {
			background-color: #ebebeb;
			color: $color-primary;
		}

		table {
			caption-side: bottom;
			border-collapse: collapse;
		}

		.table {
			--bs-table-bg: transparent;
			--bs-table-accent-bg: transparent;
			--bs-table-striped-color: #212529;
			--bs-table-striped-bg: rgba(0, 0, 0, 0.05);
			--bs-table-active-color: #212529;
			--bs-table-active-bg: rgba(0, 0, 0, 0.1);
			--bs-table-hover-color: #212529;
			--bs-table-hover-bg: rgba(0, 0, 0, 0.075);
			width: 100%;
			color: #212529;
			vertical-align: top;
			border-color: #dee2e6;
		}

		.table > :not(caption) > * > * {
			padding: 0.5rem 0.5rem;
			border-bottom-width: 1px;
			box-shadow: inset 0 0 0 9999px transparent;
			text-align: center;
		}

		.table > tbody {
			vertical-align: inherit;
		}

		.table > thead {
			vertical-align: bottom;
		}

		.table-bordered > :not(caption) > * {
			border-width: 1px 0;
		}

		.table-bordered > :not(caption) > * > * {
			border-width: 0 1px;
			vertical-align: middle;
		}

		.table-hover > tbody > tr:hover > * {
			--bs-table-accent-bg: var(--bs-table-hover-bg);
			color: var(--bs-table-hover-color);
		}
	}

	.faqBorder {
		box-shadow: 0px 3px 10px 0px rgb(0 0 0 / 20%);
		padding: 10px;
		background: #ffffff;

		@media screen and (max-width: 768px) {
			max-height: 320px;
			overflow-y: auto;
		}

		.QAPart {
			.Q {
				font-size: 15px;
				font-weight: bold;
				color: #333333;
			}

			.A {
				font-size: 13px;
				font-weight: 400;
				color: #333333;
				margin-bottom: 0;
				margin-top: 13px;
			}
		}

		.QAPart:not(:last-child) {
			margin-bottom: 30px;
		}
	}
}
</style>
