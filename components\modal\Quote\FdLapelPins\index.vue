<template>
	<div class="LapelPins modal-box" @click.self="setModalType({}, modal.list, 'quote_table')">
		<template v-if="isManage">
			<div class="lapelPinsBox" :style="[modal.style]">
				<v-card style="background-color: #fff">
					<v-row justify="center" align="center" style="width: 100%; height: 100%; display: flex; margin: 0">
						<div class="manageHeader" style="padding: 10px">
							<div class="introduceItem" v-for="(l, li) in modal.list" :key="li" :style="{ ...modal.cardBoxStyle, ...l.style }">
								<template>
									<litterTitle>
										<EditDiv class="titleBox" :tagName="l.title?.tagName || 'div'" v-model:content="l.title.value" @click="setModalType(l.title, modal.list, 'text')" :style="{ ...modal.titleStyle }" v-if="l.title?.value" />
									</litterTitle>
									<EditDiv class="proValueBox" :tagName="l.proValue?.tagName || 'p'" v-model:content="l.proValue.value" @click="setModalType(l.proValue, modal.list, 'text')" :style="{ ...modal.proValueStyle }" v-if="l.proValue?.value" />
									<EditDiv v-if="l.buttonText?.value" class="buttonText" :tagName="l.buttonText?.tagName || 'p'" v-model:content="l.buttonText.value" @click="setModalType(l.buttonText, modal.list, 'text')" :style="{ ...modal.proValueStyle }" />
									<EditDiv v-if="l.titleValue?.value" class="buttonText" :tagName="l.titleValue?.tagName || 'p'" v-model:content="l.titleValue.value" @click="setModalType(l.titleValue, modal.list, 'text')" :style="{ ...modal.proValueStyle }" />
								</template>
							</div>
						</div>
					</v-row>
				</v-card>
			</div>
		</template>
		<template v-else>
			<div class="lapelContent">
				<LapelPinsNav :cateList="cateList" :cateChildId="modal.quoteCateId" :title="modal.list[0].titleValue?.value || lang.fdPins.quoteNav"> </LapelPinsNav>
				<div class="lapelCrumb">
					<span class="gray"
						><span style="cursor: pointer" @click="goIndex">{{ layoutLang.home }}</span> / {{ fatherCateName }} / </span
					>&nbsp;<span>{{ cateName }}</span>
				</div>
				<div class="contentMain">
					<div class="leftBar" :style="{ top: headerHeight }">
						<swiperDetail :imgList="imgList" :cateName="cateName" :proSku="proSku" @nowSiperImg="setNowImg"> </swiperDetail>
					</div>
					<div class="mbConcatBtn" :style="{ top: headerHeight }" v-show="false">
						<div class="contactUs btnItem" :title="lang.bannerQuote.cs" @click="openView('contact')">
							<b class="icon-youjian"></b>
							<span>{{ lang.bannerQuote.cs }}</span>
						</div>
						<div class="quickSale btnItem" @click="openDetail" v-if="modal.list[4].buttonText?.value">
							<b class="icon-baojiaxiaoji"></b>
							<EditDiv :tagName="modal.list[4].buttonText.tagName || 'div'" v-model:content="modal.list[4].buttonText.value" :title="modal.list[4].buttonText.value" @click="setModalType(modal.list[4].buttonText, modal.list, 'text')" v-if="modal.list[4].buttonText?.value" />
						</div>
					</div>
					<div class="rightBar">
						<div class="proNameBox">
							<H1 class="cateName">{{ cateName }}</H1>
							<div class="sku">{{ lang.fdPins.Item }} {{ proSku }}</div>
						</div>
						<div class="introduceBox">
							<div class="introduceItem" v-if="l.type && l.title && l.title.value.length > 0 && l.proValue && l.proValue.value.length > 0" v-for="(l, li) in modal.list" :key="li" :style="{ ...modal.cardBoxStyle, ...l.style }">
								<template v-if="l.type && l.title && l.title.value.length > 0 && l.proValue && l.proValue.value.length > 0">
									<litterTitle v-if="showProvalue2(l)">
										<EditDiv class="titleBox" :tagName="l.title.tagName || 'div'" v-model:content="l.title.value" @click="setModalType(l.title, modal.list, 'text')" v-if="showProvalue2(l)" :style="{ ...modal.titleStyle, ...l.style }" />
									</litterTitle>
									<EditDiv class="proValueBox" :tagName="l.proValue.tagName || 'p'" v-model:content="l.proValue.value" @click="setModalType(l.proValue, modal.list, 'text')" :style="{ ...modal.proValueStyle, ...l.style }" v-if="showProvalue(l)" />
								</template>
								<div class="mbClickBtn" v-show="isMobile && l.type == 'Description' && l.title && l.title.value.length > 0 && l.proValue && l.proValue.value.length > 0">
									<div class="mbTools">
										<H2 class="toolTitleText">{{ lang.fdPins.disTools }}</H2>
										<div class="toolBox">
											<div class="toolItem">
												<div class="toolText">
													<span>{{ lang.fdPins.catalogs }}</span>
													<div class="clickEvent" @click="openView('catalogs')">
														<span>{{ lang.neon.tips2 }}</span>
														<div class="arrow"></div>
													</div>
												</div>
												<div class="toolImg">
													<img src="https://static-oss.gs-souvenir.com/web/quoteManage/20240930/catalog_20240930cE3Kie.png" alt="" />
												</div>
											</div>
											<div class="toolItem">
												<div class="toolText">
													<span>{{ lang.fdPins.orderSample }}</span>
													<div class="clickEvent" @click="openView('order')">
														<span>{{ lang.neon.tips2 }}</span>
														<div class="arrow"></div>
													</div>
												</div>
												<div class="toolImg">
													<img src="https://static-oss.gs-souvenir.com/web/quoteManage/20240930/order_sample_202409304GtsbW.png" alt="" />
												</div>
											</div>
											<div class="toolItem" v-show="showQuoteBtn">
												<div class="toolText">
													<span>{{ lang.fdPins.customSheet }}</span>
													<div class="clickEvent" @click="openView('quoteDetail')">
														<span>{{ lang.neon.tips2 }}</span>
														<div class="arrow"></div>
													</div>
												</div>
												<div class="toolImg">
													<img src="https://static-oss.gs-souvenir.com/web/quoteManage/20250310/MB-custom_sales_sheet_20250310mcKEGC.png" alt="" />
												</div>
											</div>
											<div class="toolItem">
												<span class="introText">{{ lang.fdPins.needView }}</span>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="priceHead" v-if="tableData.length > 0 && isMobile">
							<h3 class="headerIntro">
								{{ lang.fdPins.pricingList }}
								<el-tooltip v-show="isMobile" effect="light" :content="lang.fdPins.wenhaoTip" placement="top">
									<b @click.stop class="iconfont icon-wenhao1" style="font-size: 14px; color: #333; margin-left: 2px"></b>
								</el-tooltip>
							</h3>
							<div class="headerBtn" v-if="false">
								<div class="contactUs btnItem" :title="lang.bannerQuote.cs" :class="{ hasCorner: modal.list[4].buttonText?.value }" @click="openView('contact')">
									<b class="icon-youjian"></b>
									<span>{{ lang.bannerQuote.cs }}</span>
								</div>
								<div class="quickSale btnItem" ref="quickSale" @click="openDetail" v-if="modal.list[4].buttonText?.value">
									<b class="icon-baojiaxiaoji"></b>
									<EditDiv :tagName="modal.list[4].buttonText.tagName || 'div'" v-model:content="modal.list[4].buttonText.value" :title="modal.list[4].buttonText.value" @click="setModalType(modal.list[4].buttonText, modal.list, 'text')" v-if="modal.list[4].buttonText?.value" />
								</div>
							</div>
						</div>
						<priceTable v-if="isMobile" :tableData="tableData" :arrData="arrData" @setArrData="setArrData" @closeArrData="closeArrData" :discountCode="discountCode" :cateName="cateName" @switchLoading="switchLoading"></priceTable>
					</div>
					<div class="proList" v-show="isMobile && showProList">
						<lanyardProDetail v-if="isLanyard" :detailInfoList="detailInfoList" :quotePid="modal.quotePid" :quoteCateId="modal.quoteCateId"></lanyardProDetail>
						<lapelPinsProDetail v-else :fatherCateName="fatherCateName" :detailInfoList="detailInfoList" :quotePid="modal.quotePid" :quoteCateId="modal.quoteCateId" :isNeon="isNeon"></lapelPinsProDetail>
						<!-- <div v-else class="mbProDetail">
							<H2 class="mbProDetailTitle">{{ lang.fdPins.proOptionAndImg }}</H2>
							<div class="showProDetail" :class="{ first: index == 0 }" v-for="(item, index) in mbProDetail" :key="index">
								<div class="showDetailHead">
									<div class="headItem" v-for="item2 in item" :key="item2.id" :class="{ active: item2.active }" @click="changeActive(item2, index)">
										<h3>{{ item2.alias }}</h3>
									</div>
								</div>
								<div class="showDetailBox">
									<div class="showDetail custom-scrollbar" :class="{ showTwo: item2.showTwo }" v-show="item2.active" v-for="item2 in item" :key="item2.id">
										<div class="detailItem" v-for="item3 in item2.childList" :key="item3.id">
											<div class="imgBox">
												<img :src="item3.imageJson && JSON.parse(item3.imageJson)[0].url" alt="" />
											</div>
											<div class="proInfo">
												<div class="proName">{{ item3.alias }}</div>
												<template v-if="Array.isArray(item3.priceInfo?.priceShow)">
													<div
														class="priceList"
														:class="{
															free: item3.priceInfo.priceShow[0] == 'Free' || item3.priceInfo.priceShow[0] == 'free',
														}"
														v-if="item3.priceInfo?.priceShow && item3.priceInfo.priceShow.length > 0"
													>
														<div class="proPrice" v-for="(text, index2) in item3.priceInfo.priceShow" :key="index2">
															{{ text }}
														</div>
													</div>
													<div class="free" v-else>
														<span>{{ lang.free }}</span>
													</div>
												</template>
												<template v-else-if="isObject(item3.priceInfo?.priceShow)">
													<div class="viewMore">
														<el-popover :close-delay="0" :visible-arrow="false" :transition="null" popper-class="selfPinsProDetailPopper" placement="bottom" trigger="click">
															<div class="tableBox">
																<table>
																	<thead>
																		<tr>
																			<th v-for="(th, index) in item3.priceInfo.priceShow.table[0]" :key="index">
																				{{ th }}
																			</th>
																		</tr>
																	</thead>
																	<tbody>
																		<tr v-for="(tdItem, index2) in item3.priceInfo.priceShow.table" v-if="index2 != 0" :key="index2">
																			<td v-for="(td, index3) in tdItem" :key="index3">
																				{{ td }}
																			</td>
																		</tr>
																	</tbody>
																</table>
															</div>
															<div class="subscript">
																<span></span><span>{{ item3.priceInfo.priceShow.discountCode }}</span>
															</div>
															<span class="viewMoreText" slot="reference">{{ lang.ViewMore }}</span>
														</el-popover>
													</div>
												</template>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div> -->
					</div>
				</div>
				<div class="distributorTools">
					<span>{{ lang.fdPins.disTools }}</span>
					<div class="toolBox">
						<div class="toolItem" v-for="item in toolBox" :key="item.id">
							<div class="toolText">
								<span>{{ item.toolName }}</span>
								<div class="clickEvent" @click="openView(item.type, item)">
									<span>{{ lang.neon.tips2 }}</span>
									<div class="arrow"></div>
								</div>
							</div>
							<div class="toolImg">
								<img :src="item.icon" alt="" />
							</div>
						</div>
					</div>
				</div>
				<div class="priceHead" v-if="tableData.length > 0 && !isMobile">
					<h3 class="headerIntro">
						{{ lang.fdPins.pricingList }}
						<el-tooltip v-show="isMobile" effect="light" :content="lang.fdPins.wenhaoTip" placement="top">
							<b @click.stop class="iconfont icon-wenhao1" style="font-size: 14px; color: #333; margin-left: 2px"></b>
						</el-tooltip>
					</h3>
					<div class="headerBtn" v-if="false">
						<div class="contactUs btnItem" :title="lang.bannerQuote.cs" :class="{ hasCorner: modal.list[4].buttonText?.value }" @click="openView('contact')">
							<b class="icon-youjian"></b>
							<span>{{ lang.bannerQuote.cs }}</span>
						</div>
						<div class="quickSale btnItem" ref="quickSale" @click="openDetail" v-if="modal.list[4].buttonText?.value">
							<b class="icon-baojiaxiaoji"></b>
							<EditDiv :tagName="modal.list[4].buttonText.tagName || 'div'" v-model:content="modal.list[4].buttonText.value" :title="modal.list[4].buttonText.value" @click="setModalType(modal.list[4].buttonText, modal.list, 'text')" v-if="modal.list[4].buttonText?.value" />
						</div>
					</div>
				</div>
				<priceTable v-if="!isMobile" :tableData="tableData" :arrData="arrData" @setArrData="setArrData" @closeArrData="closeArrData" :discountCode="discountCode" :cateName="cateName" @switchLoading="switchLoading"></priceTable>
				<div class="proList" v-if="!isMobile && showProList">
					<lanyardProDetail v-if="isLanyard" :detailInfoList="detailInfoList" :quotePid="modal.quotePid" :quoteCateId="modal.quoteCateId"></lanyardProDetail>
					<!-- <div class="proDetailList" v-else>
						<div class="proItem" v-for="item in detailInfoList" :key="item.id">
							<proDetail :itemData="item"></proDetail>
						</div>
					</div> -->
					<lapelPinsProDetail v-else :fatherCateName="fatherCateName" :detailInfoList="detailInfoList" :quotePid="modal.quotePid" :quoteCateId="modal.quoteCateId" :isNeon="isNeon"></lapelPinsProDetail>
				</div>

				<TopDialog class="topDialog1" v-model="showInquiry" :model="false" :width="!isMobile ? '410px' : '90%'" margin="20px 0">
					<pinsInquiryBox ref="pinsInquiryBox" :pinsForm="pinsForm" :titlePlaceholder="titlePlaceholder" :commentPlaceholder="inquiryRemark" @cancelForm="cancelForm" @submitForm="submitForm">{{ inquiryTitle }} </pinsInquiryBox>
				</TopDialog>
				<TopDialog class="topDialog2" v-model="showImport" :model="false" :width="!isMobile ? '48vw' : '90%'" :minWidth="!isMobile ? '852px' : 'none'" margin="20px 0">
					<imprintBox :cateName="cateName" :proSku="proSku" :nowSiperImg="nowSiperImg" :tableData="tableData" :listData="modal.list" @switchLoading="switchLoading"></imprintBox>
				</TopDialog>

				<TopDialog v-model="loading" :mask="false" :model="false">
					<LoadingComponent ref="imgLoading"></LoadingComponent>
				</TopDialog>

				<TopDialog v-model="agreeDialog" maskColor="rgba(0,0,0,0.4)" :width="!isMobile ? '100vw' : '90%'" :mask="false" :model="false">
					<div class="remind" :style="{ right: remindRight }">
						<b class="closeIcon icon-guanbi" @click.stop="handleClose('close')"></b>
						<div class="remindHeader">{{ lang.fdPins.pleaseNotice }}:</div>
						<div class="remindContent">
							{{ lang.fdPins.fdPinsRemind }}
						</div>
						<div class="remindBtn" @click="openDetail">{{ lang.fdPins.agree }}</div>
					</div>
				</TopDialog>
			</div>
		</template>
	</div>
</template>

<script>
import swiperDetail from "@/components/lapelPins/swiperDetail.vue";
import LapelPinsNav from "@/components/lapelPins/LapelPinsNav.vue";
import litterTitle from "@/components/lapelPins/litterTitle.vue";
import priceTable from "@/components/lapelPins/priceTable.vue";
import proDetail from "@/components/lapelPins/proDetail.vue";
import pinsInquiryBox from "@/components/lapelPins/pinsInquiryBox.vue";
import lanyardProDetail from "@/components/lapelPins/lanyardProDetail.vue";
import lapelPinsProDetail from "@/components/lapelPins/lapelPinsProDetail.vue";
import imprintBox from "@/components/lapelPins/imprintBox.vue";
import TopDialog from "@/components/Quote/TopDialog.vue";
import LoadingComponent from "@/components/LoadingDialog.vue";

import { getChildCateList, getPriceList, fdEditInquiry } from "@/api/quote/fdCommon.js";
import { getCateParamRelationByCateId } from "@/api/pins.js";
import JSZip from "jszip";
export default {
	name: "LapelPins",
	components: {
		swiperDetail,
		LapelPinsNav,
		litterTitle,
		priceTable,
		proDetail,
		TopDialog,
		LoadingComponent,
		pinsInquiryBox,
		imprintBox,
		lanyardProDetail,
		lapelPinsProDetail,
	},
	props: {
		data: {
			type: Object,
			default: () => ({}),
		},
	},
	data() {
		return {
			isManage: false,
			modal: {
				style: {},
				type: {},
				...this.data,
			},
			fatherCateName: "",
			cateName: "",
			proSku: "",
			detailUrl: "/quote/pins-quote",
			imgList: [],
			cateList: [],
			toolBox: [
				{
					id: 1,
					icon: "https://static-oss.gs-souvenir.com/web/quoteManage/20240914/Print__Edit_Sheet_20240914pEE5Yc.png",
					toolName: "Print & Edit Sheet",
					type: "print",
				},
				{
					id: 2,
					icon: "https://static-oss.gs-souvenir.com/web/quoteManage/20250310/custom_sheet_20250310SWSYGE.png",
					toolName: "Custom Sheet",
					type: "quoteDetail",
				},
				{
					id: 3,
					icon: "https://static-oss.gs-souvenir.com/web/quoteManage/20240914/Download_Hi-Res_Image_20240914y5AZ8H.png",
					toolName: "Download Hi-Res Image",
					type: "download",
				},
				{
					id: 4,
					icon: "https://static-oss.gs-souvenir.com/web/quoteManage/20240914/Catalogs_20240914yBDAKb.png",
					toolName: "Catalogs",
					type: "catalogs",
				},
				{
					id: 5,
					icon: "https://static-oss.gs-souvenir.com/web/quoteManage/20240914/Order_Sample_20240914pXYGR3.png",
					toolName: "Order Sample",
					type: "order",
				},
			],
			tableData: [],
			arrData: [],
			discountCode: "",
			inquiryTitle: "",
			showInquiry: false,
			pinsForm: {
				email: "",
				firstName: "",
				lastName: "",
				telephone: "",
				areaCode: "",
				subject: "",
				groupNumber: [
					{
						id: 1,
						name: "ASI",
						value: "",
					},
					{
						id: 2,
						name: "SAGE",
						value: "",
					},
					{
						id: 3,
						name: "PPAI",
						value: "",
					},
					{
						id: 4,
						name: "PPPC",
						value: "",
					},
					{
						id: 5,
						name: "PSI",
						value: "",
					},
					{
						id: 6,
						name: "APPA",
						value: "",
					},
					{
						id: 7,
						name: "DC",
						value: "",
					},
				],
				remark: "",
				isSample: 0,
			},
			showImport: false,
			loading: false,
			agreeDialog: false,
			detailInfoList: [],
			cloneArr: [],
			nowSiperImg: "",
			headerHeight: 0,
			inquiryRemark: "",
			titlePlaceholder: "",
			remindRight: "",
			needRemind: false,
			isNewYearDiscount: false,
			hideLanyardId: [701, 702, 703, 704, 705],
			hideQuoteId: [897, 902, 282, 270],
			// hideProListId: [850, 851, 852],
			hideProListId: [],
		};
	},
	watch: {
		modal: {
			handler(val) {
				if (process.env.isManage) this.$emit("update:data", val);
			},
			immediate: true,
			deep: true,
		},
		showInquiry: function (val) {
			if (val) {
				if (this.userInfo && this.userInfo.telephone) {
					let telephone, areaCode;
					if (this.userInfo.telephone) {
						if (this.userInfo.telephone.includes("-")) {
							telephone = this.userInfo.telephone.split("-")[1];
							areaCode = this.userInfo.telephone.split("-")[0];
						} else {
							telephone = this.userInfo.telephone;
							areaCode = this.areaCodes;
						}
					}
					this.pinsForm.email = this.userInfo.email;
					this.pinsForm.firstName = this.userInfo.firstName;
					this.pinsForm.lastName = this.userInfo.lastName;
					this.pinsForm.areaCode = this.userInfo.telephone ? areaCode : "";
					this.pinsForm.telephone = this.userInfo.telephone ? telephone : "";
				} else {
					this.pinsForm.areaCode = this.areaCodes;
				}
			}
		},
		"$store.state.currency": {
			handler(newVal) {
				this.getProjectDetail(newVal.code);
			},
		},
	},
	computed: {
		lang() {
			return this.$store.getters.lang.quote || {};
		},
		layoutLang() {
			return this.$store.getters.lang.layout || {};
		},
		userInfo() {
			return this.$store.state.userInfo;
		},
		//获取区号
		areaCodes() {
			return this.$store.state.areaCode;
		},
		isMobile() {
			return this.$store.getters.isMobile;
		},
		mbProDetail() {
			return this.cloneArr;
		},
		isLanyard() {
			return this.hideLanyardId.includes(this.modal.quotePid);
		},
		isNeon() {
			return this.modal.quotePid == 67;
		},
		showProList() {
			return !this.hideProListId.includes(this.modal.quoteCateId);
		},
		showQuoteBtn() {
			return !(this.hideQuoteId.includes(this.modal.quoteCateId) || this.hideQuoteId.includes(this.modal.quotePid));
		},
	},
	methods: {
		setModalType(customData, customDataParent, clickType, event, index) {
			this.$setModal(this, customData, customDataParent, clickType, event, index);
		},
		showProvalue(item) {
			if (!this.isNewYearDiscount) {
				return item.proValue?.value;
			} else {
				return item.proValue?.value && item.type != "ServiceOffered";
			}
		},
		showProvalue2(item) {
			if (!this.isNewYearDiscount) {
				return item.title?.value;
			} else {
				return item.title?.value && item.type != "ServiceOffered";
			}
		},
		async openView(type, data) {
			switch (type) {
				case "print":
					this.showImport = true;
					break;
				case "quoteDetail":
					this.openDetail(data);
					break;
				case "download":
					this.switchLoading("open");
					const zip = new JSZip();
					const promises = this.imgList.map(async (item, index) => {
						if (item.url) {
							const response = await fetch(item.url);
							const blob = await response.blob();
							const fileName = `image${index}_${new Date().getTime()}.png`;
							zip.file(fileName, blob);
						}
					});
					await Promise.allSettled(promises);
					// 生成 ZIP 文件
					zip.generateAsync({ type: "blob" })
						.then((content) => {
							// 创建一个隐藏的 a 标签
							const link = document.createElement("a");
							link.style.display = "none";
							document.body.appendChild(link);
							// 设置 a 标签的 href 和 download 属性
							link.href = URL.createObjectURL(content);
							link.download = `${this.proSku}_${this.cateName}.zip`;
							// 触发点击事件
							link.click();
							// 清理
							URL.revokeObjectURL(link.href);
							document.body.removeChild(link);
							this.switchLoading("close");
						})
						.finally(() => {
							this.switchLoading("close");
						});
					break;
				case "catalogs":
					window.open("https://www.fulldesigns.com/Catalogs");
					break;
				case "order":
					this.inquiryTitle = this.lang.fdPins.getSamples;
					this.titlePlaceholder = "";
					this.inquiryRemark = this.lang.fdPins.sendSamples;
					this.cancelForm();
					this.pinsForm.isSample = 1;
					this.showInquiry = true;
					break;
				case "contact":
					this.inquiryTitle = this.layoutLang.contactUs;
					this.titlePlaceholder = this.lang.fdPins.describe;
					this.inquiryRemark = this.lang.fdPins.provideAssistance;
					this.cancelForm();
					this.pinsForm.isSample = 0;
					this.showInquiry = true;
					break;
				default:
					break;
			}
			return;
		},
		cancelForm() {
			for (let key in this.pinsForm) {
				if (this.pinsForm.hasOwnProperty(key)) {
					let element = this.pinsForm[key];
					if (element === null || element === undefined) {
						continue;
					}
					if (Array.isArray(element)) {
						element.forEach((item) => {
							if (item && typeof item === "object") {
								item.value = "";
							}
						});
					} else {
						if (this.userInfo.telephone && (key == "areaCode" || key == "telephone")) continue;
						this.pinsForm[key] = "";
					}
				}
			}
		},
		submitForm(noCheck) {
			let obj = {};
			if (noCheck) {
			} else {
				obj = this.pinsForm.groupNumber.reduce((pre, next, index) => {
					if (next.value) {
						pre[next.name] = next.value;
					}
					return pre;
				}, {});
			}
			this.pinsForm.socialCode = JSON.stringify(obj);
			this.pinsForm.quoteParam = JSON.stringify({
				fontData: {
					fontImgCustom: [],
					comments: `FDCate:${this.cateName},UserComments:${this.pinsForm.remark}`,
				},
			});
			fdEditInquiry(
				Object.assign(
					this.pinsForm,
					{
						quoteCateId: +this.modal.quotePid,
						quoteCateChildId: +this.modal.quoteCateId,
						buyType: 7,
						productsName: "fd",
						isMobile: this.isMobile ? 1 : 0,
					},
					{
						telephone: this.pinsForm.areaCode + "-" + this.pinsForm.telephone,
					}
				)
			)
				.then((res) => {
					this.showInquiry = false;
					this.$toast.success("add inquiry success");
				})
				.finally(() => {
					this.showInquiry = false;
				});
		},
		switchLoading(type = "open") {
			if (type == "open") {
				this.loading = true;
				this.$nextTick(() => {
					this.$refs.imgLoading.show();
				});
			} else {
				this.loading = false;
			}
		},
		getAppRecommendCateList() {
			getChildCateList({
				cateId: this.modal.quotePid,
				childCateId: this.modal.quoteCateId,
			}).then((res) => {
				this.fatherCateName = res.data.cateName;
				this.cateList = res.data.childList;
				let nowData = res.data.childList.find((item) => item.id == this.modal.quoteCateId);
				if (nowData) {
					this.cateList = this.cateList.filter((item) => item.id !== this.modal.quoteCateId);
					this.cateList.unshift(nowData);
					this.proSku = nowData.productNumber;
					this.cateName = nowData.cateName;
					this.detailUrl = nowData.quoteRoutingName;
					if (nowData.imageJson) {
						this.imgList = JSON.parse(nowData.imageJson);
					}
					this.isNewYearDiscount = nowData.isNewYearDiscount == 1 ? true : false;
				}
			});
		},
		getPriceData() {
			getPriceList({ cateId: this.modal.quoteCateId }).then((res) => {
				this.discountCode = res.data.discountCode;
				this.tableData = res.data.priceList;
				if (!Array.isArray(this.tableData)) this.tableData = [];
				res.data.priceList.map((item) => {
					let arr = Array.from({ length: item.quantityPrice.length }, () => false);
					this.arrData.push(arr);
					return item;
				});
			});
		},
		async getProjectDetail(currency) {
			let res = await getCateParamRelationByCateId({
				cateId: this.modal.quoteCateId,
				currency: currency,
			});
			let tempArr = [];
			let cloneArr = JSON.parse(JSON.stringify(res.data));
			let detailInfo = res.data.find((item) => item.paramType == "SIZE");
			// if (this.hideLanyardId.includes(this.modal.quotePid)) {
			// 	cloneArr = cloneArr.filter((item) => item.alias != "Select Attachment");
			// }
			if (detailInfo) {
				let copyNeedAdd = cloneArr.filter((item) => item.id != detailInfo.id);
				tempArr = [].concat(copyNeedAdd);
				if (this.isLanyard) {
					//织带需要将size里面的width 提出来和height 组合成 lanyard size
					let detailHeight = res.data.find((item) => item.paramName == "Select Lanyard Length");
					let widthItem = detailInfo.childList.find((item) => item.paramName == "Pin Size");
					widthItem.alias = "Lanyard Width";
					detailHeight.alias = "Lanyard Height";
					let tempItem = {
						id: detailInfo.id,
						paramName: "Lanyard Size",
						alias: this.lang.lanyard.h3,
						childList: [widthItem, detailHeight],
					};
					let copyNeedAdd = cloneArr.filter((item) => item.id != detailInfo.id && item.id != detailHeight.id);
					tempArr = [].concat([tempItem], copyNeedAdd);
					let styleMap = {
						Size: "Size",
						Colors: "Colors",
						Attachment: "Attachment",
						Stitch: "Stitch",
						Options: "Options",
						Printing: "Options",
						Packaging: "Packaging",
					};
					tempArr.forEach((item) => {
						for (let key in styleMap) {
							if (item.paramName && item.paramName.includes(key)) {
								item.styleClass = styleMap[key];
								break;
							}
						}
					});
				} else if (this.isNeon) {
					tempArr = this.setNeonProDetail(tempArr);
				} else {
					let styleMap = {
						Plating: "normal",
						Colors: "Colors",
						Attachments: "normal",
						Upgrades: "normal",
						BackStamp: "Colors",
						Packaging: "Packaging",
					};
					tempArr.forEach((item) => {
						if (item.paramName) {
							item.styleName = "";
							item.aspectRatio = "";
							item.imgStyleObj = {};
							let arr220 = ["Colors", "Attachments"];
							let style2Arr = ["Upgrades", "BackStamp"];
							if (arr220.some((key) => item.paramName.includes(key))) item.aspectRatio = 220 / 160;
							if (style2Arr.some((key) => item.paramName.includes(key))) item.styleName = item.styleName + " style5_2";
							if (item.paramName.includes("BackStamp")) item.aspectRatio = 1;
							if (item.paramName.includes("Attachments")) {
								item.imgStyleObj = { "object-fit": "cover !important" };
							}
							for (let key in styleMap) {
								if (item.paramName.includes(key)) {
									item.styleClass = styleMap[key];
									break;
								}
								item.styleClass = "normal";
							}
						}
					});
				}
			} else {
				tempArr = res.data;
				if (this.isNeon) {
					tempArr = this.setNeonProDetail(tempArr);
				}
			}
			this.detailInfoList = tempArr;

			if (this.isMobile) {
				let show2 = [];
				let show3 = [];
				this.detailInfoList.map((item) => {
					if (item.mbShowCount == 2) {
						item.showTwo = true;
						show2.push(item);
					}
					if (item.mbShowCount == 3) {
						item.showTwo = false;
						show3.push(item);
					}
				});
				let arr = [...this.getThreeItem(show3), ...this.getThreeItem(show2)];
				this.cloneArr = arr;
			}
		},
		getThreeItem(arr) {
			let temp = arr.reduce((acc, item, index) => {
				const chunkIndex = Math.floor(index / 3);
				if (!acc[chunkIndex]) {
					acc[chunkIndex] = [];
				}
				if (index % 3 === 0) {
					item.active = true;
				} else {
					item.active = false;
				}
				acc[chunkIndex].push(item);
				return acc;
			}, []);
			return temp;
		},
		changeActive(item2, index) {
			this.cloneArr[index].forEach((item) => {
				item.active = false;
				if (item2.id == item.id) item.active = true;
			});
			this.$forceUpdate();
		},
		setNowImg(img) {
			this.nowSiperImg = img;
		},
		setArrData(index, index2, data, type = "self") {
			if (type == "clearOther") {
				this.closeArrData();
			}
			this.$set(this.arrData[index], index2, data);
			// this.arrData[index][index2] = data;
		},
		closeArrData() {
			this.arrData.forEach((row, rowIndex) => {
				row.forEach((item, itemIndex) => {
					this.$set(this.arrData[rowIndex], itemIndex, false);
				});
			});
		},
		openDetail(quoteUrl) {
			if (this.needRemind) {
				this.agreeDialog = true;
				if (!this.isMobile) {
					let react = this.$refs.quickSale.getBoundingClientRect();
					this.remindRight = window.innerWidth - react.right + "px";
				}
				this.needRemind = false;
				return;
			}
			this.agreeDialog = false;
			let url = `${this.detailUrl}?cateId=${this.modal.quotePid}&cateChildId=${this.modal.quoteCateId}&type=quoteIframe`;
			if (quoteUrl && quoteUrl.url) url = quoteUrl.url;
			let data = {
				modal: "modalQuoteDialog",
				quoteUrl: url,
			};
			let timeDes = {
				fdPriceDes: "",
				fdServiceDes: "",
			};
			// try {
			// 	this.modal?.list.forEach(item => {
			// 		if (item.type == 'PriceIncludes') {
			// 			timeDes.fdPriceDes = item.proValue?.value || '';
			// 		}
			// 		if (item.type == 'ServiceOffered') {
			// 			timeDes.fdServiceDes = item.proValue?.value || '';
			// 		}
			// 	});
			// 	if (this.isNewYearDiscount) timeDes.fdServiceDes = ''
			// 	this.$store.commit("setFdTimeDes", timeDes);
			// } catch (error) {

			// }
			this.$store.commit("setMask", data);
		},
		goIndex() {
			this.$router.push("/");
		},
		isObject(value) {
			return Object.prototype.toString.call(value) === "[object Object]";
		},
		handleClose() {
			this.agreeDialog = false;
		},
		setNeonProDetail(arr) {
			let styleMap = {
				"Font-fd": "fontSize",
				Jackets: "normal",
				"Dimmer-fd": "Colors",
				"Outdoor-fd": "Colors",
			};
			arr.forEach((item) => {
				if (item.paramName) {
					item.aspectRatio = "";
					item.styleName = "";
					item.imgStyleObj = {};
					let style2_1Arr = ["Jackets"];
					let style2_2Arr = ["Dimmer-fd", "Outdoor-fd"];
					let style5_2Arr = ["Options-fd", "Backboard-fd", "Color-fd"];
					let styleHasTipArr = ["Backboard-fd"];
					if (style2_1Arr.some((key) => item.paramName.includes(key))) item.styleName = item.styleName + " style2_1";
					if (style2_2Arr.some((key) => item.paramName.includes(key))) item.styleName = item.styleName + " style2_2";
					if (style5_2Arr.some((key) => item.paramName.includes(key))) item.styleName = item.styleName + " style5_2_0";
					if (styleHasTipArr.some((key) => item.paramName.includes(key))) item.styleName = item.styleName + " hasTip";
					for (let key in styleMap) {
						if (item.paramName.includes(key)) {
							item.styleClass = styleMap[key];
							break;
						}
						item.styleClass = "normal";
					}
				}
			});
			let hideStepId = ["Text-fd", "Artwork-fd", "Size", "Type-fd", "Select Color-fd", "Size-fd"];
			let firstKey = "Jackets";
			return arr
				.filter((item) => !hideStepId.some((key) => item.paramName.includes(key)))
				.sort((a, b) => {
					if (a.paramName.includes(firstKey)) return -1; // 如果 a 是 Jackets，则放在前面
					if (b.paramName.includes(firstKey)) return 1; // 如果 b 是 Jackets，则 a 放在后面
					return 0;
				});
		},
	},
	created() {
		if (this.modal.quotePid == 67) {
			//neon去掉
			//neno三个类别添加报价按钮以及报价链接
			let childCateIdArr = [850, 851, 852];
			let childCateIdObj = {
				850: "/quote/static-color-custom-neon-signs?type=quoteIframe",
				851: "/quote/custom-rgb-changing-7-colors-neon-signs?type=quoteIframe",
				852: "/quote/rgb-gradient-changing-color-neon-signs?type=quoteIframe",
			};
			if (childCateIdArr.includes(this.modal.quoteCateId)) {
				this.toolBox.forEach((item) => {
					if (item.type == "quoteDetail") {
						item.url = childCateIdObj[this.modal.quoteCateId] || "";
					}
				});
			} else {
				this.toolBox = this.toolBox.filter((item) => item.type != "quoteDetail");
			}
		}
		//隐藏quote按钮
		if (this.hideQuoteId.includes(this.modal.quoteCateId) || this.hideQuoteId.includes(this.modal.quotePid)) {
			this.toolBox = this.toolBox.filter((item) => item.id != 2);
		}
	},
	async mounted() {
		if (process.env.isManage) {
			this.isManage = true;
			return false;
		}
		let header = document.querySelector("#modalHeader");
		if (header) this.headerHeight = header.getBoundingClientRect().height + "px";
		this.getAppRecommendCateList();
		this.getPriceData();
		await this.getProjectDetail(this.$store.state.currency.code);
	},
};
</script>

<style scoped lang="scss">
@mixin flex-y-center {
	display: flex;
	align-items: center;
}

.gray {
	color: #666666;
}

$sizes: (
	"8px": 8px,
	"9px": 9px,
	"10px": 10px,
	"11px": 11px,
	"12px": 12px,
	"13px": 13px,
	"14px": 14px,
	"16px": 16px,
	"18px": 18px,
	"20px": 20px,
	"22px": 22px,
	"24px": 24px,
);

@function size($name) {
	@if not map-get($sizes, $name + "") {
		@error "size `#{$name}` not found in map $sizes";
	}

	@return var(--#{unquote($name)});
}

.LapelPins {
	@each $key, $value in $sizes {
		--#{$key}: #{$value};
	}

	padding: 1em max(calc(50% - 600px), 1.5vw) 1em !important;
	background-color: #fff !important;

	@include respond-to(mb) {
		padding: 0 3vw !important;
	}

	color: #333333;
	font-family: Calibri !important;
	overflow: initial !important;

	.lapelContent {
		position: relative;

		.lapelCrumb {
			@include flex-y-center;
			width: 100%;
			height: 36px;
			padding: 0 8px;
			background: #e4e7ef;
			font-size: size("16px");
			margin: 40px 0;

			@include respond-to(mb) {
				font-size: 14px;
				width: calc(100% + 6vw);
				margin: 20px -3vw;
			}
		}

		.contentMain {
			position: relative;
			display: grid;
			grid-template-columns: 40% 1fr;
			column-gap: 3%;
			align-items: flex-start;
			margin-bottom: 45px;

			.leftBar {
				position: sticky;
				top: 0;
			}

			.rightBar {
				width: 100%;
				overflow: hidden;

				.proNameBox {
					margin-bottom: 20px;

					.cateName {
						font-size: size("24px");
						font-weight: bold;
					}

					.sku {
						margin: 10px 0 0;
						font-weight: bold;
						font-size: size("18px");
						color: #0066cc;
					}
				}

				.introduceBox {
					overflow: hidden;

					.introduceItem {
						margin-bottom: 24px;
					}
				}

				.priceHead {
					margin-bottom: 10px;
					width: 100%;
					height: 24px;
					display: flex;
					align-items: center;
					justify-content: space-between;

					.headerIntro {
						font-weight: bold;
						font-size: 22px;
					}

					.headerBtn {
						font-weight: bold;
						font-size: 16px;
						color: #ffffff;
						width: fit-content;
						min-width: 58%;
						max-width: 65%;
						height: 36px;
						display: flex;
						align-items: center;

						.btnItem {
							height: 100%;
							position: relative;
							display: flex;
							align-items: center;
							justify-content: center;
							column-gap: 4px;
							cursor: pointer;

							b {
								font-size: 18px;
							}

							&.contactUs {
								width: 45%;
								background: #0066cc;
								border-radius: 18px;
								margin-left: auto;

								&.hasCorner {
									margin-left: initial;
									border-radius: 18px 0px 0px 18px;

									&::before,
									&::after {
										content: "";
										position: absolute;
										top: 0;
										right: 0;
										width: 0;
										height: 0;
									}

									&::before {
										width: 3px;
										height: 100%;
										transform: skewX(-27deg);
										background-color: #ffffff;
										right: 9px;
									}

									&::after {
										border-top: 36px solid transparent;
										border-right: 18px solid #ff6600;
									}
								}
							}

							&.quickSale {
								width: 55%;
								background: #ff6600;
								border-radius: 0px 18px 18px 0px;

								span {
									min-width: 0;
									text-overflow: ellipsis;
									overflow: hidden;
									white-space: nowrap;
								}
							}
						}
					}
					@include respond-to(mb) {
						margin-bottom: 4px;
						margin-top: 0px;
						.headerIntro {
							font-size: 1.5em;
						}

						.headerBtn {
							display: none;
						}
					}
				}
			}

			@include respond-to(mb) {
				grid-template-columns: 1fr;
				align-items: initial;
				margin-bottom: 0;

				.leftBar {
					position: static;
					width: 100%;
					min-width: 0;

					.distributorTools {
						display: none;
					}
				}

				.mbConcatBtn {
					width: calc(100% + 6vw);
					min-width: 0;
					position: sticky;
					top: 0;
					margin: 20px -3vw 10px;
					display: flex;
					align-items: center;
					justify-content: center;
					column-gap: 2vw;
					padding: 0 2vw;
					background-color: #fff;
					z-index: 2;

					.btnItem {
						width: 47vw;
						height: 40px;
						text-align: center;
						line-height: 40px;
						border-radius: 6px;
						color: #ffffff;
						font-size: 15px;
						font-weight: bold;

						&.contactUs {
							background: #0066cc;
						}

						&.quickSale {
							background: #ff6600;
						}
					}
				}

				.rightBar {
					.proNameBox {
						display: none;
					}

					.introduceBox {
						.mbClickBtn {
							margin-top: 30px;

							.mbTools {
								.toolTitleText {
									text-align: left;
									font-family: Calibri;
									font-weight: bold;
									font-size: 1.5em;
									color: #333333;
								}

								.toolBox {
									display: grid;
									grid-template-columns: 1fr 1fr;
									gap: 10px;

									.toolText {
										position: absolute;
										top: 1em;
										left: 0.5em;
										font-size: 18px;

										h2 {
											font-size: 18px;
										}

										.clickEvent {
											margin-top: 8px;
											width: 75px;
											height: 30px;
											font-size: 14px;
											font-weight: bold;
											background: linear-gradient(-70deg, #5b7aa9, #243c5f);
											border-radius: 10px;
											line-height: 30px;
											text-align: center;
											display: flex;
											align-items: center;
											justify-content: center;
											column-gap: 4px;
											cursor: pointer;
											user-select: none;

											span {
												&:first-child {
													color: #ffffff;
													font-size: size("16px");
												}
											}

											.arrow {
												width: 0;
												height: 0;
												border-top: 8px solid transparent;
												border-bottom: 8px solid transparent;
												border-left: 16px solid #fff;
											}
										}
									}

									.toolItem {
										position: relative;
										min-width: 0;
										min-height: 180px;

										img {
											object-fit: contain !important;
										}
									}
								}

								.introText {
									font-family: Calibri;
									font-weight: 400;
									font-size: 14px;
									color: #666666;
								}
							}
						}
						.introduceItem {
							margin-bottom: 10px;
						}
					}
				}
			}
		}

		.distributorTools {
			margin-top: 38px;
			margin-bottom: 30px;
			font-weight: bold;
			font-size: size("22px");

			.toolBox {
				margin-top: 12px;
				display: grid;
				grid-template-columns: repeat(5, 1fr);
				gap: 10px;

				.toolItem {
					display: flex;
					align-items: center;
					justify-content: space-between;
					column-gap: 4px;
					padding: 14px 20px;
					border-radius: size("16px");
					background: #f8f8f8;

					.toolText {
						white-space: nowrap;
						font-weight: bold;
						font-size: size("18px");

						.clickEvent {
							margin-top: 8px;
							min-width: 75px;
							max-width: 120px;
							width: fit-content;
							height: 28px;
							padding: 2px 6px;
							border-radius: size("14px");
							line-height: 28px;
							text-align: center;
							background: linear-gradient(-70deg, #5b7aa9, #243c5f);
							display: flex;
							align-items: center;
							justify-content: center;
							column-gap: 4px;
							cursor: pointer;
							user-select: none;

							span {
								&:first-child {
									color: #ffffff;
									font-size: size("16px");
									text-overflow: ellipsis;
									overflow: hidden;
									white-space: nowrap;
								}
							}

							.arrow {
								width: 0;
								height: 0;
								border-top: 8px solid transparent;
								border-bottom: 8px solid transparent;
								border-left: 16px solid #fff;
							}

							// @media (any-hover: hover) {
							// 	&:hover {
							// 		box-shadow: 1px 1px 1px 1px rgba(0, 0, 0, 0.2);
							// 	}
							// }
						}
					}

					.toolImg {
						flex-shrink: 0;
						width: 48px;
						height: 48px;
						align-self: flex-end;

						img {
							width: 100%;
							height: 100%;
							aspect-ratio: 1/1;
						}
					}
				}
			}
			@include respond-to(mb) {
				display: none;
			}
		}

		.priceHead {
			margin-bottom: 10px;
			width: 100%;
			height: 24px;
			display: flex;
			align-items: center;
			justify-content: space-between;

			.headerIntro {
				font-weight: bold;
				font-size: 22px;
			}

			.headerBtn {
				font-weight: bold;
				font-size: 16px;
				color: #ffffff;
				width: fit-content;
				min-width: 58%;
				max-width: 65%;
				height: 36px;
				display: flex;
				align-items: center;

				.btnItem {
					height: 100%;
					position: relative;
					display: flex;
					align-items: center;
					justify-content: center;
					column-gap: 4px;
					cursor: pointer;

					b {
						font-size: 18px;
					}

					&.contactUs {
						width: 45%;
						background: #0066cc;
						border-radius: 18px;
						margin-left: auto;

						&.hasCorner {
							margin-left: initial;
							border-radius: 18px 0px 0px 18px;

							&::before,
							&::after {
								content: "";
								position: absolute;
								top: 0;
								right: 0;
								width: 0;
								height: 0;
							}

							&::before {
								width: 3px;
								height: 100%;
								transform: skewX(-27deg);
								background-color: #ffffff;
								right: 9px;
							}

							&::after {
								border-top: 36px solid transparent;
								border-right: 18px solid #ff6600;
							}
						}
					}

					&.quickSale {
						width: 55%;
						background: #ff6600;
						border-radius: 0px 18px 18px 0px;

						span {
							min-width: 0;
							text-overflow: ellipsis;
							overflow: hidden;
							white-space: nowrap;
						}
					}
				}
			}
			@include respond-to(mb) {
				margin-bottom: 0;
				.headerIntro {
					font-size: 1.5em;
				}

				.headerBtn {
					display: none;
				}
			}
		}

		.proList {
			margin-top: 30px;

			.proDetailList {
				display: grid;
				grid-template-columns: repeat(3, 1fr);
				column-gap: 20px;
				row-gap: 50px;

				.proItem {
					display: flex;
					flex-direction: column;
					align-items: center;
					background: #ffffff;
					box-shadow: 0px 0px 8px 0px rgba(4, 0, 0, 0.13);
				}
			}

			@include respond-to(mb) {
				display: n;
				.mbProDetail {
					.mbProDetailTitle {
						font-family: Calibri;
						font-weight: bold;
						font-size: 1.6em;
						color: #333333;
						text-align: center;
					}

					.showProDetail {
						background-color: #ffffff;
						padding: 30px 3vw 20px;
						margin: 0 -3vw;
						width: calc(100% + 6vw);

						&.first {
							padding: 20px 3vw 20px;
						}

						&:nth-child(odd) {
							background: #f7f7f7;

							.showDetailHead {
								background: #ffffff;
							}
						}

						.showDetailHead {
							width: 100%;
							display: grid;
							grid-template-columns: repeat(3, 1fr);
							column-gap: 3vw;
							border-radius: 40px;
							border: 1px solid #dfdfdf;
							background: #f7f7f7;
							padding: 4px 0;

							.headItem {
								height: 100%;
								text-align: center;
								padding: 0 0.5em;
								display: flex;
								align-items: center;
								justify-content: center;
								position: relative;

								h3 {
									font-size: 1.2em;
									font-weight: bold;
								}

								&.active {
									h3 {
										color: #0066cc;
									}

									&::after {
										content: "";
										width: 60%;
										height: 3px;
										position: absolute;
										left: 50%;
										bottom: -2px;
										transform: translateX(-50%);
										background: #0066cc;
										border-radius: 2px;
									}
								}
							}
						}

						.showDetailBox {
							.showDetail {
								margin-top: 6vw;
								display: grid;
								grid-template-columns: repeat(3, 1fr);
								gap: 3vw;
								max-height: 400px;
								overflow: hidden auto;

								&.showTwo {
									grid-template-columns: repeat(2, 1fr);
								}

								.detailItem {
									display: flex;
									flex-direction: column;
									align-items: center;
									row-gap: 8px;

									.imgBox {
										img {
											object-fit: contain;
										}
									}

									.proInfo {
										width: 100%;
										display: flex;
										flex-direction: column;
										align-items: center;
										justify-content: center;
										row-gap: 4px;
										font-size: 14px;
										text-align: center;

										.proName {
											font-weight: bold;
											font-size: 14px;
										}

										.priceList {
											font-weight: 400;
											font-size: 12px;
										}

										.proPrice {
											line-height: 1.3;
											white-space: normal;
											word-break: break-word;
										}

										.free {
											font-weight: 400;
											font-size: 14px;
											color: #f32b11;
										}

										.viewMore {
											font-weight: 400;
											font-size: 14px;
											cursor: pointer;

											.viewMoreText {
												color: #0066cc;
												text-decoration: underline;
											}
										}
									}
								}
							}
						}
					}
				}
			}
		}

		::v-deep .topDialog {
			.close-icon-wrapper {
				color: #8a8a8a !important;
			}

			&.topDialog1 {
				.dialogContent {
					border-radius: 8px;
				}

				.close-icon-wrapper {
					top: 10px;
					right: 10px;
				}
			}
		}

		.remind {
			position: absolute;
			top: 50%;
			transform: translateY(-50%);
			width: 490px;
			background-color: #fff;
			border-radius: 8px;
			padding: 10px 50px 20px;
			display: flex;
			flex-direction: column;
			align-items: center;
			row-gap: 10px;
			color: #333333;

			@include respond-to(mb) {
				width: 100%;
				top: 50%;
				right: 50%;
				transform: translate(50%, -50%);
				padding: 10px 20px 20px;
			}

			.remindHeader {
				font-size: 22px;
				font-weight: bold;
			}

			.remindContent {
				font-size: 16px;
			}

			.remindBtn {
				margin-top: 10px;
				font-size: 16px;
				color: #fff;
				padding: 0.5em 3em;
				background: #ff6600;
				border-radius: 8px;
				cursor: pointer;
				line-height: initial;
			}

			.closeIcon {
				display: block !important;
				position: absolute;
				top: 0.5em;
				right: 0.5em;
				width: 2em;
				height: 2em;
				padding: 0.5em;
				cursor: pointer;
				color: #000 !important;
			}
		}
	}
}

.LapelPins * {
	font-family: Calibri;
}
</style>
