import {request} from '~/utils/request'

//获取经销商网站绑定报价类别
export function getProBindCate(data) {
	return request({
		url: '/retailer/quote/getProBindCate',
		method: 'get',
		params: data,
	})
}

//获取网站上架产品类别
export function getInStoreBindCate(data) {
	return request({
		url: '/retailer/quote/getInStoreBindCate',
		method: 'get',
		params: data,
	})
}

//获取下架不隐藏产品类别
export function getOutStoreNoHiddenBindCate(data) {
	return request({
		url: '/retailer/quote/getOutStoreNoHiddenBindCate',
		method: 'get',
		params: data,
	})
}

//获取下架影藏产品类别
export function getOutStoreInHiddenBindCate(data) {
	return request({
		url: '/retailer/quote/getOutStoreInHiddenBindCate',
		method: 'get',
		params: data,
	})
}

//编辑网站绑定类别信息
export function editRetailerProCateRelation(data) {
	return request({
		url: '/retailer/quote/editRetailerProCateRelation',
		method: 'post',
		data: data,
	})
}

//批量编辑
export function batchEditRetailerProCateRelation(data) {
	return request({
		url: '/retailer/quote/batchEditRetailerProCateRelation',
		method: 'post',
		data: data,
	})
}

//新增产品意见
export function save(data) {
	return request({
		url: '/retailer/product/comments/save',
		method: 'post',
		data: data,
	})
}
//查询产品意见
export function findByProId(proId) {
	return request({
		url: '/retailer/product/comments/findByProId?proId='+proId,
		method: 'get',
	})
}

//获取所有总后台用户
export function getAllUser() {
	return request({
		url: '/retailer/backUser/getAllUser',
		method: 'get',
	})
}

//设置经销商用户绑定的总后台用户
export function editUserBackUserId(data) {
	return request({
		url: '/retailer/user/editUserBackUserId',
		method: 'post',
		data: data,
	})
}
