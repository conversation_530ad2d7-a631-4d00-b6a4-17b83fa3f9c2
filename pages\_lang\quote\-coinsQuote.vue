<template>
	<div id="coins-quote" class="quoteWrap">
		<Preloader v-if="isLoading"></Preloader>
		<template v-else>
			<div class="content">
				<QuoteNav :pid="pid" title="We provide these coins types for you." noPreview :config="{ slidesPerView: 6 }"></QuoteNav>
				<QuoteTitle :h1-text="lang.coins.h1"></QuoteTitle>
				<div class="leftArea">
					<template v-for="(item, index) in generalData">
						<PublicStep v-if="item.paramName == 'Coin Type'" :selectedData="selectedData" :config="allStepConfig[item.paramName]" :stepData="item" :maskName.sync="maskName" @clickFun="selectQuoteParamsFunc(item, $event)" @showMaskFn="showMaskFn"></PublicStep>

						<PublicStep v-if="item.paramName == 'quoteCategory' && !quoteStyleData.noShowDetail" :generalData="generalData" :selectedData="selectedData" :config="allStepConfig[item.paramName]" :stepData="item" :maskName.sync="maskName" @clickFun="selectQuoteParamsFunc(item, $event)" @showMaskFn="showMaskFn" @viewVideo="viewVideo"></PublicStep>

						<PublicStep v-if="item.paramName == 'Soft Enamel or Hard Enamel Coins'" :selectedData="selectedData" :config="allStepConfig[item.paramName]" :stepData="item" :maskName.sync="maskName" @clickFun="selectQuoteParamsFunc(item, $event)" @showMaskFn="showMaskFn"></PublicStep>

						<PublicStep v-if="item.paramName === 'Select Your Coin Colors' && !item.noShowDetail" :selectedData="selectedData" :config="allStepConfig[item.paramName]" :stepData="item" :maskName.sync="maskName" @clickFun="selectQuoteParamsFunc(item, $event)" @showMaskFn="showMaskFn"></PublicStep>

						<PublicStep v-if="item.paramName === 'Plating'" :selectedData="selectedData" :config="allStepConfig[item.paramName]" :stepData="item" :maskName.sync="maskName" @clickFun="selectQuoteParamsFunc(item, $event)" @showMaskFn="showMaskFn"></PublicStep>

						<PinsSizeSelect v-if="item.paramName == 'Your Coin Size'" :generalData="generalData" :selectedData="selectedData" :stepData="item" :maskName="maskName" :smallTitle="lang.coins.stepSizeTitle" :sizeImgP1="lang.coins.p1" @clickFun="selectQuoteParamsFunc(item, $event)" @closeMask="closeMask" @showMaskFn="showMaskFn"></PinsSizeSelect>

						<PublicStep v-if="item.paramName === 'Thickness of Coins'" :selectedData="selectedData" :config="allStepConfig[item.paramName]" :stepData="item" :maskName.sync="maskName" @clickFun="selectQuoteParamsFunc(item, $event)" @showMaskFn="showMaskFn"></PublicStep>

						<PublicStep v-if="item.paramName === 'Edge'" :selectedData="selectedData" :config="allStepConfig[item.paramName]" :stepData="item" :maskName.sync="maskName" @clickFun="selectQuoteParamsFunc(item, $event)" @showMaskFn="showMaskFn" @viewVideo="viewEdgeVideo"></PublicStep>

						<PublicStep v-if="item.paramName == 'Additional Upgrades (Optional)'" :selectedData="selectedData" :stepData="item" :config="allStepConfig[item.paramName]" :maskName.sync="maskName" @clickFun="selectQuoteParamsFunc(item, $event)" @viewVideo="viewAdditionalVideo" @showMaskFn="showMaskFn"></PublicStep>

						<PublicStep v-if="item.paramName == 'Packaging'" :selectedData="selectedData" :stepData="item" :config="allStepConfig[item.paramName]" :maskName.sync="maskName" @clickFun="selectQuoteParamsFunc(item, $event)" @showMaskFn="showMaskFn"></PublicStep>

						<template v-if="item.paramName === 'Upload Artwork & Comments'">
							<StepUpload class="step-item step-upload" :class="{ mask: maskName === item.paramName }" :id="item.paramName" :index="item.customIndex" :itemData="item" :uploadList.sync="uploadArtworkList" :remark.sync="remark" :isUpload.sync="isUpload" @closeMask="closeMask" @showMaskFn="showMaskFn" :key="index"></StepUpload>
						</template>
						<template v-if="item.paramName === 'qty'">
							<StepQty class="step-item step-qty" :class="{ mask: maskName === item.paramName }" :id="item.paramName" :index="item.customIndex" :itemData="item" :customQty.sync="customQty" :restaurants="restaurants" @closeMask="closeMask" @showMaskFn="showMaskFn" @calcPrice="debounceCalcPrice" :key="index"></StepQty>
						</template>
						<template v-if="item.paramName === 'Select Turnaround Time'">
							<StepTime class="step-item step-date" :class="{ mask: maskName === item.paramName }" :id="item.paramName" :cateData="cateData" :customQty="customQty" :index="item.customIndex" :itemData="item" :priceInfo="priceInfo" :previewMode="previewMode" :selectedParams="selectedData" @showMaskFn="showMaskFn" @selectItem="selectQuoteParamsFunc($event.item, $event.citem)" @closeMask="closeMask" :key="index"></StepTime>
						</template>
					</template>
				</div>
				<div class="rightArea" id="rightAreaCustom">
					<transition enter-active-class="animate__animated animate__faster animate__fadeInBottomLeft" leave-active-class="animate__animated animate__faster animate__fadeOutBottomLeft">
						<Detail v-show="showRightArea" class="type1" :class="{ mask: maskName }" :key="1" :absoluteImg="comeFromDZ.absoluteImg" :selectedData="selectedData" :priceInfo="priceInfo" :customQty="customQty" :textInfo="textInfo" :generalData="generalData" @toPosition="toPosition" @addInquiry="debounceAddInquiry" @addCart="addCart"></Detail>
					</transition>
				</div>
			</div>
			<div class="footer" id="foot">
				<Detail class="type2" :key="3" :absoluteImg="comeFromDZ.absoluteImg" :selectedData="selectedData" :priceInfo="priceInfo" :customQty="customQty" :textInfo="textInfo" :generalData="generalData" @toPosition="toPosition" @addInquiry="debounceAddInquiry" @addCart="addCart"></Detail>
			</div>
			<el-drawer :visible.sync="showArea" :with-header="true" custom-class="drawDialog" :size="device === 'ipad' ? '728px' : '85%'">
				<Detail class="type3" :key="2" :absoluteImg="comeFromDZ.absoluteImg" :selectedData="selectedData" :priceInfo="priceInfo" :customQty="customQty" :textInfo="textInfo" :generalData="generalData" @toPosition="toPosition" @addInquiry="debounceAddInquiry" @addCart="addCart"></Detail>
			</el-drawer>
			<!-- 建议弹窗-->
			<RecomendDialog :commentsDialog.sync="commentsDialog" :paramValue="selectedParamsValue" @next="recomendNext"></RecomendDialog>
			<!-- 预览弹窗-->
            <VideoPreviewDialog :pic-dialog.sync="picDialog" :zoom-pic="zoomPic" :temp-type="tempType" :selected-params-value="selectedParamsValue" @qtyAndBtnConfirm="qtyAndBtnConfirm" @qtyAndBtnNext="qtyAndBtnNext" @dialogNextStep="dialogNextStep"></VideoPreviewDialog>
            <!-- 升级数量弹窗-->
			<BaseDialog v-model="upgradeDialog" :width="device != 'mb' ? '640px' : '320px'" :minWidth="0">
				<Upgrade @closeUpgrade="upgradeDialog = false" :tipArr="tipArr" :typeName="cateData.cateName" @upgradeLevel="upgradeLevel"></Upgrade>
			</BaseDialog>
			<!-- o2o预览页面弹窗-->
			<BaseDialog v-model="showOtoDialog" :width="device !== 'mb' ? '570px' : '90%'">
				<div class="otoWrap">
					<img loading="lazy" src="@/assets/images/oto.png" alt="" />
					<p>{{ lang.p1 }}</p>
					<h3>{{ lang.p2 }}</h3>
					<p style="color: #666666">
						{{ lang.p3 }}<br />
						{{ lang.p4 }}.
					</p>

					<div class="box">
						<p class="t1">
							{{ lang.p5 }} <br />
							{{ lang.p6 }}!
						</p>
						<a href="https://www.o2o.co/previewWeb" style="text-decoration: none">
							<el-button type="primary">{{ lang.p7 }}</el-button>
						</a>
					</div>
				</div>
			</BaseDialog>
			<infoDialog :infoDialogVisible.sync="infoDialogVisible" :uploadList.sync="uploadList" :otherUpload="uploadArtworkList" :recomendUpload="recomendUpload" @getValue="getValueFun"></infoDialog>
			<!--    遮罩-->
			<myMask :maskName.sync="maskName"></myMask>
			<!--  手机，ipad端预览-->
			<PreviewBtn :showArea.sync="showArea"></PreviewBtn>
			<BaseDialog v-model="noFileDialog" :width="device != 'mb' ? '485px' : '95%'" :model="false">
				<template #closeIcon>
					<div style="display: none"></div>
				</template>
				<infoUpload :infoUploadList="infoUploadList" @pushInfoList="pushInfoList" @delInfoList="delInfoList" @updateInquiry="updateInquiry" @closeInfoDialog="closeInfoDialog"></infoUpload>
			</BaseDialog>
		</template>
	</div>
</template>
<script>
import "@/plugins/element";
import VideoPlayer from "@/components/Quote/VideoPlayer.vue";
import myMask from "@/components/Quote/Mask";
import Detail from "@/components/Quote/Detail";
import BaseDialog from "@/components/Quote/BaseDialog";
import Upgrade from "@/components/Quote/Upgrade";
import Preloader from "@/components/Quote/Preloader";
import StepUpload from "@/components/Quote/StepUpload";
import StepTime from "@/components/Quote/StepTime";
import StepQty from "@/components/Quote/StepQty";
import QuoteNav from "@/components/Medals/QuoteNav";
import infoDialog from "@/components/Medals/infoDialog";
import quoteMixin from "@/mixins/quote";
import RecomendDialog from "@/components/Quote/RecomendDialog.vue";
import PreviewBtn from "@/components/Quote/PreviewBtn.vue";
import { getQuoteConfig } from "@/assets/js/quote/quoteConfig";
import { DataProcessing } from "@/utils/dataProcessing";
import PublicStep from "@/components/Quote/PublicStep/index.vue";
import PinsSizeSelect from "@/components/Quote/PinsSizeSelect.vue";
import QuoteTitle from "@/components/Quote/QuoteTitle.vue";
import VideoPreviewDialog from "@/components/Quote/VideoPreviewDialog.vue";

export default {
	head() {
		return {
			title: "Quote page",
		};
	},
	components: {
        VideoPreviewDialog,
		QuoteTitle,
		PinsSizeSelect,
		PublicStep,
		PreviewBtn,
		RecomendDialog,
		Detail,
		BaseDialog,
		Upgrade,
		myMask,
		VideoPlayer,
		Preloader,
		StepUpload,
		StepTime,
		StepQty,
		infoDialog,
		QuoteNav,
	},
	mixins: [quoteMixin],
	data() {
		const config = getQuoteConfig.call(this, this.$route.name);
		return {
			...config,
			shoppingModel: {
				op: 0,
				selectedData: undefined, //核心回显数据
				isShopping: true, //true||第一次加载
			},
			showSizeImg: false,
			showAll: false,
			noQtyParamNameList: ["One Side 3D", "Two Sides 3D", "Edge Engraving", "Sequential Numbering", "Epoxy"],
			noCommentParamNameList: ["One Side 3D", "Two Sides 3D", "Epoxy"],
		};
	},
	watch: {
		generalDataOld: {
			handler(val) {
				this.shoppingBackfillFunc(this.generalDataOld, this.shoppingModel.selectedData);
			},
			deep: true,
		},
	},
	created() {
		this.initShoppingFunc(this.selectedData);
	},
	methods: {
		viewEdgeVideo() {
			this.viewVideo(arguments[0], arguments[1], "video");
		},
		viewAdditionalVideo() {
            this.selectQuoteParamsFunc(arguments[0], arguments[1])
		},
		selectQuoteParamsFunc(item, citem, op) {
			if (item.paramName === "Your Coin Size") {
				this.showSizeImg = true;
			}
			if (op) {
				this.selectQuoteParams(item, citem, true); //quoteCategory 只有这个是true
			} else {
				this.selectQuoteParams(item, citem);
			}
		},
		//回填业务
		shoppingBackfillFunc(generalDataOld, selectedData) {
			//回填二级 Soft Enamel or Hard Enamel Coins
			let softEnamelOrHardEnamelCoinsModel = selectedData["Soft Enamel or Hard Enamel Coins"];
			if (softEnamelOrHardEnamelCoinsModel?.length > 0) {
				softEnamelOrHardEnamelCoinsModel = softEnamelOrHardEnamelCoinsModel[0];
			}
			let colorsChildList = []; //二级绑定三级的数据
			generalDataOld.map((n) => {
				if (n.paramName === "Soft Enamel or Hard Enamel Coins") {
                    if(n.childList && n.childList.length){
                        n.childList.map((m) => {
                            if (m.id == softEnamelOrHardEnamelCoinsModel.id) {
                                softEnamelOrHardEnamelCoinsModel.childList = m.childList;
                                this.selectQuoteParamsFunc(n, softEnamelOrHardEnamelCoinsModel);
                            }
                        });
                    }
				}
			});
			//回填二级 Soft Enamel or Hard Enamel Coins end

			//回填三级 Select Your Coin Colors
			let selectYourCoinColorsModel = selectedData["Select Your Coin Colors"];
			if (selectYourCoinColorsModel?.length > 0) {
				selectYourCoinColorsModel = selectYourCoinColorsModel[0];
			}
			generalDataOld.map((n) => {
				if (n.paramName === "Select Your Coin Colors") {
					n.childList = softEnamelOrHardEnamelCoinsModel.childList; //过滤不正确的id数据
                    if(n.childList && n.childList.length){
                        n.childList.map((m) => {
                            if (m.id == this.shoppingModel.selectedData["Select Your Coin Colors"][0].id) {
                                this.selectedData["Select Your Coin Colors"] = [m];
                                this.selectQuoteParamsFunc(n, m);
                            }
                        });
                    }
				}
			});
			this.showMaskFn();
			console.log("最后", this.selectedData, this.generalData);
			console.log("最后原数据", this.shoppingModel.selectedData, this.generalDataOld);
			//回填三级 Select Your Coin Colors end
		},

		//购物车回填业务
		initShoppingFunc(item) {
			//实时读取选中数据.  最多5秒则释放
			if (this.shoppingModel.op < 5) {
				setTimeout(() => {
					this.shoppingModel.op += 1;
					this.initShoppingFunc(this.selectedData);
				}, 1000);
			} else {
				if (this.shoppingModel.selectedData == null) {
					this.shoppingModel.selectedData = DataProcessing.deepCopy(this.selectedData); //只赋值一次  避免item清空
				}
				if (this.shoppingModel.selectedData["quoteCategory"]?.length > 0) {
					let quoteCategoryModel = this.shoppingModel.selectedData["quoteCategory"][0];
					this.generalData.map((n) => {
						if (n.paramName === "quoteCategory") {
							quoteCategoryModel.quoteCategoryCateId = quoteCategoryModel.id; //用于联动查询 顶级id
							this.selectQuoteParams(n, quoteCategoryModel, true);
						}
					});
				}
				this.$forceUpdate();
			}
		},
	},
};
</script>

<style scoped lang="scss">
@import "@/assets/css/quotePublic";
</style>
