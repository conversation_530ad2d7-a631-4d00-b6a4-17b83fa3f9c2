<template>
	<div class="attachment">
		<div class="attachmentGrid">
			<!-- 顶部大图部分 -->
			<div class="topOptions">
				<div class="optionBox" @click="selectOption(item, index)" v-for="(item, index) in itemData.childList" :key="item.id" :class="{ active: selectItem?.id == item.id }">
					<div class="checkmark">
						<b class="iconfont icon-xuanzhong1"></b>
					</div>
					<div class="imgWrap">
						<img :src="getImg(item)" alt="" class="img" />
					</div>
					<div class="optionName">{{ item.alias2 || item.alias }}</div>
				</div>
			</div>
			<!-- 底部小图部分 -->
			<div class="bottomOptions">
				<div class="optionBox" v-for="(item, index) in selectItem.childList" :key="item.id">
					<div class="imgWrap">
						<img :src="getImg(item)" alt="" class="img" />
						<b class="icon-fangda4" @click="fangdaImg(index)"></b>
					</div>
					<div class="optionInfo">
						<div class="optionName">{{ item.alias }}</div>
						<div class="optionPrice" :class="{ free: citem == 'Free' }" v-for="(citem, index) in getPrice(item)" :key="index">{{ citem }}</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	name: "Attachment",
	components: {},
	props: {
		itemData: {
			type: Object,
			default: () => {},
		},
	},
	data() {
		return {
			selectItem: null,
			selectIndex: -1,
		};
	},
	watch: {},
	computed: {
		isMobile() {
			return this.$store.getters.isMobile;
		},
		viewImgList() {
			return this.selectItem.childList.map((item) => {
				return this.getImg(item);
			});
		},
	},
	methods: {
		getImg(item) {
			if (item.imageJson) {
				try {
					let imgUrl = JSON.parse(item.imageJson);
					return imgUrl[0].url;
				} catch (e) {
					console.error("Error parsing imageJson:", e);
					return item.imageUrl || "";
				}
			} else {
				return "";
			}
		},
		getPrice(item) {
			if (item.priceInfo && item.priceInfo?.priceShow) {
				return item.priceInfo["priceShow"];
			}
			return "Free";
		},
		selectOption(item, index) {
			this.selectItem = item;
			this.selectIndex = index;
		},
		fangdaImg(index) {
			let zoomNum = 3.2;
			if (this.isMobile) zoomNum = 1.4;
			const $viewer = this.$viewerApi({
				images: this.viewImgList,
				options: {
					initialViewIndex: index,
					initialCoverage: 0,
					viewed: function () {
						$viewer.zoomTo(zoomNum);
					},
				},
			});
			return $viewer;
		},
	},
	created() {
		if (this.itemData && this.itemData.childList.length > 0) {
			this.selectItem = this.itemData.childList[0];
			this.selectIndex = 0;
		}
	},
	mounted() {},
};
</script>
<style scoped lang="scss">
.attachment {
	width: 100%;

	.attachmentGrid {
		width: 100%;
		display: flex;
		flex-direction: column;
		gap: 20px;

		.topOptions {
			display: grid;
			grid-template-columns: repeat(2, 1fr);
			gap: 20px;

			.optionBox {
				position: relative;
				padding: 15px;
				background: #f6f8fa;
				border-radius: 10px 10px 0 0;
				border: 1px solid transparent;
				border-bottom: none;
				display: flex;
				flex-direction: column;
				align-items: center;
				gap: 10px;
				cursor: pointer;
				transition: all 0.3s ease;

				&.active {
					border: 1px solid #445b7d;
					border-bottom: none;
					background: #fff;
					.checkmark {
						display: block;
					}
				}

				.checkmark {
					display: none;
					position: absolute;
					top: 0;
					left: 0;
					width: 40px;
					height: 40px;
					border-top-left-radius: 6px;
					overflow: hidden;

					&::before {
						content: "";
						position: absolute;
						top: 50%;
						left: -150%;
						width: 200%;
						height: 200%;
						background: #445b7d;
						transform-origin: center;
						transform: rotate(45deg) translate(-50%, -68%);
					}

					b {
						position: relative;
						z-index: 1;
						color: white;
						font-size: 20px;
						margin-left: 4px;
						margin-top: 0;
					}
				}

				.imgWrap {
					width: 100%;
					display: flex;
					align-items: center;
					justify-content: center;

					.img {
						aspect-ratio: 3/1;
						max-width: 100%;
						max-height: 100%;
						object-fit: contain !important;
					}
				}

				.optionName {
					font-size: 18px;
					font-weight: 500;
					color: #333333;
					text-align: center;
				}
			}
		}

		.bottomOptions {
			display: grid;
			grid-template-columns: repeat(5, 1fr);
			gap: 15px;

			.optionBox {
				position: relative;
				border-radius: 10px;
				display: flex;
				flex-direction: column;
				align-items: center;
				gap: 10px;
				cursor: pointer;
				transition: all 0.3s ease;
				overflow: hidden;
				padding-bottom: 10px;
				background-color: #f5f6fa;
				border: 1px solid #d9dbdd;
				.imgWrap {
					width: 100%;
					display: flex;
					align-items: center;
					justify-content: center;
					border-radius: 8px;

					.img {
						aspect-ratio: 186/126;
						max-width: 100%;
						max-height: 100%;
						object-fit: contain !important;
					}
					b {
						cursor: pointer;
						position: absolute;
						top: 10px;
						right: 10px;
						color: #ccc;
						font-size: 16px;
					}
				}

				.optionInfo {
					width: 100%;
					display: flex;
					flex-direction: column;
					align-items: center;
					gap: 5px;

					.optionName {
						font-size: 18px;
						color: #333333;
						padding: 0 1em;
						text-align: center;
					}

					.optionPrice {
						font-size: 16px;
						color: #666666;
						padding: 0 1em;
						&.free {
							color: #e74c3c;
						}
					}
				}
			}
		}
	}
	@include respond-to(mb) {
		.attachmentGrid {
			.topOptions {
				gap: 15px;
				.optionBox {
					padding: 6px;
					.checkmark {
						width: 30px;
						height: 30px;
						b {
							font-size: 14px;
						}
					}
					.optionName {
						font-size: 14px;
						font-weight: 700;
					}
				}
			}
			.bottomOptions {
				grid-template-columns: repeat(3, 1fr);
				gap: 10px;
				.optionBox {
					gap: 6px;
					padding-bottom: 4px;
					justify-content: space-between;
					.imgWrap {
						b {
							top: 5px;
							right: 5px;
							font-size: 12px;
						}
					}
					.optionInfo {
						.optionName {
							font-size: 13px;
							font-weight: 700;
							padding: 0 4px;
						}
						.optionPrice {
							font-size: 12px;
						}
					}
				}
			}
		}
	}
}
</style>
