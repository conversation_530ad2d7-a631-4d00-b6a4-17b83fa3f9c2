<template>
	<div class="proDetail">
		<div class="proHead">
			<img src="https://static-oss.gs-souvenir.com/web/quoteManage/20240919/accessories_bar_20240919FbEDPy.png" alt="" />
			<h3>{{ itemData.alias }}</h3>
		</div>
		<div class="proContent custom-scrollbar">
			<div class="proDetailItem" v-for="(item, index) in itemData.childList" :key="index">
				<div class="imgBox">
					<img :src="item.imageJson && JSON.parse(item.imageJson)[0].url" alt="" />
				</div>
				<div class="proInfo">
					<div class="proName">{{ item.alias }}</div>
					<template v-if="Array.isArray(item.priceInfo?.priceShow)">
						<div class="priceList" :class="{ free: item.priceInfo.priceShow[0] == 'Free' || item.priceInfo.priceShow[0] == 'free' }" v-if="item.priceInfo?.priceShow && item.priceInfo.priceShow.length > 0">
							<div class="proPrice" v-for="(text, index2) in item.priceInfo.priceShow" :key="index2">
								{{ text }}
							</div>
						</div>
						<div class="free" v-else>
							<span>{{ lang.free }}</span>
						</div>
					</template>
					<template v-else-if="isObject(item.priceInfo?.priceShow)">
						<div class="viewMore">
							<el-popover :close-delay="0" :visible-arrow="false" :transition="null" popper-class="selfPinsProDetailPopper" placement="right" trigger="click">
								<div class="tableBox">
									<table>
										<thead>
											<tr>
												<th v-for="(th, index) in item.priceInfo.priceShow.table[0]" :key="index">
													{{ th }}
												</th>
											</tr>
										</thead>
										<tbody>
											<tr class="tdItem" v-for="(tdItem, index2) in item.priceInfo.priceShow.table" v-if="index2 != 0" :key="index2">
												<td v-for="(td, index3) in tdItem" :key="index3">{{ td }}</td>
											</tr>
										</tbody>
									</table>
								</div>
								<div class="subscript">
									<span></span><span>{{ item.priceInfo.priceShow.discountCode }}</span>
								</div>
								<span class="viewMoreText" slot="reference">{{ lang.ViewMore }}</span>
							</el-popover>
						</div>
					</template>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	name: "proDetail",
	props: {
		itemData: {
			type: Object,
			default: () => ({
				alias: "",
				childList: [],
			}),
		},
	},
	components: {},
	data() {
		return {};
	},
	watch: {},
	computed: {
		lang() {
			return this.$store.getters.lang.quote || {};
		},
	},
	methods: {
		isObject(value) {
			return Object.prototype.toString.call(value) === "[object Object]";
		},
	},
	created() {},
	mounted() {},
};
</script>

<style lang="scss">
.selfPinsProDetailPopper {
	transition: none;
	margin-bottom: 0 !important;
	padding: 20px 20px 0 !important;
	background-color: #ffff;
  font-family: Calibri !important;
	@include respond-to(mb) {
		max-width: 100%;
	}
	.tableBox {
		@include respond-to(mb) {
			max-width: 100%;
			overflow: auto hidden;
			padding-bottom: 6px;
		}
		table {
			width: fit-content;
			border-collapse: collapse;
			border: 1px solid #dfdfdf;
			color: #333333;
			font-family: Calibri;
			thead {
				height: 50px;
				background-color: #969eb5;
				font-size: 14px;
				font-weight: bold;
				color: #ffffff;
			}
			tr {
				&.tdItem {
					&:nth-child(odd) {
						background-color: #ffffff;
					}
					&:nth-child(even) {
						background-color: #f7f7f7;
					}
				}
				th {
					padding: 0 2px;
					min-width: 50px;
					word-break: break-word;
					text-align: center;
					//border: 1px solid #868686;
					&:first-child {
						width: 100px;
						min-width: 100px;
					}
					&:nth-child(2) {
						width: 90px;
						min-width: 90px;
					}
				}
				td {
					min-width: 50px;
					min-height: 46px;
					line-height: 46px;
					white-space: nowrap;
					border-bottom: 1px solid #dfdfdf;
					text-align: center;
				}
			}
		}
	}
	.subscript {
		padding-top: 6px;
		padding-bottom: 4px;
		font-size: 16px;
		font-family: Calibri;
		color: #333333;
		display: flex;
		align-items: center;
		justify-content: space-between;
		background-color: #fff;
	}
}
</style>
<style scoped lang="scss">
.custom-scrollbar::-webkit-scrollbar {
	/*滚动条整体样式*/
	width: 5px;
	/*高宽分别对应横竖滚动条的尺寸*/
	height: 1px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
	/*滚动条里面小方块*/
	border-radius: 10px;
	background: #8e9dc2;
}

.custom-scrollbar::-webkit-scrollbar-track {
	/*滚动条里面轨道*/
	border-radius: 10px;
}

.custom-scrollbar {
	&::-moz-scrollbar {
		width: 5px;
		height: 1px;
	}
	&::-moz-scrollbar-thumb {
		border-radius: 10px;
		background: #8e9dc2;
	}
	&::-moz-scrollbar-track {
		border-radius: 10px;
		background: #dfdfdf;
	}
}
.proDetail * {
	font-family: Calibri;
}
.proDetail {
	width: 100%;
	position: relative;
	padding-bottom: 20px;
	color: #333333;
	font-family: Calibri;
	.proHead {
		position: relative;
		height: 40px;
		border-bottom: 1px solid #dfdfdf;
		img {
			position: absolute;
			top: -54%;
			width: 100%;
			height: 50px;
			left: 50%;
			transform: translateX(-50%);
			object-fit: contain;
		}
		h3 {
			position: absolute;
			top: -0.85em;
			left: 50%;
			transform: translate(-50%, 0);
			font-weight: bold;
			font-size: 18px;
			white-space: nowrap;
			width: 65%;
			text-align: center;
			text-overflow: ellipsis;
			overflow: hidden;
		}
	}
	.proContent {
		overflow: hidden auto;
		height: 460px;
		scrollbar-color: #8e9dc2;
		.proDetailItem {
			padding: 10px;
			border-bottom: 1px solid #dfdfdf;
			display: flex;
			align-items: center;
			column-gap: 12px;
			.imgBox {
				width: 45%;
				flex-shrink: 0;
				img {
					object-fit: cover;
				}
				border: 1px solid #dfdfdf;
			}
			.proInfo {
				flex: 1;
				display: flex;
				flex-direction: column;
				justify-content: center;
				row-gap: 4px;
				.proName {
					font-weight: bold;
					font-size: 16px;
				}
				.priceList {
					font-weight: 400;
					font-size: 16px;
				}
				.proPrice {
					line-height: 1.3;
					white-space: normal;
					word-break: break-word;
				}
				.free {
					font-weight: 400;
					font-size: 16px;
					color: #f32b11;
				}
				.viewMore {
					font-weight: 400;
					font-size: 16px;
					cursor: pointer;
					.viewMoreText {
						color: #0066cc;
						text-decoration: underline;
					}
				}
			}
			&:nth-child(odd) {
				background-color: #ffffff;
			}
			&:nth-child(even) {
				background-color: #f7f7f7;
			}
		}
	}
}
</style>
