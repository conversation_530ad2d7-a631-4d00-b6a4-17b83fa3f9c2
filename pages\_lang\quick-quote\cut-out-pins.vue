<template>
	<div id="quickQuote">
		<div class="modal-box">
			<div class="left">
				<h1>{{ cateInfo.cateName }}</h1>
				<div class="swiper-area">
					<div class="swiper myswiper1" ref="swiper1">
						<div class="swiper-wrapper">
							<div class="swiper-slide" v-for="(item, index) in imgList" :key="index">
								<img :src="item.url" :alt="item.alt" :title="item.alt" />
							</div>
							<!--                            <div class="swiper-slide gallery">-->
							<!--                                <span>photo gallery</span>-->
							<!--                                <b class="icon-xialajiantou-copy"></b>-->
							<!--                            </div>-->
						</div>
					</div>
					<div class="swiper myswiper2" ref="swiper2">
						<div class="swiper-wrapper">
							<div class="swiper-slide" v-for="(item, index) in imgList" :key="index">
								<img :src="item.url" :alt="item.alt" :title="item.alt" style="width: 100%; height: 100%" />
							</div>
						</div>
						<div class="swiper-button-prev" slot="button-prev"><b class="icon-qiehuan"></b></div>
						<div class="swiper-button-next" slot="button-next"><b class="icon-qiehuan2"></b></div>
					</div>
				</div>
				<ul class="tip">
					<li>
						<b class="icon-jxsht-zdgl-dg"></b>
						<span><strong>Free</strong> Shipping</span>
					</li>
					<li>
						<b class="icon-jxsht-zdgl-dg"></b>
						<span><strong>Free</strong> Proof </span>
					</li>
					<li>
						<b class="icon-jxsht-zdgl-dg"></b>
						<span><strong>No</strong> MOQ</span>
					</li>
					<li>
						<b class="icon-jxsht-zdgl-dg"></b>
						<span><strong>No</strong> mold fees</span>
					</li>
				</ul>
				<div class="productDes" v-if="cateInfo.description && device !== 'mb'">
					<div class="title">
						<strong>Description:</strong>
					</div>
					<div class="des-con">
						<p v-for="item in JSON.parse(cateInfo.description)">{{ item.str }}</p>
					</div>
				</div>
			</div>
			<div class="right">
				<div>
					<div class="stepList">
						<template v-for="item in generalData">
							<size :id="item.customStepName" :key="item.id" v-if="item.customStepName === 'size'" class="step-size" :class="{ mask: stepName === item.customStepName }" :item="item" :hasTitleIcon="false" @clickIcon="showSizeDialog" @selectQuoteParams="selectQuoteParams($event.item, $event.citem)" @clickNext="clickNext"></size>
							<qty :id="item.customStepName" :key="item.id" :hideHot="true" v-if="item.customStepName === 'qty'" class="step-qty" :class="{ mask: stepName === item.customStepName }" :item="item" @selectQtyList="selectQtyList" @filterCustomNumber="filterCustomNumber" @selectQty="selectQty"></qty>
							<Step :itemData="item" v-if="item.customStepName === 'plating'" class="step-plating">
								<template v-slot:content>
									<div class="box-border"></div>
									<div class="step-item-params">
										<div
											class="param-item"
											v-for="citem in item.childList"
											:key="citem.id"
											:class="{
												active: hasId(citem.id, selectedData[item.paramName]),
											}"
											@click="selectQuoteParams(item, citem)"
										>
											<div>
												<img loading="lazy" :src="parseJSON(citem.imageJson)[0].url" :alt="parseJSON(citem.imageJson)[0].alt" style="aspect-ratio: 660/420" />
											</div>
											<div style="display: flex; align-items: center">
												<CustomCircle :circleType="2" :isActive="hasId(citem.id, selectedData[item.paramName])"></CustomCircle>
												<span>{{ citem.alias }}</span>
											</div>
										</div>
									</div>
								</template>
							</Step>
							<Step :itemData="item" v-if="item.customStepName === 'attachment'" class="step-attachment">
								<template v-slot:content>
									<div class="box-border"></div>
									<div class="step-item-params">
										<div
											class="param-item"
											v-for="citem in item.childList"
											:key="citem.id"
											:class="{
												active: hasId(citem.id, selectedData[item.paramName]),
											}"
											@click="selectQuoteParams(item, citem)"
										>
											<div style="display: flex; align-items: center">
												<CustomCircle :circleType="2" :isActive="hasId(citem.id, selectedData[item.paramName])"></CustomCircle>
												<div>
													<span>{{ citem.alias }}</span>
													<PriceText :paramData="citem"></PriceText>
												</div>
											</div>
											<div>
												<img loading="lazy" :src="parseJSON(citem.imageJson)[0].url" :alt="parseJSON(citem.imageJson)[0].alt" style="aspect-ratio: 660/420" />
											</div>
										</div>
									</div>
								</template>
							</Step>
							<upload :id="item.customStepName" :key="item.id" v-if="item.customStepName === 'upload'" class="step-upload" :class="{ mask: stepName === item.customStepName }" :item="item" @uploadPic="uploadPic" @clickNext="clickNext" @setIsLater="isLater = $event"></upload>
							<Step :itemData="item" v-if="item.customStepName === 'discount'" class="step-time">
								<template v-slot:content>
									<div class="box-border"></div>
									<div class="step-time-tip">{{lang.pleaseNote}}</div>
									<div class="step-item-params">
										<div
											class="param-item"
											v-for="citem in getNewDiscountList(item)"
											:key="citem.id"
											:class="{
												active: hasId(citem.id, selectedData[item.paramName]),
											}"
											@click="selectQuoteParams(item, citem)"
										>
											<DiscountText :itemData="citem" :textConfig="textConfig" style="font-weight: 700"></DiscountText>
											<div class="des">{{ citem.alias }}</div>
										</div>
									</div>
								</template>
							</Step>
						</template>
					</div>
					<div class="sub-detail">
						<div class="subtotal-left">
							<div class="sub-item">
								<div class="sub-item-left">{{ lang.Quantity }}:</div>
								<div class="sbu-item-right">
									{{ customQty || 0 }}<span v-if="presentedQuantity > 0 && presentedQuantity <= giftQuantity"> + </span><span style="color: #ff0000" v-if="presentedQuantity > 0 && presentedQuantity <= giftQuantity">{{ presentedQuantity }} Free</span>
								</div>
							</div>
							<div class="sub-item" v-show="!onlyAddInquiry">
								<div class="sub-item-left">{{ lang.unitPrice }}:</div>
								<div class="sbu-item-right">
									<CCYRate :price="priceInfo.foundationUnitPrice"></CCYRate>
								</div>
							</div>
						</div>
						<div class="subtotal-right">
							<div class="totalPriceBox">
								<strong>{{ lang.subtotal }}:</strong>
								<CCYRate class="final-price" :price="finalPrice"></CCYRate>
								<CCYRate class="before-discount-price" :price="originPrice" v-if="finalPrice !== originPrice"></CCYRate>
							</div>
							<div class="btnGroup">
								<QuoteBtn bgColor="linear-gradient(to top, #FF412B 0%, #FF7743 100%)" @click.native="addInquiry">
									{{ lang.submitInquiry }}
									<el-tooltip popper-class="cusToolTip" effect="light" :content="textInfo.inquiryTip">
										<b class="icon-wenhao3 tip-icon" @click.stop></b>
									</el-tooltip>
								</QuoteBtn>
								<QuoteBtn bgColor="linear-gradient(to top, #0066CC 0%, #2FB6F5 100%)" v-if="onlyAddInquiry === 0 || !onlyAddInquiry" @click.native="addCart">
									{{ lang.addToCart }}
									<el-tooltip popper-class="cusToolTip" effect="light" :content="textInfo.addCartTip">
										<b class="icon-wenhao3 tip-icon" @click.stop></b>
									</el-tooltip>
								</QuoteBtn>
							</div>
						</div>
					</div>
				</div>
				<div class="productDes" v-if="cateInfo.description && device === 'mb'">
					<div class="title">
						<strong>Description:</strong>
					</div>
					<div class="des-con">
						<p v-for="item in JSON.parse(cateInfo.description)">{{ item.str }}</p>
					</div>
				</div>
			</div>
		</div>
		<base-dialog v-model="sizeDialog" class="sizePreviewDialog" :width="device !== 'mb' ? '750px' : '95%'">
			<div class="sizePreviewDialogContent">
				<div class="header">Size Guideline</div>
				<div class="body">
					<div class="des" style="color: #666666">
						<p>Refer to the size comparison chart below for a quicker overview of each size. Besides, our team of professional designers will give you free help with design and sizing.</p>
						<p>If you have any more questions, please feel free to <a :href="`mailto:${userEmail}`">contact us</a>.</p>
					</div>
					<div class="previewCon">
						<div class="left-size">
							<div :class="{ active: dialogSizeItem && dialogSizeItem.id === item.id }" v-for="item in sizeList" :key="item.id" @click="selectSize(item)">
								{{ item.alias }}
							</div>
							<div :class="{ active: dialogSizeItem && dialogSizeItem.id === item.id }" v-for="item in lanyardSizeHList" :key="item.id" @click="selectLanyardHeight(item)">
								{{ item.alias }}
							</div>
						</div>
						<div class="right-preview-image">
							<div class="textWrap">
								<p class="normal-text">Size View</p>
							</div>
							<div class="shape-img">
								<img :src="shapeImg" alt="" />
							</div>
						</div>
					</div>
				</div>
				<div class="footer">
					<QuoteBtn :disabled="!dialogSizeItem" @click.native="confirmSize">Confirm</QuoteBtn>
				</div>
			</div>
		</base-dialog>
		<!--			询盘弹窗-->
		<infoDialog :infoDialogVisible.sync="infoDialogVisible" :uploadList.sync="uploadList" :otherUpload="uploadArtworkList" @getValue="getValueFun"></infoDialog>
		<!--    遮罩-->
		<myMask v-if="device === 'mb'" style="z-index: 1000" :maskName.sync="stepName"></myMask>
		<BaseDialog v-model="noFileDialog" :width="device !== 'mb' ? '485px' : '90%'" :model="false">
			<template #closeIcon>
				<div style="display: none"></div>
			</template>
			<infoUpload :infoUploadList="infoUploadList" @pushInfoList="pushInfoList" @delInfoList="delInfoList" @updateInquiry="updateInquiry" @closeInfoDialog="closeInfoDialog"></infoUpload>
		</BaseDialog>
	</div>
</template>

<script>
import "@/plugins/element";
import CustomCircle from "@/components/Quote/customCircle.vue";
import QuoteBtn from "@/components/Quote/QuoteBtn";
import CCYRate from "@/components/CCYRate.vue";
import InfoDialog from "@/components/Medals/infoDialog.vue";
import BaseDialog from "@/components/Quote/BaseDialog.vue";
import InfoUpload from "@/components/Medals/infoUpload.vue";
import myMask from "@/components/Quote/Mask";
import stepWrap from "~/components/QuickQuote/stepWrap.vue";
import lanyardStepWrap from "~/components/QuickQuote/lanyardStepWrap.vue";
import { uploadFile } from "@/utils/oss";
import { getCateParamRelationByCateId } from "@/api/web";
import { getInfo, calculateAll, calculate, otoAddCart, otoEditInquiry, setInquiry, getPriceData } from "@/api/pins";
import { debounce, deepClone, scrollToViewTop } from "@/utils/utils";
import { checkFile, acceptFileType } from "@/utils/validate";
import size from "@/components/QuickQuote/Item/size.vue";
import Discount from "@/components/QuickQuote/Item/discount.vue";
import Color from "@/components/QuickQuote/Item/color.vue";
import Attachment from "@/components/QuickQuote/Item/attachment.vue";
import Upload from "@/components/QuickQuote/Item/upload.vue";
import Qty from "@/components/QuickQuote/Item/qty.vue";
import Ribbon from "@/components/QuickQuote/Item/ribbon.vue";
import Step from "@/components/QuickQuote/Step.vue";
import PriceText from "@/components/Quote/PriceText.vue";
import DiscountText from "@/components/modal/Quote/QuoteComponents/DicountText.vue";
import {getQuoteTime} from "@/assets/js/QuotePublic";

const setIndex = (data) => {
	if (!data) {
		return false;
	}
	let index = 0;
	data.forEach((item) => {
		if (item) {
			if (!item.noShowDetail) {
				index += 1;
			}
			item.customIndex = index;
		}
	});
	return data;
};
const sortByKey = (array, key) => {
	return array.sort(function (a, b) {
		let x = a.priceInfo[key]; //如果要从大到小,把x,y互换就好
		let y = b.priceInfo[key];
		return x < y ? -1 : x > y ? 1 : 0;
	});
};

const getMyCustomStepName = (item) => {
	let paramName = item.paramName,
		type = item.paramType;
	let qtyNameArr = ["qty", "Quantity"],
		platingArr = ["Plating"],
		attachNameArr = ["Select Metal Finish", "Select Plating Colors", "Select Attachment", "Edge", "Patch Shape", "Printing Method", "Patch Style", "PVC Patch Shape", "Lanyard Attachment", "Patch Backing", "Color", "Patch Border", "2D or 3D", "Plating", "Ornament Attachment", "Select Link & Chain Options", "Link & Chain Options", "Fidget Spinner Plating", "Attachment"],
		uploadNameArr = ["Upload Artwork & Comments", "Design Your Printing"];
	if (type === "SIZE") {
		return "size";
	}
	if (type === "COLOR") {
		return "color";
	}
	if (type === "DISCOUNT") {
		return "discount";
	}
	if (paramName === "Ribbon") {
		return "ribbon";
	}
	if (platingArr.includes(paramName)) {
		return "plating";
	}
	if (qtyNameArr.includes(paramName)) {
		return "qty";
	}
	if (attachNameArr.includes(paramName)) {
		return "attachment";
	}
	if (uploadNameArr.includes(paramName)) {
		return "upload";
	}
};

const addCustomProperty = (data) => {
	let handle = (list) => {
		for (let i = 0; i < list.length; i++) {
			let item = list[i];
			item.remark = "";
			item.noShowDetail = false;
			item.isHidden = false; //不可选属性
			item.files = [];
			item.inputNum = undefined;
			if (item.childList && item.childList.length) {
				handle(item.childList);
			}
			item.customStepName = getMyCustomStepName(item);
			//特殊处理
			if (item.paramName === "Upload Artwork & Comments") {
				item.alias = "Upload Your Artwork";
			}
			if (item.paramName === "Lanyard Popular Colors") {
				item.noShowDetail = true;
				item.childList.forEach((c) => {
					c.giftQuantity = undefined;
				});
			}
			if (item.paramName === "Lanyard Attachment") {
				item.childList[0].customIndex = 3;
			}
		}
	};
	handle(data);
	return data;
};
export default {
	provide() {
		return {
			getProvideData: () => {
				return {
					cateInfo: this.cateInfo,
					customQty: this.customQty,
					selectedData: this.selectedData,
					stepName: this.stepName,
					isCustom: this.isCustom,
					qtyList: this.qtyList,
					selectedQtyInd: this.selectedQtyInd,
					customNumberPrice: this.customNumberPrice,
					customNumberUnitPrice: this.customNumberUnitPrice,
					uploadArtworkList: this.uploadArtworkList,
					isLater: this.isLater,
					isFullReductionActivity: this.isFullReductionActivity,
					satisfiedQuantity: this.satisfiedQuantity,
					giftQuantity: this.giftQuantity,
				};
			},
			customObj: this.customObj,
		};
	},
	props: ["isUs"],
	data() {
		return {
			acceptFileType,
			noFileDialog: false,
			onlyAddInquiry: 0,
			sizeDialog: false,
			uploadArtworkList: [],
			uploadList: [],
			infoUploadList: [],
			customObj: {
				customNumber: "",
			},
			selectedParamsValueParent: {},
			selectedParamsValue: {},
			isCustom: false,
			isDs: 0,
			isFastQuote: 1,
			loadAddCart: false,
			isInquiry: false,
			infoDialogVisible: false,
			inquiryId: 0,
			debounceCalcPrice: null,
			debounceAddInquiry: null,
			dialogSizeItem: null,
			tempSizeItemParent: null,
			mapMessage: {
				upload: "Please Upload your Artwork.",
				size: "Please Select Your Size.",
				qty: "Please Select Your Qty.",
				attachment: "Please Select Your Attachment.",
				discount: "Please Select your discount.",
			},
			stepName: "",
			cateInfo: {},
			generalData: [],
			isLater: false,
			qtyList: [],
			priceInfo: {},
			sizeList: [],
			lanyardSizeHList: [],
			selectedData: {},
			selectedQtyInd: -1,
			pid: 460,
			cateId: 462,
			isFullReductionActivity: 0,
			satisfiedQuantity: Number.MAX_VALUE,
			giftQuantity: 0,
			isLanyardHeight: false,
			imgList: [],
		};
	},
	watch: {
		selectedData: {
			handler() {
				this.debounceCalcPrice();
			},
			deep: true,
		},
	},
	components: {
		DiscountText,
		PriceText,
		Step,
		Ribbon,
		Qty,
		Upload,
		Attachment,
		Color,
		Discount,
		size,
		InfoUpload,
		BaseDialog,
		InfoDialog,
		CustomCircle,
		QuoteBtn,
		CCYRate,
		myMask,
		stepWrap,
		lanyardStepWrap,
	},
	computed: {
		textConfig() {
			return {
				freeText: "Standard Free",
			};
		},
		presentedQuantity() {
			return this.selectedData["Lanyard Popular Colors"] && this.selectedData["Lanyard Popular Colors"].reduce((accumulator, currentValue) => Number(accumulator) + Number(currentValue?.giftQuantity || 0), 0);
		},
		lengthData() {
			return this.generalData.find((item) => item.paramName === "Lanyard Length");
		},
		userEmail() {
			return this.$store.state.proSystem.email;
		},
        finalPrice() {
            return this.priceInfo.totalPrice;
        },
		originPrice() {
			let priceInfo = this.priceInfo;
			return priceInfo.foundationUnitPrice * priceInfo.totalQuantity + priceInfo.toolingCharge;
		},
		customNumberPrice() {
			return this.isCustom && this.customObj.customNumber ? this.priceInfo.totalPrice : 0;
		},
		customNumberUnitPrice() {
			return this.isCustom && this.customObj.customNumber ? this.priceInfo.foundationUnitPrice : 0;
		},
		quoteLink() {
			return "";
		},
		sizeValue() {
			let findSize = this.generalData.find((item) => {
				return item.paramType === "SIZE";
			});
			if (!findSize) {
				return "";
			}
			return this.selectedData[findSize.paramName] && this.selectedData[findSize.paramName][0]?.paramCode;
		},
		shapeImg() {
			try {
				let defaultUrl = JSON.parse(this.sizeList[0].imageJson)[0].url;
				let sizeItem = this.dialogSizeItem;
				if (sizeItem) {
					return JSON.parse(sizeItem.imageJson)[0].url;
				} else {
					return defaultUrl;
				}
			} catch (e) {
				return "";
			}
		},
		proId() {
			if (this.previewMode) {
				return 148;
			} else {
				return this.$store.state.proId;
			}
		},
		projectName() {
			if (this.previewMode) {
				return "o2o-site-style";
			} else {
				return this.$store.state.proName;
			}
		},
		previewMode() {
			return process.env.VUE_APP_MODE === "Preview";
		},
		proType() {
			return this.$store.state.proType;
		},
		lang() {
			return this.$store.getters.lang.quote || {};
		},
		userUUID() {
			return this.$store.state.userUUID;
		},
		userId() {
			return this.$store.state.userInfo?.id;
		},
		isLogin() {
			return this.$store.getters.isLogin;
		},
		userInfo() {
			return this.$store.state.userInfo;
		},
		device() {
			return this.$store.state.device;
		},
		textInfo() {
			return {
				inquiryTip: this.lang.inquiryTip,
				addCartTip: this.lang.addCartTip,
				emailText: this.lang.emailText + " " + this.$store.state.proSystem.email,
				email: this.lang.mailto + this.$store.state.proSystem.email,
			};
		},
		customQty() {
			if (this.isCustom) {
				return this.customObj.customNumber;
			} else {
				return (this.qtyList && this.qtyList.length && this.qtyList[this.selectedQtyInd]?.totalQuantity) || 0;
			}
		},
	},
	methods: {
        clearField(e = "DISCOUNT") {
            let findDiscount = this.generalData.find((item) => item.paramType === e);
            if (findDiscount) {
                let name = findDiscount.paramName;
                this.selectedData[name] = [];
            }
        },
        getNewDiscountList(itemData) {
            let result = getQuoteTime(itemData.childList,this.priceInfo,this.proType),
                    originShowSmallPrice =  this.$store.state.showSmallPrice;
            this.$store.commit("setShowSmallPrice", result.newShowSmallPrice);
            if(result.newShowSmallPrice !== originShowSmallPrice){
                this.clearField("DISCOUNT")
            }
            return result.arr;
        },
		handleWeightDiscount() {
			//获取算价格参数
			let priceParam = this.getPriceParam();
			//获取折扣参数列表
			let discountList = this.generalData
				.find((item) => {
					return item.paramType === "DISCOUNT";
				})
				?.childList.filter((item) => item.priceInfo.priceType === 10);
			if (!discountList?.length) {
				return;
			}
			discountList.forEach((item) => {
				if (priceParam.sizeId && priceParam.cateId) {
					if (priceParam.sizeId && priceParam.cateId && (priceParam.quantity || priceParam.qtyDetailDTOS?.length)) {
						calculate(Object.assign({}, priceParam, { discountId: item.priceInfo.id })).then((res) => {
							this.$set(item.priceInfo, "customWeightPrice", res.data.discountPrice);
						});
					}
				}
			});
		},
		//判断是否选中
		hasId(id, arr) {
			if (!arr) {
				return false;
			}
			return !!arr.find((item) => {
				return item.id === id;
			});
		},
		selectSize(item) {
			this.dialogSizeItem = item;
			this.isLanyardHeight = false;
		},
		selectLanyardHeight(item) {
			this.dialogSizeItem = item;
			this.isLanyardHeight = true;
		},
		parseJSON(str) {
			return str
				? JSON.parse(str)
				: [
						{
							url: "",
						},
				  ];
		},
		clickNext() {
			if (!this.checkParams()) {
				scrollToViewTop(document.getElementById(`${this.stepName}`));
			}
		},
		pushInfoList(data) {
			this.infoUploadList.push(data);
		},
		delInfoList(index) {
			this.infoUploadList.splice(index, 1);
		},
		updateInquiry() {
			if (!this.inquiryId) {
				this.closeInfoDialog(false);
			}
			let newPicPath = this.infoUploadList.map((item) => item.secure_url);
			let data = {
				id: this.inquiryId,
				newPicPath: newPicPath,
			};
			setInquiry(data).then(() => {
				this.closeInfoDialog(false);
			});
		},
		closeInfoDialog(data) {
			this.infoUploadList = [];
			this.noFileDialog = data;
			window.location.href = "/";
		},
		confirmSize() {
			if (this.isLanyardHeight) {
				this.selectQuoteParams(this.lengthData, this.dialogSizeItem);
			} else {
				this.selectQuoteParams(this.tempSizeItemParent, this.dialogSizeItem);
			}
			this.sizeDialog = false;
			this.dialogSizeItem = null;
		},
		selectQtyList(ind) {
			this.isCustom = false;
			this.selectedQtyInd = ind;
			this.customObj.customNumber = "";
			this.debounceCalcPrice();
			this.handleWeightDiscount();
		},
		filterCustomNumber() {
			this.debounceCalcPrice();
			this.handleWeightDiscount();
		},
		selectQty() {
			this.isCustom = true;
			this.selectedQtyInd = -1;
		},
		selectQuoteAdditional(item, citem) {
			let param = this.selectedData[item.paramName];
			if (!param) {
				param = [];
			}
			//判断是否已选, 是则取消选中，否则选中
			let findInd = param.findIndex((item) => {
				return item.paramName === citem.paramName;
			});
			if (findInd >= 0) {
				citem.inputNum = undefined;
				param.splice(findInd, 1);
			} else {
				param.push(citem);
			}
			//取消选中 No Upgrades
			let ind = param.findIndex((item) => {
				return item.chooseNum <= 1;
			});
			if (ind >= 0) {
				param[ind].inputNum = undefined;
				param.splice(ind, 1);
			}
			//如果全部取消，默认选中 No Upgrades
			if (!param.length) {
				let fItem = item.childList.find((item) => {
					return item.chooseNum <= 1;
				});
				if (fItem) {
					param = [fItem];
				}
			}
			this.selectedData[item.paramName] = param;
		},
		//参数选中事件
		async selectQuoteParams(item, citem) {
			this.selectedParamsValueParent = item;
			this.selectedParamsValue = citem;
			this.$set(this.selectedData, item.paramName, [citem]);
			//如果是重量相关参数
			if (citem.isWeightParam) {
				// this.handleWeightDiscount();
			}
		},
		showSizeDialog(item) {
			this.tempSizeItemParent = item;
			this.sizeDialog = true;
		},
		uploadPic(files) {
			this.$gl.show();
			let fileResult = checkFile(Array.from(files), this.acceptFileType, 80);
			if (!fileResult) {
				this.$toast.error("File type error");
				this.$gl.hide();
				return false;
			}
			if (!fileResult.nomalSize.length) {
				this.$store.commit("setSizeDialog", true);
				this.$store.commit("setInputRefName", "replayUpload");
				this.$store.commit("setOverSizeList", fileResult.overSize);
				document.querySelector("#uploadInput").value = "";
				return false;
			}
			let uploadPromises = [];
			fileResult.nomalSize.forEach((file) => {
				let promise = uploadFile(file).then((res) => {
					this.uploadArtworkList.push({
						original_filename: file.name,
						secure_url: res,
						size: (file.size / 1024).toFixed(1),
					});
				});
				uploadPromises.push(promise);
			});
			Promise.all(uploadPromises).then(() => {
				document.querySelector("#uploadInput").value = "";
				if (fileResult.overSize.length > 0) {
					this.$store.commit("setSizeDialog", true);
					this.$store.commit("setInputRefName", "replayUpload");
					this.$store.commit("setOverSizeList", fileResult.overSize);
				}
				this.$gl.hide();
			});
		},
		getQuoteParam(type = "inquiry") {
			let selectedData = deepClone(this.selectedData),
				finaData = [],
				generalData = deepClone(this.generalData);
			for (let i = 0; i < generalData.length; i++) {
				let item = generalData[i];
				if (item.customStepName === "qty") {
					item.childList = [];
					finaData.push(item);
					continue;
				}
				if (item.customStepName === "upload") {
					item.childList = [];
					item.later = this.isLater;
					finaData.push(item);
					continue;
				}
				//将选中参数里面的childlist置空
				if (selectedData[item.paramName] && selectedData[item.paramName].length) {
					selectedData[item.paramName].forEach((c) => {
						c.childList = [];
						c.quantity = c.inputNum;
						if (!c.giftQuantity) {
							c.giftQuantity = 0;
						}
					});
				}
				item.childList = selectedData[item.paramName] || [];
				finaData.push(item);
			}
			let files = [];
			let artworkList = this.uploadArtworkList;
			if (artworkList.length > 0) {
				artworkList.forEach((item) => {
					item.secure_url && files.push(item.secure_url);
				});
			}
			return {
				classificationData: this.cateInfo,
				finaData: finaData,
				fontData: {
					fontImgCustom: files,
					comments: this.remark,
				},
				designCanvas: [],
			};
		},
		getPriceParam(qty) {
			const data = {
				cateId: this.cateId,
				discountId: "",
				packingIdList: [],
				paramIdList: [],
				projectName: this.projectName,
				quantity: "",
				sizeId: "",
				upgradesQtyDTO: [],
				qtyDetailDTOS: [],
			};
			let generalData = this.generalData,
				selectedData = this.selectedData;
			for (let i = 0; i < generalData.length; i++) {
				let item = generalData[i];
				let paramName = item.paramName;
				let selectedVal = selectedData[paramName];
				if (item.customStepName === "qty") {
					data.quantity = qty || Number(this.customQty);
					continue;
				}
				if (item.customStepName === "upload") {
					continue;
				}
				if (!selectedVal) {
					continue;
				}
				selectedVal.forEach((citem) => {
					let paramType = citem.paramType;
					switch (paramType) {
						case "NORMAL":
							data.paramIdList.push(citem?.priceInfo?.id);
							break;
						case "DISCOUNT":
							data.discountId = citem?.priceInfo?.id;
							break;
						case "SIZE":
							data.sizeId = citem?.priceInfo?.id;
							break;
						case "COLOR":
							if (citem.inputNum && citem.inputNum > 0) {
								data.qtyDetailDTOS.push({
									quantity: citem.inputNum || 0,
									paramType: "COLOR",
									paramId: citem?.priceInfo?.id,
									paramValue: "",
									giftQuantity: citem.giftQuantity || 0,
								});
							}
							break;
						default:
							break;
					}
				});
			}
			if (!data.qtyDetailDTOS.length) {
				delete data.qtyDetailDTOS;
			}
			return data;
		},
		calcPrice() {
			let priceParam = this.getPriceParam();
			if (priceParam.sizeId && priceParam.cateId) {
				calculateAll(priceParam).then((res) => {
					this.qtyList = res.data.filter((item) => item.isFastQuote);
				});
			}
			if (priceParam.sizeId && priceParam.cateId && (priceParam.quantity || priceParam.qtyDetailDTOS?.length)) {
				calculate(priceParam).then((res) => {
					this.priceInfo = res.data;
                    console.log(this.priceInfo)
				});
			}
		},
		checkParams() {
			let verify = true,
				stepName = "",
				selectedData = this.selectedData,
				generateData = this.generalData;
			for (let i = 0; i < generateData.length; i++) {
				let item = generateData[i];
				//参数名称
				let paramName = item.paramName;
				//选中的参数值
				let selectedVal = selectedData[paramName];
				if (item.customStepName === "upload") {
					if (!this.uploadArtworkList.length && !this.isLater) {
						stepName = item.customStepName;
						verify = false;
						break;
					}
				} else if (item.customStepName === "qty") {
					if (!this.customQty || this.customQty <= 0) {
						stepName = item.customStepName;
						verify = false;
						break;
					}
				} else if (item.customStepName === "color") {
					let temp = selectedVal;
					if (!temp || !temp.length) {
						stepName = item.customStepName;
						verify = false;
						break;
					}
					let result = temp.every((item) => {
						return item.inputNum > 0;
					});
					if (!result) {
						stepName = item.customStepName;
						verify = false;
						break;
					}
				} else if (item.customStepName === "ribbon") {
					//medals报价特殊校验
					let temp = selectedVal;
					if (!temp.length) {
						stepName = item.customStepName;
						verify = false;
						break;
					} else {
						if (temp[0].paramName == "Stock Ribbon") {
							if (!temp[0].colorValue || !temp[0].sizeValue) {
								stepName = item.customStepName;
								verify = false;
								break;
							}
						} else if (temp[0].paramName == "Custom Ribbon") {
							if (!temp[0].sizeValue) {
								stepName = item.customStepName;
								verify = false;
								break;
							}
						}
					}
				} else {
					if (!selectedVal || selectedVal.length === 0) {
						stepName = item.customStepName;
						verify = false;
						break;
					}
				}
			}
			this.stepName = stepName;
			return verify;
		},
		addInquiry() {
			if (!this.checkParams()) {
				this.$toast.error(this.mapMessage[this.stepName] || "Please improve the parameters");
				scrollToViewTop(document.getElementById(`${this.stepName}`));
				return false;
			}
			//如果是临时项目
			if (this.$store.state.isTemporary) {
				this.$store.commit("setMask", "temporary");
				return false;
			}
			this.infoDialogVisible = true;
		},

		getValueFun(val) {
			let quoteParam = this.getQuoteParam();
			let priceParam = this.getPriceParam();
			//询盘如果有期待时间，finaData手动添加数据
			if (val.expectTime) {
				quoteParam.finaData.push({
					parentId: 0,
					alias: "Expected Delivery Date",
					childList: [
						{
							parentId: 10000,
							alias: val.expectTime,
						},
					],
				});
			}
			let data = {
				platformProductId: this.cateInfo.platformProductId,
				proId: this.proId,
				email: "",
				productsName: this.projectName,
				quoteCateId: this.pid,
				quoteCateChildId: priceParam.cateId,
				isMobile: this.device === "mb" ? 1 : 0,
				buyType: 7,
				quotePriceParam: JSON.stringify(priceParam),
				quoteParam: JSON.stringify(quoteParam),
				...val,
				telephone: val.areaCode + "-" + val.telephone,
				isDs: this.isDs,
				isFastQuote: this.isFastQuote,
			};
			this.otoEditInquiry(data);
		},

		otoEditInquiry(data) {
			if (this.isInquiry) {
				return false;
			}
			this.isInquiry = true;
			otoEditInquiry(data)
				.then((res) => {
					this.inquiryId = res.data;
					if (!this.uploadArtworkList.length && !this.uploadList.length) {
						this.noFileDialog = true;
					} else {
						this.$confirm(this.lang.p22, this.lang.p21, {
							confirmButtonText: this.lang.Confirm,
							type: "success",
							showCancelButton: false,
							center: true,
							customClass: "inquirySuccess",
							confirmButtonClass: "inquirySuccessBtn",
						}).finally(() => {
							window.location.href = "/";
						});
					}
				})
				.finally(() => {
					this.isInquiry = false;
				});
		},

		async addCart() {
			if (!this.checkParams()) {
				this.$toast.error(this.mapMessage[this.stepName] || "Please improve the parameters");
				scrollToViewTop(document.getElementById(`${this.stepName}`));
				return false;
			}
			//如果是临时项目
			if (this.$store.state.isTemporary) {
				this.$store.commit("setMask", "temporary");
				return false;
			}
			if (this.loadAddCart) {
				return false;
			}
			this.loadAddCart = true;
			let quoteParam = this.getQuoteParam("cart");
			let priceParam = this.getPriceParam();
			let data = {
				proId: this.proId,
				uuid: this.isLogin ? null : this.userUUID,
				userId: this.isLogin ? this.userId : null,
				buyType: 7,
				quoteCateId: this.pid,
				isMobile: this.device === "mb" ? 1 : 0,
				quoteCateChildId: priceParam.cateId,
				quantity: this.customQty,
				quotePriceParam: JSON.stringify(priceParam),
				quoteParam: JSON.stringify(quoteParam),
				isDs: this.isDs,
				isFastQuote: this.isFastQuote,
			};
			otoAddCart(data, this.priceInfo)
				.then((res) => {
					this.$toast.success(res.message);
					this.$router.push({
						path: "/cart",
					});
				})
				.finally(() => {
					setTimeout(() => {
						this.loadAddCart = false;
					}, 1000);
				});
		},
		initSwiper() {
			this.myswiper1 = new Swiper(this.$refs.swiper1, {
				slidesPerView: 5,
				direction: "vertical",
				spaceBetween: 8,
				watchSlidesVisibility: true, //防止不可点击
				grabCursor: true,
			});
			this.myswiper2 = new Swiper(this.$refs.swiper2, {
				slidesPerView: 1,
				spaceBetween: 10,
				autoplay: false,
				grabCursor: true,
				observer: true,
				observeParents: true,
				thumbs: {
					swiper: this.myswiper1,
				},
				navigation: {
					nextEl: ".swiper-button-next",
					prevEl: ".swiper-button-prev",
				},
			});
		},
		//价格分层参数
		getPriceData() {
			getPriceData({ buyType: 7, productCateId: this.pid }).then((res) => {
				this.$store.commit("setMorePriceData", res.data);
			});
		},
	},
	async created() {
		this.getPriceData();
		this.debounceAddInquiry = debounce(this.addInquiry, 300);
		this.debounceCalcPrice = debounce(this.calcPrice, 300);
	},
	async mounted() {
		try {
			this.$gl.show();
			let cateId = this.cateId;
			let result = await Promise.all([
				getInfo({ id: cateId }),
				getCateParamRelationByCateId({
					cateId: cateId,
					isFastQuote: 1,
				}),
			]);
			this.cateInfo = result[0]?.data || {};
			this.imgList = JSON.parse(this.cateInfo.imageJson);
            this.$nextTick(()=>{
                this.initSwiper();
            })
			this.sizeList = result[1]?.data.find((item) => item.paramType === "SIZE")?.childList || [];
			let selectedData = {},
				isLater = false,
				selectedQtyInd = -1,
				selectedQty = 0,
				generalData = setIndex(sortByKey(addCustomProperty(result[1]?.data), "fastQuoteStepIndex"));
			//默认选中参数
			if (generalData?.length) {
				isLater = true;
				generalData.forEach((item) => {
					let customStepName = item.customStepName;
					if (customStepName !== "qty" && customStepName !== "upload") {
						let findDefault = item.childList.find((c) => c.priceInfo.isFastQuoteSelected);
						if (findDefault) {
							selectedData[item.paramName] = [findDefault];
							//选中尺寸下的默认数量
							if (customStepName === "size") {
								try {
									let priceInfo = findDefault.priceInfo;
									let increasePrice = JSON.parse(priceInfo.increasePrice).filter((item) => item.isFastQuote);
									let defaultQtyIndex = increasePrice.findIndex((q) => q.isFastQuoteSelected);
									if (defaultQtyIndex > -1) {
										selectedQtyInd = defaultQtyIndex;
										selectedQty = increasePrice[defaultQtyIndex].quantity;
									}
								} catch (e) {}
							}
							if (customStepName === "color") {
								findDefault.inputNum = 100;
							}
						}
					}
				});
			}
			this.selectedData = selectedData;
			this.generalData = generalData;
			this.isLater = isLater;
			this.selectedQtyInd = selectedQtyInd;
			let priceParam = this.getPriceParam(selectedQty),
				priceResult;
			priceResult = await Promise.all([calculateAll(priceParam), calculate(priceParam)]);
			this.qtyList = (priceResult && priceResult[0].data.filter((item) => item.isFastQuote)) || [];
			this.priceInfo = (priceResult && priceResult[1].data) || {};
			this.handleWeightDiscount();
			this.$gl.hide();
		} catch (e) {
			console.log(e);
		}
	},
};
</script>
<style scoped lang="scss">
@mixin selectedStyle {
	border-color: $color-primary;
	&::after {
		content: "\e82c";
		position: absolute;
		width: 1rem;
		height: 1rem;
		line-height: 1rem;
		right: 0;
		top: 0;
		border-bottom-left-radius: 70%;
		text-align: center;
		color: #ffffff;
		background-color: $color-primary;
		font-family: "modalicon";
		font-size: 0.6rem;
	}

	@include respond-to(mb) {
		&::after {
			width: 1.25rem;
			height: 1.25rem;
			line-height: 1.25rem;
		}
	}
}

.productDes {
	.title {
		margin-bottom: 10px;
		font-size: 16px;
	}

	.des-con {
		p {
			margin: 0 0 10px 0;
			line-height: 1.4em;
			font-size: 14px;
		}
	}

    @include respond-to(mb){
        padding: 10px;
    }
}

.sizePreviewDialogContent {
	padding: 10px;

	.header {
		padding: 10px;
		font-size: 24px;
		font-weight: 700;
		text-align: center;

		@include respond-to(mb) {
			font-size: 16px;
		}
	}

	.body {
		padding: 0 20px;

		p {
			margin-bottom: 10px;
		}
	}

	.des a {
		color: $color-primary;
		text-decoration: underline;
	}

	.previewCon {
		display: grid;
		grid-template-columns: 150px 1fr;
		align-items: flex-start;
		grid-gap: 40px;
		color: #333333;

		@include respond-to(mb) {
			grid-template-columns: 1fr;
			grid-gap: 10px;
		}

		.left-size {
			@include respond-to(mb) {
				display: grid;
				grid-template-columns: repeat(4, 1fr);
				grid-gap: 5px;
			}

			& > div {
				display: flex;
				align-items: center;
				padding: 0 10px;
				min-height: 30px;
				background: #f6f6f6;
				border-radius: 4px;
				margin-bottom: 6px;
				transition: all 0.3s;
				cursor: pointer;

				@include respond-to(mb) {
					padding: 0 5px;
					justify-content: center;
				}

				@media (any-hover: hover) {
					&:hover {
						background-color: $color-primary;
						color: #ffffff;
					}
				}

				&.active {
					background-color: $color-primary;
					color: #ffffff;
				}
			}
		}

		.right-preview-image {
			display: flex;
			flex-direction: column;
			position: relative;
			padding: 20px;
			background-color: #f6f6f6;
			border-radius: 10px;

			@include respond-to(mb) {
				margin: 0;
			}

			&::before {
				position: absolute;
				left: -20px;
				top: 20px;
				content: "";
				width: 0;
				height: 0;
				border-width: 20px 0 20px 20px;
				border-style: solid;
				border-color: transparent transparent #f6f6f6;
			}

			.textWrap {
				text-align: left;

				.normal-text {
					margin-bottom: 8px;
					font-size: 18px;
					color: #333333;
					transition: all 0.3s;

					@include respond-to(mb) {
						font-size: 12px;
					}
				}
			}

			.shape-img {
				flex: 1;
				display: flex;
				justify-content: center;
				align-items: center;

				img {
					height: 320px;
					object-fit: contain;

					@include respond-to(mb) {
						height: 214px;
					}
				}
			}
		}
	}

	.footer {
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 20px 0 10px;
	}
}

#quickQuote {
	font-size: 14px;

	::v-deep b {
		font-size: 16px;
	}

	::v-deep .icon-wenhao3 {
		font-weight: 600;
		color: #666666;
	}

	@include respond-to(mb) {
		font-size: 12px;
	}

	::v-deep .v-application--wrap {
		min-height: auto;
	}

	.modal-box {
		display: grid;
		grid-template-columns: 1.45fr 2fr;
		grid-gap: 60px;
		padding-top: 20px;
		padding-bottom: 20px;
		z-index: auto;

		@include respond-to(mb) {
			grid-template-columns: 1fr;
			grid-gap: 0;
			padding: 0;
		}

		.left {
			min-width: 0;
			@include respond-to(mb) {
				order: 1;
				padding: 10px;
				background-color: #ffffff;
			}

			h1 {
				margin-bottom: 10px;
				font-weight: bold;
				font-size: 36px;
				color: #333333;
				text-align: center;
				@include respond-to(mb) {
					font-size: 18px;
				}
			}

			.swiper-area {
				display: flex;
				gap: 0.5rem;
				height: 460px;
				margin-bottom: 30px;

				.swiper-button-prev::after,
				.swiper-button-next::after {
					display: none;
				}

				.swiper-button-prev b,
				.swiper-button-next b {
					font-size: 40px;
					color: #d2d2d2;

					@include respond-to(mb) {
						font-size: 24px;
					}
				}

				@include respond-to(mb) {
					height: 24rem;
				}

				.myswiper1 {
                    width: 86px;

					@include respond-to(mb) {
                        width: 4.38rem;
					}

					.gallery {
						display: flex;
						flex-direction: column;
						justify-content: center;
						align-items: center;
						background-color: $color-primary;
						color: #ffffff;
						font-size: 1.13rem;
						font-weight: 700;
						text-transform: uppercase;
					}

					img {
						border-radius: 6px;
					}

					.swiper-slide-thumb-active img {
						border: 1px solid #f96a00;
					}
				}

				.myswiper2 {
					flex: 1;

					img {
						border-radius: 1.25rem;
						@include respond-to(mb) {
							border-radius: 0;
						}
					}
				}
			}

			.tip {
				display: grid;
				justify-content: center;
				grid-template-columns: repeat(4, 1fr);
				grid-gap: 20px;
				margin-bottom: 20px;
				font-size: 14px;
				padding-left: 0;
				margin-left: 0;
				list-style: none;
				text-align: center;
				white-space: nowrap;

				@include respond-to(mb) {
					grid-gap: 4px;
					font-size: 1rem;
                    margin-bottom: 0;
				}

				b {
					color: #68bd2c;
				}
			}
		}

		.right {
			min-width: 0;
			overflow: hidden;
			background: #ffffff;
			border-radius: 5px;
			border: 1px solid #dcdfe6;

			@include respond-to(mb) {
				order: 2;
				border: none;
				border-radius: 0;
			}

			.stepList ::v-deep {
				display: grid;
				grid-template-columns: 1fr 2.5fr;
				grid-template-areas: "a b";
				grid-gap: 10px; /* 可选：设置行和列之间的间距 */
				padding: 10px;

				@include respond-to(mb) {
					grid-template-columns: repeat(1, 1fr);
					grid-template-areas: none;
					padding: 0 10px 20px;
				}

				@include respond-to(mb) {
					.step-item.mask {
						position: relative;
						z-index: 1001;
						padding: 10px 0;

						.confirmBtnWrap {
							margin-top: 10px;
						}

						.box-border {
							position: absolute;
							left: -10px;
							right: -10px;
							top: 0;
							bottom: 0;
							display: block;
							background-color: #fff;
							z-index: -1;
						}
					}

					.step-size.mask,
					.step-qty.mask {
						padding: 10px;
					}
				}

				.step-size .step-item-params {
					grid-template-columns: repeat(1, 1fr);

                    @include respond-to(mb){
                        grid-template-columns: repeat(2,1fr);
                    }
				}

				.step-plating {
					grid-column: 1 / span 2;

					@include respond-to(mb) {
						grid-column: 1 / span 1;
					}

					.step-item-params {
						display: grid;
						grid-template-columns: repeat(4, 1fr);
						grid-gap: 10px 20px;

						@include respond-to(mb) {
							grid-template-columns: repeat(2, 1fr);
							grid-gap: 5px;
						}

						.param-item {
							min-width: 0;
							display: flex;
							flex-direction: column;
							justify-content: center;
							align-items: center;
							padding: 8px;
							background-color: #fafafa;
							cursor: pointer;
							border-radius: 6px;
							border: 1px solid transparent;
							transition: all 0.3s;

							&.active {
								border-color: $color-primary;
							}

							@media (any-hover: hover) {
								b:hover {
									color: $color-primary;
								}
								&:hover {
									border-color: #eb7e1a;
								}
							}

							img {
								width: 108px;
								object-fit: contain;
								margin: 0 4px;
                                @include respond-to(mb){
                                    width: 90px;
                                }
							}
						}
					}
				}

				.step-attachment {
					grid-column: 1 / span 2;

					@include respond-to(mb) {
						grid-column: 1 / span 1;
					}

					.step-item-params {
						display: grid;
						grid-template-columns: repeat(3, 1fr);
						grid-gap: 10px;

						@include respond-to(mb) {
							grid-template-columns: repeat(2, 1fr);
							grid-gap: 5px;
						}

						.param-item {
							min-width: 0;
							display: flex;
							justify-content: center;
							align-items: center;
							padding: 8px;
							background-color: #fafafa;
							cursor: pointer;
							border-radius: 6px;
							border: 1px solid transparent;
							transition: all 0.3s;

							&.active {
								border-color: $color-primary;
							}

							@media (any-hover: hover) {
								b:hover {
									color: $color-primary;
								}
								&:hover {
									border-color: #eb7e1a;
								}
							}

							img {
								width: 80px;
								object-fit: contain;
								margin: 0 4px;
							}

							.PriceText .tip-text,
							.PriceText .normal-text {
								display: inline;
								text-align: left;
							}
						}
					}
				}

				.step-time-tip {
					margin-bottom: 10px;
					font-size: 14px;
					color: #808080;

                    @include respond-to(mb){
                        font-size: 12px;
                    }
				}

				.step-time {
					grid-column: 1 / span 2;
					min-width: 0;

					.box-border {
						display: none;
					}

					@include respond-to(mb) {
						grid-column: 1 / span 1;
					}

					.step-item-params {
						display: grid;
						grid-template-columns: repeat(3, 1fr);
						grid-gap: 10px;

						@include respond-to(mb) {
							grid-template-columns: repeat(2, 1fr);
							grid-gap: 5px;
						}

						.param-item {
							overflow: hidden;
							position: relative;
							min-width: 0;
							background-color: #fafafa;
							cursor: pointer;
							border-radius: 6px;
							padding: 8px;
							border: 1px solid transparent;
							transition: all 0.3s;

							@include respond-to(mb) {
								padding: 5px;
							}

							&.active {
								@include selectedStyle;
							}

							.des {
								margin-top: 8px;
							}
						}
					}
				}
			}

			.sub-detail {
				display: grid;
				grid-template-columns: repeat(2, 1fr);
				align-items: center;
				border-radius: 10px;
				padding: 10px;
				background: #f6f6f6;
				grid-gap: 20px;
				margin: 10px;

				@include respond-to(mb) {
					grid-template-columns: repeat(1, 1fr);
					grid-gap: 5px;
					border-radius: 0;
					margin: 0;
					background-color: #ebebeb;
				}

				.subtotal-left {
					.sub-item {
						display: flex;
						align-items: center;
						margin-bottom: 8px;

						@include respond-to(mb) {
							justify-content: space-between;
						}

						&:last-child {
							margin-bottom: 0;
						}

						.sub-item-left {
							width: 110px;
							margin-right: 38px;
						}
					}
				}

				.subtotal-right {
					text-align: center;

					.btnGroup {
						display: flex;
						margin-top: 10px;

						button {
							flex: 1;
							margin: 0 5px;
							border-radius: 4px;
							background: transparent;
							border: none;

							b {
								margin-left: 4px;
								color: #ffffff;
							}
						}
					}

					.final-price {
						margin: 0 10px;
						font-size: 24px;
						color: #e6252e;
					}

					.before-discount-price {
						text-decoration: line-through;
					}
				}
			}
		}
	}
}
</style>