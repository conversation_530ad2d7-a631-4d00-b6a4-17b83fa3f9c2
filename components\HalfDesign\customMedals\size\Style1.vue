<template>
	<div class="size">
	    <slot name="stepText"></slot>
		<div class="sizeBox">
			<div class="imgBox">
				<pic :src="selectImg" :class="{ zoom: isBig }" :alt="''"></pic>
				<span class="zoomBox" @click="zoom"><b
						:class="{ 'icon-a-tgsc-add': !isBig, 'icon-a-tgsc-jzhuanhuan': isBig }"></b></span>
			</div>
			<div class="sizeItemBox">
				<div class="sizeItem step-item" :class="{ active: index === selectIndex }"
					@click="selectStep(item, index)" v-for="(item, index) in stepData.productParamList" :key="index">
					<span>{{ item.valueName }}</span>
					<labelText :labelText="item.labelName" :style="{ backgroundColor: item.backgroundColor || '' }">
					</labelText>
					<half-design-check-icon class="absolute-top-right2 check-icon"></half-design-check-icon>
				</div>
			</div>
		</div>
		<div class="errorTip">
			<v-alert dense outlined type="error">
				{{ langSemiCustom.errorTip }}
			</v-alert>
		</div>
	</div>
</template>

<script>
import labelText from '@/components/HalfDesign/customMedals/common/labelText'
export default {
	name: 'size',
	components: { labelText },
	props: {
		stepData: {
			type: Object,
			default: {},
		},
	},
	data() {
		return {
			selectIndex: -1,
			selectItem: null,
			selectImg: '',
			isBig: false,
		}
	},
	watch: {},
	computed: {
		langSemiCustom() {
			return this.$store.getters.lang?.semiCustom;
		},
	},
	methods: {
		selectStep(item, index, status = false) {
			this.selectIndex = index;
			this.selectItem = item;
			this.selectImg = item.imgDetail
			this.$emit("selectStep", {
				type: this.stepData.attributeFlag,
				data: item,
				parent: this.stepData,
				id: this.stepData.id,
				firstSelect: status,
			});
		},
		selectDefault() {
			if (this.stepData.productParamList.length) {
				this.selectStep(this.stepData.productParamList[0], 0, true);
			}
		},
		zoom() {
			this.isBig = !this.isBig
		}
	},
	created() { },
	mounted() {
		this.$Bus.$on("selectDefaultSizeStep", this.selectDefault);
	},
	beforeDestroy() {
		this.$Bus.$off("selectDefaultSizeStep", this.selectDefault);
	}
}
</script>

<style scoped lang="scss">
@import "~assets/css/half.scss";

.size {
	.sizeBox {
		display: flex;
		align-items: stretch;
		gap: 20px;

		.imgBox {
			width: 40%;
			flex-shrink: 0;
			border-radius: 6px;
			border: 1px solid #DBDBDB;
			overflow: hidden;
			display: flex;
			align-items: center;
			justify-content: center;
			padding: 10px;
			position: relative;

			img {
				width: 100%;
				height: 100%;
				object-fit: cover;

				&.zoom {
					transform-origin: center center;
					transform: scale(2);
					transition: all 0.3s ease-in-out
				}
			}

			.zoomBox {
				cursor: pointer;
				position: absolute;
				top: 6px;
				right: 6px;
				color: $color-primary;
			}
		}

		.sizeItemBox {
			flex: 1;
			display: grid;
			grid-template-columns: 1fr 1fr;
			gap: 10px;
			font-size: 16px;
			color: #303133;

			.sizeItem {
				min-width: 0;
				display: flex;
				align-items: center;
				column-gap: 8%;
				padding: 0.4em 1em;
				line-height: initial;
				background: #F4F5F5;
				border-radius: 6px;
				border: 2px solid transparent;
				cursor: pointer;
				position: relative;

				@media (any-hover: hover) {
					&:hover {
						border-width: 2px;
						border-color: $color-primary;
					}
				}

				&.active {
					border: 2px solid $color-primary;

					.check-icon {
						display: flex;
					}
				}

				.check-icon {
					display: none;
				}

				span {
					flex-shrink: 0;
				}
			}
		}

		@media screen and (min-width: 1000px) and (max-width: 1500px) {
			.labelText {
				font-size: 12px;
			}
		}

		@include respond-to(mb) {
			gap: 4px;

			.imgBox {
				width: 30%;
				padding: 2px;
			}

			.sizeItemBox {
				gap: 4px;
				font-size: 12px;

				.sizeItem {
					column-gap: 4%;
					padding: 0.4em 0.4em;
				}

				.labelText {
					font-size: 12px;
					padding: .1em .4em;
				}
			}
		}
	}

}
</style>
