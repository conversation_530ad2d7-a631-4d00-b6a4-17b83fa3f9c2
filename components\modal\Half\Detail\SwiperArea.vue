<script>
import VideoPlayer from "@/components/HalfDesign/VideoPlayer.vue";
import { isImageType, isVideoType } from "@/utils/utils";
import { pdt360DegViewer } from "@/assets/js/pdt360DegViewer";

export default {
	props: {
		productInfo: {
			type: Object,
		},
		mobileCanvasWidth: {
			type: Object,
		},
		editStatus: {
			type: Boolean,
		},
		imgList: {
			type: Array,
		},
		loadTempData: {
			type: Boolean,
		},
		firstImg: {
			type: String,
		},
		isMask: {
			type: Boolean,
		},
		isCoins: {
			type: Boolean,
		},
		isPens: {
			type: Boolean,
		},
		isLuggagetags: {
			type: <PERSON>olean,
		},
		currentStep: {
			type: Number,
		},
		isBig: {
			type: Boolean,
		},
		showThreeD: {
			type: Boolean,
		},
		showEditBtn: {
			type: Boolean,
			default: true
		},
		showDesignTool: {
			type: Boolean,
			default: true
		},
		maskContent: {
			type: String,
		},
		noShowBack: {
			type: Boolean,
			default: false
		}
	},
	data() {
		return {
			isInit360: false
		}
	},
	components: { VideoPlayer },
	watch: {
		editStatus(val) {
			if (val) {
				this.$emit("toggleThreeD", false);
			}
		},
	},
	computed: {
		isMobile() {
			return this.$store.state.device === "mb";
		},
		langSemiCustom() {
			return this.$store.getters.lang?.semiCustom;
		},
		hasRotateImg() {
			if (!this.productInfo.rotateImg) {
				return false
			}
			return JSON.parse(this.productInfo.rotateImg).length
		}
	},
	methods: {
		initThreeD() {
			this.$emit("toggleThreeD", !this.showThreeD);
			if (!this.isInit360) {
				let picList = (JSON.parse(this.productInfo.rotateImg)).map(i => i.url);
				pdt360DegViewer("product360Preview", picList, true, true); //autoPlay
				this.isInit360 = true;
			}
		},
		isImageType,
		isVideoType,
		getVideoOptions(item, type) {
			if (type === 1) {
				return {
					autoplay: true,
					controls: true,
					muted: true,
					loop: true,
					fill: true,
					preload: "auto",
					poster: item.imgUrl,
					sources: [
						{
							src: item.url,
							type: "video/mp4",
						},
					],
				};
			} else if (type === 4) {
				return {
					autoplay: false,
					controls: false,
					muted: true,
					loop: true,
					fill: true,
					sources: [
						{
							src: path,
							type: "video/mp4",
						},
					],
				};
			}
		},
		toPrev() {
			this.$emit("toPrev");
		},
		zoom() {
			this.$emit("zoom");
		},
		beginEdit() {
			this.$emit("beginEdit");
		},
		closeEdit() {
			this.$emit("closeEdit");
		},
	},
	mounted() {
		let _this = this;
		this.myswiper1 = new Swiper(this.$refs.swiper1, {
			slidesPerView: "auto",
			spaceBetween: 20,
			watchSlidesVisibility: true, //防止不可点击
			grabCursor: true,
			on: {
				click: function () {
					_this.closeEdit();
				},
			},
		});
		this.myswiper2 = new Swiper(this.$refs.swiper2, {
			slidesPerView: 1,
			spaceBetween: 10,
			autoplay: false,
			grabCursor: true,
			observer: true,
			observeParents: true,
			navigation: {
				nextEl: ".myswiper2 .swiper-button-next",
				prevEl: ".myswiper2 .swiper-button-prev",
			},
			thumbs: {
				swiper: this.myswiper1,
			},
			on: {
				slideChangeTransitionEnd: function (value) {
					_this.imgList.forEach(function (v, i) {
						if (i === value.activeIndex) {
							if (_this.$refs["videoPlayer" + i]) {
								_this.$refs["videoPlayer" + i][0].player.play();
							}
						} else {
							if (_this.$refs["videoPlayer" + i]) {
								_this.$refs["videoPlayer" + i][0].player.pause();
							}
						}
					});
				},
			},
		});
	},
};
</script>

<template>
	<div class="swiper-container-wrap">
		<div class="top" :style="mobileCanvasWidth">
			<div class="swiper myswiper2" ref="swiper2" v-show="!editStatus">
				<div class="swiper-wrapper">
					<div class="swiper-slide" v-for="(item, index) in imgList" :key="index">
						<div class="smallImg" v-if="isImageType(item.url)">
							<client-only>
								<template v-if="isMobile">
									<img :src="item.url" :alt="item.alt" style="width: 100%; height: 100%" />
								</template>
								<template v-else>
									<PicZoom :url="item.url" :alt="item.alt" :width="300" :scale="2" type="round">
									</PicZoom>
								</template>
							</client-only>
						</div>
						<VideoPlayer class="swiper-no-swiping" v-else :ref="'videoPlayer' + index" disabled-mouse
							:options="getVideoOptions(item, 1)"></VideoPlayer>
					</div>
				</div>
				<div class="swiper-button-prev"></div>
				<div class="swiper-button-next"></div>
			</div>
			<div id="designBody" class="canvasWrap" :class="{ disabled: loadTempData }" v-show="editStatus">
				<div class="loadProgress" v-show="loadTempData">
					<v-progress-circular indeterminate color="primary"></v-progress-circular>
				</div>
				<div v-show="!firstImg" style="position: relative;">
					<div class="areaMask" v-show="isMask">
						{{ maskContent }}
					</div>
					<canvas id="fabricCanvas"></canvas>
				</div>
				<div v-show="firstImg" class="firstImg">
					<img :src="firstImg" alt="first" />
				</div>
			</div>
			<div id="product360Preview" v-show="showThreeD"></div>
			<div class="threeD" :class="{ active: showThreeD }" @click="initThreeD" v-if="hasRotateImg">
				<img src="https://static-oss.gs-souvenir.com/web/quoteManage/20241024/%E5%BD%A2%E7%8A%B6_629_2058c2t8sQ.png"
					alt="360" title="360" />
			</div>
		</div>
		<div class="backBox" v-show="isMobile && !noShowBack">
			<div class="backForword" :disabled="currentStep === 1" @click="toPrev">
				<span><b class="icon-a-lrgl-jt1zhuanhuan"></b></span>
				{{ langSemiCustom.back }}
			</div>
			<div v-show="editStatus" class="tapBox" @click="zoom">
				<span><b :class="!isBig ? 'icon-a-tgsc-add' : 'icon-a-tgsc-jzhuanhuan'"></b></span>
				{{ langSemiCustom.topToZoom }}
			</div>
			<div v-show="!editStatus"></div>
		</div>
		<div class="borderBox"></div>
		<div class="designTool" :class="{ active: editStatus, mbNoHidden: isCoins }" @click="beginEdit('edit')">
			<div v-if="productInfo.isDevise !== 1 && showDesignTool">
				<v-icon large>mdi-square-edit-outline</v-icon>
				{{ langSemiCustom.professional }}
			</div>
			<span v-else @click.stop style="height: 36px">
				<span v-show="isCoins" class="coinsNote">{{ langSemiCustom.coins.Note }}: </span><span v-show="isCoins"
					style="color: #333;">
					{{ langSemiCustom.coins.noteText }}</span>
			</span>
		</div>
		<div class="bottom">
			<div v-if="productInfo.isDevise !== 1 && showEditBtn" class="edit" :class="{ active: editStatus }"
				@click="beginEdit('edit')">
				<b class="icon-DESIGN"></b>
			</div>
			<div class="swiper myswiper1" :class="{ isEdit: editStatus || showThreeD }" ref="swiper1">
				<div class="swiper-wrapper">
					<div class="swiper-slide" v-for="(item, index) in imgList" :key="index"
						@click="$emit('toggleThreeD', false)">
						<template v-if="isImageType(item.url)">
							<img :src="item.url" :alt="item.alt" :title="item.alt" />
						</template>
						<template v-else-if="isVideoType(item.url)">
							<img v-show="item.imgUrl" :src="item.imgUrl" :alt="item.alt || 'videoMask'"
								:title="item.alt || 'videoMask'" />
						</template>
					</div>
				</div>
			</div>
			<div class="threeD" :class="{ active: showThreeD }" @click="initThreeD" v-if="hasRotateImg">
				<img src="https://static-oss.gs-souvenir.com/web/quoteManage/20241024/%E5%BD%A2%E7%8A%B6_629_2058c2t8sQ.png"
					alt="360" title="360" />
			</div>
		</div>
		<div id="dummy" style="display: none"></div>
	</div>
</template>

<style scoped lang="scss">
#product360Preview {
	position: absolute;
	display: flex;
	align-items: center;
	inset: 0;
	z-index: 1000;
	cursor: e-resize;
	background: #ffffff url("https://static-oss.gs-souvenir.com/web/quoteManage/20241024/%E7%BB%84_872_2058SW6WeS.png") (center bottom 20px) / contain no-repeat;

	::v-deep img {
		width: 100%;
		height: 100%;
		object-fit: contain;
	}
}

.swiper.myswiper2 {
	--swiper-navigation-color: #ffffff;
	/* 单独设置按钮颜色 */
	--swiper-navigation-size: 20px;

	/* 设置按钮大小 */
	.swiper-button-next,
	.swiper-button-prev {
		width: 46px;
		height: 46px;
		background: #000000;
		opacity: 0.4;
		border-radius: 50%;
		transform: translateY(-50%);
		margin-top: 0;
	}
}

.swiper-container-wrap {
	display: flex;
	flex-direction: column;
	user-select: none;
	overflow: hidden;

	.top {
		position: relative;
		height: 500px;

		.canvasWrap {
			display: flex;
			justify-content: center;
			align-items: center;
			position: absolute;
			top: 0;
			right: 0;
			bottom: 0;
			left: 0;
			height: 100%;
			z-index: 1000;

			.loadProgress {
				display: flex;
				justify-content: center;
				align-items: center;
				position: absolute;
				left: 0;
				top: 0;
				right: 0;
				bottom: 0;
				z-index: 1000;
			}

			.areaMask {
				position: absolute;
				top: 50%;
				transform: translateY(-50%);
				width: 500px;
				background-color: #2c2c2c;
				opacity: 0.5;
				padding: 20px;
				color: #fff;
				font-size: 16px;
				z-index: 99;
				word-break: break-word;

				@include respond-to(mb) {
					width: 341px;
				}
			}

			.firstImg {
				width: 500px;
				height: 500px;
				position: relative;
				img{
					width: 100%;
					height: 100%;
					object-fit: contain;
				}

				@include respond-to(mb) {
					width: 341px;
					height: 341px;
				}
			}
		}

		b {
			font-size: 30px;
			cursor: pointer;
		}

		.myswiper2 {
			height: 100%;

			.zoom {
				position: absolute;
				top: 0;
				right: 0;
				cursor: pointer;
				z-index: 100;

				b {
				}

				@include respond-to(mb) {
					width: 341px;
					height: 341px;
				}
			}
		}

		b {
			font-size: 30px;
			cursor: pointer;
		}

		.myswiper2 {
			height: 100%;

			.zoom {
				position: absolute;
				top: 0;
				right: 0;
				cursor: pointer;
				z-index: 100;

				b {
					font-size: 22px;
					margin-right: 4px;
				}

				&:hover {
					b {
						color: $color-primary;
					}
				}
			}

			.swiper-slide {
				display: flex;
				justify-content: center;
				align-items: center;

				.smallImg {
					display: flex;
					align-items: center;
					justify-content: center;
					position: relative;
					width: 500px;
					height: 100%;
					text-align: center;
					position: relative;

				}
			}

			img {
				max-width: 100%;
				max-height: 100%;
				object-fit: contain;
			}
		}

		.threeD {
			display: none;
			position: absolute;
			top: 10px;
			right: 10px;
			width: 40px;
			height: 40px;
			padding: 2px;
			border: 1px solid #f5f5f5;
			border-radius: 4px;
			cursor: pointer;
			z-index: 1001;

			@include respond-to(mb) {
				display: block;
			}

			img {
				width: 100%;
				height: 100%;
				object-fit: contain;
			}
		}

		.threeD.active {
			border-color: $color-primary;
		}
	}

	.designTool {
		display: flex;
		justify-content: center;
		align-items: center;
		font-size: 16px;
		cursor: pointer;
		margin: 10px 0 20px;

		.coinsNote {
			color: $color-primary;
		}

		&.active {
			color: $color-primary;

			.v-icon {
				color: $color-primary;
			}
		}
	}

	.bottom.isEdit {
		.swiper-slide {
			&.swiper-slide-thumb-active {
				border-color: #eeeeee;
			}
		}
	}

	.bottom {
		display: flex;
		align-items: center;
		justify-content: flex-start;

		.edit {
			flex-shrink: 0;
			position: relative;
			width: 88px;
			height: 88px;
			background: rgba(0, 0, 0, 0.4);
			border: 2px solid #f5f5f5;
			border-radius: 10px;
			margin-right: 20px;
			cursor: pointer;

			b {
				position: absolute;
				left: 50%;
				top: 50%;
				transform: translate(-50%, -50%);
				color: #ffffff;
				font-size: 32px;
			}
		}

		.edit.active {
			border-color: $color-primary;
		}

		.edit.disabledDesign {
			cursor: not-allowed;
		}

		.threeD {
			flex-shrink: 0;
			position: relative;
			width: 88px;
			height: 88px;
			padding: 10px;
			border: 2px solid #f5f5f5;
			margin-left: 20px;
			border-radius: 10px;
			cursor: pointer;

			img {
				width: 100%;
				height: 100%;
				object-fit: contain;
			}
		}

		.threeD.active {
			border-color: $color-primary;
		}

		.swiper {
			margin: 0;
		}
	}

	.myswiper1 {
		.swiper-slide {
			display: flex;
			justify-content: center;
			align-items: center;
			overflow: hidden;
			width: 88px;
			height: 88px;
			border: 2px solid #eeeeee;
			border-radius: 10px;
			cursor: pointer;

			&:last-child {
				margin-right: 0 !important;
			}

			&.swiper-slide-thumb-active {
				border: 2px solid $color-primary;
			}
		}

		&.isEdit {
			.swiper-slide.swiper-slide-thumb-active {
				border: 2px solid #eeeeee;
			}
		}

		img {
			width: 100%;
			height: 100%;
			object-fit: cover;
		}

		.playBtn {
			display: flex;
			justify-content: center;
			align-items: center;
			position: absolute;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
			width: 30px;
			height: 30px;
			background-color: rgba(0, 0, 0, 0.7);
			border-radius: 50%;

			svg {
				fill: #ffffff;
			}
		}
	}
}

@include respond-to(mb) {
	.swiper-container-wrap {

		.top {
			width: 100%;
			margin: 0 auto;
			height: 350px;

			.myswiper2 {
				height: 100%;

				.zoom {
					position: absolute;
					top: 0;
					right: 0;
					cursor: pointer;
					z-index: 100;

					b {
						font-size: 22px;
						margin-right: 4px;
					}

					&:hover {
						b {
							color: $color-primary;
						}
					}
				}

				.swiper-slide {
					display: flex;
					justify-content: center;
					align-items: center;

					.smallImg {
						position: relative;
						width: 500px;
						height: 100%;
						text-align: center;
					}
				}

				img {
					max-width: 100%;
					max-height: 100%;
					object-fit: cover;
				}
			}
		}

		.backBox {
			margin-top: 10px;
			display: flex;
			align-items: center;
			justify-content: space-between;
			font-weight: 400;
			font-size: 14px;
			color: #333333;

			.backForword[disabled] {
				color: #CCCCCC;
			}

			b {
				font-size: 18px;
				margin-right: 4px;
			}

			div {
				padding: 6px 0;
			}
		}

		.borderBox {
			margin: 6px -1em 0;
			height: 1px;
			width: calc(100% + 2em);
			overflow: hidden;
			background-color: #f2f2f2;
			box-shadow: 0 2px 8px 2px #f2f2f2;
		}

		.designTool {
			display: none;
			font-size: 14px;

			&.mbNoHidden {
				display: block;
			}
		}

		.bottom {
			display: none;
		}
	}
}

//.absoulteText {
//	position: absolute;
//	left: 0;
//	bottom: 0;
//	width: 100%;
//	padding: 6px 10%;
//	font-size: 12px;
//	font-weight: 500;
//	color: #3B393C;
//	text-align: center;
//	background-color: rgba(159, 156, 156, 0.6);
//	z-index: 2;
//
//	@include respond-to(mb) {
//		font-size: 12px;
//		font-size: 10px;
//		padding: 2px 0;
//
//		&.noShow {
//			display: none;
//		}
//
//		&.canvasText {
//			bottom: -10px;
//		}
//	}
//}
</style>