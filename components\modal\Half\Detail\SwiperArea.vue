<script>
import VideoPlayer from "@/components/HalfDesign/VideoPlayer.vue";
import { isImageType, isVideoType } from "@/utils/utils";
import { pdt360DegViewer } from "@/assets/js/pdt360DegViewer";

export default {
	inject: ["getCurrentAreaParam"],
	props: {
		isBig: {
			type: Boolean,
		},
		productInfo: {
			type: Object,
		},
		mobileCanvasWidth: {
			type: Object,
		},
		editStatus: {
			type: Boolean,
		},
		imgList: {
			type: Array,
		},
		loadTempData: {
			type: Boolean,
		},
		firstImg: {
			type: String,
		},
		isMask: {
			type: Boolean,
		},
		currentStep: {
			type: Number,
		},
		showThreeD: {
			type: Boolean,
		},
		maskContent: {
			type: String,
		},
		isCufflinks: {
			type: Boolean,
			default: false,
		},
		colorParams: {
			type: Object,
			default: () => ({}),
		},
	},
	data() {
		return {
			currentIndex: 0,
			currentIndex2: 0,
			newImgList: [],
			isInit360: false,
			showColorSwiper: false,
			myswiper1: null,
			myswiper2: null,
			myswiper3: null,
			myswiper4: null,
			startX: null,
			isNormalSwiperEnd: false,
			isBigSwiperStart: false,
		};
	},
	components: { VideoPlayer },
	watch: {
		async imgList(val) {
			let list = JSON.parse(JSON.stringify(val));
			if (this.hasRotateImg) {
				list.unshift({
					url: "https://static-oss.gs-souvenir.com/web/quoteManage/20241024/%E5%BD%A2%E7%8A%B6_629_2058c2t8sQ.png",
					alt: "360",
					type: "is360",
				});
				this.initThreeD();
			}
			this.newImgList = list;
			await this.$nextTick();
			this.initSwiper();
		},
		async colorImgList(val) {
			if (this.isMobile) {
				this.$nextTick(() => {
					this.initSwiper2();
				});
			}
		},
		editStatus(val) {
			if (val) {
				this.$emit("update:showThreeD", false);
			}
		},
	},
	computed: {
		isMobile() {
			return this.$store.state.device === "mb";
		},
		langSemiCustom() {
			return this.$store.getters.lang?.semiCustom;
		},
		hasRotateImg() {
			if (!this.productInfo.rotateImg) {
				return false;
			}
			return JSON.parse(this.productInfo.rotateImg).length;
		},
		currentArea() {
			return this.getCurrentAreaParam();
		},
		colorImgList() {
			if (this.isCufflinks && this.hasColorSwiper) {
				return this.colorParams?.productParamList?.map((item) => {
					return { url: item.sizeImg };
				});
			}
			if (!this.currentArea) return [];
			const colorUrlList = [];
			const areaId = this.currentArea.attributeValueId;
			if (Array.isArray(this.colorParams?.productParamList)) {
				this.colorParams.productParamList.forEach((item) => {
					if (!item.imgJson) return;
					try {
						const imgData = JSON.parse(item.imgJson);
						if (imgData && Array.isArray(imgData)) {
							imgData.forEach((img) => {
								if (img.attributeValueId === areaId) {
									colorUrlList.push({ url: img.url });
								}
							});
						}
					} catch (e) {
						console.error("JSON 解析失败_colorSwiper:", item.imgJson);
					}
				});
			}

			return colorUrlList;
		},
		hasColorSwiper() {
			return Object.keys(this.colorParams).length > 0 && this.colorImgList.length > 0;
		},
	},
	methods: {
		zoom() {
			this.$emit("zoom");
		},
		initSwiper() {
			if(!this.imgList.length) return
			let _this = this;
			let num = "auto";
			if (this.isMobile) {
				if (this.hasColorSwiper) {
					num = 1;
				}
				if (this.hasColorSwiper && this.hasRotateImg) {
					num = 2;
				}
			}

			this.myswiper1 = new Swiper(this.$refs.swiper1, {
				slidesPerView: num,
				direction: "vertical",
				spaceBetween: 6,
				watchSlidesVisibility: true,
				watchSlidesProgress: true,
				grabCursor: true,
				breakpoints: {
					320: {
						direction: "horizontal",
					},
					750: {
						direction: "horizontal",
					},
					1001: {
						direction: "vertical",
					},
				},
				on: {
					click: (value) => {
						if (_this.showColorSwiper) _this.showColorSwiper = false;
						let type = _this.newImgList[value.clickedIndex]?.type;
						let pdtLeft = document.querySelector(".pdt-left");
						pdtLeft.classList.remove("isSticky");
						_this.$emit("update:editStatus", false);
						_this.$emit("update:showThreeD", false);
						if (type === "is360") {
							_this.initThreeD();
						}
					},
				},
			});
			this.myswiper2 = new Swiper(this.$refs.swiper2, {
				slidesPerView: 1,
				spaceBetween: 10,
				autoplay: false,
				grabCursor: true,
				observer: true,
				observeParents: true,
				navigation: {
					nextEl: ".normalSwiper.swiper-button-next",
					prevEl: ".normalSwiper.swiper-button-prev",
				},
				thumbs: {
					swiper: this.myswiper1,
				},
				on: {
					touchStart: function (swiper, event) {
						if (!_this.isMobile) return;
						if (event.touches.length > 0) {
							_this.startX = event.touches[0].clientX;
						}
					},
					touchMove: function (swiper, event) {
						if (!_this.isMobile) return;
						if (!_this.startX || !swiper.isEnd) return;
						const currentX = event.touches[0].clientX;
						const diffX = _this.startX - currentX;
						if (diffX > 15) {
							if (!_this.showColorSwiper && _this.hasColorSwiper) {
								setTimeout(() => {
									_this.showColorSwiper = true;
									_this.syncThumbs(_this.myswiper4, _this.myswiper3, 0);
								}, 500);
							}
							_this.startX = null;
						}
					},
					touchEnd: function (swiper, event) {
						if (!_this.isMobile) return;
						_this.startX = null;
					},
					slideChangeTransitionEnd: function (value) {
						_this.isNormalSwiperEnd = false;
						if (value.isEnd) _this.isNormalSwiperEnd = true;
						if (_this.showColorSwiper) _this.showColorSwiper = false;
						_this.currentIndex = value.activeIndex;
						let type = _this.newImgList[value.activeIndex]?.type;
						_this.$emit("update:editStatus", false);
						_this.$emit("update:showThreeD", false);
						if (type === "is360") {
							_this.initThreeD();
						}
						_this.newImgList.forEach(function (v, i) {
							if (i === value.activeIndex) {
								if (_this.$refs["videoPlayer" + i]) {
									_this.$refs["videoPlayer" + i][0].player.play();
								}
							} else {
								if (_this.$refs["videoPlayer" + i]) {
									_this.$refs["videoPlayer" + i][0].player.pause();
								}
							}
						});
					},
				},
			});
		},
		initSwiper2() {
			if(!this.colorImgList.length) return
			let _this = this;
			let num = 4.2;
			if (this.isMobile) {
				if (this.hasRotateImg) {
					num = 3.2;
				}
			}
			this.myswiper3 = new Swiper(this.$refs.swiper3, {
				slidesPerView: num,
				direction: "horizontal",
				spaceBetween: 6,
				watchSlidesVisibility: true,
				watchSlidesProgress: true,
				grabCursor: true,
				on: {
					click: (value) => {
						if (!_this.showColorSwiper) {
							_this.showColorSwiper = true;
							value.update();
							value.updateProgress();
							value.updateSlidesClasses();
						}
						let pdtLeft = document.querySelector(".pdt-left");
						pdtLeft.classList.remove("isSticky");
						_this.$emit("update:editStatus", false);
					},
				},
			});
			this.myswiper4 = new Swiper(this.$refs.swiper4, {
				slidesPerView: 1,
				spaceBetween: 10,
				autoplay: false,
				grabCursor: true,
				observer: true,
				observeParents: true,
				navigation: {
					nextEl: ".bigColorSwiper.swiper-button-next",
					prevEl: ".bigColorSwiper.swiper-button-prev",
				},
				thumbs: {
					swiper: this.myswiper3,
				},
				on: {
					touchStart: function (swiper, event) {
						if (!_this.isMobile) return;
						if (event.touches.length > 0) {
							_this.startX = event.touches[0].clientX;
						}
					},
					touchMove: function (swiper, event) {
						if (!_this.isMobile) return;
						if (!_this.startX || swiper.activeIndex != 0) return;
						const currentX = event.touches[0].clientX;
						const diffX = currentX - _this.startX;
						if (diffX > 15) {
							if (_this.showColorSwiper && _this.hasColorSwiper) {
								setTimeout(() => {
									_this.showColorSwiper = false;
									_this.syncThumbs(_this.myswiper2, _this.myswiper1, _this.imgList.length - 1);
								}, 500);
							}
							_this.startX = null;
						}
					},
					touchEnd: function (swiper, event) {
						if (!_this.isMobile) return;
						_this.startX = null;
					},
					slideChangeTransitionEnd: function (value) {
						if (!_this.showColorSwiper) _this.showColorSwiper = true;
						_this.currentIndex2 = value.activeIndex;
						_this.isBigSwiperStart = false;
						if (value.activeIndex == 0) {
							_this.isBigSwiperStart = true;
						}
						_this.syncThumbs(_this.myswiper4, _this.myswiper3, value.activeIndex);
						_this.$emit("update:editStatus", false);
						_this.colorImgList.forEach(function (_, i) {
							if (i === value.activeIndex) {
								if (_this.$refs["videoPlayer" + i]) {
									_this.$refs["videoPlayer" + i][0].player.play();
								}
							} else {
								if (_this.$refs["videoPlayer" + i]) {
									_this.$refs["videoPlayer" + i][0].player.pause();
								}
							}
						});
					},
				},
			});
		},
		initThreeD() {
			this.$emit("update:showThreeD", true);
			if (!this.isInit360) {
				let picList = JSON.parse(this.productInfo.rotateImg).map((i) => i.url);
				pdt360DegViewer("product360Preview", picList, true, true); //autoPlay
				this.isInit360 = true;
			}
		},
		isImageType,
		isVideoType,
		getVideoOptions(item, type) {
			if (type === 1) {
				return {
					autoplay: true,
					controls: true,
					muted: true,
					loop: true,
					fill: true,
					preload: "auto",
					poster: item.imgUrl,
					sources: [
						{
							src: item.url,
							type: "video/mp4",
						},
					],
				};
			} else if (type === 4) {
				return {
					autoplay: false,
					controls: false,
					muted: true,
					loop: true,
					fill: true,
					sources: [
						{
							src: path,
							type: "video/mp4",
						},
					],
				};
			}
		},
		beginEdit() {
			this.$emit("beginEdit");
		},
		openDetail() {
			this.$emit("openDetail");
		},
		syncThumbs(mainSwiper, thumbSwiper, index) {
			if (!mainSwiper || !thumbSwiper) return;
			mainSwiper.slideTo(index, 0, false);
			thumbSwiper.slideTo(index, 0, false);
			setTimeout(() => {
				mainSwiper.update();
				thumbSwiper.update();
				mainSwiper.updateSlidesClasses();
				thumbSwiper.updateSlidesClasses();
			}, 50);
		},
	},
	mounted() {
		if (this.isMobile) {
			try {
				document
					.querySelector(".normalSwiperBox")
					.querySelector(".swiper-button-next.normalSwiper")
					.addEventListener("click", () => {
						if (this.isNormalSwiperEnd) {
							if (!this.showColorSwiper && this.hasColorSwiper) {
								setTimeout(() => {
									this.showColorSwiper = true;
									this.$nextTick(() => {
										this.syncThumbs(this.myswiper4, this.myswiper3, 0);
									});
								}, 500);
							}
						}
					});
				document
					.querySelector(".bigColorSwiperBox")
					.querySelector(".swiper-button-prev.bigColorSwiper")
					.addEventListener("click", () => {
						if (this.isBigSwiperStart) {
							if (this.showColorSwiper && this.hasColorSwiper) {
								setTimeout(() => {
									this.showColorSwiper = false;
									this.$nextTick(() => {
										this.syncThumbs(this.myswiper2, this.myswiper1, this.imgList.length - 1);
									});
								}, 500);
							}
						}
					});
			} catch (error) {
				console.log(error, "swiper");
			}
		}
	},
};
</script>

<template>
	<div class="swiper-container-wrap">
		<div class="thumbImgWrap" :class="{ isEdit: editStatus }">
			<div class="swiper myswiper1" :class="{ hasRotateImg: hasRotateImg, noColorSwiper: !hasColorSwiper }" ref="swiper1">
				<div class="swiper-wrapper" :class="{ showColorSwiper: !showColorSwiper }">
					<div class="swiper-slide" v-for="(item, index) in newImgList" :key="index">
						<template v-if="item.type === 'is360'">
							<div class="threeD">
								<img src="https://static-oss.gs-souvenir.com/web/quoteManage/20241024/%E5%BD%A2%E7%8A%B6_629_2058c2t8sQ.png" alt="360" title="360" />
							</div>
						</template>
						<template v-else-if="isImageType(item.url)">
							<img :src="item.url" :alt="item.alt" :title="item.alt" />
						</template>
						<template v-else-if="isVideoType(item.url)">
							<img v-show="item.imgUrl" :src="item.imgUrl" :alt="item.alt || 'videoMask'" :title="item.alt || 'videoMask'" />
							<div class="play-icon">
								<svg t="1747451684935" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="28740" width="20" height="20">
									<path d="M512 512m-512 0a44 44 0 1 0 1024 0 44 44 0 1 0-1024 0Z" fill="#000000" opacity="0.5" p-id="28741"></path>
									<path d="M746.327273 534.035416 418.181818 723.708144C401.890909 733.017235 380.945455 721.380871 380.945455 701.599053L380.945455 322.253598C380.945455 303.635416 401.890909 290.835416 418.181818 300.144507L746.327273 489.817235C763.781818 500.289962 763.781818 523.562689 746.327273 534.035416L746.327273 534.035416Z" fill="#FFFFFF" p-id="28742"></path>
								</svg>
							</div>
						</template>
					</div>
				</div>
			</div>
			<div v-if="productInfo.isDevise !== 1" class="edit" :class="{ active: editStatus, hasColorSwiper: hasColorSwiper }" @click="openDetail">
				<!-- <b class="icon-DESIGN"></b>
				<span>Design Tool</span> -->
				<span>Options</span>
			</div>
			<div class="swiper myswiper1 colorSwiper" ref="swiper3" v-show="isMobile && hasColorSwiper">
				<div class="swiper-wrapper" :class="{ showColorSwiper: showColorSwiper }">
					<div class="swiper-slide" v-for="(item, index) in colorImgList" :key="index">
						<template v-if="isImageType(item.url)">
							<img :src="item.url" :alt="item.alt" :title="item.alt" />
						</template>
						<template v-else-if="isVideoType(item.url)">
							<img v-show="item.imgUrl" :src="item.imgUrl" :alt="item.alt || 'videoMask'" :title="item.alt || 'videoMask'" />
							<div class="play-icon">
								<svg t="1747451684935" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="28740" width="20" height="20">
									<path d="M512 512m-512 0a44 44 0 1 0 1024 0 44 44 0 1 0-1024 0Z" fill="#000000" opacity="0.5" p-id="28741"></path>
									<path d="M746.327273 534.035416 418.181818 723.708144C401.890909 733.017235 380.945455 721.380871 380.945455 701.599053L380.945455 322.253598C380.945455 303.635416 401.890909 290.835416 418.181818 300.144507L746.327273 489.817235C763.781818 500.289962 763.781818 523.562689 746.327273 534.035416L746.327273 534.035416Z" fill="#FFFFFF" p-id="28742"></path>
								</svg>
							</div>
						</template>
					</div>
				</div>
			</div>
			<b class="arrowIcon icon-bps-xiaozhankai" v-show="isMobile && hasColorSwiper" @click="openDetail"></b>
		</div>
		<div class="previewImgWrap" :style="mobileCanvasWidth">
			<div class="myswiper2-wrap normalSwiperBox" v-show="!showColorSwiper">
				<div class="swiper myswiper2" ref="swiper2">
					<div class="swiper-wrapper">
						<div class="swiper-slide" v-for="(item, index) in newImgList" :key="index">
							<div v-if="item.type === 'is360'"></div>
							<div class="smallImg" v-else-if="isImageType(item.url)">
								<client-only>
									<template v-if="isMobile">
										<img :src="item.url" :alt="item.alt" style="width: 100%; height: 100%" />
									</template>
									<template v-else>
										<PicZoom :url="item.url" :alt="item.alt" :width="300" :scale="2" type="round"></PicZoom>
									</template>
								</client-only>
							</div>
							<VideoPlayer class="swiper-no-swiping" v-else :ref="'videoPlayer' + index" disabled-mouse :options="getVideoOptions(item, 1)"></VideoPlayer>
						</div>
					</div>
					<div class="custom-swiper-pagination" v-if="isMobile">Gallery {{ currentIndex + 1 }}/{{ newImgList.length }}</div>
				</div>
				<div class="normalSwiper swiper-button-prev" v-show="!editStatus && !showThreeD && !showColorSwiper"></div>
				<div class="normalSwiper swiper-button-next" :class="{ hasColorSwiper: hasColorSwiper }" v-show="!editStatus && !showThreeD && !showColorSwiper"></div>
			</div>
			<div class="myswiper2-wrap bigColorSwiperBox" v-show="isMobile && showColorSwiper && hasColorSwiper">
				<div class="swiper myswiper2" ref="swiper4">
					<div class="swiper-wrapper">
						<div class="swiper-slide" v-for="(item, index) in colorImgList" :key="index">
							<div class="smallImg" v-if="isImageType(item.url)">
								<client-only>
									<template v-if="isMobile">
										<img :src="item.url" :alt="item.alt" style="width: 100%; height: 100%" />
									</template>
									<template v-else>
										<PicZoom :url="item.url" :alt="item.alt" :width="300" :scale="2" type="round"></PicZoom>
									</template>
								</client-only>
							</div>
							<VideoPlayer class="swiper-no-swiping" v-else :ref="'videoPlayer' + index" disabled-mouse :options="getVideoOptions(item, 1)"></VideoPlayer>
						</div>
					</div>
					<!-- <div class="custom-swiper-pagination" v-if="isMobile">Gallery {{ currentIndex2 + 1 }}/{{ colorImgList.length }}</div> -->
				</div>
				<div class="bigColorSwiper swiper-button-prev" v-show="!editStatus && !showThreeD && showColorSwiper"></div>
				<div class="bigColorSwiper swiper-button-next" v-show="!editStatus && !showThreeD && showColorSwiper"></div>
			</div>
			<div id="designBody" class="canvasWrap" :class="{ disabled: loadTempData }" v-show="editStatus">
				<div class="loadProgress" v-show="loadTempData">
					<v-progress-circular indeterminate color="primary"></v-progress-circular>
				</div>
				<div class="designBox" v-show="!firstImg">
					<div class="areaMask" v-show="isMask">
						{{ maskContent }}
					</div>
					<canvas v-if="!isMobile" id="fabricCanvas"></canvas>
				</div>
				<div v-show="firstImg" class="firstImg">
					<img :src="firstImg" alt="first" />
				</div>
			</div>
			<div id="product360Preview" v-show="showThreeD"></div>
			<div v-show="editStatus" class="tap-box extend-click-area" @click="zoom">
				<span><b :class="!isBig ? 'icon-a-tgsc-add' : 'icon-a-tgsc-jzhuanhuan'"></b></span>
				{{ langSemiCustom.topToZoom }}
			</div>
		</div>
		<div id="dummy" style="display: none"></div>
	</div>
</template>

<style scoped lang="scss">
#product360Preview {
	position: absolute;
	display: flex;
	align-items: center;
	inset: 0;
	z-index: 1000;
	cursor: e-resize;
	background: #ffffff url("https://static-oss.gs-souvenir.com/web/quoteManage/20241024/%E7%BB%84_872_2058SW6WeS.png") (center bottom 20px) / contain no-repeat;

	::v-deep img {
		width: 100%;
		height: 100%;
		object-fit: contain;
	}
}

.myswiper2-wrap {
	position: relative;
	height: 100%;
	--swiper-navigation-color: #ffffff;
	/* 单独设置按钮颜色 */
	--swiper-navigation-size: 20px;

	/* 设置按钮大小 */
	.swiper-button-next,
	.swiper-button-prev {
		width: 2.55em;
		height: 2.55em;
		background: rgba(0, 0, 0, 0.5);
		border-radius: 50%;
		transform: translateY(-50%);
		margin-top: 0;

		@include respond-to(mb) {
			width: 2.57em;
			height: 2.57em;
		}
	}
	.swiper-button-prev {
		left: 0;
		top: 50%;
		transform: translate(-50%, -50%);

		@include respond-to(mb) {
			left: 10px;
			transform: translate(0, -50%);
			&.bigColorSwiper.swiper-button-disabled {
				opacity: 1 !important;
				pointer-events: initial !important;
				user-select: initial !important;
			}
		}
	}

	.swiper-button-next {
		right: 0;
		top: 50%;
		transform: translate(50%, -50%);

		@include respond-to(mb) {
			right: 10px;
			transform: translate(0, -50%);
			&.normalSwiper.hasColorSwiper.swiper-button-disabled {
				opacity: 1 !important;
				pointer-events: initial !important;
				user-select: initial !important;
			}
		}
	}
}

.swiper-container-wrap {
	display: flex;
	gap: 6.5%;
	width: 100%;
	aspect-ratio: 495/417;

	@include respond-to(mb) {
		height: auto;
		gap: 10px;
		flex-direction: column;
		aspect-ratio: auto;
	}

	.previewImgWrap {
		flex: 1;
		position: relative;
		width: 0;
		height: 100%;

		.tap-box {
			position: absolute;
			right: 0.5em;
			bottom: 0.5em;
			z-index: 1001;

			b {
				font-size: 1em;
			}
		}

		@include respond-to(mb) {
			order: 1;
			flex: none;
			width: 100%;
		}

		.canvasWrap {
			display: flex;
			justify-content: center;
			align-items: center;
			position: absolute;
			inset: 0;
			height: 100%;
			z-index: 1000;

			.loadProgress {
				display: flex;
				justify-content: center;
				align-items: center;
				position: absolute;
				inset: 0;
				z-index: 1000;
			}

			.areaMask {
				position: absolute;
				top: 50%;
				transform: translateY(-50%);
				left: 0;
				right: 0;
				background-color: #2c2c2c;
				opacity: 0.5;
				padding: 20px;
				color: #fff;
				font-size: 16px;
				z-index: 99;
				word-break: break-word;
			}

			.designBox {
				display: flex;
				justify-content: center;
				align-items: center;
				position: absolute;
				inset: 0;
				background-color: #ffffff;

				@include respond-to(mb) {
					padding: 2px;
				}
			}

			.firstImg {
				position: absolute;
				inset: 0;

				img {
					width: 100%;
					height: 100%;
					object-fit: contain;
				}
			}
		}

		b {
			font-size: 30px;
			cursor: pointer;
		}

		.myswiper2 {
			position: relative;
			height: 100%;

			.custom-swiper-pagination {
				position: absolute;
				right: 0;
				bottom: 0;
				padding: 8px 10px;
				border-top-left-radius: 20px;
				background-color: rgba(0, 0, 0, 0.4);
				z-index: 1;
				color: #ffffff;
				font-size: 12px;
			}

			.swiper-slide {
				overflow: hidden;
				display: flex;
				justify-content: center;
				align-items: center;
				aspect-ratio: 1;
				border-radius: 10px;

				@include respond-to(mb) {
					border-radius: 0;
				}

				::v-deep .video-js video {
					object-fit: cover;
				}

				.smallImg {
					width: 100%;
					height: 100%;
					display: flex;
					align-items: center;
					justify-content: center;
					position: relative;
					text-align: center;

					::v-deep {
						.pic-img {
							width: 100%;
							height: 100%;

							.img-container {
								width: 100%;
								height: 100%;

								img {
									max-width: 100%;
									max-height: 100%;
									object-fit: cover;
								}
							}
						}
					}
				}
			}

			img {
				max-width: 100%;
				max-height: 100%;
				object-fit: cover;
			}
		}
	}

	$customWidth: 4.31em;

	.thumbImgWrap {
		flex-basis: $customWidth;

		@include respond-to(mb) {
			display: flex;
			flex-basis: auto;
			order: 2;
			padding: 0 10px;
		}

		&.isEdit {
			.myswiper1 {
				.swiper-slide.swiper-slide-thumb-active {
					border: 1px solid #f1f1f1;
				}
			}
		}

		.myswiper1 {
			width: 100%;
			max-height: calc(100% - (#{$customWidth} + 8px));
			margin-bottom: 8px;

			@include respond-to(mb) {
				max-height: unset;
				margin-bottom: 0;
				margin-left: 0;
				margin-right: 8px;
				width: calc((100% - 36px) / 6.5) !important;
				height: auto;
				max-width: calc(100% - 36px);
				&.colorSwiper {
					flex: 1;
					width: auto !important;
				}
				&.hasRotateImg {
					width: calc((100% - 36px) / 6.5 * 2) !important;
				}
				&.noColorSwiper {
					width: auto !important;
					.swiper-slide {
						width: 48px !important;
						height: 48px !important;
					}
				}
				.swiper-wrapper {
					.swiper-slide {
						&.swiper-slide-thumb-active {
							border: 1px solid #f1f1f1 !important;
						}
					}
					&.showColorSwiper {
						.swiper-slide {
							&.swiper-slide-thumb-active {
								border: 2px solid $color-primary !important;
							}
						}
					}
				}
			}

			.swiper-slide {
				position: relative;
				display: flex;
				justify-content: center;
				align-items: center;
				overflow: hidden;
				border: 1px solid #f1f1f1;
				height: $customWidth !important;
				border-radius: 4px;
				cursor: pointer;
				aspect-ratio: 1;

				.play-icon {
					display: flex;
					align-items: center;
					justify-content: center;
					position: absolute;
					left: 50%;
					top: 50%;
					transform: translate(-50%, -50%);
				}

				@include respond-to(mb) {
					// width: 48px !important;
					height: auto !important;

					&:last-child {
						margin-right: 0 !important;
					}
				}

				&:last-child {
					margin-bottom: 0 !important;
				}

				&.swiper-slide-thumb-active {
					border: 2px solid $color-primary;
				}

				img {
					width: 100%;
					height: 100%;
					object-fit: cover;
					aspect-ratio: 1/1;
				}
			}
		}

		.edit {
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			width: $customWidth;
			height: $customWidth;
			text-align: center;
			line-height: 1.2;
			color: #666666;
			border: 1px solid #f1f1f1;
			border-radius: 4px;
			cursor: pointer;

			span {
				font-size: 14px;
			}

			&.active {
				border: 2px solid $color-primary;
			}

			@include respond-to(mb) {
				padding: 0 4px;
				width: 48px;
				height: 48px;
				aspect-ratio: 1;
				flex-shrink: 0;
				&.hasColorSwiper {
					width: calc((100% - 36px) / 6.5);
					height: auto;
					margin-right: 6px;
				}

				span {
					font-size: 12px;
				}
			}

			b {
				font-size: 18px;

				@include respond-to(mb) {
					font-size: 14px;
				}
			}
		}

		.threeD {
			width: 100%;
			height: 100%;

			img {
				object-fit: contain !important;
			}
		}
		.arrowIcon {
			display: flex;
			align-items: center;
			width: calc(((100% - 36px) / 6.5) * 0.3);
			font-size: 16px;
		}
	}
}
</style>
