<template>
    <div id="custom-embroidered-patches" class="quoteWrap">
        <Preloader v-if="isLoading"></Preloader>
        <template v-else>
            <article class="content">
                <QuoteNav :pid="pid" title="We provide these fidget spinner types for you."></QuoteNav>
				<div class="header">
					<h1>{{ lang.cy + " " + cateData.cateName }}</h1>
				</div>
                <div class="leftArea" id="leftArea">
                    <div v-for="(item, index) in filterShowGeneralData" :key="item.id" class="kk" :class="{ type1: picDialog }">
                  
                        <PinsSizeSelect 
                            class="Fidget-Spinner-Size"
                            v-if="item.paramName == 'Fidget Spinner Size'" 
                            :generalData="generalData" 
                            :selectedData="selectedData" 
                            :stepData="item" 
                            :maskName="maskName" 
                            :sizeImgP1="p1Text"
                            :smallTitle="stepSizeTitle"
                            showMbColorImg 
                            @clickFun="selectQuoteParams(item, $event)" 
                            @closeMask="closeMask" 
                            @showMaskFn="showMaskFn"
                        > </PinsSizeSelect>

                        <PublicStep 
                            v-if="['Select Plating Colors','Fidget Spinner Plating','Plating'].includes(item.paramName)" 
                            :config="allStepConfig[item.paramName]" 
                            :selectedData="selectedData" 
                            :stepData="item" 
                            :maskName.sync="maskName" 
                            @clickFun="selectQuoteParams(item, $event)"
                        ></PublicStep>
                        
                        <PublicStep 
                            v-if="item.paramName == 'Select Your 3D style'" 
                            :config="allStepConfig[item.paramName]" 
                            :selectedData="selectedData" 
                            :stepData="item" 
                            :maskName.sync="maskName" 
                            @clickFun="selectQuoteParams(item, $event)"
                        ></PublicStep>

                        <PublicStep 
                            class="Additional-Upgrades"
                            v-if="item.paramName == 'Additional Upgrades (Optional)'" 
                            :config="allStepConfig[item.paramName]" 
                            :selectedData="selectedData" 
                            :stepData="item" 
                            :maskName.sync="maskName" 
                            @clickFun="selectQuoteParams(item, $event)" 
                            @showMaskFn="showMaskFn" 
                            @viewVideo="viewVideo"
                            :isShowPlayIcon="true"
                        ></PublicStep>

                        <PublicStep 
                            v-if="['Select Packaging Options','Fidget Spinner Packaging'].includes(item.paramName)" 
                            :config="allStepConfig[item.paramName]" 
                            :selectedData="selectedData" 
                            :stepData="item" 
                            :maskName.sync="maskName" 
                            @clickFun="selectQuoteParams(item, $event)"
                        ></PublicStep>

                        <template v-if="item.paramName === 'Upload Artwork & Comments'">
                            <StepUpload 
                                class="step-item step-upload" 
                                :class="{ mask: maskName === item.paramName }" 
                                :id="item.paramName" 
                                :index="item.customIndex" 
                                :itemData="item" 
                                :uploadList.sync="uploadArtworkList" 
                                :remark.sync="remark" 
                                :isUpload.sync="isUpload" 
                                @closeMask="closeMask" 
                                @showMaskFn="showMaskFn" 
                                :key="index"
                            ></StepUpload>
                        </template>

                        <!--                步骤九-->
                        <template v-if="item.paramName === 'Quantity'">
                            <StepQty 
                                class="step-item step-qty" 
                                :class="{ mask: maskName === item.paramName }" 
                                :id="item.paramName" 
                                :index="item.customIndex" 
                                :itemData="item" 
                                :customQty.sync="customQty" 
                                :restaurants="restaurants" 
                                @closeMask="closeMask" 
                                @showMaskFn="showMaskFn" 
                                @calcPrice="debounceCalcPrice" 
                                :key="index"
                            ></StepQty>
                        </template>

                        <!--                步骤十-->
                        <template v-if="['Turnaround Time','Turnaround Discount'].includes(item.paramName)">
                            <StepTime 
                                class="step-item step-date" 
                                :class="{ mask: maskName === item.paramName }" 
                                :id="item.paramName" 
                                :cateData="cateData" 
                                :pid="pid" 
                                :customQty="customQty" 
                                :index="item.customIndex" 
                                :itemData="item" 
                                :priceInfo="priceInfo" 
                                :previewMode="previewMode" 
                                :selectedParams="selectedData" 
                                @showMaskFn="showMaskFn" 
                                @selectItem="selectQuoteParams($event.item, $event.citem)" 
                                @closeMask="closeMask" 
                                :key="index"
                            ></StepTime>
                        </template>
                    </div>
                </div>
                <div class="rightArea" id="rightAreaCustom">
                    <transition 
                        enter-active-class="animate__animated animate__faster animate__fadeInBottomLeft" 
                        leave-active-class="animate__animated animate__faster animate__fadeOutBottomLeft"
                    >
                        <Detail 
                            v-show="showRightArea" 
                            class="type1" 
                            :class="{ mask: maskName }" 
                            :key="1" 
                            :absoluteImg="comeFromDZ.absoluteImg" 
                            :selectedData="selectedData" 
                            :priceInfo="priceInfo" 
                            :customQty="customQty" 
                            :textInfo="textInfo" 
                            :generalData="generalData" 
                            @toPosition="toPosition" 
                            @addInquiry="debounceAddInquiry" 
                            @addCart="addCart"
                        ></Detail>
                    </transition>
                </div>
            </article>
            <div class="footer" id="foot">
                <Detail 
                    class="type2" 
                    :key="3" 
                    :absoluteImg="comeFromDZ.absoluteImg" 
                    :selectedData="selectedData" 
                    :priceInfo="priceInfo" 
                    :customQty="customQty" 
                    :textInfo="textInfo" 
                    :generalData="generalData" 
                    @toPosition="toPosition" 
                    @addInquiry="debounceAddInquiry" 
                    @addCart="addCart"
                ></Detail>
            </div>
            <el-drawer 
                :visible.sync="showArea" 
                :with-header="true" 
                custom-class="drawDialog" 
                :size="device === 'ipad' ? '728px' : '85%'"
            >
                <Detail 
                    class="type3" 
                    :key="2" 
                    :absoluteImg="comeFromDZ.absoluteImg" 
                    :selectedData="selectedData" 
                    :priceInfo="priceInfo" 
                    :customQty="customQty" 
                    :textInfo="textInfo" 
                    :generalData="generalData" 
                    @toPosition="toPosition" 
                    @addInquiry="debounceAddInquiry" 
                    @addCart="addCart"
                ></Detail>
            </el-drawer>
            <!--    遮罩-->
            <myMask :maskName.sync="maskName"></myMask>
            <!-- 建议弹窗-->
            <RecomendDialog 
                :showUpload="false" 
                :commentsDialog.sync="commentsDialog" 
                :paramValue="selectedParamsValue" 
                @next="recomendNext" 
                :textPlaceholder="lang.placeholder2"
            ></RecomendDialog>
            <!--  手机，ipad端预览-->
            <PreviewBtn :showArea.sync="showArea"></PreviewBtn>
            <BaseDialog v-model="showOtoDialog" :width="device != 'mb' ? '50%' : '90%'">
                <div class="otoWrap">
                    <img src="@/assets/images/oto.png" alt="" />
                    <p>{{ lang.p1 }}</p>
                    <h3>{{ lang.p2 }}</h3>
                    <p style="color: #666666">
                        {{ lang.p3 }}<br />
                        {{ lang.p4 }}.
                    </p>

                    <div class="box">
                        <p class="t1">
                            {{ lang.p5 }} <br />
                            {{ lang.p6 }}!
                        </p>
                        <a href="https://www.o2o.co/previewWeb" style="text-decoration: none">
                            <el-button type="primary">{{ lang.p7 }}</el-button>
                        </a>
                    </div>
                </div>
            </BaseDialog>
            <BaseDialog v-model="picDialog" :width="device != 'mb' ? '45%' : '90%'" :minWidth="device != 'mb' ? '30%' : '90%'">
                <div class="picWrap" style="aspect-ratio:286/235">
                    <template v-if="getFileSuffix(zoomPic) === '.mp4'">
                        <VideoPlayer disabledMouse :options="getVideoOptions(zoomPic, 2)"></VideoPlayer>
                    </template>
                    <template v-else>
                        <img :src="zoomPic" alt="" />
                    </template>
                    <DialogBM :dialogItem="selectedParamsValue" @dialogNextStep="dialogNextStep" v-if="tempType == 'video'"></DialogBM>
                    <QtyAndBtn 
                        v-else 
                        :dialogItem.sync="selectedParamsValue" 
                        :noQty="selectedParamsValue.paramName == '3D'" 
                        :noComment="selectedParamsValue.paramName == '3D'" 
                        @qtyAndBtnConfirm="qtyAndBtnConfirm" 
                        @qtyAndBtnNext="qtyAndBtnNext($event)" 
                        :title="lang.medals.title"
                    ></QtyAndBtn>
                </div>
            </BaseDialog>
            <BaseDialog v-model="upgradeDialog" :width="device != 'mb' ? '640px' : '320px'">
                <Upgrade @closeUpgrade="upgradeDialog = false" :tipArr="tipArr" :typeName="cateData.cateName" @upgradeLevel="upgradeLevel"></Upgrade>
            </BaseDialog>
            <infoDialog 
                :infoDialogVisible.sync="infoDialogVisible" 
                @getValue="getValueFun" 
                :otherUpload="uploadArtworkList" 
                :recomendUpload="recomendUpload" 
                :uploadList.sync="uploadList"
            ></infoDialog>
            <BaseDialog v-model="noFileDialog" :width="device != 'mb' ? '485px' : '90%'" :model="false">
                <template #closeIcon>
                    <div style="display: none"></div>
                </template>
                <infoUpload 
                    :infoUploadList="infoUploadList" 
                    @pushInfoList="pushInfoList" 
                    @delInfoList="delInfoList" 
                    @updateInquiry="updateInquiry" 
                    @closeInfoDialog="closeInfoDialog"
                > </infoUpload>
            </BaseDialog>
        </template>
    </div>
</template>
<script>
import "@/plugins/element";
import BaseDialog from "@/components/Quote/BaseDialog";
import VideoPlayer from "@/components/Quote/VideoPlayer.vue";
import Upgrade from "@/components/Quote/Upgrade";
import DialogBM from "@/components/Medals/DialogBM";
import QtyAndBtn from "@/components/Medals/QtyAndBtn";
import infoDialog from "@/components/Medals/infoDialog";
import QuoteNav from "@/components/Medals/QuoteNav";
import quoteMixin from "@/mixins/quote";
import quoteBanChoiceMixins from "@/mixins/quoteBanChoice";
import StepTime from "@/components/Quote/StepTime.vue";
import StepUpload from "@/components/Quote/StepUpload.vue";
import StepQty from "@/components/Quote/StepQty.vue";
import RecomendDialog from "@/components/Quote/RecomendDialog.vue";
import myMask from "@/components/Quote/Mask.vue";
import Detail from "@/components/Quote/Detail.vue";
import PreviewBtn from "@/components/Quote/PreviewBtn.vue";
import Preloader from "@/components/Quote/Preloader.vue";
import PublicStep from "@/components/Quote/PublicStep/index.vue";
import { getQuoteConfig } from "@/assets/js/quoteConfig";
import PinsSizeSelect from "~/components/Quote/PinsSizeSelect.vue";
export default {
    head() {
        return {
            title: "Quote page",
        };
    },
    components: {
        PublicStep,
        Preloader,
        PreviewBtn,
        Detail,
        myMask,
        RecomendDialog,
        StepQty,
        StepUpload,
        StepTime,
        BaseDialog,
        VideoPlayer,
        DialogBM,
        QtyAndBtn,
        infoDialog,
        Upgrade,
        QuoteNav,
        PinsSizeSelect,
    },
    mixins: [quoteMixin, quoteBanChoiceMixins],
    data() {
        const config = getQuoteConfig.call(this,this.$route.name);
        return {
            ...config,
            restaurants: [
				{ value: "10", address: "10" },
				{ value: "15", address: "15" },
				{ value: "20", address: "20" },
				{ value: "25", address: "25" },
				{ value: "30", address: "30" },
				{ value: "35", address: "35" },
				{ value: "40", address: "40" },
				{ value: "45", address: "45" },
				{ value: "50", address: "50" },
				{ value: "60", address: "60" },
				{ value: "70", address: "70" },
				{ value: "80", address: "80" },
				{ value: "90", address: "90" },
				{ value: "100", address: "100" },
				{ value: "150", address: "150" },
				{ value: "200", address: "200" },
				{ value: "250", address: "250" },
				{ value: "300", address: "300" },
				{ value: "350", address: "350" },
				{ value: "400", address: "400" },
				{ value: "450", address: "450" },
				{ value: "500", address: "500" },
				{ value: "750", address: "750" },
				{ value: "1000", address: "1000" },
				{ value: "2500", address: "2500" },
				{ value: "3000", address: "3000" },
				{ value: "4000", address: "4000" },
				{ value: "5000", address: "5000" },
			],
        };
    },
};
</script>

<style scoped lang="scss">
@import "@/assets/css/quotePublic";
.Additional-Upgrades{
    ::v-deep .zoomIcon{
        color:#ccc !important;
    }
}
.Fidget-Spinner-Size{
    ::v-deep .shape-img img{
       aspect-ratio: 474 / 285; 
    }
    ::v-deep .step-size-rightArea{
        background-color:transparent !important;
        &::before{  
            border-width:0 !important;
        }
        .normal-text{
            font-size:16px !important;
            padding-left: 41px;
        }
    }
}
</style>