<template>
	<div class="artList" v-show="showArt" @click.stop>
		<div class="listWrap" :class="{'noData':!artList.length}" scrollbar v-loadmore="loadArt" :infinite-scroll-disabled="disabledLoadArt">
			<div class="list-item" v-for="item in artList" :key="item.id" :title="item.elementName">
				<cliparts
					:item="item"
					:checked="0"
					:clipartId="item.id"
					:isCollection="item.isCollection"
					:clipartImage="item.clipartImage"
					:clipartName="item.elementName"
					@addImg="addImg"
				>
				</cliparts>
			</div>
			<noResult v-if="!artList.length && !loadingArt"></noResult>
		</div>
		<div class="loadMore" v-show="loadingArt">
			{{ lang.loading }}...
		</div>
	</div>
</template>

<script>
import cliparts from "@/components/NeonDzxt/clipartsArt.vue"
import { favoriteClipart, getArtList, likeQ<PERSON>y } from "@/api/newDzxt";
import dzMixin from "@/mixins/dzMixin";
import noResult from "@/components/MyDzxt/noResult.vue";

export default {
	mixins: [dzMixin],
	data() {
		return {
			loadingArt: false,
			artForm: {
				page: 1,
				pageSize: 60,
				clipartTypeId: 908,
				total: 0,
				pages: 1,
				likeQuery: ""
			},
			artList: []
		}
	},
	watch: {
		isLogin() {
			this.getArtList();
		}
	},
	components: {
		cliparts,
		noResult
	},
	computed: {
		lang() {
			return this.$store.getters.lang?.design;
		},
		showArt() {
			return this.$store.state.design.showArt
		},
		noMoreArt() {
			return this.artForm.page >= this.artForm.pages;
		},
		disabledLoadArt() {
			return this.loadingArt || this.noMoreArt;
		},
		status() {
			return this.$store.state.design.status
		},
		artStatus() {
			return this.$store.state.design.artStatus
		},
		isLogin() {
			return this.$store.getters.isLogin;
		},
		userId() {
			return this.$store.state.userInfo?.id;
		}
	},
	methods: {
		loadArt() {
			this.artForm.page++;
			this.getArtList("scroll");
		},
		getArtList(type = 'select') {
			this.loadingArt = true;
			if (this.artForm.likeQuery) {
				likeQuery(Object.assign({}, this.artForm, {
					userId: this.userId,
					quoteName:this.$store.state.design?.pageInfo?.quoteCateName,
				})).then(res => {
					console.log('res.data.collectionList', res.data.collectionList )
					if (type === "scroll") {
						let list = res.data.collectionList;
						this.artList = this.artList.concat(list);
						this.artForm.total = res.data.total;
						this.artForm.pages = res.data.pages;
						this.loadingArt = false;
					} else if (type === "select") {
						this.artList = res.data.collectionList;
						this.artForm.total = res.data.total;
						this.artForm.pages = res.data.pages;
						this.loadingArt = false;
					}
				})
			} else {
				getArtList(Object.assign({}, this.artForm, {
					userId: this.userId
				})).then(res => {
					if (type === "scroll") {
						let list = res.data.records;
						this.artList = this.artList.concat(list);
						this.artForm.total = res.data.total;
						this.artForm.pages = res.data.pages;
						this.loadingArt = false;
					} else if (type === "select") {
						this.artList = res.data.records;
						this.artForm.total = res.data.total;
						this.artForm.pages = res.data.pages;
						this.loadingArt = false;
					}
				});
			}
		},
		addImg({src, property}) {
			this.$store.commit('design/set_showArt', false)
			this.$Bus.$emit('triggerAddArt', {
				src,
				property
			})
		},
		addArtFavorite(item) {
			if (!this.isLogin) {
				this.$store.commit('setLogin', 'login')
				return false;
			}
			favoriteClipart({
				clipartId: item.id
			}).then((res) => {
				item.isCollection = item.isCollection ? false : true;
			});
		},
		selectArtCategory(item) {
			this.artList = [];
			this.artForm.page = 1;
			this.artForm.clipartTypeId = item.id;
			this.artForm.likeQuery = "";
			this.getArtList();
		},
		searchArt(val) {
			this.artList = [];
			this.artForm.page = 1;
			this.artForm.clipartTypeId = 908;
			this.artForm.likeQuery = val;
			this.getArtList();
		},
		updateArtList(data){
			let findItem = this.artList.find(item=>{
				return item.id === data.clipartId
			})
			if(findItem){
				findItem.isCollection = !findItem.isCollection
			}
		}
	},
	mounted() {
		this.getArtList();
		this.$Bus.$on('updateArtList',this.updateArtList);
		this.$Bus.$on("selectArtCategory", this.selectArtCategory);
		this.$Bus.$on("searchArt", this.searchArt);
	},
	beforeDestroy() {
		this.$Bus.$off('updateArtList',this.updateArtList);
		this.$Bus.$off("selectArtCategory", this.selectArtCategory);
		this.$Bus.$off("searchArt", this.searchArt);
	},
};
</script>

<style scoped lang="scss">
.artList {
	overflow: hidden;
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	padding: 10px 30px;
	background-color: #e6e6e6;
	z-index: 2;

	.loadMore {
		position: absolute;
		left: 50%;
		bottom: 5px;
		transform: translateX(-50%);
		text-align: center;
	}

	.listWrap {
		display: grid;
		align-content: flex-start;
		overflow: hidden auto;
		padding: 20px;
		height: 100%;
		background-color: #ffffff;

		&.noData{
			display: flex;
			justify-content: center;
			align-items: center;
		}

		.list-item {
			position: relative;
			background: #EEEEEE;
			border: 1px solid #e0e0e0;
   		 	border-radius: 5px;
			cursor: pointer;
		}
	}
}

.artList {
	.listWrap {
		grid-template-columns: repeat(5, 1fr);
		grid-column-gap: 10px;
		grid-row-gap: 10px;
	}
}
</style>
