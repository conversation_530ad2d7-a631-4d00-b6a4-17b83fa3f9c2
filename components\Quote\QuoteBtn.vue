<template>
	<el-tooltip v-if="moreHover && !isMobile" popper-class="cusToolTip" effect="light" :content="tips" placement="top-start" style="padding-left: 5px">
		<button
			type="button"
			class="gs-quote-button"
			:class="{ type2: type2 }"
			:disabled="disabled"
			:style="{
				backgroundImage: bgColor,
				borderColor: borderColor,
				opacity: disabled ? 0.5 : 1,
			}"
		>
			<slot></slot>
			<span @click.stop style="margin-left: 5px">
				<b class="icon-wenhao1 tip-icon"></b>
			</span>
		</button>
	</el-tooltip>
	<button
		v-else
		type="button"
		class="gs-quote-button"
		:class="{ type2: type2 }"
		:disabled="disabled"
		:style="{
			backgroundImage: bgColor,
			borderColor: borderColor,
			opacity: disabled ? 0.5 : 1,
		}"
	>
		<slot></slot>
		<span @click.stop>
			<el-tooltip v-if="tips" popper-class="cusToolTip" effect="light" :content="tips" placement="top-start" style="padding-left: 5px">
				<b class="icon-wenhao1 tip-icon"></b>
			</el-tooltip>
		</span>
	</button>
</template>

<script>
export default {
	props: {
		bgColor: {
			type: String,
		},
		borderColor: {
			type: String,
		},
		disabled: {
			type: Boolean,
			default: false,
		},
		tips: {
			type: String,
		},
		type2: {
			type: Boolean,
			default: false,
		},
		moreHover: {
			type: Boolean,
			default: false,
		},
	},
	computed: {
		isMobile() {
			return this.$store.state.device === "mb";
		},
	},
};
</script>

<style scoped lang="scss">
.gs-quote-button[disabled] {
	opacity: 0.7;
}

.gs-quote-button {
	display: flex;
	align-items: center;
	justify-content: center;
	min-width: 200px;
	height: 40px;
	background: $color-primary;
	border-radius: 10px;
	padding: 0 10px;
	border: 1px solid transparent;
	color: #fff;
	font-size: 18px;

	@media screen and (max-width: 767px) {
		min-width: 125px;
		height: 30px;
		font-size: 12px;
		border-radius: 5px;
	}

	&.type2 {
		background-color: #fff;
		border-color: $color-primary;
		color: $color-primary;
	}
}
</style>