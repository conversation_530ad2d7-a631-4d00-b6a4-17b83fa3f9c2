<template>
  <div class="tabs-step stepWrap"
    :id="stepData.paramName"
    :class="{ active: stepData.paramName === maskName }">
    <slot name="title">
      <StepTitle :title="getTitle(stepData)"
        :step="getStep(stepData)">
        <template #suffix>
          <ToolTip :itemData="stepData"
            style="font-size: 16px"></ToolTip>
        </template>
      </StepTitle>
    </slot>

    <slot name="tips">
      <div class="step-tips">
        <b class="icon-a-Group35261"></b>
        <div class="tip">
          <span class="label">
            {{ lang.wristband.adult.split(' ')[0] }}:
          </span>
          8" {{ lang.wristband.circumference }}
        </div>
        <div class="tip">
          <span class="label">
            {{ lang.wristband.youth.split(' ')[0] }}:
          </span>
          7" {{ lang.wristband.circumference }}
        </div>
      </div>
    </slot>

    <slot name="content">
      <div class="stepContent"
        :ref="stepData.paramName">
        <!-- Tabs 导航 -->
        <div class="tabs-nav-container">
          <div class="arrow left"
            @click="scrollTabs('left')">
            <i class="el-icon-arrow-left"></i>
          </div>
          <div class="tabs-nav">
            <div v-for="(tab, index) in tabs"
              :key="index"
              :class="['tab-item', { active: activeTab === index }]"
              @click="activeTab = index">
              {{  tab.alias || tab.paramName }}
            </div>
          </div>
          <div class="arrow right"
            @click="scrollTabs('right')">
            <i class="el-icon-arrow-right"></i>
          </div>
        </div>

        <!-- Tabs 内容 -->
        <div class="tabs-content">
          <!-- 其他 Tab 的内容 -->
          <div v-for="(tab, index) in tabs"
            :key="index"
            v-show="activeTab === index"
            class="tab-pane my-scroll-style">
            <!-- 加号按钮 -->
            <div class="content-item add-button"
              v-show="index != 4"
              @click="showColorPicker(tab)">
              <div class="item-name custom">{{ 'Custom Color' }}</div>
              <div class="plus">
                +
              </div>
            </div>
            <!-- 渲染 customData -->
            <div v-for="(item, idx) in customColor"
              v-show="index != 4"
              :key="'custom-' + idx"
              class="content-item"
              @click="handleClickColor(item)"
              :class="{ selected: !!item.adult || !!item.youth }">
              <img :src="imgUrl(item)"
                style="width: 100% !important"
                loading="lazy" />
              <div class="item-name">
                <span class="name-text"
                  :title="item.typeAlias + ':' + item.alias">{{ item.typeAlias + ':' +item.alias }}</span>
                <b class="icon-Vector-edit"
                  @click="handleEditCustomColor(item)"></b>
              </div>
              <span class="label">{{ lang.wristband.adult }}</span>
              <el-input v-model.number="item.adult"
                type="number"
                placeholder="QTY"
                size="mini"
                oninput="value=value.replace(/[^0-9]/g,'')"
                @change="handleChange(item, tab)" />
              <span class="label">{{ lang.wristband.youth }}</span>
              <el-input v-model.number="item.youth"
                type="number"
                placeholder="QTY"
                size="mini"
                oninput="value=value.replace(/[^0-9]/g,'')"
                @change="handleChange(item, tab)" />
            </div>
            <div v-for="(item, idx) in tab.childList"
              :key="idx"
              class="content-item"
              @click="handleClickColor(item)"
              :class="{ selected: !!item.adult || !!item.youth }">
              <img :src="imgUrl(item)"
                style="width: 100% !important"
                loading="lazy" />
              <div class="item-name">
                <span class="name-text"
                  :title="item.alias">{{ item.alias }}</span>
              </div>
              <span class="label">{{ lang.wristband.adult }}</span>
              <el-input v-model.number="item.adult"
                type="number"
                placeholder="QTY"
                size="mini"
                oninput="value=value.replace(/[^0-9]/g,'')"
                @change="handleChange(item, tab)" />
              <span class="label">{{ lang.wristband.youth }}</span>
              <el-input v-model.number="item.youth"
                type="number"
                placeholder="QTY"
                size="mini"
                oninput="value=value.replace(/[^0-9]/g,'')"
                @change="handleChange(item, tab)" />
            </div>
          </div>
        </div>
      </div>
    </slot>

    <slot name="footer">
      <div class="stepFooter">
        <QuoteBtn @click.native="nextStepFun(stepData.paramName)"
          :disabled="!selectedItems.length > 0">
          {{ lang.next }}
        </QuoteBtn>
      </div>
    </slot>

    <i v-if="stepData.paramName === maskName"
      class="el-icon-close closeIcon"
      @click="closeMask"></i>

    <BaseDialog v-model="colorPickerVisible"
      :width="device == 'ipad' ? '500px' : device != 'mb' ? '60%' : '90%'">
      <div class="select-title">
        {{ lang.wristband.pmsTitle }}
      </div>
      <div class="select-header">
        <div class="selected-tags">
          <div v-for="(color, index) in selectedColors"
            :key="index"
            class="tag">
            <div class="tag-color"
              :style="{ backgroundColor: color.code }"></div>
            <div class="tag-name">{{ color.pantone }}</div>
            <i class="el-icon-close"
              @click="removeColor(index)"></i>
          </div>
        </div>

        <el-select v-model="pickerMode"
          style="width: 15em"
          value-key="value"
          size="mini">
          <el-option v-for="item in customTypes"
            :key="item.value"
            :label="item.label"
            :value="item"></el-option>
        </el-select>
      </div>

      <!-- 色卡列表 -->
      <div class="color-list my-scroll-style">
        <div v-for="(color, index) in colorList"
          :key="index"
          class="color-item"
          @click="selectColor(color)">
          <div class="color-name">{{ color.pantone }}</div>
          <div class="color-block"
            :style="{ backgroundColor: color.code }"></div>
        </div>
      </div>
      <!-- 确认按钮 -->
      <div class="select-footer">
        <QuoteBtn @click.native="colorPickerVisible = false">{{ designLang.cancel }}</QuoteBtn>
        <QuoteBtn @click.native="confirmColorSelection">{{ designLang.continue }}</QuoteBtn>
      </div>
    </BaseDialog>
  </div>
</template>

<script>
import StepTitle from "@/components/Quote/PublicStep/StepTitle.vue";
import ToolTip from "@/components/Quote/ToolTip.vue";
import QuoteBtn from "@/components/Quote/QuoteBtn.vue";
import BaseDialog from "@/components/Quote/BaseDialog";

export default {
  components: { ToolTip, StepTitle, QuoteBtn, BaseDialog },
  props: {
    stepData: {
      type: Object,
      default: () => ({}),
    },
    selectedData: {
      type: Object,
      default: () => ({}),
    },
    maskName: {
      type: [String, Boolean],
    },
    colorList: {
      type: Array,
    },
    customColor: {
      type: Array,
    },
  },
  data() {
    return {
      activeTab: 0, // 当前选中的 Tab 索引
      selectedItems: [], // 存储所有已选中的项
      history: [], // 记录操作历史
      colorPickerVisible: false, // 弹框是否可见
      pickerMode: "", // 颜色类型：solid segemented swril
      selectedColors: [], // 选中的色卡
      editingItem: null, // 当前正在编辑的项
      showLeftArrow: false,
      showRightArrow: true,
    };
  },
  computed: {
    customTypes() {
      return [
        {
          value: "Solid",
          alias: this.lang.wristband.solidAlias,
          label: this.lang.wristband.solidLabel,
        },
        {
          value: "Segmented",
          alias: this.lang.wristband.segmentedAlias,
          label: this.lang.wristband.segmentedLabel,
        },
        {
          value: "Swirl",
          alias: this.lang.wristband.swirlAlias,
          label: this.lang.wristband.swirlLabel,
        },
      ];
    },
	defCustomType() {
		return {
			value: 'Solid',
			alias: this.lang.wristband.solidAlias,
			label: this.lang.wristband.solidLabel
		}
	},
    tabs() {
      return this.stepData.childList;
    },
    device() {
      return this.$store.state.device;
    },
    lang() {
      return this.$store.getters.lang.quote || {};
    },
    designLang() {
      return this.$store.getters.lang?.design;
    },
  },
  watch: {
    colorPickerVisible: {
      handler(newV) {
        if (!newV) this.editingItem = null;
      },
    },
  },
  mounted() {
    this.checkScrollPosition();
  },
  methods: {
    checkScrollPosition() {
      const tabsNav = this.$el.querySelector(".tabs-nav");
      tabsNav.addEventListener("scroll", () => {
        this.showLeftArrow = tabsNav.scrollLeft > 0;
        this.showRightArrow =
          tabsNav.scrollLeft < tabsNav.scrollWidth - tabsNav.clientWidth;
      });
    },
    scrollTabs(direction) {
      const tabsNav = this.$el.querySelector(".tabs-nav");
      const scrollAmount = 100; // 每次滚动的距离

      if (direction === "left") {
        tabsNav.scrollBy({ left: -scrollAmount, behavior: "smooth" });
      } else {
        tabsNav.scrollBy({ left: scrollAmount, behavior: "smooth" });
      }
    },
    // 编辑颜色
    handleEditCustomColor(item) {
      this.editingItem = item; // 设置当前编辑的项
      const selectType = this.customTypes.find((x) => x.value === item.type);
      this.pickerMode = selectType || this.defCustomType // 设置 pickerMode
      this.selectedColors = this.parseColorCode(item); // 解析 color.code、id 和 alias
      this.colorPickerVisible = true; // 打开弹框
      this.closeMask();
    },
    // 点击颜色
    handleClickColor(val) {
      this.$emit("updateColor", val);
    },
    // 解析 color.code、id 和 alias
    parseColorCode(item) {
      if (!item.code) return [];

      // 拆分 code
      const codes = item.code.includes(",")
        ? item.code.split(",")
        : [item.code];
      // 拆分 _id
      const ids = item._id.includes(",") ? item._id.split(",") : [item._id];
      // 拆分 alias
      const aliases = item.alias.includes(" + ")
        ? item.alias.split(" + ")
        : [item.alias];

      // 将 code、id 和 alias 映射为颜色对象
      return codes.map((code, index) => ({
        code: code.trim(),
        _id: ids[index]?.trim() || "", // 确保 id 存在
        pantone: aliases[index]?.trim() || "", // 确保 alias 存在
      }));
    },
    // 显示弹框
    showColorPicker(val) {
      if (val.paramName == "Glow" || val.paramName == "Popular") {
        this.pickerMode = this.defCustomType;
      } else {
		const selectType = this.customTypes.find(x => x.value === val.paramName)
        this.pickerMode = selectType;
      }
      this.colorPickerVisible = true;
      this.selectedColors = []; // 清空已选中的色卡
      this.closeMask();
    },
    // 选择色卡
    selectColor(color) {
      if (this.pickerMode?.value === "Solid") {
        this.selectedColors = [color]; // 单选
      } else {
        if (this.selectedColors.includes(color)) {
          this.selectedColors = this.selectedColors.filter((c) => c !== color); // 取消选中
        } else if (this.selectedColors.length < 3) {
          this.selectedColors.push(color); // 多选（最多三个）
        }
      }
    },
    // 移除选中的颜色
    removeColor(index) {
      this.selectedColors.splice(index, 1);
    },
    confirmColorSelection() {
      // 根据 pickerMode 处理 selectedColors
      let customColorData = null;

      if (this.pickerMode?.value === "Solid") {
        // 如果是 solid 模式，直接生成一个对象
        const item = this.selectedColors[0]; // 因为 solid 只能选一个
        customColorData = {
          paramName: item.pantone,
          code: item.code,
          alias: item.pantone,
          id: this.editingItem ? this.editingItem.id : Date.now(), // 保留原有 _id 或生成新的
          _id: item.id + "",
          adult: this.editingItem?.adult ? this.editingItem.adult : "",
          youth: this.editingItem?.youth ? this.editingItem.youth : "",
          type: this.pickerMode.value,
		  typeAlias: this.pickerMode.alias,
          parentId: 1,
        };
      } else {
        // 如果是 segmented 或 swirl 模式，将多个颜色拼接成一个对象
        const combinedName = this.selectedColors
          .map((color) => color.pantone)
          .join(" + ");
        const combinedCode = this.selectedColors
          .map((color) => color.code)
          .join(",");
        const combinedId = this.selectedColors
          .map((color) => color._id)
          .join(",");

        customColorData = {
          paramName: combinedName,
          code: combinedCode,
          alias: combinedName,
          id: this.editingItem ? this.editingItem.id : Date.now(), // 保留原有 _id 或生成新的
          _id: combinedId,
          adult: this.editingItem?.adult ? this.editingItem.adult : "",
          youth: this.editingItem?.youth ? this.editingItem.youth : "",
          type: this.pickerMode.value,
		  typeAlias: this.pickerMode.alias,
          parentId: 1,
        };
      }

      // 关闭弹框
      this.colorPickerVisible = false;

      // 将处理后的数据传递给父组件
      this.$emit("customColorAdd", customColorData);
    },
    // 处理输入框变化
    handleChange(item, parentTab) {
      // 判断是否选中（adult 或 youth 有值）
      const isSelected = !!item.adult || !!item.youth;

      // 如果选中且不在 selectedItems 中，则添加到 selectedItems
      if (isSelected && !this.selectedItems.includes(item)) {
        this.selectedItems.push(item);
        this.history.push(item); // 记录新增操作
        this.$emit("item-updated", {
          action: "add", // 操作类型：新增
          item, // 当前操作的项
          parentTab, // 父 Tab
        });
      }

      // 如果未选中且已在 selectedItems 中，则从 selectedItems 中移除
      if (!isSelected && this.selectedItems.includes(item)) {
        this.selectedItems = this.selectedItems.filter(
          (selectedItem) => selectedItem !== item
        );
        this.history = this.history.filter(
          (historyItem) => historyItem !== item
        ); // 从历史记录中移除
        const lastAddedItem =
          this.history.length > 0
            ? this.history[this.history.length - 1]
            : null;
        this.$emit("item-updated", {
          action: "remove", // 操作类型：删除
          item, // 当前操作的项
          parentTab, // 父 Tab
          lastAddedItem, // 上一个添加的项
        });
      }

      // 保留原有的 clickFun 事件
      this.$emit("clickFun", this.selectedItems);
    },
    closeMask() {
      this.$emit("update:maskName", false);
    },
    getTitle(item) {
      return this.lang.Select + " " + item.alias;
    },
    getStep(item) {
      return this.lang.step + " " + item.customIndex;
    },
    nextStepFun(name) {
      this.$emit("nextStepFun", name);
    },
    parseJSON(str) {
      return str
        ? JSON.parse(str)
        : [
            {
              url: "",
            },
          ];
    },
    imgUrl(row) {
      if (row.img) return row.img;
      return this.parseJSON(row.imageJson)[0].url;
    },
  },
};
</script>

<style scoped lang="scss">
.stepWrap {
  margin-bottom: 20px;
  padding: 40px 30px 30px 30px;
  background: #ffffff;
  border-radius: 10px;
  position: relative;

  &.active {
    z-index: 100;
  }

  .step-tips {
    display: flex;
    padding: 5px 10px;
    background: #fffaf6;
    border-radius: 7px;
    width: fit-content;
    .tip {
      margin-inline: 5px 12px;
      .label {
        font-weight: bold;
      }
    }
    b {
      color: var(--color-primary);
    }
  }

  .stepContent {
    align-items: flex-start;

    .tabs-nav-container {
      display: flex;
      align-items: center;
      position: relative;
      overflow: hidden;

      .arrow {
        display: none;
        &.left {
          margin-right: 10px;
        }
        &.right {
          margin-left: 10px;
        }
        @media screen and(max-width: 768px) {
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: #f5f5f5;
          border-radius: 50%;
          cursor: pointer;
          z-index: 1;
          padding: 0.5em;
          font-size: 2em;
        }
      }

      .tabs-nav {
        display: flex;
        border-bottom: 1px solid #ccc;
        margin-bottom: 10px;
        width: fit-content;
        margin: 0 auto;
        @media screen and(max-width: 768px) {
          overflow-x: auto;
          scroll-behavior: smooth;
          width: calc(100% - 60px);
          &::-webkit-scrollbar {
            display: none;
          }
        }

        .tab-item {
          padding: 10px 20px;
          cursor: pointer;
          border-bottom: 2px solid transparent;
          @media screen and (max-width: 768px) {
            flex: 0 0 33.33%; // 一屏显示三个 Tab
            text-align: center;
          }
          &.active {
            color: var(--color-primary);
            border-bottom-color: var(--color-primary);
            font-weight: bold;
          }

          &:hover {
            background-color: #f5f5f5;
          }
        }
      }
    }
    &.hasViewMoreHeight {
      max-height: 520px;
      overflow: hidden;
    }

    @include respond-to(mb) {
      grid-template-columns: repeat(2, 1fr);
    }
  }
  .closeIcon {
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: 700;
    position: absolute;
    top: 0;
    right: 0;
    transform: translate(50%, -50%);
    width: 40px;
    height: 40px;
    cursor: pointer;
    background: #ffffff;
    box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.3);
    border-radius: 50%;
    z-index: 10;

    @media screen and (max-width: 800px) {
      transform: translate(0, 0);
      box-shadow: none;
    }
  }
}

.stepFooter {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 30px;
}

.my-scroll-style {
  /* 自定义滚动条样式 */
  &::-webkit-scrollbar {
    width: 6px;
  }
  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 2px;
  }
  &::-webkit-scrollbar-thumb {
    background: #bdbdbd;
    border-radius: 2px;
  }
  &::-webkit-scrollbar-thumb:hover {
    background: #a3a3a3;
  }
  &::-webkit-scrollbar-button {
    display: none;
  }
}

.tabs-content {
  .tab-pane {
    max-height: 550px;
    overflow-y: auto;
    padding: 10px;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 16px 11px;
    @media screen and(max-width: 768px) {
      max-height: 520px;
      grid-template-columns: repeat(2, 1fr);
    }
  }

  .content-item {
    padding: 8px;
    display: grid;
    grid-template-columns: 5.21vw auto 1fr;
    border: 1px solid #d9dbdd;
    border-radius: 10px;
    gap: 8px 10px;
    @media screen and(max-width: 768px) {
      grid-template-columns: 1fr auto 1fr;
      span {
        margin: auto;
      }
    }
    .item-name {
      display: flex;
      align-items: center;
      gap: 8px; // 调整按钮与名称的间距
      overflow: hidden; // 防止内容溢出

      b {
        font-size: 0.8em;
        color: var(--color-primary);
        cursor: pointer;
      }

      .name-text {
        flex: 1; // 占据剩余空间
        white-space: nowrap; // 禁止换行
        overflow: hidden; // 隐藏溢出部分
        text-overflow: ellipsis; // 显示省略号
        max-width: 150px; // 设置固定宽度，根据需要调整
        @media screen and(max-width: 768px) {
          max-width: 160px;
        }
      }

      .edit-icon {
        cursor: pointer;
        font-size: 16px;
        color: #666;
        flex-shrink: 0; // 禁止按钮缩小

        &:hover {
          color: var(--color-primary);
        }
      }
    }
    .label {
      flex: 1; // 占据剩余空间
      white-space: nowrap; // 禁止换行
      overflow: hidden; // 隐藏溢出部分
      text-overflow: ellipsis; // 显示省略号
      max-width: 50px; // 设置固定宽度，根据需要调整
    }
    &.add-button {
      cursor: pointer;
      display: flex;
      justify-content: center;
      flex-direction: column;
      align-items: center;
      border-radius: 10px;
      .plus {
        font-size: 2em;
        color: #666;
        border: 1px dashed #ccc;
        border-radius: 10px;
        width: 2.5em;
        height: 2.5em;
        line-height: 2.5em;
        text-align: center;
      }
    }
    &.selected {
      border-color: var(--color-primary);
    }
    & > .el-input {
      .el-input__inner {
        border-radius: 2px;
      }
    }
    & > img {
      grid-row-start: span 3;
    }
    & > .item-name {
      grid-column-start: span 2;
      @media screen and(max-width: 768px) {
        grid-column-start: span 3;
        grid-row-start: 1;
        text-align: center;
      }
    }
  }
}

.base-dialog {
  ::v-deep .base-dialog-model-con {
    padding: 2.18em;
    .select-title {
      font-size: 1.6875em;
    }
    .select-header {
      display: flex;
      justify-content: space-between;

      .selected-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        margin-bottom: 16px;

        .tag {
          display: flex;
          align-items: center;
          padding: 4px 8px;
          background-color: #f5f5f5;
          border-radius: 4px;
          cursor: default;

          .tag-color {
            width: 16px;
            height: 16px;
            border-radius: 2px;
            margin-right: 8px;
          }

          .tag-name {
            font-size: 12px;
          }

          .el-icon-close {
            margin-left: 8px;
            cursor: pointer;
            color: #999;

            &:hover {
              color: #333;
            }
          }
        }
      }
      .el-select {
        margin-bottom: 20px;
      }
      @media screen and(max-width: 768px) {
        flex-direction: column;
        .selected-tags {
          order: 1;
          margin-bottom: 0;
        }
        .el-select {
          margin-bottom: 0;
        }
      }
    }

    .color-list {
      display: grid;
      grid-template-columns: repeat(9, 1fr);
      gap: 16px;
      max-height: 50vh;
      overflow: auto;
      @media screen and(max-width: 768px) {
        grid-template-columns: repeat(4, 1fr);
        gap: 11px;
        margin-top: 10px;
      }
      .color-item {
        border-radius: 6px;
        padding: 0.875em;
        border: 1px solid #e8e8e8;
        cursor: pointer;
        text-align: center;
        .color-name {
          margin-bottom: 10px;
          font-size: 0.875em;
        }
        .color-block {
          width: 100%;
          height: 50px;
          border-radius: 4px;
        }
      }
    }

    .select-footer {
      display: flex;
      margin-top: 1em;
      width: fit-content;
      margin-left: auto;
	  @media screen and(max-width: 768px) {
		margin-inline: auto;
	  }
      > button:first-child {
        margin-right: 1em;
      }
    }
  }
}

::v-deep {
  .el-input__inner {
    background-color: #f5f6f7;
    font-family: Calibri;
    &:focus {
      border-color: var(--color-primary);
    }
  }
}
</style>