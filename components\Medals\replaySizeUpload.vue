<template>
	<div class="replaySizeUpload">
		<div class="dialogTitle">
			<p>{{ lang.Tips }}</p>
		</div>
		<div class="dialogContent">
			<p>{{ lang.Filecannot80 }}</p>
			<div class="uploadArea">
				<template v-if="overSizeList.length > 0">
					<ul scrollBar>
						<li v-for="(citem, cindex) in infoUploadList" class="uploadItem" :key="cindex">
							<span class="uploadItem_text">{{ citem.original_filename }}</span>
							<div class="uploadItem_right">
								<span>{{ citem.size }} Kb</span>
								<b class="icon-check myIcon" style="color: #0cbd5f"></b>
								<el-button style="border: none; padding: 5px; background: transparent" icon="el-icon-delete" @click.stop="delUploadImg(cindex)"></el-button>
							</div>
						</li>
					</ul>
				</template>
				<div class="upload-btn">
					<button type="button" @click="replayUpload">
						<span>{{ lang.reUpload }}</span>
					</button>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	name: "replaySizeUpload",
	components: {},
	props: {
		overSizeDialog: {
			type: Boolean,
			default: false,
		},
		overSizeList: {
			type: Array,
			default: [],
		},
	},
	data() {
		return {};
	},
	watch: {},
	computed: {
		lang() {
			return this.$store.getters.lang.quote || {};
		},
	},
	methods: {
		replayUpload(){
			this.$emit('replayUpload')
			this.$emit('update:overSizeDialog',false)
		}
	},
	created() {},
	mounted() {},
};
</script>

<style scoped lang="scss">
p {
	line-height: 24;
	font-family: Calibri;
	font-size: 14px;
	text-align: center;
}
.replaySizeUpload {
	padding: 10px;
}
.dialogTitle {
	padding: 10px 15px;
}
.dialogContent {
	padding: 0 30px;
	.uploadArea {
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		align-items: center;
		padding: 10px;
		padding-top: 0;
		background: #ffffff;
		border: 1px dashed #d8d8d8;
		border-radius: 4px;
		cursor: pointer;
		transition: all 0.3s;
		width: 90%;
		margin: 5px auto;
		ul {
			background: hsla(0, 0%, 85%, 0.27);
			max-height: 88px;
			width: calc(100% + 20px);
			overflow: auto;
			margin-left: 0;
			.uploadItem {
				display: flex;
				justify-content: space-between;
				align-items: center;
				font-size: 12px;
				padding: 2px 10px;
				color: #3d3d3d;
				.uploadItem_text {
					padding-right: 5px;
					word-break: break-word;
					text-align: left;
				}
				.uploadItem_right {
					flex-shrink: 0;
					span {
						font-size: 12px;
					}
				}
			}

			.myIcon {
				margin: 0 4px;
			}
		}
		.upload-btn {
			text-align: center;
			button {
				font-size: 16px;
				border-radius: 2px;
				color: #fff;
				line-height: 35px;
				padding: 0 10px;
				margin-top: 12px;
				background: #ff633a;
			}
		}
	}
}
</style>
