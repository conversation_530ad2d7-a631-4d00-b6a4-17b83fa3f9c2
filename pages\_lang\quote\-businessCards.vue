<template>
	<div>
		<BusinessCard :data="dataConfig" :isGsPageQuote="true"></BusinessCard>
	</div>
</template>

<script>
import BusinessCard from "@/components/modal/Quote/BusinessCard.vue";
// 键为路由名称 值为报价相关配置
const mapObj = {
    'lang-quote-standard-stainless-steel-business-cards': { quotePid: 671, quoteCateId: 672 },
    'lang-quote-standard-black-metal-business-cards': { quotePid: 671, quoteCateId: 674 },
    'lang-quote-standard-gold-metal-business-cards': { quotePid: 671, quoteCateId: 676 },
    'lang-quote-standard-rose-gold-metal-business-cards': { quotePid: 671, quoteCateId: 678 },
    'lang-quote-metal-nfc-business-cards': { quotePid: 671, quoteCateId: 680 },
    'lang-quote-square-business-cards': { quotePid: 671, quoteCateId: 682 },
    'lang-quote-mini-business-cards': { quotePid: 671, quoteCateId: 684 },
    'lang-quote-metal-bottle-opener-business-cards': { quotePid: 671, quoteCateId: 712 },
    'lang-quote-grinder-business-cards': { quotePid: 671, quoteCateId: 713 },
    'lang-quote-standard-copper-metal-business-cards': { quotePid: 671, quoteCateId: 714 }
};
export default {
	data() {
		return {};
	},
	components: {
		BusinessCard,
	},
    computed:{
      dataConfig(){
          return mapObj[this.$route.name]
      }
    },
};
</script>

<style lang="scss" scoped></style>