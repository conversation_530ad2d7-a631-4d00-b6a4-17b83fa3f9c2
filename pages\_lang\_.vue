<template>
	<div :class="{ 'catalogue modal-box': modalData?.find((i) => i.class && i.class.includes('catalogue-modal')) }">
		<!-- 分类类型 0:报价分类 1:半定制父分类 2:半定制子分类 3:半定制产品 4:半定制二级子分类-->
		<v-app v-if="cateType === 1 || cateType === 2 || cateType === 3 || cateType === 4" style="background: none">
			<v-main>
				<component v-for="(item, index) in modalData" :is="'modal' + item.name" :halfCateDTO="halfCateDTO"
					:cateId="cateId" :parentOneChildCateId="parentOneChildCateId" :parentCateId="parentCateId"
					:cateType="cateType" :isStockPage="isStockPage"
					:data="{ ...item, id: item.id + '-' + item.theme + '-' + index }"
					:id="item.id + '-' + item.theme + '-' + index" :key="index"></component>
			</v-main>
		</v-app>

		<component v-else v-for="(item, index) in modalData" :is="'modal' + item.name"
			:class="{ 'bg-second': !$store.state.pagePath || $store.state.pagePath === 'about-us' }"
			:data="{ ...item, id: item.id + '-' + item.theme + '-' + index }"
			:id="item.id + '-' + item.theme + '-' + index" :key="index"></component>
	</div>
</template>

<script>
import { getPageData, saveVisitRecord, getLanguageLink, googleDataStructural, getGoogleProductStructure } from "@/api/web.js";
import { getViewHomeHtml, getViewItemHtml } from "@/utils/criteoConfig";

export default {
	name: "AllPage",
	async asyncData({ store, error, route }) {
		try {
			// let page = store.state.pagePath?.split('/')[store.state.pagePath?.split('/')?.length - 1] || 'home';
			// if (process.env.NODE_ENV == 'development') return {
			// 	modalData: require("@/initWeb/style_20/" + page + ".json") || [],
			// 	mbItem: require("@/initWeb/style_20/mb/" + page + ".json") || [],
			// 	cateType: 0
			// };

			let result = await Promise.all([getPageData(store.state.pagePath), getLanguageLink(store.state.pagePath, store.state.language.language, store.state.language.countryCode)]);
			let data = result[0].data,
				pcItem = [],
				mbItem = [];
			let quoteCateId = data?.parentCateId || data?.cateId;

			//多语言meta
			let linkData = result[1];
			let LanguageData = [];
			if (linkData && linkData.data && linkData.data.length > 0)
				linkData.data.forEach((item) => {
					if (item.hreflang == null) LanguageData.push({ href: item.href, rel: item.rel });
					else {
						if (item.hreflang === "en-UK") item.hreflang = "en-GB";
						LanguageData.push({ href: item.href, rel: item.rel, hreflang: item.hreflang });
					}
				});

			//保存页面类型
			store.commit("setCateType", data?.cateType);

			// 半定制结构化数据
			let halfStructuring = "";
			if ([1, 2].includes(data?.cateType)) {
				try {
					halfStructuring = (
						await googleDataStructural({
							categoryId: quoteCateId,
							childCategoryId: data.parentCateId ? data.cateId : null,
							page: 1,
							pageSize: 60,
							productType: data.isStockPage,
							url: store.state.domain,
						})
					).data;
				} catch (e) { }
			} else if ([3].includes(data?.cateType)) {
				// keychains网站 半定制详情页面去除结构化数据
				if (store.state.proId != 354) {
					try {
						halfStructuring = (
							await getGoogleProductStructure({
								routingName: store.state.pagePath,
								productParamId: route.query.id,
								url: store.state.domain,
							})
						).data;
					} catch (e) {
						console.log(e);
					}
				}
			}

			//页面数据处理
			if (data && data.pageRowList)
				data.pageRowList
					.filter((item) => !item.isHidden)
					.map((item) => {
						item.sampleData && pcItem.push(JSON.parse(item.sampleData));
						item.mbSampleData && mbItem.push(JSON.parse(item.mbSampleData));
					});

			if (pcItem.length && store.state.languageUrl)
				return {
					...data,
					cateType: data.cateType || 0,
					quoteCateId,
					LanguageData,
					pcItem,
					mbItem,
					halfStructuring,
					modalData: store.getters.isMobile ? store.getters.deepMerge(pcItem, mbItem) : pcItem,
				};
			else return error({ statusCode: 404 });
		} catch (e) {
			console.log(e);
			return error({ statusCode: 404 });
		}
	},
	head() {
		let meta = [
			{
				hid: "description",
				name: "description",
				content: this.description,
			},
			{
				hid: "keywords",
				name: "keywords",
				content: this.keyword,
			},
		];
		//bps网站 半定制列表页和详情页面禁止爬虫抓取（2024-09-11）
		if ([1, 2, 3].includes(this?.cateType) && this.$store.state.proId == 358) {
			meta.push({
				hid: "customMeta",
				name: "robots",
				content: "noindex,nofollow",
			});
		}
		//结构化数据空值处理
		let script = [];
		if (this.structuring) {
			let list = JSON.parse(this.structuring);
			list.forEach((item) => {
				script.push({
					type: "application/ld+json",
					innerHTML: item || "",
				});
			});
		}
		if (this.halfStructuring) {
			script.push({
				type: "application/ld+json",
				innerHTML: this.halfStructuring,
			});
		}
		//Criteo Product Tag
		let isLogin = this.$store.getters.isLogin;
		let userInfo = this.$store.state.userInfo;
		let criteoConfig = this.$store.getters.getAdvertisementConfig("criteoConfig");
		if (criteoConfig) {
			let account = criteoConfig?.account;
			let email = isLogin ? userInfo.email : "";
			//判断是否是产品页面 platformProductId 有值
			if (this.platformProductId && account) {
				script.push({
					innerHTML: getViewItemHtml({
						account,
						email,
						platformProductId: this.platformProductId
					})
				});
			}
			//判断是否是首页
			if (this.$store.state.pagePath === '/' && account) {
				script.push({
					innerHTML: getViewHomeHtml({
						account,
						email
					})
				});
			}
		}
		return {
			title: this.title,
			meta: meta,
			link: this.LanguageData || [],
			script: script,
		};
	},
	created() {
		if (this.$store.getters.isMobile) this.modalData = this.$store.getters.deepMerge(this.pcItem || this.modalData, this.mbItem);
	},
	mounted() {
		this.saveVisitRecord();
		this.$Bus.$on("saveVisit", this.saveVisitRecord);

		if (process.browser) this.$nextTick(() => this.$store.dispatch("setProDom"));
	},
	methods: {
		saveVisitRecord(productId) {
			saveVisitRecord({
				proId: this.$store.state.proId,
				productId: productId ? productId : "",
				userId: this.$store.state.userInfo.id ? this.$store.state.userInfo.id : this.$store.state.userUUID,
				routingName: this.$store.state.pagePath,
				quoteCateId: this.quoteCateId,
				buyType: this?.cateType,
			});
		},
	},
	beforeDestroy() {
		this.$Bus.$off("saveVisit");
	},
};
</script>

<style scoped lang="scss">
::v-deep .v-application--wrap {
	min-height: auto;
}

.modal-box:not(.catalogue) {
	overflow: hidden;
}

.catalogue {
	background: #f0f0f0;

	::v-deep .catalogue-modal footer a:hover {
		color: $color-primary;
		border-bottom-color: transparent;
	}

	::v-deep h2::before {
		display: none !important;
	}
}

[theme="19"] .catalogue {
	background: var(--bg-primary);

	>div:not(.catalogue-modal) {
		padding: 1em 1em 2em;
	}

	::v-deep .catalogue-modal {
		.title b {
			opacity: 0.8;
			font-size: 0.8em;
		}

		[select] {
			border-radius: 0.25em;
			background: rgba(128, 121, 112, 0.08);
			border-left-color: $color-primary !important;
		}
	}
}

[theme="20"] .catalogue {
	background-color: #ffffff;

	::v-deep .catalogue-modal {
		[select] {
			font-weight: 700;
		}
	}
}

@media screen and (min-device-width: $mb-width) {
	[theme] .catalogue {
		padding-top: 0;
		padding-bottom: 2em;
		margin-top: calc((100vh - var(--scroll-padding-top) - 1em) * -1);

		>div {
			margin-left: 23%;
			padding-left: 0;
			padding-right: 0;
		}
	}

	[theme="7"] .catalogue-modal ::v-deep .nav {
		padding-top: 1em;
		border-top: 1px solid #eeeeee;
	}

	[theme="19"] .catalogue>div:not(.catalogue-modal) {
		margin-left: 25%;
		padding-right: 0;
	}

	[theme="20"] .catalogue>div:not(.catalogue-modal) {
		margin-left: 21%;
	}
}

@media screen and (max-width: $mb-width) {
	.catalogue {
		padding: 0 !important;

		::v-deep .catalogue-modal {
			&:hover {
				box-shadow: 0 0 1em var(--bg-mask);

				.menu {
					display: none;
				}
			}

			&:not(:hover) footer>div:not(.menu),
			&:not(:hover) footer .menu>div:not(.title) {
				display: none;
			}

			.menu .title {
				justify-content: center;

				div {
					flex: none;
				}
			}

			.nav-box label {
				font-size: 1em;
			}
		}
	}

	[theme="20"] .catalogue {
		background-color: #f0f0f0;
	}
}
</style>
