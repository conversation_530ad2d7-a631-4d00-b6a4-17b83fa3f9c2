import {
	getMedalsAllStepConfig,
	getPinsAllStepConfig,
	getCoinsAllStepConfig,
	getPinBadgesAllStepConfig,
	getBallMarkerAllStepConfig,
	getLanyardAllStepConfig,
	getWristbandAllStepConfig,
	getKeychainsAllStepConfig,
	getPatchesAllStepConfig,
	getOrnamentAllStepConfig,
	getCufflinksAllStepConfig,
	getFidgetSpinnerAllStepConfig,
	getBeltBucklesAllStepConfig,
	getButtonBadgesAllStepConfig,
	getPvcLuggageTagsAllStepConfig,
	getEmbroideredBadgesAllStepConfig,
	getOldOrnamentAllStepConfig
} from "@/assets/js/quote/quoteStepConfig";

// Quote 类
export class Quote {
	constructor(pid, productsName, restaurants) {
		this.pid = pid;
		this.productsName = productsName;
		this.restaurants = restaurants || [
			{ value: "10", address: "10" },
			{ value: "50", address: "50" },
			{ value: "100", address: "100" },
			{ value: "200", address: "200" },
			{ value: "300", address: "300" },
			{ value: "500", address: "500" },
			{ value: "1000", address: "1000" },
			{ value: "3000", address: "3000" },
			{ value: "5000", address: "5000" },
			{ value: "10000", address: "10000" },
		];
	}
}

export const getQuoteConfig = function (routeName) {
	let config;
	switch (routeName) {
		case "lang-quote-medals-quote":
			config = new Quote(45, "Medals");
			config.allStepConfig = getMedalsAllStepConfig.call(this);
			break;
		case "lang-quote-uv-medals-quote":
			config = new Quote(433, "UV Printed 3D Medals");
			config.allStepConfig = getMedalsAllStepConfig.call(this);
			break;
		case "lang-quote-coins-quote":
			config = new Quote(40, "Coins");
			config.allStepConfig = getCoinsAllStepConfig.call(this);
			break;
		case "lang-quote-uv-printed-3D-challenge-coins":
			config = new Quote(324, "Coins");
			config.allStepConfig = getCoinsAllStepConfig.call(this);
			break;
		case "lang-quote-bottle-opener-coins":
			config = new Quote(407, "Coins");
			config.allStepConfig = getCoinsAllStepConfig.call(this);
			break;
		case "lang-quote-spinner-coins":
			config = new Quote(408, "Coins");
			config.allStepConfig = getCoinsAllStepConfig.call(this);
			break;
		case "lang-quote-single-sided-challenge-coins":
			config = new Quote(323, "Coins");
			config.allStepConfig = getCoinsAllStepConfig.call(this);
			break;
		case "lang-quote-3D-coins":
			config = new Quote(472, "Coins");
			config.allStepConfig = getCoinsAllStepConfig.call(this);
			break;
		case "lang-quote-mint-coins":
			config = new Quote(409, "Coins");
			config.allStepConfig = getCoinsAllStepConfig.call(this);
			break;
		case "lang-quote-heat-transfer-patches":
			config = new Quote(458, "heat transfer patches");
			config.sizeType = "sizeSelect";
			config.allStepConfig = getPatchesAllStepConfig.call(this);
			config.allStepConfig["quoteCategory"] = {
				stepContentConfig: {
					pc: {
						style: {
							"grid-template-columns": "repeat(4, 1fr)",
						},
					},
					mb: {
						style: {
							"grid-template-columns": "repeat(2, 1fr)",
						},
					},
				},
			};
			break;
		case "lang-quote-3d-silicone-patches":
			config = new Quote(478, "3d silicone patches");
			config.sizeType = "sizeSelect";
			config.allStepConfig = getPatchesAllStepConfig.call(this);
			config.allStepConfig["quoteCategory"] = {
				stepContentConfig: {
					pc: {
						style: {
							"grid-template-columns": "repeat(4, 1fr)",
						},
					},
					mb: {
						style: {
							"grid-template-columns": "repeat(2, 1fr)",
						},
					},
				},
			};
			break;
		case "lang-quote-custom-embroidered-patches":
			config = new Quote(107, "custom embroidered patches");
			config.sizeType = "sizeSelect";
			config.allStepConfig = getPatchesAllStepConfig.call(this);
			break;
		case "lang-quote-3d-embroidered-patches":
			config = new Quote(121, "3d embroidered patches");
			config.sizeType = "normal";
			config.allStepConfig = getPatchesAllStepConfig.call(this);
			break;
		case "lang-quote-custom-embroidered-printed-patches":
			config = new Quote(444, "custom embroidered printed patches");
			config.sizeType = "normal";
			config.allStepConfig = getPatchesAllStepConfig.call(this);
			break;
		case "lang-quote-custom-printed-patches":
			config = new Quote(108, "custom printed patches");
			config.sizeType = "sizeSelect";
			config.allStepConfig = getPatchesAllStepConfig.call(this);
			break;
		case "lang-quote-custom-tatami-fabric-silicone-patches":
			config = new Quote(568, "custom tatami fabric silicone patches", [
				{ value: "50", address: "50" },
				{ value: "100", address: "100" },
				{ value: "200", address: "200" },
				{ value: "300", address: "300" },
				{ value: "500", address: "500" },
				{ value: "1000", address: "1000" },
				{ value: "1500", address: "1500" },
				{ value: "2000", address: "2000" },
				{ value: "2500", address: "2500" },
			]);
			config.sizeType = "sizeSelect";
			config.allStepConfig = getPatchesAllStepConfig.call(this);
			config.allStepConfig["Patch Color"] = {
				moldText: 4,
				priceTextCss: {
					normaltext: {
						"text-align": "left",
					},
					tipTextStyle: {
						"text-align": "left",
						"margin-left": "1.8em",
					},
				},
				stepContentConfig: {
					pc: {
						style: {
							"grid-template-columns": "repeat(4, 1fr)",
						},
					},
					mb: {
						style: {
							"grid-template-columns": "repeat(2, 1fr)",
						},
					},
				},
				stepItemStyle: {
					style: {
						"flex-direction": "row",
						padding: "10px",
						"border-radius": "10px",
						border: "1px solid #EFEFEF",
						"justify-content": "left",
					},
					mbStyle: {
						padding: "5px",
					},
					stepImgWrapStyle: {
						style: {
							width: "85%",
						},
					},
					mbStepImgWrapStyle: {
						style: {
							width: "55px",
							"border-radius": "5px",
						},
					},
					stepTextWrapStyle: {
						style: {
							width: "100%",
							"margin-left": "10px",
							"text-align": "center",
						},
						aliasStyle: {
							style: {
								"display": "flex",
								"justify-content": "flex-start",
								"align-items": "center"
							},
						},
					},
					mbStepTextWrapStyle: {
						customCircleStyle: {
							style: {
								width: "16px",
								height: "16px",
							},
						},
					},
				},
			};
			break;
		case "lang-quote-custom-woven-patches":
			config = new Quote(109, "custom woven patches");
			config.sizeType = "sizeSelect";
			config.allStepConfig = getPatchesAllStepConfig.call(this);
			break;
		case "lang-quote-custom-chenille-patches":
			config = new Quote(110, "custom chenille patches");
			config.sizeType = "sizeSelect";
			config.allStepConfig = getPatchesAllStepConfig.call(this);
			break;
		case "lang-quote-custom-leather-patches":
			config = new Quote(111, "custom leather patches");
			config.sizeType = "sizeSelect";
			config.allStepConfig = getPatchesAllStepConfig.call(this);
			config.allStepConfig["Additional Upgrades"] = {
				showNextBtn: false,
				hideTitleSelectText: true,
			}
			break;
		case "lang-quote-custom-flex-patches":
			config = new Quote(122, "custom flex patches");
			config.sizeType = "sizeSelect";
			config.allStepConfig = getPatchesAllStepConfig.call(this);
			break;
		case "lang-quote-one-color-tpu-patches":
			config = new Quote(295, "one color tpu patches");
			config.sizeType = "normal";
			config.allStepConfig = getPatchesAllStepConfig.call(this);
			config.allStepConfig["Patch Color"] = {
				showPriceText: false,
				stepContentConfig: {
					pc: {
						style: {
							"grid-template-columns": "repeat(4, 1fr)",
						},
					},
					mb: {
						style: {
							"grid-template-columns": "repeat(2, 1fr)",
						},
					},
				},
			};
			break;
		case "lang-quote-PVCpatches-quote":
			config = new Quote(55, "PVC patches");
			config.sizeType = "sizeSelect";
			config.allStepConfig = {
				quoteCategory: {
					mediaConfig: {
						style: {
							"aspect-ratio": "1008/708",
						},
					},
					stepContentConfig: {
						pc: {
							style: {
								"grid-template-columns": "repeat(3, 1fr)",
							},
						},
						mb: {
							style: {
								"grid-template-columns": "repeat(2, 1fr)",
							},
						},
					},
				},
				Size: {
					showNextBtn: true,
					smallTitle: this.$store.getters.lang.quote.sizeSmallTitle,
				},
				Color: {
					moldText: 1,
					stepItemStyle: {
						stepImgWrapStyle: {
							style: {
								background: "#f4f5f5",
							},
						},
					},
					stepContentConfig: {
						pc: {
							style: {
								"grid-template-columns": "repeat(5, 1fr)",
								"align-items": "flex-start"
							},
						},
						mb: {
							style: {
								"grid-template-columns": "repeat(2, 1fr)",
								"align-items": "flex-start"
							},
						},
					},
				},
				"2D or 3D": {
					moldText: 3,
					mediaConfig: {
						style: {
							"aspect-ratio": "1008/708",
						},
					},
					stepItemStyle: {
						stepTextWrapStyle: {
							pcAliasStyle: {
								style: {
									"justify-content": "left",
								},
							},
						},
					},
					priceTextCss: {
						pcNormaltext: {
							"align-items": "flex-start",
							"margin-left": "22px",
						},
					},
					stepContentConfig: {
						pc: {
							style: {
								"grid-template-columns": "repeat(3, 1fr)",
								"align-items": "flex-start",
							},
						},
						mb: {
							style: {
								"grid-template-columns": "repeat(2, 1fr)",
								"align-items": "flex-start",
							},
						},
					},
				},
				"Purchase Intention": {
					showPriceText: false,
					imgBorderStyle: 2,
					hideTitleSelectText: true,
					showNextBtn: true,
					stepContentConfig: {
						pc: {
							style: {
								"grid-template-columns": "repeat(4, 1fr)",
							},
						},
						mb: {
							style: {
								"grid-template-columns": "repeat(2, 1fr)",
							},
						},
					},
				},
				"PVC Patch Shape": {
					stepContentConfig: {
						pc: {
							style: {
								"grid-template-columns": "repeat(5, 1fr)",
							},
						},
						mb: {
							style: {
								"grid-template-columns": "repeat(2, 1fr)",
							},
						},
					},
				},
				"Backing": {
					clickStepShowTips: true,
					showNextBtn: true,
				},
			};
			break;
		case "lang-quote-custom-blank-patches":
			config = new Quote(273, "custom black patches");
			config.sizeType = "sizeSelect";
			config.allStepConfig = {
				"Patch Size": {
					showNextBtn: true,
					smallTitle: this.$store.getters.lang.quote.sizeSmallTitle,
				},
				"Patch Border": {
					showNextBtn: true,
					stepContentConfig: {
						pc: {
							style: {
								"grid-template-columns": "repeat(3, 1fr)",
							},
						},
						mb: {
							style: {
								"grid-template-columns": "repeat(2, 1fr)",
							},
						},
					},
					stepItemStyle:{
						stepImgWrapStyle:{
							style:{
								"aspect-ratio": "372 / 203"
							}
						}
					}
				},
				"Patch Shape": {
					showPriceText: false,
					stepContentConfig: {
						pc: {
							style: {
								"grid-template-columns": "repeat(5, 1fr)",
							},
						},
						mb: {
							style: {
								"grid-template-columns": "repeat(2, 1fr)",
							},
						},
					},
				},
				"Patch Twill Color": {
					showPriceText: false,
					stepContentConfig: {
						pc: {
							style: {
								"grid-template-columns": "repeat(5, 1fr)",
							},
						},
						mb: {
							style: {
								"grid-template-columns": "repeat(2, 1fr)",
							},
						},
					},
				},
				"Patch Backing": {
					clickStepShowTips: true,
					showNextBtn: true,
					stepContentConfig: {
						pc: {
							style: {
								"grid-template-columns": "repeat(4, 1fr)",
							},
						},
						mb: {
							style: {
								"grid-template-columns": "repeat(2, 1fr)",
							},
						},
					},
				},
				"Purchase Intention": {
					showPriceText: false,
					imgBorderStyle: 2,
					hideTitleSelectText: true,
					showNextBtn: true,
					stepContentConfig: {
						pc: {
							style: {
								"grid-template-columns": "repeat(4, 1fr)",
							},
						},
						mb: {
							style: {
								"grid-template-columns": "repeat(2, 1fr)",
							},
						},
					},
				},
			};
			break;
		case "lang-quote-custom-sequin-patches":
			config = new Quote(298, "custom sequin patches");
			config.sizeType = "sizeSelect";
			config.allStepConfig = getPatchesAllStepConfig.call(this);
			config.allStepConfig["Patch Color"] = {
				stepContentConfig: {
					pc: {
						style: {
							"grid-template-columns": "repeat(4, 1fr)",
						},
					},
					mb: {
						style: {
							"grid-template-columns": "repeat(2, 1fr)",
						},
					},
				},
			};
			break;
		case "lang-quote-custom-woven-labels":
			config = new Quote(310, "custom woven labels");
			config.sizeType = "sizeSelect";
			config.allStepConfig = getPatchesAllStepConfig.call(this);
			break;
		case "lang-quote-custom-denim-patches":
			config = new Quote(398, "custom denim patches");
			config.sizeType = "sizeSelect";
			config.allStepConfig = getPatchesAllStepConfig.call(this);
			break;
		case "lang-quote-custom-printed-care-labels":
			config = new Quote(459, "custom printed care labels");
			config.sizeType = "sizeSelect";
			config.allStepConfig = getPatchesAllStepConfig.call(this);
			break;
		//pins
		case "lang-quote-tradingpins-quote":
			config = new Quote(51, "Trading Pins");
			config.allStepConfig = getPinsAllStepConfig.call(this);
			config.allStepConfig["Additional Upgrades (Optional)"] = {
				showNextBtn: true,
				mediaConfig: {
					style: {
						"aspect-ratio": "700/604",
					},
				},
				stepContentConfig: {
					pc: {
						style: {
							"grid-template-columns": "repeat(5, 1fr)",
							"align-items": "flex-start"
						},
					},
					mb: {
						style: {
							"grid-template-columns": "repeat(2, 1fr)",
							"align-items": "flex-start"
						},
					},
				}
			};
			config.allStepConfig["Select Packaging"] = {
				mediaConfig: {
					style: {
						"aspect-ratio": "272/244",
					},
				},
				stepContentConfig: {
					pc: {
						style: {
							"grid-template-columns": "repeat(5, 1fr)",
							"align-items": "flex-start"
						},
					},
					mb: {
						style: {
							"grid-template-columns": "repeat(2, 1fr)",
							"align-items": "flex-start"
						},
					},
				},
			}
			config.allStepConfig["Select Trading Pin Size"] = {
				sizeImgP1: this.$store.getters.lang.quote.tradingpins.p1,
				smallTitle: this.$store.getters.lang.quote.tradingpins.stepSizeTitle
			}
			break;
		case "lang-quote-custom-enamel-pins":
			config = new Quote(479, "Pins");
			config.allStepConfig = getPinsAllStepConfig.call(this);
			break;
		case "lang-quote-custom-die-struck-pins":
			config = new Quote(489, "Pins");
			config.allStepConfig = getPinsAllStepConfig.call(this);
			break;
		case "lang-quote-custom-3D-pins":
			config = new Quote(535, "Pins");
			config.allStepConfig = getPinsAllStepConfig.call(this);
			break;
		case "lang-quote-custom-uv-printed-pins":
			config = new Quote(536, "Pins");
			config.allStepConfig = getPinsAllStepConfig.call(this);
			config.allStepConfig["Back Side Option"] = {
				stepContentConfig: {
					pc: {
						style: {
							"grid-template-columns": "repeat(4, 1fr)",
						},
					},
					mb: {
						style: {
							"grid-template-columns": "repeat(2, 1fr)",
						},
					},
				},
				stepItemStyle: {
					stepImgWrapStyle: {
						style: {
							background: "#f4f5f5",
						},
					},
				},
			}
			break;
		case "lang-quote-custom-offset-pins":
			config = new Quote(490, "Pins");
			config.allStepConfig = getPinsAllStepConfig.call(this);
			break;
		case "lang-quote-custom-rush-pins":
			config = new Quote(491, "Pins");
			config.allStepConfig = getPinsAllStepConfig.call(this);
			break;
		case "lang-quote-custom-acrylic-pins":
			config = new Quote(537, "Pins");
			config.allStepConfig = getPinsAllStepConfig.call(this);
			break;
		case "lang-quote-custom-cut-out-pins":
			config = new Quote(460, "Pins");
			config.allStepConfig = getPinsAllStepConfig.call(this);
			break;
		case "lang-quote-hatpins-quote":
			config = new Quote(275, "Pins");
			config.allStepConfig = getPinsAllStepConfig.call(this);
			config.allStepConfig["Select Amount of Colors for Pin"].hideTitleSelectText = true;
			config.allStepConfig["Pin Size"] = {
				sizeImgP1: this.$store.getters.lang.quote.pins.p1,
				smallTitle: this.$store.getters.lang.quote.hatPins.stepSizeTitle
			}
			break;
		//pinBadges
		case "lang-quote-custom-cut-out-pin-badges":
			config = new Quote(661, "pinBadges");
			config.allStepConfig = getPinBadgesAllStepConfig();
			break;
		case "lang-quote-custom-enamel-pin-badges":
			config = new Quote(495, "pinBadges");
			config.allStepConfig = getPinBadgesAllStepConfig();
			break;
		case "lang-quote-custom-die-struck-pin-badges":
			config = new Quote(502, "pinBadges");
			config.allStepConfig = getPinBadgesAllStepConfig();
			break;
		case "lang-quote-custom-3D-pin-badges":
			config = new Quote(543, "pinBadges");
			config.allStepConfig = getPinBadgesAllStepConfig();
			break;
		case "lang-quote-custom-uv-printed-pin-badges":
			config = new Quote(506, "pinBadges");
			config.allStepConfig = getPinBadgesAllStepConfig();
			break;
		case "lang-quote-custom-offset-pin-badges":
			config = new Quote(503, "pinBadges");
			config.allStepConfig = getPinBadgesAllStepConfig();
			break;
		case "lang-quote-custom-rush-pin-badges":
			config = new Quote(504, "pinBadges");
			config.allStepConfig = getPinBadgesAllStepConfig();
			break;
		case "lang-quote-custom-metal-patches":
			config = new Quote(573, "metal Patches");
			config.allStepConfig = {
				quoteCategory: {
					stepContentConfig: {
						pc: {
							style: {
								"grid-template-columns": "repeat(3, 1fr)",
							},
						},
						mb: {
							style: {
								"grid-template-columns": "repeat(2, 1fr)",
							},
						},
					},
				},
				Style: {
					stepContentConfig: {
						pc: {
							style: {
								"grid-template-columns": "repeat(3, 1fr)",
							},
						},
						mb: {
							style: {
								"grid-template-columns": "repeat(2, 1fr)",
							},
						},
					},
				},
				"Plating/Finish（copperl）": {
					stepContentConfig: {
						pc: {
							style: {
								"grid-template-columns": "repeat(5, 1fr)",
							},
						},
						mb: {
							style: {
								"grid-template-columns": "repeat(2, 1fr)",
							},
						},
					},
				},
				"Plating/Finish（Aluminum）": {
					stepContentConfig: {
						pc: {
							style: {
								"grid-template-columns": "repeat(5, 1fr)",
							},
						},
						mb: {
							style: {
								"grid-template-columns": "repeat(2, 1fr)",
							},
						},
					},
				},
				"Plating/Finish（stainless steel）": {
					stepContentConfig: {
						pc: {
							style: {
								"grid-template-columns": "repeat(5, 1fr)",
							},
						},
						mb: {
							style: {
								"grid-template-columns": "repeat(2, 1fr)",
							},
						},
					},
				},
				"Plating/Finish（废除）": {
					stepContentConfig: {
						pc: {
							style: {
								"grid-template-columns": "repeat(5, 1fr)",
							},
						},
						mb: {
							style: {
								"grid-template-columns": "repeat(2, 1fr)",
							},
						},
					},
				},
				"Intended Use": {
					showPriceText: false,
					imgBorderStyle: 2,
					hideTitleSelectText: true,
					//	showNextBtn: true,
					stepContentConfig: {
						pc: {
							style: {
								"grid-template-columns": "repeat(5, 1fr)",
							},
						},
						mb: {
							style: {
								"grid-template-columns": "repeat(2, 1fr)",
							},
						},
					},
				},
				"Metal Process": {
					stepContentConfig: {
						pc: {
							style: {
								"grid-template-columns": "repeat(3, 1fr)",
							},
						},
						mb: {
							style: {
								"grid-template-columns": "repeat(2, 1fr)",
							},
						},
					},
				},
				"Assembly Method": {
					stepContentConfig: {
						pc: {
							style: {
								"grid-template-columns": "repeat(4, 1fr)",
							},
						},
						mb: {
							style: {
								"grid-template-columns": "repeat(2, 1fr)",
							},
						},
					},
				},
				"Metal Patches Shape": {
					hasViewMore: true,
					showPriceText: false,
					showChildListLength:6,
					stepContentConfig: {
						pc: {
							style: {
								"grid-template-columns": "repeat(5, 1fr)",
							},
						},
						mb: {
							style: {
								"grid-template-columns": "repeat(2, 1fr)"
							},
						},
					},
				},
				Material: {
					stepContentConfig: {
						pc: {
							style: {
								"grid-template-columns": "repeat(3, 1fr)",
							},
						},
						mb: {
							style: {
								"grid-template-columns": "repeat(2, 1fr)",
							},
						},
					},
				},
				"Plating/Finish": {
					hasViewMore: true,
					showChildListLength:6,
					stepContentConfig: {
						pc: {
							style: {
								"grid-template-columns": "repeat(5, 1fr)",
							},
						},
						mb: {
							style: {
								"grid-template-columns": "repeat(2, 1fr)"
							},
						},
					},
				},
			};
			break;
		case "lang-quote-custom-flock-patches": {
			config = new Quote(557, "flockPatches", [
				{ value: "50", address: "50" },
				{ value: "100", address: "100" },
				{ value: "200", address: "200" },
				{ value: "300", address: "300" },
				{ value: "500", address: "500" },
				{ value: "1000", address: "1000" },
				{ value: "1500", address: "1500" },
				{ value: "2000", address: "2000" },
				{ value: "2500", address: "2500" },
			]);
			config.sizeType = "sizeSelect";
			config.allStepConfig = {
				"Tatami Fabric Color": {
					moldText: 4,
					hideTitleSelectText: true,
					priceTextCss: {
						normaltext: {
							"text-align": "left",
						},
						tipTextStyle: {
							"text-align": "left",
							"margin-left": "1.8em",
						},
					},
					stepContentConfig: {
						pc: {
							style: {
								"grid-template-columns": "repeat(4, 1fr)",
							},
						},
						mb: {
							style: {
								"grid-template-columns": "repeat(2, 1fr)",
							},
						},
					},
					stepItemStyle: {
						style: {
							"flex-direction": "row",
							padding: "10px",
							"border-radius": "10px",
							border: "1px solid #EFEFEF",
							"justify-content": "left",
						},
						mbStyle: {
							padding: "5px",
						},
						stepImgWrapStyle: {
							style: {
								width: "85%",
							},
						},
						mbStepImgWrapStyle: {
							style: {
								width: "55px",
								"border-radius": "5px",
							},
						},
						stepTextWrapStyle: {
							style: {
								width: "100%",
								"margin-left": "10px",
								"text-align": "center",
							},
							aliasStyle: {
								style: {
									"display": "flex",
									"justify-content": "flex-start",
									"align-items": "center"
								},
							},
						},
						mbStepTextWrapStyle: {
							customCircleStyle: {
								style: {
									width: "16px",
									height: "16px",
								},
							},
						},
					},
				},
				Size: {
					showNextBtn: true,
					smallTitle: this.$store.getters.lang.quote.sizeSmallTitle,
					heightDes: "Patch Height",
					widthDes: "Patch Width",
					hideTitleSelectText: true,
				},
				Backing: {
					hideTitleSelectText: true,
				},
				"More Options": {
					hideTitleSelectText: true,
				},
				"Upload Artwork & Comments": {
					showNextBtn: true,
					hideTitleSelectText: true,
				},
				Shape: {
					showPriceText: false,
					hideTitleSelectText: true,
					stepContentConfig: {
						pc: {
							style: {
								"grid-template-columns": "repeat(5, 1fr)",
							},
						},
						mb: {
							style: {
								"grid-template-columns": "repeat(2, 1fr)",
							},
						},
					},
				},
				Quantity: {
					hideTitleSelectText: true,
				},
				"Select Number of Colors": {
					showPriceText: true,
					moldText: 4,
					hideTitleSelectText: true,
					stepItemStyle: {
						style: {
							"justify-content": "space-between !important",
						},
					},
					priceTextCss: {
						pcNormaltext: {
							"flex-direction": "column",
							"text-align": "left",
							"margin-left": "5em",
						},
						mbNormaltext: {
							"flex-direction": "column",
						},

						tipTextStyle: {
							visibility: "hidden",
						},
					},
				},
				"Intended Use": {
					showPriceText: false,
					showNextBtn: true,
					hideTitleSelectText: true,
					imgBorderStyle: 2,
				},
				"Turnaround Time": {
					hideTitleSelectText: true,
				},
			};
			break;
		}
		//keychains
		case "lang-quote-cut-out-keychains":
			config = new Quote(633, "keychains");
			config.allStepConfig = getKeychainsAllStepConfig();
			break;
		case "lang-quote-custom-rush-printed-keychains":
			config = new Quote(631, "keychains");
			config.allStepConfig = getKeychainsAllStepConfig();
			break;
		case "lang-quote-metal-couples-keychains":
			config = new Quote(583, "keychains");
			config.allStepConfig = getKeychainsAllStepConfig();
			break;
		case "lang-quote-custom-acrylic-couples-keychains":
			config = new Quote(581, "keychains");
			config.allStepConfig = getKeychainsAllStepConfig();
			break;
		case "lang-quote-custom-acrylic-keychains":
			config = new Quote(117, "keychains");
			config.allStepConfig = getKeychainsAllStepConfig();
			config.allStepConfig["Keychain Printed Area"] = {
				pcActive2: false,
				mbActive2: true,
				stepContentConfig: {
					pc: {
						style: {
							"grid-template-columns": "repeat(3, 1fr)",
						},
					},
					mb: {
						style: {
							"grid-template-columns": "repeat(2, 1fr)",
						},
					},
				},
				stepItemStyle: {
					mbStyle: {
						borderRadius: "6px",
						overflow: "hidden",
						"background-color": "#f3f3f3",
						"padding-bottom": "5px",
					},
					stepTextWrapStyle: {
						mbStyle: {
							flex: 1,
							width: "100%",
							backgroundColor: "#f3f3f3",
							padding: "10px",
							borderRadius: "0 0 6px 6px",
						},
					},
				},
			}
			config.allStepConfig["Acrylic Charm Color"] = {
				stepItemStyle: {
					style: {
						background: "rgb(244 245 245)",
						"border-radius": "10px",
						"padding-bottom": "5px",
					},
				},
				stepContentConfig: {
					pc: {
						style: {
							"grid-template-columns": "repeat(6, 1fr)",
						},
					},
					mb: {
						style: {
							"grid-template-columns": "repeat(3, 1fr)",
						},
					},
				},
			}
			config.allStepConfig["Additional Upgrades"] = {
				videoFit: "cover",
				pcActive2: true,
				mbActive2: true,
				showNextBtn: true,
				mediaConfig: {
					style: {
						"aspect-ratio": "384/270",
						"object-fit": "cover",
					},
				},
				stepContentConfig: {
					pc: {
						style: {
							"grid-template-columns": "repeat(3, 1fr)",
						},
					},
					mb: {
						style: {
							"grid-template-columns": "repeat(2, 1fr)",
							"align-items": "stretch",
						},
					},
				},
				stepItemStyle: {
					style: {
						borderRadius: "6px",
						overflow: "hidden",
						"background-color": "#f3f3f3",
					},
					stepTextWrapStyle: {
						style: {
							flex: 1,
							width: "100%",
							backgroundColor: "#f3f3f3",
							padding: "10px 0 5px",
							"margin-top": "0",
							borderRadius: "0 0 6px 6px",
						},
					},
				},
			}
			break;
		case "lang-quote-custom-embroidered-keychains":
			config = new Quote(219, "embroidered keychains");
			config.allStepConfig = getKeychainsAllStepConfig();
			config.sizeType = "sizeSelect";
			config.allStepConfig["quoteCategory"] = {
				pcActive2: true,
				mbActive2: true,
				stepContentConfig: {
					pc: {
						style: {
							"grid-template-columns": "repeat(4, 1fr)",
						},
					},
					mb: {
						style: {
							"grid-template-columns": "repeat(2, 1fr)",
						},
					},
				},
				stepItemStyle: {
					style: {
						borderRadius: "6px",
						overflow: "hidden",
						"background-color": "rgb(242, 242, 242)",
					},
					stepImgWrapStyle: {
						style: {
							borderRadius: "6px 6px 0 0",
							border: "none",
						},
					},
					stepTextWrapStyle: {
						style: {
							flex: 1,
							width: "100%",
							backgroundColor: "#f2f2f2",
							padding: "10px 5px 10px 10px",
							borderRadius: "0 0 6px 6px",
						},
					},
				},
			}
			config.allStepConfig['Additional Upgrades'] ={
				showNextBtn: true,
				stepContentConfig: {
					pc: {
						style: {
							"grid-template-columns": "repeat(4, 1fr)",
						},
					},
					mb: {
						style: {
							"grid-template-columns": "repeat(2, 1fr)",
						},
					},
				},
			};
			config.allStepConfig['Link & Chain Options'] ={
				stepContentConfig: {
					pc: {
						style: {
							"grid-template-columns": "repeat(5, 1fr)",
						},
					},
					mb: {
						style: {
							"grid-template-columns": "repeat(2, 1fr)",
						},
					},
				}
			};
			break;
		case "lang-quote-custom-metal-keychains":
			config = new Quote(112, "metal keychains");
			config.allStepConfig = getKeychainsAllStepConfig();
			config.allStepConfig["Keychain Color"] = {
				stepContentConfig: {
					pc: {
						style: {
							"grid-template-columns": "repeat(6, 1fr)",
						},
					},
					mb: {
						style: {
							"grid-template-columns": "repeat(3, 1fr)",
						},
					},
				},
			};
			config.allStepConfig["Additional Upgrades"] = {
				pcActive2: true,
				mbActive2: true,
				showNextBtn: true,
				hasViewMore: true,
				showChildListLength:6,
				mediaConfig: {
					style: {
						"aspect-ratio": "700/604",
						"object-fit": "cover"
					},
				},
				stepContentConfig: {
					pc: {
						style: {
							"grid-template-columns": "repeat(4, 1fr)",
							"align-items": "stretch"
						},
					},
					mb: {
						style: {
							"grid-template-columns": "repeat(2, 1fr)",
							"align-items": "stretch"
						},
					},
				},
				stepItemStyle: {
					pcStyle: {
						borderRadius: "6px",
						overflow: "hidden",
						"background-color": "#f2f2f2",
						"padding-bottom":"10px",
					},
					mbStyle: {
						borderRadius: "6px",
						overflow: "hidden",
						"background-color": "#f2f2f2",
					},
					mbStepTextWrapStyle: {
						style: {
							flex: 1,
							width: "100%",
							backgroundColor: "#f2f2f2",
							padding: "10px 0 5px",
							"margin-top": "0",
							borderRadius: "0 0 6px 6px",
						},
					},
				},
				priceTextCss: {
					normaltext: {
						"display": "block",
					},
				},
			};
			config.allStepConfig["Select Link & Chain Options"] = {
				pcActive2: true,
				mbActive2: true,
				stepContentConfig: {
					pc: {
						style: {
							"grid-template-columns": "repeat(5, 1fr)",
						},
					},
					mb: {
						style: {
							"grid-template-columns": "repeat(2, 1fr)",
						},
					},
				},
				stepItemStyle: {
					style: {
						borderRadius: "6px",
						overflow: "hidden",
						"background-color": "#f4f5f5",
					},
					stepImgWrapStyle: {
						style: {
							borderRadius: "10px",
						},
					},
					stepTextWrapStyle: {
						style: {
							flex: 1,
							width: "100%",
							backgroundColor: "#f4f5f5",
							padding: "10px 0",
							"margin-top": "0",
							borderRadius: "0 0 6px 6px",
						}
					},
				},
			};
			config.allStepConfig["Design Areas"] = {
				pcActive2: false,
				mbActive2: true,
				stepContentConfig: {
					pc: {
						style: {
							"grid-template-columns": "repeat(3, 1fr)"
						},
					},
					mb: {
						style: {
							"grid-template-columns": "repeat(2, 1fr)"
						},
					},
				},
				stepItemStyle: {
					mbStyle: {
						borderRadius: "6px",
						overflow: "hidden",
						"background-color": "#f4f5f5",
					},
					mbStepTextWrapStyle: {
						style: {
							flex: 1,
							width: "100%",
							backgroundColor: "#f4f5f5",
							padding: "10px 0px 3px",
							"margin-top": "0",
							borderRadius: "0 0 6px 6px",
						},
					},
				},
			};
			config.allStepConfig["Plating"] = {
				pcActive2: false,
				mbActive2: true,
				stepContentConfig: {
					pc: {
						style: {
							"grid-template-columns": "repeat(6, 1fr)",
						},
					},
					mb: {
						style: {
							"grid-template-columns": "repeat(2, 1fr)",
						},
					},
				},
				stepItemStyle: {
					mbStyle: {
						borderRadius: "6px",
						overflow: "hidden",
						"background-color": "#f4f5f5",
					},
					mbStepTextWrapStyle: {
						style: {
							flex: 1,
							width: "100%",
							backgroundColor: "#f4f5f5",
							padding: "10px 0px 3px",
							"margin-top": "0",
							borderRadius: "0 0 6px 6px",
						},
					},
				},
			};
			config.allStepConfig["Backstamp Options"] = {
				pcActive2: true,
				mbActive2: true,
				stepContentConfig: {
					pc: {
						style: {
							"grid-template-columns": "repeat(3, 1fr)",
						},
					},
					mb: {
						style: {
							"grid-template-columns": "repeat(2, 1fr)",
						},
					},
				},
				stepItemStyle: {
					style: {
						borderRadius: "6px",
						overflow: "hidden",
						"background-color": "#f4f5f5",
					},
					stepImgWrapStyle: {
						style: {
							borderRadius: "10px",
						},
					},
					stepTextWrapStyle: {
						style: {
							flex: 1,
							width: "100%",
							backgroundColor: "#f4f5f5",
							padding: "10px 0",
							"margin-top": "0",
							borderRadius: "0 0 6px 6px",
						},
					},
				},
			};
			break;
		case "lang-quote-custom-metal-printing-keychains":
			config = new Quote(399, "keychains");
			config.allStepConfig = getKeychainsAllStepConfig();
			config.allStepConfig["Plating"] = {
				pcActive2: false,
				mbActive2: true,
				stepContentConfig: {
					pc: {
						style: {
							"grid-template-columns": "repeat(6, 1fr)",
						},
					},
					mb: {
						style: {
							"grid-template-columns": "repeat(2, 1fr)",
						},
					},
				},
				stepItemStyle: {
					mbStyle: {
						borderRadius: "6px",
						overflow: "hidden",
						"background-color": "#f4f5f5",
					},
					mbStepTextWrapStyle: {
						style: {
							flex: 1,
							width: "100%",
							backgroundColor: "#f4f5f5",
							padding: "10px 0px 3px",
							"margin-top": "0",
							borderRadius: "0 0 6px 6px",
						},
					},
				},
			};
			config.allStepConfig["Design Areas"] = {
				pcActive2: false,
				mbActive2: true,
				stepContentConfig: {
					pc: {
						style: {
							"grid-template-columns": "repeat(3, 1fr)",
						},
					},
					mb: {
						style: {
							"grid-template-columns": "repeat(2, 1fr)",
						},
					},
				},
				stepItemStyle: {
					mbStyle: {
						borderRadius: "10px",
						overflow: "hidden",
						"background-color": "#f4f5f5",
					},
					mbStepTextWrapStyle: {
						style: {
							flex: 1,
							width: "100%",
							backgroundColor: "#f4f5f5",
							padding: "10px 0px 5px",
							"margin-top": "0",
							borderRadius: "0 0 6px 6px",
						},
					},
				},
			};
			config.allStepConfig["Backstamp Options"] = {
				pcActive2: true,
				mbActive2: true,
				stepContentConfig: {
					pc: {
						style: {
							"grid-template-columns": "repeat(3, 1fr)",
						},
					},
					mb: {
						style: {
							"grid-template-columns": "repeat(2, 1fr)",
						},
					},
				},
				stepItemStyle: {
					style: {
						borderRadius: "6px",
						overflow: "hidden",
						"background-color": "#f4f5f5",
					},
					stepImgWrapStyle: {
						style: {
							borderRadius: "10px",
						},
					},
					stepTextWrapStyle: {
						style: {
							flex: 1,
							width: "100%",
							backgroundColor: "#f4f5f5",
							padding: "10px 0",
							"margin-top": "0",
							borderRadius: "0 0 6px 6px",
						},
					},
				},
			};
			break;
		case "lang-quote-custom-pvc-keychains":
			config = new Quote(118, "keychains");
			config.allStepConfig = getKeychainsAllStepConfig();
			config.allStepConfig["Keychain Printed Area"] = {
				pcActive2: false,
				mbActive2: true,
				stepContentConfig: {
					pc: {
						style: {
							"grid-template-columns": "repeat(3, 1fr)",
						},
					},
					mb: {
						style: {
							"grid-template-columns": "repeat(2, 1fr)",
						},
					},
				},
				stepItemStyle: {
					mbStyle: {
						borderRadius: "6px",
						overflow: "hidden",
						"background-color": "rgb(242, 242, 242)",
						"padding-bottom": "5px",
					},
					stepTextWrapStyle: {
						mbStyle: {
							flex: 1,
							width: "100%",
							backgroundColor: "#f2f2f2",
							padding: "10px",
							borderRadius: "0 0 6px 6px",
						},
					},
				},
			};
			break;

		// ball Marker报价
		case "lang-quote-custom-ball-marker":
			config = new Quote(561, "ballMarker");
			config.allStepConfig = getBallMarkerAllStepConfig.call(this);
			break;
		case "lang-quote-custom-ball-marker-with-hat-clips":
			config = new Quote(638, "ballMarker");
			config.allStepConfig = getBallMarkerAllStepConfig.call(this);
			break;
		case "lang-quote-custom-magnetic-ball-markers":
			config = new Quote(644, "ballMarker");
			config.allStepConfig = getBallMarkerAllStepConfig.call(this);
			break;
		case "lang-quote-custom-divot-tools":
			config = new Quote(650, "ballMarker");
			config.allStepConfig = getBallMarkerAllStepConfig.call(this);
			break;
		case "lang-quote-two-tone-lanyards":
			config = new Quote(619, "lanyard");
			config.noDesignTab = true;
			config.lanyardType = "twoToneLanyards";
			config.allStepConfig = getLanyardAllStepConfig();
			break;
		case "lang-quote-light-up-lanyards":
			config = new Quote(635, "lanyard");
			config.noDesignTab = true;
			config.noCustomTab = true;
			config.lanyardType = "lightUpLanyards";
			config.allStepConfig = getLanyardAllStepConfig();
			break;
		case "lang-quote-glitter-lanyards":
			config = new Quote(629, "lanyard");
			config.noDesignTab = true;
			config.noCustomTab = false;
			config.lanyardType = 'glitterLanyards';
			config.freeText =  this.$store.getters.lang.quote.StandardFREE;
			config.allStepConfig = getLanyardAllStepConfig();
			break;
		case "lang-quote-3d-silicone-printing-lanyards":
			config = new Quote(700, "lanyard");
			config.noDesignTab = true;
			config.noCustomTab = false;
			config.allStepConfig = getLanyardAllStepConfig();
			break;
		case "lang-quote-dye-sublimated-lanyards":
			config = new Quote(701, "lanyard");
			config.noDesignTab = false;
			config.noCustomTab = false;
			config.allStepConfig = getLanyardAllStepConfig();
			break;
		case "lang-quote-nylon-lanyards":
			config = new Quote(702, "lanyard");
			config.noDesignTab = true;
			config.noCustomTab = false;
			config.allStepConfig = getLanyardAllStepConfig();
			break;
		case "lang-quote-tubular-lanyards":
			config = new Quote(703, "lanyard");
			config.noDesignTab = true;
			config.noCustomTab = false;
			config.allStepConfig = getLanyardAllStepConfig();
			break;
		case "lang-quote-polyester-lanyards":
			config = new Quote(704, "lanyard");
			config.noDesignTab = true;
			config.noCustomTab = false;
			config.allStepConfig = getLanyardAllStepConfig();
			break;
		case "lang-quote-woven-lanyards":
			config = new Quote(705, "lanyard");
			config.noDesignTab = true;
			config.noCustomTab = false;
			config.allStepConfig = getLanyardAllStepConfig();
			break;
		case "lang-quote-cord-lanyards":
			config = new Quote(597, "lanyard");
			config.noDesignTab = true;
			config.noCustomTab = false;
			config.allStepConfig = getLanyardAllStepConfig();
			break;
		case "lang-quote-reflective-lanyards":
			config = new Quote(621, "lanyard");
			config.noDesignTab = true;
			config.noCustomTab = false;
			config.lanyardType = "reflectiveLanyards"
			config.allStepConfig = getLanyardAllStepConfig();
			break;
		case "lang-quote-neoprene-lanyards":
			config = new Quote(602, "lanyard");
			config.noDesignTab = true;
			config.noCustomTab = true;
			config.lanyardType = "neopreneLanyards";
			config.allStepConfig = getLanyardAllStepConfig();
			break;
		case "lang-quote-phone-lanyards":
			config = new Quote(747, "lanyard");
			config.noDesignTab = true;
			config.noCustomTab = false;
			config.lanyardType = "phoneLanyards";
			config.allStepConfig = getLanyardAllStepConfig();
			break;
		case "lang-quote-short-wrist-lanyards":
			config = new Quote(689, "lanyard");
			config.noDesignTab = true;
			config.noCustomTab = false;
			config.lanyardType = "shortWristLanyards";
			config.allStepConfig = getLanyardAllStepConfig();
			break;
		case "lang-quote-blank-lanyards":
			config = new Quote(695, "lanyard");
			config.noDesignTab = true;
			config.noCustomTab = false;
			config.lanyardType = "blankLanyards";
			config.allStepConfig = getLanyardAllStepConfig();
			break;
		//咬板报价
		case "lang-quote-custom-stainless-steel-ornaments":
			config = new Quote(599, "ornaments");
			config.leftImg = [
				{
					id: 1,
					url: "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20241229/ueditor/97/custom-ornaments.jpg",
					alt: this.$store.getters.lang.quote.ornament.customOrnamentsImg,
				},
				{
					id: 2,
					url: "https://static-oss.gs-souvenir.com/web/quoteManage/20250110/Custom_Stainless_Steel_Ornaments_with_Etched_Process_20250110pwbGMH.jpg",
					alt: this.$store.getters.lang.quote.ornament.ornaments,
				},
				{
					id: 3,
					url: "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20241229/ueditor/97/custom-christmas-ornaments.jpg",
					alt: this.$store.getters.lang.quote.ornament.customChristmasOrnaments,
				},
				{
					id: 4,
					url: "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20241229/ueditor/97/stainless-steel-ornaments.jpg",
					alt: this.$store.getters.lang.quote.ornament.stainlessSteelOrnaments,
				},
				{
					id: 5,
					url: "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20241229/ueditor/97/stainless-steel-ornaments-with-silkscreen-process.png",
					alt: this.$store.getters.lang.quote.ornament.silkscreenProcess,
				},
			];
			config.descriptionText = this.$store.getters.lang.quote.ornament.ornamentDescriptionText;
			config.descriptionText2 = this.$store.getters.lang.quote.ornament.ornamentDescriptionText2;
			config.descriptionText3 = "";
			config.h1 = this.$store.getters.lang.quote.ornament.stainlessSteelOrnamentsH1;
			config.allStepConfig = getOrnamentAllStepConfig();
			break;
		case "lang-quote-custom-gold-brass-ornaments":
			config = new Quote(600, "ornaments");
			config.leftImg = [
				{
					id: 1,
					url: "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20241226/ueditor/97/gold-brass-ornaments-with-silkscreen-process.jpg",
					alt: this.$store.getters.lang.quote.ornament.goldSilkscreenProcess,
				},
				{
					id: 2,
					url: "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20241226/ueditor/97/custom-ornaments.jpg",
					alt: this.$store.getters.lang.quote.ornament.customOrnamentsImg,
				},
				{
					id: 3,
					url: "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20241226/ueditor/97/custom-christmas-ornaments.jpg",
					alt: this.$store.getters.lang.quote.ornament.customChristmasOrnaments,
				},
				{
					id: 4,
					url: "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20241229/ueditor/97/gold-brass-ornaments.jpg",
					alt: this.$store.getters.lang.quote.ornament.goldBrassOrnaments,
				},
				{
					id: 5,
					url: "https://static-oss.gs-souvenir.com/web/quoteManage/20250110/Gold_Brass_Ornaments_with_Debossed_Process_202501102ZScpP.png",
					alt: this.$store.getters.lang.quote.ornament.goldDebossedProcess,
				},
			];
			config.descriptionText = this.$store.getters.lang.quote.ornament.ornamentDescriptionText3;
			config.descriptionText2 = this.$store.getters.lang.quote.ornament.ornamentDescriptionText4;
			config.descriptionText3 = "";
			config.h1 = this.$store.getters.lang.quote.ornament.goldBrassOrnamentsH1;
			config.allStepConfig = getOrnamentAllStepConfig();
			break;
		case "lang-quote-custom-zinc-die-casting-ornaments":
			config = new Quote(601, "ornaments");
			config.leftImg = [
				{
					id: 1,
					url: "https://static-oss.gs-souvenir.com/web/quoteManage/20250106/Personalized_Zinc_Die_Casting_Ornaments_20250106khMxAe.jpg",
					alt: this.$store.getters.lang.quote.ornament.personalizedOrnaments,
				},
				{
					id: 2,
					url: "https://static-oss.gs-souvenir.com/web/quoteManage/20250110/Christmas_Zinc_Die_Casting_Ornaments_20250110Zkp7YJ.jpg",
					alt: this.$store.getters.lang.quote.ornament.christmasOrnaments,
				},
				{
					id: 3,
					url: "https://static-oss.gs-souvenir.com/web/quoteManage/20250106/Shop_Zinc_Die_Casting_Ornaments_20250106aHrzJM.jpg",
					alt: this.$store.getters.lang.quote.ornament.shopOrnaments,
				},
				{
					id: 4,
					url: "https://static-oss.gs-souvenir.com/web/quoteManage/20250106/Custom_Zinc_Die_Casting_Ornaments_20250106ihdiDX.jpg",
					alt: this.$store.getters.lang.quote.ornament.customZincOrnaments,
				},
				{
					id: 5,
					url: "https://static-oss.gs-souvenir.com/web/quoteManage/20250106/zinc-die-casting-ornaments-5_2025010634EKJY.jpg",
					alt: this.$store.getters.lang.quote.ornament.customZincOrnaments,
				},
			];
			config.descriptionText = this.$store.getters.lang.quote.ornament.ornamentDescriptionText5;
			config.descriptionText2 = this.$store.getters.lang.quote.ornament.ornamentDescriptionText6;
			config.descriptionText3 = "";
			config.descriptionText4 = this.$store.getters.lang.quote.ornament.ornamentDescriptionText17;
			config.descriptionText5 = this.$store.getters.lang.quote.ornament.ornamentDescriptionText18;
			config.descriptionText6 = this.$store.getters.lang.quote.ornament.ornamentDescriptionText19;

			config.h1 = this.$store.getters.lang.quote.ornament.ZincDieCastingOrnamentsH1;
			config.allStepConfig = getOrnamentAllStepConfig();
			break;
		case "lang-quote-custom-spinning-ornaments":
			config = new Quote(659, "ornaments");
			config.leftImg = [
				{
					id: 1,
					url: "https://static-oss.gs-souvenir.com/web/quoteManage/20250110/custom_spinning_ornaments1_20250110chXyi4.jpg",
					alt: this.$store.getters.lang.quote.ornament.customspinningornaments,
				},
				{
					id: 2,
					url: "https://static-oss.gs-souvenir.com/web/quoteManage/20250108/Christmas_ornaments1_2025010844a6Gz.png",
					alt: this.$store.getters.lang.quote.ornament.spinningchristmasornaments,
				},
				{
					id: 3,
					url: "https://static-oss.gs-souvenir.com/web/quoteManage/20250108/spinning_ornaments_with_logo1_20250108JrFzpr.png",
					alt: this.$store.getters.lang.quote.ornament.spinningornamentswithlogo,
				},
				{
					id: 4,
					url: "https://static-oss.gs-souvenir.com/web/quoteManage/20250116/spinning_ornaments_20250116XiHFA6.png",
					alt: this.$store.getters.lang.quote.ornament.spinningornaments,
				},
				{
					id: 5,
					url: "https://static-oss.gs-souvenir.com/web/quoteManage/20250108/spinning_Xmas_ornaments_202501088fXpAc.png",
					alt: this.$store.getters.lang.quote.ornament.spinningXmasornaments,
				},
			];
			config.descriptionText = this.$store.getters.lang.quote.ornament.ornamentDescriptionText7;
			config.descriptionText2 = this.$store.getters.lang.quote.ornament.ornamentDescriptionText8;
			config.descriptionText3 = this.$store.getters.lang.quote.ornament.ornamentDescriptionText9;
			config.h1 = this.$store.getters.lang.quote.ornament.SpinningOrnamentsH1;
			config.allStepConfig = getOrnamentAllStepConfig();
			break;
		case "lang-quote-custom-acrylic-ornaments":
			config = new Quote(656, "ornaments");
			config.leftImg = [
				{
					id: 1,
					url: "https://static-oss.gs-souvenir.com/web/quoteManage/20250110/Personalized_Acrylic_Ornaments_20250110Hz8Swt.jpg",
					alt: this.$store.getters.lang.quote.ornament.personalizedAcrylicOrnaments,
				},
				{
					id: 2,
					url: "https://static-oss.gs-souvenir.com/web/quoteManage/20250109/Acrylic_Ornaments_Custom_20250109kZKQam.jpg",
					alt: this.$store.getters.lang.quote.ornament.acrylicOrnamentsCustom,
				},
				{
					id: 3,
					url: "https://static-oss.gs-souvenir.com/web/quoteManage/20250110/Acrylic_Ornaments_Personalized_20250110eZDPaY.jpg",
					alt: this.$store.getters.lang.quote.ornament.acrylicOrnamentsPersonalized,
				},
				{
					id: 4,
					url: "https://static-oss.gs-souvenir.com/web/quoteManage/20250109/Acrylic_Ornaments_Near_Me_20250109PbEzkm.jpg",
					alt: this.$store.getters.lang.quote.ornament.acrylicOrnamentsNearMe,
				},
				{
					id: 5,
					url: "https://static-oss.gs-souvenir.com/web/quoteManage/20250109/Acrylic_Christmas_Ornaments_20250109XHEmfz.jpg",
					alt: this.$store.getters.lang.quote.ornament.acrylicChristmasOrnaments,
				},
			];
			config.descriptionText = this.$store.getters.lang.quote.ornament.ornamentDescriptionText10;
			config.descriptionText2 = this.$store.getters.lang.quote.ornament.ornamentDescriptionText11;
			config.descriptionText3 = this.$store.getters.lang.quote.ornament.ornamentDescriptionText12;
			config.h1 = this.$store.getters.lang.quote.ornament.acrylicOrnamentsH1;
			config.allStepConfig = getOrnamentAllStepConfig();
			break;
		case "lang-quote-custom-laser-cut-ornaments":
			config = new Quote(625, "ornaments");
			config.leftImg = [
				{
					id: 1,
					url: "https://static-oss.gs-souvenir.com/web/quoteManage/20250122/Love_Laser_Cut_Ornaments_20250122cbx3eb.jpg",
					alt: this.$store.getters.lang.quote.ornament.loveLaserCutOrnaments,
				},
				{
					id: 2,
					url: "https://static-oss.gs-souvenir.com/web/quoteManage/20250122/Christmas_Bow_20250122QxJfBm.jpg",
					alt: this.$store.getters.lang.quote.ornament.christmasBow,
				},
				{
					id: 3,
					url: "https://static-oss.gs-souvenir.com/web/quoteManage/20250122/Love_Couple_Laser_Cut_Ornaments_20250122NaTPdE.jpg",
					alt: this.$store.getters.lang.quote.ornament.loveCoupleLaserCutOrnaments,
				},
				{
					id: 4,
					url: "https://static-oss.gs-souvenir.com/web/quoteManage/20250122/Dog_Laser_Cut_Ornaments_20250122Jnbspm.jpg",
					alt: this.$store.getters.lang.quote.ornament.dogLaserCutOrnaments,
				},
				{
					id: 5,
					url: "https://static-oss.gs-souvenir.com/web/quoteManage/20250122/Flower_Laser_Cut_Ornaments_20250122ineZ8c.jpg",
					alt: this.$store.getters.lang.quote.ornament.flowerLaserCutOrnaments,
				},
			];
			config.descriptionText = this.$store.getters.lang.quote.ornament.ornamentDescriptionText1;
			config.descriptionText2 = this.$store.getters.lang.quote.ornament.ornamentDescriptionText13;
			config.descriptionText3 = "";
			config.descriptionText4 = this.$store.getters.lang.quote.ornament.ornamentDescriptionText14;
			config.descriptionText5 = this.$store.getters.lang.quote.ornament.ornamentDescriptionText15;
			config.descriptionText6 = this.$store.getters.lang.quote.ornament.ornamentDescriptionText16;
			config.h1 = this.$store.getters.lang.quote.ornament.laserCutOrnamentsH1;
			config.allStepConfig = {
				quoteCategory: {
					videoFit: "cover",
					mediaConfig: {
						style: {
							"aspect-ratio": "240 / 210",
						},
					},
					stepContentConfig: {
						pc: {
							style: {
								"grid-template-columns": "repeat(3, 1fr)",
							},
						},
						ipad: {
							style: {
								"grid-template-columns": "repeat(3, 1fr)",
							},
						},
						mb: {
							style: {
								"grid-template-columns": "repeat(2, 1fr)",
							},
						},
					},
					stepItemStyle: {
						style: {
							"flex-direction": "column",
							borderRadius: "6px",
							overflow: "hidden",
						},
						stepImgWrapStyle: {
							style: {
								borderRadius: "10px",
							},
						},
						stepTextWrapStyle: {
							style: {
								flex: 1,
								width: "100%",
								padding: "10px",
								"margin-top": "0",
								borderRadius: "0 0 6px 6px",
							},
						},
					},
				},
				"Ornament Attachment": {
					pcActive2: true,
					mbActive2: true,
					previewModal: "hover",
					stepContentConfig: {
						pc: {
							style: {
								"grid-template-columns": "repeat(3, 1fr)",
							},
						},
						ipad: {
							style: {
								"grid-template-columns": "repeat(3, 1fr)",
							},
						},
						mb: {
							style: {
								"grid-template-columns": "repeat(3, 1fr)",
							},
						},
					},
					zoomIconConfig: {
						showZoomIcon: true,
						style: {
							color: "#666666",
						},
					},
					stepItemStyle: {
						stepImgWrapStyle: {
							style: {
								borderRadius: "16px 16px 0 0",
							},
						},
						mbStepImgWrapStyle:{
							style: {
								borderRadius: "8px 8px 0 0",
							},
						}
					},
				},
				"Ornament Package": {
					pcActive2: true,
					mbActive2: true,
					stepContentConfig: {
						pc: {
							style: {
								"grid-template-columns": "repeat(3, 1fr)",
							},
						},
						ipad: {
							style: {
								"grid-template-columns": "repeat(3, 1fr)",
							},
						},
						mb: {
							style: {
								"grid-template-columns": "repeat(2, 1fr)",
							},
						},
					},
					stepItemStyle: {
						style: {
							overflow: "hidden",
						},
						stepImgWrapStyle: {
							style: {
								borderRadius: "10px 10px 0 0",
							},
						}
					},
				},
			};
			break;
		case "lang-quote-printed-wristbands":
			config = new Quote(749, "wristband");
			config.allStepConfig = getWristbandAllStepConfig();
			break;
		//袖扣
		case "lang-quote-custom-3d-cufflinks":
			config = new Quote(761, "custom 3d cufflinks");
			config.allStepConfig = getCufflinksAllStepConfig.call(this);
			break;
		case "lang-quote-custom-engraved-cufflinks":
			config = new Quote(763, "custom engraved cufflinks");
			config.allStepConfig = getCufflinksAllStepConfig.call(this);
			break;
		case "lang-quote-custom-picture-cufflinks":
			config = new Quote(764, "custom picture cufflinks");
			config.allStepConfig = getCufflinksAllStepConfig.call(this);
			break;
		case "lang-quote-custom-cutout-cufflinks":
			config = new Quote(762, "custom cutout cufflinks");
			config.allStepConfig = getCufflinksAllStepConfig.call(this);
			break;
		case "lang-quote-custom-soft-enamel-cufflinks":
			config = new Quote(759, "custom soft enamel cufflinks");
			config.allStepConfig = getCufflinksAllStepConfig.call(this);
			break;
		case "lang-quote-custom-hard-enamel-cufflinks":
			config = new Quote(760, "custom hard enamel cufflinks");
			config.allStepConfig = getCufflinksAllStepConfig.call(this);
			break;
		//指尖陀螺
		case "lang-quote-3d-fidget-spinner-quote":
			config = new Quote(231, "3d-fidget-spinner-quote");
			config.stepSizeTitle = this.$store.getters.lang.quote.fidgetSpinner.stepSizeTitle;
			config.p1Text = this.$store.getters.lang.quote.fidgetSpinner.p1;
			config.allStepConfig = getFidgetSpinnerAllStepConfig.call(this);
			break;
		case "lang-quote-die-struck-fidget-spinner-quote":
			config = new Quote(229, "die-struck-fidget-spinner-quote");
			config.stepSizeTitle = this.$store.getters.lang.quote.fidgetSpinner.stepSizeTitle;
			config.p1Text = this.$store.getters.lang.quote.fidgetSpinner.p1;
			config.allStepConfig = getFidgetSpinnerAllStepConfig.call(this);
			break;
		case "lang-quote-enamel-fidget-spinner-quote":
			config = new Quote(227, "enamel-fidget-spinner-quote");
			config.stepSizeTitle = this.$store.getters.lang.quote.fidgetSpinner.stepSizeTitle;
			config.p1Text = this.$store.getters.lang.quote.fidgetSpinner.p1;
			config.allStepConfig = getFidgetSpinnerAllStepConfig.call(this);
			break;
		case "lang-quote-glow-in-the-dark-fidget-spinner-quote":
			config = new Quote(233, "glow-in-the-dark-fidget-spinner-quote");
			config.stepSizeTitle = this.$store.getters.lang.quote.fidgetSpinner.stepSizeTitle;
			config.p1Text = this.$store.getters.lang.quote.fidgetSpinner.p1;
			config.allStepConfig = getFidgetSpinnerAllStepConfig.call(this);
			break;
		case "lang-quote-rhinestone-fidget-spinner-quote":
			config = new Quote(235, "rhinestone-fidget-spinner-quote");
			config.stepSizeTitle = this.$store.getters.lang.quote.fidgetSpinner.stepSizeTitle;
			config.p1Text = this.$store.getters.lang.quote.fidgetSpinner.p1;
			config.allStepConfig = getFidgetSpinnerAllStepConfig.call(this);
			break;
		case "lang-quote-belt-buckles-quote":
			config = new Quote(53, "belt buckles quote");
			config.allStepConfig = getBeltBucklesAllStepConfig.call(this);
			break;
		case "lang-quote-buttonBadges-quote":
			config = new Quote(160, "buttonBadges");
			config.allStepConfig = getButtonBadgesAllStepConfig.call(this);
			break;
		case "lang-quote-custom-pvc-luggage-tags":
			config = new Quote(526, "custom luggage tags");
			config.allStepConfig = getPvcLuggageTagsAllStepConfig.call(this);
			break;
		case "lang-quote-embroideredBadges-quote":
			config = new Quote(159, "embroideredBadges");
			config.allStepConfig = getEmbroideredBadgesAllStepConfig.call(this);
			break;
		case "lang-quote-ornament-quote":
			config = new Quote(201, "ornament");
			config.allStepConfig = getOldOrnamentAllStepConfig.call(this);
			break;
	}
	if(!config){
		throw new Error("Quote config not found.")
	}
	config.sizeType = config?.sizeType || "normal";
	return config;
};
