<template>
	<BaseDialog :value="visible" @update="closeDialog" class="neonAiPreviewDialog" :width="customWidth">
		<div class="content">
			<div class="left">
				<div class="myswiperWrap">
					<div class="swiper myswiper" ref="swiper">
						<div class="swiper-wrapper">
							<div class="swiper-slide promptItem" v-for="(item, index) in previewData.imgList" :key="index">
                                <div class="toolBtn">
                                    <b class="icon-a-fuzhi1 hoverStyle" @click="copy(item)"></b>
                                    <b class="icon-Download hoverStyle" @click="down(item)"></b>
                                </div>
								<img :src="item.imageOssUrl" :alt="item.imageFilename" :title="item.imageFilename" />
							</div>
						</div>
					</div>
					<div class="swiper-button-next">
						<img src="https://static-oss.gs-souvenir.com/web/quoteManage/20240905/Right_Toggle_Button_20240905KiJQ6t.png" alt="rightBtn" />
					</div>
					<div class="swiper-button-prev">
						<img src="https://static-oss.gs-souvenir.com/web/quoteManage/20240905/Left_Toggle_Button_20240905wjrTGT.png" alt="leftBtn" />
					</div>
				</div>
				<div class="btnWrap">
					<button primary @click="toNeonQuoteDialog(previewData.imgList[0].imageOssUrl)">Free Quote</button>
				</div>
			</div>
			<div class="right">
				<div class="time">{{ previewData.timeDes }}</div>
				<div class="des">
					<div class="prompt">
						{{ previewData.inputValue }}
					</div>
					<div class="list">
						<img v-for="(item, index) in previewData.imgList" :key="index" :src="item.imageOssUrl" :alt="item.imageFilename" :title="item.imageFilename" @click="slideToImg(index)" />
					</div>
				</div>
			</div>
		</div>
		<template #closeIcon>
			<div class="close-icon">
				<b class="icon-guanbi" @click.stop="closeDialog(false)"></b>
			</div>
		</template>
	</BaseDialog>
</template>

<script>
import BaseDialog from "@/components/Quote/BaseDialog.vue";
import {base64toBlob, copyContent, newDownFile} from "@/utils/utils";

export default {
	props: {
		visible: {
			type: Boolean,
		},
		previewData: {
			type: Object,
			default: () => {
				return {};
			},
		},
	},
	data() {
		return {
			mySwiper: null,
		};
	},
	components: { BaseDialog },
    computed:{
        device(){
            return this.$store.state.device
        },
        customWidth(){
            return this.device==='pc'?"78em":"95%"
        }
    },
	methods: {
        toNeonQuoteDialog(url) {
            let uploadList = [
                {
                    original_filename:"neon ai img",
                    secure_url: url
                }
            ]
            this.$store.commit("setMask", {
                modal: "modalQuoteDialog",
                quoteUrl:`/quote/neon-signs?id=13729&uploadList=${JSON.stringify(uploadList)}&type=quoteIframe`
            });
        },
		closeDialog(val) {
			this.$emit("update:visible", val);
		},
		copy(item) {
			let url = item.imageOssUrl;
			if (!navigator.clipboard) {
				this.$toast.error("Copy not supported by the browser");
				return false;
			}
            copyContent(url);
			this.$toast.error("Copy successful");
		},
		down(item) {
			newDownFile(item.imageOssUrl, "neon_ai");
		},
		slideToImg(index) {
			this.mySwiper.slideTo(index);
		},
	},
	mounted() {
		this.mySwiper = new Swiper(this.$refs.swiper, {
			slidesPerView: 1,
			spaceBetween: 0,
			watchSlidesVisibility: true,
			slideToClickedSlide: true,
			navigation: {
				nextEl: ".myswiperWrap .swiper-button-next",
				prevEl: ".myswiperWrap .swiper-button-prev",
			},
		});
		this.mySwiper.slideTo(this.previewData.ind);
	},
};
</script>

<style lang="scss" scoped>
.neonAiPreviewDialog {
	.close-icon{
		width: 2em;
		line-height: 2em;
		text-align: center;
		border-radius: 50%;
		background: #1D2129;
		position: absolute;
		margin: 5px;
		transform: translate(50%, -50%);
		right: 0;
		top: 0;
		color: #ffffff;
		cursor: pointer;

		@include respond-to(mb) {
			transform: translate(0%, 0%);
			border-radius: 0;
			background: transparent;
		}
	}

	::v-deep .base-dialog-model-con {
		background: #1d2129 !important;
	}

	.content {
		display: grid;
		grid-template-columns: 2fr 1fr;
		gap: 7em;
		padding: 2em;

        @include respond-to(mb){
            grid-template-columns: 1fr;
            padding: 1em;
            gap: 2em;
        }

		.left {
			min-width: 0;

			.myswiperWrap {
				position: relative;
				padding: 0 5em;

                @include respond-to(mb){
                   padding: 0 2em;
                }

				.swiper-button-next::after,
				.swiper-button-prev::after {
					display: none;
				}

				.swiper-button-next,
				.swiper-button-prev {
					width: 2.63em;
					@include respond-to(mb) {
						width: 3.58em;
						img {
							background: rgba(0, 0, 0, 0.4);
							border-radius: 50%;
						}
					}
				}
			}

			.btnWrap {
				display: flex;
				justify-content: center;
				align-items: center;
				padding: 1em 1em 0;

				button {
					height: 2.5em;
				}
			}

			.promptItem {
				position: relative;

				img {
					max-width: 100%;
					max-height: 100%;
					object-fit: contain;
				}

				.toolBtn {
					display: flex;
					justify-content: space-between;
					align-items: center;
					position: absolute;
					right: 0.6em;
					top: 0.6em;
					transition: all 0.3s;
                    width: max-content;
					background: rgba(0, 0, 0, 0.6);
					border-radius: 0.38em;
					border: 0.06em solid #39414e;
					padding: 0.2em 0.5em;

                    @include respond-to(mb){
                        position: relative;
                        top: auto;
                        right: auto;
                        margin: 0 auto 1em;
                        background: #272C36;
                    }

					b {
                        margin: 0 0.5em;
						color: #ffffff;
						cursor: pointer;

                        @include respond-to(mb){
                            font-size: 1.25em;
                        }
					}
				}
			}
		}

		.right {
			min-width: 0;

			.time {
				font-weight: 700;
				font-size: 1.25em;
				color: #ffffff;
				margin-bottom: 0.4em;
			}

			.des {
				padding: 1.5em 0.8em;
				background: #272c36;
				border-radius: 0.38rem;
				border: 0.06rem solid #39414e;
				color: #ffffff;

				.prompt {
					margin-bottom: 1em;
				}

				.list {
					display: grid;
					grid-template-columns: repeat(4, 1fr);
					gap: 0.2em;

					img {
						cursor: pointer;
					}
				}
			}
		}
	}
}
</style>
