<template>
	<PublicStep :config="config" :stepData="stepData" :maskName="maskName" :selectedData="selectedData" @clickFun="selectQuoteParamsFunc(stepData, $event)">
		<template #content-extend="{ stepData }">
			<div class="border-color" v-show="showBorderColor">
				<div>Select Border Color</div>
				<div class="color-fixedColor">
					<div v-for="(item, index) in colorList"
						 :key="index"
						 class="color-fixedColor-1"
						 @click="borderColorClick(item)"
					>
						<div class="div2"
							 :class="{ imgStyle: isImgStyle && item.paramName === 'Recommend', codeStyle: !isImgStyle && item.paramName === 'Recommend' }"
							 :style="item.icon && item.paramName !== 'Recommend' ? 'border: 2px solid #D17B5C' : 'border: 1px solid #C1C1C1'"
						>
							<div :style="{ background: customColor.code }" v-show="item.paramName === 'Recommend'"></div>
							<el-image
								:src="JSON.parse(item.imageJson)[0].url"
								:alt="item.paramName"
								:title="item.paramName"
							></el-image>
							<i class="el-icon-check" v-show="item.icon"></i>
						</div>
						<PriceText :paramData="item"></PriceText>
					</div>
				</div>
				<div class="colourAtla" v-show="showColourAtla">
					<div class="colourAtla_title">Select Border Color</div>
					<div class="colourAtla_div">
						<div
							v-for="(item, index) in colourAtlaList"
							:key="index"
							class="div2"
							@click="colourAtlaClick(item)"
						>
							<div :style="{ backgroundColor: item.code }" class="div3" :title="item.pantone"></div>
						</div>
					</div>
				</div>
			</div>
		</template>
		<template #footer>
			<div class="stepFooter" v-if="config.showNextBtn">
				<QuoteBtn  :style="{
                    opacity: selectedParamsValue?.colorValue || selectedParamsValue?.paramName === 'Heat Cut' ? 1 : 0.5,
                }" @click.native="showMaskFn(stepData.paramName)" :disabled="selectedData[stepData.paramName].colorValue === null">
					{{ lang.next }}
				</QuoteBtn>
			</div>
		</template>
	</PublicStep>
</template>

<script>
import PriceText from "@/components/Quote/PriceText";
import PublicStep from "@/components/Quote/PublicStep/index.vue";
import QuoteBtn from "@/components/Quote/QuoteBtn.vue";
export default {
    components: {
		QuoteBtn,
        PriceText,
		PublicStep
    },
    props: {
		config:{
			type: Object
		},
        colourAtlaList: {
            typeof: Array,
            default: () => []
        },
        customColor: {
            typeof: Object,
            default: () => {}
        },
        isImgStyle: {
            typeof: Boolean,
            default: true
        },
        showColourAtla: {
            typeof: Boolean,
            default: false
        },
        stepData:{
            typeof: Object,
            default: () => {}
        },
        maskName: {
			type: [String, Boolean],
		},
        selectedData: {
			type: Object,
		},
        showBorderColor:{
            typeof: Boolean,
            default:false,
        },
        colorList:{
            typeof: Array,
            default: () => []
        },
        selectedParamsValue:{
            typeof: Object,
            default: () => {}
        }
    },
    computed:{
        lang() {
			return this.$store.getters.lang.quote || {};
		},
    },
    methods: {
        colourAtlaClick(item) {
            this.$emit("colourAtlaClick", item);
        },
        selectQuoteParamsFunc(item, itemChild) {
            this.$emit("selectQuoteParamsFunc", item, itemChild);
        },
        closeMask(){
            this.$emit("closeMask");
        },
        borderColorClick(item){
            this.$emit("borderColorClick",item)
        },
        showMaskFn(paramName){
            this.$emit("showMaskFn",paramName)
        }
    }
}
</script>

<style scoped lang="scss">
.border-color {
	border: 1px solid #c1c1c1;
	padding: 18px 14px;
	font-size: 18px;
	margin-top: 20px;
	height: 100%;
	position: relative;

	.color-fixedColor {
		display: grid;
		grid-template-columns: repeat(14, 1fr);
		row-gap: 7.3333vw;
		margin-top: 20px;

		.imgStyle {
			width: 36px;
			height: 36px;
			margin-left: 20px;
		}

		.codeStyle {
			width: 36px;
			height: 36px;
			position: relative;
			border: 2px solid #d17b5c !important;
			padding: 2px !important;
			margin-left: 20px;

			div {
				width: 28px;
				height: 27px;
				border-radius: 50%;
				border: 1px solid #c6c7c8;
			}

			.el-image {
				width: 15px;
				height: 15px;
				position: absolute;
				right: 0;
				bottom: 0;
				border: 0;
			}

			.el-icon-check {
				position: absolute;
				top: 7px;
				right: 8px;
				color: #fff;

				@media screen and (max-width: 767px) {
					font-size: 17px;
				}
			}
		}

		.div2 {
			width: 35px;
			height: 35px;
			border: 1px solid #c6c7c8;
			border-radius: 50%;
			padding: 3px;
			background-color: #ffffff;
			margin: 0 auto;
			display: flex;
			justify-content: center;
			align-items: center;

			.div3 {
				width: 30px;
				height: 30px;
				border-radius: 50%;
				cursor: pointer;
			}
		}

		.color-fixedColor-1 {
			text-align: center;
			cursor: pointer;
			position: relative;
			margin: 0 auto;

			.el-icon-check {
				position: absolute;
				color: #fff;

				@media screen and (max-width: 767px) {
					font-size: 16px;
				}
			}
		}
	}

	.colourAtla {
		box-shadow: 0px 0px 7px 2px #ccc;
		margin-top: 20px;
		width: 29%;
		position: absolute;
		right: 30%;
		top: 53%;
		background: #fff;
		z-index: 10;

		@media screen and (max-width: 767px) {
			width: 89%;
			position: absolute;
			right: 0;
			top: 77%;
		}

		@media screen and (max-width: 1000px) {
			@media screen and (min-width: 768px) {
				width: 69%;
				position: absolute;
				right: 28%;
				top: 49%;
			}
		}

		.colourAtla_title {
			text-align: center;
			padding: 13px 0;

			@media screen and (max-width: 767px) {
				font-size: 15px;
			}
		}

		.colourAtla_div {
			height: 253px;
			display: flex;
			flex-wrap: wrap;
			overflow: auto;
			padding: 0 15px;

			.div2 {
				margin-right: 10px;
				margin-bottom: 10px;
				border: 1px solid #c6c7c8;
				border-radius: 50%;
				padding: 3px;
				background-color: #e3eefe;

				.div3 {
					width: 30px;
					height: 30px;
					border-radius: 50%;
					cursor: pointer;
				}
			}
		}
	}

	@media screen and (max-width: 767px) {
		height: 100%;
		font-size: 3.2vw;
		padding: 3.4vw 2.8667vw 4.4vw;
		margin-top: 10px;

		.color-fixedColor {
			margin-top: 2.6667vw;
			display: grid;
			grid-template-columns: repeat(5, 1fr);
			row-gap: 3.3333vw;
		}
	}

	@media screen and (max-width: 450px) {
		.color-fixedColor {
			display: grid;
			grid-template-columns: repeat(4, 1fr);
		}
	}
}
</style>
