#map_canvas img,
#map_canvas embed,
#map_canvas object,
.map_canvas img,
.map_canvas embed,
.map_canvas object {
	max-width: none !important;
}

.left {
	float: left !important;
}

.right {
	float: right !important;
}

.text-left {
	text-align: left !important;
}

.text-right {
	text-align: right !important;
}

.text-center {
	text-align: center !important;
}

.text-justify {
	text-align: justify !important;
}

.hide {
	display: none;
}

.antialiased {
	-webkit-font-smoothing: antialiased;
}

img {
	display: inline-block;
	vertical-align: middle;
}

textarea {
	height: auto;
	min-height: 50px;
}

select {
	width: 100%;
}

/* Grid HTML Classes */
.row {
	width: 100%;
	margin-left: auto;
	margin-right: auto;
	margin-top: 0;
	margin-bottom: 0;
	max-width: 62.5em;
	*zoom: 1;
}

.row:before,
.row:after {
	content: " ";
	display: table;
}

.row:after {
	clear: both;
}

.row.collapse .column,
.row.collapse .columns {
	position: relative;
	padding-left: 0;
	padding-right: 0;
	float: left;
}

.row .row {
	width: auto;
	margin-left: -0.9375em;
	margin-right: -0.9375em;
	margin-top: 0;
	margin-bottom: 0;
	max-width: none;
	*zoom: 1;
}

.row .row:before,
.row .row:after {
	content: " ";
	display: table;
}

.row .row:after {
	clear: both;
}

.row .row.collapse {
	width: auto;
	margin: 0;
	max-width: none;
	*zoom: 1;
}

.row .row.collapse:before,
.row .row.collapse:after {
	content: " ";
	display: table;
}

.row .row.collapse:after {
	clear: both;
}

.column,
.columns {
	position: relative;
	padding-left: 0.9375em;
	padding-right: 0.9375em;
	width: 100%;
	float: left;
}

@media only screen {

	.column,
	.columns {
		position: relative;
		padding-left: 0.9375em;
		padding-right: 0.9375em;
		float: left;
	}

	.small-1 {
		position: relative;
		width: 8.33333%;
	}

	.small-2 {
		position: relative;
		width: 16.66667%;
	}

	.small-3 {
		position: relative;
		width: 25%;
	}

	.small-4 {
		position: relative;
		width: 33.33333%;
	}

	.small-5 {
		position: relative;
		width: 41.66667%;
	}

	.small-6 {
		position: relative;
		width: 50%;
	}

	.small-7 {
		position: relative;
		width: 58.33333%;
	}

	.small-8 {
		position: relative;
		width: 66.66667%;
	}

	.small-9 {
		position: relative;
		width: 75%;
	}

	.small-10 {
		position: relative;
		width: 83.33333%;
	}

	.small-11 {
		position: relative;
		width: 91.66667%;
	}

	.small-12 {
		position: relative;
		width: 100%;
	}

	.small-offset-0 {
		position: relative;
		margin-left: 0%;
	}

	.small-offset-1 {
		position: relative;
		margin-left: 8.33333%;
	}

	.small-offset-2 {
		position: relative;
		margin-left: 16.66667%;
	}

	.small-offset-3 {
		position: relative;
		margin-left: 25%;
	}

	.small-offset-4 {
		position: relative;
		margin-left: 33.33333%;
	}

	.small-offset-5 {
		position: relative;
		margin-left: 41.66667%;
	}

	.small-offset-6 {
		position: relative;
		margin-left: 50%;
	}

	.small-offset-7 {
		position: relative;
		margin-left: 58.33333%;
	}

	.small-offset-8 {
		position: relative;
		margin-left: 66.66667%;
	}

	.small-offset-9 {
		position: relative;
		margin-left: 75%;
	}

	.small-offset-10 {
		position: relative;
		margin-left: 83.33333%;
	}

	[class*="column"]+[class*="column"]:last-child {
		float: right;
	}

	[class*="column"]+[class*="column"].end {
		float: left;
	}

	.column.small-centered,
	.columns.small-centered {
		position: relative;
		margin-left: auto;
		margin-right: auto;
		float: none !important;
	}
}

/* Styles for screens that are atleast 768px; */
@media only screen and (min-width: 768px) {
	.large-1 {
		position: relative;
		width: 8.33333%;
	}

	.large-2 {
		position: relative;
		width: 16.66667%;
	}

	.large-3 {
		position: relative;
		width: 25%;
	}

	.large-4 {
		position: relative;
		width: 33.33333%;
	}

	.large-5 {
		position: relative;
		width: 41.66667%;
	}

	.large-6 {
		position: relative;
		width: 50%;
	}

	.large-7 {
		position: relative;
		width: 58.33333%;
	}

	.large-8 {
		position: relative;
		width: 66.66667%;
	}

	.large-9 {
		position: relative;
		width: 75%;
	}

	.large-10 {
		position: relative;
		width: 83.33333%;
	}

	.large-11 {
		position: relative;
		width: 91.66667%;
	}

	.large-12 {
		position: relative;
		width: 100%;
	}

	.row .large-offset-0 {
		position: relative;
		margin-left: 0%;
	}

	.row .large-offset-1 {
		position: relative;
		margin-left: 8.33333%;
	}

	.row .large-offset-2 {
		position: relative;
		margin-left: 16.66667%;
	}

	.row .large-offset-3 {
		position: relative;
		margin-left: 25%;
	}

	.row .large-offset-4 {
		position: relative;
		margin-left: 33.33333%;
	}

	.row .large-offset-5 {
		position: relative;
		margin-left: 41.66667%;
	}

	.row .large-offset-6 {
		position: relative;
		margin-left: 50%;
	}

	.row .large-offset-7 {
		position: relative;
		margin-left: 58.33333%;
	}

	.row .large-offset-8 {
		position: relative;
		margin-left: 66.66667%;
	}

	.row .large-offset-9 {
		position: relative;
		margin-left: 75%;
	}

	.row .large-offset-10 {
		position: relative;
		margin-left: 83.33333%;
	}

	.row .large-offset-11 {
		position: relative;
		margin-left: 91.66667%;
	}

	.push-1 {
		position: relative;
		left: 8.33333%;
		right: auto;
	}

	.pull-1 {
		position: relative;
		right: 8.33333%;
		left: auto;
	}

	.push-2 {
		position: relative;
		left: 16.66667%;
		right: auto;
	}

	.pull-2 {
		position: relative;
		right: 16.66667%;
		left: auto;
	}

	.push-3 {
		position: relative;
		left: 25%;
		right: auto;
	}

	.pull-3 {
		position: relative;
		right: 25%;
		left: auto;
	}

	.push-4 {
		position: relative;
		left: 33.33333%;
		right: auto;
	}

	.pull-4 {
		position: relative;
		right: 33.33333%;
		left: auto;
	}

	.push-5 {
		position: relative;
		left: 41.66667%;
		right: auto;
	}

	.pull-5 {
		position: relative;
		right: 41.66667%;
		left: auto;
	}

	.push-6 {
		position: relative;
		left: 50%;
		right: auto;
	}

	.pull-6 {
		position: relative;
		right: 50%;
		left: auto;
	}

	.push-7 {
		position: relative;
		left: 58.33333%;
		right: auto;
	}

	.pull-7 {
		position: relative;
		right: 58.33333%;
		left: auto;
	}

	.push-8 {
		position: relative;
		left: 66.66667%;
		right: auto;
	}

	.pull-8 {
		position: relative;
		right: 66.66667%;
		left: auto;
	}

	.push-9 {
		position: relative;
		left: 75%;
		right: auto;
	}

	.pull-9 {
		position: relative;
		right: 75%;
		left: auto;
	}

	.push-10 {
		position: relative;
		left: 83.33333%;
		right: auto;
	}

	.pull-10 {
		position: relative;
		right: 83.33333%;
		left: auto;
	}

	.push-11 {
		position: relative;
		left: 91.66667%;
		right: auto;
	}

	.pull-11 {
		position: relative;
		right: 91.66667%;
		left: auto;
	}

	.column.large-centered,
	.columns.large-centered {
		position: relative;
		margin-left: auto;
		margin-right: auto;
		float: none !important;
	}

	.column.large-uncentered,
	.columns.large-uncentered {
		margin-left: 0;
		margin-right: 0;
		float: left !important;
	}

	.column.large-uncentered.opposite,
	.columns.large-uncentered.opposite {
		float: right !important;
	}
}