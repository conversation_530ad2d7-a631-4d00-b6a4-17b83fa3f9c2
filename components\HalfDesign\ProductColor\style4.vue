<template>
  <div class="mb-4" :class="stepData.styleName">
    <half-design-litter-title
      v-show="stepData.attributeTitle"
      :index="stepData.id"
      :selectedValue="shape"
      :data-name="`${stepData.styleClass + '_' + stepData.id}`"
      style="margin: 4px 0"
      :stepTitle="stepData.minStepTitle"
      >{{ stepData.attributeTitle }}
    </half-design-litter-title>
	<slot name="stepText"></slot>
    <div
      class="step-content"
      v-if="
        !stepData.isHideStep ||
        (stepData.isHideStep && stepData.productParamList.length > 1)
      "
    >
      <template v-for="(step, index) in stepData.productParamList">
        <v-tooltip max-width="250" top :disabled="!step.colorAlias">
          <template v-slot:activator="{ on, attrs }">
            <div
              class="step-item"
              v-bind="attrs"
              v-on="on"
              :class="{ active: index === selectIndex }"
              :style="{ background: inkColorData(step) }"
              :key="index"
              @click="selectStep(step, index)"
              :alt="step.sizeImg ? size.imgAltTitle : step.colorAlias"
              :title="step.sizeImg ? size.imgAltTitle : step.colorAlias"
            ></div>
          </template>
          <span>{{ step.colorAlias }}</span>
        </v-tooltip>
      </template>
    </div>
    <div class="errorTip">
      <v-alert dense outlined type="error">
        {{ langSemiCustom.errorTip }}
      </v-alert>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    stepData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      selectIndex: -1,
      selectItem: null,
    };
  },
  computed: {
    langSemiCustom() {
      return this.$store.getters.lang?.semiCustom;
    },
    shape() {
      return this.selectItem?.valueName;
    },
  },
  methods: {
    selectStep(item, index, state = false) {
      this.selectIndex = index;
      this.selectItem = item;
      this.$emit("selectStep", {
        type: this.stepData.attributeFlag,
        data: item,
        parent: this.stepData,
        id: this.stepData.id,
        firstSelect: state,
      });
    },
    selectDefault() {
      if (this.selectIndex <= -1) {
        this.selectStep(this.stepData.productParamList[0], 0, true);
      }
    },
    setActiveProductColor() {
      let newColorList = this.stepData.productParamList.filter((color) => {
        return color.isActivity == 1;
      });
      if (newColorList.length == 0) {
        return;
      }
      this.stepData.productParamList = newColorList;
    },
    inkColorData(step) {
      if (step.sizeImg) return `url(${step.sizeImg}) center / cover no-repeat`;
      return step.colorCode;
    },
  },
  mounted() {
    this.$Bus.$on("selectDefaultInkColorStep", this.selectDefault);
  },
  beforeDestroy() {
    this.$Bus.$off("selectDefaultInkColorStep", this.selectDefault);
  },
};
</script>
<style scoped lang="scss">
@import "~assets/css/half.scss";

.style1 .step-content {
  display: grid;
  grid-template-columns: repeat(auto-fill, 30px);
  grid-gap: 10px;

  .step-item {
    position: relative;
    border: 1px solid $border-color;
    cursor: pointer;
    aspect-ratio: 1;
    height: 30px;
    border-radius: 4px;

    &::after {
      width: auto;
      height: auto;
      display: none;
      content: "";
      position: absolute;
      left: -6px;
      top: -6px;
      right: -6px;
      bottom: -6px;
      border: 2px solid $color-primary;
      border-radius: 4px;
    }

    @media (any-hover: hover) {
      &:hover {
        border: none;
      }
    }
  }

  .step-item.active {
    box-shadow: 0 3px 3px -2px rgba(0, 0, 0, 0.2), 0 3px 4px 0 rgba(0, 0, 0, 0.14),
      0 1px 8px 0 rgba(0, 0, 0, 0.12);
    border: none;
    &::after {
      display: block;
    }
  }
}

@include respond-to(mb) {
  .style1 .step-content {
    grid-template-columns: repeat(auto-fill, 30px);
  }
}
</style>
