<template>
	<div class="quoteWrap"  :class="[modal.class]">
		<div class="home modal-box" v-for="(l, li) in modal.list" :style="modal.style" :key="li">
			<div class="banner" @click="setModalType(l.banner, modal.list, 'banner')" v-if="l.banner?.value">
				<pic :src="l.banner.value" :alt="l.banner.alt" :style="modal.homeImgStyle" />
			</div>
			<div class="banner" @click="setModalType(l.video, modal.list, 'video')" v-else-if="l.video?.value" @mouseenter="setModalType(l.video, null, 'video', { type: 'enter' })">
				<video :src="l.video.value" :title="l.video.alt" :poster="l.video?.poster" :style="modal.homeVideoStyle" :loop="!modal.mousePlay" autoplay muted playsinline></video>
			</div>
			<div class="banner" @click="setModalType(l.imgList, modal.list, 'img_list', {}, li)" v-else-if="l.imgList">
				<pic v-for="(i, ii) in l.imgList" :src="i.value" :alt="i.alt" :key="i.value" :style="modal.homeImgStyle" v-show="bannerIndex == ii" preload="auto" />
			</div>
			<div flex class="content" :style="{ ...modal.contentStyle, ...l.contentStyle }">
				<div class="textContent">
					<EditDiv :tagName="l.title.tagName || 'h2'" v-model:content="l.title.value" :style="modal.titleStyle" @click="setModalType(l.title, modal.list, 'text')" v-if="l.title?.value" />
					<EditDiv class="describe" :tagName="l.subTitle.tagName" v-model:content="l.subTitle.value" :style="modal.subTitleStyle" @click="setModalType(l.subTitle, modal.list, 'text')" v-if="l.subTitle?.value"></EditDiv>
                    <div class="swiper-area">
                        <div class="myswiper2">
                            <div class="swiper" ref="swiper2">
                                <div class="swiper-wrapper">
                                    <div class="swiper-slide" v-for="(item, index) in imageJson" :key="index">
                                        <PicZoom :url="item.url" :alt="item.alt" :width="300" :scale="2" type="round"></PicZoom>
                                    </div>
                                </div>
                            </div>
                            <div class="swiper-button-next">
                                <img src="https://static-oss.gs-souvenir.com/web/quoteManage/20240905/Right_Toggle_Button_20240905KiJQ6t.png" alt="rightBtn" />
                            </div>
                            <div class="swiper-button-prev">
                                <img src="https://static-oss.gs-souvenir.com/web/quoteManage/20240905/Left_Toggle_Button_20240905wjrTGT.png" alt="leftBtn" />
                            </div>
                        </div>
                        <div class="myswiper1">
                            <div class="swiper" ref="swiper1">
                                <div class="swiper-wrapper">
                                    <div class="swiper-slide" v-for="(item, index) in imageJson" :key="index">
                                        <img :src="item.urlThumb || item.url" :alt="item.alt" :title="item.alt" />
                                        <div class="check">
                                            <b class="icon-check"></b>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="btnWrap">
                        <button primary @click.stop="selectShape" :disabled="disabledBtn">{{lang.selectShape}}</button>
                    </div>
                </div>
                <div class="rightArea">
                    <div class="quoteContent" ref="excludeElement">
                        <QuoteStep ref="quoteStep" :pid="modal.quotePid" :cateId="modal.quoteCateId" :uploadType="uploadType" :configStyle="modal.styleConfig || {}" @click.native="setModalType({}, modal.list, 'quote_table')"></QuoteStep>
                    </div>
                </div>
			</div>
		</div>
	</div>
</template>

<script>
import QuoteStep from "@/components/Quote/QuickQuote/QuoteStep.vue";
import VideoPlayer from "@/components/Quote/VideoPlayer.vue";
import {scrollToViewCenter} from "@/utils/utils";
import {getInfo} from "@/api/pins";
export default {
	props: {
		data: {
			type: Object,
			default: {},
		},
	},
	data() {
		return {
            disabledBtn: false,
            templateIdList:[16458,16459,16460,16461,16462,16463,28619,28608,23645,28620,28610,23647,23653,28611,24696,28609,24697,28612,28613,28607,28614,28615,28616, 28617,28618],
            activeIndex: 0,
            uploadType:1,
            imageJson: [],
			modal: {
                styleConfig:{},
				productImgStyle: {},
				homeImgStyle: {},
				style: {},
				type: {},
				list: [],
				...this.data,
			}
		};
	},
	computed: {
		lang() {
			return this.$store.getters.lang.quote || {};
		},
        device() {
            return this.$store.state.device;
        }
	},
	components: {
		VideoPlayer,
		QuoteStep,
	},
	watch: {
		modal: {
			handler(val) {
				if (val.list?.find((i) => i.imgList)) this.bannerLength = val.list.find((i) => i.imgList).imgList.length;
				if (process.env.isManage) this.$emit("update:data", val);
			},
			immediate: true,
			deep: true,
		},
	},
	methods: {
        selectShape() {
            scrollToViewCenter(document.querySelector(".quoteContent"));
            this.$refs.quoteStep[0].initQuote({
                shapeId: this.templateIdList[this.activeIndex]
            })
            this.$refs.quoteStep[0].initShowSelected();
            this.disabledBtn = true;
        },
		setModalType(target, targetArray, clickType, event, other) {
			this.$setModal(this, target, targetArray, clickType, event, other);
            if(!this.$store.getters.isManage){
                this.$refs.quoteStep[0].initShowSelected();
            }
		},
        initSwiper() {
            this.myswiper1 = new Swiper(this.$refs.swiper1, {
                slidesPerView: 4,
                spaceBetween: 8,
                watchSlidesVisibility: true, //防止不可点击
                grabCursor: true,
            });
            this.myswiper2 = new Swiper(this.$refs.swiper2, {
                slidesPerView: 1,
                spaceBetween: 10,
                autoplay: false,
                grabCursor: true,
                observer: true,
                observeParents: true,
                thumbs: {
                    swiper: this.myswiper1,
                },
                navigation: {
                    nextEl: ".myswiper2 .swiper-button-next",
                    prevEl: ".myswiper2 .swiper-button-prev",
                },
                on: {
                    slideChange:  (val)=>{
                        this.activeIndex = val.activeIndex;
                        this.$refs.quoteStep[0].initQuote({
                            shapeId: this.templateIdList[this.activeIndex]
                        })
                        this.$refs.quoteStep[0].startAnimation();
                        this.disabledBtn = false;
                    },
                }
            });
        },
	},
    async mounted() {
        let res = await getInfo({ id: this.modal.quoteCateId });
        this.imageJson = res.data.imageJson ? JSON.parse(res.data.imageJson) : [];
        this.$nextTick(() => {
            this.initSwiper();
        });
	},
};
</script>

<style lang="scss" scoped>
button[disabled]{
    pointer-events: none;
    opacity: 0.7;
}
.mySwiper {
	--swiper-navigation-color: #ffffff;
	/* 单独设置按钮颜色 */
	--swiper-navigation-size: 20px;

	/* 设置按钮大小 */
	.swiper-button-next,
	.swiper-button-prev {
		width: 46px;
		height: 46px;
		background: #000000;
		opacity: 0.4;
		border-radius: 50%;
		transform: translateY(-50%);
		margin-top: 0;

		@include respond-to(mb) {
			width: 30px;
			height: 30px;
		}
	}
	.swiper-button-prev {
		left: -70px;

		@include respond-to(mb) {
			left: 0;
		}
	}
}
.quoteWrap {
	.home {
		position: relative;

		.banner {
			position: absolute;
			inset: 0;
			img {
				width: 100%;
				height: 100%;
				object-fit: cover;
			}
		}

		.content {
            position: relative;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 6.25rem;
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;

            @include respond-to(mb) {
                grid-template-columns: repeat(1, 1fr);
                gap: 1rem;
            }

			.textContent {
				min-width: 0;
                color: #fff;

                .btnWrap{
                    text-align: center;
                }

                .swiper-area {
                    margin: 1.25rem 0;
                    width: 100%;

                    .myswiper2 {
                        width: 100%;
                        margin: 0 auto;
                        position: relative;

                        @include respond-to(mb){
                            width: 100%;
                        }

                        .swiper-slide{
                            display: flex;
                            justify-content: center;
                            align-items: center;

                             ::v-deep .pic-img .img-container{
                                height: 20rem;

                                 @include respond-to(mb){
                                     height: 15rem;
                                 }
                            }
                        }

                        .swiper-button-next::after,
                        .swiper-button-prev::after {
                            display: none;
                        }

                        .swiper-button-next,
                        .swiper-button-prev {
                            width: 3rem;
                            @include respond-to(mb) {
                                width: 2rem;
                            }
                        }
                    }

                    .myswiper1 {
                        margin: 1.25rem auto 0;
                        width: 80%;

                        @include respond-to(mb) {
                            padding: 0;
                            width: 100%;
                        }

                        .swiper-slide {
                            position: relative;
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            border-radius: 8px;
                            border: 1px solid transparent;
                            width: 100%;
                            height: 6rem;
                            padding: 0.625rem 0;
                            background-color: rgba(0, 0, 0, 0.25);
                            box-sizing: border-box;
                            cursor: pointer;

                           .check{
                               display: none;
                               position: absolute;
                               top: 0;
                               right: 0;
                               width: 15px;
                               height: 15px;
                               line-height: 15px;
                               font-size: 12px;
                               text-align: center;
                               background-color: $color-primary;
                               color: #ffffff;
                               border-radius: 0 6px 0 6px;
                           }

                            img{
                                width: 100%;
                                height: 100%;
                                object-fit: contain;
                            }

                            @include respond-to(mb){
                                background-color: rgba(255,255,255,0.25);
                            }

                            &.swiper-slide-thumb-active {
                                border-color: $color-primary;

                                .check{
                                    display: block;
                                }
                            }
                        }
                    }
                }

				@include respond-to(mb) {
					padding-top: 0;
					margin-bottom: 10px;
				}
			}

            .rightArea {
                min-width: 0;
                @include respond-to(mb) {
                    .stepBar {
                        display: none;
                    }
                }
            }
		}
	}
}
</style>
