<template>
	<div class="checkIcon">
		<i class="el-icon-check"></i>
	</div>
</template>

<script>
export default {};
</script>

<style scoped lang="scss">
.absolute-top-right {
	position: absolute;
	right: 0;
	top: 0;
}

.checkIcon {
	display: flex;
	justify-content: center;
	align-items: center;
	width: 22px;
	height: 22px;
	background: var(--color-primary, transparent);
	border-radius: 0 4px 0 4px;
    i{
        color: #ffffff;
    }
	@include respond-to(mb) {
		width: 16px;
		height: 16px;
		border-radius: 0 2px 0 2px;
	}
}
</style>
