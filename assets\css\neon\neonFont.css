@font-face {
	font-family: "Google Sans";
	src: url("https://static-oss.gs-souvenir.com/static/font/neon/google-sans.ttf");
}

@font-face {
	font-family: "Google Sans Lighter";
	src: url("https://static-oss.gs-souvenir.com/static/font/neon/NunitoSans-ExtraLight.ttf");
}

@font-face {
	font-family: "Alexa";
	src: url("https://static-oss.gs-souvenir.com/static/font/neon/Alexa.ttf");
}

@font-face {
	font-family: "Amanda";
	src: url("https://static-oss.gs-souvenir.com/static/font/neon/Amanda.ttf");
}

@font-face {
	font-family: "Amsterdam";
	src: url("https://static-oss.gs-souvenir.com/static/font/neon/Amsterdam.ttf");
}

@font-face {
	font-family: "Austin";
	src: url("https://static-oss.gs-souvenir.com/static/font/neon/Austin.ttf");
}

@font-face {
	font-family: "Avante";
	src: url("https://static-oss.gs-souvenir.com/static/font/neon/Avante.ttf");
}

@font-face {
	font-family: "Barcelona";
	src: url("https://static-oss.gs-souvenir.com/static/font/neon/Barcelona.ttf");
}

@font-face {
	font-family: "Bayview";
	src: url("https://static-oss.gs-souvenir.com/static/font/neon/Bayview.ttf");
}

@font-face {
	font-family: "NewCursive";
	src: url("https://static-oss.gs-souvenir.com/static/font/neon/NewCursive.ttf");
}

@font-face {
	font-family: "Beachfront";
	src: url("https://static-oss.gs-souvenir.com/static/font/neon/Beachfront.ttf");
}

@font-face {
	font-family: "Bellview";
	src: url("https://static-oss.gs-souvenir.com/static/font/neon/Bellview.ttf");
}

@font-face {
	font-family: "Buttercup";
	src: url("https://static-oss.gs-souvenir.com/static/font/neon/Buttercup.ttf");
}

@font-face {
	font-family: "Chelsea";
	src: url("https://static-oss.gs-souvenir.com/static/font/neon/Chelsea.ttf");
}

@font-face {
	font-family: "ClassicType";
	src: url("https://static-oss.gs-souvenir.com/static/font/neon/ClassicType.ttf");
}

@font-face {
	font-family: "Freehand";
	src: url("https://static-oss.gs-souvenir.com/static/font/neon/Freehand.ttf");
}

@font-face {
	font-family: "Freespirit";
	src: url("https://static-oss.gs-souvenir.com/static/font/neon/Freespirit.ttf");
}

@font-face {
	font-family: "Greenworld";
	src: url("https://static-oss.gs-souvenir.com/static/font/neon/Greenworld.ttf");
}

@font-face {
	font-family: "LoveNeon";
	src: url("https://static-oss.gs-souvenir.com/static/font/neon/LoveNeon.ttf");
}

@font-face {
	font-family: "LoveNote";
	src: url("https://static-oss.gs-souvenir.com/static/font/neon/LoveNote.ttf");
}

@font-face {
	font-family: "Marquee";
	src: url("https://static-oss.gs-souvenir.com/static/font/neon/Marquee.ttf");
}

@font-face {
	font-family: "Mayfair";
	src: url("https://static-oss.gs-souvenir.com/static/font/neon/Mayfair.ttf");
}

@font-face {
	font-family: "Melbourne";
	src: url("https://static-oss.gs-souvenir.com/static/font/neon/Melbourne.ttf");
}

@font-face {
	font-family: "Monaco";
	src: url("https://static-oss.gs-souvenir.com/static/font/neon/Monaco.ttf");
}

@font-face {
	font-family: "NeonGlow";
	src: url("https://static-oss.gs-souvenir.com/static/font/neon/NeonGlow.ttf");
}

@font-face {
	font-family: "NeonLite";
	src: url("https://static-oss.gs-souvenir.com/static/font/neon/NeonLite.ttf");
}

@font-face {
	font-family: "Neonscript";
	src: url("https://static-oss.gs-souvenir.com/static/font/neon/Neonscript.ttf");
}

@font-face {
	font-family: "Neontrace";
	src: url("https://static-oss.gs-souvenir.com/static/font/neon/Neontrace.ttf");
}

@font-face {
	font-family: "NeoTokyo";
	src: url("https://static-oss.gs-souvenir.com/static/font/neon/NeoTokyo.ttf");
}

@font-face {
	font-family: "Nevada";
	src: url("https://static-oss.gs-souvenir.com/static/font/neon/Nevada.ttf");
}

@font-face {
	font-family: "Sorrento";
	src: url("https://static-oss.gs-souvenir.com/static/font/neon/Sorrento.ttf");
}

@font-face {
	font-family: "Northshore";
	src: url("https://static-oss.gs-souvenir.com/static/font/neon/Northshore.ttf");
}

@font-face {
	font-family: "Photogenic";
	src: url("https://static-oss.gs-souvenir.com/static/font/neon/Photogenic.ttf");
}

@font-face {
	font-family: "Rocket";
	src: url("https://static-oss.gs-souvenir.com/static/font/neon/Rocket.ttf");
}

@font-face {
	font-family: "Royalty";
	src: url("https://static-oss.gs-souvenir.com/static/font/neon/Royalty.ttf");
}

@font-face {
	font-family: "SciFi";
	src: url("https://static-oss.gs-souvenir.com/static/font/neon/SciFi.ttf");
}

@font-face {
	font-family: "Signature";
	src: url("https://static-oss.gs-souvenir.com/static/font/neon/Signature.ttf");
}

@font-face {
	font-family: "Sorrento";
	src: url("https://static-oss.gs-souvenir.com/static/font/neon/Sorrento.ttf");
}

@font-face {
	font-family: "Typewriter";
	src: url("https://static-oss.gs-souvenir.com/static/font/neon/Typewriter.ttf");
}

@font-face {
	font-family: "Venetian";
	src: url("https://static-oss.gs-souvenir.com/static/font/neon/Venetian.ttf");
}

@font-face {
	font-family: "Vintage";
	src: url("https://static-oss.gs-souvenir.com/static/font/neon/Vintage.ttf");
}

@font-face {
	font-family: "Waikiki";
	src: url("https://static-oss.gs-souvenir.com/static/font/neon/Waikiki.ttf");
}

@font-face {
	font-family: "WildScript";
	src: url("https://static-oss.gs-souvenir.com/static/font/neon/WildScript.ttf");
}