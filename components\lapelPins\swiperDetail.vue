<template>
	<div class="swiperDetail">
		<div class="swiperBox">
			<div class="swiper myPinsSwiper2">
				<div class="swiper-wrapper">
					<div class="swiper-slide" v-for="(item, index) in imgList" :key="index">
						<img :src="item.url" />
						<b class="icon-fangda4" @click="fangdaImg(index)"></b>
					</div>
				</div>
				<div class="swiper-pagination swiper-pagination-pinsSwiper"></div>
				<div class="prevBtn" @click="swiperPrev"></div>
				<div class="nextBtn" @click="swiperNext"></div>
			</div>
			<div thumbsSlider="" class="swiper myPinsSwiper">
				<div class="swiper-wrapper">
					<div class="swiper-slide" v-for="(item, index) in imgList" :key="index">
						<img :src="item.url" />
					</div>
				</div>
			</div>
		</div>
		<div class="proNameBox" v-if="isMobile">
			<H1 class="cateName">{{ cateName }}</H1>
			<div class="sku">{{ lang.fdPins.Item }} {{ proSku }}</div>
		</div>
		<div class="iconBox">
			<div class="iconItem" v-for="item in iconList" :key="item.id">
				<div class="iconImg" :title="item.alt" :alt="item.alt">
					<b :class="{ [item.icon]: true }"></b>
				</div>
				<div class="iconText" :class="{ mbStyle: item.id == 4, oneStyle: item.id == 1 }">
					<span>{{ item.iconName }}</span>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	name: "swiperDetail",
	components: {},
	props: {
		imgList: {
			type: Array,
			default: () => [],
		},
		cateName: {
			type: String,
			default: "",
		},
		proSku: {
			type: String,
			default: "",
		},
	},
	data() {
		return {
			swiperDetail: null,
			swiperDetail2: null,
			iconList: [
				{
					id: 1,
					icon: "icon-a-FreeArtwork",
					iconName: "Free Artwork",
					alt: "free artwork",
				},
				{
					id: 2,
					icon: "icon-a-FastTurnaround",
					iconName: "Fast Turnaround",
					alt: "fast turnaround",
				},
				{
					id: 3,
					icon: "icon-a-FreeShipping4",
					iconName: "Free Shipping",
					alt: "free shipping",
				},
				{
					id: 4,
					icon: "icon-a-LowCost",
					iconName: "Low Cost",
					alt: "low cost",
				},
				{
					id: 5,
					icon: "icon-a-SatisfactionGuarantee",
					iconName: "Satisfaction Guarantee",
					alt: "satisfaction guarantee",
				},
			],
		};
	},
	watch: {
		imgList: {
			handler(val) {
				if (val.length) {
					this.$nextTick(() => {
						this.initSwiper();
					});
				}
			},
		},
	},
	computed: {
		isMobile() {
			return this.$store.getters.isMobile;
		},
		lang() {
			return this.$store.getters.lang.quote || {};
		},
		viewImgList() {
		    return this.imgList.map((item) => item.url)||[];
		}
	},
	methods: {
		initSwiper() {
			let _this = this;
			this.swiperDetail = new Swiper(".myPinsSwiper", {
				slidesPerView: this.isMobile ? 5 : 5,
				spaceBetween: this.isMobile ? 0 : 10,
				// freeMode: true,
				observer: true,
				watchSlidesProgress: true,
				grabCursor: true,
				direction: "horizontal",
			});
			this.swiperDetail2 = new Swiper(".myPinsSwiper2", {
				observer: true,
				slidesPerView: 1,
				thumbs: {
					swiper: this.swiperDetail,
				},
				pagination: {
					el: ".swiper-pagination-pinsSwiper",
					type: "fraction",
					currentClass: "my-pagination-current",
					totalClass: "my-pagination-total",
				},
				on: {
					init: function (swiper) {
						if (_this.isMobile) _this.updateArrowsVisibility(swiper);
						_this.getCurrentImg(swiper);
					},
					slideChange: function (swiper) {
						if (_this.isMobile) _this.updateArrowsVisibility(swiper);
						_this.getCurrentImg(swiper);
					},
				},
			});
		},
		updateArrowsVisibility(swiper) {
			try {
				const prevButton = document.querySelector(".prevBtn");
				const nextButton = document.querySelector(".nextBtn");
				prevButton.style.display = "block";
				nextButton.style.display = "block";
				if (swiper.isBeginning) {
					prevButton.style.display = "none";
				}
				if (swiper.isEnd) {
					nextButton.style.display = "none";
				}
			} catch (error) {
				console.log(error, "updateArrowsVisibility");
			}
		},
		swiperPrev() {
			this.swiperDetail2.slidePrev();
		},
		swiperNext() {
			this.swiperDetail2.slideNext();
		},
		getCurrentImg(swiper) {
			this.$nextTick(() => {
				try {
					const activeIndex = swiper.activeIndex;
					const currentSlide = swiper.slides[activeIndex];
					const imgElement = currentSlide.querySelector("img");
					const imgURL = imgElement ? imgElement.src : null;
					this.$emit("nowSiperImg", imgURL);
				} catch (error) {
					console.log(error, "getCurrentImg");
				}
			});
		},
		fangdaImg(index) {
			this.$viewerApi({
				images: this.viewImgList,
				options: {
					initialViewIndex:index,
					initialCoverage: 1,
				},
			});
		},
	},
	created() {},
	mounted() {},
};
</script>

<style scoped lang="scss">
.swiperDetail * {
	font-family: Calibri;
}
.swiperDetail {
	color: #333333;
	font-family: Calibri;
	.swiperBox {
		position: relative;
		width: 100%;
		padding: 4px 0;
		// @media (min-width: 1000px) and (max-width: 1500px) {
		// 	height: 32vw;
		// }
		// @media (min-width: 1500px) and (max-width: 1700px) {
		// 	height: 26vw;
		// }
		// @media (min-width: 1700px) {
		// 	height: 21vw;
		// }
		.swiper {
			width: 100%;
		}
		.swiper-slide {
			text-align: center;
			font-size: 18px;
			background: #fff;
			display: flex;
			justify-content: center;
			align-items: center;
			background-size: cover;
			background-position: center;
		}
		.swiper-slide img {
			user-select: none;
			display: block;
			width: 100%;
			height: 100%;
			object-fit: cover;
		}

		.prevBtn,
		.nextBtn {
			width: 18px;
			height: 4em;
			position: absolute;
			background: #445b7d;
			border-radius: 0px 4px 4px 0;
			z-index: 1;
			top: 50%;
			transform: translateY(-50%);
			cursor: pointer;
			&::after {
				content: "";
				position: absolute;
				top: 50%;
				left: 50%;
				transform: translate(-50%, -50%);
				border-top: 6px solid transparent;
				border-bottom: 6px solid transparent;
			}
			@include respond-to(mb) {
				width: 2.6em;
				min-width: 2.6em;
				height: 2.6em;
				background-color: #999999;
				top: calc(47.5vw - 2em) !important;
				right: initial;
			}
		}
		.prevBtn {
			left: 0;
			&::after {
				border-right: 8px solid #fff;
			}
			@include respond-to(mb) {
				border-radius: 50%;
				left: 0;
				&::after {
					content: "\e620";
					font-family: "modalicon";
					border: none;
					color: #fff;
					transform: translate(-50%, -50%) rotate(-90deg);
				}
			}
		}
		.nextBtn {
			right: 0;
			border-radius: 4px 0 0 4px;
			&::after {
				border-left: 8px solid #fff;
			}
			@include respond-to(mb) {
				border-radius: 50%;
				bottom: initial;
				right: 0;
				&::after {
					content: "\e620";
					font-family: "modalicon";
					border: none;
					color: #fff;
					transform: translate(-50%, -50%) rotate(90deg);
				}
			}
		}
		.swiper-pagination-pinsSwiper {
			display: none;
			position: absolute;
			z-index: 999;
			top: 40px;
			left: 50%;
			transform: translateX(-50%);
			color: #fff;
			font-size: 16px;
			@include respond-to(mb) {
				display: block;
				top: initial;
				left: initial;
				transform: none;
				right: 10px;
				bottom: 10px;
				padding: 0.2em 1em;
				width: fit-content;
				background-color: rgba(26, 26, 26, 0.6);
				border-radius: 23px;
				pointer-events: none;
			}

			::v-deep .swiper-pagination-bullet {
				height: 21px;
				width: 21px;
				opacity: 1;
				background-color: #b7b7b7;
				margin: 0 6px;

				&.swiper-pagination-bullet-active {
					// background-image: linear-gradient(90deg, #20aeff, #b61ee8);
				}

				@include respond-to(mb) {
					width: 10px;
					height: 10px;
				}
			}
		}

		.myPinsSwiper2 {
			.swiper-slide {
				position: relative;
				img {
					width: 100%;
					aspect-ratio: 1 / 1;
				}
				b {
					cursor: pointer;
					position: absolute;
					top: 10px;
					right: 10px;
					color: #ccc;
				}
			}
			.swiper-pagination {
				display: none;
				@include respond-to(mb) {
					display: block;
				}
			}
		}

		.myPinsSwiper {
			width: 100%;
			// max-height: 74.2%;
			box-sizing: border-box;
			.swiper-wrapper {
				.swiper-slide {
					position: relative;
					aspect-ratio: 1/1;
					img {
						padding: 2px;
						border: 2px solid transparent;
						aspect-ratio: 1/1;
						object-fit: contain;
						border: 1px solid #f0f0f0;
					}
					&.swiper-slide-thumb-active {
						&::after {
							display: none;
						}
						img {
							padding: 1px;
							border: 2px solid #689fd3;
						}
					}
				}
			}
		}
		@include respond-to(mb) {
			flex-direction: column;
			row-gap: 3vw;
			column-gap: initial;
			height: 120vw;
			.myPinsSwiper {
				width: 100%;
				height: 18vw;
				.swiper-wrapper {
					.swiper-slide {
						&::after {
							content: "";
							position: absolute;
							inset: 3px;
							border-radius: 8px;
							background-color: rgba(26, 26, 26, 0.4);
						}
						img {
							border-radius: 8px;
						}
						width: initial;
					}
				}
			}
			.myPinsSwiper2 {
				.swiper-slide {
					img {
						border-radius: 8px;
					}
					b {
						display: none;
					}
				}
			}
		}
	}
	.proNameBox {
		margin-top: 10px;
		.cateName {
			font-size: 24px;
			font-weight: bold;
		}
		.sku {
			font-weight: bold;
			font-size: 18px;
			color: #0066cc;
		}
	}
	.iconBox {
		margin-top: 20px;
		display: grid;
		grid-template-columns: repeat(5, 1fr);
		.iconItem {
			display: flex;
			align-items: center;
			justify-content: center;
			flex-direction: column;
			row-gap: 8px;
			.iconImg {
				display: flex;
				align-items: center;
				justify-content: center;
				font-size: 22px;
				width: 51px;
				height: 51px;
				background: #ffffff;
				border-radius: 50%;
				border: 1px solid #5b7aa9;
				.icon-a-FastTurnaround {
					margin-left: 3px;
				}
				b {
					margin-top: 2px;
					color: #5b7aa9;
				}
			}
			.iconText {
				font-weight: bold;
				font-size: 14px;
				word-break: break-word;
				padding: 0 0.8em;
				text-align: center;
				&.mbStyle {
					padding: 0 2em;
				}
			}
		}
		@include respond-to(mb) {
			grid-template-columns: repeat(5, 1fr);
			.iconItem {
				justify-content: space-between;
				.iconImg {
					font-size: 20px;
					width: 40px;
					height: 40px;
				}
				.iconText {
					padding: 0 1px;
					text-align: center;
					white-space: normal;
					overflow-wrap: break-word;
					&.oneStyle {
						padding: 0 0.5em;
					}
					&.mbStyle {
						padding: 0 1em;
					}
				}
			}
		}
	}
}
</style>
