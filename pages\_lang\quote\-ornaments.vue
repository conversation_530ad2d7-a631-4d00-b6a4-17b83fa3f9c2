<template>
	<div id="Ornaments" class="quoteWrap">
		<Preloader v-if="isLoading"></Preloader>
		<template v-else>
			<article class="content">
				<QuoteNav :pid="pid" :title="lang.ornament.quoteNavTitle" noPreview :config="{ slidesPerView: 6 }"></QuoteNav>
				<h1>{{ h1 }}</h1>
				<transition enter-active-class="animate__animated animate__faster animate__fadeInBottomLeft" leave-active-class="animate__animated animate__faster animate__fadeOutBottomLeft">
					<div class="leftArea" id="leftArea">
						<div class="swiper-area">
							<div class="swiper myswiper1" ref="swiper1">
								<div class="swiper-wrapper">
									<div class="swiper-slide" v-for="(item, index) in leftImg" :key="index">
										<img :src="item.url" :alt="item.alt" :title="item.alt" />
									</div>
								</div>
							</div>
							<div class="swiper myswiper2" ref="swiper2">
								<div class="swiper-wrapper">
									<div class="swiper-slide" v-for="(item, index) in leftImg" :key="index">
										<img :src="item.url" :alt="item.alt" :title="item.alt" style="width: 100%; height: 100%" />
									</div>
								</div>
								<div class="swiper-button-next"></div>
								<div class="swiper-button-prev"></div>
							</div>
						</div>
						<ul class="tip">
							<li>
								<b class="icon-jxsht-zdgl-dg"></b>
								<span
									><strong>{{ langSemi.free }}</strong> {{ langSemi.shipping }}</span
								>
							</li>
							<li>
								<b class="icon-jxsht-zdgl-dg"></b>
								<span
									><strong>{{ langSemi.free }}</strong> {{ langSemi.proof }}
								</span>
							</li>
							<li>
								<b class="icon-jxsht-zdgl-dg"></b>
								<span
									><strong>{{ lang.ornament.no }}</strong> {{ lang.ornament.moq }}</span
								>
							</li>
						</ul>
						<div class="productDes" v-if="device !== 'mb'">
							<div class="title">
								<strong>{{ lang.ornament.description }}:</strong>
							</div>
							<div class="des-con">
								<div style="margin-bottom: 10px">{{ descriptionText }}</div>
								<div style="margin-bottom: 10px">{{ descriptionText2 }}</div>
								<div style="margin-bottom: 10px">{{ descriptionText3 }}</div>
							</div>
							<div class="title" v-if="pid === 601">
								<strong>{{ descriptionText4 }}</strong>
							</div>
							<div class="des-con" v-if="pid === 601">
								<div style="margin-bottom: 10px">{{ descriptionText5 }}</div>
								<div style="margin-bottom: 10px">{{ descriptionText6 }}</div>
							</div>
							<div class="title" style="margin-top: 20px" v-if="pid === 625">
								<strong>{{ descriptionText4 }}</strong>
							</div>
							<div class="des-con" v-if="pid === 625">
								<div>{{ descriptionText5 }}</div>
								<div>{{ descriptionText6 }}</div>
							</div>
						</div>
					</div>
				</transition>
				<div class="rightArea" id="rightAreaCustom">
					<div class="rightArea_div">
						<div v-for="(item, index) in filterShowGeneralData" :key="item.id" :class="{ type1: picDialog, dd: showAcrylicOrnaments, kk: !showAcrylicOrnaments }" :style="item.paramName == 'Select Printed Area' ? 'margin: 0;grid-column-end: 1;grid-column-start: 3;background: transparent;' : item.paramName == 'Ornament Process' ? 'grid-column-end: 1;grid-column-start: 3;' : item.paramName === 'Ornament Size' || item.paramName === 'qty' ? 'margin: 20px 0 0;' : ''">
							<PublicStep v-if="(item.paramName == 'quoteCategory' && (pid === 659 || pid === 625)) || item.paramName == 'Select Printed Area'" :class="item.paramName == 'Select Printed Area' ? 'Select-Printed-Area' : 'quoteCategory'" :config="allStepConfig[item.paramName]" :selectedData="selectedData" :stepData="item" @clickFun="selectQuoteParamsFn(item, $event)" @viewVideo="viewVideo"></PublicStep>

							<template v-if="item.paramName === 'Ornament Size'">
								<div class="step-item Ornament-Size" :class="{ mask: maskName === item.paramName, hideContent: $route.query.designPic }" :id="item.paramName">
									<h3 class="step-title">
										<span stepColor>{{ lang.Step }} {{ item.customIndex }}:&nbsp;</span>
										{{ lang.Select }} Size
										<span v-if="$route.query.designPic" style="color: #999999">: {{ $route.query.techName }}</span>
									</h3>
									<div class="step-box">
										<StepSize :itemData="item" :hideHelp="true" :selectedData="selectedData" @selectQuoteParamsFn="selectQuoteParamsFn($event.item, $event.citem)"> </StepSize>
									</div>
								</div>
							</template>

							<template v-if="item.paramName === 'qty'">
								<div class="step-item step-quantity" id="enterQuantity">
									<h3 class="step-title">
										<span stepColor>{{ lang.Step }} {{ item.customIndex }}:&nbsp;</span>
										{{ lang.Select }} {{ lang.ornament.enterQuantity }}

										<span v-if="$route.query.designPic" style="color: #999999">: {{ $route.query.techName }}</span>
									</h3>
									<div class="table">
										<table width="100%" align="center">
											<thead>
												<tr align="center">
													<th align="left">{{ lang.quantity }}</th>
													<th>{{ lang.unitPrice }}</th>
													<th>{{ lang.moldPrice }}</th>
													<th>{{ lang.subtotal }}</th>
												</tr>
											</thead>
											<tbody>
												<tr v-for="(itemC, itemCIndex) in qtyList" :key="itemC.id" @click="updateQty(itemC, itemCIndex)" align="center" v-if="itemC.totalQuantity !== 2000 && itemC.totalQuantity !== 5000">
													<td class="quantity" v-if="itemC.totalQuantity">
														<CustomCircle :circleType="2" :isActive="itemCIndex === selectQtyIndex"> </CustomCircle>
														<div>
															<div>{{ itemC.totalQuantity }}</div>
														</div>
													</td>
													<td><CCYRate :price="itemC.foundationUnitPrice"></CCYRate>ea</td>
													<td>
														<CCYRate :price="itemC.toolingCharge"></CCYRate>
													</td>
													<td>
														<CCYRate :price="itemC.totalPrice"></CCYRate>
													</td>
												</tr>
												<tr align="center">
													<td class="quantity">
														<CustomCircle :circleType="2" :isActive="selectQtyIndex === -1"> </CustomCircle>
														<div>
															<input
																type="text"
																class="customInput"
																v-model="customQtyModel"
																placeholder="Enter Qty"
																@input="inputCustomQty"
																:style="{
																	borderColor: inputRequired === false ? (customQtyModel ? '#ccc' : 'red') : '',
																}"
															/>
														</div>
													</td>
													<td width="25%"><CCYRate :price="qtyUnitPrice"></CCYRate>ea</td>
													<td width="25%">
														<CCYRate :price="qtyToolingCharge"></CCYRate>
													</td>
													<td width="25%">
														<CCYRate :price="totalPriceCharge"> </CCYRate>
													</td>
												</tr>
											</tbody>
										</table>
									</div>
								</div>
							</template>

							<PublicStep v-if="item.paramName == 'Ornament Process'" :config="allStepConfig[item.paramName]" :selectedData="selectedData" :stepData="item" @clickFun="selectQuoteParamsFn(item, $event)" :class="showAcrylicOrnaments ? 'acrylic-Ornament-Process' : 'Ornament-Process'"></PublicStep>

							<PublicStep v-if="item.paramName == 'Select Finish' || item.paramName == 'Select Acrylic Charm Color'" :config="allStepConfig[item.paramName]" :selectedData="selectedData" :stepData="item" @clickFun="selectQuoteParamsFn(item, $event)" :class="item.paramName == 'Select Acrylic Charm Color' ? 'Select-Acrylic-Charm-Color' : 'Ornament-Process'"></PublicStep>

							<PublicStep v-if="item.paramName == 'Ornament Attachment'" :config="allStepConfig[item.paramName]" :selectedData="selectedData" :stepData="item" @clickFun="selectQuoteParamsFn(item, $event)" class="Ornament-Attachment"> </PublicStep>

							<PublicStep v-if="item.paramName == 'Ornament Package'" :config="allStepConfig[item.paramName]" :selectedData="selectedData" :stepData="item" @clickFun="selectQuoteParamsFn(item, $event)" class="Ornament-Package"></PublicStep>

							<template v-if="item.paramName === 'Upload Artwork & Comments'">
								<h3 class="step-title" :style="device !== 'mb' ? 'margin-top: 20px;' : ''">
									<span>{{ lang.step }} {{ item.customIndex }}: </span> {{ lang.Select }}
									{{ item.alias ? item.alias : item.paramName }}
								</h3>
								<UploadNew :id="item.paramName" :key="index" class="step-upload" :class="{ mask: maskName === item.paramName, stepUpload2: uploadArtworkList.length > 0 && device === 'mb' }" :item="item" :uploadList="uploadArtworkList" :isCustomEmail="isUpload" :isCustomBrowse="isCustomBrowse" @deleteUpload="deleteUpload" @setIsLater="setIsLater" @openUpload="openUpload" @uploadPic="uploadPic"></UploadNew>
							</template>

							<StepTime v-if="item.paramName === 'Select Turnaround Time'" class="step-item step-date Select-Turnaround-Time" :class="{ mask: maskName === item.paramName }" :id="item.paramName" :cateData="cateData" :pid="pid" :customQty="customQty" :index="item.customIndex" :itemData="item" :priceInfo="priceInfo" :previewMode="previewMode" :selectedParams="selectedData" @showMaskFn="showMaskFn" @selectItem="selectQuoteParamsFn($event.item, $event.citem)" @closeMask="closeMask" :key="index"></StepTime>
						</div>
						<div class="kk">
							<div class="sub-detail" id="foot">
								<div class="subtotal-left">
									<div class="sub-item">
										<div class="sub-item-left">{{ lang.Quantity }}:</div>
										<div class="sbu-item-right">
											<span>{{ priceInfo.totalQuantity }}</span>
										</div>
									</div>
									<div class="sub-item" v-show="!onlyAddInquiry">
										<div class="sub-item-left">{{ lang.unitPrice }}:</div>
										<div class="sbu-item-right">
											<CCYRate :price="priceInfo.foundationUnitPrice"></CCYRate>
										</div>
									</div>
									<div class="sub-item" v-show="!onlyAddInquiry && priceInfo.toolingCharge && priceInfo.toolingCharge > 0">
										<div class="sub-item-left">{{ lang.moldPrice }}:</div>
										<div class="sbu-item-right">
											<CCYRate :price="priceInfo.toolingCharge"></CCYRate>
										</div>
									</div>
									<div class="sub-item" v-show="!onlyAddInquiry && priceInfo.setupCharge && priceInfo.setupCharge > 0">
										<div class="sub-item-left">{{ lang.setUpFee }}:</div>
										<div class="sbu-item-right">
											<CCYRate :price="priceInfo.setupCharge"></CCYRate>
										</div>
									</div>
									<div class="sub-item" v-show="!onlyAddInquiry && discountPrice != 0">
										<div class="sub-item-left">{{ !showAcrylicOrnaments ? lang.discount : lang.rushFee }}:</div>
										<div class="sbu-item-right" :style="device !== 'mb' ? 'width: 182px;' : ''">
											{{ text2 }}<CCYRate :price="discountPrice"></CCYRate>
											<span v-if="!showAcrylicOrnaments">{{ text1 }}</span>
										</div>
									</div>
								</div>
								<div class="subtotal-right">
									<div class="totalPriceBox">
										<strong>{{ lang.total }}:</strong>
										<CCYRate class="final-price" :price="finalPrice"></CCYRate>
										<CCYRate class="before-discount-price" :price="originPrice" v-if="finalPrice !== originPrice && !showAcrylicOrnaments"></CCYRate>
										<el-select v-model="currencyId" :placeholder="lang.PleaseSelect" @change="changeCurrency" size="small" style="width: 100px">
											<el-option v-for="item in currencyList" :key="item.id" :label="item.code" :value="item.id" />
										</el-select>
									</div>
									<div class="btnGroup">
										<QuoteBtn bgColor="linear-gradient(to top, #FF412B 0%, #FF7743 100%)" @click.native="addInquiry">
											{{ lang.submitInquiry }}
											<el-tooltip popper-class="cusToolTip" effect="light" :content="textInfo.inquiryTip">
												<b class="icon-wenhao3 tip-icon" @click.stop></b>
											</el-tooltip>
										</QuoteBtn>
										<QuoteBtn bgColor="linear-gradient(to top, #0066CC 0%, #2FB6F5 100%)" @click.native="addCart" v-if="onlyAddInquiry === 0 || !onlyAddInquiry">
											{{ lang.addToCart }}
											<el-tooltip popper-class="cusToolTip" effect="light" :content="textInfo.addCartTip">
												<b class="icon-wenhao3 tip-icon" @click.stop></b>
											</el-tooltip>
										</QuoteBtn>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="productDes" v-if="device === 'mb'">
					<div class="title">
						<strong>{{ lang.ornament.description }}:</strong>
					</div>
					<div class="des-con">
						<div style="margin-bottom: 10px">{{ descriptionText }}</div>
						<div style="margin-bottom: 10px">{{ descriptionText2 }}</div>
						<div style="margin-bottom: 10px">{{ descriptionText3 }}</div>
					</div>
					<div class="title" v-if="pid === 601">
						<strong>{{ descriptionText4 }}</strong>
					</div>
					<div class="des-con" v-if="pid === 601">
						<div style="margin-bottom: 10px">{{ descriptionText5 }}</div>
						<div style="margin-bottom: 10px">{{ descriptionText6 }}</div>
					</div>
					<div class="title" style="margin-top: 20px" v-if="pid === 625">
						<strong>{{ descriptionText4 }}</strong>
					</div>
					<div class="des-con" v-if="pid === 625">
						<div>{{ descriptionText5 }}</div>
						<div>{{ descriptionText6 }}</div>
					</div>
				</div>
			</article>

			<el-drawer :visible.sync="showArea" :with-header="true" custom-class="drawDialog" :size="device === 'ipad' ? '728px' : '85%'">
				<Detail class="type3" :key="2" :absoluteImg="comeFromDZ.absoluteImg" :selectedData="selectedData" :priceInfo="priceInfo" :customQty="customQty" :textInfo="textInfo" :generalData="generalData" :attachment="attachment" @toPosition="toPosition" @addInquiry="debounceAddInquiry" @addCart="addCart"> </Detail>
			</el-drawer>
			<!--    遮罩-->
			<!-- <myMask :maskName.sync="maskName"></myMask> -->
			<!-- 建议弹窗-->
			<RecomendDialog :showUpload="false" :commentsDialog.sync="commentsDialog" :paramValue="selectedParamsValue" @next="recomendNext" :textPlaceholder="lang.placeholder2"></RecomendDialog>
			<!--  手机，ipad端预览-->
			<PreviewBtn :showArea.sync="showArea"></PreviewBtn>
			<BaseDialog v-model="showOtoDialog" :width="device != 'mb' ? '50%' : '90%'">
				<div class="otoWrap">
					<img src="@/assets/images/oto.png" alt="" />
					<p>{{ lang.p1 }}</p>
					<h3>{{ lang.p2 }}</h3>
					<p style="color: #666666">
						{{ lang.p3 }}<br />
						{{ lang.p4 }}.
					</p>

					<div class="box">
						<p class="t1">
							{{ lang.p5 }} <br />
							{{ lang.p6 }}!
						</p>
						<a href="https://www.o2o.co/previewWeb" style="text-decoration: none">
							<el-button type="primary">{{ lang.p7 }}</el-button>
						</a>
					</div>
				</div>
			</BaseDialog>
			<VideoPreviewDialog :pic-dialog.sync="picDialog" :zoom-pic="zoomPic" :temp-type="tempType" :selected-params-value="selectedParamsValue" @qtyAndBtnConfirm="qtyAndBtnConfirm" @qtyAndBtnNext="qtyAndBtnNext" @dialogNextStep="dialogNextStep"></VideoPreviewDialog>
			<BaseDialog v-model="upgradeDialog" :width="device != 'mb' ? '640px' : '320px'">
				<Upgrade @closeUpgrade="upgradeDialog = false" :tipArr="tipArr" :typeName="cateData.cateName" @upgradeLevel="upgradeLevel"></Upgrade>
			</BaseDialog>
			<infoDialog :infoDialogVisible.sync="infoDialogVisible" @getValue="getValueFun" :otherUpload="uploadArtworkList" :recomendUpload="recomendUpload" :uploadList.sync="uploadList"></infoDialog>
			<BaseDialog v-model="noFileDialog" :width="device != 'mb' ? '485px' : '90%'" :model="false">
				<template #closeIcon>
					<div style="display: none"></div>
				</template>
				<infoUpload :infoUploadList="infoUploadList" @pushInfoList="pushInfoList" @delInfoList="delInfoList" @updateInquiry="updateInquiry" @closeInfoDialog="closeInfoDialog"> </infoUpload>
			</BaseDialog>
		</template>
	</div>
</template>
<script>
import "@/plugins/element";
import BaseDialog from "@/components/Quote/BaseDialog";
import VideoPlayer from "@/components/Quote/VideoPlayer.vue";
import Upgrade from "@/components/Quote/Upgrade";
import infoDialog from "@/components/Medals/infoDialog";
import QuoteNav from "@/components/Medals/QuoteNav";
import QuoteBtn from "@/components/Quote/QuoteBtn";
import quoteMixin from "@/mixins/quote";
import quoteBanChoiceMixins from "@/mixins/quoteBanChoice";
import StepTime from "@/components/Quote/StepTime.vue";
import RecomendDialog from "@/components/Quote/RecomendDialog.vue";
import PreviewBtn from "@/components/Quote/PreviewBtn.vue";
import Preloader from "@/components/Quote/Preloader.vue";
import PublicStep from "@/components/Quote/PublicStep/index.vue";
import { getQuoteConfig } from "@/assets/js/quote/quoteConfig";
import StepSize from "@/components/Quote/StepSize.vue";
import CustomCircle from "@/components/Quote/customCircle.vue";
import UploadNew from "@/components/Quote/UploadNew.vue";
import { acceptFileType, checkFile } from "@/utils/validate";
import { uploadFile } from "@/utils/oss";
import { round2 } from "@/utils/utils";
import Detail from "@/components/Quote/Detail.vue";
import VideoPreviewDialog from "@/components/Quote/VideoPreviewDialog.vue";
export default {
	head() {
		return {
			title: "Ornaments",
		};
	},
	components: {
		VideoPreviewDialog,
		PublicStep,
		Preloader,
		PreviewBtn,
		RecomendDialog,
		StepTime,
		BaseDialog,
		VideoPlayer,
		infoDialog,
		Upgrade,
		QuoteNav,
		QuoteBtn,
		StepSize,
		CustomCircle,
		UploadNew,
		acceptFileType,
		Detail,
	},
	mixins: [quoteMixin, quoteBanChoiceMixins],
	data() {
		const config = getQuoteConfig.call(this, this.$route.name);
		return {
			...config,
			customQtySelected: null,
			currencyId: "",
			isCustomBrowse: false,
			acceptFileType,
			customQtyModel: "",
			selectQtyIndex: 0,
		};
	},
	watch: {
		isLoading() {
			this.$nextTick(() => {
				this.myswiper1 = new Swiper(this.$refs.swiper1, {
					slidesPerView: 5,
					direction: "vertical",
					spaceBetween: this.device === "mb" ? 0 : 8,
					watchSlidesVisibility: true, //防止不可点击
					grabCursor: true,
				});
				this.myswiper2 = new Swiper(this.$refs.swiper2, {
					slidesPerView: 1,
					spaceBetween: 10,
					autoplay: false,
					grabCursor: true,
					observer: true,
					observeParents: true,
					navigation: {
						nextEl: ".swiper-button-next",
						prevEl: ".swiper-button-prev",
					},
					thumbs: {
						swiper: this.myswiper1,
					},
				});
				this.handleSelectDefaultParam();
			});
		},
		"$store.state.currency": {
			handler(newValue) {
				this.currencyId = newValue.id;
			},
			immediate: true,
		},
		qtyList: {
			handler(newVal, oldVal) {
				//默认选中第一个数量
				if (this.selectQtyIndex === 0) {
					this.customQty = newVal[0].totalQuantity;
				}
			},
			deep: true,
		},
	},
	computed: {
		showAcrylicOrnaments() {
			return this.pid === 656;
		},
		onlyAddInquiry() {
			let onlyAddInquiry = false;
			for (const stepData in this.selectedData) {
				let data = this.selectedData[stepData];
				if (data[0]) {
					if (data[0].onlyAddInquiry && data[0].onlyAddInquiry != 0) {
						onlyAddInquiry = true;
						this.viewMore = true;
					}
				}
			}
			return onlyAddInquiry;
		},
		subtotal() {
			return round2(round2(this.priceInfo.foundationUnitPrice * this.priceInfo.totalQuantity) + this.priceInfo.toolingCharge + (this.priceInfo.setupCharge || 0));
		},
		discountPrice() {
			if (this.priceInfo) {
				return `${Math.abs(this.priceInfo.totalPrice - this.subtotal)}`;
			} else {
				return 0;
			}
		},
		text2() {
			let ac;
			if (this.priceInfo.totalPrice > this.subtotal) {
				ac = "+";
			} else {
				ac = "-";
			}
			return ac;
		},
		text1() {
			//加急费，重量加价
			if (this.priceInfo.discountPrice) {
				return this.lang.rushDelivery;
			}
			if (this.priceInfo.discount > 1) {
				return this.lang.rushDelivery;
			} else if (this.priceInfo.discount < 1) {
				return `(${(Math.abs(1 - this.priceInfo.discount) * 100).toFixed(0)}% ${this.langSemi.forUnitPrice})`;
			}
		},
		//货币
		currencyList() {
			return this.$store.state.currencyList;
		},
		//输入框数量单价
		qtyUnitPrice() {
			return this.customQtyModel !== "" ? this.priceInfo.foundationUnitPrice : 0;
		},
		//输入框数量模具费
		qtyToolingCharge() {
			return this.customQtyModel !== "" ? this.priceInfo.toolingCharge : 0;
		},
		//输入框数量总价
		totalPriceCharge() {
			return this.customQtyModel !== "" ? this.priceInfo.totalPrice : 0;
		},
		//折后总价
		finalPrice() {
			return this.priceInfo.totalPrice;
		},
		//折前总价
		originPrice() {
			let priceInfo = this.priceInfo;
			return priceInfo.foundationUnitPrice * priceInfo.totalQuantity + priceInfo.toolingCharge;
		},
	},
	methods: {
		selectQuoteParamsFn(item,citem){
			this.selectQuoteParams(item,citem,true);
		},
		async handleSelectDefaultParam() {
			let selectedData = {},
				generalData = this.generalData;
			//默认选中参数
			if (generalData?.length) {
				generalData.forEach((item) => {
					if (item.paramName !== "qty" && item.paramName !== "Upload Artwork & Comments") {
						let findDefault = item.childList[0];
						if (findDefault) {
							selectedData[item.paramName] = [findDefault];
						}
					}
				});
			}
			this.selectedData = selectedData;
			this.isUpload = true;
			this.customQty = 10;
			this.debounceCalcPrice();
		},

		inputCustomQty() {
			this.customQtyModel = this.customQtyModel.replace(/[^0-9]/g, "").replace(/^0+/, "");
			if (this.customQtyModel > 999999) {
				this.customQtyModel = "999999";
			}
			this.debounceCalcPrice();
			this.handleWeightDiscount();
			this.selectQtyIndex = -1;
			this.customQty = this.customQtyModel;
		},

		updateQty(item, index) {
			if (index !== -1) {
				this.customQtyModel = "";
			}
			this.customQty = item.totalQuantity;
			this.selectQtyIndex = index;
			this.debounceCalcPrice();
			this.handleWeightDiscount();
			// this.showMaskFn(item.paramName);
		},
		openUpload() {
			document.querySelector("#uploadInput").click();
			this.isCustomBrowse = true;
			this.isUpload = false;
		},
		uploadPic(event, type = "upload") {
			this.$gl.show();
			let files = type === "upload" ? event.target.files : event;
			let fileResult = checkFile(Array.from(files), this.acceptFileType, 80);
			if (!fileResult) {
				this.$toast.error("File type error");
				this.$gl.hide();
				return false;
			}
			if (fileResult.nomalSize.length == 0) {
				this.$gl.hide();
				this.$store.commit("setSizeDialog", true);
				this.$store.commit("setInputRefName", "replayUpload");
				this.$store.commit("setOverSizeList", fileResult.overSize);
				// this.$refs.upload.value = "";
				return false;
			}
			let uploadPromises = [];
			fileResult.nomalSize.forEach((file) => {
				let promise = uploadFile(file).then((res) => {
					this.uploadArtworkList.push({
						original_filename: file.name,
						secure_url: res,
						size: (file.size / 1024).toFixed(1),
					});
				});
				uploadPromises.push(promise);
			});
			Promise.all(uploadPromises).then(() => {
				this.$gl.hide();
				//google 记录用户上传动作
				this.$store.commit("setGoogleUploadAction", { [`STEP${this.index}:${this.itemData.paramName}`]: this.uploadArtworkList.length, content_id: this.pid });

				this.$refs.upload.value = "";
				if (fileResult.overSize.length > 0) {
					this.$store.commit("setSizeDialog", true);
					this.$store.commit("setInputRefName", "replayUpload");
					this.$store.commit("setOverSizeList", fileResult.overSize);
				}
			});
		},
		setIsLater() {
			this.isUpload = true;
			this.isCustomBrowse = false;
		},
		deleteUpload(index) {
			console.log(index, "index");
			this.uploadArtworkList.splice(index, 1);
		},
		changeCurrency(val) {
			let findC = this.currencyList.find((item) => {
				return item.id === val;
			});
			if (findC) {
				this.$store.commit("setCurrency", findC);
			}
		},
		//判断是否选中
		hasId(id, arr) {
			if (!arr) {
				return false;
			}
			return !!arr.find((item) => {
				return item.id === id;
			});
		},
	},
};
</script>

<style scoped lang="scss">
@import "@/assets/css/quotePublic";

::v-deep .stepItem {
	&:hover {
		.imgWrap {
			border-color: $color-primary !important;
		}

		.customCircle {
			border-color: $color-primary;
			background: #fff;

			&::after {
				background-color: $color-primary;
			}
		}
	}
}

::v-deep .stepItem.active .customCircle {
	border-color: $color-primary;
	background: #fff;

	&::after {
		background: $color-primary;
	}
}

::v-deep .customCircle.isActive {
	border-color: $color-primary;
	background: #fff;

	&::after {
		background: $color-primary;
	}
}

::v-deep .customCircle::after {
	background: #d4d7d9;
}

.content {
	padding: 20px max(calc(50% - 697px), 1.5vw) !important;

	@include respond-to(mb) {
		display: block !important;
		padding: 10px !important;
		text-align: center;
	}

	h1 {
		font-weight: bold;
		font-size: 28px;
		color: #333333;
		margin-bottom: 16px;
		grid-column-end: 1;
		grid-column-start: 49;
		padding-left: 25px;
		@include respond-to(mb) {
			padding-left: 0;
			margin-bottom: 10px;
			font-size: 20px;
		}
	}

	@include respond-to(mb) {
		padding-left: 0;
	}
}

.leftArea {
	grid-column: 2 / 21 !important;

	.tip {
		display: grid;
		justify-content: center;
		grid-template-columns: repeat(3, 1fr);
		grid-gap: 20px;
		margin-bottom: 20px;
		font-size: 14px;
		padding-left: 0;
		margin-left: 0;
		list-style: none;
		text-align: center;
		white-space: nowrap;
		margin-top: 23px;

		@include respond-to(mb) {
			grid-gap: 4px;
			font-size: 1rem;
			margin-bottom: 0;
		}

		b {
			color: #68bd2c;
		}
	}

	.productDes {
		.title {
			margin-bottom: 10px;
			font-size: 16px;
		}

		.des-con {
			margin: 0 0 10px 0;
			line-height: 1.4em;
			font-size: 14px;
		}
	}
}

@include respond-to(mb) {
	.productDes {
		text-align: left;
		padding: 0 2.6vw;

		.title {
			margin-bottom: 1.6vw;
			font-size: 3.2vw;
			font-weight: bold;
		}

		.des-con {
			margin: 0 0 10px 0;
			line-height: 1.4em;
			font-size: 3.2vw;
		}
	}
}

.rightArea {
	flex: 1;
	grid-column: 23 / 49 !important;
	border-radius: 10px 10px 10px 10px;
	border: 1px solid #dcdfe6;
	margin-left: 0 !important;
	background: #fff;
	padding: 0 22px 20px;
	@media screen and (max-width: 1500px) {
		display: block !important;
	}
	@include respond-to(mb) {
		background: none;
	}
	.kk,
	.dd {
		@include respond-to(mb) {
			margin: 20px 0;
		}
	}

	::v-deep .PriceText .tip-text {
		text-align: left;
		margin-left: 1.5em;
	}
	::v-deep .PriceText .normal-text {
		text-align: left;
		margin-left: 1.5em;
	}
	::v-deep .textWrap {
		// padding: 0 10px;
		.name {
			text-align: left !important;
		}
	}

	.Select-Printed-Area {
		margin: 20px 0 0 0 !important;
		::v-deep .stepItem {
			background: #fafafa;
			border-radius: 16px;
			padding-bottom: 10px;
			font-size: 14px;
			@include respond-to(mb) {
				border-radius: 8px;
				padding-bottom: 1.7333vw;
			}
		}
	}
	.quoteCategory {
		::v-deep .step-item-title {
			margin-bottom: 11px !important;
			font-size: 1em !important;
		}
	}

	.Ornament-Size {
		margin: 20px 0 0;
		@include respond-to(mb) {
			margin: 0;
		}

		.step-box {
			display: block;
			background: #fafafa;
			border-radius: 12px;
		}

		.confirmBtnWrap {
			display: none;
			margin-top: 23px;
		}

		&.mask {
			.confirmBtnWrap {
				display: flex;
				justify-content: center;
				align-items: center;

				.gs-quote-button {
					background-color: #d17448;
				}
			}
		}
	}

	.step-quantity {
		margin: 20px 0 0;
		@include respond-to(mb) {
			margin: 0;
		}

		.table {
			background: #fafafa;
			border-radius: 12px;
			padding: 7px 20px;

			tr {
				line-height: 30px;
			}

			.quantity {
				display: flex;
				align-items: center;

				.circle2 {
					border-color: $color-primary;
					background-color: $color-primary;
					flex-shrink: 0;
					width: 18px;
					height: 18px;
					border-radius: 50%;
					margin-right: 10px;
					display: flex;
					justify-content: center;
					align-items: center;

					&::after {
						background-color: #ffffff;
						content: "";
						width: 6px;
						height: 6px;
						border-radius: 50%;
					}
				}

				.customInput {
					border: 1px solid #dbdbdb;
					width: 100px;
					padding: 0 8px;
					border-radius: 4px;
				}
			}
		}
	}

	.dd:nth-of-type(n + 5):nth-of-type(-n + 11) {
		grid-column-end: 1;
		grid-column-start: 3;
	}
	.kk:nth-of-type(n + 4):nth-of-type(-n + 11) {
		grid-column-end: 1;
		grid-column-start: 3;
	}

	::v-deep .stepWrap,
	.step-upload,
	.step-date {
		padding: 0;
		margin: 20px 0 0;
	}

	.Select-Acrylic-Charm-Color {
		::v-deep .stepItem {
			background: #fafafa;
			border-radius: 16px;
			padding-bottom: 13px;

			@include respond-to(mb) {
				border-radius: 5px;
				padding-bottom: 1.7333vw;
				.name {
					text-align: left;
				}
			}
		}
	}
	.acrylic-Ornament-Process {
		::v-deep .stepItem {
			justify-content: flex-start;
			background: #fafafa;
			border-radius: 16px;
			padding-bottom: 13px;
			@include respond-to(mb) {
				justify-content: start !important;
				border-radius: 8px;
				padding-bottom: 1.7333vw;
			}
		}
	}

	.Ornament-Process {
		::v-deep .step-item-title {
			margin-bottom: 11px !important;
			font-size: 1em !important;
		}
		::v-deep .stepItem {
			justify-content: flex-start;
			background: #fafafa;
			border-radius: 1em;
			padding-bottom: 13px;
			@include respond-to(mb) {
				justify-content: start !important;
				border-radius: 8px;
				padding-bottom: 1.7333vw;
			}
		}
		::v-deep .step-item-title {
			.title2 {
				display: none;
			}
		}
	}

	.Ornament-Attachment {
		::v-deep .stepItem {
			background: #fafafa;
			border-radius: 16px;
			padding-bottom: 13px;
		}

		::v-deep .tips.type2 {
			border-top-left-radius: 15px;

			@include respond-to(mb) {
				border-top-left-radius: 6px;
			}
		}
	}
	.Ornament-Package {
		::v-deep .PriceText .normal-text {
			text-align: center;
			margin-left: 0;
		}
		::v-deep .PriceText .tip-text {
			text-align: center !important;
			margin-left: 0 !important;
		}

		::v-deep .step-item-title {
			margin-bottom: 11px !important;
			font-size: 1em !important;
		}
		::v-deep .stepItem {
			background: #fafafa;
			border-radius: 1em;
			padding-bottom: 13px;
			.imgWrap {
				height: 94px;
			}
			@include respond-to(mb) {
				border-radius: 5px;
				padding-bottom: 1.7333vw;
			}
		}
	}

	.Select-Turnaround-Time {
		::v-deep .step-item-title {
			margin-bottom: 11px !important;
			font-size: 1em !important;
		}
		::v-deep .step-box {
			grid-template-columns: repeat(3, 1fr);
			row-gap: 10px;

			@include respond-to(mb) {
				grid-template-columns: repeat(2, 1fr) !important;
			}
		}
		@include respond-to(mb) {
			::v-deep .price-box {
				display: none;
			}
		}
	}

	::v-deep .step-date .step-box .item-wrap.discount-item0 {
		background: #fafafa;
	}

	::v-deep .step-date .step-box .item-wrap.discount-item1 {
		background: #fafafa;
	}

	::v-deep .step-date .step-box .item-wrap.discount-item2 {
		background: #fafafa;
	}

	::v-deep .step-date .step-box .item-wrap .bottom {
		font-size: 14px;
		color: #808080;
		margin-left: 0;
	}

	.rightArea_div {
		display: grid;
		grid-template-columns: 1fr 2fr;
		gap: 0 24px;

		.kk:first-of-type,
		.dd:first-of-type {
			grid-column-end: 1;
			grid-column-start: 3;
		}

		.step-title {
			margin-bottom: 11px;
			font-size: 1em;
			font-weight: bold;

			span {
				color: $color-primary;
				font-weight: 700;
				text-transform: uppercase;
			}
			// @include respond-to(mb) {
			//     font-size: 14px;
			// }
		}

		.sub-detail {
			display: grid;
			grid-template-columns: repeat(2, 1fr);
			align-items: center;
			border-radius: 10px;
			padding: 13px 21px;
			background: #f6f6f6;
			grid-gap: 10px;

			@include respond-to(mb) {
				grid-template-columns: repeat(1, 1fr);
				grid-gap: 5px;
				border-radius: 0;
				margin: 0;
				background-color: #ebebeb;
			}

			.subtotal-left {
				.sub-item {
					display: flex;
					align-items: center;
					margin-bottom: 8px;

					@include respond-to(mb) {
						justify-content: space-between;
					}

					&:last-child {
						margin-bottom: 0;
					}

					.sub-item-left {
						width: 85px;
					}

					.sbu-item-right {
						color: #666666;
					}
				}
			}

			.subtotal-right {
				text-align: center;

				.btnGroup {
					display: flex;
					margin-top: 10px;

					button {
						flex: 1;
						margin: 0 5px;
						border-radius: 4px;
						background: transparent;
						border: none;
						width: 180px;
						@include respond-to(mb) {
							width: auto;
						}

						b {
							margin-left: 4px;
							color: #ffffff;
						}
					}
				}

				.final-price {
					margin: 0 5px 0 10px;
					font-size: 24px;
					color: #d24600;
					font-weight: bold;
				}

				.before-discount-price {
					text-decoration: line-through;
					font-size: 14px;
					margin-right: 5px;
				}
			}
		}
	}

	@include respond-to(mb) {
		display: block !important;
		border: 0;
		margin: 0;
		padding: 0;
		margin-right: 0 !important;

		.step-date {
			margin: 0;
			background: none;
		}

		.step-upload {
			margin: 3.6667vw 0 6vw;
		}
		.stepUpload2 {
			background: #ffff;
			border-radius: 5px;
			padding: 4vw 2vw;
		}

		.rightArea_div {
			display: block;

			.kk,
			.dd {
				text-align: left;
				margin-top: 20px;
			}

			.kk:last-child,
			.dd:last-child {
				padding: 0;
			}
			.quoteCategory {
				margin: 5vw 0 0;
			}

			.Ornament-Size {
				padding: 3.4667vw 1.8667vw;
				background: #fafafa;
				border-radius: 12px;
				.step-item-params {
					grid-template-columns: repeat(2, 1fr) !important;
					grid-gap: 1em;
					margin: 0 0.6em;

					::v-deep .param-item .labelText {
						font-size: 12px !important;
					}
				}
			}

			.step-quantity {
				padding: 3.4667vw 1.8667vw;
				background: #fafafa;
				border-radius: 12px;
				.table {
					padding: 0;
				}
			}

			.Ornament-Attachment {
				::v-deep .stepItem {
					border-radius: 8px;
					padding-bottom: 1.7333vw;
				}
			}

			.Ornament-Package {
				::v-deep .PriceText .tip-text {
					text-align: left;
					margin-left: 2em;
				}
				::v-deep .customCircle {
					margin-right: 0.25em;
					position: relative;
					top: 2vw;
				}

				::v-deep .stepItem {
					flex-direction: row-reverse;
					padding: 1.3333vw;

					.imgWrap {
						flex: 1;
						height: 100%;
					}

					.textWrap {
						flex: 1.4;
						margin: 10px 0;
					}
				}
			}
		}

		.stepWrap {
			margin: 0;
			background: none;
		}
	}
}

::v-deep .step-item-title {
	font-size: 1em !important;
	margin-bottom: 11px !important;
}

::v-deep .step-date .step-title {
	font-size: 1em !important;
	margin-bottom: 11px !important;
	font-weight: bold !important;
}

::v-deep .hoursText {
	font-size: 14px;
}

.swiper-area {
	display: flex;
	gap: 0.5em;
	height: 30.13em;

	.swiper-button-next,
	.swiper-button-prev {
		background-color: rgba(163, 163, 163, 0.5);
		border-radius: 75px 75px 75px 75px;
		padding: 0 22px;

		&::after {
			font-size: 22px;
			color: #fff;
		}
	}

	@include respond-to(mb) {
		height: 31.25em;

		&.showThumbs {
			margin: 1em;
			height: 24.28em;

			.myswiper1 {
				display: block;
				flex-basis: 4.5em;

				img {
					border-radius: 6px;
				}

				.openGalleryBox {
					border-radius: 0.5em;
					font-size: 0.75em;
				}
			}

			.myswiper2 {
				img {
					border-radius: 0.8em;
				}
			}
		}

		&.paginationHidn {
			.myswiper2 {
				.swiper-pagination {
					display: none;
				}
			}
		}
	}

	.myswiper1 {
		flex-basis: 5.63em;

		img {
			border-radius: 6px;
		}

		.swiper-slide {
			height: 19%;
			margin-bottom: 8px;

			@include respond-to(mb) {
				margin-bottom: 0;
			}
		}

		.swiper-slide-thumb-active img {
			border: 2px solid $color-primary;
		}

		.openGalleryBox {
			aspect-ratio: 1/1;
			width: 100%;
			background: #f96a00;
			border-radius: 1.25em;
			padding: 0.5em;
			text-align: center;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			color: #fff;
			font-size: 1em;
			cursor: pointer;
			overflow: hidden;
		}
	}

	.myswiper2 {
		flex: 1;

		.swiper-pagination {
			display: none;

			@include respond-to(mb) {
				display: block;

				::v-deep .swiper-pagination-bullet {
					min-width: 3em;
					height: 0.17em;
					background: rgba(0, 0, 0, 0.24);
					border-radius: 0.25em;

					&.swiper-pagination-bullet-active {
						background-color: $color-primary;
					}
				}
			}
		}

		img {
			border-radius: 1.25em;

			@include respond-to(mb) {
				border-radius: 0;
			}
		}
	}
}
</style>
