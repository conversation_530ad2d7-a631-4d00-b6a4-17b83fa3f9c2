<template>
	<div class="noParamInquiryBox">
		<BaseDialog class="topDialog1" v-model="infoDialogVisible" :model="false" :width="!isMobile ? '500px' : '90%'" margin="20px 0">
			<div class="close-icon" slot="closeIcon" @click="handleClose">
				<v-icon>mdi-close-thick</v-icon>
			</div>
			<v-card>
				<v-card-title class="noParamInquiryBoxHead">
					<div class="dialogHead">
						<span class="text-h5" style="margin: auto">{{ langSemiCustom.syi }}</span>
					</div>
				</v-card-title>
				<v-card-text>
					<myForm ref="dynamicValidateForm" class="noParamForm">
						<div class="nameBox">
							<myInput class="nameItem" v-model="dynamicValidateForm.firstName" prop="firstName" :rules="dynamicValidateFormRules.firstNameRules" :textLabel="langQuote.firstName" requireIcon="pre"> </myInput>
							<myInput class="nameItem" v-model="dynamicValidateForm.lastName" prop="lastName" :rules="dynamicValidateFormRules.lastNameRules" :textLabel="langQuote.lastName" requireIcon="pre"> </myInput>
						</div>
						<myInput v-model="dynamicValidateForm.email" prop="email" :rules="dynamicValidateFormRules.emailRules" :textLabel="langQuote.email" requireIcon="pre"> </myInput>
						<div class="telephoneBox" :class="{ 'error--text': !areaCodeOk || !phoneOk }">
							<div class="textLabel" :style="{ color: !areaCodeOk || !phoneOk ? '#ff5252' : '#000' }">
								{{ langQuote.telephone }}
							</div>
							<div class="inputGroup">
								<div class="areaCode">
									<div class="prefix">+</div>
									<input v-model="dynamicValidateForm.areaCode" :class="{ noValid: !areaCodeOk }" @blur="checkTelephone" ref="areaCodeInput" class="customInput" type="text" style="width: 100%" @input="handleInputAreaCode" />
									<img class="area-cornor" :src="cornorImg" alt="" />
								</div>
								<input v-model="dynamicValidateForm.telephone" :class="{ noValid: !phoneOk }" @blur="checkTelephone" ref="telephoneInput" @input="handleInputTelephone" class="customInput telephone" type="text" />
							</div>
							<div class="textField">
								<div class="vaildate-message" v-show="!areaCodeOk || !phoneOk">
									{{ vaildateText }}
								</div>
							</div>
						</div>
						<myInput v-model="dynamicValidateForm.subject" :textLabel="langQuote.title"></myInput>
						<div class="nameBox">
							<myInput class="nameItem" v-model="dynamicValidateForm.quantity" :textLabel="langQuote.quantity" @input="handleInputQuantity"></myInput>
							<v-menu v-show="isGS" ref="menu1" content-class="dateSelfClass" v-model="queryDatesMenu" :close-on-content-click="false" offset-y :offset="50" max-width="350px" min-width="350px">
								<template v-slot:activator="{ on }">
									<myInput class="nameItem" v-on="on" prefix="icon-riqi1" slotIconStyle="top:54%;font-size: 20px;" v-model="dynamicValidateForm.expectTime" :textLabel="langQuote.expectedDate"></myInput>
								</template>
								<v-date-picker class="dateSelfPickerClass"  :min="minDate" v-model="date" no-title @input="queryDatesMenu = false"></v-date-picker>
							</v-menu>
						</div>
						<myTextarea v-model="dynamicValidateForm.comments" :textLabel="langSemiCustom.comments" :uploadList.sync="dynamicValidateForm.uploadList"> </myTextarea>
					</myForm>
				</v-card-text>
				<v-card-actions class="noParamInquiryBoxBtn">
					<v-btn color="primary" @click="debounceAddInquiry">
						{{ langQuote.Confirm }}
					</v-btn>
					<v-btn @click="handleClose">
						{{ langQuote.Cancel }}
					</v-btn>
				</v-card-actions>
			</v-card>
		</BaseDialog>
		<v-dialog v-model="snackbar" max-width="500px">
			<v-card>
				<v-card-title class="text-h5">
					{{ langQuote.p21 }}
				</v-card-title>
				<v-card-text>
					{{ langQuote.p22 }}
				</v-card-text>
				<v-divider></v-divider>
				<v-card-actions>
					<v-spacer></v-spacer>
					<v-btn color="primary" @click="goHome">
						{{ langQuote.Confirm }}
					</v-btn>
				</v-card-actions>
			</v-card>
		</v-dialog>
	</div>
</template>

<script>
import { otoEditInquiry } from "@/api/pins";
import myForm from "@/components/modal/Half/Detail/myForm/my-form.vue";
import myInput from "@/components/modal/Half/Detail/myForm/my-input.vue";
import myTextarea from "@/components/modal/Half/Detail/myForm/my-textarea.vue";
import BaseDialog from "@/components/Quote/BaseDialog";
import { debounce } from "@/utils/utils";
export default {
	name: "noParamInquiryBox",
	components: { BaseDialog, myForm, myInput, myTextarea },
	props: {
		infoDialogVisible: {
			type: Boolean,
			default: false,
		},
		keyword: {
			type: String,
			default: "",
		},
		cateName: {
			type: String,
			default: "",
		},
		parentCateId: {},
		cateId: {},
		fatherCateName: {},
		nuxtContentIndex: {},
		isDialog:{}
	},
	data() {
		return {
			cornorImg: "data:image/png;base64,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",
			queryDatesMenu: false,
			areaCodeOk: true,
			phoneOk: true,
			vaildateText: "",
			snackbar: false,
			date: null,
			minDate: new Date().toISOString().substring(0, 10),
			dynamicValidateForm: {
				email: "",
				firstName: "",
				lastName: "",
				telephone: "",
				subject: "",
				expectTime: "",
				quantity: "",
				comments: "",
				uploadList: [],
				// isSmsSubscriptions: 0
			},
			oldDynamicValidateFormRules: {
				subjectRules: [(v) => !!v || this.langQuote.p34],
				emailRules: [(v) => !!v || this.langQuote.p30, (v) => /.+@.+\..+/.test(v) || this.langQuote.p31],
				quantityRules: [(v) => !!v || this.langQuote.p35, (v) => /^[0-9]*$/.test(v) || this.langQuote.p36],
			},
			debounceAddInquiry: null,
		};
	},
	watch: {
		infoDialogVisible(newV) {
			if (newV) this.dynamicValidateForm.areaCode = this.areaCodes;
			this.resetPhoneRules();
		},
		date(val) {
			this.dynamicValidateForm.expectTime = this.formatDate(this.date);
		},
	},
	computed: {
		langQuote() {
			return this.$store.getters.lang.quote || {};
		},
		langSemiCustom() {
			return this.$store.getters.lang?.semiCustom;
		},
		isMobile() {
			return this.$store.getters.isMobile;
		},
		areaCodes() {
			return this.$store.state.areaCode;
		},
		proType() {
			return this.$store.state.proType;
		},
		isGS() {
			//GS体系 proType=0 且不是FD和POP
			return this.proType == 0 && this.proId != 9 && this.proId != 10;
		},
		dynamicValidateFormRules() {
			return {
				firstNameRules: [{ required: true, message: this.langQuote.p28, trigger: "blur" }],
				lastNameRules: [{ required: true, message: this.langQuote.p29, trigger: "blur" }],
				telephoneRules: [
					{ required: true, message: this.langQuote.p33, trigger: "blur" },
					{
						validator: (rule, value, callback) => {
							let rex = /^[0-9]*$/;
							if (value && !rex.test(value)) {
								callback(new Error(this.langQuote.p32));
							} else {
								callback();
							}
						},
						trigger: "blur",
					},
				],
				areaCodeRules: [{ required: true, message: this.langQuote.p38, trigger: "blur" }],
				emailRules: [
					{ required: true, message: this.langQuote.p30, trigger: "blur" },
					{
						validator: (rule, value, callback) => {
							let rex = /.+@.+\..+/;
							if (value && !rex.test(value)) {
								callback(new Error(this.langQuote.p31));
							} else {
								callback();
							}
						},
						trigger: "blur",
					},
				],
			};
		},
	},
	methods: {
		formatDate(date) {
			if (!date) return null;
			const [year, month, day] = date.split("-");
			return `${month}/${day}/${year}`;
		},
		handleClose() {
			let nuxtContent = document.querySelector(".nuxtContent");
			if (nuxtContent) {
				nuxtContent.style.zIndex = this.nuxtContentIndex;
			}
			let modalHalfListBox = document.querySelector(".modalHalfListBox");
			if (modalHalfListBox) {
				modalHalfListBox.style.zIndex = "auto";
			}
			this.$refs.dynamicValidateForm.reset();
			this.$emit("update:infoDialogVisible", false);
		},
		handleConfirm() {
			let validateResult = this.$refs.dynamicValidateForm.validate();
			if (validateResult) {
				let params = {};
				if (this.dynamicValidateForm.uploadList.length) {
					let userUrl = [];
					this.dynamicValidateForm.uploadList.forEach((item, index) => {
						userUrl.push({ img: item.secure_url });
					});
					params.Artwork = userUrl;
				}
				if (this.keyword) {
					params["Product Keywords"] = [
						{
							paramName: this.keyword,
						},
					];
				}
				if (this.dynamicValidateForm.expectTime) {
					params["Expected Delivery Date"] = [
						{
							paramName: this.dynamicValidateForm.expectTime,
						},
					];
				}
				otoEditInquiry(
					Object.assign(
						{},
						{
							quoteParam: JSON.stringify({
								fontData: {
									comments: this.dynamicValidateForm.comments,
								},
							}),
							productsName: this.fatherCateName,
							noParamRoutingName: "/" + this.cateName,
							params: params,
							quoteCateId: this.parentCateId || this.cateId,
							quoteCateChildId: this.parentCateId ? this.cateId : null,
							isMobile: this.isMobile ? 1 : 0,
							buyType: 9,
						},
						this.dynamicValidateForm,
						{
						telephone: this.dynamicValidateForm.areaCode + "-" + this.dynamicValidateForm.telephone,
					    },
					)
				).then((res) => {
					this.handleClose();
					this.snackbar = true;
				});
			}
		},
		goHome() {
			if (this.isDialog) {
				this.snackbar = false;
				this.$store.commit("setMask", false);
			}
			this.$router.push({
				path: "/",
			});
		},
		handleAllowSendMsg() {
			let numBoolean = this.dynamicValidateForm.isSmsSubscriptions ? 0 : 1;
			this.dynamicValidateForm.isSmsSubscriptions = numBoolean;
		},
		// 电话校验
		async checkTelephone() {
			this.resetPhoneRules();
			if (!this.dynamicValidateForm.areaCode) {
				this.vaildateText = this.langQuote.areaCode;
				this.areaCodeOk = false;
			}
			if (!this.dynamicValidateForm.telephone) {
				this.vaildateText = this.langQuote.telephoneRequired;
				this.phoneOk = false;
			}
			if (this.dynamicValidateForm.telephone.length < 7 && this.phoneOk) {
				this.vaildateText = this.langQuote.phoneNoMinLength;
				this.phoneOk = false;
			}
			return this.areaCodeOk && this.phoneOk;
		},
		// 重置电话校验
		resetPhoneRules() {
			this.areaCodeOk = true;
			this.phoneOk = true;
		},
		handleInputAreaCode(e) {
			// 只允许输入数字
			const v = e.target.value;
			this.dynamicValidateForm.areaCode = v.replace(/[^\d]/g, "");
		},
		handleInputTelephone(e) {
			// 只允许输入数字
			const v = e.target.value;
			this.dynamicValidateForm.telephone = v.replace(/[^\d]/g, "");
		},
		handleInputQuantity(e) {
			// 只允许输入数字
			const v = e.target.value;
			this.dynamicValidateForm.quantity = v.replace(/[^\d]/g, "");
		},
	},
	created() {},
	mounted() {
		this.debounceAddInquiry = debounce(() => {
			this.handleConfirm();
		}, 500);
	},
};
</script>

<style scoped lang="scss">
.close-icon {
	.v-icon {
		cursor: pointer;
		position: absolute;
		top: 20px;
		right: 16px;
		&::before {
			font-size: 18px;
			font-weight: 700;
			color: #333;
		}
	}
}

.dateSelfPickerClass {
	width: 100%;
	::v-deep .v-picker__body {
		width: 100% !important;
	}
}

.noParamInquiryBoxHead {
	.dialogHead {
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: space-between;
		font-size: 18px;
		font-weight: 700 !important;
		color: #333;
		.text-h5 {
			font-size: 13px;
		}
	}
	.close-icon {
		.v-icon {
			cursor: pointer;
			position: absolute;
			top: 20px;
			right: 16px;
			&::before {
				font-size: 18px;
				font-weight: 700;
				color: #333;
			}
		}
		@include respond-to(mb) {
			.close-icon {
				.v-icon {
					top: 10px;
					right: 10px;
				}
			}
		}
	}
}
.noParamInquiryBoxBtn {
	justify-content: center;
	.v-btn.v-btn {
		padding: 0 30px;
	}
}

.noParamForm {
	.nameBox {
		display: flex;
		align-items: center;
		gap: 10px;
		.nameItem {
			flex: 1;
			min-width: 0;
		}
	}
}

.telephoneBox {
	font-size: 16px;
	.textLabel {
		color: #000;
		margin-bottom: 4px;
		&::before {
			content: "*";
			color: #de3500;
		}
	}
	&.error--text {
		caret-color: #000;
		.noValid {
			border-color: #de3500 !important;
			caret-color: #de3500;
		}
	}
	.inputGroup {
		display: grid;
		grid-template-columns: 60px 1fr;
		gap: 10px;
		.customInput {
			font-size: 12px;
			color: #333;
			&.telephone {
				font-size: 16px;
			}
		}
		input {
			padding: 8px 10px;
			background: #f5f5f5;
			border: 1px solid transparent;
			border-radius: 6px;
			margin-bottom: 4px;
		}
		.areaCode {
			position: relative;
			input {
				text-indent: 1em;
			}
			.prefix {
				position: absolute;
				left: 0;
				top: 0;
				font-size: 16px;
				left: 0.5em;
				top: 0.5em;
				color: #000;
			}
			.area-cornor {
				position: absolute;
				right: 0;
				bottom: 4px;
				width: 1em;
			}
		}
	}
	.textField {
		display: flex;
		flex: 1 0 auto;
		max-width: 100%;
		min-height: 14px;
		overflow: hidden;
		line-height: 12px;
		font-size: 12px;
		word-break: break-word;
		word-wrap: break-word;
		hyphens: auto;
	}
	@include respond-to(mb) {
		font-size: 12px;
		.inputGroup {
			.customInput {
				&.telephone {
					font-size: 12px;
				}
			}
		}
	}
}
.dateSelfPickerClass {
	width: 100%;
	::v-deep .v-picker__body {
		width: 100% !important;
	}
}
</style>
