<template>
	<div class="colorSize" :class="stepData.styleName">
        <slot name="stepText"></slot>
		<div class="stepContent">
			<div class="step-item" v-for="(item, index) in stepData.productParamList" :key="item.id"
				:class="{ active: selectIndex == index || item.inputNum > 0 }" @click="selectStep(item, index)">
				<div class="imgBox">
					<img :src="item.imgDetail" alt="" />
				</div>
				<span class="introText">{{ item.colorAlias }}</span>
				<div class="inputMain">
					<div class="inputBox" @click.stop>
						<input class="priceInputStyle" @blur="selectIndex = -1" :ref="`myIunputStyle${index}`"
							:disabled="!item.stock" type="text" v-model="item.inputNum" @input="checkFn" @keyup="formatNum(item, index)"
							@change="updatePrice" placeholder="Enter Qty" />
					</div>
				</div>

				<half-design-check-icon class="absolute-top-right2 check-icon"></half-design-check-icon>
			</div>
		</div>
		<div class="errorTip">
			<v-alert dense outlined type="error">{{ langSemiCustom.miniQty }} {{ productInfo.lowestPurchaseQuantity || 1
				}}
			</v-alert>
		</div>
	</div>
</template>

<script>
export default {
	inject: ["getProductInfo"],
	name: "colorSize",
	props: {
		stepData: {
			type: Object,
			default: () => ({}),
		},
	},
	data() {
		return {
			lastIndex: -2,
			lastIndex2: -3,
			selectIndex: -1,
			selectItem: null,
		};
	},
	computed: {
		productInfo() {
			return this.getProductInfo();
		},
		langSemiCustom() {
			return this.$store.getters.lang?.semiCustom;
		},
	},
	methods: {
		formatNum(item, index) {
			item.inputNum = (item.inputNum + "").replace(/[^\d]/g, "");
			if (item.stock && item.stock > 0 && item.inputNum > item.stock) {
				item.inputNum = String(item.stock);
			}
			if (item.stock <= 0) {
				item.inputNum = "";
			}
			this.lastIndex = index;
			this.selectIndex = index;
			this.selectItem = item;
		},
		async updatePrice() {
			this.$forceUpdate();
			this.$nextTick(() => {
				let priceInputs = document.getElementsByClassName("priceInputStyle");
				let sum = Array.from(priceInputs).reduce((acc, input) => acc + parseInt(input.value || 0), 0);
				if (sum < this.productInfo.lowestPurchaseQuantity) {
					let errDom = document.getElementById(this.stepData.id).getElementsByClassName("errorTip")[0];
					errDom.style.display = "block";
				} else {
					let errDom = document.getElementById(this.stepData.id).getElementsByClassName("errorTip")[0];
					errDom.style.display = "none";
				}
				if (this.lastIndex >= 0 && this.stepData.productParamList?.[this.lastIndex].inputNum > 0 && this.lastIndex == this.lastIndex2) {
					//判断小重量交期
					this.$emit("updatePrice");
					//需要新增计算是否满足要求
					this.$emit("checkParam")
					return;
				}
				this.lastIndex2 = this.selectIndex;
				this.selectIndex = -1;
				//选择最后输入价格的
				let data = null;
				console.log(this.selectItem, '3333');
				if (this.selectItem.inputNum && this.selectItem.inputNum > 0) {
					data = this.selectItem;
				} else {
					let index = this.stepData.productParamList.findIndex((item) => {
						return item.inputNum && item.inputNum > 0;
					});
					if (index >= 0) data = this.stepData.productParamList[index];
					this.lastIndex2 = -2;
				}
				this.$emit("selectStep", {
					type: this.stepData.attributeFlag,
					data: data || this.stepData.productParamList[0],
					parent: this.stepData,
					id: this.stepData.id,
					colorIndex: this.lastIndex,
					firstSelect: false,
					cancel: !(sum > this.productInfo.lowestPurchaseQuantity),
				});
				// this.$emit("updatePrice");
			});
		},
		checkFn(){
			this.$emit("checkParam");
		},
		selectStep(item, index) {
			if (item.stock <= 0) {
				return;
			}
			item.inputNum = "";
			this.$forceUpdate();
			this.$refs[`myIunputStyle${index}`][0].focus();
			this.$emit("updatePrice");
			this.$emit("checkParam");
		},
		setInputNum(num) {
			// this.stepData.productParamList[0].inputNum = num;
			// this.selectItem = this.stepData.productParamList[0];
			// this.selectIndex = 0;
			// this.lastIndex = 0;
			// this.$forceUpdate();
			this.$emit("selectStep", {
				type: this.stepData.attributeFlag,
				data: this.stepData.productParamList[0],
				parent: this.stepData,
				id: this.stepData.id,
				colorIndex: 0,
				firstSelect: true,
			});
		},
	},
	created() { },
	mounted() {
		//默认渲染的是color 步骤,medals 没有默认选中勾选数量
		this.$Bus.$on("selectDefaultSizeStep", this.setInputNum);
	},
	beforeDestroy() {
		this.$Bus.$off("selectDefaultSizeStep", this.setInputNum);
	},
};
</script>

<style lang="scss" scoped>
@import "~assets/css/half.scss";

.colorSize {
	width: 100%;
	height: 100%;
	overflow: hidden;
	position: relative;

	.stepContent {
		width: 100%;
		height: 100%;
		box-sizing: border-box;
		display: grid;
		grid-template-columns: repeat(4, 1fr);
		gap: 10px;

		.step-item {
			@include step-default;
			position: relative;
			display: flex;
			align-items: center;
			justify-content: center;
			flex-direction: column;
			row-gap: 8px;
			background: #FAFAFA;
			border-color: transparent;

			.check-icon {
				display: none;
			}

			&.active {
				background-color: #fff;

				.check-icon {
					display: flex;
				}

				.introText {
					font-weight: 700;
				}
			}

			.imgBox {
				@include flex-center;
			}

			.introText {
				font-weight: 400;
				font-size: 13px;
				text-align: center;
			}

			.inputMain {
				display: flex;
				align-items: center;
				justify-content: space-between;
				background: #fff;
				border-radius: 4px;
				border: 1px solid #DFE2E6;
				margin: 0 6px;
				margin-bottom: 4px;

				&:focus-within {
					border: 1px solid $color-primary;
				}

				.addBtn,
				.subBtn {
					padding: 2px 4px;
					cursor: pointer;

					.v-icon {
						font-size: 20px;
					}
				}

				.subBtn:active,
				.addBtn:active {
					.v-icon {
						color: $color-primary;
					}
				}

				.inputBox {
					input {
						line-height: 30px;
						max-width: 100%;
						min-width: 0;
						width: 100%;
						text-align: center;
						font-weight: 400;
						font-size: 16px;
						color: #333333;
						text-align: center;

						&::placeholder {
							font-size: 12px;
						}

						&:focus {
							color: $color-primary;

							&::placeholder {
								opacity: 0;
							}
						}
					}
				}
			}
		}
	}
}

@include respond-to(mb) {
	.colorSize {
		.stepContent {
			grid-template-columns: repeat(3, 1fr);
			gap: 5px;

			.step-item {
				padding: 6px;
				row-gap: 6px;

				&.active {
					padding: 5px;
				}

				@media (any-hover: hover) {
					&:hover {
						padding: 5px;
					}
				}

				.introText {
					font-size: 12px;
				}

				.addBtn,
				.subBtn {
					.v-icon {
						font-size: 16px !important;
					}
				}

				.inputMain {
					.inputBox {
						input {
							font-size: 13px;
							line-height: 26px;
						}
					}
				}
			}
		}
	}
}
</style>
