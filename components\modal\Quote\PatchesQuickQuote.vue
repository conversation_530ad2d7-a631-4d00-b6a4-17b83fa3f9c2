<template>
    <div class="quoteWrap"  :class="[modal.class]">
        <div class="home" v-for="(l, li) in modal.list" :style="modal.style" :key="li">
            <div class="banner" @click="setModalType(l.banner, modal.list, 'banner')" v-if="l.banner?.value">
                <pic :src="l.banner.value" :alt="l.banner.alt" :style="modal.homeImgStyle" />
            </div>
            <div class="banner" @click="setModalType(l.video, modal.list, 'video')" v-else-if="l.video?.value" @mouseenter="setModalType(l.video, null, 'video', { type: 'enter' })">
                <video :src="l.video.value" :title="l.video.alt" :poster="l.video?.poster" :style="modal.homeVideoStyle" :loop="!modal.mousePlay" autoplay muted playsinline></video>
            </div>
            <div class="banner" @click="setModalType(l.imgList, modal.list, 'img_list', {}, li)" v-else-if="l.imgList">
                <pic v-for="(i, ii) in l.imgList" :src="i.value" :alt="i.alt" :key="i.value" :style="modal.homeImgStyle" v-show="bannerIndex == ii" preload="auto" />
            </div>
            <div flex class="content modal-box" :style="{ ...modal.contentStyle, ...l.contentStyle }">
                <div class="textContent">
                    <EditDiv :tagName="l.title.tagName || 'h2'" v-model:content="l.title.value" :style="modal.titleStyle" @click="setModalType(l.title, modal.list, 'text')" v-if="l.title?.value" />
                    <EditDiv class="describe" :tagName="l.subTitle.tagName" v-model:content="l.subTitle.value" :style="modal.subTitleStyle" @click="setModalType(l.subTitle, modal.list, 'text')" v-if="l.subTitle?.value"></EditDiv>
                    <EditDiv v-if="l.text?.value" :style="modal.textStyle" v-model:content="l.text.value"
                             :tagName="l.text.tagName" @click="setModalType(l.text, modal.list, 'text')"></EditDiv>
                    <div class="swiper-area">
                        <div class="largeSwiper">
							<div class="swiper swiper-bg" ref="largeSwiper">
								<div class="swiper-wrapper">
									<div class="swiper-slide" v-for="(item, index) in imageJson" :key="index">
										<img :src="item.url" :alt="item.alt" :title="item.alt" />
										<!-- <PicZoom :url="item.url" :alt="item.alt" :width="300" :scale="2" type="round"></PicZoom> -->
									</div>
								</div>
							</div>
                            <div class="swiper-button-next">
                                <img src="https://static-oss.gs-souvenir.com/web/quoteManage/20240905/Right_Toggle_Button_20240905KiJQ6t.png" alt="rightBtn" />
                            </div>
                            <div class="swiper-button-prev">
                                <img src="https://static-oss.gs-souvenir.com/web/quoteManage/20240905/Left_Toggle_Button_20240905wjrTGT.png" alt="leftBtn" />
                            </div>
                        </div>
                    </div>
                </div>
                <div class="rightArea">
                    <div class="quoteContent" ref="excludeElement">
                        <PatchesQuoteStep :pid="modal.quotePid" :cateId="modal.quoteCateId"></PatchesQuoteStep>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import PatchesQuoteStep from "@/components/Quote/QuickQuote/PatchesQuoteStep.vue";
import VideoPlayer from "@/components/Quote/VideoPlayer.vue";
import {scrollToViewCenter} from "@/utils/utils";
import {getInfo} from "@/api/pins";

export default {
    props: {
        data: {
            type: Object,
            default: {},
        },
    },
    data() {
        return {
            uploadType:1,
            imageJson: [],
            modal: {
                productImgStyle: {},
                homeImgStyle: {},
                style: {},
                type: {},
                list: [],
                ...this.data,
            },
        };
    },
    computed: {
        lang() {
            return this.$store.getters.lang.layout || {};
        },
        device() {
            return this.$store.state.device;
        }
    },
    components: {
        VideoPlayer,
        PatchesQuoteStep,
    },
    watch: {
        modal: {
            handler(val) {
                if (val.list?.find((i) => i.imgList)) this.bannerLength = val.list.find((i) => i.imgList).imgList.length;
                if (process.env.isManage) this.$emit("update:data", val);
            },
            immediate: true,
            deep: true,
        },
    },
    methods: {
        setModalType(target, targetArray, clickType, event, other) {
            this.$setModal(this, target, targetArray, clickType, event, other);
            scrollToViewCenter(document.querySelector(".quoteContent"));
        },
        initSwiper() {
            this.largeSwiper = new Swiper(this.$refs.largeSwiper, {
                slidesPerView: 1,
                spaceBetween: 0,
                autoplay: false,
                grabCursor: true,
                observer: true,
                observeParents: true,
                // thumbs: {
                //     swiper: this.thumbSwiper,
                // },
                navigation: {
                    nextEl: ".largeSwiper .swiper-button-next",
                    prevEl: ".largeSwiper .swiper-button-prev",
                },
            });
        },
    },
    async mounted() {
        let res = await getInfo({ id: this.modal.quoteCateId });
        let imageList = res.data.imageJson ? JSON.parse(res.data.imageJson) : [];
		if(imageList.length) {
			if(this.device!=='mb'){
				this.imageJson= imageList.filter(item=>item.name=="PC")
			}else {
				this.imageJson=imageList.filter(item=>item.name=="MB")
			}
		}else {
			this.imageJson=[]
		}
        this.$nextTick(() => {
            this.initSwiper();
        });
    },
};
</script>

<style lang="scss" scoped>
.quoteWrap {
    .home {
        position: relative;

        .banner{
            position: absolute;
            inset: 0;
            img{
                width: 100%;
                height: 100%;
                object-fit: cover;
            }
        }

        .content {
            position: relative;
            display: grid;
            grid-template-columns: 1fr 1.2fr;
            gap: 6.25em;
            padding-bottom: 1.5em;
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;

            @include respond-to(mb) {
                grid-template-columns: repeat(1, 1fr);
                gap: 1em;
            }

            .textContent {
                min-width: 0;

                .swiper-area {
                    display: flex;
                    margin-top: 1.6em;
                    height: 24.06em;
					.swiper-bg {
						z-index: -1;
						position: absolute;
						inset: 0;
						img{
							width: 100%;
							height: 100%;
							object-fit: cover;
						}
					}
                    .largeSwiper {
                        flex:1;
                        width: 0;
                        margin: 0 2em;
                        height: 100%;
                        // position: relative;
                        @include respond-to(mb){
                            margin: 0;
                        }

                        .swiper{
                            height: 100%;
                            ::v-deep{
                                .pic-img{
                                    height: 100%;
                                }
                                .img-container{
                                    height: 100%;
                                }
                            }
                        }

                        .swiper-slide{
                            display: flex;
                            justify-content: center;
                            align-items: center;
                        }

                        .swiper-button-next::after,
                        .swiper-button-prev::after {
                            display: none;
                        }
						.swiper-button-next {
							right:3vw;
						}
						.swiper-button-prev {
							left:3vw;
						}
                        .swiper-button-next,
                        .swiper-button-prev {
                            width: 3em;
                            @include respond-to(mb) {
								top:23%;
                                width: 2.5em;
                            }
                        }
                    }

                    .thumbSwiper {
                        flex-basis: 5em;
                        height: 100%;
                        .swiper{
                            height: 100%;
                        }

                        @include respond-to(mb) {
                            padding: 0;
                            width: 100%;
                        }

                        .swiper-slide {
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            border-radius: 8px;
                            border: 1px solid transparent;
                            background-color: rgba(255, 255, 255, 0.25);
                            box-sizing: border-box;
                            // cursor: pointer;

                            @include respond-to(mb){
                                background-color: rgba(255,255,255,0.25);
                            }

                            &.swiper-slide-thumb-active {
                                border-color: $color-primary;
                            }
                        }
                    }
                }

                @include respond-to(mb) {
                    padding-top: 0;
                    margin-bottom: 10px;
                }
            }

            .rightArea {
                min-width: 0;
                @include respond-to(mb) {
                    .stepBar {
                        display: none;
                    }
                }
            }
        }
    }
}
</style>
