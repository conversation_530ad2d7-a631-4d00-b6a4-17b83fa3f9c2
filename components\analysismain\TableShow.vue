<template>
  <div id="table-show">
    <div class="top">WHAT'S YOUR TOP SELLING PRODUCT CATEGORY?</div>
    <div class="main">
      <!-- <div class="table-row-1">
				<analysismain-TableShowHeader />
				<analysismain-TableShowItem class="analysismain-TableShowItem-set-margin" />
			</div>
			<div class="table-row-remain" v-for="item in 11" :key="item">
				<analysismain-TableShowItem />
			</div> -->
      <v-data-table :headers="headers" :items="desserts" hide-default-footer>
        <template v-slot:header.product="{ header  }">
          <span class="ml-4">{{ header.text  }}</span>
        </template>
        <template v-slot:item.id="{ item }">
          <span style="color: #999999">{{ item.id }}</span>
        </template>

        <template v-slot:item.product="{ item }">
          <v-avatar class="mr-2 ml-4">
            <!-- <img src="https://cdn.vuetifyjs.com/images/john.jpg" alt="John" /> -->
            <img :src="item.image" alt="John" />
          </v-avatar>
          {{ item.product }}
        </template>

        <template v-slot:item.inquiry="{ item }">
          <span style="color: #999999">{{ item.inquiry }}</span>
        </template>
        <template v-slot:item.orderQty="{ item }">
          <span style="color: #999999">{{ item.orderQty }}</span>
        </template>
        <template v-slot:item.total="{ item }">
          <span style="font-weight: 900;">${{ item.total }}</span>
        </template>
        <template v-slot:item.orderRate="{ item }">
          <span style="color: #999999">{{ item.orderRate }}</span>
        </template>
        <template v-slot:item.avg="{ item }">
          <span>${{ item.avg }}</span>
        </template>
      </v-data-table>
    </div>
  </div>
</template>

<script>
export default {
  name: "TableShow",
  props: {
    currentPage: {
      type: [Number, String],
      default: 1
    },
    jumpPage: {
      type: [Number, String],
      default: 1
    },
    desserts: {
      type: Array,
      default() {
        return []
      }
    }
  },
  data() {
    return {
      headers: [
        {
          text: "# ID",
          align: "start",
          sortable: false,
          value: "id",
        },
        { text: "Product Image & Name", value: "product", align: "left" },
        { text: "Inquiry Qty", value: "inquiry", align: "center" },
        { text: "Order Qty", value: "orderQty", align: "center" },
        { text: "Total Amount", value: "total", align: "center" },
        { text: "Order Rate", value: "orderRate", align: "center" },
        { text: "Avg. Order Value", value: "avg", align: "center" },
      ],
      // desserts: [
      // {
      //   id: "# 1234",
      //   product: "Lapel Pins",
      //   inquiry: 1499,
      //   orderQty: 1253,
      //   total: 149990,
      //   orderRate: "15%",
      //   avg: 172,
      // },
      // {
      //   id: "# 1234",
      //   product: "Lapel Pins",
      //   inquiry: 1499,
      //   orderQty: 1253,
      //   total: 149990,
      //   orderRate: "15%",
      //   avg: 172,
      // },
      // {
      //   id: "# 1234",
      //   product: "Lapel Pins",
      //   inquiry: 1499,
      //   orderQty: 1253,
      //   total: 149990,
      //   orderRate: "15%",
      //   avg: 172,
      // },
      // {
      //   id: "# 1234",
      //   product: "Lapel Pins",
      //   inquiry: 1499,
      //   orderQty: 1253,
      //   total: 149990,
      //   orderRate: "15%",
      //   avg: 172,
      // },
      // {
      //   id: "# 1234",
      //   product: "Lapel Pins",
      //   inquiry: 1499,
      //   orderQty: 1253,
      //   total: 149990,
      //   orderRate: "15%",
      //   avg: 172,
      // },
      // ],
    };
  },
  watch: {
    // desserts: {
    //   handler(newValue) {
    //     this.desserts = newValue
    //     console.log('desserts', newValue, this.desserts);
    //   }
    // },
    currentPage: {
      handler(newValue, Oldvalue) {
        console.log(newValue, Oldvalue);
      }
    },
    jumpPage(newValue, Oldvalue) {
      console.log(newValue, Oldvalue);
    }
  }
};
</script>

<style lang="scss" scoped>
#table-show {
  width: 100%;
}

.top {
  width: 23.69791667vw;
  height: 1.5625vw;
  margin-left: 0.98958333vw;
  /* background: #e5e5e5; */
  border-radius: 0.3125vw;
  margin-bottom: 0.8854vw;
  font-size: 0.9375vw;
  font-weight: 400;
  color: #333333;
}

.main {
  height: 45.9375vw;
  background: #fff;
  border-radius: 0.5208vw;
  padding-right: 1.6146vw;
  padding-left: 1.3542vw;

  .table-row-1 {
    height: 6.3021vw;
    padding-top: 1.25vw;
    padding-bottom: 0.625vw;
    border-bottom: 1px solid #e5e5e5;

    .analysismain-TableShowItem-set-margin {
      margin-top: 0.4688vw;
    }
  }

  .table-row-remain {
    padding-top: 0.5208vw;
    padding-bottom: 0.625vw;
    border-bottom: 1px solid #e5e5e5;
  }

  .table-row-remain:last-child {
    border: none;
  }
}
</style>
