[{"id": "customFidgetSpinners_01", "name": "Card", "theme": 20, "style": {"padding-bottom": 0, "background-color": "#f3f4f5"}, "titleStyle": {"font-size": "2.25em"}, "subTitleStyle": {"font-size": "1em", "margin-top": "1em", "margin-bottom": "0"}, "outer": [{"title": {"value": "Our Best-selling Fidget Spinner Styles"}, "subTitle": {"value": "Spread brand recognition with custom fidget spinners! These cool fidget spinner are popular among people of all ages and have become exquisite gifts given to customers at many events. Don’t worry, you won’t need to perform cool fancy tricks, you can still promote in style!"}}], "list": []}, {"id": "customFidgetSpinners_02", "name": "Card", "class": "hover-content-gray hover-btn-outline", "theme": 20, "column": 4, "linkArea": "card", "style": {"padding-top": 0, "padding-bottom": 0, "background-color": "#f3f4f5"}, "titleStyle": {"text-align": "left", "font-size": "1.375em", "margin-top": "1.875em"}, "subTitleStyle": {"display": "none"}, "cardBoxStyle": {"margin-top": "1.25em"}, "cardStyle": {"flex-direction": "column", "border-radius": "0.625em", "background-color": "#fff"}, "cardImgStyle": {"min-height": "15.3125em"}, "cardTextStyle": {"display": "flex", "position": "absolute", "gap": "0 0.3125em", "top": "0.75em", "left": "50%", "transform": "translateX(-50%)"}, "cardContentStyle": {"gap": "0", "padding-left": "1.25em", "padding-right": "1.25em"}, "cardTitleStyle": {"margin-top": "0.555556em", "font-size": "1.125em", "text-align": "center", "line-height": "normal"}, "cardPriceListStyle": {"margin-block-start": "1em", "margin-block-end": "1em", "text-align": "center", "font-size": "1em"}, "cardBtnBoxStyle": {"margin-bottom": "1.111111em", "text-align": "center"}, "cardBtnStyle": {"padding-top": "0.555556em", "padding-bottom": "0.555556em", "padding-left": "2.777778em", "padding-right": "2.777778em", "font-size": "1.125em", "font-weight": "bold", "line-height": "1.3333333em", "border": "0.049383em solid #D24600", "border-radius": "1.277778em", "height": "unset", "min-width": "unset"}, "outer": [{"title": {"value": "Hottest Styles"}}], "list": [{"img": {"value": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20240701/ueditor/95/custom-classic-hand-finger-spinner.png", "alt": "Custom Classic Hand Finger Spinner"}, "cornerLabelType": "right", "cornerLabel": {"id": 1, "value": "", "icon": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20240227/ueditor/97/popular-style-icon.png"}, "title": {"value": "Custom Classic Hand Finger Spinner"}, "text": {"value": "<span style=\"display: block; width: 0.6875em; height: 0.6875em; border-radius: 50%;background-color: #474E9E\"></span><span style=\"display: block; width: 0.6875em; height: 0.6875em;border-radius: 50%; background-color: #E291DE\"></span><span style=\"display: block; width: 0.6875em; height: 0.6875em; border-radius: 50%;background-color: #971E15\"></span>"}, "priceList": [{"value": "As low as"}, {"value": 2.51, "style": {"color": "#D24600", "font-weight": "normal", "font-size": "1.125em"}}, {"value": "ea."}], "button": {"value": "Order Now", "alt": "Finger Spinner", "method": {"modal": "modalQuoteHalfDetail", "name": "/custom-fidget-spinners/custom-classic-hand-finger-spinner", "componentName": "modalHalfDetail"}}}, {"img": {"value": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20240227/ueditor/97/custom-rainbow-fidget-spinner-toys.png", "alt": "Rainbow Fidget Spinner Toys"}, "cornerLabelType": "right", "cornerLabel": {"id": 1, "value": "", "icon": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20240227/ueditor/97/popular-style-icon.png"}, "text": {"value": "<span style=\"display: block; width: 0.6875em; height: 0.6875em; border-radius: 50%;background-image: url(https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20240227/ueditor/97/rainbow-color.png);\"></span>"}, "title": {"value": "Custom Rainbow Fidget Spinner Toys"}, "priceList": [{"value": "As low as"}, {"value": 4.64, "style": {"color": "#D24600", "font-weight": "normal", "font-size": "1.125em"}}, {"value": "ea."}], "button": {"value": "Order Now", "alt": "Rainbow Fidget Spinner Toys", "method": {"modal": "modalQuoteHalfDetail", "name": "/custom-fidget-spinners/custom-rainbow-fidget-spinner-toys", "componentName": "modalHalfDetail"}}}, {"img": {"value": "https://static-oss.gs-souvenir.com/web/20250418/Custom_Solid_Color_Tri-leaf_Promotional_Fidget_Toys_20250418hDNC5d.jpg", "alt": "Custom Fidget Spinners Suction Toys"}, "title": {"value": "Custom Solid Color Tri-leaf Promotional Fidget Toys"}, "priceList": [{"value": "As low as"}, {"value": 1.66, "style": {"color": "#D24600", "font-weight": "normal", "font-size": "1.125em"}}, {"value": "ea."}], "button": {"value": "Order Now", "alt": "Solid Color Tri-leaf Promotional Fidget Toys", "method": {"modal": "modalQuoteHalfDetail", "name": "/custom-fidget-spinners/custom-solid-color-tri-leaf-promotional-fidget-toys", "componentName": "modalHalfDetail"}}}, {"img": {"value": "https://static-oss.gs-souvenir.com/web/20250418/Custom_Lightweight_Fidget_Toys_Glow-in-the-Dark_20250418PrPyKs.jpg", "alt": "Custom Lightweight Fidget Toys Glow-in-the-Dark"}, "title": {"value": "Custom Lightweight Fidget Toys Glow-in-the-Dark"}, "priceList": [{"value": "As low as"}, {"value": 1.34, "style": {"color": "#D24600", "font-weight": "normal", "font-size": "1.125em"}}, {"value": "ea."}], "button": {"value": "Order Now", "alt": "Lightweight Fidget Toys Glow-in-the-Dark", "method": {"modal": "modalQuoteHalfDetail", "name": "/custom-fidget-spinners/custom-lightweight-fidget-toys-glow-in-the-dark", "componentName": "modalHalfDetail"}}}]}, {"id": "customFidgetSpinners_03", "name": "Card", "class": "hover-content-gray hover-btn-outline", "theme": 20, "column": 4, "linkArea": "card", "style": {"padding-top": 0, "padding-bottom": 0, "background-color": "#f3f4f5"}, "titleStyle": {"text-align": "left", "font-size": "1.375em", "margin-top": "1.363636em"}, "subTitleStyle": {"display": "none"}, "cardBoxStyle": {"margin-top": "1.25em"}, "cardStyle": {"flex-direction": "column", "border-radius": "0.625em", "background-color": "#fff"}, "cardImgStyle": {"min-height": "15.3125em"}, "cardTextStyle": {"display": "flex", "position": "absolute", "gap": "0 0.3125em", "top": "0.75em", "left": "50%", "transform": "translateX(-50%)"}, "cardContentStyle": {"gap": "0"}, "cardTitleStyle": {"margin-top": "0.555556em", "font-size": "1.125em", "text-align": "center", "line-height": "normal"}, "cardPriceListStyle": {"margin-block-start": "1em", "margin-block-end": "1em", "text-align": "center", "font-size": "1em"}, "cardBtnBoxStyle": {"margin-bottom": "1.111111em", "text-align": "center"}, "cardBtnStyle": {"padding-top": "0.555556em", "padding-bottom": "0.555556em", "padding-left": "2.777778em", "padding-right": "2.777778em", "font-size": "1.125em", "font-weight": "bold", "line-height": "1.333333em", "border": "0.055556 solid #D24600", "border-radius": "1.277778em", "height": "unset", "min-width": "unset"}, "outer": [{"title": {"value": "Multiple Uses"}}], "list": [{"img": {"value": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20240227/ueditor/97/custom-led-lights-fidget-spinner.png", "alt": "Custom LED Lights Fidget Spinner"}, "cornerLabelType": "right", "cornerLabel": {"id": 1, "value": "", "icon": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20240227/ueditor/97/popular-style-icon.png"}, "text": {"value": "<span style=\"display: block; width: 0.6875em; height: 0.6875em;border-radius: 50%; background-color: yellow\"></span><span style=\"display: block; width: 0.6875em; height: 0.6875em; border-radius: 50%;background-color: blue\"></span><span style=\"display: block; width: 0.6875em; height: 0.6875em; border-radius: 50%;background-color: white\"></span>"}, "title": {"value": "Custom LED Lights Fidget<br>Spinner"}, "priceList": [{"value": "As low as"}, {"value": 2.15, "style": {"color": "#D24600", "font-weight": "normal", "font-size": "1.125em"}}, {"value": "ea."}], "button": {"value": "Order Now", "alt": "Custom Led Lights Fidget Spinner", "method": {"modal": "modalQuoteHalfDetail", "name": "/sc/custom-fidget-spinners/custom-led-lights-fidget-spinner", "componentName": "modalHalfDetail"}}}, {"img": {"value": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20240227/ueditor/97/custom-clear-led-light-fidget-spinner-toys.png", "alt": "Custom Clear LED Light Fidget Spinner Toys"}, "title": {"value": "Custom Clear LED Light Fidget<br>Spinner Toys"}, "priceList": [{"value": "As low as"}, {"value": 2.29, "style": {"color": "#D24600", "font-weight": "normal", "font-size": "1.125em"}}, {"value": "ea."}], "button": {"value": "Order Now", "alt": "Custom Clear LED Light Fidget Spinner Toys", "method": {"modal": "modalQuoteHalfDetail", "name": "/custom-fidget-spinners/custom-clear-led-light-fidget-spinner-toys", "componentName": "modalHalfDetail"}}}, {"img": {"value": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20240227/ueditor/97/printed-bottle-up-fidget-spinner-with-logo.png", "alt": "Custom Fun Spinner Bottle Opener"}, "text": {"value": "<span style=\"display: block; width: 0.6875em; height: 0.6875em; border-radius: 50%;background-color: red;\"></span><span style=\"display: block; width: 0.6875em; height: 0.6875em; border-radius: 50%;background-color: black;\"></span><span style=\"display: block; width: 0.6875em; height: 0.6875em; border-radius: 50%;background-color: blue;\"></span>"}, "title": {"value": "Custom Fun Spinner<br><PERSON><PERSON>er"}, "priceList": [{"value": "As low as"}, {"value": 2.37, "style": {"color": "#D24600", "font-weight": "normal", "font-size": "1.125em"}}, {"value": "ea."}], "button": {"value": "Order Now", "alt": "Custom Fun Bottle Opener Fidget Spinner", "method": {"modal": "modalQuoteHalfDetail", "name": "/custom-fidget-spinners/custom-fun-bottle-opener-fidget-spinner", "componentName": "modalHalfDetail"}}}, {"img": {"value": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20240227/ueditor/97/plastic-printed-fidget-spinner.png", "alt": "Custom Bouncing Tire Spinning Top"}, "text": {"value": "<span style=\"display: block; width: 0.6875em; height: 0.6875em; border-radius: 50%;background-color: black\"></span><span style=\"display: block; width: 0.6875em; height: 0.6875em;border-radius: 50%; background-color: blue\"></span><span style=\"display: block; width: 0.6875em; height: 0.6875em; border-radius: 50%;background-color: orange\"></span><span style=\"display: block; width: 0.6875em; height: 0.6875em; border-radius: 50%;background-color: pink\"></span>"}, "title": {"value": "Custom Bouncing Tire<br>Spinning Top"}, "priceList": [{"value": "As low as"}, {"value": 4.3, "style": {"color": "#D24600", "font-weight": "normal", "font-size": "1.125em"}}, {"value": "ea."}], "button": {"value": "Order Now", "alt": "Custom Bouncing Tire Spinning Top", "method": {"modal": "modalQuoteHalfDetail", "name": "/custom-fidget-spinners/custom-bouncing-tire-spinning-top", "componentName": "modalHalfDetail"}}}]}, {"id": "customFidgetSpinners_04", "name": "Card", "class": "hover-content-gray hover-btn-outline", "theme": 20, "column": 4, "linkArea": "card", "style": {"padding-top": 0, "padding-bottom": 0, "background-color": "#f3f4f5"}, "titleStyle": {"text-align": "left", "font-size": "1.375em", "margin-top": "1.363636em"}, "subTitleStyle": {"display": "none"}, "cardBoxStyle": {"margin-top": "1.25em"}, "cardStyle": {"flex-direction": "column", "border-radius": "0.625em", "background-color": "#fff"}, "cardImgStyle": {"min-height": "15.3125em"}, "cardTextStyle": {"display": "flex", "position": "absolute", "gap": "0 0.3125em", "top": "0.75em", "left": "50%", "transform": "translateX(-50%)"}, "cardContentStyle": {"gap": "0"}, "cardTitleStyle": {"margin-top": "0.555556em", "font-size": "1.125em", "text-align": "center", "line-height": "normal"}, "cardPriceListStyle": {"margin-block-start": "1em", "margin-block-end": "1em", "text-align": "center", "font-size": "1em"}, "cardBtnBoxStyle": {"margin-bottom": "1.111111em", "text-align": "center"}, "cardBtnStyle": {"padding-top": "0.555556em", "padding-bottom": "0.555556em", "padding-left": "2.777778em", "padding-right": "2.777778em", "font-size": "1.125em", "font-weight": "bold", "line-height": "1.333333em", "border": "0.055556em solid #D24600", "border-radius": "1.277778em", "height": "unset", "min-width": "unset"}, "outer": [{"title": {"value": "Metal Fidget Spinners"}}], "list": [{"img": {"value": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20240701/ueditor/95/custom-metal-wheel-fidget-spinner.png", "alt": "Custom Metal Wheel Fidget Spinner"}, "cornerLabelType": "right", "cornerLabel": {"id": 1, "value": "", "icon": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20240227/ueditor/97/popular-style-icon.png"}, "text": {"value": "<span style=\"display: block; width: 0.6875em; height: 0.6875em; border-radius: 50%;background-color: gold\"></span><span style=\"display: block; width: 0.6875em; height: 0.6875em;border-radius: 50%; background-color: silver\"></span>"}, "title": {"value": "Custom Metal Wheel<br>Fidget Spinner"}, "priceList": [{"value": "As low as"}, {"value": 5.26, "style": {"color": "#D24600", "font-weight": "normal", "font-size": "1.125em"}}, {"value": "ea."}], "button": {"value": "Order Now", "alt": "Custom Metal Wheel Fidget Spinner", "method": {"modal": "modalQuoteHalfDetail", "name": "/custom-fidget-spinners/custom-metal-wheel-fidget-spinner", "componentName": "modalHalfDetail"}}}, {"img": {"value": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20240227/ueditor/97/rudder-shaped-brass-fidget-spinner.png", "alt": "Custom Rudder Shaped Brass Fidget Spinner"}, "cornerLabelType": "right", "cornerLabel": {"id": 1, "value": "", "icon": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20240227/ueditor/97/popular-style-icon.png"}, "text": {"value": "<span style=\"display: block; width: 0.6875em; height: 0.6875em; border-radius: 50%;background-color: rgb(184, 115, 51)\"></span>"}, "title": {"value": "Rudder Shaped Brass<br><PERSON><PERSON><PERSON> Spinner"}, "priceList": [{"value": "As low as"}, {"value": 4.93, "style": {"color": "#D24600", "font-weight": "normal", "font-size": "1.125em"}}, {"value": "ea."}], "button": {"value": "Order Now", "alt": "Custom Rudder Shaped Brass Fidg", "method": {"modal": "modalQuoteHalfDetail", "name": "/custom-fidget-spinners/custom-rudder-shaped-fidget-spinner", "componentName": "modalHalfDetail"}}}, {"img": {"value": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20240227/ueditor/97/brass-hexgonal-detachable-rudder-fidget-spinner.png", "alt": "Custom Detachable Hexagonal Rudder Fidget Spinner"}, "cornerLabelType": "right", "cornerLabel": {"id": 1, "value": "", "icon": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20240227/ueditor/97/popular-style-icon.png"}, "text": {"value": "<span style=\"display: block; width: 0.6875em; height: 0.6875em; border-radius: 50%;background-color: rgb(184, 115, 51)\"></span>"}, "title": {"value": "Custom Detachable Hexagonal<br>Rudder Fidget Spinner"}, "priceList": [{"value": "As low as"}, {"value": 4.82, "style": {"color": "#D24600", "font-weight": "normal", "font-size": "1.125em"}}, {"value": "ea."}], "button": {"value": "Order Now", "alt": "Custom Detachable Hexagonal Rudder Fidget Spinner", "method": {"modal": "modalQuoteHalfDetail", "name": "/custom-fidget-spinners/custom-detachable-hexagonal-rudder-fidget-spinner", "componentName": "modalHalfDetail"}}}, {"img": {"value": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20240701/ueditor/95/custom-metal-dual-fidget-spinner-hand-toy.png", "alt": "Custom Metal Dual Fidget Spinner Hand Toy"}, "text": {"value": "<span style=\"display: block; width: 0.6875em; height: 0.6875em; border-radius: 50%;background-color: rgb(183, 110, 121)\"></span><span style=\"display: block; width: 0.6875em; height: 0.6875em;border-radius: 50%; background-color: silver\"></span>"}, "title": {"value": "Custom Metal Dual Fidget<br>Spinner Hand Toy"}, "priceList": [{"value": "As low as"}, {"value": 7.37, "style": {"color": "#D24600", "font-weight": "normal", "font-size": "1.125em"}}, {"value": "ea."}], "button": {"value": "Order Now", "alt": "Custom Metal Dual Fidget Spinner Hand Toy", "method": {"modal": "modalQuoteHalfDetail", "name": "/custom-fidget-spinners/custom-metal-dual-fidget-spinner-hand-toy", "componentName": "modalHalfDetail"}}}]}, {"id": "customFidgetSpinners_05", "name": "Card", "class": "hover-content-gray hover-btn-outline", "theme": 20, "column": 4, "linkArea": "card", "style": {"padding-top": 0, "background-color": "#f3f4f5"}, "titleStyle": {"text-align": "left", "font-size": "1.375em", "margin-top": "1.363636em"}, "subTitleStyle": {"display": "none"}, "cardBoxStyle": {"margin-top": "1.25em"}, "cardStyle": {"flex-direction": "column", "border-radius": "0.625em", "background-color": "#fff"}, "cardImgStyle": {"min-height": "15.3125em"}, "cardTextStyle": {"display": "flex", "position": "absolute", "gap": "0 0.3125em", "top": "0.75em", "left": "0.75em"}, "cardContentStyle": {"gap": "0"}, "cardTitleStyle": {"margin-top": "0.555556em", "font-size": "1.125em", "text-align": "center", "line-height": "normal"}, "cardSubTitleStyle": {"font-size": "1em", "margin-top": "0.625em", "margin-bottom": "0.625em", "text-align": "center"}, "cardBtnBoxStyle": {"margin-bottom": "1.111111em", "text-align": "center"}, "cardBtnStyle": {"padding-top": "0.555556em", "padding-bottom": "0.555556em", "padding-left": "2.777778em", "padding-right": "2.777778em", "font-size": "1.125em", "font-weight": "bold", "line-height": "1.333333em", "border": "0.0493823em solid #D24600", "border-radius": "1.277778em", "height": "unset", "min-width": "unset"}, "outer": [{"title": {"value": "100% Customization Spinners"}}], "list": [{"img": {"value": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20240701/ueditor/95/custom-enamel-fidget-spinners.png", "alt": "Custom Enamel Fidget Spinners"}, "cornerLabelType": "right", "cornerLabel": {"id": 1, "value": "", "icon": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20240227/ueditor/97/100-customization-icon.png"}, "title": {"value": "Custom Enamel<br>Fidget Spinners"}, "subTitle": {"value": "Totally customized. All the components designed and produced from scratch"}, "button": {"value": "Customize", "alt": "custom fidget spinners", "method": {"modal": "modalQuoteDialog", "quoteUrl": "/quote/enamel-fidget-spinner-quote"}}}, {"img": {"value": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20240701/ueditor/95/custom-3d-fidget-spinners.png", "alt": "Custom 3D Fidget Spinners"}, "cornerLabelType": "right", "cornerLabel": {"id": 1, "value": "", "icon": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20240227/ueditor/97/100-customization-icon.png"}, "title": {"value": "Custom 3D<br>Fidget Spinners"}, "subTitle": {"value": "Totally customized. All the components designed and produced from scratch"}, "button": {"value": "Customize", "alt": "custom fidget spinners", "method": {"modal": "modalQuoteDialog", "quoteUrl": "/quote/3D-fidget-spinner-quote"}}}, {"img": {"value": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20240227/ueditor/97/custom-die-struck-no-color-fidget-spinner.png", "alt": "Custom Die-struck Fidget Spinners"}, "cornerLabelType": "right", "cornerLabel": {"id": 1, "value": "", "icon": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20240227/ueditor/97/100-customization-icon.png"}, "title": {"value": "Custom Die-Struck<br>Fidget Spinners"}, "subTitle": {"value": "Totally customized. All the components designed and produced from scratch"}, "button": {"value": "Customize", "alt": "custom fidget spinners", "method": {"modal": "modalQuoteDialog", "quoteUrl": "/quote/die-struck-fidget-spinner-quote"}}}, {"img": {"value": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20240227/ueditor/97/custom-glow-in-the-dark-fidget-spinners.png", "alt": "Custom Glow-in-the-dark Fidget Spinners"}, "cornerLabelType": "right", "cornerLabel": {"id": 1, "value": "", "icon": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20240227/ueditor/97/100-customization-icon.png"}, "title": {"value": "Custom Glow-in-the-dark<br>Fidget Spinners"}, "subTitle": {"value": "Totally customized. All the components designed and produced from scratch"}, "button": {"value": "Customize", "alt": "Custom Glow-in-the-dark Fidget Spinners", "method": {"modal": "modalQuoteDialog", "quoteUrl": "/quote/glow-in-the-dark-fidget-spinner-quote"}}}]}, {"id": "customFidgetSpinners_06", "name": "Twins", "theme": 20, "style": {"background-color": "rgb(23, 23, 25)", "flex-direction": "column"}, "outer": [{"name": "Summary", "style": {"padding-top": "3.75em", "padding-bottom": "3.75em", "border-bottom": "0.0625em solid rgba(255,255,255,0.1)"}, "boxStyle": {"gap": "3%"}, "imgStyle": {"width": "37.75em", "flex-grow": 0}, "contentStyle": {"gap": 0}, "titleStyle": {"font-size": "2.25em", "color": "#FFFFFF", "line-height": "normal"}, "subTitleStyle": {"margin-block-start": "1em", "margin-block-end": "1em", "font-size": "1em", "color": "#FFFFFF"}, "btnBoxStyle": {"margin-top": "0.9375em"}, "btnStyle": {"border-radius": "0.333333em", "padding": "0.555556em 2.777778em", "height": "unset", "min-width": "unset", "font-size": "1.125em", "line-height": "normal"}, "outer": [{"img": {"value": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20240226/ueditor/97/cheap-fidget-spinners-for-different-colors.png", "alt": "best fidget spinner"}, "title": {"value": "100% Customized Fidget Spinner or 24-hour Fidget Spinner ? Create your unique style today!"}, "subTitle": {"value": "24-hour Fidget spinners are typically built using existing templates or design elements. with optional design areas, colors, printing or engraving text, and the common materials plastic or metal are usually used. The manufacturing process is relatively simple and is generally in mass production mode. They are cost-effective and have quick production time and delivery."}, "button": {"value": "Start Ordering Now", "alt": "order fidget spinner 24 hours", "method": {"modal": "modalQuoteHalfDetail", "name": "/custom-fidget-spinners", "componentName": "modalHalfList4"}}}]}], "list": [{"name": "Summary", "style": {"padding-top": "3.75em", "padding-bottom": "3.75em"}, "boxStyle": {"gap": "3%"}, "imgStyle": {"order": 2, "width": "37.75em", "flex-grow": 0}, "contentStyle": {"gap": 0}, "titleStyle": {"font-size": "2.25em", "color": "#FFFFFF", "line-height": "normal"}, "subTitleStyle": {"margin-block-start": "1em", "margin-block-end": "1em", "font-size": "1em", "color": "#FFFFFF"}, "btnBoxStyle": {"margin-top": "0.9375em"}, "btnStyle": {"border-radius": "0.333333em", "padding": "0.555556em 2.777778em", "height": "unset", "min-width": "unset", "font-size": "1.125em", "line-height": "normal"}, "outer": [{"img": {"value": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20240226/ueditor/97/100-customized-fidget-spinner.png", "alt": "fidget spinner customizable"}, "title": {"value": "100% Customized Fidget Spinner From Scratch"}, "subTitle": {"value": "• Our personalized fidget spinners are fully customizable , they can be made exactly to your needs.<br>• Available for any style, shape, size, and color.<br><br>You can choose your fidget spinner styles with color, without color, 3D styles, print, or engraved styles. We have excellent skilled workers to help you with high-quality fidget spinner products."}, "button": {"value": " Get a Free Quote & Quick Order", "alt": "Order custom fidget spinners", "method": {"modal": "modalQuoteDialog", "quoteUrl": "/quote/enamel-fidget-spinner-quote/"}}}]}]}, {"id": "customFidgetSpinners_07", "name": "Card", "theme": 20, "column": 2, "margin": 4.666667, "style": {"background-color": "rgb(243,244,245)"}, "titleStyle": {"line-height": "normal"}, "subTitleStyle": {"margin-top": "1em", "margin-bottom": "1em", "font-size": "1em"}, "cardBoxStyle": {"margin-top": "1.875em"}, "boxStyle": {"row-gap": "2em"}, "cardStyle": {"align-items": "center"}, "cardImgStyle": {"width": "16.5em"}, "cardContentStyle": {"row-gap": "0.5em", "padding-left": "1em", "padding-right": "1em", "padding-top": "2em", "padding-bottom": "2em", "border-radius": "0 1em 1em 0", "background-color": "#ffffff"}, "cardTitleStyle": {"font-size": "1.25em"}, "cardTextStyle": {"font-size": "1.125em", "opacity": "1"}, "boxBtnStyle": {"margin-top": "0.9375em"}, "btnStyle": {"height": "unset", "min-width": "unset", "font-size": "1.125em", "border-radius": "0.333333em", "padding": "0.555556em 2.777778em", "line-height": "normal"}, "outer": [{"title": {"value": "Fidget Toys or Funny Fidget Spinners for Different Occasions"}, "subTitle": {"value": "Custom Fidget Spinners, popular custom fidget toys, are suitable for people of all age groups. They gained popularity due to their simple<br>operations and interesting spinning actions. Experience the captivating charm of Finger Spinning!"}}], "list": [{"img": {"value": "https://static-oss.gs-souvenir.com/web/quoteManage/20250320/fidget_spinner_customizable_20250320pmdQKi.jpg", "alt": "fidget spinner customizable"}, "title": {"value": "For Adults or Children"}, "subTitle": false, "text": {"value": "• family leisure time <br>• fidget spinner customizable<br>• Increase parent-child interaction among family"}}, {"img": {"value": "https://static-oss.gs-souvenir.com/web/quoteManage/20250320/adult_fidget_spinners_20250320ZPXwbZ.jpg", "alt": "adult fidget spinners"}, "title": {"value": "For the Younger Generation"}, "subTitle": false, "text": {"value": "• personalized fidget spinners showcase their personalities<br>• different tricks"}}, {"img": {"value": "https://static-oss.gs-souvenir.com/web/quoteManage/20250320/spinners_fidget_20250320sp8YH2.jpg", "alt": "spinners fidget"}, "title": {"value": "Custom Fidget Spinners for the Office"}, "subTitle": false, "text": {"value": "• best fidget spinner relieves work stress<br>• improves work efficiency"}}, {"img": {"value": "https://static-oss.gs-souvenir.com/web/quoteManage/20250320/classic_promotional_fidget_spinners_20250320hGy45S.jpg", "alt": "classic promotional fidget spinners"}, "title": {"value": "For Social gatherings"}, "subTitle": false, "text": {"value": "• branded fidget spinners are part of social activities<br>• inspire competition and enhance communication"}}, {"img": {"value": "https://static-oss.gs-souvenir.com/web/quoteManage/20250320/cool_fidget_spinners_20250320NXfJkw.jpg", "alt": "cool fidget spinner"}, "title": {"value": "cool fidget spinners"}, "subTitle": false, "text": {"value": "• promotional fidget spinner,adjust their mind and body<br>• improve concentration"}}, {"img": {"value": "https://static-oss.gs-souvenir.com/web/quoteManage/20250320/cool_looking_fidget_spinners_20250320K8Q8AT.jpg", "alt": "cool looking fidget spinners"}, "title": {"value": "For Anxiety and Autism"}, "subTitle": false, "text": {"value": "• a simple and relaxing way<br>• alleviate tense emotions and anxiety"}}]}, {"id": "customFidgetSpinners_08", "theme": "20", "name": "Card", "style": {"background-color": "rgb(23, 23, 25)"}, "titleStyle": {"font-size": "2.25em", "color": "#FFFFFF", "line-height": "normal", "text-align": "left"}, "subTitleStyle": {"width": "20%", "height": "0.25em", "background": "#D24600", "margin-left": "0", "margin-right": "0", "margin-top": "1em", "margin-bottom": "0"}, "textStyle": {"font-size": "1.25em", "color": "#FFFFFF", "margin-top": "1.75em", "margin-bottom": "1.75em", "line-height": "normal"}, "cardBoxStyle": {"position": "absolute", "top": "0", "left": "0", "z-index": "0", "height": "100%"}, "boxStyle": {"height": "100%"}, "cardImgStyle": {"height": "100%", "object-fit": "cover"}, "boxBtnStyle": {"z-index": "1", "position": "relative", "width": "30em", "text-align": "left"}, "btnStyle": {"border-radius": "0.333333em", "font-size": "1.125em", "min-width": "unset", "padding-top": "0.555556em", "padding-bottom": "0.555556em", "padding-left": "2.777778em", "padding-right": "2.777778em", "line-height": "normal"}, "column": 1, "outer": [{"style": {"width": "30em", "z-index": 1, "position": "relative"}, "title": {"value": "Spin Away from Smoking Woes, Play with Fun Fidget Spinner!"}, "subTitle": {"value": " "}, "text": {"value": "▪ Relieve tension, enhance concentration, help relax,improve work efficiency, help to quit smoking.<br><br>▪ Just pick up the fidget spinner and spin it.<br><br>▪ Order your fidget spinner now and experience the perfect balance between work and relaxation!"}, "button": {"value": "Start Ordering Now", "alt": "adult fidget spinner", "method": {"modal": "modalQuoteDialog", "quoteUrl": "/quote/enamel-fidget-spinner-quote/"}}}], "list": [{"img": {"value": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20240226/ueditor/97/adult-fidget-spinners-help-you-away-form-smoking.png", "alt": "<PERSON><PERSON><PERSON> Spinner"}}]}, {"id": "customFidgetSpinners_09", "theme": "20", "name": "Card", "column": 3, "style": {"background-color": "#fff"}, "titleStyle": {"font-size": "2.25em", "line-height": "normal"}, "subTitleStyle": {"margin-top": "1em", "margin-bottom": "1em", "font-size": "1em", "line-height": "normal"}, "cardImgStyle": {"min-height": "10.5625em"}, "cardBoxStyle": {"padding-left": "15%", "padding-right": "15%"}, "boxStyle": {"row-gap": "1.25em"}, "outer": [{"style": {"padding-left": "15%", "padding-right": "15%"}, "title": {"value": "Customized Packaging for High-quality Fidget Spinners"}, "subTitle": {"value": "We offer a variety of packaging options for you to choose from, including plastic bags, velvet bags, velvet<br>boxes, and paper boxes."}}], "list": [{"img": {"value": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20240227/ueditor/97/paper-bag-for-fidget-spinners.png", "alt": "Paper box for fidget spinner"}}, {"img": {"value": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20240226/ueditor/97/capsule-box-for-fidget-spinner.png", "alt": "Capsule box of custom fidget spinners"}}, {"img": {"value": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20240226/ueditor/97/velour-box.png", "alt": "Velour Box of custom fidget spinners"}}, {"img": {"value": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20240226/ueditor/97/velour-box-2.png", "alt": "Velour Box of fidget spinners"}}, {"img": {"value": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20240226/ueditor/97/aluminum-round-box.png", "alt": "aluminum round box of custom fidget spinner"}}, {"img": {"value": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20240226/ueditor/97/aluminum-rectangle-box.png", "alt": "aluminum rectangle box of custom fidget spinner"}}]}, {"id": "customFidgetSpinners_10", "name": "Twins", "theme": 20, "style": {"flex-direction": "column", "background-color": "#f3f4f5"}, "outer": [{"name": "Card", "column": 1, "titleStyle": {"font-size": "2.25em", "line-height": "normal"}, "subTitleStyle": {"margin-top": "1em", "margin-bottom": "1em", "font-size": "1em", "line-height": "normal"}, "cardBoxStyle": {"margin-top": "1.25em", "margin-bottom": "1.25em"}, "cardImgStyle": {"width": "61.375em", "margin": "0 auto"}, "outer": [{"title": {"value": "How to Customize Your Fidget Spinner?"}, "subTitle": {"value": "At GS-JJ, you can choose from various styles of customized fidget spinners. Our high-quality service and exquisite craftsmanship will ensure that you get a unique customized fidget spinner that stands out. Make your fidget spinner a one-of-a-kind toy that showcases your personality. Start customizing now!"}}]}, {"img": {"value": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20240226/ueditor/97/order-steps-of-custom-fidget-spinners.png", "alt": "step"}}], "list": [{"name": "Card", "column": 4, "boxStyle": {"gap": "3.125em"}, "cardStyle": {"flex-direction": "column", "gap": 0, "width": "calc(25% - 2.34375em)"}, "cardImgStyle": {"min-height": "11.25em"}, "cardTitleStyle": {"margin-top": "0.75em", "font-size": "1.25em", "text-align": "center"}, "outer": [{}]}, {"img": {"value": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20240226/ueditor/97/various-cool-fidget-spinner-designs.png", "alt": "various different fidget spinners designs"}, "title": {"value": "Choose Your Fidget Spinners style"}}, {"img": {"value": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20240226/ueditor/97/design-idea-for-fidget-spinner.png", "alt": "upload your fidget spinner design"}, "title": {"value": "Upload Your Design"}}, {"img": {"value": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20240226/ueditor/97/customized-fidget-spinner.png", "alt": "fidget spinners designs"}, "title": {"value": "Confirm Fidget Spinners Artwork"}}, {"img": {"value": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20240226/ueditor/97/custom-fidget-spinner-maker-for-24-hour-or-custom-fidget-spinners.png", "alt": "Production and delivery for personalized fidget spinners"}, "title": {"value": "Production and Delivery"}}]}, {"id": "customFidgetSpinners_11", "theme": 23, "name": "Card", "column": 3, "margin": 4, "style": {"background-color": "rgb(23, 23, 25)", "color": "rgb(229,229,229)"}, "subTitleStyle": {"margin-bottom": "2.5em"}, "boxStyle": {"margin-top": "5em", "row-gap": "4.875em"}, "cardStyle": {"flex-direction": "column", "row-gap": "1em", "padding": "1.5em", "border-radius": "0.5em", "background-color": "rgb(244,244,244)", "color": "rgb(51,51,51)", "overflow": "visible"}, "cardImgStyle": {"margin-top": "-3.5em", "width": "4em", "height": "4em"}, "cardContentStyle": {"gap": "0.625rem"}, "cardTitleStyle": {"font-size": "1.25em"}, "cardTextStyle": {"font-size": "1.125em"}, "outer": [{"title": {"value": "Why Choose GS-JJ Customized Fidget Spinners?"}, "subTitle": {"value": "GS-JJ provides personalized, high-quality hats at competitive prices, enhancing your brand visibility with excellent service and timely delivery."}}], "list": [{"img": {"value": "https://static-oss.gs-souvenir.com/web/quoteManage/20250320/Innovation_and_Variety_20250320jPhpah.png", "alt": "Innovation and Variety"}, "title": {"value": "Innovation and Variety"}, "text": {"value": "As a manufacturer of customized fidget spinners, we cater to consumers' personalized needs. Our goal is to bring more fun and enjoyment to customers."}}, {"img": {"value": "https://static-oss.gs-souvenir.com/web/quoteManage/20250320/Design_Flexibility__Customization_20250320xpCHdY.png", "alt": "Design Flexibility & Customization"}, "title": {"value": "Design Flexibility & Customization"}, "text": {"value": "In production, we have high flexibility in terms of size, color, material, and shape. We customize fidget spinners according to individual needs."}}, {"img": {"value": "https://static-oss.gs-souvenir.com/web/quoteManage/20250320/Quality_Assurance_20250320sbKDPp.png", "alt": "Quality Assurance"}, "title": {"value": "Quality Assurance"}, "text": {"value": "We use high-quality materials and advanced manufacturing techniques to ensure durability, stability, and safety."}}, {"img": {"value": "https://static-oss.gs-souvenir.com/web/quoteManage/20250320/Attentive_Follow-up_Service_202503208TAEpc.png", "alt": "Attentive Follow-up Service"}, "title": {"value": "Attentive Follow-up Service"}, "text": {"value": "We provide professional customized services. From artwork proofing and production to packaging, we closely follow the needs of our customers."}}, {"img": {"value": "https://static-oss.gs-souvenir.com/web/quoteManage/20250320/Easy_Ordering_Online_20250320CxTnks.png", "alt": "Easy Ordering Online"}, "title": {"value": "Easy Ordering Online"}, "text": {"value": "By offering a variety of options for customized fidget spinners, you can easily complete your customization in a short amount of time."}}, {"img": {"value": "https://static-oss.gs-souvenir.com/web/quoteManage/20250320/Market_Competitiveness_20250320cXM8fX.png", "alt": "Market Competitiveness"}, "title": {"value": "Market Competitiveness"}, "text": {"value": "By creating personalized fidget spinners, we have gained a competitive advantage in the market and helped establish a unique brand image."}}]}, {"id": "customFidgetSpinners_12", "theme": 20, "name": "Card", "manualPlay": true, "boxStyle": {"display": "grid", "column-gap": "10%", "grid-template-columns": "repeat(2, 1fr)"}, "cardStyle": {"width": false, "overflow": "visible"}, "cardTitleStyle": {"font-size": "2.25rem"}, "cardSubTitleStyle": {"font-size": "1.125rem"}, "cardBtnStyle": {"margin-top": "1.875rem", "min-width": "15.625rem", "height": "3.375rem", "font-size": "1.125rem", "border-radius": "0.5rem"}, "list": [{"title": {"value": "100% Customized Fidget Spinner"}, "style": {"align-self": "flex-end"}}, {"video": {"value": "https://static-oss.gs-souvenir.com/web/quoteManage/20250320/Personalized_Fidget_Spinner_20250318PdrQMp_20250320mGWieN.mp4", "poster": "https://static-oss.gs-souvenir.com/web/quoteManage/20250428/Personalized_Fidget_Spinner_20250427xbasja1_20250428ExtwG6.jpg", "alt": "Personalized Fidget Spinner"}, "style": {"grid-row-start": "span 2"}}, {"subTitle": {"value": "• Our personalized fidget spinners are fully customizable , they can be made exactly to your needs.<br><br>• Available for any style, shape, size, and color.<br><br>You can choose your fidget spinner styles with color, without color, 3D styles, print, or engraved styles. We have excellent skilled workers to help you with high-quality fidget spinner products."}, "button": {"value": "Get a Quote", "alt": "Order custom fidget spinners", "method": {"modal": "modalQuoteDialog", "quoteUrl": "/quote/enamel-fidget-spinner-quote/?type=quoteIframe"}}}]}, {"id": "customFidgetSpinners_13", "theme": 20, "name": "Card", "column": 3, "margin": 1, "hideImgListIcon": true, "style": {"background-color": "rgb(23, 23, 25)", "color": "rgb(255,255,255)"}, "cardStyle": {"padding": "1em", "flex-direction": "column", "background-color": "#F7F7F7", "color": "#333333", "border-radius": "0.5em"}, "cardImgStyle": {"min-height": "20em"}, "cardContentStyle": {"display": "grid", "align-items": "center", "grid-template-columns": "1fr auto"}, "cardImgListBoxStyle": {"order": -1, "grid-column": "span 2", "justify-content": "center"}, "cardImgsBoxStyle": {"gap": "0.75em"}, "cardImgListStyle": {"width": "4.5em", "height": "4.5em", "border-width": "1px", "border-radius": "0.25em", "background-color": "#ffffff"}, "cardTitleStyle": {"grid-column": "span 2"}, "cardSubTitleStyle": {"opacity": 0.7, "font-size": "0.875em", "grid-column": "span 2"}, "cardPriceListStyle": {"font-size": "1.125em"}, "cardBtnStyle": {"height": "2.5rem", "min-width": "unset", "border-radius": "3em", "font-size": "1.125rem"}, "outer": [{"title": {"value": "Our 100% Custom Fidget Spinners Types"}, "subTitle": {"value": "Spread brand recognition with custom fidget spinners! These cool fidget spinner are popular among people of all ages and have become exquisite gifts given to customers at many events. Don’t worry, you won’t need to perform cool fancy tricks, you can still promote in style!"}}], "list": [{"imgList": [{"value": "https://static-oss.gs-souvenir.com/web/quoteManage/20250320/Enamel_Fidget_Spinners_20250320RHt3Sj.png", "alt": "enamel fidget spinners"}, {"value": "https://static-oss.gs-souvenir.com/web/quoteManage/20250320/best_fidget_spinner_20250320KTtajS.png", "alt": "best fidget spinner"}, {"value": "https://static-oss.gs-souvenir.com/web/quoteManage/20250320/fidget_spinner_game_20250320dyQjar.png", "alt": "fidget spinner game"}, {"value": "https://static-oss.gs-souvenir.com/web/quoteManage/20250320/custom_fidget_spinner_20250320Df3nSp.png", "alt": "custom fidget spinner"}], "title": {"value": "Enamel Fidget Spinners"}, "subTitle": {"value": "Enamel Fidget Spinners blend style and smooth spins for ultimate relaxation."}, "priceList": [{"value": "As Low As "}, {"value": 9.41}, {"value": "ea"}], "button": {"value": "Get Quoted", "alt": "Custom Enamel Fidget Spinners", "method": {"modal": "modalQuoteDialog", "quoteUrl": "/quote/enamel-fidget-spinner-quote"}}}, {"imgList": [{"value": "https://static-oss.gs-souvenir.com/web/quoteManage/20250320/metal_fidget_spinner_20250320SyxTWb.png", "alt": "metal fidget spinner"}, {"value": "https://static-oss.gs-souvenir.com/web/quoteManage/20250320/fidget_spinner_metal_20250320eJrY7r.png", "alt": "fidget spinner metal"}, {"value": "https://static-oss.gs-souvenir.com/web/quoteManage/20250320/fidget_spinner_bearings_20250320hD3SYF.png", "alt": "fidget spinner bearings"}, {"value": "https://static-oss.gs-souvenir.com/web/quoteManage/20250320/cool_fidget_spinners_20250320C4wBx3.png", "alt": "cool fidget spinners"}], "title": {"value": "Metal Fidget Spinners"}, "subTitle": {"value": "Metal Fidget Spinners offer durable, precise design for a premium spinning experience."}, "priceList": [{"value": "As Low As "}, {"value": 8.72}, {"value": "ea"}], "button": {"value": "Get Quoted", "alt": "Custom Metal Fidget Spinners", "method": {"modal": "modalQuoteDialog", "quoteUrl": "/quote/die-struck-fidget-spinner-quote"}}}, {"imgList": [{"value": "https://static-oss.gs-souvenir.com/web/quoteManage/20250320/3D_fidget_spinners_20250320REFxmN.png", "alt": "3D fidget spinners"}, {"value": "https://static-oss.gs-souvenir.com/web/quoteManage/20250320/promotional_fidget_spinners_20250320MmH3EF.png", "alt": "promotional fidget spinners"}, {"value": "https://static-oss.gs-souvenir.com/web/quoteManage/20250320/customized_fidget_spinners_20250320Xja5Qt.png", "alt": "customized fidget spinners"}, {"value": "https://static-oss.gs-souvenir.com/web/quoteManage/20250320/diy_fidget_spinners_20250320CkBt4e.png", "alt": "diy fidget spinners"}], "title": {"value": "3D Fidget Spinners"}, "subTitle": {"value": "3D Fidget Spinners feature intricate, sculptural designs for a unique and dynamic spinning experience."}, "priceList": [{"value": "As Low As "}, {"value": 9.73}, {"value": "ea"}], "button": {"value": "Get Quoted", "alt": "Custom 3D Fidget Spinners", "method": {"modal": "modalQuoteDialog", "quoteUrl": "/quote/3d-fidget-spinner-quote"}}}, {"imgList": [{"value": "https://static-oss.gs-souvenir.com/web/quoteManage/20250320/glow_in_the_dark_fidget_spinners_202503203cD26m.png", "alt": "glow in the dark fidget spinners"}, {"value": "https://static-oss.gs-souvenir.com/web/quoteManage/20250320/light_up_fidget_spinner_20250320wxSmwH.png", "alt": "light up fidget spinner"}, {"value": "https://static-oss.gs-souvenir.com/web/quoteManage/20250320/unique_fidget_spinners_20250320eCkB2T.png", "alt": "unique fidget spinners"}], "title": {"value": "Glow In The Dark Fidget Spinners"}, "subTitle": {"value": "Glow In The Dark Fidget Spinners light up your spin, offering fun and relaxation even in the dark."}, "priceList": [{"value": "As Low As "}, {"value": 9.72}, {"value": "ea"}], "button": {"value": "Get Quoted", "alt": "custom enamel earrings", "method": {"modal": "modalQuoteDialog", "quoteUrl": "/quote/custom-enamel-earrings"}}}, {"imgList": [{"value": "https://static-oss.gs-souvenir.com/web/quoteManage/20250320/diamond_fidget_spinners_20250320AxajsZ.png", "alt": "diamond fidget spinners"}, {"value": "https://static-oss.gs-souvenir.com/web/quoteManage/20250320/fidget_spinners_20250320GDR3Zr.png", "alt": "fidget spinners"}], "title": {"value": "Diamond Fidget Spinners"}, "subTitle": {"value": "Diamond Fidget Spinners sparkle with elegance, adding a touch of glamour to your spinning experience."}, "priceList": [{"value": "As Low As "}, {"value": 9.02}, {"value": "ea"}], "button": {"value": "Get Quoted", "alt": "Diamond Fidget Spinners", "method": {"modal": "modalQuoteDialog", "quoteUrl": "/quote/rhinestone-fidget-spinner-quote"}}}]}, {"id": "customFidgetSpinners_14", "theme": "20", "name": "Summary", "style": {"color": "#fff"}, "titleStyle": {"text-align": "left"}, "imgStyle": {"position": "absolute", "left": 0, "top": 0, "width": "100%", "height": "100%", "object-fit": "cover", "border-radius": 0}, "textStyle": {"font-size": "1.125em", "width": "100%"}, "contentStyle": {"padding-left": "50%"}, "btnBoxStyle": {"width": "100%", "text-align": "left"}, "outer": [{"title": {"value": "Boost Focus and <PERSON><PERSON><PERSON> with Customizable Fidget Spinners from GSJJ"}, "text": {"value": "Fidget Spinners are a potent stress-relief tool with the following features:<br><br>▪ Easy to Use: design fidget spinner adopts a simple design that is easy to operate. With just one turn, you can experience the soothing sensation it provides.<br><br>▪ No Batteries Required: Unlike other stress-relief tools, custom-made fidget spinner doesn't rely on battery power. This makes it more convenient and worry-free to use. Say goodbye to dead batteries and the hassle of replacing them, allowing you to enjoy relaxation and stress relief anytime, anywhere."}, "button": {"value": "Get a Quote", "alt": "how to customize a fidget spinner", "url": "#FidgetSpinnersTop"}, "img": {"value": "https://static-oss.gs-souvenir.com/web/quoteManage/20250320/Custom_Fidget_Spinners_20250320wd7HDW.jpg", "alt": "Custom Fidget Spinners"}}]}]