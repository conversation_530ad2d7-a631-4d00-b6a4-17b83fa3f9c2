<template>
	<v-card id="cutImage1" class="cutImage1 rounded-0" flat>
		<v-card class="rounded-0 py-1" flat color="#cee3fb">
			<div class="d-flex justify-center align-center relative" v-if="!(logo || manageMobile) && !isVideo">
				<div class="d-flex justify-center align-center">
					<v-btn-toggle group dense borderless tile v-model="toggle_exclusive" background-color="#cee3fb"
						mandatory @change="changeShape">
						<v-btn v-for="(item, index) in btn_group" :key="index" :value="item" height="20px" width="20px"
							min-width="20px" depressed class="mx-2" plain text>
							<v-icon :style="{ transform: item.rotate }">{{ item.icon }}</v-icon>
						</v-btn>
					</v-btn-toggle>
				</div>
				<v-btn height="20px" width="20px" min-width="20px" depressed class="rotateBtn" plain text
					@click="rotateFun"><v-icon>mdi-format-rotate-90</v-icon></v-btn>
			</div>
			<v-card-text class="text-center py-1">
				<slot>{{ isVideo ? 'video' : toggle_exclusive.title }}</slot>
			</v-card-text>
		</v-card>
		<div class="imgBox" :style='{ width: cutWidth }'>
			<div class="board" id="board" :style="{
				'mask-image':
					toggle_exclusive.title == 'Fit Image'
						? 'none'
						: 'url(' + toggle_exclusive.url + ')',
			}">
				<img id="moveImg" ref="moveImg" v-show="!isVideo" :src="resizeSrc" :style="{
					'object-fit': 'contain',
					'width': 'auto',
					'height': 'auto',
					'max-height': zoom + '%',
					'max-width': zoom + '%',
				}" />
				<video id="myVideo" :key="msrc" width="100%" controls v-show="isVideo">
					<source :src="msrc" type="video/mp4">
					您的浏览器不支持视频标签。
				</video>
			</div>
		</div>

		<v-card class="rounded-0 py-1" flat color="#cee3fb">
			<v-card-text>
				<v-slider :disabled="toggle_exclusive.title == 'Fit Image' ? true : false" v-model="zoom"
					append-icon="mdi-magnify-plus-outline" prepend-icon="mdi-magnify-minus-outline" @click:append="zoomIn"
					@click:prepend="zoomOut" hide-details :max="max" :min="min" step="10" @change="sliderChange"></v-slider>
			</v-card-text>
			<div class="d-flex justify-space-between pb-2">
				<div class="mx-4 w-100">
					<v-btn color="#ffffff" small @click.stop="getMsrc" block>
						<!-- <v-icon left> mdi-move-resize </v-icon> -->
						Change Image/Video
					</v-btn>
				</div>
			</div>
		</v-card>
	</v-card>
</template>

<script>
export default {
	name: "cutImage1",
	model: {
		prop: "msrc",
		event: "change",
	},

	props: {
		msrc: {
			type: String,
			default: '',
		},
		logo: {
			type: Boolean,
			default: false,
		},
		cutWidth: {
			type: String,
			default: '368px',
		},
	},
	data: () => ({
		isVideo: false,
		//第一次渲染左间距
		firstLeft: 0,
		firstTop: 0,

		moveX: 0,
		moveY: 0,
		timed: null,

		//图片参数
		naturalWidth: null,
		naturalHeight: null,
		showWidth: null,
		showHeight: null,

		//图片坐标
		markX: 0,
		markY: 0,
		// boxX: 0,
		// boxY: 0,

		tempCutObj: {},

		//裁剪参数
		coordinate: {
			x: 0,
			y: 0,
			w: 0,
			h: 0,
		},
		rotate: 0,
		resizeSrc: "",
		imgWidth: null,
		imgHeight: null,
		disabledSlider: false,
		media: 0,
		alarm: 0,
		zoom: 100,
		max: 200,
		min: 100,
		toggle_exclusive: {},
		height: 250,
		width: 368,
		btn_group: [
			{
				icon: "mdi-rectangle",
				rotate: "rotate(0)",
				title: "Landscape",
				url: require("@/assets/images/cutImage/portrait.png"),
				h: 164,
				w: 200,
				rectangleL: 84,
				rectangleT: 43,
			},
			{
				icon: "mdi-square",
				rotate: "rotate(0)",
				title: "Square",
				url: require("@/assets/images/cutImage/square.png"),
				h: 250,
				w: 250,
				rectangleL: 56,
				rectangleT: 0,
			},
			{
				icon: "mdi-rectangle",
				rotate: "rotate(90deg)",
				title: "Portrait",
				url: require("@/assets/images/cutImage/landscape.png"),
				h: 250,
				w: 200,
				rectangleL: 84,
				rectangleT: 0,
			},
			{
				icon: "mdi-rectangle",
				rotate: "rotate(0)",
				title: "Panoramic",
				url: require("@/assets/images/cutImage/Panoramic.png"),
				h: 200,
				w: 250,
				rectangleL: 59,
				rectangleT: 25,
			},
			{
				icon: "mdi-overscan",
				rotate: "rotate(0)",
				title: "Fit Image",
			},
		],
	}),
	methods: {
		getImageSize (url) {
			return new Promise((resolve, reject) => {
				if (this.isVideo) {
					resolve({
						width: this.cutWidth,
						height: '250px',
					});
				} else {
					let image = new Image();
					image.src = url;
					image.onload = function () {
						resolve({
							width: image.width,
							height: image.height,
						});
					};
					image.onerror = function () {
						reject(new Error("error"));
					};
				}
			});
		},
		//防抖
		debounce (fn) {
			if (typeof fn != "function") {
				return;
			}
			let that = this;
			return function () {
				if (that.timed) {
					clearTimeout(that.timed);
				}
				that.timed = setTimeout(() => {
					fn();
				}, 500);
			};
		},
		clean () {
			this.resizeSrc = "";
		},
		getMsrc () {
			this.$emit("getMsrc");
		},
		comeFromGF () {
			if (this.msrc?.split("?")[1]) {
				this.splitSrc(this.msrc).then((res) => {
					let moveImg = document.querySelector("#moveImg");
					this.tempCutObj = res;
					this.zoom = this.tempCutObj.zoom;
					this.rotate = this.tempCutObj.rotate;
					this.firstLeft = this.tempCutObj.firstLeft;
					this.firstTop = this.tempCutObj.firstTop;
					let temp = this.btn_group.find((x) => {
						return x.title == this.tempCutObj.cutType;
					});
					this.toggle_exclusive = temp
						? temp
						: this.btn_group[this.btn_group.length - 1];
					this.resetImg(
						moveImg,
						this.tempCutObj.left + "px",
						this.tempCutObj.top + "px"
					);
				});
				this.factory("comeFromGF");
			} else {
				this.resetParameters();
				this.factory();
			}
		},
		regFun (str) {
			// var my_reg = /(?<=浙江\=)\d+/;
			// var my_reg2 = new RegExp("(?<=广东\\=)\\d+");
			var sp1 = "(?<=";
			var sp2 = str; //sp2用于接收参数。
			var sp3 = "\\=)\\d+";
			var s = sp1 + sp2 + sp3; //s由 sp1 sp2 sp3 拼接而成
			var my_reg3 = new RegExp(s); //s传入正则构造方法生成正则对象 my_reg3

			// alert( my_str.match(my_reg3) );
			return my_str.match(my_reg3);
		},
		rotateFun () {
			if (this.rotate + 90 == 360) this.rotate = 0;
			else this.rotate += 90;
			this.factory();
		},

		splitSrc (src) {
			return new Promise((resolve, reject) => {
				let splitObj = [
					{ sta: "p_", end: "/" },
					{ sta: "rotate,", end: "/" },
					{ sta: "x_", end: "," },
					{ sta: "y_", end: "," },
					{ sta: "w_", end: "," },
					{ sta: "h_", end: "," },
					{ sta: "left_", end: "," },
					{ sta: "top_", end: "," },
					{ sta: "firstLeft_", end: "," },
					{ sta: "firstTop_", end: "," },
					{ sta: "cutType_", end: "," },
					// { sta: "r_", end: "/" },
				],
					regObj = {},
					regex2 = new RegExp("(?<=cutType_).+", "g");
				splitObj.forEach((item) => {
					var regex = new RegExp(
						"(?<=" + item.sta + ").*?(?=" + item.end + ")",
						"g"
					);
					if (src.match(regex)) {
						switch (item.sta) {
							case "p_":
								regObj.zoom = src.match(regex)[0];
								break;
							case "rotate,":
								regObj.rotate = src.match(regex)[0];
								break;
							case "x_":
								regObj.x = src.match(regex)[0];
								break;
							case "y_":
								regObj.y = src.match(regex)[0];
								break;
							case "w_":
								regObj.w = src.match(regex)[0];
								break;
							case "h_":
								regObj.h = src.match(regex)[0];
								break;
							case "left_":
								regObj.left = src.match(regex)[0];
								break;
							case "top_":
								regObj.top = src.match(regex)[0];
								break;
							case "firstLeft_":
								regObj.firstLeft = src.match(regex)[0];
								break;
							case "firstTop_":
								regObj.firstTop = src.match(regex)[0];
								break;
							// case "r_":
							//   regObj.toggle_exclusive = this.btn_group[4];
							//   break;
						}
					}
				});
				regObj.cutType = src.match(regex2) ? src.match(regex2)[0] : null;

				// var regex = /(?<=p_).*?(?=\/)/g;
				resolve(regObj);
			});
		},

		resetImg (target, x, y) {
			target.style.left = x;
			target.style.top = y;
		},

		factory (type) {
			return new Promise(async (resolve, reject) => {
				let originSrc = this.msrc?.split("?")[0];
				if(this.isVideo){
					this.resizeSrc = originSrc
				}else{
					this.resizeSrc =
					originSrc +
					"?x-oss-process=image/resize,p_" +
					this.zoom +
					("/rotate," + this.rotate);
				}
				Promise.all([
					this.getImageSize(originSrc),
					this.getImageSize(this.resizeSrc),
				]).then((res) => {
					if (this.isVideo) {
						this.$emit(
							"change",
							originSrc
						);
						return
					}
					let moveImg = document.querySelector("#moveImg");

					//图片真实宽高
					this.naturalWidth = ~~((res[0].width * this.zoom) / 100).toFixed(0);
					this.naturalHeight = ~~((res[0].height * this.zoom) / 100).toFixed(0);
					//图片展示宽高
					this.showWidth = ~~moveImg.width;
					this.showHeight = ~~moveImg.height;
					if (this.toggle_exclusive.title == "Fit Image") {
						if (this.logo) {
							moveImg.style.left = "50%";
							moveImg.style.top = "50%";
							moveImg.style.transform = "translate(-50%, -50%)";
						} else {
							this.firstLeft = (368 - this.showWidth) / 2;
							this.firstTop = (250 - this.showHeight) / 2;
							this.resetImg(
								moveImg,
								this.firstLeft + "px",
								this.firstTop + "px"
							);
						}
					}

					let scw = this.naturalWidth / this.showWidth,
						sch = this.naturalHeight / this.showHeight,
						//图片距离父级的距离
						imgOffsetTop = this.$refs.moveImg.offsetTop,
						imgOffsetLeft = this.$refs.moveImg.offsetLeft;

					//裁剪起点横坐标（默认左上角为原点
					if (!this.coordinate.x) {
						this.coordinate.x = ~~Math.abs(
							this.toggle_exclusive.rectangleL - imgOffsetLeft
						);
					}

					if (!this.coordinate.y) {
						this.coordinate.y = ~~Math.abs(
							this.toggle_exclusive.rectangleT - imgOffsetTop
						); //裁剪起点纵坐标
					}

					this.coordinate.w = this.coordinate.w
						? this.coordinate.w
						: this.toggle_exclusive.w; //裁剪宽度
					this.coordinate.h = this.coordinate.h
						? this.coordinate.h
						: this.toggle_exclusive.h; //裁剪高度

					if (this.toggle_exclusive.title == "Fit Image") {
						if (this.rotate) {
							this.$emit(
								"change",
								originSrc + "?x-oss-process=image" + ("/rotate," + this.rotate)
							);
						} else {
							this.$emit("change", originSrc);
						}
					} else {
						if (type == "comeFromGF") {
							this.$emit(
								"change",
								originSrc +
								"?x-oss-process=image" +
								("/resize,p_" + this.zoom) +
								("/rotate," + this.rotate) +
								"/crop,x_" +
								this.tempCutObj.x +
								",y_" +
								this.tempCutObj.y +
								",w_" +
								this.tempCutObj.w +
								",h_" +
								this.tempCutObj.h +
								",left_" +
								this.tempCutObj.left +
								",top_" +
								this.tempCutObj.top +
								",firstLeft_" +
								this.firstLeft +
								",firstTop_" +
								this.firstTop +
								",cutType_" +
								this.toggle_exclusive.title
							);
						} else {
							this.$emit(
								"change",
								originSrc +
								"?x-oss-process=image" +
								("/resize,p_" + this.zoom) +
								("/rotate," + this.rotate) +
								"/crop,x_" +
								(this.coordinate.x * scw).toFixed(0) +
								",y_" +
								(this.coordinate.y * sch).toFixed(0) +
								",w_" +
								(this.coordinate.w * scw).toFixed(0) +
								",h_" +
								(this.coordinate.h * sch).toFixed(0) +
								",left_" +
								moveImg.offsetLeft +
								",top_" +
								moveImg.offsetTop +
								",firstLeft_" +
								this.firstLeft +
								",firstTop_" +
								this.firstTop +
								",cutType_" +
								this.toggle_exclusive.title
							);
						}
					}
					if (this.logo) {
						this.$emit("updateLogo");
					}
					this.mouseDown();
					this.coordinate = {
						x: 0,
						y: 0,
						w: 0,
						h: 0,
					};
					resolve();
				});
			});
		},
		zoomOut () {
			this.zoom = this.zoom - 10 || 100;
			this.dontMoveFun();
		},
		zoomIn () {
			this.zoom = this.zoom + 10 || 200;
			this.dontMoveFun();
		},
		sliderChange () {
			this.dontMoveFun();
		},
		dontMoveFun () {
			let moveImg = document.querySelector("#moveImg");
			this.resetImg(moveImg, this.firstLeft + "px", this.firstTop + "px");
			this.debounce(this.factory)();
		},
		resetParameters () {
			this.markX = 0;
			this.markY = 0;
			this.boxX = 0;
			this.boxY = 0;
			this.rotate = 0;
			this.toggle_exclusive = this.btn_group[this.btn_group.length - 1];
			this.zoom = 100;
		},
		//形状
		changeShape (val) {
			let moveImg = document.querySelector("#moveImg");

			if (val.title == "Fit Image") {
				this.zoom = 100;
			} else {
				moveImg.style.top = this.toggle_exclusive.rectangleT + "px";
				moveImg.style.left = this.toggle_exclusive.rectangleL + "px";
			}
			// this.resetImg(moveImg, this.firstLeft + "px", this.firstTop + "px");
			this.factory();
		},
		mouseDown () {
			let moveImg = document.querySelector("#moveImg"),
				parentBox = document.querySelector("#board"),
				cutImage = document.querySelector("#cutImage1"),
				parentH = parentBox.clientHeight,
				parentW = parentBox.clientWidth,
				toX,
				toY,
				maxX,
				maxY, //最大移动距离
				isDrop = true;

			moveImg.onmousedown = (e) => {
				toX = e.clientX - moveImg.offsetLeft; // e.clientX鼠标相对于浏览器左上角x轴的坐标-button上层控件的位置
				toY = e.clientY - moveImg.offsetTop;
				isDrop = false;
			};
			cutImage.onmousemove = (e) => {
				if (e.target.tagName == "I") return;
				if (!isDrop) {
					e.preventDefault();
					this.imgWidth = moveImg.width;
					this.imgHeight = moveImg.height;
					this.moveX = e.clientX - toX; //得到距离左边移动距离
					this.moveY = e.clientY - toY; //得到距离上边移动距离
					//可移动最大距离
					// maxX = parentW - moveImg.offsetWidth;
					// maxY = parentH - moveImg.offsetHeight;

					//移动的有效距离计算
					//height width 裁剪框宽高
					//toggle_exclusive.w toggle_exclusive.h 裁剪框背景图宽高
					//imgWidth imgHeight 图片宽高

					this.moveX = Math.min(
						(this.width - this.toggle_exclusive.w) / 2,
						Math.max(
							-(
								(this.width - this.toggle_exclusive.w) / 2 +
								(this.imgWidth - this.width)
							),
							this.moveX
						)
					);
					this.moveY = Math.min(
						(this.height - this.toggle_exclusive.h) / 2,
						Math.max(
							-(
								(this.height - this.toggle_exclusive.h) / 2 +
								(this.imgHeight - this.height)
							),
							this.moveY
						)
					);
					moveImg.style.left = this.moveX + "px";
					moveImg.style.top = this.moveY + "px";
					//(图片高度-圆形高度)/2
				} else {
					return;
				}
			};
			cutImage.onmouseup = (e) => {
				if (e.target.tagName == "I") return;
				e.preventDefault();
				isDrop = true;

				this.factory();
			};
		},
	},
	computed: {
		manageMobile () {
			return this.$store.getters.getManageMobile;
		},
	},
	watch: {
		msrc: {
			handler (newValue, oldValue) {
				if(newValue&&newValue.toLowerCase().includes('.mp4')){
					this.isVideo = true
				}else{
					this.isVideo = false
				}
				if (this.cutWidth == '472px') this.resizeSrc = newValue.split("?")[0];
			},
			immediate: true,
		},
	},
	mounted () {
		this.toggle_exclusive = this.btn_group[this.btn_group.length - 1];
		this.mouseDown();
	},
};
</script>
<style scoped lang="scss">
.cutImag1 {

	// position: relative;
	.imgBox {
		// width: 368px;
		height: 250px;
		background: #eee;
		background-image: linear-gradient(45deg,
				#bbb 25%,
				transparent 0,
				transparent 75%,
				#bbb 0),
			linear-gradient(45deg, #bbb 25%, transparent 0, transparent 75%, #bbb 0);
		background-size: 15px 15px;
		background-position: 0 0, 8px 8px;
		overflow: hidden;

		.board {
			// mask-image: url("@/assets/images/cutImage/circle2.png");
			mask-repeat: no-repeat;
			position: relative;
			mask-position: center center;
			// mask-size: contain;
			height: 100%;

			#moveImg {
				position: absolute;
				left: 0;
				top: 0;
				cursor: move;
				object-fit: cover;
			}
		}
	}

	.rotateBtn {
		position: absolute;
		right: 10px;
		top: 7px;
	}
}
</style>
