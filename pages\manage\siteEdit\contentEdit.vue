<template>
	<v-sheet class="siteEdit" :theme="$store.state.proTheme">
		<v-window v-model="step">
			<v-window-item :value="1">
				<v-card class="mx-auto" flat color="#f5f5f5">
					<v-list-item three-line>
						<v-list-item-content>
							<h3 class="text-h5 font-weight-bold">Page Management</h3>
						</v-list-item-content>
						<v-btn class="mr-5" color="primary" @click="addPage">new page</v-btn>
						<span class="d-flex align-center mr-5">
							Collection:<v-select style="background-color: #ffffff" class="ml-3" dense hide-details
								:items="collectionData" item-text="cateName" item-value="cateId" v-model="theCateValue"
								outlined @change="filterFun"></v-select></span>
						<span class="d-flex align-center mr-5">
							Filter by:<v-select class="ml-3" style="background-color: #ffffff" dense hide-details
								:items="items" item-text="name" item-value="value" v-model="filterByValue" outlined
								@change="filterFun"></v-select></span>
						<span class="d-flex align-center">
							<v-text-field dense hide-details style="background-color: #ffffff" label="Search..." outlined
								prepend-inner-icon="mdi-magnify" v-model="keyWord" @input="keyWordFun"></v-text-field>
						</span>
					</v-list-item>
					<v-list-item>
						<div class="country">
							<span>Current national language：</span>
							<v-autocomplete v-model="nationalLanguageTab" :items="countryList" solo flat
								label="Please select" outlined class="autocomplete" @change="nationalLanguageTabClick">
								<template v-slot:selection="data">
									<div v-bind="data.attrs" :input-value="data.selected">
										<v-avatar left tile v-if="data.item.img">
											<v-img :src="data.item.img"></v-img>
										</v-avatar>
										{{ data.item.country }}
									</div>
								</template>
								<template v-slot:item="data">
									<v-list-item-avatar tile>
										<img :src="data.item.img">
									</v-list-item-avatar>
									<v-list-item-content>
										<v-list-item-title v-html="data.item.country"></v-list-item-title>
										<v-list-item-subtitle v-html="data.item.language"
											v-if="data.item.country == 'Canada'"></v-list-item-subtitle>
									</v-list-item-content>
								</template>
							</v-autocomplete>
						</div>
						<div class="country country1" style="margin-left: 30px;">
							<span>LOGO：</span>
							<v-autocomplete v-model="logoSelectValue" :items="logoLanguageList" solo flat
								label="Please select" outlined class="autocomplete">
								<template v-slot:selection="data">
									<div v-bind="data.attrs" :input-value="data.selected">
										<v-avatar left tile v-if="data.item.img">
											<v-img :src="data.item.img"></v-img>
										</v-avatar>
										{{ data.item.country }}
									</div>
								</template>
								<template v-slot:item="data">
									<v-list-item-avatar tile>
										<img :src="data.item.img">
									</v-list-item-avatar>
									<v-list-item-content>
										<v-list-item-title v-html="data.item.country"></v-list-item-title>
										<v-list-item-subtitle v-html="data.item.language"
											v-if="data.item.country == 'Canada'"></v-list-item-subtitle>
									</v-list-item-content>
								</template>
							</v-autocomplete>
							<v-img v-if="logoPreviewValue" :src="logoPreviewValue" class="grey darken-4"
								style="margin-left: 10px;" max-width="300px"></v-img>
							<v-btn color="primary" :height="40" style="margin-left: 10px;"
								@click="showLogoDialog">upload</v-btn>
						</div>
						<div class="country" style="background:transparent;">
							<v-btn @click="structureClick" color="primary" :height="40" style="margin-left: 10px;">structure</v-btn>
						</div>
					</v-list-item>
				</v-card>
				<v-data-table :headers="headers" :items="desserts" single-expand @item-expanded="showExpandIndex = -1"
					:items-per-page="-1" show-expand class="elevation-0" hide-default-footer>
					<template v-slot:item.actions="{ item }">
						<v-tooltip top>
							<template v-slot:activator="{ on, attrs }">
								<v-icon v-bind="attrs" v-on="on" color="primary" title="View the Page" class="mr-2"
									@click.stop="showPage(item)">
									mdi-text-search
								</v-icon>
							</template>
							<span>View the Page</span>
						</v-tooltip>
						<v-tooltip top>
							<template v-slot:activator="{ on, attrs }">
								<v-icon v-bind="attrs" v-on="on" color="primary" class="mr-2" @click.stop="editPage(item)"
									@click.right.prevent="handlerRightClick(item)">
									mdi-square-edit-outline
								</v-icon>
							</template>
							<span>Edit the Page</span>
						</v-tooltip>
						<v-tooltip top>
							<template v-slot:activator="{ on, attrs }">
								<v-icon v-bind="attrs" v-on="on" color="primary" class="mr-2" @click="editClick(item)">
									mdi-playlist-edit
								</v-icon>
							</template>
							<span>Edit popover</span>
						</v-tooltip>
						<v-tooltip top>
							<template v-slot:activator="{ on, attrs }">
								<v-icon v-bind="attrs" v-on="on" :color="item.isPublish ? 'primary' : ''"
									@click="isPublishFun(item)" v-if="item.pageType == 1 || item.pageType == 2">
									{{ item.isPublish ? "mdi-eye" : "mdi-eye-off" }}
								</v-icon>
							</template>
							<span>
								{{
									item.isPublish ? "Remove from Your Store" : "Display in Your Store"
								}}</span>
						</v-tooltip>
						<v-tooltip top>
							<template v-slot:activator="{ on, attrs }">
								<v-icon v-bind="attrs" v-on="on" color="primary" class="mr-2" @click="delPageClick(item)"
									v-if="item.pageType == 2">
									mdi-trash-can-outline
								</v-icon>
							</template>
							<span>Deleting pages</span>
						</v-tooltip>
						<v-tooltip top>
							<template v-slot:activator="{ on, attrs }">
								<v-icon v-bind="attrs" v-on="on" color="primary" title="view page img" class="mr-2"
									@click="showImgPage(item)">
									mdi-image-area
								</v-icon>
							</template>
							<span>view page img</span>
						</v-tooltip>
					</template>
					<template v-slot:item.data-table-expand="{ item, expand, isExpanded }">
						<v-btn icon @click="expand(!isExpanded)" v-show="item.cateType === 1">
							<v-icon>mdi-chevron-down</v-icon>
						</v-btn>
					</template>
					<template v-slot:expanded-item="{ headers, item }">
						<td :colspan="headers.length">
							<div class="expand-item" v-for="(expand, index) in expandList">
								<div class="expand-title">
									<v-btn icon @click="toggleExpandIndex(index, expand, item)">
										<v-icon>mdi-chevron-down</v-icon>
									</v-btn>
									{{ expand.name }}
								</div>
								<div class="expand-child-list" v-show="showExpandIndex === index">
									<v-card>
										<v-card-title>
											{{ expand.name }}
											<v-spacer></v-spacer>
											<v-text-field v-model="keyword2" append-icon="mdi-magnify" label="Search"
												single-line hide-details></v-text-field>
										</v-card-title>
										<v-data-table :headers="headers2" :items="desserts2" :search="keyword2"
											:items-per-page="-1" class="elevation-0" hide-default-footer>
											<template v-slot:item.actions="{ item }">
												<v-tooltip top>
													<template v-slot:activator="{ on, attrs }">
														<v-icon v-bind="attrs" v-on="on" color="primary"
															title="View the Page" class="mr-2" @click.stop="showPage(item)">
															mdi-text-search
														</v-icon>
													</template>
													<span>View the Page</span>
												</v-tooltip>
												<v-tooltip top>
													<template v-slot:activator="{ on, attrs }">
														<v-icon v-bind="attrs" v-on="on" color="primary" class="mr-2"
															@click.stop="editPage(item)"
															@click.right.prevent="handlerRightClick(item)">
															mdi-square-edit-outline
														</v-icon>
													</template>
													<span>Edit the Page</span>
												</v-tooltip>
												<v-tooltip top>
													<template v-slot:activator="{ on, attrs }">
														<v-icon v-bind="attrs" v-on="on" color="primary" class="mr-2"
															@click="editClick(item)">
															mdi-playlist-edit
														</v-icon>
													</template>
													<span>Edit popover</span>
												</v-tooltip>
												<v-tooltip top>
													<template v-slot:activator="{ on, attrs }">
														<v-icon v-bind="attrs" v-on="on"
															:color="item.isPublish ? 'primary' : ''"
															@click="isPublishFun(item)"
															v-if="item.pageType == 1 || item.pageType == 2">
															{{ item.isPublish ? "mdi-eye" : "mdi-eye-off" }}
														</v-icon>
													</template>
													<span>
														{{
															item.isPublish ? "Remove from Your Store" : "Display in Your Store"
														}}</span>
												</v-tooltip>
												<v-tooltip top>
													<template v-slot:activator="{ on, attrs }">
														<v-icon v-bind="attrs" v-on="on" color="primary" class="mr-2"
															@click="delPageClick(item)" v-if="item.pageType == 2">
															mdi-trash-can-outline
														</v-icon>
													</template>
													<span>Deleting pages</span>
												</v-tooltip>
												<v-tooltip top>
												<template v-slot:activator="{ on, attrs }">
													<v-icon v-bind="attrs" v-on="on" color="primary" title="view page img" class="mr-2"
														@click="showImgPage(item)">
														mdi-image-area
													</v-icon>
												</template>
												<span>view page img</span>
											</v-tooltip>
											</template>
										</v-data-table>
									</v-card>
								</div>
							</div>
						</td>
					</template>
				</v-data-table>
				<div class="text-right mt-2">
					<pagination :length="pageData.pages" @changePages="changePages"></pagination>
				</div>
			</v-window-item>
			<v-window-item :value="2">
				<v-card class="mx-auto" flat color="#f5f5f5">
					<v-list-item three-line>
						<v-list-item-content>
							<v-row class="flex">
								<v-col flex style="align-items: center">
									<b class="icon-Undo mr-5" style="cursor: pointer" @click="step = 1"></b>
									<h3 class="text-h5 font-weight-bold">Page Management</h3>
								</v-col>
								<v-col style="text-align: right">
									<v-btn color="success" @click="editPage(target)"> Edit Site</v-btn>
								</v-col>
							</v-row>
						</v-list-item-content>
					</v-list-item>
				</v-card>
				<v-card flat color="#f5f5f5">
					<div class="viewBox">
						<div class="left custom-scrollbar">
							<div class="header">
								<div>
									<div class="ic">
										<v-icon class="mx-5" color="#D6D6D6" large>mdi-dots-horizontal
										</v-icon>
									</div>
									<div v-if="target" class="urlDiv">
										<v-icon class="mr-3">mdi-lock</v-icon>
										{{ currentSite.url + (target.routingName == '/' ? '' : target.routingName) }}
									</div>
								</div>
							</div>
							<component v-for="(item, index) in pageRowDraftList" :is="'modal' + item.sampleData.name"
								:data="item.sampleData" :key="item.sampleData.name + index"></component>
						</div>
						<div></div>
					</div>
				</v-card>
			</v-window-item>
		</v-window>
		<input type="file" id="uploads" ref="uploads" style="position: absolute; clip: rect(0 0 0 0)"
			@change="getFile($event)" accept="image/png, image/jpeg, image/jpg" />
		<!-- <v-dialog
			v-model="dialog"
			attach=".siteEdit"
			hide-overlay
			transition="dialog-bottom-transition"
		>
			<v-toolbar dark color="primary">
			<v-btn icon dark @click="dialog = false">
				<v-icon>mdi-close</v-icon>
			</v-btn>
			<v-toolbar-title>Settings</v-toolbar-title>
			</v-toolbar>
			<v-card>
			<component
				v-for="(item, index) in pageRowDraftList"
				:is="'modal' + item.sampleData.name"
				:data="item.sampleData"
				:key="item.sampleData.name + index"
			></component>
			</v-card>
		</v-dialog> -->

		<v-dialog v-model="editDialog" scrollable max-width="54.0833vw">
			<v-card class="addCard">
				<v-form v-model="valid" class="addFrom">
					<div class="addFrom_div">
						<div class="country">
							<span>Current national language：</span>
							<v-autocomplete v-model="editForm.nationalLanguage" :items="countryList" solo flat
								label="Please select" outlined class="autocomplete" @change="nationalLanguageClick">
								<template v-slot:selection="data">
									<div v-bind="data.attrs" :input-value="data.selected">
										<v-avatar left tile>
											<v-img :src="data.item.img"></v-img>
										</v-avatar>
										{{ data.item.country }}
									</div>
								</template>
								<template v-slot:item="data">
									<v-list-item-avatar tile>
										<img :src="data.item.img">
									</v-list-item-avatar>
									<v-list-item-content>
										<v-list-item-title v-html="data.item.country"></v-list-item-title>
										<v-list-item-subtitle v-html="data.item.language"
											v-if="data.item.country == 'Canada'"></v-list-item-subtitle>
									</v-list-item-content>
								</template>
							</v-autocomplete>
						</div>
						<div @click="delClick" class="del">
							<v-icon>mdi-close</v-icon>
						</div>
					</div>
					<ul>
						<li>
							<span style="color:red">*</span><span>Page Name:</span>
							<v-text-field v-model="editForm.pageName" outlined clearable :rules="nameRules"></v-text-field>
						</li>
						<li>
							<span>Crowd:</span>
							<v-autocomplete v-model="editForm.crowdId" :items="crowdItems" item-text="name" item-value="id"
								chips small-chips solo flat label="Please select" outlined>
							</v-autocomplete>
						</li>
						<li>
							<span>Title:</span>
							<v-text-field v-model="editForm.title" outlined clearable></v-text-field>
						</li>
						<li>
							<span>Keywords:</span>
							<v-text-field v-model="editForm.keywords" outlined clearable></v-text-field>
						</li>
						<li>
							<span>Route Name:</span>
							<!--							//去首尾空格  转小写字符  去特殊字符 多个横杠合并成一个横杠 多个斜杠合并成一个斜杠-->
							<v-text-field v-model="editForm.routingName" :value="editForm.routingName" outlined clearable
								:disabled="editForm.pageType === 0"
								oninput="value=value.trim().toLowerCase().replace(/[^a-z0-9-/]/g, '').replace(/\-+/g, '-').replace(/\/+/g, '/')"></v-text-field>
						</li>
						<li>
							<span>Route Name Lang:</span>
							<!--							//去首尾空格  转小写字符  去特殊字符 多个横杠合并成一个横杠 多个斜杠合并成一个斜杠-->
							<v-text-field v-model="editForm.routingNameLang" :value="editForm.routingNameLang" outlined
								clearable
								oninput="value=value.trim().toLowerCase().replace(/[^a-z0-9-/]/g, '').replace(/\-+/g, '-').replace(/\/+/g, '/')"></v-text-field>
						</li>
						<li class="gg">
							<span>Description:</span>
							<v-textarea v-model="editForm.description" outlined></v-textarea>
						</li>
						<li class="gg">
							<span>Structuring:</span>
							<div>
								<div v-for="(item, index) in structuringList" :key="index" style="display: flex;">
									<v-textarea outlined v-model="item.value"></v-textarea>
									<v-icon style="height:10%;align-items: baseline;"
										@click="delStructuring(index)">mdi-trash-can-outline</v-icon>
								</div>
								<div style="text-align: center;">
									<v-btn @click="addStructuring" style="width: 50%;margin-bottom: 30px;" color="primary">+
										New structure</v-btn>
								</div>
							</div>
						</li>
						<li class="addBtn gg">
							<v-checkbox label="Need to synchronize translation ?" :false-value="0" :true-value="1"
								class="mt-0 pt-0 mr-1" hide-details v-model="editForm.isTranslateEmpty"></v-checkbox>
							<v-btn elevation="2" @click="delClick">cancel</v-btn>
							<v-btn elevation="2" color="primary" @click="edit">confirm</v-btn>
						</li>
					</ul>
				</v-form>
			</v-card>
		</v-dialog>
		<v-dialog v-model="imgDialog" scrollable max-width="54.0833vw">
			<v-card class="addCard">
				<div style="font-size: 0;margin-bottom: 20px">
					<div style="display: inline-block;width: 15%;vertical-align: middle">
						<v-btn class="text-none text-subtitle-1" color="#5865f2" size="small" variant="flat"
							@click="addDataEdit('add')">
							<span style="color: #fff">add page img</span>
						</v-btn>
					</div>
					<div style="display: inline-block;width: 80%;margin-left: 5%;vertical-align: middle">
						<div class="country" style="margin-bottom: 0">
							<span style="font-size: 12px">Current national language：</span>
							<v-autocomplete v-model="imgLanguageTab" :items="countryList" solo flat label="Please select"
								outlined class="autocomplete" @change="imgLanguageTabClick">
								<template v-slot:selection="data">
									<div v-bind="data.attrs" :input-value="data.selected">
										<v-avatar left tile>
											<v-img :src="data.item.img"></v-img>
										</v-avatar>
										{{ data.item.country }}
									</div>
								</template>
								<template v-slot:item="data">
									<v-list-item-avatar tile>
										<img :src="data.item.img">
									</v-list-item-avatar>
									<v-list-item-content>
										<v-list-item-title v-html="data.item.country"></v-list-item-title>
										<v-list-item-subtitle v-html="data.item.language"
											v-if="data.item.country == 'Canada'"></v-list-item-subtitle>
									</v-list-item-content>
								</template>
							</v-autocomplete>
						</div>
					</div>
				</div>
				<v-data-table :headers="headers3" :items="imgItemData" :items-per-page="-1" :hide-default-footer="true">
					<template v-slot:item.url="{ item }">
						<img :src="item.url" :alt="item.alt" style="width: 100px;height: 100px;">
					</template>
					<template v-slot:item.actions="{ item }">
						<v-tooltip top>
							<template v-slot:activator="{ on, attrs }">
								<v-icon v-bind="attrs" v-on="on" color="primary" class="mr-2"
									@click.stop="imgDataEdit(item, 'edit')">
									mdi-square-edit-outline
								</v-icon>
							</template>
							<span>Edit</span>
						</v-tooltip>
						<v-tooltip top>
							<template v-slot:activator="{ on, attrs }">
								<v-icon v-bind="attrs" v-on="on" color="primary" class="mr-2" @click="delPageImgById(item)">
									mdi-trash-can-outline
								</v-icon>
							</template>
							<span>Deleting pages</span>
						</v-tooltip>
					</template>
				</v-data-table>
			</v-card>
		</v-dialog>
		<v-dialog v-model="addimgDialog" scrollable max-width="52.0833vw">
			<v-card class="addCard">
				<v-form class="addFrom" ref="imgFormData">
					<ul>
						<li><span class="avatar" style="color:#666666 ">Avatar</span>
							<v-input @click="triggerUpload">
								<div class="uploadBox">
									<template v-if="imgFormData.url">
										<div class="imgWrap">
											<img height="60" width="60" :src="imgFormData.url" style="border-radius: 50%" />
											<v-btn icon small class="closeIcon" color="primary"
												@click.stop="imgFormData.url = ''">
												<v-icon>mdi-close</v-icon>
											</v-btn>
										</div>
									</template>
									<template v-else>
										<span class="jia">+</span>
										<span class="input_upload">Upload</span>
									</template>
									<input type="file" ref="icon" accept="image/*" @change="uploadIcon">

								</div>
							</v-input>
						</li>
						<li>imgUrl
							<v-text-field v-model="imgFormData.url" outlined clearable label="imgUrl" single-line
								:rules="[rules.img]"></v-text-field>
						</li>
						<!-- <li>Title
							<v-text-field v-model="imgFormData.title" outlined clearable label="title" single-line></v-text-field>
						</li> -->
						<li>Alt&Title
							<v-text-field v-model="imgFormData.alt" outlined clearable label="Alt&Title" single-line
								:rules="[rules.required]"></v-text-field>
						</li>
						<li>ContentTitle
							<v-text-field v-model="imgFormData.contentTitle" outlined clearable label="contentTitle"
								single-line :rules="[rules.required]"></v-text-field>
						</li>
						<li>ContentSubtitle
							<v-text-field v-model="imgFormData.contentSubtitle" outlined clearable label="contentSubtitle"
								single-line :rules="[rules.required]"></v-text-field>
						</li>
						<li class="addBtn gg">
							<v-btn elevation="2" @click="imgDelClick">cancel</v-btn>
							<v-btn elevation="2" color="primary" @click="saveOrUpdatePageImg()">confirm</v-btn>
						</li>
					</ul>
				</v-form>
			</v-card>
		</v-dialog>

		<v-dialog v-model="logoDialog" width="540px">
			<v-card :loading="loading" :disabled="loading">
				<v-card-text class="pl-1 pr-1 pt-1 pb-0">
					<div class="country country1" style="margin-bottom: 10px;">
						<span>Current national language：</span>
						<v-autocomplete v-model="logoSelectValue" :items="logoLanguageList" solo flat label="Please select"
							outlined class="autocomplete" @change="logoSelectHandler">
							<template v-slot:selection="data">
								<div v-bind="data.attrs" :input-value="data.selected">
									<v-avatar left tile v-if="data.item.img">
										<v-img :src="data.item.img"></v-img>
									</v-avatar>
									{{ data.item.country }}
								</div>
							</template>
							<template v-slot:item="data">
								<v-list-item-avatar tile>
									<img :src="data.item.img">
								</v-list-item-avatar>
								<v-list-item-content>
									<v-list-item-title v-html="data.item.country"></v-list-item-title>
									<v-list-item-subtitle v-html="data.item.language"
										v-if="data.item.country == 'Canada'"></v-list-item-subtitle>
								</v-list-item-content>
							</template>
						</v-autocomplete>
					</div>
				</v-card-text>
				<v-card-text>
					<v-icon class="mr-2">mdi-format-title</v-icon>
					<span class="text-h7">Image Logo Setting</span>
				</v-card-text>
				<v-card-text class="pt-0">
					<p>Upload a HeadLogo</p>
					<v-card>
						<cutImage ref="cutImage2" :cutWidth="'472px'" logo @getMsrc="getMsrcFun(1)"
							v-model="cutPreviewValue">HeadLogo</cutImage>
					</v-card>
				</v-card-text>
				<v-card-text class="pt-0">
					<v-text-field hide-details v-model="logoAltTitle" label="Alt & Title"></v-text-field>
				</v-card-text>
				<v-card-text class="pt-0">
					<p>Upload a FootLogo</p>
					<v-card>
						<cutImage ref="cutImage1" :cutWidth="'472px'" logo @getMsrc="getMsrcFun(2)"
							v-model="cutFootPreviewValue">FootLogo</cutImage>
					</v-card>
				</v-card-text>
				<v-card-actions>
					<v-spacer></v-spacer>
					<v-btn depressed color="#909399" @click="logoDialog = false" min-width="100"
						min-height="40">Cancel</v-btn>
					<v-btn depressed color="primary" @click="confirmChangeLogo" min-width="100"
						min-height="40">Confirm</v-btn>
				</v-card-actions>
			</v-card>
		</v-dialog>

		<v-dialog v-model="structuringDialog" scrollable max-width="54.0833vw">
			<v-card class="addCard">
				<v-form v-model="valid" class="addFrom">
					<div class="addFrom_div">
						<div class="country">
							<span>Current national language：</span>
							<v-autocomplete v-model="editForm.nationalLanguage" :items="countryList" solo flat
								label="Please select" outlined class="autocomplete" @change="structuringLanguageClick">
								<template v-slot:selection="data">
									<div v-bind="data.attrs" :input-value="data.selected">
										<v-avatar left tile>
											<v-img :src="data.item.img"></v-img>
										</v-avatar>
										{{ data.item.country }}
									</div>
								</template>
								<template v-slot:item="data">
									<v-list-item-avatar tile>
										<img :src="data.item.img">
									</v-list-item-avatar>
									<v-list-item-content>
										<v-list-item-title v-html="data.item.country"></v-list-item-title>
										<v-list-item-subtitle v-html="data.item.language"
											v-if="data.item.country == 'Canada'"></v-list-item-subtitle>
									</v-list-item-content>
								</template>
							</v-autocomplete>
						</div>
						<div @click="structuringCancel" class="del">
							<v-icon>mdi-close</v-icon>
						</div>
					</div>
					<ul>
						<li class="gg">
							<span>Structuring:</span>
							<div>
								<div v-for="(item, index) in structuringList" :key="index" style="display: flex;">
									<v-textarea outlined v-model="item.value"></v-textarea>
									<v-icon style="height:10%;align-items: baseline;"
										@click="delStructuring(index)">mdi-trash-can-outline</v-icon>
								</div>
								<div style="text-align: center;">
									<v-btn @click="addStructuring" style="width: 50%;margin-bottom: 30px;" color="primary">+
										New structure</v-btn>
								</div>
							</div>
						</li>
						<li class="addBtn gg">
							<v-btn elevation="2" @click="structuringCancel" style="width:207px;">cancel</v-btn>
							<v-btn elevation="2" color="primary" @click="structuringConfirm" style="width:207px;">confirm</v-btn>
						</li>
					</ul>
				</v-form>
			</v-card>
		</v-dialog>
	</v-sheet>
</template>

<script>
import {
	getPagesDraftById,
	getHeadFootPagesDraft,
	listPage,
	getAudienceList,
	getPageById
} from "@/api/manage/buildWeb";
import cutImage from "@/components/buildWeb/cutImage.vue";
import { toReader } from "@/utils/buildWeb.js";
import {
	getProBindCate,
	changePublishStatus,
	movePageOrder,
	setLeading,
	getCustomPageSampleList,
	editPageTDK,
	deleteCustomPage,
	listAllProductOrSubCatePage,
	listPageImgByPageId,
	delPageImgById,
	saveOrUpdatePageImg,
	getIndependentDomian,
	updateLanguageById,
	updateSystemProjectById,
	getProjectStructuringByProId,
	saveOrUpdateProjectStructuring
} from "@/api/manage/siteEdit";
import { getWebSiteInfo } from '@/api/createWebsite'
import { getUsefulWebSite } from "@/api/manage/webManage";
import { getLanguage } from "@/api/pins"

import { Message } from 'element-ui';
import { uploadFile } from "@/utils/oss";

export default {
	name: "siteEdit",
	components: { cutImage },
	data () {
		return {
			structuringId:0,
			structuringDialog:false,
			rules: {
				required: value => !!value || 'Value Is Required.',
				img: v => !!v || "Please upload or fill in the image link."
			},
			structuringList: [],
			//国家语言列表
			countryList: [],
			imgItemData: [],
			showExpandIndex2: -1,
			showExpandIndex: -1,
			expandList: [
				{
					name: "小分类",
					cateType: 2
				},
				{
					name: "产品",
					cateType: 3
				}
			],
			crowdItems: [],
			editForm: {
				id: '',
				proId: '',
				pageName: '',
				description: '',
				keywords: '',
				title: '',
				crowdId: '',
				structuring: '',
				nationalLanguage: {},
				isTranslateEmpty: 0,
				routingName: '',
				routingNameLang: '',
			},

			valid: false,
			nameRules: [
				v => !!v || 'Name is required',
			],
			editDialog: false,
			imgDialog: false,
			addimgDialog: false,
			leadingSwitch: 0,
			currentSite: null,
			target: null,
			step: 1,
			pageData: {
				page: 1,
				pages: 1,
				pageSize: 10,
			},
			timed: null,
			keyWord: "",
			theCateValue: -1,
			filterByValue: -1,
			proId: null,
			headerFooterInfo: null,
			contentInfo: null,
			headerFooterId: null,
			contentId: null,
			dialog: false,
			collectionValue: "All products",
			collectionData: [
				{
					cateName: "All products",
					cateId: -1,
				},
			],
			items: [
				{
					name: "All pages",
					value: -1,
				},
				{
					name: "Shown Page",
					value: 1,
				},
				{
					name: "Hidden Page",
					value: 0,
				},
			],
			headers: [
				{ text: '', value: 'data-table-expand', width: "100" },
				{
					text: "Serial Number",
					align: "start",
					sortable: false,
					value: "index",
					width: "200"
				},
				{ text: "Webpages", value: "pageName" },
				{ text: "Route Name", value: "routingName" },
				{ text: "Route Name Lang", value: "routingNameLang" },
				{ text: "Modified Date", value: "createTime", width: 300 },
				{ text: "Actions ", value: "actions", width: "200" },
			],
			desserts: [],
			pageRowDraftList: [],
			total: [],
			editList: {},
			headers2: [
				{ text: "Webpages", value: "pageName" },
				{ text: "Route Name", value: "routingName" },
				{ text: "Route Name Lang", value: "routingNameLang" },
				{ text: "Modified Date", value: "createTime", width: 300 },
				{ text: "Actions ", value: "actions", width: 200 },
			],
			headers3: [
				{ text: "Url", value: "url", sortable: false, },
				{ text: "contentTitle", value: "contentTitle", sortable: false, },
				{ text: "contentSubtitle", value: "contentSubtitle", sortable: false, },
				{ text: "title ", value: "title", sortable: false, },
				{ text: "alt", value: "alt", sortable: false, },
				{ text: "Actions ", value: "actions", sortable: false, width: '100' },
			],
			imgFormData: {
				alt: '',
				contentSubtitle: '',
				contentTitle: '',
				title: '',
				url: ''
			},
			desserts2: [],
			keyword2: "",
			languageE: '',
			nationalLanguageTab: {},
			logoSelectValue: {},
			logoDialog: false,
			logoLanguageList: [],
			logoPreviewValue: "",
			cutPreviewValue: '',
			cutFootPreviewValue: '',
			logoAltTitle: '',
			loading: false,
			imgLanguageTab: '',
			imgItem: {},
			imgName: '',
			uploadType: 1
		};
	},
	methods: {
		structuringLanguageClick(){
			let data = {
				language: this.editForm.nationalLanguage.language + '-' + this.editForm.nationalLanguage.countryCode
			}
			this.getProjectStructuringByProId(data);
		},
		structuringCancel(){
			this.structuringDialog = false;
		},
		structuringConfirm(){
			let value;
			let arrayValue = this.structuringList.map(item => item.value), check = true;
			for (let i = 0; i < arrayValue.length; i++) {
				let item = arrayValue[i];
				if (!item) {
					check = false;
					continue
				}
				try {
					if (typeof JSON.parse(item) !== 'object' || item.trim()[0] !== '{') {
						check = false;
						continue
					}
				} catch (error) {
					check = false;
					continue
				}
			}
			if (!check) {
				this.$toast.error('improper format');
				return;
			}
			value = JSON.stringify(arrayValue)
			let data = {
				id:this.structuringId,
				proId: this.proId,
				structuring: value,
				language: this.editForm.nationalLanguage.language + '-' + this.editForm.nationalLanguage.countryCode,
			}
			saveOrUpdateProjectStructuring(data).then((res)=>{
				this.$toast.success(res.message);
				this.structuringDialog = false;
			})
		},
		async structureClick(){
			this.structuringDialog=true;
			await this.getLanguageList();
			let data = {
				language: "en-us"
			}
			this.getProjectStructuringByProId(data);
		},
		getProjectStructuringByProId(data){
			getProjectStructuringByProId(data).then((res)=>{
				this.structuringId = res.data.data.id;
				this.countryList.forEach((i) => {
					if ((i.language + '-' + i.countryCode) === res.data.data.language) {
						this.editForm.nationalLanguage = i;
					}
				})

				if (res.data.data.structuring) {
					let list = JSON.parse(res.data.data.structuring);
					this.structuringList = list.map(value => ({ value }));
				} else if (res.data.structuring === null) {
					this.structuringList = [];
				}
			})
		},

		delStructuring (index) {
			this.structuringList.splice(index, 1)
		},
		addStructuring () {
			this.structuringList.push({
				value: ""
			})
		},
		logoSelectHandler () {
			this.cutPreviewValue = this.logoSelectValue.proLogo
			this.cutFootPreviewValue = this.logoSelectValue.proLogoFooter
		},
		getMsrcFun (type) {
			this.uploadType = type
			this.$refs.uploads.click();
		},
		getFile (e) {
			this.loading = true
			let file = toReader(e);
			if (this.uploadType == 1) {
                uploadFile(file).then(res => {
					this.cutPreviewValue = res
					this.$nextTick(() => this.$refs.cutImage2.comeFromGF());
					this.$refs.uploads.value = "";
					this.loading = false
				}).catch(err => this.loading = false);
			} else if (this.uploadType == 2) {
                uploadFile(file).then(res => {
					this.cutFootPreviewValue = res
					this.$nextTick(() => this.$refs.cutImage1.comeFromGF());
					this.$refs.uploads.value = "";
					this.loading = false
				}).catch(err => this.loading = false);
			}
		},
		showLogoDialog () {
			this.cutPreviewValue = this.logoPreviewValue
			this.cutFootPreviewValue = this.logoSelectValue.proLogoFooter || ''
			this.logoAltTitle = this.logoSelectValue.logoAltTile || ''
			this.logoDialog = true
		},
		confirmChangeLogo () {
			if (this.logoSelectValue.country == 'Default') {
				let params = {
					id: this.proId,
					proLogo: this.cutPreviewValue,
					proLogoFooter: this.cutFootPreviewValue,
					proType: this.$store.state.proType,
					logoAltTile: this.logoAltTitle
				}
				updateSystemProjectById(params).then(res0 => {
					this.$message.success('修改成功')
					this.logoDialog = false
					this.getIndependentDomian(1)
				})
			} else {
				let params = {
					id: this.logoSelectValue.id,
					proLogo: this.cutPreviewValue,
					proLogoFooter: this.cutFootPreviewValue,
					logoAltTile: this.logoAltTitle,
					proType: this.$store.state.proType
				}
				updateLanguageById(params).then(res0 => {
					this.$message.success('修改成功')
					this.logoDialog = false
					this.getIndependentDomian(1)
				})
			}
		},
		// 是不是第一次调，第一次调在mounted里面改变
		async getIndependentDomian (type = 0) {
			const { data } = await getWebSiteInfo(this.proId)
			let obj = {
				country: 'Default',
				proLogo: data.proLogo || '',
				logoAltTile: data.logoAltTile || '',
				proLogoFooter: data.proLogoFooter || ''
			}
			getIndependentDomian(this.proId).then(res => {
				if (res.data && res.data.length > 0) this.logoLanguageList = [obj, ...res.data]
				else this.logoLanguageList = [obj]
				if (this.logoLanguageList.length > 0 && type == 1) {
					if (this.logoSelectValue && this.logoSelectValue.country) {
						let find = this.logoLanguageList.find((res) => res.country == this.logoSelectValue.country);
						if (find) this.logoSelectValue = find
					} else {
						let find = this.logoLanguageList.find((res) => res.country == 'United States');
						if (find) this.logoSelectValue = find
						else this.logoSelectValue = this.logoLanguageList[0]
					}
				}
			})
		},
		nationalLanguageTabClick () {
			this.listPage();
		},
		imgLanguageTabClick () {
			this.listPageImgByPageId(this.imgItem, this.imgLanguageTab.language + '-' + this.imgLanguageTab.countryCode)
		},
		nationalLanguageClick () {
			let data = {
				id: this.editForm.id,
				language: this.editForm.nationalLanguage.language + '-' + this.editForm.nationalLanguage.countryCode || this.languageE
			}
			getPageById(data).then((res) => {
				this.editForm.pageName = res.data.pageName;
				this.editForm.description = res.data.description;
				this.editForm.keywords = res.data.keyword;
				this.editForm.title = res.data.title;
				this.editForm.crowdId = res.data.audienceId;
				this.editForm.routingName = res.data.routingName;
				this.editForm.routingNameLang = res.data.routingNameLang;
				if (res.data.structuring) {
					let list = JSON.parse(res.data.structuring);
					this.structuringList = list.map(value => ({ value }));
				} else if (res.data.structuring === null) {
					this.structuringList = [];
				}
			})
		},
		toggleExpandIndex (index, expand, item) {
			if (this.showExpandIndex === index) {
				this.showExpandIndex = -1;
				return
			}
			let language
			if (this.nationalLanguageTab.language || this.nationalLanguageTab.countryCode !== undefined) {
				language = this.nationalLanguageTab.language + '-' + this.nationalLanguageTab.countryCode
			} else {
				language = "en-us"
			}
			this.desserts2 = [];
			this.showExpandIndex = index;
			let data = {
				keyWord: "",
				proId: this.proId,
				cateId: item.cateId,
				cateType: expand.cateType,
				isStockPage: item.isStockPage,
				language: language
			}
			listAllProductOrSubCatePage(data).then(res => {
				console.log(res, "res");
				this.desserts2 = res.data;
			})
		},
		//删除页面
		delPageClick (item) {
			this.$messageBox.show({
				title: 'WARNING',
				content: 'Delete This Section?',
				confirmText: 'Confirm'
			}).then(() => {
				let data = {
					id: item.id
				}
				deleteCustomPage(data).then((res) => {
					if (res.code == 200) {
						this.$message.success(res.message);
						this.listPage();
					}
				})
			}).catch(() => {
				this.$message.success('Cancel Delete')
			})
		},
		showImgPage (item) {
			console.log(item,'>>>>')
			this.imgDialog = true
			this.listPageImgByPageId(item, this.imgLanguageTab.language + '-' + this.imgLanguageTab.countryCode)
		},
		listPageImgByPageId (item, language) {
			let languageName = ''
			if (language == 'undefined-undefined') {
				languageName = this.nationalLanguageTab.language + '-' + this.nationalLanguageTab.countryCode
			}
			this.imgItem = item
			let data = {
				id: item.id,
				language: language == 'undefined-undefined' ? languageName : language
			}
			listPageImgByPageId(data).then((res) => {
				if (res.code == 200) {
					this.imgDialog = true
					this.imgItemData = res.data.data
					this.imgFormData.proId = item.proId
					this.imgFormData.pageId = item.id
					this.imgFormData.language = item.language
				}
			})
		},
		imgDataEdit (item, name) {
			this.imgName = name
			this.imgFormData = item
			this.addimgDialog = true
		},
		addDataEdit (name) {
			this.imgName = name
			this.addimgDialog = true
			this.imgFormData.alt = ''
			this.imgFormData.contentSubtitle = ''
			this.imgFormData.contentTitle = ''
			this.imgFormData.title = ''
			this.imgFormData.url = ''
		},
		delPageImgById (item) {
			delPageImgById(item.id).then((res) => {
				if (res.code == 200) {
					Message({
						message: 'Changed successfully',
						type: 'success'
					});
					this.listPageImgByPageId(this.imgItem, this.imgLanguageTab.language + '-' + this.imgLanguageTab.countryCode)
				}
			})
		},
		//查询人群
		getcrowd () {
			let data = {
				keyWord: ''
			}
			getAudienceList(data).then((res) => {
				this.crowdItems = res.data;
			})
		},

		//修改页面行
		async editClick (item) {
			this.editDialog = true;
			this.countryList = [];
			await this.getLanguageList();
			this.countryList.forEach((i) => {
				if ((i.language + '-' + i.countryCode) === (this.nationalLanguageTab.language + '-' + this.nationalLanguageTab.countryCode)) {
					this.editForm.nationalLanguage = i;
				}
			})

			this.editForm.id = item.id;
			this.editForm.proId = item.proId;
			this.editForm.keywords = item.keyword;
			this.editForm.crowdId = item.audienceId;
			this.editForm.routingName = item.routingName;
			this.editForm.pageName = item.pageName;
			this.editForm.description = item.description;
			this.editForm.title = item.title;
			this.languageE = this.nationalLanguageTab.language + '-' + this.nationalLanguageTab.countryCode;
			this.editForm.routingNameLang = item.routingNameLang;

			if (item.structuring) {
				let list = JSON.parse(item.structuring);
				this.structuringList = list.map(value => ({ value }));
			} else if (item.structuring === null) {
				this.structuringList = [];
			}

			this.nationalLanguageClick();
			this.getcrowd();
		},

		//查询国家语言
		getLanguageList () {
			return new Promise(resolve => {
				let data = {
					proId: this.proId
				}
				// getLanguage(data).then((res) => {
				// 	res.data.forEach((i) => {
				// 		i.childList.forEach((p) => {
				// 			this.countryList.push(p);
				// 			this.structuringCountryList.push(p);
				// 		})
				// 	})
				// 	resolve()
				// })
				getLanguage(data).then((res) => {
					res.data.forEach((i) => {
						i.childList.forEach((p) => {
							const isExist = this.countryList.some(item => item.id === p.id);
							if (!isExist) {
								this.countryList.push(p);
							}
						});
					});
					resolve();
				});
			})
		},

		edit () {
			let value;
			let arrayValue = this.structuringList.map(item => item.value), check = true;
			for (let i = 0; i < arrayValue.length; i++) {
				let item = arrayValue[i];
				if (!item) {
					check = false;
					continue
				}
				try {
					if (typeof JSON.parse(item) !== 'object' || item.trim()[0] !== '{') {
						check = false;
						continue
					}
				} catch (error) {
					check = false;
					continue
				}
			}
			if (!check) {
				this.$toast.error('improper format');
				return;
			}
			value = JSON.stringify(arrayValue)
			let data = {
				id: this.editForm.id,
				proId: this.editForm.proId,
				pageName: this.editForm.pageName,
				audienceId: this.editForm.crowdId,
				isTranslateEmpty: this.editForm.isTranslateEmpty || 0,
				title: this.editForm.title,
				structuring: value,
				keyword: this.editForm.keywords,
				description: this.editForm.description,
				language: this.editForm.nationalLanguage.language + '-' + this.editForm.nationalLanguage.countryCode,
				routingName: this.editForm.routingName,
				routingNameLang: this.editForm.routingNameLang
			}
			editPageTDK(data).then((res) => {
				this.$toast.success(res.message);
				this.delClick();
				this.listPage();
			})
		},
		delClick () {
			this.editDialog = false;
			this.listPage();
			this.editForm.id = "";
			this.editForm.proId = "";
			this.editForm.pageName = "";
			this.editForm.description = "";
			this.editForm.keywords = "";
			this.editForm.isTranslateEmpty = 0
			this.editForm.title = "";
			this.editForm.crowdId = "";
			this.editForm.structuring = "";
			this.editForm.nationalLanguage = "";
			this.editForm.routingNameLang = "";
			this.structuringList = [];
		},
		triggerUpload () {
			this.$refs.icon.click();
		},
		async uploadIcon () {
			const files = Array.from(this.$refs.icon.files);
			this.imgFormData.url = await uploadFile(files[0]);
			this.$refs.icon.value = null;
		},
		imgDelClick () {
			this.$refs.imgFormData.reset();
			this.addimgDialog = false
			this.imgFormData.alt = '',
				this.imgFormData.contentSubtitle = ''
			this.imgFormData.contentTitle = ''
			this.imgFormData.title = ''
			this.imgFormData.url = ''
			this.listPageImgByPageId(this.imgItem, this.imgLanguageTab.language + '-' + this.imgLanguageTab.countryCode)
		},
		saveOrUpdatePageImg () {
			let flag = this.$refs.imgFormData.validate()
			let data = null
			if (flag) {
				if (this.imgName == 'edit') {
					data = {
						id: this.imgFormData.id,
						proId: this.imgFormData.proId,
						pageId: this.imgFormData.pageId,
						url: this.imgFormData.url,
						contentTitle: this.imgFormData.contentTitle,
						contentSubtitle: this.imgFormData.contentSubtitle,
						title: this.imgFormData.title,
						alt: this.imgFormData.alt,
						language: this.imgFormData.language,
					}
				} else {
					data = {
						id: '',
						proId: this.imgFormData.proId,
						pageId: this.imgFormData.pageId,
						url: this.imgFormData.url,
						contentTitle: this.imgFormData.contentTitle,
						contentSubtitle: this.imgFormData.contentSubtitle,
						title: this.imgFormData.title,
						alt: this.imgFormData.alt,
						language: this.imgFormData.language,
					}
				}
				saveOrUpdatePageImg(data).then((res) => {
					if (res.code == 200) {
						Message({
							message: 'Changed successfully',
							type: 'success'
						});
						this.addimgDialog = false
						this.listPageImgByPageId(this.imgItem, this.imgLanguageTab.language + '-' + this.imgLanguageTab.countryCode)
					}
				})
			}
		},
		//新增页面
		addPage () {
			let data = {
				websiteStyleId: this.desserts[0].websiteStyleId
			}
			getCustomPageSampleList(data).then((res) => {
				// console.log(res);
				if (res.code === 200) {
					let language
					if (this.nationalLanguageTab.language || this.nationalLanguageTab.countryCode !== undefined) {
						language = this.nationalLanguageTab.language + '-' + this.nationalLanguageTab.countryCode
					} else {
						language = "en-us"
					}
					this.$router.push({
						path: "/manage/siteEdit/buildWeb",
						query: {
							id: res.data.pageId,
							type: 0, // 0 新增；1 编辑
							language
						},
					});
				}
			})
		},

		leadingSwitchClick (item) {
			item.isLeading = Number(item.isLeading)
			console.log(item, this)
			setLeading({ id: item.id }).then(res => {
				if (res.code == 200) {
					Message({
						message: 'Changed successfully',
						type: 'success'
					});
				} else {
					Message({
						message: 'Changed failed',
						type: 'error'
					});
				}
			})
		},
		changeOrdersFun (val) {
			this.movePageOrder(val);
		},
		isPublishFun (val) {
			let postData = {
				proId: this.proId,
				id: val.id,
				isPublish: !val.isPublish,
			};
			val.isPublish = !val.isPublish
			changePublishStatus(postData).then((res) => {
				this.listPage();
			});
		},
		changePages (val) {
			this.pageData.page = val;
			this.listPage();
		},
		keyWordFun () {
			this.debounce(this.filterFun)();
		},
		//防抖
		debounce (fn) {
			let that = this;
			if (typeof fn != "function") {
				return;
			}
			return function () {
				if (that.timed) {
					clearTimeout(that.timed);
				}
				that.timed = setTimeout(() => {
					fn();
				}, 1000);
			};
		},
		filterFun (val) {
			this.listPage();
		},
		getProBindCate () {
			getProBindCate(this.proId).then((res) => {
				this.collectionData = this.collectionData.concat(res.data);
			});
		},
		editPage (info) {
			this.$store.commit("setManageMobile", 0);
			let language
			if (this.nationalLanguageTab.language || this.nationalLanguageTab.countryCode !== undefined) {
				language = this.nationalLanguageTab.language + '-' + this.nationalLanguageTab.countryCode
			} else {
				language = "en-us"
			}
			this.$router.push({
				path: "/manage/siteEdit/buildWeb/",
				query: {
					id: info.id,
					type: 1,
					language
				},
			});
		},
		handlerRightClick (info) {
			this.$store.commit("setManageMobile", 0);
			let language
			if (this.nationalLanguageTab.language || this.nationalLanguageTab.countryCode !== undefined) {
				language = this.nationalLanguageTab.language + '-' + this.nationalLanguageTab.countryCode
			} else {
				language = "en-us"
			}
			const routeUrl = this.$router.resolve({
				path: "/manage/siteEdit/buildWeb/",
				query: {
					id: info.id,
					type: 1,
					language
				}
			});
			window.open(routeUrl.href, '_blank');
		},
		getPageTemplateFun (info) {
			//初始化页面id
			return new Promise((resolve, reject) => {
				let postData = {
					id: info.id,
				};

				let postData2 = {
					proId: this.$store.getters["manage/getProId"],
					websiteStyleId: 1,
				};
				Promise.all([getPagesDraftById(postData), getHeadFootPagesDraft(postData2)]).then(
					(res) => {
						let contentInfo = res[0].data;
						let headerFooterInfo = res[1].data;

						this.headerFooterId = headerFooterInfo.id;
						this.contentId = contentInfo.id;

						if (headerFooterInfo.pageRowDraftList.length > 0) {
							this.pageRowDraftList = headerFooterInfo.pageRowDraftList;
							contentInfo.pageRowDraftList.forEach((item, index) => {
								this.pageRowDraftList.splice(index + 1, 0, item);
							});

							//获取页面样式数据
							//主题色
							this.pageRowDraftList.forEach((item) => {
								item.sampleData = JSON.parse(item.sampleData);
								item.randomKey = Math.random();
							});
						}
						resolve();
					}
				);
			});
		},
		async showPage (val) {
			this.target = val;
			this.$store.commit("setUserInfo", {
				proId: this.$cookies.get("manageProId"),
			});
			await this.getPageTemplateFun(val);
			this.step = 2;
		},
		movePageOrder (item) {
			let postData = {
				proId: this.$cookies.get("manageProId"),
				pageId: item.id,
				newOrder: Number(item.orders),
			};
			movePageOrder(postData).then((res) => {
				this.listPage();
			});
		},
		editItem () {
		},
		deleteItem () {
		},
		listPage () {
			let language

			if (this.nationalLanguageTab.language || this.nationalLanguageTab.countryCode !== undefined) {
				language = this.nationalLanguageTab.language + '-' + this.nationalLanguageTab.countryCode
			} else {
				language = "en-us"
			}
			let postData = {
				page: this.pageData.page,
				pageSize: this.pageData.pageSize,
				keyWord: this.keyWord,
				proId: this.proId,
				pageType: null,
				isPublish: this.filterByValue,
				cateId: this.theCateValue,
				language: language
			};
			listPage(postData).then((res) => {
				let desserts = res.data.records;
				desserts.sort(function (a, b) {
					return Number(a.orders) - Number(b.orders);
				});
				this.desserts = desserts;
				let temp = res.data.total;
				for (let i = 1; i <= temp + 1; i++) {
					this.total.push(i);
				}
				console.log(this.total);
				this.pageData.pages = res.data.pages;
			});
		},
	},
	watch: {
		logoSelectValue: {
			handler (val) {
				if (val) {
					this.logoPreviewValue = val.proLogo || ''
					this.logoAltTitle = val.logoAltTile || ''
					this.cutFootPreviewValue = this.logoSelectValue.proLogoFooter || ''
				}
			},
			immediate: true,
			deep: true,
		},
		addimgDialog: {
			handler (newName, oldName) {
				if (!newName) {
					this.listPageImgByPageId(this.imgItem, this.imgLanguageTab.language + '-' + this.imgLanguageTab.countryCode)
				}
			},
		},
		imgDialog: {
			handler (newName, oldName) {
				if (newName) {
					console.log(newName);
					this.imgLanguageTab = this.nationalLanguageTab
				}
			},
		}
	},
	async mounted () {
		if (this.$route.query.proName) {
			const website = await getUsefulWebSite();
			const webSiteList = website.data;
			this.$store.commit("manage/updateSiteList", webSiteList);
			let findItem = webSiteList.find(
				(item) => item.proName === this.$route.query.proName
			);
			if (findItem) {
				this.$store.commit("manage/updateSite", findItem);
				this.$cookies.set("otoManageProId", findItem.id, {
					maxAge: 60 * 60 * 24 * 7,
					path: "/",
				});
				location.replace(`${location.origin}/manage/siteEdit/contentEdit`);
				return false;
			}
		}
		this.currentSite = this.$store.state.manage.currentSite;
		setTimeout(() => {
			if (this.countryList.length > 0) {
				let find = this.countryList.find((res) => (res.language + "-" + res.countryCode) === "en-us");
				if (find) {
					this.nationalLanguageTab = find;
				}
			}
			if (this.logoLanguageList.length > 0) {
				let find = this.logoLanguageList.find((res) => res.country === 'United States');
				if (find) this.logoSelectValue = find
				else this.logoSelectValue = this.logoLanguageList[0]
			}
		}, 2000)
	},
	created () {
		this.proId = this.$store.getters["manage/getProId"];
		this.getIndependentDomian()
		this.$cookies.remove('manageMobile', { domain: 'localhost', path: '/manage/siteEdit/buildWeb' });
		this.$cookies.remove('manageMobile', { domain: 'localhost', path: '/manage/siteEdit' });
		this.$cookies.remove('manageMobile', { domain: 'localhost', path: '/manage' });
		this.listPage();
		this.getProBindCate();
		this.getLanguageList();
	}
};
</script>

<style scoped lang="scss">
.uploadBox {
	flex: 1;
	position: relative;
	height: 172px;
	background: #FFFFFF;
	border: 1px dashed #E6E6E6;
	border-radius: 6px;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	transition: all .3s;
	cursor: pointer;

	&.disabled:hover {
		border-color: #E6E6E6;
	}

	.imgWrap {
		display: flex;
		justify-content: center;
		align-items: center;
		width: 120px;
		height: 120px;
		position: relative;
		background-color: #EEEEEE;

		.closeIcon {
			position: absolute;
			top: 0;
			right: 0;
			transform: translate(50%, -50%);
		}
	}

	&:hover {
		border-color: var(--v-primary-base);
	}

	input {
		opacity: 0;
		cursor: pointer;
		z-index: -1;
		height: 0;
	}
}

.form {
	padding: 0;
	color: #666666;

	ul {
		padding: 0;
		display: flex;
		margin-bottom: -15px;

		li {
			list-style: none;
			width: 250px;
			padding: 0px;
			margin-right: 20px;
			font-size: 14px;
		}
	}

	.form_li {
		margin-top: 12px;
	}

	.wenhao {
		.v-icon:hover {
			color: #1A73E8;
		}
	}

	.remark {
		width: 790px;
		margin-top: 12px;
	}
}

.country {
	display: flex;
	background: #ccc;
	line-height: 40px;
	padding: 3px 10px;
	height: 47px;
	margin-bottom: 20px;
	border-radius: 3px;

	.autocomplete {
		padding: 0;
		height: 0;
	}

	::v-deep .v-text-field__details {
		display: none;
	}

	::v-deep .v-text-field--outlined>.v-input__control {
		min-height: 30px;

		>.v-input__slot {
			width: 350px;
			min-height: 40px;
			margin-bottom: 0;

			.cusSelection {
				margin-bottom: 0;
				display: flex;
			}

			.v-avatar {
				width: 25px !important;
				height: auto !important;
			}
		}
	}
}

.country1 {
	::v-deep .v-text-field--outlined>.v-input__control {
		>.v-input__slot {
			width: 285px !important;
		}
	}
}

::v-deep .v-list-item__action,
.v-list-item__avatar,
.v-list-item__icon {
	display: inline-flex;
	min-width: 24px;
	width: 48px !important;
	height: 30px !important;
}

// ::v-deep .v-list-item__content{
// 	flex-wrap: nowrap;
// 	width: 48%;
// }

.addCard {
	height: 100%;
	padding: 3.6458vw 2.6042vw 2vw;


	.addFrom_div {
		display: flex;
		justify-content: space-between;
	}

	.addFrom {
		.del {
			display: flex;
			justify-content: flex-end;
			position: relative;
			bottom: 1.5625vw;
		}

		ul {
			padding: 0;
			margin: 0;
			display: grid;
			grid-template-columns: repeat(2, 1fr);
			grid-gap: 0 2.0833vw;

			li:first-of-type {
				grid-row-start: span 2;
			}
			li {
				list-style: none;
				line-height: 30px;
			}

			.gg {
				grid-column-start: 1;
				grid-column-end: 3;
			}

			.addBtn {
				text-align: center;

				.v-btn {
					width: 150px;
					height: 50px;
					font-size: 20px;
					margin: 0 20px;
				}
			}
		}
	}
}

.siteEdit {
	.viewBox {
		display: flex;
		justify-content: space-between;

		.left {
			width: 100vw;
			transform: scale(0.75);
			transform-origin: 0 0;
			background-color: #ffffff;
			box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.15);
			max-height: 100vh;
			overflow-y: scroll;

			::v-deep a {
				color: inherit;
			}

			.header {
				height: 44px;
				background: #f2f4f5;

				>div {
					display: flex;
					justify-content: space-between;
					align-items: center;
					height: 100%;

					.ic {
						i {
							font-size: 80px !important;
						}
					}

					.urlDiv {
						background: #ffffff;
						width: 100%;
						height: 30px;
						margin-right: 20px;
						display: flex;
						align-items: center;
						padding-left: 10px;
					}
				}
			}
		}
	}

	.custom-scrollbar::-webkit-scrollbar {
		/*滚动条整体样式*/
		width: 5px;

		/*高宽分别对应横竖滚动条的尺寸*/
		height: 1px;
	}

	.custom-scrollbar.csu1::-webkit-scrollbar {
		height: 50px;
	}

	.custom-scrollbar::-webkit-scrollbar-thumb {
		/*滚动条里面小方块*/
		border-radius: 10px;
		background: #d3d3d3;
	}

	.custom-scrollbar::-webkit-scrollbar-track {
		/*滚动条里面轨道*/
		border-radius: 10px;
	}
}

.expand-item {
	padding-left: 80px;

	.expand-title {
		display: flex;
		align-items: center;
		height: 50px;
		font-size: 16px;
	}
}
</style>
