<template>
	<div id="pie-chart-show">
		<div class="top">WHAT'S YOUR DETAILED SALES DATA BY CATEGORY?</div>
		<div class="main">
			<div class="main-top">
				<div class="left">
					<span class="main-heading">Inquiries / Orders / Revenue</span>
					<span class="sub-heading">by category Total</span>
				</div>
				<div class="right">
					<v-select :items="selectValue" label="Time" solo v-model="currentSelected" @change="changeSelected">
					</v-select>
				</div>
			</div>
			<div class="main-pie-area">
				<analysismain-InquiriesPieShow :filtByTime="currentSelected" v-bind="$attrs" />
			</div>
			<div class="main-pie-area">
				<analysismain-OrdersPieShow :filtByTime="currentSelected" v-bind="$attrs" />
			</div>
			<div class="main-pie-area">
				<analysismain-RevenuePieShow :filtByTime="currentSelected" v-bind="$attrs"/>
			</div>
		</div>
	</div>
</template>

<script>

export default {
	name: "PieChartShow",
	
	props: {
		
	},
	created() {

	},
	mounted() {
		// console.log('$attrs', this.$attrs);
		// console.log('inquiriesData', this.inquiriesData);
	},
	data() {
		return {
			selectValue: ["Years", "Months", "Weeks", "Days"],
			currentSelected: '',
		};
	},
	methods: {
		changeSelected(value) {
			// console.log(this.currentSelected, value);
		}
	}
};
</script>

<style lang="scss" scoped>
#pie-chart-show {
	width: 100%;
}

.top {
	width: 23.69791667vw;
	height: 1.5625vw;
	margin-left: 0.98958333vw;
	/* background: #e5e5e5; */
	border-radius: 0.3125vw;
	margin-bottom: 0.8854vw;
	font-size: 0.9375vw;
	font-weight: 400;
	color: #333333;
}

.main {
	height: 45.9375vw;
	background: #ffff;
	border-radius: 0.5208vw;
	padding: 0 1.0417vw;

	.main-top {
		display: flex;
		height: 3.125vw;
		padding: 0 1.0417vw;

		.left {
			width: 23.69791667vw;
			height: 1.0417vw;
			margin-top: 1.4583vw;
			margin-right: 5.9375vw;
			/* background: #e5e5e5; */
			border-radius: 0.3125vw;

			.main-heading {
				font-size: 0.9375vw;
				margin-right: 0.4167vw;
			}

			.sub-heading {
				font-size: 13px;
				color: #b0b0b0;
			}
		}

		.right {
			width: 6.1292vw;
			height: 2.0833vw;
			background: #ffffff;
			border: 0.0521vw solid #e5e5e5;
			border-radius: 0.3125vw;
			margin-top: 0.9896vw;
			overflow: hidden;
		}
	}

	.main-pie-area {
		height: 12.7083vw;
		/* width: 35.3646vw; */
		/* padding: 2.3438vw 3.3854vw 0 3.4896vw; */
		/* padding-left: 3.6458vw;
		padding-right: 3.3854vw; */
		margin: 1.25vw 0;
		display: block;
	}
}
</style>

<style lang="scss">
#pie-chart-show {
	.v-select {
		top: -0.2604vw;
	}

	.v-select__slot {
		font-size: .7292vw;
	}
}
</style>
