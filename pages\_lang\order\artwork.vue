<template>
	<client-only>
		<div class="container-confirm">
			<div flex class="step-box">
				<p v-for="(i, index) in lang.step" :class="{ select: index <= productStatus }" :key="i">
					<strong>
						<b v-if="index < productStatus" class="icon-gou"></b>
						<template v-else>{{ index + 1 }}</template>
					</strong>
					{{ i }}
				</p>
			</div>
			<div flex class="order-box">
				<div flex>{{ lang.orderNumber }}: <label class="number">{{ oid }}</label></div>
				<!--			<div flex>{{ payItem.oid ? lang.balanceDue : lang.finalPrice }}:-->
				<div flex>
					<!--				有子单全部付款或者无子单groundTotal 否则balanceDue-->
					{{ ((childOrder && childOrder.length>0) && !payItem.oid) || (!childOrder || !childOrder.length) ? lang.groundTotal : lang.balanceDue}}
					<!--				有子单全部付款或者无子单多item则有allItems-->
					<span v-if="((childOrder&& childOrder.length>0) && !payItem.oid) || (!childOrder || !childOrder.length && ordersProducts.length>1)">({{lang.allItems}})</span>:
					<label>
						<CCY-rate :ccy="ccy" red class="amount" :price="payItem.totalPrice || orderPrice"/>
						<!--	<b class="icon-jxsht-jcsz-wh" v-show="(payItem.totalPrice || orderPrice) != subTotal">
								<div class="tips" hidden>
									<strong>{{ lang.orderSummary }}</strong>
									<div flex v-show="subTotal">
										<span>{{ lang.subTotal }} ({{ ordersProducts.length }} {{ lang.items }}):</span>
										<CCY-rate :ccy="ccy" :price="subTotal"/>
									</div>
									<div flex v-show="taxPrice">
										<span>{{ lang.taxPrice }}:</span>
										+
										<CCY-rate :ccy="ccy" :price="taxPrice"/>
									</div>
									<div flex v-show="apoShippingPrice">
										<span>{{ lang.apoShippingFee }}:</span>
										+
										<CCY-rate :ccy="ccy" :price="apoShippingPrice"/>
									</div>
									<div flex v-show="firstDiscountPrice">
										<span>{{ lang.firstOrderCodeDisc }} ({{ firstDiscount * 100 }}% {{ lang.off }}):</span>
										-
										<CCY-rate :ccy="ccy" :price="firstDiscountPrice"/>
									</div>
									<div flex v-show="couponAdditionalCosts">
										<span>{{ lang.couponAdditionalCosts }}:</span>
										+
										<CCY-rate :ccy="ccy" :price="couponAdditionalCosts"/>
									</div>
									<div flex v-show="crowdDiscountPrice">
										<span>
											<b v-if="couponAdditionalCosts">$1 for a $75 Voucher</b>
											<b v-else>{{ lang.specialCode }}</b>
											<b v-show="crowdDiscount">({{ crowdDiscount * 100 }}%)</b>:</span>
										-
										<CCY-rate :ccy="ccy" :price="crowdDiscountPrice"/>
									</div>
									<div flex v-show="pointPrice">
										<span>{{ lang.pointsDeduct }} ({{ point }} {{ lang.points }}):</span>
										-
										<CCY-rate :ccy="ccy" :price="pointPrice"/>
									</div>
									<div flex v-show="voucherPrice">
										<span>{{ lang.giftVouchers }}:</span>
										-
										<CCY-rate :ccy="ccy" :price="voucherPrice"/>
									</div>
									<div flex v-show="couponDiscountPrice">
										<span>{{ lang.coupon }} <b v-show="couponDiscount">({{
												couponDiscount * 100
											}}%)</b>:</span>
										-
										<CCY-rate :ccy="ccy" :price="couponDiscountPrice"/>
									</div>
									<div flex v-show="userLevelDiscountPrice">
										<span>{{ langCart.userLevel }} <b v-show="userLevelDiscount">({{
												userLevelDiscount * 100
											}}%)</b>:</span>
										-
										<CCY-rate :ccy="ccy" :price="userLevelDiscountPrice"/>
									</div>

									<div flex v-show="orderDiscountPrice">
										<span>{{ lang.goldVIPMember }} ({{ orderDiscount * 100 }}%):</span>
										-
										<CCY-rate :ccy="ccy" :price="orderDiscountPrice"/>
									</div>
									&lt;!&ndash; <div flex v-show="orderDiscountPrice">
										<span>Order Discount({{orderDiscount}}%):</span>
										-<CCY-rate :ccy="ccy" :price="orderDiscountPrice" />
									</div> &ndash;&gt;
									<div flex v-show="promotionPrice">
										<span>{{ lang.extraDiscount }} ({{ promotionDiscount * 100 }}%):</span>
										-
										<CCY-rate :ccy="ccy" :price="promotionPrice"/>
									</div>
									&lt;!&ndash; 最终价格若等于优惠前价格，则说明没有变动，则不展示finalPrice &ndash;&gt;
									<div flex v-show="payItem.oid && orderPrice != subTotal">
										<span>{{ lang.finalPrice }}:</span>
										<CCY-rate :ccy="ccy" :price="orderPrice"/>
									</div>
									<div flex v-show="payItem.oid && payItem.oid != oid">
										<span>{{ lang.amountPaid }}:</span>
										<CCY-rate :ccy="ccy" :price="orderPrice - (payItem.totalPrice || 0)"/>
									</div>
									<div flex class="final">
										<strong>{{ payItem.oid ? lang.balanceDue : lang.finalPrice }}:</strong>
										<del v-show="discountPrice">
											<CCY-rate :ccy="ccy" :price="subTotal"/>
										</del>
										<CCY-rate :ccy="ccy" red :price="payItem.totalPrice || orderPrice"/>
									</div>
								</div>
							</b>-->
					</label>
				</div>
				<div flex v-show="$store.getters.isMobile" @click="showMenu = !showMenu">
					{{ lang.artworkNumber }}:
					<client-only>
						<label class="ordersProducts">
							<span v-if="unPaidOidList.length == 0  && allConfirmed">{{ productNum + 1 }}</span>
							<span v-else>{{ proofIndex + 1 }} </span>
							/
							<span v-if="unPaidOidList.length == 0  && allConfirmed">{{ ordersProducts.length }}</span>
							<span v-else>{{ newArr.length }}</span>
							<b v-show="ordersProducts.length > 1" class="icon-arrow-down arrow"
							   :class="{ rotate: showMenu }"></b>
						</label>
					</client-only>
				</div>
<!--				invoiceId !== null && invoiceId !== 'null' && invoiceId !== ''-->
				<button primary @click="buyAgain" v-if="(!unPaidOidList || unPaidOidList.length ==0) && invoiceId !== null && invoiceId !== 'null' && invoiceId !== ''">{{
						lang.buyAgain
					}}
				</button>
				<!--			<button v-ripple class="check-out" @click="buyAgain" v-if="isAllOperated && unPaidOidList && unPaidOidList.length>0">{{langCart.checkOut}}</button>-->
				<nuxt-link :to="checkoutLink" v-if="(isAllOperated && unPaidOidList && unPaidOidList.length>0) || payLater">
					<button v-ripple class="check-out">{{langCart.checkOut}}</button>
				</nuxt-link>
			</div>

			<div flex class="main">
				<!--			如果订单（主单+子单）全部确认图稿(allConfirmed)且全部付款(unPaidOidList.length == 0)，则全部展示。-->
				<div class="menu" v-if="ordersProducts.length > 1 && showMenu" @click.self="showMenu = !showMenu">
					<div class="index">
						<span v-if="unPaidOidList.length == 0  && allConfirmed">{{ productNum + 1 }}</span>
						<span v-else>{{ proofIndex + 1 }} </span>
						/
						<span v-if="unPaidOidList.length == 0  && allConfirmed">{{ ordersProducts.length }}</span>
						<span v-else>{{ newArr.length }}</span>
					</div>
					<div flex scrollbar class="list-box" ref="productList"
						 v-if="unPaidOidList.length == 0  && allConfirmed">
						<div v-for="i in ordersProducts" :class="{ select: productNum == i.indexNum }" :key="i.indexNum"
							 @click="(productNum = i.indexNum)">
							<div class="proof" pointer>{{ lang.proof }} #{{ i.indexNum + 1 }}</div>
							<CustomImage class="customImage" v-if="i.picPath[0].picLink || i.picPath[0].url"
										 :url="i.picPath[0].picLink || i.picPath[0].url" :show-file-name="false"
										 :preview="false"></CustomImage>


							<b :class="{ 'icon-gou': i.confirm == 1, 'icon-edit': i.confirm == 2 }"></b>
							<b :class="{ 'icon-Approved': i.confirm == 1, 'icon-Modify': i.confirm == 2 }"></b>
						</div>
					</div>
					<div flex scrollbar class="list-box" ref="productList" v-else>
						<!--					只展示未付款或者未确认的-->
						<div
							v-for="(i,x) in ordersProducts.filter(obj=> obj.confirm !== 1 || unPaidOidList.find(ele=> ele == obj.oid))"
							:class="{ select: proofIndex == x }" :key="i.indexNum"
							@click="(productNum = i.indexNum,proofIndex = x)">
							<div class="proof" pointer>{{ lang.proof }} #{{ x + 1 }}</div>
							<CustomImage class="customImage" v-if="i.picPath[0].picLink || i.picPath[0].url"
										 :url="i.picPath[0].picLink || i.picPath[0].url" :show-file-name="false"
										 :preview="false"></CustomImage>


							<b :class="{ 'icon-gou': i.confirm == 1, 'icon-edit': i.confirm == 2 }"></b>
							<b :class="{ 'icon-Approved': i.confirm == 1, 'icon-Modify': i.confirm == 2 }"></b>
						</div>
					</div>
				</div>

				<div class="product-box">
					<div flex center class="name" v-if="unPaidOidList.length == 0  && allConfirmed">
						<strong>{{ lang.proof }} <span v-if="ordersProducts && ordersProducts.length>1">#{{ productNum + 1 }}</span></strong>
						<b :class="{ 'icon-Approved': product.confirm == 1, 'icon-Modify': product.confirm == 2 }"></b>
						<strong>{{ product.productName }}</strong>
					</div>
					<div flex center class="name" v-else>
						<strong>
							<span>{{ lang.proof }} <span v-if="ordersProducts && ordersProducts.length>1">#{{ proofIndex + 1 }}</span></span>
						</strong>
						<b :class="{ 'icon-Approved': newArr[proofIndex].confirm == 1, 'icon-Modify': newArr[proofIndex].confirm == 2 }"></b>
						<strong>{{ product.productName }}</strong>
					</div>

					<div flex center class="product">
						<b class="icon-left pointer" @click="artworkIndex -= 1" v-show="product.picPath.length > 1"></b>
						<CustomImage class="customImage2"
									 v-if="product.picPath[artworkIndex].picLink || product.picPath[artworkIndex].url"
									 :url="product.picPath[artworkIndex].picLink || product.picPath[artworkIndex].url"
									 :show-file-name="false" :preview="true"
									 :alt="product.picPath[artworkIndex].draftType"></CustomImage>


						<b class="icon-right pointer" @click="artworkIndex += 1" v-show="product.picPath.length > 1"></b>
					</div>

					<div flex center class="system">
						<!--					 除了图片 gif bmp可以大图预览 pdf新窗口预览，其他的都只有下载-->
						<b v-if="getFileTypeIsPic(product.picPath[artworkIndex].picLink) && product.picPath[artworkIndex].picLink"
						   flex pointer class="icon-a-tgsc-addzhuanhuan" @click="artworkView"
						   v-show="product.picPath[artworkIndex].picLink">{{ lang.view }}</b>
						<a v-if="getFileIsPdf(product.picPath[artworkIndex].picLink) && product.picPath[artworkIndex].picLink"
						   :href="product.picPath[artworkIndex].picLink" target="_blank">
							<b flex pointer class="icon-a-tgsc-addzhuanhuan">{{ lang.view }}</b>
						</a>
						<b flex pointer class="icon-a-tgsc-Downloadzhuanhuan" @click="artworkDownload"
						   v-if="product.picPath[artworkIndex].picLink">{{ lang.download }}</b>
<!--						<b flex pointer class="icon-a-lrgl-dezhuanhuan" @click="artworkRemove"
						   v-if="orderStatus == 3 && product.confirm != 1 && (product.picPath[artworkIndex].picLink || product.picPath[artworkIndex].url)">{{
								lang.remove
							}}</b>-->
					</div>

					<div flex scrollbar class="list-box" v-show="product.picPath.length > 1" ref="artworkList">
						<div pointer v-for="(i, index) in product.picPath" :class="{ select: index == artworkIndex }"
							 @click="artworkIndex = index" :key="i.picLink">
							<CustomImage class="customImage" v-if="i.picLink || i.url" :url="i.picLink || i.url"
										 :show-file-name="false" :preview="false"></CustomImage>
							<p>{{ i.draftType || i.picName && i.picName.split(".")[0].replace(/-/g, " ") }}</p>
						</div>
					</div>
					<div flex class="point-box" v-show="product.picPath.length > 4">
						<div v-for="(i, index) in product.picPath" :class="{ select: artworkIndex == index }" :key="index"
							 @click="artworkIndex = index"></div>
					</div>
				</div>

				<div class="info-box">
					<div class="info" scrollbar>
						<!--					v-if="productStatus == 1"-->
						<div class="modifying" v-if="!allConfirmed && product.confirm == 2">
							<strong>{{ lang.artworkModifying }}</strong>
							{{ lang.modifying }}
							<div pre class="contact">{{ lang.contact }} {{ manage.email }}</div>

							<b flex center class="icon-a-icon-web3zhuanhuan">{{ lang.remark }}</b>

							<!--						<a :href="checkoutLink" target="_blank" v-show="payItem.id">
														<button class="buy-now" primary> {{ lang.buyNow }}
															<b class="icon-jxsht-jcsz-wh">
																<div class="tips" hidden>{{ lang.buyNowTip }}</div>
															</b>
														</button>
													</a>-->
						</div>
						<div class="modifying" v-if="!allConfirmed && product.confirm == 1">
							<strong>Artwork Approved</strong>
							<!--						全部操作完-->
							<span v-if="isAllOperated">
<!--							1 artwork has been approved, and 2 artworks are pending revision. Thanks for your patience!-->
								<!--							2 artworks have been approved, and 1 artwork is pending revision. Thanks for your patience!-->
							{{ this.ordersProducts.filter(i => i.confirm == 1).length }}
							{{ this.ordersProducts.filter(i => i.confirm == 1).length <= 1 ? lang.completeTip[0]: lang.completeTip[1]}}
							{{ this.ordersProducts.filter(i => i.confirm == 2).length }}
							{{ this.ordersProducts.filter(i => i.confirm == 2).length <= 1 ? lang.completeTip[2]: lang.completeTip[3]}}
							{{ lang.completeTip[4] }}
						</span>
							<!--						comfirm 0未操作，1已通过，2修改-->
							<!--						未操作完-->
							<span v-else>
<!--							1 artwork has been approved, 1 artwork is under revision, and 2 artworks are pending confirmation!-->
								<!--2 artworks have been approved, 2 artworks are under revision, and 1 artwork is pending confirmation!-->
							{{ this.ordersProducts.filter(i => i.confirm == 1).length }}
							{{ this.ordersProducts.filter(i => i.confirm == 1).length <= 1 ? lang.completeTip[5] : lang.completeTip[6] }}
								<span v-if="this.ordersProducts.filter(i => i.confirm == 2).length">
									{{ this.ordersProducts.filter(i => i.confirm == 2).length }}
									{{ this.ordersProducts.filter(i => i.confirm == 2).length <= 1 ? lang.completeTip[7] : lang.completeTip[8] }}
								</span>
								<span v-else>{{lang.completeTip[11]}}</span>

							{{ this.ordersProducts.filter(i => i.confirm == 0).length }}
							{{ this.ordersProducts.filter(i => i.confirm == 0).length <= 1 ? lang.completeTip[9] : lang.completeTip[10] }}
						</span>
						</div>
						<template>
							<!-- <label class="view-more" @click="viewMore" v-show="showViewMore">
								 <strong>{{ lang.viewMore }}</strong> <b class="icon-Down"></b>
							 </label>-->

							<div class="details view-box" ref="viewBox">
								<strong>{{ lang.details }}</strong>
								<label v-if="product.productName">
									<span class="label">{{ lang.itemName }}:</span>
									<span class="con">{{ product.productName }}</span>
								</label>
								<label v-for="i in product.productParam" :key="i.key">
									<span class="label">{{ i.key }}:</span>
									<span class="con">{{ i.value }}</span>
								</label>
								<label v-if="product.comments">
									<span class="label">{{ langCart.comments }}:</span>
									<span class="con" style="white-space: pre-wrap">{{ product.comments }}</span>
								</label>
							</div>

							<div class="message" v-show="product.confirm == 2 || product.messageLists.length">
								<strong>{{ lang.message }}
									<label>
										<b pointer class="icon-a-icon-Historyzhuanhuan" v-show="product.messageLists.length"
										   @click="checkEmail ? showHistory = true : showCheckEmail = true">{{
												lang.history
											}}</b>
										<!--<b pointer class="icon-a-icon-Editzhuanhuan" v-show="product.message"
										   @click="checkEmail ? showModify = true : showCheckEmail = true">{{
												lang.edit
											}}</b>-->
									</label>
								</strong>
								<label><span>{{ product.message || "-" }}</span></label>

								<label v-for="(i, index) in product.msgPicPath" :key="index + 100">
									<a :href="i.imgLink" target="_blank">{{ i.imgName }}</a>
									<!--								<b class="icon-a-lrgl-dezhuanhuan" @click="msgImgRemove(index)"></b>-->
								</label>
							</div>

							<div class="total details">
								<label>
									<span>{{ lang.subTotal }}:</span>
									<span>
								<label>
									<CCY-rate :ccy="ccy"
											  :price="product.quantity * product.unitPrice + product.mouldPrice + product.extraPrice + product.setupCharge"
											  :style="product.discount !== 0 ? 'text-decoration: line-through':''"/>
								</label>
							</span>
								</label>
								<label>
									<span>{{ lang.quantity }}:</span>
									<span>{{ product.quantity }}<span v-if="product.giftQuantity" style="color: #F01E1E"> +{{product.giftQuantity}} {{ langCart.free }}</span></span>
								</label>
								<label>
									<span>{{ lang.unitPrice }}:</span>
									<span> <CCY-rate :ccy="ccy" :price="product.unitPrice"/></span>
								</label>
<!--								<label v-show="product.setupCharge || product.mouldPrice">
&lt;!&ndash;									全定制 mold fee   半定制 Setup Fee &ndash;&gt;
									<span v-if="product.buyType == 7">{{ lang.mold }}:</span>
									<span v-else>{{ lang.setupFee }}:</span>
									<span> <CCY-rate :ccy="ccy" :price="product.setupCharge || product.mouldPrice"/></span>
									<span v-if="product.mouldNum > 1" style="color: #000000DE">(<CCY-rate :ccy="ccy" :price="(product.setupCharge || product.mouldPrice)/product.mouldNum"></CCY-rate> x {{ product.mouldNum }})</span>
								</label>-->
								<label>
									<span>{{ lang.setupFee }}:</span>
									<span>
										<CCY-rate :ccy="ccy" :price="product.setupCharge"/>
									</span>
								</label>
								<label>
									<span>{{ lang.mold }}:</span>
									<span>
								<CCY-rate :ccy="ccy" :price="product.mouldPrice"/>
								<span v-if="product.mouldNum > 1" style="color: #000000DE">(<CCY-rate :ccy="ccy" :price="product.mouldPrice/product.mouldNum"></CCY-rate> x {{ product.mouldNum }})</span>
									</span>
								</label>
								<label v-show="product.extraPrice"><span>{{ lang.extra }}:</span>
									<span><CCY-rate :ccy="ccy" :price="product.extraPrice" /></span>
								</label>
								<label v-show="product.productDiscountInfo.additionalCostsPrice"><span>{{ langCart.additionalCosts }}:</span>
									+<span><CCY-rate :ccy="ccy" :price="product.productDiscountInfo.additionalCostsPrice"/></span>
								</label>

								<label v-if="product.allocateAdditionalTariffFees && product.allocateAdditionalTariffFees>0">
									<span>{{ lang.tariffFees }}:</span>
									<span> +<CCY-rate :ccy="ccy" :price="product.allocateAdditionalTariffFees"/></span>
								</label>

								<label v-if="product.allocateTaxPrice>0">
									<span>{{gstVat}}:</span>
									<span> +
								<CCY-rate :ccy="ccy" :price="product.allocateTaxPrice"/></span>
								</label>
								<label v-show="product.allocateApoTaxPrice>0">
									<span>{{ lang.apoShippingFee }}:</span>
									+
									<CCY-rate :ccy="ccy" :price="product.allocateApoTaxPrice"/>
								</label>
								<label v-if="product.quoteDiscountPrice">
									<span>{{ langCart.discount }}:</span>
									<span>
										<label v-if="product.quoteDiscountPrice >0">
										 <div>-<CCY-rate :ccy="ccy" :price="product.quoteDiscountPrice"/></div>
										 ({{ product.discount }}% {{ lang.discount }})
										</label>
										<label v-else>
											<div>+<CCY-rate :ccy="ccy" :price="product.quoteDiscountPrice * -1"/></div>
											(+{{ product.discount * -1 }}% {{ lang.delivery }})
										</label>
									</span>
								</label>

								<label v-if="product.productDiscountInfo.firstDiscountPrice"><span>{{ lang.firstOrderCodeDisc }}:</span>
									<span>-<CCY-rate :ccy="ccy" :price="product.productDiscountInfo.firstDiscountPrice"/></span>
								</label>
								<label v-show="product.productDiscountInfo.couponAdditionalCosts"><span>{{ lang.couponAdditionalCosts }}:</span>
									<span>-<CCY-rate :ccy="ccy" :price="product.productDiscountInfo.couponAdditionalCosts"/></span>
								</label>
								<label flex v-show="product.productDiscountInfo.crowdDiscountPrice">
									<span>
										<b v-if="product.productDiscountInfo.additionalCostsPrice">$1 for a $75 Voucher</b>
										<b v-else>{{ lang.specialCode }}</b>:</span>
									-
									<CCY-rate :ccy="ccy" :price="product.productDiscountInfo.crowdDiscountPrice"/>
								</label>
								<label v-show="product.productDiscountInfo.pointPrice"><span>{{ lang.pointsDeduct }}:</span>
									<span>-<CCY-rate :ccy="ccy" :price="product.productDiscountInfo.pointPrice"/></span>
								</label>
								<label v-show="product.productDiscountInfo.voucherPrice"><span>{{ lang.giftVouchers }}:</span>
									<span>-<CCY-rate :ccy="ccy" :price="product.productDiscountInfo.voucherPrice"/></span>
								</label>
								<label v-show="product.productDiscountInfo.couponDiscountPrice"><span>{{ lang.coupon }}:</span>
									<span>-<CCY-rate :ccy="ccy" :price="product.productDiscountInfo.couponDiscountPrice"/></span>
								</label>
								<label v-show="product.productDiscountInfo.userLevelDiscountPrice">
<!--									{{ langCart.levelDiscount }}:-->
									<span v-if="userLevelId == 1">{{ langCart.userDiscount1 }}:</span>
									<span v-if="userLevelId == 4">{{ langCart.userDiscount4  }}:</span>
									<span v-if="userLevelId == 5">{{ langCart.userDiscount5  }}:</span>
									<span v-if="!userLevelId || ![1,4,5].includes(userLevelId)">{{ langCart.levelDiscount }}:</span>
									<span>-<CCY-rate :ccy="ccy" :price="product.productDiscountInfo.userLevelDiscountPrice"/></span>
								</label>
								<label v-show="product.productDiscountInfo.orderDiscountPrice"><span>{{ lang.goldVIPMember }}:</span>
									<span>-<CCY-rate :ccy="ccy" :price="product.productDiscountInfo.orderDiscountPrice"/></span>
								</label>
								<label v-show="product.productDiscountInfo.promotionPrice"><span>{{ lang.extraDiscount }}:</span>
									<span>-<CCY-rate :ccy="ccy" :price="product.productDiscountInfo.promotionPrice"/></span>
								</label>

								<!-- 促销中心优惠劵显示 -->
								<label v-for="(item,index) in product.promotionDiscountInfoList" :key="index" style="align-items: center;">
									<span>{{ item.title }}:</span>
									<span>-<CCY-rate :ccy="ccy" :price="item.discountPrice"/></span>
								</label>
								<label>
									<span>{{ langCart.promotionCenter }}:</span>
									<span>+<CCY-rate :ccy="ccy" :price="getPromTion(product.promotionDiscountInfoList)"/></span>
								</label>


								<label class="align-items-center"><span>{{ lang.total }}:</span>
									<strong> <CCY-rate :ccy="ccy" :class="{price:unPaidOidList && unPaidOidList.length >0}" :price="product.totalPrice"/></strong>
								</label>
							</div>
						</template>
					</div>
					<div class="approved-box">
						<template v-if="!product.confirm">
							<button primary @click.self="checkEmail ? checkConfirm(1) : showCheckEmail = true">
								{{ lang.approved }}
								<b class="icon-jxsht-jcsz-wh">
									<div class="tips" hidden>{{ lang.buttonTip.approved }}</div>
								</b>
							</button>
							<button primary class="modify" @click.self="checkEmail ? showModify = true : showCheckEmail = true">
								{{ lang.modify }}
								<b class="icon-jxsht-jcsz-wh">
									<div class="tips right" hidden>{{ lang.buttonTip.modify }}</div>
								</b>
							</button>
						</template>
						<!--<button primary @click.self="artworkSave" v-else>
							{{ lang.complete }}
							<b class="icon-jxsht-jcsz-wh">
								<div class="tips" hidden>{{ lang.buttonTip.confirm }}</div>
							</b>
						</button>-->
					</div>
					<img class="paidImg" :src="unPaidOidList.includes(product.oid)?'':'https://oss-static-cn.liyi.co/web/quoteManage/20231226/%E5%B7%B2%E4%BB%98%E6%AC%BE-4_2061Ya7A3Q.png'">
				</div>
			</div>

			<div flex class="dialog-box" v-show="showModify || showHistory || showCheckEmail">
				<div class="dialog">
					<b class="icon-hxsht-xp-gb" @click="showModify = showHistory = showCheckEmail = false"></b>

					<div scrollbar class="modify" v-show="showModify">
						<h3>{{ lang.leaveMessage }}</h3>

						<div class="title">{{ product.productName }}</div>
						<CustomImage class="customImage3"
									 v-if="product.picPath[artworkIndex].picLink || product.picPath[artworkIndex].url"
									 :url="product.picPath[artworkIndex].picLink || product.picPath[artworkIndex].url"
									 :show-file-name="false" :preview="false"
									 style="max-height: 118px;width: auto"></CustomImage>
						<div class="title">{{ lang.message }}:</div>
						<textarea v-model="product.message" rows="7" :placeholder="lang.messagePlaceholder"></textarea>

						<div class="title">{{ lang.upload[0] }} <span>({{ lang.upload[1] }})</span></div>
						<div class="drag">
							<div flex class="file" v-for="(i, index) in product.msgPicPath" :key="index + 200">
								<span>{{ i.imgName }}</span>
								<label>
									<b class="icon-gou"></b>
									<!--								<b class="icon-a-lrgl-dezhuanhuan" @click="msgImgRemove(index)"></b>-->
								</label>
							</div>
							<b class="icon-hxsht-xp-tp" v-show="!product.msgPicPath.length"></b>
							<p class="browse">
								<strong>{{ lang.drag[0] }}
									<label>
										<span>{{ lang.drag[1] }}</span>
										<input type="file" value="browse" @change="msgImgUpload" hidden/>
									</label>
								</strong>
							</p>

							<p pre class="tip" v-show="!product.msgPicPath.length">{{ lang.dragTip }}</p>
						</div>

						<div class="btn-box">
							<button primary @click="modifySubmit">{{ lang.submit }}</button>
						</div>
					</div>


					<div scrollbar class="history" v-show="showHistory">
						<h3>{{ lang.messageHistory }}</h3>

						<div class="title">{{ product.productName }}</div>
						<CustomImage class="customImage"
									 v-if="product.picPath[artworkIndex].picLink || product.picPath[artworkIndex].url"
									 :url="product.picPath[artworkIndex].picLink || product.picPath[artworkIndex].url"
									 :show-file-name="false" :preview="false"
									 style="width: auto; height: 8em;"></CustomImage>


						<template v-for="(i, index) in product.messageLists">
							<hr v-show="index"/>
							<div :class="$store.getters.isMobile ? 'sub-title' : 'title'">{{ i.createTime }}</div>
							<div class="message">{{ i.message }}</div>
							<a v-for="item in JSON.parse(i.picPath)" :href="item.imgLink" target="_blank">
								{{ item.imgName }}
							</a>
						</template>
					</div>


					<div class="check-email" v-show="showCheckEmail">
						<strong>{{ lang.verifyEmail }}</strong>
						<div flex class="email-box">
							<input type="text" v-model="userEmail"/> {{ email }}
						</div>
						<div class="btn-box">
							<button primary @click="checkUserEmail">{{ lang.submit }}</button>
						</div>
					</div>
				</div>
			</div>
		</div>
	</client-only>
</template>

<script>
import {getOrderDrawing, checkDrawingLinkEmail, deleteDrawings, setConfirmData, getBuyAgain,getCountryRate} from "@/api/web.js";
import {getFileSuffix, newDownFile, replacePicPrefix} from "@/utils/utils";
import { getCountryList } from "~/api/web";
import {uploadFile} from "@/utils/oss";
export default {
	async asyncData({query, store, error}) {
		try {
			if (!query.id) return location.href = "/";
			const info = await getOrderDrawing({oid: query.id});
			const data = info.data.orderInfo, userInfo = info.data.userInfo;
			if (userInfo) store.commit("setUserInfo", {
				token: userInfo.token,
				userInfo: userInfo.userInfo
			});
			let obj = store.state.currencyList.find(item=>item.code == data.currency)
			if(obj){
				store.commit('setCurrency', obj);
			}
			data.ccy = {rate: data.givingQuotation, symbol: data.currencySymbol, country: data.language?.split("-")[0]};
			//paymentStatus 1 Awaiting Payment  ,   data.orderStatus 11 Cancelled

			data.payItem = data.paymentStatus == 1 && data.orderStatus != 11 ? data : {}; // 若主订单未支付，则不会有子订单
			if (data.childOrder) data.childOrder.map(c => {
				data.taxPrice += c.taxPrice;
				if (c.paymentStatus == 1 && data.orderStatus != 11) data.payItem = c; // 主订单以及所有子订单中，只会有一个未支付
			});
            let payLater = false;
            //处理后付款
            if(data.paymentStatus==0){
                //判断是否有子订单未付款
                if(data.childOrder){
                    let find = data.childOrder.find(c => {
                        return c.paymentStatus == 1
                    });
                    payLater = !find;
                }else{
                    payLater = true;
                }
            }
			data.subTotal = data.ordersProducts.map(i => {
				i.msgPicPath = i.msgPicPath || [];
				i.picPath = i.picPath && JSON.parse(i.picPath);
				i.productParam = i.productParam && JSON.parse(i.productParam);
				return i.totalPrice;
			}).reduce((a, b) => a + b);
			return {
                ...data,
                payLater:payLater
            };
		} catch (e) {
			return error({statusCode: 404});
		}
	},
	data() {
		return {
            payLater:false,
			proofIndex: 0,
			productNum: 0,
			productIndex: 0,
			artworkIndex: 0,
			userEmail: "",
			checkEmail: true,
			showCheckEmail: false,
			showModify: false,
			showHistory: false,
			showViewMore: false,
			showMenu: !this.$store.getters.isMobile,
			countryList:[]
		};
	},
	computed: {
		gstVat(){
			let item = this.countryList.find(e=>e.id == this.shippingCountryId)
			return item && item.taxName?item.taxName: 'Tax Price'
		},
		headerH(){
			let ele = document.getElementById('modalHeader')
			return ele.scrollHeight
		},
		checkoutLink() {
			if (this.currencyItem) {
				return "/checkout?type=artwork&orderid=" + this.oid + "&rate=" + this.currencyItem.rate + "&symbol=" + this.currencyItem.symbol;
			} else {
				return "/checkout?type=artwork&orderid=" + this.oid;
			}
		},
		lang() {
			return this.$store.getters.lang.confirm || {};
		},
		langCart() {
			return this.$store.getters.lang.cart || {};
		},
		currencyItem() {
			let obj = null;
			//已付款的不能改 ，           子单不能改(==主单已付款)   》 有子单的不能改
			if (this.unPaidOidList.length > 0 && !this.childOrder.length > 0) {
				obj = {
					rate: this.$store.state.currency.exchangeRate,
					symbol: this.$store.state.currency.symbol
				};
			}
			return obj;
		},
		productStatus() {
			// confirm: 0 待确认 1 通过 2 不通过
			if (this.ordersProducts.find(i => i.confirm == 0)) return 0;
			else if (this.ordersProducts.find(i => i.confirm == 2)) return 1;
			else return 2;
		},
		product() {
			// return this.ordersProducts.find(i => i.indexNum == this.productNum) || this.ordersProducts[0] || {}
			let obj = this.ordersProducts.find(i => i.indexNum == this.productNum) || this.ordersProducts[0] || {};

			// // 存在未付款或者未确认的 只展示未付款或者未确认里面的
			if ((this.unPaidOidList && this.unPaidOidList.length > 0) || !this.allConfirmed) {
				obj = this.newArr[this.proofIndex] || this.newArr[0] || {};
			}
            obj.productDiscountInfo = obj.productDiscountInfo || {};
			return obj;
		},
		allConfirmed() {
			return !this.ordersProducts.find(i => i.confirm !== 1);
		},
		isAllOperated(){
			// 无未操作的
			return !this.ordersProducts.find(i => i.confirm == 0)
		},
		newArr() {
			return this.ordersProducts.filter(obj => obj.confirm !== 1 || this.unPaidOidList.find(ele => ele == obj.oid));
		},
		taxFee(){
			let num =(this.product.totalPrice
				+ (this.product.productDiscountInfo?.additionalCostsPrice || 0) - this.product.productDiscountInfo.firstDiscountPrice
				- this.product.productDiscountInfo.crowdDiscountPrice - this.product.productDiscountInfo.pointPrice
				- this.product.productDiscountInfo.voucherPrice - this.product.productDiscountInfo.couponDiscountPrice
				- this.product.productDiscountInfo.userLevelDiscountPrice - this.product.productDiscountInfo.orderDiscountPrice
				- this.product.productDiscountInfo.promotionPrice)*(Number(this.stateTax?this.stateTax:0))
			return num
		}
	},
	watch: {
		currencyItem(val) {
			// 已支付的不能切换货币汇率
			if (val && this.unPaidOidList && this.unPaidOidList.length > 0) this.ccy = val;
		},
		productNum(val) {
			if (!this.ordersProducts[val]) return this.productNum = 0;
			this.artworkIndex = 0;

			this.checkShowViewMore();
			this.productIndex = this.ordersProducts.findIndex(i => this.productNum == i.indexNum);

			if (this.$store.getters.isMobile) this.showMenu = false;
			else this.$refs.productList.scrollTo({
				top: (this.productIndex - 1) * window.innerWidth * 0.06,
				behavior: "smooth"
			});
		},
		artworkIndex(val) {
			if (val < 0) this.artworkIndex = this.product.picPath.length - 1;
			if (val >= this.product.picPath.length) this.artworkIndex = 0;

			this.$refs.artworkList.scrollTo({
				left: (this.artworkIndex - 1) * window.innerHeight * 0.1,
				behavior: "smooth"
			});
		},
	},
	created() {
		// this.userEmail = this.$store.state.userInfo.email ? this.$store.state.userInfo.email.split('@')[0] : this.$cookies.get("artwork_email");
		// this.checkUserEmail();
		this.checkConfirm(null);
	},
	async mounted() {

		this.getCountry();
		if(!this.$store.getters.isMobile){
			// console.log(this.$el,788877);
			window.onload = function() {
				window.scrollTo(0, this.headerH || 130);
			};
		}
		console.log('productStatus',this.productStatus);
		// console.log(this.$router, this.$store.state.language.language);
		await this.$nextTick();
		// 拖拽上传文件
		let drag = document.querySelector(".drag");
		if (drag) drag.addEventListener("dragover", e => e.preventDefault());
		if (drag) drag.addEventListener("drop", e => {
			e.preventDefault();
			this.msgImgUpload(e.dataTransfer.files);
		});

		this.productNum = this.ordersProducts.find(i => !i.confirm) ? this.ordersProducts.find(i => !i.confirm).indexNum : 0;
		// this.$store.commit('getItemLang', this.$store.state.language.language)
		// this.checkShowViewMore();
	},
	methods: {
		getPromTion(promotionDiscountInfoList){
			let total = 0;
			promotionDiscountInfoList && promotionDiscountInfoList.forEach((item) => {
				if (item.additionalPrice) {
					total += item.additionalPrice;
				}
			});
			return total;
		},
		getCountry() {
			return new Promise(resolve => {
				getCountryList().then(res => {
					res.data.forEach(e=>e.code = e.code.toString())
					this.countryList = res.data;
					resolve();
				});
			});
		},
		checkConfirm(status) {
			if (status) this.ordersProducts[this.productIndex].confirm = status;
			if (status!==null) this.artworkSave();
			// if (status == 1) this.artworkSave();
			// 排序展示：先未确认，再已确认，后不通过
			this.ordersProducts = [
				...this.ordersProducts.filter(i => !i.confirm).sort((a, b) => a.indexNum - b.indexNum),
				...this.ordersProducts.filter(i => i.confirm == 2).sort((a, b) => a.indexNum - b.indexNum),
				...this.ordersProducts.filter(i => i.confirm == 1).sort((a, b) => a.indexNum - b.indexNum)
			];
		},
		checkUserEmail() {
			checkDrawingLinkEmail({oid: this.oid, email: this.userEmail + this.email}).then(res => {
				this.checkEmail = res.data;
				this.showCheckEmail = !res.data;
				if (this.userEmail && !res.data) this.$toast.error(res.message);
			});
			this.$cookies.set("artwork_email", this.userEmail);
		},
		checkShowViewMore() {
			if (this.$refs.viewBox) this.$nextTick(() => this.showViewMore = this.$refs.viewBox.scrollHeight > window.innerHeight * 0.35);
		},
		viewMore() {
			this.showViewMore = false;
			this.$refs.viewBox.scrollTo({top: this.$refs.viewBox.scrollHeight, behavior: "smooth"});
		},

		// 判断文件是否是图片或gif,pdf
		getFileTypeIsPic(url) {
			if (url) {
				let type = getFileSuffix(url).toLowerCase();
				if (type === ".jpg" || type === ".jpeg" || type === ".png" || type === ".gif" || type === ".bmp") {
					return true;
				}
			}
			return false;
		},

		// 判断文件是否是pdf
		getFileIsPdf(url) {
			if (url) {
				let type = getFileSuffix(url).toLowerCase();
				if (type === ".pdf") {
					return true;
				}
			}
			return false;
		},

		artworkView() {
			this.$viewerApi({
				images: this.product.picPath,
				options: {
					url: "picLink",
					initialViewIndex: this.artworkIndex
				}
			});
		},
		artworkDownload() {
			let url = this.product.picPath[this.artworkIndex].picLink || this.product.picPath[this.artworkIndex].url;
			if (this.getFileIsPdf(url)) {
				this.downloadPdf(url);
				return;
			}
			window.open(replacePicPrefix(url));
		},
		downloadPdf(url) {
			// downFile(img,'test')
			let fileName = this.getFileName(url);
			newDownFile(url, fileName);
		},
		getFileName(url) {
			// https://static-oss.gs-souvenir.com/web/public/picStore/20230826/PDF_20230826XaBMrT.png
			let lastIndex = url.lastIndexOf("/");
			url = url.substring(lastIndex + 1, url.length); // PDF_20230826XaBMrT.png
			let s = url.lastIndexOf("_");
			let e = url.lastIndexOf(".");
			let n = url.substring(s, e); // _20230826XaBMrT
			let name = url.replace(n, "");
			return name;
		},
		artworkRemove() {
			this.$toast.show(this.lang.removeTip, {
				containerClass: "toast-action-box",
				className: "toast-action artwork",
				duration: null,
				action: [
					{
						text: this.lang.remove,
						onClick: (e, toastObject) => {
							deleteDrawings({
								oid: this.product.oid,
								id: this.product.id,
							}).then(() => {
								getOrderDrawing({
									oid: this.$route.query.id
								}).then(({data}) => {
									data.orderInfo.ordersProducts.map(i => {
										i.msgPicPath = i.msgPicPath || [];
										i.picPath = i.picPath && JSON.parse(i.picPath);
										i.productParam = i.productParam && JSON.parse(i.productParam);
									});
									// 刷新左侧 避免重复删除问题
									this.ordersProducts = data.orderInfo.ordersProducts;
									Object.assign(this, data);
									this.productNum += 1;
									toastObject.goAway(0);
								});
							});
						}
					},
					{
						text: this.lang.cancelText,
						onClick: (e, toastObject) => toastObject.goAway(0)
					},
				],
			});
		},

		msgImgUpload(files) {
			let fileList = files.target ? files.target.files : files;
			for (let key in fileList) {
				if (fileList[key].type && fileList[key].name) uploadFile(fileList[key]).then(url => {
					this.ordersProducts[this.productIndex].msgPicPath.push({
						imgLink: url,
						imgName: fileList[key].name
					});
				});
			}
			// this.$forceUpdate();
		},
		msgImgRemove(i) {
			this.ordersProducts[this.productIndex].msgPicPath = this.product.msgPicPath.filter((item, index) => {
				if (index != i) return item;
			});
		},

		modifySubmit() {
			if (!this.product.message) return this.$toast.error(this.lang.messageTip);
			this.checkConfirm(2);
			// this.checkShowViewMore();
			this.showModify = false;
			// if (this.ordersProducts.length == 1) this.artworkSave();

			// if (!this.product.message) return this.$toast.error(this.lang.messageTip);
			// this.checkConfirm(2);
			// this.checkShowViewMore();
			// this.showModify = false;
			// if (this.ordersProducts.length == 1) this.artworkSave();
		},

		// confirm: 0 待确认 1 通过 2 不通过
		async artworkSave() {
			console.log("this.productStatus", this.productStatus);
			// if (this.checkEmail) {
			// if (this.productStatus == 0) this.productNum = this.ordersProducts.find(i => i.confirm == 0).indexNum;
			if (this.ordersProducts.find(i => i.confirm == 0))this.productNum = this.ordersProducts.find(i => i.confirm == 0).indexNum;
			setConfirmData({
				oid: this.oid,
				email: this.email,
				orderStatus: this.productStatus,
				ordersProducts: this.ordersProducts.map(i => {
					return {
						id: i.id,
						oid: i.oid,
						confirm: i.confirm,
						messageLists: i.message ? [{
							message: i.message,
							picPath: JSON.stringify(i.msgPicPath)
						}] : null
					};
				}).filter(ele=>ele.id == this.product.id)
			}).then(res => {
				this.orderStatus = res.data;
				if (this.orderStatus == 5) {
					// orderStatus 5  是Artwork Confirmed
					let url = "/checkout?type=artwork&orderid=" + this.oid;
					if (this.currencyItem) {
						url = "/checkout?type=artwork&orderid=" + this.oid + "&rate=" + this.currencyItem.rate + "&symbol=" + this.currencyItem.symbol;
					}
					if (this.payItem.id) location.href = url;
					else if (this.$store.state.token) location.href = "/user/account/orders/summary?orderId=" + this.oid;
					else this.$store.commit("setLogin", "login");
				}
			})

			// if (this.productStatus == 0) this.productNum = this.ordersProducts.find(i => i.confirm == 0).indexNum;
			// else if (this.orderStatus == 5) {
			// 	// orderStatus 5  是Artwork Confirmed
			// 	let url = "/checkout?type=artwork&orderid=" + this.oid;
			// 	if (this.currencyItem) {
			// 		url = "/checkout?type=artwork&orderid=" + this.oid + "&rate=" + this.currencyItem.rate + "&symbol=" + this.currencyItem.symbol;
			// 	}
			// 	if (this.payItem.id) location.href = url;
			// 	else if (this.$store.state.token) location.href = "/user/account/orders/summary?orderId=" + this.oid;
			// 	else this.$store.commit("setLogin", "login");
			// }
			// else this.$toast.show(this.lang.completeTip[0] + this.ordersProducts.filter(i => i.confirm == 1).length + this.lang.completeTip[1] + this.ordersProducts.filter(i => i.confirm == 2).length + this.lang.completeTip[2], {
			// 	containerClass: "toast-action-box",
			// 	className: "toast-action artwork",
			// 	duration: null,
			// 	action: [
			// 		{
			// 			text: this.lang.confirmText,
			// 			onClick: (e, toastObject) => setConfirmData({
			// 				oid: this.oid,
			// 				email: this.email,
			// 				orderStatus: this.productStatus,
			// 				ordersProducts: this.ordersProducts.map(i => {
			// 					return {
			// 						id: i.id,
			// 						oid: i.oid,
			// 						confirm: i.confirm,
			// 						messageLists: i.message ? [{
			// 							message: i.message,
			// 							picPath: JSON.stringify(i.msgPicPath)
			// 						}] : null
			// 					};
			// 				})
			// 			}).then(res => {
			// 				if (this.productStatus == 1) this.orderStatus = res.data;
			// 				else if (this.productStatus == 2) {
			// 					let url = "/checkout?type=artwork&orderid=" + this.oid;
			// 					if (this.currencyItem) {
			// 						url = "/checkout?type=artwork&orderid=" + this.oid + "&rate=" + this.currencyItem.rate + "&symbol=" + this.currencyItem.symbol;
			// 					}
			// 					if (this.payItem.id) location.href = url;
			// 					else if (this.$store.state.token) location.href = "/user/account/orders/summary?orderId=" + this.oid;
			// 					else this.$store.commit("setLogin", "login");
			// 				}
			// 				toastObject.goAway(0);
			// 			})
			// 		},
			// 		{
			// 			text: this.lang.backText,
			// 			onClick: (e, toastObject) => toastObject.goAway(0)
			// 		},
			// 	],
			// });
		},

		//点击再次购买
		async buyAgain() {
			let res = await getBuyAgain({"oid": this.oid});
			if (res.data) {
				this.$toast.show(this.lang.buyAgainPop[0] + this.oid + this.lang.buyAgainPop[1] + this.payTime.split(" ")[0] + this.lang.buyAgainPop[2], {
					containerClass: "toast-action-box",
					className: "toast-action artwork",
					duration: null,
					action: [
						{
							text: this.lang.yes,
							onClick: (e, toastObject) => {
								// toastObject.goAway(0)
								let url = "/checkout?type=artwork&orderid=" + res.data + '&buyAgain=' + true;
								if (this.currencyItem) {
									url = "/checkout?type=artwork&orderid=" + res.data + "&rate=" + this.currencyItem.rate + "&symbol=" + this.currencyItem.symbol + '&buyAgain=' + true;
								}
								location.href = url;
							}
						},
						{
							text: this.lang.no,
							onClick: (e, toastObject) => toastObject.goAway(0), // 关闭弹窗
						},
					],
				});
			} else {
				this.$toast.show(this.lang.buyAgainTip, {
					containerClass: "toast-action-box",
					className: "toast-action artwork",
					duration: null,
					action: [
						{
							text: this.lang.confirmBtn,
							onClick: (e, toastObject) => {
								toastObject.goAway(0);
							}
						},

					],
				});
				// this.$alert(this.lang.buyAgainTip, {
				//     confirmButtonText: this.lang.confirmBtn,
				//     callback: action => {
				//         this.$message({
				//             type: 'info',
				//             message: `action: ${action}`
				//         });
				//     }
				// });
			}
		}
	}
};
</script>

<style lang="scss" scoped>
.container-confirm {
	margin: 0 auto;
	max-width: 1630px;
	font-size: 0.875em;
	padding: 0 0.75vmax;
	--color-primary: #0066CC;

	label {
		display: inline;
	}

	.step-box {
		padding: 10px;
		text-align: center;
		justify-content: center;

		> p {
			width: 10vmax;
			color: #999999;
			position: relative;

			&:not(:first-child)::before {
				content: "";
				width: 100%;
				border-top: 0.25em solid #E6E6E6;
				position: absolute;
				left: -50%;
				top: 0.9em;
			}

			strong {
				width: 2em;
				height: 2em;
				display: block;
				margin: 0 auto 8px;
				line-height: 1.8em;
				border-radius: 50%;
				background: white;
				border: 0.15em solid #E6E6E6;
				position: relative;
				z-index: 2;
			}

			&.select {
				color: $text-primary;

				&::before {
					border-color: $color-primary;
				}

				strong {
					color: white;
					background: $color-primary;
					border-color: $color-primary;
				}
			}
		}
	}

	button {
		transform: none;
		padding-left: 1em;
		padding-right: 1em;
		border-radius: 5px;
		position: relative;
		z-index: 3;
		&:hover.tips{
			display: block;
			background: white;
		}

		.icon-jxsht-jcsz-wh {
			margin-left: 10px;
		}
	}

	.icon-jxsht-jcsz-wh {
		&:hover .tips {
			display: block;
			text-align: left;
			padding: 8px 1.5em;
			border: #E6E7EB;
			color: $text-primary;
			background: white;
			box-shadow: 0 0 10px -5px $bg-mask;
			position: absolute;
			right: -3vw;
			left: -3vw;
			top: -5em;
			z-index: 99;

			&.right{
				top: -6em;
			}

			&::before {
				content: "";
				width: 10px;
				height: 10px;
				background: white;
				box-shadow: -3px -3px 6px -4px $bg-mask;
				transform: scale(1, 1.3) rotate(45deg);
				position: absolute;
				left: calc(50% - 5px);
				bottom: -5px;
			}
		}
	}

	.order-box {
		padding: 0.8vw;
		line-height: 2vmax;
		background: #F5F7F9;
		justify-content: center;
		align-items: center;

		.check-out{
			background: linear-gradient(180deg, #ffffff 0%, #f9a128 0%, #ff6c00 100%);
			color: #fff;

			//&.disabled {
			//	background: #ccc;
			//	pointer-events: none;
			//	cursor: not-allowed;
			//}
		}

		> div {
			padding: 0 5vw;
			font-size: 1.43em;

			> label {
				font-size: 1.2em;
				font-weight: bold;
				margin-left: 0.5em;
			}

			&:not(:first-child) {
				border-left: 1px solid #E5E7EA;
			}
		}

		.number {
			color: $color-primary;
		}

		.icon-jxsht-jcsz-wh {
			margin-left: 8px;
			vertical-align: top;
			font-size: 0.834vmax;
			position: relative;

			&::before {
				color: $color-primary;
			}

			.tips {
				width: max-content;
				border-radius: 6px;
				top: calc(25px + 1vw);
				right: -15em;
				left: auto;
				z-index: 5;

				&::before {
					left: auto;
					right: 15.15em;
				}

				> div {
					line-height: 16px;
					margin-top: 0.5vmax;
					justify-content: space-between;

					> span {
						flex: 1;
						margin-right: 3em;
					}

					&.final {
						margin: 1vmax 0 1vmax 3em;
						align-items: baseline;
						justify-content: right;

						strong {
							font-size: calc(1em + 2px);
						}

						label {
							padding-left: 8px;
						}

						[red] {
							font-weight: bold;
							font-size: calc(1em + 8px);
						}
					}
				}
			}
		}
	}

	.list-box > div {
		position: relative;

		//img,
		//video {
		//    width: 6.67vw;
		//    height: 4.69vw;
		//    border: 2px solid #F5F5F5;
		//    border-radius: 0px 1vh 1vh 1vh;
		//}
		.customImage::v-deep {
			height: unset;

			img,
			video {
				width: 6.67vw;
				height: 4.69vw;
				border: 2px solid #F5F5F5;
				border-radius: 0px 1vh 1vh 1vh;
				object-fit: cover;
			}
		}

		&.select {
			background: white;
			border-radius: 1vh 0px 0 1vh;

			.proof {
				color: white;
				width: fit-content;
				background: $color-primary;
				border-radius: 1vh 0.3em 0 0;
				position: relative;

				&::before {
					content: "";
					border-style: solid;
					border-width: 2em 0.7em 0 0;
					border-color: transparent #fff transparent transparent;
					position: absolute;
					right: -0.22em;
					bottom: 0;
				}
			}

			.customImage::v-deep {
				img,
				video {
					border-color: $color-primary;
					box-shadow: 0px 2px 5px 0px rgba(0, 0, 0, 0.4);
				}
			}


		}
	}

	.main {
		.menu {
			padding-left: 0.4vmax;
			background: #F5F7F9;

			.index {
				font-size: 1.143em;
				padding: 0 1vmax 5px;

				span {
					color: $color-primary;
				}
			}

			.list-box {
				padding-bottom: 3vmax;
				flex-direction: column;
				height: calc(100vh - 260px);

				> div {
					padding: 0.5vmax 1.2vmax 0.95vmax 0.7vmax;

					div {
						padding: 0.3em 0.7em 0.2em 0.6em;
					}

					b {
						position: absolute;

						&.icon-gou,
						&.icon-edit {
							width: 16px;
							line-height: 16px;
							text-align: center;
							border-radius: 4px;
							background: #ADADAD;
							right: 1.2vmax;

							&::before {
								font-size: 10px;
								color: white;
								transform: scale(0.6);
							}
						}

						&.icon-gou {
							background: $color-primary;
						}

						&.icon-Approved,
						&.icon-Modify {
							font-size: 2.6em;
							color: #B2B2B2;
							bottom: 0.6vmax;
							right: 1.6vmax;
						}

						&.icon-Approved {
							color: $color-primary;
						}
					}
				}
			}
		}

		.product-box {
			flex: 2;
			padding-left: 3vmax;
			padding-bottom: 2vmax;
			max-width: calc(50% - 1.5vmax);

			.name {
				height: 4em;

				strong {
					font-size: 1.143em;
				}

				b {
					margin: 0 0.2em;
					font-size: 2.5em;
					color: #B2B2B2;

					&.icon-Approved {
						color: $color-primary;
					}
				}
			}

			.product, .product .customImage2 {
				height: 26vmax;
				padding: 1.5vmax 0;
				justify-content: center;
				border-top: 1px solid #EBEBEB;
				position: relative;

				img,
				video {
					width: auto;
					height: 100%;
					object-fit: contain;
					max-width: calc(100% - 4em);
				}

				> b {
					width: 2em;
					height: 2em;
					font-size: 1.5em;
					line-height: 2em;
					color: white;
					font-weight: bold;
					text-align: center;
					border-radius: 50%;
					background: rgba($color: black, $alpha: 0.4);
					position: absolute;
					z-index: 5;

					&.icon-left {
						left: 0;
					}

					&.icon-right {
						right: 0;
					}
				}
			}

			.customImage2 {
				max-width: calc(100% - 4em);
			}

			.system {
				justify-content: center;

				b {
					margin: 0 2em;
					line-height: 2em;

					&::before {
						font-size: 1.8em;
						margin-right: 0.2em;
					}
				}
			}

			.list-box {
				margin: 0 auto;
				max-width: 100%;
				width: fit-content;
				padding: 1vmax 0 0.3vw;

				> div {
					&:not(:first-child) {
						margin-left: 2vmax;
					}

					img,
					video {
						border-radius: 1vh;
					}

					p {
						color: white;
						text-align: center;
						border-radius: 0 0 0.4vmax 0.4vmax;
						background: rgba($color: black, $alpha: 0.4);
						position: absolute;
						bottom: 0.11em;
						right: 0.11em;
						left: 0.11em;
					}
				}
			}

			.point-box {
				justify-content: center;

				div {
					width: 0.6em;
					height: 0.6em;
					margin: 10px 0.3em;
					border-radius: 50%;
					background: #E6E6E6;

					&.select {
						background: #ADADAD;
					}
				}
			}
		}

		.info-box {
			position: relative;
			flex: 1;
			max-width: calc(50% - 2em);
			margin-top: 4em;
			padding-left: 3vmax;
			border-top: 1px solid #EBEBEB;

			.info {
				width: 100%;
				position: relative;
				max-height: 65vh;
				z-index: 2;
				.view-more {
					line-height: 2.5em;
					background: white;
					position: absolute;
					top: calc(35vh - 2em);
					//left: 3vmax;
					right: 0;
					width: 100%;
					text-align: center;

					strong {
						color: $color-primary;
					}

					b {
						color: #B0B2B5;
						transform: scale(0.5);
					}
				}

				.total {
				}

				> div {
					padding: 1vmax 0;

					> strong {
						display: block;
						padding-bottom: 1em;
					}

					> label {
						display: flex;
						margin-bottom: 7px;

						> span {
							min-width: 10em;
							//color: #858585;
							margin-right: 0.5em;
						}

						.price {
							color: $color-red;
							font-weight: bold;
							font-size: calc(1em + 4px);
						}
					}

					button {
						margin-bottom: 10px;

						&[primary] {
							margin-top: 1vmax;
						}

						b {
							margin-left: 5px;
						}
					}
				}

				.details {
					//max-height: 35vh;

					> label {
						> span {
							&:first-child {
								width: 45%;
								text-align: right;
								color: #858585;
								margin-right: 20px;
							}

							&:last-child {
								width: 45%;
								text-align: left;
							}
						}
					}
				}

				.message {
					border-top: 1px solid #E6E7EB;

					> strong {
						display: flex;
						justify-content: space-between;

						b {
							margin-right: 1.2em;
							color: $color-primary;

							&::before {
								font-size: 1.2em;
								margin-right: 5px;
							}
						}
					}

					> label {

						span {
							width: 100%;
						}

						a {
							width: 26em;

							&:hover {
								color: $color-primary;
							}
						}

						b {
							color: #999999;

							&.icon-gou {
								margin-right: 1em;
								color: $color-green;
							}
						}
					}
				}

				.total {
					padding-top: 1.25em;
					border-top: 1px solid #E6E7EB;

				}

				.modifying {
					border-bottom: 1px solid #e6e7eb;
					.contact {
						color: #999;
						margin: 1.5em 0;
					}

					b.icon-a-icon-web3zhuanhuan {
						padding: 1em;
						color: #999;
						border-radius: 0.5em;
						background: #F7F7F7;

						&::before {
							margin-right: 0.5em;
							color: $color-green;
						}
					}

					.buy-now {
						width: 70%;
						display: block;
						margin: 3em auto;
						border-radius: 10px;
					}
				}
			}

			.approved-box {
				display: flex;
				justify-content: center;
				padding-bottom: 1em;

				.modify {
					margin-left: 2em;
				}
			}

			.approved-box .modify {
				color: white;
				margin-left: 10px;
				background: #333;
			}

			.paidImg{
				position: absolute;
				max-width: 460px;
				top: 50%;
				transform: translateY(-50%);
				z-index: -1;
			}
		}
	}
	.dialog-box {
		height: 100vh;
		align-items: center;
		background: $bg-mask;
		justify-content: center;
		position: fixed;
		bottom: 0;
		right: 0;
		left: 0;
		top: 0;
		z-index: 1000;

		.dialog {
			border-radius: 10px;
			background: white;
			position: relative;

			> div > *:not(h3) {
				margin-left: 20px;
				margin-right: 20px;
			}

			b.icon-hxsht-xp-gb {
				width: 2.4em;
				font-size: 1em;
				line-height: 2.4em;
				text-align: center;
				color: #666666;
				border-radius: 50%;
				background: white;
				position: absolute;
				right: -1.2em;
				top: -1.2em;
			}

			h3 {
				text-align: center;
				padding: 18px 0 12px;
				border-bottom: 1px solid #E6E6E6;
			}

			.title,
			.sub-title {
				margin-top: 15px;
				margin-bottom: 10px;
			}

			.title {
				font-size: 1.143em;

				span {
					font-size: calc(1em - 2px);
				}
			}

			.now-pic {
				width: auto;
				height: 8em;
				max-width: 100%;
				margin-bottom: 10px;
			}

			.btn-box {
				margin-top: 20px;
				text-align: center;
			}

			[scrollbar] {
				max-height: 90vh;
				padding-bottom: 30px;
			}

			.check-email {
				padding: 20px 10px;

				.email-box {
					margin-top: 20px;
					align-items: center;

					input {
						min-width: 17em;
						margin-right: 7px;
						padding: 10px 10px;
						background: #EBEBEB;
					}
				}
			}

			.modify {
				width: 28vmax;

				textarea {
					padding: 0.6em;
					border-radius: 5px;
					background: #F5F5F5;
					width: calc(100% - 40px);
				}

				.drag {
					padding-top: 5px;
					border-radius: 5px;
					text-align: center;
					border: 1px dashed #CCCCCC;

					.file {
						padding: 5px 10px;
						justify-content: space-between;

						b {
							color: #999999;

							&.icon-gou {
								color: $color-green;
							}
						}
					}

					b.icon-hxsht-xp-tp {
						font-size: 50px;
						color: #E0E0E0;
					}

					.browse {
						margin: 10px 0;

						span {
							color: $color-primary;
						}
					}

					.tip {
						color: #999999;
						margin-bottom: 20px;
						font-size: calc(1em - 4px);
					}
				}
			}

			.history {
				width: 28vmax;

				.message {
					margin: 0 2em 10px;
				}

				a {
					display: block;
					line-height: 2em;
					color: #858585;

					&:hover {
						color: $color-primary;
					}
				}

				hr {
					margin-top: 15px;
				}
			}
		}

	}
}


@media screen and (max-width: $mb-width) {
	.container-confirm {
		font-size: 3.2vw;
		background: #f3f3f3;

		button .icon-jxsht-jcsz-wh:hover .tips {
			top: auto;
			bottom: 4em;

			&::before {
				top: auto;
				bottom: -0.375em;
				box-shadow: 3px 3px 6px -4px $bg-mask;
			}
		}

		.step-box > p {
			width: 33vw;

			strong {
				font-weight: normal;
			}
		}

		.order-box {
			background: white;
			padding: 1em 0 1.5em;
			border-radius: 5px 5px 0 0;

			> div {
				padding: 0 3vw;
				font-size: 3.2vw;
				text-align: center;
				flex-direction: column;

				> label {
					margin-left: 0;
					font-size: 3.2vw;
					margin-top: 0.5em;
					word-break: break-all;
				}

				.ordersProducts {
					span {
						font-size: 15px;
					}

					b {
						transform: scale(0.4);
					}
				}
			}
		}

		.list-box > div {

			.customImage::v-deep {
				height: unset;

				img,
				video {
					padding: 0;
					width: 20vw;
					height: 14vw;
					border-width: 1px;
				}
			}

			//img,
			//video {
			//    padding: 0;
			//    width: 20vw;
			//    height: 14vw;
			//    border-width: 1px;
			//}
		}

		.main {
			background: white;
			flex-direction: column;

			.menu {
				height: 300vh;
				padding: 0 0.75vmax;
				background: $bg-mask;
				position: absolute;
				z-index: 9;
				right: 0;
				left: 0;

				.index {
					padding: 0.5em 1em;
					background: #F5F7F9;
				}

				.list-box {
					height: auto;
					flex-wrap: wrap;
					flex-direction: row;
					background: #F5F7F9;

					> div {
						background: none;
						padding: 0 0 0.5em calc((100vw - 1.5vmax - 88vw) / 5);

						b {
							right: 0;
						}

						div::before {
							border-right-color: #F5F7F9;
						}
					}
				}
			}

			.product-box {
				display: flex;
				margin: 0 10px;
				max-width: 100%;
				padding: 10px 0 20px;
				flex-direction: column;
				border-top: 1px solid #EBEBEB;
				border-bottom: 1px solid #EBEBEB;

				.name {
					padding: 0 1.5vmax;
					flex-direction: column;
					align-items: flex-start;
					position: relative;

					label {
						margin: 0;
					}

					b {
						position: absolute;
						right: 20%;
						top: -2px;
					}
				}

				.product {
					border: none;

					img,
					video {
						padding: 0;
						position: static;
					}

					> b {
						display: none;
					}
				}

				.system {
					order: 1;
					margin-top: 20px;
				}

				.list-box {
					margin-top: 20px;
					justify-content: initial;
				}
			}

			.info-box {
				border: none;
				max-width: 100%;
				padding: 0 20px 30px;
				.info {
					max-height: unset;
					.total {
						border-top-style: dashed;
					}
				}

				.approved-box {
					display: flex;
					text-align: center;
					background: white;
					position: fixed;
					z-index: 8;
					bottom: -2px;
					right: 0;
					left: 0;

					button {
						min-width: 42.6vw;
					}
				}
				.paidImg{
					left: 50%;
					transform: translateX(-50%);
				}
			}

		}

		.dialog-box .dialog > div {
			width: 92vw !important;
		}
	}
}
</style>
