[{"id": "coastGuardChallengeCoins_03", "theme": 20, "name": "Card", "column": 1, "style": {"background-color": "rgb(23, 23, 25)", "color": "#e5e5e5"}, "boxStyle": {"display": "grid", "grid-template-columns": "40% 1fr", "column-gap": "5%", "row-gap": "0.3125em"}, "cardStyle": {"overflow": "visible"}, "cardImgStyle": {"max-width": "100%", "margin-left": "auto", "margin-right": "auto"}, "cardContentStyle": {"row-gap": "0.3125em"}, "cardSubTitleStyle": {"font-size": "1.125em", "order": 1}, "cardPriceListStyle": {"display": "grid", "grid-template-columns": "repeat(3, 1fr)", "gap": "0 0.5em", "align-items": "center", "font-size": "1.125em", "order": 3}, "cardTextStyle": {"font-size": "1.125em", "order": 4, "opacity": 1}, "cardBtnBoxStyle": {"margin-top": "0.625em", "order": 5}, "outer": [{}], "list": [{"style": {"grid-column": "2 / 3"}, "titleStyle": {"text-align": "left"}, "title": {"value": "Coast Guard Challenge Coins", "tagName": "h2"}}, {"style": {"grid-column": "1 / 2", "grid-row": "1 / 3"}, "img": {"value": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20220904/ueditor/16/custom-coast-guard-challenge-coins.png", "alt": "Police Department Challenge Coins"}}, {"titleStyle": {"font-size": "1.5em", "order": 2}, "title": {"value": "Main Benefits"}, "subTitle": {"value": "There are more than 40,000 Coast Guard personnel in the United States. Awarding US Coast Guard Challenge Coins to these service members is a way of showing recognition for their efforts."}, "priceList": [{"value": "● Glory symbol"}, {"value": "● Support for military"}, {"value": "● Build long-term bonds"}, {"value": "● Memorable"}, {"value": "● Collectibles"}, {"value": "●  Show military status"}], "text": {"value": "GS-JJ is a professional challenge coin maker in the United States, we use excellent raw materials to make the high-quality USCG challenge coin. Comprehensive service makes it easy for you to obtain personalized coins."}, "button": {"value": "Free Quote", "alt": "Free Quote For Coast Guard Challenge Coins", "icon": "icon-GSA", "style": {"padding": "0.12em 0.12em 0.12em 2em", "height": "unset", "border-radius": "0.333333em"}, "iconStyle": {"background": "#fff", "padding": "0 1em", "font-size": "1.5em", "border-radius": "0 0.2em 0.2em 0"}, "method": {"modal": "modalQuoteDialog", "quoteUrl": "/quote/coins-quote"}}}]}, {"id": "coastGuardChallengeCoins_01", "theme": 20, "name": "Card", "column": 3, "style": {"background-color": "#f3f4f5"}, "titleStyle": {"font-size": "2.25em", "line-height": "normal", "margin-bottom": "0.555556em"}, "subTitleStyle": {"margin-top": "1em", "margin-bottom": "1em", "font-size": "1em"}, "boxStyle": {"gap": 0}, "cardStyle": {"width": "33.333333%", "flex-direction": "column", "align-items": "flex-start", "padding-top": "1.25em", "padding-bottom": "1.5625em", "padding-left": "5.625em", "background-image": "url(https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20220628/ueditor/16/step-2.png)", "background-size": "100%", "background-repeat": "no-repeat"}, "cardImgStyle": {"width": "unset", "height": "10.6875em"}, "cardContentStyle": {"margin-top": "1.25em", "flex-direction": "row", "justify-content": "center", "gap": 0, "position": "relative"}, "cardTitleStyle": {"order": 1, "font-size": "1.5em", "line-height": "1.666666em"}, "cardTextStyle": {"width": "2.1875em", "height": "2.5em", "background-color": "#508AC3", "transform": "rotate(-30deg)", "border-radius": "0.8125em", "position": "absolute", "top": 0, "left": 0, "z-index": 0, "opacity": 1}, "cardPriceStyle": {"width": "1.458333em", "margin-right": "0.416666em", "line-height": "1.666666em", "text-align": "center", "color": "#ffffff", "font-size": "1.5em", "font-weight": "bold", "opacity": 1, "z-index": 1}, "boxBtnStyle": {"margin-top": "1.25em"}, "btnStyle": {"width": "13.75em", "padding": 0, "line-height": "2.25em", "text-align": "center", "background-color": "#0066cc", "border-radius": "0.625em"}, "outer": [{"title": {"value": "Easy & Quick Custom Coast Guard Coins"}, "subTitle": {"value": "GS-JJ's team of professional designers will perfectly transform your ideas into artwork, and provide proof of realistic style for your approval.You can modify it without limit before production."}, "button": {"value": "Design Now", "alt": "Design Now", "url": "/design/challenge-coins?cid=9"}}], "list": [{"style": {"background-image": "url(https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20220628/ueditor/16/step-1.png)"}, "img": {"value": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20220904/ueditor/16/free-design-for-coast-guard-challenge-coins.png", "alt": "Free Design For Coast Guard Challenge Coins"}, "title": {"value": "Free Design"}, "text": {"value": " "}, "price": {"value": "01"}}, {"img": {"value": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20220904/ueditor/16/free-artwork-for-coast-guard-challenge-coins.png", "alt": "Free Artwork For Coast Guard Challenge Coins"}, "title": {"value": "Free Artwork"}, "text": {"value": " "}, "price": {"value": "02"}}, {"style": {"background-image": "url(https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20220628/ueditor/16/step-3.png)"}, "img": {"value": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20220904/ueditor/16/final-product-for-coast-guard-challenge-coins.png", "alt": "Final Product For Coast Guard Challenge Coins"}, "title": {"value": "Free Real Effect"}, "text": {"value": " "}, "price": {"value": "03"}}]}, {"id": "coastGuardChallengeCoins_02", "theme": "20", "name": "Card", "manualPlay": true, "mousePlay": true, "style": {"background-color": "#f3f4f5"}, "titleStyle": {"font-size": "2.25em", "margin-bottom": "0.9375em"}, "tabBoxStyle": {"padding-top": "0.625em", "padding-left": "2.25em", "padding-right": "2.25em", "background-color": "#ffffff", "overflow": "hidden", "justify-content": "space-between", "gap": "4em"}, "tabStyle": {"min-width": "unset", "height": "unset", "padding-left": "0.5em", "padding-right": "0.5em", "padding-top": "0", "padding-bottom": "0.25em", "line-height": "1.2em", "font-size": "1.25em", "font-weight": "normal", "border-bottom": "0.15em solid transparent"}, "tabSeleStyle": {"font-weight": "bold", "border-radius": "0", "color": "#0066CB", "border-bottom": "0.15em solid #0066CB", "background-color": "transparent"}, "manualBtnStyle": {"position": "absolute", "bottom": 0, "font-size": "2.5em", "right": "0.1em", "top": "unset", "left": "unset", "transform": "none"}, "btnStyle": {"padding-top": 0, "padding-bottom": 0, "line-height": "2.222222em", "background-color": "#0066CB", "border-radius": "0.333333em"}, "column": 5, "outer": [{"title": {"value": "Custom Coast Guard Challenge Coins Options"}, "tabList": [{"value": "Edge Options", "column": 5}, {"value": "Plating Options", "column": 6}, {"value": "Packaging Options", "column": 4}], "button": {"value": "Free Quote", "alt": "Free Quote", "url": "/quote/coins-quote/"}}], "cardStyle": {"flex-direction": "column"}, "cardTitleStyle": {"text-align": "center", "font-size": "1em", "color": "#333333", "line-height": "2.5em", "font-weight": "normal"}, "list": [{"videoBoxStyle": {"border-radius": "0.625em", "border": "0.0625em solid #ccc"}, "tab": 0, "video": {"value": "https://static-oss.gs-souvenir.com/web/quoteManage/20230710/202307107JTb8mTw.mp4?id=5", "poster": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20230709/ueditor/95/standard-edge.jpg", "alt": "Standard Edge"}, "title": {"value": "Standard Edge"}}, {"videoBoxStyle": {"border-radius": "0.625em", "border": "0.0625em solid #ccc"}, "tab": 0, "video": {"value": "https://static-oss.gs-souvenir.com/web/quoteManage/20230710/202307106NpkjQZF.mp4?id=5", "poster": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20230709/ueditor/95/rope-edge.jpg", "alt": "Rope Edge"}, "title": {"value": "Rope Edge"}}, {"videoBoxStyle": {"border-radius": "0.625em", "border": "0.0625em solid #ccc"}, "tab": 0, "video": {"value": "https://static-oss.gs-souvenir.com/web/quoteManage/20241014/Bike_Chain_Edge_20482iway5.mp4", "poster": "https://static-oss.gs-souvenir.com/web/quoteManage/20241014/Bike_Chain_Edge_2048Jrs8aw.png", "alt": "Bike Chain Edge"}, "title": {"value": "Bike Chain Edge"}}, {"videoBoxStyle": {"border-radius": "0.625em", "border": "0.0625em solid #ccc"}, "tab": 0, "video": {"value": "https://static-oss.gs-souvenir.com/web/quoteManage/20241014/Chain_Edge_20482FSES7.mp4", "poster": "https://static-oss.gs-souvenir.com/web/quoteManage/20241014/Chain_Edge_20482KfGfM.png", "alt": "Chain Edge"}, "title": {"value": "Chain Edge"}}, {"videoBoxStyle": {"border-radius": "0.625em", "border": "0.0625em solid #ccc"}, "tab": 0, "video": {"value": "https://static-oss.gs-souvenir.com/web/quoteManage/20241014/Heavy_Chain_Edge_2048acriXW.mp4", "poster": "https://static-oss.gs-souvenir.com/web/quoteManage/20241014/Heavy_Chain_Edge_2048EhMK3s.png", "alt": "Heavy Chain Edge"}, "title": {"value": "Heavy Chain Edge"}}, {"videoBoxStyle": {"border-radius": "0.625em", "border": "0.0625em solid #ccc"}, "tab": 0, "video": {"value": "https://static-oss.gs-souvenir.com/web/quoteManage/20230710/20230710f5ZxYdEm.mp4?id=5", "poster": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20230709/ueditor/95/cross-cut-edge.jpg", "alt": "Cross-cut Edge"}, "title": {"value": "Cross-cut Edge"}}, {"videoBoxStyle": {"border-radius": "0.625em", "border": "0.0625em solid #ccc"}, "tab": 0, "video": {"value": "https://static-oss.gs-souvenir.com/web/quoteManage/20230710/20230710zkcTkwfY.mp4?id=5", "poster": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20230709/ueditor/95/spur-edge.jpg", "alt": "Spur Edge"}, "title": {"value": "Spur Edge"}}, {"videoBoxStyle": {"border-radius": "0.625em", "border": "0.0625em solid #ccc"}, "tab": 0, "video": {"value": "https://static-oss.gs-souvenir.com/web/quoteManage/20230710/20230710iNQfCwZD.mp4?id=5", "poster": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20230709/ueditor/95/oblique-edge.jpg", "alt": "Oblique Edge"}, "title": {"value": "Oblique Edge"}}, {"videoBoxStyle": {"border-radius": "0.625em", "border": "0.0625em solid #ccc"}, "tab": 0, "video": {"value": "https://static-oss.gs-souvenir.com/web/quoteManage/20230710/20230710ebnJiFm6.mp4?id=5", "poster": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20230709/ueditor/95/leaf-edge.jpg", "alt": "Leaf Edge"}, "title": {"value": ""}}, {"videoBoxStyle": {"border-radius": "0.625em", "border": "0.0625em solid #ccc"}, "tab": 0, "video": {"value": "https://static-oss.gs-souvenir.com/web/quoteManage/20230710/20230710H5sQBzYr.mp4?id=5", "poster": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20230709/ueditor/95/bezel-edge.jpg", "alt": "<PERSON><PERSON>"}, "title": {"value": "<PERSON><PERSON>"}}, {"videoBoxStyle": {"border-radius": "0.625em", "border": "0.0625em solid #ccc"}, "tab": 0, "video": {"value": "https://static-oss.gs-souvenir.com/web/quoteManage/20230710/20230710KHS5MwBw.mp4?id=5", "poster": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20230709/ueditor/95/reeded-edge.jpg", "alt": "Reeded <PERSON>"}, "title": {"value": "Reeded <PERSON>"}}, {"videoBoxStyle": {"border-radius": "0.625em", "border": "0.0625em solid #ccc"}, "tab": 0, "video": {"value": "https://static-oss.gs-souvenir.com/web/quoteManage/20230710/20230710DAwCzd8R.mp4?id=5", "poster": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20230709/ueditor/95/sunburst-edge.jpg", "alt": "Sunburst Edge"}, "title": {"value": "Sunburst Edge"}}, {"tab": 1, "img": {"value": "https://sticker-static.oss-accelerate.aliyuncs.com/image/uploads/20231221/shiny-gold-finish-of-custom-medals.png", "alt": "Shiny Gold"}, "title": {"value": "Shiny Gold"}}, {"tab": 1, "img": {"value": "https://sticker-static.oss-accelerate.aliyuncs.com/image/uploads/20231221/shiny-silver-finish-of-custom-medals.png", "alt": "<PERSON><PERSON>"}, "title": {"value": "<PERSON><PERSON>"}}, {"tab": 1, "img": {"value": "https://sticker-static.oss-accelerate.aliyuncs.com/image/uploads/20231221/shiny-copper-finish-of-custom-medals.png", "alt": "Shiny Copper"}, "title": {"value": "Shiny Copper"}}, {"tab": 1, "img": {"value": "https://sticker-static.oss-accelerate.aliyuncs.com/image/uploads/20231221/two-tone-shiny-finish-of-custom-medals.png", "alt": "Two Tone (shiny)"}, "title": {"value": "Two Tone (shiny)"}}, {"tab": 1, "img": {"value": "https://sticker-static.oss-accelerate.aliyuncs.com/image/uploads/20231221/black-nickel-finish-of-custom-medals.png", "alt": "Black Nickel"}, "title": {"value": "Black Nickel"}}, {"tab": 1, "img": {"value": "https://sticker-static.oss-accelerate.aliyuncs.com/image/uploads/20231221/antique-gold-finish-of-custom-medals.png", "alt": "Antique Gold"}, "title": {"value": "Antique Gold"}}, {"tab": 1, "img": {"value": "https://sticker-static.oss-accelerate.aliyuncs.com/image/uploads/20231221/antique-silver-finish-of-custom-medals.png", "alt": "Antique Silver"}, "title": {"value": "Antique Silver"}}, {"tab": 1, "img": {"value": "https://sticker-static.oss-accelerate.aliyuncs.com/image/uploads/20231221/antique-copper-finish-of-custom-medals.png", "alt": "Antique Copper"}, "title": {"value": "Antique Copper"}}, {"tab": 1, "img": {"value": "https://sticker-static.oss-accelerate.aliyuncs.com/image/uploads/20231221/two-tone-antique-finish-of-custom-medals.png", "alt": "Two Tone (antique)"}, "title": {"value": "Two Tone (antique)"}}, {"tab": 1, "img": {"value": "https://sticker-static.oss-accelerate.aliyuncs.com/image/uploads/20231221/rainbow-finish-of-custom-medals.png", "alt": "Rainbow Finish"}, "title": {"value": "Rainbow Finish"}}, {"tab": 1, "img": {"value": "https://sticker-static.oss-accelerate.aliyuncs.com/image/uploads/20231221/dye-black-finish-of-custom-medals.png", "alt": "<PERSON><PERSON>"}, "title": {"value": "<PERSON><PERSON>"}}, {"tab": 1, "img": {"value": "https://sticker-static.oss-accelerate.aliyuncs.com/image/uploads/20231221/dye-red-finish-of-custom-medals.png", "alt": "Dye Red"}, "title": {"value": "Dye Red"}}, {"tab": 1, "img": {"value": "https://sticker-static.oss-accelerate.aliyuncs.com/image/uploads/20231221/dye-blue-finish-of-custom-medals.png", "alt": "Dye Blue"}, "title": {"value": "Dye Blue"}}, {"tab": 1, "img": {"value": "https://sticker-static.oss-accelerate.aliyuncs.com/image/uploads/20231221/dye-green-finish-of-custom-medals.png", "alt": "Dye Green"}, "title": {"value": "Dye Green"}}, {"tab": 1, "img": {"value": "https://sticker-static.oss-accelerate.aliyuncs.com/image/uploads/20231221/dye-yellow-finish-of-custom-medals.png", "alt": "Dye Yellow"}, "title": {"value": "Dye Yellow"}}, {"tab": 2, "style": {"background-color": "#ffffff", "border-radius": "0.625em"}, "img": {"value": "https://sticker-static.oss-accelerate.aliyuncs.com/image/uploads/20231226/PVC-pouch-for-custom-challenge-coins-.jpg", "alt": "Poly Bag Standard"}, "title": {"value": "PVC Pouch"}}, {"tab": 2, "style": {"background-color": "#ffffff", "border-radius": "0.625em"}, "img": {"value": "https://sticker-static.oss-accelerate.aliyuncs.com/image/uploads/20231226/coins-capsule.jpg", "alt": "<PERSON><PERSON><PERSON>"}, "title": {"value": "Coin Capsule"}}, {"tab": 2, "style": {"background-color": "#ffffff", "border-radius": "0.625em"}, "img": {"value": "https://sticker-static.oss-accelerate.aliyuncs.com/image/uploads/20231226/Velour-pouch-for-challenge-coins.jpg", "alt": "Plastic Box"}, "title": {"value": "<PERSON><PERSON><PERSON>"}}, {"style": {"background-color": "#ffffff", "border-radius": "0.625em"}, "tab": 2, "img": {"value": "https://sticker-static.oss-accelerate.aliyuncs.com/image/uploads/20231226/Velour-box-for-challenge-coins.jpg", "alt": "Velour Box"}, "title": {"value": "Velour Box"}}]}]