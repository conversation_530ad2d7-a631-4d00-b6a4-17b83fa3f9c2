<template>
	<div class="card-box" :class="modal.wrapClass" :style="modal.style">
		<pic v-if="modal.bgImg" class="bgImg" :src="modal.bgImg[0].img.value" :alt="modal.bgImg[0].img.alt"
			:title="modal.bgImg[0].img.alt" @click="setModalType(modal.bgImg[0].img, modal.bgImg, 'img')" />
		<div class="bps-container">
			<div class="title">
				<b :class="modal.outer[0].icon.value" v-if="modal.outer[0].icon"
					@click="setModalType(modal.outer[0].icon, modal.outer, 'icon')"></b>
				<EditDiv tagName="h2" v-model:content="modal.outer[0].title.value"
					@click="setModalType(modal.outer[0].title, modal.outer, 'text')" />
			</div>
			<div class="sub-title">
				<EditDiv v-model:content="modal.outer[0].subTitle.value" v-if="modal.outer[0].subTitle"
					@click="setModalType(modal.outer[0].subTitle, modal.outer, 'text')" />
			</div>
			<div class="content">
				<pic v-if="modal.bgImg" class="bgImg" :src="modal.bgImg[0].img.value" :alt="modal.bgImg[0].img.alt"
					:title="modal.bgImg[0].img.alt" @click="setModalType(modal.bgImg[0].img, modal.bgImg, 'img')" />
				<div class="item part2" v-for="(l, li) in modal.list" :childHoverIndex="li" :key="li">
					<pic v-if="l.img" :src="l.img.value" :alt="l.img.alt" @click="setModalType(l.img, modal.list, 'img')" />
					<EditDiv tagName="h3" v-model:content="l.title.value" v-if="l.title"
						@click="setModalType(l.title, modal.list, 'text')" />
					<EditDiv class="des" v-model:content="l.text.value" v-if="l.text"
						@click="setModalType(l.text, modal.list, 'text')" />
					<div class="item-text" v-if="l.button" @click="setModalType(l.button, modal.list, 'button')"
						:style="{ ...l.button.style }">
						<nuxt-link :to="l.button.url || ''" :title="l.button.alt" :target="l.button.target || '_self'">
							<h3>{{ l.button.value }}</h3>
						</nuxt-link>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	props: {
		data: {
			type: Object,
			default: {},
		}
	},
	data() {
		return {
			modal: {
				style: {},
				type: { index: 0, clickPosition: 'outer' },
				...this.data
			}
		};
	},
	watch: {
		modal: {
			handler(val) {
				if (process.env.isManage) this.$emit("update:data", { ...val });
			},
			immediate: true,
			deep: true
		}
	},
	methods: {
		setModalType(target, targetArray, clickType, event, other) {
			this.$setModal(this, target, targetArray, clickType, event, other)
		},
	}
};
</script>

<style scoped lang="scss">
.card-box {
	position: relative;

	.bgImg {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		object-fit: cover;
	}

	.bps-container {
		position: relative;

		&>.title {
			display: flex;
			align-items: center;
			margin-bottom: 25px;

			img {
				margin-right: 18px;
				width: 24px;
				height: 24px;
				object-fit: contain;
			}

			b {
				margin-right: 18px;
				font-size: 26px;
				color: $color-primary;
			}

			h2 {
				font-size: 24px;
				font-weight: bold;
				color: #333333;
			}
		}

		.content {
			&>.bgImg {
				display: none;
			}
		}
	}
}

.solutions_07 {
	padding: 100px 0 143px !important;

	.bps-container {
		.title {
			b {
				display: none;
			}

			h2 {
				width: 755px;
				font-size: 36px;
				font-family: Buenos Aires;
				font-weight: bold;
				color: #333333;
				margin: auto;
				text-align: center;
			}
		}

		.content {
			grid-column-gap: 19px;
			grid-row-gap: 28px;

			.item {
				.item-text {
					width: 226px;
					background: #FFFFFF;
					opacity: 0.87;
					border-radius: 18px;
					bottom: 30px;
				}
			}
		}
	}
}

.solutions_07 {
	padding: 127px 0 47px;

	.content {
		display: grid;
		grid-template-columns: repeat(4, 1fr);
		grid-column-gap: 30px;
		grid-row-gap: 48px;

		.item {
			position: relative;

			img {
				aspect-ratio: 376/350;
			}

			.item-text {
				position: absolute;
				bottom: 0;
				left: 50%;
				transform: translate(-50%, 50%);
				display: flex;
				justify-content: center;
				align-items: center;
				width: 200px;
				height: 36px;
				background: #e6e6e6;
				border-radius: 18px;
				font-size: 14px;
				cursor: pointer;
				transition: 0.1s;
				font-weight: 400;

				h3 {
					font-weight: 400;
				}

				&:hover {
					box-shadow: 0px 0px 21px 3px rgba(88, 88, 88, 0.65);
				}
			}
		}
	}
}

.home-part4,
.solutions_02 {
	padding: 100px 0;

	.bps-container {
		&>.title {
			margin-bottom: 47px;

			h2 {
				width: 1200px;
				margin: 0 auto;
				font-size: 36px;
			}
		}
	}

	.content {
		display: grid;
		justify-content: center;
		grid-template-columns: repeat(3, minmax(auto, 320px));
		grid-gap: 120px;

		.item {
			position: relative;

			&:nth-child(1) {
				background: url("https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230130/20230130BNC2BwKE.png") no-repeat right bottom/auto 80%;
			}

			&:nth-child(2) {
				background: url("https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230130/20230130tM2amFeH.png") no-repeat right bottom/auto 80%;
			}

			&:nth-child(3) {
				background: url("https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230130/202301306Dhf4ZGd.png") no-repeat right bottom/auto 80%;
			}

			img {
				max-width: 53px;
				height: 47px;
			}

			h3 {
				margin: 20px 0 17px;
				font-size: 18px;
				font-weight: 700;
			}

			.des {
				font-size: 14px;
				line-height: 22px;
				color: #666666;
			}
		}
	}
}

.solutions_02 {
	.bps-container {
		.title {
			margin-bottom: 29px !important;

			h2 {
				text-align: center;
			}
		}

		.sub-title {
			div {
				width: 866px;
				font-size: 18px;
				font-family: Buenos Aires;
				font-weight: 400;
				color: #666666;
				text-align: center;
				margin: 29px auto;
			}
		}
	}

	.content {
		grid-template-columns: repeat(3, 1fr);
		grid-gap: 34px !important;

		.item {
			text-align: center;
			border: 1px solid #DFDFDF;
			border-radius: 10px;
			padding: 20px;

			img {
				width: auto !important;
				height: auto !important;
			}

			&:nth-child(1) {
				background: none;
			}

			&:nth-child(2) {
				background: none;
			}

			&:nth-child(3) {
				background: none;
			}

			&:hover {
				box-shadow: 1px 2px 18px 0px rgba(188, 188, 188, 0.54);
			}
		}
	}
}

.home-part5 {
	padding: 60px 0 50px;

	.bps-container {
		padding: 80px 6vw;
		background-color: rgba(255, 255, 255, 0.6);
		border-radius: 30px;

		&>.title {
			justify-content: center;
			margin-bottom: 10px;

			h2 {
				font-size: 36px;
			}
		}

		.sub-title {
			text-align: center;
			color: #666666;
			font-size: 15px;
			margin-bottom: 10px;
		}
	}

	.content {
		display: grid;
		justify-content: center;
		grid-template-columns: repeat(3, 1fr);
		grid-gap: 0 20px;

		.item {
			position: relative;
			text-align: center;

			&::after {
				content: "";
				position: absolute;
				right: 0;
				top: 50%;
				transform: translate(50%, -50%);
				width: 2px;
				height: 110px;
				background-color: #ffffff;
			}

			&:nth-child(3n + 4) {
				&::after {
					display: none;
				}
			}

			h3 {
				font-size: 72px;
				color: #333333;
				text-shadow: 0px 20px 27px rgba(122, 99, 58, 0.35);
				background: linear-gradient(-67deg, #C19952 0%, #F1C16C 36.71875%, #C19952 100%);
				-webkit-background-clip: text;
				-webkit-text-fill-color: transparent
			}

			.des {
				font-size: 14px;
				font-weight: 600;
				line-height: 18px;
				text-transform: uppercase;
			}
		}
	}
}

@media screen and (max-width: $mb-width) {
	.solutions_07 {
		.bps-container {
			.title {
				b {
					display: none;
				}

				h2 {
					width: 755px;
					font-size: 21px !important;
					font-family: Buenos Aires;
					text-align: left;
					font-weight: bold;
					color: #333333;
					margin: auto;
				}
			}

			.content {
				grid-column-gap: 8px;
				grid-row-gap: 15px;

				.item {
					.item-text {
						width: 90% !important;
						background: #FFFFFF;
						opacity: 0.87;
						border-radius: 18px;
						bottom: 5px;
						position: absolute;
						left: 50%;
						transform: translateX(-50%);

						h3 {
							font-size: 12px;
						}
					}
				}
			}
		}
	}

	.card-box {
		padding: 54px 0 27px;

		&>.bgImg {
			display: none;
		}

		.bps-container {
			&>.title {
				margin-bottom: 20px;

				img {
					margin-right: 8px;
					width: 16px;
					height: 16px;
				}

				b {
					margin-right: 8px;
					font-size: 17px;
					color: $color-primary;
				}

				h2 {
					font-size: 22px;
				}
			}

			.content {
				&>.bgImg {
					display: block;
				}
			}
		}
	}

	.solutions_07 {
		padding: 54px 0 27px !important;

		.content {
			grid-template-columns: repeat(2, 1fr);
			grid-gap: 13px 16px;

			.item {
				position: relative;

				img {
					width: 100%;
					aspect-ratio: 165/154;
				}

				.item-text {
					position: static;
					bottom: auto;
					left: auto;
					transform: none;
					width: 100%;
					height: 29px;
					border-radius: 0px 0px 5px 5px;
					font-size: 12px;

					&:hover {
						box-shadow: none;
					}
				}
			}
		}
	}

	.solutions_02 {
		.bps-container {
			.title {
				margin-bottom: 0px !important;

				h2 {
					border-bottom: none !important;
					text-align: left;
					font-size: 21px;
					font-family: Buenos Aires;
					font-weight: bold;
					color: #333333;
					padding-bottom: 0px !important;
				}
			}

			.sub-title {
				div {
					font-size: 12px;
					font-family: Buenos Aires;
					font-weight: 400;
					color: #333333;
					width: 100% !important;
					text-align: left;
					margin: 15px 0 20px 0;
				}
			}
		}

		.content {
			.item {
				border: none !important;
				background-color: #F9F9F9 !important;

				img {
					width: auto !important;
					height: auto !important;
					position: relative;
				}

				img {
					width: auto !important;
					height: auto !important;
					position: absolute;
					top: 50%;
					transform: translateY(-50%);
					left: 5%;
				}

				h3,
				.des {
					width: 85%;
					margin-left: 18%;
					text-align: left;
					font-size: 12px;
				}
			}
		}
	}

	.home-part4,
	.solutions_02 {
		padding: 27px 0;

		.bps-container {
			&>.title {
				margin-bottom: 0;

				h2 {
					width: 100%;
					border-bottom: 2px solid #f2f2f2;
					padding-bottom: 23px;
				}
			}
		}

		.content {
			grid-template-columns: repeat(1, 1fr);
			grid-gap: 20px;

			.item {
				padding: 15px 0 23px;
				border-bottom: 2px solid #f2f2f2;

				&:nth-child(1) {
					background-size: auto 83px;
					background-position: right calc(100% - 20px);
				}

				&:nth-child(2) {
					background-size: auto 83px;
					background-position: right calc(100% - 20px);
				}

				&:nth-child(3) {
					background-size: auto 83px;
					background-position: right calc(100% - 20px);
				}

				img {
					max-width: 27px;
					height: 24px;
				}
			}
		}
	}

	.home-part5 {
		height: auto;
		padding: 27px 0;

		.bps-container {
			padding: 0 15px;
			background-color: transparent;
			border-radius: 0;
			text-align: left;

			&>.title {
				margin-bottom: 10px;

				h2 {
					width: 100%;
					font-size: 21px;
				}
			}

			.sub-title {
				text-align: left;
				margin-bottom: 25px;
			}

			.content {
				position: relative;
				grid-template-columns: 1fr;
				padding: 50px 0;
				margin: 0 -15px;
				grid-gap: 20px 0;

				.item {
					position: relative;
					width: 265px;
					background: rgba(255, 255, 255, 0.83);
					border-radius: 5px;
					padding: 24px 0;
					margin: 0 auto;

					&::after {
						display: none;
					}

					h3 {
						font-size: 36px;
						text-shadow: 0px 10px 13px rgba(122, 99, 58, 0.35);

						background: linear-gradient(-67deg,
								#c19952 0%,
								#f1c16c 36.71875%,
								#c19952 100%);
						-webkit-background-clip: text;
						-webkit-text-fill-color: transparent;
					}

					.des {
						font-size: 12px;
						margin-bottom: 0;
					}
				}
			}
		}
	}
}

@media screen and (min-width: 1600px) {
	.solutions_07 {
		.bps-container {
			width: 1600px;
			max-width: 1600px;
		}
	}
}
</style>
