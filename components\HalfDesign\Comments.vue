<template>
	<div class="mb-4" :class="stepData.styleName">
		<div class="step-content">
			<div class="step-text grey--text">{{ stepData.attributeTitle }}</div>
			<div class="step-wrap">
				<textarea class="myTextarea" :placeholder="langSemiCustom.comments" ref="myTextarea"
					@input="validateComment" v-model="commentObj.comments"></textarea>
				<div class="errorContainer" ref="errorContainer"></div>
				<div class="des">
					<span v-show="!commentObj.uploadList.length">{{ langSemiCustom.noattached }}</span> <a
						href="javascript:;" @click="openUpload"> + {{ langSemiCustom.attachFile }}</a>
				</div>
				<div class="tipText" v-show="!commentObj.uploadList.length"><b>!</b>{{ langSemiCustom.fileTip }}</div>
				<div class="fileWrap">
					<div class="file-item" v-for="(item, index) in commentObj.uploadList" :key="item.secure_url"
						@click="zoomPic(item.secure_url)">
						<div class="fileInfo d-flex align-items-center">
							<v-icon class="mr-1">mdi-file</v-icon>
							<span class="fileName">{{ item.original_filename }}</span>
						</div>
						<div class="control">
							<v-btn small icon>
								<v-icon>mdi-check</v-icon>
							</v-btn>
							<v-btn small icon @click.stop="deletePic(index)">
								<v-icon>mdi-trash-can</v-icon>
							</v-btn>
						</div>
					</div>
				</div>
				<input type="file" ref="upload" @change="uploadPic" />
			</div>
		</div>
	</div>
</template>
<script>
import { uploadFile } from "@/utils/oss";
import { isImageType } from "@/utils/utils";
export default {
	inject: ["getCommentObj"],
	props: {
		stepData: {
			type: Object,
			default: {},
		},
	},
	data() {
		return {
			comments: "",
			uploadList: [],
			myTextarea: null,
			errorContainer: null,
			colorPrimary: "",
			textareaMinHeight: 130,
			textareaMaxHeight: 240,
		};
	},
	computed: {
		langSemiCustom() {
			return this.$store.getters.lang?.semiCustom;
		},
		commentObj() {
			return this.getCommentObj();
		},
	},
	methods: {
		validateComment() {
			if (this.commentObj.comments.length > 250) {
				this.errorContainer.innerText = "Max 250 characters";
				this.myTextarea.style.setProperty("border-color", "red", "important");
			} else {
				this.errorContainer.innerText = "";
				this.myTextarea.style.setProperty("border-color", `${this.colorPrimary}`, "important");
			}
			if (+this.myTextarea.scrollHeight > +this.textareaMinHeight) {
				this.myTextarea.style.height = "auto";
				const newHeight = this.myTextarea.scrollHeight;
				if (+newHeight > +this.textareaMaxHeight) {
					this.myTextarea.style.overflow = "auto";
				} else {
					this.myTextarea.style.overflow = "hidden";
				}
				this.myTextarea.style.height = `${newHeight}px`;
			} else {
				this.myTextarea.style.overflow = "hidden";
				this.myTextarea.style.height = +this.textareaMinHeight + "px";
			}
		},

		zoomPic(img) {
			if (isImageType(img)) {
				this.$viewerApi({
					images: [img],
				});
			}
		},
		openUpload() {
			this.$refs.upload.click();
		},
		deletePic(index) {
			this.commentObj.uploadList.splice(index, 1);
		},
		uploadPic(event) {
			let files = event.target.files,
				size = files[0].size;
			if (size / 1020 / 1024 > 80) {
				this.$toast.error("File size cannot exceed 80m.");
				this.$refs.upload.value = "";
				return;
			}
			uploadFile(files[0]).then((res) => {
				this.commentObj.uploadList.push({
					original_filename: files[0].name,
					secure_url: res,
				});
				this.$refs.upload.value = "";
			});
		},
	},
	mounted() {
		this.errorContainer = this.$refs.errorContainer;
		this.colorPrimary = window.getComputedStyle(document.documentElement).getPropertyValue("--color-primary");
		this.myTextarea = this.$refs.myTextarea;
		let borderColor2 = "--bg-dark";
		const colorBgDark = window.getComputedStyle(document.documentElement).getPropertyValue(borderColor2);
		this.myTextarea.addEventListener("focus", () => {
			if (this.commentObj.comments.length > 250) {
				this.myTextarea.style.setProperty("border-color", "red", "important");
			} else {
				// myTextarea.style.setProperty("border-color", `${colorPrimary}`, "important");
				this.myTextarea.style.borderColor = "";
			}
		});
		this.myTextarea.addEventListener("blur", () => {
			this.myTextarea.style.setProperty("border-color", `${colorBgDark}`);
		});
		this.$nextTick(() => {
			this.textareaMinHeight = this.$refs.myTextarea.getBoundingClientRect().height || 130;
		})
	},
};
</script>
<style scoped lang="scss">
@import "~assets/css/half.scss";

[theme="10"] {
	.des {
		@include respond-to(mb) {
			display: flex;
			justify-content: flex-end;
		}
	}

	.myTextarea {
		border: 2px solid $bg-dark !important;
		background: $bg-primary !important;

		&:focus {
			border: 2px solid $color-primary !important;
		}
	}
}

[theme="11"] {
	.grey--text {
		color: #333 !important;
	}

	.des {
		display: none !important;
	}

	.myTextarea {
		border: 2px solid var(--color-second) !important;
		background: #e3ebe5 !important;

		&:focus {
			border: 2px solid $color-primary !important;
		}
	}

	.step-content {
		@include respond-to(mb) {
			grid-gap: 10px;
			grid-template-columns: 40px 1fr;
			align-items: center;

			.step-text {
				position: relative;

				&::after {
					content: ":";
					position: absolute;
					right: 0;
					top: 42%;
					transform: translateY(-50%);
				}
			}
		}
	}
}

.step-content {
	display: grid;
	grid-template-columns: 100px 1fr;
	grid-gap: 10px;

	.step-wrap {
		position: relative;
	}

	.myTextarea {
		box-sizing: border-box;
		width: 100%;
		min-height: 130px;
		max-height: 240px;
		overflow: hidden;
		padding: 10px;
		border-radius: 6px;
		border: 2px solid #ede9e9;
		background-color: rgb(246, 246, 246);
		font-size: 12px;

		&:focus {
			border: 2px solid $color-primary;
		}

		&::placeholder {
			display: none;
		}
	}

	.errorContainer {
		font-size: 12px;
		color: red;
	}

	.fileWrap {
		margin-top: 10px;

		.file-item {
			display: flex;
			justify-content: space-between;

			.control {
				margin-left: 40px;

				i {
					margin: 0 2px;
					cursor: pointer;
				}
			}
		}
	}

	.des {
		color: #999999;
		margin-top: 10px;
		font-size: 14px;

		a {
			color: $color-primary;
		}
	}

	.tipText {
		display: none;
		margin-top: 0.4em;
		font-weight: 400;
		font-size: 14px;
		color: #999999;

		b {
			width: 1.2em;
			height: 1.2em;
			line-height: 1.2em;
			text-align: center;
			border-radius: 50%;
			background-color: $color-second;
			color: $color-primary;
			margin-right: 6px;
		}
	}

	input[type="file"] {
		position: absolute;
		z-index: -1;
		opacity: 0;
	}
}

.style2 .step-content {
	grid-template-columns: 1fr;

	.step-text {
		display: none;
	}

	.myTextarea {
		&::placeholder {
			font-weight: 400;
			font-size: 16px;
			color: #999999;
		}
	}
}

.style3 .step-content {
	grid-template-columns: 1fr;

	.myTextarea {
		min-height: 80px;
		border-radius: 6px 6px 6px 6px;
		border: 1px solid #E9ECF0;
		background-color: #fff;
	}

	.tipText {
		display: block;
	}
}

@include respond-to(mb) {
	.step-content {
		grid-template-columns: 1fr;
		grid-gap: 5px;

		.v-textarea {
			font-size: 14px;
		}

		.fileWrap {
			margin-top: 10px;

			.file-item {
				font-size: 12px;

				.v-icon {
					font-size: 18px;
				}
			}
		}

		.des {
			font-size: 12px;
		}

		.tipText {
			font-size: 12px;
		}
	}
}
</style>
