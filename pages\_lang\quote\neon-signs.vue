<template>
	<FdNeonSigns v-if="isFd"></FdNeonSigns>
	<NeonSigns v-else></NeonSigns>
</template>
<script>
import NeonSigns from "@/components/Neon/NeonSigns.vue";
import FdNeonSigns from "@/components/Neon/FdNeonSigns.vue";

export default {
	head() {
		return {
			title: "Quote page",
		};
	},
	components: {
		NeonSigns,
		FdNeonSigns
	},
	computed: {
		isFd() {
			return this.$store.state.proId === 9;
		}
	}
};
</script>
<style lang="scss" scoped></style>