<template>
    <div class="freeTip">
        <div v-for="(item,index) in freeTipList" :key="index">
            <b :class="item.icon"></b><strong>{{ item.value }}</strong>
        </div>
    </div>
</template>

<script>
export default {
    props:{
        freeTipList:{
            type: Array,
            required: true
        }
    },
    computed: {
        countryCode() {
            return this.$store.state.country.countryCode;
        },
        lang() {
            return this.$store.getters.lang.quote || {};
        },
    },
};
</script>

<style lang="scss" scoped>
.freeTip {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.8em;
    margin: 1em 0;

    b {
        color: #68bd2c;
        margin-right: 4px;
    }
}
</style>