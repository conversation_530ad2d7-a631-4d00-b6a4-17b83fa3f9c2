<template>
	<div class="mb-4" :class="stepData.styleName">
		<half-design-select-tip :customText="stepData.attributeTitle" :selectedValue="shape"></half-design-select-tip>
		<slot name="stepText"></slot>
		<div class="step-content">
			<div class="step-item" :style="{ pointerEvents: canClick ? 'auto' :  'none' }"  :class="{ active: index === selectIndex }"
				v-for="(step, index) in stepData.productParamList" :key="index" @click="selectStep(step, index)">
				<div class="imgWrap">
					<img :src="step.imgDetail" :alt="step.valueName" :title="step.valueName" />
				</div>
				<div class="d-flex-center text-center name">
					<div class="text-truncate">
						<span style="margin-right:4px">{{ step.valueName }}</span>
						<v-tooltip bottom v-if="stepData.styleName == 'style1' || stepData.styleName == 'style2'">
							<template v-slot:activator="{ on, attrs }">
								<v-icon v-bind="attrs" v-on="on" size="18px"> mdi-help-circle-outline </v-icon>
							</template>
							<div class="text-center" style="display: flex; align-items: start" v-show="step.remark">
								<div
									style="text-align: center; color: #fff; line-height: 22px; font-size: 13px; word-break: break-word;max-width:250px ;">
									{{ step.remark }}</div>
							</div>
						</v-tooltip>
					</div>
				</div>
				<half-design-check-icon class="absolute-top-right check-icon"></half-design-check-icon>
				<v-btn small icon color="#9E9E9E" class="absolute-top-right zoom-icon" @click.stop="zoomPic(step.imgDetail)">
					<b class="icon-a-tgsc-addzhuanhuan"></b>
				</v-btn>
			</div>
		</div>
		<div class="errorTip">
			<v-alert dense outlined type="error">
				{{ langSemiCustom.errorTip }}
			</v-alert>
		</div>
	</div>
</template>
<script>
export default {
	props: {
		stepData: {
			type: Object,
			default: {},
		},
	},
	data() {
		return {
			selectIndex: -1,
			selectItem: null,
		};
	},
	computed: {
		shape() {
			return this.selectItem?.valueName;
		},
		langSemiCustom() {
			return this.$store.getters.lang?.semiCustom;
		},
		canClick(){
			return this.$store.state.halfDesign.canClickBtn;
		}
	},
	methods: {
		clearData(state, areaIndex=0) {
			this.selectIndex = areaIndex;
			this.selectItem = null;
			if (areaIndex < 0) return;
			if (this.stepData && this.stepData.productParamList && this.stepData.productParamList.length > 0) {
				let item = this.stepData.productParamList[areaIndex];
				this.selectStep(item, areaIndex, state);
			}
		},
		selectStep(item, index, state = false) {
			this.selectIndex = index;
			this.selectItem = item;
			this.$emit("selectStep", {
				type: this.stepData.attributeFlag,
				data: item,
				parent: this.stepData,
				id: this.stepData.id,
				firstSelect: state,
			});
		},
		zoomPic(img) {
			this.$viewerApi({
				images: [img],
			});
		},
	},
	mounted() {
		this.$Bus.$on("clearArea", this.clearData);
		this.$Bus.$on("selectDefaultAreaStep",this.clearData);
	},
	beforeDestroy() {
		this.$Bus.$off("clearArea", this.clearData);
		this.$Bus.$off("selectDefaultAreaStep", this.clearData);
	},
};
</script>
<style scoped lang="scss">
@import "~assets/css/half.scss";

.step-content {
	display: grid;

	.step-item {
		position: relative;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		@include step-default;
		min-width: 0;
		cursor: pointer;

		.text-truncate{
			display: flex;
			align-items: center;
		}

		.name {
			margin-top: 4px;
		}

		.check-icon {
			display: none;
		}

		.zoom-icon {
			display: none;
		}

		@media (any-hover: hover) {
			&:hover {
				border-color: $color-primary;
			}
		}
	}
}

.style1 .step-content {
	grid-template-columns: repeat(3, 1fr);
	grid-gap: 10px;

	.step-item {
		.imgWrap {
			@include flex-center;
			height: 160px;
		}
	}

	.step-item.active {
		border-color: $color-primary;

		.check-icon {
			display: flex;
		}
	}
}

.style2 .step-content {
	grid-template-columns: repeat(5, 1fr);
	grid-gap: 10px;

	.step-item {
		border-color: transparent;
		background-color: $background-color;

		&::after {
			display: none;
			content: "";
			position: absolute;
			bottom: 0;
			left: 50%;
			transform: translateX(-50%);
			width: 0;
			height: 0;
			border-width: 0 5px 5px;
			border-style: solid;
			border-color: transparent transparent $color-primary;
		}

		&:hover {
			border-color: $color-primary;
		}

		.imgWrap {
			@include flex-center;
			height: 80px;
		}
	}

	.step-item.active {
		border-color: $color-primary;

		&::after {
			display: block;
		}
	}
}

.style3 .step-content {
	grid-template-columns: repeat(3, 1fr);
	grid-gap: 10px;

	.zoom-icon {
		display: block;
	}

	.step-item {
		.imgWrap {
			@include flex-center;
			height: 160px;
		}
	}

	.step-item.active {
		border-color: $color-primary;
	}
}

.style4 .step-content {
	grid-template-columns: repeat(2, 1fr);
	grid-gap: 10px;

	.zoom-icon {
		display: block;
	}

	.step-item {
		.imgWrap {
			@include flex-center;
			height: 200px;
		}

		.name {
			display: none;
		}
	}

	.step-item.active {
		border-color: $color-primary;
	}
}

.style5 .step-content {
	grid-template-columns: repeat(5, 1fr);
	grid-gap: 10px;

	.step-item {
		background-color: $background-color;

		.zoom-icon {
			display: block;
		}

		.imgWrap {
			@include flex-center;
			height: 80px;
		}

		&::after {
			display: none;
			content: "";
			position: absolute;
			bottom: 0;
			left: 50%;
			transform: translateX(-50%);
			width: 0;
			height: 0;
			border-width: 0 5px 5px;
			border-style: solid;
			border-color: transparent transparent $color-primary;
		}
	}

	.step-item.active {
		border-color: $color-primary;

		&::after {
			display: block;
		}
	}
}
@include respond-to(mb) {
	.style1 .step-content {
		grid-template-columns: repeat(3, 1fr);
		grid-gap: 5px;

		.step-item {
			.imgWrap {
				height: 80px;
			}
		}
	}

	.style2 .step-content {
		grid-template-columns: repeat(4, 1fr);
		grid-gap: 5px;

		.step-item {
			.imgWrap {
				@include flex-center;
				height: 50px;
			}
		}
	}

	.style3 .step-content {
		grid-template-columns: repeat(3, 1fr);
		grid-gap: 5px;

		.step-item {
			.imgWrap {
				@include flex-center;
				height: 80px;
			}
		}
	}

	.style4 .step-content {
		grid-template-columns: repeat(2, 1fr);
		grid-gap: 5px;

		.step-item {
			.imgWrap {
				height: 120px;
			}
		}
	}

	.style5 .step-content {
		grid-template-columns: repeat(4, 1fr);
		grid-gap: 5px;

		.step-item {
			.imgWrap {
				height: 60px;
			}
		}
	}
}
</style>
