<template>
	<div class="quoteWrap" :class="[modal.class]">
		<div class="home" v-for="(l, li) in modal.list" :style="modal.style" :key="li">
			<div flex class="content modal-box" :style="{ ...modal.contentStyle, ...l.contentStyle }" :class="{ mask: mask }" @click="closeMask">
                <div class="banner" @click="setModalType(l.banner, modal.list, 'banner')" v-if="l.banner?.value">
                    <pic :src="l.banner.value" :alt="l.banner.alt" :style="modal.homeImgStyle" />
                </div>
                <div class="banner" @click="setModalType(l.video, modal.list, 'video')" v-else-if="l.video?.value" @mouseenter="setModalType(l.video, null, 'video', { type: 'enter' })">
                    <video :src="l.video.value" :title="l.video.alt" :poster="l.video?.poster" :style="modal.homeVideoStyle" :loop="!modal.mousePlay" autoplay muted playsinline></video>
                </div>
                <div class="banner" @click="setModalType(l.imgList, modal.list, 'img_list', {}, li)" v-else-if="l.imgList">
                    <pic v-for="(i, ii) in l.imgList" :src="i.value" :alt="i.alt" :key="i.value" :style="modal.homeImgStyle" v-show="bannerIndex == ii" preload="auto" />
                </div>
                <div class="textContent">
					<EditDiv :tagName="l.title.tagName || 'h1'" v-model:content="l.title.value" :style="modal.titleStyle" @click="setModalType(l.title, modal.list, 'text')" v-if="l.title?.value" />
                    <div v-if="l.reviews" flex :pointer="l.reviews.url" class="reviews" :style="modal.reviewsStyle"
                         @click="setModalType(l.reviews, modal.list, 'reviews', l.reviews)">
                        <span v-show="l.reviews.showScore && l.reviews.star > 1">{{ l.reviews.star }}</span>
                        <div class="star-box" v-show="l.reviews.star > 1"
                             :style="{ ...modal.reviewsStarStyle, backgroundImage: 'linear-gradient(90deg,' + (l.reviews.starColor || '#F96A00') + ' ' + l.reviews.star * 20 + '%,#999 0)' }">
                            <b v-for="s in 5" :class="l.reviews.starIcon || 'icon-star'"></b>
                        </div>
                        <template v-if="l.reviews.number">
                            <span :style="modal.reviewsExcellentStyle">{{ lang.excellent }}</span>
                            <span :style="modal.reviewsReviewsStyle"
                                  v-html="l.reviews.number.toLocaleString() + '+ ' + lang.reviews + ' ' + lang.on"></span>
                            <pic :style="modal.reviewsImgStyle" :alt="lang.shopperApproved"
                                 :src="l.reviews.img || 'https://sticker-static.oss-accelerate.aliyuncs.com/image/uploads/20230728/ShopperApproved.png'" />
                        </template>
                    </div>
					<EditDiv class="describe" :tagName="l.subTitle.tagName" v-model:content="l.subTitle.value" :style="modal.subTitleStyle" @click="setModalType(l.subTitle, modal.list, 'text')" v-if="l.subTitle?.value"></EditDiv>
					<EditDiv v-if="l.text?.value" :style="modal.textStyle" v-model:content="l.text.value" :tagName="l.text.tagName" @click="setModalType(l.text, modal.list, 'text')"></EditDiv>
                    <div v-if="l.button?.value || l.button1?.value || l.btnList" class="btn-box" :style="modal.btnBoxStyle">
                        <button :title="l.button.alt" :style="{ ...modal.btnStyle, ...l.button.style }"
                                v-if="l.button?.value" :primary="!l.button.outline" :outline="l.button.outline"
                                @click="setModalType(l.button, modal.list, 'button', l.button)">
                            <EditDiv v-show="l.button.value" tagName="label" v-model:content="l.button.value" />
                            <b :class="l.button.icon" v-show="l.button.icon"></b>
                        </button>

                        <button :title="l.button1.alt" :style="modal.btn1Style" v-if="l.button1?.value"
                                :primary="!l.button1.outline" :outline="l.button1.outline"
                                @click="setModalType(l.button1, modal.list, 'button', l.button1)">
                            <EditDiv v-show="l.button1.value" tagName="label" v-model:content="l.button1.value" />
                            <b :class="l.button1.icon" v-show="l.button1.icon"></b>
                        </button>

                        <button v-for="b in l.btnList" :nowPage="b.url == $store.state.pagePath" :style="modal.btnStyle"
                                :primary="!b.outline" :outline="b.outline" :key="b.value" :title="b.value"
                                @click="setModalType(l.btnList, modal.list, 'img_list', b)">
                            <EditDiv v-show="b.value" tagName="label" v-model:content="b.value" />
                            <b v-show="b.icon" :class="b.icon"></b>
                        </button>
                    </div>
                    <div class="swiper-area" v-if="imageJson.length">
                        <div class="myswiper2">
                            <div class="swiper" ref="swiper2">
                                <div class="swiper-wrapper">
                                    <div class="swiper-slide" v-for="(item, index) in imageJson" :key="index">
                                        <img :src="item.url" :alt="item.alt" :title="item.alt">
                                    </div>
                                </div>
                            </div>
                            <div class="swiper-button-next">
                                <img src="https://static-oss.gs-souvenir.com/web/quoteManage/20240905/Right_Toggle_Button_20240905KiJQ6t.png" alt="rightBtn" />
                            </div>
                            <div class="swiper-button-prev">
                                <img src="https://static-oss.gs-souvenir.com/web/quoteManage/20240905/Left_Toggle_Button_20240905wjrTGT.png" alt="leftBtn" />
                            </div>
                        </div>
                        <div class="myswiper1">
                            <div class="swiper" ref="swiper1">
                                <div class="swiper-wrapper">
                                    <div class="swiper-slide" v-for="(item, index) in imageJson" :key="index">
                                        <img :src="item.urlThumb||item.url" :alt="item.alt" :title="item.alt" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
				</div>
				<div class="rightArea">
					<div class="quoteContent" ref="excludeElement">
						<component :is="modal.quoteComponentName" class="bannerQuote" :style="modal.quoteStyle" :cateId="modal.quoteCateId" :pid="modal.quotePid" :configStyle="modal.styleConfig || {}" @click.native="setModalType({}, modal.list, 'quote_table')"></component>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import VideoPlayer from "@/components/Quote/VideoPlayer.vue";
import { getInfo } from "@/api/pins";
import QuickQuote from "@/components/modal/Quote/BannerQuote/QuickQuote.vue";
import QuoteStep from "@/components/Quote/QuickQuote/QuoteStep.vue";
export default {
	props: {
		data: {
			type: Object,
			default: {},
		},
	},
	data() {
		return {
			description: [],
			imageJson: [],
			configJson: null,
			mask: false,
			swiperList: [
				{
					url: "https://static-oss.gs-souvenir.com/web/quoteManage/20241022/Round_UV_Printed_Medals_2056wmTPb7.png",
					alt: "Round UV Printed Medals",
				},
				{
					url: "https://static-oss.gs-souvenir.com/web/quoteManage/20241022/Round_Wreath_Medal_2056PGRBwy.png",
					alt: "Round Wreath 5K Medal",
				},
				{
					url: "https://static-oss.gs-souvenir.com/web/quoteManage/20241022/Star_Cluster_Medal_2056rFBjhY.png",
					alt: "Star Cluster School Medal",
				},
				{
					url: "https://static-oss.gs-souvenir.com/web/quoteManage/20241022/Wreath_Metal_Medal_2056ZmSRxw.png",
					alt: "Round Flag Running Medal",
				},
			],
			modal: {
                quoteComponentName:"QuickQuote",
				productImgStyle: {},
				homeImgStyle: {},
				style: {},
				type: {},
				list: [],
				...this.data,
			},
		};
	},
	computed: {
		lang() {
			return this.$store.getters.lang.layout || {};
		},
		device() {
			return this.$store.state.device;
		},
	},
	components: {
        QuoteStep,
		QuickQuote,
		VideoPlayer,
	},
	watch: {
		modal: {
			handler(val) {
				if (val.list?.find((i) => i.imgList)) this.bannerLength = val.list.find((i) => i.imgList).imgList.length;
				if (process.env.isManage) this.$emit("update:data", val);
			},
			immediate: true,
			deep: true,
		},
	},
	methods: {
		closeMask(event) {
			const excludeElement = this.$refs.excludeElement[0];
			if (!excludeElement.contains(event.target)) {
				this.mask = false;
			}
		},
		showMask() {
			this.mask = true;
		},
		setModalType(target, targetArray, clickType, event, other) {
			this.$setModal(this, target, targetArray, clickType, event, other);
		},
        initSwiper() {
            this.myswiper1 = new Swiper(this.$refs.swiper1, {
                slidesPerView: 4,
                spaceBetween: 8,
                watchSlidesVisibility: true, //防止不可点击
                grabCursor: true,
            });
            this.myswiper2 = new Swiper(this.$refs.swiper2, {
                slidesPerView: 1,
                spaceBetween: 10,
                autoplay: false,
                grabCursor: true,
                observer: true,
                observeParents: true,
                thumbs: {
                    swiper: this.myswiper1,
                },
                navigation: {
                    nextEl: ".myswiper2 .swiper-button-next",
                    prevEl: ".myswiper2 .swiper-button-prev",
                },
            });
        },
	},
	async mounted() {
		let res = await getInfo({ id: this.modal.quoteCateId });
		this.description = res.data.description ? JSON.parse(res.data.description) : [];
		this.imageJson = res.data.imageJson ? JSON.parse(res.data.imageJson) : [];
		this.configJson = res.data.configJson ? JSON.parse(res.data.configJson) : [];
		this.$nextTick(() => {
			this.initSwiper();
		});
	},
};
</script>

<style lang="scss" scoped>
.quoteWrap {
	.home {
		position: relative;

		.banner {
			position: absolute;
			inset: 0;
			img {
				width: 100%;
				height: 100%;
				object-fit: cover;
			}
		}

		.content {
			position: relative;
			display: grid;
            align-items: center;
			grid-template-columns: 1fr 1fr;
			gap: 6em;
			padding-bottom: 1.5em;
			background-size: cover;
			background-position: center;
			background-repeat: no-repeat;
            z-index: auto;

            .bannerQuote{
                @include respond-to(mb){
                    ::v-deep .quoteBoxFooter{
                        position: static;
                        margin: 0 -1.2em -1.2em -1.2em;
                    }
                }
            }
			&.mask {
				&::after {
					content: "";
					position: absolute;
					inset: 0;
					background-color: rgba(0, 0, 0, 0.5);
					z-index: 10;
				}

				.quoteContent {
					position: relative;
					z-index: 11;
				}
			}

			@include respond-to(mb) {
				grid-template-columns: repeat(1, 1fr);
				gap: 2em;
			}

			.textContent {
                position: relative;
                display: flex;
                flex-direction: column;
				min-width: 0;

                .describe {
                    line-height: 1.5;
                    font-size: calc(1em + 2px);
                    font-weight: 700;
                }

                .btn-box {
                    grid-gap: 1em;
                    flex-wrap: wrap;
                    margin-top: 1em;
                    display: inline-flex;
                }

                .reviews {
                    grid-gap: 0.3em;
                    flex-wrap: wrap;
                    width: max-content;
                    line-height: 1.5em;
                    margin-bottom: 1em;
                    align-items: center;
                    max-width: 100%;

                    @include respond-to(mb){
                        font-size: 2.6vw;

                        .star-box {
                            font-size: 1em;
                        }
                    }

                    >span {
                        line-height: 1em;
                    }

                    .star-box {
                        font-size: 1.2em;
                        background-clip: text;
                        -webkit-background-clip: text;

                        b {
                            color: transparent;
                            margin-right: 0.1vw;
                        }
                    }

                    img {
                        width: auto;
                        height: 1.5em;
                    }
                }

                .swiper-area {
                    margin: 2.25em 0 0;
                    width: 100%;

                    @include respond-to(mb){
                        margin-top: 1em;
                    }

                    .myswiper2 {
                        width: 60%;
                        margin: 0 auto;
                        position: relative;

                        @include respond-to(mb){
                            width: 100%;
                        }

                        .swiper-slide{
                            display: flex;
                            justify-content: center;
                            align-items: center;
                        }
                        .swiper-button-prev{
                            left: -8em;

                            @include respond-to(mb){
                                left: 0;
                            }
                        }
                        .swiper-button-next{
                            right: -8em;
                            @include respond-to(mb){
                                right: 0;
                            }
                        }

                        .swiper-button-next::after,
                        .swiper-button-prev::after {
                            display: none;
                        }

                        .swiper-button-next,
                        .swiper-button-prev {
                            width: 3em;
                            @include respond-to(mb) {
                                width: 2em;
                            }
                        }
                    }

                    .myswiper1 {
                        margin: 1em auto 0;
                        width: 70%;

                        @include respond-to(mb) {
                            padding: 0;
                            width: 100%;
                        }

                        .swiper-slide {
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            border-radius: 8px;
                            border: 1px solid transparent;
                            width: 100%;
                            height: 5em;
                            background-color: rgba(0, 0, 0, 0.25);
                            box-sizing: border-box;
                            cursor: pointer;

                            img{
                                width: 100%;
                                height: 100%;
                                object-fit: contain;
                            }

                            @include respond-to(mb){
                                background-color: rgba(255,255,255,0.25);
                            }

                            &.swiper-slide-thumb-active {
                                border-color: $color-primary;
                            }
                        }
                    }
                }

				@include respond-to(mb) {
					padding-top: 0;
				}
			}

			.rightArea {
				min-width: 0;

                ::v-deep .quoteBoxContent{
                    width: 100%;
                }

				@include respond-to(mb) {
					.stepBar {
						display: none;
					}
				}
			}
		}
	}
}
</style>