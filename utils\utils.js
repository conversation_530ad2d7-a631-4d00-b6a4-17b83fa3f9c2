import FileSaver from "@/utils/fileSave";
import { colorDiff } from "@/assets/js/colorDiff";
export const getDate = () => {
	var date = new Date();
	var year = date.getFullYear();
	var month = date.getMonth() + 1;
	var day = date.getDate();
	month = month > 9 ? month : "0" + month;
	day = day < 10 ? "0" + day : day;
	return year + "-" + month + "-" + day;
};
export const formatDate = (date) => {
	if (!date) return null;

	const [year, month, day] = date.split("-");
	return `${month}/${day}/${year}`;
};
export const parseDate = (date) => {
	if (!date) return null;

	const [month, day, year] = date.split("/");
	return `${year}-${month.padStart(2, "0")}-${day.padStart(2, "0")}`;
};

export const mergeImg = (picArr) => {
	return new Promise(async (resolve) => {
		let canvas = document.createElement("canvas");
		let context = canvas.getContext("2d");
		let positionX = 0,
			canvasWidth = 0,
			canvasHeight = 0,
			imgArr = [];
		//先加载完图片，定好canvas的宽高
		for (let i = 0; i < picArr.length; i++) {
			const pic = picArr[i];
			let img = await loadImage(pic);
			// 绘制
			canvasWidth += img.width;
			canvasHeight = Math.max(canvasHeight, img.height);
			imgArr.push(img);
		}
		canvas.width = canvasWidth;
		canvas.height = canvasHeight;
		//画图
		for (let i = 0; i < imgArr.length; i++) {
			const img = imgArr[i];
			context.drawImage(img, positionX, 0, img.width, img.height);
			positionX += img.width;
		}
		resolve(canvas.toDataURL("image/png"));
	});
};

export const copyContent = (val) => {
	if (navigator.clipboard) {
		// clipboard api 复制
		navigator.clipboard.writeText(val);
	} else {
		var textarea = document.createElement("textarea");
		textarea.addEventListener("focusin", (e) => e.stopPropagation());
		textarea.setAttribute("readonly", "readonly");
		document.body.appendChild(textarea);
		// 隐藏此输入框
		textarea.style.position = "fixed";
		textarea.style.clip = "rect(0 0 0 0)";
		textarea.style.top = "10px";
		// 赋值
		textarea.value = val;
		// 选中
		textarea.select();
		// 复制
		document.execCommand("copy", true);
		// 移除输入框
		document.body.removeChild(textarea);
	}
};

/**
 * 保存json为Excel文件
 * @param {*} data
 * @param {*} filename  文件名后缀为.xlsx
 */
export function saveJsonToExcel(data, filename) {
	let sheet = XLSX.utils.json_to_sheet(data);
	sheet["!cols"] = [{ wch: 20 }, { wch: 10 }, { wch: 35 }, { wch: 10 }, { wch: 15 }, { wch: 35 }];
	let workbook = {
		SheetNames: ["sheet1"],
		Sheets: {
			sheet1: sheet,
		},
	};

	let wbout = XLSX.write(workbook, {
		bookType: "xlsx",
		bookSST: true,
		type: "array",
	});

	FileSaver.saveAs(new Blob([wbout], { type: "application/octet-stream" }), filename);
}

export function isExternal(path) {
	return /^(https?:|mailto:|tel:)/.test(path);
}

export function dateFormat(fmt, date) {
	if (!date) {
		return "";
	}
	let ret;
	const opt = {
		"Y+": date.getFullYear().toString(), // 年
		"m+": (date.getMonth() + 1).toString(), // 月
		"d+": date.getDate().toString(), // 日
		"H+": date.getHours().toString(), // 时
		"M+": date.getMinutes().toString(), // 分
		"S+": date.getSeconds().toString(), // 秒
		// 有其他格式化字符需求可以继续添加，必须转化成字符串
	};
	for (let k in opt) {
		ret = new RegExp("(" + k + ")").exec(fmt);
		if (ret) {
			fmt = fmt.replace(ret[1], ret[1].length == 1 ? opt[k] : opt[k].padStart(ret[1].length, "0"));
		}
	}
	return fmt;
}

export const getImageBase64 = (image) => {
	const canvas = document.createElement("canvas");
	canvas.width = image.width;
	canvas.height = image.height;
	const ctx = canvas.getContext("2d");
	ctx.drawImage(image, 0, 0, image.width, image.height);
	// 获取图片后缀名
	const extension = image.src.substring(image.src.lastIndexOf(".") + 1).toLowerCase();
	// 某些图片 url 可能没有后缀名，默认是 png
	return canvas.toDataURL("image/" + extension, 1);
};
export const downloadImage = (url, downloadName) => {
	const link = document.createElement("a");
	link.setAttribute("download", downloadName);
	const image = new Image();
	// 添加时间戳，防止浏览器缓存图片
	image.src = url + "?timestamp=" + new Date().getTime();
	// 设置 crossOrigin 属性，解决图片跨域报错
	image.setAttribute("crossOrigin", "Anonymous");
	image.onload = () => {
		link.href = getImageBase64(image);
		link.click();
	};
};

export function newDownFile(fileStr, fileName) {
	let NewUrl = urlAddVersion(fileStr);
	fetch(NewUrl).then((res) =>
		res.blob().then((blob) => {
			const a = document.createElement("a"), // 动态创建a标签，防止下载大文件时，用户没看到下载提示连续点击
				url = window.URL.createObjectURL(blob),
				filename = fileName;
			a.href = url;
			a.download = filename;
			a.click();
			window.URL.revokeObjectURL(url);
		})
	);
}

export const getBase64 = (url, callback) => {
	var Img = new Image(),
		dataURL = "";
	Img.src = url + "?v=" + Math.random();
	Img.onload = function () {
		var canvas = document.createElement("canvas"),
			width = Img.width,
			height = Img.height;
		canvas.width = width;
		canvas.height = height;
		canvas.getContext("2d").drawImage(Img, 0, 0, width, height);
		dataURL = canvas.toDataURL("image/png");
		return callback ? callback(dataURL) : null;
	};
	Img.setAttribute("crossOrigin", "Anonymous");
};

export const getTotalPageNum = (totalRecord, pageSize) => {
	return parseInt((totalRecord + pageSize - 1) / pageSize);
};

// 合成版
/**
 * @desc 函数防抖
 * @param func 目标函数
 * @param wait 延迟执行毫秒数
 * @param immediate true - 立即执行， false - 延迟执行
 */
export const debounce = (func, wait, immediate) => {
	let timer;
	return function () {
		let context = this,
			args = arguments;
		if (timer) clearTimeout(timer);
		if (immediate) {
			let callNow = !timer;
			timer = setTimeout(() => {
				timer = null;
			}, wait);
			if (callNow) {
				func.apply(context, args);
			}
		} else {
			timer = setTimeout(() => {
				func.apply(context, args);
			}, wait);
		}
	};
};

/**
 * @desc 函数节流
 * @param func 函数
 * @param wait 延迟执行毫秒数
 */
export const throttle = (func, wait) => {
	let previous = 0;
	return function () {
		let context = this;
		let args = arguments;
		let now = Date.now();
		if (now - previous > wait) {
			func.apply(context, args);
			previous = now;
		}
	};
};

/* 银行卡类型判断*/
export const creditCardType = (cardNumber) => {
	let creditCartTypes = {
		// Visa: 4开头 长度16位
		VI: [new RegExp("^4[0-9]{12}([0-9]{3})?$"), new RegExp("^[0-9]{3}$"), true],

		// Express: 34、37开头 长度15位
		// 'AE': [new RegExp('^3[47][0-9]{13}$'), new RegExp('^[0-9]{4}$'), true],
		AE: [new RegExp("^(?:3[47][0-9]{13})$"), new RegExp("^[0-9]{4}$"), true],
	};
	let card = {};
	// 默认
	if (cardNumber) {
		card.cardName = "";
		card.cardImage = "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20220809/20220809ZJ2y3W45.png";
		card.cardIcon = "";
	}
	// Visa
	if (creditCartTypes["VI"][0].test(cardNumber)) {
		card.cardName = "Visa";
		card.cardImage = "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20220730/20220730PiHQ8BXi.png";
		card.cardIcon = "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20220730/20220730BCzYtfXk.png";
	}
	// Express 待改
	if (creditCartTypes["AE"][0].test(cardNumber)) {
		card.cardName = "American Express";
		card.cardImage = "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20220731/20220731y37NMBh7.png";
		card.cardIcon = "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20220730/20220730QYZXkZ3G.png";
	}
	return card;
};

// 兼容写法，获取滚动条的高度
export const getWindowScrollTop = () => {
	let scroll_top = 0;
	if (document.documentElement && document.documentElement.scrollTop) {
		scroll_top = document.documentElement.scrollTop;
	} else if (document.body) {
		scroll_top = document.body.scrollTop;
	}
	return scroll_top;
};

export const scrollToViewCenter = (el, offset = 0) => {
	const { top, height } = el.getBoundingClientRect();
	// 元素的中心高度
	const elCenter = top + height / 2;
	// 窗口的中心高度
	const center = window.innerHeight / 2;
	window.scrollTo({
		top: getWindowScrollTop() - (center - elCenter) - offset,
		behavior: "smooth",
	});
};

export const scrollToViewTop = (el, offset = 0) => {
	if (!el) {
		return;
	}
	const { top, height } = el.getBoundingClientRect();
	window.scrollTo({
		top: getWindowScrollTop() + top - 100 - offset,
		behavior: "smooth",
	});
};

export const getDomScrollTop = (dom) => {
	let scroll_top = 0;
	if (dom == window) {
		if (document.documentElement && document.documentElement.scrollTop) {
			scroll_top = document.documentElement.scrollTop;
		} else if (document.body) {
			scroll_top = document.body.scrollTop;
		}
	} else {
		scroll_top = dom.scrollTop;
	}

	return scroll_top;
};

export const domScrollFn = (el, position = "center", offset = 0, self = false, parent = null) => {
	let posList = ["start", "center", "end"];
	if (!posList.includes(position)) {
		console.log("position 参数错误 --start,center,end");
		return;
	}
	let element = null;
	let parentElement = null;
	try {
		if (parent) parentElement = document.querySelector(parent);
	} catch (error) {
		if (!parentElement && parent.startsWith("#")) {
			parentElement = document.getElementById("" + parent.slice(1));
		}
	}
	try {
		element = document.querySelector(el);
	} catch (error) {
		if (!element && el.startsWith("#")) {
			element = document.getElementById("" + el.slice(1));
		}
	}

	if (element) {
		const { top, bottom, height } = element.getBoundingClientRect();
		const windowHeight = window.innerHeight;
		const domCenter = top + height / 2;
		const center = windowHeight / 2;
		let positionTop = 0;
		switch (position) {
			case "start":
				positionTop = top;
				break;
			case "center":
				positionTop = domCenter - center;
				break;
			case "end":
				positionTop = bottom - windowHeight;
				break;
		}
		//offset 正值往上移，负值往下移
		if (self) offset = height + offset;
		let dom = window;
		if (parentElement) dom = parentElement;
		dom.scrollTo({
			top: getDomScrollTop(dom) + positionTop + offset,
			behavior: "smooth",
		});
		return getDomScrollTop(dom) + positionTop + offset;
	}
};

/**
 * 根据文件类型生成随机文件名
 * @param file
 */
export const generateRandomFileName = (file) => {
	let randomName = getRandomString(6);
	let randomNameSuffix = getFileSuffix(file.name);
	return randomName + randomNameSuffix;
};

export const getFilePrefix = (filePath) => {
	if(typeof filePath !== "string"){
		return ""
	}
	const pos = filePath.lastIndexOf(".");
	let prefix = "";
	if (pos !== -1) {
		prefix = filePath.substring(0, pos);
	}
	return prefix;
};

export const getFileSuffix = (filePath) => {
	if(typeof filePath !== "string"){
		return ""
	}
	const pos = filePath.lastIndexOf(".");
	let suffix = "";
	if (pos !== -1) {
		suffix = filePath.substring(pos);
		const end = suffix.indexOf("?");
		if (end !== -1) {
			let result = "";
			result = suffix.substring(0, end);
			return result;
		} else {
			return suffix;
		}
	}
};

function isNumber(num) {
	return /^[0-9]+.?[0-9]*$/.test(num);
}

export const exchangeRates = (price, rate) => {
	if (!isNumber(price)) {
		return 0;
	}
	return (price * rate).toFixed(2);
};

export const isImageType = (str) => {
	if (!str) {
		return false;
	}
	let newStr = getFileSuffix(str);
	if(!newStr)return false
	return [".png", ".jpg", ".jpeg", ".bmp", ".gif", ".webp", ".psd", ".svg", ".tiff"].indexOf(newStr.toLowerCase()) !== -1;
};

export const isVideoType = (str) => {
	if (!str) {
		return false;
	}
	let newStr = getFileSuffix(str);
	if(!newStr)return false
	return [".avi", ".mp4", ".rmvb", ".mov"].indexOf(newStr.toLowerCase()) !== -1;
};

//过滤标签
export function filterHTML(html) {
	return html.replace(/<[^>]*>/g); //去除<...></...>标签
}

//获取随机长度字符串
export const getRandomString = (len) => {
	const date = new Date();
	let month = date.getMonth() + 1;
	if (month >= 1 && month <= 9) {
		month = "0" + month;
	}
	let strDate = date.getDate();
	if (strDate >= 0 && strDate <= 9) {
		strDate = "0" + strDate;
	}
	const currentDate = date.getFullYear() + month + strDate;

	len = len || 32;
	const chars = "ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678";
	const maxPos = chars.length;
	let pwd = "";
	for (let i = 0; i < len; i++) {
		pwd += chars.charAt(Math.floor(Math.random() * maxPos));
	}
	return currentDate + pwd;
};

export const generateUUID = () => {
	let d = new Date().getTime();
	let uuid = "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, function (c) {
		let r = (d + Math.random() * 16) % 16 | 0;
		d = Math.floor(d / 16);
		return (c == "x" ? r : (r & 0x3) | 0x8).toString(16);
	});
	return uuid;
};

export const getUrlParams = (url = "") => {
	let obj = {};
	if (url && url.indexOf("?") !== -1) {
		// 判断是否带有?参数
		let params = url.split("?") || [];
		if (params.length === 2) {
			params = params[1].split("&");
			// 获取所有的参数并组装成对象
			for (let i = 0; i < params.length; i++) {
				let tmp = params[i].split("=");
				obj[tmp[0]] = tmp[1];
			}
		}
	}
	return obj;
};

function roundNumber(num, scale) {
	if (!("" + num).includes("e")) {
		return +(Math.round(num + "e+" + scale) + "e-" + scale);
	} else {
		var arr = ("" + num).split("e");
		var sig = "";
		if (+arr[1] + scale > 0) {
			sig = "+";
		}
		return +(Math.round(+arr[0] + "e" + sig + (+arr[1] + scale)) + "e-" + scale);
	}
}

function checkType(reg, str) {
	return reg.test(str);
}

function checkSize(num, maxNum) {
	return num < maxNum;
}

export const checkImgType = (name) => {
	return checkType(/\.(jpg|jpeg|png|gif|bmp|BMP|GIF|JPG|JPEG|PNG)$/, name);
};

export const checkImgSize = (size) => {
	return checkSize(size, 80 * 1024 * 1024);
};

export const round2 = (num) => {
	return roundNumber(num, 2);
};

export const deepClone = (obj, clonedObjects = new WeakMap()) => {
	if (obj === null || typeof obj !== "object") {
		return obj;
	}

	if (clonedObjects.has(obj)) {
		return clonedObjects.get(obj);
	}

	const clonedObj = Array.isArray(obj) ? [] : {};

	clonedObjects.set(obj, clonedObj);

	for (let key in obj) {
		if (obj.hasOwnProperty(key)) {
			clonedObj[key] = deepClone(obj[key], clonedObjects);
		}
	}
	return clonedObj;
};

export const concurRequest = (urls, maxNum, iteratorFn) => {
	return new Promise((resolve) => {
		if (urls.length === 0) {
			resolve([]);
			return [];
		}
		const results = [];
		let index = 0;
		let count = 0;

		async function request() {
			if (index === urls.length) {
				return;
			}
			const i = index;
			const url = urls[index];
			index++;
			try {
				results[i] = await iteratorFn(url);
			} catch (err) {
				results[i] = err;
			} finally {
				//判断所有请求是否已经完成
				count++;
				if (count === urls.length) {
					resolve(results);
				}
				request();
			}
		}

		const times = Math.min(maxNum, urls.length);
		for (let i = 0; i < times; i++) {
			request();
		}
	});
};

/**
 * @description 使用鼠标拖拽div，实现横向、纵向滚动
 * @param el 被拖拽滚动的元素（产生滚动条的元素）
 */
export const addDragable = function (el, cb, cb2) {
	let startX = 0; // el的scroll横向初始位置
	let gapX = 0; // 鼠标点击时的横向初始位置
	let startY = 0; // el的scroll纵向向初始位置
	let gapY = 0; // 鼠标点击时的纵向初始位置
	el.addEventListener("mousedown", start);
	el.addEventListener("mouseleave", stop); // 移除时解除事件

	function start(event) {
		// 判断是否点击鼠标左键
		if (event.button == 0) {
			gapX = event.clientX;
			gapY = event.clientY;
			startX = el.scrollLeft;
			startY = el.scrollTop;
			el.addEventListener("mousemove", move); // document
			el.addEventListener("mouseup", stop);
		}
		// event.preventDefault(); // 阻止默认事件或冒泡 如拖拽时选中文本
		return false;
	}

	function move(event) {
		// 移动时的坐标 - 鼠标左键点击时的坐标 = 鼠标移动的相对距离
		var left = event.clientX - gapX;
		var top = event.clientY - gapY;
		// 滚动条初始坐标 - 移动的相对距离 = 应该滚动后的坐标
		el.scrollTo(startX - left, startY - top); // 横向 纵向
		if (typeof cb === "function") {
			let site = el.scrollWidth == el.scrollLeft + el.clientWidth ? 0 : left > 0 ? 1 : -1;
			cb(site);
		}
		return false;
	}

	function stop() {
		if (typeof cb2 === "function") {
			cb2();
		}
		// 鼠标松开，解除绑定
		el.removeEventListener("mousemove", move, false);
		el.removeEventListener("mouseup", stop, false);
	}
};

//过滤对象
export const filterObj = (obj, filterKey) => {
	const _obj = obj;
	filterKey.forEach((x) => delete _obj[x]);
	return _obj;
};

export const loadScript = (url) => {
	return new Promise(function (resolve, reject) {
		let script = document.createElement("script");
		script.src = url;
		script.onload = function () {
			resolve();
		};
		script.onerror = function () {
			reject(new Error("Failed to load script: " + url));
		};
		document.head.appendChild(script);
	});
};

export const replacePicPrefix = (str) => {
	if (!str) {
		return "";
	}
	return str.replace(/static-oss.gs-souvenir.com/i, "oss-static-cn.liyi.co");
};

export const withRetry = (fn, maxAttempts) => {
	let attempts = 0;

	return function retry() {
		if (attempts >= maxAttempts) {
			console.log("Exceeded maximum attempts.");
			return;
		}

		attempts++;

		try {
			return fn();
		} catch (error) {
			console.log("Attempt " + attempts + " failed: " + error.message);
			return retry();
		}
	};
};

export const withRetryAsync = (fn, maxAttempts) => {
	let attempts = 0;
	return async function retry() {
		if (attempts >= maxAttempts) {
			console.log("Exceeded maximum attempts.");
			return;
		}
		attempts++;
		try {
			return await fn();
		} catch (error) {
			return retry();
		}
	};
};

export const base64toBlob = (dataurl) => {
	var arr = dataurl.split(","),
		mime = arr[0].match(/:(.*?);/)[1],
		bstr = atob(arr[1]),
		n = bstr.length,
		u8arr = new Uint8Array(n);
	while (n--) {
		u8arr[n] = bstr.charCodeAt(n);
	}
	return new Blob([u8arr], { type: mime });
};

// 图片转base64
export const readFileAsBase64 = (file) => {
	return new Promise((resolve, reject) => {
		let reader = new FileReader();
		reader.onload = function (e) {
			resolve(e.target.result);
		};
		reader.onerror = function (error) {
			reject(error);
		};
		reader.readAsDataURL(file); // 将文件读取为DataURL
	});
};

export const isBase64 = (url) => {
	return url.indexOf("data:image") > -1 ? true : false;
};

export const urlAddVersion = (url) => {
	if (!url) {
		return;
	}
	if (isBase64(url)) {
		return url;
	}
	const urlObject = new URL(url);
	const searchParams = new URLSearchParams(urlObject.search);
	// 添加版本号参数
	searchParams.set("versions", generateUUID());
	// 重新构建 URL
	urlObject.search = searchParams.toString();
	console.log(urlObject.toString());
	return urlObject.toString();
};

/*
	根据rgb色转换16进制
	参数1：rgb值||string
	demo:
	import { rgbToHex } from "@/utils/utils";
	console.log(rgbToHex("rgb(255,255,255)"));
	结果：
	#ffffff
*/
export const rgbToHex = (rgb) => {
	// 分割RGB值
	let rgbArray = rgb.match(/\d+/g);
	let r = parseInt(rgbArray[0]);
	let g = parseInt(rgbArray[1]);
	let b = parseInt(rgbArray[2]);
	// 将RGB转换为十六进制
	let hex = ((r << 16) | (g << 8) | b).toString(16);
	hex = "#" + ("000000" + hex).slice(-6); // 补全至6位
	return hex;
};

/*
	根据16进制颜色转换rgb色
	参数1：16进制值||string
	demo:
	import { colorRgb } from "@/utils/utils";
	console.log(colorRgb("#ffffff"));
	结果：
	rgb(255,255,255)
*/
export const colorRgb = (sColor) => {
	if (!sColor) {
		return false;
	}
	sColor = sColor.toLowerCase();
	//十六进制颜色值的正则表达式
	let reg = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/;
	// 如果是16进制颜色
	if (sColor && reg.test(sColor)) {
		if (sColor.length === 4) {
			let sColorNew = "#";
			for (let i = 1; i < 4; i += 1) {
				sColorNew += sColor.slice(i, i + 1).concat(sColor.slice(i, i + 1));
			}
			sColor = sColorNew;
		}
		//处理六位的颜色值
		let sColorChange = [];
		for (let i = 1; i < 7; i += 2) {
			sColorChange.push(parseInt("0x" + sColor.slice(i, i + 2)));
		}
		return "rgb(" + sColorChange.join(",") + ")";
	}
	return sColor;
};

export const dataURLtoFile = (dataurl, filename) => {
	let arr = dataurl.split(","),
		mime = arr[0].match(/:(.*?);/)[1],
		bstr = atob(arr[1]),
		n = bstr.length,
		u8arr = new Uint8Array(n);
	while (n--) {
		u8arr[n] = bstr.charCodeAt(n);
	}
	return new File([u8arr], filename + ".png", { type: "image/png" });
};

// 预加载图片
export const loadImage = (url) => {
	return new Promise((resolve, reject) => {
		let img = new Image();
		img.onload = () => resolve(img);
		img.onerror = reject;
		img.src = url;
		img.crossOrigin = "Anonymous";
	});
};

export const isSimilarColor = (color1, color2, threshold = 30) => {
	// 计算 RGB 分量的差异
	let similarity = Math.sqrt(Math.pow(color1[0] - color2[0], 2) + Math.pow(color1[1] - color2[1], 2) + Math.pow(color1[2] - color2[2], 2));
	return similarity <= threshold;
};

export const rgbToArr = (rgbColor) => {
	if (!rgbColor) {
		return false;
	}
	let rgbaValues = rgbColor.substring(rgbColor.indexOf("(") + 1, rgbColor.lastIndexOf(")")).split(",");
	// 将字符串转换为数字，并存储到数组中
	return rgbaValues.map(function (value) {
		return parseFloat(value.trim());
	});
};

/**
 *
 * @param url
 * @param colorObj
 * @param {Function} callback
 */
//改变图片颜色
export const changeImgColor = (url, colorObj, callback) => {
	loadImage(url).then((myImage) => {
		let oldColor = rgbToArr(colorRgb(colorObj.oldColor));
		let newColor = rgbToArr(colorRgb(colorObj.newColor));
		let canvas = document.createElement("canvas");
		let ctx = canvas.getContext("2d");
		let originW = myImage.width;
		let originH = myImage.height;
		canvas.width = originW;
		canvas.height = originH;
		ctx.drawImage(myImage, 0, 0);
		let myImageData = ctx.getImageData(0, 0, originW, originH);
		let data = myImageData.data;
		let picLength = data.length;
		// 替换相似颜色
		for (let i = 0; i < picLength; i += 4) {
			let color = [data[i], data[i + 1], data[i + 2]];
			const diff = colorDiff(
				{
					r: color[0],
					g: color[1],
					b: color[2],
				},
				{
					r: oldColor[0],
					g: oldColor[1],
					b: oldColor[2],
				}
			);
			if (diff <= 5) {
				data[i] = newColor[0];
				data[i + 1] = newColor[1];
				data[i + 2] = newColor[2];
			}
		}
		ctx.putImageData(myImageData, 0, 0);
		let dataUrl = canvas.toDataURL();
		if (typeof callback === "function") {
			callback(dataUrl);
		}
	});
};

// 获取图片原始宽高
export const getImageSize = (url) => {
	return new Promise((resolve, reject) => {
		let image = new Image();
		image.onload = () => {
			resolve({
				width: image.width,
				height: image.height,
			});
		};
		image.onerror = () => {
			reject(new Error("error"));
		};
		image.src = url;
	});
};

//将图片改为指定颜色单色图
export const removeImgColor = (url, color, callback) => {
	let canvas = document.createElement("canvas");
	let ctx = canvas.getContext("2d");
	let img = new Image();
	img.onload = () => {
		let originW = img.width;
		let originH = img.height;
		canvas.width = originW;
		canvas.height = originH;
		//左右镜像翻转
		// ctx.translate(canvas.width, 0);
		// ctx.scale(-1, 1);
		ctx.drawImage(img, 0, 0);
		let myImageData = ctx.getImageData(0, 0, originW, originH);
		let picLength = myImageData.data.length;
		let colorChange = colorRgb(color)
			.substring(4, colorRgb(color).length - 1)
			.split(",");
		for (let i = 0; i < picLength; i += 4) {
			myImageData.data[i] = colorChange[0];
			myImageData.data[i + 1] = colorChange[1];
			myImageData.data[i + 2] = colorChange[2];
		}
		ctx.putImageData(myImageData, 0, 0);
		let dataUrl = canvas.toDataURL();
		if (typeof callback === "function") {
			callback(dataUrl);
		}
	};
	img.src = url;
	img.crossOrigin = "Anonymous";
};

export const pdfToImg = (src) => {
	return new Promise(function (resolve) {
		let pdfjsLib = window["pdfjsLib"];
		pdfjsLib.GlobalWorkerOptions.workerSrc = "https://cdn.jsdelivr.net/npm/pdfjs-dist@3.1.81/build/pdf.worker.min.js";
		let loadingPdf = pdfjsLib.getDocument(src);
		loadingPdf.promise.then((pdf) => {
			let pageNumber = 1;
			pdf.getPage(pageNumber)
				.then((page) => {
					let scale = 1; //缩放倍数，1表示原始大小
					let viewport = page.getViewport({ scale: scale });
					let canvas = document.createElement("canvas");
					let context = canvas.getContext("2d"); //创建绘制canvas的对象
					canvas.height = viewport.height; //定义canvas高和宽
					canvas.width = viewport.width;
					let renderContext = {
						canvasContext: context,
						viewport: viewport,
					};
					let renderTask = page.render(renderContext);
					renderTask.promise.then(function () {
						resolve(canvas.toDataURL());
					});
				})
				.catch((err) => {});
		});
	});
};

/*
  解析图像中的颜色RGB值
	参数1：传入base64图片
	参数2：配置项 = {
	  ignoreArr:["rgb(255,255,255)", "rgb(0,0,0)"],//指定颜色值  可以为空||[]
		keepCount:9,  //保留最大主色数
		removeSimilarColors:true, //是否移除相似色.  推荐true.  匹配度精准些
	}
  demo:
	let res = "data:image/png;base64,iVBORw0KGgoAAAANS..."
	new Promise((resolve) => {
		analyzeImageColor(res, {
			ignoreArr: [],
			keepCount: 9,
			removeSimilarColors: true,
		}).then((r) => {
			console.log(r);
			resolve(res);
		});
	});

	结果：
	[
		{color: "rgb(236,234,244)",count: 3278},
		{color: "rgb(49,64,133)",count: 156}
	]
*/
export const analyzeImageColor = (url, options) => {
	return new Promise((resolve) => {
		const colorWorker = new Worker("/worker/getImageColor.worker.js");
		colorWorker.onmessage = (event) => {
			resolve(event.data);
		};
		colorWorker.postMessage({
			url,
			...options,
		});
	});
};

export const removeImageColor = (url, options) => {
	return new Promise((resolve) => {
		// Load the image onto the canvas
		const defaultOptions = {
			diff: 10,
			color: "rgb(255,255,255)",
		};
		options = Object.assign({}, defaultOptions, options);
		let canvas = document.createElement("canvas");
		let ctx = canvas.getContext("2d");
		let image = new Image();
		image.onload = function () {
			canvas.width = image.width;
			canvas.height = image.height;
			// Draw the image onto the canvas
			ctx.drawImage(image, 0, 0);
			// Get the image data
			let imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
			let data = imageData.data;
			let backgroundColor = rgbToArr(options.color); // Example: white background
			for (let i = 0; i < data.length; i += 4) {
				let red = data[i];
				let green = data[i + 1];
				let blue = data[i + 2];
				const myColorDiff = colorDiff(
					{
						r: red,
						g: green,
						b: blue,
					},
					{
						r: backgroundColor[0],
						g: backgroundColor[1],
						b: backgroundColor[2],
					}
				);
				if (myColorDiff <= defaultOptions.diff) {
					data[i + 3] = 0;
				}
			}
			ctx.putImageData(imageData, 0, 0);
			resolve(canvas.toDataURL());
		};
		image.src = url;
		image.crossOrigin = "Anonymous";
	});
};

// 管理历史状态的对象
export class HistoryManager {
	constructor() {
		this.history = [];
		this.currentIndex = -1;
	}

	addState(state) {
		// 截取当前位置之后的历史记录，插入新状态
		this.history = this.history.slice(0, this.currentIndex + 1);
		this.history.push(state);
		this.currentIndex = this.history.length - 1;
	}

	undo() {
		if (this.currentIndex > 0) {
			this.currentIndex--;
			return this.history[this.currentIndex];
		}
		return null; // 没有可撤销的操作
	}

	redo() {
		if (this.currentIndex < this.history.length - 1) {
			this.currentIndex++;
			return this.history[this.currentIndex];
		}
		return null; // 没有可重做的操作
	}
}
export const setOpacity = (hexColor, opacity = 1) => {
	if (!hexColor) return false;
	const { r, g, b } = {
		r: parseInt(hexColor.slice(1, 3), 16),
		g: parseInt(hexColor.slice(3, 5), 16),
		b: parseInt(hexColor.slice(5, 7), 16),
	};
	return `rgba(${r}, ${g}, ${b}, ${opacity})`;
};


// 锁定滚动条
export const lockScroll =()=>{
	document.documentElement.style.overflow = 'hidden'; // 锁定 HTML 元素的滚动
}

// 解锁滚动条
export const unlockScroll =()=>{
	document.documentElement.style.overflow = ''; // 锁定 HTML 元素的滚动
}

// findInTree
export const findInTree = (tree, predicate, childrenKey = "children") => {
	// 1. 检查当前节点是否满足条件
	if (predicate(tree)) {
		return tree;
	}

	// 2. 获取子节点数组
	const children = tree[childrenKey];
	if (!Array.isArray(children)) {
		return null;
	}

	// 3. 递归查找子节点
	for (const child of children) {
		const result = findInTree(child, predicate, childrenKey);
		if (result) {
			return result;
		}
	}

	return null;
}

// findInTreeArray
export const findInTreeArray = (treeArray, predicate, childrenKey = "children") => {
	if (!Array.isArray(treeArray)) {
		return null;
	}

	for (const item of treeArray) {
		const result = findInTree(item, predicate, childrenKey);
		if (result) {
			return result;
		}
	}

	return null;
}
