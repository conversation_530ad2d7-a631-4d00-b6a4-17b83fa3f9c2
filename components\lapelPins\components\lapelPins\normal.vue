<template>
	<div class="normal">
		<div class="optionsGrid">
			<div class="optionBox" v-for="(item, index) in itemData.childList" :key="index" :class="{ selected: item.selected }">
				<div class="imgWrap">
					<img :src="getImg(item)" alt="" class="img" />
					<b class="icon-fangda4" @click="fangdaImg(index)"></b>
				</div>
				<div class="optionInfo">
					<div class="optionName">{{ item.alias }}</div>
					<div class="optionPrice" :class="{ free: citem == 'Free' }" v-for="(citem, index) in getPrice(item)" :key="index">{{ citem }}</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	name: "Options",
	components: {},
	props: {
		itemData: {
			type: Object,
			default: () => {},
		},
	},
	data() {
		return {};
	},
	methods: {
		getImg(item) {
			if (item.imageJson) {
				try {
					let imgUrl = JSON.parse(item.imageJson);
					return imgUrl[0].url;
				} catch (e) {
					console.error("Error parsing imageJson:", e);
					return item.imageUrl || "";
				}
			} else {
				return item.imageUrl || "";
			}
		},
		getPrice(item) {
			if (item.priceInfo && item.priceInfo?.priceShow) {
				return item.priceInfo["priceShow"];
			}
			return "Free";
		},
		selectOption(item) {
			// 实现选择逻辑
			this.$emit("option-selected", item);
		},
		fangdaImg(index) {
			this.$viewerApi({
				images: this.viewImgList,
				options: {
					initialViewIndex: index,
					initialCoverage: 3,
				},
			});
		},
	},
	created() {},
	mounted() {},
	computed: {
		viewImgList() {
			return this.itemData.childList.map((item) => {
				return this.getImg(item);
			});
		},
	},
	watch: {},
};
</script>
<style scoped lang="scss">
.normal {
	width: 100%;

	.optionsGrid {
		width: 100%;
		display: grid;
		grid-template-columns: repeat(5, 1fr);
		gap: 20px;

		.optionBox {
			position: relative;
			background: #ffffff;
			border-radius: 8px;
			border: 1px solid #d9dbdd;
			display: flex;
			flex-direction: column;
			align-items: center;
			gap: 10px;
			cursor: pointer;
			transition: all 0.3s ease;
			padding-bottom: 10px;

			&.selected {
				border: 2px solid #4a90e2;
			}

			.imgWrap {
				width: 100%;
				display: flex;
				align-items: center;
				justify-content: center;
				border-radius: 8px 8px 0 0;
				overflow: hidden;

				.img {
					max-width: 100%;
					max-height: 100%;
					object-fit: contain !important;
				}
				b {
					cursor: pointer;
					position: absolute;
					top: 10px;
					right: 10px;
					color: #ccc;
				}
			}

			.optionInfo {
				width: 100%;
				display: flex;
				flex-direction: column;
				align-items: center;
				gap: 5px;

				.optionName {
					font-size: 18px;
					color: #333333;
					text-align: center;
				}

				.optionPrice {
					font-size: 16px;
					color: #666666;

					&.free {
						color: #d81e06;
					}
				}
			}
		}

		@include respond-to(mb) {
			grid-template-columns: repeat(3, 1fr);
			gap: 10px;
			.optionBox {
				.optionInfo {
					.optionName {
						font-size: 14px;
						font-weight: bold;
						color: #333333;
						text-align: center;
					}

					.optionPrice {
						font-size: 12px;
						font-weight: bold;
						color: #666666;

						&.free {
							color: #d81e06;
						}
					}
				}
			}
		}
	}
}
</style>
