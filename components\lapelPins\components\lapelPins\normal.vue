<template>
	<div class="normal">
		<div class="optionsGrid" :class="itemData.styleName">
			<div class="optionBox" v-for="(item, index) in itemData.childList" :key="index" :class="{ selected: item.selected }">
				<div class="imgWrap">
					<img :src="getImg(item)" :style="{ aspectRatio: aspectRatio + '', ...imgStyleObj }" alt="" class="img" />
					<b class="icon-fangda4" @click="fangdaImg(index)"></b>
				</div>
				<div class="optionInfo">
					<div class="optionName">{{ item.alias2 || item.alias }}</div>
					<div class="optionPrice" :class="{ free: citem == 'Free' }" v-for="(citem, index) in getPrice(item)" :key="index">{{ citem }}</div>
					<div class="optionTips">
						{{ item.tips }}
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	name: "normal",
	components: {},
	props: {
		itemData: {
			type: Object,
			default: () => {},
		},
		aspectRatio: {
			type: [Number, String],
			default: "",
		},
		imgStyleObj: {
			type: Object,
			default: () => {},
		},
		className: {
			type: String,
			default: "",
		},
	},
	data() {
		return {};
	},
	methods: {
		getImg(item) {
			if (item.imageJson) {
				try {
					let imgUrl = JSON.parse(item.imageJson);
					return imgUrl[0].url;
				} catch (e) {
					console.error("Error parsing imageJson:", e);
					return item.imageUrl || "";
				}
			} else {
				return item.imageUrl || "";
			}
		},
		getPrice(item) {
			if (item.priceInfo && item.priceInfo?.priceShow) {
				return item.priceInfo["priceShow"];
			}
			return "Free";
		},
		selectOption(item) {
			// 实现选择逻辑
			this.$emit("option-selected", item);
		},
		fangdaImg(index) {
			let zoomNum = 2.2;
			if (this.itemData.paramName.includes("Upgrades")) zoomNum = 1.5;
			if (this.itemData.paramName.includes("Jackets")) zoomNum = 1.2;
			if (this.itemData.paramName.includes("Backboard-fd")) zoomNum = 1.2;
			if (this.itemData.paramName.includes("Backboard Color-fd")) zoomNum = 1.2;
			if (this.itemData.paramName.includes("Options-fd")) zoomNum = 0.4;
			if (this.isMobile) {
				zoomNum = 1;
				if (this.itemData.paramName.includes("Options-fd")) zoomNum = 0.18;
				if (this.itemData.paramName.includes("Jackets")) zoomNum = 0.4;
				if (this.itemData.paramName.includes("Backboard-fd")) zoomNum = 0.5;
				if (this.itemData.paramName.includes("Color-fd")) zoomNum = 0.5;
			}
			const $viewer = this.$viewerApi({
				images: this.viewImgList,
				options: {
					initialViewIndex: index,
					initialCoverage: 0,
					viewed: function () {
						$viewer.zoomTo(zoomNum);
					},
				},
			});
			return $viewer;
		},
	},
	created() {},
	mounted() {},
	computed: {
		isMobile() {
			return this.$store.getters.isMobile;
		},
		viewImgList() {
			return this.itemData.childList.map((item) => {
				return this.getImg(item);
			});
		},
	},
	watch: {},
};
</script>
<style scoped lang="scss">
.normal {
	width: 100%;

	.optionsGrid {
		width: 100%;
		display: grid;
		grid-template-columns: repeat(5, 1fr);
		gap: 20px;
		&.style2_1 {
			grid-template-columns: repeat(2, 1fr);
		}
		&.hasTip {
			.optionBox {
				.optionInfo {
					.optionTips {
						display: flex;
					}
				}
			}
		}

		.optionBox {
			position: relative;
			background: #ffffff;
			border: 1px solid #f0f0f0;
			border-radius: 8px;
			display: flex;
			flex-direction: column;
			align-items: center;
			gap: 10px;
			cursor: pointer;
			transition: all 0.3s ease;
			padding-bottom: 10px;

			&.selected {
				border: 2px solid #4a90e2;
			}

			.imgWrap {
				width: 100%;
				display: flex;
				align-items: center;
				justify-content: center;
				border-radius: 8px 8px 0 0;
				overflow: hidden;

				.img {
					max-width: 100%;
					max-height: 100%;
					object-fit: contain !important;
				}
				b {
					cursor: pointer;
					position: absolute;
					top: 10px;
					right: 10px;
					color: #ccc;
					font-size: 16px;
				}
			}

			.optionInfo {
				width: 100%;
				display: flex;
				flex-direction: column;
				align-items: center;
				gap: 5px;
				padding: 0 6px;

				.optionName {
					font-size: 18px;
					color: #333333;
					text-align: center;
				}

				.optionPrice {
					font-size: 16px;
					color: #666666;
					text-align: center;

					&.free {
						color: #d81e06;
					}
				}

				.optionTips {
					display: none;
					font-size: 16px;
					color: #999999;
				}
			}
		}

		@include respond-to(mb) {
			grid-template-columns: repeat(3, 1fr);
			gap: 10px;
			&.style2_1 {
				grid-template-columns: 1fr;
				.optionBox {
					border: 1px solid #f0f0f0;
				}
			}

			&.style5_2 {
				grid-template-columns: repeat(2, 1fr);
			}
			&.style5_2_0 {
				grid-template-columns: repeat(2, 1fr);
				.optionBox {
					border: 1px solid #f0f0f0;
				}
			}
			.optionBox {
				gap: 6px;
				padding-bottom: 6px;
				border: none;
				.imgWrap {
					b {
						top: 5px;
						right: 5px;
						font-size: 12px;
					}
				}
				.optionInfo {
					text-align: center;
					.optionName {
						font-size: 14px;
						font-weight: bold;
						color: #333333;
						text-align: center;
					}

					.optionPrice {
						font-size: 12px;
						font-weight: bold;
						color: #666666;

						&.free {
							color: #d81e06;
						}
					}

					.optionTips {
						font-size: 12px;
					}
				}
			}
		}
	}
}
</style>
