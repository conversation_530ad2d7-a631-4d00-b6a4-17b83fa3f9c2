<template>
	<div class="qtyAndBtn">
		<div class="q1 active">
			<!-- <div class="circle3 rounded-circle">
				 <i class="el-icon-check checkIcon"></i> 
			</div> -->
			<p class="normal-text">{{ dialogItem.alias }}</p>
		</div>
		<template v-if="!noQty">
			<div class="q2">{{ lang.p18 }} {{ dialogItem.alias }} {{ lang.per }} {{ lang.p37 }}.</div>
			<div class="q3">
				<div>{{ filterTitle(dialogItem.paramName) }}</div>
				<div class="item-wrap" v-for="(item, index) in 5" :key="index" @click="selectQty(item)" :class="{ active: dialogItem.selectQty == item }">
					<div class="item normalBorder">
						<medals-check-icon class="absolute-top-right"></medals-check-icon>
						<div v-if="dialogItem.paramName == 'Rhinestone'" class="imgWrap">
							<span v-if="item == 1">{{ item }} {{ lang.pc }}</span>
							<span v-else>{{ item }} {{ lang.pcs }}</span>
						</div>
						<div v-else class="imgWrap">
							<span v-if="item == 1">{{ item }} {{ lang.Color }}</span>
							<span v-else>{{ item }} {{ lang.Colors }}</span>
						</div>
						<div class="textWrap">
							<p class="normal-text">+{{ currencySymbol }} {{ getPriceFun(dialogItem, item, currencyRate) }}/{{ lang.pc }}</p>
						</div>
						<div class="circle">
							<div class="inner-circle"></div>
						</div>
					</div>
				</div>
				<div class="item-wrap selfInput" @click="selectCustomQty" :class="{ active: dialogItem.inputQty }">
					<div class="item normalBorder">
						<medals-check-icon class="absolute-top-right"></medals-check-icon>
						<div class="imgWrap">
							<el-input-number v-model="dialogItem.inputQty" @change="updateQty" size="small" :precision="0" :controls="false" :placeholder="lang.customQty"></el-input-number>
							<b class="icon-edit--fill editIcon"></b>
						</div>
						<div class="textWrap">
							<p class="normal-text">+{{ currencySymbol }} {{ getPriceFun(dialogItem, dialogItem.inputQty, currencyRate) }}/{{ lang.pc }}</p>
						</div>
						<div class="circle">
							<div class="inner-circle"></div>
						</div>
					</div>
				</div>
			</div>
		</template>

		<div class="q4" v-if="!noComment">
			<!-- <div>{{ lang.Comments }}</div> -->
			<div>Enter the {{ dialogItem.alias }} you need.</div>
			<div>
				<!-- :placeholder="device === 'pc' ? lang.Enter + ' ' + dialogItem.alias + ' ' + lang.p24 + filterPlaceHolder(dialogItem.paramName) : ''"  -->
				<el-input type="textarea" :rows="2" v-model="dialogItem.remark"></el-input>
			</div>
		</div>
		<div class="q5">
			<QuoteBtn :disabled="disabledBtn()" style="margin-right: 10px" @click.native="confirm('next')">
				{{ lang.SelectNextStep }}
			</QuoteBtn>
			<QuoteBtn :disabled="disabledBtn()" v-show="!noBack && !isHideMore" @click.native="confirm" moreHover type2 :tips="lang.p27_1">
				{{ lang.SelectGoBack }}
			</QuoteBtn>
		</div>
	</div>
</template>

<script>
import QuoteBtn from "@/components/Quote/QuoteBtn";
import { getFileSuffix, exchangeRates, isImageType, isVideoType } from "@/utils/utils";

export default {
	name: "qtyAndBtn",
	components: {
		QuoteBtn,
	},
	props: {
		selectedParams: Object,
		dialogItem: Object,
		qtyList: Array,
		noQty: {
			type: Boolean,
			default: false,
		},
		noComment: {
			type: Boolean,
			default: false,
		},
		noBack: {
			type: Boolean,
			default: false,
		},
		isHideMore: {
			type: Boolean,
			default: false,
		},
	},

	data() {
		return {
			textarea: "",
			customQtyStatus: undefined,
		};
	},
	computed: {
		lang() {
			return this.$store.getters.lang.quote || {};
		},
		currencySymbol() {
			return this.$store.getters.currencySymbol;
		},
		currencyRate() {
			return this.$store.getters.currencyRate;
		},
		morePriceData() {
			return this.$store.getters.morePriceData;
		},
		device() {
			return this.$store.state.device;
		},
	},
	watch: {
		// customQty: {
		//   handler(newValue) {
		//     if (newValue && newValue > 0) {
		//       this.qty = 6
		//     }
		//   },
		// },
		dialogItem: {
			handler(newValue) {
				this.$emit("update:dialogItem", newValue);
			},
			deep: true,
			immediate: true,
		},
	},
	created() {},
	methods: {
		disabledBtn() {
			if (this.noQty) {
				return false;
			}
			return !this.dialogItem.selectQty && !this.dialogItem.inputQty;
		},
		filterPlaceHolder(name) {
			if (name == "Edge Engraving" || name == "Sequential Numbering") {
				return ".";
			} else {
				return this.lang.p25;
			}
		},
		filterTitle(name) {
			switch (name) {
				case "Transparent Color":
					return this.lang.TransparentColor;
				case "Translucent Color":
					return this.lang.TransparentColor;
				case "Glow in the Dark":
					return this.lang.GlowintheDark;
				case "Glitter Color":
					return this.lang.GlitterColor;
				case "Rhinestone":
					return this.lang.Rhinestone;
				case "Pearlescent":
					return this.lang.Pearlescent;
				default:
					return this.lang.p20;
			}
		},
		getPriceFun(dialogItem, count, currencyRate) {
			let temp;
			if (count <= 0 || !count) {
				temp = this.exchangeRates(0, currencyRate);
				return temp
			} else if (dialogItem.priceInfo.increasePrice) {
				temp = this.upPriceFun(dialogItem.priceInfo.increasePrice, count, currencyRate) * count;
			} else if (dialogItem.priceInfo.unitPrice) {
				temp = this.exchangeRates(dialogItem.priceInfo.unitPrice, currencyRate) * count;
			} else {
				temp = this.exchangeRates(0, currencyRate) * count;
			}
			return ((temp + this.morePriceData.plusAdditionalItemPrice) * this.morePriceData.multiAdditionalItemPrice).toFixed(2);
		},
		upPriceFun(increasePrice, count, currencyRate) {
			let temp = JSON.parse(increasePrice);
			if (count >= temp[temp.length - 1].quantity) {
				return this.exchangeRates(temp[temp.length - 1].unitPrice, currencyRate);
			} else if (count < temp[0].quantity || (count >= temp[0].quantity && count < temp[1]?.quantity)) {
				return this.exchangeRates(temp[0].unitPrice, currencyRate);
			} else {
				for (let i = 0; i <= temp.length; i++) {
					if (count >= temp[i].quantity && count < temp[i + 1].quantity) {
						return this.exchangeRates(temp[i].unitPrice, currencyRate);
					}
				}
			}
		},
		selectQty(val) {
			this.dialogItem.selectQty = val;
			this.dialogItem.inputQty = undefined;
			// this.qty = val
			// this.customQty = undefined
			this.$forceUpdate();
		},
		exchangeRates(price, rate) {
			return exchangeRates(price, rate);
		},
		selectCustomQty() {
			if (!this.dialogItem.inputQty) {
				this.dialogItem.inputQty = 1;
			}
			this.dialogItem.selectQty = undefined;
		},
		updateQty(val) {
			if (!val) {
				this.dialogItem.selectQty = undefined;
			}
			// val= (val + '').replace(/[^\d]/g, '');
			//     this.$emit("update:customQty", val);
			//   this.$emit("calcPrice");
		},
		confirm(type) {
			if (this.dialogItem.selectQty) {
				this.dialogItem.inputNum = this.dialogItem.selectQty;
			} else {
				this.dialogItem.inputNum = this.dialogItem.inputQty;
			}
			if (!this.dialogItem.inputNum) {
				return false;
			}
			if (type == "next") {
				this.$emit("qtyAndBtnNext", this.dialogItem);
			} else {
				this.$emit("qtyAndBtnConfirm", this.dialogItem);
			}
		},
	},
};
</script>

<style scoped lang="scss">
.qtyAndBtn {
	display: flex;
	flex-direction: column;
	width: 100%;
	padding: 0 20px;
	@media screen and (max-width: 767px) {
		padding: 0 10px;
	}

	> div {
		padding: 5px 0;
	}

	.q1 {
		display: flex;
		align-items: center;
		justify-content: center;

		.normal-text {
			font-weight: bold;
			@media screen and (max-width: 767px) {
				font-size: 4vw;
			}
		}

		.circle3 {
			display: flex;
			justify-content: center;
			align-items: center;
			width: 18px;
			height: 18px;
			min-width: 18px;
			border: 1px solid $color-primary;
			background-color: $color-primary;
			margin-right: 6.5px;
			transition: all 0.3s;
			position: relative;
			border-radius: 50%;

			&::after {
				background: white;
				position: absolute;
				content: "";
				left: 50%;
				top: 50%;
				transform: translate(-50%, -50%);
				height: 6px;
				width: 6px;
				border-radius: 50%;
			}
		}

		&.active {
			.circle3 {
				border-color: $color-primary;
				background: $color-primary;

				&::after {
					background-color: #fff !important;
					height: 8px;
					width: 8px;
				}
			}

			.normal-text {
				color: $color-primary;
			}
		}
	}

	.q2 {
		@media screen and (max-width: 767px) {
			font-size: 3.2vw;
			padding: .6667vw 0;
		}
	}

	.q3 {
		display: grid;
		grid-template-columns: repeat(6, 1fr);
		row-gap: 5px;
		column-gap: 10px;
		justify-content: space-between;
		@media screen and (max-width: 767px) {
			grid-template-columns: repeat(3, 1fr);
			font-size: 3.2vw;
			row-gap: 3.4vw;
		}

		> div:first-child {
			font-weight: bold;
			grid-column: 1/7;
			@media screen and (max-width: 767px) {
				grid-column: 1/4;
			}
		}

		.item-wrap.normalBorder {
			background: #f4f5f5;
		}

		.item-wrap {
			position: relative;

			.item {
				background-color: #fff !important;
				border: 1px solid #d9d9d9;
				border-radius: 4px;
				position: relative;
				display: flex;
				flex-direction: column;
				justify-content: space-between;
				align-items: center;
				cursor: pointer;
				transition: all 0.3s;

				.checkIcon {
					width: 20px;
					height: 20px;
					border-radius: 0 0 0 6px;
					display: none;
					z-index: 1;
					@media screen and (max-width: 767px) {
						width: 18px;
						height: 18px;
						::v-deep .v-icon.v-icon {
							font-size: 14px !important;
						}
					}
				}

				.imgWrap {
					flex-shrink: 0;
					width: 100%;
					height: 40px;
					padding: 10px;
					border-bottom: 1px dashed #e9ecf0;
					display: flex;
					justify-content: center;
					align-items: center;
					@media screen and (max-width: 767px) {
						padding: 1.3333vw;
						height: 8.3333vw;
					}

					::v-deep .el-input-number {
						width: 100%;
					}
				}

				.textWrap {
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;
					flex: 1;
					text-align: center;
					padding: 10px 0;
					@media screen and (max-width: 767px) {
						padding: 2.3333vw 0;
					}

					.normal-text {
						font-size: 16px;
						color: #333;
						transition: all 0.3s;
						text-align: left;
						@media screen and (max-width: 767px) {
							font-size: 3.2vw;
						}
					}

					.tip-text {
						color: #de3500;
						font-size: 16px;
						@media screen and (max-width: 767px) {
							font-size: 3.2vw;
						}
					}
				}

				.circle {
					position: absolute;
					left: 50%;
					bottom: 0;
					transform: translate(-50%, 100%);
					border-top: none;
					// position: absolute;
					// left: 50%;
					// bottom: 0;
					// transform: translate(-50%, 100%);
					// width: 28px;
					// height: 15px;
					// border: 1px solid #e9ecf0;
					// border-top: none;
					// background: #ffffff;
					// border-radius: 0 0 16px 16px;
					// z-index: 10;
					// transition: all 0.3s;

					.inner-circle {
						position: absolute;
						left: 50%;
						top: 0;
						transform: translate(-50%, -50%);
						width: 18px;
						height: 18px;
						border-radius: 50%;
						background: #fff;
						border: 1px solid #aaaeb3;
						transition: all 0.3s;

						&::after {
							content: "";
							position: absolute;
							left: 50%;
							top: 50%;
							transform: translate(-50%, -50%);
							width: 6px;
							height: 6px;
							background-color: #aaaeb3;
							border-radius: 50%;
							transition: all 0.3s;
						}
					}
				}
			}

			&.active{
					.item {
						border-color: $color-primary !important;

						.checkIcon {
							display: flex;
						}

						.editIcon {
							display: none;
						}

						.circle {
							// border-color: $color-primary !important;

							.inner-circle {
								border-color: $color-primary !important;

								&::after {
									background-color: $color-primary !important;
								} 
							}
						}
					}
				}
		}
		@media (hover: hover) {
			.item-wrap {
				&:hover {
					.item {
						border-color: $color-primary !important;

						.checkIcon {
							display: flex;
						}

						.editIcon {
							display: none;
						}

						.circle {
							// border-color: $color-primary !important;

							.inner-circle {
								border-color: $color-primary !important;

								&::after {
									background-color: $color-primary !important;
								} 
							}
						}
					}
				}
			}
		}

		.selfInput.item-wrap {
			.item {
				.imgWrap {
					padding: 0;

					::v-deep .el-input-number {
						width: 100%;

						.el-input__inner {
							padding: 0;
							border: none;
							font-size: 16px;
							@media screen and (max-width: 767px) {
								font-size: 3.4667vw !important;
								height: 7vw;
							}
						}
					}

					.editIcon {
						flex-shrink: 0;
						color: #b3b3b3;
						font-size: 16px;
						@media screen and (max-width: 767px) {
							font-size: 3.4667vw;
							padding-right: 1.3333vw;
						}
					}
				}
			}
		}
	}

	.q4 {
		margin-top: 10px;
		@media screen and (max-width: 767px) {
			font-size: 3.2vw;
			margin-top: 1.3333vw;

			.el-input{
				min-height:16vw;
			}
		}

		> div:first-child {
			font-weight: bold;
			margin-bottom: 5px;
		}

		textarea {
			background: #f4f5f5;
		}
	}

	.q5 {
		display: flex;
		justify-content: center;

		@media screen and (max-width: 767px) {
			.gs-quote-button{
				width: 100% !important;
			}
		}
	}

	::v-deep .el-textarea__inner:focus {
		border-color: $color-primary;
	}

	::v-deep .svg-icon {
		@media screen and (max-width: 767px) {
			width: 1.5em;
			height: 1.5em;
		}
	}
}
</style>
