<template>
	<div class="modal-box" :class="modal.class" :style="modal.style"
		@click.self="setModalType({}, {}, 'modal_data')">
		<div class="product" v-for="(o, oi) in modal.outer" :key="oi">
			<div class="pdt-left">
				<div class="swiper-box">
					<div class="swiper1" ref="swiper1">
						<div class="swiper-wrapper">
							<div class="swiper-slide" v-for="(item, index) in imgList" :key="index">
								<div class="smallImg" v-if="isImageType(item.url)">
									<client-only>
										<pic v-if="$store.getters.isMobile" :src="item.url" :alt="item.alt" />
										<PicZoom v-else :url="item.url" :width="300" :scale="2" type="round"></PicZoom>
									</client-only>
								</div>
								<VideoPlayer class="swiper-no-swiping" v-else :ref="'videoPlayer' + index"
									disabled-mouse :options="getVideoOptions(item, 1)"></VideoPlayer>
							</div>
						</div>
						<!--箭头。如果放置在swiper外面，需要自定义样式。-->
						<div pointer class="swiper-button-prev"></div>
						<div pointer class="swiper-button-next"></div>
					</div>
					<div class="swiper2" ref="swiper2">
						<div flex class="swiper-wrapper">
							<div class="swiper-slide" v-for="(item, index) in imgList" :key="index">
								<pic v-if="isImageType(item.url)" :src="item.url" :alt="item.alt" />
								<template v-else-if="isVideoType(item.url)">
									<video v-show="!item.imgUrl" :src="item.url" preload="auto"></video>
									<pic v-show="item.imgUrl" :src="item.imgUrl" :alt="item.alt" />
								</template>
							</div>
						</div>
					</div>
				</div>
			</div>

			<div class="pdt-right">
				<div class="info-box">
					<h2>{{ productInfo.productName }}</h2>
					<EditDiv class="sub-title" :style="modal.subTitleStyle" v-model:content="o.subTitle.value"
						v-if="o.subTitle?.value" @click="setModalType(o.subTitle, modal.outer, 'text')" />

					<div flex class="size-box">
						<div v-for="(i, index) in productInfo.productPriceTableDetailList" :key="i.sizeName"
							:class="{ sele: sizeIndex == index }" @click="sizeIndex = index">{{ i.sizeName }}</div>
					</div>

					<template v-for="(i, index) in productInfo.productPriceTableDetailList" v-if="sizeIndex == index">
						<div flex class="discount-box" v-show="productInfo.discount">
							<div class="low-price">
								<span>{{ lang.as }}</span>
								<CCYRate :price="i.discountPriceList[i.discountPriceList.length - 1]"></CCYRate>
							</div>
							<div class="discount">
								<div class="circle">%</div> {{ lang.save }} {{ (productInfo.discount * 100).toFixed(0)
								}} %
							</div>
							<div class="old-price">
								{{ lang.listPrices }}
								<del>
									<CCYRate :price="i.originalPriceList[i.originalPriceList.length - 1]"></CCYRate>
									{{ lang.to }}
									<CCYRate :price="i.originalPriceList[0]"></CCYRate>
								</del>
							</div>
						</div>
						<div class="price-box">
							<EditDiv v-model:content="o.qty.value" v-if="o.qty?.value"
								@click="setModalType(o.qty, modal.outer, 'text')" />
							<div flex>
								<div v-for="p in i.quantityList" :key="p">{{ p }}+</div>
							</div>
							<EditDiv v-model:content="o.original.value" v-if="o.original?.value"
								@click="setModalType(o.original, modal.outer, 'text')" />
							<div flex class="original">
								<CCYRate v-for="p in i.originalPriceList" :price="p" :key="p"></CCYRate>
							</div>
							<EditDiv v-model:content="o.discount.value" v-if="o.discount?.value"
								@click="setModalType(o.discount, modal.outer, 'text')" />
							<div flex>
								<CCYRate v-for="p in i.discountPriceList" :price="p" :key="p"></CCYRate>
							</div>
						</div>
					</template>

					<button v-if="o.button?.value" :primary="!o.button.outline" :outline="o.button.outline"
						:style="{ ...modal.btnStyle, ...o.button.style }" :title="o.button.alt"
						@click="setModalType(o.button, modal.outer, 'button', { url: productInfo.routingName })">
						<EditDiv v-show="o.button.value" tagName="label" v-model:content="o.button.value" />
						<b :class="o.button.icon" v-show="o.button.icon"></b>
					</button>
				</div>
			</div>


			<div class="card-box" :scroll="modal.scroll" :style="modal.cardBoxStyle">
				<div flex :style="{
					width: modal.scroll ? (modal.column * 100 + modal.margin * (modal.column - 1)) + '%' : false,
					'grid-gap': Math.min(modal.column, 1.5) + 'em ' + modal.margin + '%',
					...modal.boxStyle
				}">
					<div flex class="card part2" v-for="(l, li) in modal.list" :childHoverIndex="li" :key="li" :style="{
						width: (100 - modal.margin * (modal.column - 1)) / modal.column + '%', // 撑开宽度，以使card按照column数量换行
						marginTop: modal.float && (li > (modal.column - 2)) ? (9 - modal.column) / 3 + '%' : null,
						marginRight: modal.float && ((li + 2) % modal.column && li < modal.list.length - 1) ? modal.margin + '%' : null,
						...modal.cardStyle,
						...l.style
					}">
						<pic v-if="l.img" :src="l.img.value" :alt="l.img.alt" :pointer="l.img.url || l.button?.url"
							@click="setModalType(l.img, modal.list, 'img', { ...l.button, ...l.img })"
							:style="{ ...modal.cardImgStyle, ...l.img.style, ...l.imgStyle }" />

						<b v-if="l.icon" class="hover-tag icon" :class="l.icon.value"
							:style="{ ...modal.cardImgStyle, ...l.iconStyle }"
							@click="setModalType(l.icon, modal.list, 'icon')"></b>

						<EditDiv v-if="l.index?.value" class="index" @click="setModalType(l.index, modal.list, 'text')"
							v-model:content="l.index.value" :style="{ ...modal.cardIndexStyle, ...l.indexStyle }" />


						<div class="content" :style="{ ...modal.cardContentStyle, ...l.contentStyle }">
							<EditDiv class="card-title" v-if="l.title?.value" :tagName="l.title.tagName || 'h3'"
								@click="setModalType(l.title, modal.list, 'text')" v-model:content="l.title.value"
								:style="{ ...modal.cardTitleStyle, ...l.title.style, ...l.titleStyle }"></EditDiv>

							<EditDiv class="card-subtitle" :style="{ ...modal.cardSubTitleStyle, ...l.subTitleStyle }"
								@click="setModalType(l.subTitle, modal.list, 'text')" v-model:content="l.subTitle.value"
								v-if="l.subTitle?.value">
							</EditDiv>

							<EditDiv class="card-text" :style="{ ...modal.cardTextStyle, ...l.textStyle }"
								@click="setModalType(l.text, modal.list, 'text')" v-model:content="l.text.value"
								v-if="l.text" :class="{ noExpand: !l.isExpand }"></EditDiv>

							<div class="price-list" :style="{ ...modal.cardPriceListStyle, ...l.priceListStyle }"
								v-if="l.priceList" @click="setModalType(l.priceList, modal.list, 'price_list')">
								<template v-for="p in l.priceList">
									<CCYRate v-if="Number(p.value)" :price="p.value" :style="p.style"></CCYRate>
									<span v-else :style="p.style">{{ p.value }}</span>
								</template>
							</div>
						</div>
					</div>
				</div>
			</div>

		</div>
	</div>
</template>

<script>
import { isImageType, isVideoType } from "@/utils/utils";
import { getProductSizePriceTableBySku } from "@/api/web";


export default {
	name: "modalLanding",
	props: {
		data: {
			type: Object,
			default: {},
		}
	},
	async fetch() {
		let res = await getProductSizePriceTableBySku({ sku: this.data.sku });
		this.productInfo = res.data || {};
		this.imgList = JSON.parse(res.data.imgJson).filter(item => item.url) || [];
	},
	data() {
		return {
			modal: {
				type: {},
				style: {},
				outer: [{}],
				...this.data
			},
			imgList: [],
			productInfo: {},
			sizeIndex: 0
		};
	},
	computed: {
		lang() {
			return this.$store.getters.lang?.semiCustom;
		}
	},
	watch: {
		modal: {
			handler(val) {
				if (process.env.isManage) this.$emit("update:data", val);
			},
			immediate: true,
			deep: true
		}
	},
	async mounted() {
		await this.$nextTick();
		let _this = this;
		this.swiper2 = new Swiper(this.$refs.swiper2, {
			slidesPerView: "auto",
			watchSlidesVisibility: true, //防止不可点击
			grabCursor: true,
			on: {
				click: function () {
					_this.editStatus = false;
				},
			},
		});
		this.swiper1 = new Swiper(this.$refs.swiper1, {
			slidesPerView: 1,
			autoplay: false,
			grabCursor: true,
			observer: true,
			observeParents: true,
			navigation: {
				nextEl: ".swiper1 .swiper-button-next",
				prevEl: ".swiper1 .swiper-button-prev",
			},
			thumbs: {
				swiper: this.swiper2,
			},
			on: {
				slideChangeTransitionEnd: function (value) {
					_this.imgList.forEach(function (v, i) {
						if (i === value.activeIndex) {
							if (_this.$refs["videoPlayer" + i]) _this.$refs["videoPlayer" + i][0].player.play();
						} else {
							if (_this.$refs["videoPlayer" + i]) _this.$refs["videoPlayer" + i][0].player.pause();
						}
					});
				},
			},
		});
	},
	methods: {
		setModalType(target, targetArray, clickType, event, other) {
			this.$setModal(this, target, targetArray, clickType, event, other)
		},
		isImageType(url) {
			return isImageType(url);
		},
		isVideoType(url) {
			return isVideoType(url);
		},
	},
};
</script>

<style scoped lang="scss">
.product {
	display: grid;
	grid-column-gap: 7vw;
	grid-template-columns: 49em 1fr;

	.pdt-left {
		::v-deep img {
			max-height: 42em;
		}

		.swiper-box {
			overflow: hidden;
			border-radius: 1.5em;
			background-color: white;
			position: relative;
		}

		.swiper1 {
			>div[class^=swiper-button] {
				width: 2em;
				height: 2em;
				color: black;
				font-weight: 900;
				padding-top: 0.1em;
				border-radius: 50%;
				--swiper-navigation-size: 0.8em;
				background-color: rgba(0, 0, 0, 0.06);
				top: 40%;
			}

			.swiper-button-next.swiper-button-disabled,
			.swiper-button-prev.swiper-button-disabled {
				opacity: 1;
				color: #999;
			}
		}

		.swiper2 {
			margin: 0 1.25vw 2em;
			position: relative;
			z-index: 1;

			.swiper-wrapper {
				grid-gap: 1.25vw;
			}

			.swiper-slide {
				width: 8em;
				height: 8em;
				padding: 2px;
				cursor: pointer;
				margin-top: -5em;

				&.swiper-slide-thumb-active>* {
					box-shadow: 0 0 0 2px $color-primary;
				}

				>* {
					overflow: hidden;
					border-radius: 0.6em;
					background-color: #F1F1F1;
					box-shadow: 0 0 0.6em #f1f1f1;
				}
			}
		}
	}

	.pdt-right {
		position: relative;
		grid-row-start: span 2;

		.info-box {
			position: sticky;
			top: calc(var(--scroll-padding-top) + 2em);
			bottom: 1em;
		}

		h2 {
			content: '';
			text-align: inherit;
		}

		.size-box {
			gap: 1em;
			text-align: center;

			>div {
				width: 5em;
				line-height: 2em;
				font-size: 1.125em;
				border-radius: 0.25em;
				border: 1px solid #E2E2E2;

				&.sele {
					color: $color-primary;
					border-color: $color-primary;
				}
			}
		}

		.discount-box {
			color: #666;
			margin: 1.8em 0;
			flex-wrap: wrap;
			grid-gap: 0.5em 1em;
			align-items: center;

			.low-price {
				font-size: 1.25em;
				font-weight: bold;
				color: $color-primary;
			}

			.discount {
				display: flex;
				color: #fff;
				column-gap: 0.3em;
				align-items: center;
				border-radius: 0.3em;
				padding: 0.4em 0.6em;
				font-size: calc(1em - 2px);
				background: linear-gradient(to right, var(--color-primary-darken), var(--color-bright));

				.circle {
					width: 1.5em;
					text-align: center;
					line-height: 1.5em;
					border-radius: 50%;
					color: $color-primary;
					font-size: calc(1em - 2px);
					background-color: #ffffff;
				}
			}

			.old-price {
				width: 100%
			}
		}

		.price-box {
			display: grid;
			overflow: hidden;
			font-size: 1.25em;
			text-align: center;
			margin-bottom: 2em;
			border-radius: 0.5em;
			grid-template-columns: auto 1fr;
			border: 1px solid #E2E2E2;
			border-top-width: 2px;

			>div {
				border-left: 1px solid #E2E2E2;
				border-bottom: 1px solid #E2E2E2;

				&:not([flex]) {
					width: 6em;
					font-weight: 600;
					padding: 0.7em 0;
					background: $color-second;
				}

				&[flex]>* {
					flex: 1;
					padding: 0.7em 0;
					border-right: 1px solid #E2E2E2;

					&:nth-child(even) {
						background: rgba($color: white, $alpha: 0.5);
					}
				}
			}

			.original {
				color: #999999;
				text-decoration: line-through;
			}
		}
	}


	.card-box {
		margin-top: 2em;

		&>div {
			flex-wrap: wrap;
			scroll-behavior: smooth;
		}

		&[scroll] {
			overflow-x: auto;
			scroll-behavior: smooth;

			&::-webkit-scrollbar {
				width: 0;
				height: 0;
			}
		}
	}

	.card {
		overflow: hidden;
		transition: all 0.1s;
		position: relative;
		z-index: 1;

		>.index {
			font-size: 8.5em;
			font-weight: bold;
			position: absolute;
			right: 1.5em;
		}

		>img {
			object-fit: contain;
		}

		>.icon {
			font-size: 3em;
			display: inline-block;
		}

		.content {
			flex: 1;
			display: flex;
			grid-gap: 0.5em;
			flex-direction: column;

			.card-text,
			.card-price {
				opacity: 0.8;
			}

			.price-list {
				label:not(:last-of-type) {
					opacity: 0.7;
					padding: 0 0.2em;
					text-decoration: line-through;
				}

				label:last-of-type {
					color: #EB1D07;
					padding: 0 0.2em;
					font-weight: bold;
				}
			}
		}
	}
}


@media screen and (max-width: $mb-width) {
	.product {
		display: block;

		.pdt-left {
			width: 100%;
			margin-bottom: 2em;

			.swiper2 {
				margin: 0 1.25vw 2em;
				position: relative;
				z-index: 1;

				.swiper-wrapper {
					grid-gap: 1.25vw;
				}

				.swiper-slide {
					width: 6em;
					height: 6em;
				}
			}
		}

		.pdt-right {

			.discount-box {
				.low-price {
					font-size: 1.5em;
				}

				.old-price {
					font-size: 1.175em;
				}
			}

			.price-box {
				font-size: 1em;
				border-top-width: 1px;

				>div {
					border: none;

					&[flex]>* {
						&:nth-child(even) {
							background: none;
						}

						&:last-child {
							border-right: none;
						}
					}
				}
			}
		}

		.tab-box {
			width: 90vw;
			background: none;
			grid-gap: 0.8em 0.5em;
			justify-content: center;

			>h3 {
				padding: 0.6em 1.5em;

				&:not([primary]) {
					background-color: #EFEFEF;
				}
			}
		}
	}
}
</style>