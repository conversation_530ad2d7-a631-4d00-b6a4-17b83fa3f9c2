<template>
	<div class="packaging">
		<div class="packagingGrid">
			<div class="packagingBox" v-for="(item, index) in itemData.childList" :key="index" :class="{ selected: item.selected }">
				<div class="imgWrap">
					<img :src="getImg(item)" alt="" class="img" />
					<b class="icon-fangda4" @click="fangdaImg(index)"></b>
				</div>
				<div class="packagingInfo">
					<div class="packagingName">{{ item.alias2 || item.alias }}</div>
					<div class="packagingPrice" :class="{ free: citem == 'Free' }" v-for="(citem, index) in getPrice(item)" :key="index">{{ citem }}</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	name: "Packaging",
	components: {},
	props: {
		itemData: {
			type: Object,
			default: () => {},
		},
	},
	data() {
		return {};
	},
	computed: {
		isMobile() {
			return this.$store.getters.isMobile;
		},
		viewImgList() {
			return this.itemData.childList.map((item) => {
				return this.getImg(item);
			});
		},
	},
	watch: {},
	methods: {
		getImg(item) {
			if (item.imageJson) {
				try {
					let imgUrl = JSON.parse(item.imageJson);
					return imgUrl[0].url;
				} catch (e) {
					console.error("Error parsing imageJson:", e);
					return item.imageUrl || "";
				}
			} else {
				return item.imageUrl || "";
			}
		},
		getPrice(item) {
			if (item.priceInfo && item.priceInfo?.priceShow) {
				return item.priceInfo["priceShow"];
			}
			return "Free";
		},
		selectOption(item) {
			// 实现选择逻辑
			this.$emit("option-selected", item);
		},
		fangdaImg(index) {
			let zoomNum = 1.6;
			if (this.isMobile) zoomNum = 0.6;
			const $viewer = this.$viewerApi({
				images: this.viewImgList,
				options: {
					initialViewIndex: index,
					initialCoverage: 0,
					viewed: function () {
						$viewer.zoomTo(zoomNum);
					},
				},
			});
			return $viewer;
		},
	},
	created() {},
	mounted() {},
};
</script>
<style scoped lang="scss">
.packaging {
	width: 100%;

	.packagingGrid {
		width: 100%;
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		gap: 20px;
		padding: 0 15%;

		.packagingBox {
			position: relative;
			background: #ffffff;
			border-radius: 8px;
			border: 1px solid #d9dbdd;
			display: flex;
			flex-direction: column;
			align-items: center;
			gap: 10px;
			cursor: pointer;
			transition: all 0.3s ease;
			padding-bottom: 10px;

			&.selected {
				border: 2px solid #4a90e2;
			}
			.imgWrap {
				width: 100%;
				display: flex;
				align-items: center;
				justify-content: center;

				.img {
					aspect-ratio: 304/200;
					max-width: 100%;
					max-height: 100%;
					object-fit: contain !important;
				}
				b {
					cursor: pointer;
					position: absolute;
					top: 10px;
					right: 10px;
					color: #ccc;
					font-size: 16px;
				}
			}

			.packagingInfo {
				width: 100%;
				display: flex;
				flex-direction: column;
				align-items: center;
				gap: 5px;

				.packagingName {
					font-size: 18px;
					color: #333333;
				}

				.packagingPrice {
					font-size: 16px;
					color: #666666;

					&.free {
						color: #d81e06;
					}
				}
			}
		}

		@include respond-to(mb) {
			padding: 0;
			gap: 10px;
			.packagingBox {
				.imgWrap {
					b {
						top: 5px;
						right: 5px;
						font-size: 12px;
					}
				}
				.packagingInfo {
					.packagingName {
						font-size: 14px;
						font-weight: 700;
					}

					.packagingPrice {
						font-size: 12px;
					}
				}
			}
		}
	}
}
</style>
