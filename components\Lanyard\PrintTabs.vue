<template>
	<div class="PrintTabs" :class="{'is-lightUp-lanyards':lanyardType === 'lightUpLanyards'}">
		<div class="designLoading">
			<el-tabs v-model="fontTabs" class="demo-tabs" @tab-click="fontTabsFun">
				<el-tab-pane v-for="item in sortBindValue" :key="item.id" :name="item.paramName">
					<template #label>
						<span class="custom-tabs-label">
							<el-tooltip effect="light" :content="lang.directly" placement="top-start" popper-class="uploadArtworkPopper" v-if="item.paramName == 'Upload Artwork'">
								<span class="custom-tabs-label">
									<img class="myIcon" v-if="item.paramName == 'Upload Artwork'" src="https://oss-static-cn.liyi.co/web/quoteManage/20231014/icon_Upload_2047msPASF.png" />
								</span>
							</el-tooltip>
							<img v-else :src="JSON.parse(item.imageJson)[0].url" alt="" loading="lazy" />
							<span>{{ item.alias }}</span>
						</span>
					</template>
					<div class="t-radio" v-if="item.childList.length">
						<LanyardCheckBox v-for="citem in item.childList" :key="citem.id" :bindValue="citem" :selectedData="selectedData" :bindName="bindName" needLoading @clickFun="selectFun"></LanyardCheckBox>
					</div>
				</el-tab-pane>
			</el-tabs>
			<div class="showText" v-if="fontTabs">
				<div class="cyl-content" v-if="fontTabs !== 'Upload Artwork' && selectInfo">
					<el-form class="el-form mob" ref="canvasFontForm" :model="canvasFontForm">
						<template v-if="changeYourText1">
							<el-form-item class="z1" prop="ropeText1">
								<div class="cyl-title">{{ lang.lanyard.ChangeyourText1 }} :</div>
								<el-input placeholder="" v-model="canvasFontForm.ropeText1" @input="updateValues"> </el-input>
							</el-form-item>
							<el-form-item class="z2" prop="ropFontFamily1">
								<div class="cyl-title">{{ lang.lanyard.ChooseAFont }} :</div>
								<el-select class="customFontFamily" v-model="canvasFontForm.ropFontFamily1" :placeholder="lang.PleaseSelect" @input="updateValues">
									<el-option :style="{ fontFamily: item.name + ' !important' }" v-for="(item, index) in fontList" :key="index" :label="item.name" :value="item.name"></el-option>
								</el-select>
							</el-form-item>
							<el-form-item class="z3" @click.stop="visible1 = true">
								<el-tooltip :content="lang.multiColorPrice" placement="right" effect="light" popper-class="selectLanyardSizePopper">
									<span class="icon flex align-items-center" style="width: fit-content">
										<div class="cyl-title">
											{{ lang.lanyard.ImprintColor1 }}:&nbsp;
											<b class="icon-wenhao3" style="fontsize: 13px"></b>
										</div>
									</span>
								</el-tooltip>
								<el-popover v-model:visible="visible1" popper-class="colorPopper" placement="bottom" :width="400" trigger="click" @input="updateValues">
									<template #reference>
										<div class="inputStyleLike" :class="canvasFontForm.ropeFontFillStyle1 ? 'selected-border' : ''">
											<div>
												<div
													class="showColor"
													:style="{
														backgroundColor: canvasFontForm.ropeFontFillStyle1,
													}"
												></div>
												<div>
													{{ canvasFontForm.ropeFontFillStyleName1 }}
												</div>
											</div>
											<div>
												<img src="https://oss-static-cn.liyi.co/web/quoteManage/20231014/icon_color_2047rb7FJR.png" style="width: 22px; height: 22px" />
											</div>
										</div>
									</template>
									<div class="closeBtn">
										<el-button class="btn-close" circle @click="visible1 = false"></el-button>
										<div class="colorPickerGridBox custom-scrollbar">
											<div v-for="item in allColor" :title="item.pantone" :key="item.id" :style="{ backgroundColor: item.code }" @click.stop="colorPickerFun(item)"></div>
										</div>
									</div>
								</el-popover>
							</el-form-item>
							<el-form-item class="z4">
								<div class="cyl-title">{{ lang.lanyard.Styling }}:</div>
								<div class="tBtn">
									<el-button :class="canvasFontForm.ropeFontWeight1 ? 'selected-border' : ''" @click="BFun(1)">B </el-button>
									<el-button :class="canvasFontForm.ropeFontStyle1 ? 'selected-border' : ''" @click="obliqueFun(1)">/ </el-button>
								</div>
							</el-form-item>
						</template>
						<template v-if="changeYourText2">
							<el-form-item class="z1" prop="ropeText2">
								<div class="cyl-title">{{ lang.lanyard.ChangeyourText2 }} :</div>
								<el-input :class="canvasFontForm.ropeText2 ? 'selected-border' : ''" placeholder="" v-model="canvasFontForm.ropeText2" @input="updateValues"> </el-input>
							</el-form-item>
							<el-form-item class="z2" prop="ropFontFamily2">
								<div class="cyl-title">{{ lang.lanyard.ChooseAFont }} :</div>
								<el-select :class="canvasFontForm.ropFontFamily2 ? 'selected-border' : ''" class="customFontFamily2" v-model="canvasFontForm.ropFontFamily2" :placeholder="lang.PleaseSelect" @input="updateValues">
									<el-option :style="{ fontFamily: item.name }" v-for="(item, index) in fontList" :key="index" :label="item.name" :value="item.name"></el-option>
								</el-select>
							</el-form-item>
							<el-form-item class="z3" @click.stop="visible2 = true">
								<div class="cyl-title">{{ lang.lanyard.ImprintColor2 }}:</div>
								<el-popover v-model:visible="visible2" popper-class="colorPopper" placement="bottom" :width="400" trigger="click" @input="updateValues">
									<template #reference>
										<div class="inputStyleLike" :class="canvasFontForm.ropeFontFillStyle2 ? 'selected-border' : ''">
											<div>
												<div
													class="showColor"
													:style="{
														backgroundColor: canvasFontForm.ropeFontFillStyle2,
													}"
												></div>
												<div>
													{{ canvasFontForm.ropeFontFillStyleName2 }}
												</div>
											</div>
											<div>
												<img src="https://oss-static-cn.liyi.co/web/quoteManage/20231014/icon_color_2047rb7FJR.png" style="width: 22px; height: 22px" />
											</div>
										</div>
									</template>
									<div class="closeBtn">
										<el-button class="btn-close" circle @click="visible2 = false"></el-button>
										<div class="colorPickerGridBox custom-scrollbar">
											<div v-for="item in allColor" :title="item.pantone" :key="item.id" :style="{ backgroundColor: item.code }" @click.stop="colorPickerFun(item)"></div>
										</div>
									</div>
								</el-popover>
							</el-form-item>
							<el-form-item class="z4">
								<div class="cyl-title">{{ lang.lanyard.Styling }}:</div>
								<div class="tBtn">
									<el-button :class="canvasFontForm.ropeFontWeight2 ? 'selected-border' : ''" @click="BFun(2)">B </el-button>
									<el-button :class="canvasFontForm.ropeFontStyle2 ? 'selected-border' : ''" @click="obliqueFun(2)">/ </el-button>
								</div>
							</el-form-item>
						</template>
						<template v-if="startClipart">
							<el-form-item
								class="z1 z5"
								prop="startClipartValue"
								:rules="[
									{
										required: true,
										message: lang.PleaseSelect,
										trigger: 'change',
									},
								]"
							>
								<div class="cyl-title">{{ lang.lanyard.StartClipart }} :</div>
								<div class="fontBox">
									<el-select id="startClipartImg" v-model="canvasFontForm.startClipartValue" :placeholder="lang.PleaseSelect" @input="updateValues">
										<el-option @click.native="clipartFun(item.id, $event, 'start')" v-for="(item, index) in clipartList" :key="item.id" :label="item.title" :value="item.id"> </el-option>
									</el-select>
									<el-popover v-model:visible="visible3" popper-class="colorPopper" placement="bottom" :width="400" trigger="click">
										<template #reference>
											<div></div>
										</template>
										<div class="closeBtn">
											<el-button class="btn-close" circle @click="visible3 = false"></el-button>
											<div class="colorPickerGridBox custom-scrollbar">
												<div v-for="item in allColor" :title="item.pantone" :key="item.id" :style="{ backgroundColor: item.code }"></div>
											</div>
										</div>
									</el-popover>
									<label v-if="canvasFontForm.fontBeforeImg" for="startClipartImg" class="fontImg">
										<img :src="canvasFontForm.fontBeforeImg" alt="" loading="lazy" />
									</label>
									<!-- <label v-else for="startClipartImg" class="fontImg">
                                      <img :src="canvasFontForm.beforeTempLogo" alt="" />
                                    </label> -->
								</div>
							</el-form-item>
						</template>
						<template v-if="endClipart">
							<el-form-item
								class="z2 z6"
								:class="{ noHeader: !startClipart }"
								prop="endClipartValue"
								:rules="[
									{
										required: true,
										message: lang.PleaseSelect,
										trigger: 'change',
									},
								]"
							>
								<div class="cyl-title">{{ lang.lanyard.EndClipart }} :</div>
								<div class="fontBox">
									<el-select v-model="canvasFontForm.endClipartValue" id="endClipartImg" :placeholder="lang.PleaseSelect" @input="updateValues">
										<el-option @click.native="clipartFun(item.id, 'end')" v-for="(item, index) in clipartList" :key="item.id" :label="item.title" :value="item.id"> </el-option>
									</el-select>
									<label v-if="canvasFontForm.fontAfterImg" class="fontImg" for="endClipartImg">
										<img :src="canvasFontForm.fontAfterImg" alt="" loading="lazy" />
									</label>
								</div>
							</el-form-item>
						</template>
						<el-form-item
							prop="doubleFont"
							class="doubleFont z3"
							:rules="[
								{
									required: true,
									message: lang.PleaseSelect,
									trigger: 'change',
								},
							]"
						>
							<div class="cyl-title">{{ lang.lanyard.PrintPositionOptions }}: <span class="def-select" v-show="bindValueSide.childList.length <= 1">{{ canvasFontForm.doubleFont.alias }}</span></div>
							<div class="fontBox" v-show="bindValueSide.childList.length > 1">
								<el-select class="selected-border" value-key="id" v-model="canvasFontForm.doubleFont" @change="changeDoubleFont" :placeholder="lang.PleaseSelect" @input="updateValues">
									<el-option style="font-size: 16px" v-for="item in bindValueSide.childList" :key="item.id" :label="item.paramName == 'Both Side' ? item.paramName + `(+$${item.priceInfo.unitPrice})` : item.paramName" :value="item"> </el-option>
								</el-select>
							</div>
						</el-form-item>
					</el-form>
					<div class="el-form mob onlyMB">
						<div class="previewDialog2" @click="showDetailsFun">
							<div class="text-center">
								<b class="icon-icon_Preview d-block f24"></b>
								<span> {{ lang.lanyard.PreviewYourDesign }} </span>
							</div>
						</div>
					</div>
					<div class="comments">
						<div style="font-weight: 700">{{ lang.OrderComments }}</div>
						<div>
							{{ lang.notes2 }}
						</div>
						<div>
							<el-input rows="5" type="textarea" :placeholder="lang.placeholder1" v-model="canvasFontForm.comments" @input="updateValues"></el-input>
						</div>
					</div>
				</div>
				<div v-if="fontTabs == 'Upload Artwork'">
					<div class="upload-box">
						<div>{{ lang.UploadFile }}</div>
						<div>{{ lang.canskip }}</div>
						<div class="upload-window">
							<ul v-if="fontImgCustom.length">
								<li v-for="(item, index) in fontImgCustom" :key="index">
									<span>{{ item.original_filename }}</span>
									<span class="icon-box">
										<b class="icon-check" style="color: #0cbd5f"></b>
										<b class="icon-shanchu2 pointer" style="color: #b6b0b0" @click.stop="removeFile(index)"></b>
									</span>
								</li>
							</ul>
							<label for="upload_widget" class="uploadLabel">
								<b class="icon-shangchuan myIcon" v-if="!fontImgCustom.length"></b>
								<el-button :disabled="canvasFontForm.commentsImgLater" type="" @click="browseFun">
									{{ lang.Browse }}
								</el-button>
								<div class="info">
									{{ lang.content2 }}
								</div>
								<el-checkbox v-model="canvasFontForm.commentsImgLater" :label="false" size="large">{{ lang.Uploadbyemaillater }} </el-checkbox>
							</label>
						</div>
						<div class="comments">
							<div style="font-weight: 700">{{ lang.OrderComments }}</div>
							<div>
								{{ lang.notes2 }}
							</div>
							<div>
								<el-input :class="canvasFontForm.comments ? 'selected-border' : ''" rows="5" v-model="canvasFontForm.comments" :placeholder="lang.placeholder1" type="textarea"></el-input>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<input type="file" ref="upload" multiple :accept="acceptFileType" @change="uploadPic" hidden />
	</div>
</template>
<script>
import LanyardCheckBox from "@/components/Lanyard/LanyardCheckBox";
import MySwitch from "@/components/Medals/MySwitch";

import { uploadFile } from "@/utils/oss";
import {checkFile,acceptFileType} from "@/utils/validate";

export default {
	head: {
		script: [
			{
				src: "https://upload-widget.cloudinary.com/global/all.js",
			},
		],
	},
	props: {
        defaultColor:{
            type: Object,
            default:()=>{
                return {
                    code: "#ffffff",
                    pantone: "white"
                }
            }
        },
        lanyardType:{
            type: [String, Boolean],
            default: false,
        },
		tips: String,
		bindValue: {
			type: Object,
		},
		allColor: {
			type: Array,
		},
		selectedData: {
			type: [Array, Object],
		},
		bindName: {
			type: String,
		},
		copyCanvasData: {
			type: Object,
		},
		bindValueSide: {
			type: Object,
		},
		fontImgCustom: {
			type: Array,
		},
		fontBeforeImg: {
			type: String,
		},
		fontAfterImg: {
			type: String,
		},
		beforeTempLogo: {
			type: String,
		},
		afterTempLogo: {
			type: String,
		},
		no: {
			type: Boolean,
			default: false,
		},
	},
	components: {
		LanyardCheckBox,
		MySwitch,
	},
	data() {
		return {
            acceptFileType,
			showInput: false,
			comments: "",
			selectInfo: null,
			canvasFontForm: {
				ropeText1: "",
				ropFontFamily1: "",
				ropeFontFillStyle1: "",
				ropeFontFillStyleName1: "",
				ropeFontWeight1: "",
				ropeFontStyle1: "",
				ropeText2: "",
				ropFontFamily2: "",
				ropeFontFillStyle2: "",
				ropeFontFillStyleName2: "",
				ropeFontWeight2: "",
				ropeFontStyle2: "",
				startClipartValue: "",
				fontBeforeImg: "",
				beforeTempLogo: "",
				endClipartValue: "",
				fontAfterImg: "",
				afterTempLogo: "",
				doubleFont: "",
				commentsImgLater: false,
			},
			target: "",
			myWidget: null,
			allImage: [],
			allImgDialog: false,
			tempTabs: null,
			fontTabs: "",
			visible1: false,
			visible2: false,
			visible3: false,
			drawer: false,
		};
	},
	computed: {
		lang() {
			return this.$store.getters.lang.quote || {};
		},
		userEmail() {
			return this.$store.state.proSystem.email;
		},
		fontList() {
			return require("@/assets/json/fontList.json").filter((item) => !item.noResource);
		},
		clipartList() {
			return [
				{
					id: 1,
					title: this.lang.noClipart,
				},
				{
					id: 2,
					title: this.lang.browseCliparts,
				},
				{
					id: 3,
					title: this.lang.chooseClipart,
				},
				{
					id: 4,
					title: this.lang.uploadLater,
				},
			];
		},
		sortBindValue() {
			return this.bindValue.childList.sort((a, b) => a.stepIndex - b.stepIndex);
		},
		changeYourText1() {
			return this.selectInfo.value.paramName !== "Only Logo";
		},
		changeYourText2() {
			return this.selectInfo.value.paramName !== "Only Logo"
			&& this.selectInfo.value.paramName !== "A Line of Words 1"
			&& this.selectInfo.value.paramName !== "A Line of Words & Started, End Clipart"
			&& this.selectInfo.value.paramName !== "A Line of Words & Started Clipart"
			&& this.selectInfo.value.paramName !== "A Line of Words & End Clipart";
		},
		startClipart() {
			return this.selectInfo.value.paramName !== "A Line of Words 1" && this.selectInfo.value.paramName !== "A Line of Words 2" && this.selectInfo.value.paramName !== "Two Lines of Words" && this.selectInfo.value.paramName !== "A Line of Words & End Clipart" && this.selectInfo.value.paramName !== "Two lines words & end clipart";
		},
		endClipart() {
			return this.selectInfo.value.paramName !== "Only Logo" && this.selectInfo.value.paramName !== "A Line of Words 1" && this.selectInfo.value.paramName !== "A Line of Words 2" && this.selectInfo.value.paramName !== "Two Lines of Words" && this.selectInfo.value.paramName !== "A Line of Words & Started Clipart" && this.selectInfo.value.paramName !== "Two lines words & started clipart";
		},
	},
	watch: {
		selectInfo: {
			handler(val) {
				if (val) {
					this.$emit("clickFun", val);
					this.$emit("printingData", val);
				}
			},
			deep: true,
		},
		canvasFontForm: {
			handler(val) {
				this.$emit("canvasFontForm", val);
			},
			deep: true,
		},
		fontBeforeImg: {
			handler(val) {
				this.canvasFontForm.fontBeforeImg = val;
				this.$emit("update:fontBeforeImg", val);
			},
		},
		fontAfterImg: {
			handler(val) {
				this.canvasFontForm.fontAfterImg = val;
				this.$emit("update:fontAfterImg", val);
			},
		},
		beforeTempLogo: {
			handler(val) {
				this.canvasFontForm.beforeTempLogo = val;
			},
		},
		afterTempLogo: {
			handler(val) {
				this.canvasFontForm.afterTempLogo = val;
			},
		},
	},
	beforeDestroy() {
		this.$Bus.$off("change:before-click", this.fontTabsFun); // 在组件销毁前移除事件监听
		this.$Bus.$off("printTabs");
	},
	mounted() {
		this.$Bus.$on("change:before-click", this.fontTabsFun);
		this.$Bus.$on("printTabs", this.replayUpload);
	},
	methods: {
		clearDoubleFont() {
			this.canvasFontForm.doubleFont = "";
		},
		replayUpload() {
			this.$refs.upload.click();
			this.$store.commit("setSizeDialog", false);
		},
		uploadPic(event, type = "upload") {
            this.$gl.show();
            let files = type === "upload" ? event.target.files : event;
            let fileResult = checkFile(Array.from(files), this.acceptFileType, 80);
            if (!fileResult) {
                this.$toast.error("File type error");
                this.$gl.hide();
                return false;
            }
			if (fileResult.nomalSize.length == 0) {
				this.$gl.hide();
				this.$store.commit("setSizeDialog", true);
				this.$store.commit("setInputRefName", "printTabs");
				this.$store.commit("setOverSizeList", fileResult.overSize);
				this.$refs.upload.value = "";
				return false;
			}
			let uploadPromises = [];
			fileResult.nomalSize.forEach((file) => {
				let promise = uploadFile(file).then((res) => {
					this.$emit("updateUploadList", {
						original_filename: file.name,
						secure_url: res,
					});
				});
				uploadPromises.push(promise);
			});
			Promise.all(uploadPromises).then(() => {
				this.$gl.hide();
				if (fileResult.overSize.length > 0) {
					this.$store.commit("setSizeDialog", true);
					this.$store.commit("setInputRefName", "printTabs");
					this.$store.commit("setOverSizeList", fileResult.overSize);
				} else {
					this.$toast.success("success");
				}
				this.$refs.upload.value = "";
			});
		},

		updateValues() {
			this.$emit("values-updated", this.canvasFontForm);
		},
		showDetailsFun() {
			this.$emit("update:showDetails", true);
		},
		changeDoubleFont(val) {
			this.selectedData["Print Position Options"] = [val];
		},
		validForm(cb) {
			this.$refs["canvasFontForm"]?.validate((valid) => {
				if (valid) {
					cb(true);
				} else {
					console.log("error submit!!");
					cb(false);
				}
			});
		},
		//文字加粗
		BFun(type) {
			if (type == 1) {
				this.canvasFontForm.ropeFontWeight1 = !this.canvasFontForm.ropeFontWeight1;
			} else if (type == 2) {
				this.canvasFontForm.ropeFontWeight2 = !this.canvasFontForm.ropeFontWeight2;
			}
		},
		// 斜体
		obliqueFun(type) {
			if (type == 1) {
				this.canvasFontForm.ropeFontStyle1 = !this.canvasFontForm.ropeFontStyle1;
			} else if (type == 2) {
				this.canvasFontForm.ropeFontStyle2 = !this.canvasFontForm.ropeFontStyle2;
			}
		},
		browseFun() {
			this.$emit("beforeAfterFun", "3");
			// this.openWidget();
			this.$refs.upload.click();
		},
		//删除自定义图片
		removeFile(index) {
			this.fontImgCustom.splice(index, 1);
			this.$emit("update:fontImgCustom", this.fontImgCustom);
		},
		// getFileName(name) {
		//   // 获取到文件名
		//   let pos = name.lastIndexOf("/"); // 查找最后一个/的位置
		//   return name.substring(pos + 1); // 截取最后一个/位置到字符长度，也就是截取文件名
		// },

		clipartFun(val, event, type) {
			// console.log(val, event,type, "val, type");
			this.$emit("clipartFun", {
				val: val,
				event: event,
				type: type,
			});
			// this.target = type
			// if (type == "start") {
			//   this.$emit("beforeAfterFun", "1")
			// } else {
			//   this.$emit("beforeAfterFun", "2")
			// }
			// switch (val) {
			//   case 1:
			//     if (type == "start") {
			//       this.canvasFontForm.fontBeforeImg = ""
			//       this.canvasFontForm.beforeTempLogo = ""
			//     } else {
			//       this.canvasFontForm.fontAfterImg = ""
			//       this.canvasFontForm.afterTempLogo = ""
			//     }
			//     // this.countColor();
			//     break
			//   case 2:
			//     this.allImgDialog = true
			//     this.getAll()
			//     break
			//   case 3:
			//     this.openWidget()
			//     break
			//   case 4:
			//     if (type == "start") {
			//       this.canvasFontForm.fontBeforeImg = ""
			//       this.canvasFontForm.beforeTempLogo = this.fontLogo
			//     } else {
			//       this.canvasFontForm.fontAfterImg = ""
			//       this.canvasFontForm.afterTempLogo = this.fontLogo
			//     }
			//     // this.countColor();
			//     break
			// }
		},

        setLightUpColor(item){
            try {
                if(this.changeYourText1){
                    this.canvasFontForm.ropeFontFillStyle1 = item.code;
                    this.canvasFontForm.ropeFontFillStyleName1 = item.pantone;
                }
                if(this.changeYourText2){
                    this.canvasFontForm.ropeFontFillStyle1 = item.code;
                    this.canvasFontForm.ropeFontFillStyleName1 = item.pantone;
                    this.canvasFontForm.ropeFontFillStyle2 = item.code;
                    this.canvasFontForm.ropeFontFillStyleName2 = item.pantone;
                }
            }catch (e) {

            }
        },
		colorPickerFun(item) {
			if (this.changeYourText1) {
				this.canvasFontForm.ropeFontFillStyle1 = item.code;
				this.canvasFontForm.ropeFontFillStyleName1 = item.pantone;
			} else if (this.changeYourText2) {
				this.canvasFontForm.ropeFontFillStyle2 = item.code;
				this.canvasFontForm.ropeFontFillStyleName2 = item.pantone;
			}
			this.visible1 = false;
			this.visible2 = false;
		},
		fontTabsFun() {
			if (this.fontTabs == "Upload Artwork") {
				this.$refs.upload.click();
				this.canvasFontForm.fontAfterImg = "";
				this.canvasFontForm.fontBeforeImg = "";
			} else {
				this.fontImgCustom.splice(0, this.fontImgCustom.length);
			}

			this.selectInfo = null;
			// this.clean()
			this.$emit("clickFun", {
				key: "Design Your Printing",
				title: this.fontTabs,
				value: null,
			});
		},
		selectFun(val) {
			this.selectInfo = val;
			this.initDataDef();
			this.$nextTick(() => {
				this.$refs.canvasFontForm.resetFields();
			});
			this.$emit("filterTampInfo", val.value);
		},
		clean() {
			this.canvasFontForm = {
				ropeText1: "",
				ropFontFamily1: "",
				ropeFontFillStyle1: "",
				ropeFontFillStyleName1: "",
				ropeFontWeight1: "",
				ropeFontStyle1: "",
				ropeText2: "",
				ropFontFamily2: "",
				ropeFontFillStyle2: "",
				ropeFontFillStyleName2: "",
				ropeFontWeight2: "",
				ropeFontStyle2: "",
				startClipartValue: "",
				fontBeforeImg: "",
				beforeTempLogo: "",
				endClipartValue: "",
				fontAfterImg: "",
				afterTempLogo: "",
				doubleFont: "",
			};
		},
		// 重置默认值
		initDataDef() {
			this.clean();
			if (this.changeYourText1) {
				this.canvasFontForm.ropeText1 = "Text Here";
				this.canvasFontForm.ropeFontFillStyleName1 = this.defaultColor.pantone;
				this.canvasFontForm.ropFontFamily1 = this.fontList[0].name;
				this.canvasFontForm.ropeFontFillStyle1 = this.defaultColor.code;
			}
			if (this.changeYourText2) {
				this.canvasFontForm.ropeText2 = "Text Here";
				this.canvasFontForm.ropeFontFillStyleName2 = this.defaultColor.pantone;
				this.canvasFontForm.ropFontFamily2 = this.fontList[0].name;
				this.canvasFontForm.ropeFontFillStyle2 = this.defaultColor.code;
			}
			if (this.startClipart) {
				this.canvasFontForm.startClipartValue = 1;
			}
			if (this.endClipart) {
				this.canvasFontForm.endClipartValue = 1;
			}
			this.canvasFontForm.doubleFont = this.bindValueSide.childList[0];
			this.selectedData["Print Position Options"] = [this.canvasFontForm.doubleFont];
		},

		openWidget() {
			this.$emit("openWidget");
		},
	},
};
</script>

<style scoped lang="scss">
.later {
	cursor: pointer;
	display: flex;
	align-items: center;
	padding-top: 20px;
	font-size: 18px !important;
	@media screen and (max-width: 767px) {
		font-size: 14px !important;
	}

	::v-deep .el-switch.is-checked {
		.el-switch__core {
			border-color: $color-primary !important;
			background-color: $color-primary !important;
		}
	}

	a {
		color: #007aff;
		text-decoration: underline;
	}
}

.tips {
	div {
		font-size: 15px !important;
		color: #b3b3b3 !important;
		text-align: center;
		margin-top: 10px;
	}
}

.circle2 {
	display: flex;
	justify-content: center;
	align-items: center;
	flex-shrink: 0;
	width: 18px;
	height: 18px;
	border: 1px solid #afb1b3;
	border-radius: 50%;
	margin-right: 10px;
	background-color: #fff;

	&::after {
		content: "";
		width: 6px;
		height: 6px;
		background: #d4d7d9;
		border-radius: 50%;
	}

	@media screen and (max-width: 767px) {
		width: 15px;
		height: 15px;

		&::after {
			width: 5px;
			height: 5px;
		}
	}

	&.active {
		background-color: $color-primary !important;
		border-color: $color-primary !important;

		&::after {
			background-color: #ffffff !important;
		}
	}

	a {
		color: blue;
	}
}

.t1 {
	font-size: 18px;
	display: flex;
	align-items: center;
	margin-bottom: 10px;

	@media screen and (max-width: 768px) {
		font-size: 14px;
	}
}

.t2 {
	margin-bottom: 10px;
	font-size: 14px;
	color: #999999;
}

.PrintTabs ::v-deep {
	width: 100%;

    &.is-lightUp-lanyards{
        .el-form-item.z3{
            display: none;
        }
        .el-form-item.z4{
            grid-column: 29 / 49 !important;

            @media screen and (max-width: 1650px) {
                grid-column: 28/49 !important;
            }

            @media screen and (max-width: 768px) {
                grid-column: 1/38 !important;
            }
        }
    }

    .isLightUpLanyards{

    }

	.onlyMB {
		display: none !important;

		@media screen and (max-width: 1500px) {
			display: grid !important;
		}

		.previewDialog2 {
			background: $color-primary;
			border-radius: 2px;
			color: #fff;
			font-size: 12px;
			padding: 10px;
		}
	}

	.el-textarea__inner:focus {
		border-color: $color-primary;
	}

	.el-tabs__nav-wrap::after {
		display: none;
	}

	.el-select .el-input .el-select__caret {
		color: black;
	}

	.el-tabs__nav-scroll {
		overflow-x: auto;
	}

	.el-tabs__content {
		overflow: visible;
		border-bottom: 1px solid #e6e6e6;
	}

	.el-image {
		width: 113px;
		height: 152px;
		transition: all 0.3s;

		@media screen and (max-width: 768px) {
			width: 100%;
			height: 100%;
		}
	}

	.el-image.isPC:hover {
		transform: scale(1.5);
		background-color: #fff;
		z-index: 2;
	}

	.el-tabs__header {
		background: #f5f6f7;
		height: 120px;
		display: flex;
		align-items: center;
		padding: 5px 10px 0 10px;
		margin: 0;

		@media screen and (max-width: 768px) {
			height: auto;
		}
	}

	.el-tabs__nav-wrap {
		width: 100%;
		margin-bottom: -2px;
	}

	.el-tabs__nav {
		display: grid;
		width: 100%;
		grid-template-columns: repeat(5, 1fr);

		@media screen and (max-width: 620px) {
			grid-template-columns: repeat(5, 155px);
		}

		.el-tabs__item {
			padding: 10px;
			font-size: 18px;
			font-weight: 400;
			color: #999999;
			text-align: center;
			line-height: normal;
			display: flex;
			flex-direction: column;
			height: 100%;
			border-radius: 10px 10px 0 0;
			position: relative;
			border: 1px solid transparent;
			// overflow: hidden;

			@media screen and (max-width: 768px) {
				font-size: 12px;
			}

			.custom-tabs-label {
				display: flex;
				flex-direction: column;
				justify-content: space-between;
				align-items: center;
				height: 100%;

				img {
					height: 70px;
					object-fit: contain;

					@media screen and (max-width: 768px) {
						height: 45px;
						margin-bottom: 5px;
					}
				}

				input[type="file"] {
					position: absolute;
					left: 0;
					top: 0;
					right: 0;
					bottom: 0;
					opacity: 0;
				}

				.iconfont {
					font-size: 34px;
					flex: 1;
					display: flex;
					align-items: center;
					justify-content: center;
				}
			}

			&:not(:last-child):after {
				position: absolute;
				content: "";
				right: -3px;
				top: 50%;
				transform: translateY(-50%);
				width: 1px;
				height: 30%;
				background-color: #d2d5d9;
			}
		}

		.el-tabs__item:hover {
			border-color: $color-primary;
		}

		.el-tabs__item.is-active {
			background-color: #fff;
			font-weight: bold;
			color: $color-primary;

			@media screen and (max-width: 768px) {
				font-weight: normal;
			}
		}

		.el-tabs__item.is-active::after {
			display: none;
		}

		.el-tabs__active-bar {
			display: none;
		}
	}

	.t-radio {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		column-gap: 20px;
		padding: 20px 10px;
		border-left: 1px solid #e6e6e6;
		border-right: 1px solid #e6e6e6;

		@media screen and (max-width: 1730px) and (min-width: 1559px) {
			column-gap: 10px;
		}

		@media screen and (max-width: 768px) {
			column-gap: 5px;
			margin-top: 0;

			.custom-checkbox {
				padding-left: 0;
			}

			.label:before {
				width: 9px;
				height: 9px;
				transform: translateX(-50%);
				left: 50%;
				top: -14px;
			}

			.label:after {
				width: 5px;
				height: 5px;
				transform: translateX(-50%);
				left: 50%;
				top: -10px;
			}
		}

		.StepBox {
			.item {
				font-size: 16px;
				font-weight: 400;
				color: #333333;
				padding: 10px 0 16px 0;

				.swiper {
					width: 113px;
					height: 152px;
					transition: all 0.3s;
				}

				@media screen and (max-width: 768px) {
					font-size: 12px;
					text-align: center;
					border: 1px solid #e6e6e6;
					height: 90%;
					padding: 5px 0 10px 0 !important;

					.swiper {
						width: auto;
						height: auto;
						border: 0;

						img {
							height: 75px;
							object-fit: contain;
						}
					}
				}
			}
		}
	}

	.t-radio.bottom {
		border-bottom: 1px solid #e6e6e6;
	}

	.showText {
		.cyl-content {
			border: 1px solid #e6e6e6;
			border-top: none;
			padding: 23px;

			@media screen and (max-width: 1650px) {
				padding: 10px;
			}

			@media screen and (max-width: 768px) {
				padding: 0px 10px 10px 10px;

				.el-form-item {
					margin-bottom: 0;
				}
			}

			.el-form:nth-child(1) {
				margin-bottom: 15px;
			}

			.el-form {
				display: grid;
				grid-template-columns: repeat(48, 1fr);
				justify-content: space-between;

				@media screen and (max-width: 768px) {
					column-gap: 0;
					row-gap: 0;
				}

				.cyl-title {
					font-size: 18px;
					font-weight: 400;
					color: #666666;
					margin-bottom: -8px;

					@media screen and (max-width: 768px) {
						font-size: 12px;
					}
				}

				.z5,
				.z6 {
					@media screen and (max-width: 768px) {
						grid-column: 1/49 !important;
					}
				}

				.z1 {
					grid-column: 1/15;
					padding-right: 10px;

					@media screen and (max-width: 1650px) {
						grid-column: 1/14;
					}

					@media screen and (max-width: 768px) {
						grid-column: 1/25;
					}
				}

				.z2 {
					grid-column: 15/29;
					padding-right: 10px;

					@media screen and (max-width: 1650px) {
						grid-column: 14/28;
					}

					@media screen and (max-width: 768px) {
						grid-column: 26/49;
					}

					.el-select {
						width: 100%;
					}

					&.noHeader {
						grid-column: 1/15;

						@media screen and (max-width: 768px) {
							grid-column: 1/25;
						}
					}
				}

				.z3 {
					grid-column: 29/43;

					@media screen and (max-width: 1650px) {
						grid-column: 28/42;
					}

					@media screen and (max-width: 768px) {
						grid-column: 1/23;
						grid-row: auto;
					}
				}

				.z4 {
					grid-column: 44/49;

					@media screen and (max-width: 1650px) {
						grid-column: 42/49;
					}

					@media screen and (max-width: 768px) {
						grid-column: 26/38;
						grid-row: auto;
					}

					> div {
						.tBtn {
							display: flex;
							height: 38px;

							@media screen and (max-width: 768px) {
								height: 30px;
							}

							button {
								height: 100%;
								background-color: #f5f6f7;
								border-color: transparent;
								font-size: 18px;
								padding: 0 20px;

								@media screen and (max-width: 768px) {
									height: 30px;
									font-size: 12px;
								}
							}

							.el-button + .el-button {
								margin-left: 5px;
							}

							.el-button:hover,
							.el-button:active,
							.el-button:focus {
								color: $color-primary;
							}
						}
					}
				}

				.el-form-item__label {
					font-size: 18px !important;
					font-weight: 400 !important;
					color: #666666 !important;
					margin: 0 !important;
					padding: 0 !important;
					padding-bottom: 5px;
					line-height: 2em;
				}


				.el-input__inner {
					height: 40px;
					background-color: #f5f6f7;
					font-size: 16px;

					@media screen and (max-width: 768px) {
						height: 30px;
						font-size: 12px;
					}
				}

				.el-input__inner:focus {
					border-color: $color-primary;
				}
			}

			.el-form.mob {
				@media screen and (max-width: 768px) {
					justify-content: space-between;

					> div:nth-child(1) {
						// margin-right: 15px;
					}
				}

				.el-form-item--default {
					margin-bottom: 0;
				}
			}

			.el-form {
				> div:nth-child(1) {
					grid-column: 1/15;

					@media screen and (max-width: 1650px) {
						grid-column: 1/14;
					}

					@media screen and (max-width: 768px) {
						grid-column: 1/25;
					}
				}

				> div:nth-child(2) {
					grid-column: 15/29;

					@media screen and (max-width: 1650px) {
						grid-column: 14/28;
					}

					@media screen and (max-width: 768px) {
						grid-column: 26/49;
					}
				}

				.doubleFont {
					grid-column: 1/15 !important;

					@media screen and (max-width: 768px) {
						grid-column: 1/49 !important;
						grid-row: auto;
					}
				}

				.fontBox {
					width: 100%;
					position: relative;

					.fontImg {
						position: absolute;
						width: 25px;
						height: 25px;
						right: 30px;
						top: 50%;
						transform: translateY(-50%);
						cursor: pointer;

						img {
							width: 100%;
							height: 100%;
							object-fit: contain;
							display: block;
						}
					}

					.el-select {
						width: 100%;
					}
				}
			}

			.comments {
				margin-top: 30px;

				@media screen and (max-width: 768px) {
					margin-top: 20px;
				}

				> div:nth-child(1) {
					font-size: 18px;
					font-weight: 400;
					color: #333333;
					line-height: 1em;
					margin-bottom: 10px;

					@media screen and (max-width: 768px) {
						font-size: 14px;
						color: #666666;
					}
				}

				> div:nth-child(2) {
					font-size: 14px;
					font-weight: 400;
					color: #999999;
					margin-bottom: 20px;
					line-height: 1em;

					@media screen and (max-width: 768px) {
						font-size: 12px;
						color: #999999;
						margin-bottom: 10px;
					}
				}
			}

			.inputStyleLike {
				display: flex;
				justify-content: space-between;
				align-items: center;
				background: #f5f6f7;
				border-radius: 4px;
				padding: 0 13px;
				height: 38px;
				border: 1px solid transparent;
				font-size: 16px;

				@media screen and (max-width: 768px) {
					height: 28px;
					font-size: 12px;
				}

				> div:nth-child(1) {
					display: flex;
					align-items: center;

					.showColor {
						width: 22px;
						height: 22px;
						border-radius: 2px;
						margin-right: 5px;
					}
				}

				> div:nth-child(2) {
					display: flex;
					align-items: center;
				}
			}

			.fullColor {
				// padding: 0 7px;
				.el-checkbox__label {
					font-size: 18px !important;
				}
			}
		}
	}

	.upload-box {
		border: 1px solid #e6e6e6;
		border-top: none;
		padding: 23px;
		// display: flex;

		@media screen and (max-width: 768px) {
			padding: 10px;
			display: block;

			.left-Box {
				width: 100% !important;
				margin-right: 0 !important;
			}
		}

		div {
			font-size: 18px;
			font-weight: 400;
			color: #333333;
			line-height: 1em;
			// margin-right: 30px;

			@media screen and (max-width: 767px) {
				font-size: 18px;
				margin-bottom: 5px;
			}
		}

		// .left-Box {
		//   width: 67%;
		//   margin-right: 30px;
		// }

		> div:nth-child(2) {
			font-size: 14px;
			font-weight: 400;
			color: #999999;
			line-height: 2em;
			width: 52%;

			@media screen and (max-width: 767px) {
				font-size: 14px;
				line-height: 1em;
				margin-bottom: 5px;
				width: 100%;
			}
		}

		.upload-window {
			border: 2px dashed #d9dbdd;
			padding: 0 1em;

			ul {
				padding: 0;
				margin-top: 10px;

				li {
					list-style-type: none;
					display: flex;
					justify-content: space-between;
					margin: 0.4em 0;
					font-size: 14px;

					.icon-box {
						> i:nth-child(1) {
							margin-right: 15px;
							color: #39c464;
						}

						> i:nth-child(2) {
							margin-right: 15px;
							color: #cccccc;
						}
					}
				}
			}

			.myIcon {
				display: flex;
				align-items: center;
				justify-content: center;
				height: 75px;
				font-size: 50px;
				color: #cccc;
				position: relative;
				top: 15px;

				@media screen and (max-width: 768px) {
					height: 2em;
				}
			}

			.uploadLabel {
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				height: 100%;
				border-radius: 4px;
				// padding: 30px 0;
				// margin-top: -22px;

				@media screen and (max-width: 768px) {
					padding: 6.5px 0;
					// margin-top: -42px;
				}

				.tips {
					font-size: 15px;
					color: #b3b3b3;
					text-align: center;
				}

				.uploadBtnWrap {
					position: relative;
					top: 15px;

					input[type="file"] {
						position: absolute;
						left: 0;
						top: 0;
						right: 0;
						bottom: 0;
						opacity: 0;
						// z-index: -1;
						// width: 0;
						// height: 0;
					}

					button[type="uploadButton"] {
						width: 220px;
						height: 40px;
						color: #fff;
						border-radius: 10px;
						background: $color-primary !important;
					}

					button.isDisabled {
						background: $color-primary !important;
						opacity: 0.5;
					}
				}

				.myIcon {
					font-size: 55px;
					color: gainsboro;
					display: flex;
					width: 68px;
					height: 55px;
					justify-content: center;
					margin-bottom: 10px;
				}

				.el-button {
					width: 220px;
					height: 40px;
					font-size: 18px;
					color: #fff;
					background-color: $color-primary;
					border-color: $color-primary;
					border-radius: 5px;
					line-height: 15px;
					margin-top: 15px;

					@media screen and (max-width: 768px) {
						font-size: 13.5px;
						height: 30px;
						line-height: 7px;
						width: 182px;
					}
				}

				.el-button.is-disabled {
					opacity: 0.5;
				}

				.info {
					font-size: 16px;
					color: #999999;
					text-align: center;
					margin: 5px 0 15px;

					@media screen and (max-width: 768px) {
						font-size: 12px;
						margin: 10px 0;
						line-height: 16px;
					}
				}

				.el-checkbox:last-of-type {
					margin-bottom: 8px;
				}

				.el-checkbox__inner {
					width: 16px;
					height: 16px;
				}

				.el-checkbox__inner::after {
					height: 9px;
					left: 5px;
				}

				.el-checkbox__inner:hover {
					border-color: $color-primary;
				}

				.el-checkbox__label {
					font-size: 18px;

					@media screen and (max-width: 767px) {
						font-size: 16px;
					}
				}

				.el-checkbox__input.is-checked + .el-checkbox__label {
					color: $color-primary;
				}

				.el-checkbox__input.is-checked .el-checkbox__inner {
					background-color: $color-primary;
					border-color: $color-primary;
				}
			}
		}

		.comments {
			margin-top: 30px;

			@media screen and (max-width: 768px) {
				margin-top: 20px;
			}

			> div:nth-child(1) {
				font-size: 18px;
				font-weight: 400;
				color: #333333;
				line-height: 1em;
				margin-bottom: 10px;

				@media screen and (max-width: 768px) {
					font-size: 12px;
					color: #666666;
				}
			}

			> div:nth-child(2) {
				font-size: 14px;
				font-weight: 400;
				color: #999999;
				margin-bottom: 20px;
				line-height: 1em;

				@media screen and (max-width: 768px) {
					font-size: 12px;
					color: #999999;
					margin-bottom: 10px;
				}
			}

			.el-textarea__inner {
				min-height: 33px !important;

				@media screen and (max-width: 768px) {
					min-height: 140px !important;
				}
			}

			.el-textarea__inner:focus {
				border-color: $color-primary;
			}
		}
	}
}
</style>
<style lang="scss">
.colorPickerGridBox {
	display: grid;
	grid-template-columns: repeat(auto-fill, 24px);
	grid-auto-rows: 24px;
	grid-gap: 5px;
	gap: 5px;
	justify-content: space-between;
	max-height: 268px;
	overflow-y: auto;

	> div {
		border: 1px solid transparent;

		&:hover {
			border-color: $color-primary;
		}
	}
}

.colorPopper .btn-close {
	right: -30px !important;
	top: -30px !important;
}

.allImgDialogBox {
	display: grid;
	grid-template-columns: repeat(auto-fill, 150px);
	grid-auto-rows: 150px;
	gap: 10px;
	margin-top: 1rem;
	justify-content: space-around;

	@media screen and (max-width: 768px) {
		grid-template-columns: repeat(2, 1fr);
		justify-content: center;
		grid-auto-rows: auto;
	}

	.item {
		border: 1px solid #e6e6e6;
		border-radius: 6px;
		cursor: pointer;
		position: relative;
		background: url(data:image/png;base64,UklGRl4AAABXRUJQVlA4TFIAAAAvI8AIABcgEEj8YRthDoFs4g/7EJpANvGHfQht/gMrP7CKbKvNSS8SkIBKJERCb0cnf0RARP9Da1Yj/AdOe5LC61z2zLVmNUAeyy4GgNeZpP8B);
		box-shadow: 0 2px 4px rgb(0 0 0 / 12%), 0 0 6px rgb(0 0 0 / 4%);

		img {
			width: 100%;
			height: 100%;
			object-fit: contain;
		}

		.btn {
			button {
				width: 40px;
				height: 40px;
			}

			position: absolute;
			display: flex;
			left: 0;
			top: 0;
			bottom: 0;
			right: 0;
			background-color: rgba(0, 0, 0, 0.4);
			justify-content: center;
			align-items: center;
			opacity: 0;
			transition: all 0.3s;

			b {
				width: 15px;
			}
		}
	}

	.item:hover {
		.btn {
			opacity: 1;
		}
	}
}

.el-select-dropdown .el-scrollbar .el-select-dropdown__list{
	padding: 0;
}
</style>