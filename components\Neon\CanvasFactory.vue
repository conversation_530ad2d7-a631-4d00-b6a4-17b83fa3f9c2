<template>
  <div class="canvas">
    <img class="canvasDom"
      v-if="isImg"
      :src="canvasUrl"
      alt="">
    <canvas v-else
      id="canvas"
      class="canvasDom canvasEle"
      :data-src="canvasUrl">
    </canvas>
  </div>
</template>
<script>
import { Loading } from "element-ui";
import { debounce, getImageSize } from "@/utils/utils";
export default {
  props: {
    parameters: {
      type: Object,
    },
    selectedFontData: {
      type: Array,
    },
    neonSampleData: {
      type: Object,
    },
    currentSampleData: {
      type: Object,
    },
    currentFontSize: {
      type: Object,
    },
    needCanvas: {
      type: Boolean,
    },
    isImgTemplate: {
      type: Number,
    },
    isText: {
      type: Boolean,
    },
    textDesign: {
      type: Boolean,
    },
    isImg: {
      type: Boolean,
      default: () => false,
    },
    fontWidth: {},
    fontHeight: {},
    customSizeFlag: {
      type: Boolean,
    },
    unitSign: {
      type: String,
    },
    unitConvertRatio: {
      type: Number,
    },
    uploadArtwork: {
      type: [Object, String],
      default: () => {},
    },
    isAcrylicNeon: {
      type: Boolean,
    },
    tabName: {
      type: String,
    },
    isBack: {
      type: Boolean,
    },
  },
  data() {
    return {
      width: 1344,
      height: 800,
      canvasUrl: "",
      canvas: null,
      canvasBg: null,
      // 发光模糊的颜色组
      badColor: ["#ffe0b3", "#fffcf8", "#42ff27", "#2eebff", "#fff64a"],

      canvasFt: null,
      canvasCtx: null,

      canvasImg: null,
      canvasImgCtx: null,

      canvasBgCtx: null,
      canvasFtCtx: null,
      canvasRuler: null,
      canvasRulerCtx: null,
      animatClock: [],
      maxWidthData: [],
      canvasList: [],
      animatInitList: [],
      // 文字区域偏移和总宽高
      dxx: Infinity,
      dyy: Infinity,
      dmw: 0,
      dht: 0,
      fontCustomSize: null,
      // 纯文字设计背景已渲染 不需要重复渲染 tips:纯文字设计不闪
      textBgHasRender: false,
    };
  },
  methods: {
    /**
     * 供外部调用 修改自定义尺寸触发
     */
    changeCustomSizeChild(val) {
      this.fontCustomSize = val.rectInfo;
      console.log("fontCustomSize", this.fontCustomSize);
      if (val?.noDraw) return;
      this.debounceCanvasFt();
    },
    /**
     * 分步绘制 （解决切换文字也加载背景的黑屏问题）
     * canvasFactoryBg 绘制背景
     * canvasFactory   绘制文字
     */
    async canvasFactoryType() {
      this.canvas = document.querySelector("#canvas");
      this.canvasCtx = this.canvas.getContext("2d");
      this.canvas.width = this.width;
      this.canvas.height = this.height;

      if (this.isText) {
        if (!this.textBgHasRender) {
          await this.canvasFactoryBg();
          this.textBgHasRender = true;
        }
      } else {
        await this.canvasFactoryBg();
      }
      await this.canvasFactory();
    },

    async canvasFactoryBg() {
      let loadingInstance = Loading.service({
        lock: true,
        text: "Loading",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
        target: ".canvas",
      });

      try {
        // 创建 canvas 和 context
        this.canvasBg = document.createElement("canvas");
        this.canvasBgCtx = this.canvasBg.getContext("2d");
        this.canvasBg.width = this.width;
        this.canvasBg.height = this.height;

        let bg;
        if (
          this.neonSampleData &&
          this.neonSampleData.fontPosttion &&
          this.needCanvas
        ) {
          if (!this.parameters.switchLight) {
            bg = this.currentSampleData?.img
              ? this.currentSampleData.img
              : this.neonSampleData.color[0].img;
          } else {
            bg = this.neonSampleData.darkImg;
          }
        } else {
          if (this.isAcrylicNeon) {
            bg =
              "https://static-oss.gs-souvenir.com/web/quoteManage/20250123/noBgA_2036063Yisp7_20250123mAGZya.png";
          } else {
            bg =
              "https://static-oss.gs-souvenir.com/web/quoteManage/20250123/noBg_203606zit8Yb_20250123Hiiejc.jpg";
          }
        }
        if (bg) {
          // 等待图片加载完成后再绘制
          await this.imgFactory(
            this.canvasBgCtx,
            0,
            0,
            this.width,
            this.height,
            bg
          );
        }
        // 清除并重新绘制背景
        this.canvasCtx.clearRect(0, 0, this.width, this.height);
        this.canvasCtx.drawImage(this.canvasBg, 0, 0);
      } catch (error) {
        console.error("Error during canvas rendering:", error);
      } finally {
        // 确保 loading 会关闭
        this.$nextTick(() => {
          loadingInstance.close();
        });
      }
    },

    /**
     * 获得placeholder文字的位置 返回三个对齐点
     */
    getTemplateTextPosition(obj) {
      const { fontFamily, fontSize, left, top, rotate, text } = obj;
      // 获得这一行文字的宽高
      const { width } = this.getTextWidth(text, fontSize, fontFamily);
      const positionInfo = {
        left: {
          x: Number(left),
          y: Number(top),
        },
        center: {
          x: Number(left) + Number(width) / 2,
          y:
            Number(top) +
            (Number(width) / 2) * Math.tan((Number(rotate) * Math.PI) / 180),
        },
        right: {
          x: Number(left) + Number(width),
          y:
            Number(top) +
            Number(width) * Math.tan((Number(rotate) * Math.PI) / 180),
        },
      };
      return positionInfo;
    },

    async canvasFactoryImg() {
      this.canvas = document.querySelector("#canvas");
      this.canvasCtx = this.canvas.getContext("2d");
      if (this.timer0 || this.anima0) {
        this.selectedFontData.forEach((x, i) => {
          clearInterval(this[`timer${i}`]);
          cancelAnimationFrame(this[`anima${i}`]);
        });
      }
      this.canvas.width = this.width;
      this.canvas.height = this.height;
      await this.canvasFactoryBg();

      this.canvasImg = document.createElement("canvas");
      this.canvasImgCtx = this.canvasImg.getContext("2d");
      try {
        const wAndH = await getImageSize(this.uploadArtwork.secure_url);
        const substring = {
          width: wAndH.width - this.width,
          height: wAndH.height - this.height,
        };
        let ratio = 1;
        if (substring.height > 0 || substring.width > 0) {
          if (substring.width > substring.height) {
            ratio = (this.width - 650) / wAndH.width;
          } else {
            ratio = (this.height - 200) / wAndH.height;
          }
        }
        const width = wAndH.width * ratio;
        const height = wAndH.height * ratio;
        this.canvasImg.width = width;
        this.canvasImg.height = height;
        const marginLeft = (this.width - width) / 2;
        const marginTop = (this.height - height) / 2;
        await this.imgFactory(
          this.canvasImgCtx,
          0,
          0,
          width,
          height,
          this.uploadArtwork.secure_url
        );

        this.canvasCtx.drawImage(
          this.canvasImg,
          marginLeft,
          marginTop,
          width,
          height
        );
      } catch (err) {
        console.log("canvasFactoryImgError", err);
      }
    },

    /**
     * 绘制文字
     * 类型
     *     1. 纯文字
     *        1.1 单行文字 炫彩 单色 变色 无灯
     *        1.2 多行文字 炫彩 单色 变色 无灯
     *     2. 模板文字
     *        2.1 多行文字 单色 无灯
     */
    async canvasFactory() {
      try {
        if (this.neonSampleData && this.neonSampleData.fontPosttion) {
          this.canvasFt = document.createElement("canvas");
          this.canvasFtCtx = this.canvasFt.getContext("2d", {
            willReadFrequently: true,
          });
          this.canvasFt.width = this.width;
          this.canvasFt.height = this.height;
          if (this.canvasCtx && this.canvasBg)
            this.canvasCtx.drawImage(this.canvasBg, 0, 0);
          let fontPosttion = this.neonSampleData.fontPosttion;
          if (!this.isImgTemplate) {
            if (this.isText) {
              this.canvasList = [];
              this.maxWidthData = [];
              this.animatClock = [];
              this.canvasRuler = document.createElement("canvas");
              this.canvasRulerCtx = this.canvasRuler.getContext("2d");
              this.canvasRuler.width = this.width;
              this.canvasRuler.height = this.height;

              for (let i = 0; i < this.selectedFontData.length; i++) {
                const obj = {
                  dom: document.createElement("canvas"),
                  ctx: null,
                };
                obj.ctx = obj.dom.getContext("2d");
                obj.dom.width = this.width;
                obj.dom.height = this.height;
                this.canvasList.push(obj);
                this.$data[`timer${i}`] = null;
                this.$data[`anima${i}`] = null;
                this.maxWidthData.push(0);
                this.animatClock.push(0);
                let color =
                    this.selectedFontData[i]["Select Color"]?.paramCode ||
                    fontPosttion[i].color ||
                    "",
                  tubeColor = this.selectedFontData[i].tubeColor,
                  fontFamily =
                    this.selectedFontData[i]["Select Font"]?.paramName,
                  fontLineHeight =
                    this.selectedFontData[i]["Select Font"]?.fontLineHeignt,
                  multipleColor =
                    this.selectedFontData[i]["Select Color"]?.isNeonColorful ||
                    0,
                  textAlign = this.selectedFontData[i].textAlign,
                  allColor =
                    color && color.includes(",") ? color.split(",") : [color],
                  text = "";

                if (fontFamily === "Google Sans") fontFamily += " Lighter";
                if (this.needCanvas) {
                  text = this.selectedFontData[i]["Your Text"]
                    ? this.selectedFontData[i]["Your Text"]
                    : this.neonSampleData.fontPosttion[i]["text"];
                }
                if (!this.parameters.switchLight) {
                  // 多颜色模式
                  if (allColor.length > 1 && multipleColor) {
                    // 渐变彩色
                    this.canvasList[i].ctx.clearRect(
                      0,
                      0,
                      this.width,
                      this.height
                    );
                    clearInterval(this[`timer${i}`]);
                    cancelAnimationFrame(this[`anima${i}`]);
                    let { yy, rx } = await this.gradientFontFactory(
                      this.canvasList[i].ctx,
                      fontPosttion[i].left,
                      fontPosttion[i].top,
                      fontPosttion[i].rotate,
                      fontLineHeight,
                      fontPosttion[i].fontSize,
                      // this.device == 'mb' ? '120px':'80px',
                      fontFamily,
                      text,
                      false,
                      textAlign,
                      null,
                      i
                    );
                    const position = await this.mesureFontRelH(
                      this.canvasList[i].ctx
                    );
                    this.animatInitList[i] = this.animatInitList[i] + 1;
                    const currentInitIndex = this.animatInitList[i];

                    const animatFunc = async () => {
                      if (currentInitIndex < this.animatInitList[i]) return;
                      this.clearManyCanvas(i, position);
                      await this.gradientFontFactory(
                        this.canvasList[i].ctx,
                        fontPosttion[i].left,
                        fontPosttion[i].top,
                        fontPosttion[i].rotate,
                        fontLineHeight,
                        fontPosttion[i].fontSize,
                        // this.device == 'mb' ? '120px':'80px',
                        fontFamily,
                        text,
                        false,
                        textAlign,
                        allColor,
                        i
                      );
                      if (
                        this.animatClock[i] >
                        this.canvasList[i].ctx.measureText(text).width /
                          allColor.length
                      ) {
                        // 拿第一个元素插入到末尾
                        let tail = allColor.pop();
                        allColor.unshift(tail);
                        this.animatClock[i] = 0;
                      }
                      this.animationFont(
                        this.canvasList[i].ctx,
                        rx,
                        yy,
                        allColor,
                        i,
                        position
                      );
                      // 绘制到文本画布
                      this.canvasFtCtx.drawImage(this.canvasList[i].dom, 0, 0);
                      // 绘制到真实画布
                      this.canvasCtx.drawImage(this.canvasFt, 0, 0);
                      // 绘制尺寸轴
                      this.canvasCtx.drawImage(this.canvasRuler, 0, 0);
                      this[`anima${i}`] = requestAnimationFrame(animatFunc);
                    };

                    if (multipleColor) {
                      await animatFunc();
                    }
                  }
                  // 多色变
                  else if (allColor.length > 1 && !multipleColor) {
                    let index = 1;
                    clearInterval(this[`timer${i}`]);
                    cancelAnimationFrame(this[`anima${i}`]);
                    await this.fontFactory(
                      this.canvasList[i].ctx,
                      fontPosttion[i].left,
                      fontPosttion[i].top,
                      fontPosttion[i].rotate,
                      fontLineHeight,
                      allColor[0],
                      fontPosttion[i].fontSize,
                      // this.device == 'mb' ? '120px':'80px',
                      fontFamily,
                      text,
                      true,
                      textAlign,
                      i,
                      tubeColor
                    );
                    const position = await this.mesureFontRelH(
                      this.canvasList[i].ctx
                    );
                    this.canvasFtCtx.drawImage(this.canvasList[i].dom, 0, 0);
                    this.canvasCtx.drawImage(this.canvasFt, 0, 0);
                    this[`timer${i}`] = setInterval(async () => {
                      // 清除尺子
                      this.canvasRulerCtx.clearRect(
                        0,
                        0,
                        this.width,
                        this.height
                      );
                      this.clearManyCanvas(i, position);
                      await this.fontFactory(
                        this.canvasList[i].ctx,
                        fontPosttion[i].left,
                        fontPosttion[i].top,
                        fontPosttion[i].rotate,
                        fontLineHeight,
                        allColor[index % allColor.length],
                        fontPosttion[i].fontSize,
                        // this.device == 'mb' ? '120px':'80px',
                        fontFamily,
                        text,
                        true,
                        textAlign,
                        i,
                        tubeColor
                      );
                      index++;

                      // 将这个文字绘制到文字画布上 用于计算尺寸需要
                      this.canvasFtCtx.drawImage(this.canvasList[i].dom, 0, 0);
                      await this.emitHandW(this.canvasFtCtx);
                      await this.drawRectLine(
                        this.canvasRulerCtx,
                        this.dxx,
                        this.dyy,
                        this.dmw,
                        this.dht,
                        this.formatSize
                      );
                      // 尺寸绘制完成后 将文字和轴绘制到真实画布
                      this.canvasCtx.drawImage(this.canvasRuler, 0, 0);
                      this.canvasCtx.drawImage(this.canvasFt, 0, 0);
                    }, 500);
                  }
                  // 纯单色
                  else {
                    clearInterval(this[`timer${i}`]);
                    cancelAnimationFrame(this[`anima${i}`]);
                    await this.fontFactory(
                      this.canvasList[i].ctx,
                      fontPosttion[i].left,
                      fontPosttion[i].top,
                      fontPosttion[i].rotate,
                      fontLineHeight,
                      color,
                      fontPosttion[i].fontSize,
                      // this.device == 'mb' ? '120px':'80px',
                      fontFamily,
                      text,
                      true,
                      textAlign,
                      i,
                      tubeColor
                    );
                    this.canvasFtCtx.drawImage(this.canvasList[i].dom, 0, 0);
                  }
                } else {
                  this.selectedFontData.forEach((x, ind) => {
                    this[`timer${ind}`] && clearInterval(this[`timer${ind}`]);
                    this[`anima${ind}`] &&
                      cancelAnimationFrame(this[`anima${ind}`]);
                  });
                  this.clearManyCanvas(i);
                  this.canvasFtCtx.shadowOffsetX = 1;
                  this.canvasFtCtx.shadowOffsetY = 1;
                  this.canvasFtCtx.shadowBlur = 0;
                  this.canvasFtCtx.shadowColor = "black";
                  await this.fontFactory(
                    this.canvasList[i].ctx,
                    fontPosttion[i].left,
                    fontPosttion[i].top,
                    fontPosttion[i].rotate,
                    fontLineHeight,
                    color,
                    fontPosttion[i].fontSize,
                    // this.device == 'mb' ? '120px':'80px',
                    fontFamily,
                    text,
                    false,
                    textAlign,
                    i,
                    tubeColor
                  );
                  // this.canvasList[i].ctx.fillStyle = "rgba(255, 255, 255, 0)";
                  this.canvasFtCtx.drawImage(this.canvasList[i].dom, 0, 0);
                }
              }

              // 将文字画布绘制到真实画布
              this.canvasCtx.drawImage(this.canvasFt, 0, 0);

              // code1
              await this.emitHandW(this.canvasFtCtx);
              await this.drawRectLine(
                this.canvasRulerCtx,
                this.dxx,
                this.dyy,
                this.dmw,
                this.dht,
                this.formatSize
              );
              this.canvasCtx.drawImage(this.canvasRuler, 0, 0);
            } else {
              this.canvasCtx.clearRect(0, 0, this.width, this.height);
              this.canvasCtx.drawImage(this.canvasBg, 0, 0);
              this.canvasFtCtx.clearRect(0, 0, this.width, this.height);

              for (let i = 0; i < this.selectedFontData.length; i++) {
                const obj = {
                  dom: document.createElement("canvas"),
                  ctx: null,
                };
                obj.ctx = obj.dom.getContext("2d");
                obj.dom.width = this.width;
                obj.dom.height = this.height;
                this.canvasList.push(obj);
                let color =
                    this.selectedFontData[i]["Select Color"]?.paramCode ||
                    fontPosttion[i].color ||
                    "",
                  tubeColor = this.selectedFontData[i].tubeColor,
                  fontFamily =
                    this.selectedFontData[i]["Select Font"]?.paramName,
                  fontLineHeight =
                    this.selectedFontData[i]["Select Font"]?.fontLineHeignt,
                  textAlign = this.selectedFontData[i].textAlign,
                  text = "";
                if (fontFamily === "Google Sans") fontFamily += " Lighter";
                // 测量普通模板中文字left center right 三个对齐位置
                let templateTextPositionMap = this.getTemplateTextPosition(
                  this.neonSampleData.fontPosttion[i]
                );
                if (this.needCanvas) {
                  text = this.selectedFontData[i]["Your Text"]
                    ? this.selectedFontData[i]["Your Text"]
                    : this.neonSampleData.fontPosttion[i]["text"];
                }
                if (!this.parameters.switchLight) {
                  clearInterval(this[`timer${i}`]);
                  cancelAnimationFrame(this[`anima${i}`]);
                  this.clearManyCanvas(i);
                  await this.fontFactory(
                    this.canvasList[i].ctx,
                    fontPosttion[i].left,
                    fontPosttion[i].top,
                    fontPosttion[i].rotate,
                    fontLineHeight,
                    color,
                    fontPosttion[i].fontSize,
                    fontFamily,
                    text,
                    true,
                    textAlign,
                    i,
                    tubeColor,
                    templateTextPositionMap
                  );
                  this.canvasFtCtx.drawImage(this.canvasList[i].dom, 0, 0);
                  this.canvasCtx.drawImage(this.canvasFt, 0, 0);
                } else {
                  clearInterval(this[`timer${i}`]);
                  cancelAnimationFrame(this[`anima${i}`]);
                  this.clearManyCanvas(i);
                  this.canvasFtCtx.shadowOffsetX = 1;
                  this.canvasFtCtx.shadowOffsetY = 1;
                  this.canvasFtCtx.shadowBlur = 0;
                  this.canvasFtCtx.shadowColor = "black";
                  await this.fontFactory(
                    this.canvasList[i].ctx,
                    fontPosttion[i].left,
                    fontPosttion[i].top,
                    fontPosttion[i].rotate,
                    fontLineHeight,
                    color,
                    fontPosttion[i].fontSize,
                    fontFamily,
                    text,
                    false,
                    textAlign,
                    i,
                    tubeColor,
                    templateTextPositionMap
                  );
                  this.canvasFtCtx.fillStyle = "rgba(255, 255, 255, 0)";
                  this.canvasFtCtx.drawImage(this.canvasList[i].dom, 0, 0);
                  this.canvasCtx.drawImage(this.canvasFt, 0, 0);
                }
              }
            }
          }
          if (this.canvas) {
            this.canvasUrl = this.canvas.toDataURL("image/png");
            this.$emit("changeImgUrl", this.canvasUrl);
          }
          // loadingInstance.close();
        }
      } catch (err) {
        console.log(err);
      }
    },
    /**
     * 测量画布文字的像素宽高
     * @param context canvas2d对象
     * @return minX maxX minY maxY 四个坐标
     */
    async mesureFontRelH(context) {
      context.willReadFrequently = true;
      let imageData = context.getImageData(
        0,
        0,
        this.canvasFt.width,
        this.canvasFt.height
      );
      let data = imageData.data;
      let minX = Infinity;
      let minY = Infinity;
      let maxX = -Infinity;
      let maxY = -Infinity;

      for (let y = 0; y < this.canvasFt.height; y++) {
        for (let x = 0; x < this.canvasFt.width; x++) {
          let index = (y * this.canvasFt.width + x) * 4;
          let r = data[index];
          let g = data[index + 1];
          let b = data[index + 2];
          let a = data[index + 3];
          if (a > 0) {
            // 如果像素不是完全透明的
            if (minX > x) minX = x;
            if (maxX < x) maxX = x;
            if (minY > y) minY = y;
            if (maxY < y) maxY = y;
          }
        }
      }
      this.dxx = minX;
      this.dyy = minY;
      let width = maxX - minX + 1;
      let height = maxY - minY + 1;
      return {
        minX,
        minY,
        width,
        height,
      };
    },
    /**
     * 绘制背景方法
     * @param context canvas2d对象
     */
    async imgFactory(context, x, y, width, height, url) {
      return new Promise((resolve, reject) => {
        // if(!url) reject()
        this.imgLoadFun(url).then((res) => {
          context.drawImage(res, x, y, width, height);
          resolve();
        });
      });
    },
    imgLoadFun(url) {
      return new Promise((resolve) => {
        if (!url) {
          resolve();
          return;
        }
        let image = new Image();
        image.setAttribute("crossOrigin", "anonymous");
        image.src = url;
        image.onload = () => {
          resolve(image);
        };
        image.onerror = () => {
          resolve();
          console.log("image load failed");
        };
      });
    },
    // 纯色文字
    fontFactory(
      context,
      x,
      y,
      rotate,
      fontLineHeight,
      color = "red",
      fontSize = "80px",
      fontFamily = "Alexa",
      text,
      lightOn,
      textAlign,
      index,
      tubeColor,
      templateTextPositionMap
    ) {
      return new Promise(async (resolve, reject) => {
        if (text) {
          let mText = text.split("\n"),
            maxWidth = 0,
            lineHeight = 0,
            offsetForAlign = 0,
            maxHeight = 0,
            xx = parseInt(x),
            yy = parseInt(y),
            rx = xx;
          // 纯文字计算宽高
          if (this.isText) {
            let widthArr = [];
            let heightArr = [];
            for (let i = 0; i < mText.length; i++) {
              widthArr.push(
                this.getTextWidth(
                  mText[i].trim(),
                  fontSize,
                  fontFamily,
                  fontLineHeight
                ).width
              );
              heightArr.push(
                this.getTextWidth(
                  mText[i].trim(),
                  fontSize,
                  fontFamily,
                  fontLineHeight
                ).height
              );
            }
            maxWidth = Math.max(...widthArr);
            if (this.maxWidthData[index] < maxWidth)
              this.maxWidthData[index] = maxWidth;
            maxHeight = heightArr[0];
            offsetForAlign =
              textAlign == "center"
                ? 0
                : textAlign == "left"
                ? -maxWidth / 2
                : +maxWidth / 2;
            if (this.textDesign) {
              xx = this.width / 2 + offsetForAlign;
              rx = this.width / 2 - maxWidth / 2;
            }
          } else {
            xx = templateTextPositionMap[textAlign].x;
            yy = templateTextPositionMap[textAlign].y;
          }
          context.fillStyle = color;
          if (tubeColor.name != "darkWhite") color = tubeColor.color;
          // 绘制每一行文字
          for (let i = 0; i < mText.length; i++) {
            context.save();
            context.translate(xx, yy + lineHeight);
            context.rotate((rotate * Math.PI) / 180);
            context.translate(-xx, -yy - lineHeight);
            context.font = `${
              this.isAcrylicNeon ? "bolder" : "normal"
            } ${fontSize} ${fontFamily}`;
            await document.fonts.load(context.font);

            context.textBaseline = "top";
            context.textAlign = textAlign;
            context.miterLimit = 2;
            context.lineJoin = "circle";

            function setShadow(blur = 5, color = "white") {
              context.shadowOffsetX = 0;
              context.shadowOffsetY = 0;
              context.shadowBlur = blur;
              context.shadowColor = color;
              context.fillText(mText[i].trim(), xx, yy + lineHeight);
            }

            if (lightOn) {
              //阴影
              if (this.isAcrylicNeon) {
                setShadow(30, color);
              } else {
                setShadow();
                // setShadow(5, color);
              }
              //   setShadow(undefined, color);
              setShadow(8, color);
            } else {
              context.fillStyle = tubeColor.color;
              context.fillText(mText[i].trim(), xx, yy + lineHeight);
            }
            lineHeight += Number(fontSize.split("px")[0] * fontLineHeight);
            context.restore();
          }
          resolve();
        } else {
          console.log("no default text");
          resolve();
        }
      });
    },

    // 清除画布
    async clearManyCanvas(i, position) {
      if (!position) {
        this.canvasList[i].ctx.clearRect(0, 0, this.width, this.height);
        this.canvasCtx.clearRect(0, 0, this.width, this.height);
        this.canvasCtx.drawImage(this.canvasBg, 0, 0);
      } else {
        // 清除单个文字画布
        this.canvasList[i].ctx.clearRect(0, 0, this.width, this.height);
        // 清除文字画布
        this.canvasFtCtx.clearRect(
          position.minX,
          position.minY,
          position.width,
          position.height
        );
        // 清除画布
        this.canvasCtx.clearRect(
          position.minX,
          position.minY,
          position.width,
          position.height
        );
        // 绘制背景
        this.canvasCtx.drawImage(this.canvasBg, 0, 0);
      }
    },
    // 绘制尺寸轴
    drawRectLine(context, xx, yy, w, h, value) {
      return new Promise(async (resolve, reject) => {
        if (!this.parameters.showSize) reject();
        let mesureArr = [];
		let fontSize = this.device === 'mb' ? 18 : 14
        value.forEach((x) => {
          mesureArr.push(this.getTextWidth(x, fontSize, "google-sans"));
        });
        context.font = `${ fontSize }px google-sans`;
        context.fillStyle = "white";
        // 画横轴
        context.moveTo(xx, yy + h + 20 + 3); // 画线头
        context.lineTo(xx, yy + h + 20 - 3);
        context.moveTo(xx, yy + h + 20);
        context.lineTo(xx + w / 2 - mesureArr[0].width, yy + h + 20);
        context.fillText(
          `${value[0]}`,
          xx + w / 2 - mesureArr[0].width + 5,
          yy + h + 20 + mesureArr[0].height / 2
        );
        context.moveTo(xx + w / 2 + mesureArr[0].width, yy + h + 20);
        context.lineTo(xx + w, yy + h + 20);
        context.moveTo(xx + w, yy + h + 20 + 3); // 画线头
        context.lineTo(xx + w, yy + h + 20 - 3);
        // 画纵轴
        context.moveTo(xx + w + 50 - 3, yy + h); // 画线头
        context.lineTo(xx + w + 50 + 3, yy + h);
        context.moveTo(xx + w + 50, yy + h);
        context.lineTo(xx + w + 50, yy + mesureArr[1].height + h / 2);
        context.fillText(
          `${value[1]}`,
          xx + w + 50 - mesureArr[1].width / 2,
          yy + 10 - mesureArr[1].height + h / 2
        );
        context.moveTo(xx + w + 50, yy - 20 + mesureArr[1].height + h / 2);
        context.lineTo(xx + w + 50, yy);
        context.moveTo(xx + w + 50 - 3, yy); // 画线头
        context.lineTo(xx + w + 50 + 3, yy);
        context.strokeStyle = "white";
        context.stroke();
        resolve();
      });
    },

    animationFont(context, xx, yy, allColors, idx, position) {
      // 平移多色
      let gradient = context.createLinearGradient(
        xx + this.animatClock[idx],
        yy + this.animatClock[idx],
        xx + this.maxWidthData[idx] + this.animatClock[idx],
        yy + this.animatClock[idx]
      );
      allColors.forEach((x, index) => {
        gradient.addColorStop(index / allColors.length, x);
      });
      context.fillStyle = gradient;
      let speed = position.width / 1000;
      this.animatClock[idx] += speed;
    },
    // 渐变文字
    gradientFontFactory(
      context,
      x,
      y,
      rotate,
      fontLineHeight,
      fontSize = "80px",
      fontFamily = "Alexa",
      text,
      staticFlag,
      textAlign,
      allColors,
      index
    ) {
      return new Promise(async (resolve, reject) => {
        if (text && this.tabName != 'Upload') {
          let mText = text.split("\n"),
            maxWidth = 0,
            maxHeight = 0,
            lineHeight = 0,
            offsetForAlign = 0,
            xx = parseInt(x),
            yy = parseInt(y),
            rx = xx;
          if (this.isText) {
            let widthArr = [];
            let heightArr = [];
            for (let i = 0; i < mText.length; i++) {
              widthArr.push(
                this.getTextWidth(mText[i].trim(), fontSize, fontFamily).width
              );
              heightArr.push(
                this.getTextWidth(mText[i].trim(), fontSize, fontFamily).height
              );
            }
            maxWidth = Math.max(...widthArr);
            if (this.maxWidthData[index] < maxWidth)
              this.maxWidthData[index] = maxWidth;
            maxHeight = heightArr[0];
            offsetForAlign =
              textAlign == "center"
                ? 0
                : textAlign == "left"
                ? -maxWidth / 2
                : +maxWidth / 2;
            if (this.textDesign) {
              xx = this.width / 2 + offsetForAlign;
              rx = this.width / 2 - maxWidth / 2;
            }
            // yy = this.height / 2 - (maxHeight * mText.length) / 2;
          }
          let gradient;
          if (staticFlag) {
            // 平移多色
            gradient = context.createLinearGradient(
              rx,
              yy,
              rx + this.maxWidthData[index],
              yy
            );

            allColors.forEach((x, id) => {
              gradient.addColorStop(id / allColors.length, x);
            });
          }

          for (let i = 0; i < mText.length; i++) {
            context.save();
            context.translate(xx, yy + lineHeight);
            context.rotate((rotate * Math.PI) / 180);
            context.translate(-xx, -yy - lineHeight);
            context.font = `${
              this.isAcrylicNeon ? "bolder" : "normal"
            } ${fontSize} ${fontFamily}`;
            await document.fonts.load(context.font);

            context.fillStyle = gradient;
            context.textBaseline = "top";
            context.textAlign = textAlign;
            context.miterLimit = 2;
            context.lineJoin = "circle";

            context.shadowOffsetX = 2;
            context.shadowOffsetY = 2;
            context.shadowBlur = 0;
            context.shadowColor = "rgba(0,0,0,.8)";
            context.fillText(mText[i].trim(), xx, yy + lineHeight);

            lineHeight += Number(fontSize.split("px")[0] * fontLineHeight);
            context.restore();
          }

          resolve({
            context,
            xx,
            yy,
            maxWidth,
            maxHeight,
            lineHeight,
            mText,
            rx,
          });
        } else {
          console.log("no default text");
          reject();
        }
      });
    },
    // 测量文字的宽高
    getTextWidth(text, fontSize, fontFamily) {
      let canvas = document.createElement("canvas");
      let context = canvas.getContext("2d");
      context.font = `${
        this.isAcrylicNeon ? "bolder" : "normal"
      } ${fontSize} ${fontFamily}`;
      let textMetrics = context.measureText(text);
      let width = textMetrics.width;
      let height =
        textMetrics.actualBoundingBoxDescent +
        textMetrics.actualBoundingBoxAscent;
      return { width, height };
    },
    async emitHandW(ctx, pxToInch) {
      const { height, width } = await this.mesureFontRelH(ctx);
      this.dmw = width;
      this.dht = height;
      let subs = 16;
      if (
        this.parameters.switchLight ||
        this.selectedFontData.find((x) => x["Select Color"])?.["Select Color"]
          ?.paramName == "Gradient Changing Color"
      ) {
        subs = 0;
      }
      this.$emit(
        "changeRatio",
        Number(((height - subs) / (width - subs)).toFixed(2))
      );
    },

    /**
     *  仅用于外部调用的函数
     */
    async drawAnimatStatic() {
		console.log('drawAnimatStatic', this.selectedFontData)
      if (
        !this.selectedFontData.some((x) => x["Select Color"]?.isNeonColorful) ||
        this.parameters.switchLight
      ) {
        return;
      }
      for (let i = 0; i < this.selectedFontData.length; i++) {
        let color = this.selectedFontData[i]["Select Color"]?.paramCode || "",
          fontPosttion = this.neonSampleData.fontPosttion,
          fontFamily = this.selectedFontData[i]["Select Font"]?.paramName,
          fontLineHeight =
            this.selectedFontData[i]["Select Font"]?.fontLineHeignt,
          textAlign = this.selectedFontData[i].textAlign,
          allColor = color && color.includes(",") ? color.split(",") : [color],
          multipleColor =
            this.selectedFontData[i]["Select Color"]?.isNeonColorful,
          text = "";
        if (fontFamily === "Google Sans") fontFamily += " Lighter";
        if (this.needCanvas) {
          text = this.selectedFontData[i]["Your Text"]
            ? this.selectedFontData[i]["Your Text"]
            : this.neonSampleData.fontPosttion[i]["text"];
        } else {
          text = "";
        }
        this.clearManyCanvas(i);
        if (multipleColor) {
          await this.gradientFontFactory(
            this.canvasList[i].ctx,
            fontPosttion[i].left,
            fontPosttion[i].top,
            fontPosttion[i].rotate,
            fontLineHeight,
            // fontPosttion[i].fontSize,
            this.device == "mb" ? "120px" : "80px",
            fontFamily,
            text,
            true,
            textAlign,
            allColor,
            i
          );
        }
        this.canvasFtCtx.drawImage(this.canvasList[i].dom, 0, 0);
        this.canvasCtx.drawImage(this.canvasRuler, 0, 0);
      }
      this.canvasCtx.drawImage(this.canvasFt, 0, 0);
      this.canvasFtCtx.clearRect(0, 0, this.width, this.height);
      if (this.canvas) {
        this.canvasUrl = this.canvas.toDataURL("image/png");
        this.$emit("changeImgUrl", this.canvasUrl);
      }
    },
    changeTemplate() {
      this.textBgHasRender = false;
    },
  },
  watch: {
    parameters: {
      handler(newV) {
        this.debounceCanvasFactory();
      },
      deep: true,
    },
    selectedFontData: {
      handler(newV) {
        if (this.tabName == "Upload" && this.isAcrylicNeon) return;
        this.debounceCanvasFt();
      },
      deep: true,
    },
    textAlign: {
      handler(newV) {
        this.debounceCanvasFt();
      },
    },
    fontHeight: {
      handler(newV) {
        if (this.tabName == "Upload" && (this.isAcrylicNeon || this.isBack))
          return;
        this.debounceCanvasFt();
      },
    },
    neonSampleData: {
      handler(newV) {
        if (this.tabName == "Upload" && (this.isAcrylicNeon || this.isBack))
          return;
        this.fontCustomSize = null;
        this.debounceCanvasFactory();
      },
      deep: true,
    },
    currentSampleData: {
      handler(newV) {
        if (this.tabName == "Upload" && (this.isAcrylicNeon || this.isBack))
          return;
        this.debounceCanvasFactory();
      },
      deep: true,
    },
    uploadArtwork: {
      handler(newV) {
        newV.original_filename && this.canvasFactoryImg();
      },
    },
    tabName: {
      handler(newV) {
        if (this.isAcrylicNeon && newV == "Upload") {
          this.canvasFactoryImg();
        }
      },
    },

    // needCanvas: {
    //   handler(newV) {
    //     this.debounceCanvasFactory();
    //   },
    // },
  },
  mounted() {
    this.animatInitList = new Array(6).fill(0);
    this.debounceCanvasFactory = debounce(this.canvasFactoryType, 300);
    this.debounceCanvasBg = debounce(this.canvasFactoryBg, 300);
    this.debounceCanvasFt = debounce(this.canvasFactory, 400);
  },
  computed: {
    lang() {
      return this.$store.getters.lang.quote || {};
    },
    textAlign() {
      return this.$store.getters.getTextAlign;
    },
    formatSize() {
      const arr = [
        `${this.fontCustomSize?.width || this.fontWidth}`,
        `${this.fontCustomSize?.height || this.fontHeight}`,
      ];
      return arr.map(
        (x) =>
          ((Number(x) / 0.3937) * this.unitConvertRatio).toFixed(0) +
            this.unitSign || '"'
      );
    },
    device() {
      return this.$store.state.device;
    },
  },
};
</script>
<style lang="scss" scoped>
.canvas {
  // display: none;
  // aspect-ratio: 1344/800;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  .canvasDom {
    width: 100%;
    height: 100%;
    object-fit: cover;
    overflow: hidden;
  }
  .textDom {
    height: 50px;
    width: 200px;
  }
}
</style>
