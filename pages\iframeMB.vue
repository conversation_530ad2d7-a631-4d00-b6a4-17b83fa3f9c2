<template>
	<div class="iframeMB" :theme="$store.state.proTheme">
		<v-card :ripple="false" flat class="part item tools rounded-0 hover-tag" :class="{
			justLine: item.isHidden,
			borderWhite: item.isHidden,
			updown: !item.isHidden,
			forHidden: item.isHidden,
		}" style="min-height: 15px" v-for="(item, index) in pageRowDraftListMB" :key="item.id" :index="index"
			@mouseover="mouseoverFun($event, index, item)" @click.stop="clickFun(item, index)">
			<template v-if="item.isHidden"> </template>
			<template v-else>
				<div v-if="currentLan == 'en-us'" class="transFlag mbTrans" @click.stop="changeTranslateStatus(item, index)"
					:style="`background-color:${item.isTranslate ? '#1e88e5' : '#ccc'}`">
					<b class="icon-ENG"></b>
				</div>
				<div v-if="item.sample&&item.sample.sortIndex" :style="{top:currentLan != 'en-us'?'0':'2em'}" class="transFlag">
					<div>{{ item.sample.sortIndex }}</div>
				</div>
				<component :key="item.randomKey" :is="'modal' + item.sampleData.name" :data.sync="item.sampleData"
					:id="item.sampleData.id" :data-theme="item.theme"></component>
			</template>
		</v-card>
	</div>
</template>

<script>
export default {
	layout: "stripeLayout",
	data () {
		return {
			pageRowDraftListMB: [],
			clickIndex: null,
			clickData: null,
			hoverItem: null,
		};
	},
	methods: {
		clickFun (item, index) {
			this.clickIndex = index;
			this.clickData = item;
			if (
				item.sampleData.type.clickType == "banner" ||
				item.sampleData.type.clickType == "video"
			) {
				let temp = {
					item: item,
					index: index,
					type: "click-banner",
				};
				window.parent.postMessage(temp, "*");
			} else if (item.sampleData.type.clickType == "img") {
				let temp = {
					item: item,
					index: index,
					type: "click-img",
				};
				window.parent.postMessage(temp, "*");
			} else if (
				item.sampleData.type.clickType == "product_crowd" ||
				item.sampleData.type.clickType == "sticker_crowd"
			) {
				let temp = {
					item: item,
					index: index,
					type: "click-product",
				};
				window.parent.postMessage(temp, "*");
			} else if (item.sampleData.type.clickType == "nav_list") {
				let temp = {
					item: item,
					index: index,
					type: "click-navList"
				}
				window.parent.postMessage(temp, "*");
			} else if (item.sampleData.type.clickType == "img_list") {
				let temp = {
					item: item,
					index: index,
					type: "click-imgList"
				}
				window.parent.postMessage(temp, "*");
			}
		},
		//移动端鼠标移入时需要进的操作
		mouseoverFun (event, index, item) {
			// postMessage不能对dom对象进行序列化传递，可在目标对象上添加独一无二的属性，根据属性来进行dom选取
			$("* [data-mouseover]").removeAttr('data-mouseover')
			$(event.target).attr('data-mouseover', 'true')
			let temp = {
				item: item,
				index: index,
				type: "mouseover-list",
			}
			window.parent.postMessage(temp, "*")
		},
		messageEventFun (e) {
			let type = e.data.type
			let data = e.data.payload
			let temp;
			switch (type) {
				case "SET_IFRAME_LIST":
					this.pageRowDraftListMB = data;
					break;
				case "SET_VUEX_STORE":
					this.$store.replaceState(data);
					break;
				case "SET_THEMECOLOR":
					this.$store.getters.setTheme(JSON.parse(data));
					break;
				case "SET_IMAGE":
					if (data.type) {
						this.clickData.sampleData.type.customData.img.value = data.url;
					} else {
						this.clickData.sampleData.type.customData.value = data.url;
					}
					temp = {
						item: this.pageRowDraftListMB,
						type: "MB-list",
					};
					window.parent.postMessage(temp, "*");
					break;
				case "SET_BANNER":
					this.clickData.sampleData.type.customData.value = data.url;
					temp = {
						item: this.pageRowDraftListMB,
						type: "MB-list",
					};
					window.parent.postMessage(temp, "*");
					break;
				case "SET_VIDEO":
					// 当为视频类型时，赋值并且通知buildWeb改变pageRowDraftListMB
					this.clickData.sampleData.type.customData.value = data.url.value;
					if (data.url?.alt) this.clickData.sampleData.type.customData.alt = data.url.alt;
					temp = {
						item: this.pageRowDraftListMB,
						type: "MB-list",
					};
					window.parent.postMessage(temp, "*");
					break;
				case "SET_HOVERITEMDATA":
					this.hoverItem = this.pageRowDraftListMB[data.url] || {}
					break;
				case "SET_STYLEVALUE":
					//鼠标移入并点击事件菜单触发的事件操作hoverItem
					this.hoverItem.sampleData.style = data.url;
					temp = {
						item: this.pageRowDraftListMB,
						type: "MB-list",
					};
					window.parent.postMessage(temp, "*");
					break;
				case "SET_BOXSTYLEVALUE":
					if (data.url.boxStyle) {
						this.hoverItem.sampleData.boxStyle = data.url.boxStyle;
						temp = {
							item: this.pageRowDraftListMB,
							type: "MB-list",
						};
						window.parent.postMessage(temp, "*");
					}
					break;
				case "SET_CONTENTSTYLE":
					this.hoverItem.sampleData.contentStyle = data.url
					temp = {
						item: this.pageRowDraftListMB,
						type: "MB-list",
					};
					window.parent.postMessage(temp, "*");
				break;
				case "SET_NAVLISTVALUE":
					this.clickData.sampleData.outer = data.url;
					temp = {
						item: this.pageRowDraftListMB,
						type: "MB-list",
					};
					window.parent.postMessage(temp, "*");
					break;
				case "SET_IMGLISTVALUE":
					// 有可能是outer或者list，利用对象索引遍历过去
					if (data.url && data.url.list && Array.isArray(this.clickData.sampleData.type.customData)) {
						this.clickData.sampleData.type.customData.forEach((item, index) => {
							item.value = data.url.list[index].value
						})
						temp = {
							item: this.pageRowDraftListMB,
							type: "MB-list",
						};
						window.parent.postMessage(temp, "*");
					}
					break;
			}
		},
		async changeTranslateStatus (val, ind) {
			this.pageRowDraftListMB[ind].isTranslate = val.isTranslate ? 0 : 1;
			window.parent.postMessage({
				item: this.pageRowDraftListMB,
				flag:this.pageRowDraftListMB[ind],
				type: "MB-Translate",
			}, "*");
		}
	},
	computed: {
		currentLan () {
			let flag = window.parent.parent.location.search.substr(-5) || null
			return flag
		}
	},
	mounted () {
		window.addEventListener("message", this.messageEventFun)
	},
};
</script>

<style lang="scss" scoped>
.iframeMB {
	font-size: 12px;

	::v-deep .modal-box h2 {
		font-size: 22px;
	}

	::v-deep .register,
	::v-deep .login,
	::v-deep a {
		pointer-events: none;
	}

	::v-deep .tools {
		position: relative;

		.transFlag {
			position: absolute;
			margin: 6px;
			font-size: 16px;
			z-index: 989;
			background-color: #ccc;
			color: #fff;
			border-radius: 4px;
			transition: 0.3s ease;

			&:hover {
				transform: scale(1.2);
			}
		}
	}
}


// 发布到线上css不生效，新添加tools样式
::v-deep .myTools {

	.btn-before,
	.btn-after {
		opacity: 0;
		cursor: pointer;
		transition: all 0.3s;
		transform: translateX(-50%);
		position: absolute;
		z-index: 0;
		left: 50%;

		.v-btn {
			width: 3em;
			height: 3em;
		}

		&:hover {
			>.v-btn {
				width: 210px;
				border-radius: 15px;
			}

			.font {
				display: inline;
				transform: scale(1);
			}
		}
	}

	.btn-before {
		top: -1.35em;
	}

	.btn-after {
		bottom: -1.35em;
	}

	.edit-zone {
		opacity: 0;
		display: flex;
		align-items: center;
		transition: all 0.3s;
		padding: 0.55em 0.15em 0.45em;
		background-color: #1a73e8;
		position: absolute;
		right: -1px;
		top: 0;

		button {
			width: auto;
			height: auto;
			padding: 0 0.2em;
		}

		.icon-gengduo {
			color: white;
			font-size: 1.8em;
		}

		.icon-jxsht-3d-tj {
			padding: 0 0.2em;
		}
	}

	.v-btn {
		display: flex;
		min-width: auto;
		border-radius: 50%;
		transition: all 0.3s;

		.font {
			display: none;
			transform: scale(0);
			transition: all 0.3s;
		}
	}
}

::v-deep .hover-type .myTools {

	.edit-zone,
	.btn-before,
	.btn-after {
		opacity: 1;
		z-index: 8;
	}
}
</style>
