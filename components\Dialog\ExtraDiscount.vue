<template>
	<v-dialog
		:value="addExtraDiscountDialog"
		@input="disDialogInputChange"
		width="400"
	>
		<v-card class="addExtraDiscountDialogCard">
			<div style="font-size: 18px;" class="mb-3">
				Add Discount
			</div>
			<div class="d-flex align-center mb-2">
				<span style="flex-shrink: 0">Discount Value:</span>
				<div class="mx-2" style="width: 150px">
					<v-combobox
						height="32"
						class="autoSelect1 rounded-0"
						background-color="#F0F0F0"
						v-model="discount"
						:items="discountList"
						label="discount"
						hide-details
						dense
						solo
						flat
						outlined
					></v-combobox>
				</div>
				<span style="flex-shrink: 0">% off</span>
			</div>
			<div>
				<v-checkbox
					v-model="updateUserInfo"
					hide-details
					dense
				>
					<template v-slot:label>
						<span style="font-size:14px;color: #666666">Is this discount always reserved for this customer? You can also go to the  member center to modify.</span>
					</template>
				</v-checkbox>
			</div>
			<v-divider class="my-2"></v-divider>
			<div class="mb-2">
				<div>Notes:</div>
				<div class="mb-2" style="color:#666666">Discount reason (optional, customers will not see this)
				</div>
				<div>
					<v-textarea
						class="body-2"
						v-model="reason"
						rows="3"
						hide-details
						outlined
					>
						<template v-slot:prepend-inner>
							<v-icon size="14">mdi-pencil</v-icon>
						</template>
					</v-textarea>
				</div>
			</div>
			<div class="px-2">
				<div class="d-flex align-center justify-space-between">
					<div class="label">
						Cost of Production:
					</div>
					<div class="con" style="color: #EA4335">
						${{ selectedItem.costPrice | formatText }}
					</div>
				</div>
				<div class="d-flex align-center justify-space-between">
					<div class="label">
						Customer's Original Price:
					</div>
					<div class="con">
						$<span>{{ selectedItem.productPrice | formatText }}</span>
					</div>
				</div>
				<div class="d-flex align-center justify-space-between">
					<div class="label">Your Profit Amount:</div>
					<div class="con" style="color: #EA4335">
						$<span>{{ profitPrice | formatText }}</span>
					</div>
				</div>
				<div class="d-flex align-center justify-space-between">
					<div class="label">
						Discount:
					</div>
					<div class="con">
						-{{ discount }}% Off
					</div>
				</div>
				<div class="d-flex align-center justify-space-between">
					<div class="label">
						Total After Discount:
					</div>
					<div class="con">
						$<span>{{ totalPrice | formatText }}</span>
					</div>
				</div>
			</div>
			<div class="d-flex justify-center mt-3">
				<v-btn color="primary" depressed class="mr-2" @click="confirmDiscount">Apply</v-btn>
				<v-btn depressed @click="disDialogInputChange">Cancel</v-btn>
			</div>
		</v-card>
	</v-dialog>
</template>

<script>
export default {
	props: ['addExtraDiscountDialog', 'selectedItem'],
	data() {
		return {
			reason: '',
			discount: '',
			updateUserInfo: false,
			discountList: [5, 10, 15, 20],
		}
	},
	computed: {
		totalPrice() {
			let selectedItem = this.selectedItem
			if (!selectedItem) {
				return false;
			}
			let price = (selectedItem.productPrice * (1 - this.discount / 100)).toFixed(2);
			return price > 0 ? price : 0
		},
		profitPrice() {
			let selectedItem = this.selectedItem
			if (!selectedItem) {
				return false;
			}
			return (this.totalPrice - selectedItem.costPrice).toFixed(2);
		}
	},
	watch:{
		selectedItem(newVal){
			this.discount = newVal.orderDiscount * 100
		}
	},
	methods: {
		disDialogInputChange() {
			this.reason = '';
			this.discount = '';
			this.updateUserInfo = false;
			this.$emit('update:addExtraDiscountDialog',false);
			this.$emit('clearSelectItem')
		},
		confirmDiscount() {
			this.$emit('confirmDiscount',{
				proId: this.$store.getters["manage/getProId"],
				orderId: this.selectedItem.id,
				discount: this.discount / 100,
				reason: this.reason,
				updateUserInfo: this.updateUserInfo
			})
			this.disDialogInputChange()
		}
	}
}
</script>

<style scoped lang="scss">
.addExtraDiscountDialogCard {
	padding: 20px;
	font-size: 14px;

	.label {
		flex-shrink: 0;
		width: 160px;
	}

	.con {
		min-width: 90px;
		text-align: left;
	}
}

</style>
