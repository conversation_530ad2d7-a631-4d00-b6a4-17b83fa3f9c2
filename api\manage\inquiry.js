import {request} from '~/utils/request'

//获取所有询盘
export function getAllInquiryList(data) {
	return request({
		url: '/retailer/inquiry/getAllInquiryList',
		method: 'post',
		data,
	})
}

//删除询盘
export function delById(data) {
	return request({
		url: '/retailer/inquiry/delById',
		method: 'post',
		data,
	})
}

export function getInquiryRead(data) {
	return request({
		url: '/retailer/inquiry/getInquiryRead',
		method: 'post',
		data:data,
	})
}

//获取询盘详情
export function getById(data) {
	return request({
		url: '/retailer/inquiry/getById',
		method: 'post',
		data,
	})
}

export function changeInquiryStatus(data) {
	return request({
		url: '/retailer/inquiry/changeInquiryStatus',
		method: 'post',
		data,
	})
}

//修改询盘
export function editInquiry(data) {
	return request({
		url: '/retailer/inquiry/editInquiry',
		method: 'post',
		data,
	})
}


//编辑用户信息
export function editUser(data) {
	return request({
		url: '/retailer/member/editUser',
		method: 'post',
		data,
	})
}

//获取信息列表
export function getMessageListByInquiryId(data) {
	return request({
		url: '/retailer/inquiry/getMessageListByInquiryId',
		method: 'post',
		data,
	})
}

//获取业务员列表
export function getAllSalesMan(data) {
	return request({
		url: '/retailer/allotConf/getAllSalesMan',
		method: 'post',
		data,
	})
}

//获取链接信息
export function getUrlById(data) {
	return request({
		url: '/retailer/inquiry/getUrlById',
		method: 'post',
		data,
	})
}

//创建3D建模订单
export function create(data) {
	return request({
		url: '/retailer/modeling/order/create',
		method: 'post',
		data,
	})
}

//获取图稿链接信息
export function getLinkProductByInquiryId(data) {
	return request({
		url: '/retailer/inquiry/getLinkProductByInquiryId',
		method: 'post',
		data,
	})
}

//获取图稿链接信息
export function getOrderInfo(data) {
	return request({
		url: '/retailer/order/getOrderInfo',
		method: 'post',
		data,
	})
}









