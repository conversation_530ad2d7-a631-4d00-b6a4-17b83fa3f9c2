<template>
	<v-app style="background-color:none" :class="modal.class">
		<div class="modal-box" v-for="(o, oi) in modal.outer" :style="{ ...modal.style, ...o.style }" :key="oi">

			<EditDiv :tagName="o.title.tagName || 'h2'" :style="modal.titleStyle" v-model:content="o.title.value"
				@click="setModalType(o.title, modal.outer, 'text')" v-if="o.title?.value" />

			<div class="sub-title" :style="modal.subTitleStyle" :hidden="!o.title && !o.subTitle"
				:pointer="o.subTitle?.event" @click="setModalType(o.subTitle, modal.outer, 'text', o.subTitle.event)">
				<EditDiv v-model:content="o.subTitle.value" v-if="o.subTitle?.value" />
			</div>

			<PicVideo class="pic" v-if="o.img" :modal="modal" :data="o" data-source="outer"
				:pic-style="{ ...modal.imgStyle, ...o.img.style }" @setModalType="setModalType" />


			<form ref="form" :style="modal.boxStyle" v-if="!oi">
				<div v-for="l in modal.list" :key="l.key" :style="{ width: l.width, ...modal.cardStyle, ...l.style }">
					<label @click="setModalType(l.title, modal.list, 'text')" :style="modal.cardTitleStyle">
						<EditDiv tagName="label" v-model:content="l.title.value" v-if="l.title?.value" />
						<span v-show="l.required" red>*</span>
					</label>

					<v-combobox v-if="l.key.toLowerCase().includes('country')" v-model="form[l.key]"
						:items="countryList" return-object item-text="countryName" :placeholder="l.placeholder?.value"
						:style="modal.inputStyle"></v-combobox>
					<v-combobox v-else-if="l.key.toLowerCase().includes('state')" v-model="form[l.key]"
						:items="stateList" return-object item-text="stateName" :placeholder="l.placeholder?.value"
						:style="modal.inputStyle"></v-combobox>

					<textarea v-else-if="l.key.toLowerCase().includes('message')" v-model="form[l.key]"
						:rows="l.rows || 10" :style="{ ...modal.inputStyle, ...l.inputStyle }"
						:placeholder="l.placeholder?.value"></textarea>

					<input v-else v-model="form[l.key]" :placeholder="l.placeholder?.value" :style="modal.inputStyle" />

					<label class="err-tip" v-if="l.err">{{ l.err }}</label>
				</div>
			</form>


			<EditDiv v-if="o.text" :style="modal.textStyle" v-model:content="o.text.value"
				@click="setModalType(o.text, modal.outer, 'text')"
				:hidden="modal.api == 'addSampleOrder' && !sampleTip">
			</EditDiv>

			<button v-if="o.button" primary :style="{ ...modal.btnStyle, ...o.button.style }" :title="o.button.alt"
				@click="onTap(o.button)">
				<EditDiv v-show="o.button.value" tagName="label" v-model:content="o.button.value" />
				<b :class="o.button.icon" v-show="o.button.icon"></b>
			</button>
		</div>



		<div flex class="mask" v-show="sampleSuccess" @click.self="sampleSuccess = false">
			<div class="sample-success-tip">
				<pic src="https://sticker-static.oss-accelerate.aliyuncs.com/image/uploads/20231101/icon.png"
					:alt="lang.success" />
				<strong>{{ lang.success }}</strong>
				<div>{{ lang.successTip }}</div>
			</div>
			<b pointer class="icon-hxsht-xp-gb" @click="sampleSuccess = false"></b>
		</div>

		<div flex class="mask" :class="modal.api" v-show="successTip" @click.self="successTip = false">
			<div v-for="(o, oi) in modal.outer" :key="oi + 100" v-if="oi == modal.outer.length - 1">
				<pic v-if="o.img" :style="o.img.style" :src="o.img.value" :alt="o.img.alt" />
				<strong v-if="o.title" :style="o.title.style">{{ o.title?.value }}</strong>
				<div v-if="o.subTitle" :style="o.subTitle.style">{{ o.subTitle?.value }}</div>

				<button primary v-if="o.button" :style="o.button.style" :title="o.button.alt"
					@click="setModalType(o.button, modal.outer, 'button', o.button)">
					<EditDiv v-show="o.button.value" tagName="label" v-model:content="o.button.value" />
					<b :class="o.button.icon" v-show="o.button.icon"></b>
				</button>
			</div>
		</div>
	</v-app>
</template>

<script>
import * as apiData from "@/api/web.js";

export default {
	name: "modalForm",
	props: {
		data: {
			type: Object,
			default: {},
		}
	},
	data() {
		return {
			modal: {
				style: {},
				type: {},
				...this.data
			},
			form: {},
			countryList: [],
			stateList: [],
			sampleTip: false,
			sampleSuccess: false,
			successTip: false,
		}
	},
	computed: {
		lang() {
			return this.$store.getters.lang?.contactUs
		}
	},
	watch: {
		modal: {
			handler(val) {
				if (process.env.isManage) this.$emit("update:data", val);
			},
			immediate: true,
			deep: true
		},
		'form.country': {
			handler(val) {
				if (val) apiData.getStateList({ id: val.id }).then(({ data }) => {
					this.stateList = data;
					if (this.form.state && !data.find(i => i.id == this.form.state.id)) this.form.state = undefined;
				});
			}
		}
	},
	created() {
		if (this.modal.list.find(i => i.key == 'country')) apiData.getCountryList().then(({ data }) => this.countryList = data);
		if (this.modal.api == 'addSampleOrder') this.sampleTip = this.$store.state.userInfo.hasSampleOrder;
	},
	methods: {
		setModalType(target, targetArray, clickType, event, other) {
			this.$setModal(this, target, targetArray, clickType, event, other)
		},
		async onTap(button) {
			if (button.url || process.env.isManage) return this.setModalType(button, this.modal.outer, 'button', button);

			// 表单校验
			let errList = this.modal.list.map(i => {
				i.err = '';
				if ((!this.form[i.key] || JSON.stringify(this.form[i.key]) == "{}") && i.required) i.err = i.title?.value?.replace(':', '') + this.lang.required;
				else if (i.key.toLowerCase().includes('email')) {
					let re = /^[A-Za-z0-9-_\u4e00-\u9fa5]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/;
					if (!re.exec(this.form[i.key])) i.err = this.lang.requiredEmail;
				} else if (this.modal.api == 'influencersProgram' && i.key == 'websiteLink' && !this.form.instagram && !this.form.tiktok && !this.form.youtube && !this.form.discord && !this.form.facebook && !this.form.twitter && !this.form.pinsterest && !this.form.LinkedIn && !this.form.reddit && !this.form.websiteLink) i.err = i.error.value;

				if (i.err) return i.err;
			}).filter(i => i);

			this.$forceUpdate();
			if (errList.length) return this.$nextTick(() => document.querySelector('.err-tip')?.scrollIntoView({ block: "center", behavior: "smooth" }));


			// 接口字段矫正
			if (this.modal.api == 'addSampleOrder') {
				this.form.currency = this.$store.state.currency.code;
				this.form.shippingCountry = this.form.country?.countryName;
				this.form.shippingCountryId = this.form.country?.id;
				this.form.shippingCountryCode = this.form.country?.code;
				this.form.shippingState = this.form.state?.stateName;
				this.form.shippingStateId = this.form.state?.id;
			} else if (this.modal.api == 'influencersProgram') {
				this.form.country = this.form.country?.id;
				this.form.topCountry = this.form.topCountry?.id;
			}

			// 判断用户是否下过样品单
			if (this.modal.api == 'addSampleOrder') {
				if (!this.$store.state.userInfo.hasSampleOrder) await apiData.isLogin().then(({ data }) => this.$store.commit('setUserInfo', data));
				if (this.$store.state.userInfo.hasSampleOrder) return this.sampleTip = true;
			}

			// 提交
			apiData[this.modal.api](this.form).then(res => {
				this.form = {};
				if (this.modal.api == 'addSampleOrder') this.sampleSuccess = true;
				else if (this.modal.successTip) this.successTip = true;
				else this.$toast.success(res.message);
			})
		}
	}
}
</script>

<style lang="scss" scoped>
.modal-box {
	width: 100%;

	form {
		display: flex;
		flex-wrap: wrap;
		padding: 0 7.5vw;
		grid-row-gap: 1.5em;
		justify-content: space-between;

		>div {
			width: 48.8%;

			>*:not(label) {
				width: 100%;
				padding: 0 0.6em;
				margin-top: 0.6em;
				border-radius: 0.5em;
				background: #FCFCFC;
				height: calc(1.7em + 1vmax);
				border: 1px solid #D3D4D9;
			}

			textarea {
				padding: 0.6em;
				height: auto !important;
			}

			::v-deep .v-input {
				font-size: inherit;

				.v-input__control {
					min-height: calc(1.7em + 1vmax);
				}

				.v-input__slot::before,
				.v-input__slot::after {
					border: none;
				}
			}
		}
	}

	button {
		display: flex;
		margin: 3em auto 0.5em;
		font-size: calc(1em + 2px);
	}

	.err-tip {
		color: #FF8A00;
		font-size: 0.75em;
	}
}



.mask {
	height: 100vh;
	max-height: 1200px;
	text-align: center;
	align-items: center;
	flex-direction: column;
	justify-content: center;
	background: rgba($color: black, $alpha: 0.5);
	position: fixed;
	z-index: 9;
	right: 0;
	left: 0;
	top: 0;

	>div {
		min-width: 50vw;
		max-width: 90vw;
		background: white;
		border-radius: 10px;
		padding: 4em 3vw 5em;

		img {
			height: 6em;
			object-fit: contain
		}
	}

	.sample-success-tip {
		background: linear-gradient(#F3FFF1, white 50%);

		strong {
			line-height: 2em;
			font-size: 2.63em;
		}

		div {
			font-size: 1.75em;
		}
	}

	>.icon-hxsht-xp-gb {
		color: white;
		width: 2.8em;
		margin-top: 2.5em;
		border: 2px solid;
		line-height: 2.4em;
		border-radius: 50%;
	}
}

.laylards-contact {
	form {
		.err-tip {
			color: #eb5757;
		}
	}
}

.ring-contact {

	input::placeholder,
	textarea::placeholder {
		color: #B4B0A8 !important;
		font-weight: normal !important;
	}
}

[theme='10'] {
	label {
		font-weight: bold;
	}

	form>div>*:not(label) {
		background: $bg-primary;
		border: 1px solid $bg-dark;
	}
}


@media screen and (max-width: $mb-width) {
	.modal-box {
		form {
			padding: 1.5vw;
		}

		button {
			font-size: 1em;
		}
	}

	.laylards-contact {
		form {
			.err-tip {
				font-size: .85em;
				max-width: 29%;
			}
		}
	}
}
</style>
