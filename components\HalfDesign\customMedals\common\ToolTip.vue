<template>
  <span class="tip" @click.stop v-if="titleContent || itemData.remark || itemData.imgDetail">
    <v-tooltip :top="area === 'top'" :bottom="area === 'bottom'" :left="area === 'left'" :right="area === 'right'" max-width="300" :color="color" :content-class="tooltipContent">
      <template v-slot:activator="{ on, attrs }">
        <b v-bind="attrs" v-on="on" v-if="selfIcon" :class="selfIcon"></b>
        <v-icon v-bind="attrs" v-on="on" size="18px" v-else> mdi-help-circle-outline </v-icon>
      </template>
      <div class="text-center" style="display: flex; align-items: start">
        <div v-if="itemData?.imgDetail" style="display: flex;align-items: center;justify-content: center;max-width: 150px; max-height: 150px; margin:10px -10px">
          <img :src="itemData.imgDetail" :alt="itemData.valueName" :title="itemData.valueName" style="object-fit: contain;" />
        </div>
        <div v-else style="text-align: left; font-size: 13px; width: fit-content; max-width: 250px; word-break: break-word;" :style="{ color: textColor }">
          {{ titleContent || itemData.remark }}</div>
      </div>
    </v-tooltip>
  </span>
</template>

<script>
export default {
  props: {
    titleContent: {
      type: String,
      default: ''
    },
    itemData: {
      type: Object,
      default: () => {
        return {}
      }
    },
    color: {
      type: String,
      default: '#fff'
    },
    textColor: {
      type: String,
      default: '#333'
    },
    area: {
      type: String,
      default: 'top'
    },
    selfIcon: {
      type: String,
      default: ''
    },
    contentClass: {
      type: String,
      default: ''
    }
  },
  computed: {
    tooltipContent() {
      if (this.contentClass.length > 0) {
        return this.contentClass + ' custom-Vtooltip'
      } else {
        return 'custom-Vtooltip'
      }
    }
  },
  data() {
    return {}
  }
}
</script>

<style>
.custom-Vtooltip {
  z-index: 1000000 !important;
}
.custom-Vtooltip.v-tooltip__content {
  border: 1px solid #ccc !important;
}
</style>
<style lang="scss" scoped>
.tip {
  position: relative;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 4px;
}
</style>
