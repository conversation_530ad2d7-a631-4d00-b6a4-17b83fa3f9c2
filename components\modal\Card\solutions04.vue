<template>
	<!-- 模板外层的盒子上写theme属性，用来分别写每一套的样式，详见下方style -->
	<div class="banner-box" :class="modal.wrapClass" :style="modal.style">
		<div style="height: 100%;" v-for="(l, li) in modal.list" :key="li">
			<div class="imgWrap mb-none" @click="setModalType(l.banner,modal.list,'banner')" v-if="l.banner && l.banner.value">
				<pic :src="l.banner.value" :alt="l.banner.alt" :title="l.banner.alt"/>
			</div>
			<div class="bps-containera mb-none">
				<div v-for="(o,oi) in modal.outer.slice(0,4)" :key="oi">
					<div class="imgWrap" >
						<pic class="pic" :src="o.img.value" :alt="o.img.alt" :title="o.img.alt" v-if="o.img" @click="setModalType(o.img,modal.list,'img')"/>
						<EditDiv tag-name="h3" v-model:content="o.title.value" v-if="o.title"
										 @click="setModalType(o.title,modal.list,'text')" />
					</div>
				</div>
			</div>
			<EditDiv class="pc-none mb-block" tag-name="h2" v-model:content="modal.title.value" v-if="modal.title"
							 @click="setModalType(modal.title,modal.list,'title')" style="margin-bottom: 26px" />
			<div class="pc-none mb-block bps-containerc">
				<div class="bps-containera">
					<div v-for="(o,oi) in modal.outer" :key="oi">
						<div class="imgWrap" >
							<pic class="pic" :src="o.img.value" :alt="o.img.alt" :title="o.img.alt" v-if="o.img" @click="setModalType(o.img,'img')"/>
							<EditDiv tag-name="h3" v-model:content="o.title.value" v-if="o.title"
											 @click="setModalType(o.title,modal.list,'text')" />
						</div>
					</div>
				</div>
				<EditDiv class="pc-none mb-block des solutions-part4" v-model:content="modal.subTitle.value" v-if="modal.subTitle"
								 @click="setModalType(modal.subTitle,modal.list,'text')" />
				<div class="btnWrap pc-none mb-block" @click="setModalType(modal.button,modal.list,'button')" v-if="modal.button">
					<a :href="modal.button.url" :title="modal.button.alt" :target="modal.button.target || '_self'"
						 class="default-button bps-button">
						{{ modal.button.value }}
						<b class="icon-bps-sanjiao"></b>
					</a>
				</div>
			</div>

			<div class="bps-containerb mb-none">
				<div v-for="(o,oi) in modal.outerTwo" :key="oi">
					<div class="imgWrap" >
						<pic class="pic" :src="o.img.value" :alt="o.img.alt" :title="o.img.alt" v-if="o.img" @click="setModalType(o.img,modal.list,'img')"/>
						<EditDiv tag-name="h2" v-model:content="o.H2title.value" v-if="o.H2title"
										 @click="setModalType(modal.outerTwo[oi].H2title,modal.list,'text')" />
						<EditDiv class="des" v-model:content="o.subTitle.value" v-if="o.subTitle"
										 @click="setModalType(modal.outerTwo[oi].subTitle,modal.list,'text')" />
						<EditDiv tag-name="h3" v-model:content="o.title.value" v-if="o.title"
										 @click="setModalType(modal.outerTwo[oi].title,modal.list,'text')" />
						<div class="btnWrap" @click="setModalType(modal.button,modal.list,'button')"  v-if="o.button">
							<a :href="o.button.url" :title="o.button.alt" :target="o.button.target || '_self'"
								 class="default-button bps-button">
								{{ o.button.value }}
								<b class="icon-bps-sanjiao"></b>
							</a>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	name: "Banner",
	props: {
		preview: {
			type: Boolean,
			default: false,
		},
		data: {
			type: Object,
			default: {},
		}
	},
	data() {
		return {
			modal: {
				style: {},
				type: {index: 0, clickPosition: 'outer'},
				...this.data
			},
		};
	},
	watch: {
		modal: {
			handler(val) {
				if (process.env.isManage) this.$emit("update:data", val);
			},
			immediate: true,
			deep: true
		}
	},
	methods: {
		setModalType(target, targetArray, clickType, event, other) {
			this.$setModal(this, target, targetArray, clickType, event, other)
		},
	}
};
</script>

<style lang="scss" scoped>
.pc-none {
	display: none !important;
}
.imgWrap {
	height: 100%;
	position: relative;

	img, video {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}
}

.btnWrap {
	display: inline-block;
	color: #ffffff;
	margin-top: 30px;
}

.solutions-part6 {
	position: relative;
	margin-top: 100px;
	.imgWrap {
		height: 100%;

		img {
			width: 100%;
			height: 100%;
			object-fit: cover;
		}
	}
	.bps-containerb {
		grid-template-columns: repeat(3,1fr) !important;
		grid-column-gap: 17px;
		bottom: 0 !important;
		top: auto !important;
		padding: 0 161px 0 261px;
		div {
			position: relative;
			&:nth-child(2) {
				.imgWrap {
					margin-left: 35px;
					margin-top: 30px;
					position: inherit !important;
					bottom: 15% !important;
				}
			}
			.imgWrap {
				position: absolute;
				bottom: 0;
				h2 {
					font-size: 1.2vw;
					font-family: Buenos Aires;
					font-weight: bold;
					color: #333333;
				}
				.des {
					font-size: 16px;
					font-family: Buenos Aires;
					font-weight: 400;
					color: #666666;
				}
				height: auto !important;
				img {
					height: auto !important;
				}
			}

		}
	}
	.bps-containera,.bps-containerb {
		display: grid;
		grid-template-columns: repeat(4,1fr);
		grid-column-gap: 17px;
		grid-row-gap: 33px;
		position: absolute;
		left: 0;
		top: 0;
		color: #ffffff;
		div {
			&:nth-child(5) {
				margin-left: 261px;
			}
		}
		.imgWrap {
			position: relative;
			h3 {
				font-size: 18px;
				font-family: Buenos Aires;
				font-weight: 400;
				color: #FFFFFF;
				position: absolute;
				width: 100%;
				text-align: center;
				top: 88%;
			}
		}
		h1 {
			width: 696px;
			font-size: 48px;
			font-weight: bold;
			color: #ffffff;
			text-shadow: 0px 9px 24px rgba(122, 99, 58, 0.35);
		}

		.des {
			font-size: 30px;
			margin-top: 10px;
			font-weight: 400;
			color: #ffffff;
			line-height: 30px;
			text-shadow: 0px 9px 24px rgba(122, 99, 58, 0.35);
			&:nth-child(2) {
				color: #C19952;
			}
		}
	}
}

.termsBanner {
	position: relative;
	max-width: 1170px;
	height: 99px;
	margin: 40px auto;

	.bps-container {
		position: absolute;
		left: 50%;
		top: 50%;
		transform: translate(-50%, -50%);

		h1 {
			font-size: 36px;
		}
	}

	.imgWrap {
		width: 100%;
		height: 100%;

		img {
			object-fit: cover;
			width: 100%;
			height: 100%;
		}
	}
}

@media screen and (min-width: $mb-width) and (max-width: $pad-width) {
	.solutions-part6 {
		position: relative;

		.bps-container {
			h1 {
				width: 522px;
				font-size: 36px;
			}

			.des {
				width: 415px;
				font-size: 16px;
				line-height: 24px;
				margin: 18px 0 28px;
			}
		}
	}
}

@media screen and (max-width: $mb-width) {
	.mb-block {
		display: grid !important;
	}
	.mb-none {
		display: none !important;
	}
	.bps-containera {
		grid-column-gap: 14px !important;
		grid-row-gap: 21px !important;
		grid-template-columns: repeat(2,1fr) !important;
		position: inherit !important;
		div {
			&:nth-child(5) {
				margin-left: 0 !important;
			}
		}
	}
	.solutions-part4 {
		margin-top: 25px;
		font-size: 12px;
		font-family: Buenos Aires;
		font-weight: 400;
		color: #333333;
		line-height: 18px;
	}
	.solutions-part6 {
		margin-top: 0 !important;
		.bps-containerc {
			.btnWrap {
				width: 30%;
				transform: none;
			}
		}
		height: auto;
		padding: 10px 17px;

		.imgWrap {
			overflow: hidden;
			position: relative;
			height: 225px;
			border-radius: 10px;
			img{
				object-position: right top;
			}
			h3 {
				top: auto !important;
				bottom: 5px;
				line-height: 1;
			}
		}

		.bps-container {
			position: absolute;
			left: 50%;
			top: 45%;
			width: 85%;
			transform: translate(-50%, -50%);
			padding: 0;

			h1 {
				width: 100%;
				font-size: 21px;
				text-shadow: none;
				color: #333333;
				margin-top: 30px;
			}

			.des {
				width: auto;
				font-size: 15px;
				font-weight: bold;
				text-align: center;
				color: #333333;
				line-height: 18px;
				text-shadow: none;
				margin: auto;
				&:nth-child(2) {
					width: 80%;
					margin: 15px auto;
				}
			}
		}
		.btnWrap {
			left: 50%;
			transform: translateX(-50%);
			top: 100%;
		}
	}
	.termsBanner {
		max-width: 100%;
		margin: 10px 10px 24px;
		height: 50px;

		.bps-container {
			h1 {
				font-size: 21px;
			}
		}
	}
}
</style>
