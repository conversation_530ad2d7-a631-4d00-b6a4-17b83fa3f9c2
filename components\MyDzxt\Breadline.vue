<template>
	<div>
		<div class="modal-box menu" hidden @click.self="showMenu = !showMenu">
			<div flex center class="system-box">
				<div class="ccy-box options-box">
					<b class="icon-bps-guojia"></b> {{ $store.state.currency.code }}({{ $store.state.currency.symbol }})
					<b class="icon-a-icon-arrow2zhuanhuan arrow"></b>

					<div class="options" hidden>
						<label v-for="i in $store.state.currencyList" :class="{ select: i.id == $store.state.currency.id }"
							   @click="$store.commit('setCurrency', i)">{{ i.code }}
						</label>
					</div>
				</div>

				<div class="lang-box options-box">
					<pic class="lang-img" :src="$store.state.language.img" :alt="$store.state.language.countryCode" />
					{{ $store.state.language.language }}
					<Language class="options"></Language>
				</div>
			</div>


			<div class="user-box" v-if="$store.state.token">
				<pic class="avatar" :src="$store.state.userInfo.picPath || '/img/avatar.png'" />
				<div>{{ lang.welcome }} {{ $store.state.userInfo.firstName }} {{ $store.state.userInfo.lastName }}</div>
				<button primary @click="logOut">{{ lang.logOut }}</button>
			</div>
			<div class="user-box" v-else>
				<pic class="avatar" src="/img/avatar.png" :alt="lang.userCenter" :title="lang.userCenter" />
				<button primary @click="$store.commit('setLogin', 'login')">
					{{ lang.signOrJoin }}
				</button>
			</div>


			<div class="link-box" v-for="nav in navList" :title="nav.name" :key="nav.id"
				 :class="{ select: pagePath == nav.linkUrl, hot: nav.isHot }">
				<label @click.stop="changeNav(nav)">{{ nav.name }}
					<b class="icon-a-icon-arrow2zhuanhuan arrow" :class="{ rotate: !nav.isEnable }" v-show="nav.children.length"></b>
				</label>

				<div class="link" v-if="nav.isHot" v-show="!nav.isEnable">
					<modalCustom :data="{ api: 'getHotProductList', pageSize: 5, column: 1, hot: true ,categoryId: nav.categoryId||null}"></modalCustom>
					<div class="link-box" v-for="pro in nav.children" :title="pro.name" :key="pro.id"
						 :class="{ select: pagePath == pro.linkUrl }">
						<label @click.stop="changeNav(pro)">{{ pro.name }}
							<span :style="{ background: pro.labelColor }">{{ pro.labelText }}</span>
						</label>
					</div>
				</div>

				<div class="link" v-else-if="nav.children.length" v-show="!nav.isEnable">
					<div class="link-box" v-for="pro in nav.children" :title="pro.name" :key="pro.id"
						 :class="{ select: pagePath == pro.linkUrl }">
						<label @click.stop="changeNav(pro)">{{ pro.name }}
							<span :style="{ background: pro.labelColor }">{{ pro.labelText }}</span>
							<b :class="!pro.isEnable ? 'icon-jianshao' : 'icon-jxsht-3d-tj'"
							   v-show="pro.children.length"></b>
						</label>

						<div class="link" v-if="pro.children.length" v-show="!pro.isEnable"
							 :class="{ select: pagePath == pro.linkUrl }">
							<div class="link-box" v-for="item in pro.children" :title="item.name" :key="item.id"
								 :class="{ select: pagePath == item.linkUrl }">
								<label @click.stop="changeNav(item)">{{ item.name }}
									<span :style="{ background: item.labelColor }">{{ item.labelText }}</span>
									<b :class="!item.isEnable ? 'icon-jianshao' : 'icon-jxsht-3d-tj'"
									   v-show="item.children.length"></b>
								</label>

								<div class="link" v-if="item.children.length" v-show="!item.isEnable"
									 :class="{ select: pagePath == pro.linkUrl }">
									<div class="link-box" v-for="i in item.children" :title="i.name" :key="i.id"
										 :class="{ select: pagePath == i.linkUrl }">
										<label @click.stop="changeNav(i)">{{ i.name }}
											<span :style="{ background: i.labelColor }">{{ i.labelText }}</span>
											<b :class="!i.isEnable ? 'icon-jianshao' : 'icon-jxsht-3d-tj'"
											   v-show="i.children.length"></b>
										</label>

										<div class="link" v-if="i.children.length" v-show="!i.isEnable"
											 :class="{ select: pagePath == pro.linkUrl }">
											<div class="link-box" v-for="r in i.children" :title="r.name" :key="r.id"
												 :class="{ select: pagePath == r.linkUrl }">
												<label @click.stop="changeNav(r)">{{ r.name }}
													<span :style="{ background: r.labelColor }">{{ r.labelText }}</span>
												</label>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import {getNavList} from "@/api/web.js";

export default {
	name: "modalHeader",
	props: {
		data: {
			type: Object,
			default: {},
		},
	},
	data() {
		return {
			theme: this.data.theme,
			modal: {
				type: {},
				outer: [],
				...this.data,
			},
			navList: [],
			showMenu: false,
			showProduct: true,
			showLang: false
		};
	},
	computed: {
		lang() {
			return this.$store.getters.lang.layout || {};
		},
		pagePath() {
			return this.$store.state.pagePath;
		},
		proSystem() {
			return this.$store.state.proSystem;
		}
	},
	watch: {
		modal: {
			handler(val) {
				this.$emit("update:data", val);
			},
			immediate: true,
			deep: true,
		},
	},
	created() {
		getNavList(process.env.isManage ? {proId: this.$store.getters["manage/getProId"]} : null).then(({data}) => this.navList = data)

		if (process.browser) {
			// 往下滚动一屏后出现回到顶部按钮
			document.addEventListener("scroll", () => {
				if (document.querySelector(".to-top")) document.querySelector(".to-top").hidden = window.scrollY < window.innerHeight;
			});

			// 点击页面关闭语言框
			document.addEventListener("click", ({target}) => {
				if (!target.className.includes('lang-box') && !target.parentElement.className.includes('lang-box')) return (this.showLang = false)
			});
		}
	},
	methods: {
		changeNav(nav) {
			if (!nav.isHot && nav.linkUrl && nav.linkUrl != this.pagePath) {
				this.showMenu = false;
				this.$router.push({path: nav.linkUrl});
			} else nav.isEnable = !nav.isEnable;
		},
		logOut() {
			this.$toast.show(this.lang.confirmLogOut, {
				containerClass: "toast-action-box",
				className: "toast-action",
				duration: null, // 不自动关闭弹窗
				action: [
					{
						text: this.lang.sure,
						onClick: (e, toastObject) => {
							this.$store.commit("logOut");
							toastObject.goAway(0);
						},
					},
					{
						text: this.lang.cancel,
						onClick: (e, toastObject) => toastObject.goAway(0), // 关闭弹窗
					},
				],
			});
		},
		scrollTop() {
			document.body.scrollIntoView({behavior: "smooth"});
		}
	},
};
</script>

<style lang="scss" scoped>
.menu {
	width: 100vw;
	height: 100vh;
	display: contents;
	overflow-y: auto;
	padding: 0 20% 0 0 !important;
	background: linear-gradient(to right, white 80%, $bg-shadow 80%, $bg-shadow 82%, transparent 88%) !important;
	position: fixed;
	z-index: 990;
	left: 0;
	top: 0;

	.system-box {
		justify-content: space-between;
		border-bottom: 1px solid #ebebeb;

		>div {
			padding: 1.5em 2em;
		}

		.ccy-box {
			.icon-bps-guojia {
				font-size: 1.3em;
				color: $color-primary;
			}

			.options {
				transform: translate(-2em);
				left: 60% !important;
				&::before {
					left: 1.5em;
				}
			}
		}
	}

	.user-box {
		text-align: center;
		margin-bottom: 1em;

		.avatar {
			width: 150px;
			height: 150px;
			border-radius: 50%;
			margin: 20px auto;
			border: 1px solid $color-primary;
		}
		@include respond-to(mb){
			.avatar {
				margin: 6.5vw 20vw 2vw !important;
			}
		}
		button {
			height: 30px;
			margin-top: 20px;
			border-radius: 5px;
		}
	}

	>.link-box {
		margin: 0 1em;
	}

	.link-box {
		&.select {
			border-radius: 10px;
			background: $color-second;
		}

		a {
			display: block;
		}

		>label {
			display: block;
			padding: 0.5em 0;
			margin-left: 1.5em;

			span {
				color: white;
				font-size: 10px;
				padding: 2px 3px 1px;
				display: inline-block;
				transform: scale(0.8);
				border-radius: 0 1em 1em 1em;
			}

			b {
				float: right;
				margin-right: calc(1em + 3px);

				&.arrow {
					font-size: 10px;
					margin-right: 1em;
				}
			}
		}

		.link-box {
			>label {
				padding-left: 1em;
			}

			.link-box>label {
				padding-left: 2em;

				.link-box>label {
					padding-left: 3em;
				}
			}
		}

		.link {
			margin-left: 0;
		}
	}

	.hot {
		::v-deep .card {
			flex-direction: row;
			margin-top: 0.9em !important;

			.card-img {
				width: 22vw;
				height: 22vw;
				padding-bottom: 0;
			}

			.hot-box {
				transform: translateY(-25%) scale(0.5);
				left: -0.5em;
			}

			.content {
				text-align: left;
				padding: 0.5em 0 0.5em 1.3em;
				justify-content: space-evenly;

				>label {
					flex: 0;
				}
			}

			.content-title {
				font-size: 1em;
				font-weight: normal;
			}

			.content-sub-title {
				display: none;
			}

			.content-price {
				color: #666;
			}
		}

		.link-box {
			text-align: center;

			label {
				padding: 0;
				color: #999999;
				margin: 2em 0 3em;
				display: inline-block;
				border-bottom: 1px solid;
			}
		}
	}
}
.top-tip {
	color: white;
	text-align: center;
	align-items: center;
	justify-content: center;
	font-size: calc(1em - 2px);
	background-color: $color-primary;
	background-size: cover;

	>div {
		padding: 0.8em 0;
	}
}

.options-box {
	cursor: pointer;
	padding: 1.5em 0;
	position: relative;

	b.arrow {
		font-size: 10px;
	}

	a.select,
	.select>*:not(.options),
	&.select>*:not(.options) {
		font-weight: bold;
		color: $color-primary !important;

		&::after {
			width: 100% !important;
		}
	}

	&:hover {
		>a {
			color: $color-primary;

			&::after {
				width: 100% !important;
			}
		}

		b.arrow::before {
			transform: rotate(-180deg);
		}

		>a b.arrow::before {
			transform: rotate(90deg);
		}

		>.options {
			display: flex;
		}
	}

	>.options {
		cursor: auto;
		border-radius: 5px;
		padding-bottom: 1em;
		background: white;
		color: $text-primary;
		min-width: max-content;
		flex-direction: column;
		transform: translate(-50%);
		box-shadow: 0 0 10px -5px $bg-mask;
		position: absolute;
		z-index: 99;
		left: -105%;
		top: 90%;
		@include respond-to(mb){
			>.options {
				left: -65% !important;
			}
		}
		label {
			white-space: nowrap;
			padding: 1em 1.25em 0.5em;
		}

		a>span[style] {
			color: white;
			font-size: 10px;
			padding: 1px 6px;
			display: inline-block;
			border-radius: 0 1em 1em 1em;
		}

		&::before {
			content: '';
			width: 10px;
			height: 10px;
			background: white;
			box-shadow: -3px -3px 6px -4px $bg-mask;
			transform: scale(1, 1.3) rotate(45deg);
			position: absolute;
			left: calc(50% - 5px);
			z-index: -1;
			top: -5px;
		}
	}
}

.product:not(.category)>.options {
	min-width: 55em;
	flex-wrap: wrap;
	flex-direction: row;
	padding: 1.5em 0 2.5em 3em;

	>label {
		width: 33.33%;
	}
}

.product.category {
	position: static;

	>.options {
		padding: 1em 2em 2em;
		flex-direction: row;
		top: 68px;

		>label {
			padding-left: 3em;

			&:not(:first-child) {
				border-left: 1px solid #EFEFEF;
			}

			&:first-child {
				width: 48em;

				>div {
					display: flex;
					flex-wrap: wrap;

					>.options-box {
						width: 33.33%;

						>.options {
							left: 85%;
						}
					}
				}
			}

			>div>.options-box {
				padding: 0.8em 0;
			}

			>a {
				line-height: 3em;
				font-weight: bold;
				font-size: 1.286em;
			}
		}
	}
}

.hot .options {
	padding-bottom: 1em;
	font-size: calc(1em - 2px);

	::v-deep .modal-box {
		width: 59vmax;
		padding: 13px 25px 8px;

		.card {
			color: white;

			.content {
				padding: 10px;
				text-align: left;
				background: var(--color-primary);

				.content-title {
					font-size: 1em;
					min-height: 4em;
					font-weight: 400
				}

				.content-sub-title {
					display: none;
				}
			}
		}
	}

	>label a {
		color: #999;
		margin-left: 8px;
		border-bottom: 1px solid;
	}
}

.nav {
	height: 68px;
	padding-top: 0 !important;
	padding-bottom: 0 !important;

	.logo-box img {
		width: auto;
		height: 52px;
		max-width: 16vw;
		object-fit: contain;
	}

	.options a:hover {
		color: $color-primary !important;
	}

	.nav-box {
		flex: 1;

		>* {
			margin-left: min(4vw, 5em);

			>a {
				padding: 1em 0;
				position: relative;

				&::after {
					content: '';
					width: 0;
					border-bottom: 1px solid;
					transition: all 0.2s ease;
					position: absolute;
					bottom: 0;
					left: 0;
				}
			}
		}
	}

	.system-box {
		justify-content: end;

		>*:not(:first-child) {
			margin-left: 2vw;
		}

		.options a {
			color: #666;
			min-width: 4em;
			padding-top: 0.7em;
			margin: 0.7em calc(1vmax + 6px) 0;
		}

		.cart-box {
			b {
				position: relative;
				margin-right: 0.5vmax;

				label {
					height: 20px;
					font-size: 10px;
					min-width: 20px;
					color: white;
					line-height: 20px;
					text-align: center;
					border-radius: 50%;
					background: $color-red;
					position: absolute;
					right: -12px;
					top: -10px;
				}
			}

			>label {
				font-weight: bold;
				font-size: calc(1em - 2px);
			}
		}

		.lang-box .options {
			transform: none;
			right: -1vmax;
			left: auto;
		}
	}
}

.lang-box {
	font-size: 12px;
	text-transform: uppercase;

	.lang-img {
		width: 1.7em;
		height: 1.7em;
		margin-right: 5px;
		border-radius: 50%;
		vertical-align: text-bottom;
	}
	::v-deep .continent-box {
		.continent {
			display: grid;
			grid-template-columns: repeat(2,1fr);
			gap: 10px;
		}
	}
}

.to-top {
	color: white;
	cursor: pointer;
	padding: 0.6em 0.7em;
	border-radius: 0.5em;
	background: rgba($color: #000, $alpha: .4);
	transform: rotate(-90deg);
	position: fixed;
	z-index: 99999;
	bottom: 90px;
	right: 3vw;
}
</style>
