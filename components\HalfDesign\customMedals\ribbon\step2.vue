<template>
  <div class="step2 ribbonStep2 style2" :id="stepData.copyId">
    <half-design-litter-title v-show="stepData.attributeTitle" :index="stepData.id"
      :data-name="`${stepData.styleClass + '_' + stepData.id}`" style="margin: 10px 0"
      :stepTitle="stepData.minStepTitle">{{ stepData.attributeTitle }} </half-design-litter-title>
    <div class="step-content"
      v-if="!stepData.isHideStep || (stepData.isHideStep && stepData.productParamList.length > 1)">
      <div class="step-item" :class="{ active: index === selectIndex }"
        v-for="(step, index) in stepData.productParamList" :key="index" @click="selectStep(step, index)">
        <div class="d-flex-center text-center name">
          <div class="text-truncate">
            <div class="nameText">
              <span class="valueNameText">{{ step[nameProperty] }} </span>
            </div>
          </div>
        </div>
        <half-design-check-icon class="absolute-top-right2 check-icon"></half-design-check-icon>
      </div>
    </div>
    <div class="errorTip">
      <v-alert dense outlined type="error">
        {{ langSemiCustom.errorTip }}
      </v-alert>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ribbonStep2',
  props: {
    stepData: {
      type: Object,
      default: () => ({}),
    },
  },
  components: {},
  data() {
    return {
      selectIndex: -1,
      selectItem: null,
    }
  },
  watch: {},
  computed: {
    shape() {
      return this.selectItem?.valueName;
    },
    attributeFlag() {
      return this.stepData.attributeFlag;
    },
    nameProperty() {
      let attributeFlag = this.attributeFlag,
        key;
      switch (attributeFlag) {
        case "color":
          key = "colorAlias";
          break;
        default:
          key = "valueName";
          break;
      }
      return key;
    },
    langSemiCustom() {
      return this.$store.getters.lang?.semiCustom;
    },
  },
  methods: {
    selectStep(item, index = 0, state = false) {
      this.selectIndex = index;
      this.selectItem = item;
      this.$emit("selectStep", {
        type: this.stepData.isLanyards,
        data: item,
        parent: this.stepData,
        id: this.stepData.id,
        copyId: this.stepData.copyId,
        firstSelect: state,
      });
    },
    selectDefault() {
      if (this.stepData.productParamList.length) {
        this.selectStep(this.stepData.productParamList[0], 0, true);
      }
    },
  },
  created() { },
  mounted() {
    if (!this.noDefaultSelect) {
      this.$Bus.$on("selectDefaultOtherStep", this.selectDefault);
    }
  },
  beforeDestroy() {
    if (!this.noDefaultSelect) {
      this.$Bus.$off("selectDefaultOtherStep", this.selectDefault);
    }
  },
}
</script>

<style scoped lang="scss">
@import "~assets/css/half.scss";

.ribbonStep2 {
  .step-content {
    display: grid;

    .step-item {
      position: relative;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      @include step-default;
      border-color: transparent;
      min-width: 0;
      cursor: pointer;
      color: #333333;
      overflow: hidden;
      background-color: #f6f6f6;
      border-radius: 6px;

      .text-truncate {
        display: flex;
        align-items: center;
        font-weight: 400;
        font-size: 14px;

        .nameText {
          display: flex;
          align-items: center;
          overflow: hidden;
          max-width: 100%;
        }

        .valueNameText {
          white-space: normal;
          word-wrap: break-word;
        }
      }

      .name {
        margin-top: 4px;
      }

      .questionMark {
        flex-shrink: 0;
        display: none;
      }

      .priceText {
        display: none;
      }

      .check-icon {
        display: none;
      }

      .zoom-icon {
        display: none;
      }
    }
  }
}

.style2 .step-content {
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;

  .step-item {
    background-color: #F4F5F5;

    &.active {
      background-color: #F4F5F5;

      .check-icon {
        display: flex;
      }
    }

    .imgWrap {
      @include flex-center;

      img {
        aspect-ratio: 150/184;
        border: none;
      }
    }

    .text-truncate {
      flex-direction: column;
      justify-content: center;
      row-gap: 4px;

      .priceText {
        display: none;

        &.redText {
          color: #de3500;
        }
      }
    }

    .questionMark {
      margin-left: 4px;
      display: flex;
    }
  }
}

.style3 .step-content {
  grid-template-columns: repeat(3, 1fr) !important;
  gap: 10px;
}
</style>
