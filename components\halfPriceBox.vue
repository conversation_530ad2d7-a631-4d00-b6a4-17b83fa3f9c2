<!-- priceType: [
				{
					label: "单价",
					value: "1",
					key: "unitPrice",
				},
				{
					label: "总价",
					value: "2",
					key: "totalPrice",
				},
				{
					label: "单价百分比",
					value: "3",
					key: "unitPercent",
				},
				{
					label: "总价百分比",
					value: "4",
					key: "totalPercent",
				},
				{
					label: "递增价格",
					value: "5",
					key: "increasePrice",
				},
				{
					label: "组合单价",
					value: "6",
					key: "composeUnitPrice",
				},
				{
					label: "面积递增价格",
					value: "7",
				},
			], -->
<template>
	<label v-bind="$attrs" class="halfPrice">
		<span v-show="ccyPrice !== 0 && ccyPrice > 0 && priceData.priceType == 1">
			{{ "+" + symbol + ccyPrice + (country == "zh" ? "元" : "") }}<span v-show="showUnit">{{ "/" + unit }}</span>
		</span>
		<span v-show="ccyPrice !== 0 && ccyPrice > 0 && priceData.priceType == 3">{{ ccyPrice }}</span>
		<span v-show="ccyPrice == 0">{{ this.langQuote.free }}</span>
	</label>
</template>

<script>
export default {
	name: "half-price-box",
	inject: ["getCustomPriceData", "getUnit"],
	model: {
		prop: "nowPrice", // 自定义 prop 名称
		event: "setPrice", // 自定义事件名称
	},
	props: {
		nowPrice: {
			type: [Object, Number, String],
		},
		showUnit: {
			type: Boolean,
			default: false,
		},
		priceData: {
			type: Object,
			default() {
				return {};
			},
		},
		hideSymbol: {
			// 隐藏货币标识
			type: Boolean,
			default: false,
		},
		decimal: {
			// 小数位数
			type: Number,
			default: 2,
		},
		ccy: {
			// 指定要转换的货币
			type: Object,
			default() {
				return {};
			},
		},
		removeZero: {
			//去除小数点后面的0 例子：1070.00 -》 1070
			type: Boolean,
			default: false,
		},
	},
	watch: {
		ccyPrice(newVal) {
			let data = newVal;
			if (data == 0) {
				data = this.langQuote.free;
				this.$emit("setPrice", { data: data, isFree: true });
				return;
			}
			if (this.priceData.priceType == 1 && data !== 0) {
				data = "+" + this.symbol + newVal + (this.country == "zh" ? "元" : "");
				if (this.showUnit) {
					data += "/" + this.unit;
				}
			}
			this.$emit("setPrice", { data: data, isFree: false });
		},
	},
	computed: {
		customPriceData() {
			return this.getCustomPriceData();
		},
		unit() {
			return this.getUnit();
		},
		langQuote() {
			return this.$store.getters.lang.quote || {};
		},
		country() {
			return this.ccy.country || this.$store.state.language.language;
		},
		symbol() {
			return this.hideSymbol || this.country == "zh" ? "" : (this.ccy.rate && this.ccy.symbol) || this.$store.state.currency.symbol || "$";
		},
		rate() {
			return Number(this.ccy.rate && this.ccy.symbol ? this.ccy.rate : this.$store.state.currency.rate || 1) * 100;
		},
		price() {
			if (this.priceData.priceType == 1) {
				return this.priceData.unitPrice;
			}
			if (this.priceData.priceType == 3) {
				return this.priceData.unitPercent;
			}
		},
		ccyPrice() {
			let price = this.price && !isNaN(this.price) ? (Number(Number(this.price).toFixed(this.decimal)) * this.rate) / 100 : 0;
			if (this.priceData.priceType == 1) {
				const plusAdditionalItemPrice = +(this.customPriceData?.plusAdditionalItemPrice || 0);
				const multiAdditionalItemPrice = +(this.customPriceData?.multiAdditionalItemPrice || 1);
				let adjustedPrice = price + plusAdditionalItemPrice;
				adjustedPrice = adjustedPrice * multiAdditionalItemPrice;
				// 需要适配分隔符的国家标识 德国
				adjustedPrice = adjustedPrice.toLocaleString(["de"].includes(this.country) ? this.country : "en", { minimumFractionDigits: this.decimal, maximumFractionDigits: this.decimal });
				if (this.removeZero) {
					return adjustedPrice.replace(/(\.0*|0+)$/, "");
				} else {
					return adjustedPrice;
				}
			} else if (this.priceData.priceType == 3) {
				if (price == 0) return price;
				const multiAdditionalItemPrice = this.customPriceData?.multiAdditionalItemPrice || 1;
				price = price * multiAdditionalItemPrice * 100;
				return "+" + price.toLocaleString(["de"].includes(this.country) ? this.country : "en", { minimumFractionDigits: this.decimal, maximumFractionDigits: this.decimal }) + "%";
			} else {
				return 0;
			}
		},
	},
};
</script>
