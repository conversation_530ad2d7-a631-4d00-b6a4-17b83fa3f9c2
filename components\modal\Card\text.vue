<template>
	<div class="modal-box" :class="modal.wrapClass" :style="modal.style">
		<div class="content part2 hover-tag" v-for="(l, li) in modal.list" :key="li" :style="modal.contentStyle"
			:childHoverIndex="li">
			<EditDiv class="title" v-model:content="l.title.value" v-if="l.title"
				@click="setModalType(l.title, modal.list, 'text')" :style="modal.titleStyle" />
			<EditDiv class="des" v-model:content="l.text.value" v-if="l.text"
				@click="setModalType(l.text, modal.list, 'text')" :style="modal.textStyle" />
		</div>
	</div>
</template>

<script>
export default {
	props: {
		data: {
			type: Object,
			default: {},
		}
	},
	data() {
		return {
			modal: {
				style: {},
				type: { index: 0, clickPosition: 'outer' },
				...this.data
			}
		};
	},
	watch: {
		modal: {
			handler(val) {
				if (process.env.isManage) this.$emit("update:data", val);
			},
			immediate: true,
			deep: true
		}
	},
	methods: {
		setModalType(target, targetArray, clickType, event, other) {
			this.$setModal(this, target, targetArray, clickType, event, other)
		},
	}
};
</script>

<style lang="scss" scoped>
[theme] .modal-box {
	padding-top: 3em;

	.content {
		padding: 0 max(calc(50% - 640px), 0px) 2.5em;
		position: relative;
	}

	.title {
		font-size: 1.5em;
		font-weight: bold;
		margin-bottom: 0.5em;
	}

	.des {
		color: #666666;
	}
}


.policeText {
	padding: 70px 0;
	background-color: #F8F8F8;

	.title {
		font-size: 30px;
		margin-bottom: 23px;
	}

	.des {
		font-size: 18px;
		color: #333333;
	}
}



[theme='10'] {
	font-size: 1.25em;
}

[theme='11'] .content {
	font-size: 1.1em;
}



@media screen and (max-width: $mb-width) {
	[theme] .modal-box {
		.title {
			font-size: 15px;
			line-height: 18px;
		}

		.des {
			font-size: 12px;
			line-height: 15px;
		}
	}

	.policeText {
		padding: 20px 0;
	}
}
</style>
