<template>
	<div>

		<div class="box-border">
			<i class="el-icon-close" @click="closeMask"></i>
		</div>
		<h3 class="step-title">
			<span class="step-title-color">{{ index }}</span>
			{{ itemData.alias ? itemData.alias : itemData.paramName }}
		</h3>
		<div class="step-qty-box" v-if="!isMobile">
			<span>Quantity:</span>
			<el-autocomplete class="newInputNumber" :value="customQty" @input="updateQty" size="small" :controls="false"
				:fetch-suggestions="querySearch"></el-autocomplete>
			<div class="control-btn">
				<div class="top" @click="add"></div>
				<div class="bottom" @click="sub"></div>
			</div>
			<span class="pair">Pair</span>
			<div class="d-flex-center confirmBtnWrap">
				<QuoteBtn @click.native="showMaskFn('qty')" :disabled="!Boolean(customQty)">{{ btnInfo }}</QuoteBtn>
			</div>
		</div>
		<div v-else class="mb-qty-box">
			<div class="control-text">Quantity:</div>
			<div style="display:flex;align-items: center;column-gap: 10px;">
				<div class="mb-control">
					<span @click="sub" class="left"></span>
					<el-autocomplete class="newInputNumber" :value="customQty" @input="updateQty" size="small"
						:controls="false" :fetch-suggestions="querySearch"></el-autocomplete>
					<span @click="add" class="right"></span>
				</div>
				<span>Pair</span>
			</div>
			<div class="d-flex-center confirmBtnWrap">
				<QuoteBtn @click.native="showMaskFn('qty')" :disabled="!Boolean(customQty)">{{ btnInfo }}</QuoteBtn>
			</div>
		</div>
	</div>
</template>

<script>
import QuoteBtn from "@/components/Quote/QuoteBtn";

export default {
	props: {
		restaurants: Array,
		itemData: Object,
		index: Number,
		customQty: [Number, String],
		btnInfo: {
			type: String,
			default: "Next Step",
		},
		nomal: {
			type: String,
		},
	},
	data() {
		return {
			// qty: this.customQty
		};
	},
	computed: {
		lang() {
			return this.$store.getters.lang.quote || {};
		},
		isMobile() {
			return this.$store.getters.isMobile;
		}
	},
	components: {
		QuoteBtn,
	},
	methods: {
		querySearch(queryString, cb) {
			var restaurants = this.restaurants;
			var results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants;
			// 调用 callback 返回建议列表的数据
			cb(results);
		},
		createFilter(queryString) {
			return (restaurant) => {
				return restaurant.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0;
			};
		},
		updateQty(val) {
			val = (val + "").replace(/[^\d]/g, "");
			// this.qty = val;
			this.$emit("update:customQty", val);
			this.$emit("calcPrice");
		},
		showMaskFn(name) {
			this.$emit("showMaskFn", name);
		},
		closeMask() {
			this.$emit("closeMask");
		},
		add() {
			// if (!this.qty) {
			// 	return;
			// }
			if (!this.customQty) {
				return;
			}
			// this.qty = String(Number(this.qty) + 1);
			this.$emit("update:customQty", String(Number(this.customQty) + 1));
			this.$emit("calcPrice");
		},
		sub() {
			if (!this.customQty || Number(this.customQty) < 1) {
				return
			}
			// this.qty = String(Number(this.qty) - 1);
			this.$emit("update:customQty", String(Number(this.customQty) - 1));
			this.$emit("calcPrice");
		}
	}
};
</script>

<style scoped lang="scss">
.confirmBtnWrap {
	display: none;
	margin-top: 33px;

	@media screen and (max-width: 767px) {
		margin-top: 20px;
	}
}





.step-qty {
	position: relative;
	background-color: #fff;
	border-radius: 10px;

	.step-qty-box {
		display: flex;
		justify-content: flex-start;
		align-items: center;
		padding-left: 20px;

		span {
			margin-right: 26px;
			color: #9E9E9E;
		}

		span.pair {
			color: #333;
		}

		.control-btn {
			margin: 0 15px;
			cursor: pointer;

			>div {
				width: 25px;
				height: 20px;
				background: #F9F9F9;
				border-radius: 4px 4px 0px 0px;
				position: relative;
			}

			.top::after {
				content: "";
				position: absolute;
				width: 0;
				height: 0;
				bottom: 4px;
				left: 5px;
				border-left: 7px solid transparent;
				border-right: 7px solid transparent;
				border-bottom: 7px solid #333;
			}

			.bottom::after {
				content: "";
				position: absolute;
				width: 0;
				height: 0;
				top: 4px;
				left: 5px;
				border-left: 7px solid transparent;
				border-right: 7px solid transparent;
				border-top: 7px solid #333;
			}
		}

		.confirmBtnWrap {
			margin-top: 0 !important;
			width: 240px;

			@media screen and (max-width: 767px) {
				flex: 30%;
			}
		}
	}

	.control-text {
		margin-bottom: 6px;
		font-size: 14px;
		color: #9E9E9E;
	}

	.mb-control {
		display: inline-flex;
		align-items: center;
		border: 1px solid #EBEBEB;
		border-radius: 5px;

		span {
			position: relative;
			width: 30px;
			height: 37px;
			background: #F9F9F9;
		}

		.left::after {
			content: "";
			position: absolute;
			width: 0;
			height: 0;
			top: 11px;
			left: 10px;
			border-right: 8px solid #333;
			border-top: 8px solid transparent;
			border-bottom: 8px solid transparent;
		}

		.right::after {
			content: "";
			position: absolute;
			width: 0;
			height: 0;
			top: 11px;
			right: 10px;
			border-left: 8px solid #333;
			border-top: 8px solid transparent;
			border-bottom: 8px solid transparent;
		}
	}

	.box-border {
		display: none;

		.el-icon-close {
			display: flex;
			justify-content: center;
			align-items: center;
			position: absolute;
			font-weight: 700;
			top: 0;
			right: 0;
			width: 40px;
			height: 40px;
			transform: translate(50%, -50%);
			cursor: pointer;
			background: #ffffff;
			border-radius: 50%;
			box-shadow: 1px 2px 20px 0 rgba(0, 0, 0, 0.2);
			z-index: 10;
		}

		@media screen and (max-width: 767px) {
			.el-icon-close {
				width: 30px;
				height: 30px;
				transform: translate(0, 0);
				box-shadow: none;
			}
		}
	}

	&.mask {
		position: relative;
		z-index: 101;

		.confirmBtnWrap {
			position: relative;
		}

		.box-border {
			position: absolute;
			left: 0;
			right: 0;
			top: 0;
			bottom: 0;
			display: block;
			background-color: #fff;
			border: 1px solid #d9dbdd;
		}

		.step-title {
			position: relative;
		}

		.step-box {
			position: relative;
		}
	}

	&.step-active {
		.confirmBtnWrap {
			display: flex;
			justify-content: center;
			align-items: center;
		}

		.box-border {
			position: absolute;
			left: 0;
			right: 0;
			top: 0;
			bottom: 0;
			display: block;
			background-color: #fff;
			border: 1px solid #d9dbdd;
		}
        .step-title {
			position: relative;
		}
		.step-qty-box {
			position: relative;
		}
		.mb-qty-box {
			position: relative;
		}
	}

	.step-title {
		font-size: 24px;
		font-weight: 700;
		color: #333333;
		margin-bottom: 23px;

		&>span {
			color: $color-primary;
		}
	}

	@media screen and (max-width: 767px) {
		margin-bottom: 10px;
		border-radius: 5px;
		padding: 20px 7px;

		&.mask {
			.box-border {
				.el-icon-close {
					width: 30px;
					height: 30px;
					transform: translate(0, 0);
					box-shadow: none;
				}
			}
		}

		.step-title {
			margin-bottom: 10px;
			font-size: 14px;
			font-weight: bold;
			color: #171719;
		}
	}
}

::v-deep .newInputNumber {
	width: 90px;
	height: 40px;
	border-radius: 10px;
	border: 1px solid #EBEBEB;

	@media screen and (max-width: 767px) {
		height: 30px;
		width: 100px;
		border: none;
	}

	.el-input {
		height: 100%;

		input {
			height: 100%;
			line-height: 1em;
			text-align: center;
			font-size: 16px;
			border-radius: 10px;
			color: #333333;
			border: none;

			@media screen and (max-width: 767px) {
				font-size: 12px;
				border-radius: 5px;
			}
		}
	}
}</style>
