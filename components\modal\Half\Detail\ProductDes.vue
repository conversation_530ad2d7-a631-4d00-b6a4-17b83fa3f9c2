<script>
export default {
	props: {
		productInfo: {
			type: Object,
		},
	},
	computed: {
		proId() {
			return this.$store.state.proId;
		},
		proSystem() {
			return this.$store.state.proSystem;
		},
		isMobile() {
			return this.$store.state.device === "mb";
		},
	},
	data() {
		return {};
	},
	methods: {
		createQRcode(logoSrc, logoSize = 60) {
			if (this.isMobile) logoSize = 40;
			//二维码 尝试
			let QRCode = require("qrcodejs2");
			const ele = this.$refs.qrcode;
			const styles = window.getComputedStyle(ele);
			const width = styles.getPropertyValue("width");
			const height = styles.getPropertyValue("height");
			const qrcode = new QRCode(ele, {
				text: window.location.href,
				width: parseInt(width),
				height: parseInt(height),
				colorDark: "#000000",
				colorLight: "#ffffff",
				correctLevel: QRCode.CorrectLevel.H,
			});
			if (!logoSrc) return;
			const logoImg = new Image();
			logoImg.setAttribute("crossOrigin", "anonymous");
			logoImg.src = logoSrc;
			logoImg.onload = () => {
				const canvas = qrcode._el.getElementsByTagName("canvas")[0];
				const qrcodeImg = ele.querySelector("img");
				const context = canvas.getContext("2d");
				const x = (canvas.width - logoSize) / 2;
				const y = (canvas.height - logoSize) / 2;
				context.drawImage(logoImg, x, y, logoSize, logoSize);
				const imageUrl = canvas.toDataURL("image/png");
				qrcodeImg.src = imageUrl;
				qrcode.im = imageUrl;
			};
		},
	},
	mounted() {
		if (this.proId === 446) {
			let logoSrc = this.proSystem.logo;
			this.createQRcode(logoSrc);
		}
	},
};
</script>

<template>
	<div class="productDes">
		<div class="desTip" v-if="$store.state.proTheme == '11'">产品信息</div>
		<div class="desBox">
			<div class="des-title">
				{{ productInfo.productDesc }}
			</div>
			<div class="des" v-html="productInfo.richDescription"></div>
			<div ref="qrcode" id="qrcode" v-if="proId === 446" style="width: 150px; height: 150px"></div>
		</div>
	</div>
</template>

<style scoped lang="scss">
.productDes {
	.desBox {
        padding: 1em;
		margin-top: 30px;
		background: #f7f7f7;
		border-radius: 10px;

		.des-title {
			font-weight: 400;
			font-size: 14px;
			color: #333333;
			line-height: 24px;
			margin-bottom: 12px;
		}

		.des {
			line-height: 2em;
			word-break: break-word;

			::v-deep p {
				margin-bottom: 0;
				line-height: 1.4;
				font-family: inherit;
				font-size: 14px;
			}
		}
	}

	@include respond-to(mb) {
		.desTip {
			margin-top: 20px;
			font-size: 16px;
		}
		.desBox {
			overflow: hidden;
			background-color: #f5f5f5;
			//margin: 20px -3vw 0;
            margin-top: 10px;
            border-radius: 0;
            padding: .5em;

			.des-title {
				margin: 10px 0;
				font-weight: 400;
				font-size: 14px;
				color: #333333;
			}

			.des {
				line-height: 2em;
				word-break: break-word;

				::v-deep p {
					margin-bottom: 0;
					line-height: 1.4;
				}
			}

			#qrcode2 {
				margin: 0px 0px 24px 24px;
			}
		}
	}
}

[theme="11"] .productDes {
	.desTip {
		margin-top: 30px;
		background: $color-dark;
		border-radius: 20px 0 20px 0;
		font-size: 18px;
		padding-left: 24px;
		font-weight: 500;
		color: #fffcf1;
	}

	.desBox {
		margin-top: 30px;
		border-radius: 10px;
		background-color: #e3ebe5;
		overflow: hidden;

		#qrcode {
			margin-top: 10px;
		}
	}

	//.des {
	//	background-image: url("https://oss-static-cn.liyi.co/web/quoteManage/20230921/%E5%9B%BE%E5%B1%82_26_20230921yxSmhe.png");
	//	background-position: right bottom;
	//	background-repeat: no-repeat;
	//	background-color: $bg-primary !important;
	//}
}
</style>
