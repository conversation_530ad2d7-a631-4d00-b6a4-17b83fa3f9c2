<template>
	<div class="tool-bar">
		<el-tooltip popper-class="dzToolTip" effect="light" :content="lang.pen" placement="bottom">
			<el-popover popper-class="pen-popover" placement="bottom" width="340" trigger="click">
				<div class="tool-icon" :class="{ active: currentTab === 1}" slot="reference" @click="pen">
					<b class="icon-a-icon-huihzhuanhuan"></b>
				</div>
				<div class="penWrap">
					<div class="top">
						<span>{{ lang.brushSize }}</span>
						<el-slider :value="penWidth" show-input :show-input-controls="false" :min="6" :max="8" :step="2"
								   :show-tooltip="false" @input="inputPenWidth" @change="changeDrawWidth"></el-slider>
						<el-popover popper-class="color-popover" placement="bottom-end" trigger="click">
							<div class="colorPicker" slot="reference">
								<div class="color-inner" :style="{ backgroundColor: penColor }">
									<b class="icon-down"></b>
								</div>
							</div>
							<div class="colorWrap" scrollbar>
								<div class="color-item"
									v-for="item in colorList.filter(x => !x.isNeonColorful)"
									@click="changeDrawColor(item)"
									:key="item.id">
									<div class="color-ellipse"
										:class="{'active': item.code == penColor }"
										:style="{'--bg': item.paramCode}">
									</div>
								</div>
							</div>
							<!-- <ColorPickerItem :colorItem="item" :isActive="item.code === penColor"
												 v-for="(item, index) in colorList" :key="index"
												 @changeColor="changeDrawColor"></ColorPickerItem> -->
						</el-popover>
					</div>
					<div class="btnWrap">
						<div class="startBtn" @click="startDraw('pen')">
							<b class="icon-a-icon-huihzhuanhuan"></b>
							<span>{{ lang.startDrawing }}</span>
						</div>
						<div class="stopBtn" @click="stopDraw">
							<button type="button" class="dzBtn stopBtn">{{ lang.stopDrawing }}</button>
						</div>
					</div>
				</div>
			</el-popover>
		</el-tooltip>
		<el-tooltip popper-class="dzToolTip" effect="light" :content="lang.eraser" placement="bottom">
			<el-popover popper-class="eraser-popover" placement="bottom" width="453" trigger="click">
				<div class="tool-icon" :class="{ active: currentTab === 2}" @click="eraser" slot="reference">
					<b class="icon-xiangpi"></b>
				</div>
				<div class="eraser-size">
					<span>{{ lang.size }}</span>
					<el-slider :value="eraserWidth" show-input :show-input-controls="false" :min="1" :max="100" :step="1"
						:show-tooltip="false"  @input="inputEraserWidth" @change="changeDrawWidth"></el-slider>
				</div>
			</el-popover>
		</el-tooltip>
		<el-tooltip popper-class="dzToolTip" effect="light" :content="lang.undo" placement="bottom">
			<div class="tool-icon" :class="{ active: currentTab === 3}" @click="undo">
				<b class="icon-a-icon-Undozhuanhuan"></b>
			</div>
		</el-tooltip>
		<el-tooltip popper-class="dzToolTip" effect="light" :content="lang.redo" placement="bottom">
			<div class="tool-icon" :class="{ active: currentTab === 4}" @click="redo">
				<b class="icon-a-icon-Redozhuanhuan"></b>
			</div>
		</el-tooltip>
		<el-tooltip popper-class="dzToolTip" effect="light" :content="lang.dowmload" placement="bottom">
			<div class="tool-icon" :class="{ active: currentTab === 5}" @click="download">
				<b class="icon-xiazai1"></b>
			</div>
		</el-tooltip>
		<el-tooltip popper-class="dzToolTip" effect="light" :content="lang.clear" placement="bottom">
			<div class="tool-icon" :class="{ active: currentTab === 6}" @click="clear">
				<b class="icon-a-icon-Clearzhuanhuan"></b>
			</div>
		</el-tooltip>
		<el-tooltip popper-class="dzToolTip" effect="light" :content="lang.share" placement="bottom">
			<div class="tool-icon" :class="{ active: currentTab === 7}" @click="share">
				<b class="icon-a-icon-Sharezhuanhuan"></b>
			</div>
		</el-tooltip>
		<el-tooltip popper-class="dzToolTip" effect="light" :content="lang.save" placement="bottom">
			<div class="tool-icon" :class="{ active: currentTab === 8}" @click="save">
				<b class="icon-Save"></b>
			</div>
		</el-tooltip>
	</div>
</template>

<script>
import ColorPickerItem from "@/components/MyDzxt/ColorPickerItem.vue";
import dzMixin from "@/mixins/dzMixin";
import canvas from "assets/js/fabricCore/dzPublic";

export default {
	mixins: [dzMixin],
	data() {
		return {
			currentTab: 0,
		}
	},
	components: {ColorPickerItem},
	computed: {
		canvas() {
			return canvas
		},
		lang() {
			return this.$store.getters.lang?.design;
		},
		colorList() {
			return this.$store.state.design.colorList
		},
		isGroup() {
			return this.canvas.mSelectMode === 'one' && this.canvas.mSelectOneType === 'group'
		},
		isGrid() {
			return !!this.canvas?.getWorkspace()?.get('fill')
		},
		penColor() {
			return this.$store.state.design.penColor
		},
		penWidth() {
			return this.$store.state.design.penWidth
		},
		eraserWidth(){
			return this.$store.state.design.eraserWidth
		}
	},
	methods: {
		centerElement() {
			this.canvas.position('center');
			this.$Bus.$emit('triggerUpdateLocation')
		},
		changeElementProperty(val, property) {
			this.canvas.changeElementProperty(val, property)
		},
		clone() {
			this.canvas.clone()
		},
		flip() {
			this.canvas.flip()
		},
		up() {
			this.canvas.up()
		},
		down() {
			this.canvas.down()
		},
		pen() {
			this.currentTab = 1
			this.canvas.pen()
			this.canvas.changeDrawWidth(this.penWidth - 2)
			this.canvas.changeDrawColor(this.penColor)
			this.canvas.startDraw('pen')
		},
		eraser() {
			this.currentTab = 2
			this.canvas.eraser()
			this.canvas.changeDrawWidth(this.eraserWidth)
			this.canvas.startDraw('eraser')
		},
		undo() {
			this.currentTab = 3
			this.canvas.undo()
		},
		redo() {
			this.currentTab = 4
			this.canvas.redo()
		},
		download() {
			this.currentTab = 5
			this.canvas.download()
		},
		clear() {
			this.currentTab = 6
			this.$confirm(`<div class='d-flex-center flex-column'><b class='warningIcon icon-sanjiao-gantanhao'></b><p>${ this.lang.deleteDesign }</p></div>`, this.lang.tips, {
				confirmButtonText: this.lang.comfirm,
				cancelButtonText: this.lang.cancel,
				dangerouslyUseHTMLString: true,
				customClass: 'warningCheckDialog'
			})
				.then(() => {
					this.canvas.clear()
					//其他处理
					this.$Bus.$emit('triggerAddText', 1)
				})
				.catch(() => {
				});
		},
		share() {
			this.currentTab = 7
			this.$emit("shareClick", { val: '', type: ''});
		},
		save() {
			this.currentTab = 8
			this.$emit("saveClick");
		},
		addGrid() {
			if (this.isGrid) {
				this.canvas.hideGrid()
			} else {
				this.canvas.showGrid()
			}
		},
		group() {
			if (this.isGroup) {
				this.canvas.unGroup()
			} else {
				this.canvas.group()
			}
		},
		
		inputPenWidth(val){
			this.$store.commit('design/set_penWidth', val)
		},
		inputEraserWidth(val){
			this.$store.commit('design/set_eraserWidth', val)
		},
		changeDrawWidth(penWidth) {
			this.canvas.changeDrawWidth(penWidth - 2)
		},
		changeDrawColor(item) {
			this.$store.commit("design/set_penColor", item.code);
			this.canvas.changeDrawColor(item.code)
		},
		startDraw(key) {
			this.canvas.startDraw(key)
			this.$toast.success("success");
		},
		stopDraw() {
			this.canvas.stopDraw();
			this.$toast.success("success");
		}
	}
};
</script>

<style scoped lang="scss">
.list2 {
	ul {
		margin-left: 0;
		li {
			list-style: none;
			display: flex;
			align-items: center;
			margin-bottom: 10px;

            &>div:first-child{
                width: 50px;
                margin-right: 10px;
            }

			&:last-child{
				margin-bottom: 0;
			}

			b, span {
				display: inline-block;
				background: #F4F4F4;
				padding: 5px 10px;
				height: 30px;
			}

			.jia {
				background: #fff;
				padding: 3px 5px;
			}
		}
	}
}

.tool-bar {
	display: flex;
	justify-content: center;
	align-items: center;
	background-color: #fff;
	border-radius: 2px;

	.tool-icon {
		display: flex;
		justify-content: center;
		align-items: center;
		position: relative;
		width: 40px;
		height: 40px;
		font-size: 17px;
		cursor: pointer;
		transition: all 0.3s;
		color: #070A35;
		&.active{
			position: relative;
			color: #5159C4;
			&::before{
				content: '';
				position: absolute;
				right: 0;
				bottom: 0;
				width: 3px;
				height: 3px;
				background-color: #5159C4;
			}
		}
	}
}

.stopBtn {
	width: 100px;
	height: 40px;
	background: #3053E1;
	border-radius: 6px;
	font-weight: 400;
	color: #FFFFFF;
}
</style>