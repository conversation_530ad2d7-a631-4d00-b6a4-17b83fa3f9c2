import {request} from "@/utils/request";

//获取优惠券列表
export function getUserCouponList(data) {
	return request({
		url: "/app/coupon/getUserCouponList",
		method: "post",
		data
	});
}

//使用优惠券
export function checkCode(data) {
	return request({
		url: "/app/member/checkCode",
		method: "post",
		data
	});
}

//获取代金券列表
export function getUserUsableVoucherList(data) {
	return request({
		url: "/app/refer/getUserUsableVoucherList",
		method: "post",
		data
	});
}

//获取转介绍积分
export function getUserReferPointsInfo(data) {
	return request({
		url: "/app/member/refer/getUserReferPointsInfo",
		method: "get",
		params:data
	});
}

//获取具体地址
export function getGoogleAddress(data) {
	return request({
		url: "/app/language/getGoogleAddress",
		method: "get",
		params:data
	});
}

//获取国家省市区
export function getGoogleAddressDetail(data) {
	return request({
		url: "/app/language/getGoogleAddressDetail",
		method: "get",
		params:data
	});
}
