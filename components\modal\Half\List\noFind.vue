<template>
	<div>
		<div class="not-find">
			<div class="result-tip">
				<span>{{ `${langSemiCustom.resultsFor} "${keyword}"` }}</span>
			</div>
			<div class="result-box" style="display: block">
				<div class="result-main" style="display: block">
					<div class="main-tip1">
						{{
							`${langSemiCustom.sorrySearch}"${keyword}"
             ${langSemiCustom.notMatchPro}`
						}}
					</div>
					<div class="main-tip2">
						<div class="tip2">{{ langSemiCustom.seachTips }}</div>
						<ul>
							<li>{{ langSemiCustom.checkSpelling }}</li>
							<li>{{ langSemiCustom.useFewerWords }}</li>
							<li>{{ langSemiCustom.useDifferentWords }}</li>
						</ul>
					</div>
					<div class="main-tip3">
						<div class="tip3">{{ langSemiCustom.needHelp }}</div>
						<div class="tip31">{{ langSemiCustom.within24Hours }}</div>
						<div class="tip31">
							<span>{{ langSemiCustom.orEmail }}</span>
							<a :href="'mailto:' + $store.state.proSystem.email">{{ $store.state.proSystem.email }}</a> <span>{{ langSemiCustom.weHelpyou }}</span>
						</div>
					</div>
					<div>
						<v-btn color="primary" @click="goInquiry">
							{{ langSemiCustom.si }}
							<v-icon right large> mdi-menu-right</v-icon>
						</v-btn>
					</div>
				</div>
			</div>
		</div>
		<noParamInquiryBox :infoDialogVisible.sync="infoDialogVisible" :keyword="keyword" :cateName="cateName" :fatherCateName="fatherCateName" :parentCateId="parentCateId" :cateId="cateId"  :nuxtContentIndex="nuxtContentIndex" :isDialog="isDialog"></noParamInquiryBox>
	</div>
</template>

<script>
import noParamInquiryBox from "@/components/modal/Half/List/noParamInquiryBox.vue";
export default {
	name: "noFind",
	props: {
		keyword: {
			type: String,
			default: "",
		},
		cateName: {},
		parentCateId: {},
		cateId: {},
		fatherCateName: {},
		isDialog:{}
	},
	components: { noParamInquiryBox },
	data() {
		return {
			infoDialogVisible: false,
			nuxtContentIndex: "auto",
		};
	},
	watch: {},
	computed: {
		langQuote() {
			return this.$store.getters.lang.quote || {};
		},
		langSemiCustom() {
			return this.$store.getters.lang?.semiCustom;
		},
		isMobile() {
			return this.$store.state.device === "mb";
		},
	},
	methods: {
		goInquiry() {
			let nuxtContent = document.querySelector(".nuxtContent");
			let modalHalfListBox = document.querySelector(".modalHalfListBox");
			if (nuxtContent) {
				this.nuxtContentIndex = window.getComputedStyle(nuxtContent).zIndex;
				nuxtContent.style.zIndex = "999";
			}
			if (modalHalfListBox) {
				modalHalfListBox.style.zIndex = "2";
			}
			this.infoDialogVisible = true;
		},
	},
	created() {},
	mounted() {},
};
</script>

<style scoped lang="scss">
.not-find {
	width: 100%;

	.result-tip {
		height: 47px;
		background-color: #000;

		span {
			font-size: 18px;
			font-weight: bold;
			line-height: 47px;
			letter-spacing: 0em;
			color: #ffffff;
			padding-left: 26px;
		}
	}

	.result-box {
		width: 100%;
		min-height: 424px;
		overflow: hidden;
		background-color: #fff;
		margin: 10px auto;

		.result-main {
			width: 58%;
			padding: 33px 0;
			margin: 0 auto;

			.main-tip1 {
				font-size: 24px;
				font-weight: 500;
				line-height: 30px;
				letter-spacing: 0em;
				color: #3d3d3d;
			}

			.main-tip2 {
				margin: 30px 0;

				.tip2 {
					font-weight: 900;
					font-size: 18px;
					line-height: 30px;
					color: #3d3d3d;
				}

				ul {
					li {
						line-height: 30px;
						font-weight: 300;
						font-size: 18px;
						list-style: disc;
					}
				}
			}

			.main-tip3 {
				margin-bottom: 20px;

				.tip3 {
					font-weight: 900;
					font-size: 18px;
					line-height: 30px;
					color: #3d3d3d;
				}

				.tip31 {
					line-height: 30px;
					font-weight: 300;
					font-size: 18px;

					a {
						font-weight: 500;
						color: $color-primary;
						text-decoration: underline;
					}
				}
			}
		}
	}

	@include respond-to(mb) {
		.result-tip {
			span {
				font-size: 14px;
			}
		}

		.result-box {
			height: 474px;

			.result-main {
				width: 92%;

				.main-tip1 {
					font-size: 16px;
				}

				.main-tip2 {
					margin: 10px 0;

					.tip2 {
						font-size: 14px;
					}

					ul li {
						font-size: 14px;
					}
				}

				.main-tip3 {
					.tip3 {
						font-size: 14px;
					}

					.tip31 {
						font-size: 14px;
					}
				}
			}
		}
	}
}

.allowMsg {
	padding: 10px;
	margin-block: 10px 15px;
	background-color: #fff1ea;
	cursor: pointer;
	.allow-header {
		display: flex;
		align-items: center;
		font-family: Cambria, Cochin, Georgia, Times, "Times New Roman", serif;
		.allow-checkbox {
			height: 2em;
			width: 2em;
			background-color: #fff;
			border-radius: 2px;
			border: 1px solid #d24600;
			position: relative;
			&.active::before {
				content: "";
				position: absolute;
				left: 50%;
				top: 50%;
				height: 18px;
				width: 8px;
				transform: translate(-50%, -60%) rotate(45deg);
				border-right: 1px solid #d24600;
				border-bottom: 1px solid #d24600;
			}
		}
		.allow-title {
			font-weight: 400;
			font-size: 16px;
			margin-left: 10px;
		}
	}
	.allow-content {
		font-size: 11px;
		margin-top: 5px;
		line-height: 1.5;
	}
}
.dateSelfPickerClass {
	width: 100%;
	::v-deep .v-picker__body {
		width: 100% !important;
	}
}
</style>
