<template>
  <div class="designList"
    v-show="showDesign"
    @click.stop>
    <div class="design-tab">
      <div class="tab-item"
        :class="{'active':tabName == 'design'}"
        @click="changeTab('design')">
        {{ lang.myDesign }}
      </div>
      <div class="tab-item"
        :class="{'active':tabName == 'creation'}"
        @click="changeTab('creation')">
        {{ lang.myCreation }}
      </div>
    </div>
    <div class="listWrap"
      neonAiScrollBar>
      <div class="data-area"
        :class="{'noData':!designList.length}"
        v-show="tabName == 'design'">
        <div class="data-list">
          <div class="list-item"
            v-for="item in designList"
            :key="item.id"
            :title="item.templateName">
            <design-item :item="item"
              @clipartChecked="clipartChecked($event,'designList')"
              @updateName="updateName"
              @editDesign="editDesign"
              @downloadDesign="download($event, 'design')"
              @shareDesign="share($event, 'design')"
              @removeDesign="removeDesign"
              @previewDesign="previewDesign"
              @getPrice="getPrice"></design-item>
          </div>
        </div>

        <noResult v-if="!designList.length && !loadingArt"></noResult>
      </div>
      <div class="data-area creationList"
        :class="{'noData':!creationList.length}"
        v-show="tabName == 'creation'">
        <div class="data-list">
          <div class="list-item"
            v-for="item in creationList"
            :key="item.id">
            <cliparts :item="item"
              :clipartId="item.id"
              :clipartImage="item.creationUrl"
              @clipartChecked="clipartChecked($event,'creationList')"
              @download="download($event, 'creation')"
              @share="share($event, 'design')"
              @remove="remove">
            </cliparts>
          </div>
        </div>
        <noResult v-if="!creationList.length && !loadingArt"></noResult>
      </div>
    </div>
    <div class="loadMore"
      v-show="loadingArt">
      {{ lang.loading }}...
    </div>

    <el-dialog
      custom-class="previewDialog"
      :visible.sync="previewVisable"
      append-to-body
      width="30%"
      :before-close="handleClose">
      <div class="swiper">
        <img :src="previewDesignObj.templateUrl">
        <div class="operate-area">
          <div class="arrow-group">
            <button class="arrow left" @click="toDesign(-1)">
              <b class="icon-left"></b>
            </button>
            <button class="arrow right" @click="toDesign(1)">
              <b class="icon-right"></b>
            </button>
          </div>

          <div class="button-group">
            <button class="download" @click="download(previewDesignObj, 'design')">
              <b class="icon-xiazai"></b>
            </button>
            <button class="share" @click="share(previewDesignObj, 'design')">
              <b class="icon-a-icon-Sharezhuanhuan"></b>
            </button>
            <button class="delete" @click="removeDesign(previewDesignObj)">
              <b class="icon-shanchu"></b>
            </button>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import cliparts from "@/components/NeonDzxt/cliparts.vue";
import designItem from "@/components/NeonDzxt/designItem.vue";
import { downloadImage } from "@/utils/utils";
import { favoriteClipart, getArtList, likeQuery } from "@/api/newDzxt";
import dzMixin from "@/mixins/dzMixin";
import noResult from "@/components/MyDzxt/noResult.vue";
import { neonApi } from "@/api/neon/neon";
import {
  getDefaultTemplateList,
  editUserTemplates,
  getTemplateById,
  deleteUserTemplates,
} from "@/api/newDzxt";

export default {
  mixins: [dzMixin],
  data() {
    return {
      loadingArt: false,
      artForm: {
        page: 1,
        pageSize: 60,
        clipartTypeId: null,
        total: 0,
        pages: 1,
        likeQuery: "",
      },
      tabName: "design",
      designList: [],
      creationList: [],
      tipsHtml: "",
      previewDesignObj: {
        index: '',
        id: '',
        templateUrl: ''
      },
      previewVisable: false
    };
  },
  watch: {
    showDesign() {
      this.getCreationList();
      this.getDesignList();
    },
    isLogin(newV){
      if(newV){
        this.getCreationList();
        this.getDesignList();
      }
    }
  },
  components: {
    cliparts,
    designItem,
    noResult,
  },
  computed: {
    lang() {
      return this.$store.getters.lang?.design;
    },
    showDesign() {
      return this.$store.state.design.showDesign;
    },
    noMoreArt() {
      return this.artForm.page >= this.artForm.pages;
    },
    disabledLoadArt() {
      return this.loadingArt || this.noMoreArt;
    },
    status() {
      return this.$store.state.design.status;
    },
    artStatus() {
      return this.$store.state.design.artStatus;
    },
    isLogin() {
      return this.$store.getters.isLogin;
    },
    userId() {
      return this.$store.state.userInfo?.id;
    },
  },
  methods: {
    handleClose(){
      this.previewVisable = false
    },
    changeTab(val) {
      this.tabName = val;
    },
    async updateName(val) {
      await editUserTemplates({
        id: val.id,
        templateName: val.templateName,
      });
      this.designList.forEach((x) => {
        if (x.id == val.id) {
          x.templateName = val.templateName;
        }
      });
    },
    //编辑上传模版画布
    async editDesign(item) {
      this.canvas.templateName = item.templateName;
      this.canvas.loadTemplate(item.templateFile);
      this.$emit("update:myDesignDialog", false);
    },
    //单个去报价
    async getPrice(val) {
      let quoteUrl = this.$store.getters["design/getQuoteUrl"];
      if (!quoteUrl) return;
      const obj = {
        original_filename: val.templateName,
        secure_url: val.templateUrl,
      };
      let uploadList = [obj];
      let url = new URL(
        `${
          window.location.origin
        }${quoteUrl}?id=13729&uploadList=${JSON.stringify(uploadList)}`
      );
      window.removeEventListener("beforeunload", this.canvas.confirmExit);
      window.location.href = url.toString();
    },
    //获取所有选中的 既包含design也包含creation
    getAllChecked() {
      let arr = [];
      this.designList.forEach((x) => {
        if (x.checked)
          arr.push({
            original_filename: x.templateName,
            secure_url: x.templateUrl,
          });
      });
      this.creationList.forEach((x) => {
        if (x.checked)
          arr.push({
            original_filename: x.type + x.id,
            secure_url: x.creationUrl,
          });
      });
      return arr;
    },

    previewDesign(val) {
      this.previewDesignObj = val
      this.previewVisable = true
    },


    toDesign(val){
      const index = this.previewDesignObj.index;
      const length = this.designList.length;
      const newIndex = (index + val + length) % length;
      this.previewDesignObj = this.designList[newIndex]
    },

    download(val, type) {
      downloadImage(
        type == "creation" ? val.creationUrl : val.templateUrl,
        val.id
      );
    },

    share(val, type) {
      this.previewVisable = false
      this.$emit("shareClick", {
        val,
        type
      });
    },

    deleteUserUploadPic(citem) {
      this.$confirm(this.lang.delTemplate, this.lang.tips, {
        confirmButtonText: this.lang.comfirm,
        cancelButtonText: this.lang.cancel,
        type: this.lang.warning,
      })
        .then(() => {
          deleteUserTemplates({
            id: citem.id,
          }).then((res) => {
            this.getDefaultList();
          });
        })
        .catch(() => {});
    },

    // 删除design
    removeDesign(val) {
      this.$confirm(
        `<div class='d-flex-center flex-column'><b class='warningIcon icon-sanjiao-gantanhao'></b><p>${this.lang.delTemplate}</p></div>`,
        this.lang.tips,
        {
          confirmButtonText: this.lang.comfirm,
          cancelButtonText: this.lang.cancel,
          dangerouslyUseHTMLString: true,
          customClass: "warningCheckDialog",
        }
      ).then(async () => {
        await deleteUserTemplates({ id: val.id });
        this.previewVisable = false
        this.getDesignList();
      });
    },

    // 删除reaction
    remove(val) {
      // this.lang.delTemplate
      this.$confirm(
        `<div class='d-flex-center flex-column'><b class='warningIcon icon-sanjiao-gantanhao'></b><p>${this.lang.delTemplate}</p></div>`,
        this.lang.tips,
        {
          confirmButtonText: this.lang.comfirm,
          cancelButtonText: this.lang.cancel,
          dangerouslyUseHTMLString: true,
          customClass: "warningCheckDialog",
        }
      ).then(async () => {
        await neonApi.delUserCreation({ id: val.id });
        let newCreation = await this.getCreationList();
        this.$emit("updateCreation", newCreation);
      });
    },

    clipartChecked(val, arrKey) {
      this[arrKey].forEach((x) => {
        if (x.id == val.id) {
          if (x.checked) {
            this.$set(x, "checked", 0);
          } else {
            this.$set(x, "checked", 1);
          }
        }
      });
    },
    async getDesignList() {
      const { data } = await getDefaultTemplateList({
        categoryId: this.$store.state.design?.pageInfo?.id,
        page: 1,
        pageSize: 25,
      });
      this.designList = data.records.map((x,i)=>{ return {...x, index: i}});
    },
    async getCreationList() {
      const { data } = await neonApi.getUserCreation();
      this.creationList = data;
      return data;
    },

    addImg({ src, property }) {
      this.$store.commit("design/set_showArt", false);
      this.$Bus.$emit("triggerAddArt", {
        src,
        property,
      });
    },
    addArtFavorite(item) {
      if (!this.isLogin) {
        this.$store.commit("setLogin", "login");
        return false;
      }
      favoriteClipart({
        clipartId: item.id,
      }).then((res) => {
        item.isCollection = item.isCollection ? false : true;
      });
    },
  },
  mounted() {
    this.getCreationList();
    this.getDesignList();
  },
};
</script>

<style scoped lang="scss">
@import "@/assets/css/dzxt_theme";
.designList {
  overflow: hidden;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 10px 30px;
  background-color: #f4f4f4;
  z-index: 2;

  .design-tab {
    width: fit-content;
    display: grid;
    margin: 0 auto;
    grid-template-columns: 334px 334px;
    column-gap: 20px;
    font-size: 18px;
    color: #070a35;
    .tab-item {
      line-height: 50px;
      height: 50px;
      background-color: #e6e6e6;
      border-radius: 10px;
      text-align: center;
      cursor: pointer;
      position: relative;
      &.active {
        background-color: #fff;
        border-bottom-right-radius: 0;
        border-bottom-left-radius: 0;

        &::before,
        &::after {
          content: "";
          position: absolute;
          width: calc(50% + 20px);
          height: 20px;
          bottom: -19px;
        }
        &::before {
          left: -10px;
          background-image: radial-gradient(
            circle at 0 0,
            transparent 10px,
            #fff 10px
          );
        }
        &::after {
          right: -10px;
          background-image: radial-gradient(
            circle at 100% 0,
            transparent 10px,
            #fff 10px
          );
        }
      }
    }
  }

  .loadMore {
    position: absolute;
    left: 50%;
    bottom: 5px;
    transform: translateX(-50%);
    text-align: center;
  }

  .listWrap {
    display: grid;
    align-content: flex-start;
    overflow: hidden auto;
    padding: 20px;
    margin-top: 10px;
    height: 100%;
    background-color: #ffffff;
    padding-bottom: 140px;

    .data-area {
      &.noData {
        display: flex;
        height: calc(100vh - 100%);
        justify-content: center;
        align-items: center;
      }
      .data-list {
        display: grid;
        grid-template-columns: repeat(6, 1fr);
        grid-column-gap: 10px;
        grid-row-gap: 10px;
      }
    }

    .list-item {
      position: relative;
      background: #ffffff;
      border-radius: 10px;
      overflow: hidden;
    }
  }
}
</style>
<style lang="scss">
.previewDialog{
  border-radius: 10px;
  .swiper{
    text-align: center;
    .operate-area{
      position: absolute;
      z-index: 1;
      left: 0;
      top: 0;
      font-size: 20px;
      font-weight: 700;
      display: flex;
      height: 100%;
      width: 100%;
      flex-direction: column;
      .arrow-group{
        margin-top: auto;
        font-size: 35px;
        width: 100%;
        display: flex;
        justify-content: space-between;
      }
      .button-group{
        width: 50%;
        margin-top: auto;
        margin-inline: auto;
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        column-gap: 30px;
      }
    }

    img{
      width: 300px;
      height: 300px;
    }
  }
}
</style>