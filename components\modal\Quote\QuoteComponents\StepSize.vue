<template>
	<div class="step-item-params step-size-params" :class="{ largeGap: largeGap }">
		<div
			class="param-item"
			v-for="citem in itemData.childList"
			:key="citem.id"
			:class="{
				active: hasId(citem.id, selectedData[itemData.paramName]),
			}"
			@click="selectQuoteParams(itemData, citem)"
		>
			<CustomCircle :circleType="2" :isActive="hasId(citem.id, selectedData[itemData.paramName])"></CustomCircle>
			<span>{{ citem.alias }}</span>
			<Corner v-if="citem.labelText" :backgroundColor="citem.labelColor" type2 position="absolute">
				{{ citem.labelText }}
			</Corner>
		</div>
		<span @click="showSizeDialog" class="help" v-if="!hideHelp">{{ lang.bannerQuote.sizeHelp }} <b class="icon-info"></b></span>
	</div>
</template>

<script>
import CustomCircle from "@/components/Quote/customCircle.vue";
import Corner from "@/components/Medals/Corner.vue";

export default {
	props: {
		hideHelp: {
			type: Boolean,
			default: false,
		},
		selectedData: {
			type: Object,
			default: () => {
				return {};
			},
		},
		itemData: {
			type: Object,
			default: () => {
				return {};
			},
		},
		largeGap: {
			type: Boolean,
		},
	},
	computed: {
		lang() {
			return this.$store.getters.lang.quote || {};
		},
	},
	components: { Corner, CustomCircle },
	methods: {
		selectQuoteParams(item, citem) {
			this.$emit("selectQuoteParams", {
				item,
				citem,
			});
		},
		showSizeDialog() {
			this.$emit("showSizeDialog", true);
		},
		//判断是否选中
		hasId(id, arr) {
			if (!arr) {
				return false;
			}
			return !!arr.find((item) => {
				return item.id === id;
			});
		},
	},
};
</script>

<style lang="scss" scoped>
.step-item-params {
	display: grid;
	align-items: center;
	grid-template-columns: 1fr;
	gap: 0.8em;
	padding: 1em 1.3em;
	background-color: #fafafa;
	border-radius: 10px;

	&.largeGap {
		gap: 1.25em;
	}

	@include respond-to(mb) {
		grid-template-columns: repeat(2, 1fr);
		grid-gap: 1.3em;
		background-color: transparent;
		padding: 0;
	}

	.param-item {
        position: relative;
		display: flex;
		align-items: center;
		min-width: 0;
		cursor: pointer;

        @include respond-to(mb){
            .tips.type2{
                display: none;
            }
        }
	}
}

.help {
	display: flex;
	align-items: center;
	font-size: 0.75em;
	color: $color-primary;
	cursor: pointer;

	b {
		margin-left: 4px;
		font-size: 0.875em;
		@include respond-to(mb) {
			font-size: 1em;
		}
	}

	@include respond-to(mb) {
		display: none;
	}
}
</style>