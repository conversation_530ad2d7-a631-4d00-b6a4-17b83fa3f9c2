<template>
  <div class="labelText" v-show="labelText && labelText.length">
    {{ labelText }}
  </div>
</template>

<script>
export default {
  name: 'labelText',
  components: {},
  props: {
    labelText: {
      type: String,
      default: ''
    }
  },
  data() {
    return {

    }
  },
  watch: {},
  computed: {},
  methods: {},
  created() { },
  mounted() { },
}
</script>

<style scoped lang="scss">
.labelText {
  width: fit-content;
  max-width: 100px;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 14px;
  padding: 0 0.4em;
  color: #FFFFFF;
  background: #0066CC;
  border-radius: 10px 0px 10px 0px;

  @include respond-to(mb) {
    font-size: 12px;
    padding: 0.2em 0.4em;
  }
}
</style>
