<template>
	<base-dialog :value="sizeDialog" @update="update" class="sizePreviewDialog" :width="device !== 'mb' ? '610px' : '95%'">
		<div class="sizePreviewDialogContent">
			<div class="header">{{ lang.bannerQuote.t }}</div>
			<div class="body">
				<div class="des" style="color: #666666">
					<p>
						<span style="font-weight: bold">{{ lang.bannerQuote.t1 }}</span>
						{{ lang.bannerQuote.t2 }}
					</p>
					<p>{{ lang.bannerQuote.t3 }} <br />{{ lang.bannerQuote.t4 }}</p>
					<p>{{ lang.bannerQuote.t5 }}</p>
				</div>
				<div class="previewCon">
					<img :src="previewImg" alt="" />
				</div>
				<div class="contact">
					{{ lang.bannerQuote.t6 }}
					<a href="mailto:<EMAIL>">{{ lang.bannerQuote.cs }}</a>
				</div>
			</div>
		</div>
	</base-dialog>
</template>

<script>
import BaseDialog from "@/components/Quote/BaseDialog.vue";

export default {
	props: {
		sizeDialog: {
			type: Boolean,
			default: false,
		},
        previewImg:{
            type: String,
            default: "https://static-oss.gs-souvenir.com/web/quoteManage/20240827/Legend_-_Custom_keychain_size_reference_20240827riFhaW.jpg",
        }
	},
    computed: {
        lang() {
            return this.$store.getters.lang.quote || {};
        },
        device() {
            return this.$store.state.device;
        },
    },
	components: { BaseDialog },
	methods: {
		update(val) {
			this.$emit("update:sizeDialog", val);
		},
	},
};
</script>

<style lang="scss" scoped>
.sizePreviewDialogContent {
    padding: 36px;
    font-size: 14px;

    @include respond-to(mb) {
        padding: 2.67rem 1.58rem;
        font-size: 1rem;
    }

    .header {
        margin-bottom: 10px;
        font-size: 24px;
        font-weight: 700;
        text-align: left;

        @include respond-to(mb) {
            font-size: 1.25rem;
        }
    }

    .body {
        p {
            margin-bottom: 10px;
        }
    }

    .des {
        p {
            margin-bottom: 20px;
        }

        a {
            color: $color-primary;
            text-decoration: underline;
        }
    }

    .previewCon {
        img {
            width: 100%;
            aspect-ratio: 706/438;
            object-fit: contain;
        }
    }

    .contact {
        text-align: center;

        a {
            color: $color-primary;
        }

        @include respond-to(mb) {
            text-align: left;
        }
    }
}
</style>
