<template>
	<div>
		<div class="summary-box" :class="modal.wrapClass" :style="modal.style">
			<div class="bps-container part5_page" v-for="(o, oi) in modal.outer" :key="oi">
				<div class="content pc_none mb_show" :style="modal.contentStyle" style="margin-left: 120px" v-if="oi == 0">
					<EditDiv tagName="h2" v-model:content="o.title.value" v-if="o.title"
									 @click="setModalType(o.title,modal.outer,'text')" />
				</div>
				<div class="part5" v-if="oi == 0">
					<pic class="pic" :src="o.img.value" v-if="o.img" :alt="o.img.alt" :title="o.img.alt"
							 @click="setModalType(o.img,modal.outer,'img')"/>
				</div>
				<div class="content pc_none mb_show" :style="modal.contentStyle" style="margin-left: 120px" v-if="oi == 0">
					<EditDiv class="des" v-for="(t,ti) in o.text" :key="ti" v-model:content="t.value" v-if="o.text"
									 @click="setModalType(o.text[ti],o.text,'text')" />
				</div>
				<div class="content mb_none" :style="modal.contentStyle" style="margin-left: 120px" v-if="oi == 0">
					<EditDiv tagName="h2" v-model:content="o.title.value" v-if="o.title"
									 @click="setModalType(o.title,modal.outer,'text')" />
					<EditDiv class="des" v-for="(t,ti) in o.text" :key="ti" v-model:content="t.value" v-if="o.text"
									 @click="setModalType(o.text[ti],o.text,'text')" />
				</div>
				<div class="content mb_none" :style="modal.contentStyle" v-else-if="oi == 1" style="margin-right: 120px;margin-top: 65px">
					<EditDiv tagName="h2" v-model:content="o.title.value" v-if="o.title"
									 @click="setModalType(o.title,modal.outer,'text')" />
					<EditDiv class="des" v-for="(t,ti) in o.text" :key="ti" v-model:content="t.value" v-if="o.text"
									 @click="setModalType(o.text[ti],o.text,'text')" />
					<div class="des conter" :hidden="!o.title && !o.subTitle" >
						<div class="item" v-for="(item,index) in o.subTitle" @click="setModalType(o.subTitle[index].title,o.subTitle,'text')">
							<span></span>{{item.title.value}}
						</div>
					</div>
				</div>
				<div class="content pc_none mb_show" :style="modal.contentStyle" style="margin-left: 120px" v-if="oi == 1">
					<EditDiv tagName="h2" v-model:content="o.title.value" v-if="o.title"
									 @click="setModalType(o.title,modal.outer,'text')" />
				</div>
				<div class="part5" v-if="oi == 1" style="margin-top: 65px">
					<pic class="pic" :src="o.img.value" v-if="o.img" :alt="o.img.alt" :title="o.img.alt"
							 @click="setModalType(o.img,modal.outer,'img')"/>
				</div>
				<div class="content pc_none mb_show" :style="modal.contentStyle" v-if="oi == 1">
					<EditDiv class="des" v-for="(t,ti) in o.text" :key="ti" v-model:content="t.value" v-if="o.text"
									 @click="setModalType(o.text[ti],o.text,'text')" />
					<div class="des conter" :hidden="!o.title && !o.subTitle" >
						<div class="item" v-for="(item,index) in o.subTitle" @click="setModalType(o.subTitle[index].title,o.subTitle,'text')">
							<span></span>{{item.title.value}}
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>

</template>

<script>
	export default {
		props: {
			data: {
				type: Object,
				default: {},
			}
		},
		data() {
			return {
				modal: {
					style: {},
					type: {index: 0, clickPosition: 'outer'},
					...this.data
				}
			};
		},
		watch: {
			modal: {
				handler(val) {
					if (process.env.isManage) this.$emit("update:data", val);
				},
				immediate: true,
				deep: true
			}
		},
		methods: {
			setModalType(target, targetArray, clickType, event, other) {
				this.$setModal(this, target, targetArray, clickType, event, other)
			}
		},
		mounted() {

		}
	};
</script>

<style lang="scss" scoped>
	.summary-box {
		padding: 70px 0;

		.bps-container {
			display: grid;
			align-items: center;
			position: relative;
			grid-template-columns: repeat(2, 1fr);
			grid-gap: 20px;
			.pic {
				border-radius: 10px;
			}

			.content {
				h1,
				h2 {
					font-size: 36px;
					text-align: left;
				}

				.des {
					margin: 30px 0;
					line-height: 24px;
					color: #666666;
				}
				.conter {
					display: grid;
					grid-template-columns: repeat(2, 1fr);
					grid-column-gap: 30px;
					grid-row-gap: 10px;
					font-size: 14px;
					font-weight: 400;
					color: #333;
					.item {
						span {
							display: inline-block;
							width: 8px;
							height: 8px;
							background: #939393;
							border-radius: 50%;
							margin-right: 20px;
						}
					}
				}
			}
		}
	}
	.pc_none {
		display: none;
	}
	.pc_show {
		display: block;
	}
	.home-part3 {
		background-color: #F8F8F8;

		.bps-container {
			position: relative;
			grid-template-columns: 1fr 1.2fr;
			grid-gap: 70px;

			.content {
				grid-row: 1/2;
			}
		}
	}

	@media screen and (max-width: $mb-width) {
		.summary-box {
			padding: 27px 0;
			background-color: #ffffff;

			.bps-container {
				display: grid;
				grid-template-columns: 1fr;
				align-items: center;
				grid-gap: 0px !important;
				.content {
					h2 {
						font-size: 21px;
						margin-bottom: 20px;
					}
					.conter {
						.item {
							span {
								margin-right: 10px !important;
							}
						}
					}
					.des {
						margin: 23px 0;
						font-size: 12px;
						grid-column-gap: 0px !important;
					}
				}
			}
		}
		.home-part3 {
			.bps-container {
				.content {
					grid-row: none;
				}
			}
		}
		.mb_none {
			display: none;
		}
		.mb_show {
			display: block;
		}
		.part5_page {
			grid-template-columns: repeat(1,1fr);
			.content {
				margin-left: 0 !important;
				margin-right: 0 !important;
			}
			.part5 {
				margin-top: 0 !important;
			}
		}
	}

</style>
