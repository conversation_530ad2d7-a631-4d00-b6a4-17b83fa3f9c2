<template>
	<div class="NeonSwiper" :style="{ width: width }">
		<div class="swiper neonSwiper" ref="neonSwiper">
			<div class="swiper-wrapper">
				<div class="swiper-slide" v-for="(item, index) in imgList" :key="item.id" v-if="item.previewImg || item.previewVideo || item.coverVideo">
					<div class="li-img" :class="{ hasBackground: isLuggagetags }">
						<template v-if="item.previewImg">
							<img :data-src="item.previewImg" class="swiper-lazy" :alt="item.imgAlt" :title="item.imgTitle" />
							<div class="swiper-lazy-preloader"></div>
						</template>
						<template v-else>
							<!-- <VideoPlayer :style="{ 'aspect-ratio': zoomAspectRatio }" class="swiper-no-swiping" :ref="'videoPlayer' + index" disabled-mouse :options="getVideoOptions(item, 1)"></VideoPlayer> -->
							<video class="self-lazy swiper-lazy" :src="item.previewVideo || item.coverVideo" :ref="'videoPlayer' + item.id" controls></video>
						</template>
						<div class="imgDetailInfo" v-show="isLuggagetags && (item.title || item.description)">
							<div class="textContent">
								<h2>{{ item.title }}</h2>
								<p>{{ item.description }}</p>
								<!-- <div class="btnGroup">
									<button primary class="quote" :alt="item.buttonAlt" :title="item.buttonTitle" @click="openQuote(item.jumpLink)">
										{{ item.buttonText }}
									</button>
								</div> -->
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="swiper-pagination swiper-pagination-neonSwiper"></div>
			<div class="swiper-button-prev" style="z-index: 999"></div>
			<!--左箭头。如果放置在swiper外面，需要自定义样式。-->
			<div class="swiper-button-next" style="z-index: 999"></div>
			<!--右箭头。如果放置在swiper外面，需要自定义样式。-->
		</div>
	</div>
</template>

<script>
import CustomImage from "@/components/CustomImage";
import { debounce, getFileSuffix } from "@/utils/utils";
import VideoPlayer from "@/components/HalfDesign/VideoPlayer.vue";
export default {
	name: "NeonSwiper",
	components: { CustomImage, VideoPlayer },
	props: {
		imgList: {
			type: Array,
			default: () => [],
		},
		width: {
			type: [String, Number],
			default: "50%",
		},
		isLuggagetags: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {
			neonSwiper: null,
			debounceInitSwiper: null,
			zoomAspectRatio: 4 / 3,
			observer: null,
		};
	},
	watch: {
		// imgList: {
		// 	handler() {
		// 		this.initSwiper();
		// 		this.swiperUpdate();
		// 		console.log("Swiper slides count:", this.imgList.length, "00000", this.neonSwiper.slides.length);
		// 	},
		// },
	},
	computed: {},
	methods: {
		initSwiper() {
			if (!this.imgList.length) return;
			let _this = this;
			this.neonSwiper = new Swiper(this.$refs.neonSwiper, {
				speed:this.isLuggagetags?0:300,
				slidesPerView: 1,
				observer: true,
				observerParents: true,
				watchSlidesVisibility: true, //防止不可点击
				loop: true,
				simulateTouch: true,
				lazy: {
					loadPrevNext: true,
					loadPrevNextAmount: 2,
					loadOnTransitionStart: true,
				},
				pagination: {
					el: ".swiper-pagination-neonSwiper",
					type: "fraction",
					currentClass: "my-pagination-current",
					totalClass: "my-pagination-total",
				},
				navigation: {
					nextEl: ".neonSwiper .swiper-button-next",
					prevEl: ".neonSwiper .swiper-button-prev",
				},
				on: {
					slideChangeTransitionEnd: function (swiper) {
						swiper.lazy.load();
						const previousSlide = swiper.slides[swiper.previousIndex];
						const currentSlide = swiper.slides[swiper.activeIndex];
						_this.pauseVideoInSlide(previousSlide);
						_this.playVideoInSlide(currentSlide);
					},
				},
			});
		},
		swiperUpdate() {
			this.$nextTick(() => {
				if (this.neonSwiper) this.neonSwiper.update();
			});
		},
		setSwiperTo(num, item) {
			this.$nextTick(() => {
				if (this.neonSwiper) {
					this.neonSwiper.slideToLoop(num);
					try {
						// if (item.previewVideo) {
						// 	this.zoomPic(item.previewVideo);
						// }
					} catch (error) {}
				}
			});
		},
		getVideoOptions(item, type) {
			if (type === 1) {
				return {
					autoplay: true,
					controls: true,
					muted: true,
					loop: true,
					fill: true,
					preload: "auto",
					poster: item.previewVideo,
					sources: [
						{
							src: item.previewVideo,
							type: "video/mp4",
						},
					],
				};
			} else if (type === 4) {
				return {
					autoplay: false,
					controls: false,
					muted: true,
					loop: true,
					fill: true,
					sources: [
						{
							src: path,
							type: "video/mp4",
						},
					],
				};
			}
		},
		async playVideoInSlide(slide) {
			if (!slide) return;
			const videoDom = slide.querySelector("video");
			if (!videoDom) {
				// console.warn("Video element not found in slide:", slide);
				return;
			}
			// 确保视频已加载
			await new Promise((resolve) => {
				if (videoDom.readyState >= 2) {
					resolve();
				} else {
					videoDom.addEventListener("canplay", resolve, { once: true });
				}
			});
			videoDom.currentTime = 0;
			videoDom.muted = true;
			videoDom.loop = true;
			videoDom.play().catch((err) => {
				console.error("Video play failed:", err);
			});
		},
		pauseVideoInSlide(slide) {
			if (!slide) return;
			const videoDom = slide.querySelector("video");
			if (videoDom) {
				videoDom.pause();
			}
		},
		getFileSuffix,
		async zoomPic(val) {
			let getVideoMsg = (url) => {
				return new Promise((resolve) => {
					let videoElement = document.createElement("video");
					videoElement.src = url;
					videoElement.addEventListener("loadedmetadata", function () {
						resolve({
							duration: videoElement.duration,
							height: videoElement.videoHeight,
							width: videoElement.videoWidth,
						});
					});
				});
			};
			let getImgInfo = (url) => {
				return new Promise((resolve) => {
					let img = new Image();
					img.src = url;
					img.onload = function () {
						resolve({
							height: img.width,
							width: img.height,
						});
					};
				});
			};
			if (this.getFileSuffix(val) === ".mp4") {
				let videoInfo = await getVideoMsg(val);
				this.zoomAspectRatio = videoInfo.width / videoInfo.height;
			} else {
				let msgInfo = await getImgInfo(val);
				this.zoomAspectRatio = msgInfo.width / msgInfo.height;
			}
		},
		callback(entries, instance) {
			entries.forEach((entry) => {
				if (entry.isIntersecting) {
					const element = entry.target;
					element.src = element.dataset.src;
					element.classList.remove("self-lazy");
					console.log("成功");
					instance.unobserve(element);
				}
			});
		},
		openQuote(url) {
			if (!url) return;
			this.$store.commit("setMask", {
				modal: "modalQuoteDialog",
				quoteUrl: url,
			});
			this.$emit("close");
		},
	},
	created() {
		this.debounceInitSwiper = debounce(this.initSwiper, 500);
	},
	mounted() {
		this.neonSwiper = null;
		this.initSwiper();
		this.observer = new IntersectionObserver(this.callback);
	},
};
</script>

<style scoped lang="scss">
.neonSwiper {
	width: 100%;
	@include respond-to(mb) {
		height: 100vh;
		display: flex;
		align-items: center;
	}

	.swiper-wrapper {
		width: 100%;
		box-sizing: border-box;
		display: flex;
		align-items: center;

		.swiper-slide {
			padding: 0 100px;
			width: fit-content;
			cursor: pointer;
			white-space: nowrap;
			text-align: center;
			user-select: none;

			img {
				max-width: 100%;
				max-height: 100%;
				object-fit: contain;
			}
			.li-img {
				max-height: 85vh;
				text-align: center;
				width: 100%;
				min-width: 10px;
				display: flex;
				align-items: center;
				justify-content: center;
				flex-direction: column;
				overflow: hidden;
				img,
				video {
					min-height: 0;
					max-height: 85vh;
					object-fit: contain;
				}
				&.hasBackground {
					background: #fff;
					.imgDetailInfo {
						flex-shrink: 0;
						width: 100%;
						.textContent {
							width: 100%;
							overflow: hidden;
							display: flex;
							align-items: center;
							justify-content: center;
							flex-direction: column;
							padding: 0 1rem;
							h2 {
								margin: 0;
								padding: 1.2rem 0 0.5rem;
								font-size: 1.13rem;
								word-break: break-word;
								white-space: normal;
							}

							p {
								padding-bottom: 1.2rem;
								word-break: break-word;
								white-space: normal;
							}

							.btnGroup {
								display: flex;
								justify-content: center;
								align-items: center;
								gap: 1rem;
								margin-top: 1.63rem;
								padding-bottom: 1.81rem;

								button {
									min-width: auto;
									font-size: 1rem;
									height: 2.5rem;
									padding: 0;
									background: linear-gradient(90deg, var(--color-bright), var(--btn-primary));
								}
								.quote {
									width: 15.63rem;
								}
							}
						}
					}
				}
				@include respond-to(mb) {
					max-height: 80vh;
					img,
					video {
						max-height: 80vh;
					}
				}
			}
		}
		@include respond-to(mb) {
			height: fit-content;
			.swiper-slide {
				padding: 0;
				width: 100%;
				margin-left: 0;
				font-size: 12px;
				padding-block: 6px;
				user-select: none;
				height: 100vh;
				display: flex;
				align-items: center;
				justify-content: center;
			}
		}
	}

	.swiper-pagination-neonSwiper {
		display: none;
		position: absolute;
		z-index: 999;
		top: 40px;
		left: 50%;
		transform: translateX(-50%);
		color: #fff;
		font-size: 16px;
		@include respond-to(mb) {
			display: block;
			top: 24px;
			height: 20px;
			pointer-events: none;
		}

		::v-deep .swiper-pagination-bullet {
			height: 21px;
			width: 21px;
			opacity: 1;
			background-color: #b7b7b7;
			margin: 0 6px;

			&.swiper-pagination-bullet-active {
				// background-image: linear-gradient(90deg, #20aeff, #b61ee8);
				background-color: $color-primary;
			}

			@include respond-to(mb) {
				width: 10px;
				height: 10px;
			}
		}
	}

	.swiper-button-prev,
	.swiper-button-next {
		display: block;
		width: 40px;
		height: 40px;
		background: transparent;
		opacity: 1;
		will-change: opacity;
		transform: translateY(-50%);
		border-radius: 50%;
		outline: none;
		@include respond-to(mb) {
			display: none;
		}
		&::before {
			content: "";
			position: absolute;
			inset: 0;
			border-radius: 50%;
			background: #cbcbcb;
			z-index: -1;
		}
		&:after {
			position: absolute;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
			color: #333;
			font-size: 14px;
			font-weight: 700;
		}
		&:active {
			&:before {
				// opacity: 1;
			}
			&:after {
				// color: #fff;
			}
		}
	}
}
</style>
