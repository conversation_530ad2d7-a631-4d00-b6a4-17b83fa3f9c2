<template>
    <div class="clipart">
        <img loading="lazy" :src="clipartImage" :title="clipartName" :alt="clipartName"
            @click="addImg(clipartImage, clipartId)" />
        <div class="list-item-btn" :style="{'--opacity': item.checked || 0 }" @click="clipartChecked">
            <div class="checkbox" :class="{'active': item.checked }">

            </div>
            <div class="btn" @click.stop>
                <div class="download" @click="download">
                    <b class="icon-xiazai"></b>
                </div>
                <div class="share" @click="share">
                    <b class="icon-a-icon-Sharezhuanhuan"></b>
                </div>
                <div class="remove" @click="remove">
                    <b class="icon-shanchu"></b>
                </div>
            </div>
        </div>

        <!-- 手机端收藏 -->
        <div class="lovebtnMB" v-show="item.checked === 0">
            <div :title="isCollection ? lang.cancelFavorite : lang.addFavorite" @click="addArtFavorite(item)" v-throttle>
                <b :class="isCollection ? 'icon-love' : 'icon-a-T-axzhuanhuan'"></b>
            </div>
        </div>
    </div>
</template>

<script>
import { recordReplaceClipart } from "@/api/newDzxt";

export default {
    // isCollection: 1收藏
    // checked: 1显示复选框,0隐藏复选框

    props: ['item', 'clipartImage', 'isCollection','clipartName','clipartId'],
    data() {
        return {

        }
    },
    computed: {
        lang() {
            return this.$store.getters.lang?.design;
        },
		isLogin() {
			return this.$store.getters.isLogin;
		}
    },
    methods: {
        addImg(src,property) {
            //记录元素使用次数
            if(property!=0) recordReplaceClipart({id:property})
            this.$emit('addImg',{
                src,
                property
            })
        },
        addArtFavorite(item) {
			if (!this.isLogin) {
				this.$store.commit('setLogin', 'login')
				return false;
			}
            this.$emit('addArtFavorite', item)
        },
        clipartChecked() {
            this.$emit('clipartChecked', this.item)
        },
        download(){
            this.$emit('download', this.item)
        },
        share(){
            this.$emit('share', this.item)
        },
        remove(){
            this.$emit('remove', this.item)
        }
    }
}
</script>

<style scoped lang="scss">
.clipart {
    aspect-ratio: 1;
    img {
        width: 100%;
        height: 100%;
        object-fit: contain;
    }

    .list-item-btn {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: flex-end;
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.25);
        transition: all 0.3s;
        padding: 10px;
        opacity: var(--opacity);

        .checkbox{
            height: 31px;
            width: 31px;
            border-radius: 50%;
            border: 2px solid #ED21D2;
            &.active{
                background-color: #ED21D2;
                position: relative;
                &::after{
                    content: '';
                    position: absolute;
                    left: 50%;
                    top: 45%;
                    width: 8px;
                    height: 15px;
                    border: 2px solid #fff;
                    border-top: none;
                    border-left: none;
                    transform: translate(-50%, -50%) rotate(45deg);
                }
            }
        }

        .btn {
            display: flex;

            &>div {
                display: flex;
                justify-content: center;
                align-items: center;
                margin: 0 4px;
                width: 24px;
                height: 36px;
                border-radius: 50%;
                border: none;
                cursor: pointer;

                b {
                    color: #ffffff;
                    font-size: 18px;
                }
            }
        }

        .btn-Text {
            margin-top: 10px;
            padding: 5px 10px;
            background: #FFFFFF;
            border-radius: 4px;
            font-size: 14px;
            border: 1px solid #666666;
        }


        &:hover {
            opacity: 1;
        }
    }

    .lovebtnMB {
        display: none;
    }
}

@include respond-to(mb) {
    .clipart {
        .list-item-btn {
            display: none;
        }

        .lovebtnMB {
            display: block;
            position: absolute;
            right: 5px;
            top: 5px;

            b {
                color: #cccccc;
            }

            b.icon-love {
                color: #ce3d3a;
            }
        }
    }
}
</style>
