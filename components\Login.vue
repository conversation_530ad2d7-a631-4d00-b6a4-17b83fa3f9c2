<template>
	<!--			登录弹窗-->
	<div class="content">
		<div pointer class="close-icon" @click="closeLogin">
			<svg t="1698635711458" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="38050" width="16" height="16">
				<path d="M868.29477597 933.875c-16.28861023 0-32.57722046-6.51544409-44.9565639-18.89478754L111.20018091 198.2813706C86.44149402 173.52268372 86.44149402 133.12693051 111.20018091 108.36824363c24.75868689-24.75868689 64.50289536-24.75868689 89.26158307 0l712.13803116 716.69884103c24.75868689 24.75868689 24.75868689 65.15444009 0 89.91312781-12.37934345 12.37934345-28.01640893 18.89478754-44.30501916 18.89478753z" p-id="38051"></path>
				<path d="M156.15674481 933.875a63.72104233 63.72104233 0 0 1-44.9565639-108.80791535l712.13803116-716.69884102c24.75868689-24.75868689 64.50289536-24.75868689 89.26158307 0s24.75868689 65.15444009 0 89.91312697l-712.13803117 716.69884186c-11.72779953 12.37934345-28.01640893 18.89478754-44.30501916 18.89478754z" p-id="38052"></path>
			</svg>
		</div>
		<div class="method-tab">
			<div class="sign-item" :class="{active:active===1}" @click="active=1">
				{{ isRegister ? lang.register : lang.login }}
			</div>
		</div>
		<div class="myForm">
			<!--		登录-->
			<form class="formWrap" id="loginForm" v-if="!isRegister" @submit.prevent>
				<div class="form-item">
					<label for="email"><span>*</span>{{lang.email}}:</label>
					<input type="text" name="email" v-model="loginForm.email">
				</div>
				<div class="form-item" v-if="active===1">
					<label for="password"><span>*</span>{{lang.password}}:</label>
					<div style="position: relative">
						<input :type="loginPwdType" name="password" autocomplete="current-pass" v-model="loginForm.password" style="width: 100%;">
						<div class="viewIcon">
							<b :class="loginPwdType==='password'?'icon-biyan':'icon-zhengyan'" @click="changePwdType('login')"></b>
						</div>
					</div>
				</div>
				<div class="form-item" v-else>
					<label for="code"><span>*</span>{{lang.code}}:</label>
					<div class="getCode">
						<input type="text" name="code">
						<p pointer class="codeBtn" :class="{ send: !loginForm.email || loginForm.sendTime }" @click="getLoginCode">
							{{ loginForm.sendTime ? loginForm.sendTime + "s" : lang.getCode }}</p>
					</div>
				</div>
				<div style="text-align: right">
					<div class="forgot" @click="closeLogin" v-if="active===1">
						{{lang.forgot}}
						<n-link tag="a" pointer class="forgot-tips" to="/user/account/reset-password">{{lang.pwd}}</n-link>
					</div>
				</div>
				<div class="btnGroup">
					<button @click="login(loginForm)">{{lang.login}}</button>
					<button type="button" @click="isRegister = !isRegister">{{lang.register}}</button>
				</div>
			</form>
			<!--		注册-->
			<form class="formWrap" id="registerForm" v-else @submit.prevent>
				<div class="form-item">
					<label for="email"><span>*</span>{{lang.email}}:</label>
					<input type="text" name="email" v-model="registerForm.email">
				</div>
				<div class="form-item" v-if="active===1">
					<label for="password"><span>*</span>{{lang.password}}:</label>
					<div style="position: relative">
						<input :type="registerPwdType" name="password" v-model="registerForm.password" style="width: 100%;">
						<div class="viewIcon">
							<b :class="registerPwdType==='password'?'icon-biyan':'icon-zhengyan'" @click="changePwdType('register')"></b>
						</div>
					</div>
				</div>
				<div class="name-box">
					<div class="form-item">
						<label for="firstName"><span>*</span>{{lang.fName}}:</label>
						<input type="text" name="firstName" v-model="registerForm.firstName">
					</div>
					<div class="form-item" v-if="active===1">
						<label for="lastName"><span>*</span>{{lang.lName}}:</label>
						<input type="text" name="lastName" v-model="registerForm.lastName">
					</div>
				</div>
				<div class="form-item">
					<label for="gender">{{lang.gender}}:</label>
					<div class="gender-box">
						<div @click="registerForm.gender=1" :class="registerForm.gender==1?'gender-active':''"><span></span><label>{{lang.male}}</label></div>
						<div @click="registerForm.gender=0" :class="registerForm.gender==0?'gender-active':''"><span></span><label>{{lang.female}}</label></div>
					</div>
				</div>
				<template v-if="$store.state.proType==0">
					<div class="form-item telephone-box">
						<b class="icon-duanxintongzhi-weiqianyue"></b>
						<div flex class="code-box">
							<span class="add">+</span>
							<input type="number" v-model="areaCode" @focus="isShow=true" @blur="phoneBlur"/>
						</div>
						<input type="number" v-model="telePhone" :placeholder="lang.telephonePlace" @focus="isShow=true" @blur="phoneBlur">
					</div>
					<div class="reminders" v-show="isShow">
						<div class="check" flex><div @click="check" class="right-check" :class="isRight?'right-checked':''"><span>√</span></div><span>{{ lang.reminders }}</span></div>
						<div class="check-text">{{ checkText }} {{ webSite }}. {{ lang.checkText1 }}</div>
					</div>
				</template>
				<div class="btnGroup">
					<button @click="register(registerForm)">{{lang.create}}</button>
					<button type="button" @click="isRegister = !isRegister">{{lang.login}}</button>
				</div>
			</form>
			<!--		第三方-->
			<div class="third-sign-wrap" v-if="hasFirebaseConfig">
				<div class="service">{{lang.or}} {{ isRegister ? lang.re : lang.lo }} {{lang.se}}</div>
				<div class="third-sign">
<!--					<div class="facebook-login-button">-->
<!--						<img src="https://static-oss.gs-souvenir.com/web/quoteManage/20231114/facebook_2048KWyNwd.png" alt="facebook">-->
<!--						<span>{{lang.logF}}</span>-->
<!--					</div>-->
					<div class="google-login-button">
						<img src="https://static-oss.gs-souvenir.com/web/quoteManage/20231114/logo-google_2048CTsNm4.png" alt="google">
						<span>{{lang.logG}}</span>
					</div>
				</div>
			</div>
			<div class="tips" v-if="hasFirebaseConfig">
				{{lang.by}} {{ isRegister ? lang.ce : lang.si }}. {{lang.agree}} {{ $store.state.proName }} <a href="/info/privacy-policy">{{lang.pp}}</a> and <a href="/info/terms-and-conditions">{{lang.tc}}</a>.
			</div>
		</div>
	</div>
</template>

<script>
import { mergeShoppingCart} from "@/api/stylistCart.js";
import {login, cartMerge, register} from "@/api/web.js";
import initFirebase from "@/assets/js/firebase"
export default {
	name: "login",
	data() {
		return {
			loginPwdType: 'password',
			registerPwdType: 'password',
			// login
			loginByCode: false,
			loginForm: {
				email: "",
				password: "",
				sendTime: 0
			},
			// register
			registerForm: {
				email: "",
				password: "",
				firstName: "",
				lastName: "",
				gender:1
			},
			countryList: [],
			identifyCode: "",
			// forgot
			forgotForm: {sendTime: 0},
            isRegister: false,
            registerLoad: false,
			active: 1,
            loginLoad: false,
			//短信营销新增
			isShow:false,
			isRight:0,
			telePhone:"",
			areaCode:""
		};
	},
	computed: {
		lang() {
			return this.$store.getters.lang.login || {};
		},
		proType() {
			return this.$store.state.proType
		},
		hasFirebaseConfig(){
			return this.$store.getters.projectComment?.firebase
		},
		webSite(){
            return location.hostname.replace(/www./, '');
        },
		checkText(){
			return this.lang.checkText.replace(/'/g,'"')
		}
	},
	watch: {
		showLogin(val) {
			// 打开注册页面时获取地区列表
			if (val == "register") {
				this.changeCode();
				if (!this.countryList.length)
					this.$axios.$post("api/config/getCountry").then(res => {
						this.countryList = res;
						this.registerForm.country = res.find(item => item.is_default == 1).id;
					});
			}
		},
		'$store.state.loginEmail': {
			handler(val) {
				this.loginForm.email = val;
			}
		}
	},
	methods: {
		check(){
			if(!this.isRight){
				if(this.checkPhone()){
					this.isRight=1
				}
			}else {
				this.isRight=0
			}
		},
		checkPhone(){
			if (this.telePhone == '') {
				if(this.isRight==0) this.$toast.error(this.lang.phoneNumberRequired);
				return false;
			} else if (this.areaCode=='') {
				if(this.isRight==0) this.$toast.error(this.lang.phoneCodeRequired)
				return false;
			} else if (this.telePhone.length < 7) {
				if(this.isRight==0) this.$toast.error(this.lang.phoneNumberLength)
				return false;
			} else {
				return true;
			}
		},
		phoneBlur(){
			if(this.isRight){
				if(this.checkPhone()){
					this.isRight=1
				}else {
					this.isRight=0;
				}
			}
		},
		closeLogin() {
			this.loginForm = {
				email: "",
				password: "",
				sendTime: 0
			}
			this.registerForm = {
				email: "",
				password: "",
				firstName: "",
				lastName: ""
			}
			this.$store.commit('setLogin', false);
		},
		changePwdType(type) {
			if (type === 'login') {
				this.loginPwdType = this.loginPwdType === 'password' ? 'text' : 'password'
			} else {
				this.registerPwdType = this.registerPwdType === 'password' ? 'text' : 'password'
			}
		},
		// login
		login(form) {
			if (!form.email) return this.$toast.error(this.lang.emailInput);
            if(this.loginLoad) return false;
            this.loginLoad = true;
			login(form).then(async res => {
				let data = res.data;
				await this.$store.commit('setUserInfo', data);
				this.closeLogin();
				await cartMerge({
					uuid: this.$store.state.userUUID
				});
				//设计师购物车
				await mergeShoppingCart({
					cartUuid: this.$store.state.userUUID
				})
			}).then(() => {
				this.$store.dispatch('updateHeadFootPages')
			}).finally(()=>{
                this.loginLoad = false;
            })
		},
		getLoginCode() {
			if (!this.loginForm.email || this.loginForm.sendTime) return;
			this.$axios
				.$post("api/User/sendLoginCode", {
					email: this.loginForm.email
				})
				.then(() => {
					this.loginForm.sendTime = 60;
					let timer = setInterval(() => {
						this.loginForm.sendTime -= 1;
						if (this.loginForm.sendTime == 0) clearInterval(timer);
					}, 1000);
				});
		},
		changeCode() {
			this.identifyCode = "";
			for (let i = 0; i < 4; i++) {
				this.identifyCode += Math.floor(Math.random() * 10); // 验证码范围：0-9
			}
		},
		register() {
			if(this.$store.state.proType==0){
				this.registerForm.telephone=this.areaCode+'-'+this.telePhone;
				this.registerForm.isSmsSubscriptions=this.isRight;
			}
			if (!this.registerForm.firstName || !this.registerForm.lastName || !this.registerForm.password || !this.registerForm.email) return this.$toast.error(this.lang.verifyForm);
            let reg = /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/;
			if (!reg.test(this.registerForm.email)) return this.$toast.error(this.lang.verifyEmail);
			// if (this.registerForm.password != this.registerForm.confirmPwd) return this.$toast.error(this.lang.verifyPwd);
			// if (this.registerForm.code != this.identifyCode) return this.$toast.error(this.lang.verifyCode);
            if(this.registerLoad) return false;
            this.registerLoad = true
            register(this.registerForm).then(() => {
				this.login(this.registerForm)
			}).finally(()=>{
                this.registerLoad = false
            })
		}
	},
	mounted() {
		initFirebase.apply(this);
	}
};
</script>

<style lang="scss" scoped>
.content {
	width: 550px;
	min-width: 550px;
	border-radius: 8px;
	background: white;
	position: relative;

	.myForm {
		padding: 20px;
		max-height:90vh;
		overflow-y:auto;
	}

	.method-tab {
		overflow: hidden;
		display: flex;
		border-top-left-radius: 8px;
		border-top-right-radius: 8px;
		padding: 0 20px;
		border-bottom: 1px solid #CCCCCC;

		& > div {
			display: flex;
			justify-content: flex-start;
			align-items: center;
			flex: 1;
			height: 3.125em;
			background-color: #F2F2F2;
			cursor: pointer;

			&.active {
				font-weight: 700;
				background-color: #ffffff;
			}
		}
	}

	.formWrap {

		.forgot {
			display: inline-block;

			a {
				color: $color-primary;
				text-decoration: underline;

				&:hover {
					opacity: .9;
				}
			}
		}

		.btnGroup {
			display: flex;
			margin: 20px 0;

			button {
				flex: 1;
				margin: 0 5px;
				border: 1px solid #ccc;
				border-radius: 4px;
				height: 40px;

				&:hover {
					opacity: .9;
				}

				&:first-child {
					background-color: $color-primary;
					color: #ffffff;
					border-color: $color-primary;
				}
			}
		}

		.form-item {
			position: relative;
			display: flex;
			flex-direction: column;
			margin-bottom: 10px;

			.viewIcon {
				position: absolute;
				right: 10px;
				top: 50%;
				transform: translateY(-50%);
				cursor: pointer;
				font-size: 20px;
			}

			.getCode {
				display: flex;
				justify-content: space-between;
				align-items: center;

				input {
					flex: 1;
					margin-right: 20px;
				}

				.codeBtn {
					border: 1px solid #CCCCCC;
					padding: 8px 10px;
					border-radius: 20px;
					background-color: #D9D9D9;
				}

				.codeBtn.send {
					cursor: not-allowed;
				}
			}

			label {
				margin-bottom: 5px;

				span {
					color: red;
				}
			}

			input {
				border: 1px solid #CCCCCC;
				padding: 8px 10px;
				border-radius: 20px;
				background-color: #F7F9FA;
			}

			.gender-box {
				font-size: calc(1em - 2px);
				display: flex;
				align-items: center;
				column-gap:2.78em;
				>div {
					display: flex;
					align-items: center;
					column-gap: 0.64em;
					span {
						display:flex;
						justify-content:center;
						align-items:center;
						width: 20px;
						height:20px;
						border-radius: 10px;
						border: 1px solid #CCCCCC;
					}
					span::before {
						content:"";
						width:8px;
						height:8px;
						border-radius:4px;
					}
					label {
						margin-bottom:0
					}
				}
				>.gender-active {
					span {
						border-color:$color-primary;
					}
					span::before {
						background: $color-primary;
					}
				}
			}
		}
		.telephone-box {
			display:flex;
			flex-direction:row;
			align-items:center;
			column-gap:8px;
			margin-bottom:-0.4em;
			>b {
				margin-left:-0.2em;
				font-size:2.8em;
				color:#808080;
			}
			.code-box {
				position:relative;
				input {
					padding-left:2em;
					width:6.375em;
				}
				.add {
					position:absolute;
					left:.7em;
					top:50%;
					transform: translateY(-50%);
					padding:0 2px 2px;
					font-size: 1.3em;
					color:#999;
				}
			}
			>input {
				width:100%;
			}
			input::placeholder {
				font-size:calc(1em - 2px)
			}
		}
		.reminders {
			margin-bottom: 1em;
			margin-top: 0.5em;
			padding: .6em 1em .6em .6em;
			background: #FFF1EA;
			.check {
				align-items: center;
				column-gap: 0.5em;
				margin-bottom:6px;
				.right-check {
					width: 1.2em;
					height: 1.2em;
					text-align: center;
					line-height: 1.2em;
					font-size: 1.1em;
					color: transparent;
					border-radius: 3px;
					border: 1px solid #ED9072;
					cursor: pointer;
				}
				.right-checked {
					color:#fff;
					background: #ED9072;
				}
			}
			.check-text {
				font-size:calc(1em - 2px);
			}
		}
		.name-box {
			display:flex;
			justify-content:space-between;
			>div {
				width:48%;
			}
		}
	}
}

.third-sign-wrap {
	background-color: #F2F2F2;
	padding: 20px;

	.third-sign {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;

		.google-login-button,
		.facebook-login-button {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 100%;
			height: 40px;
			background: #3B5998;
			padding: 8px 25px;
			margin: 5px 0;
			border-radius: 20px;
			cursor: pointer;
			font-size: 14px;
			color: #ffffff;

			&:hover {
				opacity: .9;
			}
		}

		.google-login-button {
			background-color: #85A3EF;
		}

		img {
			width: 20px;
			height: 20px;
			margin-right: 5px;
			object-fit: contain;
		}
	}

	.service {
		justify-content: center;
		display: flex;
		align-items: center;
		margin-bottom: 10px;
		font-size: 14px;
	}
}

.tips {
	margin-top: 15px;
	font-size: 14px;
	text-align: center;

	a {
		color: $color-primary;
		text-decoration: underline;

		&:hover {
			opacity: .9;
		}
	}
}

.close-icon {
	display: flex;
	justify-content: center;
	align-items: center;
	position: absolute;
	top: 0;
	right: 0;
	border-radius: 50%;
	width: 40px;
	height: 40px;
	transform: translate(50%, -50%);
	background-color: #ffffff;
	box-shadow: 1px 2px 20px 0 rgba(0, 0, 0, .2);

    @include respond-to(mb){
        transform: translate(0%, -0%);
        background-color: transparent;
        border-radius: 0;
        box-shadow: none;
    }
}

@media screen and (max-device-width: $pad-width) {
	.content {
		transform: scale(1.2);
	}
}

@media screen and (max-device-width: $mb-width) {
	.content {
		min-width: 95%;
		width: 95%;
		transform: scale(0.95);
	}
	.third-sign-wrap {
		padding-top:10px;
		padding-bottom:10px;
		.service {
			font-size:1em;
			margin-bottom:5px;
		}
	}
	.tips {
		font-size:1em;
	}
	.content .formWrap .btnGroup {
		margin:1.2em 0;
	}
	.content .myForm {
		padding:15px 20px;
	}
	.close-icon {
		width:32px;
		height:32px;
	}
}
</style>
