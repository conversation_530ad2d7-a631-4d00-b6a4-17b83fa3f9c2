/***
 * baseUrl:接口地址
 * domain:cookie域
 * redirectWebUrl:管理后台登录过期重定向地址
 * isManage:是否是后台管理系统
 * tokenKey:token存储的键名
 * entry: 乾坤路由前缀
 * imgServer: 图片资源前缀常量
 * interComConfig 聊天配置
 */

const interComConfig = {
	app_id: "n61ou70j",
	secret: "Uv3DXMTp4NUhECFwe4_ObfnN6kAAoFfnOcUMw0Mk"
}

module.exports = {
	dev_web: {
		baseUrl: "https://products-api-dev.gs-souvenir.com",
		domain: "/",
		redirectWebUrl: "/",
		entry: "https://o2o-api-dev.gs-souvenir.com/user",
		isManage: false,
		tokenKey: "token",
		imgServer: "https://oss-static-cn.liyi.co/web/",
		...interComConfig
	},
	beta_web: {
		baseUrl: "https://products-api-dev.gs-souvenir.com",
		domain: ".a1gifts.biz",
		entry: "https://o2o-api-dev.gs-souvenir.com/user",
		redirectWebUrl: "https://www.a1gifts.biz",
		isManage: false,
		tokenKey: "token",
		imgServer: "https://oss-static-cn.liyi.co/web/",
		...interComConfig
	},
	prod_web: {
		baseUrl: "https://products-api-o2o-prod.gs-souvenir.com",
		domain: ".o2o.co",
		entry: "https://o2o-api-prod.gs-souvenir.com/user",
		redirectWebUrl: "https://www.o2o.co",
		isManage: false,
		tokenKey: "token",
		imgServer: "https://static-oss.gs-souvenir.com/web/",
		...interComConfig
	},

	dev_manage: {
		baseUrl: "https://products-api-dev.gs-souvenir.com",
		domain: "/",
		redirectWebUrl: "/",
		isManage: true,
		tokenKey: "manageToken",
		imgServer: "https://oss-static-cn.liyi.co/web/",
		...interComConfig
	},
	beta_manage: {
		baseUrl: "https://products-api-dev.gs-souvenir.com",
		domain: ".a1gifts.biz",
		redirectWebUrl: "https://www.a1gifts.biz",
		isManage: true,
		tokenKey: "manageToken",
		imgServer: "https://oss-static-cn.liyi.co/web/",
		...interComConfig
	},
	prod_manage: {
		baseUrl: "https://products-api-o2o-prod.gs-souvenir.com",
		domain: ".o2o.co",
		redirectWebUrl: "https://www.o2o.co",
		isManage: true,
		tokenKey: "manageToken",
		imgServer: "https://static-oss.gs-souvenir.com/web/",
		...interComConfig
	},

	enamel: {
		baseUrl: "https://products-api-dev.gs-souvenir.com",
		domain: "/",
		entry: "https://o2o-api-dev.gs-souvenir.com/user",
		redirectWebUrl: "/",
		isManage: false,
		tokenKey: "token",
		...interComConfig
	},
};
