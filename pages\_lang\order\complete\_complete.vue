<template>
    <div class="complete">
        <div class="success same" v-if="orderInfo.paymentStatus == 2 || orderInfo.paymentStatus == 0">
            <div>
                <div>
                    <img class="img" :src="successImg" alt="payment successful" />
                </div>
                <div class="info1">{{ lang.success }}</div>
                <div class="info2">{{ lang.for }} {{ $store.state.orderIdentification + orderInfo.oid }}</div>
                <div class="info3">
                    {{ lang.successfully }}
                </div>
                <div class="btn" v-show="orderInfo.email === this.$store.state.userInfo.email">
                    <a href="javaScript:;" class="type2" @click="routerPushFun('viewMore')">{{ lang.viewMore }}</a>
                </div>
            </div>
        </div>
        <!-- 5 Pc-D经销商图稿-直接下单付款失败或是取消付款的界面 -->
        <div class="error same" v-else-if="orderInfo.paymentStatus == 1">
            <div>
                <div>
                    <img class="img" :src="errorImg" alt="payment failed" />
                </div>
                <div class="info1">{{ lang.failed }}</div>
                <div class="info2">{{ lang.for }} {{ orderInfo.oid }}</div>
                <div class="info3">
                    {{ lang.problem }}
                </div>
                <div class="btn">
                    <a href="javaScript:;" class="type1" @click="routerPushFun('tryAgain')">{{ lang.again }}</a>
                    <a href="javaScript:;" class="type2" @click="routerPushFun('viewMore')">{{ lang.viewDetail }}</a>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { getOrderNotLogin, getOrderInfo } from "@/api/web";
import {getTrackTransactionHtml} from "@/utils/criteoConfig";

export default {
    data() {
        return {
            successImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20220922/20220922aCAi8YwJ.png",
            successImg2: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20220922/20220922bBwMsmXF.png",
            errorImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20220922/20220922AKKJTx8s.png",
            errorImg2: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20220922/20220922DPAMK5k5.png",
            orderInfo: {},
            orderId: null,
            orderDetail: "",
        };
    },
    computed: {
        lang() {
            return this.$store.getters.lang.complete || {};
        },
        isLogin() {
            return this.$store.getters.isLogin;
        },
        userIp() {
            return this.$store.state.userIp;
        },
        advertConfig() {
            return this.$store.state.advertConfig;
        },
    },
    methods: {
        getMyOrderInfo(data) {
            return new Promise((resolve) => {
                if (this.isLogin) {
                    getOrderInfo(data).then((res) => {
                        resolve(res);
                    });
                } else {
                    getOrderNotLogin(data).then((res) => {
                        resolve(res);
                    });
                }
            });
        },
        routerPushFun(type) {
            if (this.isLogin) {
                window.location.href = `/user/account/orders/summary?orderId=${this.orderId}`;
            } else {
                this.$toast.show("please log in first");
                this.$store.commit("setLogin", "login");
            }
        },
        getOrderInfo() {
            let postData = {
                oid: this.orderId,
                getOrderInfo: true,
                getUserInfo: true,
                getProductInfo: true,
                getAddressInfo: true,
            };
            this.getMyOrderInfo(postData).then((res) => {
                let findChildOrder;
                //判断是否存在子订单，找到未支付的子订单
                if (res.data.childOrder && res.data.childOrder.length) {
                    findChildOrder = res.data.childOrder.find((item) => {
                        return item.paymentStatus == 1;
                    });
                }
                //如果未支付的子订单存在
                if (findChildOrder) {
                    this.orderInfo = findChildOrder;
                } else {
                    this.orderInfo = res.data;
                }
                this.$nextTick(() => {
                    //转化
                    if (this.orderInfo.paymentStatus == 2 || this.orderInfo.paymentStatus == 0) {
                        try {
                            if (window.gtag && this.advertConfig.googleConfig) {
                                let orderPrice = this.orderInfo.orderPrice;
                                //如果在黑名单金额设置为0.01
                                if (require("@/assets/json/blackList.json").includes(this.userIp)) orderPrice = 0.01;

                                //网络增强型转化
                                gtag('set', 'user_data', {
                                    "email": this.orderInfo.email,
                                    "phone_number": this.orderInfo.shippingPhoneNumber
                                })
                                let googlePurchaseConfig = this.$store.getters.getAdvertisementConfig("googlePurchaseConfig");
                                gtag("event", "conversion", {
                                    send_to: googlePurchaseConfig.value,
                                    currency: "USD",
                                    value: orderPrice,
                                    transaction_id: this.orderInfo.oid,
                                });
                                gtag("event", "purchase", {
                                    currency: "USD",
                                    value: orderPrice,
                                    transaction_id: this.orderInfo.oid,
                                });

                                // 广告
                                if (this.$cookies.get("fbclid") && window.fbq) {
                                    fbq("track", "Purchase", {
                                        currency: "USD",
                                        value: orderPrice,
                                    }, { eventID: this.orderInfo.oid });
                                }
                                if (this.$cookies.get("rdt_cid") && window.rdt) {
                                    rdt("track", "Purchase", {
                                        currency: "USD",
                                        itemCount: 1,
                                        transactionId: this.orderInfo.oid,
                                        value: orderPrice,
                                    });
                                }
                            }
                            let criteoConfig = this.$store.getters.getAdvertisementConfig("criteoConfig");
                            if (criteoConfig) {
                                //Criteo Sales Tag
                                let account = criteoConfig.account;
                                let email = this.orderInfo.email;
                                let zipcode = this.orderInfo.shippingZipCode;
                                let script = window.document.createElement("script");
                                let productItem = [];
                                let projectComment = this.$store.getters.projectComment;
                                //报价的评论等级(0 父级 1 子级)
                                let quote = projectComment.quoteCommentLevel == 0 ? "parent" : "child";
                                //半定制的评论等级(0 父级 1 子级)
                                let semi = projectComment.semiCommentLevel == 0 ? "parent" : "child";
                                this.orderInfo.ordersProducts.forEach((item) => {
                                    let id;
                                    if (!item.platformProduct) {
                                        return;
                                    }
                                    if (item.buyType == 7) {
                                        id = item.platformProduct[quote].id;
                                    } else if (item.buyType == 9) {
                                        id = item.platformProduct[semi].id;
                                    }
                                    productItem.push({
                                        id: id,
                                        price: item.totalPrice / (item.quantity || 1),
                                        quantity: item.quantity || 1,
                                    });
                                });
                                function hasCommonItem(idList, productItemIdList) {
                                    return productItemIdList.some(item => idList.includes(item));
                                }
                                //判断订单产品是否存在 idList 中，如果存在，才触发 criteo 跟踪事件
                                // let idList = [4,300,671,472,589,469,282,561,638,692,644,470,8,268,67,524,7];
                                // let productItemIdList = productItem.filter(item=>item.id);
                                // const hasMatch = hasCommonItem(idList, productItemIdList);
                                // if(hasMatch){
                                    script.innerHTML = getTrackTransactionHtml({
                                        account,
                                        email,
                                        zipcode,
                                        id:this.orderInfo.oid,
                                        productItem
                                    })
                                    script.type = "text/javascript";
                                    document.getElementsByTagName("head")[0].appendChild(script);
                                // }
                            }
                            //lintrk
                            let lintrkConfig = this.$store.getters.getAdvertisementConfig("lintrkConfig");
                            if (lintrkConfig) {
                                window.lintrk("setUserData", { email: this.orderInfo.email })
                                window.lintrk('track', {
                                    conversion_id: lintrkConfig.purchases_conversion_id,
                                    value: this.orderInfo.orderPrice, // 设置转化价值
                                    custom_data: {
                                        id: this.orderInfo.oid,
                                        email: this.orderInfo.email
                                    }
                                });
                            }
                            if(window.ttq){
                                let contents = [];
                                let projectComment = this.$store.getters.projectComment;
                                //报价的评论等级(0 父级 1 子级)
                                let quote = projectComment.quoteCommentLevel == 0 ? "parent" : "child";
                                this.orderInfo.ordersProducts.forEach((item) => {
                                    let id;
                                    if (!item.platformProduct) {
                                        return;
                                    }
                                    if (item.buyType == 7) {
                                        id = item.platformProduct[quote].id;
                                    } else if (item.buyType == 9) {
                                        id = item.platformProduct[semi].id;
                                    }
                                    contents.push({
                                        content_id: String(id),
                                    });
                                });
                                ttq.track('CompletePayment', {
                                    "contents": contents,
                                    "value": Number(this.orderInfo.orderPrice), // number. Value of the order or items sold. Example: 100.
                                    "currency": "USD" // string. The 4217 currency code. Example: "USD".
                                },{ event_id : this.orderInfo.oid});
                            }
                            if(window.twq){
                                // Insert Twitter Event ID
                                twq('event', 'tw-p6azw-p6baw', {
                                    value: Number(this.orderInfo.orderPrice), // use this to pass the value of the conversion (e.g. 5.00)
                                    currency: "USD", // use this to pass the currency of the conversion with an ISO 4217 code (e.g. ‘USD’)
                                    conversion_id: this.orderInfo.oid, // use this to pass a unique ID for the conversion event for deduplication (e.g. order id '1a2b3c')
                                    email_address: this.orderInfo.email // use this to pass a user’s email address
                                });
                            }
                        } catch (e) {
                            console.log(e);
                        }
                    }
                    //评论
                    this.orderDetail = res.data;
                    //获取评论配置信息
                    this.proType = this.$store.getters.projectComment;
                    if (!this.orderDetail) {
                        return;
                    }
                    //拦截不是自营网站的
                    if (this.proType.proType != 0) {
                        return false;
                    }
                    if (this.orderDetail.paymentStatus != 2) {
                        return false;
                    }
                    if (this.proType.enableComment != 1) {
                        return false;
                    }
                    this.CommentInfo = JSON.parse(this.proType["shopperCommentInfo"]);
                    this.proData = this.getProduct("shopper");
                    //开启shopper 评论
                    this.openShopper();
                });
            });
        },
        //shopper 评论页面
        openShopper() {
            //这个是shopper的评论配置
            window.sa_values = {
                site: this.CommentInfo.site,
                token: this.CommentInfo.token,
                orderid: this.orderDetail.oid,
                name: this.orderDetail.shippingFirstName + " " + this.orderDetail.shippingLastName,
                email: this.orderDetail.email,
                country: this.orderDetail.shippingCountry,
                state: this.orderDetail.shippingState,
                products: this.proData,
            };
            var d = new Date();
            if (d.getTime() - 172800000 > 1477399567000) {
                this.saLoadScript(`//www.shopperapproved.com/thankyou/rate/${this.CommentInfo.site}.js`);
            } else {
                this.saLoadScript(`//direct.shopperapproved.com/thankyou/rate/${this.CommentInfo.site}.js?d=` + d.getTime());
            }
        },
        //引入shopper 的外部脚本
        saLoadScript(src) {
            var js = window.document.createElement("script");
            js.src = src;
            js.type = "text/javascript";
            document.getElementsByTagName("head")[0].appendChild(js);
        },
        // 获取shopper或者google 用于评论的订单信息
        getProduct(name) {
            //报价的评论等级(0 父级 1 子级)
            let quote = this.proType.quoteCommentLevel == 0 ? "parent" : "child";
            //半定制的评论等级(0 父级 1 子级)
            let semi = this.proType.semiCommentLevel == 0 ? "parent" : "child";
            let product;
            if (name == "shopper") {
                product = {};
                this.orderDetail.ordersProducts.map((item) => {
                    if (!item.platformProduct || Object.keys(item.platformProduct).length == 0) {
                        return;
                    }
                    if (item.buyType == 7) {
                        product[item.platformProduct[quote].id] = item.platformProduct[quote].name;
                    } else if (item.buyType == 9) {
                        product[item.platformProduct[semi].id] = item.platformProduct[semi].name;
                    }
                });
            }
            return product;
        },
    },
    async mounted() {
        this.orderId = this.$route.params.complete;
        this.getOrderInfo();
    },
};
</script>

<style lang="scss" scoped>
.complete {
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    padding: 50px 1vmax;

    * {
        font-family: Roboto;
    }

    .img {
        height: 150px;
        width: 150px;
        object-fit: contain;
    }

    .same {
        max-width: 650px;
        text-align: center;
        margin-top: 30px;

        .info1 {
            font-size: 18px;
            font-weight: 400;
            color: #333333;
            line-height: 24px;
            margin-top: 30px;
        }

        .info2 {
            font-size: 12px;
            font-weight: 400;
            color: #1a73e8;
            line-height: 24px;
            margin-top: 15px;
        }

        .info3 {
            font-size: 14px;
            font-weight: 400;
            color: #666666;
            margin-top: 20px;

            .confirm-button {
                color: #1a73e8;
            }
        }

        .btn {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-top: 40px;

            a {
                width: 288px;
                height: 40px;

                border-radius: 10px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 14px;
                font-weight: 400;
                color: #ffffff;
                margin: 5px 0;
            }

            .type1 {
                background: #0ad2b9;
            }

            .type2 {
                background: #1a73e8;
            }
        }
    }

    .order {
        background: #fafafa;
        border-radius: 10px;
        padding: 20px 20px 30px 20px;
        margin-top: 20px;

        .title {
            font-size: 16px;
            font-weight: bold;
            color: #333333;
            line-height: 24px;
            text-align: left;
        }

        .order-content {
            display: grid;
            grid-template-columns: 110px 1fr;
            column-gap: 30px;
            row-gap: 5px;
            margin-top: 15px;

            >div {
                text-align: left;
            }

            >div:nth-child(2n + 1) {
                font-size: 14px;
                font-weight: 400;
                color: #666666;
                line-height: 24px;
            }

            >div:nth-child(2n) {
                font-size: 14px;
                font-weight: 400;
                color: #333333;
                line-height: 24px;
            }
        }
    }
}
</style>