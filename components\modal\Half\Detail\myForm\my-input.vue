<template>
  <div class="myInput" :class="{ 'error--text': notValidate, hasPrefix: prefix || hasSlotPrefix, hasSuffix: suffix || hasSlotSuffix }">
    <label>
      <span class="textLabel" @click.stop v-show="textLabel">{{ textLabel }}</span>
      <input :type="type" :ref="oneProp" :placeholder="placeholder" :data-required="requireIcon" :value="value" @input="onInput" v-bind="$attrs" v-on="$listeners" />
	  <slot></slot>
    </label>
    <span class="my-input__prefix slotIcon" :style="slotIconStyle" v-show="prefix || hasSlotPrefix">
      <slot name="prefix">
        <b class="my-input__icon iconfont" :class="prefix"></b>
      </slot>
    </span>
    <span class="my-input__suffix slotIcon" :style="slotIconStyle" v-show="suffix || hasSlotSuffix">
      <slot name="suffix">
        <b class="my-input__icon iconfont" :class="suffix"></b>
      </slot>
    </span>
    <div class="textField" v-show="!noErrorText||notValidate">
      <div class="vaildate-message" v-show="notValidate">
        {{ vaildateText }}
      </div>
    </div>
  </div>
</template>

<script>
import { getRandomString } from '@/utils/utils'
export default {
  name: 'myInput',
  components: {},
  props: {
    value: {
      type: [String, Number]
    },
    prop: {
      type: String,
      default: ''
    },
    requireIcon: {
      type: String,
      default: ''
    },
    prefix: {
      type: String,
      default: ''
    },
    suffix: {
      type: String,
      default: ''
    },
    slotIconStyle: {
      type: [Object, String],
      default: ''
    },
    textLabel: {
      type: String,
      default: ''
    },
    textField: {
      type: String,
      default: ''
    },
    rules: {
      type: Array,
      default: () => []
    },
    placeholder: {
      type: String,
      default: ''
    },
    noErrorText: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: 'text',
      validator: function (value) {
        const validTypes = ['text', 'password', 'email', 'number', 'tel', 'url', 'search', 'date', 'color', 'file']
        return validTypes.includes(value)
      }
    }
  },
  inheritAttrs: false,
  model: {
    prop: 'value',
    event: 'myInputUpdate'
  },
  data() {
    return {
      needVaildate: false,
      vaildateStatus: false,
      notValidate: false,
      noNextValidate: false,
      vaildateText: '',
      inputDom: null,
      hasSlotPrefix: false,
      hasSlotSuffix: false
    }
  },
  watch: {},
  computed: {
    oneProp() {
      return this.prop + getRandomString(3)
    }
  },
  methods: {
    onInput(e) {
      if (e.target.value) this.notValidate = false
      this.$emit('myInputUpdate', e.target.value)
    },
    reset() {
      this.notValidate = false
      this.vaildateStatus = true
      this.$emit('myInputUpdate', '')
    },
    resetValue() {
      this.notValidate = false
    },
    checkFun(error) {
      if (this.noNextValidate) return
      if (error) {
        const errorMessage = error instanceof Error ? error.message : error
        this.notValidate = true
        this.vaildateText = errorMessage
        this.vaildateStatus = false
      } else {
        this.notValidate = false
        this.vaildateStatus = true
      }
    },
    validate() {
      if (!this.needVaildate) return Promise.resolve(true)
      return new Promise((resolve, reject) => {
        let value = this.inputDom.value.trim()
        if (this.rules.length > 0) {
          this.vaildateStatus = true
          for (let i = 0; i < this.rules.length; i++) {
            let rule = this.rules[i]
            if (rule.required && !value) {
              this.notValidate = true
              this.vaildateText = rule.message
              this.vaildateStatus = false
              break
            }
            if (rule.validator) {
              if (typeof rule.validator === 'function') {
                rule.validator({ ...rule }, value, this.checkFun)
                if (!this.vaildateStatus) break
              }
            }
          }
        }
        resolve(this.vaildateStatus)
      })
    }
  },
  created() {},
  mounted() {
    //判断是否有传入插槽
    if (this.$slots.prefix) {
      this.hasSlotPrefix = true
    } else if (this.$slots.suffix) {
      this.hasSlotSuffix = true
    }
    this.inputDom = this.$refs[this.oneProp]
    if (this.rules.length > 0) {
      this.rules.forEach(rule => {
        if (rule.required) this.needVaildate = true
        if (rule.trigger && rule.message) {
          this.inputDom.addEventListener(rule.trigger, () => {
            this.notValidate = false
            this.noNextValidate = false
            if (!this.inputDom.value || !this.inputDom.value.trim()) {
              this.notValidate = true
              this.noNextValidate = true
              this.vaildateText = rule.message
            }
          })
        }
        if (rule.trigger && rule.validator) {
          if (typeof rule.validator === 'function') {
            this.inputDom.addEventListener(rule.trigger, () => {
              rule.validator({ ...rule }, this.inputDom.value, this.checkFun)
            })
          }
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
@import '~assets/css/half.scss';
.myInput {
  color: #333;
  margin-top: 4px;
  max-width: 100%;
  position: relative;
  &.error--text {
    input {
      border: 1px solid #ff5252 !important;
    }
    .vaildate-message {
      color: #ff5252 !important;
      caret-color: #ff5252 !important;
    }
  }
  &.hasPrefix {
    .my-input__prefix {
      left: 5px;
    }
    input {
      padding-left: 30px;
    }
  }
  &.hasSuffix {
    .my-input__suffix {
      right: 5px;
    }
    input {
      padding-right: 30px;
    }
  }
  .slotIcon {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    font-size: 16px;
    color: #999;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  label {
    position: relative;
    display: flex;
    flex-direction: column;
    font-size: 16px;
    .textLabel {
      margin-bottom: 4px;
    }
    input {
      padding: 7px 10px;
      background: #f5f5f5;
      border: 1px solid transparent;
      border-radius: 6px;
      &::placeholder {
        font-size: 14px;
        color: #d8d8d8;
      }
    }

    span:has(+ input[data-required='pre']) {
      &::before {
        content: '*';
        color: #de3500;
      }
    }
    span:has(+ input[data-required='post']) {
      &::after {
        content: '*';
        color: #de3500;
      }
    }

    @include respond-to(mb) {
      font-size: 12px;
    }
  }
  .textField {
    pointer-events: none;
    margin-top: 2px;
    display: flex;
    flex: 1 0 auto;
    max-width: 100%;
    min-height: 14px;
    overflow: hidden;
    line-height: 12px;
    font-size: 12px;
    word-break: break-word;
    word-wrap: break-word;
    hyphens: auto;
  }
}
</style>
