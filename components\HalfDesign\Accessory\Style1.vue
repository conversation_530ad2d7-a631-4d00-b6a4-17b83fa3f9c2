<template>
	<div class="mb-4" :class="stepData.styleName">
		<half-design-select-tip :customText="stepData.attributeTitle" :selectedValue="shape"></half-design-select-tip>
		<slot name="stepText"></slot>
		<div class="step-content">
			<div class="step-item" :class="{active:index===selectIndex}" v-for="(step,index) in stepData.productParamList"
					 :key="index"
					 @click="selectStep(step,index)">
				<div class="imgWrap" :class="{active:index===selectIndex}">
					<img :src="step.imgDetail" :alt="step.valueName" :title="step.valueName">
				</div>
				<div class="d-flex-center text-center name">
					<half-design-check-box class="mr-2"></half-design-check-box>
					<div>
						<div class="text-truncate">
							{{ step.valueName  }}
						</div>
						<div class="price isFree">
							<halfPriceBox v-model="step.selfPrice" :priceData="step" :showUnit="true"></halfPriceBox>
						</div>
					</div>
				</div>
				<half-design-check-icon class="absolute-top-right check-icon"></half-design-check-icon>
				<v-btn
					icon
					small
					color="#9E9E9E"
					class="absolute-top-right zoom-icon"
					@click.stop="zoomPic(step.imgDetail)"
				>
					<b class="icon-a-tgsc-addzhuanhuan"></b>
				</v-btn>
			</div>
		</div>
		<div class="errorTip">
			<v-alert
				dense
				outlined
				type="error"
			>
				{{ langSemiCustom.errorTip }}
			</v-alert>
		</div>
	</div>
</template>
<script>
import {round2} from "@/utils/utils";

export default {
	inject: ['getUnit'],
	props: {
		stepData: {
			type: Object,
			default: {}
		}
	},
	data() {
		return {
			selectIndex: -1,
			selectItem: null
		}
	},
	computed: {
		shape() {
			return this.selectItem?.valueName
		},
		unit() {
			return this.getUnit()
		},
		symbolCode() {
			return this.$store.state.currency.symbol;
		},
		rate() {
			return this.$store.state.currency.rate;
		},
		langSemiCustom() {
			return this.$store.getters.lang?.semiCustom;
		}
	},
	methods: {
		getIncreasePrice(num = 0, increase) {
			if (num <= 0) {
				return 0
			}
			let increaseArr = JSON.parse(increase);
			let findItem = increaseArr.find((item) => {
				return Number(item.quantity) >= Number(num);
			});
			if (findItem) {
				return findItem.unitPrice;
			} else {
				return increaseArr[increaseArr.length - 1].unitPrice;
			}
		},
		getPrice(step) {
			let priceType = step.priceType,
				code = this.symbolCode,
				price = 0,
				rate = this.rate;
			if (priceType === 1) {
				price = round2(step.unitPrice * rate);
				return {
					t: `+${code}${price}/${this.unit}`,
					show: price && price > 0,
				};
			} else {
				return {
					show: false,
				};
			}
		},
		printColorData(state, areaIndex=0) {
		  this.selectIndex = areaIndex;
		  this.selectItem = null;
		  if (areaIndex < 0) return;
		  if (this.stepData && this.stepData.productParamList && this.stepData.productParamList.length > 0) {
		  	let item = this.stepData.productParamList[areaIndex];
		  	this.selectStep(item, areaIndex, state);
		  }
		},
		selectStep(item, index) {
			this.selectIndex = index;
			this.selectItem = item;
			this.$emit('selectStep', {
				type: this.stepData.attributeFlag,
				data: item,
				parent: this.stepData,
				id: this.stepData.id
			})
		},
		zoomPic(img) {
			this.$viewerApi({
				images: [img]
			})
		}
	},
	mounted() {
		this.$Bus.$on("printColor",this.printColorData);
    this.$Bus.$on("selectDefaultPrintColorStep",this.printColorData);
	},
	beforeDestroy(){
		this.$Bus.$off("printColor",this.printColorData);
    this.$Bus.$off("selectDefaultPrintColorStep",this.printColorData);
	}
}
</script>
<style scoped lang="scss">
@import "~assets/css/half.scss";

.step-content {
	display: grid;

	.step-item {
		position: relative;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		@include step-default;
		min-width: 0;
		cursor: pointer;

		.name {
			padding: 10px;
		}

		.check-icon {
			display: none;
		}

		.check-box {
			display: none;
		}

		.price {
			display: none;
			font-size: 14px;

			@include respond-to(mb) {
				font-size: 12px;
			}
		}

		.price.isFree {
			color: $color-red;
		}

		.zoom-icon {
			display: none;
		}

		@include respond-to(mb) {
			.name {
				padding: 5px;
			}
		}
	}
}

.style1 .step-content {
	grid-template-columns: repeat(3, 1fr);
	grid-gap: 10px;

	.step-item {
		.imgWrap {
			@include flex-center;
			width: 100%;
			height: 160px;
		}

		.name {
			padding: 10px 10px 0;
		}
	}

	.step-item.active {
		.check-icon {
			display: flex;
		}
	}

	@include respond-to(mb) {
		grid-gap: 5px;

		.step-item {
			.imgWrap {
				height: 100px;
			}
		}
	}
}

.style2 .step-content {
	grid-template-columns: repeat(5, 1fr);
	grid-gap: 10px;

	.step-item {
		border: none;
		padding: 0;
		background-color: transparent;
		outline: none;

		.name {
			padding: 4px;
		}

		.imgWrap {
			width: 100%;
			@include step-default;
			@include flex-center;
			background-color: $background-color;
			height: 120px;
		}
	}

	.step-item.active {
		.check-icon {
			display: flex;
		}
	}

	@include respond-to(mb) {
		grid-template-columns: repeat(4, 1fr);
		grid-gap: 5px;

		.step-item {
			.imgWrap {
				height: 80px;
			}
		}
	}
}

.style3 .step-content {
	grid-template-columns: repeat(4, 1fr);
	grid-gap: 10px;

	.step-item {
		.check-box {
			display: flex;
		}

		.price {
			display: block;
		}

		.zoom-icon {
			display: block;
		}

		.imgWrap {
			@include flex-center;
			height: 100px;
		}

		.name {
			padding: 10px 10px 0;
		}

		@media (any-hover: hover) {
			&:hover {
				::v-deep .check-box {
					border-color: $color-primary;

					.check-box-inner {
						background-color: $color-primary;
					}
				}
			}
		}
	}

	.step-item.active {
		::v-deep .check-box {
			border-color: $color-primary;

			.check-box-inner {
				background-color: $color-primary;
			}
		}
	}


	@include respond-to(mb) {
		grid-template-columns: repeat(3, 1fr);
		grid-gap: 5px;

		.step-item {
			padding: 0;

			.imgWrap {
				@include flex-center;
				height: 100px;
			}
		}
	}
}

.style4 .step-content {
	grid-template-columns: repeat(3, 1fr);
	grid-gap: 10px;

	.step-item {
		.name {
			padding: 10px 10px 0;
		}

		.price {
			display: block;
		}

		.imgWrap {
			@include flex-center;
			width: 100%;
			height: 160px;
		}
	}

	.step-item.active {
		.check-icon {
			display: flex;
		}
	}

	@include respond-to(mb) {
		grid-template-columns: repeat(3, 1fr);
		grid-gap: 5px;

		.step-item {
			padding: 0 5px;

			.imgWrap {
				height: 100px;
			}
		}
	}
}

.style5 .step-content {
	grid-template-columns: repeat(3, 1fr);
	grid-gap: 10px;

	.step-item {

		.name{
			padding: 0;
		}

		.check-box {
			display: flex;
		}

		.price {
			display: block;
		}

		.imgWrap {
			display: none;
		}

		@media (any-hover: hover) {
			&:hover {

				::v-deep .check-box {
					border-color: $color-primary;

					.check-box-inner {
						background-color: $color-primary;
					}
				}
			}
		}
	}

	.step-item.active {
		background-color: $color-primary;
		color: #ffffff;

		.price {
			color: #ffffff;
		}

		::v-deep .check-box {
			border-color: $color-primary;
			background-color: #ffffff;

			.check-box-inner {
				background-color: $color-primary;
			}
		}
	}

	@include respond-to(mb) {
		grid-template-columns: repeat(2, 1fr);
		grid-gap: 5px;
	}
}
</style>
