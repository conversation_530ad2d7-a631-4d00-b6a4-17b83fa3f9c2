<template>
  <div class="mb-4" :class="stepData.styleName">
    <slot name="stepText"></slot>
    <div class="step-content">
      <div
        class="step-item"
        :class="{ active: index === selectIndex }"
        v-for="(step, index) in stepData.productParamList"
        :key="index"
        @click="selectStep(step, index)"
      >
        <div class="imgWrap">
          <img :src="step.imgDetail" :alt="step.colorAlias" :title="step.colorAlias" />
        </div>
      </div>
    </div>
    <div class="errorTip">
      <v-alert dense outlined type="error">
        {{ langSemiCustom.errorTip }}
      </v-alert>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    stepData: {
      type: Object,
      default: {},
    },
  },
  data() {
    return {
      selectIndex: -1,
      selectItem: null,
    };
  },
  computed: {
    langSemiCustom() {
      return this.$store.getters.lang?.semiCustom;
    },
  },
  methods: {
    selectStep(item, index, state = false) {
      this.selectIndex = index;
      this.selectItem = item;
      this.$emit("selectStep", {
        type: this.stepData.attributeFlag,
        data: item,
        parent: this.stepData,
        id: this.stepData.id,
        firstSelect: state,
        colorIndex: index,
      });
    },
    selectDefault() {
      if (this.selectIndex <= -1) {
        this.selectStep(this.stepData.productParamList[0], 0, true);
      }
    },
    setActiveProductColor() {
      let newColorList = this.stepData.productParamList.filter((color) => {
        return color.isActivity == 1;
      });
      if (newColorList.length == 0) {
        return;
      }
      this.stepData.productParamList = newColorList;
    },
  },
  mounted() {
    this.$Bus.$on("selectDefaultColorStep", this.selectDefault);
    this.$Bus.$on("setActiveProductColor", this.setActiveProductColor);
  },
  beforeDestroy() {
    this.$Bus.$off("selectDefaultColorStep", this.selectDefault);
    this.$Bus.$off("setActiveProductColor", this.setActiveProductColor);
  },
};
</script>
<style scoped lang="scss">
@import "~assets/css/half.scss";
.style1 .step-content {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  gap: 10px;

  .step-item {
    min-width: 0;
    position: relative;
    display: flex;
    align-items: center;
    cursor: pointer;

    border-radius: 50%;
    overflow: hidden;
    aspect-ratio: 1;

    &.active {
      .imgWrap {
        border-color: $color-primary;
        color: $color-primary;
      }
    }

    @media (any-hover: hover) {
      &:hover {
        .imgWrap {
          border-color: $color-primary;
          color: $color-primary;
        }
      }
    }

    .imgWrap {
      padding: 6px;
      border: 2px solid #bebebe;
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }
  }

  .step-item.active {
    .checkIcon {
      display: flex;
    }
  }
}

@include respond-to(mb) {
  .style1 .step-content {
    grid-template-columns: repeat(6, 1fr);
    grid-gap: 5px;
  }
}
</style>
