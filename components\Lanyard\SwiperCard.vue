<template>
  <div class="SwiperCard">
    <div>
      <div class="swiper swiper1" ref="swiper1">
        <div class="swiper-wrapper">
          <div
            class="swiper-slide pointer"
            v-for="(item, index) in bindValue.childList"
            v-show="(index == 0 || (item.childList && item.childList.length > 0)) && !item.noShow"
            :key="item.id"
          >
            <div
              v-if="index == 0 || (item.childList && item.childList.length > 0)"
              class="top-item hover-type"
              :class="{ active: Number.isFinite(selectInfoIndex)  && item.id == bindValue.childList[selectInfoIndex].id }"
            >
              <div
                class="afterBottomWhite"
                :class="{
                  rotateYAnimation:
                    rotateYAnimation && item.paramName != 'No Upgrades',
                }"
                @click="selectFun(item, index)"
              >
                <div>
                  <el-image
                    :src="
                      JSON.parse(item.imageJson)[0]
                        ? JSON.parse(item.imageJson)[0].url
                        : ''
                    "
                    fit="contain"
                    lazy
                  ></el-image>
                  <el-image
                    v-if="JSON.parse(item.imageJson)[1]"
                    :src="
                      JSON.parse(item.imageJson)[1]
                        ? JSON.parse(item.imageJson)[1].url
                        : ''
                    "
                    fit="contain"
                    lazy
                  ></el-image>
                </div>
                <div class="title">
                  {{ item.alias }}
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="swiper-button-next"></div>
        <div class="swiper-button-prev"></div>
      </div>
    </div>
    <div
      class="box custom-scrollbar"
      :class="label"
      v-if="selectInfoIndex && (bindValue.childList[selectInfoIndex].childList && bindValue.childList[selectInfoIndex].childList.length > 0)"
    >
      <template>
        <label
          :for="label + item.id"
          class="bottom-item"
          v-for="(item, index) in bindValue.childList[selectInfoIndex].childList"
          :key="item.id"
          :class="{ activeItem: index == chooseIndex }"
        >
          <div>
            <el-image
              :src="
                item && JSON.parse(item.imageJson)[0]
                  ? JSON.parse(item.imageJson)[0].url
                  : ''
              "
              fit="contain"
              lazy
            ></el-image>
            <div class="title">
              {{ JSON.parse(item.imageJson)[0].alt }}
            </div>
            <PriceText
              :price-info="item.priceInfo"
              :onlyAddInquiry="item.onlyAddInquiry"
              :inputNum="item.quantity"
              v-if="showPrice"
            ></PriceText>
          </div>
          <div class="inputbox">
            <div class="title">
              {{ JSON.parse(item.imageJson)[0].alt }}
            </div>
            <div class="input">
              <input
                :class="{ active: item.quantity && item.quantity > 0 }"
                :controls="false"
                class="myInput"
                onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode)))"
                type="number"
                v-model.number="item.quantity"
                placeholder="QTY"
                @input="inputFun($event, item)"
                @click="chooseFun(item,index)"
              />
            </div>
          </div>
        </label>
      </template>
    </div>
  </div>
</template>
<script>
import PriceText from "@/components/Quote/PriceText"
export default {
  name: "SwiperCard",
  props: {
    bindValue: {
      type: Object,
    },
    label: {
      type: String,
    },
    rotateYAnimation: {
      type: Boolean,
      default: false,
    },
    top: {
      required: true,
      type: [String,Number],
    },
    bindName: {
      type: String,
    },
    selectedData: {
      type: [Array, Object],
    },
    arr: {
      type: Array,
    },
    showPrice: {
      type: Boolean,
      default: true,
    },
  },
  components: {
    PriceText,
  },
  data() {
    return {
      swiper1: null,
      selectInfoIndex: '',
      selectArr: [],
      chooseIndex:null,
    }
  },
  methods: {
    reset(){
      this.selectInfoIndex = '';
    },
    chooseFun(val,index){
      this.chooseIndex = index
      this.bindValue.childList[this.selectInfoIndex].childList.forEach(item => {
        if (item.id !== val.id) {
          item.quantity = '';
          this.inputFun(null,item,false) //切换后清空其他  并更新selectedData 否则能直接点下一步
        }
      });

    },
    inputFun(num, val,needAdd=true) {
      let tempList = JSON.parse(JSON.stringify(this.arr))
      const selectInfo = this.bindValue.childList[this.selectInfoIndex]
      if (!selectInfo) return
      let index = tempList.findIndex((x) => {
        return x.id == selectInfo.id
      })
      if (index >= 0) {
        let Boo = selectInfo.childList.some((x) => {
          return x.quantity && x.quantity > 0
        })
        // tempList=[]
        Boo
          ? tempList.splice(index, 1, selectInfo)
          : tempList.splice(index, 1)
      } else {
        if (val.quantity && val.quantity > 0) {
          tempList.push(selectInfo)
        }
      }
      this.$emit("update:arr", tempList)
      this.$emit("clickFun", {
        key: this.bindName,
		type:needAdd,
        value: tempList,
      })
    },
    selectFun(val, index) {
      this.$emit("update:arr", [])
      this.bindValue.childList.forEach(item1=>{
        item1.childList.forEach(item2 => {
          item2.quantity = '';
            this.inputFun(null,item2,false) //切换后清空其他  并更新selectedData 否则能直接点下一步
        })
      })
      // ----------------------------
      this.selectInfoIndex = index
      if (val.paramName == "No Upgrades") {
        this.$emit("clickFun", {
          key: this.bindName,
          value: [val],
        })
        this.$emit("update:arr", [])
      }
      // this.$emit("clickFun", {
      //   key: this.bindName,
      //   value: val,
      // })
      // this.$emit("picDialogFun", false)
    },
    //计算阶梯价格
    calculateIncreasePriceFun(val) {
      let temp = 0
      if (typeof val.priceInfo.increasePrice == "string") {
        val.priceInfo.increasePrice = JSON.parse(val.priceInfo.increasePrice)
      }
      if (val && val.quantity && val.quantity > 0) {
        if (val.quantity <= val.priceInfo.increasePrice[0].quantity) {
          return val.priceInfo.increasePrice[0].unitPrice
        } else {
          for (let i = val.priceInfo.increasePrice.length - 1; i >= 0; i--) {
            if (val.quantity >= val.priceInfo.increasePrice[i].quantity) {
              return val.priceInfo.increasePrice[i].unitPrice
            }
          }
        }
      } else {
        return val.priceInfo.increasePrice[0].unitPrice
      }
    },
  },
  computed: {},
  watch: {},
  created() {},
  updated(){
    this.swiper1?.updateSlides();
  },
  async mounted() {
    console.log('加载swiper',this.bindValue.childList,this.top);
    await this.$nextTick()
    this.swiper1 = new Swiper(this.$refs.swiper1, {
      slidesPerView: this.top,
      spaceBetween: 10,
      watchSlidesVisibility: true, //防止不可点击
      navigation: {
        nextEl: ".swiper-button-next",
        prevEl: ".swiper-button-prev",
      },
    })
  },
}
</script>

<style scoped lang="scss">
.SwiperCard ::v-deep {
  .top-item {
    background: #f5f6f7;
    border: 1px solid #f0f1f2;
    border-radius: 10px 10px 0 0;
    box-sizing: content-box;
    cursor: pointer;
    text-align: center;
    height: 100%;
    > div {
      height: 100%;
      padding: 10px 0 20px;
      .el-image {
        height: 240px;
        @media screen and (max-width: 767px) {
          height: 96px;
          padding: 10px 0;
        }
      }
    }
    .title {
      color: #333;
      font-size: 16px;
      font-weight: 400;
      line-height: 1em;
      text-overflow: ellipsis;
      white-space: nowrap;
      @media screen and (max-width: 767px) {
        white-space: normal;
        font-size: 12px;
        height: 24px;
      }
    }
    @media (any-hover: hover) {
      &.hover-type:hover {
        border-color: $color-primary;
      }
    }
    &.active {
      background: #ffffff;
      border-color: $color-primary;
      border-bottom: transparent;

      &::before {
        position: absolute;
        content: url("~/assets/lanyardQuote/svg/bgh_Selected.svg");
        left: 0;
        top: 0;
        width: 50px;
        height: 50px;
      }

      &::after {
        position: absolute;
        content: url("~/assets/lanyardQuote/svg/Selected.svg");
        left: 8px;
        top: 5px;
        width: 19px;
        height: 20px;
        display: flex;
        align-items: center;
      }
    }
  }
  .box {
    grid-gap: 24px;
    border: 1px solid #ebebeb;
    border-top: 0;
    box-sizing: border-box;
    display: grid;
    gap: 24px;
    grid-template-columns: repeat(4, 1fr);
    justify-content: space-between;
    overflow-x: hidden;
    overflow-y: auto;
    padding: 30px 13px;
    @media screen and (max-width: 767px) {
      grid-template-columns: repeat(2, 1fr);
      padding: 10px;
      gap: 10px;
    }
    .bottom-item {
      border: 1px solid #f5f5f5;
      border-radius: 4px;
      padding: 10px 39px 16px;
      text-align: center;
      @media screen and (max-width: 767px) {
        padding: 10px;
      }
      .price {
        font-size: 16px;
        font-weight: 400;
        margin-top: 5px;
        @media screen and (max-width: 767px) {
          font-size: 12px;
        }
      }
      .inputbox {
        align-items: center;
        display: flex;
        margin-top: 5px;
        @media screen and (max-width: 767px) {
          flex-direction: column;
          justify-content: center;
        }
        .title {
          font-size: 16px;
          line-height: 1em;
          white-space: nowrap;
          margin-right: 5px;
          @media screen and (max-width: 767px) {
            font-size: 12px;
          }
        }
        .el-input-number {
          width: auto;
          margin-left: 10px;
          input {
            height: 30px;
            line-height: 30px;
            font-size: 16px;
            text-align: left;
            &:focus {
              border-color: $color-primary;
            }
            @media screen and (max-width: 767px) {
              margin-right: 0;
              font-size: 12px;
            }
          }
          &.active {
            input {
              border-color: $color-primary;
            }
          }
        }
      }
    }
    &.badgeReel {
      grid-gap: 15px;
      gap: 15px;
      grid-template-columns: repeat(6, 1fr);
      @media screen and (max-width: 767px) {
        grid-template-columns: repeat(3, 1fr);
        padding: 10px;
        gap: 10px;
      }
      .bottom-item {
        padding: 12px;
        .inputbox {
          .title {
            display: none;
          }
        }
      }
    }
  }
  .swiper-button-prev:after {
    width: 28px;
    height: 28px;
    content: "";
    background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBzdGFuZGFsb25lPSJubyI/PjwhRE9DVFlQRSBzdmcgUFVCTElDICItLy9XM0MvL0RURCBTVkcgMS4xLy9FTiIgImh0dHA6Ly93d3cudzMub3JnL0dyYXBoaWNzL1NWRy8xLjEvRFREL3N2ZzExLmR0ZCI+PHN2ZyB0PSIxNjQ3MDQ5NjY0MTA3IiBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHAtaWQ9IjM1NzMiIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PGRlZnM+PHN0eWxlIHR5cGU9InRleHQvY3NzIj48L3N0eWxlPjwvZGVmcz48cGF0aCBkPSJNNTEyIDEwMjRBNTEyIDUxMiAwIDEgMCAwIDUxMmE1MTIgNTEyIDAgMCAwIDUxMiA1MTJ6IiBmaWxsPSIjQjhCQUJDIiBwLWlkPSIzNTc0Ij48L3BhdGg+PHBhdGggZD0iTTM1NS40NzQyODYgNTM1LjA0TDMyOS4xNDI4NTcgNTEybDI2OS41MzE0MjktMjQ2LjQ5MTQyOWEzNi41NzE0MjkgMzYuNTcxNDI5IDAgMCAxIDQ5LjM3MTQyOCAwIDI5LjYyMjg1NyAyOS42MjI4NTcgMCAwIDEgMCA0NC42MTcxNDNsLTIxOS40Mjg1NzEgMjAxLjUwODU3MiAyMTkuNDI4NTcxIDIwMS41MDg1NzFhMjkuOTg4NTcxIDI5Ljk4ODU3MSAwIDAgMSAwIDQ0Ljk4Mjg1NyAzNi41NzE0MjkgMzYuNTcxNDI5IDAgMCAxLTQ5LjM3MTQyOCAwbC0yNDMuNTY1NzE1LTIyMy4wODU3MTR6IiBmaWxsPSIjRkZGRkZGIiBwLWlkPSIzNTc1Ij48L3BhdGg+PC9zdmc+);
    background-repeat: no-repeat;
    background-size: contain;
    border-radius: 50%;
  }

  .swiper-button-next:after {
    width: 28px;
    height: 28px;
    content: "";
    background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBzdGFuZGFsb25lPSJubyI/PjwhRE9DVFlQRSBzdmcgUFVCTElDICItLy9XM0MvL0RURCBTVkcgMS4xLy9FTiIgImh0dHA6Ly93d3cudzMub3JnL0dyYXBoaWNzL1NWRy8xLjEvRFREL3N2ZzExLmR0ZCI+PHN2ZyB0PSIxNjQ3MDQ5NjU4MDU4IiBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHAtaWQ9IjM0NDIiIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PGRlZnM+PHN0eWxlIHR5cGU9InRleHQvY3NzIj48L3N0eWxlPjwvZGVmcz48cGF0aCBkPSJNNTEyIDEwMjRhNTEyIDUxMiAwIDEgMSA1MTItNTEyIDUxMiA1MTIgMCAwIDEtNTEyIDUxMnoiIGZpbGw9IiNCOEJBQkMiIHAtaWQ9IjM0NDMiPjwvcGF0aD48cGF0aCBkPSJNNjY4LjUyNTcxNCA1MzUuMDRMNjk0Ljg1NzE0MyA1MTJsLTI2OS41MzE0MjktMjQ2LjQ5MTQyOWEzNi41NzE0MjkgMzYuNTcxNDI5IDAgMCAwLTQ5LjM3MTQyOCAwIDI5LjYyMjg1NyAyOS42MjI4NTcgMCAwIDAgMCA0NC42MTcxNDNsMjE5LjQyODU3MSAyMDEuNTA4NTcyLTIxOS40Mjg1NzEgMjAxLjUwODU3MWEyOS45ODg1NzEgMjkuOTg4NTcxIDAgMCAwIDAgNDQuOTgyODU3IDM2LjU3MTQyOSAzNi41NzE0MjkgMCAwIDAgNDkuMzcxNDI4IDBsMjQzLjU2NTcxNS0yMjMuMDg1NzE0eiIgZmlsbD0iI0ZGRkZGRiIgcC1pZD0iMzQ0NCI+PC9wYXRoPjwvc3ZnPg==);
    background-repeat: no-repeat;
    background-size: contain;
    border-radius: 50%;
  }
  .rotateYAnimation {
    > div:nth-child(1) {
      position: relative;

      .el-image {
        img {
          backface-visibility: hidden;
        }
      }

      .el-image:nth-child(1) {
        z-index: 1;
        opacity: 1;
      }

      .el-image:nth-child(2) {
        position: absolute;
        left: 0;
        top: 0;
        right: 0;
        bottom: 0;
        opacity: 0;

        img {
          transform: translateY(180deg);
        }
      }
    }

    &:hover {
      > div:nth-child(1) {
        animation-name: translateImage;
        animation-timing-function: linear;
        animation-duration: 0.5s;
        animation-fill-mode: forwards;

        .el-image:nth-child(1) {
          animation-name: zIndexChange1;
          animation-timing-function: linear;
          animation-duration: 0.5s;
          animation-fill-mode: forwards;
        }

        .el-image:nth-child(2) {
          animation-name: zIndexChange2;
          animation-timing-function: linear;
          animation-duration: 0.5s;
          animation-fill-mode: forwards;
        }
      }
    }
  }
  @keyframes translateImage {
    0% {
      transform: rotateY(0deg);
    }

    50% {
      transform: rotateY(90deg);
    }

    100% {
      transform: rotateY(180deg);
    }
  }

  @keyframes zIndexChange1 {
    0% {
      z-index: 2;
      opacity: 1;
    }

    50% {
      z-index: 2;
      opacity: 1;
    }

    100% {
      z-index: 1;
      opacity: 0;
    }
  }

  @keyframes zIndexChange2 {
    0% {
      z-index: 1;
      opacity: 0;
    }

    50% {
      z-index: 1;
      opacity: 0;
    }

    100% {
      z-index: 2;
      opacity: 1;
    }
  }
  .myInput {
    width: auto;
    line-height: 1em;
    text-align: left;
    -webkit-appearance: none;
    background-color: var(--el-input-bg-color, var(--el-color-white));
    background-image: none;
    border: 1px solid #dcdfe6;
    box-sizing: border-box;
    color: var(--el-input-text-color, var(--el-text-color-regular));
    display: inline-block;
    font-size: 16px;
    height: 30px;
    line-height: 30px;
    outline: 0;
    padding: 0 11px;
    transition: var(--el-transition-border);
    width: 100%;

    @media screen and (max-width: 768px) {
      height: 30px;
      line-height: 30px;
      font-size: 12px;
    }
  }
}

.SwiperCard .box .activeItem{
  border-color: $color-primary;
}
</style>
