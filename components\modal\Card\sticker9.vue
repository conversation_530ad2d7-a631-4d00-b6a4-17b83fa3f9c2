<template>
	<div class="summary-box" :class="modal.wrapClass" :style="modal.style">
		<div class="bps-container" v-for="(o, oi) in modal.outer" :key="oi">
			<div class="content">
				<div class="content_div">
					<pic :src="modal.outer[0].img.value" :alt="modal.outer[0].img.alt" :title="modal.outer[0].img.alt"
						@click="setModalType(modal.outer[0].img, modal.outer, 'img')" />
				</div>
				<div class="content_div">
					<div class="faq">
						<div class="container">
							<EditDiv tag-name="h2" v-model:content="modal.outer[0].title.value"
								@click="setModalType(modal.outer[0].title, modal.outer, 'text')" />
							<ul id="faq">
								<li v-for="(item, index) in modal.outer[0].list" :key="index">
									<div flex class="question">
										<EditDiv v-if="item.title" class="questions-item"
											v-model:content="item.title.value"
											@click="setModalType(item.title, modal.outer, 'text')" />
										<div class="faq_img"></div>
										<!-- <strong>+</strong> -->
									</div>
									<EditDiv v-if="item.subTitle" class="answer" v-model:content="item.subTitle.value"
										@click="setModalType(item.subTitle, modal.outer, 'text')" />
								</li>
							</ul>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	props: {
		data: {
			type: Object,
			default: {},
		}
	},
	data() {
		return {
			modal: {
				style: {},
				type: { index: 0, clickPosition: 'outer' },
				...this.data
			}
		};
	},
	watch: {
		modal: {
			handler(val) {
				if (process.env.isManage) this.$emit("update:data", val);
			},
			immediate: true,
			deep: true
		}
	},
	methods: {
		setModalType(target, targetArray, clickType, event, other) {
			this.$setModal(this, target, targetArray, clickType, event, other)
		},
		toggleFaq() {
			let findParent = (node, k) => {
				let findNode = "";
				let find = (node, k) => {
					let nodeName = node.nodeName.toUpperCase();
					if (nodeName != k) {
						find(node.parentNode, k);
					} else {
						findNode = node;
					}
				};
				find(node, k);
				return findNode;
			};

			function isHidden(el) {
				return el.style.display === "none" || el.style.display === "";
			}

			let faq = document.getElementById("faq");
			faq.addEventListener("click", e => {
				let target = e.target;
				if (target.nodeName.toUpperCase() === "UL") {
					return;
				}
				let liNode = findParent(target, "LI");
				let answer = liNode.getElementsByClassName("answer")[0];
				let faqImg = liNode.getElementsByClassName("faq_img")[0];
				let answerList = Array.from(faq.getElementsByClassName("answer"));
				let faqImgList = Array.from(faq.getElementsByClassName("faq_img"));
				if (isHidden(answer)) {
					answer.style.display = "block";
					faqImg.style.background = "url('https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230328/20230328EkJrQC6c.png')center/contain no-repeat";
				} else {
					answer.style.display = "none";
					faqImg.style.background = "url('https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230328/20230328ksdmRa6Q.png')center/contain no-repeat";
				}
				answerList.forEach(item => {
					if (item !== answer) {
						item.style.display = "none";
					}
				});
				faqImgList.forEach(item => {
					if (item !== faqImg) {
						item.style.background = "url('https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230328/20230328ksdmRa6Q.png')center/contain no-repeat";
					}
				});
			});
		},
	},
	created() {
		if (process.browser) {
			document.querySelector('script#faqStruct')?.remove();

			let script = document.createElement('script');
			script.id = 'faqStruct';
			script.type = 'application/ld+json';
			script.text = JSON.stringify({
				"@context": "http://schema.org",
				"@id": `https://${this.$store.state.proUrl}/`,
				"@type": "FAQPage",
				"name": this.$store.state.proName,
				"url": `https://${this.$store.state.proUrl}/`,
				"mainEntity": this.data.outer[0]?.list.map(i => {
					return {
						"@type": "Question",
						"name": (i.title?.value || '') + (i.subTitle?.value || ''),
						"acceptedAnswer": { "@type": "Answer", "text": i.text.value }
					}
				})
			});
			document.querySelector('head').appendChild(script);
		}
	},
	mounted() {
		if (document.getElementById("faq")) this.toggleFaq();
	}
};
</script>

<style lang="scss" scoped>
.summary-box {
	padding: 70px 260px;
	background: linear-gradient(90deg, #FFF2F4 0%, #B1DAFA 100%);

	.bps-container {
		background: #FFFFFF;
		box-shadow: 0px 8px 22px 0px rgba(13, 15, 17, 0.14);
		border-radius: 10px;
		padding: 30px;

		.content {
			font-size: 0;

			.content_div {
				display: inline-block;
				vertical-align: middle;

				&:nth-child(1) {
					width: 40%;
				}

				&:nth-child(2) {
					margin-left: 2%;
					width: 58%;

					.faq {
						padding-bottom: 2vmax;

						h2 {
							font-size: 42px;
							text-align: left;
							margin-bottom: 47px;
						}

						ul {
							margin: 0 !important;
						}

						li {
							list-style-type: none;
							border-bottom: 1px solid #dddddd;

							&:first-child {
								border-top: 1px solid #dddddd;
							}
						}

						.question {
							padding: 2% 0;
							cursor: pointer;
							justify-content: space-between;

							.questions-item {
								font-size: 18px;
								font-family: Roboto;
								font-weight: bold;
								color: #333333;
							}
						}

						.faq_img {
							width: 8px;
							height: 8px;
							flex-shrink: 0;
							background: url("https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230328/20230328ksdmRa6Q.png") center/contain no-repeat;
						}

						.answer {
							display: none;
							margin-bottom: 2%;
							font-size: 16px;
							font-family: Roboto;
							font-weight: 300;
							color: #333333;
						}
					}
				}
			}
		}
	}
}

@media screen and (max-width: $mb-width) {
	.summary-box {
		padding: 13px 10px;

		.bps-container {
			.content {
				.content_div {
					&:nth-child(1) {
						display: none;
					}

					&:nth-child(2) {
						width: 100% !important;
						margin-left: 0 !important;

						.faq {
							h2 {
								font-size: 21px;
							}

							.question {
								padding: 5% 0;

								.questions-item {
									font-size: 12px;
								}
							}

							.answer {
								font-size: 12px;
							}
						}
					}
				}
			}
		}
	}
}
</style>
