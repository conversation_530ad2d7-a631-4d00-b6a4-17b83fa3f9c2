<template>
	<div class="step3 ribbonStep3" :id="stepData.copyId">
		<half-design-litter-title v-show="stepData.attributeTitle" :index="stepData.id" :data-name="`${stepData.styleClass + '_' + stepData.id}`" style="margin: 10px 0" :stepTitle="stepData.minStepTitle">{{ showChangeTitle }} </half-design-litter-title>
		<div v-show="!uploadBox" class="step-content">
			<div class="leftBox">
				<div class="step-item" :class="{ active: index === selectIndex }" v-for="(step, index) in stepData.productParamList" :key="index" @click="selectStep(step, index)">
					<div class="imgBox">
						<pic v-if="step.imgUrl" :src="step.imgUrl" :alt="step.valueName" :title="step.valueName"></pic>
						<span v-else :style="{ backgroundColor: step.valueName }"></span>
						<b class="icon-check selfIcon"></b>
					</div>
					<!-- <half-design-check-icon class="absolute-top-right2 check-icon"></half-design-check-icon> -->
				</div>
			</div>
			<div class="rightBox">
				<div class="imgWrap" :class="{ active: index === selectIndex }" v-for="(step, index) in stepData.productParamList" :key="index">
					<pic v-if="step.imgDetail" :src="step.imgDetail" :alt="step.valueName" :title="step.valueName"></pic>
				</div>
			</div>
		</div>
		<div v-show="uploadBox" class="nowUploadFile">
			<div class="upload">
				<div class="con-radio2">
					<label @click="openUpload">
						<input type="radio" :checked="+radioIndex == 1" name="ribbonStep3" :value="1" @change="changeRadio(1)" />
						<span @click.stop class="custom-radio"></span>
						<span class="uploadText" @click.stop>
							<b class="icon-shangchuan"></b>
							Browse
						</span>
					</label>
				</div>
				<ToolTip class="uploadTip" :titleContent="'Allowed extensions: EPS, AI, PDF, JPG, JPEG, GIF, PNG, BMP, TIF, SVG, WORD, EXCEL, TXT, WEBP.'"> </ToolTip>
			</div>
			<div class="orBox">
				{{ langQuote.bannerQuote.or }}
			</div>
			<div class="emailLater">
				<div class="con-radio2">
					<label>
						<input type="radio" :checked="+radioIndex == 0" name="ribbonStep3" :value="0" @change="changeRadio(0)" />
						<span class="custom-radio"></span>
						<span>
							{{ langQuote.emailUsLater }}
						</span>
					</label>
				</div>
			</div>
		</div>
		<div class="fileWrap" :class="{ hasFile: step3Upload.length > 0 }" v-if="uploadBox">
			<div class="file-item" v-for="(item, index) in step3Upload" :key="item.secure_url" @click="zoomPic(item.secure_url)">
				<div class="fileInfo d-flex align-items-center">
					<b class="icon-a-jxs-lanyarddesignzhuanhuan" style="margin-right: 2px; color: #000"></b>
					<span class="fileName">{{ item.original_filename }}</span>
				</div>
				<div class="control">
					<b class="icon-check"></b>
					<b class="icon-shanchu1" @click.stop="deletePic(index)"></b>
				</div>
			</div>
		</div>
		<input type="file" ref="uploadRibbonStep3" @change="uploadPic" />
		<div class="errorTip">
			<v-alert dense outlined type="error">
				{{ langSemiCustom.errorTip }}
			</v-alert>
		</div>
	</div>
</template>

<script>
import { uploadFile } from "@/utils/oss";
import { isImageType } from "@/utils/utils";
import ToolTip from "@/components/HalfDesign/customMedals/common/ToolTip";
export default {
	name: "ribbonStep3",
	props: {
		stepData: {
			type: Object,
			default: () => ({}),
		},
		changeTitle: {
			type: String,
			default: "",
		},
		uploadBox: {
			type: Boolean,
			default: false,
		},
		step1SelectItem: {
			type: Object,
			default: () => ({}),
		},
		radioIndex: {
			type: [Number, String],
			default: -1,
		},
	},
	components: { ToolTip },
	data() {
		return {
			selectIndex: -1,
			selectItem: null,
			step3Upload: [],
		};
	},
	watch: {},
	computed: {
		shape() {
			return this.uploadBox ? "" : this.selectItem?.valueName;
		},
		showChangeTitle() {
			return this.changeTitle || this.stepData.attributeTitle;
		},
		attributeFlag() {
			return this.stepData.attributeFlag;
		},
		nameProperty() {
			let attributeFlag = this.attributeFlag,
				key;
			switch (attributeFlag) {
				case "color":
					key = "colorAlias";
					break;
				default:
					key = "valueName";
					break;
			}
			return key;
		},
		langQuote() {
			return this.$store.getters.lang.quote || {};
		},
		langSemiCustom() {
			return this.$store.getters.lang?.semiCustom;
		},
	},
	methods: {
		selectStep(item, index = 0, state = false) {
			this.selectIndex = index;
			this.selectItem = item;
			this.$emit("selectStep", {
				type: this.stepData.isLanyards,
				data: item,
				parent: this.stepData,
				id: this.stepData.id,
				copyId: this.stepData.copyId,
				firstSelect: state,
			});
		},
		selectDefault() {
			if (this.stepData.productParamList.length) {
				this.selectStep(this.stepData.productParamList[0], 0, true);
			}
		},
		openUpload() {
			this.$refs.uploadRibbonStep3.click();
		},
		uploadPic(event) {
			let files = event.target.files,
				size = files[0].size;
			this.$gl.show();
			if (size / 1020 / 1024 > 80) {
				this.$toast.error("File size cannot exceed 80m.");
				this.$refs.uploadRibbonStep3.value = "";
				return;
			}
			uploadFile(files[0])
				.then((res) => {
					this.step3Upload = [];
					this.step3Upload.push({
						original_filename: files[0].name,
						secure_url: res,
					});
					this.$set(this.step1SelectItem, "files", []);
					this.$set(this.step1SelectItem, "files", [res]);
					this.changeRadio(this.radioIndex);
					this.$refs.uploadRibbonStep3.value = "";
					this.$gl.hide();
				})
				.finally(() => {
					this.$gl.hide();
				});
		},
		deletePic(index) {
			this.step3Upload.splice(index, 1);
			this.$set(this.step1SelectItem, "files", []);
			this.changeRadio(-1);
		},
		changeRadio(num) {
			this.$emit("selectRadioFn", num);
		},
		zoomPic(img) {
			if (isImageType(img)) {
				this.$viewerApi({
					images: [img],
				});
			}
		},
	},
	created() {},
	mounted() {
		if (!this.noDefaultSelect) {
			this.$Bus.$on("selectDefaultOtherStep", this.selectDefault);
		}
	},
	beforeDestroy() {
		if (!this.noDefaultSelect) {
			this.$Bus.$off("selectDefaultOtherStep", this.selectDefault);
		}
	},
};
</script>

<style scoped lang="scss">
@import "~assets/css/half.scss";

.ribbonStep3 {
	.step-content {
		display: flex;
		gap: 30px;
		.leftBox {
			flex: 1;
			display: grid;
			grid-template-columns: repeat(8, calc((100% - (8 - 1) * 4px) / 8));
			gap: 4px;
			background-color: #fff;

			.step-item {
				padding: 2px;
				border: 1px solid #e6e6e6;
				border-radius: 50%;
				overflow: hidden;
				aspect-ratio: 1/1;

				&.active {
					padding: 1px;
					border-width: 2px;
					border-color: $color-primary;
					color: $color-primary;

					.imgBox {
						.selfIcon {
							display: flex;
						}
					}
				}

				@media (any-hover: hover) {
					&:hover {
						padding: 1px;
						border-width: 2px;
						border-color: $color-primary;
						color: $color-primary;
					}
				}

				.imgBox {
					width: 100%;
					height: 100%;
					aspect-ratio: 1/1;
					border-radius: 50%;
					overflow: hidden;
					position: relative;
					display: flex;
					align-items: center;
					justify-content: center;

					img {
						width: 100%;
						height: 100%;
						aspect-ratio: 1/1;
						overflow: hidden;
					}

					.selfIcon {
						display: none;
						font-size: 14px;
						color: #fff;
						position: absolute;
						top: 50%;
						left: 50%;
						transform: translate(-50%, -50%);
						z-index: 1;
					}
				}
			}
		}
		.rightBox {
			flex: 1;
			min-width: 0;
			padding: 4%;
			.imgWrap {
				display: none;
				padding: 8px;
				border: 1px solid #dbdbdb;
				border-radius: 6px;
				width: 100%;
				align-items: center;
				justify-content: center;
				&.active {
					display: flex;
				}
				img {
					width: 100%;
					height: 100%;
					object-fit: contain;
				}
			}
		}
		@include respond-to(mb) {
			flex-direction: column;
			padding: 6px;
			.rightBox {
				padding: 0;
				.imgWrap {
					padding: 0;
					border: none;
					border-radius: 0;
				}
			}
		}
	}

	.nowUploadFile {
		display: flex;
		align-items: center;
		font-size: 14px;
		gap: 1.4em;

		.upload {
			display: flex;
			align-items: center;
			gap: 4px;
			color: $color-primary;

			.uploadText {
				text-decoration: underline;
			}

			b.icon-shangchuan {
				font-size: 1.4em;
				color: $color-primary;
				margin-right: 4px;
			}
		}

		.orBox {
			width: 2.5em;
			height: 2.5em;
			line-height: 2.5em;
			text-align: center;
			background: #fafafa;
			border-radius: 50%;
		}

		.emailLater {
		}
	}

	.con-radio2 {
		label {
			display: flex;
			align-items: center;
			gap: 7px;
			cursor: pointer;
		}

		/* 未选中状态下的样式 */
		input[type="radio"] {
			/* 隐藏原始的单选按钮 */
			display: none;
		}

		/* 自定义样式 */
		.custom-radio {
			display: inline-block;
			width: 16px;
			height: 16px;
			border-radius: 50%;
			border: 1px solid #333;
			position: relative;
			cursor: pointer;
		}

		/* 选中状态下的样式 */
		.custom-radio:before {
			content: "";
			display: block;
			width: 0.5em;
			height: 0.5em;
			background-color: #fff;
			position: absolute;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
			border-radius: 50%;
		}

		/* 选中状态下的外圈样式 */
		input[type="radio"]:checked + .custom-radio {
			background-color: $color-primary !important;
			border: 1px solid $color-primary;
		}
	}
}

.fileWrap {
	&.hasFile {
		margin-top: 10px;
	}

	.file-item {
		display: flex;
		justify-content: space-between;

		.control {
			margin-left: 40px;
			flex-shrink: 0;

			b {
				margin: 0 2px;
				cursor: pointer;
			}
		}
	}

	@include respond-to(mb) {
		.file-item {
			font-size: 12px;

			.v-icon {
				font-size: 18px;
			}
		}
	}
}

input[type="file"] {
	display: none;
	position: absolute;
	z-index: -1;
	opacity: 0;
}
</style>
