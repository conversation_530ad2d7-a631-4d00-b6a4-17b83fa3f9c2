<template>
	<div id="table-show">
		<div class="top"></div>
		<div class="main">
			<div class="table-row-1">
				<analysis-TableShowHeader />
				<analysis-TableShowItem class="analysis-TableShowItem-set-margin" />
			</div>
			<div class="table-row-remain" v-for="item in 11" :key="item">
				<analysis-TableShowItem />
			</div>
		</div>
	</div>
</template>

<script>
export default {
	data() {
		return {};
	},
};
</script>

<style lang="scss" scoped>
#table-show {
	width: 100%;
}
.top {
	width: 23.69791667vw;
	height: 1.5625vw;
	margin-left: 0.98958333vw;
	background: #e5e5e5;
	border-radius: 0.3125vw;
	margin-bottom: 0.8854vw;
}
.main {
	height: 45.9375vw;
	background: #eeeeee;
	border-radius: 0.5208vw;
	padding-right: 1.6146vw;
	padding-left: 1.3542vw;
	.table-row-1 {
		height: 6.3021vw;
		padding-top: 1.25vw;
		padding-bottom: 0.625vw;
		border-bottom: 1px solid #e5e5e5;
		.analysis-TableShowItem-set-margin {
			margin-top: 0.4688vw;
		}
	}
	.table-row-remain {
		padding-top: .5208vw;
		padding-bottom: .625vw;
		border-bottom: 1px solid #e5e5e5;
	}
	.table-row-remain:last-child{
		border: none;
	}
}
</style>
