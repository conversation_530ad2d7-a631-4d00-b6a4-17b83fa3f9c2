<template>
	<div id="quickQuote">
		<div class="modal-box">
			<div class="left">
				<h1>{{ cateInfo.cateName }}</h1>
				<div class="imgWrap">
					<img :src="cateInfo.imagePhoto" :alt="cateInfo.cateName" />
				</div>
				<ul class="tip">
					<li>
						<b class="icon-jxsht-zdgl-dg"></b>
						<span><strong>Free</strong> Shipping</span>
					</li>
					<li>
						<b class="icon-jxsht-zdgl-dg"></b>
						<span><strong>Free</strong> Proof </span>
					</li>
					<li>
						<b class="icon-jxsht-zdgl-dg"></b>
						<span><strong>No</strong> MOQ</span>
					</li>
				</ul>
				<div class="des" v-if="cateInfo.description">
					<div class="title">
						<strong>Description:</strong>
					</div>
					<div class="des-con">
						<p v-for="item in JSON.parse(cateInfo.description)">{{ item.str }}</p>
					</div>
				</div>
			</div>
			<div class="right">
				<div class="bar">
					Remark: This is a quick quote page. To view the professional quoting system, please
					<nuxt-link :to="quoteLink">click here.</nuxt-link>
				</div>
				<h1>{{ cateInfo.cateName }}</h1>
				<div>
					<lanyardStepWrap v-if="pid === 12" class="stepList" :generalData="generalData" :stepName="stepName" @showSizeDialog="showSizeDialog($event)" @clickNext="clickNext($event)" @selectQuoteParams="selectQuoteParams($event.item, $event.citem)" @selectQtyList="selectQtyList($event)" @updatePrice="debounceCalcPrice" @selectQty="selectQty" @setIsLater="isLater = $event" @uploadPic="uploadPic"></lanyardStepWrap>
					<stepWrap v-else class="stepList" :generalData="generalData" :stepName="stepName" @showSizeDialog="showSizeDialog($event)" @clickNext="clickNext($event)" @selectQuoteParams="selectQuoteParams($event.item, $event.citem)" @selectQtyList="selectQtyList($event)" @updatePrice="debounceCalcPrice" @selectQty="selectQty" @setIsLater="isLater = $event" @uploadPic="uploadPic"></stepWrap>
					<div class="sub-detail">
						<div class="subtotal-left">
							<div class="sub-item">
								<div class="sub-item-left">{{ lang.Quantity }}:</div>
								<div class="sbu-item-right">
									{{ customQty || 0 }}<span v-if="presentedQuantity > 0 && presentedQuantity <= giftQuantity"> + </span><span style="color: #ff0000" v-if="presentedQuantity > 0 && presentedQuantity <= giftQuantity">{{ presentedQuantity }} Free</span>
								</div>
							</div>
							<div class="sub-item" v-show="!onlyAddInquiry">
								<div class="sub-item-left">{{ lang.unitPrice }}:</div>
								<div class="sbu-item-right">
									<CCYRate :price="priceInfo.foundationUnitPrice"></CCYRate>
								</div>
							</div>
							<div class="sub-item" v-show="!onlyAddInquiry">
								<div class="sub-item-left">{{ lang.moldPrice }}:</div>
								<div class="sbu-item-right">
									<CCYRate :price="priceInfo.toolingCharge"></CCYRate>
								</div>
							</div>
							<div class="sub-item" v-show="!onlyAddInquiry && discountPrice != 0">
								<div class="sub-item-left">{{ text1 }}</div>
								<div class="sbu-item-right">
									{{ text2 }}
									<CCYRate :price="discountPrice"></CCYRate>
								</div>
							</div>
						</div>
						<div class="subtotal-right">
							<div class="totalPriceBox">
								<strong>{{ lang.subtotal }}:</strong>
								<CCYRate class="final-price" :price="priceInfo.totalPrice"></CCYRate>
								<CCYRate class="before-discount-price" :price="subtotal" v-if="priceInfo.totalPrice !== subtotal"></CCYRate>
							</div>
							<div class="btnGroup">
								<QuoteBtn bgColor="linear-gradient(to top, #FF412B 0%, #FF7743 100%)" @click.native="addInquiry">
									{{ lang.submitInquiry }}
									<el-tooltip popper-class="cusToolTip" effect="light" :content="textInfo.inquiryTip">
										<b class="icon-wenhao3 tip-icon" @click.stop></b>
									</el-tooltip>
								</QuoteBtn>
								<QuoteBtn bgColor="linear-gradient(to top, #0066CC 0%, #2FB6F5 100%)" v-if="onlyAddInquiry === 0 || !onlyAddInquiry" @click.native="addCart">
									{{ lang.addToCart }}
									<el-tooltip popper-class="cusToolTip" effect="light" :content="textInfo.addCartTip">
										<b class="icon-wenhao3 tip-icon" @click.stop></b>
									</el-tooltip>
								</QuoteBtn>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<base-dialog v-model="sizeDialog" class="sizePreviewDialog" :width="device !== 'mb' ? '750px' : '95%'">
			<div class="sizePreviewDialogContent">
				<div class="header">Size Guideline</div>
				<div class="body">
					<div class="des" style="color: #666666">
						<p>Refer to the size comparison chart below for a quicker overview of each size. Besides, our team of professional designers will give you free help with design and sizing.</p>
						<p>If you have any more questions, please feel free to <a :href="`mailto:${userEmail}`">contact us</a>.</p>
					</div>
					<div class="previewCon">
						<div class="left-size">
							<div :class="{ active: dialogSizeItem && dialogSizeItem.id === item.id }" v-for="item in sizeList" :key="item.id" @click="selectSize(item)">
								{{ item.alias }}
							</div>
							<div :class="{ active: dialogSizeItem && dialogSizeItem.id === item.id }" v-for="item in lanyardSizeHList" :key="item.id" @click="selectLanyardHeight(item)">
								{{ item.alias }}
							</div>
						</div>
						<div class="right-preview-image">
							<div class="textWrap">
								<p class="normal-text">Size View</p>
							</div>
							<div class="shape-img">
								<img :src="shapeImg" alt="" />
							</div>
						</div>
					</div>
				</div>
				<div class="footer">
					<QuoteBtn :disabled="!dialogSizeItem" @click.native="confirmSize">Confirm</QuoteBtn>
				</div>
			</div>
		</base-dialog>
		<!--			询盘弹窗-->
		<infoDialog :infoDialogVisible.sync="infoDialogVisible" :uploadList.sync="uploadList" :otherUpload="uploadArtworkList" @getValue="getValueFun"></infoDialog>
		<!--    遮罩-->
		<myMask v-if="device === 'mb'" style="z-index: 1000" :maskName.sync="stepName"></myMask>
		<BaseDialog v-model="noFileDialog" :width="device !== 'mb' ? '485px' : '90%'" :model="false">
			<template #closeIcon>
				<div style="display: none"></div>
			</template>
			<infoUpload :infoUploadList="infoUploadList" @pushInfoList="pushInfoList" @delInfoList="delInfoList" @updateInquiry="updateInquiry" @closeInfoDialog="closeInfoDialog"></infoUpload>
		</BaseDialog>
	</div>
</template>

<script>
import "@/plugins/element";
import CustomCircle from "@/components/Quote/customCircle.vue";
import QuoteBtn from "@/components/Quote/QuoteBtn";
import CCYRate from "@/components/CCYRate.vue";
import InfoDialog from "@/components/Medals/infoDialog.vue";
import BaseDialog from "@/components/Quote/BaseDialog.vue";
import InfoUpload from "@/components/Medals/infoUpload.vue";
import myMask from "@/components/Quote/Mask";
import stepWrap from "~/components/QuickQuote/stepWrap.vue";
import lanyardStepWrap from "~/components/QuickQuote/lanyardStepWrap.vue";
import { uploadFile } from "@/utils/oss";
import { getCateParamRelationByCateId } from "@/api/web";
import { getInfo, calculateAll, calculate, otoAddCart, otoEditInquiry, setInquiry } from "@/api/pins";
import {debounce, deepClone, round2, scrollToViewTop} from "@/utils/utils";
import { routeMap, usRouteMap } from "@/assets/js/quickQuoteRouteMap";
import { checkFile, acceptFileType } from "@/utils/validate";
import {findSelectDiscount, getIsSmallQty} from "assets/js/QuotePublic";

const setIndex = (data) => {
	if (!data) {
		return false;
	}
	let index = 0;
	data.forEach((item) => {
		if (item) {
			if (!item.noShowDetail) {
				index += 1;
			}
			item.customIndex = index;
		}
	});
	return data;
};
const sortByKey = (array, key) => {
	return array.sort(function (a, b) {
		let x = a.priceInfo[key]; //如果要从大到小,把x,y互换就好
		let y = b.priceInfo[key];
		return x < y ? -1 : x > y ? 1 : 0;
	});
};

const getMyCustomStepName = (item) => {
	let paramName = item.paramName,
		type = item.paramType;
	let qtyNameArr = ["qty", "Quantity"],
		attachNameArr = ["Select Metal Finish", "Select Plating Colors", "Select Attachment","Keychain Printed Area","Edge", "Patch Shape", "Printing Method", "Patch Style", "PVC Patch Shape", "Lanyard Attachment", "Patch Backing", "Color", "Patch Border", "2D or 3D", "Plating", "Ornament Attachment", "Select Link & Chain Options", "Link & Chain Options", "Fidget Spinner Plating"],
		uploadNameArr = ["Upload Artwork & Comments", "Design Your Printing"];
	if (type === "SIZE") {
		return "size";
	}
	if (type === "COLOR") {
		return "color";
	}
	if (type === "DISCOUNT") {
		return "discount";
	}
	if (paramName === "Ribbon") {
		return "ribbon";
	}
	if (qtyNameArr.includes(paramName)) {
		return "qty";
	}
	if (attachNameArr.includes(paramName)) {
		return "attachment";
	}
	if (uploadNameArr.includes(paramName)) {
		return "upload";
	}
};

const addCustomProperty = (data) => {
	let handle = (list) => {
		for (let i = 0; i < list.length; i++) {
			let item = list[i];
			item.remark = "";
			item.noShowDetail = false;
			item.isHidden = false; //不可选属性
			item.files = [];
			item.inputNum = undefined;
			if (item.childList && item.childList.length) {
				handle(item.childList);
			}
			item.customStepName = getMyCustomStepName(item);
			//特殊处理
			if (item.paramName === "Upload Artwork & Comments") {
				item.alias = "Upload Your Artwork";
			}
			if (item.paramName === "Lanyard Popular Colors") {
				item.noShowDetail = true;
				item.childList.forEach((c) => {
					c.giftQuantity = undefined;
				});
			}
			if (item.paramName === "Lanyard Attachment") {
				item.childList[0].customIndex = 3;
			}
		}
	};
	handle(data);
	return data;
};
export default {
	provide() {
		return {
			getProvideData: () => {
				return {
					cateInfo: this.cateInfo,
					customQty: this.customQty,
					selectedData: this.selectedData,
					stepName: this.stepName,
					isCustom: this.isCustom,
					qtyList: this.qtyList,
					selectedQtyInd: this.selectedQtyInd,
					customNumberPrice: this.customNumberPrice,
					customNumberUnitPrice: this.customNumberUnitPrice,
					uploadArtworkList: this.uploadArtworkList,
					isLater: this.isLater,
					isFullReductionActivity: this.isFullReductionActivity,
					satisfiedQuantity: this.satisfiedQuantity,
					giftQuantity: this.giftQuantity,
					priceInfo: this.priceInfo,
				};
			},
			customObj: this.customObj,
		};
	},
	props: ["isUs"],
	data() {
		return {
			acceptFileType,
			noFileDialog: false,
			onlyAddInquiry: 0,
			sizeDialog: false,
			uploadArtworkList: [],
			uploadList: [],
			infoUploadList: [],
			customObj: {
				customNumber: "",
			},
			selectedParamsValueParent: {},
			selectedParamsValue: {},
			isCustom: false,
			isDs: 0,
			isFastQuote: 1,
			loadAddCart: false,
			isInquiry: false,
			infoDialogVisible: false,
			inquiryId: 0,
			debounceCalcPrice: null,
			debounceAddInquiry: null,
			dialogSizeItem: null,
			tempSizeItemParent: null,
			mapMessage: {
				upload: "Please Upload your Artwork.",
				size: "Please Select Your Size.",
				qty: "Please Select Your Qty.",
				attachment: "Please Select Your Attachment.",
				discount: "Please Select your discount.",
			},
			stepName: "",
			cateInfo: {},
			generalData: [],
			isLater: false,
			qtyList: [],
			priceInfo: {
				isSmallWeight: 1,
			},
			sizeList: [],
			lanyardSizeHList: [],
			selectedData: {},
			selectedQtyInd: -1,
			pid: "",
			isFullReductionActivity: 0,
			satisfiedQuantity: Number.MAX_VALUE,
			giftQuantity: 0,
			isLanyardHeight: false,
		};
	},
	watch: {
		selectedData: {
			handler() {
				this.debounceCalcPrice();
			},
			deep: true,
		},
	},
	components: {
		InfoUpload,
		BaseDialog,
		InfoDialog,
		CustomCircle,
		QuoteBtn,
		CCYRate,
		myMask,
		stepWrap,
		lanyardStepWrap,
	},
	computed: {
		presentedQuantity() {
			return this.selectedData["Lanyard Popular Colors"] && this.selectedData["Lanyard Popular Colors"].reduce((accumulator, currentValue) => Number(accumulator) + Number(currentValue?.giftQuantity || 0), 0);
		},
		lengthData() {
			return this.generalData.find((item) => item.paramName === "Lanyard Length");
		},
		newRouteMap() {
			return this.isUs ? usRouteMap : routeMap;
		},
		userEmail() {
			return this.$store.state.proSystem.email;
		},
		// finalPrice() {
		// 	let priceInfo = this.priceInfo;
		// 	return (Math.round(priceInfo.foundationUnitPrice * priceInfo.discount * 100) / 100) * priceInfo.totalQuantity + priceInfo.toolingCharge;
		// },
		// originPrice() {
		// 	let priceInfo = this.priceInfo;
		// 	return priceInfo.foundationUnitPrice * priceInfo.totalQuantity + priceInfo.toolingCharge;
		// },
		customNumberPrice() {
			return this.isCustom && this.customObj.customNumber ? this.priceInfo.totalPrice : 0;
		},
		customNumberUnitPrice() {
			return this.isCustom && this.customObj.customNumber ? this.priceInfo.foundationUnitPrice : 0;
		},
		cateId() {
			let routeName = this.$route.params.quote;
			return this.newRouteMap[routeName]?.id;
		},
        text1() {
            let discountName = "";
            //加急费，重量加价
            if (this.priceInfo.discountPrice) {
                return this.lang.rushDelivery;
            }
            if (this.priceInfo.discount > 1) {
                return this.lang.rushDelivery;
            } else if (this.priceInfo.discount < 1) {
                discountName = this.lang.discount;
                return `${discountName} (${this.lang.Turnaround}: ${(Math.abs(1 - this.priceInfo.discount) * 100).toFixed(0)}% ${this.lang.p8}):`;
            }
        },
        subtotal() {
            return round2(round2(this.priceInfo.foundationUnitPrice * this.priceInfo.totalQuantity) + this.priceInfo.toolingCharge + (this.priceInfo.setupCharge || 0));
        },
        text2() {
            let ac;
            if (this.priceInfo.totalPrice > this.subtotal) {
                ac = "+";
            } else {
                ac = "-";
            }
            return ac;
        },
        discountPrice() {
            if (this.priceInfo) {
                return `${Math.abs(this.priceInfo.totalPrice - this.subtotal)}`;
            } else {
                return 0;
            }
        },
		quoteLink() {
			let routeName = this.$route.params.quote;
			return this.newRouteMap[routeName]?.quoteLink;
		},
		sizeValue() {
			let findSize = this.generalData.find((item) => {
				return item.paramType === "SIZE";
			});
			if (!findSize) {
				return "";
			}
			return this.selectedData[findSize.paramName] && this.selectedData[findSize.paramName][0]?.paramCode;
		},
		shapeImg() {
			try {
				let defaultUrl = JSON.parse(this.sizeList[0].imageJson)[0].url;
				let sizeItem = this.dialogSizeItem;
				if (sizeItem) {
					return JSON.parse(sizeItem.imageJson)[0].url;
				} else {
					return defaultUrl;
				}
			} catch (e) {
				return "";
			}
		},
		proId() {
			if (this.previewMode) {
				return 148;
			} else {
				return this.$store.state.proId;
			}
		},
		projectName() {
			if (this.previewMode) {
				return "o2o-site-style";
			} else {
				return this.$store.state.proName;
			}
		},
		previewMode() {
			return process.env.VUE_APP_MODE === "Preview";
		},
		proType() {
			return this.$store.state.proType;
		},
		lang() {
			return this.$store.getters.lang.quote || {};
		},
		userUUID() {
			return this.$store.state.userUUID;
		},
		userId() {
			return this.$store.state.userInfo?.id;
		},
		isLogin() {
			return this.$store.getters.isLogin;
		},
		userInfo() {
			return this.$store.state.userInfo;
		},
		device() {
			return this.$store.state.device;
		},
		textInfo() {
			return {
				inquiryTip: this.lang.inquiryTip,
				addCartTip: this.lang.addCartTip,
				emailText: this.lang.emailText + " " + this.$store.state.proSystem.email,
				email: this.lang.mailto + this.$store.state.proSystem.email,
			};
		},
		customQty() {
			if (this.pid === 45) {
				return this.selectedData["Metal Finish"] && this.selectedData["Metal Finish"].reduce((accumulator, currentValue) => Number(accumulator) + Number(currentValue?.inputNum || 0), 0);
			}
			if (this.isCustom) {
				return this.customObj.customNumber;
			} else {
				return (this.qtyList && this.qtyList.length && this.qtyList[this.selectedQtyInd]?.totalQuantity) || 0;
			}
		},
	},
	methods: {
		selectSize(item) {
			this.dialogSizeItem = item;
			this.isLanyardHeight = false;
		},
		selectLanyardHeight(item) {
			this.dialogSizeItem = item;
			this.isLanyardHeight = true;
		},
		parseJSON(str) {
			return str
				? JSON.parse(str)
				: [
						{
							url: "",
						},
				  ];
		},
		clickNext() {
			if (!this.checkParams()) {
				scrollToViewTop(document.getElementById(`${this.stepName}`));
			}
		},
		pushInfoList(data) {
			this.infoUploadList.push(data);
		},
		delInfoList(index) {
			this.infoUploadList.splice(index, 1);
		},
		updateInquiry() {
			if (!this.inquiryId) {
				this.closeInfoDialog(false);
			}
			let newPicPath = this.infoUploadList.map((item) => item.secure_url);
			let data = {
				id: this.inquiryId,
				newPicPath: newPicPath,
			};
			setInquiry(data).then(() => {
				this.closeInfoDialog(false);
			});
		},
		closeInfoDialog(data) {
			this.infoUploadList = [];
			this.noFileDialog = data;
			window.location.href = "/";
		},
		confirmSize() {
			if (this.isLanyardHeight) {
				this.selectQuoteParams(this.lengthData, this.dialogSizeItem);
			} else {
				this.selectQuoteParams(this.tempSizeItemParent, this.dialogSizeItem);
			}
			this.sizeDialog = false;
			this.dialogSizeItem = null;
		},
		selectQtyList(ind) {
			this.isCustom = false;
			this.selectedQtyInd = ind;
			this.customObj.customNumber = "";
			this.calcPrice();
		},
		selectQty() {
			this.isCustom = true;
			this.selectedQtyInd = -1;
		},
		selectQuoteAdditional(item, citem) {
			let param = this.selectedData[item.paramName];
			if (!param) {
				param = [];
			}
			//判断是否已选, 是则取消选中，否则选中
			let findInd = param.findIndex((item) => {
				return item.paramName === citem.paramName;
			});
			if (findInd >= 0) {
				citem.inputNum = undefined;
				param.splice(findInd, 1);
			} else {
				param.push(citem);
			}
			//取消选中 No Upgrades
			let ind = param.findIndex((item) => {
				return item.chooseNum <= 1;
			});
			if (ind >= 0) {
				param[ind].inputNum = undefined;
				param.splice(ind, 1);
			}
			//如果全部取消，默认选中 No Upgrades
			if (!param.length) {
				let fItem = item.childList.find((item) => {
					return item.chooseNum <= 1;
				});
				if (fItem) {
					param = [fItem];
				}
			}
			this.selectedData[item.paramName] = param;
			console.log(this.selectedData[item.paramName]);
		},
		//参数选中事件
		async selectQuoteParams(item, citem) {
			this.selectedParamsValueParent = item;
			this.selectedParamsValue = citem;
			if ((this.pid === 45 && item.paramName === "Metal Finish") || (this.pid === 12 && item.paramName === "Lanyard Popular Colors")) {
				this.selectQuoteAdditional(item, citem);
			} else {
				this.$set(this.selectedData, item.paramName, [citem]);
			}
			//medals报价特殊处理
			if (this.pid === 45 && item.paramName === "Ribbon") {
				//默认选中尺寸和颜色
				if (citem.paramName === "Stock Ribbon") {
					citem.sizeValue = citem.childList[0]?.childList[0];
					citem.colorValue = citem.sizeValue?.childList[0]?.childList[0];
				} else if (citem.paramName === "Custom Ribbon") {
					citem.sizeValue = citem.childList[0]?.childList[0];
					citem.colorValue = null;
				} else {
					citem.sizeValue = null;
					citem.colorValue = null;
				}
			}
		},
		showSizeDialog(item) {
			this.tempSizeItemParent = item;
			this.sizeDialog = true;
		},
		uploadPic(files) {
			this.$gl.show();
			let fileResult = checkFile(Array.from(files), this.acceptFileType, 80);
			if (!fileResult) {
				this.$toast.error("File type error");
				this.$gl.hide();
				return false;
			}
			if (!fileResult.nomalSize.length) {
				this.$store.commit("setSizeDialog", true);
				this.$store.commit("setInputRefName", "replayUpload");
				this.$store.commit("setOverSizeList", fileResult.overSize);
				document.querySelector("#uploadInput").value = "";
				return false;
			}
			let uploadPromises = [];
			fileResult.nomalSize.forEach((file) => {
				let promise = uploadFile(file).then((res) => {
					this.uploadArtworkList.push({
						original_filename: file.name,
						secure_url: res,
						size: (file.size / 1024).toFixed(1),
					});
				});
				uploadPromises.push(promise);
			});
			Promise.all(uploadPromises).then(() => {
				document.querySelector("#uploadInput").value = "";
				if (fileResult.overSize.length > 0) {
					this.$store.commit("setSizeDialog", true);
					this.$store.commit("setInputRefName", "replayUpload");
					this.$store.commit("setOverSizeList", fileResult.overSize);
				}
				this.$gl.hide();
			});
		},
		getQuoteParam(type = "inquiry") {
			let selectedData = deepClone(this.selectedData),
				finaData = [],
				generalData = deepClone(this.generalData);
			for (let i = 0; i < generalData.length; i++) {
				let item = generalData[i];
				if (item.customStepName === "qty") {
					item.childList = [];
					finaData.push(item);
					continue;
				}
				if (item.customStepName === "upload") {
					item.childList = [];
					item.later = this.isLater;
					finaData.push(item);
					continue;
				}
				//将选中参数里面的childlist置空
				if (selectedData[item.paramName] && selectedData[item.paramName].length) {
					selectedData[item.paramName].forEach((c) => {
						c.childList = [];
						c.quantity = c.inputNum;
						if (!c.giftQuantity) {
							c.giftQuantity = 0;
						}
					});
				}
				item.childList = selectedData[item.paramName] || [];
				finaData.push(item);
			}
			let files = [];
			let artworkList = this.uploadArtworkList;
			if (artworkList.length > 0) {
				artworkList.forEach((item) => {
					item.secure_url && files.push(item.secure_url);
				});
			}
			return {
				classificationData: this.cateInfo,
				finaData: finaData,
				fontData: {
					fontImgCustom: files,
					comments: this.remark,
				},
				designCanvas: [],
			};
		},
		getPriceParam(qty) {
			const data = {
				cateId: this.cateId,
				discountId: "",
				packingIdList: [],
				paramIdList: [],
				projectName: this.projectName,
				quantity: "",
				sizeId: "",
				upgradesQtyDTO: [],
				qtyDetailDTOS: [],
			};
			let generalData = this.generalData,
				selectedData = this.selectedData;
			for (let i = 0; i < generalData.length; i++) {
				let item = generalData[i];
				let paramName = item.paramName;
				let selectedVal = selectedData[paramName];
				if (item.customStepName === "qty") {
					data.quantity = qty || Number(this.customQty);
					continue;
				}
				if (item.customStepName === "upload") {
					continue;
				}
				if (!selectedVal) {
					continue;
				}
				selectedVal.forEach((citem) => {
					let paramType = citem.paramType;
					switch (paramType) {
						case "NORMAL":
							//medals报价特殊处理
							if (this.pid === 45 && item.customStepName === "ribbon") {
								if (citem.colorValue && Object.keys(citem.colorValue).length != 0) {
									data.paramIdList.push(citem.colorValue.priceInfo.id);
								}
								if (citem.sizeValue && Object.keys(citem.sizeValue).length != 0) {
									data.paramIdList.push(citem.sizeValue.priceInfo.id);
								}
							} else {
								data.paramIdList.push(citem?.priceInfo?.id);
							}
							break;
						case "DISCOUNT":
							data.discountId = citem?.priceInfo?.id;
							break;
						case "SIZE":
							data.sizeId = citem?.priceInfo?.id;
							break;
						case "COLOR":
							if (citem.inputNum && citem.inputNum > 0) {
								data.qtyDetailDTOS.push({
									quantity: citem.inputNum || 0,
									paramType: "COLOR",
									paramId: citem?.priceInfo?.id,
									paramValue: "",
									giftQuantity: citem.giftQuantity || 0,
								});
							}
							break;
						default:
							break;
					}
				});
			}
			if (!data.qtyDetailDTOS.length) {
				delete data.qtyDetailDTOS;
			}
			return data;
		},
		calcPrice() {
			let priceParam = this.getPriceParam();
			if (priceParam.sizeId && priceParam.cateId) {
				calculateAll(priceParam).then((res) => {
					this.qtyList = res.data.filter((item) => item.isFastQuote);
				});
			}
			if (priceParam.sizeId && priceParam.cateId && (priceParam.quantity || priceParam.qtyDetailDTOS?.length)) {
				calculate(priceParam).then((res) => {
					this.priceInfo = res.data;
				});
			}
		},
		checkParams() {
			let verify = true,
				stepName = "",
				selectedData = this.selectedData,
				generateData = this.generalData;
			for (let i = 0; i < generateData.length; i++) {
				let item = generateData[i];
				//参数名称
				let paramName = item.paramName;
				//选中的参数值
				let selectedVal = selectedData[paramName];
				if (item.customStepName === "upload") {
					if (!this.uploadArtworkList.length && !this.isLater) {
						stepName = item.customStepName;
						verify = false;
						break;
					}
				} else if (item.customStepName === "qty") {
					if (!this.customQty || this.customQty <= 0) {
						stepName = item.customStepName;
						verify = false;
						break;
					}
				} else if (item.customStepName === "color") {
					let temp = selectedVal;
					if (!temp || !temp.length) {
						stepName = item.customStepName;
						verify = false;
						break;
					}
					let result = temp.every((item) => {
						return item.inputNum > 0;
					});
					if (!result) {
						stepName = item.customStepName;
						verify = false;
						break;
					}
					if (this.pid == 12) {
						let presentedQuantity = this.selectedData["Lanyard Popular Colors"] && this.selectedData["Lanyard Popular Colors"].reduce((accumulator, currentValue) => Number(accumulator) + Number(currentValue?.giftQuantity || 0), 0);
						if (presentedQuantity > this.giftQuantity) {
							stepName = item.customStepName;
							verify = false;
							this.$Bus.$emit("showGiftDialog");
							break;
						}
					}
				} else if (item.customStepName === "ribbon") {
					//medals报价特殊校验
					let temp = selectedVal;
					if (!temp.length) {
						stepName = item.customStepName;
						verify = false;
						break;
					} else {
						if (temp[0].paramName == "Stock Ribbon") {
							if (!temp[0].colorValue || !temp[0].sizeValue) {
								stepName = item.customStepName;
								verify = false;
								break;
							}
						} else if (temp[0].paramName == "Custom Ribbon") {
							if (!temp[0].sizeValue) {
								stepName = item.customStepName;
								verify = false;
								break;
							}
						}
					}
				} else {
					if (!selectedVal || selectedVal.length === 0) {
						stepName = item.customStepName;
						verify = false;
						break;
					}
				}
			}
			this.stepName = stepName;
			return verify;
		},
		addInquiry() {
			if (!this.checkParams()) {
				this.$toast.error(this.mapMessage[this.stepName] || "Please improve the parameters");
				scrollToViewTop(document.getElementById(`${this.stepName}`));
				return false;
			}
			//如果是临时项目
			if (this.$store.state.isTemporary) {
				this.$store.commit("setMask", "temporary");
				return false;
			}
			this.infoDialogVisible = true;
		},

		getValueFun(val) {
			let quoteParam = this.getQuoteParam();
			let priceParam = this.getPriceParam();
			//询盘如果有期待时间，finaData手动添加数据
			if (val.expectTime) {
				quoteParam.finaData.push({
					parentId: 0,
					alias: "Expected Delivery Date",
					childList: [
						{
							parentId: 10000,
							alias: val.expectTime,
						},
					],
				});
			}
			let data = {
				platformProductId: this.cateInfo.platformProductId,
				proId: this.proId,
				email: "",
				productsName: this.projectName,
				quoteCateId: this.pid,
				quoteCateChildId: priceParam.cateId,
				isMobile: this.device === "mb" ? 1 : 0,
				buyType: 7,
				quotePriceParam: JSON.stringify(priceParam),
				quoteParam: JSON.stringify(quoteParam),
				...val,
				telephone: val.areaCode + "-" + val.telephone,
				isDs: this.isDs,
				isFastQuote: this.isFastQuote,
                isSmallQty: getIsSmallQty(findSelectDiscount(this.selectedData),this.$store.state.enableTurnaroundTimeCheck)
			};
			this.otoEditInquiry(data);
		},

		otoEditInquiry(data) {
			if (this.isInquiry) {
				return false;
			}
			this.isInquiry = true;
			otoEditInquiry(data)
				.then((res) => {
					this.inquiryId = res.data;
					if (!this.uploadArtworkList.length && !this.uploadList.length) {
						this.noFileDialog = true;
					} else {
						this.$confirm(this.lang.p22, this.lang.p21, {
							confirmButtonText: this.lang.Confirm,
							type: "success",
							showCancelButton: false,
							center: true,
							customClass: "inquirySuccess",
							confirmButtonClass: "inquirySuccessBtn",
						}).finally(() => {
							window.location.href = "/";
						});
					}
				})
				.finally(() => {
					this.isInquiry = false;
				});
		},

		async addCart() {
			if (!this.checkParams()) {
				this.$toast.error(this.mapMessage[this.stepName] || "Please improve the parameters");
				scrollToViewTop(document.getElementById(`${this.stepName}`));
				return false;
			}
			//如果是临时项目
			if (this.$store.state.isTemporary) {
				this.$store.commit("setMask", "temporary");
				return false;
			}
			if (this.loadAddCart) {
				return false;
			}
			this.loadAddCart = true;
			let quoteParam = this.getQuoteParam("cart");
			let priceParam = this.getPriceParam();
			let data = {
				proId: this.proId,
				uuid: this.isLogin ? null : this.userUUID,
				userId: this.isLogin ? this.userId : null,
				buyType: 7,
				quoteCateId: this.pid,
				isMobile: this.device === "mb" ? 1 : 0,
				quoteCateChildId: priceParam.cateId,
				quantity: this.customQty,
				quotePriceParam: JSON.stringify(priceParam),
				quoteParam: JSON.stringify(quoteParam),
				isDs: this.isDs,
				isFastQuote: this.isFastQuote,
                isSmallQty: getIsSmallQty(findSelectDiscount(this.selectedData),this.$store.state.enableTurnaroundTimeCheck)
			};
			otoAddCart(data, this.priceInfo)
				.then((res) => {
					this.$toast.success(res.message);
					this.$router.push({
						path: "/cart",
					});
				})
				.finally(() => {
					setTimeout(() => {
						this.loadAddCart = false;
					}, 1000);
				});
		},
		clearDiscount() {
			let findDiscount = this.generalData.find((item) => item.paramType === "DISCOUNT");
			if (findDiscount) {
				let name = findDiscount.paramName;
				this.selectedData[name] = [];
			}
		},
	},
	async created() {
		this.debounceAddInquiry = debounce(this.addInquiry, 300);
		this.debounceCalcPrice = debounce(this.calcPrice, 300);
	},
	async mounted() {
		try {
			this.$Bus.$on("clearDiscount", this.clearDiscount);
			this.$gl.show();
			let routeName = this.$route.params.quote;
			let cateId = this.newRouteMap[routeName]?.id,
				pid = this.newRouteMap[routeName]?.pid;
			this.pid = pid;
			let result = await Promise.all([
				getInfo({ id: cateId }),
				getCateParamRelationByCateId({
					cateId: cateId,
					isFastQuote: 1,
				}),
			]);
			this.cateInfo = result[0]?.data || {};
			this.sizeList = result[1]?.data.find((item) => item.paramType === "SIZE")?.childList || [];
			if (this.pid === 12) {
				this.lanyardSizeHList = result[1]?.data.find((item) => item.paramName === "Lanyard Length")?.childList || [];
			}
			let selectedData = {},
				isLater = false,
				selectedQtyInd = -1,
				selectedQty = 0,
				generalData = setIndex(sortByKey(addCustomProperty(result[1]?.data), "fastQuoteStepIndex"));
			//默认选中参数
			if (generalData?.length) {
				isLater = true;
				generalData.forEach((item) => {
					let customStepName = item.customStepName;
					if (customStepName !== "qty" && customStepName !== "upload") {
						let findDefault = item.childList.find((c) => c.priceInfo.isFastQuoteSelected);
						if (findDefault) {
							selectedData[item.paramName] = [findDefault];
							//选中尺寸下的默认数量
							if (customStepName === "size") {
								try {
									let priceInfo = findDefault.priceInfo;
									let increasePrice = JSON.parse(priceInfo.increasePrice).filter((item) => item.isFastQuote);
									let defaultQtyIndex = increasePrice.findIndex((q) => q.isFastQuoteSelected);
									if (defaultQtyIndex > -1) {
										selectedQtyInd = defaultQtyIndex;
										selectedQty = increasePrice[defaultQtyIndex].quantity;
									}
								} catch (e) {}
							}
							if (customStepName === "color") {
								findDefault.inputNum = 100;
							}
							if (customStepName === "ribbon") {
								findDefault.sizeValue = findDefault.childList[0].childList[0];
								findDefault.colorValue = findDefault.sizeValue.childList[0].childList[0];
							}
							if (this.pid === 12 && customStepName === "attachment") {
								let childList = findDefault.childList;
								let findDefault1 = childList.find((c) => c.priceInfo.isFastQuoteSelected);
								selectedData[findDefault.paramName] = [findDefault1];
							}
						}
					}
				});
			}
			this.selectedData = selectedData;
			this.generalData = generalData;
			this.isLater = isLater;
			this.selectedQtyInd = selectedQtyInd;
			let priceParam = this.getPriceParam(selectedQty),
				priceResult;
			priceResult = await Promise.all([calculateAll(priceParam), calculate(priceParam)]);
			this.qtyList = (priceResult && priceResult[0].data.filter((item) => item.isFastQuote)) || [];
			this.priceInfo = (priceResult && priceResult[1].data) || {};
			if (this.pid === 12) {
				getInfo({ id: 12 }).then((res) => {
					this.isFullReductionActivity = res.data.isFullReductionActivity;
					this.satisfiedQuantity = res.data.satisfiedQuantity;
					this.giftQuantity = res.data.giftQuantity;
				});
			}
			this.$gl.hide();
		} catch (e) {
			console.log(e);
		}
	},
	beforeDestroy() {
        this.$Bus.$off("clearDiscount");
    },
};
</script>
<style scoped lang="scss">
.sizePreviewDialogContent {
	padding: 10px;

	.header {
		padding: 10px;
		font-size: 24px;
		font-weight: 700;
		text-align: center;

		@include respond-to(mb) {
			font-size: 16px;
		}
	}

	.body {
		padding: 0 20px;

		p {
			margin-bottom: 10px;
		}
	}

	.des a {
		color: $color-primary;
		text-decoration: underline;
	}

	.previewCon {
		display: grid;
		grid-template-columns: 150px 1fr;
		align-items: flex-start;
		grid-gap: 40px;
		color: #333333;

		@include respond-to(mb) {
			grid-template-columns: 1fr;
			grid-gap: 10px;
		}

		.left-size {
			@include respond-to(mb) {
				display: grid;
				grid-template-columns: repeat(4, 1fr);
				grid-gap: 5px;
			}

			& > div {
				display: flex;
				align-items: center;
				padding: 0 10px;
				min-height: 30px;
				background: #f6f6f6;
				border-radius: 4px;
				margin-bottom: 6px;
				transition: all 0.3s;
				cursor: pointer;

				@include respond-to(mb) {
					padding: 0 5px;
					justify-content: center;
				}

				@media (any-hover: hover) {
					&:hover {
						background-color: $color-primary;
						color: #ffffff;
					}
				}

				&.active {
					background-color: $color-primary;
					color: #ffffff;
				}
			}
		}

		.right-preview-image {
			display: flex;
			flex-direction: column;
			position: relative;
			padding: 20px;
			background-color: #f6f6f6;
			border-radius: 10px;

			@include respond-to(mb) {
				margin: 0;
			}

			&::before {
				position: absolute;
				left: -20px;
				top: 20px;
				content: "";
				width: 0;
				height: 0;
				border-width: 20px 0 20px 20px;
				border-style: solid;
				border-color: transparent transparent #f6f6f6;
			}

			.textWrap {
				text-align: left;

				.normal-text {
					margin-bottom: 8px;
					font-size: 18px;
					color: #333333;
					transition: all 0.3s;

					@include respond-to(mb) {
						font-size: 12px;
					}
				}
			}

			.shape-img {
				flex: 1;
				display: flex;
				justify-content: center;
				align-items: center;

				img {
					height: 320px;
					object-fit: contain;

					@include respond-to(mb) {
						height: 214px;
					}
				}
			}
		}
	}

	.footer {
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 20px 0 10px;
	}
}

#quickQuote {
	font-size: 14px;

	::v-deep b {
		font-size: 16px;
	}

	::v-deep .icon-wenhao3 {
		font-weight: 600;
		color: #666666;
	}

	@include respond-to(mb) {
		font-size: 12px;
	}

	::v-deep .v-application--wrap {
		min-height: auto;
	}

	.modal-box {
		display: grid;
		grid-template-columns: 1.25fr 2fr;
		grid-gap: 60px;
		padding-top: 20px;
		padding-bottom: 20px;
		z-index: auto;

		@include respond-to(mb) {
			grid-template-columns: 1fr;
			grid-gap: 0;
			padding: 0;
		}

		.left {
			flex: 1;
			@include respond-to(mb) {
				order: 2;
				padding: 10px 18px;
				background-color: #ffffff;
			}

			h1 {
				margin-bottom: 10px;
				font-weight: bold;
				font-size: 36px;
				color: #333333;
				@include respond-to(mb) {
					display: none;
				}
			}

			.imgWrap {
				margin-bottom: 20px;
				aspect-ratio: 530/372;
				background-color: #f5f5f5;

				img {
					width: 100%;
					height: 100%;
					object-fit: cover;
				}
			}

			.tip {
				display: grid;
				justify-content: center;
				grid-template-columns: repeat(3, 1fr);
				grid-gap: 20px;
				margin-bottom: 20px;
				font-size: 14px;
				padding-left: 0;
				margin-left: 0;
				list-style: none;
				text-align: center;

				@include respond-to(mb) {
					grid-gap: 10px;
					font-size: 12px;
				}

				b {
					margin-right: 4px;
					color: #68bd2c;
				}
			}

			.des {
				.title {
					margin-bottom: 10px;
					font-size: 16px;
				}

				.des-con {
					p {
						margin: 0 0 10px 0;
						line-height: 1.4em;
						font-size: 14px;
					}
				}
			}
		}

		.right {
			overflow: hidden;
			flex: 2;
			background: #ffffff;
			border-radius: 5px;
			border: 1px solid #dcdfe6;

			h1 {
				display: none;
				@include respond-to(mb) {
					display: block;
					padding: 20px 0 10px;
					font-size: 18px;
					text-align: center;
				}
			}

			@include respond-to(mb) {
				order: 1;
				border: none;
				border-radius: 0;
			}

			.bar {
				height: 30px;
				line-height: 30px;
				padding: 0 10px;
				background: #1469b3;
				color: #ffffff;
				font-size: 14px;

				a {
					color: #fff600;
					text-decoration: underline;
				}

				@include respond-to(mb) {
					height: auto;
					padding: 10px 20px;
					line-height: 1.2;
					font-size: 12px;
					text-align: center;
				}
			}

			.stepList ::v-deep {
				display: grid;
				grid-template-columns: 1fr 1.2fr;
				grid-template-areas: "a b";
				grid-gap: 10px; /* 可选：设置行和列之间的间距 */
				padding: 10px;

				@include respond-to(mb) {
					grid-template-columns: repeat(1, 1fr);
					grid-template-areas: none;
					padding: 0 10px;
				}

				@include respond-to(mb) {
					.step-item.mask {
						position: relative;
						z-index: 1001;
						padding: 10px 0;

						.confirmBtnWrap {
							margin-top: 10px;
						}

						.box-border {
							position: absolute;
							left: -10px;
							right: -10px;
							top: 0;
							bottom: 0;
							display: block;
							background-color: #fff;
							z-index: -1;
						}
					}

					.step-size.mask,
					.step-qty.mask {
						padding: 10px;
					}
				}
			}

			.sub-detail {
				display: grid;
				grid-template-columns: repeat(2, 1fr);
				align-items: center;
				border-radius: 10px;
				padding: 10px;
				background: #f6f6f6;
				grid-gap: 20px;
				margin: 10px;

				@include respond-to(mb) {
					grid-template-columns: repeat(1, 1fr);
					grid-gap: 5px;
					border-radius: 0;
					margin: 0;
					background-color: #ebebeb;
				}

				.subtotal-left {
					.sub-item {
						display: flex;
						align-items: center;
						margin-bottom: 8px;

						@include respond-to(mb) {
							justify-content: space-between;
						}

						&:last-child {
							margin-bottom: 0;
						}

						.sub-item-left {
							width: 110px;
							margin-right: 38px;
						}
					}
				}

				.subtotal-right {
					text-align: center;

					.btnGroup {
						display: flex;
						margin-top: 10px;

						button {
							flex: 1;
							margin: 0 5px;
							border-radius: 4px;
							background: transparent;
							border: none;

							b {
								margin-left: 4px;
								color: #ffffff;
							}
						}
					}

					.final-price {
						margin: 0 10px;
						font-size: 24px;
						color: #e6252e;
					}

					.before-discount-price {
						text-decoration: line-through;
					}
				}
			}
		}
	}
}
</style>