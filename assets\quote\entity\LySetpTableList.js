export class baseModel {
	//遮罩
	maskName = "";
	cssTime = undefined; //定时任务
	nextStepName = undefined; //步骤id
	//遮罩瞄点集合
	maskNameKeyArr = [];
	textUploadError = undefined; //图片上传未选中文案

	//颜色集合列表
	colorModel = {
		oldList: [],
		newList: [],
	};

	//Select Turnaround Time评论
	commentModel = {
		remark: undefined, //评论内容
		files: [],
	};

	priceTextCss = {
		normaltext: "font-size: 14px;", //修改价格行内样式
	};
	/* list = [
		{
			steplabel: "STEP 1",
			name: "Select Material",
			css: {
				rowColumns: 3,
			},
			type: "NORMAL",
			mobileCss: {},
			children: [
				{
					//checked:true,  是否选中
					img: process.env.imgServer + "quoteManage/20241205/Material-Metal_203605kdFCaC.png",
					desc: "Metal",
				},
				{
					img: process.env.imgServer + "quoteManage/20241205/Material-Plastic_203605cYH4WK.png",
					desc: "Plastic",
				},
			],
		},
		{
			steplabel: "STEP 2",
			name: "Select Shape",
			css: {
				rowColumns: 5,
			},
			mobileCss: {},
			type: "NORMAL",
			children: [
				{ img: process.env.imgServer + "quoteManage/20241205/Shape-Rectangle_203605BaSYMx.png", desc: "Rectangle" },
				{
					img: process.env.imgServer + "quoteManage/20241205/Shape-Oval_203605f4MnNp.png",
					desc: "Oval",
				},
				{ img: process.env.imgServer + "quoteManage/20241205/Shape-Square_203605pQfJ4p.png", desc: "Square" },
				{ img: process.env.imgServer + "quoteManage/20241205/Shape-Circle_2036057pFaY7.png", desc: "Circle" },
				{ img: process.env.imgServer + "quoteManage/20241205/Shape-Custom_Shape_203605f5GxB6.png", desc: "Custom Shape" },
			],
		},
		{
			steplabel: "STEP 3",
			name: "Select Color",
			css: {
				rowColumns: 5,
			},
			mobileCss: {},
			type: "NORMAL",
			children: [
				{ img: process.env.imgServer + "quoteManage/20240912/sales_sheet_bar_20240912dGdHzi.png", desc: "Shiny Gold" },
				{ img: process.env.imgServer + "quoteManage/20240912/sales_sheet_bar_20240912dGdHzi.png", desc: "Shiny Sliver" },
				{ img: process.env.imgServer + "quoteManage/20240912/sales_sheet_bar_20240912dGdHzi.png", desc: "Shiny Rose Gold" },
			],
		},
		{
			steplabel: "STEP 4",
			name: "Select Size",
			css: {
				rowColumns: 3,
			},
			mobileCss: {},
			type: "NORMAL",
			children: [
				{ img: process.env.imgServer + "quoteManage/20241205/1_×3_2036053QmaSP.jpg", desc: "1''×3''" },
				{ img: process.env.imgServer + "quoteManage/20241205/05_x_3_203605EhZ6sC.jpg", desc: '1/2" x 3"' },
				{ img: process.env.imgServer + "quoteManage/20241205/2x_3_203605icQHz2.jpg", desc: '2" x 3"' },
			],
		},
		{
			steplabel: "STEP 5",
			name: "Select Imprint Method",
			css: {
				rowColumns: 3,
			},
			mobileCss: {},
			type: "NORMAL",
			children: [
				{ img: process.env.imgServer + "quoteManage/20241205/Imprint_Method-Engraved_203605nGWrAn.jpg", desc: "Engraved" },
				{ img: process.env.imgServer + "quoteManage/20241205/Imprint_Method-Full_Color_203605Gjrhmz.png", desc: "Full Color" },
			],
		},
		{
			steplabel: "STEP 6",
			name: "Additional Upgrades (Optional)",
			title: "Transparent domes protect badges’ text and graphics and provide a finished look. Scratch and dent resistant.",
			css: {
				rowColumns: 3,
			},
			mobileCss: {},
			type: "NORMAL",
			children: [
				{ img: process.env.imgServer + "quoteManage/20241205/Optional-No_Upgrades_203605hpQe4b.jpg", desc: "No Upgrades" },
				{ img: process.env.imgServer + "quoteManage/20241205/Optional-Add_a_Dome_203605DBQEMK.jpg", desc: "Add a Dome" },
			],
		},
		{
			steplabel: "STEP 7",
			name: "Add Texts & Upload Image",
			css: {
				rowColumns: 3,
			},
			mobileCss: {},
			type: "TEXTUPLOAD", //文本和上传textUpload
			textUploadList: [
				{
					textHere: undefined, //文本
					fontFamily: undefined, //字体
					bold: undefined, //加粗
					incline: undefined, //倾斜
					color: undefined, //颜色
				},
			],
			isEmail: false,
			children: [
				{ img: process.env.imgServer + "quoteManage/20241205/Add_Texts__Upload_Image-Text__Clipart_203605cxFAxm.jpg", desc: "Text & Clipart" },
				{ img: process.env.imgServer + "quoteManage/20241205/Add_Texts__Upload_Image-Only_Text_203605pQRhkY.jpg", desc: "Only Text" },
			],
		},
		{
			steplabel: "STEP 8",
			name: "Select Fastener Type",
			css: {
				rowColumns: 3,
			},
			mobileCss: {},
			type: "NORMAL",
			children: [
				{ img: process.env.imgServer + "quoteManage/20241205/Fastener_Type-Magnet_203605ppNpQm.jpg", desc: "Magnet" },
				{ img: process.env.imgServer + "quoteManage/20241205/Fastener_Type-Locking_Pin_203605E525fw.jpg", desc: "Locking Pin" },
			],
		},
		{
			steplabel: "STEP 9",
			name: "Select Quantity",
			css: {},
			mobileCss: {},
			type: "QUANTITY", // "quantity",
			quantity: 3,
		},
		{
			steplabel: "STEP 10",
			name: "Select Turnaround Time",
			title: "Please Note: the production time start from proof approved，we do the first proof within 24 hours，and modify proof within 12 hours.",
			css: {
				rowColumns: 2,
			},
			mobileCss: {},
			type: "DISCOUNT", //"discount",
			children: [
				{ name: "Standard Free", tip: "5 to 7 business days for production + 3 days shipping" },
				{ name: "Rush", tip: "3 business days for production+ 3 days shipping", price: "30", unit: "$" },
			],
		},
	]; */
}
