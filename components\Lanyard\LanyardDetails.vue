<template>
	<div class="LanyardDetails" :class="{ footer: footer, noDetails: noDetails }" :style="{ top: cusTop + 'px' }"
		id="detailInfo">
		<div class="topImage">
			<div class="imgWrap">
				<slot></slot>
			</div>
		</div>
		<div v-if="showColor" class="showColor">
			<template
				v-if="selectedData?.['Lanyard Popular Colors']?.length || selectedData?.['Lanyard Design Colors']?.length">
				<div class="title">{{ lang.LanyardColor }}:</div>
				<div class="box custom-shadow custom-scrollbar scrollBar" max-height="170px" always>
					<div class="item pointer" v-for="(item, index) in selectedData['Lanyard Popular Colors']" :key="item.id"
						@click="showPopular(item)">
						<div class="flex align-items-center">
							<div style="width: 39px; height: 58px" :style="{
								backgroundImage: 'url(' + JSON.parse(item.imageJson)[0].url + ')',
							}"></div>
						</div>
						<div>
                            <span>{{ item.alias }}</span>
                            <span v-if="item.alias2">{{ item.alias2 }}</span>
                        </div>
						<div>
							<div class="h-100  align-items-center">
								<div class="h-90 flex align-items-center">{{ item.quantity }} {{ lang.QTY }} </div>
								<div class="flex align-items-center" v-if="item.giftQuantity">{{ lang.lanyard.FullCopy16
								}}:&nbsp;<span style="color:red">+ {{ item.giftQuantity }} pcs</span></div>
								<!-- <el-input-number v-show="item.show" type="number" :controls="false" v-model.number="item.quantity" @change="inputChangeFun($event, index, selectedData['Lanyard Popular Colors'])" /> -->
							</div>
							<div class="flex justify-content-end">
								<b @click="jump('Lanyard Popular Colors')" class="icon-edit--fill colorB3 pointer"></b>
								<b @click="deleteInput($event, index, selectedData['Lanyard Popular Colors'])"
									class="icon-shanchu2 colorB3 pointer" style="margin-left: 10px; color: #b6b0b0"></b>
							</div>
						</div>
					</div>
					<div class="item pointer" v-for="(item, index) in selectedData['Lanyard Design Colors']" :key="item.id"
						@click="showPopular(item)">
						<div class="flex align-items-center">
							<div style="width: 39px; height: 58px" :style="{
								backgroundImage: 'url(' + JSON.parse(item.imageJson)[0].url + ')',
							}"></div>
						</div>
						<div>{{ item.alias }}</div>
						<div>
							<div class="h-100  align-items-center">
								<div class="h-90 flex align-items-center">{{ item.quantity }} {{ lang.QTY }}</div>
								<div class="flex align-items-center" v-if="item.giftQuantity">{{ lang.lanyard.FullCopy10
								}}:&nbsp;<span style="color:red">+ {{ item.giftQuantity }}{{ lang.lanyard.FullCopy8
}}</span></div>
								<!-- <el-input-number v-show="item.show" type="number" :controls="false" v-model.number="item.quantity" @change="inputChangeFun($event, index, selectedData['Lanyard Design Colors'])" /> -->
							</div>
							<div class="flex justify-content-end">
								<b @click="jump('Lanyard Design Colors')" class="icon-edit--fill colorB3 pointer"></b>
								<b @click="deleteInput($event, index, selectedData['Lanyard Design Colors'])"
									class="icon-shanchu2 colorB3 pointer" style="margin-left: 10px; color: #b6b0b0"></b>
							</div>
						</div>
					</div>
				</div>
			</template>
			<template v-if="selectedData?.['color card']?.length">
				<div class="title">{{ lang.lanyard.CustomLanyardColor }}:</div>
				<div class="box custom-shadow custom-scrollbar" max-height="170px" always>
					<div class="item pointer" v-for="(item, index) in selectedData['color card']" :key="item.id"
						@click="showCustom(item)">
						<div class="flex align-items-center">
							<div v-if="!item.isTwoToneCustom" style="width: 39px; height: 58px" :style="{
								backgroundColor: item.code,
							}"></div>
                            <div v-else style="width: 39px; height: 58px" :style="{
								background: item.code,
							}"></div>
						</div>
						<div v-if="!item.isTwoToneCustom">{{ item.pantone }}</div>
                        <div v-else>
                            <span>{{item.mainColor.pantone}}</span>
                            <span>{{item.accentColor.pantone}}</span>
                        </div>
						<div>
							<div class="h-100  align-items-center">
								<div class="h-90 flex align-items-center">{{ item.quantity }} {{ lang.QTY }}</div>
								<div class="flex align-items-center" v-if="item.giftQuantity">{{ lang.lanyard.FullCopy10
								}}: <span style="color:red">+ {{ item.giftQuantity }}{{ lang.lanyard.FullCopy8 }}</span>
								</div>
								<!-- <el-input-number v-show="item.show" type="number" :controls="false" v-model.number="item.quantity" @change="inputChangeFun($event, index, selectedData['color card'])" /> -->
							</div>
							<div class="flex justify-content-end">
								<b @click="jump('color card')" class="icon-edit--fill colorB3 pointer"></b>
								<b @click="deleteInput($event, index, selectedData['color card'])"
									class="icon-shanchu2 colorB3 ml-10 pointer"
									style="margin-left: 10px; color: #b6b0b0"></b>
							</div>
						</div>
					</div>
				</div>
			</template>
		</div>
		<div class="con" v-else>
			<div>
				<p class="title">{{ lang.orderSummary }}</p>
			</div>
			<ul ref="detailList" class="scrollBar custom-scrollbar" :class="{ showMore: showMore }"
				:style="{ 'overflow-y': showMore ? 'hidden' : 'auto' }">
				<template v-for="(val, key, i) in selectedData">
					<li class="detail-item" :key="key" @click="jump(key)" v-if="showList(key)">
						<div class="left">
							<div class="f-left">{{ filter ? getName(key) : key }}<span style="color: red">*</span>:</div>
							<div class="f-right">
								<template v-if="val">
									<template v-if="key == 'Lanyard Style'">
										<span v-for="(item, index) in val" :key="item.id">
											{{ item.cateName || item.cateNameQuote }}
											{{ index + 1 == val.length ? null : ", " }}
										</span>
									</template>
									<template v-else-if="key == 'color card'">
										<span v-for="(item, index) in val" :key="index">
											{{ getPantone(item) }}
											{{ index + 1 == val.length ? null : ", " }}
										</span>
									</template>
									<template v-else-if="key == 'Select Printing Method'">
										<span v-for="(item, index) in val" :key="index">
											{{ item.alias + (item.childList.length ? ('(' + item.childList[0].alias + ')'):'') }}
										</span>
									</template>
									<template v-else>
										<span v-for="(item, index) in val" :key="index"> {{ getAlias(item) }}{{ index + 1 == val.length ? null : ", " }} </span>
									</template>
								</template>
							</div>
						</div>
						<span class="editIcon" v-show="(key === 'Lanyard Style' && lanyardStyle.length > 1) || key !== 'Lanyard Style'">{{ lang.Edit }}<b class="icon-edit--fill"></b></span>
					</li>
				</template>
				<div class="detail-item" v-if="!noText" @click="jump('Design Your Printing')">
					<div class="left">
						<div class="f-left">{{ lang.font }}1:</div>
						<div class="f-right">{{ ropFontFamily1 }}</div>
					</div>
					<span class="editIcon">{{ lang.Edit }}<b class="icon-edit--fill"></b></span>
				</div>
				<div class="detail-item" v-if="!noText" @click="jump('Design Your Printing')">
					<div class="left">
						<div class="f-left">{{ lang.font }}2:</div>
						<div class="f-right">{{ ropFontFamily2 }}</div>
					</div>
					<span class="editIcon">{{ lang.Edit }}<b class="icon-edit--fill"></b></span>
				</div>
				<div class="detail-item" v-if="!noText" @click="jump('Design Your Printing')">
					<div class="left">
						<div class="f-left">{{ lang.withText }}1:</div>
						<div class="f-right">{{ ropeText1 }}</div>
					</div>
					<span class="editIcon">{{ lang.Edit }}<b class="icon-edit--fill"></b></span>
				</div>
				<div class="detail-item" v-if="!noText" @click="jump('Design Your Printing')">
					<div class="left">
						<div class="f-left">{{ lang.withText }}2:</div>
						<div class="f-right">{{ ropeText2 }}</div>
					</div>
					<span class="editIcon">{{ lang.Edit }}<b class="icon-edit--fill"></b></span>
				</div>
				<div class="detail-item" v-if="!noText" @click="jump('Design Your Printing')">
					<div class="left">
						<div class="f-left">{{ lang.withClipart }}1:</div>
						<div class="f-right">
							<img :src="fontBeforeImg" style="width: 10% !important" />
						</div>
					</div>
					<span class="editIcon">{{ lang.Edit }}<b class="icon-edit--fill"></b></span>
				</div>
				<div class="detail-item" v-if="!noText" @click="jump('Design Your Printing')">
					<div class="left">
						<div class="f-left">{{ lang.withClipart }}2:</div>
						<div class="f-right">
							<img :src="fontAfterImg" style="width: 10% !important" />
						</div>
					</div>
					<span class="editIcon">{{ lang.Edit }}<b class="icon-edit--fill"></b></span>
				</div>
				<div class="detail-item" v-if="!noText" @click="jump('Design Your Printing')">
					<div class="left">
						<div class="f-left">{{ lang.imprintColors }}:</div>
						<div class="f-right">{{ ropeFontFillStyleName1 }} {{ ropeFontFillStyleName2 }}</div>
					</div>
					<span class="editIcon">{{ lang.Edit }}<b class="icon-edit--fill"></b></span>
				</div>
			</ul>
			<a href="javascript:;" @click="showMoreFun" class="more">
				{{ !showMore ? lang.ViewLess : lang.ViewMore }}<i :class="!showMore ? 'less' : 'more'"
					class="el-icon-d-arrow-right"></i>
			</a>
		</div>
		<div class="hr" v-if="!showColor"></div>
		<div class="priceDetail">
			<ul>
				<!-- 总数 -->
				<li class="detail-item" @click="jump('Lanyard Popular Colors')" v-show="showQty">
					<div class="left">
						<div class="f-left">{{ lang.quantity }}:</div>
						<div class="f-right">
							<template v-if="newCalculateData.totalQuantity">
								<div>
									{{ newCalculateData.totalQuantity }}&nbsp;<span style="color:red">(+{{ getGiftsTotal() }}
										{{ lang.free }})</span>
								</div>
							</template>
						</div>
					</div>
					<span class="editIcon">{{ lang.Edit }}<b class="icon-edit--fill "></b></span>
				</li>
				<li class="detail-item">
					<div class="left">
						<div class="f-left">{{ lang.subtotal }}:</div>
						<div class="f-right">
							<CCYRate
								:price="newCalculateData.foundationUnitPrice * newCalculateData.totalQuantity + newCalculateData.toolingCharge">
							</CCYRate>
						</div>
					</div>
				</li>
				<li class="detail-item">
					<div class="left">
						<div class="f-left">{{ lang.unitPrice }}:</div>
						<div class="f-right">
							<CCYRate :price="newCalculateData.foundationUnitPrice"></CCYRate>
						</div>
					</div>
				</li>
				<li class="detail-item">
					<div class="left">
						<div class="f-left">{{ lang.optionsCharges }}:</div>
						<div class="f-right">
							<CCYRate :price="newCalculateData.toolingCharge"></CCYRate>
						</div>
					</div>
				</li>
				<li class="detail-item">
					<div class="left">
						<div class="f-left">{{ lang.setUpCharge || 'Set Up Charges' }}:</div>
						<div class="f-right">
							<CCYRate :price="newCalculateData.setupCharge"></CCYRate>
						</div>
					</div>
				</li>
				<li class="detail-item" v-show="!newCalculateData.onlyAddInquiry && discountPrice != 0">
					<div class="left">
						<div class="f-left">
							{{ text1 }}
						</div>
						<div class="f-right" v-if="Object.keys(newCalculateData).length > 0">
							{{ text2 }}
							<CCYRate :price="discountPrice"></CCYRate>
						</div>
						<div class="f-right" v-else>
							{{ text2 }}
							<CCYRate price="0"></CCYRate>
						</div>
					</div>
				</li>
			</ul>
		</div>
		<div class="hr"></div>
		<div class="footBtn">
			<!-- 美国和澳大利亚不展示提前算税 -->
			<div :style="continentName == 'Europe' && textPrice > 0  ? 'display: flex;justify-content: space-between;width: 100%;padding: 0 10px;' : 'display: flex;justify-content: center;'"
			>
			<div class="currencyText">
					{{ lang.Currency }}:
					<el-select v-model="currencyId" :placeholder="lang.PleaseSelect" @change="changeCurrency" size="small"
						style="width: 100px">
						<el-option v-for="item in currencyList" :key="item.id" :label="item.code" :value="item.id" />
					</el-select>
				</div>
				<div v-if="continentName == 'Europe' && textPrice > 0"
					:style="continentName == 'Europe' && textPrice > 0 ? 'display: flex;align-items: center;justify-content: center;' :'display:none'">
					<strong class="subTotalText">{{getTax(1)}}:&nbsp;</strong>
					<el-switch  v-model="IncludingVAT" :active-value="1" :inactive-value="0" active-text="Yes" inactive-text="No" class="operation"></el-switch>
				</div>
			</div>
			<div class="sub">
				<div class="total-price">
					<span v-if="continentName != 'Europe'">{{ lang.total }}:</span>
					<span v-else>{{ IncludingVAT == 1 ? getTax(2) : getTax(3) }}:</span>
					<CCYRate :price="getTotalPrice(newCalculateData.totalPrice)" style="color: #de3500"></CCYRate>
				</div>
			</div>
			<freeTip></freeTip>
			<div class="btnGroup">
				<el-button v-if="newCalculateData.onlyAddInquiry === 0 || !newCalculateData.onlyAddInquiry" class="toCart btn"
					@click="presentedQuantity > giftQuantity1 ? jump('Lanyard Popular Colors') : submitFun('cart', $event)">{{
						lang.addToCart }}
					<el-tooltip popper-class="cusToolTip" effect="light" :content="textInfo.addCartTip"
						placement="top-start">
						<b class="icon-wenhao3 tip-icon"></b>
					</el-tooltip>
				</el-button>
				<el-button class="submit btn"
					@click="presentedQuantity > giftQuantity1 ? jump('Lanyard Popular Colors') : submitFun('inquiry', $event)">{{
						lang.submitInquiry }}
					<el-tooltip popper-class="cusToolTip" effect="light" :content="textInfo.inquiryTip"
						placement="top-start">
						<b class="icon-wenhao3 tip-icon"></b>
					</el-tooltip>
				</el-button>
			</div>
			<div class="btnGroup-text">
				<b class="icon-jingshi"></b>
				<div>{{ lang.prompter }} {{ this.$store.state.proSystem.email }}</div>
			</div>
		</div>
	</div>
</template>

<script>
import {
	getTaxByPrice
} from "@/api/web";
import freeTip from "~/components/Quote/freeTip"
import {round2} from "@/utils/utils";
export default {
	props: {
		giftQuantity1: {
			type: Number
		},
		presentedQuantity: {
			type: Number
		},
		ropFontFamily1: {
			type: String,
		},
		ropFontFamily2: {
			type: String,
		},
		ropeText1: {
			type: String,
		},
		ropeText2: {
			type: String,
		},
		fontAfterImg: {
			type: String,
		},
		fontBeforeImg: {
			type: String,
		},
		ropeFontFillStyleName1: {
			type: String,
		},
		ropeFontFillStyleName2: {
			type: String,
		},
		showQty: {
			type: Boolean,
			default: false,
		},
		noText: {
			type: Boolean,
			default: false
		},
		showPrice: {
			type: Boolean,
			default: false,
		},
		showMore: {
			type: Boolean,
			default: false,
		},
		selectedData: {
			type: Object,
		},
		calculateData: {
			type: Object,
		},
		defaultData: {
			type: Object,
		},
		textInfo: {
			type: Object,
			default: function () {
				return {};
			},
		},
		totalQuantity: {
			type: Number,
		},
		uploadList: {
			type: Array,
		},
		hasComment: {
			type: Boolean,
		},
		showColor: {
			type: Boolean,
			default: false,
		},
		footer: {
			type: Boolean,
			default: false,
		},
		noDetails: {
			type: Boolean,
			default: false,
		},
		generalData: {
			type: Array,
            default:()=>[]
		},
		filter: {
			type: Boolean,
		},
	},
	data() {
		return {
			continentName:'',
			countryName:'',
			IncludingVAT:1,
			textPrice:null,
			imageLoading: false,
			cusTop: 0,
			currencyId: "",
		};
	},
	components: {
		freeTip
	},
	methods: {
        getAlias(item){
            if(item.alias2){
				if(item.paramType == 'PARTS') {
					return `${item.alias} + ${item.childList[0].alias2}`
				}else{
	                return `${item.alias} + ${item.alias2}`
				}
            }else{
                return `${item.alias}`
            }
        },
        getPantone(item){
            if(item.isTwoToneCustom){
                return `${item.mainColor.pantone} + ${item.accentColor.pantone}`
            }else{
                return item.pantone
            }
        },
        //欧洲国家文案
        getTax(type){
            if(type == 1){
                return 'Including VAT'
            }else if(type == 2){
                return 'Subtotal incl. VAT'
            }else if(type == 3){
                return 'Subtotal excl. VAT'
            }
		},

		//税费开关开启，小计加上税费
		getTotalPrice(totalPrice){
			if(this.IncludingVAT == 0 || this.continentName != 'Europe'){
				return totalPrice
			}else{
				let t = (Math.round((this.textPrice*totalPrice) * 100) / 100).toFixed(2);
				return totalPrice+Number(t)
			}
		},

		getGiftsTotal() {
			let total = 0, popTotal = 0, designTotal = 0, colorTotle = 0;
			this.selectedData['Lanyard Popular Colors'] && this.selectedData['Lanyard Popular Colors'].forEach((item) => {
				popTotal += parseInt(item.giftQuantity) || 0;
			});
			this.selectedData['Lanyard Design Colors'] && this.selectedData['Lanyard Design Colors'].forEach((item) => {
				designTotal += parseInt(item.giftQuantity) || 0;
			});
			this.selectedData['color card'] && this.selectedData['color card'].forEach((item) => {
				colorTotle += parseInt(item.giftQuantity) || 0;
			});
			total = popTotal + designTotal + colorTotle
			return total;
		},
		getTop() {
			let el = document.getElementById("detailInfo");
			if (!el) {
				return false;
			}
			const { top, height } = el.getBoundingClientRect();
			// this.cusTop = (window.innerHeight - height) / 2;
			this.cusTop = (window.innerHeight - height) / 2 + 113 / 2 - 20;
		},

		showList(key) {
			let target = this.selectedData["Products Categories"] ? this.selectedData["Products Categories"][0]?.paramName : 'Lanyards Only',
				boo = true;
			if (target) {
				switch (target) {
					case "Lanyards Only":
						if (key == "Upload Artwork & Comments" || key == "Select Your Card" || key == "Lanyard Badge Holder Options") {
							boo = false;
						}
						break;
					case "Lanyards & Badge Holder":
						break;
					case "Lanyards & PVC Card":
						break;
				}
			} else {
				boo = true;
			}
			return boo;
		},
		getName(key) {
			let temp = this.generalData.find((x) => {
				return x.paramName == key;
			});
			if (key == "Lanyard Style") {
				return this.lang.lanyard.LanyardStyle;
			} else {
				return temp && temp.alias;
			}
		},
		showPopular(val) {
			this.$emit("currentPopularCardItem", val);
			this.$emit("tabsName", "POPULAR");
		},
		showCustom(val) {
			this.$emit("currentCustomCardItem", val);
			this.$emit("tabsName", "CUSTOM");
		},
		showInput(val) {
			val.show = true;
			this.$forceUpdate();
		},
		deleteInput(val, index, arr) {
			arr[index].quantity = undefined;
			arr.splice(index, 1);
		},
		inputChangeFun(val, index, arr) {
			if (!val || val == 0) {
				arr[index].quantity = undefined;
				arr.splice(index, 1);
			}
		},
		showMoreFun() {
			if (!this.showMore) {
				this.$refs.detailList.scrollTo(0, 0)
			}
			this.$emit("update:showMore", !this.showMore);
		},
		changeCurrency(val) {
			let findC = this.currencyList.find((item) => {
				return item.id === val;
			});
			if (findC) {
				this.$store.commit("setCurrency", findC);
			}
		},
		jump(val) {
			if (val == "Lanyard Style" && this.lanyardStyle.length == 1) return;

			let target;
			if (val == "Lanyard Length") {
				target = "Lanyard Width";
			} else if (val == "Print Position Options") {
				target = "Design Your Printing";
			} else {
				target = val;
			}
			this.$emit("jump", target);
		},
		submitFun(type, e) {
			if (e.target.nodeName == "b") {
				return;
			}
			if (type == "inquiry") {
				this.$emit("submitInquiry");
			} else {
				this.$emit("addToCart");
			}
		},
		filterImage(val) {
			return JSON.parse(val.imageJson)[0].url;
		},
	},
	computed: {
        hasDiscount(){
            let selectData = this.selectedData,generateData = this.generalData;
            let findDiscountKey = (generateData.find(item=>item.paramType === "DISCOUNT"))?.paramName;
            return !!selectData[findDiscountKey]?.length;
        },
        newCalculateData(){
            //如果没选折扣，不显示价格
            return this.hasDiscount?this.calculateData:{}
        },
		lang() {
			return this.$store.getters.lang.quote || {};
		},
		proType() {
			return this.$store.state.proType;
		},
        subtotal() {
            return round2(round2(this.newCalculateData.foundationUnitPrice * this.newCalculateData.totalQuantity) + this.newCalculateData.toolingCharge + (this.newCalculateData.setupCharge || 0));
        },
        discountPrice() {
            if (this.newCalculateData) {
                return `${Math.abs(this.newCalculateData.totalPrice - this.subtotal)}`;
            } else {
                return 0;
            }
        },
        text1() {
            let discountName = "";
            //加急费，重量加价
            if (this.newCalculateData.discountPrice) {
                return this.lang.rushDelivery;
            }
            if (this.newCalculateData.discount > 1) {
                return this.lang.rushDelivery;
            } else if (this.newCalculateData.discount < 1) {
                discountName = this.lang.discount;
                return `${discountName} (${this.lang.Turnaround}: ${(Math.abs(1 - this.newCalculateData.discount) * 100).toFixed(0)}% ${this.lang.p8}):`;
            }
        },
        text2() {
            let ac;
            if (this.newCalculateData.totalPrice > this.subtotal) {
                ac = "+";
            } else {
                ac = "-";
            }
            return ac;
        },

		currencyList() {
			return this.$store.state.currencyList;
		},
		canvasLoading() {
			return this.$store.state.canvasLoading;
		},
		device() {
			return this.$store.state.device;
		},
		lanyardStyle(){
			return this.generalData.find(x => x.paramName === 'Lanyard Style').childList
		}
	},
	watch: {
		"$store.state.currency": {
			handler(newValue) {
				this.currencyId = newValue.id;
			},
			immediate: true,
		}
	},
	mounted() {
		window.addEventListener("scroll", () => {
			this.getTop();
		});
		setTimeout(() => {
			this.getTop();
		}, 1000);
	},
	created(){
		//提前算税费
		getTaxByPrice().then((res)=>{
			this.textPrice = res.data.rate;
			this.countryName = res.data?.res?.country?.names.en;
			this.continentName = res.data?.res?.continent?.names.en;//洲
		})
	},
};
</script>

<style scoped lang="scss">
.el-icon-d-arrow-right.more {
	transform: rotate(90deg);
}

.el-icon-d-arrow-right.less {
	transform: rotate(270deg);
}

.LanyardDetails {
	position: relative;
	// background-color: transparent;
	background-color: #f3f4f5;
	border-radius: 10px 0 0 0;
	top: auto !important;
	margin: 0 auto;
	font-size: 14px;
	display: grid;
	align-items: flex-start;
	grid-template-columns: 1fr 1fr;
	padding: 0 30px 20px 0;

	@media screen and (max-width: 767px) {
		grid-template-columns: 1fr;
		padding: 0;
	}

	&.footer {
		@media screen and (max-width: 767px) {
			.topImage {
				display: none;
			}
		}
	}

	&.noDetails {
		@media screen and (max-width: 767px) {
			.con {
				display: none;
			}
		}
	}

	a.more {
		display: block;
		margin: 8px 0 0;
		text-decoration: none;
		font-size: 16px;
		font-weight: 400;
		color: #0066cc;
		text-align: center;

		@media screen and (max-width: 767px) {
			font-size: 12px;
		}
	}

	::v-deep .el-input--small .el-input__inner {
		font-size: 15px;
	}

	::v-deep .el-input-number {
		width: 100%;
	}

	.colorB3 {
		color: #b3b3b3;
	}

	.showColor {
		margin-top: 20px;

		.title {
			font-size: 18px;
			font-weight: 700;
			color: #333;
			text-align: center;
			margin-bottom: 7px;
		}

		.box {
			display: grid;
			grid-template-columns: 1fr;
			// grid-auto-rows: 80px;
			border: 1px solid #d9dbdd;
			border-radius: 4px;
			box-sizing: border-box;
			background-color: #fff;

			overflow-y: auto;
			max-height: 170px;
			margin-bottom: 20px;

			.item {
				display: grid;
				grid-template-columns: 39px 1fr 1fr 1fr;
				padding: 0 10px;
				grid-auto-rows: auto;
				box-sizing: border-box;
				min-height: 95px;
				column-gap: 10px;
				transition: all 0.3s;

				>div:nth-child(1) {
					grid-column: 1/2;
					grid-row: 1/3;

					>div {
						border: 1px solid #d9dbdd;
                        background-size: cover;
					}
				}

				>div:nth-child(2) {
					grid-column: 2/5;
					grid-row: 1/2;
					display: flex;
                    flex-direction: column;
					font-size: 14px;
					font-weight: 400;
					padding-top: 5px;
					color: #666666;
				}

				>div:nth-child(3) {
					grid-column: 2/5;
					grid-row: 2/3;
					display: flex;
					// align-items: center;
					justify-content: space-between;
					padding-bottom: 10px;

					>div:nth-child(1) {
						>div:nth-child(1) {
							font-size: 14px;
							white-space: nowrap;
							margin-right: 5px;
						}
					}

					::v-deep .el-input {
						height: 100%;
						max-width: 96px;
						width: 100%;
						height: 26px;

						@media screen and (max-width: 768px) {
							font-size: 12px;
						}

						.el-input__inner {
							text-align: left;
							height: 100%;
							display: block;
						}
					}
				}
			}

			.item:hover {
				background-color: #f3f4f5;
			}

			.item:not(:last-child) {
				border-bottom: 1px dashed #e6e6e6;
			}

			.item.sp {
				>div:nth-child(2) {
					grid-row: 1/3;
					padding-top: 0;
				}

				>div:nth-child(3) {
					grid-row: 1/3;
					grid-column: 4/5;
					padding-bottom: 0;

					@media screen and (max-width: 768px) {
						justify-content: flex-end;
					}
				}
			}
		}
	}

	.hr {
		background-color: #dcdfe6;
		position: relative;
		display: block;
		height: 1px;
		width: 100%;
		margin: 10px 0;
	}

	.topImage {
		grid-row: 1/6;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		margin-bottom: 10px;
		padding: 10px;

		.imgWrap {
			display: flex;
			justify-content: center;
			// align-items: center;
			height: 100%;
			width: 100%;
			border-radius: 10px;
			// border: 1px dashed #d9dbdd;

			// img {
			// 	border-radius: 10px;
			// }
		}
	}

	.con {
		height: auto;

		.title {
			padding: 0 8px;
			font-size: 27px;
			font-weight: 700;
			color: #202428;
			line-height: 36px;
			margin-top: 10px;
		}

		ul {
			min-height: 380px;
			height: 380px;
			overflow-y: auto;

			&.showMore {
				max-height: calc(100vh - 1000px);
			}
		}
	}

	ul {
		.detail-item {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 8px 10px;
			cursor: pointer;
			transition: all 0.3s;

			&:hover {
				background-color: #dddddd;
			}

			.l {
				font-size: 16px;
				color: rgb(85, 85, 85);

				@media screen and (max-width: 767px) {
					font-size: 12px;
				}
			}

			.left {
				flex: 1;
				display: flex;
				align-items: center;
				justify-content: space-between;

				.f-left {
					// color: #222222;
					color: rgb(85, 85, 85);
					flex-grow: 1;
					flex-basis: 175px;
					flex-shrink: 0;
					font-size: 16px;

					span {
						display: none;
					}

					@media screen and (max-width: 767px) {
						font-size: 12px;
						color: #555;
					}
				}

				.f-right {
					margin-left: 10px;
					color: #222;
					word-break: break-word;
					text-align: right;
					font-size: 16px;
					line-height: normal;

					@media screen and (max-width: 768px) {
						font-size: 12px;
						color: #222;
					}
				}
			}

			.editIcon {
				flex-shrink: 0;
				margin-left: 10px;
				color: #b3b3b3;
				font-size: 12px;
			}

			.m {
				// width: 273px;
				text-align: right;
				color: #222;

				@media screen and (max-width: 767px) {
					width: 256px;
					color: #222;
				}
			}
		}

		.detail-item:nth-child(-n + 8) {
			.left {
				.f-left {
					display: flex;

					span {
						display: block;
					}
				}
			}
		}
	}

	.priceDetail {
		@media screen and (max-width: 767px) {
			margin-top: 0;
			margin-right: 0;
			line-height: 25px;
		}
	}

	.footBtn {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;

		.sub {
			display: flex;
			align-items: flex-start;
			flex-direction: column;
			margin: 5px 0 12px;
			row-gap: 5px;

			.currencyText {
				font-size: 16px;
				color: #222222;
				font-weight: 400;
			}

			.total-price {
				// margin: 0 25px;
				font-size: 30px;
				color: #de3500;
				font-weight: 700;

				span {
					padding-top: 10px;
					font-size: 16px;
					color: #222222;
					font-weight: 400;

					@media screen and (max-width: 767px) {
						padding-top: 0;
					}
				}
			}
		}

		.btnGroup-text {
			background-color: #fff;
			margin: 20px 5px 0;
			border-radius: 4px;
			padding: 14px 15px 21px 22px;
			display: flex;
			align-items: center;

			b {
				font-size: 48px;
				color: #41a25e;
			}

			div {
				font-size: 16px;
				font-weight: 400;
				color: #333333;
				padding-left: 15px;
			}

			@media screen and (max-width: 767px) {
				padding: 10px 20px;

				div {
					font-size: 12px;
				}

				b {
					font-size: 24px;
				}
			}
		}

		.btnGroup {
			width: 100%;
			display: flex;
			flex-wrap: wrap;
			flex-direction: column;

			@media screen and (max-width: 767px) {
				flex-direction: initial;

				.btn:first-child {
					margin-bottom: 0 !important;
				}

				.btn {
					font-weight: bold;
				}
			}

			.btn:first-child {
				margin-bottom: 10px;
			}

			.btn {
				flex: 1;
				margin: 0 5px;
				border-radius: 4px;
				background: transparent;
				border: none;
				font-size: 18px;
				font-weight: 400;
				color: #ffffff;

				.tip-icon {
					width: 18px;
					height: 18px;
					margin-left: 10px;
					color: #fff;
				}
			}

			.submit {
				background-image: linear-gradient(to right, rgb(255, 65, 43) 0%, rgb(255, 119, 67) 100%);
			}

			.toCart {
				background: linear-gradient(to right, #0066cc 0%, #2fb6f5 100%);
			}
		}

		.tip {
			width: 100%;
			text-align: center;
			padding: 10px 0;
			cursor: pointer;
			font-size: 16px;
			color: #666666;
			text-decoration: none;

			.tip-icon {
				width: 22px;
				height: 22px;
				margin-right: 10px;
				vertical-align: middle;
			}

			span {
				max-width: 70%;
			}

			.text-mb {
				display: none;
			}
		}
	}

	@media screen and (max-width: 767px) {
		.topImage {
			.imgWrap {}
		}

		.con {
			.title {
				font-size: 14px;
			}

			.detail-item {
				padding: 5px 10px;
				font-size: 12px;
			}
		}

		.priceDetail {
			.detail-item {
				padding: 5px 10px;
				font-size: 12px;

				.left {
					display: grid;
					grid-template-columns: 1fr 1fr;

					.f-right {
						text-align: right;
					}
				}
			}
		}

		.footBtn {
			.sub {
				.currencyText {
					font-size: 12px;
				}

				.total-price {
					font-size: 21px;

					// margin: 0 15px;
					span {
						font-size: 12px;
					}
				}
			}

			.btnGroup {
				.submit {
					background-image: linear-gradient(to top, rgb(255, 65, 43) 0%, rgb(255, 119, 67) 100%);
					border-radius: 5px;
					font-size: 12px;
					font-weight: 700;
					color: #ffffff;
					padding: 12px;
					line-height: 13px;

					.tip-icon {
						width: 14px;
						height: 14px;
						font-size: 16px;
					}
				}

				.toCart {
					background: linear-gradient(0deg, #0066cc 0%, #2fb6f5 100%);
					border-radius: 5px;
					font-size: 12px;
					font-weight: 700;
					color: #ffffff;
					padding: 12px;
					line-height: 13px;

					.tip-icon {
						width: 14px;
						height: 14px;
						font-size: 16px;
					}
				}
			}

			.tip {
				background: #ebebeb;
				border-radius: 3px;

				.tip-icon {
					width: 14px;
					height: 14px;
				}

				span {
					font-size: 12px;
				}

				.text-pc {
					display: none;
				}

				.text-mb {
					display: inline-block;
				}
			}
		}
	}
}
::v-deep .el-switch__label * {
  line-height: 1;
  font-size: 13px;
  display: inline-block;
}

::v-deep .el-switch__label {
  position: absolute;
  display: none;
  color: #fff !important;
//   font-size: 13px !important;
}

::v-deep .el-switch__label--right {
  z-index: 1;
  right: 20px !important;
  margin-left: 0px;
}

::v-deep .el-switch__label--left {
  z-index: 1;
  left: 20px !important;
  margin-right: 0px;
}

::v-deep .el-switch__label.is-active {
  display: block;
}

::v-deep .el-switch .el-switch__core,
.el-switch .el-switch__label {
  width: 50px !important;
}
</style>