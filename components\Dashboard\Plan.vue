<template>
	<v-dialog
		:value="value"
		@input="$emit('input',false)"
		max-width="523"
	>
		<v-card>
			<v-card-title class="text-h6 justify-center">
				Get more with Premium plan
			</v-card-title>

			<div class="px-6">
				<div class="text-center body-2">
					Historical data is limited<br/>Please switch to the Guru plan to open new<br/>possibilities and
					features. You're going to like it!
				</div>
				<div class="d-flex justify-center align-center text-center my-4">
					<div v-for="item in 3" class="mx-2">
						<v-img width="130" height="120"
							   src="https://images.unsplash.com/photo-1618859867043-79afc5c6c581?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHx0b3BpYy1mZWVkfDF8NnNNVmpUTFNrZVF8fGVufDB8fHx8&auto=format&fit=crop&w=500&q=60"></v-img>
						<span>aaaaaa</span>
					</div>
				</div>
			</div>

			<v-card-actions class="justify-center pb-6">
				<v-btn depressed color="success" width="183">
					See plans and pricing
				</v-btn>

				<v-btn color="#E6E6E6" depressed class="ml-3" width="183">
					Thank you not now
				</v-btn>
			</v-card-actions>
		</v-card>
	</v-dialog>
</template>

<script>
export default {
	name: "Plan",
	props: ['value'],
	model: {
		prop: "value",
		event: "input"
	},
}
</script>

<style scoped>

</style>
