/**
 * @api:     阿里云OSS服务API
 * @author:  xec
 */
import axios from 'axios';
// 创建axios实例
const request = axios.create({
	baseURL: process.env.NODE_ENV === 'development' ? '' : '',
	// timeout: 10000
})

//oss获取签名
export function getSignature(url,data) {
	return request({
		url: url,
		method: 'post',
		data: data,
		headers: {
			'Content-Type': 'application/x-www-form-urlencoded'
		}
	})
}


//上传至oss
export function uploadToOss(url,data,cb) {
	return request({
		url: url,
		method: 'post',
		data: data,
		headers: {
			'Content-Disposition':'inline'
		},
		onUploadProgress: progressEvent => {
			if(typeof cb ==='function'){
				cb(progressEvent)
			}
		}
	})
}



