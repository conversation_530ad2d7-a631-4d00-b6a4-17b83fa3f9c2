import Vue from "vue";

const formatWithRegex = (number) => {
	return !(number + "").includes(".")
		? // 就是说1-3位后面一定要匹配3位
		(number + "").replace(/\d{1,3}(?=(\d{3})+$)/g, (match) => {
			return match + ",";
		})
		: (number + "").replace(/\d{1,3}(?=(\d{3})+(\.))/g, (match) => {
			return match + ",";
		});
};

// 全局注册src指令
const imageIsExist = (url) => {
	return new Promise((resolve) => {
		if (!url) resolve(false);
		// 检测图片是否存在
		let img = new Image();
		img.onload = function () {
			if (this.complete == true) {
				resolve(true);
				img = null;
			}
		};
		img.onerror = function () {
			resolve(false);
			img = null;
		};
		img.src = url;
	});
};


export default ({ store, route }) => {
	//全局千分位数字过滤器
	Vue.filter("formatText", function (value) {
		if (!value) {
			return value;
		}
		return formatWithRegex(value);
	});

	Vue.directive("src", async (el) => {
		// let value = addQueryToUrl({ 'x-oss-process': 'style/waterImage' }, value);

		if (el.src == '/img/default.png') return;
		// 若图片存在http开头的、无法正确加载的src属性，则替换成默认图片
		if (!(await imageIsExist(el.src))) return el.setAttribute('src', '/img/default.png');
		// if (await imageIsExist(value)) el.setAttribute('src', value);
		// else el.setAttribute('src', '/img/default.png');

		// 图片alt、title
		if (!el.alt || !el.title) {
			let imgName = el.src && el.src.split('?')[0];
			imgName = imgName.substring(imgName.lastIndexOf('/') + 1, imgName.lastIndexOf('.')).replace(/^[0-9]+/, '').replace(/-|<br \/>/g, ' ');
			if (!el.alt) el.setAttribute('alt', el.title || imgName);
			if (!el.title) el.setAttribute('title', el.alt || imgName);
		}
	});

	//节流指令
	Vue.directive("throttle", {
		bind: (el, binding) => {
			let { emit = 'click', delay = 1000 } = binding.value || {};
			let cbFun;
			el.addEventListener(
				emit,
				(event) => {
					if (!cbFun) {
						// 第一次执行
						cbFun = setTimeout(() => {
							cbFun = null;
						}, delay);
					} else {
						event && event.stopImmediatePropagation();
					}
				},
				true
			);
		},
	});

	Vue.directive("permission", {
		inserted(el, binding) {
			let permission = {
				name: binding.value,
				list: store.getters["manage/getBtnPermission"],
			};
			if (store.state.manage.currentSite.retailerId === store.state.manage.userInfo.id) {
				return;
			}
			if (!permission.list.includes(permission.name)) {
				el.parentNode && el.parentNode.removeChild(el);
			}
		},
	});


	Vue.directive("disabled", {
		inserted(el, binding) {
			let permission = {
				name: binding.value,
				list: store.getters["manage/getBtnPermission"],
			};
			console.log(permission);
			if (store.state.manage.currentSite.retailerId === store.state.manage.userInfo.id) {
				return;
			}
			if (!permission.list.includes(permission.name)) {
				el.setAttribute("disabled", "disabled");
			}
		},
	});

	Vue.directive("loadmore", {
		inserted: (el, bind, vnode) => {
			let distance = el.getAttribute("infinite-scroll-distance") || 100;
			el.addEventListener("scroll", function () {
				let disabled = el.getAttribute("infinite-scroll-disabled");
				if (disabled) {
					return;
				}
				if (el.scrollTop + el.clientHeight > el.scrollHeight - distance) {
					bind.value();
				}
			});
		}
	});
	//设置输入框过滤非数字
	Vue.directive('filter-number', {
		inserted(el, binding) {
			console.log('我生效了');

			const filterHandler = function (event) {
				el.value = (el.value + '').replace(/[^\d]/g, '');
			};

			const inputHandler = binding.value;

			const handler = function (event) {
				filterHandler(event);
				const val = event.target.value;
				if (typeof inputHandler === 'function') {
					inputHandler(val, event);
				}
			};

			el.handler = handler; // 将 handler 保存到 el 对象上
			el.addEventListener('input', el.handler);
		},

		unbind(el) {
			el.removeEventListener('input', el.handler); // 使用 el.handler 引用已保存的 handler
			delete el.handler; // 清除保存在 el 对象上的 handler
		}
	});
	//首字母大写 过滤器
	Vue.filter('capitalize', function (value) {
		if (!value) return '';
		value = value.toLowerCase();
		return value.charAt(0).toUpperCase() + value.slice(1);
	});
};
