//数量类
export class quantityModel {
	/* 	field = [
		{ key: "quantity", value: "Quantity (PCS)" },
		{ key: "price", value: "Unit Price" },
		{ key: "total", value: "Total Price" },
	]; */
	list = [
		{
			checked: false,
			quantity: 100,
			//disabled:true,
		},
		{
			checked: false,
			quantity: 250,
		},
		{
			checked: false,
			quantity: 500,
		},
		{
			checked: false,
			quantity: 1000,
		},
		{
			checked: false,
			quantity: 3000,
		},
		{
			checked: false,
			quantity: 5000,
		},
	];
	custQuantity = undefined; //自定义价格  左上角输入框
	custQuantityMsg = 1; //自定义价格 左上角推荐数量
}

//tableList表格
export class tableListModel {
	list = [
		/* 	{
			img: process.env.imgServer + "quoteManage/20240924/Soft_enamel_pins_20240924e2W5pM.png",
		},
		{
			img: process.env.imgServer + "quoteManage/20240924/Soft_enamel_pins_20240924e2W5pM.png",
		}, */
	];
	listCopy = [];
}

//基础类
export class baseModel {
	img = process.env.imgServer + "quoteManage/20240912/sales_sheet_bar_20240912dGdHzi.png";
	loading = false;
	refs = "html2canvas";
	constantQuery = "fd-table-detail"; //页面数据传递标识
	rate = 100; //汇率默认是100%
	quantity = undefined; //输入框数量  上一个页面的自定义数量
	quantityChecked = true; //输入框数量  选中
	initQuantity = undefined; //初始金额
	sellQuantity = 168; //总价
	copyList = []; //自定义数量list

	data = {
		//enableProdTime: "1",   //1||显示时间字段  0||隐藏
		//enableRushService: "1",
		//prodTime:undefined,
		//rushTime:undefined,
	};
	//	isEdit = true; //是否启动编辑模式
}
//总价类
export class totalModel {
	sellQuantity = 168; //总价
	op = true;
}
