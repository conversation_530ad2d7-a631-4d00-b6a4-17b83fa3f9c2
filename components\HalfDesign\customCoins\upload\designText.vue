<template>
  <div class="designText">
    <half-design-litter-title :index="stepData.id" :data-name="`${stepData.styleClass + '_' + stepData.id}`"
      style="margin: 4px 0" :stepTitle="stepData.minStepTitle" v-show="stepData.attributeTitle" :showTitle="false">{{
        stepData.attributeTitle }} </half-design-litter-title>
	<slot name="stepText"></slot>
    <div class="textArea" v-for="(item, index) in coinsTextArr" :key="index">
      <v-text-field class="inputText" :value="item.text" solo flat outlined dense hide-details
        :label="langSemiCustom.enterHere" @input="addTextFn($event, item)" />
      <!-- <v-select class="inputSelect" :value="item.fontFamily" :menu-props="{ bottom: true, offsetY: true }" solo flat
        outlined dense hide-details :items="fontsData" item-text="name" item-value="name" :label="langSemiCustom.ff"
        @change="changeTextProperty($event, 'fontFamily', item)">
        <template #item="{ item }">
          <span :style="{ fontFamily: item.name }">{{ item.name }}</span>
        </template>
</v-select> -->
      <!-- <div class="font-bold" :class="{ active: item.fontWeight === 'bold' }"
        @click="changeTextProperty(item.fontWeight === 'normal' || item.fontWeight === 400 ? 'bold' : 'normal', 'fontWeight', item)">
        B</div>
      <div class="font-style" :class="{ active: item.fontStyle === 'italic' }"
        @click="changeTextProperty(item.fontStyle === 'normal' || !item.fontStyle ? 'italic' : 'normal', 'fontStyle', item)">
        I</div>
      <v-menu :close-on-content-click="false" offset-y min-width="300">
        <template v-slot:activator="{ on, attrs }">
          <div class="font-color" :style="{ '--bg-color': item.fill }" tabindex="0" v-bind="attrs" v-on="on">
            <div>A</div>
          </div>
        </template>
        <v-card class="color-picker-wrap" color="#ffffff">
          <div class="color-picker-title">{{ langSemiCustom.ec }}</div>
          <div class="color-picker-list">
            <div class="color-item" :class="{ active: colorItem.code === item.fill }" v-for="colorItem in colorList"
              :key="colorItem.id" :style="{ backgroundColor: colorItem.code }" :title="colorItem.pantone"
              @click="changeTextProperty(colorItem, 'fill', item)">
              <v-icon color="#ffffff" small>mdi-check</v-icon>
            </div>
          </div>
        </v-card>
      </v-menu> -->
    </div>
  </div>
</template>

<script>
import { debounce } from "@/utils/utils";
export default {
  name: 'designText',
  inject: ["getAreaUploadList"],
  props: {
    stepData: {
      type: Object,
      default: () => ({}),
    },
  },
  components: {},
  data() {
    return {
      textIcon: [
        {
          icon: "icon-a-lrgl-text-leftzhuanhuan",
          val: "left",
        },
        {
          icon: "icon-a-lrgl-text-centerzhuanhuan",
          val: "center",
        },
        {
          icon: "icon-a-lrgl-text-rightzhuanhuan",
          val: "right",
        },
      ],
      imgIcon: [
        {
          icon: "icon-D-center",
          val: "center",
        },
        {
          icon: "icon-D-left",
          val: "left",
        },
        {
          icon: "icon-D-right",
          val: "right",
        },
      ],
      allIcon: [
        {
          icon: "icon-T-right",
          val: "right",
        },
        {
          icon: "icon-T-left",
          val: "left",
        },
        {
          icon: "icon-T-down",
          val: "down",
        },
        {
          icon: "icon-T-up",
          val: "up",
        },
      ],
      //颜色选项卡
      selectColorModel: {
        uploadModel: [],
        inputModel: [],
      },
      addTextFn: null,
      showAreaText: ""
    }
  },
  watch: {
    isUpload(val) {
      if (val) {
        this.stepData.noShow = true
      } else {
        this.stepData.noShow = false
      }
    }
  },
  computed: {
    langSemiCustom() {
      return this.$store.getters.lang?.semiCustom;
    },
    colorList() {
      return this.$store.state.colorList;
    },
    fontsData() {
      return require("@/assets/json/fontList.json");
    },
    coinsTextArr() {
      let data = this.getAreaUploadList();
      return data[this.showAreaText]?.textArr
    },
    isUpload() {
      return this.$store.state.halfDesign.medalsLaterUpload;
    },
  },
  methods: {
    addText(val, item) {
      this.$emit("newAddTextBefore2", {
        text: val.trim(),
        textItem: item,
      });
    },
    changeTextProperty(val, property, item) {
      this.$emit("changeTextPropertyBefore2", {
        val,
        property,
        textItem: item,
      });
    },
    addTextBox(data,frist=false,colorIndex=0) {
      this.showAreaText = data.valueName
      this.stepData.noShow = false;
      if (data.curves && data.curves.length == 0) this.stepData.noShow = true;
      //判断是否有默认值
      if (this.coinsTextArr && this.coinsTextArr.length > 0) {
        this.coinsTextArr.forEach(text => {
          if (text.defaultText && text.defaultText.length > 0) {
            if (text.text.length == 0) {
              text.text = text.defaultText
            }
			setTimeout(() => {
              this.addText(text.text, text)
            }, 1200)
          }
        });
      }
    }
  },
  created() { },
  mounted() {
    this.addTextFn = debounce(this.addText, 1000);
    this.$Bus.$on("currentArea", this.addTextBox);
  },
  beforeDestroy() {
    this.$Bus.$off("currentArea", this.addTextBox);
  }
}
</script>

<style scoped lang="scss">
@import "~assets/css/half.scss";

.designText {
  .textArea {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 4px;
    white-space: nowrap;
    margin-bottom: 10px;

    .v-input {
      overflow: hidden;
      flex: 1 0 0;
    }

    .addNewLine {
      flex: 0 0 60px;
      cursor: pointer;

      b {
        display: inline-block;
        margin-right: 8px;
        color: $color-primary;
      }

      b.icon-shanchu2 {
        color: red;
      }
    }

    .line {
      width: 1px;
      height: 24px;
      margin: 0 10px;
      background: #d3d5d7;
    }

    .font-style {
      font-style: italic;
    }

    .font-color {
      --bg-color: #cccccc;
      position: relative;
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      border: 1px solid #F0F0F0;

      &::after {
        position: absolute;
        content: "";
        height: 3px;
        background-color: var(--bg-color);
        width: 15px;
        top: calc(50% + 8px);
        left: 50%;
        transform: translate(-50%, -50%);
        box-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
      }
    }

    .font-bold,
    .font-style {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 40px;
      height: 40px;
      cursor: pointer;
      border-radius: 4px;
      transition-duration: 0.15s;
      transition-property: color;
      transition-timing-function: cubic-bezier(0.25, 0.8, 0.25, 1);
      border: 1px solid #F0F0F0;

      @media (any-hover: hover) {
        &:hover {
          color: $color-primary;
          background-color: #efefef;
        }
      }

      &.active {
        color: $color-primary;
        background-color: #efefef;
      }
    }

  }

  @include respond-to(mb) {
    .textArea {
      .inputText {

        ::v-deep .v-input__control {

          .v-input__slot {
            background-color: #F5F5F5 !important;
          }
        }
      }

      .inputSelect {
        ::v-deep .v-input__control {
          .v-input__slot {
            background-color: #F5F5F5 !important;
          }
        }
      }

      .font-bold,
      .font-style {
        margin-right: 5px;
      }
    }
  }
}
</style>
