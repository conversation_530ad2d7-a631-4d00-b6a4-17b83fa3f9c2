<template>
	<client-only>
		<div id="dzApp" noDebounce>
			<div id="dsWrap">
				<dz-head @showMyDesign="showMyDesign" @shareClick="shareClick"></dz-head>
				<div class="ds-content">
					<div class="left-area-wrap">
						<left-area v-if="show && !isMb" :defaultStatus="defaultStatus" @showMyDesign="showMyDesign"></left-area>
					</div>
					<div class="ds-right-area" @click="showDefault">
						<div class="sideWrap" :class="{ isSticker: canvasWorker.isSticker }">
							<side v-if="show && !isMb" :class="{ visibilityHidden: isDefault, isYd: editHelp }"></side>
						</div>
						<div class="ds-right-content">
							<div class="tool-bar-wrap">
								<tool-bar v-if="show && !isMb" :class="{ visibilityHidden: isDefault, isYd: editHelp }"></tool-bar>
							</div>
							<div class="fbWrap" id="designBody" :class="{ isSticker: canvasWorker.isSticker }">
								<div class="des" :class="{ visibilityHidden: isDefault || !canvasWorker.templateName }">{{ canvasWorker.templateName }}</div>
								<div id="workspace" v-loading="loading">
									<canvas id="fabricCanvas"></canvas>
									<mbLeftTool class="mbLeftTool" v-if="show"></mbLeftTool>
									<mbRightTool class="mbRightTool" v-if="show && !canvasWorker.isSticker"></mbRightTool>
								</div>
								<zoom class="zoom"></zoom>
								<mouseMenu v-if="show"></mouseMenu>
								<div class="stickerSize" v-if="isMb && canvasWorker.isSticker">
									<div>Size:</div>
									<div class="size">
										<el-popover placement="top-start" popper-class="stickerSizeToolTip" effect="light" width="117" trigger="manual" content="This field is required" v-model="sizeWError">
											<input type="text" slot="reference" v-model="canvasWorker.stickerSize.w" class="sizeInput" :class="{ isError: sizeWError }" @input="changeSize('w')" />
										</el-popover>
										<span class="c">X</span>
										<el-popover placement="top-start" popper-class="stickerSizeToolTip" effect="light" width="117" trigger="manual" content="This field is required" v-model="sizeHError">
											<input type="text" slot="reference" v-model="canvasWorker.stickerSize.h" class="sizeInput" :class="{ isError: sizeHError }" @input="changeSize('h')" />
										</el-popover>
										<el-dropdown trigger="click" @command="handleStickerSizeCommand">
											<div class="sizeSelect">
												<b class="icon-down"></b>
											</div>
											<el-dropdown-menu slot="dropdown">
												<el-dropdown-item :command="{ w: 1, h: 1 }">1” x 1”</el-dropdown-item>
												<el-dropdown-item :command="{ w: 2, h: 2 }">2” x 2”</el-dropdown-item>
												<el-dropdown-item :command="{ w: 3, h: 3 }">3” x 3”</el-dropdown-item>
												<el-dropdown-item :command="{ w: 4, h: 4 }">4” x 4”</el-dropdown-item>
												<el-dropdown-item :command="{ w: 5, h: 5 }">5” x 5”</el-dropdown-item>
											</el-dropdown-menu>
										</el-dropdown>
									</div>
								</div>
								<div class="space" v-if="isMb"></div>
								<div class="mb-control-area" v-if="isMb">
									<div class="space"></div>
									<dzTips v-if="show"></dzTips>
									<controlBottomArea v-if="show" @next="next" @saveClick="saveClick"></controlBottomArea>
								</div>
							</div>
						</div>
						<div class="right-tool-wrap" v-if="!isMb">
							<right-tool v-if="show" :class="{ visibilityHidden: isDefault, isYd: editHelp }" @saveClick="saveClick" @shareClick="shareClick"></right-tool>
						</div>
						<!-- 模板列表-->
						<template-list v-if="show && !isMb"></template-list>
						<!-- 元素列表-->
						<art-list v-if="show && !isMb"></art-list>
					</div>
					<div class="bottomAreaWrap" v-if="!isMb">
						<bottom-area v-if="show" @next="next"></bottom-area>
					</div>
				</div>
			</div>
			<change-Element v-if="showReplaceDialog" :width="device !== 'mb' ? '710px' : '92%'"> </change-Element>
			<tool-option v-if="show" ref="toolOption"></tool-option>
			<dzYd v-if="editHelp && show && !isMb" @closeEditHelp="closeEditHelp" @start="start"></dzYd>
			<myDesign :myDesignDialog.sync="myDesignDialog" v-if="isLogin && myDesignDialog"></myDesign>
			<saveDialog :saveDialogDialog.sync="saveDialogDialog" :saveShow.sync="saveShow"></saveDialog>
			<preview></preview>
			<shareDialog :shareDialogDialog.sync="shareDialogDialog" :saveShow.sync="shareShow" :shareImg="shareImg" :shareLink="shareLink"></shareDialog>
			<platDialog ref="platDialog" :userUploadList="userUploadList" :platDialog.sync="platDialog" :previewImg.sync="previewImg" :originPreviewImg="originPreviewImg" v-if="show"></platDialog>
		</div>
	</client-only>
</template>
<script>
import { addDesignTemplateShare } from "~/api/newDzxt";
import shareDialog from "@/components/MyDzxt/shareDialog.vue";
import saveDialog from "@/components/MyDzxt/saveDialog.vue";
import myDesign from "@/components/MyDzxt/myDesign.vue";
import "@/plugins/element";
import leftArea from "@/components/MyDzxt/leftArea.vue";
import dzHead from "~/components/MyDzxt/dzHead.vue";
import ColorPickerItem from "@/components/MyDzxt/ColorPickerItem.vue";
import zoom from "@/components/MyDzxt/zoom.vue";
import side from "@/components/MyDzxt/side.vue";
import toolBar from "@/components/MyDzxt/toolBar.vue";
import rightTool from "@/components/MyDzxt/rightTool.vue";
import bottomArea from "@/components/MyDzxt/bottomArea.vue";
import templateList from "@/components/MyDzxt/templateList.vue";
import artList from "@/components/MyDzxt/artList.vue";
import toolOption from "@/components/MyDzxt/toolOption.vue";
import { getAllColorCard, getAllColorType, getByUrlAndProId, geTemplateByRouteUrl, getFirstBlankByCategoryId, getUserTemplateById } from "@/api/newDzxt";
import changeElement from "@/components/MyDzxt/changeElement.vue";
import canvas from "@/assets/js/fabricCore/dzPublic";
import dzYd from "@/components/MyDzxt/dzYd.vue";
import { dataURLtoFile, deepClone } from "@/utils/utils";
import { getDesignTemplateShare, addTemporaryUserTemplates } from "~/api/newDzxt";
import dzTips from "@/components/MyDzxt/dzTips.vue";
import controlBottomArea from "@/components/MyDzxt/control-bottom-area.vue";
import mbLeftTool from "@/components/MyDzxt/mbLeftTool.vue";
import mbRightTool from "@/components/MyDzxt/mbRightTool.vue";
import preview from "@/components/MyDzxt/preview.vue";
// 右键菜单
import mouseMenu from "@/components/MyDzxt/contextMenu/index.vue";
import platDialog from "@/components/MyDzxt/platDialog.vue";
import { uploadFile } from "@/utils/oss";

export default {
	head: {
		link: [
			{
				href: "//at.alicdn.com/t/c/font_3787566_08g4d7jt302e.css", //黑
				rel: "stylesheet",
				body: true,
			},
			{
				href: "//at.alicdn.com/t/c/font_3787572_ovbvnsxlgfe.css", //彩
				rel: "stylesheet",
				body: true,
			},
		],
		script: [
			{
				src: "https://cdn.jsdelivr.net/npm/pdfjs-dist@3.1.81/build/pdf.min.js",
				async: true,
				body: true,
			},
		],
	},
	computed: {
		isLogin() {
			return this.$store.getters.isLogin;
		},
		isDefault() {
			return this.$store.state.design.isDefault;
		},
		loading() {
			return this.$store.state.design.loading;
		},
		device() {
			return this.$store.state.device;
		},
		langDesign() {
			return this.$store.getters.lang?.design;
		},
		isMb() {
			return this.$store.state.device === "mb";
		},
		showReplaceDialog() {
			return this.$store.state.design.showReplaceDialog;
		},
		templateId() {
			return this.$route.query.templateId;
		},
	},

	data() {
		return {
			defaultStatus: 0,
			userUploadList: [],
			sizeWError: false,
			sizeHError: false,
			previewImg: "",
			originPreviewImg: "",
			platDialog: false,
			shareShow: 1,
			shareImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20220821/20220821rB4ZHrbJ.png",
			shareLink: "",
			shareDialogDialog: false,
			canvasWorker: canvas,
			editHelp: false,
			signUpDialog: false,
			canvas: null,
			hideTool: false,
			workspace: null,
			show: false,
			myDesignDialog: false,
			saveDialogDialog: false,
			saveShow: 1,
			selectArr: [],
			creationTemplatesList: [],
		};
	},
	provide() {
		return {
			canvas: this.canvasWorker,
		};
	},
	components: {
		rightTool,
		toolBar,
		zoom,
		ColorPickerItem,
		dzHead,
		leftArea,
		side,
		bottomArea,
		templateList,
		artList,
		toolOption,
		dzYd,
		changeElement,
		myDesign,
		saveDialog,
		dzTips,
		controlBottomArea,
		mbLeftTool,
		mbRightTool,
		preview,
		mouseMenu,
		platDialog,
		shareDialog,
	},
	methods: {
		changeSize(type) {
			canvas.stickerSize[type] = canvas.stickerSize[type].replace(/[^0-9.]/g, "");
			if (!canvas.checkStickerSize()) {
				return false;
			}
			canvas.initStickerShape();
		},
		handleStickerSizeCommand(command) {
			canvas.stickerSize.w = command.w.toString();
			this.changeSize("w");
			canvas.stickerSize.h = command.h.toString();
			this.changeSize("h");
		},
		async next() {
			this.userUploadList = canvas.getUserUploadFile();
			//是否展示电镀弹窗
			if (this.$store.state.design.pageInfo.isMetal == 1) {
				this.originPreviewImg = this.previewImg = await canvas.getTemplatePicPath(1, true);
				this.platDialog = true;
				this.$nextTick(() => {
					this.$refs.platDialog.loadPlating = false;
					this.$refs.platDialog.customStatus = 1;
					this.$refs.platDialog.technology = "";
				});
			} else {
				this.$gl.show();
				try {
					let quoteUrl = this.$store.getters["design/getQuoteUrl"];
					console.log(quoteUrl);
					if (!quoteUrl) {
						this.$gl.hide();
						return false;
					}
					this.previewImg = await canvas.getTemplatePicPath(1, true);
					let file = dataURLtoFile(this.previewImg, 1);
					let picPath = await uploadFile(file);
					let url = new URL(`${window.location.origin}${quoteUrl}?designPic=${picPath}&userUploadList=${JSON.stringify(this.userUploadList)}`);
					if (canvas.isSticker) {
						url = new URL(`${window.location.origin}${quoteUrl}?designPic=${picPath}&size=${JSON.stringify(canvas.stickerSize)}&userUploadList=${JSON.stringify(this.userUploadList)}`);
					}
					let searchParams = url.searchParams;
					searchParams.forEach(function (value, key) {
						if (value === "") {
							searchParams.delete(key); // 删除无效值的参数
						}
					});
					let apiData = {
						categoryId: this.$store.state.design?.pageInfo?.id,
						templateFile: JSON.stringify(canvas.getTemplateData()),
						templateUrl: picPath,
					};
					addTemporaryUserTemplates(apiData);
					window.removeEventListener("beforeunload", canvas.confirmExit);
					window.location.href = url.toString();
					this.$gl.hide();
				} catch (e) {
					this.$gl.hide();
				}
			}
		},
		async shareClick() {
			this.$gl.show();
			try {
				let specification = null;
				if (canvas.isSticker) {
					if (!canvas.checkStickerSize()) {
						this.$gl.hide();
						return false;
					}
					specification = JSON.stringify({
						width: canvas.stickerSize.w,
						height: canvas.stickerSize.h,
					});
				}
				this.shareImg = await canvas.getTemplatePicPath();
				let layersData = canvas.getTemplateData();
				let res = await addDesignTemplateShare({
					productId: this.$store.state.design?.pageInfo?.id,
					imgUrl: this.shareImg,
					layersData: JSON.stringify(layersData),
					specification,
					routingName: this.$route.params.systemName,
				});
				this.shareShow = 1;
				this.shareDialogDialog = true;
				this.shareLink = `${window.location.origin}/us/design/${this.$route.params.designName}?id=${res.data.secret}`;
				this.$gl.hide();
			} catch (e) {
				this.$gl.hide();
			}
		},
		showDefault(e) {
			let target = e.target;
			if (target.tagName !== "CANVAS") {
				let topBar = document.querySelector(".tool-bar"); // 获取目标容器元素
				if (topBar && topBar.contains(target)) {
					return;
				}
				this.$Bus.$emit("showDefault");
				this.$Bus.$emit("hideTools");
				this.$nextTick(() => {
					this.canvas.discardActiveObject().requestRenderAll();
				});
			}
		},
		//保存弹窗
		saveClick() {
			if (canvas.isSticker) {
				if (!canvas.checkStickerSize()) {
					return false;
				}
			}
			this.saveShow = 1;
			this.saveDialogDialog = true;
		},
		//MyDesign弹窗
		showMyDesign() {
			this.myDesignDialog = true;
		},
        start(){
            this.defaultStatus = 0;
        },
		closeEditHelp() {
			this.editHelp = false;
		},
		//初始化画布
		initCanvas() {
			this.canvas = canvas.initCanvas("fabricCanvas");
			this.canvas.on({
				"object:added": (e) => {
					if (e.target.id !== "workspace") {
						this.$store.commit("design/set_isDefault", false);
					}
				},
			});
		},
		handleResize() {
			window.requestAnimationFrame(() => {
				if (this.isMb) {
					let clientWidth = document.documentElement.clientWidth;
					let workspace = document.querySelector("#workspace");
					workspace.style.flex = `0 0 ${clientWidth < 500 ? clientWidth : 500}px`;
					canvas.hideGrid();
				}
				canvas.auto();
			});
		},
		getDesignTemplateShare() {
			return new Promise((resolve, reject) => {
				getDesignTemplateShare({
					secret: this.$route.query.id,
				})
					.then((res) => {
						//判断是否是sticker设计系统
						if (canvas.isSticker) {
							let specification = JSON.parse(res.data.specification);
							canvas.stickerSize = {
								w: specification.width,
								h: specification.height,
							};
						}
						resolve(res.data.layersData);
					})
					.catch((err) => {
						reject(err);
					});
			});
		},
		async loadDefaultTemplate(productType) {
			//判断是半定制还是全定制,1半定制,0全定制
			if (productType === 1) {
				//半定制暂时不做处理
			} else if (productType === 0) {
				//判断路由是否存在模板，如果不存在默认加载第一个光版模板，否则获取路由上的模板数据
				if (this.$route.params.templateName) {
					let data = await geTemplateByRouteUrl({
						routeUrl: this.$route.params.templateName,
					});
					if (data.data) {
						canvas.templateName = data.data.templateName;
						//判断是否是sticker设计系统
						if (canvas.isSticker) {
							let specification = JSON.parse(data.data.specification);
							canvas.stickerSize = {
								w: specification.width,
								h: specification.height,
							};
						}
						await canvas.loadTemplate(data.data.templateFile);
					}
				} else {
					let data = await getFirstBlankByCategoryId({
						categoryId: this.$store.state.design?.pageInfo?.id,
					});
					if (data.data) {
						canvas.templateName = data.data.templateName;
						//判断是否是sticker设计系统
						if (canvas.isSticker) {
							let specification = JSON.parse(data.data.specification);
							canvas.stickerSize = {
								w: specification.width,
								h: specification.height,
							};
						}
						await canvas.loadTemplate(data.data.templateFile);
					}
				}
			}
		},
		//根据id查询模版数据
		getUserTemplateById(templateId) {
			return new Promise((resolve) => {
				getUserTemplateById({
					id: templateId,
				}).then((res) => {
					//判断是否是sticker设计系统
					if (canvas.isSticker) {
						let specification = JSON.parse(res.data.specification);
						canvas.stickerSize = {
							w: specification.width,
							h: specification.height,
						};
					}
					resolve(res.data.templateFile);
				});
			});
		},
	},
	created() {
		getAllColorCard().then((res) => {
			this.$store.commit("design/set_colorList", res.data);
		});
		getAllColorType().then((res) => {
			this.$store.commit("design/set_colorType", res.data);
		});
	},
	async mounted() {
		if (this.$route.query.cid) {
			this.defaultStatus = 1;
		}
		await this.$nextTick();
		// 根据url获取信息
		let urlData = (
			await getByUrlAndProId({
				url: `/design/${this.$route.params.designName}/`,
			})
		).data;
		let positionConfigs = deepClone(urlData.positionConfigs);
		positionConfigs.forEach((item) => {
			item.url = "";
			item.templateData = null;
		});
		canvas.positionConfigs = positionConfigs;
		canvas.hasBg = urlData.hasBg;
		canvas.isSticker = urlData.isSticker;
		canvas.stickerEffectType = urlData.stickerEffectType;
		this.$store.commit("design/set_pageInfo", urlData);
		let templateId = this.$route.query.templateId;
		this.initCanvas();
		//是半定制还是全定制
		let productType = urlData.productType;
		if (productType === 1) {
			//半定制暂时不做处理
			canvas.designDes = "half";
		} else if (productType === 0) {
			canvas.designDes = "quote";
		}
		//判断是否是分享的模板
		if (this.$route.query.id) {
			//如果分享失效加载默认光板模板
			try {
				let layersData = await this.getDesignTemplateShare();
				await canvas.loadTemplate(layersData);
			} catch (e) {
				await this.loadDefaultTemplate(productType);
			}
		} else if (templateId) {
			let layersData = await this.getUserTemplateById(templateId);
			await canvas.loadTemplate(layersData);
		} else {
			await this.loadDefaultTemplate(productType);
		}
		window.addEventListener("beforeunload", canvas.confirmExit);
		window.addEventListener("resize", this.handleResize);
		this.handleResize();
		let val = this.$cookies.get("show_dz_yd");
		if (val === undefined && !this.isMb) {
			this.editHelp = true;
		}
		if (!this.isMb) {
			await canvas.showGrid();
		}
        this.show = true;
	},
	beforeDestroy() {
		this.canvas.off();
		window.removeEventListener("resize", this.handleResize);
		window.removeEventListener("beforeunload", canvas.confirmExit);
	},
};
</script>
<style lang="scss" scoped>
#dzApp {
	position: relative;
	font-family: Calibri, sans-serif;

	::v-deep {
		.linear-gradient-1 {
			background: repeating-linear-gradient(-45deg, #f4f5f6, #f4f5f6 5px, #f8f9fa 0, #f8f9fa 40px) !important;
			background-color: #f4f5f6;
		}

		.visibilityHidden {
			visibility: hidden;
		}

		.isYd.visibilityHidden {
			visibility: visible;
		}
	}
}

#dsWrap {
	overflow: auto;
	display: flex;
	flex-direction: column;
	position: relative;
	height: 100dvh;
	user-select: none;
	font-size: 16px;

	@include respond-to(mb) {
		font-size: 14px;
		min-height: auto;
	}
}

.ds-content {
	overflow: auto;
	position: relative;
	display: grid;

	grid-template-columns: 1fr 2fr;

	&.disabledPage {
		pointer-events: none;
	}

	.left-area-wrap {
		min-width: 500px;
		max-width: 630px;
		height: calc(100dvh - 60px);
	}

	.ds-right-area {
		overflow: hidden auto;
		position: relative;
		display: flex;
		justify-content: left;
		align-items: flex-start;
		background-color: #e6e6e6;
		height: calc(100dvh - 140px);
		//min-width: 950px;
		padding: 0 2vw 20px;

		.sideWrap {
			width: 4.7vw;
			margin-top: 40px;

			&.isSticker {
				visibility: hidden;
			}
		}

		.ds-right-content {
			flex: 1;
			width: 0;
			max-width: 800px;
			margin: 0 10px;
			display: flex;
			flex-direction: column;
			justify-content: flex-end;
			align-items: center;
			position: relative;
			height: 100%;

			.tool-bar-wrap {
				display: flex;
				justify-content: center;
				align-items: center;
				height: 60px;
			}

			& > div {
				width: 100%;
			}

			.fbWrap {
				//width: 700px;
				display: flex;
				flex-direction: column;
				flex: 1;
				height: 0;
				position: relative;
				// background-color: #ffffff;
				padding: 0 10px 65px;
				margin-top: 40px;
				@include respond-to(mb) {
					margin-top: 0;
				}

				&.isSticker {
					background-color: transparent;
				}

				.des {
					margin: 10px 0;
					font-weight: 700;
					position: absolute;
					top: -39px;
					left: -5vw;

					@include respond-to(mb) {
						position: relative;
						left: auto;
						top: auto;
					}
				}

				#workspace {
					flex: 1;
					height: 0;

					.mbLeftTool {
						display: none;
					}

					.mbRightTool {
						display: none;
					}

					::v-deep .el-loading-mask {
						z-index: 1;
					}
				}

				.zoom {
					position: absolute;
					bottom: 10px;
					right: 70px;
				}

				.space {
					display: none;
					flex: 1;
				}

				.mb-control-area {
					display: none;
				}
			}
		}

		.right-tool-wrap {
			width: 4.2vw;
			margin-left: 1vw;
		}

		.bottomAreaWrap {
			height: 80px;
		}
	}

	@include respond-to(mb) {
		display: block;

		.tool-bar-wrap {
			display: none !important;
		}

		.left-area-wrap {
			display: none;
		}

		.sideWrap {
			display: none;
		}

		.right-tool-wrap {
			display: none;
		}

		.bottomAreaWrap {
			display: none;
		}

		.ds-right-area {
			min-width: 100%;
			width: 100%;
			height: calc(100dvh - 44px);
			padding: 0;

			.ds-right-content {
				width: 100%;
				margin: 0;

				.fbWrap {
					width: 100%;
					height: 100%;
					padding: 0;
					background: #ffffff url("https://static-oss.gs-souvenir.com/web/quoteManage/20231023/grid_2056nnW8sc.png") repeat left top;

					&.isSticker {
						background: #f2f2f2;
					}

					.des {
						margin: 5px 0;
						text-align: center;
						font-size: 12px;
					}

					#workspace {
						flex: 1;
						position: relative;

						.mbLeftTool {
							display: block;
							position: absolute;
							top: 1em;
							left: 0.5em;
						}

						.mbRightTool {
							display: block;
							position: absolute;
							top: 1em;
							right: 0.5em;
						}
					}

					.zoom {
						display: none;
					}

					.stickerSize {
						display: flex;
						align-items: center;
						justify-content: center;
						margin: 10px 0;

						.size {
							margin-left: 10px;

							.c {
								margin: 0 10px;
							}

							.sizeSelect {
								display: flex;
								justify-content: center;
								align-items: center;
								width: 24px;
								height: 24px;
								background: #ebebeb;
								border-radius: 50%;
								margin-left: 14px;

								b {
									font-weight: 700;
								}
							}
						}

						.sizeInput {
							width: 70px;
							height: 40px;
							background: #ffffff;
							border: 1px solid #dbdbdb;
							border-radius: 4px;
							padding: 0 10px;

							&.isError {
								border-color: red;
							}
						}
					}

					.space {
						display: block;
						flex: 1;
					}

					.mb-control-area {
						display: flex;
						flex-direction: column;
						justify-content: space-between;
						padding: 10px 0;
						height: 240px;

						.plating-area {
							position: relative;
							height: 80px;
							margin-bottom: 40px;
						}
					}
				}
			}
		}
	}
}
</style>

<style lang="scss">
.el-select-dropdown {
	border-color: #2a96fa;
}

.el-select-dropdown.el-popper .popper__arrow {
	border-bottom-color: #2a96fa;
}

.el-button--primary {
	color: #fff;
	background-color: #2a96fa;
	border-color: #2a96fa;
}

.el-select .el-input.is-focus .el-input__inner {
	border-color: #2a96fa;
}

.el-select-dropdown__item.selected {
	color: #2a96fa;
}

.editColor-colorPicker {
	overflow: hidden;
	display: flex;
	flex-direction: column;
	flex: 1;

	.editColor-colorPicker-title {
	}

	.editColor-colorPicker-selected {
		display: flex;
		align-items: center;
		margin: 12px 0;

		.color-name {
			margin-left: 8px;
		}
	}

	.editColor-colorPicker-colorContainer {
		flex: 1;
		overflow: hidden auto;

		.editColor-colorPicker-colors {
			display: grid;
			grid-template-columns: repeat(auto-fill, 2.875em);
			grid-column-gap: 10px;
			grid-row-gap: 10px;
		}
	}

	@include respond-to(mb) {
		.editColor-colorPicker-colorContainer {
			.editColor-colorPicker-colors {
				display: grid;
				grid-template-columns: repeat(auto-fill, 34px);
				grid-column-gap: 6px;
				grid-row-gap: 10px;
			}
		}
	}
}

.more-popover {
	min-width: auto;
	user-select: none;

	.more-popover-item {
		margin-bottom: 6px;

		a {
			white-space: nowrap;
		}

		&:last-child {
			margin-bottom: 0;
		}

		b {
			margin-right: 8px;
		}

		&:hover {
			color: #2a96fa;
			text-decoration: underline;
		}
	}
}

.layer-popover {
	padding: 12px 0 0;
	user-select: none;

	.el-popover__title {
		padding: 0 12px;
	}

	.list .list-item {
		display: flex;
		align-items: center;
		height: 30px;
		padding: 0 12px;
		cursor: pointer;

		b {
			margin-right: 4px;
		}

		&:hover {
			background: #f4f5f6;
		}
	}
}

.pen-popover {
	.penWrap {
		.top {
			display: flex;
			align-items: center;

			.el-slider {
				flex: 1;
				margin: 0 20px;
			}

			.el-slider .el-slider__input {
				width: 70px;
			}

			.colorPicker {
				display: flex;
				justify-content: center;
				align-items: center;
				flex-shrink: 0;
				width: 40px;
				height: 40px;
				background: #ffffff;
				border: 1px solid #e6e6e6;
				cursor: pointer;
				border-radius: 4px;

				.color-inner {
					display: flex;
					justify-content: center;
					align-items: center;
					width: 34px;
					height: 34px;
					background: #da9f19;
					border-radius: 4px;

					b {
						font-size: 14px;
						color: #ffffff;
					}
				}
			}
		}

		.btnWrap {
			display: flex;
			justify-content: center;
			margin-top: 10px;

			.startBtn {
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				margin-right: 20px;
				cursor: pointer;

				&:hover {
					color: #2a96fa;
				}
			}
		}
	}
}

.color-popover {
	width: 540px;

	.colorWrap {
		display: grid;
		grid-template-columns: repeat(auto-fill, 2.875em);
		grid-column-gap: 5px;
		grid-row-gap: 5px;
		overflow: hidden auto;
		max-height: 302px;
	}

	@include respond-to(mb) {
		width: 304px;
		padding: 10px;

		.colorWrap {
			grid-template-columns: repeat(auto-fill, 34px);
			grid-column-gap: 7px;
			grid-row-gap: 10px;
			max-height: 122px;
		}
	}
}

.img-color-popover {
	width: 480px;

	.color-item {
		width: 30px !important;
		height: 30px !important;
	}

	.editColor-colorPicker-selected {
		display: grid;
		grid-template-columns: repeat(auto-fill, 30px);
		grid-column-gap: 10px;
		grid-row-gap: 10px;
		max-height: 110px;
		overflow: hidden auto;
	}

	.editColor-colorPicker-colorContainer {
		margin-top: 10px;
	}

	.editColor-colorPicker-colors {
		grid-template-columns: repeat(auto-fill, 30px) !important;
		grid-column-gap: 10px;
		grid-row-gap: 10px;
		max-height: 110px;
		overflow: hidden auto;
	}

	.editImgColor-foot {
		padding: 1em;
		display: none;
		justify-content: center;
		align-items: center;
	}
}

.text-color-popover {
	.colorWrap {
		display: grid;
		grid-template-columns: repeat(12, 1fr);
		grid-column-gap: 10px;
		grid-row-gap: 10px;
		max-height: 110px;
		overflow: hidden auto;

		@include respond-to(mb) {
			grid-template-columns: repeat(6, 1fr);
		}
	}

	.color-item {
		width: 30px !important;
		height: 30px !important;
	}
}

.el-slider {
	.el-slider__runway.show-input {
		margin-right: 110px;
	}

	.el-slider__input {
		width: 90px;
		border-radius: 6px;

		input {
			background: #f5f5f5;
		}
	}
}

.dzToolTip.el-tooltip__popper {
	border-color: #ffffff !important;
	background-color: #ffffff;
	box-shadow: 0px 1px 6px 0px rgba(179, 179, 179, 0.64);

	font-size: 16px;
	font-family: Calibri, Arial, serif;

	.popper__arrow {
		border-bottom-color: #ffffff !important;
		border-top-color: #ffffff !important;
	}
}

.stickerSizeToolTip {
	border-color: #ff4242 !important;
	background-color: #ff4242;
	box-shadow: 0px 1px 6px 0px rgba(179, 179, 179, 0.64);
	font-size: 12px;
	padding: 5px;
	color: #ffffff;
	max-width: 120px;
	min-width: auto;
	white-space: nowrap;
	text-align: center;
	font-family: Calibri, Arial, serif;

	.popper__arrow {
		border-bottom-color: #ff4242 !important;
		border-top-color: #ff4242 !important;

		&::after {
			border-top-color: #ff4242 !important;
		}
	}
}

@include respond-to(mb) {
	.el-message-box {
		width: 95%;
	}

	#comm100-container img {
		display: none;
	}
}
</style>
