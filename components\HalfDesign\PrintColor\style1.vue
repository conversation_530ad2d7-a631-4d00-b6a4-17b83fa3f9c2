<template>
	<div class="mb-4" :class="stepData.styleName">
	    <slot name="stepText"></slot>
		<div class="step-content">
			<div class="step-item" :class="{ active: index === selectIndex }" v-for="(step, index) in stepData.productParamList" :key="index" @click="selectStep(step, index)">
				<half-design-check-box class="mr-1"></half-design-check-box>
				<div class="paramName text-truncate mr-2">
					{{ step.valueName }}
				</div>
				<v-tooltip bottom>
					<template v-slot:activator="{ on, attrs }">
						<v-icon v-bind="attrs" v-on="on" size="18px">
							{{ iconName }}
						</v-icon>
					</template>
					<div class="text-center">
						<div style="text-align: left; font-size: 12px; width: 250px">
							{{ step.remark }}
						</div>
						<div v-if="step.imgDetail" style="max-width: 150px; max-height: 150px">
							<img :src="step.imgDetail" :alt="step.valueName" :title="step.valueName" style="object-fit: contain" />
						</div>
					</div>
				</v-tooltip>
			</div>
		</div>
		<div class="errorTip">
			<v-alert dense outlined type="error">
				{{ langSemiCustom.errorTip }}
			</v-alert>
		</div>
	</div>
</template>

<script>
export default {
	props: {
		stepData: {
			type: Object,
			default: {},
		},
	},
	data() {
		return {
			selectIndex: -1,
			selectItem: null,
		};
	},
	computed: {
		unitPriceStep() {
			return this.getUnitPriceStep();
		},
		langSemiCustom() {
			return this.$store.getters.lang?.semiCustom;
		},
		showText() {
			return (this.stepData.styleName === "style6" || this.stepData.styleName === "style8") && this.qtyPrice && this.qtyPrice.length > 0;
		},
		iconName() {
			let style = this.stepData.styleName;
			if (style === "style7" || style === "style8") {
				return "mdi-image-size-select-actual";
			} else {
				return "mdi-help-circle-outline";
			}
		},
	},
	methods: {
		selectStep(item, index) {
			this.selectIndex = index;
			this.selectItem = item;
			this.$emit("selectStep", {
				type: this.stepData.attributeFlag,
				data: item,
				parent: this.stepData,
				id: this.stepData.id,
			});
			this.$nextTick(() => {
				let container = document.querySelector(".qtyWrap");
				if (container) {
					this.showScrollBtn = container.scrollWidth > container.clientWidth;
				}
			});
		},
		selectDefault() {
			if (this.selectIndex <= -1) {
				let findIndex = this.stepData.productParamList.findIndex((item) => {
					return !item.isBlank;
				});
				if (findIndex > -1) {
					this.selectStep(this.stepData.productParamList[findIndex], findIndex);
				}
			}
		},
	},
	mounted() {},
};
</script>

<style scoped lang="scss">
@import "~assets/css/half.scss";

.priceList {
	position: relative;

	.right-btn {
		display: none;
		justify-content: center;
		align-items: center;
		position: absolute;
		right: 0;
		top: 50%;
		transform: translate(50%, -50%);

		b {
			font-size: 12px;
		}
	}
}

.priceList.showBtn {
	.right-btn {
		display: flex;
	}
}

.qtyWrap {
	position: relative;
	cursor: move;

	.qty-item {
		box-shadow: inset 0 0 20px 20px rgba(0, 0, 0, 0.02);
	}
}

.step-content {
	display: grid;
	margin-bottom: 10px;

	.step-item {
		min-width: 0;
		@include flex-center;
		cursor: pointer;
	}
}

.style4,
.style5 {
	.qtyWrap {
		overflow: auto;
		display: flex;
		flex-wrap: nowrap;
		width: 100%;
		font-size: 14px;

		&::-webkit-scrollbar {
			display: none;
		}

		.qty-item {
			flex: 0 0 100px;
			text-align: center;
			border: 1px solid $border-color;
			margin-right: -1px;

			& > div {
				padding: 2px;
			}

			&:first-child {
				font-weight: 600;
			}

			& > div:first-child {
				border-bottom: 1px solid $border-color;
			}
		}
	}

	.right-btn {
		top: 0;
		bottom: 0;
		right: 0;
		width: 20px;
		transform: none;
		background-color: $background-color2;
	}

	@include respond-to(mb) {
		.qtyWrap {
			font-size: 12px;

			.qty-item {
				flex: 0 0 80px;
			}
		}
	}
}

.style6,
.style8 {
	.qtyWrap {
		overflow: auto;
		display: flex;
		flex-wrap: nowrap;
		width: 100%;
		font-size: 14px;

		& > .qty-item:first-child {
			display: none;
		}

		&::-webkit-scrollbar {
			display: none;
		}

		.qty-item {
			flex: 0 0 100px;
			padding: 2px;
			margin-right: 5px;
			text-align: center;
			border: 1px solid $border-color;

			&:last-child {
				margin-right: 0;
			}

			& > div:last-child {
				color: $color-red;
			}
		}
	}

	.right-btn {
		width: 20px;
		height: 20px;
		background-color: $background-color2;
		border-radius: 50%;
	}

	@include respond-to(mb) {
		.qtyWrap {
			font-size: 12px;

			.qty-item {
				flex: 0 0 80px;
				margin-right: 5px;
			}
		}
	}
}

.style1 .step-content,
.style4 .step-content {
	grid-template-columns: repeat(4, 1fr);
	grid-gap: 10px;

	.step-item {
		@include step-default;

		.check-box {
			display: none;
		}
	}

	@include respond-to(mb) {
		grid-template-columns: repeat(2, 1fr);
		grid-gap: 5px;

		.step-item {
			padding: 10px;
		}
	}
}

.style2 .step-content,
.style5 .step-content {
	grid-template-columns: repeat(3, 1fr);
	grid-row-gap: 10px;

	.step-item {
		min-width: 0;
		border: 1px solid $border-color;
		padding: 10px;
		border-radius: 0;
		margin-right: -1px;
		@media (any-hover: hover) {
			&:hover ::v-deep {
				.check-box {
					border-color: $color-primary;

					.check-box-inner {
						background-color: $color-primary;
					}
				}
			}
		}

		&:nth-child(3n) {
			border-top-right-radius: 6px;
			border-bottom-right-radius: 6px;
		}

		&:nth-child(3n + 1) {
			border-top-left-radius: 6px;
			border-bottom-left-radius: 6px;
		}

		&:last-child {
			border-top-right-radius: 6px;
			border-bottom-right-radius: 6px;
		}
	}

	.step-item.active ::v-deep {
		background-color: var(--color-second);
		border-color: $color-primary;
		border-width: 2px;
		padding: 9px;
		z-index: 1;

		.check-box {
			border-color: $color-primary;

			.check-box-inner {
				background-color: $color-primary;
			}
		}
	}

	@include respond-to(mb) {
		grid-row-gap: 5px;
		grid-template-columns: repeat(2, 1fr);
		.step-item {
			&:nth-child(2n) {
				border-top-right-radius: 2px;
				border-bottom-right-radius: 2px;
			}

			&:nth-child(2n + 1) {
				border-top-left-radius: 2px;
				border-bottom-left-radius: 2px;
			}

			&:last-child {
				border-top-right-radius: 2px;
				border-bottom-right-radius: 2px;
			}
		}
	}
}

.style3 .step-content,
.style6 .step-content,
.style7 .step-content,
.style8 .step-content {
	grid-template-columns: repeat(3, 1fr);
	grid-gap: 20px 10px;
	margin-bottom: 20px;

	.step-item {
		position: relative;
		padding: 10px;
		background-color: $background-color2;
		@include radius-response;

		.v-icon {
			color: #cccccc;
			transition: none;
		}

		.check-box {
			display: none;
		}

		&::after {
			content: "";
			position: absolute;
			bottom: -10px;
			left: 50%;
			transform: translateX(-50%);
			width: 0;
			height: 0;
			border-width: 10px 10px 0;
			border-style: solid;
			border-color: $background-color transparent transparent;
		}

		@media (any-hover: hover) {
			&:hover {
				background-color: $color-primary;
				color: #ffffff;

				.v-icon {
					color: #ffffff;
				}

				&::after {
					border-color: $color-primary transparent transparent;
				}
			}
		}
	}

	.step-item.active {
		background-color: $color-primary;
		color: #ffffff;

		.v-icon {
			color: #ffffff;
		}

		&::after {
			border-color: $color-primary transparent transparent;
		}
	}

	@include respond-to(mb) {
		grid-template-columns: repeat(2, 1fr);
		grid-gap: 5px;
		margin-bottom: 10px;

		.step-item {
			&::after {
				bottom: -5px;
				border-width: 5px 5px 0;
			}
		}
	}
}
</style>
