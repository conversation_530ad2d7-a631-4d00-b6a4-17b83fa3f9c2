<template>
	<div class="summary-box" :class="modal.wrapClass" :style="modal.style">
		<div class="bps-container" v-for="(o, oi) in modal.outer" :key="oi">
			<EditDiv tag-name="h2" v-model:content="modal.outer[0].title.value" @click="setModalType(o.title,modal.outer,'text')" />
			<EditDiv class="des sticker_p" v-model:content="modal.outer[0].subTitle.value" @click="setModalType(o.subTitle,modal.outer,'text')" />
			<div class="sticker_img">
				<div class="img_div" style="width: 60%">
					<div class="img">
						<pic :src="modal.outer[0].img.value" :alt="modal.outer[0].img.alt" :title="modal.outer[0].img.alt" @click="setModalType(modal.outer[0].img,modal.outer,'img')" />
						<EditDiv tag-name="h3" v-model:content="modal.outer[0].img.title.value" @click="setModalType(modal.outer[0].img.title,modal.outer,'text')" />
					</div>
					<div class="img">
						<pic :src="modal.outer[0].img1.value" :alt="modal.outer[0].img1.alt" :title="modal.outer[0].img1.alt" @click="setModalType(modal.outer[0].img1,modal.outer,'img')" />
						<EditDiv tag-name="h3" v-model:content="modal.outer[0].img1.title.value" @click="setModalType(modal.outer[0].img1.title,modal.outer,'text')" />
					</div>
					<div class="img">
						<pic :src="modal.outer[0].img2.value" :alt="modal.outer[0].img2.alt" :title="modal.outer[0].img2.alt" @click="setModalType(modal.outer[0].img2,modal.outer,'img')" />
						<EditDiv tag-name="h3" v-model:content="modal.outer[0].img2.title.value" @click="setModalType(modal.outer[0].img2.title,modal.outer,'text')" />
					</div>
				</div>
				<div class="img_div" style="width: 37.5%;margin-left: 1%">
					<div class="img2">
						<pic :src="modal.outer[0].img3.value" :alt="modal.outer[0].img3.alt" :title="modal.outer[0].img3.alt" @click="setModalType(modal.outer[0].img3,modal.outer,'img')" />
						<EditDiv tag-name="h3" v-model:content="modal.outer[0].img3.title.value" @click="setModalType(modal.outer[0].img3.title,modal.outer,'text')" />
					</div>
					<div class="img2">
						<pic :src="modal.outer[0].img4.value" :alt="modal.outer[0].img4.alt" :title="modal.outer[0].img4.alt" @click="setModalType(modal.outer[0].img4,modal.outer,'img')" />
						<EditDiv tag-name="h3" v-model:content="modal.outer[0].img4.title.value" @click="setModalType(modal.outer[0].img4.title,modal.outer,'text')" />
					</div>
				</div>
			</div>
			<div class="btnWrap" v-if="o.button" @click="setModalType(o.button,modal.outer,'button')">
				<a :href="o.button.url" :title="o.button.alt" :target="o.button.target || '_self'"
					 class="default-button bps-button" :style="{...o.button.style }">
					{{ o.button.value }}
				</a>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	props: {
		data: {
			type: Object,
			default: {},
		}
	},
	data() {
		return {
			modal: {
				style: {},
				type: {index: 0, clickPosition: 'outer'},
				...this.data
			}
		};
	},
	watch: {
		modal: {
			handler(val) {
				if (process.env.isManage) this.$emit("update:data", val);
			},
			immediate: true,
			deep: true
		}
	},
	methods:{
        setModalType(target, targetArray, clickType, event, other) {
            this.$setModal(this, target, targetArray, clickType, event, other)
        },
	},
	mounted() {

	}
};
</script>

<style lang="scss" scoped>
.squareStickers {
	.bps-container {
		.sticker_img {
			.img_div {
				.img:nth-child(2) {
					h3 {
						left: 15% !important;
					}
				}
				.img:nth-child(3) {
					h3 {
						left: 18% !important;
					}
				}
				&:nth-child(2) {
					.img2 {
						&:nth-child(1) {
							h3 {
								right: 15% !important;
							}
						}
						&:nth-child(2) {
							h3 {
								right: 10% !important;
							}
						}
					}
				}
			}
		}
	}
}
.clear-sticker {
	.bps-container {
		.sticker_img {
			.img_div {
				.img:nth-child(2) {
					 h3 {
						 left: 5% !important;
					 }
				 }
				.img:nth-child(3) {
					h3 {
						left: 18% !important;
					}
				}
				&:nth-child(2) {
					.img2 {
						&:nth-child(1) {
							h3 {
								right: 15% !important;
							}
						}
					}
				}
			}
		}
	}
}
.HolographicStickers {
	.bps-container {
		.sticker_img {
			.img_div {
				.img {
					h3 {
						top: 5px !important;
					}
				}
				.img:nth-child(2) {
					h3 {
						left: 10% !important;
					}
				}
				.img:nth-child(3) {
					h3 {
						left: 6% !important;
					}
				}
				&:nth-child(2) {
					.img2 {
						&:nth-child(1) {
							h3 {
								right: 10% !important;
							}
						}
					}
				}
			}
		}
	}
}
.StaticClingsStickers {
	.bps-container {
		.sticker_img {
			.img_div {
				.img:nth-child(2) {
					h3 {
						left: 10% !important;
					}
				}
				&:nth-child(2) {
					.img2 {
						&:nth-child(1) {
							h3 {
								right: 15% !important;
							}
						}
					}
				}
				.img:nth-child(3) h3 {
					left: 15% !important;
				}
				.img2 {
					h3 {
						right: 10% !important;
					}
				}
			}
		}
	}
}
.WhiteVinylStickers {
	.bps-container {
		.sticker_img {
			.img_div {
				.img:nth-child(2) {
					h3 {
						left: 2% !important;
					}
				}
				&:nth-child(2) {
					.img2 {
						&:nth-child(1) {
							h3 {
								right: 15% !important;
							}
						}
					}
				}
				.img:nth-child(3) h3 {
					left: 15% !important;
				}
			}
		}
	}
}
.kiss-sticker {
	.bps-container {
		.sticker_img {
			.img_div {
				.img:nth-child(2) {
					h3 {
						left: 5% !important;
					}
				}
				&:nth-child(2) {
					.img2 {
						&:nth-child(1) {
							h3 {
								right: 5% !important;
							}
						}
					}
				}
			}
		}
	}
}
.Rectanglestickers {
	.bps-container {
		.sticker_img {
			.img_div {
				.img {
					&:nth-child(2) {
						h3 {
							top: 5px;
							left: 5% !important;
						}
					}
					&:nth-child(3) {
						margin-left: 2%;
						h3 {
							top: 5px;
							left: 21% !important;
						}
					}
				}
			}
		}
	}
}
.roundedCornerStickers {
	.bps-container {
		.sticker_img {
			.img_div {
				.img {
					&:nth-child(2) {
						h3 {
							top: 5px;
						}
					}
					&:nth-child(3) {
						margin-left: 2%;
						h3 {
							top: 5px;
							left: 21% !important;
						}
					}
				}
			}
		}
	}
}
.circleStickers {
	.bps-container {
		.sticker_img {
			.img_div {
				.img {
					&:nth-child(2) {
						h3 {
							top: 5px;
						}
					}
					&:nth-child(3) {
						margin-left: 2%;
						h3 {
							top: 5px;
							left: 6% !important;
						}
					}
					&:nth-child(2) {
						h3 {
							left: 2% !important;
						}
					}
				}
			}
		}
	}
}
.bumperStickers {
	.bps-container {
		.sticker_img {
			.img_div {
				.img {
					&:nth-child(2) {
						h3 {
							top: 5px;
						}
					}
					&:nth-child(3) {
						margin-left: 2%;
						h3 {
							top: 5px;
							left: 16% !important;
						}
					}
					&:nth-child(2) {
						h3 {
							left: 20% !important;
						}
					}
				}
			}
		}
	}
}
.summary-box {
	background: #C6E5FD;
	margin-top: 117px;
	padding: 91px 0;
	.bps-container {
			h2 {
				font-size: 42px;
				text-align: center;
			}
		.sticker_p {
			text-align: center;
			font-size: 18px;
			margin-top: 31px;
		}
		.sticker_img {
			font-size: 0;
			margin-top: 52px;
			margin-bottom: 40px;
			.img_div {
				display: inline-block;
				font-size: 0;
				vertical-align: middle;
				.img {
					position: relative;
					h3 {
						font-size: 30px;
						color: #fff;
						position: absolute;
						top: 10px;
						left: 30%;
					}
					&:nth-child(2),&:nth-child(3) {
						display: inline-block;
						width: 49%;
						margin-top: 15px;
					}
					&:nth-child(2) {
						h3 {
							top: 5px;
							left: 13%;
						}
					}
					&:nth-child(3) {
						margin-left: 2%;
						h3 {
							top: 5px;
							left: 4%;
						}
					}
				}
				.img2 {
					position: relative;
					h3 {
						font-size: 30px;
						color: #fff;
						position: absolute;
						top: 9px;
						right: 20%;
					}
					&:nth-child(2) {
						margin-top: 2%;
					}
				}
			}
		}
		.btnWrap {
			width: 20%;
			margin:auto;
		}
	}
}
@media screen and (max-width: $mb-width) {
	.summary-box {
		padding: 51px 0;
		.bps-container {
			h2 {
				font-size: 21px;
			}
			.sticker_p {
				font-size: 12px;
				margin-top: 10px;
				text-align: center;
			}
			.sticker_img {
				margin-top: 21px;
				overflow: auto;
				white-space: nowrap;
				.img_div {
					&:nth-child(1) {
						width: 90% !important;
					}
					&:nth-child(2) {
						width: 57.5% !important;
						margin-left: 3% !important;
					}
					.img {
						h3 {
							font-size: 12px !important;
							top: 3px !important;
						}
					}
					.img2 {
						h3 {
							font-size: 12px !important;
							top: 3px !important;
						}
					}
				}
			}
			.btnWrap {
				width: 40% !important;
			}
		}
	}
}
</style>
