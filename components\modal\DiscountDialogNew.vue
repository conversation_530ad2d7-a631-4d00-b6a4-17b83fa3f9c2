<template>
	<!--新版折扣优惠弹窗-->
	<div>
		<template v-if="(isShowFixedBtn && ($store.state.projectComment.openEmailBinaryLong&32)==32)">
			<div class="fixedBtn" @click="openDiscountDialog()" :primary="!$store.getters.isMobile">
				<span v-if="!$store.getters.isMobile">{{ langNew[0].configBtn}}</span>
				<pic v-else src="https://static-oss.gs-souvenir.com/web/quoteManage/20241210/5-off_2046YrBKpE.png" style="width: 4.8em;" alt="5% OFF" />
				<b class="fixedBtn_close" :class="$store.getters.isMobile?'icon-hxsht-xp-gb':'icon-close'" @click.stop="fixedBtnClose()"></b>
			</div>
		</template>

		<div flex class="mask1" v-show="(!isNotFirst && ($store.state.projectComment.openEmailBinaryLong&32)==32)">
			<template v-for="(item, index) in langNew">
				<div class="discount_content"  v-if="index === 0" v-show="isShowBigBox">
					<!-- <p>{{ $store.state.projectComment.subscribesDiscount }}</p> -->
					<div pointer class="close-icon close-icon1" @click="closeDialog">
						<b class="icon-hxsht-xp-gb"></b>
					</div>
					<div class="content_text1">
						{{item.subTitle}}
					</div>
					<div class="content_text2">
						<strong>{{discountQuantity}}%</strong><span style="font-size:0.6em">{{item.title.replace(/(\s*)\d+%\s*/g, ' ').trim().replace(/\s+/g, ' ')}}</span>
					</div>
					<div class="content_text3">
						{{item.text}}
					</div>
					<div class="content_text4">
						{{item.tip}}
					</div>
					<div flex class="input-box discount_content_input_box">
						<input v-model="email" type="email" :placeholder="lang.emailPlaceholder" @keyup.enter="btn_getFirstDisCount()" />
					</div>
					<span v-if="isEmailUsed" style="color: red;font-size: 0.8em;">{{ lang.emailUsed }}</span>
					<button :primary="true" class="discount_content_btn" @click="btn_getFirstDisCount()">
						{{item.button}}
					</button>

					<div class="content_text5">
						{{item.reminder1}}&nbsp;{{websiteName}}.&nbsp;{{item.reminder2}}
						{{ lang.view }}
						<span class="underline_span">
							<n-link tag="a" title="Terms and Conditions" to="/info/terms-and-conditions">{{ lang.terms }}</n-link>
						</span> &
						<span class="underline_span">
							<n-link tag="a" title="Privacy Policy" to="/info/privacy-policy">{{ lang.privacy }}</n-link>
						</span>
						<span>{{lang.deView}}</span>
					</div>

					<p class="content_text6"><span @click="closeDialog">{{ lang.noDiscount }}</span></p>
				</div>


				<div class="part2 discount_content2" v-else v-show="!isShowBigBox">
					<div pointer class="close-icon close-icon2" @click="closeSonDialog">
						<b class="icon-hxsht-xp-gb"></b>
					</div>
					<img class="content2_img" src="https://sticker-static.oss-accelerate.aliyuncs.com/image/uploads/20230906/neon-signs.png" alt="">
					<div class="content2_text1">
						{{item.subTitle}}
					</div>
					<div class="content2_text2">
						{{item.title}}
					</div>
					<div class="content2_text3">
						{{item.text}}
					</div>
					<button class="discount_content_btn discount_content2_btn" :primary="true" @click="closeSonDialog()">
						{{item.button}}
					</button>
				</div>

			</template>
		</div>
	</div>
</template>

<script>
	import {
		medalsApi
	} from "@/api/medals/medals";
	export default {
		name: "discountDialogNew",
		data() {
			return {
				discountQuantity: 5, //折扣数量

				email: '',

				isShowBigBox: true,

				isNotFirst: true,

				isEmailUsed: false, //邮箱是否已使用过

				isShowFixedBtn: false,
			};
		},
		computed: {
			lang() {
				return this.$store.getters.lang.discountDialog || {};
			},
			langNew() {
				return this.$store.getters.lang.discountDialogNew || [];
			},
			websiteName() {
				// return this.$store.state.pagePath;
				// return this.$store.state.language.domainUrl;
				// return this.$store.state.proName;
				return this.$store.state.logoAltTitle || this.$store.state.proName;
			},
		},
		watch: {
			isNotFirst(newVal) {
				if (!newVal) this.email = this.$options.data().email
			},
			email(newVal, oldVal) {
				this.isEmailUsed = false
			}
		},
		methods: {
			setModalType(target, targetArray, clickType, event, other) {
				this.$setModal(this, target, targetArray, clickType, event, other)
			},
			fixedBtnClose() {
				this.isShowFixedBtn = false
				this.$cookies.set('isShowFixedBtn', false, {
					path: '/',
					maxAge: 60 * 60 * 24 * 360
				});
			},
			openDiscountDialog() {
				this.isNotFirst = false
			},
			close() {
				this.isNotFirst = true
				this.$cookies.set('isNotFirstShowDiscount', true, {
					path: '/',
					maxAge: 60 * 60 * 24 * 360
				});
			},

			// closeDialog2 () {
			// 	this.isShowBigBox = true
			// },
			closeDialog() {
				// this.$store.commit('setMask', false);
				// sessionStorage.setItem('isNotFirstShowDiscount', JSON.stringify(true));
				// this.$emit('close')
				this.close()
			},

			async btn_getFirstDisCount() {
				// var reg = /^([a-zA-Z]|[0-9])(\w|\-)+@[a-zA-Z0-9]+\.([a-zA-Z]{2,4})$/;
				var reg = /^([a-zA-Z0-9._-]+)@([a-zA-Z0-9-]+)\.([a-zA-Z]{2,4})$/;;
				if (!this.email || this.email == '') return this.$toast.error(this.lang.emailToast)
				else if (!reg.test(this.email)) return this.$toast.error(this.lang.emailValidate)
				let res = await medalsApi.getFirstDiscount({
					// proId: this.$store.state.proId,
					email: this.email
				})
				if (res.data == null) {
					this.isEmailUsed = true
					return
				} else {
					this.isEmailUsed = false
					this.isShowFixedBtn = false
					this.$cookies.set('isShowFixedBtn', false, {
						path: '/',
						maxAge: 60 * 60 * 24 * 360
					});
				}
				this.isShowBigBox = false
			},
			closeSonDialog() {
				this.isNotFirst = true
				this.$cookies.set('isNotFirstShowDiscount', true, {
					path: '/',
					maxAge: 60 * 60 * 24 * 360
				});
			},

		},
		mounted() {
			// if (process.client) {
			// 			this.isNotFirst = sessionStorage.getItem('isNotFirstShowDiscount') || null
			// 		}
			console.log('subscribesDiscount', this.$store.state.projectComment.subscribesDiscount);
			if (process.browser) {
				let isShowFixedBtn = this.$cookies.get('isShowFixedBtn') //isShowFixedBtn默认得给false 不然用户叉掉后刷新会弹出来一下
				this.isShowFixedBtn = isShowFixedBtn
				if (isShowFixedBtn == undefined) {
					this.isShowFixedBtn = true
				}
				if (isShowFixedBtn) {
					this.$cookies.set('isShowFixedBtn', isShowFixedBtn, { //cookies 时间重新赋值
						path: '/',
						maxAge: 60 * 60 * 24 * 360
					});
				}
				let isNotFirstShowDiscount = this.$cookies.get('isNotFirstShowDiscount') //用isNot默认给false 然后判断!isNot 就不需要跟上面一样判断undefined
				this.isNotFirst = isNotFirstShowDiscount
				if (isNotFirstShowDiscount) {
					this.$cookies.set('isNotFirstShowDiscount', isNotFirstShowDiscount, {
						path: '/',
						maxAge: 60 * 60 * 24 * 360
					});
				}
			}
		},
		created() {},
	};
</script>

<style lang="scss" scoped>
	.discount_content {
		min-width: 25vw;
		width: 25vw;
		min-height: 25.7813vw;
		padding: 3em 3em 2em;
		text-align: center;
		position: relative;
		// background-image: url("https://static-oss.gs-souvenir.com/web/quoteManage/20250321/pcBackground_20250321pfRcJG.png"), linear-gradient(180deg, #FAEDC4 43%, #FFFFFF 100%);
		background-image: url("https://static-oss.gs-souvenir.com/web/quoteManage/20250324/PC_20250324GxHb2S.png"), linear-gradient(180deg, #FAEDC4 43%, #FFFFFF 100%);
		border-radius: 3em 1em 3em 1em;
		background-position: center;
    	background-size: cover;
	}

	.content_text1,
	.content_text2,
	.content_text3 {
		color: #FFF;
		line-height: 1;
	}
	.content_text1,
	.content_text3 {
		font-weight: bold;
	}

	.content_text1{
		font-size: 1.375em;
		margin-left: 13vw;
	}
	.content_text2 {
		font-size: 3.75em;
		margin-left: 3vw;
		margin-top: -0.5rem;
		display: inline-block;
		transform: skew(-10deg);//倾斜
		display: flex;
		justify-content: center;
		align-items: baseline;
		column-gap: 0.9rem;
	}
	.content_text3 {
		font-size: 1.125em;
		margin-left: 2vw;
    	margin-top: 0.5rem;
	}


	[theme='7'] .content_text2 {
		h2::before {
			content: none;
		}
	}

	.content_text4 {
		font-size: 1em;
		margin-top: 2.5em;
		text-align: left;
	}

	.content_text5 {
		font-size: calc(1em - 2px);

		.underline_span {
			text-decoration: underline;
		}
	}

	.content_text6 {
		font-weight: bold;
		font-size: calc(1em + 2px);
		text-decoration: underline;
		margin-top: 1.25rem;

		span {
			cursor: pointer;
		}
	}


	.discount_content_btn {
		width: 100%;
		height: 3.125em;
		margin: 0.75em 0 1.125em;
		border-radius: 0.25em !important;
		// border-radius: 0;
		// background: $color-primary;
		// background: linear-gradient(90deg, $color-dark 0%, $btn-primary 100%);
	}

	[theme='6'] .discount_content_btn {
		background: linear-gradient(90deg, #FE852F 0%, #E83D23 100%);
		color: #fff;
	}

	[theme='7'] .discount_content_btn {
		background: linear-gradient(90deg, #FDD206 0%, #D27745 100%);
		color: #fff;
	}

	[theme='14'] .discount_content_btn {
		background: linear-gradient(90deg, rgb(255 131 81) 0%, rgb(255 216 0) 100%);
		color: #000;
	}

	.discount_content_btn:hover {
		transform: scale(1.02);
	}

	.discount_content_input_box {
		flex: 1;
		height: 3.125em;
		line-height: 3.125em;
		background: #FFF;
		border-radius: 0;
		border: none;
		margin-top: 0.7em;
		border-radius: 0.25em;

		input {
			flex: 1;
			padding: 0 0.625rem;
			color: var(--text-primary);
		}
	}

	.close-icon {
		position: absolute;
		right: 1.2em;
		top: 1.2em;
		top: 0.625rem;
		right: 0.625rem;
		// transform: scale(1.2);
	}

	.close-icon1 {
		color: #333;
		width: 2.25rem;
		height: 2.25rem;
		background: #FFF;
		display: flex;
		justify-content: center;
		align-items: center;
		border-radius: 50%;
	}

	.fixedBtn {
		min-width: 10em;
		min-height: 3.125rem;
		width: max-content;
		text-align: center;
		font-size: 1.2em;
		// width: 180px;
		position: fixed;
		left: 1vw;
		bottom: 0.5em;
		border-radius: 0.4em;
		border: 1px solid #fff;
		color: #FFF;
		cursor: pointer;
		z-index: 999;
		padding: 0 1.3em;
		clip-path: unset !important;
		background-color: var(--btn-primary);
		display: flex;
		align-items: center;

		.fixedBtn_close {
			// transform: translateX(26px) translateY(-10px) rotate(45deg)
			// transform: rotate(45deg);
			position: absolute;
			top: .05vw;
			right: .2vw;
			font-weight: 400;
		}
	}

	.discount_content2 {
		min-width: 25.9375em;//415
		width: 25.9375em;
		border-radius: 1em;
		background: #FFF;
		padding: 1em 4em calc(2em + 4px);
		text-align: center;
		position: relative;
	}

	.content2_text1,
	.content2_text2 {
		font-weight: bold;
	}

	.content2_text1 {
		font-size: 1.5em;
	}

	.content2_text2 {
		font-size: calc(2em + 4px);
		// color: #3C5FFE;
		color: var(--color-primary);
		margin: 0.15em 0
	}

	.content2_text3 {
		font-size: 0.8em;
		transform: scale(1.2);
	}

	.content2_img {
		height: 6vw;
		object-fit: contain;
	}

	.mask1 {
		height: 100vh;
		// max-height: 1200px;
		align-items: center;
		justify-content: center;
		background: rgba($color: black, $alpha: 0.85);
		position: fixed;
		z-index: 9999999;
		right: 0;
		left: 0;
		top: 0;
	}

	.manageMask {
		position: static;
	}


	@media screen and (max-device-width: $pad-width) {
	}

	@media screen and (max-device-width: $mb-width) {
		.discount_content {
			background-image: url("https://static-oss.gs-souvenir.com/web/quoteManage/20250324/MB_20250324CQCpED.png"), linear-gradient(180deg, #FAEDC4 43%, #FFFFFF 100%);
			background-size: cover;
		}
		.discount_content2 {
			min-width: 290px;
			width: 290px;
			padding: 1.5em calc(4em - 6px) calc(2em + 1px);
		}

		.discount_content {
			min-width: 28.75rem;
			width: 28.75rem;
			padding:calc(2rem + 2px) 2.5rem 2.5rem;
		}

		.discount_content_input_box,
		.discount_content_btn {
			height: calc(2.75rem + 2px); //35
			line-height: calc(2.75rem + 2px);
		}

		.discount_content_input_box {
			margin-top: 1em;
		}

		.discount_content_btn {
			margin: 0.67em 0 1em;
		}

		.discount_content2_btn {
			margin: 1.5em 0 0;
		}

		//
		.content_text1,
		.content_text2,
		.content_text3{
			font-weight: bold;
		}

		.content_text1{
			font-size: calc(1rem + 2px);
			margin-left: 41vw;
			margin-left: 57%;
		}
		.content_text2{
			column-gap: 0.5rem;
			margin-left: 17%;
			font-size: calc(3em + 1px);
			margin-top: 0.6rem;
		}
		.content_text3{
			font-size: 0.75rem;
			margin-left: 14vw;
		}

		.content_text4,
		.content_text5 {
			font-size: 1em;
		}

		.content_text5 {
			padding: 0;
		}

		.content_text6 {
			font-size: calc(1em + 1px);
		}

		.content2_img {
			height: 18vw;
		}
		.fixedBtn {
			min-width: initial;
			bottom: 1em;
			padding: initial;
			border: initial;
			background: initial;

			.fixedBtn_close {
				top: 3px;
				right: 5px;
				font-size: 0.5em;
				transform: none;
				color: #fff;
			}
		}
	}
</style>