<template>
	<div class="dashboard-con-wrap">
		<div class="part1" v-permission="'p-2'">
			<v-row>
				<v-col cols="3">
					<v-card elevation="0" height="133" class="pa-2 rounded white pointer">
						<div class="d-flex align-center px-3" style="height: 100%">
							<v-btn elevation="0" fab color="primary" large class="white--text">
								<v-icon>mdi-abacus</v-icon>
							</v-btn>
							<div class="ml-4">
								<div class="num">
									{{ todayData.todayInquiries }}
								</div>
								<div class="des">
									Today's Inquiries
								</div>
							</div>
						</div>
					</v-card>
				</v-col>
				<v-col cols="3">
					<v-card elevation="0" height="133" class="pa-2 rounded white pointer">
						<div class="d-flex align-center px-3" style="height: 100%">
							<v-btn elevation="0" fab color="#FBBC04" large class="white--text">
								<v-icon>mdi-calendar-multiselect</v-icon>
							</v-btn>
							<div class="ml-4">
								<div class="num">
									{{ todayData.todayOrders }}
								</div>
								<div class="des">
									Today's Orders
								</div>
							</div>
						</div>
					</v-card>
				</v-col>
				<v-col cols="3">
					<v-card elevation="0" height="133" class="pa-2 rounded white pointer">
						<div class="d-flex align-center px-3" style="height: 100%">
							<v-btn elevation="0" fab color="#EA4335" large class="white--text">
								<v-icon>mdi-application-outline</v-icon>
							</v-btn>
							<div class="ml-4">
								<div class="num">
									{{ todayData.todayRevenue | formatText }}
								</div>
								<div class="des">
									Today's Revenue
								</div>
							</div>
						</div>
					</v-card>
				</v-col>
				<v-col cols="3">
					<v-card elevation="0" height="133" class="pa-2 rounded white pointer">
						<div class="d-flex align-center px-3" style="height: 100%">
							<v-btn elevation="0" fab color="#34A752" large class="white--text">
								<v-icon>mdi-account-badge</v-icon>
							</v-btn>
							<div class="ml-4">
								<div class="num">
									{{ todayData.todayCustomers }}
								</div>
								<div class="des">
									Today's New Members
								</div>
							</div>
						</div>
					</v-card>
				</v-col>
			</v-row>
		</div>
		<div class="part2 mt-6">
			<v-row no-gutters>
				<v-col cols="8" v-permission="'p-4'">
					<v-card elevation="0" class="statistics white rounded">
						<div class="cusTitle">
							<div class="leftTab">
								<v-tabs background-color="#F5F6FA" height="40" v-model="tabs" @change="changeTab">
									<v-tab :ripple="false" class="tab-item-title" v-for="item in tabList"
										   :key="item.title">
										{{ item.title }}
										<div class="circle"></div>
									</v-tab>
								</v-tabs>
							</div>
							<div class="d-flex align-center flex-shrink-0">
								<div class="mr-7">
									<div class="dz-right">
										<v-select @change="selectChange" :items="items" label="Time" solo
												  v-model="timeDefault"></v-select>
									</div>
									<!-- <v-menu offset-y>
										<template v-slot:activator="{ on, attrs }">
											<v-btn depressed class="primary--text" v-bind="attrs" v-on="on">
												Monthy
												<v-icon right dark> mdi-chevron-down</v-icon>
											</v-btn>
										</template>
										<v-list>
											<v-list-item v-for="(item, index) in items" :key="index">
												<v-list-item-title>{{ item.title }}</v-list-item-title>
											</v-list-item>
										</v-list>
									</v-menu> -->
								</div>
								<v-tooltip top>
									<template v-slot:activator="{ on, attrs }">
										<v-btn v-bind="attrs" v-on="on" elevation="0" fab color="primary" width="20"
											   height="20" x-small
											   class="white--text mr-2">
											<v-icon x-small>mdi-help
											</v-icon>
										</v-btn>
									</template>
									<span>Programmatic tooltip</span>
								</v-tooltip>
								<nuxt-link to="/manage/analysis">See Detail
									<v-icon color="primary">mdi-chevron-right</v-icon>
								</nuxt-link>
							</div>
						</div>
						<div class="statistics-con">
							<div id="myChart"></div>
						</div>
					</v-card>
				</v-col>
				<v-col cols="4" class="pl-4" v-permission="'p-3'">
					<v-card elevation="0" class="todoList white rounded">
						<div class="cusTitle">
							<span class="title">To-do List</span>
							<div class="d-flex align-center">
								<v-tooltip top>
									<template v-slot:activator="{ on, attrs }">
										<v-btn v-bind="attrs" v-on="on" elevation="0" fab color="primary" width="20"
											   height="20" x-small
											   class="white--text mr-2">
											<v-icon x-small>mdi-help
											</v-icon>
										</v-btn>
									</template>
									<span>Programmatic tooltip</span>
								</v-tooltip>
								<nuxt-link to="/manage/orders">See Detail
									<v-icon color="primary">mdi-chevron-right</v-icon>
								</nuxt-link>
							</div>
						</div>
						<v-list class="mt-2 todoWrap">
							<template v-for="(item, index) in todoList">
								<div class="d-flex align-center pl-7 todoItem" @click="todoListItem(item)">
									<span class="ind">{{ index + 1 }}</span>
									<v-list-item two-line class="pa-0">
										<div class="mr-6 rounded-circle d-flex justify-center align-center"
											 style="background-color:#F5F6FA;width:62px; height:46px;">
											<b :class="gettodoListIcon(index)" :style="gettodoListIconColor(index)"></b>
											<!-- <v-icon large :color="gettodoListIcon(index).iconColor">{{
													gettodoListIcon(index).iconName
											}}</v-icon> -->
											<!-- <v-img :src="item.pic" width="62" height="46"></v-img> -->
										</div>
										<v-list-item-content>
											<div class="des">{{ item.des }}</div>
											<div class="title">{{ item.title }}</div>
										</v-list-item-content>
										<div class="arrow mr-4">
											<v-icon>mdi-chevron-right</v-icon>
										</div>
									</v-list-item>
								</div>
								<v-divider v-if="index !== todoList.length - 1"></v-divider>
							</template>
						</v-list>
					</v-card>
				</v-col>
			</v-row>
		</div>
		<div class="part3 mt-6" v-permission="'p-5'">
			<v-row no-gutters>
				<v-col cols="8">
					<v-card elevation="0" class="order white rounded">
						<div class="cusTitle">
							<span class="title">Latest Orders</span>
							<div class="d-flex align-center">
								<v-tooltip top>
									<template v-slot:activator="{ on, attrs }">
										<v-btn v-bind="attrs" v-on="on" elevation="0" fab color="primary" width="20"
											   height="20" x-small
											   class="white--text mr-2">
											<v-icon x-small>mdi-help
											</v-icon>
										</v-btn>
									</template>
									<span>Programmatic tooltip</span>
								</v-tooltip>
								<nuxt-link to="/manage/orders">See Detail
									<v-icon color="primary">mdi-chevron-right</v-icon>
								</nuxt-link>
							</div>
						</div>
						<v-data-table class="elevation-0 mt-3 orderTable inquiryTable" :headers="headers"
									  :items="latestOrders"
									  hide-default-footer item-key="id">
							<template v-slot:item.orderId="{ item }">
								<div style="font-size:12px;cursor: pointer;" @click="toDetail(item)">OID:{{
										item.oid
									}}
								</div>
								<div style="font-size:12px; " v-if="item.pid">PID:{{
										item.pid
									}}
								</div>
							</template>
							<template v-slot:item.customer="{ item }">
								<div class="px-2">
									<div style="font-size:12px">
										<v-icon x-small color="#B3B3B3" class="mr-1">mdi-account</v-icon>
										<span>{{ item.firstName }} {{ item.lastName }}</span>
										<b v-if="item.isReputable === 2" class="icon-a-jxs-djszhuanhuan ml-1"
										   style="color: #f1ad19"></b>
									</div>
									<div style="font-size:12px">
										<v-icon x-small color="#B3B3B3" class="mr-1">mdi-email
										</v-icon>
										<span>{{ item.email }}</span>
									</div>
									<div style="font-size:12px">
										<v-icon x-small color="#B3B3B3" class="mr-1">mdi-cellphone
										</v-icon>
										<span>{{ item.telephone }}</span>
									</div>
								</div>

							</template>
							<template v-slot:item.time="{ item }">

								<span style="font-size:12px">{{ item.createTime }}</span>
							</template>
							<template v-slot:item.productsType="{ item }">
								<span style="font-size:12px"> {{ item.productType }}</span>

							</template>
							<template v-slot:item.totalPrice="{ item }">
								<span style="font-size:12px">$ {{ item.totalPrice }}</span>
							</template>
							<template v-slot:item.paymentStatus="{ item }">
									<span style="font-size:12px" :style="customPaymentStatusColor(item.paymentStatus)">{{
											getPaymentText(item.paymentStatus)
										}}</span>
							</template>
							<template v-slot:item.orderStatus="{ item }">
								<span style="font-size:12px">{{ getOrderText(item.orderStatus) }}</span>
							</template>
							<template v-slot:item.salesman="{ item }">
								<span style="font-size:12px">{{ item.salesman }}</span>
							</template>

							<!-- <template v-slot:item.time="{ item }">
								<span style="color:#999999">{{ item.time }}</span>
							</template>
							<template v-slot:item.orderNum="{ item }">
								<span style="color:#999999">{{ item.orderNum }}</span>
							</template>
							<template v-slot:item.amount="{ item }">
								<span style="font-weight: 700">${{ item.amount }}</span>
							</template>
							<template v-slot:item.status="{ item }">
								<div class="statusBox">
									<span>{{ getOrderStatus(item.status) }}</span>
								</div>
							</template> -->
						</v-data-table>
					</v-card>
				</v-col>
				<v-col cols="4" class="pl-4">
					<v-card elevation="0" class="topCategory white rounded">
						<div class="cusTitle">
							<span class="title">Top Category</span>
							<div class="d-flex align-center">
								<v-tooltip top>
									<template v-slot:activator="{ on, attrs }">
										<v-btn v-bind="attrs" v-on="on" elevation="0" fab color="primary" width="20"
											   height="20" x-small
											   class="white--text mr-2">
											<v-icon x-small>mdi-help
											</v-icon>
										</v-btn>
									</template>
									<span>Programmatic tooltip</span>
								</v-tooltip>
								<nuxt-link to="/manage/siteEdit/productEdit">See Detail
									<v-icon color="primary">mdi-chevron-right</v-icon>
								</nuxt-link>
							</div>
						</div>
						<v-list class="mt-3 categoryListWrap">
							<template v-for="(item, index) in topCategory">
								<div class="d-flex align-center categoryItem">
									<span class="ind">#{{ index + 1 }}</span>
									<v-list-item three-line class="align-center pa-0 pb-2">
										<div class="mr-5">
											<v-img :src="item.pic" width="86" height="86" contain
												   class="rounded"></v-img>
										</div>
										<v-list-item-content>
											<div class="name">{{ item.productName }}</div>
											<div class="orderNum">{{ item.orderCount }} order</div>
											<div class="revenue">Total Revenue ${{ item.totalAmount }}</div>
										</v-list-item-content>
									</v-list-item>
								</div>
								<v-divider v-if="index !== topCategory.length - 1"></v-divider>
							</template>
						</v-list>
					</v-card>
				</v-col>
			</v-row>
		</div>
	</div>
</template>

<script>
import {getAllData, filterData} from '@/api/manage/dashboard'
import {CustomersStatistics, RevenueStatistics, OrdersStatistics, InquiriesStatistics} from '@/assets/constant/config'

export default {
	name: "dashboard",
	async created() {
		// 获取当前页面所需的所有数据
		try {
			let allData = await getAllData({proId: this.$store.getters["manage/getProId"]})
			// console.log('allData', allData);

			// 给todayData注入数据
			Object.keys(this.todayData).forEach(key => {
				this.todayData[key] = allData.data[key]
			})
			// console.log('this.todayData', this.todayData);
			// this.todayDate
			// *--- 给todoList注入数据
			this.todoList = allData.data.todoList

			// *--- 给topCategory注入数据
			this.topCategory = allData.data.topCategory
			// console.log('this.topCategory', this.topCategory);

			// *---- 给Latest Orders注入数据
			this.latestOrders = allData.data.latestOrders

			// *---- 给条形图注入数据
			this.options.dataset = allData.data.dataset
			this.echartsInit()
			// console.log('createdOptions', this.options);
		} catch (error) {
		}
	},
	data() {
		return {
			myChart: '',
			todayData: {
				todayCustomers: 0,
				todayInquiries: 0,
				todayOrders: 0,
				todayRevenue: 0,
			},
			type: 1,
			period: 1,
			todoListIcon: [
				{iconColor: '#91AEF2', iconName: 'icon-jxsht-ybp-hb'},
				{iconColor: '#FFE089', iconName: 'icon-jxsht-ybp-xg'},
				{iconColor: '#87eded', iconName: 'icon-jxsht-ybp-gc'},
				{iconColor: '#8CB9F3', iconName: 'icon-jxsht-ybp-fh'},
				{iconColor: '#91AEF2', iconName: 'icon-jxsht-ybp-ky'},
				{iconColor: '#F6967F', iconName: 'icon-jxsht-ybp-tk'}
			],
			options: {
				grid: {
					width: '',
					left: '0',
					bottom: '0',
					containLabel: true,
					trigger: 'axis',
					tooltip: {
						formatter: (data) => {
							// console.log('this', this, data);
							let res = `<div style="width: 209px;background: rgba(255,255,255,0.9000);border-radius: 10px;">
											<div>${data.name} 2022</div>`;
							let dimensionNames = data.dimensionNames;
							for (let i = 1; i < dimensionNames.length; i++) {
								res += `<div style="margin-top:10px;">
										<div style="display: flex;justify-content: space-between">
											<div>
												<span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:${this.options.series[i - 1].itemStyle.color}"></span>
												<span>${data.dimensionNames[i]}</span>
											</div>
											<span>${data.data[i]}</span>
									</div>
								<div>`
							}
							return res + '</div>'
						}
					}
				},
				legend: {
					left: '0',
					itemWidth: 10,
					itemHeight: 10,
					itemGap: 30,
					icon: 'circle',
					formatter: [
						'{a|{name}}'
					].join('\n'),
					textStyle: {
						height: 12,
						rich: {
							a: {
								verticalAlign: 'middle',
								fontSize: 16,
								color: '#999999',
								fontFamily: 'Roboto'
							},
						}
					},
					data: [
						{
							name: 'All Inquiries',
							itemStyle: {
								color: '#235DE6'
							},
						},
						{
							name: 'New Inquiries',
							itemStyle: {
								color: '#4F7DEB'
							},
						},
						{
							name: 'Artwork Confirmed',
							itemStyle: {
								color: '#7A9EF0'
							},
						},
						{
							name: 'Ordered',
							itemStyle: {
								color: '#A6BFF5'
							},
						},
						{
							name: 'Cancelled',
							itemStyle: {
								color: '#FF727E'
							},
						},
						{
							name: 'Others',
							itemStyle: {
								color: '#FFB172'
							},
						}
					]
				},
				tooltip: {},
				xAxis: {
					type: 'category',
					// data: ['lan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Des']
				},
				yAxis: {
					type: 'value',
				},
				// Declare several bar series, each will be mapped
				// to a column of dataset.source by default.
				series: [{
					type: 'bar',
					name: 'All Inquiries',
					// data: [10, 52, 200, 334, 390, 330, 220, 52, 200, 334, 390, 330, 220],
					itemStyle: {
						color: '#235DE6',
						borderRadius: [3, 3, 0, 0],
					},
				}, {
					type: 'bar',
					name: 'New Inquiries',
					// data: [10, 52, 200, 334, 390, 330, 220, 52, 200, 334, 390, 330, 220],
					itemStyle: {
						color: '#4F7DEB',
						borderRadius: [3, 3, 0, 0],
					}
				}, {
					type: 'bar',
					name: 'Artwork Confirmed',
					// data: [10, 52, 200, 334, 390, 330, 220, 52, 200, 334, 390, 330, 220],
					itemStyle: {
						color: '#7A9EF0',
						borderRadius: [3, 3, 0, 0],
					}
				}, {
					type: 'bar',
					name: 'Ordered',
					// data: [10, 52, 200, 334, 390, 330, 220, 52, 200, 334, 390, 330, 220],
					itemStyle: {
						color: '#A6BFF5',
						borderRadius: [3, 3, 0, 0],
					}
				}, {
					type: 'bar',
					name: 'Cancelled',
					// data: [10, 52, 200, 334, 390, 330, 220, 52, 200, 334, 390, 330, 220],
					itemStyle: {
						color: '#FF727E',
						borderRadius: [3, 3, 0, 0],
					}
				}, {
					type: 'bar',
					name: 'Others',
					// data: [10, 52, 200, 334, 390, 330, 220, 52, 200, 334, 390, 330, 220],
					itemStyle: {
						color: '#FFB172',
						borderRadius: [3, 3, 0, 0],
					}
				}]
			},
			timeDefault: 'Daily',
			items: [
				'Daily',
				'Weekly',
				'Monthly',
				'Yearly',
			],
			todoList: [],
			topCategory: [],
			headers: [
				{
					text: 'Order Id',
					align: 'start',
					value: 'orderId',
					sortable: false
				},
				{text: 'Customer', value: 'customer', sortable: false, align: 'start'},
				{text: 'Time', value: 'time', sortable: false, align: 'center'},
				{text: 'Products Type', value: 'productsType', sortable: false, align: 'center'},
				{text: 'TotalPrice', value: 'totalPrice', sortable: false, align: 'center'},
				{text: 'Payment Status', value: 'paymentStatus', sortable: false, align: 'center'},
				{text: 'Order Status', value: 'orderStatus', sortable: false, align: 'center'},
				{text: 'Salesman', value: 'salesman', sortable: false, align: 'center'},
			],
			latestOrders: [],
			tabs: 0,
			tabList: [
				{
					title: 'Inquiries Statistics'
				},
				{
					title: ' Orders Statistics'
				},
				{
					title: 'Revenue Statistics '
				},
				{
					title: 'Customers Statistics'
				},
			],
			chartWidth: ''
		}
	},
	methods: {
		todoListItem(item) {
			// console.log('todoListItem: ', item);
			this.$router.push({
				name: 'manage-orders',
				params: {
					orderStatus: item.type
				}
			})
		},
		async toDetail(item) {
			const navigationResult = await
				this.$router.push({
					name: 'manage-orders-id',
					params: {
						id: item.id
					}
				})

			if (navigationResult) {
				// 导航被阻止
			} else {
				// 导航成功 (包括重新导航的情况)
				window, scrollTo(0, 0)
				// console.log('导航成功',this);
			}

		},
		customPaymentStatusColor(val) {
			let style;
			switch (val) {
				case 1:
					style = {
						color: '#EA4335'
					}
					break;
			}
			return style
		},
		getPaymentText(status) {
			return this.$store.state.manage.paymentStatus.find(item => item.status === status)?.text
		},
		getOrderText(status) {
			return this.$store.state.manage.orderStatus.find(iem => iem.status ===
				status)?.text
		},
		filterBarData(type, period) {
			filterData(
				{
					period, // 类型 1-inquiry 2-order 3-revenue 4-customer
					type, // 周期 1-daily 2-weekly 3-monthly 4-yearly
					proId: this.$store.getters["manage/getProId"],
				})
				.then(res => {
					// console.log('type', type);
					// console.log('filterBarData', res);
					if (type == 1) {
						InquiriesStatistics.dataset.dimensions = res.data.dimensions
						InquiriesStatistics.dataset.source = res.data.source
						this.options = InquiriesStatistics
					} else if (type == 2) {
						OrdersStatistics.dataset.dimensions = res.data.dimensions
						OrdersStatistics.dataset.source = res.data.source
						this.options = OrdersStatistics

					} else if (type == 3) {
						RevenueStatistics.dataset.dimensions = res.data.dimensions
						RevenueStatistics.dataset.source = res.data.source
						this.options = RevenueStatistics
					} else if (type == 4) {
						CustomersStatistics.dataset.dimensions = res.data.dimensions
						CustomersStatistics.dataset.source = res.data.source
						this.options = CustomersStatistics
					}
					// console.log('changeOptions', this.options);
					this.echartsInit()
				})
				.catch(error => {
					console.log('filterBarData', error);
				})
		},
		selectChange(value) {
			// console.log('Select change: ' + value);
			// console.log(this.items.indexOf(value) + 1);
			let period = this.items.indexOf(value) + 1
			this.period = period
			this.filterBarData(this.type, period)
		},
		changeTab(value) {
			let type = ++value
			this.type = type
			// console.log('changeTab', type);
			this.filterBarData(type, this.period)
		},
		gettodoListIconColor(index) {
			return {
				color: this.todoListIcon[index].iconColor,
				fontSize: '22px'
			}
		},
		gettodoListIcon(index) {
			let iconName = this.todoListIcon[index].iconName

			return {
				[iconName]: true

			}
		},
		echartsInit() {
			// 开始渲染
			this.$echarts.init(this.myChart, null, {renderer: 'svg'}).setOption(this.options, {notMerge: true})
		},
	},
	mounted() {
		try {
			this.chartWidth = document.querySelector('.statistics-con').clientWidth;
			// 找到容器
			this.myChart = document.getElementById('myChart');
		} catch (e) {

		}
	}
}
</script>

<style scoped lang="scss">
/*  */
.dz-right {
	width: 120px;
	height: 40px;
	background: #ffffff;
	border: 0.0521vw solid #e5e5e5;
	border-radius: 0.3125vw;
	/* margin-top: 0.9896vw; */
	overflow: hidden;

	::v-deep .v-select__slot {
		top: -4px;
	}
}

/* 更改表格的默认样式 */
.inquiryTable {
	::v-deep .v-data-table__wrapper > table > tbody > tr > td {
		/* font-size: 14px; */
		padding: 0 10px;
	}
}

.dashboard-con-wrap {
	::v-deep .v-tabs-slider-wrapper {
		display: none;
	}

	.subTitle {
		font-size: 16px;
		font-weight: bold;
		color: #666666;
	}

	.part1 {
		.num {
			font-size: 24px;
			font-weight: bold;
			color: #333333;
		}

		.des {
			font-size: 18px;
			font-style: italic;
			color: #999999;
		}
	}

	.part2 {
		.statistics {
			display: flex;
			flex-direction: column;
			height: 500px;
			padding: 30px 20px;

			.leftTab {
				width: 660px;
				border-radius: 6px;
			}

			.statistics-con {
				overflow: auto;
				flex: 1;
				width: 100%;
				height: 0;
				padding: 40px 20px 0;

				#myChart {
					width: 100%;
					height: 100%;
				}
			}
		}

		.todoList {
			display: flex;
			flex-direction: column;
			height: 500px;
			padding: 30px 20px 0px;

			.todoWrap {
				overflow: auto;
				height: 0;
				flex: 1;
			}

			.todoItem {
				.ind {
					color: #999999;
					font-size: 14px;
					margin-right: 20px;
				}

				.title {
					color: #333333;
					font-size: 18px !important;
					margin-top: 4px;
				}

				.des {
					color: #999999;
					font-size: 16px !important;
				}
			}
		}
	}

	.part3 {
		.order {
			display: flex;
			flex-direction: column;
			height: 406px;
			padding: 24px;

			.orderTable {
				overflow: auto;
				flex: 1;
				height: 0;

				.statusBox {
					margin: 0 auto;
					width: 222px;
					height: 50px;
					background: #FFFFFF;
					border: 1px solid #EBEBEB;
					border-radius: 6px;
					color: #91AEF2;
				}
			}
		}

		.topCategory {
			display: flex;
			flex-direction: column;
			height: 403px;
			padding: 24px;

			.categoryListWrap {
				overflow: auto;
				flex: 1;
				height: 0;
			}

			.categoryItem {
				.ind {
					color: #999999;
					font-size: 14px;
					margin-right: 20px;
				}

				.name {
					font-size: 16px;
					color: #999999;
				}

				.orderNum {
					font-size: 16px;
					color: #333333;
					margin: 4px 0;
				}

				.revenue {
					font-size: 16px;
					color: #333333;
					font-weight: 700;
					font-style: italic;
				}
			}
		}
	}
}

.cusTitle {
	display: flex;
	align-items: center;
	justify-content: space-between;

	.title {
		font-size: 24px;
		font-weight: 700;
	}
}

.tab-item-title {
	position: relative;
	text-transform: capitalize;
	letter-spacing: normal;
	margin-top: 10px;
	margin-right: 15px;
	font-weight: 700;
	color: #F5F6FA;

	.circle {
		display: none;

		&::before {
			content: '';
			position: absolute;
			width: 6px;
			height: 6px;
			left: -6px;
			bottom: 0;
			background: radial-gradient(circle at top left, transparent 6px, #fff 0) top left;
		}

		&::after {
			content: '';
			position: absolute;
			width: 6px;
			height: 6px;
			right: -6px;
			bottom: 0;
			background: radial-gradient(circle at top right, transparent 6px, #fff 0) top right;
		}
	}

	&::before {
		display: none;
	}

	&.v-tab--active {
		background-color: #fff;
		border-top-left-radius: 6px;
		border-top-right-radius: 6px;
		color: #333333;

		.circle {
			display: block;
		}
	}
}
</style>
