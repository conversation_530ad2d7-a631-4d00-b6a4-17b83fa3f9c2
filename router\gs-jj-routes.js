const { resolve } = require("path");

export const GSJJGalleryPageUrlPrefix = [
	"lapel-pins",
	"enamel-pins",
	"baseball-trading-pins",
	"challenge-coins",
	"medals",
	"embroidery-patches",
	"pvc-patches",
	"keychains",
	"lanyards",
	"luggage-tags",
	"silicone-wristbands",
	"belt-buckles",
	"ornaments",
	"cufflinks",
	"golf-ball-markers",
	"stickers",
	"pvc-coasters",
	"pvc-lapel-pins",
	"pvc-labels",
	"pvc-zipper-pulls",
];

export const GSJJStockPageUrlPrefix = [
	"lapel-pins",
	"baseball-trading-pins",
	"medals",
	"lanyards",
	"embroidery-patches",
	"challenge-coins",
	"keychains",
	"greek",
	"belt-buckles",
	"pvc-patches",
	"buttons",
	"stickers",
	"ornaments",
];

const galleryRoutes = GSJJGalleryPageUrlPrefix.map((item) => [
	{
		path: `/:lang?/${item}/s/:cateName`,
		name: `lang-${item}-s-cateName`,
		component: resolve(__dirname, "../pages/_lang/exhibition/-list.vue"),
	},
	{
		path: `/:lang?/${item}/s/:cateName/:page.html`,
		name: `lang-${item}-s-cateName-page`,
		component: resolve(__dirname, "../pages/_lang/exhibition/-list.vue"),
	},
	{
		path: `/:lang?/${item}/exhibit/:cateName`,
		name: `lang-${item}-exhibit-cateName`,
		component: resolve(__dirname, "../pages/_lang/exhibition/-detail.vue"),
	},
]).flat();

const stockRoutes = GSJJStockPageUrlPrefix.map((item) => [
	{
		path: `/:lang?/${item}/stock/:cateName`,
		name: `lang-${item}-stock-cateName`,
		component: resolve(__dirname, "../pages/_lang/exhibition/-stock-list.vue"),
	},
	{
		path: `/:lang?/${item}/stock/:cateName/:page.html`,
		name: `lang-${item}-stock-cateName-page`,
		component: resolve(__dirname, "../pages/_lang/exhibition/-stock-list.vue"),
	},
	{
		path: `/:lang?/${item}/shop/:cateName`,
		name: `lang-${item}-shop-cateName`,
		component: resolve(__dirname, "../pages/_lang/exhibition/-stock-detail.vue"),
	},
]).flat();

export default [
	...[
		{
			path: "/:lang?/products",
			name: "lang-products",
			component: resolve(__dirname, "../pages/_lang/exhibition/-stock-list.vue"),
		},
		{
			path: "/:lang?/products/:page.html",
			name: "lang-products-page",
			component: resolve(__dirname, "../pages/_lang/exhibition/-stock-list.vue"),
		},
	],
	...galleryRoutes,
	...stockRoutes,
];
