<template>
	<div class="swiper_detail" :class="{ showTemplate: showTemplate }">
		<!-- Swiper预览区域 -->
		<div class="pic_vIew">
			<div class="title-name">{{ titleName }}</div>
			<div class="swiper-container-wrap">
				<!-- <div class="swiper myswiper2" ref="swiper2" v-show="!editStatus"> -->
				<div class="swiper myswiper2" ref="swiper2">
					<div class="swiper-wrapper">
						<div class="swiper-slide" v-for="(item, index) in imgList" :key="index">
							<div class="smallImg" v-if="isImageType(item.url)">
								<client-only>
									<!-- <template v-if="isMobile"> -->
									<img :src="item.url" style="width: 100%; height: 100%" />
									<!-- <template v-if="1">
                                       
                                    </template>
                                    <template v-else>
                                        <PicZoom :url="item.url" :width="300" :scale="2" type="round"> </PicZoom>
                                    </template> -->
								</client-only>
							</div>
							<!-- <div v-else>
                                <video :src="item.url" controls :poster="item.url"></video>
                            </div> -->
							<VideoPlayer class="swiper-no-swiping" v-else :ref="'videoPlayer' + index" disabled-mouse :options="getVideoOptions(item.url, 1)"></VideoPlayer>
						</div>
					</div>
					<div class="swiper-button-prev" style="z-index: 999"></div>
					<!--左箭头。如果放置在swiper外面，需要自定义样式。-->
					<div class="swiper-button-next" style="z-index: 999"></div>
					<!--右箭头。如果放置在swiper外面，需要自定义样式。-->
				</div>
			</div>

			<!-- 奖章报价---跳转到上传步骤 -->
			<div v-show="showProfessional && device != 'mb'">
				<div class="design-tool" @click="toPosition('Upload Artwork & Comments')">
					<b class="icon-bianji1"></b>&nbsp;
					<div>{{ langsemiCustom.professional }}</div>
				</div>
			</div>
			<div class="showTemplateBtn" v-show="showTemplate" @click="openTemplate">
				SHOP ALL TEMPLATE
				<i class="el-icon-d-arrow-right"></i>
			</div>

			<!-- <div class="swiper myswiper1" :class="{ isEdit: editStatus }" ref="swiper1"> -->
			<div class="swiper myswiper1 isEdit" ref="swiper1">
				<!-- <div class="swiper-wrapper" :style="carouselList.length>3?'justify-content: center;':''"> -->
				<div class="swiper-wrapper">
					<div class="swiper-slide" v-for="(item, index) in imgList" :key="index">
						<template v-if="isImageType(item.url)">
							<img v-src :src="item.url" />
						</template>
						<template v-else-if="isVideoType(item.url)">
							<!-- <v-btn icon>
                                <v-icon x-large> mdi-play-circle</v-icon>
                            </v-btn>  
                                <b class="icon-a-icon-100EcoFriendlyzhuanhuan"></b>  -->
							<!-- <video v-show="!item.imgUrl" :src="item.url" preload="auto"></video>
                            <img v-show="item.imgUrl" v-src :src="item.imgUrl" :alt="item.alt" /> -->
							<video :src="item.url" preload="auto"></video>
						</template>
					</div>
				</div>
			</div>
		</div>
		<!-- 简化版报价详情 -->
		<div class="con" v-if="$slots.default === undefined">
			<!-- :class="viewMore ? 'max-height' : ''" -->
			<div class="scrollBar custom-scrollbar">
				<p class="title">{{ lang.orderSummary }}</p>
				<ul style="margin: 0">
					<template v-for="(item, index) in customGenerate">
						<li class="detail-item" :class="{ disabled: !showEdit(item) }" :key="index" v-if="selectedData.hasOwnProperty(item.paramName)" @click="toPosition(item.paramName)">
							<div class="left">
								<div class="f-left">{{ item.alias }}:</div>
								<div class="f-right">
									<div>
										<template v-for="(citem, cindex) in selectedData[item.paramName]">
											<template v-if="item.paramName == 'Ribbon'">
												{{ citem.alias ? citem.alias + "," : citem.cateName + "," }}
												{{ citem.colorValue && citem.colorValue.alias ? citem.colorValue.alias + "," : null }}
												{{ citem.sizeValue ? citem.sizeValue.alias : null }}
												<template v-if="citem.uploadList">
													<span v-for="(im, imI) in citem.uploadList" :key="imI">
														{{ im.original_filename }}
													</span>
												</template>
											</template>
											<template v-else-if="citem.paramType === 'COLOR' || citem.paramType === 'QUANTITY' || item.paramName === 'Plating'">
												<span>{{ citem.alias || citem.cateName || citem.paramName }}({{ citem.inputNum }})</span>
												<i style="font-style: normal" v-show="cindex != selectedData[item.paramName].length - 1"> , </i>
											</template>
											<template v-else>
												<span>{{ citem.alias || citem.cateNameQuote || citem.cateName || citem.paramName }}</span>
												<i style="font-style: normal" v-show="cindex != selectedData[item.paramName].length - 1"> , </i>
											</template>
										</template>
									</div>
								</div>
							</div>
							<span class="editIcon">{{ lang.Edit }}<b class="icon-edit--fill"></b></span>
						</li>
					</template>
				</ul>
			</div>

			<div class="viewMore pointer" style="display: block" v-show="showViewMore">
				<span class="noShow" @click="viewMore = !viewMore" style="position: relative; left: 43%; color: #d24600"> {{ viewMore ? lang.ViewLess : lang.ViewMore }}<i :class="viewMore ? 'less' : 'more'" class="el-icon-d-arrow-right"></i> </span>
			</div>
		</div>
		<slot></slot>
	</div>
</template>

<script>
import { isImageType, isVideoType } from "@/utils/utils";
import VideoPlayer from "@/components/HalfDesign/VideoPlayer.vue";
export default {
	name: "SwiperDetail",

	components: {
		VideoPlayer,
	},
	props: {
		imgList: {
			type: Array,
			default: [],
		},
		isLoading: {
			type: Boolean,
			default: false,
		},
		selectedData: {
			type: [Object, Array],
		},
		generalData: {
			type: Array,
			default: [],
		},
		titleName: {
			type: String,
		},
		showProfessional: {
			type: Boolean,
			default: false,
		},
		showViewMore: {
			type: Boolean,
			default: false,
		},
		attachment: {
			type: Boolean,
			default: false,
		},
		loop: {
			type: Boolean,
			default: false,
		},
		showTemplate: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {
			myswiper1: null,
			myswiper2: null,
			viewMore: false,
		};
	},
	computed: {
		langsemiCustom() {
			return this.$store.getters.lang.semiCustom || {};
		},
		device() {
			return this.$store.state.device;
		},
		lang() {
			return this.$store.getters.lang.quote || {};
		},
		customGenerate() {
			let temp = [];
			//过滤掉某些属性
			this.generalData.forEach((item) => {
				if (item.paramName !== "Upload Artwork & Comments" && item.paramName !== "Select Upload Artwork" && item.paramName !== "qty" && item.paramName !== "Quantity" && !item.noShowDetail) {
					temp.push(item);
				}
			});
			if (this.attachment) {
				let index = temp.findIndex((item) => {
					return item.paramName === "Select Packaging" || item.paramName === "Select Packing Options" || item.paramName === "Select Turnaround Time";
				});
				if (index >= 0) {
					let attachmentData = {
						parentId: 0,
						paramName: "customAttachment",
						alias: this.lang.Attachment,
						childList: [
							{
								parentId: 1,
								id: "-1",
								paramName: "customAttachment",
								alias: this.lang.StandardMilitaryClutch,
							},
						],
					};
					temp.splice(index, 0, attachmentData);
				}
			}
			return temp;
		},
	},
	methods: {
		isImageType,
		isVideoType,
		showEdit(item) {
			let paramNameList = ["Plating", "Select Buckle Finish", "Select Metal Finish"];
			if (this.isDs === 1 && (item.paramType === "quoteCategory" || paramNameList.includes(item.paramName))) {
				return false;
			}
			return item.alias !== this.lang.Attachment || !this.attachment;
		},
		toPosition(item) {
			if (item === "customAttachment") {
				return false;
			}
			this.$emit("toPosition", item);
		},
		getVideoOptions(path, type, poster) {
			if (type === 1) {
				return {
					autoplay: true,
					controls: true,
					muted: true,
					loop: true,
					fill: true,
					sources: [
						{
							src: path,
							type: "video/mp4",
						},
					],
				};
			} else if (type === 2) {
				return {
					autoplay: true,
					controls: true,
					muted: false,
					loop: true,
					fill: true,
					sources: [
						{
							src: path,
							type: "video/mp4",
						},
					],
				};
			} else if (type === 3) {
				return {
					autoplay: true,
					controls: false,
					muted: true,
					loop: true,
					fill: true,
					sources: [
						{
							src: path,
							type: "video/mp4",
						},
					],
				};
			} else if (type === 4) {
				return {
					autoplay: false,
					controls: false,
					muted: true,
					loop: true,
					fill: true,
					sources: [
						{
							src: path,
							type: "video/mp4",
						},
					],
					poster: poster,
				};
			}
		},
		openTemplate() {
			this.$emit("openTemplate");
		},
	},
	mounted() {
		this.$nextTick(() => {
			let _this = this;
			this.myswiper1 = new Swiper(this.$refs.swiper1, {
				slidesPerView: "auto",
				spaceBetween: 18,
				watchSlidesVisibility: true, //防止不可点击
				grabCursor: true,
				// on: {
				//     click: function () {
				//         // _this.editStatus = false;
				//     },
				// },
			});
			this.myswiper2 = new Swiper(this.$refs.swiper2, {
				slidesPerView: 1,
				spaceBetween: 10,
				grabCursor: true,
				observer: true,
				observeParents: true,
				loop: this.loop,
				navigation: {
					nextEl: ".myswiper2 .swiper-button-next",
					prevEl: ".myswiper2 .swiper-button-prev",
				},
				thumbs: {
					swiper: this.myswiper1,
				},
				on: {
					slideChangeTransitionEnd: function (value) {
						_this.imgList.forEach(function (v, i) {
							if (i === value.activeIndex) {
								if (_this.$refs["videoPlayer" + i]) {
									_this.$refs["videoPlayer" + i][0].player.play();
								}
							} else {
								if (_this.$refs["videoPlayer" + i]) {
									_this.$refs["videoPlayer" + i][0].player.pause();
								}
							}
						});
					},
				},
			});
		});
	},
};
</script>

<style scoped lang="scss">
.design-tool {
	display: flex;
	justify-content: center;
	align-items: center;
	margin-top: 7.5px;
	cursor: pointer;
}
// 通用样式
.swiper_detail {
	position: sticky;
	top: 0;
	bottom: auto;
	font-size: 14px;

	.con {
		margin-top: 30px;
		padding: 10px 22px 20px;
		border-radius: 10px;
		background-color: #f6f6f6;

		.scrollBar {
			// overflow: hidden;
			// max-height: 200px;

			.title {
				padding: 0 8px;
				font-size: 18px;
				font-weight: bold;
				color: #202428;
				line-height: 30px;
			}

			ul {
				li {
					display: flex;
					justify-content: space-between;
					align-items: flex-start;
					padding: 5px 8px;
					cursor: pointer;
					transition: all 0.3s;

					&.disabled {
						pointer-events: none;
						padding-right: 50px;

						.editIcon {
							display: none;
						}
					}

					&:hover {
						background-color: #f3f3f3;
					}

					.left {
						display: flex;
						flex: 1;
						justify-content: space-between;

						.f-left {
							color: #222222;
							// width: 170px;
							word-break: break-word;
						}

						.f-right {
							flex: 1;
							margin-left: 10px;
							color: #666666;
							word-break: break-word;
							text-align: right;
						}
					}
				}

				.editIcon {
					flex-shrink: 0;
					margin-left: 10px;
					color: #b3b3b3;
					font-size: 12px;
					padding-top: 2px;
				}
			}
		}

		.max-height {
			overflow: hidden auto;
			max-height: 350px !important;

			@media screen and (max-width: 767px) {
				max-height: 100% !important;
			}
		}
	}

	.more {
		transform: rotate(90deg);
	}

	.less {
		transform: rotate(270deg);
	}
}

.swiper_detail.showTemplate {
	.pic_vIew {
		.swiper-container-wrap {
			background-color: #fafafa;
			border-radius: 4px;
		}
		.design-tool {
			display: none;
		}
		.showTemplateBtn {
			padding: 10px 0 30px;
			width: 100%;
			text-align: center;
			background-color: #fafafa;
			color: $color-primary;
			font-weight: 600;
			font-size: 16px;
			cursor: pointer;
		}
		.myswiper1 {
			img {
				object-fit: contain;
			}
			.swiper-wrapper {
				margin-top: 20px;
			}
		}

		.myswiper2 {
			height: 460px;
			.swiper-button-prev,
			.swiper-button-next {
				width: 40px;
				height: 40px;
				border-radius: 4px;
				background: transparent;
				opacity: 1;
				will-change: opacity;
				transform: translateY(-50%);
				&::before {
					content: "";
					position: absolute;
					inset: 0;
					background: $color-primary;
					opacity: 0.1;
					z-index: -1;
				}
				&:after {
					color: $color-primary;
					font-size: 14px;
					font-weight: 700;
				}
				&:active {
					&:before {
						opacity: 1;
					}
					&:after {
						color: #fff;
					}
				}
			}
		}

		@include respond-to(mb) {
			.showTemplateBtn {
				padding: 10px 0;
			}
			.myswiper1 {
				display: block;
			}

			.myswiper2 {
				height: 64vw;
				width: 98.5%;
				.swiper-slide {
					.smallImg {
						height: 100%;
					}
				}
			}

			.title-name {
				display: none;
			}
		}
	}
	.con {
		display: none;
	}
}

// Swiper 样式
.myswiper1 {
	.swiper-wrapper {
		margin-top: 30px;
	}

	.swiper-slide {
		display: flex;
		justify-content: center;
		align-items: center;
		overflow: hidden;
		width: 88px;
		height: 88px;
		border: 2px solid #eeeeee;
		border-radius: 10px;
		cursor: pointer;

		&.swiper-slide-thumb-active {
			border: 2px solid $color-primary !important;
		}
	}

	&.isEdit {
		.swiper-slide.swiper-slide-thumb-active {
			border: 2px solid #eeeeee;
		}
	}

	img {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}

	.playBtn {
		display: flex;
		justify-content: center;
		align-items: center;
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		width: 30px;
		height: 30px;
		background-color: rgba(0, 0, 0, 0.7);
		border-radius: 50%;

		svg {
			fill: #ffffff;
		}
	}
}

.swiper.myswiper2 {
	--swiper-navigation-color: #ffffff;
	/* 单独设置按钮颜色 */
	--swiper-navigation-size: 20px;

	/* 设置按钮大小 */
	.swiper-button-next,
	.swiper-button-prev {
		width: 46px;
		height: 46px;
		background: #000000;
		opacity: 0.4;
		border-radius: 50%;
		transform: translateY(-50%);
		margin-top: 0;
	}
}
.title-name {
	display: none;
	font-size: 18px;
	text-align: center;
	font-weight: bold;
}

.myswiper2 {
	height: 405px;
	.zoom {
		position: absolute;
		top: 0;
		right: 0;
		cursor: pointer;
		z-index: 100;

		b {
			font-size: 22px;
			margin-right: 4px;
		}

		&:hover {
			b {
				color: $color-primary;
			}
		}
	}

	.swiper-slide {
		display: flex;
		justify-content: center;
		align-items: center;

		.smallImg {
			position: relative;
			width: 100%;
			height: 400px;
			text-align: center;
		}
	}

	img {
		max-width: 100%;
		max-height: 100%;
		object-fit: contain;
	}
}
@media screen and (max-width: 767px) {
	.title-name {
		display: block;
		margin-bottom: 12px;
	}
	.myswiper2 {
		height: 64vw;
		width: 98.5%;
	}
	.myswiper1 {
		display: none;
	}
	.con {
		display: none;
	}
	.swiper_detail {
		background-color: #fff;
		padding: 18px 7px;
		border-radius: 5px;
	}
}
</style>
