import {getRetailerUserInfo, getRetailerUserPermission, getUsefulWebSite} from "@/api/manage/webManage";

export default ({app, store, route, redirect, error, query}) => {
	const isManage = process.env.isManage;
	//如果是经销商后台管理系统
	if (isManage) {
		//权限白名单
		const whiteList = ['manage-site', 'manage', 'manage-invoice', 'manage-createSite'];
		app.router.beforeEach(async (to, from, next) => {
			try {
				let tokenKey = process.env.tokenKey;
				if(query.token){
					tokenKey = query.token
				}

				if (!app.$cookies.get(tokenKey)) {
					if (whiteList.includes(to.name)) {
						next();
					} else {
						next('/manage/site')
					}
					// 总后台跳转直接设置登录选定项目
					if(route.path === '/manage/siteEdit/contentEdit' && query.token){
						tokenKey = query.token
						app.$cookies.set('manageToken', tokenKey, {
							maxAge: 60 * 60 * 24 * 7,
							domain: '.o2o.co',
							// process.env.domain
							path: '/'
						})
						app.$cookies.set('otoManageProId', query.proId, {
							maxAge: 60 * 60 * 24 * 7,
							path: '/'
						})
						redirect('/manage/siteEdit/contentEdit')
					}
				}
				else{
					let userInfo = store.state.manage.userInfo, webSiteList = store.state.manage.webSiteList
					if (!userInfo || !webSiteList.length) {
						//获取,更新用户信息
						userInfo = await getRetailerUserInfo({token: app.$cookies.get(tokenKey)});
						store.commit('manage/updateUserInfo', userInfo.data);
						//获取,更新网站列表
						const website = await getUsefulWebSite();
						webSiteList = website.data;
						store.commit('manage/updateSiteList', webSiteList);
					}
					if (!webSiteList.length) {
						if (to.name === 'manage-site' || to.name.indexOf('manage-account') > -1 || to.name === 'manage-createSite') {
							next()
						} else {
							next('/manage/site')
						}
					}
					else {
						/**
						 * 获取proId
						 * 如果存在默认,就从站点列表寻找对应项,如果没找到,移除之前的,并将站点设置为列表的第一个
						 * 如果不存在,将站点设置为列表的第一个
						 **/
						const defaultProId = app.$cookies.get('otoManageProId');
						if (!defaultProId) {
							store.commit('manage/updateSite', webSiteList[0]);
							app.$cookies.set('otoManageProId', webSiteList[0].id, {
								maxAge: 60 * 60 * 24 * 7,
								path: '/'
							})
						} else {
							let findItem = webSiteList.find(item => {
								return item.id === defaultProId
							})
							if (findItem) {
								store.commit('manage/updateSite', findItem)
							} else {
								app.$cookies.remove('otoManageProId', {
									path: '/'
								})
								store.commit('manage/updateSite', webSiteList[0]);
								app.$cookies.set('otoManageProId', webSiteList[0].id, {
									maxAge: 60 * 60 * 24 * 7,
									path: '/'
								})
							}
						}
						//获取权限列表
						let res = await getRetailerUserPermission({proId: store.getters["manage/getProId"]});
						// res.data 有值的时候才执行下面的代码
						store.commit('manage/setPermissionData', res.data);
						//如果是超级管理员，则开启网站完整左侧导航
						if (store.state.manage.currentSite.retailerId === store.state.manage.userInfo.id) {
							next();
						} else {
							//判断有无权限
							let havePermission = false, lItem;
							let fn = (list) => {
								for (let i = 0; i < list.length; i++) {
									if (list[i].permissionName === to.name && list[i].type === 0) {
										havePermission = true;
										lItem = list[i];
										break
									}
									if (list[i].childList && list[i].childList.length) {
										fn(list[i].childList, havePermission)
									}
								}
							}
							fn(store.getters["manage/getPermissionData"]);
							if (!havePermission) {
								console.log('无权限');
								if (whiteList.includes(to.name)) {
									next();
								} else {
									//无权限,提示，去到有权限的第一个路由
									app.$message.error('No Permission!');
									if (from.name) {
										next(from)
									} else {
										let firstRouter = '/' + store.getters["manage/getPermissionData"][0].permissionName.replace('-', '/');
										next(firstRouter);
									}
								}
							} else {
								next()
							}
						}
					}
					if(route.path === '/manage/siteEdit/contentEdit' && query.token){
						redirect('/manage/siteEdit/contentEdit');
					}
				}
			} catch (e) {
			}
		})
	} else {
	}
};
