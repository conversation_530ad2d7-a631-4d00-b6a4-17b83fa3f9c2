<template>
	
	<div class="detailList" :style="{ top: cusTop + 'px' }" id="detailInfo">
		<div class="observeDom"></div>
		<div class="topImg" id="topImg">
			<div class="imgWrap">
				<img :src="absoluteImg" />
			</div>
		</div>
		<div class="con">
			<div class="scrollBar custom-scrollbar" :class="viewMore ? 'max-height' : ''">
				<p class="title">{{ lang.orderSummary }}</p>
				<ul style="margin: 0">
					<template v-for="(item, index) in customGenerate">
						<li class="detail-item" :class="{ disabled: !showEdit(item) }" :key="index" v-if="selectedData.hasOwnProperty(item.paramName)" @click="toPosition(item.paramName)">
							<div class="left">
								<div class="f-left">{{ item.alias }}:</div>
								<div class="f-right">
									<div>
										<template v-for="(citem, cindex) in selectedData[item.paramName]">
											<template v-if="item.paramName == 'Ribbon'">
												{{ citem.alias ? citem.alias + "," : citem.cateName + "," }}
												{{ citem.colorValue && citem.colorValue.alias ? citem.colorValue.alias + "," : null }}
												{{ citem.sizeValue ? citem.sizeValue.alias : null }}
												<template v-if="citem.uploadList">
													<span v-for="(im, imI) in citem.uploadList" :key="imI">
														{{ im.original_filename }}
													</span>
												</template>
												<!-- semiMedals Ribbon上传改为存为files -->
												<template v-if="citem.files">
													<span v-for="(im, imI) in citem.files" :key="imI">
														{{ getImageNameFromURL(im) }}
													</span>
												</template>
											</template>
											<template v-else-if="citem.paramType === 'COLOR' || citem.paramType === 'QUANTITY'">
												<span>{{ citem.alias || citem.cateName }}({{ citem.inputNum }}<span v-if="citem.giftQuantity" style="color:red"> + {{ citem.giftQuantity }} Free</span>)</span>
												<i style="font-style: normal" v-show="cindex != selectedData[item.paramName].length - 1"> , </i>
											</template>
											<template v-else-if="citem.sizeAlias">
												<span>{{ citem.sizeAlias }}</span>
											</template>
											<template v-else>
												<span>
                                                    {{ citem.alias || citem.cateNameQuote || citem.cateName }}
                                                    <template v-if="citem.extendStepValue">
                                                        ({{citem.extendStepValue.alias}})
                                                    </template>
                                                </span>
												<!-- blank patches 自定义边框颜色 -->
												<span v-if="item.paramName === 'Patch Border' && customColor && citem.paramName === 'Merrow'">({{ citem.colorValue?.paramName ? citem.colorValue?.paramName : customColor.pantone }})</span>
												<!-- adhesive材质 -->
												<span v-if="(item.paramName === 'Patch Backing' || item.paramName === 'Backing') && (citem.paramName==='Adhesive' || citem.paramName==='Adhesive Backing') && selectedTextures.length">
													(<span v-for="(titem,tindex) in selectedTextures" :key="tindex">{{ titem.name }}<span v-if="tindex < selectedTextures.length - 1">,</span></span>)
												</span>
												<i style="font-style: normal" v-show="cindex != selectedData[item.paramName].length - 1"> , </i>
											</template>
										</template>
									</div>
								</div>
							</div>
							<span class="editIcon">{{ lang.Edit }}<b class="icon-edit--fill"></b></span>
						</li>
					</template>
				</ul>
			</div>
			<div v-if="!isHideMove" class="viewMore pointer" style="display: block">
				<span class="noShow" @click="viewMore = !viewMore" style="position: relative; left: 43%; color: #0066cc;font-weight: bold;"> {{ viewMore ? lang.ViewLess : lang.ViewMore }}<i :class="viewMore ? 'less' : 'more'" class="el-icon-d-arrow-right"></i> </span>
			</div>
		</div>
		<div class="hr"></div>
		<div class="priceDetail">
			<ul style="margin: 0">
				<li class="price-item" v-if="selectedData['qty'] || selectedData['Quantity']" @click="toPosition(qtyName)">
					<div class="left">
						<div class="f-left">{{ lang.Quantity }}:</div>
						<div class="f-right">
							<span>{{ customQty }}</span>
							<span v-if="qtyChecked && customQty >= 300" style="color:red">(+30 {{ lang.free }})</span>
						</div>
					</div>
					<span class="editIcon">{{ lang.Edit }}<b class="icon-edit--fill"></b></span>
				</li>
				<li class="price-item" v-show="!onlyAddInquiry">
					<div class="left">
						<div class="f-left">{{ lang.unitPrice }}:</div>
					</div>
					<div class="f-right">
						<CCYRate :price="newPriceInfo.foundationUnitPrice"></CCYRate>
					</div>
				</li>
				<li class="price-item" v-show="!onlyAddInquiry && newPriceInfo.toolingCharge && newPriceInfo.toolingCharge > 0">
					<div class="left">
						<div class="f-left">{{ lang.moldPrice }}:</div>
					</div>
					<div class="f-right">
						<CCYRate :price="newPriceInfo.toolingCharge"></CCYRate>
					</div>
				</li>
				<li class="price-item" v-show="!onlyAddInquiry && newPriceInfo.setupCharge && newPriceInfo.setupCharge > 0">
					<div class="left">
						<div class="f-left">{{ lang.setUpFee }}:</div>
					</div>
					<div class="f-right">
						<CCYRate :price="newPriceInfo.setupCharge"></CCYRate>
					</div>
				</li>
				<li class="price-item" v-show="!onlyAddInquiry">
					<div class="left">
						<div class="f-left">{{ lang.subtotal }}:</div>
					</div>
					<div class="f-right">
						<CCYRate :price="subtotal"></CCYRate>
					</div>
				</li>
				<li class="price-item" v-show="!onlyAddInquiry && discountPrice != 0">
					<div class="left">
						<div class="f-left">{{ text1 }}</div>
					</div>
					<div class="f-right">
						{{ text2 }}
						<CCYRate :price="discountPrice"></CCYRate>
					</div>
				</li>
			</ul>
		</div>
		<div class="hr"></div>
		<div class="footBtn">
			<!-- 美国和澳大利亚不展示提前算税 -->
			<div :style="continentName == 'Europe' && textPrice > 0 ? 'display: flex;justify-content: space-between;width: 100%;padding: 0 10px;' : 'display: flex;justify-content: center;'" v-show="!onlyAddInquiry">
				<div class="currencyText">
					{{ lang.Currency }}:
					<el-select v-model="currencyId" :placeholder="lang.PleaseSelect" @change="changeCurrency" size="small" style="width: 100px">
						<el-option v-for="item in currencyList" :key="item.id" :label="item.code" :value="item.id" />
					</el-select>
				</div>
				<div v-if="continentName == 'Europe' && textPrice > 0" :style="continentName == 'Europe' && textPrice > 0 ? 'display: flex;align-items: center;justify-content: center;' : 'display:none'">
					<strong class="subTotalText">{{ getTax(1) }}:</strong>
					<el-switch v-model="IncludingVAT" :active-value="1" :inactive-value="0" active-text="Yes" inactive-text="No" class="operation"></el-switch>
				</div>
			</div>

			<div class="sub" v-show="!onlyAddInquiry">
				<div class="total-price">
					<span v-if="continentName != 'Europe'">{{ lang.total }}:</span>
					<span v-else>{{ IncludingVAT == 1 ? getTax(2) : getTax(3) }}:</span>
					<CCYRate :price="getTotalPrice(newPriceInfo.totalPrice)" style="color: #de3500"></CCYRate>
				</div>
			</div>
			<div class="inquiryTip" v-show="isPriceRemind && !sizeInquiry">
				{{ lang.inquiryTip2 }}
			</div>
			<div class="inquiryTip" v-show="sizeInquiry">
				{{ lang.inquiryTip3 }}
			</div>
			<freeTip v-show="showTextTip"></freeTip>
			
			<div class="btnGroup" id="btnGroup">
				<QuoteBtn bgColor="linear-gradient(to top, #FF412B 0%, #FF7743 100%)" @click.native="addInquiry" :disabled="isProcessing"
					>{{ lang.submitInquiry }}
					<span @click.stop>
						<el-tooltip popper-class="cusToolTip" effect="light" :content="textInfo.inquiryTip" placement="top-start">
							<b class="icon-wenhao3 tip-icon"></b>
						</el-tooltip>
					</span>
				</QuoteBtn>
				<QuoteBtn bgColor="linear-gradient(to top, #0066CC 0%, #2FB6F5 100%)" v-if="onlyAddInquiry === 0 || !onlyAddInquiry" @click.native="addCart" :disabled="isProcessing"
					>{{ $route.query.isBack ? lang.saveCart : lang.addToCart }}
					<span @click.stop>
						<el-tooltip popper-class="cusToolTip" effect="light" :content="textInfo.addCartTip" placement="top-start">
							<b class="icon-wenhao3 tip-icon"></b>
						</el-tooltip>
					</span>
				</QuoteBtn>
			</div>
		</div>
	</div>
</template>

<script>
import { getTaxByPrice } from "@/api/web";

import QuoteBtn from "@/components/Quote/QuoteBtn";
import freeTip from "~/components/Quote/freeTip";
import { deepClone, round2 } from "@/utils/utils";

export default {
	props: {
		isProcessing:{
			type: Boolean,
		},
		//棒球活动赠品数量
		qtyChecked:{
			type: Boolean,
			default: false,
		},
		customColor:{
			type: [Object, Array],
		},
		selectedData: {
			type: [Object, Array],
		},
		priceInfo: {
			type: [Object, String],
			default: {},
		},
		customQty: {
			type: [Number, String],
		},
		textInfo: {
			type: Object,
		},
		generalData: {
			type: Array,
			default: ()=>[],
		},
		absoluteImg: {
			type: String,
		},
		attachment: {
			type: Boolean,
			default: false,
		},
		showTextTip: {
			type: Boolean,
			default: true,
		},
		isHideMove: {
			type: Boolean,
			default: false,
		},
		selectedTextures:{
			type: [Object, Array],
			
		}
		// showOnly:{
		// 	type: Boolean,
		// 	default:false
		// }
	},
	inject: ["getIsDs"],
	components: {
		QuoteBtn,
		freeTip,
	},
	data() {
		return {
			continentName: "",
			countryName: "",
			IncludingVAT: 1,
			textPrice: null,
			viewMore: false,
			cusTop: 0,
			currencyId: "",
			showDetail: false,
		};
	},
	computed: {
        hasDiscount(){
          let selectData = this.selectedData,generateData = this.generalData;
          let findDiscountKey = (generateData.find(item=>item.paramType === "DISCOUNT"))?.paramName;
          return !!selectData[findDiscountKey]?.length;
        },
        newPriceInfo(){
            //如果没选折扣，不显示价格
            return this.hasDiscount?this.priceInfo: ""
        },
		isDs() {
			return this.getIsDs();
		},
		qtyName() {
			if (this.selectedData["qty"]) {
				return "qty";
			} else {
				return "Quantity";
			}
		},
		lang() {
			return this.$store.getters.lang.quote || {};
		},
		customGenerate() {
			let temp = [],
				generalData = deepClone(this.generalData);
			//过滤掉某些属性
			generalData.forEach((item) => {
				if (item.paramName !== "Upload Artwork & Comments" && item.paramName !== "qty" && item.paramName !== "Quantity" && !item.noShowDetail) {
					temp.push(item);
				}
			});
			if (this.attachment) {
				let index = temp.findIndex((item) => {
					return item.paramName === "Select Packaging" || item.paramName === "Select Packing Options" || item.paramName === "Select Turnaround Time";
				});
				if (index >= 0) {
					let attachmentData = {
						parentId: 0,
						paramName: "customAttachment",
						alias: this.lang.Attachment,
						childList: [
							{
								parentId: 1,
								id: "-1",
								paramName: "customAttachment",
								alias: this.lang.StandardMilitaryClutch,
							},
						],
					};
					temp.splice(index, 0, attachmentData);
				}
			}
			return temp;
		},
		currencyList() {
			return this.$store.state.currencyList;
		},
		currencySymbol() {
			return this.$store.getters.currencySymbol;
		},
		currencyRate() {
			return this.$store.getters.currencyRate;
		},
		previewMode() {
			return process.env.VUE_APP_MODE === "Preview";
		},
		proType() {
			return this.$store.state.proType;
		},
		text1() {
			let discountName = "";
			//加急费，重量加价
			if (this.newPriceInfo.discountPrice) {
				return this.lang.rushDelivery;
			}
			if (this.newPriceInfo.discount > 1) {
				return this.lang.rushDelivery;
			} else if (this.newPriceInfo.discount < 1) {
				discountName = this.lang.discount;
				return `${discountName} (${this.lang.Turnaround}: ${(Math.abs(1 - this.newPriceInfo.discount) * 100).toFixed(0)}% ${this.lang.p8}):`;
			}
		},
		text2() {
			let ac;
			if (this.newPriceInfo.totalPrice > this.subtotal) {
				ac = "+";
			} else {
				ac = "-";
			}
			return ac;
		},
		subtotal() {
			return round2(round2(this.newPriceInfo.foundationUnitPrice * this.newPriceInfo.totalQuantity) + this.newPriceInfo.toolingCharge + (this.newPriceInfo.setupCharge || 0));
		},
		discountPrice() {
			if (this.newPriceInfo) {
                return `${Math.abs(this.newPriceInfo.totalPrice - this.subtotal)}`;
			} else {
				return 0;
			}
		},
		sizeInquiry() {
			for (const stepData in this.selectedData) {
				if (stepData.includes("Size") || stepData.includes("size")) {
					if (this.selectedData[stepData][0] && (this.selectedData[stepData][0].onlyAddInquiry || this.selectedData[stepData][0].isPriceRemind)) {
						return true;
					}
				}
			}
			return false;
		},
		onlyAddInquiry() {
			let onlyAddInquiry = false;
			for (const stepData in this.selectedData) {
				let data = this.selectedData[stepData];
				if (data[0]) {
					if (data[0].onlyAddInquiry && data[0].onlyAddInquiry != 0) {
						onlyAddInquiry = true;
						this.viewMore = true;
					}
				}
			}

			return onlyAddInquiry;
		},
		isPriceRemind() {
			let isPriceRemind = false;
			for (const stepData in this.selectedData) {
				let data = this.selectedData[stepData];
				if (data[0]) {
					if (data[0].isPriceRemind && data[0].isPriceRemind != 0) {
						isPriceRemind = true;
					}
				}
			}
			return isPriceRemind;
		},
	},
	watch: {
		"$store.state.currency": {
			handler(newValue) {
				this.currencyId = newValue.id;
			},
			immediate: true,
		},
	},
	methods: {

		//欧洲国家文案
		getTax(type) {
			// if(this.countryName == 'Australia'){
			// 	if(type == 1){
			// 		return 'Including GST'
			// 	}else if(type == 2){
			// 		return 'Subtotal incl. GST'
			// 	}else if(type == 3){
			// 		return 'Subtotal excl. GST'
			// 	}
			// }else{
			if (type == 1) {
				return "Including VAT";
			} else if (type == 2) {
				return "Subtotal incl. VAT";
			} else if (type == 3) {
				return "Subtotal excl. VAT";
			}
			// }
		},

		//税费开关开启，小计加上税费
		getTotalPrice(totalPrice) {
			if (this.IncludingVAT == 0 || this.continentName != "Europe") {
				//将总价传出去
				this.$store.commit("set_total", totalPrice);
				return totalPrice;
			} else {
				let t = (Math.round(this.textPrice * (this.newPriceInfo.foundationUnitPrice * this.newPriceInfo.totalQuantity + this.newPriceInfo.toolingCharge) * 100) / 100).toFixed(2);
				this.$store.commit("set_total", totalPrice + Number(t));
				return totalPrice + Number(t);
			}
		},

		showEdit(item) {
			let paramNameList = ["Plating", "Select Buckle Finish", "Select Metal Finish"];
			if (this.isDs === 1 && (item.paramType === "quoteCategory" || paramNameList.includes(item.paramName))) {
				return false;
			}
			return item.alias !== this.lang.Attachment || !this.attachment;
		},
		getTop() {
			let el = document.getElementById("detailInfo");
			if (!el) {
				return false;
			}
			const { top, height } = el.getBoundingClientRect();
			this.cusTop = (window.innerHeight - height) / 2;
			// this.cusTop = (window.innerHeight - height) / 2 + 113 / 2 - 20;
		},
		toPosition(item) {
			if (item === "customAttachment") {
				return false;
			}
			this.$emit("toPosition", item);
		},
		addInquiry() {
			this.$emit("addInquiry");
		},
		addCart() {
			this.$emit("addCart");
		},
		changeCurrency(val) {
			let findC = this.currencyList.find((item) => {
				return item.id === val;
			});
			if (findC) {
				this.$store.commit("setCurrency", findC);
			}
		},
		getImageNameFromURL(imageURL) {
			// 获取最后一个斜杠的索引
			const lastIndex = imageURL.lastIndexOf("/");
			// 提取文件名部分
			const imageName = imageURL.substring(lastIndex + 1);
			// 返回图片名称
			return imageName;
		},
	},
	created() {
		//提前算税费
		getTaxByPrice().then((res) => {
			this.textPrice = res.data.rate;
			this.countryName = res.data?.res?.country?.names.en;
			this.continentName = res.data?.res?.continent?.names.en; //洲
		});
	},
	mounted() {
		window.addEventListener("scroll", () => {
			this.getTop();
		});
		setTimeout(() => {
			this.getTop();
		}, 1000);
	},
};
</script>

<style scoped lang="scss">
.noShow{
	@include respond-to(mb) {
		font-size: 12px;
	}
}
.notFixed {
	@include respond-to(mb) {
		position: static;
		box-shadow: none;
		z-index: auto;
		border-radius: 3px;
	}
}
.footBtn .fixed-btn{
    position: fixed;
    bottom: 0;
    background: #fff;
    z-index: 10;
    padding: 10px 0;
    margin-bottom: 0 !important;
    box-shadow: 1px 1px 10px #6666;
}

.subTotalText {
	margin-right: 10px;
}

::v-deep .v-input--selection-controls {
	margin-top: 0 !important;
	padding-top: 0 !important;
}

.hr {
	background-color: #dcdfe6;
	position: relative;
	display: block;
	height: 1px;
	width: 100%;
	margin: 10px 0;
}

.detailList ::v-deep .freeTip {
	justify-content: flex-start;

	& > div {
		display: flex;
		justify-content: center;
		margin: 0 5px;
		flex: 1;
	}
}

.detailList.type1 {
	box-shadow: -8px 0px 10px -10px rgba(0, 0, 0, 0.25);
	font-size: 14px;
}

.detailList.type1,
.detailList.type2,
.detailList.type3 {
	position: sticky;
	background-color: #ffffff;
	padding: 20px;
	top: 0;
	border-radius: 10px;
	bottom: auto;
	font-size: 14px;

	.topImg {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		margin-bottom: 10px;

		.imgWrap {
			display: flex;
			justify-content: center;
			align-items: center;
			height: 200px;
			border-radius: 10px;

			img {
				width: 100%;
				height: 100%;
				object-fit: cover;
				border-radius: 10px;
			}

			::v-deep .video-js {
				width: 100%;
				height: 100%;
				border-radius: 10px;
			}
		}

		span {
			margin-top: 10px;
		}
	}

	.scrollBar {
		overflow: hidden;
		max-height: 200px;
	}

	.max-height {
		overflow: hidden auto;
		max-height: 100% !important;

		@media screen and (max-width: 767px) {
			height: 100% !important;
		}
	}

	.more {
		transform: rotate(90deg);
	}

	.less {
		transform: rotate(270deg);
	}

	.con,
	.priceDetail {
		.title {
			padding: 0 8px;
			font-size: 16px;
			font-weight: bold;
			color: #202428;
			line-height: 36px;
		}

		ul {
			li {
				display: flex;
				justify-content: space-between;
				// align-items: flex-start;
				align-items: center;
				padding: 5px 8px;
				cursor: pointer;
				transition: all 0.3s;

				&.disabled {
					pointer-events: none;
					padding-right: 50px;

					.editIcon {
						display: none;
					}
				}

				&:hover {
					background-color: #f3f3f3;
				}

				.left {
					display: flex;
					flex: 1;
					justify-content: space-between;

					.f-left {
						color: #222222;
						// width: 170px;
						word-break: break-word;
					}

					.f-right {
						flex: 1;
						margin-left: 10px;
						color: #666666;
						word-break: break-word;
						text-align: right;
					}
				}
			}

			.editIcon {
				flex-shrink: 0;
				margin-left: 10px;
				color: #666;
				font-size: 12px;
				// padding-top: 2px;
			}
		}
	}
}

.footBtn {
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;

	.sub {
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		margin: 5px 5px 12px;

		span {
			font-size: 16px;
			color: #222222;
		}

		.currencyText {
			font-size: 16px;
			color: #222222;
			font-weight: 400;
			@media screen and (max-width: 767px) {
				font-size: 12px;
			}
		}

		div.total-price {
			font-size: 30px;
			color: #de3500;
			font-weight: 700;

			span {
				font-weight: 400;
				padding-top: 6px;
			}
		}
	}

	.inquiryTip {
		font-size: 14px;
		padding: 2px 6px;
		margin-bottom: 10px;
	}

	.btnGroup {
		width: 100%;
		display: flex;
		flex-wrap: wrap;
		justify-content: center;
		align-items: center;

		.tip-icon {
			width: 18px;
			height: 18px;
			margin-left: 10px;
			color: #fff;
		}

		button {
			flex: 1;
			margin: 0 5px;
			border-radius: 4px;
			background: transparent;
			border: none;
		}
	}

	.tip {
		width: 100%;
		text-align: center;
		padding: 10px 0;
		cursor: pointer;
		font-size: 16px;
		color: #666666;
		text-decoration: none;

		.tip-icon {
			width: 22px;
			height: 22px;
			margin-right: 10px;
			vertical-align: middle;
		}

		span {
			max-width: 70%;
		}

		.text-mb {
			display: none;
		}
	}
}

.detailList.type2,
.detailList.type3 {
	position: relative;
	background-color: transparent;
	top: auto !important;
	width: 520px;
	margin: 0 auto;
	padding: 10px;

	.con {
		.scrollBar {
			@media screen and (max-width: 767px) {
				// max-height: 100% !important;
				overflow: hidden;
				height: 130px;
			}
		}

		ul {
			max-height: none;
		}

		li:hover {
			background-color: #fff !important;
		}
	}
}

.detailList.type3 {
	width: 100%;
}

@media screen and (max-width: 767px) {
	.detailList.type2,
	.detailList.type3 {
		padding: 10px;

		.hr {
			border-top: 1px solid #cccccc;
		}

		.topImg {
			.imgWrap {
				border-radius: 5px;
				height: 100%;

				img {
					border-radius: 5px;
				}

				::v-deep .video-js {
					border-radius: 5px;
				}
			}
		}

		.con,
		.priceDetail {
			margin-right: 0;

			.title {
				font-size: 14px;
			}

			ul {
				padding-right: 0;

				li {
					padding: 8px;

					&:hover {
						background-color: #ebebeb !important;
					}

					.left {
						.f-left {
							font-size: 12px;
						}

						.f-right {
							flex: 1;
							margin-left: 5px;
							font-size: 12px;
							text-align: right;
						}
					}
				}
			}
		}

		.footBtn {
			.sub {
				// align-self: flex-end;
				margin-bottom: 5px;

				span {
					font-size: 12px;
					color: #222222;
				}

				div.total-price {
					// margin: 0 10px;
					font-size: 18px;
				}
			}

			.inquiryTip {
				font-size: 12px;
			}

			.btnGroup {
				display: flex;
				flex-wrap: wrap;
				justify-content: center;
				align-items: center;
				margin-bottom: 10px;

				.tip-icon {
					width: 13px;
					height: 13px;
					margin-left: 5px;
					font-size: 16px;
				}

				button {
					width: 166px;
					height: 35px;
					margin: 5px;
					font-size: 12px;
					border-radius: 5px;
				}
			}

			.tip {
				width: 340px;
				font-size: 12px;
				padding: 5px 0;
				background-color: #ebebeb;

				.tip-icon {
					width: 14px;
					height: 14px;
					margin-right: 5px;
				}

				.text-pc {
					display: none;
				}

				.text-mb {
					display: inline-block;
				}
			}
		}
	}

	.detailList.type2 {
		width: 100%;
	}
}

button[disabled] {
	color: #fff !important;
	background: grey !important;
}

::v-deep .el-switch__label * {
	line-height: 1;
	font-size: 13px;
	display: inline-block;
}

::v-deep .el-switch__label {
	position: absolute;
	display: none;
	color: #fff !important;
	//   font-size: 13px !important;
}

::v-deep .el-switch__label--right {
	z-index: 1;
	right: 20px !important;
	margin-left: 0px;
}

::v-deep .el-switch__label--left {
	z-index: 1;
	left: 20px !important;
	margin-right: 0px;
}

::v-deep .el-switch__label.is-active {
	display: block;
}

::v-deep .el-switch .el-switch__core,
.el-switch .el-switch__label {
	width: 50px !important;
}
</style>
