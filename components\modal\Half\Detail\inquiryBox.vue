<template>
	<div class="inquiryBox">
		<BaseDialog class="nameDialog" :class="{ isFd: isFd }" :model="false" v-model="inquiryBoxVisible" :minWidth="''"
			:width="!isMobile ? '500px' : '90%'">
			<div class="close-icon" slot="closeIcon" @click="closeFn">
				<v-icon>mdi-close-thick</v-icon>
			</div>
			<v-card>
				<v-card-title class="inquiryDialogHead">
					<div class="dialogHead">
						<span class="text-h5" style="margin: auto">{{ langSemiCustom.concactInfo }}</span>
						<!-- <span class="closeBtn" @click="closeFn"><v-icon>mdi-close-thick</v-icon></span> -->
					</div>
				</v-card-title>
				<v-card-text>
					<myForm ref="myParentRef">
						<div class="flexBox">
							<myInput class="flexItem" v-model="dynamicValidateForm.firstName" prop="firstName"
								:rules="dynamicValidateFormRules.firstNameRules" :textLabel="langQuote.firstName"
								requireIcon="pre">
							</myInput>
							<myInput class="flexItem" v-model="dynamicValidateForm.lastName" prop="lastName"
								:rules="dynamicValidateFormRules.lastNameRules" :textLabel="langQuote.lastName"
								requireIcon="pre">
							</myInput>
						</div>
						<myInput v-model="dynamicValidateForm.email" @blur="getSmsSubscriptionByMail" prop="email"
							:rules="dynamicValidateFormRules.emailRules" :textLabel="langQuote.email" requireIcon="pre">
						</myInput>
						<div class="telephoneBox" :class="{ 'error--text': !areaCodeOk || !phoneOk }">
							<div class="textLabel" :style="{ color: !areaCodeOk || !phoneOk ? '#ff5252' : '#000' }">
								{{ langQuote.telephone }}
							</div>
							<div class="inputGroup">
								<div class="areaCode">
									<div class="prefix">+</div>
									<input v-model="dynamicValidateForm.areaCode" :class="{ noValid: !areaCodeOk }"
										@blur="checkTelephone" ref="areaCodeInput" class="customInput" type="text"
										style="width: 100%" @input="handleInputAreaCode" />
									<img class="area-cornor" :src="cornorImg" alt="" />
								</div>
								<input v-model="dynamicValidateForm.telephone" :class="{ noValid: !phoneOk }"
									@blur="checkTelephone" ref="telephoneInput" @input="handleInputTelephone"
									class="customInput telephone" type="text" />
							</div>
							<div class="textField">
								<div class="vaildate-message" v-show="!areaCodeOk || !phoneOk">
									{{ vaildateText }}
								</div>
							</div>
						</div>
						<div class="allowMsg" v-show="showSMStext" @click="handleAllowSendMsg">
							<div class="allow-header">
								<div class="allow-checkbox" :class="{ active: dynamicValidateForm.isSmsSubscriptions }">
								</div>
								<span class="allow-title">{{ langQuote.allowMsgTitle }}</span>
							</div>
							<p class="allow-content">
								{{ langQuote.allowMsgContentBefore + projectName + langQuote.allowMsgContentAfter }}
							</p>
						</div>
						<myInput v-model="dynamicValidateForm.subject" :textLabel="langQuote.title"></myInput>
						<div v-show="isFd" class="groupNumber" :class="{ 'error--text': groupNotValidate }">
							<label>
								<span class="textLabel">{{ langQuote.fdPins.pleaseOneGroupNum }}</span>
								<div class="groupBox">
									<div class="groupItem" v-for="item in dynamicValidateForm.groupNumber"
										:key="item.id">
										<myInput v-model="item.value" :textLabel="item.name" @blur="startCheckGroup">
										</myInput>
									</div>
								</div>
								<div class="textField">
									<div class="vaildate-message" v-show="groupNotValidate">{{
										langQuote.fdPins.pleaseOneGroupNum }}</div>
								</div>
							</label>
						</div>
						<div v-show="isFd" class="noCheckBox">
							<div class="tip">
								{{ langQuote.fd.fdInquiryTip }}
								<span style="color: #de3500;">*</span>
							</div>
							<div class="selectBox">
								<div class="allow-checkbox" @click="noCheck = !noCheck" :class="{ active: noCheck }">
								</div>
								<span class="allow-title">{{ langQuote.fd.otherNumber }}</span>
							</div>
						</div>
						<v-menu v-show="isGS" ref="menu1" content-class="dateSelfClass" v-model="queryDatesMenu"
							:close-on-content-click="false" offset-y :offset="50" max-width="350px" min-width="350px">
							<template v-slot:activator="{ on }">
								<myInput v-on="on" prefix="icon-riqi1" slotIconStyle="top:54%;font-size: 20px;"
									v-model="dynamicValidateForm.expectTime" :textLabel="langQuote.expectedDate">
								</myInput>
							</template>
							<v-date-picker class="dateSelfPickerClass" :min="minDate" v-model="date" no-title
								@input="queryDatesMenu = false"></v-date-picker>
						</v-menu>
						<div v-show="isFd" class="inquiryComments">
							<span class="textLabel">{{ langQuote.Comments }}</span>
							<textarea v-model="commentObj.comments"></textarea>
						</div>
					</myForm>
				</v-card-text>
				<v-card-actions class="actionBox">
					<v-btn color="primary" @click="handleConfirm">
						{{ langQuote.Confirm }}
					</v-btn>
				</v-card-actions>
			</v-card>
		</BaseDialog>
		<BaseDialog class="successDialog" :model="false" v-model="snackbar" :width="!isMobile ? '720px' : '90%'">
			<div class="close-icon" slot="closeIcon" @click="goHome">
				<v-icon>mdi-close-thick</v-icon>
			</div>
			<v-card>
				<div class="imgBox">
					<img src="https://oss-static-cn.liyi.co/web/quoteManage/20240807/success_20240807yQXzmH.png"
						alt="" />
				</div>
				<div class="successDes">
					<p>{{ langSemiCustom.inquirySuccess }}</p>
					<div class="introduce">{{ langSemiCustom.receivedLater }}</div>
				</div>
				<div class="productDetailInfo" v-if="inquiryData?.params && Object.keys(inquiryData.params).length > 0">
					<div class="productImg flexItem">
						<img :src="inquiryData.params[langSemiCustom.Artwork][0].img" alt="" />
					</div>
					<div class="productInfo">
						<div class="proName">{{ productInfo.name }}</div>
						<div class="sku">{{ langCart.itemNo + ".: " + langSemiCustom.item + productSku }}</div>
						<div class="productDetail">
							<div class="productFlexBox" v-for="(value, key, index) in inquiryData.params"
								v-if="key != langSemiCustom.Artwork" :key="index">
								<div class="proTextTitle">{{ key + ":" }}</div>
								<div class="proTextIntroduce">
									<template v-for="(citem, index2) in value">
										<a v-if="value[0].isEmailLater" :href="`mailto:${siteEmail}`"
											style="text-decoration: underline">{{
												citem.paramName }}</a>
										<span v-else :key="index2">
											{{ citem.paramName }}
										</span>
										<br />
									</template>
								</div>
							</div>
						</div>
					</div>
				</div>
			</v-card>
		</BaseDialog>
	</div>
</template>

<script>
import { otoEditInquiry } from "@/api/pins";
import myForm from "@/components/modal/Half/Detail/myForm/my-form.vue";
import myInput from "@/components/modal/Half/Detail/myForm/my-input.vue";
import BaseDialog from "@/components/Quote/BaseDialog";
import { debounce } from "@/utils/utils";
import { getSmsSubscriptionState, getSmsSubscriptionByMail, getSocialCodeByEmail } from "@/api/web";
export default {
	name: "inquiryBox",
	inject: ["getCommentObj"],
	components: { myForm, myInput, BaseDialog },
	props: {
		inquiryBoxVisible: {
			type: Boolean,
			default: false,
		},
		productSku: {
			type: String,
			default: "",
		},
		type: {},
		priceInfo: {
			type: Object,
			default: () => ({}),
		},
		productInfo: {
			type: Object,
			default: () => ({}),
		},
	},
	data() {
		return {
			dynamicValidateForm: {
				email: "",
				firstName: "",
				lastName: "",
				areaCode: "",
				telephone: "",
				subject: "",
				isSmsSubscriptions: 0,
				expectTime: "",
				groupNumber: [
					{
						id: 1,
						name: "ASI",
						value: "",
					},
					{
						id: 2,
						name: "SAGE",
						value: "",
					},
					{
						id: 3,
						name: "PPAI",
						value: "",
					},
					{
						id: 4,
						name: "PPPC",
						value: "",
					},
					{
						id: 5,
						name: "PSI",
						value: "",
					},
					{
						id: 6,
						name: "APPA",
						value: "",
					},
					{
						id: 7,
						name: "DC",
						value: "",
					},
				],
			},
			needCheck: false,
			noCheck: false,
			date: null,
			minDate: new Date().toISOString().substring(0, 10),
			inquiryData: {
				params: {},
			},
			snackbar: false,
			debounceAddInquiry: null,
			showSMStext: false,
			areaCodeOk: true,
			phoneOk: true,
			vaildateText: "",
			cornorImg: "data:image/png;base64,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",
			queryDatesMenu: false,
		};
	},
	watch: {
		inquiryBoxVisible(newV) {
			if (newV) {
				if (this.userInfo) {
					this.dynamicValidateForm.firstName = this.userInfo.firstName
					this.dynamicValidateForm.lastName = this.userInfo.lastName
					if (this.userInfo.email) {
						this.dynamicValidateForm.email = this.userInfo.email;
						this.getSocialCode()
					}
					if (this.userInfo.telephone) {
						let telephone, areaCode;
						if (this.userInfo.telephone) {
							if (this.userInfo.telephone.includes("-")) {
								telephone = this.userInfo.telephone.split("-")[1];
								areaCode = this.userInfo.telephone.split("-")[0];
							} else {
								telephone = this.userInfo.telephone;
								areaCode = this.areaCodes;
							}
							this.dynamicValidateForm.areaCode = this.userInfo.telephone ? areaCode : ""
							this.dynamicValidateForm.telephone = this.userInfo.telephone ? telephone : ""
						}
					} else {
						this.dynamicValidateForm.areaCode = this.areaCodes;
					}

				} else {
					this.dynamicValidateForm.areaCode = this.areaCodes || '';
				}
			}
			this.resetPhoneRules();
		},
		date(val) {
			this.dynamicValidateForm.expectTime = this.formatDate(this.date);
		},
	},
	computed: {
		projectName() {
			return this.$store.state.proName;
		},
		langSemiCustom() {
			return this.$store.getters.lang?.semiCustom;
		},
		langQuote() {
			return this.$store.getters.lang.quote || {};
		},
		langCart() {
			return this.$store.getters.lang?.cart;
		},
		isLogin() {
			return this.$store.getters.isLogin;
		},
		isMobile() {
			return this.$store.state.device === "mb";
		},
		siteEmail() {
			return this.$store.state.proSystem.email;
		},
		areaCodes() {
			return this.$store.state.areaCode;
		},
		dynamicValidateFormRules() {
			return {
				firstNameRules: [{ required: true, message: this.langQuote.p28, trigger: "blur" }],
				lastNameRules: [{ required: true, message: this.langQuote.p29, trigger: "blur" }],
				telephoneRules: [
					{ required: true, message: this.langQuote.p33, trigger: "blur" },
					{
						validator: (rule, value, callback) => {
							let rex = /^(?:\d+(-\d+)*|\d+)$/;
							if (value && !rex.test(value)) {
								callback(new Error(this.langQuote.p32));
							} else {
								callback();
							}
						},
						trigger: "blur",
					},
				],
				areaCodeRules: [{ required: true, message: this.langQuote.p38, trigger: "blur" }],
				emailRules: [
					{ required: true, message: this.langQuote.p30, trigger: "blur" },
					{
						validator: (rule, value, callback) => {
							let rex = /.+@.+\..+/;
							if (value && !rex.test(value)) {
								callback(new Error(this.langQuote.p31));
							} else {
								callback();
							}
						},
						trigger: "blur",
					},
				],
			};
		},
		proType() {
			return this.$store.state.proType;
		},
		proId() {
			return this.$store.state.proId;
		},
		isGS() {
			//GS体系 proType=0 且不是FD和POP ---// && this.proId != 9 当时忘记获取proId了
			return this.proType == 0 && this.proId != 10;
		},
		isFd() {
			return this.proId == 9
		},
		groupNotValidate() {
			if (!this.needCheck || this.noCheck) return false;
			return !this.dynamicValidateForm.groupNumber.some((item) => {
				return item.value && ("" + item.value).length > 0;
			});
		},
		commentObj() {
			return this.getCommentObj();
		},
		userInfo() {
			return this.$store.state.userInfo;
		},
	},
	methods: {
		//是否短信订阅(登录状态)
		getSmsSubscriptionLogin() {
			getSmsSubscriptionState().then((res) => {
				if (!res.data) this.showSMStext = true;
				else {
					if (res.data.isSmsSubscriptions == 0) {
						this.showSMStext = true;
					} else {
						this.showSMStext = false;
					}
				}
			});
		},

		//是否短信订阅(未登录状态)
		getSmsSubscriptionByMail() {
			if (!this.dynamicValidateForm.email.trim()) return;
			const params = {
				email: this.dynamicValidateForm.email,
			};
			this.getSocialCode()
			getSmsSubscriptionByMail(params).then((res) => {
				if (!res.data) this.showSMStext = true;
				else {
					if (res.data.isSmsSubscriptions == 0) {
						this.showSMStext = true;
					} else {
						this.showSMStext = false;
					}
				}
			});
		},
		// 电话校验
		async checkTelephone() {
			this.resetPhoneRules();
			if (!this.dynamicValidateForm.areaCode) {
				this.vaildateText = this.langQuote.areaCode;
				this.areaCodeOk = false;
			}
			if (!this.dynamicValidateForm.telephone) {
				this.vaildateText = this.langQuote.telephoneRequired;
				this.phoneOk = false;
			}
			if (this.dynamicValidateForm.telephone.length < 7 && this.phoneOk) {
				this.vaildateText = this.langQuote.phoneNoMinLength;
				this.phoneOk = false;
			}
			return this.areaCodeOk && this.phoneOk;
		},
		// 重置电话校验
		resetPhoneRules() {
			this.areaCodeOk = true;
			this.phoneOk = true;
		},

		closeFn() {
			this.$emit("update:inquiryBoxVisible", false);
			this.$refs.myParentRef.reset();
			this.dynamicValidateForm.telephone = "";
			this.dynamicValidateForm.expectTime = "";
			this.needCheck = false;
		},
		goHome() {
			if (this.type === "dialog") {
				this.snackbar = false;
				this.$store.commit("setMask", false);
			}
			this.$router.push({
				path: "/",
			});
			// this.snackbar = false;
		},
		async handleConfirm() {
			let validateResult = await this.$refs.myParentRef.validate();
			let validateTelephone = await this.checkTelephone();
			this.needCheck = true;
			if (this.isFd && this.groupNotValidate) return;
			if (validateResult && validateTelephone) {
				let expectTime = null;
				if (this.dynamicValidateForm.expectTime) {
					expectTime = {
						attributeFlag: "",
						attributeName: "Expected Delivery Date",
						productParamList: [
							{
								valueName: this.dynamicValidateForm.expectTime,
							},
						],
					};
				}
				this.debounceAddInquiry(expectTime);
			}
		},
		handleInputAreaCode(e) {
			// 只允许输入数字
			const v = e.target.value;
			this.dynamicValidateForm.areaCode = v.replace(/[^\d]/g, "");
		},
		handleInputTelephone(e) {
			// 只允许输入数字
			const v = e.target.value;
			this.dynamicValidateForm.telephone = v.replace(/[^\d]/g, "");
		},
		handleAllowSendMsg() {
			let numBoolean = this.dynamicValidateForm.isSmsSubscriptions ? 0 : 1;
			this.dynamicValidateForm.isSmsSubscriptions = numBoolean;
		},
		async saveInquiryData(data) {
			this.inquiryData = data;
			let priceInfo = {};
			if (Object.keys(this.priceInfo).length > 0) {
				priceInfo = {
					discount: (10000 - this.priceInfo.discount * 10000) / 100,
					quantity: this.priceInfo.totalQuantity,
					unitPrice: this.priceInfo.foundationUnitPrice,
					mouldPrice: this.priceInfo.toolingCharge,
					totalPrice: this.priceInfo.totalPrice,
					productSku: this.productSku,
				};
			}
			if (this.isFd) {
				let obj = {}
				if (this.noCheck) {

				} else {
					obj = this.dynamicValidateForm.groupNumber.reduce((pre, next, index) => {
						if (next.value) {
							pre[next.name] = next.value;
						}
						return pre;
					}, {});
				}
				this.dynamicValidateForm.socialCode = JSON.stringify(obj);
			}
			otoEditInquiry(
				Object.assign(
					{},
					{
						platformProductId: this.productInfo.platformProductId,
						productsName: this.productInfo.name,
						quoteCateId: this.productInfo.categoryId,
						quoteCateChildId: this.productInfo.id,
						isMobile: this.isMobile ? 1 : 0,
						buyType: 9,
					},
					this.dynamicValidateForm,
					{
						telephone: this.dynamicValidateForm.areaCode + "-" + this.dynamicValidateForm.telephone,
					},
					this.inquiryData,
					priceInfo
				)
			).then((res) => {
				this.closeFn();
				this.snackbar = true;
			});
		},
		formatDate(date) {
			if (!date) return null;
			const [year, month, day] = date.split("-");
			return `${month}/${day}/${year}`;
		},
		startCheckGroup() {
			if (!this.needCheck) this.needCheck = true;
		},
		getSocialCode() {
			if (!this.isFd) return
			let rex = /.+@.+\..+/;
			if (rex.test(this.dynamicValidateForm.email)) {
				getSocialCodeByEmail({
					email: this.dynamicValidateForm.email
				}).then(res => {
					if (res.data && res.data.length > 0) {
						let socialCode = JSON.parse(res.data);
						this.dynamicValidateForm.groupNumber.forEach((item, index) => {
							if (item.value === undefined || item.value === null || item.value === '') {
								let key = item.name;
								if (socialCode[key] !== undefined) {
									this.$set(this.dynamicValidateForm.groupNumber, index, {
										...item,
										value: socialCode[key]
									});
								}
							}
						})
					}
				})
			}
		}
	},
	created() { },
	mounted() {
		this.debounceAddInquiry = debounce((data, comment) => {
			this.$emit("addInquiry", data, comment);
		}, 500);
		this.$Bus.$on("sendInquiryData", this.saveInquiryData);
		if (this.isLogin) this.getSmsSubscriptionLogin();
	},
	beforeDestroy() {
		this.$Bus.$off("sendInquiryData", this.saveInquiryData);
	},
};
</script>
<style></style>

<style scoped lang="scss">
@import "~assets/css/half.scss";

.close-icon {
	.v-icon {
		cursor: pointer;
		position: absolute;
		top: 20px;
		right: 16px;

		&::before {
			font-size: 18px;
			font-weight: 700;
			color: #333;
		}
	}
}

.dateSelfClass {
	transform: translate(0, -100%);
}

.dateSelfPickerClass {
	width: 100%;

	::v-deep .v-picker__body {
		width: 100% !important;
	}
}

.inquiryDialogHead {
	.dialogHead {
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: space-between;
		font-size: 18px;
		font-weight: 700 !important;
		color: #333;

		.text-h5 {
			font-size: 13px;
		}
	}
}

.v-card__actions.actionBox {
	justify-content: center;
	padding: 0 10px 24px !important;

	button {
		font-size: 16px;
		padding: 0 50px !important;
	}

	@include respond-to(mb) {
		button {
			font-size: 12px;
		}
	}
}

.telephoneBox {
	font-size: 14px;

	.textLabel {
		color: #333 !important;
		margin-bottom: 4px;

		&::before {
			content: "*";
			color: #de3500;
		}
	}

	&.error--text {
		caret-color: #000;

		.noValid {
			border-color: #de3500 !important;
			caret-color: #de3500;
		}
	}

	.inputGroup {
		display: grid;
		grid-template-columns: 60px 1fr;
		gap: 10px;

		.customInput {
			font-size: 12px;
			color: #333;

			&.telephone {
				font-size: 16px;
			}
		}

		input {
			padding: 8px 10px;
			background: #f5f5f5;
			border: 1px solid transparent;
			border-radius: 6px;
			margin-bottom: 4px;
		}

		.areaCode {
			position: relative;

			input {
				text-indent: 1em;
			}

			.prefix {
				position: absolute;
				left: 0;
				top: 0;
				font-size: 16px;
				left: 0.5em;
				top: 0.5em;
				color: #000;
			}

			.area-cornor {
				position: absolute;
				right: 0;
				bottom: 4px;
				width: 1em !important;
			}
		}
	}

	.textField {
		display: flex;
		flex: 1 0 auto;
		max-width: 100%;
		min-height: 14px;
		overflow: hidden;
		line-height: 12px;
		font-size: 12px;
		word-break: break-word;
		word-wrap: break-word;
		hyphens: auto;
	}

	@include respond-to(mb) {
		font-size: 12px;

		.inputGroup {
			.customInput {
				&.telephone {
					font-size: 12px;
				}
			}
		}
	}
}

::v-deep .groupNumber {
	width: 100%;

	&.error--text {
		input {
			border: 1px solid #ff5252 !important;
		}

		.vaildate-message {
			color: #ff5252 !important;
			caret-color: #ff5252 !important;
		}
	}

	label {
		position: relative;
		display: flex;
		flex-direction: column;
		font-size: 14px;
		pointer-events: none;

		.textLabel {
			margin-bottom: 4px;
			color: #333;
			font-size: 14px;

			@include respond-to(mb) {
				font-size: 12px;
			}
		}

		input {
			pointer-events: all;
			padding: 8px 10px;
			background: #f5f5f5;
			border: 1px solid transparent;
			border-radius: 6px;

			&::placeholder {
				font-size: 14px;
				color: inherit;
			}
		}

		span {
			&::after {
				content: "*";
				color: #de3500;
				margin-left: 4px;
			}
		}

		@include respond-to(mb) {
			font-size: 12px;
		}
	}

	.textField {
		margin-top: 4px;
		pointer-events: none;
		display: flex;
		flex: 1 0 auto;
		max-width: 100%;
		min-height: 14px;
		overflow: hidden;
		line-height: 12px;
		font-size: 12px;
		word-break: break-word;
		word-wrap: break-word;
		hyphens: auto;
	}

	.groupBox {
		width: 100%;
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		column-gap: 20px;

		@include respond-to(mb) {
			column-gap: 10px;
		}

		.groupItem {
			min-width: 0;

			.myInput {
				label {
					font-size: 14px;

					input {
						background: #f5f5f5;
						border-radius: 4px;

						&::placeholder {
							font-weight: 400;
							font-size: 12px;
							color: #999999;
						}
					}
				}

				.textLabel {
					font-size: 14px;

					&::after {
						content: none;
					}

					@include respond-to(mb) {
						font-size: 12px;
					}
				}

				.textField {
					display: none;
				}
			}
		}
	}
}

.inquiryComments {
	margin-top: 4px;
	width: 100%;
	display: flex;
	flex-direction: column;
	color: #333;

	.textLabel {
		color: #333;
		font-size: 14px;
		margin-bottom: 4px;
	}

	textarea {
		padding: 4px 6px;
		font-size: 14px;
		width: 100%;
		height: 120px;
		resize: none;
		overflow-y: auto;
		background: #f5f5f5;
		border-radius: 6px;

		&::placeholder {
			font-size: 12px;
			color: #999999;
		}
	}

	@include respond-to(mb) {
		.textLabel {
			font-size: 12px;
		}
	}
}

.inquiryBox {
	.nameDialog {
		.v-card {
			width: 100%;
		}

		::v-deep .myInput .textLabel {
			font-size: 14px;
		}

		.flexBox {
			display: flex;
			align-items: center;
			gap: 10px;

			.flexItem {
				min-width: 0;
				flex: 1;
			}
		}

		&.isFd {
			::v-deep .base-dialog-model-con {
				margin: 20px 0;
			}
		}
	}

	.successDialog {
		.imgBox {
			width: 40%;
			margin: 0 auto;
			padding-top: 20px;

			img {
				max-width: 100%;
				max-height: 100%;
				object-fit: cover;
				vertical-align: middle;
			}
		}

		.successDes {
			width: 80%;
			margin: 0 auto;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;

			p {
				font-weight: bold;
				font-size: 24px;
				color: #333333;
				text-align: center;
				margin-bottom: 10px;
			}

			.introduce {
				font-weight: 400;
				font-size: 16px;
				color: #666666;
				text-align: center;
				margin-bottom: 20px;
			}
		}

		.productDetailInfo {
			background: #fafafa;
			padding: 15px 20px;
			display: flex;

			.flexItem {
				width: 40%;
			}

			.productImg {
				aspect-ratio: 1/1;

				img {
					width: auto;
					max-width: 100%;
					max-height: 100%;
					vertical-align: middle;
					object-fit: cover;
				}
			}

			.productInfo {
				width: 60%;
				padding-left: 20px;
				font-size: 16px;

				.proName {
					font-weight: bold;
					color: #333333;
				}

				.sku {
					margin: 8px 0;
					font-weight: 400;
					color: #333333;
				}

				.productDetail {
					display: flex;
					flex-direction: column;
					row-gap: 8px;

					.productFlexBox {
						display: flex;
						align-items: flex-start;
						font-size: 16px;

						.proTextTitle {
							font-weight: 400;
							color: #666666;
							margin-right: 10px;
							text-align: left;
							flex: 0 0 130px;
							word-break: break-word;
							word-wrap: break-word;
						}

						.proTextIntroduce {
							min-width: 0;
							overflow: hidden;
							font-weight: 400;
							color: #333333;
							flex: 1;
							word-break: break-word;
							word-wrap: break-word;

							a {
								color: $color-primary;
							}
						}
					}
				}
			}
		}

		@include respond-to(mb) {
			.close-icon {
				.v-icon {
					top: 10px;
					right: 10px;
				}
			}

			.imgBox {
				width: 50%;
			}

			.successDes {
				width: 90%;

				p {
					font-size: 15px;
				}

				.introduce {
					font-size: 12px;
				}
			}

			.productDetailInfo {
				flex-direction: column;

				.flexItem {
					width: 80%;
					margin: 0 auto;
				}

				.productInfo {
					width: 90%;
					margin: 10px auto 0;
					padding-left: 0;
					font-size: 12px;

					.productDetail {
						.productFlexBox {
							font-size: 12px;
						}
					}
				}
			}
		}
	}
}

.allowMsg {
	padding: 10px;
	margin-block: 10px 15px;
	background-color: #fff1ea;
	cursor: pointer;

	.allow-header {
		display: flex;
		align-items: center;
		font-family: Cambria, Cochin, Georgia, Times, "Times New Roman", serif;

		.allow-checkbox {
			height: 2em;
			width: 2em;
			background-color: #fff;
			border-radius: 2px;
			border: 1px solid #d24600;
			position: relative;

			&.active::before {
				content: "";
				position: absolute;
				left: 50%;
				top: 50%;
				height: 18px;
				width: 8px;
				transform: translate(-50%, -60%) rotate(45deg);
				border-right: 1px solid #d24600;
				border-bottom: 1px solid #d24600;
			}
		}

		.allow-title {
			font-weight: 400;
			font-size: 16px;
			margin-left: 10px;
		}
	}

	.allow-content {
		font-size: 11px;
		margin-top: 5px;
		line-height: 1.5;
	}
}

.noCheckBox {
	margin: 6px 0;

	.tip {
		font-size: 15px;
		color: #333;
		margin-bottom: 4px;
	}

	.selectBox {
		display: flex;
		align-items: center;
		gap: 1em;

		.allow-checkbox {
			height: 2em;
			width: 2em;
			background-color: #fff;
			border-radius: 2px;
			border: 1px solid #000;
			position: relative;
			flex-shrink: 0;
			cursor: pointer;

			&.active::before {
				content: "";
				position: absolute;
				left: 50%;
				top: 50%;
				height: 18px;
				width: 8px;
				transform: translate(-50%, -60%) rotate(45deg);
				border-right: 2px solid #ff5252;
				border-bottom: 2px solid #ff5252;
			}
		}

		.allow-title {
			color: rgba(0, 0, 0, 0.6);
			font-weight: 400;
			font-size: 13px;
		}
	}

	@include respond-to(mb) {
		.tip {
			font-size: 12px;
		}

		.selectBox {
			.allow-title {
				font-size: 12px;
			}
		}
	}
}
</style>
