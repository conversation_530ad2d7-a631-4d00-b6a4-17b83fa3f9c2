<template>
	<article class="cufflink-quote">
		<template v-if="this.cateList.length">
			<template v-if="!isMobile">
				<p class="title">We provide these Cufflinks types for you.</p>
				<div class="box">
					<div class="card" v-for="item in cateList" :key="item.id" @click="linkTo(item)">
						<div class="card-box">
							<div  :class="item.id == pid ? 'box-active' : ''" >
								<img :src="item.recommendPhoto">
							</div>
							<div class="stitle" :class="item.id == pid ? 'text-active' : ''">
								{{ item.cateName }}
							</div>
						</div>
						<div class="card-hover">
							<img :src="item.recommendPhoto2">
						</div>
					</div>
				</div>
			</template>
			<template v-else>
				<p class="title">We provide these Cufflinks types for you.</p>
				<div class="mySwiper">
					<div style="--swiper-navigation-color: #fff; --swiper-pagination-color: #fff" class="swiper"
						ref="mySwiper">
						<div class="swiper-wrapper">
							<div class="swiper-slide" v-for="item in cateList" :key="item.id"
								:disabled="item.id == pid ? true : false">
								<div class="card"  @click="linkTo(item)">
									<div :class="item.id == pid ? 'cardActiveMB' : ''">
										<img :src="item.recommendPhoto">
									</div>
									<div :class="item.id == pid ? 'textActiveMB' : ''">
										<p>{{ item.cateName }}</p>
									</div>
								</div>
							</div>
						</div>
						<div class="swiper-button-prev" slot="button-prev"><b class="icon-qiehuan"></b></div>
						<div class="swiper-button-next" slot="button-next"><b class="icon-qiehuan2"></b></div>
					</div>
				</div>
			</template>
		</template>
	</article>
</template>
<script>
export default {
	data() {
		return {
			swiper: null,
			swiper2: null,
		};
	},
	props: {
		pid: {
			type: [Number, String],
		},
		title: {
			type: String,
		},
		cateList: {
			type: Array
		}
	},
	methods: {
		async linkTo(val) {
		    let routeName='/quote/'+val.routingName.split('/')[1]+'-'+val.routingName.split('/')[2];
			if (val.id == '-1') {
				window.open(this.customData.customHref);
				return
			}
			this.$router.push({
				path: routeName,
			});
		},
		async siwperInit() {
			await this.$nextTick();
			this.swiper = new Swiper(this.$refs.mySwiper, {
				slidesPerView: 3,
				spaceBetween: 10, // 控制swiper-slider间距
				slidesPerGroup: 3,
				loop: true,
				loopFillGroupWithBlank: true,
				navigation: {
					nextEl: ".swiper-button-next",
					prevEl: ".swiper-button-prev",
				},
			});
		},
	},
	computed: {
		lang() {
			return this.$store.getters.lang.quote || {};
		},
		proId() {
			return this.$store.state.proId;
		},
		device() {
			return this.$store.state.device;
		},
		isMobile() {
			return this.$store.getters.isMobile;
		},
	},
	created() {
		this.siwperInit();
	}
};
</script>
<style lang="scss">
.cufflink-quote {
	padding: 32px 0;

	.title {
		margin-bottom: 20px;
		font-size: 20px;
		text-align: center;
		font-weight: bold;
	}

	.box {
		display: grid;
		grid-template-columns: repeat(6,1fr);
		column-gap: 18px;

		.card {
			// width: 186px;
			// height: 201px;
			// margin-right: 20px;
			cursor: pointer;
			position: relative;
			img {
				min-height: 148px;
				border-radius: 10px;
			}
			.stitle {
				margin-top:14px;
				font-size: 18px;
				text-align: center;
			}
			.box-active {
				border:2px solid $color-primary;
				border-radius: 10px;
				// .stitle {
				// 	font-weight: bold;
				// 	color:$color-primary
				// }
			}
			.box-active::after {
				content: "\e7b8";
				font-family: "modalicon";
				position: absolute;
				color: #fff;
				top: 0;
				right: 0;
				width: 34px;
				height: 22px;
				font-size: 12px;
				text-align: center;
				line-height: 20px;
				background: $color-primary;
				border-radius: 0px 9px 0px 12px;
			}
			.text-active {
				color:$color-primary;
				font-weight: bold;
			}
			.card-hover {
				display: none;
			}
		}
		.card:hover {
			.card-hover {
				display: block;
			}
			.card-box {
				display: none;
			}
		}
	}
	.swiper-button-prev::after {
		content:"" !important;
	}
	.swiper-button-next::after {
		content:"" !important;
	}
	.swiper-button-prev {
		top: 40% !important;
		left: 0 !important;
	}
	.swiper-button-next {
		top: 40% !important;
		right: 0 !important;
	}
	.mySwiper b {
		color: rgba(0, 0, 0, .3) !important;
        font-size: 24px !important;
	}
}


@media screen and (max-width: 767px) {
	.cufflink-quote {
		font-family: Calibri;
		padding:20px 0 0;
		.cardActiveMB {
			border-radius: 5px;
			border: 1px solid $color-primary;
				// .stitle {
				// 	font-weight: bold;
				// 	color:$color-primary
				// }
			&::after {
				content: "\e7b8";
				font-family: "modalicon";
				position: absolute;
				color: #fff;
				top: 0;
				right: 0;
				width: 22px;
				height: 14px;
				font-size: 12px;
				text-align: center;
				line-height: 14px;
				background: $color-primary;
				border-radius: 0px 4px 0px 6px;
			}
		}
		.textActiveMB {
			color:$color-primary;
			font-weight: bold;
		}
	    .mySwiper {
			.swiper-slide {
				img {
					width: 100% !important;
					border-radius: 5px;
					min-height: 45px;
				}
				p {
					font-size: 12px;
					margin-top:7px;
				}
			}
			
		}
	}
}

</style>