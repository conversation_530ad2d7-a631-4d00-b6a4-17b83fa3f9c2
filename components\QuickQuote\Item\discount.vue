<template>
	<Step :itemData="item">
		<template v-slot:content>
			<div class="box-border"></div>
            <div class="step-time-tip">
                {{ lang.pleaseNote }}
            </div>
			<div class="step-item-params">
				<div class="param-item-wrap" v-for="citem in getNewDiscountList(item)" :key="citem.id">
					<div
						class="param-item"
						:class="{
							active: hasId(citem.id, selectedData[item.paramName]),
						}"
						@click="selectQuoteParams(item, citem)"
					>
						<CustomCircle :circleType="2" :isActive="hasId(citem.id, selectedData[item.paramName])"></CustomCircle>
                        <DiscountText :itemData="citem" :textConfig="textConfig"></DiscountText>
					</div>
					<div
						class="param-item-hover"
						:class="{
							active: hasId(citem.id, selectedData[item.paramName]),
						}"
						@click="selectQuoteParams(item, citem)"
					>
						<div class="param-item">
							<CustomCircle :circleType="2" :isActive="hasId(citem.id, selectedData[item.paramName])"></CustomCircle>
							<DiscountText :itemData="citem" :textConfig="textConfig"></DiscountText>
						</div>
						<div class="des">{{ citem.alias }}</div>
					</div>
				</div>
			</div>
		</template>
	</Step>
</template>
<script>
import Step from "~/components/QuickQuote/Step.vue";
import CustomCircle from "@/components/Quote/customCircle.vue";
import DiscountText from "@/components/modal/Quote/QuoteComponents/DicountText.vue";
import {getQuoteTime} from "@/assets/js/quote/quotePublic";
export default {
	inject: ["getProvideData"],
	props: ["item"],
	components: { DiscountText, Step, CustomCircle },
	computed: {
		lang() {
			return this.$store.getters.lang.quote || {};
		},
		device() {
			return this.$store.state.device;
		},
		proType() {
			return this.$store.state.proType;
		},
		currencySymbol() {
			return this.$store.getters.currencySymbol;
		},
		provideData() {
			return this.getProvideData();
		},
		cateInfo() {
			return this.provideData.cateInfo;
		},
		selectedData() {
			return this.provideData.selectedData;
		},
        priceInfo(){
            return this.provideData.priceInfo;
        },
		customQty() {
			return this.provideData.customQty;
		},
        textConfig(){
            return {
                freeText: this.lang.NoDiscount
            }
        },
	},
	methods: {
		//判断是否选中
		hasId(id, arr) {
			if (!arr) {
				return false;
			}
			return !!arr.find((item) => {
				return item.id === id;
			});
		},
		selectQuoteParams(item, citem) {
			this.$emit("selectQuoteParams", {
				item,
				citem,
			});
		},
        getNewDiscountList(itemData) {
            let result = getQuoteTime(itemData.childList,this.priceInfo,this.proType),
                    originShowSmallPrice =  this.$store.state.showSmallPrice;
            this.$store.commit("setShowSmallPrice", result.newShowSmallPrice);
            if(result.newShowSmallPrice !== originShowSmallPrice){
                this.$Bus.$emit("clearDiscount");
            }
            return result.arr;
        },
	},
};
</script>

<style scoped lang="scss">
.step-time-tip{
    margin-bottom: 10px;
    font-size: 14px;
    color: #808080;
}
.step-time {
	grid-column: 1 / span 2;
	min-width: 0;

	.box-border {
		display: none;
	}

	@include respond-to(mb) {
		grid-column: 1 / span 1;
	}

	.step-item-params {
		display: grid;
		grid-template-columns: repeat(4, 1fr);
		grid-gap: 20px;

		@include respond-to(mb) {
			grid-template-columns: repeat(2, 1fr);
			grid-gap: 5px;
		}

		.param-item-wrap {
			position: relative;
			border: 1px solid transparent;
			border-radius: 6px;

			@include respond-to(mb) {
				background-color: #fafafa;
				& > .param-item {
					display: none !important;
				}
			}

			.param-item-hover {
				position: absolute;
				border: 1px solid transparent;
				border-radius: 6px;
				left: -1px;
				right: -1px;
				top: -1px;
				background-color: #ffffff;
				display: none;

				@include respond-to(mb) {
					display: block;
					position: relative;
					min-height: 90px;
					background-color: transparent;
					padding: 10px 0;
				}

				.des {
					display: block;
					padding-bottom: 10px;
				}
			}

			.param-item {
				position: relative;
				min-width: 0;
				display: flex;
				justify-content: flex-start;
				align-items: center;
				padding: 8px;
				cursor: pointer;

				@include respond-to(mb) {
					padding: 5px;
				}

				&.active {
					border-color: $color-primary;
				}

				.brow {
					margin: 0 4px;
					color: #eb7e1a;
					text-decoration-line: underline;
				}

				.uploadIcon {
					margin-right: 8px;
					font-size: 22px;
					color: #eb7e1a;
				}
			}

			.des {
				display: none;
				padding: 0 8px 0 36px;
			}

			@media (any-hover: hover) {
				&:hover {
					.param-item-hover {
						display: block;
						border-color: $color-primary;
					}
				}
			}
		}
	}
}
</style>