<template>
	<div class="wrap">
		<div class="content" id="pdfDom">
			<div class="con content1">
				<div>
					<img src="http://192.168.1.54:50045/_nuxt/static/img/logo.png" alt="" style="width: max-content;">
				</div>
				<div style="display: flex;margin-top: 30px">
					<div style="flex: 1">
						<p style="margin: 0;font-weight: bold">O20.com LTD</p>
						<p style="margin: 0;font-weight: bold">20829 Valley Blvd. Walnut, CA 91789<br/> United Stated</p>
					</div>
					<div style="flex: 1">
						<p style="margin: 0;font-weight: bold">send To:</p>
						<p style="margin: 0;font-weight: bold">enamelpins inc 4061 Paso Fino Way Yorba LindaCA, United Stated</p>
					</div>
				</div>
				<div style="margin-top: 165px">
					<p style="font-size: 16px;font-weight: bold">lnvoice #1002673713  |   11th June, 2022  |   Paid</p>
				</div>
				<div style="margin-top: 20px">
					<table style="width: 100%;background-color: #F2F2F2;font-size:16px;border-bottom: 1px solid #ccc;font-weight: bold">
						<thead>
						<tr>
							<th style="padding: 15px 0" colspan="2">order No.</th>
							<th colspan="2">Product Types </th>
							<th colspan="2">Quantity</th>
							<th colspan="2">Uint Price</th>
							<th colspan="2">Amount</th>
						</tr>
						<tr v-for="(item,index) in detailInfo.ordersProducts" style="background-color: #fff">
							<th colspan="2" style="padding: 20px 0;font-weight:400">{{item.oid}}</th>
							<th colspan="2" style="padding: 20px 0;font-weight:400">{{item.productName}}</th>
							<th colspan="2" style="padding: 20px 0;font-weight:400">{{item.quantity}}</th>
							<th colspan="2" style="padding: 20px 0;font-weight:400">{{item.unitPrice}}</th>
							<th colspan="2" style="padding: 20px 0;font-weight:400">{{item.totalPrice}}</th>
						</tr>
						</thead>
					</table>
				</div>
				<div style="display: flex;margin-top: 120px">
					<div style="flex: 1">
						<p style="margin: 0;font-weight: bold">Payment Method: AMEX·.·1001</p>
					</div>
					<div style="flex: 1">
						<p style="margin: 0;overflow: hidden;border-bottom: 1px solid #ccc;padding-bottom: 35px">
							<span>Subtotal:</span>
							<span style="float: right">${{detailInfo.productPrice}}</span>
						</p>
						<p style="margin: 0;font-weight: bold;overflow:hidden;margin-top: 35px">
							<span>Total:</span>
							<span style="float: right">${{detailInfo.totalPrice}}</span>
						</p>
					</div>
				</div>
				<div style="margin-top: 340px;border-top: 1px solid #ccc;padding: 15px 0;display: flex">
					<div style="flex: 1;overflow: hidden">
						<span>Welcome to contact us</span>
						<span style="margin-left: 20px">O2O.com/support</span>
					</div>
					<div style="flex: 1;overflow:hidden;">
						<span style="margin-left: 150px">555-555-5555</span>
						<span style="float: right">O2O.com/contact</span>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
	import {getOrderInfo} from "@/api/manage/order";
	export default {
		data() {
			return {
				currentTime: '',
				detailInfo:''
			};
		},
		methods: {
			getInquiryDetailInfo() {
				getOrderInfo({
					orderId: this.$route.query.id,
					proId: this.$store.getters["manage/getProId"],
					getProductInfo:true
				}).then(res => {
					this.detailInfo = res.data;
					this.currentTime = new Date().toLocaleString();
					setTimeout(()=>{
						window.print()
					},1000)
				})
			},
		}
	};
</script>
<style lang="scss" scoped>
	.wrap {
		font-size: 15px;
	}

	.content {
		width: 910px;
		margin: 20px auto;
	}

	.con {
		width: 100%;
		background: #FFFFFF;
		padding: 25px;
		font-size: 15px;
		color: #525659;
		page-break-after: always;

		table {
			border-collapse: collapse;
			border-color: #D9D9D9;
		}

		.bg {
			background-color: #f2f2f2;
		}
	}
</style>
