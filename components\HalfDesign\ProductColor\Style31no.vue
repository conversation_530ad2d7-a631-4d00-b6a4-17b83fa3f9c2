<template>
	<!-- 鼠标移入展示图片，如果只有一个选项隐藏 -->
  <div class="mb-4" :class="stepData.styleName">
    <half-design-litter-title v-show="stepData.attributeTitle&&stepData.productParamList.length > 1" :index="stepData.id"  :data-name="`${stepData.styleClass + '_' + stepData.id}`" style="margin: 4px 0" :stepTitle="stepData.minStepTitle">{{ stepData.attributeTitle }} </half-design-litter-title>
	<slot name="stepText"></slot>
    <div class="step-content" v-if="stepData.productParamList.length > 1">
      <template v-for="(step, index) in stepData.productParamList">
        <v-tooltip top color="#fff"  :content-class="'customColorTip'" nudge-top="4">
          <template v-slot:activator="{ on, attrs }">
            <div class="step-item" v-bind="attrs" v-on="on" :class="{ active: index === selectIndex }" :key="index" @click.stop="selectStep(step, index)">
              <img v-if="step.imgDetail" :src="step.imgDetail" :alt="step.valueName" :title="step.valueName" />
              <span v-else :style="{ backgroundColor: step.colorCode }"></span>
            </div>
          </template>
          <div class="text-center" style="display: flex; align-items: start">
            <div v-if="step?.imgDetail" style="display: flex;align-items: center;justify-content: center;max-width: 150px;min-width: 40px; max-height: 150px;background-color: #fff;">
              <img :src="step.imgDetail" :alt="step.valueName" :title="step.valueName" style="object-fit: contain;" />
            </div>
            <div v-else style="text-align: left; font-size: 13px; width: fit-content; max-width: 250px; word-break: break-word;" :style="{ color: '#fff' }">
              {{step.remark }}</div>
          </div>
        </v-tooltip>
      </template>
    </div>
    <div class="errorTip">
      <v-alert dense outlined type="error">
        {{ langSemiCustom.errorTip }}
      </v-alert>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    stepData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      selectIndex: -1,
      selectItem: null
    }
  },
  computed: {
    langSemiCustom() {
      return this.$store.getters.lang?.semiCustom
    },
    shape() {
      return this.selectItem?.colorAlias || this.selectItem?.valueName
    }
  },
  methods: {
    selectStep(item, index, state = false) {
      this.selectIndex = index
      this.selectItem = item
      this.$emit('selectStep', {
        type: this.stepData.attributeFlag,
        data: item,
        parent: this.stepData,
        id: this.stepData.id,
        firstSelect: state,
        colorIndex: index
      })
    },
    selectDefault() {
      if (this.selectIndex <= -1) {
        this.selectStep(this.stepData.productParamList[0], 0, true)
      }
    },
    setActiveProductColor() {
      let newColorList = this.stepData.productParamList.filter(color => {
        return color.isActivity == 1
      })
      if (newColorList.length == 0) {
        return
      }
      this.stepData.productParamList = newColorList
    }
  },
  mounted() {
    this.$Bus.$on('selectDefaultColorStep', this.selectDefault)
    this.$Bus.$on('setActiveProductColor', this.setActiveProductColor)
  },
  beforeDestroy() {
    this.$Bus.$off('selectDefaultColorStep', this.selectDefault)
    this.$Bus.$off('setActiveProductColor', this.setActiveProductColor)
  }
}
</script>
<style lang="scss">
.customColorTip {
  z-index: 1000000 !important;
}
.customColorTip.v-tooltip__content {
  padding: 6px 16px;
  background: #fff;
  opacity: 1;
  border: 2px solid $color-primary !important;
  min-width: 80px;
  &::after,
  &::before {
    content: '';
    width: 0;
    height: 0;
    aspect-ratio: 1;
    position: absolute;
    bottom: -20px;
    left: 50%;
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
    border-bottom: 10px solid transparent;
    border-top: 10px solid $color-primary;
    transform: translateX(-50%);
  }
  &::before {
    bottom: -16px;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-bottom: 8px solid transparent;
    border-top: 8px solid #fff;
    z-index: 1;
  }
}
</style>
<style scoped lang="scss">
@import '~assets/css/half.scss';

.style1 .step-content {
  display: grid;
  grid-template-columns: repeat(auto-fill, 24px);
  grid-gap: 10px;

  .step-item {
    min-width: 0;
    position: relative;
    @include flex-center;
    border-radius: 50%;
    cursor: pointer;
    aspect-ratio: 1;
    height: 24px;
    width: 24px;
    border: 1px solid #ccc;

    .v-icon {
      display: none;
    }

    &::before {
      display: none;
      content: '';
      position: absolute;
      left: -4px;
      top: -4px;
      right: -4px;
      bottom: -4px;
      border-radius: 50%;
      border: 2px solid $color-primary;
    }

    @media (any-hover: hover) {
      &:hover {
        &::before {
          display: block;
        }

        .v-icon {
          display: block;
        }
      }
    }
  }

  .step-item.active {
    &::before {
      display: block;
    }

    .v-icon {
      display: block;
    }
  }
}

@include respond-to(mb) {
  .style1 .step-content {
    grid-template-columns: repeat(auto-fill, 24px);
  }
}
</style>
