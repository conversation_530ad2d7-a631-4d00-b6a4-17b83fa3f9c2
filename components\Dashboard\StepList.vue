<template>
	<div :class="{'isNav':isNav}">
		<template v-for="(item,index) in stepList">
			<v-hover
				v-slot="{ hover }"
				open-delay="200"
			>
				<div :key="index" class="step-item mb-5" :class="{complete:item.status,'hover-style':hover}" v-if="showStep(item)" @click="toUrl(item)">
					<div class="left d-flex align-center">
						<div class="icon">
							<template v-if="item.status===0">
								<span class="circle"></span>
							</template>
							<template v-else>
								<v-icon color="success" small>mdi-check</v-icon>
							</template>
						</div>
						<div class="pl-6">
							<span>{{ item.stepName }}</span>
							<span v-if="item.tips" class="error--text">{{ item.tips }}</span>
						</div>
					</div>
					<div class="operateBtn d-flex align-center" v-if="item.status===0">
						<span class="mr-5 pointer skip" @click.stop="skipStep(item)" v-if="item.isSkip===1">skip</span>
						<v-btn width="100" color="primary" depressed rounded :outlined="!hover" small class="full-height font-weight-bold">Get
							Started
						</v-btn>
					</div>
				</div>
			</v-hover>
		</template>
	</div>
</template>

<script>
import {updateProSteps} from "@/api/manage/dashboard";

export default {
	name: "StepList",
	props: {
		stepList: {
			type: Array,
			default: []
		},
		showAllStep: {
			type: Boolean,
			default: false
		},
		isNav:{
			type: Boolean,
			default: false
		}
	},
	data() {
		return {}
	},
	methods: {
		skipStep(item) {
			this.$messageBox.show({
				title: 'Tips',
				content: 'Are you sure to skip this step?',
				confirmText: 'Confirm'
			}).then(() => {
				updateProSteps({
					proId: this.$store.getters["manage/getProId"],
					id: item.id,
					status: 2,
					stepId: item.stepId
				}).then(res => {
					this.$store.dispatch('manage/updateStep')
				})
			}).catch(() => {

			})
		},
		toUrl(item) {
			this.$emit('closeMenu')
			this.$router.push({
				path: item.url
			})
		},
		showStep(item) {
			if (this.showAllStep) {
				return true
			} else {
				return item.status === 0;
			}
		}
	}
}
</script>

<style scoped lang="scss">
.skip{
	color: #999999;
}
.isNav{
	color: #333333;
	.error--text{
		display: none;
	}
	.skip{
		color: var(--v-primary-base);
	}
}
.step-item {
	position: relative;
	display: flex;
	align-items: center;
	border: 1px solid #E6E6E6;
	box-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.0500);
	border-radius: 6px;
	padding: 10px 20px;
	height: 40px;
	transition: all .3s;

	.operateBtn {
		position: absolute;
		right: 10px;
		top: 0;
		bottom: 0;
	}
	&.hover-style{
		background: #F5F6FA;
		border: 1px solid #1A73E8;
	}
}

.step-item.complete .left {
	color: #999999;
}

.icon {
	display: flex;
	justify-content: center;
	align-items: center;
	width: 10px;
}

.circle {
	width: 5px;
	height: 5px;
	background: #1A73E8;
	border-radius: 50%;
}
</style>
