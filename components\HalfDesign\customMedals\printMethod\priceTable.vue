<template>
	<div class="priceTable" :class="stepData.styleName">
		<div class="priceList" :class="{ showBtn: showScrollBtn }">
			<div class="pricesWrap" v-show="priceList && Object.keys(priceList).length > 0">
				<div class="qty-item" v-if="index == 0" v-for="([key, item], index) in Object.entries(priceList)" :key="getRandomString()">
					<div class="price_head">
						{{ qtyText2 }}
					</div>
					<div class="price-item head-item" v-for="(citem, index) in item" :key="index">
						{{ citem.quantity }}
					</div>
				</div>
				<div class="qty-item" v-for="([key, item], index) in Object.entries(priceList)" :key="index">
					<div class="price_head" :class="{ lastHeadText: index == Object.keys(priceList).length - 1 }">{{ key }}</div>
					<div class="price-item" v-for="(citem, index) in item" :key="index">
						<div v-if="productInfo.discount">
							<CCYRate :price="citem.markupUnitPrice"></CCYRate>
						</div>
						<div v-else>
							<CCYRate :price="citem.unitPrice"></CCYRate>
						</div>
					</div>
				</div>
			</div>
			<div class="rightBox">
				<div class="right-btn" v-show="priceList && Object.keys(priceList).length > 0" @click="toRight">
					<b :class="{ 'icon-right': !cursor, 'icon-left': cursor }"></b>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import { addDragable, getRandomString } from "@/utils/utils";
import { Calculator } from "@/utils/decimal.js";
export default {
	inject: ["getUnitPriceStep", "getCustomPriceData", "getProductInfo"],
	props: {
		stepData: {
			type: Object,
			default: {},
		},
	},
	data() {
		return {
			selectIndex: -1,
			selectItem: null,
			showScrollBtn: false,
			cursor: false,
			priceList: {},
		};
	},
	computed: {
		productInfo() {
			return this.getProductInfo();
		},
		unitPriceStep() {
			return this.getUnitPriceStep();
		},
		langSemiCustom() {
			return this.$store.getters.lang?.semiCustom;
		},
		langCart() {
			return this.$store.getters.lang?.cart;
		},
		customPriceData() {
			return this.getCustomPriceData();
		},
		qtyPrice() {
			if (!this.selectItem) {
				return [];
			}
			try {
				return this.setPrice(this.productInfo.discount);
			} catch (e) {
				return [];
			}
		},
		qtyText() {
			let style = this.stepData.styleName,
				text;
			switch (style) {
				case "style4":
					text = this.langSemiCustom.qty;
					break;
				case "style5":
					text = this.langSemiCustom.quantity;
					break;
				default:
					text = this.langSemiCustom.qty;
			}
			return text;
		},
		qtyText2() {
			return this.langCart.size + " / " + this.langSemiCustom.quantity;
		},
		markupIncreasePrice() {
			return this.selectItem.markupIncreasePrice && JSON.parse(this.selectItem.markupIncreasePrice)||[];
		},
	},
	methods: {
		getRandomString,
		setPrice(discount = 0, sizeItem) {
			let increasePrice = JSON.parse(this.unitPriceStep.increasePrice),
				itemIncreasePrice = JSON.parse(this.selectItem.increasePrice),
				sizeIncreasePrice = JSON.parse(sizeItem.increasePrice);
			const multiBasicUnitPrice = parseFloat(this.customPriceData.multiBasicUnitPrice);
			if (this.selectItem.priceType === 5) {
				increasePrice.forEach((item) => {
					// 获取价格
					const unitPriceValue = parseFloat(item.unitPrice);
					const increase1Value = parseFloat(this.findPrice(item, itemIncreasePrice)?.unitPrice);
					const increase2Value = parseFloat(this.findPrice(item, sizeIncreasePrice)?.unitPrice);
					// 创建 Calculator 实例
					const unitPrice = Calculator.create(unitPriceValue);
					const increase1 = Calculator.create(increase1Value).toNumber();
					const increase2 = Calculator.create(increase2Value).toNumber();
					// 计算总价
					let price = unitPrice.add(increase1).add(increase2);
					let price2 = price.multiply(multiBasicUnitPrice).round(2);
					// 计算折扣
					let price3;
					if (discount !== 0) {
						const discountValue = Math.abs(1 - discount);
						price3 = price2.multiply(discountValue).round(2);
						item.discountPrice = +price3.toNumber().toFixed(2);
					}
					item.unitPrice = +price2.toNumber().toFixed(2);
				});
				let isShowPrice = increasePrice.filter((item) => item.isShow == 1 || item.isShow == null);
				this.$Bus.$emit("priceSection", increasePrice);
				return isShowPrice || [];
			} else if (this.selectItem.priceType === 1) {
				increasePrice.forEach((item) => {
					//单价
					let price = parseFloat(item.unitPrice) + this.selectItem.unitPrice;
					if (sizeItem) {
						price = parseFloat(item.unitPrice) + this.selectItem.unitPrice + sizeItem.unitPrice;
					}
					//计算价格分层
					let price2 = (price * this.customPriceData.multiBasicUnitPrice).toFixed(2);
					//计算折扣
					let price3 = Math.round(price2 * Math.abs(1 - discount) * 100) / 100;
					if (discount != 0) {
						item.discountPrice = price3;
						item.unitPrice = price2;
					} else {
						item.unitPrice = price2;
					}
				});
				let isShowPrice = increasePrice.filter((item) => item.isShow !== 0);
				this.$Bus.$emit("priceSection", increasePrice);
				return isShowPrice || [];
			}
		},
		savePrice(price, index) {
			if (index == 0) return "—";
			return (((this.qtyPrice[0].unitPrice - price) / this.qtyPrice[0].unitPrice) * 100).toFixed(0) + "%";
		},
		toRight() {
			let container = document.querySelector(".pricesWrap");
			let startX = container.scrollLeft;
			if (container.scrollWidth == container.scrollLeft + container.clientWidth && !this.cursor) {
				this.cursor = true;
				return;
			}
			if (container.scrollWidth == container.scrollLeft + container.clientWidth && this.cursor) {
				this.cursor = false;
				container.scrollTo({ top: 0, left: 0, behavior: "smooth" });
				return;
			}
			container.scrollTo(startX + 45, 0); // 横向 纵向
		},
		findPrice(item, itemIncreasePrice) {
			let len = itemIncreasePrice.length,
				newNum = parseInt(item.quantity),
				findItem = itemIncreasePrice[0];
			for (let i = 0; i < len; i++) {
				let item = itemIncreasePrice[i],
					nextItem = itemIncreasePrice[i + 1];
				if (newNum >= item.quantity && (nextItem ? newNum < nextItem.quantity : true)) {
					findItem = item;
					break;
				}
			}
			return findItem;
		},
		selectStep(item, index, status = false) {
			this.selectIndex = index;
			this.selectItem = item;
			this.$emit("selectStep", {
				type: this.stepData.attributeFlag,
				data: item,
				parent: this.stepData,
				id: this.stepData.id,
				firstSelect: status,
			});
			this.$nextTick(() => {
				let container = document.querySelector(".pricesWrap");
				if (container) {
					this.showScrollBtn = container.scrollWidth > container.clientWidth;
				}
			});
		},
		selectDefault(sizeParams) {
			if (this.stepData.productParamList.length) {
				this.selectStep(this.stepData.productParamList[0], 0, true);
			}
			if (sizeParams?.productParamList.length) {
				sizeParams.productParamList.forEach((item, index) => {
					let sizePrice = JSON.parse(item.markupIncreasePrice);
					// let price = this.setPrice(this.productInfo.discount, item);
					this.$set(this.priceList, `${item.valueName}`,  sizePrice);
				});
			}
			this.$nextTick(() => {
				let container = document.querySelector(".pricesWrap");
				if (container) {
					this.showScrollBtn = container.scrollWidth > container.clientWidth;
				}
			});
		},
	},
	mounted() {
		this.$Bus.$on("selectDefaultPrintStep", this.selectDefault);
		this.$Bus.$on("selectShowPrintStep", this.selectDefault);
		let container = document.querySelector(".pricesWrap");
		if (container) {
			addDragable(container, (scrollLeft) => {
				if (scrollLeft == 0) {
					this.cursor = true;
				} else {
					this.cursor = false;
				}
			});
		}
	},
	watch: {},
	beforeDestroy() {
		this.$Bus.$off("selectDefaultPrintStep", this.selectDefault);
		this.$Bus.$on("selectShowPrintStep", this.selectDefault);
	},
};
</script>

<style scoped lang="scss">
@import "~assets/css/half.scss";

.priceList {
	margin-top: 24px;
	position: relative;
	display: flex;

	@include respond-to(mb) {
		margin-top: 12px;

		.pricesWrap {
			.qty-item {
				.price_head {
					min-width: 120px;
				}
			}
		}
	}

	.rightBox {
		flex-shrink: 0;
		display: flex;
		align-items: center;
		column-gap: 2px;
		border: 1px solid #f5f5f5;
		border-left: none;
	}

	.right-btn {
		width: 24px;
		height: 100%;
		display: none;
		justify-content: center;
		align-items: center;

		&.cursor {
			cursor: no-drop;
		}

		b {
			font-size: 12px;
		}
	}
}

.priceList.showBtn {
	.right-btn {
		display: flex;
	}
}

.pricesWrap {
	position: relative;
	cursor: move;
	background: #ffffff;
	border: 1px solid #f5f5f5;
	border-bottom: none;
	overflow: scroll;
	scrollbar-width: none;

	.qty-item {
		display: flex;
		font-size: 16px;

		.price_head {
			width: fit-content;
			min-width: 140px;
			text-align: center;
			padding: 0.4em 0.6em;
			z-index: 1;
			background-color: #fff4ee;
			border-bottom: 1px solid #fff;

			&.lastHeadText {
				border-bottom: 1px solid #f5f5f5;
			}
		}

		.price-item {
			position: relative;
			width: fit-content;
			min-width: 90px;
			text-align: center;
			padding: 0.4em 0.6em;
			border-left: 1px solid #f5f5f5;
			border-bottom: 1px solid #f5f5f5;

			&.head-item {
				border-left: 1px solid #fff;
				background-color: #fff4ee;
			}
		}

		.arrowBox {
			color: red;

			.v-icon {
				font-size: 14px;
				color: red;
			}
		}

		.line {
			text-decoration: line-through;
			color: #bab6b6;
		}

		@include respond-to(mb) {
			font-size: 14px;
		}
	}
}
</style>
