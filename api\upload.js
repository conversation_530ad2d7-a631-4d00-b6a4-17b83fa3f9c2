import axios from 'axios';
// 创建axios实例
const request = axios.create({
	baseURL: process.env.baseUrl,
	// timeout: 10000
})

// 导入页面编辑日志
export function importPageRowDraftData(data) {
	return request({
		url: "/retailer/page/importPageRowDraftData",
		method: "post",
		data,
		headers: {
			'Content-Type': 'application/x-www-form-urlencoded',
			'token': $nuxt.$cookies.get(process.env.tokenKey)
        }
	});
}