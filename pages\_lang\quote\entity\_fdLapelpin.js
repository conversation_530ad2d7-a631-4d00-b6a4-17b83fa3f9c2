//导航类
export class navBannerModel {
	leftModel = {
		alt: "customize lapel pins",
		title: "customize lapel pins",
	};
	centerModel = {
		stepTotal: 6, //步骤总数
		label: "Step", //步骤标签
	};
	rightModel = {
		alt: "create your sales sheet",
		title: "create your sales sheet",
	};
}
//缓存数据
export class cacheModel {
	calculate = undefined;
}
//卡片类
export class cardModel {
	field = {
		title: "alias",
		desc: "unitConversion",
	};
	tabs = 0;
	list = [
		/* 	{ alias: "1/2 inch", desc: "12.70 mm" },
		{ alias: "3/4 inch", desc: "19.05 mm", labelText: "Hot", labelColor: "#FF0000" },
		{ alias: "1 inch", desc: "25.40 mm", labelText: "Hot" },
		{ alias: "1-1/4 inch", desc: "31.75 mm", labelText: "Hot" },
		{ alias: "1-1/2 inch", desc: "38.10 mm" },
		{ alias: "1-3/4 inch", desc: "44.45 mm" },
		{ alias: "2 inch ", desc: "50.80 mm" },
		{ alias: "2-1/4 inch", desc: "57.15 mm" },
		{ alias: "2-1/2 inch", desc: "63.50 mm" },
		{ alias: "2-3/4 inch", desc: "70.85 mm" },
		{ alias: "3 inch", desc: "76.20 mm" }, */
		// model
	];
}
//数量类
export class quantityModel {
	field = [
		{ key: "totalQuantity", value: "Quantity (PCS)" },
		{ key: "foundationUnitPrice", value: "Unit Price" },
		{ key: "totalPrice", value: "Total Price" },
	];
	//第一个对象
	initModel = {
		/* totalQuantity: undefined, //数量
		foundationUnitPrice: undefined, //单价
		totalPrice: undefined, //总价 */
	};
	list = [
		/* { totalQuantity: 50, foundationUnitPrice: 2.54 },
		{ totalQuantity: 100, foundationUnitPrice: 1.67, isHot: true },
		{ totalQuantity: 250, foundationUnitPrice: 1.28, isHot: true },
		{ totalQuantity: 500, foundationUnitPrice: 1.13, isHot: true },
		{ totalQuantity: 1000, foundationUnitPrice: 1.02 },
		{ totalQuantity: 3000, foundationUnitPrice: 0.76 },
		{ totalQuantity: 5000, foundationUnitPrice: 0.71 }, */
	];
}

//下拉列表
export class selectModel {
	currencyOptions = []; //国家
	options = [
		{
			value: "选项1",
			label: "黄金糕",
		},
		{
			value: "选项2",
			label: "双皮奶",
		},
		{
			value: "选项3",
			label: "蚵仔煎",
		},
		{
			value: "选项4",
			label: "龙须面",
		},
		{
			value: "选项5",
			label: "北京烤鸭",
		},
	];
}
//产品目录--框--复制品--用于判断原价
export class summaryModelOld {}

//产品目录--框
export class summaryModel {
	img = process.env.imgServer + "quoteManage/20240919/Soft_enamel_pins_20240919pK2kST.png";
	//获取遮罩标签 getMaskName(item) 同步要改
	//tableListBind(item, index, domId) { 同步要改
	list = [
		{ label: "Size:", value: "", object: {}, parentObject: {} },
		{ label: "Plating/Finish (pc):", value: "", object: {}, parentObject: {} },
		{ label: "Amount of Colors:", value: "", object: {}, parentObject: {} },
		{ label: "Backing Attachment (pc):", value: "", object: {}, parentObject: {} },
		{ label: "Special Options:", value: "", quantity: 13, object: {}, parentObject: {} },
		{ label: "Pin Back Stamp (pc):", value: "", object: {}, parentObject: {} },
		{ label: "Pin Packaging (pc):", value: "", object: {}, parentObject: {} },
		{ label: "Select Packaging-fd:", value: "", object: {}, parentObject: {} },
		/* 	{ label: "Select Packaging:", value: "", object: {}, parentObject: {} }, */
		/* { label: "Size:", value: "1/2 inches", object: {}, parentObject: {} },
		{ label: "Plating/Finish (pc):", value: "Shiny Gold Free", object: {}, parentObject: {} },
		{ label: "Amount of Colors:", value: "6-8  Colors", valueBottom: "+$0.48 each (C)", object: {}, parentObject: {} },
		{ label: "Backing Attachment (pc):", value: "Two Butterfly Clutches", valueBottom: "+$0.10 each (C)", object: {}, parentObject: {} },
		{ label: "Special Options:", value: "Glitter Enamel", valueBottom: "+$0.15 each (C)", quantity: 13, object: {}, parentObject: {} },
		{ label: "Pin Back Stamp (pc):", value: "Raised Backdie", valueBottom: "Set up fee: $32(c) /each order", object: {}, parentObject: {} },
		{ label: "Pin Packaging (pc):", value: "Poly Bag Free", object: {}, parentObject: {} }, */
	];
	desc = "Economy Soft Enamel Pins";
	//总价格
	moneyModel = {
		currency: "USD", //下拉列表
		quantity: 0.0, //数量
		quantityColor: false, //数量颜色高亮
		unitPriceTotal: 0.0, //单价总数
		unitPrice: 0.0, //单价
		moldFee: 0.0, //磨具费
		subtotal: 0.0, //总价
		unit: "$", //国家单位
	};
}

//弹窗总价格

//带图文的产品目录
export class summaryImgModel {
	list = [
		{
			img: process.env.imgServer + "quoteManage/20240923/Award_shape_125_inches_20240923t5jfWe.png",
			hideFree: true,
			title: "Flag Wave 1 inch",
		},
		{ img: process.env.imgServer + "quoteManage/20240923/Single_Flag_1_inch_20240923f6yhrp.png", hideFree: true, title: "Single Flag 1 inch" },
		{ img: process.env.imgServer + "quoteManage/20240923/Cross_Flag_125_inches_20240923zJxYAe.png", hideFree: true, title: "Cross Flag 1.25 inches" },

		{ img: process.env.imgServer + "quoteManage/20240923/Wave_Edge__20240923BxwiFN.png", hideFree: true, title: "Flag Wave 1 inch" },
		{ img: process.env.imgServer + "quoteManage/20240923/Sunburst_Round_1_inch_20240923WHK4sR.png", hideFree: true, title: "Flag Wave 1 inch" },
		{ img: process.env.imgServer + "quoteManage/20240923/Square_075_inch_20240923FKcK3A.png", hideFree: true, title: "Flag Wave 1 inch" },
		{ img: process.env.imgServer + "quoteManage/20240923/Square_1_inch_20240923K3w3N6.png", hideFree: true, title: "Flag Wave 1 inch" },
		{ img: process.env.imgServer + "quoteManage/20240923/Square_125_inches_20240923WWtRNP.png", hideFree: true, title: "Flag Wave 1 inch" },
		{ img: process.env.imgServer + "quoteManage/20240923/Rectangle_125x1_inches_20240923air5Py.png", hideFree: true, title: "Flag Wave 1 inch" },
		{ img: process.env.imgServer + "quoteManage/20240923/Rectangle_1x075_inches_20240923KJfjFn.png", hideFree: true, title: "Flag Wave 1 inch" },
		{ img: process.env.imgServer + "quoteManage/20240923/Round_075_inch_20240923TGHQfa.png", hideFree: true, title: "Flag Wave 1 inch" },
		{ img: process.env.imgServer + "quoteManage/20240923/Round_1_inch_202409235JeFAn.png", hideFree: true, title: "Flag Wave 1 inch" },
		{ img: process.env.imgServer + "quoteManage/20240923/Oval_125_inches_20240923nCTjfe.png", hideFree: true, title: "Flag Wave 1 inch" },
		{ img: process.env.imgServer + "quoteManage/20240923/Shield_1_inch_20240923CxJskc.png", hideFree: true, title: "Flag Wave 1 inch" },
		{ img: process.env.imgServer + "quoteManage/20240923/Shield_125_inch_20240923ZefwW8.png", hideFree: true, title: "Flag Wave 1 inch" },
		{ img: process.env.imgServer + "quoteManage/20240923/Round_075_inch_20240923TGHQfa.png", hideFree: true, title: "Flag Wave 1 inch" },
		{ img: process.env.imgServer + "quoteManage/20240923/Round_1_inch_202409235JeFAn.png", hideFree: true, title: "Flag Wave 1 inch" },
		{ img: process.env.imgServer + "quoteManage/20240923/Oval_125_inches_20240923nCTjfe.png", hideFree: true, title: "Flag Wave 1 inch" },
		{ img: process.env.imgServer + "quoteManage/20240923/Shield_1_inch_20240923CxJskc.png", hideFree: true, title: "Flag Wave 1 inch" },
		{ img: process.env.imgServer + "quoteManage/20240923/Shield_125_inch_20240923ZefwW8.png", hideFree: true, title: "Flag Wave 1 inch" },
		{ img: process.env.imgServer + "quoteManage/20240923/Round_075_inch_20240923TGHQfa.png", hideFree: true, title: "Flag Wave 1 inch" },
		{ img: process.env.imgServer + "quoteManage/20240923/Round_1_inch_202409235JeFAn.png", hideFree: true, title: "Flag Wave 1 inch" },
		{ img: process.env.imgServer + "quoteManage/20240923/Oval_125_inches_20240923nCTjfe.png", hideFree: true, title: "Flag Wave 1 inch" },
		{ img: process.env.imgServer + "quoteManage/20240923/Shield_1_inch_20240923CxJskc.png", hideFree: true, title: "Flag Wave 1 inch" },
		{ img: process.env.imgServer + "quoteManage/20240923/Shield_125_inch_20240923ZefwW8.png", hideFree: true, title: "Flag Wave 1 inch" },
		{ img: process.env.imgServer + "quoteManage/20240923/Round_075_inch_20240923TGHQfa.png", hideFree: true, title: "Flag Wave 1 inch" },
		{ img: process.env.imgServer + "quoteManage/20240923/Round_1_inch_202409235JeFAn.png", hideFree: true, title: "Flag Wave 1 inch" },
		{ img: process.env.imgServer + "quoteManage/20240923/Oval_125_inches_20240923nCTjfe.png", hideFree: true, title: "Flag Wave 1 inch" },
		{ img: process.env.imgServer + "quoteManage/20240923/Shield_1_inch_20240923CxJskc.png", hideFree: true, title: "Flag Wave 1 inch" },
		{ img: process.env.imgServer + "quoteManage/20240923/Shield_125_inch_20240923ZefwW8.png", hideFree: true, title: "Flag Wave 1 inch" },
	];
	//stepTitle = "Step 2："; //步骤标题
	//title = "Pin Plating / Finish Options"; //标题
	//desc = "Select from a variety of plating options."; //描述
	//	isMove = true; //是否显示更多栏.
	css = {
		rowColumns: 5, //一行几列
	};
	mobileCss = {
		rowColumns: 3, //一行几列
	};
}

//图文列表
export class tableListModel {
	tableList = [
		{
			list: [],
			stepTitle: "Step 1：", //步骤标题
			title: "Select Size & Quantity", //标题
			desc: "All of our size options are priced based on the longest dimension of the lapel pin.", //描述
			// 	isMove: true, //是否显示更多栏.
			//isCarousel: true, //是否开启轮播
			css: {
				rowColumns: 5, //一行几列
				liStyle: {
					//border: "1px solid #e4e4e4",
					//background: "#e4e4e4",
					//borderRadius: "8px 8px 0px 0px",
					margin: "0 12px 10px 0",
				},
				bottomDivStyle: {
					//background: "#e4e4e4",
				},
				topImgImgStyle: {
					border: "1px solid #eaeaea",
					borderRadius: "6px",
					//borderRadius: "8px 8px 0px 0px",
				},
				bottomDivRowOneStyle: {
					fontSize: "14px",
				},
			},
			mobileCss: {
				rowColumns: 3, //一行几列
			},
		},
		{
			list: [
				/*{
					img: process.env.imgServer + "quoteManage/20240918/shiny_gold_20240918xEHTmj.png",
					isHot: true,
					alias: "Shiny Gold",
				},
				 	{
					img: process.env.imgServer + "quoteManage/20240918/silver_plating_20240918zPKCDi.png",
					isHot: true,
					alias: "Shiny Silver",
				},
				{
					img: process.env.imgServer + "quoteManage/20240918/antique_gold_20240918NmQHeH.png",
					alias: "Antique Gold",
				},
				{
					img: process.env.imgServer + "quoteManage/20240918/antique_silver_20240918XDzSnK.png",
					alias: "Antique Silver",
				},
				{
					img: process.env.imgServer + "quoteManage/20240918/black_nickel_2024091883FTFt.png",
					isHot: true,
					alias: "Black Nickel",
				}, */
			],
			stepTitle: "Step 2：", //步骤标题
			title: "Pin Plating / Finish Options", //标题
			desc: "Select from a variety of plating options.", //描述
			isMove: true, //是否显示更多栏.
			isCarousel: true, //是否开启轮播
			css: {
				rowColumns: 5, //一行几列
			},
			mobileCss: {
				rowColumns: 3, //一行几列
			},
		},
		{
			list: [
				/* {
					img: process.env.imgServer + "quoteManage/20240918/shiny_gold_20240918xEHTmj.png",
					isHot: true,
					title: "5 colors or less",
				},
				{
					img: process.env.imgServer + "quoteManage/20240918/silver_plating_20240918zPKCDi.png",
					isHot: true,
					title: "6-8  Colors ",
				},
				{
					img: process.env.imgServer + "quoteManage/20240918/antique_gold_20240918NmQHeH.png",
					title: "9-12 Colors ",
				},
				{
					img: process.env.imgServer + "quoteManage/20240918/antique_silver_20240918XDzSnK.png",
					title: "13-20 Colors ",
				},
				{
					img: process.env.imgServer + "quoteManage/20240918/black_nickel_2024091883FTFt.png",
					title: "More than 20  Colors ",
				}, */
			],
			stepTitle: "Step 3：",
			title: "Select Amount of Colors",
			desc: "Select amount of colors of enamel.",
			css: {
				rowColumns: 5, //一行几列
			},
			mobileCss: {
				rowColumns: 3, //一行几列
			},
		},
		{
			list: [
				/* {
					img: process.env.imgServer + "quoteManage/20240923/Butterfly_Clutch_20240923HAfkHH.png",
					title: "Single Butterfly",
				},
				{
					img: process.env.imgServer + "quoteManage/20240923/Rubber_Clutch_202409237fYG64.png",
					title: "Single Rubber Clutch",
				},
				{
					img: process.env.imgServer + "quoteManage/20240923/Double_Butterfly_Clutch_20240923aDcF2R.png",
					title: "Two Rubber Clutches",
				},
				{
					img: process.env.imgServer + "quoteManage/20240923/Double_Rubber_Clutch_20240923bGb3b4.png",
					title: "Two Butterfly Clutches",
				},
				{
					img: process.env.imgServer + "quoteManage/20240923/Round_Magnet_20240923sKKWtQ.png",
					title: "Single Magnetic Back",
				},
				{
					img: process.env.imgServer + "quoteManage/20240923/Round_Magnet_20240923sKKWtQ.png",
					title: "Two Magnetic Back",
				}, */
			],
			stepTitle: "Step 4：",
			title: "Select Pin Backing Attachment",
			desc: "Select from a variety of plating options.",
			isMove: true, //是否显示更多栏.
			isCarousel: true, //是否开启轮播
			css: {
				rowColumns: 6, //一行几列
			},
			mobileCss: {
				rowColumns: 3, //一行几列
			},
		},
		{
			list: [
				/* {
					img: process.env.imgServer + "quoteManage/20240923/No_need_upgrades_20240923S5NszN.png",
					isHot: true,
					title: "No Upgrade",
				},
				{
					img: process.env.imgServer + "quoteManage/20240923/Glitter_202409233P5PT2.png",
					isHot: true,
					title: "Glitter Enamel",
					wenhao: "正在建设中",
					inputModel: {
						placeholder: "Enter Glitter Color Quantity",
						type: "number",
					},
				},

				{
					img: process.env.imgServer + "quoteManage/20240923/Rhinestone_20240923HXi2n5.png",
					title: "Rhinestone",
					wenhao: "正在建设中",
					inputModel: {
						placeholder: "Enter Rhinestone Quantity",
						type: "number",
					},
				},
				{ img: process.env.imgServer + "quoteManage/20240923/Exopy_Dome_2024092367dkJX.png", title: "Epoxy coating", wenhao: "正在建设中" }, */
			],
			stepTitle: "Step 5：",
			title: "Add-ons & Upgrades",
			desc: "Select from a variety of plating options.",
			isMove: true, //是否显示更多栏.
			isCarousel: true, //是否开启轮播
			/* 	inputModel: {
				placeholder: "Enter Glitter Color Quantity",
				type: "number",
			}, */
			css: {
				rowColumns: 4, //一行几列
			},
			mobileCss: {
				rowColumns: 2, //一行几列
			},
		},
		{
			list: [
				/* {
					img: process.env.imgServer + "quoteManage/20240923/No_need_upgrades_20240923S5NszN.png",
					isHot: true,
					title: "Standard Back",
					wenhao: "正在建设中",
				},
				{ img: process.env.imgServer + "quoteManage/20240923/No_need_upgrades_20240923S5NszN.png", isHot: true, title: "Raised Backdie" },
				{ img: process.env.imgServer + "quoteManage/20240923/No_need_upgrades_20240923S5NszN.png", isHot: true, title: "Laser Engraving" },
				{ img: process.env.imgServer + "quoteManage/20240923/No_need_upgrades_20240923S5NszN.png", title: "Deep Engraving " }, */
			],
			stepTitle: "Step 6：",
			title: "Select Pin Back Stamp",
			desc: "Select from a variety of plating options.",

			css: {
				rowColumns: 4, //一行几列
			},
			mobileCss: {
				rowColumns: 2, //一行几列
			},
		},
		{
			list: [
				/* {
					img: process.env.imgServer + "quoteManage/20240923/poly_bag_20240923CamsZc.png",
					isHot: true,
					title: "Poly Bag",
				},
				{
					img: process.env.imgServer + "quoteManage/20240923/plastic_case_20240923KDbykB.png",
					isHot: true,
					title: "Plastic Box ",
				},

				{ img: process.env.imgServer + "quoteManage/20240923/velour_pouch_20240923yYWkiZ.png", title: "Velour Pouch" },
				{ img: process.env.imgServer + "quoteManage/20240923/Velour_Case_20240923EGndCj.png", title: "Velour Case" },
				{ img: process.env.imgServer + "quoteManage/20240923/paper_card_20240923xxZd74.png", title: "Custom Printed Paper Card" }, */
			],
			stepTitle: "Step 7：",
			title: "Select Pin Packaging",
			desc: "Select from a variety of plating options.",
			/*
			isMove: true, //是否显示更多栏.
			isCarousel: true, //是否开启轮播
			*/
			css: {
				rowColumns: 5, //一行几列
			},
			mobileCss: {
				rowColumns: 2, //一行几列
			},
		},
		{
			list: [],
			stepTitle: "Step 8：", //步骤标题
			title: "Pin Plating / Finish Options", //标题
			desc: "Select from a variety of plating options.", //描述
			isMove: true, //是否显示更多栏.
			isCarousel: true, //是否开启轮播
			css: {
				rowColumns: 5, //一行几列
			},
			mobileCss: {
				rowColumns: 3, //一行几列
			},
		},
		{
			list: [],
			stepTitle: "", //步骤标题
			title: "", //标题
			desc: "", //描述
			isMove: true, //是否显示更多栏.
			isCarousel: true, //是否开启轮播
			css: {
				rowColumns: 5, //一行几列
			},
			mobileCss: {
				rowColumns: 3, //一行几列
			},
		},
		{
			list: [],
			stepTitle: "", //步骤标题
			title: "", //标题
			desc: "", //描述
			isMove: true, //是否显示更多栏.
			isCarousel: true, //是否开启轮播
			css: {
				rowColumns: 5, //一行几列
			},
			mobileCss: {
				rowColumns: 3, //一行几列
			},
		},
		{
			list: [],
			stepTitle: "", //步骤标题
			title: "", //标题
			desc: "", //描述
			isMove: true, //是否显示更多栏.
			isCarousel: true, //是否开启轮播
			css: {
				rowColumns: 5, //一行几列
			},
			mobileCss: {
				rowColumns: 3, //一行几列
			},
		},
		{
			list: [],
			stepTitle: "", //步骤标题
			title: "", //标题
			desc: "", //描述
			isMove: true, //是否显示更多栏.
			isCarousel: true, //是否开启轮播
			css: {
				rowColumns: 5, //一行几列
			},
			mobileCss: {
				rowColumns: 3, //一行几列
			},
		},
		{
			list: [],
			stepTitle: "", //步骤标题
			title: "", //标题
			desc: "", //描述
			isMove: true, //是否显示更多栏.
			isCarousel: true, //是否开启轮播
			css: {
				rowColumns: 5, //一行几列
			},
			mobileCss: {
				rowColumns: 3, //一行几列
			},
		},
		{
			list: [],
			stepTitle: "", //步骤标题
			title: "", //标题
			desc: "", //描述
			isMove: true, //是否显示更多栏.
			isCarousel: true, //是否开启轮播
			css: {
				rowColumns: 5, //一行几列
			},
			mobileCss: {
				rowColumns: 3, //一行几列
			},
		},
	];
}

//给价格组件赋值
export class customizeModel {
	moldFee = 0;
	type = "fd";
}
//基础类
export class baseModel {
	base = {
		pid: 25,
		//proId: 9, //上线记得去掉. 系统底层自带有
		cateId: 435, // 32||图文     28, 435
		alert: 1,
	};
	loadingOk = false; //是否加载完成
	loading = false; //进度条
	//所有步骤的遍历
	modelList = [{}, {}];
	//卡片选中数据源
	sizeSelectedModel = {};
	//卡片选中下标
	sizeSelectedIndex = undefined;

	//列表页面传给详情页面的数据 特殊处理
	data = {};

	//上一个页面数据传递
	windowParent = {
		//cateId,
		//cateChildId
	};
	constantQuery = "fd-table-detail"; //页面数据传递标识
	//遮罩
	maskName = "";
	//遮罩瞄点集合
	maskNameKeyArr = [];

	//核心提交 paramType
	calculateModel = {
		paramIdList: [], //normal
		discount: undefined, //discount
		upgradesQtyDTO: [], //QUANTITY
		packingIdList: [], //packingIdList
		sizeId: undefined, //size
	};
	//清空
	calculateModelNew = {
		paramIdList: [], //normal
		discount: undefined, //discount
		upgradesQtyDTO: [], //QUANTITY
		packingIdList: [], //packingIdList
		sizeId: undefined, //size
	};

	priceTextCss = {
		normaltext: "font-size: 14px;",
	};
	unit = undefined; //全局金钱单位
	rate = undefined; //倍率
	code = undefined; //货币符号
	currencyId = undefined; //国家id
	model = undefined; //calculate 接口请求参数

	defaultImg = "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20221209/203409bh7TxJHM.png";
}
//询盘类
export class inquiryModel {
	showInquiry = false;
	pinsForm = {
		email: "",
		firstName: "",
		lastName: "",
		telephone: "",
		areaCode: "",
		subject: "",
		groupNumber: [
			{
				id: 1,
				name: "ASI",
				value: "",
			},
			{
				id: 2,
				name: "SAGE",
				value: "",
			},
			{
				id: 3,
				name: "PPAI",
				value: "",
			},
			{
				id: 4,
				name: "PPPC",
				value: "",
			},
			{
				id: 5,
				name: "PSI",
				value: "",
			},
			{
				id: 6,
				name: "APPA",
				value: "",
			},
			{
				id: 7,
				name: "DC",
				value: "",
			},
		],
		remark: "",
		isSample: 0,
	};
}
