<template>
	<div @mouseenter="handleMouseEnter" @mouseleave="handleMouseLeave" style="width: 100%; height: 100%" class="videoBox showPlayBtn">
		<video ref="videoPlayer" class="video-js vjs-theme-forest"></video>
	</div>
</template>

<script>
export default {
	props: {
		options: {
			type: Object,
			default: {},
		},
		disabledMouse: {
			type: Boolean,
			default: false,
		},
		height: {
			type: String,
			default: "100%",
		},
	},
	data() {
		return {
			player: "",
			playPromise: "",
		};
	},
	methods: {
		handleMouseEnter() {
			if (this.disabledMouse) {
				return false;
			}
			if (this.$store.state.device === "mb") {
				return false;
			}
			if (this.player) {
				this.playPromise = this.player.play();
			}
		},
		handleMouseLeave() {
			if (this.disabledMouse) {
				return false;
			}
			if (this.player) {
				if (this.playPromise) {
					this.playPromise
						.then((_) => {
							this.player.pause();
						})
						.catch((error) => {
							console.log(error);
						});
				}
			}
		},
	},
	mounted() {
		this.player = videojs(
			this.$refs["videoPlayer"],
			Object.assign({}, this.options, {
				playsinline: true,
			})
		);
		this.$refs.videoPlayer.addEventListener("loadedmetadata", () => {
			this.$refs.videoPlayer.addEventListener("timeupdate", () => {}, false);
		});
	},
	beforeDestroy() {
		if (this.player) {
			this.player.dispose();
		}
	},
};
</script>

<style scoped lang="scss">
img{
	position: absolute;
    top: 0;
	border-radius: 5px;
}
.video-js {
	overflow: hidden;
	cursor: pointer;
}

.video-js ::v-deep {
	background-color: transparent;
	.vjs-big-play-button {
		left: 50%;
		margin-left: -1.5em;
		margin-top: -0.81666em;
		top: 50%;
		@media screen and (max-width: 767px) {
			display: none !important;
		}
	}

	video {
		object-fit: contain;
	}

	.vjs-poster {
		background-size: cover;
	}
}

.showPlayBtn {
	.video-js ::v-deep .vjs-big-play-button {
		display: block;
		bottom: 10px;
		left: auto;
		top: auto;
		right: 10px;
		font-size: 27px;
		width: 39px;
		height: 39px;
		background-color: rgba(255, 255, 255, 0.6);
		border-radius: 50%;
		border: none;
		color: rgba(0, 0, 0, 0.6);
	}

	.vjs-has-started.vjs-paused ::v-deep .vjs-big-play-button {
		display: none;
	}

	.vjs-has-started.vjs-playing ::v-deep .vjs-big-play-button {
		display: none;
	}
}

.vjs-has-started.vjs-paused ::v-deep .vjs-poster {
	display: none;
}

.vjs-has-started.vjs-playing ::v-deep .vjs-poster {
	display: none;
}
@media screen and (max-width: 750px) {
	.videoBox {
		position: relative;
	}
	.videoBox::after {
		content: "";
		position: absolute;
		left: 0;
		top: 0;
		right: 0;
		bottom: 0;
	}
}
</style>
