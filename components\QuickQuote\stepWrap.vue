<template>
	<div>
		<template v-for="(item, index) in generalData">
			<size :id="item.customStepName" :key="item.id" v-if="item.customStepName === 'size'" class="step-size" :class="{ mask: stepName === item.customStepName }" :item="item" :hasTitleIcon="true" @clickIcon="showSizeDialog" @selectQuoteParams="selectQuoteParams" @clickNext="clickNext"></size>
			<qty :id="item.customStepName" :key="item.id" v-if="item.customStepName === 'qty'" class="step-qty" :class="{ mask: stepName === item.customStepName }" :item="item" @selectQtyList="selectQtyList" @updatePrice="updatePrice" @selectQty="selectQty"></qty>
			<color :id="item.customStepName" :key="item.id" v-if="item.customStepName === 'color'" class="step-color" :class="{ mask: stepName === item.customStepName }" :item="item" @selectQuoteParams="selectQuoteParams" @clickNext="clickNext"></color>
			<attachment :id="item.customStepName" :key="item.id" v-if="item.customStepName === 'attachment'" :class="[{ mask: stepName === item.customStepName},getClass(item)]" :item="item" @selectQuoteParams="selectQuoteParams"></attachment>
			<ribbon :id="item.customStepName" :key="item.id" v-if="item.customStepName === 'ribbon'" class="step-ribbon" :class="{ mask: stepName === item.customStepName }" :item="item" @selectQuoteParams="selectQuoteParams"></ribbon>
			<upload :id="item.customStepName" :key="item.id" v-if="item.customStepName === 'upload'" class="step-upload" :class="{ mask: stepName === item.customStepName }" :item="item" @uploadPic="uploadPic" @clickNext="clickNext" @setIsLater="setIsLater"></upload>
			<discount :id="item.customStepName" :key="item.id" v-if="item.customStepName === 'discount'" class="step-time" :class="{ mask: stepName === item.customStepName }" :item="item" @selectQuoteParams="selectQuoteParams"></discount>
		</template>
	</div>
</template>

<script>
import Step from "~/components/QuickQuote/Step.vue";
import size from "~/components/QuickQuote/Item/size.vue";
import Qty from "~/components/QuickQuote/Item/qty.vue";
import Color from "~/components/QuickQuote/Item/color.vue";
import Attachment from "~/components/QuickQuote/Item/attachment.vue";
import Ribbon from "~/components/QuickQuote/Item/ribbon.vue";
import Upload from "~/components/QuickQuote/Item/upload.vue";
import Discount from "~/components/QuickQuote/Item/discount.vue";

export default {
	props: ["generalData","stepName"],
	components: { Discount, Upload, Ribbon, Attachment, Color, Qty, Step, size },
    methods:{
        showSizeDialog(data){
            this.$emit('showSizeDialog',data)
        },
        selectQuoteParams(data){
            this.$emit('selectQuoteParams',data)
        },
        clickNext(data){
            this.$emit('clickNext',data)
        },
        selectQtyList(ind){
            this.$emit('selectQtyList',ind)
        },
        updatePrice(){
            this.$emit('updatePrice')
        },
        selectQty(){
            this.$emit('selectQty')
        },
        uploadPic(files){
            this.$emit('uploadPic',files)
        },
        setIsLater(bool){
            this.$emit('setIsLater',bool)
        },
        getClass(item){
            if(item.paramName==='Patch Backing' || item.paramName==='Patch Border' || item.paramName==='Color'){
                return "step-attachment-1"
            }
            return "step-attachment"
        }
    }
};
</script>

<style scoped lang="scss"></style>
