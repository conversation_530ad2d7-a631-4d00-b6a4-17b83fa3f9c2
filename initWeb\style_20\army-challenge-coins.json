[{"id": "armyChallengeCoins_01", "theme": 20, "name": "Card", "column": 3, "style": {"background-color": "#f3f4f5"}, "titleStyle": {"font-size": "2.25rem", "line-height": "normal", "margin-bottom": "1.25rem"}, "subTitleStyle": {"margin-top": "1em", "margin-bottom": "1em", "font-size": "1rem", "width": "60%", "margin-left": "auto", "margin-right": "auto"}, "boxStyle": {"gap": 0}, "cardStyle": {"width": "33.333333%", "flex-direction": "column", "align-items": "flex-start", "padding-top": "1.25rem", "padding-bottom": "1.5625rem", "padding-left": "5.625rem", "background-image": "url(https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20220628/ueditor/16/step-2.png)", "background-size": "100% 100%", "background-repeat": "no-repeat"}, "cardImgStyle": {"width": "unset", "height": "10.6875rem"}, "cardContentStyle": {"margin-top": "1.25rem", "flex-direction": "row", "justify-content": "space-between", "flex-wrap": "wrap", "gap": 0, "position": "relative", "width": "70%"}, "cardTitleStyle": {"order": 1, "font-size": "1.5rem", "line-height": "1.666666em", "margin-left": "2em"}, "cardSubTitleStyle": {"order": 2}, "cardTextStyle": {"width": "2.1875rem", "height": "2.5rem", "background-color": "#508AC3", "transform": "rotate(-30deg)", "border-radius": "0.8125rem", "position": "absolute", "top": 0, "left": 0, "z-index": 0, "opacity": 1}, "cardPriceStyle": {"position": "absolute", "top": 0, "left": 0, "width": "2.1875rem", "line-height": "1.666666em", "text-align": "center", "color": "#ffffff", "font-size": "1.5rem", "font-weight": "bold", "opacity": 1, "z-index": 1}, "boxBtnStyle": {"margin-top": "1.25rem"}, "btnStyle": {"background-color": "#0066cc", "border-radius": "0.625rem"}, "outer": [{"title": {"value": "Order Best Army Challenge Coins As Easy As 1.2.3."}, "subTitle": {"value": "Worrying if army challenge coins custom don't match your idea or artwork? At GS-JJ, our professional designers will help you with authentic looking artwork for your approval."}, "button": {"value": "Order Army Challenge Coins", "title": "Order Army Challenge Coins", "url": "/design/challenge-coins?cid=7", "target": "_self"}}], "list": [{"style": {"background-image": "url(https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20220628/ueditor/16/step-1.png)", "flex-direction": "column-reverse"}, "contentStyle": {"margin-top": 0}, "subTitleStyle": {"margin-bottom": "0.5em"}, "img": {"value": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20230919/ueditor/97/an-idea-for-challenge-coins.png", "alt": "Free Design For Coast Guard Challenge Coins"}, "title": {"value": "Free Design"}, "subTitle": {"value": "Gather your elements, files and ideas and send them to us via a quote request."}, "text": {"value": " "}, "price": {"value": "01"}}, {"img": {"value": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20230919/ueditor/97/free-artwork-for-army-challenge-coins.png", "alt": "Free Artwork For Coast Guard Challenge Coins"}, "title": {"value": "Free Artwork"}, "subTitle": {"value": "Within 12 to 24 hours you will get a quote along with a digital proof and real coins effect."}, "text": {"value": " "}, "price": {"value": "02"}}, {"style": {"background-image": "url(https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20220628/ueditor/16/step-3.png)", "flex-direction": "column-reverse"}, "contentStyle": {"margin-top": 0}, "subTitleStyle": {"margin-bottom": "0.5em"}, "img": {"value": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20230919/ueditor/97/free-real-effect-of-coins.png", "alt": "Final Product For Coast Guard Challenge Coins"}, "title": {"value": "Free Real Effect"}, "subTitle": {"value": "Once approved, we produce it and ship it as soon as possible. Your satisfaction is our priority."}, "text": {"value": " "}, "price": {"value": "03"}}]}, {"id": "armyChallengeCoins_02", "theme": "20", "name": "Card", "manualPlay": true, "mousePlay": true, "style": {"background-color": "#f3f4f5"}, "titleStyle": {"font-size": "2.25rem", "margin-bottom": "0.9375rem"}, "tabBoxStyle": {"padding-top": "0.625rem", "padding-left": "2.25rem", "padding-right": "2.25rem", "background-color": "#ffffff", "overflow": "hidden", "justify-content": "space-between", "gap": "4rem"}, "tabStyle": {"min-width": "unset", "height": "unset", "padding-left": "0.625rem", "padding-right": "0.625rem", "padding-top": "0", "padding-bottom": "0.3125rem", "line-height": "1.2em", "font-size": "1.25rem", "font-weight": "normal", "border-bottom": "0.1875rem solid transparent"}, "tabSeleStyle": {"font-weight": "bold", "border-radius": "0rem", "color": "#0066CB", "border-bottom": "0.1875rem solid #0066CB", "background-color": "transparent"}, "manualBtnStyle": {"position": "absolute", "bottom": 0, "font-size": "2.5rem", "right": "0.625rem", "top": "unset", "left": "unset", "transform": "none"}, "btnStyle": {"padding-top": 0, "padding-bottom": 0, "line-height": "2.5rem", "background-color": "#0066CB", "border-radius": "0.375rem"}, "column": 5, "outer": [{"title": {"value": "Custom Army Challenge Coins Options"}, "subTitle": {"value": "Do you want to know more about army challenge coins options? The following are our electroplating and edge options. If you want to know more, <br> please consult our customer service."}, "tabList": [{"value": "Edge Options", "column": 5}, {"value": "Plating Options", "column": 6}, {"value": "Packaging Options", "column": 4}], "button": {"value": "Create Custom Army Challenge Coins Now", "title": "Create Custom Army Challenge Coins Now", "url": "/design/challenge-coins?cid=7", "target": "_self"}}], "cardStyle": {"flex-direction": "column"}, "cardTitleStyle": {"text-align": "center", "font-size": "1rem", "color": "#333333", "line-height": "2.5rem", "font-weight": "normal"}, "list": [{"videoBoxStyle": {"border-radius": "0.625rem", "border": "0.0625rem solid #ccc"}, "tab": 0, "video": {"value": "https://static-oss.gs-souvenir.com/web/quoteManage/20230710/202307107JTb8mTw.mp4?id=5", "poster": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20230709/ueditor/95/standard-edge.jpg", "alt": "Standard Edge"}, "title": {"value": "Standard Edge"}}, {"videoBoxStyle": {"border-radius": "0.625rem", "border": "0.0625rem solid #ccc"}, "tab": 0, "video": {"value": "https://static-oss.gs-souvenir.com/web/quoteManage/20230710/202307106NpkjQZF.mp4?id=5", "poster": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20230709/ueditor/95/rope-edge.jpg", "alt": "Rope Edge"}, "title": {"value": "Rope Edge"}}, {"videoBoxStyle": {"border-radius": "0.625rem", "border": "0.0625rem solid #ccc"}, "tab": 0, "video": {"value": "https://static-oss.gs-souvenir.com/web/quoteManage/20241014/Bike_Chain_Edge_20482iway5.mp4", "poster": "https://static-oss.gs-souvenir.com/web/quoteManage/20241014/Bike_Chain_Edge_2048Jrs8aw.png", "alt": "Bike Chain Edge"}, "title": {"value": "Bike Chain Edge"}}, {"videoBoxStyle": {"border-radius": "0.625rem", "border": "0.0625rem solid #ccc"}, "tab": 0, "video": {"value": "https://static-oss.gs-souvenir.com/web/quoteManage/20241014/Chain_Edge_20482FSES7.mp4", "poster": "https://static-oss.gs-souvenir.com/web/quoteManage/20241014/Chain_Edge_20482KfGfM.png", "alt": "Chain Edge"}, "title": {"value": "Chain Edge"}}, {"videoBoxStyle": {"border-radius": "0.625rem", "border": "0.0625rem solid #ccc"}, "tab": 0, "video": {"value": "https://static-oss.gs-souvenir.com/web/quoteManage/20241014/Heavy_Chain_Edge_2048acriXW.mp4", "poster": "https://static-oss.gs-souvenir.com/web/quoteManage/20241014/Heavy_Chain_Edge_2048EhMK3s.png", "alt": "Heavy Chain Edge"}, "title": {"value": "Heavy Chain Edge"}}, {"videoBoxStyle": {"border-radius": "0.625rem", "border": "0.0625rem solid #ccc"}, "tab": 0, "video": {"value": "https://static-oss.gs-souvenir.com/web/quoteManage/20230710/20230710f5ZxYdEm.mp4?id=5", "poster": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20230709/ueditor/95/cross-cut-edge.jpg", "alt": "Cross-cut Edge"}, "title": {"value": "Cross-cut Edge"}}, {"videoBoxStyle": {"border-radius": "0.625rem", "border": "0.0625rem solid #ccc"}, "tab": 0, "video": {"value": "https://static-oss.gs-souvenir.com/web/quoteManage/20230710/20230710zkcTkwfY.mp4?id=5", "poster": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20230709/ueditor/95/spur-edge.jpg", "alt": "Spur Edge"}, "title": {"value": "Spur Edge"}}, {"videoBoxStyle": {"border-radius": "0.625rem", "border": "0.0625rem solid #ccc"}, "tab": 0, "video": {"value": "https://static-oss.gs-souvenir.com/web/quoteManage/20230710/20230710iNQfCwZD.mp4?id=5", "poster": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20230709/ueditor/95/oblique-edge.jpg", "alt": "Oblique Edge"}, "title": {"value": "Oblique Edge"}}, {"videoBoxStyle": {"border-radius": "0.625rem", "border": "0.0625rem solid #ccc"}, "tab": 0, "video": {"value": "https://static-oss.gs-souvenir.com/web/quoteManage/20230710/20230710ebnJiFm6.mp4?id=5", "poster": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20230709/ueditor/95/leaf-edge.jpg", "alt": "Leaf Edge"}, "title": {"value": ""}}, {"videoBoxStyle": {"border-radius": "0.625rem", "border": "0.0625rem solid #ccc"}, "tab": 0, "video": {"value": "https://static-oss.gs-souvenir.com/web/quoteManage/20230710/20230710H5sQBzYr.mp4?id=5", "poster": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20230709/ueditor/95/bezel-edge.jpg", "alt": "<PERSON><PERSON>"}, "title": {"value": "<PERSON><PERSON>"}}, {"videoBoxStyle": {"border-radius": "0.625rem", "border": "0.0625rem solid #ccc"}, "tab": 0, "video": {"value": "https://static-oss.gs-souvenir.com/web/quoteManage/20230710/20230710KHS5MwBw.mp4?id=5", "poster": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20230709/ueditor/95/reeded-edge.jpg", "alt": "Reeded <PERSON>"}, "title": {"value": "Reeded <PERSON>"}}, {"videoBoxStyle": {"border-radius": "0.625rem", "border": "0.0625rem solid #ccc"}, "tab": 0, "video": {"value": "https://static-oss.gs-souvenir.com/web/quoteManage/20230710/20230710DAwCzd8R.mp4?id=5", "poster": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20230709/ueditor/95/sunburst-edge.jpg", "alt": "Sunburst Edge"}, "title": {"value": "Sunburst Edge"}}, {"tab": 1, "img": {"value": "https://sticker-static.oss-accelerate.aliyuncs.com/image/uploads/20231221/shiny-gold-finish-of-custom-medals.png", "alt": "Shiny Gold"}, "title": {"value": "Shiny Gold"}}, {"tab": 1, "img": {"value": "https://sticker-static.oss-accelerate.aliyuncs.com/image/uploads/20231221/shiny-silver-finish-of-custom-medals.png", "alt": "<PERSON><PERSON>"}, "title": {"value": "<PERSON><PERSON>"}}, {"tab": 1, "img": {"value": "https://sticker-static.oss-accelerate.aliyuncs.com/image/uploads/20231221/shiny-copper-finish-of-custom-medals.png", "alt": "Shiny Copper"}, "title": {"value": "Shiny Copper"}}, {"tab": 1, "img": {"value": "https://sticker-static.oss-accelerate.aliyuncs.com/image/uploads/20231221/two-tone-shiny-finish-of-custom-medals.png", "alt": "Two Tone (shiny)"}, "title": {"value": "Two Tone (shiny)"}}, {"tab": 1, "img": {"value": "https://sticker-static.oss-accelerate.aliyuncs.com/image/uploads/20231221/black-nickel-finish-of-custom-medals.png", "alt": "Black Nickel"}, "title": {"value": "Black Nickel"}}, {"tab": 1, "img": {"value": "https://sticker-static.oss-accelerate.aliyuncs.com/image/uploads/20231221/antique-gold-finish-of-custom-medals.png", "alt": "Antique Gold"}, "title": {"value": "Antique Gold"}}, {"tab": 1, "img": {"value": "https://sticker-static.oss-accelerate.aliyuncs.com/image/uploads/20231221/antique-silver-finish-of-custom-medals.png", "alt": "Antique Silver"}, "title": {"value": "Antique Silver"}}, {"tab": 1, "img": {"value": "https://sticker-static.oss-accelerate.aliyuncs.com/image/uploads/20231221/antique-copper-finish-of-custom-medals.png", "alt": "Antique Copper"}, "title": {"value": "Antique Copper"}}, {"tab": 1, "img": {"value": "https://sticker-static.oss-accelerate.aliyuncs.com/image/uploads/20231221/two-tone-antique-finish-of-custom-medals.png", "alt": "Two Tone (antique)"}, "title": {"value": "Two Tone (antique)"}}, {"tab": 1, "img": {"value": "https://sticker-static.oss-accelerate.aliyuncs.com/image/uploads/20231221/rainbow-finish-of-custom-medals.png", "alt": "Rainbow Finish"}, "title": {"value": "Rainbow Finish"}}, {"tab": 1, "img": {"value": "https://sticker-static.oss-accelerate.aliyuncs.com/image/uploads/20231221/dye-black-finish-of-custom-medals.png", "alt": "<PERSON><PERSON>"}, "title": {"value": "<PERSON><PERSON>"}}, {"tab": 1, "img": {"value": "https://sticker-static.oss-accelerate.aliyuncs.com/image/uploads/20231221/dye-red-finish-of-custom-medals.png", "alt": "Dye Red"}, "title": {"value": "Dye Red"}}, {"tab": 1, "img": {"value": "https://sticker-static.oss-accelerate.aliyuncs.com/image/uploads/20231221/dye-blue-finish-of-custom-medals.png", "alt": "Dye Blue"}, "title": {"value": "Dye Blue"}}, {"tab": 1, "img": {"value": "https://sticker-static.oss-accelerate.aliyuncs.com/image/uploads/20231221/dye-green-finish-of-custom-medals.png", "alt": "Dye Green"}, "title": {"value": "Dye Green"}}, {"tab": 1, "img": {"value": "https://sticker-static.oss-accelerate.aliyuncs.com/image/uploads/20231221/dye-yellow-finish-of-custom-medals.png", "alt": "Dye Yellow"}, "title": {"value": "Dye Yellow"}}, {"tab": 2, "style": {"background-color": "#ffffff", "border-radius": "0.625rem"}, "img": {"value": "https://sticker-static.oss-accelerate.aliyuncs.com/image/uploads/20231226/PVC-pouch-for-custom-challenge-coins-.jpg", "alt": "Poly Bag Standard"}, "title": {"value": "PVC Pouch"}}, {"tab": 2, "style": {"background-color": "#ffffff", "border-radius": "0.625rem"}, "img": {"value": "https://sticker-static.oss-accelerate.aliyuncs.com/image/uploads/20231226/coins-capsule.jpg", "alt": "<PERSON><PERSON><PERSON>"}, "title": {"value": "Coin Capsule"}}, {"tab": 2, "style": {"background-color": "#ffffff", "border-radius": "0.625rem"}, "img": {"value": "https://sticker-static.oss-accelerate.aliyuncs.com/image/uploads/20231226/Velour-pouch-for-challenge-coins.jpg", "alt": "Plastic Box"}, "title": {"value": "<PERSON><PERSON><PERSON>"}}, {"style": {"background-color": "#ffffff", "border-radius": "0.625rem"}, "tab": 2, "img": {"value": "https://sticker-static.oss-accelerate.aliyuncs.com/image/uploads/20231226/Velour-box-for-challenge-coins.jpg", "alt": "Velour Box"}, "title": {"value": "Velour Box"}}]}, {"id": "armyChallengeCoins_03", "theme": 20, "name": "Card", "column": 4, "manualPlay": true, "mousePlay": true, "cardStyle": {"flex-direction": "column", "background-color": "#fff", "border-radius": "0.6em", "position": "relative"}, "cardContentStyle": {"grid-gap": "unset", "padding": "0.8em"}, "cardTitleStyle": {"position": "absolute", "left": 0, "bottom": 0, "width": "100%", "height": "2em", "line-height": "2em", "text-align": "center", "background": "#242527", "color": "#fff", "opacity": 0.8}, "outer": [{"title": {"value": "Challenge Coins of Upgrade Types"}, "button": {"value": "Challenge Coins Fast Quote", "title": "Challenge Coins Fast Quote", "method": {"modal": "modalQuoteDialog", "quoteUrl": "/quote/coins-quote?type=quoteIframe"}}}], "list": [{"video": {"value": "https://static-oss.gs-souvenir.com/web/quoteManage/20230710/20230710rmHjpAfd.mp4", "poster": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20230709/ueditor/95/rhinestone.jpg"}, "title": {"value": "Rhinestone Challenge Coins"}}, {"video": {"value": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/u_file/ueditor/20230920/bottle-opener.mp4", "poster": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20230709/ueditor/95/bottle-opener.jpg"}, "title": {"value": "Bottle Opener Challenge Coins"}}, {"video": {"value": "https://static-oss.gs-souvenir.com/web/quoteManage/20230710/20230710QrtsWN3P.mp4", "poster": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20230709/ueditor/95/edge-engraving.jpg"}, "title": {"value": "Edge Engraved Challenge Coins"}}, {"video": {"value": "https://static-oss.gs-souvenir.com/web/quoteManage/20230710/2023071045ridjaC.mp4", "poster": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20230709/ueditor/95/epoxy.jpg"}, "title": {"value": "Epoxy Challenge Coins"}}, {"video": {"value": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/u_file/ueditor/20230920/cut-out-and-spinner.mp4", "poster": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20230719/ueditor/95/cut-out-spinner.jpg"}, "title": {"value": "Spinner Challenge Coins"}}, {"video": {"value": "https://static-oss.gs-souvenir.com/web/quoteManage/20230710/20230710YMwGzaEf.mp4", "poster": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20230709/ueditor/95/glow-in-the-dark.jpg"}, "title": {"value": "Glow in the Dark Coins"}}, {"video": {"value": "https://static-oss.gs-souvenir.com/web/quoteManage/20230710/20230710pSrFy2M3.mp4", "poster": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20230709/ueditor/95/two-sides-3d.jpg"}, "title": {"value": "3D Challenge Coins"}}, {"video": {"value": "https://static-oss.gs-souvenir.com/web/quoteManage/20230710/202307105nJ66jnY.mp4", "poster": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20230709/ueditor/95/translucent-color.jpg"}, "title": {"value": "Translucent Enamel Coins"}}]}, {"id": "armyChallengeCoins_04", "theme": 20, "name": "Card", "column": 3, "style": {"background-color": "#171719", "color": "#fff"}, "btnStyle": {"background": "#0066cc", "border": "none"}, "cardStyle": {"flex-direction": "column", "padding": "1em 1em 0 1em", "background": "#fff", "box-shadow": "0px 3px 6px 0px rgb(0 0 0 / 8%)", "border-radius": "0.4em"}, "cardBtnBoxStyle": {"text-align": "center"}, "cardBtnStyle": {"font-size": "1.2em", "margin": "0.5em 0", "text-align": "center", "color": "#0066CC", "background": "#fff", "border": "none"}, "outer": [{"title": {"value": "Free Design Templates For Army Challenge Coins"}, "subTitle": {"value": "Creating custom challenge coins fast for your team? Browse through template categories and edit template with ease. GS-JJ customize army challenge coin shape and cut it according to the artwork you provide."}, "button": {"value": "Edit Army Coins Templates", "title": "Edit Army Coins Templates", "url": "/design/challenge-coins?cid=7", "target": "_blank"}}], "list": [{"img": {"value": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20220905/ueditor/16/retired-u.s.-army-coin-template.png"}, "button": {"value": "US Army Retired Coins Templates >>", "title": "US Army Retired Coins Templates", "url": "/design/challenge-coins/army-retired-challenge-coins", "target": "_self"}}, {"img": {"value": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20220905/ueditor/16/gates-committee-coin-template.png"}, "button": {"value": "Army Recognition Service Coins >>", "title": "Army Recognition Service Coins", "url": "/design/challenge-coins/gates-committee-coins", "target": "_self"}}, {"img": {"value": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20220905/ueditor/16/u.s.combat-veteran-coins-template.png"}, "button": {"value": "Combat Veteran Army Coins >>", "title": "Combat Veteran Army Coins", "url": "/design/challenge-coins/custom-united-states-army-coins", "target": "_self"}}, {"img": {"value": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20220905/ueditor/16/special-forces-coins-template.png"}, "button": {"value": "Special Forces Airborne Coins >>", "title": "Special Forces Airborne Coins", "url": "/design/challenge-coins/special-forces-challengin-coins", "target": "_self"}}, {"img": {"value": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20220905/ueditor/16/the-army-coins-custom-template.png"}, "button": {"value": "Us Navy Seal Team Coins >>", "title": "Us Navy Seal Team Coins", "url": "/design/challenge-coins/army-custom-challenge-coins-1896", "target": "_self"}}, {"img": {"value": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20220906/ueditor/16/u.s.-e-2-army-coins-template.png"}, "button": {"value": "US E-2 Army Dept Service Coins >>", "title": "US E-2 Army Dept Service Coins", "url": "/design/challenge-coins/united-states-army-challenge-coins", "target": "_self"}}]}, {"id": "armyChallengeCoins_05", "theme": 20, "name": "Card", "column": 4, "style": {"background-color": "#171719"}, "titleStyle": {"color": "#fff"}, "cardStyle": {"flex-direction": "column", "padding": "1.5em", "background": "#fff", "box-shadow": "0px 3px 6px 0px rgb(0 0 0 / 8%)", "border-radius": "0.4em", "text-align": "center"}, "cardTitleStyle": {"font-size": "1.2em", "font-weight": "bold", "margin-top": "0.5em"}, "cardSubTitleStyle": {"font-size": "1em", "color": "#666"}, "cardImgStyle": {"width": "7em", "height": "6em", "margin": "0 auto"}, "outer": [{"title": {"value": "What GS-<PERSON><PERSON>er For You?"}}], "list": [{"img": {"value": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20220905/ueditor/16/army-challenge-coin-competitive-price.png"}, "title": {"value": "Competitive Price"}, "subTitle": {"value": "20 years of coin design and manufacturer in U.S., quality guarantee & factory direct prices."}}, {"img": {"value": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20220906/ueditor/16/free-design-service.png"}, "title": {"value": "Easy Creation"}, "subTitle": {"value": "GS-JJ Provides professional free designs and massive templates for you to create unlimited inspiration."}}, {"img": {"value": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20220905/ueditor/16/good-service-for-army-challenge-coin.png"}, "title": {"value": "Excellent Service"}, "subTitle": {"value": "GS-JJ offer you 24-hour customer service and solve your problem and doubts timely. We deliver goods with fast turn-around"}}, {"img": {"value": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20220905/ueditor/16/custom-army-challenge-coin-no-mininum.png"}, "title": {"value": "No Minimum"}, "subTitle": {"value": "GS-JJ Provides very personal service . You can start with 1 pc, Small business is always welcome here."}}]}]