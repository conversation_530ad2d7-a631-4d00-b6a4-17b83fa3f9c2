<template>
	<div class="modal-box stock-list">
		<GSJJBreadcrumb :items="breadcrumb" @click.native="openMenu" />

		<div class="stock-list__main">
			<div class="left" v-show="menuShow" @click="closeMenu">
				<GSJJMenuList :cate-list="cateList" static-path="stock" />
			</div>

			<div class="right">
				<div class="category-brief" v-if="currentCate?.description" v-html="currentCate?.description"></div>

				<div class="page-title">
					<h1>{{ currentCate ? currentCate.cateName : lang.products }}</h1>
				</div>

				<div class="prod-list">
					<nuxt-link class="prod-item" v-for="item in records" :key="item.id" :to="'/' + item.pageUrlPrefix + '/shop/' + item.pageUrl">
						<Pic class="pic" :src="item.picPathZero" :alt="item.proName" />
						<div class="prod-item__content">
							<div class="title">{{ item.proName }}</div>
							<div class="price"><CCYRate :price="getLowPrice(item.wholesale)" /></div>
							<div class="action">
								<div class="btn">{{ lang.seeMore }}</div>
							</div>
						</div>
					</nuxt-link>
				</div>

				<v-pagination v-if="records.length" :value="currentPage" :length="pages" :total-visible="8" @input="pageChange"></v-pagination>
			</div>
		</div>
	</div>
</template>

<script>
import { findInTreeArray } from "@/utils/utils";
import { getStockList } from "@/api/web";
import CCYRate from "@/components/CCYRate.vue";
import GSJJBreadcrumb from "./components/-GSJJBreadcrumb.vue";
import GSJJMenuList from "./components/-GSJJMenuList.vue";

export default {
	name: "GSJJStockList",

	components: { CCYRate, GSJJBreadcrumb, GSJJMenuList },

	async asyncData({ store, error, route }) {
		try {
			const params = {
				page: route.params.page || 1,
				pageSize: 27,
				pageUrl: route.params.cateName,
			};
			const {
				data: { stockList, cateList },
			} = await getStockList(params);
			let currentCate = null;
			if (route.params.cateName) {
				currentCate = findInTreeArray(
					cateList,
					(item) => {
						return item.pageUrl === route.params.cateName;
					},
					"childList"
				);
			}
			return {
				stockList,
				cateList,
				currentCate,
			};
		} catch (e) {
			return error({ statusCode: 404 });
		}
	},

	head() {
		const currentCate = this.currentCate;
		const seoConfig = {
			title: currentCate ? currentCate.seoTitle : "Pin Stock | Coin Stock | Buckle Stock | FREE SHIPPING | CHEAP | GS-JJ.com ®",
			keywords: currentCate
				? currentCate.seoKeyword || currentCate.cateName
				: "Pin Stock, Coin Stock, Buckle Stock, Stock Lapel Pins, Buckle Stock, Coin Stock, Stock Patches",
			description: currentCate
				? currentCate.seoDescription
				: "GS-JJ.com ® can provide lapel pins, belt buckles, lanyards, coins, wristbands and other products in stock. If you order our products, we will deliver them them to you soon with Free shipping",
		};
		const meta = [
			{
				hid: "keywords",
				name: "keywords",
				content: seoConfig.keywords,
			},
			{
				hid: "description",
				name: "description",
				content: seoConfig.description,
			},
		];
		return {
			title: seoConfig.title,
			meta,
		};
	},

	data() {
		return {
			visible: false,
		};
	},

	computed: {
		lang() {
			return this.$store.getters.lang?.exhibition;
		},
		currentPage() {
			const pageNum = Number(this.$route.params.page);
			if (isNaN(pageNum) || pageNum < 1 || this.$route.query.Keyword) {
				return 1;
			}
			return pageNum;
		},
		isMobile() {
			return this.$store.getters.isMobile;
		},
		breadcrumb() {
			let list = [];
			if (this.currentCate) {
				list.unshift({
					text: this.currentCate.cateName,
					link: `/${this.currentCate.pageUrlPrefix}/stock/${this.currentCate.pageUrl}`,
				});
				if (this.currentCate.parentId) {
					const parentCate = findInTreeArray(this.cateList, (item) => item.id === this.currentCate.parentId);
					if (parentCate) {
						list.unshift({
							text: parentCate.cateName,
							link: `/${parentCate.pageUrlPrefix}/stock/${parentCate.pageUrl}`,
						});
					}
				}
			}
			let result = [{ text: this.lang.home, link: "/" }, { text: this.lang.inStock, link: "/products" }, ...list];
			if (this.isMobile) {
				result = result.slice(1).map((item) => ({ ...item, link: null }));
			}
			return result;
		},
		menuShow() {
			return !this.isMobile || this.visible;
		},
		records() {
			return this.stockList?.records || [];
		},
		pages() {
			return this.stockList?.pages || 1;
		},
	},

	methods: {
		openMenu() {
			if (!this.isMobile) return;
			this.visible = true;
		},
		closeMenu() {
			if (!this.isMobile) return;
			this.visible = false;
		},
		getFormQueryParams(form) {
			const formData = new FormData(form);
			return Object.fromEntries(Array.from(formData.entries()).filter(([_, value]) => value));
		},
		pageChange(targetPage) {
			// 避免重复导航到相同页面
			if (targetPage === this.currentPage) return;

			const routeName = this.$route.name;
			const routeParams = this.$route.params;
			const routeQuery = this.$route.query;
			let config = {
				path: `${this.$route.path}/${targetPage}.html`,
				query: { ...routeQuery },
			};
			if (routeName.endsWith("-page")) {
				config = {
					name: routeName,
					params: { ...routeParams, page: targetPage },
					query: { ...routeQuery },
				};
			}
			this.$router.push(config);
		},
		getLowPrice(wholesale) {
			if (!wholesale) return null;
			// wholesale 格式："{\"1\":\"9.99\",\"10\":\"7.2\",\"20\":\"4.3\",\"50\":\"1.99\",\"100\":\"1.42\"}"
			const wholesaleObj = JSON.parse(wholesale);
			const prices = Object.values(wholesaleObj);
			return Math.min(...prices);
		},
	},
};
</script>

<style lang="scss" scoped>
.stock-list {
	padding-top: 1rem;
	z-index: auto;
}

.stock-list__main {
	display: grid;
	grid-template-columns: 21% 78%;
	justify-content: space-between;

	.category-brief {
		padding: 0 24px;
		margin-bottom: 30px;
		@include respond-to(mb) {
			display: none;
		}
	}

	.page-title {
		margin-bottom: 1.875rem;
		padding: 0.5rem 1.5rem;
		background-color: #f7f7f7;
		h1 {
			line-height: 18px;
			font-size: 14px;
			font-weight: 700;
		}

		@include respond-to(mb) {
			margin-bottom: 4px;
		}
	}

	.filter {
		padding: 8px 10px;
		display: flex;
		gap: 4px;
		align-items: center;
		font-size: 12px;
		border: 1px solid #d4d4d4;
		background-color: #f5f5f5;

		input[type="text"] {
			width: 178px;
			height: 26px;
			padding: 2px 5px;
			border: 1px solid #ddd;
			background-color: #ffff;
		}
		input[type="submit"] {
			width: 40px;
			height: 24px;
			font-weight: 700;
			color: #333;
			border: 1px solid #ddd;
			border-radius: 3px;
			background-color: #eee;
		}

		@include respond-to(mb) {
			display: none;
		}
	}

	.prod-list {
		margin-top: 1.25rem;
		margin-bottom: 1.25rem;
		display: grid;
		gap: 1.25rem;
		grid-template-columns: repeat(3, 1fr);

		@include respond-to(mb) {
			margin-top: 0.5rem;
			margin-bottom: 1rem;
			gap: 0.5rem;
			grid-template-columns: repeat(2, 1fr);
		}
	}

	.prod-item {
		background-color: #fafafa;
		&__content {
			display: flex;
			gap: 0.5rem;
			flex-direction: column;
			padding: 0.5rem;
			font-size: 12px;
		}
		.pic {
			padding: 0.625rem;
			aspect-ratio: 1;
			background-color: #fff;
		}
		.title {
			min-height: 2.25rem;
		}
		.price {
			color: var(--color-primary);
		}
		.action {
			display: flex;
			justify-content: center;
		}
		.btn {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 120px;
			height: 40px;
			border: 3px solid #e9e9e9;
			background-image: url("https://static-oss.gs-souvenir.com/web/20250525/prod_action_20250525xH6HBb.png");
			background-repeat: no-repeat;
			background-position: 3px -98px;
			&:hover {
				color: #fff;
				border-color: #232323;
				background-color: #232323;
				background-position: 3px -66px;
			}
		}
		@include respond-to(mb) {
			border: 1px solid #d0dbe0;
			.pic {
				padding: 0;
			}
			.price {
				display: none;
			}
			.btn {
				width: 40px;
				text-indent: -9999px;
			}
		}
	}

	::v-deep {
		.v-pagination__item--active {
			background: #333 !important;
		}
	}

	@include respond-to(mb) {
		grid-template-columns: 100%;
	}
}

.left {
	@include respond-to(mb) {
		position: fixed;
		z-index: 999;
		left: 0;
		right: 0;
		top: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.5);

		.category {
			position: absolute;
			width: 60vw;
			top: 26vw;
			bottom: 0;
			overflow-y: auto;
		}
	}
}

.breadcrumb {
	@include respond-to(mb) {
		&::after {
			content: "";
			display: block;
			border: solid #555;
			border-width: 0 1px 1px 0;
			padding: 4px;
			transform: rotate(45deg);
			margin-top: -4px;
			margin-left: 10px;
		}
	}
}
</style>
