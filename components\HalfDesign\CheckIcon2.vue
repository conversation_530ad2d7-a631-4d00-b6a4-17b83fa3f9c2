<template>
	<div class="checkIcon" :class="{'nomal':iconStyle==='nomal','round':iconStyle==='round','noBack':iconStyle==='noBack','round':iconStyle==='round'}">
		<b :class="[iconName,{'medium':iconSize==='medium'}]" class="my_icon" :style="{color:iconColor}"></b>
	</div>
</template>

<script>
export default {
  props:{
    iconName:{
      type:String,
      default:"icon-a-icon-checkzhuanhuan"
    },
    iconColor:{
      type:String,
    },
    size: {
      type: String,
      validator: (value) => ['mini', 'small', 'medium', 'large'].includes(value)
    },
    iconSize: {
      type: String,
      validator: (value) => ['mini', 'small', 'medium', 'large'].includes(value)
    },
    iconStyle:{
      type: String,
      default:"nomal",
      validator: (value) => ['nomal', 'round','noBack'].includes(value)
    }
  }
};
</script>

<style scoped lang="scss">
@import "~assets/css/half.scss";

.checkIcon {
	display: flex;
	justify-content: center;
	align-items: center;
	width: 22px;
	height: 22px;
	background: var(--color-primary, transparent);
	@include respond-to(mb) {
		width: 16px;
		height: 16px;
		border-radius: 0 2px 0 2px;
	}
}
.checkIcon.nomal{
  border-radius: 0 4px 0 4px;
}
.checkIcon.round{
  border-radius:50%;
}
.checkIcon.noBack{
  width: 0;
  height: 0;
  @include respond-to(mb) {
		width: 0;
		height: 0;
		border-radius: 0;
	}
}

.my_icon{
  font-size: 16px;
  background-color: #fff;
  clip-path: circle(40% at 50% 50%);
  z-index: 2;
}

.my_icon.medium{
  &::before{
    font-size: 24px;
  }
  @include respond-to(mb) {
  &::before{
    font-size: 22px;
  }
	}
  
}
</style>