<template>
	<div class="fixed-button-container">
		<div class="fixed-button" :class="{ luggagetags: isLuggagetags }">
			<div
				class="add-favorite"
				@click="
					goCollection({
						isCollection: productInfo.isCollection,
						id: productInfo.id,
					})
				"
			>
				<template v-if="!productInfo.isCollection">
					<b class="icon-shoucang"></b>
				</template>
				<template v-else>
					<b class="icon-xinxin isActive"></b>
				</template>
			</div>
			<div class="order-button" @click="handleClick">{{ "Order Now - Get Free Quote" }}</div>
		</div>
	</div>
</template>

<script>
export default {
	name: "FixedOrderButton",
	props: {
		productInfo: {
			type: Object,
			required: true,
		},
		isLuggagetags: {
			type: Boolean,
			default: false,
		},
	},
	methods: {
		handleClick() {
			this.$emit("click");
		},
		goCollection(data) {
			this.$emit("goCollection", data);
		},
	},
};
</script>

<style scoped lang="scss">
.fixed-button-container {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	display: flex;
	justify-content: center;
	align-items: center;
	z-index: 1000;
	padding: 16px 20px 12px;
	height: 70px;
	background: #ffffff;
	border-top: 1px solid #e6e6e6;
}

.fixed-button {
	width: 100%;
	height: 100%;
	position: relative;
	display: flex;
	align-items: center;
	justify-content: space-between;
	gap: 20px;
	&.luggagetags {
		.order-button {
			background: linear-gradient(90deg, #f8430d, #fea33a) !important;
		}
	}
	.add-favorite {
		cursor: pointer;
		b {
			font-size: 22px;
		}

		b.isActive {
			color: $color-primary;
		}
	}
	.order-button {
		flex: 1;
		height: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 16px;
		font-weight: bold;
		color: white;
		border-radius: 6px;
		cursor: pointer;
		background: $color-primary;
	}
}
</style>
