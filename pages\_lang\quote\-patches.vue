<template>
	<div id="custom-embroidered-patches" class="quoteWrap">
		<Preloader v-if="isLoading"></Preloader>
		<template v-else>
			<article class="content">
				<QuoteNav :pid="pid" :title="lang.patchType"></QuoteNav>
				<div class="header">
					<h1>{{ lang.cy + " " + cateData.cateName }}</h1>
				</div>
				<div class="leftArea" id="leftArea">
					<div v-for="(item, index) in filterShowGeneralData" :key="item.id" class="kk" :class="{ type1: picDialog }">
						<PublicStep v-if="item.paramName == 'quoteCategory' && !quoteStyleData.noShowDetail" :config="allStepConfig[item.paramName]" :selectedData="selectedData" :stepData="item" :maskName.sync="maskName" @clickFun="selectQuoteParams(item, $event)" @viewVideo="viewVideo"></PublicStep>

						<PublicStep v-if="item.paramName == 'More Options'" :config="allStepConfig[item.paramName]" :selectedData="selectedData" :stepData="item" :maskName.sync="maskName" @clickFun="selectQuoteParams(item, $event)"></PublicStep>

						<PublicStep v-if="item.paramName == 'Cutting Method'" :config="allStepConfig[item.paramName]" :selectedData="selectedData" :stepData="item" :maskName.sync="maskName" @clickFun="selectQuoteParams(item, $event)"></PublicStep>

						<PublicStep v-if="item.paramName == 'Patch Surface effect'" :config="allStepConfig[item.paramName]" :selectedData="selectedData" :stepData="item" :maskName.sync="maskName" @clickFun="selectQuoteParams(item, $event)"></PublicStep>

						<PublicStep v-if="item.paramName == 'Pre-Cut Transfers'" :config="allStepConfig[item.paramName]" :selectedData="selectedData" :stepData="item" :maskName.sync="maskName" @clickFun="selectQuoteParams(item, $event)"></PublicStep>

						<PublicStep v-if="item.paramName == '2D or 3D'" :config="allStepConfig[item.paramName]" :selectedData="selectedData" :stepData="item" :maskName.sync="maskName" @clickFun="selectQuoteParams(item, $event)" @viewVideo="viewCustomVideo"></PublicStep>

						<PublicStep v-if="item.paramName == 'Select Sequins Diameter'" :config="allStepConfig[item.paramName]" :selectedData="selectedData" :stepData="item" :maskName.sync="maskName" @clickFun="selectQuoteParams(item, $event)"></PublicStep>

						<PublicStep v-if="item.paramName == 'Printing Method'" :config="allStepConfig[item.paramName]" :selectedData="selectedData" :stepData="item" :maskName.sync="maskName" @clickFun="selectQuoteParams(item, $event)"></PublicStep>

						<PublicStep v-if="item.paramName == 'Patch Outline'" :config="allStepConfig[item.paramName]" :selectedData="selectedData" :stepData="item" :maskName.sync="maskName" @clickFun="selectQuoteParams(item, $event)"></PublicStep>

						<PublicStep v-if="['Patch Color', 'Color', 'Number of Silicone Colors', 'Leather Color', 'Select Number of Metallic Colors', 'Patch Twill Color', 'Tatami Fabric Color', 'Select Number of Colors', 'Label Color'].includes(item.paramName)" :config="allStepConfig[item.paramName]" :selectedData="selectedData" :stepData="item" :maskName.sync="maskName" @clickFun="selectQuoteParams(item, $event)"></PublicStep>

						<PublicStep v-if="['Patch Type', 'Label Thread Type'].includes(item.paramName)" :config="allStepConfig[item.paramName]" :selectedData="selectedData" :stepData="item" :maskName.sync="maskName" @clickFun="selectQuoteParams(item, $event)"></PublicStep>

						<PublicStep v-if="item.paramName == 'Label Fold Type'" :config="allStepConfig[item.paramName]" :selectedData="selectedData" :stepData="item" :maskName.sync="maskName" @clickFun="selectFoldType(item, $event)" @clickExtendStepFun="clickExtendStepFun(item, $event)"></PublicStep>

						<SizeSelect v-if="['Patch Size', 'Size', 'Label Size'].includes(item.paramName) && sizeType === 'sizeSelect'" :config="allStepConfig[item.paramName]" :selectedData="selectedData" :shoppingModel="shoppingModel.sizeModel" :stepData="item" :maskName.sync="maskName" @clickFun="selectQuoteParams(item, $event)" @showMaskFn="showMaskFn"></SizeSelect>

						<PinsSizeSelect v-if="item.paramName == 'Patch Size' && sizeType === 'normal'" :generalData="generalData" :selectedData="selectedData" :stepData="item" :maskName="maskName" :smallTitle="lang.patches.stepSizeTitle" :sizeImgP1="lang.patches.p1" @clickFun="selectQuoteParams(item, $event)" @closeMask="closeMask" @showMaskFn="showMaskFn"> </PinsSizeSelect>

						<PublicStep v-if="['Patch Backing', 'Backing'].includes(item.paramName)" :config="allStepConfig[item.paramName]" :selectedData="selectedData" :stepData="item" :maskName.sync="maskName" @clickFun="selectQuoteParams(item, $event)" @showMaskFn="showMaskFn"></PublicStep>

						<PublicStep v-if="item.paramName == 'Patch Border' && pid !== 273" :config="allStepConfig[item.paramName]" :selectedData="selectedData" :stepData="item" :maskName.sync="maskName" @clickFun="selectQuoteParams(item, $event)"></PublicStep>

						<PublicStep v-if="item.paramName == 'Additional Upgrades'" :config="allStepConfig[item.paramName]" :selectedData="selectedData" :stepData="item" :maskName.sync="maskName" @clickFun="selectQuoteParams(item, $event)" @showMaskFn="showMaskFn"></PublicStep>

						<PublicStep v-if="['Patch Shape', 'PVC Patch Shape', 'Shape'].includes(item.paramName)" :config="allStepConfig[item.paramName]" :selectedData="selectedData" :stepData="item" :maskName.sync="maskName" @clickFun="selectQuoteParams(item, $event)"></PublicStep>

						<PublicStep v-if="['Purchase Intention', 'Intended Use'].includes(item.paramName)" :config="allStepConfig[item.paramName]" :selectedData="selectedData" :stepData="item" :maskName.sync="maskName" :show-next-btn="true" @clickFun="selectQuoteParams(item, $event)" @showMaskFn="showMaskFn"></PublicStep>

						<StepBorderColor v-if="item.paramName == 'Patch Border' && pid === 273" :id="item.paramName" :config="allStepConfig[item.paramName]" @colourAtlaClick="colourAtlaClick" @selectQuoteParamsFunc="selectQuoteParamsFunc" :colourAtlaList="colourAtlaList" :customColor="customColor" :isImgStyle="isImgStyle" :showColourAtla="showColourAtla" :stepData="item" :maskName.sync="maskName" @closeMask="closeMask" :selectedData="selectedData" :showBorderColor="showBorderColor" :colorList="colorList" @borderColorClick="borderColorClick" :selectedParamsValue="selectedParamsValue" @showMaskFn="showMaskFn"></StepBorderColor>

						<PublicStep v-if="item.paramName == 'Number of Silicone Layers'" :config="allStepConfig[item.paramName]" :selectedData="selectedData" :stepData="item" :maskName.sync="maskName" @clickFun="selectFoldType(item, $event)" @clickExtendStepFun="clickExtendStepFun(item, $event)"></PublicStep>

						<template v-if="item.paramName === 'Upload Artwork & Comments'">
							<StepUpload class="step-item step-upload" :class="{ mask: maskName === item.paramName }" :id="item.paramName" :index="item.customIndex" :itemData="item" :uploadList.sync="uploadArtworkList" :remark.sync="remark" :isUpload.sync="isUpload" @closeMask="closeMask" @showMaskFn="showMaskFn" :key="index"></StepUpload>
						</template>
						<!--                步骤九-->
						<template v-if="['qty', 'Quantity'].includes(item.paramName)">
							<StepQty class="step-item step-qty" :class="{ mask: maskName === item.paramName }" :id="item.paramName" :index="item.customIndex" :itemData="item" :customQty.sync="customQty" :restaurants="restaurants" @closeMask="closeMask" @showMaskFn="showMaskFn" @calcPrice="debounceCalcPrice" :key="index"></StepQty>
						</template>
						<!--                步骤十-->
						<template v-if="['Turnaround Time', 'Select Turnaround Time'].includes(item.paramName)">
							<StepTime class="step-item step-date" :class="{ mask: maskName === item.paramName }" :id="item.paramName" :cateData="cateData" :pid="pid" :customQty="customQty" :index="item.customIndex" :itemData="item" :priceInfo="priceInfo" :previewMode="previewMode" :selectedParams="selectedData" @showMaskFn="showMaskFn" @selectItem="selectQuoteParams($event.item, $event.citem)" @closeMask="closeMask" :key="index"></StepTime>
						</template>
					</div>
				</div>
				<div class="rightArea" id="rightAreaCustom">
					<transition enter-active-class="animate__animated animate__faster animate__fadeInBottomLeft" leave-active-class="animate__animated animate__faster animate__fadeOutBottomLeft">
						<Detail v-show="showRightArea" class="type1" :class="{ mask: maskName }" :key="1" :absoluteImg="comeFromDZ.absoluteImg" :selectedData="selectedData" :priceInfo="priceInfo" :customQty="customQty" :textInfo="textInfo" :generalData="generalData" @toPosition="toPosition" @addInquiry="debounceAddInquiry" @addCart="addCart" :customColor="customColor" :selectedTextures="selectedTextures"></Detail>
					</transition>
				</div>
			</article>
			<div class="footer" id="foot">
				<Detail class="type2" :key="3" :absoluteImg="comeFromDZ.absoluteImg" :selectedData="selectedData" :priceInfo="priceInfo" :customQty="customQty" :textInfo="textInfo" :generalData="generalData" @toPosition="toPosition" @addInquiry="debounceAddInquiry" @addCart="addCart" :customColor="customColor" :selectedTextures="selectedTextures"></Detail>
			</div>
			<el-drawer :visible.sync="showArea" :with-header="true" custom-class="drawDialog" :size="device === 'ipad' ? '728px' : '85%'">
				<Detail class="type3" :key="2" :absoluteImg="comeFromDZ.absoluteImg" :selectedData="selectedData" :priceInfo="priceInfo" :customQty="customQty" :textInfo="textInfo" :generalData="generalData" @toPosition="toPosition" @addInquiry="debounceAddInquiry" @addCart="addCart" :customColor="customColor" :selectedTextures="selectedTextures"></Detail>
			</el-drawer>
			<!--    遮罩-->
			<myMask :maskName.sync="maskName"></myMask>
			<!-- 建议弹窗-->
			<RecomendDialog :showUpload="false" :commentsDialog.sync="commentsDialog" :paramValue="selectedParamsValue" @next="recomendNext" :textPlaceholder="lang.placeholder2"></RecomendDialog>
			<!--  手机，ipad端预览-->
			<PreviewBtn :showArea.sync="showArea"></PreviewBtn>
			<BaseDialog v-model="showOtoDialog" :width="device != 'mb' ? '570px' : '90%'">
				<div class="otoWrap">
					<img src="@/assets/images/oto.png" alt="" />
					<p>{{ lang.p1 }}</p>
					<h3>{{ lang.p2 }}</h3>
					<p style="color: #666666">
						{{ lang.p3 }}<br />
						{{ lang.p4 }}.
					</p>

					<div class="box">
						<p class="t1">
							{{ lang.p5 }} <br />
							{{ lang.p6 }}!
						</p>
						<a href="https://www.o2o.co/previewWeb" style="text-decoration: none">
							<el-button type="primary">{{ lang.p7 }}</el-button>
						</a>
					</div>
				</div>
			</BaseDialog>
			<VideoPreviewDialog :pic-dialog.sync="picDialog" :zoom-pic="zoomPic" :temp-type="tempType" :selected-params-value="selectedParamsValue" @qtyAndBtnConfirm="qtyAndBtnConfirm" @qtyAndBtnNext="qtyAndBtnNext" @dialogNextStep="dialogNextStep"></VideoPreviewDialog>
			<BaseDialog v-model="upgradeDialog" :width="device != 'mb' ? '640px' : '320px'">
				<Upgrade @closeUpgrade="upgradeDialog = false" :tipArr="tipArr" :typeName="cateData.cateName" @upgradeLevel="upgradeLevel"></Upgrade>
			</BaseDialog>
			<infoDialog :infoDialogVisible.sync="infoDialogVisible" @getValue="getValueFun" :otherUpload="uploadArtworkList" :recomendUpload="recomendUpload" :uploadList.sync="uploadList"></infoDialog>
			<BaseDialog v-model="noFileDialog" :width="device != 'mb' ? '485px' : '90%'" :model="false">
				<template #closeIcon>
					<div style="display: none"></div>
				</template>
				<infoUpload :infoUploadList="infoUploadList" @pushInfoList="pushInfoList" @delInfoList="delInfoList" @updateInquiry="updateInquiry" @closeInfoDialog="closeInfoDialog"> </infoUpload>
			</BaseDialog>
			<PatchesColor v-if="patchesColorDialog" :patchesColorDialog.sync="patchesColorDialog" :colorNumberList="colorNumberList" :itemData="selectedParamsValue" @qtyAndBtnConfirm="qtyAndBtnConfirm" @qtyAndBtnNext="qtyAndBtnNext($event)"></PatchesColor>
			<PatchesAdhesive v-if="patchesAdhesiveDialog" :patchesAdhesiveDialog.sync="patchesAdhesiveDialog" :adhesiveList="adhesiveList" :itemData="selectedParamsValue" :selectedTextures="selectedTextures" @texturesBtnNext="texturesBtnNext($event)" @update:textures="handleUpdateTextures"></PatchesAdhesive>
		</template>
	</div>
</template>
<script>
import "@/plugins/element";
import MyCheckBox from "@/components/Medals/MyCheckBox";
import MyCheckBox3 from "@/components/Medals/MyCheckBox3";
import MyCheckBox4 from "@/components/Medals/MyCheckBox4";
import BaseDialog from "@/components/Quote/BaseDialog";
import VideoPlayer from "@/components/Quote/VideoPlayer.vue";
import Upgrade from "@/components/Quote/Upgrade";
import DialogBM from "@/components/Medals/DialogBM";
import QtyAndBtn from "@/components/Medals/QtyAndBtn";
import Corner from "@/components/Medals/Corner";
import infoDialog from "@/components/Medals/infoDialog";
import QuoteNav from "@/components/Medals/QuoteNav";
import QuoteBtn from "@/components/Quote/QuoteBtn";
import quoteMixin from "@/mixins/quote";
import quoteBanChoiceMixins from "@/mixins/quoteBanChoice";
import StepTime from "@/components/Quote/StepTime.vue";
import StepUpload from "@/components/Quote/StepUpload.vue";
import StepQty from "@/components/Quote/StepQty.vue";
import RecomendDialog from "@/components/Quote/RecomendDialog.vue";
import myMask from "@/components/Quote/Mask.vue";
import Detail from "@/components/Quote/Detail.vue";
import PreviewBtn from "@/components/Quote/PreviewBtn.vue";
import Preloader from "@/components/Quote/Preloader.vue";
import PublicStep from "@/components/Quote/PublicStep/index.vue";
import { getQuoteConfig } from "@/assets/js/quote/quoteConfig";
import SizeSelect from "@/components/Quote/SizeSelect.vue";
import PinsSizeSelect from "@/components/Quote/PinsSizeSelect.vue";
import PatchesColor from "@/components/Quote/PatchesColor.vue";
import PatchesAdhesive from "@/components/Quote/PatchesAdhesive.vue";
import { DataProcessing } from "@/utils/dataProcessing";
import StepBorderColor from "@/components/Quote/StepBorderColor.vue";
import { getAllColorCard } from "@/api/web.js";
import { scrollToViewTop } from "@/utils/utils";
import VideoPreviewDialog from "@/components/Quote/VideoPreviewDialog.vue";

export default {
	head() {
		return {
			title: "Quote page",
		};
	},
	components: {
		VideoPreviewDialog,
		PatchesAdhesive,
		PatchesColor,
		PinsSizeSelect,
		SizeSelect,
		PublicStep,
		Preloader,
		PreviewBtn,
		Detail,
		myMask,
		RecomendDialog,
		StepQty,
		StepUpload,
		StepTime,
		MyCheckBox,
		MyCheckBox3,
		MyCheckBox4,
		BaseDialog,
		VideoPlayer,
		DialogBM,
		QtyAndBtn,
		Corner,
		infoDialog,
		Upgrade,
		QuoteNav,
		QuoteBtn,
		StepBorderColor,
	},
	mixins: [quoteMixin, quoteBanChoiceMixins],
	data() {
		const config = getQuoteConfig.call(this, this.$route.name);
		return {
			...config,
			colourAtlaList: [],
			customColor: {},
			isImgStyle: true,
			showColourAtla: false,
			shoppingModel: {
				op: 0,
				selectedData: undefined,
				sizeModel: {},
			},
		};
	},
	watch: {
		async initData(val) {
			let pShapeId = this.$route.query.pShapeId,
				pColorId = this.$route.query.pColorId,
				parentShapeParam = this.generalData.find((x) => x.paramName === "Patch Shape"),
				parentColorParam = this.generalData.find((x) => x.paramName === "Patch Twill Color");
			if (val && pShapeId && pColorId) {
				if (parentShapeParam) {
					let childParam = parentShapeParam.childList.find((x) => x.id == pShapeId);
					await this.selectQuoteParams(parentShapeParam, childParam, true);
				}
				if (parentColorParam) {
					let childParam = parentColorParam.childList.find((x) => x.id == pColorId);
					await this.selectQuoteParams(parentColorParam, childParam, true);
				}
			}
		},
	},
	methods: {
		viewCustomVideo() {
			this.viewVideo(arguments[0], arguments[1], "video");
		},
		selectFoldType(item, citem) {
			let findParam = this.generalData.find((gitem) => {
				return gitem.id === item.id;
			});
			const titleMap = {
				"Label Fold Type": {
					extendStepTitle: this.lang.printedCareLabels.extendStepTitle,
				},
				"Number of Silicone Layers":{
					extendStepTitle: this.lang.siliconePatches.extendStepTitle
				}
			};
			if (findParam) {
				if (citem.childList.length) {
					findParam.showExtendStep = !!citem.childList.length;
					findParam.extendStepTitle = titleMap[item.paramName]?.extendStepTitle;
					findParam.extendStepItem = citem;
				} else {
					findParam.showExtendStep = false;
					findParam.extendStepTitle = "";
					findParam.extendStepItem = null;
				}
			}
			this.selectQuoteParamsFunc(item, citem);
		},
		clickExtendStepFun(item, citem) {
			let findParam = this.generalData.find((gitem) => {
				return gitem.id === item.id;
			});
			if (findParam) {
				let findChildParam = findParam.childList.find((cc) => {
					return cc.id === findParam.extendStepItem.id;
				});
				findParam.extendStepItem.extendStepValue = citem;
				findChildParam.extendStepValue = citem;
			}
			this.showMaskFn();
		},

		colourAtlaClick(item) {
			this.isImgStyle = false;
			this.customColor = item;
			this.showColourAtla = !this.showColourAtla;
			this.selectedParamsValue.colorValue = item;
			this.selectedParamsValue.colorValue.parentId = 1;
			this.selectedParamsValue.colorValue.alias = item.pantone;
			this.$forceUpdate();
			this.debounceCalcPrice();
		},
		selectQuoteParamsFunc(item, itemChild) {
			if (["Label Fold Type","Number of Silicone Layers"].includes(item.paramName)) {
				this.selectQuoteParams(item, itemChild, !!itemChild.childList.length);
				setTimeout(() => {
					let id = "extendStepWrap-" + item.paramName.replace(/\s+/g, "-")
					scrollToViewTop(document.getElementById(id));
				}, 500);
			} else {
				this.selectQuoteParams(item, itemChild);
			}
		},

		//购物车回填业务
		initShoppingFunc(item) {
			//实时读取选中数据.  最多5秒则释放
			if (this.shoppingModel.op < 5) {
				setTimeout(() => {
					this.shoppingModel.op += 1;
					this.initShoppingFunc(this.selectedData);
				}, 1000);
			} else {
				if (this.pid === 273) {
					if (this.shoppingModel.selectedData == null) {
						this.shoppingModel.selectedData = DataProcessing.deepCopy(this.selectedData); //只赋值一次  避免item清空
					}
					if (item["Patch Border"]?.length > 0) {
						let patchBorderModel = item["Patch Border"][0];
						this.generalData.map((n) => {
							if (n.paramName === "Patch Border") {
								this.selectQuoteParamsFunc(n, patchBorderModel);
								let colorValue = JSON.parse(JSON.stringify(patchBorderModel.colorValue));

								if (patchBorderModel?.colorValue) {
									let recommend = {};
									this.colorList.map((n) => {
										if (n.paramName == "Recommend") {
											recommend = n;
										}
									});

									this.colorList.map((n) => {
										if (n.id == patchBorderModel.colorValue.id) {
											this.borderColorClick(patchBorderModel.colorValue);
										}
									});

									this.colourAtlaList.map((n) => {
										if (n.id == patchBorderModel.colorValue.id) {
											this.borderColorClick(recommend); //第一次打开颜色选择器
											this.colourAtlaClick(colorValue); //第二次选中
										}
									});
								}
							}
						});
					}
				}

				if (item["Patch Shape"]?.length > 0) {
					let patchShapeModel = item["Patch Shape"][0];
					this.generalData.map((n) => {
						if (n.paramName === "Patch Shape") {
							this.selectQuoteParams(n, patchShapeModel);
						}
					});
				}

				if (this.pid === 55) {
					if (item["quoteCategory"]?.length > 0) {
						let quoteCategoryModel = item["quoteCategory"][0];
						this.generalData.map((n) => {
							if (n.paramName === "quoteCategory") {
								this.selectQuoteParams(n, quoteCategoryModel, true);
								this.$forceUpdate();
							}
						});
					}
				}

				let sizeItem = item["Patch Size"] || item["Size"];
				if (sizeItem?.length > 0) {
					let patchSizeModel = sizeItem[0];
					this.generalData.map((n) => {
						if (n.paramName === "Patch Size" || n.paramName === "Size") {
							this.shoppingModel.sizeModel = patchSizeModel;
						}
					});
				}
				this.$forceUpdate();
			}
		},
	},
	created() {
		//blank-patches 获取自定义颜色
		getAllColorCard().then((res) => {
			this.colourAtlaList = res.data;
		});
		this.initShoppingFunc(this.selectedData);
	},
};
</script>

<style scoped lang="scss">
@import "@/assets/css/quotePublic";

.quoteWrap{
	::v-deep #extendStepWrap-Number-of-Silicone-Layers .stepContent{
		grid-template-columns: repeat(6, 1fr);

		@include respond-to(mb){
			grid-template-columns: repeat(3, 1fr);
		}
	}
}
</style>
