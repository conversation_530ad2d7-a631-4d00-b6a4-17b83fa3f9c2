import { request } from '~/utils/request'

//设置是否为主打产品
export function setLeading(data) {
	return request({
		// url: '/retailer/quote/getProBindCate?proId=' + id,
		url: '/retailer/page/editPageLeading',
		method: 'post',
		data
	})
}
//获取经销商网站绑定报价类别
export function getProBindCate(id) {
	return request({
		url: '/retailer/quote/getProBindCate?proId=' + id,
		method: 'get',
	})
}
//发布或隐藏页面
export function changePublishStatus(data) {
	return request({
		url: '/retailer/page/changePublishStatus',
		method: 'post',
		data,
	})
}
//排序
export function movePageOrder(data) {
	return request({
		url: '/retailer/page/movePageOrder',
		method: 'post',
		data,
	})
}

//根据风格id获取对应自定义模板页
export function getCustomPageSampleList(data) {
	return request({
		url: 'retailer/websiteStyle/websiteStylePage/getCustomPageSampleList',
		method: 'post',
		data,
	})
}

//修改SEO信息
export function editPageTDK(data) {
	return request({
		url: '/retailer/page/editPageTDK',
		method: 'post',
		data,
	})
}

//删除
export function deleteCustomPage(data) {
	return request({
		url: '/retailer/page/deleteCustomPage',
		method: 'post',
		data,
	})
}

//获取所有半定制子分类页面或产品页面
export function listAllProductOrSubCatePage(data) {
	return request({
		url: '/retailer/page/listAllProductOrSubCatePage',
		method: 'post',
		data,
	})
}

//获取所有半定制子分类页面或产品页面
export function getIndependentDomian(proId) {
	return request({
		url: `/retailer/language/getIndependentDomianCountryList?proId=${proId}`,
		method: 'get'
	})
}

//更新非Default的logo
export function updateLanguageById(data) {
	return request({
		url: '/retailer/language/updateLanguageById',
		method: 'post',
		data,
	})
}

//更新Default的logo
export function updateSystemProjectById(data) {
	return request({
		url: '/retailer/webSite/updateSystemProjectById',
		method: 'post',
		data,
	})
}

//获取页面弹窗图片列表
export function listPageImgByPageId(data) {
	return request({
		url: '/retailer/page/listPageImgByPageId',
		method: 'post',
		data,
	})
}


//根据id删除页面弹窗图片
export function delPageImgById(id) {
	return request({
		url: '/retailer/page/delPageImgById?id='+id,
		method: 'get',
	})
}

//新增编辑页面弹窗图片
export function saveOrUpdatePageImg(data) {
	return request({
		url: '/retailer/page/saveOrUpdatePageImg',
		method: 'post',
		data,
	})
}

//获取项目结构化数据
export function getProjectStructuringByProId(data) {
	return request({
		url: `/retailer/page/getProjectStructuringByProId`,
		method: 'get',
		params: data,
	})
}

//编辑项目结构化数据
export function saveOrUpdateProjectStructuring(data) {
	return request({
		url: `/retailer/page/saveOrUpdateProjectStructuring`,
		method: 'post',
		data,
	})
}
