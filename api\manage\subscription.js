import { request } from '~/utils/request'

//查询订阅计划列表
export function getSubscribeList(retailerId,id) {
	return request({
		url: '/retailer/subscribe/list/'+ retailerId + '?proId='+ id,
		method: 'get',
	})
}

//删除订阅计划
export function deleteSubscribe(id) {
	return request({
		url: '/retailer/subscribe/delById?id='+ id,
		method: 'get',
	})
}

//查询订阅计划详情
export function getSubscribeDetail(id) {
	return request({
		url: '/retailer/subscribe/getById?id='+ id,
		method: 'get',
	})
}
