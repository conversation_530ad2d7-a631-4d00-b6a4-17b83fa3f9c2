<template>
	<div class="RibbonOptinos">
		<div v-for="kitem in List" :key="kitem.id" :class="{ gridBox: parentName == 'Custom Ribbon' }">
			<div class="topBox">
				<span class="tec">{{ kitem.alias }}</span>
				<div class="shape-box">
					<div
						@click="selectCitemFun(citem)"
						class="shape-item"
						v-for="citem in kitem.childList"
						:key="citem.id"
						:class="{
							active: citem.id == (bindValue.sizeValue && bindValue.sizeValue.id),
						}"
					>
						<span>{{ citem.alias }}</span>
						<div class="circle">
							<div class="inner-circle"></div>
						</div>
					</div>
				</div>
			</div>
			<div class="additionalBox" v-if="objHasValue(bindValue.sizeValue) && parentName == 'Stock Ribbon'">
				<template v-for="forName in bindValue.sizeValue.childList">
					<span class="tec">{{ forName.alias }}</span>
					<div class="cards-group">
						<div
							v-for="citem in forName.childList"
							:key="citem.id"
							class="card"
							:class="{
								active: citem.id == (bindValue.colorValue && bindValue.colorValue.id),
							}"
							@click="cardsFun(citem)"
						>
							<div
								class="card-heart"
								:style="{
									backgroundImage: 'url(' + filterImage(citem.imageJson) + ')',
								}"
							></div>
							<i class="el-icon-check"></i>
						</div>
					</div>
				</template>
			</div>
			<div class="commentBox" v-if="objHasValue(bindValue.sizeValue) && parentName == 'Custom Ribbon'">
				<span class="tec">{{ lang.Comments }}</span>
				<el-input type="textarea" v-model="bindValue.remark"></el-input>
			</div>
			<div class="UploadBox" v-if="objHasValue(bindValue.sizeValue) && parentName == 'Custom Ribbon'">
				<span class="tec">{{ lang.Upload }}</span>
				<div class="area">
					<div class="left">
						<button
							class="uploadBtn"
							type="button"
							@click="openWidgetBtn"
							:disabled="bindValue.later"
							:class="{
								disabled: bindValue.later,
							}"
							:style="{
								opacity: !bindValue.later ? 1 : 0.5,
							}"
						>
							{{ lang.Upload }}
							<el-tooltip popper-class="cusToolTip" effect="light" :content="lang.content2" placement="top-start">
								<b class="icon-wenhao1 tip-icon"></b>
							</el-tooltip>
						</button>
						<div class="uploadList" v-if="bindValue.uploadList && bindValue.uploadList.length > 0">
							<ul>
								<li v-for="(citem, cindex) in bindValue.uploadList" class="uploadItem" :key="cindex">
									<span>{{ citem.original_filename }}</span>
									<div class="fileBox">
										<b class="icon-check myIcon" style="color: #0cbd5f"></b>
										<el-button style="border: none; padding: 5px" icon="el-icon-delete" :disabled="bindValue.later" @click="delUploadImg(bindValue.uploadList, cindex)"></el-button>
									</div>
								</li>
							</ul>
						</div>
					</div>
					<div class="right">
						<el-checkbox :label="lang.Uploadbyemaillater" v-model="bindValue.later" @change="laterFun(bindValue.later)" />
					</div>
				</div>
			</div>
			<div class="theBtn">
				<el-button class="myBtn" @click="nextStepFun" v-if="objHasValue(bindValue.colorValue) || bindValue.later || (bindValue && bindValue.uploadList && bindValue.uploadList.length > 0)">
					{{ lang.next }}
				</el-button>
			</div>
		</div>
		<input type="file" ref="uploadRibbon" :accept="acceptFileType" multiple @change="uploadPic" hidden />
	</div>
</template>

<script>
import { uploadFile } from "@/utils/oss";
import { checkFile, acceptFileType } from "@/utils/validate";

export default {
	name: "RibbonOptinos",
	props: ["List", "parentName", "bindValue"],
	components: {},
	data() {
		return {
            acceptFileType
        };
	},
	methods: {
		ribbonOption() {
			this.openWidgetBtn();
			this.$store.commit("setSizeDialog", false);
		},
		laterFun(Bo) {
			if (Bo) {
				this.bindValue.uploadList = [];
			}
		},
		delUploadImg(arr, ind) {
			arr.splice(ind, 1);
			this.$forceUpdate();
		},
		uploadPic(event, type = "upload") {
			this.$gl.show();
			let files = type === "upload" ? event.target.files : event;
			let fileResult = checkFile(Array.from(files), this.acceptFileType, 80);
			if (!fileResult) {
				this.$toast.error("File type error");
				this.$gl.hide();
				return false;
			}
			if (fileResult.nomalSize.length == 0) {
				this.$gl.hide();
				this.$store.commit("setSizeDialog", true);
				this.$store.commit("setInputRefName", "ribbonOption");
				this.$store.commit("setOverSizeList", fileResult.overSize);
				this.$refs.uploadRibbon.value = "";
				return false;
			}
			let uploadPromise = [];
			fileResult.nomalSize.forEach((file) => {
				let promise = uploadFile(file).then((res) => {
					let temp = {
						original_filename: file.name,
						secure_url: res,
					};
					this.bindValue.uploadList ? this.bindValue.uploadList.push(temp) : (this.bindValue.uploadList = [temp]);
				});
				uploadPromise.push(promise);
			});
			Promise.all(uploadPromise).then(() => {
				this.$gl.hide();
				this.$refs.uploadRibbon.value = "";
				if (fileResult.overSize.length > 0) {
					this.$store.commit("setSizeDialog", true);
					this.$store.commit("setInputRefName", "ribbonOption");
					this.$store.commit("setOverSizeList", fileResult.overSize);
				}
				this.$forceUpdate();
			});
		},
		objHasValue(obj) {
			if (obj) {
				if (Object.values(obj).length > 0) {
					return true;
				} else {
					return false;
				}
			}
		},
		openWidgetBtn() {
			this.$refs.uploadRibbon.click();
		},
		nextStepFun() {
			this.$emit("nextStepFun");
		},
		selectCitemFun(val) {
			this.bindValue.sizeValue = val;
			this.bindValue.colorValue = null;
			this.$forceUpdate();
			this.$emit("update");
		},
		cardsFun(val) {
			this.bindValue.colorValue = val;
			this.$forceUpdate();
			this.$emit("update");
		},
		filterImage(val) {
			if (this.isJSON(val)) {
				return JSON.parse(val)[0].url;
			} else {
				return val.url;
			}
		},
		isJSON(str) {
			if (typeof str == "string") {
				try {
					var obj = JSON.parse(str);
					if (typeof obj == "object" && obj) {
						return true;
					} else {
						return false;
					}
				} catch (e) {
					return false;
				}
			} else {
				return false;
			}
		},
	},
	computed: {
		lang() {
			return this.$store.getters.lang.quote || {};
		},
	},
	mounted() {
		this.$Bus.$on("ribbonOption", this.ribbonOption);
	},
	beforeDestroy() {
		this.$Bus.$off("ribbonOption");
	},
};
</script>

<style scoped lang="scss">
.RibbonOptinos {
	border: 1px solid #e6e6e6;
	padding: 20px;
	margin-top: 10px;

	::v-deep .el-checkbox__input.is-checked + .el-checkbox__label {
		color: $color-primary;
	}

	::v-deep .el-checkbox__input.is-checked .el-checkbox__inner {
		background-color: $color-primary;
		border-color: $color-primary;
	}

	::v-deep .el-checkbox__inner:hover {
		border-color: $color-primary;
	}

	.tec {
		font-size: 18px;
		font-weight: 400;
		color: #666666;
	}

	.topBox {
		.shape-box {
			margin-top: 15px;
			display: grid;
			grid-template-columns: repeat(5, 1fr);
			gap: 10px;
			justify-content: space-between;

			.shape-item {
				position: relative;
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				height: 60px;
				border: 1px solid #e6e6e6;
				border-radius: 5px;
				padding: 8px 0;
				cursor: pointer;

				.circle {
					background: #fff;
					position: absolute;
					left: 50%;
					bottom: 0;
					transform: translate(-50%, 100%);
					width: 28px;
					height: 15px;
					border: 1px solid #e9ecf0;
					border-top: none;
					border-radius: 0 0 16px 16px;
					z-index: 10;
					transition: all 0.3s;

					.inner-circle {
						position: absolute;
						left: 50%;
						top: 0;
						transform: translate(-50%, -50%);
						width: 18px;
						height: 18px;
						border-radius: 50%;
						background: #fff;
						border: 1px solid #aaaeb3;
						transition: all 0.3s;

						&::after {
							content: "";
							position: absolute;
							left: 50%;
							top: 50%;
							transform: translate(-50%, -50%);
							width: 6px;
							height: 6px;
							background-color: #aaaeb3;
							border-radius: 50%;
							transition: all 0.3s;
						}
					}
				}

				&.active {
					position: relative;
					border-color: $color-primary;

					.circle {
						border-color: $color-primary;

						.inner-circle {
							border-color: $color-primary;

							&::after {
								background-color: $color-primary;
							}
						}
					}
				}

				@media (any-hover: hover) {
					&:hover {
						position: relative;
						border-color: $color-primary;

						.circle {
							border-color: $color-primary;

							.inner-circle {
								border-color: $color-primary;

								&::after {
									background-color: $color-primary;
								}
							}
						}
					}
				}
			}
		}
	}

	.commentBox {
		::v-deep .el-textarea {
			height: 100%;
			margin-top: 15px;

			.el-textarea__inner {
				height: 100%;
			}
		}
	}

	.additionalBox {
		margin-top: 30px;

		.cards-group {
			margin-top: 15px;
			display: grid;
			grid-template-columns: repeat(auto-fill, 38px);
			gap: 10px;

			.card {
				width: 38px;
				height: 38px;
				background: #e3eeff;
				border: 1px solid #e6e6e6;
				border-radius: 50%;
				display: flex;
				align-items: center;
				justify-content: center;
				position: relative;

				.card-heart {
					width: 30px;
					height: 30px;
					background-size: contain;
					background-position: center;
					border-radius: 50%;

					& + i {
						position: absolute;
						left: 50%;
						top: 50%;
						transform: translate(-50%, -50%);
						display: none;
						font-size: 16px;
						font-weight: bold;
						color: white;
					}
				}

				&.active {
					border: 1px solid $color-primary;
					box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.25);

					.card-heart + i {
						display: block;
					}
				}
			}
		}
	}

	.UploadBox {
		.area {
			display: grid;
			grid-template-columns: 1fr 1fr;
			margin-top: 15px;
			gap: 15px;

			.left {
				.uploadBtn {
					width: 100%;
					height: 40px;
					background: #fff;
					border-radius: 10px;
					border: none;
					color: $color-primary;
					border: 1px solid $color-primary;
					font-size: 18px;
					font-weight: 700;
				}

				.uploadList {
					width: 100%;
					height: 100px;
					overflow: auto;
					margin-bottom: 10px;
					text-align: center;

					ul {
						margin-left: 0;

						.uploadItem {
							display: flex;
							justify-content: space-between;
							align-items: center;
							padding: 5px;
							font-size: 14px;

							span {
								word-break: break-word;
								text-align: left;
								padding-right: 5px;
							}

							.fileBox {
								flex-shrink: 0;
							}

							.myIcon {
								margin: 0 4px;
							}

							button {
								border: none;
								padding: 5px;
							}
						}
					}
				}
			}

			.right {
				padding-top: 10px;

				::v-deep el-checkbox {
					margin-top: 10px;
				}
			}
		}
	}

	.theBtn {
		text-align: center;
		margin-top: 30px;

		.myBtn {
			background: $color-primary;
			border-radius: 10px;
			font-size: 18px;
			font-family: Calibri;
			font-weight: 400;
			color: #fff;
			padding: 10px 65px;
			max-width: 200px;
		}
	}

	.gridBox {
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 15px;

		.shape-box {
			grid-template-columns: repeat(2, 1fr);
		}

		.commentBox {
			grid-column: 2/3;
			grid-row: 1/3;
		}

		.theBtn {
			grid-column: 1/3;
		}
	}
}
</style>
