<template>
	<div id="lanyard" v-bind="$attrs" :class="{ lightUpLanyards: lanyardType === 'lightUpLanyards' }">
		<CanvasFactory ref="CanvasFactory" class="canvasFactory" :lanyardType="lanyardType" :printingData="printingData" :tabsName="tabsName" :canvasData="canvasData" :tampInfo="selectedData['Design Your Printing']" :fugaiImage="fugaiImage" :fugaiImage2="fugaiImage2" :fugaiImage3="fugaiImage3" :danImage="danImage" :danImageB="danImageB" :shuangeImage="shuangeImage" @canvasToImage="canvasToImageFun"></CanvasFactory>
		<QuoteNav :pid="pid" title="We provide these Lanyards types for you." style="margin: 10px 0"></QuoteNav>
		<article class="content">
			<div class="leftArea" id="leftArea">
				<div class="advertisingBanner" v-if="isFullReductionActivity">
					<div>
						{{ lang.lanyard.FullCopy1 }} {{ satisfiedQuantity }} {{ lang.lanyard.FullCopy7 }}
						<span>{{ lang.lanyard.FullCopy11 }} {{ giftQuantity1 }} {{ lang.lanyard.FullCopy8 }} </span>
					</div>
				</div>
				<div v-for="(item, index) in filterShowGeneralData" :key="item.id" class="kk" :class="{ type1: picDialog }">
					<!-- one What Suits Your Needs 0 -->
					<div v-if="item.paramName == 'Products Categories'" class="part Products-Categories" :id="item.paramName" :class="{ 'step-active': item.paramName == currentStep }">
						<h3 class="step-title">
							<span>{{ lang.Step }} {{ item.customIndex }}: </span>
							{{ item.alias ? item.alias : item.paramName }}
						</h3>
						<div class="boxContent">
							<LanyardCheckBox2 v-for="itemChild in item.childList" :key="itemChild.id" :bindValue="itemChild" :aspectRatio="'aspect-ratio: 330/330'" :selectedData="selectedData" :bindName="item.paramName" @clickFun="productsCategoriesFun($event,item.customIndex)" @picDialogFun="picDialogFun"></LanyardCheckBox2>
						</div>
						<i v-if="item.paramName == currentStep" class="el-icon-close" @click="shutMask"></i>
					</div>
					<!-- two Select Lanyard Style 1-->
					<div v-if="item.paramName == 'Lanyard Style' && medalStyleData.childList.length > 1" class="part Select-Medal-Style" :id="item.paramName" :class="{ 'step-active': item.paramName == currentStep, hideContent: hideContent }">
						<h3 class="step-title">
							<span>{{ lang.Step }} {{ item.customIndex }}: </span>
							{{ lang.Select }} {{ item.alias ? item.alias : item.paramName }}
							<span v-if="hideContent" style="color: #999999">: {{ lanyardStyleName }}</span>
						</h3>
						<div class="boxContent">
							<LanyardCheckBox2 v-for="(itemChild, itemIndex) in item.childList" :key="itemChild.id" :bindValue="itemChild" :selectedData="selectedData" :bindName="item.paramName" imageValue="imagePhoto" @clickFun="selectFun($event,itemIndex,item,'ty')" @picDialogFun="picDialogFun" @zoomPicFun="zoomPicFun($event, itemChild, 'video')"></LanyardCheckBox2>
						</div>
						<i v-if="item.paramName == currentStep" class="el-icon-close" @click="shutMask"></i>
					</div>
					<!-- three Select Lanyard Size 2-->
					<div v-if="item.paramName == 'Lanyard Width' && item.show && lanyardType != 'neopreneLanyards'" class="part Lanyard-Size" :id="item.paramName" :class="{ 'step-active': item.paramName == currentStep }">
						<h3 class="step-title">
							<span>{{ lang.Step }} {{ item.customIndex }}: </span>
							{{ lang.Select }} {{ lang.lanyard.h3 }}
						</h3>
						<div class="boxContent">
							<WidthAndLength :selectedData="selectedData" :class="showWidthMask ? 'shadowMask' : null" :bindName="item.paramName" :bindValue="item" :num="formatSubIndex(item.customIndex, 1)" @clickFun="selectFun($event,1,item)"></WidthAndLength>
							<WidthAndLength :selectedData="selectedData" :class="showMask ? null : 'shadowMask'" :bindName="lanyardLengthData.paramName" :bindValue="lanyardLengthData" :num="formatSubIndex(item.customIndex, 2)" @clickFun="selectFun($event,2,item)"></WidthAndLength>
						</div>
						<i v-if="item.paramName == currentStep" class="el-icon-close" @click="shutMask"></i>
					</div>
					<div v-if="item.paramName == 'Lanyard Popular Colors'" class="part Lanyard-Popular-Colors" :id="item.paramName" :class="{ 'step-active': item.paramName == currentStep }">
						<h3 class="step-title">
							<span>{{ lang.Step }} {{ item.customIndex }}: </span>
							{{ lang.Select }} {{ item.alias ? item.alias : item.paramName }}
						</h3>
						<div class="boxContent">
							<template v-if="lanyardType === 'twoToneLanyards'">
								<two-tone-color-picker ref="colorPicker" :bindValue="item" :key="colorKey" :bindValueDesign="bindValueDesign" :selectedData="selectedData" :tabsName.sync="tabsName" :noDesignTab="noDesignTab" @currentCustomCardItem="customCardObjFun($event,item)" @currentPopularCardItem="popularCardObjFun($event,item)"></two-tone-color-picker>
							</template>
							<template v-else>
								<ColorPicker ref="colorPicker" :bindValue="item" :key="colorKey" :bindValueDesign="bindValueDesign" :selectedData="selectedData" :tabsName.sync="tabsName" :noDesignTab="noDesignTab" :no-custom-tab="noCustomTab" @currentCustomCardItem="customCardObjFun($event,item)" @currentPopularCardItem="popularCardObjFun($event,item)"></ColorPicker>
							</template>
							<!-- 活动 -->
							<div class="activity" :style="backgroundColorVal ? 'background-color: #E9F5FF;color:#333333' : 'background-color: #EBEBEB;'" v-if="isFullReductionActivity">
								<div class="activity-text">
									<div class="activity-text-1">
										<el-checkbox v-model="checked" :disabled="getcurrentPopularCardList(currentPopularCardList) < satisfiedQuantity"></el-checkbox>
										<div>
											<span>*&nbsp;{{ lang.lanyard.FullCopy2 }} {{ giftQuantity1 }} {{ lang.lanyard.FullCopy3 }}</span> {{ lang.lanyard.FullCopy4 }}
										</div>
									</div>
									<div class="activity-text-2" v-show="!checked && getcurrentPopularCardList(currentPopularCardList) <= satisfiedQuantity">
										{{ lang.lanyard.FullCopy5 }} <span>{{ lang.lanyard.FullCopy6 }}</span>
									</div>
								</div>
								<div v-if="checked && getcurrentPopularCardList(currentPopularCardList) >= satisfiedQuantity" style="margin-top: 15px">
									<div style="font-weight: 400; color: #333333; font-size: 15px">
										{{ lang.lanyard.FullCopy14 }}
										<span style="color: red">{{ lang.lanyard.FullCopy15 }}</span>
										{{ lang.lanyard.FullCopy9 }}:
									</div>
									<div class="box custom-scrollbar">
										<div v-for="(i, idx) in currentPopularCardList" :key="idx" class="activity-list custom-shadow2" :class="{ active: i.giftQuantity && i.giftQuantity > 0 }">
											<div :class="{ active2: i.giftQuantity && i.giftQuantity > 0 }" class="xuanzhong">√</div>
											<div class="activity-img">
												<div
													v-if="!i.isTwoToneCustom"
													:style="{
														backgroundColor: i.code,
														backgroundImage: i.imageJson && JSON.parse(i.imageJson)[0] ? 'url(' + JSON.parse(i.imageJson)[0].url + ')' : '',
													}"
												></div>
												<div v-else :style="{ background: i.code }"></div>
											</div>
											<div class="p2" v-if="!i.isTwoToneCustom">
												<div>
													{{ i.alias ? i.alias : i.pantone }}
												</div>
												<div v-if="i.alias2">
													{{ i.alias2 }}
												</div>
											</div>
											<div class="p2 isTwoToneCustom" v-else>
												<div>{{ i.mainColor.pantone }}</div>
												<div>{{ i.accentColor.pantone }}</div>
											</div>
											<div class="p3">
												<input :controls="false" class="myInput" onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode)))" type="number" v-model.number="i.giftQuantity" placeholder="QTY" @input="i.giftQuantity = $event.target.value" @change="presentedQuantityChange(i.giftQuantity)" />
											</div>
										</div>
									</div>
									<div style="margin: 15px 0 10px; font-size: 15px">{{ lang.lanyard.FullCopy10 }}</div>
								</div>
							</div>
							<div class="text-center">
								<el-button
									class="myBtn"
									@click="nextStepFun(item.paramName)"
									:style="{
										opacity: selectedData['Lanyard Design Colors']?.length > 0 || selectedData['Lanyard Popular Colors']?.length > 0 || selectedData['color card']?.length > 0 || presentedQuantity > giftQuantity1 ? 1 : 0.5,
									}"
									:disabled="(!selectedData['Lanyard Design Colors']?.length > 0 && !selectedData['Lanyard Popular Colors']?.length > 0 && !selectedData['color card']?.length > 0) || presentedQuantity > giftQuantity1"
									>{{ lang.next }}
								</el-button>
							</div>
							<div>
								<el-alert :title="`${lang.lanyard.FullCopy12} ${giftQuantity1} ${lang.lanyard.FullCopy13}`" type="error" :closable="false" v-show="presentedQuantity > giftQuantity1 && getcurrentPopularCardList(currentPopularCardList) >= satisfiedQuantity"></el-alert>
							</div>
						</div>

						<i v-if="item.paramName == currentStep" class="el-icon-close" @click="shutMask"></i>
					</div>

					<!-- 闪粉独有步骤 Select Your Glitter Colors & Glitter Lanyards Printing Colors -->
					<div v-if="item.paramName == 'Select Your Glitter Colors'" class="part Lanyard-Glitter-Style" :id="item.paramName" :class="{ 'step-active': item.paramName == currentStep }">
						<h3 class="step-title">
							<span>{{ lang.Step }} {{ item.customIndex }}: </span>
							{{ lang.Select }} {{ item.alias ? item.alias : item.paramName }}
						</h3>
						<div class="boxContent">
							<template v-for="itemChild in item.childList">
								<LanyardCheckBox v-show="itemChild" :bindValue="itemChild" innerBorder :selectedData="selectedData" :bindName="item.paramName" customShadow showPrice :aspectRatio="'aspect-ratio: 240 /120'" @clickFun="selectFun($event,-1,item)" @picDialogFun="picDialogFun"></LanyardCheckBox>
							</template>
						</div>
						<i v-if="item.paramName == currentStep" class="el-icon-close" @click="shutMask"></i>
					</div>
					<div v-if="item.paramName == 'Glitter Lanyards Printing Colors'" class="part Lanyard-Glitter-Printing-Style" :id="item.paramName" :class="{ 'step-active': item.paramName == currentStep }">
						<h3 class="step-title">
							<span>{{ lang.Step }} {{ item.customIndex }}: </span>
							{{ lang.Select }} {{ item.alias ? item.alias : item.paramName }}
						</h3>
						<div class="boxContent">
							<template v-for="itemChild in item.childList">
								<LanyardCheckBox v-show="itemChild" :bindValue="itemChild" innerBorder :selectedData="selectedData" :bindName="item.paramName" customShadow showPrice :aspectRatio="'aspect-ratio: 170/186'" @clickFun="selectFun($event,-1,item)" @picDialogFun="picDialogFun"></LanyardCheckBox>
							</template>
						</div>
						<i v-if="item.paramName == currentStep" class="el-icon-close" @click="shutMask"></i>
					</div>

					<!-- 橡胶独有步骤 Select Printing Method -->
					<div v-if="item.paramName == 'Select Printing Method'" class="part Select-Printing-Method" :id="item.paramName" :class="{ 'step-active': item.paramName == currentStep }">
						<h3 class="step-title">
							<span>{{ lang.Step }} {{ item.customIndex }}: </span>
							{{ lang.Select }} {{ item.alias ? item.alias : item.paramName }}
						</h3>
						<div class="boxContent">
							<TabsSample :bindValue="item" :selectedData="selectedData" :bindName="item.paramName" :defActiveName="defActiveAttachment" @clickFun="selectPritingMethodFun($event,item)"></TabsSample>
						</div>
						<div class="text-center">
							<el-button
								class="myBtn"
								@click="nextStepFun(item.paramName)"
								:style="{
									opacity: selectedData[item.paramName]?.length > 0 ? 1 : 0.5,
								}"
								:disabled="!selectedData[item.paramName]?.length > 0"
								>{{ lang.next }}
							</el-button>
						</div>
						<i v-if="item.paramName == currentStep" class="el-icon-close" @click="shutMask"></i>
					</div>

					<!-- five Select Lanyard Attachment4 -->
					<div v-if="item.paramName == 'Lanyard Attachment' && lanyardType !== 'lightUpLanyards'" class="part Lanyard-Attachment" :id="item.paramName" :class="{ 'step-active': item.paramName == currentStep }">
						<h3 class="step-title">
							<span>{{ lang.Step }} {{ item.customIndex }}: </span>
							{{ lang.Select }} {{ item.alias ? item.alias : item.paramName }}
						</h3>
						<div class="boxContent">
							<Tabs :bindValue="item" :selectedData="selectedData" :bindName="item.paramName" :defActiveName="defActiveAttachment" :sameAsTheLeftAttachement="sameAsTheLeftAttachement" @changeSameForAttachment="changeSameForAttachment" @clickFun="selectTabsFun(...arguments,item)" @ropeCross="ropeCrossFun"></Tabs>
						</div>
						<div class="text-center">
							<el-button
								class="myBtn"
								@click="nextStepFun(item.paramName)"
								:style="{
									opacity: selectedData[item.paramName]?.length > 0 ? 1 : 0.5,
								}"
								:disabled="!selectedData[item.paramName]?.length > 0"
								>{{ lang.next }}
							</el-button>
						</div>
						<i v-if="item.paramName == currentStep" class="el-icon-close" @click="shutMask"></i>
					</div>
					<!--light up,phone织带配件没有三级-->
					<div v-if="item.paramName == 'Lanyard Attachment' && lanyardType === 'lightUpLanyards'" class="part Lanyard-Attachment-lightUp" :id="item.paramName" :class="{ 'step-active': item.paramName == currentStep }">
						<h3 class="step-title">
							<span>{{ lang.Step }} {{ item.customIndex }}: </span>
							{{ lang.Select }} {{ item.alias ? item.alias : item.paramName }}
						</h3>
						<div class="boxContent">
							<template v-for="itemChild in item.childList">
								<LanyardCheckBox freeTitle :showPrice="false" innerBorder :bindValue="itemChild" :selectedData="selectedData" :bindName="item.paramName" customShadow :aspectRatio="'aspect-ratio: 186/126'" @clickFun="selectFun($event,-1,item)" @picDialogFun="picDialogFun"></LanyardCheckBox>
							</template>
						</div>
						<i v-if="item.paramName == currentStep" class="el-icon-close" @click="shutMask"></i>
					</div>
					<!-- six Design Your Printing 5-->
					<div v-if="item.paramName == 'Lanyard Stitch Style'" class="part Lanyard-Stitch-Style" :id="item.paramName" :class="{ 'step-active': item.paramName == currentStep }">
						<h3 class="step-title">
							<span>{{ lang.Step }} {{ item.customIndex }}: </span>
							{{ lang.Select }} {{ item.alias ? item.alias : item.paramName }}
						</h3>
						<div class="boxContent">
							<template v-for="itemChild in item.childList">
								<LanyardCheckBox v-show="itemChild.show" innerBorder :bindValue="itemChild" :selectedData="selectedData" :bindName="item.paramName" customShadow showPrice :aspectRatio="'aspect-ratio: 170/186'" @clickFun="selectFun($event,-1,item)" @picDialogFun="picDialogFun"></LanyardCheckBox>
							</template>
						</div>
						<i v-if="item.paramName == currentStep" class="el-icon-close" @click="shutMask"></i>
					</div>
					<!-- 7 -->
					<div v-if="item.paramName == 'Additional Options'" class="part Additional-Options" :id="item.paramName" :class="{ 'step-active': item.paramName == currentStep }">
						<h3 class="step-title">
							<span>{{ lang.Step }} {{ item.customIndex }}: </span>
							{{ item.alias ? item.alias : item.paramName }}
						</h3>
						<div class="boxContent">
							<LanyardCheckBox v-for="itemChild in item.childList" innerBorder :key="itemChild.id" :bindValue="itemChild" :aspectRatio="'aspect-ratio: 304/200'" :selectedData="selectedData" :bindName="item.paramName" customShadow showPrice @clickFun="selectFun($event,-1,item)" @picDialogFun="picDialogFun"></LanyardCheckBox>
						</div>
						<i v-if="item.paramName == currentStep" class="el-icon-close" @click="shutMask"></i>
					</div>
					<!-- 8 -->
					<div v-if="item.paramName == 'Select Packaging'" class="part Select-Packaging" :id="item.paramName" :class="{ 'step-active': item.paramName == currentStep }">
						<h3 class="step-title">
							<span>{{ lang.Step }} {{ item.customIndex }}:</span>
							{{ lang.Select + " " + (item.alias ? item.alias : item.paramName) }}
						</h3>
						<div class="boxContent">
							<LanyardCheckBox v-for="itemChild in item.childList" innerBorder :key="itemChild.id" :bindValue="itemChild" :selectedData="selectedData" :bindName="item.paramName" customShadow @clickFun="selectFun($event,-1,item)" @picDialogFun="picDialogFun" showPrice></LanyardCheckBox>
						</div>
						<i v-if="item.paramName == currentStep" class="el-icon-close" @click="shutMask"></i>
					</div>

					<!-- 新步骤 只有3D类型有 -->
					<div v-if="item.paramName == 'Silicone Printing Colors'" class="part Select-Silicone-Printing-Colors" :id="item.paramName" :class="{ 'step-active': item.paramName == currentStep }">
						<h3 class="step-title">
							<span>{{ lang.Step }} {{ item.customIndex }}: </span>
							{{ lang.Select }} {{ item.alias ? item.alias : item.paramName }}
						</h3>
						<div class="boxContent">
							<LanyardCheckBox v-for="itemChild in item.childList" innerBorder :key="itemChild.id" :bindValue="itemChild" :aspectRatio="'aspect-ratio: 304/200'" :selectedData="selectedData" :bindName="item.paramName" :totalQuantity="getcurrentPopularCardList(currentPopularCardList)" customShadow showPrice @clickFun="selectFun($event,-1,item)" @picDialogFun="picDialogFun"></LanyardCheckBox>
						</div>
						<i v-if="item.paramName == currentStep" class="el-icon-close" @click="shutMask"></i>
					</div>

					<!-- 9 -->
					<div v-if="item.paramName == 'Select Your Card' && item.show" class="part Select-Your-Card" :id="item.paramName" :class="{ 'step-active': item.paramName == currentStep }">
						<h3 class="step-title">
							<span>{{ lang.Step }} {{ item.customIndex }}:</span>
							{{ lang.Select + " " + (item.alias ? item.alias : item.paramName) }}
						</h3>
						<div class="boxContent">
							<template v-for="(itemChild, itemIndex) in item.childList">
								<CardBox v-if="itemChild.show" :ref="'CardBox' + itemChild.id" :key="itemChild.id" :bindValue="item.childList[itemIndex]" customShadow :selectedData="selectedData" :bindName="item.paramName" @openWidget="openWidget" @beforeAfterFun="beforeAfterFun" :cardUploadList.sync="cardUploadList" :cardBoxId="itemChild.id" @clickFun="selectFun($event,-1,item)" @picDialogFun="picDialogFun" @updateUploadList="updateUploadListCard"></CardBox>
							</template>
						</div>
						<div class="text-center" v-if="item.paramName == currentStep">
							<el-button
								class="myBtn"
								@click="nextStepFun(item.paramName)"
								:style="{
									opacity: selectedData[item.paramName]?.length > 0 ? 1 : 0.5,
								}"
								:disabled="!selectedData[item.paramName]?.length > 0"
								>{{ lang.next }}
							</el-button>
						</div>
						<i v-if="item.paramName == currentStep" class="el-icon-close" @click="shutMask"></i>
					</div>
					<!-- 10 -->
					<div v-if="item.paramName == 'Lanyard Badge Holder Options' && item.show" class="part Lanyard-Badge-Holder-Options" :id="item.paramName" :class="{ 'step-active': item.paramName == currentStep }">
						<h3 class="step-title">
							<span>{{ lang.Step }} {{ item.customIndex }}: </span>
							{{ lang.Select }} {{ item.alias ? item.alias : item.paramName }}
						</h3>
						<div class="boxContent">
							<SwiperSelectCard ref="badgeHolderOption" :bindValue="item" label="LanyardBadgeHolderOptions" :selectedData="selectedData" :bindName="item.paramName" :arr.sync="lanyardBadgeHolderOptionsArr" :top="device === 'mb' ? 2 : 4" @clickFun="selectFun($event,-1,item)"></SwiperSelectCard>
						</div>
						<div class="text-center" v-if="item.paramName == currentStep">
							<el-button
								class="myBtn"
								@click="nextStepFun(item.paramName)"
								:style="{
									opacity: selectedData['Lanyard Badge Holder Options'][0]?.childList[0]?.quantity > 0 ? 1 : 0.5,
								}"
								:disabled="!selectedData['Lanyard Badge Holder Options'][0]?.childList[0]?.quantity"
								>{{ lang.next }}
							</el-button>
						</div>
						<i v-if="item.paramName == currentStep" class="el-icon-close" @click="shutMask"></i>
					</div>
					<!-- 11 -->
					<div v-if="item.paramName == 'Badge Reels'" class="part Badge-Reels" :id="item.paramName" :class="{ 'step-active': item.paramName == currentStep }">
						<h3 class="step-title">
							<span>{{ lang.Step }} {{ item.customIndex }}: </span>
							{{ lang.Select }} {{ item.alias ? item.alias : item.paramName }}
						</h3>
						<div class="boxContent">
							<SwiperCard :bindValue="item" label="badgeReel" rotateYAnimation :selectedData="selectedData" :bindName="item.paramName" :arr.sync="badgeReelsArr" :top="device === 'mb' ? 3 : 5" @clickFun="selectFun($event,-1,item)"></SwiperCard>
						</div>
						<div class="text-center" v-show="showNextButton(item.childList)">
							<el-button
								class="myBtn"
								@click="nextStepFun(item.paramName)"
								:style="{
									opacity: badgeReelsArr.length > 0 ? 1 : 0.5,
								}"
								:disabled="!badgeReelsArr.length > 0"
								>{{ lang.next }}
							</el-button>
						</div>
						<i v-if="item.paramName == currentStep" class="el-icon-close" @click="shutMask"></i>
					</div>
					<!-- 12 -->
					<div v-if="item.paramName == 'Delivery Date'" class="part Delivery-Date" :id="item.paramName" :class="{ 'step-active': item.paramName == currentStep }">
						<h3 class="step-title">
							<span>{{ lang.Step }} {{ item.customIndex }}: </span>
							{{ lang.Select }} {{ item.alias ? item.alias : item.paramName }}
						</h3>
						<div class="step-text">
							{{ lang.hourstext }}
						</div>
						<div class="boxContent">
							<DateBox @selectDiscount="selectDiscount(...arguments,item)" :freeText="freeText" :calculateData="calculateData" :selectedData="selectedData" :bindValue="item" :proType="proType" :class="isActive ? 'active' : ''"></DateBox>
						</div>
						<i v-if="item.paramName == currentStep" class="el-icon-close" @click="shutMask"></i>
					</div>

					<!-- 13 -->
					<div v-if="item.paramName == 'Design Your Printing'" class="part Design-Your-Printing" :id="item.paramName" :class="{ 'step-active': item.paramName == currentStep }">
						<h3 class="step-title">
							<span>{{ lang.Step }} {{ item.customIndex }}: </span>
							{{ item.alias ? item.alias : item.paramName }}
						</h3>
						<div class="boxContent">
							<PrintTabs ref="PrintTabs" :lanyardType="lanyardType" :defaultColor="defaultColor" :bindValue="item" :bindValueSide="bindValueSide" :allColor="allColor" :selectedData="selectedData" :bindName="item.paramName" :fontImgCustom.sync="fontImgCustom" :showDetails.sync="showDetails" :fontBeforeImg.sync="fontBeforeImg" :fontAfterImg.sync="fontAfterImg" :beforeTempLogo="beforeTempLogo" :afterTempLogo="afterTempLogo" @beforeAfterFun="beforeAfterFun" @openWidget="openWidget" @canvasFontForm="canvasFontFormFun" @clickFun="selectFun($event,-1,item)" @printingData="printingDataFun" @clipartFun="clipartFun" @filterTampInfo="filterTampInfo" @updateUploadList="updateUploadList"></PrintTabs>
						</div>
						<div class="text-center">
							<el-button
								class="myBtn"
								@click.native="nextStepFun(item.paramName)"
								:style="{
									opacity: selectedData[item.paramName]?.length > 0 || copyCanvasData.commentsImgLater || fontImgCustom.length > 0 ? 1 : 0.5,
								}"
								:disabled="!selectedData[item.paramName]?.length > 0 && !copyCanvasData.commentsImgLater && !fontImgCustom.length > 0"
								>{{ lang.next }}
							</el-button>
						</div>
						<i v-if="item.paramName == currentStep" class="el-icon-close" @click="shutMask"></i>
					</div>
				</div>
			</div>
			<div class="rightArea" id="rightAreaCustom">
				<div class="stickyType step-active">
					<transition enter-active-class="animate__animated animate__faster animate__fadeInBottomLeft" leave-active-class="animate__animated animate__faster animate__fadeOutBottomLeft">
						<LanyardDetails v-show="showRightArea" @submitInquiry="submitInquiry" @addToCart="addToCart" @jump="jump" :showQty="false" :selectedData="selectedData" :generalData="generalData" :currencyList="currencyList" :calculateData="calculateData" :defaultData="defaultData" :textInfo="textInfo" :totalQuantity="totalQuantity" :uploadList="uploadList" :hasComment="hasComment" :showMore.sync="showMore" showColor @tabsName="getTabsName" @currentCustomCardItem="customCardObjFun" @currentPopularCardItem="popularCardObjFun" :presentedQuantity="presentedQuantity" :satisfiedQuantity="satisfiedQuantity" :giftQuantity1="giftQuantity1">
							<article class="canvas">
								<img :src="canvasToImage" alt="" loading="lazy" />
							</article>
						</LanyardDetails>
					</transition>
				</div>
			</div>
		</article>
		<article class="mFooter" id="foot">
			<div class="medalsDetails">
				<LanyardDetails @submitInquiry="submitInquiry" @addToCart="addToCart" @jump="jump" :showQty="true" :currencyList="currencyList" :calculateData="calculateData" :defaultData="defaultData" :textInfo="textInfo" :totalQuantity="totalQuantity" :uploadList="uploadList" :hasComment="hasComment" :showMore.sync="showMore" :selectedData="selectedData" :generalData="generalData" :ropFontFamily1="ropFontFamily1" :ropFontFamily2="ropFontFamily2" :ropeText1="ropeText1" :ropeText2="ropeText2" :fontAfterImg="fontAfterImg" :fontBeforeImg="fontBeforeImg" :ropeFontFillStyleName1="ropeFontFillStyleName1" :ropeFontFillStyleName2="ropeFontFillStyleName2" footer filter :presentedQuantity="presentedQuantity" :satisfiedQuantity="satisfiedQuantity" :giftQuantity1="giftQuantity1">
					<article class="canvas">
						<img :src="canvasToImage" alt="" loading="lazy" />
					</article>
				</LanyardDetails>
			</div>
		</article>
		<article @click.stop="shutMask" class="mask" :class="{ checking: checking }"></article>
		<el-dialog :destroy-on-close="true" :show-close="false" custom-class="commentDialog" :visible.sync="commentDialogVisible" :width="'300px'">
			<i class="el-icon-close" @click="shutComment"></i>
			<div class="isComment">
				<div class="textWrap">
					<div class="circle2"></div>
					<div>
						<p class="normal-text">{{ lang.RecommendedSize }}</p>
					</div>
				</div>
				<el-input type="textarea" :rows="4" :placeholder="lang.p23" v-model="commentTarget.comment"></el-input>
				<button type="button" @click="commentSubmit">{{ lang.ok }}</button>
			</div>
		</el-dialog>
		<el-dialog :visible.sync="dialogTips" :width="device == 'mb' ? '80%' : '20%'" :show-close="false" :close-on-press-escape="false" :close-on-click-modal="false">
			<span>{{ lang.lanyard.tips }} </span>
			<template #footer>
				<span class="dialog-footer">
					<el-button type="primary" @click="dialogTipsFun">{{ lang.ok }}</el-button>
				</span>
			</template>
		</el-dialog>
		<el-dialog :append-to-body="true" :visible.sync="allImgDialog" custom-class="allImgDialog" :width="device == 'mb' ? '100%' : '60%'" :title="lang.browseCliparts">
			<div class="allImgDialogBox">
				<div class="item" @click="imgPickerFun(item)" v-for="item in allImage" :title="item.pantone" :key="item.id">
					<img :src="item.imageUrl" :alt="item.imageName" loading="lazy" />
					<div class="btn">
						<el-button circle>
							<svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" data-v-ba633cb8="">
								<path fill="currentColor" d="M480 480V128a32 32 0 0 1 64 0v352h352a32 32 0 1 1 0 64H544v352a32 32 0 1 1-64 0V544H128a32 32 0 0 1 0-64h352z"></path>
							</svg>
						</el-button>
					</div>
				</div>
			</div>
		</el-dialog>
		<el-drawer :modal-append-to-body="false" :visible.sync="showDetails" :with-header="false" custom-class="drawDialog" :size="device === 'mb' ? '80%' : '800px'">
			<div class="rightArea rightFixedArea">
				<LanyardDetails @submitInquiry="submitInquiry" @addToCart="addToCart" @jump="jump" :showQty="false" :currencyList="currencyList" :showMore.sync="showMore" :calculateData="calculateData" :defaultData="defaultData" :textInfo="textInfo" :totalQuantity="totalQuantity" :uploadList="uploadList" :hasComment="hasComment" :selectedData="selectedData" :generalData="generalData" noDetails filter>
					<article class="canvas">
						<img :src="canvasToImage" alt="" loading="lazy" />
					</article>
				</LanyardDetails>
				<div class="fold-icon" @click="showDetails = false"><i class="el-icon-caret-left arrow"></i></div>
			</div>
		</el-drawer>
		<div class="preview" @click="showDetails = !showDetails" v-if="showRightArea">
			<b class="icon-preview"></b>
			<span>{{ lang.preview }}</span>
		</div>
		<BaseDialog v-model="showOtoDialog" :width="device != 'mb' ? '570px' : '90%'">
			<div class="otoWrap">
				<img src="@/assets/images/oto.png" alt="" loading="lazy" />
				<p>{{ lang.p1 }}</p>
				<h3>{{ lang.p2 }}</h3>
				<p style="color: #666666">
					{{ lang.p3 }}<br />
					{{ lang.p4 }}.
				</p>

				<div class="box">
					<p class="t1">
						{{ lang.p5 }} <br />
						{{ lang.p6 }}!
					</p>
					<a href="https://www.o2o.co/previewWeb" style="text-decoration: none">
						<el-button type="primary">{{ lang.p7 }}</el-button>
					</a>
				</div>
			</div>
		</BaseDialog>
		<VideoPreviewDialog :pic-dialog.sync="picDialog" :zoom-pic="zoomPic" :temp-type="tempType" :selected-params-value="selectedParamsValue" @qtyAndBtnConfirm="qtyAndBtnConfirm" @qtyAndBtnNext="qtyAndBtnNext" @dialogNextStep="dialogNextStep"></VideoPreviewDialog>
		<infoDialog :infoDialogVisible.sync="infoDialogVisible" :otherUpload="cardBoxFile ? fontImgCustom.concat(cardBoxFile) : fontImgCustom" :uploadList.sync="uploadList" @getValue="getValueFun"></infoDialog>
		<input type="file" ref="upload1" :accept="acceptFileType" @change="uploadPic" hidden />
		<input type="file" ref="upload2" :accept="acceptFileType" @change="uploadPic2" hidden />
		<input type="file" ref="upload3" :accept="acceptFileType" @change="uploadPicClipart" hidden />
		<BaseDialog v-model="noFileDialog" :width="device != 'mb' ? '485px' : '90%'" :model="false">
			<template #closeIcon>
				<div style="display: none"></div>
			</template>
			<infoUpload :infoUploadList="infoUploadList" @pushInfoList="pushInfoList" @delInfoList="delInfoList" @updateInquiry="updateInquiry" @closeInfoDialog="closeInfoDialog"></infoUpload>
		</BaseDialog>
	</div>
</template>
<script>
import "@/plugins/element";
import BaseDialog from "@/components/Quote/BaseDialog";
import VideoPlayer from "@/components/Quote/VideoPlayer.vue";
import DialogBM from "@/components/Medals/DialogBM";
import QtyAndBtn from "@/components/Medals/QtyAndBtn";
import Corner from "@/components/Medals/Corner";
import DateBox from "@/components/Medals/DateBox";
import infoDialog from "@/components/Medals/infoDialog";
import infoUpload from "@/components/Medals/infoUpload";
import PriceTable from "@/components/Lanyard/PriceTable";
import LanyardDetails from "@/components/Lanyard/LanyardDetails";
import CanvasFactory from "@/components/Lanyard/CanvasFactory";
import Tabs from "@/components/Lanyard/Tabs";
import TabsSample from "@/components/Lanyard/TabsSample.vue";
import PrintTabs from "@/components/Lanyard/PrintTabs";
import LanyardCheckBox from "@/components/Lanyard/LanyardCheckBox";
import LanyardCheckBox2 from "@/components/Lanyard/LanyardCheckBox2";
import CardBox from "@/components/Lanyard/CardBox";
import SwiperCard from "@/components/Lanyard/SwiperCard";
import SwiperSelectCard from "@/components/Lanyard/SwiperSelectCard.vue";
import WidthAndLength from "@/components/Lanyard/WidthAndLength";
import quoteBanChoiceMixins from "@/mixins/quoteBanChoice";
import quoteMixin from "@/mixins/quote";
//识图
import { analyzeImageColor, debounce, deepClone, getFileSuffix, scrollToViewTop, lockScroll, unlockScroll } from "@/utils/utils";

import { uploadFile } from "@/utils/oss";
import { medalsApi } from "@/api/medals/medals";
import { indexApi } from "@/api/lanyardQuote";
import { Loading } from "element-ui";
import { otoAddCart, otoEditInquiry, setInquiry } from "@/api/pins";
import { checkFile, acceptFileType } from "@/utils/validate";
import { getQuoteConfig } from "@/assets/js/quote/quoteConfig";
import TwoToneColorPicker from "@/components/Lanyard/twoToneColorPicker.vue";
import QuoteNav from "@/components/Medals/QuoteNav.vue";
import ColorPicker from "@/components/Lanyard/ColorPicker.vue";
import { dictModel, baseModel } from "./entity/lanyard";
import { findSelectDiscount, getIsSmallQty } from "@/assets/js/quote/quotePublic";
import VideoPreviewDialog from "@/components/Quote/VideoPreviewDialog.vue";

export default {
	components: {
		VideoPreviewDialog,
		QuoteNav,
		TwoToneColorPicker,
		LanyardCheckBox,
		LanyardCheckBox2,
		BaseDialog,
		VideoPlayer,
		DialogBM,
		QtyAndBtn,
		Corner,
		PriceTable,
		CanvasFactory,
		LanyardDetails,
		Tabs,
		TabsSample,
		PrintTabs,
		CardBox,
		SwiperCard,
		WidthAndLength,
		DateBox,
		infoDialog,
		infoUpload,
		ColorPicker,
		SwiperSelectCard,
	},
	mixins: [quoteBanChoiceMixins, quoteMixin],
	data() {
		const config = getQuoteConfig.call(this, this.$route.name);
		return {
            defaultColor: {
                code: "#ffffff",
                pantone: "white"
            },
			dictModel: new dictModel(),
			baseModel: new baseModel(),
			freeText: "",
			cateData: {},
			isFullReductionActivity: 0,
			satisfiedQuantity: 0,
			giftQuantity1: 0,
			acceptFileType,
			hideContent: false, //隐藏分类选择
			backgroundColorVal: null,
			colorKey: 0,
			showAlert: false,
			// presentedQuantity:null,
			checked: false,
			isIframe: false,
			hidePreview: false,
			platformProductId: "",
			isUpload: false,
			// 比较大类id(来自不可选参数)
			compareParentFlag: true,
			ropFontFamily1: "",
			ropFontFamily2: "",
			ropeText1: "",
			ropeText2: "",
			ropeFontFillStyleName1: "",
			ropeFontFillStyleName2: "",

			isActive: null,
			showRightArea: true,
			infoDialogVisible: false,
			beforeTempLogo: "",
			afterTempLogo: "",
			allImgDialog: false,
			allImage: [],
			uploadList: [],
			uploadList2: [],
			lanyardStyleName: "",
			dialogTips: false,
			fontBeforeImg: "",
			fontAfterImg: "",
			replaceIndex: -1,
			productsCategoriesName: "",
			totalColorNum: 0,
			lanyardBadgeHolderOptionsArr: [],
			badgeReelsArr: [],
			selectIndex: 0,
			beforeAfter: "",
			canvasToImage: "",
			currentPopularCardItem: {},
			currentPopularCardList: [],
			currentCustomCardItem: {},
			fontImgCustom: [],
			cardUploadList: [],
			// Attachement是否同步
			sameAsTheLeftAttachement: true,
			copyCanvasData: {
				lightUpLanyard: "",
				commentsImgLater: false,
				popularImg: "", //带图背景
				defaultFillStyle: "#C8102E", //canvas 底色
				ropeFillStyle1: "#C8102E", //挂绳填充色1 rgba(255, 255, 255, 0)
				customColorData: [],
				popularColorData: [],
				totalImg: [],
				beforeTempLogo: null,
				afterTempLogo: null,
				doubleFont: null, //织带双面打印
				extraImg: "", // 额外材质图(闪粉，反光)
				canvasWdith: "375",
				canvasHeight: "893",
				// trueCanvasWidth: "400",
				buckleC1: "", //底部卡扣
				buckleC1_1: "", //底部左卡扣
				buckleC1_2: "", //底部右卡扣
				buckleB1: "", //中部配件
				buckleA1: "", //上部配件
				buckleB1Height: 61,
				buckleB1Width: 130,
				buckleA1Height: 893,
				buckleA1Width: 375,
				buckleC1Height: 169,
				buckleC1Width: 80.5,
				white_height: 250,
				ropeHeight: 626, //挂绳长度
				defautlPaddingTop: 20, //绘制上边距
				//交叉
				// ropeWidthType1: 650, //挂绳长度1
				ropeWidthType1: 620, //挂绳长度1
				ropeHeightType1: 39, //挂绳高度1
				lRo1: 79.7, //78.2
				rRo1: -79.5, //-78
				// lX1: 42, // 上下
				lX1: 74, // 上下
				// lY1: -50.5, //-53.3
				lY1: -80.5, //-53.3
				rX1: -636.5, //-523.5
				// rY1: 255.8, //左右
				rY1: 288.8, //左右
				//交叉B
				// ropeWidthType1B: 480, //挂绳长度1
				ropeWidthType1B: 440, //挂绳长度1
				ropeHeightType1B: 39, //挂绳高度1
				lRo1B: 76.6, //78.2
				rRo1B: -76.3, //-78
				// lX1B: 45, // 上下
				lX1B: 90, // 上下
				// lY1B: -50, //-53.3
				lY1B: -81, //-53.3
				rX1B: -450.5, //-523.5
				// rY1B: 250, //左右
				rY1B: 285, //左右
				//垂直
				// ropeWidthType2: 755, //挂绳长度2
				ropeWidthType2: 755, //挂绳长度2
				ropeHeightType2: 41, //挂绳高度2
				lRo2: 90,
				rRo2: -90,
				// lX2: 26,
				lX2: 60,
				// lY2: -81, //水平
				lY2: -112.2, //水平
				rX2: -565,
				// rY2: 214.5, //水平
				rY2: 247.5, //水平
				// 短交叉
				// ropeWidthTypeS: 650, //挂绳长度1
				ropeWidthTypeS: 650, //挂绳长度1
				ropeHeightTypeS: 39, //挂绳高度1
				lRo3: 79.7, //78.2
				rRo3: -79.5, //-78
				lX3: 42, // 上下
				// lY3: -70.5, //-53.3
				lY3: -100.5, //-53.3
				rX3: -636.5, //-523.5
				// rY3: 235.8, //左右
				rY3: 270.8, //左右
				//水平
				// ropeWidthTypeT: 650, //
				ropeWidthTypeT: 720, //
				ropeHeightTypeT: 41, //
				//底绳
				ropeWidthType3: 41,
				ropeHeightType3: 215,
				ropeText1: "", //挂绳文字内容1
				ropeText2: "",
				ropeFontFillStyle1: "", //挂绳文字颜色1
				ropeFontFillStyle2: "", //挂绳文字颜色2
				ropeFontFillStyle3: "", //图标颜色
				ropeFontFillStyleName1: "white", //文字名1
				ropeFontFillStyleName2: "white", //文字名2
				ropeFontFillStyleName3: "", //图标颜色名字
				ropeFontSize1: 26, //挂绳文字大小1
				ropeFontSize2: 14, //挂绳文字大小2
				ropFontFamily1: "Arial", //挂绳文字类型1,
				ropFontFamily2: "Arial",
				ropeFontWeight1: false,
				ropeFontWeight2: false,
				ropeFontStyle1: false, //斜体
				ropeFontStyle2: false, //斜体
				ropeCross: true, //双绳
				fontBeforeImg: "",
				fontAfterImg: "",
				spacing: 20, //文字间距
			},
			tabsName: "POPULAR",
			defActiveAttachment: "",
			printingData: null,
			canvasData: {},
			bindValueSide: null,
			bindValueDesign: null,
			lanyardLengthData: {},
			allColor: [],
			currencyData: {},
			RibbonBindValue: {},
			showMore: true,
			selectedObj: {},
			tempType: null,
			tempItem: null,
			dialogItem: {},
			zoomPic: "",
			picDialog: false,
			hasComment: false,
			totalQuantity: 0,
			showOtoDialog: false,
			showDetails: false,
			debounceCalcPrice: "",
			defaultData: {},
			remark: "",
			calculateData: {},
			cateId: null,
			commentTarget: {},
			commentDialogVisible: false,
			currencyList: [],
			currentStep: null, //当前step
			going: false, //step模式
			selectedData: {
				//选中的
			},
			generalData: [], //总纲
			medalStyleData: {
				stepIndex: 1,
				childList: [],
				paramName: "Lanyard Style",
			}, //大类
			checking: false,
			fontLogo: "http://res.cloudinary.com/gs-jj-cloud/image/upload/v1678673484/gs-jj/eod0cz3u4txokf6xpvzp.png",
			logo: "https://res.cloudinary.com/gs-jj-cloud/image/upload/v1677139759/gs-jj/soikei6sst8uo5r6cvbl.png", //logo
			fugaiImage: "https://static-oss.gs-souvenir.com/web/quoteManage/20250313/涤纶_20250313dSpp5x.png", //涤纶A
			fugaiImage2: "https://static-oss.gs-souvenir.com/web/quoteManage/20250313/涤纶_20250313ipzmYy.png", //涤纶B
			fugaiImage3: "https://static-oss.gs-souvenir.com/web/quoteManage/20250313/涤纶_20250313SRE7cE.png", //涤纶 双
			danImage: "https://static-oss.gs-souvenir.com/web/quoteManage/20250313/30_2025031344TG2r.png", //dan L1_30
			danImageB: "https://static-oss.gs-souvenir.com/web/quoteManage/20250313/30_20250313CMBPWP.png", //danBL30
			shuangeImage: "https://static-oss.gs-souvenir.com/web/quoteManage/20250313/30_20250313hKdh4w.png", //shuangL3_30
			danImgList: [
				"https://res.cloudinary.com/gs-jj-cloud/image/upload/v1677139588/gs-jj/jec3uiddjelk6qaga5tt.png",
				"https://res.cloudinary.com/gs-jj-cloud/image/upload/v1677139922/gs-jj/byxjhq2dgdf9rehaqvic.png", //danl1 36
				"https://res.cloudinary.com/gs-jj-cloud/image/upload/v1677139960/gs-jj/eh7uofursrmhbdhukbhm.png", //danl1 42
			],
			danImgBList: [
				"http://res.cloudinary.com/gs-jj-cloud/image/upload/v1677486183/gs-jj/qptxk8eb1phnlu2vbpxg.png",
				"https://res.cloudinary.com/gs-jj-cloud/image/upload/v1677140026/gs-jj/agzxoxuhm01hjws9wonr.png", //danBL36
				"https://res.cloudinary.com/gs-jj-cloud/image/upload/v1677140042/gs-jj/xbi8oqjqnxlmlqof4xig.png", //danBL42
			],
			shuangeImgList: [
				"https://res.cloudinary.com/gs-jj-cloud/image/upload/v1677139701/gs-jj/z12stk06w9ouitfimbnv.png",
				"https://res.cloudinary.com/gs-jj-cloud/image/upload/v1677140078/gs-jj/rg7c9gewe2iilzsomyza.png", //shuangL3_36
				"https://res.cloudinary.com/gs-jj-cloud/image/upload/v1677140096/gs-jj/g7qlwucxgljp8hqacofe.png", //shuangL3_42
			],
			fugaiImageList: [
				"https://res.cloudinary.com/gs-jj-cloud/image/upload/v1677140201/gs-jj/p0gmmjhyjaaodnnjsqmm.png", //zhuanying
				"https://res.cloudinary.com/gs-jj-cloud/image/upload/v1677140216/gs-jj/ozd76h0maryggmiqd628.png", //nilong
				"https://res.cloudinary.com/gs-jj-cloud/image/upload/v1677140233/gs-jj/ejbtsdx4gpn61erkeebj.png", //guanzhuang
				"https://res.cloudinary.com/gs-jj-cloud/image/upload/v1677140260/gs-jj/mbi2ywa3tkynmhlm8xej.png", //dilun
				"https://res.cloudinary.com/gs-jj-cloud/image/upload/v1677140280/gs-jj/atzhfkwmhms4rabekbny.png", //bianzhi
			],
			fugaiImage2List: [
				"https://res.cloudinary.com/gs-jj-cloud/image/upload/v1677140309/gs-jj/qkatbxpjsyn6go3zmjql.png", //zhuanying
				"https://res.cloudinary.com/gs-jj-cloud/image/upload/v1677140327/gs-jj/veledy3hzof6oxg1jymd.png", //nilong
				"https://res.cloudinary.com/gs-jj-cloud/image/upload/v1677140343/gs-jj/jujmby1lxc2qulwz1mko.png", //guanzhuang
				"https://res.cloudinary.com/gs-jj-cloud/image/upload/v1677140360/gs-jj/etaeoamvv79jmaxrqsyz.png", //dilun
				"https://res.cloudinary.com/gs-jj-cloud/image/upload/v1677140374/gs-jj/sfk8453yjpykbhlj2lhm.png", //bianzhi
			],
			fugaiImage3List: [
				"https://res.cloudinary.com/gs-jj-cloud/image/upload/v1677140399/gs-jj/xqzzjvxhl2vwteb8t0gj.png", //zhuanying
				"https://res.cloudinary.com/gs-jj-cloud/image/upload/v1677140414/gs-jj/ljmnvpekcsgmhxeh3bbl.png", //nilong
				"https://res.cloudinary.com/gs-jj-cloud/image/upload/v1677140432/gs-jj/rsifmt5bllpmhwsmqlmq.png", //guanzhuang
				"https://res.cloudinary.com/gs-jj-cloud/image/upload/v1677140445/gs-jj/by9vjse0r5pp36bqkhtx.png", //dilun
				"https://res.cloudinary.com/gs-jj-cloud/image/upload/v1677140460/gs-jj/eayqf6mmibssh5bx7wxr.png", //bianzhi
			],
			noFileDialog: false,
			infoUploadList: [],
			inquiryId: 0,
			cardBoxFile: [],
			presentedQuantity: 0, //贈送總數
			noQuantityId: -1,
			lanyardType: "",
			...config,
		};
	},

	methods: {
		showNextButton(arr) {
			return arr.some(item => item.childList.length)
		},
		formatSubIndex(index, subIndex) {
			return String(index).padStart(2, "0") + "-" + subIndex;
		},
		// 参数默认选中逻辑
		async selectDefaultParam() {
			let query = this.$route.query;
			if (query.typeId) {
				let parentParam = this.generalData.find((x) => x.paramName == "Products Categories");
				if (parentParam) {
					let childParam = parentParam.childList.find((x) => x.id == query.typeId);
					childParam && this.selectQuoteParams(parentParam, childParam, true);
				}
			}

			if (query.sizeId) {
				let parentParam = this.generalData.find((x) => x.paramName == "Lanyard Width");
				if (parentParam) {
					let childParam = parentParam.childList.find((x) => x.id == query.sizeId);
					childParam && this.selectQuoteParams(parentParam, childParam, true);
				}
			}

			if (query.lengthId) {
				let parentParam = this.generalData.find((x) => x.paramName == "Lanyard Length");
				if (parentParam) {
					let childParam = parentParam.childList.find((x) => x.id == query.lengthId);
					childParam && this.selectQuoteParams(parentParam, childParam, true);
				}
			}

			if (query.optionId) {
				let parentParam = this.generalData.find((x) => x.paramName == "Additional Options");
				if (parentParam) {
					let childParam = parentParam.childList.find((x) => x.id == query.optionId);
					childParam && this.selectQuoteParams(parentParam, childParam, true);
				}
			}

			if (query.isOpenEnded) {
				this.defActiveAttachment = query.isOpenEnded == 1 ? "Single Clip Lanyard" : "Open Ended Lanyard";
			}

			if (this.lanyardType == "neopreneLanyards") {
				let parentParamSize = this.generalData.find((x) => x.paramName == "Lanyard Width");
				let parentParamLength = this.generalData.find((x) => x.paramName == "Lanyard Length");

				if (parentParamSize) {
					let childParam = parentParamSize.childList;
					childParam.length && this.selectQuoteParams(parentParamSize, childParam[0], true);
				}
				if (parentParamLength) {
					let childParam = parentParamLength.childList;
					childParam.length && this.selectQuoteParams(parentParamLength, childParam[0], true);
				}
			}
		},
		getGiftList() {
			this.currentPopularCardList = [...this.popularColor, ...this.designColor, ...this.colorCard];
		},
		presentedQuantityChange(giftQuantity,item) {
			this.presentedQuantity = this.currentPopularCardList.reduce((sum, item) => {
				let qua = item.giftQuantity ? parseInt(item.giftQuantity) : 0;
				return sum + qua;
			}, 0);
		},
		getcurrentPopularCardList(currentPopularCardList) {
			let totalQuantity = 0;

			if (currentPopularCardList.some((item) => item.quantity === undefined)) {
				return null;
			}
			currentPopularCardList.forEach((item) => {
				if (item.quantity) {
					totalQuantity += Number(item.quantity) || 0;
				} else {
					item.giftQuantity = null;
				}
			});

			if (totalQuantity < this.satisfiedQuantity) {
				this.checked = false;
				this.currentPopularCardList.forEach((i) => {
					i.giftQuantity = null;
				});
				this.presentedQuantity = 0;
			}

			//如果数量大于等于满赠数量默认选中
			if (totalQuantity >= this.satisfiedQuantity) {
				this.checked = true;
			}
			return totalQuantity;
		},

		pushInfoList(data) {
			this.infoUploadList.push(data);
		},
		delInfoList(index) {
			this.infoUploadList.splice(index, 1);
		},
		updateInquiry() {
			if (!this.inquiryId) {
				this.closeInfoDialog(false);
			}
			let newPicPath = this.infoUploadList.map((item) => item.secure_url);
			let data = {
				id: this.inquiryId,
				newPicPath: newPicPath,
			};
			setInquiry(data).then(() => {
				this.closeInfoDialog(false);
			});
		},
		closeInfoDialog(data) {
			this.infoUploadList = [];
			this.noFileDialog = data;
			if (this.isIframe) {
				let targetWindow = window.opener || window.parent; // 目标窗口，可以是父窗口或打开当前窗口的窗口
				targetWindow.postMessage(
					{
						type: "toHome",
					},
					window.origin
				); // 发送消息
			} else {
				window.location.href = "/";
			}
		},
		updateUploadListCard(item) {
			this.cardBoxFile = [item];
			this.$set(this.selectedData["Select Your Card"][0], "uploadImg", item.secure_url);
		},
		updateUploadList(item) {
			this.fontImgCustom.push(item);
		},

		changeSameForAttachment(val) {
			this.sameAsTheLeftAttachement = val;
			this.selectedData["Lanyard Attachment"] = [];
		},
		//计算织带颜色数量
		countColor() {
			let that = this;
			let count = 0;
			this.totalColorNum = 0;
			if (this.copyCanvasData.ropeFontFillStyle1) {
				count++;
			}
			if (this.copyCanvasData.ropeFontFillStyle2 && this.copyCanvasData.ropeFontFillStyle2 != this.copyCanvasData.ropeFontFillStyle1) {
				count++;
			}
			//颜色前5
			let getCountNum = function () {
				return new Promise(async (resolve, reject) => {
					let temp1 = [],
						temp2 = [],
						temp3 = [],
						obj = {};
					if (that.fontBeforeImg) {
						temp1 = await analyzeImageColor(that.fontBeforeImg + "?temp=" + new Date().valueOf());
						temp1 = temp1.splice(0, 5);
					}
					if (that.fontAfterImg) {
						temp2 = await analyzeImageColor(that.fontAfterImg + "?temp=" + new Date().valueOf());
						temp2 = temp2.splice(0, 5);
					}
					temp3 = temp1.concat(temp2).reduce(function (item, next) {
						obj[next.color] ? "" : (obj[next.color] = true && item.push(next));
						return item;
					}, []);
					resolve(temp3);
				});
			};
			getCountNum().then((res) => {
				count = count + res.length;
				if (count > 3 && this.lanyardStyleName !== "Dye Sublimated Lanyards") {
					this.dialogTips = true;
				}
				this.totalColorNum = count > 3 ? 3 : count;
				this.calculate();
			});
		},
		async dialogTipsFun() {
			this.dialogTips = false;
			let target = this.getInfo("Lanyard Style");
			//       this.$emit("clickFun", {
			//   key: this.bindName,
			//   value: this.bindValue,
			// })
			this.selectFun(
				{
					key: target.paramName,
					value: target.childList[0],
				},
				0
			);
		},
		filterStitchStyleFun() {
			if (this.lanyardType === "lightUpLanyards") {
				return false;
			}
			this.getInfo("Lanyard Stitch Style")?.childList.forEach((x) => {
				x.show = true;
			});
			let arr1 = this.selectedData["Lanyard Style"],
				arr2 = this.selectedData["Lanyard Width"],
				target = this.getInfo("Lanyard Stitch Style")?.childList.find((x) => {
					return x.paramName == "Length Adjuster";
				}),
				target2 = this.getInfo("Lanyard Stitch Style")?.childList.find((x) => {
					return x.paramName == "Metal Bead";
				});
			if (arr1.length && arr2.length) {
				switch (arr1[0].cateName) {
					case "Dye Sublimated Lanyards":
						if (arr2[0].paramName == "1 Inch") {
							if (target) target.show = false;
							if (target2) target2.show = false;
						}
						break;
					case "Nylon Lanyards":
						if (arr2[0].paramName == "1 Inch") {
							if (target) target.show = false;
							if (target2) target2.show = false;
						}
						break;
					case "Tubular Lanyards":
						if (arr2[0].paramName == "5/8 Inch" || arr2[0].paramName == "1/2 Inch") {
							if (target) target.show = false;
							if (target2) target2.show = false;
						}
						break;
					case "Polyester Lanyards":
						if (arr2[0].paramName == "1 Inch") {
							if (target) target.show = false;
							if (target2) target2.show = false;
						}
						break;
					case "Woven Lanyards":
						if (arr2[0].paramName == "1 Inch") {
							if (target) target.show = false;
							if (target2) target2.show = false;
						}
						break;
				}
			}
			this.selectedData["Lanyard Stitch Style"] = [];
		},
		backfill() {
			this.getInfo("Lanyard Design Colors")?.childList.forEach((item) => {
				this.selectedData["Lanyard Design Colors"].forEach((item2) => {
					if (item.id == item2.id) {
						item.quantity = item2.quantity;
					}
				});
			});
			this.getInfo("Lanyard Popular Colors")?.childList.forEach((item) => {
				this.selectedData["Lanyard Popular Colors"].forEach((item2) => {
					if (item.id == item2.id) {
						item.quantity = item2.quantity;
					}
				});
			});
		},
		convertCanvasToImage(canvas) {
			return new Promise((resolve, reject) => {
				var userName = "canvasImg";
				var fileName = userName + ".jpg"; //vm.addUserName
				var firstName = fileName.charAt(0);
				var dataurl = canvas.toDataURL("image/png", 1.0);
				var arr = dataurl.split(","),
					mime = arr[0].match(/:(.*?);/)[1],
					bstr = atob(arr[1]),
					n = bstr.length,
					u8arr = new Uint8Array(n);
				while (n--) {
					u8arr[n] = bstr.charCodeAt(n);
				}
				var file = new File([u8arr], fileName, {
					type: mime,
				});
				// file.lastModifiedDate = new Date();
				uploadFile(file).then((res) => {
					resolve(res);
				});
			});
		},
		beforeAfterFun(val) {
			this.beforeAfter = val;
		},
		ropeCrossFun(val) {
			this.copyCanvasData.ropeCross = val;
			this.selectedData["Lanyard Attachment"] = [];
			this.sameAsTheLeftAttachement = true;
		},
		canvasToImageFun(val) {
			this.canvasToImage = val;
		},
		selectPritingMethodFun(obj,item) {
			this.selectedData["Select Printing Method"] = [obj];
		},
		selectTabsFun(arr, val, name, type,item) {
			console.log(arguments,'1111');
			try {
				//记录用户的选择
				this.$store.commit("setGoogleUploadAction", {
					[`STEP${item.customIndex}:${val.key}`]: val.value.paramName,
					content_id: this.pid,
				});
			}catch(e){}
			let tempArr = [];
			arr.forEach((item) => {
				if (item) tempArr.push(item.value);
			});
			this.selectedData["Lanyard Attachment"] = tempArr;
			if (type == "single") {
				this.copyCanvasData.buckleC1 = JSON.parse(val.value.imageJson)[1].url + "?temp=" + new Date().valueOf();
			} else if (type == "same") {
				this.copyCanvasData.buckleC1_1 = JSON.parse(val.value.imageJson)[1].url + "?temp=" + new Date().valueOf();
				this.copyCanvasData.buckleC1_2 = JSON.parse(val.value.imageJson)[1].url + "?temp=" + new Date().valueOf();
			} else if (type == "left") {
				this.copyCanvasData.buckleC1_1 = JSON.parse(val.value.imageJson)[1].url + "?temp=" + new Date().valueOf();
			} else if (type == "right") {
				this.copyCanvasData.buckleC1_2 = JSON.parse(val.value.imageJson)[1].url + "?temp=" + new Date().valueOf();
			}
			this.filterStepsFun().then((res) => {
				this.noChoiceData = res;
			});
			this.$nextTick(() => {
				this.debounceCanvasInit();
			});
		},
		getAll() {
			indexApi.getAll().then((res) => {
				this.allImage = res.data;
			});
		},
		getInfo(name, dataName = "filterShowGeneralData") {
			return this[dataName].find((x) => {
				return x.paramName == name;
			});
		},
		generatePopularImg(color1, color2, picWidth = 348, picHeight = 518) {
			const canvas = document.createElement("canvas");
			const ctx = canvas.getContext("2d");
			canvas.width = picWidth;
			canvas.height = picHeight;
			// 左边一种颜色 不是对半分
			ctx.fillStyle = color1;
			ctx.fillRect(0, 0, picWidth / 2, picHeight);

			// 右边一种颜色
			ctx.fillStyle = color2;
			ctx.fillRect(picWidth / 2, 0, picWidth, picHeight);
			return canvas.toDataURL();
		},
		popularCardObjFun(val,item) {
			if (!val) {
				return;
			}
			console.log(arguments,'3333');

			this.backgroundColorVal = val;
			let paramCode = val?.paramCode.split(",");
			if (paramCode.length) {
				let previewIcon = document.querySelector(".preview b");
				previewIcon.style.color = paramCode[0];
			}
			this.currentPopularCardItem = val;
			//根据颜色生成图片
			if (this.currentPopularCardItem && this.currentPopularCardItem.imageJson && this.lanyardType != "twoToneLanyards") {
				this.copyCanvasData.popularImg = JSON.parse(this.currentPopularCardItem.imageJson)[0].url + "?temp=" + new Date().valueOf();
			} else {
				this.copyCanvasData.popularImg = this.generatePopularImg(paramCode[0], paramCode[1]);
			}
			//lightUp织带
			try {
				//记录用户的选择
				this.$store.commit("setGoogleUploadAction", {
					[`STEP${item.customIndex}:${item.paramName}`]: val.alias+'_'+val.alias2,
					content_id: this.pid,
				});
				if (val.paramName === "Color Changing") {
					let colorParams = this.getInfo("Lanyard Popular Colors"),
						list = [];
					if (colorParams) {
						colorParams.childList.forEach((item, index) => {
							if (item.paramName !== "Color Changing") {
								list.push(JSON.parse(item.priceInfo.imagePath)[0].path);
							}
						});
					}
					this.copyCanvasData.lightUpLanyard = list;
				} else {
					this.copyCanvasData.lightUpLanyard = JSON.parse(val.priceInfo.imagePath)[0].path;
				}
			} catch (e) {}
            //lightUp织带设计文字颜色按照选中的colors来
            if(this.lanyardType==="lightUpLanyards"){
                this.$refs["PrintTabs"][0].setLightUpColor({
                    code: val.paramCode,
                    pantone: val.paramName
                })
                this.defaultColor = {
                    code: val.paramCode,
                    pantone: val.paramName
                };
            }
			this.filterStepsFun().then((res) => {
				this.noChoiceData = res;
			});
			this.$nextTick(() => {
				this.debounceCanvasInit();
			});
			this.lanyardLengthData = this.getInfo("Lanyard Length");
		},
		customCardObjFun(val,item) {
			if (!val) {
				return;
			}
			console.log(arguments,'...........');
			try {
				//记录用户的选择
				this.$store.commit("setGoogleUploadAction", {
					[`STEP${item.customIndex}:${item.paramName}`]: val.alias+'_'+val.alias2,
					content_id: this.pid,
				});
			}catch (e) {}
			if (this.lanyardType === "twoToneLanyards") {
				if (!val) {
					return;
				}
				if (val.mainColor.code) {
					let previewIcon = document.querySelector(".preview b");
					previewIcon.style.color = val.mainColor.code;
				}
				this.currentCustomCardItem = val;
				//根据颜色生成图片
				this.copyCanvasData.popularImg = this.generatePopularImg(val.mainColor.code, val.accentColor.code);
			} else {
				if (val.code) {
					let previewIcon = document.querySelector(".preview b");
					previewIcon.style.color = val.code;
				}
				this.currentCustomCardItem = val;
				if (this.currentCustomCardItem) {
					this.copyCanvasData.defaultFillStyle = this.currentCustomCardItem.code;
					this.copyCanvasData.ropeFillStyle1 = this.currentCustomCardItem.code;
				} else {
					this.copyCanvasData.defaultFillStyle = "#C8102E";
					this.copyCanvasData.ropeFillStyle1 = "#C8102E";
				}
			}
			this.$nextTick(() => {
				this.debounceCanvasInit();
			});
		},
		getTabsName(val) {
			this.tabsName = val;
		},
		canvasFontFormFun(val) {
			this.ropFontFamily1 = val.ropFontFamily1;
			this.ropFontFamily2 = val.ropFontFamily2;
			this.ropeText1 = val.ropeText1;
			this.ropeText2 = val.ropeText2;
			this.fontAfterImg = val.fontAfterImg;
			this.fontBeforeImg = val.fontBeforeImg;
			this.ropeFontFillStyleName1 = val.ropeFontFillStyleName1;
			this.ropeFontFillStyleName2 = val.ropeFontFillStyleName2;

			this.copyCanvasData = Object.assign(this.copyCanvasData, val);
			if (this.copyCanvasData.commentsImgLater === true) {
				this.fontImgCustom = [];
			}
			// this.copyCanvasData = [...this.copyCanvasData, ...val]
			this.$nextTick(() => {
				this.debounceCanvasInit();
			});
		},
		printingDataFun(val) {
			this.printingData = val;
		},
		//所有色卡
		findAll() {
			return new Promise((resolve, reject) => {
				indexApi.findAll().then((res) => {
					this.allColor = res.data;
					resolve();
				});
			});
		},
		qtyAndBtnConfirm(val) {
			this.picDialog = false;
			this.selectFun(this.selectedObj);
		},
		dialogNextStep() {
			this.picDialog = false;
			this.selectFun(this.selectedObj);
		},
		imgPickerFun(value) {
			if (this.clipartType == "start") {
				this.fontBeforeImg = value.imageUrl;
			} else if (this.clipartType == "end") {
				this.fontAfterImg = value.imageUrl;
			}
			// this.countColor();
			this.allImgDialog = false;
		},
		filterTampInfo(tamp) {
			this.fontBeforeImg = "";
			this.fontAfterImg = "";
			switch (tamp.paramName) {
				case "Only Logo":
					this.beforeTempLogo = this.logo;
					this.afterTempLogo = "";
					break;
				case "A Line of Words 1":
					this.beforeTempLogo = "";
					this.afterTempLogo = "";
					break;
				case "A Line of Words 2":
					this.beforeTempLogo = "";
					this.afterTempLogo = "";
					break;
				case "Two Lines of Words":
					this.beforeTempLogo = "";
					this.afterTempLogo = "";
					break;
				case "A Line of Words & Started, End Clipart":
					this.beforeTempLogo = this.logo;
					this.afterTempLogo = this.logo;
					break;
				case "A Line of Words & Started Clipart":
					this.beforeTempLogo = this.logo;
					this.afterTempLogo = "";
					break;
				case "A Line of Words & End Clipart":
					this.beforeTempLogo = "";
					this.afterTempLogo = this.logo;
					break;
				case "Two lines words & started, end clipart":
					this.beforeTempLogo = this.logo;
					this.afterTempLogo = this.logo;
					break;
				case "Two lines words & started clipart":
					this.beforeTempLogo = this.logo;
					this.afterTempLogo = "";
					break;
				case "Two lines words & end clipart":
					this.beforeTempLogo = "";
					this.afterTempLogo = this.logo;
					break;
				default:
			}
		},
		clipartFun(obj) {
			this.clipartType = obj.type;
			if (obj.type == "start") {
				this.beforeAfter = "1";
			} else {
				this.beforeAfter = "2";
			}
			switch (obj.val) {
				case 1:
					if (obj.type == "start") {
						this.fontBeforeImg = "";
						this.beforeTempLogo = "";
					} else {
						this.fontAfterImg = "";
						this.afterTempLogo = "";
					}
					// this.countColor();
					break;
				case 2:
					this.allImgDialog = true;
					this.getAll();
					break;
				case 3:
					this.$refs.upload3.click();
					break;
				case 4:
					if (obj.type == "start") {
						this.fontBeforeImg = "";
						this.beforeTempLogo = this.fontLogo;
					} else {
						this.fontAfterImg = "";
						this.afterTempLogo = this.fontLogo;
					}
					// this.countColor();
					break;
			}
		},
		getVideoOptions(path, type, poster) {
			if (type === 1) {
				return {
					autoplay: true,
					controls: false,
					muted: true,
					loop: true,
					fill: true,
					sources: [
						{
							src: path,
							type: "video/mp4",
						},
					],
				};
			} else if (type === 2) {
				return {
					autoplay: true,
					controls: true,
					muted: false,
					loop: true,
					fill: true,
					sources: [
						{
							src: path,
							type: "video/mp4",
						},
					],
				};
			} else if (type === 3) {
				return {
					autoplay: true,
					controls: false,
					muted: true,
					loop: true,
					fill: true,
					sources: [
						{
							src: path,
							type: "video/mp4",
						},
					],
				};
			} else if (type === 4) {
				return {
					autoplay: false,
					controls: false,
					muted: true,
					loop: true,
					fill: true,
					sources: [
						{
							src: path,
							type: "video/mp4",
						},
					],
					poster: poster,
				};
			}
		},
		getFileSuffix(url) {
			return getFileSuffix(url);
		},
		shutComment() {
			this.commentDialogVisible = false;
			this.checkForm();
		},
		commentSubmit() {
			this.commentDialogVisible = false;
			this.checkForm();
		},
		zoomPicFun($event, val, type) {
			this.tempType = type;
			this.picDialog = true;
			this.selectedObj = $event;
			this.dialogItem = val;

			if (this.tempType == "video") {
				this.zoomPic = val.videoPath ? val.videoPath : val.priceInfo.videoPath;
			} else {
				this.zoomPic = val.priceInfo?.videoPath || val.priceInfo.imagePath || JSON.parse(val.imageJson)[0].url;
			}
		},
		picDialogFun(val) {
			// this.picDialog = val
			// this.shutMask()
		},

		//购物车
		async addCart() {
			if (this.$store.state.isTemporary) {
				this.$store.commit("setMask", "temporary");
				return false;
			}
			this.$gl.show();
			let quoteParam = await this.getQuoteParam();
			let priceParam = await this.getPriceParam();
			let data = {
				proId: this.proId,
				uuid: this.isLogin ? null : this.userUUID,
				userId: this.isLogin ? this.userId : null,
				buyType: 7,
				quoteCateId: this.pid,
				quoteCateChildId: this.cateId,
				quotePriceParam: JSON.stringify(priceParam),
				quoteParam: JSON.stringify(quoteParam),
				isOneDollarPens: 0,
				isSmallQty: getIsSmallQty(findSelectDiscount(this.selectedData), this.$store.state.enableTurnaroundTimeCheck),
			};
			otoAddCart(data, this.calculateData)
				.then((res) => {
					this.$toast.success(res.message);
					this.$store.commit("setGoogleUploadAction", { addCart: "addCart", content_id: this.pid });
					if (this.isIframe) {
						let targetWindow = window.opener || window.parent; // 目标窗口，可以是父窗口或打开当前窗口的窗口
						targetWindow.postMessage(
							{
								type: "toCart",
							},
							window.origin
						); // 发送消息
					} else {
						this.$router.push({
							path: "/cart",
						});
					}
				})
				.finally(() => {
					setTimeout(() => {
						this.$gl.hide();
					}, 1000);
				});
		},
		//添加询盘
		async addInquiry() {
			//如果是临时项目
			if (this.$store.state.isTemporary) {
				this.$store.commit("setMask", "temporary");
				return false;
			}
			this.$store.commit("setGoogleUploadAction", { addInquiry: 'addInquiry', content_id: this.pid });
			this.infoDialogVisible = true;
		},
		async getValueFun(val) {
			this.$gl.show();
			let quoteParam = await this.getQuoteParam();
			let priceParam = await this.getPriceParam(); ///
			//询盘如果有期待时间，finaData手动添加数据
			if (val.expectTime) {
				quoteParam.finaData.push({
					parentId: 0,
					alias: "Expected Delivery Date",
					childList: [
						{
							parentId: 10000,
							alias: val.expectTime,
						},
					],
				});
			}
			let data = {
				platformProductId: this.platformProductId,
				proId: this.proId,
				email: "",
				productsName: "LandYard",
				quoteCateId: this.pid,
				quoteCateChildId: this.cateId,
				isMobile: this.device === "mb" ? 1 : 0,
				buyType: 7,
				quotePriceParam: JSON.stringify(priceParam), ///
				quoteParam: JSON.stringify(quoteParam),
				notes: "",
				...val,
				telephone: val.areaCode + "-" + val.telephone,
				giftQuantityTotal: this.presentedQuantity, //赠送总数
				isSmallQty: getIsSmallQty(findSelectDiscount(this.selectedData), this.$store.state.enableTurnaroundTimeCheck),
			};
			//经销商
			// data.email = val.email;
			// data.firstName = val.firstName;
			// data.lastName = val.lastName;
			// data.telephone = val.telephone;
			this.otoEditInquiry(data);
		},
		otoEditInquiry(data) {
			otoEditInquiry(data).then((res) => {
				this.inquiryId = res.data;
				this.$gl.hide();
				// if(this.uploadList.length==0&&this.fontImgCustom.length==0&&this.cardBoxFile.length==0){
				//  this.noFileDialog=true
				// }else{
				this.$confirm(this.lang.p22, this.lang.p21, {
					confirmButtonText: this.lang.Confirm,
					type: "success",
					showCancelButton: false,
					center: true,
					customClass: "inquirySuccess",
					confirmButtonClass: "inquirySuccessBtn",
				}).finally(() => {
					if (this.isIframe) {
						let targetWindow = window.opener || window.parent; // 目标窗口，可以是父窗口或打开当前窗口的窗口
						targetWindow.postMessage(
							{
								type: "toHome",
							},
							window.origin
						); // 发送消息
					} else {
						window.location.href = "/";
					}
				});
				// }
			});
		},
		//询盘数据
		getQuoteParam() {
			return new Promise(async (resolve, reject) => {
				if (this.copyCanvasData.commentsImgLater == true) {
					this.selectedData["Design Your Printing"][0] = {};
					this.selectedData["Design Your Printing"][0].later = this.copyCanvasData.commentsImgLater;
				}
				let copyGeneralData = JSON.parse(JSON.stringify(this.generalData));
				let copySelectedData = JSON.parse(JSON.stringify(this.selectedData));
				let theCanvas = document.getElementById("canvas");
				let listKey = Object.keys(copySelectedData);

				let copyGeneralData2 = [];

				listKey.forEach((item) => {
					copyGeneralData.forEach((item2) => {
						if (item == item2.paramName) {
							copyGeneralData2.push(item2);
						}
					});
				});
				copyGeneralData.forEach((item) => {
					let a = true;
					listKey.forEach((item2) => {
						if (item2 == item.paramName) {
							a = false;
						}
					});
					if (a) {
						copyGeneralData2.push(item);
					}
				});
				copyGeneralData = copyGeneralData2;

				let delIndex = copyGeneralData.findIndex((x) => {
					return x.paramName == "Lanyard Style";
				});
				if (delIndex >= 0) {
					copyGeneralData.splice(delIndex, 1);
				}
				for (let x in copySelectedData) {
					copyGeneralData.forEach((item) => {
						if (item.paramName == x) {
							item.childList = copySelectedData[x];
							if (item.paramName == "Lanyard Badge Holder Options" || item.paramName == "Badge Reels") {
								item.childList.forEach((item2) => {
									item2.childList = item2.childList.filter((x2) => {
										return x2.quantity && x2.quantity > 0;
									});
								});
							} else if (item.paramName == "Select Your Card" && item.show) {
								item.childList[0].childList = item.selectedObj ? [item.selectedObj] : [];
							} else if (item.paramName == "color card") {
								item.childList.forEach((kk) => {
									if (this.lanyardType === "twoToneLanyards") {
										kk.id = kk.mainColor.id;
										kk.pantone = `${this.lang.mainColor}:${kk.mainColor.pantone},${this.lang.accentColor}:${kk.accentColor.pantone}`;
									}
									kk.paramType = "COLORCARD";
									kk.giftQuantity = kk.giftQuantity || 0;
								});
								if (this.lanyardType === "twoToneLanyards") {
									item.childList.forEach((kk) => {
										if (this.lanyardType === "twoToneLanyards") {
											kk.id = kk.mainColor.id;
											kk.pantone = `${this.lang.mainColor}:${kk.mainColor.pantone},${this.lang.accentColor}:${kk.accentColor.pantone}`;
										}
										kk.paramType = "COLORCARD";
										kk.giftQuantity = kk.giftQuantity || 0;
									});
								} else {
									item.childList.forEach((kk) => {
										kk.paramType = "COLORCARD";
										kk.giftQuantity = kk.giftQuantity || 0;
									});
								}
							} else if (item.paramName == "Lanyard Popular Colors") {
								if (this.lanyardType === "twoToneLanyards") {
									item.childList.forEach((kk) => {
										kk.alias = `${kk.alias} + ${kk.alias2}`;
										kk.giftQuantity = kk.giftQuantity || 0;
									});
								} else {
									item.childList.forEach((kk) => {
										kk.giftQuantity = kk.giftQuantity || 0;
									});
								}
							} else if (item.paramName == "Lanyard Design Colors") {
								item.childList.forEach((kk) => {
									kk.giftQuantity = kk.giftQuantity || 0;
								});
							} else if (item.paramName == "Select Printing Method") {
								item.childList.forEach((kk) => {
									if (kk.childList.length) {
										kk.inputNum = Number(kk.childList[0].paramCode);
										kk.childList = [];
									}
								});
							}
						}
					});
				}
				let files = [];
				if (this.uploadList2.length > 0) {
					this.uploadList2.forEach((item) => {
						files.push(item.secure_url);
					});
				}
				// let killPriceInfo = function (arr) {
				//   arr.forEach((item) => {
				//     item.priceInfo = null
				//     if (item.childList.length) {
				//       killPriceInfo(item.childList)
				//     }
				//   })
				// }
				// killPriceInfo(copyGeneralData)
				this.convertCanvasToImage(theCanvas).then((res) => {
					let files = [];
					let artworkList = this.fontImgCustom.concat(this.uploadList);
					//led织带 Color Changing 去除图片
					if (this.copyCanvasData.lightUpLanyard && Array.isArray(this.copyCanvasData.lightUpLanyard)) {
						res = "https://static-oss.gs-souvenir.com/web/quoteManage/20250424/color_changing_20250424SSBrCF.jpg";
					}
					if (artworkList.length > 0) {
						artworkList.forEach((item) => {
							files.push(item.secure_url);
						});
					}
					if (this.selectedData["Design Your Printing"].length) {
						this.copyCanvasData.commentsImgLater = false;
					} else {
						if (this.copyCanvasData.commentsImgLater) {
							this.fontImgCustom = [];
						}
						this.copyCanvasData.ropeText1 = "";
						this.copyCanvasData.ropeText2 = "";
						this.copyCanvasData.ropeFontFillStyleName1 = "";
						this.copyCanvasData.ropeFontFillStyleName2 = "";
						this.copyCanvasData.ropFontFamily1 = "";
						this.copyCanvasData.ropFontFamily2 = "";
					}
					resolve({
						classificationData: copySelectedData["Lanyard Style"][0],
						finaData: copyGeneralData,
						fontData: {
							ropeText1: this.copyCanvasData.ropeText1,
							ropeText2: this.copyCanvasData.ropeText2,
							ropeFontFillStyle1: this.copyCanvasData.ropeFontFillStyle1,
							ropeFontFillStyle2: this.copyCanvasData.ropeFontFillStyle2,
							ropeFontFillStyleName1: this.copyCanvasData.ropeFontFillStyleName1,
							ropeFontFillStyleName2: this.copyCanvasData.ropeFontFillStyleName2,
							ropeFontSize1: this.copyCanvasData.ropeFontSize1,
							ropeFontSize2: this.copyCanvasData.ropeFontSize2,
							ropFontFamily1: this.copyCanvasData.ropFontFamily1,
							ropFontFamily2: this.copyCanvasData.ropFontFamily2,
							ropeFontWeight1: this.copyCanvasData.ropeFontWeight1,
							ropeFontWeight2: this.copyCanvasData.ropeFontWeight2,
							ropeFontStyle1: this.copyCanvasData.ropeFontStyle1,
							ropeFontStyle2: this.copyCanvasData.ropeFontStyle2,
							fontBeforeImg: this.copyCanvasData.fontBeforeImg,
							fontAfterImg: this.copyCanvasData.fontAfterImg,
							fontALater: this.copyCanvasData.startClipartValue == 4 ? true : false,
							fontBLater: this.copyCanvasData.endClipartValue == 4 ? true : false,
							// fontBeforeImgCustom: this.copyCanvasData.fontBeforeImgCustom,
							// fontAfterImgCustom: this.copyCanvasData.fontAfterImgCustom,
							fontImgCustom: this.copyCanvasData.commentsImgLater ? [] : files, //自定义图片
							comments: this.copyCanvasData.comments ? this.copyCanvasData.comments : "",
							commentsImgLater: this.copyCanvasData.commentsImgLater,
							doubleFont: this.copyCanvasData.doubleFont,
							totalColorNum: copySelectedData["Lanyard Style"][0].cateName == "Dye Sublimated Lanyards" ? 0 : this.totalColorNum,
						},
						canvasData: {
							url: res,
						},
					});
				});
			});
		},
		//价格数据
		getPriceParam() {
			return new Promise((resolve, reject) => {
				let accessoriesDetailDTOS = [],
					qtyDetailDTOS = [],
					paramIdList = [],
					upgradesQtyDTO = [];

				for (let i in this.selectedData) {
					if (i == "Lanyard Popular Colors") {
						this.selectedData[i].forEach((item) => {
							if (item.quantity && item.quantity > 0) {
								let tempObj = {
									quantity: item.quantity,
									paramType: "COLOR",
									paramId: item.priceInfo.id,
									paramValue: item.paramCode,
									giftQuantity: item.giftQuantity ? item.giftQuantity : 0,
									isQuoteQuantity: item.giftQuantity ? 1 : 0,
								};
								qtyDetailDTOS.push(tempObj);
							}
						});
					} else if (i == "Lanyard Design Colors") {
						this.selectedData[i].forEach((item) => {
							if (item.quantity && item.quantity > 0) {
								let tempObj = {
									quantity: item.quantity,
									paramType: "COLOR",
									paramId: item.priceInfo.id,
									paramValue: item.paramCode,
									giftQuantity: item.giftQuantity ? item.giftQuantity : 0,
									isQuoteQuantity: item.giftQuantity ? 1 : 0,
								};
								qtyDetailDTOS.push(tempObj);
							}
						});
					} else if (i == "color card") {
						this.selectedData[i].forEach((item) => {
							if (item.quantity && item.quantity > 0) {
								let tempObj = {
									quantity: item.quantity,
									paramType: "COLORCARD",
									paramValue: item.code,
									paramId: this.getInfo("color card").priceInfo.id,
									giftQuantity: item.giftQuantity ? item.giftQuantity : 0,
									isQuoteQuantity: item.giftQuantity ? 1 : 0,
								};
								qtyDetailDTOS.push(tempObj);
							}
						});
					} else if (i == "Select Your Card") {
						this.selectedData[i].forEach((item) => {
							if (item.quantity && item.selectedObj) {
								let tempObj = {
									quantity: item.quantity,
									paramId: item.selectedObj.priceInfo.id,
								};
								accessoriesDetailDTOS.push(tempObj);
							}
						});
					} else if (i == "Lanyard Badge Holder Options") {
						this.selectedData[i].forEach((item) => {
							item.childList.forEach((item2) => {
								if (item2.quantity && item2.quantity > 0) {
									let tempObj = {
										quantity: item2.quantity,
										paramId: item2.priceInfo.id,
									};
									accessoriesDetailDTOS.push(tempObj);
								}
							});
						});
					} else if (i == "Badge Reels") {
						this.selectedData[i].forEach((item) => {
							item.childList.forEach((item2) => {
								if (item2.quantity && item2.quantity > 0) {
									let tempObj = {
										quantity: item2.quantity,
										paramId: item2.priceInfo.id,
									};
									accessoriesDetailDTOS.push(tempObj);
								}
							});
						});
					} else if (i == "Lanyard Attachment") {
						this.selectedData[i].forEach((item) => {
							paramIdList.push(item.priceInfo.id);
						});
					} else if (i == "Lanyard Stitch Style") {
						this.selectedData[i].forEach((item) => {
							paramIdList.push(item.priceInfo.id);
						});
					} else if (i == "Additional Options") {
						this.selectedData[i].forEach((item) => {
							paramIdList.push(item.priceInfo.id);
						});
					} else if (i == "Select Your Glitter Colors") {
						this.selectedData[i].forEach((item) => {
							paramIdList.push(item.priceInfo.id);
						});
					} else if (i == "Glitter Lanyards Printing Colors") {
						this.selectedData[i].forEach((item) => {
							paramIdList.push(item.priceInfo.id);
						});
					} else if (i == "Select Packaging") {
						this.selectedData[i].forEach((item) => {
							paramIdList.push(item.priceInfo.id);
						});
					} else if (i == "Print Position Options") {
						this.selectedData[i].forEach((item) => {
							paramIdList.push(item.priceInfo.id);
						});
					} else if (i == "Silicone Printing Colors") {
						this.selectedData[i].forEach((item) => {
							paramIdList.push(item.priceInfo.id);
						});
					} else if (i == "Select Printing Method") {
						const item = this.selectedData[i][0];
						if (item) {
							item.childList.forEach((citem) => {
								paramIdList.push(citem.priceInfo.id);
							});
						}
					} else if (i == "Lanyard Style") {
						this.selectedData[i].forEach((item) => {
							let temp = {
								quantity: item.cateName == "Dye Sublimated Lanyards" ? 0 : this.totalColorNum,
								paramId: this.getInfo("Design Your Printing Color")?.priceInfo.id,
								isLanyard: 1,
							};
							upgradesQtyDTO.push(temp);
						});
					} else if (i == "Lanyard Length" && this.lanyardType != "phoneLanyards") {
						this.selectedData[i].forEach((item) => {
							paramIdList.push(item.priceInfo.id);
						});
					}
				}
				resolve({
					qtyDetailDTOS: qtyDetailDTOS, //ok
					accessoriesDetailDTOS: accessoriesDetailDTOS, //ok
					cateId: this.cateId, //ok
					paramIdList: paramIdList, //
					projectName: this.projectName, //ok
					sizeId: this.selectedData["Lanyard Width"] && this.selectedData["Lanyard Width"][0]?.priceInfo.id, //ok
					sizeLengthId: this.selectedData["Lanyard Length"] && this.selectedData["Lanyard Length"][0]?.priceInfo.id, //ok
					discountId: this.selectedData["Delivery Date"][0]?.priceInfo.id, //ok
					upgradesQtyDTO: upgradesQtyDTO, //
				});
			});
		},
		//算价格
		async calculate() {
			if (this.selectedData["Lanyard Width"].length) {
				let postData = await this.getPriceParam();
				indexApi.calculate(postData).then((res) => {
					let { data } = res;
					this.calculateData = data;
				});
			}
		},
		replayUpload() {
			this.$store.commit("setSizeDialog", false);
			this.$refs.upload3.click();
		},
		uploadPic(event) {
			this.$gl.show();
			let files = event.target.files;
			uploadFile(files[0])
				.then((res) => {
					let temp = {
						original_filename: files[0].name,
						secure_url: res,
					};
					if (!this.selectedData["Ribbon"][0].uploadList) {
						this.selectedData["Ribbon"][0].uploadList = [];
					}
					let tempArr = JSON.parse(JSON.stringify(this.selectedData["Ribbon"][0].uploadList));
					this.selectedData["Ribbon"][0].uploadList = [];
					tempArr ? tempArr.push(temp) : (tempArr = [temp]);
					this.selectedData["Ribbon"][0].uploadList = tempArr;
					this.$refs.upload1.value = "";
					this.$forceUpdate();
				})
				.finally(() => {
					this.$gl.hide();
				});
		},
		uploadPic2(event) {
			this.$gl.show();
			let files = event.target.files;
			uploadFile(files[0])
				.then((res) => {
					let temp = {
						original_filename: files[0].name,
						secure_url: res,
					};
					this.selectedData["Upload Artwork & Comments"][0].uploadList ? this.selectedData["Upload Artwork & Comments"][0].uploadList.push(temp) : (this.selectedData["Upload Artwork & Comments"][0].uploadList = [temp]);
					this.$refs.upload2.value = "";
					this.$forceUpdate();
				})
				.finally(() => {
					this.$gl.hide();
				});
		},
		//Design Your Printing 上传
		uploadPicClipart(event, type = "upload") {
			this.$gl.show();
			let files = type === "upload" ? event.target.files : event;
			let fileResult = checkFile(Array.from(files), this.acceptFileType, 80);
			if (!fileResult) {
				this.$toast.error("File type error");
				this.$gl.hide();
				return false;
			}
			if (fileResult.nomalSize.length == 0) {
				this.$gl.hide();
				this.$store.commit("setSizeDialog", true);
				this.$store.commit("setInputRefName", "uploadPicClipart");
				this.$store.commit("setOverSizeList", fileResult.overSize);
				this.$refs.upload3.value = "";
				return false;
			}
			uploadFile(fileResult.nomalSize[0])
				.then((res) => {
					let temp = {
						original_filename: files[0].name,
						secure_url: res,
					};
					if (this.clipartType === "start") {
						//图片传给后台
						this.selectedData["Design Your Printing"][0].fontBeforeImg ? this.selectedData["Design Your Printing"][0].fontBeforeImg.push(temp) : (this.selectedData["Design Your Printing"][0].fontBeforeImg = [temp]);
						//图片回显
						this.fontBeforeImg = temp.secure_url;
					} else {
						this.selectedData["Design Your Printing"][0].fontAfterImg ? this.selectedData["Design Your Printing"][0].fontAfterImg.push(temp) : (this.selectedData["Design Your Printing"][0].fontAfterImg = [temp]);
						this.fontAfterImg = temp.secure_url;
					}
					this.$refs.upload3.value = "";
					this.$forceUpdate();
				})
				.finally(() => {
					this.$gl.hide();
					this.$toast.success("success");
				});
		},
		shutMask() {
			this.checking = false;
			this.currentStep = null;
		},
		jump(val) {
			let target;
			if (val == "color card" || val == "Lanyard Design Colors") {
				target = "Lanyard Popular Colors";
			} else {
				target = val;
			}
			this.checking = true;
			this.currentStep = target;
			this.scrollFun(target);
			this.showDetails = false;
		},
		nextStepFun(name) {
			const step10ParamName = "Lanyard Badge Holder Options";
			if (name == step10ParamName && this.productsCategoriesName == "Lanyards & Badge Holder") {
				this.selectedData["Select Your Card"] = [];

				if (this.selectedData[step10ParamName][0]) {
					let step10ParamCode = this.selectedData[step10ParamName][0]?.childList.find((item) => item.quantity !== "")?.paramCode;

					// let step11Data=this.filterShowGeneralData.find(fItem=>fItem.paramName=="Select Your Card")
					let step11Data = this.generalData.find((fItem) => fItem.paramName == "Select Your Card");

					const step11NeedBigObj = step11Data?.childList.find((item) => item.childList.some((child) => child.paramCode === step10ParamCode));
					if (step11Data) {
						var step11SelectedObj = this.findObjectByParamCode(step11Data.childList, step10ParamCode);
					}

					if (!step11SelectedObj) return;
					this.selectedData["Select Your Card"][0] = {
						firstCheck: true,
						disabled: false,
						selectedObj: step11SelectedObj,
						...step11NeedBigObj,
					};

					let refId = this.selectedData["Select Your Card"][0].id;
					this.$refs["CardBox" + refId][0].setFormDataSelectedObj();
				}
			}
			this.checkForm().then((res) => {
				if (res) {
					this.checking = false;
					this.scrollFun("foot");
					this.currentStep = null;
				}
			});
			if (this.currentStep == name) {
				this.errorMessage(name);
			}
		},
		errorMessage(name) {
			if (name == "Ribbon") {
				this.$toast.error("Please choose the ribbon options.");
			} else if (name == "Upload Artwork & Comments") {
				this.$toast.error("Please Upload your Artwork.");
			} else if (name == "Additional Upgrades (Options)") {
				this.$toast.error("Please Select Your Additional Upgrades.");
			} else if (name == "Medal Size") {
				this.$toast.error("Please Select Your Medal Size.");
			} else if (name == "Metal Finish") {
				this.$toast.error("Please Select Your Metal Finish.");
			} else if (name == "Lanyard Attachment") {
				this.$toast.error("Please Select Your Lanyard Attachment.");
			} else if (name == "Select Printing Method") {
				this.$toast.error("Please Select Your Printing Method");
			}
		},
		//校验
		checkForm(obj) {
			return new Promise(async (resolve) => {
				let boo = true;
				let that = this;
				let noPass = function (obj, key) {
					that.checking = true;
					if (!obj) {
						that.scrollFun(key); //根据key滚动
					}
					that.currentStep = obj ? obj : key; //下一步步骤名
					return false;
				};

				for (let key in this.selectedData) {
					if ((this.productsCategoriesName == "Lanyards Only" && key == "Select Your Card") || (this.productsCategoriesName == "Lanyards Only" && key == "Lanyard Badge Holder Options") || (this.productsCategoriesName == "Lanyards & PVC Card" && key == "Lanyard Badge Holder Options")) {
					} else if (key == "Upload Artwork & Comments") {
						let temp = this.selectedData[key][0];
						if (temp.later || (temp.uploadList && temp.uploadList.length > 0)) {
						} else {
							boo = noPass(obj, key);
							break;
						}
					} else if (key == "Lanyard Popular Colors" || key == "color card" || key == "Lanyard Design Colors") {
						if (this.selectedData["Lanyard Design Colors"]?.length || this.selectedData["color card"]?.length || this.selectedData["Lanyard Popular Colors"]?.length) {
						} else {
							boo = noPass(obj, key);
							break;
						}
					} else if (key == "Select Your Card") {
						if (this.selectedData["Select Your Card"][0]?.paramName === "No Upgrades") {
						} else {
							if (this.selectedData["Select Your Card"].length == 0) {
								// this.$refs.CardBox.validForm()
								boo = noPass(obj, key);
								break;
							} else {
								let temp = this.selectedData["Select Your Card"][0];
								let ac;
								this.$refs["CardBox" + temp.id][0].validForm((x) => {
									ac = x;
								});
								// ---------
								if (this.selectedData["Select Your Card"][0].firstCheck) {
									this.$refs["CardBox" + temp.id][0].clearValid();
								}
								this.selectedData["Select Your Card"][0].firstCheck = false;
								// ---------
								if (!ac) {
									boo = noPass(obj, key);
									break;
								}
							}
						}
					} else if (key == "Lanyard Badge Holder Options") {
						if (this.selectedData["Lanyard Badge Holder Options"].length == 0 || !this.selectedData["Lanyard Badge Holder Options"][0]?.childList[0]?.quantity) {
							boo = noPass(obj, key);
							break;
						}
					} else if (key == "Lanyard Attachment") {
						if (this.copyCanvasData.ropeCross) {
							if (this.selectedData["Lanyard Attachment"].length == 0) {
								boo = noPass(obj, key);
								break;
							}
						} else {
							if (this.sameAsTheLeftAttachement) {
								if (this.selectedData["Lanyard Attachment"].length == 0) {
									boo = noPass(obj, key);
									break;
								}
							} else {
								if (this.selectedData["Lanyard Attachment"].length < 2) {
									boo = noPass(obj, key);
									break;
								}
							}
						}
					} else if (key == "Badge Reels") {
						if (this.selectedData["Badge Reels"].length == 0) {
							boo = noPass(obj, key);
							break;
						}
					} else if (key == "Select Printing Method") {
						if (!this.selectedData["Select Printing Method"].length) {
							boo = noPass(obj, key);
							break;
						} else {
							if (!this.selectedData["Select Printing Method"][0].childList.length) {
								boo = noPass(obj, key);
								break;
							}
						}
					} else if (key == "Design Your Printing" || key == "Print Position Options") {
						if (this.selectedData["Design Your Printing"].length == 0) {
							if (this.copyCanvasData.commentsImgLater || this.fontImgCustom.length) {
							} else {
								boo = noPass(obj, key);
								break;
							}
						} else {
							let ac;
							await this.$nextTick();
							this.$refs["PrintTabs"][0].validForm((x) => {
								ac = x;
							});
							if (!ac) {
								boo = noPass(obj, key);
								break;
							}
						}
					} else if (key == "Lanyard Width" || key == "Lanyard Length") {
						if (this.selectedData["Lanyard Width"].length == 0 || this.selectedData["Lanyard Length"].length == 0) {
							boo = noPass(obj, key);
							break;
						}
					} else if (this.selectedData[key].length == 0) {
						boo = noPass(obj, key);
						break;
					}
				}
				resolve(boo);
			});
		},
		//滚动
		scrollFun(target) {
			const element = document.getElementById(target);
			if (element) {
				let offset = 0;
				if (target === "foot") {
					offset = -100;
				}
				scrollToViewTop(element, offset);
			}
		},
		findObjectByParamCode(data, paramCode) {
			for (const item of data) {
				if (item?.paramCode === paramCode) {
					return item;
				}
				if (item.childList.length > 0) {
					for (const childItem of item.childList) {
						if (childItem.paramCode === paramCode) {
							return childItem;
						}
					}
				}
			}
			return null;
		},
		//通用
		async selectFun(obj, index,item) {
			if(index >= 0) this.selectIndex = index;
			//记录用户的选择
			if(item&&obj.type!==false){
				try {
					this.$store.commit("setGoogleUploadAction", {
						[`STEP${item.customIndex}:${obj.key}`]: obj.value.paramName||obj.value.cateName,
						content_id: this.pid,
			        });
				} catch (error) {

				}
			}
			if (obj.key == "Lanyard Badge Holder Options" || obj.key == "Badge Reels") {
				this.selectedData[obj.key] = obj.value;
			} else if (obj.key == "Design Your Printing" && !obj.value) {
				this.selectedData[obj.key] = [];
			} else {
				this.selectedData[obj.key] = [obj.value];
			}

			this.lanyardLengthData = this.getInfo("Lanyard Length");
			if (obj.key == "Additional Options") {
				this.additionalOptionsFun(obj);
			} else if (obj.key == "Design Your Printing") {
				//   this.designYourPrintingFun(obj.value)
				this.$nextTick(() => {
					this.debounceCanvasInit();
				});
			} else if (obj.key == "Lanyard Style") {
				this.lanyardStyleName = obj.value.cateNameQuote;
				this.cateId = obj.value.id;
				await this.getCateParamRelationByCateId();
				// 获取新的Categories
				if (this.selectedData["Products Categories"] && this.selectedData["Products Categories"].length != 0) this.selectedData["Products Categories"][0] = this.getInfo("Products Categories").childList.find((x) => x.id == this.selectedData["Products Categories"][0].id);

				this.defaultData = obj.value;
				if (this.totalColorNum >= 3 && this.lanyardStyleName != "Dye Sublimated Lanyards") {
					this.fontBeforeImg = "";
					this.fontAfterImg = "";
				}
				//清除
				try {
					this.$refs.colorPicker[0].findAll();
					// if (this.lanyardStyleName !== "Woven Lanyards") {
					// 	this.$refs.PrintTabs[0].clearDoubleFont();
					// }
				} catch (error) {}
			} else if (obj.key == "Lanyard Width" && this.lanyardType != "phoneLanyards") {
				this.filterStitchStyleFun();
			} else if (obj.key == "Select Your Glitter Colors") {
				this.copyCanvasData.extraImg = JSON.parse(obj.value.priceInfo.imagePath);
				this.$nextTick(() => {
					this.debounceCanvasInit();
				});
			} else if (obj.key == "Lanyard Length") {
				const lengthItem = this.dictModel.lengthList.find((x) => x.paramCode == obj.value.paramCode);
				this.danImage = lengthItem?.img1;
				this.danImageB = lengthItem?.img2;
				this.shuangeImage = lengthItem?.img3;
				this.$nextTick(() => {
					this.debounceCanvasInit();
				});
			} else if (obj.key === "Lanyard Attachment" && this.lanyardType === "lightUpLanyards") {
				this.copyCanvasData.buckleC1 = JSON.parse(obj.value.imageJson)[1].url + "?temp=" + new Date().valueOf();
				this.$nextTick(() => {
					this.debounceCanvasInit();
				});
			}

			await this.filterStepsFun().then((res) => {
				this.noChoiceData = res;
			});

			this.$forceUpdate();
			//如果是iframe传递的事件就不进行校验
			if (obj.type === "isIframe") {
				this.hideContent = true;
				return false;
			}
			if (
				//无需跳转下一步 需要手动点击
				obj.key == "Design Your Printing" ||
				(obj.key == "Select Your Card" && obj.value.paramName != "No Upgrades") ||
				(obj.key == "Lanyard Badge Holder Options" && obj.value[0]?.paramName != "No Upgrades") ||
				(obj.key == "Badge Reels" && obj.value[0]?.paramName != "No Upgrades")
			) {
				this.checkForm(obj.key).then((res) => {
					if (res) {
						this.checking = false;
						// this.scrollFun("foot");
						this.currentStep = null;
					}
				});
			} else {
				this.checkForm().then((res) => {
					if (res) {
						this.checking = false;
						this.scrollFun("foot");
						this.currentStep = null;
					}
				});
			}
		},
		productsCategoriesFun(obj,index) {
			//记录用户的选择
			this.$store.commit("setGoogleUploadAction", {
				[`STEP${index}:${obj.key}`]: obj.value.paramName,
				content_id: this.pid,
			});
			this.selectedData[obj.key] = [obj.value];
			this.productsCategoriesName = obj.value.paramName;
			this.checkForm().then((res) => {
				if (res) {
					this.checking = false;
					this.scrollFun("foot");
					this.currentStep = null;
				}
			});
			this.filterStepsFun().then((res) => {
				this.noChoiceData = res;
			});
		},
		selectDiscount(item, citem, cindex,tempItem) {
			this.isActive = cindex + 1;
			try {
				//记录用户的选择
				this.$store.commit("setGoogleUploadAction", {
					[`STEP${tempItem.customIndex}:${tempItem.alias}`]: citem.paramName,
					content_id: this.pid,
				});
			}catch(e){}
			this.selectedData[item.paramName] = [citem];
			this.checkForm().then((res) => {
				if (res) {
					this.checking = false;
					this.scrollFun("foot");
					this.currentStep = null;
				}
			});
			this.filterStepsFun().then((res) => {
				this.noChoiceData = res;
			});
		},
		additionalOptionsFun(obj) {
			if (obj.value.paramName == "No Upgrades") {
				this.copyCanvasData.buckleA1 = "";
				this.copyCanvasData.buckleB1 = "";
			} else {
				switch (obj.value.paramName) {
					case "Flat Plastic Breakaway":
						this.copyCanvasData.buckleA1 = JSON.parse(obj.value.imageJson)[1].url;
						this.copyCanvasData.buckleB1 = "";
						break;
					case "Plastic Buckle":
						this.copyCanvasData.buckleA1 = "";
						this.copyCanvasData.buckleB1 = JSON.parse(obj.value.imageJson)[1].url;
						break;
					case "Velcro Breakaway":
						this.copyCanvasData.buckleA1 = JSON.parse(obj.value.imageJson)[1].url;
						this.copyCanvasData.buckleB1 = "";
						break;
					case "Plastic Buckle & Plastic Breakaway":
						this.copyCanvasData.buckleA1 = JSON.parse(obj.value.imageJson)[1].url;
						this.copyCanvasData.buckleB1 = JSON.parse(obj.value.imageJson)[2].url;
						break;
					case "Plastic Buckle & Velcro Breakaway":
						this.copyCanvasData.buckleA1 = JSON.parse(obj.value.imageJson)[1].url;
						this.copyCanvasData.buckleB1 = JSON.parse(obj.value.imageJson)[2].url;
						break;
				}
			}
			this.$nextTick(() => {
				this.debounceCanvasInit();
			});
		},
		//询盘
		submitInquiry() {
			this.checkForm().then((res) => {
				if (res) {
					this.addInquiry();
				}
			});
		},
		//购物车
		addToCart() {
			this.checkForm().then((res) => {
				if (res) {
					this.addCart();
				}
			});
		},
		getByPId() {
			medalsApi
				.getByPId({
					pid: this.pid,
				})
				.then(async (res) => {
					this.medalStyleData.childList = res.data;
					this.cateId = this.medalStyleData.childList[0].id;
					this.lanyardStyleName = this.medalStyleData.childList[0].cateNameQuote || this.medalStyleData.childList[0].cateName; // 适配多小类织带
					this.defaultData = this.medalStyleData.childList[0];
					await this.getCateParamRelationByCateId();
					await this.getBanParamList(res.data[0].id);
					await this.selectDefaultParam();
					if (this.isIframe) {
						let targetWindow = window.opener || window.parent; // 目标窗口，可以是父窗口或打开当前窗口的窗口
						targetWindow.postMessage(
							{
								type: "selectStyle",
							},
							window.origin
						); // 发送消息
					}
					if (this.medalStyleData.childList.length === 1) {
						this.selectedData[this.medalStyleData.paramName] = [res.data[0]];
					}
				});
		},
		movearritem(arr, key, item) {
			for (var i = 0; i < arr.length; i++) {
				if (arr[i] == key) {
					arr.splice(arr.indexOf(item) + 1, 0, arr[i]); //index:元素需要放置的位置索引，从0开始
					arr.splice(i + 1, 1); //移除原来位置上的该元素
					break;
				}
			}
			return arr;
		},

		//切换分类回填已选中过的参数
		backQuoteParams() {
			let newSelectedParams = {},
				generalData = deepClone(this.generalData),
				selectedData = deepClone(this.selectedData);
			let selectId;

			function findId(arr) {
				for (let i = 0; i < arr.length; i++) {
					let item = arr[i];
					if (item.id === selectId) {
						return item;
					} else {
						if (item.childList && item.childList.length) {
							const foundInChild = findId(item.childList); // 递归查找子级
							if (foundInChild) {
								return foundInChild; // 如果子级找到则返回
							}
						}
					}
				}
			}

			//遍历总纲
			for (let i = 0; i < generalData.length; i++) {
				let item = generalData[i];
				let paramName = item.paramName;
				//判断是否存在选中的参数
				if (selectedData[paramName] && selectedData[paramName].length) {
					let newArr = [];
					//遍历选中的参数
					selectedData[paramName].forEach((pitem) => {
						selectId = pitem.id;
						let findItem = findId(item.childList);
						if (findItem) {
							pitem.priceInfo = findItem.priceInfo;
							findItem = pitem;
							newArr.push(pitem);
						}
					});
					if (newArr.length) {
						newSelectedParams[paramName] = newArr;
					} else {
						newSelectedParams[paramName] = [];
					}
				} else {
					newSelectedParams[paramName] = [];
				}
			}
			//双向回填
			this.generalData = generalData;
			this.selectedData = newSelectedParams;
		},
		setIndex(data) {
			if (!data) {
				return false;
			}
			let index = 0;
			for (let i = 0; i < data.length; i++) {
				let item = data[i];
				if (item) {
					if (item.paramName === "Lanyard Style" && item.childList.length === 1) {
						continue;
					}
					if (this.productsCategoriesName === "Lanyards Only") {
						if (item.paramName === "Lanyard Badge Holder Options") {
							continue;
						}
						if (item.paramName === "Select Your Card") {
							continue;
						}
					} else if (this.productsCategoriesName === "Lanyards & PVC Card") {
						if (item.paramName === "Lanyard Badge Holder Options") {
							continue;
						}
					}
					//led 橡胶织带不展示尺寸
					if (this.lanyardType === "lightUpLanyards" || this.lanyardType === "neopreneLanyards") {
						if (item.paramName === "Lanyard Width" || item.paramName === "Lanyard Length" || item.paramName === "color card") {
							continue;
						}
					}
					index += 1;
					item.customIndex = index;
				}
			}
			return data;
		},
		sortByKey(array, key) {
			return array.sort(function (a, b) {
				let x = a[key]; //如果要从大到小,把x,y互换就好
				let y = b[key];
				return x < y ? -1 : x > y ? 1 : 0;
			});
		},
		getCateParamRelationByCateId() {
			this.shutMask();
			return new Promise((resolve, reject) => {
				let loadingInstance = Loading.service({
					target: "#leftArea",
				});
				medalsApi
					.getCateParamRelationByCateId({
						cateId: this.cateId,
					})
					.then((res) => {
						this.generalData = res.data;
						this.generalData = this.setIndex(this.sortByKey(this.generalData.concat(this.medalStyleData), "stepIndex"));
						//副本
						let selectedData = JSON.parse(JSON.stringify(this.selectedData));
						let tempObj = {},
							tempObj2 = {},
							tempArr = [];

						this.generalData.forEach((item, index) => {
							item.childList.forEach((x) => {
								x.inputNum = undefined;
							});
							if (item.paramName == "Upload Artwork & Comments") {
								// item.later = false;
								tempObj[item.paramName] = [item];
							} else {
								tempObj[item.paramName] = [];
							}
							tempArr.push(item.paramName);
						});
						tempArr = this.movearritem(tempArr, "Lanyard Length", "Lanyard Width");
						tempArr = this.movearritem(tempArr, "color card", "Lanyard Popular Colors");
						tempArr = this.movearritem(tempArr, "Lanyard Design Colors", "color card");
						tempArr = this.movearritem(tempArr, "Print Position Options", "Design Your Printing");
						tempArr.forEach((item) => {
							tempObj2[item] = tempObj[item];
						});

						for (let item in tempObj2) {
							if (selectedData[item]) {
								tempObj2[item] = selectedData[item];
							}
						}
						//副本赋值
						this.selectedData = tempObj2;
						this.backQuoteParams();
						delete this.selectedData["Design Your Printing Color"];
						//过滤选项
						this.backfill();
						if (this.lanyardType != "phoneLanyards") this.filterStitchStyleFun();
						this.filterStepFun(this.productsCategoriesName);
						// 设置材质
						const lanyard_style = this.dictModel.materialList.find((x) => x.paramName === this.lanyardStyleName);
						if (this.lanyardType === "shortWristLanyards") {
							this.fugaiImage = lanyard_style?.img4;
						} else {
							this.fugaiImage = lanyard_style?.img1;
							this.fugaiImage2 = lanyard_style?.img2;
							this.fugaiImage3 = lanyard_style?.img3;
						}

						this.lanyardLengthData = this.getInfo("Lanyard Length");
						this.bindValueDesign = this.getInfo("Lanyard Design Colors") ? this.getInfo("Lanyard Design Colors") : {};
						this.bindValueSide = this.getInfo("Print Position Options");
						if (this.lanyardType === "lightUpLanyards") {
							let lanyardLength = this.getInfo("Lanyard Length");
							let lanyardWidth = this.getInfo("Lanyard Width");
							this.selectedData["Lanyard Width"] = [lanyardWidth.childList[0]];
							this.selectedData["Lanyard Length"] = [lanyardLength.childList[0]];
							let paramCode = this.getInfo("Lanyard Popular Colors")?.childList[0]?.paramCode.split(",");
							this.copyCanvasData.popularImg = this.copyCanvasData.popularImg ? this.copyCanvasData.popularImg : this.generatePopularImg(paramCode[0], paramCode[1]);
							this.copyCanvasData.buckleC1 = this.copyCanvasData.buckleC1 ? this.copyCanvasData.buckleC1 : JSON.parse(this.getInfo("Lanyard Attachment").childList[0].imageJson)[1].url + "?temp=" + new Date().valueOf();
							this.copyCanvasData.buckleC1_1 = this.copyCanvasData.buckleC1_1 ? this.copyCanvasData.buckleC1_1 : JSON.parse(this.getInfo("Lanyard Attachment").childList[0].imageJson)[1].url + "?temp=" + new Date().valueOf();
							this.copyCanvasData.buckleC1_2 = this.copyCanvasData.buckleC1_2 ? this.copyCanvasData.buckleC1_2 : JSON.parse(this.getInfo("Lanyard Attachment").childList[0].imageJson)[1].url + "?temp=" + new Date().valueOf();
							this.danImage = "https://static-oss.gs-souvenir.com/web/quoteManage/20250421/led背景2_202503175pCxEj_20250421J2jJTK.jpg";
							this.copyCanvasData.lightUpLanyard = JSON.parse(this.getInfo("Lanyard Popular Colors")?.childList[0]?.priceInfo.imagePath)[0].path;
						} else if (this.lanyardType === "phoneLanyards") {
							let paramCode = this.getInfo("Lanyard Popular Colors")?.childList[0]?.paramCode.split(",");
							const lengthDefParam = this.getInfo("Lanyard Length")?.childList.find((x) => x.priceInfo.isCommonQuoteSelected);
							const lengthDef = this.dictModel.lengthList.find((x) => x.paramCode == (lengthDefParam?.paramCode || 7));
							const styleDef = this.dictModel.materialList.find((x) => x.paramName == "Phone Lanyards");
							this.danImage = lengthDef.img1;
							this.fugaiImage = styleDef.img1;
							this.copyCanvasData.buckleC1Height = 240;
							this.copyCanvasData.defautlPaddingTop = 90;
							this.copyCanvasData.popularImg = this.copyCanvasData.popularImg ? this.copyCanvasData.popularImg : this.generatePopularImg(paramCode[0], paramCode[1]);
							this.copyCanvasData.buckleC1 = this.copyCanvasData.buckleC1 ? this.copyCanvasData.buckleC1 : "https://static-oss.gs-souvenir.com/web/quoteManage/20250207/手机挂绳图_20250207FnXbNY.png" + "?temp=" + new Date().valueOf();
							this.copyCanvasData.buckleC1_1 = this.copyCanvasData.buckleC1_1 ? this.copyCanvasData.buckleC1_1 : "https://static-oss.gs-souvenir.com/web/quoteManage/20250207/手机挂绳图_20250207FnXbNY.png" + "?temp=" + new Date().valueOf();
							this.copyCanvasData.buckleC1_2 = this.copyCanvasData.buckleC1_2 ? this.copyCanvasData.buckleC1_2 : "https://static-oss.gs-souvenir.com/web/quoteManage/20250207/手机挂绳图_20250207FnXbNY.png" + "?temp=" + new Date().valueOf();
						} else if (this.lanyardType === "shortWristLanyards") {
							let paramCode = this.getInfo("Lanyard Popular Colors")?.childList[0]?.paramCode.split(",");
							const lengthDefParam = this.getInfo("Lanyard Length").childList.find((x) => x.priceInfo.isCommonQuoteSelected);
							const lengthDef = this.dictModel.lengthList.find((x) => x.paramCode == (lengthDefParam?.paramCode || 12));
							this.danImage = lengthDef.img1;
							this.copyCanvasData.defautlPaddingTop = 165;
							this.copyCanvasData.popularImg = this.copyCanvasData.popularImg ? this.copyCanvasData.popularImg : this.generatePopularImg(paramCode[0], paramCode[1]);
							this.copyCanvasData.buckleC1 = this.copyCanvasData.buckleC1 ? this.copyCanvasData.buckleC1 : "https://static-oss.gs-souvenir.com/web/quoteManage/20250213/20220527hpF8YXaD_20250108mXkkfW_20250213wb3xdP.png" + "?temp=" + new Date().valueOf();
							this.copyCanvasData.buckleC1_1 = this.copyCanvasData.buckleC1_1 ? this.copyCanvasData.buckleC1_1 : "https://static-oss.gs-souvenir.com/web/quoteManage/20250213/20220527hpF8YXaD_20250108mXkkfW_20250213wb3xdP.png" + "?temp=" + new Date().valueOf();
							this.copyCanvasData.buckleC1_2 = this.copyCanvasData.buckleC1_2 ? this.copyCanvasData.buckleC1_2 : "https://static-oss.gs-souvenir.com/web/quoteManage/20250213/20220527hpF8YXaD_20250108mXkkfW_20250213wb3xdP.png" + "?temp=" + new Date().valueOf();
						} else if (this.lanyardType === "reflectiveLanyards") {
							this.copyCanvasData.extraImg = this.dictModel.extraMaterialList.find((x) => x.paramName === "Reflective Lanyards").imgList;
							let paramCode = this.getInfo("Lanyard Popular Colors")?.childList[0]?.paramCode.split(",");
							this.copyCanvasData.popularImg = this.copyCanvasData.popularImg ? this.copyCanvasData.popularImg : this.generatePopularImg(paramCode[0], paramCode[1]);
							this.copyCanvasData.buckleC1 = this.copyCanvasData.buckleC1 ? this.copyCanvasData.buckleC1 : JSON.parse(this.getInfo("Lanyard Attachment").childList[0].childList[0].imageJson)[1].url + "?temp=" + new Date().valueOf();
							this.copyCanvasData.buckleC1_1 = this.copyCanvasData.buckleC1_1 ? this.copyCanvasData.buckleC1_1 : JSON.parse(this.getInfo("Lanyard Attachment").childList[0].childList[0].imageJson)[1].url + "?temp=" + new Date().valueOf();
							this.copyCanvasData.buckleC1_2 = this.copyCanvasData.buckleC1_2 ? this.copyCanvasData.buckleC1_2 : JSON.parse(this.getInfo("Lanyard Attachment").childList[0].childList[0].imageJson)[1].url + "?temp=" + new Date().valueOf();
						} else {
							let paramCode = this.getInfo("Lanyard Popular Colors")?.childList[0]?.paramCode.split(",");
							this.copyCanvasData.popularImg = this.copyCanvasData.popularImg ? this.copyCanvasData.popularImg : this.generatePopularImg(paramCode[0], paramCode[1]);
							this.copyCanvasData.buckleC1 = this.copyCanvasData.buckleC1 ? this.copyCanvasData.buckleC1 : JSON.parse(this.getInfo("Lanyard Attachment").childList[0].childList[0].imageJson)[1].url + "?temp=" + new Date().valueOf();
							this.copyCanvasData.buckleC1_1 = this.copyCanvasData.buckleC1_1 ? this.copyCanvasData.buckleC1_1 : JSON.parse(this.getInfo("Lanyard Attachment").childList[0].childList[0].imageJson)[1].url + "?temp=" + new Date().valueOf();
							this.copyCanvasData.buckleC1_2 = this.copyCanvasData.buckleC1_2 ? this.copyCanvasData.buckleC1_2 : JSON.parse(this.getInfo("Lanyard Attachment").childList[0].childList[0].imageJson)[1].url + "?temp=" + new Date().valueOf();
						}

						this.$nextTick(() => {
							this.debounceCanvasInit();
						});
						this.$nextTick(() => {
							loadingInstance.close();
						});
						resolve(res);
					});
			});
		},
		openWidget() {
			this.myWidget.open();
		},
		initWidget() {
			this.myWidget = cloudinary.createUploadWidget(
				{
					cloudName: "gs-jj-cloud",
					uploadPreset: "icz0sjex",
					sources: ["local", "url", "camera", "image_search", "google_drive", "facebook", "dropbox", "instagram", "shutterstock", "istock", "unsplash"],
					googleApiKey: "AIzaSyCs9WaBS8LN0lZvlrIGGRcI6Voxt59jXtU",
					showAdvancedOptions: false,
					cropping: true,
					multiple: false,
					defaultSource: "local",
					styles: {
						palette: {
							window: "#FFFFFF",
							windowBorder: "#90A0B3",
							tabIcon: "#0078FF",
							menuIcons: "#5A616A",
							textDark: "#000000",
							textLight: "#FFFFFF",
							link: "#0078FF",
							action: "#FF620C",
							inactiveTabIcon: "#0E2F5A",
							error: "#F44235",
							inProgress: "#0078FF",
							complete: "#20B832",
							sourceBg: "#E4EBF1",
						},
						fonts: {
							default: {
								active: true,
							},
						},
					},
					text: {
						en: {},
					},
				},
				async (error, result) => {
					if (!error && result && result.event === "success") {
						this.$toast.success("Upload Success!");
						if (this.beforeAfter == "1") {
							// this.copyCanvasData.fontBeforeImg = result.info.secure_url
							this.fontBeforeImg = result.info.secure_url;
							// this.countColor()
						} else if (this.beforeAfter == "2") {
							// this.copyCanvasData.fontAfterImg = result.info.secure_url
							this.fontAfterImg = result.info.secure_url;
							// this.countColor()
						} else if (this.beforeAfter == "3") {
							this.fontImgCustom.push(result.info.secure_url);
						} else if (this.beforeAfter == "4") {
							this.selectedData["Select Your Card"][0].uploadImg = result.info.secure_url;
						} else if (this.beforeAfter == "5") {
							this.selectedData["Select Your Card"][0].uploadImg = result.info.secure_url;
						}
					}
				}
			);
		},
		debounce(func, delay = 1000, immediate = false) {
			//闭包
			let timer = null;
			//不能用箭头函数
			return function () {
				if (timer) {
					clearTimeout(timer);
				}
				if (immediate && !timer) {
					func.apply(this, arguments);
				}
				timer = setTimeout(() => {
					func.apply(this, arguments);
				}, delay);
			};
		},
		canvasInitFun() {
			this.canvasData = Object.assign({}, this.copyCanvasData);
			this.$nextTick(() => {
				try {
					this.$refs.CanvasFactory.canvasInit();
				} catch (e) {}
			});
		},
		filterStepFun(val) {
			const badgeHolderOptionData = this.generalData.find((x) => x.paramName == "Lanyard Badge Holder Options");
			const badgeReelsData = this.generalData.find((x) => x.paramName == "Badge Reels");
			//清空隐藏步骤的数据
			if (badgeHolderOptionData && badgeHolderOptionData.show) {
				try {
					this.selectedData["Lanyard Badge Holder Options"] = [];
					this.$refs.badgeHolderOption[0].reset();
					badgeHolderOptionData.childList.forEach((x) => {
						x.childList.forEach((y) => {
							delete y.quantity;
						});
					});
				} catch (e) {}
			}
			if (badgeReelsData?.show) {
				try {
					this.selectedData["Badge Reels"] = [];
					badgeReelsData.childList.forEach((x) => {
						x.childList.forEach((y) => {
							delete y.quantity;
						});
					});
				} catch (e) {}
			}
			switch (val) {
				case "Lanyards Only":
					this.replaceIndex = -2;
					if (this.lanyardType !== "shortWristLanyards" && this.lanyardType !== "phoneLanyards") {
						this.getInfo("Lanyard Badge Holder Options", "generalData").show = false;
						this.getInfo("Lanyard Badge Holder Options", "generalData").childList[0].noShow = true;
						this.getInfo("Select Your Card", "generalData").show = false;
						this.getInfo("Select Your Card", "generalData").childList[0].show = false;
						this.getInfo("Select Your Card", "generalData").childList[1].show = false;
						this.getInfo("Select Your Card", "generalData").childList[2].show = false;
					}
					if (this.lanyardType !== "lightUpLanyards" && this.lanyardType !== "shortWristLanyards" && this.lanyardType !== "phoneLanyards") {
						this.getInfo("Badge Reels", "generalData").show = false;
					}
					if (this.medalStyleData.childList && this.medalStyleData.childList.length > 1) this.getInfo("Lanyard Width", "generalData").show = true;
					break;
				case "Lanyards & Badge Holder":
					this.replaceIndex = 1;
					this.getInfo("Lanyard Badge Holder Options", "generalData").show = true;
					if (this.lanyardType !== "lightUpLanyards") {
						this.getInfo("Badge Reels", "generalData").show = true;
					}
					this.getInfo("Lanyard Badge Holder Options", "generalData").childList[0].noShow = true;
					this.getInfo("Select Your Card", "generalData").show = true;
					this.getInfo("Select Your Card", "generalData").childList[0].show = true;
					this.getInfo("Select Your Card", "generalData").childList[1].show = false;
					this.getInfo("Select Your Card", "generalData").childList[2].show = true;
					this.selectedData["Select Your Card"] = [];
					if (this.medalStyleData.childList && this.medalStyleData.childList.length > 1) this.getInfo("Lanyard Width", "generalData").show = true;
					break;
				case "Lanyards & PVC Card":
					this.replaceIndex = 0;
					this.getInfo("Lanyard Badge Holder Options", "generalData").show = false;
					if (this.lanyardType !== "lightUpLanyards") {
						this.getInfo("Badge Reels", "generalData").show = true;
					}
					this.getInfo("Lanyard Badge Holder Options", "generalData").childList[0].noShow = true;
					this.getInfo("Select Your Card", "generalData").show = true;
					this.getInfo("Select Your Card", "generalData").childList[0].show = false;
					this.getInfo("Select Your Card", "generalData").childList[1].show = true;
					this.getInfo("Select Your Card", "generalData").childList[2].show = false;
					this.selectedData["Select Your Card"] = [];
					if (this.medalStyleData.childList && this.medalStyleData.childList.length > 1) this.getInfo("Lanyard Width", "generalData").show = true;
					break;
				default:
					this.replaceIndex = 1;
					if (this.lanyardType !== "shortWristLanyards" && this.lanyardType !== "phoneLanyards") {
						this.getInfo("Lanyard Badge Holder Options", "generalData").show = true;
						this.getInfo("Lanyard Badge Holder Options", "generalData").childList[0].noShow = false;
						this.getInfo("Select Your Card", "generalData").show = true;
						this.getInfo("Select Your Card", "generalData").childList[0].show = true;
						this.getInfo("Select Your Card", "generalData").childList[1].show = true;
						this.getInfo("Select Your Card", "generalData").childList[2].show = true;
						this.selectedData["Select Your Card"] = [];
					}
					if (this.lanyardType !== "lightUpLanyards") {
						if (this.lanyardType !== "shortWristLanyards" && this.lanyardType !== "phoneLanyards") {
							this.getInfo("Badge Reels", "generalData").show = true;
						}
						this.getInfo("Lanyard Width", "generalData").show = true;
					} else {
						this.getInfo("Lanyard Popular Colors", "generalData").show = true;
						this.getInfo("Lanyard Width", "generalData").show = false;
					}
			}
		},
	},
	computed: {
		proType() {
			return this.$store.state.proType;
		},
		textInfo() {
			return {
				inquiryTip: this.lang.inquiryTip,
				addCartTip: this.lang.addCartTip,
				emailText: this.lang.emailText + " " + this.$store.state.proSystem.email,
				email: this.lang.mailto + this.$store.state.proSystem.email,
			};
		},
		device() {
			return this.$store.state.device;
		},
		projectName() {
			if (this.previewMode) {
				return "o2o-site-style";
			} else {
				return this.$store.state.proName;
			}
		},
		lang() {
			return this.$store.getters.lang.quote || {};
		},
		showMask() {
			if (this.selectedData["Lanyard Length"] && this.selectedData["Lanyard Length"].length) {
				return true;
			} else if (this.checking) {
				return this.selectedData["Lanyard Width"].length ? true : false;
			} else {
				return true;
			}
		},
		showWidthMask() {
			if (this.selectedData["Lanyard Width"]?.length && !this.selectedData["Lanyard Length"]?.length) {
				return true;
			}
			if (this.selectedData["Lanyard Width"]?.length && this.selectedData["Lanyard Length"]?.length) {
				return false;
			}
		},
		popularColor() {
			return this.selectedData["Lanyard Popular Colors"] || [];
		},
		designColor() {
			return this.selectedData["Lanyard Design Colors"] || [];
		},
		colorCard() {
			return this.selectedData["color card"] || [];
		},
	},
	watch: {
		allImgDialog(newValue){
			if (newValue) {
				lockScroll()
			}else{
				unlockScroll();
			}
			if (this.isIframe && this.device == "mb") {
				this.toggleIframeCloseIcon(!newValue);
			}
		},
		checking(newValue, oldValue){
			if (this.isIframe && this.device == "mb") {
				this.toggleIframeCloseIcon(!newValue);
			}
		},
		showDetails(newValue, oldValue) {
			if (this.isIframe && (this.device == 'mb')) {
				this.toggleIframeCloseIcon(!newValue);
			}
		},
		popularColor: {
			handler(val) {
				this.getGiftList();
			},
			deep: true,
		},
		designColor: {
			handler(val) {
				this.getGiftList();
			},
			deep: true,
		},
		colorCard: {
			handler(val) {
				this.getGiftList();
			},
			deep: true,
		},

		fontBeforeImg: {
			handler() {
				this.countColor();
			},
		},
		fontAfterImg: {
			handler() {
				this.countColor();
			},
		},
		productsCategoriesName: {
			handler(val) {
				this.setIndex(this.generalData);
				this.filterStepFun(val);
			},
		},
	},
	created() {
		this.debounceCalcPrice = debounce(this.calculate, 300);
		this.debounceCanvasInit = this.debounce(this.canvasInitFun, 300, false);
		this.findAll();
	},
	async mounted() {
		this.getByPId();
		this.$Bus.$on("uploadPicClipart", this.replayUpload);
		this.isIframe = !!this.$route.query.type;
		window.addEventListener(
			"message",
			(event) => {
				// 检查消息来源的安全性
				if (event.origin !== window.origin) {
					return;
				}
				if (event.data.type === "selectStyle") {
					let findStyle = this.medalStyleData.childList.find((item) => {
						return item.id == event.data.value;
					});
					if (findStyle) {
						this.selectFun({
							key: "Lanyard Style",
							type: "isIframe",
							value: findStyle,
						});
					}
				}
			},
			false
		);
	},
	beforeDestroy() {
		this.$Bus.$off("uploadPicClipart");
	},
};
</script>

<style scoped lang="scss">
@import url("https://fonts.googleapis.com/css2?family=Oswald:wght@200&display=swap");
.lightUpLanyards {
	::v-deep .colorPicker .el-tabs__nav-wrap {
		display: none;
	}
	::v-deep .colorPicker .el-tabs__header {
		margin: 0 !important;
	}
}
#lanyard {
	font-family: Calibri, Arial, serif;
	padding-top: 20px;
    font-size: 16px;

    @media screen and (max-width: 767px) {
        font-size: 12px;
		::v-deep input {
			height: 30px;
			line-height: 30px;
			padding-left: 10px;
			border-radius: 4px;
		}
    }


	::v-deep .el-drawer__header {
		margin-bottom: 0;
		padding: 5px;
	}

	::v-deep .el-drawer {
		background-color: #f3f4f5;
	}

	::v-deep img {
		object-fit: contain !important;
	}

	::v-deep video {
		object-fit: contain;
	}

	::v-deep ul {
		margin-left: 0;
	}

	::v-deep .video-js,
	::v-deep .vjs-poster {
		background-color: white !important;
	}

	.canvasFactory {
		display: none;
	}

	::v-deep .StepBox {
		&.active {
			border-color: $color-primary !important;
		}

		@media (any-hover: hover) {
			&:hover {
				.zoomIcon {
					color: $color-primary;
				}

				.product-info {
					.radio-beauty {
						background-color: $color-primary;
						border-color: $color-primary;

						&::after {
							background-color: white;
						}
					}

					.title {
						color: $color-primary;
					}
				}
			}
		}
	}

	.picWrap {
		display: flex;
		justify-content: center;
		align-items: center;
		flex-direction: column;
		width: 100%;
		padding: 0px !important;
	}

	.text-center {
		text-align: center;
	}

	.myBtn {
		background: $color-primary;
		border-radius: 10px;
		font-size: 18px;
		font-weight: 400;
		color: #ffffff;
		padding: 10px 65px;

		&:active {
			border-color: $color-primary;
		}

		&:hover {
			border-color: $color-primary;
		}

		@media screen and (max-width: 767px) {
			width: 125px;
			height: 30px;
			background: $color-primary;
			border-radius: 5px;
			font-size: 12px;
			font-weight: 400;
			color: #ffffff;
			// margin-top: 9.5px;
			padding: 0;
		}
	}

	.header {
		background-color: #fff;
		display: grid;
		grid-template-columns: repeat(48, 1fr);
		padding-bottom: 20px;
		grid-column: 2/49;

		h1 {
			position: relative;
			font-size: 48px;
			font-weight: bold;
			color: #333333;
			text-align: center;
			z-index: 0;
			grid-column: 1/49;
			text-align: center;
			padding-left: 10px;
			font-weight: normal;
		}

		@media screen and (max-width: 767px) {
			grid-template-columns: 1fr;

			h1 {
				padding-left: 20px;
				margin-bottom: 0;
				font-size: 21px;
				// grid-column: 2/33;
			}
		}
	}

	.priceTable {
		grid-column: 2/49;
		margin-bottom: 20px;
	}

	.content {
		position: relative;
		display: grid;
		grid-template-columns: repeat(48, 1fr);
		background-color: #fff;
		padding: 20px 0;

		.leftArea {
			grid-column: 2/29;

			@media screen and (max-width: 1500px) {
				grid-column: 1/49;
			}

			.advertisingBanner {
				background: url("http://customed-center.oss-us-west-1.aliyuncs.com/web/public/picStore/20240314/Custom_LanyardsPC_20240314azFA62.png") no-repeat;
				height: 60px;
				background-size: 100%;
				margin: 10px 0 0 30px;
				line-height: 60px;

				div {
					font-weight: bold;
					font-size: 24px;
					color: #ffffff;
					text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.63);
					text-align: center;

					span {
						color: #fff000;
					}
				}

				@media screen and (max-width: 767px) {
					background: url("http://customed-center.oss-us-west-1.aliyuncs.com/web/public/picStore/20240326/Custom_LanyardsMb_20240326kstXY3.png") no-repeat;
					height: 18.3333vw;
					background-size: 100%;
					margin: 0;
					line-height: 1;
					padding: 0.5667vw 3.4vw;

					div {
						font-size: 4vw;
						line-height: 6vw;
						padding-top: 9px;
					}
				}
			}

			.step-title {
				font-size: 27px;
				font-weight: bold;
				color: #202428;
				margin-bottom: 15px;

				span{
					color: $color-primary;
				}

				@media screen and (max-width: 767px) {
					margin-bottom: 15px;
					font-size: 16px;
				}

				// > span {
				// 	color: $color-primary;
				// }

				&.hasTips {
					margin-bottom: 0;
				}
			}

			.tips {
				margin-bottom: 23px;

				@media screen and (max-width: 767px) {
					margin-bottom: 8.5px;
				}
			}

			.kk {
				.part {
					padding: 40px 30px 30px 30px;
					background: #ffffff;
					// border-radius: 10px;
					position: relative;
					z-index: 0;

					@media screen and (max-width: 767px) {
						padding: 20px 12px 10px;
						border-bottom: 10px solid #f3f4f5;
					}

					.boxContent {
						display: grid;
						justify-content: space-between;
						grid-template-columns: repeat(1, 1fr);
						grid-gap: 22px;
						gap: 22px;

						@media screen and (max-width: 768px) {
							display: block;
						}
					}

					&.Products-Categories {
						.boxContent {
							grid-template-columns: repeat(4, 1fr);
							display: grid;

							.StepBox {
								min-width: 100px;
								// height: 323px;
								padding-bottom: 0;
								// transition: all 0.2s;

								::v-deep .se {
									height: 100%;
									position: relative;

									.swiper {
										padding: 0;

										.el-image {
											display: flex;
											justify-content: center;
											align-items: center;
											height: 100%;
											width: 100%;

											.video-js {
												border-radius: 10px 10px 0 0;
											}
										}
									}

									.product-info {
										width: 100%;
										margin-top: 0;
										padding: 10px;
										border-radius: 0 0 10px 10px;
									}
								}
							}

							@media screen and (max-width: 1919px) {
								grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
							}

							@media screen and (max-width: 767px) {
								grid-template-columns: repeat(2, 1fr);
								column-gap: 8px;
								row-gap: 0;

								::v-deep .el-image {
									// height: 135px !important;
									border-radius: 10px 10px 0 0;
								}

								::v-deep .product-info {
									width: 100%;
									margin-top: 0;
									padding: 10px 0;
									align-items: center;
									flex-direction: column;
								}
							}
						}
					}

					&.Lanyard-Size {
						.boxContent {
							grid-template-columns: repeat(2, 1fr);

							@media screen and (max-width: 1024px) {
								grid-template-columns: repeat(2, 1fr);
								column-gap: 8px;
							}

							@media screen and (max-width: 850px) {
								display: grid;
								grid-template-columns: 1fr;
								row-gap: 15px;
								margin-bottom: 20px;
							}
						}
					}

					&.Lanyard-Popular-Colors {
						padding-bottom: 20px;
						@media screen and (max-width: 767px) {
							padding-bottom: 0;
						}
						.boxContent {
							grid-template-columns: repeat(1, 1fr);

							@media screen and (max-width: 767px) {
								margin-top: 30px;
								.text-center{
									margin-block: 20px 10px;
								}

								::v-deep .el-tab-pane:nth-child(2) > ul {
									margin-top: 20px;
								}

								::v-deep .el-input__icon {
									display: none;
								}
							}

							.activity {
								padding: 7px 0 7px 20px;
								color: #858282;

								.activity-text {
									line-height: 30px;

									.activity-text-1 {
										display: flex;
										font-size: 16px;
										font-weight: bold;

										span {
											color: #ff0000;
											margin-left: 5px;
										}

										::v-deep .el-checkbox__inner {
											border-color: #c4c7cd;
										}
									}

									.activity-text-2 {
										margin-left: 20px;
										color: #858282;

										span {
											color: #ff0000;
										}

										@media screen and (max-width: 768px) {
											line-height: 16px;
										}
									}
								}

								.activity-list {
									margin-right: 20px;
									width: 184px;
									border: 1px solid #d9dbdd;
									margin-top: 10px;
									border-radius: 5px;
									display: grid;
									grid-template-columns: repeat(3, 1fr);
									grid-template-rows: repeat(2, 45px);

									.activity-img {
										grid-column: 1/2;
										grid-row: 1/3;
										padding: 8px;

										div {
											width: 100%;
											height: 100%;
											border: 1px solid #d9dbdd;
											background-size: cover;
										}
									}

									.myInput {
										text-align: left;
										-webkit-appearance: none;
										background-color: var(--el-input-bg-color, var(--el-color-white));
										border-radius: 5px !important;
										background-image: none;
										border: 1px solid #dcdfe6;
										box-sizing: border-box;
										color: var(--el-input-text-color, var(--el-text-color-regular));
										display: inline-block;
										font-size: 16px;
										height: 30px;
										line-height: 30px;
										margin-top: 4px;
										outline: 0;
										padding: 0 11px;
										transition: var(--el-transition-border);
										width: 100%;

										@media screen and (max-width: 768px) {
											height: 30px;
											line-height: 30px;
											font-size: 12px;
										}
									}

									.p2 {
										grid-column: 2/4;
										grid-row: 1/2;
										padding: 5px 12px 5px 5px;
										overflow: hidden;
										font-weight: 400;
										color: #666666;

										&.isTwoToneCustom {
											line-height: 1.1;
										}

										@media screen and (max-width: 768px) {
											font-size: 12px;
										}

										&:hover {
											overflow: visible;
										}
									}

									.p3 {
										grid-column: 2/4;
										grid-row: 2/3;
										display: flex;
										padding: 0 15px 0 5px;

										@media screen and (max-width: 768px) {
											padding: 0 5px;
										}
									}

									.xuanzhong {
										position: absolute;
										right: 0px;
										top: -1px;
										font-size: 11px;
										display: none;

										&.active2 {
											display: block;
											background-color: var(--color-primary);
											color: #fff;
											padding: 0 3px;
											border-radius: 0 4px 0 0;
											@media screen and (max-width: 768px) {
												padding: 1px 3px;
											}
										}
									}

									&.active {
										border-color: $color-primary;
									}
								}

								.custom-shadow2 {
									position: relative;
									background: #fff;

									&::before,
									&::after {
										content: "";
										position: absolute;
										z-index: -1;
										bottom: 14px;
										left: 0px;
										width: 50%;
										height: 20%;
										box-shadow: 0 14px 7px #d9dbdd;
										transform: rotate(-3deg);
									}

									&::after {
										right: 0;
										left: auto;
										transform: rotate(3deg);
									}
								}

								.custom-scrollbar {
									grid-template-columns: repeat(5, 1fr);
									display: grid;
									position: relative;
									z-index: 0;
								}

								@media screen and (max-width: 767px) {
									padding: 7px 0 7px 10px;
									margin-top: 10px;
									.activity-text {
										align-items: center;

										div {
											font-size: 3.3vw;
										}
									}
									.custom-scrollbar {
										grid-template-columns: repeat(2, 1fr);
									}

									.activity-list {
										margin-right: 0;
										width: 39.5333vw;
									}
								}
							}
						}
					}

					&.Select-Medal-Style {
						&.hideContent {
							margin-bottom: 0;

							.boxContent {
								display: none;
							}

							.step-title {
								margin-bottom: 0;
							}
						}

						.boxContent {
							grid-template-columns: repeat(4, 1fr);
							display: grid;

							.StepBox {
								min-width: 100px;
								// height: 323px;
								padding-bottom: 0;
								// transition: all 0.2s;

								::v-deep .titleCard {
									right: 7px !important;
									top: 7px !important;
									left: initial;
									border-radius: 0 0 0 10px !important;
									background-color: #fd4242 !important;
								}

								::v-deep .se {
									height: 100%;
									position: relative;

									.swiper {
										padding: 0;

										.el-image {
											display: flex;
											justify-content: center;
											align-items: center;
											height: 100%;
											width: 100%;

											.video-js {
												border-radius: 10px 10px 0 0;
											}
										}
									}

									.product-info {
										width: 100%;
										margin-top: 0;
										padding: 10px;
										border-radius: 0 0 10px 10px;
									}
								}
							}

							@media screen and (max-width: 1919px) {
								grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
							}

							@media screen and (max-width: 767px) {
								grid-template-columns: repeat(2, 1fr);
								column-gap: 10px;
								row-gap: 0;

								::v-deep .el-image {
									// height: 135px !important;
									border-radius: 10px 10px 0 0;
								}

								::v-deep .product-info {
									width: 100%;
									margin-top: 0;
									padding: 10px 0;
									align-items: center;
									flex-direction: column;
								}
							}
						}
					}

					&.Design-Your-Printing {
						.el-form {
							> div:nth-child(1) {
								@media screen and (max-width: 768px) {
									grid-column: 1/28;
								}
							}
						}

						::v-deep .swiper:hover {
							transform: scale(1.5);
							transition: all 0.3s;
							z-index: 2;
							background-color: #fff;

							@media screen and (max-width: 767px) {
								transform: scale(1);
							}
						}

						.boxContent {
							grid-template-columns: repeat(1, 1fr);
							justify-content: space-between;
							grid-gap: 22px;
							gap: 22px;
							margin-bottom: 20px;

							.StepBox {
								display: block;

								::v-deep .se {
									height: 100%;
									position: relative;

									.imgWrap {
										display: flex;
										justify-content: center;
										align-items: center;
										height: 100%;
										width: 100%;

										.video-js {
											border-radius: 10px 10px 0 0;
										}
									}

									// .product-info {
									//   width: 100%;
									//   margin-top: 0;
									//   padding: 10px;
									//   background-color: #f2f2f2;
									//   border-radius: 0 0 10px 10px;
									// }
								}
							}

							@media screen and (max-width: 767px) {
								grid-template-columns: repeat(2, 1fr);
								column-gap: 10px;
								row-gap: 10px;

								::v-deep .el-image {
									// height: 135px !important;
									border-radius: 10px 10px 0 0;
								}

								::v-deep .product-info {
									width: 100%;
									margin-top: 0;
									padding: 10px 0;
									align-items: center;
								}
							}
						}
					}

					&.Lanyard-Badge-Holder-Options {
						.boxContent {
							display: block;
							margin-bottom: 20px;
						}
					}

					&.Badge-Reels {
						// ::v-deep .swiper-wrapper{
						// 	justify-content: space-between;
						// }
						::v-deep .swiper-slide {
							width: 186px !important;
							margin-right: 10px !important;
						}

						.boxContent {
							display: block;
							margin-bottom: 20px;
						}

						@media screen and (max-width: 767px) {
							::v-deep .swiper-slide {
								width: 115px !important;
							}
						}
					}

					&.Select-Package {
						.boxContent {
							grid-template-columns: repeat(4, 1fr);

							::v-deep .StepBox {
								&.active {
									.el-image {
										border-color: $color-primary !important;
									}
								}

								.se {
									.el-image {
										border: 1px solid transparent;
										transition: all 0.2s;

										@media (any-hover: hover) {
											&:hover {
												border-color: $color-primary !important;
												box-shadow: 0 3px 4px 0 #ccc;
											}
										}
									}
								}
							}

							@media screen and (max-width: 767px) {
								grid-template-columns: repeat(2, 1fr);
								column-gap: 5px;
								row-gap: 0;

								::v-deep .el-image {
									// height: 139px !important;
								}

								::v-deep .product-info {
									width: 100%;
									margin-top: 0 !important;
									padding: 10px 0 6.5px 0;
									align-items: center;
								}

								::v-deep .product-price {
									margin-top: 0;
								}
							}
						}
					}

					&.Lanyard-Attachment {
						.boxContent {
							grid-template-columns: repeat(1, 1fr);
							column-gap: 20px;
							row-gap: 20px;
							margin-bottom: 20px;

							> div {
								overflow: hidden;
							}

							::v-deep .StepBox {
								padding-bottom: 0;
								overflow: hidden;

								.se .product-info .bTitle {
									white-space: normal;
								}

								&.active {
									.el-image {
										border-color: $color-primary !important;
									}
								}

								// .se {
								//   .el-image {
								//     border: 1px solid transparent;
								//     transition: all 0.2s;
								//     @media (any-hover: hover) {
								//       &:hover {
								//         border-color: $color-primary !important;
								//         box-shadow: 0 3px 4px 0 #ccc;
								//       }
								//     }
								//   }
								// }
								.product-price {
									margin-bottom: 5px;
								}
							}

							@media screen and (max-width: 767px) {
								::v-deep .StepBox {
									background: #f4f5f5;
									border-radius: 5px;
									display: flex;
									align-items: center;
									height: 100%;

									.se {
										.swiper {
											border: none;
										}

										.product-info {
											justify-content: flex-start;
											margin-top: 0;
											background-color: #f7f9fa;
										}
									}

									::v-deep .product-price {
										margin-bottom: 5px;
									}
								}
							}
						}
					}

					&.Select-Printing-Method {
						.boxContent {
							grid-template-columns: repeat(1, 1fr);
							column-gap: 20px;
							row-gap: 20px;
							margin-bottom: 20px;

							> div {
								overflow: hidden;
							}

							::v-deep .StepBox {
								padding-bottom: 0;
								overflow: hidden;

								.se .product-info .bTitle {
									white-space: normal;
								}

								&.active {
									.el-image {
										border-color: $color-primary !important;
									}
								}
								.product-price {
									margin-bottom: 5px;
								}
							}

							@media screen and (max-width: 767px) {
								::v-deep .StepBox {
									background: #f4f5f5;
									border-radius: 5px;
									display: flex;
									align-items: center;
									height: 100%;

									.se {
										.swiper {
											border: none;
										}

										.product-info {
											justify-content: flex-start;
											margin-top: 0;
											background-color: #f7f9fa;
										}
									}

									::v-deep .product-price {
										margin-bottom: 5px;
									}
								}
							}
						}
					}

					&.Lanyard-Attachment-lightUp {
						.boxContent {
							grid-template-columns: repeat(5, 1fr);
							column-gap: 20px;
							row-gap: 20px;
							margin-bottom: 20px;

							@include respond-to(mb) {
								display: grid;
								grid-template-columns: repeat(2, 1fr);
								gap: 10px;
							}

							> div {
								overflow: hidden;
							}

							::v-deep .StepBox {
								padding-bottom: 0;
								overflow: hidden;

								.swiper > div {
									width: 100%;
								}

								.se .product-info .bTitle {
									white-space: normal;
								}

								// &.active {
								// 	.el-image {
								// 		border-color: $color-primary !important;
								// 	}
								// }
								.product-price {
									margin-bottom: 5px;
								}
							}

							@media screen and (max-width: 767px) {
								::v-deep .StepBox {
									background: #f4f5f5;
									border-radius: 5px;
									display: flex;
									align-items: center;
									height: 100%;

									.se {
										.swiper {
											// border: none;
										}

										.product-info {
											justify-content: flex-start;
											margin-top: 5px;
											background-color: #f7f9fa;
										}
									}

									::v-deep .product-price {
										margin-bottom: 5px;
									}
								}
							}
						}
					}

					&.Lanyard-Stitch-Style {
						::v-deep .label {
							margin-bottom: 0 !important;
						}

						.boxContent {
							display: grid;
							grid-template-columns: repeat(5, 1fr);
							justify-content: space-between;
							grid-gap: 22px;
							gap: 22px;

							@media screen and (max-width: 1730px) {
								grid-template-columns: repeat(3, 1fr);
							}

							@media screen and (max-width: 1280px) and (min-width: 769px) {
								gap: 13px;
							}

							@media screen and (max-width: 768px) {
								grid-template-columns: repeat(3, 1fr);
								column-gap: 8px;
								row-gap: 10px;
								padding-bottom: 10px;
							}

							::v-deep .StepBox {
								&.active {
									.el-image {
										border-color: $color-primary !important;
									}
								}

								.item {
									border-radius: 6px;
									padding-bottom: 5px;
									display: block;

									@media screen and (max-width: 768px) {
										padding: 0;
									}

									.swiper {
										img {
											width: 100%;
											height: 186px;
											object-fit: contain;
										}
									}
								}

								.se {
									.swiper {
										img {
										}
									}
								}
							}

							@media screen and (max-width: 767px) {
								// grid-template-columns: repeat(2, 1fr);
								// column-gap: 5px;
								// row-gap: 7.5px;

								::v-deep .StepBox {
									padding: 0;

									.se .swiper img {
										height: 90px;
									}
								}
							}
						}
					}

					&.Lanyard-Glitter-Style {
						::v-deep .label {
							margin-bottom: 0 !important;
						}

						.boxContent {
							display: grid;
							grid-template-columns: repeat(2, 1fr);
							justify-content: space-between;
							grid-gap: 22px;
							gap: 22px;

							@media screen and (max-width: 1730px) {
								grid-template-columns: repeat(2, 1fr);
							}

							@media screen and (max-width: 1280px) and (min-width: 769px) {
								gap: 13px;
							}

							@media screen and (max-width: 768px) {
								grid-template-columns: repeat(2, 1fr);
								column-gap: 5px;
								row-gap: 10px;
								padding-bottom: 10px;
							}

							::v-deep .StepBox {
								&.active {
									.el-image {
										border-color: $color-primary !important;
									}
								}

								.item {
									border-radius: 6px;
									padding-bottom: 5px;
									display: block;

									@media screen and (max-width: 768px) {
										padding-bottom: 5px;
										border-color: #e6e6e6;
										.swiper {
											border: none;
										}
									}

									.swiper {
										img {
											width: 100%;
											height: 186px;
											object-fit: contain;
										}
									}
								}

								.se {
									.swiper {
										img {
										}
									}
								}
							}

							@media screen and (max-width: 767px) {
								// grid-template-columns: repeat(2, 1fr);
								// column-gap: 5px;
								// row-gap: 7.5px;

								::v-deep .StepBox {
									padding: 0;

									.se .swiper img {
										height: 90px;
									}
								}
							}
						}
					}

					&.Lanyard-Glitter-Printing-Style {
						::v-deep .label {
							margin-bottom: 0 !important;
						}

						.boxContent {
							display: grid;
							grid-template-columns: repeat(4, 1fr);
							justify-content: space-between;
							grid-gap: 22px;
							gap: 22px;

							@media screen and (max-width: 1730px) {
								grid-template-columns: repeat(4, 1fr);
							}

							@media screen and (max-width: 1280px) and (min-width: 769px) {
								gap: 13px;
							}

							@media screen and (max-width: 768px) {
								grid-template-columns: repeat(2, 1fr);
								column-gap: 5px;
								row-gap: 10px;
								padding-bottom: 10px;
							}

							::v-deep .StepBox {
								&.active {
									.el-image {
										border-color: $color-primary !important;
									}
								}

								.item {
									border-radius: 6px;
									padding-bottom: 5px;
									display: block;

									@media screen and (max-width: 768px) {
										padding-bottom: 5px;
									}

									.swiper {
										img {
											width: 100%;
											height: 186px;
											object-fit: contain;
										}
									}
								}

								.se {
									.swiper {
										img {
										}
									}
								}
							}

							@media screen and (max-width: 767px) {
								// grid-template-columns: repeat(2, 1fr);
								// column-gap: 5px;
								// row-gap: 7.5px;

								::v-deep .StepBox {
									padding: 0;

									.se .swiper img {
										height: 90px;
									}
								}
							}
						}
					}

					&.Additional-Options {
						.prompt {
							color: #666;
							font-size: 16px;
							font-weight: 400;
							margin-bottom: 20px;
							text-align: left;
						}

						@media screen and (max-width: 767px) {
							padding-bottom: 15px;
							.prompt {
								color: #666;
								font-size: 12px;
							}
						}

						.boxContent {
							grid-template-columns: repeat(3, 1fr);
							display: grid;

							::v-deep .StepBox {
								padding: 0;

								&.active {
									.el-image {
										border-color: $color-primary !important;
									}
								}

								.item {

									@media screen and (max-width: 767px)  {
										padding: 0;
										border-radius: 0;
									}

									.swiper {
										img {
											height: 163px;
											object-fit: contain;
										}

										@media screen and (max-width: 767px) {
											&::before,
											&::after {
												content: "";
												position: absolute;
												z-index: -1;
												bottom: 12px;
												left: 5px;
												width: 50%;
												height: 20%;
												box-shadow: 0 14px 7px #d9dbdd;
												transform: rotate(-3deg);
											}

											&::after {
												right: 5px;
												left: auto;
												transform: rotate(3deg);
											}
										}
									}

									.bTitle {
										white-space: normal;
									}
								}
							}

							@media screen and (max-width: 767px) {
								grid-template-columns: repeat(2, 1fr);
								column-gap: 8px;
								row-gap: 7.5px;

								::v-deep .StepBox {
									.se .swiper img {
										height: 112px;
									}
								}
							}
						}
					}

					&.Select-Packaging {
						@media screen and (max-width: 767px) {
							padding-bottom: 15px;
						}

						.boxContent {
							grid-template-columns: repeat(3, 1fr);
							// column-gap: 30px;
							display: grid;

							::v-deep .StepBox {
								padding: 0;

								&.active {
									.el-image {
										border-color: $color-primary !important;
									}
								}

								.item {

									.swiper {
										img {
											height: 163px;
											object-fit: contain;
										}

										@media screen and (max-width: 767px) {
											&::before,
											&::after {
												content: "";
												position: absolute;
												z-index: -1;
												bottom: 12px;
												left: 5px;
												width: 50%;
												height: 20%;
												box-shadow: 0 14px 7px #d9dbdd;
												transform: rotate(-3deg);
											}

											&::after {
												right: 5px;
												left: auto;
												transform: rotate(3deg);
											}
										}
									}
								}
							}

							@media screen and (max-width: 767px) {
								grid-template-columns: repeat(2, 1fr);
								column-gap: 8px;
								row-gap: 7.5px;

								::v-deep .StepBox {
									.se .swiper img {
										height: 90px;
									}
									.item{
										padding: 0;
										border-radius: 0;
									}
								}
							}
						}
					}

					&.Select-Silicone-Printing-Colors {
						.boxContent {
							grid-template-columns: repeat(4, 1fr);
							display: grid;
							margin-bottom: 20px;

							::v-deep .StepBox {
								padding: 0;

								&.active {
									.el-image {
										border-color: $color-primary !important;
									}
								}

								.item {
									padding: 18px;

									.swiper {
										img {
											height: 163px;
											object-fit: contain;
										}

										@media screen and (max-width: 767px) {
											&::before,
											&::after {
												content: "";
												position: absolute;
												z-index: -1;
												bottom: 12px;
												left: 5px;
												width: 50%;
												height: 20%;
												box-shadow: 0 14px 7px #d9dbdd;
												transform: rotate(-3deg);
											}

											&::after {
												right: 5px;
												left: auto;
												transform: rotate(3deg);
											}
										}
									}

									.bTitle {
										white-space: normal;
									}
								}
							}

							@media screen and (max-width: 767px) {
								grid-template-columns: repeat(2, 1fr);
								column-gap: 5px;
								row-gap: 7.5px;

								::v-deep .StepBox {
									.se .swiper img {
										height: 112px;
									}
								}
							}
						}
					}

					&.Select-Your-Card {
						.boxContent {
							display: grid;
							grid-template-columns: repeat(2, 1fr);
							grid-auto-rows: auto;
							grid-column-gap: 24px;
							column-gap: 20px;
							margin-bottom: 20px;

							::v-deep .StepBox {
								&.active {
									.el-image {
										border-color: $color-primary !important;
									}
								}

								.item {
									padding: 18px;
									margin-bottom: 0;

									@media screen and (max-width: 768px) {
										padding: 0;
									}
								}

								.se {
									.swiper {
										img {
										}
									}
								}
							}

							@media screen and (max-width: 768px) {
								grid-template-columns: repeat(1, 1fr);
								column-gap: 5px;
								row-gap: 10px;

								::v-deep .StepBox {
									padding: 0;

									.radio-beauty {
										height: 14px;
										min-width: 14px;
										width: 14px;
									}

									.product-info {
										margin-bottom: 0;
									}

									.rightInfo {
										padding: 7px;
									}

									.se .swiper img {
										height: 250px !important;
										object-fit: contain;
									}

									.el-form-item__label {
										font-size: 12px;
										padding: 0;
									}

									.el-input__inner {
										height: 30px;
										font-size: 12px;
									}
								}
							}
						}
					}

					&.Delivery-Date {
						border-bottom: 0;

						::v-deep .dateBox .item-wrap {
							@media screen and (max-width: 767px) {
								padding: 15px !important;

								.bottom {
									// margin: 8px 0 0 27px;
								}

								.top span {
									font-size: 14px;
								}
							}
						}

						@media screen and (max-width: 767px) {
							::v-deep .dateBox .el-popover__reference-wrapper .el-popover__reference .active {
								// background: $color-primary;
								border: 1px solid $color-primary !important;
								// color: #ffffff;

								.label {
									color: #ffffff !important;
								}

								.info {
									color: #ffffff !important;
								}

								.circle2 {
									border-color: #fff;
									background-color: $color-primary;
								}
							}
						}

						::v-deep .dateBox .item-wrap.discount-item0,
						::v-deep .dateBox .item-wrap.discount-item1,
						::v-deep .dateBox .item-wrap.discount-item2,
						::v-deep .dateBox .item-wrap.discount-item3 {
							border: 1px solid #e9ecf0;

							&:hover {
								border-color: $color-primary;
							}
						}

						.step-text {
							font-size: 16px;
							font-weight: 400;
							color: #666666;
							text-align: left;
							margin-bottom: 20px;

							@media screen and (max-width: 767px) {
								font-size: 12px;
								color: #999999;
							}
						}
					}

					&.step-active {
						z-index: 100;
					}
				}

				&:not(:last-child) {
					.part {
						margin-bottom: 20px;

						@media screen and (max-width: 767px) {
							margin-bottom: 0px;
						}
					}
				}
			}

			.kk.type1 {
				.part {
					position: static;
				}
			}
		}

		.rightArea {
			position: relative;
			grid-column: 30/49;

			@media screen and (max-width: 1500px) {
				grid-column: auto;
				display: none;

				.preview {
					display: flex;
				}

				.content {
					.leftArea {
						grid-column: 2/48;
					}

					.rightArea {
						display: none;
					}
				}
			}

			.stickyType {
				position: sticky;
				background-color: #ffffff;
				// padding: 20px;
				// margin: 0 30px;
				border-radius: 10px;
				bottom: auto;
				top: 10px;
				font-size: 14px;
				margin-top: 7px;
				// box-shadow: -8px 0px 10px -10px rgb(0 0 0 / 25%);

				&.step-active {
					z-index: 100;
				}
			}
		}
	}

	.mFooter {
		display: grid;
		grid-template-columns: 850px;
		justify-content: center;
		padding: 20px;
		background: #eef2f5;

		@media screen and (max-width: 767px) {
			grid-template-columns: 1fr;
			padding: 15px;
		}

		.medalsDetails {
			::v-deep .btnGroup {
				display: flex;
				flex-wrap: nowrap;
				flex-direction: initial;

				.btn:first-child {
					margin-bottom: 0;
				}
			}
		}
	}

	.shadowMask {
		position: relative;

		&::after {
			content: "";
			position: absolute;
			inset: 0;
			background-color: rgba(0, 0, 0, 0.3);
			border-radius: 6px;
		}
	}

	.mask {
		position: fixed;
		left: 0;
		top: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.5);
		display: none;
		z-index: 10;
	}

	.mask.checking {
		display: block;
	}

	::v-deep .commentDialog {
		width: 100%;
		border: 1px solid #ccc;
		// padding: 10px;
		border-radius: 4px;
		box-shadow: 0 2px 4px rgb(0 0 0 / 12%), 0 0 6px rgb(0 0 0 / 4%);
		text-align: center;

		.el-dialog__header {
			display: none;
		}

		.el-dialog__body {
			padding: 0;
		}

		.isComment {
			// width: 100%;
			height: 100%;
			// border: 1px solid #ccc;
			padding: 10px;
			border-radius: 4px;
			// box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
			text-align: center;

			.textWrap {
				display: flex;
				align-items: center;
				justify-content: flex-start;
				margin-top: 0 !important;
				font-size: 18px;
			}

			.circle2 {
				border-color: $color-primary;
				background-color: $color-primary;
				flex-shrink: 0;
				width: 18px;
				height: 18px;
				border-radius: 50%;
				margin-right: 10px;
				display: flex;
				justify-content: center;
				align-items: center;

				&::after {
					background-color: #ffffff;
					content: "";
					width: 6px;
					height: 6px;
					border-radius: 50%;
				}
			}

			.el-textarea {
				flex: 1;
				margin: 10px 0;

				textarea {
					height: 100%;
					background-color: #f5f5f5 !important;
				}
			}

			button {
				background-color: $color-primary;
				color: #fff;
				padding: 4px;
				width: 100px;
				outline: none;
				border: none;
				border-radius: 4px;
				font-size: 14px;
				text-align: center;
			}
		}
	}

	.el-icon-close {
		display: flex;
		justify-content: center;
		align-items: center;
		font-weight: 700;
		position: absolute;
		top: 0;
		right: 0;
		transform: translate(50%, -50%);
		width: 40px;
		height: 40px;
		cursor: pointer;
		background: #ffffff;
		box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.3);
		border-radius: 50%;
		z-index: 10;

		@media screen and (max-width: 800px) {
			transform: translate(0, 0);
			box-shadow: none;
		}
	}

	.preview {
		display: none;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		position: fixed;
		right: 0;
		top: 30%;
		padding: 5px 10px;
		transform: translateY(-50%);
		opacity: 0.9;
		z-index: 1000;
		border-radius: 10px;
		background-color: #fff0e9;
		cursor: pointer;
		-webkit-user-select: none;
		-moz-user-select: none;
		user-select: none;

		@media screen and (max-width: 1500px) {
			display: flex;
		}

		b {
			font-size: 30px;
			color: #c95c28;
		}

		span {
			font-size: 12px;
			color: #c95c28;
			font-weight: 700;
		}
	}
}
</style>
<style lang="scss">
.drawDialog {
	.rightArea.rightFixedArea {
		position: relative;
		@include respond-to(mb) {
			padding-bottom: 50px;
		}
		.fold-icon {
			position: absolute;
			left: 0;
			top: 50%;
			transform: translateY(-50%);
			height: 100px;
			width: 9px;
			background: #dedbdb;
			display: flex;
			align-items: center;
			justify-content: center;
			z-index: 1;
			cursor: pointer;

			.arrow {
				transform: rotate(180deg);
				font-size: 10px;
				color: #fff;
			}
		}
	}
}

.allImgDialog {
	.el-dialog__header {
		.el-dialog__title,.el-dialog__headerbtn{
			font-weight: bold;
			font-size: 20px;
		}
	}
	.el-dialog__body {
		padding-top: 0;
		.allImgDialogBox{
			.item{
				@media screen and(max-width: 768px) {
					height: 100px;
				}
			}
		}
	}
}

.closeBtn {
	position: relative;

	.btn-close {
		font-size: 12px;
		position: absolute;
		right: -15px;
		top: -15px;
		left: auto;
		border-radius: 50%;
		box-shadow: 0 0.5rem 1rem rgb(0 0 0 / 15%);
		width: 36px;
		height: 36px;
		background: transparent url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23000'%3e%3cpath d='M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z'/%3e%3c/svg%3e") (center / 1em) auto no-repeat;
		background-color: #fff;
		background-size: 0.7em;
		opacity: 1;
		z-index: 2;

		@media screen and (max-width: 768px) {
			right: -5px;
		}
	}
}
</style>