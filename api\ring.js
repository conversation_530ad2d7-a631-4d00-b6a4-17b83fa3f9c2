import { request } from "@/utils/request";

//获取分类绑定的参数
export function getAppRingIconList(data) {
	return request({
		url: '/quote/ring/icons/getAppRingIconList',
		method: 'post',
		data: data,
	})
}

//获取分类绑定的参数
export function findAll(data) {
	return request({
		url: '/quote/colors/findAll',
		method: 'post',
		data: data,
	})
}

//获取分类绑定的参数
export function getAppRingIconTagList(data) {
	return request({
		url: '/quote/ring/icons/getAppRingIconTagList',
		method: 'get',
		params: data,
	})
}

//获取分类绑定的参数
export function getAppRingCateList(data) {
	return request({
		url: '/quote/ring/templates/getAppRingCateList',
		method: 'get',
		params: data,
	})
}

//获取分类绑定的参数
export function getAppRingTagList(data) {
	return request({
		url: '/quote/ring/templates/getAppRingTagList',
		method: 'get',
		params: data,
	})
}

//获取分类绑定的参数
export function getAppRingTemplatesList(data) {
	return request({
		url: '/quote/ring/templates/getAppRingTemplatesList',
		method: 'post',
		data: data,
	})
}
