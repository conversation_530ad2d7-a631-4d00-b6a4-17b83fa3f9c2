<template>
	<div class="summary-box" :class="modal.wrapClass" :style="modal.style">
		<EditDiv class="bps-container summary_h2" tag-name="h2" v-model:content="modal.outer[0].title.value" @click="setModalType(modal.outer[0].title,modal,'text')" />
		<div class="pc-show mb-none">
			<div class="bps-container" v-for="(o, oi) in modal.outer" >
				<div class="content">
					<EditDiv class="des" v-model:content="modal.outer[0].subTitle.value" @click="setModalType(o.subTitle,modal.outer,'text')" />
					<div class="btnWrap" @click="setModalType(modal.button,modal.outer,'button')"  v-if="o.button">
						<a :href="o.button.url" :title="o.button.alt" :target="o.button.target || '_self'"
						   class="default-button bps-button">
							{{ o.button.value }}
						</a>
					</div>
				</div>
			</div>
			<div v-for="(o, oi) in modal.outer"  class="sticker_two">
				<pic class="pic" :src="o.background.value" v-if="o.background"  @click="setModalType(o.background,modal.outer,'img')"/>
				<div class="bps-container">
					<div v-for="(item,index) in o.list">
						<pic :alt="item.img.alt" :src="item.img.value"  :title="item.img.alt"  @click="setModalType(o.list[index].img,modal.outer,'img')"/>
						<EditDiv class="des" v-model:content="item.title.value" @click="setModalType(o.list[index].title,modal.outer,'text')" />
					</div>
				</div>
			</div>
		</div>
		<div class="pc-none mb-show">
			<pic class="pic" :src="modal.outer[0].background.value" v-if="modal.wrapClass == 'productStyle2 newSticker'"  @click="setModalType(modal.outer[0].background,modal.outer,'img')"/>
			<div class="bps-container" v-for="(o, oi) in modal.outer" >
				<EditDiv class="des" v-model:content="modal.outer[0].subTitle.value" @click="setModalType(o.subTitle,modal.outer,'text')" />
				<div class="containerFlex">
					<div v-for="(item,index) in o.list">
						<pic :src="item.img.value" :title="item.img.alt" :alt="item.img.alt" @click="setModalType(o.list[index].img,modal.outer,'img')"/>
						<EditDiv class="des" v-model:content="item.title.value" @click="setModalType(o.list[index].title,modal.outer,'text')" />
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	props: {
		data: {
			type: Object,
			default: {},
		}
	},
	data() {
		return {
			modal: {
				style: {},
				type: {index: 0, clickPosition: 'outer'},
				...this.data
			}
		};
	},
	watch: {
		modal: {
			handler(val) {
				if (process.env.isManage) this.$emit("update:data", val);
			},
			immediate: true,
			deep: true
		}
	},
	methods:{
		setModalType(target, targetArray, clickType, event, other) {
			this.$setModal(this, target, targetArray, clickType, event, other)
		},
	}
};
</script>

<style lang="scss" scoped>
.newSticker {
	.pc-show {
		.sticker_two {
			.bps-container {
				div {
					img {
						height: auto;
					}
					div {
						background-color: #fff;
						width: 100%;
						padding: 15px 0;
					}
					&:nth-child(1),&:nth-child(2) {
						img {
							height: 255px !important;
						}
					}
					.des {
						bottom: -37px;
						border-radius: 0 0 15px 15px;
					}
				}
			}
		}
	}
}
.summary-box {
	.summary_h2 {
		font-size: 42px;
	}
	.pc-show {
		display: block;
	}
	.pc-none {
		display: none;
	}
	padding: 70px 0;
	.sticker_two {
		margin-top: 41px;
		position: relative;
		padding: 46px 0 61px 0;
		.pic {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: auto;
			-o-object-fit: cover;
			object-fit: cover;
		}
		.bps-container {
			display: block;
			div {
				display: inline-block;
				position: relative;
				.des {
					position: absolute;
					width: 100%;
					text-align: center;
					left: 0;
					bottom: 19px;
					margin: 0 !important;
				}
				&:nth-child(2) {
					margin: 0 24px 0 22px;
				}
				img {
					height: 337px;
					border-radius:20px ;
					width: 360px;
				}
				&:nth-child(3) {
					position: absolute;
					right: 16px;
					bottom: 0;
					img {
						height: 653px;
						width: 633px;
					}
				}
			}
		}
	}
	.bps-container {
		display: grid;
		align-items: center;
		position: relative;
		grid-template-columns: repeat(2, 1fr);
		grid-gap: 20px;

		.btnWrap {
			display: inline-block;
			color: #ffffff;
		}

		&>.pic {
			border-radius: 10px;
		}

		.content {
			.contentGrid {
				display: grid;
				grid-template-columns: repeat(2, 1fr);
				grid-column-gap: 46px;
				div {
					img {
						width: auto;
						vertical-align: middle;
					}
					div {
						vertical-align: middle;
						display: inline-block;
						width: 60%;
						margin-left: 19px;
					}
				}
			}
			h1,
			h2 {
				font-size: 36px;
				text-align: left;
			}
			&>.pic{
				display: none;
			}

			.des {
				margin: 30px 0;
				line-height: 24px;
				color: #666666;
			}
			.conter {
				display: grid;
				grid-template-columns: repeat(2, 1fr);
				grid-column-gap: 30px;
				grid-row-gap: 10px;
				font-size: 14px;
				font-weight: 400;
				color: #333;
				.item {
					span {
						display: inline-block;
						width: 8px;
						height: 8px;
						background: #939393;
						border-radius: 50%;
						margin-right: 20px;
					}
				}
			}
		}
	}
}

.home-part3 {
	background-color: #F8F8F8;

	.bps-container {
		position: relative;
		grid-template-columns: 1fr 1.2fr;
		grid-gap: 70px;

		.content {
			grid-row: 1/2;
		}
	}
}

.about-part {
	padding: .7813vw 0 2.6042vw;
	background-color: #FFF;

	.bps-container {
		max-width: 74.4792vw;
		position: relative;
		grid-template-columns: 1fr 1.7fr;
		grid-gap: 1.5vw;
		.content {
			grid-row: 1/2;
			margin-left: 0;
		}
	}
	.bps-button{
		margin-top: 0;
	}

	.content{
		margin-left: 1.3021vw;
	}
}

@media screen and (max-width: $mb-width) {
	.newSticker {
		.mb-show {
			margin-bottom: 80px;
			position: relative;
			.bps-container {
				.containerFlex {
					div {
						padding-bottom: 35px;
						position: relative;
						z-index: 9;
						background-color: #fff;
						overflow: hidden;
						.des {
							bottom: -25px;
						}
						&:nth-child(3) {
							.des {
								bottom: -31px !important;
							}
						}
					}
				}
			}
			.pic {
				position: absolute;
				bottom: -50px;
				height: 65%;
			}
		}
	}
	.summary-box {
		.summary_h2 {
			font-size: 21px;
			text-align: center;
		}
		.mb-none {
			display: none;
		}
		.mb-show {
			display: block;
		}
		padding: 27px 0;
		background-color: #ffffff;
		.bps-container {
			display: block;
			h2,.btnWrap {
				display: inline-block;
				vertical-align: middle
			}
			h2 {
				width: 65%;
				font-size: 16px;
			}
			.btnWrap {
				a {
					border-radius: 25px;
				}
			}
			.des {
				margin-top: 16px;
				font-size: 12px;
				font-family: Arial;
				font-weight: 400;
				color: #666666;
			}
			.containerFlex {
				margin-top: 22px;
				display: grid;
				grid-template-areas:
					'b a'
					'c a';
				grid-column-gap: 11px;
				>div {
					border-radius: 10px;
					border: 1px solid #ccc;
					padding-bottom: 15px;
				}
				div {
					position: relative;
					img {
						height: 100%;
					}
					&:nth-child(3) {
						grid-area: a;
						.des {
							bottom: 8px !important;
						}
					}
					&:nth-child(2) {
						margin-top: 10px;
					}
					.des {
						position: absolute;
						text-align: center;
						width: 80%;
						left: 50%;
						transform: translateX(-50%);
						bottom: 15px;
						font-size: 12px;
						font-family: Arial;
						font-weight: 400;
						color: #333333;
					}
				}
			}
		}
	}
}
</style>
