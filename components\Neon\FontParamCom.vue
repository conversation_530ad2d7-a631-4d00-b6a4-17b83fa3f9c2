<!-- 霓虹灯小尺寸 Design Your Neon Signs -->
<template>
  <div class='font-param-small'>
    <div class="sub-step-title step1">
      <span class="stepIndex">{{ lang.Step + ' 1-1'}}</span>
      <span class="stepName">{{ lang.neon.selectSizeStep }}</span>
      <p class="tips">{{ lang.neon.askBetterSize }}</p>
    </div>

    <!-- Tab -->
    <div class="line-group">
      <div class="line-item"
        v-for="item in lineData"
        :key="item.id"
        :class="activeLine.id == item.id ? 'active':''"
        @click="changeLine(item)">
        <div class="com-sign">
          <b
            :class="item.paramCode == 1 ? 'icon-a-icon-oneline': item.paramCode == 2 ? 'icon-a-icon-twoline':'icon-a-icon-threeline'"></b>
        </div>
        <span>{{ item.alias }}</span>
      </div>
    </div>

    <!-- Size -->
    <div class="size-group"
      v-if="activeLine">
      <div class="size-item"
        v-for="(item,index) in activeLine.priceInfo.neonMiniPrice"
        @click="selectSize(item)"
        :class="{'active': item.paramValue == activeLine.selectedSize?.paramValue }"
        :key="index">
        <div class="item-inner">
          <div class="price">
            +<CCY-rate :decimal="2"
              :price="item.price ? Number((item.price * morePriceData.multiBasicUnitPrice).toFixed(2)) * (1-neonDiscount?.unitPercent):0"></CCY-rate>
          </div>
          <div class="raw-price">
            +<CCY-rate :decimal="2"
              :price="item.price * morePriceData.multiBasicUnitPrice">
            </CCY-rate>
          </div>
          <div class="detail">
            {{ formatInch(item.paramValue) + ' / ' + lang.neon.max + ' ' + item.limitPerLine + ' ' +  lang.neon.lettersPerLine }}
          </div>
        </div>

        <Corner :active="item.paramValue == activeLine.selectedSize?.paramValue"></Corner>
      </div>
    </div>

  </div>
</template>

<script>
import Corner from "@/components/Neon/Corner";
import quotePrepare from "@/mixins/quotePrepare";
export default {
  //import引入的组件需要注入到对象中才能使用
  mixins: [quotePrepare],
  props: {
    lineData: {
      type: Array,
      default: () => [],
    },
    neonDiscount: {
      type: Object,
      default: () => {},
    },
    sizeData: {
      type: Array,
      default: () => [],
    },
    activeLine: {
      type: [Object, String],
      default: () => {},
    },
  },
  components: { Corner },
  data() {
    //这里存放数据
    return {
      spelling: false,
    };
  },
  //监听属性 类似于data概念
  computed: {
    lang() {
      return this.$store.getters.lang.quote || {};
    },
    morePriceData() {
      return (
        this.$store.getters.morePriceData || {
          multiAdditionalItemPrice: 1,
          multiBasicGrindingPrice: 1,
          multiEntiretyPrice: 1,
          multiBasicUnitPrice: 1,
          plusAdditionalitemPrice: 0,
          plusBasicGrindingPrice: 0,
          plusBasicUnitPrice: 0,
          plusEntiretyPrice: 0,
        }
      ); //价格分层
    },
  },
  //监控data中的数据变化
  watch: {},
  //方法集合
  methods: {
    changeLine(val) {
      this.$emit("update:activeLine", val);
    },
    selectSize(val) {
      this.$emit("selectSize", val);
    },
	formatInch(val) {
		if(this.unitSign === 'cm'){
			return (Number(val) / 0.3937 * this.unitConvertRatio).toFixed(0) + 'cm'
		}else{
			return val + '"'
		}
	}
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created() {},
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
  beforeCreate() {}, //生命周期 - 创建之前
  beforeMount() {}, //生命周期 - 挂载之前
  beforeUpdate() {}, //生命周期 - 更新之前
  updated() {}, //生命周期 - 更新之后
  beforeDestroy() {}, //生命周期 - 销毁之前
  destroyed() {}, //生命周期 - 销毁完成
  activated() {}, //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang='scss' scoped>
.font-param-small {
  .sub-step-title {
    font-size: 14px;
    .stepIndex {
      color: #911efc;
    }
    &.step2 {
      margin-top: 20px;
    }
  }
  .line-group {
    display: flex;
    margin-top: 10px;
    position: relative;
    z-index: 2;
    .line-item {
      padding: 20px;
      border: 1px solid #dbdbdb;
      background-color: #f2f2f2;
      cursor: pointer;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      .com-sign {
        .word {
          font-weight: bolder;
        }
        .line-group {
          display: flex;
          flex-direction: column;
        }
      }
      &:not(:first-child) {
        margin-left: 20px;
      }
      &.active {
        color: #911efc;
        border-bottom-color: #fff;
        background-color: #fff;
      }
    }
  }
  .size-group {
    padding: 20px 17px;
    border: 1px solid #dbdbdb;
    position: relative;
    z-index: 1;
    transform: translateY(-1px);
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    .size-item {
      display: flex;
      justify-content: center;
      align-items: center;
      padding-block: 11px;
      border: 1px solid #dbdbdb;
      border-radius: 10px;
      position: relative;
      cursor: pointer;
      .item-inner {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        .price {
          font-size: 18px;
          font-weight: bolder;
          text-align: right;
          @media screen and(max-width: 768px) {
            font-size: 16px;
          }
        }
        .raw-price {
          font-size: 14px;
          color: #666666;
          text-align: left;
          line-height: 1.8;
          text-decoration: line-through;
          @media screen and(max-width: 768px) {
            font-size: 14px;
          }
        }
        .detail {
          grid-column-end: span 2;
          text-align: center;
        }
      }

      &.active {
        border-color: #911efc;
        .corner {
          opacity: 1;
        }
      }
    }
  }
}

.popo-row[x-placement^="bottom"] .popper__arrow {
  border-bottom-color: var(--neon-light-color) !important;
}

.popo-row[x-placement^="top"] .popper__arrow {
  border-top-color: var(--neon-light-color) !important;
}

.popo-row {
  border: 1.5px solid var(--neon-light-color) !important;
}
</style>

<style lang="scss">
.input-group {
  .iconInput {
    margin-top: 15px;
    position: relative;
    b {
      display: block;
      top: 8px;
      font-size: 10px;
      left: 17px;
      position: absolute;
      color: #999999;
      z-index: 10;
    }
  }

  .nTextArea {
    .el-textarea__inner {
      border-radius: 6px;
      &::placeholder {
        text-indent: 1.3em;
      }
    }
  }
}
</style>