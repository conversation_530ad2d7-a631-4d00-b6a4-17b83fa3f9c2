<template>
  <div class="uploadDrawer">
    <div class="top-content">
      <div class="uploadDrawerHeader">
        <span>{{ langSemi.chooseLogo }}</span>
        <div class="borderBox"></div>
      </div>
      <!-- 标签列表 -->
      <div>
        <div class="tag-div">
          <div
            v-for="(item, index) in medalsIconTagList"
            :key="index"
            @click="tagNameClick(item)"
            :class="showMedalsId === item.id ? 'tt' : 'div'"
          >
            {{ item.tagName }}
          </div>
        </div>
        <div class="items-cont" v-if="false">
          <div class="mySwiperDrawer swiper" ref="mySwiperDrawer">
            <div class="swiper-wrapper">
              <div
                class="swiper-slide pointer"
                v-for="(item, index) in medalsIconTagList"
                :class="{ active: showMedalsId === item.id }"
                @click="tagNameClick(item)"
                :key="index"
              >
                <p class="item-content">{{ item.tagName }}</p>
              </div>
            </div>
            <div class="swiper-pagination swiper-pagination-drawer"></div>
          </div>
        </div>
      </div>
      <!-- 图标列表 -->
      <div class="iconListBox custom-scrollbar">
        <div class="iconBox">
          <loader :loadState="!canvas.loadImgState"></loader>
          <div class="iconItem uploadBox">
            <div class="imgBox" v-if="uploadList.length > 0 && !moreUpload">
              <pic :src="uploadList[uploadList.length - 1]?.secure_url"></pic>
            </div>
            <div class="uploadText" @click="$refs.uploadDrawer.click()">
              {{ lang.Upload }}
              <span v-show="!moreUpload">& {{ langDesign.replace }}</span>
            </div>
            <input
              type="file"
              ref="uploadDrawer"
              :accept="acceptFileType"
              :multiple="moreUpload"
              @change="uploadPic"
            />
          </div>
          <div
            v-show="moreUpload"
            class="iconItem uploadBox"
            :class="{ active: isActive(item.id), moreUpload: moreUpload }"
            v-for="item in showMoreUploadList"
            :key="item.id"
          >
            <div class="imgBox">
              <pic :src="item.iconUrl"></pic>
              <b
                class="icon-shanchu2 shanchuIcon"
                @click.stop="$emit('delImg2', item.id)"
              ></b>
              <half-design-check-icon
                class="absolute-top-right2 check-icon"
              ></half-design-check-icon>
            </div>
          </div>
          <div
            class="iconItem"
            :class="{ active: isActive(item.id), moreUpload: moreUpload }"
            v-for="item in showMedalsIconList"
            :key="item.id"
            @click="debounceClickImg(item)"
          >
            <b
              class="icon-shanchu2 shanchuIcon"
              @click.stop="$emit('delImg2', item.id)"
            ></b>
            <half-design-check-icon
              class="absolute-top-right2 check-icon"
            ></half-design-check-icon>
            <img :src="item.iconUrl" style="aspect-ratio: 1/1" />
          </div>
        </div>
      </div>
    </div>
    <div class="continue" @click="toNext">
      <nextStep
        :style="{ opacity: uploadList.length > 0 ? 1 : 0.5 }"
        :disabled="!(uploadList.length > 0)"
        >{{ lang.continue }}</nextStep
      >
    </div>
    <BaseDialog
      v-model="showSelectAreaDialog"
      class="selectAreaDialog"
      :persistent="true"
      :width="!isMobile ? '50%' : '90%'"
      :beforeClose="cancel"
    >
      <areaSelectDialog
        @selectArea="selectArea"
        :areaIndex.sync="areaIndex"
      ></areaSelectDialog>
    </BaseDialog>
  </div>
</template>

<script>
import { generateUUID, debounce } from "@/utils/utils";
import { uploadFile } from "@/utils/oss";
import ToolTip from "@/components/HalfDesign/customMedals/common/ToolTip";
import nextStep from "@/components/HalfDesign/customMedals/common/nextStep";
import CustomImage from "@/components/CustomImage";
import { checkFile } from "@/utils/validate";
import loader from "@/components/HalfDesign/customMedals/common/loader.vue";
import BaseDialog from "@/components/Quote/BaseDialog.vue";
import areaSelectDialog from "@/components/modal/Half/Detail/components/areaSelectDialog.vue";

export default {
  name: "uploadDrawer",
  inject: ["canvas", "getAreaIndex"],
  props: {
    showMedalsId: {
      type: [Number, String],
    },
    medalsIconList: Array,
    medalsIconTagList: Array,
    uploadList: {
      type: Array,
      default: () => [],
    },
    clickImgId: {
      type: Array,
      default: () => [],
    },
    //绑定值
    bindValue: {
      type: Object,
    },
    moreUpload: {
      type: Boolean,
      default: false,
    },
    needOpenDialog: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      acceptFileType: ".JPG,.JPEG,.jpe,.jif,.jfif,.jfi,.GIF,.PNG,.BMP,.WEBP",
      myswiper2: null,
      selfUplodList: [],
      selfClickImgId: [],
      debounceClickImg: null,
      tempData: {},
      canAdd: false,
      showSelectAreaDialog: false,
      nowUpload: false,
    };
  },
  watch: {
    selfUplodList: {
      deep: true,
      handler(newVal) {
        this.$emit("update:uploadList", newVal);
      },
    },
    selfClickImgId: {
      deep: true,
      handler(newVal) {
        this.$emit("update:clickImgId", newVal);
      },
    },
  },
  computed: {
    lang() {
      return this.$store.getters.lang.quote || {};
    },
    langSemi() {
      return this.$store.getters.lang.semiCustom || {};
    },
    langDesign() {
      return this.$store.getters.lang.design || {};
    },
    device() {
      return this.$store.state.device;
    },
    isMobile() {
      return this.$store.state.device === "mb";
    },
    mbNoUploadStyle() {
      return this.device == "mb" && this.uploadList.length == 0;
    },
    showMedalsIconList() {
      if (this.showMedalsId >= 0) {
        let nowShow = this.medalsIconList.find((item) => {
          return item.id == this.showMedalsId;
        });
        if (nowShow) {
          return nowShow.copyThemeLogoImgList;
        }
      }
      return this.medalsIconList.reduce((pre, cur) => {
        if (Array.isArray(cur.themeLogoList) && cur.themeLogoList.length > 0) {
          let data = cur.copyThemeLogoImgList || [];
          pre = pre.concat(data);
        }
        return pre;
      }, []);
    },
    showMoreUploadList() {
      return this.clickImgId.length > 0
        ? this.clickImgId.filter((item) => item.type == "upload")
        : [];
    },
    areaIndex() {
      return this.getAreaIndex();
    },
  },
  components: {
    ToolTip,
    nextStep,
    CustomImage,
    loader,
    BaseDialog,
    areaSelectDialog,
  },
  methods: {
    tagNameClick(item) {
      this.$emit("update:showMedalsId", item.id);
      this.$emit("tagNameClick", item);
    },
    uploadPic(event, type = "upload") {
      let files = type === "upload" ? event.target.files : event;
      //判断是否弹出选择框
      if (this.needOpenDialog && !this.moreUpload && !this.canAdd) {
        let fileResult = checkFile(Array.from(files), this.acceptFileType, 80);
        if (!fileResult) {
          this.$toast.error("File type error");
          return false;
        }
        if (fileResult.nomalSize.length == 0) {
          this.$store.commit("setSizeDialog", true);
          this.$store.commit("setInputRefName", "replayUpload");
          this.$store.commit("setOverSizeList", file.overSize);
          return false;
        }
        this.tempData = fileResult.nomalSize[0];
        this.$refs.uploadDrawer.value = "";
        this.showSelectAreaDialog = true;
        this.nowUpload = true;
        return;
      }
      this.$gl.show();
      let fileResult = checkFile(Array.from(files), this.acceptFileType, 80);

      if (!fileResult) {
        this.$toast.error("File type error");
        this.$gl.hide();
        return false;
      }
      if (fileResult.nomalSize.length == 0) {
        this.$gl.hide();
        this.$store.commit("setSizeDialog", true);
        this.$store.commit("setInputRefName", "replayUpload");
        this.$store.commit("setOverSizeList", fileResult.overSize);
        this.$refs.uploadDrawer.value = "";
        return false;
      }
      let uploadPromises = [];
      fileResult.nomalSize.forEach((file) => {
        let promise = uploadFile(file).then((res) => {
          let upDataId = generateUUID();
          if (this.moreUpload) {
            let data = {
              id: upDataId,
              iconUrl: res,
            };
            this.$emit("moreAddImgFn", data, "upload");
            this.$emit("update:showMedalsId", -1);
          } else {
            this.$emit("delAllImg");
            this.selfClickImgId = [
              {
                id: upDataId,
                iconUrl: res,
                type: "upload",
              },
            ];
            this.selfUplodList = [];
            this.selfUplodList.push({
              id: upDataId,
              original_filename: file.name,
              secure_url: res,
              iconUrl: res,
              size: (file.size / 1024).toFixed(1),
            });
          }
        });
        uploadPromises.push(promise);
      });

      Promise.all(uploadPromises).then(() => {
        if (this.moreUpload) {
        } else {
          this.$emit("addImg", "upload");
        }
        this.$gl.hide();
        this.$refs.uploadDrawer.value = "";
        if (fileResult.overSize.length > 0) {
          this.$store.commit("setSizeDialog", true);
          this.$store.commit("setInputRefName", "replayUpload");
          this.$store.commit("setOverSizeList", fileResult.overSize);
        }
      });
    },
    delUploadImg() {
      this.$emit("delImg");
      this.selfUplodList = [];
      this.selfClickImgId = [
        {
          id: -1,
          iconUrl: "",
        },
      ];
      this.swiperUpdate();
    },
    initSwiper() {
      this.myswiper2 = new Swiper(this.$refs.swiper2, {
        slidesPerView: 3.5,
        breakpoints: {
          0: {
            slidesPerView: 2, // 小屏幕设备
            spaceBetween: 10,
          },
          1000: {
            slidesPerView: 3.5,
            spaceBetween: 10,
          },
        },
        spaceBetween: 10,
        watchSlidesVisibility: true, //防止不可点击
        on: {
          click: this.onSlideClick, // 添加点击事件监听器
        },
      });
    },
    swiperUpdate() {
      this.$nextTick(() => {
        if (this.myswiper2) this.myswiper2.update();
      });
    },
    onImgClick(data) {
      if (!data.iconUrl || !this.canvas.loadImgState) return;
      //判断是否弹出选择框
      //   if (this.needOpenDialog && !this.moreUpload && !this.canAdd) {
      //     this.showSelectAreaDialog = true;
      //     this.tempData = data;
      //     return;
      //   }
      if (this.moreUpload) {
        this.$emit("update:showMedalsId", data.themeId);
        this.$emit("moreAddImgFn", data, "clicked");
      }
      let imgId = data.id;
      let index = this.selfClickImgId.findIndex((item) => item.id == imgId);
      if (index > -1) {
        return;
      }
      this.$emit("delAllImg");
      data.type = "clicked";
      this.selfClickImgId = [data];
      this.selfUplodList = [];
      this.selfUplodList.push({
        original_filename: "logo",
        iconUrl: data.iconUrl,
        secure_url: data.iconUrl,
        size: 1024,
        id: imgId,
      });
      this.$emit("update:showMedalsId", data.themeId);
      this.$nextTick(() => {
        this.$emit("addImg");
      });
    },
    toNext() {
      this.$emit("nextStep");
    },
    initSwiper2() {
      this.mySwiperDrawer = new Swiper(this.$refs.mySwiperDrawer, {
        spaceBetween: 10,
        observer: true,
        observeParents: true,
        watchSlidesVisibility: true,
        hideOnClick: true,
        slidesPerView: this.device === "mb" ? 3 : "auto",
        slidesPerGroup: 1,
        pagination: {
          el: ".swiper-pagination-drawer",
          clickable: true,
        },
      });
    },
    isActive(id) {
      return this.clickImgId.some((item) => item.id == id);
    },
    async selectArea(needChange, index) {
      this.showSelectAreaDialog = false;
      this.canAdd = true;
      if (needChange) {
        if (this.nowUpload) {
          this.$gl.show();
          uploadFile(this.tempData).then((res) => {
            let upDataId = generateUUID();
            let data = {
              id: upDataId,
              iconUrl: res,
            };
            this.$emit("uploadChangeArea", data, "upload", index[0]);
          });
          this.$gl.hide();
        } else {
          this.$emit("uploadChangeArea", this.tempData, "clicked", index[0]);
        }
        this.tempData = {};
      } else {
        //上传图片的提交
        if (this.nowUpload) {
          this.uploadPic([this.tempData], "afterUpload");
          if (index.length > 1) {
            index.forEach(async (item) => {
              if (item !== this.areaIndex) {
                await uploadFile(this.tempData).then((res) => {
                  let upDataId = generateUUID();
                  let data = {
                    id: upDataId,
                    iconUrl: res,
                  };
                  this.$emit("setOtherFiles", item, {
                    item: data,
                    type: "upload",
                  });
                });
              }
            });
          }
        } else {
          await this.onImgClick(this.tempData);
        }
        this.tempData = {};
      }
      this.canAdd = false;
      this.nowUpload = false;
    },
    cancel() {
      this.needUpload = false;
      this.canAdd = false;
    },
  },
  mounted() {
    this.selfClickImgId = this.clickImgId;
    this.selfUplodList = this.uploadList;
    // this.initSwiper();
    this.debounceClickImg = debounce(this.onImgClick, 100);
    if (this.device === "mb") {
      this.$nextTick(() => {
        // this.initSwiper2();
      });
    }
  },
};
</script>

<style scoped lang="scss">
@import "~assets/css/half.scss";

.borderBox {
  margin-top: 10px;
  width: 100%;
  height: 2px;
  background: #eeeeee;
}

.uploadDrawer {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: 20px;
  height: 100%;
  padding: 20px;

  .top-content {
    .uploadDrawerHeader {
      flex-shrink: 0;

      span {
        font-weight: bold;
        font-size: 18px;
      }
    }
  }

  @include respond-to(mb) {
    max-height: 80vh;
  }
}

.myswiper2 {
  height: 100%;

  .swiper-slide {
    overflow: hidden;
    border-radius: 6px;
    cursor: pointer;
    border: 2px solid transparent;
    // background-color: #e9e9e9;
    align-items: center;
    display: flex;
    justify-content: center;
    box-sizing: border-box;

    &:hover {
      .li-img {
        border-color: $color-primary;

        .icon-shanchu2 {
          color: $color-primary !important;
          opacity: 1;
        }
      }

      .li-imgName {
        color: $color-primary;
      }
    }
  }

  .swiper-slide-thumb-active {
    border-color: #0066cc;
  }

  .li-img {
    position: relative;
    border: 2px solid #d0d0d0;
    border-radius: 5px;
    overflow: hidden;
    aspect-ratio: 1/1;
    max-height: 130px;
    display: flex;
    align-items: center;
    justify-content: center;

    .myIconBox {
      position: absolute;
      right: 0;
      top: 0;

      .icon-shanchu2 {
        opacity: 0;
      }
    }

    ::v-deep .fileBox {
      img {
        max-width: 100%;
        object-fit: cover !important;
        user-select: none;
      }
    }
  }

  .li-imgName {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }
}

.iconListBox {
  max-height: 500px;
  min-height: 340px;
  height: auto;
  overflow: hidden auto;
  transition: none;
  scroll-behavior: smooth;

  .iconBox {
    padding: 10px 2px;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 10px;

    .uploadBox {
      aspect-ratio: 1/1;
      overflow: hidden;
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 4px;
      font-size: 12px;
      padding: 6px;

      ::v-deep .imgBox {
        min-width: 0;
        overflow: hidden;
        flex: 1;
        aspect-ratio: 1/1;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;

        img {
          aspect-ratio: 1/1;
          object-fit: cover;
        }
        .check-icon {
          display: none;
        }

        .shanchuIcon {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          font-size: 24px;
          color: $color-primary;
          cursor: pointer;
          display: none;
        }

        &.active {
          border-color: $color-primary;

          .check-icon {
            display: flex;
          }
        }

        &:hover {
          border-color: $color-primary;
          box-shadow: 2px 1px 4px 2px #efeded, -1px -1px 4px 2px #efeded;
          &.active {
            &::after {
              content: "";
              position: absolute;
              inset: 0;
              background-color: rgba(0, 0, 0, 0.6);
              z-index: 1;
            }
            .shanchuIcon {
              display: flex;
              z-index: 2;
            }
          }
        }
      }

      .uploadText {
        min-width: 0;
        flex-shrink: 0;
        color: #fff;
        background-color: $color-primary;
        border-radius: 4px;
        padding: 0.4em 0.8em;
        text-align: center;
        cursor: pointer;
      }
    }

    .iconItem {
      position: relative;
      cursor: pointer;
      border: 2px solid #d3d5d7;

      img {
        aspect-ratio: 1 / 1;
        object-fit: contain;
        width: 100%;
      }

      .check-icon {
        display: none;
      }

      .shanchuIcon {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 24px;
        color: $color-primary;
        cursor: pointer;
        display: none;
      }

      &.active {
        border-color: $color-primary;

        .check-icon {
          display: flex;
        }
      }

      &:hover {
        border-color: $color-primary;
        box-shadow: 2px 1px 4px 2px #efeded, -1px -1px 4px 2px #efeded;
        &.active {
          &::after {
            content: "";
            position: absolute;
            inset: 0;
            background-color: rgba(0, 0, 0, 0.6);
            z-index: 1;
          }
          .shanchuIcon {
            display: flex;
            z-index: 2;
          }
        }
      }
    }
  }

  @include respond-to(mb) {
    max-height: 30vh;
    height: 30vh;

    .iconBox {
      grid-template-columns: repeat(3, 1fr);
    }
  }
}

.continue {
  cursor: pointer;
  width: fit-content;
  max-width: 65%;
  align-self: center;
  text-align: center;
  flex-shrink: 0;
}

.upload-div {
  border: 1px dashed #ccc;
  width: 100%;
  height: 165px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;

  &.noUpload {
    .text {
      width: 100%;
      border: 0;

      .uploadTip {
        display: none;
      }

      .uploadSize {
        display: flex;
        align-items: center;
        justify-content: center;

        ::v-deep .uploadTip2 {
          b {
            font-size: 12px !important;
          }
        }
      }
    }

    .img {
      width: 0;
    }
  }

  .icon-shangchuan {
    font-size: 20px;
    color: $color-primary;
  }

  .icon-wenhao3 {
    color: $color-primary;
  }

  .text {
    width: 40%;
    border-left: 1px solid #ccc;
    padding: 20px 30px;
    text-align: center;
    position: relative;
    flex-shrink: 0;
    cursor: pointer;

    div:nth-of-type(1) {
      font-size: 16px;
    }

    div:nth-of-type(2) {
      font-size: 14px;
    }

    ::v-deep .uploadTip {
      .v-icon.v-icon {
        color: $color-primary;
      }
    }
  }

  .img {
    width: 60%;
  }

  @include respond-to(mb) {
    height: 120px;

    .text {
      height: 100%;
      padding: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;

      strong {
        font-size: 14px;
      }

      .uploadTip {
        display: none;
      }

      .uploadSize {
        display: none;
      }
    }
  }
}

.tag-div {
  display: flex;
  flex-wrap: wrap;
  column-gap: 36px;
  row-gap: 6px;
  margin: 10px 0;

  .div {
    cursor: pointer;
    color: #333;
    font-size: 14px;
    width: fit-content;
    flex-shrink: 0;
    font-weight: bold;
    opacity: 0.6;

    &:hover {
      color: #333;
      opacity: 1;
    }
  }

  .tt {
    cursor: pointer;
    font-size: 14px;
    color: #333;
    font-weight: bold;
  }

  @include respond-to(mb) {
    column-gap: 2em;
  }
}

.items-cont {
  display: flex;
  flex-direction: column;

  .mySwiperDrawer {
    width: 100%;
    margin-top: 5px;

    .swiper-wrapper {
      width: 100%;
      box-sizing: border-box;

      .swiper-slide {
        margin-left: 10px;
        width: fit-content;
        border: 1px solid #d0d0d0;
        border-radius: 16px;
        padding: 8px 15px;
        cursor: pointer;
        white-space: nowrap;
        text-align: center;

        // margin-left: 10px;
        @include respond-to(mb) {
          width: 100%;
          margin-left: 0;
          font-size: 12px;
          padding-block: 6px;
        }

        &.active {
          // color: white;
          // background: linear-gradient(90deg, #20aeff, #b61ee8);
          // background-color: $color-primary;
        }
      }
    }

    .swiper-pagination-drawer {
      display: none;
    }
  }

  @include respond-to(mb) {
    width: 100%;
    flex-direction: column;
    gap: 20px;

    .mySwiperDrawer {
      width: 100%;
      min-height: 60px;

      &.noContentSwiper {
        min-height: 0;
      }

      margin-top: 5px;

      .swiper-wrapper {
        width: 100%;

        .swiper-slide {
          width: 100%;
          border: 1px solid #d0d0d0;
          border-radius: 4px;
          // padding: 6px 10px;
          cursor: pointer;
          white-space: nowrap;
          font-size: 12px;

          &.active {
            // color: white;
            // background: $color-primary;
            font-weight: bold;
            border: 1px solid $color-primary;
          }
        }
      }

      .swiper-pagination-drawer {
        display: block;
        position: absolute;
        z-index: 999;
        top: 40px;
        left: 50%;
        transform: translateX(-50%);

        ::v-deep .swiper-pagination-bullet {
          height: 21px;
          width: 21px;
          opacity: 1;
          background-color: #b7b7b7;
          margin: 0 6px;

          &.swiper-pagination-bullet-active {
            // background-image: linear-gradient(90deg, #20aeff, #b61ee8);
            background-color: $color-primary;
          }

          @include respond-to(mb) {
            width: 10px;
            height: 10px;
          }
        }
      }
    }
  }
}

input[type="file"] {
  display: none;
  position: absolute;
  left: 0;
  top: 0;
  width: 0;
  opacity: 0;
  clip: rect(0, 0, 0, 0);
}

.selectAreaDialog ::v-deep {
  .base-dialog-model-con {
    max-width: 750px;
    border-radius: 0;
  }
  .icon-guanbi {
    top: 10px !important;
  }
  @include respond-to(mb) {
    .icon-guanbi {
      top: 12px !important;
    }
  }
}
</style>
