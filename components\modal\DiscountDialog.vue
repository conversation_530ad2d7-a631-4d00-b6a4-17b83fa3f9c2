<template>
	<!--折扣优惠弹窗-->
	<div>
		<template v-if="isManage || (isShowFixedBtn && ($store.state.projectComment.openEmailBinaryLong&32)==32)">
			<div class="fixedBtn" :class="{ 'manageFixedBtn': isManage }" @click="openDiscountDialog()" :primary="!$store.getters.isMobile"
				v-if="modal.outer[0].configBtn"
				:style="{ ...modal.outer[0].configBtnStyle, ...modal.outer[0].configBtn.style, ...modal.outer[0].configBtnStyle }">
				<!-- <span>{{ modal.outer[0].configBtn.value }}</span> -->
				<EditDiv v-show="!$store.getters.isMobile&&modal.outer[0].configBtn" tagName="label" v-model:content="modal.outer[0].configBtn.value"
					@click="setModalType(modal.title, modal.outer, 'text')">
					</EditDiv>
				<pic v-if="$store.getters.isMobile&&modal.outer[0].img" :src="modal.outer[0].img.value" :style="{ ...modal.outer[0].imgStyle}"
				:alt="modal.outer[0].img.alt" @click="setModalType(modal.outer[0].img, modal.outer, 'img')" />
				<!-- <b class="fixedBtn_close" :class="$store.getters.isMobile?'icon-hxsht-xp-gb':'icon-a-icon-dzlj-add2zhuanhuan'" @click.stop="fixedBtnClose()"></b> -->
				<b class="fixedBtn_close" :class="$store.getters.isMobile?'icon-hxsht-xp-gb':'icon-close'" @click.stop="fixedBtnClose()"></b>
			</div>
		</template>

		<div flex class="mask" :class="{ 'manageMask': isManage }"
			v-show="isManage || (!isNotFirst && ($store.state.projectComment.openEmailBinaryLong&32)==32)">
			<template v-for="(o, oi) in modal.outer">
				<div class="discount_content" :style="modal.style" v-if="oi === 0" v-show="isShowBigBox">
					<!-- <p>{{ $store.state.projectComment.subscribesDiscount }}</p> -->
					<div pointer class="close-icon2" @click="closeDialog">
						<b class="icon-hxsht-xp-gb"></b>
					</div>
					<div class="content_text1">
						<EditDiv v-model:content="o.subTitle.value" :style="{ ...modal.subTitleStyle, ...o.subTitle.style, ...o.subTitleStyle }"
							v-if="o.subTitle?.value">
						</EditDiv>
					</div>
					<div class="content_text2">
						<EditDiv :tagName="modal.titleTag" v-model:content="o.title.value"
							:style="{ ...modal.titleStyle, ...o.title.style, ...o.titleStyle }" v-if="o.title?.value">
						</EditDiv>
					</div>
					<div class="content_text3">
						<EditDiv v-model:content="o.text.value" :style="{ ...modal.textStyle, ...o.text.style, ...o.textStyle }"
							v-if="o.text">
						</EditDiv>
					</div>
					<div class="content_text4">
						<EditDiv v-model:content="o.tip.value" :style="{ ...modal.tipStyle, ...o.tip.style, ...o.tipStyle }" v-if="o.tip">
						</EditDiv>
					</div>
					<div flex class="input-box discount_content_input_box">
						<input v-model="email" type="email" :placeholder="lang.emailPlaceholder"
							@keyup.enter="btn_getFirstDisCount()" />
					</div>
					<span v-if="isEmailUsed" style="color: red;font-size: 0.8em;">{{ lang.emailUsed }}</span>
					<button v-if="o.button" :primary="o.button.value" class="discount_content_btn"
						:style="{ ...modal.btnStyle, ...o.button.style }" :title="o.button.alt">
						<EditDiv v-show="o.button.value" tagName="label" v-model:content="o.button.value"
							@click="btn_getFirstDisCount()">
						</EditDiv>
						<b :class="o.button.icon" v-show="o.button.icon"></b>
					</button>
					<!-- <p class="content_text5">{{ lang.receiveEmail }}{{ lang.view }}<span>{{ lang.terms }}</span> & <span>{{
					lang.privacy
				}}</span></p> -->

					<!-- <p class="content_text5" v-if="o.reminder" :style="{ ...modal.reminderStyle, ...o.reminder.style }">{{
						o.reminder.value }} {{ lang.view }} <span><n-link tag="a" title="Terms and Conditions"
								to="/info/terms-and-conditions">{{
									lang.terms }}</n-link></span> & <span><n-link tag="a" title="Privacy Policy" to="/info/privacy-policy">{{
		lang.privacy }}</n-link></span>
					</p> -->

					<p class="content_text5" v-if="o.reminder" :style="{ ...modal.reminderStyle, ...o.reminder.style }">
						<!-- {{o.reminder.value }}  -->
						<EditDiv tagName="span" v-model:content="o.reminder.value" v-if="o.reminder" />
						{{ lang.view }}
						<span class="underline_span">
							<n-link tag="a" title="Terms and Conditions" to="/info/terms-and-conditions">{{ lang.terms }}</n-link>
						</span> &
						<span class="underline_span"><n-link tag="a" title="Privacy Policy" to="/info/privacy-policy">{{ lang.privacy }}</n-link></span>
						<span>{{lang.deView}}</span>
					</p>

					<p class="content_text6" ><span @click="closeDialog">{{ lang.noDiscount }}</span></p>
				</div>


				<div class="part2 discount_content2" v-else v-show="!isShowBigBox || isManage" :childHoverIndex="oi">
					<div pointer class="close-icon2" @click="closeSonDialog">
						<b class="icon-hxsht-xp-gb"></b>
					</div>
					<pic class="pic" :src="o.img.value" :style="{ ...modal.imgStyle, ...o.img.style, ...o.imgStyle }" v-if="o.img"
						:alt="o.img.alt" @click="setModalType(o.img, modal.outer, 'img')" />
					<div class="content2_text1">
						<EditDiv v-model:content="o.subTitle.value" :style="{ ...modal.subTitleStyle, ...o.subTitle.style }"
							v-if="o.subTitle?.value"
							@click="setModalType(o.title, modal.outer, 'text')">
						</EditDiv>
					</div>
					<div class="content2_text2">
						<EditDiv v-model:content="o.title.value" :style="{ ...modal.titleStyle, ...o.title.style }"
							v-if="o.title?.value"
							@click="setModalType(o.title, modal.outer, 'text')">
						</EditDiv>
					</div>
					<div class="content2_text3">
						<EditDiv v-model:content="o.text.value" :style="{ ...modal.textStyle, ...o.text.style }"
							v-if="o.text">
						</EditDiv>
					</div>
					<button class="discount_content_btn discount_content2_btn" v-if="o.button" :primary="o.button.value"
						:style="{ ...modal.btnStyle, ...o.button.style }" :title="o.button.alt">
						<EditDiv v-show="o.button.value" tagName="label" v-model:content="o.button.value"
							@click="closeSonDialog()">
						</EditDiv>
						<b :class="o.button.icon" v-show="o.button.icon"></b>
					</button>
				</div>

			</template>
		</div>
	</div>
</template>

<script>
import { medalsApi } from "@/api/medals/medals";
export default {
	name: "modalDiscountDialog",
	props: {
		data: {
			type: Object,
			default: {},
		},
	},
	data() {
		return {
			modal: {
				type: {},
				style: {},
				outer: [{}],
				titleTag: "div",
				...this.data,
			},
			isManage: false,
			// discountForm: {  },

			email: '',

			isShowBigBox: true,

			isNotFirst: true,

			isEmailUsed: false,//邮箱是否已使用过

			// ---
			isShowFixedBtn: false,



		};
	},
	computed: {
		lang() {
			return this.$store.getters.lang.discountDialog || {};
		},

	},
	watch: {
		modal: {
			handler(val) {
				if (process.env.isManage) this.$emit("update:data", val);
			},
			immediate: true,
			deep: true
		},
		isNotFirst(newVal) {
			if (!newVal) this.email = this.$options.data().email
		},
		email(newVal, oldVal) {
			this.isEmailUsed = false
		}
	},
	methods: {
		setModalType(target, targetArray, clickType, event, other) {
			this.$setModal(this, target, targetArray, clickType, event, other)
		},
		fixedBtnClose() {
			this.isShowFixedBtn = false
			this.$cookies.set('isShowFixedBtn', false, {
				path: '/',
				maxAge: 60 * 60 * 24 * 360
			});
		},
		openDiscountDialog() {
			this.isNotFirst = false
		},
		close() {
			this.isNotFirst = true
			this.$cookies.set('isNotFirstShowDiscount', true, {
				path: '/',
				maxAge: 60 * 60 * 24 * 360
			});
		},

		// closeDialog2 () {
		// 	this.isShowBigBox = true
		// },
		closeDialog() {
			// this.$store.commit('setMask', false);
			// sessionStorage.setItem('isNotFirstShowDiscount', JSON.stringify(true));
			// this.$emit('close')
			this.close()
		},

		async btn_getFirstDisCount() {
			// var reg = /^([a-zA-Z]|[0-9])(\w|\-)+@[a-zA-Z0-9]+\.([a-zA-Z]{2,4})$/;
			var reg = /^([a-zA-Z0-9._-]+)@([a-zA-Z0-9-]+)\.([a-zA-Z]{2,4})$/;;
			if (!this.email || this.email == '') return this.$toast.error(this.lang.emailToast)
			else if (!reg.test(this.email)) return this.$toast.error(this.lang.emailValidate)
			let res = await medalsApi.getFirstDiscount({
				// proId: this.$store.state.proId,
				email: this.email
			})
			if (res.data == null) {
				this.isEmailUsed = true
				return
			} else {
				this.isEmailUsed = false
				this.isShowFixedBtn = false
				this.$cookies.set('isShowFixedBtn', false, {
					path: '/',
					maxAge: 60 * 60 * 24 * 360
				});
			}
			this.isShowBigBox = false
		},
		closeSonDialog() {
			this.isNotFirst = true
			this.$cookies.set('isNotFirstShowDiscount', true, {
				path: '/',
				maxAge: 60 * 60 * 24 * 360
			});
		},

	},
	mounted() {
		// if (process.client) {
		// 			this.isNotFirst = sessionStorage.getItem('isNotFirstShowDiscount') || null
		// 		}
		// console.log('subscribesDiscount', this.$store.state.projectComment.subscribesDiscount);
		if (process.browser) {
			let isShowFixedBtn = this.$cookies.get('isShowFixedBtn')  //isShowFixedBtn默认得给false 不然用户叉掉后刷新会弹出来一下
			this.isShowFixedBtn = isShowFixedBtn
			if (isShowFixedBtn == undefined) {
				this.isShowFixedBtn = true
			}
			if (isShowFixedBtn) {
				this.$cookies.set('isShowFixedBtn', isShowFixedBtn, { //cookies 时间重新赋值
					path: '/',
					maxAge: 60 * 60 * 24 * 360
				});
			}
			let isNotFirstShowDiscount = this.$cookies.get('isNotFirstShowDiscount') //用isNot默认给false 然后判断!isNot 就不需要跟上面一样判断undefined
			this.isNotFirst = isNotFirstShowDiscount
			if (isNotFirstShowDiscount) {
				this.$cookies.set('isNotFirstShowDiscount', isNotFirstShowDiscount, {
					path: '/',
					maxAge: 60 * 60 * 24 * 360
				});
			}
		}
	},
	created() {
		if (process.env.isManage) {
			this.isManage = true;
		}
	},
};
</script>

<style lang="scss" scoped>
.discount_content2 {
	min-width: 415px;
	width: 415px;
	background: #FFF;
	padding: 1em 4em calc(2em + 4px);
	text-align: center;
	position: relative;
}

.pic {
	min-height: 115px;
}

.content2_text1,
.content2_text2 {
	font-weight: bold;
}

.content2_text1 {
	font-size: 1.5em;
}

.content2_text2 {
	font-size: calc(2em + 4px);
	color: #3C5FFE;
	margin: 0.15em 0
}

.content2_text3 {
	font-size: 0.8em;
	transform: scale(1.2);
}

.discount_content {
	min-width: 575px;
	width: 575px;
	color: #FFF;
	padding: 2em 3em calc(1vw + 2em);
	text-align: center;
	position: relative;
}

// [theme='9'] .discount_content {
.discount_content {
	// background: linear-gradient(0deg, #381396 0%, #B80FEF 100%);
	background-image: linear-gradient(0deg, $color-dark 0%, $btn-primary 100%);
}

[theme='6'] .discount_content {
	background-image: linear-gradient(0deg, #6F86D6 0%, #619ee0 48%, #48C6EF 100%);
}
[theme='7'] .discount_content {
	background-image: linear-gradient(0deg,#753A0C 0%, #753A0C 48%, #240000 100%);
}
[theme='8'] .discount_content {
	background-image: linear-gradient(0deg, #269E7C 0%, #0DAC7F 48%, #2B84D8 100%);
}
[theme='14'] .discount_content {
	background-image: linear-gradient(0deg, #0532b1 0%, #268fcf 48%, #0286ff 100%);
}


.content_text1,
.content_text2,
.content_text3 {
	font-weight: bold;
}

.content_text1,
.content_text3 {
	font-size: calc(2em - 2px);
}

.content_text2 {
	font-size: calc(4em - 4px);
	color: #FFDE01;
}

[theme='7'] .content_text2 {
	h2::before{
		content: none;
	}
}
[theme='14'] .content_text2 {
	color: #ffa701;
}

.content_text4 {
	font-size: 1em;
	margin-top: 1em;
	padding: 0 1.5em;
}

.content_text5 {
	font-size: calc(1em - 2px);
	padding: 0 3em;

	.underline_span {
		text-decoration: underline;
	}
}

.content_text6 {
	font-weight: bold;
	font-size: calc(1em + 2px);
	text-decoration: underline;
	margin-top: calc(2em - 8px);
	span{
		cursor: pointer;
	}
}


.discount_content_btn {
	width: 100%;
	height: 50px;
	margin: 10px 0 30px;
	border-radius: 0;
	// background: $color-primary;
	// background: linear-gradient(90deg, $color-dark 0%, $btn-primary 100%);
}

[theme='6'] .discount_content_btn {
	background: linear-gradient(90deg,#FE852F 0% ,  #E83D23 100%);
	color: #fff;
	border-radius: 8px;
}
[theme='7'] .discount_content_btn {
	background: linear-gradient(90deg,#FDD206 0% ,  #D27745 100%);
	color: #fff;
}
[theme='14'] .discount_content_btn {
	background: linear-gradient(90deg,rgb(255 131 81) 0% ,  rgb(255 216 0) 100%);
	color: #000;
}

.discount_content_btn:hover {
	transform: scale(1)
}

.discount_content2_btn {
	margin: 1.5em 0 0;
}

.discount_content_input_box {
	flex: 1;
	height: 50px;
	line-height: 50px;
	background: #FFF;
	border-radius: 0;
	border: none;
	// margin-top: calc(2em - 1px);
	margin-top: 0.7em;

	input {
		flex: 1;
		border: none;
		// margin: 0 0.63em;
		margin: 0 10px;
		color: var(--text-primary);
	}
}

.close-icon2 {
	position: absolute;
	right: 1.2em;
	top: 1.2em;
	transform: scale(1.2);
}

// .tips-bottom {
// 	display: block;
// 	color: #74767e;
// 	text-align: center;
// 	font-size: calc(1em + 2px);

// 	span {
// 		font-weight: bold;
// 		color: $color-primary;
// 		text-decoration: underline;
// 	}
// }

.fixedBtn {
	min-width: 10em;
    width: max-content;
	text-align: center;
	font-size: 1.2em;
	// width: 180px;
	position: fixed;
	left: 1vw;
	bottom: 0.5em;
	border-radius: 0.4em;
	border: 1px solid #fff;
	color: #FFF;
	cursor: pointer;
	z-index: 999;
	padding: 0 1.3em;
	clip-path: unset !important;

	.fixedBtn_close {
		// transform: translateX(26px) translateY(-10px) rotate(45deg)
		// transform: rotate(45deg);
		position: absolute;
		top: .05vw;
		right: .2vw;
		font-weight: 400;
	}
}

@media screen and (max-device-width: $pad-width) {
	.discount_content {
		// transform: scale(1.2);
	}
}

@media screen and (max-device-width: $mb-width) {
	.discount_content2 {
		min-width: 290px;
		width: 290px;
		padding: 1.5em calc(4em - 6px) calc(2em + 1px);
	}

	.discount_content {
		min-width: 345px;
		width: 345px;
		// transform: scale(0.95);
		padding: 30px;
	}

	.discount_content_input_box,
	.discount_content_btn {
		height: 35px;
		line-height: 35px;
	}

	.discount_content_input_box {
		margin-top: 1em;
	}

	.discount_content_btn {
		margin: 5px 0 1em;
	}

	.discount_content2_btn {
		margin: 1.5em 0 0;
	}

	.content_text1,
	.content_text3,
	.content_text6 {
		font-size: calc(1em + 3px);
	}

	.content_text2 {
		font-size: calc(2em + 6px);
		margin-bottom: 0.4em;
	}

	.content_text4,
	.content_text5 {
		font-size: 1em;
	}

	.content_text5 {
		// padding: 0 1em;
		padding: 0;
	}

	// .fixedBtn {
	// 	display: none;
	// }
	.fixedBtn {
		min-width: initial;
		bottom:1em;
		.fixedBtn_close {
			top: 3px;
			right: 5px;
			font-size: 0.5em;
			transform: none;
			color:#fff;
		}
	}
}

.mask {
	height: 100vh;
	max-height: 1200px;
	align-items: center;
	justify-content: center;
	background: rgba($color: black, $alpha: 0.85);
	position: fixed;
	z-index: 9999999;
	right: 0;
	left: 0;
	top: 0;
}

.manageMask {
	position: static;
}

.manageFixedBtn {
	position: static !important;
}

</style>
