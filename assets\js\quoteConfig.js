const getPinsAllStepConfig = () => {
	return {
		quoteCategory: {
			pcActive2: true,
			mbActive2: true,
			mediaConfig: {
				style: {
					"aspect-ratio": "1008/708",
				},
			},
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						gap: "20px",
						"align-items": "stretch",
					},
				},
				ipad: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						"column-gap": "12px",
						"row-gap": "23px",
						"align-items": "stretch",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						gap: "10px",
						"align-items": "stretch",
					},
				},
			},
			stepItemStyle: {
				style: {
					borderRadius: "6px",
					"background-color": "rgb(242, 242, 242)",
				},
				stepImgWrapStyle: {
					style: {
						borderRadius: "6px 6px 0 0",
						border: "none",
					},
				},
				stepTextWrapStyle: {
					style: {
						flex: 1,
						width: "100%",
						backgroundColor: "#f2f2f2",
						padding: "10px",
						"margin-top": "0",
						"font-size": "14px",
						borderRadius: "0 0 6px 6px",
					},
				},
			},
		},
		"Rush Pin Size": {
			showPriceText: false,
			mediaConfig: {
				style: {
					"aspect-ratio": "213/178",
				},
			},
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(5, 1fr)",
						"column-gap": "25px",
						"row-gap": "23px",
					},
				},
				ipad: {
					style: {
						"grid-template-columns": "repeat(5, 1fr)",
						"column-gap": "12px",
						"row-gap": "23px",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						"column-gap": "5px",
						"row-gap": "10px",
					},
				},
			},
			stepItemStyle: {
				style: {
					borderRadius: "6px",
				},
				stepTextWrapStyle: {
					style: {
						flex: 1,
						fontSize: "16px",
						padding: "5px 0.4em",
						"margin-top": "5px",
					},
				},
				mbStepTextWrapStyle: {
					style: {
						fontSize: "12px",
						padding: "5px 0.4em",
						"margin-top": "0",
						"text-align": "center",
					},
				},
				stepImgWrapStyle: {
					style: {
						background: "#f4f5f5",
						"border-radius": "6px",
					},
				},
				mbStepImgWrapStyle: {
					style: {
						"border-radius": "6px",
					},
					customCircle: 3,
				},
			},
		},
		"Select Metal Finish": {
			mediaConfig: {
				style: {
					"aspect-ratio": "220/118",
				},
			},
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(5, 1fr)",
						gap: "20px",
					},
				},
				ipad: {
					style: {
						"grid-template-columns": "repeat(5, 1fr)",
						"column-gap": "10px",
						"row-gap": "25px",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						gap: "10px",
					},
				},
			},
			stepItemStyle: {
				style: {
					borderRadius: "6px",
				},
				stepTextWrapStyle: {
					style: {
						flex: 1,
						fontSize: "16px",
						padding: "5px 0.4em",
						"margin-top": "5px",
						"text-align": "center",
					},
				},
				mbStepTextWrapStyle: {
					style: {
						fontSize: "12px",
						padding: "5px 0.4em",
						"margin-top": "0",
					},
				},
				stepImgWrapStyle: {
					style: {
						backgroundColor: "#e4e4e4",
					},
				},
				mbStepImgWrapStyle: {
					style: {
						"border-radius": "6px",
					},
					customCircle: 3,
				},
			},
		},
		"Select Amount of Colors for Pin": {
			mediaConfig: {
				style: {
					"aspect-ratio": "180/170",
				},
			},
			stepItemStyle: {
				stepImgWrapStyle: {
					style: {
						background: "#f4f5f5",
					},
				},
				mbStepImgWrapStyle: {
					style: {
						"border-radius": "6px",
					},
					customCircle: 3,
				},
				mbStepTextWrapStyle: {
					style: {
						fontSize: "12px",
						padding: "5px 0.4em",
						"margin-top": "0",
					},
				},
				stepTextWrapStyle: {
					style: {
						flex: 1,
						fontSize: "16px",
						"margin-top": "16px",
						"text-align": "center",
					},
				},
			},
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(6, 1fr)",
						gap: "20px",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						"row-gap": "10px",
						"column-gap": "5px",
					},
				},
			},
		},
		"Additional Upgrades (Optional)": {
			pcActive2: true,
			mbActive2: true,
			showNextBtn: true,
			mediaConfig: {
				style: {
					"aspect-ratio": "1008/708",
				},
			},
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						"column-gap": "32px",
						"row-gap": "22px",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						"row-gap": "20px",
						"column-gap": "5px",
					},
				},
			},
			stepItemStyle: {
				style: {
					borderRadius: "6px",
					"background-color": "rgb(242, 242, 242)",
				},
				mbStepImgWrapStyle: {
					customCircle: 3,
				},
				stepImgWrapStyle: {
					style: {
						borderRadius: "6px 6px 0 0",
						border: "none",
					},
				},
				mbStepTextWrapStyle: {
					style: {
						"font-size": "12px",
						padding: "6px 10px 12px",
					},
				},
				stepTextWrapStyle: {
					style: {
						flex: 1,
						width: "100%",
						backgroundColor: "#f2f2f2",
						padding: "6px 10px",
						"margin-top": "0",
						"font-size": "16px",
						borderRadius: "0 0 6px 6px",
					},
				},
			},
		},
		"Select Attachment": {
			pcActive2: true,
			mbActive2: true,
			hasViewMore: true,
			showChildListLength:6,
			mediaConfig: {
				style: {
					"aspect-ratio": "660/420",
				},
			},
			stepItemStyle: {
				style: {
					borderRadius: "6px",
					background: "#f4f5f5",
				},
				mbStepImgWrapStyle: {
					style: {
						"border-radius": "6px",
					},
					customCircle: 3,
				},
				mbStepTextWrapStyle: {
					style: {
						fontSize: "12px",
						padding: "6px 10px 12px",
						"margin-bottom": "0",
					},
				},
				stepTextWrapStyle: {
					style: {
						flex: 1,
						fontSize: "16px",
						padding: "5px 0.4em",
						"margin-top": "0",
						"margin-bottom": "10px",
					},
				},
			},
			stepContentConfig: {
				ipad: {
					style: {
						"grid-template-columns": "repeat(5, 1fr)",
						"column-gap": "10px",
						"row-gap": "27px",
					},
				},
				mediaQueries: {
					"@media screen and (min-width: 1801px)": {
						"grid-template-columns": "repeat(5, 1fr)",
						gap: "20px",
					},
					"@media screen and (max-width: 1800px)": {
						"grid-template-columns": "repeat(4, 1fr)",
						gap: "20px",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						"column-gap": "5px",
						"row-gap": "13px",
					},
				},
			},
		},
		"Back Side Option": {
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						"column-gap": "25px",
						"row-gap": "23px",
					},
				},
				ipad: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						"column-gap": "12px",
						"row-gap": "23px",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						"column-gap": "5px",
						"row-gap": "10px",
					},
				},
			},
			stepItemStyle: {
				style: {
					"border-radius": "5px",
				},
				mbStepImgWrapStyle: {
					style: {
						"border-radius": "5px",
					},
				},
				mbStepTextWrapStyle: {
					style: {
						"font-size": "12px",
						"margin-bottom": "0",
					},
				},
			},
		},
		"Select Packaging": {
			pcActive2: true,
			mbActive2: true,
			mediaConfig: {
				style: {
					"aspect-ratio": "696/540",
				},
			},
			stepItemStyle: {
				style: {
					borderRadius: "6px",
					background: "#f4f5f5",
				},
				stepImgWrapStyle: {
					style: {
						borderRadius: "6px 6px 0 0",
					},
				},
				stepTextWrapStyle: {
					style: {
						flex: 1,
						fontSize: "16px",
						padding: "5px 0.5em",
						"margin-top": "0",
						"margin-bottom": "10px",
						"text-align": "center",
					},
				},
				mbStepTextWrapStyle: {
					style: {
						fontSize: "12px",
						"margin-bottom": "5px",
					},
				},
			},
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(5, 1fr)",
						gap: "20px",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						"column-gap": "5px",
						"row-gap": "13px",
					},
				},
			},
		},
		"Select Shapes": {
			showPriceText: false,
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(5, 1fr)",
						gap: "20px",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						"column-gap": "5px",
						"row-gap": "13px",
					},
				},
			},
		},
	};
};

const getPinBadgesAllStepConfig = () => {
	return {
		"Select Shapes": {
			showPriceText: false,
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(5, 1fr)",
						gap: "20px",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						"column-gap": "5px",
						"row-gap": "13px",
					},
				},
			},
		},
		quoteCategory: {
			pcActive2: true,
			mbActive2: true,
			mediaConfig: {
				style: {
					"aspect-ratio": "1008/708",
				},
			},
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						"row-gap": "20px",
						"column-gap": "14px",
					},
				},
				ipad: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						"row-gap": "20px",
						"column-gap": "14px",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						gap: "10px",
					},
				},
			},
			stepItemStyle: {
				style: {
					borderRadius: "6px",
					"background-color": "rgb(242, 242, 242)",
				},
				stepImgWrapStyle: {
					style: {
						borderRadius: "6px 6px 0 0",
						border: "none",
					},
				},
				stepTextWrapStyle: {
					style: {
						flex: 1,
						width: "100%",
						backgroundColor: "#f2f2f2",
						padding: "10px",
						"margin-top": "0",
						"font-size": "14px",
						borderRadius: "0 0 6px 6px",
					},
				},
			},
		},
		"Rush Pin Size": {
			showPriceText: false,
			mediaConfig: {
				style: {
					"aspect-ratio": "213/178",
				},
			},
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(5, 1fr)",
						"column-gap": "20px",
						"row-gap": "20px",
					},
				},
				ipad: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						"column-gap": "20px",
						"row-gap": "20px",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						"column-gap": "20px",
						"row-gap": "20px",
					},
				},
			},
			stepItemStyle: {
				style: {
					borderRadius: "6px",
				},
				stepTextWrapStyle: {
					style: {
						flex: 1,
						fontSize: "16px",
						padding: "5px 0.4em",
						"margin-top": "5px",
					},
				},
				mbStepTextWrapStyle: {
					style: {
						fontSize: "12px",
						padding: "5px 0.4em",
						"margin-top": "0",
					},
				},
				stepImgWrapStyle: {
					style: {
						background: "#f4f5f5",
						"border-radius": "6px",
					},
				},
				mbStepImgWrapStyle: {
					style: {
						"border-radius": "6px",
					},
				},
			},
		},
		"Select Metal Finish": {
			mbActive2: true,
			hasViewMore: true,
			showChildListLength:6,
			mediaConfig: {
				style: {
					"aspect-ratio": "220/118",
				},
			},
			stepContentConfig: {
				mediaQueries: {
					"@media screen and (min-width: 1801px)": {
						"grid-template-columns": "repeat(6, 1fr)",
						gap: "20px",
					},
					"@media screen and (max-width: 1800px)": {
						"grid-template-columns": "repeat(5, 1fr)",
						gap: "20px",
					},
					"@media screen and (max-width: 1678px)": {
						"grid-template-columns": "repeat(4, 1fr)",
						gap: "20px",
					},
					"@media screen and (min-width: 1366px) and (max-width: 1500px)": {
						"grid-template-columns": "repeat(6, 1fr)",
						gap: "20px",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						"column-gap": "5.5px",
						"row-gap": "5px",
					},
				},
			},
			stepItemStyle: {
				style: {
					borderRadius: "6px",
				},
				stepTextWrapStyle: {
					style: {
						flex: 1,
						fontSize: "16px",
						padding: "5px 0.4em",
						"margin-top": "5px",
						"text-align": "center",
					},
				},
				mbStepTextWrapStyle: {
					style: {
						width: "100%",
						fontSize: "12px",
						padding: "5px 0.4em",
						"margin-top": "0",
						backgroundColor: "#f2f2f2",
						borderRadius: "0 0 6px 6px",
					},
				},
				stepImgWrapStyle: {
					style: {
						backgroundColor: "#e4e4e4",
					},
				},
				mbStepImgWrapStyle: {
					style: {
						border: "none",
						"border-radius": "6px 6px 0 0",
					},
				},
			},
		},
		"Select Amount of Colors for Pin": {
			mediaConfig: {
				style: {
					"aspect-ratio": "180/170",
				},
			},
			stepItemStyle: {
				stepImgWrapStyle: {
					style: {
						background: "#f4f5f5",
					},
				},
				mbStepImgWrapStyle: {
					style: {
						"border-radius": "6px",
					},
				},
				mbStepTextWrapStyle: {
					style: {
						fontSize: "12px",
						padding: "5px 0.4em",
						"margin-top": "0",
					},
				},
				stepTextWrapStyle: {
					style: {
						flex: 1,
						fontSize: "16px",
						"margin-top": "16px",
						"text-align": "center",
					},
				},
			},
			stepContentConfig: {
				mediaQueries: {
					"@media screen and (min-width: 1801px)": {
						"grid-template-columns": "repeat(6, 1fr)",
						gap: "20px",
					},
					"@media screen and (max-width: 1800px)": {
						"grid-template-columns": "repeat(5, 1fr)",
						gap: "20px",
					},
					"@media screen and (max-width: 1650px)": {
						"grid-template-columns": "repeat(4, 1fr)",
						gap: "20px",
					},
					"@media screen and (min-width: 1366px) and (max-width: 1500px)": {
						"grid-template-columns": "repeat(6, 1fr)",
						gap: "20px",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						"row-gap": "18.5px",
						"column-gap": "7px",
					},
				},
			},
		},
		"Additional Upgrades (Optional)": {
			pcActive2: true,
			mbActive2: true,
			showNextBtn: true,
			mediaConfig: {
				style: {
					"aspect-ratio": "1008/708",
				},
			},
			stepContentConfig: {
				mediaQueries: {
					"@media screen and (min-width: 1901px)": {
						"grid-template-columns": "repeat(3, 1fr)",
						gap: "11px",
					},
					"@media screen and (min-width: 1501px) and (max-width: 1900px)": {
						"grid-template-columns": "repeat(3, 1fr)",
						gap: "11px",
					},
					"@media screen and (min-width: 1366px) and (max-width: 1500px)": {
						"grid-template-columns": "repeat(5, 1fr)",
						gap: "11px",
					},
					"@media screen and (max-width: 1365px)": {
						"grid-template-columns": "repeat(3, 1fr)",
						gap: "11px",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						"row-gap": "10px",
						"column-gap": "5px",
					},
				},
			},
			stepItemStyle: {
				style: {
					borderRadius: "6px",
					"background-color": "rgb(242, 242, 242)",
				},
				stepImgWrapStyle: {
					style: {
						borderRadius: "6px 6px 0 0",
						border: "none",
						backgroundColor: "#f4f5f5",
					},
				},
				mbStepTextWrapStyle: {
					style: {
						"font-size": "12px",
						padding: "6px 10px 12px",
					},
				},
				stepTextWrapStyle: {
					style: {
						flex: 1,
						width: "100%",
						backgroundColor: "#f2f2f2",
						padding: "10px",
						"margin-top": "0",
						"font-size": "16px",
						borderRadius: "0 0 6px 6px",
					},
				},
			},
		},
		"Select Attachment": {
			pcActive2: true,
			mbActive2: true,
			hasViewMore: true,
			showChildListLength:6,
			mediaConfig: {
				style: {
					"aspect-ratio": "660/420",
				},
			},
			stepItemStyle: {
				style: {
					borderRadius: "6px",
					background: "#f4f5f5",
				},
				mbStepImgWrapStyle: {
					style: {
						"border-radius": "6px",
					},
					customCircle: 3,
				},
				mbStepTextWrapStyle: {
					style: {
						fontSize: "12px",
						padding: "6px 10px 12px",
						"margin-bottom": "0",
					},
				},
				stepTextWrapStyle: {
					style: {
						flex: 1,
						fontSize: "16px",
						padding: "5px 0.4em",
						"margin-top": "0",
						"margin-bottom": "10px",
					},
				},
			},
			stepContentConfig: {
				mediaQueries: {
					"@media screen and (min-width: 1651px)": {
						"grid-template-columns": "repeat(5, 1fr)",
						gap: "10px",
					},
					"@media screen and (min-width: 1366px) and (max-width: 1500px)": {
						"grid-template-columns": "repeat(5, 1fr) !important",
						gap: "10px",
					},
					"@media screen and (max-width: 1650px)": {
						"grid-template-columns": "repeat(4, 1fr)",
						gap: "10px",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						"column-gap": "5px",
						"row-gap": "13px",
					},
				},
			},
		},
		"Back Side Option": {
			mbActive2: true,
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						"column-gap": "30px",
						"row-gap": "20px",
					},
				},
				ipad: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						"column-gap": "30px",
						"row-gap": "20px",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						"column-gap": "5px",
						"row-gap": "7.5px",
					},
				},
			},
			stepItemStyle: {
				mbStepImgWrapStyle: {
					style: {
						border: "none",
						"border-radius": "6px 6px 0 0",
					},
				},
				mbStepTextWrapStyle: {
					style: {
						"font-size": "12px",
						"margin-bottom": "0",
						background: "#f3f3f3",
						width: "100%",
						padding: "6px",
						borderRadius: "0 0 6px 6px",
						"margin-top": "0",
					},
				},
			},
		},
		"Select Packaging": {
			pcActive2: true,
			mbActive2: true,
			mediaConfig: {
				style: {
					"aspect-ratio": "696/540",
				},
			},
			stepItemStyle: {
				style: {
					borderRadius: "6px",
					background: "#f4f5f5",
				},
				stepImgWrapStyle: {
					style: {
						border: "none",
						borderRadius: "6px 6px 0 0",
					},
				},
				stepTextWrapStyle: {
					style: {
						flex: 1,
						fontSize: "16px",
						padding: "5px 0.4em",
						"margin-top": "0",
						"margin-bottom": "10px",
						"text-align": "center",
					},
				},
				mbStepTextWrapStyle: {
					style: {
						fontSize: "12px",
						"margin-bottom": "5px",
					},
				},
			},
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(5, 1fr)",
						gap: "20px",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						gap: "5px",
					},
				},
			},
		},
	};
};

const getBallMarkerAllStepConfig = () => {
	return {
		quoteCategory: {
			videoFit: "cover",
			pcActive2: true,
			mbActive2: false,
			mediaConfig: {
				style: {
					"aspect-ratio": "382/287",
				},
			},
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						"row-gap": "20px",
						"column-gap": "14px",
					},
				},
				ipad: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						"row-gap": "20px",
						"column-gap": "14px",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						gap: "10px",
					},
				},
			},
			stepItemStyle: {
				style: {
					borderRadius: "6px",
					overflow: "hidden",
					"background-color": "rgb(242, 242, 242)",
				},
				stepImgWrapStyle: {
					style: {
						borderRadius: "10px",
					},
				},
				stepTextWrapStyle: {
					style: {
						flex: 1,
						width: "100%",
						backgroundColor: "#f2f2f2",
						padding: "10px",
						"margin-top": "0",
						"font-size": "14px",
						borderRadius: "0 0 6px 6px",
					},
				},
			},
		},
		"Select Plating / Finish": {
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(5, 1fr)",
						gap: "20px",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						gap: "7px",
						"align-items": "baseline",
					},
				},
			},
		},
		"Select  Amount of Colors for Ball Markers": {
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(6, 1fr)",
						gap: "20px",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						gap: "8px",
					},
				},
			},
		},
		"Additional Upgrades (Optional)": {
			videoFit: "cover",
			pcActive2: true,
			mbActive2: true,
			mediaConfig: {
				style: {
					"aspect-ratio": "287/237",
				},
			},
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(4, 1fr)",
						"row-gap": "20px",
						"column-gap": "14px",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						gap: "10px",
					},
				},
			},
			stepItemStyle: {
				style: {
					borderRadius: "6px",
					"background-color": "rgb(242, 242, 242)",
				},
				stepImgWrapStyle: {
					style: {
						borderRadius: "6px 6px 0 0",
						border: "none",
					},
				},
				stepTextWrapStyle: {
					style: {
						flex: 1,
						width: "100%",
						backgroundColor: "#f2f2f2",
						padding: "10px",
						"margin-top": "0",
						"font-size": "14px",
						borderRadius: "0 0 6px 6px",
					},
				},
			},
		},
	};
};

const getLanyardAllStepConfig = () => {
	return {
		"Products Categories": {
			showPriceText: false,
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(4, 1fr)",
						"row-gap": "20px",
						"column-gap": "14px",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						gap: "10px",
					},
				},
			},
		},
	};
};

const getWristbandAllStepConfig = () => {
	return {
		"Band Size": {
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(6, 1fr)",
						gap: "20px",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						gap: "10px",
						"align-items": "self-start",
					},
				},
			},
			showPriceText: false
		},
		"Message Style": {
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						gap: "20px",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						gap: "10px",
						"align-items": "self-start",
					},
				},
			},
			showPriceText: false
		},
		"More Options": {
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						gap: "20px",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						gap: "10px",
						"align-items": "self-start",
					},
				},
			},
		},
		"Packaging Options": {
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						gap: "20px",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						gap: "10px",
						"align-items": "self-start",
					},
				},
			},
		}
	}
}

const getKeychainsAllStepConfig = () => {
	return {
		quoteCategory: {
			videoFit: "cover",
			pcActive2: true,
			mbActive2: true,
			mediaConfig: {
				style: {
					"aspect-ratio": "1008/708",
				},
			},
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						"row-gap": "20px",
						"column-gap": "14px",
					},
				},
				ipad: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						"row-gap": "20px",
						"column-gap": "14px",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						gap: "10px",
						"align-items": "stretch",
					},
				},
			},
			stepItemStyle: {
				style: {
					borderRadius: "6px",
					overflow: "hidden",
					"background-color": "#f2f2f2",
				},
				stepImgWrapStyle: {
					style: {
						borderRadius: "10px",
					},
				},
				pcStepTextWrapStyle: {
					style: {
						flex: 1,
						width: "100%",
						backgroundColor: "#f2f2f2",
						padding: "10px 0",
						"margin-top": "0",
						"font-size": "16px",
						borderRadius: "0 0 6px 6px",
						display: "flex",
						"justify-content": "center",
						"flex-wrap": "wrap",
					},
				},
				mbStepTextWrapStyle: {
					style: {
						flex: 1,
						width: "100%",
						backgroundColor: "#f2f2f2",
						padding: "10px 0",
						"margin-top": "0",
						borderRadius: "0 0 6px 6px",
						display: "flex",
						"justify-content": "center",
						"flex-wrap": "wrap",
					},
				},
			},
		},
		"Select Shapes": {
			mediaConfig: {
				style: {
					"aspect-ratio": "1000/1000",
					background: "rgb(244 245 245)",
				},
			},
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						gap: "20px",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						gap: "10px",
						"align-items": "self-start",
					},
				},
			},
		},
		Plating: {
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(6, 1fr)",
						gap: "20px",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						gap: "10px",
					},
				},
			},
		},
		"Link & Chain Options": {
			hasViewMore: true,
			pcActive2: true,
			mbActive2: true,
			showChildListLength:6,
			stepItemStyle: {
				style: {
					background: "rgb(244 245 245)",
					"border-radius": "10px",
					"padding-bottom": "10px",
				},
				stepTextWrapStyle: {
					style: {
						padding: "0 7px",
					},
				},
			},
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(5, 1fr)",
						gap: "20px"
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						gap: "10px"
					},
				},
			},
		},
		"Select Packaging": {
			pcActive2: true,
			mbActive2: true,
			stepItemStyle: {
				style: {
					background: "rgb(244 245 245)",
					"border-radius": "10px",
					"padding-bottom": "10px",
					"justify-content": "flex-start"
				},
			},
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(5, 1fr)",
						gap: "20px"
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						gap: "10px"
					},
				},
			},
		},
		Packaging: {
			pcActive2: true,
			mbActive2: true,
			stepItemStyle: {
				style: {
					background: "rgb(244 245 245)",
					"border-radius": "10px",
					"padding-bottom": "10px",
					"justify-content": "flex-start"
				},
			},
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(5, 1fr)",
						gap: "20px"
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						gap: "10px"
					},
				},
			},
		},
		"Design Areas": {
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						gap: "20px",
						"align-items": "flex-start",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						gap: "10px",
						"align-items": "flex-start",
					},
				},
			},
		},
		"Backstamp Options": {
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						gap: "20px",
						"align-items": "flex-start",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						gap: "10px",
						"align-items": "flex-start",
					},
				},
			},
		},
		"Acrylic Charm Color": {
			stepItemStyle: {
				style: {
					background: "rgb(244 245 245)",
					"border-radius": "10px",
					"padding-bottom": "10px",
				},
			},
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(6, 1fr)",
						gap: "20px",
						"align-items": "flex-start",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						gap: "10px",
						"align-items": "flex-start",
					},
				},
			},
		},
		"Additional Upgrades": {
			stepItemStyle: {
				style: {
					background: "rgb(244 245 245)",
					"border-radius": "10px",
					"padding-bottom": "10px",
				},
			},
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						gap: "20px",
						"align-items": "flex-start",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						gap: "10px",
						"align-items": "flex-start",
					},
				},
			},
		},
		"Keychain Printed Area": {
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						gap: "20px",
						"align-items": "flex-start",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						gap: "10px",
						"align-items": "flex-start",
					},
				},
			},
		},
		"PVC Keychain Color": {
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(5, 1fr)",
						gap: "20px",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						gap: "10px",
					},
				},
			},
		},
		"2D or 3D": {
			pcActive2: false,
			mbActive2: true,
			mediaConfig: {
				style: {
					"aspect-ratio": "1008/708",
					"object-fit": "cover",
				},
			},
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						gap: "20px",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						gap: "10px",
					},
				},
			},
			stepItemStyle: {
				mbStyle: {
					borderRadius: "6px",
					overflow: "hidden",
					"background-color": "rgb(242, 242, 242)",
					"padding-bottom": "5px",
				},
				stepTextWrapStyle: {
					mbStyle: {
						flex: 1,
						width: "100%",
						backgroundColor: "#f2f2f2",
						padding: "10px",
						borderRadius: "0 0 6px 6px",
					},
				},
			},
		},
		"Back Printing Options": {
			pcActive2: false,
			mbActive2: true,
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						gap: "20px",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						gap: "10px",
						"font-size": "13px",
					},
				},
			},
			stepItemStyle: {
				mbStyle: {
					borderRadius: "6px",
					overflow: "hidden",
					"background-color": "rgb(242, 242, 242)",
					"font-size": "13px",
					"padding-bottom": "5px",
				},
				stepTextWrapStyle: {
					mbStyle: {
						flex: 1,
						width: "100%",
						backgroundColor: "#f2f2f2",
						padding: "10px",
						borderRadius: "0 0 6px 6px",
						"font-size": "13px",
					},
				},
			},
		},
		"Keychain Color": {
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						gap: "20px",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						gap: "10px",
					},
				},
			},
		},
		"Keychain Size": {
			widthDes: "Keychains width",
			heightDes: "Keychains height",
			showNextBtn: true,
			// smallTitle: this.$store.getters.lang.quote.sizeSmallTitle,
			smallTitle: "Try selecting your height and width below and we will provide you with the exact measurements and price. The size calculation rule is based on the design (height + width) / 2 = actual size.",
		},
		"Embroidery Coverage": {
			showPriceText: false,
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						gap: "20px",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						gap: "10px",
					},
				},
			},
		},
		"Keychain Design Areas": {
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						gap: "20px",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						gap: "10px",
					},
				},
			},
		},
		"Keychain Border": {
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						gap: "20px",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						gap: "10px",
					},
				},
			},
		},
		"Backside Options": {
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						gap: "20px",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						gap: "10px",
					},
				},
			},
		},
	};
};

const getPatchesAllStepConfig = function () {
	return {
		"Number of Silicone Colors": {
			moldText: 4,
			stepItemStyle: {
				style: {
					"justify-content": "space-between !important",
				},
			},
			// priceTextCss: {
			// 	normaltext: {
			// 		"flex-direction": "column",
			// 		"text-align": "left",
			// 		"margin-left": "5em",
			// 	},
			// 	tipTextStyle: {
			// 		visibility: "hidden",
			// 	},
			// },
		},
		"Pre-Cut Transfers": {
			smallTitle: "We can pre-cut them so that you can use them more conveniently!",
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						gap: "20px",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						gap: "10px",
					},
				},
			},
		},
		"Intended Use": {
			showPriceText: false,
			imgBorderStyle: 2,
			hideTitleSelectText: true,
			showNextBtn: true,
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(4, 1fr)",
						gap: "20px",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						gap: "10px",
					},
				},
			},
		},
		"Select Sequins Diameter": {
			showPriceText: false,
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						gap: "20px",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						gap: "10px",
					},
				},
			},
		},
		"Leather Color": {
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(5, 1fr)",
						gap: "20px",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						gap: "10px",
					},
				},
			},
		},
		"Patch Outline": {
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						gap: "20px",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						gap: "10px",
					},
				},
			},
		},
		"Printing Method": {
			"smallTitle": this.$store.getters.lang.quote.Reminder,
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						gap: "20px",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						gap: "10px",
					},
				},
			},
		},
		quoteCategory: {
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						gap: "20px",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						gap: "10px",
					},
				},
			},
		},
		"Patch Size": {
			showNextBtn: true,
			smallTitle: this.$store.getters.lang.quote.sizeSmallTitle,
		},
		"Patch Color": {
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						gap: "20px",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						gap: "10px",
					},
				},
			},
		},
		"Patch Shape": {
			showPriceText: false,
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(5, 1fr)",
						gap: "20px",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						gap: "10px",
					},
				},
			},
		},
		"Patch Border": {
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						gap: "20px",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						gap: "10px",
					},
				},
			},
		},
		"Purchase Intention": {
			showPriceText: false,
			imgBorderStyle: 2,
			hideTitleSelectText: true,
			showNextBtn: true,
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(4, 1fr)",
						gap: "20px",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						gap: "10px",
					},
				},
			},
		},
		"Additional Upgrades": {
			smallTitle: this.$store.getters.lang.quote.prompt,
			showNextBtn: true,
			hideTitleSelectText: true,
		},
		"Patch Backing": {
			clickStepShowTips: true,
			showNextBtn: true,
		},
		"Label Size": {
			showNextBtn: true,
			smallTitle: this.$store.getters.lang.quote.sizeSmallTitle,
		},
		"Label Fold Type": {
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(4, 1fr)",
						gap: "20px",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						gap: "10px",
					},
				},
			},
		},
		"Label Color": {
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						gap: "20px",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						gap: "10px",
					},
				},
			},
		},
		"Label Thread Type": {
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						gap: "20px",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						gap: "10px",
					},
				},
			},
		},
	};
};

const getOrnamentAllStepConfig = function () {
	return {
		quoteCategory: {
			videoFit: "cover",
			pcActive2: true,
			mbActive2: true,
			mediaConfig: {
				style: {
					"aspect-ratio": "382/287",
				},
			},
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						"row-gap": "20px",
						"column-gap": "14px",
					},
				},
				ipad: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						"row-gap": "20px",
						"column-gap": "14px",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						gap: "10px",
					},
				},
			},
			stepItemStyle: {
				style: {
					"flex-direction": "row-reverse",
					borderRadius: "6px",
					overflow: "hidden",
					"background-color": "#FAFAFA",
				},
				stepImgWrapStyle: {
					style: {
						borderRadius: "10px",
					},
				},
				stepTextWrapStyle: {
					style: {
						flex: 1,
						width: "100%",
						backgroundColor: "#FAFAFA",
						padding: "10px",
						"margin-top": "0",
						"font-size": "14px",
						borderRadius: "0 0 6px 6px",
					},
				},
			},
		},
		"Ornament Process": {
			smallTitle: "You can select more than one option.",
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						gap: "20px",
					},
				},
				ipad: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						gap: "20px",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						gap: "10px",
					},
				},
			},
		},
		"Ornament Attachment": {
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						gap: "20px",
					},
				},
				ipad: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						gap: "20px",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						gap: "10px",
					},
				},
			},
			zoomIconConfig: {
				showZoomIcon: true,
				style: {
					color: "#666666",
				},
			},
			showPreviewImg: true, //场景图预览
		},
		"Ornament Package": {
			pcActive2: false,
			mbActive2: true,
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						gap: "20px",
					},
				},
				ipad: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						gap: "20px",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						gap: "10px",
					},
				},
			},
		},
		"Select Finish": {
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						gap: "20px",
					},
				},
				ipad: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						gap: "20px",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						gap: "10px",
					},
				},
			},
		},
		"Select Printed Area": {
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						gap: "20px",
					},
				},
				ipad: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						gap: "20px",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						gap: "10px",
					},
				},
			},
		},
		"Select Acrylic Charm Color": {
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						gap: "10px",
					},
				},
				ipad: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						gap: "10px",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						gap: "1.3333vw .7667vw",
					},
				},
			},
		},
	};
};

const getCufflinksAllStepConfig = function () {
	return {
		"Select Turnaround Time": {
			showNextBtn: true,
			smallTitle: this.$store.getters.lang.quote.hourstext,
		},
		"Upload Artwork & Comments": {
			hideTitleSelectText: true,
		},
		"Quantity":{
			hideTitleSelectText: true
		},
	};
};

const getFidgetSpinnerAllStepConfig = function(){
	return {
		"Select Plating Colors":{
			hasViewMore: true,
			showChildListLength:6,
			priceTextCss: {
				mbNormaltext: {
					"font-size":"0.9em"
				}
			},
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(5, 1fr)",
						gap: "20px",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						gap: "20px 10px",
						"align-items": "flex-start"
					},
				},
			}
		},
		"Select Your 3D style":{
			mediaConfig: {
				style: {
					"aspect-ratio": "372 / 203"
				},
			},
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						gap: "20px",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						gap: "10px",
					},
				},
			}
		},
		"Select Packaging Options":{
			pcActive2: true,
			mbActive2: true,
			stepItemStyle: {
				style: {
					borderRadius: "10px",
					"background-color": "rgb(242, 242, 242)",
				},
				stepTextWrapStyle: {
					style: {
						flex: 1,
						width: "100%",
						backgroundColor: "#f2f2f2",
						padding: "10px",
						"margin-top": "0",
						"font-size": "14px",
						borderRadius: "0 0 10px 10px",
					},
				},
			},
		},
		"Fidget Spinner Plating":{
			hasViewMore: true,
			showChildListLength:6,
			priceTextCss: {
				mbNormaltext: {
					"font-size":"0.9em"
				}
			},
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(5, 1fr)",
						gap: "20px",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						gap: "20px 10px",
						"align-items": "flex-start"
					},
				},
			}
		},
		"Fidget Spinner Packaging":{
			pcActive2: true,
			mbActive2: true,
			stepItemStyle: {
				style: {
					borderRadius: "10px",
					"background-color": "rgb(242, 242, 242)",
				},
				stepTextWrapStyle: {
					style: {
						flex: 1,
						width: "100%",
						backgroundColor: "#f2f2f2",
						padding: "10px",
						"margin-top": "0",
						"font-size": "14px",
						borderRadius: "0 0 10px 10px",
					},
				},
			},
		},
		"Plating":{
			hasViewMore: true,
			showChildListLength:6,
			priceTextCss: {
				mbNormaltext: {
					"font-size":"0.9em"
				}
			},
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(5, 1fr)",
						gap: "20px",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						gap: "20px 10px",
						"align-items": "flex-start"
					},
				},
			}
		},
	}
}

// Quote 类
export class Quote {
	constructor(pid, productsName, restaurants) {
		this.pid = pid;
		this.productsName = productsName;
		this.restaurants = restaurants || [
			{ value: "10", address: "10" },
			{ value: "50", address: "50" },
			{ value: "100", address: "100" },
			{ value: "200", address: "200" },
			{ value: "300", address: "300" },
			{ value: "500", address: "500" },
			{ value: "1000", address: "1000" },
			{ value: "3000", address: "3000" },
			{ value: "5000", address: "5000" },
			{ value: "10000", address: "10000" },
		];
	}
}

class quoteData {
	constructor(data) {
		let defaultData = {
			stepIndex: 1, //参数步骤排序
			childList: [],
			paramName: "quoteCategory", //参数名称
			alias: "test", //参数别名
			type: "quoteCategory", //类型
			paramType: "quoteCategory", //参数类型
		};
		let newData = Object.assign({}, defaultData, data);
		for (let i in newData) {
			if (newData.hasOwnProperty(i)) {
				this[i] = newData[i];
			}
		}
	}
}

export const getQuoteStyleData = function (pid) {
	switch (pid) {
		case 573:
			return new quoteData({
				stepIndex: 0,
				alias: this.lang.Patches.paramName1,
			});
		case 25:
			return new quoteData({
				alias: this.lang.pins.paramName,
			});
		case 201:
			return new quoteData({
				alias: this.lang.ornament.paramName,
			});
		case 53:
			return new quoteData({
				alias: this.lang.beltBuckles.paramName,
			});
		case 40:
			return new quoteData({
				alias: this.lang.coins.paramName,
			});
		case 323:
			return new quoteData({
				alias: this.lang.coins.paramName,
			});
		case 324:
			return new quoteData({
				alias: this.lang.coins.paramName,
			});
		case 51:
			return new quoteData({
				alias: this.lang.tradingpins.paramName,
			});
		case 45:
			return new quoteData({
				alias: this.lang.medals.paramName,
			});
		case 121:
			return new quoteData({
				stepIndex: 0,
				alias: this.lang.n3DembroideredPatches.paramName,
				noShowDetail: true,
			});
		case 117:
			return new quoteData({
				stepIndex: 0,
				alias: this.lang.AcrylicKeyChains.paramName,
				noShowDetail: true,
			});
		case 112:
			return new quoteData({
				alias: this.lang.metalkeychains.paramName,
			});
		case 399:
			return new quoteData({
				alias: this.lang.MetalPrintingKeyChains.paramName,
				noShowDetail: true,
			});
		case 118:
			return new quoteData({
				alias: this.lang.PVCKeyChains.paramName,
				noShowDetail: true,
			});
		case 110:
			return new quoteData({
				alias: this.lang.chenillePatches.paramName,
				noShowDetail: true,
			});
		case 568:
			return new quoteData({
				alias: this.lang.TatamiFabricSiliconePatches.paramName,
				noShowDetail: true,
			});
		case 107:
			return new quoteData({
				alias: this.lang.embroideredPatches.paramName,
			});
		case 122:
			return new quoteData({
				stepIndex: 0,
				alias: this.lang.flexPatches.paramName,
				noShowDetail: true,
			});
		case 111:
			return new quoteData({
				stepIndex: 0,
				alias: this.lang.leatherPatches.paramName,
				noShowDetail: true,
			});
		case 108:
			return new quoteData({
				stepIndex: 0,
				alias: this.lang.denimPatches.paramName,
				noShowDetail: true,
			});
		case 398:
			return new quoteData({
				stepIndex: 0,
				alias: this.lang.Patches.paramName,
				noShowDetail: true,
			});
		case 407:
			return new quoteData({
				alias: this.lang.coins.paramName,
			});
		case 408:
			return new quoteData({
				alias: this.lang.coins.paramName,
			});
		case 409:
			return new quoteData({
				alias: this.lang.coins.paramName,
			});
		case 109:
			return new quoteData({
				stepIndex: 0,
				alias: this.lang.Patches.paramName,
				noShowDetail: true,
			});
		case 160:
			return new quoteData({
				stepIndex: 0,
				alias: this.lang.buttonBadges.paramName,
			});
		case 159:
			return new quoteData({
				stepIndex: 0,
				alias: this.lang.embroideredBadges.paramName,
			});
		case 158:
			return new quoteData({
				stepIndex: 0,
				alias: this.lang.pinBadges.paramName,
			});
		case 55:
			return new quoteData({
				alias: this.lang.PVCpatches.paramName,
			});
		case 219:
			return new quoteData({
				stepIndex: 0,
				alias: this.lang.embroideredKeychains.paramName,
			});
		case 227:
			return new quoteData({
				stepIndex: 0,
				alias: this.lang.fidgetSpinner.paramName,
				noShowDetail: true,
			});
		case 229:
			return new quoteData({
				stepIndex: 0,
				alias: this.lang.fidgetSpinner.paramName,
				noShowDetail: true,
			});
		case 231:
			return new quoteData({
				stepIndex: 0,
				alias: this.lang.fidgetSpinner.paramName,
				noShowDetail: true,
			});
		case 233:
			return new quoteData({
				stepIndex: 0,
				alias: this.lang.fidgetSpinner.paramName,
				noShowDetail: true,
			});
		case 235:
			return new quoteData({
				stepIndex: 0,
				alias: this.lang.fidgetSpinner.paramName,
				noShowDetail: true,
			});
		case 237:
			return new quoteData({
				stepIndex: 0,
				alias: this.lang.fidgetSpinner.paramName,
				noShowDetail: true,
			});
		case 240:
			return new quoteData({
				stepIndex: 0,
				alias: this.lang.fidgetSpinner.paramName,
				noShowDetail: true,
			});
		case 763:
			return new quoteData({
				stepIndex: -1,
				alias: this.lang.cufflinks.paramName,
				noShowDetail: true,
			});
		case 764:
			return new quoteData({
				stepIndex: -1,
				alias: this.lang.cufflinks.paramName,
				noShowDetail: true,
			});
		case 762:
			return new quoteData({
				stepIndex: -1,
				alias: this.lang.cufflinks.paramName,
				noShowDetail: true,
			});
		case 759:
			return new quoteData({
				stepIndex: -1,
				alias: this.lang.cufflinks.paramName,
				noShowDetail: true,
			});
		case 761:
			return new quoteData({
				stepIndex: -1,
				alias: this.lang.cufflinks.paramName,
				noShowDetail: true,
			});
		case 760:
			return new quoteData({
				stepIndex: -1,
				alias: this.lang.cufflinks.paramName,
				noShowDetail: true,
			});
		case 261:
			return new quoteData({
				stepIndex: 0,
				alias: this.lang.fidgetSpinner.paramName,
				noShowDetail: true,
			});
		case 273:
			return new quoteData({
				stepIndex: 0,
				alias: this.lang.fidgetSpinner.paramName,
				noShowDetail: true,
			});
		case 275:
			return new quoteData({
				stepIndex: 0,
				alias: this.lang.fidgetSpinner.paramName
			});
		case 286:
			return new quoteData({
				stepIndex: 0,
				alias: this.lang.fidgetSpinner.paramName,
				noShowDetail: true,
			});
		case 295:
			return new quoteData({
				stepIndex: 0,
				alias: this.lang.fidgetSpinner.paramName,
				noShowDetail: true,
			});
		case 298:
			return new quoteData({
				stepIndex: 0,
				alias: this.lang.fidgetSpinner.paramName,
				noShowDetail: true,
			});
		case 300:
			return new quoteData({
				alias: this.lang.metalBusinessCards.paramName,
			});
		case 671:
			return new quoteData({
				stepIndex: 0,
				alias: this.lang.metalBusinessCards.paramName,
				noShowDetail: true,
			});
		case 310:
			return new quoteData({
				stepIndex: 0,
				alias: this.lang.fidgetSpinner.paramName,
				noShowDetail: true,
			});
		case 312:
			return new quoteData({
				stepIndex: 0,
				alias: this.lang.fidgetSpinner.paramName,
				noShowDetail: true,
			});
		case 330:
			return new quoteData({
				stepIndex: 0,
				alias: this.lang.fidgetSpinner.paramName,
				noShowDetail: true,
			});
		case 205:
			return new quoteData({
				stepIndex: 0,
				alias: this.lang.fidgetSpinner.paramName,
				noShowDetail: true,
			});
		case 433:
			return new quoteData({
				alias: this.lang.medals.paramName,
				noShowDetail: true,
			});
		case 444:
			return new quoteData({
				stepIndex: 0,
				alias: this.lang.denimPatches.paramName,
				noShowDetail: true,
			});
		case 459:
			return new quoteData({
				alias: this.lang.care.paramName,
			});
		case 472:
			return new quoteData({
				alias: this.lang.coins.paramName,
			});
		case 458:
			return new quoteData({
				stepIndex: 1,
				alias: this.lang.heatTransferPatches.paramName,
			});
		case 478:
			return new quoteData({
				stepIndex: 0,
				alias: this.lang.siliconePatches.paramName,
			});
		case 479:
			return new quoteData({
				alias: this.lang.pins.paramName,
			});
		case 489:
			return new quoteData({
				noShowDetail: true,
				alias: this.lang.pins.paramName,
			});
		case 535:
			return new quoteData({
				alias: this.lang.pins.paramName,
			});
		case 536:
			return new quoteData({
				stepIndex: 0,
				noShowDetail: true,
				alias: this.lang.pins.paramName,
			});
		case 490:
			return new quoteData({
				noShowDetail: true,
				alias: this.lang.pins.paramName,
			});
		case 491:
			return new quoteData({
				noShowDetail: true,
				alias: this.lang.pins.paramName,
			});
		case 537:
			return new quoteData({
				noShowDetail: true,
				alias: this.lang.pins.paramName,
			});
		case 460:
			return new quoteData({
				noShowDetail: true,
				alias: this.lang.pins.paramName,
			});
		case 495:
			return new quoteData({
				alias: this.lang.pinBadges.paramName,
			});
		case 502:
			return new quoteData({
				noShowDetail: true,
				alias: this.lang.pinBadges.paramName,
			});
		case 543:
			return new quoteData({
				alias: this.lang.pinBadges.paramName,
			});
		case 506:
			return new quoteData({
				stepIndex: 0,
				noShowDetail: true,
				alias: this.lang.pinBadges.paramName,
			});
		case 503:
			return new quoteData({
				noShowDetail: true,
				alias: this.lang.pinBadges.paramName,
			});
		case 504:
			return new quoteData({
				noShowDetail: true,
				alias: this.lang.pinBadges.paramName,
			});

		case 633:
			return new quoteData({
				noShowDetail: true,
				alias: this.lang.pinBadges.paramName,
			});
		case 631:
			return new quoteData({
				noShowDetail: true,
				alias: this.lang.pinBadges.paramName,
			});
		case 583:
			return new quoteData({
				noShowDetail: true,
				alias: this.lang.pinBadges.paramName,
			});
		case 581:
			return new quoteData({
				noShowDetail: true, //报价没有父类
				alias: this.lang.pinBadges.paramName,
			});
		case 561:
			return new quoteData({
				alias: this.lang.ballMarker.paramName,
			});
		case 638:
			return new quoteData({
				alias: this.lang.ballMarker.paramName,
			});
		case 644:
			return new quoteData({
				alias: this.lang.ballMarker.paramName,
			});
		case 650:
			return new quoteData({
				alias: this.lang.ballMarker.paramName,
			});
		case 661:
			return new quoteData({
				stepIndex: 0,
				noShowDetail: true,
				alias: this.lang.pinBadges.paramName,
			});
		case 557:
			return new quoteData({
				stepIndex: 0,
				noShowDetail: true,
				alias: this.lang.ballMarker.paramName,
			});
		case 619:
			return new quoteData({
				stepIndex: 0,
				noShowDetail: true,
				alias: this.lang.lanyard.LanyardStyle,
			});
		case 635:
			return new quoteData({
				stepIndex: 0,
				noShowDetail: true,
				alias: this.lang.lanyard.LanyardStyle,
			});
		case 629:
			return new quoteData({
				stepIndex: 0,
				noShowDetail: true,
				alias: this.lang.lanyard.LanyardStyle,
			});
		case 695:
			return new quoteData({
				stepIndex: 0,
				noShowDetail: true,
				alias: this.lang.lanyard.LanyardStyle,
			});
		case 700:
			return new quoteData({
				stepIndex: 0,
				noShowDetail: true,
				alias: this.lang.lanyard.LanyardStyle,
			});
		case 701:
			return new quoteData({
				stepIndex: 0,
				noShowDetail: true,
				alias: this.lang.lanyard.LanyardStyle,
			});
		case 702:
			return new quoteData({
				stepIndex: 0,
				noShowDetail: true,
				alias: this.lang.lanyard.LanyardStyle,
			});
		case 703:
			return new quoteData({
				stepIndex: 0,
				noShowDetail: true,
				alias: this.lang.lanyard.LanyardStyle,
			});
		case 704:
			return new quoteData({
				stepIndex: 0,
				noShowDetail: true,
				alias: this.lang.lanyard.LanyardStyle,
			});
		case 705:
			return new quoteData({
				stepIndex: 0,
				noShowDetail: true,
				alias: this.lang.lanyard.LanyardStyle,
			});
		case 597:
			return new quoteData({
				stepIndex: 0,
				noShowDetail: true,
				alias: this.lang.lanyard.LanyardStyle,
			});
		case 621:
			return new quoteData({
				stepIndex: 0,
				noShowDetail: true,
				alias: this.lang.lanyard.LanyardStyle,
			});
		case 602:
			return new quoteData({
				stepIndex: 0,
				noShowDetail: true,
				alias: this.lang.lanyard.LanyardStyle,
			});
		case 747:
			return new quoteData({
				stepIndex: 0,
				noShowDetail: true,
				alias: this.lang.lanyard.LanyardStyle,
			});
		case 599:
			return new quoteData({
				stepIndex: 0,
				noShowDetail: true,
			});
		case 600:
			return new quoteData({
				stepIndex: 0,
				noShowDetail: true,
			});
		case 601:
			return new quoteData({
				stepIndex: 0,
				noShowDetail: true,
			});
		case 689:
			return new quoteData({
				stepIndex: 0,
				noShowDetail: true,
				alias: this.lang.lanyard.LanyardStyle,
			});
		case 659:
			return new quoteData({
				stepIndex: 0,
				alias: this.$store.getters.lang.quote.Color,
			});
		case 656:
			return new quoteData({
				stepIndex: 0,
				noShowDetail: true,
			});
		case 526:
			return new quoteData({
				stepIndex: 0,
				alias: this.lang.ballMarker.paramName,
			});
		case 625:
			return new quoteData({
				stepIndex: 0,
				alias: this.$store.getters.lang.quote.Color,
			});
		case 749:
			return new quoteData({
				stepIndex: 0,
				noShowDetail: true,
				alias: this.lang.wristband.WristbandStyle,
			});
	}
};

export const getQuoteConfig = function (routeName) {
	let config;
	switch (routeName) {
		case "lang-quote-tradingpins-quote":
			config = new Quote(51, "Trading Pins");
			config.allStepConfig = getPinsAllStepConfig();
			config.allStepConfig["Additional Upgrades (Optional)"] = {
				pcActive2: true,
				mbActive2: true,
				showNextBtn: true,
				mediaConfig: {
					style: {
						"aspect-ratio": "700/604",
					},
				},
				stepContentConfig: {
					pc: {
						style: {
							"grid-template-columns": "repeat(5, 1fr)",
						},
					},
					mb: {
						style: {
							"grid-template-columns": "repeat(2, 1fr)",
						},
					},
				},
				stepItemStyle: {
					style: {
						borderRadius: "6px",
						"background-color": "rgb(242, 242, 242)",
					},
					mbStepImgWrapStyle: {
						customCircle: 3,
					},
					stepImgWrapStyle: {
						style: {
							borderRadius: "6px 6px 0 0",
							border: "none",
						},
					},
					mbStepTextWrapStyle: {
						style: {
							"font-size": "12px",
							padding: "6px 10px 12px",
						},
					},
					stepTextWrapStyle: {
						style: {
							flex: 1,
							width: "100%",
							backgroundColor: "#f2f2f2",
							padding: "6px 10px",
							"margin-top": "0",
							"font-size": "16px",
							borderRadius: "0 0 6px 6px",
						},
					},
				},
			};
			config.allStepConfig["Select Packaging"] = {
				pcActive2: true,
				mbActive2: true,
				mediaConfig: {
					style: {
						"aspect-ratio": "696/540",
					},
				},
				stepItemStyle: {
					style: {
						borderRadius: "6px",
							background: "#f4f5f5",
					},
					stepImgWrapStyle: {
						style: {
							borderRadius: "6px 6px 0 0",
						},
					},
					stepTextWrapStyle: {
						style: {
							flex: 1,
								fontSize: "16px",
								padding: "5px 0.5em",
								"margin-top": "0",
								"margin-bottom": "10px",
								"text-align": "center",
						},
					},
					mbStepTextWrapStyle: {
						style: {
							fontSize: "12px",
								"margin-bottom": "5px",
						},
					},
				},
				stepContentConfig: {
					pc: {
						style: {
							"grid-template-columns": "repeat(4, 1fr)",
								gap: "20px",
						},
					},
					mb: {
						style: {
							"grid-template-columns": "repeat(2, 1fr)",
								"column-gap": "5px",
								"row-gap": "13px",
						},
					},
				},
			}
			break;
		case "lang-quote-coins-quote":
			config = new Quote(40, "Coins");
			break;
		case "lang-quote-uv-printed-3D-challenge-coins":
			config = new Quote(324, "Coins");
			break;
		case "lang-quote-bottle-opener-coins":
			config = new Quote(407, "Coins");
			break;
		case "lang-quote-spinner-coins":
			config = new Quote(408, "Coins");
			break;
		case "lang-quote-single-sided-challenge-coins":
			config = new Quote(323, "Coins");
			break;
		case "lang-quote-3D-coins":
			config = new Quote(472, "Coins");
			break;
		case "lang-quote-heat-transfer-patches":
			config = new Quote(458, "heat transfer patches");
			config.sizeType = "sizeSelect";
			config.allStepConfig = getPatchesAllStepConfig.call(this);
			config.allStepConfig["quoteCategory"] = {
				stepContentConfig: {
					pc: {
						style: {
							"grid-template-columns": "repeat(4, 1fr)",
							gap: "20px",
						},
					},
					mb: {
						style: {
							"grid-template-columns": "repeat(2, 1fr)",
							gap: "10px",
						},
					},
				},
			};
			break;
		case "lang-quote-3d-silicone-patches":
			config = new Quote(478, "3d silicone patches");
			config.sizeType = "sizeSelect";
			config.allStepConfig = getPatchesAllStepConfig.call(this);
			config.allStepConfig["quoteCategory"] = {
				stepContentConfig: {
					pc: {
						style: {
							"grid-template-columns": "repeat(4, 1fr)",
							gap: "20px",
						},
					},
					mb: {
						style: {
							"grid-template-columns": "repeat(2, 1fr)",
							gap: "10px",
						},
					},
				},
			};
			break;
		case "lang-quote-custom-embroidered-patches":
			config = new Quote(107, "custom embroidered patches");
			config.sizeType = "sizeSelect";
			config.allStepConfig = getPatchesAllStepConfig.call(this);
			break;
		case "lang-quote-3d-embroidered-patches":
			config = new Quote(121, "3d embroidered patches");
			config.sizeType = "normal";
			config.allStepConfig = getPatchesAllStepConfig.call(this);
			break;
		case "lang-quote-custom-embroidered-printed-patches":
			config = new Quote(444, "custom embroidered printed patches");
			config.sizeType = "normal";
			config.allStepConfig = getPatchesAllStepConfig.call(this);
			break;
		case "lang-quote-custom-printed-patches":
			config = new Quote(108, "custom printed patches");
			config.sizeType = "sizeSelect";
			config.allStepConfig = getPatchesAllStepConfig.call(this);
			break;
		case "lang-quote-custom-tatami-fabric-silicone-patches":
			config = new Quote(568, "custom tatami fabric silicone patches", [
				{ value: "50", address: "50" },
				{ value: "100", address: "100" },
				{ value: "200", address: "200" },
				{ value: "300", address: "300" },
				{ value: "500", address: "500" },
				{ value: "1000", address: "1000" },
				{ value: "1500", address: "1500" },
				{ value: "2000", address: "2000" },
				{ value: "2500", address: "2500" },
			]);
			config.sizeType = "sizeSelect";
			config.allStepConfig = getPatchesAllStepConfig.call(this);
			config.allStepConfig["Patch Color"] = {
				moldText: 4,
				priceTextCss: {
					normaltext: {
						"text-align": "left",
					},
					tipTextStyle: {
						"text-align": "left",
						"margin-left": "1.8em",
					},
				},
				stepContentConfig: {
					pc: {
						style: {
							"grid-template-columns": "repeat(4, 1fr)",
							gap: "20px",
						},
					},
					mb: {
						style: {
							"grid-template-columns": "repeat(2, 1fr)",
							gap: "5px",
						},
					},
				},
				stepItemStyle: {
					style: {
						"flex-direction": "row",
						padding: "10px",
						"border-radius": "10px",
						border: "1px solid #EFEFEF",
						"justify-content": "left",
					},
					mbStyle: {
						padding: "5px",
					},
					stepImgWrapStyle: {
						style: {
							width: "85%",
						},
					},
					mbStepImgWrapStyle: {
						style: {
							width: "55px",
							"border-radius": "5px",
						},
					},
					stepTextWrapStyle: {
						style: {
							width: "100%",
							"margin-left": "10px",
							"text-align": "center",
						},
						aliasStyle: {
							style: {
								"justify-content": "left",
							},
						},
					},
					mbStepTextWrapStyle: {
						style: {
							"font-size": "12px",
						},
						customCircleStyle: {
							style: {
								width: "16px",
								height: "16px",
							},
						},
					},
				},
			};
			break;
		case "lang-quote-custom-woven-patches":
			config = new Quote(109, "custom woven patches");
			config.sizeType = "sizeSelect";
			config.allStepConfig = getPatchesAllStepConfig.call(this);
			break;
		case "lang-quote-custom-chenille-patches":
			config = new Quote(110, "custom chenille patches");
			config.sizeType = "sizeSelect";
			config.allStepConfig = getPatchesAllStepConfig.call(this);
			break;
		case "lang-quote-custom-leather-patches":
			config = new Quote(111, "custom leather patches");
			config.sizeType = "sizeSelect";
			config.allStepConfig = getPatchesAllStepConfig.call(this);
			break;
		case "lang-quote-custom-flex-patches":
			config = new Quote(122, "custom flex patches");
			config.sizeType = "sizeSelect";
			config.allStepConfig = getPatchesAllStepConfig.call(this);
			break;
		case "lang-quote-one-color-tpu-patches":
			config = new Quote(295, "one color tpu patches");
			config.sizeType = "normal";
			config.allStepConfig = getPatchesAllStepConfig.call(this);
			config.allStepConfig["Patch Color"] = {
				showPriceText: false,
				stepContentConfig: {
					pc: {
						style: {
							"grid-template-columns": "repeat(4, 1fr)",
							gap: "20px",
						},
					},
					mb: {
						style: {
							"grid-template-columns": "repeat(2, 1fr)",
							gap: "10px",
						},
					},
				},
			};
			break;
		case "lang-quote-PVCpatches-quote":
			config = new Quote(55, "PVC patches");
			config.sizeType = "sizeSelect";
			config.allStepConfig = {
				quoteCategory: {
					mediaConfig: {
						style: {
							"aspect-ratio": "1008/708",
						},
					},
					stepContentConfig: {
						pc: {
							style: {
								"grid-template-columns": "repeat(3, 1fr)",
								gap: "20px",
							},
						},
						mb: {
							style: {
								"grid-template-columns": "repeat(2, 1fr)",
								gap: "10px",
							},
						},
					},
				},
				Size: {
					showNextBtn: true,
					smallTitle: this.$store.getters.lang.quote.sizeSmallTitle,
				},
				Color: {
					moldText: 1,
					stepContentConfig: {
						pc: {
							style: {
								"grid-template-columns": "repeat(5, 1fr)",
								gap: "20px",
							},
						},
						mb: {
							style: {
								"grid-template-columns": "repeat(2, 1fr)",
								gap: "10px",
							},
						},
					},
				},
				"2D or 3D": {
					moldText: 3,
					mediaConfig: {
						style: {
							"aspect-ratio": "1008/708",
						},
					},
					stepItemStyle: {
						stepTextWrapStyle: {
							pcAliasStyle: {
								style: {
									"justify-content": "left",
								},
							},
						},
					},
					priceTextCss: {
						pcNormaltext: {
							"align-items": "flex-start",
							"margin-left": "22px",
						},
					},
					stepContentConfig: {
						pc: {
							style: {
								"grid-template-columns": "repeat(3, 1fr)",
								"align-items": "flex-start",
								gap: "20px",
							},
						},
						mb: {
							style: {
								"grid-template-columns": "repeat(2, 1fr)",
								"align-items": "flex-start",
								gap: "10px",
							},
						},
					},
				},
				"Purchase Intention": {
					showPriceText: false,
					imgBorderStyle: 2,
					hideTitleSelectText: true,
					showNextBtn: true,
					stepContentConfig: {
						pc: {
							style: {
								"grid-template-columns": "repeat(4, 1fr)",
								gap: "20px",
							},
						},
						mb: {
							style: {
								"grid-template-columns": "repeat(2, 1fr)",
								gap: "10px",
							},
						},
					},
				},
				"PVC Patch Shape": {
					stepContentConfig: {
						pc: {
							style: {
								"grid-template-columns": "repeat(5, 1fr)",
								gap: "20px",
							},
						},
						mb: {
							style: {
								"grid-template-columns": "repeat(2, 1fr)",
								gap: "10px",
							},
						},
					},
				},
			};
			break;
		case "lang-quote-custom-blank-patches":
			config = new Quote(273, "custom black patches");
			config.sizeType = "sizeSelect";
			config.allStepConfig = {
				"Patch Size": {
					showNextBtn: true,
					smallTitle: this.$store.getters.lang.quote.sizeSmallTitle,
				},
				"Patch Shape": {
					showPriceText: false,
					stepContentConfig: {
						pc: {
							style: {
								"grid-template-columns": "repeat(5, 1fr)",
								gap: "20px",
							},
						},
						mb: {
							style: {
								"grid-template-columns": "repeat(2, 1fr)",
								gap: "10px",
							},
						},
					},
				},
				"Patch Twill Color": {
					showPriceText: false,
					stepContentConfig: {
						pc: {
							style: {
								"grid-template-columns": "repeat(5, 1fr)",
								gap: "20px",
							},
						},
						mb: {
							style: {
								"grid-template-columns": "repeat(2, 1fr)",
								gap: "10px",
							},
						},
					},
				},
				"Patch Backing": {
					clickStepShowTips: true,
					showNextBtn: true,
					stepContentConfig: {
						pc: {
							style: {
								"grid-template-columns": "repeat(4, 1fr)",
								gap: "20px",
							},
						},
						mb: {
							style: {
								"grid-template-columns": "repeat(2, 1fr)",
								gap: "10px",
							},
						},
					},
				},
				"Purchase Intention": {
					showPriceText: false,
					imgBorderStyle: 2,
					hideTitleSelectText: true,
					showNextBtn: true,
					stepContentConfig: {
						pc: {
							style: {
								"grid-template-columns": "repeat(4, 1fr)",
								gap: "20px",
							},
						},
						mb: {
							style: {
								"grid-template-columns": "repeat(2, 1fr)",
								gap: "10px",
							},
						},
					},
				},
			};
			break;
		case "lang-quote-custom-sequin-patches":
			config = new Quote(298, "custom sequin patches");
			config.sizeType = "sizeSelect";
			config.allStepConfig = getPatchesAllStepConfig.call(this);
			config.allStepConfig["Patch Color"] = {
				stepContentConfig: {
					pc: {
						style: {
							"grid-template-columns": "repeat(4, 1fr)",
							gap: "20px",
						},
					},
					mb: {
						style: {
							"grid-template-columns": "repeat(2, 1fr)",
							gap: "10px",
						},
					},
				},
			};
			break;
		case "lang-quote-custom-woven-labels":
			config = new Quote(310, "custom woven labels");
			config.sizeType = "sizeSelect";
			config.allStepConfig = getPatchesAllStepConfig.call(this);
			break;
		case "lang-quote-custom-denim-patches":
			config = new Quote(398, "custom denim patches");
			config.sizeType = "sizeSelect";
			config.allStepConfig = getPatchesAllStepConfig.call(this);
			break;
		case "lang-quote-custom-printed-care-labels":
			config = new Quote(459, "custom printed care labels");
			config.sizeType = "sizeSelect";
			config.allStepConfig = getPatchesAllStepConfig.call(this);
			break;
		//pins
		case "lang-quote-custom-enamel-pins":
			config = new Quote(479, "Pins");
			config.allStepConfig = getPinsAllStepConfig();
			break;
		case "lang-quote-custom-die-struck-pins":
			config = new Quote(489, "Pins");
			config.allStepConfig = getPinsAllStepConfig();
			break;
		case "lang-quote-custom-3D-pins":
			config = new Quote(535, "Pins");
			config.allStepConfig = getPinsAllStepConfig();
			break;
		case "lang-quote-custom-uv-printed-pins":
			config = new Quote(536, "Pins");
			config.allStepConfig = getPinsAllStepConfig();
			break;
		case "lang-quote-custom-offset-pins":
			config = new Quote(490, "Pins");
			config.allStepConfig = getPinsAllStepConfig();
			break;
		case "lang-quote-custom-rush-pins":
			config = new Quote(491, "Pins");
			config.allStepConfig = getPinsAllStepConfig();
			break;
		case "lang-quote-custom-acrylic-pins":
			config = new Quote(537, "Pins");
			config.allStepConfig = getPinsAllStepConfig();
			break;
		case "lang-quote-custom-cut-out-pins":
			config = new Quote(460, "Pins");
			config.allStepConfig = getPinsAllStepConfig();
			break;
		case "lang-quote-hatpins-quote":
			config = new Quote(275, "Pins");
			config.allStepConfig = getPinsAllStepConfig();
			config.allStepConfig["quoteCategory"] = {
				pcActive2: true,
				mbActive2: true,
				mediaConfig: {
					style: {
						"aspect-ratio": "380/265",
					},
				},
				stepContentConfig: {
					pc: {
						style: {
							"grid-template-columns": "repeat(3, 1fr)",
							gap: "20px",
							"align-items": "stretch",
						},
					},
					ipad: {
						style: {
							"grid-template-columns": "repeat(3, 1fr)",
							"column-gap": "12px",
							"row-gap": "23px",
							"align-items": "stretch",
						},
					},
					mb: {
						style: {
							"grid-template-columns": "repeat(2, 1fr)",
							gap: "10px",
							"align-items": "stretch",
						},
					},
				},
				stepItemStyle: {
					style: {
						borderRadius: "6px",
						"background-color": "rgb(242, 242, 242)",
					},
					stepImgWrapStyle: {
						style: {
							borderRadius: "6px 6px 0 0",
							border: "none",
						},
					},
					stepTextWrapStyle: {
						style: {
							flex: 1,
							width: "100%",
							backgroundColor: "#f2f2f2",
							padding: "10px",
							"margin-top": "0",
							"font-size": "14px",
							borderRadius: "0 0 6px 6px",
						},
					},
				},
			},
			config.allStepConfig["Select Attachment"] = {
				pcActive2: true,
				mbActive2: true,
				mediaConfig: {
					style: {
						"aspect-ratio": "660/420",
					},
				},
				stepItemStyle: {
					style: {
						borderRadius: "6px",
						background: "#f4f5f5",
					},
					mbStepImgWrapStyle: {
						style: {
							"border-radius": "6px",
						},
						customCircle: 3,
					},
					mbStepTextWrapStyle: {
						style: {
							fontSize: "12px",
							padding: "6px 10px 12px",
							"margin-bottom": "0",
						},
					},
					stepTextWrapStyle: {
						style: {
							flex: 1,
							fontSize: "16px",
							padding: "5px 0.4em",
							"margin-top": "0",
							"margin-bottom": "10px",
						},
					},
				},
				stepContentConfig: {
					ipad: {
						style: {
							"grid-template-columns": "repeat(5, 1fr)",
							"column-gap": "10px",
							"row-gap": "27px",
						},
					},
					mediaQueries: {
						"@media screen and (min-width: 1801px)": {
							"grid-template-columns": "repeat(5, 1fr)",
							gap: "20px",
						},
						"@media screen and (max-width: 1800px)": {
							"grid-template-columns": "repeat(4, 1fr)",
							gap: "20px",
						},
					},
					mb: {
						style: {
							"grid-template-columns": "repeat(2, 1fr)",
							"column-gap": "5px",
							"row-gap": "13px",
						},
					},
				},
			},
			config.allStepConfig["Select Amount of Colors for Pin"] = {
				hideTitleSelectText: true,
				mediaConfig: {
					style: {
						"aspect-ratio": "180/170",
					},
				},
				stepItemStyle: {
					stepImgWrapStyle: {
						style: {
							background: "#f4f5f5",
						},
					},
					mbStepImgWrapStyle: {
						style: {
							"border-radius": "6px",
						},
						customCircle: 3,
					},
					mbStepTextWrapStyle: {
						style: {
							fontSize: "12px",
							padding: "5px 0.4em",
							"margin-top": "0",
						},
					},
					stepTextWrapStyle: {
						style: {
							flex: 1,
							fontSize: "16px",
							"margin-top": "16px",
							"text-align": "center",
						},
					},
				},
				stepContentConfig: {
					pc: {
						style: {
							"grid-template-columns": "repeat(6, 1fr)",
							gap: "20px",
						},
					},
					mb: {
						style: {
							"grid-template-columns": "repeat(3, 1fr)",
							"row-gap": "10px",
							"column-gap": "5px",
						},
					},
				},
			},
			config.allStepConfig["Select Attachment"] = {
				hideTitleSelectText: true,
					pcActive2: true,
					mbActive2: true,
					hasViewMore: true,
					showChildListLength:6,
					mediaConfig: {
						style: {
							"aspect-ratio": "660/420",
						},
					},
					stepItemStyle: {
						style: {
							borderRadius: "6px",
							background: "#f4f5f5",
						},
						mbStepImgWrapStyle: {
							style: {
								"border-radius": "6px",
							},
							customCircle: 3,
						},
						mbStepTextWrapStyle: {
							style: {
								fontSize: "12px",
								padding: "6px 10px 12px",
								"margin-bottom": "0",
							},
						},
						stepTextWrapStyle: {
							style: {
								flex: 1,
								fontSize: "16px",
								padding: "5px 0.4em",
								"margin-top": "0",
								"margin-bottom": "10px",
							},
						},
					},
					stepContentConfig: {
						ipad: {
							style: {
								"grid-template-columns": "repeat(5, 1fr)",
								"column-gap": "10px",
								"row-gap": "27px",
							},
						},
						mediaQueries: {
							"@media screen and (min-width: 1801px)": {
								"grid-template-columns": "repeat(5, 1fr)",
								gap: "20px",
							},
							"@media screen and (max-width: 1800px)": {
								"grid-template-columns": "repeat(4, 1fr)",
								gap: "20px",
							},
						},
						mb: {
							style: {
								"grid-template-columns": "repeat(2, 1fr)",
								"column-gap": "5px",
								"row-gap": "13px",
							},
						},
					},
			}
			break;
		//pinBadges
		case "lang-quote-custom-cut-out-pin-badges":
			config = new Quote(661, "pinBadges");
			config.allStepConfig = getPinBadgesAllStepConfig();
			break;
		case "lang-quote-custom-enamel-pin-badges":
			config = new Quote(495, "pinBadges");
			config.allStepConfig = getPinBadgesAllStepConfig();
			break;
		case "lang-quote-custom-die-struck-pin-badges":
			config = new Quote(502, "pinBadges");
			config.allStepConfig = getPinBadgesAllStepConfig();
			break;
		case "lang-quote-custom-3D-pin-badges":
			config = new Quote(543, "pinBadges");
			config.allStepConfig = getPinBadgesAllStepConfig();
			break;
		case "lang-quote-custom-uv-printed-pin-badges":
			config = new Quote(506, "pinBadges");
			config.allStepConfig = getPinBadgesAllStepConfig();
			break;
		case "lang-quote-custom-offset-pin-badges":
			config = new Quote(503, "pinBadges");
			config.allStepConfig = getPinBadgesAllStepConfig();
			break;
		case "lang-quote-custom-rush-pin-badges":
			config = new Quote(504, "pinBadges");
			config.allStepConfig = getPinBadgesAllStepConfig();
			break;
		case "lang-quote-custom-metal-patches":
			config = new Quote(573, "metal Patches");
			config.allStepConfig = {
				quoteCategory: {
					stepContentConfig: {
						pc: {
							style: {
								"grid-template-columns": "repeat(3, 1fr)",
								gap: "20px",
							},
						},
						mb: {
							style: {
								"grid-template-columns": "repeat(2, 1fr)",
								gap: "10px",
							},
						},
					},
				},
				Style: {
					stepContentConfig: {
						pc: {
							style: {
								"grid-template-columns": "repeat(3, 1fr)",
								gap: "20px",
							},
						},
						mb: {
							style: {
								"grid-template-columns": "repeat(2, 1fr)",
								gap: "10px",
							},
						},
					},
				},
				"Plating/Finish（copperl）": {
					stepContentConfig: {
						pc: {
							style: {
								"grid-template-columns": "repeat(5, 1fr)",
								gap: "20px",
							},
						},
						mb: {
							style: {
								"grid-template-columns": "repeat(2, 1fr)",
								gap: "10px",
							},
						},
					},
				},
				"Plating/Finish（Aluminum）": {
					stepContentConfig: {
						pc: {
							style: {
								"grid-template-columns": "repeat(5, 1fr)",
								gap: "20px",
							},
						},
						mb: {
							style: {
								"grid-template-columns": "repeat(2, 1fr)",
								gap: "10px",
							},
						},
					},
				},
				"Plating/Finish（stainless steel）": {
					stepContentConfig: {
						pc: {
							style: {
								"grid-template-columns": "repeat(5, 1fr)",
								gap: "20px",
							},
						},
						mb: {
							style: {
								"grid-template-columns": "repeat(2, 1fr)",
								gap: "10px",
							},
						},
					},
				},
				"Plating/Finish（废除）": {
					stepContentConfig: {
						pc: {
							style: {
								"grid-template-columns": "repeat(5, 1fr)",
								gap: "20px",
							},
						},
						mb: {
							style: {
								"grid-template-columns": "repeat(2, 1fr)",
								gap: "10px",
							},
						},
					},
				},
				"Intended Use": {
					showPriceText: false,
					imgBorderStyle: 2,
					hideTitleSelectText: true,
					//	showNextBtn: true,
					stepContentConfig: {
						pc: {
							style: {
								"grid-template-columns": "repeat(5, 1fr)",
								gap: "20px",
							},
						},
						mb: {
							style: {
								"grid-template-columns": "repeat(2, 1fr)",
								gap: "10px",
							},
						},
					},
				},
				"Metal Process": {
					stepContentConfig: {
						pc: {
							style: {
								"grid-template-columns": "repeat(3, 1fr)",
								gap: "20px",
							},
						},
						mb: {
							style: {
								"grid-template-columns": "repeat(2, 1fr)",
								gap: "10px",
							},
						},
					},
				},
				"Assembly Method": {
					stepContentConfig: {
						pc: {
							style: {
								"grid-template-columns": "repeat(4, 1fr)",
								gap: "20px",
							},
						},
						mb: {
							style: {
								"grid-template-columns": "repeat(2, 1fr)",
								gap: "10px",
							},
						},
					},
				},
				"Metal Patches Shape": {
					hasViewMore: true,
					showPriceText: false,
					showChildListLength:6,
					stepContentConfig: {
						pc: {
							style: {
								"grid-template-columns": "repeat(5, 1fr)",
								gap: "20px",
							},
						},
						mb: {
							style: {
								"grid-template-columns": "repeat(2, 1fr)",
								gap: "10px",
								"min-height": "611px",
							},
						},
					},
				},
				Material: {
					stepContentConfig: {
						pc: {
							style: {
								"grid-template-columns": "repeat(3, 1fr)",
								gap: "20px",
							},
						},
						mb: {
							style: {
								"grid-template-columns": "repeat(2, 1fr)",
								gap: "10px",
							},
						},
					},
				},
				"Plating/Finish": {
					hasViewMore: true,
					showChildListLength:6,
					stepContentConfig: {
						pc: {
							style: {
								"grid-template-columns": "repeat(5, 1fr)",
								gap: "20px",
							},
						},
						mb: {
							style: {
								"grid-template-columns": "repeat(2, 1fr)",
								gap: "10px",
								"min-height": "611px",
							},
						},
					},
				},
			};
			break;
		case "lang-quote-custom-flock-patches": {
			config = new Quote(557, "flockPatches", [
				{ value: "50", address: "50" },
				{ value: "100", address: "100" },
				{ value: "200", address: "200" },
				{ value: "300", address: "300" },
				{ value: "500", address: "500" },
				{ value: "1000", address: "1000" },
				{ value: "1500", address: "1500" },
				{ value: "2000", address: "2000" },
				{ value: "2500", address: "2500" },
			]);
			config.sizeType = "sizeSelect";
			config.allStepConfig = {
				"Tatami Fabric Color": {
					moldText: 4,
					hideTitleSelectText: true,
					priceTextCss: {
						normaltext: {
							"text-align": "left",
						},
						tipTextStyle: {
							"text-align": "left",
							"margin-left": "1.8em",
						},
					},
					stepContentConfig: {
						pc: {
							style: {
								"grid-template-columns": "repeat(4, 1fr)",
								gap: "20px",
							},
						},
						mb: {
							style: {
								"grid-template-columns": "repeat(2, 1fr)",
								gap: "5px",
							},
						},
					},
					stepItemStyle: {
						style: {
							"flex-direction": "row",
							padding: "10px",
							"border-radius": "10px",
							border: "1px solid #EFEFEF",
							"justify-content": "left",
						},
						mbStyle: {
							padding: "5px",
						},
						stepImgWrapStyle: {
							style: {
								width: "85%",
							},
						},
						mbStepImgWrapStyle: {
							style: {
								width: "55px",
								"border-radius": "5px",
							},
						},
						stepTextWrapStyle: {
							style: {
								width: "100%",
								"margin-left": "10px",
								"text-align": "center",
							},
							aliasStyle: {
								style: {
									"justify-content": "left",
								},
							},
						},
						mbStepTextWrapStyle: {
							style: {
								"font-size": "12px",
							},
							customCircleStyle: {
								style: {
									width: "16px",
									height: "16px",
								},
							},
						},
					},
				},
				"Patch Color": {
					moldText: 4,
					priceTextCss: {
						normaltext: {
							"text-align": "left",
						},
						tipTextStyle: {
							"text-align": "left",
							"margin-left": "1.8em",
						},
					},
					stepContentConfig: {
						pc: {
							style: {
								"grid-template-columns": "repeat(4, 1fr)",
								gap: "20px",
							},
						},
						mb: {
							style: {
								"grid-template-columns": "repeat(2, 1fr)",
								gap: "5px",
							},
						},
					},
					stepItemStyle: {
						style: {
							"flex-direction": "row",
							padding: "10px",
							"border-radius": "10px",
							border: "1px solid #EFEFEF",
							"justify-content": "left",
						},
						mbStyle: {
							padding: "5px",
						},
						stepImgWrapStyle: {
							style: {
								width: "85%",
							},
						},
						mbStepImgWrapStyle: {
							style: {
								width: "55px",
								"border-radius": "5px",
							},
						},
						stepTextWrapStyle: {
							style: {
								width: "100%",
								"margin-left": "10px",
								"text-align": "center",
							},
							aliasStyle: {
								style: {
									"justify-content": "left",
								},
							},
						},
						mbStepTextWrapStyle: {
							style: {
								"font-size": "12px",
							},
							customCircleStyle: {
								style: {
									width: "16px",
									height: "16px",
								},
							},
						},
					},
				},
				Size: {
					showNextBtn: true,
					smallTitle: this.$store.getters.lang.quote.sizeSmallTitle,
					heightDes: "Patch Height",
					widthDes: "Patch Width",
					hideTitleSelectText: true,
				},
				Backing: {
					hideTitleSelectText: true,
				},
				"More Options": {
					hideTitleSelectText: true,
				},
				"Upload Artwork & Comments": {
					showNextBtn: true,
					hideTitleSelectText: true,
				},
				Shape: {
					showPriceText: false,
					hideTitleSelectText: true,
					stepContentConfig: {
						pc: {
							style: {
								"grid-template-columns": "repeat(5, 1fr)",
								gap: "20px",
							},
						},
						mb: {
							style: {
								"grid-template-columns": "repeat(2, 1fr)",
								gap: "10px",
							},
						},
					},
				},
				Quantity: {
					hideTitleSelectText: true,
				},
				"Select Number of Colors": {
					showPriceText: true,
					moldText: 4,
					hideTitleSelectText: true,
					stepItemStyle: {
						style: {
							"justify-content": "space-between !important",
						},
					},
					priceTextCss: {
						pcNormaltext: {
							"flex-direction": "column",
							"text-align": "left",
							"margin-left": "5em",
						},
						mbNormaltext: {
							"flex-direction": "column",
						},

						tipTextStyle: {
							visibility: "hidden",
						},
					},
				},
				"Intended Use": {
					showPriceText: false,
					showNextBtn: true,
					hideTitleSelectText: true,
					imgBorderStyle: 2,
				},
				"Turnaround Time": {
					hideTitleSelectText: true,
				},

				"Patch Border": {
					stepContentConfig: {
						pc: {
							style: {
								"grid-template-columns": "repeat(3, 1fr)",
								gap: "20px",
							},
						},
						mb: {
							style: {
								"grid-template-columns": "repeat(2, 1fr)",
								gap: "10px",
							},
						},
					},
				},
				"Additional Upgrades": {
					smallTitle: this.$store.getters.lang.quote.prompt,
					hideTitleSelectText: true,
					showNextBtn: true,
				},
				"Purchase Intention": {
					showPriceText: false,
					imgBorderStyle: 2,
					hideTitleSelectText: true,
					showNextBtn: true,
					stepContentConfig: {
						pc: {
							style: {
								"grid-template-columns": "repeat(4, 1fr)",
								gap: "20px",
							},
						},
						ipad: {
							style: {
								"grid-template-columns": "repeat(3, 1fr)",
								gap: "20px",
							},
						},
						mb: {
							style: {
								"grid-template-columns": "repeat(2, 1fr)",
								gap: "10px",
							},
						},
					},
				},
			};
			break;
		}

		case "lang-quote-mint-coins":
			config = new Quote(409, "Coins");
			break;
		//keychains
		case "lang-quote-cut-out-keychains":
			config = new Quote(633, "keychains");
			config.allStepConfig = getKeychainsAllStepConfig();
			break;
		case "lang-quote-custom-rush-printed-keychains":
			config = new Quote(631, "keychains");
			config.allStepConfig = getKeychainsAllStepConfig();
			break;
		case "lang-quote-metal-couples-keychains":
			config = new Quote(583, "keychains");
			config.allStepConfig = getKeychainsAllStepConfig();
			break;
		case "lang-quote-custom-acrylic-couples-keychains":
			config = new Quote(581, "keychains");
			config.allStepConfig = getKeychainsAllStepConfig();
			break;
		case "lang-quote-custom-acrylic-keychains":
			config = new Quote(117, "keychains");
			config.allStepConfig = getKeychainsAllStepConfig();
			config.allStepConfig["Keychain Printed Area"] = {
				pcActive2: false,
				mbActive2: true,
				stepContentConfig: {
					pc: {
						style: {
							"grid-template-columns": "repeat(3, 1fr)",
							gap: "20px",
						},
					},
					mb: {
						style: {
							"grid-template-columns": "repeat(2, 1fr)",
							gap: "10px",
						},
					},
				},
				stepItemStyle: {
					mbStyle: {
						borderRadius: "6px",
						overflow: "hidden",
						"background-color": "#f3f3f3",
						"font-size": "13px",
						"padding-bottom": "5px",
					},
					stepTextWrapStyle: {
						mbStyle: {
							flex: 1,
							width: "100%",
							backgroundColor: "#f3f3f3",
							padding: "10px",
							borderRadius: "0 0 6px 6px",
							"font-size": "13px",
						},
					},
				},
			}
			config.allStepConfig["Acrylic Charm Color"] = {
				stepItemStyle: {
					style: {
						background: "rgb(244 245 245)",
						"border-radius": "10px",
						"padding-bottom": "5px",
					},
				},
				stepContentConfig: {
					pc: {
						style: {
							"grid-template-columns": "repeat(6, 1fr)",
							gap: "20px",
						},
					},
					mb: {
						style: {
							"grid-template-columns": "repeat(3, 1fr)",
							gap: "10px",
						},
					},
				},
			}
			config.allStepConfig["Additional Upgrades"] = {
				videoFit: "cover",
				pcActive2: true,
				mbActive2: true,
				showNextBtn: true,
				mediaConfig: {
					style: {
						"aspect-ratio": "384/270",
						"object-fit": "cover",
					},
				},
				stepContentConfig: {
					pc: {
						style: {
							"grid-template-columns": "repeat(3, 1fr)",
							gap: "20px",
						},
					},
					mb: {
						style: {
							"grid-template-columns": "repeat(2, 1fr)",
							gap: "10px",
							"align-items": "stretch",
						},
					},
				},
				stepItemStyle: {
					style: {
						borderRadius: "6px",
						overflow: "hidden",
						"background-color": "#f3f3f3",
					},
					stepTextWrapStyle: {
						style: {
							flex: 1,
							width: "100%",
							backgroundColor: "#f3f3f3",
							padding: "10px 0 5px",
							"margin-top": "0",
							borderRadius: "0 0 6px 6px",
						},
					},
				},
			}
			break;
		case "lang-quote-custom-embroidered-keychains":
			config = new Quote(219, "embroidered keychains");
			config.allStepConfig = getKeychainsAllStepConfig();
			config.sizeType = "sizeSelect";
			config.allStepConfig["quoteCategory"] = {
				pcActive2: true,
				mbActive2: true,
				stepContentConfig: {
					pc: {
						style: {
							"grid-template-columns": "repeat(4, 1fr)",
							gap: "20px",
						},
					},
					mb: {
						style: {
							"grid-template-columns": "repeat(2, 1fr)",
							gap: "10px",
						},
					},
				},
				stepItemStyle: {
					style: {
						borderRadius: "6px",
						overflow: "hidden",
						"background-color": "rgb(242, 242, 242)",
					},
					stepImgWrapStyle: {
						style: {
							borderRadius: "6px 6px 0 0",
							border: "none",
						},
					},
					stepTextWrapStyle: {
						style: {
							flex: 1,
							width: "100%",
							backgroundColor: "#f2f2f2",
							padding: "10px 5px 10px 10px",
							borderRadius: "0 0 6px 6px",
						},
					},
				},
			}
			config.allStepConfig['Additional Upgrades'] ={
				showNextBtn: true,
				stepContentConfig: {
					pc: {
						style: {
							"grid-template-columns": "repeat(4, 1fr)",
							gap: "20px",
						},
					},
					mb: {
						style: {
							"grid-template-columns": "repeat(2, 1fr)",
							gap: "10px",
						},
					},
				},
			};
			config.allStepConfig['Link & Chain Options'] ={
				stepContentConfig: {
					pc: {
						style: {
							"grid-template-columns": "repeat(5, 1fr)",
							gap: "20px",
						},
					},
					mb: {
						style: {
							"grid-template-columns": "repeat(2, 1fr)",
							gap: "10px",
						},
					},
				},
				stepItemStyle: {
					pcStepTextWrapStyle: {
						style: {
							"font-size": "16px",
						},
					},
					mbStepTextWrapStyle: {
						style: {
							"font-size": "14px",
						},
					},
				},
			};
			break;
		case "lang-quote-custom-metal-keychains":
			config = new Quote(112, "metal keychains");
			config.allStepConfig = getKeychainsAllStepConfig();
			config.allStepConfig["Keychain Color"] = {
				stepContentConfig: {
					pc: {
						style: {
							"grid-template-columns": "repeat(6, 1fr)",
							gap: "20px",
						},
					},
					mb: {
						style: {
							"grid-template-columns": "repeat(3, 1fr)",
							gap: "10px",
						},
					},
				},
			};
			config.allStepConfig["Additional Upgrades"] = {
				pcActive2: true,
				mbActive2: true,
				showNextBtn: true,
				hasViewMore: true,
				showChildListLength:6,
				mediaConfig: {
					style: {
						"aspect-ratio": "700/604",
						"object-fit": "cover"
					},
				},
				stepContentConfig: {
					pc: {
						style: {
							"grid-template-columns": "repeat(4, 1fr)",
							gap: "20px",
							"align-items": "stretch"
						},
					},
					mb: {
						style: {
							"grid-template-columns": "repeat(2, 1fr)",
							gap: "10px",
							"align-items": "stretch"
						},
					},
				},
				stepItemStyle: {
					pcStyle: {
						borderRadius: "6px",
						overflow: "hidden",
						"background-color": "#f2f2f2",
						"padding-bottom":"10px",
					},
					mbStyle: {
						borderRadius: "6px",
						overflow: "hidden",
						"background-color": "#f2f2f2",
					},
					mbStepTextWrapStyle: {
						style: {
							flex: 1,
							width: "100%",
							backgroundColor: "#f2f2f2",
							padding: "10px 0 5px",
							"margin-top": "0",
							borderRadius: "0 0 6px 6px",
						},
					},
				},
				priceTextCss: {
					normaltext: {
						"display": "block",
					},
				},
			};
			config.allStepConfig["Select Link & Chain Options"] = {
				pcActive2: true,
				mbActive2: true,
				stepContentConfig: {
					pc: {
						style: {
							"grid-template-columns": "repeat(5, 1fr)",
							gap: "20px",
						},
					},
					mb: {
						style: {
							"grid-template-columns": "repeat(2, 1fr)",
							gap: "10px",
						},
					},
				},
				stepItemStyle: {
					style: {
						borderRadius: "6px",
						overflow: "hidden",
						"background-color": "#f4f5f5",
					},
					stepImgWrapStyle: {
						style: {
							borderRadius: "10px",
						},
					},
					stepTextWrapStyle: {
						style: {
							flex: 1,
							width: "100%",
							backgroundColor: "#f4f5f5",
							padding: "10px 0",
							"margin-top": "0",
							borderRadius: "0 0 6px 6px",
						},
						aliasStyle:{
							style:{
								"font-size": "16px",
							}
						}
					},
				},
			};
			config.allStepConfig["Design Areas"] = {
				pcActive2: false,
				mbActive2: true,
				stepContentConfig: {
					pc: {
						style: {
							"grid-template-columns": "repeat(3, 1fr)",
							gap: "20px",
							"align-items": "flex-start"
						},
					},
					mb: {
						style: {
							"grid-template-columns": "repeat(2, 1fr)",
							gap: "10px",
							"align-items": "flex-start"
						},
					},
				},
				stepItemStyle: {
					mbStyle: {
						borderRadius: "6px",
						overflow: "hidden",
						"background-color": "#f4f5f5",
					},
					mbStepTextWrapStyle: {
						style: {
							flex: 1,
							width: "100%",
							backgroundColor: "#f4f5f5",
							padding: "10px 0px 3px",
							"margin-top": "0",
							borderRadius: "0 0 6px 6px",
						},
					},
				},
			};
			config.allStepConfig["Plating"] = {
				pcActive2: false,
				mbActive2: true,
				stepContentConfig: {
					pc: {
						style: {
							"grid-template-columns": "repeat(6, 1fr)",
							gap: "20px",
						},
					},
					mb: {
						style: {
							"grid-template-columns": "repeat(2, 1fr)",
							gap: "10px",
						},
					},
				},
				stepItemStyle: {
					mbStyle: {
						borderRadius: "6px",
						overflow: "hidden",
						"background-color": "#f4f5f5",
					},
					mbStepTextWrapStyle: {
						style: {
							flex: 1,
							width: "100%",
							backgroundColor: "#f4f5f5",
							padding: "10px 0px 3px",
							"margin-top": "0",
							borderRadius: "0 0 6px 6px",
						},
					},
				},
			};
			config.allStepConfig["Backstamp Options"] = {
				pcActive2: true,
				mbActive2: true,
				stepContentConfig: {
					pc: {
						style: {
							"grid-template-columns": "repeat(3, 1fr)",
							gap: "20px",
						},
					},
					mb: {
						style: {
							"grid-template-columns": "repeat(2, 1fr)",
							gap: "10px",
						},
					},
				},
				stepItemStyle: {
					style: {
						borderRadius: "6px",
						overflow: "hidden",
						"background-color": "#f4f5f5",
					},
					stepImgWrapStyle: {
						style: {
							borderRadius: "10px",
						},
					},
					stepTextWrapStyle: {
						style: {
							flex: 1,
							width: "100%",
							backgroundColor: "#f4f5f5",
							padding: "10px 0",
							"margin-top": "0",
							borderRadius: "0 0 6px 6px",
						},
					},
				},
			};
			break;
		case "lang-quote-custom-metal-printing-keychains":
			config = new Quote(399, "keychains");
			config.allStepConfig = getKeychainsAllStepConfig();
			config.allStepConfig["Plating"] = {
				pcActive2: false,
				mbActive2: true,
				stepContentConfig: {
					pc: {
						style: {
							"grid-template-columns": "repeat(6, 1fr)",
							gap: "20px",
						},
					},
					mb: {
						style: {
							"grid-template-columns": "repeat(2, 1fr)",
							gap: "10px",
						},
					},
				},
				stepItemStyle: {
					mbStyle: {
						borderRadius: "6px",
						overflow: "hidden",
						"background-color": "#f4f5f5",
					},
					mbStepTextWrapStyle: {
						style: {
							flex: 1,
							width: "100%",
							backgroundColor: "#f4f5f5",
							padding: "10px 0px 3px",
							"margin-top": "0",
							"font-size": "13px",
							borderRadius: "0 0 6px 6px",
						},
					},
				},
			};
			config.allStepConfig["Design Areas"] = {
				pcActive2: false,
				mbActive2: true,
				stepContentConfig: {
					pc: {
						style: {
							"grid-template-columns": "repeat(3, 1fr)",
							gap: "20px",
						},
					},
					mb: {
						style: {
							"grid-template-columns": "repeat(2, 1fr)",
							gap: "10px",
						},
					},
				},
				stepItemStyle: {
					mbStyle: {
						borderRadius: "10px",
						overflow: "hidden",
						"background-color": "#f4f5f5",
					},
					mbStepTextWrapStyle: {
						style: {
							flex: 1,
							width: "100%",
							backgroundColor: "#f4f5f5",
							padding: "10px 0px 5px",
							"margin-top": "0",
							"font-size": "13px",
							borderRadius: "0 0 6px 6px",
						},
					},
				},
			};
			config.allStepConfig["Backstamp Options"] = {
				pcActive2: true,
				mbActive2: true,
				stepContentConfig: {
					pc: {
						style: {
							"grid-template-columns": "repeat(3, 1fr)",
							gap: "20px",
						},
					},
					mb: {
						style: {
							"grid-template-columns": "repeat(2, 1fr)",
							gap: "10px",
						},
					},
				},
				stepItemStyle: {
					style: {
						borderRadius: "6px",
						overflow: "hidden",
						"background-color": "#f4f5f5",
					},
					stepImgWrapStyle: {
						style: {
							borderRadius: "10px",
						},
					},
					stepTextWrapStyle: {
						style: {
							flex: 1,
							width: "100%",
							backgroundColor: "#f4f5f5",
							padding: "10px 0",
							"margin-top": "0",
							borderRadius: "0 0 6px 6px",
						},
					},
				},
			};
			break;
		case "lang-quote-custom-pvc-keychains":
			config = new Quote(118, "keychains");
			config.allStepConfig = getKeychainsAllStepConfig();
			config.allStepConfig["Keychain Printed Area"] = {
				pcActive2: false,
				mbActive2: true,
				stepContentConfig: {
					pc: {
						style: {
							"grid-template-columns": "repeat(3, 1fr)",
							gap: "20px",
						},
					},
					mb: {
						style: {
							"grid-template-columns": "repeat(2, 1fr)",
							gap: "10px",
						},
					},
				},
				stepItemStyle: {
					mbStyle: {
						borderRadius: "6px",
						overflow: "hidden",
						"background-color": "rgb(242, 242, 242)",
						"font-size": "13px",
						"padding-bottom": "5px",
					},
					stepTextWrapStyle: {
						mbStyle: {
							flex: 1,
							width: "100%",
							backgroundColor: "#f2f2f2",
							padding: "10px",
							borderRadius: "0 0 6px 6px",
							"font-size": "13px",
						},
					},
				},
			};
			break;

		// ball Marker报价
		case "lang-quote-custom-ball-marker":
			config = new Quote(561, "ballMarker");
			config.h1Text = this.$store.getters.lang.quote.ballMarker.h1;
			config.p1Text = this.$store.getters.lang.quote.ballMarker.p1;
			config.stepSizeTitle = this.$store.getters.lang.quote.ballMarker.stepSizeTitle;
			config.allStepConfig = getBallMarkerAllStepConfig();
			break;
		case "lang-quote-custom-ball-marker-with-hat-clips":
			config = new Quote(638, "ballMarker");
			config.h1Text = this.$store.getters.lang.quote.ballMarker.h1;
			config.p1Text = this.$store.getters.lang.quote.ballMarker.p1;
			config.stepSizeTitle = this.$store.getters.lang.quote.ballMarker.stepSizeTitle;
			config.allStepConfig = getBallMarkerAllStepConfig();
			break;
		case "lang-quote-custom-magnetic-ball-markers":
			config = new Quote(644, "ballMarker");
			config.h1Text = this.$store.getters.lang.quote.ballMarker.h1;
			config.p1Text = this.$store.getters.lang.quote.ballMarker.p1;
			config.stepSizeTitle = this.$store.getters.lang.quote.ballMarker.stepSizeTitle;
			config.allStepConfig = getBallMarkerAllStepConfig();
			break;
		case "lang-quote-custom-divot-tools":
			config = new Quote(650, "ballMarker");
			config.h1Text = this.$store.getters.lang.quote.divottools.h1;
			config.p1Text = this.$store.getters.lang.quote.divottools.p1;
			config.stepSizeTitle = this.$store.getters.lang.quote.ballMarker.stepSizeTitle;
			config.allStepConfig = getBallMarkerAllStepConfig();
			break;
		case "lang-quote-two-tone-lanyards":
			config = new Quote(619, "lanyard");
			config.noDesignTab = true;
			config.lanyardType = "twoToneLanyards";
			config.allStepConfig = getLanyardAllStepConfig();
			break;
		case "lang-quote-light-up-lanyards":
			config = new Quote(635, "lanyard");
			config.noDesignTab = true;
			config.noCustomTab = true;
			config.lanyardType = "lightUpLanyards";
			config.allStepConfig = getLanyardAllStepConfig();
			break;
		case "lang-quote-glitter-lanyards":
			config = new Quote(629, "lanyard");
			config.noDesignTab = true;
			config.noCustomTab = false;
			config.lanyardType = 'glitterLanyards';
			config.freeText =  this.$store.getters.lang.quote.StandardFREE;
			config.allStepConfig = getLanyardAllStepConfig();
			break;
		case "lang-quote-3d-silicone-printing-lanyards":
			config = new Quote(700, "lanyard");
			config.noDesignTab = true;
			config.noCustomTab = false;
			config.allStepConfig = getLanyardAllStepConfig();
			break;
		case "lang-quote-dye-sublimated-lanyards":
			config = new Quote(701, "lanyard");
			config.noDesignTab = false;
			config.noCustomTab = false;
			config.allStepConfig = getLanyardAllStepConfig();
			break;
		case "lang-quote-nylon-lanyards":
			config = new Quote(702, "lanyard");
			config.noDesignTab = true;
			config.noCustomTab = false;
			config.allStepConfig = getLanyardAllStepConfig();
			break;
		case "lang-quote-tubular-lanyards":
			config = new Quote(703, "lanyard");
			config.noDesignTab = true;
			config.noCustomTab = false;
			config.allStepConfig = getLanyardAllStepConfig();
			break;
		case "lang-quote-polyester-lanyards":
			config = new Quote(704, "lanyard");
			config.noDesignTab = true;
			config.noCustomTab = false;
			config.allStepConfig = getLanyardAllStepConfig();
			break;
		case "lang-quote-woven-lanyards":
			config = new Quote(705, "lanyard");
			config.noDesignTab = true;
			config.noCustomTab = false;
			config.allStepConfig = getLanyardAllStepConfig();
			break;
		case "lang-quote-cord-lanyards":
			config = new Quote(597, "lanyard");
			config.noDesignTab = true;
			config.noCustomTab = false;
			config.allStepConfig = getLanyardAllStepConfig();
			break;
		case "lang-quote-reflective-lanyards":
			config = new Quote(621, "lanyard");
			config.noDesignTab = true;
			config.noCustomTab = false;
			config.lanyardType = "reflectiveLanyards"
			config.allStepConfig = getLanyardAllStepConfig();
			break;
		case "lang-quote-neoprene-lanyards":
			config = new Quote(602, "lanyard");
			config.noDesignTab = true;
			config.noCustomTab = false;
			config.lanyardType = "neopreneLanyards";
			config.allStepConfig = getLanyardAllStepConfig();
			break;
		case "lang-quote-phone-lanyards":
			config = new Quote(747, "lanyard");
			config.noDesignTab = true;
			config.noCustomTab = false;
			config.lanyardType = "phoneLanyards";
			config.allStepConfig = getLanyardAllStepConfig();
			break;
		case "lang-quote-short-wrist-lanyards":
			config = new Quote(689, "lanyard");
			config.noDesignTab = true;
			config.noCustomTab = false;
			config.lanyardType = "shortWristLanyards";
			config.allStepConfig = getLanyardAllStepConfig();
			break;
		case "lang-quote-blank-lanyards":
			config = new Quote(695, "lanyard");
			config.noDesignTab = true;
			config.noCustomTab = false;
			config.lanyardType = "blankLanyards";
			config.allStepConfig = getLanyardAllStepConfig();
			break;
		//咬板报价
		case "lang-quote-custom-stainless-steel-ornaments":
			config = new Quote(599, "ornaments");
			config.leftImg = [
				{
					id: 1,
					url: "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20241229/ueditor/97/custom-ornaments.jpg",
					alt: this.$store.getters.lang.quote.ornament.customOrnamentsImg,
				},
				{
					id: 2,
					url: "https://static-oss.gs-souvenir.com/web/quoteManage/20250110/Custom_Stainless_Steel_Ornaments_with_Etched_Process_20250110pwbGMH.jpg",
					alt: this.$store.getters.lang.quote.ornament.ornaments,
				},
				{
					id: 3,
					url: "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20241229/ueditor/97/custom-christmas-ornaments.jpg",
					alt: this.$store.getters.lang.quote.ornament.customChristmasOrnaments,
				},
				{
					id: 4,
					url: "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20241229/ueditor/97/stainless-steel-ornaments.jpg",
					alt: this.$store.getters.lang.quote.ornament.stainlessSteelOrnaments,
				},
				{
					id: 5,
					url: "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20241229/ueditor/97/stainless-steel-ornaments-with-silkscreen-process.png",
					alt: this.$store.getters.lang.quote.ornament.silkscreenProcess,
				},
			];
			config.descriptionText = this.$store.getters.lang.quote.ornament.ornamentDescriptionText;
			config.descriptionText2 = this.$store.getters.lang.quote.ornament.ornamentDescriptionText2;
			config.descriptionText3 = "";
			config.h1 = this.$store.getters.lang.quote.ornament.stainlessSteelOrnamentsH1;
			config.allStepConfig = getOrnamentAllStepConfig();
			break;
		case "lang-quote-custom-gold-brass-ornaments":
			config = new Quote(600, "ornaments");
			config.leftImg = [
				{
					id: 1,
					url: "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20241226/ueditor/97/gold-brass-ornaments-with-silkscreen-process.jpg",
					alt: this.$store.getters.lang.quote.ornament.goldSilkscreenProcess,
				},
				{
					id: 2,
					url: "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20241226/ueditor/97/custom-ornaments.jpg",
					alt: this.$store.getters.lang.quote.ornament.customOrnamentsImg,
				},
				{
					id: 3,
					url: "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20241226/ueditor/97/custom-christmas-ornaments.jpg",
					alt: this.$store.getters.lang.quote.ornament.customChristmasOrnaments,
				},
				{
					id: 4,
					url: "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20241229/ueditor/97/gold-brass-ornaments.jpg",
					alt: this.$store.getters.lang.quote.ornament.goldBrassOrnaments,
				},
				{
					id: 5,
					url: "https://static-oss.gs-souvenir.com/web/quoteManage/20250110/Gold_Brass_Ornaments_with_Debossed_Process_202501102ZScpP.png",
					alt: this.$store.getters.lang.quote.ornament.goldDebossedProcess,
				},
			];
			config.descriptionText = this.$store.getters.lang.quote.ornament.ornamentDescriptionText3;
			config.descriptionText2 = this.$store.getters.lang.quote.ornament.ornamentDescriptionText4;
			config.descriptionText3 = "";
			config.h1 = this.$store.getters.lang.quote.ornament.goldBrassOrnamentsH1;
			config.allStepConfig = getOrnamentAllStepConfig();
			break;
		case "lang-quote-custom-zinc-die-casting-ornaments":
			config = new Quote(601, "ornaments");
			config.leftImg = [
				{
					id: 1,
					url: "https://static-oss.gs-souvenir.com/web/quoteManage/20250106/Personalized_Zinc_Die_Casting_Ornaments_20250106khMxAe.jpg",
					alt: this.$store.getters.lang.quote.ornament.personalizedOrnaments,
				},
				{
					id: 2,
					url: "https://static-oss.gs-souvenir.com/web/quoteManage/20250110/Christmas_Zinc_Die_Casting_Ornaments_20250110Zkp7YJ.jpg",
					alt: this.$store.getters.lang.quote.ornament.christmasOrnaments,
				},
				{
					id: 3,
					url: "https://static-oss.gs-souvenir.com/web/quoteManage/20250106/Shop_Zinc_Die_Casting_Ornaments_20250106aHrzJM.jpg",
					alt: this.$store.getters.lang.quote.ornament.shopOrnaments,
				},
				{
					id: 4,
					url: "https://static-oss.gs-souvenir.com/web/quoteManage/20250106/Custom_Zinc_Die_Casting_Ornaments_20250106ihdiDX.jpg",
					alt: this.$store.getters.lang.quote.ornament.customZincOrnaments,
				},
				{
					id: 5,
					url: "https://static-oss.gs-souvenir.com/web/quoteManage/20250106/zinc-die-casting-ornaments-5_2025010634EKJY.jpg",
					alt: this.$store.getters.lang.quote.ornament.customZincOrnaments,
				},
			];
			config.descriptionText = this.$store.getters.lang.quote.ornament.ornamentDescriptionText5;
			config.descriptionText2 = this.$store.getters.lang.quote.ornament.ornamentDescriptionText6;
			config.descriptionText3 = "";
			config.descriptionText4 = this.$store.getters.lang.quote.ornament.ornamentDescriptionText17;
			config.descriptionText5 = this.$store.getters.lang.quote.ornament.ornamentDescriptionText18;
			config.descriptionText6 = this.$store.getters.lang.quote.ornament.ornamentDescriptionText19;

			config.h1 = this.$store.getters.lang.quote.ornament.ZincDieCastingOrnamentsH1;
			config.allStepConfig = getOrnamentAllStepConfig();
			break;
		case "lang-quote-custom-spinning-ornaments":
			config = new Quote(659, "ornaments");
			config.leftImg = [
				{
					id: 1,
					url: "https://static-oss.gs-souvenir.com/web/quoteManage/20250110/custom_spinning_ornaments1_20250110chXyi4.jpg",
					alt: this.$store.getters.lang.quote.ornament.customspinningornaments,
				},
				{
					id: 2,
					url: "https://static-oss.gs-souvenir.com/web/quoteManage/20250108/Christmas_ornaments1_2025010844a6Gz.png",
					alt: this.$store.getters.lang.quote.ornament.spinningchristmasornaments,
				},
				{
					id: 3,
					url: "https://static-oss.gs-souvenir.com/web/quoteManage/20250108/spinning_ornaments_with_logo1_20250108JrFzpr.png",
					alt: this.$store.getters.lang.quote.ornament.spinningornamentswithlogo,
				},
				{
					id: 4,
					url: "https://static-oss.gs-souvenir.com/web/quoteManage/20250116/spinning_ornaments_20250116XiHFA6.png",
					alt: this.$store.getters.lang.quote.ornament.spinningornaments,
				},
				{
					id: 5,
					url: "https://static-oss.gs-souvenir.com/web/quoteManage/20250108/spinning_Xmas_ornaments_202501088fXpAc.png",
					alt: this.$store.getters.lang.quote.ornament.spinningXmasornaments,
				},
			];
			config.descriptionText = this.$store.getters.lang.quote.ornament.ornamentDescriptionText7;
			config.descriptionText2 = this.$store.getters.lang.quote.ornament.ornamentDescriptionText8;
			config.descriptionText3 = this.$store.getters.lang.quote.ornament.ornamentDescriptionText9;
			config.h1 = this.$store.getters.lang.quote.ornament.SpinningOrnamentsH1;
			config.allStepConfig = getOrnamentAllStepConfig();
			break;
		case "lang-quote-custom-acrylic-ornaments":
			config = new Quote(656, "ornaments");
			config.leftImg = [
				{
					id: 1,
					url: "https://static-oss.gs-souvenir.com/web/quoteManage/20250110/Personalized_Acrylic_Ornaments_20250110Hz8Swt.jpg",
					alt: this.$store.getters.lang.quote.ornament.personalizedAcrylicOrnaments,
				},
				{
					id: 2,
					url: "https://static-oss.gs-souvenir.com/web/quoteManage/20250109/Acrylic_Ornaments_Custom_20250109kZKQam.jpg",
					alt: this.$store.getters.lang.quote.ornament.acrylicOrnamentsCustom,
				},
				{
					id: 3,
					url: "https://static-oss.gs-souvenir.com/web/quoteManage/20250110/Acrylic_Ornaments_Personalized_20250110eZDPaY.jpg",
					alt: this.$store.getters.lang.quote.ornament.acrylicOrnamentsPersonalized,
				},
				{
					id: 4,
					url: "https://static-oss.gs-souvenir.com/web/quoteManage/20250109/Acrylic_Ornaments_Near_Me_20250109PbEzkm.jpg",
					alt: this.$store.getters.lang.quote.ornament.acrylicOrnamentsNearMe,
				},
				{
					id: 5,
					url: "https://static-oss.gs-souvenir.com/web/quoteManage/20250109/Acrylic_Christmas_Ornaments_20250109XHEmfz.jpg",
					alt: this.$store.getters.lang.quote.ornament.acrylicChristmasOrnaments,
				},
			];
			config.descriptionText = this.$store.getters.lang.quote.ornament.ornamentDescriptionText10;
			config.descriptionText2 = this.$store.getters.lang.quote.ornament.ornamentDescriptionText11;
			config.descriptionText3 = this.$store.getters.lang.quote.ornament.ornamentDescriptionText12;
			config.h1 = this.$store.getters.lang.quote.ornament.acrylicOrnamentsH1;
			config.allStepConfig = getOrnamentAllStepConfig();
			break;
		case "lang-quote-printed-wristbands":
			config = new Quote(749, "wristband");
			config.allStepConfig = getWristbandAllStepConfig();
			break;
		case "lang-quote-custom-laser-cut-ornaments":
			config = new Quote(625, "ornaments");
			config.leftImg = [
				{
					id: 1,
					url: "https://static-oss.gs-souvenir.com/web/quoteManage/20250122/Love_Laser_Cut_Ornaments_20250122cbx3eb.jpg",
					alt: this.$store.getters.lang.quote.ornament.loveLaserCutOrnaments,
				},
				{
					id: 2,
					url: "https://static-oss.gs-souvenir.com/web/quoteManage/20250122/Christmas_Bow_20250122QxJfBm.jpg",
					alt: this.$store.getters.lang.quote.ornament.christmasBow,
				},
				{
					id: 3,
					url: "https://static-oss.gs-souvenir.com/web/quoteManage/20250122/Love_Couple_Laser_Cut_Ornaments_20250122NaTPdE.jpg",
					alt: this.$store.getters.lang.quote.ornament.loveCoupleLaserCutOrnaments,
				},
				{
					id: 4,
					url: "https://static-oss.gs-souvenir.com/web/quoteManage/20250122/Dog_Laser_Cut_Ornaments_20250122Jnbspm.jpg",
					alt: this.$store.getters.lang.quote.ornament.dogLaserCutOrnaments,
				},
				{
					id: 5,
					url: "https://static-oss.gs-souvenir.com/web/quoteManage/20250122/Flower_Laser_Cut_Ornaments_20250122ineZ8c.jpg",
					alt: this.$store.getters.lang.quote.ornament.flowerLaserCutOrnaments,
				},
			];
			config.descriptionText = this.$store.getters.lang.quote.ornament.ornamentDescriptionText1;
			config.descriptionText2 = this.$store.getters.lang.quote.ornament.ornamentDescriptionText13;
			config.descriptionText3 = "";
			config.descriptionText4 = this.$store.getters.lang.quote.ornament.ornamentDescriptionText14;
			config.descriptionText5 = this.$store.getters.lang.quote.ornament.ornamentDescriptionText15;
			config.descriptionText6 = this.$store.getters.lang.quote.ornament.ornamentDescriptionText16;
			config.h1 = this.$store.getters.lang.quote.ornament.laserCutOrnamentsH1;
			config.allStepConfig = {
				quoteCategory: {
					videoFit: "cover",
					pcActive2: false,
					mbActive2: false,
					mediaConfig: {
						style: {
							"aspect-ratio": "240 / 210",
						},
					},
					stepContentConfig: {
						pc: {
							style: {
								"grid-template-columns": "repeat(3, 1fr)",
								"row-gap": "20px",
								"column-gap": "14px",
							},
						},
						ipad: {
							style: {
								"grid-template-columns": "repeat(3, 1fr)",
								"row-gap": "20px",
								"column-gap": "14px",
							},
						},
						mb: {
							style: {
								"grid-template-columns": "repeat(2, 1fr)",
								gap: "10px",
							},
						},
					},
					stepItemStyle: {
						style: {
							"flex-direction": "column",
							borderRadius: "6px",
							overflow: "hidden",
							"background-color": "#FAFAFA",
						},
						stepImgWrapStyle: {
							style: {
								borderRadius: "10px",
							},
						},
						stepTextWrapStyle: {
							style: {
								flex: 1,
								width: "100%",
								backgroundColor: "#FAFAFA",
								padding: "10px",
								"margin-top": "0",
								"font-size": "14px",
								borderRadius: "0 0 6px 6px",
							},
						},
					},
				},
				"Ornament Process": {
					smallTitle: "You can select more than one option.",
					stepContentConfig: {
						pc: {
							style: {
								"grid-template-columns": "repeat(3, 1fr)",
								gap: "20px",
							},
						},
						ipad: {
							style: {
								"grid-template-columns": "repeat(3, 1fr)",
								gap: "20px",
							},
						},
						mb: {
							style: {
								"grid-template-columns": "repeat(2, 1fr)",
								gap: "10px",
							},
						},
					},
				},
				"Ornament Attachment": {
					stepContentConfig: {
						pc: {
							style: {
								"grid-template-columns": "repeat(3, 1fr)",
								gap: "20px",
							},
						},
						ipad: {
							style: {
								"grid-template-columns": "repeat(3, 1fr)",
								gap: "20px",
							},
						},
						mb: {
							style: {
								"grid-template-columns": "repeat(3, 1fr)",
								gap: "10px",
							},
						},
					},
					zoomIconConfig: {
						showZoomIcon: true,
						style: {
							color: "#666666",
						},
					},
					showPreviewImg: true, //场景图预览
				},
				"Ornament Package": {
					pcActive2: false,
					mbActive2: true,
					stepContentConfig: {
						pc: {
							style: {
								"grid-template-columns": "repeat(3, 1fr)",
								gap: "20px",
							},
						},
						ipad: {
							style: {
								"grid-template-columns": "repeat(3, 1fr)",
								gap: "20px",
							},
						},
						mb: {
							style: {
								"grid-template-columns": "repeat(2, 1fr)",
								gap: "10px",
							},
						},
					},
				},
			};
			break;
		//袖扣
		case "lang-quote-custom-3d-cufflinks":
			config = new Quote(761, "custom 3d cufflinks");
			config.allStepConfig = getCufflinksAllStepConfig.call(this);
			break;
		case "lang-quote-custom-engraved-cufflinks":
			config = new Quote(763, "custom engraved cufflinks");
			config.allStepConfig = getCufflinksAllStepConfig.call(this);
			break;
		case "lang-quote-custom-picture-cufflinks":
			config = new Quote(764, "custom picture cufflinks");
			config.allStepConfig = getCufflinksAllStepConfig.call(this);
			break;
		case "lang-quote-custom-cutout-cufflinks":
			config = new Quote(762, "custom cutout cufflinks");
			config.allStepConfig = getCufflinksAllStepConfig.call(this);
			break;
		case "lang-quote-custom-soft-enamel-cufflinks":
			config = new Quote(759, "custom soft enamel cufflinks");
			config.allStepConfig = getCufflinksAllStepConfig.call(this);
			break;
		case "lang-quote-custom-hard-enamel-cufflinks":
			config = new Quote(760, "custom hard enamel cufflinks");
			config.allStepConfig = getCufflinksAllStepConfig.call(this);
			break;
		//指尖陀螺
		case "lang-quote-3d-fidget-spinner-quote":
			config = new Quote(231, "3d-fidget-spinner-quote");
			config.stepSizeTitle = this.$store.getters.lang.quote.fidgetSpinner.stepSizeTitle;
			config.p1Text = this.$store.getters.lang.quote.fidgetSpinner.p1;
			config.allStepConfig = getFidgetSpinnerAllStepConfig.call(this);
			break;
		case "lang-quote-die-struck-fidget-spinner-quote":
			config = new Quote(229, "die-struck-fidget-spinner-quote");
			config.stepSizeTitle = this.$store.getters.lang.quote.fidgetSpinner.stepSizeTitle;
			config.p1Text = this.$store.getters.lang.quote.fidgetSpinner.p1;
			config.allStepConfig = getFidgetSpinnerAllStepConfig.call(this);
			break;
		case "lang-quote-enamel-fidget-spinner-quote":
			config = new Quote(227, "enamel-fidget-spinner-quote");
			config.stepSizeTitle = this.$store.getters.lang.quote.fidgetSpinner.stepSizeTitle;
			config.p1Text = this.$store.getters.lang.quote.fidgetSpinner.p1;
			config.allStepConfig = getFidgetSpinnerAllStepConfig.call(this);
			break;
		case "lang-quote-glow-in-the-dark-fidget-spinner-quote":
			config = new Quote(233, "glow-in-the-dark-fidget-spinner-quote");
			config.stepSizeTitle = this.$store.getters.lang.quote.fidgetSpinner.stepSizeTitle;
			config.p1Text = this.$store.getters.lang.quote.fidgetSpinner.p1;
			config.allStepConfig = getFidgetSpinnerAllStepConfig.call(this);
			break;
		case "lang-quote-rhinestone-fidget-spinner-quote":
			config = new Quote(235, "rhinestone-fidget-spinner-quote");
			config.stepSizeTitle = this.$store.getters.lang.quote.fidgetSpinner.stepSizeTitle;
			config.p1Text = this.$store.getters.lang.quote.fidgetSpinner.p1;
			config.allStepConfig = getFidgetSpinnerAllStepConfig.call(this);
			break;
	}
	config.sizeType = config?.sizeType || "normal";
	return config;
};
