import {getPaymentConfig} from "~/api/web"
import axios from "axios";

// call the create order method
export async function createOrder(data,accessToken,base) {
    const url = `${base}/v2/checkout/orders`;
    const response = await axios.post(url,data,{
		headers: {
			'Content-Type': 'application/json',
			'Authorization': accessToken
		}
	})
    return response;
}

// capture payment for an order
export async function capturePayment(orderId,accessToken,base) {
    const url = `${base}/v2/checkout/orders/${orderId}/capture`;
    const response = await axios.post(url,{},{
		headers:{
			"Content-Type": "application/json",
			Authorization: accessToken,
		}
	})
    return response;
}

// generate access token
export async function generateAccessToken(data) {
	return await getPaymentConfig(data)
}

// generate client token
export async function generateClientToken(accessToken,base) {
    const response = await axios.post(`${base}/v1/identity/generate-token`, {}, {
		headers: {
			'Content-Type': 'application/json',
			'Authorization': accessToken
		}
	})
    return response.data.client_token;
}
