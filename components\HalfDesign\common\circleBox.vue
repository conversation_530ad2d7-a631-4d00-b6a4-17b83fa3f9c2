<template>
	<div class="circleBox">
		<div class="circle" :class="{
			active: active,
		}">

		</div>
		<slot></slot>
	</div>
</template>

<script>
export default {
	name: 'circleBox',
	props: {
		active: {
			type: Boolean,
			default: false
		}
	},
	components: {},
	data() {
		return {

		}
	},
	watch: {},
	computed: {},
	methods: {},
	created() { },
	mounted() { },
}
</script>

<style scoped lang="scss">
.circleBox {
	width: fit-content;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 8px;
}

.circle {
	display: flex;
	justify-content: center;
	align-items: center;
	border-color: $color-primary;
	flex-shrink: 0;
	width: 18px;
	height: 18px;
	border: 1px solid #afb1b3;
	border-radius: 50%;
	background-color: #fff;

	&::after {
		background-color: #ffffff;
		content: "";
		width: 6px;
		height: 6px;
		background: #d4d7d9;
		border-radius: 50%;
	}

	@include respond-to(mb) {
		width: 15px;
		height: 15px;

		&::after {
			width: 5px;
			height: 5px;
		}
	}

	&.active {
		border-color: $color-primary !important;

		&::after {
			background-color: $color-primary !important;
		}
	}
}
</style>
