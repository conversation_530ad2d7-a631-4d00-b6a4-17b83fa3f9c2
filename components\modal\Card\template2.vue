<template>
	<div class="summary-box" :class="modal.wrapClass" :style="modal.style">
		<div class="bps-container" v-for="(o, oi) in modal.outer" :key="oi">
			<pic class="pic" :src="o.img.value" v-if="o.img" :alt="o.img.alt" :title="o.img.alt" @click="setModalType(o.img,modal.outer,'img')"/>
			<div class="content" :style="modal.contentStyle">
				<EditDiv tagName="h2" v-model:content="o.title.value" v-if="o.title" @click="setModalType(o.title,modal.outer,'text')" />
				<pic class="pic" :src="o.img.value" v-if="o.img" :alt="o.img.alt" :title="o.img.alt" @click="setModalType(o.img,modal.outer,'img')"/>
				<EditDiv class="des" v-model:content="o.text.value" v-if="o.text" @click="setModalType(o.text,modal.outer,'text')" />
				<div class="des conter" v-if="o.subTitle" >
					<div class="item" v-for="(item,index) in o.subTitle" @click="setModalType(o.subTitle[index].title,o.subTitle,'text')">
						<span></span>{{item.title.value}}
					</div>
				</div>
				<div class="btnWrap" v-if="o.button" @click="setModalType(o.button,modal.outer,'button',o.button)">
					<a  href="javascript:void(0)" :title="o.button.alt" :target="o.button.target || '_self'"
						 class="default-button bps-button" :style="{...o.button.style }">
						{{ o.button.value }}
						<b class="icon-bps-sanjiao"></b>
					</a>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	props: {
		data: {
			type: Object,
			default: {},
		}
	},
	data() {
		return {
			modal: {
				style: {},
				type: {index: 0, clickPosition: 'outer'},
				...this.data
			}
		};
	},
	watch: {
		modal: {
			handler(val) {
				if (process.env.isManage) this.$emit("update:data", val);
			},
			immediate: true,
			deep: true
		}
	},
	methods:{
		setModalType(target, targetArray, clickType, event, other) {
			this.$setModal(this, target, targetArray, clickType, event, other)
		}
	},
	mounted() {

	}
};
</script>

<style lang="scss" scoped>
.summary-box {
	padding: 70px 0;

	.bps-container {
		display: grid;
		align-items: center;
		position: relative;
		grid-template-columns: repeat(2, 1fr);
		grid-gap: 20px;

		.btnWrap {
			display: inline-block;
			color: #ffffff;
		}

		&>.pic {
			border-radius: 10px;
		}

		.content {
			h1,
			h2 {
				font-size: 36px;
				text-align: left;
			}
			&>.pic{
				display: none;
			}

			.des {
				margin: 30px 0;
				line-height: 24px;
				color: #666666;
			}
			.conter {
				display: grid;
				grid-template-columns: repeat(2, 1fr);
				grid-column-gap: 30px;
				grid-row-gap: 10px;
				font-size: 14px;
				font-weight: 400;
				color: #333;
				.item {
					span {
						display: inline-block;
						width: 8px;
						height: 8px;
						background: #939393;
						border-radius: 50%;
						margin-right: 20px;
					}
				}
			}
		}
	}
}

.home-part3 {
	background-color: #F8F8F8;

	.bps-container {
		position: relative;
		grid-template-columns: 1fr 1.2fr;
		grid-gap: 70px;

		.content {
			grid-row: 1/2;
		}
	}
}

.about-part {
	padding: .7813vw 0 2.6042vw;
	background-color: #FFF;

	.bps-container {
   		 max-width: 74.4792vw;
		position: relative;
		grid-template-columns: 1fr 1.7fr;
		grid-gap: 1.5vw;
		.content {
			grid-row: 1/2;
			margin-left: 0;
		}
	}
	.bps-button{
		margin-top: 0;
	}

	.content{
		margin-left: 1.3021vw;
	}
}

@media screen and (max-width: $mb-width) {
	.summary-box {
		padding: 27px 0;
		background-color: #ffffff;

		.bps-container {
			display: grid;
			grid-template-columns: 1fr;
			align-items: center;
			grid-gap: 25px;

			.content {
				h2 {
					font-size: 21px;
					margin-bottom: 20px;
				}

				.des {
					margin: 23px 0;
					font-size: 12px !important;
					grid-column-gap: 10px !important;
				}
			}
		}
	}
	.home-part3 {
		.bps-container {
			&>.pic {
				display: none;
			}
			.content {
				grid-row: none;
				&>.pic {
					display: block;
				}
			}
		}
	}

	.about-part {
		padding: 9.0667vw 4.4vw 0 3.2vw;
		.bps-container {
			display: block;
			max-width: 100%;
			padding: 0 !important;

			.bps-button {
				font-size: 3.2vw;
				height: 9.0667vw;
				border-radius: .8vw;

				b.icon-bps-sanjiao {
					margin-left: 1.3333vw;
					font-size: 3.2vw;
				}
			}

			&>.pic {
				display: none;
			}
			.content {
				grid-row: none;

				h2{
				font-size: 5.6vw;
				margin-left: .3646vw;
			}
				&>.pic {
					display: block;
				}

				.des{
					font-size: 3.2vw !important;
					line-height: 4.8vw;
					margin: 0 .4vw 0 1.4667vw;
				}
			}

			.btnWrap{
				margin-top: 6vw;
			}
		}
	}
}
</style>
