<template>
	<div
		class="tips"
		:class="{ type2: type2 }"
		:style="{
			backgroundColor: backgroundColor,
			position: position,
			...cssConfig.style
		}"
	>
		<slot></slot>
	</div>
</template>

<script>
export default {
	name: "tips",
	props: {
		backgroundColor: {
			type: String,
		},
		position: {
			type: String,
		},
		type2: {
			type: Boolean,
			default: false,
		},
        cssConfig:{
            type: Object,
            default: ()=>{
                return {
                    style:{}
                }
            },
        }
	},
	computed: {},
};
</script>

<style scoped lang="scss">
.tips {
	border-radius: 6px 0 6px 0;
	font-size: 14px;
	// font-family: Calibri;
	font-weight: 400;
	color: #fff;
	line-height: 1em;
	padding: 3px 10px;
	position: relative;
	top: 0;
	left: 0;

	&.type2 {
		top: 50%;
		transform: translateY(-50%);
		right: 5px;
		left: auto;
	}

	@media screen and (max-width: 767px) {
		font-size: 12px;
	}
}
</style>