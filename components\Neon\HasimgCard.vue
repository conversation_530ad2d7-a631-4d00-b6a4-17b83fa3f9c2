<template>
  <div class="hasimg-card"
    :style="{'--columns':columns, '--columnsMb': columnsMb || columns}">
    <template v-for="(item,index) in data_list">
      <div class="card"
        :key="item.id"
        :data-id="item.id"
        :id="item.id"
        @click="handleClick(item)"
        v-if="!item.isHidden"
        :class="item.id === value?.id ? 'is-active':''">
        <div class="img">
          <div v-if="showTag && item.labelText"
            class="tag">
            <div class="tag-txt"
              :style="{'--tag-color': item.labelColor ?  item.labelColor:'#FBF67E'}"> {{ item.labelText }} </div>
          </div>
          <div class="video-type"
            v-if="hasVideo(item)">
            <VideoPlayer :style="{'aspect-ratio':aspect}"
              class="showPlayBtn"
              ref="videos"
              :options="getVideoOptions(vidioPath(item).tVideoPath,vidioPath(item).tImagePath)"></VideoPlayer>
            <i class="el-icon-zoom-in zoomIcon"
              @click.stop="viewVideo(vidioPath(item).tVideoPath, item, 'video')"></i>
          </div>
          <div v-else
            class="card-img"
            :class="{'rotateYAnimation':index != 0 && rotateYAnimation}">
            <img :src="filterImg(item)[0]"
              :style="{'aspect-ratio':aspect}"
              alt="img error">
            <img v-if="hasTwoImg(item) > 1"
              :src="filterImg(item)[1]"
              :style="{'aspect-ratio':aspect}"
              alt="img error">
          </div>
        </div>
        <div class="content"
          v-if="showText">
          <div class="detail">
            <div class="title">
              <p>{{item.alias}}</p>
              <div class="stepTitle">
                <slot></slot>
                <el-tooltip v-if="item.tips && iconTips"
                  popper-class="cusToolTip"
                  effect="light"
                  :content="item.tips"
                  placement="top-start">
                  <b style="color:#999999;"
                    @click.stop
                    class="icon-jxsht-jcsz-wh"></b>
                </el-tooltip>
              </div>
            </div>
            <div class="other"
              :style="reverse ? 'flex-direction: column-reverse':'flex-direction: column'">
              <p class="addtional-price">
					<span v-if="isFd">
						<span :style="item.priceInfo.isQur ? '':'color:#de3500'">{{ item.priceInfo.isQur ? 'QUR':"Free" }}</span>
					</span>
					<NeonPriceText v-else :price-info="item.priceInfo"
						:onlyAddInquiry="item.onlyAddInquiry"
						:neonDiscount="neonDiscount"></NeonPriceText>
              </p>
              <p v-if="item.tips && !iconTips"
                class="description">
                {{item.tips}}
              </p>
              <p class="overflowTips"
                v-if="overTips && item.paramName == 'Wooden Box'">
                {{ lang.neon.recommendSizeTips }}
              </p>
            </div>
          </div>
        </div>
        <Corner radius="0px 3px 0px 10px"
          :active="item.id === value?.id"></Corner>

        <div class="previewIcon"
          v-if="showPreviewIcon"
          @click.stop="showPreview(item)">
          <i class="el-icon-zoom-in"></i>
        </div>
      </div>
    </template>

  </div>
</template>

<script>
import "@/plugins/element";
import VideoPlayer from "@/components/Quote/VideoPlayer.vue";
import Corner from "@/components/Neon/Corner";
import NeonPriceText from "@/components/Neon/NeonPriceText";
import { getFileSuffix } from "@/utils/utils";
export default {
  name: "HasimgCard",
  model: {
    prop: "value",
    event: "change",
  },
  computed: {
    lang() {
      return this.$store.getters.lang.quote || {};
    },
  },
  props: {
    showPreviewIcon: {
      type: Boolean,
      default: false,
    },
    value: {
      type: Object,
      default: () => {},
    },
    bindName: {
      type: String,
    },
    data_list: {
      type: Array,
      default: () => [],
    },
    color: {
      type: String,
      default: () => "#d88416",
    },
    showText: {
      type: Boolean,
      default: () => true,
    },
    columns: {
      type: Number,
      default: () => 2,
    },
    columnsMb: {
      type: Number,
      default: () => 0,
    },
    showTag: {
      type: Boolean,
      default: () => false,
    },
    reverse: {
      type: Boolean,
      default: () => false,
    },
    iconTips: {
      type: Boolean,
      default: () => false,
    },
    aspect: {
      type: String,
      default: () => "",
    },
    overTips: {
      type: Boolean,
      default: () => false,
    },
    rotateYAnimation: {
      type: Boolean,
      default: () => false,
    },
    neonDiscount: {
      type: Object,
      default: () => {},
    },
	isFd: {
	  type: Boolean,
	  default: false
	}
  },
  components: {
    Corner,
    NeonPriceText,
    VideoPlayer,
  },
  methods: {
    showPreview(item) {
      this.$store.commit("setNeonPreview", {
        paramName: item.paramName,
        alias: item.alias,
        show: true,
      });
    },
    hasVideo(val) {
      const arr = JSON.parse(val.imageJson);
      return arr.some((x) =>
        [".mp4", ".flv", ".f4v"].includes(getFileSuffix(x.url).toLowerCase())
      );
    },
    hasTwoImg(val) {
      let count = 0;
      if (val?.imageJson) {
        JSON.parse(val.imageJson).forEach((x) => {
          if (
            [".png", ".jpg", "jpeg", "svg", "gif"].includes(
              getFileSuffix(x.url).toLowerCase()
            )
          ) {
            count++;
          }
        });
      }
      return count;
    },
    filterImg(val) {
      const arr = [];
      if (val?.imageJson) {
        JSON.parse(val.imageJson).forEach((x) => {
          if (
            [".png", ".jpg", ".jpeg", ".svg", ".gif"].includes(
              getFileSuffix(x.url).toLowerCase()
            )
          ) {
            arr.push(x.url);
          }
        });
      }
      return arr;
    },
    handleClick(v) {
      this.$emit("change", v);
    },
    // 找到第一张图 和第一个视频
    vidioPath(val) {
      const arr = JSON.parse(val.imageJson);
      return {
        tImagePath: arr.find((x) =>
          [".png", ".jpg", "jpeg", "svg", "gif"].includes(
            getFileSuffix(x.url).toLowerCase()
          )
        ).url,
        tVideoPath: arr.find((x) =>
          [".mp4", ".flv", ".f4v"].includes(getFileSuffix(x.url).toLowerCase())
        ).url,
      };
    },

    getVideoOptions(path, poster) {
      return {
        autoplay: false,
        controls: false,
        muted: true,
        loop: true,
        fill: true,
        sources: [
          {
            src: path,
            type: "video/mp4",
          },
        ],
        poster: poster,
      };
    },

    viewVideo(src, val, type) {
      this.$emit("picDialogFun", true);
      this.$emit("zoomPicFun", { key: this.bindName, value: val });
    },
  },
};
</script>

<style lang="scss" scoped>
.hasimg-card {
  display: grid;
  grid-template-columns: repeat(var(--columns), 1fr);
  gap: 10px;
  @media screen and(max-width: 1273px) {
    gap: 10px;
  }
  @media screen and(max-width: 768px) {
    grid-template-columns: repeat(var(--columnsMb), 1fr);
  }
  .card {
    border: 1px solid #dbdbdb;
    border-radius: 6px;
    padding: 4px;
    cursor: pointer;
    position: relative;

    .previewIcon {
      font-weight: 600;
      font-size: 20px;
      position: absolute;
      padding: 5px 10px;
      right: 0;
      top: 0;
      color: #626166;
    }
    &:hover {
      box-shadow: 0 0 5px 0 #ccc;
      .zoomIcon {
        transform: scale(1.03);
        transition: 0.3s ease-in-out;
      }
    }
    .img {
      position: relative;
      .tag {
        position: absolute;
        .tag-txt {
          display: block;
          position: relative;
          height: 22px;
          line-height: 22px;
          color: var(--tag-color);
          font-size: 14px;
          text-shadow: 0px 1px 1px rgba(0, 0, 0, 0.336);
          text-align: center;
          overflow: hidden;
          padding: 0 3px;
          z-index: 1;
          @media screen and (max-width: 768px) {
            font-size: 8px;
            padding: 0 4.5px;
          }
          &::after {
            content: "";
            position: absolute;
            width: 120%;
            height: 100%;
            left: -10%;
            top: 0;
            border-radius: 0 0 50% 50%;
            background: linear-gradient(
              90deg,
              #fb271c 0%,
              #ffa332 50%,
              #fb271c 100%
            );
            z-index: -1;
          }
        }
      }
      .video-type {
        width: 100%;
        height: 100%;
        position: relative;
        .zoomIcon {
          color: transparent;
          background: linear-gradient(
            90deg,
            rgb(74, 72, 255) 0%,
            rgb(176, 18, 251) 100%
          );
          -webkit-background-clip: text;
          font-weight: 600;
          font-size: 20px;
          position: absolute;
          padding: 5px 10px;
          right: 0;
          top: 0;
          z-index: 1;
        }
      }
      .card-img {
        height: 100%;
        width: 100%;
        position: relative;
        img {
          object-fit: contain;
        }
        img:nth-child(2) {
          display: none;
        }
        &.rotateYAnimation {
          img {
            backface-visibility: hidden;
          }

          img:nth-child(1) {
            z-index: 1;
            opacity: 1;
          }

          img:nth-child(2) {
            display: inline-block;
            position: absolute;
            left: 0;
            top: 0;
            right: 0;
            bottom: 0;
            opacity: 0;
          }
          &:hover {
            // animation-name: translateImage;
            // animation-timing-function: linear;
            // animation-duration: 0.5s;
            // animation-fill-mode: forwards;

            img:nth-child(1) {
              animation-name: zIndexChange1;
              animation-timing-function: linear;
              animation-duration: 0.5s;
              animation-fill-mode: forwards;
            }

            img:nth-child(2) {
              animation-name: zIndexChange2;
              animation-timing-function: linear;
              animation-duration: 0.5s;
              animation-fill-mode: forwards;
            }
          }
        }
      }
      ::v-deep .showPlayBtn {
        border-radius: 6px;
        // overflow: hidden;
        img {
          object-fit: cover;
        }
        video {
          object-fit: cover;
          overflow: hidden;
        }
      }
    }
    &.is-active {
      border: 1px solid var(--neon-light-color);
      .content {
      }
    }
    .content {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 12px 0 8px;
      font-size: 14px;
      text-align: center;
      @media screen and(max-width: 1273px) {
        font-size: 12px;
      }
      .title {
        display: flex;
        margin-bottom: 5px;
        justify-content: center;
        gap: 5px;
        .stepTitle {
          font-size: 1.125em;
          font-family: Google Sans;
          font-weight: 600;
        }
      }
      .other {
        display: flex;
        flex-direction: column-reverse;
        .overflowTips {
          color: red;
          padding: 5px 10px;
        }
      }
      .description {
        font-size: 14px;
        font-family: Google Sans;
        font-weight: 400;
        color: #999999;
        @media screen and(max-width: 1273px) {
          font-size: 12px;
        }
      }
    }
  }
}

@keyframes translateImage {
  0% {
    transform: rotateY(0deg);
  }

  50% {
    transform: rotateY(90deg);
  }

  100% {
    transform: rotateY(180deg);
  }
}

@keyframes zIndexChange1 {
  0% {
    z-index: 2;
    opacity: 1;
  }

  50% {
    z-index: 2;
    opacity: 1;
  }

  100% {
    z-index: 1;
    opacity: 0;
  }
}

@keyframes zIndexChange2 {
  0% {
    z-index: 1;
    opacity: 0;
  }

  50% {
    z-index: 1;
    opacity: 0;
  }

  100% {
    z-index: 2;
    opacity: 1;
  }
}
</style>