<template>
	<div class="imgAndColor" :class="stepData.styleName">
		<half-design-litter-title v-show="stepData.attributeTitle" :index="stepData.id"
			:data-name="`${stepData.styleClass + '_' + stepData.id}`" style="margin: 4px 0"
			:stepTitle="stepData.minStepTitle">{{ stepData.attributeTitle }}
		</half-design-litter-title>
		<slot name="stepText"></slot>
		<div class="step-content">
			<div class="step-item" :class="{ active: index === selectIndex }"
				v-for="(step, index) in stepData.productParamList" :key="index" @click="selectStep(step, index)">
				<div class="imgWrap">
					<img :src="step.sizeImg" :alt="step.imgAltTitle" :title="step.imgAltTitle" />
				</div>
				<div class="text-truncate name">{{ step.colorAlias }}
				</div>
				<half-design-check-icon class="absolute-top-right check-icon"></half-design-check-icon>
			</div>
		</div>
		<div class="errorTip">
			<v-alert dense outlined type="error">
				{{ langSemiCustom.errorTip }}
			</v-alert>
		</div>
	</div>
</template>
<script>
export default {
	props: {
		stepData: {
			type: Object,
			default: {},
		},
	},
	data() {
		return {
			selectIndex: -1,
			selectItem: null,
		};
	},
	computed: {
		langSemiCustom() {
			return this.$store.getters.lang?.semiCustom;
		},
		shape() {
			return this.selectItem?.colorAlias || this.selectItem?.valueName;
		},
	},
	methods: {
		selectStep(item, index, state = false) {
			this.selectIndex = index;
			this.selectItem = item;
			this.$emit("selectStep", {
				type: this.stepData.attributeFlag,
				data: item,
				parent: this.stepData,
				id: this.stepData.id,
				firstSelect: state,
				colorIndex: index,
			});
		},
		getImg(item) {
			if (!item.imgJson) {
				return "";
			}
			return JSON.parse(item.imgJson)[0].url;
		},
		selectDefault() {
			if (this.selectIndex <= -1) {
				this.selectStep(this.stepData.productParamList[0], 0, true);
			}
		},
		setActiveProductColor() {
			let newColorList = this.stepData.productParamList.filter((color) => {
				return color.isActivity == 1;
			});
			if (newColorList.length == 0) {
				return;
			}
			this.stepData.productParamList = newColorList;
		},
	},
	mounted() {
		this.$Bus.$on("selectDefaultColorStep", this.selectDefault);
		this.$Bus.$on("setActiveProductColor", this.setActiveProductColor);
	},
	beforeDestroy() {
		this.$Bus.$off("selectDefaultColorStep", this.selectDefault);
		this.$Bus.$off("setActiveProductColor", this.setActiveProductColor);
	},
};
</script>
<style scoped lang="scss">
@import "~assets/css/half.scss";

.imgAndColor {
	&.style1 {
		.step-content {
			display: grid;
			grid-template-columns: repeat(3, 1fr);
			grid-gap: 10px;

			.step-item {
				min-width: 0;
				position: relative;
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				@include step-default;
				cursor: pointer;
				background-color: #F4F5F5;
				border-color: transparent;

				.imgWrap {
					width: 100%;
					display: flex;
					justify-content: center;
					align-items: center;

					img {
						width: 100%;
						height: 100%;
						object-fit: contain;
					}
				}

				.text-truncate {
					min-width: 0;
					white-space: normal !important;
				}

				.checkIcon {
					display: none;
				}

				&>.name {
					width: 100%;
					margin-top: 4px;
					text-align: center;
				}
			}

			.step-item {
				&:hover {
					color: #333;
				}

				&.active {
					color: #333;

					.checkIcon {
						display: flex;
					}
				}
			}


		}
	}

	@include respond-to(mb) {
		&.style1 {
			.step-content {
				grid-template-columns: repeat(3, 1fr);
				grid-gap: 5px;
			}
		}
	}
}
</style>
