export const getMedalsAllStepConfig = function () {
	return {
		quoteCategory: {
			mediaConfig: {
				style: {
					"aspect-ratio": "756/594",
				},
			},
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(4, 1fr)",
					},
				},
				ipad: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			},
		},
		"Medal Type": {
			showPriceText: false,
			mediaConfig: {
				style: {
					"aspect-ratio": "1008/708",
				},
			},
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						"align-items": "flex-start",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						"align-items": "flex-start",
					},
				},
			},
		},
		"Mold Option": {
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						"align-items": "flex-start",
					},
				},
				ipad: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						"align-items": "flex-start",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						"align-items": "flex-start",
					},
				},
			},
		},
		"Medal Color": {
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(6, 1fr)",
						"align-items": "flex-start",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						"align-items": "flex-start",
					},
				},
			},
		},
		"Medal Size": {
			sizeImgP1: this.$store.getters.lang.quote.medals.p1,
			smallTitle: this.$store.getters.lang.quote.medals.stepSizeTitle,
		},
		"Metal Finish": {
			smallTitle: this.$store.getters.lang.quote.medals.step5Text,
			showNextBtn: true,
			hasViewMore: true,
			showChildListLength: 4,
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(6, 1fr)",
						"align-items": "flex-start",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						"align-items": "flex-start",
					},
				},
			},
		},
		"Additional Upgrades (Options)": {
			smallTitle: this.$store.getters.lang.quote.prompt,
			mediaConfig: {
				style: {
					"aspect-ratio": "1008/708",
				},
			},
			stepItemStyle: {
				stepImgWrapStyle: {
					style: {
						"background-color": "#EBEBEB",
					},
				},
			},
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						"align-items": "flex-start",
					},
				},
				ipad: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						"align-items": "flex-start",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						"align-items": "flex-start",
					},
				},
			},
		},
		"Back Side Option": {
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
					},
				},
				ipad: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			},
		},
		"Medal Loop": {
			showPriceText: false,
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(4, 1fr)",
						"align-items": "flex-start",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						"align-items": "flex-start",
					},
				},
			},
		},
		Ribbon: {
			showPriceText: false,
			stepItemStyle: {
				stepImgWrapStyle: {
					style: {
						"background-color": "#EBEBEB",
						"aspect-ratio": "326 / 237",
					},
				},
			},
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						"align-items": "flex-start",
					},
				},
			},
		},
		Package: {
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(4, 1fr)",
						"align-items": "flex-start",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						"align-items": "flex-start",
					},
				},
			},
		},
	};
};

export const getPinsAllStepConfig = function () {
	return {
		quoteCategory: {
			pcActive2: true,
			mbActive2: true,
			mediaConfig: {
				style: {
					"aspect-ratio": "1008/708",
				},
			},
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						"align-items": "stretch",
					},
				},
				ipad: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						"align-items": "stretch",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						"align-items": "stretch",
					},
				},
			},
			stepItemStyle: {
				style: {
					borderRadius: "6px",
					"background-color": "rgb(242, 242, 242)",
				},
				stepImgWrapStyle: {
					style: {
						borderRadius: "6px 6px 0 0",
						border: "none",
					},
				},
				stepTextWrapStyle: {
					style: {
						flex: 1,
						width: "100%",
						backgroundColor: "#f2f2f2",
						padding: "10px",
						"margin-top": "0",
						borderRadius: "0 0 6px 6px",
					},
				},
			},
		},
		"Pin Size": {
			sizeImgP1: this.$store.getters.lang.quote.pins.p1,
			smallTitle: this.$store.getters.lang.quote.pins.stepSizeTitle,
		},
		"Rush Pin Size": {
			showPriceText: false,
			mediaConfig: {
				style: {
					"aspect-ratio": "213/178",
				},
			},
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(5, 1fr)",
					},
				},
				ipad: {
					style: {
						"grid-template-columns": "repeat(5, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
					},
				},
			},
			stepItemStyle: {
				style: {
					borderRadius: "6px",
				},
				stepTextWrapStyle: {
					style: {
						flex: 1,
						padding: "5px 0.4em",
						"margin-top": "5px",
					},
				},
				mbStepTextWrapStyle: {
					style: {
						padding: "5px 0.4em",
						"margin-top": "0",
						"text-align": "center",
					},
				},
				stepImgWrapStyle: {
					style: {
						background: "#f4f5f5",
						"border-radius": "6px",
					},
				},
				mbStepImgWrapStyle: {
					style: {
						"border-radius": "6px",
					},
					customCircle: 3,
				},
			},
		},
		"Select Metal Finish": {
			mediaConfig: {
				style: {
					"aspect-ratio": "220/118",
				},
			},
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(5, 1fr)",
						"align-items": "flex-start",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						"align-items": "flex-start",
					},
				},
			},
		},
		"Select Amount of Colors for Pin": {
			mediaConfig: {
				style: {
					"aspect-ratio": "180/170",
				},
			},
			stepItemStyle: {
				stepImgWrapStyle: {
					style: {
						background: "#f4f5f5",
					},
				},
			},
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(6, 1fr)",
						"align-items": "flex-start",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						"align-items": "flex-start",
					},
				},
			},
		},
		"Additional Upgrades (Optional)": {
			showNextBtn: true,
			mediaConfig: {
				style: {
					"aspect-ratio": "1008/708",
				},
			},
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						"align-items": "flex-start",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						"align-items": "flex-start",
					},
				},
			},
		},
		"Select Attachment": {
			hasViewMore: true,
			showChildListLength: 6,
			mediaConfig: {
				style: {
					"aspect-ratio": "660/420",
				},
			},
			stepItemStyle: {
				stepImgWrapStyle: {
					style: {
						background: "#f4f5f5",
					},
				},
			},
			stepContentConfig: {
				ipad: {
					style: {
						"grid-template-columns": "repeat(5, 1fr)",
						"align-items": "flex-start",
					},
				},
				mediaQueries: {
					"@media screen and (min-width: 1801px)": {
						"grid-template-columns": "repeat(5, 1fr)",
						"align-items": "flex-start",
					},
					"@media screen and (max-width: 1800px)": {
						"grid-template-columns": "repeat(4, 1fr)",
						"align-items": "flex-start",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						"align-items": "flex-start",
					},
				},
			},
		},
		"Back Side Option": {
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
					},
				},
				ipad: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			},
		},
		"Select Packaging": {
			mediaConfig: {
				style: {
					"aspect-ratio": "696/540",
				},
			},
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(5, 1fr)",
						"align-items": "flex-start",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						"align-items": "flex-start",
					},
				},
			},
		},
		"Select Shapes": {
			showPriceText: false,
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(5, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			},
		},
	};
};

export const getCoinsAllStepConfig = function () {
	return {
		quoteCategory: {
			mediaConfig: {
				style: {
					"aspect-ratio": "1008/708",
				},
			},
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
					},
				},
				ipad: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			},
		},
		"Coin Type": {
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(5, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			},
		},
		"Soft Enamel or Hard Enamel Coins": {
			showPriceText: true,
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
					},
				},
				ipad: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			},
		},
		"Select Your Coin Colors": {
			showPriceText: true,
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
					},
				},
				ipad: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			},
		},
		Plating: {
			hasViewMore: true,
			showChildListLength: 9,
			showPriceText: true,
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(6, 1fr)",
						"align-items": "flex-start",
					},
				},
				ipad: {
					style: {
						"grid-template-columns": "repeat(6, 1fr)",
						"align-items": "flex-start",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						"align-items": "flex-start",
					},
				},
			},
		},
		"Thickness of Coins": {
			showPriceText: true,
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
					},
				},
				ipad: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			},
		},
		Edge: {
			hasViewMore: true,
			showChildListLength: 6,
			pcActive2: true,
			mbActive2: true,
			mediaConfig: {
				style: {
					"aspect-ratio": "2/1",
				},
			},
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(5, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			},
			zoomIconConfig: {
				style: {
					color: "#cccccc",
				},
			},
			stepItemStyle: {
				style: {
					borderRadius: "6px",
					background: "#f4f5f5",
				},
				stepImgWrapStyle: {
					style: {
						borderRadius: "6px 6px 0 0",
						border: "none",
					},
				},
				stepTextWrapStyle: {
					style: {
						flex: 1,
						width: "100%",
						backgroundColor: "#f2f2f2",
						padding: "6px 10px",
						"margin-top": "0",
						borderRadius: "0 0 6px 6px",
					},
				},
			},
		},
		"Additional Upgrades (Optional)": {
			smallTitle: this.$store.getters.lang.quote.prompt,
			hasViewMore: true,
			showChildListLength: 6,
			pcActive2: true,
			mbActive2: true,
			showNextBtn: true,
			mediaConfig: {
				style: {
					"aspect-ratio": "1008/870",
				},
			},
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(5, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			},
			stepItemStyle: {
				style: {
					borderRadius: "6px",
					background: "#f4f5f5",
				},
				stepImgWrapStyle: {
					style: {
						borderRadius: "6px 6px 0 0",
						border: "none",
					},
				},
				stepTextWrapStyle: {
					style: {
						flex: 1,
						width: "100%",
						backgroundColor: "#f2f2f2",
						padding: "6px 10px",
						"margin-top": "0",
						borderRadius: "0 0 6px 6px",
					},
				},
			},
		},
		Packaging: {
			pcActive2: true,
			mbActive2: true,
			stepItemStyle: {
				style: {
					borderRadius: "6px",
					background: "#f4f5f5",
				},
				stepImgWrapStyle: {
					style: {
						borderRadius: "6px 6px 0 0",
					},
				},
				stepTextWrapStyle: {
					style: {
						flex: 1,
						padding: "5px 0.5em",
						"margin-bottom": "10px",
						backgroundColor: "#f2f2f2",
						"text-align": "center",
					},
				},
			},
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(4, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			},
		},
	};
};

export const getPinBadgesAllStepConfig = function () {
	return {
		"Select Shapes": {
			showPriceText: false,
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(5, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			},
		},
		quoteCategory: {
			pcActive2: true,
			mbActive2: true,
			mediaConfig: {
				style: {
					"aspect-ratio": "1008/708",
				},
			},
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						"align-items": "stretch",
					},
				},
				ipad: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						"align-items": "stretch",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						"align-items": "stretch",
					},
				},
			},
			stepItemStyle: {
				style: {
					borderRadius: "6px",
					"background-color": "rgb(242, 242, 242)",
				},
				stepImgWrapStyle: {
					style: {
						borderRadius: "6px 6px 0 0",
						border: "none",
					},
				},
				stepTextWrapStyle: {
					style: {
						flex: 1,
						width: "100%",
						backgroundColor: "#f2f2f2",
						padding: "10px",
						"margin-top": "0",
						borderRadius: "0 0 6px 6px",
					},
				},
			},
		},
		"Rush Pin Size": {
			showPriceText: false,
			mediaConfig: {
				style: {
					"aspect-ratio": "213/178",
				},
			},
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(5, 1fr)",
					},
				},
				ipad: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			},
			stepItemStyle: {
				style: {
					borderRadius: "6px",
				},
				stepTextWrapStyle: {
					style: {
						flex: 1,
						padding: "5px 0.4em",
						"margin-top": "5px",
					},
				},
				mbStepTextWrapStyle: {
					style: {
						padding: "5px 0.4em",
						"margin-top": "0",
					},
				},
				stepImgWrapStyle: {
					style: {
						background: "#f4f5f5",
						"border-radius": "6px",
					},
				},
				mbStepImgWrapStyle: {
					style: {
						"border-radius": "6px",
					},
				},
			},
		},
		"Select Metal Finish": {
			hasViewMore: true,
			showChildListLength: 6,
			mediaConfig: {
				style: {
					"aspect-ratio": "220/118",
				},
			},
			stepContentConfig: {
				mediaQueries: {
					"@media screen and (min-width: 1801px)": {
						"grid-template-columns": "repeat(6, 1fr)",
					},
					"@media screen and (max-width: 1800px)": {
						"grid-template-columns": "repeat(5, 1fr)",
					},
					"@media screen and (max-width: 1678px)": {
						"grid-template-columns": "repeat(4, 1fr)",
					},
					"@media screen and (min-width: 1366px) and (max-width: 1500px)": {
						"grid-template-columns": "repeat(6, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			},
		},
		"Select Amount of Colors for Pin": {
			mediaConfig: {
				style: {
					"aspect-ratio": "180/170",
				},
			},
			stepContentConfig: {
				mediaQueries: {
					"@media screen and (min-width: 1801px)": {
						"grid-template-columns": "repeat(6, 1fr)",
					},
					"@media screen and (max-width: 1800px)": {
						"grid-template-columns": "repeat(5, 1fr)",
					},
					"@media screen and (max-width: 1650px)": {
						"grid-template-columns": "repeat(4, 1fr)",
					},
					"@media screen and (min-width: 1366px) and (max-width: 1500px)": {
						"grid-template-columns": "repeat(6, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
					},
				},
			},
		},
		"Additional Upgrades (Optional)": {
			showNextBtn: true,
			mediaConfig: {
				style: {
					"aspect-ratio": "1008/708",
				},
			},
			stepContentConfig: {
				mediaQueries: {
					"@media screen and (min-width: 1901px)": {
						"grid-template-columns": "repeat(3, 1fr)",
					},
					"@media screen and (min-width: 1501px) and (max-width: 1900px)": {
						"grid-template-columns": "repeat(3, 1fr)",
					},
					"@media screen and (min-width: 1366px) and (max-width: 1500px)": {
						"grid-template-columns": "repeat(5, 1fr)",
					},
					"@media screen and (max-width: 1365px)": {
						"grid-template-columns": "repeat(3, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			},
		},
		"Select Attachment": {
			hasViewMore: true,
			showChildListLength: 6,
			mediaConfig: {
				style: {
					"aspect-ratio": "660/420",
				},
			},
			stepItemStyle: {
				stepImgWrapStyle: {
					style: {
						backgroundColor: "#f2f2f2",
					},
				},
			},
			stepContentConfig: {
				mediaQueries: {
					"@media screen and (min-width: 1651px)": {
						"grid-template-columns": "repeat(5, 1fr)",
					},
					"@media screen and (min-width: 1366px) and (max-width: 1500px)": {
						"grid-template-columns": "repeat(5, 1fr) !important",
					},
					"@media screen and (max-width: 1650px)": {
						"grid-template-columns": "repeat(4, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			},
		},
		"Back Side Option": {
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
					},
				},
				ipad: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			},
		},
		"Select Packaging": {
			mediaConfig: {
				style: {
					"aspect-ratio": "696/540",
				},
			},
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(5, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			},
		},
	};
};

export const getBallMarkerAllStepConfig = function () {
	return {
		quoteCategory: {
			videoFit: "cover",
			mediaConfig: {
				style: {
					"aspect-ratio": "382/287",
				},
			},
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						"align-items": "flex-start"
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						"align-items": "flex-start"
					},
				},
			},
			zoomIconConfig:{
				style:{
					color: "#cccccc"
				}
			}
		},
		"Ball Markers Size":{
			sizeImgP1: this.$store.getters.lang.quote.ballMarker.p1,
			smallTitle: this.$store.getters.lang.quote.ballMarker.stepSizeTitle,
		},
		"Select Plating / Finish": {
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(5, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)"
					},
				},
			},
		},
		"Select  Amount of Colors for Ball Markers": {
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(6, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			},
		},
		"Additional Upgrades (Optional)": {
			videoFit: "cover",
			mediaConfig: {
				style: {
					"aspect-ratio": "287/237",
				},
			},
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(4, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			},
		},
	};
};

export const getLanyardAllStepConfig = function () {
	return {
		"Products Categories": {
			showPriceText: false,
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(4, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			},
		},
	};
};

export const getWristbandAllStepConfig = function () {
	return {
		"Band Size": {
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(6, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						"align-items": "self-start",
					},
				},
			},
			showPriceText: false,
		},
		"Message Style": {
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						"align-items": "self-start",
					},
				},
			},
			showPriceText: false,
		},
		"More Options": {
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						"align-items": "self-start",
					},
				},
			},
		},
		"Packaging Options": {
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						"align-items": "self-start",
					},
				},
			},
		},
	};
};

export const getKeychainsAllStepConfig = function () {
	return {
		quoteCategory: {
			videoFit: "cover",
			pcActive2: true,
			mbActive2: true,
			mediaConfig: {
				style: {
					"aspect-ratio": "1008/708",
				},
			},
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
					},
				},
				ipad: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						"align-items": "stretch",
					},
				},
			},
			stepItemStyle: {
				style: {
					borderRadius: "6px",
					overflow: "hidden",
					"background-color": "#f2f2f2",
				},
				stepImgWrapStyle: {
					style: {
						borderRadius: "10px",
					},
				},
				pcStepTextWrapStyle: {
					style: {
						flex: 1,
						width: "100%",
						backgroundColor: "#f2f2f2",
						padding: "10px 0",
						"margin-top": "0",
						borderRadius: "0 0 6px 6px",
						display: "flex",
						"justify-content": "center",
						"flex-wrap": "wrap",
					},
				},
				mbStepTextWrapStyle: {
					style: {
						flex: 1,
						width: "100%",
						backgroundColor: "#f2f2f2",
						padding: "10px 0",
						"margin-top": "0",
						borderRadius: "0 0 6px 6px",
						display: "flex",
						"justify-content": "center",
						"flex-wrap": "wrap",
					},
				},
			},
		},
		"Select Shapes": {
			mediaConfig: {
				style: {
					"aspect-ratio": "1000/1000",
					background: "rgb(244 245 245)",
				},
			},
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						"align-items": "self-start",
					},
				},
			},
		},
		Plating: {
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(6, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			},
		},
		"Link & Chain Options": {
			hasViewMore: true,
			pcActive2: true,
			mbActive2: true,
			showChildListLength: 6,
			stepItemStyle: {
				style: {
					background: "rgb(244 245 245)",
					"border-radius": "10px",
					"padding-bottom": "10px",
				},
				stepTextWrapStyle: {
					style: {
						padding: "0 7px",
					},
				},
			},
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(5, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			},
		},
		"Select Packaging": {
			pcActive2: true,
			mbActive2: true,
			stepItemStyle: {
				style: {
					background: "rgb(244 245 245)",
					"border-radius": "10px",
					"padding-bottom": "10px",
					"justify-content": "flex-start",
				},
			},
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(5, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			},
		},
		Packaging: {
			pcActive2: true,
			mbActive2: true,
			stepItemStyle: {
				style: {
					background: "rgb(244 245 245)",
					"border-radius": "10px",
					"padding-bottom": "10px",
					"justify-content": "flex-start",
				},
			},
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(5, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			},
		},
		"Design Areas": {
			stepItemStyle: {
				stepImgWrapStyle: {
					style: {
						background: "#f4f5f5",
					},
				},
			},
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						"align-items": "flex-start",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						"align-items": "flex-start",
					},
				},
			}
		},
		"Backstamp Options": {
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						"align-items": "flex-start",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						"align-items": "flex-start",
					},
				},
			},
		},
		"Acrylic Charm Color": {
			stepItemStyle: {
				style: {
					background: "rgb(244 245 245)",
					"border-radius": "10px",
					"padding-bottom": "10px",
				},
			},
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(6, 1fr)",
						"align-items": "flex-start",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						"align-items": "flex-start",
					},
				},
			},
		},
		"Additional Upgrades": {
			stepItemStyle: {
				style: {
					background: "rgb(244 245 245)",
					"border-radius": "10px",
					"padding-bottom": "10px",
				},
			},
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						"align-items": "flex-start",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						"align-items": "flex-start",
					},
				},
			},
		},
		"Keychain Printed Area": {
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						"align-items": "flex-start",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
						"align-items": "flex-start",
					},
				},
			},
		},
		"PVC Keychain Color": {
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(5, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
					},
				},
			},
		},
		"2D or 3D": {
			pcActive2: false,
			mbActive2: true,
			mediaConfig: {
				style: {
					"aspect-ratio": "1008/708",
					"object-fit": "cover",
				},
			},
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			},
			stepItemStyle: {
				mbStyle: {
					borderRadius: "6px",
					overflow: "hidden",
					"background-color": "rgb(242, 242, 242)",
					"padding-bottom": "5px",
				},
				stepTextWrapStyle: {
					mbStyle: {
						flex: 1,
						width: "100%",
						backgroundColor: "#f2f2f2",
						padding: "10px",
						borderRadius: "0 0 6px 6px",
					},
				},
			},
		},
		"Back Printing Options": {
			pcActive2: false,
			mbActive2: true,
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			},
			stepItemStyle: {
				mbStyle: {
					borderRadius: "6px",
					overflow: "hidden",
					"background-color": "rgb(242, 242, 242)",
					"padding-bottom": "5px",
				},
				stepTextWrapStyle: {
					mbStyle: {
						flex: 1,
						width: "100%",
						backgroundColor: "#f2f2f2",
						padding: "10px",
						borderRadius: "0 0 6px 6px",
					},
				},
			},
		},
		"Keychain Color": {
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			},
		},
		"Keychain Size": {
			widthDes: "Keychains width",
			heightDes: "Keychains height",
			showNextBtn: true,
			// smallTitle: this.$store.getters.lang.quote.sizeSmallTitle,
			smallTitle: "Try selecting your height and width below and we will provide you with the exact measurements and price. The size calculation rule is based on the design (height + width) / 2 = actual size.",
		},
		"Embroidery Coverage": {
			showPriceText: false,
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			},
		},
		"Keychain Design Areas": {
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			},
		},
		"Keychain Border": {
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			},
		},
		"Backside Options": {
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			},
		},
	};
};

export const getPatchesAllStepConfig = function () {
	return {
		"Number of Silicone Colors": {
			moldText: 4,
			stepItemStyle: {
				style: {
					"justify-content": "space-between !important",
				},
			},
		},
		"Number of Silicone Layers":{
			showPriceText: false,
			showExtendStepPriceText: true,
		},
		"Pre-Cut Transfers": {
			smallTitle: "We can pre-cut them so that you can use them more conveniently!",
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			},
		},
		"Intended Use": {
			showPriceText: false,
			imgBorderStyle: 2,
			hideTitleSelectText: true,
			showNextBtn: true,
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(4, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			},
		},
		"Select Sequins Diameter": {
			showPriceText: false,
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			},
		},
		"Leather Color": {
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(5, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			},
		},
		"Patch Outline": {
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			},
		},
		"Printing Method": {
			smallTitle: this.$store.getters.lang.quote.Reminder,
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			},
		},
		quoteCategory: {
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			},
		},
		"Patch Size": {
			showNextBtn: true,
			smallTitle: this.$store.getters.lang.quote.sizeSmallTitle,
		},
		"Patch Color": {
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			},
		},
		"Patch Shape": {
			showPriceText: false,
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(5, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			},
		},
		"Patch Border": {
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			},
		},
		"Purchase Intention": {
			showPriceText: false,
			imgBorderStyle: 2,
			hideTitleSelectText: true,
			showNextBtn: true,
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(4, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			},
		},
		"Additional Upgrades": {
			smallTitle: this.$store.getters.lang.quote.prompt,
			showNextBtn: true,
			hideTitleSelectText: true,
		},
		"Patch Backing": {
			clickStepShowTips: true,
			showNextBtn: true,
		},
		"Label Size": {
			showNextBtn: true,
			smallTitle: this.$store.getters.lang.quote.sizeSmallTitle,
		},
		"Label Fold Type": {
			stepItemStyle: {
				stepImgWrapStyle: {
					style: {
						background: "#f4f5f5",
					},
				},
			},
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(4, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			},
		},
		"Label Color": {
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			},
		},
		"Label Thread Type": {
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			},
		},
	};
};

export const getOrnamentAllStepConfig = function () {
	return {
		quoteCategory: {
			videoFit: "cover",
			pcActive2: true,
			mbActive2: true,
			mediaConfig: {
				style: {
					"aspect-ratio": "382/287",
				},
			},
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
					},
				},
				ipad: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			},
			stepItemStyle: {
				style: {
					"flex-direction": "row-reverse",
					borderRadius: "6px",
					overflow: "hidden",
					"background-color": "#FAFAFA",
				},
				stepImgWrapStyle: {
					style: {
						borderRadius: "10px",
					},
				},
				stepTextWrapStyle: {
					style: {
						flex: 1,
						width: "100%",
						backgroundColor: "#FAFAFA",
						padding: "10px",
						"margin-top": "0",
						borderRadius: "0 0 6px 6px",
					},
				},
			},
		},
		"Ornament Process": {
			smallTitle: "You can select more than one option.",
			pcActive2: true,
			mbActive2: true,
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
					},
				},
				ipad: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			},
			stepItemStyle: {
				style: {
					overflow: "hidden",
				},
				stepImgWrapStyle: {
					style: {
						borderRadius: "10px 10px 0 0",
					},
				},
				mbStepImgWrapStyle:{
					style: {
						borderRadius: "8px 8px 0 0",
					},
				}
			},
		},
		"Ornament Attachment": {
			pcActive2: true,
			mbActive2: true,
			previewModal: "hover",
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
					},
				},
				ipad: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
					},
				},
			},
			zoomIconConfig: {
				showZoomIcon: true,
				style: {
					color: "#666666",
				},
			},
			stepItemStyle: {
				stepImgWrapStyle: {
					style: {
						borderRadius: "16px 16px 0 0",
					},
				},
				mbStepImgWrapStyle:{
					style: {
						borderRadius: "8px 8px 0 0",
					},
				}
			},
		},
		"Ornament Package": {
			pcActive2: true,
			mbActive2: true,
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
					},
				},
				ipad: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			},
			stepItemStyle: {
				style: {
					overflow: "hidden",
				},
				stepImgWrapStyle: {
					style: {
						borderRadius: "10px 10px 0 0",
					},
				}
			},
		},
		"Select Finish": {
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
					},
				},
				ipad: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			},
		},
		"Select Printed Area": {
			pcActive2: true,
			mbActive2: true,
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
					},
				},
				ipad: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			},
		},
		"Select Acrylic Charm Color": {
			pcActive2: true,
			mbActive2: true,
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
					},
				},
				ipad: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
					},
				},
			},
		},
	};
};

export const getCufflinksAllStepConfig = function () {
	return {
		"Select Turnaround Time": {
			showNextBtn: true,
			smallTitle: this.$store.getters.lang.quote.hourstext,
		},
		"Upload Artwork & Comments": {
			hideTitleSelectText: true,
		},
		Quantity: {
			hideTitleSelectText: true,
		},
	};
};

export const getFidgetSpinnerAllStepConfig = function () {
	return {
		"Select Plating Colors": {
			hasViewMore: true,
			showChildListLength: 6,
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(5, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						"align-items": "flex-start",
					},
				},
			},
		},
		"Select Your 3D style": {
			mediaConfig: {
				style: {
					"aspect-ratio": "372 / 203",
				},
			},
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			},
		},
		"Select Packaging Options": {
			pcActive2: true,
			mbActive2: true,
			stepItemStyle: {
				style: {
					borderRadius: "10px",
					"background-color": "rgb(242, 242, 242)",
				},
				stepTextWrapStyle: {
					style: {
						flex: 1,
						width: "100%",
						backgroundColor: "#f2f2f2",
						padding: "10px",
						"margin-top": "0",
						borderRadius: "0 0 10px 10px",
					},
				},
			},
		},
		"Fidget Spinner Plating": {
			hasViewMore: true,
			showChildListLength: 6,
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(5, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						"align-items": "flex-start",
					},
				},
			},
		},
		"Fidget Spinner Packaging": {
			pcActive2: true,
			mbActive2: true,
			stepItemStyle: {
				style: {
					borderRadius: "10px",
					"background-color": "rgb(242, 242, 242)",
				},
				stepTextWrapStyle: {
					style: {
						flex: 1,
						width: "100%",
						backgroundColor: "#f2f2f2",
						padding: "10px",
						"margin-top": "0",
						borderRadius: "0 0 10px 10px",
					},
				},
			},
		},
		Plating: {
			hasViewMore: true,
			showChildListLength: 6,
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(5, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						"align-items": "flex-start",
					},
				},
			},
		},
	};
};

export const getBeltBucklesAllStepConfig = function () {
	return {
		"quoteCategory": {
			mediaConfig: {
				style: {
					"aspect-ratio": "1008/708",
				},
			},
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			},
		},
		"Select Belt Buckle Size":{
			sizeImgP1: this.$store.getters.lang.quote.beltBuckles.p1,
			smallTitle: this.$store.getters.lang.quote.beltBuckles.stepSizeTitle,
		},
		"Select Shape": {
			showPriceText: false,
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(5, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			},
		},
		"Belt Buckle Color": {
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			},
		},
		"Select Buckle Finish": {
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(5, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			},
		},
		"Additional Upgrades (Optional)": {
			smallTitle: this.$store.getters.lang.quote.prompt,
			hideTitleSelectText: true,
			mediaConfig: {
				style: {
					"aspect-ratio": "1008/708",
				},
			},
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			},
		},
		"Belt Buckle Back Side": {
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			},
		},
		"Select Attachment": {
			stepItemStyle: {
				stepImgWrapStyle: {
					style: {
						background: "#f4f5f5",
					},
				},
			},
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			},
		},
		"Select Packaging": {
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			},
		}
	}
}

export const getButtonBadgesAllStepConfig = function (){
	return {
		"quoteCategory": {
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			},
		},
		"Button Size":{
			sizeImgP1: "Sample Button Badges Size View",
			smallTitle: "All our size options are priced based on the longest dimension of the button badges.",
		},
		"Print Options Finish": {
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(5, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			},
		},
		"Button Back": {
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(5, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			},
		}
	}
}

export const  getPvcLuggageTagsAllStepConfig = function (){
	return {
		"quoteCategory": {
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			},
		},
		"Size":{
			sizeImgP1: this.$store.getters.lang.quote.luggage.p1,
			smallTitle: this.$store.getters.lang.quote.luggage.stepSizeTitle,
		},
		"Color": {
			stepItemStyle: {
				stepImgWrapStyle: {
					style: {
						background: "#f4f5f5",
					},
				},
			},
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(5, 1fr)",
						"align-items": "flex-start",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			}
		},
		"Shape": {
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(5, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			},
		},
		"2D or 3D": {
			mediaConfig:{
				style: {
					"aspect-ratio": "1191/835",
				},
			},
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			},
		},
		"Hole Position": {
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			},
		},
		"Link Options": {
			smallTitle: this.$store.getters.lang.quote.luggage.text,
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			},
		},
		"Select Packaging": {
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
						"align-items": "flex-start",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			},
		}
	}
}

export const getEmbroideredBadgesAllStepConfig = function () {
	return {
		"quoteCategory": {
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			},
		},
		"Patches Size":{
			sizeImgP1: this.$store.getters.lang.quote.embroideredPatches.p1,
			smallTitle: this.$store.getters.lang.quote.embroideredPatches.stepSizeTitle,
		},
		"Patches Backing": {
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(4, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			},
		},
		"Embroidery Coverage": {
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(5, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			},
		},
		"Patches Border": {
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			},
		},
		"Additional Upgrades (Optional)": {
			smallTitle: this.$store.getters.lang.quote.prompt,
			showNextBtn: true,
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(4, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			},
		}
	}
}

export const getOldOrnamentAllStepConfig = function () {
	return {
		quoteCategory: {
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			},
		},
		"Ornament Size":{
			sizeImgP1: this.$store.getters.lang.quote.ornament.p1,
			smallTitle: this.$store.getters.lang.quote.ornament.stepSizeTitle,
		},
		"Ornament Process": {
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
					},
				},
				ipad: {
					style: {
						"grid-template-columns": "repeat(3, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			},
		},
		"Ornament Attachment": {
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(5, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			}
		},
		"Ornament Package": {
			stepContentConfig: {
				pc: {
					style: {
						"grid-template-columns": "repeat(4, 1fr)",
					},
				},
				mb: {
					style: {
						"grid-template-columns": "repeat(2, 1fr)",
					},
				},
			},
		},
	};
};
