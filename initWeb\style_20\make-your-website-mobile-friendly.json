[{"id": "nav_01", "theme": 20, "name": "Footer", "class": "catalogue-modal", "style": {"margin-left": 0, "width": "20%", "height": "calc(100vh - var(--scroll-padding-top) - 1em)", "position": "sticky", "top": "calc(var(--scroll-padding-top) + 1em)", "bottom": 0, "border": "1px solid #ebebeb", "background-color": "#fff"}, "boxStyle": {"padding": 0, "flex-direction": "column"}, "cardStyle": {"gap": 0}, "titleStyle": {"align-items": "baseline", "padding": "0.25rem", "font-size": "calc(1em - 2px)", "background-color": "#f4f4f4"}, "navListStyle": {"grid-gap": 0, "padding": "0 0.25rem", "font-size": "0.75em"}, "navStyle": {"padding": "0.375rem 1.25rem", "border-bottom": "1px dashed #e5e5e5"}, "outer": [{"title": {"value": "Catalogue", "icon": "icon-type1"}, "style": {"height": 0, "overflow": "hidden"}, "titleStyle": {"font-size": "1.25em", "font-weight": 700, "background-color": "#fff"}}, {"title": {"value": "Charts", "icon": "icon-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "navList": [{"value": "Size Chart", "url": "/info/lapel-pins-size-chart"}, {"value": "Color Chart", "url": "/info/color-chart"}, {"value": "Thread Color Chart", "url": "/info/Embroidery-Thread-Color-Chart"}, {"value": "Cloth Color Swatches", "url": "/info/cloth-color-swatches"}, {"value": "Woven Thread Swatches", "url": "/info/woven-thread-swatches"}]}, {"title": {"value": "Your Account", "icon": "icon-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "navList": [{"value": "Create an Account", "url": "/user/account/register"}, {"value": "View Saved Designs", "url": "/user/account/design"}, {"value": "Your Orders", "url": "/user/account/orders"}]}, {"title": {"value": "About", "icon": "icon-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "navList": [{"value": "Privacy Policy", "url": "/info/privacy-policy"}, {"value": "Terms and Conditions", "url": "/info/terms-and-conditions"}, {"value": "About Us", "url": "/info/about-us"}, {"value": "Influencer Program", "url": "/Influencer-Program"}]}, {"title": {"value": "BLOG & SAMPLES GALLERY", "icon": "icon-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "navList": [{"value": "In-Stock", "url": "/products/"}, {"value": "Blog", "url": "/blog/"}, {"value": "Exhibition", "url": "/Exhibition/"}]}, {"title": {"value": "LAPEL PINS", "icon": "icon-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "navList": [{"value": "<PERSON><PERSON><PERSON><PERSON>", "url": "/info/cloisonne-pins"}, {"value": "Custom Hat Pins", "url": "/info/custom-hat-pins"}, {"value": "Custom Made Pins", "url": "/info/custom-made-pins"}, {"value": "Custom Enamel Pins", "url": "/info/custom-enamel-pins"}, {"value": "Enamel <PERSON>ns", "url": "/info/enamel-pins"}, {"value": "Hard Enamel Pins", "url": "/info/hard-enamel-pins"}, {"value": "How to Make Enamel Pins?", "url": "/info/lapel-pins"}, {"value": "Identification & Security Badges", "url": "/info/security-badges"}, {"value": "Soft Enamel Pins Gallery", "url": "/info/soft-enamel-pins"}, {"value": "Offset Printed VS Enamel Pins", "url": "/info/offset-printed-pins-and-enamel-pins"}, {"value": "How To Maintain Your Lapel Pins", "url": "/info/How-To-Maintain-Your-<PERSON><PERSON>-<PERSON><PERSON>-<PERSON><PERSON><PERSON>-Long-Time"}, {"value": "Popular Add-ons of Custom Pins", "url": "/info/Promote-custom-pins-by-popular-Add-ons"}, {"value": "Show Fashion with Custom Pins", "url": "/info/Fashion-with-custom-lapel-pins"}, {"value": "Order Process of Custom Systems ", "url": "/info/order-process"}]}, {"title": {"value": "CHALLENGE COINS", "icon": "icon-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "navList": [{"value": "How to choose the proper edge for your challenge coins", "url": "/info/challenge-coins-edge"}, {"value": "Do you know well about epoxy coating for custom coins?", "url": "/info/Do-you-know-well-about-epoxy-coating-for-custom-coins"}, {"value": "2D Challenge Coins VS 3D Challenge Coins", "url": "/info/2D-VS-3D-Challenge-Coins"}, {"value": "Single-sided or dual-sided challenge coin: which is better?", "url": "/info/Single-sided-or-dual-sided-challenge-coin-which-is-better"}, {"value": "How to Display Challenge Coins? The Great Presentation Options Will Help You! ", "url": "/info/great-presentation-options-to-help-you-display-challenge-coin"}]}, {"title": {"value": "PATCHES", "icon": "icon-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "navList": [{"value": "Things you never knew about the history of Chinese Embroidered Patches", "url": "/info/Embroidery-Patches"}, {"value": "How to Choose the Right Backing for Your Custom Patches?", "url": "/info/backing-of-custom-patches"}, {"value": "  How To Use Iron On Patches On Clothing? ", "url": "/info/iron-on-patches"}, {"value": "Info: Woven Patches VS Embroidered Patches", "url": "/info/woven-patches-vs-embroidered-patches"}, {"value": "What’s the difference of 50% Embroidery,75% Embroidery and 100% Embroidery?", "url": "/info/the-difference-between-embroidery"}, {"value": "Why Custom Patches Can Promote Your Business?", "url": "/info/custom-patches-promote-business"}, {"value": "Learn About Custom Patches Border Options", "url": "/info/Custom-Patches-Border "}, {"value": " The Difference Between Our Five Patches Styles", "url": "/info/Five-<PERSON><PERSON>-Styles"}, {"value": "Amazing Effect for Custom Patches", "url": "/info/amazing-effect-custom-patches"}]}, {"title": {"value": "MEDALS", "icon": "icon-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "navList": [{"value": "What should You Focus on When Ordering Custom Medals?", "url": "/info/Things-about-Custom-Medals"}, {"value": "How to Finish Custom Medals?", "url": "/info/process-of-custom-medals"}, {"value": "Guidance to Distinguish Twins Color", "url": "/info/different-colors-in-custom-medals"}]}, {"title": {"value": "Help Center", "icon": "icon-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "navList": [{"value": "FAQ & Help", "url": "/info/faq-help"}, {"value": "Options", "url": "/options"}, {"value": "Contact Us", "url": "/info/Contact-Us"}]}, {"title": {"value": "GROWTH & MARKETING", "icon": "icon-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "navList": [{"value": "9 Easy Ways to Make Your Website Mobile-Friendly", "url": "/info/make-your-website-mobile-friendly"}]}]}, {"id": "makeYourWebsiteMobileFriendly_01", "theme": 20, "name": "Summary", "style": {"margin-top": "1em", "padding-top": 0, "padding-bottom": 0, "border-top": "1px solid #ebebeb", "border-left": "1px solid #ebebeb", "border-right": "1px solid #ebebeb"}, "boxStyle": {"gap": 0}, "titleStyle": {"padding": "0.25rem 1.25rem", "line-height": 1.3, "font-size": "calc(1em - 2px)", "font-weight": 400, "background-color": "#f4f4f4"}, "outer": [{"title": {"value": "9 Easy Ways to Make Your Website Mobile-Friendly", "tagName": "h1"}}]}, {"id": "makeYourWebsiteMobileFriendly_02", "theme": 20, "name": "Card", "column": 1, "style": {"padding-top": "1.25rem", "padding-bottom": "1.25rem", "padding-left": "1.25rem", "padding-right": "1.25rem", "border-left": "1px solid #ebebeb", "border-right": "1px solid #ebebeb", "border-bottom": "1px solid #ebebeb", "background-color": "#fff"}, "titleStyle": {"text-align": "center", "font-size": "1.5em"}, "cardStyle": {"flex-direction": "column"}, "cardImgStyle": {"margin": "auto", "width": "50%"}, "cardTextStyle": {"opacity": 1}, "cardPriceStyle": {"opacity": 1}, "outer": [{"title": {"value": "9 Easy Ways to Make Your Website Mobile-Friendly"}}], "list": [{"text": {"value": "These days we’d sooner leave our house without our wallet than leave our smart-phones behind. We always have them, and we are always using them, so it’s no surprise that over 40% of all time spent on websites is on a mobile device."}}, {"text": {"value": "No matter your website’s main aim, ignoring how it looks on a mobile device is a huge mistake. Think of it this way, say you Google something on your phone and click on a search result. When the page loads, you can only see a small corner of the website, and to read you have to move across manually with your thumb. It doesn’t exactly make for an ideal user experience, does it? The truth is, most people are going to leave, and that could be as much as 40% of your visitors."}}, {"text": {"value": "So what measures can you take to make sure that the mobile experience is as good as it can be? Here are 9 easy ways you can make your website mobile-friendly."}}, {"img": {"value": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20210114/ueditor/25/mobile-friendly-2.jpg", "alt": "mobile friendly interface"}}, {"title": {"value": "1) It Should be Responsive"}, "text": {"value": "A responsive website adapts itself to the screen it’s being viewed on, so important when your visitors could be viewing it on a 27” flat screen, a 12.9” iPad, or a smartphone. If your current website was built recently, it’s likely your template or builder included a responsive design, but check if you aren’t sure."}, "price": {"value": "The simplest way to do this is simply to look at your website on your smartphone and/or tablet, but you can also use the Google Mobile-Friendly Test. This test will let you know if your site is easy to use on a smart-phone, as well as any loading issues."}}, {"img": {"value": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20210114/ueditor/25/mobile-frriendly-test.jpg", "alt": "mobile frriendly test"}}, {"text": {"value": "If it is not responsive, then it’s time to update your site. The best way to do this will depend on your CMS (content management system) and your current website build. If you’re ready for a complete renovation, make sure you backup your files and install a new theme. For WordPress, you can choose a free theme or purchase one from a site like ThemeForest.net. How much you need to spend will depend on your needs and wants, but you can find good ones from about $16 – $60. If your site was custom-built, and you want another custom build or an upgrade, it’s time to reach out to a developer."}, "price": {"value": "A non-responsive website will mean that your content will be compromised and your visitors’ experience severely compromised. Most people will leave and not come back."}}, {"title": {"value": "2) Curate Your Responsive Website’s Content"}, "text": {"value": "Once you’ve got a responsive layout, it’s time to take a good look at what it looks like. Most website builders will have an option when designing to toggle between the different views: smartphone, tablet, and desktop. Open your website editor and take a look at how your website looks on a smartphone."}, "price": {"value": "Many of these will allow you to omit certain sections when viewed on a smaller device. This can be a good idea if you have something like a video that plays automatically on a desktop but doesn’t look quite right or drastically slows the loading time when viewed on a smartphone. So take a look at the first pages your visitors are likely to land on and see how they look on each device."}}, {"title": {"value": "3) Speed Matters"}, "text": {"value": "When you start simplifying your site for mobile, you’ll also likely see your loading times improve. Ideally, your site should load within about 4 seconds, less whenever possible. People’s attention spans are short, and often they simply want a quick answer."}, "price": {"value": "Similarly, compress any pictures so that they load as quickly as possible. You don’t need to do this manually; there are plugins that can do this for you on all platforms (such as Smush on WordPress)."}}, {"img": {"value": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20210129/ueditor/25/smush.png", "alt": "Smush Dashboard on WordPress"}}, {"title": {"value": "4) Delay (or Ditch) Popups"}, "text": {"value": "This is a good idea generally, but on mobile, it’s more important. If you have a popup on your site that normally pops up within the first 1-5 seconds of a customer being on your site, delay it a little longer. When it pops up on the desktop, we find it easy to see some of the sites around the popup or exit out of it, but this is often difficult on mobile. If it’s too difficult, you’ll lose people. So make sure your popup is easily exited out of on mobile, and consider delaying it a little longer. That way, someone can decide if they’re in the right place before you hit them with an offer."}, "price": {"value": "Alternatively, ditch the popup altogether if it’s hindering the experience."}}, {"title": {"value": "5) Avoid Busy Layouts"}, "text": {"value": "A busy layout is the go-to in certain niches, but it’s best to choose something much more minimalistic for your mobile site. Make sure you don’t have ads sliding in from all over the place, avoid cursive fonts that may be too small to read easily, and make sure the text and background colors are easy to read. For text, a good test is to load the website on your phone and see if you can read it while strolling down the street. If you can, then your text is likely the perfect size and color."}}, {"title": {"value": "6) <PERSON> of Flash"}, "text": {"value": "Flash is old and outdated and won’t work on most mobile devices (in fact, you’ll have trouble finding one that does). If your site is relatively recent, it’s unlikely that it has Flash, but if it does, it’s time to upgrade your website."}}, {"img": {"value": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20210128/ueditor/25/get-rid-of-flash-image.png", "alt": "<PERSON> Rid of Flash"}}, {"title": {"value": "7) Keep it Tappable"}, "text": {"value": "Make sure all the buttons are big enough to click on for a thumb. Don’t crowd buttons or links together. People don’t want to be led somewhere they didn’t want to go, so give them a chance of touching the button or link they meant. If a viewer has to use a finger and thumb to expand their view, then that’s the website designer’s fault and shouldn’t happen. Also, consider your audience – what age are they? If your audience or target customer is in an older generation, consider sizing everything up a little more."}}, {"title": {"value": "8) Stick to One Core CTA"}, "text": {"value": "If you have a service-based business and not a store with several different products to browse, make your CTA (call-to-action) as clear as possible. If you’re a plumber, make your core CTA a clickable phone number so they can get in contact with you quickly. If you want them to register for a call-back or to schedule a demo, make sure that’s clear and easy to do, too."}}, {"title": {"value": "9) Test, Test, Test"}, "text": {"value": "However you or your designer build a website, there is only one way to know if it has been optimized for mobile devices – physically check. You must not assume that everything works on all devices; you have to test if that is true. Test on various mobile devices at each stage of the design process and encourage friends, family, and employees to do the same. It is very easy to miss something. There are bound to be anomalies, but they can’t be addressed if you are not aware of them."}, "price": {"value": "Constant testing needs to carry on even after the site is live. The technology of hardware and software is changing all the time; what works today may not tomorrow. Don’t assume everything is okay, test. There is a new iteration of every mobile phone each year, operating systems are constantly upgrading, and the only way to stay on top of the situation is by constantly checking and evaluating."}}, {"text": {"value": "These nine tips will help ensure that your website is as compelling on a mobile device as it is on a computer. You may experience the odd hic-cup when optimizing a website for the myriad of devices available to the consumer, so backup your files and reach out to a designer or developer if you’re struggling."}, "price": {"value": "Mobile devices are not going away anytime soon, and if you want your internet presence to provide your visitors with a memorable experience, make sure your mobile experience is just as good as the experience your desktop website offers."}}, {"img": {"value": "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20211008/ueditor/25/gsjj-logo-with-tail-1.png", "alt": "gs-jj logo"}, "imgStyle": {"width": "8em"}}]}]