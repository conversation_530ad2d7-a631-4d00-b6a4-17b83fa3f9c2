<template>
	<el-drawer :visible="artClipartDrawer" @update:visible="update" custom-class="templateCategoryDrawerWrap drawerWrap" :with-header="false" size="90%" direction="btt" @close="$emit('close')">
		<div class="drawerContent templateCategoryContent">
			<topBar :title="artContentTitle" :show-back-icon="showArtBackIcon" @back="artBack" @close="showDefault"></topBar>
			<div style="padding: 10px">
				<search-input v-model="artForm.likeQuery" :extend-list="artState ? artRecordList : artExtendList" @input="inputArtValue" @search="searchArt" @focus="artExtend"></search-input>
			</div>
			<div class="art-category">
				<div class="con" scrollbar v-show="artStatus === 1">
					<div class="box-item" :class="['box-item-style' + artDisplayControl]" @click="selectArtCategory({ id: null, name: lang.all })">
						<b class="icon-a-T-Allzhuanhuan"></b>
						<span class="item-name">{{ lang.all }}</span>
					</div>
					<div class="box-item" :class="['box-item-style' + item.displayControl]" v-for="item in artCategoryList" :key="item.id" @click="selectArtCategory(item)">
						<div class="imgWrap">
							<img v-if="item.displayControl === 1" loading="lazy" :src="item.icon" :alt="item.clipartTypeName" />
							<img v-if="item.displayControl === 2" loading="lazy" :src="item.displayDrawing" :alt="item.clipartTypeName" />
						</div>
						<span class="item-name">{{ item.clipartTypeName }}</span>
					</div>
				</div>
				<div v-show="artStatus === 2">
					<div class="clipart-box-item" v-show="showArtCategory">
						<div class="item-div">
							<div v-for="item in artCategoryList" :key="item.id" class="clipart-text" @click="selectClipart(item)">
								<div :class="item.id == artForm.clipartTypeId ? 'item-name2' : 'item-name'">
									{{ item.clipartTypeName }}
								</div>
							</div>
						</div>
					</div>
					<div class="artList" :style="showArtCategory ? 'height:550px' : 'height:605px'" v-loadmore="loadArt" :infinite-scroll-disabled="disabledLoadArt">
						<div class="clipart-img" :class="{ noData: !artList.length }">
							<div class="list-item linear-gradient-1" v-for="item in artList" :key="item.id" :title="item.elementName">
								<cliparts :item="item" :checked="0" :clipartId="item.id" :isCollection="item.isCollection" :clipartImage="item.clipartImage" @addArtFavorite="addArtFavorite" @addImg="addImg"></cliparts>
							</div>
							<noResult v-if="!artList.length && !loadingArt"></noResult>
						</div>
						<div class="loadMore" v-show="loadingArt">{{ lang.loading }}...</div>
					</div>
				</div>
			</div>
		</div>
	</el-drawer>
</template>
<script>
import cliparts from "@/components/MyDzxt/cliparts.vue";
import dzMixin from "@/mixins/dzMixin";
import topBar from "@/components/MyDzxt/topBar.vue";
import searchInput from "@/components/MyDzxt/searchInput.vue";
import { getArtCateList, getArtList, favoriteClipart, likeAllString, likeQuery } from "@/api/newDzxt";
import noResult from "@/components/MyDzxt/noResult.vue";

export default {
	props: ["artClipartDrawer"],
	mixins: [dzMixin],
	components: {
		noResult,
		searchInput,
		topBar,
		cliparts,
	},
	data() {
		return {
			isArtList: [],
			loadingArt: false,
			showArtCategory: false,
			artState: false,
			artRecordList: [],
			artExtendList: [],
			artCategoryList: [],
			artCategoryListCopy: [],
			artStatus: 1,
			artDisplayControl: 0,
			artList: [],
			artForm: {
				page: 1,
				pageSize: 60,
				clipartTypeId: null,
				total: 0,
				pages: 1,
				likeQuery: "",
			},
			artTagList: [],
		};
	},
	computed: {
		lang() {
			return this.$store.getters.lang?.design;
		},
		artContentTitle() {
			let obj = {
				1: this.lang.officalCliparts,
				2: this.clipartTypeName,
			};
			return obj[this.artStatus];
		},
		showArtBackIcon() {
			let obj = {
				1: false,
				2: true,
				3: false,
				4: true,
			};
			return obj[this.artStatus];
		},
		noMoreArt() {
			return this.artForm.page >= this.artForm.pages;
		},
		disabledLoadArt() {
			return this.loadingArt || this.noMoreArt;
		},
		isLogin() {
			return this.$store.getters.isLogin;
		},
		userId() {
			return this.$store.state.userInfo?.id;
		},
	},
	watch: {
		isLogin() {
			this.getArtList();
		},
	},
	methods: {
		async addImg({ src, property }) {
			this.showDefault();
			this.$store.commit("design/set_loading", true);
			await this.canvas.addImg(src, { artId: property });
			this.$store.commit("design/set_loading", false);
		},
		//滚动加载
		loadArt() {
			this.artForm.page++;
			this.getArtList("scroll");
		},
		//收藏元素
		addArtFavorite(item) {
			if (!this.isLogin) {
				this.$store.commit("setLogin", "login");
				return false;
			}
			favoriteClipart({
				clipartId: item.id,
			}).then((res) => {
				item.isCollection = item.isCollection ? false : true;
			});
		},
		selectClipart(item) {
			this.artForm.clipartTypeId = item.id;
			this.getArtList();
		},
		selectArtCategory(item) {
			this.artList = [];
			this.artForm.page = 1;
			this.artForm.clipartTypeId = item.id;
			this.artForm.likeQuery = "";
			this.artExtendList = [];
			this.clipartTypeName = item.clipartTypeName || this.lang.clipartsAll;
			this.isArtList = item.clipartTypeList;
			if (item.clipartTypeList && item.clipartTypeList.length > 0) {
				this.artStatus = 2;
				this.artCategoryList = item.clipartTypeList;
				this.showArtCategory = true;
				this.getArtList();
			} else if (item.clipartTypeList && item.clipartTypeList.length === 0) {
				this.artStatus = 2;
				this.showArtCategory = false;
				this.getArtList();
			} else if (item.name && item.name === this.lang.all) {
				this.artStatus = 2;
				this.showArtCategory = false;
				this.getArtList();
			}
		},
		searchArt(val) {
			this.setLocalStorage("artRecord", val);
			this.artList = [];
			this.artForm.page = 1;
			this.artForm.clipartTypeId = null;
			this.artForm.likeQuery = val;
			this.artStatus = 2;
			this.showArtCategory = false;
			this.getArtList();
		},
		//关闭
		showDefault() {
			this.$emit("closeDraw");
			this.artForm.clipartTypeId = null;
			this.artStatus = 1;
			this.artForm.likeQuery = "";
			this.artCategoryList = this.artCategoryListCopy;
		},
		//回退
		artBack() {
			this.artForm.likeQuer = "";
			this.artStatus = 1;
			this.artCategoryList = this.artCategoryListCopy;
		},
		inputArtValue(val) {
			if (val && this.artTagList.length > 0) {
				if (val.trim().length == 1) {
					this.artExtend(true);
					return;
				}
				const regex = new RegExp(`^${val}`, "gi");
				this.artExtendList = this.artTagList
					.filter((item) => item.match(regex))
					.map((item) => {
						return {
							value: item,
							icon: false,
						};
					});
				this.artState = false;
			} else {
				this.artExtend(true);
				this.searchArt(val);
				this.artExtendList = [];
			}
		},

		getArtList(type = "select") {
			this.loadingArt = true;
			if (this.artForm.likeQuery) {
				likeQuery(
					Object.assign({}, this.artForm, {
						userId: this.userId,
						quoteName: this.$store.state.design?.pageInfo?.quoteCateName,
					})
				).then((res) => {
					if (type === "scroll") {
						let list = res.data.collectionList;
						list.forEach((item) => {
							item.imgUrl = item.clipartImage;
						});
						this.artList = this.artList.concat(list);
						this.artForm.total = res.data.total;
						this.artForm.pages = res.data.pages;
						this.loadingArt = false;
					} else if (type === "select") {
						let list = res.data.collectionList;
						list.forEach((item) => {
							item.imgUrl = item.clipartImage;
						});
						this.artList = list;
						this.artForm.total = res.data.total;
						this.artForm.pages = res.data.pages;
						this.loadingArt = false;
					}
				});
			} else {
				getArtList(
					Object.assign({}, this.artForm, {
						userId: this.userId,
					})
				).then((res) => {
					if (type === "scroll") {
						let list = res.data.records;
						list.forEach((item) => {
							item.imgUrl = item.clipartImage;
						});
						this.artList = this.artList.concat(list);
						this.artForm.total = res.data.total;
						this.artForm.pages = res.data.pages;
						this.loadingArt = false;
					} else if (type === "select") {
						let list = res.data.records;
						list.forEach((item) => {
							item.imgUrl = item.clipartImage;
						});
						this.artList = list;
						this.artForm.total = res.data.total;
						this.artForm.pages = res.data.pages;
						this.loadingArt = false;
					}
				});
			}
		},

		//获取列表
		getArtCateList() {
			getArtCateList({
				id: 1,
			}).then((res) => {
				let list = res.data;
				this.artCategoryList = this.artCategoryListCopy = list;
				this.artDisplayControl = list[0].displayControl;
			});
		},
		update(val) {
			this.$emit("update:artClipartDrawer", val);
		},
	},
	mounted() {
		this.getArtCateList();
		likeAllString().then((res) => {
			this.artTagList = res.data;
		});
	},
};
</script>
<style lang="scss" scoped>
@import "@/assets/css/dzxt_theme";

::v-deep .topBar .back-icon {
	font-size: 16px;
}

.drawerContent {
	.topBar {
		height: 40px;
		border-bottom: 0;

		::v-deep .close-icon {
			right: 14px;
			font-size: 12px;
		}
	}

	.art-category {
		padding: 0 12px;
		height: 605px;
		overflow: auto;

		.con {
			display: grid;
			grid-template-columns: repeat(3, 1fr);
			grid-column-gap: 10px;
			grid-row-gap: 5px;
			align-items: center;

			.box-item {
				font-size: 12px;
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				aspect-ratio: 156/108;
				background-color: #f5f5f5;
				border-radius: 10px;
				border: 1px solid transparent;
				transition: all 0.3s;
				cursor: pointer;

				.imgWrap {
					width: 34px;
					height: 34px;

					img {
						width: 100%;
						height: 100%;
						object-fit: contain;
					}
				}

				b {
					color: $dz-primary;
					font-size: 34px;
				}

				.item-name {
					margin-top: 10px;
					text-align: center;
				}

				&.active {
					background: linear-gradient(0deg, #f8fcff 0%, #eff7ff 100%);
					box-shadow: 0 3px 5px 0 rgba(0, 0, 0, 0.24);
					border-color: $dz-primary;
				}

				&:hover {
					background: linear-gradient(0deg, #f8fcff 0%, #eff7ff 100%);
					box-shadow: 0 3px 5px 0 rgba(0, 0, 0, 0.24);
					border-color: $dz-primary;
				}
			}

			.box-item.box-item-style0 {
				aspect-ratio: 156/60;

				b {
					display: none;
				}

				.imgWrap {
					display: none;
				}

				.item-name {
					margin-top: 0;
				}
			}
		}

		.clipart-box-item {
			width: 100%;
			height: 50px;
			overflow: auto;

			.item-div {
				display: flex;
				width: 140%;

				.clipart-text {
					line-height: 31px;
					margin-right: 8px;
					text-align: center;

					.item-name {
						width: auto;
						padding: 0 10px;
						font-size: 12px;
					}

					.item-name2 {
						width: auto;
						padding: 0 10px;
						font-size: 12px;
						border: 1px solid #2a96fa;
						background: linear-gradient(0deg, #f8fcff 0%, #eff7ff 100%);
						box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.15);
						border-radius: 5px;
					}
				}
			}
		}

		.clipart-box-item::-webkit-scrollbar {
			width: 0;
		}

		.artList {
			height: 550px;
			overflow: auto;

			.clipart-img {
				display: grid;
				grid-template-columns: repeat(3, 1fr);
				grid-column-gap: 5px;
				grid-row-gap: 5px;

				&.noData {
					display: flex;
					justify-content: center;
					align-items: center;
				}

				.list-item {
					position: relative;
					display: flex;
					justify-content: center;
					align-items: center;
				}
			}
		}
	}
}
</style>