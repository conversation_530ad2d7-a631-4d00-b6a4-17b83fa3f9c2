<template>
	<div class="quoteWrap">
		<Preloader v-if="isLoading"></Preloader>
		<div class="containerWrap" v-else>
			<QuoteNav :pid="pid" :title="navTitle" style="margin-bottom: 10px; padding-top: 20px"></QuoteNav>
			<div class="content" :class="{hideBg:!twentyFourHours}" :style="{ backgroundImage: `url(${configJson && configJson.backgroundImg})` }">
				<div class="leftArea">
					<h2 :style="twentyFourHours || (!twentyFourHours && device === 'mb') ? 'color:#fff' : 'color:#333'">
						{{ configJson.descTitle_translate }}
					</h2>
					<div class="des" :style="twentyFourHours || (!twentyFourHours && device === 'mb') ? 'color:#fff' : 'color:#333'">
						<p v-for="(item, index) in description" :key="index">{{ item.str }}</p>
					</div>
					<div v-if="!twentyFourHours" class="video">
						<OrnamentsVideo ref="videoPlayer" class="background-video" disabled-mouse :options="getVideoOptions(productVideo, posterImage)"></OrnamentsVideo>
					</div>
					<div class="swiper-area" v-if="twentyFourHours">
						<div class="myswiper2">
							<div class="swiper" ref="swiper2">
								<div class="swiper-wrapper">
									<div class="swiper-slide" v-for="(item, index) in imageJson" :key="index">
										<div v-if="item.url.endsWith('.mp4')">
											<video :src="item.url" muted="muted" controls playsinline loop style="width: 749px; max-height: 394px"></video>
										</div>
										<img :src="item.url" :alt="item.alt" :title="item.alt" />
									</div>
								</div>
							</div>
							<div class="swiper-button-next">
								<img src="https://static-oss.gs-souvenir.com/web/quoteManage/20240905/Right_Toggle_Button_20240905KiJQ6t.png" alt="rightBtn" />
							</div>
							<div class="swiper-button-prev">
								<img src="https://static-oss.gs-souvenir.com/web/quoteManage/20240905/Left_Toggle_Button_20240905wjrTGT.png" alt="leftBtn" />
							</div>
						</div>
						<div class="myswiper1">
							<div class="swiper" ref="swiper1">
								<div class="swiper-wrapper">
									<div class="swiper-slide" v-for="(item, index) in imageJson" :key="index">
										<img :src="item.urlThumb" :alt="item.alt" :title="item.alt" />
									</div>
								</div>
							</div>
						</div>
					</div>
					<div class="btnWrap" v-if="twentyFourHours">
						<button @click.stop="selectShape" :disabled="disabledBtn">{{ lang.selectShape }}</button>
					</div>
				</div>
				<div class="rightArea">
					<div class="quoteContent" ref="excludeElement">
						<NewQuoteStep ref="quoteStep" :pid="pid" :cateId="cateId"></NewQuoteStep>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import QuoteNav from "@/components/Medals/QuoteNav.vue";
import Preloader from "@/components/Quote/Preloader.vue";
import StepBar from "@/components/modal/Quote/QuoteComponents/StepBar.vue";
import NewQuoteStep from "@/components/Quote/QuickQuote/NewQuoteStep.vue";
import {getQuickQuoteConfig} from "assets/js/quickQuoteConfig";
import { getInfo } from "@/api/pins";
import { scrollToViewCenter } from "@/utils/utils";
import OrnamentsVideo from "@/components/Ornaments/index.vue";

export default {
	components: {
        NewQuoteStep,
		StepBar,
		Preloader,
		QuoteNav,
		OrnamentsVideo,
	},
	data() {
        const config = getQuickQuoteConfig.call(this, this.$route.name);
		return {
            activeIndex: 0,
			disabledBtn: false,
			isLoading: true,
			description: [],
			imageJson: [],
			configJson: null,
			currentStep: 1,
			...config,
		};
	},
	computed: {
		twentyFourHours() {
			return this.pid === 627;
		},
		lang() {
			return this.$store.getters.lang.quote || {};
		},
		device() {
			return this.$store.state.device;
		},
		posterImage() {
			return this.device !== "mb" ? "https://static-oss.gs-souvenir.com/web/quoteManage/20241230/custom_shape_rush_medal_2066a8thba.png" : "https://static-oss.gs-souvenir.com/web/quoteManage/20241230/custom_shape_rush_printed_medals_206653wpDz.png";
		},
		productVideo() {
			return this.device !== "mb" ? "https://static-oss.gs-souvenir.com/web/quoteManage/20241230/rush_printed_medals_within_48_hours_2066W6wXrP.mp4" : "https://static-oss.gs-souvenir.com/web/quoteManage/20241230/48_hours_rush_medals_2066HNGbRW.mp4";
		},
	},
	methods: {
		getVideoOptions(url, img) {
			return {
				autoplay: false,
				controls: false,
				muted: true,
				loop: false,
				fill: true,
				preload: "auto",
				poster: img,
				sources: [
					{
						src: url,
						type: "video/mp4",
					},
				],
			};
		},
		selectShape() {
			scrollToViewCenter(document.querySelector(".quoteContent"));
			this.$refs.quoteStep.initQuote({
				shapeId: this.imageJson[this.activeIndex].name,
			});
			this.$refs.quoteStep.initShowSelected();
			this.disabledBtn = true;
		},
		initSwiper() {
			this.myswiper1 = new Swiper(this.$refs.swiper1, {
				slidesPerView: 4,
				spaceBetween: 8,
				watchSlidesVisibility: true, //防止不可点击
				grabCursor: true,
			});
			this.myswiper2 = new Swiper(this.$refs.swiper2, {
				slidesPerView: 1,
				spaceBetween: 10,
				autoplay: false,
				grabCursor: true,
				observer: true,
				observeParents: true,
				thumbs: {
					swiper: this.myswiper1,
				},
				navigation: {
					nextEl: ".myswiper2 .swiper-button-next",
					prevEl: ".myswiper2 .swiper-button-prev",
				},
				on: {
					slideChangeTransitionEnd: (val) => {
						this.activeIndex = val.activeIndex;
						this.$refs.quoteStep.initQuote({
                            shapeId: this.imageJson[this.activeIndex].name,
						});
						this.$refs.quoteStep.startAnimation();
						this.disabledBtn = false;
					},
				},
			});
		},
	},
	async mounted() {
		let res = await getInfo({ id: this.cateId });
		this.isLoading = false;
		this.description = res.data.description ? JSON.parse(res.data.description) : [];
		this.imageJson = res.data.imageJson ? JSON.parse(res.data.imageJson) : [];
		this.configJson = res.data.configJson ? JSON.parse(res.data.configJson) : [];
		this.$nextTick(() => {
			this.initSwiper();
		});
	},
};
</script>

<style scoped lang="scss">
button[disabled] {
	pointer-events: none;
	opacity: 0.7;
}

@media screen and (max-width: 767px) {
	::v-deep .vjs-paused .vjs-big-play-button {
		display: block !important;
	}
}

@include respond-to(mb) {
	::v-deep .video-js .vjs-big-play-button {
		display: block !important;
	}

	::v-deep .vjs-paused .vjs-big-play-button {
		display: block !important;
	}
	::v-deep .showPlayBtn .vjs-has-started.vjs-playing .vjs-big-play-button {
		display: none !important;
	}
	::v-deep .videoBox::after {
		display: none;
	}
}

::v-deep .showPlayBtn .vjs-has-started.vjs-paused .vjs-big-play-button {
	display: block !important;
}

::v-deep .showPlayBtn .vjs-has-started.vjs-playing .vjs-big-play-button {
	display: none !important;
}

.quoteWrap {
	font-family: var(--text-family), Calibri, Arial, serif;

	.content {
		position: relative;
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 6.25em;
		background-size: cover;
		background-position: center;
		background-repeat: no-repeat;
		padding: 5em max(calc(50% - 860px), 1.5vw);
		background-color: #cccccc;

		@include respond-to(mb) {
			grid-template-columns: repeat(1, 1fr);
			gap: 2em;
            padding: 5em max(calc(50% - 860px), 1.5vw) 1em;
            &.hideBg{
                background: #bebebe;
                background-image: none !important;
            }
		}

		.background-video {
			position: absolute !important;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;

			::v-deep .video-js video {
				object-fit: cover !important;
			}

			::v-deep .vjs-big-play-button {
				left: 20%;
				top: 60%;
				font-size: 32px;
				width: 49px;
				height: 47px;
				@include respond-to(mb) {
					left: 56%;
					top: 58%;
				}
			}

			@include respond-to(mb) {
				height: 53% !important;
			}
		}

		.leftArea {
			min-width: 0;
			color: #fff;
			@include respond-to(mb) {
				.video {
					height: 78vw;
				}
			}

			.btnWrap {
				text-align: center;

				button {
					width: 14em;
					height: 2.5em;
					border-radius: 6px;
					color: #ffffff;
					font-size: 1em;
					border: none;
					cursor: pointer;
					background-color: $color-primary;
				}
			}

			h2 {
				margin-bottom: 0.4em;
				text-align: left;
				font-size: 2.25em;
				font-weight: 700;
				color: #fff;
				position: relative;
				bottom: 40px;
				z-index: 2;
				@include respond-to(mb) {
					text-align: center;
				}
			}

			.des {
				padding-right: 1em;
				position: relative;
				bottom: 45px;
				z-index: 2;

				p {
					margin-bottom: 0.625em;
				}

				@include respond-to(mb) {
					text-align: center;
					padding: 0;
				}
			}

			.swiper-area {
				margin: 1.25em 0;
				width: 100%;

				.myswiper2 {
					width: 90%;
					margin: 0 auto;
					position: relative;

					@include respond-to(mb) {
						width: 100%;
					}

					.swiper-slide {
						display: flex;
						justify-content: center;
						align-items: center;

						::v-deep .pic-img .img-container {
							height: 20em;
							@include respond-to(mb) {
								height: 15em;
							}
						}
					}

					.swiper-button-next::after,
					.swiper-button-prev::after {
						display: none;
					}

					.swiper-button-next img,
					.swiper-button-prev img {
						width: 5em;
						@include respond-to(mb) {
							width: 2em;
						}
					}
				}

				.myswiper1 {
					margin: 1.25em auto 0;
					width: 70%;

					@include respond-to(mb) {
						padding: 0;
						width: 100%;
					}

					.swiper-slide {
						display: flex;
						justify-content: center;
						align-items: center;
						border-radius: 8px;
						border: 1px solid transparent;
						width: 100%;
						height: 5em;
						padding: 0.625em 0;
						background-color: rgba(0, 0, 0, 0.25);
						box-sizing: border-box;
						cursor: pointer;

                        img{
                            width: 100%;
                            height: 100%;
                            object-fit: contain;
                        }

						@include respond-to(mb) {
							background-color: rgba(255, 255, 255, 0.25);
						}

						&.swiper-slide-thumb-active {
							border-color: $color-primary;
						}
					}
				}
			}
		}

		.rightArea {
			min-width: 0;

			@include respond-to(mb) {
				.stepBar {
					display: none;
				}
			}
		}
	}
}
</style>