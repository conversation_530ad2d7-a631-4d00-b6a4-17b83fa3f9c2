<template>
	<div>
		<div class="search-tips" v-show="!keyword">{{ o.searchTips.value }}</div>
		<div class="search-box" :class="{ 'search-box--unfocused': !keyword }">
			<div class="search">
				<b class="icon-sousuo1"></b>
				<input type="text" :placeholder="o.search.value" v-model.trim="keyword" @input="onSearch" />
				<b v-show="keyword" class="icon-a-chahao6 clear" @click="clearSearch"></b>
			</div>
		</div>

		<div class="search-result-box" v-show="keyword">
			<div class="search-list" v-show="searchResult.length > 0">
				<div class="search-item" v-for="item in searchResult" :key="item.id">
					<Pic class="img" :src="item.imageUrl" :alt="item.name" />
					<div class="title">{{ item.name }}</div>
					<div class="backing-item" v-for="backing in item.backingList" :key="backing.id">
						<p>{{ backing.name }}</p>
						<b
							class="icon-fangda4"
							v-on:[eventType]="openPopover($event, backing)"
							@mouseleave="closePopover"
						></b>
					</div>
				</div>
			</div>

			<div class="search-empty" v-if="showSearchEmpty">
				<Pic :src="o.searchEmpty?.icon" />
				<div>{{ o.searchEmpty?.value }}</div>
			</div>

			<el-popover ref="myPopover" placement="right">
				<div class="show-backing">
					<Pic class="img" :src="showBacking.imageUrl" :alt="showBacking.name" />
					<p class="title">{{ showBacking.name }}</p>
				</div>
			</el-popover>
		</div>

		<div class="backing-box" v-show="!keyword">
			<div class="swiper">
				<div class="swiper-wrapper">
					<div class="swiper-slide" v-for="item in list" :key="item.id">
						<div class="arc arc--left"></div>
						<div class="arc arc--right"></div>

						<pic class="img" :src="item.imageUrl" :alt="item.name" />
						<div class="title">{{ item.name }}</div>
					</div>
				</div>
			</div>
			<div class="swiper-button-prev"></div>
			<div class="swiper-button-next"></div>
		</div>

		<div class="fabric-box" v-show="!keyword">
			<div class="fabric-tips">{{ o.tips?.value }}</div>

			<div class="fabric-list">
				<div class="fabric-item" v-for="(l, li) in fabricList" :key="li">
					<pic :src="l.imageUrl" :alt="l.name" />
					<div>{{ l.name }}</div>
				</div>
			</div>
		</div>

		<div class="btn-box" v-if="o.button || o.button1">
			<button
				v-if="o.button?.value"
				:title="o.button?.alt"
				:primary="!o.button?.outline"
				:outline="o.button?.outline"
				:style="{ ...modal.btnStyle }"
				@click="setModalType(o.button, modal.outer, 'button', { ...o.button })"
			>
				<EditDiv tagName="label" v-model:content="o.button.value" />
			</button>

			<button
				v-if="o.button1?.value"
				:title="o.button1?.alt"
				:primary="!o.button1?.outline"
				:outline="o.button1?.outline"
				:style="{ ...modal.btn1Style }"
				@click="setModalType(o.button1, modal.outer, 'button', { ...o.button1 })"
			>
				<EditDiv tagName="label" v-model:content="o.button1.value" />
			</button>
		</div>
	</div>
</template>

<script>
import { debounce } from "@/utils/utils";
import { getFabricList } from "@/api/web";

export default {
	name: "PatchesBacking",

	props: {
		modal: { type: Object, default: () => ({}) },
		list: { type: Array, default: () => [] },
	},

	data() {
		return {
			keyword: "",
			initialSlide: 2,

			searchLoading: false,
			searchResult: [],
			showBacking: {},
		};
	},

	computed: {
		isMobile() {
			return this.$store.getters.isMobile;
		},
		eventType() {
			return this.isMobile ? "click" : "mouseenter";
		},
		o() {
			return this.modal.outer[0] || {};
		},
		fabricList() {
			return this.list[this.initialSlide] ? this.list[this.initialSlide].fabricList : [];
		},
		showSearchEmpty() {
			return !this.searchLoading && this.searchResult.length <= 0;
		},
	},

	methods: {
		setModalType(target, targetArray, clickType, event, other) {
			this.$setModal(this, target, targetArray, clickType, event, other);
		},

		onSearch() {
			this.searchLoading = true;
			this.debounceSearchFabric();
		},

		clearSearch() {
			this.keyword = "";
			this.searchResult = [];
		},

		searchFabric() {
			getFabricList({
				keyword: this.keyword,
			})
				.then(({ data }) => {
					this.searchResult = data;
				})
				.finally(() => {
					this.searchLoading = false;
				});
		},

		initSwiper() {
			this.mySwiper = new Swiper(this.$el.querySelector(".swiper"), {
				loop: true,
				initialSlide: this.initialSlide,
				slidesPerView: this.isMobile ? 3 : 5,
				loopedSlides: this.isMobile ? 5 : 7,
				centeredSlides: true,
				slideToClickedSlide: true,
				navigation: {
					nextEl: this.$el.querySelector(".swiper-button-next"),
					prevEl: this.$el.querySelector(".swiper-button-prev"),
				},

				on: {
					slideChangeTransitionEnd: (swiper) => {
						this.initialSlide = swiper.realIndex;
					},
				},
			});
		},

		openPopover(e, backing) {
			this.showBacking = backing;
			const reference = e.target.parentElement;
			const popper = this.$refs.myPopover;

			popper.referenceElm = reference;
			popper.popperJS && (popper.popperJS._reference = reference);
			popper.doShow();
		},

		closePopover() {
			const popper = this.$refs.myPopover;
			popper.doClose();
		},
	},

	mounted() {
		this.debounceSearchFabric = debounce(this.searchFabric, 300);
		this.initSwiper();
	},
};
</script>

<style lang="scss" scoped>
.search-tips {
	padding-bottom: 0.75rem;
	font-size: calc(1rem - 2px);
	background-color: #f0f0f0;
}
.search-box {
	display: flex;
	align-items: center;
	background-color: #fff;
	padding: 1.25rem;
	@include respond-to(mb) {
		padding: calc(1rem - 2px);
	}

	.search {
		padding: 0 1.5rem;
		display: flex;
		align-items: center;
		gap: 0.75rem;
		width: 100%;
		height: 3rem;
		border-radius: 0.5rem;
		background-color: #f1f1f1;

		input {
			width: 100%;
		}

		b {
			color: #717273;
			line-height: 1;
		}

		.clear {
			cursor: pointer;
		}
	}

	&.search-box--unfocused {
		padding: unset;

		.search {
			background-color: unset;
		}
	}
}

.backing-box {
	--swiper-navigation-size: 1.5rem;
	--swiper-navigation-color: var(--text-primary);

	position: relative;
	padding: calc(1rem - 2px) 2.5rem 0;
	background-color: #f0f0f0;
	@include respond-to(mb) {
		padding: 1rem 1.5rem 0;
	}

	.swiper {
		height: 17.875rem;
		@include respond-to(mb) {
			height: 11.8rem;
		}
	}

	.swiper-button-next,
	.swiper-button-prev {
		width: 1.75rem;
		height: 1.75rem;
		font-weight: 700;
	}
	.swiper-button-prev {
		left: 0;
	}
	.swiper-button-next {
		right: 0;
	}

	.swiper-slide {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		color: var(--text-primary);
		text-align: center;
		transform: scale(0.8);
		@include respond-to(mb) {
		}

		.img {
			max-width: 100%;
			width: 8.4375rem;
			@include respond-to(mb) {
				width: 5.667rem;
			}
		}

		.title {
			font-size: calc(1rem - 2px);
		}
	}

	.swiper-slide-active {
		border-radius: 0.5rem 0.5rem 0 0;
		background-color: #fff;
		transform: scale(1);
		@include respond-to(mb) {
		}

		.img {
			padding: 0 1rem;
			width: 12.5rem;
			@include respond-to(mb) {
				width: 8.33rem;
			}
		}

		.title {
			font-size: 1.125em;
			font-weight: 700;
			@include respond-to(mb) {
				font-size: 1em;
			}
		}

		.arc {
			position: absolute;
			bottom: 0;
			width: 2.5rem;
			height: 2.5rem;
			background-color: #fff;
			transform: unset;
			@include respond-to(mb) {
				width: 1.5rem;
				height: 1.5rem;
			}
			&::before {
				content: "";
				position: absolute;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				background-color: #f0f0f0;
			}
		}

		.arc--left {
			left: -2.5rem;
			@include respond-to(mb) {
				left: -1.5rem;
			}
			&::before {
				border-bottom-right-radius: 1rem;
			}
		}
		.arc--right {
			right: -2.5rem;
			@include respond-to(mb) {
				right: -1.5rem;
			}
			&::before {
				border-bottom-left-radius: 1rem;
			}
		}
	}
}

.fabric-box {
	padding: 1.25rem;
	background-color: #fff;
	@include respond-to(mb) {
		padding: calc(1rem - 2px);
	}

	.fabric-tips {
		margin-bottom: 1.25rem;
		display: flex;
		align-items: center;
		justify-content: center;
		gap: 0.5rem;
		height: 3.25rem;
		font-weight: 700;
		background: no-repeat center / cover
			url("https://static-oss.gs-souvenir.com/web/20250407/backdrop_20250407eKj7Jb.jpg");
		@include respond-to(mb) {
			background-image: url("https://static-oss.gs-souvenir.com/web/20250407/backdrop_20250407xfMH8F.jpg");
		}

		&::before {
			content: "";
			display: block;
			width: 1.5rem;
			height: 1.5rem;
			background: no-repeat center / cover
				url("https://static-oss.gs-souvenir.com/web/20250408/Here_are_the_fabrics_that_match_your_backing_choice_20250408KxJ3Fn.png");
		}
	}

	.fabric-list {
		display: grid;
		gap: calc(1rem - 2px);
		grid-template-columns: repeat(4, 1fr);
		@include respond-to(mb) {
			grid-template-columns: repeat(2, 1fr);
		}
	}

	.fabric-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 0.5rem;
		text-align: center;

		& > img {
			border-radius: 0.375rem;
		}

		& > div {
			font-size: 1em;
			font-weight: 700;
			color: var(--text-primary);
		}
	}
}

.btn-box {
	padding-top: 1.25rem;
	padding-bottom: 3.75rem;
	display: flex;
	gap: 2rem;
	justify-content: center;
	background-color: #fff;
	@include respond-to(mb) {
		padding-bottom: 1.25rem;
	}
}

.search-result-box {
	padding: 0 1.25rem;
	min-height: 22rem;
	display: flex;
	align-items: center;
	background-color: #fff;
	@include respond-to(mb) {
		padding: 0 calc(1rem - 2px);
	}
}

.search-list {
	display: grid;
	gap: calc(1rem - 2px);
	grid-template-columns: repeat(4, 1fr);
	@include respond-to(mb) {
		grid-template-columns: repeat(2, 1fr);
	}
}

.search-item {
	display: flex;
	flex-direction: column;
	padding: 0.5rem 0.5rem 0.75rem;
	border-radius: 0.5rem;
	background-color: #f6f6f6;

	.img {
		border-radius: 0.375rem;
	}

	.title {
		padding: 0.75rem;
		font-size: 1em;
		font-weight: 700;
		text-align: center;
		border-bottom: 1px solid #e3e3e3;
	}

	.backing-item {
		margin-top: 0.75rem;
		padding: 0 0.25rem;
		display: flex;
		align-items: center;
		gap: 0.375rem;
		font-size: 1em;

		&::before {
			content: "";
			width: 0.625rem;
			height: 0.625rem;
			background: no-repeat center / cover
				url("https://static-oss.gs-souvenir.com/web/20250408/Checkmark_20250408bNfC4M.png");
		}

		p {
			flex: 1;
		}

		b {
			color: #666;
			&:hover {
				color: #ff8224;
			}
		}
	}
}

.search-empty {
	margin: auto;
	width: 34%;
	display: flex;
	gap: 1.5rem;
	flex-direction: column;
	align-items: center;
	text-align: center;
	img {
		width: 50%;
	}
	@include respond-to(mb) {
		width: 80%;
		img {
			width: 56%;
		}
	}
}

.show-backing {
	display: flex;
	flex-direction: column;
	align-items: center;
	width: 13.75rem;
	color: var(--text-primary);
	@include respond-to(mb) {
		width: 11.33rem;
	}
	.img {
		width: 10.75rem;
		@include respond-to(mb) {
			width: 9.5rem;
		}
	}
	.title {
		font-size: 1rem;
		font-weight: 700;
	}
}
</style>
