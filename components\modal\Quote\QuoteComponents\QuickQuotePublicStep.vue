<template>
	<div class="step-item">
		<StepTitle :title="getTitle(stepData)" :step="getStep(stepData)"></StepTitle>
		<div class="step-item-params" :style="getStepContentStyle">
			<div
				class="param-item"
				v-for="(citem, cindex) in stepData.childList"
				v-show="cindex <= 8"
				:style="{ ...stepItemConfig?.stepItemStyle?.style, ...stepItemConfig?.stepItemStyle?.[`${nowDevice}Style`] }"
				:key="citem.id"
				:class="{
					active: hasId(citem.id, selectedData[stepData.paramName]),
				}"
				@click="selectQuoteParams(stepData, citem)"
			>
				<div class="img-wrap" :style="{ ...stepItemConfig?.stepItemStyle?.stepImgWrapStyle?.style, ...stepItemConfig?.stepItemStyle?.[`${nowDevice}StepImgWrapStyle`]?.style }">
					<template v-if="hasVideo(citem)">
						<VideoPlayer disabledMouse :style="stepItemConfig.mediaConfig.style" ref="videos" :fit="stepItemConfig.videoFit" :options="getVideoOptions(citem.priceInfo.videoPath, citem.priceInfo.imagePath)"></VideoPlayer>
					</template>
					<template v-else>
						<img loading="lazy" :src="getImgSrc(citem)" :alt="citem.imageJson[0].alt" :title="citem.imageJson[0].alt" />
					</template>
					<div class="zoom extend-click-area" v-if="hasVideo(citem)" :style="{ ...stepItemConfig?.zoomIconConfig?.style, ...stepItemConfig?.[`${nowDevice}ZoomIconConfig`]?.style }" @click.stop="preview(citem.priceInfo.videoPath)">
						<img src="https://static-oss.gs-souvenir.com/web/quoteManage/20240608/bigpng_202406086dzdXr.png" alt="" />
					</div>
				</div>
				<div class="text-wrap" :style="{ ...stepItemConfig?.stepItemStyle?.stepTextWrapStyle?.style, ...stepItemConfig?.stepItemStyle?.[`${nowDevice}StepTextWrapStyle`]?.style }">
					<div class="alias" :style="{ ...stepItemConfig?.stepItemStyle?.stepTextWrapStyle?.aliasStyle?.style, ...stepItemConfig?.stepItemStyle?.stepTextWrapStyle?.[`${nowDevice}AliasStyle`]?.style }">
						<span>{{ citem.alias }}</span>
					</div>
					<div class="price" v-if="stepItemConfig.showPriceText">
						<PriceText :morePriceDataOrigin="morePriceData" :paramData="citem"></PriceText>
					</div>
				</div>
				<div class="zoom extend-click-area" v-if="stepItemConfig.zoomIconConfig.showZoomIcon" :style="stepItemConfig.zoomIconConfig.style" @click.stop="zoomPic( citem.imageJson[1] ?citem.imageJson[1].url : citem.imageJson[0].url)">
					<img src="https://static-oss.gs-souvenir.com/web/quoteManage/20240608/bigpng_202406086dzdXr.png" alt="" />
				</div>
				<b class="icon-a-icon-Editzhuanhuan edit" v-show="citem.paramName === 'Mixed'"></b>
			</div>
			<div class="param-item more" v-if="stepData.childList.length >= 10" @click="showMore">
				<span>More Template</span>
				<b class="icon-xialajiantou"></b>
			</div>
		</div>
	</div>
</template>

<script>
import StepTitle from "@/components/modal/Quote/QuoteComponents/StepTitle.vue";
import PriceText from "@/components/Quote/PriceText.vue";
import VideoPlayer from "@/components/Quote/VideoPlayer.vue";
import { getVideoUrl } from "assets/js/QuotePublic";

export default {
	props: {
        morePriceData:{
            type: Object
        },
		selectedData: {
			type: Object,
			required: true,
		},
		stepData: {
			type: Object,
			required: true,
		},
		config: {
			type: Object,
			required: true,
		},
	},
	components: { VideoPlayer, PriceText, StepTitle },
	computed: {
		proType() {
			return this.$store.state.proType;
		},
		device() {
			return this.$store.state.device;
		},
		nowDevice() {
			return this.device === "mb" ? "mb" : "pc";
		},
		lang() {
			return this.$store.getters.lang.quote || {};
		},
		getStepContentStyle() {
			try {
				return this.config?.stepContentConfig[this.device]?.style || this.config?.stepContentConfig["pc"]?.style;
			} catch (e) {
				return false;
			}
		},
		stepItemConfig() {
			return this.deepMerge(
				{
					imgBorderStyle: 1,
					showPriceText: false,
					mediaConfig: {
						style: {
							"object-fit": "contain",
						},
					},
					zoomIconConfig: {
						showZoomIcon: false,
						style: {
							color: "#ffffff",
						},
					},
					playIconConfig: {
						showPlayIcon: false,
						style: {
							color: "#ccc",
						},
					},
					connerConfig: {
						style: {
							top: 0,
							left: 0,
							right: "auto",
							transform: "translateY(0)",
						},
					},
					stepItemStyle: {
						style: {},
						pcStyle: {},
						mbStyle: {},
						stepImgWrapStyle: {
							style: {},
						},
						pcStepImgWrapStyle: {
							style: {},
						},
						mbStepImgWrapStyle: {
							style: {},
						},
						stepTextWrapStyle: {
							style: {},
						},
						pcStepTextWrapStyle: {
							style: {},
						},
						mbStepTextWrapStyle: {
							style: {},
						},
					},
				},
				this.config
			);
		},
	},
	methods: {
		preview(path) {
			let ph = getVideoUrl(path, this.proType);
			this.$store.commit("setMask", { video: true, value: ph });
		},
		hasVideo(itemData) {
			return itemData.priceInfo.videoPath && this.parseJSON(itemData.priceInfo.videoPath).length;
		},
		parseJSON(str) {
			return str
				? JSON.parse(str)
				: [
						{
							url: "",
						},
				  ];
		},
		getVideoOptions(path, poster) {
			let ph = getVideoUrl(path, this.proType);
			return {
				autoplay: true,
				controls: false,
				muted: true,
				loop: true,
				fill: true,
				sources: [
					{
						src: ph,
						type: "video/mp4",
					},
				],
			};
		},
		zoomPic(img) {
			this.$viewerApi({
				images: [img],
			});
		},
		getImgSrc(citem) {
			return citem.imageJson[1] ? citem.imageJson[1]?.url : citem.imageJson[0]?.url;
		},
		//判断是否选中
		hasId(id, arr) {
			if (!arr) {
				return false;
			}
			return !!arr.find((item) => {
				return item.id === id;
			});
		},
		showMore() {
			this.$emit("showMore");
		},
		getTitle(item) {
			if (item.customStepName === "upload") {
				return item.alias;
			} else {
				return this.lang.Select + " " + item.alias;
			}
		},
		getStep(item) {
			return this.lang.step + " " + item.customIndex;
		},
		selectQuoteParams(item, citem) {
			this.$emit("selectQuoteParams", {
				item,
				citem,
			});
		},
		deepMerge(target, source) {
			if (typeof source !== "object" || source === null) {
				return source;
			}
			if (typeof target !== "object" || target === null) {
				target = Array.isArray(source) ? [] : {};
			}
			if (Array.isArray(source)) {
				if (!Array.isArray(target)) {
					target = [];
				}
				source.forEach((sourceValue, index) => {
					const targetValue = target[index];
					if (sourceValue && typeof sourceValue === "object") {
						target[index] = this.deepMerge(targetValue, sourceValue);
					} else {
						target[index] = sourceValue;
					}
				});
			} else {
				Object.keys(source).forEach((key) => {
					const sourceValue = source[key];
					const targetValue = target[key];
					if (sourceValue && typeof sourceValue === "object") {
						target[key] = this.deepMerge(targetValue, sourceValue);
					} else {
						target[key] = sourceValue;
					}
				});
			}
			return target;
		},
	},
};
</script>

<style lang="scss" scoped>
@mixin selectedStyle {
	border-color: $color-primary;
	&::after {
		content: "\e82c";
		position: absolute;
		width: 1.38em;
		height: 1.38em;
		line-height: 1.38em;
		right: 0;
		top: 0;
		border-bottom-left-radius: 50%;
		text-align: center;
		color: #ffffff;
		background-color: $color-primary;
		font-family: "modalicon";
	}

	@include respond-to(mb) {
		&::after {
			width: 1.25em;
			height: 1.25em;
			line-height: 1.25em;
		}
	}
}

.step-item {
	grid-column: 1 / span 2;
	min-width: 0;
	font-size: 0.88em;

	.step-item-params {
		display: grid;
		grid-template-columns: repeat(4, 1fr);
		grid-gap: 0.63em;

		@include respond-to(mb) {
			grid-template-columns: repeat(2, 1fr);
			grid-gap: 0.42em;
		}

		.param-item {
			position: relative;
			min-width: 0;
			overflow: hidden;
			display: flex;
			justify-content: center;
			align-items: center;
			gap: 0.5em;
			padding: 0.2em;
			background-color: #fafafa;
			cursor: pointer;
			border-radius: 6px;
			border: 1px solid transparent;
			transition: all 0.3s;

			.img-wrap {
				position: relative;

				.zoom {
					position: absolute;
					top: 0.5em;
					right: 0.5em;
					left: auto;
					width: 1.5em;
					cursor: pointer;
				}
			}

			.text-wrap {
				word-break: break-word;
			}

			&.active {
				@include selectedStyle;

				.edit {
					display: block;
				}
			}

			.zoom {
				position: absolute;
                line-height: 1;
				top: 0.5em;
				left: 0.5em;
				width: 1.5em;
				cursor: pointer;
			}

			.edit {
				position: absolute;
				left: 4px;
				top: 4px;
				display: none;
			}
		}
	}

	@include respond-to(mb) {
		font-size: 1em;
		margin-bottom: 1em;
	}

	img {
		max-width: 100%;
		max-height: 100%;
		object-fit: contain;
		width: auto;
	}
}
</style>
