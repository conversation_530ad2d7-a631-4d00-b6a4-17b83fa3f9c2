<template>
	<div class="areaStyle mb-4">
		<div class="switch-box " ref="coinsSwitchBoxSizeStyle1">
			<div class="switch-item" @click="selectStep(item, index)" :class="{ active: index == selectIndex }"
				v-for="(item, index) in stepData.productParamList" :key="index">{{
					item.valueName }}</div>
		</div>
	</div>
</template>

<script>
export default {
	name: 'areaStyle',
	components: {},
	props: {
		stepData: {
			type: Object,
			default: () => ({}),
		},
	},
	data() {
		return {
			selectIndex: -1,
			selectItem: null,
		}
	},
	watch: {},
	computed: {},
	methods: {
		selectStep(item, index, state=false, stepType) {
			this.selectIndex=index;
			this.selectItem=item;
			this.$emit("selectStep", {
				stepType: stepType,
				type: this.stepData.attributeFlag,
				data: item,
				parent: this.stepData,
				id: this.stepData.id,
				firstSelect: state,
			});
		},
		clearData(state, areaIndex=0, stepType) {
			this.selectIndex=areaIndex;
			this.selectItem=null;
			if (areaIndex<0) return;
			if (this.stepData&&this.stepData.productParamList&&this.stepData.productParamList.length>0) {
				let item=this.stepData.productParamList[areaIndex];
				this.selectStep(item, areaIndex, state, stepType);
			}
		},
	},
	created() { },
	mounted() {
		this.$Bus.$on("clearArea", this.clearData);
		this.$Bus.$on("selectDefaultAreaStep", this.clearData);
	},
	beforeDestroy() {
		this.$Bus.$off("clearArea", this.clearData);
		this.$Bus.$off("selectDefaultAreaStep", this.clearData);
	},
}
</script>

<style scoped lang="scss">
.areaStyle {
	.switch-box {
		margin: auto;
		width: fit-content;
		max-width: 100%;
		overflow-x: auto;
		display: flex;
		align-items: center;
		justify-content: flex-start;
		column-gap: 2px;
		background: #E2E2E2;
		border: 2px solid #ebebeb;
		border-radius: 20px;

		// scrollbar-width: none;
		&::-webkit-scrollbar {
			/*滚动条整体样式*/
			width: 5px;
			height: 2px;
		}

		&.canScroll {
			justify-content: flex-start;
		}

		.switch-item {
			flex-shrink: 0;
			width: fit-content;
			min-width: 200px;
			height: 36px;
			text-align: center;
			line-height: 36px;
			border-radius: 20px;
			padding: 0 10px;
			font-weight: 400;
			font-size: 16px;
			cursor: pointer;
			transition: background 0.15s ease-in-out;
			user-select: none;

			&.active {
				font-weight: bold;
				color: #fff;
				background: $color-primary;
				box-shadow: 1px 1px 4px 0px rgba(0, 0, 0, 0.15);
			}

			&:hover {
				color: #fff;
				background: $color-primary;
			}
		}
	}

	@include respond-to(mb) {
		.switch-box {
			justify-content: flex-start;

			.switch-item {
				min-width: 100px;
				height: 26px;
				font-size: 12px;
				line-height: 26px;
			}
		}
	}
}
</style>
