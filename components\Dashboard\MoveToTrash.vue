<template>
	<v-dialog
		:value="value"
		@input="$emit('input',false)"
		max-width="523"
	>
		<v-card>
			<v-card-title class="text-h5">
				Move Site to Trash
			</v-card-title>
			<div class="px-6">
				You are about to move <strong>mable bb</strong> to Trash.<br> Once moved, this site will become unpublished and cannot be edited.
			</div>
			<v-card-actions class="pb-6">
				<v-spacer></v-spacer>
				<v-btn depressed color="error" outlined width="183" @click="$emit('input',false)">
					Cancel
				</v-btn>

				<v-btn color="error" depressed class="ml-3" width="183" @click="$emit('input',false)">
					Move to Trash
				</v-btn>
			</v-card-actions>
		</v-card>
	</v-dialog>
</template>

<script>
export default {
	props: ['value'],
	model: {
		prop: "value",
		event: "input"
	},
}
</script>

<style scoped>

</style>
