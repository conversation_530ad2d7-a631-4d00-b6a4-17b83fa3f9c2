<template>
	<div class="sizeWrap">
		<div>
			<div class="cusTitle">{{ textConfig.sizeH }}</div>
			<div class="step-item-params step-size-params">
				<div
					class="param-item"
					v-for="(citem, cindex) in itemData.childList"
					:key="citem.id"
					v-show="cindex < 5"
					:class="{
						active: sizeH.id === citem.id,
					}"
					@click="selectH(citem)"
				>
					<CustomCircle :circleType="2" :isActive="sizeH.id === citem.id"></CustomCircle>
					<span>{{ citem.alias }}</span>
				</div>
				<div class="param-item more" v-show="itemData.childList.length >= 6" @click="showMoreSize('sizeH')">More >></div>
			</div>
		</div>
		<div>
			<div class="cusTitle">{{ textConfig.sizeW }}</div>
			<div class="step-item-params step-size-params">
				<div
					class="param-item"
					v-for="(citem, cindex) in itemData.childList"
					:key="citem.id"
					v-show="cindex < 5"
					:class="{
						active: sizeW.id === citem.id,
					}"
					@click="selectW(citem)"
				>
					<CustomCircle :circleType="2" :isActive="sizeW.id === citem.id"></CustomCircle>
					<span>{{ citem.alias }}</span>
				</div>
				<div class="param-item more" v-show="itemData.childList.length >= 6" @click="showMoreSize('sizeW')">More >></div>
			</div>
		</div>
	</div>
</template>

<script>
import CustomCircle from "@/components/Quote/customCircle.vue";

export default {
	props: {
		selectedData: {
			type: Object,
			default: () => {
				return {};
			},
		},
		itemData: {
			type: Object,
			default: () => {
				return {};
			},
		},
		textConfig: {
			type: Object,
			default: () => {
				return {
					width: "",
					height: "",
				};
			},
		},
        sizeW:{
            type: Object,
            default: () => {
                return {};
            },
        },
        sizeH:{
            type: Object,
            default: () => {
                return {};
            },
        }
	},
	data() {
		return {

		};
	},
	watch: {
		sizeW: {
			handler(val) {
				if (val && !this.isEmptyObject(this.sizeH)) {
					this.handlerSize();
				}
			},
			deep: true,
		},
		sizeH: {
			handler(val) {
				if (val && !this.isEmptyObject(this.sizeW)) {
					this.handlerSize();
				}
			},
			deep: true,
		},
	},
	components: { CustomCircle },
	methods: {
        showMoreSize(type){
            this.$emit("showMoreSize",type)
        },
		isEmptyObject(obj) {
			return Object.keys(obj).length === 0 && obj.constructor === Object;
		},
		selectW(item) {
            this.$emit("update:sizeW",item)
		},
		selectH(item) {
            this.$emit("update:sizeH",item)
		},
		handlerSize() {
			let w = this.sizeW,
				h = this.sizeH;
			if (w.onlyAddInquiry || h.onlyAddInquiry) {
				if (w.onlyAddInquiry) {
					delete w.sizeAlias;
					this.$emit("clickFun", w);
				}
				if (h.onlyAddInquiry) {
					delete h.sizeAlias;
					this.$emit("clickFun", h);
				}
				return false;
			}
			if (!w.paramCode || !h.paramCode) {
                this.$emit("update:sizeW",{});
                this.$emit("update:sizeH",{})
				return false;
			}
			let sizeValue = (Number(w.paramCode) + Number(h.paramCode)) / 2,
				list = this.itemData.childList.filter((item) => item.onlyAddInquiry != 1),
				len = list.length,
				findSize;
			for (let i = len - 1; i >= 0; i--) {
				if (list[i].onlyAddInquiry) {
					continue;
				}
				if (i == 0) {
					if (sizeValue > Number(list[i].paramCode)) {
						findSize = list[Math.min(i + 1, len - 1)];
					} else {
						findSize = list[i];
					}
					break;
				} else if (sizeValue == Number(list[i].paramCode)) {
					findSize = list[i];
				} else if (sizeValue > Number(list[i].paramCode)) {
					findSize = list[Math.min(i + 1, len - 1)];
					break;
				}
			}
			if (!findSize) {
                this.$emit("update:sizeW",{});
                this.$emit("update:sizeH",{})
				return false;
			}
			findSize.sizeAlias = `Height: ${this.sizeH.alias} - Width: ${this.sizeW.alias}`;
			this.$emit("clickFun", findSize);
		}
	},
};
</script>

<style lang="scss" scoped>
.sizeWrap {
	background-color: #fafafa;
	border-radius: 10px;
	padding: 1em 1.3em;
    @include respond-to(mb){
        background-color: transparent;
        border-radius: 0;
        padding: 0;
    }

	& > div {
		margin-bottom: 2.87em;
        @include respond-to(mb){
            margin-bottom: 1em;
        }
	}

	.cusTitle {
		font-weight: 700;
		margin-bottom: 0.94em;
	}
}

.step-item-params {
	display: grid;
	align-items: center;
	grid-template-columns: 1fr 1fr;
	gap: 0.8em;

	.more {
		color: $color-primary;
	}

	@include respond-to(mb) {
		grid-template-columns: repeat(4, 1fr);
		grid-gap: 0.5em;
		background-color: transparent;
		padding: 0;
	}

	.param-item {
		display: flex;
		align-items: center;
		min-width: 0;
		cursor: pointer;
	}
}

.help {
	display: flex;
	align-items: center;
	font-size: 0.75em;
	color: $color-primary;

	b {
		margin-left: 4px;
		font-size: 0.875em;
		@include respond-to(mb) {
			font-size: 1em;
		}
	}

	@include respond-to(mb) {
		display: none;
	}
}
</style>
