<template>
	<div id="beltBucklesQuote" class="quoteWrap">
		<Preloader v-if="isLoading"></Preloader>
		<template v-else>
			<div class="content">
				<QuoteTitle :h1-text="`${lang.cy} ${cateData.cateName}`"></QuoteTitle>
				<div class="leftArea">
					<div v-for="(item, index) in filterShowGeneralData" :key="item.id">
						<component v-if="renderStepComponent(item, index)" :is="renderStepComponent(item, index).is" v-bind="renderStepComponent(item, index).props" v-on="renderStepComponent(item, index).on" :class="renderStepComponent(item, index).dynamicClass" />
					</div>
				</div>
				<div class="rightArea" id="rightAreaCustom">
					<transition enter-active-class="animate__animated animate__faster animate__fadeInBottomLeft" leave-active-class="animate__animated animate__faster animate__fadeOutBottomLeft">
						<Detail v-show="showRightArea" class="type1" :class="{ mask: maskName }" :key="1" :absoluteImg="comeFromDZ.absoluteImg" :selectedData="selectedData" :priceInfo="priceInfo" :customQty="customQty" :textInfo="textInfo" :generalData="generalData" @toPosition="toPosition" @addInquiry="debounceAddInquiry" @addCart="addCart"></Detail>
					</transition>
				</div>
			</div>
			<div class="footer" id="foot">
				<Detail class="type2" :key="3" :absoluteImg="comeFromDZ.absoluteImg" :selectedData="selectedData" :priceInfo="priceInfo" :customQty="customQty" :textInfo="textInfo" :generalData="generalData" @toPosition="toPosition" @addInquiry="debounceAddInquiry" @addCart="addCart"></Detail>
			</div>
			<el-drawer :visible.sync="showArea" :with-header="true" custom-class="drawDialog" :size="device === 'ipad' ? '728px' : '85%'">
				<Detail class="type3" :key="2" :absoluteImg="comeFromDZ.absoluteImg" :selectedData="selectedData" :priceInfo="priceInfo" :customQty="customQty" :textInfo="textInfo" :generalData="generalData" @toPosition="toPosition" @addInquiry="debounceAddInquiry" @addCart="addCart"></Detail>
			</el-drawer>
			<!-- 建议弹窗-->
			<RecomendDialog :commentsDialog.sync="commentsDialog" :paramValue="selectedParamsValue" @next="recomendNext"></RecomendDialog>
			<!-- 预览弹窗-->
			<VideoPreviewDialog :pic-dialog.sync="picDialog" :zoom-pic="zoomPic" :temp-type="tempType" :selected-params-value="selectedParamsValue" @qtyAndBtnConfirm="qtyAndBtnConfirm" @qtyAndBtnNext="qtyAndBtnNext" @dialogNextStep="dialogNextStep"></VideoPreviewDialog>
			<!-- 升级数量弹窗-->
			<BaseDialog v-model="upgradeDialog" :width="device != 'mb' ? '640px' : '320px'">
				<Upgrade @closeUpgrade="upgradeDialog = false" :tipArr="tipArr" :typeName="cateData.cateName" @upgradeLevel="upgradeLevel"></Upgrade>
			</BaseDialog>
			<!-- o2o预览页面弹窗-->
			<BaseDialog v-model="showOtoDialog" :width="device !== 'mb' ? '570px' : '90%'">
				<div class="otoWrap">
					<img src="@/assets/images/oto.png" alt="" />
					<p>{{ lang.p1 }}</p>
					<h3>{{ lang.p2 }}</h3>
					<p style="color: #666666">
						{{ lang.p3 }}<br />
						{{ lang.p4 }}.
					</p>

					<div class="box">
						<p class="t1">
							{{ lang.p5 }} <br />
							{{ lang.p6 }}!
						</p>
						<a href="https://www.o2o.co/previewWeb" style="text-decoration: none">
							<el-button type="primary">{{ lang.p7 }}</el-button>
						</a>
					</div>
				</div>
			</BaseDialog>
			<infoDialog :infoDialogVisible.sync="infoDialogVisible" :uploadList.sync="uploadList" :otherUpload="uploadArtworkList" :recomendUpload="recomendUpload" @getValue="getValueFun"></infoDialog>
			<!--    遮罩-->
			<myMask :maskName.sync="maskName"></myMask>
			<!--  手机，ipad端预览-->
			<PreviewBtn :showArea.sync="showArea"></PreviewBtn>
			<BaseDialog v-model="noFileDialog" :width="device != 'mb' ? '485px' : '90%'" :model="false">
				<template #closeIcon>
					<div style="display: none"></div>
				</template>
				<infoUpload :infoUploadList="infoUploadList" @pushInfoList="pushInfoList" @delInfoList="delInfoList" @updateInquiry="updateInquiry" @closeInfoDialog="closeInfoDialog"> </infoUpload>
			</BaseDialog>
		</template>
	</div>
</template>

<script>
import "@/plugins/element";
import myMask from "@/components/Quote/Mask";
import Detail from "@/components/Quote/Detail";
import BaseDialog from "@/components/Quote/BaseDialog";
import Upgrade from "@/components/Quote/Upgrade";
import Preloader from "@/components/Quote/Preloader";
import StepUpload from "@/components/Quote/StepUpload";
import StepTime from "@/components/Quote/StepTime";
import StepQty from "@/components/Quote/StepQty";
import infoDialog from "@/components/Medals/infoDialog";
import quoteMixin from "@/mixins/quote";
import quoteBanChoice from "@/mixins/quoteBanChoice";
import RecomendDialog from "@/components/Quote/RecomendDialog.vue";
import PreviewBtn from "@/components/Quote/PreviewBtn.vue";
import VideoPreviewDialog from "@/components/Quote/VideoPreviewDialog.vue";
import { getQuoteConfig } from "assets/js/quote/quoteConfig";
import QuoteTitle from "@/components/Quote/QuoteTitle.vue";
import PublicStep from "@/components/Quote/PublicStep/index.vue";
import PinsSizeSelect from "~/components/Quote/PinsSizeSelect.vue";
import { initStepComponentConfig } from "@/assets/js/quote/quoteStepComponent";
export default {
	head() {
		return {
			title: "Quote page",
		};
	},
	components: {
		PublicStep,
		QuoteTitle,
		VideoPreviewDialog,
		PreviewBtn,
		RecomendDialog,
		Detail,
		BaseDialog,
		Upgrade,
		myMask,
		Preloader,
		StepUpload,
		StepTime,
		StepQty,
		infoDialog,
		PinsSizeSelect,
	},
	mixins: [quoteMixin, quoteBanChoice],
	data() {
		const config = getQuoteConfig.call(this, this.$route.name);
		return {
			...config,
		};
	},
	methods: {
		renderStepComponent(item) {
			const stepComponentConfig = initStepComponentConfig.call(this, item);
			const map = {
				quoteCategory: stepComponentConfig.defaultConfig,
				"Select Belt Buckle Size": stepComponentConfig.sizeConfig,
				"Select Shape": stepComponentConfig.defaultConfig,
				"Belt Buckle Color": stepComponentConfig.defaultConfig,
				"Select Buckle Finish": stepComponentConfig.defaultConfig,
				"Additional Upgrades (Optional)": stepComponentConfig.additionalConfig,
				"Belt Buckle Back Side": stepComponentConfig.defaultConfig,
				"Select Attachment": stepComponentConfig.defaultConfig,
				"Select Packaging": stepComponentConfig.defaultConfig,
				"Upload Artwork & Comments": stepComponentConfig.uploadConfig,
				qty: stepComponentConfig.qtyConfig,
				"Select Turnaround Time": stepComponentConfig.timeConfig,
			};
			return map[item.paramName];
		},
	},
};
</script>

<style lang="scss" scoped>
@import "@/assets/css/quotePublic";
</style>
