<template>
	<div class="quoteWrap">
		<Preloader v-if="isLoading"></Preloader>
		<template v-else>
			<div class="content">
				<QuoteNav :pid="pid" title="We provide these Keychains types for you."></QuoteNav>
				<QuoteTitle :h1-text="lang.cy + ' ' + cateData.cateName" :prompts-text="lang.pins.prompts"></QuoteTitle>
				<div class="leftArea">
					<div v-for="(item, index) in filterShowGeneralData" :key="item.id" class="kk">
						<PublicStep v-if="item.paramName == 'quoteCategory' && [219, 112].includes(pid)" :selectedData="selectedData" :stepData="item" :config="allStepConfig[item.paramName]" :maskName.sync="maskName" @clickFun="selectQuoteParams(item, $event)" @viewVideo="viewVideo" :key="item.paramName"> </PublicStep>

						<PublicStep v-if="['Select Shapes', 'Embroidery Coverage'].includes(item.paramName)" :selectedData="selectedData" :stepData="item" :config="allStepConfig[item.paramName]" :maskName.sync="maskName" @clickFun="selectQuoteParams(item, $event)" @viewVideo="viewVideo" :key="item.paramName"> </PublicStep>

						<SizeSelect v-if="item.paramName == 'Keychain Size' && sizeType === 'sizeSelect'" :config="allStepConfig[item.paramName]" :selectedData="selectedData" :stepData="item" :maskName.sync="maskName" @clickFun="selectQuoteParams(item, $event)" @viewVideo="viewVideo" @showMaskFn="showMaskFn"></SizeSelect>

						<PinsSizeSelect v-if="item.paramName == 'Keychain Size' && sizeType === 'normal'" :generalData="generalData" :selectedData="selectedData" :stepData="item" :maskName="maskName" :smallTitle="lang.embroideredKeychains.stepSizeTitle" :sizeImgP1="lang.embroideredKeychains.p1" @clickFun="selectQuoteParams(item, $event)" @viewVideo="viewVideo" @closeMask="closeMask" @showMaskFn="showMaskFn" :key="item.paramName"> </PinsSizeSelect>

						<PublicStep v-if="['Acrylic Charm Color', 'Keychain Color', 'PVC Keychain Color'].includes(item.paramName)" :selectedData="selectedData" :stepData="item" :config="allStepConfig[item.paramName]" :maskName.sync="maskName" @clickFun="selectQuoteParams(item, $event)" @viewVideo="viewVideo" :key="item.paramName"> </PublicStep>

						<PublicStep v-if="['2D or 3D'].includes(item.paramName)" :selectedData="selectedData" :stepData="item" :config="allStepConfig[item.paramName]" :maskName.sync="maskName" @clickFun="selectQuoteParams(item, $event)" @viewVideo="viewVideo" :key="item.paramName"> </PublicStep>

						<PublicStep v-if="item.paramName == 'Keychain Border'" :selectedData="selectedData" :stepData="item" :config="allStepConfig[item.paramName]" :maskName.sync="maskName" @clickFun="selectQuoteParams(item, $event)" @viewVideo="viewVideo" :key="item.paramName"> </PublicStep>

						<PublicStep v-if="item.paramName == 'Plating'" :selectedData="selectedData" :config="allStepConfig[item.paramName]" :stepData="item" :maskName.sync="maskName" @clickFun="selectQuoteParams(item, $event)" @viewVideo="viewVideo" @showMaskFn="showMaskFn" :key="item.paramName"> </PublicStep>

						<PublicStep v-if="['Design Areas', 'Keychain Design Areas', 'Keychain Printed Area'].includes(item.paramName)" :selectedData="selectedData" :stepData="item" :config="allStepConfig[item.paramName]" :maskName.sync="maskName" @clickFun="selectQuoteParams(item, $event)" @viewVideo="viewVideo" @showMaskFn="showMaskFn" :key="item.paramName"> </PublicStep>

						<PublicStep v-if="['Link & Chain Options', 'Select Link & Chain Options', 'Back Printing Options'].includes(item.paramName) && !item.noShowDetail" :selectedData="selectedData" :config="allStepConfig[item.paramName]" :stepData="item" :maskName.sync="maskName" @clickFun="selectQuoteParams(item, $event)" @viewVideo="viewVideo" @showMaskFn="showMaskFn" :key="item.paramName" @showLessBtn="showLessBtn(item.paramName)"> </PublicStep>

						<PublicStep v-if="['Backstamp Options', 'Backside Options', 'Additional Upgrades'].includes(item.paramName) && !item.noShowDetail" :selectedData="selectedData" :stepData="item" :config="allStepConfig[item.paramName]" :maskName.sync="maskName" @clickFun="selectQuoteParams(item, $event)" @viewVideo="viewVideo" @showMaskFn="showMaskFn" :key="item.paramName" @showLessBtn="showLessBtn(item.paramName)"> </PublicStep>

						<PublicStep v-if="item.paramName == 'Select Packaging' || item.paramName == 'Packaging'" :selectedData="selectedData" :stepData="item" :config="allStepConfig[item.paramName]" :maskName.sync="maskName" @clickFun="selectQuoteParams(item, $event)" @viewVideo="viewVideo" :key="item.paramName"> </PublicStep>

						<PublicStep v-if="item.paramName == 'Quantity of Second Charms'" :selectedData="selectedData" :stepData="item" :config="allStepConfig[item.paramName]" :maskName.sync="maskName" @clickFun="selectQuoteParams(item, $event)" @viewVideo="viewVideo" :key="item.paramName"> </PublicStep>

						<template v-if="item.paramName === 'Upload Artwork & Comments'">
							<StepUpload class="step-upload" :class="{ mask: maskName === item.paramName }" :id="item.paramName" :index="item.customIndex" :itemData="item" :uploadList.sync="uploadArtworkList" :remark.sync="remark" :isUpload.sync="isUpload" @closeMask="closeMask" @showMaskFn="showMaskFn" :key="index"></StepUpload>
						</template>

						<template v-if="item.paramName === 'qty' || item.paramName === 'Quantity'">
							<StepQty class="step-qty" :class="{ mask: maskName === item.paramName }" :id="item.paramName" :index="item.customIndex" :itemData="item" :customQty.sync="customQty" :restaurants="restaurants" @closeMask="closeMask" @showMaskFn="showMaskFn" @calcPrice="debounceCalcPrice" @changeQty="changeQty" :key="item.paramName"></StepQty>
						</template>

						<template v-if="['Turnaround Time', 'Select Turnaround Time'].includes(item.paramName)">
							<StepTime class="step-date" :class="{ mask: maskName === item.paramName }" :id="item.paramName" :cateData="cateData" :pid="pid" :customQty="customQty" :index="item.customIndex" :itemData="item" :priceInfo="priceInfo" :previewMode="previewMode" :selectedParams="selectedData" @showMaskFn="showMaskFn" @selectItem="selectQuoteParams($event.item, $event.citem)" @closeMask="closeMask" :key="item.paramName"></StepTime>
						</template>
					</div>
				</div>
				<div class="rightArea" id="rightAreaCustom">
					<transition enter-active-class="animate__animated animate__faster animate__fadeInBottomLeft" leave-active-class="animate__animated animate__faster animate__fadeOutBottomLeft">
						<Detail v-show="showRightArea" class="type1" :class="{ mask: maskName }" :key="1" :absoluteImg="comeFromDZ.absoluteImg" :selectedData="selectedData" :priceInfo="priceInfo" :customQty="customQty" :textInfo="textInfo" :generalData="generalData" :attachment="attachment" @toPosition="toPosition" @addInquiry="debounceAddInquiry" @addCart="addCart"></Detail>
					</transition>
				</div>
			</div>
			<div class="footer" id="foot">
				<Detail class="type2" :key="3" :absoluteImg="comeFromDZ.absoluteImg" :selectedData="selectedData" :priceInfo="priceInfo" :customQty="customQty" :textInfo="textInfo" :generalData="generalData" :attachment="attachment" @toPosition="toPosition" @addInquiry="debounceAddInquiry" @addCart="addCart"> </Detail>
			</div>
			<el-drawer :visible.sync="showArea" :with-header="true" custom-class="drawDialog" :size="device === 'ipad' ? '728px' : '85%'">
				<Detail class="type3" :key="2" :absoluteImg="comeFromDZ.absoluteImg" :selectedData="selectedData" :priceInfo="priceInfo" :customQty="customQty" :textInfo="textInfo" :generalData="generalData" :attachment="attachment" @toPosition="toPosition" @addInquiry="debounceAddInquiry" @addCart="addCart"> </Detail>
			</el-drawer>
			<!-- 建议弹窗-->
			<RecomendDialog :commentsDialog.sync="commentsDialog" :paramValue="selectedParamsValue" @next="recomendNext"> </RecomendDialog>
			<!-- 预览弹窗-->
			<VideoPreviewDialog :pic-dialog.sync="picDialog" :zoom-pic="zoomPic" :temp-type="tempType" :selected-params-value="selectedParamsValue" @qtyAndBtnConfirm="qtyAndBtnConfirm" @qtyAndBtnNext="qtyAndBtnNext" @dialogNextStep="dialogNextStep"></VideoPreviewDialog>
			<!-- 升级数量弹窗-->
			<BaseDialog v-model="upgradeDialog" :width="device != 'mb' ? '640px' : '320px'">
				<Upgrade @closeUpgrade="upgradeDialog = false" :tipArr="tipArr" :typeName="cateData.cateName" @upgradeLevel="upgradeLevel"> </Upgrade>
			</BaseDialog>
			<!-- o2o预览页面弹窗-->
			<BaseDialog v-model="showOtoDialog" :width="device != 'mb' ? '570px' : '90%'">
				<div class="otoWrap">
					<img src="~/assets/images/oto.png" alt="" />
					<p>{{ lang.p1 }}</p>
					<h3>{{ lang.p2 }}</h3>
					<p style="color: #666666">
						{{ lang.p3 }}<br />
						{{ lang.p4 }}.
					</p>

					<div class="box">
						<p class="t1">
							{{ lang.p5 }} <br />
							{{ lang.p6 }}!
						</p>
						<a href="https://www.o2o.co/previewWeb" style="text-decoration: none">
							<el-button type="primary">{{ lang.p7 }}</el-button>
						</a>
					</div>
				</div>
			</BaseDialog>
			<!--			询盘弹窗-->
			<infoDialog :infoDialogVisible.sync="infoDialogVisible" :uploadList.sync="uploadList" :otherUpload="uploadArtworkList" :recomendUpload="recomendUpload" @getValue="getValueFun"></infoDialog>
			<!--    遮罩-->
			<myMask :maskName.sync="maskName"></myMask>
			<!--  手机，ipad端预览-->
			<PreviewBtn :showArea.sync="showArea"></PreviewBtn>
			<BaseDialog v-model="noFileDialog" :width="device != 'mb' ? '485px' : '90%'" :model="false">
				<template #closeIcon>
					<div style="display: none"></div>
				</template>
				<infoUpload :infoUploadList="infoUploadList" @pushInfoList="pushInfoList" @delInfoList="delInfoList" @updateInquiry="updateInquiry" @closeInfoDialog="closeInfoDialog"></infoUpload>
			</BaseDialog>
		</template>
	</div>
</template>

<script>
import "@/plugins/element";
import VideoPlayer from "@/components/Quote/VideoPlayer.vue";
import QuoteBtn from "@/components/Quote/QuoteBtn";
import myMask from "@/components/Quote/Mask";
import Detail from "@/components/Quote/Detail";
import BaseDialog from "@/components/Quote/BaseDialog";
import Upgrade from "@/components/Quote/Upgrade";
import Preloader from "@/components/Quote/Preloader";
import PriceText from "@/components/Quote/PriceText";
import Star from "@/components/Quote/Star";
import StepUpload from "@/components/Quote/StepUpload";
import StepTime from "@/components/Quote/StepTime";
import StepQty from "@/components/Quote/StepQty";
import Corner from "@/components/Medals/Corner";
import Pimg from "@/components/Medals/Pimg";
import QtyAndBtn from "@/components/Medals/QtyAndBtn";
import DialogBM from "@/components/Medals/DialogBM";
import infoDialog from "@/components/Medals/infoDialog";
import RecomendDialog from "@/components/Quote/RecomendDialog.vue";
import quoteMixin from "@/mixins/quote";
import quoteBanChoiceMixin from "@/mixins/quoteBanChoice";
import QuoteTitle from "@/components/Quote/QuoteTitle.vue";
import PreviewBtn from "@/components/Quote/PreviewBtn.vue";
import CustomCircle from "@/components/Quote/customCircle.vue";
import ToolTip from "@/components/Quote/ToolTip.vue";
import { getQuoteConfig } from "@/assets/js/quote/quoteConfig";
import PinsSizeSelect from "~/components/Quote/PinsSizeSelect.vue";
import PublicStep from "~/components/Quote/PublicStep/index.vue";
import QuoteNav from "@/components/Medals/QuoteNav";
import SizeSelect from "@/components/Quote/SizeSelect.vue";
import VideoPreviewDialog from "@/components/Quote/VideoPreviewDialog.vue";

export default {
	head() {
		return {
			title: "keychains",
		};
	},
	components: {
		VideoPreviewDialog,
		ToolTip,
		CustomCircle,
		PreviewBtn,
		QuoteTitle,
		DialogBM,
		QuoteBtn,
		Detail,
		BaseDialog,
		Upgrade,
		myMask,
		VideoPlayer,
		Preloader,
		PriceText,
		Star,
		StepUpload,
		StepTime,
		StepQty,
		Corner,
		Pimg,
		QtyAndBtn,
		infoDialog,
		RecomendDialog,
		PinsSizeSelect,
		PublicStep,
		QuoteNav,
		SizeSelect,
	},
	mixins: [quoteMixin, quoteBanChoiceMixin],
	data() {
		const config = getQuoteConfig(this.$route.name);
		return {
			...config,
		};
	},
	created() {},
	mounted() {
		this.applyMediaQueries(this.allStepConfig);
	},
};
</script>
<style scoped lang="scss">
@import "@/assets/css/quotePublic";
</style>
