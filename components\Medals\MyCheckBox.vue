<!--
	demo:
	<MyCheckBox 
		v-for="itemChild in [{},{},{}]" :key="itemChild.id" 	//*
		:bindValue="itemChild"   															//当前循环对象* 
		:showPrice="true"    																	//boolean	是否显示价格.默认显示Free	?
		:bindName="item.paramName"														//string	绑定标题名称	*
		:titleValue="'alias'"																	//string	是否显示标题  固定参数alias	*
		:imageValue="'imageJson'"															//string	是否显示图片.  固定参数imageJson	?
		:aspectRatio="'aspect-ratio: 372/240'"								//string	图片等比压缩比例	?
		:likeModel="selectedData"															//json		无		*
		@clickFun="selectQuoteParams(item, itemChild)"				//参数1：当前大对象. 参数2：当前对象	?
	/>
-->
<template>
	<div class="StepBox" :class="{ active: isActive(), activeType: activeType && isActive(), type2: type2 ? true : false, circleDown: circleDown ? true : false }" :style="cssModel.stepBoxModel ? cssModel.stepBoxModel.stepBoxStyle : ''">
		<!--卡片问号-->
		<div class="tooltip" v-if="(bindValue.tips || bindValue.tipsImage) && !tipsDown" :style="cssModel.stepBoxModel ? cssModel.stepBoxModel.tooltipStyle : ''">
			<!--问号提示组件-->
			<ToolTip :item-data="bindValue"></ToolTip>
		</div>

		<!-- isPageQuote为true是semi-medals报价样式 -->
		<div :class="isPageQuote === false ? 'se' : 'm-se'" @click.stop="changeInput($event)">
			<div v-if="isPageQuote" :class="isActive() ? 'semi-medals-icon2' : 'semi-medals-icon'">
				<b class="icon-check"></b>
			</div>

			<template v-if="childList">
				<div class="swiper">
					<div class="swiperChild">
						<div class="img-outer" :class="{ active: bindValue[titleValue] == item[titleValue] }" v-for="item in bindValue.childList" :key="item.id">
							<img @click.stop="activeFun(item)" :src="filterImage(item[imageValue], true)" />
							<i class="el-icon-check"></i>
						</div>
					</div>
					<el-image :src="filterImage(bindValue[imageValue])" fit="contain" style="width: 100%" :style="aspectRatio" lazy> </el-image>
				</div>
			</template>
			<template v-else>
				<template v-if="tVideoPath">
					<div class="imgWrap" style="width: 100%; height: 100%">
						<VideoPlayer @clicking="changeInput(null, 'video')" :style="aspectRatio" class="showPlayBtn" ref="videos" :options="getVideoOptions(tVideoPath, 4, tImagePath)"></VideoPlayer>
					</div>
					<i class="el-icon-zoom-in zoomIcon" @click.stop="viewVideo(tVideoPath)"></i>
				</template>
				<template v-else-if="imageValue">
					<el-image v-if="imageValue == 'imageJson'" :src="ribbonImage()" fit="contain" :style="aspectRatio" style="width: 100%" lazy> </el-image>
					<el-image :style="aspectRatio" v-else :src="bindValue[imageValue]" fit="contain" style="width: 100%" lazy> </el-image>
				</template>
			</template>
			<div class="ad" v-if="starJson?.length">
				<template v-for="(citem, cindex) in starJson">
					<div class="ad-item" v-if="!(citem.key == 'Delivery' && device == 'mb')" :key="cindex">
						<span>{{ citem.key }}:</span>
						<Star v-model="citem.value"></Star>
						<strong>{{ citem.value }}</strong>
					</div>
				</template>
			</div>
			<span class="product-info" :class="isFontCenter ? 'isFontCenterDiv' : ''" :style="cssModel.stepBoxModel ? cssModel.stepBoxModel.productInfoStyle : ''">
				<label v-if="!(circleDown && device == 'mb')" :for="bindValue.cateName + bindValue.id" class="radio-beauty" :class="isFontCenter ? 'isFontCenterLabel' : ''" :style="cssModel.stepBoxModel ? cssModel.stepBoxModel.radioBeautyStyle : ''"> </label>
				<span class="title" :style="cssModel.stepBoxModel ? cssModel.stepBoxModel.titleStyle : ''">
					{{ bindValue[titleValue] }}
				</span>
				<div v-if="hot && hasHot" class="hot" style="display: flex; align-items: center; justify-content: center; margin-left: 6px; width: 1em; height: 1em; aspect-ratio: 1/1">
					<img style="aspect-ratio: 1/1; object-fit: contain" src="https://static-oss.gs-souvenir.com/web/quoteManage/20240828/%E7%81%AB_20240828khMdy2.png" alt="" />
				</div>
				<!--图文问号-->
				<div class="tooltip" style="position: static; margin-left: 5px" v-if="(bindValue.tips || bindValue.tipsImage) && tipsDown">
					<ToolTip :item-data="bindValue"></ToolTip>
				</div>

				<Corner v-if="bindValue.labelText && !hot" :backgroundColor="bindValue.labelColor" :type2="type2" position="absolute" :style="cssModel.stepBoxModel ? cssModel.stepBoxModel.cornerStyle : ''">{{ bindValue.labelText }}</Corner>
			</span>
			<PriceText :paramData="bindValue" :sizeValue="sizeValue" :freeText="freeText" v-if="showPrice" :moldText="moldText"></PriceText>
			<div class="circle" v-if="circleDown && device == 'mb'">
				<div class="inner-circle"></div>
			</div>
		</div>
		<span class="inputBox" v-if="bindValue.paramType === 'QUANTITY' || bindValue.paramType === 'COLOR'">
			<template v-if="numberInput || !autocompleteInput">
				<el-input-number ref="myInput" size="small" class="number-input" @focus="focusInput" @blur="blurInput" @keyup.native="keyupInput" v-model.number="bindValue.inputNum" :precision="0" placeholder="Enter Qty" controls-position="right" :min="0" :controls="windowWidth > 800"></el-input-number>
			</template>
			<template v-if="autocompleteInput">
				<el-autocomplete ref="myInput" size="small" class="autocompleter-input" @focus="focusInput" @blur="blurInput" @select="blurInput" @keyup.native="keyupInput" v-model="bindValue.inputNum" @input="updateQty" :fetch-suggestions="querySearch" :precision="0" placeholder="Enter Qty" :controls="false" :min="0"></el-autocomplete>
			</template>
		</span>
	</div>
</template>
<script>
import VideoPlayer from "@/components/Quote/VideoPlayer.vue";
import Corner from "@/components/Medals/Corner";
import Star from "@/components/Quote/Star";
import PriceText from "@/components/Quote/PriceText";
import ToolTip from "@/components/Quote/ToolTip.vue";

export default {
	components: {
		ToolTip,
		VideoPlayer,
		Corner,
		Star,
		PriceText,
	},
	props: {
		tipNum: Number,
		restaurants: Array,
		sizeValue: {
			type: [Number, String],
			default: 0,
		},
		type2: {
			type: Boolean,
			default: false,
		},
		starJson: {
			type: Array,
			default: function () {
				return [];
			},
		},
		activeType: {
			type: Boolean,
			default: false,
		},
		aspectRatio: {
			type: String,
		},
		//绑定值
		bindValue: {
			type: Object,
		},
		//绑定名字
		bindName: {
			type: String,
		},
		titleValue: {
			type: String,
		},
		imageValue: {
			type: String,
		},
		height: {
			type: String,
		},
		likeModel: {
			type: [Array, Object],
		},
		showPrice: {
			type: Boolean,
			default: false,
		},
		showMold: {
			type: Boolean,
			default: false,
		},
		numberInput: {
			type: Boolean,
			default: false,
		},
		autocompleteInput: {
			type: Boolean,
			default: false,
		},
		childList: {
			type: Boolean,
			default: false,
		},
		videoPath: {
			type: String,
		},
		videoImage: {
			type: String,
		},
		update: {
			type: Boolean,
		},
		isFontCenter: {
			type: Boolean,
			default: false,
		},
		tipsDown: {
			type: Boolean,
			default: false,
		},
		circleDown: {
			type: Boolean,
			default: false,
		},
		cssModel: {
			type: Object,
			default: () => ({}),
		},
		isPageQuote: {
			type: Boolean,
			default: false,
		},
		freeText: {
			type: String,
		},
		hot: {
			type: Boolean,
			default: false,
		},
        moldText:{
            type: Number,
            default: 0,
        }
	},
	data() {
		return {
			windowWidth: document.documentElement.clientWidth,
			picDialog: false,
			zoomPic: "",
		};
	},
	methods: {
		isJsonString(str) {
			try {
				const toObj = JSON.parse(str); // json字符串转对象
				/*
                判断条件 1. 排除null可能性
                         2. 确保数据是对象或数组
            */
				if (toObj && typeof toObj === "object") {
					return true;
				}
			} catch {}
			return false;
		},
		ribbonImage() {
			if (this.bindValue.colorValue && this.bindValue.colorValue.imageJson) {
				return this.filterImage(this.bindValue.colorValue.imageJson, true);
			} else if (this.bindValue.sizeValue && this.bindValue.sizeValue.imageJson) {
				return this.filterImage(this.bindValue.sizeValue.imageJson, true);
			} else {
				return this.filterImage(this.bindValue[this.imageValue]);
			}
		},
		parseJSON(str) {
			return str
				? JSON.parse(str)
				: [
						{
							url: "",
						},
				  ];
		},
		viewVideo(src) {
			this.$emit("picDialogFun", true);
			this.$emit("zoomPicFun", { key: this.bindName, value: this.bindValue });
		},
		activeFun(val) {
			this.bindValue.paramName = val.paramName;
			this.bindValue.imageJson = val.imageJson;
		},
		isJSON(str) {
			if (typeof str == "string") {
				try {
					var obj = JSON.parse(str);
					if (typeof obj == "object" && obj) {
						return true;
					} else {
						return false;
					}
				} catch (e) {
					return false;
				}
			} else {
				return false;
			}
		},
		filterImage(val, hasSec = false) {
			if (this.isJSON(val)) {
				if (hasSec) {
					return JSON.parse(val)[1] ? JSON.parse(val)[1].url : JSON.parse(val)[0].url;
				} else {
					return JSON.parse(val)[0].url;
				}
			} else {
				return val.url;
			}
		},
		filterPrice(val) {
			if (!val.unitPrice && !val.moldPrice) {
				return "Free";
			} else if (!val.unitPrice && val.moldPrice) {
				return `+ Mold Fee: $${val.moldPrice.toFixed(0)}`;
			} else {
				return val.moldPrice ? `+ $${val.moldPrice.toFixed(0)} Setup,+ $${val.unitPrice.toFixed(2)}/pc` : `+ $${val.unitPrice.toFixed(2)}/pc`;
			}
		},
		quantityChange(val) {
			this.$emit("selectCurrentParams", { key: this.bindName, value: this.bindValue });
		},
		changeInput(e, type) {
			if (type == "video") {
				this.$emit("picDialogFun", false);
				this.$emit("clickFun", {
					key: this.bindName,
					value: this.bindValue,
				});
			} else {
				if (this.isActive() || type == "input" || e.target.nodeName == "b") {
					this.$emit("clickFun", {
						key: this.bindName,
						value: this.bindValue,
						type: "click",
					});
				} else {
					this.$emit("picDialogFun", false);
					this.$emit("clickFun", {
						key: this.bindName,
						value: this.bindValue,
					});
					this.$nextTick(() => {
						try {
							this.$emit("update:tipNum", 2);
							this.$refs.myInput.focus();
						} catch (e) {}
					});
				}
			}
		},
		getVideoOptions(path, type, poster) {
			if (type === 1) {
				return {
					autoplay: true,
					controls: false,
					muted: true,
					loop: true,
					fill: true,
					sources: [
						{
							src: path,
							type: "video/mp4",
						},
					],
				};
			} else if (type === 2) {
				return {
					autoplay: true,
					controls: true,
					muted: false,
					loop: true,
					fill: true,
					sources: [
						{
							src: path,
							type: "video/mp4",
						},
					],
				};
			} else if (type === 3) {
				return {
					autoplay: true,
					controls: false,
					muted: true,
					loop: true,
					fill: true,
					sources: [
						{
							src: path,
							type: "video/mp4",
						},
					],
				};
			} else if (type === 4) {
				let newPoster = "";
				if (!this.isJsonString(poster)) {
					newPoster = poster;
				} else {
					let ph = JSON.parse(poster);
					if (ph && ph.length) {
						let tempPath = ph.find((x) => {
							return x.proType == this.proType;
						});
						newPoster = tempPath ? tempPath.path : ph[0].path;
					}
				}
				return {
					autoplay: false,
					controls: false,
					muted: true,
					loop: true,
					fill: true,
					sources: [
						{
							src: path,
							type: "video/mp4",
						},
					],
					poster: newPoster,
				};
			}
		},
		isActive() {
			if (this.inputVal.length > 0) {
				return this.inputVal.some((item) => {
					return item.id == this.bindValue.id;
				});
			}
		},
		focusInput() {
			if (this.tipNum > 1) {
				return false;
			}
			try {
				let myInputDom = this.$refs.myInput.$el;
				myInputDom.classList.add("isFocus");
			} catch (e) {}
		},
		keyupInput() {
			try {
				let myInputDom = this.$refs.myInput.$el;
				myInputDom.classList.remove("isFocus");
			} catch (e) {}
		},
		blurInput() {
			try {
				let myInputDom = this.$refs.myInput.$el;
				myInputDom.classList.remove("isFocus");
				this.$emit("selectCurrentParams", { key: this.bindName, value: this.bindValue });
                this.$emit("triggerSmallQty")
			} catch (e) {}
		},
		updateQty(val) {
			val = (val + "").replace(/[^\d]/g, "");
			this.bindValue.inputNum = val;
		},
		querySearch(queryString, cb) {
			var restaurants = this.restaurants;
			var results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants;
			// 调用 callback 返回建议列表的数据
			cb(results);
		},
		createFilter(queryString) {
			return (restaurant) => {
				return restaurant.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0;
			};
		},
	},
	watch: {
		update: {
			handler(val) {
				this.$forceUpdate();
			},
		},
	},
	computed: {
		tVideoPath() {
			if (!this.isJsonString(this.videoPath)) return this.videoPath;
			let ph = JSON.parse(this.videoPath);
			if (ph && ph.length) {
				let tempPath = ph.find((x) => {
					return x.proType == this.proType;
				});
				return tempPath ? tempPath.path : ph[0].path;
			} else {
				return undefined;
			}
		},
		tImagePath() {
			if (!this.isJsonString(this.videoImage)) return this.videoImage;
			let ph = JSON.parse(this.videoImage);
			if (ph && ph.length) {
				let tempPath = ph.find((x) => {
					return x.proType == this.proType;
				});
				return tempPath ? tempPath.path : ph[0].path;
			} else {
				return undefined;
			}
		},
		proType() {
			return this.$store.state.proType;
		},
		inputVal() {
			return this.likeModel[this.bindName] || [];
		},
		device() {
			return this.$store.state.device;
		},
		hasHot() {
			return this.bindValue.labelText.trim().length > 0;
		},
	},
};
</script>

<style scoped lang="scss">
.StepBox {
	::v-deep .el-image__inner {
		vertical-align: middle;
	}

	$bg: #afb1b3;
	$bgc: white;
	$bgc2: $color-primary;
	cursor: pointer;
	display: flex;
	flex-direction: column;
	align-items: center;
	position: relative;
	@media screen and (max-width: 767px) {
		padding: 0 0 5.5px 0;
	}

	.m-se {
		cursor: pointer;
		width: 100%;
		display: flex;
		flex-direction: column;
		align-items: center;
		position: relative;

		.semi-medals-icon {
			display: none;
		}
		.semi-medals-icon2 {
			--border-radius: 16px;
			position: absolute;
			top: 0;
			right: 0;
			font-size: 12px;
			background-color: $color-primary;
			border-radius: 0px calc(var(--border-radius) / 4) 0px var(--border-radius);
			padding: 0 5px;
			color: #fff;
			z-index: 1;
			display: flex;
			align-items: center;
			justify-content: center;
			width: 24px;
			height: 24px;
			&::after {
				content: "";
				position: absolute;
				inset: 0;
				right: -1px;
				top: -1px;
				background-color: $color-primary;
				border-radius: 0px 0 0px var(--border-radius);
				z-index: 1;
			}
			b {
				z-index: 2;
			}
			@media screen and (max-width: 767px) {
				--border-radius: 10px;
				width: 18px;
				height: 18px;
				font-size: 10px;
			}
		}

		.zoomIcon {
			position: absolute;
			top: 10px;
			right: 10px;
			color: #ffffff;
			font-size: 20px;
			z-index: 2;
			transition: font-size 0.2s;

			&:hover {
				font-size: 24px;
				color: $color-primary;
			}
		}

		.swiper {
			position: relative;
			width: 100%;
			height: 148px;

			.swiperChild {
				position: absolute;
				right: 5px;
				top: 5px;
				z-index: 1;
				display: flex;
				flex-direction: column;

				.img-outer {
					height: 20px;
					width: 20px;
					margin-bottom: 3px;
					padding: 2px;
					display: flex;
					transition: padding 0.2s;
					position: relative;

					i {
						display: none;
						position: absolute;
						left: 50%;
						top: 50%;
						transform: translate(-50%, -50%);
						color: #ffffff;
					}

					img {
						width: 100%;
						height: 100%;
						border-radius: 50%;
						object-fit: contain;
					}

					&:hover {
						padding: 0;
					}

					&.active {
						padding: 0;

						i {
							display: block;
						}
					}
				}
			}
		}

		.el-image {
			border-radius: 6px;
		}

		.isFontCenterDiv {
			position: relative;
			.isFontCenterLabel {
				position: absolute !important;
				left: -32px;
			}
		}

		.product-info {
			display: flex;
			align-items: center;
			justify-content: center;
			margin-top: 10px;

			.radio-beauty {
				width: 18px;
				height: 18px;
				min-width: 18px;
				box-sizing: border-box;
				display: inline-block;
				border: 1px solid $bg;
				margin: 0 4px; //0 12px 0 3px;
				border-radius: 50%;
				background-color: $bgc;
				background-clip: content-box;
				position: relative;
				cursor: pointer;

				&::after {
					content: "";
					position: absolute;
					border-radius: 50%;
					left: 50%;
					top: 50%;
					transform: translate(-50%, -50%);
					width: 6px;
					height: 6px;
					background-color: $bg;
				}

				@media screen and (max-width: 767px) {
					width: 16px;
					height: 16px;
					min-width: 16px;
					&::after {
						width: 5px;
						height: 5px;
					}
				}
			}

			.type2-price {
				display: flex;
				flex-direction: column;
			}
		}

		.product-price {
			font-size: 16px;
			font-weight: 400;
			color: #333333;
			margin-top: 5px;
			text-align: center;
			@media screen and (max-width: 767px) {
				margin-top: 5px;
				margin-bottom: 0;
				font-size: 12px;
			}

			&.free {
				color: #de3500;
			}
		}

		@media screen and (max-width: 767px) {
			.circle {
				display: block;
				height: 10px;
				width: 20px;
				background: #edf1f5;
				border: 1px solid #e9ecf0;
				border-radius: 0 0 16px 16px;
				border-top: none;
				bottom: -10px;
				left: 50%;
				position: absolute;
				transform: translate(-50%, 100%);
				transition: all 0.3s;
				z-index: 10;

				.inner-circle {
					height: 14px;
					width: 14px;
					background: #fff;
					border: 1px solid #aaaeb3;
					border-radius: 50%;
					left: 50%;
					position: absolute;
					top: 0;
					transform: translate(-50%, -50%);
					transition: all 0.3s;

					&::after {
						content: "";
						height: 5px;
						width: 5px;
						background-color: #aaaeb3;
						border-radius: 50%;
						left: 50%;
						position: absolute;
						top: 50%;
						transform: translate(-50%, -50%);
						transition: all 0.3s;
					}
				}
			}
		}
	}

	.ad {
		align-items: flex-start;
		display: flex;
		padding-top: 10px;

		.ad-item {
			display: flex;
			align-items: center;
			border-right: 1px solid #ccc;
			padding: 0 10px;
			@media screen and (max-width: 767px) {
				padding: 0 1px;
			}

			&:last-child {
				border-right: none;
			}
		}
	}

	.tooltip {
		position: absolute;
		right: 10px;
		z-index: 2;

		i {
			color: $color-primary;
		}
	}

	&.activeType {
		position: relative;

		&::after {
			position: absolute;
			content: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBzdGFuZGFsb25lPSJubyI/Pg0KPCFET0NUWVBFIHN2ZyBQVUJMSUMgIi0vL1czQy8vRFREIFNWRyAxLjEvL0VOIiAiaHR0cDovL3d3dy53My5vcmcvR3JhcGhpY3MvU1ZHLzEuMS9EVEQvc3ZnMTEuZHRkIj4NCjxzdmcgdD0iMTY0MTM0NzgxOTM5NyIgY2xhc3M9Imljb24iIHZpZXdCb3g9IjAgMCAxMjE2IDEwMjQiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiBwLWlkPSIyNDM3IiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayIgd2lkdGg9IjE5IiBoZWlnaHQ9IjE2Ij4NCiAgICA8ZGVmcz4NCiAgICAgICAgPHN0eWxlIHR5cGU9InRleHQvY3NzIj48L3N0eWxlPg0KICAgIDwvZGVmcz4NCiAgICA8cGF0aCBkPSJNNjAxLjYgNzY4bDQ0OC03NjhMMTIxNiA5NiA2NzIgMTAyNCAwIDY0Ni40bDk2LTE2Ni40eiIgZmlsbD0iI0ZGRkZGRiIgcC1pZD0iMjQzOCI+PC9wYXRoPg0KPC9zdmc+);
			left: 8px;
			top: 5px;
			width: 19px;
			height: 16px;
			z-index: 1;
		}

		&::before {
			position: absolute;
			content: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBzdGFuZGFsb25lPSJubyI/Pg0KPCFET0NUWVBFIHN2ZyBQVUJMSUMgIi0vL1czQy8vRFREIFNWRyAxLjEvL0VOIiAiaHR0cDovL3d3dy53My5vcmcvR3JhcGhpY3MvU1ZHLzEuMS9EVEQvc3ZnMTEuZHRkIj4NCjxzdmcgdD0iMTY0MTI4NzczOTU1MSIgY2xhc3M9Imljb24iIHZpZXdCb3g9IjAgMCAxMDI0IDEwMjQiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiBwLWlkPSIyNDM3IiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayIgd2lkdGg9IjUwIiBoZWlnaHQ9IjUwIj4NCiAgICA8ZGVmcz4NCiAgICAgICAgPHN0eWxlIHR5cGU9InRleHQvY3NzIj48L3N0eWxlPg0KICAgIDwvZGVmcz4NCiAgICA8cGF0aCBkPSJNMCAxNTguODk2NTUydjg2NS4xMDM0NDhMMTAyNCAwSDE1OC44OTY1NTJDODQuMjE1MTcyIDAgMCAxMDAuOTg3NTg2IDAgMTU4Ljg5NjU1MnoiIGZpbGw9IiMxODYxQUUiIHAtaWQ9IjI0MzgiPjwvcGF0aD4NCjwvc3ZnPg==);
			left: 0;
			top: 0;
			width: 50px;
			height: 50px;
			z-index: 1;
		}
	}

	@keyframes inputBorderColor {
		0% {
			border-color: red;
		}
		100% {
			border-color: #dcdfe6;
		}
	}

	.number-input {
		width: 140px;
		background: #ffffff;
		border-radius: 4px;
		margin-top: 5px;

		&.isFocus ::v-deep input {
			animation-name: inputBorderColor;
			animation-duration: 0.8s;
			animation-iteration-count: infinite;
		}

		::v-deep input::placeholder {
			color: #666666;
		}

		::v-deep .el-input-number__increase {
			width: 24px;
		}

		::v-deep .el-input-number__decrease {
			width: 24px;
		}

		::v-deep .el-input__inner {
			@media screen and (max-width: 767px) {
				text-align: center;
			}
		}
	}

	.inputBox {
		.autocompleter-input {
			margin: 0.5em;
			font-size: 14px;
			::v-deep .el-input {
				input {
					text-align: center;
					&::placeholder {
						text-align: center;
					}
				}
			}
		}
	}

	.se {
		cursor: pointer;
		width: 100%;
		height: 100%;
		display: flex;
		flex-direction: column;
		align-items: center;
		position: relative;
		// padding: 10px 13px;
		// @media screen and (max-width: 767px) {
		//   padding: 0;
		// }
		.zoomIcon {
			position: absolute;
			top: 10px;
			right: 10px;
			color: #ffffff;
			font-size: 20px;
			z-index: 2;
			transition: font-size 0.2s;

			&:hover {
				font-size: 24px;
				color: $color-primary;
			}
		}

		.swiper {
			position: relative;
			width: 100%;
			height: 148px;

			.swiperChild {
				position: absolute;
				right: 5px;
				top: 5px;
				z-index: 1;
				display: flex;
				flex-direction: column;

				.img-outer {
					height: 20px;
					width: 20px;
					margin-bottom: 3px;
					padding: 2px;
					display: flex;
					transition: padding 0.2s;
					position: relative;

					i {
						display: none;
						position: absolute;
						left: 50%;
						top: 50%;
						transform: translate(-50%, -50%);
						color: #ffffff;
					}

					img {
						width: 100%;
						height: 100%;
						border-radius: 50%;
						object-fit: contain;
					}

					&:hover {
						padding: 0;
					}

					&.active {
						padding: 0;

						i {
							display: block;
						}
					}
				}
			}
		}

		.el-image {
			border-radius: 6px;
		}

		.isFontCenterDiv {
			position: relative;
			.isFontCenterLabel {
				position: absolute !important;
				left: -32px;
			}
		}

		.product-info {
			display: flex;
			align-items: center;
			justify-content: center;
			margin-top: 10px;

			.radio-beauty {
				width: 18px;
				height: 18px;
				min-width: 18px;
				box-sizing: border-box;
				display: inline-block;
				border: 1px solid $bg;
				margin: 0 4px; //0 12px 0 3px;
				border-radius: 50%;
				background-color: $bgc;
				background-clip: content-box;
				position: relative;
				cursor: pointer;

				&::after {
					content: "";
					position: absolute;
					border-radius: 50%;
					left: 50%;
					top: 50%;
					transform: translate(-50%, -50%);
					width: 6px;
					height: 6px;
					background-color: $bg;
				}

				@media screen and (max-width: 767px) {
					width: 16px;
					height: 16px;
					min-width: 16px;
					&::after {
						width: 5px;
						height: 5px;
					}
				}
			}

			.type2-price {
				display: flex;
				flex-direction: column;
			}
		}

		.product-price {
			font-size: 16px;
			font-weight: 400;
			color: #333333;
			margin-top: 5px;
			text-align: center;
			@media screen and (max-width: 767px) {
				margin-top: 5px;
				margin-bottom: 0;
				font-size: 12px;
			}

			&.free {
				color: #de3500;
			}
		}

		@media screen and (max-width: 767px) {
			.circle {
				display: block;
				height: 10px;
				width: 20px;
				background: #edf1f5;
				border: 1px solid #e9ecf0;
				border-radius: 0 0 16px 16px;
				border-top: none;
				bottom: -10px;
				left: 50%;
				position: absolute;
				transform: translate(-50%, 100%);
				transition: all 0.3s;
				z-index: 10;

				.inner-circle {
					height: 14px;
					width: 14px;
					background: #fff;
					border: 1px solid #aaaeb3;
					border-radius: 50%;
					left: 50%;
					position: absolute;
					top: 0;
					transform: translate(-50%, -50%);
					transition: all 0.3s;

					&::after {
						content: "";
						height: 5px;
						width: 5px;
						background-color: #aaaeb3;
						border-radius: 50%;
						left: 50%;
						position: absolute;
						top: 50%;
						transform: translate(-50%, -50%);
						transition: all 0.3s;
					}
				}
			}
		}
	}

	&.active {
		.product-info .radio-beauty {
			background-color: $color-primary;
			border-color: $color-primary;

			&::after {
				background-color: $bgc;
			}
		}

		.product-info .title {
			color: $color-primary;
		}
	}

	&.type2 {
		background-color: #f2f2f2;

		.StepBox .se .imgWrap {
			border: none;
		}

		::v-deep .video-js {
			border-end-end-radius: 0 !important;
			border-end-start-radius: 0 !important;
		}
	}

	&.circleDown.active {
		.circle {
			border-color: var(--color-primary, transparent);

			.inner-circle {
				border-color: var(--color-primary, transparent);

				&::after {
					background-color: var(--color-primary, transparent);
				}
			}
		}
	}
}

//cg  这样命名F12检查 可以直接搜索得到.方便修改
@media (max-width: 414px) {
	//小刚的报价class选择器    对应页面:custom-woven-labels.vue
	.customWovenLabels {
		.StepBox .tooltip {
			height: 30px;
			display: flex;
			align-items: center;
		}
		.StepBox .product-info .title {
			padding-top: 0 !important;
		}
		.StepBox .product-info .radio-beauty {
			margin-top: 0 !important;
		}
		.tips.type2 {
			top: 5px !important;
		}
	}
}
</style>
