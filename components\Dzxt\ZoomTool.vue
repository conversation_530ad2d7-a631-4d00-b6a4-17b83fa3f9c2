<template>
	<div class="custom-zoom">
		<div class="editorZoomOut" title="Zoom out" @click="zoomOutStage">
			<b class="icon-minus"></b>
		</div>
		<div class="editorZoomValue" title="Current zoom">
			<span>{{ stageScale | formatScale }}</span>
		</div>
		<div class="editorZoomFit" title="Fit to window" @click="fitStage">
			<span>Fit</span>
		</div>
		<div class="editorZoomIn" title="Zoom on" @click="zoomInStage">
			<b class="icon-plus"></b>
		</div>
	</div>
</template>

<script>
export default {
	props: ["fabricStage", "wrapNode"],
	data() {
		return {
			stageScale: 1, //画布比例
		};
	},
	filters: {
		formatScale(str) {
			return parseInt(str * 100) + "%";
		},
	},
	methods: {
		stageZoom() {
			this.stageScale = this.fabricStage.getZoom();
		},

		zoomIt(canvas, zoom) {
			let width = 650 * zoom,
				height = 650 * zoom;
			if (zoom <= 0.1) return false;
			canvas.setWidth(width);
			canvas.setHeight(height);
			canvas.requestRenderAll();
			canvas.setZoom(zoom);
			canvas.calcOffset();
			this.stageZoom();
		},

		zoomOutStage() {
			let presentZoom = this.fabricStage.getZoom();
			this.zoomIt(this.fabricStage, presentZoom - 0.1);
		},

		zoomInStage() {
			let presentZoom = this.fabricStage.getZoom();
			this.zoomIt(this.fabricStage, presentZoom + 0.1);
		},

		fitStage() {
			this.zoomIt(this.fabricStage,1)
		},
	},
};
</script>

<style scoped lang="scss">
.custom-zoom {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 184px;
	height: 34px;
	background: #f0f1f2;
	border-radius: 17px;
	color: #90979e;
	font-size: 18px;

	.editorZoomValue {
		margin: 0 20px;
	}

	.editorZoomFit {
		position: relative;
		margin-right: 20px;
		cursor: pointer;

		&::before {
			content: "";
			position: absolute;
			top: -10px;
			right: -10px;
			bottom: -10px;
			left: -10px;
		}
	}

	.editorZoomOut,
	.editorZoomIn {
		position: relative;
		cursor: pointer;

		&::before {
			content: "";
			position: absolute;
			top: -10px;
			right: -10px;
			bottom: -10px;
			left: -10px;
		}
	}
}
</style>
