<template>
	<div>
		<div class="box-border">
			<i class="el-icon-close" @click="closeMask"></i>
		</div>
		<h3 class="step-title">
			<span>{{ index }}</span>
			{{ itemData.alias ? itemData.alias : itemData.paramName }}
		</h3>
		<div class="step-box">
			<!-- <div class="editArea">
				<p class="t1">{{ lang.OrderComments }}</p>
				<div style="flex: 1">
					<div class="pcInput" style="height: 100%">
						<el-input type="textarea" :value="remark" @input="updateRemark" :placeholder="lang.placeholder1"></el-input>
					</div>
					<div class="mbInput">
						<el-input type="textarea" :rows="4" :value="remark" @input="updateRemark" :placeholder="lang.placeholder1"></el-input>
					</div>
				</div>
			</div> -->
			<div class="editArea" v-if="addTextShow">
				<div class="upload-text" :class="{ 'text-active': !editActive }" @click="updateEdit">
					<p class="t1">
						<span class="circle2" :class="{
							active: !editActive
						}"></span>Add Text
					</p>
					<p class="t2">{{ lang.notes }}</p>
				</div>
				<div class="input-text" @click="updateEdit1" v-show="!editActive">
					<div>
						<el-input :value="remark1" @input="updateRemark1" placeholder="Enter your text here.">
							<template slot="append">1st cufflink</template>
						</el-input>
					</div>
					<div>
						<el-input :value="remark2" @input="updateRemark2" placeholder="Enter your text here.">
							<template slot="append">2nd cufflink</template>
						</el-input>
					</div>
					<div class="edit-tip" v-if="tipShow">Please fill in at least one of the texts on the cufflinks.</div>
				</div>
			</div>
			<div class="uploadArea">
				<div @click="openUpload" style="cursor: pointer">
					<div class="upload-text" :class="{ 'text-active': showUpload }">
						<p class="t1">
							<span class="circle2" :class="{
								active: showUpload
							}"></span> <b class="icon-shangchuan uploadIcon"></b> {{ lang.UploadFile }}
						</p>
						<p class="t2">{{ lang.notes }}.</p>
					</div>
					<div class="upload-box" ref="uploadBox" :class="{ dropActive: dropActive }" v-show="showUpload"
						@click="uploadArtwork">
						<div class="uploadList">
							<template v-if="uploadList.length">
								<ul>
									<li v-for="(item, index) in uploadList" class="uploadItem" :key="index">
										<span>{{ item.original_filename }}</span>
										<div>
											<b class="icon-check myIcon" style="color: #0cbd5f"></b>
											<span @click.stop="delUploadImg(index)">
												<b class="icon-shanchu2 myIcon" style="color: #b6b0b0"></b>
											</span>
										</div>
									</li>
								</ul>
							</template>
							<template v-else>
								<b class="icon-shangchuan uploadIcon"></b>
							</template>
						</div>
						<div>
							<div class="upload-btn" style="text-align: center">
								<div class="uploadBtnWrap">
									<button type="button" :disabled="!editActive || isUpload"
										:class="{ isDisabled: !editActive || isUpload }">
										{{ lang.Browse }}
									</button>
									<input type="file" ref="upload"
										:accept="acceptFileType" multiple
										@change="uploadPic" />
								</div>
							</div>
							<div class="tips" v-if="!no">
								<div>
									{{ lang.p9 }}.
									<el-tooltip popper-class="cusToolTip" effect="light" :content="itemData.tips"
										placement="top-start">
										<b class="icon-wenhao1"></b>
									</el-tooltip>
								</div>
								<div>{{ lang.p10 }},</div>
								<div>
									{{ lang.p11 }}
									<el-button @click="toDZ" style="color: #0066cc; text-decoration: underline" type="text">
										{{ lang.p12 }}
									</el-button>
								</div>
							</div>
							<div class="tips" v-else>
								<div>
									{{ lang.p9 }}.
									<el-tooltip popper-class="cusToolTip" effect="light" :content="itemData.tips"
										placement="top-start">
										<b class="icon-wenhao1"></b>
									</el-tooltip>
								</div>
							</div>
						</div>
					</div>
				</div>

				<div :class="isUpload ? 'later-active' : ''" class="later-padding">
					<div class="later" @click="updateCheckBox($event)">
						<span class="circle2" :class="{
							active: isUpload,
						}"></span>
						<span class="later-text">{{ lang.Uploadbyemaillater }}</span>
						<!-- <MySwitch :active="isUpload" style="padding-left: 5px; padding-right: 5px"></MySwitch> -->
						<!-- <span v-show="isUpload">({{ lang.p26 }})</span> -->
					</div>
					<span class="t2">
						{{ lang.p26 }}
						<a @click.stop="" class="click_text" :href="`mailto:${userEmail}`">
							{{ userEmail }}.
						</a>
					</span>
				</div>
				<!-- <div v-show="!isUpload" style="height:22px"></div> -->
			</div>
		</div>
		<div class="confirmBtnWrap">
			<QuoteBtn @click.native="showMaskFn(itemData.paramName)">{{ lang.next }}</QuoteBtn>
		</div>
	</div>
</template>

<script>
import PriceText from "@/components/Quote/PriceText";
import QuoteBtn from "@/components/Quote/QuoteBtn";
import MySwitch from "@/components/Medals/MySwitch";
import { uploadFile } from "@/utils/oss";
import {checkFile,acceptFileType} from "@/utils/validate";

export default {
	props: {
		itemData: Object,
		index: Number,
		uploadList: Array,
		isUpload: Boolean,
		remark1: String,
		remark2: String,
		no: {
			type: Boolean,
			default: true,
		},
		editActive: {
			type: Boolean,
			default: true,
		},
		tipShow: Boolean,
		addTextShow:{
			type: Boolean,
			default: true
		}
	},
	data() {
		return {
            acceptFileType,
			dropActive: false,
			showUpload: false,
			fileList: [],
		};
	},
	components: {
		PriceText,
		QuoteBtn,
		MySwitch,
	},
	methods: {
		updateEdit() {
			this.$emit("update:editActive", !this.editActive);
			this.clearEdit();
			if (this.uploadList.length > 0) {
				this.fileList = [...this.uploadList];
			}
			this.$emit("update:uploadList", []);
			this.showUpload = false;
			this.$emit("update:isUpload", false);
		},
		updateEdit1() {
			this.$emit("update:editActive", false);
			if (this.uploadList.length > 0) {
				this.fileList = [...this.uploadList];
			}
			this.$emit("update:uploadList", []);
			this.showUpload = false;
			this.$emit("update:isUpload", false);
		},
		clearEdit() {
			this.$emit("update:remark1", "");
			this.$emit("update:remark2", "");
			this.$emit("update:tipShow", false);
		},
		updateRemark1(val) {
			this.$emit("update:tipShow", false);
			this.$emit("update:remark1", val)
		},
		updateRemark2(val) {
			this.$emit("update:tipShow", false);
			this.$emit("update:remark2", val)
		},
		toDZ() {
			this.$emit("toDZ");
		},
		updateCheckBox(e) {
			e.preventDefault();
			this.$store.commit("SET_UploadSwitch", !this.isUpload);
			if (this.isUpload) {
				if (this.fileList.length > 0) {
					this.$emit("update:uploadList", [...this.fileList]);
				} else {
					this.$emit("update:uploadList", []);
				}
			} else {
				this.$emit("update:uploadList", []);
			} 
			this.showUpload = false;
			this.$emit("update:isUpload", !this.isUpload);
			// this.editActive=false;
			this.$emit("update:editActive", true);
			this.clearEdit();
		},
		closeMask() {
			this.$emit("closeMask");
		},
		delUploadImg(index) {
			this.uploadList.splice(index, 1);
			this.fileList.splice(index, 1);
		},
		showMaskFn(name) {
			this.$emit("showMaskFn", name);
		},
		openUpload() {
			this.showUpload = !this.showUpload;
			this.$emit("update:isUpload", false);
			if (this.fileList.length > 0) {
				this.$emit("update:uploadList", [...this.fileList]);
			}
			// this.editActive=false;
			this.$emit("update:editActive", true);
			this.clearEdit()
		},
		uploadArtwork() {
			this.showUpload = true
			this.$refs.upload.click();
		},
		replayUpload() {
			this.openUpload();
			this.$store.commit("setSizeDialog", false);
		},
		uploadPic(event,type='upload') {
            this.$gl.show();
            let files = type === "upload" ? event.target.files : event;
            let fileResult = checkFile(Array.from(files), this.acceptFileType, 80);
            if (!fileResult) {
                this.$toast.error("File type error");
                this.$gl.hide();
                return false;
            }
			if (fileResult.nomalSize.length == 0) {
                this.$gl.hide();
				this.$store.commit('setSizeDialog', true);
				this.$store.commit("setInputRefName", 'replayUpload');
				this.$store.commit('setOverSizeList', fileResult.overSize);
				this.$refs.upload.value = "";
				return false;
			}
			let uploadPromises = [];
			fileResult.nomalSize.forEach((file) => {
				let promise = uploadFile(file).then((res) => {
					this.uploadList.push({
						original_filename: file.name,
						secure_url: res,
						size: (file.size / 1024).toFixed(1),
					})
				});
				uploadPromises.push(promise);
			});
			Promise.all(uploadPromises).then(() => {
                this.$gl.hide();
				this.$refs.upload.value = "";
				if (fileResult.overSize.length > 0) {
					this.$store.commit('setSizeDialog', true);
					this.$store.commit("setInputRefName", 'replayUpload');
					this.$store.commit('setOverSizeList', fileResult.overSize);
				}
			});
		},
		dragleave(e) {
			e.stopPropagation();
			e.preventDefault();
			this.dropActive = false;
		},
		dragenter(e) {
			e.stopPropagation();
			e.preventDefault();
			this.dropActive = true;
		},
		dragover(e) {
			e.stopPropagation();
			e.preventDefault();
			this.dropActive = true;
		},
		drop(e) {
			e.stopPropagation();
			e.preventDefault();
			let files = e.dataTransfer.files;
			this.uploadPic(files,'drop')
		},
	},
	computed: {
		lang() {
			return this.$store.getters.lang.quote || {};
		},
		userEmail() {
			return this.$store.state.proSystem.email;
		},
	},
	mounted() {
		const dropArea = this.$refs.uploadBox;
		dropArea.addEventListener("drop", this.drop, false);
		dropArea.addEventListener("dragleave", this.dragleave, false);
		dropArea.addEventListener("dragenter", this.dragenter, false);
		dropArea.addEventListener("dragover", this.dragover, false);
		this.$Bus.$on('replayUpload', this.replayUpload)
	},
	beforeDestroy() {
		const dropArea = this.$refs.uploadBox;
		dropArea.removeEventListener("drop", this.drop);
		dropArea.removeEventListener("dragleave", this.dragleave);
		dropArea.removeEventListener("dragenter", this.dragenter);
		dropArea.removeEventListener("dragover", this.dragover);
		this.$Bus.$off('replayUpload')
	},
};
</script>

<style scoped lang="scss">
.step-upload {
	.confirmBtnWrap {
		display: none;
		margin-top: 23px;
	}

	&.mask {
		.confirmBtnWrap {
			display: flex;
			justify-content: center;
			align-items: center;
		}
	}
}

.upload-box.dropActive {
	border-color: $color-primary !important;
}

.circle2 {
	position: relative;
	width: 20px;
	height: 20px;
	border: 1px solid #CCC;
	border-radius: 50%;
	margin-right: 10px;
	background-color: #fff;

	&::after {
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		background-color: #fff;
		content: "";
		width: 10px;
		height: 10px;
		border-radius: 50%;
	}

	&.active {
		background-color: $color-primary !important;
		border-color: $color-primary !important;
	}
}

.t1 {
	display: flex;
	align-items: center;

	b {
		color: #9e9e9e;
		margin-right: 7px;
		font-size: 18px;
	}
}

.later {
	cursor: pointer;
	display: flex;
	align-items: center;
	font-size: 16px;
	font-weight: bold;

	.later-text {
		display: flex;
		column-gap: 7px;
		align-items: center;

		&::before {
			content: "\e7a1";
			font-family: "modalicon";
			color: #9e9e9e;
		}
	}
}

.later-padding {
	padding: 10px 20px;

	@media screen and (max-width: 767px) {
		padding: 8px 11px;
	}
}

.later-active {
	background: #FAECE5;
}

.t2 {
	color: #999;
	font-size: 14px;
	padding-left: 30px;
}

.uploadBtnWrap {
	position: relative;

	input[type="file"] {
		position: absolute;
		left: 0;
		top: 0;
		right: 0;
		bottom: 0;
		opacity: 0;
		z-index: -1;
		width: 0;
		height: 0;
	}

	button.isDisabled {
		background: $color-primary !important;
		opacity: 0.5;
	}
}

::v-deep .el-checkbox__input.is-checked .el-checkbox__inner {
	background-color: $color-primary;
	border-color: $color-primary;
}

::v-deep .el-checkbox__input.is-checked+.el-checkbox__label {
	color: $color-primary;
}

.click_text {
	color: #007aff;
	cursor: pointer;
}

.step-upload {
	position: relative;
	background-color: #fff;
	border-radius: 10px;
	padding: 20px;

	.step-title {
		>span {
			color: $color-primary;
		}
	}

	.step-box {
		.upload-text {
			padding: 10px 20px;
			margin-bottom: 14px;
		}

		.upload-text.text-active {
			background: #FAECE5;
		}

		.t1 {
			margin-bottom: 4px;
			font-weight: bold;
			font-size: 16px;
			color: #333333;

			@media screen and (max-width: 767px) {
				font-size: 14px !important;
				margin-bottom: 2px !important;
			}
		}

		.t2 {
			font-size: 14px;
			color: #333;

			@media screen and (max-width: 767px) {
				font-size: 12px;
				padding-left: 17px;
				margin-bottom: 0 !important;
			}
		}

		.upload-box {
			display: flex;
			flex-direction: column;
			justify-content: space-between;
			align-items: center;
			margin: 14px 0 11px 50px;
			padding: 10px;
			background: #ffffff;
			border: 1px dashed #e9ecf0;
			border-radius: 10px;
			cursor: pointer;
			transition: all 0.3s;

			.el-checkbox__label {
				font-size: 18px;
			}

			@media (any-hover: hover) {
				&:hover {
					border-color: $color-primary;
				}
			}

			.uploadList {
				width: 100%;
				margin-bottom: 12px;
				text-align: center;

				.uploadIcon {
					margin-top: 11px;
					font-size: 32px;
					color: #9e9e9e;
				}

				.uploadItem {
					display: flex;
					justify-content: space-between;
					align-items: center;
					padding: 5px;
					font-size: 14px;
				}

				.myIcon {
					margin: 0 4px;
				}
			}

			.upload-btn {
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;

				button {
					width: 172px;
					height: 40px;
					margin-bottom: 10px;
					background: $color-primary;
					border-radius: 10px;
					border: none;
					color: #fff;
					font-weight: bold;
				}

				.el-checkbox__label {
					font-size: 16px;
				}
			}

			.tips {
				font-size: 15px;
				color: #b3b3b3;
				text-align: center;
			}
		}



		// @media screen and (min-width: 768px) and (max-width: 1499px) {
		// 	grid-template-columns: 2fr 1.4fr;
		// 	column-gap: 20px;
		// }

		// @media screen and (max-width: 767px) {
		// 	grid-template-columns: repeat(1, 1fr);
		// 	height: auto;

		// 	.t1 {
		// 		margin-bottom: 5px;
		// 		font-size: 12px;
		// 	}

		// 	.t2 {
		// 		margin-bottom: 5px;
		// 		font-size: 12px;
		// 	}

		// 	.upload-box {
		// 		height: 150px;

		// 		.el-checkbox__label {
		// 			font-size: 12px !important;
		// 		}

		// 		.uploadList {
		// 			height: 80px;

		// 			.uploadItem {
		// 				font-size: 12px;
		// 			}

		// 			.uploadIcon {
		// 				width: 51px;
		// 				height: 41px;
		// 				margin-top: 0;
		// 				font-size: 48px !important;
		// 			}
		// 		}

		// 		.upload-btn button {
		// 			width: 147px;
		// 			height: 30px;
		// 			margin-bottom: 10px;
		// 			font-size: 14px;
		// 			border-radius: 4px;
		// 		}

		// 		.upload-btn {
		// 			.el-checkbox__inner {
		// 				width: 15.8px;
		// 				height: 15.8px;

		// 				&::after {
		// 					left: 5px;
		// 					top: 2px;
		// 				}
		// 			}
		// 		}

		// 		.tips {
		// 			font-size: 12px;
		// 		}
		// 	}

		// 	.editArea {
		// 		margin-top: 10px;

		// 		.tip {
		// 			margin: 5px 0;
		// 			font-size: 12px;
		// 		}

		// 		.pcInput {
		// 			display: none;
		// 		}

		// 		.mbInput {
		// 			display: block;
		// 		}
		// 	}
		// }
	}

	.box-border {
		display: none;

		.el-icon-close {
			display: flex;
			justify-content: center;
			align-items: center;
			position: absolute;
			font-weight: 700;
			top: 0;
			right: 0;
			width: 40px;
			height: 40px;
			transform: translate(50%, -50%);
			cursor: pointer;
			background: #ffffff;
			border-radius: 50%;
			box-shadow: 1px 2px 20px 0 rgba(0, 0, 0, 0.2);
			z-index: 10;
		}
	}

	&.mask {
		position: relative;
		z-index: 101;

		.confirmBtnWrap {
			position: relative;
		}

		.box-border {
			position: absolute;
			left: 0;
			right: 0;
			top: 0;
			bottom: 0;
			display: block;
			background-color: #fff;
			border: 1px solid #d9dbdd;
		}

		.step-title {
			position: relative;
		}

		.step-box {
			position: relative;
		}
	}

	.step-title {
		font-size: 24px;
		font-weight: 700;
		color: #333333;
		margin-bottom: 23px;

		.step-title-icon {
			width: 21px;
			margin-left: 4px;
			cursor: pointer;
			vertical-align: middle;
		}
	}

	@media screen and (max-width: 767px) {
		margin-bottom: 10px;
		background-color: #fff;
		border-radius: 5px;
		padding: 20px 7px;

		&.mask {
			.box-border {
				.el-icon-close {
					width: 30px;
					height: 30px;
					transform: translate(0, 0);
					box-shadow: none;
				}
			}
		}

		.step-title {
			margin-bottom: 10px;
			font-size: 14px;
			font-weight: bold;
			color: #171719;

			.step-title-icon {
				width: 17px;
				margin-left: 5px;
			}
		}
	}
}

::v-deep .editArea {
	.input-text {
		padding-left: 50px;
		overflow: hidden;
		margin-bottom: 4px;

		&>div {
			margin-bottom: 10px;
		}

		.edit-tip {
			color: $color-primary;
		}

		@media screen and (max-width: 767px) {
			padding-left: 28px;
			margin-bottom: 0;

			&>div {
				margin-bottom: 6px;
			}
		}
	}

	.el-input-group--append {
		.el-input__inner {
			border-color: #EBEBEB;
			border-radius: 5px 0 0 5px;
			font-size: 16px;

			@media screen and (max-width: 767px) {
				font-size: 12px;
			}
		}

		.el-input-group__append {
			border-color: #EBEBEB !important;
			background: #fff !important;
			border-radius: 0 5px 5px 0;
			min-width: 117px;
			color: #333;
			font-size: 16px;

			@media screen and (max-width: 767px) {
				min-width: 80px;
				font-size: 12px;
				padding: 10px;
			}
		}
	}
}

@media screen and (max-width: 767px) {
	.step-upload {
		.confirmBtnWrap {
			margin-top: 20px;
		}
	}

	.circle2 {
		width: 12px;
		height: 12px;
		margin-right: 5px;

		&::after {
			width: 6px;
			height: 6px;
		}
	}

	.later {
		.later-text {
			font-size: 14px;
			margin-bottom: 2px;
			column-gap: 5px;
		}
	}

	.step-upload {
		.step-box {
			.upload-text {
				padding: 8px 11px;
				margin-bottom: 6px;

				b {
					font-size: 13px !important;
				}
			}


			.t1 {
				font-size: 14px !important;
				margin-bottom: 2px !important;
			}

			.t2 {
				font-size: 12px;
				padding-left: 17px;
				margin-bottom: 0 !important;
			}

			.upload-box {
				margin:5px 0 5px 28px;
				.uploadList {
					margin-bottom: 10px;
					.uploadIcon {
						margin-top:5px;
						font-size: 36px;
					}
				}

				.upload-btn {
					button {
						width: 147px;
						height: 30px;
						font-size: 14px;
						margin-bottom: 6px;
						background: $color-primary;
						border-radius: 5px;
						border: none;
						color: #fff;
						font-weight: bold;
					}

					.el-checkbox__label {
						font-size: 16px;
					}
				}

				.tips {
					font-size: 12px;
					margin-bottom: 5px;
				}
			}



			// @media screen and (min-width: 768px) and (max-width: 1499px) {
			// 	grid-template-columns: 2fr 1.4fr;
			// 	column-gap: 20px;
			// }

			// @media screen and (max-width: 767px) {
			// 	grid-template-columns: repeat(1, 1fr);
			// 	height: auto;

			// 	.t1 {
			// 		margin-bottom: 5px;
			// 		font-size: 12px;
			// 	}

			// 	.t2 {
			// 		margin-bottom: 5px;
			// 		font-size: 12px;
			// 	}

			// 	.upload-box {
			// 		height: 150px;

			// 		.el-checkbox__label {
			// 			font-size: 12px !important;
			// 		}

			// 		.uploadList {
			// 			height: 80px;

			// 			.uploadItem {
			// 				font-size: 12px;
			// 			}

			// 			.uploadIcon {
			// 				width: 51px;
			// 				height: 41px;
			// 				margin-top: 0;
			// 				font-size: 48px !important;
			// 			}
			// 		}

			// 		.upload-btn button {
			// 			width: 147px;
			// 			height: 30px;
			// 			margin-bottom: 10px;
			// 			font-size: 14px;
			// 			border-radius: 4px;
			// 		}

			// 		.upload-btn {
			// 			.el-checkbox__inner {
			// 				width: 15.8px;
			// 				height: 15.8px;

			// 				&::after {
			// 					left: 5px;
			// 					top: 2px;
			// 				}
			// 			}
			// 		}

			// 		.tips {
			// 			font-size: 12px;
			// 		}
			// 	}

			// 	.editArea {
			// 		margin-top: 10px;

			// 		.tip {
			// 			margin: 5px 0;
			// 			font-size: 12px;
			// 		}

			// 		.pcInput {
			// 			display: none;
			// 		}

			// 		.mbInput {
			// 			display: block;
			// 		}
			// 	}
			// }
		}
	}
}</style>
