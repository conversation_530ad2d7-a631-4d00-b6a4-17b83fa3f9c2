<template>
    <BaseDialog :value="patchesAdhesiveDialog" @update="$emit('update:patchesAdhesiveDialog', $event)" persistent
        :width="device != 'mb' ? '778px' : '95%'" style="zIndex:10000;">
        <div class="content-patches">
            <div class="header">
                {{ itemData.alias }}
            </div>
            <div class="body">
                <div class="imgWrap">
                    <img :src="device === 'pc' ? 'https://static-oss.gs-souvenir.com/web/quoteManage/20250225/作图1_20250225KdhjTX.jpg' : 'https://static-oss.gs-souvenir.com/web/quoteManage/20250228/%E4%BD%9C%E5%9B%BEMB_20250228H8sjXJ.jpg' " alt="" />
                </div>
                <div class="colorNumber_title">{{ lang.adhesiveText }}</div>
                <div class="colorNumber">
                    <div class="colorNumberContent">
                        <div class="itemWrap1" v-for="(item, index) in adhesiveList" :key="index"
                            @click="selectColor(item)" style="text-align: center;">
                            <div class="itemWrap" :class="{ active: hasActive(item) }">
                                <img :src="item.imgUrl" :alt="item.code" :title="item.code" />
                            </div>
                            <div class="texture">
                                <div class="circle-patches">
                                    <div class="inner-circle" :class="{ active: hasActive(item) }"></div>
                                </div>
                                <span class="code">{{ item.code }}</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="comments">
                    <div class="comments_others" @click="selectColor('Others')">
                        <div class="circle-patches">
                            <div class="inner-circle" :class="{ active: isOthersSelected }"></div>
                        </div><span>Others</span>
                    </div>
                    <el-input v-model="remark" :placeholder="lang.adhesiveInputText" @change="othersChange" :disabled="!isOthersSelected"></el-input>
                </div>
            </div>
            <div class="footer-patches">
                <QuoteBtn :disabled="disabledBtn()" @click.native="confirm">
                    {{ lang.next }}
                </QuoteBtn>
            </div>
        </div>
    </BaseDialog>
</template>

<script>
import BaseDialog from "@/components/Quote/BaseDialog.vue";
import QuoteBtn from "@/components/Quote/QuoteBtn.vue";

export default {
    props: {
        patchesAdhesiveDialog: {
            type: Boolean,
        },
        itemData: {
            type: Object,
        },
        adhesiveList: {
			type: Array,
		},
        selectedTextures:{
            type: Array,
        }
    },
    data() {
        return {
            localSelectedTextures: [],
            isOthersSelected: false, // Others是否被选中
            remark: '' // 输入的其他项
        };
    },
    components: { QuoteBtn, BaseDialog },
    computed: {
        device() {
            return this.$store.state.device;
        },
        lang() {
            return this.$store.getters.lang.quote || {};
        },
        currencySymbol() {
            return this.$store.getters.currencySymbol;
        },
        currencyRate() {
            return this.$store.getters.currencyRate;
        },
    },
    methods: {
        disabledBtn() {
            return this.localSelectedTextures.length ? false : true;
        },
        othersChange(){
            let find = this.localSelectedTextures.find((item)=>item.code === "Others");
            if(find){
                find.name = this.remark
            }else{
                this.localSelectedTextures.push({
                    id:1,
                    code:'Others',
                    name:this.remark,
                    type:this.device === 'pc' ? 'Adhesive-PC' : 'Adhesive-MB'
                })
            }
        },
        selectColor(item) {
            if (item === 'Others') {
                this.isOthersSelected = !this.isOthersSelected; // 切换 "Others" 的选中状态
                if (this.isOthersSelected === false) {
                    const findIndex = this.localSelectedTextures.findIndex(i => i.code === 'Others');
                    if (findIndex !== -1) {
                        this.localSelectedTextures.splice(findIndex, 1);
                        this.remark = "";
                    }
                } else {
                    // 如果选择，将“Others”添加到localSelectedTextures中
                    this.localSelectedTextures.push({ name: 'Others', code: 'Others' });
                }
            } else {
                const index = this.localSelectedTextures.findIndex(selected => selected.code === item.code);
                if (index === -1) {
                    this.localSelectedTextures.push(item);
                } else {
                    this.localSelectedTextures.splice(index, 1);
                }
            }
            // 向父组件发出更新后的数据
            this.$emit('update:textures', this.localSelectedTextures);
        },
        hasActive(item) {
            if (item === 'Others') {
                return this.isOthersSelected;
            }
            return this.localSelectedTextures.some(selected => selected.code === item.code);
        },
        confirm() {
            this.itemData.texturesList = this.selectedTextures;
            this.$emit("texturesBtnNext", this.itemData);
         },
    },
    created(){
        //回填材质参数
        this.localSelectedTextures = [...this.selectedTextures];
        if(this.itemData.texturesList && this.itemData.texturesList.length){
            let find = this.itemData.texturesList.find((item)=>item.code === 'Others');
            if(find){
                this.isOthersSelected = true;
                this.remark = find.name;
            }
        }
    },
    mounted() {

     },
};
</script>

<style lang="scss" scoped>
::v-deep .base-dialog{
    z-index: 99999;
}
.content-patches {
    padding: 20px;

    @include respond-to(mb) {
        padding: 10px;
    }

    .header {
        margin-bottom: 10px;
        font-weight: 600;
        font-size: 24px;
        text-align: center;

        @include respond-to(mb) {
            font-size: 14px;
        }
    }
    .active{
        border: 2px solid $color-primary !important;

        &::after {
            background-color: $color-primary !important;
        }
    }

    .body {
        .circle-patches {
            transform: translate(0, 100%);
            width: 28px;
            height: 15px;
            border-top: none;
            cursor: pointer;

            .inner-circle {
                position: absolute;
                left: 50%;
                top: 0;
                transform: translate(-50%, -91%);
                width: 18px;
                height: 18px;
                border-radius: 50%;
                background: #fff;
                border: 2px solid #aaaeb3;
                transition: all 0.3s;

                &::after {
                    content: "";
                    position: absolute;
                    left: 50%;
                    top: 50%;
                    transform: translate(-50%, -50%);
                    width: 6px;
                    height: 6px;
                    background-color: #aaaeb3;
                    border-radius: 50%;
                    transition: all 0.3s;
                }
            }

            &.active,
            &:hover {
                // border: 1px solid $color-primary;

                .inner-circle {
                    border: 2px solid $color-primary;

                    &::after {
                        background-color: $color-primary;
                    }
                }
            }
        }

        .colorNumber_title {
            margin: 30px 0 15px;
						@include respond-to(mb){
							margin: 15px 0 15px;
						}
        }

        .colorNumber {
            margin-bottom: 20px;

            .colorNumberContent {
                display: grid;
                grid-template-columns: repeat(5, 1fr);
                gap: 10px;
                font-size: 14px;

                @include respond-to(mb) {
                    grid-template-columns: repeat(3, 1fr);
                    font-size: 12px;
                }

                .texture {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    margin-top: 5px;

                    ::v-deep .point2 {
                        margin: 0;
                    }
                }

                .itemWrap1 {
                    .itemWrap {
                        overflow: hidden;
                        position: relative;
                        display: flex;
                        flex-direction: column;
                        justify-content: center;
                        align-items: center;
                        min-width: 0;
                        // width: 130px;
                        // height: 99px;
                        // padding: 15px 5px 5px;
                        background: #ffffff;
                        border-radius: 10px;
                        border: 2px solid #dcdfe6;
                        cursor: pointer;

                        @include respond-to(mb) {
                            // padding-top: 5px;
                            border-radius: 4px;
                        }

                        .checkIcon {
                            width: 20px;
                            height: 20px;
                            border-radius: 0 0 0 6px;
                            display: none;
                            z-index: 1;

                            @include respond-to(mb) {
                                width: 18px;
                                height: 18px;

                                ::v-deep .v-icon.v-icon {
                                    font-size: 14px !important;
                                }
                            }
                        }

                        img {
                            width: 100% !important;
                            height: 100% !important;
                            object-fit: contain !important;
                        }

                        .code {
                            margin-bottom: 14px;

                            @include respond-to(mb) {
                                margin-bottom: 5px;
                            }
                        }
                    }

                    &.active,
                    &:hover {
                        .itemWrap {
                            border: 2px solid $color-primary;
                        }

                        .circle-patches {
                            // border: 2px solid $color-primary;

                            .inner-circle {
                                border: 2px solid $color-primary;

                                &::after {
                                    background-color: $color-primary;
                                }
                            }
                        }
                    }
                }
            }
        }

        .comments {
            .el-input {
                margin-top: 10px;
            }

            .comments_others {
                display: flex;
                align-items: center;

                ::v-deep .point2 {
                    margin: 0;
                }
            }

            ::v-deep .el-input__inner:focus {
                border-color: $color-primary;
            }
        }
    }

    .footer-patches {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 20px 20px 0;

        button {
            margin: 0 10px;
        }
    }
}
</style>
