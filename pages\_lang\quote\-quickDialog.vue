<template>
	<div class="quoteWrap">
		<Preloader v-if="isLoading"></Preloader>
		<template v-else>
            <QuoteNav :pid="pid" :title="navTitle" style="margin-bottom: 10px; padding-top: 20px"></QuoteNav>
			<div class="content" :class="{hideBg:!twentyFourHours}" :style="{ backgroundImage: `url(${configJson && configJson.backgroundImg})` }">
                <div class="leftArea">
					<h2 :style="twentyFourHours || (!twentyFourHours && device === 'mb') ? 'color:#fff' : 'color:#333'">
						{{ configJson.descTitle_translate }}
					</h2>
					<div class="des" :style="twentyFourHours || (!twentyFourHours && device === 'mb') ? 'color:#fff' : 'color:#333'">
						<p v-for="(item, index) in description" :key="index">{{ item.str }}</p>
					</div>
					<div v-if="!twentyFourHours" class="video">
						<OrnamentsVideo ref="videoPlayer" class="background-video" disabled-mouse :options="getVideoOptions(productVideo, posterImage)"></OrnamentsVideo>
					</div>
                    <CustomSwiper :image-json="imageJson" v-if="twentyFourHours && imageJson.length" @changeSlide="changeSlide"></CustomSwiper>
					<div class="btnWrap" v-if="twentyFourHours">
						<button @click.stop="selectShape" :disabled="disabledBtn">{{ lang.selectShape }}</button>
					</div>
				</div>
				<div class="rightArea">
					<div class="quoteContent" ref="excludeElement">
						<NewQuoteStep ref="quoteStep" :pid="pid" :cateId="cateId"></NewQuoteStep>
					</div>
				</div>
			</div>
		</template>
	</div>
</template>

<script>
import QuoteNav from "@/components/Medals/QuoteNav.vue";
import Preloader from "@/components/Quote/Preloader.vue";
import StepBar from "@/components/modal/Quote/QuoteComponents/StepBar.vue";
import NewQuoteStep from "@/components/Quote/QuickQuote/NewQuoteStep.vue";
import {getQuickQuoteConfig} from "assets/js/quickQuoteConfig";
import { getInfo } from "@/api/pins";
import { scrollToViewCenter } from "@/utils/utils";
import OrnamentsVideo from "@/components/Ornaments/index.vue";
import CustomSwiper from "@/components/modal/Quote/QuoteComponents/CustomSwiper.vue";
export default {
	components: {
        CustomSwiper,
        NewQuoteStep,
		StepBar,
		Preloader,
		QuoteNav,
		OrnamentsVideo,
	},
	data() {
        const config = getQuickQuoteConfig.call(this, this.$route.name);
		return {
            activeIndex: 0,
			disabledBtn: false,
			isLoading: true,
			description: [],
			imageJson: [],
			configJson: null,
			currentStep: 1,
			...config,
		};
	},
	computed: {
		twentyFourHours() {
			return this.pid === 627;
		},
		lang() {
			return this.$store.getters.lang.quote || {};
		},
		device() {
			return this.$store.state.device;
		},
		posterImage() {
			return this.device !== "mb" ? "https://static-oss.gs-souvenir.com/web/quoteManage/20241230/custom_shape_rush_medal_2066a8thba.png" : "https://static-oss.gs-souvenir.com/web/quoteManage/20241230/custom_shape_rush_printed_medals_206653wpDz.png";
		},
		productVideo() {
			return this.device !== "mb" ? "https://static-oss.gs-souvenir.com/web/quoteManage/20241230/rush_printed_medals_within_48_hours_2066W6wXrP.mp4" : "https://static-oss.gs-souvenir.com/web/quoteManage/20241230/48_hours_rush_medals_2066HNGbRW.mp4";
		},
	},
	methods: {
        changeSlide(val){
			this.activeIndex = val;
			if(!this.imageJson[this.activeIndex]?.name){
				return
			}
			this.$refs.quoteStep.initQuote({
				shapeId: this.imageJson[this.activeIndex]?.name
			})
			this.$refs.quoteStep.initShowSelected();
			this.$refs.quoteStep.startAnimation();
			this.disabledBtn = false;
        },
		getVideoOptions(url, img) {
			return {
				autoplay: false,
				controls: false,
				muted: true,
				loop: false,
				fill: true,
				preload: "auto",
				poster: img,
				sources: [
					{
						src: url,
						type: "video/mp4",
					},
				],
			};
		},
		selectShape() {
			scrollToViewCenter(document.querySelector(".quoteContent"));
			this.$refs.quoteStep.initQuote({
				shapeId: this.imageJson[this.activeIndex].name,
			});
			this.$refs.quoteStep.initShowSelected();
			this.disabledBtn = true;
		},
	},
	async mounted() {
		let res = await getInfo({ id: this.cateId });
		this.isLoading = false;
		this.description = res.data.description ? JSON.parse(res.data.description) : [];
		this.imageJson = res.data.imageJson ? JSON.parse(res.data.imageJson) : [];
		this.configJson = res.data.configJson ? JSON.parse(res.data.configJson) : [];
	},
};
</script>

<style scoped lang="scss">
button[disabled] {
	pointer-events: none;
	opacity: 0.7;
}

@media screen and (max-width: 767px) {
	::v-deep .vjs-paused .vjs-big-play-button {
		display: block !important;
	}
}

@include respond-to(mb) {
	::v-deep .video-js .vjs-big-play-button {
		display: block !important;
	}

	::v-deep .vjs-paused .vjs-big-play-button {
		display: block !important;
	}
	::v-deep .showPlayBtn .vjs-has-started.vjs-playing .vjs-big-play-button {
		display: none !important;
	}
	::v-deep .videoBox::after {
		display: none;
	}
}

::v-deep .showPlayBtn .vjs-has-started.vjs-paused .vjs-big-play-button {
	display: block !important;
}

::v-deep .showPlayBtn .vjs-has-started.vjs-playing .vjs-big-play-button {
	display: none !important;
}

.quoteWrap {
	font-family: Calibri, Arial, serif;
    font-size: 16px;

    @include respond-to(mb){
        font-size: 12px;
    }

	.content {
		position: relative;
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 6.25em;
		background-size: cover;
		background-position: center;
		background-repeat: no-repeat;
		padding: 5em max(calc(50% - 860px), 1.5vw);
		background-color: #cccccc;

		@include respond-to(mb) {
			grid-template-columns: repeat(1, 1fr);
			gap: 2em;
            padding: 5em max(calc(50% - 860px), 1.5vw) 1em;
            &.hideBg{
                background: #bebebe;
                background-image: none !important;
            }
		}

		.background-video {
			position: absolute !important;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;

			::v-deep .video-js video {
				object-fit: cover !important;
			}

			::v-deep .vjs-big-play-button {
				left: 20%;
				top: 60%;
				font-size: 32px;
				width: 49px;
				height: 47px;
				@include respond-to(mb) {
					left: 56%;
					top: 58%;
				}
			}

			@include respond-to(mb) {
				height: 53% !important;
			}
		}

		.leftArea {
			min-width: 0;
			color: #fff;
			@include respond-to(mb) {
				.video {
					height: 78vw;
				}
			}

			.btnWrap {
				text-align: center;

				button {
					width: 14em;
					height: 2.5em;
					border-radius: 6px;
					color: #ffffff;
					font-size: 1em;
					border: none;
					cursor: pointer;
					background-color: $color-primary;
				}
			}

			h2 {
				margin-bottom: 0.4em;
				text-align: left;
				font-size: 2.25em;
				font-weight: 700;
				color: #fff;
				position: relative;
				bottom: 40px;
				z-index: 2;
				@include respond-to(mb) {
                    width: 80%;
                    margin: 0 auto .7em;
					text-align: center;
                    font-size: 1.5em;
				}
			}

			.des {
				padding-right: 1em;
				position: relative;
				bottom: 45px;
				z-index: 2;

				p {
					margin-bottom: 0.625em;
				}

				@include respond-to(mb) {
					text-align: center;
					padding: 0;
				}
			}
		}

		.rightArea {
			min-width: 0;

            ::v-deep .quoteBox{
                min-height: 41em;
            }

			@include respond-to(mb) {
				.stepBar {
					display: none;
				}
			}
		}
	}
}
</style>
