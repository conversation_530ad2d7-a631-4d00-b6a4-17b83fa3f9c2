<template>
	<div class="uploadDrawer">
		<div class="top-content">
			<div class="uploadDrawerHeader">
				<span>{{ langSemi.chooseLogo }}</span>
				<div class="borderBox"></div>
			</div>
			<!-- 标签列表 -->
			<div>
				<div class="tag-div">
					<div v-for="(item, index) in medalsIconTagList" :key="index" @click="tagNameClick(item)"
						:class="showMedalsId === item.id ? 'tt' : 'div'">{{ item.tagName }}</div>
				</div>
				<div class="items-cont" v-if="false">
					<div class="mySwiperDrawer swiper" ref="mySwiperDrawer">
						<div class="swiper-wrapper">
							<div class="swiper-slide pointer" v-for="(item, index) in medalsIconTagList"
								:class="{ active: showMedalsId === item.id }" @click="tagNameClick(item)" :key="index">
								<p class="item-content">{{ item.tagName }}</p>
							</div>
						</div>
						<div class="swiper-pagination swiper-pagination-drawer"></div>
					</div>
				</div>
			</div>
			<!-- 上传 -->
			<div>
				<div class="upload-div" :class="{ noUpload: mbNoUploadStyle }">
					<div class="img">
						<div class="uploadList">
							<div class="swiper myswiper2" ref="swiper2">
								<div class="swiper-wrapper">
									<div class="swiper-slide pointer" v-for="(item, index) in uploadList" :key="index">
										<div class="imgBox">
											<CustomImage :url="item.secure_url" :show-file-name="false"
												:preview="false"></CustomImage>
											<div class="fileNameBox">
												<div class="li-imgName">{{ item.original_filename }}</div>
												<span class="myIconBox">
													<b class="icon-xuanzhong1"></b>
													<b class="icon-shanchu2 myIcon" @click.stop="delUploadImg"></b>
												</span>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div class="text" @click="$refs.uploadDrawer.click()">
						<b class="icon-shangchuan"></b>
						<div style="white-space: nowrap; user-select: none;display: flex;align-items: center;">
							<strong v-if="device !== 'mb'">{{ lang.uploadYourDesign }}</strong>
							<strong v-else>{{ lang.uploadDesign }}</strong>
							<ToolTip class="uploadTip"
								:titleContent="'Allowed extensions: EPS, AI, PDF, JPG, JPEG, GIF, PNG, BMP, TIF, SVG, WORD, EXCEL, TXT, WEBP.'"
								v-show="device !== 'mb'" :item-data="bindValue"></ToolTip>
							<ToolTip class="uploadTip3"
								:titleContent="'Allowed extensions: EPS, AI, PDF, JPG, JPEG, GIF, PNG, BMP, TIF, SVG, WORD, EXCEL, TXT, WEBP.Maximum file size 80M.'"
								v-show="device == 'mb'" :item-data="bindValue"></ToolTip>
						</div>
						<div class="uploadSize">
							{{ lang.maxSize80 }}
							<ToolTip class="uploadTip2"
								:titleContent="'Allowed extensions: EPS, AI, PDF, JPG, JPEG, GIF, PNG, BMP, TIF, SVG, WORD, EXCEL, TXT, WEBP.'"
								v-show="device == 'mb'" :item-data="bindValue"></ToolTip>
						</div>
						<input type="file" ref="uploadDrawer" :accept="acceptFileType" multiple @change="uploadPic" />
					</div>
				</div>
			</div>
			<!-- 图标列表 -->
			<div class="iconListBox custom-scrollbar">
				<div class="iconBox">
					<loader :loadState="!canvas.loadImgState"></loader>
					<div class="iconItem" :class="{ active: isActive(item.id) }"
						v-for="(item, index) in showMedalsIconList" :key="index" @click="debounceClickImg(item)">
						<img :src="item.iconUrl" style="aspect-ratio: 1/1" />
					</div>
				</div>
			</div>
		</div>
		<div class="continue" @click="toNext">
			<nextStep :style="{ opacity: uploadList.length > 0 ? 1 : 0.5, }" :disabled="!(uploadList.length > 0)">{{
				lang.continue }}</nextStep>
		</div>
	</div>
</template>

<script>
import { generateUUID, debounce } from "@/utils/utils"
import { uploadFile } from "@/utils/oss";
import ToolTip from '@/components/HalfDesign/customMedals/common/ToolTip'
import nextStep from '@/components/HalfDesign/customMedals/common/nextStep'
import CustomImage from "@/components/CustomImage";
import { checkFile } from "@/utils/validate";
import loader from "@/components/HalfDesign/customMedals/common/loader.vue";
export default {
	name: "uploadDrawer",
	inject: ["canvas"],
	props: {
		showMedalsId: {
			type: [Number, String],
		},
		medalsIconList: Array,
		medalsIconTagList: Array,
		uploadList: {
			type: Array,
			default: () => []
		},
		clickImgId: {
			type: Array,
			default: () => []
		},
		//绑定值
		bindValue: {
			type: Object,
		},
	},
	data() {
		return {
			acceptFileType: ".JPG,.JPEG,.jpe,.jif,.jfif,.jfi,.GIF,.PNG,.BMP,.WEBP",
			myswiper2: null,
			selfUplodList: [],
			selfClickImgId: [],
			debounceClickImg: null
		};
	},
	watch: {
		selfUplodList: {
			deep: true,
			handler(newVal) {
				this.$emit("update:uploadList", newVal);
			},
		},
		selfClickImgId: {
			deep: true,
			handler(newVal) {
				this.$emit("update:clickImgId", newVal);
			},
		},
	},
	computed: {
		lang() {
			return this.$store.getters.lang.quote || {};
		},
		langSemi() {
			return this.$store.getters.lang.semiCustom || {};
		},
		device() {
			return this.$store.state.device;
		},
		mbNoUploadStyle() {
			return this.device == "mb" && this.uploadList.length == 0;
		},
		showMedalsIconList() {
			if (this.showMedalsId >= 0) {
				let nowShow = this.medalsIconList.find((item) => {
					return item.id == this.showMedalsId;
				})
				if (nowShow) {
					return nowShow.copyThemeLogoImgList;
				}
			}
			return this.medalsIconList.reduce((pre, cur) => {
				if (Array.isArray(cur.themeLogoList) && cur.themeLogoList.length > 0) {
					let data = cur.copyThemeLogoImgList || []
					pre = pre.concat(data);
				}
				return pre;
			}, [])
		},
	},
	components: {
		ToolTip,
		nextStep,
		CustomImage,
		loader
	},
	methods: {
		tagNameClick(item) {
			this.$emit("update:showMedalsId", item.id);
			this.$emit("tagNameClick", item);
		},
		uploadPic(event, type = "upload") {
			this.$gl.show();
			let files = type === "upload" ? event.target.files : event;
			let fileResult = checkFile(Array.from(files), this.acceptFileType, 30);
			if (!fileResult) {
				this.$toast.error("File type error");
				this.$gl.hide();
				return false;
			}
			if (fileResult.nomalSize.length == 0) {
				this.$gl.hide();
				this.$store.commit("setSizeDialog", true);
				this.$store.commit("setInputRefName", "replayUpload");
				this.$store.commit("setOverSizeList", fileResult.overSize);
				this.$refs.uploadDrawer.value = "";
				return false;
			}
			let uploadPromises = [];
			fileResult.nomalSize.forEach((file) => {
				let promise = uploadFile(file).then((res) => {
					this.$emit("delImg")
					this.selfClickImgId = [{
						id: -2,
						iconUrl: res,
					}]
					this.selfUplodList = []
					this.selfUplodList.push({
						id: generateUUID(),
						original_filename: file.name,
						secure_url: res,
						size: (file.size / 1024).toFixed(1),
					});
				});
				uploadPromises.push(promise);
			});
			Promise.all(uploadPromises).then(() => {
				this.$gl.hide();
				this.$emit("addImg")
				this.$refs.uploadDrawer.value = "";
				if (fileResult.overSize.length > 0) {
					this.$store.commit("setSizeDialog", true);
					this.$store.commit("setInputRefName", "replayUpload");
					this.$store.commit("setOverSizeList", fileResult.overSize);
				}
				this.swiperUpdate();
			});
		},
		delUploadImg() {
			this.$emit("delImg")
			this.selfUplodList = []
			this.selfClickImgId = [{
				id: -1,
				iconUrl: "",
			}]
			this.swiperUpdate();
		},
		initSwiper() {
			this.myswiper2 = new Swiper(this.$refs.swiper2, {
				slidesPerView: 3.5,
				breakpoints: {
					0: {
						slidesPerView: 2, // 小屏幕设备
						spaceBetween: 10,
					},
					1000: {
						slidesPerView: 3.5,
						spaceBetween: 10,
					},
				},
				spaceBetween: 10,
				watchSlidesVisibility: true, //防止不可点击
				on: {
					click: this.onSlideClick, // 添加点击事件监听器
				},
			});
		},
		swiperUpdate() {
			this.$nextTick(() => {
				if (this.myswiper2) this.myswiper2.update();
			});
		},
		onImgClick(data) {
			let imgId = data.id;
			let index = this.selfClickImgId.findIndex((item) => item.id == imgId);
			this.$emit("delImg")
			if (index > -1) {
				this.selfClickImgId = [{
					id: -1,
					iconUrl: "",
				}]
				this.selfUplodList = []
				this.swiperUpdate();
				return;
			}
			this.selfClickImgId = [data];
			this.selfUplodList = []
			this.selfUplodList.push({
				original_filename: "logo",
				secure_url: data.iconUrl,
				size: 1024,
				id: imgId,
			});
			this.swiperUpdate();
			this.$nextTick(() => {
				this.$emit("addImg")
			})
		},
		toNext() {
			this.$emit("nextStep");
		},
		initSwiper2() {
			this.mySwiperDrawer = new Swiper(this.$refs.mySwiperDrawer, {
				spaceBetween: 10,
				observer: true,
				observeParents: true,
				watchSlidesVisibility: true,
				hideOnClick: true,
				slidesPerView: this.device === "mb" ? 3 : "auto",
				slidesPerGroup: 1,
				pagination: {
					el: ".swiper-pagination-drawer",
					clickable: true,
				},
			});
		},
		isActive(id) {
			return this.clickImgId.some((item) => item.id == id);
		}
	},
	mounted() {
		this.selfClickImgId = this.clickImgId;
		this.selfUplodList = this.uploadList;
		// this.initSwiper();
		this.debounceClickImg = debounce(this.onImgClick, 800)
		if (this.device === "mb") {
			this.$nextTick(() => {
				// this.initSwiper2();
			});
		}
	},
};
</script>

<style scoped lang="scss">
.borderBox {
	margin-top: 10px;
	width: 100%;
	height: 2px;
	background: #eeeeee;
}

.uploadDrawer {
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	gap: 20px;
	height: 100%;
	padding: 20px;

	.top-content {
		.uploadDrawerHeader {
			flex-shrink: 0;

			span {
				font-weight: bold;
				font-size: 18px;
			}
		}
	}

	@include respond-to(mb) {
		max-height: 80vh;
		padding: 10px;
	}
}

.myswiper2 {
	height: 100%;

	.swiper-slide {
		overflow: hidden;
		border-radius: 6px;
		cursor: pointer;
		border: 2px solid transparent;
		// background-color: #e9e9e9;
		align-items: center;
		display: flex;
		justify-content: center;
		box-sizing: border-box;
	}

	.swiper-slide-thumb-active {
		border-color: #0066cc;
	}

	.imgBox {
		width: 100%;
		height: 100%;
		display: flex;
		align-items: center;
		gap: 6px;
		position: relative;
		border-radius: 5px;
		overflow: hidden;
		max-height: 130px;

		.fileNameBox {
			min-width: 0;

			.li-imgName {
				text-overflow: ellipsis;
				white-space: nowrap;
				overflow: hidden;
			}

			.myIconBox {
				display: flex;
				margin-top: 6px;

				.icon-xuanzhong1 {
					color: #68BD2C;
					font-size: 1.2em;
				}

				.icon-shanchu2 {
					margin-left: 1em;
					color: #333;
					font-size: 1em;
				}
			}
		}


		::v-deep .fileBox {
			min-width: 0;
			flex-shrink: 0;
			width: 110px;
			aspect-ratio: 1/1;

			img {
				max-width: 100%;
				object-fit: contain !important;
				user-select: none;
			}
		}
	}
}

.iconListBox {
	max-height: 500px;
	min-height: 340px;
	height: auto;
	overflow: hidden auto;
	transition: none;
	scroll-behavior: smooth;

	.iconBox {
		padding: 10px 2px;
		display: grid;
		grid-template-columns: repeat(4, 1fr);
		gap: 10px;

		.iconItem {
			cursor: pointer;
			border: 3px solid transparent;

			img {
				aspect-ratio: 1 / 1;
				object-fit: contain !important;
				width: 100%;
			}
			&.active {
				border-color: $color-primary;
			}

			&:hover {
				border-color: $color-primary;
				box-shadow: 2px 1px 4px 2px #efeded, -1px -1px 4px 2px #efeded;
			}
		}
	}

	@include respond-to(mb) {
		max-height: 30vh;
		height: 30vh;
		min-height: 100px;

		.iconBox {
			grid-template-columns: repeat(3, 1fr);
		}
	}
}

.continue {
	cursor: pointer;
	width: fit-content;
	max-width: 65%;
	align-self: center;
	text-align: center;
	flex-shrink: 0;
}

.upload-div {
	border: 1px solid #ccc;
	width: 100%;
	height: 140px;
	display: flex;
	justify-content: space-between;
	align-items: center;

	&.noUpload {
		.text {
			width: 100%;
			border: 0;

			.uploadTip {
				display: none;
			}

			.uploadSize {
				display: flex;
				align-items: center;
				justify-content: center;

				::v-deep .uploadTip2 {
					b {
						font-size: 12px !important;
					}
				}
			}
		}

		.img {
			width: 0;
			padding: 0;
		}
	}

	.icon-shangchuan {
		font-size: 20px;
		color: $color-primary;
	}

	.icon-wenhao3 {
		color: $color-primary;
	}

	.text {
		display: flex;
		flex-direction: column;
		justify-content: center;
		width: 40%;
		height: 100%;
		border-left: 1px solid #DBDBDB;
		background: #FAFAFA;
		padding: 20px 30px;
		text-align: center;
		position: relative;
		flex-shrink: 0;
		cursor: pointer;

		div:nth-of-type(1) {
			font-size: 16px;
		}

		div:nth-of-type(2) {
			font-size: 14px;
		}

		::v-deep .uploadTip {
			.v-icon.v-icon {
				color: $color-primary;
			}
		}

		input[type="file"] {
			position: absolute;
			left: 0;
			top: 0;
			width: 0;
			// right: 0;
			// bottom: 0;
			opacity: 0;
			clip: rect(0, 0, 0, 0);
		}
	}

	.img {
		width: 60%;
		height: 100%;
		padding: 10px;

		.uploadList {
			height: 100%;
		}
	}

	@include respond-to(mb) {
		height: 120px;

		&.noUpload {
			.text {
				.uploadTip {
					display: flex;
				}

				.uploadTip3 {
					display: none;
				}

				.uploadSize {
					display: flex;
				}
			}
		}

		.imgBox {
			::v-deep .fileBox {
				width: 80px;

				img {
					aspect-ratio: 1/1;
					height: auto;
				}
			}
		}

		.text {
			height: 100%;
			padding: 0;
			display: flex;
			align-items: center;
			justify-content: center;
			flex-direction: column;

			strong {
				font-size: 14px;
			}

			.uploadTip {
				display: none;
			}

			::v-deep .uploadTip3 {
				.v-icon.v-icon {
					color: $color-primary;
				}
			}

			.uploadSize {
				display: none;
			}
		}
	}
}

.tag-div {
	display: flex;
	flex-wrap: wrap;
	column-gap: 36px;
	row-gap: 6px;
	margin: 10px 0;

	.div {
		cursor: pointer;
		color: #333;
		font-size: 14px;
		width: fit-content;
		flex-shrink: 0;
		font-weight: bold;
		opacity: 0.6;

		&:hover {
			color: #333;
			opacity: 1;
		}
	}

	.tt {
		cursor: pointer;
		font-size: 14px;
		color: #333;
		font-weight: bold;
	}

	@include respond-to(mb) {
		column-gap: 2em;
	}
}

.items-cont {
	display: flex;
	flex-direction: column;

	.mySwiperDrawer {
		width: 100%;
		margin-top: 5px;

		.swiper-wrapper {
			width: 100%;
			box-sizing: border-box;

			.swiper-slide {
				margin-left: 10px;
				width: fit-content;
				border: 1px solid #d0d0d0;
				border-radius: 16px;
				padding: 8px 15px;
				cursor: pointer;
				white-space: nowrap;
				text-align: center;

				// margin-left: 10px;
				@include respond-to(mb) {
					width: 100%;
					margin-left: 0;
					font-size: 12px;
					padding-block: 6px;
				}

				&.active {
					// color: white;
					// background: linear-gradient(90deg, #20aeff, #b61ee8);
					// background-color: $color-primary;
				}
			}
		}

		.swiper-pagination-drawer {
			display: none;
		}
	}

	@include respond-to(mb) {
		width: 100%;
		flex-direction: column;
		gap: 20px;

		.mySwiperDrawer {
			width: 100%;
			min-height: 60px;

			&.noContentSwiper {
				min-height: 0;
			}

			margin-top: 5px;

			.swiper-wrapper {
				width: 100%;

				.swiper-slide {
					width: 100%;
					border: 1px solid #d0d0d0;
					border-radius: 4px;
					// padding: 6px 10px;
					cursor: pointer;
					white-space: nowrap;
					font-size: 12px;

					&.active {
						// color: white;
						// background: $color-primary;
						font-weight: bold;
						border: 1px solid $color-primary;
					}
				}
			}

			.swiper-pagination-drawer {
				display: block;
				position: absolute;
				z-index: 999;
				top: 40px;
				left: 50%;
				transform: translateX(-50%);

				::v-deep .swiper-pagination-bullet {
					height: 21px;
					width: 21px;
					opacity: 1;
					background-color: #b7b7b7;
					margin: 0 6px;

					&.swiper-pagination-bullet-active {
						// background-image: linear-gradient(90deg, #20aeff, #b61ee8);
						background-color: $color-primary;
					}

					@include respond-to(mb) {
						width: 10px;
						height: 10px;
					}
				}
			}
		}
	}
}
</style>
