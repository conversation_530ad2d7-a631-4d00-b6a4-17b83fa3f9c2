<template>
	<div class="modal-box" :class="modal.class" :style="modal.style"
		@click.self="setModalType({}, {}, 'reviews_product')">
		<div v-for="(o, oi) in modal.outer" :key="oi">
			<div flex class="review-box">
				<pic class="review-bg" :src="o.img.value" :style="{ ...modal.imgStyle, ...o.img.style }" v-if="o.img"
					:alt="o.img.alt" @click="setModalType(o.img, modal.outer, 'img')" />

				<div class="score-box">
					<strong>{{ reviewsInfo.averageRating }}</strong>
					<EditDiv v-model:content="o.subTitle.value" />
				</div>

				<div class="progress-box">
					<template v-for="(s, si) in ['fiveStar', 'fourStar', 'threeStar', 'twoStar', 'oneStar']">
						<label>{{ 5 - si }} <span v-html="o.text.value"></span></label>
						<div
							:style="'background:linear-gradient(90deg,' + (o.starColor || 'var(--color-primary)') + ' ' + (reviewsInfo[s] / reviewsInfo.totalReviews * 100) + '%,' + (o.starBgColor || '#ddd') + ' 0)'">
						</div>
						<span>({{ reviewsInfo[s] }})</span>
					</template>
				</div>

				<div class="star-box" :style="modal.reviewsStarStyle">
					<span hidden>{{ reviewsInfo.averageRating }}
						<EditDiv tagName="span" v-model:content="o.subTitle.value" />
					</span>
					<EditDiv tagName="strong" v-model:content="o.title.value" />
					<div><b v-for="s in 5" class="icon-star"></b></div>
					<strong hidden>{{ reviewsInfo.totalReviewsStr }}+ {{ lang.reviews }}</strong>
				</div>
			</div>


			<div class="review-list" v-if="modal.isFilter">
				<div class="review" v-for="i in reviewList" :key="i.reviewBody">
					<div flex class="author">
						<div>{{ i.authorName }}</div>
						<pic src="https://www.shopperapproved.com/account/images/verified_customer.svg" />
					</div>

					<div class="star-box">
						<b v-for="s in 5" class="icon-star"></b>
						<span>{{ i.reviewDate }}</span>
					</div>

					<div flex class="img-box" scrollbar v-if="i.images && JSON.parse(i.images)">
						<pic pointer v-for="p in JSON.parse(i.images)" :src="p.mediaURL" v-if="p.mediaURL"
							@click="$store.commit('setMask', { value: p.mediaURL, img: true })" :key="p.mediaURL"></pic>
					</div>

					<EditDiv v-if="i.reviewBody" v-model:content="i.reviewBody" />
				</div>

				<v-pagination v-if="Math.floor(total / 10) > 1" v-model="page" :length="Math.floor(total / 10)"
					:total-visible="10"></v-pagination>
			</div>


			<div id="SA_review_wrapper" v-else></div>



			<button v-if="o.button?.value" :primary="o.button.value" :style="modal.btnStyle" :title="o.button.alt"
				@click="setModalType(o.button, modal.outer, 'button', o.button)">
				<EditDiv v-show="o.button.value" tagName="label" v-model:content="o.button.value" />
				<b :class="o.button.icon" v-show="o.button.icon"></b>
			</button>
		</div>
	</div>
</template>

<script>
import { getProCommentAggregates, getSiteComments } from "@/api/web.js";
export default {
	name: "modalReview",
	props: {
		data: {
			type: Object,
			default: {},
		}
	},
	data() {
		return {
			modal: {
				style: {},
				type: {},
				...this.data
			},
			reviewsInfo: {},
			reviewList: [],
			page: 1,
			total: 0
		};
	},
	computed: {
		lang() {
			return this.$store.getters.lang.layout || {};
		}
	},
	watch: {
		modal: {
			handler(val) {
				if (process.env.isManage) this.$emit("update:data", val);
			},
			immediate: true,
			deep: true
		},
		page() {
			this.getFilterData();
		}
	},
	created() {
		if (process.browser) {
			getProCommentAggregates().then(({ data }) => {
				if (!data) return;
				data.totalReviewsStr = data.totalReviews > 1000 ? data.totalReviews.toString()[0] + '0'.repeat(data.totalReviews.toString().length - 1) : data.totalReviews;
				this.reviewsInfo = data;

				if (this.modal.isFilter) this.getFilterData();
				else {
					try {
						let js = document.createElement('script');
						js.type = 'text/javascript';
						js.src = `//www.shopperapproved.com/widgets/${data.siteId}/product/${this.modal.product}/product-widget/${data.codeSign}.js?v=1736295616`;

						if (typeof (shopper_first) == 'undefined') document.getElementsByTagName('head')[0].appendChild(js);

						new MutationObserver(() => document.querySelectorAll('.SA__customer_reviews').forEach(i => { // 创建一个观察器，观察到变动时会执行回调函数
							if (i.querySelector('.SA__review_date').textContent.includes('china')) i.style.display = 'none';
						})).observe(document.getElementById('SA_review_wrapper'), { childList: true, subtree: true }); // 配置观察的目标节点、观察器的配置（需要观察什么变动）
					} catch (error) {
						console.log('shopperapproved: ', error)
					}
				}
			})
		}
	},
	methods: {
		setModalType(target, targetArray, clickType, event, other) {
			this.$setModal(this, target, targetArray, clickType, event, other)
		},
		getFilterData() {
			getSiteComments({
				countryCode: this.modal.countryCode,
				cateId: this.modal.product,
				page: this.page,
				pageCount: 10,
			}).then(({ data }) => {
				this.total = data.total;
				this.reviewList = data.reviewList;

				// document.querySelector('script#reviewStruct')?.remove();

				// let script = document.createElement('script');
				// script.id = 'reviewStruct';
				// script.type = 'application/ld+json';
				// script.text = data.commentStruct;
				// document.querySelector('head').appendChild(script);
			})
		}
	},
};
</script>

<style lang="scss" scoped>
.modal-box {
	font-size: 1rem;
}

.review-box {
	height: 11em;
	// margin-top: -1em;
	padding-right: 9vw;
	align-items: center;
	justify-content: flex-end;
	position: relative;
	z-index: 1;
}

.review-bg {
	height: 17em;
	position: absolute;
	z-index: -1;
	top: -1em;
	left: 0;
}

.score-box {
	color: white;
	text-align: center;
	background: #019f17;
	width: calc(2em + 3.5vmax);
	height: calc(2em + 3.5vmax);
	position: relative;

	strong {
		font-size: 2em;
		line-height: 4vmax
	}

	>div {
		width: 100%;
		padding: 5px;
		background: black;
		font-size: calc(1em - 2px);
		position: absolute;
		bottom: 0;
	}
}

.progress-box {
	display: grid;
	color: black;
	margin-left: 2vw;
	grid-gap: 0 0.5em;
	margin-right: 1vmax;
	align-items: center;
	font-size: calc(1em - 4px);
	grid-template-columns: auto 1fr auto;

	>div {
		width: 7vw;
		height: 0.8em;
	}

	>span {
		color: #ddd;
	}
}

.star-box {
	text-align: center;

	>strong {
		font-size: 1.38em;
	}

	>div {
		margin-top: 0.3em;
	}

	b {
		color: #f78f1e;
		font-size: 1.2em;
		margin-right: 0.1em;
	}
}

.img-box {
	grid-gap: 0.5em;
	padding-bottom: 1em;

	img {
		width: auto;
		max-width: 8em;
		max-height: 5em;
		object-fit: contain;
	}
}


.modal-box>div>button {
	display: flex;
	margin: 1em auto 0;
}


#SA_review_wrapper {
	padding: 13px 3vw 0;
	background-color: white;

	::v-deep #sa-body {

		#sa-previews-header,
		#sa_dropdown_filter2,
		.SA__verified_customer,
		.SA__sub_title,
		.SA__progress_wrap,
		.SA__social-share-wrapper,
		.SA__more_review {
			display: none;
		}

		.SA__customer_name {
			background: url(https://www.shopperapproved.com/account/images/verified_customer.svg) right no-repeat;
			background-size: auto 90%;
			font-weight: normal;

			&::before {
				display: none;
			}
		}

		.SA__rating {
			transform: scale(0.7) translateX(-20%);
		}

		.SA__review_date {
			float: revert;
		}

		.SA__customer_images {
			padding: 0 0 0.7em;

			img {
				width: auto;
			}
		}
	}
}


.review-list {
	padding: 7em 7vw 2em;

	.review {
		margin-bottom: 1.5em;
		padding-bottom: 1.5em;
		border-bottom: 1px solid #D2D2D9;
	}

	.author {
		justify-content: space-between;

		img {
			width: auto;
			height: 1.2em;
		}
	}

	.star-box {
		margin: 0.5em 0 1em;
		text-align: left;

		span {
			padding-left: 2em;
		}
	}

	::v-deep .v-pagination li>button {
		box-shadow: none;
		border-radius: 50%;

		&.v-pagination__navigation {
			width: auto;
			background: none;
		}

		&.v-pagination__item--active {
			background: #333;
		}
	}
}


.keychains {
	>div {
		border-top: 0;
		padding-bottom: 2em;
	}

	.review-box {
		height: 12.63em;
		padding-right: 7vw;

		.score-box {
			overflow: hidden;
			background: #F7E4CE;
			border: 1px solid #F96A00;
			border-radius: .5em;

			strong {
				color: #333;
			}

			>div {
				background: #F96A00;
			}
		}

		.progress-box {
			margin-left: 2.8vw;
			color: #fff;
			font-size: 1em;
			row-gap: .4em;

			>div {
				width: 9vw;
				border-radius: .2em;
			}
		}

		.star-box {
			display: flex;
			align-items: center;
			flex-direction: column;
			row-gap: .5em;

			strong {
				font-size: 1.2em;
				max-width: 5em;
				color: #fff;
			}

			b {
				color: #F96A00
			}
		}
	}

	.review-list {
		background: white;
		margin-top: -2.5em;
		padding: 5em 5vw 2em;
		border-radius: 0 0 1em 1em;
		border: 1px solid #D3D3D3;

		.star-box b {
			color: #F96A00
		}
	}
}



@media screen and (max-width: $pad-width) and (min-width: $mb-width) {
	.review-bg {
		min-width: 80vw;
	}

	.review-list ::v-deep .v-pagination {
		justify-content: flex-start;
	}
}



@media screen and (max-width: $mb-width) {
	.review-box {
		height: 6em;
		width: 100%;
		padding: 0 1vw;
		overflow: hidden;
		margin-top: -1em;

		>div:not(.star-box) {
			display: none;
		}

		.star-box {
			>strong:not(:last-child) {
				display: none;
			}

			>span {
				display: block;
			}

			>strong {
				display: block;
				font-size: calc(1em + 2px);
				text-transform: capitalize;
			}
		}
	}

	.review-bg {
		width: 152%;
		height: 12em;
		top: -2em;
	}


	#SA_review_wrapper {
		padding: 1px 1vw 1em;

		::v-deep .SA__review_container {
			padding: 0;

			.SA__customer_name {
				font-size: calc(1em + 2px);
			}

			.SA__customer_content p {
				font-size: calc(1em + 2px);
			}
		}
	}


	.review-list {
		padding: 1.5em 0 0;
		border-top: 1px solid #D2D2D9;
	}

	.keychains {
		>div {
			border-radius: 2em 5em .5em .5em;
		}

		.review-box {
			height: 6.67em;

			.star-box {
				row-gap: .2em;
				align-items: start;

				>div {
					margin-top: 0;
				}

				span {
					color: #fff;
				}

				strong {
					font-size: 1em;
					font-weight: 400;
					max-width: fit-content;
				}
			}
		}

		.review-list {
			border-top: 0;
			padding: 4em 3vw 2em;
		}
	}
}
</style>
