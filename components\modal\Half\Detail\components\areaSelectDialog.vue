<template>
  <div class="selectBox">
    <div class="headerTitle">{{langSemiCustom.applyImage}}</div>
    <div class="mbHeaderTitle">{{ langSemiCustom.chooseLogo }}</div>
    <div class="noteText">
     {{langSemiCustom.noteImg}}
    </div>
    <div class="areaImgs">
      <div
        class="areaImg"
        :class="{ active: isActive(index) }"
        v-for="(item, index) in areaImg"
        :key="index"
      >
        <div class="imgWrap" @click="selectAreaFn(index)">
          <img :src="item.imgUrl" :alt="item.name" />
          <half-design-check-icon
            class="absolute-top-right check-icon"
          ></half-design-check-icon>
        </div>
        <div class="areaName">
          {{ item.name }}
        </div>
      </div>
    </div>
    <div class="confirmBtn" @click="commitFn">
      {{ langQuote.ns }}
    </div>
  </div>
</template>

<script>
export default {
  inject: ["getAreaIndex"],
  name: "",
  components: {},
  props: {},
  data() {
    return {
      areaImg: [
        {
          name: "Front",
          imgUrl:
            "https://static-oss.gs-souvenir.com/web/quoteManage/20250305/front_20250305QAPzZm.png",
        },
        {
          name: "Back",
          imgUrl:
            "https://static-oss.gs-souvenir.com/web/quoteManage/20250305/back_20250305Yyhy87.png",
        },
      ],
      tempIndex: [],
    };
  },
  computed: {
    langQuote() {
      return this.$store.getters.lang?.quote;
    },
    langSemiCustom() {
      return this.$store.getters.lang?.semiCustom;
    },
    isMobile() {
      return this.$store.state.device === "mb";
    },
    areaIndex() {
      return this.getAreaIndex();
    },
  },
  methods: {
    isActive(index) {
      if (this.tempIndex.length > 0) {
        if (this.tempIndex.includes(index)) return true;
        return false;
      }
    },
    selectAreaFn(index) {
      if (this.tempIndex.includes(index)) {
		if(this.tempIndex.length == 1) return;
        this.tempIndex = this.tempIndex.filter((item) => item != index);
      } else {
        this.tempIndex.push(index);
      }
    },
    commitFn() {
      let needChange = false;
      let index = [];
      if (this.tempIndex.length > 0) {
        needChange = true;
        this.tempIndex.forEach((item) => {
          if (this.areaIndex == item) {
            needChange = false;
          }
        });
        index = this.tempIndex;
      } else {
        index = [this.areaIndex];
      }
      this.$emit("selectArea", needChange, index);
    },
  },
  created() {
    this.tempIndex = [].concat(this.areaIndex);
  },
  mounted() {},

  watch: {},
};
</script>
<style scoped lang="scss">
.selectBox {
  padding: 20px 40px;
  .headerTitle {
    font-weight: bold;
    font-size: 18px;
    color: #333333;
    margin-bottom: 0.8em;
  }
  .mbHeaderTitle {
    display: none;
  }
  .noteText {
    padding-top: 10px;
    border-top: 1px solid #eeeeee;
    font-size: 14px;
    color: #999999;
    margin-bottom: 1.5em;
  }
  .areaImgs {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    .areaImg {
      cursor: pointer;
      .check-icon {
        display: none;
      }
      .imgWrap {
        position: relative;
        border: 1px solid #d3d5d7;
        border-radius: 4px;
      }
      &.active {
        .check-icon {
          display: flex;
        }
        .imgWrap {
          border: 1px solid $color-primary;
        }
        .areaName {
          font-weight: bold;
        }
      }
    }
    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
    .areaName {
      font-size: 14px;
      color: #333333;
      text-align: center;
      margin-top: 10px;
    }
  }
  .confirmBtn {
    width: fit-content;
    margin: 20px auto 0;
    font-size: 16px;
    padding: 0.6em 2em;
    border-radius: 6px 6px 6px 6px;
    background-color: $color-primary;
    color: #fff;
    cursor: pointer;
  }

  @include respond-to(mb) {
    padding: 14px 10px;
    .headerTitle {
      display: none;
    }
    .noteText {
      display: none;
    }
    .mbHeaderTitle {
      display: block;
      font-weight: bold;
      font-size: 16px;
      color: #333333;
      margin-bottom: 0.8em;
    }
    .areaImgs {
      padding-top: 16px;
      border-top: 1px solid #eeeeee;
    }
    .confirmBtn {
      font-size: 14px;
    }
  }
}
</style>
