<template>
	<div class="step-upload mb-4" :id="`copy${stepData.id}`">
		<div class="box-border">
			<i class="el-icon-close" @click="closeMask"></i>
		</div>
		<slot name="stepText"></slot>
		<div class="step-box">
			<div class="uploadArea" style="cursor: pointer">
				<div class="upload-box" :class="{ dropActive: dropActive, greyBox: isUpload }">
					<loader :loadState="!loadImgState"></loader>
					<div class="uploadList">
						<div v-show="!needShowSelectedImg" class="swiper myswiperMedals1" ref="myswiperMedals1">
							<div class="swiper-wrapper">
								<div class="swiper-slide pointer" @click="onSlideClick(item)" v-for="(item, index) in showMedalsIconList" :key="index">
									<v-tooltip bottom max-width="300" color="#FFFFFF" content-class="absoluteImgBox">
										<template v-slot:activator="{ on, attrs }">
											<img v-bind="attrs" v-on="on" style="aspect-ratio: 1/1; object-fit: cover; width: 100%" :data-id="item.id" :src="item.iconUrl" />
										</template>
										<div class="absoluteImg" v-show="item.sideImgUrl">
											<img :src="item.sideImgUrl" style="aspect-ratio: 1/1; object-fit: cover; width: 100%" alt="" />
										</div>
									</v-tooltip>
								</div>
							</div>
						</div>
						<div class="selectedImgBox" v-show="needShowSelectedImg">
							<pic :src="clickImgId[0].iconUrl" :alt="'selectedImg'"> </pic>
							<span class="myIconBox" @click.stop="delUploadImg">
								<b class="icon-shanchu2 myIcon"></b>
							</span>
						</div>
						<div class="swiper-button-prev medalsUploadBtn" @click.stop v-show="!needShowSelectedImg"></div>
						<div class="swiper-button-next medalsUploadBtn" @click.stop v-show="!needShowSelectedImg"></div>
					</div>
					<div class="upload-btn" style="text-align: center" @click="openUploadBox">
						<span class="uploadText" :class="{ isDisabled: isUpload }">{{ uploadList.length > 0 ? langSemi.changeLogo : langSemi.chooseLogo }}</span>
						<button :disabled="isUpload" :class="{ isDisabled: isUpload }" @click="upload">
							<span>{{ lang.Upload }} </span>
						</button>
					</div>
				</div>
			</div>
		</div>
		<BaseDialog v-model="showUpload" :model="false" :width="!isMobile ? '700px' : '90%'">
			<div class="close-icon" slot="closeIcon" @click="closeFn">
				<b class="icon-guanbi"></b>
			</div>
			<UploadDrawer :style="{ width: !isMobile ? '700px' : '100%' }" :showMedalsId.sync="showMedalsId" :bindValue="stepData" :medalsIconList="medalsIconList" :medalsIconTagList="medalsIconTagList" :clickImgId.sync="clickImgId" :uploadList.sync="uploadList" @addImg="addImg" @delImg="delImg" @nextStep="nextStep"> </UploadDrawer>
		</BaseDialog>
		<div class="errorTip">
			<v-alert dense outlined type="error">
				{{ langSemi.errorTip }}
			</v-alert>
		</div>
	</div>
</template>

<script>
import PriceText from "@/components/Quote/PriceText";
import QuoteBtn from "@/components/Quote/QuoteBtn";
import { getMedalTemplatesList } from "@/api/web";
import UploadDrawer from "@/components/HalfDesign/customMedals/upload/UploadDrawer";
import BaseDialog from "@/components/Quote/BaseDialog.vue";
import loader from "@/components/HalfDesign/customMedals/common/loader.vue";

export default {
	inject: ["getCustomProductId", "getCustomCategoryId", "canvas"],
	props: {
		stepData: {
			type: Object,
			default: {},
		},
	},
	data() {
		return {
			selectItem: null,
			showUpload: false,
			dropActive: false,
			myswiper1: null,
			uploadList: [],
			showMedalsId: -1,
			showMedalsIconList: [],
			medalsIconList: [],
			medalsIconTagList: [],
			clickImgId: [
				{
					id: -1,
					iconUrl: "",
				},
			],
			isCircle:false,
		};
	},
	watch: {
		uploadList: {
			handler(newVal) {
				if (newVal.length > 0) {
					this.selectItem.files = newVal.map((item) => item.secure_url);
				} else {
					this.selectItem.files = [];
				}
				this.$emit("selectStep", {
					type: this.stepData.attributeFlag,
					data: this.selectItem,
					parent: this.stepData,
					id: this.stepData.id,
					copyId: "copy" + this.stepData.id,
					firstSelect: false,
				});
			},
			deep: true,
		},
	},
	components: {
		PriceText,
		QuoteBtn,
		BaseDialog,
		UploadDrawer,
		loader,
	},
	methods: {
		upload() {
			this.$emit("drawerUpload", { data: this.stepData, type: "open" });
		},
		updateCheckBox(e) {
			e.preventDefault();
			this.$store.commit("SET_UploadSwitch", !this.isUpload);
			this.$emit("update:isUpload", !this.isUpload);
		},
		closeMask() {
			this.$emit("closeMask");
		},
		showMaskFn(name) {
			this.$emit("showMaskFn", name);
		},
		initSwiper() {
			this.myswiperMedals1 = new Swiper(this.$refs.myswiperMedals1, {
				slidesPerView: 4.5,
				spaceBetween: 10,
				navigation: {
					nextEl: ".medalsUploadBtn.swiper-button-next",
					prevEl: ".medalsUploadBtn.swiper-button-prev",
				},
				breakpoints: {
					0: {
						slidesPerView: 2.5, // 小屏幕设备
						spaceBetween: 10,
					},
					1000: {
						slidesPerView: 4.5,
						spaceBetween: 10,
					},
					1500: {
						slidesPerView: 4.5,
						spaceBetween: 10,
					},
				},
				watchSlidesVisibility: true, //防止不可点击
				on: {
					// click: this.onSlideClick,
				},
			});
		},
		onSlideClick(item) {
			this.clickImgId = [];
			this.clickImgId.push({ id: item.id, iconUrl: item.iconUrl });
			this.uploadList = [
				{
					secure_url: item.iconUrl,
					original_filename: "logo",
					size: 1024,
					id: item.id,
					active: false,
				},
			];
			this.showMedalsId = this.medalsIconList.length ? this.medalsIconList[0].id : -1;
			this.addImg();
		},
		addImg() {
			this.$nextTick(() => {
				if (this.uploadList.length == 0) return;
				//调用绘制图片方法
				this.$emit("newAddImg2", {
					imgItem: this.uploadList[0],
					isCircle: this.isCircle,
				});
			});
		},
		delImg() {
			if (this.uploadList.length > 0) {
				this.$emit("delImg", this.uploadList?.[0].id);
			}
		},
		delUploadImg() {
			this.delImg();
			this.clickImgId = [
				{
					id: -1,
					iconUrl: "",
				},
			];
			this.uploadList = [];
		},
		openUploadBox() {
			this.showUpload = true;
		},
		closeFn() {
			this.showUpload = false;
		},
		nextStep() {
			this.closeFn();
			// this.$emit("nextStep");
		},
		selectDefault() {
			if (this.stepData.productParamList.length) {
				this.selectItem = this.stepData.productParamList[0];
				this.$emit("selectStep", {
					type: this.stepData.attributeFlag,
					data: this.selectItem,
					parent: this.stepData,
					id: this.stepData.id,
					copyId: "copy" + this.stepData.id,
					firstSelect: true,
				});
				if (this.listUploadFile.length > 0) {
					this.onSlideClick(this.listUploadFile[0]);
					this.showUpload = false;
					this.showMedalsId = this.listUploadFile[0].id;
				}
			}
		},
		showUploadBox(data) {
			this.isCircle = false;
			if (data.circle && data.circle.radius && +data.circle.radius > 0) {
				this.isCircle = true;
			}
		},
	},
	computed: {
		lang() {
			return this.$store.getters.lang.quote || {};
		},
		langSemi() {
			return this.$store.getters.lang.semiCustom || {};
		},
		langDesign() {
			return this.$store.getters.lang?.design || {};
		},
		isMobile() {
			return this.$store.state.device === "mb";
		},
		productId() {
			return this.getCustomProductId();
		},
		categoryId() {
			return this.getCustomCategoryId();
		},
		isUpload() {
			return this.$store.state.halfDesign.medalsLaterUpload;
		},
		needShowSelectedImg() {
			return this.clickImgId[0].id !== -1;
		},
		listUploadFile() {
			return this.$store.state.halfDesign.uploadPic;
		},
		loadImgState() {
			return this.canvas.loadImgState;
		},
	},
	mounted() {
		this.$Bus.$on("selectDefaultArtworkStep", this.selectDefault);
		this.$Bus.$on("currentArea", this.showUploadBox);
		getMedalTemplatesList({ productId: this.productId, categoryId: this.categoryId }).then((res) => {
			this.medalsIconList = res.data.map((item) => {
				if (item.themeLogoList && item.themeLogoList?.length > 0 && Array.isArray(item.themeLogoList)) {
					item.copyThemeLogoImgList = item.themeLogoList.map((citem) => {
						return {
							...citem,
							iconUrl: citem.imgUrl,
						};
					});
				}
				return item;
			});
			this.showMedalsIconList = this.medalsIconList[0]?.copyThemeLogoImgList || [];
			this.medalsIconTagList = [{ id: -1, tagName: this.langDesign.all }].concat(res.data.map((item) => ({ id: item.id, tagName: item.name })));
		});
		this.$nextTick(() => {
			this.initSwiper();
		});
	},
	beforeDestroy() {
		this.$Bus.$off("currentArea", this.showUploadBox);
		this.$Bus.$off("selectDefaultArtworkStep", this.selectDefault);
	},
};
</script>

<style lang="scss">
.absoluteImgBox {
	z-index: 1000000 !important;
	background-color: transparent !important;
	opacity: 1 !important;

	@media screen and (max-width: 758px) {
		display: none !important;
	}

	.absoluteImg {
		width: 160px;
		height: 160px;
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 10px;
		background: #ffffff;
		box-shadow: 0px 2px 12px 0px rgba(0, 0, 0, 0.3);
		border-radius: 10px;
	}
}
</style>
<style scoped lang="scss">
@import "~assets/css/half.scss";

.step-upload {
	.confirmBtnWrap {
		display: none;
		margin-top: 33px;

		@include respond-to(mb) {
			margin-top: 20px;
		}
	}

	&.mask {
		.confirmBtnWrap {
			display: flex;
			justify-content: center;
			align-items: center;
		}
	}
}

.upload-box.dropActive {
	border-color: $color-primary !important;
}

.circle2 {
	display: flex;
	justify-content: center;
	align-items: center;
	border-color: $color-primary;
	background-color: $color-primary;
	flex-shrink: 0;
	width: 18px;
	height: 18px;
	border: 1px solid #afb1b3;
	border-radius: 50%;
	margin-right: 10px;
	background-color: #fff;

	&::after {
		background-color: #ffffff;
		content: "";
		width: 6px;
		height: 6px;
		background: #d4d7d9;
		border-radius: 50%;
	}

	@include respond-to(mb) {
		width: 15px;
		height: 15px;

		&::after {
			width: 5px;
			height: 5px;
		}
	}

	&.active {
		background-color: $color-primary !important;
		border-color: $color-primary !important;

		&::after {
			background-color: #ffffff !important;
		}
	}
}

.t1 {
	display: flex;
	align-items: center;
	font-weight: bold;
	font-size: 16px;
}

.t2 {
	color: #333333;
	font-size: 14px;
	margin-bottom: 10px;
	padding-left: 29px;

	@include respond-to(mb) {
		padding-left: 23px;
	}
}

.noteText {
	margin-top: 10px;
	font-size: 14px;
}

.click_text {
	color: #007aff;
	text-decoration: underline;
	cursor: pointer;
}

.step-upload {
	position: relative;
	background-color: #fff;
	// padding: 40px 0;
	border-radius: 10px;

	&.errorStep {
		.upload-box {
			--error-color: #ff0000;
			border-color: var(--error-color) !important;
		}
	}

	.step-box {
		.t1 {
			margin-bottom: 10px;
			font-size: 16px;
			color: #333333;
			font-weight: bold;

			.icon-shangchuan {
				color: #9e9e9e;
			}
		}

		.t2 {
			margin-bottom: 10px;
			font-size: 14px;
			color: #333333;
		}

		.upload-box {
			display: flex;
			justify-content: space-between;
			align-items: center;
			background: #fafafa;
			border: 1px solid #dbdbdb;
			border-radius: 4px;
			cursor: pointer;
			transition: all 0.3s;

			&.greyBox {
				pointer-events: none;
				user-select: none;

				.swiper-slide:hover {
					border-color: transparent;
					background-color: transparent;
				}
			}

			.uploadList {
				position: relative;
				width: 100%;
				overflow: auto;
				text-align: center;
				padding: 10px 22px;
				background-color: #ffffff;
				border-right: 1px solid #dbdbdb;

				/* 设置按钮大小 */
				.swiper-button-next,
				.swiper-button-prev {
					width: 20px;
					height: 40px;
					background: #c0c4cc;
					color: #333333;
					opacity: 1;
					transform: translateY(-50%);
					margin-top: 0;

					&::after {
						font-size: 14px;
					}

					&.swiper-button-disabled {
						opacity: 0.5;
					}
				}

				.swiper-button-prev {
					left: 0;
				}

				.swiper-button-next {
					right: 0;
				}

				.uploadIcon {
					width: 68px;
					height: 55px;
					margin-top: 15px;
					font-size: 52px;
					color: #ccc;
				}

				.selectedImgBox {
					position: relative;
					border: 2px solid $color-primary;
					aspect-ratio: 1 / 1;
					border-radius: 6px;
					display: flex;
					align-items: center;
					justify-content: center;
					width: 120px;

					img {
						width: 100%;
						aspect-ratio: 1 / 1;
						object-fit: contain;
					}

					.myIconBox {
						pointer-events: all;
						color: $color-primary;
						position: absolute;
						right: 0;
						top: 0;

						.icon-shanchu2 {
						}
					}
				}

				.uploadItem {
					display: flex;
					justify-content: space-between;
					align-items: center;
					padding: 5px;
					font-size: 14px;
				}

				.myIcon {
					margin: 0 4px;
				}
			}

			.upload-btn {
				height: 100%;
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				gap: 0.6em;
				font-size: 16px;
				padding: 10px 20px;

				.uploadText {
					color: $color-primary;
					text-decoration: underline;

					&.isDisabled {
						color: #cccccc !important;
					}
				}

				button {
					padding: 0.4em 2em;
					background: var(--color-primary);
					border-radius: 5px;
					border: none;
					color: #fff;
					font-size: 18px;
					display: flex;
					align-items: center;
					justify-content: center;
				}

				button.isDisabled {
					background: #cccccc !important;
				}
			}

			.tips {
				font-size: 15px;
				color: #b3b3b3;
				text-align: center;
			}
		}

		@media screen and (min-width: 768px) and (max-width: 1499px) {
			grid-template-columns: 2fr 1.4fr;
			column-gap: 20px;
		}

		@include respond-to(mb) {
			grid-template-columns: repeat(1, 1fr);
			height: auto;

			.t1 {
				margin-bottom: 5px;
				font-size: 12px;
			}

			.t2 {
				margin-bottom: 5px;
				font-size: 12px;
			}

			.upload-box {
				.uploadList {
					.selectedImgBox {
						width: 80px;
					}

					.uploadItem {
						font-size: 12px;
					}

					.uploadIcon {
						width: 51px;
						height: 41px;
						margin-top: 0;
						font-size: 48px !important;
					}
				}

				.upload-btn button {
					font-size: 14px;
					border-radius: 4px;
				}

				.upload-btn {
					padding: 10px;
					font-size: 12px;
				}

				.tips {
					font-size: 12px;
				}
			}
		}
	}

	.box-border {
		display: none;

		.el-icon-close {
			display: flex;
			justify-content: center;
			align-items: center;
			position: absolute;
			font-weight: 700;
			top: 0;
			right: 0;
			width: 40px;
			height: 40px;
			transform: translate(50%, -50%);
			cursor: pointer;
			background: #ffffff;
			border-radius: 50%;
			box-shadow: 1px 2px 20px 0 rgba(0, 0, 0, 0.2);
			z-index: 10;
		}
	}

	&.mask {
		position: relative;
		z-index: 101;

		.confirmBtnWrap {
			position: relative;
		}

		.box-border {
			position: absolute;
			left: 0;
			right: 0;
			top: 0;
			bottom: 0;
			display: block;
			background-color: #fff;
			border: 1px solid #d9dbdd;
		}

		.step-box {
			position: relative;
		}
	}

	@include respond-to(mb) {
		background-color: #fff;
		border-radius: 5px;
		padding: 0px;

		&.mask {
			.box-border {
				.el-icon-close {
					width: 30px;
					height: 30px;
					transform: translate(0, 0);
					box-shadow: none;
				}
			}
		}
	}
}

.myswiper1 {
	height: 100%;

	.swiper-slide {
		overflow: hidden;
		border-radius: 6px;
		cursor: pointer;
		border: 2px solid transparent;
		// background-color: #e9e9e9;
		align-items: center;
		display: flex;
		justify-content: center;
		box-sizing: border-box;
		user-select: none;

		// &.active {
		// 	border-color: $color-primary;
		// 	background-color: #fff;
		// }
		@media (any-hover: hover) {
			&:hover {
				border-color: $color-primary;
				background-color: #fff;
			}
		}
	}

	.swiper-slide-thumb-active {
		border-color: #0066cc;
	}

	img {
		width: 100%;
		height: 100%;
		object-fit: contain;
	}
}

.close-icon {
	.v-icon {
		cursor: pointer;
		position: absolute;
		top: 20px;
		right: 16px;

		&::before {
			font-size: 18px;
			font-weight: 700;
			color: #333;
		}
	}

	@include respond-to(mb) {
		.close-icon {
			.v-icon {
				top: 10px;
				right: 10px;
			}
		}
	}
}
</style>
