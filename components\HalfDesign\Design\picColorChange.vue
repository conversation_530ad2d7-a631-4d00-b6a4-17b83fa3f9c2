<template>
	<div class="editColor-area" v-show="uploadImg.url">
		<div class="oneColor item">
			<div class="left-label">{{ langSemiCustom.removeBackground }}</div>
			<div class="right-con">
				<v-switch dense hide-details :input-value="removeBgColor" @change="removeBg"></v-switch>
			</div>
		</div>
		<template v-if="oneColor">
			<div class="editColor editOneColor item" id="editOneColor">
				<div class="left-label">{{ langSemiCustom.editColor }}</div>
				<v-menu content-class="changeColorMenu" attach="#editOneColor" :close-on-content-click="false" offset-y>
					<template v-slot:activator="{ on, attrs }">
						<div class="right-con" v-bind="attrs" v-on="on">
							{{ filterColor.pantone }}
							<div class="color-item" :style="{ backgroundColor: filterColor.code }"></div>
							<v-icon size="30">mdi-chevron-right</v-icon>
						</div>
					</template>
					<v-card class="changeColor-picker-wrap">
						<div class="color-picker-title">{{ langSemiCustom.chooseColor2 }}</div>
						<div class="color-picker-list">
							<div class="color-item" :class="{ active: filterColor.code === item.code }" v-for="item in colorList" :key="item.id" :style="{ backgroundColor: item.code }" :title="item.pantone" @click="filterPicColorBefore(item)">
								<v-icon color="#ffffff" small>mdi-check</v-icon>
							</div>
						</div>
					</v-card>
				</v-menu>
			</div>
		</template>
		<template v-else>
			<div class="editColor item" id="editPicColor" v-if="picColorList.length <= 9">
				<div class="left-label">{{ langSemiCustom.editColors }}</div>
				<v-menu content-class="changeColorMenu" attach="#editPicColor" :close-on-content-click="false" offset-y>
					<template v-slot:activator="{ on, attrs }">
						<div class="right-con" v-bind="attrs" v-on="on">
							<div class="color-item" v-show="index < 2" v-for="(item, index) in picColorList" :key="index" :style="{ backgroundColor: item.color }" @click="selectPicColor(item)"></div>
							<div class="moreColor" v-show="picColorList.length >= 2">
								<span>...</span>
							</div>
							<v-icon size="30">mdi-chevron-right</v-icon>
						</div>
					</template>
					<v-card class="changeColor-picker-wrap">
						<div class="color-picker-title">{{ langSemiCustom.chooseChange }}</div>
						<div class="color-picker-list">
							<div class="color-item" :class="{ active: item.color === oldColor }" v-for="(item, index) in picColorList" :key="index" :style="{ backgroundColor: item.color }" @click="selectPicColor(item)">
								<v-icon color="#ffffff" small>mdi-check</v-icon>
							</div>
						</div>
						<div class="color-picker-title" v-show="oldColor">{{ langSemiCustom.chooseColor }}</div>
						<div class="color-picker-list" v-show="oldColor">
							<div class="color-item" :class="{ active: newColor === item.code }" v-for="item in colorList" :key="item.id" :style="{ backgroundColor: item.code }" :title="item.pantone" @click="changePicColor(item)">
								<v-icon color="#ffffff" small>mdi-check</v-icon>
							</div>
						</div>
					</v-card>
				</v-menu>
			</div>
			<div class="moreColor item" v-else>
				<div class="left-label">{{ langSemiCustom.color }}</div>
				<div class="right-con">{{ langSemiCustom.fullColor }}</div>
			</div>
		</template>
		<!--		<div class="oneColor item">-->
		<!--			<div class="left-label">-->
		<!--				Remove Background Color-->
		<!--			</div>-->
		<!--			<div class="right-con">-->
		<!--				<v-switch-->
		<!--					dense-->
		<!--					hide-details-->
		<!--					:value="removeBgColor"-->
		<!--					@change="removeBg"-->
		<!--				></v-switch>-->
		<!--			</div>-->
		<!--		</div>-->
	</div>
</template>
<script>
export default {
	props: ["oneColor", "uploadImg", "picColorList", "oldColor", "newColor", "colorList", "copyPicColorList", "filterColor", "removeBgColor"],
	methods: {
		changeOneColor(val) {
			// this.$emit("update:oneColor", val);
			// this.$emit("changeOneColor", val);
		},
		selectPicColor(item) {
			console.log(11111);
			this.$emit("selectPicColor", item);
		},
		changePicColor(item) {
			console.log(22222);
			this.$emit("changePicColor", item);
		},
		filterPicColorBefore(item) {
			this.$emit("filterPicColorBefore", item);
		},
		removeBg(val) {
			this.$emit("update:removeBgColor", val);
			this.$emit("removeBg", val);
		},
	},
	computed: {
		langSemiCustom() {
			return this.$store.getters.lang?.semiCustom;
		},
	},
};
</script>
<style scoped lang="scss">
@import "~assets/css/half.scss";

#editPicColor,
#editOneColor {
	position: relative;
}

.changeColorMenu {
	left: 0 !important;
	right: 0 !important;
	max-width: 100% !important;
	width: 100%;

	.changeColor-picker-wrap {
		overflow: auto;
		max-height: 400px;
		background-color: #ffffff;

		.color-picker-title {
			text-align: left;
		}

		.color-picker-list {
			display: grid;
			grid-template-columns: repeat(auto-fill, 30px);
			grid-gap: 20px;
			padding: 10px;
		}
	}
}

.editColor-area {
	.item {
		display: flex;
		align-items: center;
		height: 56px;
		padding: 10px 0;
		border-bottom: 1px solid $border-color;

		.left-label {
			flex: 1;
		}

		.right-con {
			flex: 0 1 auto;
			display: flex;
			align-items: center;
		}
	}

	.oneColor {
		.v-input {
			margin-top: 0;
		}
	}

	.editColor {
		.right-con {
			.color-item {
				display: inline-block;
				width: 26px;
				height: 26px;
				margin-left: 5px;
				border: 1px solid rgba(0, 0, 0, 0.1);
				border-radius: 50%;
				cursor: pointer;

				&:hover {
					border: 1px solid rgba(0, 0, 0, 0.4);
				}
			}

			.moreColor {
				position: relative;
				display: inline-block;
				width: 26px;
				height: 26px;
				margin-left: 5px;
				cursor: pointer;

				span {
					position: absolute;
					left: 50%;
					transform: translateX(-50%);
					bottom: 0;
				}
			}
		}
	}
}
</style>
