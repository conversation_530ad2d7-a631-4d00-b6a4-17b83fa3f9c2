import {request} from '~/utils/request'

//查询账单列表-分页
export function list(data) {
	return request({
		url: '/retailer/bill/manage/list',
		method: 'post',
		data: data,
	})
}

//查询账单列表-分页
export function getById(id) {
	return request({
		url: '/bill/manage/getById?id='+id,
		method: 'get',
	})
}

//查询账单列表-分页
export function exportBillList(data) {
	return request({
		url: '/retailer/bill/manage/export-bill-list',
		method: 'post',
		data: data,
	})
}

//根据经销商ID获取支付信用卡列表
export function cardList(id) {
	return request({
		url: '/pay/retailer/card-list?retailerId='+id,
		method: 'get',
	})
}

//手动执行账单支付
export function manualPayBill(billId,configId,retailerId) {
	return request({
		url: '/retailer/bill/manage/'+retailerId+'/manual-pay-Bill?billId='+billId+'&configId='+configId,
		method: 'get',
	})
}

//操作日志列表
export function operationLogList(data) {
	return request({
		url: '/retailer/operationLog/list',
		method: 'post',
		data: data,
	})
}
