<template>
	<div class="colors">
		<div class="colorGrid">
			<div class="colorBox" v-for="item in itemData.childList" :key="item.id">
				<div class="colorSwatch" :style="getSwatchStyle(item)"></div>
				<div class="colorName">{{ item.alias2||item.alias }}</div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	name: "Colors",
	components: {},
	props: {
		itemData: {
			type: Object,
			default: () => {},
		},
	},
	data() {
		return {
			colorMap: {
				"Kelly Green": "#3a8c3f",
				Burgundy: "#7b2c3d",
				Black: "#1a1a1a",
				"Light Blue": "#7ab5d6",
				"Navy Blue": "#1c3b5a",
				"Reflex Blue": "#1a3f8e",
				"White C": "#ffffff",
				Orange: "#e26b34",
				Yellow: "#f0d43a",
				"Athletic Gold": "#d9a53b",
				Purple: "#5d3b8b",
				Grey: "#a6a6a6",
				Gray: "#a6a6a6",
				"Forest Green": "#1e553a",
				Red: "#c92c3d",
			},
		};
	},
	methods: {
		getSwatchStyle(item) {
			// 如果有图片，优先使用图片作为背景
			if (item.imageJson) {
				const imgUrl = this.getImg(item);
				if (imgUrl) {
					return {
						backgroundImage: `url(${imgUrl})`,
						backgroundSize: "cover",
						backgroundPosition: "center",
					};
				}
			}
			// 否则使用颜色映射
			const backgroundColor = this.colorMap[item.alias] || "#cccccc";
			return { backgroundColor };
		},
		getImg(item) {
			if (item.imageJson) {
				try {
					let imgUrl = JSON.parse(item.imageJson);
					return imgUrl[0].url;
				} catch (e) {
					console.error("Error parsing imageJson:", e);
					return "";
				}
			} else {
				return "";
			}
		},
	},
	created() {},
	mounted() {},
	computed: {},
	watch: {},
};
</script>
<style scoped lang="scss">
.colors {
	width: 100%;
	.colorGrid {
		width: 100%;
		display: grid;
		grid-template-columns: repeat(5, 1fr);
		gap: 15px;

		.colorBox {
			display: flex;
			flex-direction: row;
			align-items: center;
			gap: 16px;
			padding: 14px;
			background: #ffffff;
			border-radius: 4px;
			border: 1px solid #d9dbdd;

			.colorSwatch {
				width: 40px;
				height: 40px;
				border: 1px solid #d9dbdd;
				flex-shrink: 0;
				background-size: cover;
				background-position: center;
				background-repeat: no-repeat;
			}

			.colorName {
				font-size: 16px;
				color: #333333;
				flex-grow: 1;
			}
		}
		@include respond-to(mb) {
			grid-template-columns: repeat(3, 1fr);
			gap: 10px;
			.colorBox {
				border-radius: 8px;
				padding: 6px;
				gap: 6px;
				.colorSwatch {
					width: 26px;
					height: 26px;
				}
				.colorName {
					font-size: 12px;
					font-weight: 700;
				}
			}
		}
	}
}
</style>
