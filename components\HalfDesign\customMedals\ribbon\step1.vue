<template>
	<div class="style1 ribbonStep1" :class="stepData.styleName">
	    <slot name="stepText"></slot>
		<div class="step-content"
			v-if="!stepData.isHideStep || (stepData.isHideStep && stepData.productParamList.length > 1)">
			<div class="step-item" :class="{ active: index === selectIndex }"
				v-for="(step, index) in stepData.productParamList" :key="index" @click="selectStep(step, index)">
				<div class="imgWrap" v-show="getImg(step)">
					<img :src="getImg(step)" :alt="attributeFlag=='color'?step.imgAltTitle:step.valueName" :title="attributeFlag=='color'?step.imgAltTitle:step.valueName" />
					<labelText style="position: absolute;top: 0;left: 0;border-radius: 6px 0px 6px 0px;"
						:labelText="step.labelName" :style="{ backgroundColor: step.backgroundColor || '' }">
					</labelText>
				</div>
				<div class="d-flex-center text-center name">
					<div class="text-truncate">
						<div class="nameText">
							<span class="valueNameText">{{ step[nameProperty] }} </span>
							<div class="questionMark" v-show="step.remark">
								<v-tooltip bottom>
									<template v-slot:activator="{ on, attrs }">
										<v-icon v-bind="attrs" v-on="on" size="18px"> mdi-help-circle-outline </v-icon>
									</template>
									<div class="text-center" style="display: flex; align-items: start">
										<div
											style="text-align: center; color: #fff; line-height: 1; font-size: 13px; word-break: break-word; white-space: normal; max-width: 250px">
											{{ step.remark }}</div>
									</div>
								</v-tooltip>
							</div>
						</div>
						<div class="priceText" :class="{ redText: index === selectIndex }">{{ step.selfPrice }}</div>
					</div>
				</div>
				<half-design-check-icon class="absolute-top-right2 check-icon"></half-design-check-icon>
			</div>
		</div>
		<div class="moreDataBox"
			:class="{ 'style1': +nowLanyardsType == 1, 'style2': +nowLanyardsType == 2, 'style3': +nowLanyardsType == 3 }">
			<div class="stepItem" :class="{ lastActive: index == 1 }" v-for="(value, key, index) in LanyardData"
				:key="key">
				<half-design-custom-medals-ribbon-step2 v-if="index == 0" :stepData="value"
					@selectStep="selectStep2"></half-design-custom-medals-ribbon-step2>
				<half-design-custom-medals-ribbon-step3 :uploadBox="+nowLanyardsType == 2" :step1SelectItem="selectItem"
					:changeTitle="changeTitle" v-if="index == 1" :stepData="value" :radioIndex="radioIndex"
					@selectRadioFn="selectRadioFn" @selectStep="selectStep3"></half-design-custom-medals-ribbon-step3>
			</div>
		</div>
		<div class="errorTip">
			<v-alert dense outlined type="error">
				{{ langSemiCustom.errorTip }}
			</v-alert>
		</div>
	</div>
</template>

<script>
import { round2 } from "@/utils/utils";
import labelText from '@/components/HalfDesign/customMedals/common/labelText'
export default {
	name: 'ribbonStep1',
	props: {
		stepData: {
			type: Object,
			default: () => ({}),
		},
	},
	components: { labelText },
	data() {
		return {
			selectIndex: -1,
			selectItem: null,
			nowLanyardsType: -1,
			radioIndex: -1
		}
	},
	watch: {},
	computed: {
		langSemiCustom() {
			return this.$store.getters.lang?.semiCustom;
		},
		langQuote() {
			return this.$store.getters.lang.quote || {};
		},
		nameProperty() {
			let attributeFlag = this.attributeFlag,
				key;
			switch (attributeFlag) {
				case "color":
					key = "colorAlias";
					break;
				default:
					key = "valueName";
					break;
			}
			return key;
		},
		attributeFlag() {
			return this.stepData.attributeFlag;
		},
		LanyardData() {
			return this.$store.state.halfDesign.LanyardData;
		},
		changeTitle() {
			return +this.nowLanyardsType == 2 ? this.langQuote.Upload : '';
		}
	},
	methods: {
		getImg(step) {
			let attributeFlag = this.attributeFlag,
				key;
			switch (attributeFlag) {
				case "color":
					key = "sizeImg";
					break;
				case "area":
					key = "imgDetail";
					if (step.areaImg) {
						key = "areaImg";
					}
					break;
				default:
					key = "imgDetail";
					break;
			}
			return step[key];
		},
		selectStep(item, index = 0, state = false) {
			this.selectIndex = index;
			this.selectItem = item;
			this.nowLanyardsType = item.lanyardsType
			this.$emit("selectLanyardsTypeFn", this.nowLanyardsType, this.stepData.id)
			this.$emit("selectStep", {
				type: this.stepData.isLanyards,
				data: item,
				parent: this.stepData,
				id: this.stepData.id,
				firstSelect: state,
			});
		},
		selectStep2(data) {
			this.$emit("selectStep", data)
		},
		selectStep3(data) {
			this.$emit("selectStep", data)
		},
		selectDefault() {
			if (this.stepData.productParamList.length) {
				this.selectStep(this.stepData.productParamList[0], 0, true);
			}
		},
		getPrice(step) {
			let stepCopy = JSON.parse(JSON.stringify(step));
			let priceType = stepCopy.priceType,
				code = this.symbolCode,
				price = 0,
				rate = this.rate;
			if (priceType === 1) {
				price = round2(stepCopy.unitPrice * rate);
				return {
					t: `+${code}${price}/${this.unit}`,
					show: price && price > 0,
				};
			} else {
				return {
					show: false,
				};
			}
		},
		setItemSelfPrice() {
			this.stepData.productParamList.forEach((item) => {
				if (item.copySelfPrice) {
					//打印方式关闭了展示以及计算打印颜色价格
					if (this.canShowPrintColorPrice) {
						item.selfPrice = item.copySelfPrice;
					} else {
						item.selfPrice = this.langQuote.free;
					}
					return;
				}
				item.selfPrice = this.getPrice(item)["show"] ? this.getPrice(item)["t"] : this.langQuote.free;
				item.showFree = this.getPrice(item)["show"] ? false : true;
				item.copySelfPrice = item.selfPrice;
				item.copyShowFree = item.showFree;
				if (this.attributeFlag === "printColor") {
					if (!this.canShowPrintColorPrice) {
						item.selfPrice = this.langQuote.free;
					}
				}
			});
		},
		selectRadioFn(data) {
			this.radioIndex = data
			let LanyardColorFile = this.selectItem.files.length > 0 ? true : false;
			this.$emit("selectRadioFn", this.radioIndex, LanyardColorFile);
			//选择织带第一步用来验证
			this.$emit("selectStep", {
				type: this.stepData.isLanyards,
				data: this.selectItem,
				parent: this.stepData,
				id: this.stepData.id,
				firstSelect: false,
			});
		}
	},
	created() { },
	mounted() {
		this.$Bus.$on("selectDefaultOtherStep", this.selectDefault);
		// this.setItemSelfPrice();
	},
	beforeDestroy() {
		this.$Bus.$off("selectDefaultOtherStep", this.selectDefault);
	},
}
</script>

<style scoped lang="scss">
@import "~assets/css/half.scss";

.step-content {
	display: grid;
	gap: 10px;

	.step-item {
		position: relative;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		gap: 4px;
		@include step-default;
		min-width: 0;
		cursor: pointer;
		color: #333333;
		overflow: hidden;
		background-color: #f6f6f6;
		border-radius: 6px;

		.text-truncate {
			display: flex;
			align-items: center;
			font-weight: 400;
			font-size: 14px;

			.nameText {
				display: flex;
				align-items: center;
				overflow: hidden;
				max-width: 100%;
			}

			.valueNameText {
				white-space: normal;
				word-wrap: break-word;
			}
		}

		.name {
			flex: 1;
			align-items: flex-end;
		}

		.questionMark {
			flex-shrink: 0;
			display: none;
		}

		.priceText {
			display: none;
		}

		.check-icon {
			display: none;
		}

		.zoom-icon {
			display: none;
		}
	}

	.step-item-bg {
		background-color: #f6f6f6;
	}

	@include respond-to(mb) {
		gap: 4px;

		.step-item {
			font-size: 12px;
			padding: 4px;

			.text-truncate {
				font-size: 12px;
			}

			&.active {
				padding: 3px;
			}

			@media (any-hover: hover) {
				&:hover {
					padding: 3px;
				}
			}
		}

	}
}

.moreDataBox {
	margin-top: 10px;
	background: #FFFFFF;
	border-radius: 6px;
	border: 1px solid #DFE5EB;
	grid-template-columns: 1fr 1fr;
	gap: 1px;
	display: none;

	&.style1 {
		display: grid;
	}

	&.style2 {
		display: grid;
		grid-template-columns: 1fr;

		::v-deep .stepItem {
			&.lastActive {
				border-left: none;
				border-top: 1px solid #DFE5EB;
			}

			.ribbonStep2 {
				.step-content {
					grid-template-columns: repeat(3, 1fr);
					gap: 10px;
				}
			}
		}
	}

	&.style3 {
		display: none;
	}

	@media screen and (min-width: 1000px) {
		&.style1 {
			::v-deep .stepItem {
				.litterTitle {
					font-size: 12px;
				}

				.ribbonStep2 {
					.step-content {
						gap: 4px;
					}

					.step-item {
						padding: 8px 4px;

						&:hover {
							padding: 7px 3px;
						}

						&.active {
							padding: 7px 3px;
						}
					}
				}

				.valueNameText {
					font-size: 12px;
				}
			}
		}

		&.style2 {
			::v-deep .stepItem {
				.litterTitle {
					font-size: 14px;
				}

				.ribbonStep2 {
					.step-content {
						gap: 10px;
					}

					.step-item {
						padding: 8px 4px;

						&:hover {
							padding: 7px 3px;
						}

						&.active {
							padding: 7px 3px;
						}
					}
				}

				.valueNameText {
					font-size: 14px;
				}
			}
		}
	}

	.stepItem {
		padding: 8px;

		&.lastActive {
			border-left: 1px solid #DFE5EB;
		}
	}

	@include respond-to(mb) {
		.stepItem {
			padding: 4px;
		}

		&.style1 {
			grid-template-columns: 1fr;

			::v-deep .ribbonStep2 {
				.step-content {
					gap: 4px;

					.step-item {
						font-size: 12px;
						padding: 6px 4px;

						&.active {
							padding: 3px;
						}

						@media (any-hover: hover) {
							&:hover {
								padding: 3px;
							}
						}
					}
				}
			}

			::v-deep .stepItem {
				.litterTitle {
					font-size: 14px;
				}


				.valueNameText {
					font-size: 12px;
				}
			}
		}

		&.style2 {
			grid-template-columns: 1fr;

			::v-deep .stepItem {
				.litterTitle {
					font-size: 14px;
				}

				.valueNameText {
					font-size: 12px;
				}

				.ribbonStep2 {
					.step-content {
						grid-template-columns: repeat(2, 1fr);
						gap: 4px;

						.step-item {
							font-size: 12px;
							padding: 4px;

							&.active {
								padding: 3px;
							}

							@media (any-hover: hover) {
								&:hover {
									padding: 3px;
								}
							}
						}
					}
				}

				.ribbonStep3 .nowUploadFile {
					font-size: 12px;
					gap: 1em;
				}
			}
		}
	}
}

.style1 .step-content {
	grid-template-columns: repeat(3, 1fr);

	.step-item {
		background-color: #F4F5F5;

		&.active {
			background-color: #F4F5F5;

			.check-icon {
				display: flex;
			}
		}

		.imgWrap {
			@include flex-center;

			img {
				aspect-ratio: 150/184;
				border: none;
			}
		}

		.text-truncate {
			flex-direction: column;
			justify-content: center;
			row-gap: 4px;

			.priceText {
				display: none;

				&.redText {
					color: #de3500;
				}
			}
		}

		.questionMark {
			margin-left: 4px;
			display: flex;
		}
	}
}
</style>
