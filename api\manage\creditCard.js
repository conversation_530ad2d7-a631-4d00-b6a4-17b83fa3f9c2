import { request } from '~/utils/request'

//查询信用卡列表
export function getCardList() {
	return request({
		url: '/retailer/pay/to-config/list',
		method: 'get',

	})
}

//新增
export function addNewCard(data) {
	return request({
		url: '/retailer/pay/to-config/add',
		method: 'post',
		data,
	})
}

//新增
export function deleteACard(id) {
	return request({
		url: '/retailer/pay/to-config/delById?id=' + id,
		method: 'get',
	})
}
