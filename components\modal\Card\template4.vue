<template>
	<div class="card-box" :class="modal.wrapClass" :style="modal.style">
		<div class="bps-container" v-for="(o, oi) in modal.outer" :key="oi">
			<div class="content" :style="modal.contentStyle">
				<pic class="titleIcon" :src="modal.content[0].img.value" v-if="modal.content"
						 :alt="modal.content[0].img.alt" :title="modal.content[0].img.alt"
						 @click="setModalType(modal.content[0].img,modal.content,'img')"/>
				<EditDiv tagName="h2" v-model:content="o.title.value" v-if="o.title"
								 @click="setModalType(o.title,modal.content,'text')" />
				<div class="partImgWrap">
					<pic class="pic" :src="o.img.value" v-if="o.img" :alt="o.img.alt" :title="o.img.alt"
							 @click="setModalType(o.img,modal.outer,'img')"/>
					<div class="imgList">
						<div class="img-item" v-for="(l,li) in modal.list">
							<div class="imgWrap">
								<pic class="pic" :src="l.img.value" v-if="l.img" :alt="l.img.alt" :title="l.img.alt"/>
							</div>
							<EditDiv tag-name="span" v-model:content="l.text.value" v-if="l.text" />
						</div>
					</div>
				</div>
				<EditDiv class="des" v-model:content="o.text.value" v-if="o.text"
								 @click="setModalType(modal.outer[0].img,modal.outer,'text')" />
				<div class="btnWrap" @click="setModalType(modal.outer[0].button,modal.outer,'button')" v-if="o.button">
					<nuxt-link :to="o.button.url" :title="o.button.alt" :target="o.button.target || '_self'"
						 class="default-button bps-button" :style="{...o.button.style }">
						{{ o.button.value }}
						<b class="icon-bps-sanjiao"></b>
					</nuxt-link>
				</div>
			</div>
			<div class="partImgWrap">
				<pic class="pic" :src="o.img.value" v-if="o.img" :alt="o.img.alt" :title="o.img.alt"
						 @click="setModalType(modal.outer[0].img,modal.outer,'img')"/>
				<div class="imgList">
					<div class="img-item" v-for="(l,li) in modal.list">
						<div class="imgWrap">
							<pic class="pic" :src="l.img.value" v-if="l.img" :alt="l.img.alt" :title="l.img.alt"
									 @click="setModalType(l.img,modal.list,'img')"/>
						</div>
						<EditDiv tag-name="span" v-model:content="l.text.value" v-if="l.text"
										 @click="setModalType(l.text,modal.list,'text')" />
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	props: {
		data: {
			type: Object,
			default: {},
		}
	},
	data() {
		return {
			modal: {
				style: {},
				type: {index: 0, clickPosition: 'outer'},
				...this.data
			}
		};
	},
	watch: {
		modal: {
			handler(val) {
				this.$emit("update:data", val);
			},
			immediate: true,
			deep: true
		}
	},
	methods: {
		setModalType(target, targetArray, clickType, event, other) {
			this.$setModal(this, target, targetArray, clickType, event, other)
		},
	},
};
</script>

<style lang="scss" scoped>
.card-box {
	background-color: #f8f8f8;
	padding: 81px 0 64px;
	margin-top: 118px;

	.bps-container {
		position: relative;
		display: grid;
		padding-left: 100px;
		grid-template-columns: 405px 1fr;
		justify-content: flex-end;
		align-items: center;
		grid-gap: 45px;

		.btnWrap {
			display: inline-block;
			color: #ffffff;
		}

		.content {
			margin-top: -20px;

			& > .partImgWrap {
				display: none;
			}

			.titleIcon {
				width: 42px;
				margin-bottom: 27px;
			}
		}

		h2 {
			width: 318px;
			font-size: 36px;
			font-weight: 700;
		}

		.des {
			margin: 28px 0 32px;
			color: #666666;
			line-height: 24px;
		}

		.partImgWrap {
			position: relative;
			display: flex;
			height: 100%;
			justify-content: center;
			align-content: center;

			& > img {
				width: 100%;
				height: 100%;
				border-radius: 20px;
				object-fit: cover;
			}

			.imgList {
				position: absolute;
				top: 50%;
				right: 50px;
				transform: translateY(-50%);
				display: grid;
				grid-template-columns: repeat(4, 1fr);
				align-items: flex-start;
				grid-gap: 20px;

				.img-item {
					overflow: hidden;
					display: flex;
					flex-direction: column;
					justify-content: center;
					align-items: center;

					span {
						max-width: 100px;
						margin-top: 10px;
						font-size: 14px;
						word-break: break-word;
					}
				}

				.imgWrap {
					width: 76px;
					height: 76px;
					display: flex;
					justify-content: center;
					align-items: center;
					background: #ffffff;
					box-shadow: 0px 0px 12px 1px rgba(185, 205, 216, 0.65);
					border-radius: 10px;

					img {
						width: 36px;
					}
				}
			}
		}
	}
}

@media screen and (min-width: $mb-width) and (max-width: $pad-width) {
	.card-box {
		.bps-container {
			padding-left: 15px;
		}
	}
}

@media screen and (max-width: $mb-width) {
	.card-box {
		background-color: #ffffff;
		padding: 27px 0;
		margin-top: 0;

		.bps-container {
			display: grid;
			grid-template-columns: 1fr;
			justify-content: flex-end;
			padding-left: 15px;
			align-items: center;
			grid-gap: 45px;

			.content {
				margin-top: 0;

				.titleIcon {
					width: 21px;
					margin-bottom: 12px;
				}

				& > .partImgWrap {
					display: block;
				}

				& > img {
					width: 21px;
					margin-bottom: 13px;
				}
			}

			.partImgWrap {
				display: none;
				position: relative;
				top: auto;
				transform: none;
				font-size: 12px;
				margin: 0 -15px;

				& > img {
					height: 290px;
					object-fit: cover;
					border-radius: 0;
				}

				.imgList {
					transform: translate(-50%, -50%);
					left: 50%;
					width: 350px;
					right: auto;
					grid-gap: 20px;

					.img-item {
						span {
							font-size: 12px;
							text-align: center;
						}
					}

					.imgWrap {
						width: 50px;
						height: 50px;
						padding: 0;
						border-radius: 5px;
						img {
							width: 26px;
						}
					}
				}
			}

			h2 {
				width: 100%;
				font-size: 21px;
				margin-bottom: 25px;
			}

			.des {
				margin: 30px 0 28px;
				font-size: 12px;
				line-height: 22px;
			}
		}
	}
}
</style>
