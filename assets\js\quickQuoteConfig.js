// Quote 类
export class quickQuote {
	constructor(pid, cateId, productsName) {
		this.pid = pid;
		this.cateId = cateId;
		this.productsName = productsName;
	}
}

export const getQuickQuoteConfig = function (routeName) {
	let config = {};
	switch (routeName) {
		case "lang-quote-custom-24-hours-printed-medals":
			config = new quickQuote(627, 628, "custom 24 hours printed medals");
			config.navTitle = "We provide these medals types for you.";
			config.maxStep = 2;
			config.allStepConfig = {
				size: {
					showStep: 1,
				},
				qty: {
					showStep: 1,
				},
				upload: {
					showStep: 2,
				},
				discount: {
					showStep: 2,
				},
				template: {
					columns: {
						pc: 5,
						ipad: 5,
						mb: 3,
					},
					showStep: 2,
				},
				color: {
					showStep: 2,
				},
				attachment: {
					showStep: 2,
				},
			};
			break;
		case "lang-quote-48-hours-printed-medals":
			config = new quickQuote(623, 624, "custom shape medals");
			config.navTitle = "We provide these medals types for you.";
			config.maxStep = 2;
			config.uploadType = 2;
			config.allStepConfig = {
				size: {
					showStep: 1,
				},
				qty: {
					showStep: 1,
				},
				upload: {
					showStep: 2,
				},
				discount: {
					showStep: 2,
				},
				template: {
					showStep: 2,
				},
				color: {
					showStep: 2,
				},
				attachment: {
					showStep: 2,
				},
			};
			break;
	}
	return config;
};

export const getQuickQuoteConfigByPidAndCateId = function (pid,cateId) {
	let config = {};
	switch (pid) {
		case 444:
			config = new quickQuote(pid, cateId, "Custom Patches No Minimun");
			config.maxStep = 3;
			config.allStepConfig = {
				size: {
					showStep: 1,
					sizeTextConfig:{
						sizeW:"Patch Width",
						sizeH:"Patch Height"
					}
				},
				qty: {
					showStep: 1,
				},
				upload: {
					showStep: 3,
				},
				discount: {
					showStep: 3,
				},
				patchShape: {
					showStep: 2,
				},
				patchBacking: {
					showStep: 2,
				},
				patchBorder: {
					showStep: 3,
				},
				patchOptions: {
					showStep: 3,
				},
			};
			break
		case 399:
			config = new quickQuote(pid, cateId, "Custom Keychains");
			config.maxStep = 3;
			config.allStepConfig = {
				size: {
					showStep: 1,
				},
				qty: {
					showStep: 1,
				},
				keyChainsAttachment: {
					showStep: 2,
				},
				plating: {
					showStep: 2,
				},
				attachment: {
					showStep: 2,
				},
				upload: {
					showStep: 3,
				},
				discount: {
					showStep: 3,
				},
			};
			delete config.pid;
			delete config.cateId;
			break
		case 117:
			config = new quickQuote(pid, cateId, "Custom Keychains");
			config.maxStep = 2;
			config.allStepConfig = {
				size: {
					showStep: 1,
				},
				qty: {
					showStep: 1,
				},
				keyChainsAttachment: {
					showStep: 2,
				},
				upload: {
					showStep: 2,
				},
				discount: {
					showStep: 2,
				},
			};
			delete config.pid;
			delete config.cateId;
			break
		case 656:
			config = new quickQuote(pid, cateId, "Custom Ornaments");
			config.maxStep = 2;
			config.allStepConfig = {
				size: {
					showStep: 1,
				},
				qty: {
					showStep: 1,
				},
				template: {
					columns: {
						pc: 6,
						ipad: 6,
						mb: 2,
					},
					showStep: 2,
				},
				upload: {
					showStep: 2,
				},
				discount: {
					showStep: 2,
				},
			};
			delete config.pid;
			delete config.cateId;
			break
		case 659:
			config = new quickQuote(pid, cateId, "Custom Ornaments");
			config.maxStep = 2;
			config.allStepConfig = {
				size: {
					showStep: 1,
				},
				qty: {
					showStep: 1,
				},
				template: {
					columns: {
						pc: 6,
						ipad: 6,
						mb: 2,
					},
					showStep: 2,
				},
				upload: {
					showStep: 2,
				},
				discount: {
					showStep: 2,
				},
			};
			delete config.pid;
			delete config.cateId;
			break
		case 599:
			config = new quickQuote(pid, cateId, "Custom Ornaments");
			config.maxStep = 2;
			config.allStepConfig = {
				size: {
					showStep: 1,
				},
				qty: {
					showStep: 1,
				},
				template: {
					columns: {
						pc: 6,
						ipad: 6,
						mb: 2,
					},
					showStep: 2,
				},
				upload: {
					showStep: 2,
				},
				discount: {
					showStep: 2,
				},
			};
			delete config.pid;
			delete config.cateId;
			break
		case 625:
			config = new quickQuote(pid, cateId, "Custom Ornaments");
			config.maxStep = 2;
			config.allStepConfig = {
				size: {
					showStep: 1,
				},
				qty: {
					showStep: 1,
				},
				template: {
					columns: {
						pc: 6,
						ipad: 6,
						mb: 2,
					},
					showStep: 2,
				},
				upload: {
					showStep: 2,
				},
				discount: {
					showStep: 2,
				},
			};
			delete config.pid;
			delete config.cateId;
			break
		case 219:
			config = new quickQuote(pid, cateId, "Custom Embroidered Keychains");
			config.maxStep = 2;
			config.allStepConfig = {
				size: {
					showStep: 1,
				},
				qty: {
					showStep: 1,
				},
				plating: {
					showStep: 2,
				},
				attachment: {
					showStep: 2,
				},
				upload: {
					showStep: 2,
				},
				discount: {
					showStep: 2,
				},
			};
			delete config.pid;
			delete config.cateId;
			break
		case 118:
			config = new quickQuote(pid, cateId, "PVC Keychains");
			config.maxStep = 2;
			config.allStepConfig = {
				size: {
					showStep: 1,
				},
				qty: {
					showStep: 1,
				},
				plating: {
					showStep: 2,
				},
				attachment: {
					showStep: 2,
				},
				upload: {
					showStep: 2,
				},
				discount: {
					showStep: 2,
				},
			};
			delete config.pid;
			delete config.cateId;
			break
		case 583:
			config = new quickQuote(pid, cateId, "Metal Couples Keychains");
			config.maxStep = 2;
			config.allStepConfig = {
				size: {
					showStep: 1,
				},
				qty: {
					showStep: 1,
				},
				plating: {
					showStep: 2,
				},
				attachment: {
					showStep: 2,
				},
				upload: {
					showStep: 2,
				},
				discount: {
					showStep: 2,
				},
			};
			delete config.pid;
			delete config.cateId;
			break
		case 692:
			config = new quickQuote(pid, cateId, "Custom Golf Ball Markers");
			config.maxStep = 2;
			config.allStepConfig = {
				size: {
					showStep: 1,
				},
				qty: {
					showStep: 1,
				},
				plating: {
					showStep: 2,
				},
				upload: {
					showStep: 2,
				},
				discount: {
					showStep: 2,
				},
			};
			delete config.pid;
			delete config.cateId;
			break
		case 459:
			config = new quickQuote(pid, cateId, "Printed Care Labels");
			config.maxStep = 2;
			config.allStepConfig = {
				size: {
					showStep: 1,
				},
				qty: {
					showStep: 1,
				},
				color: {
					showStep: 2,
				},
				foldType: {
					showStep: 2,
				},
				upload: {
					showStep: 2,
				},
				discount: {
					showStep: 2,
				},
			};
			delete config.pid;
			delete config.cateId;
			break
		case 739:
			config = new quickQuote(pid, cateId, "Custom Earrings");
			config.maxStep = 2;
			config.allStepConfig = {
				size: {
					showStep: 1,
				},
				qty: {
					showStep: 1,
				},
				color: {
					showStep: 2,
				},
				package: {
					showStep: 2,
				},
				upload: {
					showStep: 2,
				},
				discount: {
					showStep: 2,
				},
			};
            delete config.pid;
            delete config.cateId;
			break;
		case 312:
			config = new quickQuote(pid, cateId, "Rush Medals");
			config.maxStep = 2;
			config.allStepConfig = {
				size: {
					showStep: 1,
				},
				qty: {
					showStep: 1,
				},
				upload: {
					showStep: 2,
				},
				discount: {
					showStep: 2,
				},
				template: {
					columns: {
						pc: 5,
						ipad: 5,
						mb: 3,
					},
					showStep: 2,
				},
				color: {
					showStep: 2,
				},
				attachment: {
					showStep: 2,
				}
			};
			delete config.pid;
			delete config.cateId;
			break;
		case 627:
			config = new quickQuote(627, 628, "custom 24 hours printed medals");
			config.navTitle = "We provide these medals types for you.";
			config.maxStep = 2;
			config.allStepConfig = {
				size: {
					showStep: 1,
				},
				qty: {
					showStep: 1,
				},
				upload: {
					showStep: 2,
				},
				discount: {
					showStep: 2,
				},
				template: {
					columns: {
						pc: 5,
						ipad: 5,
						mb: 3,
					},
					showStep: 2,
				},
				color: {
					showStep: 2,
				},
				attachment: {
					showStep: 2,
				},
			};
			delete config.pid;
			delete config.cateId;
			break
		case 623:
			config = new quickQuote(623, 624, "custom shape medals");
			config.navTitle = "We provide these medals types for you.";
			config.maxStep = 2;
			config.uploadType = 2;
			config.allStepConfig = {
				size: {
					showStep: 1,
				},
				qty: {
					showStep: 1,
				},
				upload: {
					showStep: 2,
				},
				discount: {
					showStep: 2,
				},
				template: {
					showStep: 2,
				},
				color: {
					showStep: 2,
				},
				attachment: {
					showStep: 2,
				},
			};
			delete config.pid;
			delete config.cateId;
			break
		default:
			config = new quickQuote(pid, cateId, "Custom Ornaments");
			config.maxStep = 3;
			config.allStepConfig = {
				size: {
					showStep: 1,
				},
				qty: {
					showStep: 1,
				},
				upload: {
					showStep: 3,
				},
				discount: {
					showStep: 3,
				},
				template: {
					columns: {
						pc: 5,
						ipad: 5,
						mb: 3,
					},
					showStep: 2,
				},
				color: {
					showStep: 2,
				},
				attachment: {
					showStep: 3,
				}
			};
			delete config.pid;
			delete config.cateId;
	}
	return config;
};