export const getQuickQuoteConfig = function (routeName) {
	const PID_CONFIG_MAP = {
		"lang-quote-custom-24-hours-printed-medals": {
			pid: 627,
			cateId: 628,
			navTitle: "We provide these medals types for you."
		},
		"lang-quote-48-hours-printed-medals": {
			pid: 623,
			cateId: 624,
			navTitle: "We provide these medals types for you."
		},
	};
	return PID_CONFIG_MAP[routeName];
};

export const getQuickQuoteConfigByPidAndCateId = function (pid) {
	let lang = this.$store.getters.lang.quote;
	// 定义默认配置
	const DEFAULT_CONFIG = {
		productsName: "quick quote",
		maxStep: 3,
		navTitle: "",
		uploadType: 1,
		allStepConfig: {},
		freeTipList: [
			{
				icon: "icon-jxsht-ybp-dg",
				value: lang.freeTip.t1,
			},
			{
				icon: "icon-jxsht-ybp-dg",
				value: lang.freeTip.t2,
			},
			{
				icon: "icon-jxsht-ybp-dg",
				value: lang.freeTip.t3,
			},
			{
				icon: "icon-jxsht-ybp-dg",
				value: lang.freeTip.t4,
			},
		],
	};
	// 创建配置映射表
	const PID_CONFIG_MAP = {
		444: {
			...DEFAULT_CONFIG,
			productsName: "Custom Patches No Minimun",
			maxStep: 3,
			allStepConfig: {
				size: {
					showStep: 1,
					sizeTextConfig: {
						sizeW: "Patch Width",
						sizeH: "Patch Height",
					},
				},
				qty: {
					showStep: 1,
				},
				upload: {
					showStep: 3,
				},
				discount: {
					showStep: 3,
				},
				patchShape: {
					showStep: 2,
				},
				patchBacking: {
					showStep: 2,
				},
				patchBorder: {
					showStep: 3,
				},
				patchOptions: {
					showStep: 3,
				},
			},
		},
		399: {
			...DEFAULT_CONFIG,
			productsName: "Custom Keychains",
			maxStep: 3,
			allStepConfig: {
				size: {
					showStep: 1,
				},
				qty: {
					showStep: 1,
				},
				keyChainsAttachment: {
					showStep: 2,
				},
				plating: {
					showStep: 2,
				},
				attachment: {
					showStep: 2,
				},
				upload: {
					showStep: 3,
				},
				discount: {
					showStep: 3,
				},
			},
		},
		117: {
			...DEFAULT_CONFIG,
			productsName: "Custom Keychains",
			maxStep: 2,
			allStepConfig: {
				size: {
					showStep: 1,
				},
				qty: {
					showStep: 1,
				},
				keyChainsAttachment: {
					showStep: 2,
				},
				upload: {
					showStep: 2,
				},
				discount: {
					showStep: 2,
				},
			},
		},
		656: {
			...DEFAULT_CONFIG,
			productsName: "Custom Ornaments",
			maxStep: 2,
			allStepConfig: {
				size: {
					showStep: 1,
				},
				qty: {
					showStep: 1,
				},
				template: {
					columns: {
						pc: 6,
						ipad: 6,
						mb: 2,
					},
					showStep: 2,
				},
				upload: {
					showStep: 2,
				},
				discount: {
					showStep: 2,
				},
				"Ornament Attachment":{
					showStep: 2,
					zoomIconConfig:{
						showZoomIcon: true
					},
					stepContentConfig: {
						pc: {
							style: {
								"grid-template-columns": "repeat(5, 1fr)",
							},
						},
						mb: {
							style: {
								"grid-template-columns": "repeat(3, 1fr)",
							},
						},
					},
					stepItemStyle: {
						style: {
							"flex-direction": "column",
						},
						stepImgWrapStyle: {
							style: {
								"width": "80%",
								"justify-content": "flex-start"
							},
						},
						stepTextWrapStyle: {
							style: {
								"text-align": "center"
							},
						},
					},
				},
			},
			freeTipList: [
				{
					icon: "icon-jxsht-ybp-dg",
					value: lang.freeTip.t1,
				},
				{
					icon: "icon-jxsht-ybp-dg",
					value: lang.freeTip.t10,
				},
				{
					icon: "icon-jxsht-ybp-dg",
					value: lang.freeTip.t3,
				},
				{
					icon: "icon-jxsht-ybp-dg",
					value: lang.freeTip.t4,
				},
			],
		},
		659: {
			...DEFAULT_CONFIG,
			productsName: "Custom Ornaments",
			maxStep: 2,
			allStepConfig: {
				size: {
					showStep: 1,
				},
				qty: {
					showStep: 1,
				},
				template: {
					columns: {
						pc: 6,
						ipad: 6,
						mb: 2,
					},
					showStep: 2,
				},
				upload: {
					showStep: 2,
				},
				discount: {
					showStep: 2,
				},
				"Ornament Process":{
					showStep: 2,
					zoomIconConfig:{
						showZoomIcon: true
					},
					stepContentConfig: {
						pc: {
							style: {
								"grid-template-columns": "repeat(5, 1fr)",
							},
						},
						mb: {
							style: {
								"grid-template-columns": "repeat(3, 1fr)",
							},
						},
					},
					stepItemStyle: {
						style: {
							"flex-direction": "column",
						},
						stepImgWrapStyle: {
							style: {
								"width": "80%",
								"justify-content": "flex-start"
							},
						},
						stepTextWrapStyle: {
							style: {
								"text-align": "center"
							},
						},
					},
				},
			},
			freeTipList: [
				{
					icon: "icon-jxsht-ybp-dg",
					value: lang.freeTip.t1,
				},
				{
					icon: "icon-jxsht-ybp-dg",
					value: lang.freeTip.t10,
				},
				{
					icon: "icon-jxsht-ybp-dg",
					value: lang.freeTip.t3,
				},
				{
					icon: "icon-jxsht-ybp-dg",
					value: lang.freeTip.t4,
				},
			],
		},
		625: {
			...DEFAULT_CONFIG,
			productsName: "Custom Ornaments",
			maxStep: 2,
			allStepConfig: {
				size: {
					showStep: 1,
				},
				qty: {
					showStep: 1,
				},
				template: {
					columns: {
						pc: 6,
						ipad: 6,
						mb: 2,
					},
					showStep: 2,
				},
				upload: {
					showStep: 2,
				},
				discount: {
					showStep: 2,
				},
				"Ornament Attachment":{
					showStep: 2,
					zoomIconConfig:{
						showZoomIcon: true
					},
					stepContentConfig: {
						pc: {
							style: {
								"grid-template-columns": "repeat(5, 1fr)",
							},
						},
						mb: {
							style: {
								"grid-template-columns": "repeat(3, 1fr)",
							},
						},
					},
					stepItemStyle: {
						style: {
							"flex-direction": "column",
						},
						stepImgWrapStyle: {
							style: {
								"width": "80%",
								"justify-content": "flex-start"
							},
						},
						stepTextWrapStyle: {
							style: {
								"text-align": "center"
							},
						},
					},
				},
			},
			freeTipList: [
				{
					icon: "icon-jxsht-ybp-dg",
					value: lang.freeTip.t1,
				},
				{
					icon: "icon-jxsht-ybp-dg",
					value: lang.freeTip.t10,
				},
				{
					icon: "icon-jxsht-ybp-dg",
					value: lang.freeTip.t3,
				},
				{
					icon: "icon-jxsht-ybp-dg",
					value: lang.freeTip.t4,
				},
			],
		},
		219: {
			...DEFAULT_CONFIG,
			productsName: "Custom Embroidered Keychains",
			maxStep: 2,
			allStepConfig: {
				size: {
					showStep: 1,
				},
				qty: {
					showStep: 1,
				},
				plating: {
					showStep: 2,
				},
				attachment: {
					showStep: 2,
				},
				upload: {
					showStep: 2,
				},
				discount: {
					showStep: 2,
				},
			},
		},
		118: {
			...DEFAULT_CONFIG,
			productsName: "PVC Keychains",
			maxStep: 3,
			allStepConfig: {
				size: {
					showStep: 1,
				},
				qty: {
					showStep: 1,
				},
				plating: {
					showStep: 2,
				},
				attachment: {
					showStep: 2,
				},
				colorAttachment: {
					showStep: 2,
				},
				upload: {
					showStep: 3,
				},
				discount: {
					showStep: 3,
				},
			},
		},
		583: {
			...DEFAULT_CONFIG,
			productsName: "Metal Couples Keychains",
			maxStep: 2,
			allStepConfig: {
				size: {
					showStep: 1,
				},
				qty: {
					showStep: 1,
				},
				plating: {
					showStep: 2,
				},
				attachment: {
					showStep: 2,
				},
				upload: {
					showStep: 2,
				},
				discount: {
					showStep: 2,
				},
			},
		},
		459: {
			...DEFAULT_CONFIG,
			productsName: "Printed Care Labels",
			maxStep: 2,
			allStepConfig: {
				size: {
					showStep: 1,
				},
				qty: {
					showStep: 1,
				},
				color: {
					showStep: 2,
				},
				foldType: {
					showStep: 2,
				},
				upload: {
					showStep: 2,
				},
				discount: {
					showStep: 2,
				},
			},
		},
		627: {
			...DEFAULT_CONFIG,
			productsName: "custom 24 hours printed medals",
			maxStep: 2,
			allStepConfig: {
				size: {
					showStep: 1,
				},
				qty: {
					showStep: 1,
				},
				upload: {
					showStep: 2,
				},
				discount: {
					showStep: 2,
				},
				template: {
					columns: {
						pc: 5,
						ipad: 5,
						mb: 3,
					},
					showStep: 2,
				},
				color: {
					showStep: 2,
				},
				attachment: {
					showStep: 2,
				},
				"Plating":{
					showStep: 2,
					stepItemStyle: {
						stepImgWrapStyle: {
							style: {
								"width": "30%"
							},
						},
					},
				},
				"Ribbon":{
					showStep: 2,
					stepContentConfig: {
						pc: {
							style: {
								"grid-template-columns": "repeat(8, 1fr)",
							},
						},
						mb: {
							style: {
								"grid-template-columns": "repeat(4, 1fr)",
							},
						},
					},
					stepItemStyle: {
						stepTextWrapStyle: {
							style: {
								"display": "none"
							},
						},
					},
				},
			},
			freeTipList: [
				{
					icon: "icon-jxsht-ybp-dg",
					value: lang.freeTip.t1,
				},
				{
					icon: "icon-jxsht-ybp-dg",
					value: lang.freeTip.t10,
				},
				{
					icon: "icon-jxsht-ybp-dg",
					value: lang.freeTip.t3,
				},
				{
					icon: "icon-jxsht-ybp-dg",
					value: lang.freeTip.t4,
				},
			],
		},
		623: {
			...DEFAULT_CONFIG,
			productsName: "custom shape medals",
			uploadType: 2,
			maxStep: 2,
			allStepConfig: {
				size: {
					showStep: 1,
				},
				qty: {
					showStep: 1,
				},
				upload: {
					showStep: 2,
				},
				discount: {
					showStep: 2,
				},
				template: {
					showStep: 2,
				},
				color: {
					showStep: 2,
				},
				attachment: {
					showStep: 2,
				},
				"Plating":{
					showStep: 2,
				},
				"Ribbon":{
					showStep: 2,
					stepContentConfig: {
						pc: {
							style: {
								"grid-template-columns": "repeat(8, 1fr)",
							},
						},
						mb: {
							style: {
								"grid-template-columns": "repeat(4, 1fr)",
							},
						},
					},
					stepItemStyle: {
						stepTextWrapStyle: {
							style: {
								"display": "none"
							},
						},
					},
				},
			},
			freeTipList: [
				{
					icon: "icon-jxsht-ybp-dg",
					value: lang.freeTip.t1,
				},
				{
					icon: "icon-jxsht-ybp-dg",
					value: lang.freeTip.t10,
				},
				{
					icon: "icon-jxsht-ybp-dg",
					value: lang.freeTip.t3,
				},
				{
					icon: "icon-jxsht-ybp-dg",
					value: lang.freeTip.t4,
				},
			],
		},
		112: {
			...DEFAULT_CONFIG,
			productsName: "Metal Enamel Keychains",
			maxStep: 3,
			uploadType: 2,
			allStepConfig: {
				size: {
					showStep: 1,
				},
				qty: {
					showStep: 1,
				},
				upload: {
					showStep: 2,
				},
				discount: {
					showStep: 2,
				},
				"Select Metal Finish": {
					showStep: 2,
					showPriceText: false,
				},
			},
		},

		240: {
			...DEFAULT_CONFIG,
			productsName: "Rush Pins",
			maxStep: 2,
			allStepConfig: {
				size: {
					showStep: 1,
				},
				qty: {
					showStep: 1,
				},
				upload: {
					showStep: 2,
				},
				discount: {
					showStep: 2,
				},
				"Select Metal Finish": {
					showStep: 2,
					stepItemStyle: {
						stepImgWrapStyle: {
							style: {
								flex: "1 0 0",
							},
						},
						stepTextWrapStyle: {
							style: {
								flex: "0.4 0 0",
							},
						},
					},
				},
			},
		},
		441: {
			...DEFAULT_CONFIG,
			productsName: "Rush Pins",
			maxStep: 3,
			allStepConfig: {
				size: {
					showStep: 1,
				},
				qty: {
					showStep: 1,
				},
				upload: {
					showStep: 3,
				},
				discount: {
					showStep: 3,
				},
				Plating: {
					showStep: 2,
				},
				"Packaging Options": {
					showStep: 2,
				},
				"Coin Edge": {
					showStep: 2,
					stepContentConfig: {
						pc: {
							style: {
								"grid-template-columns": "repeat(2, 1fr)",
							},
						},
					},
					mediaConfig: {
						style: {
							"aspect-ratio": "2/1",
						},
					},
					stepItemStyle: {
						style: {
							padding: "0",
						},
						mbStyle: {
							"flex-direction": "column",
						},
						stepImgWrapStyle: {
							style: {
								flex: "1 0 0",
							},
						},
						mbStepImgWrapStyle: {
							style: {
								width: "100%",
							},
						},
						stepTextWrapStyle: {
							style: {
								flex: "0.5 0 0",
							},
						},
						mbStepTextWrapStyle: {
							style: {
								padding: "0.5em",
							},
						},
					},
					mbZoomIconConfig: {
						style: {
							left: ".5em",
						},
					},
				},
			},
		},
		631: {
			...DEFAULT_CONFIG,
			productsName: "Rush keychains",
			maxStep: 3,
			allStepConfig: {
				size: {
					showStep: 1,
				},
				qty: {
					showStep: 1,
				},
				upload: {
					showStep: 3,
				},
				discount: {
					showStep: 3,
				},
				Plating: {
					showStep: 2,
					stepContentConfig: {
						pc: {
							style: {
								"grid-template-columns": "repeat(4, 1fr)",
							},
						},
						mb: {
							style: {
								"grid-template-columns": "repeat(2, 1fr)",
							},
						},
					},
				},
				"Design Areas": {
					showStep: 2,
					zoomIconConfig:{
						showZoomIcon: true
					},
					stepContentConfig: {
						pc: {
							style: {
								"grid-template-columns": "repeat(3, 1fr)",
							},
						}
					},
					stepItemStyle: {
						style: {
							"flex-direction": "column",
						},
						stepImgWrapStyle: {
							style: {
								width: "80%",
							},
						},
						stepTextWrapStyle: {
							style: {
								padding: "0.5em",
								"text-align": "center"
							},
						},
					},
				}
			},
		},
		692: {
			...DEFAULT_CONFIG,
			productsName: "Custom Golf Ball Markers",
			maxStep: 2,
			allStepConfig: {
				size: {
					showStep: 1,
				},
				qty: {
					showStep: 1,
				},
				plating: {
					showStep: 2,
					stepItemStyle: {
						stepImgWrapStyle: {
							style: {
								width: "5em",
							}
						}
					},
				},
				upload: {
					showStep: 2,
				},
				discount: {
					showStep: 2,
				},
			},
		},
		931: {
			...DEFAULT_CONFIG,
			productsName: "Custom Luggage Tags",
			maxStep: 3,
			allStepConfig: {
				size: {
					showStep: 1,
				},
				qty: {
					showStep: 1,
				},
				"Decoration Location": {
					showStep: 2,
					showPriceText: true,
					stepContentConfig: {
						pc: {
							style: {
								"grid-template-columns": "repeat(3, 1fr)",
							},
						},
						mb: {
							style: {
								"grid-template-columns": "repeat(2, 1fr)",
							},
						},
					},
					stepItemStyle: {
						stepImgWrapStyle: {
							style: {
								flex: "1 0 0",
							},
						},
						stepTextWrapStyle: {
							style: {
								flex: "1.5 0 0",
								"text-align": "center"
							},
						}
					},
				},
				"Layout": {
					showStep: 2,
					stepContentConfig: {
						pc: {
							style: {
								"grid-template-columns": "repeat(3, 1fr)",
							},
						},
						mb: {
							style: {
								"grid-template-columns": "repeat(2, 1fr)",
							},
						},
					},
					stepItemStyle: {
						stepImgWrapStyle: {
							style: {
								width: "30%",
							},
						}
					},
				},
				"Attachments": {
					showStep: 2,
					showPriceText: true,
					stepItemStyle: {
						style: {
							"flex-direction": "column",
						},
						stepImgWrapStyle: {
							style: {
								"justify-content": "flex-start"
							},
						},
						stepTextWrapStyle: {
							style: {
								padding: "0.5em",
								"text-align": "center"
							},
						},
					},
				},
				upload: {
					showStep: 3,
				},
				discount: {
					showStep: 3,
				},
			},
			freeTipList: [
				{
					icon: "icon-jxsht-ybp-dg",
					value: lang.freeTip.t5,
				},
				{
					icon: "icon-jxsht-ybp-dg",
					value: lang.freeTip.t6,
				},
				{
					icon: "icon-jxsht-ybp-dg",
					value: lang.freeTip.t7,
				},
				{
					icon: "icon-jxsht-ybp-dg",
					value: lang.freeTip.t8,
				},
			],
		},
		739: {
			...DEFAULT_CONFIG,
			productsName: "Custom Earrings",
			maxStep: 2,
			allStepConfig: {
				size: {
					showStep: 1,
				},
				qty: {
					showStep: 1,
				},
				color: {
					showStep: 2,
				},
				package: {
					showStep: 2,
				},
				"Plating Color":{
					showStep: 2,
					stepItemStyle: {
						stepImgWrapStyle: {
							style: {
								width :"50%"
							},
						}
					},
				},
				"Packaging":{
					showStep: 2,
					stepItemStyle: {
						stepImgWrapStyle: {
							style: {
								width :"50%"
							},
						}
					},
				},
				upload: {
					showStep: 2,
				},
				discount: {
					showStep: 2,
				},
			},
			freeTipList: [
				{
					icon: "icon-jxsht-ybp-dg",
					value: lang.freeTip.t1,
				},
				{
					icon: "icon-jxsht-ybp-dg",
					value: lang.freeTip.t9,
				},
				{
					icon: "icon-jxsht-ybp-dg",
					value: lang.freeTip.t3,
				},
				{
					icon: "icon-jxsht-ybp-dg",
					value: lang.freeTip.t4,
				},
			],
		},
		599: {
			...DEFAULT_CONFIG,
			productsName: "Custom Ornaments",
			maxStep: 2,
			allStepConfig: {
				size: {
					showStep: 1,
				},
				qty: {
					showStep: 1,
				},
				template: {
					columns: {
						pc: 6,
						ipad: 6,
						mb: 2,
					},
					showStep: 2,
				},
				"Ornament Attachment":{
					showStep: 2,
					zoomIconConfig:{
						showZoomIcon: true
					},
					stepContentConfig: {
						pc: {
							style: {
								"grid-template-columns": "repeat(5, 1fr)",
							},
						},
						mb: {
							style: {
								"grid-template-columns": "repeat(3, 1fr)",
							},
						},
					},
					stepItemStyle: {
						style: {
							"flex-direction": "column",
						},
						stepImgWrapStyle: {
							style: {
								"width": "80%",
								"justify-content": "flex-start"
							},
						},
						stepTextWrapStyle: {
							style: {
								"text-align": "center"
							},
						},
					},
				},
				upload: {
					showStep: 2,
				},
				discount: {
					showStep: 2,
				},
			},
			freeTipList: [
				{
					icon: "icon-jxsht-ybp-dg",
					value: lang.freeTip.t1,
				},
				{
					icon: "icon-jxsht-ybp-dg",
					value: lang.freeTip.t10,
				},
				{
					icon: "icon-jxsht-ybp-dg",
					value: lang.freeTip.t3,
				},
				{
					icon: "icon-jxsht-ybp-dg",
					value: lang.freeTip.t4,
				},
			],
		},
		312: {
			...DEFAULT_CONFIG,
			productsName: "Rush Medals",
			maxStep: 2,
			allStepConfig: {
				size: {
					showStep: 1,
				},
				qty: {
					showStep: 1,
				},
				upload: {
					showStep: 2,
				},
				discount: {
					showStep: 2,
				},
				template: {
					columns: {
						pc: 5,
						ipad: 5,
						mb: 3,
					},
					showStep: 2,
				},
				"Plating":{
					showStep: 2,
				},
				"Ribbon":{
					showStep: 2,
					stepContentConfig: {
						pc: {
							style: {
								"grid-template-columns": "repeat(8, 1fr)",
							},
						},
						mb: {
							style: {
								"grid-template-columns": "repeat(4, 1fr)",
							},
						},
					},
					stepItemStyle: {
						stepTextWrapStyle: {
							style: {
								"display": "none"
							},
						},
					},
				},
				color: {
					showStep: 2,
				},
				attachment: {
					showStep: 2,
				},
			},
		},
	};
	if (!PID_CONFIG_MAP[pid]) {
		throw new Error("quote config error");
	}
	return PID_CONFIG_MAP[pid];
};
