<template>
	<div class="favorite" @click.stop="onClick">
		<b :class="className"></b>
	</div>
</template>

<script>
import { addCollection, deleteConllectionByUserId } from "@/api/web";

export default {
	name: "Favorite",
	props: {
		// 产品信息
		productInfo: Object,
	},
	data() {
		return {
			isFavorite: this.productInfo.isCollection,
		};
	},
	computed: {
		userId() {
			return this.$store.state.userInfo?.id;
		},
		isLogin() {
			return this.$store.getters.isLogin;
		},
		className() {
			return this.isFavorite ? "icon-xinxin isActive" : "icon-shoucang";
		},
	},
	methods: {
		onClick() {
			if (!this.isLogin) {
				this.$store.commit("setLogin", "login");
				return;
			}

			let promise;
			const curIsFavorite = this.isFavorite;
			this.isFavorite = !this.isFavorite;
			if (curIsFavorite) {
				promise = deleteConllectionByUserId({ userId: this.userId, productId: this.productInfo.id });
			} else {
				// const productData = {
				// 	data: {
				// 		item_id: this.productInfo.productSku,
				// 		item_name: this.productInfo.name,
				// 	},
				// 	value: this.priceInfo.totalPrice || 0,
				// };
				promise = addCollection(
					{
						website: 1,
						userId: this.userId,
						cateId: this.productInfo.categoryId,
						productId: this.productInfo.id,
					},
					// productData,
				);
			}

			promise
				.then(() => {
					this.$emit("done");
				})
				.catch(() => {
					this.isFavorite = !this.isFavorite;
				});
		},
	},
};
</script>

<style scoped>
.favorite {
	cursor: pointer;

	b {
		font-size: 1.5em;
        text-shadow: 1px 1px 2px white;
		@include respond-to(mb) {
			font-size: 1.33em;
		}
	}

	b.isActive {
		color: var(--color-primary);
	}
}
</style>
