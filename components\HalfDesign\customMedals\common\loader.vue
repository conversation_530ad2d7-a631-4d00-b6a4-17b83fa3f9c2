<template>
  <div class="overlay changeLoad" v-show="loadState" :style="{ backgroundColor: backgroundColor }">
    <div class="loader"></div>
  </div>
</template>

<script>
export default {
  name: 'loader',
  props: {
    loadState: {
      type: Boolean,
      default: false
    },
    backgroundColor: {
      type: String,
    }
  },
  components: {},
  data() {
    return {

    }
  },
  watch: {},
  computed: {},
  methods: {},
  created() { },
  mounted() { },
}
</script>

<style scoped lang="scss">
.loader {
  width: 50px;
  aspect-ratio: 1;
  display: grid;
  border: 4px solid #0000;
  border-radius: 50%;
  border-right-color: $color-primary;
  animation: l15 1s infinite linear;
}

.loader::before,
.loader::after {
  content: "";
  grid-area: 1/1;
  margin: 2px;
  border: inherit;
  border-radius: 50%;
  animation: l15 2s infinite;
}

.loader::after {
  margin: 8px;
  animation-duration: 3s;
}

@keyframes l15 {
  100% {
    transform: rotate(1turn);
  }
}

.changeLoad {
  position: absolute;
  inset: 0;
  width: 100%;

}

.overlay {
  align-items: center;
  border-radius: inherit;
  display: flex;
  justify-content: center;
  width: 100%;
  background-color: rgba(0, 0, 0, 0.1);
  z-index: 1
}
</style>
