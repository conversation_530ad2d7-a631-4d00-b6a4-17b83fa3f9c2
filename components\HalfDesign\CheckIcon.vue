<template>
	<div class="checkIcon">
		<v-icon color="#ffffff" small> mdi-check </v-icon>
	</div>
</template>

<script>
export default {};
</script>

<style scoped lang="scss">
@import "~assets/css/half.scss";

.checkIcon {
	--border-radius:4px;
	display: flex;
	justify-content: center;
	align-items: center;
	width: 22px;
	height: 22px;
	background: var(--color-primary, transparent);
	border-radius: 0 var(--border-radius) 0 var(--border-radius);
	&::after {
		content: "";
		position: absolute;
		inset: 0;
		background-color: $color-primary;
		border-radius: 0 0 0 var(--border-radius);
		z-index: 0;
	}
	.v-icon{
		z-index: 1;
	}
	@include respond-to(mb) {
		--border-radius:2px;
		width: 16px;
		height: 16px;
	}
}
</style>
