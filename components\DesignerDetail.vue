<template>
	<div class="productWrap">
		<div class="productTitle" v-if="device !== 'mb'">
			<h2>
				<span @click="back"><b class="icon-back"></b></span>
				{{ productData.productName }}
			</h2>
			<div
				v-if="isLogin"
				class="add-favorite"
				style="cursor: pointer"
				@click="
					goCollection({
						isCollection: productData.isCollection,
						id: productData.id,
					})
				"
			>
				<template v-if="!productData.isCollection">
					<b class="icon-shoucang"></b>
					<span>Add To Favorites</span>
				</template>
				<template v-else>
					<b style="color: #d24600" class="icon-xinxin isActive"></b>
					<span>Cancel Favorites</span>
				</template>
			</div>
		</div>
		<div class="content">
			<div class="left">
				<div class="swiper myswiper2" ref="swiper2">
					<div class="swiper-wrapper">
						<div class="swiper-slide" v-if="productVideo">
							<VideoPlayer ref="videoPlayer" disabled-mouse :options="getVideoOptions(productVideo)"></VideoPlayer>
						</div>
						<div class="swiper-slide" v-for="(item, index) in imgList" :key="index">
							<img :src="item.url" :alt="item.name" :title="item.name" style="width: 100%; height: 100%; cursor: pointer" />
						</div>
					</div>
					<div class="swiper-button-prev"></div>
					<div class="swiper-button-next"></div>
				</div>
				<div class="swiper myswiper1" ref="swiper1">
					<div class="swiper-wrapper">
						<div class="swiper-slide" v-if="productVideo">
							<img :src="productData.productCoverImage" />
						</div>
						<div class="swiper-slide" v-for="(item, index) in imgList" :key="index">
							<img style="cursor: pointer" :src="item.url" :alt="item.name" :title="item.name" />
						</div>
					</div>
				</div>
			</div>
			<div class="right">
				<div class="productTitle" v-if="device === 'mb'">
					<h2>
						<span @click="back"><b class="icon-back"></b></span> {{ productData.productName }}
					</h2>
					<div
						v-if="isLogin"
						class="add-favorite"
						@click="
							goCollection({
								isCollection: productData.isCollection,
								id: productData.id,
							})
						"
					>
						<template v-if="!productData.isCollection">
							<b class="icon-shoucang"></b>
						</template>
						<template v-else>
							<b style="color: #d24600" class="icon-xinxin isActive"></b>
						</template>
					</div>
				</div>
				<div class="userInfo">
					<img :src="productData.user.avatar" alt="" />
					Designed and sold by <strong>&nbsp;{{ productData.user.firstName }} {{ productData.user.lastName }}</strong>
				</div>
				<div class="price">
					<strong>
						<CcyRate :price="productData.unitPrice"></CcyRate>
					</strong>
					<span v-if="productData.salesVolume && productData.salesVolume > 0"> Sales {{ getSalesVolume(productData.salesVolume || 0) }} </span>
				</div>
				<div class="params">
					<div class="paramsItem">
						<div>Quantity:</div>
						<div>
							<el-input-number v-model="quantity" :min="1" :max="productData.productStock" :disabled="!productData.productStock"></el-input-number>
						</div>
					</div>
					<div class="paramsItem">
						<div>Size:</div>
						<div>{{ productData.productSize }} {{ productData.productUnit }}</div>
					</div>
				</div>
				<div class="btnGroup" id="subtotalBtnGroup">
					<button primary plain class="addCart" @click="addCart" :disabled="!productData.productStock || !productData.isPublish">Add to cart</button>
					<button primary class="buyNow" @click="buyNow" :disabled="!productData.productStock || !productData.isPublish">Buy Now</button>
				</div>
				<div class="freeTip">
					<img src="https://static-oss.gs-souvenir.com/web/quoteManage/20241121/Frame_20563hFH7e.png" alt="" />
					Free Shipping
				</div>
				<div class="des">
					<div>
						<strong>Design Concept:</strong>
					</div>
					<p class="des-con" v-html="productData.productDesc"></p>
				</div>
			</div>
		</div>
		<div class="pdt-sub">
			<div class="pdt-sub-item pdt-sub-qty">
				<span>Quantity</span>
				<span class="blackFont"> {{ quantity }}pcs </span>
			</div>
			<div class="pdt-sub-item pdt-sub-unit">
				<span>Unit Price</span>
				<span class="blackFont">
					<CCYRate :price="productData.unitPrice"></CCYRate>
				</span>
			</div>
			<div class="pdt-sub-item pdt-sub-total">
				<span>Subtotal</span>
				<span class="blackFont">
					<CCYRate :price="subTotal"></CCYRate>
				</span>
			</div>
			<div class="pdt-sub-item pdt-sub-cart" @click="addCart" :disabled="!productData.productStock || !productData.isPublish">
				<span style="margin-bottom: 0">
					<b class="icon-a-Shoppingcart"></b>
					Add To Cart
				</span>
			</div>
		</div>
	</div>
</template>

<script>
import "@/plugins/element";
import { designerAddCollection, removeCollectionById, getProductByRoutingName, addShoppingCart, addShoppingCartNotLogin } from "@/api/web";
import CcyRate from "@/components/CCYRate.vue";
import VideoPlayer from "@/components/HalfDesign/VideoPlayer.vue";

export default {
	props: {
		productRouting: {
			type: String,
			required: true,
		},
	},
	components: { VideoPlayer, CcyRate },
	data() {
		return {
			productData: {
				user: {},
			},
			quantity: 1,
			videoOptions: {},
			productVideo: "",
		};
	},
	computed: {
		imgList() {
			return this.productData.imgJson ? JSON.parse(this.productData.imgJson).filter((item) => item.url) : [];
		},
		subTotal() {
			return this.productData.unitPrice * this.quantity;
		},
		isLogin() {
			return this.$store.getters.isLogin;
		},
		userId() {
			return this.$store.state.userInfo?.id;
		},
		userUUID() {
			return this.$store.state.userUUID;
		},
		proId() {
			return this.$store.state.proId;
		},
		device() {
			return this.$store.state.device;
		},
	},
	watch: {
		async isLogin() {
			try {
				let resData = await getProductByRoutingName({
					productRouting: this.productRouting,
					userId: this.userId,
				});
				this.productData = resData.data;
			} catch (e) {}
		},
	},
	methods: {
		back() {
			this.$emit("back");
		},
		//获取加号
		getSalesVolume(num) {
			if (num < 100) {
				return num;
			}
			let a = parseInt(num / 100) * 100;
			return a + "+";
		},
		getVideoOptions(url) {
			return {
				autoplay: true,
				controls: true,
				muted: true,
				loop: true,
				fill: true,
				preload: "auto",
				poster: url,
				sources: [
					{
						src: url,
						type: "video/mp4",
					},
				],
			};
		},
		addToCart() {
			return new Promise((resolve, reject) => {
				if (this.isLogin) {
					addShoppingCart({
						userId: this.userId,
						newShoppingCartListItem: [
							{
								productId: this.productData.id,
								proId: this.proId,
								cateId: this.productData.categoryId,
								productNum: this.quantity,
							},
						],
					}).then((res) => {
						if (res.code === 200) {
							resolve(res);
						} else {
							this.$toast.error(res.message);
						}
					});
				} else {
					addShoppingCartNotLogin({
						cartUuid: this.userUUID,
						newShoppingCartListItem: [
							{
								productId: this.productData.id,
								proId: this.proId,
								cateId: this.productData.categoryId,
								productNum: this.quantity,
							},
						],
					}).then((res) => {
						if (res.code === 200) {
							resolve(res);
						} else {
							this.$toast.error(res.message);
						}
					});
				}
			});
		},
		async addCart() {
			this.$gl.show();
			try {
				await this.addToCart();
				if (this.proId == 1) {
					let targetWindow = window.opener || window.parent;
					targetWindow.postMessage({ type: "toCart" }, window.origin);
				} else {
					let targetWindow = window.opener || window.parent;
					targetWindow.postMessage({ type: "toStyleListCart" }, window.origin);
				}
				setTimeout(() => {
					this.$gl.hide();
				}, 1000);
			} catch (e) {}
		},
		async buyNow() {
			try {
				if (this.proId == 1) {
					let targetWindow = window.opener || window.parent;
					targetWindow.postMessage(
						{
							type: "toCheckout",
							query: {
								productId: this.productData.id,
								quantity: this.quantity,
								isBuyNow: 1,
							},
						},
						window.origin
					);
				} else {
					let targetWindow = window.opener || window.parent;
					targetWindow.postMessage(
						{
							type: "toStyleListCheckout",
							query: {
								productId: this.productData.id,
								quantity: this.quantity,
								isBuyNow: 1,
							},
						},
						window.origin
					);
				}
			} catch (e) {}
		},
		goCollection(item) {
			if (this.isLoading) {
				return;
			}
			this.isLoading = true;
			let isCollection = item.isCollection;
			if (!this.isLogin) {
				this.isLoading = false;
				showLoginBoxMethod();
				// this.$store.commit("setLogin", "login");
				return;
			}
			if (isCollection) {
				removeCollectionById({
					id: this.productData.collectionId,
				})
					.then(() => {
						this.productData.isCollection = 0;
						this.$Bus.$emit("changeCollectionStatus", {
							id: this.productData.id,
							isCollection: false,
						});
					})
					.finally(() => {
						this.isLoading = false;
					});
			} else {
				designerAddCollection({
					userId: this.userId,
					cateId: this.productData.categoryId,
					productId: this.productData.id,
				})
					.then((res) => {
						this.productData.isCollection = 1;
						this.productData.collectionId = res.data;
						this.$Bus.$emit("changeCollectionStatus", {
							id: this.productData.id,
							isCollection: true,
						});
					})
					.finally(() => {
						this.isLoading = false;
					});
			}
		},
		initSwiper() {
			let _this = this;
			this.myswiper1 = new Swiper(this.$refs.swiper1, {
				slidesPerView: 6,
				spaceBetween: 8,
				watchSlidesVisibility: true, //防止不可点击
				grabCursor: true,
			});
			this.myswiper2 = new Swiper(this.$refs.swiper2, {
				slidesPerView: 1,
				spaceBetween: 30,
				autoplay: false,
				grabCursor: true,
				observer: true,
				observeParents: true,
				thumbs: {
					swiper: this.myswiper1,
				},
				navigation: {
					nextEl: ".myswiper2 .swiper-button-next",
					prevEl: ".myswiper2 .swiper-button-prev",
				},
				on: {
					slideChangeTransitionEnd: function (value) {
						_this.imgList.forEach(function (v, i) {
							if (i === value.activeIndex) {
								if (_this.$refs["videoPlayer"]) {
									_this.$refs["videoPlayer"][0].player.play();
								}
							} else {
								if (_this.$refs["videoPlayer"]) {
									_this.$refs["videoPlayer"][0].player.pause();
								}
							}
						});
					},
				},
			});
		},
		observeBtn() {
			if (this.device !== "mb") {
				return;
			}
			const ele = document.querySelector("#subtotalBtnGroup"),
				zhanwei = document.querySelector("#zhanwei"),
				observeDom2 = document.querySelector("#modalFooter");
			const observer = new IntersectionObserver(
				(entries) => {
					entries.forEach((entry) => {
						if (entry.isIntersecting) {
							ele.classList.remove("fixed");
							ele.classList.add("notFixed");
							zhanwei.style.height = 0;
						} else {
							ele.classList.remove("notFixed");
							ele.classList.add("fixed");
							zhanwei.style.height = 68 + "px";
						}
					});
				},
				{
					threshold: 0.1,
				}
			);
			observer.observe(observeDom2);
		},
	},
	async mounted() {
		this.$gl.show();
		let resData = await getProductByRoutingName({
			productRouting: this.productRouting,
			userId: this.$store.state.userInfo?.id,
		});
		this.productData = resData.data;
		try {
			this.productVideo = this.productData.productVideo ? JSON.parse(this.productData.productVideo)[0] : "";
		} catch (e) {}
		// this.observeBtn();
		this.initSwiper();
		this.$gl.hide();
	},
};
</script>
<style lang="scss" scoped>
$color-price: #d24600;
.swiper.myswiper2 {
	--swiper-navigation-color: #ffffff;
	/* 单独设置按钮颜色 */
	--swiper-navigation-size: 20px;

	/* 设置按钮大小 */
	.swiper-button-next,
	.swiper-button-prev {
		width: 46px;
		height: 46px;
		background: #000000;
		opacity: 0.4;
		border-radius: 50%;
		transform: translateY(-50%);
		margin-top: 0;
	}
}

.productWrap {
	position: relative;
	padding: 20px;
	width: 100%;

	@include respond-to(mb) {
		padding: 0;
		font-size: 14px;
	}

	.bread {
		margin-bottom: 20px;
		font-size: 14px;

		a {
			color: $color-primary;
		}

		@include respond-to(mb) {
			display: none;
		}
	}

	.productTitle {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-bottom: 20px;
		padding-right: 40px;

		@include respond-to(mb) {
			padding-right: 0;
		}

		h2 {
			font-size: 24px;
			font-weight: 700;
			text-transform: capitalize;

			b {
				color: #666666;
				cursor: pointer;
			}
		}
	}

	.content {
		display: grid;
		align-items: flex-start;
		grid-template-columns: 1fr 1fr;
		gap: 70px;

		@include respond-to(mb) {
			grid-template-columns: 1fr;
			gap: 0;
		}

		.left {
			min-width: 0;

			.myswiper1 {
				@include respond-to(mb) {
					padding: 10px;
				}

				.swiper-slide-thumb-active img {
					border: 1px solid #f96a00;
				}
			}

			.myswiper2 {
				height: 468px;
				margin-bottom: 20px;

				@include respond-to(mb) {
					height: 375px;
				}

				::v-deep video {
					object-fit: cover !important;
				}

				img {
					object-fit: contain !important;
				}
			}
		}

		.right {
			min-width: 0;

			@include respond-to(mb) {
				padding: 10px;
				h2 {
					font-size: 18px;
					font-weight: 700;
				}
			}

			.userInfo {
				position: relative;
				display: flex;
				align-items: center;
				width: 486px;
				height: 48px;
				background: linear-gradient(90deg, #ffaf81 0%, rgba(255, 255, 255, 0) 100%);
				border-radius: 0px 0px 0px 28px;
				padding-left: 80px;
				margin-bottom: 20px;

				@include respond-to(mb) {
					width: 80%;
					margin-bottom: 10px;
				}

				img {
					position: absolute;
					left: 20px;
					bottom: 0;
					max-width: 40px;
					max-height: 60px;
					border-radius: 50%;
				}
			}

			.price {
				margin-bottom: 20px;

				@include respond-to(mb) {
					margin-bottom: 10px;
				}

				strong {
					font-size: 28px;
					color: $color-price;
				}

				span {
					color: #666666;
				}
			}

			.params {
				@include respond-to(mb) {
					border-bottom: 1px solid #f4f4f4;
				}

				.paramsItem {
					display: flex;
					align-items: center;
					margin-bottom: 20px;

					@include respond-to(mb) {
						margin-bottom: 10px;
					}

					& > div:first-child {
						width: 120px;
						margin-right: 10px;
					}
				}
			}

			.btnGroup {
				display: flex;
				gap: 20px;

				@include respond-to(mb) {
					gap: 10px;
				}

				&.notFixed {
					position: static;
				}

				&.fixed {
					position: fixed;
					left: 0;
					right: 0;
					bottom: 0;
					padding: 10px;
					z-index: 10;
					background-color: #ffffff;
					box-shadow: 0px -1px 12px 0px rgba(0, 0, 0, 0.12);
				}

				button {
					width: 252px;
					height: 48px;
					background: #d24600;
					border-radius: 8px;
					color: #ffffff;

					&.buyNow {
						background: #0066cc;
					}

					&[disabled] {
						background-color: #cccccc;
					}

					@include respond-to(mb) {
						border-radius: 4px;
						font-size: 16px;
					}
				}
			}

			.freeTip {
				display: flex;
				align-items: center;
				margin: 30px 0 20px;
				color: #1a8055;

				img {
					width: 25px;
					margin-right: 4px;
					@include respond-to(mb) {
						width: 20px;
					}
				}

				@include respond-to(mb) {
					margin: 20px 0;
					font-size: 14px;
				}
			}

			.des {
				@include respond-to(mb) {
					padding: 10px;
					margin: 0 -10px -10px;
					background: #f4f4f4;
				}

				.des-con {
					margin-top: 16px;
					color: #666666;
					font-size: 14px;
					white-space: pre-wrap;
				}
			}
		}
	}

	.pdt-sub {
		position: absolute;
		left: 0;
		right: 0;
		bottom: 0;
		display: grid;
		grid-template-columns: repeat(4, minmax(200px, 300px));
		justify-content: center;
		color: #000000;
		padding: 10px 0;
		background: #ffffff;
		box-shadow: 0 -1px 10px 1px #f5f5f5;
		border-top: 1px solid #f6f6f6;
		transition: all 0.3s;
		z-index: 100;

		@include respond-to(mb) {
			display: none;
		}

		&.showSub {
			bottom: 0;
		}

		.pdt-sub-item {
			position: relative;
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			padding: 0 5px;

			.blackFont {
				color: #000000;
			}

			&:last-child {
				&::after {
					display: none;
				}
			}

			&:nth-of-type(5) {
				&::after {
					display: none;
				}
			}

			&::after {
				content: "";
				position: absolute;
				top: 50%;
				right: 0;
				transform: translateY(-50%);
				width: 1px;
				height: 18px;
				background-color: #cccccc;
			}

			span:first-child {
				margin-bottom: 5px;
				font-weight: 700;
			}
		}

		.pdt-sub-discount {
			span:last-child {
				color: #e6252e;
			}
		}

		.pdt-sub-total {
			&::after {
				display: none;
			}

			span:last-child {
				font-weight: bold;
				font-size: 1.125em;
			}
		}

		.pdt-sub-cart {
			flex: 1;
			margin: auto auto auto 10px;
			padding: 10px 15px;
			cursor: pointer;
			border-radius: 4px;
			font-weight: bold;
			color: #ffffff;
			background-color: #d24600;

			&[disabled] {
				background-color: #cccccc;
			}
		}
	}
}
</style>
