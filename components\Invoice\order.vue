<template>
	<div class="wrap">
		<div class="content" id="pdfDom">
			<div class="con content1">
				<div>
					<img v-if="invoicePrint.invoiceNo" class="img1" :src="[flag ? 'https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20221228/ueditor/16/666-446546465.png' : 'https://gs-jj-us-static.oss-accelerate.aliyuncs.com/tmp/photo/20221228/ueditor/16/1555.png']" />
				</div>
				<table class="printTable noBorder">
					<tr>
						<!-- <td colspan="33.3%" style="padding-left: 0">
							{{ invoicePrint.invoiceNo }}
						</td> -->
					</tr>
					<div class="mb-5"></div>
					<tr class="bgf3">
						<td width="33.3%" height="68px">
							<img style="height: 100%;" :src="invoicePrint.retailerLogPath" alt="" />
						</td>
						<td width="33.3%" height="68px" class="color"> <span v-if="invoicePrint.retailerPhone">Phone {{ invoicePrint.retailerPhone }}</span></td>

						<td width="33.3%" height="68px" class="color"> <span v-if="invoicePrint.retailerEmail">Email {{ invoicePrint.retailerEmail }}</span></td>
					</tr>
				</table>

				<table class="printTable">
					<tr>
						<td class="bgf2" style="font-weight:700 ;">From</td>
						<td>{{ invoicePrint.from }}</td>
						<td class="bgf2" style="font-weight:700 ;">To</td>
						<td style="width:53%">{{ invoicePrint.invTo }}</td>
					</tr>
					<tr>
						<td class="bgf2" style="font-weight:700 ;">Email</td>
						<td>{{ invoicePrint.email }}</td>
						<td class="bgf2" style="font-weight:700 ;">Attn</td>
						<td style="width:53%">{{ invoicePrint.invAttn }}</td>
					</tr>
				</table>

				<div class="small-title1">
					lNVOICE
				</div>
				<table class="printTable">
					<tr>
						<td class="bgf2" style="font-weight:700 ;">Invoice No</td>
						<td>{{ invoicePrint.invoiceNo }}</td>
						<td class="bgf2" style="font-weight:700 ;">Date</td>
						<td>{{ invoicePrint.date }}</td>
						<td class="bgf2" style="font-weight:700 ;">Payment Method</td>
						<td v-if="invoicePrint.invPayInfo">{{ invoicePrint.paymentMethod }}</td>
					</tr>
				</table>

				<table class="printTable">
					<tr>
						<td class="bgf2" style="font-weight:700 ;fontSize:13px;height:29px;width:121px;text-align: center;">
							No.
						</td>
						<td class="bgf2" style="font-weight:700 ;fontSize:13px;height:29px;width:120px;text-align: center;">
							Artwork
						</td>
						<td class="bgf2" style="font-weight:700 ;fontSize:13px;height:29px;width:62px">
							Item
						</td>
						<td class="bgf2" style="font-weight:700 ;fontSize:13px;height:29px;width:190px;text-align: center;">
							Description
						</td>
						<td class="bgf2" style="font-weight:700 ;fontSize:13px;height:29px;width:70px;padding: 11px;text-align: center;">
							Qty
						</td>
						<td class="bgf2" style="font-weight:700 ;fontSize:13px;height:29px;width:76px;padding: 10px;">
							Unit Price
						</td>
						<td class="bgf2" style="font-weight:700 ;fontSize:13px;height:29px;width:75px;padding: 13px;padding: 11px;">
							Mold Fee
						</td>
						<td class="bgf2" style="font-weight:700 ;fontSize:13px;height:29px;width:77px;padding: 11px">
							Extra Fee
						</td>
						<td class="bgf2" style="font-weight:700 ;fontSize:13px;height:29px;width:70px;padding: 7px">
							Amount
						</td>
					</tr>
					<tr v-for="(item, index) in productPrint.ordersProducts" :key="index">
						<td style="fontSize:12px;height:86px;width:58px">
							{{ item.productionNumber }}
						</td>
						<td style="fontSize:12px;height:86px;width:120px">
							<img style="height: 100px;" :src="item.invArtwork" alt="" />
						</td>
						<td style="fontSize:12px;height:86px;width:56px">
							{{ item.productName }}
						</td>
						<td style="fontSize:12px;height:86px;width:190px;white-space: pre-wrap;text-align: left;">
							{{ item.originProductParam }}
						</td>
						<td style="fontSize:12px;height:86px;width:70px">{{ item.quantity }}</td>
						<td style="fontSize:13px;height:86px;width:76px;" v-if="item.unitPrice">
							<p :style="item.discount==0?'color:black;margin-bottom:0;fontSize:12px;':'color:red'">${{ ((item.unitPrice * (100 - item.discount)) / 100).toFixed(2) }}</p>
							<p v-if="item.discount!==0" style="fontSize:12px;text-decoration: line-through">${{ item.unitPrice }}</p>
						</td>
						<td style="fontSize:12px;height:86px;width:75px">${{ item.mouldPrice }}</td>

						<td style="fontSize:12px;height:86px;width:77px">${{ item.extraPrice }}</td>
						<td style="fontSize:12px;height:86px;width:70px">
							<p :style="item.discount==0?'color:black;margin-bottom:0':'color:red;fontSize:13px'">${{ item.totalPrice }}</p>
							<p v-if="item.discount!==0" style="fontSize:12px;text-decoration: line-through">${{ (item.unitPrice * item.quantity + item.mouldPrice+item.extraPrice).toFixed(2) }}</p>
						</td>
					</tr>
					<tr>
						<td colspan="7" style="text-align: right;border-right: 1px solid rgba(255, 0, 0, 0);">
							<p style="border-right:0px" v-if="showDoc">Subtotal: </p>
							<p style="border-right:0px" v-if="productPrint.apoShippingPrice">Apo Shipping Fee: </p>

							<p v-if="productPrint.taxPrice > 0">Tax Price: </p>

							<p v-if="productPrint.ordersDiscount && productPrint.ordersDiscount.firstDiscountPrice > 0">First Order Code Disc({{ (productPrint.ordersDiscount.firstDiscount)*100 }}%): </p>

							<p v-if="productPrint.ordersDiscount && productPrint.ordersDiscount.crowdDiscountPrice > 0">Promo Code({{ (productPrint.ordersDiscount.crowdDiscount)*100 }}%): </p>

							<p v-if="productPrint.ordersDiscount && productPrint.ordersDiscount.pointPrice > 0">Points Deduct({{ productPrint.ordersDiscount.points }} Points):</p>

							<p v-if="productPrint.ordersDiscount && productPrint.ordersDiscount.voucherPrice > 0">Gift Vouchers:</p>

							<p v-if="productPrint.ordersDiscount && productPrint.ordersDiscount.couponDiscountPrice > 0">Coupon Discount
								<span v-if="productPrint.ordersDiscount.couponDiscount">({{(productPrint.ordersDiscount.couponDiscount)*100}}%)</span>
								:
							</p>

							<p v-if="productPrint.ordersDiscount && productPrint.ordersDiscount.orderDiscountPrice > 0">Order Discount({{ (productPrint.ordersDiscount.orderDiscount)*100 }}%):</p>

							<p v-if="productPrint.ordersDiscount && productPrint.ordersDiscount.promotionPrice > 0">Extra Discount({{ (productPrint.ordersDiscount.promotionDiscount)*100 }}%):</p>
							<!-- 新加 -->
							<p v-if="productPrint.childOrder&&productPrint.childOrder.length!==0||showDoc">{{one == 1?'Grand Total':'Amount Paid'}}:</p>
							<p style="font-weight: bold;color:black" v-if="productPrint.childOrder&&!productPrint.childOrder.length">Final Price:</p>

						</td>
						<td colspan="2" style="text-align: right;">
							<p v-if="showDoc">${{ productPrint.productPrice }}</p>
							<p v-if="productPrint.apoShippingPrice">+${{productPrint.apoShippingPrice}}</p>

							<p v-if="productPrint.taxPrice > 0">+${{ productPrint.taxPrice }}</p>

							<p v-if="productPrint.ordersDiscount && productPrint.ordersDiscount.firstDiscountPrice > 0">-${{ productPrint.ordersDiscount.firstDiscountPrice }} </p>

							<p v-if="productPrint.ordersDiscount && productPrint.ordersDiscount.crowdDiscountPrice > 0">-${{ productPrint.ordersDiscount.crowdDiscountPrice }}</p>

							<p v-if="productPrint.ordersDiscount && productPrint.ordersDiscount.pointPrice > 0">-${{ productPrint.ordersDiscount.pointPrice }}</p>

							<p v-if="productPrint.ordersDiscount && productPrint.ordersDiscount.voucherPrice > 0">-${{ productPrint.ordersDiscount.voucherPrice }}</p>

							<p v-if="productPrint.ordersDiscount && productPrint.ordersDiscount.couponDiscountPrice > 0">-${{ productPrint.ordersDiscount.couponDiscountPrice }}</p>

							<p v-if="productPrint.ordersDiscount && productPrint.ordersDiscount.orderDiscountPrice > 0">-${{ productPrint.ordersDiscount.orderDiscountPrice }}</p>

							<p v-if="productPrint.ordersDiscount && productPrint.ordersDiscount.promotionPrice > 0">-${{ productPrint.ordersDiscount.promotionPrice }}</p>
							<!-- 新加 -->
							<p v-if=" productPrint.childOrder&&productPrint.childOrder.length!==0||showDoc">${{productPrint.totalPrice}}</p>
							<p v-if="productPrint.childOrder&&!productPrint.childOrder.length"> <span style="text-decoration:line-through;font-size: 14px;" v-if="showDoc">${{ productPrint.productPrice }}</span>&nbsp;&nbsp;<span style="color:red;font-size: 16px;font-weight:700;">${{ productPrint.totalPrice }}</span></p>
						</td>
					</tr>

					<template v-for="(it, aindex) in productPrint.childOrder">
						<tr v-for="(item, index) in it.ordersProducts">
							<td style="fontSize:12px;height:86px;width:58px">
								{{ item.productionNumber }}
							</td>
							<td style="fontSize:12px;height:86px;width:120px">
								<img style="height: 100px;" :src="item.invArtwork" alt="" />
							</td>
							<td style="fontSize:12px;height:86px;width:56px">
								{{ item.productName }}
							</td>
							<td style="fontSize:12px;height:86px;width:190px;white-space: pre-wrap;text-align: left;">
								{{ item.originProductParam }}
							</td>
							<td style="fontSize:12px;height:86px;width:70px">{{ item.quantity }}</td>
							<td style="fontSize:13px;height:86px;width:76px;">
								<p v-if="item.unitPrice" :style="item.discount==0?'color:black;margin-bottom:0;fontSize:12px;':'color:red'">${{ ((item.unitPrice * (100 - item.discount)) / 100).toFixed(2) }}</p>
								<p v-if="item.discount!==0" style="fontSize:12px;text-decoration: line-through">${{ item.unitPrice }}</p>
							</td>
							<td style="fontSize:12px;height:86px;width:75px">${{ item.mouldPrice?item.mouldPrice:0 }} </td>

							<td style="fontSize:12px;height:86px;width:77px">${{ item.extraPrice }}</td>
							<td style="fontSize:12px;height:86px;width:70px">
								<p :style="item.discount==0?'color:black;margin-bottom:0':'color:red;fontSize:13px'">${{ item.totalPrice }}</p>
								<p v-if="item.discount!==0" style="fontSize:12px;text-decoration: line-through">${{ (item.unitPrice * item.quantity + item.mouldPrice+item.extraPrice).toFixed(2) }}</p>
							</td>
						</tr>

						<tr v-if="it.taxPrice">
							<td style="text-align: right;border-right: 1px solid rgba(255, 0, 0, 0);" colspan="7">Tax Price:</td>
							<td colspan="2" style="text-align: right;">
								<span>+${{ it.taxPrice }}</span>
							</td>
						</tr>
						<tr v-if="two">
							<td style="text-align: right;border-right: 1px solid rgba(255, 0, 0, 0);;" colspan="7" :style="it.paymentStatus == 2 && it.paymentConfirmation == 1?'':'font-size: 19px;font-family: Calibri;font-weight: bold;color: #000000;'">{{it.paymentStatus == 2 && it.paymentConfirmation == 1?'Amount Paid':'Balance Due'}}:</td>
							<td colspan="2" style="text-align: right;">
								<span :style="it.paymentStatus == 2 && it.paymentConfirmation == 1?'':'color:red;font-size: 21px;font-family: Calibri;font-weight: bold;'">${{it.totalPrice}}</span>
							</td>
						</tr>
						<tr v-if="it.totalPrice&&one">
							<td style="text-align: right;border-right: 1px solid rgba(255, 0, 0, 0);" colspan="7">Grand Total:</td>
							<td colspan="2" style="text-align: right;">
								<span>${{ it.totalPrice }}</span>
							</td>
						</tr>

						<!-- <template v-if="productPrint.childOrder&&productPrint.childOrder.length&&it.paymentStatus !== 2 && it.paymentConfirmation !== 1">

							<tr>
								<td style="text-align: right;border-right: 1px solid rgba(255, 0, 0, 0);font-size: 21px;font-family: Calibri;font-weight: bold;color: #000000;" colspan="7">{{one?'Final Total Price':'Balance Due'}}:</td>
								<td colspan="2" style="text-align: right;">
									<span style="color:red;font-size: 21px;font-family: Calibri;font-weight: bold;">${{ one?productPrint.orderPrice.toFixed(2):it.productPrice }}</span>
								</td>
							</tr>
						</template> -->

					</template>
					<template v-if="productPrint.childOrder&&productPrint.childOrder.length&&one">

						<tr>
							<td style="text-align: right;border-right: 1px solid rgba(255, 0, 0, 0);font-size: 19px;font-family: Calibri;font-weight: bold;color: #000000;" colspan="7">Final Total Price:</td>
							<td colspan="2" style="text-align: right;">
								<span style="color:red;font-size: 21px;font-family: Calibri;font-weight: bold;">${{ productPrint.orderPrice.toFixed(2) }}</span>
							</td>
						</tr>
					</template>

					<template>
						<!-- <tr v-if="productPrint.totalPrice !== productPrint.productPrice">
							<td colspan="7" style="text-align: right;border-right: 1px solid rgba(255, 0, 0, 0);border-top: 1px solid rgba(255, 0, 0, 0);font-weight: bold;color:black">Final Price:</td>
							<td colspan="2" style="text-align: right;">

							</td>
						</tr> -->
						<!-- <tr>
							<td colspan="7" class="border" style="text-align: right;border-right: 1px solid rgba(255, 0, 0, 0);">Amount Paid:</td>
							<td v-if="productPrint.totalPrice||productPrint.unPaidPrice" colspan="2" style="text-align: right;">${{ (productPrint.totalPrice - productPrint.unPaidPrice).toFixed(2) }}</td>
						</tr>
						<tr>
							<td colspan="7" style="text-align: right;border-right: 1px solid rgba(255, 0, 0, 0);">Balance Due:</td>
							<td colspan="2" style="text-align: right;">
								<span>${{ productPrint.productPrice }}</span><span style="color:red;font-size: 14px;font-weight:700;">${{ productPrint.unPaidPrice }}</span>
							</td>
						</tr> -->
					</template>
				</table>

				<table class="printTable">
					<tr border="1">
						<th width="14%" style="border: 0"></th>
						<th width="6%" style="border: 0"></th>
						<th width="10%" style="border: 0"></th>
						<th width="10%" style="border: 0"></th>
						<th width="10%" style="border: 0"></th>
						<th width="10%" style="border: 0"></th>
						<th width="9%" style="border: 0"></th>
						<th width="9%" style="border: 0"></th>
						<th width="9%" style="border: 0"></th>
						<th width="13%" style="border: 0"></th>
					</tr>
					<tr border="1">
						<td colspan="2" class="bgf2" style="font-weight:700 ;text-align: center; ">Estimated Ship Date</td>
						<td colspan="3">
							{{ invoicePrint.estimatedShipDate }}
						</td>
						<td colspan="1" rowspan="2" class="bgf2" style="font-weight:700 ;">Notes</td>
						<td colspan="5" rowspan="2">{{invoicePrint.notes }}
						</td>
					</tr>
					<tr class="vtop" border="1">
						<td colspan="2" class="bgf2" style="font-weight:700 ;text-align: center;line-height: 51px; ">Ship To</td>
						<td colspan="3" style="text-align: left;">
							<!-- {{ invoicePrint.address }} -->
							Name:{{ invoicePrint.name }} <br />
							{{ invoicePrint.isApoAddress === 1 ? 'APO/FPO Address:' : 'Address:' }} {{ invoicePrint.address }}<br />
							<!-- 94556, United States<br /> -->
							Phone:{{ invoicePrint.phone }}
						</td>
					</tr>
					<tr border="1">
						<td colspan="10" class="bgf2 text-center" style="font-weight:700 ;color:black">Payment Method</td>
					</tr>
					<tr>
						<td colspan="2" style=" text-align: center;" v-if="invoicePrint.invPayInfo">{{ invoicePrint.invPayInfo.payTypeName }}</td>
						<td colspan="8" style="text-align: left;" v-if="invoicePrint.invPayInfo">{{ invoicePrint.invPayInfo.paymentMethodName }}</td>
					</tr>
					<tr>
						<td colspan="2" style="text-align:center">Check</td>
						<td :colspan="check.length == 2 ? 4 : 8" v-if="check[0]" style=";white-space: pre-wrap;text-align: left;">{{check[0] }}
						</td>
						<td :colspan="check.length == 2 ? 4 : 0" style="white-space: pre-wrap;text-align: left;" v-if="check[1]">{{ check[1] }}</td>
					</tr>
					<tr></tr>
				</table>

			</div>
		</div>
	</div>
</template>
<script>
import { getInvoicePrintInfo, getInvoiceOrderInfo } from "@/api/manage/orderInvoice";
export default {
	data () {
		return {
			flag: false,
			invoicePrint: {},
			objList: {},
			shipToList: [],
			logoUrl: "",
			productPrint: {},
			nums: "",
			check: []
		};
	},
	computed: {
		showDoc () {
			let show = false
			if (this.productPrint.ordersDiscount) {
				if (this.productPrint.ordersDiscount.couponDiscountPrice > 0 || this.productPrint.ordersDiscount.crowdDiscountPrice > 0 || this.productPrint.ordersDiscount.firstDiscountPrice > 0 || this.productPrint.ordersDiscount.orderDiscountPrice > 0 || this.productPrint.ordersDiscount.pointPrice > 0 || this.productPrint.ordersDiscount.promotionPrice > 0 || this.productPrint.ordersDiscount.voucherPrice > 0) {
					return show = true
				} else {
					return show = false
				}
			}
			return show
		},
		one () {
			let result = false
			result = this.productPrint.childOrder.every((item) => {
				return item.paymentStatus == 2 && item.paymentConfirmation == 1
			})
			return result
		},
		two () {
			let flag = false
			let result = this.productPrint.childOrder.every((item) => {
				return item.paymentStatus == 2 && item.paymentConfirmation == 1
			})
			if (this.productPrint.paymentStatus == 2 && this.productPrint.paymentConfirmation == 1) {
				if (result == true) {
					flag = false
				} else {
					flag = true
				}
			}
			return flag
		},
		number () {
			return this.invoicePrint.invoiceNo + "_" + this.invoicePrint.invTo + "_$" + this.productPrint.totalPrice;
		},

	},
	beforeUpdate () {
		this.$nextTick(() => {
			document.title = "Inv" + "\xa0" + this.number;
		});
	},
	mounted () {
		getInvoiceOrderInfo(this.$route.query.id, this.$route.query.proId).then(res => {
			let result = res.data.childOrder.every((item) => {
				return item.paymentStatus == 2 && item.paymentConfirmation == 1
			})
			if (res.data.paymentStatus == 2 && res.data.paymentConfirmation == 1 && result == true) {
				this.flag = true;
			} else {
				this.flag = false;
			}
			let { data } = res;
			this.productPrint = data;
			this.productPrint.subTotal = this.productPrint.ordersProducts
				.reduce((pre, cur) => {
					return pre + cur.totalPrice;
				}, 0)
				.toFixed(2);
		});
		getInvoicePrintInfo(this.$route.query.id).then(res => {
			let { data } = res;
			this.invoicePrint = data;
			this.check = JSON.parse(res.data.invPayInfo.paymentMethodCheck.replace(/\n/g, "\\n").replace(/\r/g, "\\r"));
			// this.$nextTick(() => {
			//   setTimeout(() => {
			//     window.print();
			//   }, 1000);
			// });
		});
	}
};
</script>
<style lang="scss" scoped>
body {
	font-family: Calibri;
}
.wrap {
	font-size: 15px;

	overflow: hidden;
	.img1 {
		width: 47%;
		position: absolute;
		top: 27%;
		right: 6%;
		opacity: 0.5;
		z-index: 3;
	}
}

.content {
	//width: 910px;
	width: 770px;
	margin: 20px auto;
}

.con {
	width: 100%;
	font-family: Calibri;
	font-size: 15px;
	color: #525659;
	page-break-after: always;
	position: relative;
	z-index: 1;

	.printTable {
		width: 100%;
		border-collapse: collapse;
		border-color: #d9d9d9;

		tr th,
		tr td {
			border: 1px solid #d9d9d9;
			z-index: 1;
		}

		td {
			word-break: break-word;
			padding: 10px 5px;
			line-height: normal;
			vertical-align: middle;
			z-index: 1;
			text-align: center;
			border-right: none;
		}
		.td1 {
			word-break: break-word;
			padding: 5px 5px;
			line-height: normal;
			vertical-align: middle;
			z-index: 1;
			text-align: center;
		}

		.vtop,
		.vtop td {
			vertical-align: top;
			z-index: 1;
		}

		.bgf2 {
			background-color: #f2f2f2;
			position: relative;
			z-index: 1;
		}
		.bgf3 {
			background-color: #bbb;
			position: relative;
			z-index: 1;
		}
		.color {
			font-size: 18px;
			font-family: Calibri;
			font-weight: 400;
			color: #ffffff;
			padding-left: 75px;
			// line-height: 209px;
		}
		.border {
			border-right: none;
			border-bottom: none;
			text-align: right;
			font-family: Calibri;
			font-weight: 400;
			font-size: 14px;
		}
		.border1 {
			border-bottom: none;
			text-align: right;
		}
		.border2 {
			// border-bottom: none;
			text-align: right;
		}
		.border3 {
			border-right: none;
			text-align: right;
		}
		.Final {
			border-top: none !important;
		}
	}
	.printTable1 {
		width: 100%;
		border-collapse: collapse;
		border-color: #d9d9d9;
		// border-top: 1px solid #d9d9d9;
		// border-left: 1px solid #d9d9d9;
		// margin-bottom: 20px;
		// border-right: 1px solid #d9d9d9;

		tr th,
		tr td {
			// border-right: 1px solid #d9d9d9;
			// border-bottom: 1px solid #d9d9d9;
			z-index: 1;
			border: none;
		}

		td {
			border: none;
			word-break: break-word;
			padding: 5px 5px;
			line-height: normal;
			vertical-align: middle;
			z-index: 1;
			text-align: center;
		}
		.td1 {
			word-break: break-word;
			padding: 5px 5px;
			line-height: normal;
			vertical-align: middle;
			z-index: 1;
			text-align: center;
		}

		.vtop,
		.vtop td {
			vertical-align: top;
			z-index: 1;
		}

		.bgf2 {
			background-color: #f2f2f2;
			position: relative;
			z-index: 1;
		}
		.bgf3 {
			background-color: #bbb;
			position: relative;
			z-index: 1;
		}
		.color {
			font-size: 18px;
			font-family: Calibri;
			font-weight: 400;
			color: #ffffff;
			padding-left: 75px;
		}
		.border {
			border-right: none;
			border-bottom: none;
			text-align: right;
			font-family: Calibri;
			font-weight: 400;
			font-size: 14px;
		}
		.border1 {
			border-bottom: none;
			text-align: right;
		}
		.border2 {
			// border-bottom: none;
			text-align: right;
		}
		.border3 {
			border-right: none;
			text-align: right;
		}
	}

	.printTable.noBorder {
		border: none;

		tr th,
		tr td {
			border: none;
			word-break: break-word;
			padding: 5px 5px;
			line-height: normal;
			vertical-align: middle;
			z-index: 1;
			text-align: center;
		}
	}

	.small-title {
		height: 40px;
		line-height: 35px;
		text-align: center;
		// background: #f2f2f2;
		border: 1px solid #d9d9d9;
		// margin: 20px 0;
		font-weight: bold;
		z-index: 1;
		position: relative;
	}
	.small-title1 {
		line-height: 35px;
		text-align: center;
		// background: #f2f2f2;
		border: 1px solid #d9d9d9;
		// margin: 20px 0;
		font-weight: bold;
		z-index: 1;
		position: relative;
		font-weight: 700;
		font-size: 16px;
		color: black;
	}
}
</style>
