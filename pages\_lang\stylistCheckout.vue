<template>
    <v-app :disabled="isPaidOrder">
        <div class="bps-container py-2 py-lg-3 checkoutPage" id="mainContent">
            <div class="left-box">
                <div class="yourPosition py-3 mb-5 text-body-2">
                    {{ langCart.position }}
                </div>
                <div class="cusTitle text-uppercase border-bottom pb-2 pb-lg-4">
                    <nuxt-link to="/stylistCart"><b class="icon-Undo pointer mr-2" style="color: #b8b8b8"></b></nuxt-link>
                    {{ langCart.checkOut }}
                </div>
                <!--产品列表-->
                <div class="box cart-box custom-scrollbar" :style="isPopSite ? 'max-height: 750px;' : 'max-height: 315px;'">
                    <div v-for="(item, index) in selectCartList" :key="index">
                        <div class="cart-item2">
							<div class="cart-item">
                            <div class="item-p t1">
                                <div class="imgWrap">
                                    <CustomImage :url="item.product ? getPicPath(item.product.imgJson) : item.picPath" :controls="false"></CustomImage>
                                </div>
                            </div>
                            <div class="item-p t2">
                                {{ item.product ? item.product.productName : item.productName }}
                            </div>
                            <div class="item-p t4">
                                <div class="t">
                                    {{ langCart.quantity }}
                                </div>
                                <div class="c">
                                    {{ item.productNum ? item.productNum : item.quantity }}
                                </div>
                            </div>
                            <div class="item-p t5">
                                <div class="t">
                                    {{ langCart.subtotal }}
                                </div>
                                <div class="c">
									<div>
										<CCYRate :price="item.totalPrice" :ccy="currentCurrency"></CCYRate>
									</div>
                                </div>
                            </div>
							</div>
						</div>
                    </div>
                </div>
                <div class="box delivery-box">
                    <div class="box-title">{{ langCart.shippingDelivery }}</div>
                    <div class="mt-2 mt-lg-4 mb-lg-2">
                        <span style="color: #666666">{{ langCart.shippingMethod }}: </span>
                        <span>{{ langCart.fed }}</span>
                        <span style="color: #de3500" v-show="!isPopSite">({{ langCart.free }})</span>
                    </div>
                    <img :src="language === 'de' ? 'https://static-oss.gs-souvenir.com/web/quoteManage/20241008/%E5%BE%B7%E8%AF%AD%E7%89%88_2034087MjGw4.png' : 'https://static-oss.gs-souvenir.com/web/quoteManage/20241008/%E8%8B%B1%E8%AF%AD%E7%89%88_203408Kbm34y.png'" alt="" style="width: auto" />
                </div>
            </div>
            <div class="right-box custom-scrollbar" id="rightContent">
                <div>
                    <!--支付方式列表-->
                    <div class="box pay-box">
                        <div class="box-title">
                            {{ langCart.spm }}
                        </div>
                        <div class="my-2">
                            <!--支付方式列表-->
                            <v-radio-group dense row class="mt-0" :hide-details="true" v-model="paymentMethodId" @change="changePaymentMethod">
                                <!--支付方式列表-->
                                <template v-for="item in payTypeList">
                                    <v-radio :value="item.id" @change="payTypeId = item.payType.id">
                                        <template #label>
                                            <fieldset class="myFileset">
                                                <legend>{{ langCart[item.payType.payName] ? langCart[item.payType.payName] : item.payType.payName }}</legend>
                                                <img :src="item.payType.payImg" alt="" />
                                            </fieldset>
                                        </template>
                                    </v-radio>
                                </template>
                                <v-radio :value="0" v-if="isReputable === 2" @change="payTypeId = 0">
                                    <template #label>
                                        <fieldset class="myFileset">
                                            <legend>{{ langCart.opm }}</legend>
                                            <img src="https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20220809/20220809bzdH4kBR.png" />
                                        </fieldset>
                                    </template>
                                </v-radio>
                            </v-radio-group>
                        </div>
                        <!--延迟支付表单-->
                        <div class="payLater-box mb-2" v-show="payTypeId === 0">
                            <v-form ref="payLaterForm">
                                <v-autocomplete v-model="payLater" :rules="payLaterRules" :items="payLaterList" label="Province" hide-details="auto" dense solo flat outlined item-text="name" item-value="name"></v-autocomplete>
                            </v-form>
                        </div>
                    </div>
                    <!--非子订单或者已下单但是没地址-->
                    <div>
                        <!--fd网站隐藏地址-2024-00-02-->
                        <div class="box shipping-box mb-6" v-if="!hideAddress">
                            <div class="box-title">
                                {{ langCart.sa }}
                            </div>
                            <v-radio-group :disabled="!disabledAddress" class="my-2 pt-0" hide-details dense v-model="addressStatus" row>
                                <v-radio :label="langCart.shippingAddress" :value="0"></v-radio>
                                <v-radio :label="langCart.apoAddress" :value="1" v-show="!hideApoAddress"></v-radio>
                            </v-radio-group>
                            <div>
                                <template v-if="addressStatus === 0">
                                    <v-form ref="shippingAddressForm" :disabled="!disabledAddress">
                                        <v-row dense>
                                            <v-col cols="12" v-if="showEmailInput">
                                                <v-text-field v-model="shippingAddressForm.email" :rules="shippingAddressFormRules.email" :error-messages="emailErrorMessage" dense solo hide-details="auto" @change="isRegister">
                                                    <template #label><span class="xin">*</span>{{ langCart.email }} </template>
                                                </v-text-field>
                                            </v-col>
                                            <v-col cols="12" v-if="isLogin">
                                                <v-autocomplete autocomplete="new-password" v-model="userShippingAddress" :items="userShippingAddressList" dense solo :label="langCart.myAddress" hide-details="auto" item-value="id" @change="changeUserShippingAddress">
                                                    <template v-slot:selection="{ item }">
                                                        {{ item.customShowAddress }}
                                                    </template>
                                                    <template v-slot:item="{ item }">
                                                        {{ item.customShowAddress }}
                                                    </template>
                                                </v-autocomplete>
                                            </v-col>
                                            <v-col cols="6">
                                                <v-text-field v-model="shippingAddressForm.shippingFirstName" :rules="shippingAddressFormRules.shippingFirstName" dense solo hide-details="auto">
                                                    <template #label><span class="xin">*</span>{{ langCart.firstName }} </template>
                                                </v-text-field>
                                            </v-col>
                                            <v-col cols="6">
                                                <v-text-field v-model="shippingAddressForm.shippingLastName" :rules="shippingAddressFormRules.shippingLastName" dense solo hide-details="auto">
                                                    <template #label><span class="xin">*</span>{{ langCart.lastName }} </template>
                                                </v-text-field>
                                            </v-col>

                                            <v-col cols="12" class="d-flex align-items-center">
                                                <v-combobox v-model="shippingAddressForm.shippingAddressLine1" :loading="loadGoogleAddress" :error-messages="errorMessageObj.shippingLine1" :rules="shippingAddressFormRules.shippingAddressLine1" :items="predictions" item-text="mainTextCopy" item-value="mainText" :return-object="false" hide-no-data no-filter :search-input.sync="searchText" @update:search-input="debounceGetSpecificAddress" dense solo hide-details="auto" @change="setAddress">
                                                    <template #label>
                                                        <span class="xin">*</span>{{ langCart.addressLine1 }}
                                                        <span class="d-none d-md-inline-block">({{ langCart.example }})</span>
                                                    </template>
                                                    <template #item="{ item }">
                                                        <span v-html="item.mainTextCopy"></span>
                                                    </template>
                                                </v-combobox>
                                                <v-icon color="primary" class="ml-3 pointer" v-show="!shippingAddressLine2" @click="shippingAddressLine2 = true"> mdi-plus-circle-outline </v-icon>
                                            </v-col>
                                            <v-col cols="12" v-show="shippingAddressLine2">
                                                <v-text-field v-model="shippingAddressForm.shippingAddressLine2" :rules="shippingAddressFormRules.shippingAddressLine2" dense solo hide-details="auto">
                                                    <template #label>
                                                        {{ langCart.addressLine2 }}
                                                        <span class="d-none d-md-inline-block">({{ langCart.example }})</span>
                                                    </template>
                                                </v-text-field>
                                            </v-col>
                                            <v-col cols="12" class="d-md-none hint ml-1">
                                                {{ langCart.example }}
                                            </v-col>
                                            <v-col cols="12" sm="12" lg="6">
                                                <v-text-field v-model="shippingAddressForm.shippingCity" :error-messages="errorMessageObj.shippingCity" :rules="shippingAddressFormRules.shippingCity" dense solo label="*City" hide-details="auto">
                                                    <template #label><span class="xin">*</span>{{ langCart.city }} </template>
                                                </v-text-field>
                                            </v-col>
                                            <v-col cols="6" sm="6">
                                                <v-autocomplete autocomplete="new-password" :filter="filterName" v-model="shippingAddressForm.shippingCountryId" :error-messages="errorMessageObj.shippingCountry" :rules="shippingAddressFormRules.shippingCountryId" :items="countryList" dense solo hide-details="auto" item-text="countryName" item-value="id" @change="selectShippingCountry">
                                                    <template #label><span class="xin">*</span>{{ langCart.destination }} </template>
                                                </v-autocomplete>
                                            </v-col>
                                            <v-col cols="6" sm="6" lg="4" v-if="shippingStateList && shippingStateList.length > 0 && showshippingAddressDe">
                                                <v-autocomplete autocomplete="new-password" validate-on-blur :filter="filterName" v-model="shippingAddressForm.shippingStateId" :error-messages="errorMessageObj.shippingState" @change="changeShippingState" :rules="shippingAddressFormRules.shippingStateId" :items="shippingStateList" dense solo hide-details="auto" :item-text="formatShippingState" item-value="id">
                                                    <template #label><span class="xin">*</span>{{ langCart.state }} </template>
                                                </v-autocomplete>
                                            </v-col>
                                            <v-col cols="6" sm="6" lg="4" v-else-if="showshippingAddressDe && !shippingStateList.length">
                                                <v-text-field v-model="shippingAddressForm.shippingState" :error-messages="errorMessageObj.shippingState" :rules="shippingAddressFormRules.shippingState" dense solo hide-details="auto">
                                                    <template #label><span class="xin">*</span>{{ langCart.state }} </template>
                                                </v-text-field>
                                            </v-col>
                                            <v-col cols="6" sm="6" lg="4">
                                                <v-text-field v-model="shippingAddressForm.shippingZipCode" :error-messages="errorMessageObj.shippingCode" :rules="shippingAddressFormRules.shippingZipCode" dense solo hide-details="auto" @input="inputZipPostal">
                                                    <template #label><span class="xin">*</span>{{ langCart.zipCode }} </template>
                                                </v-text-field>
                                            </v-col>
                                            <v-col cols="6" sm="6" lg="4" flex class="bullNum">
                                                <div style="position: relative; width: 27%; height: 34px">
                                                    <img src="https://static-oss.gs-souvenir.com/web/quoteManage/20231228/image-removebg-preview_24_20634Zmnah.png" />
                                                    <v-text-field v-model="shippingPrefixNumber" :rules="shippingAddressFormRules.shippingPrefixNumber" dense solo hide-details="auto" :prefix="'+'" class="bill">
                                                        <template #label><span class="xin">*</span></template>
                                                    </v-text-field>
                                                </div>
                                                <v-text-field v-model="shippingAddressForm.shippingPhoneNumber" :rules="shippingAddressFormRules.shippingPhoneNumber" dense solo hide-details="auto" class="bill2 sms">
                                                    <!-- <template #label><span class="xin">*</span>{{ langCart.phoneNumber }} </template> -->
                                                    <template #label>
                                                        <span class="xin">*</span><span style="font-size: 12px">{{ langCart.EnterNumber }}</span></template
                                                    >
                                                </v-text-field>
                                                <div class="SMStextPC" v-show="showSMStext === true">
                                                    <v-checkbox v-model="isSMSCheckbox" :disabled="false"></v-checkbox>
                                                    {{ langCart.SMSText }}
                                                </div>
                                            </v-col>
                                            <v-col cols="12" class="SMStextMb">
                                                <div class="SMStextMb" v-show="showSMStext === true">
                                                    <v-checkbox v-model="isSMSCheckbox"></v-checkbox>
                                                    {{ langCart.SMSText }}

                                                </div>
                                            </v-col>

                                            <!-- pop网站不显示 -->
                                            <v-col cols="12" v-if="!isPopSite">
                                                <v-checkbox style="flex-grow: 0; display: inline-block" class="mt-0" v-model="saveAddress" :label="langCart.saveAddress" hide-details :true-value="1" :false-value="0"></v-checkbox>
                                            </v-col>
                                        </v-row>
                                    </v-form>
                                </template>
                                <template v-else-if="addressStatus === 1">
                                    <v-form ref="shippingAddressForm" :disabled="!disabledAddress">
                                        <v-row dense>
                                            <v-col cols="12" v-if="showEmailInput">
                                                <v-text-field v-model="shippingAddressForm.email" :rules="shippingAddressFormRules.email" :error-messages="emailErrorMessage" dense solo hide-details="auto" @change="isRegister">
                                                    <template #label><span class="xin">*</span>{{ langCart.email }} </template>
                                                </v-text-field>
                                            </v-col>
                                            <v-col cols="12" v-if="isLogin">
                                                <v-autocomplete v-model="userApoAddress" autocomplete="new-password" :items="userApoAddressList" dense solo :label="langCart.myAddress" hide-details="auto" item-value="id" @change="changeUserApoAddress">
                                                    <template v-slot:selection="{ item }">
                                                        {{ item.customShowAddress }}
                                                    </template>
                                                    <template v-slot:item="{ item }">
                                                        {{ item.customShowAddress }}
                                                    </template>
                                                </v-autocomplete>
                                            </v-col>
                                            <v-col cols="6">
                                                <v-text-field v-model="shippingAddressForm.shippingFirstName" :rules="shippingAddressFormRules.shippingFirstName" dense solo hide-details="auto">
                                                    <template #label><span class="xin">*</span>{{ langCart.firstName }} </template>
                                                </v-text-field>
                                            </v-col>
                                            <v-col cols="6">
                                                <v-text-field v-model="shippingAddressForm.shippingLastName" :rules="shippingAddressFormRules.shippingLastName" dense solo hide-details="auto">
                                                    <template #label><span class="xin">*</span>{{ langCart.lastName }} </template>
                                                </v-text-field>
                                            </v-col>
                                            <v-col cols="12">
                                                <v-text-field v-model="shippingAddressForm.apoAddress" :rules="shippingAddressFormRules.apoAddressRules" @change="changeApoAddress" dense solo hide-details="auto">
                                                    <template #label><span class="xin">*</span>{{ langCart.apoAddress2 }} </template>
                                                </v-text-field>
                                            </v-col>
                                            <v-col sm="6" lg="4" cols="6" flex class="bullNum bullNum2">
                                                <div style="position: relative; width: 27%; height: 34px">
                                                    <img src="https://static-oss.gs-souvenir.com/web/quoteManage/20231228/image-removebg-preview_24_20634Zmnah.png" />
                                                    <v-text-field v-model="shippingPrefixNumber" :rules="shippingAddressFormRules.shippingPrefixNumber" dense solo hide-details="auto" :prefix="'+'" class="bill3">
                                                        <template #label><span class="xin">*</span></template>
                                                    </v-text-field>
                                                </div>

                                                <v-text-field v-model="shippingAddressForm.shippingPhoneNumber" :rules="shippingAddressFormRules.shippingPhoneNumber" dense solo hide-details="auto" class="bill2">
                                                    <template #label><span class="xin">*</span>{{ langCart.phoneNumber }} </template>
                                                </v-text-field>
                                            </v-col>
                                            <v-col sm="6" lg="4" cols="6">
                                                <v-autocomplete autocomplete="new-password" :filter="filterName" v-model="shippingAddressForm.shippingApoCity" :error-messages="errorMessageObj.shippingApoCity" :rules="shippingAddressFormRules.shippingApoCity" :items="APOCityList" dense solo hide-details="auto" item-text="name" item-value="name">
                                                    <template #label><span class="xin">*</span>{{ langCart.city }} </template>
                                                </v-autocomplete>
                                            </v-col>
                                            <v-col sm="12" lg="4">
                                                <v-autocomplete autocomplete="new-password" validate-on-blur :filter="filterName" v-model="shippingAddressForm.shippingApoState" :error-messages="errorMessageObj.shippingApoState" :rules="shippingAddressFormRules.shippingApoState" :items="APOStateList" dense solo hide-details="auto" item-text="name" item-value="name">
                                                    <template #label><span class="xin">*</span>{{ langCart.state }} </template>
                                                </v-autocomplete>
                                            </v-col>
                                            <v-col sm="6" lg="6">
                                                <v-text-field v-model="shippingAddressForm.shippingApoZip" :error-messages="errorMessageObj.shippingApoZip" :rules="shippingAddressFormRules.shippingApoZip" dense solo hide-details="auto">
                                                    <template #label><span class="xin">*</span>{{ langCart.zipCode }} </template>
                                                </v-text-field>
                                            </v-col>

                                            <v-col cols="12" v-if="!isPopSite">
                                                <v-checkbox class="mt-0" v-model="saveAddress" :label="langCart.saveAddress" hide-details :true-value="1" :false-value="0"></v-checkbox>
                                            </v-col>
                                        </v-row>
                                    </v-form>
                                </template>
                            </div>
                        </div>
                        <div class="box billing-box mb-3 pb-4" v-if="payTypeId !== 10000 && !isPopSite && !hideAddress">
                            <div class="box-title">
                                {{ langCart.billAddress }}
                            </div>
                            <v-radio-group :disabled="!disabledAddress" class="my-2 pt-0" hide-details dense v-model="billingAddressStatus" @change="changeAddressStatus" row>
                                <v-radio :label="langCart.t1" :value="0"></v-radio>
                                <v-radio :label="langCart.t2" :value="1"></v-radio>
                            </v-radio-group>
                            <div v-if="billingAddressStatus === 1">
                                <v-form ref="billAddressForm" :disabled="!disabledAddress">
                                    <v-row dense>
                                        <v-col cols="6">
                                            <v-text-field v-model="billingAddressForm.billFirstName" :rules="billingAddressFormRules.billFirstName" dense solo hide-details="auto">
                                                <template #label><span class="xin">*</span>{{ langCart.firstName }} </template>
                                            </v-text-field>
                                        </v-col>
                                        <v-col cols="6">
                                            <v-text-field v-model="billingAddressForm.billLastName" :rules="billingAddressFormRules.billLastName" dense solo hide-details="auto">
                                                <template #label><span class="xin">*</span>{{ langCart.lastName }} </template>
                                            </v-text-field>
                                        </v-col>
                                        <v-col cols="12" class="d-flex align-items-center">
                                            <v-combobox v-model="billingAddressForm.billAddressLine1" :error-messages="errorMessageObj.billingLine1" :loading="loadGoogleAddress2" :rules="billingAddressFormRules.billAddressLine1" :items="predictions2" item-text="mainTextCopy2" item-value="mainText" :return-object="false" :search-input.sync="searchText2" @update:search-input="debounceGetSpecificAddress2" hide-no-data no-filter dense solo hide-details="auto" @change="setAddress2">
                                                <template #label>
                                                    <span class="xin">*</span>{{ langCart.addressLine1 }}
                                                    <span class="d-none d-md-inline-block">({{ langCart.example }})</span>
                                                </template>
                                                <template #item="{ item }">
                                                    <span v-html="item.mainTextCopy2"></span>
                                                </template>
                                            </v-combobox>

                                            <v-icon color="primary" class="ml-3 pointer" v-show="!billingAddressLine2" @click="billingAddressLine2 = true"> mdi-plus-circle-outline </v-icon>
                                        </v-col>
                                        <v-col cols="12" v-show="billingAddressLine2">
                                            <v-text-field v-model="billingAddressForm.billAddressLine2" :rules="billingAddressFormRules.billAddressLine2" dense solo hide-details="auto">
                                                <template #label>
                                                    {{ langCart.addressLine2 }} <span class="d-none d-md-inline-block">({{ langCart.example }})</span>
                                                </template>
                                            </v-text-field>
                                        </v-col>
                                        <v-col cols="12" class="d-md-none hint ml-1">
                                            {{ langCart.example }}
                                        </v-col>
                                        <v-col cols="12" sm="12" lg="6">
                                            <v-text-field v-model="billingAddressForm.billCity" :error-messages="errorMessageObj.billingCity" :rules="billingAddressFormRules.billCity" dense solo hide-details="auto">
                                                <template #label><span class="xin">*</span>{{ langCart.city }} </template>
                                            </v-text-field>
                                        </v-col>
                                        <v-col cols="6" sm="6">
                                            <v-autocomplete autocomplete="new-password" :filter="filterName" v-model="billingAddressForm.billCountryId" :error-messages="errorMessageObj.billingCountry" :rules="billingAddressFormRules.billCountryId" :items="countryList" dense solo hide-details="auto" item-text="countryName" item-value="id" @change="selectBillingCountry">
                                                <template #label><span class="xin">*</span>{{ langCart.destination }} </template>
                                            </v-autocomplete>
                                        </v-col>
                                        <v-col cols="6" sm="6" lg="4" v-if="billStateList && billStateList.length && showBillingAddressDe">
                                            <v-autocomplete autocomplete="new-password" validate-on-blur :filter="filterName" v-model="billingAddressForm.billStateId" :error-messages="errorMessageObj.billingState" :rules="billingAddressFormRules.billStateId" :items="billStateList" dense solo hide-details="auto" :item-text="formatbillState" item-value="id">
                                                <template #label><span class="xin">*</span>{{ langCart.state }} </template>
                                            </v-autocomplete>
                                        </v-col>
                                        <v-col cols="6" sm="6" lg="4" v-else-if="showBillingAddressDe && !billStateList.length">
                                            <v-text-field v-model="billingAddressForm.billState" :rules="billingAddressFormRules.billState" :error-messages="errorMessageObj.billingState" dense solo hide-details="auto">
                                                <template #label><span class="xin">*</span>{{ langCart.state }} </template>
                                            </v-text-field>
                                        </v-col>
                                        <v-col cols="6" sm="6" lg="4">
                                            <v-text-field v-model="billingAddressForm.billZipCode" :error-messages="errorMessageObj.billingCode" :rules="billingAddressFormRules.billZipCode" dense solo hide-details="auto">
                                                <template #label><span class="xin">*</span>{{ langCart.zipCode }} </template>
                                            </v-text-field>
                                        </v-col>
                                        <v-col cols="6" sm="6" lg="4" flex class="bullNum">
                                            <div style="position: relative; width: 27%; height: 34px">
                                                <img src="https://static-oss.gs-souvenir.com/web/quoteManage/20231228/image-removebg-preview_24_20634Zmnah.png" />
                                                <v-text-field v-model="billingPrefixNumber" :rules="billingAddressFormRules.billingPrefixNumber" dense solo hide-details="auto" :prefix="'+'" class="bill">
                                                    <template #label><span class="xin">*</span></template>
                                                </v-text-field>
                                            </div>
                                            <v-text-field v-model="billingAddressForm.billPhoneNumber" :rules="billingAddressFormRules.billPhoneNumber" dense solo hide-details="auto" class="bill2">
                                                <template #label><span class="xin">*</span>{{ langCart.phoneNumber }} </template>
                                            </v-text-field>
                                        </v-col>
                                    </v-row>
                                </v-form>
                            </div>
                        </div>
                    </div>
                    <div class="box delivery-box">
                        <div class="box-title">{{ langCart.shippingDelivery }}</div>
                        <div class="mt-2 mt-lg-4 mb-lg-2">
                            <span style="color: #666666">{{ langCart.shippingMethod }}: </span>
                            <span>{{ langCart.fed }}</span>
                            <span style="color: #de3500">({{ langCart.free }})</span>
                        </div>
                        <img :src="language === 'de' ? 'https://static-oss.gs-souvenir.com/web/quoteManage/20241008/%E5%BE%B7%E8%AF%AD%E7%89%88_2034087MjGw4.png' : 'https://static-oss.gs-souvenir.com/web/quoteManage/20241008/%E8%8B%B1%E8%AF%AD%E7%89%88_203408Kbm34y.png'" alt="" style="width: auto;max-width:100%;" />
                    </div>
                    <div class="subtotal-con">
                        <div class="subtotal">
                            <div class="sub-item subtotalPrice">
                                <div class="label">
                                    {{ langCart.subtotal }}
                                </div>
                                <div class="con">
                                    <CCYRate :price="totalPrice" :ccy="currentCurrency"></CCYRate>
                                </div>
                            </div>
                            <div class="sub-item">
                                <div class="label">{{ langCart.shipping }}</div>
                                <div class="con" style="color: #29bc75">{{ langCart.free }}</div>
                            </div>
                        </div>
                        <div id="messages" role="alert"></div>
                    </div>
                </div>
                <div class="payBtnWrap text-center mt-2" :disabled="load">
                    <div class="b1 d-flex align-center">
                        <!--待支付价格-->
                        <div class="label font-weight-bold">{{ langCart.total }}:</div>
                        <div class="d-flex align-baseline">
                            <div class="symbol">
                                <strong>{{ symbolCode }}</strong>
                            </div>
                            <div class="unPaidPrice">
                                <CCYRate :price="totalPrice" :ccy="currentCurrency"></CCYRate>
                            </div>
                        </div>
                    </div>
                    <div class="b2 d-flex align-center px-2">
                        <div v-if="!isFdSite">
                            <button v-ripple id="payment-button" class="checkoutBtn" @click="submitOrderBefore">
                                {{ langCart.pay }}
                            </button>
                        </div>
                        <div class="fd-btn" v-else>
                            <v-btn color="primary" @click="submitOrder" v-show="payTypeId !== 10000 || isZeroOrder"> PAY NOW </v-btn>
                            <!--paypal支付按钮-->
                            <div id="paypal-button-container" v-show="payTypeId === 10000 && !isZeroOrder"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <v-dialog style="z-index: 1000" max-width="600" persistent v-model="stripeStatus" class="pa-5">
            <div style="background: white" class="d-flex justify-center align-center">
                <form id="payment-form" class="stripeForm">
                    <div id="payment-element"></div>
                    <div class="btnWrap">
                        <button type="button" class="stripeButton cancelBtn" @click.stop="toOrderDetail">
                            <div class="spinner hidden"></div>
                            <span>{{ langCart.cancel }}</span>
                        </button>
                        <button id="submit" class="stripeButton">
                            <div class="spinner hidden" id="spinner"></div>
                            <span id="button-text">{{ langCart.payNow }}</span>
                        </button>
                    </div>
                </form>
            </div>
        </v-dialog>

        <v-dialog style="z-index: 1000" max-width="600" v-model="notFreeShipping" class="pa-5">
            <div style="background: white; position: relative" class="d-flex justify-center align-center pa-8">
                <span style="position: absolute; top: 10px; right: 10px; cursor: pointer"><v-icon class="close-icon" @click="notFreeShipping = false">mdi-close</v-icon></span>
                {{ langCart.noFreeShippingTips }}
            </div>
        </v-dialog>

        <v-dialog style="z-index: 1000" eager transition="dialog-top-transition" max-width="auto" width="auto" content-class="payBeforeDialog" v-model="payBeforeDialog">
            <v-card>
                <v-card-title> Confirm Your Address</v-card-title>
                <v-divider></v-divider>
                <v-card-text>
                    <div class="py-2 mb-4 small-title">Your order will be shipped to this address.</div>
                    <div class="addressInfo" v-if="addressStatus == 0">
                        <div class="addressItem custom-row mb-2">
                            <div class="item">
                                <span>{{ langCart.firstName }}:</span>
                                <span>{{ shippingAddressForm.shippingFirstName }}</span>
                            </div>
                            <div class="item">
                                <span>{{ langCart.lastName }}:</span>
                                <span>{{ shippingAddressForm.shippingLastName }}</span>
                            </div>
                        </div>
                        <div class="addressItem addressItem">
                            <div class="item mb-2">
                                <span>{{ langCart.addressLine1 }}:</span>
                                <span>{{ shippingAddressForm.shippingAddressLine1 }}</span>
                            </div>
                        </div>
                        <div class="addressItem">
                            <div class="item mb-2" v-show="shippingAddressForm.shippingAddressLine2">
                                <span>{{ langCart.addressLine2 }}:</span>
                                <span>{{ shippingAddressForm.shippingAddressLine2 }}</span>
                            </div>
                        </div>
                        <div class="addressItem custom-row mb-2">
                            <div class="item">
                                <span>{{ langCart.city }}:</span>
                                <span>{{ shippingAddressForm.shippingCity }}</span>
                            </div>
                            <div class="item">
                                <span>{{ langCart.destination }}:</span>
                                <span>{{ currentShippingCountry.countryName }}</span>
                            </div>
                        </div>
                        <div class="addressItem custom-row2">
                            <div class="item mb-2" v-if="(shippingStateList && shippingStateList.length > 0 && showshippingAddressDe) || (showshippingAddressDe && !shippingStateList.length)">
                                <span>{{ langCart.state }}:</span>
                                <span>{{ currentShippingState?.stateName || shippingAddressForm.shippingState }}</span>
                            </div>
                            <div class="item">
                                <span>{{ langCart.zipCode }}:</span>
                                <span>{{ shippingAddressForm.shippingZipCode }}</span>
                            </div>
                            <div class="item">
                                <span>{{ langCart.phoneNumber }}:</span>
                                <span>{{ shippingPrefixNumber }}-{{ shippingAddressForm.shippingPhoneNumber }}</span>
                            </div>
                        </div>
                        <div class="addressItem custom-row2" v-if="isFdSite">
                            <div class="item">
                                <span>Shipping Company:</span>
                                <span>{{ shippingAddressForm.shippingCompany }}</span>
                            </div>
                        </div>
                    </div>
                    <div class="addressInfo" v-if="addressStatus == 1">
                        <div class="addressItem custom-row mb-2">
                            <div class="item">
                                <span>{{ langCart.firstName }}:</span>
                                <span>{{ shippingAddressForm.shippingFirstName }}</span>
                            </div>
                            <div class="item">
                                <span>{{ langCart.lastName }}:</span>
                                <span>{{ shippingAddressForm.shippingLastName }}</span>
                            </div>
                        </div>
                        <div class="addressItem">
                            <div class="item mb-2">
                                <span>{{ langCart.apoAddress2 }}:</span>
                                <span>{{ shippingAddressForm.apoAddress }}</span>
                            </div>
                        </div>
                        <div class="addressItem mb-2 custom-row2">
                            <div class="item">
                                <span>{{ langCart.phoneNumber }}:</span>
                                <span>{{ shippingPrefixNumber }}-{{ shippingAddressForm.shippingPhoneNumber }}</span>
                            </div>
                            <div class="item">
                                <span>{{ langCart.city }}:</span>
                                <span>{{ shippingAddressForm.shippingApoCity }}</span>
                            </div>
                            <div class="item">
                                <span>{{ langCart.state }}:</span>
                                <span>{{ shippingAddressForm.shippingApoState }}</span>
                            </div>
                        </div>
                        <div class="addressItem mb-2">
                            <div class="item">
                                <span>{{ langCart.zipCode }}:</span>
                                <span>{{ shippingAddressForm.shippingApoZip }}</span>
                            </div>
                        </div>
                    </div>
                </v-card-text>
                <v-card-actions class="foot">
                    <v-btn outlined @click="cancelPay()"> Back to Edit Address</v-btn>
                    <v-btn color="primary" @click="submitOrder" v-show="payTypeId !== 10000 || isZeroOrder"> Confirm Address and Go to Pay </v-btn>
                    <!--paypal支付按钮-->
                    <div id="paypal-button-container" v-show="payTypeId === 10000 && !isZeroOrder"></div>
                </v-card-actions>
            </v-card>
        </v-dialog>

        <v-dialog style="z-index: 1000" max-width="500" v-model="isBuyAgainLowTips" class="pa-5">
            <div style="background: white; position: relative" class="d-flex justify-center align-center pa-8">
                <span style="position: absolute; top: 10px; right: 10px; cursor: pointer"><v-icon class="close-icon" @click="isBuyAgainLowTips = false">mdi-close</v-icon></span>
                <span>{{ langCart.minAmountTips }}&nbsp;</span>
                <CCYRate :price="lowestPrice" :ccy="currentCurrency"></CCYRate>
            </div>
        </v-dialog>
    </v-app>
</template>

<script>
import {
    stylistCheckout,
    stylistCheckoutNotLogin,
    stylistAddOrder,
    stylistAddOrderNotLogin,
    stylistGetOrderInfo,
    stylistGetOrderInfoNotLogin,
    getAllShoppingCart,
    stylistAutoVerifyPayment,
    updatePaymentMethodId,
    stylistEditOrderAddress
} from "@/api/stylistCart"

import {
    googleVerifyAddress,
    childPaymentIntent,
    getAccountId,
    getAddressList,
    getCountryList,
    getLowestPrice,
    getPayTypeList,
    getStateList,
    getStripeKey,
    isOldUser,
    orderPayFail,
    paymentIntent,
    getSmsSubscriptionByMail,
    getSmsSubscriptionState,
    paymentErrorLogs
} from "@/api/web";
import * as paypalApi from "@/assets/js/paypal-api.js";
import { loadScript } from "@paypal/paypal-js";
import { getGoogleAddress, getGoogleAddressDetail, getUserReferPointsInfo, getUserUsableVoucherList } from "@/api/coupon";
import { debounce, deepClone, round2, withRetryAsync,getDate } from "@/utils/utils";
import { loadStripe } from "@stripe/stripe-js";

export default {
    data() {
        return {
			// giveawayItem:{},
            aId:'',
            orderType:"", //1. 直接订单 2.链接订单
            triggerInput:false,
            showCouponAll:true,

            showPrice:false,
            // ordersPromotionDiscounts:[],
            showSMStext:false,//默认不显示短信营销提示文案
            isSMSCheckbox:false,//是否订阅短信

            isNewCoupon: null, //是否是新优惠卷

            markupPercentage: null,
            freightCharge: null,
            addFeePercentage: null,

            couponApplyType: null,

            APOCityList: [
                {
                    id: 0,
                    name: "APO(Army Post Office)",
                },
                {
                    id: 1,
                    name: "FPO(Fleet Post Office)",
                },
                {
                    id: 2,
                    name: "DPO(Diplomatic Post Office)",
                },
            ],
            APOStateList: [
                {
                    id: 0,
                    name: "AA(Armed Forces Americas)",
                },
                {
                    id: 1,
                    name: "AE(Armed Forces Europe)",
                },
                {
                    id: 2,
                    name: "AP(Armed Forces Pacific)",
                },
            ],

            // currentTaxRate: 0,
            VATtext: false,
            couponType: null,
            couponJson: "",
            couponJsonDiscountPrice:null,
            showcouponJsonDiscountPrice:true,
            couponName: "",
            showcoupon: false,
            couponList: [],
            payBeforeDialog: false,
            loadGoogleAddress: false,
            loadGoogleAddress2: false,
            taxName1: "",
            lowestPrice: null,
            shippingPrefixNumber: "",
            billingPrefixNumber: "",
            maxRetries: 3,
            retryCount: 0,
            unfreeCountryList: [],
            clearState: true,
            searchText: null,
            searchText2: null,
            showProgress: false,
            stripeAccountId: "",
            billingAddressLine2: false,
            shippingAddressLine2: false,
            radioGroup: [],
            paymentRequest: undefined,
            load: false,
            debounceCalcPrice: null,
            // debounceEditOrderDiscount: null,
            showCardBtn1: false,
            showCardBtn2: false,
            stripeApplePay: false,
            stripeGooglePay: false,
            showCode: false,
            userShippingAddress: "",
            userApoAddress: "",
            userShippingAddressList: [],
            userApoAddressList: [],
            payLaterList: ["Cash on delivery", "Payment by instalments", "Payment by cc", "Net 30 days", "Cash"],
            payLater: "Cash on delivery",
            payLaterRules: [(v) => !!v || "Payment Methods is required"],
            noLoginEmailRules: [
                (v) => {
                    if (!v) {
                        return "Email is required";
                    } else {
                        this.emailErrorMessage = "";
                        return true;
                    }
                },
                (v) => {
                    if (!/.+@.+\..+/.test(v)) {
                        return "E-mail must be valid";
                    } else {
                        this.emailErrorMessage = "";
                        return true;
                    }
                },
            ],
            countryList: [],
            shippingStateList: [],
            billStateList: [],
            billingAddressStatus: 0,
            addressStatus: 0,
            saveAddress: 0,
            shippingAddressForm: {
                shippingFirstName: "",
                shippingLastName: "",
                shippingAddressLine1: "",
                shippingAddressLine2: "",
                shippingCountryCode: "",
                shippingPhoneNumber: "",
                shippingCity: "",
                shippingState: "",
                shippingStateId: "",
                shippingCountry: "",
                shippingCountryId: "",
                shippingZipCode: "",
                apoAddress: "",
                email: "",
                shippingApoCity: "",
                shippingApoState: "",
                shippingApoZip: "",
                shippingCompany: "",
            },
            shippingAddressFormRules: {
                shippingFirstName: [(v) => !!v || "First name is required"],
                shippingLastName: [(v) => !!v || "Last name is required"],
                shippingAddressLine1: [
                    (v) => !!v || "Address is required",
                    (v) => !v || v.length <= 250 || "Max 250 characters",
                    (v) =>
                            (() => {
                                let pobox = ["p.o.box", "p.o box", "po.box", "p o box", "po box", "pobox","p.o. box"];
                                let isPobox = false;
                                if (!v) {
                                    return false;
                                }
                                pobox.map((i) => {
                                    if (v.toLowerCase().includes(i)) isPobox = true;
                                });
                                return !isPobox;
                            })() || "P.O.Box is unavailable",
                ],
                shippingAddressLine2: [(v) => !v || v.length <= 250 || "Max 250 characters"],
                shippingPhoneNumber: [(v) => !!v || "Phone is required", (v) => /^\d+$/.test(v) || "Phone must contain only digits"],
                shippingCity: [(v) => !!v || "City is required"],
                // shippingApoCity: [(v) => !!v || "City is required"],
                shippingStateId: [
                    (v) => {
                        if (!this.shippingStateList.length) {
                            return true;
                        } else {
                            return !!v || "Province is required";
                        }
                    },
                ],
                shippingState: [
                    (v) => {
                        if (this.shippingStateList.length) {
                            return true;
                        } else {
                            return !!v || "Province is required";
                        }
                    },
                ],
                shippingApoState: [
                    (v) => {
                        if (this.APOStateList.length) {
                            return true;
                        } else {
                            return !!v || "Province is required";
                        }
                    },
                ],
                shippingApoCity: [
                    (v) => {
                        if (this.APOCityList.length) {
                            return true;
                        } else {
                            return !!v || "City is required";
                        }
                    },
                ],
                shippingCountryId: [(v) => !!v || "Country is required"],
                shippingZipCode: [(v) => !!v || "Zip code is required"],
                shippingApoZip: [(v) => !!v || "Zip code is required"],
                apoAddressRules: [(v) => !!v || "APO address required"],
                email: [
                    (v) => {
                        if (!v) {
                            return "Email is required";
                        } else {
                            this.emailErrorMessage = "";
                            return true;
                        }
                    },
                    (v) => {
                        if (!/.+@.+\..+/.test(v)) {
                            return "E-mail must be valid";
                        } else {
                            this.emailErrorMessage = "";
                            return true;
                        }
                    },
                ],
                shippingPrefixNumber: [(v) => !!v || "", (v) => /^\d+$/.test(v) || ""],
            },
            billingAddressForm: {
                billFirstName: "",
                billLastName: "",
                billAddressLine1: "",
                billAddressLine2: "",
                billCountryCode: "",
                billPhoneNumber: "",
                billCity: "",
                billState: "",
                billStateId: "",
                billCountry: "",
                billCountryId: "",
                billZipCode: "",
            },
            billingAddressFormRules: {
                billFirstName: [(v) => !!v || "First name is required"],
                billLastName: [(v) => !!v || "Last name is required"],
                billAddressLine1: [(v) => !!v || "Address is required", (v) => !v || v.length <= 250 || "Max 250 characters"],
                billAddressLine2: [(v) => !v || v.length <= 250 || "Max 250 characters"],
                billPhoneNumber: [(v) => !!v || "Phone is required", (v) => /^\d+$/.test(v) || "Phone must contain only digits"],
                billCity: [(v) => !!v || "City is required"],
                billStateId: [
                    (v) => {
                        if (!this.billStateList.length) {
                            return true;
                        } else {
                            return !!v || "Province is required";
                        }
                    },
                ],
                billState: [
                    (v) => {
                        if (this.billStateList.length) {
                            return true;
                        } else {
                            return !!v || "Province is required";
                        }
                    },
                ],
                billCountryId: [(v) => !!v || "Country is required"],
                billZipCode: [(v) => !!v || "Zip code is required"],
                billingPrefixNumber: [(v) => !!v || "", (v) => /^\d+$/.test(v) || ""],
            },
            paymentTypeId: "",
            paymentMethodId: "",
            totalPrice: 0,
            taxPrice: 0,
            apoShippingPrice: 0,
            shippingChargesPrice: 0,
            productPrice: 0,
            payAdditionalFee: 0,
            discountPrice: 0,
            paypalConfig: {
                merchantId: "WPQW3YQEDPWML", //商户号
                payId: process.env.payId,
                clientId: process.env.clientId, //客户端Id
                dataPartnerAttributionId: process.env.dataPartnerAttributionId, // PayPal Partner Attribution ID.
                clientToken: "", //客户端令牌
                accessToken: "", //后台获取的token
            },
            orderId: "",
            payCardOrderId: "",
            orderProductList: [],
            isChildOrder: false,
            mainOrderId: "",
            waitPaymentOrder: "",
            paypalId: "",
            otherPayId: 0,
            payTypeList: [],
            paypal_sandBox: "",
            authorize_sandBox: "",
            nCouponDiscount: "",
            nCouponCash: "",
            firstOrderDiscount: "",
            nCouponCodeTemp: "",
            nCouponCode: "",
            couponId: "",
            voucherId: "",
            // pointPrice: "",
            emailErrorMessage: "",
            pointDiscountChecked: false,
            pointDiscountPrice: "",
            pointDiscountPriceTemp: "",
            couponIdTemp: "",
            couponDiscountChecked: false,
            couponOption: [],
            voucherDiscountChecked: false,
            voucherOption: [],
            // crowdDiscount: "",
            // crowdDiscountPrice: "",
            firstDiscount: "",
            firstDiscountPrice: "",
            // couponDiscount: "",
            // couponDiscountPrice: "",
            // voucherPrice: "",
            // promotionDiscount: "",
            promotionPrice: "",
            // userLevelDiscount: "", //用户等级折扣
            // userLevelDiscountPrice: "", //用户等级折扣
            userLevelId: null,
            // couponAdditionalCosts: "",
            // promotionCenterAdditionalPrice:"",
            referPointsInfo: "",
            tempCartList: [],
            clientSecret: "",
            stripe: undefined,
            element: undefined,
            stripeStatus: false,
            paymentSerialNum: "",
            stripeBtn: null,
            orderDiscount: "",
            orderDiscountPrice: "",
            stripeKey: "", // 3.9 stripe获取key
            payTypeId: "",
            userEmail: "",
            predictions: [],
            predictions2: [],
            orderGivingQuotation: "",
            orderCurrency: "",
            orderCurrencySymbol: "",
            notFreeShipping: 0,
            disabledCouponCode: false,
            disabledCouponOption: false,
            disabledVoucherOption: false,
            disabledPoint: false,
            customPaypal: "",
            isBuyAgainLowTips: false,
            isUpdateAddress: false,
            isPaidOrder: false,
            debounceGetSpecificAddress: null,
            debounceGetSpecificAddress2: null,
            errorMessageObj: {
                shippingLine1: "",
                shippingCity: "",
                shippingCountry: "",
                shippingState: "",
                shippingCode: "",

                shippingApoZip: "",
                shippingApoState: "",
                shippingApoCity: "",

                billingLine1: "",
                billingCity: "",
                billingCountry: "",
                billingState: "",
                billingCode: "",
            },
            debounceVerifyAddress: null,
            couponRadioshow: 0,
            debounceShippingPhoneNumberInput: null,
			oneDollarEmail:{},
        };
    },
    watch: {
        addressStatus() {
            this.$refs.shippingAddressForm.reset();
            this.initPaypalPaymentConfig();
            this.getStripeKey();
        },
        isLogin() {
            location.reload();
        },
        couponId(val) {
            console.log("优惠券改动");
            this.changeDiscount(val, 2);
            this.debounceCalcPrice();
        },
        voucherId(val) {
            console.log("代金券改动");
            this.changeDiscount(val, 3);
            this.debounceCalcPrice();
        },
        // 积分可抵扣金额不超过订单总价
        pointDiscountPrice(val) {
            this.changeDiscount(val, 4);
            this.$nextTick(() => {
                if (Number(val) > Number(this.pointMaxPriceOrNoPointTotalPrice)) {
                    this.pointDiscountPrice = this.pointMaxPriceOrNoPointTotalPrice;
                }
                this.debounceCalcPrice();
            });
        },
        symbolCode() {
            this.initPaypalPaymentConfig(this.customPaypal);
        },
        shippingAddressForm: {
            handler() {
                this.debounceVerifyAddress("shippingAddress");
            },
            deep: true,
        },
        billingAddressForm: {
            handler() {
                this.debounceVerifyAddress("billingAddress");
            },
            deep: true,
        },
        currentShippingCountry() {
            this.initPaypalPaymentConfig();
            this.getStripeKey();
        },
    },
    computed: {
		device() {
			return this.$store.state.device;
		},
        couponIdAsNumber: {
            get() {
                return Number(this.couponId);
            },
            set(value) {
                this.couponId = Number(value);
            }
        },
        customCountryId() {
            if (this.addressStatus === 1) {
                return 226;
            } else {
                return this.currentShippingCountry.id;
            }
        },
        showEmailInput() {
            return (!this.isLogin && !this.isArtWork && !this.isOrder) || ((this.isArtWork || this.isOrder) && !this.userEmail);
        },
        showVoucherOption() {
            if (this.isChildOrder) {
                return false;
            }
            return this.voucherOption.length || this.disabledVoucherOption;
        },
        showPoint() {
            if (this.isChildOrder) {
                return false;
            }
            return this.pointMaxPriceOrNoPointTotalPrice || this.pointDiscountPriceTemp;
        },

        //隐藏apo地址
        hideApoAddress() {
            let idList = [74, 224, 206, 215];
            return idList.includes(this.shippingAddressForm.shippingCountryId);
        },
        //不展示发货地址中的州
        showshippingAddressDe() {
            let idList = [74, 81, 224, 206, 215];
            return !idList.includes(this.shippingAddressForm.shippingCountryId);
        },
        //不展示账单地址中的州
        showBillingAddressDe() {
            let idList = [74, 81, 224, 206, 215];
            return !idList.includes(this.billingAddressForm.billCountryId);
        },
        currentCurrency() {
            let currency = deepClone(this.$store.state.currency);
            /**
             * 先判断是否是子订单
             * 在判断订单信息里有无货币信息
             */
            if (this.isChildOrder) {
                if (this.orderCurrency && this.orderCurrencySymbol && this.orderGivingQuotation) {
                    return {
                        rate: this.orderGivingQuotation,
                        symbol: this.orderCurrencySymbol,
                        symbolCode: this.orderCurrency,
                    };
                } else {
                    return {
                        rate: currency.rate,
                        symbol: currency.symbol,
                        symbolCode: currency.code,
                    };
                }
            } else {
                return {
                    rate: currency.rate,
                    symbol: currency.symbol,
                    symbolCode: currency.code,
                };
            }
        },
        //货币符号
        symbol() {
            return this.currentCurrency.symbol;
        },
        //货币code
        symbolCode() {
            return this.currentCurrency.symbolCode;
        },
        proId() {
            return this.$store.state.proId;
        },
        isFdSite(){
            return this.proId === 9;
        },
        isPopSite(){
            return this.proId === 10;
        },
        hideAddress(){
            return this.isFdSite && (this.isArtWork || this.orderType==2)
        },
        disabledAddress() {
            //是子订单或者已支付订单,禁用地址
            return !(this.isChildOrder || this.isPaidOrder || this.isPopSite);
            //有订单并且之前已填写过地址
            // return !(this.orderId && !this.noAddress);
        },
        isZeroOrder() {
            return Number(this.totalPrice) <= 0;
        },
        proType() {
            return this.$store.state.proType;
        },
        //结算产品列表
        selectCartList() {
            if (process.client) {
                if (this.isOrder || this.isArtWork ) {
                    return this.orderProductList;
                } else if (this.isBuyNow) {
                    return this.tempCartList;
                } else {
                    if (!localStorage.getItem("tempDesignCart")) {
                        return [];
                    }
                    return JSON.parse(localStorage.getItem("tempDesignCart"));
                }
            } else {
                return [];
            }
        },
        currentShippingCountry() {
            let find = this.countryList.find((item) => item.id === this.shippingAddressForm.shippingCountryId);
            return find || {};
        },
        currentBillingCountry() {
            if (this.billingAddressStatus === 0) {
                return this.currentShippingCountry;
            }
            let find = this.countryList.find((item) => item.id === this.billingAddressForm.billCountryId);
            return find || {};
        },
        currentShippingState() {
            let findState = this.shippingStateList.find((item) => {
                return item.id === this.shippingAddressForm.shippingStateId;
            });
            return findState || {};
        },
        currentBillingState() {
            if (this.billingAddressStatus === 0) {
                return this.currentShippingState;
            }
            let findState = this.billStateList.find((item) => {
                return item.id === this.billingAddressForm.billStateId;
            });
            return findState || {};
        },
        isLogin() {
            return this.$store.getters.isLogin;
        },
        linkOrderId(){
            return this.$route.params.oid || this.$route.query.orderid || this.$route.query.orderId
        },
        isOrder() {
            return !!(this.$route.query.type === "designer" && this.linkOrderId);
        },
        isArtWork() {
            return !!(this.$route.query.type === "artwork" && this.linkOrderId);
        },
        totalQty() {
            let qty = 0;
            if (!this.selectCartList.length) {
                return 0;
            }
            this.selectCartList.forEach((item) => {
                qty += item.quantity;
            });
            return qty;
        },
        isReputable() {
            if (this.isLogin) {
                return this.$store.state.userInfo.isReputable; //判断是否信誉客户 1:非信誉客户 2:信誉客户
            } else {
                return 0;
            }
        },
        //用户uuid
        cartUuid() {
            if (this.isBuyNow) {
                return this.$route.query.uuid;
            } else {
                if (this.isLogin) {
                    return null;
                } else {
                    return this.$store.state.userUUID;
                }
            }
        },
        //获取邮箱
        inputEmail() {
            if (this.isLogin) {
                return this.$store.state.userInfo.email;
            } else {
                return this.shippingAddressForm.email?.trim();
            }
        },
        //支付方式
        paymentMethodText() {
            let list = this.payTypeList;
            let pay = list.find((item) => {
                return item.id === this.paymentMethodId;
            });
            return pay ? pay.payType.payName : "";
        },
        // 积分最大可抵扣金额
        pointMaxPrice() {
            if (!this.referPointsInfo?.pointToCash || !this.referPointsInfo?.activeReferPoints) return 0;
            return parseInt(this.referPointsInfo.activeReferPoints / this.referPointsInfo.pointToCash);
        },
        // 未抵扣积分时的总价
        noPointTotalPrice() {
            return Math.max(this.productPrice - this.firstDiscountPrice - this.couponDiscountPrice - this.voucherPrice - this.crowdDiscountPrice - this.promotionPrice, 0);
        },
        //如果积分抵扣高于订单金额,则最大是订单金额
        pointMaxPriceOrNoPointTotalPrice() {
            if (!this.referPointsInfo?.pointToCash || !this.referPointsInfo?.activeReferPoints) return 0;
            //积分可抵扣金额
            let pointMaxPrice = this.pointMaxPrice;
            //未抵扣积分时的总价
            //如果积分抵扣高于订单金额,则最大是订单金额.如果小于,则最大输入是积分抵现金额
            let noPointTotalPrice = this.noPointTotalPrice;
            return Math.round((pointMaxPrice > noPointTotalPrice ? noPointTotalPrice * this.currentCurrency.rate : pointMaxPrice * this.currentCurrency.rate) * 100) / 100;
        },
        isBuyNow() {
            return this.$route.query.isBuyNow == 1 ? true : false;
        },
        paypalBaseUrl() {
            if (this.paypal_sandBox) {
                return "https://api-m.sandbox.paypal.com";
            }
            return "https://api-m.paypal.com";
        },
        langCart() {
            return this.$store.getters.lang?.cart;
        },
        language() {
            return this.$store.state.language.language;
        },
        isDirectOrder() {
            return !this.isBuyNow && !this.isOrder && !this.isArtWork;
        },
    },
    methods: {
        uniqueId(val){
            return Number(val.uniqueId)
        },
        showSMStextIcon(){
            this.showSMStext = false;
        },

        //洲回填简写和全称
        formatShippingState(item) {
            if (item.acronymCode !== null) {
                return `(${item.acronymCode}) ${item.stateName}`;
            } else {
                return `${item.stateName}`;
            }
        },
        //洲回填简写和全称
        formatbillState(item) {
            if (item.acronymCode !== null) {
                return `(${item.acronymCode}) ${item.stateName}`;
            } else {
                return `${item.stateName}`;
            }
        },

        verifyAddress(type) {
            let data;
            if (type === "shippingAddress") {
                let currentShippingCountry = this.currentShippingCountry;
                let shippingState = this.currentShippingState?.stateName || this.shippingAddressForm.shippingState;
                data = {
                    addressDetail: this.shippingAddressForm.shippingAddressLine1,
                    state: shippingState,
                    city: this.shippingAddressForm.shippingCity,
                    country: currentShippingCountry?.acronym,
                    postalCode: this.shippingAddressForm.shippingZipCode,
                };
                if (!data.addressDetail || !data.country || !data.city || !data.postalCode) {
                    return;
                }
            } else {
                let currentBillingCountry = this.currentBillingCountry;
                let shippingState = this.currentBillingState?.stateName || this.billingAddressForm.billState;
                data = {
                    addressDetail: this.billingAddressForm.billAddressLine1,
                    state: shippingState,
                    city: this.billingAddressForm.billCity,
                    country: currentBillingCountry?.acronym,
                    postalCode: this.billingAddressForm.billZipCode,
                };
                if (!data.addressDetail || !data.country || !data.city || !data.postalCode) {
                    return;
                }
            }
            googleVerifyAddress(data).then((res) => {
                let arr = res.data;
                if (!arr || !arr.length) {
                    if (type === "shippingAddress") {
                        this.errorMessageObj = Object.assign({}, this.errorMessageObj, {
                            shippingLine1: "",
                            shippingCity: "",
                            shippingCountry: "",
                            shippingState: "",
                            shippingCode: "",
                        });
                    } else {
                        this.errorMessageObj = Object.assign({}, this.errorMessageObj, {
                            billingLine1: "",
                            billingCity: "",
                            billingCountry: "",
                            billingState: "",
                            billingCode: "",
                        });
                    }
                    return false;
                }
                let map;
                if (type === "shippingAddress") {
                    map = {
                        1: "shippingCountry",
                        2: "shippingState",
                        3: "shippingCity",
                        4: "shippingLine1",
                        5: "shippingCode",
                    };
                } else {
                    map = {
                        1: "billingCountry",
                        2: "billingState",
                        3: "billingCity",
                        4: "billingLine1",
                        5: "billingCode",
                    };
                }
                this.errorMessageObj = Object.assign({}, this.errorMessageObj, {
                    shippingLine1: "",
                    shippingCity: "",
                    shippingCountry: "",
                    shippingState: "",
                    shippingCode: "",
                });
                arr.forEach((item) => {
                    this.errorMessageObj[map[item.code]] = item.msg;
                });
            });
        },
        async changeShippingState() {
            if (!this.shippingAddressForm.shippingCountryId || !this.shippingAddressForm.shippingZipCode) return;
            this.debounceCalcPrice();
            if (!this.isDirectOrder && !this.isChildOrder) {
                let result = this.validateForm();
                if (!result.length) {
                    await this.updateAddress();
                }
            }
        },
        async changeApoAddress() {
            this.debounceCalcPrice();
            if (!this.isDirectOrder && !this.isChildOrder) {
                let result = this.validateForm();
                if (!result.length) {
                    await this.updateAddress();
                }
            }
        },
        inputZipPostal() {
            if (!this.shippingAddressForm.shippingStateId || !this.shippingAddressForm.shippingCountryId) return;
            this.debounceCalcPrice();
        },
        async selectShippingCountry(val) {
            let find = this.countryList.find((item) => item.id === this.shippingAddressForm.shippingCountryId);
            if (find) {
                this.taxName1 = find.taxName === null ? "Tax Price" : find.taxName;
                this.VATtext = find.continentId === 5 ? true : false; //欧洲国家显示税费提示文案
            }
            this.notFreeShipping = !this.countryList.find((ele) => ele.id == this.shippingAddressForm.shippingCountryId)?.isFreeShipping;
            this.shippingAddressForm.shippingStateId = "";
            this.shippingAddressForm.shippingState = "";
            this.shippingStateList = await this.getState({
                id: val,
            });
            this.shippingPrefixNumber = this.currentShippingCountry.code ? String(this.currentShippingCountry.code) : this.shippingPrefixNumber;
            this.calcPrice();
        },
        async selectBillingCountry(val) {
            this.notFreeShipping = !this.countryList.find((ele) => ele.id == this.billingAddressForm.billCountryId)?.isFreeShipping;
            this.billingAddressForm.billStateId = "";
            this.billingAddressForm.billState = "";
            this.billStateList = await this.getState({
                id: val,
            });
            this.billingPrefixNumber = this.currentBillingCountry.code ? String(this.currentBillingCountry.code) : this.billingPrefixNumber;
            this.calcPrice();
        },
        async changeUserApoAddress(val) {
            let findAddress = this.userApoAddressList.find((item) => item.id === val);
            if (!findAddress) {
                return;
            }
            let cityName, stateName;
            this.APOCityList.forEach((item) => {
                if (item.name.substring(0, 3) === findAddress.apoCity) {
                    cityName = item.name;
                }
            });
            this.APOStateList.forEach((item) => {
                if (item.name.substring(0, 2) === findAddress.apoState) {
                    stateName = item.name;
                }
            });
            this.shippingAddressForm.shippingFirstName = findAddress.firstName;
            this.shippingAddressForm.shippingLastName = findAddress.lastName;
            this.shippingAddressForm.shippingApoCity = cityName;
            this.shippingAddressForm.shippingApoState = stateName;
            this.shippingAddressForm.shippingApoZip = findAddress.apoZip;

            try {
                this.shippingAddressForm.apoAddress = JSON.parse(findAddress.addressLine)[1];
            } catch (e) {
                this.shippingAddressForm.apoAddress = findAddress.addressLine;
            }
            if (findAddress.phoneNum.includes("-")) {
                this.shippingPrefixNumber = findAddress.phoneNum.split("-")[0];
                this.shippingAddressForm.shippingPhoneNumber = findAddress.phoneNum.split("-")[1];
            } else {
                this.shippingPrefixNumber = "";
                this.shippingAddressForm.shippingPhoneNumber = findAddress.phoneNum;
            }
            this.debounceCalcPrice();
            await this.$nextTick();
            if (!this.isDirectOrder && !this.isChildOrder) {
                let result = this.validateForm();
                if (!result.length) {
                    await this.updateAddress();
                }
            }
        },
        async changeUserShippingAddress(val) {
            let findAddress = this.userShippingAddressList.find((item) => item.id === val);
            if (!findAddress) {
                return;
            }
            this.shippingAddressForm.shippingFirstName = findAddress.firstName;
            this.shippingAddressForm.shippingLastName = findAddress.lastName;
            this.shippingAddressForm.shippingAddressLine1 = JSON.parse(findAddress.addressLine)[1];
            this.shippingAddressForm.shippingAddressLine2 = JSON.parse(findAddress.addressLine)[2];
            this.shippingAddressForm.shippingCity = findAddress.city;
            this.shippingAddressForm.shippingCountryId = findAddress.countryId;
            this.shippingAddressForm.shippingStateId = findAddress.stateId;
            this.shippingAddressForm.shippingZipCode = findAddress.zipCode;
            this.shippingAddressForm.shippingCompany = findAddress.shippingCompany;

            if (findAddress.phoneNum.includes("-")) {
                this.shippingPrefixNumber = findAddress.phoneNum.split("-")[0];
                this.shippingAddressForm.shippingPhoneNumber = findAddress.phoneNum.split("-")[1];
            } else {
                this.shippingPrefixNumber = "";
                this.shippingAddressForm.shippingPhoneNumber = findAddress.phoneNum;
            }
            this.notFreeShipping = !this.countryList.find((ele) => ele.id == findAddress.countryId)?.isFreeShipping;
            let shippingStateList = await this.getState({
                id: findAddress.countryId,
            });
            this.shippingStateList = shippingStateList;
            if (shippingStateList && shippingStateList.length > 0) {
                let findState = shippingStateList.find((item) => {
                    return item.id === findAddress.stateId;
                });
                if (findState) {
                    this.shippingAddressForm.shippingStateId = findState.id;
                } else {
                    this.shippingAddressForm.shippingState = findAddress.state;
                }
            } else {
                this.shippingAddressForm.shippingState = findAddress.state;
            }
            let find = this.countryList.find((item) => item.id === this.shippingAddressForm.shippingCountryId);
            if (find) {
                this.taxName1 = find.taxName === null ? "Tax Price" : find.taxName;
                this.VATtext = find.continentId === 5 ? true : false;
            }
            if(findAddress.phoneNum){
                this.getSmsSubscriptionLogin();
            }
            this.debounceCalcPrice();
            await this.$nextTick();
            if (!this.isDirectOrder && !this.isChildOrder) {
                let result = this.validateForm();
                if (!result.length) {
                    await this.updateAddress();
                }
            }
        },
        getSpecificAddress(val) {
            this.loadGoogleAddress = true;
            setTimeout(() => {
                this.loadGoogleAddress = false;
            }, 1000);
            if (val) {
                let params = {
                    address: val,
                };
                getGoogleAddress(params).then((res) => {
                    let predictions = res.data;
                    predictions.forEach((i) => {
                        const commaIndex = i.mainText.indexOf(",");
                        if (commaIndex !== -1) {
                            const firstPart = i.mainText.substring(0, commaIndex + 1);
                            const secondPart = i.mainText.substring(commaIndex + 1);
                            i.mainTextCopy = `${firstPart}<span style="color: gray;">${secondPart}</span>`;
                        }
                    });
                    this.predictions = predictions;
                });
            } else {
                this.predictions = [];
            }
        },
        setAddress() {
            if (this.shippingAddressForm.shippingAddressLine1) {
                this.getAddressDetails();
            }
        },
        //获取国家省市区
        getAddressDetails() {
            let findPredictions = this.predictions.find((item) => item.mainText === this.shippingAddressForm.shippingAddressLine1);
            if (findPredictions) {
                let params = {
                    placeId: findPredictions.placeId,
                };
                this.$nextTick(() => {
                    this.shippingAddressForm.shippingAddressLine1 = findPredictions.detailAddress;
                });
                getGoogleAddressDetail(params).then((res) => {
                    this.shippingAddressForm.shippingCity = res.data.city;
                    let findCountry = this.countryList.find((item) => item.countryName === res.data.country);
                    if (findCountry) {
                        this.taxName1 = findCountry.taxName === null ? "Tax Price" : findCountry.taxName;
                        this.VATtext = findCountry.continentId === 5 ? true : false;
                        this.shippingPrefixNumber = findCountry.code;
                        this.shippingAddressForm.shippingCountryId = findCountry.id;
                        this.$nextTick(() => {
                            //如果是没有州的国家并且google推荐无城市,就把州回填到城市
                            if (!this.showshippingAddressDe && !this.shippingAddressForm.shippingCity) {
                                this.shippingAddressForm.shippingCity = res.data.state;
                            }
                        });
                        this.getState({
                            id: findCountry.id,
                        }).then(async (res2) => {
                            console.log(res2, "res2");
                            this.shippingStateList = res2;
                            let findState = this.shippingStateList.find((item) => item.stateName === res.data.state);
                            if (findState) {
                                this.shippingAddressForm.shippingStateId = findState.id;
                            } else {
                                this.shippingAddressForm.shippingState = res.data.state;
                            }
                            this.debounceCalcPrice();
                            if (!this.isDirectOrder && !this.isChildOrder) {
                                let result = this.validateForm();
                                if (!result.length) {
                                    await this.updateAddress();
                                }
                            }
                        });
                    }
                    this.shippingAddressForm.shippingZipCode = res.data.code;
                });
            }
        },

        //Billing Address获取具体地址
        getSpecificAddress2(val) {
            this.loadGoogleAddress2 = true;
            setTimeout(() => {
                this.loadGoogleAddress2 = false;
            }, 1000);
            if (val) {
                let params = {
                    address: val,
                };
                getGoogleAddress(params).then((res) => {
                    let predictions2 = res.data;
                    predictions2.forEach((i) => {
                        const commaIndex = i.mainText.indexOf(",");
                        if (commaIndex !== -1) {
                            const firstPart = i.mainText.substring(0, commaIndex + 1);
                            const secondPart = i.mainText.substring(commaIndex + 1);
                            i.mainTextCopy2 = `${firstPart}<span style="color: gray;">${secondPart}</span>`;
                        }
                    });
                    this.predictions2 = predictions2;
                });
            } else {
                this.predictions2 = [];
            }
        },
        setAddress2() {
            if (this.billingAddressForm.billAddressLine1) {
                this.getAddressDetails2();
            }
        },

        //获取国家省市区
        getAddressDetails2() {
            let findPredictions2 = this.predictions2.find((item) => item.mainText === this.billingAddressForm.billAddressLine1);
            if (findPredictions2) {
                let params = {
                    placeId: findPredictions2.placeId,
                };
                this.$nextTick(() => {
                    this.billingAddressForm.billAddressLine1 = findPredictions2.detailAddress;
                });
                getGoogleAddressDetail(params).then((res) => {
                    this.billingAddressForm.billCity = res.data.city;
                    let findCountry2 = this.countryList.find((item) => item.countryName === res.data.country);
                    if (findCountry2) {
                        this.billingPrefixNumber = findCountry2.code;
                        this.billingAddressForm.billCountryId = findCountry2.id;
                        this.$nextTick(() => {
                            //如果是没有州的国家并且google推荐无城市,就把州回填到城市
                            if (!this.showshippingAddressDe && !this.billingAddressForm.billCity) {
                                this.billingAddressForm.billCity = res.data.state;
                            }
                        });
                        this.getState({
                            id: findCountry2.id,
                        }).then((res2) => {
                            this.billStateList = res2;
                            let findState2 = this.billStateList.find((item) => item.stateName === res.data.state);
                            if (findState2) {
                                this.billingAddressForm.billStateId = findState2.id;
                            } else {
                                this.billingAddressForm.billState = res.data.state;
                            }
                        });
                    }
                    this.billingAddressForm.billZipCode = res.data.code;
                });
            }
        },

        getAllCoupon() {
            if (this.proType !== 0 || !this.isLogin) {
                return Promise.resolve();
            }
            return new Promise((resolve) => {
                Promise.all([
                    getUserUsableVoucherList(),
                    getUserReferPointsInfo(),
                ]).then((res) => {
                    this.voucherOption = res[0].data;
                    this.referPointsInfo = res[1].data;
                    resolve();
                });
            });
        },
        modifyPayMethod(data) {
            return new Promise((resolve) => {
                updatePaymentMethodId(data).then((res) => {
                    resolve(res);
                });
            });
        },
        async changePaymentMethod(val) {
            //如果订单已存在，先调用修改支付方式的接口,再调用更新订单折扣的接口
            //订单不存在,直接调用更新价格的接口
            if (this.orderId) {
                await this.modifyPayMethod({
                    paymentMethodId: val,
                    id: this.orderId,
                });
            } else {
                this.debounceCalcPrice();
            }
        },
        changeAddressStatus(val) {
            let node = document.getElementById("rightContent");
            this.$nextTick(() => {
                node.scroll({
                    top: node.scrollHeight - node.clientHeight,
                    behavior: "smooth",
                });
            });
        },
        changeDiscount(val, type) {
            console.log(typeof val,"leixing");
            if (val) {
                !this.radioGroup.includes(type) ? this.radioGroup.push(type) : "";
            } else {
                let findIndex = this.radioGroup.findIndex((item) => {
                    return item === type;
                });
                if (findIndex >= 0) {
                    this.radioGroup.splice(findIndex, 1);
                }
            }
            let find=this.couponOption && this.couponOption.find((r)=>r.uniqueId == val)
            if(find){
                if(find.isNew === 0){
                    this.showPrice = true;
                }else{
                    this.showPrice = false;
                }
            }
        },
        toOrderDetail() {
            this.stripeStatus = false;
        },
        onInput(){
            this.triggerInput = true;
        },

        updatePrice2(data) {
            this.orderDiscount = data.orderDiscount;
            this.orderDiscountPrice = data.orderDiscountPrice;
            this.productPrice = data.productPrice;
            this.totalPrice = data.totalPrice;
            this.taxPrice = data.taxPrice;
            this.apoShippingPrice = data.apoShippingPrice;
            this.payAdditionalFee = data.payAdditionalFee;
            this.discountPrice = data.discountPrice;

            this.userLevelId = data.userLevelId;
        },
        updatePrice(data) {
            this.orderDiscount = data.orderDiscount;
            this.orderDiscountPrice = data.orderDiscountPrice;
            this.productPrice = data.productPrice;
            this.totalPrice = data.totalPrice;
            this.taxPrice = data.taxPrice;
            this.apoShippingPrice = data.apoShippingPrice;
            this.payAdditionalFee = data.payAdditionalFee;
            this.discountPrice = data.discountPrice;

            // this.couponAdditionalCosts = data.couponAdditionalCosts;
            this.userLevelId = data.userLevelId;
        },
        async updateAddress() {
            try {
                //判断是否是询盘生成的订单，询盘生成的订单无地址
                this.isUpdateAddress = true;
                let orderData = this.getAddOrderData();
                let res = await stylistEditOrderAddress(
                            Object.assign({}, orderData, {
                                orderId: this.aId,
                            })
                    );
                this.isUpdateAddress = false;
                return res.data;
            } catch (e) {
                console.log(e,"12312");
                this.isUpdateAddress = false;
            }
        },
        getPicPath(img) {
            try {
                const parsed = JSON.parse(img);
                if (Array.isArray(parsed) && parsed.length > 0 && parsed[0].url) {
                    return parsed[0].url;
                } else {
                    return '';
                }
            } catch (error) {
                console.error('JSON parsing error:', error);
                return '';
            }
        },

        filterName(item, queryText, itemText) {
            if (item.acronymCode) {
                const normalizedQuery = queryText.toLocaleLowerCase().replace(/\s/g, ""); //文本转换为小写并移除所有空格
                const normalizedItemText = itemText.toLocaleLowerCase().replace(/\s/g, "");
                const normalizedAcronymCode = item.acronymCode.toLocaleLowerCase().replace(/\s/g, "");
                return normalizedItemText.includes(normalizedQuery) || normalizedAcronymCode.includes(normalizedQuery);
            }
            const normalizedQuery = queryText.toLocaleLowerCase().replace(/\s/g, "");
            const normalizedItemText = itemText.toLocaleLowerCase().replace(/\s/g, "");
            return normalizedItemText.includes(normalizedQuery);
        },
        isRegister(val) {
            isOldUser({
                email: val,
            }).then((res) => {
                if (res.data === 0) {
                    this.$store.commit("setLogin", "login");
                    this.$store.commit("setLoginEmail", val);
                }
            });
            let emailRules = this.noLoginEmailRules,
                    emailErrorMessage;
            for (let i = 0; i < emailRules.length; i++) {
                let bool = emailRules[i](val);
                if (typeof bool === "boolean" && bool) {
                    emailErrorMessage = "";
                } else {
                    emailErrorMessage = bool;
                    break;
                }
            }
            if (!emailErrorMessage) {
                this.debounceCalcPrice();
            }
            if (this.addressStatus === 0) {
                this.getSmsSubscriptionNotLogin();
            }
        },
        getState(data) {
            return new Promise((resolve) => {
                getStateList(data).then((res) => {
                    resolve(res.data);
                });
            });
        },
        getCountry() {
            return new Promise((resolve) => {
                getCountryList().then((res) => {
                    this.countryList = res.data;
                    resolve(res.data);
                });
            });
        },
        validateForm() {
            let result = [];
            //子订单不需要校验地址，自动采用主订单的地址
            if (!this.isChildOrder) {
                let resultShipping = this.$refs.shippingAddressForm.validate();
                let resultBill = false;
                //如果是paypal支付，不需要填写账单
                if (this.payTypeId === 10000) {
                    resultBill = true;
                } else {
                    if (this.billingAddressStatus === 0) {
                        resultBill = resultShipping;
                    } else if (this.billingAddressStatus === 1) {
                        resultBill = this.$refs.billAddressForm.validate();
                    }
                }
                if (!resultShipping || !resultBill) {
                    result.push({
                        message: null,
                        type: "error",
                    });
                }
            }
            return result;
        },

        toComplete() {
            //存在子订单，拿主订单的oid
            if (this.isChildOrder) {
                this.$router
                        .replace({
                            path: `/order/stylistComplete/${this.mainOrderId}`,
                        })
                        .then(() => {
                            this.$gl.hide();
                        });
            } else {
                this.$router
                        .replace({
                            path: `/order/stylistComplete/${this.orderId}`,
                        })
                        .then(() => {
                            this.$gl.hide();
                        });
            }
        },

        //支付校验
        verifyPayment(data) {
            this.$gl.show();
            stylistAutoVerifyPayment(data).finally(() => {
                localStorage.removeItem("tempDesignCart");
                this.toComplete();
            });
        },

        cancelPay() {
            this.payBeforeDialog = false;
        },

        handleZfBefore() {
            return new Promise(async (resolve, reject) => {
                try {
                    await this.addOrder();
                    //如果不是直接订单并且不是子订单
                    console.log(this.isDirectOrder, this.isChildOrder);
                    if (!this.isDirectOrder && !this.isChildOrder && !this.isBuyNow) {
                        await this.updateAddress();
                    }
                    let resData = await this.getMyOrderInfo({
                        oid: this.mainOrderId || this.orderId,
                        getProductInfo: true,
                        getAddressInfo: true,
                        getDiscountInfo: true,
                    });
                    let result2 = this.findPendingPaymentOrder(resData);
                    if (result2) {
                        //更新价格
                        this.updatePrice(this.waitPaymentOrder);
                    }
                    this.userEmail = resData.data.email;
                    return resolve();
                } catch (e) {
                    return reject(e);
                }
            });
        },

        //提交按钮
        submitOrderBefore() {
            let find = this.countryList.find((item) => item.id === this.shippingAddressForm.shippingCountryId);
            if (find) {
                this.taxName1 = find.taxName === null ? "Tax Price" : find.taxName;
                this.VATtext = find.continentId === 5 ? true : false;
            }
            let result = this.validateForm();
            if (result.length) {
                if (result[0].message) {
                    this.$toast.error(result[0].message);
                }
                return false;
            }
            this.payBeforeDialog = true;
        },

        async submitOrder() {
            try {
                this.payBeforeDialog = false;
                this.$gl.show();
                await this.handleZfBefore();
				try {
                    // 记录Stripe唤起支付错误日志
                    let date=getDate();
                    let time = new Date().toTimeString().substring(0,8);
                    let logDate=date+' '+time
                    let errorLogsData={
                        oid: this.isChildOrder ? this.orderId : this.mainOrderId,
                        errorType:1,
                        payType: 2,
                        logDate
                    }
                    paymentErrorLogs(errorLogsData);
                }catch(e){
                    console.log(e);
                }
                if (this.isZeroOrder || this.payTypeId === 0) {
                    this.verifyPayment({
                        id: this.orderId,
                        paymentSerialNum: "",
                        cartIdList: this.selectCartList.map((item) => item.id),
                        cartUuid: this.cartUuid,
                        givingQuotation: this.currentCurrency.rate,
                        currency: this.symbolCode,
                        countryId: this.customCountryId,
                    });
                    return false;
                } else if (this.payTypeId === 10010) {
                    this.$gl.hide();
                    this.stripeStatus = true;
                    await this.$nextTick();
                    this.setLoading(true);
                    await this.getPayIntent();
                    await this.initStripePay();
                }
            } catch (e) {
                this.payBeforeDialog = false;
                this.$gl.hide();
            }
        },

        loadPayPalSDK(paypalConfig) {
            const fn = () => {
                return loadScript({
                    "client-id": paypalConfig.clientId,
                    "enable-funding": "venmo",
                    components: ["buttons", "hosted-fields", "funding-eligibility"],
                    "data-client-token": paypalConfig.clientToken,
                    currency: this.symbolCode,
                    intent: "capture",
                });
            };
            let retryLoadScript = withRetryAsync(fn, 3);
            return retryLoadScript();
        },

        async initPaypalPaymentConfig() {
            let paypalConfig = this.paypalConfig,
                    payTypeList = this.payTypeList;
            if (!payTypeList.length) {
                return;
            }
            //获取paypal的配置
            let paypalItem = payTypeList.find((item) => {
                return item.payType.id === 10000;
            });
            try {
                let res = await paypalApi.generateAccessToken(
                        {
                            payId: paypalItem.payType.id,
                            countryId: this.customCountryId,
                        },
                        this.paypalBaseUrl
                );
                paypalConfig.accessToken = res.data.access_token;
                paypalConfig.clientId = res.data.pay_config.clientId;
                paypalConfig.clientToken = await paypalApi.generateClientToken(this.paypalConfig.accessToken, this.paypalBaseUrl);
            } catch (e) {
                this.$toast.error("System Error: Please reach out to our customer service team at your earliest convenience. Thank you!");
            }
            // if (!res.data.pay_config.merchantId) {
            // 	this.$toast.show('Please bind the merchant first')
            // }
            // paypalConfig.merchantId = res.data.pay_config.merchantId || 'WPQW3YQEDPWML';
            this.loadPayPalSDK(paypalConfig)
                    .then((paypal) => {
                        var FUNDING_SOURCES = [paypal.FUNDING.PAYPAL];
                        let _this = this;
                        FUNDING_SOURCES.forEach(function (fundingSource) {
                            var button = paypal.Buttons({
                                fundingSource: fundingSource,
                                style: {
                                    layout: "vertical",
                                    color: "blue",
                                    shape: "rect",
                                    height: 36,
                                },
                                onClick: async function (data, actions) {
                                    try {
                                        _this.payBeforeDialog = false;
                                        await _this.handleZfBefore();
										try {
                                            //记录PayPel唤起支付错误日志
                                            let date=getDate();
                                            let time = new Date().toTimeString().substring(0,8);
                                            let logDate=date+' '+time
                                            let errorLogsData={
                                                oid: _this.isChildOrder ? _this.orderId : _this.mainOrderId,
                                                errorType:1,
                                                payType: 1,
                                                logDate
                                            }
                                            paymentErrorLogs(errorLogsData);
                                        }
                                        catch(e){
                                            console.log(e);
                                        }
                                        return actions.resolve();
                                    } catch (e) {
                                        return actions.reject();
                                    }
                                },
                                createOrder: async function (data, actions) {
                                    return actions.order.create({
                                        purchase_units: [
                                            {
                                                amount: {
                                                    currency_code: _this.symbolCode,
                                                    value: round2(_this.totalPrice * _this.currentCurrency.rate),
                                                },
                                                custom_id: `designer|${_this.orderId}|${_this.customCountryId}`,
                                                invoice_id: _this.orderId.toString(),
                                            },
                                        ],
                                        application_context: { shipping_preference: "NO_SHIPPING" },
                                    });
                                },
                                onApprove: function (data, actions) {
                                    return actions.order.capture().then(function (orderData) {
                                        _this.verifyPayment({
                                            id: _this.orderId,
                                            paymentSerialNum: orderData.id,
                                            cartIdList: _this.selectCartList.map((item) => item.id),
                                            cartUuid: _this.cartUuid,
                                            givingQuotation: _this.currentCurrency.rate,
                                            currency: _this.symbolCode,
                                            countryId: _this.customCountryId,
                                        });
                                    });
                                },
                                onCancel: function () {
                                    _this.toOrderDetail();
                                },
                                onError: function (error) {
                                    //支付失败的情况
                                    let failData = {
                                        oid: _this.isChildOrder ? _this.orderId : _this.mainOrderId,
                                        paymentMethod: "Paypal",
                                        message: JSON.stringify(error),
                                    };
                                    orderPayFail(failData);
									try {
                                        //记录PayPel支付错误日志
                                        let date=getDate();
                                        let time = new Date().toTimeString().substring(0,8);
                                        let logDate=date+' '+time
                                        let errorLogsData={
                                                oid: _this.isChildOrder ? _this.orderId : _this.mainOrderId,
                                                payType: 1,
                                                errorMsg: JSON.stringify(error),
                                                errorType:2,
                                                logDate
                                        }
                                        paymentErrorLogs(errorLogsData);
                                    }catch(e){
                                        console.log(e);
                                    }
                                },
                            });
                            if (button.isEligible()) {
                                button.render("#paypal-button-container").catch(() => {});
                            }
                        });
                    })
                    .catch((error) => {
                        this.$toast.error("failed to load the PayPal JS SDK script");
                    });
        },

        // 预支付
        async getPayIntent() {
            let orderData = {
                orderId: this.orderId,
                source: "designer",
                currency: this.symbolCode,
                currencyRate: this.currentCurrency.rate,
                countryId: this.customCountryId,
            };
            if (this.isChildOrder) {
                // GooglePay测试接口，金额最小
                // const {data} = await minAmountIntent(orderData);
                const { data } = await childPaymentIntent(orderData);
                this.paymentSerialNum = data.paymentSerialNum;
                this.clientSecret = data.clientSecret;
            } else {
                // GooglePay测试接口，金额最小
                // const {data} = await minAmountIntent(orderData);
                const { data } = await paymentIntent(orderData);
                this.paymentSerialNum = data.paymentSerialNum;
                this.clientSecret = data.clientSecret;
            }
        },

        // stripe初始化
        async initStripePay() {
            try {
                let stripeAccountId = this.stripeAccountId;
                // 初始化Stripe.js
                const stripe = await loadStripe(this.stripeKey, stripeAccountId ? { stripeAccount: stripeAccountId } : "");
                const options = {
                    clientSecret: this.clientSecret,
                    // 自定义样式
                    appearance: {},
                    locale: "en",
                };
                const elements = stripe.elements(options);
                this.elements = elements;
                const paymentElement = elements.create("payment");
                paymentElement.mount("#payment-element");
                let form = document.querySelector("#payment-form");
                this.$nextTick(() => {
                    this.setLoading(false);
                });
                form.addEventListener("submit", async (e) => {
                    e.preventDefault();
                    this.setLoading(true);
                    let data = {
                        id: this.orderId,
                        paymentSerialNum: this.paymentSerialNum,
                        cartIdList: this.selectCartList.map((item) => item.id),
                        cartUuid: this.cartUuid,
                        givingQuotation: this.currentCurrency.rate,
                        currency: this.symbolCode,
                        countryId: this.customCountryId,
                    };
                    const { error, paymentIntent } = await stripe.confirmPayment({
                        elements,
                        confirmParams: {
                            return_url: `${window.location.origin}/stripeComplete?data=${JSON.stringify(data)}`,
                        },
                        redirect: "if_required",
                    });
                    if (error) {
                        console.log(error);
                        let errorType = error.type;
                        if (errorType === "validation_error" || errorType === "card_error") {
                            this.$toast.error(error.message);
                        } else {
                            //stripe 支付失败
                            let failData = {
                                oid: this.isChildOrder ? this.orderId : this.mainOrderId,
                                paymentMethod: "Stripe",
                                message: JSON.stringify(error),
                            };
                            orderPayFail(failData);
                            this.$toast.error(error.message?error.message:"An unexpected error occurred.");
							try {
                                //记录stripe支付错误日志
                                let date=getDate();
                                let time = new Date().toTimeString().substring(0,8);
                                let logDate=date+' '+time
                                let errorLogsData={
                                        oid: this.isChildOrder ? this.orderId : this.mainOrderId,
                                        payType: 2,
                                        errorMsg: JSON.stringify(error),
                                        errorType:2,
                                        logDate
                                    }
                                paymentErrorLogs(errorLogsData);
                            }
                            catch(e) {
                                console.log(e);
                            }
                        }
                        this.setLoading(false);
                    }
                    if (paymentIntent) {
                        switch (paymentIntent.status) {
                            case "succeeded":
                                this.verifyPayment(data);
                                break;
                            case "processing":
                                this.verifyPayment(data);
                                break;
                            default:
                                this.$toast.error("An unexpected error occurred.");
                                this.setLoading(false);
                        }
                    }
                });
            } catch (e) {
                console.log(e);
            }
        },

        // Show a spinner on payment submission
        setLoading(isLoading) {
            if (isLoading) {
                // Disable the button and show a spinner
                document.querySelector("#submit").disabled = true;
                document.querySelector("#spinner").classList.remove("hidden");
                document.querySelector("#button-text").classList.add("hidden");
                this.showProgress = true;
            } else {
                document.querySelector("#submit").disabled = false;
                document.querySelector("#spinner").classList.add("hidden");
                document.querySelector("#button-text").classList.remove("hidden");
                this.showProgress = false;
            }
        },

        async calcPrice() {
            //订单图稿过来的不用调用此接口
            if (this.isOrder || this.isArtWork) {
                return false;
            }
            try {
                let res,data;
                if(this.isBuyNow){
                    data={
                        productId: this.$route.query.productId,
                        quantity:this.$route.query.quantity,
                    }
                }else{
                    data={
                        cartUuid:this.cartUuid,
                        cartIdList:this.selectCartList.map((item) => item.id),
                    }
                }
                if (this.isLogin) {
                    res = await stylistCheckout(data);
                } else {
                    res = await stylistCheckoutNotLogin(data);
                }
                this.updatePrice(res.data);
                return res;
            } catch (e) {
                console.log(e,"e");
                await this.$router.push({
                    path: "/stylistCart",
                });
            }
        },
        getAddOrderData() {
            let data,
                    shippingAddressForm = this.shippingAddressForm,
                    billingAddressForm = this.billingAddressForm,
                    billingAddressStatus = this.billingAddressStatus;
            //如果用户没有地址，默认存储一个
            if (this.isLogin && this.userShippingAddressList.length === 0 && this.userApoAddressList.length === 0) {
                saveAddress = 1;
            }
            let shippingState = this.currentShippingState?.stateName || shippingAddressForm.shippingState;
            let billState = this.currentBillingState?.stateName || billingAddressForm.billState;

            data = {
                isMobile: this.$store.getters.isMobile === false ? 0 : 1,
                email: this.inputEmail,
                cartUuid: this.cartUuid,
                apoAddress: shippingAddressForm.apoAddress,
                apoZip: shippingAddressForm.shippingApoZip,
                apoCity: shippingAddressForm.shippingApoCity ? shippingAddressForm.shippingApoCity.substring(0, 3) : "",
                apoState: shippingAddressForm.shippingApoState ? shippingAddressForm.shippingApoState.substring(0, 2) : "",
                shippingFirstName: shippingAddressForm.shippingFirstName,
                shippingLastName: shippingAddressForm.shippingLastName,
                shippingAddressLine1: shippingAddressForm.shippingAddressLine1,
                shippingAddressLine2: shippingAddressForm.shippingAddressLine2,
                shippingCountryCode: this.currentShippingCountry?.code,
                shippingPhoneNumber: this.shippingPrefixNumber + "-" + shippingAddressForm.shippingPhoneNumber,
                shippingCity: shippingAddressForm.shippingCity,
                shippingState: shippingState,
                shippingStateId: shippingAddressForm.shippingStateId,
                shippingCountry: this.currentShippingCountry?.countryName,
                shippingCountryId: shippingAddressForm.shippingCountryId,
                shippingZipCode: shippingAddressForm.shippingZipCode,
                billFirstName: billingAddressStatus === 0 ? shippingAddressForm.shippingFirstName : billingAddressForm.billFirstName,
                billLastName: billingAddressStatus === 0 ? shippingAddressForm.shippingLastName : billingAddressForm.billLastName,
                billAddressLine1: billingAddressStatus === 0 ? shippingAddressForm.shippingAddressLine1 : billingAddressForm.billAddressLine1,
                billAddressLine2: billingAddressStatus === 0 ? shippingAddressForm.shippingAddressLine2 : billingAddressForm.billAddressLine2,
                billCountryCode: billingAddressStatus === 0 ? this.currentShippingCountry?.code : this.currentBillingCountry?.code,
                billPhoneNumber: billingAddressStatus === 0 ? this.shippingPrefixNumber + "-" + shippingAddressForm.shippingPhoneNumber : this.billingPrefixNumber + "-" + billingAddressForm.billPhoneNumber,
                billCity: billingAddressStatus === 0 ? shippingAddressForm.shippingCity : billingAddressForm.billCity,
                billState: billingAddressStatus === 0 ? shippingState : billState,
                billStateId: billingAddressStatus === 0 ? shippingAddressForm.shippingStateId : billingAddressForm.billStateId,
                billCountry: billingAddressStatus === 0 ? this.currentShippingCountry?.countryName : this.currentBillingCountry?.countryName,
                billCountryId: billingAddressStatus === 0 ? shippingAddressForm.shippingCountryId : billingAddressForm.billCountryId,
                billZipCode: billingAddressStatus === 0 ? shippingAddressForm.shippingZipCode : billingAddressForm.billZipCode,
                paymentMethodId: this.paymentMethodId,
                paymentMethod: this.paymentMethodText,
                deferredPaymentStatus: this.payLater,
                isSmsSubscriptions: this.isSMSCheckbox === true ? 1 : 0,
                currency: this.symbolCode,
            }
            if(this.isBuyNow){
                data.productId=this.$route.query.productId;
                data.quantity=this.$route.query.quantity;
            }else{
                data.cartIdList=this.selectCartList.map((item) => item.id);
            }
            return data;
        },
        async addOrder() {
            if (this.orderId) {
                return;
            }
            let res;
            if (this.isLogin) {
                res = await stylistAddOrder(this.getAddOrderData());
                this.orderId = res.data.oid;
            } else {
                res = await stylistAddOrderNotLogin(this.getAddOrderData());
                this.orderId = res.data.oid;
            }
            this.$router.replace({
                path: this.$route.path,
                query: {
                    orderid: this.orderId,
                    type: "designer",
                },
            });
        },
        getAddressList() {
            if (!this.isLogin) {
                return Promise.resolve();
            }
            let formatAddress = (item) => {
                return `${item.city},${item.state},${item.zipCode},${item.country}`;
            };
            let formatApoAddress = (item) => {
                try {
                    return JSON.parse(item.addressLine)[1];
                } catch (e) {
                    return item.addressLine;
                }
            };
            return new Promise((resolve) => {
                getAddressList().then((res) => {
                    let userShippingAddressList = res.data.shippingAddress;
                    let userApoAddressList = res.data.apoFpoAddress;
                    userShippingAddressList.forEach((item) => {
                        item.customShowAddress = formatAddress(item);
                    });
                    userApoAddressList.forEach((item) => {
                        item.customShowAddress = formatApoAddress(item);
                    });
                    this.userShippingAddressList = userShippingAddressList;
                    this.userApoAddressList = userApoAddressList;
                    resolve(res.data);
                });
            });
        },
        setDefaultPaymentMethod(id) {
            let payTypeList = this.payTypeList;
            if (!payTypeList) {
                return false;
            }
            let findPayment = payTypeList.find((item) => {
                return item.id === id;
            });
            if (findPayment) {
                this.paymentMethodId = findPayment.id;
                this.payTypeId = findPayment.payType.id;
            } else {
                this.paymentMethodId = payTypeList[0].id;
                this.payTypeId = payTypeList[0].payType.id;
            }
            this.changePaymentMethod(this.paymentMethodId);
        },
        getCartList() {
            return new Promise((resolve) => {
                getAllShoppingCart({
                    cartUuid: this.isLogin ? null : this.$store.state.userUUID,
                    userId:this.isLogin ? this.$store.state.userInfo?.id : null,
                }).then((res) => {
                    resolve(res);
                });
            });
        },
        async getMyOrderInfo(data) {
            let res;
            if (this.isLogin) {
                res = await stylistGetOrderInfo(data);
            } else {
                res = await stylistGetOrderInfoNotLogin(data);
            }
            this.aId = res.data.id;
            this.orderType = res.data.orderType;

            if (res.data.crowdDiscountPrice !== 0) {
                this.nCouponCodeTemp = res.data.crowdCode;
                this.disabledCouponCode = true;
                this.radioGroup.push(1);
            }
            let find = this.countryList.find((item) => item.id === res.data.shippingCountryId);
            if (find) {
                this.taxName1 = find.taxName === null ? "Tax Price" : find.taxName;
                this.VATtext = find.continentId === 5 ? true : false;
            }

            if (res.data.paymentStatus === 2) {
                res.data.childOrder.forEach((item) => {
                    this.freightCharge = item.addFee;
                    this.markupPercentage = item.shippingPrice;
                    this.addFeePercentage = item.addFeePercentage;
                });
            } else {
                this.freightCharge = res.data.addFee;
                this.markupPercentage = res.data.shippingPrice;
                this.addFeePercentage = res.data.addFeePercentage;
            }

            if (res.data.email) {
                let params = {
                    email: res.data.email,
                    proId: this.proId,
                };
                getSmsSubscriptionByMail(params).then((res) => {
                    if (res.data === null || (res.data && res.data.isSmsSubscriptions == 0)) {
                        this.showSMStext = true;
                    } else {
                        this.showSMStext = false;
                    }
                });
            }
            return res;
        },
        //判断是否有未支付的订单
        findPendingPaymentOrder(res) {
            //判断是否存在子订单，找到未支付的子订单
            let findChildOrder;
            this.orderProductList = res.data.orderProductList;
            if (res.data.childOrder && res.data.childOrder.length) {
                findChildOrder = res.data.childOrder.find((item) => {
                    return item.paymentStatus == 1;
                });
                console.log(findChildOrder);
                if (findChildOrder) {
                    //子订单的产品展示
                    res.data.childOrder.forEach((citem) => {
                        this.orderProductList = this.orderProductList.concat(citem.ordersProducts || []);
                    });
                    this.isChildOrder = true;
                }
            }
            //如果不存在未支付的子订单并且主订单已支付或者后付   || res.data.paymentStatus === 0
            if (!findChildOrder && res.data.paymentStatus === 2) {
                return false;
            }
            this.mainOrderId = res.data.oid;
            this.orderId = findChildOrder ? findChildOrder.oid : res.data.oid;
            this.orderGivingQuotation = res.data.givingQuotation; //订单汇率
            this.orderCurrency = res.data.currency; //订单货币code
            this.orderCurrencySymbol = res.data.currencySymbol; //订单货币符号
            this.waitPaymentOrder = findChildOrder ? findChildOrder : res.data;
            return true;
        },
        backFillAddress(data) {
            let cityName, stateName;
            this.APOCityList.forEach((item) => {
                if (item.name.substring(0, 3) === data.apoCity) {
                    cityName = item.name;
                }
            });
            this.APOStateList.forEach((item) => {
                if (item.name.substring(0, 2) === data.apoState) {
                    stateName = item.name;
                }
            });
            this.shippingAddressForm = {
                shippingFirstName: data.shippingFirstName,
                shippingLastName: data.shippingLastName,
                shippingAddressLine1: data.shippingAddressLine1,
                shippingAddressLine2: data.shippingAddressLine2,
                shippingCountryCode: data.shippingCountryCode,
                shippingPhoneNumber: data.shippingPhoneNumber,
                shippingCity: data.shippingCity,
                shippingState: data.shippingState,
                shippingStateId: data.shippingStateId,
                shippingCountry: data.shippingCountry,
                shippingCountryId: data.shippingCountryId,
                shippingZipCode: data.shippingZipCode,
                apoAddress: data.apoAddress,
                email: data.email,
                shippingApoZip: data.apoZip,
                shippingApoCity: cityName,
                shippingApoState: stateName,
                shippingCompany: data.shippingCompany,
            };
            if (data.shippingAddressLine2) {
                this.shippingAddressLine2 = true;
            }
            this.billingAddressForm = {
                billFirstName: data.billFirstName,
                billLastName: data.billLastName,
                billAddressLine1: data.billAddressLine1,
                billAddressLine2: data.billAddressLine2,
                billCountryCode: data.billCountryCode,
                billPhoneNumber: data.billPhoneNumber,
                billCity: data.billCity,
                billState: data.billState,
                billStateId: data.billStateId,
                billCountry: data.billCountry,
                billCountryId: data.billCountryId,
                billZipCode: data.billZipCode,
            };
            if (data.billAddressLine2) {
                this.billingAddressLine2 = true;
            }
            const checkAddressIsAlike = () => {
                return data.shippingAddressLine1 === data.billAddressLine1 && data.shippingAddressLine2 === data.billAddressLine2;
            };
            if (!checkAddressIsAlike()) {
                this.billingAddressStatus = 1;
            }
            try {
                if (data.shippingPhoneNumber.includes("-") || data.billPhoneNumber.includes("-")) {
                    this.shippingPrefixNumber = data.shippingPhoneNumber.split("-")[0];
                    this.shippingAddressForm.shippingPhoneNumber = data.shippingPhoneNumber.split("-")[1];
                    this.billingPrefixNumber = data.billPhoneNumber.split("-")[0];
                    this.billingAddressForm.billPhoneNumber = data.billPhoneNumber.split("-")[1];
                } else {
                    this.shippingPrefixNumber = "1";
                    this.shippingAddressForm.shippingPhoneNumber = data.shippingPhoneNumber;
                    this.billingPrefixNumber = "1";
                    this.billingAddressForm.billPhoneNumber = data.billPhoneNumber;
                }
            } catch (e) {
                console.log(e);
            }
            this.userEmail = data.email;
            this.getState({
                id: data.shippingCountryId,
            }).then((res) => {
                this.shippingStateList = res;
            });
            this.getState({
                id: data.billCountryId,
            }).then((res) => {
                this.billStateList = res;
            });
            this.$refs.shippingAddressForm?.resetValidation();
        },
        getStripeKey() {
            getStripeKey({ proId: this.proId, countryId: this.customCountryId }).then((res) => {
                this.stripeKey = res.data;
            });
        },
        // 是否短信订阅(登录状态)
        getSmsSubscriptionLogin() {
            getSmsSubscriptionState().then((res) => {
                if (res.data === null || (res.data && res.data.isSmsSubscriptions == 0)) {
                    this.showSMStext = true;
                } else {
                    this.showSMStext = false;
                }
            });
        },
        // 是否短信订阅(未登录状态)
        getSmsSubscriptionNotLogin() {
            let params = {
                email: this.shippingAddressForm.email,
                proId: this.proId,
            };
            getSmsSubscriptionByMail(params).then((res) => {
                if (res.data === null || (res.data && res.data.isSmsSubscriptions == 0) ) {
                    this.showSMStext = true;
                } else {
                    this.showSMStext = false;
                }
            });
        },
    },
    created() {
        this.debounceGetSpecificAddress = debounce(this.getSpecificAddress, 300);
        this.debounceGetSpecificAddress2 = debounce(this.getSpecificAddress2, 300);
        this.debounceVerifyAddress = debounce(this.verifyAddress, 300);

        getLowestPrice({
            oid: this.linkOrderId,
            proId: this.proId,
        }).then((res) => {
            this.lowestPrice = res.data?.lowestPrice;
        });
        this.debounceCalcPrice = debounce(this.calcPrice, 500);
        this.getStripeKey();
        getAccountId().then((res) => {
            this.stripeAccountId = res.data.accountId;
        });
        if (this.isLogin) {
            this.getSmsSubscriptionLogin();
        }
    },
    async mounted() {
        this.$gl.show();
        //获取国家列表和用户地址列表
        await Promise.all([this.getCountry(), this.getAddressList()]);
        if (document.getElementById("mainContent")) document.getElementById("mainContent").style.height = window.innerHeight - 68 + "px";
        //获取支付方式
        let payData = await getPayTypeList();
        this.payTypeList = payData.data.payList;
        this.paypal_sandBox = payData.data.paypal_sandBox; //paypal支付环境
        this.authorize_sandBox = payData.data.authorize_sandBox; //信用卡支付环境
        //初始化支付配置
        this.initPaypalPaymentConfig();
        if (this.isOrder || this.isArtWork) {
            //获取订单信息
            let res = await this.getMyOrderInfo({
                oid: this.linkOrderId,
                getProductInfo: true,
                getAddressInfo: true,
                getDiscountInfo: true,
            });
            // 判断是否有模具费的订单
            this.isBuyAgainLowTips = this.$route.query.buyAgain && res.data.ordersProducts.find((ele) => ele.mouldPrice && ele.mouldPrice > 0) && this.lowestPrice && this.lowestPrice > 0;
            //判断是否有未支付的订单
            let result = this.findPendingPaymentOrder(res);
            if (result) {
                //更新价格
                this.updatePrice(this.waitPaymentOrder);
                //如果apo地址存在，切换地址tab
                if (res.data.apoAddress) {
                    this.addressStatus = 1;
                }
                await this.$nextTick();
                //地址回填,邮箱回填
                this.backFillAddress(res.data);
                //设置支付方式
                this.setDefaultPaymentMethod(res.data.paymentMethodId);
            } else {
                this.$toast.show("Order has been paid", {
                    duration: 0,
                    action: [
                        {
                            text: "YES",
                            onClick: (e, toastObject) => {
                                toastObject.goAway(0);
                                window.location.href = "/";
                            },
                        },
                    ],
                });
                this.isPaidOrder = true;
                this.$gl.hide();
                return false;
            }
        } else if (this.isBuyNow) {
            let list = await this.calcPrice();
            this.tempCartList = list.data.orderProductList;
            this.setDefaultPaymentMethod();
            //计算价格
            await this.debounceCalcPrice();
        } else {
            //普通下单
            if (!this.selectCartList.length) {
                await this.$router.push({
                    path: "/stylistCart",
                });
                return false;
            }
            await this.debounceCalcPrice();
            this.setDefaultPaymentMethod();
        }
        await this.getAllCoupon();

        this.$gl.hide();

        //处理德语情况
        if (this.language === "de") {
            this.shippingPrefixNumber = "49";
            this.billingPrefixNumber = "49";
            this.shippingAddressForm.shippingCountryId = 81;
            this.billingAddressForm.billCountryId = 81;
        }
    },
};
</script>

<style lang="scss">
.discount-item-div {
    display: flex;

    .discount {
        display: flex;
        align-items: center;
        justify-content: space-between;

        & > div:first-child {
            flex: 0 0 370px;
        }
    }

    .con {
        color: #de3500;
        width: 100%;
        text-align: right;
    }

    & > div:first-child {
        flex: 0 0 240px;
    }

    @include respond-to(mb) {
        flex-wrap: wrap;
        & > div:first-child {
            flex: 0 0 210px;
        }

        .discount {
            & > div:first-child {
                flex: 0 0 250px;
            }
        }
    }
}

.payBeforeDialog {
    .v-card__title {
        font-size: 20px !important;
        font-weight: 700 !important;
        @include respond-to(mb) {
            font-size: 16px !important;
        }
    }

    .v-card__text {
        font-size: 16px;
        color: #333333 !important;
        @include respond-to(mb) {
            font-size: 14px;
        }
    }

    .small-title {
        font-size: 18px;
        @include respond-to(mb) {
            font-size: 16px;
        }
    }

    .custom-row {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        grid-gap: 10px;
        align-items: center;
        @include respond-to(mb) {
            grid-template-columns: repeat(1, 1fr);
        }

        .item {
            min-width: 0;
        }

        .item:first-child span:first-child {
            width: 120px;
            text-align: right;
        }
    }

    .custom-row2 {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        grid-gap: 10px;
        align-items: center;

        .item {
            min-width: 0;
        }

        .item:first-child span:first-child {
            width: 120px;
            text-align: right;
        }

        @include respond-to(mb) {
            grid-template-columns: repeat(1, 1fr);
        }
    }

    .addressInfo .addressItem .item:first-child span:first-child {
        width: 120px;
        text-align: right;
        @include respond-to(mb) {
            width: auto;
        }
    }

    .addressInfo .item {
        display: flex;
        align-items: center;

        span:first-child {
            margin-right: 4px;
        }

        span:last-child {
            flex: 1;
            padding: 5px;
            border-radius: 2px;
            opacity: 1;
            background: #efefef;
            color: #666666;
        }
    }

    .foot {
        display: flex;
        justify-content: center;
        padding-bottom: 20px !important;

        button {
            width: 260px;
            margin: 0 10px;
            text-transform: capitalize;
            font-size: 14px;
        }

        #paypal-button-container {
            width: 260px;
        }

        @include respond-to(mb) {
            flex-direction: column;
            button {
                width: 80%;
                margin: 0 0 5px !important;
            }
            #paypal-button-container {
                width: 80%;
            }
        }
    }
}
</style>

<style scoped lang="scss">
[disabled] {
    pointer-events: none; //鼠标点击不可修改
}


.sms {
    ::v-deep .v-input__control .v-input__slot {
        padding: 0 !important;
    }

    ::v-deep .v-label {
        max-width: 100% !important;
    }
}

.checkoutBtn {
    width: 343px;
    height: 43px;
    background: linear-gradient(180deg, #ffffff 0%, #f9a128 0%, #ff6c00 100%);
    border-radius: 4px;
    color: #fff;
    font-size: 18px;

    &.disabled {
        background: #ccc;
        pointer-events: none;
        cursor: not-allowed;
    }
}

.SMStextMb{
    display: none;

    @include respond-to(mb) {
        display: flex;
        align-items: center;
        color: #757575;

        .icon{
            position: absolute;
            right: -7px;
            top: -7px;
            border-radius: 50%;
            border: 1px solid #959595;
            background: #fff;
            cursor: pointer;
            width: 20px;
            height: 20px;
            display: flex;
            justify-content: center;
            align-items: center;

            b{
                color:#959595;
            }
            @include respond-to(mb) {
                position: absolute;
                right: -4px;
                top: -3px;
            }
        }

        ::v-deep .v-input--checkbox {
            margin: 0;
            position: relative;
            top: 3px;

            .v-input__control .v-messages {
                display: none;
            }
        }

    }
}


.bullNum {
    position: relative;

    .SMStextPC{
        position: absolute;
        top: 47px;
        left: -80px;
        z-index: 1;
        display: flex;
        align-items: center;
        font-size: 12px;
        font-family: Calibri;
        color: #6f7070;
        // border: 1px solid #898989;
        // background-color: #fffcfc;
        // border-radius: 4px;
        // padding: 8px 7px;

        @include respond-to(mb) {
            display: none;
        }

        .icon{
            position: absolute;
            right: -7px;
            top: -7px;
            border-radius: 50%;
            border: 1px solid #959595;
            background: #fff;
            cursor: pointer;
            width: 20px;
            height: 20px;
            display: flex;
            justify-content: center;
            align-items: center;

            b{
                color:#959595;
            }
            @include respond-to(mb) {
                position: absolute;
                right: -4px;
                top: -3px;
            }
        }

        // &::before {
        // 	position: absolute;
        // 	top: -20px;
        // 	left: 155px;
        // 	content: "";
        // 	border-top: 10px transparent dashed;
        // 	border-left: 10px transparent dashed;
        // 	border-right: 10px transparent dashed;
        // 	border-bottom: 10px #757474 solid;
        // }

        // &::after {
        // 	position: absolute;
        // 	top: -19px;
        // 	left: 155px;
        // 	content: "";
        // 	border-top: 10px transparent dashed;
        // 	border-left: 10px transparent dashed;
        // 	border-right: 10px transparent dashed;
        // 	border-bottom: 10px #fffcfc solid;
        // }

        ::v-deep .v-input--checkbox {
            margin: 0;
            position: relative;
            top: 3px;

            .v-input__control .v-messages {
                display: none;
            }
        }

        // ::v-deep .v-icon.v-icon {
        // 	font-size: 35px;
        // 	@include respond-to(mb) {
        // 		font-size: 30px;
        // 	}
        // }
    }


    img {
        width: 13px;
        position: absolute;
        z-index: 1;
        bottom: 0;
        right: 0;

        @include respond-to(mb) {
            width: 11px;
        }
    }

    .bill {
        border-radius: 5px 0 0 5px;
        position: relative;

        ::v-deep .v-input__slot {
            padding: 0 5px !important;
        }

        ::v-deep .v-text-field__details {
            width: 50px;
            padding: 0 !important;
        }
    }

    .bill3_img {
        width: 13px;
        position: absolute;
        z-index: 1;
        left: 44px;
        top: 25px;
        @include respond-to(mb) {
            width: 11px;
            position: absolute;
            z-index: 1;
            left: 41px;
            top: 28px;
        }
    }

    .bill3 {
        border-radius: 5px 0 0 5px;
        position: relative;
        @include respond-to(mb) {
            ::v-deep .v-input__slot {
                padding: 0 7px;
            }
        }
    }

    .bill2 {
        border-radius: 0 5px 5px 0;
    }
}

.bullNum2 {
    .bill3 {
        ::v-deep .v-input__slot {
            padding: 0 6px !important;
        }
    }

    .bill2 {
        border-radius: 0 5px 5px 0;
        width: 55%;
    }
}

.border-bottom {
    border-bottom: 1px solid #e5e5e5;
}

.border-2 {
    height: 1px;
    border-top: 1px dashed #dbdbdb;
}

.checkoutPage {
    @include respond-to(mb) {
        .v-input {
            font-size: 12px;
        }
    }
}

.bps-container {
    overflow: hidden auto;
    display: grid;
    grid-template-columns: 5fr 7fr;
    grid-gap: 50px;
    align-items: flex-start;

    .hint {
        font-size: 12px;
        color: #999999;
    }

    ::v-deep {
        .v-radio .v-label {
            color: #333333;
            display: block;
        }

        .v-text-field.v-text-field--solo.v-input--dense > .v-input__control {
            min-height: 34px;
        }

        .v-text-field.v-text-field--solo:not(.v-text-field--solo-flat) > .v-input__control > .v-input__slot {
            box-shadow: 0px 2px 4px 0px rgba(204, 204, 204, 0.35);
            border: 1px solid #d0d5dd;
        }
    }

    .box-title {
        font-size: 18px;
        font-weight: 700;
    }

    .payBtnWrap {
        position: sticky;
        bottom: 0;
        left: 0;
        right: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 80px;
        padding: 10px 0;
        background-color: #f2f4f5;
        border-top: 1px solid #dbdbdb;
        z-index: 2;

        .label {
            margin-right: 10px;
            font-size: 24px;
        }

        .symbol {
            padding: 0 4px;
            font-size: 18px;
        }

        .unPaidPrice {
            font-size: 24px;
            color: #de3500;
        }
    }

    .left-box {
        .cusTitle {
            font-size: 24px;
        }
    }

    .right-box {
        display: flex;
        flex-direction: column;
        position: relative;
        max-height: 100%;
        overflow: hidden auto;
        padding: 15px 20px 0;
        background: #f2f4f5;

        .totalPrice {
            color: #de3500;
            font-size: 24px;
        }

        .delivery-box {
            display: none;
        }

        .coupoun-tip {
            img {
                width: 40px;
                margin-right: 10px;
            }

            color: #de3500;
            padding: 5px 0;
            font-size: 12px;
            display: flex;
            align-items: center;
        }
    }

    .pay-box {
        margin-bottom: 0;

        ::v-deep .v-label {
            padding-bottom: 10px;
        }

        .myFileset {
            border: 1px solid #d0d5dd;
            box-shadow: 0px 2px 4px 0px rgba(204, 204, 204, 0.35);
            border-radius: 6px;
            padding: 0 10px 10px;
            background-color: #ffffff;
            height: 54px;

            legend {
                font-size: 14px;
                padding: 0 2px;
                font-weight: 700;
                background: #ffffff;
                color: #333333;
                text-transform: uppercase;
            }

            img {
                height: 25px;
                object-fit: contain;
            }
        }

        .paymentBtn {
            flex: 0 0 33.3%;
            margin-right: 10px;
            margin-bottom: 10px;

            &:last-child {
                margin-right: 0;
            }
        }
    }

    .cart-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 10px 0;
        border-bottom: 1px solid #e5e5e5;

        .imgWrap {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 10px;
            height: 83px;
            background: #fafafa;
            border: 1px solid #e5e5e5;
            border-radius: 4px;

            img {
                object-fit: contain;
            }
        }

        .imgWrap2 {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 10px;
            height: 83px;
        }

        .item-p {
            width: 20%;
            text-align: center;

            img {
                width: 100%;
                height: 100%;
                border-radius: 5px;
            }

            &.t1 {
                .cartInfo {
                    display: none;
                }
            }

            &.t2 {
                width: 40%;
            }

            &.t3,
            &.t4,
            &.t5 {
                .t {
                    margin-bottom: 5px;
                }
            }
        }

        .c {
            text-align: center;
        }

        .originUnitPrice {
            font-size: 14px;
        }

        .discountUnitPrice {
            font-size: 18px;
            margin-left: 4px;
        }

        .discount {
            color: #de3500;
        }
    }

    .cart-item2 {
		border-bottom: 1px solid #e5e5e5;

		.giveawayClass{
			font-family: Google Sans;
			font-weight: 400;
			font-size: 14px;
			color: #FFFFFF;
			background: linear-gradient(90deg, #4A48FF 0%, #B012FB 100%);
			border-radius: 2px 18px 2px 18px;
			padding: 5px;
			width: 75px;
    		margin: 0 auto 10px;
		}

        @media screen and (max-width: $mb-width) {
            border-bottom: 1px solid #e5e5e5 !important;
            max-height: 65px;

			.giveawayClass{
				font-size: 10px;
				width: 67px;
				padding: 4px;
			}
        }
    }

    .cart-box {
        overflow: hidden auto;
        margin-bottom: 20px;
        max-height: 315px;
    }

    .cart-box,
    .delivery-box {
        border-bottom: none;
    }

    @media screen and (max-width: $mb-width) {
        height: auto !important;
        grid-template-columns: 1fr;
        grid-gap: 0;
        background-color: #e1e3e6;
        padding: 5px;
        .box-title {
            font-size: 14px;
        }
        .payBtnWrap {
            flex-wrap: wrap;
            height: auto;
            position: relative;
            padding: 10px;
            border: none;
            border-bottom-left-radius: 5px;
            border-bottom-right-radius: 5px;
            margin: 0 !important;
            font-size: 12px;
            background-color: #ffffff;

            &::before {
                content: "";
                position: absolute;
                top: 0;
                left: 10px;
                right: 10px;
                height: 1px;
                background-color: #e6e6e6;
            }

            .b1,
            .b2 {
                flex-basis: 100%;
                justify-content: center;
            }

            .label {
                margin-right: 5px;
                font-size: 14px;
            }

            .symbol {
                font-size: 12px;
            }

            .unPaidPrice {
                font-size: 16px;
            }

            .checkoutBtn {
                width: 100%;
                height: 35px;
                font-size: 12px;
                margin: 0 10px;
            }
        }
        .left-box {
            background: #ffffff;
            border-radius: 10px;
            padding: 10px;
            margin-bottom: 0;

            .cusTitle {
                font-size: 16px;
            }

            .yourPosition {
                display: none;
            }

            .delivery-box {
                display: none;
            }

            .cart-box {
                max-height: none;
            }

            .item-p.t5 {
                .c {
                    color: #de3500;
                }
            }
        }
        .right-box {
            overflow: hidden;
            padding: 0;
            background-color: transparent;
            max-height: none;

            ::v-deep .v-label {
                font-size: 12px;
            }

            .delivery-box {
                display: block;
            }

            .pay-box {
                margin-top: 0;
                margin-bottom: 5px;
            }

            .shipping-box {
                margin-bottom: 5px !important;
            }

            .billing-box {
                padding-bottom: 10px !important;
                margin-bottom: 0 !important;
            }

            .subtotal-con {
                padding: 10px;
                border: none;
                border-top-left-radius: 5px;
                border-top-right-radius: 5px;
                margin-top: 5px;
                font-size: 12px;
                background-color: #ffffff;
            }

            .box {
                padding: 10px;
                margin: 5px 0;
                border: none;
                border-radius: 5px;
                font-size: 12px;
                background-color: #ffffff;
            }

            .pay-box {
                .paymentBtn {
                    flex: 0 0 100%;
                    margin-right: 0;
                    margin-bottom: 10px;
                }
            }

            .subtotal-con .sub-item {
                font-size: 12px;
            }
        }
        .cart-item {
            font-size: 12px;

            &:last-child {
                border-bottom: none;
            }

            .item-p {
                &.t1 {
                    display: flex;

                    .imgWrap {
                        height: 52px;

                        img {
                            object-fit: contain;
                        }
                    }

                    .cartInfo {
                        display: block;
                        align-self: center;
                        margin-left: 10px;
                        font-size: 12px;
                        text-align: left;

                        .info1 {
                            font-size: 14px;
                            font-weight: 700;
                        }

                        .info2 {
                            margin: 8px 0;
                        }

                        .info3 {
                            display: flex;

                            div:first-child {
                                margin-right: 10px;
                            }
                        }
                    }
                }

                .discountUnitPrice {
                    font-size: 14px;
                }
            }
        }
        .cart-box {
            margin-bottom: 0;
        }
        .pay-box {
            .myFileset {
                padding: 0 5px 5px;

                legend {
                    font-weight: 400;
                    padding: 0;
                }

                img {
                    height: 15px;
                }
            }

            .paymentBtn {
                flex: 0 0 33.3%;
                margin-right: 10px;
                margin-bottom: 10px;

                &:last-child {
                    margin-right: 0;
                }
            }
        }
    }
}

.billing-box {
    border-bottom: 1px solid #dbdbdb;
}

.paymentBtn.active {
    border: 1px solid #1e88e5 !important;
}

/* Variables */
* {
    box-sizing: border-box;
}

.stripeForm {
    width: 100%;
    //width: 30vw;
    //min-width: 500px;
    align-self: center;
    box-shadow: 0px 0px 0px 0.5px rgba(50, 50, 93, 0.1), 0px 2px 5px 0px rgba(50, 50, 93, 0.1), 0px 1px 1.5px 0px rgba(0, 0, 0, 0.07);
    border-radius: 7px;
    padding: 40px;
}

.hidden {
    display: none;
}

#payment-form {
    box-shadow: none;
}

#payment-element {
    margin-bottom: 24px;
}

.btnWrap {
    display: flex;
    justify-content: center;
    align-items: center;
}

/* Buttons and links */
.stripeButton {
    background: #5469d4;
    font-family: Arial, sans-serif;
    color: #ffffff;
    border-radius: 4px;
    border: 0;
    padding: 12px 16px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    display: block;
    transition: all 0.2s ease;
    box-shadow: 0px 4px 5.5px 0px rgba(0, 0, 0, 0.07);
    width: 100%;
    margin: 4px;
}

.stripeButton:hover {
    filter: contrast(115%);
}

.stripeButton:disabled {
    opacity: 0.5;
    cursor: default;
}

.stripeButton.cancelBtn {
    background-color: #f5f5f5;
    color: #333333;
}

/* spinner/processing state, errors */
.spinner,
.spinner:before,
.spinner:after {
    border-radius: 50%;
}

.spinner {
    color: #ffffff;
    font-size: 22px;
    text-indent: -99999px;
    margin: 0px auto;
    position: relative;
    width: 20px;
    height: 20px 5.3333vw;
    box-shadow: inset 0 0 0 2px;
    -webkit-transform: translateZ(0);
    -ms-transform: translateZ(0);
    transform: translateZ(0);
}

.spinner:before,
.spinner:after {
    position: absolute;
    content: "";
}

.spinner:before {
    width: 10.4px;
    height: 20.4px;
    background: #5469d4;
    border-radius: 20.4px 0 0 20.4px;
    top: -0.2px;
    left: -0.2px;
    -webkit-transform-origin: 10.4px 10.2px;
    transform-origin: 10.4px 10.2px;
    -webkit-animation: loading 2s infinite ease 1.5s;
    animation: loading 2s infinite ease 1.5s;
}

.spinner:after {
    width: 10.4px;
    height: 10.2px;
    background: #5469d4;
    border-radius: 0 10.2px 10.2px 0;
    top: -0.1px;
    left: 10.2px;
    -webkit-transform-origin: 0px 10.2px;
    transform-origin: 0px 10.2px;
    -webkit-animation: loading 2s infinite ease;
    animation: loading 2s infinite ease;
}

#payment-request-button {
    width: 200px;
    height: 50px;

    button {
        background-color: #635bff;
    }

    button {
        background: #635bff;
        border-radius: 6px;
        color: white;
        border: 0;
        padding: 12px 16px;
        margin-top: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s ease;
        display: block;
    }

    button:hover {
        filter: contrast(115%);
    }

    button:active {
        transform: translateY(0px) scale(0.98);
        filter: brightness(0.9);
    }

    button:disabled {
        opacity: 0.5;
        cursor: none;
    }
}

#messages {
    display: none;
    //background-color: #0A253C;
    color: #00d924;
    padding: 0px;
    margin: 0px;
    font-size: 1em;
    width: 300px;
}

@-webkit-keyframes loading {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

@keyframes loading {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

.xin {
    color: #de3500;
}

.subtotal-con {
    .radio-group-div {
        background: #fff;
        padding: 10px;
        border: 1px solid #d0d5dd;
        border-radius: 4px;
        margin-top: 10px;

        ::v-deep .v-input--selection-controls {
            margin-top: 0;
        }

        @media screen and (max-width: $mb-width) {
            background: #f2f4f5;
            border-color: #d0d5dd;

            .v-radio {
                ::v-deep .v-label {
                    display: block;
                }
            }
        }
    }

    .sub-item {
        display: flex;
        align-items: center;
        margin-top: 5px;
        font-size: 16px;

        .label {
            flex-basis: 75%;
            text-align: left;
        }

        .con {
            flex: 1;
            text-align: right;
            color: #de3500;
        }
    }

    .discount-item {
        height: 45px;

        .v-radio {
            flex: 0 0 170px;
            margin-bottom: 0 !important;
            margin-right: 10px;
        }

        .discount {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex: 1;

            & > div:first-child {
                flex: 0 0 270px;
            }
        }

        .con {
            color: #de3500;
            width: 100%;
            text-align: right;
        }
    }

    .discount-item.dis2 {
        height: 30px;
    }

    .sub-item.subtotalPrice .con {
        color: #333333;
    }

    @media screen and (max-width: $mb-width) {
        .discount-item {
            &:first-child {
                button {
                    font-size: 12px;
                    min-width: 37px;
                    padding: 10px;
                }
            }

            .v-radio {
                flex: 0 0 140px;
            }

            .discount {
                & > div:first-child {
                    flex: 0 1 165px;
                }

                .v-input {
                    font-size: 12px;
                }

                .con {
                    text-align: right;
                    flex: 0 0 50px;
                    font-size: 12px;

                    .couponicon {
                        font-size: 25px;
                    }
                }
            }
        }
    }
}

.card_field {
    width: 100%;
    padding: 12px;
    border: 1px solid #00000061;
    border-radius: 6px;
    box-sizing: border-box;
    resize: vertical;
    height: 40px;
    background: white;
    font-size: 16px;
    color: #333333;
}

.mask {
    background: $bg-mask;
    position: fixed;
    z-index: 16777270;
    bottom: 0;
    right: 0;
    left: 0;
    top: 0;

    .menu-box {
        width: 380px;
        min-height: 100vh;
        padding-top: 550px;
        background: white;
        flex-direction: column;

        button {
            width: 80%;
            font-size: 26px;
            margin-top: 15px;
            line-height: 46px;
            font-weight: bold;
        }

        .login {
            color: white;
            background: $color-dark;
        }

        .register {
            color: $color-dark;
            text-decoration: underline;
        }
    }
}

@media screen and (max-width: $mb-width) {
    .btnWrap {
        flex-wrap: wrap;
    }
}

::v-deep .v-application--wrap {
    min-height: auto;
}
</style>