<template>
	<div id="table-show-header">
		<div class="item item-1"></div>
		<div class="item item-2"></div>
		<div class="item item-3"></div>
		<div class="item item-4"></div>
		<div class="item item-5"></div>
		<div class="item item-6"></div>
		<div class="item item-7"></div>
	</div>
</template>

<script>
export default {
	name: "TableShowHeader",
	data() {
		return {};
	},
};
</script>

<style lang="scss" scoped>
#table-show-header {
	width: 100%;
	display: flex;
	align-items: center;
}
.item {
	background: #D9D9D9;
  border-radius: .3125vw;
  height: 1.5625vw;
}
.item-1 {
	width: 3.0729vw;
	height: 1.5625vw;
	border-radius: 0.3125vw;
	margin-right: 3.0729vw;
}
.item-2 {
	width: 8.0208vw;
	height: 1.5625vw;
	margin-right: 2.9688vw;
}
.item-3 {
  width: 5.5208vw;
	margin-right: 1.7188vw;
}
.item-4 {
  width: 4.9479vw;
	margin-right: 1.5625vw;
}
.item-5 {
  width: 6.5625vw;
	margin-right: 1.875vw;
}
.item-6 {
  width: 5.1563vw;
	margin-right: 51px;
}
.item-7 {
  width: 7.0833vw;
}
</style>
