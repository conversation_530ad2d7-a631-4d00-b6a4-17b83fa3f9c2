<template>
	<div class="NeonDateBox">
		<template v-for="item in newQtyList">
			<div class="card" :class="{ active: item.id == value?.id }" :key="item.id" v-if="!item.isHidden" @click="handleClick(item)">
				<!-- <span v-if="!item.priceInfo.unitPercent"></span>
                <span v-else>{{((item.priceInfo.unitPercent-1)*100).toFixed(0)}}%</span> -->
				<template v-if="!smallQuote">
					<template v-if="byPriceType(item) === 0">
						<span v-if="proType !== 0">{{ lang.StandardFREE }}!</span>
						<span v-else>{{ lang.NoDiscount }}</span>
					</template>
					<template v-else>
						<DiscountText :itemData="item" :textConfig="textConfig"></DiscountText>
					</template>
				</template>

				<template v-else>
					{{ item.alias }}
				</template>
				<!-- <p class="title">{{ item.alias }}</p>
                副标题(可选) -->
				<p v-if="item.tips" class="sub-label">{{ item.tips }}</p>
				<Corner radius="0px 7px 0px 10px" :active="item.id === value?.id"></Corner>
			</div>
		</template>
	</div>
</template>

<script>
import Corner from "@/components/Neon/Corner";
import { getQuoteTime } from "@/assets/js/quote/quotePublic";
import DiscountText from "@/components/modal/Quote/QuoteComponents/DicountText.vue";

export default {
	name: "NeonDateBox",
	model: {
		prop: "value",
		event: "change",
	},
	props: {
		priceInfo: {
			type: Object,
			default: () => {},
		},
		value: {
			type: Object,
			default: () => {},
		},
		data_list: {
			type: Array,
			default: () => [
				{
					id: 1,
					label: "UK/IRELAND 230V",
					subLabel: "3-5 Weeks to Get Your Neon Signs",
				},
				{
					id: 2,
					label: "EUROPE 230V",
					subLabel: "3-5 Weeks to Get Your Neon Signs",
				},
			],
		},
		selected: {
			type: Number,
			default: () => 1,
		},
		color: {
			type: String,
			default: () => "#d88416",
		},
		neon: {
			type: String,
			default: () => "",
		},
		round: {
			type: String,
			default: () => "",
		},
		smallQuote: {
			type: Boolean,
			default: false,
		},
	},
	components: {
		DiscountText,
		Corner,
	},
	data() {
		return {
			priceType: [
				{
					label: "单价",
					value: "1",
					key: "unitPrice",
				},
				{
					label: "总价",
					value: "2",
					key: "totalPrice",
				},
				{
					label: "单价百分比",
					value: "3",
					key: "unitPercent",
				},
				{
					label: "总价百分比",
					value: "4",
					key: "totalPercent",
				},
				{
					label: "递增价格",
					value: "5",
					key: "increasePrice",
				},
				{
					label: "组合单价",
					value: "6",
					key: "composeUnitPrice",
				},
				{
					label: "面积递增价格",
					value: "7",
				},
			],
		};
	},
	methods: {
		byPriceType(citem) {
			let temp = this.priceType.find((x) => {
				return x.value == citem.priceInfo.priceType;
			}).key;
			return citem.priceInfo[temp];
		},
		handleClick(v) {
			this.$emit("change", v);
		},
		filterDiscount(v) {
			return `${v.priceInfo.unitPercent * 100}% ` + this.lang.discount;
		},
	},
	computed: {
		lang() {
			return this.$store.getters.lang.quote || {};
		},
		proType() {
			return this.$store.state.proType;
		},
		device() {
			return this.$store.state.device;
		},
		newQtyList() {
			let result = getQuoteTime(this.data_list, this.priceInfo, this.proType),
				originShowSmallPrice = this.$store.state.showSmallPrice;
			this.$store.commit("setShowSmallPrice", result.newShowSmallPrice);
			if (result.newShowSmallPrice !== originShowSmallPrice) {
				this.$Bus.$emit("clearDiscount");
			}
			return result.arr;
		},
        textConfig() {
            return {
                freeText: this.freeText ? this.freeText : this.lang.NoDiscount,
            };
        },
	},
};
</script>

<style lang="scss" scoped>
.NeonDateBox {
	display: grid;
	column-gap: 11px;
	row-gap: 11px;

	.card {
		padding: 13.5px 18px;
		border: 1px solid #dbdbdb;
		border-radius: 10px;
		position: relative;
		cursor: pointer;
		transition: all 0.3s ease-in-out;
		@media screen and(max-width: 1273px) {
			padding: 9px 10px;
		}

		.cTitle {
			font-size: 14px;
			@media screen and(max-width: 1273px) {
				font-size: 12px;
				padding-bottom: 8px;
			}
		}

		.sub-label {
			font-size: 14px;
			margin-top: 12px;
			@media screen and(max-width: 1273px) {
				font-size: 12px;
				margin-top: 0;
			}
			color: #999;
		}

		&:hover {
			box-shadow: 0 0 5px 0 #ccc;
		}

		&.active {
			border: 1px solid var(--neon-light-color);
			transition: all 0.3 ease-in-out;
		}
	}
}
</style>