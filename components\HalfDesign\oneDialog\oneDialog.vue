<template>
	<div class="oneDialog">
		<BaseDialog class="oneElDialog" :value="oneDialog" :model="false" :width="dialogWidth">
			<div class="oneDialogBox">
				<div class="oneDialogTitle">
					<span>{{ langSemiCustom.oneDialogTitle }}</span>
				</div>
				<div class="border_bottom"></div>
				<div class="oneDialogContent">
					<div class="oneText">{{ langSemiCustom.oneDialogText }}</div>
					<div class="oneText">{{ langSemiCustom.oneDialogText2 }}</div>
					<div class="selectBox">
						<v-radio-group v-model="radio">
							<v-radio :value="1">
								<template #label>
									<span class="radioText" :class="{ active: radio == 1 }">
										{{ langSemiCustom.selectPenRadio }}
									</span>
								</template>
							</v-radio>
							<v-radio :value="2">
								<template #label>
									<span class="radioText" :class="{ active: radio == 2 }">
										{{ langSemiCustom.selectPenRadio2 + " " + productSku }}
									</span>
								</template>
							</v-radio>
						</v-radio-group>
					</div>
				</div>
				<div class="dialog-footer">
					<button class="confirmBtn clickBtn" @click="handleConfirm">
						{{ langSemiCustom.confirm }}
					</button>
					<button class="cancelBtn clickBtn" @click="handleClose">{{ langSemiCustom.cancel }}</button>
				</div>
			</div>
			<template v-slot:closeIcon>
				<div @click="handleClose" class="closeIcon1">
					<b class="icon-guanbi"></b>
				</div>
			</template>
		</BaseDialog>
	</div>
</template>

<script>
import BaseDialog from "@/components/Quote/BaseDialog";
export default {
	name: "oneDialog",
	components: { BaseDialog },
	props: {
		oneDialog: {
			type: Boolean,
			default: false,
		},
		productSku: {
			type: String,
			default: "",
		},
	},
	data() {
		return {
			radio: -1,
		};
	},
	watch: {},
	computed: {
		langSemiCustom() {
			return this.$store.getters.lang?.semiCustom;
		},
		isMobile() {
			return this.$store.getters.isMobile;
		},
		dialogWidth() {
			let width = this.isMobile ? "90%" : "450px";
			return width;
		},
	},
	methods: {
		handleClose() {
			this.$emit("update:oneDialog", false);
		},
		handleConfirm() {
			if (this.radio == -1) {
				//  this.$toast.warning('Please select an option')
				this.$toast.info("Please select an option");
				return;
			}
			this.$emit("update:oneDialog", false);
			this.$emit("oneSelect", this.radio);
		},
	},
	created() {},
	mounted() {},
};
</script>

<style scoped lang="scss">
.oneDialog {
	.oneElDialog {
		.closeIcon1 {
			cursor: pointer;
			color: #000000;
			display: flex;
			justify-content: center;
			align-items: center;
			position: absolute;
			top: 5px;
			right: 5px;
			width: 30px;
			height: 30px;

			b {
				height: 24px;
				font-size: 13px;
				// color: #fff;
			}

			b::before {
				font-size: 14px;
			}
		}

		.oneDialogBox {
			padding: 0 25px;

			.oneDialogTitle {
				font-weight: bold;
				font-size: 16px;
				color: #000000;
				line-height: 36px;
				height: 100%;
				text-align: left !important;
				position: relative;
				display: flex;
				justify-content: space-between;
				align-items: center;
			}

			.border_bottom {
				width: calc(100% + 50px);
				margin-left: -25px;
				border-bottom: 1px solid #b7b7b7;
			}

			.oneDialogContent {
				padding: 20px 0;
			}

			.oneDialogContent {
				.oneText {
					font-weight: 400;
					font-size: 16px;
					color: #000000;
					line-height: 23px;
				}

				.selectBox {
					margin-top: 20px;

					::v-deep .v-input--selection-controls__input {
						margin-right: 0;

						.v-icon {
							font-size: 20px;
						}
					}

					.radioText {
						font-weight: bold;
						font-size: 16px;
						color: #333333;

						&.active {
							color: $color-primary;
						}
					}
				}
			}

			.dialog-footer {
				padding: 0px 0px 30px;
				display: flex;
				justify-content: center;
				align-items: center;
				column-gap: 10px;

				.clickBtn {
					width: 50%;
					font-size: 16px;
					background-color: rgb(255, 255, 255);
					border: 1px solid #dcdfe6;
					border-radius: 4px;
					box-sizing: border-box;
					cursor: pointer;
					display: inline-block;
					font-weight: 500;
					line-height: 1;
					margin: 0;
					margin-left: 0px;
					outline: 0;
					padding: 12px 20px;
					text-align: center;
					transition: 0.1s;
					white-space: nowrap;
				}

				.confirmBtn {
					background-color: $color-primary;
					color: #fff;
				}

				.cancelBtn {
					border: 1px solid $color-primary;
					color: $color-primary;
				}
			}

			@media screen and (max-width: 768px) {
				.oneDialogTitle {
					font-size: 14px;
				}

				.oneDialogContent {
					padding: 10px 0;

					.oneText {
						font-size: 12px;
					}

					.selectBox {
						margin-top: 10px;

						.radioText {
							font-size: 13px;
						}
					}
				}

				.dialog-footer {
					padding: 0px 0px 20px;

					.clickBtn {
						font-size: 12px;
					}
				}
			}
		}
	}
}
</style>
