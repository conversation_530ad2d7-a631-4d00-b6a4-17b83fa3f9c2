<template>
  <div class="mb-4" :class="stepData.styleName">
    <half-design-litter-title v-show="stepData.attributeTitle" :index="stepData.id" :data-name="`${stepData.styleClass + '_' + stepData.id}`" style="margin: 4px 0" :stepTitle="stepData.minStepTitle">{{ stepData.attributeTitle }} </half-design-litter-title>
	<slot name="stepText"></slot>
    <div class="step-content" v-if="!stepData.isHideStep || (stepData.isHideStep && stepData.productParamList.length > 1)">
      <halfDetailMyFormMySelect v-model="selectItem" :options="selectOptions" placeholder="Select Your Belt Length..." @change="handleChange">
      </halfDetailMyFormMySelect>
    </div>
    <div class="errorTip">
      <v-alert dense outlined type="error">
        {{ langSemiCustom.errorTip }}
      </v-alert>
    </div>
  </div>
</template>
<script>
import halfDetailMyFormMySelect from '@/components/modal/Half/Detail/myForm/my-select'
export default {
  props: {
    stepData: {
      type: Object,
      default: () => ({})
    }
  },
  components: {
    halfDetailMyFormMySelect
  },
  data() {
    return {
      selectIndex: -1,
      selectItem: null
    }
  },
  computed: {
    langSemiCustom() {
      return this.$store.getters.lang?.semiCustom
    },
    selectOptions() {
      if (this.stepData.productParamList.length > 0) {
        return this.stepData.productParamList.map(item => {
          return {
            value: item,
            label: item.valueName
          }
        })
      }
      return []
    }
  },
  methods: {
    selectStep(item, index, state = false) {
      this.selectIndex = index
      this.selectItem = item
      this.$emit('selectStep', {
        type: this.stepData.attributeFlag,
        data: item,
        parent: this.stepData,
        id: this.stepData.id,
        firstSelect: state
      })
    },
    selectDefault() {
      if (this.selectIndex <= -1) {
        this.selectStep(this.stepData.productParamList[0], 0, true)
      }
    },
    handleChange(data) {
      if (data.value) {
        this.selectStep(data.value)
      }
    }
  },
  mounted() {
    this.$Bus.$on('selectDefaultOtherStep', this.selectDefault)
  },
  beforeDestroy() {
    this.$Bus.$off('selectDefaultOtherStep', this.selectDefault)
  }
}
</script>
<style scoped lang="scss">
@import '~assets/css/half.scss';

.style1 .step-content {
  display: grid;
  grid-template-columns: repeat(auto-fill, 30px);
  grid-gap: 20px;

  .step-item {
    min-width: 0;
    position: relative;
    @include flex-center;
    border-radius: 50%;
    cursor: pointer;
    aspect-ratio: 1;
    height: 30px;
    border: 1px solid #ccc;

    .v-icon {
      display: none;
    }

    &::before {
      display: none;
      content: '';
      position: absolute;
      left: -6px;
      top: -6px;
      right: -6px;
      bottom: -6px;
      border-radius: 50%;
      border: 2px solid $color-primary;
    }

    @media (any-hover: hover) {
      &:hover {
        &::before {
          display: block;
        }

        .v-icon {
          display: block;
        }
      }
    }
  }

  .step-item.active {
    &::before {
      display: block;
    }

    .v-icon {
      display: block;
    }
  }
}

@include respond-to(mb) {
  .style1 .step-content {
    grid-template-columns: repeat(auto-fill, 30px);
  }
}
</style>
