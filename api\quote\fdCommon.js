import { request } from "@/utils/request";
/* const model = {
  baseUrl:Config.app.apiTest,
  list:"/patientInfo/select/",
  add:"/patientInfo/insert/",
  byId:"/patientInfo/getPatientInfoById/",
  update:"/patientInfo/update/",
  del:"/patientInfo/delete/",
  exp:"",
}
 */

//报价计算calculateAll
export function quoteCalculateCalculateAll(data) {
	return request({
		url: "/quote/quoteCalculate/calculateAll",
		method: "post",
		data,
	});
}
//报价计算calculate
export function quoteCalculateCalculate(data) {
	return request({
		url: "/quote/quoteCalculate/calculate",
		method: "post",
		data,
	});
}
//报价计算calculate 数量
export function quoteCalculateCalculateQty(data) {
	return request({
		url: "/quote/quoteCalculate/calculateQty",
		method: "post",
		data,
	});
}

//获取报价分类
export function quoteCalculateGetChildCateList(data) {
	return request({
		url: "/quote/quoteCalculate/getChildCateList",
		method: "get",
		data,
	});
}

export function getCurrency(data) {
	return request({
		url: "/quote/currency/getAllCurrency",
		method: "get",
		params: data,
	});
}

//获取2个时间字段
export function getCateFdRushAndTimeStatus(data) {
	return request({
		url: "/quote/cate/fdRushAndTimeStatus",
		method: "get",
		params: data,
	});
}

//获取所有子分类列表
export function getChildCateList(query) {
	return request({
		url: "/quote/quoteCalculate/getChildCateList",
		method: "get",
		params: query,
	});
}

//获取价格表
export function getPriceList(data) {
	return request({
		url: "/quote/quoteCalculate/getPriceList",
		method: "post",
		data: data,
	});
}

//下载pdf 文件
export function downloadFdPdf(data) {
	return request({
		url: "/quote/quoteCalculate/downloadFdPdf",
		method: "post",
		data: data,
	});
}

//添加询盘
export function fdEditInquiry(data) {
	return request({
		url: "/app/inquiry/editInquiry",
		method: "post",
		data: data,
	});
}

//保存FD报价快照
export function saveQuoteRecord(data) {
	return request({
		url: "/quote/quoteCalculate/saveQuoteRecord",
		method: "post",
		data: data,
	});
}

export function getLanyardsAccessoryQuoteParam(data) {
	return request({
		url: "/quote/params/getLanyardsAccessoryQuoteParam",
		method: "get",
		params: data,
	});
}