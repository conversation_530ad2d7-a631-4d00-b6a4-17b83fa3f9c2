<template>
	<div class="emailLater" :class="stepData.styleName">
		<div class="laterContent">
			<div class="selctBox">
				<input id="emailLaterStyle1" class="clip" type="checkbox" />
				<label for="emailLaterStyle1">
					<span>{{ langSemiCustom.designLater1 }}</span> <span class="grayText">{{ langSemiCustom.designLater2 }}</span></label
				>
			</div>
			<div class="modeBox" v-show="showMode">
				<div class="modeItem">{{ nowMode }}</div>
				<div class="modeItem active" @click="switchMode">
					<span><b class="icon-DESIGN"></b></span>
					<span class="modeText">{{ selectFirst ? langSemiCustom.goTo : langSemiCustom.backTo }} {{ " " + setMode }}</span>
					<span><v-icon>mdi-chevron-right-circle-outline</v-icon></span>
				</div>
			</div>
			<div class="solidBox" v-show="showMode"></div>
		</div>
	</div>
</template>

<script>
export default {
	name: "Style1",
	inject: ["getPrintStyleClass", "getPrintColorStyleClass"],
	components: {},
	props: {
		stepData: {
			type: Object,
			default: () => ({}),
		},
	},
	data() {
		return {
			selectIndex: -1,
			selectItem: null,
			showMode: true,
			nowSelectedMode: -1,
			emailLaterData: [],
			noEmailLaterData: [],
			selectFirst: true,
		};
	},
	watch: {},
	computed: {
		langSemiCustom() {
			return this.$store.getters.lang?.semiCustom;
		},
		nowMode() {
			let map = {
				1: this.langSemiCustom.designMode2,
				2: this.langSemiCustom.designMode2,
				3: this.langSemiCustom.designMode1,
			};
			return map[this.modeType];
		},
		setMode() {
			let map = {
				1: this.langSemiCustom.designMode1,
				2: this.langSemiCustom.designMode1,
				3: this.langSemiCustom.designMode2,
			};
			return map[this.modeType];
		},
		modeType() {
			return this.$store.state.halfDesign.modeType;
		},
		defaultModeType() {
			return this.$store.state.halfDesign.defaultModeType;
		},
		changeModeType() {
			return this.$store.state.halfDesign.changeModeType;
		},
		printStyleClass() {
			return this.getPrintStyleClass();
		},
		printColorStyleClass() {
			return this.getPrintColorStyleClass();
		},
	},
	methods: {
		switchMode() {
			this.selectFirst = false;
			let changeMode = this.nowSelectedMode == this.defaultModeType ? this.changeModeType : this.defaultModeType;
			this.$store.commit("halfDesign/setProModeType", changeMode);
			this.nowSelectedMode = changeMode;
			if (changeMode == 1 || changeMode == 2) {
				if(this.printStyleClass) this.$Bus.$emit(this.printStyleClass, { noShowTitle: true });
				if(this.printColorStyleClass) this.$Bus.$emit(this.printColorStyleClass, { noShowTitle: true });
			} else {
				if(this.printStyleClass) this.$Bus.$emit(this.printStyleClass, { noShowTitle: false });
				if(this.printColorStyleClass) this.$Bus.$emit(this.printColorStyleClass, { noShowTitle: false });
			}
			//记录用户切换的模式
			if (this.$store.state.halfDesign.noChangeModeType == 0) {
				this.$store.commit("halfDesign/setNoChangeModeType", 1);
			}
			try {
				if (window.$nuxt.$store.state.advertConfig.googleConfig && window.gtag) {
					gtag("event", "select_content", {
						content_type: "modeType",
						content_value: this.nowSelectedMode == 3 ? "Professional Design Mode" : "Simple Design Mode",
					});
				}
			} catch (error) {}
		},
		selectStep(item, index, state = false) {
			const checkbox = document.getElementById("emailLaterStyle1");
			this.selectIndex = index;
			this.selectItem = item;
			this.$emit("selectStep", {
				type: this.stepData.attributeFlag,
				data: item,
				parent: this.stepData,
				id: this.stepData.id,
				firstSelect: state,
			});
			if (checkbox.checked) {
				/**
				 * 选项卡切换并清空
				 * 兄弟组件在 components/HalfDesign/ArtworkOptions/Style31 目录下
				 */
				this.$Bus.$emit("selectImgColorClear");

				/**
				 * 左侧canvas清空
				 * 兄弟组件在 components/modal/Half/Detail/index.vue 目录下
				 */
				this.$Bus.$emit("canvasClear");
			}
		},
		selectDefault() {
			let findIndex = this.stepData.productParamList.findIndex((item) => {
				return item.isBlank === 0;
			});
			if (findIndex > -1) {
				this.selectStep(this.stepData.productParamList[findIndex], findIndex, true);
			}
		},
		cancelEmailLater() {
			const checkbox = document.getElementById("emailLaterStyle1");
			checkbox.checked = false;
			this.showMode = !this.changeModeType ? false : true;
			this.selectStep(this.noEmailLaterData[0], 0);
		},
	},
	created() {},
	mounted() {
		this.showMode = !this.changeModeType ? false : true;
		this.nowSelectedMode = this.defaultModeType;
		this.emailLaterData = this.stepData.productParamList.filter((item) => {
			return item.isBlank == 1;
		});
		this.noEmailLaterData = this.stepData.productParamList.filter((item) => {
			return item.isBlank == 0;
		});
		const checkbox = document.getElementById("emailLaterStyle1");
		if (checkbox) {
			checkbox.addEventListener("change", (event) => {
				if (event.target.checked) {
					this.selectStep(this.emailLaterData[0], 0);
					this.showMode = false;
				} else {
					this.showMode = !this.changeModeType ? false : true;
					this.selectStep(this.noEmailLaterData[0], 0);
				}
			});
		}
		this.$Bus.$on("selectDefaultEmailStep", this.selectDefault);
		this.$Bus.$on("cancelEmailLater", this.cancelEmailLater);
	},
	beforeDestroy() {
		this.$Bus.$off("selectDefaultEmailStep", this.selectDefault);
		this.$Bus.$off("cancelEmailLater", this.cancelEmailLater);
	},
};
</script>

<style scoped lang="scss">
.clip {
	position: absolute;
	clip: rect(0, 0, 0, 0);
}
.emailLater {
	width: 100%;
	.laterContent {
		width: 100%;
		height: 100%;
		overflow: hidden;

		.selctBox {
			width: 100%;
			height: 48px;
			display: flex;
			align-items: center;
			column-gap: 10px;
			cursor: pointer;
			font-family: Roboto;
			font-weight: bold;
			font-size: 14px;
			color: #333333;
			// background: #edf2fa;
			background-color: var(--color-primary-half-opacity, var(--color-primary-opacity));
			padding: 0 20px;

			label {
				position: relative;
				user-select: none;

				span:first-child {
					padding-left: 30px;
				}

				.grayText {
					font-weight: 400;
				}
			}

			input[type="checkbox"] + label::before {
				content: "";
				position: absolute;
				display: inline-block;
				font: normal normal normal 24px / 1 "Material Design Icons";
				width: 20px;
				height: 20px;
				margin-top: -2px;
				font-size: 14px;
				text-align: center;
				line-height: 20px;
				border: 1px solid #adadad;
				background-color: #ffffff;
			}

			input[type="checkbox"]:checked + label::before {
				content: "\F012C";
				border: 1px solid $color-primary;
				color: #fff;
				background-color: $color-primary;
			}
		}

		.modeBox {
			margin: 20px 0;
			display: flex;
			align-items: center;
			justify-content: space-between;
			.modeItem {
				font-family: Roboto;
				font-weight: bold;
				font-size: 16px;
				color: #333333;
				display: flex;
				align-items: center;
				column-gap: 4px;
				user-select: none;
				&.active {
					cursor: pointer;
					font-weight: 400;
					color: $color-primary;
					text-decoration: underline;
					.v-icon {
						color: $color-primary;
					}
				}
				b {
					font-size: 16px;
				}
				.v-icon {
					font-size: 20px;
				}
			}
		}

		.solidBox {
			width: 100%;
			height: 1px;
			background: #ebebeb;
		}
	}
}

.style2.emailLater {
	.laterContent {
		.modeItem.active {
			display: none;
		}
	}
}

@include respond-to(mb) {
	.emailLater {
		.laterContent {
			.selctBox {
				font-size: 12px;
				label {
					display: flex;
					align-items: flex-start;
					justify-content: center;
					flex-direction: column;
					span {
						padding-left: 30px;
					}
				}
			}

			.modeBox {
				margin: 10px 0;
				.modeItem {
					font-size: 12px;
					&.active {
						// max-width: 60%;
						align-items: flex-start;
						.modeText {
							word-wrap: break-word;
							word-break: break-word;
						}
					}
					b {
						font-size: 14px;
					}
					.v-icon {
						font-size: 18px;
					}
				}
			}
		}
	}
}
</style>
