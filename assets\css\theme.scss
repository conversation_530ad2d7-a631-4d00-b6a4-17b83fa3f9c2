// 主题变量（默认）
:root {
	--text-primary: #333333;
	--color-bright: #FDD305;
	--color-dark: #333333;
	--btn-primary: var(--color-primary);
	--btn-second: var(--color-primary);

	--bg-mask: rgba(0, 0, 0, 0.5);
	--bg-shadow: rgba(100, 100, 100, 0.1);
}

[primary],
[outline] {
	margin: 0;
	height: 2.5em;
	min-width: 10em;
	max-width: 100%;
	grid-gap: 0.5em;
	cursor: pointer;
	width: max-content;
	text-align: center;
	align-items: center;
	display: inline-flex;
	padding: 0.3rem 1.7rem;
	box-sizing: border-box;
	min-height: fit-content;
	justify-content: center;
	transition-duration: 0.05s;
	border-radius: $radius-btn;

	&:hover {
		transform: scale(1.08, 1.1);
	}
}

[primary] {
	font-weight: bold;
	background: $btn-primary;
	color: var(--btn-text, white);
}

[outline] {
	color: $btn-primary;
	border: 1px solid;
}



.modal-box {
	line-height: 1.3;
	background-size: cover;
	background-position: center;
	background-repeat: no-repeat;
	padding: 5em max(calc(50% - 700px), 1.5vw);
	position: relative;
	z-index: 1;

	&.h2NeonLinear {
		margin-top: 1em;
		padding-top: 6em;

		h2 {
			min-width: 80%;
			color: white;
			max-width: 100%;
			text-align: center;
			font-size: 1.875em;
			padding: 0.584em 2em;
			border-radius: 0.1em 0.1em 5em 5em;
			background: linear-gradient(99deg, var(--color-primary-lighten), var(--btn-primary) 150%);
			box-shadow: 0 -3px 2px rgba(255, 255, 255, 0.3) inset, 0.15em -0.05em 0.3em var(--color-bright) inset, -0.15em -0.15em 0.3em var(--btn-primary) inset, 0.1em 0.03em 0.1em var(--btn-primary), -0.1em 0.06em 0.1em var(--color-bright);
			transform: translate(-50%);
			position: absolute;
			left: 50%;
			top: 0;
		}
	}

	.video-box {
		color: white;
		position: relative;

		video {
			width: 100%;
			height: 100%;
			// object-fit: contain;
		}

		video::-webkit-media-controls {
			display: none !important;
		}

		video::-moz-media-controls {
			display: none !important;
		}

		video::-ms-media-controls {
			display: none !important;
		}

		.icon-jxsht-3d-dbf {
			font-size: 4em;
			transform: translate(-50%, -50%);
			color: rgba(255, 255, 255, 0.7);
			position: absolute;
			left: 50%;
			top: 50%;
		}

		.icon-a-tgsc-add {
			cursor: pointer;
			position: absolute;
			right: 1em;
			top: 1em;

			&::before {
				font-size: 1.5em;
			}
		}

		// .videoCover {
		// 	height: 100%;
		// 	position: absolute;
		// 	left: 0;
		// 	top: 0;
		// }
	}
}



[theme] {
	h1 {
		line-height: 1.25;
		font-size: 3.5rem;
	}

	h2 {
		line-height: 1.25;
		font-size: 2.25rem;
		text-align: center;
	}

	h3 {
		font-size: calc(1em + 2px);
	}

	.sub-title {
		font-size: 1.125rem;
		text-align: center;
		margin: 1em auto 2em;
	}
}



[theme='2'],
[theme='3'] {
	overflow: hidden;

	.modal-box {
		padding: 3vmax max(calc(50% - 600px), 1.5vw);

		&:nth-child(odd) {
			.card {
				background-color: $bg-primary;

				.index {
					color: white;
				}
			}

			&.bg-second .card {
				background-color: $bg-second;
			}
		}

		&:nth-child(even) {
			&:not(.bg-second) {
				background-color: $bg-primary;
			}

			.card {
				background-color: white;

				.index {
					color: $bg-primary;
				}
			}

			&.bg-second {
				background-color: $bg-second;
			}
		}
	}

	&[theme='3'] h2::after {
		content: '';
		width: 74px;
		height: 6px;
		margin-top: 1vmax;
		border-radius: 5px;
		display: inline-block;
		background: $color-primary;
	}
}



[theme='4'] {
	--radius-btn: 3em;

	.modal-box {
		background-color: $bg-primary;
		padding: 5em max(calc(50% - 800px), 1.5vw);

		&:not(.bg-second) h2 {
			color: $color-primary;
		}
	}

	h1 {
		font-size: 2.625em;
	}
}



[theme='5'] {
	--radius-btn: 0.3rem;

	.modal-box {
		line-height: 1.2;
		padding: 3.8em max(calc(50% - 700px), 1.5vw) 4.2em;
	}

	h1 {
		font-size: 2.625em;
	}

	.sub-title {
		font-size: 1em;
		margin: 0.3em 0 1.7em;
	}
}



[theme='6'] {
	.modal-box {
		line-height: 1.5;
		padding: 4em max(calc(50% - 800px), 1.5vw);
	}

	[primary] {
		background: linear-gradient(to right, $color-bright, $btn-primary);
		box-shadow: 0 2px 5px 1px rgba(255, 255, 255, 0.3) inset, 0 -1px 3px rgba(0, 0, 0, 0.2) inset;
	}
}



[theme='7'] {
	h1 {
		font-size: 4em;
	}

	h2 {
		text-align: left;
		margin-left: auto;
		margin-right: auto;
		width: fit-content;

		&::before {
			content: attr(data-before);
			display: block;
			font-size: 16px;
			width: fit-content;
			font-weight: normal;
			margin-bottom: 0.5vw;
			padding: 0 5vw 0.5em 0;
			border-bottom: 2px solid;
		}
	}

	.modal-box.h2Center h2 {
		text-align: center;

		&::before {
			margin: 0 auto 0.5vw;
			padding: 0 2.5vw 0.5em;
		}
	}

	.modal-box.h2NoBefore h2::before {
		all: unset;
	}

	.modal-box.h2Right h2::before {
		padding: 0 0 0.5em 5vw;
	}

	[primary] {
		color: $text-primary;
		background: linear-gradient(to right, $color-bright, $btn-primary);
	}
}



[theme='8'] {
	--radius-btn: 0.5vmax;

	.modal-box {
		padding: 5em max(calc(50% - 850px), 1.5vw);
	}

	h1 {
		font-size: 3em;
	}

	[primary]:hover {
		filter: brightness(120%);
	}
}



[theme='9'] {
	--radius-btn: 0.3rem;

	.modal-box {
		padding: 4.3em max(calc(50% - 800px), 1.5vw);
	}

	h1 {
		letter-spacing: 0.1rem;
	}

	h3 {
		font-weight: inherit;
	}

	.card .content .card-text {
		opacity: 0.7;
	}

	[primary],
	[outline] {
		font-weight: normal;
		border: 0.15em solid;
		clip-path: inset(0 round $radius-btn);
		background: linear-gradient(90deg, var(--color-primary), var(--btn-primary) 150%);
		border-image: linear-gradient(90deg, var(--color-primary), var(--btn-primary) 150%) 1;
		animation: neon-btn 1s infinite alternate ease;
		filter: hue-rotate(20deg);
	}

	[outline] {
		color: white;
		border-radius: 0;
		background: none;
		box-shadow: inset 0 0 9em -5em var(--color-primary);
		-moz-box-shadow: inset 0 0 9em -5em var(--color-primary);
		-webkit-box-shadow: inset 0 0 9em -5em var(--color-primary);
	}
}



[theme='10'] {
	--radius-btn: 3em;

	h1 {
		font-size: 3em;
	}

	.modal-box {
		padding: 4.3em max(calc(50% - 800px), 1.5vw);

		&:not(.h2NoIcon) h2::before {
			content: "\e672";
			display: block;
			font-size: 1.8em;
			line-height: 0.8;
			font-weight: normal;
			margin-bottom: 0.2vw;
			font-family: modalicon !important;
		}
	}

	.banner-box button {
		height: 3em;
		min-width: auto;
		padding: 0 1.2em;
	}

	[primary] {
		background: linear-gradient(-23deg, $color-primary, $btn-primary);
		box-shadow: 0 2px 5px 1px rgba(255, 255, 255, 0.1) inset, 0 -1px 3px rgba(0, 0, 0, 0.1) inset, 0px 0.2em 0.4em -0.2em var(--color-primary-opacity);
	}
}



[theme='11'] {
	--radius-btn: 0.3em;

	h2 {
		grid-gap: 0.43em;
		font-size: 2.1em;
	}

	.modal-box {
		padding: 7em max(calc(50% - 800px), 1.5vw);

		div {
			line-height: 1.6;
			letter-spacing: 0.1em;
		}

		&:not(.h2NoIcon) h2 {
			display: flex;
			align-items: center;
			flex-direction: column;

			&::before {
				content: "\e796";
				font-size: 1.2em;
				font-weight: normal;
				font-family: modalicon !important;
			}
		}
	}

	[primary] {
		padding: 0.3em 3.25vw;
		box-shadow: 0 0 0.3em 0 var(--color-primary);
		background: linear-gradient(163deg, $btn-primary -150%, $color-primary);
	}
}



[theme='12'] {
	--radius-btn: 3em;

	.modal-box {
		line-height: 1.2;
		padding-top: 4em;
		padding-bottom: 4em;
	}

	h1 {
		font-size: 3.25em;
	}

	.sub-title {
		padding: 0 2vw;
	}

	[primary] {
		background: linear-gradient(to right, $color-bright, $btn-primary 90%);

		&:not(.card-btn [primary]):not(footer [primary]) {
			min-width: calc(12em + 3vw);
		}
	}
}



[theme='13'] {
	.modal-box {
		line-height: 1.2;
	}

	h1 {
		font-size: 2.875em;
	}

	.sub-title {
		font-size: 1em;
	}

	[primary] {
		color: $text-primary;
		background: linear-gradient(to right, $color-primary, $btn-primary, $color-primary);
	}
}



[theme='14'] {
	--radius-btn: 0.25em;

	.modal-box {
		padding: 4.5em max(calc(50% - 878px), 1.5vw);
	}

	h1 {
		font-size: 3em;
	}

	.sub-title {
		font-size: 1em;
	}
}



[theme='15'] {
	--radius-btn: 0;

	.modal-box {
		line-height: 1.5;
		letter-spacing: 0.5px;
		padding: 4.5em max(calc(50% - 800px), 1.5vw);

		&.summary-box .content>div:not(.btn-box) {
			font-size: calc(1em + 2px);
		}
	}

	h1 {
		font-size: 5em;
	}

	h2 {
		line-height: 1.5;
		font-size: 3.25em;
		text-transform: uppercase;
	}

	.sub-title {
		opacity: 0.7;
		font-size: 1em;
	}

	[primary] {
		color: $bg-page;
		font-weight: 400;
		clip-path: polygon(5% 0, 95% 0, 100% 25%, 100% 75%, 95% 100%, 5% 100%, 0 75%, 0 25%);
	}

	[outline] {
		border: none;
		clip-path: polygon(5% 0, 95% 0, 100% 25%, 100% 75%, 95% 100%, 5% 100%, 0 75%, 0 25%);
		background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0.75 -1.5 98.5 103" preserveAspectRatio="none"><polygon points="5,0 95,0 100,25 100,75 95,100 5,100 0,75 0,25" fill="transparent" stroke="black" stroke-width="3"/></svg>');
	}

	[primary]:not(.card-box button),
	[outline]:not(.card-box button) {
		font-size: 1.25em;
		text-transform: uppercase;
	}
}



[theme='16'] {
	--radius-btn: 0;

	.modal-box {
		line-height: 1.4;
		padding: 4em max(calc(50% - 800px), 1.5vw) 5em;
	}

	[primary] {
		font-weight: 500;
		clip-path: polygon(0 0, 95% 0, 100% 25%, 100% 100%, 100% 100%, 5% 100%, 0 75%, 0 0%);
	}

	[primary]:not(.card-box button),
	[outline]:not(.card-box button) {
		font-weight: 700;
	}
}



[theme='17'] {
	--radius-btn: 3em;

	.modal-box {
		padding: 5em max(calc(50% - 850px), 1.5vw);
	}

	h1,
	h2,
	h3,
	[primary],
	[outline] {
		font-weight: 400;
	}

	h1 {
		font-size: 3em;
	}

	h2 {
		font-size: 2.5em;
	}

	.sub-title {
		font-size: 1em;
		margin-top: 0.5em;
	}
}



[theme='18'] {
	--radius-btn: 0.4rem;

	.modal-box {
		padding: 5em max(calc(50% - 860px), 1.5vw);
	}

	h1 {
		font-size: 3em;
	}

	.sub-title {
		font-size: 1em;
	}

	[outline] {
		color: inherit;
		font-weight: 900;
	}
}



[theme='19'] {
	--radius-btn: 0.7rem;

	.modal-box {
		line-height: 1.5;
		padding: 5em max(calc(50% - 700px), 1.5vw);

		.card-box .card-btn button {
			width: 100%;
		}

		[primary],
		[outline] {
			height: 3.2rem;
			grid-gap: 0.25em;

			b {
				font-weight: 400;
				transform: scale(0.8);
			}
		}
	}

	h1 {
		font-size: 2.5em;
	}

	h3,
	.sub-title {
		font-size: 1em;
	}

	[primary] {
		text-transform: uppercase;
		background-image: linear-gradient(to right, $btn-primary, $btn-second);
	}

	[outline] {
		font-weight: 900;
		color: $color-primary;
		text-transform: uppercase;
	}
}



[theme='20'] {
	--radius-btn: 0.5rem;

	h1 {
		font-size: 3.4375em;
	}

	.modal-box {
		padding: 3.125em max(calc(50% - 600px), 1.5vw);

		// .sub-title {
		// 	font-size: 1em;
		// }
	}

	[primary]:not(.card-box button),
	[outline]:not(.card-box button) {
		gap: 0.75rem;
		min-width: 12.5rem;
	}

	.card-btn button:last-child:not(:first-child) {
		background: var(--btn-second);
	}
}



[theme='21'] {
	--radius-btn: 3em;

	.modal-box {
		padding: 3.125em max(calc(50% - 600px), 1.5vw) 3.5em;
	}

	h2 {
		font-size: 2em;
		text-transform: uppercase;
	}
}



[theme='22'] {
	--radius-btn: 3em;

	.modal-box {
		padding: 4.25em max(calc(50% - 850px), 1.5vw);
	}

	h1 {
		font-size: 3em;
	}

	[primary] {
		box-shadow: inset 0px 4px 4px 0px rgba(255, 255, 255, 0.25);
		background: linear-gradient($btn-second, $color-primary 50%);
	}
}



[theme='23'] {
	--radius-btn: 3em;

	.modal-box {
		padding: 3.8em max(calc(50% - 800px), 1.5vw);
	}

	[outline] {
		font-weight: 900;
	}
}



[theme='24'] {
	--radius-btn: 0.5rem;

	h1 {
		font-size: 2.875rem;
	}

	.sub-title {
		font-size: 1rem;
	}

	.modal-box {
		padding: 3.8em max(calc(50% - 850px), 1.5vw);

		&:not(.h2NoBefore) h2 {
			display: flex;
			align-items: center;
			flex-direction: column;
			gap: 0.875rem;

			&::before {
				content: "";
				width: 3.625rem;
				height: 0.375rem;
				border-radius: 3px;
				background-image: linear-gradient(to right, $btn-primary, $btn-second, $btn-primary);
			}
		}

		&.h2BeforeLeft h2 {
			align-items: flex-start;
		}
	}

	[primary] {
		color: $text-primary;
		background-image: linear-gradient(to right, $btn-primary, $btn-second, $btn-primary);
	}
}









// 霓虹灯按钮动画
@keyframes neon-btn {
	from {
		filter: hue-rotate(-50deg);
	}

	to {
		filter: hue-rotate(20deg);
	}
}

// 霓虹灯图片动画
@keyframes neon-img {
	from {
		filter: hue-rotate(-500deg);
	}

	to {
		filter: hue-rotate(500deg);
	}
}

// 跑马灯效果
@keyframes marquee {
	0% {
		transform: translateX(0);
	}

	100% {
		transform: translateX(-100%);
	}
}

// 跑马灯效果-长
@keyframes marquee-long {
	0% {
		transform: translateX(0);
	}

	100% {
		transform: translateX(-200%);
	}
}

// 跑马灯效果-垂直
@keyframes marqueeY {
	0% {
		transform: translateY(0);
	}

	100% {
		transform: translateY(-43%);
	}
}

@keyframes rotate-img {
	0% {
		transform: rotateY(0deg);
	}

	50% {
		transform: rotateY(90deg);
	}

	100% {
		transform: rotateY(180deg);
	}
}








// pc 和 ipad
@media screen and (min-device-width: $mb-width) {

	[primary],
	[outline] {
		height: 3.4375rem;
	}

	.card-box [primary],
	.card-box [outline] {
		height: 3rem;
	}



	[theme='4'] [primary],
	[theme='4'] [outline] {
		height: 2.5rem;
	}



	[theme='5'] [primary],
	[theme='5'] [outline] {
		min-width: 15.5em;
		font-size: 1.125em;
	}



	[theme='6'] [primary],
	[theme='6'] [outline] {
		height: 3.75rem;
	}



	[theme='7'],
	[theme='8'],
	[theme='10'] {

		[primary],
		[outline] {
			min-width: 12.5em;
		}
	}



	[theme='9'] .sub-title,
	[theme='9'] .summary-box .content>div:not(.btn-box) {
		font-size: 1.125em;
	}



	[theme='11'] .modal-box {
		font-size: 1.25em;

		[primary],
		[outline] {
			height: 2.5em;
			font-size: 1.6em;
			min-width: 6.57em;
		}
	}



	[theme='12'] {
		.summary-box .content>div {
			font-size: 1.125em;
		}

		[primary],
		[outline] {
			height: 3.125rem;

			&:not(.card-btn>[primary]):not(header>[primary]) {
				font-size: 1.125em;
			}
		}
	}



	[theme='13'] [primary],
	[theme='13'] [outline] {
		height: 2.8125rem;
		font-size: 1.125em;
	}



	[theme='14'] .modal-box {
		line-height: 1.5;

		[primary],
		[outline] {
			height: 2.75rem;
			min-width: 12.5em;
		}
	}



	[theme='16'] .summary-box .content>div:not(.btn-box) {
		font-size: calc(1em + 2px);
	}



	[theme='16'] .modal-box {

		[primary],
		[outline] {
			min-width: 7.8125rem;
			height: 2.1875rem;

			&:not(.card-box button) {
				min-width: 15.9375rem;
				height: 2.875rem;
			}
		}
	}



	[theme='17'] .modal-box {
		font-size: 1.25em;

		[primary],
		[outline] {
			height: 2.6155em;
			font-size: 1.3em;
		}

		[primary]:not(.card-box button):not(.subscribe-box button),
		.card-box [primary]:not(:hover) {
			color: inherit;
			background: none;
			border: 2px solid;
		}

		[primary]:not(.card-box button) {
			width: 12.5em
		}
	}



	[theme='18'] .modal-box {

		[primary]:not(.card-box button),
		[outline]:not(.card-box button) {
			height: 3.125rem;
			min-width: 16rem;
			font-size: 1.125em;
		}

		.card-box button {
			min-width: auto;
			height: 2.125rem;
		}
	}



	[theme='19'] {

		.card-box .card-subtitle,
		.card-box .card-text {
			font-size: calc(1em - 2px);
		}

		[primary],
		[outline] {
			min-width: 16.5rem;
		}
	}



	[theme='20'] {

		[primary]:not(.card-box button),
		[outline]:not(.card-box button){
			height: 2.5rem;
			font-size: 1.125em;
		}

		.card-box button {
			height: 2.25rem;
		}

		// .summary-box button {
		// 	height: 3rem;
		// 	font-size: 1.125em;
		// }
		.sub-title {
			margin: 1em auto 1.25em;

			>div {
				padding-bottom: 0.75em;
			}
		}
	}



	[theme='21'] [primary]:not(.card-box button),
	[theme='21'] [outline]:not(.card-box button) {
		height: 3.125rem;
		min-width: 11.67em;
		font-size: 1.125em;
	}



	[theme='22'] [primary],
	[theme='22'] [outline] {
		height: 3rem;
		min-width: 12em;
		font-size: 1.125em;
		padding: 0.3rem 2.7rem;
	}



	[theme='23'] {
		.modal-box {
			font-size: 1.125rem;
			line-height: 1.5;

			[primary],
			[outline] {
				min-width: 12em;
				height: 2.66667em;
				padding: 0.3rem 2.7rem;

				&:not(.card-box button) {
					font-size: 1em;
				}
			}

			.card-box button {
				font-size: 1rem;
			}
		}

		.banner-box button {
			height: 3em;
		}
	}



	[theme='24'] .modal-box {

		[primary],
		[outline] {
			min-width: 13.75rem;
			height: 3.125rem;
			font-size: 1.125rem;

			&:not(.card-box button) {
				min-width: 15.625rem;
			}
		}
	}
}









// ipad 和 mb
@media screen and (max-device-width: $pad-width) {
	[theme] .modal-box {
		padding: 3.4em 3vw 3.6em;
	}
}


// mb
@media screen and (max-device-width: $mb-width) {
	.modal-box.h2NeonLinear {
		padding-top: 3.5em;

		h2 {
			min-width: 92vw;
			font-size: 1.1em;
			padding: 0.65em 2em;
			font-weight: normal;
		}
	}

	[theme] {
		h1 {
			font-size: 2.33em;
		}

		h2 {
			font-size: 1.5em;
		}

		h3 {
			font-size: calc(1em + 1px);
		}

		.sub-title {
			font-size: 1em;
			margin: 0.8em 1em 1.5em;
		}
	}



	[theme='4'] {
		h2 {
			font-size: 1.75em;
		}

		.sub-title {
			font-size: calc(1em + 2px);

			br {
				display: none;
			}
		}
	}



	[theme='5'] {
		h1 {
			font-size: 2em;
		}

		.modal-box {
			padding: 3em 2.5vw;
		}
	}



	[theme='6'] h2 {
		font-size: 1.75em;
	}



	[theme='7'] {
		.modal-box:not(.h2Center) h2 {
			margin-left: 0;
		}

		h2::before {
			font-size: 0.6em;
			border-bottom-width: 1px;
		}

		[primary] {
			min-width: 8em;
		}
	}



	[theme='8'] h1 {
		font-size: 2em;
	}



	[theme='9'] [primary] {
		border-width: 1px;
	}



	[theme='10'] h1 {
		font-size: 1.5em;
	}



	[theme='11'] {
		h1 {
			font-size: 1.7em;
		}

		h2 {
			grid-column-gap: 0.7em;
		}

		.modal-box div {
			line-height: 1.5;
			letter-spacing: 0.2vw;
		}
	}



	[theme='12'] .modal-box {
		padding: 2em 3vw;

		h1 {
			font-size: 2.166em;
		}

		h2:not(.card h2) {
			margin: 0 6vw;
		}

		.sub-title {
			font-size: 1em;
		}

		&.summary-box .content .btn-box,
		.router-btn {
			margin-top: 1em;
		}
	}



	[theme='13'] .modal-box {
		--radius-btn: 3em;
		padding-left: 0.4em;
		padding-right: 0.4em;

		h1 {
			font-size: 2em;
		}
	}



	[theme='14'] {

		h1 {
			font-size: 2em;
		}

		h2 {
			font-size: 4.8vw;
		}

		>div>.modal-box,
		>div>.modal-box h3,
		>div>.modal-box h4 {
			line-height: 1.25;
			font-size: 3.73333vw;
		}
	}



	[theme='15'] {

		.modal-box {
			padding: 3em 1rem;
			font-size: 3.73333vw;
			letter-spacing: -0.1vw;
		}

		h1 {
			font-size: 2.857em;
		}

		h2 {
			font-size: 2.2857em;
		}

		.sub-title {
			margin: 0.8em 0 1.5em;
		}

		[primary],
		[outline] {
			height: 3em;
			padding: 0.3em 2em;

			&:not(.card-box button) {
				font-size: calc(1em + 2px);
			}
		}
	}



	[theme='17'] h1,
	[theme='17'] h2 {
		font-size: 2.0833em;
	}



	[theme='19'] {
		font-size: 2.99vw;

		h1,
		h2 {
			font-size: 1.60535em;
		}

		[primary],
		[outline] {
			padding: 0.3rem 1rem;
			font-size: calc(1em + 2px);
		}
	}



	[theme='20'] {
		h1 {
			font-size: 1.75em;
		}

		h3 {
			font-size: calc(1em + 2px);
		}
		.modal-box {
			padding: 2.5em 3vw;
		}

		.banner-box button[primary] {
			min-width: 8.75em;
			padding: 0.3rem 1rem;
		}

		.card-box button {
			height: 2.5rem;
		}
	}



	[theme='21'] {
		.modal-box {
			padding: 3.4em 2vw 3.6em;
		}

		h3 {
			font-size: calc(1em + 1px);
		}
	}


	[theme='22'] h1,
	[theme='23'] h1 {
		font-size: 2em;
	}



	[theme='24'] {
		.modal-box {
			&:not(.h2NoBefore) h2 {
				gap: 0.6666rem;

				&::before {
					width: 2.4166rem;
					height: 0.3333rem;
					border-radius: 2px;
				}
			}
		}
	}
}