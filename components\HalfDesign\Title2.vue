<template>
	<div class="step-title">
		<div class="step" :class="{ pureColor: isKeychainsCo }">
			<div class="stepContent">
				{{ langSemiCustom.step + " " + stepNum }}
			</div>
		</div>
		<div class="titleBox">
			<div class="textBox">
				<h2 class="text">
					{{ title }}
				</h2>
				<span class="plugText" v-show="valueName">{{ ": " + valueName }}</span>
			</div>
			<slot name="suffix">
			</slot>
		</div>
	</div>
</template>

<script>
export default {
	props: {
		stepNum: {
			type: [String, Number],
			default: 1,
		},
		title: {
			type: String,
			default: "Step",
		},
		valueName: {
			type: String,
			default: "",
		},
	},
	computed: {
		isMobile() {
			return this.$store.state.device === "mb";
		},
		langSemiCustom() {
			return this.$store.getters.lang?.semiCustom;
		},
		proId() {
			return this.$store.state.proId;
		},
		isKeychainsCo() {
			return this.proId == "354";
		},
	},
	methods: {},
};
</script>

<style scoped lang="scss">
@import "~assets/css/half.scss";

.step {
	display: flex;
	align-items: center;
	width: fit-content;
	min-width: 100px;
	padding-right: 20px;
	height: 30px;
	overflow: hidden;

	&.pureColor {
		.stepContent {
			background: $color-primary;

			&::after {
				border: 10px solid transparent;
				border-left-color: $color-primary !important;
			}

			&::before {
				background-color: $color-primary;
			}
		}
	}

	.stepContent {
		padding: 0 4px;
		flex: 1;
		height: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
		text-align: center;
		font-weight: bold;
		font-size: 18px;
		color: #ffffff;
		border-radius: 8px 8px 0 0;
		position: relative;
		background: linear-gradient(90deg, var(--title-color1, var(--color-primary)) 0%, var(--title-color2, var(--color-primary)) 80%);
		z-index: 1;

		// box-shadow: 2px 0px 0px 0px rgba($color-primary, 0.7);
		&::after {
			content: "";
			position: absolute;
			right: -43px;
			bottom: -9px;
			width: 50px;
			height: 50px;
			border-radius: 100%;
			transform: rotate(-45deg);
			border: 10px solid transparent;
			border-left-color: var(--title-color2, var(--color-primary));
			z-index: -1;
		}

		&::before {
			content: "";
			position: absolute;
			right: -9px;
			top: 3px;
			height: 90%;
			width: 10px;
			clip-path: polygon(0 0, 0% 100%, 100% 100%);
			background-color: var(--title-color2, var(--color-primary));
			z-index: -1;
		}
	}
}

.step-title {
	display: flex;
	align-items: flex-end;
	margin: 15px 0;

	.titleBox {
		border-bottom: 1px solid #f0f0f0;
		color: #000000;
		flex: 1;
		line-height: 1;
		align-self: stretch;
		display: flex;
		align-items: center;
		justify-content: space-between;

		.text {
			all: unset;
			font-weight: bold;
			font-size: 18px;
		}

		.plugText {
			font-size: 16px;
			font-weight: 400;
		}
	}
}

@include respond-to(mb) {
	.step {
		.stepContent {
			font-size: 16px;
			color: #ffffff;
			font-weight: 700;
		}
	}

	.step-title {
		margin: 10px 0;
		border: none;

		.titleBox {
			.text {
				font-size: 15px;
			}

			.plugText {
				font-size: 12px;
			}
		}
	}
}
</style>
