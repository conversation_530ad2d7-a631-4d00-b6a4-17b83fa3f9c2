<template>
	<div class="productHead">
		<nuxt-link
			v-if="!isDiv"
			target="_blank"
			:to="{
				path: productData.productRouting,
			}"
			:event="isMobile && isStockPage && $store.state.proTheme == '11' ? '' : 'click'"
		>
			<slot></slot>
		</nuxt-link>
		<div v-else style="height: 100%;" @click="openMaskDetail(productData.productRouting)">
			<slot></slot>
		</div>
	</div>
</template>

<script>
export default {
	name: "productHead",
    inject: ["isDialog"],
	props: {
		isDiv: {
			type: Boolean,
			default: false,
		},
		isStockPage: {},
		productData: {
			type: Object,
			default: () => ({}),
		},
	},
	data() {
		return {};
	},
	watch: {},
	methods: {
		openMaskDetail(url) {
            if(this.isDialog){
                this.$emit("toDetail",url);
            }else{
                if(location.pathname.indexOf('metal-pens')>-1){
                    window.open(url)
                }else{
                    let data = {
                        modal: "modalQuoteHalfDetail",
                        name: url,
                    };
                    this.$store.commit("setMask", data);
                }
            }
		},
	},
	computed: {
		isMobile() {
			return this.$store.state.device === "mb";
		},
	},
	created() {},
	mounted() {},
};
</script>

<style scoped lang="scss">
.productHead {
	width: 100%;
	height: 100%;
}
</style>
