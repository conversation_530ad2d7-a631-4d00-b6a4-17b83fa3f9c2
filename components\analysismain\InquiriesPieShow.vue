<template>
	<div id='inquiries-wrapper'>
		<div id="inquiries-pie-show" style="margin-right: 10px;"></div>
		<div class="pie-right">
			<div class="item" v-for="item in pieRightData" :key="item.name">
				<span >{{ item.percentage }}%</span>
				<span>{{ item.value }} inquiries</span>
			</div>
		</div>
	</div>

</template>

<script>
import { format } from '@/utils/analysismain'

export default {
	name: "PieShowTest",
	props: {
		filtByTime: {
			type: [Number, String]
		},
		inquiriesData: {
			type: Array,
			default() {
				return []
			}
		}
	},
	watch: {
		filtByTime: {
			handler(newValue) {
				console.log('newValue', newValue);
			}
		},
		inquiriesData: {
			handler(newValue) {
				this.option.series[0].data = newValue;
				console.log('inquiriesPieShowData', newValue);

				let pieRightData = format(newValue);
				console.log('pieRightData', pieRightData);
				this.pieRightData = pieRightData

				this.myChartRender()
			}
		}
	},
	data() {
		return {
			chartDom: '',
			pieRightData: [],
			option: {
				color: ["#1A73E8", "#DCE4E9", "#49C9C9", "#7A9EF0"],
				title: {
					text: "Inquires",
					subtext: "by category",
					left: "270",
					top: "25",
				},
				tooltip: {
					trigger: "item",
					formatter: "{b} {d}% {a}",
				},
				legend: {
					orient: "vertical",
					left: "270",
					top: "85",
					itemGap: 10,
					itemWidth: 14,
					itemHeight: 14,
					// formatter: (data) => {
					// 	console.log("data", data);
					// 	console.log(this);
					// 	return "Legend " + name;
					// },
				},
				series: [
					{
						label: {
							position: "inside",
							formatter: "{d}%",
							color: "#ffff",
						},
						itemStyle: {
							borderWidth: 3, //边框的宽度
							borderColor: "#fff", //边框的颜色
						},
						left: "-210",
						name: "Inquiries",
						type: "pie",
						radius: "75%",
						data: [],
						// [
						// 	{ value: 1048, name: "Lapel Pins" },
						// 	{ value: 735, name: "Stickers" },
						// 	{ value: 580, name: "Lanyards" },
						// 	{ value: 484, name: "Challenge Coins" },
						// ],
						emphasis: {
							disabled: true,
							itemStyle: {
								shadowBlur: 10,
								shadowOffsetX: 0,
								shadowColor: "rgba(0, 0, 0, 0.5)",
								borderWidth: 0,
							},
						},
					},
				],
			}
		};
	},
	mounted() {
		let chartDom = document.getElementById("inquiries-pie-show");
		this.chartDom = chartDom
	},
	methods: {
		myChartRender() {
			this.$echarts.init(this.chartDom).setOption(this.option);
		}
	}
};
</script>

<style lang="scss" scoped>
#inquiries-wrapper {
	display: flex;
	width: 100%;
	height: 100%;
	box-shadow: 1px 1px 6px #d6d6d6;
	border-radius: 0.3125vw;

	#inquiries-pie-show {
		width: 65%;
		height: 100%;
		background: #ffffff;
		/* background-color: pink; */
	}

	.pie-right {
		width: 35%;
		height: 100%;
		padding-top: 87px;
		background: #ffffff;
		font-size: 14px;
		.item {
			margin-bottom: 5px;
			display: flex;
			justify-content: space-between;
			padding-right: 45px;
		}
	}
}
</style>
