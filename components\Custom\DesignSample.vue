<template>
	<div>
		<div :style="{ ...o.style }">
			<EditDiv v-if="o.title?.value" :tagName="o.title.tagName || 'h2'" v-model:content="o.title.value" :style="modal.titleStyle" @click="setModalType(o.title, modal.outer, 'text')" />

			<div class="sub-title" :pointer="o.subTitle?.event" :style="modal.subTitleStyle" :hidden="!o.title?.value && !o.subTitle?.value" @click="setModalType(o.subTitle, modal.outer, 'text', o.subTitle.event)">
				<EditDiv v-if="o.subTitle?.value" v-model:content="o.subTitle.value" />
			</div>

			<EditDiv v-if="o.text?.value" :style="modal.textStyle" v-model:content="o.text.value" @click="setModalType(o.text, modal.outer, 'text')" />
		</div>

		<div class="card-box" :style="{ ...modal.cardBoxStyle }">
			<div :style="{ ...modal.boxStyle }">
				<div class="card" v-for="(l, li) in list" :key="li" :style="{ ...modal.cardStyle }">
					<a :href="modal.routeUrlPrefix + l.routeUrl" target="_blank">
						<Pic :src="l.templateImg" :alt="l.templateName" />
					</a>
				</div>
			</div>
		</div>

		<div class="pagination-box">
			<v-pagination :value="page" :length="pages" :total-visible="8" @input="pageChange"></v-pagination>
		</div>
	</div>
</template>

<script>
import Pic from "@/components/pic.vue";

export default {
	name: "CustomDesignSample",

	components: { Pic },

	props: {
		modal: { type: Object, default: () => ({}) },
		page: { type: Number, default: 1 },
		data: { type: Object, default: () => ({}) },
	},

	computed: {
		o() {
			return this.modal.outer[0] || {};
		},

		list() {
			return this.data?.records || [];
		},

		pages() {
			return this.data?.pages || 1;
		},
	},

	methods: {
		setModalType(target, targetArray, clickType, event, other) {
			this.$setModal(this, target, targetArray, clickType, event, other);
		},

		pageChange(targetPage) {
			if (this.page === targetPage) return;
			this.$emit("refresh", {
				page: targetPage,
			});
		},
	},
};
</script>

<style lang="scss" scoped>
.pagination-box {
	margin-top: 1.5rem;

	::v-deep {
		.v-pagination__item--active {
			background: var(--color-primary) !important;
		}
	}
}
</style>
