<template>
	<div class="bottom-area">
		<div class="bottom-container">
			<div class="bottom-left">
				<!--				<div class="switch-pro" @click="tipDialog = true">{{ getSwitchProBtnText }}</div>-->
				<!--				<div></div>-->
				<div class="stickerSize" v-if="canvas.isSticker">
					<div>Size:</div>
					<div class="size">
						<el-popover
							placement="top-start"
							popper-class="stickerSizeToolTip"
							effect="light"
							width="117"
							trigger="manual"
							content="This field is required"
							v-model="canvas.sizeWError">
							<input type="text" slot="reference" v-model="canvas.stickerSize.w" class="sizeInput" :class="{isError:canvas.sizeWError}" @input="changeSize('w')">
						</el-popover>
						<span class="c">X</span>
						<el-popover
							placement="top-start"
							popper-class="stickerSizeToolTip"
							effect="light"
							width="117"
							trigger="manual"
							content="This field is required"
							v-model="canvas.sizeHError">
							<input type="text" slot="reference" v-model="canvas.stickerSize.h" class="sizeInput" :class="{isError:canvas.sizeHError}" @input="changeSize('h')">
						</el-popover>
						<el-dropdown trigger="click" @command="handleStickerSizeCommand">
							<div class="sizeSelect">
								<b class="icon-down"></b>
							</div>
							<el-dropdown-menu slot="dropdown">
								<el-dropdown-item :command="{w:1,h:1}">1” x 1”</el-dropdown-item>
								<el-dropdown-item :command="{w:2,h:2}">2” x 2”</el-dropdown-item>
								<el-dropdown-item :command="{w:3,h:3}">3” x 3”</el-dropdown-item>
								<el-dropdown-item :command="{w:4,h:4}">4” x 4”</el-dropdown-item>
								<el-dropdown-item :command="{w:5,h:5}">5” x 5”</el-dropdown-item>
							</el-dropdown-menu>
						</el-dropdown>
					</div>
				</div>
				<!-- <star v-model="score"></star> -->
			</div>
			<div class="bottom-right">
				<div class="bottom-right-1"></div>
				<div class="bottom-right-2">
					<div class="getPrice" @click="next">
						<span>{{
								$store.state.design.pageInfo.isMetal == "1" ? langDesign.next
									: langDesign.getPrice
							}}</span>
						<template v-if="$store.state.design.pageInfo.isMetal === 0">
							<el-tooltip class="item" effect="light" popper-class="dzToolTip" :content="langDesign.getPriceTip" placement="top">
								<b class="iconfont icon-wenhao" style="font-size: 20px; color: #ffffff; margin-left: 2px"></b>
							</el-tooltip>
						</template>
					</div>
				</div>
			</div>
			<!-- 切换产品提示 -->
			<el-dialog :title="langDesign.changeDesignTip" custom-class="custom-dialog-tip" :visible.sync="tipDialog" width="40%" :close-on-click-modal="false" :before-close="handleClose" center>
				<span style="font-size: 18px; font-weight: 400; color: #333333; overflow-wrap: break-word">{{ langDesign.tipMain }}</span>
				<span slot="footer" class="dialog-footer">
					<el-button @click="tipDialog = false" type="primary" style="font-size: 16px; font-weight: bold; color: #fff">{{ langDesign.nowDesign }}</el-button>
					<el-button @click="selectProduct" style="font-size: 16px; font-weight: bold; color: #333333">{{ langDesign.newDesign }}</el-button>
				</span>
			</el-dialog>
			<div class="modal" v-show="productDialog">
				<div class="proBox">
					<div class="custom-title">
						<h3>{{ langDesign.chooseProText }}</h3>
						<b class="iconfont icon-close" @click="closeModal"></b>
					</div>
					<div class="productInfo">
						<div class="pro-left">
							<div class="cateTitle">{{ langDesign.allProducts }}</div>
							<div class="pro-cates" v-for="(item, index) in productList" :key="index">
								<div class="cate" :class="{ active: selectedCate == item.id }" @click="showCate(item)">
									<span>{{ item.cate }}</span>
									<b class="iconfont" :class="{ 'icon-plus': activeName.includes(item.id), 'icon-minus': !activeName.includes(item.id) }" v-show="item.showBtn"></b>
								</div>
								<transition name="fade">
									<div class="cateList" :style="{ maxHeight: '480px', overflowY: 'auto' }" v-show="!activeName.includes(item.id)">
										<div class="cateChild" :class="{ active: selectedCate == citem.id }" v-for="(citem, index) in item.cateList" :key="index" @click="selectCate(citem)">
											<span>{{ citem.cate }}</span>
										</div>
									</div>
								</transition>
								<div style="border-bottom: 1px solid #ebebeb; width: 100%; height: 1px"></div>
							</div>
						</div>
						<div class="pro-right">
							<div class="pro-show" v-show="!showDetail">
								<div class="cateDetail" v-for="(item, index) in productList" :key="index">
									<div class="pro-img">
										<img :src="item.url" alt=""/>
									</div>
									<div class="proCate">
										{{ item.cate }}
									</div>
								</div>
								<i></i>
								<i></i>
								<i></i>
							</div>
							<div class="pro-detail" v-show="showDetail">
								<template v-if="loadData">
									<div class="loadFirstData">
										<Loading></Loading>
									</div>
								</template>
								<div v-else class="proDetail">
									<div class="searchBox">
										<b class="icon-a-uspp-sousuozhuanhuan"></b>
										<input v-model="keyword" @input="changePro" type="text" :placeholder="langDesign.ProductKeywords"/>
									</div>
									<div class="proList">
										<div class="good-item" v-for="(item, index) in proDetail" :key="item.id">
											<div class="good-img">
												<img :src="item.showImgSrc" :alt="item.name" :title="item.name" loading="lazy"/>
											</div>
											<div class="good-back">
												<div class="good-color" v-if="item.productParamList && item.productParamList.length">
													<div class="good-color-item" v-show="item.sceneImg" :class="{ active: item.selectedColorIndex === -1 }" title="Gallery" @click.stop="toSceneImg($event, item)">
														<span class="color-circle"></span>
													</div>
													<div class="good-color-item" :class="{ active: item.selectedColorIndex === cindex }" @click="selectGoodColor($event, index, cindex, citem)" v-show="cindex < showColorNum" :title="citem.colorAlias" v-for="(citem, cindex) in item.productParamList" :key="cindex">
														<span class="color-circle" :style="{ background: citem.colorSecondary ? `linear-gradient(-45deg, ${citem.colorCode} 50%, ${citem.colorSecondary} 50%)` : citem.colorCode }"></span>
													</div>
													<span v-if="item.productParamList && item.productParamList.length - showColorNum > 0">+{{ item.productParamList.length - showColorNum }}</span>
												</div>
												<div class="good-info">
													<div v-if="isMobile" class="itemNo">{{ item.productSku }}</div>
													<h3 :title="item.name">
														{{ item.name }}
													</h3>
													<div class="xin">
														<template v-if="item.commentLevel > 0">
															<v-rating :value="item.commentLevel" background-color="#cccccc" color="#f9d309" half-increments readonly dense length="5"></v-rating>
															<span v-if="item.commentLevel">{{ item.commentLevel }}</span>
															<span v-if="item.commentNum" style="color: rgb(182, 177, 177)">({{ item.commentNum }})</span>
														</template>
													</div>
													<div class="priceBox">
														<div class="price" v-if="!item.discount">
															<div style="height: 17px"></div>
															{{ langDesign.priceFrom }}
															<label>${{ item.lowestPrice }}</label>
															<span v-show="item.highestPrice" style="color: #333333; font-weight: 400">{{ langDesign.to }}</span>
															<label v-show="item.highestPrice">${{ item.highestPrice }}</label>
														</div>
														<div class="discountPrice" v-else>
															<div class="oldPrice">
																{{ langDesign.listPrices }}
																<span class="price">${{ item.lowestPrice }}</span>
																<span style="text-decoration: line-through" v-show="item.highestPrice">{{ langDesign.to }}</span>
																<span v-show="item.highestPrice">${{ item.highestPrice }}</span>
															</div>
															<div class="nowPrice">
																<span>{{ langDesign.onSale }}</span>
																{{ langDesign.from }}
																<label>${{ item.lowestDiscountPrice }}</label>
																<span v-show="item.highestDiscountPrice" style="color: #333333; font-weight: 400">{{ langDesign.to }}</span>
																<label v-show="item.highestDiscountPrice">${{ item.highestDiscountPrice }}</label>
															</div>
														</div>
													</div>
													<div class="des" :title="item.description">{{ item.description }}</div>
												</div>
												<div class="good-collection" @click.stop="goCollection($event, item)">
													<b class="iconfont icon-shoucang" v-if="!item.isCollection"></b>
													<b class="iconfont icon-xinxin isActive" v-else></b>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import {getProductList, addCollection} from "@/api/web";
import {debounce} from "@/utils/utils";
import dzMixin from "@/mixins/dzMixin";
import star from "@/components/MyDzxt/star.vue";

export default {
	components: {
		star,
	},
	mixins: [dzMixin],
	data() {
		return {
			editorDraftObj:{},
			// {
			// "lapel-pins":{},
			// "medals":{},
			// "pvc-patches":{},
			// "patches":{},
			// "round-corner-stickers":{},
			// "challenge-coins":{},
			// "trading-pins":{},
			// "lanyards":{},
			// "belt-buckles":{}
			// },//所有全定制设计系统对应的草稿
			score: 3,
			debounceSearchProduct: null,
			text: 1,
			colorList: [0, 1, 2, 3, 4],
			productList: [],
			showDetail: false,
			proDetail: [],
			activeName: [],
			selectedCate: -1,
			tipDialog: false,
			productDialog: false,
			page: 1,
			pages: 0,
			pageSize: 20,
			totalResult: 0,
			sorts: 2,
			showColorNum: 6,
			loadData: false,
			loadingProduct: false,
			colorItem: "",
			keyword: ""
		};
	},
	created() {
		this.debounceSearchProduct = debounce(this.searchProduct, 600);
	},
	computed: {
		canvas() {
			return canvas
		},
		isDefault() {
			return this.$store.state.design.isDefault;
		},
		langDesign() {
			return this.$store.getters.lang?.design;
		},
		getSwitchProBtnText() {
			if (this.text == 1) {
				return this.langDesign.chooseProText;
			} else {
				return this.langDesign.changeProText;
			}
		},
		userId() {
			return this.$store.state.userInfo?.id;
		},
		isLogin() {
			return this.$store.getters.isLogin;
		},
		isMobile() {
			return this.$store.state.device === "mb";
		},
	},
	async mounted() {
		this.activeName = this.productList.map((item) => {
			item.showBtn = false;
			if (item.cateList && item.cateList.length != 0) {
				item.showBtn = true;
				return item.id;
			}
		});
        let key=this.$route.params.designName;
		this.editorDraftObj = JSON.parse(localStorage.getItem('editorDraftObj')) || {};
		if(this.editorDraftObj.hasOwnProperty(key)){
			//有草稿则提示弹窗
			this.$confirm(this.langDesign.saveDraftNotice,'', {
					confirmButtonText: this.langDesign.yes,
					cancelButtonText: this.langDesign.discardDraft,
				}).then(() => {
					this.canvas.loadTemplate(this.editorDraftObj[key]['templateFile']);
				}).catch(() => {
					delete this.editorDraftObj[key];
					localStorage.setItem('editorDraftObj',JSON.stringify(this.editorDraftObj));
				});
		}
		// const proDetail = document.querySelector(".pro-detail");
		// proDetail.addEventListener("scroll", this.handleScroll, true);
	},
	methods: {
		changeSize(type) {
			this.canvas.stickerSize[type] = this.canvas.stickerSize[type].replace(/[^0-9.]/g, '');
			if (!this.canvas.checkStickerSize()) {
				return false;
			}
			this.canvas.initStickerShape();
		},
		handleStickerSizeCommand(command) {
			this.canvas.stickerSize.w = command.w.toString();
			this.changeSize('w');
			this.canvas.stickerSize.h = command.h.toString();
			this.changeSize('h');
		},
		async next() {
			if (this.canvas.isEmptyCanvas()) {
				this.$toast.info("The canvas is empty");
				return false;
			}
			let templateFile = this.canvas.getTemplateData();
			let params = {
			templateFile: JSON.stringify(templateFile),
			}
			let key=this.$route.params.designName;
            this.editorDraftObj[key] = params;
			localStorage.setItem('editorDraftObj',JSON.stringify(this.editorDraftObj));
			if(this.canvas.isSticker){
				if (!this.canvas.checkStickerSize()) {
					return false;
				}
			}
			this.$emit("next");
		},
		selectProduct() {
			this.tipDialog = false;
			this.productDialog = true;
		},
		handleClose(done) {
			done();
		},
		showCate(item) {
			let findInd = this.activeName.findIndex((id) => item.id == id);
			if (findInd >= 0) {
				this.selectedCate = item.id;
				this.activeName.splice(findInd, 1);
				this.searchProduct("");
				this.$nextTick(() => {
					const proRight = document.querySelector(".pro-right");
					proRight.addEventListener("scroll", this.handleScroll);
				});
			} else {
				this.activeName.push(item.id);
				this.selectedCate = -1;
				// this.showDetail = false;
				// this.proDetail = [];
			}
		},
		selectCate(item) {
			this.selectedCate = item.id;
		},
		searchProduct(type) {
			this.loadingProduct = true;
			if (type !== "loadMore") {
				this.page = 1;
			}
			getProductList({
				categoryId: 6,
				childCategoryId: this.parentCateId ? this.cateId : null,
				page: this.page,
				pageSize: this.pageSize,
				keyWord: this.keyword,
				userId: this.isLogin ? this.userId : null,
				sorts: this.sorts,
				productType: 0,
			})
				.then((res) => {
					this.loadData = false;
					let productList = this.setDefaultShowImg(res.data.records);
					this.totalResult = res.data.total;
					this.pages = res.data.pages;
					if (type === "loadMore") {
						this.proDetail = this.proDetail.concat(productList);
					} else {
						this.proDetail = productList;
					}
					this.showDetail = true;
				})
				.finally(() => {
					this.loadData = false;
					this.loadingProduct = false;
				});
		},
		setDefaultShowImg(list) {
			if (!list) {
				return;
			}
			let colorItem = this.colorItem,
				colorId;
			if (colorItem) {
				colorId = colorItem.id;
			}
			list.forEach((item) => {
				if (item.productParamList && item.productParamList.length > 0) {
					let productParamList = item.productParamList;
					let findColorIndex = productParamList.findIndex((citem) => citem.attributeValueId === colorId);
					if (findColorIndex > -1) {
						item.showImgSrc = JSON.parse(productParamList[findColorIndex].imgJson)[0]?.url;
						item.selectedColorIndex = findColorIndex;
					} else {
						if (item.sceneImg) {
							item.showImgSrc = item.sceneImg;
							item.selectedColorIndex = -1;
						} else {
							try {
								item.showImgSrc = JSON.parse(productParamList[0].imgJson)[0]?.url;
								item.selectedColorIndex = 0;
							} catch (e) {
							}
						}
					}
				} else {
					item.showImgSrc = item.sceneImg;
					item.selectedColorIndex = -1;
				}
			});
			return list;
		},
		changePro() {
			this.debounceSearchProduct("");
		},
		toSceneImg(e, item) {
			e.stopPropagation();
			e.preventDefault();
			item.showImgSrc = item.sceneImg;
			item.selectedColorIndex = -1;
		},
		selectGoodColor(e, ind1, ind2, citem) {
			e.stopPropagation();
			e.preventDefault();
			this.$set(this.proDetail[ind1], "selectedColorIndex", ind2);
			this.$set(this.proDetail[ind1], "showImgSrc", this.parseJSON(citem.imgJson)[0]?.url);
		},
		parseJSON(str) {
			return str ? JSON.parse(str) : [];
		},
		goCollection(e, item) {
			e.stopPropagation();
			e.preventDefault();
			let isCollection = item.isCollection;
			if (!this.isLogin) {
				this.$store.commit("setLogin", "login");
				return;
			}
			if (isCollection) {
				deleteConllectionByUserId({
					userId: this.userId,
					productId: item.id,
				}).then((res) => {
					item.isCollection = false;
					this.$forceUpdate();
				});
			} else {
				addCollection({
					userId: this.userId,
					website: 1,
					cateId: this.parentCateId || this.cateId,
					productId: item.id,
				}).then((res) => {
					item.isCollection = true;
					this.$forceUpdate();
				});
			}
		},
		handleScroll() {
			try {
				let productList = document.querySelector(".proList");
				let proRight = document.querySelector(".pro-right");
				let rect = productList.getBoundingClientRect();
				let targetHeight = rect.height;
				let contentHeight = proRight.clientHeight; //页面高度
				let scrollBarTop = proRight.scrollTop; // 滚动条离页面顶端的距离
				let threshold = 100; // 可以指定提前加载的距离
				if (targetHeight < contentHeight + scrollBarTop + threshold) {
					//加载数据
					if (this.loadingProduct) {
						return false;
					}
					this.loadMore();
				}
			} catch (e) {
			}
		},
		loadMore() {
			if (this.totalResult === 0 || this.pages <= this.page) {
				return;
			}
			this.page += 1;
			this.searchProduct("loadMore");
			// this.debounceSearchProduct("loadMore", true);
		},
		closeModal() {
			this.productDialog = false;
			this.selectedCate = -1;
		},
	},
};
</script>

<style scoped lang="scss">
.bottom-area {
	position: absolute;
	left: 90px;
	right: 0;
	bottom: 0;
	background-color: #ffffff;
	height: 80px;
	box-shadow: 0px -4px 6px rgba(0, 0, 0, 0.15);
}

.bottom-container {
	width: 100%;
	height: 100%;
	display: grid;
	padding: 0 20px;
	grid-template-columns: minmax(410px, 540px) 1fr;

	.bottom-left {
		display: flex;
		align-items: center;
		justify-content: space-between;

		.switch-pro {
			display: none;
			width: 160px;
			height: 48px;
			margin-left: 20px;
			color: #ffffff;
			font-size: 18px;
			font-weight: bold;
			line-height: 48px;
			text-align: center;
			background-color: #2996fb;
			border: 1px solid #2996fb;
			border-radius: 6px;
		}

		.stickerSize {
			display: flex;
			align-items: center;

			.size {
				margin-left: 10px;

				.c {
					margin: 0 10px;
				}

				.sizeSelect {
					display: flex;
					justify-content: center;
					align-items: center;
					width: 24px;
					height: 24px;
					background: #EBEBEB;
					border-radius: 50%;
					margin-left: 14px;

					b {
						font-weight: 700;
					}
				}
			}

			.sizeInput {
				width: 70px;
				height: 40px;
				background: #FFFFFF;
				border: 1px solid #DBDBDB;
				border-radius: 4px;
				padding: 0 10px;

				&.isError {
					border-color: red;
				}
			}
		}
	}

	.bottom-right {
		height: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 0 1em;

		.bottom-right-1 {
			width: 100px;
		}

		.bottom-right-2 {
            flex:1;
			height: 100%;
			display: flex;
            justify-content: flex-end;
			align-items: center;

			.getPrice {
				width: 180px;
				height: 48px;
				background: #0a9a35;
				border-radius: 6px;
				color: #ffffff;
				font-size: 20px;
				font-weight: 700;
				line-height: 48px;
				text-align: center;
				cursor: pointer;
			}
		}
	}

	::v-deep .custom-dialog-tip .el-dialog__header .el-dialog__title {
		font-size: 18px;
		font-weight: bold;
		color: #333333;
	}

	::v-deep .custom-dialog-tip .el-dialog__header .el-dialog__headerbtn {
		color: #666666;
	}

	.modal {
		position: fixed;
		height: 100%;
		width: 100%;
		top: 0;
		left: 0;
		background-color: rgba(0, 0, 0, 0.6);
		z-index: 999;

		.proBox {
			position: absolute;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
			width: 80%;
			height: 700px;
			padding: 10px 20px;
			background-color: #ffffff;

			.custom-title {
				position: relative;
				text-align: center;
				padding-bottom: 10px;

				h3 {
					font-size: 24px;
					font-weight: 400;
					color: #333333;
				}

				b {
					position: absolute;
					right: 20px;
					top: 50%;
					transform: translateY(-50%);
					font-size: 20px;
					color: #666666;
				}
			}

			.productInfo {
				width: 100%;
				height: 610px;
				display: grid;
				grid-template-columns: 240px 1fr;
				column-gap: 20px;

				.pro-left {
					max-height: 600px;
					overflow: hidden auto;

					&::-webkit-scrollbar {
						display: none;
					}

					.cateTitle {
						width: 100%;
						height: 43px;
						font-size: 18px;
						font-weight: bold;
						color: #333333;
						line-height: 43px;
						border-bottom: 1px solid #ebebeb;
					}

					.pro-cates {
						.cate {
							padding: 8px 0;
							font-size: 16px;
							font-weight: bold;
							color: #333333;
							display: flex;
							align-items: center;
							justify-content: space-between;

							&.active {
								color: #2996fb;
							}

							b {
								// float: right;
								padding: 5px;
								font-size: 14px;
								color: #555555;
								margin-right: 10px;
							}
						}

						.cateList {
							padding-left: 5px;

							.cateChild {
								padding: 2px 0;
								font-size: 14px;
								color: #666666;

								&.active {
									color: #2996fb;
								}

								&:hover {
									color: #2996fb;
								}
							}
						}
					}
				}

				.pro-right {
					height: 610px;
					overflow-y: auto;

					.pro-show {
						display: flex;
						align-items: flex-end;
						justify-content: space-between;
						flex-wrap: wrap;
						// overflow: auto;
						i {
							width: calc(20% - 10px);
						}

						.cateDetail {
							width: calc(20% - 10px);
							margin-bottom: 20px;
							display: flex;
							align-items: center;
							justify-content: center;
							flex-direction: column;

							.pro-img {
								aspect-ratio: 1;

								img {
									aspect-ratio: 1;
									object-fit: contain;
								}
							}

							.proCate {
								margin-top: 8px;
								font-weight: bold;
								// color: #2996fb;
								&:hover {
									color: #2996fb;
								}
							}
						}
					}

					.pro-detail {
						height: 100%;

						.proDetail {
							padding: 10px 10px 40px;
							background: #f6f6f6;

							.searchBox {
								position: relative;
								display: flex;
								align-items: center;
								justify-content: flex-end;

								input {
									min-width: 350px;
									background-color: #fff;
									border-radius: 18px;
									height: 30px;
									line-height: 30px;
									outline: none;
									border: none;
									padding: 0 50px 0 20px;
								}

								b {
									position: absolute;
									right: 20px;
									top: 50%;
									transform: translateY(-50%);
									font-size: 18px;
									color: #9b9b9b;
								}
							}

							.proList {
								display: grid;
								grid-template-columns: repeat(4, 1fr);
								gap: 10px;
								margin-top: 10px;
								height: 100%;
								overflow-y: auto;
								@media screen and (max-width: 1640px) {
									grid-template-columns: repeat(3, 1fr);
								}

								.good-item {
									min-width: 0;
									position: relative;
									box-shadow: 1px 1px 4px 0px rgba(0, 0, 0, 0.06);
									border-radius: 4px;
									background-color: #fff;
									transition: all 0.3s;
									overflow: hidden;
									user-select: text;
									cursor: pointer;
									display: grid;
									grid-template-rows: auto 1fr;

									.good-collection {
										position: absolute;
										top: 10px;
										left: 10px;

										b {
											color: #999999;
											font-size: 24px;
										}

										b.isActive {
											color: #2996fb;
											// background: #2996fb;
										}
									}

									.good-img {
										padding: 5px;
										aspect-ratio: 1;
										display: flex;
										align-items: center;
										justify-content: center;

										img {
											object-fit: contain;
											aspect-ratio: 1;
										}
									}

									.good-back {
										.good-color {
											display: flex;
											flex-wrap: wrap;
											align-items: center;
											justify-content: flex-start;
											padding: 10px;

											.good-color-item {
												display: flex;
												justify-content: center;
												align-items: center;
												border: 2px solid transparent;
												padding: 2px;
												border-radius: 50%;
												margin-right: 8px;
												transition: all 0.3s;
												@include respond-to(mb) {
													margin-right: 6px;
												}

												&:hover {
													border-color: #ccc;
												}

												.color-circle {
													display: inline-block;
													width: 12px;
													height: 12px;
													background: url("https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230619/20230619xcxWJWKD.png") center/contain no-repeat;
													border-radius: 50%;
													outline: 1px solid #ccc;
												}
											}

											.good-color-item.active {
												border-color: #2996fb;
											}
										}

										.good-info {
											display: flex;
											flex-direction: column;
											justify-content: center;
											padding: 15px;
											transition: all 0.3s;

											h3 {
												font-size: 20px;
												height: 48px;
												line-height: 1.2;
												word-break: break-word;
												text-overflow: ellipsis;
												display: -webkit-box;
												-webkit-box-orient: vertical;
												-webkit-line-clamp: 2; /* 这里是超出几行省略 */
												overflow: hidden;
												user-select: auto !important;
												-moz-user-select: auto !important;
												-webkit-user-select: auto !important;
												-ms-user-select: auto !important;
												pointer-events: auto !important;
												cursor: auto;
											}

											.priceBox {
												.price {
													margin: 0 0 8px 0;
													font-size: 16px;

													label {
														// color: $color-primary;
														font-size: 18px;
														font-weight: bold;
													}
												}

												.discountPrice {
													margin: 0 0 8px 0;
													font-size: 18px;

													.oldPrice {
														font-size: 14px;
														color: #636363;

														span {
															font-size: 14px;
															text-decoration: line-through;
															color: #636363;
															font-weight: 400;
														}
													}

													span {
														// color: $color-primary;
														font-weight: bold;
													}

													label {
														// color: $color-primary;
														font-size: 18px;
														font-weight: bold;
													}
												}
											}

											.des {
												color: #999999;
												font-size: 12px;
												word-break: break-word;
												text-overflow: ellipsis;
												display: -webkit-box;
												-webkit-box-orient: vertical;
												-webkit-line-clamp: 1; /* 这里是超出几行省略 */
												overflow: hidden;
												height: 14px;
											}

											.xin {
												display: flex;
												align-items: center;
												margin: 5px 0;
												height: 30px;

												span {
													margin-left: 4px;
													font-size: 14px;
													padding-top: 4px;
												}

												@include respond-to(mb) {
													height: auto;
													margin: 2px 0;
													::v-deep .v-rating .v-icon {
														font-size: 20px;
													}
													span {
														font-size: 12px;
													}
												}
											}
										}
									}
								}
							}
						}
					}
				}
			}
		}
	}

	.fade-enter-active,
	.fade-leave-active {
		transition: opacity 0.2s;
	}

	.fade-enter,
	.fade-leave-to {
		opacity: 0.1;
	}

	@media (any-hover: hover) {
		.good-item:hover .good-back .good-info h3 {
			color: #2996fb;
		}
	}
}
</style>
