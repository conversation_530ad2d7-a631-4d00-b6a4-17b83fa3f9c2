<template>
	<div class="pinsInquiryBox">
		<div class="head">
			<slot></slot>
		</div>
		<myForm ref="pinsMyform">
			<div class="flexBox">
				<myInput class="nameItem" v-model="pinsForm.firstName" prop="firstName"
					:rules="pinsFormRules.firstNameRules" :textLabel="lang.firstName" requireIcon="post"> </myInput>
				<myInput class="nameItem" v-model="pinsForm.lastName" prop="lastName"
					:rules="pinsFormRules.lastNameRules" :textLabel="lang.lastName" requireIcon="post"> </myInput>
			</div>
			<myInput v-model="pinsForm.email" prop="email" :rules="pinsFormRules.emailRules" :textLabel="lang.email"
				requireIcon="post" @blur="getSocialCode"> </myInput>
			<div class="flexBox">
				<myInput class="areaPhone" ref="areaPhone" v-model="pinsForm.telephone" prop="telephone"
					:textLabel="lang.telephone" placeholder="Enter your phone number"> </myInput>
				<myInput class="areaCode" placeholder="area" ref="areaCode" v-model="pinsForm.areaCode"
					@input="handleInputAreaCode"></myInput>
			</div>
			<myInput v-model="pinsForm.subject" :textLabel="lang.title" :placeholder="titlePlaceholder"> </myInput>
			<div class="groupNumber" :class="{ 'error--text': groupNotValidate }">
				<label>
					<span class="textLabel">{{ lang.fdPins.pleaseOneGroupNum }}</span>
					<div class="groupBox">
						<div class="groupItem" v-for="item in pinsForm.groupNumber" :key="item.id">
							<myInput v-model="item.value" :textLabel="item.name" @blur="startCheckGroup"></myInput>
						</div>
					</div>
					<div class="textField">
						<div class="vaildate-message" v-show="groupNotValidate">{{ lang.fdPins.pleaseOneGroupNum }}
						</div>
					</div>
				</label>
			</div>
			<div class="noCheckBox">
				<div class="tip">
					{{ lang.fd.fdInquiryTip }}
					<span style="color: #de3500;">*</span>
				</div>
				<div class="selectBox">
					<div class="allow-checkbox" @click="noCheck = !noCheck" :class="{ active: noCheck }">
					</div>
					<span class="allow-title">{{ lang.fd.otherNumber }}</span>
				</div>
			</div>
		</myForm>
		<div class="pinsComments">
			<span class="textLabel">{{ lang.Comments }}</span>
			<textarea v-model="pinsForm.remark" :placeholder="commentPlaceholder"></textarea>
		</div>
		<div class="pinsSubmitBtn">
			<div class="cancelBtn btnItem" @click="cancelForm">{{ lang.Cancel }}</div>
			<div class="submitBtn btnItem" @click="submitForm">{{ lang.Submit }}</div>
		</div>
	</div>
</template>

<script>
import myForm from "@/components/modal/Half/Detail/myForm/my-form.vue";
import myInput from "@/components/modal/Half/Detail/myForm/my-input.vue";
import { getSocialCodeByEmail } from "@/api/web";
export default {
	name: "pinsInquiryBox",
	components: { myForm, myInput },
	props: {
		pinsForm: {
			type: Object,
			default: () => ({}),
		},
		titlePlaceholder: {
			type: String,
			default: "",
		},
		commentPlaceholder: {
			type: String,
			default: "",
		},
	},
	data() {
		return {
			codeValidate: false,
			needCheck: false,
			noCheck: false,
		};
	},
	watch: {},
	computed: {
		lang() {
			return this.$store.getters.lang.quote || {};
		},
		isLogin() {
			return this.$store.getters.isLogin;
		},
		codeMessage() {
			if (this.codeValidate) {
				return this.lang.areaCode;
			}
			return this.lang.p33;
		},
		pinsFormRules() {
			return {
				firstNameRules: [{ required: true, message: this.lang.p28, trigger: "blur" }],
				lastNameRules: [{ required: true, message: this.lang.p29, trigger: "blur" }],
				emailRules: [
					{ required: true, message: this.lang.p30, trigger: "blur" },
					{
						validator: (rule, value, callback) => {
							let rex = /.+@.+\..+/;
							if (value && !rex.test(value)) {
								callback(new Error(this.lang.p31));
							} else {
								callback();
							}
						},
						trigger: "blur",
					},
				],
			};
		},
		groupNotValidate() {
			if (!this.needCheck || this.noCheck) return false;
			return !this.pinsForm.groupNumber.some((item) => {
				return item.value && ("" + item.value).length > 0;
			});
		},
		userInfo() {
			return this.$store.state.userInfo;
		},
	},
	methods: {
		handleInputAreaCode(event) {
			// 只允许输入数字
			this.pinsForm.areaCode = event.target.value.replace(/[^\d]/g, "");
		},
		thoneFocus() {
			this.codeValidate = false;
			this.$nextTick(() => {
				this.$refs.areaPhone.validate();
			});
		},
		focusAreaCode() {
			this.codeValidate = true;
			if (("" + this.pinsForm.areaCode).length > 0) {
				this.$nextTick(() => {
					this.$refs.areaPhone.resetValue();
				});
			} else {
				this.$nextTick(() => {
					this.$refs.areaPhone.validate();
				});
			}
		},
		validateAreaCode() {
			if (("" + this.pinsForm.areaCode).length > 0) {
				this.codeValidate = false;
				this.$nextTick(() => {
					this.$refs.areaPhone.resetValue();
				});
			} else {
				this.codeValidate = true;
				this.$nextTick(() => {
					this.$refs.areaPhone.validate();
				});
			}
		},
		startCheckGroup() {
			if (!this.needCheck) this.needCheck = true;
		},
		cancelForm() {
			this.needCheck = false;
			this.$emit("cancelForm");
		},
		async submitForm() {
			const pinsMyform = this.$refs.pinsMyform;
			if (pinsMyform) {
				const valid = await pinsMyform.validate();
				if (!valid) return;
			}
			this.needCheck = true;
			if (this.groupNotValidate) return;
			this.$emit("submitForm", this.noCheck);
		},
		getSocialCode() {
			let rex = /.+@.+\..+/;
			if (rex.test(this.pinsForm.email)) {
				getSocialCodeByEmail({
					email: this.pinsForm.email
				}).then(res => {
					if (res.data && res.data.length > 0) {
						let socialCode = JSON.parse(res.data);
						this.pinsForm.groupNumber.forEach((item, index) => {
							if (item.value === undefined || item.value === null || item.value === '') {
								let key = item.name;
								if (socialCode[key] !== undefined) {
									this.$set(this.pinsForm.groupNumber, index, {
										...item,
										value: socialCode[key]
									});
								}
							}
						})
					}
				})
			}
		}
	},
	created() { },
	mounted() {
		if (this.isLogin) {
			if (this.userInfo && this.userInfo.email) {
				this.pinsForm.email = this.userInfo.email
				this.getSocialCode()
			}
		}
	},
};
</script>

<style scoped lang="scss">
.pinsInquiryBox * {
	font-family: Calibri;
}

.pinsInquiryBox {
	padding: 20px 30px;
	color: #333333;
	font-family: Calibri;

	@include respond-to(mb) {
		padding: 20px 10px;
	}

	.head {
		font-weight: bold;
		font-size: 18px;
		margin-bottom: 10px;
	}

	::v-deep .my-form {
		.myInput {
			label {
				font-size: 14px;

				input {
					background: #ffffff;
					border-radius: 4px;
					border: 1px solid #dddddd;

					&::placeholder {
						font-weight: 400;
						font-size: 12px;
						color: #999999;
					}
				}
			}

			.textLabel {
				font-size: 14px;

				&::after {
					margin-left: 4px;
				}
			}
		}

		.flexBox {
			position: relative;
			width: 100%;
			display: flex;
			align-items: center;
			column-gap: 12px;

			.nameItem {
				flex: 1;
				min-width: 0;
			}

			.areaCode {
				position: absolute;
				left: 2px;
				top: 28px;
				width: 64px;
				height: 28px;
				margin: 0;

				&::before {
					content: "+";
					font-size: 18px;
					position: absolute;
					top: 50%;
					left: 8px;
					transform: translateY(-50%);
					z-index: 1;
					color: #666666;
				}

				input {
					color: #666666;
					margin: 0;
					border-radius: 0;
					border: none;
					border-right: 1px solid #dfdfdf;
					padding: 8px 4px 8px 22px;
					height: 28px;
				}

				.textField {
					display: none;
				}

				@include respond-to(mb) {
					height: 28px;

					input {
						height: 28px;
					}
				}
			}
		}

		.areaPhone {
			position: relative;
			width: 100%;

			input {
				padding: 8px 10px 8px 90px;

				&::placeholder {
					margin-left: 10px;
				}
			}
		}
	}

	::v-deep .groupNumber {
		width: 100%;

		&.error--text {
			input {
				border: 1px solid #ff5252 !important;
			}

			.vaildate-message {
				color: #ff5252 !important;
				caret-color: #ff5252 !important;
			}
		}

		label {
			position: relative;
			display: flex;
			flex-direction: column;
			font-size: 14px;
			pointer-events: none;

			.textLabel {
				margin-bottom: 4px;
			}

			input {
				pointer-events: all;
				padding: 8px 10px;
				background: #f5f5f5;
				border: 1px solid transparent;
				border-radius: 6px;

				&::placeholder {
					font-size: 14px;
					color: inherit;
				}
			}

			span {
				&::after {
					content: "*";
					color: #de3500;
					margin-left: 4px;
				}
			}

			@include respond-to(mb) {
				font-size: 12px;
			}
		}

		.textField {
			margin-top: 4px;
			pointer-events: none;
			display: flex;
			flex: 1 0 auto;
			max-width: 100%;
			min-height: 14px;
			overflow: hidden;
			line-height: 12px;
			font-size: 12px;
			word-break: break-word;
			word-wrap: break-word;
			hyphens: auto;
		}

		.groupBox {
			width: 100%;
			display: grid;
			grid-template-columns: repeat(3, 1fr);
			column-gap: 20px;

			@include respond-to(mb) {
				column-gap: 10px;
			}

			.groupItem {
				min-width: 0;

				.myInput {
					label {
						font-size: 14px;

						input {
							background: #ffffff;
							border-radius: 4px;
							border: 1px solid #dddddd;

							&::placeholder {
								font-weight: 400;
								font-size: 12px;
								color: #999999;
							}
						}
					}

					.textLabel {
						font-size: 14px;

						&::after {
							content: none;
						}
					}

					.textField {
						display: none;
					}
				}
			}
		}
	}

	.pinsComments {
		margin-top: 4px;
		width: 100%;
		display: flex;
		flex-direction: column;

		.textLabel {
			font-size: 14px;
			margin-bottom: 4px;
		}

		textarea {
			padding: 4px 6px;
			font-size: 14px;
			width: 100%;
			height: 120px;
			resize: none;
			overflow-y: auto;
			background: #ffffff;
			border-radius: 2px;
			border: 1px solid #dddddd;

			&::placeholder {
				font-size: 12px;
				color: #999999;
			}
		}
	}

	.pinsSubmitBtn {
		margin-top: 20px;
		position: relative;
		width: 100%;
		display: flex;
		align-items: center;
		column-gap: 18px;

		.btnItem {
			flex: 1;
			min-width: 0;
			height: 42px;
			border-radius: 10px;
			font-weight: bold;
			font-size: 16px;
			line-height: 42px;
			text-align: center;
			cursor: pointer;
			user-select: none;

			&.cancelBtn {
				color: #ff6600;
				border: 1px solid #ff6600;
			}

			&.submitBtn {
				color: #fff;
				background: #ff6600;
			}
		}
	}

	.noCheckBox {
		margin: 6px 0;

		.tip {
			font-size: 15px;
			color: #333;
			margin-bottom: 4px;
		}

		.selectBox {
			display: flex;
			align-items: center;
			gap: 1em;

			.allow-checkbox {
				height: 2em;
				width: 2em;
				background-color: #fff;
				border-radius: 2px;
				border: 1px solid #000;
				position: relative;
				flex-shrink: 0;
				cursor: pointer;

				&.active::before {
					content: "";
					position: absolute;
					left: 50%;
					top: 50%;
					height: 18px;
					width: 8px;
					transform: translate(-50%, -60%) rotate(45deg);
					border-right: 2px solid #ff5252;
					border-bottom: 2px solid #ff5252;
				}
			}

			.allow-title {
				color: rgba(0, 0, 0, 0.6);
				font-weight: 400;
				font-size: 13px;
			}
		}

		@include respond-to(mb) {
			.tip {
				font-size: 12px;
			}

			.selectBox {
				.allow-title {
					font-size: 12px;
				}
			}
		}
	}
}
</style>
