<template>
	<div class="modal-box summary-box" :class="modal.class" :style="modal.style">
		<div flex v-for="(o, oi) in modal.outer" :key="oi" :class="{ position: modal.position }"
			:style="{ ...modal.boxStyle, ...o.style }">

			<!-- mb || position -->
			<EditDiv :tagName="o.title.tagName || 'h2'" v-model:content="o.title.value" class="summary-title"
				:style="{ ...modal.titleStyle, ...o.title.style, ...o.titleStyle }"
				@click="setModalType(o.title, modal.outer, 'text')"
				v-if="o.title?.value && (($store.getters.isMobile && !modal.position) || (!$store.getters.isMobile && modal.position))">
			</EditDiv>

			<PicVideo class="pic" v-if="o.img" :modal="modal" :data="o" data-source="outer"
				:pic-style="{ ...modal.imgStyle, ...o.img.style, ...o.imgStyle }" @setModalType="setModalType" />

			<b class="icon" :class="o.icon.value" :style="{ ...modal.cardImgStyle, ...o.icon.style, ...o.iconStyle }"
				v-if="o.icon" @click="setModalType(o.icon, modal.outer, 'icon')"></b>

			<VideoPlayer class="pic" :videoUrl="o.video.value" v-if="modal.theme < 4 && o.video" :title="o.video?.alt"
				:style="{ ...modal.imgStyle, ...o.video.style }" @click="setModalType(o.video, modal.outer, 'video')">
			</VideoPlayer>

			<PicVideo class="pic" v-else-if="o.video" :modal="modal" :data="o" data-source="outer"
				:video-box-style="{ ...modal.imgStyle, ...modal.videoBoxStyle, ...o.videoBoxStyle }"
				:video-style="{ ...modal.videoStyle }" @setModalType="setModalType" />

			<div flex class="content part2" :style="{ ...modal.contentStyle, ...o.contentStyle }" :childHoverIndex="oi">
				<!-- pc || !position-->
				<EditDiv :tagName="o.title.tagName || 'h2'" v-model:content="o.title.value"
					:style="{ ...modal.titleStyle, ...o.title.style, ...o.titleStyle }"
					@click="setModalType(o.title, modal.outer, 'text')"
					v-if="o.title?.value && ((!$store.getters.isMobile && !modal.position) || ($store.getters.isMobile && modal.position))">
				</EditDiv>

				<div :style="{ ...modal.subTitleStyle, ...o.subTitle?.style, ...o.subTitleStyle }"
					@click="setModalType(o.subTitle, modal.outer, 'text')" v-if="o.subTitle?.value">
					<EditDiv v-model:content="o.subTitle.value" :tagName="o.subTitle.tagName" />
				</div>

				<EditDiv v-model:content="o.text.value" :style="{ ...modal.textStyle, ...o.text.style, ...o.textStyle }"
					v-if="o.text?.value" @click="setModalType(o.text, modal.outer, 'text')" />

				<div flex class="btn-box" :style="modal.btnBoxStyle" v-if="o.button?.value || o.button1?.value">
					<button v-if="o.button?.value" :primary="!o.button.outline" :outline="o.button.outline"
						:style="{ ...modal.btnStyle, ...o.button.style, ...o.btnStyle }" :title="o.button.alt"
						@click="setModalType(o.button, modal.outer, 'button', o.button)">
						<EditDiv v-show="o.button.value" tagName="label" v-model:content="o.button.value" />
						<b :class="o.button.icon" v-if="o.button.icon" :style="o.button.iconStyle"></b>
					</button>

					<button v-if="o.button1?.value" :primary="!o.button1.outline" :outline="o.button1.outline"
						:style="{ ...modal.btn1Style, ...o.button1.style }" :title="o.button1.alt"
						@click="setModalType(o.button1, modal.outer, 'button', o.button1)">
						<EditDiv v-show="o.button1.value" tagName="label" v-model:content="o.button1.value" />
						<b :class="o.button1.icon" v-if="o.button1.icon" :style="o.button1.iconStyle"></b>
					</button>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	name: "modalSummary",
	props: {
		data: {
			type: Object,
			default: {},
		}
	},
	data() {
		return {
			modal: {
				style: {},
				type: {},
				outer: [{}],
				...this.data
			}
		};
	},
	computed: {
		videoList() {
			return document.querySelectorAll(`#${this.modal.id} video`) || [];
		}
	},
	watch: {
		modal: {
			handler(val) {
				if (process.env.isManage) this.$emit("update:data", val);
			},
			immediate: true,
			deep: true
		}
	},
	methods: {
		setModalType(target, targetArray, clickType, event, other) {
			this.$setModal(this, target, targetArray, clickType, event, other)
		}
	},
	mounted() {
		this.$nextTick(() => {
			// 默认视频播放方式为页面滑动到所在位置自动播放
			if (!this.modal.manualPlay && !this.modal.mousePlay && this.videoList?.length) document.addEventListener("scroll", () => {
				this.videoList.forEach(i => {
					if (i.play && i.getBoundingClientRect && (i.getBoundingClientRect().top < window.innerHeight)) i.play();
				});
			})
		});
	}
};
</script>

<style lang="scss" scoped>
.modal-box {
	>div {
		grid-gap: 1em;

		&.position {
			flex-wrap: wrap;

			.summary-title {
				width: 100%;
				margin-bottom: 1em;
			}
		}

		>:not(.pic) {
			z-index: 1;
		}
	}

	.pic {
		width: 43%;
		overflow: hidden;
		border-radius: 10px;
	}

	.content {
		flex: 1;
		grid-gap: 1.5em;
		flex-direction: column;
		justify-content: center;

		h1,
		h2 {
			text-align: inherit;
		}

		.btn-box {
			order: 5;
			grid-gap: 0.5em;
			margin-top: 1vw;
		}
	}
}


[theme='4'] {
	h1,
	h2 {
		color: $color-primary;
	}

	.content {
		line-height: 1.4;
	}
}


.h2-line .content div:nth-child(1) {
	gap: 1.1vw;
	display: flex;
	align-items: center;
	justify-content: center;

	&::before,
	&::after {
		all: unset;
		content: "";
		flex: 1;
		border-top: 1px solid #333333;
	}
}


.tea-story>div {
	&:nth-child(even) {
		justify-content: flex-end;

		img {
			order: 1;
			margin-right: 0 !important;
		}
	}

	&:nth-child(odd) .content {
		margin-right: 0 !important;
	}
}



@media screen and (max-width: $mb-width) {
	.summary-box.modal-box {
		width: 100vw;
		overflow: hidden;

		>div {
			text-align: center;
			flex-direction: column;
		}

		.content {
			grid-gap: 1em;
			justify-content: flex-start;
		}

		.pic {
			width: 100%;
			border-radius: 0;
		}

		.btn-box {
			justify-content: center;
		}
	}

	[theme='4'] {
		z-index: auto;
	}
}



@media screen and (max-width: 700) {
	.summary-box.modal-box {
		padding-left: 0;
		padding-right: 0;
	}
}
</style>
