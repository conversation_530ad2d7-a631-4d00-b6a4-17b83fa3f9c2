<template>
	<v-app>
		<v-main>
			<template v-for="(o, oi) in modal.outer">
				<div class="main modal-box" style="position: relative" :key="oi">
				<div class="pcTitle">
					<h2 class="title">{{ productName }} On Trends- Best Sellers</h2>
				</div>
				<div class="mbTitle">
					<h2 class="title">{{ productName }} On Trends- Best Sellers</h2>
					<div class="mb_search">
						<div class="right_Navigation">
							<input type="text" placeholder="Product Keywords..." v-model="keyword">
							<b class="icon-a-uspp-sousuozhuanhuan"></b>
						</div>
						<div class="left_Navigation" @click="collapse = true">
							<svg t="1735549393288" class="icon" viewBox="0 0 1025 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="28543" width="13" height="13"><path d="M988.578182 93.843988h-113.750289a131.381583 131.381583 0 0 0-251.388136 0H35.350767a34.125086 34.125086 0 0 0-25.025063 9.668774 34.125086 34.125086 0 0 0 0 48.912624 34.125086 34.125086 0 0 0 25.025063 9.668775H625.714762a130.24408 130.24408 0 0 0 124.556566 93.843987 131.950334 131.950334 0 0 0 126.26282-93.843987h113.750288a34.693838 34.693838 0 0 0 25.025063-9.668775 34.125086 34.125086 0 0 0 0-48.912624 34.693838 34.693838 0 0 0-25.025063-9.668774z m-238.306854 93.275236a56.875144 56.875144 0 1 1 60.856404-56.875144 60.287653 60.287653 0 0 1-60.856404 56.875144zM988.578182 455.001152a34.693838 34.693838 0 0 1 25.025063 9.668775 34.125086 34.125086 0 0 1 0 48.912624 34.693838 34.693838 0 0 1-25.025063 9.668774H398.214186a131.381583 131.381583 0 0 1-251.956888 0h-113.750288A34.125086 34.125086 0 0 1 10.325704 511.876297a34.125086 34.125086 0 0 1 0-48.912624A34.125086 34.125086 0 0 1 35.350767 455.001152h113.750288A131.381583 131.381583 0 0 1 398.214186 455.001152zM273.657621 545.432632a56.875144 56.875144 0 1 0-60.856404-56.875145 59.718901 59.718901 0 0 0 60.856404 56.875145z m714.920561 316.225801a34.693838 34.693838 0 0 1 25.025063 9.668774 34.125086 34.125086 0 0 1 0 48.912624 34.693838 34.693838 0 0 1-25.025063 9.668774h-113.750289a131.381583 131.381583 0 0 1-251.956888 0H35.350767a34.125086 34.125086 0 0 1-25.025063-9.668774 34.125086 34.125086 0 0 1 0-48.912624 34.125086 34.125086 0 0 1 25.025063-9.668774H625.714762a131.381583 131.381583 0 0 1 251.388137 0z m-238.306854 93.275236a56.875144 56.875144 0 1 0-60.287653-56.875144 60.287653 60.287653 0 0 0 60.287653 56.875144z m0 0" p-id="28544"></path></svg>
							<p>Filters</p>
						</div>
					</div>
				</div>

				<div class="topNavigation" >
					<div class="left_Navigation">
						<div class="left_grid">
							<div>
								Filters <span class="subtitle">({{hatsData.total}} Results)</span>
							</div>

							<div v-for="(item,index) in tableList" class="grid_div" :class="{ active:index === activeIndex }" :key="index" >
								<div @click.stop="openSelectDiv(index)">{{item.nameEn}} <svg t="1736212108025" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="29692" width="13" height="13"><path d="M512 704c8.288 0 15.776-3.232 21.456-8.4l0.064 0.08 352-320-0.08-0.08c6.448-5.856 10.56-14.208 10.56-23.6a32 32 0 0 0-32-32c-8.288 0-15.76 3.232-21.456 8.4l-0.08-0.08L512 628.752 181.536 328.32l-0.08 0.08A31.776 31.776 0 0 0 160 320a32 32 0 0 0-32 32c0 9.376 4.112 17.744 10.544 23.6l-0.08 0.08 352 320 0.08-0.08c5.68 5.168 13.168 8.4 21.456 8.4z" p-id="29693"></path></svg></div>

								<div class="selectDiv" v-show="showSelectDiv(index)">
									<div style="text-align: left;height: 100%;overflow-y: auto" class="custom-scrollbar">
										<div v-for="(eitem,eindex) in item.attributeList" :key="eitem.id" >
											<div v-if="item.nameEn == 'price'">
												<div style="margin-top: 50px">
													<span>{{ eitem.valueName }}</span>
													<span>{{ eitem.remark }}</span>
													<v-range-slider class="custom-slider" @change="changeRange" v-model="priceRange" thumb-label :min="eitem.valueName" :max="eitem.remark" hide-details :step="0.01"></v-range-slider>
													<div style="display: flex">
														<div style="flex: 1">
															<CCYRate :price="eitem.valueName"></CCYRate>
														</div>
														<div style="flex: 1;text-align: end">
															<CCYRate :price="eitem.remark"></CCYRate>
														</div>
													</div>
												</div>
											</div>
											<label @change="selectList(eitem)" v-else>
												<div class="checkbox__wrap">
													<input type="checkbox" :id="eitem.id" v-model="selectedOptions" :value="eitem.valueName"  />
													<span></span>
												</div>
												<span>{{ eitem.valueName }}</span>
											</label>
										</div>
									</div>
								</div>


								<p v-show="showSelectDiv(index)" @click.stop="openSelectDiv(index)">
									<svg t="1737331184440" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="31128" width="20" height="20"><path d="M512 955.733333c-121.873067 0-227.549867-43.588267-314.112-129.536C111.854933 739.5328 68.266667 633.873067 68.266667 512c0-121.890133 43.605333-227.293867 129.5872-313.2928C284.416 112.145067 390.109867 68.266667 512 68.266667c121.924267 0 227.328 43.895467 313.326933 130.491733C911.837867 284.672 955.733333 389.802667 955.733333 511.146667c0 122.7264-43.8784 228.437333-130.440533 314.999466C739.293867 912.128 633.890133 955.733333 512 955.733333z m0-853.333333c-112.503467 0-210.090667 40.533333-290.013867 120.439467C142.626133 302.216533 102.4 399.496533 102.4 512c0 112.520533 40.2432 210.1248 119.620267 290.065067C301.8752 881.3568 399.4624 921.6 512 921.6c112.520533 0 209.800533-40.226133 289.160533-119.586133C881.083733 722.090667 921.6 624.520533 921.6 512c0-112.776533-40.4992-209.7664-120.405333-289.109333C721.7664 142.916267 624.4864 102.4 512 102.4z" p-id="31129"></path><path d="M342.186667 716.8a34.133333 34.133333 0 0 1-24.132267-58.2656L464.571733 512l-146.517333-146.5344a34.133333 34.133333 0 1 1 48.264533-48.264533l146.653867 146.653866 145.681067-144.213333a34.133333 34.133333 0 0 1 48.0256 48.520533l-145.220267 143.752534 21.384533 20.974933 122.333867 123.1872a34.133333 34.133333 0 1 1-48.4352 48.093867l-143.6672-144.093867-146.756267 146.756267c-6.656 6.638933-15.394133 9.966933-24.132266 9.966933z" p-id="31130"></path></svg>
									<!-- <svg t="1737167893538" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="32744" width="25" height="25"><path d="M813.696 210.304c166.613333 166.613333 166.613333 436.778667 0 603.392-166.613333 166.613333-436.778667 166.613333-603.392 0-166.613333-166.613333-166.613333-436.778667 0-603.392 166.613333-166.613333 436.778667-166.613333 603.392 0z m-60.330667 60.330667A341.333333 341.333333 0 1 0 270.634667 753.365333 341.333333 341.333333 0 0 0 753.365333 270.634667z m-60.330666 120.704L572.330667 512l120.704 120.661333-60.373334 60.373334L512 572.330667l-120.661333 120.704-60.373334-60.373334L451.669333 512 330.965333 391.338667l60.373334-60.373334L512 451.669333l120.661333-120.704 60.373334 60.373334z" fill="#919499" p-id="32745"></path></svg> -->
								</p>
							</div>
						</div>
					</div>
					<div class="right_Navigation">
						<input type="text" placeholder="Product Keywords..." v-model="keyword">
						<b class="icon-a-uspp-sousuozhuanhuan"></b>
					</div>
				</div>

				<div class="label">
					<div class="color_label" v-for="(item,index) in selectedOptions">{{item}} <span @click="deleteSelectData(index,item)">×</span></div>
					<div v-if="selectedOptions.length > 0" style="display: inline-block;text-decoration: underline;cursor: pointer" @click="deleteSelectedOptions">Reset Filters</div>
				</div>
				<div class="bottomNavigation">
					<div class="Navigation_grid">
						<div class="grid_div" v-for="(item,index) in hatsData.records" @click.stop="openMaskDetail(item.productRouting)">
							<img :src="JSON.parse(item.productParamList[item.colorIndex].imgJson)[0].url" :alt="JSON.parse(item.imgJson)[0].alt" :title="JSON.parse(item.imgJson)[0].alt" style="margin-top: 1em">
							<div style="margin-top: 15px" class="bottom">
								<div class="color_div">
									<div class="color_grid" >
										<p class="colorYuan" :class="{ active:eindex === item.colorIndex }" v-for="(eitem,eindex) in (item.productParamList).slice(0,7)" :style="{backgroundColor:eitem.colorCode}" :key="eindex" @click.stop="changPlaces(item,eindex)"></p>
										<p v-if="item.productParamList.length > 7">+ {{item.productParamList.length - 7}}</p>
									</div>
								</div>
								<p class="title">{{item.name}}</p>
								<div class="color_bottom">
									<div class="number">AS low as<CCYRate :price="item.lowestPrice"></CCYRate>ea</div>
									<span class="shopNowBtn">{{ langSemi.shopNow +' >' }}</span>
								</div>
								<div class="color_number mb">
									{{ item.productSku }}
								</div>
								<div class="color_div mb">
									<div class="color_grid" >
										<p class="colorYuan" :class="{ active:eindex === item.colorIndex }" v-for="(eitem,eindex) in (item.productParamList).slice(0,7)" :style="{backgroundColor:eitem.colorCode}" :key="eindex" @click.stop="changPlaces(item,eindex)"></p>
										<p v-if="item.productParamList.length > 7">+ {{item.productParamList.length - 7}}</p>
									</div>
								</div>

							</div>
							<div class="color_number">
								{{ item.productSku }}
							</div>
							<div v-if="item.cornerLabel?.id">
								<p class="hot" v-if="item.cornerLabel.value == 'Hot'">Hot</p>
								<p class="hot" v-else style="background: linear-gradient(87deg, #FF6C00, #FF9833);">Popular</p>
							</div>
						</div>
					</div>
				</div>
				<div class="left_collapse" v-if="collapse">
					<div style="position: relative;height: 100%;width: 100%">
						<div class="collapse">
							<p class="title">Filters <span>({{hatsData.total}} Results)</span></p>
							<div class="label">
								<div class="label_div" v-for="(item,index) in selectedOptions">{{item}} <span @click="deleteSelectData(index,item)">×</span></div>
								<div v-if="selectedOptions.length > 0" style="cursor: pointer;vertical-align: middle;line-height: 32px;font-family: Roboto, Roboto;font-weight: 400;font-size: 14px;color: #333333;" @click="deleteSelectedOptions">
									<svg style="vertical-align: middle" t="1735803487608" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="28715" width="13" height="13"><path d="M202.666667 256h-42.666667a32 32 0 0 1 0-64h704a32 32 0 0 1 0 64H266.666667v565.333333a53.333333 53.333333 0 0 0 53.333333 53.333334h384a53.333333 53.333333 0 0 0 53.333333-53.333334V352a32 32 0 0 1 64 0v469.333333c0 64.8-52.533333 117.333333-117.333333 117.333334H320c-64.8 0-117.333333-52.533333-117.333333-117.333334V256z m224-106.666667a32 32 0 0 1 0-64h170.666666a32 32 0 0 1 0 64H426.666667z m-32 288a32 32 0 0 1 64 0v256a32 32 0 0 1-64 0V437.333333z m170.666666 0a32 32 0 0 1 64 0v256a32 32 0 0 1-64 0V437.333333z" fill="#000000" p-id="28716"></path></svg>
									clear All
								</div>
							</div>
							<div
								v-for="(item, index) in tableList"
								:key="index"
								class="accordion-item"
								:class="{ 'active': itemIndex === index }"
							>
								<p class="title" @click.stop="openSelectDiv(index)">{{item.nameEn}}</p>
								<span class="arrow" @click="toggleAccordion(index)" v-if="itemIndex === index"><svg t="1735559349252" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="25721" width="40" height="40"><path d="M484.778667 305.194667a38.4 38.4 0 0 1 54.314666 0l307.712 307.712a38.4 38.4 0 1 1-54.272 54.314666L511.914667 386.56l-280.576 280.618667a38.4 38.4 0 1 1-54.272-54.314667l307.712-307.712z" fill="#333333" p-id="25722"></path></svg></span>
								<span class="arrow" @click="toggleAccordion(index)" v-else><svg t="1735559387253" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="25887" width="40" height="40"><path d="M484.764309 718.806576a38.4 38.4 0 0 0 54.3058 0l307.732872-307.732871a38.4 38.4 0 1 0-54.305801-54.305801L511.887039 637.378045 231.337238 356.767904a38.4 38.4 0 0 0-54.305801 54.305801l307.732872 307.732871z" fill="#333333" p-id="25888"></path></svg></span>
								<div v-if="itemIndex === index" class="selectDiv">
									<div style="text-align: left">
										<div v-for="(eitem,eindex) in item.attributeList" :key="eitem.id" @change="selectList(eitem)">
											<div v-if="item.nameEn == 'PRICE RANGE'">
												<div style="margin-top: 50px">
													<v-range-slider class="custom-slider" @change="changeRange" v-model="priceRange" thumb-label :min="eitem.valueName" :max="eitem.remark" hide-details :step="0.01"></v-range-slider>
													<div style="display: flex">
														<div style="flex: 1">
															<CCYRate :price="eitem.valueName"></CCYRate>
														</div>
														<div style="flex: 1;text-align: end">
															<CCYRate :price="eitem.remark"></CCYRate>
														</div>
													</div>
												</div>
											</div>
											<label v-else>
												<div class="checkbox__wrap">
													<input type="checkbox" :id="eitem.id" v-model="selectedOptions" :value="eitem.valueName"  />
													<span></span>
												</div>
												<span v-if="item.nameEn == 'COLOR'">
													<img :src="eitem.imgUrl" alt="" style="width: 15px;height: 15px;vertical-align: sub"> {{ eitem.valueName }}
												</span>
												<span v-else>{{ eitem.valueName }}</span>
											</label>
										</div>
									</div>
								</div>
							</div>
						</div>
						<p style="position: absolute;top: 0.5%;right: 15%" @click="collapse = false">
							<svg t="1735559534672" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="27693" width="25" height="25"><path d="M240.512 180.181333l271.530667 271.488 271.530666-271.488a42.666667 42.666667 0 0 1 56.32-3.541333l4.010667 3.541333a42.666667 42.666667 0 0 1 0 60.330667l-271.530667 271.530667 271.530667 271.530666a42.666667 42.666667 0 0 1-56.32 63.872l-4.010667-3.541333-271.530666-271.530667-271.530667 271.530667-4.010667 3.541333a42.666667 42.666667 0 0 1-56.32-63.872l271.488-271.530666-271.488-271.530667a42.666667 42.666667 0 0 1 60.330667-60.330667z" fill="#000000" p-id="27694"></path></svg>
						</p>
					</div>
				</div>
				<div class="viewMore">
					<button v-if="o.button?.value" :primary="!o.button.outline" :outline="o.button.outline"
						:style="{ ...modal.btnStyle, ...o.button.style, ...o.btnStyle }" :title="o.button.alt"
						@click="setModalType(o.button, modal.outer, 'button', o.button)">
						<EditDiv v-show="o.button.value" tagName="label" v-model:content="o.button.value" />
						<b :class="o.button.icon" v-show="o.button.icon"></b>
					</button>
				</div>
			</div>
			</template>

		</v-main>
	</v-app>
</template>

<script>
import {addCollection, getAppLabelAttributeList, getNewLabelAttributeList,getProductList,deleteConllectionByUserId} from "@/api/web";
import { debounce } from "@/utils/utils";
export default {
	components: {},

	props: ["cateId", "data", "halfCateDTO", "isStockPage", "name"],
	data() {
		return {
			selected: '',
			styleDiv:false,
			materialDiv:false,
			priceDiv:false,
			colorDiv:false,
			sortbyDiv:false,
			DeliveryDiv:false,
			colorIndex:0,
			activeIndex: null,
			pageSize:16,
			page:1,
			selectedOptions: [],
			message:'',
			customKeyword:'',
			tableList:[],
			selectData:[],
			hatsData:{},
			attributeValueIds:[],
			keyword:'',
			collapse:false,
			itemIndex: null,
			priceRange:[0, 200],
			productName: '',

			parentCateId: '',
			cateGoryData: [],
			priceData: {},
			modal:{
				...this.data
			}
		};
	},
	computed: {
		// canvas() {
		// 	return canvas
		// },
		isDefault() {
			return this.$store.state.design.isDefault;
		},
		langSemi() {
			return this.$store.getters.lang?.semiCustom;
		},
		getSwitchProBtnText() {
			if (this.text == 1) {
				return this.langDesign.chooseProText;
			} else {
				return this.langDesign.changeProText;
			}
		},
		userId() {
			return this.$store.state.userInfo?.id;
		},
		isLogin() {
			return this.$store.getters.isLogin;
		},
		isMobile() {
			return this.$store.state.device === "mb";
		},
	},
	watch:{
		selectData(newItems, oldItems) {
			let arr = []
			if (newItems.length > 0) {
				newItems.forEach(item => {
					this.selectedOptions.forEach(eitem => {
						if (item.valueName == eitem) {
							arr.push(item.id)
						}
					})
				})
			}
			this.attributeValueIds = arr
			this.getProduct().then(res => {
				res.data.records.forEach((item) => {
					item.colorIndex = 0
				})
				this.hatsData = res.data
			})
		},
		keyword(newItems, oldItems) {
			console.log(newItems);
			this.getProduct().then(res => {
				res.data.records.forEach((item) => {
					item.colorIndex = 0
				})
				this.hatsData = res.data
			})
		},
	},
	created() {
		const parentCateId = this.$route.query.parentCateId
		if(parentCateId) this.parentCateId = parentCateId
	},
	methods:{
		setModalType(target, targetArray, clickType, event, other) {
			if (!process.env.isManage && clickType == 'card') {
				if (this.modal.linkArea == 'card' && target.localName != 'video' && target.localName != 'button' && target.parentElement?.localName != 'button') this.$setModal(this, null, null, '', event);
			} else if (!process.env.isManage && clickType == 'img' && this.modal.linkArea != 'img') return;
			else if (clickType == 'button' && !event.url && !(event.method && Object.keys(event.method).length)) this.$setModal(this, target, targetArray, clickType, other);
			else this.$setModal(this, target, targetArray, clickType, event, other);
		},
		openSelectDiv(index){
			this.activeIndex = this.activeIndex === index ? null : index;
		},
		showSelectDiv(index){
			return this.activeIndex === index;
		},
		getNewLabelAttributeList(tagData = "") {
			return new Promise((resolve) => {
				getNewLabelAttributeList(
					{
						categoryId: this.parentCateId || this.cateId,
						childCategoryId: this.customKeyword ? "" : this.parentCateId ? this.cateId : null,
						isLabel: 1,
						attributeValueIds:this.attributeValueIds,
						keyWord: this.keyword,
					},
					tagData
				).then((res) => {
					resolve(res);
				});
			});
		},
		getAppLabelAttributeList(tagData = "") {
			return new Promise((resolve) => {
				getAppLabelAttributeList(
					{
						categoryId: this.parentCateId || this.cateId,
						childCategoryId: this.customKeyword ? "" : this.parentCateId ? this.cateId : null,
						isLabel: 1,
						attributeValueIds:this.attributeValueIds,
						keyWord: this.keyword,
					},
					tagData
				).then((res) => {
					resolve(res);
				});
			});
		},
		getProduct(customPage) {
			return new Promise((resolve) => {
				getProductList({
					categoryId: this.parentCateId || this.cateId,
					childCategoryId: this.customKeyword ? "" : this.parentCateId ? this.cateId : null,
					page: this.page,
					pageSize: this.pageSize,
					keyWord: this.keyword,
					attributeValueIds:this.attributeValueIds.length>0?this.attributeValueIds.join(','):'',
					userId: this.isLogin ? this.userId : null,
					sorts: this.sorts,
					priceStart: this.priceRange[0],
					priceEnd: this.priceRange[1],
					productType: this.isStockPage,
				})
					.then((res) => {
						resolve(res);
					})
					.catch((error) => {
						if (error.name === "AbortError") {
							console.log("Request canceled:", error.message);
						}
					});
			});
		},
		selectList(item){
			this.selectData.push(item)
			this.selectData = this.selectData.filter((obj, index, self) =>
				index === self.findIndex((t) => t.id === obj.id))

		},
		deleteSelectData(index,item) {
			this.selectedOptions.splice(index,1)
			this.attributeValueIds.splice(index,1)
			this.getProduct().then(res => {
				res.data.records.forEach((item) => {
					item.colorIndex = 0
				})
				this.hatsData = res.data
			})
		},
		changPlaces(item,index) {
			item.colorIndex = index
		},
		toggleAccordion(index) {
			this.itemIndex = this.itemIndex === index ? null : index;
		},
		openMaskDetail(url) {
			if(this.isDialog){
				this.$emit("toDetail",url);
			}else{
                if(location.pathname.indexOf('best-sellers')>-1||location.pathname.indexOf('personalized-keychains')>-1||location.pathname.indexOf('fast-shipping-custom-keychains')>-1){
                    window.open(url)
                }else{
					let data = {
						modal: "modalQuoteHalfDetail",
						name: url,
					};
					this.$store.commit("setMask", data);
				}
			}
		},
		changeRange(){
			this.getProduct().then(res => {
				res.data.records.forEach((item) => {
					item.colorIndex = 0
				})
				this.hatsData = res.data
			})
		},
		deleteSelectedOptions(){
			this.selectedOptions = []
			this.attributeValueIds = []
			this.getProduct().then(res => {
				res.data.records.forEach((item) => {
					item.colorIndex = 0
				})
				this.hatsData = res.data
			})
		},
		handleCollection(data){
			console.log(data);
			this.hatsData.records.forEach((item,index) => {
				if (item.id == data.productId) {
					data.type == "collection"?item.isCollection = true:item.isCollection = false
				}
			})
		},
		clickViewMore(){
			this.pageSize += 16
			this.getProduct().then(res => {
				res.data.records.forEach((item) => {
					item.colorIndex = 0
				})
				this.hatsData = res.data
			})
		},
	},
    async mounted(){
		if (process.env.isManage) {
			this.isManage = true;
			return false;
		}
		this.$Bus.$on("collectionProduct", this.handleCollection);
		const { data:cates } = await this.getNewLabelAttributeList()
		const { data:tags } = await this.getAppLabelAttributeList()
		this.productName = cates.name;
		if(cates.childList && cates.childList.length) {
			const cateObj = {
				nameEn: 'category',
				attributeList: cates.childList.map(x => {
					return { id: x.id, valueName: x.name, remark: x.remark }
				})
			}
			this.tableList.push(cateObj)
		}
		const priceRange = tags.find(x => x.nameEn == 'PRICE RANGE')
		if(priceRange) {
			const tagList = priceRange?.attributeList
			const tagObj = {
				nameEn: 'price',
				attributeList: tagList.map(x => {
					return { id: x.id, valueName: x.valueName, remark: x.remark }
				})
			}
			this.tableList.push(tagObj)
		}


		this.getProduct().then(res => {
			res.data.records.forEach((item) => {
				item.colorIndex = 0
			})
			this.hatsData = res.data
		})
	},
}
</script>

<style scoped lang="scss">
	.main {
		margin: 0 auto;
		background-color: #F6F6F6;
		&.modal-box{
			padding-block: 1em;
		}
	}
	.pcTitle {
		@include respond-to(mb) {
			display: none;
		}
		.title {
			text-align: center;
			font-family: Calibri !important;
			font-weight: bold;
			font-size: 1.7em !important;
			color: #333333;
			text-transform: none;
		}
		.subTitle {
			font-family: Calibri;
			font-weight: 400;
			font-size: 16px;
			color: #333333;
			margin: 15px 0;
			text-align: center;
		}
	}
	.mbTitle {
		display: none;
		@include respond-to(mb) {
			display: block;
			.title {
				font-family: Roboto !important;
				font-weight: bold;
				font-size: 18px !important;
				color: #333333;
				text-align: center;
			}
			.subTitle {
				font-family: Arial;
				font-weight: 400;
				font-size: 12px;
				color: #333333;
				text-align: center;
				margin: 10px 0;
			}
			.mb_search {
				background-color: #fff;
				padding: 10px;
				.right_Navigation {
					display: inline-block;
					width: 70%;
					vertical-align: middle;
					text-align: left;
					position: relative;
					input {
						-webkit-appearance: none;
						background: #F1F2F6;
						background-image: none;
						border: 1px solid #dcdfe6;
						box-sizing: border-box;
						color: #000;
						display: inline-block;
						vertical-align: middle;
						font-size: inherit;
						height: 36px;
						outline: none;
						padding: 0 15px;
						transition: border-color .2s cubic-bezier(.645,.045,.355,1);
						width: 85%;
						font-size: 14px;
					}
					b {
						display: inline-block;
						position: absolute;
						font-size: 14px;
						right: 55px;
						top: 50%;
						transform: translateY(-50%);
					}
				}
				.left_Navigation {
					width: 25%;
					display: inline-block;
					vertical-align: middle;
					text-align: left;
					padding-left: 11%;
					p {
						font-family: Arial;
						font-weight: 400;
						font-size: 12px;
						color: #333333;
						text-align: left;
						display: inline-block;
						margin: 0;
					}
				}
			}
		}
	}
	.topNavigation {
		@include respond-to(mb) {
			display: none;
		}
		margin-top: 14px;
		background-color: #EBEBEB;
		line-height: 35px;
		font-size: 0;
		.left_Navigation {
			display: inline-block;
			width: 74%;
			vertical-align: middle;
			.left_grid {
				display: grid;
				grid-template-columns: repeat(7, 1fr);
				font-family: Calibri, Calibri;
				font-weight: 400;
				font-size: 18px;
				color: #333333;
				text-align: center;
				font-style: normal;
				text-transform: none;
				div {
					.subtitle {
						font-family: Calibri, Calibri;
						font-weight: normal;
						font-size: 14px;
						color: #A7A7A7;
						line-height: 16px;
						font-style: italic;
						text-transform: none;
					}
					span {
						font-size: 10px;
					}
				}
				.active {
					color:#D24600 !important;
				}
				.grid_div {
					cursor: pointer;
					position: relative;
					>div{
						text-transform: uppercase;
					}
					.selectDiv {
						position: absolute;
						background: #FFFFFF;
						box-shadow: 2px 6px 6px 0px rgba(0,0,0,0.15);
						border-radius: 0px 0px 0px 0px;
						border: 1px solid #DBDBDB;
						padding: 20px;
						border-radius: 6px;
						left: 35px;
						width: 150%;
						top: 50px;
						z-index: 99;
						height: 300px;
						&:after {
							position: absolute;
							content: '';
							display: block;
							width: 0;
							height: 0;
							border-color: transparent;
							border-style: solid;
							top: -12px;
							left: 35px;
							margin-left: -6px;
							border-top-width: 0;
							border-bottom-color: #fff;
							border-width: 6px;
							z-index: 100;
						}
						input[type="checkbox"]:checked {
							appearance: none;
						}
						input[type="checkbox"]:checked+span {
							border:none;
							background: url("https://static-oss.gs-souvenir.com/web/quoteManage/20241219/gouxuan_1_2055hiMG3i.png") no-repeat;
						}
						label {
							font-family: Calibri, Calibri;
							font-weight: 400;
							font-size: 14px;
							color: #666666;
							text-align: left;
							font-style: normal;
							text-transform: none;
							vertical-align: middle;
							span {
								margin-left: 5px;
								vertical-align: middle;
								font-family: Calibri, Calibri;
								font-weight: 400;
								font-size: 14px;
								color: #666666;
								i {
									display: inline-block;
									vertical-align: middle;
									border-radius: 50%;
									width: 15px;
									height: 15px;
								}
							}
						}
						.checkbox__wrap {
							display: inline-block;
							position: relative;
							width: 19px;
							height: 13px;
							vertical-align: middle;
						}
						.checkbox__wrap input {
							opacity: 0;
						}
						.checkbox__wrap span {
							position: absolute;
							inset: 0;
							border-radius: 2px 2px 2px 2px;
							border: 1px solid #666666;
						}
						.checkbox__wrap input:checked + span {

						}
					}
					p {
						position: absolute;
						font-size: 25px !important;
						right: 0;
						font-size: 22px;
						right: -55px;
						top: 50px;
						color: #333;
						z-index: 99;
					}
				}
			}

		}
		.right_Navigation {
			display: inline-block;
			width: 25%;
			vertical-align: middle;
			text-align: right;
			position: relative;
			input {
				-webkit-appearance: none;
				background-color: #fff;
				background-image: none;
				border-radius: 4px;
				border: 1px solid #dcdfe6;
				box-sizing: border-box;
				color: #000;
				display: inline-block;
				vertical-align: middle;
				font-size: inherit;
				height: 36px;
				outline: none;
				padding: 0 15px;
				transition: border-color .2s cubic-bezier(.645,.045,.355,1);
				width: 85%;
				border-radius: 20px;
				font-size: 14px;
			}
			b {
				display: inline-block;
				position: absolute;
				font-size: 14px;
				right: 20px;
				top: 50%;
				transform: translateY(-50%);
			}
		}
	}
	.bottomNavigation {
		.Navigation_grid {
			@include respond-to(mb) {
				grid-template-columns: repeat(2, 1fr);
			}
			display: grid;
			grid-template-columns: repeat(5, 1fr);
			gap: 15px;
			margin-top: 10px;
			.grid_div {
				@include respond-to(mb) {
					background: #FFFFFF;
					border-radius: 2px;
					border: 1px solid #E9E9E9;
					font-size: 0;
				}
				img	 {
					@include respond-to(mb) {
						display: inline-block;
						vertical-align: middle;
						width: 100%;
					}
				}
				.bottom {
					display: flex;
					flex-direction: column;
					justify-content: space-between;
					flex: 1;
					@include respond-to(mb) {
						// display: inline-block;
						vertical-align: middle;
						margin-left: 3%;
					}
				}
				.color_number {
					width: 28%;
					position: absolute;
					vertical-align: middle;
					display: inline-block;
					font-family: Poppins;
					font-weight: 400;
					font-size: 14px;
					color: #666666;
					top: 8px;
					@include respond-to(mb) {
						display: none;
					}
				}
				position: relative;
				padding: 10px;
				border-radius: 10px;
				display: flex;
				flex-direction: column;
				position: relative;
				background-color: #fff;
				&:hover {
					box-shadow: 0px 8px 13px 0px rgba(0,0,0,0.09);
					.color_bottom{
						.shopNowBtn{
							color: #FF764A;
						}
					}
				}
				.color_div {
					@include respond-to(mb) {
						display: none;
					}
					display:inline-block ;
					vertical-align: middle;
					.color_grid {
						display: grid;
						grid-template-columns: repeat(8, 1fr);
						gap: 0px;
						@include respond-to(mb) {
							display: flex;
							flex-wrap: wrap;
						}
						p {
							margin: 0;
							vertical-align: middle;
						}
						.colorYuan {
							margin: 0;
							margin: 6px;
							border-radius: 50%;
							border: 1px solid #A30C00;
							width: 14px;
							height: 14px;
							background-color: #DFA4FB;
							position: relative;
							cursor: pointer;
						}
						.active::before {
							position: absolute;
							content: '';
							width: 20px;
							height: 20px;
							border-radius: 50%;
							border: 1px solid #A30C00;
							left: -4px;
							top: -4px;
						}
					}
				}
				.mb {
					display: none;
					@include respond-to(mb) {
						display: block !important;
					}
				}
				.title {
					font-family: Calibri !important;
					font-weight: bold;
					font-size: 16px !important;
					color: #333333;
					text-align: left;
					margin: 5px 0 0;
					@include respond-to(mb) {
						font-family: Arial;
						font-weight: bold;
						font-size: 13px !important;
						color: #333333;
						line-height: 1.4 !important;
						margin: 0;
					}
				}

				.color_bottom {
					display: flex;
					flex-direction: column;
					@include respond-to(mb) {
						margin-bottom: 5px;
					}
					// margin-bottom: 15px;
					.number {
						@include respond-to(mb) {
							margin-bottom: 5px;
						}
						margin-top: 8px;
						font-family: Calibri;
						font-weight: bold;
						font-size: 14px;
						display: inline-block;
						vertical-align: middle;
						label{
							color:#FF764A;
						}
					}
					.shopNowBtn{
						font-family: Calibri !important;
						cursor: pointer;
						width: fit-content;
						margin-top: 14px;
						font-weight: bold;
						transition: all .3s ease-in-out;
					}

				}
				.collect {
					width: 25px;
					height: 25px;
					cursor: pointer;
					position: absolute;
					top: 10px;
					left: 10px;
					svg {
						display: inline-block;
						width: 25px;
						height: 25px;
					}
				}
				.hot {
					@include respond-to(mb) {
						left: 25% !important;
					}
					position: absolute;
					text-align: center;
					background: linear-gradient(87deg, #FC1717, #FE4C4C);
					border-radius: 0px 0px 0px 15px;
					height: 22px;
					line-height: 22px;
					font-family: Calibri;
					font-weight: 400;
					font-size: 14px;
					color: #FFFFFF;
					width: 70px;
					right: 0;
					top: 0;
				}
			}
		}
	}
	.label {
		.color_label {
			display: inline-block;
			margin-right: 10px;
			padding: 0 20px;
			height: 30px;
			background: #FFFFFF;
			border-radius: 15px;
			border: 1px solid #D24600;
			text-align: center;
			font-family: Calibri;
			font-weight: 400;
			font-size: 14px;
			color: #D24600;
			line-height: 30px;
			cursor: pointer;
		}
	}
	.left_collapse {
		display: none;
		@include respond-to(mb) {
			display: block;
			position: absolute;
			width: 100%;
			height: 100%;
			left: 0;
			top: 0;
			z-index: 10;
			background: rgba(6, 6, 6, 0.5);
			.collapse {
				position: absolute;
				top: 0;
				left: 0;
				width: 93%;
				height: 90%;
				background-color: #fff;
				padding: 25px;
				.title {
					font-family: Poppins, Poppins !important;
					font-weight: bold;
					font-size: 24px !important;
					color: #000000;
					text-align: left;
					font-style: normal;
					text-transform: none;
					span {
						font-family: Poppins, Poppins;
						font-weight: normal;
						font-size: 14px;
						color: #666666;
						text-transform: none;
					}
				}
				.label {
					.label_div {
						padding: 10px 20px;
						background: rgba(210,70,0,0.12);
						border-radius: 2px 2px 2px 2px;
						font-family: Roboto, Roboto;
						font-weight: 400;
						font-size: 12px;
						color: #D24600;
						display: inline-block;
						margin-left: 10px;
						margin-bottom: 10px;
					}
				}
				.accordion-item {
					border-bottom: 2px solid #f0f0f0;
					padding: 10px;
					margin-bottom: 5px;
					cursor: pointer;
					position: relative;
					.title {
						font-family: Calibri !important;
						font-weight: bold;
						font-size: 18px !important;
						color: #333333;
						text-align: left;
						font-style: normal;
						text-transform: none;
					}
					.active {
						background-color: lightblue;
					}
					p {
						margin: 15px 0px;
					}
					.selectDiv {
						background: #FFFFFF;
						border-radius: 0px 0px 0px 0px;
						border-radius: 6px;
						width: 100%;
						z-index: 99;
						input[type="checkbox"]:checked {
							appearance: none;
						}
						input[type="checkbox"]:checked+span {
							border:none;
							background: url("https://static-oss.gs-souvenir.com/web/quoteManage/20241219/gouxuan_1_2055hiMG3i.png") no-repeat;
						}
						label {
							font-family: Calibri, Calibri;
							font-weight: 400;
							font-size: 14px;
							color: #666666;
							text-align: left;
							font-style: normal;
							text-transform: none;
							vertical-align: middle;
							margin-bottom: 15px;
							display: inline-block;
							span {
								font-family: Arial, Arial;
								font-weight: 400;
								font-size: 14px;
								color: #666666;
								text-align: left;
								font-style: normal;
								text-transform: none;
								margin-left: 5px;
								i {
									display: inline-block;
									vertical-align: middle;
									border-radius: 50%;
									width: 15px;
									height: 15px;
								}
							}
						}
						.checkbox__wrap {
							display: inline-block;
							position: relative;
							width: 19px;
							height: 13px;
							vertical-align: middle;
						}
						.checkbox__wrap input {
							opacity: 0;
						}
						.checkbox__wrap span {
							position: absolute;
							inset: 0;
							border-radius: 2px 2px 2px 2px;
							border: 1px solid #666666;
						}
						.checkbox__wrap input:checked + span {

						}
					}
				}
				.arrow {
					position: absolute;
					top: 15px;
					right: 10px;
					font-size: 39px;
				}
			}
		}
	}
	.viewMore {
		text-align: center;
		margin: 40px auto;
		@include respond-to(mb) {
			margin: 20px auto;
		}
		div {
			background-color: rgb(255, 255, 255);
			border-radius: 28px;
			color: var(--text-primary);
			border: 1px solid var(--text-primary);
			margin: auto;
			width: fit-content;
			cursor: pointer;
			padding: 0.3rem 1.7rem;
			display: flex;
			justify-content: center;
			align-items: center;
			font-size: 16px;
			font-weight: normal;
			@include respond-to(mb) {
				// background: #D24600;
				font-size: 12px;
			}
		}
	}
	.custom-scrollbar::-webkit-scrollbar {
		/*滚动条整体样式*/
		width: 5px;

		/*高宽分别对应横竖滚动条的尺寸*/
		height: 1px;
	}

	.custom-scrollbar.csu1::-webkit-scrollbar {
		height: 50px;
	}

	.custom-scrollbar::-webkit-scrollbar-thumb {
		/*滚动条里面小方块*/
		border-radius: 10px;
		background: #d3d3d3;
	}

	.custom-scrollbar::-webkit-scrollbar-track {
		/*滚动条里面轨道*/
		border-radius: 10px;
	}
</style>