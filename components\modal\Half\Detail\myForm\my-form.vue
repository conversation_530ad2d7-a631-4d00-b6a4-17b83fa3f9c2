<!--
 * @Descripttion:
 * @version:
 * @Author: jxwd
 * @Date: 2025-02-12 09:45:37
 * @LastEditors: jxwd
 * @LastEditTime: 2025-02-18 18:56:20
-->
<template>
	<div class="my-form">
		<slot></slot>
	</div>
</template>

<script>
export default {
	name: "my-form",
	components: {},
	data() {
		return {
			allSonCompontents: [],
		};
	},
	watch: {},
	computed: {},
	methods: {
		reset() {
			this.allSonCompontents.forEach((child) => {
				child.reset();
			});
		},
		validate() {
			let result = [];
			this.allSonCompontents.forEach((child) => {
				if (child.needVaildate) {
					result.push(child.validate());
				}
			});
			return new Promise((resolve) => {
				Promise.all(result).then((res) => {
					resolve(res.every((item) => item));
				});
			});
		},
	},
	created() { },
	mounted() {
		//收集子组件的引用
		if (this.$children.length > 0) {
			let childArr = ["myInput", "mySelect"];
			this.$children.forEach((child) => {
				if (childArr.includes(child.$options._componentTag)) {
					this.allSonCompontents.push(child);
				}
			});
		}
	},
};
</script>

<style scoped lang="scss">
.my-form {}
</style>
