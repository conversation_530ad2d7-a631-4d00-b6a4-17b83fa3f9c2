<template>
	<div class="wrap">
		<div class="content" id="pdfDom">
			<div class="con content1">
				<table class="printTable noBorder">
					<tr>
						<td colspan="3">
							{{ currentTime }}
							<span style="margin-left: 13px;">Inquiry ld: No{{ detailInfo.id }}</span>
						</td>
					</tr>
					<div class="mb-5"></div>
					<tr class="bgf2">
						<td width="33.3%" height="68px">
							<img style="width: 100px;" :src="detailInfo.logPath" alt="">
						</td>
						<td width="33.3%" height="68px">Phone:{{ detailInfo.retailerPhone }}</td>
						<td width="33.3%" height="68px">Email:{{ detailInfo.retailerEmail }}</td>
					</tr>
				</table>
				<table class="printTable">
					<tr>
						<th width="25%" style="border: 0"></th>
						<th width="25%" style="border: 0"></th>
						<th width="25%" style="border: 0"></th>
						<th width="25%" style="border: 0"></th>
					</tr>
					<tr>
						<td class="bgf2">From:</td>
						<td>{{ detailInfo.manageSalesmanName }}</td>
						<td class="bgf2">To:</td>
						<td>{{ detailInfo.userInfo.firstName }} {{ detailInfo.userInfo.lastName }}</td>
					</tr>
					<tr>
						<td colspan="2">{{ detailInfo.manageSalesmanEmail }}</td>
						<td class="bgf2">Attn:</td>
						<td>{{ detailInfo.userInfo.firstName }} {{ detailInfo.userInfo.lastName }}</td>
					</tr>
				</table>
				<div class="small-title">
					Personal Information
				</div>
				<table class="printTable">
					<tr>
						<th width="25%" style="border: 0"></th>
						<th width="25%" style="border: 0"></th>
						<th width="25%" style="border: 0"></th>
						<th width="25%" style="border: 0"></th>
					</tr>
					<tr>
						<td class="bgf2">Name:</td>
						<td>{{ detailInfo.userInfo.firstName }} {{ detailInfo.userInfo.lastName }}</td>
						<td class="bgf2">Email:</td>
						<td>{{ detailInfo.userInfo.email }}</td>
					</tr>
					<tr>
						<td class="bgf2">Phone:</td>
						<td>{{ detailInfo.userInfo.phone }}</td>
						<td class="bgf2">Country:</td>
						<td>{{ detailInfo.userInfo.country }}</td>
					</tr>
				</table>
				<table class="printTable">
					<tr>
						<th width="12%" style="border: 0"></th>
						<th width="13%" style="border: 0"></th>
						<th width="60%" style="border: 0"></th>
						<th width="15%" style="border: 0"></th>
					</tr>
					<tr class="bgf2">
						<td>Item:</td>
						<td>Quantity</td>
						<td>Description:</td>
						<td>Subject title</td>
					</tr>
					<tr class="vtop">
						<td>{{ detailInfo.productsName }}</td>
						<td>{{ detailInfo.quantity }}</td>
						<td>
							<div class="d-flex align-center mb-2" v-for="(item,key,index) in detailInfo.params">
								<div class="label" style="width: 180px;flex-shrink: 0">
									{{ key }}:
								</div>
								<template v-if="key==='Artwork'">
									<div class="des">
										<template v-for="(citem,index) in item">
											<a :href="citem.img" :key="index" target="_blank"
												 style="word-break: break-all">
												{{ citem.img }}
											</a>
											<br>
										</template>
									</div>
								</template>
								<template v-else>
									<div class="des">
										<template v-for="(citem,index) in item">
										<span :key="index">
											{{ citem.paramName }}
										</span>
											<br>
										</template>
									</div>
								</template>
							</div>
						</td>
						<td>{{ detailInfo.subject }}</td>
					</tr>
				</table>
			</div>
		</div>
	</div>
</template>
<script>
import {getById} from "@/api/manage/inquiry";

export default {
	data() {
		return {
			currentTime: '',
			detailInfo: {
				userInfo:''
			},
			logoUrl: ''
		};
	},
	computed: {
		number() {
			return this.detailInfo.id + "_" + this.detailInfo.userInfo.firstName + this.detailInfo.userInfo.lastName + "_$" + this.detailInfo.totalPrice;
		}
	},
	beforeUpdate() {
		this.$nextTick(() => {
			document.title = "Inv" + "\xa0" + this.number;
		});
	},
	methods: {
		getInquiryDetailInfo() {
			getById({
				id: this.$route.query.id,
				// proId: this.$route.query.proId,
			}).then(res => {
				this.detailInfo = res.data;
				this.currentTime = new Date().toLocaleString();
				this.$nextTick(()=>{
					setTimeout(()=>{
						window.print()
					},200)
				})
			})
		},
	},
	created() {

	},
	mounted() {
		this.getInquiryDetailInfo()
	}
};
</script>
<style lang="scss" scoped>
.d-flex{
	display: flex;
}
.align-center{
	align-items: center;
}
.wrap {
	font-size: 15px;
}

.content {
	//width: 910px;
	width: 790px;
	margin: 20px auto;
}

.con {
	width: 100%;
	background: #FFFFFF;
	padding: 25px;
	font-size: 15px;
	color: #525659;
	page-break-after: always;

	.printTable {
		width: 100%;
		border-collapse: collapse;
		border-color: #D9D9D9;
		border-top: 1px solid #d9d9d9;
		border-left: 1px solid #d9d9d9;
		margin-bottom: 20px;

		tr th,
		tr td {
			border-right: 1px solid #d9d9d9;
			border-bottom: 1px solid #d9d9d9;
		}

		td {
			word-break: break-word;
			padding: 10px 15px;
			line-height: normal;
			vertical-align: middle;
		}

		.vtop,
		.vtop td {
			vertical-align: top;
		}

		.bgf2 {
			background-color: #f2f2f2;
		}
	}

	.printTable.noBorder {
		border: none;

		tr th,
		tr td {
			border: none;
		}
	}

	.small-title {
		height: 40px;
		line-height: 35px;
		text-align: center;
		background: #F2F2F2;
		border: 1px solid #D9D9D9;
		margin: 20px 0;
		font-weight: bold;
	}
}
</style>
