<template>
	<div class="emailLater mb-4" :class="stepData.styleName">
		<div class="later" @click="selectStep">
			<circleBox :active="isUpload"></circleBox>
			<span class="later-text" @click.stop>{{ lang.Uploadbyemaillater }}</span>
			<MySwitch :active="isUpload"></MySwitch>
			<span class="t2" v-show="isUpload">
				{{ lang.p26 }}
				<a @click.stop="" class="click_text" :href="`mailto:${userEmail}`"> {{ userEmail }}. </a>
			</span>
		</div>
	</div>
</template>

<script>
import MySwitch from "@/components/HalfDesign/customMedals/common/switch";
import circleBox from "@/components/HalfDesign/common/circleBox";

export default {
	name: 'emailLater',
	props: {
		stepData: {
			type: Object,
			default: {},
		},
	},
	components: { MySwitch, circleBox },
	data() {
		return {
		}
	},
	watch: {},
	computed: {
		lang() {
			return this.$store.getters.lang.quote || {};
		},
		userEmail() {
			return this.$store.state.proSystem.email;
		},
		isUpload() {
			return this.$store.state.halfDesign.medalsLaterUpload;
		},
	},
	methods: {
		selectStep(e) {
			const target = e.target;
			if (target.tagName.toLowerCase() === 'input') {
				if (target.type === 'checkbox' || target.type === 'radio') {
					this.$store.commit("halfDesign/setMedalsLaterUpload", target.checked);
				}
				const status = this.isUpload ? 1 : 0
				const item = this.stepData.productParamList.find((item) => item.isBlank === status);
				this.$emit("selectStep", {
					type: this.stepData.attributeFlag,
					data: item,
					parent: this.stepData,
					id: this.stepData.id,
					firstSelect: false,
				});
			} else {
				console.log('This is not an input element.');
			}
		},
		selectDefault() {
			if (this.stepData.productParamList.length) {
				//emailLater 是1 默认选0
				const item = this.stepData.productParamList.find((item) => item.isBlank === 0);
				this.$emit("selectStep", {
					type: this.stepData.attributeFlag,
					data: item,
					parent: this.stepData,
					id: this.stepData.id,
					firstSelect: true,
				});
			}
		},
		cancelEmailLater() {
			this.$store.commit("halfDesign/setMedalsLaterUpload", false);
			const status = this.isUpload ? 1 : 0
			const item = this.stepData.productParamList.find((item) => item.isBlank === status);
			this.$emit("selectStep", {
				type: this.stepData.attributeFlag,
				data: item,
				parent: this.stepData,
				id: this.stepData.id,
				firstSelect: false,
			});
		},
	},
	created() { },
	mounted() {
		this.$Bus.$on("selectDefaultEmailStep", this.selectDefault);
		this.$Bus.$on("cancelEmailLater", this.cancelEmailLater);
	},
	beforeDestroy() {
		this.$Bus.$off("selectDefaultEmailStep", this.selectDefault);
		this.$Bus.$off("cancelEmailLater", this.cancelEmailLater);
	}
}
</script>

<style scoped lang="scss">
.emailLater {
	.later {
		width: fit-content;
		cursor: pointer;
		display: flex;
		align-items: flex-start;
		gap: 8px;
		font-size: 16px;
		font-weight: bold;

		@include respond-to(mb) {
			font-size: 12px;
		}

		.later-text {
			flex-shrink: 0;
		}

		.click_text {
			color: $color-primary;
			text-decoration: underline;
		}
	}
}

.style2.emailLater {
	display: none;
}

.style3.emailLater {
	.later {
		width: fit-content;
		cursor: pointer;
		display: flex;
		align-items: center;
		flex-wrap: wrap;
		gap: 8px;
		row-gap: 4px;
		padding: 0;
		font-size: 16px;
		font-weight: bold;

		.t2 {
			width: 100%;
		}

		@include respond-to(mb) {
			font-size: 12px;
		}

		.later-text {
			flex-shrink: 0;
		}

		.click_text {
			color: $color-primary;
			text-decoration: underline;
		}
	}
}
</style>
