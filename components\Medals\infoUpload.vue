<template>
	<div class="infoUpload">
		<div class="infoTitle">
			<div><i class="el-icon-success"></i></div>
			<span>{{ lang.p21 }}</span>
		</div>
		<div class="infoContant">
			<p>{{ lang.p22 }}</p>
			<p class="leftText">{{ lang.p22_1 }}</p>
			<div class="uploadArea">
				<template v-if="infoUploadList.length > 0">
					<ul scrollBar>
						<li v-for="(citem, cindex) in infoUploadList" class="uploadItem" :key="citem.secure_url">
							<span class="uploadItem_text">{{ citem.original_filename }}</span>
							<div class="uploadItem_right">
								<span>{{ citem.size }} Kb</span>
								<b class="icon-check myIcon" style="color: #0cbd5f"></b>
								<el-button style="border: none; padding: 5px; background: transparent" icon="el-icon-delete" @click.stop="delUploadImg(cindex)"></el-button>
							</div>
						</li>
					</ul>
				</template>
				<div class="uploadList">
					<div class="drapBox" @click="triggerUpload" @drop="handleDrop" @dragover.prevent>
						<div><b class="icon-shangchuan1 uploadIcon"></b></div>
						<p class="sizeText">{{ lang.size80 }}</p>
						<input type="file" ref="uploadInfoUpload" :accept="acceptFileType" multiple @change="uploadPic" />
					</div>
				</div>
				<div class="upload-btn" v-if="infoUploadList.length > 0">
					<button type="button" @click="updateInquiry">
						<span>{{ lang.submitArtwork }}</span>
					</button>
				</div>
			</div>
			<p class="or">{{ lang.Or }}</p>
		</div>
		<div class="infoBtn">
			<button type="button" class="noFileBtn">
				<span>
					<a @click="closeInfoDialog" href="#">{{ lang.closeAndSend }} {{ userEmail }}</a>
				</span>
			</button>
		</div>
		<!-- <BaseDialog :overSizeDialog.sync="overSizeDialog"  :width="device != 'mb' ? '485px' : '90%'" :model="false">
                <template #closeIcon>
                    <div style="display: none;"></div>
                </template>
                <replaySizeUpload :overSizeList="fileSize">
                </replaySizeUpload>
        </BaseDialog> -->
	</div>
</template>

<script>
import { uploadFile } from "@/utils/oss";
import BaseDialog from "@/components/Quote/BaseDialog";
import { checkFile, acceptFileType } from "@/utils/validate";

export default {
	name: "infoUpload",
	components: {
		BaseDialog,
		// replaySizeUpload
	},
	props: {
		infoUploadList: {
			type: Array,
			default: () => [],
		},
	},
	data() {
		return {
			acceptFileType,
		};
	},
	watch: {},
	computed: {
		lang() {
			return this.$store.getters.lang?.quote;
		},
		userEmail() {
			return this.$store.state.proSystem.email;
		},
		device() {
			return this.$store.state.device;
		},
	},
	methods: {
		infoUpload() {
			this.triggerUpload();
			this.$store.commit("setSizeDialog", false);
		},
		triggerUpload() {
			this.$refs["uploadInfoUpload"].click();
		},
		handleDrop(event) {
			event.preventDefault();
			let files = event.dataTransfer.files;
			this.uploadPic(files, "drop");
		},
		uploadPic(event, type = "upload") {
			this.$gl.show();
			let files = type === "upload" ? event.target.files : event;
			let fileResult = checkFile(Array.from(files), this.acceptFileType, 80);
			if (!fileResult) {
				this.$toast.error("File type error");
				this.$gl.hide();
				return false;
			}
			if (fileResult.nomalSize.length == 0) {
				this.$gl.hide();
				this.$store.commit("setSizeDialog", true);
				this.$store.commit("setInputRefName", "infoUpload");
				this.$store.commit("setOverSizeList", fileResult.overSize);
				this.$refs.uploadInfoUpload.value = "";
				return false;
			}
			let uploadPromise = [];
			fileResult.nomalSize.forEach((file) => {
				let promise = uploadFile(file).then((res) => {
					let temp = {
						original_filename: file.name,
						secure_url: res,
						size: (file.size / 1024).toFixed(1),
					};
					this.$emit("pushInfoList", temp);
				});
				uploadPromise.push(promise);
			});
			Promise.all(uploadPromise).then(() => {
				this.$gl.hide();
				this.$refs.uploadInfoUpload.value = "";
				if (fileResult.overSize.length > 0) {
					this.$store.commit("setSizeDialog", true);
					this.$store.commit("setInputRefName", "infoUpload");
					this.$store.commit("setOverSizeList", fileResult.overSize);
				}
			});
		},
		delUploadImg(index) {
			this.$emit("delInfoList", index);
		},
		updateInquiry() {
			this.$emit("updateInquiry");
		},
		closeInfoDialog(e) {
			e.preventDefault();
			window.location.href = `mailto:${this.userEmail}`;
			this.$emit("closeInfoDialog", false);
		},
	},
	created() {},
	mounted() {
		this.$Bus.$on("infoUpload", this.infoUpload);
	},
	beforeDestroy() {
		this.$Bus.$off("infoUpload");
	},
};
</script>

<style scoped lang="scss">
// @import "@/assets/css/index.scss";
p {
	font-size: 16px;
	line-height: 24px;
	text-align: center;
	color: #3d3d3d;
	font-family: Calibri !important;
}

.infoUpload {
	padding-bottom: 30px;
	box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

	.infoTitle {
		position: relative;
		padding: 15px 15px 10px;
		display: flex;
		align-items: center;
		justify-content: center;
		font-family: Calibri !important;

		i {
			font-size: 28px;
			color: #67c23a;
		}

		span {
			margin-left: 10px;
			font-size: 20px;
			line-height: 1;
			font-family: Calibri !important;
		}
	}

	.infoContant {
		padding: 0 30px;

		.leftText {
			text-align: left;
			color: #ed2828;
		}

		.uploadArea {
			display: flex;
			flex-direction: column;
			justify-content: space-between;
			align-items: center;
			padding: 10px;
			padding-top: 0;
			background: #ffffff;
			border: 1px dashed #d8d8d8;
			border-radius: 4px;
			cursor: pointer;
			transition: all 0.3s;
			width: 90%;
			margin: 5px auto;
			font-family: Calibri !important;

			ul {
				background: hsla(0, 0%, 85%, 0.27);
				max-height: 88px;
				width: calc(100% + 20px);
				overflow: auto;
				margin-left: 0;
				font-family: Calibri !important;

				.uploadItem {
					font-family: Calibri !important;
					display: flex;
					justify-content: space-between;
					align-items: center;
					font-size: 12px;
					padding: 2px 10px;
					color: #3d3d3d;

					.uploadItem_text {
						padding-right: 5px;
						word-break: break-word;
						text-align: left;
					}

					.uploadItem_right {
						flex-shrink: 0;

						span {
							font-size: 12px;
						}
					}
				}

				.myIcon {
					margin: 0 4px;
				}
			}

			.uploadList {
				position: relative;
				width: 100%;
				height: auto;
				text-align: center;

				input[type="file"] {
					position: absolute;
					left: 0;
					top: 0;
					right: 0;
					bottom: 0;
					opacity: 0;
					z-index: -1;
					width: 0;
					height: 0;
				}

				.uploadIcon {
					height: 40px;
					font-size: 40px;
					color: #747171;
				}

				.sizeText {
					color: #868484;
					padding: 0 18px;
				}
			}

			.upload-btn {
				font-family: Calibri !important;
				text-align: center;

				button {
					font-size: 16px;
					border-radius: 2px;
					color: #fff;
					line-height: 35px;
					padding: 0 10px;
					margin-top: 12px;
					background: #ff633a;
				}
			}
		}

		.or {
			font-family: Calibri !important;
			font-weight: bold;
			margin: 5px auto;
		}
	}

	.infoBtn {
		text-align: center;

		.noFileBtn {
			font-size: 16px;
			font-weight: 400;
			background-color: #3a9ef4;
			border-color: #3a9ef4;
			color: #fff;
			padding: 9px 15px;
			border-radius: 3px;
			font-family: Calibri !important;
		}
	}

	@media screen and (max-width: 767px) {
		p {
			font-size: 14px;
		}
		.infoContant {
			padding: 0 15px;
		}
		.infoContant .uploadArea {
			.uploadList {
				.sizeText {
					padding: 0 12px;
				}

				.uploadIcon {
					height: 37px;
					margin-top: 0;
					font-size: 36px;
				}
			}

			.upload-btn {
				button {
					font-size: 14px;
				}
			}
		}
		.infoBtn {
			text-align: center;

			.noFileBtn {
				font-size: 14px;
			}
		}
	}
}
</style>