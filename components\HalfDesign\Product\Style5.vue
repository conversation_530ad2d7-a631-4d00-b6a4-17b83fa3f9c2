<template>
	<div class="mb-4" :class="stepData.styleName">
		<div class="tips">
			{{stepData.attributeTitle}}
		</div>
		<slot name="stepText"></slot>
		<template v-for="(item,index) in tabContent">
			<div class="tab-content custom-scrollbar" v-if="index===tabIndex && item.list.length>0">
				<div class="tab-item" v-for="citem in item.list">
					<div class="tab-item-title">
						{{ item.paramName }}: <span class="selectedColor">321321</span>
					</div>
					<div class="tab-item-content">
						<template v-for="colorItem in citem.list">
							<v-tooltip top>
								<template v-slot:activator="{ on, attrs }">
									<div class="tab-item-color" v-bind="attrs" v-on="on" :style="customColorStyle(colorItem)">
										<v-icon color="#ffffff">
											mdi-check
										</v-icon>
									</div>
								</template>
								<span>{{ colorItem.codeName }}</span>
							</v-tooltip>
						</template>
					</div>
				</div>
			</div>
		</template>
	</div>
</template>
<script>
export default {
	props: {
		stepData: {
			type: Object,
			default: {}
		}
	},
	data() {
		return {
			selectIndex: -1,
			selectItem: null,
			tabIndex: 0,
			tabContent: [
				{
					paramName: "FRONT",
					list: [
						{
							paramName: "MATTE",
							list: [
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								}
							]
						},
						{
							paramName: "GLOSSY",
							list: [
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								}
							]
						}
					]
				},
				{
					paramName: "LEFT",
					list: [
						{
							paramName: "MATTE",
							list: [
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								}
							]
						},
						{
							paramName: "GLOSSY",
							list: [
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								}
							]
						}
					]
				},
				{
					paramName: "RIGHT",
					list: [
						{
							paramName: "MATTE",
							list: [
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								}
							]
						},
						{
							paramName: "GLOSSY",
							list: [
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								}
							]
						}
					]
				},
				{
					paramName: "LENS",
					list: [
						{
							paramName: "MATTE",
							list: [
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								}
							]
						},
						{
							paramName: "GLOSSY",
							list: [
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								}
							]
						}
					]
				},
				{
					paramName: "K LOGO",
					list: [
						{
							paramName: "MATTE",
							list: [
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								}
							]
						},
						{
							paramName: "GLOSSY",
							list: [
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg"
								}
							]
						}
					]
				}
			]
		}
	},
	computed: {
		shape() {
			return this.selectItem?.valueName
		}
	},
	methods: {
		selectStep(item, index) {
			this.selectIndex = index;
			this.selectItem = item
		},
		selectTab(index) {
			this.tabIndex = index
		},
		customColorStyle(colorItem) {
			return {
				backgroundColor: colorItem.code
			}
		}
	},
	mounted() {

	}
}
</script>
<style scoped lang="scss">
@import "~assets/css/half.scss";

.tips {
	margin-bottom: 10px;
	color: $gray-text;
}

.tab-content {

	.tab-item {
		.tab-item-title {
			margin-bottom: 4px;
		}

		.tab-item-content {
			display: grid;
			grid-template-columns: repeat(15, 1fr);
			grid-gap: 10px;
			margin-bottom: 10px;

			.tab-item-color {
				@include flex-center;
				position: relative;
				border: 1px solid $border-color;
				cursor: pointer;
				aspect-ratio: 1;

				.v-icon{
					display: none;
				}

				@media (any-hover: hover) {
					&:hover {
						border-color: $color-primary;
						border-width: 2px;
					}
				}

				&.active {
					border-color: $color-primary;
					border-width: 2px;
					.v-icon{
						display: block;
					}
				}
			}
		}
	}
}
</style>
