<template>
  <div class="design-step stepWrap"
    :id="stepData.paramName"
    :class="{ active: stepData.paramName === maskName }">
    <slot name="title">
      <StepTitle :title="getTitle(stepData)"
        :step="getStep(stepData)">
        <template #suffix>
          <ToolTip :itemData="stepData"
            style="font-size: 16px"></ToolTip>
        </template>
      </StepTitle>
    </slot>

    <slot name="content">
      <div class="stepContent"
        :ref="stepData.paramName">
        <!-- Tabs 导航 -->
        <div class="tabs-nav">
          <div v-for="(tab, index) in stepData.childList"
            :key="index"
            :class="['tab-item', { active: currentSelectItem?.paramCode == tab.paramCode }]"
            :ref="`tab-${index}`"
            @click="handleTabClick(tab, index)">
            <span class="product-info">
              <label :for="tab.paramName + tab.id"
                class="radio-beauty"></label>
              <span class="bTitle">{{ tab.alias }}</span>
            </span>
          </div>
        </div>

        <!-- Tabs 内容 -->
        <div class="tabs-content">
          <!-- 箭头元素 -->
          <div class="content-arrow"></div>
          <!-- 文字设计 -->
          <div class="pane"
            v-if="currentSelectItem?.paramCode == 1"
            @click="handleClick">
            <!-- 前面 -->
            <div v-show="copyCanvasData.messageStyle != 'around'">
              <div class="title">{{ lang.wristband.frontMessage }}</div>
              <div class="line"
                v-for="(item,index) in copyCanvasData.frontTextGroup"
                :key="item.id">
                <div class="z1">
                  <el-input :placeholder="lang.wristband.thirtyTextLimit"
                    v-model="item.text"
                    :maxlength="item.textLimit"></el-input>
                </div>
                <div class="z2">
                  <el-select class="customFontFamily"
                    v-model="item.family"
                    style="width: 100%"
                    :placeholder="semiLang.ff">
                    <el-option v-for="(itemc, indexc) in fontList"
                      :style="{ fontFamily: itemc.name + ' !important' }"
                      :key="indexc"
                      :label="itemc.name"
                      :value="itemc.name"></el-option>
                  </el-select>
                </div>
                <div class="z3">
                  <!-- 选第一行字体粗细 -->
                  <div class="weight">
                    <span class="icon title"
                      @click="item.weight = !item.weight"
                      :class="{'select': item.weight }">B</span>
                  </div>
                  <!-- 选第一行斜体 -->
                  <div class="italic">
                    <span class="icon title"
                      @click="item.italic = !item.italic"
                      :class="{'select': item.italic }">/</span>
                  </div>
                  <!-- 选第一行颜色 -->
                  <div class="color"
                    @click.stop="item.colorShow = true">
                    <el-popover v-model:visible="item.colorShow"
                      popper-class="colorPopper"
                      placement="bottom"
                      :width="400"
                      trigger="click">
                      <template #reference>
                        <div class="inputStyleLike">
                          <span class="icon title"
                            :style="{ '--select-color': item.color }">A</span>
                        </div>
                      </template>
                      <div class="closeBtn">
                        <el-button class="btn-close"
                          circle
                          @click="item.colorShow = false"></el-button>
                        <div class="colorPickerGridBox custom-scrollbar">
                          <div v-for="itemc in colorList"
                            :title="itemc.pantone"
                            :key="itemc.id"
                            :style="{ backgroundColor: itemc.code }"
                            @click.stop="updateAllColors(itemc.code); item.colorShow = false"></div>
                        </div>
                      </div>
                    </el-popover>
                  </div>
                </div>
                <!-- <div class="z4 title">
                  <span v-if="!index"
                    class="icon"
                    @click="addNewLine('frontTextGroup')">
                    <i class="el-icon-circle-plus"></i>&nbsp;Add
                  </span>
                  <span v-else
                    class="icon"
                    style="color: #ff9d9d"
                    @click="delLine('frontTextGroup', index)">
                    <i class="el-icon-delete"></i>
                  </span>
                </div> -->
              </div>
            </div>

            <!-- 背面 -->
            <div v-show="copyCanvasData.messageStyle == 'frontback'">
              <div class="title">{{ lang.wristband.backMessage }}</div>
              <div class="line"
                v-for="(item,index) in copyCanvasData.backTextGroup"
                :key="item.id">
                <div class="z1">
                  <el-input :placeholder="lang.wristband.thirtyTextLimit"
                    v-model="item.text"
                    :maxlength="item.textLimit"></el-input>
                </div>
                <div class="z2">
                  <el-select class="customFontFamily"
                    v-model="item.family"
                    style="width: 100%"
                    :placeholder="semiLang.ff">
                    <el-option v-for="(itemc, indexc) in fontList"
                      :style="{ fontFamily: itemc.name + ' !important' }"
                      :key="indexc"
                      :label="itemc.name"
                      :value="itemc.name"></el-option>
                  </el-select>
                </div>
                <div class="z3">
                  <!-- 选第一行字体粗细 -->
                  <div class="weight">
                    <span class="icon title"
                      @click="item.weight = !item.weight"
                      :class="{'select': item.weight }">B</span>
                  </div>
                  <!-- 选第一行斜体 -->
                  <div class="italic">
                    <span class="icon title"
                      @click="item.italic = !item.italic"
                      :class="{'select': item.italic }">/</span>
                  </div>
                  <!-- 选第一行颜色 -->
                  <div class="color"
                    @click.stop="item.colorShow = true">
                    <el-popover v-model:visible="item.colorShow"
                      popper-class="colorPopper"
                      placement="bottom"
                      :width="400"
                      trigger="click">
                      <template #reference>
                        <div class="inputStyleLike">
                          <span class="icon title"
                            :style="{ '--select-color': item.color }">A</span>
                        </div>
                      </template>
                      <div class="closeBtn">
                        <el-button class="btn-close"
                          circle
                          @click="item.colorShow = false"></el-button>
                        <div class="colorPickerGridBox custom-scrollbar">
                          <div v-for="itemc in colorList"
                            :title="itemc.pantone"
                            :key="itemc.id"
                            :style="{ backgroundColor: itemc.code }"
                            @click.stop="updateAllColors(itemc.code); item.colorShow = false"></div>
                        </div>
                      </div>
                    </el-popover>
                  </div>
                </div>
                <!-- <div class="z4 title">
                  <span v-if="!index"
                    class="icon"
                    @click="addNewLine('backTextGroup')">
                    <i class="el-icon-circle-plus"></i>&nbsp;Add
                  </span>
                  <span v-else
                    class="icon"
                    style="color: #ff9d9d"
                    @click="delLine('backTextGroup', index)">
                    <i class="el-icon-delete"></i>
                  </span>
                </div> -->
              </div>
            </div>

            <!-- 环绕 -->
            <div v-show="copyCanvasData.messageStyle == 'around'">
              <div class="title">{{ lang.wristband.aroundMessage }}</div>
              <div class="line"
                v-for="(item,index) in copyCanvasData.aroundTextGroup"
                :key="item.id">
                <div class="z1">
                  <el-input :placeholder="lang.wristband.eightyTextLimit"
                    v-model="item.text"
                    :maxlength="item.textLimit"></el-input>
                </div>
                <div class="z2">
                  <el-select class="customFontFamily"
                    v-model="item.family"
                    style="width: 100%"
                    :placeholder="semiLang.ff">
                    <el-option v-for="(itemc, indexc) in fontList"
                      :style="{ fontFamily: itemc.name + ' !important' }"
                      :key="indexc"
                      :label="itemc.name"
                      :value="itemc.name"></el-option>
                  </el-select>
                </div>
                <div class="z3">
                  <!-- 选第一行字体粗细 -->
                  <div class="weight">
                    <span class="icon title"
                      @click="item.weight = !item.weight"
                      :class="{'select': item.weight }">B</span>
                  </div>
                  <!-- 选第一行斜体 -->
                  <div class="italic">
                    <span class="icon title"
                      @click="item.italic = !item.italic"
                      :class="{'select': item.italic }">/</span>
                  </div>
                  <!-- 选第二行颜色 -->
                  <div class="color"
                    @click.stop="item.colorShow = true">
                    <el-popover v-model:visible="item.colorShow"
                      popper-class="colorPopper"
                      placement="bottom"
                      :width="400"
                      trigger="click">
                      <template #reference>
                        <div class="inputStyleLike">
                          <span class="icon title"
                            :style="{ '--select-color': item.color }">A</span>
                        </div>
                      </template>
                      <div class="closeBtn">
                        <el-button class="btn-close"
                          circle
                          @click="item.colorShow = false"></el-button>
                        <div class="colorPickerGridBox custom-scrollbar">
                          <div v-for="itemc in colorList"
                            :title="itemc.pantone"
                            :key="itemc.id"
                            :style="{ backgroundColor: itemc.code }"
                            @click.stop="updateAllColors(itemc.code); item.colorShow = false"></div>
                        </div>
                      </div>
                    </el-popover>
                  </div>
                </div>
                <!-- <div class="z4 title">
                  <span class="icon"
                    v-if="!index"
                    @click="addNewLine('insideTextGroup')">
                    <i class="el-icon-circle-plus"></i>&nbsp;Add
                  </span>
                  <span v-else
                    class="icon"
                    style="color: #ff9d9d"
                    @click="delLine('insideTextGroup', index)">
                    <i class="el-icon-delete"></i>
                  </span>
                </div> -->
              </div>
            </div>

            <!-- 内面 -->
            <div class="insideText">
              <div class="title">{{ lang.wristband.insideMessage }}
                <ToolTip :itemData="{ tips: lang.wristband.insideTips }"></ToolTip>
                <span>+ (<CCYRate :price="insidePrice"></CCYRate>)</span>
              </div>
              <div class="line"
                v-for="(item,index) in copyCanvasData.insideTextGroup"
                :key="item.id">
                <div class="z1">
                  <el-input :placeholder="lang.wristband.sixtyTextLimit"
                    v-model="item.text"
                    :maxlength="item.textLimit"></el-input>
                </div>
                <div class="z2">
                  <el-select class="customFontFamily"
                    v-model="item.family"
                    style="width: 100%"
                    :placeholder="semiLang.ff">
                    <el-option v-for="(itemc, indexc) in fontList"
                      :style="{ fontFamily: itemc.name + ' !important' }"
                      :key="indexc"
                      :label="itemc.name"
                      :value="itemc.name"></el-option>
                  </el-select>
                </div>
                <div class="z3">
                  <!-- 选第一行字体粗细 -->
                  <div class="weight">
                    <span class="icon title"
                      @click="item.weight = !item.weight"
                      :class="{'select': item.weight }">B</span>
                  </div>
                  <!-- 选第一行斜体 -->
                  <div class="italic">
                    <span class="icon title"
                      @click="item.italic = !item.italic"
                      :class="{'select': item.italic }">/</span>
                  </div>
                  <!-- 选第二行颜色 -->
                  <div class="color"
                    @click.stop="item.colorShow = true">
                    <el-popover v-model:visible="item.colorShow"
                      popper-class="colorPopper"
                      placement="bottom"
                      :width="400"
                      trigger="click">
                      <template #reference>
                        <div class="inputStyleLike">
                          <span class="icon title"
                            :style="{ '--select-color': item.color }">A</span>
                        </div>
                      </template>
                      <div class="closeBtn">
                        <el-button class="btn-close"
                          circle
                          @click="item.colorShow = false"></el-button>
                        <div class="colorPickerGridBox custom-scrollbar">
                          <div v-for="itemc in colorList"
                            :title="itemc.pantone"
                            :key="itemc.id"
                            :style="{ backgroundColor: itemc.code }"
                            @click.stop="updateAllColors(itemc.code); item.colorShow = false"></div>
                        </div>
                      </div>
                    </el-popover>
                  </div>
                </div>
                <!-- <div class="z4 title">
                  <span class="icon"
                    v-if="!index"
                    @click="addNewLine('insideTextGroup')">
                    <i class="el-icon-circle-plus"></i>&nbsp;Add
                  </span>
                  <span v-else
                    class="icon"
                    style="color: #ff9d9d"
                    @click="delLine('insideTextGroup', index)">
                    <i class="el-icon-delete"></i>
                  </span>
                </div> -->
              </div>
            </div>

            <!-- 图标上传 前面 -->
            <div class="clipart-grou front"
              v-show="copyCanvasData.messageStyle != 'around'">
              <div class="clipart start">
                <div class="title">{{ lang.wristband.frontStartClipart }}:</div>
                <div class="line fontBox">
                  <el-select id="frontStartClipartValue"
                    style="width: 100%"
                    popper-class="myClipartSelect"
                    v-model="copyCanvasData.frontStartClipartValue"
                    :placeholder="lang.PleaseSelect">
                    <el-option @click.native="clipartFun(item.id, $event, 'frontStartImg')"
                      v-for="(item, index) in clipartList"
                      :key="item.id"
                      :label="item.title"
                      :value="item.id"></el-option>
                  </el-select>
                  <label v-if="copyCanvasData.frontStartImg"
                    for="frontStartClipartValue"
                    class="fontImg">
                    <img :src="copyCanvasData.frontStartImg"
                      alt=""
                      loading="lazy" />
                  </label>
                </div>
              </div>

              <div class="clipart end">
                <div class="title">{{ lang.wristband.frontEndClipart }}:</div>
                <div class="line fontBox">
                  <el-select id="frontEndClipartValue"
                    style="width: 100%"
                    v-model="copyCanvasData.frontEndClipartValue"
                    popper-class="myClipartSelect"
                    :placeholder="lang.PleaseSelect">
                    <el-option @click.native="clipartFun(item.id, $event, 'frontEndImg')"
                      v-for="(item, index) in clipartList"
                      :key="item.id"
                      :label="item.title"
                      :value="item.id"></el-option>
                  </el-select>
                  <label v-if="copyCanvasData.frontEndImg"
                    for="frontEndClipartValue"
                    class="fontImg">
                    <img :src="copyCanvasData.frontEndImg"
                      alt=""
                      loading="lazy" />
                  </label>
                </div>
              </div>
            </div>

            <!-- 图标上传 背面 -->
            <div class="clipart-grou back"
              v-show="copyCanvasData.messageStyle == 'frontback'">
              <div class="clipart start">
                <div class="title">{{ lang.wristband.backStartClipart }}:</div>
                <div class="line fontBox">
                  <el-select id="backStartClipartValue"
                    style="width: 100%"
                    v-model="copyCanvasData.backStartClipartValue"
                    popper-class="myClipartSelect"
                    :placeholder="lang.PleaseSelect">
                    <el-option @click.native="clipartFun(item.id, $event, 'backStartImg')"
                      v-for="(item, index) in clipartList"
                      :key="item.id"
                      :label="item.title"
                      :value="item.id"></el-option>
                  </el-select>
                  <label v-if="copyCanvasData.backStartImg"
                    for="backStartClipartValue"
                    class="fontImg">
                    <img :src="copyCanvasData.backStartImg"
                      alt=""
                      loading="lazy" />
                  </label>
                </div>
              </div>

              <div class="clipart end">
                <div class="title">{{ lang.wristband.backEndClipart }}:</div>
                <div class="line fontBox">
                  <el-select id="backEndClipartValue"
                    style="width: 100%"
                    v-model="copyCanvasData.backEndClipartValue"
                    popper-class="myClipartSelect"
                    :placeholder="lang.PleaseSelect">
                    <el-option @click.native="clipartFun(item.id, $event, 'backEndImg')"
                      v-for="(item, index) in clipartList"
                      :key="item.id"
                      :label="item.title"
                      :value="item.id"></el-option>
                  </el-select>
                  <label v-if="copyCanvasData.backEndImg"
                    for="backEndClipartValue"
                    class="fontImg">
                    <img :src="copyCanvasData.backEndImg"
                      alt=""
                      loading="lazy" />
                  </label>
                </div>
              </div>
            </div>

            <!-- 图标上传 环绕 -->
            <div class="clipart-grou around"
              v-show="copyCanvasData.messageStyle == 'around'">
              <div class="clipart start">
                <div class="title">{{ lang.wristband.aroundStartClipart }}:</div>
                <div class="line fontBox">
                  <el-select id="aroundStartClipartValue"
                    style="width: 100%"
                    v-model="copyCanvasData.aroundStartClipartValue"
                    popper-class="myClipartSelect"
                    :placeholder="lang.PleaseSelect">
                    <el-option @click.native="clipartFun(item.id, $event, 'aroundStartImg')"
                      v-for="(item, index) in clipartList"
                      :key="item.id"
                      :label="item.title"
                      :value="item.id"></el-option>
                  </el-select>
                  <label v-if="copyCanvasData.aroundStartImg"
                    for="aroundStartClipartValue"
                    class="fontImg">
                    <img :src="copyCanvasData.aroundStartImg"
                      alt=""
                      loading="lazy" />
                  </label>
                </div>
              </div>

              <div class="clipart end">
                <div class="title">{{ lang.wristband.aroundEndClipart }}:</div>
                <div class="line fontBox">
                  <el-select id="aroundEndClipartValue"
                    style="width: 100%"
                    v-model="copyCanvasData.aroundEndClipartValue"
                    popper-class="myClipartSelect"
                    :placeholder="lang.PleaseSelect">
                    <el-option @click.native="clipartFun(item.id, $event, 'aroundEndImg')"
                      v-for="(item, index) in clipartList"
                      :key="item.id"
                      :label="item.title"
                      :value="item.id"></el-option>
                  </el-select>
                  <label v-if="copyCanvasData.aroundEndImg"
                    for="aroundEndClipartValue"
                    class="fontImg">
                    <img :src="copyCanvasData.aroundEndImg"
                      alt=""
                      loading="lazy" />
                  </label>
                </div>
              </div>
            </div>

            <!-- 备注 -->
            <div class="comments">
              <div class="title">{{ lang.OrderComments }}</div>
              <div class="line">
                <el-input rows="5"
                  type="textarea"
                  :placeholder="lang.wristband.commentsTips"
                  :value="comments"
                  @input="updateComments"
                  maxlength="500"></el-input>
              </div>
            </div>
          </div>
          <div class="pane"
            v-if="currentSelectItem?.paramCode == 2">
            <div class="upload-box">
              <div class="upload-window">
                <ul v-if="fontImgCustom.length">
                  <li v-for="(item, index) in fontImgCustom"
                    :key="index">
                    <span>{{ item.original_filename }}</span>
                    <span class="icon-box">
                      <b class="icon-check"
                        style="color: #0cbd5f"></b>
                      <b class="icon-shanchu2 pointer"
                        style="color: #b6b0b0"
                        @click.stop="removeFile(index)"></b>
                    </span>
                  </li>
                </ul>
                <label for="upload_widget"
                  class="uploadLabel">
                  <div class="upload-btn">
                    <b class="icon-shangchuan myIcon"></b>
                    <a @click="browseFun">
                      {{ lang.Upload }}
                    </a>
                  </div>
                  <div class="info">
                    {{ lang.content2 }}
                  </div>
                </label>
              </div>
              <div class="comments">
                <div class="title">{{ lang.OrderComments }}</div>
                <div class="line">
                  <el-input rows="5"
                    type="textarea"
                    :placeholder="lang.wristband.commentsTips"
                    :value="comments"
                    @input="updateComments"
                    maxlength="500"></el-input>
                </div>
              </div>
            </div>
          </div>
          <div class="pane"
            v-if="currentSelectItem?.paramCode == 3">
            <div class="email-later">
              <div class="notes">
                <span class="pre">{{ lang.p26 }}</span>
                <a @click.stop
                  class="click_text"
                  :href="`mailto:${userEmail}`">
                  {{ userEmail }}.
                </a>
              </div>
              <div class="comments">
                <div class="title">{{ lang.OrderComments }}</div>
                <div class="line">
                  <el-input rows="5"
                    type="textarea"
                    :placeholder="lang.wristband.commentsTips"
                    :value="comments"
                    @input="updateComments"
                    maxlength="500"></el-input>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </slot>
    <slot name="footer">
      <div class="stepFooter">
        <QuoteBtn @click.native="nextStepFun(stepData.paramName)"
          :disabled="disabled">
          {{ lang.next }}
        </QuoteBtn>
      </div>
    </slot>
    <i v-if="stepData.paramName === maskName"
      class="el-icon-close closeIcon"
      @click="closeMask"></i>
    <input type="file"
      ref="upload"
      :multiple="uploadType == 'file'"
      :accept="acceptFileType"
      @change="handleUploadPic"
      hidden />
    <el-dialog :append-to-body="true"
      :visible.sync="allImgDialog">
      <div class="allImgDialogBox">
        <div class="item"
          @click="imgPickerFun(item)"
          v-for="item in allImage"
          :title="item.pantone"
          :key="item.id">
          <img :src="item.imageUrl"
            :alt="item.imageName"
            loading="lazy" />
          <div class="btn">
            <el-button circle>
              <svg viewBox="0 0 1024 1024"
                xmlns="http://www.w3.org/2000/svg"
                data-v-ba633cb8="">
                <path fill="currentColor"
                  d="M480 480V128a32 32 0 0 1 64 0v352h352a32 32 0 1 1 0 64H544v352a32 32 0 1 1-64 0V544H128a32 32 0 0 1 0-64h352z">
                </path>
              </svg>
            </el-button>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import StepTitle from "@/components/Quote/PublicStep/StepTitle.vue";
import ToolTip from "@/components/Quote/ToolTip.vue";
import QuoteBtn from "@/components/Quote/QuoteBtn.vue";
import { uploadFile } from "@/utils/oss";
import { checkFile, acceptFileType } from "@/utils/validate";
import { indexApi } from "@/api/lanyardQuote";
export default {
  components: { ToolTip, StepTitle, QuoteBtn },
  props: {
    stepData: {
      type: Object,
      default: () => {},
    },
    selectedData: {
      type: Object,
      default: () => {},
    },
    maskName: {
      type: [String, Boolean],
    },
    colorList: {
      type: Array,
      default: () => [],
    },
    copyCanvasData: {
      type: Object,
    },
    comments: {
      type: String,
    },
    fontImgCustom: {
      type: Array,
    },
  },
  data() {
    return {
      defaultForm: {
        frontTextGroup: [
          {
            id: Date.now(),
            text: "",
            family: "",
            weight: false,
            italic: false,
            color: "",
            colorShow: false,
            textLimit: 30,
          },
        ],
        backTextGroup: [
          {
            id: Date.now() + 1,
            text: "",
            family: "",
            weight: false,
            italic: false,
            color: "",
            colorShow: false,
            textLimit: 30,
          },
        ],
        insideTextGroup: [
          {
            id: Date.now() + 2,
            text: "",
            family: "",
            weight: false,
            italic: false,
            color: "",
            colorShow: false,
            textLimit: 60,
          },
        ],
        aroundTextGroup: [
          {
            id: Date.now() + 3,
            text: "",
            family: "",
            weight: false,
            italic: false,
            color: "",
            colorShow: false,
            textLimit: 80,
          },
        ],
        frontTextPositionX: 500,
        frontTextPositionY: 420,
        insideTextPositionX: 500,
        insideTextPositionY: 220,
        backTextPositionX: 500,
        backTextPositionY: 420,
        aroundTextPositionX: 500,
        aroundTextPositionY: 420,

        frontStartClipartValue: 1,
        frontEndClipartValue: 1,
        backStartClipartValue: 1,
        backEndClipartValue: 1,
        aroundStartClipartValue: 1,
        aroundEndClipartValue: 1,

        frontStartImg: "",
        frontEndImg: "",
        backStartImg: "",
        backEndImg: "",
        aroundStartImg: "",
        aroundEndImg: "",
      },
      acceptFileType,
      allImage: [],
      clipartKey: "",
      fontLogo:
        "http://res.cloudinary.com/gs-jj-cloud/image/upload/v1678673484/gs-jj/eod0cz3u4txokf6xpvzp.png",
      logo: "https://res.cloudinary.com/gs-jj-cloud/image/upload/v1677139759/gs-jj/soikei6sst8uo5r6cvbl.png",
      allImgDialog: false,
      uploadType: "file", //上传类型 文件 / 图标
      //   copyCanvasData: {
      //     frontTextGroup: [
      //       {
      //         id: Date.now(),
      //         text: "",
      //         family: "",
      //         weight: false,
      //         italic: false,
      //         color: "",
      //         colorShow: false,
      //         textLimit: 30,
      //       },
      //     ],
      //     backTextGroup: [
      //       {
      //         id: Date.now() + 1,
      //         text: "",
      //         family: "",
      //         weight: false,
      //         italic: false,
      //         color: "",
      //         colorShow: false,
      //         textLimit: 30,
      //       },
      //     ],
      //     insideTextGroup: [
      //       {
      //         id: Date.now() + 2,
      //         text: "",
      //         family: "",
      //         weight: false,
      //         italic: false,
      //         color: "",
      //         colorShow: false,
      //         textLimit: 60,
      //       },
      //     ],
      //     aroundTextGroup: [
      //       {
      //         id: Date.now() + 3,
      //         text: "",
      //         family: "",
      //         weight: false,
      //         italic: false,
      //         color: "",
      //         colorShow: false,
      //         textLimit: 80,
      //       },
      //     ],
      //     frontTextPositionX: 500,
      //     frontTextPositionY: 420,
      //     insideTextPositionX: 500,
      //     insideTextPositionY: 220,
      //     backTextPositionX: 500,
      //     backTextPositionY: 420,
      //     aroundTextPositionX: 500,
      //     aroundTextPositionY: 420,

      //     frontStartClipartValue: 1,
      //     frontEndClipartValue: 1,
      //     backStartClipartValue: 1,
      //     backEndClipartValue: 1,
      //     aroundStartClipartValue: 1,
      //     aroundEndClipartValue: 1,

      //     frontStartImg: "",
      //     frontEndImg: "",
      //     backStartImg: "",
      //     backEndImg: "",
      //     aroundStartImg: "",
      //     aroundEndImg: "",
      //   },
    };
  },
  watch: {
    // copyCanvasData: {
    //   handler(val) {
    //     this.$emit("copyCanvasData", val);
    //   },
    //   deep: true,
    // },
    currentSelectItem: {
      handler(val) {
        const index = Number(this.currentSelectItem?.paramCode || 1) - 1;
        this.updateArrowPosition(index);
      },
    },
    "copyCanvasData.messageStyle": {
      handler(val) {
        this.resetFontParam(val);
      },
    },
  },
  computed: {
    currentSelectItem() {
      return this.selectedData["Design Your Band"][0];
    },
    insidePrice() {
      return this.stepData.childList[0].childList[0].priceInfo.unitPrice;
    },
    lang() {
      return this.$store.getters.lang.quote || {};
    },
    semiLang() {
      return this.$store.getters.lang.semiCustom || {};
    },
    fontList() {
      return require("@/assets/json/fontList.json").filter(
        (item) => !item.noResource
      );
    },
    userEmail() {
      return this.$store.state.proSystem.email;
    },
    clipartList() {
      return [
        {
          id: 1,
          title: this.lang.noClipart,
        },
        {
          id: 2,
          title: this.lang.browseCliparts,
        },
        {
          id: 3,
          title: this.lang.chooseClipart,
        },
        {
          id: 4,
          title: this.lang.uploadLater,
        },
      ];
    },
    disabled() {
      if (this.currentSelectItem?.paramCode == 2 && !this.fontImgCustom.length)
        return true;
      if (this.currentSelectItem?.paramCode == 1) {
        if (this.copyCanvasData.messageStyle == "front")
          return !this.copyCanvasData.frontTextGroup[0].text.trim().length;
        if (this.copyCanvasData.messageStyle == "frontback")
          return (
            !this.copyCanvasData.frontTextGroup[0].text.trim().length ||
            !this.copyCanvasData.backTextGroup[0].text.trim().length
          );
        if (this.copyCanvasData.messageStyle == "around")
          return !this.copyCanvasData.aroundTextGroup[0].text.trim().length;
      }
      return false;
    },
  },
  methods: {
    handleClick() {
      this.$emit("update:maskName", this.stepData.paramName);
    },
    updateAllColors(newColor) {
      // 更新所有文本组的颜色
      this.copyCanvasData.frontTextGroup[0].color = newColor;
      this.copyCanvasData.backTextGroup[0].color = newColor;
      this.copyCanvasData.insideTextGroup[0].color = newColor;
      this.copyCanvasData.aroundTextGroup[0].color = newColor;
    },
    browseFun() {
      // this.openWidget();
      this.$refs.upload.click();
    },
    // 添加新行
    addNewLine(key) {
      if (!this.selectedData["Band Size"].length) {
        this.$toast.error(this.lang.wristband.selectSize);
        return;
      }

      const obj = {
        id: Date.now(),
        text: "",
        family: "",
        weight: false,
        italic: false,
        color: "",
        colorShow: false,
      };

      this.copyCanvasData[key].push(obj);
    },
    //删除自定义图片
    removeFile(index) {
      this.fontImgCustom.splice(index, 1);
      this.$emit("update:fontImgCustom", this.fontImgCustom);
    },
    // 删除行
    delLine(key, index) {
      this.copyCanvasData[key].splice(index, 1);
    },
    handleTabClick(row, index) {
      if (row.paramCode == this.currentSelectItem?.paramCode) return;
	  this.resetFontParam()
      this.updateArrowPosition(index);
      this.$emit("update:fontImgCustom", []);
      this.$emit("clickFun", row);
    },
    updateArrowPosition(index) {
      this.$nextTick(() => {
        const tabEl = this.$refs[`tab-${index}`];
        if (tabEl) {
          const tabElement = tabEl[0]; // 获取 DOM 元素
          const arrow = this.$el.querySelector(".content-arrow");
          if (arrow) {
            const tabRect = tabElement.getBoundingClientRect();
            const contentRect = arrow.parentElement.getBoundingClientRect();
            const leftPosition =
              tabRect.left - contentRect.left + tabRect.width / 2;
            arrow.style.left = `${leftPosition}px`;
          }
        }
      });
    },
    getAll() {
      indexApi.getAllByType("Wristbands").then((res) => {
        this.allImage = res.data;
      });
    },
    imgPickerFun(value) {
      this.copyCanvasData[this.clipartKey] = value.imageUrl;
      this.allImgDialog = false;
    },
    clipartFun(val, event, key) {
      this.clipartKey = key;
      switch (val) {
        case 1:
          this.copyCanvasData[key] = "";
          break;
        case 2:
          this.allImgDialog = true;
          !this.allImage.length && this.getAll();
          break;
        case 3:
          this.uploadType = "icon";
          this.$refs.upload.click();
          break;
        case 4:
          this.copyCanvasData[key] = this.fontLogo;
          break;
        default:
          break;
      }
    },
    handleUploadPic(event) {
      if (this.uploadType == "file") {
        this.uploadPic(event);
      } else {
        this.uploadPicClipart(event);
      }
    },
    uploadPic(event, type = "upload") {
      this.$gl.show();
      let files = type === "upload" ? event.target.files : event;
      let fileResult = checkFile(Array.from(files), this.acceptFileType, 80);
      if (!fileResult) {
        this.$toast.error("File type error");
        this.$gl.hide();
        return false;
      }
      if (fileResult.nomalSize.length == 0) {
        this.$gl.hide();
        this.$store.commit("setSizeDialog", true);
        this.$store.commit("setInputRefName", "printTabs");
        this.$store.commit("setOverSizeList", fileResult.overSize);
        this.$refs.upload.value = "";
        return false;
      }

      let uploadPromises = [];
      fileResult.nomalSize.forEach((file) => {
        let promise = uploadFile(file).then((res) => {
          this.$emit("updateUploadList", {
            original_filename: file.name,
            secure_url: res,
          });
        });
        uploadPromises.push(promise);
      });

      Promise.all(uploadPromises).then(() => {
        this.$gl.hide();
        if (fileResult.overSize.length > 0) {
          this.$store.commit("setSizeDialog", true);
          this.$store.commit("setInputRefName", "printTabs");
          this.$store.commit("setOverSizeList", fileResult.overSize);
        } else {
          this.$toast.success("success");
        }
        this.$refs.upload.value = "";
      });
    },
    uploadPicClipart(event, type = "upload") {
      this.$gl.show();
      let files = type === "upload" ? event.target.files : event;
      let fileResult = checkFile(Array.from(files), this.acceptFileType, 80);
      if (!fileResult) {
        this.$toast.error("File type error");
        this.$gl.hide();
        return false;
      }
      if (fileResult.nomalSize.length == 0) {
        this.$gl.hide();
        this.$store.commit("setSizeDialog", true);
        this.$store.commit("setInputRefName", "uploadPicClipart");
        this.$store.commit("setOverSizeList", fileResult.overSize);
        this.$refs.upload.value = "";
        return false;
      }
      uploadFile(fileResult.nomalSize[0])
        .then((res) => {
          this.copyCanvasData[this.clipartKey] = res;
          this.$refs.upload.value = "";
          this.$forceUpdate();
        })
        .finally(() => {
          this.$gl.hide();
          this.$toast.success("success");
        });
    },
    updateComments(value) {
      this.$emit("update:comments", value);
    },
    closeMask() {
      this.$emit("update:maskName", false);
      this.$emit("selfCloseMask");
    },
    getTitle(item) {
      return this.lang.Select + " " + item.alias;
    },
    getStep(item) {
      return this.lang.step + " " + item.customIndex;
    },
    nextStepFun(name) {
      this.$emit("nextStepFun", name);
    },
    resetFontParam(type) {
      // 定义需要重置的字段
      const resetFields = {
        front: ["back", "around"], // 重置与 back 和 around 相关的字段
        frontback: ["around"], // 重置与 around 相关的字段
        around: ["front", "back", "around"], // 重置与 back 和 around 相关的字段
        all: ["front", "back", "inside", "around"], // 重置所有字段
      };

      // 获取需要重置的字段列表
      const fieldsToReset = resetFields[type] || resetFields.all;

      // 遍历需要重置的字段
      fieldsToReset.forEach((field) => {
        // 重置 textGroup
        this.copyCanvasData[`${field}TextGroup`] =
          this.defaultForm[`${field}TextGroup`];

        // 重置 position
        this.copyCanvasData[`${field}TextPositionX`] =
          this.defaultForm[`${field}TextPositionX`];
        this.copyCanvasData[`${field}TextPositionY`] =
          this.defaultForm[`${field}TextPositionY`];

        // 重置 clipartValue
        this.copyCanvasData[`${field}StartClipartValue`] =
          this.defaultForm[`${field}StartClipartValue`];
        this.copyCanvasData[`${field}EndClipartValue`] =
          this.defaultForm[`${field}EndClipartValue`];

        // 重置 img
        this.copyCanvasData[`${field}StartImg`] =
          this.defaultForm[`${field}StartImg`];
        this.copyCanvasData[`${field}EndImg`] =
          this.defaultForm[`${field}EndImg`];
      });
    },
  },
  mounted() {
    this.updateArrowPosition(0);
  },
};
</script>

<style scoped lang="scss">
.colorPickerGridBox {
  display: grid;
  grid-template-columns: repeat(auto-fill, 24px);
  grid-auto-rows: 24px;
  grid-gap: 5px;
  gap: 5px;
  justify-content: space-between;
  max-height: 268px;
  overflow-y: auto;

  > div {
    border: 1px solid transparent;

    &:hover {
      border-color: $color-primary;
    }
  }
}
.allImgDialogBox {
  display: grid;
  grid-template-columns: repeat(auto-fill, 150px);
  grid-auto-rows: 150px;
  gap: 10px;
  margin-top: 1rem;
  justify-content: space-around;

  @media screen and (max-width: 768px) {
    grid-template-columns: repeat(2, 1fr);
    justify-content: center;
    grid-auto-rows: auto;
  }

  .item {
    border: 1px solid #e6e6e6;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    background: url(data:image/png;base64,UklGRl4AAABXRUJQVlA4TFIAAAAvI8AIABcgEEj8YRthDoFs4g/7EJpANvGHfQht/gMrP7CKbKvNSS8SkIBKJERCb0cnf0RARP9Da1Yj/AdOe5LC61z2zLVmNUAeyy4GgNeZpP8B);
    box-shadow: 0 2px 4px rgb(0 0 0 / 12%), 0 0 6px rgb(0 0 0 / 4%);

    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }

    .btn {
      button {
        width: 40px;
        height: 40px;
      }

      position: absolute;
      display: flex;
      left: 0;
      top: 0;
      bottom: 0;
      right: 0;
      background-color: rgba(0, 0, 0, 0.4);
      justify-content: center;
      align-items: center;
      opacity: 0;
      transition: all 0.3s;

      b {
        width: 15px;
      }
    }
  }

  .item:hover {
    .btn {
      opacity: 1;
    }
  }
}
.stepWrap {
  margin-bottom: 20px;
  padding: 40px 30px 30px 30px;
  background: #ffffff;
  border-radius: 10px;
  position: relative;
  $bg: #afb1b3;
  $bgc: white;
  $bgc2: $color-primary;

  .closeIcon {
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: 700;
    position: absolute;
    top: 0;
    right: 0;
    transform: translate(50%, -50%);
    width: 40px;
    height: 40px;
    cursor: pointer;
    background: #ffffff;
    box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.3);
    border-radius: 50%;
    z-index: 10;

    @media screen and (max-width: 800px) {
      transform: translate(0, 0);
      box-shadow: none;
    }
  }

  &.active {
    z-index: 100;
  }

  .stepContent {
    align-items: flex-start;
    .tabs-nav {
      display: flex;
      .tab-item {
        padding: 8px 38px;
        border-radius: 6px;
        background: #efefef;
        border: 1px solid #e6e6e6;
        cursor: pointer;
        .product-info {
          display: flex;
          justify-content: center;
          align-items: center;
          .radio-beauty {
            width: 16px;
            height: 16px;
            min-width: 16px;
            box-sizing: border-box;
            display: inline-block;
            border: 1px solid $bg;
            vertical-align: middle;
            margin: 0 6px 0 3px;
            border-radius: 50%;
            background-color: $bgc;
            background-clip: content-box;
            position: relative;
            cursor: pointer;
            &::after {
              content: "";
              position: absolute;
              border-radius: 50%;
              left: 50%;
              top: 50%;
              transform: translate(-50%, -50%);
              width: 6px;
              height: 6px;
              background-color: $bg;
            }
            @media screen and (max-width: 767px) {
              margin: 0;
              width: 14px;
              height: 14px;
              min-width: 14px;
              margin-bottom: 5px;
              &::after {
                width: 5px;
                height: 5px;
              }
            }
          }
          .bTitle {
            font-weight: bold;
          }
        }
        @media screen and(max-width: 768px) {
          padding-inline: 14.5px;
          .product-info {
            .radio-beauty {
              margin-bottom: 0;
            }
          }
        }
        &.active {
          border-color: $color-primary !important;
          box-shadow: 0 3px 4px 0 #ccc;
          @media screen and (max-width: 767px) {
            box-shadow: none;
          }
          .product-info .radio-beauty {
            background-color: $color-primary;
            border-color: $color-primary;
            &::after {
              background-color: $bgc;
            }
          }
          .product-info .bTitle {
            color: $color-primary;
          }
        }
        &:not(:first-of-type) {
          margin-left: 10px;
        }
      }
    }

    .tabs-content {
      position: relative;
      background: white;
      border-radius: 8px;
      border: 1px solid #e6e6e6;
      margin-top: 15px;
      padding: 20px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      @media screen and(max-width: 768px) {
        padding: 6px;
      }
      .content-arrow {
        position: absolute;
        top: -10px;
        left: 50%;
        transform: translateX(-50%);
        width: 0;
        height: 0;
        border-left: 10px solid transparent;
        border-right: 10px solid transparent;
        border-bottom: 10px solid #e6e6e6; // 外框灰色

        // 白色填充层（覆盖中间部分）
        &::after {
          content: "";
          position: absolute;
          top: 1px; // 向下移动1px覆盖灰色边框
          left: -9px; // 调整位置对齐
          border-left: 9px solid transparent;
          border-right: 9px solid transparent;
          border-bottom: 9px solid white; // 内层白色
        }
      }

      // 内容区
      .pane {
        min-height: 100px; // 根据内容调整
        .insideText {
          .title {
          }
        }
        ::v-deep {
          .el-input__inner {
            height: 40px;
            background-color: #f5f5f5;
            font-size: 1em;
            font-family: Calibri;
            border: none;
            &:focus {
              border-color: var(--color-primary);
            }
          }
          .el-textarea__inner {
            background-color: #f5f5f5;
            border-radius: 6px;
            border: none;
            padding: 12px 10px;
          }
        }
        .line {
          margin-block: 15px;
          display: flex;
          justify-content: center;
          align-items: center;
          .z1 {
            width: 50%;
          }
          .z2 {
            width: 36%;
            margin-inline: 10px;
          }
          .z3 {
            width: 12%;
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            & > div {
              display: flex;
              justify-content: center;
              align-items: center;
              .inputStyleLike {
                .icon {
                  border-bottom: 2px solid var(--select-color, #d9d9d9);
                }
              }
            }
          }
          .z4 {
            width: 5%;
            margin-inline: 15px;
            i {
              color: var(--color-primary);
            }
          }

          @media screen and(max-width: 768px) {
            flex-wrap: wrap;
            margin-block: 10px;
            .z1 {
              width: 100%;
            }
            .z2 {
              margin-top: 5px;
              margin-inline: 0;
              width: 50%;
            }
            .z3 {
              width: 50%;
            }
          }
        }
        .clipart-grou {
          display: flex;
          @media screen and(max-width: 768px) {
            flex-direction: column;
          }
          .clipart {
            width: 30%;
            .fontBox {
              width: 100%;
              position: relative;

              .fontImg {
                position: absolute;
                width: 25px;
                height: 25px;
                right: 30px;
                top: 50%;
                transform: translateY(-50%);
                cursor: pointer;

                img {
                  width: 100%;
                  height: 100%;
                  object-fit: contain;
                  display: block;
                }
              }

              .el-select {
                width: 100%;
              }
            }
            &.end {
              margin-left: 28px;
              @media screen and(max-width: 768px) {
                margin-left: 0;
              }
            }
          }
          @media screen and(max-width: 768px) {
            flex-direction: column;
            .clipart {
              width: 60%;
            }
          }
        }
        .comments {
          width: 87%;
          @media screen and(max-width: 768px) {
            width: 100%;
          }
        }
        .title {
          font-weight: bold;
        }
        .icon {
          cursor: pointer;
          &.select {
            color: var(--color-primary);
          }
        }
        .upload-box {
          border-top: none;

          @media screen and (max-width: 768px) {
            display: block;
            .left-Box {
              width: 100% !important;
              margin-right: 0 !important;
            }
          }

          .upload-window {
            border: 1px dashed #cccccc;
            padding: 1em;
            background: #fafafa;
            border-radius: 6px;
            ul {
              padding: 0;
              margin-top: 10px;

              li {
                list-style-type: none;
                display: flex;
                justify-content: space-between;
                margin: 0.4em 0;
                font-size: 14px;

                .icon-box {
                  > i:nth-child(1) {
                    margin-right: 15px;
                    color: #39c464;
                  }

                  > i:nth-child(2) {
                    margin-right: 15px;
                    color: #cccccc;
                  }
                }
              }
            }

            .uploadLabel {
              display: flex;
              flex-direction: column;
              justify-content: center;
              align-items: center;
              height: 100%;
              border-radius: 4px;

              @media screen and (max-width: 768px) {
                padding: 6.5px 0;
              }

              .tips {
                font-size: 15px;
                color: #b3b3b3;
                text-align: center;
              }

              .upload-btn {
                display: flex;
                justify-content: center;
                align-items: center;
                .myIcon {
                  font-size: 1.5em;
                  color: var(--color-primary);
                  display: flex;
                  justify-content: center;
                  margin-bottom: 10px;
                }
                a {
                  margin-left: 0.5em;
                  color: var(--color-primary);
                  font-weight: bolder;
                  text-decoration: underline;
                }
              }
              .info {
                font-size: 16px;
                color: #999999;
                text-align: center;
                margin: 5px 0 15px;

                @media screen and (max-width: 768px) {
                  font-size: 12px;
                  font-family: Arial;
                  margin: 10px 0;
                  line-height: 16px;
                }
              }
            }
          }

          .comments {
            margin-top: 15px;
            @media screen and (max-width: 768px) {
              margin-top: 20px;
            }

            > div:nth-child(1) {
              font-size: 16px;
              color: #333333;
              line-height: 1em;
              margin-bottom: 10px;

              @media screen and (max-width: 768px) {
                font-size: 12px;
                font-family: Arial;
                color: #666666;
              }
            }

            > div:nth-child(2) {
              font-size: 14px;
              font-weight: 400;
              color: #999999;
              margin-bottom: 20px;
              line-height: 1em;

              @media screen and (max-width: 768px) {
                font-size: 12px;
                font-family: Arial;
                color: #999999;
                margin-bottom: 10px;
              }
            }

            .el-textarea__inner {
              min-height: 33px !important;

              @media screen and (max-width: 768px) {
                min-height: 140px !important;
              }
            }

            .el-textarea__inner:focus {
              border-color: $color-primary;
            }
          }
        }
        .email-later {
          .notes {
            border-radius: 6px;
            background-color: #f5f5f5;
            font-size: 1.25em;
            font-weight: bold;
            padding: 1em 0.75em;
            @media screen and(max-width: 768px) {
              font-size: 1em;
            }
            .click_text {
              color: #3ca1e6;
            }
          }
          .comments {
            margin-top: 15px;
          }
        }
      }
    }
    &.hasViewMoreHeight {
      max-height: 520px;
      overflow: hidden;
    }

    @include respond-to(mb) {
      grid-template-columns: repeat(2, 1fr);
    }
  }
}

.stepFooter {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 30px;
}
</style>