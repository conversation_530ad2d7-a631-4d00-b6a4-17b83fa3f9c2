import customRoutes from "./router/index.js";
const env = require("./env");
const optimization =
	process.env.NODE_ENV === "production"
		? {
				usedExports: true,
				minimize: true,
				splitChunks: {
					chunks: "all",
					maxSize: 244 * 1024,
					minSize: 30 * 1024,
					automaticNameDelimiter: "-",
					name: true,
					cacheGroups: {
						lang: {
							test: /assets[\\/]lang[\\/]/,
							priority: 30,
							chunks: "all",
						},
						vendors: {
							test: /[\\/]node_modules[\\/]/, // 匹配 node_modules 中的模块
							priority: 20,
						},
						extracted: {
							priority: 10,
							minChunks: 2,
							reuseExistingChunk: true,
						},
					},
				},
		  }
		: {};

module.exports = {
	ssr: true,
	target: "server",
	head: {
		__dangerouslyDisableSanitizers: ["script", "noscript", "style"], //不转义script标签的内容,用于结构化数据
		meta: [{ charset: "utf-8" }, { name: "viewport", content: "width=device-width, initial-scale=1, user-scalable=yes, viewport-fit=cover" }],
		link: [
			{
				//多色-O2O、经销商后台
				rel: "stylesheet",
				href: "//at.alicdn.com/t/c/font_3500115_hwb28bsfq3n.css",
				body: true,
			},
			{
				//单色-O2O、经销商后台
				rel: "stylesheet",
				href: "//at.alicdn.com/t/c/font_3500108_bxucohroob7.css",
				body: true,
			},
			{
				rel: "stylesheet",
				href: "https://cdnjs.cloudflare.com/ajax/libs/MaterialDesign-Webfont/6.9.96/css/materialdesignicons.min.css",
				body: true,
			},
			{
				rel: "stylesheet",
				href: "https://fonts.googleapis.com/css2?family=Acme&family=Amaranth&family=Anton&family=Boogaloo&family=Chewy&family=Concert+One&family=Courgette&family=Fjalla+One&family=Francois+One&family=Fredoka+One&family=Fugaz+One&family=Great+Vibes&family=Hanalei+Fill&family=Kaushan+Script&family=Lobster+Two&family=MedievalSharp&family=Oleo+Script+Swash+Caps&family=Oswald:wght@200..700&family=Pacifico&family=Palanquin+Dark&family=Passion+One&family=Permanent+Marker&family=Ranga&family=Righteous&family=Roboto&family=Roboto+Flex:opsz,wght@8..144,100..1000&family=Ruslan+Display&family=Sigmar+One&family=Timmana&family=Viga&family=Quicksand&display=swap",
				body: true,
			},
			{
				rel: "stylesheet",
				href: "https://static-oss.gs-souvenir.com/static/css/cookieconsent.css",
				body: true,
			},
			{
				rel: "stylesheet",
				href: "https://static-oss.gs-souvenir.com/web/quoteManage/20241015/video_2049XMTR46.css",
				body: true,
			},
			{
				rel: "stylesheet",
				href: "https://static-oss.gs-souvenir.com/web/quoteManage/20241015/swiper_2049TcHSbt.css",
				body: true,
			},
		],
		script: [{ src: "/js/hmac-sha256.js" }, { src: "/js/enc-base64-min.js" }, { src: "https://static-oss.gs-souvenir.com/web/quoteManage/20241015/video_2049JasChi.js" }, { src: "https://static-oss.gs-souvenir.com/web/quoteManage/20241015/swiper_20493k6MyM.js" }],
	},
	static: { prefix: false },
	css: [{ src: "~/assets/css/index.scss", lang: "scss" }, { src: "~/assets/css/font.scss" }, { src: "~/assets/css/lanyard/font.css" }, { src: "~/assets/css/theme.scss" }],
	styleResources: { scss: ["~/assets/css/var.scss"] },
	// nuxt.config.js
	buildModules: [
		// 和选项一起
		[
			"@nuxtjs/vuetify",
			{
				customVariables: ["~/assets/css/variables.scss"],
				defaultAssets: false,
				treeShake: process.env.NODE_ENV === "production",
			},
		],
	],

	plugins: ["~/plugins/axios", "~/plugins/directive", { src: "~/plugins/interCom", mode: "client" }, { src: "~/plugins/globalBus", mode: "client" }, { src: "~/plugins/cropper.js", mode: "client" }, { src: "~/plugins/globalMethods.js" }, { src: "~/plugins/loading", mode: "client" }, { src: "~/plugins/device" }],

	//待继续优化
	components: [
		{
			path: "~/components",
			ignore: ["analysis/*", "analysismain/*", "buildWeb/*", "Cufflinks/*", "Dashboard/*", "Dialog/*", "Invoice/*", "Nav/*", "PreviewWeb/*", "Product/*", "Set/*", "ThreeDModule/*", "tools/*", "vuetify/*", "Lanyard/*", "lapelPins/*", "Medals/*", "MyDzxt/*", "NeonAi/*", "NeonDzxt/*", "QuickQuote/*", "Quote/*", "semiMedals/*", "semiquote/*","DesignerDetail.vue"],
		},
	],
	// components: true,

	loading: {
		color: "#C19952",
		continuous: true,
		duration: 3000,
	},

	modules: ["@nuxtjs/axios", "@nuxtjs/style-resources", "@nuxtjs/toast", "cookie-universal-nuxt"],
	serverMiddleware: [{ path: "/robots.txt", handler: "~/serverMiddleware/robots.js" }, "~/serverMiddleware/sitemap.js"],
	server: {
		port: 3003, // default: 3000
		host: "0.0.0.0", // default: localhost,
		timing: false,
	},
	toast: {
		containerClass: "toast-box",
		position: "top-center",
		duration: "3000",
		theme: "outline",
		keepOnHover: true,
		singleton: true,
	},

	render: {
		resourceHints: false, //启用资源预加载
	},

	router: {
		middleware: ["global"],
		prefetchLinks: true,
		extendRoutes(routes, resolve) {
			routes.unshift(...customRoutes);
			//移除自动生成的路由
			let removeRouteNameArr = ["lang-design-systemName", "lang-enamelpinsProductList"];
			routes = routes.filter((route) => {
				return !removeRouteNameArr.includes(route.name);
			});
			// 排除掉以manage开头的路由
			routes = routes.filter((route) => !route.name.startsWith("manage"));
			return routes;
		},
	},

	env: {
		...env[process.env.MODE],
		clientId: "AWzqx3sFPqf9ValR6YHxRxhW8aCc45nxcm6vqsA3s6HW9rU-rbRgQkKOye7I7osRqUo-2-Wslg71CqRN",
		dataPartnerAttributionId: "FLAVORsb-gr10b16985777_MP",
		payId: 10000,
	},
	vue: {
		config: {
			productionTip: true,
		},
	},
	//打包优化
	build: {
		cache: false, //压缩JS和缓存其他loaders的处理结果。
		hardSource: false, //模块缓存
		parallel: true,
		analyze: false,
		extractCSS: false,
		// //分割vendor.app.js文件(打包优化)
		optimization,
		extend(config, { isDev, isClient }) {
			if (!isDev && isClient) {
				config.optimization.splitChunks.maxSize = 244 * 1024;
				config.optimization.minimizer[0].options.terserOptions.compress.drop_console = true;
			}
			if (!isDev) {
				// 禁用 Source Map
				config.devtool = false;
			}
		},
		babel: {
			compact: false,
			plugins: [
				[
					"component",
					{
						libraryName: "element-ui",
						styleLibraryName: "theme-chalk",
					},
				],
			],
		},
		loaders: {
			scss: {
				sassOptions: {
					// 静默所有弃用警告
					quietDeps: true,
				},
			},
		},
	},
};
