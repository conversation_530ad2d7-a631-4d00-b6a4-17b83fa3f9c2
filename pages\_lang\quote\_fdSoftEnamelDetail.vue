<template>
	<div v-loading="baseModel.loading" class="quoteFdSoftEnamelLapelpinDetail">
		<div class="main">
			<div class="back" @click="back()">
				{{ lang.fd.detailObject.back }}
				<b class="iconfont icon-a-fanhui1" />
			</div>
			<div class="title">{{ lang.fd.detailTitle }}</div>
			<div class="nav">
				<div class="li liInput">
					<el-input v-model="quantityModel.custQuantity" placeholder="Enter Qty " type="number" clearable oninput="if(value.length>7)value=value.slice(0,7)" @input="handleInput" />
				</div>
				<div v-for="(item, index) in quantityModel.list" :key="index" class="li" :class="item.checked ? 'liOn' : ''" @click="quantityTabs(item)">
					<el-checkbox v-model="item.checked" class="box" :disabled="item.disabled" @change="quantityTabs(item)" />
					{{ item.quantity || 0 }}
				</div>
				<!--上一个页面自定义数量框-->
				<div v-if="baseModel.quantity" class="li liOn">
					<el-checkbox v-model="baseModel.quantityChecked" class="box" disabled />
					{{ baseModel.quantity || 0 }}
				</div>
			</div>
			<div v-if="quantityModel.custQuantity < quantityModel.custQuantityMsg && quantityModel.custQuantity > 0" class="navMsg">
				{{ lang.fd.navMsg.p1 }}{{ getUnitFunc(tableListModel.list) }}{{ get168() }}.<br />
				({{ lang.fd.navMsg.p2 }}.:{{ quantityModel.custQuantityMsg }})
			</div>
			<div class="printConsole">
				<div class="title">
					<div class="left leftTit">{{ lang.fd.productDetail.title }}</div>
					<div class="left right">
						<div class="btn modifyPrice">
							<!-- <b v-if="!isMobile" class="iconfont icon-icon-xiugaijiage" /> -->
							{{ lang.fd.detailObject.editPrice }}
							<el-tooltip class="item modifyPriceIcon" effect="light" placement="top">
								<div slot="content" v-html="lang.fd.productDetail.modifyPrice" />
								<b v-if="!isMobile" class="iconfont icon-wenhao2" />
								<b v-if="isMobile" class="iconfont icon-wenhao1 wenhaoIcon" style="color: #515151; font-size: 18px; font-weight: 700 !important" />
							</el-tooltip>
						</div>

						<div class="btn btnInput" style="margin-left: 15px">
							<el-input v-model="baseModel.rate" class="input" type="number" placeholder="Put Discount Percentage" @input="rateInput" />
							<el-tooltip v-if="!isMobile" class="item btnInputIcon" placement="top" effect="light">
								<div slot="content" v-html="lang.fd.productDetail.putDiscountPercentage" />
								<b class="iconfont icon-wenhao2" />
							</el-tooltip>
						</div>
						<div class="btn percentage">%</div>
						<el-tooltip v-if="isMobile" class="item btnInputIcon" placement="top" effect="light">
							<div slot="content" v-html="lang.fd.productDetail.putDiscountPercentage" />
							<b class="iconfont icon-wenhao1 wenhaoIcon" style="color: #515151; font-size: 16px; font-weight: 700 !important" />
						</el-tooltip>
						<div class="buttonBtn">
							<div class="btn apply" @click="resetFunc()">
								{{ lang.fd.detailObject.reset }}
								<el-tooltip class="item applyIcon" effect="light" placement="top">
									<div slot="content" v-html="lang.fd.productDetail.reset" />
									<b v-if="!isMobile" class="iconfont icon-wenhao2" />
									<b v-if="isMobile" class="iconfont icon-wenhao1 wenhaoIcon" style="color: #515151; font-size: 16px; font-weight: 700 !important" />
								</el-tooltip>
							</div>
							<!-- <div class="btn apply modifyPriceSave" @click="saveFunc()">
								<b class="iconfont icon-AK-MN_jisuanqi_fill" />
								Save
								<el-tooltip class="item applyIcon" effect="light" placement="top">
									<div slot="content" v-html="lang.fd.productDetail.save" />
									<b class="iconfont icon-wenhao2" style="font-size: 16px; margin-left: 3px" />
								</el-tooltip>
							</div> -->
						</div>
					</div>
				</div>
				<div :ref="baseModel.refs">
					<!--list集合-->
					<div v-for="(item, index) in tableListModel.list" :key="'tableList1' + index" class="tableList">
						<table class="table">
							<tr class="th">
								<td>Catagory Picture</td>
								<td>Product Description</td>
								<td style="min-width: 50px">Qty</td>
								<td>Unit Price</td>
								<td style="min-width: 50px">Total</td>
							</tr>
							<tr>
								<td :rowspan="getRowSpan(item)" class="tdImg">
									<img :src="item.img" />
								</td>
								<td class="tdContent">
									<!--步骤遍历-->
									<div v-for="(j, jIndex) in item.list" :key="'list' + jIndex">
										<div v-if="j.parentObject.paramType == 'SIZE'" class="rowOne">{{ j.object?.paramCode }} {{ item.desc }}</div>
										<div v-if="j.parentObject.paramType == 'SIZE'" class="rowOne"><span class="fontB">Item No.:</span> {{ j.object?.priceInfo?.fdItemNo }}</div>
										<div v-if="j.parentObject.paramType != 'SIZE'" class="rowOne">
											<span class="fontB">{{ j.label }}</span>
											{{ j.value }}
										</div>
									</div>
								</td>
								<td>{{ item.moneyModel?.quantity }}</td>
								<td>
									<div class="unitPriceDiv">
										{{ item.moneyModel?.unit }}
										{{ getUnitPrice(item?.moneyModel?.unitPrice, item.moneyModel.rateInput) }}
									</div>
								</td>
								<td>{{ item.moneyModel?.unit }}{{ getSubtotal(item) }}</td>
							</tr>
							<tr>
								<!-- 	<td></td> -->
								<td class="tdContent">
									Mold Fee
									<span v-if="isTypeQUR(tableListModel.list[0]) && !isMobile" class="blue">(Please contact your sales consultant for fees)</span>
								</td>
								<td>1</td>
								<td>
									<div class="unitPriceDiv">
										{{ item.moneyModel?.unit }}
										{{ getUnitPrice(item?.moneyModel?.moldFee, item.moneyModel.rateInput) }}
									</div>
								</td>
								<td>{{ item.moneyModel?.unit }}{{ getMoldFeeTotal(item.moneyModel.moldFee) }}</td>
							</tr>
							<!--输入框数组遍历-->
							<tr v-for="(j, jIndex) in item.inputList" :key="'inputList' + jIndex">
								<td class="tdContent">{{ j.label }} {{ j.value }}</td>
								<td v-if="!j.setUpFee">
									{{ item.moneyModel?.quantity }}
								</td>
								<td v-if="j.setUpFee">
									{{ j.quantity }}
								</td>
								<td class="tdLeft">
									{{ item.moneyModel?.unit }}
									{{ getUnitPrice(j.price, item.moneyModel.rateInput) }}

									<span style="color: #666; margin-left: 5px">x{{ j.quantity || 0 }}</span>
								</td>
								<td v-if="!j.setUpFee">{{ item.moneyModel?.unit }}{{ getInputListTotal(j, item.moneyModel?.quantity) }}</td>
								<td v-if="j.setUpFee">{{ item.moneyModel?.unit }}{{ getInputListTotal(j, j.quantity) }}</td>
							</tr>
							<!--不是输入框遍历-->
							<tr v-for="(j, jIndex) in item.dataList" :key="'dataList' + jIndex">
								<td class="tdContent">
									{{ j.label }} {{ j.value }}
									<span v-if="isTypeQUR(tableListModel.list[0]) && !isMobile" class="blue">(Please contact your sales consultant for fees)</span>
									<span v-if="j.setUpFee"> - {{ j.setUpFee }}</span>
								</td>
								<td v-if="!j.setUpFee">{{ item.moneyModel?.quantity }}</td>
								<td v-if="j.setUpFee">
									{{ j.quantity }}
								</td>
								<td class="tdLeft">
									{{ item.moneyModel?.unit }}
									{{ getUnitPrice(j.price, item.moneyModel.rateInput) }}
								</td>

								<td v-if="!j.setUpFee">{{ item.moneyModel?.unit }}{{ getDataListTotal(j, item.moneyModel?.quantity) }}</td>
								<td v-if="j.setUpFee">{{ item.moneyModel?.unit }}{{ getDataListTotal(j, j.quantity) }}</td>
							</tr>
						</table>
						<div class="total">
							<div class="copywriting">
								<div v-if="baseModel.data.prodTime && baseModel.data.enableProdTime == '1'" class="p3">{{ baseModel.data.prodTime }}</div>
								<div v-if="baseModel.data.rushTime && baseModel.data.enableRushService == '1'" class="p3">{{ baseModel.data.rushTime }}</div>
								<div class="p1" :class="isTypeQUR(tableListModel.list[0]) ? 'p22' : ''">{{ lang.fd.font.h1 }}:</div>
								<div :class="isTypeQUR(tableListModel.list[0]) ? 'p22' : 'p2'">{{ lang.fd.font.h2 }}</div>
								<div class="p3" v-if="!isTypeQUR(tableListModel.list[0])">{{ lang.fd.font.h3 }}</div>
								<div class="p3" :class="isTypeQUR(tableListModel.list[0]) ? 'p33' : ''">{{ lang.fd.font.h4 }}</div>
								<div class="p2" v-if="!isTypeQUR(tableListModel.list[0])">{{ lang.fd.font.h5 }}</div>
								<div class="p3" v-if="!isTypeQUR(tableListModel.list[0])">{{ lang.fd.font.h6 }}</div>
							</div>
							<div class="right">
								<div class="liTop">
									<div class="p1">{{ lang.fd.detailObject.finalPrice }}:</div>
									<div class="p2">{{ item.moneyModel?.unit }}{{ getTotal(item, true) }}</div>
								</div>
								<div v-if="isTypeQUR(tableListModel.list[0])" class="liBottom">
									<div class="p1">{{ lang.fd.detailObject.QUR1 }}:</div>
									<div class="p2"><span>◆</span> {{ lang.fd.detailObject.QUR2 }}.</div>
									<div class="p2"><span>◆</span> {{ lang.fd.detailObject.QUR3 }}.</div>
								</div>
							</div>
						</div>
					</div>
					<!--copy自定义数量-->
					<div v-if="quantityModel.custQuantity && quantityModel.custQuantity >= quantityModel.custQuantityMsg" v-for="(item, index) in baseModel.copyList" :key="'tableList2' + index" class="tableList">
						<table class="table">
							<tr class="th">
								<td>Catagory Picture</td>
								<td>Product Description</td>
								<td style="min-width: 50px">Qty</td>
								<td>Unit Price</td>
								<td style="min-width: 50px">Total</td>
							</tr>
							<tr>
								<td :rowspan="getRowSpan(item)" class="tdImg">
									<img :src="item.img" />
								</td>
								<td class="tdContent">
									<!--步骤遍历-->
									<div v-for="(j, jIndex) in item.list" :key="'list' + jIndex">
										<div v-if="j.parentObject.paramType == 'SIZE'" class="rowOne">{{ j.object?.paramCode }} {{ item.desc }}</div>
										<div v-if="j.parentObject.paramType == 'SIZE'" class="rowOne"><span class="fontB">Item No.:</span> {{ j.object?.priceInfo?.fdItemNo }}</div>
										<div v-if="j.parentObject.paramType != 'SIZE'" class="rowOne">
											<span class="fontB">{{ j.label }}</span>
											{{ j.value }}
										</div>
									</div>
								</td>
								<td>{{ item.moneyModel?.quantity }}</td>
								<td>
									<div class="unitPriceDiv">
										{{ item.moneyModel?.unit }}
										{{ getUnitPrice(item?.moneyModel?.unitPrice, item.moneyModel.rateInput) }}
									</div>
								</td>
								<td>{{ item.moneyModel?.unit }}{{ getSubtotal(item) }}</td>
							</tr>
							<tr>
								<td class="tdContent">
									Mold Fee
									<span v-if="isTypeQUR(tableListModel.list[0]) && !isMobile" class="blue">(Please contact your sales consultant for fees)</span>
								</td>
								<td>1</td>
								<td>
									<div class="unitPriceDiv">
										{{ item.moneyModel?.unit }}
										{{ getUnitPrice(item?.moneyModel?.moldFee, item.moneyModel.rateInput) }}
									</div>
								</td>
								<td>{{ item.moneyModel?.unit }}{{ getMoldFeeTotal(item.moneyModel.moldFee) }}</td>
							</tr>
							<!--输入框数组遍历-->
							<tr v-for="(j, jIndex) in item.inputList" :key="'inputList' + jIndex">
								<td class="tdContent">{{ j.label }} {{ j.value }}</td>
								<td v-if="!j.setUpFee">
									{{ item.moneyModel?.quantity }}
								</td>
								<td v-if="j.setUpFee">
									{{ j.quantity }}
								</td>

								<td class="tdLeft">
									{{ item.moneyModel?.unit }}
									{{ getUnitPrice(j.price, item.moneyModel.rateInput) }}
									<span style="color: #666; margin-left: 5px">x{{ j.quantity || 0 }}</span>
								</td>
								<td v-if="!j.setUpFee">{{ item.moneyModel?.unit }}{{ getInputListTotal(j, item.moneyModel?.quantity) }}</td>
								<td v-if="j.setUpFee">{{ item.moneyModel?.unit }}{{ getInputListTotal(j, j.quantity) }}</td>
							</tr>
							<!--不是输入框遍历-->
							<tr v-for="(j, jIndex) in item.dataList" :key="'dataList' + jIndex">
								<td class="tdContent">
									{{ j.label }} {{ j.value }}
									<span v-if="isTypeQUR(tableListModel.list[0]) && !isMobile" class="blue">(Please contact your sales consultant for fees)</span>
								</td>
								<td v-if="!j.setUpFee">
									{{ item.moneyModel?.quantity }}
								</td>
								<td v-if="j.setUpFee">
									{{ j.quantity }}
								</td>

								<td class="tdLeft">
									{{ item.moneyModel?.unit }}
									{{ getUnitPrice(j.price, item.moneyModel.rateInput) }}
								</td>

								<td v-if="!j.setUpFee">{{ item.moneyModel?.unit }}{{ getDataListTotal(j, item.moneyModel?.quantity) }}</td>
								<td v-if="j.setUpFee">{{ item.moneyModel?.unit }}{{ getDataListTotal(j, j.quantity) }}</td>
							</tr>
						</table>
						<div class="total">
							<div class="copywriting">
								<div v-if="baseModel.data.prodTime && baseModel.data.enableProdTime == '1'" class="p3">{{ baseModel.data.prodTime }}</div>
								<div v-if="baseModel.data.rushTime && baseModel.data.enableRushService == '1'" class="p3">{{ baseModel.data.rushTime }}</div>
								<div class="p1" :class="isTypeQUR(tableListModel.list[0]) ? 'p22' : ''">{{ lang.fd.font.h1 }}:</div>
								<div :class="isTypeQUR(tableListModel.list[0]) ? 'p22' : 'p2'">{{ lang.fd.font.h2 }}</div>
								<div class="p3" v-if="!isTypeQUR(tableListModel.list[0])">{{ lang.fd.font.h3 }}</div>
								<div class="p3" :class="isTypeQUR(tableListModel.list[0]) ? 'p33' : ''">{{ lang.fd.font.h4 }}</div>
								<div class="p2" v-if="!isTypeQUR(tableListModel.list[0])">{{ lang.fd.font.h5 }}</div>
								<div class="p3" v-if="!isTypeQUR(tableListModel.list[0])">{{ lang.fd.font.h6 }}</div>
							</div>
							<div class="right">
								<div class="liTop">
									<div class="p1">{{ lang.fd.detailObject.finalPrice }}:</div>
									<div class="p2">{{ item.moneyModel?.unit }}{{ getTotal(item, true) }}</div>
								</div>
								<div v-if="isTypeQUR(tableListModel.list[0])" class="liBottom">
									<div class="p1">{{ lang.fd.detailObject.QUR1 }}:</div>
									<div class="p2"><span>◆</span> {{ lang.fd.detailObject.QUR2 }}.</div>
									<div class="p2"><span>◆</span> {{ lang.fd.detailObject.QUR3 }}.</div>
								</div>
							</div>
						</div>
					</div>
				</div>

				<div class="bottomBtn" :class="{ bottomBtn__right: !isMobile }">
					<!--pc端-->
					<div v-if="!isMobile" class="btn download" @click="cutpic()">
						{{ lang.fd.detailObject.download }}
					</div>
					<div v-if="!isMobile" class="btn imprint" @click="print()">
						{{ lang.fd.detailObject.imprint }}
					</div>
					<!--手机端-->
					<div v-if="isMobile" class="btn imprint" @click="cutpic()">{{ lang.fd.detailObject.download }}</div>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import "@/plugins/element";
import { quantityModel, tableListModel, baseModel, totalModel } from "./entity/_fdSoftEnamelDetail";
import { Common } from "@/utils/common";
import { DataProcessing } from "@/utils/dataProcessing";
import { saveQuoteRecord, downloadFdPdf, getCateFdRushAndTimeStatus } from "@/api/quote/fdCommon";

export default {
	head() {
		return {};
	},
	components: {},
	mixins: [],
	computed: {
		lang() {
			return this.$store.getters.lang.quote || {};
		},
		isMobile() {
			return this.$store.getters.isMobile;
		},
	},
	watch: {
		"$store.state.currency": {
			handler(newValue) {
				console.log("好好好", newValue);
			},
			immediate: true,
		},
	},

	data() {
		return {
			input: 0,
			quantityModel: new quantityModel(),
			tableListModel: new tableListModel(),
			baseModel: new baseModel(),
			totalModel: new totalModel(),
			loading: false,
		};
	},
	beforeDestroy() {
		console.log("页面即将销毁");
		//window.sessionStorage.removeItem(Constant.DETAILREDIRECT);
	},
	destroyed() {
		console.log("页面已销毁");
		//window.sessionStorage.removeItem(Constant.DETAILREDIRECT);
	},
	mounted() {
		if (process.client) {
			Common.init();
		}
		this.init();
		/* 		setTimeout(() => {
			console.log("高级呼啦啦", this.tableListModel.list);
		}, 5000); */
	},
	methods: {
		init() {
			if (sessionStorage) {
				let query = sessionStorage.getItem(this.baseModel.constantQuery);
				if (query) {
					query = JSON.parse(query);

					//query.moneyModel.unitPrice = 1;
					console.log("我在检查", query);
					/* if (query.moneyModel?.sellQuantity == null) {
						query.moneyModel.sellQuantity = this.totalModel.sellQuantity;
					} */
					this.dataAppend(query);
					this.tableListModel.listCopy = this.tableListModel.list;
					this.getCateFdRushAndTimeStatusFunc(query);
					this.isRec();
				}
			}
		},
		//开启递归
		isRec() {
			if (this.tableListModel.list.length > 0) {
				this.tableListModel.list.map((item) => {
					this.getTotal(item);
				});
			}
		},
		//获取2个时间字段
		getCateFdRushAndTimeStatusFunc(query) {
			getCateFdRushAndTimeStatus({}).then((res) => {
				if (res) {
					this.baseModel.data = {
						...query.moneyModel.data,
						...res.data,
					};
				}
			});
		},
		//取列表页面货币单位
		getUnitFunc(e) {
			if (e && e.length > 0) {
				return e[0]?.moneyModel?.unit;
			} else {
				return "$";
			}
		},
		//数据拼接
		dataAppend(query) {
			console.log("吉吉1");
			if (query?.moneyModel?.increaseMap) {
				query.moneyModel.increaseMapJson = DataProcessing.getObjectArray(query.moneyModel.increaseMap);
			}
			if (query?.moneyModel?.setupFeeMap) {
				query.moneyModel.setupFeeMapJson = DataProcessing.getObjectArray(query.moneyModel.setupFeeMap);
			}
			console.log("吉吉2");
			let arr = [];
			if (query.list.length > 0) {
				//过滤其他步骤
				arr.push(query.list[0]);
			}
			//价格组装
			query.moneyModel?.increaseMapJson.map((i) => {
				query.moneyModel?.paramIdMapping.map((j) => {
					if (i.key == j.priceInfoId) {
						j.price = i.value;
					}
				});
			});
			console.log("吉吉3");
			query.inputList = []; //输入框数据源
			query.dataList = []; //不是输入框数据源
			query.moneyModel.paramIdMapping.map((i) => {
				if (i.quantity) {
					//输入框数据源
					this.countList(i, arr, query, "inputList");
				} else {
					//不是输入框数据源
					this.countList(i, arr, query, "dataList");
				}
			});
			if (!this.isTypeQUR(query)) {
				query.list = arr;
			}
			console.log("吉吉4");
			this.baseModel.quantity = query.moneyModel.quantity;
			this.quantityModel.list.map((item, index) => {
				if (item.quantity == this.baseModel.quantity) {
					this.quantityModel.list.splice(index, 1);
				}
			});
			this.baseModel.rate = query.moneyModel.rate * 100.0; //DataProcessing.floatRide(query.moneyModel.rate, 100);
			this.baseModel.initQuantity = query.moneyModel.quantity;
			this.tableListModel.list.push(query);
			let setupFeeMapArray = DataProcessing.getObjectArray(query.moneyModel.setupFeeMap);
			setupFeeMapArray.map((i) => {
				query.moneyModel.paramIdMapping.map((j) => {
					if (j.priceInfoId == i.key) {
						j.price = i.value;
						this.tableListModel.list[0].dataList.push({
							label: j.stepName,
							price: j.price,
							quantity: 1,
							value: j.paramName,
							setUpFee: "Set Up Fee", //设置费
						});
					}
				});
			});
			console.log("吉吉5");
			query.moneyModel.packingIdListJson.map((item) => {
				item.label = item.label + ":";
				this.tableListModel.list[0].dataList.push(item);
			});
			//去重
			const jsonArr = this.tableListModel.list[0].dataList;
			const deduplicatedArr = jsonArr.filter((obj, index, arr) => {
				return arr.findIndex((o) => o.label === obj.label && o.value === obj.value) === index;
			});
			deduplicatedArr.map((item, index) => {
				if (isNaN(item.price)) {
					deduplicatedArr.splice(index, 1);
				} else {
					item.price = parseFloat(item.price).toFixed(2);
				}
			});
			console.log("吉吉6");
			this.tableListModel.list[0].dataList = deduplicatedArr;
			query.moneyModel.sellQuantity = this.get168();
			console.log("吉吉7");
		},
		/**
		 * 判断类别 是否是QUR(霓虹灯)
		 * 参数1：当前数据
		 */
		isTypeQUR(obj) {
			if (obj.moneyModel.type == "QUR") {
				return true;
			} else {
				return false;
			}
		},
		//计算数组
		countList(i, arr, query, e) {
			if (i.price == "Free" || i.price == "free") {
				//丢上去
				arr.push({
					label: i.stepName + ":",
					value: i.paramName,
					object: {
						paramCode: "",
						priceInfo: {
							fdItemNo: undefined,
						},
					},
					parentObject: {
						paramType: "NORMAL",
					},
				});
			} else {
				//丢下来
				query[e].push({
					quantity: i.quantity,
					price: i.price,
					label: i.stepName + ":",
					value: i.paramName,
				});
			}
			return arr;
		},

		//数量选项卡
		quantityTabs(item) {
			console.log("返工", item);
			if (this.tableListModel.list?.length > 0) {
				let thisObj = JSON.parse(JSON.stringify(this.tableListModel.list[0])); //解决数据污染

				if (thisObj.moneyModel.quantity != item.quantity) {
					item.checked = !item.checked;
					if (item.checked) {
						//复制
						let copyObj = JSON.parse(JSON.stringify(thisObj)); //解决数据污染
						copyObj.moneyModel.quantity = item.quantity;
						this.tableListModel.list.push(copyObj);

						if (this.baseModel.copyList.length > 0) {
							//有相同则清空
							if (this.baseModel.copyList[0].moneyModel.quantity == copyObj.moneyModel.quantity) {
								this.baseModel.copyList = new baseModel().copyList;
								this.quantityModel.custQuantity = undefined;
							}
						}
					} else {
						let arr = [];
						this.tableListModel.list.map((i) => {
							if (i.moneyModel.quantity != item.quantity) {
								//移除
								arr.push(i);
							}
						});
						this.tableListModel.list = arr;
					}
					this.$forceUpdate();
				}
			}
		},
		//单元格行合并
		getRowSpan(item) {
			return item.dataList.length + item.inputList.length + 2;
		},

		//打印
		print() {
			this.baseModel.loading = true;
			let fileName = "demo" + +new Date();
			Common.cutpic(this, document, false, false, fileName, this.refs, (base64, blob) => {
				let m = {
					fileName: fileName + ".png",
					base64Str: undefined,
				};

				let baseArr = base64.split("data:image/png;base64,");
				if (baseArr.length > 1) {
					m.base64Str = baseArr[1];
				} else {
					m.base64Str = baseArr[0];
				}
				this.baseModel.loading = false;
				var newWindow = window.open();
				downloadFdPdf(m).then((res) => {
					if (res) {
						newWindow.location.href = res.data;
					}
				});
			});
		},
		handleInput() {
			this.quantityModel.custQuantity = this.quantityModel.custQuantity.replace(/\D/g, "");
			//准备去重
			let op = false;
			this.tableListModel.list.map((item) => {
				if (item.moneyModel.quantity == this.quantityModel.custQuantity) {
					op = true;
				}
			});
			if (!op) {
				if (this.tableListModel.list?.length > 0) {
					this.baseModel.copyList = new baseModel().copyList;
					let copyObj = JSON.parse(JSON.stringify(this.tableListModel.list[0])); //解决数据污染
					copyObj.moneyModel.quantity = this.quantityModel.custQuantity;
					this.baseModel.copyList.push(JSON.parse(JSON.stringify(copyObj))); //解决数据污染
				}
			} else {
				this.baseModel.copyList = new baseModel().copyList; //重复去从
			}
		},
		// canvas画图
		cutpic() {
			this.baseModel.loading = true;
			setTimeout(() => {
				Common.cutpic(this, document, true, false, "demo" + +new Date(), this.baseModel.refs, (res) => {
					this.baseModel.loading = false;
				});
			}, 1000);
		},
		//汇率输入框
		rateInput(e) {
			let arr = this.tableListModel.list[0];
			if (arr) {
				arr.dataList.map((item, index) => {
					item.rateInput = this.baseModel.rate / 100;
				});
				arr.moneyModel.rateInput = this.baseModel.rate / 100;
				arr.inputList.map((item, index) => {
					item.rateInput = this.baseModel.rate / 100;
				});
			}

			let arrCopy = this.baseModel.copyList[0];
			if (arrCopy) {
				arrCopy.dataList.map((item, index) => {
					item.rateInput = this.baseModel.rate / 100;
				});
				arrCopy.moneyModel.rateInput = this.baseModel.rate / 100;
				arrCopy.inputList.map((item, index) => {
					item.rateInput = this.baseModel.rate / 100;
				});
			}

			console.log(this.tableListModel.list[0], this.baseModel.copyList);
		},
		//获取单价
		getUnitPrice(unitPrice = 0, rateInput = 1) {
			let sum = DataProcessing.floatRide(unitPrice, rateInput).toFixed(2);
			if (this.isTypeQUR(this.tableListModel.list[0])) {
				return this.tableListModel.list[0]?.moneyModel?.type;
			} else {
				return sum;
			}
		},
		//获取步骤总价
		getSubtotal(item) {
			let sum = DataProcessing.floatRide(item.moneyModel.unitPrice || 0, item.moneyModel.quantity || 0);
			sum = (sum * this.baseModel.rate) / 100;
			sum = sum.toFixed(2); // DataProcessing.toFixedNoRounding(sum, 2);
			if (this.isTypeQUR(this.tableListModel.list[0])) {
				return this.tableListModel.list[0].moneyModel.type;
			} else {
				return sum;
			}
		},
		//获取inputList总价
		getInputListTotal(item, quantity) {
			let sum = DataProcessing.floatRide(quantity || 0, item.price || 0);
			sum = DataProcessing.floatRide(sum || 0, item.quantity || 0);
			sum = (sum * this.baseModel.rate) / 100;
			sum = sum.toFixed(2); // DataProcessing.toFixedNoRounding(sum, 2);
			if (this.isTypeQUR(this.tableListModel.list[0])) {
				return this.tableListModel.list[0].moneyModel.type;
			} else {
				return sum;
			}
		},
		//获取dataList总价
		getDataListTotal(item, quantity) {
			let sum = DataProcessing.floatRide(item.price || 0, quantity || 0);
			sum = (sum * this.baseModel.rate) / 100;
			sum = sum.toFixed(2); //DataProcessing.toFixedNoRounding(sum, 2);
			if (this.isTypeQUR(this.tableListModel.list[0])) {
				return this.tableListModel.list[0].moneyModel.type;
			} else {
				return sum;
			}
		},
		//获取磨具费总价
		getMoldFeeTotal(feeTotal) {
			let sum = (feeTotal * this.baseModel.rate) / 100;
			sum = sum.toFixed(2); //DataProcessing.toFixedNoRounding(sum, 2);
			if (this.isTypeQUR(this.tableListModel.list[0])) {
				return this.tableListModel.list[0].moneyModel.type;
			} else {
				return sum;
			}
		},
		//获取总价
		//参数1：数据源
		//参数2：是否开启递归找娃  不传||递归套娃找推荐数量     true||不递归
		getTotal(item, op) {
			console.log("asdzz", item, op);
			let sub = this.getSubtotal(item);
			let fee = this.getMoldFeeTotal(item.moneyModel.moldFee);
			let inputSum = 0;
			let dataSum = 0;
			item.inputList.map((i) => {
				let sum = item.moneyModel?.quantity;
				if (i.setUpFee) {
					sum = i.quantity;
				}
				inputSum = DataProcessing.floatAdd(inputSum, this.getInputListTotal(i, sum));
			});
			item.dataList.map((i) => {
				let sum = item.moneyModel?.quantity;
				if (i.setUpFee) {
					sum = i.quantity;
				}
				dataSum = DataProcessing.floatAdd(dataSum, this.getDataListTotal(i, sum));
			});
			let total = DataProcessing.floatAdd(sub, fee);
			total = DataProcessing.floatAdd(total, inputSum);
			total = DataProcessing.floatAdd(total, dataSum);
			total = total.toFixed(2); //DataProcessing.toFixedNoRounding(total, 2);
			let s = 0;
			if (this.isTypeQUR(this.tableListModel.list[0])) {
				s = this.tableListModel.list[0].moneyModel.type;
			} else {
				s = total;
			}
			item.moneyModel.subtotal = s;
			if (op == null) {
				this.sellQuantityFunc(item);
			}
			return s;
		},
		//计算推荐数量
		sellQuantityFunc(item) {
			let itemNew = JSON.parse(JSON.stringify(item));
			let index = 0;
			for (let i = item.moneyModel.quantity; i > 0; i--) {
				itemNew.moneyModel.quantity = i;
				let s = this.getTotal(itemNew, true);
				if (s < this.get168()) {
					index = i;
					break;
				}
			}
			this.quantityModel.custQuantityMsg = index + 1;
		},
		//获取168*国家倍率
		get168() {
			if (this.tableListModel.list.length > 0) {
				if (this.totalModel.sellQuantity) {
					this.tableListModel.list[0].moneyModel.sellQuantity = this.totalModel.sellQuantity;
					let t = DataProcessing.floatRide(this.tableListModel.list[0].moneyModel.sellQuantity || 0, this.tableListModel.list[0].moneyModel.rate);
					t = parseFloat(t).toFixed(2);
					return t;
				} else {
					return 0;
				}
			} else {
				return 0;
			}
		},
		//重置
		resetFunc() {
			this.$confirm(this.lang.fd.btn.reset, this.lang.fd.btn.prompt, {
				confirmButtonText: this.lang.fd.btn.submit,
				cancelButtonText: this.lang.fd.btn.cancel,
				type: "warning",
			})
				.then(() => {
					this.quantityModel = new quantityModel();
					this.tableListModel = new tableListModel();
					this.baseModel = new baseModel();
					this.init();
				})
				.catch(() => {});
		},
		//保存
		saveFunc() {
			this.$confirm(this.lang.fd.btn.save, this.lang.fd.btn.prompt, {
				confirmButtonText: this.lang.fd.btn.submit,
				cancelButtonText: this.lang.fd.btn.cancel,
				type: "warning",
			})
				.then(() => {
					let model = {
						snapshot: JSON.stringify(this.tableListModel),
					};
					saveQuoteRecord(model).then((res) => {
						if (res) {
							this.$toast.success(res.message);
						}
					});
				})
				.catch(() => {});
		},
		back() {
			this.$router.back();
		},
	},
};
</script>
<style scoped lang="scss">
@mixin centerCenter {
	display: flex;
	justify-content: center;
	align-items: center;
}
@mixin imgCss {
	width: 100%;
	height: 100%;
}
@mixin fontBold {
	color: #333333;
	font-weight: Bold;
}
@mixin padding10 {
	padding: 0 10px;
}
$imgServer: "https://static-oss.gs-souvenir.com/web/";
$color-blue: #0066cc;
.quoteFdSoftEnamelLapelpinDetail {
	min-height: 70vh;
	background: #f2f5f7;
	.blue {
		color: $color-blue;
	}
	.main {
		width: 100%;
		max-width: 1240px;
		margin: 0 auto;
		.back {
			padding-top: 15px;
			font-weight: 700;
			font-size: 15px;
			cursor: pointer;
			b {
				color: #707070;
				font-size: 20px;
				margin-left: 3px;
			}
		}
		@include respond-to(mb) {
			.back {
				padding: 0 10px;
			}
		}
		.title {
			@include fontBold;
			@include centerCenter;
			height: 45px;
			justify-content: flex-start;
			flex-wrap: wrap;
		}
		.nav {
			display: flex;
			flex-wrap: wrap;
			.li {
				@include centerCenter;
				background: #f8f8f8;
				border: 1px solid #dfdfdf;
				border-radius: 4px;
				margin: 0 15px 15px 0;
				@include padding10;
				cursor: pointer;
				font-size: 15px;
				color: #333;
				height: 35px;
				.box {
					position: relative;
				}
			}
			.liInput {
				padding: 0;
				border: 0;
			}
			.liInput:hover {
				border: 0 !important;
			}
			.liOn {
				border: 1px solid $color-blue;
			}
			.li:hover {
				background: #fff;
				border: 1px solid $color-blue;
			}
		}
		.navMsg {
			display: block;
			margin-bottom: 15px;
			font-size: 13px;
			color: red;
			margin-top: -10px;
		}
		@include respond-to(mb) {
			.navMsg {
				padding: 0 10px;
				margin-bottom: 10px;
			}
		}

		.printConsole {
			background: #fff;
			.title {
				height: 60px;
				padding: 0 50px;
				display: flex;
				border-bottom: 1px solid #dfdfdf;
				position: relative;
				.left {
					flex: 3;
				}
				.leftTit {
					font-size: 18px;
					position: absolute;
				}
				.right {
					flex: 7;
					display: flex;
					justify-content: flex-end;
					height: 100%;
					align-items: center;
					.buttonBtn {
						display: flex;
					}
					.btn {
						font-size: 16px;
						cursor: pointer;
						font-weight: 100;
					}
					.modifyPrice {
						padding: 10px 15px;
						border-radius: 4px;
						color: #000;
						margin-right: 15px;
						font-size: 18px;
						font-family: Calibri;
						font-weight: 700;
					}
					.modifyPriceSave {
						margin-right: 0;
						margin-left: 15px;
						background: $color-blue !important;
						color: #fff !important;
					}
					.apply {
						padding: 10px 15px;
						background: #fff;
						border-radius: 4px;
						color: $color-blue;
						border: 1px solid $color-blue;
						height: 40px;
						font-family: Calibri;
						@include centerCenter;
					}
					.percentage {
						margin: 0 10px;
						@include centerCenter;
						font-size: 16px;
						color: #666;
						font-family: Calibri;
					}
				}
			}
			.tableList {
				padding: 0 50px;
				//	min-width: 500px;
				.table {
					width: 100%;
					border-collapse: collapse;
					border-spacing: 0px;
					margin-top: 10px;
					tr {
						td {
							text-align: center;
							.input {
								border: 1px dashed #9a9a9a;
								padding: 5px 10px;
								border-radius: 2px;
								width: 70px;
								margin-left: 5px;
							}
						}
						.tdImg {
							width: 160px;
						}

						.tdContent {
							text-align: left;
							.fontB {
								margin-right: 10px;
							}
						}
						.tdLeft {
							text-align: center;
						}
					}
					.th {
						height: 30px;
						background: #969eb5;
						font-size: 16px;
						color: #fff;
						td {
							text-align: center;
							line-height: 30px;
						}
					}
					td {
						vertical-align: top; /* 确保内容垂直对齐到单元格的顶部 */
						padding: 5px; /* 根据需要设置内边距 */
						overflow: hidden; /* 防止内容溢出 */
						word-break: break-all; /* 当单元格宽度不足以显示全部内容时，允许在任何字符间断开 */
						border: 1px solid #dfdfdf;
						padding: 10px;
						.rowOne {
							line-height: 1.5;
						}
					}
					.unitPriceDiv {
						@include centerCenter;
					}
				}
				.total {
					display: flex;
					margin: 15px 0 25px 0;
					font-weight: Bold;
					.copywriting {
						color: #569ee5;
						line-height: 1.5;
						flex: 5;
						.p1 {
							color: #f86918;
							font-size: 19px;
							font-weight: 700;
							color: #f86918;
							margin-bottom: 5px;
							margin-top: 20px;
							padding-left: 15px;
						}
						.p2 {
							font-size: 16px;
							font-weight: 700;
							color: #db5834;
							&::before {
								content: "•";
								color: #3d3d3d;
								padding-right: 10px;
							}
						}
						.p22 {
							padding: 0;
							width: 100%;
							font-size: 16px;
							font-weight: 700;
							color: #db5834;
						}
						.p3 {
							padding-left: 18px;
							font-size: 13px;
							font-weight: 400;
							color: #3d3d3d;
						}
						.p33 {
							padding: 0;
							width: 100%;
							&::before {
								content: " ";
							}
						}
						.copywritingP1 {
							font-size: 16px;
						}
						.copywritingP2 {
							font-size: 15px;
						}
					}
					.right {
						flex: 5;

						.liTop {
							display: flex;
							justify-content: flex-end;
							.p1 {
								color: #333;
								font-size: 16px;
								height: 50%;
								padding-top: 2px;
							}
							.p2 {
								color: #f32b11;
								font-size: 18px;
								height: 50%;
							}
						}
						.liBottom {
							padding: 10px 0px 0 15%;
							.p1 {
								font-size: 1.05rem;
								color: $color-blue;
								font-weight: 700;
								margin-bottom: 5px;
							}
							.p2 {
								font-size: 0.9rem;
								color: $color-blue;
								margin-bottom: 5px;
								span {
									font-size: 0.75rem;
								}
							}
						}
						@include respond-to(mb) {
							.liBottom {
								padding: 0;
							}
						}
					}
				}
			}

			.bottomBtn {
				display: flex;
				justify-content: center;
				margin: 40px 0 0px 0;
				padding-bottom: 28px;
				.btn {
					width: 190px;
					height: 40px;
					@include centerCenter;
					border-radius: 10px;
					margin-right: 20px;
					cursor: pointer;
				}
				.download {
					border: 1px solid #ff6600;
					background: #fff;
					color: #ff6600;
				}
				.imprint {
					background: #ff6600;
					color: #fff;
				}
				&.bottomBtn__right {
					justify-content: flex-end;
					margin-right: 30px;
				}
				@include respond-to(mb) {
					&.bottomBtn__right {
						display: flex;
						align-items: center;
						justify-content: center;
						margin-right: 0;
						margin-left: 30px;
					}
				}
			}
		}
	}
}
@media screen and (max-width: 1000px) {
	.quoteFdSoftEnamelLapelpinDetail {
		.wenhaoIcon {
			color: #515151;
			font-size: 15px;
			font-weight: 700 !important;
		}
		.main {
			.title {
				@include padding10;
				font-size: 16px;
				margin-bottom: 10px;
				border-top: 10px solid #f2f5f7;
				height: auto;
			}
			.nav {
				@include padding10;
				height: auto;
				.li {
					font-size: 13px;
				}
			}
			.printConsole {
				.title {
					@include padding10;
					height: auto;
					border-bottom: 0;
					padding: 0 10px 10px 10px;

					.leftTit {
						width: 100%;
						flex: none;
						font-size: 16px;
						margin-bottom: 10px;
						position: relative;
						padding-top: 10px;
					}
					.right {
						justify-content: flex-start;
						flex-wrap: wrap;
						.modifyPrice {
							display: flex;
							width: 100%;
							margin-bottom: 15px;
							width: auto;
							margin: 0;
							padding: 0;
							display: flex;
							align-items: center;
						}
						.percentage {
							height: 40px;
							margin: 0 5px;
						}
						.apply {
							padding: 5px 15px 5px 15px !important;
							margin-top: 17px;
						}
						.btnInput {
							color: #999;
							position: relative;
						}
						.btn {
							font-size: 16px;
							padding: 2px 0px;
							height: 35px;
							display: flex;

							.el-input {
								width: calc(100vw - 180px); //240
								height: 35px;
								.el-input__inner {
									height: 35px;
									line-height: 35px;
								}
							}
						}
						.buttonBtn {
							display: flex;
							width: calc(100vw - 20px); //75
							justify-content: flex-end;
						}
						.modifyPriceIcon {
							margin-left: 3px;
							color: #515151;
							font-size: 18px;
						}
					}
				}
				.tableList {
					@include padding10;
					.table {
						/*	height: 200px;
						overflow: auto;
						display: inline-block;
						border: 1px solid #eee;*/
						.th {
							td {
								font-size: 12px;
								line-height: 1.4;
							}
						}
						tr {
							.tdImg {
								width: 80px !important;
							}
							td {
								.input {
									width: 50px;
								}
							}
						}
					}
					.total {
						padding: 0;
						margin: 15px 0 25px 0;
						width: calc(100vw - 20px);
						.p1 {
							font-size: 12px;
						}
						.p2 {
							font-size: 12px;
						}
					}
				}

				.bottomBtn {
					.imprint {
						font-size: 13px;
					}
				}
			}
		}
		.total {
			flex-direction: column;
		}
		.copywriting {
			order: 1;
			padding: 0 10px !important;
			.p1 {
				font-size: 14px !important;
			}
			.p2 {
				font-size: 12px !important;
			}
			.p3 {
				font-size: 12px !important;
			}
			.copywritingP1 {
				font-size: 13px !important;
			}
			.copywritingP2 {
				font-size: 11px !important;
			}
		}
	}
}
</style>

<style lang="scss">
.quoteFdSoftEnamelLapelpinDetail {
	.main {
		.title {
		}
		.nav {
			.li {
				.box {
					.el-checkbox__input {
						position: relative;
						margin-right: 10px;
						display: flex;
						align-items: center;
					}
					.el-checkbox.is-bordered.el-checkbox--medium .el-checkbox__inner,
					.el-checkbox__inner {
						width: 16px;
						height: 16px;
					}
					.el-checkbox__inner {
						border: 1px solid #ccc;
						border-radius: 3px;
					}
					.el-checkbox__input.is-checked .el-checkbox__inner,
					.el-checkbox__input.is-indeterminate .el-checkbox__inner {
						border: 1px solid #409eff;
					}
				}
			}
			.liInput {
				input {
					width: 125px;
					height: 35px;
					line-height: 35px;
					border: 0;
					cursor: pointer;
				}
				.el-input__inner:focus {
					outline: none;
					border-color: #dcdfe6; /* 还原为原始的边框颜色，根据实际情况设置 */
				}
				.el-input__suffix {
					top: -2px;
				}
			}
		}
		.printConsole {
			.title {
				.right {
					.apply {
						.applyIcon {
							margin-left: 2px;
						}
					}
					.btnInput {
						padding: 0;
						position: relative;
						input {
							padding: 0 25px 0 10px;
							font-family: Calibri;
							border-radius: 4px;
							.el-input__inner {
								height: 42px;
								line-height: 42px;
							}
						}
						.btnInputIcon {
							position: absolute;
							right: 8px;
							top: 10px;
							font-family: Calibri;
							font-weight: 700;
						}
					}
					.btn {
						.el-input {
							width: 200px;
						}
					}
				}
			}
		}
	}
}
@media screen and (max-width: 1000px) {
	.quoteFdSoftEnamelLapelpinDetail {
		.main {
			.nav {
				.liInput {
					input {
						width: 160px;
					}
				}
				.li {
					.el-checkbox.is-bordered.el-checkbox--medium .el-checkbox__inner,
					.el-checkbox__inner {
						width: 13px !important;
						height: 13px !important;
					}
					.box .el-checkbox__input {
						margin-right: 5px;
					}
				}
			}
			.printConsole {
				.title {
					.btnInput {
						.el-input__inner {
							font-size: 12px;
							height: 30px;
							line-height: 30px;
						}
					}
					.right {
						flex-wrap: wrap;
						.btnInputIcon {
							top: 12px;
						}
					}
				}
			}
		}
	}
}
</style>
