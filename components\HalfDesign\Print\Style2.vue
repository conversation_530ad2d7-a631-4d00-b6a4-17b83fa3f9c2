<template>
	<div class="mb-4 printStyle2" :class="stepData.styleName">
	    <slot name="stepText"></slot>
		<div class="switchBox custom-scrollbar" ref="switchBoxPrintStyle2">
			<div class="switch-item" :style="{ pointerEvents: isScroll ? 'none' : 'auto' }" :class="{ active: selectIndex == index }" v-for="(item, index) in stepData.productParamList" :key="index" @click="selectStep(item, index)" @mouseenter="hoverItem(item)" @mouseleave="leaveItem">
				<div class="con-radio">
					<label>
						<input type="radio" :checked="selectIndex == index" name="printNumStyle2" :value="index" v-model="selectIndex" />
						<span class="custom-radio"></span>
					</label>
				</div>
				<span class="switchText">{{ item.valueName }}</span>
			</div>
		</div>
		<div class="priceBox" :class="{ active: isPriceBoxVisible || selectIndex != -1 }">
			<div class="priceLeft">
				<div class="leftText">
					{{ langSemiCustom.quantity }}
				</div>
				<div class="leftText">
					{{ langSemiCustom.unitPrice }}
				</div>
			</div>
			<div class="priceRight custom-scrollbar" ref="priceItemPrintStyle2">
				<div class="priceItem" :style="{ pointerEvents: isScroll2 ? 'none' : 'auto' }" v-for="(item, index) in qtyPrice" :key="index" v-show="qtyPrice && qtyPrice.length > 0">
					<div class="rightText">{{ item.quantity || 0 }}+</div>
					<div class="rightText priceText">
						<span class="grayFont"><CCYRate :price="item.unitPrice"></CCYRate></span><span><CCYRate :price="item.markupUnitPrice"></CCYRate></span>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import { addDragable } from "@/utils/utils";

export default {
	name: "printStyle2",
	inject: ["getUnitPriceStep", "getCustomPriceData", "getProductInfo", "getCurrentPrintMethod"],
	props: {
		stepData: {
			type: Object,
			default: {},
		},
	},
	components: {},
	data() {
		return {
			selectIndex: -1,
			selectItem: null,
			isScroll: false,
			isScroll2: false,
			isPriceBoxVisible: false,
		};
	},
	watch: {},
	computed: {
		productInfo() {
			return this.getProductInfo();
		},
		unitPriceStep() {
			return this.getUnitPriceStep();
		},
		customPriceData() {
			return this.getCustomPriceData();
		},
		langSemiCustom() {
			return this.$store.getters.lang?.semiCustom;
		},
		qtyPrice() {
			if (!this.printMethodData) {
				return [];
			}
			try {
				let data = this.printMethodData.markupIncreasePrice&&JSON.parse(this.printMethodData.markupIncreasePrice);
				return data;
			} catch (e) {
				return [];
			}
		},
		printMethodData() {
			return this.getCurrentPrintMethod();
		},
	},
	methods: {
		hoverItem(item) {
			this.selectItem = item;
			this.isPriceBoxVisible = true;
		},
		leaveItem() {
			if (this.selectIndex >= 0) {
				this.selectItem = this.stepData.productParamList[this.selectIndex];
			}
			this.isPriceBoxVisible = false;
		},
		selectStep(item, index) {
			this.selectIndex = index;
			this.selectItem = item;
			this.$emit("selectStep", {
				type: this.stepData.attributeFlag,
				data: item,
				parent: this.stepData,
				id: this.stepData.id,
			});
		},
		selectDefault() {
			if (this.stepData.productParamList.length) {
				this.selectStep(this.stepData.productParamList[0], 0, true);
			}

			// if (this.selectIndex <= -1) {
			// 	let findIndex = this.stepData.productParamList.findIndex((item) => {
			// 		return !item.isBlank;
			// 	});
			// 	if (findIndex > -1) {
			// 		this.selectStep(this.stepData.productParamList[findIndex], findIndex, true);
			// 	}
			// }

			// console.log(this.stepData.productParamList,'22123123')
			// if (this.selectIndex <= -1) {
			// 	if(this.stepData.productParamList.length==1){
			// 		this.selectStep(this.stepData.productParamList[0], 0, true);
			// 	}
			// 	// if(this.stepData.productParamList.length > 1){
			// 	// 	let findIndex = this.stepData.productParamList.findIndex((item) => {
			// 	// 		return !item.isBlank;
			// 	// 	});
			// 	// 	if (findIndex > -1) {
			// 	// 		this.selectStep(this.stepData.productParamList[findIndex], findIndex, true);
			// 	// 	}
			// 	// }
			// }
		},
		findPrice(item, itemIncreasePrice) {
			let len = itemIncreasePrice.length,
				newNum = parseInt(item.quantity),
				findItem = itemIncreasePrice[0];
			for (let i = 0; i < len; i++) {
				let item = itemIncreasePrice[i],
					nextItem = itemIncreasePrice[i + 1];
				if (newNum >= item.quantity && (nextItem ? newNum < nextItem.quantity : true)) {
					findItem = item;
					break;
				}
			}
			return findItem;
		},
		setPrice(discount = 0) {
			let increasePrice = JSON.parse(this.unitPriceStep.increasePrice),
				itemIncreasePrice = JSON.parse(this.selectItem.increasePrice);
			if (this.selectItem.priceType === 5) {
				increasePrice.forEach((item) => {
					//单价
					let price = parseFloat(item.unitPrice) + parseFloat(this.findPrice(item, itemIncreasePrice).unitPrice);
					//计算价格分层
					let price2 = (price * this.customPriceData.multiBasicUnitPrice).toFixed(2);
					//计算折扣
					let price3 = Math.round(price2 * Math.abs(1 - discount) * 100) / 100;
					if (discount != 0) {
						item.discountPrice = price3.toFixed(2);
						item.unitPrice = price2;
					} else {
						item.unitPrice = price2;
					}
				});
				this.$Bus.$emit("priceSection", increasePrice);
				let isShowPrice = increasePrice.filter((item) => item.isShow == 1 || item.isShow == null);
				return isShowPrice || [];
			} else if (this.selectItem.priceType === 1) {
				increasePrice.forEach((item) => {
					//单价
					let price = parseFloat(item.unitPrice) + this.selectItem.unitPrice;
					//计算价格分层
					let price2 = (price * this.customPriceData.multiBasicUnitPrice).toFixed(2);
					//计算折扣
					let price3 = Math.round(price2 * Math.abs(1 - discount) * 100) / 100;
					if (discount != 0) {
						item.discountPrice = price3;
						item.unitPrice = price2;
					} else {
						item.unitPrice = price2;
					}
				});
				this.$Bus.$emit("priceSection", increasePrice);
				let isShowPrice = increasePrice.filter((item) => item.isShow !== 0);
				return isShowPrice || [];
			}
		},
	},
	created() {},
	mounted() {
		this.$Bus.$on("selectDefaultSizeStep", this.selectDefault);
		const container = this.$refs.switchBoxPrintStyle2;
		const priceItem = this.$refs.priceItemPrintStyle2;
		if (container) {
			addDragable(
				container,
				() => {
					this.isScroll = true;
				},
				() => {
					this.isScroll = false;
				}
			);
		}
		if (priceItem) {
			addDragable(
				priceItem,
				() => {
					this.isScroll2 = true;
				},
				() => {
					this.isScroll2 = false;
				}
			);
		}
	},
	beforeDestroy() {
		// this.$Bus.$off("updateQty", this.updateQty);
		this.$Bus.$off("selectDefaultSizeStep", this.selectDefault);
		// this.$Bus.$off("setSizeParam", this.setSizeParam);
		// this.$Bus.$off("selectDefaultSizeStep", this.setInputNum);
	},
};
</script>

<style scoped lang="scss">
@import "~assets/css/half.scss";

.con-radio {
	label {
		display: flex;
		align-items: center;
		flex-direction: row-reverse;
	}

	/* 未选中状态下的样式 */
	input[type="radio"] {
		/* 隐藏原始的单选按钮 */
		display: none;
	}

	/* 自定义样式 */
	.custom-radio {
		display: inline-block;
		width: 16px;
		height: 16px;
		border-radius: 50%;
		border: 1px solid #afb1b3;
		background-color: #fff;
		position: relative;
		cursor: pointer;
	}

	/* 选中状态下的样式 */
	.custom-radio:before {
		content: "";
		display: block;
		width: 0.5em;
		height: 0.5em;
		background-color: #fff;
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		border-radius: 50%;
	}

	/* 选中状态下的外圈样式 */
	input[type="radio"]:checked + .custom-radio {
		background-color: $color-primary !important;
		border: 1px solid $color-primary;
	}
}

.printStyle2 {
	.switchBox {
		padding: 0.8em 0;
		display: flex;
		align-items: center;
		flex-wrap: nowrap;
		column-gap: 10px;
		width: 100%;
		overflow: auto hidden;
		box-sizing: border-box;

		&::-webkit-scrollbar {
			/*滚动条整体样式*/
			width: 5px;
			height: 2px;
		}

		.switch-item {
			flex-shrink: 0;
			width: 126px;
			border-radius: 10px;
			background: #f7f7f7;
			border: 1px solid #e6e6e6;
			text-align: center;
			cursor: pointer;
			display: flex;
			align-items: center;
			justify-content: center;
			position: relative;
			font-weight: 400;
			font-size: 16px;

			&.active {
				border-color: $color-primary;
				background-color: #fff;

				&::before,
				&::after {
					opacity: 1;
					visibility: visible;
				}
			}

			@media (any-hover: hover) {
				&:hover {
					border-color: $color-primary;
					background-color: #fff;

					&::before,
					&::after {
						opacity: 1;
						visibility: visible;
					}
				}
			}

			&::after {
				content: "";
				position: absolute;
				bottom: -0.4em;
				left: 50%;
				transform: translateX(-50%) rotate(45deg);
				width: 0.8em;
				height: 0.8em;
				border: 1px solid $color-primary;
				border-top-color: transparent;
				border-left-color: transparent;
				background-color: #fff;
				opacity: 0;
				visibility: hidden;
			}

			&.active::after {
				opacity: 1;
			}

			.switchText {
				padding: 6px 10px;
				display: flex;
				align-items: center;
				justify-content: center;
				color: #333333;
				text-align: center;
			}
		}
	}

	.priceBox {
		opacity: 0;
		visibility: hidden;
		margin-top: 10px;
		width: 100%;
		overflow: hidden;
		display: flex;
		align-items: center;
		background: #f7f7f7;
		border-radius: 8px;

		&.active {
			opacity: 1;
			visibility: visible;
		}

		.priceLeft {
			min-width: 110px;
			flex-shrink: 0;
			background-color: $color-second;
			font-weight: bold;
			font-size: 16px;
			color: #333333;
			text-align: center;
			display: flex;
			align-items: center;
			justify-content: center;
			flex-direction: column;

			.leftText {
				margin: 10px 0;
			}
		}

		.priceRight {
			flex: 1;
			overflow-x: auto;
			display: flex;
			align-items: center;
			flex-wrap: nowrap;

			&::-webkit-scrollbar {
				/*滚动条整体样式*/
				width: 5px;
				height: 2px;
			}

			.priceItem {
				width: 120px;
				display: flex;
				align-items: center;
				justify-content: center;
				flex-direction: column;
				flex-shrink: 0;
				position: relative;
				user-select: none;

				.rightText {
					margin: 10px 0;
					font-size: 16px;
					color: #333333;

					&.priceText {
						color: $color-primary;
					}

					.grayFont {
						color: #999999;
						text-decoration-line: line-through;
						margin-right: 6px;
					}
				}

				&:last-child {
					&::after {
						display: none;
					}
				}

				&::after {
					content: "";
					position: absolute;
					top: 50%;
					right: 0;
					transform: translate(0, -50%);
					width: 1px;
					height: 3.5em;
					background: #e7e1dd;
				}
			}
		}
	}
}

@include respond-to(mb) {
	.printStyle2 {
		.switchBox {
			column-gap: 8px;

			.switch-item {
				width: 90px;

				&::after {
					content: "";
					bottom: -0.3em;
					width: 0.6em;
					height: 0.6em;
				}

				.switchText {
					padding: 6px;
				}
			}
		}

		.priceBox {
			.priceLeft {
				min-width: 90px;
				align-self: stretch;

				.leftText {
					margin: 6px 0;

					&:last-child {
						flex: 1;
						margin-top: 0;
						padding-top: calc(0.5em + 8px);
					}
				}
			}

			.priceRight {
				.priceItem {
					width: 90px;

					&::after {
						width: 1px;
						height: 4.5em;
					}

					.rightText {
						margin: 6px 0;

						&.priceText {
							display: flex;
							align-items: center;
							flex-direction: column;
							row-gap: 4px;

							.grayFont {
								margin-right: 0;
							}
						}
					}
				}
			}
		}
	}
}
</style>
