<template>
  <div
    class="StepBox"
    :class="{ 'custom-shadow': customShadow, active: isActive }"
  >
    <label
      class="item pointer hover-type"
      :class="{ noBorder: noBorder }"
      :for="'chooseYourLanyardAttachmentLeft' + bindValue.id"
	  @click.stop="changeInput($event)"
    >
      <i
        v-if="freeTitle"
        class="iconfont color-a-icon-freezhuanhuan"
        :class="
          bindValue && bindValue.priceInfo && bindValue.priceInfo.unitPrice
            ? 'noShow'
            : 'freeTitle'
        "
      ></i>
      <div class="titleCard" v-if="bindValue.remark">
        {{ bindValue.remark }}
      </div>
      <div class="flex justify-content-center align-items-center custom-shadow">
        <div class="se">
          <div class="swiper flex justify-content-center align-items-center ">
            <el-image
              :src="
                imageValue
                  ? bindValue[imageValue]
                  : filterImage(bindValue.imageJson)
              "
              :style="aspectRatio"
              lazy
            />
              <img class="quickShippingLogo" :src="bindValue.quickShippingLogo" alt="">
          </div>
        </div>
      </div>
      <span class="product-info">
        <label :for="bindValue.cateName + bindValue.id" class="radio-beauty">
        </label>
        <span class="bTitle">{{
          bindValue.alias ? bindValue.alias : (bindValue.cateName || bindValue.cateNameQuote)
        }}</span>
      </span>
      <b v-if="showDetail" class="icon-jxsht-ybp-yj iconfont eye pointer"  @click="showDetailDialog" ></b>
    </label>
    <el-dialog
      :append-to-body="true"
      :destroy-on-close="true"
      :show-close="false"
      custom-class="swiperDialog closeBtn"
      :visible.sync="showDetailBoo"
      :width="windowWidth <= 1279 ? '95%' : '621px'"
    >
      <el-button
        class="btn-close"
        circle
        @click="showDetailBoo = false"
      ></el-button>
      <SwiperDialog
        :styleName="showDetailDialogCateName"
        :imgList="showDetailDialogImage"
      ></SwiperDialog>
    </el-dialog>
  </div>
</template>
<script>
import VideoPlayer from "@/components/Quote/VideoPlayer.vue"
import SwiperDialog from "@/components/Quote/SwiperDialog"
export default {
  components: {
    VideoPlayer,
    SwiperDialog,
  },
  props: {
    customShadow: {
      type: Boolean,
      default: false,
    },
    aspectRatio: {
      type: String,
    },
    imageValue: {
      type: String,
    },
    //绑定值
    bindValue: {
      type: Object,
    },
    freeTitle: {
      type: Boolean,
      default: false,
    },
    noBorder: {
      type: Boolean,
      default: false,
    },
    bindName: {
      type: String,
    },
    selectedData: {
      type: [Array, Object],
    },
    showName: {
      type: String,
    },
    showDetail: {
      type: Boolean,
    },
  },
  data() {
    return {
      showDetailDialogCateName: "",
      showDetailDialogImage: "",
      showDetailBoo: false,
      windowWidth: document.documentElement.clientWidth,
      picDialog: false,
      zoomPic: "",
      // activeData: {
      //   addComment: null,
      //   cateId: null,
      //   childList: [],
      //   chooseNum: null,
      //   createTime: null,
      //   id: null,
      //   imageJson: "",
      //   isModelShow: null,
      //   isRadio: null,
      //   modelPosition: null,
      //   onlyAddInquiry: null,
      //   paramCode: null,
      //   paramName: null,
      //   paramType: null,
      //   parentId: null,
      //   priceInfo: {},
      //   stepIndex: null,
      // },
    }
  },
  methods: {
    showDetailDialog() {
      this.showDetailBoo = true
      if (this.bindValue.imageJson) {
        this.showDetailDialogImage =
          typeof this.bindValue.imageJson === "string"
            ? JSON.parse(this.bindValue.imageJson)
            : this.bindValue.imageJson
        this.showDetailDialogCateName = this.bindValue.cateName
        // data.showDetailDialogDescription = typeof val.description === "string" ? JSON.parse(val.description) : val.description;
      }
    },
    parseJSON(str) {
      return str
        ? JSON.parse(str)
        : [
            {
              url: "",
            },
          ]
    },
    activeFun(val) {
      this.bindValue[this.bindName] = val[this.bindName]
      this.bindValue.imageJson = val.imageJson
    },
    isJSON(str) {
      if (typeof str == "string") {
        try {
          var obj = JSON.parse(str)
          if (typeof obj == "object" && obj) {
            return true
          } else {
            return false
          }
        } catch (e) {
          return false
        }
      } else {
        return false
      }
    },
    filterImage(val, hasSec = false) {
      if (this.isJSON(val)) {
        if (hasSec) {
          return JSON.parse(val)[1]
            ? JSON.parse(val)[1].url
            : JSON.parse(val)[0].url
        } else {
          return JSON.parse(val)[0].url
        }
      } else {
        return val.url
      }
    },
    filterPrice(val) {
      if (!val.unitPrice && !val.moldPrice) {
        return "Free"
      } else if (!val.unitPrice && val.moldPrice) {
        return `+ Mold Fee: $${val.moldPrice.toFixed(0)}`
      } else {
        return val.moldPrice
          ? `+ $${val.moldPrice.toFixed(0)} Setup,+ $${val.unitPrice.toFixed(
              2
            )}/pc`
          : `+ $${val.unitPrice.toFixed(2)}/pc`
      }
    },
    changeInput(e, type) {
      this.$emit("clickFun", {
        key: this.bindName,
        value: this.bindValue,
      })
      this.$emit("picDialogFun", false)
    },
    getVideoOptions(path, type, poster) {
      if (type === 1) {
        return {
          autoplay: true,
          controls: false,
          muted: true,
          loop: true,
          fill: true,
          sources: [
            {
              src: path,
              type: "video/mp4",
            },
          ],
        }
      } else if (type === 2) {
        return {
          autoplay: true,
          controls: true,
          muted: false,
          loop: true,
          fill: true,
          sources: [
            {
              src: path,
              type: "video/mp4",
            },
          ],
        }
      } else if (type === 3) {
        return {
          autoplay: true,
          controls: false,
          muted: true,
          loop: true,
          fill: true,
          sources: [
            {
              src: path,
              type: "video/mp4",
            },
          ],
        }
      } else if (type === 4) {
        return {
          autoplay: false,
          controls: false,
          muted: true,
          loop: true,
          fill: true,
          sources: [
            {
              src: path,
              type: "video/mp4",
            },
          ],
          poster: poster,
        }
      }
    },
  },
  computed: {
    isActive: function () {
      if (this.inputVal.length > 0) {
        return this.inputVal.some((item) => {
          return item.id == this.bindValue.id
        })
      }
    },
    inputVal() {
      return this.selectedData[this.bindName] || []
    },
    device() {
      return this.$store.state.device
    },
  },
  mounted() {
    // if (this.hasChildList) {
    //   let arr = JSON.parse(JSON.stringify(this.bindValue.childList[0]));
    //   for (var key in arr) {
    //     if (this.activeData.hasOwnProperty(key)) {
    //       this.activeData[key] = arr[key];
    //     }
    //   }
    // }
  },
  created() {},
}
</script>

<style scoped lang="scss">
.StepBox {
  .eye {
    color: #ccc;
    position: absolute;
    right: 15px;
    top: 15px;
    z-index: 1;
    font-size: 17px;

    @media screen and (max-width: 767px) {
      right: 6px;
      top: 9px;
      font-size: 16px;
    }
  }
  .titleCard {
    z-index: 2;
    position: absolute;
    left: 11px;
    top: 11px;
    background-color: #12c1e6;
    border-radius: 10px 0 8px 0;
    font-size: 14px;
    font-weight: 400;
    color: #fff;
    padding: 5px 10px;
    @media screen and (max-width: 767px) {
      font-size: 12px;
      top: 16px;
      padding: 4px 10px;
    }
  }
  ::v-deep .el-image__inner {
    vertical-align: middle;
    border-radius: 10px 10px 0 0;
  }
  .custom-shadow {
    position: relative;
    background: #fff;

    &::before,
    &::after {
      content: "";
      position: absolute;
      z-index: -1;
      bottom: 12px;
      left: 5px;
      width: 50%;
      height: 20%;
      box-shadow: 0 14px 7px #d9dbdd;
      transform: rotate(-3deg);
    }

    &::after {
      right: 5px;
      left: auto;
      transform: rotate(3deg);
    }
  }
  $bg: #afb1b3;
  $bgc: white;
  $bgc2: $color-primary;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  @media screen and (max-width: 767px) {
    padding: 0 0 5.5px 0;
  }
  .freeTitle {
    position: absolute;
    font-size: 39px;
    left: -2px;
    top: -2px;
    z-index: 2;
  }
  .noShow {
    overflow: hidden;
    width: 0;
    height: 0;
    position: absolute;
    opacity: 0;
  }
  .item {
    border-radius: 10px;
    padding: 0 0 10px 0;
    box-sizing: border-box;
    transition: all 0.2s;
    width: 100%;
    height: 100%;
    display: block;
    &.noBorder {
      border: 1px solid transparent;
    }
    // @media (any-hover: hover) {
    //   &:hover {
    //     border-color: $color-primary !important;
    //     box-shadow: 0 3px 4px 0 #ccc;
    //   }
    // }

    @media screen and (max-width: 500px) {
      padding: 5px 0;
    }

    position: relative;

    > div {
      > .el-image img {
        // height: 150px;
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }
  }
  .tooltip {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 2;
    b {
      color: $color-primary;
    }
  }
  &.activeType {
    position: relative;
    &::after {
      position: absolute;
      content: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBzdGFuZGFsb25lPSJubyI/Pg0KPCFET0NUWVBFIHN2ZyBQVUJMSUMgIi0vL1czQy8vRFREIFNWRyAxLjEvL0VOIiAiaHR0cDovL3d3dy53My5vcmcvR3JhcGhpY3MvU1ZHLzEuMS9EVEQvc3ZnMTEuZHRkIj4NCjxzdmcgdD0iMTY0MTM0NzgxOTM5NyIgY2xhc3M9Imljb24iIHZpZXdCb3g9IjAgMCAxMjE2IDEwMjQiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiBwLWlkPSIyNDM3IiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayIgd2lkdGg9IjE5IiBoZWlnaHQ9IjE2Ij4NCiAgICA8ZGVmcz4NCiAgICAgICAgPHN0eWxlIHR5cGU9InRleHQvY3NzIj48L3N0eWxlPg0KICAgIDwvZGVmcz4NCiAgICA8cGF0aCBkPSJNNjAxLjYgNzY4bDQ0OC03NjhMMTIxNiA5NiA2NzIgMTAyNCAwIDY0Ni40bDk2LTE2Ni40eiIgZmlsbD0iI0ZGRkZGRiIgcC1pZD0iMjQzOCI+PC9wYXRoPg0KPC9zdmc+);
      left: 8px;
      top: 5px;
      width: 19px;
      height: 16px;
      z-index: 1;
    }

    &::before {
      position: absolute;
      content: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBzdGFuZGFsb25lPSJubyI/Pg0KPCFET0NUWVBFIHN2ZyBQVUJMSUMgIi0vL1czQy8vRFREIFNWRyAxLjEvL0VOIiAiaHR0cDovL3d3dy53My5vcmcvR3JhcGhpY3MvU1ZHLzEuMS9EVEQvc3ZnMTEuZHRkIj4NCjxzdmcgdD0iMTY0MTI4NzczOTU1MSIgY2xhc3M9Imljb24iIHZpZXdCb3g9IjAgMCAxMDI0IDEwMjQiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiBwLWlkPSIyNDM3IiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayIgd2lkdGg9IjUwIiBoZWlnaHQ9IjUwIj4NCiAgICA8ZGVmcz4NCiAgICAgICAgPHN0eWxlIHR5cGU9InRleHQvY3NzIj48L3N0eWxlPg0KICAgIDwvZGVmcz4NCiAgICA8cGF0aCBkPSJNMCAxNTguODk2NTUydjg2NS4xMDM0NDhMMTAyNCAwSDE1OC44OTY1NTJDODQuMjE1MTcyIDAgMCAxMDAuOTg3NTg2IDAgMTU4Ljg5NjU1MnoiIGZpbGw9IiMxODYxQUUiIHAtaWQ9IjI0MzgiPjwvcGF0aD4NCjwvc3ZnPg==);
      left: 0;
      top: 0;
      width: 50px;
      height: 50px;
      z-index: 1;
    }
  }

  .number-input {
    margin-top: 0;
    width: 140px;
    // height: 34px;
    background: #ffffff;
    border-radius: 4px;
    @media screen and (max-width: 767px) {
      // height: 25px;
    }
    ::v-deep .el-input-number.is-controls-right .el-input-number__decrease {
      // line-height: 15px;
      // bottom: -1px;
    }
    ::v-deep .el-input-number__increase {
      // top: 3px;
      width: 24px;
      // line-height: 16px;
    }
    ::v-deep .el-input-number__decrease {
      // bottom: -1px;
      width: 24px;
      // line-height: 15px;
    }
    ::v-deep .el-input__inner {
      // height: 34px;
      // line-height: 34px;
      @media screen and (max-width: 767px) {
        // height: 25px;
        // line-height: 25px;
        text-align: center;
      }
    }
  }
  .se {
    cursor: pointer;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    // padding: 10px 13px;
    // @media screen and (max-width: 767px) {
    //   padding: 0;
    // }
    .zoomIcon {
      position: absolute;
      top: 10px;
      right: 10px;
      color: #ffffff;
      font-size: 20px;
      z-index: 2;
      transition: font-size 0.2s;
      &:hover {
        font-size: 24px;
        color: $color-primary;
      }
    }
    .swiper {
      position: relative;
      width: 100%;
      border: 1px solid #d9dbdd;
      border-radius: 6px;
      @media (any-hover: hover) {
        &:hover {
          border-color: $color-primary !important;
          box-shadow: 0 3px 4px 0 #ccc;
        }
      }
      img {
        border-radius: 10px 10px 0 0;
      }

        .quickShippingLogo{
            width: 24%;
            position: absolute;
            right: 10px;
            bottom: 10px;
            border-radius: 0;
        }
    }
  }
  .product-info {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 20px;
    .radio-beauty {
      width: 18px;
      height: 18px;
      box-sizing: border-box;
      display: inline-block;
      border: 1px solid $bg;
      vertical-align: middle;
      margin: 0 12px 0 3px;
      border-radius: 50%;
      background-color: $bgc;
      background-clip: content-box;
      position: relative;
      cursor: pointer;
        flex-shrink: 0;
      &::after {
        content: "";
        position: absolute;
        border-radius: 50%;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        width: 6px;
        height: 6px;
        background-color: $bg;
      }
      @media screen and (max-width: 767px) {
        width: 12px;
        height: 12px;
        min-width: 12px;
        margin: 0;
        margin-bottom: 2px;
        &::after {
          width: 5px;
          height: 5px;
        }
      }
    }
    .bTitle {
      font-size: 18px;
      font-weight: 400;
      color: #333333;
      @media screen and (max-width: 767px) {
        font-size: 12px;
		text-align: center;
      }
    }
    .type2-price {
      display: flex;
      flex-direction: column;
    }
  }
  .product-price {
    font-size: 16px;
    font-weight: 400;
    color: #333333;
    margin-top: 5px;
    text-align: center;
    @media screen and (max-width: 767px) {
      margin-top: 5px;
      margin-bottom: 0;
      font-size: 12px;
    }
    &.free {
      color: #de3500;
    }
  }
  &.active {
    .product-info .radio-beauty {
      background-color: $color-primary;
      border-color: $color-primary;
      &::after {
        background-color: $bgc;
      }
    }
    .product-info .bTitle {
      // color: $color-primary;
      font-size: 18px;
      font-weight: 400;
      color: #333333;
      @media screen and (max-width: 767px) {
        font-size: 12px;
      }
    }
    .swiper {
      border-color: $color-primary !important;
      box-shadow: 0 3px 4px 0 #ccc;
    }
  }
}
</style>