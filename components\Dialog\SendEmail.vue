<template>
	<v-dialog
		:value="sendEmailDialog"
		@input="$emit('update:sendEmailDialog',false)"
		width="520"
	>
		<v-card class="sendEmailDialogCard">
			<div class="text-center mb-4" style="font-size:18px;">
				Are you sure to send the new price to {{userName}}
			</div>
			<v-card-text class="text-center body-1">
				Once you confirm, it will be auto matically sent to
				your customer for confirmation.
			</v-card-text>
			<div class="d-flex justify-center">
				<v-btn color="primary" depressed class="mr-2" @click="confirmSendEmail">Confirm</v-btn>
				<v-btn depressed @click="$emit('update:sendEmailDialog',false)">Cancel</v-btn>
			</div>
		</v-card>
	</v-dialog>
</template>

<script>
export default {
	props:['sendEmailDialog','userName'],
	methods:{
		confirmSendEmail(){
			this.$emit('confirmSendEmail')
		}
	}
}
</script>

<style scoped lang="scss">

.sendEmailDialogCard {
	padding: 20px;
	font-size: 16px;
}
</style>
