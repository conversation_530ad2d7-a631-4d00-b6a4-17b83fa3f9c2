<template>
	<div class="uploadArea">
		<div class="content">
			<div class="mbTitle" v-if="device === 'mb'">
				<div class="back" @click="back">
					<b class="icon-back" style="color: #cbcbcb"></b>
					<span>Back</span>
				</div>
				<div class="step-item-title">
					<span>{{ lang.step }} 3:</span>
					<span> {{ lang.neon.uploadTitle }}</span>
				</div>
			</div>
			<div class="t" v-show="device !== 'mb'">
				<div class="back" @click="back">
					<b class="icon-back" style="color: #cbcbcb"></b>
					<span>Back</span>
				</div>
				{{ lang.neon.uploadTitle }}
			</div>
			<div class="upload-box" ref="uploadBox" :class="{ dropActive: dropActive }" @click="openUpload">
				<div class="uploadList">
					<template v-if="uploadList.length">
						<ul>
							<li v-for="(item, index) in uploadList" class="uploadItem" :key="item.secure_url">
								<span>{{ item.original_filename }}</span>
								<div>
									<b class="icon-icon_Preview myIcon" @click.stop="previewImg(item.secure_url)"></b>
									<b class="icon-check myIcon" style="color: #0cbd5f"></b>
									<span @click.stop="delUploadImg(index)">
										<b class="icon-shanchu2 myIcon" style="color: #b6b0b0"></b>
									</span>
								</div>
							</li>
						</ul>
					</template>
					<template v-else>
						<b class="icon-shangchuan uploadIcon"></b>
					</template>
				</div>
				<div>
					<div class="upload-btn" style="text-align: center">
						<div class="uploadBtnWrap">
							<button type="button" :disabled="isUpload" :class="{ isDisabled: isUpload }">
								{{ lang.Browse }}
							</button>
							<input type="file" ref="upload" :accept="acceptFileType" multiple @change="uploadPic" />
						</div>
					</div>
					<div class="uploadTip">
						{{ lang.p9 }}.
						<el-tooltip popper-class="cusToolTip" effect="light">
							<div slot="content">
								<div>{{ lang.allowFile }}</div>
							</div>
							<b class="icon-wenhao1"></b>
						</el-tooltip>
					</div>
				</div>
			</div>
			<div class="remark" v-if="uploadList.length">
				<div class="t">{{ lang.Comments }}:</div>
				<textarea v-model="remark" id="" cols="30" rows="10" :placeholder="lang.textAreaPlaceholder"></textarea>
			</div>
			<div class="skip" @click="skip" v-else>
				{{ lang.bannerQuote.or }}, <span class="k">{{ lang.bannerQuote.skip }}</span>
			</div>
			<div class="cartBtnWrap" style="width: fit-content;margin: auto;">
				<button @click="addToCart" :disabled="!uploadList.length">{{ lang.addToCart }}</button>
			</div>
		</div>
	</div>
</template>

<script>
import { uploadFile } from "@/utils/oss";
import { checkFile,acceptFileType } from "@/utils/validate";
import { isImageType } from "@/utils/utils";
import "@/plugins/element";
export default {
	props: {
		uploadList: {
			type: Array,
		},
	},
	data() {
		return {
			remark: "",
			acceptFileType,
			isUpload: false,
			dropActive: false,
		};
	},
	computed: {
		lang() {
			return this.$store.getters.lang.quote || {};
		},
		device() {
			return this.$store.state.device;
		},
	},
	methods: {
		previewImg(img) {
			if (isImageType(img)) {
				this.$viewerApi({
					images: [img],
				});
			} else {
				window.open(img);
			}
		},
		back() {
			this.$emit("back");
			this.$Bus.$emit("back");
		},
		skip() {
			this.$Bus.$emit("skip");
		},
		addToCart() {
			this.$Bus.$emit("addToCart", {
				uploadList: this.uploadList,
				remark: this.remark,
			});
		},
		openUpload() {
			this.$refs.upload.click();
		},
		replayUpload() {
			this.openUpload();
		},
		delUploadImg(index) {
			this.uploadList.splice(index, 1);
		},
		uploadPic(event, type = "upload") {
			this.$gl.show();
			let files = type === "upload" ? event.target.files : event;
			let fileResult = checkFile(Array.from(files), this.acceptFileType, 80);
			if (!fileResult) {
				this.$toast.error("File type error");
				this.$gl.hide();
				return false;
			}
			if (fileResult.nomalSize.length === 0) {
				this.$gl.hide();
				this.$store.commit("setSizeDialog", true);
				this.$store.commit("setInputRefName", "replayUpload");
				this.$store.commit("setOverSizeList", fileResult.overSize);
				this.$refs.upload.value = "";
				return false;
			}
			let uploadPromises = [];
			fileResult.nomalSize.forEach((file) => {
				let promise = uploadFile(file).then((res) => {
					this.uploadList.push({
						original_filename: file.name,
						secure_url: res,
						size: (file.size / 1024).toFixed(1),
					});
				});
				uploadPromises.push(promise);
			});
			Promise.all(uploadPromises).then(() => {
				this.$gl.hide();
				this.$refs.upload.value = "";
				if (fileResult.overSize.length > 0) {
					this.$store.commit("setSizeDialog", true);
					this.$store.commit("setInputRefName", "replayUpload");
					this.$store.commit("setOverSizeList", fileResult.overSize);
				}
			});
		},
		dragleave(e) {
			e.stopPropagation();
			e.preventDefault();
			this.dropActive = false;
		},
		dragenter(e) {
			e.stopPropagation();
			e.preventDefault();
			this.dropActive = true;
		},
		dragover(e) {
			e.stopPropagation();
			e.preventDefault();
			this.dropActive = true;
		},
		drop(e) {
			e.stopPropagation();
			e.preventDefault();
			let files = e.dataTransfer.files;
			this.uploadPic(files, "drop");
		},
	},
	mounted() {
		this.$Bus.$on("replayUpload", this.replayUpload);
		const dropArea = this.$refs.uploadBox;
		dropArea.addEventListener("drop", this.drop, false);
		dropArea.addEventListener("dragleave", this.dragleave, false);
		dropArea.addEventListener("dragenter", this.dragenter, false);
		dropArea.addEventListener("dragover", this.dragover, false);
	},
	beforeDestroy() {
		const dropArea = this.$refs.uploadBox;
		dropArea.removeEventListener("drop", this.drop);
		dropArea.removeEventListener("dragleave", this.dragleave);
		dropArea.removeEventListener("dragenter", this.dragenter);
		dropArea.removeEventListener("dragover", this.dragover);
		this.$Bus.$off("replayUpload");
	},
};
</script>

<style lang="scss" scoped>
input[type="file"] {
	position: absolute;
	clip: rect(0, 0, 0, 0);
}

.uploadArea {
	overflow: hidden;
	position: absolute;
	inset: 0;
	display: flex;
	justify-content: center;
	align-items: flex-start;
	text-align: center;
	padding: 1rem;

	@include respond-to(mb) {
		padding-top: 1rem;
		.content {
			width: 95%;
			margin: 0 auto;
		}
	}

	.t {
		width: 100%;
		margin: 0 0 2rem 0;
		font-size: 2.25rem;

		.back {
			position: absolute;
			left: max(calc(50% - 850px), 1.5vw);
			font-size: 1.25rem;
			cursor: pointer;
		}
	}

	.upload-box.dropActive {
		border-color: $color-primary !important;
	}

	.upload-box {
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 2rem;
		padding: 10px;
		background: #ffffff;
		border: 1px dashed #e9ecf0;
		border-radius: 4px;
		cursor: pointer;
		transition: all 0.3s;
		height: 220px;
		width: 400px;

		@media (any-hover: hover) {
			&:hover {
				border-color: $color-primary;
			}
		}

		.uploadList {
			width: 100%;
			height: 100px;
			overflow: auto;
			margin-bottom: 10px;
			text-align: center;

			ul {
				margin-left: 0;
			}

			.uploadIcon {
				width: 68px;
				height: 55px;
				margin-top: 15px;
				font-size: 52px;
				color: #ccc;
			}

			.uploadItem {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 5px;
				font-size: 14px;

				& > span {
					flex: 1;
					overflow: hidden;
					margin-right: 10px;
					text-overflow: ellipsis;
					white-space: nowrap;
					text-align: left;
				}
			}

			.myIcon {
				margin: 0 4px;
			}
		}

		.upload-btn {
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;

			button {
				width: 242px;
				height: 40px;
				margin-bottom: 10px;
				background: $color-primary;
				border-radius: 10px;
				border: none;
				color: #fff;
				font-size: 18px;
				font-weight: bold;
			}
		}

		.uploadTip {
			b {
				color: $color-primary;
			}
		}

		@include respond-to(mb) {
			height: 150px;
			width: 100%;

			.uploadList {
				height: 80px;

				.uploadItem {
					font-size: 12px;
				}

				.uploadIcon {
					width: 51px;
					height: 41px;
					margin-top: 0;
					font-size: 48px !important;
				}
			}

			.upload-btn button {
				width: 147px;
				height: 30px;
				margin-bottom: 10px;
				font-size: 14px;
				border-radius: 4px;
			}
		}
	}

	.skip .k {
		color: $color-primary;
		cursor: pointer;

		&:hover {
			text-decoration: underline;
		}
	}

	.remark {
		text-align: left;

		.t {
			font-size: 1.13rem;
			margin-bottom: 0.75rem;
		}

		textarea {
			width: 100%;
			padding: 0.5rem;
			height: 6.69rem;
			background: #ffffff;
			border-radius: 0.19rem;
			border: 1px solid #cfcfcf;
			font-size: 0.88rem;
		}
	}

	.cartBtnWrap {
		button {
			display: flex;
			justify-content: center;
			align-items: center;
			width: 12.56rem;
			height: 3.44rem;
			margin: 1rem auto;
			background: $color-primary;
			border-radius: 1.72rem;
			font-size: 1.25rem;
			color: #ffffff;
		}

		button[disabled] {
			background: #e2e2e2;
		}
	}
}

.mbTitle {
	display: flex;
	align-items: center;
	margin-bottom: 1.25rem;

	.back {
		display: flex;
		flex-direction: column;
		margin-right: 1rem;

		b {
			font-size: 2rem;
		}
	}

	.step-item-title {
		display: flex;
		align-items: center;
		font-size: 1rem;
		font-weight: 700;

		@include respond-to(mb) {
			font-size: 1.17rem;
		}

		& > span:first-child {
			margin-right: 4px;
			color: $color-primary;
			font-weight: 700;
			text-transform: uppercase;
		}

		& > span:nth-child(2) {
			margin-right: 4px;
		}
	}
}
</style>
