<template>
    <div class="dateBox">
        <template v-for="(citem, cindex) in newQtyList">
            <el-popover placement="bottom" width="280" trigger="click" :disabled="device == 'mb'">
                <div style="display: grid; grid-template-columns: repeat(2, 1fr); row-gap: 10px">
                    <p>{{ lang.originalAmount }}</p>
                    <p style="text-decoration: line-through; text-align: right">
                        <CCYRate :price="calculateData.foundationUnitPrice * calculateData.totalQuantity + calculateData.toolingCharge"></CCYRate>
                    </p>
                    <p style="font-weight: 700">{{ lang.amountAfterDiscount }}:</p>
                    <p style="color: #de3500; text-align: right">
                        <CCYRate :price="calculateData.totalPrice"></CCYRate>
                    </p>
                </div>
                <div slot="reference" style="height: 100%">
                    <div
                            class="item-wrap discount-item"
                            :key="cindex"
                            @click="selectDiscount(bindValue, citem, cindex)"
                            :class="[
							{
								active: hasId(citem.id, selectedData[bindValue.paramName]),
							},
							`discount-item${cindex}`,
						]"
                    >
                        <div class="top">
                            <div class="circle2"></div>
                            <DiscountText :itemData="citem" :textConfig="textConfig"></DiscountText>
                        </div>
                        <div class="bottom">
                            {{ citem.alias }}
                        </div>
                    </div>
                </div>
            </el-popover>
        </template>
        <div v-show="device == 'mb'" class="price-box">
            <div>{{ lang.originalAmount }}</div>
            <div>
				<span>
					<CCYRate :price="calculateData.foundationUnitPrice * calculateData.totalQuantity + calculateData.toolingCharge"> </CCYRate>
				</span>
            </div>
            <div>{{ lang.amountAfterDiscount }}</div>
            <div>
				<span style="color: #de3500">
					<CCYRate red :price="calculateData.totalPrice"></CCYRate>
				</span>
                <span class="f-right" v-if="Object.keys(calculateData).length > 0"> ({{ (Math.abs(1 - calculateData.discount) * 100).toFixed(0) }}% {{ lang.p8 }}) </span>
                <span class="f-right" v-else> (0% {{ lang.p8 }}) </span>
            </div>
        </div>
    </div>
</template>
<script>
import DiscountText from "@/components/modal/Quote/QuoteComponents/DicountText.vue";
import {getQuoteTime} from "@/assets/js/quote/quotePublic";

export default {
    components: { DiscountText },
    props: {
        calculateData: {
            type: Object,
            default: () => {},
        },
        selectedData: {
            type: Object,
            default: () => {},
        },
        bindValue: {
            type: Object,
            default: () => {},
        },
        pid: {
            type: Number | String,
        },
        customQty: {
            type: Number | String,
        },
        freeText: {
            type: String,
        }
    },
    data() {
        return {
            priceType: [
                {
                    label: "单价",
                    value: "1",
                    key: "unitPrice",
                },
                {
                    label: "总价",
                    value: "2",
                    key: "totalPrice",
                },
                {
                    label: "单价百分比",
                    value: "3",
                    key: "unitPercent",
                },
                {
                    label: "总价百分比",
                    value: "4",
                    key: "totalPercent",
                },
                {
                    label: "递增价格",
                    value: "5",
                    key: "increasePrice",
                },
                {
                    label: "组合单价",
                    value: "6",
                    key: "composeUnitPrice",
                },
                {
                    label: "面积递增价格",
                    value: "7",
                },
            ],
        };
    },
    computed: {
        textConfig() {
            return {
                freeText: this.freeText ? this.freeText : this.lang.NoDiscount,
            };
        },
        device() {
            return this.$store.state.device;
        },
        lang() {
            return this.$store.getters.lang.quote || {};
        },
        proType() {
            return this.$store.state.proType;
        },
        newQtyList(){
            let result = getQuoteTime(this.bindValue.childList,this.calculateData,this.proType),
                    originShowSmallPrice =  this.$store.state.showSmallPrice;
            this.$store.commit("setShowSmallPrice", result.newShowSmallPrice);
            if(result.newShowSmallPrice !== originShowSmallPrice){
                this.$Bus.$emit("clearDiscount");
            }
            return result.arr;
        }
    },
    methods: {
        selectDiscount(item, citem, cindex) {
            this.$emit("selectDiscount", item, citem, cindex);
        },
        hasId(id, arr) {
            if (!arr) {
                return false;
            }
            return arr.find((item) => {
                return item.id === id;
            });
        },
    }
};
</script>

<style scoped lang="scss">
.dateBox {
    display: grid;
    justify-content: space-between;
    grid-column-gap: 27px;
    column-gap: 27px;
    grid-template-columns: repeat(4, 1fr);
    grid-row-gap: 20px;
    row-gap: 20px;

    .item-wrap {
        display: flex;
        flex-direction: column;
        border: 1px solid transparent;
        border-radius: 6px;
        padding: 15px;
        cursor: pointer;
        transition: all 0.3s;

        @media (any-hover: hover) {
            &:hover {
                border-color: $color-primary;
                box-shadow: 0 3px 4px 0 #ccc;
            }
        }

        &.discount-item0 {
            background-color: #d6f9ea;
            height: 100%;
        }

        &.discount-item1 {
            background-color: #d5ebfc;
            height: 100%;
        }

        &.discount-item2 {
            background-color: #fff2dc;
            height: 100%;
        }

        &.discount-item3 {
            background-color: #ffe7db;
            height: 100%;
        }

        .top {
            display: flex;
            align-items: center;
            font-weight: 700;

            .cus-circle {
                display: flex;
                justify-content: center;
                align-items: center;
                width: 18px;
                height: 18px;
                margin-right: 20px;
                background: #ffffff;
                border: 1px solid #aaaeb3;
                border-radius: 50%;

                .inner-circle {
                    width: 6px;
                    height: 6px;
                    background: #dae0e5;
                    border-radius: 50%;
                }
            }
        }

        .bottom {
            flex: 1;
            margin-top: 8px;
            margin-left: 27px;
            font-size: 16px;
        }

        @media (any-hover: hover) {
            &:hover {
                border-color: $color-primary;
            }
        }
    }

    .item-wrap {
        &.active {
            border-color: $color-primary !important;

            .top {
                .circle2 {
                    border-color: $color-primary;
                    background: $color-primary;

                    &::after {
                        background-color: #fff;
                    }
                }
            }
        }

        @media (any-hover: hover) {
            &:hover {
                .top {
                    .circle2 {
                        border-color: $color-primary;
                        background: $color-primary;

                        &::after {
                            background-color: #fff;
                        }
                    }

                    > span {
                        color: $color-primary;
                    }
                }
            }
        }
    }

    .price-box {
        grid-column: 1/3;
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        font-size: 12px;
        font-family: Arial;
        row-gap: 10px;
        padding-top: 10px;
        border-top: 1px dashed #dbdbdb;
        position: relative;

        > div:nth-child(2n) {
            text-align: right;
        }
    }

    @media screen and (max-width: 767px) {
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;

        .item-wrap {
            padding: 10px;

            .bottom {
                margin: 0;
                margin-top: 14.5px;
                font-size: 12px;
            }
        }
    }
}

.circle2 {
    display: flex;
    justify-content: center;
    align-items: center;
    border-color: $color-primary;
    background-color: $color-primary;
    flex-shrink: 0;
    width: 18px;
    height: 18px;
    border: 1px solid #afb1b3;
    border-radius: 50%;
    margin-right: 10px;
    background-color: #fff;

    &::after {
        background-color: #ffffff;
        content: "";
        width: 6px;
        height: 6px;
        background: #d4d7d9;
        border-radius: 50%;
    }

    @media screen and (max-width: 767px) {
        width: 15px;
        height: 15px;

        &::after {
            width: 5px;
            height: 5px;
        }
    }
}
</style>