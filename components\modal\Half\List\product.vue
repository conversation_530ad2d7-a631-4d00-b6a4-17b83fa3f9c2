<template>
	<div class="good-item">
		<div class="adBox" v-if="productData.isEnable && !productData.galleryUrl" @click="goAdLink(productData.advertLink)"
			:style="{ height: copyAdBoxHeight }">
			<div class="adVideo" v-if="productData.type == 1">
				<div style="width: 100%; height: 100%">
					<video width="100%" height="100%" id="video" autoplay="autoplay" muted="muted" loop="loop"
						webkit-playsinline="true" playsinline="true" x5-video-player-type="h5-page"
						x5-video-orientation="landscape|portrait">
						<source :src="isMobile ? productData.mbImg : productData.pcImg" />
					</video>
				</div>
			</div>
			<div class="adImg" v-else>
				<pic width="100%" height="100%" cover :src="isMobile ? productData.mbImg : productData.pcImg"
					:alt="productData.content" :title="productData.title" />
			</div>
		</div>
		<div class="clickAdBox" :style="{ height: copyAdBoxHeight }"
			v-else-if="productData.isEnable && productData.galleryUrl">
			<div class="introTitle">
				<b class="icon-DESIGN"></b><span>{{ productData.title }}</span>
			</div>
			<pic width="100%" height="100%" contain :src="isMobile ? productData.mbImg : productData.pcImg"
				:alt="productData.content" :title="productData.title"></pic>
			<div class="introContent">
				{{ productData.content }}
			</div>
			<div class="clickAdBtn">
				<div class="btnItem galleryBtn" @click="openGallery">{{ langSemiCustom.photoGallery }}
					>></div>
				<div class="btnItem quoteBtn" @click="toQuote(productData.advertLink)">{{ langSemiCustom.freeQuote }}</div>
			</div>
		</div>
		<div style="position: relative; width: 100%; height: 100%" v-else>
			<productHead :isDiv="isDiv" :productData="productData" @toDetail="toDetail">
				<div class="goods">
					<div class="good-img">
						<div class="imgWrap">
							<div class="priceBtn" v-show="productData.discount && isMobile">
								<div>
									<span>{{ langSemiCustom.save }}</span>
									{{ (productData.discount * 100).toFixed(0) + " %" }}
								</div>
							</div>
							<pic width="100%" height="100%" :src="getSrc(productData)" :alt="productData.name"
								:title="productData.name" />
							<div v-if="!isMobile && productData.isDevise != 1" @click.stop.prevent="copy(productData.productSku)" class="itemNo">
								{{ productData.productSku }} <b class="icon-a-fuzhi1"></b>
							</div>
						</div>
					</div>
					<div class="good-back">
						<div class="good-color"
							v-if="productData.productParamList && productData.productParamList.length && productData.isDevise != 1">
							<div class="good-color-item" v-show="productData.sceneImg"
								:class="{ active: productData.selectedColorIndex === -1 }" title="Gallery"
								@click.stop="toSceneImg($event, productData)">
								<span class="color-circle"></span>
							</div>
							<div class="good-color-item" :class="{ active: productData.selectedColorIndex === cindex }"
								@click="selectGoodColor($event, itemIndex, cindex, citem)" v-show="cindex < showColorNum"
								:title="citem.colorAlias" v-for="(citem, cindex) in productData.productParamList" :key="cindex">
								<span class="color-circle"
									:style="{ background: citem.colorSecondary ? `linear-gradient(-45deg, ${citem.colorCode} 50%, ${citem.colorSecondary} 50%)` : citem.colorCode }"></span>
							</div>
							<span v-if="productData.productParamList && productData.productParamList.length - showColorNum > 0">+{{
								productData.productParamList.length - showColorNum }}</span>
						</div>
						<div class="good-info">
                            <div v-if="isMobile && productData.isDevise != 1" @click.stop.prevent="copy(productData.productSku)" class="itemNo">
                                {{ productData.productSku }} <b class="icon-a-fuzhi1"></b>
                            </div>
							<div class="productTitle clearfix">
								<div class="priceBtn" v-show="productData.discount && !isMobile">
									<div>
										<span>{{ langSemiCustom.save }}</span>
										{{ (productData.discount * 100).toFixed(0) + " %" }}
									</div>
								</div>
								<h3 :data-name="productData.name" :title="productData.name">
									{{ productData.name }}
								</h3>
							</div>
							<div class="xin">
								<template v-if="productData.commentLevel > 0">
									<v-rating :value="productData.commentLevel" background-color="#cccccc" size="16" color="#EB7100"
										half-increments readonly dense length="5"></v-rating>
									<span class="xinCommentLevel" v-if="productData.commentLevel">{{ productData.commentLevel }}</span>
									<span class="xinCommentNum" v-if="productData.commentNum" style="color: rgb(182, 177, 177)"> ({{
										productData.commentNum }}) </span>
								</template>
							</div>
							<div class="priceBox">
								<div class="price" v-if="!productData.discount">
									<div
										v-if="isStockPage && $store.state.proTheme == '11' && productData.highestPrice == productData.lowestPrice">
										<span>{{ langSemiCustom.price }}:</span>
										<label>
											<CCYRate :price="productData.lowestPrice"></CCYRate>
										</label>
									</div>
									<div v-else>
										<label>
											<CCYRate :price="productData.lowestPrice"></CCYRate>
										</label>
										<span v-show="productData.highestPrice" style="color: #333333; font-weight: 400">-</span>
										<label v-show="productData.highestPrice">
											<CCYRate :price="productData.highestPrice"></CCYRate>
										</label>
									</div>
								</div>
								<div class="discountPrice" v-else>
									<div class="nowPrice">
										<label>
											<CCYRate :price="productData.lowestDiscountPrice"></CCYRate>
										</label>
										<span v-show="productData.highestDiscountPrice" style="color: #333333; font-weight: 400">-</span>
										<label v-show="productData.highestDiscountPrice">
											<CCYRate :price="productData.highestDiscountPrice"></CCYRate>
										</label>
									</div>
									<div class="oldPrice">
										<span>
											<CCYRate :price="productData.lowestPrice"></CCYRate>
										</span>
										<span style="text-decoration: line-through" v-show="productData.highestPrice">-</span>
										<span v-show="productData.highestPrice">
											<CCYRate :price="productData.highestPrice"></CCYRate>
										</span>
									</div>
								</div>
							</div>
							<div class="productTag" v-show="tagList.length">
								<div class="tag" v-for="(item, index) in tagList" :key="index">{{ item }}</div>
							</div>
							<div class="minQuantity">
								{{ langSemiCustom.MiniQty + " " + productData.lowestPurchaseQuantity }}
							</div>
						</div>
						<div class="good-collection" @click.stop="goCollection($event, productData)">
							<v-icon class="collectionXin" v-if="!productData.isCollection">mdi-heart-outline</v-icon>
							<v-icon class="collectionXin isActive" v-else>mdi-heart</v-icon>
						</div>
					</div>
					<div class="frameBox"
						v-show="isMobile && productData.productParamList && productData.productParamList.length && productData.isDevise != 1">
					</div>
				</div>
			</productHead>
		</div>
	</div>
</template>

<script>
import { addCollection, deleteConllectionByUserId } from "@/api/web";
import productHead from "@/components/modal/Half/List/productHead.vue";
import {copyContent} from "@/utils/utils";
export default {
	name: "goodItem",
	components: { productHead },
	props: {
		productData: {
			type: Object,
			default: () => ({}),
		},
		itemIndex: {
			type: [Number, String],
		},
		isStockPage: {},
		parentCateId: {},
		cateId: {},
		productList: {
			type: Array,
			default: () => [],
		},
		isDiv: {
			type: Boolean,
			default: false,
		},
		adBoxHeight: {
			type: [Number,String],
			default: 'auto'
		},
	},
	data() {
		return {
			showColorNum: 7,
		};
	},
	watch: {},
	computed: {
		langSemiCustom() {
			return this.$store.getters.lang?.semiCustom;
		},
		isMobile() {
			return this.$store.state.device === "mb";
		},
		isLogin() {
			return this.$store.getters.isLogin;
		},
		userId() {
			return this.$store.state.userInfo?.id;
		},
		tagList() {
			if (!this.productData.productLabel || this.productData.productLabel.length <= 0) return [];
			return this.productData.productLabel.split(",");
		},
		copyAdBoxHeight() {
			if (this.productData.booth == 2 && this.isMobile) {
				const numberHeight = parseInt(String(this.adBoxHeight).replace('px', ''));
				const doubledHeight = numberHeight * 2;
				return `${doubledHeight}px`;
			}
			return this.adBoxHeight
		}
	},
	methods: {
        copy(text){
            copyContent(text);
            this.$toast.success("Copied successfully")
        },
		toDetail(url) {
			this.$emit("toDetail", url);
		},
		//点击广告跳转链接
		goAdLink(url) {
			window.open(url);
		},
		openGallery() {
			let data = {
				modal: "modalCustom",
				api: "listPageImgByPageId",
				showArrow: "icon-a-icon-jt4zhuanhuan",
				class: "img-swiper",
				showIndexPage: true,
				showTab: true,
				dialog: true,
				scroll: 1
			}
			this.$store.commit("setMask", data);
		},
		toQuote(adUrl) {
			let url = `${adUrl}?type=quoteIframe`;
			let data = {
				modal: "modalQuoteDialog",
				quoteUrl: url,
			};
			this.$store.commit("setMask", data);
		},
		getSrc(item) {
			if (item.selectedColorIndex > -1) {
				return item.showImgSrc + "?x-oss-process=image/resize,p_40";
			} else {
				return item.showImgSrc;
			}
		},
		toSceneImg(e, item) {
			e.stopPropagation();
			e.preventDefault();
			item.showImgSrc = item.sceneImg;
			item.selectedColorIndex = -1;
		},
		parseJSON(str) {
			return str ? JSON.parse(str) : [];
		},
		selectGoodColor(e, ind1, ind2, citem) {
			e.stopPropagation();
			e.preventDefault();
			this.$set(this.productList[ind1], "selectedColorIndex", ind2);
			this.$set(this.productList[ind1], "showImgSrc", this.parseJSON(citem.imgJson)[0]?.url);
		},
		toDetail2(item) {
			const routeOptions = {
				path: item.productRouting,
			};
			const url = this.$router.resolve(routeOptions).href;
			window.open(url, "_blank");
		},
		goCollection(e, item) {
			e.stopPropagation();
			e.preventDefault();
			let isCollection = item.isCollection;
			if (!this.isLogin) {
				this.$store.commit("setLogin", "login");
				return;
			}
			if (isCollection) {
				deleteConllectionByUserId({
					userId: this.userId,
					productId: item.id,
				}).then((res) => {
					item.isCollection = false;
					this.$forceUpdate();
				});
			} else {
				let productData = {
					data: { item_id: this.productData.productSku, item_name: this.productData.name },
					value: 0,
				};
				addCollection(
					{
						userId: this.userId,
						website: 1,
						cateId: this.parentCateId || this.cateId,
						productId: item.id,
					},
					productData
				).then((res) => {
					item.isCollection = true;
					this.$forceUpdate();
				});
			}
		},
	},
	created() { },
	mounted() { },
};
</script>

<style scoped lang="scss">
.good-item {
	min-width: 0;
	position: relative;
	border: 1px solid #f0f0f0;
	border-radius: 2px;
	background-color: #fff;
	transition: all 0.3s;
	overflow: hidden;
	user-select: text;
	cursor: pointer;

	@media (any-hover: hover) {
		&:hover {
			--hoverBack: #e6e6e6;
			box-shadow: 0 6px 5px var(--hoverBack), 0 -1px 3px var(--hoverBack);
		}
	}

	a {
		display: flex;
		flex-direction: column;
		height: 100%;
		width: 100%;
	}

	.adBox {
		height: 0;

		@include respond-to(ipad) {}
	}

	.adImg,
	.adVideo {
		overflow: hidden;
		display: flex;
		justify-content: center;
		align-items: center;
		height: 100%;

		img {
			object-fit: fill;
		}
	}

	.adVideo {
		video {
			height: 100%;
			width: 100%;
			object-fit: contain;
		}
	}

	.clickAdBox {
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 0.8em;
		padding: 4% 8%;
		background-color: #3d3d3d;
		color: #fff;

		@media (any-hover: hover) {
			&:hover {
				--hoverBack: #e6e6e6;
				box-shadow: 0 6px 5px var(--hoverBack), 0 -1px 3px var(--hoverBack);
			}
		}

		@include respond-to(mb) {
			padding: 2% 6%;
		}

		.introTitle {
			flex-shrink: 0;
			display: flex;
			align-items: center;
			justify-content: center;
			gap: 0.4em;
			font-size: 14px;
			width: 100%;

			b {
				flex-shrink: 0;
				font-size: 24px;
			}

			span {
				min-width: 0;
				white-space: nowrap;
				text-overflow: ellipsis;
				overflow: hidden;
			}
		}

		img {
			min-height: 0;
			flex: 1;
			height: auto;
			display: inline-block;
			border-radius: 6px;
		}

		.introContent {
			flex-shrink: 0;
			font-size: 16px;
			font-weight: 700;
			word-break: break-word;
		}

		.clickAdBtn {
			flex-shrink: 0;
			width: 100%;
			display: flex;
			align-items: center;
			justify-content: center;
			column-gap: 10%;
			text-align: center;
			padding: 0 6%;

			.btnItem {
				flex: 1;
				font-size: 16px;
				width: max-content;
				padding: 0.5em 1.4em;
				border: 1px solid $color-primary;
				border-radius: 6px;
				white-space: nowrap;

				&:hover {
					transform: scale(1.08, 1.1);
				}
			}

			.galleryBtn {
				background-color: #fff;
				color: $color-primary;
			}

			.quoteBtn {
				background-color: $color-primary;
				color: #fff;
			}

			@include respond-to(mb) {
				padding: 0;

				.btnItem {
					font-size: 14px;
				}
			}
		}
	}


	@include respond-to(pad) {
		// max-height: 474px;
	}

	@include respond-to(mb) {
		&.inserted_element {
			grid-row: span 2;
		}
	}

	.itemNo {
		position: absolute;
		right: 12px;
		bottom: 12px;
		font-size: 11px;
		color: #fff;
		padding: 4px;
		user-select: text;
		z-index: 1;
		background: rgba(0, 0, 0, 0.36);
		border-radius: 2px;
        cursor: pointer;

		@include respond-to(mb) {
			position: relative;
            width: fit-content;
			top: 0;
			right: 0;
			bottom: auto;
			background-color: #fff;
			font-size: 12px;
			padding: 0;
			margin-bottom: 2px;
			color: #a7a1a1;
		}

		@media screen and (min-width: 750px) and (max-width: 1024px) {
			font-size: 14px;
		}
	}

	.priceBtn {
		display: flex;
		align-items: center;
		justify-content: space-evenly;
		flex-shrink: 0;
		height: 24px;
		background: linear-gradient(60deg, #ee4113 0%, #f3aa1e 100%);
		border-radius: 0px 6px 0px 6px;
		font-size: 12px;
		font-weight: bold;
		color: #fff;
		width: fit-content;
		column-gap: 8px;
		padding: 0 8px;

		.circle {
			flex-shrink: 0;
			position: relative;
			width: 20px;
			height: 20px;
			border-radius: 50%;
			background-color: #ffffff;

			&::before {
				content: "%";
				font-size: 12px;
				position: absolute;
				top: 50%;
				left: 50%;
				transform: translate(-50%, -50%);
				color: $color-primary;
			}
		}
	}

	.goods {
		min-width: 0;
		height: 100%;
		display: grid;
		grid-template-columns: 1fr;
		grid-template-rows: auto 1fr;
		position: relative;
		// box-shadow: 1px 1px 4px 0px rgba(0, 0, 0, 0.06);
		border-radius: 4px;
		background-color: #fff;
		transition: all 0.3s;
		cursor: pointer;

		.good-collection {
			position: absolute;
			top: 10px;
			left: 10px;

			b {
				color: #999999;
				font-size: 24px;
			}

			.collectionXin {
				&.isActive {
					color: $color-primary;
				}
			}

			@include respond-to(mb) {
				b {
					font-size: 20px;
				}
			}
		}

		.good-img {
			overflow: hidden;
			position: relative;
			aspect-ratio: 1;

			.imgWrap {
				position: relative;
				overflow: hidden;
				display: flex;
				justify-content: center;
				align-items: center;
				padding: 5px;

				::v-deep .v-image__image--cover {
					background-size: contain;
				}
			}
		}

		.good-back {
			display: flex;
			flex-direction: column;
			padding: 0 15px;

			.good-color {
				display: flex;
				flex-wrap: wrap;
				align-items: center;
				justify-content: flex-start;
				padding: 10px 0;

				.good-color-item {
					display: flex;
					justify-content: center;
					align-items: center;
					border: 1px solid transparent;
					padding: 2px;
					border-radius: 50%;
					margin-right: 6px;
					transition: all 0.3s;

					@include respond-to(mb) {
						margin-right: 6px;
					}

					&:hover {
						border-width: 1px;
						border-color: $color-primary;
					}

					.color-circle {
						display: inline-block;
						width: 12px;
						height: 12px;
						background: url("https://oss-static-cn.liyi.co/web/quoteManage/20231021/20230619xcxWJWKD_2054DQFx75.png") center/contain no-repeat;
						border-radius: 50%;
						outline: 1px solid #ccc;
					}
				}

				.good-color-item.active {
					border-color: $color-primary;
				}
			}

			.good-info {
				flex: 1;
				display: flex;
				flex-direction: column;
				justify-content: center;
				padding: 0 0 15px;
				transition: all 0.3s;

				.productTitle {
					font-size: 16px;
					height: 3em;
					display: -webkit-box;
					-webkit-box-orient: vertical;
					-webkit-line-clamp: 2;
					word-break: break-word;
					text-overflow: ellipsis;
					overflow: hidden;

					.priceBtn {
						float: left;
						margin-right: 4px;
					}

					h3 {
						display: inline;
						font-size: 16px;
						line-height: 1.5;
						word-break: break-word;
						text-overflow: ellipsis;
						// display: -webkit-box;
						// -webkit-box-orient: vertical;
						// -webkit-line-clamp: 2;
						/* 这里是超出几行省略 */
						overflow: hidden;
						user-select: auto !important;
						-moz-user-select: auto !important;
						-webkit-user-select: auto !important;
						-ms-user-select: auto !important;
						pointer-events: auto !important;
						cursor: auto;
					}
				}

				.buyTea {
					display: none;
				}

				.priceBox {
					.price {
						font-size: 18px;

						label {
							color: $color-primary;
							font-size: 18px;
							font-weight: bold;
						}
					}

					.discountPrice {
						display: flex;
						align-items: center;
						line-height: normal;
						column-gap: 4px;
						font-size: 16px;

						.nowPrice {
							flex-shrink: 0;
						}

						.oldPrice {
							font-size: 14px;
							color: #636363;

							span,
							label {
								font-size: 14px;
								text-decoration: line-through;
								color: #636363;
								font-weight: 400;
							}
						}

						span {
							color: $color-primary;
							font-weight: bold;
						}

						label {
							color: $color-primary;
							font-size: inherit;
							font-weight: bold;
						}
					}
				}

				.productTag {
					flex: 1;
					display: flex;
					flex-wrap: wrap;
					align-items: flex-start;
					gap: 6px;
					margin: 6px 0;

					.tag {
						//flex-shrink: 0;
						font-style: italic;
						font-size: 14px;
						color: var(--tag-color, var(--color-primary));
						background-color: var(--tag-color-lighten, var(--color-second));
						border-radius: 4px;
						padding: 4px 6px;
					}

					@media screen and (min-width: 750px) and (max-width: 1024px) {
						flex-grow: 0;
					}
				}

				.minQuantity {
					font-size: 14px;
					color: #666666;
				}

				.des {
					color: #999999;
					font-size: 12px;
					word-break: break-word;
					text-overflow: ellipsis;
					display: -webkit-box;
					-webkit-box-orient: vertical;
					-webkit-line-clamp: 1;
					/* 这里是超出几行省略 */
					overflow: hidden;
					line-height: 2;
				}

				.xin {
					display: flex;
					align-items: center;
					height: 24px;
					margin-left: -2px;
					margin-bottom: 4px;

					span {
						margin-left: 4px;
						font-size: 14px;
						padding-top: 4px;
					}

					@include respond-to(mb) {
						margin: 2px 0;

						::v-deep .v-rating .v-icon {
							font-size: 20px;
						}

						span {
							font-size: 12px;
						}
					}
				}
			}
		}

		@include respond-to(mb) {
			display: grid;
			grid-template-columns: 40% 60%;

			.good-img {
				position: relative;

				.imgWrap {
					padding: 0;
					height: auto;
					position: relative;

					.priceBtn {
						position: absolute;
						right: 0;
						bottom: 0;
						height: 22px;
					}
				}
			}

			.good-back {
				padding: 10px 10px 0;

				.good-color {
					position: absolute;
					width: 100%;
					left: 0;
					bottom: 0;
					padding: 5px;

					@media screen and (min-width: 750px) and (max-width: 1024px) {
						bottom: -2px;
						left: 8px;
						min-width: 340px;
						position: absolute;
						width: 100%;
					}

					.good-color-item {
						&:hover {
							background-color: transparent;
						}

						.color-circle {
							width: 22px;
							height: 22px;
						}
					}
				}

				.good-info {
					padding: 0;

					.productTitle {
						font-size: 14px;

						h3 {
							font-size: 14px;
						}
					}

					.priceBox {
						.price {
							margin: 0 0 8px 0;
							font-size: 13px;

							label {
								font-size: 14px;
							}
						}

						.discountPrice {
							margin: 0;
							font-size: 13px;

							.oldPrice {
								font-size: 12px;

								span,
								label {
									font-size: 12px;
								}
							}

							label {
								font-size: 14px;
							}
						}
					}
				}
			}

			.frameBox {
				grid-column: span 2;
				height: 40px;
			}
		}

		@media screen and (min-width: 750px) and (max-width: 1025px) {
			.good-img {
				max-height: 280px;

				.priceBtn {
					left: auto;
					right: 0;
					top: auto;
					bottom: 0;
					height: 20px;
					width: fit-content;
					font-weight: blod;
					border-radius: 0 8px 0 8px;

					.circle {
						display: block;
					}
				}
			}

			.good-back {
				padding: 0 8px;

				.good-info {
					.priceBox {
						.price {
							font-size: 16px;

							label {
								font-size: 14px;
							}
						}

						.discountPrice {
							margin: 0;
							font-size: 16px;

							.oldPrice {
								font-size: 14px;

								span,
								label {
									font-size: 14px;
								}
							}

							label {
								font-size: 16px;
							}
						}
					}

					.productTitle {
						font-size: 16px;

						h3 {
							font-size: 16px;
						}
					}

					.minQuantity {
						font-size: 14px;
					}

					.productTag {
						.tag {
							font-size: 14px;
						}
					}
				}
			}
		}
	}
}
</style>
