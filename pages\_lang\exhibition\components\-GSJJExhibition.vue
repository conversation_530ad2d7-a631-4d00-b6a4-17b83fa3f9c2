<template>
	<div class="modal-box exhibition-list">
		<GSJJBreadcrumb :items="breadcrumb" @click.native="openMenu" />

		<div class="exhibition-list__main">
			<div class="left" v-show="menuShow" @click="closeMenu">
				<GSJJMenuList :cate-list="cateList" />
			</div>

			<div class="right">
				<div class="page-title">
					<h1>{{ currentCate ? currentCate.cateName : lang.products }}</h1>
				</div>

				<form class="filter" method="get" @submit.prevent="onSubmit">
					<input type="text" :placeholder="lang.keywords" autocomplete="off" name="Keyword" :value="currentKeyword" />
					<input type="submit" value="Go" />
				</form>

				<div class="prod-list">
					<nuxt-link class="prod-item" v-for="item in records" :key="item.id" :to="'/' + item.pageUrlPrefix + '/exhibit/' + item.pageUrl">
						<Pic class="pic" :src="item.picPathZero" :alt="item.galleryName" />
						<div class="title">{{ item.galleryName }}</div>
					</nuxt-link>
				</div>

				<v-pagination :value="currentPage" :length="pages" :total-visible="8" @input="pageChange"></v-pagination>
			</div>
		</div>
	</div>
</template>

<script>
import { findInTreeArray } from "@/utils/utils";
import GSJJBreadcrumb from "./-GSJJBreadcrumb.vue";
import GSJJMenuList from "./-GSJJMenuList.vue";

export default {
	name: "GSJJExhibition",

	components: { GSJJBreadcrumb, GSJJMenuList },

	props: ["galleryList", "cateList", "currentCate"],

	data() {
		return {
			visible: false,
		};
	},

	computed: {
		lang() {
			return this.$store.getters.lang?.exhibition;
		},

		currentPage() {
			const pageNum = Number(this.$route.params.page);
			if (isNaN(pageNum) || pageNum < 1 || this.$route.query.Keyword) {
				return 1;
			}
			return pageNum;
		},

		currentKeyword() {
			return this.$route.query.Keyword || "";
		},

		isMobile() {
			return this.$store.getters.isMobile;
		},

		breadcrumb() {
			let list = [];
			if (this.currentCate) {
				list.unshift({
					text: this.currentCate.cateName,
					link: `/${this.currentCate.pageUrlPrefix}/s/${this.currentCate.pageUrl}`,
				});
				if (this.currentCate.parentId) {
					const parentCate = findInTreeArray(this.cateList, (item) => item.id === this.currentCate.parentId);
					if (parentCate) {
						list.unshift({
							text: parentCate.cateName,
							link: `/${parentCate.pageUrlPrefix}/s/${parentCate.pageUrl}`,
						});
					}
				}
			}
			let result = [{ text: this.lang.home, link: "/" }, { text: this.lang.gallery, link: "/Exhibition" }, ...list];
			if (this.isMobile) {
				result = result.slice(1).map((item) => ({ ...item, link: null }));
			}
			return result;
		},

		menuShow() {
			return !this.isMobile || this.visible;
		},

		records() {
			return this.galleryList?.records || [];
		},

		pages() {
			return this.galleryList?.pages || 1;
		},
	},

	methods: {
		openMenu() {
			if (!this.isMobile) return;
			this.visible = true;
		},

		closeMenu() {
			if (!this.isMobile) return;
			this.visible = false;
		},

		getFormQueryParams(form) {
			const formData = new FormData(form);
			return Object.fromEntries(Array.from(formData.entries()).filter(([_, value]) => value));
		},

		pageChange(targetPage) {
			// 避免重复导航到相同页面
			if (targetPage === this.currentPage) return;

			const routeName = this.$route.name;
			const routeParams = this.$route.params;
			const routeQuery = this.$route.query;
			let config = {
				path: `${this.$route.path}/${targetPage}.html`,
				query: { ...routeQuery },
			};
			if (routeName.endsWith("-page")) {
				config = {
					name: routeName,
					params: { ...routeParams, page: targetPage },
					query: { ...routeQuery },
				};
			}
			this.$router.push(config);
		},

		async onSubmit(event) {
			const formQuery = this.getFormQueryParams(event.target);

			await this.$router.push({
				path: this.$route.path,
				query: formQuery,
			});
		},
	},
};
</script>

<style lang="scss" scoped>
.exhibition-list {
	padding-top: 1rem;
	z-index: auto;
}

.exhibition-list__main {
	display: grid;
	grid-template-columns: 21% 78%;
	justify-content: space-between;

	.page-title {
		margin-bottom: 1.875rem;
		padding: 0.5rem 1.5rem;
		background-color: #f7f7f7;
		h1 {
			line-height: 18px;
			font-size: 14px;
			font-weight: 700;
		}

		@include respond-to(mb) {
			margin-bottom: 4px;
		}
	}

	.filter {
		padding: 8px 10px;
		display: flex;
		gap: 4px;
		align-items: center;
		font-size: 12px;
		border: 1px solid #d4d4d4;
		background-color: #f5f5f5;

		input[type="text"] {
			width: 178px;
			height: 26px;
			padding: 2px 5px;
			border: 1px solid #ddd;
			background-color: #ffff;
		}
		input[type="submit"] {
			width: 40px;
			height: 24px;
			font-weight: 700;
			color: #333;
			border: 1px solid #ddd;
			border-radius: 3px;
			background-color: #eee;
		}

		@include respond-to(mb) {
			display: none;
		}
	}

	.prod-list {
		margin-top: 1.25rem;
		margin-bottom: 1.25rem;
		display: grid;
		gap: 1.25rem;
		grid-template-columns: repeat(3, 1fr);

		@include respond-to(mb) {
			margin-top: 0.5rem;
			margin-bottom: 1rem;
			gap: 0.5rem;
			grid-template-columns: repeat(2, 1fr);
		}
	}

	.prod-item {
		background-color: #fafafa;
		.pic {
			padding: 0.625rem;
			aspect-ratio: 1;
			background-color: #fff;
		}
		.title {
			padding: 0.5rem;
			font-size: 12px;
		}
		@include respond-to(mb) {
			border: 1px solid #d0dbe0;

			.pic {
				padding: 0;
			}
		}
	}

	::v-deep {
		.v-pagination__item--active {
			background: #333 !important;
		}
	}

	@include respond-to(mb) {
		grid-template-columns: 100%;
	}
}

.left {
	@include respond-to(mb) {
		position: fixed;
		z-index: 999;
		left: 0;
		right: 0;
		top: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.5);

		.category {
			position: absolute;
			width: 60vw;
			top: 26vw;
			bottom: 0;
			overflow-y: auto;
		}
	}
}

.breadcrumb {
	@include respond-to(mb) {
		&::after {
			content: "";
			display: block;
			border: solid #555;
			border-width: 0 1px 1px 0;
			padding: 4px;
			transform: rotate(45deg);
			margin-top: -4px;
			margin-left: 10px;
		}
	}
}
</style>
