<template>
	<div class="mb-4" :class="stepData.styleName">
		<half-design-litter-title :index="stepData.id" :selectedValue="shape" :data-name="`${stepData.styleClass + '_' + stepData.id}`" style="margin: 4px 0" :stepTitle="stepData.minStepTitle" v-show="stepData.attributeTitle">{{ stepData.attributeTitle }} </half-design-litter-title>
		<slot name="stepText"></slot>
		<div class="step-content" v-if="!stepData.isHideStep || (stepData.isHideStep && stepData.productParamList.length > 1)">
			<div class="step-item" :class="{ active: index === selectIndex }" v-for="(step, index) in stepData.productParamList" :key="index" @click="selectStep(step, index)">
				<div class="imgWrap">
					<img :src="step.sizeImg" :alt="step.imgAltTitle" :title="step.imgAltTitle" />
				</div>
				<div class="d-flex-center text-center name">
					<div class="text-truncate">
						<div class="nameText">
							<span class="valueNameText">{{ step.valueName }}</span>
							<div class="questionMark" v-show="step.remark.length > 0">
								<v-tooltip bottom>
									<template v-slot:activator="{ on, attrs }">
										<v-icon v-bind="attrs" v-on="on" size="18px"> mdi-help-circle-outline </v-icon>
									</template>
									<div class="text-center" style="display: flex; align-items: start" v-show="step.remark">
										<div style="text-align: center; color: #fff; line-height: 22px; font-size: 13px; word-break: break-word; white-space: pre-line">
											{{ step.remark }}
										</div>
									</div>
								</v-tooltip>
							</div>
						</div>
					</div>
				</div>
				<half-design-check-icon class="absolute-top-right check-icon"></half-design-check-icon>
			</div>
		</div>
		<div class="errorTip" ref="errorTip">
			<v-alert dense outlined type="error">
				{{ langSemiCustom.errorTip }}
			</v-alert>
		</div>
	</div>
</template>

<script>
export default {
	name: "Style3-1",
	inject: ["getUnit"],
	components: {},
	props: {
		stepData: {
			type: Object,
			default: () => ({}),
		},
	},
	data() {
		return {
			selectIndex: -1,
			selectItem: null,
			showFree: false,
		};
	},
	watch: {},
	computed: {
		shape() {
			return this.selectItem?.valueName;
		},
		langSemiCustom() {
			return this.$store.getters.lang?.semiCustom;
		},
	},
	methods: {
		clearData(state, areaIndex) {
			this.selectIndex = areaIndex;
			this.selectItem = null;
			if (areaIndex < 0) return;
			if (this.stepData && this.stepData.productParamList && this.stepData.productParamList.length > 0) {
				let item = this.stepData.productParamList[areaIndex];
				this.selectStep(item, areaIndex, state);
			}
		},
		selectStep(item, index, state = false) {
			this.selectIndex = index;
			this.selectItem = item;
			this.$emit("selectStep", {
				type: this.stepData.attributeFlag,
				data: item,
				id: this.stepData.id,
				firstSelect: state,
			});
		},
		selectDefault(num) {
			if (this.stepData.productParamList.length) {
				this.stepData.productParamList[0].inputNum = +num;
				this.selectStep(this.stepData.productParamList[0], 0, true);
			}
		},
	},
	created() {},
	mounted() {
		this.$Bus.$on("selectDefaultSizeStep", this.selectDefault);
	},
	beforeDestroy() {
		this.$Bus.$off("selectDefaultSizeStep", this.selectDefault);
	},
};
</script>

<style scoped lang="scss">
@import "~assets/css/half.scss";

.step-content {
	display: grid;

	.step-item {
		position: relative;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		@include step-default;
		min-width: 0;
		cursor: pointer;
		color: #333333;
		overflow: hidden;

		.text-truncate {
			display: flex;
			align-items: center;
			font-family: Roboto;
			font-weight: 400;
			font-size: 14px;
			.nameText {
				display: flex;
				align-items: center;
			}
		}

		.name {
			margin-top: 4px;
		}

		.questionMark {
			display: none;
		}

		.priceText {
			display: none;
		}

		.check-icon {
			display: none;
		}

		.zoom-icon {
			display: none;
		}
	}
}

.style1 .step-content {
	grid-template-columns: repeat(5, 1fr);
	column-gap: 10px;

	.step-item {
		&.active {
			.check-icon {
				display: flex;
			}
			.v-icon {
				color: $color-primary;
			}
		}

		.imgWrap {
			@include flex-center;
			max-height: 80px;
		}

		.text-truncate {
			flex-direction: column;
			justify-content: center;
			row-gap: 4px;

			.priceText {
				display: flex;

				&.redText {
					color: #de3500;
				}
			}
		}
		.questionMark {
			margin-left: 4px;
			display: flex;
		}
	}
}

@include respond-to(mb) {
	.step-content {
		column-gap: 5px !important;
		.step-item {
			padding: 6px;
			&.active {
				padding: 5px;
			}

			@media (any-hover: hover) {
				&:hover {
					padding: 5px;
				}
			}

			.text-truncate {
				font-size: 12px;
				row-gap: 2px !important;
				.valueNameText {
					white-space: wrap;
					word-wrap: break-word;
				}
			}
		}
	}
	.style1 .step-content {
		grid-template-columns: repeat(3, 1fr);
		gap: 5px;
	}
}
</style>
