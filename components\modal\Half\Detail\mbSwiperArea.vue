<script>
import VideoPlayer from "@/components/HalfDesign/VideoPlayer.vue";
import { isImageType, isVideoType } from "@/utils/utils";

export default {
	inject: ["getCurrentAreaParam", "getStepArr", "canvas"],
	props: {
		isBig: {
			type: Boolean,
		},
		productInfo: {
			type: Object,
		},
		mobileCanvasWidth: {
			type: Object,
		},
		editStatus: {
			type: Boolean,
		},
		loadTempData: {
			type: Boolean,
		},
		firstImg: {
			type: String,
		},
		isMask: {
			type: Boolean,
		},
		currentStep: {
			type: Number,
		},
		showThreeD: {
			type: <PERSON>olean,
		},
		maskContent: {
			type: String,
		},
		isCufflinks: {
			type: Boolean,
			default: false,
		},
		colorParams: {
			type: Object,
			default: () => ({}),
		},
	},
	data() {
		return {
			currentIndex: 0,
			currentIndex2: 0,
			newImgList: [],
			isInit360: false,
			showColorSwiper: false,
			myswiper1: null,
			myswiper2: null,
			myswiper3: null,
			myswiper4: null,
			startX: null,
			isNormalSwiperEnd: false,
			isBigSwiperStart: false,
			designStepIndex: -1,
			sectionObserver: {},
			fixedFrist: false,
			isCanvasFixed: false,
			originalCanvasSize: { width: 0, height: 0 },
			observerInitialized: false, // 标记观察器是否已初始化
			canvasSnapshot: null, // 保存画布快照
		};
	},
	components: { VideoPlayer },
	watch: {
		async colorImgList(val) {
			if (this.isMobile) {
				this.$nextTick(() => {
					this.initSwiper2();
				});
			}
		},
		editStatus(val) {
			if (val) {
				this.$emit("update:showThreeD", false);
				// 延迟设置观察器，避免立即触发回调
				this.$nextTick(() => {
					setTimeout(() => {
						this.setFixCanvas();
					}, 100);
				});
			} else {
				// 退出编辑状态时清理观察器和恢复画布
				this.destroyObservers();
				if (this.isCanvasFixed) {
					this.unfixCanvas();
				}
			}
		},
	},
	computed: {
		isMobile() {
			return this.$store.state.device === "mb";
		},
		langSemiCustom() {
			return this.$store.getters.lang?.semiCustom;
		},
		hasRotateImg() {
			if (!this.productInfo.rotateImg) {
				return false;
			}
			return JSON.parse(this.productInfo.rotateImg).length;
		},
		currentArea() {
			return this.getCurrentAreaParam();
		},
		colorImgList() {
			if (this.isCufflinks && this.hasColorSwiper) {
				return this.colorParams?.productParamList?.map((item) => {
					return { url: item.sizeImg };
				});
			}
			if (!this.currentArea) return [];
			const colorUrlList = [];
			const areaId = this.currentArea.attributeValueId;
			if (Array.isArray(this.colorParams?.productParamList)) {
				this.colorParams.productParamList.forEach((item) => {
					if (!item.imgJson) return;
					try {
						const imgData = JSON.parse(item.imgJson);
						if (imgData && Array.isArray(imgData)) {
							imgData.forEach((img) => {
								if (img.attributeValueId === areaId) {
									colorUrlList.push({ url: img.url });
								}
							});
						}
					} catch (e) {
						console.error("JSON 解析失败_colorSwiper:", item.imgJson);
					}
				});
			}

			return colorUrlList;
		},
		hasColorSwiper() {
			return Object.keys(this.colorParams).length > 0 && this.colorImgList.length > 0;
		},
		stepArr() {
			return this.getStepArr();
		},
		designStepId() {
			let id = 0;
			if (this.stepArr) {
				this.designStepIndex = this.stepArr.findIndex((item) => item.isDevise === 1);
				if (this.designStepIndex >= 0) id = this.stepArr[this.designStepIndex].id;
			}
			return id;
		},
		designStepNextId() {
			if (this.designStepId) {
				let nextIndex = this.designStepIndex + 1;
				let lastIndex = this.stepArr.length - 1;
				if (nextIndex != lastIndex) {
					return this.stepArr[nextIndex].id;
				}
			}
			return 0;
		},
		canvasWidth() {
			return this.parsePxValue(this.mobileCanvasWidth.width);
		},
		canvasHeight() {
			return this.parsePxValue(this.mobileCanvasWidth.height);
		},
	},
	methods: {
		zoom() {
			this.$emit("zoom");
		},
		initSwiper() {
			let _this = this;
			let num = "auto";
			if (this.isMobile) {
				if (this.hasColorSwiper) {
					num = 1;
				}
				if (this.hasColorSwiper && this.hasRotateImg) {
					num = 2;
				}
			}

			this.myswiper1 = new Swiper(this.$refs.swiper1, {
				slidesPerView: num,
				direction: "vertical",
				spaceBetween: 6,
				watchSlidesVisibility: true,
				watchSlidesProgress: true,
				grabCursor: true,
				breakpoints: {
					320: {
						direction: "horizontal",
					},
					750: {
						direction: "horizontal",
					},
					1001: {
						direction: "vertical",
					},
				},
				on: {
					click: (value) => {
						if (_this.showColorSwiper) _this.showColorSwiper = false;
						let type = _this.newImgList[value.clickedIndex]?.type;
						let pdtLeft = document.querySelector(".pdt-left");
						pdtLeft.classList.remove("isSticky");
						_this.$emit("update:editStatus", false);
						_this.$emit("update:showThreeD", false);
						if (type === "is360") {
							_this.initThreeD();
						}
					},
				},
			});
			this.myswiper2 = new Swiper(this.$refs.swiper2, {
				slidesPerView: 1,
				spaceBetween: 10,
				autoplay: false,
				grabCursor: true,
				observer: true,
				observeParents: true,
				navigation: {
					nextEl: ".normalSwiper.swiper-button-next",
					prevEl: ".normalSwiper.swiper-button-prev",
				},
				thumbs: {
					swiper: this.myswiper1,
				},
				on: {
					touchStart: function (swiper, event) {
						if (!_this.isMobile) return;
						if (event.touches.length > 0) {
							_this.startX = event.touches[0].clientX;
						}
					},
					touchMove: function (swiper, event) {
						if (!_this.isMobile) return;
						if (!_this.startX || !swiper.isEnd) return;
						const currentX = event.touches[0].clientX;
						const diffX = _this.startX - currentX;
						if (diffX > 15) {
							if (!_this.showColorSwiper && _this.hasColorSwiper) {
								setTimeout(() => {
									_this.showColorSwiper = true;
									_this.syncThumbs(_this.myswiper4, _this.myswiper3, 0);
								}, 500);
							}
							_this.startX = null;
						}
					},
					touchEnd: function (swiper, event) {
						if (!_this.isMobile) return;
						_this.startX = null;
					},
					slideChangeTransitionEnd: function (value) {
						_this.isNormalSwiperEnd = false;
						if (value.isEnd) _this.isNormalSwiperEnd = true;
						if (_this.showColorSwiper) _this.showColorSwiper = false;
						_this.currentIndex = value.activeIndex;
						let type = _this.newImgList[value.activeIndex]?.type;
						_this.$emit("update:editStatus", false);
						_this.$emit("update:showThreeD", false);
						if (type === "is360") {
							_this.initThreeD();
						}
						_this.newImgList.forEach(function (v, i) {
							if (i === value.activeIndex) {
								if (_this.$refs["videoPlayer" + i]) {
									_this.$refs["videoPlayer" + i][0].player.play();
								}
							} else {
								if (_this.$refs["videoPlayer" + i]) {
									_this.$refs["videoPlayer" + i][0].player.pause();
								}
							}
						});
					},
				},
			});
		},
		initSwiper2() {
			let _this = this;
			this.myswiper4 = new Swiper(this.$refs.swiper4, {
				slidesPerView: 1,
				spaceBetween: 10,
				autoplay: false,
				grabCursor: true,
				observer: true,
				observeParents: true,
				navigation: {
					nextEl: ".bigColorSwiper.swiper-button-next",
					prevEl: ".bigColorSwiper.swiper-button-prev",
				},
				on: {
					slideChangeTransitionEnd: function (value) {
						_this.currentIndex2 = value.activeIndex;
						_this.$emit("update:editStatus", false);
						_this.colorImgList.forEach(function (_, i) {
							if (i === value.activeIndex) {
								if (_this.$refs["videoPlayer" + i]) {
									_this.$refs["videoPlayer" + i][0].player.play();
								}
							} else {
								if (_this.$refs["videoPlayer" + i]) {
									_this.$refs["videoPlayer" + i][0].player.pause();
								}
							}
						});
					},
				},
			});
		},
		initThreeD() {
			this.$emit("update:showThreeD", true);
			if (!this.isInit360) {
				let picList = JSON.parse(this.productInfo.rotateImg).map((i) => i.url);
				pdt360DegViewer("product360Preview", picList, true, true); //autoPlay
				this.isInit360 = true;
			}
		},
		isImageType,
		isVideoType,
		getVideoOptions(item, type) {
			if (type === 1) {
				return {
					autoplay: true,
					controls: true,
					muted: true,
					loop: true,
					fill: true,
					preload: "auto",
					poster: item.imgUrl,
					sources: [
						{
							src: item.url,
							type: "video/mp4",
						},
					],
				};
			} else if (type === 4) {
				return {
					autoplay: false,
					controls: false,
					muted: true,
					loop: true,
					fill: true,
					sources: [
						{
							src: path,
							type: "video/mp4",
						},
					],
				};
			}
		},
		syncThumbs(mainSwiper, thumbSwiper, index) {
			if (!mainSwiper || !thumbSwiper) return;
			mainSwiper.slideTo(index, 0, false);
			thumbSwiper.slideTo(index, 0, false);
			setTimeout(() => {
				mainSwiper.update();
				thumbSwiper.update();
				mainSwiper.updateSlidesClasses();
				thumbSwiper.updateSlidesClasses();
			}, 50);
		},
		parsePxValue(value) {
			const num = parseFloat(String(value).replace(/[^0-9.-]+/g, ""));
			return isNaN(num) ? 0 : num;
		},
		// 保存画布状态
		saveCanvasSnapshot() {
			if (!this.canvas || !this.canvas.c) return;

			this.canvasSnapshot = {
				json: this.canvas.c.toJSON(),
				width: this.canvas.c.getWidth(),
				height: this.canvas.c.getHeight(),
				backgroundImage: this.canvas.c.backgroundImage,
				backgroundColor: this.canvas.c.backgroundColor
			};

			console.log("保存画布快照", this.canvasSnapshot);
		},

		// 恢复画布状态
		restoreCanvasSnapshot() {
			if (!this.canvas || !this.canvas.c || !this.canvasSnapshot) return;

			console.log("恢复画布快照", this.canvasSnapshot);

			// 恢复画布尺寸
			this.canvas.c.setWidth(this.canvasSnapshot.width);
			this.canvas.c.setHeight(this.canvasSnapshot.height);

			// 恢复画布内容
			this.canvas.c.loadFromJSON(this.canvasSnapshot.json, () => {
				this.canvas.c.renderAll();
				console.log("画布状态已恢复");
			});
		},

		// 使用CSS变换调整画布显示尺寸（不改变实际画布尺寸）
		scaleCanvasDisplay(scaleX, scaleY) {
			if (!this.canvas || !this.canvas.c) return;

			const canvasElement = this.canvas.c.getElement();
			if (canvasElement) {
				canvasElement.style.transform = `scale(${scaleX}, ${scaleY})`;
				canvasElement.style.transformOrigin = 'top left';
				console.log("应用画布显示缩放", { scaleX, scaleY });
			}
		},

		// 重置画布显示缩放
		resetCanvasDisplay() {
			if (!this.canvas || !this.canvas.c) return;

			const canvasElement = this.canvas.c.getElement();
			if (canvasElement) {
				canvasElement.style.transform = '';
				canvasElement.style.transformOrigin = '';
				console.log("重置画布显示缩放");
			}
		},
		designFixedFn() {
			console.log("画布固定回调触发", {
				isCanvasFixed: this.isCanvasFixed,
				editStatus: this.editStatus,
				canvasExists: !!(this.canvas && this.canvas.c)
			});

			// 只有在编辑状态下才执行固定操作
			if (!this.editStatus) {
				console.log("非编辑状态，跳过固定操作");
				return;
			}

			// 确保画布已经初始化
			if (!this.canvas || !this.canvas.c) {
				console.log("画布未初始化，延迟执行固定操作");
				setTimeout(() => {
					this.designFixedFn();
				}, 300);
				return;
			}

			// 保存原始画布尺寸（只在第一次保存）
			if (!this.originalCanvasSize.width) {
				this.originalCanvasSize = {
					width: this.canvasWidth,
					height: this.canvasHeight
				};
				console.log("保存原始画布尺寸", this.originalCanvasSize);
			}

			// 保存画布状态（只在第一次保存）
			if (!this.canvasSnapshot) {
				this.saveCanvasSnapshot();
			}

			// 固定画布
			this.fixCanvas();
		},
		fixCanvas() {
			if (this.isCanvasFixed) return;

			// 确保画布已经初始化并且有内容
			if (!this.canvas || !this.canvas.c) {
				console.warn("画布未初始化，延迟固定");
				setTimeout(() => {
					this.fixCanvas();
				}, 500);
				return;
			}

			console.log("固定画布", {
				originalSize: this.originalCanvasSize,
				targetHeight: this.canvasHeight / 2
			});

			this.isCanvasFixed = true;

			// 计算缩放比例（只缩放高度，保持宽度不变）
			const scaleY = 0.5; // 高度缩放到一半
			const scaleX = 1; // 宽度保持不变

			this.$nextTick(() => {
				let designBody = document.getElementById("mbDesignBody");
				if (designBody) {
					// 设置固定样式
					designBody.style.width = this.mobileCanvasWidth.width;
					designBody.style.height = this.canvasHeight / 2 + "px";
					designBody.style.position = "fixed";
					designBody.style.top = "45px";
					designBody.style.left = "50%";
					designBody.style.transform = "translateX(-50%)";
					designBody.style.right = "initial";
					designBody.style.bottom = "initial";
					designBody.style.zIndex = "2002";
					designBody.style.boxShadow = "0 4px 12px rgba(0, 0, 0, 0.15)";
					designBody.style.borderRadius = "8px";
					designBody.style.border = "1px solid #e1e8ed";
					designBody.style.overflow = "hidden"; // 隐藏超出部分

					// 添加固定状态类
					designBody.classList.add("canvas-fixed");

					// 使用CSS变换缩放画布显示，不改变实际画布尺寸
					setTimeout(() => {
						this.scaleCanvasDisplay(scaleX, scaleY);
					}, 100);
				}
			});
		},
		unfixCanvas() {
			if (!this.isCanvasFixed) return;

			console.log("取消固定画布");

			this.isCanvasFixed = false;

			this.$nextTick(() => {
				let designBody = document.getElementById("mbDesignBody");
				if (designBody) {
					// 清除固定样式
					designBody.style.width = this.mobileCanvasWidth.width;
					designBody.style.height = this.mobileCanvasWidth.height;
					designBody.style.position = "";
					designBody.style.top = "";
					designBody.style.left = "";
					designBody.style.transform = "";
					designBody.style.right = "";
					designBody.style.bottom = "";
					designBody.style.zIndex = "";
					designBody.style.boxShadow = "";
					designBody.style.borderRadius = "";
					designBody.style.border = "";
					designBody.style.overflow = "";

					// 移除固定状态类
					designBody.classList.remove("canvas-fixed");

					// 重置画布显示缩放
					setTimeout(() => {
						this.resetCanvasDisplay();
					}, 100);
				}
			});
		},
		initIntersectionObserver(key, height, callback) {
			console.log("初始化观察器", { key, height });
			let detailScrollBox = document.querySelector(".detailScrollBox");

			// 记录初始化时的状态，避免立即触发
			let initialCheck = true;

			const observer = new IntersectionObserver(
				(entries) => {
					entries.forEach(async (entry) => {
						const rect = entry.boundingClientRect;
						const rootBounds = entry.rootBounds || document.documentElement.getBoundingClientRect();
						const adjustedTop = rootBounds.top + Math.abs(height);

						console.log("观察器回调", {
							isIntersecting: entry.isIntersecting,
							rectTop: rect.top,
							rectBottom: rect.bottom,
							adjustedTop: adjustedTop,
							fixedFirst: this.fixedFrist,
							isCanvasFixed: this.isCanvasFixed,
							initialCheck: initialCheck
						});

						// 如果是初始化检查，跳过回调执行
						if (initialCheck) {
							initialCheck = false;
							console.log("跳过初始化检查");
							return;
						}

						// 设计步骤进入视口且顶部距离满足条件时固定画布
						if (entry.isIntersecting && rect.top <= adjustedTop && rect.bottom >= adjustedTop) {
							if (!this.isCanvasFixed && typeof callback == "function") {
								console.log("触发画布固定");
								callback();
							}
							this.fixedFrist = true;
						}
						// 设计步骤离开视口时取消固定
						else if (!entry.isIntersecting && this.isCanvasFixed) {
							console.log("设计步骤离开视口，取消固定");
							this.unfixCanvas();
							this.fixedFrist = false;
						}
						// 设计步骤在视口中但位置不满足固定条件时取消固定
						else if (entry.isIntersecting && (rect.top > adjustedTop || rect.bottom < adjustedTop) && this.isCanvasFixed) {
							console.log("设计步骤位置不满足固定条件，取消固定");
							this.unfixCanvas();
						}
					});
				},
				{
					root: detailScrollBox,
					rootMargin: `0px 0px ${height}px 0px`,
					threshold: [0, 0.1, 0.5, 1], // 增加更多阈值以获得更精确的检测
				}
			);
			this.sectionObserver[key] = {
				observer: observer,
				observedEls: new Set(),
			};
		},
		safeObserve(key, dom, observer) {
			const observerInfo = this.sectionObserver[key];
			if (!observerInfo || !dom) return;
			if (observerInfo.observedEls.has(dom)) {
				console.log("该 DOM 已被监听，跳过");
				return;
			}
			observer.observe(dom);
			observerInfo.observedEls.add(dom);
		},
		setFixCanvas() {
			if (this.designStepId) {
				console.log("设置画布固定观察器", this.designStepId);
				let designStepDom = document.getElementById(`${this.designStepId}`);
				let designDomObserver = this.sectionObserver["designStepDom"];

				if (!designDomObserver) {
					let detailScrollBox = document.querySelector(".detailScrollBox");
					if (detailScrollBox) {
						let detailScrollBoxHeight = detailScrollBox.offsetHeight;
						// 计算观察器的rootMargin，确保在合适的时机触发
						let rootMarginHeight = -(detailScrollBoxHeight - this.canvasHeight / 2);
						console.log("初始化观察器参数", {
							detailScrollBoxHeight,
							canvasHeight: this.canvasHeight,
							rootMarginHeight
						});
						this.initIntersectionObserver("designStepDom", rootMarginHeight, this.designFixedFn);
					}
				}

				if (designStepDom) {
					// 延迟观察，确保不会立即触发
					setTimeout(() => {
						this.safeObserve("designStepDom", designStepDom, this.sectionObserver["designStepDom"].observer);
						console.log("开始观察设计步骤DOM");
					}, 200);
				} else {
					console.warn("未找到设计步骤DOM元素", this.designStepId);
				}
			}
		},
		// 清理所有观察器
		destroyObservers() {
			Object.keys(this.sectionObserver).forEach(key => {
				const observerInfo = this.sectionObserver[key];
				if (observerInfo && observerInfo.observer) {
					observerInfo.observer.disconnect();
					observerInfo.observedEls.clear();
				}
			});
			this.sectionObserver = {};
			console.log("已清理所有观察器");
		},
	},
	mounted() {
		// 组件挂载时不立即设置观察器
		// 观察器将在editStatus变为true时通过watch触发
		console.log("mbSwiperArea组件已挂载", {
			editStatus: this.editStatus,
			designStepId: this.designStepId
		});
	},
	beforeDestroy() {
		// 组件销毁前清理观察器和恢复画布状态
		this.destroyObservers();
		if (this.isCanvasFixed) {
			this.unfixCanvas();
		}
		// 清理画布快照
		this.canvasSnapshot = null;
	},
};
</script>

<template>
	<div class="swiper-container-wrap" :class="{ editStatus: editStatus }">
		<div class="previewImgWrap">
			<div class="myswiper2-wrap bigColorSwiperBox" :style="mobileCanvasWidth" v-show="isMobile && hasColorSwiper">
				<div class="swiper myswiper2" ref="swiper4">
					<div class="swiper-wrapper">
						<div class="swiper-slide" v-for="(item, index) in colorImgList" :key="index">
							<div class="smallImg" v-if="isImageType(item.url)">
								<client-only>
									<template v-if="isMobile">
										<img :src="item.url" :alt="item.alt" style="width: 100%; height: 100%" />
									</template>
									<template v-else>
										<PicZoom :url="item.url" :alt="item.alt" :width="300" :scale="2" type="round"></PicZoom>
									</template>
								</client-only>
							</div>
							<VideoPlayer class="swiper-no-swiping" v-else :ref="'videoPlayer' + index" disabled-mouse :options="getVideoOptions(item, 1)"></VideoPlayer>
						</div>
					</div>
					<!-- <div class="custom-swiper-pagination" v-if="isMobile">Gallery {{ currentIndex2 + 1 }}/{{ colorImgList.length }}</div> -->
				</div>
				<div class="bigColorSwiper swiper-button-prev" v-show="!editStatus && !showThreeD"></div>
				<div class="bigColorSwiper swiper-button-next" v-show="!editStatus && !showThreeD"></div>
			</div>
			<div id="mbDesignBody" class="canvasWrap" :class="{ disabled: loadTempData }" :style="mobileCanvasWidth" v-show="editStatus">
				<div class="loadProgress" v-show="loadTempData">
					<v-progress-circular indeterminate color="primary"></v-progress-circular>
				</div>
				<div class="designBox" v-show="!firstImg">
					<div class="areaMask" v-show="isMask">
						{{ maskContent }}
					</div>
					<canvas id="fabricCanvas"></canvas>
				</div>
				<div v-show="firstImg" class="firstImg">
					<img :src="firstImg" alt="first" />
				</div>
			</div>
			<div id="product360Preview" v-show="showThreeD"></div>
			<div v-show="editStatus" class="tap-box extend-click-area" @click="zoom">
				<span><b :class="!isBig ? 'icon-a-tgsc-add' : 'icon-a-tgsc-jzhuanhuan'"></b></span>
				{{ langSemiCustom.topToZoom }}
			</div>
			<div v-show="editStatus" class="borderBox"></div>
		</div>
		<div id="dummy" style="display: none"></div>
	</div>
</template>

<style scoped lang="scss">
#product360Preview {
	position: absolute;
	display: flex;
	align-items: center;
	inset: 0;
	z-index: 1000;
	cursor: e-resize;
	background: #ffffff url("https://static-oss.gs-souvenir.com/web/quoteManage/20241024/%E7%BB%84_872_2058SW6WeS.png") (center bottom 20px) / contain no-repeat;

	::v-deep img {
		width: 100%;
		height: 100%;
		object-fit: contain;
	}
}

.myswiper2-wrap {
	position: relative;
	height: 100%;
	--swiper-navigation-color: #ffffff;
	/* 单独设置按钮颜色 */
	--swiper-navigation-size: 20px;

	/* 设置按钮大小 */
	.swiper-button-next,
	.swiper-button-prev {
		width: 2.55em;
		height: 2.55em;
		background: rgba(0, 0, 0, 0.5);
		border-radius: 50%;
		transform: translateY(-50%);
		margin-top: 0;

		@include respond-to(mb) {
			width: 2.57em;
			height: 2.57em;
		}
	}
	.swiper-button-prev {
		left: 0;
		top: 50%;
		transform: translate(-50%, -50%);

		@include respond-to(mb) {
			left: 10px;
			transform: translate(0, -50%);
			// &.bigColorSwiper.swiper-button-disabled {
			// 	opacity: 1 !important;
			// 	pointer-events: initial !important;
			// 	user-select: initial !important;
			// }
		}
	}

	.swiper-button-next {
		right: 0;
		top: 50%;
		transform: translate(50%, -50%);

		@include respond-to(mb) {
			right: 10px;
			transform: translate(0, -50%);
			&.normalSwiper.hasColorSwiper.swiper-button-disabled {
				opacity: 1 !important;
				pointer-events: initial !important;
				user-select: initial !important;
			}
		}
	}

	// 画布固定相关样式
	.canvas-fixed {
		transition: all 0.3s ease-in-out;

		// 添加固定状态下的特殊样式
		&.canvasWrap {
			background: white;
		}
	}

	// 编辑状态下的特殊样式
	.editStatus {
		.previewImgWrap {
			.canvasWrap {
				// 为画布固定做准备的样式
				transition: all 0.3s ease-in-out;
			}
		}
	}
}

.swiper-container-wrap {
	display: flex;
	gap: 6.5%;
	width: 100%;
	aspect-ratio: 495/417;

	@include respond-to(mb) {
		height: auto;
		gap: 10px;
		flex-direction: column;
		aspect-ratio: auto;
		margin: 0 -3vw;
		width: calc(100% + 6vw);
	}

	.previewImgWrap {
		flex: 1;
		position: relative;
		width: 0;
		height: 100%;

		.tap-box {
			position: absolute;
			right: 0.5em;
			bottom: 0.8em;
			z-index: 1001;
			font-size: 14px;
			color: #666;
			b {
				font-size: 14px;
			}
		}

		.borderBox {
			margin-top: 32px;
			width: 100%;
			height: 2px;
			background: rgba(0, 0, 0, 0.08);
			box-shadow: 0px -8px 9px 3px #f0f0f0;
		}

		@include respond-to(mb) {
			order: 1;
			flex: none;
			width: 100%;
		}

		.canvasWrap {
			display: flex;
			justify-content: center;
			align-items: center;
			position: absolute;
			inset: 0;
			height: 100%;
			z-index: 1000;

			.loadProgress {
				display: flex;
				justify-content: center;
				align-items: center;
				position: absolute;
				inset: 0;
				z-index: 1000;
			}

			.areaMask {
				position: absolute;
				top: 50%;
				transform: translateY(-50%);
				left: 0;
				right: 0;
				background-color: #2c2c2c;
				opacity: 0.5;
				padding: 20px;
				color: #fff;
				font-size: 16px;
				z-index: 99;
				word-break: break-word;
			}

			.designBox {
				display: flex;
				justify-content: center;
				align-items: center;
				position: absolute;
				inset: 0;
				background-color: #ffffff;

				@include respond-to(mb) {
					padding: 2px;
				}
			}

			.firstImg {
				position: absolute;
				inset: 0;

				img {
					width: 100%;
					height: 100%;
					object-fit: contain;
				}
			}
		}

		b {
			font-size: 30px;
			cursor: pointer;
		}

		.myswiper2 {
			position: relative;
			height: 100%;

			.custom-swiper-pagination {
				position: absolute;
				right: 0;
				bottom: 0;
				padding: 8px 10px;
				border-top-left-radius: 20px;
				background-color: rgba(0, 0, 0, 0.4);
				z-index: 1;
				color: #ffffff;
				font-size: 12px;
			}

			.swiper-slide {
				overflow: hidden;
				display: flex;
				justify-content: center;
				align-items: center;
				aspect-ratio: 1;
				border-radius: 10px;

				@include respond-to(mb) {
					border-radius: 0;
				}

				::v-deep .video-js video {
					object-fit: cover;
				}

				.smallImg {
					width: 100%;
					height: 100%;
					display: flex;
					align-items: center;
					justify-content: center;
					position: relative;
					text-align: center;

					::v-deep {
						.pic-img {
							width: 100%;
							height: 100%;

							.img-container {
								width: 100%;
								height: 100%;

								img {
									max-width: 100%;
									max-height: 100%;
									object-fit: cover;
								}
							}
						}
					}
				}
			}

			img {
				max-width: 100%;
				max-height: 100%;
				object-fit: cover;
			}
		}
	}

	$customWidth: 4.31em;

	.thumbImgWrap {
		flex-basis: $customWidth;

		@include respond-to(mb) {
			display: flex;
			flex-basis: auto;
			order: 2;
			padding: 0 10px;
		}

		&.isEdit {
			.myswiper1 {
				.swiper-slide.swiper-slide-thumb-active {
					border: 1px solid #f1f1f1;
				}
			}
		}

		.myswiper1 {
			width: 100%;
			max-height: calc(100% - (#{$customWidth} + 8px));
			margin-bottom: 8px;

			@include respond-to(mb) {
				max-height: unset;
				margin-bottom: 0;
				margin-left: 0;
				margin-right: 8px;
				width: calc((100% - 36px) / 6.5) !important;
				height: auto;
				max-width: calc(100% - 36px);
				&.colorSwiper {
					flex: 1;
					width: auto !important;
				}
				&.hasRotateImg {
					width: calc((100% - 36px) / 6.5 * 2) !important;
				}
				&.noColorSwiper {
					width: auto !important;
					.swiper-slide {
						width: 48px !important;
						height: 48px !important;
					}
				}
				.swiper-wrapper {
					.swiper-slide {
						&.swiper-slide-thumb-active {
							border: 1px solid #f1f1f1 !important;
						}
					}
					&.showColorSwiper {
						.swiper-slide {
							&.swiper-slide-thumb-active {
								border: 2px solid $color-primary !important;
							}
						}
					}
				}
			}

			.swiper-slide {
				position: relative;
				display: flex;
				justify-content: center;
				align-items: center;
				overflow: hidden;
				border: 1px solid #f1f1f1;
				height: $customWidth !important;
				border-radius: 4px;
				cursor: pointer;
				aspect-ratio: 1;

				.play-icon {
					display: flex;
					align-items: center;
					justify-content: center;
					position: absolute;
					left: 50%;
					top: 50%;
					transform: translate(-50%, -50%);
				}

				@include respond-to(mb) {
					// width: 48px !important;
					height: auto !important;

					&:last-child {
						margin-right: 0 !important;
					}
				}

				&:last-child {
					margin-bottom: 0 !important;
				}

				&.swiper-slide-thumb-active {
					border: 2px solid $color-primary;
				}

				img {
					width: 100%;
					height: 100%;
					object-fit: cover;
					aspect-ratio: 1/1;
				}
			}
		}

		.edit {
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			width: $customWidth;
			height: $customWidth;
			text-align: center;
			line-height: 1.2;
			color: #666666;
			border: 1px solid #f1f1f1;
			border-radius: 4px;
			cursor: pointer;

			span {
				font-size: 14px;
			}

			&.active {
				border: 2px solid $color-primary;
			}

			@include respond-to(mb) {
				padding: 0 4px;
				width: 48px;
				height: 48px;
				aspect-ratio: 1;
				flex-shrink: 0;
				&.hasColorSwiper {
					width: calc((100% - 36px) / 6.5);
					height: auto;
					margin-right: 6px;
				}

				span {
					font-size: 12px;
				}
			}

			b {
				font-size: 18px;

				@include respond-to(mb) {
					font-size: 14px;
				}
			}
		}

		.threeD {
			width: 100%;
			height: 100%;

			img {
				object-fit: contain !important;
			}
		}
		.arrowIcon {
			display: flex;
			align-items: center;
			width: calc(((100% - 36px) / 6.5) * 0.3);
			font-size: 16px;
		}
	}
}
</style>
