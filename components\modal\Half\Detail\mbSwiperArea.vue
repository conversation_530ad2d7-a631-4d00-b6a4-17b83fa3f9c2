<script>
import VideoPlayer from "@/components/HalfDesign/VideoPlayer.vue";
import { isImageType, isVideoType } from "@/utils/utils";

export default {
	inject: ["getCurrentAreaParam", "getStepArr", "canvas"],
	props: {
		isBig: {
			type: Boolean,
		},
		productInfo: {
			type: Object,
		},
		mobileCanvasWidth: {
			type: Object,
		},
		editStatus: {
			type: Boolean,
		},
		loadTempData: {
			type: Boolean,
		},
		firstImg: {
			type: String,
		},
		isMask: {
			type: Boolean,
		},
		currentStep: {
			type: Number,
		},
		showThreeD: {
			type: <PERSON>olean,
		},
		maskContent: {
			type: String,
		},
		isCufflinks: {
			type: Boolean,
			default: false,
		},
		colorParams: {
			type: Object,
			default: () => ({}),
		},
	},
	data() {
		return {
			currentIndex: 0,
			currentIndex2: 0,
			newImgList: [],
			isInit360: false,
			showColorSwiper: false,
			myswiper1: null,
			myswiper2: null,
			myswiper3: null,
			myswiper4: null,
			startX: null,
			isNormalSwiperEnd: false,
			isBigSwiperStart: false,
			designStepIndex: -1,
			sectionObserver: {},
			fixedFrist: false,
			isCanvasFixed: false,
			originalCanvasSize: { width: 0, height: 0 },
			observerInitialized: false, // 标记观察器是否已初始化
			canvasSnapshot: null, // 保存画布快照
			fixedScale: 0.5, // 固定时的缩放比例
		};
	},
	components: { VideoPlayer },
	watch: {
		async colorImgList(val) {
			if (this.isMobile) {
				this.$nextTick(() => {
					this.initSwiper2();
				});
			}
		},
		editStatus(val) {
			if (val) {
				this.$emit("update:showThreeD", false);
				// 延迟设置观察器，避免立即触发回调
				this.$nextTick(() => {
					setTimeout(() => {
						this.setFixCanvas();
					}, 100);
				});
			} else {
				// 退出编辑状态时清理观察器和恢复画布
				this.destroyObservers();
				if (this.isCanvasFixed) {
					this.unfixCanvas();
				}
				// 延迟清理状态，确保恢复操作完成
				setTimeout(() => {
					this.canvasSnapshot = null;
					this.originalCanvasSize = { width: 0, height: 0 };
					console.log("编辑状态清理完成");
				}, 500);
			}
		},
	},
	computed: {
		isMobile() {
			return this.$store.state.device === "mb";
		},
		langSemiCustom() {
			return this.$store.getters.lang?.semiCustom;
		},
		hasRotateImg() {
			if (!this.productInfo.rotateImg) {
				return false;
			}
			return JSON.parse(this.productInfo.rotateImg).length;
		},
		currentArea() {
			return this.getCurrentAreaParam();
		},
		colorImgList() {
			if (this.isCufflinks && this.hasColorSwiper) {
				return this.colorParams?.productParamList?.map((item) => {
					return { url: item.sizeImg };
				});
			}
			if (!this.currentArea) return [];
			const colorUrlList = [];
			const areaId = this.currentArea.attributeValueId;
			if (Array.isArray(this.colorParams?.productParamList)) {
				this.colorParams.productParamList.forEach((item) => {
					if (!item.imgJson) return;
					try {
						const imgData = JSON.parse(item.imgJson);
						if (imgData && Array.isArray(imgData)) {
							imgData.forEach((img) => {
								if (img.attributeValueId === areaId) {
									colorUrlList.push({ url: img.url });
								}
							});
						}
					} catch (e) {
						console.error("JSON 解析失败_colorSwiper:", item.imgJson);
					}
				});
			}

			return colorUrlList;
		},
		hasColorSwiper() {
			return Object.keys(this.colorParams).length > 0 && this.colorImgList.length > 0;
		},
		stepArr() {
			return this.getStepArr();
		},
		designStepId() {
			let id = 0;
			if (this.stepArr) {
				this.designStepIndex = this.stepArr.findIndex((item) => item.isDevise === 1);
				if (this.designStepIndex >= 0) id = this.stepArr[this.designStepIndex].id;
			}
			return id;
		},
		canvasWidth() {
			return this.parsePxValue(this.mobileCanvasWidth.width);
		},
		canvasHeight() {
			return this.parsePxValue(this.mobileCanvasWidth.height);
		},
	},
	methods: {
		zoom() {
			this.$emit("zoom");
		},
		initSwiper() {
			let _this = this;
			let num = "auto";
			if (this.isMobile) {
				if (this.hasColorSwiper) {
					num = 1;
				}
				if (this.hasColorSwiper && this.hasRotateImg) {
					num = 2;
				}
			}

			this.myswiper1 = new Swiper(this.$refs.swiper1, {
				slidesPerView: num,
				direction: "vertical",
				spaceBetween: 6,
				watchSlidesVisibility: true,
				watchSlidesProgress: true,
				grabCursor: true,
				breakpoints: {
					320: {
						direction: "horizontal",
					},
					750: {
						direction: "horizontal",
					},
					1001: {
						direction: "vertical",
					},
				},
				on: {
					click: (value) => {
						if (_this.showColorSwiper) _this.showColorSwiper = false;
						let type = _this.newImgList[value.clickedIndex]?.type;
						let pdtLeft = document.querySelector(".pdt-left");
						pdtLeft.classList.remove("isSticky");
						_this.$emit("update:editStatus", false);
						_this.$emit("update:showThreeD", false);
						if (type === "is360") {
							_this.initThreeD();
						}
					},
				},
			});
			this.myswiper2 = new Swiper(this.$refs.swiper2, {
				slidesPerView: 1,
				spaceBetween: 10,
				autoplay: false,
				grabCursor: true,
				observer: true,
				observeParents: true,
				navigation: {
					nextEl: ".normalSwiper.swiper-button-next",
					prevEl: ".normalSwiper.swiper-button-prev",
				},
				thumbs: {
					swiper: this.myswiper1,
				},
				on: {
					touchStart: function (swiper, event) {
						if (!_this.isMobile) return;
						if (event.touches.length > 0) {
							_this.startX = event.touches[0].clientX;
						}
					},
					touchMove: function (swiper, event) {
						if (!_this.isMobile) return;
						if (!_this.startX || !swiper.isEnd) return;
						const currentX = event.touches[0].clientX;
						const diffX = _this.startX - currentX;
						if (diffX > 15) {
							if (!_this.showColorSwiper && _this.hasColorSwiper) {
								setTimeout(() => {
									_this.showColorSwiper = true;
									_this.syncThumbs(_this.myswiper4, _this.myswiper3, 0);
								}, 500);
							}
							_this.startX = null;
						}
					},
					touchEnd: function (swiper, event) {
						if (!_this.isMobile) return;
						_this.startX = null;
					},
					slideChangeTransitionEnd: function (value) {
						_this.isNormalSwiperEnd = false;
						if (value.isEnd) _this.isNormalSwiperEnd = true;
						if (_this.showColorSwiper) _this.showColorSwiper = false;
						_this.currentIndex = value.activeIndex;
						let type = _this.newImgList[value.activeIndex]?.type;
						_this.$emit("update:editStatus", false);
						_this.$emit("update:showThreeD", false);
						if (type === "is360") {
							_this.initThreeD();
						}
						_this.newImgList.forEach(function (v, i) {
							if (i === value.activeIndex) {
								if (_this.$refs["videoPlayer" + i]) {
									_this.$refs["videoPlayer" + i][0].player.play();
								}
							} else {
								if (_this.$refs["videoPlayer" + i]) {
									_this.$refs["videoPlayer" + i][0].player.pause();
								}
							}
						});
					},
				},
			});
		},
		initSwiper2() {
			let _this = this;
			this.myswiper4 = new Swiper(this.$refs.swiper4, {
				slidesPerView: 1,
				spaceBetween: 10,
				autoplay: false,
				grabCursor: true,
				observer: true,
				observeParents: true,
				navigation: {
					nextEl: ".bigColorSwiper.swiper-button-next",
					prevEl: ".bigColorSwiper.swiper-button-prev",
				},
				on: {
					slideChangeTransitionEnd: function (value) {
						_this.currentIndex2 = value.activeIndex;
						_this.$emit("update:editStatus", false);
						_this.colorImgList.forEach(function (_, i) {
							if (i === value.activeIndex) {
								if (_this.$refs["videoPlayer" + i]) {
									_this.$refs["videoPlayer" + i][0].player.play();
								}
							} else {
								if (_this.$refs["videoPlayer" + i]) {
									_this.$refs["videoPlayer" + i][0].player.pause();
								}
							}
						});
					},
				},
			});
		},
		initThreeD() {
			this.$emit("update:showThreeD", true);
			if (!this.isInit360) {
				let picList = JSON.parse(this.productInfo.rotateImg).map((i) => i.url);
				pdt360DegViewer("product360Preview", picList, true, true); //autoPlay
				this.isInit360 = true;
			}
		},
		isImageType,
		isVideoType,
		getVideoOptions(item, type) {
			if (type === 1) {
				return {
					autoplay: true,
					controls: true,
					muted: true,
					loop: true,
					fill: true,
					preload: "auto",
					poster: item.imgUrl,
					sources: [
						{
							src: item.url,
							type: "video/mp4",
						},
					],
				};
			} else if (type === 4) {
				return {
					autoplay: false,
					controls: false,
					muted: true,
					loop: true,
					fill: true,
					sources: [
						{
							src: path,
							type: "video/mp4",
						},
					],
				};
			}
		},
		syncThumbs(mainSwiper, thumbSwiper, index) {
			if (!mainSwiper || !thumbSwiper) return;
			mainSwiper.slideTo(index, 0, false);
			thumbSwiper.slideTo(index, 0, false);
			setTimeout(() => {
				mainSwiper.update();
				thumbSwiper.update();
				mainSwiper.updateSlidesClasses();
				thumbSwiper.updateSlidesClasses();
			}, 50);
		},
		parsePxValue(value) {
			const num = parseFloat(String(value).replace(/[^0-9.-]+/g, ""));
			return isNaN(num) ? 0 : num;
		},
		// 保存画布的原始状态（只在第一次保存）
		saveCanvasSnapshot() {
			if (!this.canvas || !this.canvas.c || this.canvasSnapshot) return;

			this.canvasSnapshot = {
				json: this.canvas.c.toJSON(['id', 'selectable', 'evented']),
				width: this.canvas.c.getWidth(),
				height: this.canvas.c.getHeight()
			};

			console.log("保存原始画布快照", {
				width: this.canvasSnapshot.width,
				height: this.canvasSnapshot.height,
				objectCount: this.canvas.c.getObjects().length
			});
		},

		// 恢复到原始状态
		restoreToOriginalState() {
			if (!this.canvas || !this.canvas.c || !this.canvasSnapshot) {
				console.warn("无法恢复到原始状态：缺少必要数据");
				return;
			}

			console.log("恢复到原始状态");

			// 使用保存的JSON数据完全恢复画布
			this.canvas.c.loadFromJSON(this.canvasSnapshot.json, () => {
				this.canvas.c.setWidth(this.canvasSnapshot.width);
				this.canvas.c.setHeight(this.canvasSnapshot.height);
				this.canvas.c.renderAll();
				console.log("画布已恢复到原始状态");
			});
		},

		// 基于原始状态进行缩放（避免累积误差）
		resizeCanvasFromOriginal(newWidth, newHeight) {
			if (!this.canvas || !this.canvas.c || !this.canvasSnapshot) {
				console.warn("无法基于原始状态缩放：缺少必要数据");
				return;
			}

			console.log("基于原始状态缩放画布", {
				originalSize: { width: this.canvasSnapshot.width, height: this.canvasSnapshot.height },
				targetSize: { width: newWidth, height: newHeight }
			});

			// 先恢复到原始状态
			this.canvas.c.loadFromJSON(this.canvasSnapshot.json, () => {
				// 然后进行缩放
				this.resizeCanvasIntelligent(newWidth, newHeight);
			});
		},

		// 智能调整画布尺寸（保持内容比例正确，使用统一缩放比例）
		resizeCanvasIntelligent(newWidth, newHeight) {
			if (!this.canvas || !this.canvas.c) {
				console.warn("画布不存在，跳过尺寸调整");
				return;
			}

			const oldWidth = this.canvas.c.getWidth();
			const oldHeight = this.canvas.c.getHeight();

			// 如果尺寸没有变化，不需要调整
			if (oldWidth === newWidth && oldHeight === newHeight) {
				console.log("画布尺寸无变化，跳过调整");
				return;
			}

			console.log("智能调整画布尺寸", {
				from: { width: oldWidth, height: oldHeight },
				to: { width: newWidth, height: newHeight }
			});

			// 使用统一的缩放比例（取较小的比例，保持宽高比）
			const scaleX = newWidth / oldWidth;
			const scaleY = newHeight / oldHeight;
			const uniformScale = Math.min(scaleX, scaleY);

			console.log("缩放比例", { scaleX, scaleY, uniformScale });

			// 设置新的画布尺寸
			this.canvas.c.setWidth(newWidth);
			this.canvas.c.setHeight(newHeight);

			// 计算画布中心偏移（用于居中显示）
			const centerOffsetX = (newWidth - oldWidth * uniformScale) / 2;
			const centerOffsetY = (newHeight - oldHeight * uniformScale) / 2;

			// 使用统一缩放比例调整所有对象
			this.canvas.c.getObjects().forEach((obj) => {
				// 获取对象的中心点
				const objCenter = obj.getCenterPoint();

				// 使用统一缩放比例缩放中心点坐标
				const newCenterX = objCenter.x * uniformScale + centerOffsetX;
				const newCenterY = objCenter.y * uniformScale + centerOffsetY;

				// 使用统一缩放比例缩放对象的尺寸
				const newScaleX = obj.scaleX * uniformScale;
				const newScaleY = obj.scaleY * uniformScale;

				// 计算新的左上角位置
				const newLeft = newCenterX - (obj.width * newScaleX) / 2;
				const newTop = newCenterY - (obj.height * newScaleY) / 2;

				obj.set({
					left: newLeft,
					top: newTop,
					scaleX: newScaleX,
					scaleY: newScaleY
				});
				obj.setCoords();

				console.log("对象缩放", {
					type: obj.type,
					uniformScale,
					oldCenter: objCenter,
					newCenter: { x: newCenterX, y: newCenterY }
				});
			});

			// 调整背景图片
			if (this.canvas.c.backgroundImage) {
				const bg = this.canvas.c.backgroundImage;
				const bgCenter = bg.getCenterPoint();
				const newBgCenterX = bgCenter.x * uniformScale + centerOffsetX;
				const newBgCenterY = bgCenter.y * uniformScale + centerOffsetY;
				const newBgScaleX = bg.scaleX * uniformScale;
				const newBgScaleY = bg.scaleY * uniformScale;
				const newBgLeft = newBgCenterX - (bg.width * newBgScaleX) / 2;
				const newBgTop = newBgCenterY - (bg.height * newBgScaleY) / 2;

				bg.set({
					scaleX: newBgScaleX,
					scaleY: newBgScaleY,
					left: newBgLeft,
					top: newBgTop
				});
				this.canvas.c.setBackgroundImage(bg, () => {
					this.canvas.c.renderAll();
				});
			} else {
				this.canvas.c.renderAll();
			}
		},

		// 恢复画布到原始状态
		restoreCanvasToOriginal() {
			if (!this.canvas || !this.canvas.c || !this.canvasSnapshot) {
				console.warn("无法恢复画布：缺少必要数据");
				return;
			}

			console.log("恢复画布到原始状态");

			// 清空当前画布
			this.canvas.c.clear();

			// 恢复画布尺寸
			this.canvas.c.setWidth(this.canvasSnapshot.width);
			this.canvas.c.setHeight(this.canvasSnapshot.height);

			// 恢复背景
			if (this.canvasSnapshot.backgroundImage) {
				const bgData = this.canvasSnapshot.backgroundImage;
				fabric.Image.fromObject(bgData, (img) => {
					this.canvas.c.setBackgroundImage(img, () => {
						this.canvas.c.renderAll();
					});
				});
			} else if (this.canvasSnapshot.backgroundColor) {
				this.canvas.c.setBackgroundColor(this.canvasSnapshot.backgroundColor, () => {
					this.canvas.c.renderAll();
				});
			}

			// 恢复所有对象
			this.canvasSnapshot.objects.forEach(objData => {
				fabric.util.enlivenObjects([objData], (objects) => {
					objects.forEach(obj => {
						this.canvas.c.add(obj);
					});
					this.canvas.c.renderAll();
				});
			});
		},
		designFixedFn() {
			console.log("画布固定回调触发", {
				isCanvasFixed: this.isCanvasFixed,
				editStatus: this.editStatus,
				canvasExists: !!(this.canvas && this.canvas.c)
			});

			// 只有在编辑状态下才执行固定操作
			if (!this.editStatus) {
				console.log("非编辑状态，跳过固定操作");
				return;
			}

			// 确保画布已经初始化
			if (!this.canvas || !this.canvas.c) {
				console.log("画布未初始化，延迟执行固定操作");
				setTimeout(() => {
					this.designFixedFn();
				}, 300);
				return;
			}

			// 保存原始画布尺寸（只在第一次保存）
			if (!this.originalCanvasSize.width) {
				this.originalCanvasSize = {
					width: this.canvasWidth,
					height: this.canvasHeight
				};
				console.log("保存原始画布尺寸", this.originalCanvasSize);
			}

			// 保存画布状态（只在第一次保存）
			if (!this.canvasSnapshot) {
				this.saveCanvasSnapshot();
			}

			// 固定画布
			this.fixCanvas();
		},
		fixCanvas() {
			if (this.isCanvasFixed) return;

			// 确保画布已经初始化并且有内容
			if (!this.canvas || !this.canvas.c) {
				console.warn("画布未初始化，延迟固定");
				setTimeout(() => {
					this.fixCanvas();
				}, 500);
				return;
			}

			console.log("固定画布开始");

			const targetHeight = this.canvasHeight / 2;
			this.isCanvasFixed = true;

			// 先设置DOM容器样式
			this.$nextTick(() => {
				let designBody = document.getElementById("mbDesignBody");
				if (designBody) {
					// 设置固定样式
					designBody.style.width = this.mobileCanvasWidth.width;
					designBody.style.height = targetHeight + "px";
					designBody.style.position = "fixed";
					designBody.style.top = "45px";
					designBody.style.left = "50%";
					designBody.style.transform = "translateX(-50%)";
					designBody.style.right = "initial";
					designBody.style.bottom = "initial";
					designBody.style.zIndex = "2002";
					designBody.style.boxShadow = "0 4px 12px rgba(0, 0, 0, 0.15)";
					designBody.style.borderRadius = "8px";
					designBody.style.border = "1px solid #e1e8ed";

					// 添加固定状态类
					designBody.classList.add("canvas-fixed");

					// 缩放画布到固定尺寸
					setTimeout(() => {
						console.log("固定画布并缩放内容");

						// 计算缩放比例
						const scaleX = this.canvasWidth / this.canvasSnapshot.width;
						const scaleY = targetHeight / this.canvasSnapshot.height;

						// 设置新的画布尺寸
						this.canvas.c.setWidth(this.canvasWidth);
						this.canvas.c.setHeight(targetHeight);

						// 缩放所有对象
						this.canvas.c.getObjects().forEach((obj) => {
							obj.set({
								left: obj.left * scaleX,
								top: obj.top * scaleY,
								scaleX: obj.scaleX * scaleX,
								scaleY: obj.scaleY * scaleY
							});
							obj.setCoords();
						});

						// 缩放背景图片
						if (this.canvas.c.backgroundImage) {
							const bg = this.canvas.c.backgroundImage;
							bg.set({
								left: bg.left * scaleX,
								top: bg.top * scaleY,
								scaleX: bg.scaleX * scaleX,
								scaleY: bg.scaleY * scaleY
							});
							this.canvas.c.setBackgroundImage(bg, () => {
								this.canvas.c.renderAll();
							});
						} else {
							this.canvas.c.renderAll();
						}

						console.log("画布固定完成", {
							width: this.canvas.c.getWidth(),
							height: this.canvas.c.getHeight(),
							scaleX: scaleX,
							scaleY: scaleY
						});
					}, 100);
				}
			});
		},
		unfixCanvas() {
			if (!this.isCanvasFixed) return;

			console.log("取消固定画布开始");
			this.isCanvasFixed = false;

			this.$nextTick(() => {
				let designBody = document.getElementById("mbDesignBody");
				if (designBody) {
					// 清除固定样式
					designBody.style.width = this.mobileCanvasWidth.width;
					designBody.style.height = this.mobileCanvasWidth.height;
					designBody.style.position = "";
					designBody.style.top = "";
					designBody.style.left = "";
					designBody.style.transform = "";
					designBody.style.right = "";
					designBody.style.bottom = "";
					designBody.style.zIndex = "";
					designBody.style.boxShadow = "";
					designBody.style.borderRadius = "";
					designBody.style.border = "";

					// 移除固定状态类
					designBody.classList.remove("canvas-fixed");

					// 延迟恢复画布到原始尺寸
					setTimeout(() => {
						console.log("恢复画布状态");

						if (!this.canvasSnapshot) {
							console.warn("没有保存的画布快照");
							return;
						}

						// 获取当前画布的所有对象（包括固定期间新增的）
						const currentCanvasData = this.canvas.c.toJSON(['id', 'selectable', 'evented']);

						// 恢复到保存的状态
						this.canvas.c.loadFromJSON(this.canvasSnapshot.json, () => {
							// 恢复画布尺寸
							this.canvas.c.setWidth(this.canvasSnapshot.width);
							this.canvas.c.setHeight(this.canvasSnapshot.height);

							// 检查是否有新增的对象
							const originalObjects = this.canvas.c.getObjects();
							const currentObjects = JSON.parse(currentCanvasData).objects;

							if (currentObjects.length > originalObjects.length) {
								console.log("检测到新增对象，添加到原始画布");

								// 计算从固定尺寸到原始尺寸的反向缩放比例
								const scaleX = this.canvasSnapshot.width / this.canvasWidth;
								const scaleY = this.canvasSnapshot.height / (this.canvasHeight / 2);

								// 获取新增的对象（从原始对象数量开始的对象）
								const newObjectsData = currentObjects.slice(originalObjects.length);

								// 添加新增的对象并调整其尺寸
								newObjectsData.forEach(objData => {
									// 调整对象的位置和尺寸到原始画布
									objData.left = objData.left * scaleX;
									objData.top = objData.top * scaleY;
									objData.scaleX = objData.scaleX * scaleX;
									objData.scaleY = objData.scaleY * scaleY;

									// 根据对象类型创建fabric对象
									if (objData.type === 'text' || objData.type === 'i-text') {
										const textObj = new fabric.IText(objData.text, objData);
										this.canvas.c.add(textObj);
									} else if (objData.type === 'image') {
										fabric.Image.fromObject(objData, (img) => {
											this.canvas.c.add(img);
											this.canvas.c.renderAll();
										});
									} else {
										fabric.util.enlivenObjects([objData], (objects) => {
											if (objects[0]) {
												this.canvas.c.add(objects[0]);
												this.canvas.c.renderAll();
											}
										});
									}
								});
							}

							this.canvas.c.renderAll();

							console.log("画布恢复完成", {
								width: this.canvas.c.getWidth(),
								height: this.canvas.c.getHeight(),
								totalObjects: this.canvas.c.getObjects().length
							});

							// 验证恢复效果
							setTimeout(() => {
								this.validateCanvasRestore();
							}, 100);
						});
					}, 100);
				}
			});
		},
		initIntersectionObserver(key, height, callback) {
			console.log("初始化观察器", { key, height });
			let detailScrollBox = document.querySelector(".detailScrollBox");

			// 记录初始化时的状态，避免立即触发
			let initialCheck = true;

			const observer = new IntersectionObserver(
				(entries) => {
					entries.forEach(async (entry) => {
						const rect = entry.boundingClientRect;
						const rootBounds = entry.rootBounds || document.documentElement.getBoundingClientRect();
						const adjustedTop = rootBounds.top + Math.abs(height);

						console.log("观察器回调", {
							isIntersecting: entry.isIntersecting,
							rectTop: rect.top,
							rectBottom: rect.bottom,
							adjustedTop: adjustedTop,
							fixedFirst: this.fixedFrist,
							isCanvasFixed: this.isCanvasFixed,
							initialCheck: initialCheck
						});

						// 如果是初始化检查，跳过回调执行
						if (initialCheck) {
							initialCheck = false;
							console.log("跳过初始化检查");
							return;
						}

						// 设计步骤进入视口且顶部距离满足条件时固定画布
						if (entry.isIntersecting && rect.top <= adjustedTop && rect.bottom >= adjustedTop) {
							if (!this.isCanvasFixed && typeof callback == "function") {
								console.log("触发画布固定");
								callback();
							}
							this.fixedFrist = true;
						}
						// 设计步骤离开视口时取消固定
						else if (!entry.isIntersecting && this.isCanvasFixed) {
							console.log("设计步骤离开视口，取消固定");
							this.unfixCanvas();
							this.fixedFrist = false;
						}
						// 设计步骤在视口中但位置不满足固定条件时取消固定
						else if (entry.isIntersecting && (rect.top > adjustedTop || rect.bottom < adjustedTop) && this.isCanvasFixed) {
							console.log("设计步骤位置不满足固定条件，取消固定");
							this.unfixCanvas();
						}
					});
				},
				{
					root: detailScrollBox,
					rootMargin: `0px 0px ${height}px 0px`,
					threshold: [0, 0.1, 0.5, 1], // 增加更多阈值以获得更精确的检测
				}
			);
			this.sectionObserver[key] = {
				observer: observer,
				observedEls: new Set(),
			};
		},
		safeObserve(key, dom, observer) {
			const observerInfo = this.sectionObserver[key];
			if (!observerInfo || !dom) return;
			if (observerInfo.observedEls.has(dom)) {
				console.log("该 DOM 已被监听，跳过");
				return;
			}
			observer.observe(dom);
			observerInfo.observedEls.add(dom);
		},
		setFixCanvas() {
			if (this.designStepId) {
				console.log("设置画布固定观察器", this.designStepId);
				let designStepDom = document.getElementById(`${this.designStepId}`);
				let designDomObserver = this.sectionObserver["designStepDom"];

				if (!designDomObserver) {
					let detailScrollBox = document.querySelector(".detailScrollBox");
					if (detailScrollBox) {
						let detailScrollBoxHeight = detailScrollBox.offsetHeight;
						// 计算观察器的rootMargin，确保在合适的时机触发
						let rootMarginHeight = -(detailScrollBoxHeight - this.canvasHeight / 2);
						console.log("初始化观察器参数", {
							detailScrollBoxHeight,
							canvasHeight: this.canvasHeight,
							rootMarginHeight
						});
						this.initIntersectionObserver("designStepDom", rootMarginHeight, this.designFixedFn);
					}
				}

				if (designStepDom) {
					// 延迟观察，确保不会立即触发
					setTimeout(() => {
						this.safeObserve("designStepDom", designStepDom, this.sectionObserver["designStepDom"].observer);
						console.log("开始观察设计步骤DOM");
					}, 200);
				} else {
					console.warn("未找到设计步骤DOM元素", this.designStepId);
				}
			}
		},
		// 清理所有观察器
		destroyObservers() {
			Object.keys(this.sectionObserver).forEach(key => {
				const observerInfo = this.sectionObserver[key];
				if (observerInfo && observerInfo.observer) {
					observerInfo.observer.disconnect();
					observerInfo.observedEls.clear();
				}
			});
			this.sectionObserver = {};
			console.log("已清理所有观察器");
		},

		// 添加恢复的对象并调整其位置和尺寸
		addRestoredObject(obj, uniformScale, fixedCenterOffsetX, fixedCenterOffsetY) {
			// 获取对象在固定状态下的中心点
			const objCenter = obj.getCenterPoint();

			// 计算在原始画布中的位置
			const originalCenterX = (objCenter.x - fixedCenterOffsetX) / uniformScale;
			const originalCenterY = (objCenter.y - fixedCenterOffsetY) / uniformScale;
			const newScaleX = obj.scaleX / uniformScale;
			const newScaleY = obj.scaleY / uniformScale;
			const newLeft = originalCenterX - (obj.width * newScaleX) / 2;
			const newTop = originalCenterY - (obj.height * newScaleY) / 2;

			obj.set({
				left: newLeft,
				top: newTop,
				scaleX: newScaleX,
				scaleY: newScaleY
			});
			obj.setCoords();

			this.canvas.c.add(obj);
		},

		// 完成恢复操作
		completeRestore() {
			console.log("画布恢复完成", {
				width: this.canvas.c.getWidth(),
				height: this.canvas.c.getHeight(),
				totalObjects: this.canvas.c.getObjects().length
			});

			// 验证恢复效果
			setTimeout(() => {
				this.validateCanvasRestore();
			}, 100);
		},

		// 验证画布恢复效果的方法
		validateCanvasRestore() {
			if (!this.canvas || !this.canvas.c) return;

			console.log("=== 画布状态验证 ===");
			console.log("当前画布尺寸:", {
				width: this.canvas.c.getWidth(),
				height: this.canvas.c.getHeight()
			});
			console.log("原始画布尺寸:", this.originalCanvasSize);
			console.log("是否固定状态:", this.isCanvasFixed);

			const objects = this.canvas.c.getObjects();
			console.log("画布对象数量:", objects.length);

			objects.forEach((obj, index) => {
				const center = obj.getCenterPoint();
				const aspectRatio = (obj.width * obj.scaleX) / (obj.height * obj.scaleY);

				console.log(`对象 ${index} (${obj.type}):`, {
					center: { x: Math.round(center.x), y: Math.round(center.y) },
					size: {
						width: Math.round(obj.width * obj.scaleX),
						height: Math.round(obj.height * obj.scaleY)
					},
					scale: { x: obj.scaleX.toFixed(3), y: obj.scaleY.toFixed(3) },
					angle: obj.angle,
					aspectRatio: aspectRatio.toFixed(3),
					isUniformScale: Math.abs(obj.scaleX - obj.scaleY) < 0.001 ? "是" : "否"
				});
			});

			if (this.canvas.c.backgroundImage) {
				const bg = this.canvas.c.backgroundImage;
				const bgCenter = bg.getCenterPoint();
				const bgAspectRatio = (bg.width * bg.scaleX) / (bg.height * bg.scaleY);

				console.log("背景图片:", {
					center: { x: Math.round(bgCenter.x), y: Math.round(bgCenter.y) },
					size: {
						width: Math.round(bg.width * bg.scaleX),
						height: Math.round(bg.height * bg.scaleY)
					},
					scale: { x: bg.scaleX.toFixed(3), y: bg.scaleY.toFixed(3) },
					aspectRatio: bgAspectRatio.toFixed(3),
					isUniformScale: Math.abs(bg.scaleX - bg.scaleY) < 0.001 ? "是" : "否"
				});
			}
			console.log("=== 验证结束 ===");
		},
	},
	mounted() {
		// 组件挂载时不立即设置观察器
		// 观察器将在editStatus变为true时通过watch触发
		console.log("mbSwiperArea组件已挂载", {
			editStatus: this.editStatus,
			designStepId: this.designStepId
		});
	},
	beforeDestroy() {
		// 组件销毁前清理观察器和恢复画布状态
		this.destroyObservers();
		if (this.isCanvasFixed) {
			this.unfixCanvas();
		}
		// 清理画布快照和状态
		this.canvasSnapshot = null;
		this.originalCanvasSize = { width: 0, height: 0 };
	},
};
</script>

<template>
	<div class="swiper-container-wrap" :class="{ editStatus: editStatus }">
		<div class="previewImgWrap">
			<div class="myswiper2-wrap bigColorSwiperBox" :style="mobileCanvasWidth" v-show="isMobile && hasColorSwiper">
				<div class="swiper myswiper2" ref="swiper4">
					<div class="swiper-wrapper">
						<div class="swiper-slide" v-for="(item, index) in colorImgList" :key="index">
							<div class="smallImg" v-if="isImageType(item.url)">
								<client-only>
									<template v-if="isMobile">
										<img :src="item.url" :alt="item.alt" style="width: 100%; height: 100%" />
									</template>
									<template v-else>
										<PicZoom :url="item.url" :alt="item.alt" :width="300" :scale="2" type="round"></PicZoom>
									</template>
								</client-only>
							</div>
							<VideoPlayer class="swiper-no-swiping" v-else :ref="'videoPlayer' + index" disabled-mouse :options="getVideoOptions(item, 1)"></VideoPlayer>
						</div>
					</div>
					<!-- <div class="custom-swiper-pagination" v-if="isMobile">Gallery {{ currentIndex2 + 1 }}/{{ colorImgList.length }}</div> -->
				</div>
				<div class="bigColorSwiper swiper-button-prev" v-show="!editStatus && !showThreeD"></div>
				<div class="bigColorSwiper swiper-button-next" v-show="!editStatus && !showThreeD"></div>
			</div>
			<div id="mbDesignBody" class="canvasWrap" :class="{ disabled: loadTempData }" :style="mobileCanvasWidth" v-show="editStatus">
				<div class="loadProgress" v-show="loadTempData">
					<v-progress-circular indeterminate color="primary"></v-progress-circular>
				</div>
				<div class="designBox" v-show="!firstImg">
					<div class="areaMask" v-show="isMask">
						{{ maskContent }}
					</div>
					<canvas id="fabricCanvas"></canvas>
				</div>
				<div v-show="firstImg" class="firstImg">
					<img :src="firstImg" alt="first" />
				</div>
			</div>
			<div id="product360Preview" v-show="showThreeD"></div>
			<div v-show="editStatus" class="tap-box extend-click-area" @click="zoom">
				<span><b :class="!isBig ? 'icon-a-tgsc-add' : 'icon-a-tgsc-jzhuanhuan'"></b></span>
				{{ langSemiCustom.topToZoom }}
			</div>
			<div v-show="editStatus" class="borderBox"></div>
		</div>
		<div id="dummy" style="display: none"></div>
	</div>
</template>

<style scoped lang="scss">
#product360Preview {
	position: absolute;
	display: flex;
	align-items: center;
	inset: 0;
	z-index: 1000;
	cursor: e-resize;
	background: #ffffff url("https://static-oss.gs-souvenir.com/web/quoteManage/20241024/%E7%BB%84_872_2058SW6WeS.png") (center bottom 20px) / contain no-repeat;

	::v-deep img {
		width: 100%;
		height: 100%;
		object-fit: contain;
	}
}

.myswiper2-wrap {
	position: relative;
	height: 100%;
	--swiper-navigation-color: #ffffff;
	/* 单独设置按钮颜色 */
	--swiper-navigation-size: 20px;

	/* 设置按钮大小 */
	.swiper-button-next,
	.swiper-button-prev {
		width: 2.55em;
		height: 2.55em;
		background: rgba(0, 0, 0, 0.5);
		border-radius: 50%;
		transform: translateY(-50%);
		margin-top: 0;

		@include respond-to(mb) {
			width: 2.57em;
			height: 2.57em;
		}
	}
	.swiper-button-prev {
		left: 0;
		top: 50%;
		transform: translate(-50%, -50%);

		@include respond-to(mb) {
			left: 10px;
			transform: translate(0, -50%);
			// &.bigColorSwiper.swiper-button-disabled {
			// 	opacity: 1 !important;
			// 	pointer-events: initial !important;
			// 	user-select: initial !important;
			// }
		}
	}

	.swiper-button-next {
		right: 0;
		top: 50%;
		transform: translate(50%, -50%);

		@include respond-to(mb) {
			right: 10px;
			transform: translate(0, -50%);
			&.normalSwiper.hasColorSwiper.swiper-button-disabled {
				opacity: 1 !important;
				pointer-events: initial !important;
				user-select: initial !important;
			}
		}
	}

	// 画布固定相关样式
	.canvas-fixed {
		transition: all 0.3s ease-in-out;

		// 添加固定状态下的特殊样式
		&.canvasWrap {
			background: white;
		}
	}

	// 编辑状态下的特殊样式
	.editStatus {
		.previewImgWrap {
			.canvasWrap {
				// 为画布固定做准备的样式
				transition: all 0.3s ease-in-out;
			}
		}
	}
}

.swiper-container-wrap {
	display: flex;
	gap: 6.5%;
	width: 100%;
	aspect-ratio: 495/417;

	@include respond-to(mb) {
		height: auto;
		gap: 10px;
		flex-direction: column;
		aspect-ratio: auto;
		margin: 0 -3vw;
		width: calc(100% + 6vw);
	}

	.previewImgWrap {
		flex: 1;
		position: relative;
		width: 0;
		height: 100%;

		.tap-box {
			position: absolute;
			right: 0.5em;
			bottom: 0.8em;
			z-index: 1001;
			font-size: 14px;
			color: #666;
			b {
				font-size: 14px;
			}
		}

		.borderBox {
			margin-top: 32px;
			width: 100%;
			height: 2px;
			background: rgba(0, 0, 0, 0.08);
			box-shadow: 0px -8px 9px 3px #f0f0f0;
		}

		@include respond-to(mb) {
			order: 1;
			flex: none;
			width: 100%;
		}

		.canvasWrap {
			display: flex;
			justify-content: center;
			align-items: center;
			position: absolute;
			inset: 0;
			height: 100%;
			z-index: 1000;

			.loadProgress {
				display: flex;
				justify-content: center;
				align-items: center;
				position: absolute;
				inset: 0;
				z-index: 1000;
			}

			.areaMask {
				position: absolute;
				top: 50%;
				transform: translateY(-50%);
				left: 0;
				right: 0;
				background-color: #2c2c2c;
				opacity: 0.5;
				padding: 20px;
				color: #fff;
				font-size: 16px;
				z-index: 99;
				word-break: break-word;
			}

			.designBox {
				display: flex;
				justify-content: center;
				align-items: center;
				position: absolute;
				inset: 0;
				background-color: #ffffff;

				@include respond-to(mb) {
					padding: 2px;
				}
			}

			.firstImg {
				position: absolute;
				inset: 0;

				img {
					width: 100%;
					height: 100%;
					object-fit: contain;
				}
			}
		}

		b {
			font-size: 30px;
			cursor: pointer;
		}

		.myswiper2 {
			position: relative;
			height: 100%;

			.custom-swiper-pagination {
				position: absolute;
				right: 0;
				bottom: 0;
				padding: 8px 10px;
				border-top-left-radius: 20px;
				background-color: rgba(0, 0, 0, 0.4);
				z-index: 1;
				color: #ffffff;
				font-size: 12px;
			}

			.swiper-slide {
				overflow: hidden;
				display: flex;
				justify-content: center;
				align-items: center;
				aspect-ratio: 1;
				border-radius: 10px;

				@include respond-to(mb) {
					border-radius: 0;
				}

				::v-deep .video-js video {
					object-fit: cover;
				}

				.smallImg {
					width: 100%;
					height: 100%;
					display: flex;
					align-items: center;
					justify-content: center;
					position: relative;
					text-align: center;

					::v-deep {
						.pic-img {
							width: 100%;
							height: 100%;

							.img-container {
								width: 100%;
								height: 100%;

								img {
									max-width: 100%;
									max-height: 100%;
									object-fit: cover;
								}
							}
						}
					}
				}
			}

			img {
				max-width: 100%;
				max-height: 100%;
				object-fit: cover;
			}
		}
	}

	$customWidth: 4.31em;

	.thumbImgWrap {
		flex-basis: $customWidth;

		@include respond-to(mb) {
			display: flex;
			flex-basis: auto;
			order: 2;
			padding: 0 10px;
		}

		&.isEdit {
			.myswiper1 {
				.swiper-slide.swiper-slide-thumb-active {
					border: 1px solid #f1f1f1;
				}
			}
		}

		.myswiper1 {
			width: 100%;
			max-height: calc(100% - (#{$customWidth} + 8px));
			margin-bottom: 8px;

			@include respond-to(mb) {
				max-height: unset;
				margin-bottom: 0;
				margin-left: 0;
				margin-right: 8px;
				width: calc((100% - 36px) / 6.5) !important;
				height: auto;
				max-width: calc(100% - 36px);
				&.colorSwiper {
					flex: 1;
					width: auto !important;
				}
				&.hasRotateImg {
					width: calc((100% - 36px) / 6.5 * 2) !important;
				}
				&.noColorSwiper {
					width: auto !important;
					.swiper-slide {
						width: 48px !important;
						height: 48px !important;
					}
				}
				.swiper-wrapper {
					.swiper-slide {
						&.swiper-slide-thumb-active {
							border: 1px solid #f1f1f1 !important;
						}
					}
					&.showColorSwiper {
						.swiper-slide {
							&.swiper-slide-thumb-active {
								border: 2px solid $color-primary !important;
							}
						}
					}
				}
			}

			.swiper-slide {
				position: relative;
				display: flex;
				justify-content: center;
				align-items: center;
				overflow: hidden;
				border: 1px solid #f1f1f1;
				height: $customWidth !important;
				border-radius: 4px;
				cursor: pointer;
				aspect-ratio: 1;

				.play-icon {
					display: flex;
					align-items: center;
					justify-content: center;
					position: absolute;
					left: 50%;
					top: 50%;
					transform: translate(-50%, -50%);
				}

				@include respond-to(mb) {
					// width: 48px !important;
					height: auto !important;

					&:last-child {
						margin-right: 0 !important;
					}
				}

				&:last-child {
					margin-bottom: 0 !important;
				}

				&.swiper-slide-thumb-active {
					border: 2px solid $color-primary;
				}

				img {
					width: 100%;
					height: 100%;
					object-fit: cover;
					aspect-ratio: 1/1;
				}
			}
		}

		.edit {
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			width: $customWidth;
			height: $customWidth;
			text-align: center;
			line-height: 1.2;
			color: #666666;
			border: 1px solid #f1f1f1;
			border-radius: 4px;
			cursor: pointer;

			span {
				font-size: 14px;
			}

			&.active {
				border: 2px solid $color-primary;
			}

			@include respond-to(mb) {
				padding: 0 4px;
				width: 48px;
				height: 48px;
				aspect-ratio: 1;
				flex-shrink: 0;
				&.hasColorSwiper {
					width: calc((100% - 36px) / 6.5);
					height: auto;
					margin-right: 6px;
				}

				span {
					font-size: 12px;
				}
			}

			b {
				font-size: 18px;

				@include respond-to(mb) {
					font-size: 14px;
				}
			}
		}

		.threeD {
			width: 100%;
			height: 100%;

			img {
				object-fit: contain !important;
			}
		}
		.arrowIcon {
			display: flex;
			align-items: center;
			width: calc(((100% - 36px) / 6.5) * 0.3);
			font-size: 16px;
		}
	}
}
</style>
