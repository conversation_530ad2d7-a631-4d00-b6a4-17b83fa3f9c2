<template>
	<div class="StepBox" :class="{ active: isActive() }">
		<div class="se" @click.stop="changeInput($event)">
			<template v-if="tVideoPath">
				<div class="imgWrap">
					<VideoPlayer @clicking="changeInput(null, 'video')" :style="aspectRatio" class="showPlayBtn" :options="getVideoOptions(tVideoPath, 4, bindValue.priceInfo.imagePath)"></VideoPlayer>
				</div>
				<i class="el-icon-zoom-in zoomIcon" @click.stop="viewVideo(tVideoPath)"></i>
			</template>
			<template v-else>
				<el-image v-if="imageValue == 'imageJson'" :src="filterImage(bindValue[imageValue])" fit="cover" :style="aspectRatio" style="width: 100%" lazy></el-image>
			</template>
			<span class="product-info">
				<div v-if="windowWidth <= 375" class="top" @click.stop="changeInput($event)">
					<div>
						<label :for="bindValue.cateName + bindValue.id" class="radio-beauty"></label>
						<span class="title">{{ bindValue[titleValue] }}</span>
					</div>
					<div class="type2-price">
						<PriceText :paramData="bindValue"></PriceText>
					</div>
					<div class="tips" v-if="showTip&&bindValue.tips">
						{{ bindValue.tips }}
					</div>
				</div>
				<div v-else style="display: flex; align-items: center; flex: 1; justify-content: center" @click.stop="changeInput($event)">
					<label :for="bindValue.cateName + bindValue.id" class="radio-beauty"></label>
					<div class="type2-price">
						<span class="title">{{ bindValue[titleValue] }}</span>
						<PriceText :paramData="bindValue"></PriceText>
						<div class="tips" v-if="showTip&&bindValue.tips">
							{{ bindValue.tips }}
						</div>
					</div>
				</div>
				<Corner v-if="bindValue.labelText" :backgroundColor="bindValue.labelColor" :type2="type2" position="absolute">{{ bindValue.labelText }}</Corner>
			</span>
		</div>
	</div>
</template>
<script>
import VideoPlayer from "@/components/Quote/VideoPlayer.vue";
import PriceText from "@/components/Quote/PriceText";
import Corner from "@/components/Medals/Corner";

export default {
	components: {
		VideoPlayer,
		PriceText,
		Corner,
	},
	props: {
		type2: {
			type: Boolean,
			default: false,
		},
		//绑定值
		bindValue: {
			type: Object,
		},
		//绑定名字
		bindName: {
			type: String,
		},
		titleValue: {
			type: String,
		},
		imageValue: {
			type: String,
		},
		height: {
			type: String,
		},
		likeModel: {
			type: [Array, Object],
		},
		showPrice: {
			type: Boolean,
			default: false,
		},
		showMold: {
			type: Boolean,
			default: false,
		},
		numberInput: {
			type: Boolean,
			default: false,
		},
		childList: {
			type: Boolean,
			default: false,
		},
		aspectRatio: {
			type: String,
		},
		showTip:{
			type: Boolean,
			default: false,
		}
	},
	data() {
		return {
			windowWidth: document.documentElement.clientWidth,
			picDialog: false,
			zoomPic: "",
		};
	},
	methods: {
		isJsonString(str) {
			try {
				const toObj = JSON.parse(str); // json字符串转对象
				/*
                判断条件 1. 排除null可能性
                         2. 确保数据是对象或数组
            */
				if (toObj && typeof toObj === "object") {
					return true;
				}
			} catch {}
			return false;
		},
		getVideoOptions(path, type, poster) {
			if (type === 1) {
				return {
					autoplay: true,
					controls: false,
					muted: true,
					loop: true,
					fill: true,
					sources: [
						{
							src: path,
							type: "video/mp4",
						},
					],
				};
			} else if (type === 2) {
				return {
					autoplay: true,
					controls: true,
					muted: false,
					loop: true,
					fill: true,
					sources: [
						{
							src: path,
							type: "video/mp4",
						},
					],
				};
			} else if (type === 3) {
				return {
					autoplay: true,
					controls: false,
					muted: true,
					loop: true,
					fill: true,
					sources: [
						{
							src: path,
							type: "video/mp4",
						},
					],
				};
			} else if (type === 4) {
				let newPoster = "";
				if (!this.isJsonString(poster)) {
					newPoster = poster;
				} else {
					let ph = JSON.parse(poster);
					if (ph && ph.length) {
						let tempPath = ph.find((x) => {
							return x.proType == this.proType;
						});
						newPoster = tempPath ? tempPath.path : ph[0].path;
					}
				}
				return {
					autoplay: false,
					controls: false,
					muted: true,
					loop: true,
					fill: true,
					sources: [
						{
							src: path,
							type: "video/mp4",
						},
					],
					poster: newPoster,
				};
			}
		},
		viewVideo(src) {
			this.$emit("picDialogFun", true);
			this.$emit("zoomPicFun", { key: this.bindName, value: this.bindValue });
		},
		activeFun(val) {
			this.bindValue.paramName = val.paramName;
			this.bindValue.imageJson = val.imageJson;
		},
		isJSON(str) {
			if (typeof str == "string") {
				try {
					var obj = JSON.parse(str);
					if (typeof obj == "object" && obj) {
						return true;
					} else {
						return false;
					}
				} catch (e) {
					return false;
				}
			} else {
				return false;
			}
		},
		filterImage(val) {
			if (this.isJSON(val)) {
				return JSON.parse(val)[0].url;
			} else {
				return val.url;
			}
		},
		filterPrice(val, num = 0) {
			if (val.paramType == "NORMAL") {
				if (val.priceInfo.priceType == 1) {
					//加价百分比
					return `+ Mold Fee: $${val.priceInfo.moldPrice.toFixed(0)} , + $${val.priceInfo.unitPrice?.toFixed(2)}/pc`;
				}
			} else if (val.paramType == "QUANTITY") {
				if (val.priceInfo.priceType == 1) {
					return `+ $${val.priceInfo.unitPrice?.toFixed(2)}/color/pc`;
				} else if (val.priceInfo.priceType == 5) {
					let priceArr = JSON.parse(val.priceInfo.increasePrice);
					let unitPrice;
					let findItem = priceArr.find((item) => {
						return item.inputNum >= num;
					});
					if (findItem) {
						unitPrice = findItem.unitPrice;
					} else {
						unitPrice = priceArr[priceArr.length - 1].unitPrice;
					}
					return `+ $${Number(unitPrice).toFixed(2)} each/pc`;
				}
			}
		},
		quantityChange(val) {
			this.$forceUpdate();
			this.$emit("clickFun", { key: this.bindName, value: this.bindValue });
		},
		changeInput(e, type) {
			if (type == "video") {
				this.$emit("picDialogFun", false);
				this.$emit("clickFun", { key: this.bindName, value: this.bindValue });
			} else {
				if (type == "input") {
					return;
				} else if (this.isActive() || e.target.nodeName == "svg" || e.target.nodeName == "use") {
					this.$emit("clickFun", {
						key: this.bindName,
						value: this.bindValue,
						type: "click",
					});
				} else {
					this.$emit("picDialogFun", false);
					this.$emit("clickFun", { key: this.bindName, value: this.bindValue });
				}
			}
		},
		isActive() {
			if (this.inputVal.length > 0) {
				return this.inputVal.some((item) => {
					return item.id == this.bindValue.id;
				});
			}
		},
	},
	computed: {
		tVideoPath() {
			if (!this.isJsonString(this.bindValue.priceInfo.videoPath)) return this.bindValue.priceInfo.videoPath;
			let ph = JSON.parse(this.bindValue.priceInfo.videoPath);
			if (ph && ph.length) {
				let tempPath = ph.find((x) => {
					return x.proType == this.proType;
				});
				return tempPath ? tempPath.path : ph[0].path;
			} else {
				return undefined;
			}
		},
		proType() {
			return this.$store.state.proType;
		},
		lang() {
			return this.$store.getters.lang.quote || {};
		},
		inputVal() {
			return this.likeModel[this.bindName] || [];
		},
		device() {
			return this.$store.state.device;
		},
	},
};
</script>

<style scoped lang="scss">
.StepBox {
	.picWrap {
		display: flex;
		justify-content: center;
		align-items: center;
		width: 100%;
		height: 650px;
		padding: 0px !important;
		@media screen and (max-width: 1499px) {
			height: 500px;
			width: auto;
		}
		@media screen and (max-width: 767px) {
			height: 200px;
			width: auto;
		}
	}

	$bg: #afb1b3;
	$bgc: white;
	$bgc2: #0ad2b9;
	cursor: pointer;
	display: flex;
	flex-direction: column;
	align-items: center;

	.number-input {
		margin-top: 0;
		width: 140px;
		// height: 34px;
		background: #ffffff;
		border-radius: 4px;
		@media screen and (max-width: 767px) {
			height: 28px;
		}

		::v-deep .el-input-number.is-controls-right .el-input-number__decrease {
			// line-height: 15px;
			// bottom: -1px;
		}

		::v-deep .el-input-number__increase {
			// top: 3px;
			width: 24px;
			// line-height: 16px;
		}

		::v-deep .el-input-number__decrease {
			// bottom: -1px;
			width: 24px;
			// line-height: 15px;
		}

		::v-deep .el-input__inner {
			// height: 34px;
			// line-height: 34px;
			@media screen and (max-width: 767px) {
				height: 28px;
				line-height: 26px;
				text-align: center;
			}
		}
	}

	.se {
		cursor: pointer;
		width: 100%;
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 10px 13px;

		.zoomIcon {
			position: absolute;
			top: 10px;
			right: 10px;
			color: #ffffff;
			font-size: 20px;
			z-index: 2;
		}

		@media screen and (max-width: 767px) {
			padding: 0;
		}

		.swiper {
			position: relative;
			// height: 186px;
			.swiperChild {
				position: absolute;
				right: 5px;
				top: 5px;
				z-index: 1;
				display: flex;
				flex-direction: column;

				.el-image {
					padding-bottom: 5px;
				}
			}
		}

		.el-image {
			border-radius: 6px 6px 0 0;
		}

		.product-info {
			display: flex;
			align-items: center;
			justify-content: center;
			margin-top: 10px;
			width: 100%;
			@media screen and (max-width: 767px) {
				display: grid;
				grid-template-columns: 1fr;
				margin-top: 0;
				padding-top: 10px;
				background: #f3f3f3;
			}

			.top {
				> div:first-child {
					display: flex;
					align-items: center;
					justify-content: center;
				}
			}

			.topp {
				@media screen and (max-width: 767px) {
					text-align: center;
					padding: 5px 0 0 0;
				}
			}

			.radio-beauty {
				width: 18px;
				height: 18px;
				min-width: 18px;
				box-sizing: border-box;
				display: inline-block;
				border: 1px solid $bg;
				vertical-align: middle;
				margin: 0 12px 0 3px;
				border-radius: 50%;
				background-color: $bgc;
				background-clip: content-box;
				position: relative;
				cursor: pointer;

				&::after {
					content: "";
					position: absolute;
					border-radius: 50%;
					left: 50%;
					top: 50%;
					transform: translate(-50%, -50%);
					width: 6px;
					height: 6px;
					background-color: $bg;
				}

				@media screen and (max-width: 767px) {
					width: 16px;
					height: 16px;
					min-width: 16px;
					&::after {
						width: 5px;
						height: 5px;
					}
				}
			}

			.title {
				white-space: nowrap;
			}

			.type2-price {
				display: flex;
				flex-direction: column;
				text-align: center;
				@media screen and (max-width: 767px) {
					align-items: center;
					margin-top: 5px;
					white-space: nowrap;
				}
			}
			.tips {
				white-space: wrap;
				@media screen and (max-width: 767px) {
					padding: 0 0.5em;
					text-align: center;
				}
			}
		}

		.product-price {
			font-size: 16px;
			font-family: Calibri;
			font-weight: 400;
			color: #333333;
			margin-top: 10px;

			&.free {
				color: #de3500;
			}
		}
	}

	&.active {
		.product-info .radio-beauty {
			background-color: $color-primary;
			border-color: $color-primary;

			&::after {
				background-color: $bgc;
			}
		}

		.product-info .title {
			color: $color-primary;
		}
	}
}
</style>
