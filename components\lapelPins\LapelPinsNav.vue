<template>
	<article class="lapelQuoteNav" v-if="this.cateList.length">
		<H2 class="title">{{ title }}</H2>
		<div class="mySwiper1">
			<div class="swiper" ref="mySwiper1">
				<div class="swiper-wrapper">
					<div class="swiper-slide" v-for="item in cateList" :key="item.id" :disabled="item.id === cateChildId ? true : false">
						<div class="card" :class="[item.id === cateChildId ? 'cardActiveMB' : '', noPreview ? '' : 'preview-nav']" @click="linkTo(item)">
							<div class="card-box">
								<img :src="item.id === cateChildId ? item.recommendPhoto3 : item.recommendPhoto" alt="" />
								<div class="stitle" :class="item.id === cateChildId ? 'stitleActive' : ''">
									<strong>{{ item.cateName }}</strong>
								</div>
							</div>
							<div class="card-hover" v-if="item.recommendPhoto2">
								<img :src="item.recommendPhoto2" alt="" />
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="swiper-pagination swiper-pagination-pinsNav"></div>
			<div class="swiper-button-prev pagingIcon pagingIcon1" slot="button-prev"></div>
			<div class="swiper-button-next pagingIcon pagingIcon2" slot="button-next"></div>
		</div>
	</article>
</template>
<script>
export default {
	data() {
		return {
			swiper1: null,
		};
	},
	watch: {
		cateList: {
			handler(val) {
				if (val.length) {
					this.$nextTick(() => {
						this.swiperInit();
					});
				}
			},
		},
	},
	props: {
		cateList: {
			type: Array,
			default: () => [],
		},
		cateChildId: {},
		title: {
			type: String,
		},
		customData: {
			type: Object,
			default: () => {
				return {};
			},
		},
		noPreview: {
			type: Boolean,
			default: false,
		},
		config: {
			type: Object,
			default: () => {},
		},
	},
	computed: {
		lang() {
			return this.$store.getters.lang.quote || {};
		},
		proId() {
			return this.$store.state.proId;
		},
		device() {
			return this.$store.state.device;
		},
		isMobile() {
			return this.$store.getters.isMobile;
		},
	},
	methods: {
		linkTo(val) {
			if (val.id == "-1") {
				window.open(val.customHref);
				return;
			}
			this.$router.push({
				path: val.subclassRouting,
				query: this.$route.query,
			});
		},
		swiperInit() {
			this.$nextTick(() => {
				this.swiper1 = new Swiper(this.$refs.mySwiper1, {
					slidesPerView: 8,
					spaceBetween: 6,
					slidesPerGroup: 3,
					loop: false,
					loopFillGroupWithBlank: true,
					pagination: {
						el: ".swiper-pagination-pinsNav",
						clickable: true,
					},
					navigation: {
						nextEl: ".mySwiper1 .swiper-button-next",
						prevEl: ".mySwiper1 .swiper-button-prev",
					},
					...this.config,
					breakpoints: {
						1000: {
							slidesPerView: this.config?.slidesPerView || 8,
						},
						300: {
							slidesPerView: 3,
						},
					},
				});
			});
		},
	},
	mounted() {},
};
</script>
<style lang="scss" scoped>
[disabled] {
	pointer-events: none;

	button {
		opacity: 0.5;
	}
}

.stitleActive {
	color: #537cf2;
}

.cardActiveMB {
	text-align: center;
	position: relative;
}

.lapelQuoteNav {
	font-family: Calibri;
	color: #333333;

	.card {
		position: relative;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		cursor: pointer;

		.stitle {
			font-weight: bold;
			@include respond-to(mb) {
				text-align: center;
				font-size: 12px;
			}
		}

		.card-box {
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
		}

		.card-hover {
			display: none;
		}

		img {
			width: 100% !important;
			min-height: 146px;
			border: 2px solid transparent;
			padding: 2px;
			object-fit: contain;
			@include respond-to(mb) {
				min-height: auto;
			}
		}

		button {
			border: 1px solid $color-primary;
			color: #333333;
			padding: 5px 35px;
			background: #f2f5f7;
			@media screen and (max-width: 767px) {
				font-size: 12px;
				padding: 5px 15px;
			}
		}

		@media (any-hover: hover) {
			&.preview-nav:hover {
				.card-hover {
					position: absolute;
					inset: 0;
					display: block;
				}

				button {
					color: white;
					border-color: transparent;
					border-radius: 6px;
					background: $color-primary;
				}
			}
		}
	}

	.title {
		margin-bottom: 20px;
		font-size: 20px;
		font-weight: bold;
		text-align: center;
		position: relative;

		@include respond-to(mb) {
			font-size: 16px;
		}
	}

	.mySwiper1 {
		position: relative;
		padding: 0 3.2em;
		user-select: none;
		@include respond-to(mb) {
			padding: 0 2.8em;
		}
	}

	.pagingIcon {
		&.swiper-button-disabled {
			opacity: 0.5;
		}
		z-index: 999;
		width: 3em;
		height: 3em;
		line-height: 3em;
		text-align: center;
		background: #999999;
		box-shadow: 0px 2px 6px 0px rgba(23, 29, 67, 0.3);
		// box-shadow: none;
		border-radius: 50%;
		border: none;
		position: absolute;
		top: 50%;
		color: #000;
		@include respond-to(mb) {
			width: 2.6em;
			height: 2.6em;
		}
	}
	.pagingIcon::after {
		content: "";
		position: absolute;
		width: 0;
		height: 0;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		border-top: 8px solid transparent;
		border-bottom: 8px solid transparent;
		z-index: 1;
		@include respond-to(mb) {
			border-top: 6px solid transparent;
			border-bottom: 6px solid transparent;
		}
	}
	.pagingIcon1 {
		left: 0;
		&::after {
			left: 46%;
			border-right: 10px solid #fff;
		}
		@include respond-to(mb) {
			&::after {
				border-right: 8px solid #fff;
			}
		}
	}
	.pagingIcon2 {
		right: 0;
		&::after {
			left: 54%;
			border-left: 10px solid #fff;
		}
		@include respond-to(mb) {
			&::after {
				border-left: 8px solid #fff;
			}
		}
	}

	//自定义分页器位置
	::v-deep .swiper-pagination.swiper-pagination-pinsNav {
		position: absolute;
		bottom: -16px !important;
		.swiper-pagination-bullet {
			height: 6px;
			width: 6px;
			opacity: 1;
			background-color: #dfdfdf;
			cursor: pointer;
			margin: 0 2px;

			&.swiper-pagination-bullet-active {
				width: 16px;
				border-radius: 6px;
				background: #ff6600;
			}
		}
		@include respond-to(mb) {
			.swiper-pagination-bullet {
				height: 6px;
				width: 6px;
				&.swiper-pagination-bullet-active {
					width: 12px;
					border-radius: 6px;
				}
			}
		}
	}
}
.lapelQuoteNav * {
	font-family: Calibri;
}
</style>
