const env = require("./env");
const optimization = process.env.NODE_ENV === "production" ? {
	minimize: true,
	splitChunks: {
		chunks: 'all',
		automaticNameDelimiter: '-',
		name: true,
		cacheGroups: {
			lang: {
				test: /assets[\\/]lang[\\/]/,
				name: 'lang',
				priority: 30,
				chunks: 'all'
			},
			commons: {
				name: 'commons',
				test: /node_modules[\\/](fabric.js|vuetify|element-ui)[\\/]/,
				priority: 20,
				minChunks: 2,
			},
			extracted: {
				name: 'extracted',
				priority: 10,
				minChunks: 10,
			}
		}
	}
} : {};
module.exports = {
	ssr: true,
	target: "server",
	head: {
		__dangerouslyDisableSanitizers: ["script", "noscript", "style"], //不转义script标签的内容,用于结构化数据
		meta: [{ charset: "utf-8" }, { name: "viewport", content: "width=device-width, initial-scale=1, user-scalable=no" }],
		link: [
			{
				//多色-O2O、经销商后台
				rel: "stylesheet",
				href: "//at.alicdn.com/t/c/font_3500115_hwb28bsfq3n.css",
				body: true,
			},
			{
				//单色-O2O、经销商后台
				rel: "stylesheet",
				href: "//at.alicdn.com/t/c/font_3500108_bxucohroob7.css",
				body: true
			},
			{
				rel: "stylesheet",
				href: "https://fonts.googleapis.com/css2?family=Acme&family=Amaranth&family=Anton&family=Boogaloo&family=Chewy&family=Concert+One&family=Courgette&family=Fjalla+One&family=Francois+One&family=Fredoka+One&family=Fugaz+One&family=Great+Vibes&family=Hanalei+Fill&family=Kaushan+Script&family=Lobster+Two&family=MedievalSharp&family=Oleo+Script+Swash+Caps&family=Oswald:wght@200..700&family=Pacifico&family=Palanquin+Dark&family=Passion+One&family=Permanent+Marker&family=Ranga&family=Righteous&family=Roboto:wght@100&family=Ruslan+Display&family=Sigmar+One&family=Timmana&family=Viga&family=Quicksand&display=swap",
				body: true,
			},
			{
				rel: "stylesheet",
				href: "https://cdnjs.cloudflare.com/ajax/libs/MaterialDesign-Webfont/6.9.96/css/materialdesignicons.min.css",
				body: true,
			},
			{
				rel: "stylesheet",
				href: "https://static-oss.gs-souvenir.com/web/quoteManage/20241015/video_2049XMTR46.css",
				body: true,
			},
			{
				rel: "stylesheet",
				href: "https://static-oss.gs-souvenir.com/web/quoteManage/20241015/swiper_2049TcHSbt.css",
				body: true,
			},
		],
		script: [{ src: "https://static-oss.gs-souvenir.com/web/quoteManage/20241015/swiper_20493k6MyM.js" }, { src: "https://static-oss.gs-souvenir.com/static/js/jquery-3.7.0.min.js" }, { src: "https://cdn.sheetjs.com/xlsx-0.19.3/package/dist/xlsx.mini.min.js", async: true, defer: true }, { src: "https://static-oss.gs-souvenir.com/web/quoteManage/20241015/video_2049JasChi.js" }],
	},
	static: {
		prefix: false,
	},
	css: [{ src: "~/assets/css/index.scss", lang: "scss" }, { src: "~/assets/css/font.scss" }, { src: "~/assets/css/neon/neonFont.css" }, { src: "~/assets/css/theme.scss" }],
	styleResources: { scss: ["~/assets/css/var.scss"] },
	// nuxt.config.js
	buildModules: [
		// 和选项一起
		[
			"@nuxtjs/vuetify",
			{
				customVariables: ["~/assets/css/variables.scss"],
				defaultAssets: {
					icons: false,
				},
				treeShake: true,
				optionsPath: "~/plugins/vuetify.options-manage.js",
			},
		],
	],

	plugins: ["~/plugins/axios", "~/plugins/snack", "~/plugins/directive", { src: "~/plugins/echarts", ssr: false }, { src: "~/plugins/globalBus", ssr: false }, { src: "~/plugins/cropper", ssr: false }, "~/plugins/router", { src: "~/plugins/vuetifyDraggableTreeview", ssr: false }, { src: "~/plugins/globalMethods.js" }],

	components: true,

	modules: ["@nuxtjs/axios", "@nuxtjs/style-resources", "@nuxtjs/toast", "cookie-universal-nuxt", "@nuxtjs/i18n"],
	i18n: {
		strategy: "no_prefix",
		locales: [
			{ name: "English", code: "en", iso: "en-US", file: "en.js" },
			// { name: '简体中文', code: 'zh', iso: 'zh-cn', file: 'zh.js' },
		],
		lazy: false,
		langDir: "~/assets/manageLang/",
		defaultLocale: "en",
	},
	server: {
		port: 40054, // default: 3000
		host: "localhost", // default: localhost,
		timing: false,
	},

	render: {
		resourceHints: false, //禁用资源预加载
	},

	toast: {
		containerClass: "toast-box",
		position: "top-center",
		duration: "3000",
		theme: "outline",
		keepOnHover: true,
		singleton: true,
	},
	env: {
		...env[process.env.MODE],
		clientId: "AWzqx3sFPqf9ValR6YHxRxhW8aCc45nxcm6vqsA3s6HW9rU-rbRgQkKOye7I7osRqUo-2-Wslg71CqRN",
		dataPartnerAttributionId: "FLAVORsb-gr10b16985777_MP",
		payId: 10000,
	},
	vue: {
		config: {
			productionTip: false,
		},
	},
	//打包优化
	build: {
		cache: false, //压缩JS和缓存其他loaders的处理结果。
		hardSource: false, //模块缓存
		transpile: [/^v-code-diff/],
		vendor: ["vue-cropper"],
		analyze: false,
		postcss: {
			plugins: {
				cssnano: {
					preset: [
						"default",
						{
							calc: false,
						},
					],
				},
			},
		},
		babel: {
			compact: false,
			plugins: [
				[
					"component",
					{
						libraryName: "element-ui",
						styleLibraryName: "theme-chalk",
					},
				],
			],
		},
		// //分割vendor.app.js文件(打包优化)
		optimization,
	},
};
