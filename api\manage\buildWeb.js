import { request } from '~/utils/request'

//保存页面到草稿箱
export function editPagesAllSample(data) {
    return request({
        url: '/retailer/page/pageRowDraft/editPagesAllSample',
        method: 'post',
        data,
    })
}
//获取所有模板
export function listAllTemplate(data) {
    return request({
        url: '/retailer/sample/listAllSample',
        method: 'post',
        data,
    })
}
//分页获取模板
export function listSample(data) {
    return request({
        url: '/retailer/sample/listSample',
        method: 'post',
        data,
    })
}
//获取风格列表
export function listWebsiteStyleBySample(data) {
    return request({
        url: '/retailer/websiteStyle/listWebsiteStyleBySample',
        method: 'post',
        data,
    })
}
//获取页面内容
export function getPagesDraftById(data) {
    return request({
        url: '/retailer/page/getPagesDraftById',
        method: 'post',
        data,
    })
}
//获取所有网站风格
export function listAllStyle(data) {
    return request({
        url: '/retailer/websiteStyle/listAllWebsiteStyle',
        method: 'post',
        data,
    })
}
//获取所有页面
export function listAllPages(data) {
    return request({
        url: '/retailer/page/listAllPages',
        method: 'post',
        data,
    })
}
//编辑页面主题或字体
export function editPageThemeFont(data) {
    return request({
        url: '/retailer/page/editPageThemeFont',
        method: 'post',
        data,
    })
}
//查询全部的主题颜色
export function listAllColor(data) {
    return request({
        url: '/retailer/websiteStyle/themeColor/listAllThemeColor',
        method: 'post',
        data,
    })
}
//获取页头页尾模板页草稿箱
export function getHeadFootPagesDraft(data) {
    return request({
        url: '/retailer/page/getHeadFootPagesDraft',
        method: 'post',
        data,
    })
}
//移动模板到另一页面
export function movePageSampleToOtherPage(data) {
    return request({
        url: '/retailer/page/pageRowDraft/movePageSampleToOtherPage',
        method: 'post',
        data,
    })
}
//分页获取页面
export function listPage(data) {
    return request({
        url: '/retailer/page/listPage',
        method: 'post',
        data,
    })
}
//根据风格id获取对应页头页脚模板页的页头页脚模板
export function getHeadFootPageSampleList(data) {
    return request({
        url: '/retailer/websiteStyle/websiteStylePage/getHeadFootPageSampleList',
        method: 'post',
        data,
    })
}
//分页获取banner图
export function listRetailerSampleBanner(data) {
    return request({
        url: '/retailer/sample/sampleBanner/listRetailerSampleBanner',
        method: 'post',
        data,
    })
}
export function setProLogo(data) {
    return request({
        url: '/retailer/page/setProLogo',
        method: 'post',
        data,
    })
}
//获取所有banner视频
export function listVideo(data) {
    return request({
        url: '/retailer/sample/video/listVideo',
        method: 'post',
        data,
    })
}

//用户上传banner图
export function uploadSampleBanner(data) {
    return request({
        url: '/retailer/sample/sampleBanner/uploadSampleBanner',
        method: 'post',
        data,
    })
}

//删除单个banner图
export function delSampleBannerById(data) {
    return request({
        url: '/retailer/sample/sampleBanner/delSampleBannerById',
        method: 'post',
        data,
    })
}

//新增
export function addCustomPage(data) {
    return request({
        url: '/retailer/page/addCustomPage',
        method: 'post',
        data,
    })
}

//人群
export function getAudienceList(data) {
    return request({
        url: '/retailer/audience/getAudienceList',
        method:"post",
        data
    })
}

//获取页头列表
export function getHeaderList(data) {
    return request({
        url: '/retailer/header/getHeaderList',
        method: 'get',
        params: data,
    })
}
//获取页头详情
export function getHeaderDetail(data) {
    return request({
        url: '/retailer/header/getHeaderDetail',
        method: 'get',
        params: data,
    })
}
//新增/编辑页头
export function editHeader(data) {
    return request({
        url: '/retailer/header/editHeader',
        method: 'post',
        data,
    })
}
//删除页头
export function delHeader(data) {
    return request({
        url: '/retailer/header/delHeader',
        method: 'get',
        params: data,
    })
}
//页头拖拽排序
export function sortHeader(data) {
    return request({
        url: '/retailer/header/sortHeader',
        method: 'post',
        data,
    })
}
//获取产品列表
export function getProductCateList(data) {
    return request({
        url: '/retailer/product/getProductCateList',
        method: 'get',
        params: data,
    })
}
//获取产品列表 stikcer
export function getStickerProductCateList(data) {
    return request({
        url: '/retailer/product/getStickerProductCateList',
        method: 'get',
        params: data,
    })
}

//TDK国家语言
export function getPageById(data) {
    return request({
        url: '/retailer/page/getPageById',
        method: 'post',
        data,
    })
}

//获取动态产品模板的半定制产品列表
export function getSemiProductList(data) {
    return request({
        url: '/retailer/product/getSemiProductList',
        method: 'get',
        params: data,
    })
}
//获取动态模板报价父分类列表接口
export function getQuoteParentCateList(data) {
    return request({
        url: '/retailer/product/getQuoteParentCateList',
        method: 'get',
        params: data,
    })
}
//获取经销商端动态产品模板的产品接口
export function getSemiQuoteProductList(data) {
    return request({
        url: '/retailer/product/getSemiQuoteProductList',
        method: 'get',
        params: data,
    })
}

//获取多语言列表
export function getPageRowEditTagById(params) {
	return request({
		url: "/retailer/page/pageRowDraft/getPageRowEditTagById",
		method: "get",
		params
	});
}


//获取多语言列表
export function getAllQuoteSemiCateProductList(params) {
	return request({
		url: "/retailer/product/getAllQuoteSemiCateProductList",
		method: "get",
		params
	});
}

//移动页面操作
export function copyPageToOtherPage(params) {
	return request({
		url: "/retailer/page/pageRowDraft/copyPageToOtherPage",
		method: "get",
		params
	});
}