<template>
	<div id="paging">
		<v-pagination @input="pageChange" v-model="page" :length="15" :total-visible="7" circle color="#1A73E8"
			prev-icon="mdi-arrow-left" next-icon="mdi-arrow-right"> </v-pagination>
		<div class="go-page">
			<input type="text" class="pl-3" v-model="jumpPage" />
			<button @click="pageJump">Go</button>
		</div>
	</div>
</template>

<script>
export default {
	name: "Paging",
	props: {

	},
	data() {
		return {
			page: 1,
			jumpPage: 1
		};
	},
	methods: {
		pageChange(numberOfPages) {
			// console.log(numberOfPages);
			this.$emit('pageChange', numberOfPages)
		},
		pageJump() {
			// console.log(this.jumpPage);
			this.$emit('pageJump', this.jumpPage)
		},
	}
};
</script>

<style lang="scss" scoped>
#paging {
	display: flex;
	justify-content: flex-end;
	padding-right: 2.4479vw;

	.go-page {
		display: flex;
		align-items: center;

		input {
			width: 3.6458vw;
			height: 1.5625vw;
			background: #ffffff;
			border: .0521vw solid #e3e3e6;
			border-radius: .3125vw;
			margin-right: .2083vw;
		}

		button {
			width: 2.6042vw;
			height: 1.5625vw;
			background: #1a73e8;
			border-radius: .3125vw;
		}
	}
}
</style>

<style lang="scss">
#paging {

	/* 设置分页组件的页数item */
	.v-pagination__item {
		height: 1.5625vw;
		width: 1.5625vw;
		min-width: 0.2604vw;
		background-color: rgba(0, 0, 0, 0);

		font-size: 0.8333vw;
		box-shadow: none;
	}

	/* 设置上一页，下一页button */
	.v-pagination__navigation {
		background-color: rgba(0, 0, 0, 0);
		box-shadow: none;
		width: 1.5625vw;
		height: 1.5625vw;
		margin-right: 1.3021vw;
		margin-left: 1.3021vw;
	}

	/* 设置上一页，下一页button的图标 */
	.v-icon {
		color: #666666;
	}

	/* 设置整个分页组件的对齐 */
	.v-pagination {
		justify-content: end;
	}
}
</style>
