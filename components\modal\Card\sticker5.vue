<template>
	<div class="summary-box" :class="modal.wrapClass" :style="modal.style">
		<div class="bps-container">
			<div class="faq">
				<div class="container">
					<EditDiv v-if="modal.title" tag-name="h2" v-model:content="modal.title.value" @click="setModalType(modal.title,modal,'text')" />
					<ul id="faq" class="contentPart" hoverList="outer">
						<li style="position:relative" v-for="(o, oi) in modal.outer" :key="oi" class="part2 hover-tag" :childHoverIndex="oi" >
							<div flex class="question">
								<EditDiv v-if="o.title" class="questions-item" v-model:content="o.title.value" @click="setModalType(o.title,modal.outer,'text')" />
								<div class="faq_img"></div>
								<!-- <strong>+</strong> -->
							</div>
							<EditDiv v-if="o.subTitle" class="answer" v-model:content="o.subTitle.value" @click="setModalType(o.subTitle,modal.outer,'text')" />
						</li>
					</ul>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	props: {
		data: {
			type: Object,
			default: {},
		}
	},
	data() {
		return {
			modal: {
				style: {},
				type: {index: 0, clickPosition: 'outer'},
				...this.data
			}
		};
	},
	watch: {
		modal: {
			handler(val) {
				if (process.env.isManage) this.$emit("update:data", val);
			},
			immediate: true,
			deep: true
		}
	},
	methods:{
		setModalType(target, targetArray, clickType, event, other) {
			this.$setModal(this, target, targetArray, clickType, event, other)
		},
		toggleFaq() {
			let findParent = (node, k) => {
				let findNode = "";
				let find = (node, k) => {
					let nodeName = node.nodeName.toUpperCase();
					if (nodeName != k) {
						find(node.parentNode, k);
					} else {
						findNode = node;
					}
				};
				find(node, k);
				return findNode;
			};

			function isHidden(el) {
				return el.style.display === "none" || el.style.display === "";
			}

			let faq = document.getElementById("faq");
			faq.addEventListener("click", e => {
				let target = e.target;
				if (target.nodeName.toUpperCase() === "UL") {
					return;
				}
				let liNode = findParent(target, "LI");
				let answer = liNode.getElementsByClassName("answer")[0];
				let faqImg = liNode.getElementsByClassName("faq_img")[0];
				let answerList = Array.from(faq.getElementsByClassName("answer"));
				let faqImgList = Array.from(faq.getElementsByClassName("faq_img"));
				if (isHidden(answer)) {
					answer.style.display = "block";
					faqImg.style.background  = "url('https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230328/20230328EkJrQC6c.png')center/contain no-repeat";
					console.log(faqImg.style)
				} else {
					answer.style.display = "none";
					faqImg.style.background  = "url('https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230328/20230328ksdmRa6Q.png')center/contain no-repeat";
				}
				answerList.forEach(item => {
					if (item !== answer) {
						item.style.display = "none";
					}
				});
				faqImgList.forEach(item => {
					if (item !== faqImg) {
						item.style.background  = "url('https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230328/20230328ksdmRa6Q.png')center/contain no-repeat";
					}
				});
			});
		},
	},
	created() {
		if (process.browser) {
			document.querySelector('script#faqStruct')?.remove();

			let script = document.createElement('script');
			script.id = 'faqStruct';
			script.type = 'application/ld+json';
			script.text = JSON.stringify({
				"@context": "http://schema.org",
				"@id": `https://${this.$store.state.proUrl}/`,
				"@type": "FAQPage",
				"name": this.$store.state.proName,
				"url": `https://${this.$store.state.proUrl}/`,
				"mainEntity": this.data.outer.map(i => {
					return {
						"@type": "Question",
						"name": i.title?.value || '',
						"acceptedAnswer": { "@type": "Answer", "text": i.subTitle?.value }
					}
				})
			});
			document.querySelector('head').appendChild(script);
		}
	},
	mounted() {
		if (document.getElementById("faq")) this.toggleFaq();
	}
};
</script>

<style lang="scss" scoped>
.home-part5 {
	padding: 70px 0;
	.bps-container {
		.faq {
			padding-bottom: 2vmax;
			h2 {
				font-size: 36px;
				text-align: center;
				margin-bottom: 47px;
			}
			ul {
				margin: 0 !important;
			}
			li {
				list-style-type: none;
				border-bottom: 1px solid #dddddd;
				&:first-child {
					border-top: 1px solid #dddddd;
				}
			}

			.question {
				padding: 2% 0;
				cursor: pointer;
				justify-content: space-between;
				.questions-item {
					font-size: 15px;
					font-family: Roboto;
					font-weight: bold;
					color: #333333;
					font-family: 'Quicksand';
				}
			}

			.faq_img {
				width: 8px;
				height: 8px;
				flex-shrink: 0;
				background: url("https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230328/20230328ksdmRa6Q.png") center/contain no-repeat;
			}

			.answer {
				display: none;
				margin-bottom: 2%;
				font-size: 15px;
				font-family: 'Quicksand';
				font-weight: 300;
				color: #333333;
			}
		}
	}
}

@media screen and (max-width: $mb-width) {
	.home-part5 {
		padding: 27px 0;
		.bps-container {
			.faq {
				padding-bottom: 2vmax;
				h2 {
					font-size: 16px;
					text-align: left;
					margin-bottom: 15px;
				}
			}
		}
	}
}
</style>
