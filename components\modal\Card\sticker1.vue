<template>
	<div class="summary-box" :class="modal.wrapClass" :style="modal.style">
		<div class="bps-container" v-for="(o, oi) in modal.outer" :key="oi">
			<div v-if="modal.outer[0].video" class="imgWrap" @click="setModalType(o.video,modal.outer,'video')" >
				<video :src="modal.outer[0].video.value"  autoplay loop muted playsinline ></video>
			</div>
			<div class="imgWrap" v-else>
				<pic :src="o.img.value" :alt="o.img.alt" @click="setModalType(o.img,modal.outer,'img')" />
			</div>
			<div class="content">
				<EditDiv tag-name="h2" v-model:content="modal.outer[0].title.value" @click="setModalType(o.title,modal.outer,'text')" />
				<EditDiv class="des" v-model:content="modal.outer[0].subTitle.value" @click="setModalType(o.subTitle,'text')" />
				<div class="contentGrid">
					<div class="item" v-for="(item,index) in modal.outer[0].list">
						<pic class="pic" :src="item.icon.value" :alt="item.icon.alt" @click="setModalType(o.list[index].icon,modal.outer,'img')"/>
						<EditDiv class="des" v-model:content="item.title.value" @click="setModalType(o.list[index].title,modal.outer,'text')" />
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	props: {
		data: {
			type: Object,
			default: {},
		}
	},
	data() {
		return {
			modal: {
				style: {},
				type: {index: 0, clickPosition: 'outer'},
				...this.data
			}
		};
	},
	watch: {
		modal: {
			handler(val) {
				if (process.env.isManage) this.$emit("update:data", val);
			},
			immediate: true,
			deep: true
		}
	},
	methods:{
		setModalType(target, targetArray, clickType, event, other) {
			this.$setModal(this, target, targetArray, clickType, event, other)
		},
	}
};
</script>

<style lang="scss" scoped>
.productStyle1 {
	background-color: #F7F7F8;
}
.summary-box {
	padding: 70px 0;

	.bps-container {
		display: grid;
		align-items: center;
		position: relative;
		grid-template-columns: repeat(2, 1fr);
		grid-gap: 73px;

		.btnWrap {
			display: inline-block;
			color: #ffffff;
		}

		&>.pic {
			border-radius: 10px;
		}

		.content {
			.contentGrid {
				display: grid;
				grid-template-columns: repeat(2, 1fr);
				grid-column-gap: 46px;
				div {
					img {
						width: auto;
						vertical-align: middle;
					}
					div {
						vertical-align: middle;
						display: inline-block;
						width: 60%;
						margin-left: 19px;
					}
				}
			}
			h1,
			h2 {
				font-size: 36px;
				text-align: left;
			}
			&>.pic{
				display: none;
			}

			.des {
				margin: 15px 0;
				line-height: 24px;
				color: #666666;
			}
			.conter {
				display: grid;
				grid-template-columns: repeat(2, 1fr);
				grid-column-gap: 30px;
				grid-row-gap: 10px;
				font-size: 14px;
				font-weight: 400;
				color: #333;
				.item {
					span {
						display: inline-block;
						width: 8px;
						height: 8px;
						background: #939393;
						border-radius: 50%;
						margin-right: 20px;
					}
				}
			}
		}
	}
}

.home-part3 {
	background-color: #F8F8F8;

	.bps-container {
		position: relative;
		grid-template-columns: 1fr 1.2fr;
		grid-gap: 70px;

		.content {
			grid-row: 1/2;
		}
	}
}

.about-part {
	padding: .7813vw 0 2.6042vw;
	background-color: #FFF;

	.bps-container {
		max-width: 74.4792vw;
		position: relative;
		grid-template-columns: 1fr 1.7fr;
		grid-gap: 1.5vw;
		.content {
			grid-row: 1/2;
			margin-left: 0;
		}
	}
	.bps-button{
		margin-top: 0;
	}

	.content{
		margin-left: 1.3021vw;
	}
}

@media screen and (max-width: $mb-width) {
	.summary-box {
		padding: 27px 0;
		background-color: #ffffff;

		.bps-container {
			display: grid;
			grid-template-columns: 1fr;
			align-items: center;
			grid-gap: 25px;

			.content {
				.contentGrid {
					display: grid;
					grid-template-columns: repeat(3, 1fr);
					grid-column-gap: 0px;
					.item::after {
						position: absolute;
						content:"";
						width: 1px;
						height: 50%;
						top: 50%;
						transform: translateY(-50%);
						right: 0;
						background: #E2E2E2;
					}
					div {
						text-align: center;
						padding: 0 5%;
						position: relative;

						img {
							width: auto;
							vertical-align: middle;
						}
						div {
							vertical-align: middle;
							display: block;
							width: 100%;
							margin-left: 0px;
						}
					}
				}
				h2 {
					font-size: 21px;
					margin-bottom: 20px;
				}

				.des {
					margin: 10px 0;
					font-size: 12px !important;
				}
			}
		}
	}
	.home-part3 {
		.bps-container {
			&>.pic {
				display: none;
			}
			.content {
				grid-row: none;
				&>.pic {
					display: block;
				}
			}
		}
	}

	.about-part {
		padding: 9.0667vw 4.4vw 0 3.2vw;
		.bps-container {
			display: block;
			max-width: 100%;
			padding: 0 !important;

			.bps-button {
				font-size: 3.2vw;
				height: 9.0667vw;
				border-radius: .8vw;

				b.icon-bps-sanjiao {
					margin-left: 1.3333vw;
					font-size: 3.2vw;
				}
			}

			&>.pic {
				display: none;
			}
			.content {
				grid-row: none;

				h2{
					font-size: 5.6vw;
					margin-left: .3646vw;
				}
				&>.pic {
					display: block;
				}

				.des{
					font-size: 3.2vw !important;
					line-height: 4.8vw;
					margin: 0 .4vw 0 1.4667vw;
				}
			}

			.btnWrap{
				margin-top: 6vw;
			}
		}
	}
}
</style>
