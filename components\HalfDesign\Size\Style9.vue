<template>
	<div class="sizeStyle_style9 mb-4" :class="stepData.styleName">
	    <slot name="stepText"></slot>
		<div class="stepContent">
			<div class="step-item" v-for="(item, index) in stepData.productParamList" :key="item.id"
				:class="{ active: selectIndex == index || item.inputNum > 0 }" @click="selectStep(item, index)">
				<div class="inputMain">
					<button @click.stop="subNum(item, index)" class="subBtn"><v-icon>mdi-minus</v-icon></button>
					<div class="inputBox">
						<input class="priceInputStyle6" @blur="selectIndex = -1" :ref="`myIunputStyle6${index}`"
							:disabled="!item.stock" type="text" v-model="item.inputNum" @keyup="formatNum(item)"
							@change="updatePrice" placeholder="0" />
					</div>
					<button @click.stop="addNum(item, index)" class="addBtn"><v-icon>mdi-plus</v-icon></button>
				</div>
			</div>
		</div>
		<div class="errorTip" ref="errorTip">
			<v-alert dense outlined type="error">{{ langSemiCustom.miniQty }} {{ productInfo.lowestPurchaseQuantity || 1
				}}
			</v-alert>
		</div>
	</div>
</template>

<script>
export default {
	inject: ['getProductInfo'],
	name: 'Style6',
	props: {
		stepData: {
			type: Object,
			default: () => ({})
		}
	},
	data() {
		return {
			selectIndex: -1,
			selectItem: null
		}
	},
	computed: {
		productInfo() {
			return this.getProductInfo()
		},
		langSemiCustom() {
			return this.$store.getters.lang?.semiCustom
		}
	},
	methods: {
		subNum(item, index) {
			item.inputNum=+item.inputNum-1
			if (!item.inputNum||item.inputNum<=0) item.inputNum=1
			this.selectIndex=index
			this.selectItem=item
			this.updatePrice()
			this.$forceUpdate()
		},
		addNum(item, index) {
			if (!item.inputNum) item.inputNum=0
			if (item.inputNum>=item.stock) return
			item.inputNum=+item.inputNum+1
			this.selectIndex=index
			this.selectItem=item
			this.updatePrice()
			this.$forceUpdate()
		},
		formatNum(item) {
			item.inputNum=(item.inputNum+'').replace(/[^\d]/g, '')
			if (item.stock&&item.stock>0&&item.inputNum>item.stock) {
				item.inputNum=String(item.stock)
			}
			if (item.stock<=0) {
				item.inputNum=''
			}
		},
		async updatePrice() {
			this.$forceUpdate()
			this.$nextTick(() => {
				let sum=this.selectItem.inputNum
				let errDom=this.$refs.errorTip
				if (sum<this.productInfo.lowestPurchaseQuantity) {
					errDom.style.display='block'
				} else {
					errDom.style.display='none'
				}
				this.$emit('selectStep', {
					type: this.stepData.attributeFlag,
					data: this.stepData.productParamList[this.selectIndex],
					id: this.stepData.id,
					firstSelect: false,
					cancel: !(sum>this.productInfo.lowestPurchaseQuantity)
				})
				// this.$emit("updatePrice");
			})
		},
		selectStep(item, index) {
			if (item.stock<=0) {
				return
			}
			this.selectIndex=index
			this.selectItem=item
			this.$refs[`myIunputStyle6${index}`][0].focus()
			this.updatePrice()
		},
		setInputNum(num) {
			if (this.stepData.attributeFlag=='quantity') this.stepData.productParamList.forEach(item => { item.stock=9999 })
			this.stepData.productParamList[0].inputNum=num
			this.selectItem=this.stepData.productParamList[0]
			this.$forceUpdate()
			this.selectIndex=0
			this.$emit('selectStep', {
				type: this.stepData.attributeFlag,
				data: this.stepData.productParamList[0],
				id: this.stepData.id,
				colorIndex: 0,
				firstSelect: true
			})
		}
	},
	created() { },
	mounted() {
		this.$Bus.$on('selectDefaultSizeStep', this.setInputNum)
	},
	beforeDestroy() {
		this.$Bus.$off('selectDefaultSizeStep', this.setInputNum)
	}
}
</script>

<style lang="scss" scoped>
@import '~assets/css/half.scss';

.sizeStyle_style9 {
	width: 100%;
	height: 100%;
	overflow: hidden;
	position: relative;

	.stepContent {
		width: 100%;
		height: 100%;
		box-sizing: border-box;
		display: grid;
		grid-template-columns: repeat(5, 1fr);
		gap: 8px;

		.step-item {
			position: relative;
			display: flex;
			align-items: center;
			justify-content: center;
			flex-direction: column;
			border: none;
			row-gap: 8px;
			padding: 0;
			background-color: #FFF;

			.inputMain {
				display: flex;
				align-items: center;
				justify-content: space-between;
				background: #fff;
				border-radius: 4px;
				border: 1px solid #d3d5d7;

				&:focus-within {
					border: 1px solid $color-primary;

					.addBtn,
					.subBtn {
						border-color: $color-primary;
					}
				}

				.addBtn,
				.subBtn {
					height: 100%;
					padding: 0 6px;
					cursor: pointer;

					.v-icon {
						font-size: 20px;
					}
				}

				.subBtn {
					border-right: 1px solid #d3d5d7;
				}

				.addBtn {
					border-left: 1px solid #d3d5d7;
				}

				.subBtn:active,
				.addBtn:active {
					.v-icon {
						color: $color-primary;
					}
				}

				.inputBox {
					input {
						line-height: 32px;
						max-width: 100%;
						min-width: 0;
						width: 100%;
						text-align: center;
						font-weight: 400;
						font-size: 16px;
						color: #333333;
						text-align: center;
						border-radius: 6px;

						&:focus {
							color: $color-primary;

							&::placeholder {
								opacity: 0;
							}
						}
					}
				}
			}
		}
	}
}

@include respond-to(mb) {
	.sizeStyle_style9 {
		.stepContent {
			grid-template-columns: repeat(4, 1fr);
			gap: 4px;

			.step-item {
				padding: 6px;
				row-gap: 2px;

				&.active {
					padding: 5px;
				}

				@media (any-hover: hover) {
					&:hover {
						padding: 5px;
					}
				}

				.addBtn,
				.subBtn {
					.v-icon {
						font-size: 16px !important;
					}
				}

				.inputMain {
					.inputBox {
						input {
							font-size: 13px;
						}
					}
				}
			}
		}
	}
}
</style>
