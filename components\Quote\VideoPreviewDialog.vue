<template>
	<BaseDialog class="customDialog" :value="picDialog" @update="closeDialog" :width="device !== 'mb' ? '800px' : '95%'">
		<div class="pic-wrap">
			<template v-if="getFileSuffix(zoomPic) === '.mp4'">
                <div class="video-wrap" :style="{ aspectRatio: videoAspectRatio }">
                    <div class="load-box" v-if="isMediaLoading">
                        <Loading></Loading>
                    </div>
                    <VideoPlayer disabledMouse :options="videoOptions"></VideoPlayer>
                </div>
			</template>
			<template v-else>
				<img :src="zoomPic" alt="preview" />
			</template>
			<DialogBM :dialogItem="selectedParamsValue" @dialogNextStep="dialogNextStep" v-if="tempType == 'video'"></DialogBM>
			<QtyAndBtn v-else :dialogItem.sync="selectedParamsValue" :noQty="noQtyParamNameList.includes(selectedParamsValue.paramName)" :noComment="noCommentParamNameList.includes(selectedParamsValue.paramName)" @qtyAndBtnConfirm="qtyAndBtnConfirm" @qtyAndBtnNext="qtyAndBtnNext"></QtyAndBtn>
		</div>
	</BaseDialog>
</template>

<script>
import BaseDialog from "@/components/Quote/BaseDialog.vue";
import DialogBM from "@/components/Medals/DialogBM.vue";
import QtyAndBtn from "@/components/Medals/QtyAndBtn.vue";
import { getFileSuffix } from "@/utils/utils";
import VideoPlayer from "@/components/Quote/VideoPlayer.vue";
import Loading from "@/components/Loading.vue";

export default {
	props: {
		picDialog: {
			type: Boolean,
		},
		zoomPic: {
			type: String,
		},
		tempType: {
			type: String,
		},
		selectedParamsValue: {
			type: Object,
		},
	},
	methods: {
		closeDialog(val) {
			this.$emit("update:picDialog", val);
		},
		getFileSuffix,
		qtyAndBtnConfirm() {
            this.$emit("qtyAndBtnConfirm")
        },
		qtyAndBtnNext() {
            this.$emit("qtyAndBtnNext")
        },
		dialogNextStep() {
            this.$emit("dialogNextStep")
        },
	},
	components: {Loading, VideoPlayer, QtyAndBtn, DialogBM, BaseDialog },
	data() {
		return {
			noQtyParamNameList: ["Slider","Bobble Head","Spinner","Blinkie","Dangler","One Side 3D", "Two Sides 3D", "Edge Engraving", "Sequential Numbering", "Epoxy","3D","Keychains with Bottle Opener","Keychains with Cutout / Spinner","3 Dimensional"],
			noCommentParamNameList: ["Slider","Bobble Head","Spinner","Blinkie","Dangler","One Side 3D", "Two Sides 3D", "Epoxy","3D","Keychains with Bottle Opener","Keychains with Cutout / Spinner","3 Dimensional"],
			isMediaLoading: false,
			videoAspectRatio: 16 / 9,
		};
	},
	watch: {
		async zoomPic(val) {
			let getVideoMsg = (url) => {
				return new Promise((resolve) => {
					let videoElement = document.createElement("video");
					videoElement.src = url;
					videoElement.addEventListener("loadedmetadata", function () {
						resolve({
							duration: videoElement.duration,
							height: videoElement.videoHeight,
							width: videoElement.videoWidth,
						});
					});
				});
			};
			let getImgInfo = (url) => {
				return new Promise((resolve) => {
					let img = new Image();
					img.src = url;
					img.onload = function () {
						resolve({
							height: img.height,
							width: img.width,
						});
					};
				});
			};
            this.isMediaLoading = true;
			if (this.getFileSuffix(this.zoomPic) === ".mp4") {
				let videoInfo = await getVideoMsg(val);
				this.videoAspectRatio = videoInfo.width / videoInfo.height;
			} else {
				let msgInfo = await getImgInfo(val);
				this.videoAspectRatio = msgInfo.width / msgInfo.height;
			}
            this.isMediaLoading = false;
		},
	},
	computed: {
		device() {
			return this.$store.state.device;
		},
		lang() {
			return this.$store.getters.lang.quote || {};
		},
		videoOptions() {
			return {
				autoplay: true,
				controls: true,
				muted: false,
				loop: true,
				fill: true,
				sources: [
					{
						src: this.zoomPic,
						type: "video/mp4",
					},
				],
			};
		},
	},
};
</script>

<style lang="scss" scoped>
.customDialog ::v-deep .base-dialog-model-con {
	@include respond-to(mb){
		padding: 1.3333vw !important;
	}
}
.pic-wrap{
    .video-wrap{
        position: relative;
        overflow: hidden;
        height: 100%;
        border-top-right-radius: 6px;
        border-top-left-radius: 6px;
    }
    .load-box{
        display: flex;
        justify-content: center;
        align-items: center;
        position: absolute;
        inset: 0;
        z-index: 10;
    }
}
</style>
