<script>
export default {
	props: {
		productInfo: {
			type: Object,
			required: true,
		},
		hideBack: {
			type: Boolean,
			default: false,
		},
		showXin: {
			type: Boolean,
			default: true,
		}
	},
	computed: {
		langSemiCustom() {
			return this.$store.getters.lang?.semiCustom;
		},
	},
	methods: {
		goCollection(data) {
			this.$emit("goCollection", data);
		},
		back() {
			this.$emit("back");
		},
	},
};
</script>

<template>
	<div class="pdtInfo">
		<div class="pdtToolBar">
			<div class="pdt-toolBar-left">
				<div class="teaTitle">
					<h1>{{ productInfo.name }}</h1>
				</div>
			</div>
			<div class="add-favorite" @click="
				goCollection({
					isCollection: productInfo.isCollection,
					id: productInfo.id,
				})
				">
				<template v-if="!productInfo.isCollection">
					<b class="icon-shoucang"></b>
				</template>
				<template v-else>
					<b class="icon-xinxin isActive"></b>
				</template>
			</div>
		</div>
		<div class="pdtRightTitle">
			<h2>{{ langSemiCustom.item + ": " }}{{ productInfo.productSku }}</h2>
			<div class="xin" v-if="productInfo.commentLevel > 0 && showXin">
				<v-rating half-increments readonly size="18" :value="productInfo.commentLevel" background-color="#cccccc"
					color="#FFC300" dense length="5"></v-rating>
				<span class="xinCommentLevel" v-if="productInfo.commentLevel">{{ productInfo.commentLevel }}</span>
				<span class="xinCommentNum" v-if="productInfo.commentNum" style="color: rgb(182, 177, 177)">({{
					productInfo.commentNum }})</span>
			</div>
		</div>
		<div class="discountBox">
			<div class="discountPrice" v-show="productInfo.discount">
				<div class="oldPrice">
					<div v-if="$store.state.proTheme == '11' && productInfo.highestPrice == productInfo.lowestPrice">
						<span>{{ langSemiCustom.price }}:</span>
						<CCYRate :price="productInfo.lowestPrice"></CCYRate>
					</div>
					<div v-else>
						<span class="nomalText" :class="{ underLine: productInfo.discount != 0 }">{{ langSemiCustom.listPrices
							}}</span>
						<span :class="{ underLine: productInfo.discount != 0 }">
							<CCYRate :price="productInfo.lowestPrice"></CCYRate>
						</span>
						<span :class="{ underLine: productInfo.discount != 0 }" v-show="productInfo.highestPrice">{{
							langSemiCustom.to
						}}</span>
						<span :class="{ underLine: productInfo.discount != 0 }" v-show="productInfo.highestPrice">
							<CCYRate :price="productInfo.highestPrice"></CCYRate>
						</span>
					</div>
				</div>
				<div class="priceBtn">
					<div>
						<span>{{ langSemiCustom.save }}</span>
						{{ (productInfo.discount * 100).toFixed(0) + " %" }}
					</div>
				</div>
				<div class="nowPrice">
					<span>{{ langSemiCustom.onSale + ":" }}</span>
					<CCYRate :price="productInfo.lowestDiscountPrice"></CCYRate>
					<span v-show="productInfo.highestDiscountPrice">{{ langSemiCustom.to }}</span>
					<CCYRate v-show="productInfo.highestDiscountPrice" :price="productInfo.highestDiscountPrice"></CCYRate>
				</div>
			</div>
			<div class="priceCode" v-show="productInfo.category.productDiscountText && productInfo.discount">
				{{ productInfo.category.productDiscountText }}
			</div>
		</div>
	</div>
</template>

<style scoped lang="scss">
.pdtInfo {
	.pdtToolBar {
		display: flex;
		justify-content: space-between;
		align-items: center;

		h1 {
			font-weight: bold;
			font-size: 24px;
			color: #222222;
			line-height: 36px;
			word-break: break-word;

			@include respond-to(mb) {
				font-size: 15px;
				line-height: 18px;
			}
		}

		.pdt-toolBar-left {
			display: flex;
			align-items: center;

            @include respond-to(mb){
                flex-direction: column;
                justify-content: flex-start;
                align-items: flex-start;
            }

            .teaTitle{
                margin-right: 10px;
            }
		}

		.add-favorite {
			cursor: pointer;

			b {
				font-size: 24px;

				@include respond-to(mb) {
					font-size: 16px;
				}
			}

			b.isActive {
				color: $color-primary;
			}
		}
	}

	.pdtRightTitle {
		margin: 4px 0 4px;
		display: flex;
		align-items: center;

		h2 {
			font-weight: 400;
			font-size: 18px;
			color: $color-primary;
			line-height: initial;

			@include respond-to(mb) {
				font-size: 13px;
			}
		}

		.xin {
			display: flex;
			align-items: center;
			margin-left: 6px;
			column-gap: 4px;

			&>div {
				display: flex;
				align-items: center;
			}

			.xinCommentLevel,
			.xinCommentNum {
				font-size: 14px;

				@include respond-to(mb) {
					font-size: 12px;
				}
			}
		}
	}

	.discountBox {
		.discountPrice {
			display: flex;
			align-items: center;
			column-gap: 10px;

            @include respond-to(mb){
                display: grid;
                align-content: center;
                grid-template-columns: min-content 1fr; /* 两列 */
                grid-template-rows: auto auto;  /* 两行 */
                grid-template-areas:
                    "left right1"
                    "left right2";
            }

			.nowPrice {
				font-weight: bold;
				font-size: 14px;
				color: #f07b18;

				@include respond-to(mb) {
                    grid-area: right2;
				}

				span {
					font-weight: bold;
				}

				label {
					font-weight: bold;
				}
			}

			.priceBtn {
				flex-shrink: 0;
				height: 28px;
				background: linear-gradient(60deg, #ee4113 0%, #f3aa1e 100%);
				border-radius: 0px 10px 0px 10px;
				display: flex;
				align-items: center;
				justify-content: space-evenly;
				font-size: 14px;
				font-weight: bold;
				color: #fff;
				width: fit-content;
				column-gap: 8px;
				padding: 0 8px;

				@include respond-to(mb) {
                    border-radius: 0 8px 0 8px;
					font-size: 12px;
                    width: 40px;
                    padding: 4px;
                    text-align: center;
                    height: auto;
                    grid-area: left;
				}

				.circle {
					flex-shrink: 0;
					position: relative;
					width: 20px;
					height: 20px;
					border-radius: 50%;
					background-color: #ffffff;

					&::before {
						content: "%";
						font-size: 14px;
						position: absolute;
						top: 50%;
						left: 50%;
						transform: translate(-50%, -50%);
						color: $color-primary;
					}
				}
			}
		}

		.oldPrice {
			color: #636363;
			font-weight: 400;
			font-size: 14px;

			@include respond-to(mb) {
                grid-area: right1;
				font-size: 12px;
			}

			& .underLine {
				text-decoration: line-through;
			}

			.nomalText {
				&.underLine {
					text-decoration: none;
				}
			}
		}

		.priceCode {
			font-size: 14px;
			color: $color-primary;
		}
	}
}

[theme="11"] .pdtInfo {
	.pdt-toolBar-left {
		.teaTitle {
			display: flex;
			align-items: center;

			.teaXin {
				margin-left: 5px;

				::v-deep .v-rating .v-icon.v-icon {
					font-size: 18px !important;
				}
			}
		}

		.xin {
			cursor: auto !important;

			.xinCommentLevel,
			.xinCommentNum {
				display: none !important;
			}
		}
	}
}
</style>