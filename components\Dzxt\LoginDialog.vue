<template>
	<base-dialog :value="LoginDialog" @update="updateLoginDialog" :width="device !== 'mb' ? '510px' : '92%'">
		<div class="shareDialogCon">
			<div class="top">
				<span id="login" @click="blockornone('loginBox')">Sign In</span>
				<span id="register" @click="blockornone('registerBox')">Email Fast Sign In</span>
			</div>
			<div id="loginBox">
				<form action="#1">
					<span><span style="color: red">*</span>Email:</span>
					<input v-model="loginForm.email" type="email" placeholder="" id="input1" />
					<div id="isBlock" style="color: red; display: none">Please enter your email address</div>
					<span></span>

					<span><span style="color: red">*</span>Password:</span>
					<div style="position: relative">
						<input v-model="loginForm.password" :type="[flag ? 'text' : 'password']" placeholder="" id="password" required />
						<div id="isBlock1" style="color: red; display: none">Please enter your password</div>

						<b class="iconfont" :class="[flag ? 'icon-eye-fill' : 'icon-no_eye']" @click="showPwd"></b>
						<div class="forget">Forgot your &nbsp;<a href="/user/account/reset-password">password?</a></div>
					</div>
				</form>
			</div>
			<div id="registerBox">
				<form action="#2">
					<span><span style="color: red">*</span>Email:</span>
					<input v-model="loginForm.email" type="email" placeholder="" id="input2" />
					<div id="isBlock3" style="color: red; display: none">Please enter your email address</div>

					<span><span style="color: red">*</span>Email Verification:</span>
					<div class="codeWrap">
						<input v-model="loginForm.code" type="text" id="passwordR1" />
						<button id="searchButton" @click.prevent="getCode" :disabled="disabled" :plain="true">
							{{ btnText }}
						</button>
					</div>
				</form>
			</div>
			<div class="btn">
				<button class="btn1" @click.stop="SignIn(loginForm)">Sign in</button>
				<button class="btn2" @click.stop="userRegister">Join Free</button>
			</div>
		</div>
	</base-dialog>
</template>

<script>
import BaseDialog from "@/components/Quote/BaseDialog.vue";
import { login, cartMerge, memberCenterLogin } from "@/api/web.js";
import { sendResetPasswordCode, loginByPassword } from "@/api/loginOrRe";
export default {
	inject: ["LoginDialogFn", "updateLoginDialog"],
	data() {
		return {
			flag: false,
			loginForm: {
				email: "",
				password: "",
				code: "",
				email1: "",
			},
			show: true,
			btnText: "Get Code",
			disabled: false,
			sendTime: 0,
		};
	},
	computed: {
		LoginDialog() {
			return this.LoginDialogFn();
		},
		device() {
			return this.$store.state.device;
		},
	},
	components: {
		BaseDialog,
	},
	watch: {
		"loginForm.email"(val) {
			if (val) {
				document.getElementById("isBlock").style.display = "none";
				document.getElementById("isBlock3").style.display = "none";
			}
		},
		"loginForm.password"(val) {
			if (val) {
				document.getElementById("isBlock1").style.display = "none";
			}
		},
	},
	methods: {
		clear() {
			this.loginForm.email = "";
			this.loginForm.password = "";
			this.loginForm.code = "";
		},
		showPwd() {
			this.flag = !this.flag;
		},
		blockornone(id) {
			if (id == "registerBox") {
				document.getElementById(id).style.display = "block";
				document.getElementById("loginBox").style.display = "none";
				document.getElementById("register").style.background = " #ffffff";
				document.getElementById("login").style.background = "#f4f4f4";
			}
			if (id == "loginBox") {
				document.getElementById(id).style.display = "block";
				document.getElementById("registerBox").style.display = "none";
				document.getElementById("login").style.background = "#ffffff";
				document.getElementById("register").style.background = "#f4f4f4";
			}
		},
		SignIn(form) {
			let input1 = document.getElementById("input1");
			let isBlock = document.getElementById("isBlock");
			let isBlock1 = document.getElementById("isBlock1");
			let isPassword = document.getElementById("password");
			if (input1.value.trim() == "" && isPassword.value.trim() == "") {
				isBlock.style.display = "block";
				isBlock1.style.display = "block";
			} else {
				isBlock.style.display = "none";
				isBlock1.style.display = "none";
				if (form.email && form.password) {
					try {
						if (!form.email) return;
						login(form)
							.then(async (res) => {
								let data = res.data;
								await this.$store.commit("setUserInfo", data);
								this.$store.commit("setMask", false);
								await cartMerge({
									uuid: this.$store.state.userUUID,
								}); //合并购物车
								if (this.proType === 0) {
									await this.$nextTick();
									await memberCenterLogin();
									location.reload();
								}
								console.log(this.$store.state.userInfo, "kankankan ");
							})
							.then((res) => {
								this.$store.dispatch("updateHeadFootPages");
								this.updateLoginDialog();
							});
					} catch (error) {
						this.$toast.error(error.message);
					}
				} else {
					if (!form.code) return;
					loginByPassword(form.email, form.code)
						.then(async (res) => {
							let data = res.data;
							await this.$store.commit("setUserInfo", data);
							this.$store.commit("setMask", false);
							await cartMerge({
								uuid: this.$store.state.userUUID,
							}); //合并购物车
							if (this.proType === 0) {
								await this.$nextTick();
								await memberCenterLogin();
								location.reload();
							}
							// console.log(this.$store.state.userInfo, "1212121 ");
						})
						.then((res) => {
							this.$store.dispatch("updateHeadFootPages");
							this.updateLoginDialog();
						});
				}
			}
		},
		getCode() {
			let btn = document.getElementById("searchButton");
			let isBlock3 = document.getElementById("isBlock3");
			let input2 = document.getElementById("input2");

			if (input2.value.trim() == "") {
				isBlock3.style.display = "block";
			} else {
				isBlock3.style.display = "none";
				try {
					const { email } = this.loginForm;
					email &&
						sendResetPasswordCode(email).then((res) => {
							console.log(res, "121212121211212");

							btn.style.background = "#d9d9d9";
							btn.style.color = "#333333";
							this.disabled = true;
							this.btnText = "await...";
							// this.doLoop(60);
							this.sendTime = 60;
							let timer = setInterval(() => {
								this.btnText = this.sendTime + "s Get Code";
								this.sendTime -= 1;
								if (this.sendTime == 0) {
									this.btnText = "Get Code";
									this.disabled = false;
									btn.style.background = "#2a96fa";
									btn.style.color = "#ffffff";
									clearInterval(timer);
								}
							}, 1000);
						});
				} catch (error) {
					if (error) {
						this.$toast.error(error.message);
					}
				}
			}
		},
		// doLoop(seconds) {
		// 	let btn = document.getElementById("searchButton");
		// 	this.btnText = seconds + "s Get Code";
		// 	let countdown = setInterval(() => {
		// 		if (seconds > 0) {
		// 			this.btnText = seconds + "s Get Code";
		// 			seconds--;
		// 		} else if (seconds == 0) {
		// 			this.btnText = "Get Code";
		// 			this.disabled = false;
		// 			btn.style.background = "#2a96fa";
		// 			btn.style.color = "#ffffff";
		// 			clearInterval(countdown);
		// 		}
		// 	}, 1000);
		// },

		async userRegister() {
			window.location.href = "/user/account/register";
		},
	},
};
</script>

<style scoped lang="scss">
.shareDialogCon {
	padding-bottom: 31px;

	.top {
		display: flex;
		align-items: flex-end;
		width: 100%;
		height: 57px;

		background: #f4f4f4;
		font-size: 18px;
		font-weight: 400;
		color: #333333;
		line-height: 42px;

		#login {
			width: 245px;
			height: 42px;
			background: #ffffff;
			margin-left: 10px;
			border-radius: 10px 10px 0px 0px;
			text-align: center;
			cursor: pointer;
		}

		#register {
			width: 245px;
			height: 42px;
			// background: #ffffff;
			margin-right: 10px;
			border-radius: 10px 10px 0px 0px;
			text-align: center;
			cursor: pointer;
		}
	}

	#loginBox {
		padding: 0 20px;
		transition-property: background;
		transition-duration: 3s;
		transition-timing-function: ease-in;

		span {
			margin-top: 11px;
			line-height: 45px;
			font-size: 18px;
			font-weight: 400;
			color: #333333;
		}

		input {
			// margin-top: 11px;
			height: 40px;
			width: 100%;
			background: #f7f9fa;
			border: 1px solid #d9d9d9;
			border-radius: 4px;
			text-indent: 3%;
		}

		b {
			font-size: 18px;
			font-weight: 400;
			color: #808080;
			position: absolute;
			top: 10px;
			right: 12px;
		}

		.forget {
			margin-top: 10px;
			font-size: 16px;
			font-weight: 400;
			color: #333333;
			text-align: right;

			a {
				color: #2a96fa;
				text-decoration: underline;
			}
		}
	}

	#registerBox {
		padding: 0 20px;
		display: none;

		span {
			margin-top: 11px;
			line-height: 45px;
			font-size: 18px;
			font-weight: 400;
			color: #333333;
		}

		input {
			// margin-top: 11px;
			height: 40px;
			width: 100%;
			background: #f7f9fa;
			border: 1px solid #d9d9d9;
			border-radius: 4px;
			letter-spacing: 2px;
			text-indent: 3%;
		}

		b {
			font-size: 18px;
			font-weight: 400;
			color: #808080;
			position: absolute;
			top: 10px;
			right: 12px;
		}

		.codeWrap {
			display: flex;
			#passwordR1 {
				flex: 1;
				height: 40px;
				background: #f7f9fa;
				border: 1px solid #d9d9d9;
				border-radius: 4px;
			}
			button {
				width: 97px;
				height: 40px;
				background: #2a96fa;
				border-radius: 4px;
				font-size: 16px;
				font-weight: 400;
				color: #ffffff;
				margin-left: 6px;
			}
		}
	}

	.btn {
		display: flex;
		justify-content: center;
		width: 100%;
		text-align: center;
		font-size: 18px;
		margin-top: 44px;

		.btn1 {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 158px;
			height: 40px;
			background: #2a96fa;
			font-weight: 400;
			color: #ffffff;
			border-radius: 10px;
			border: 1px solid #ffffff;
			margin-right: 10px;
		}

		.btn2 {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 158px;
			height: 40px;
			font-weight: 400;
			color: #333333;
			border-radius: 10px;
			border: 1px solid #d9d9d9;
		}
	}

	.title {
		font-size: 16px;
		font-weight: 400;
		color: #333333;
		display: flex;
		justify-content: space-around;
		align-items: center;
		margin-top: 24px;

		.left {
			width: 100px;
			height: 1px;
			background: #e6e6e6;
		}

		.right {
			width: 100px;
			height: 1px;
			background: #e6e6e6;
		}
	}

	.log {
		display: flex;
		flex-direction: column;
		align-items: center;
		margin-top: 21px;

		.Facebook {
			margin-bottom: 10px;

			a {
				display: flex;
				justify-content: center;
				align-items: center;
				font-size: 18px;
				font-weight: 400;
				color: #ffffff;
				text-decoration: none;

				background: #2a5b97;
				width: 326px;
				height: 40px;
				border-radius: 10px;

				b {
					font-size: 23px;
					margin-right: 3px;
				}
			}
		}

		.Google {
			a {
				display: flex;
				justify-content: center;
				align-items: center;
				font-size: 18px;
				font-weight: 400;
				color: #ffffff;
				text-decoration: none;
				background: #de3500;
				width: 326px;
				height: 40px;
				border-radius: 10px;

				b {
					font-size: 23px;
					margin-right: 8px;
				}
			}
		}
	}
}

@media screen and(max-width:768px) {
	.shareDialogCon {
		font-size: 14px;
		.top {
			font-size: 16px;
		}
		#loginBox {
			span {
				font-size: 14px;
			}
			.forget {
				font-size: 14px;
			}
		}
		#registerBox {
			span {
				font-size: 14px;
			}
		}
		.btn {
			margin-top: 20px;
			font-size: 14px;

			.btn1 {
				width: 94px;
				border-radius: 5px;
			}

			.btn2 {
				width: 94px;
				border-radius: 5px;
			}
		}
	}
}
</style>
