<template>
  <div class="pdt-sub" :class="{ showSub: subShow, isCufflinks: isCufflinks }">
    <div class="pdt-sub-item pdt-sub-qty">
      <span>{{ langSemiCustom.Quantity }}</span>
      <span class="blackFont"
        >{{ priceInfo.totalQuantity || 0 }} {{ productInfo.category.unit }}</span
      >
    </div>
    <div class="pdt-sub-item pdt-sub-unit">
      <span>{{ langSemiCustom.unitCost }}</span>
      <span class="blackFont">
        <CCYRate :price="priceInfo.foundationUnitPrice"></CCYRate>
        <span
          v-show="productInfo.discount != 0 && priceInfo.totalQuantity > 0"
          style="color: #c3c1c1"
        >
          (<span style="text-decoration: line-through; font-size: calc(1em - 2px)">
            <CCYRate :price="getNowPrice"></CCYRate> </span
          >)
        </span>
      </span>
    </div>
    <div class="pdt-sub-item pdt-sub-discount">
      <span>{{ text1 }}</span>
      <span class="blackFont"
        >{{ text2 }}
        <CCYRate :price="Math.abs(priceInfo.deliveryFee)"> </CCYRate>
      </span>
    </div>
    <div class="pdt-sub-item pdt-sub-setupCharge">
      <span>{{ langSemiCustom.SetupCharge }}</span>
      <span class="blackFont">
        <CCYRate :price="priceInfo.toolingCharge"></CCYRate>
      </span>
    </div>
    <div class="pdt-sub-item pdt-sub-total">
      <span
        v-if="countryName != 'United States' && countryName != 'Australia'"
        class="totalPriceText"
      >
        {{ IncludingVAT == "yes" ? "Subtotal incl. VAT" : "Subtotal excl. VAT" }}:
      </span>
      <span v-else>{{ langSemiCustom.SubTotal }}</span>
      <span class="blackFont">
        <CCYRate :price="totalPriceFromSubtotal"></CCYRate>
      </span>
    </div>
    <div class="pdt-sub-item pdt-sub-cart" :class="{ luggagetags: isLuggagetags }">
      <div v-if="isFd" @click.stop="addInquiry">
        <span class="normalBox">{{ langQuote.submitInquiry }}</span>
      </div>
      <div v-else-if="selectOne == 2">
        <span class="normalBox" @click="addCart">{{ langCart.checkOut }}</span>
      </div>
      <div v-else class="addCartBtnBox" :class="{ luggagetags: isLuggagetags }">
        <span class="normalBox" @click="addCart" v-if="$route.query.orderNow == 1">{{
          langSemiCustom.orderNow
        }}</span>
        <div
          class="buyNow item"
          @click="addCart('checkOut')"
          v-if="$route.query.orderNow != 1 && !isCufflinks"
        >
          {{ langSemiCustom.buyNow }}
        </div>
        <div
          class="buyNow item"
          @click="addInquiry"
          v-if="$route.query.orderNow != 1 && isCufflinks"
        >
          {{ langSemiCustom.SubmitInquiry
          }}<ToolTip
            class="uploadTip2"
            :titleContent="langQuote.inquiryTip"
            v-show="isCufflinks"
          ></ToolTip>
        </div>
        <div
          class="addToCart item"
          @click="addCart('addAndNoCheckOut')"
          v-if="$route.query.orderNow != 1"
        >
          {{ langSemiCustom.addToCart2
          }}<ToolTip
            class="uploadTip2"
            :titleContent="langQuote.addCartTip"
            v-show="isCufflinks"
          ></ToolTip>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ToolTip from "@/components/HalfDesign/customMedals/common/ToolTip";
export default {
  components: {
    ToolTip,
  },
  props: {
    subShow: {
      type: Boolean,
    },
    priceInfo: {
      type: Object,
    },
    productInfo: {
      type: Object,
    },
    isLuggagetags: {
      type: Boolean,
    },
    isCufflinks: {
      type: Boolean,
    },
    selectOne: {
      type: [Number, String],
    },
    discountPriceArr: {
      type: Array,
    },
    totalPriceFromSubtotal: {
      type: [Number, String],
    },
    IncludingVAT: {
      type: String,
    },
    countryName: {
      type: String,
    },
  },
  data() {
    return {
      showDiscountPrice: "",
    };
  },
  computed: {
    langSemiCustom() {
      return this.$store.getters.lang?.semiCustom;
    },
    langQuote() {
      return this.$store.getters.lang.quote || {};
    },
    langCart() {
      return this.$store.getters.lang?.cart;
    },
    text1() {
      if (this.priceInfo.discount < 1) {
        return this.langQuote.discount;
      } else if (this.priceInfo.discount == 1) {
        return this.langSemiCustom.DeliveryFee;
      }else {
        return this.langSemiCustom.RushDelivery;
      }
    },
	text2() {
		if (this.priceInfo.discount < 1) {
			return "-";
		} else if (this.priceInfo.discount == 1) {
			return "";
		} else {
			return "+";
		}
	},
    deliveryFee() {
      let totalPriceInt = Math.round(this.priceInfo.totalPrice * 100);
      let foundationUnitPriceInt = Math.round(this.priceInfo.foundationUnitPrice * 100);
      let toolingChargeInt = Math.round(this.priceInfo.toolingCharge * 100);
      let intermediateResult =
        totalPriceInt -
        foundationUnitPriceInt * this.priceInfo.totalQuantity -
        toolingChargeInt;
      return Math.abs(intermediateResult / 100).toFixed(2);
    },
    getNowPrice() {
      if (this.priceInfo.totalQuantity > 0) {
        return this.getPrice(this.discountPriceArr);
      }
    },
    proId() {
      return this.$store.state.proId;
    },
    isFd() {
      return this.proId == 9;
    },
  },
  methods: {
    addCart(type) {
      this.$emit("addCart", type);
    },
    addInquiry() {
      this.$emit("openInquiryBox");
    },
    getPrice(arr) {
      let len = arr.length;
      if (!len) {
        return false;
      }
      let inputNum = parseInt(this.priceInfo.totalQuantity);
      if (inputNum > 0 && inputNum <= parseInt(arr[0].quantity)) {
        this.showDiscountPrice = parseFloat(arr[0].unitPrice);
        return this.showDiscountPrice;
      }
      if (inputNum >= parseInt(arr[len - 1].quantity)) {
        this.showDiscountPrice = parseFloat(arr[len - 1].unitPrice);
        return this.showDiscountPrice;
      }
      for (let i = 0; i < len; i++) {
        let num = arr[i];
        let num2 = arr[i + 1];
        if (!num2) break;
        if (inputNum >= parseInt(num.quantity) && inputNum < parseInt(num2.quantity)) {
          this.showDiscountPrice = parseFloat(num.unitPrice);
          break;
        }
      }
      return this.showDiscountPrice;
    },
  },
};
</script>

<style scoped lang="scss">
.luggagetags {
//   background: linear-gradient(90deg, #f8430d, #fea33a) !important;
}

.pdt-sub {
  position: fixed;
  bottom: -100%;
  left: 0;
  right: 0;
  display: grid;
  grid-template-columns: repeat(6, minmax(200px, 300px));
  justify-content: center;
  color: #000000;
  padding: 10px 0;
  background: #ffffff;
  box-shadow: 0 -1px 10px 1px #f5f5f5;
  border-top: 1px solid #f6f6f6;
  transition: all 0.3s;
  z-index: 100;

  &.showSub {
    bottom: 0;
  }
  &.isCufflinks {
    grid-template-columns: repeat(5, minmax(200px, 300px)) 360px;
  }

  .pdt-sub-item {
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 0 5px;

    .blackFont {
      color: #000000;
    }

    &:last-child {
      &::after {
        display: none;
      }
    }

    &:nth-of-type(5) {
      &::after {
        display: none;
      }
    }

    &::after {
      content: "";
      position: absolute;
      top: 50%;
      right: 0;
      transform: translateY(-50%);
      width: 1px;
      height: 18px;
      background-color: #cccccc;
    }

    span:first-child {
      margin-bottom: 5px;
      font-weight: 700;
      text-transform: uppercase;
    }
  }

  .pdt-sub-discount {
    span:last-child {
      color: #e6252e;
    }
  }

  .pdt-sub-total {
    span:last-child {
      font-weight: bold;
      font-size: 1.125em;
    }
  }

  .pdt-sub-cart {
    padding: 0;
    width: 100%;
    cursor: pointer;
	&.luggagetags{
		.normalBox{
			background: linear-gradient(90deg, #f8430d, #fea33a) !important;
		}
	 }
    .normalBox {
      border-radius: 4px;
      font-weight: bold;
      color: #ffffff;
      padding: 10px 15px;
      background-color: $color-primary;
    }
    ::v-deep .v-icon {
      color: $color-primary;
    }

    .addCartBtnBox {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      height: 3em;
	  &.luggagetags{
		.addToCart{
			background: linear-gradient(90deg, #f8430d, #fea33a) !important;
		}
	  }
      .item {
        width: 50%;
        height: 100%;
        font-weight: bold;
        font-size: 1em;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
      }
      .buyNow::v-deep {
        border-radius: 6px 0 0 6px;
        color: $color-primary;
        border: 1px solid $color-primary;
        word-break: break-word;
		background: #fff;
        .uploadTip2 {
          i {
            font-weight: 400;
            color: $color-primary !important;
          }
        }
      }
      .addToCart::v-deep {
        border-radius: 0 6px 6px 0;
        color: #fff;
        background: linear-gradient(90deg, $color-primary, var(--color-primary-opacity));
        .uploadTip2 {
          i {
            font-weight: 400;
            color: #fff !important;
          }
        }
      }
    }
  }
}

[theme="11"] .pdt-sub {
  grid-template-columns: repeat(5, minmax(200px, 300px)) !important;

  .pdt-sub-setupCharge {
    display: none;
  }
}
</style>
