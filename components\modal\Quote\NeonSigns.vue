<template>
    <div class="neon-box" id="quote" noDebounce>
        <template v-if="isManage">
            <v-card height="300">
                <v-row justify="center">
                    <v-overlay :absolute="true" :value="true">
                        <v-chip> {{ lang.neon.neonQuote }} </v-chip>
                    </v-overlay>
                </v-row>
            </v-card>
        </template>
        <template v-else>
            <NeonSigns pageQuote :customTitle="data.customTitle" :class="{titleCenter:data.isTitleCenter}"></NeonSigns>
        </template>
    </div>
</template>
<script>
import NeonSigns from "@/components/Neon/NeonSigns.vue";
export default {
    name: "NeonSignsQuote",
    props: {
        data: {
            type: Object,
            default: {},
        },
    },
    components: {
        NeonSigns,
    },
    data() {
        return {
            modal: {
                style: {},
                ...this.data,
            },
            isManage: false,
        };
    },
    computed: {
        lang() {
            return this.$store.getters.lang.quote || {};
        },
    },
    watch: {
        modal: {
            handler(val) {
                if (process.env.isManage) this.$emit("update:data", val);
            },
            immediate: true,
            deep: true,
        },
    },
    created() {
        if(!this.data?.customTitle){
            this.data["customTitle"]=this.lang.neon.customTitle
        }
        if (process.env.isManage) {
            this.isManage = true;
        }
    },
};
</script>

<style lang="scss" scoped>
.neon-box ::v-deep #neon {
    padding-top: 1em;
}
::v-deep .titleCenter{
    .content{
        #neonTop{
            padding-left: 45%!important;
        }
    }
}
</style>