export default function ({ store }, inject) {

	const setModal = (that, target, targetArray, clickType, event, other) => {
		if (process.env.isManage) {
			if (target && targetArray && clickType) {
				// that.modal.type.customData = target;
				// that.modal.type.customDataParent = targetArray;
				// that.modal.type.clickType = clickType;
				that.$set(that.modal.type, "customData", target);
				that.$set(that.modal.type, "customDataParent", targetArray);
				that.$set(that.modal.type, "clickType", clickType);
			}
			if (other && typeof (other) == 'number') that.modal.type.index = other;
			if (event && typeof (event.url) == 'number') that.isViewMore = !that.isViewMore;
		}
		else if (event?.copy) {
			const textarea = document.createElement('textarea');
			textarea.value = event.copy;
			document.body.appendChild(textarea);
			textarea.select();
			document.execCommand('copy');
			document.body.removeChild(textarea);
			that.$toast.success("Code copied!")
		}
		else if (event && event.type == 'showTipImg') {
			document.querySelector('.tips').style.display = 'flex';
			document.querySelector('.float-btn').style.display = 'none';
		}
		else if (event && clickType == 'video') {
			if (!that.modal.manualPlay && !that.modal.mousePlay) return

			document.querySelectorAll(`#${that.modal.id} video`).forEach(video => {
				video.addEventListener("click", event => event.preventDefault());
				if (video.src == target.value) {
					if (((that.modal.manualPlay && event.type == 'click') || (that.modal.mousePlay && event.type == 'enter')) && video.paused) {
						video.currentTime = target.lastPalyTime || 0;
						video.play();
						if (video.nextElementSibling && video.nextElementSibling._prevClass == 'icon-jxsht-3d-dbf') video.nextElementSibling.style.color = 'transparent';
					} else if (((that.modal.manualPlay && event.type == 'click') || (that.modal.mousePlay && event.type == 'leave')) && !video.paused) {
						target.lastPalyTime = video.currentTime;
						video.currentTime = 0;
						video.pause();
						if (video.nextElementSibling && video.nextElementSibling._prevClass == 'icon-jxsht-3d-dbf') video.nextElementSibling.style.color = '';
					} else if (event.type == 'zoom') {
						target.lastPalyTime = video.currentTime;
						video.currentTime = 0;
						video.pause();
						if (video.nextElementSibling && video.nextElementSibling._prevClass == 'icon-jxsht-3d-dbf') video.nextElementSibling.style.color = '';
						store.commit('setMask', { ...target, video: true });
					}
				}
			})
		}
		// type 默认undefined，所以用 != 来判断
		else if (event && (event.type == 'click' || (event.type != 'link' && (event.method && Object.keys(event.method).length)))) {
			if (clickType === 'img') store.commit('setMask', { ...event, ...event.method });
			else if (event.method.copy) {
				const textarea = document.createElement('textarea');
				textarea.value = event.value;
				document.body.appendChild(textarea);
				textarea.select();
				document.execCommand('copy');
				document.body.removeChild(textarea);
				that.$toast.success("Code copied!")
			}
			else if (event.method == 'login') store.commit('setLogin', event.method);
			else store.commit('setMask', event.method);
		}
		else if (event && (event.type == 'link' || (event.type != 'click' && event.url))) {
			if (clickType == 'img_list' && other) event = { ...targetArray[other].button, ...event };
			if (!event.url) return;
			if (typeof (event.url) == 'number') that.isViewMore = !that.isViewMore;
			else if (event.url.startsWith('#')) document.querySelector(event.url)?.scrollIntoView({ block: event.scrollY });
			else if (event.target == '_blank') window.open((event.url.startsWith('http') ? '' : store.state.language.lang) + event.url);
			else if (event.url.startsWith('http') || event.url.startsWith('tel') || event.url.startsWith('mailto')) location.href = event.url;
			else that.$router.push({ path: store.state.language.lang + event.url })
		}

		try {
			if (event && store.state.advertConfig.googleConfig && window.gtag) {
				if (event.url || (typeof (event.method) == "object" && event.method.modal && event.method.modal.startsWith('modalQuote'))) gtag("event", "select_content", {
					content_id: that.modal.id,
					content_type: clickType,
					content_value: event.value,
					content_alt: event.alt,
					name: event.url ? event.url.replace(/-|\//g, ' ') : event.alt,
					...event.method
				})
			};
		} catch (e) { }
	}
	inject('setModal', setModal)



	// 补全连接
	const rectifyLink = (name) => {
		if (!name) return false;

		// 匹配完整链接的正则表达式
		const regex = /^(http|https):\/\/[\w\-]+(\.[\w\-]+)+([\w\-\.,@?^=%&:/~\+#]*)?$/;
		//判断是否是全链接
		if (regex.test(name)) return name;

		//判断路径是否以/开头
		const lang = store.state.language.lang;
		if (name.startsWith('/')) {
			let newName = name.substr(1)
			return `${lang}/${newName}`
		}

		return `${lang}/${name}`;
	}
	inject('rectifyLink', rectifyLink)
}
