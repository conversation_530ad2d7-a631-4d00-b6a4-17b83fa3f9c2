<template>
  <div class="mb-4" :class="stepData.styleName">
    <slot name="stepText"></slot>
    <div class="step-content">
      <div
        class="step-item"
        :class="{
          active: index === selectIndex,
          onePens: noDisdountSelect && step.unitPercent != 0,
        }"
        v-for="(step, index) in filterStepItem"
        :key="index"
        @click="selectStep(step, index)"
      >
        <circleBox :active="index === selectIndex"></circleBox>
        <div class="name">
          <div class="text-truncate">
            <span>{{ step.valueName }}</span>
            <span v-if="step.priceType == 6 && step.weightPrice">
              <span>- {{ langSemiCustom.add }}</span
              ><CCYRate :price="step.weightPrice"></CCYRate>
            </span>
          </div>
          <div class="tip">
            {{ step.remark }}
          </div>
        </div>
      </div>
    </div>

    <div class="errorTip">
      <v-alert dense outlined type="error">
        {{ langSemiCustom.errorTip }}
      </v-alert>
    </div>
  </div>
</template>
<script>
import circleBox from "@/components/HalfDesign/common/circleBox";
import { halfDetailDiscount } from "@/mixins/halfDetailDiscount";
import { halfCalculate } from "@/api/web";
export default {
  inject: ["getProductInfo"],
  mixins: [halfDetailDiscount],
  props: {
    stepData: {
      type: Object,
      default: {},
    },
  },
  components: {
    circleBox,
  },
  data() {
    return {
      selectIndex: -1,
      selectItem: null,
      inputNum: "",
      stock: 9999,
      teaSize: {},
    };
  },
  watch: {},
  computed: {
    langSemiCustom() {
      return this.$store.getters.lang?.semiCustom;
    },
    productInfo() {
      return this.getProductInfo();
    },
    lang() {
      return this.$store.getters.lang.quote || {};
    },
  },
  methods: {
    selectStep(item, index) {
      this.selectIndex = index;
      this.selectItem = item;
      this.$emit("selectStep", {
        type: this.stepData.attributeFlag,
        data: item,
        parent: this.stepData,
        id: this.stepData.id,
      });
    },
    getPlaceholder() {
      if (this.stepData.styleName === "style1") {
        if (this.stock > 0) {
          return `${this.stock} in stock`;
        }
        if (this.stock == -1) {
          return;
        } else {
          return "Sold Out";
        }
      }
    },
    selectDefault() {
      if (this.filterStepItem.length) {
        this.selectStep(this.filterStepItem[0], 0);
      }
    },
    async getPriceFn(data) {
      let { data: result } = await halfCalculate(data);
      return result.deliveryFee;
    },
  },
  mounted() {
    this.$Bus.$on("selectDefaultDiscountStep", this.selectDefault);
  },
  beforeDestroy() {
    this.$Bus.$off("selectDefaultDiscountStep", this.selectDefault);
  },
};
</script>
<style scoped lang="scss">
@import "~assets/css/half.scss";

.step-content {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;

  .step-item {
    min-width: 0;
    @include step-default;
    cursor: pointer;
    display: flex;
    background: #fafafa;

    &.onePens {
      pointer-events: none;
      background: #f5f5f5;
    }

    @media (any-hover: hover) {
      &:hover {
        border-color: $color-primary;
        color: #ffffff;
      }
    }

    .name {
      min-width: 0;
      word-break: break-word;

      .text-truncate {
        font-weight: 700;
        margin-bottom: 6px;
        white-space: normal !important;
        line-height: normal;
      }

      .tip {
        font-size: 13px;
        color: #808080;
        line-height: initial;
      }
    }
  }

  .step-item.active {
    border-color: $color-primary;
  }
}

.style1 .step-content,
.style2 .step-content {
  grid-template-columns: repeat(2, 1fr);

  .step-item {
    align-items: flex-start;
    gap: 8px;
    border-color: #d3d5d7;

    .circleBox {
      margin-top: 2px;
    }

    @media (any-hover: hover) {
      &:hover {
        border-color: $color-primary;
        color: #333;
      }
    }

    &.active {
      border-color: $color-primary;
      color: #333;
    }
  }
}

.style2 .step-content {
  .step-item {
    background-color: #ffffff;
  }
}

@include respond-to(mb) {
  .step-content {
    gap: 5px;

    .step-item {
      .name {
        .text-truncate {
          margin-bottom: 4px;
          white-space: normal !important;
          font-size: 14px;
        }
      }

      .tip {
        font-size: 12px;
      }
    }
  }
}
</style>
