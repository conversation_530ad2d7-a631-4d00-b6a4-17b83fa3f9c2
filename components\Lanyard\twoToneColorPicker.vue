<template>
	<div class="colorPicker">
		<div class="chooseYourLanyardColors position-relative">
			<el-tabs v-model="tabsName" class="tabs" @tab-click="changeTabs">
				<el-tab-pane :label="lang.POPULAR" name="POPULAR">
					<ul class="box custom-scrollbar">
						<li class="item custom-shadow2 hover-type" :class="{ active: item.quantity && item.quantity > 0 }" v-for="item in bindValue.childList" :key="item.id">
							<div class="p1">
								<el-tooltip popper-class="hoverImage" :enterable="true" effect="light" :placement="item.left > 200 ? 'left' : 'right'" trigger="hover" :disabled="JSON.parse(item.imageJson)[2] ? false : true">
									<template #content>
										<div
											:style="{
												backgroundImage: JSON.parse(item.imageJson)[2] ? 'url(' + JSON.parse(item.imageJson)[2].url + ')' : '',
											}"
										></div>
									</template>
									<div
										@mouseover="getLeftFun($event, item)"
										:style="{
											backgroundImage: 'url(' + JSON.parse(item.imageJson)[0].url + ')',
										}"
									></div>
								</el-tooltip>
							</div>
							<div class="p2">
								<div>
									{{ item.alias }}
								</div>
								<div>
									{{ item.alias2 }}
								</div>
							</div>
							<div class="p3">
								<input :controls="false" class="myInput" onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode)))" type="number" v-model.number="item.quantity" placeholder="QTY" @change="popularInputChange(item, 'Lanyard Popular Colors')" />
							</div>
						</li>
						<li class="item custom-shadow2 hover-type" :class="{ active: item.quantity && item.quantity > 0 }" v-for="item in bindValueDesign.childList" :key="item.id">
							<div class="p1">
								<el-tooltip popper-class="hoverImage" :enterable="false" effect="light" :placement="item.left > 200 ? 'left' : 'right'" trigger="hover" :disabled="JSON.parse(item.imageJson)[2] ? false : true">
									<template #content>
										<div
											:style="{
												backgroundImage: JSON.parse(item.imageJson)[2] ? 'url(' + JSON.parse(item.imageJson)[2].url + ')' : '',
											}"
										></div>
									</template>
									<div
										@mouseover="getLeftFun($event, item)"
										:style="{
											backgroundImage: 'url(' + JSON.parse(item.imageJson)[0].url + ')',
										}"
									></div>
								</el-tooltip>
							</div>
							<div class="p2">
								{{ item.alias }}
							</div>
							<div class="p3">
								<input :controls="false" class="myInput" onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode)))" type="number" v-model.number="item.quantity" placeholder="QTY" @change="popularInputChange(item, 'Lanyard Design Colors')" />
							</div>
						</li>
					</ul>
				</el-tab-pane>
				<el-tab-pane :label="lang.CUSTOM" name="CUSTOM">
					<div class="customColor">
						<div class="colorItemWrap" v-for="(colorItem, colorIndex) in customColorList" :key="colorItem.id">
							<div class="colorItem">
								<div class="box1">
									<div class="mainColor">
										<span>{{ lang.mainColor }}:</span>
										<div>
											<el-popover placement="bottom" width="300" trigger="click">
												<div class="colorSelect" slot="reference">
													<div class="t">
														<span class="currentColor" :style="{ backgroundColor: colorItem.mainColor.code }"></span>
														<span class="name">{{ colorItem.mainColor.pantone || "Select Color" }}</span>
													</div>
													<b class="icon-xialasanjiaojiantou"></b>
												</div>
												<div class="colorExtend">
													<div class="search">
														<el-input v-model="keyWord" clearable @input="debounceFindAll" size="small" placeholder="Search Color" prefix-icon="el-icon-search" />
													</div>
													<div class="colorWrap custom-scrollbar">
                                                        <div class="color" :class="{ active: item.id === colorItem.mainColor.id }" :title="item.pantone" v-for="item in allColor" :key="item.id" :style="{ backgroundColor: item.code }" @click="selectColor(colorItem, item, 'mainColor')">
                                                            <b class="icon-cc-yes-crude"></b>
                                                        </div>
                                                    </div>
												</div>
											</el-popover>
										</div>
									</div>
									<div class="accentColor">
										<span>{{ lang.accentColor }}:</span>
										<div>
											<el-popover placement="bottom" width="300" trigger="click">
												<div class="colorSelect" slot="reference">
													<div class="t">
														<span class="currentColor" :style="{ backgroundColor: colorItem.accentColor.code }"></span>
														<span class="name">{{ colorItem.accentColor.pantone || "Select Color" }}</span>
													</div>
													<b class="icon-xialasanjiaojiantou"></b>
												</div>
												<div class="colorExtend">
                                                    <div class="search">
                                                        <el-input v-model="keyWord" clearable @input="debounceFindAll" size="small" placeholder="Search Color" prefix-icon="el-icon-search" />
                                                    </div>
													<div class="colorWrap custom-scrollbar">
                                                        <div class="color" :class="{ active: item.id === colorItem.accentColor.id }" :title="item.pantone" v-for="item in allColor" :key="item.id" :style="{ backgroundColor: item.code }" @click="selectColor(colorItem, item, 'accentColor')">
                                                            <b class="icon-cc-yes-crude"></b>
                                                        </div>
                                                    </div>
												</div>
											</el-popover>
										</div>
									</div>
								</div>
								<div class="box2">
									<div class="selectColor">
										<div class="mainColor" :style="{ backgroundColor: colorItem.mainColor.code }"></div>
										<div class="accentColor" :style="{ backgroundColor: colorItem.accentColor.code }"></div>
									</div>
									<div class="inputNum">
										<span>{{ colorItem.mainColor.pantone || lang.mainColor }}</span>
										<span>{{ colorItem.accentColor.pantone || lang.accentColor }}</span>
										<input type="text" :placeholder="lang.qty" :disabled="disabledInput(colorItem)" oninput="value=value.replace(/[^0-9]/g,'')" v-model="colorItem.quantity" @change="customInputChange(colorItem)" />
									</div>
								</div>
								<div style="display: flex; align-items: center" v-if="colorIndex === 0" @click="addColor"><b class="icon-a-icon-dzlj-addzhuanhuan"></b> {{ lang.add }}</div>
								<div style="display: flex; align-items: center" v-else @click="delColor(colorIndex, colorItem)"><b class="icon-a-icon-dzlj-delzhuanhuan"></b> {{ lang.del }}</div>
							</div>
							<el-alert style="margin-top: 10px" :title="lang.colorTip" type="error" v-if="colorItem.mainColor.id && colorItem.accentColor.id && colorItem.mainColor.id === colorItem.accentColor.id"></el-alert>
						</div>
					</div>
				</el-tab-pane>
				<el-tab-pane :label="lang.DESIGN" name="DESIGN" v-if="!noDesignTab">
					<ul class="box custom-scrollbar" v-if="bindValueDesign">
						<li class="item custom-shadow2 hover-type" :class="{ active: item.quantity && item.quantity > 0 }" v-for="item in bindValueDesign.childList" :key="item.id">
							<div class="p1">
								<el-tooltip popper-class="hoverImage" :enterable="false" effect="light" :placement="item.left > 200 ? 'left' : 'right'" trigger="hover" :disabled="JSON.parse(item.imageJson)[2] ? false : true">
									<template #content>
										<div
											:style="{
												backgroundImage: JSON.parse(item.imageJson)[2] ? 'url(' + JSON.parse(item.imageJson)[2].url + ')' : '',
											}"
										></div>
									</template>
									<div
										@mouseover="getLeftFun($event, item)"
										:style="{
											backgroundImage: 'url(' + JSON.parse(item.imageJson)[0].url + ')',
										}"
									></div>
								</el-tooltip>
							</div>
							<div class="p2">
								{{ item.alias }}
							</div>
							<div class="p3">
								<input :controls="false" class="myInput" onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode)))" type="number" v-model.number="item.quantity" placeholder="QTY" @change="popularInputChange(item, 'Lanyard Design Colors')" />
							</div>
						</li>
					</ul>
				</el-tab-pane>
			</el-tabs>
		</div>
	</div>
</template>
<script>
import { indexApi } from "@/api/lanyardQuote/index";
import {debounce, deepClone, generateUUID} from "@/utils/utils";

export default {
	props: {
		bindValue: {
			type: Object,
			default: {},
		},
		bindValueDesign: {
			type: Object,
			default: null,
		},
		selectedData: {
			type: Object,
		},
		noDesignTab: {
			type: Boolean,
		},
	},
	data() {
		return {
            debounceFindAll: null,
			keyWord: "",
			customColorList: [
				{
					id: generateUUID(),
					isTwoToneCustom: true,
					mainColor: {},
					accentColor: {},
					quantity: "",
				},
			],
			allColor: [],
			tabsName: "POPULAR",
			currentCustomCardItem: {},
			currentPopularCardItem: {},
		};
	},
	methods: {
		disabledInput(colorItem) {
			if (colorItem.mainColor.id && colorItem.accentColor.id && colorItem.mainColor.id !== colorItem.accentColor.id) {
				return false;
			}
			return true;
		},
		selectColor(colorItem, item, type) {
			colorItem[type] = deepClone(item);
			if (colorItem.mainColor.id && colorItem.accentColor.id && colorItem.mainColor.id === colorItem.accentColor.id) {
				colorItem.quantity = "";
			}
			if (colorItem.mainColor.id && colorItem.accentColor.id) {
				colorItem.code = `linear-gradient(to bottom, ${colorItem.mainColor.code} 50%, ${colorItem.accentColor.code} 50%)`;
			}
		},
		addColor() {
			this.customColorList.unshift({
				id: generateUUID(),
				code: "",
				isTwoToneCustom: true,
				mainColor: {},
				accentColor: {},
				quantity: "",
			});
		},
		delColor(index, colorItem) {
			this.customColorList.splice(index, 1);
			let tempList = this.selectedData["color card"];
			let findInd = tempList.findIndex((x) => {
				return x.id == colorItem.id;
			});
			if (findInd > -1) {
				this.selectedData["color card"].splice(findInd, 1);
			}
		},
		popularInputChange(val, name) {
			this.currentPopularCardItem = val;
			let tempList = this.selectedData[name];
			let index = tempList.findIndex((x) => {
				return x.id == val.id;
			});
			if (index >= 0) {
				if (val.quantity && val.quantity > 0) {
					tempList.splice(index, 1, val);
				} else {
					tempList.splice(index, 1);
					if (tempList.length) {
						this.currentPopularCardItem = tempList[tempList.length - 1];
					} else {
						this.currentPopularCardItem = null;
					}
				}
			} else {
				tempList.push(val);
			}
		},
		customInputChange(val) {
			this.currentCustomCardItem = val;
			let tempList = this.selectedData["color card"];

			let index = tempList.findIndex((x) => {
				return x.id == val.id;
			});
			if (index >= 0) {
				if (val.quantity && val.quantity > 0) {
					tempList.splice(index, 1, val);
				} else {
					tempList.splice(index, 1);
					if (tempList.length) {
						this.currentCustomCardItem = tempList[tempList.length - 1];
					} else {
						this.currentCustomCardItem = null;
					}
				}
			} else {
				tempList.push(val);
			}
		},
		findAll() {
			return new Promise((resolve, reject) => {
				this.allColor = [];
                let postData = {
                    keyWord: this.keyWord,
                    quoteColorCardList: this.selectedData["color card"],
                }
				indexApi.findAll(postData).then((res) => {
					let allColor = res.data;
					//去除末尾黑白两色
					if (res.data.length > 200) {
						allColor.splice(-2);
					}
					allColor.forEach((item) => {
						if (!item.quantity) {
							item.quantity = undefined;
						}
					});
					this.allColor = allColor;
					resolve();
				});
			});
		},
		inputColorFun(item, type, isDesigen) {},
		//距离左边距离
		getLeftFun(e, val) {
			val.left = e.target.getBoundingClientRect().left;
		},
		changeTabs(targetName) {
			if (targetName._props.label == "CUSTOM") {
				this.findAll();
			}
			this.$emit("update:tabsName", this.tabsName);
		},
	},
	computed: {
		lang() {
			return this.$store.getters.lang.quote || {};
		},
	},
	watch: {
		currentCustomCardItem: {
			handler(val) {
				this.$emit("currentCustomCardItem", val);
			},
			deep: true,
		},
		currentPopularCardItem: {
			handler(val) {
				this.$emit("currentPopularCardItem", val);
			},
			deep: true,
		},
		noDesignTab: {
			handler(val) {
				if (val) {
					this.tabsName = "POPULAR";
				}
			},
		},
	},
	created() {
        this.debounceFindAll = debounce(this.findAll, 500, false)
    },
};
</script>

<style scoped lang="scss">
.colorExtend {
	@include respond-to(mb) {
		max-height: 134px;
	}

    .search{
        margin-bottom: 10px;
    }

    .colorWrap{
        display: grid;
        grid-template-columns: repeat(6, 1fr);
        gap: 15px;
        max-height: 250px;
        overflow: hidden auto;
        padding: 10px;
    }

	.color {
		display: flex;
		justify-content: center;
		align-items: center;
		aspect-ratio: 1;
		border-radius: 50%;
		position: relative;
		cursor: pointer;

		b {
			display: none;
			color: #ffffff;
		}

		&.active b {
			display: block;
		}

		&.active::before {
			outline-color: $color-primary;
		}

		&::before {
			content: "";
			position: absolute;
			inset: -3px;
			border-radius: 50%;
			outline: 1px solid #cccccc;
		}
	}
}

.customColor {
	padding: 10px;

	@include respond-to(mb) {
		padding: 0;
	}

	.colorItemWrap {
		padding: 20px 0;
		border-bottom: 1px solid #ccc;

		@include respond-to(mb) {
			padding: 10px 0;
		}

		&:first-child {
			padding-top: 0;
		}

		&:last-child {
			border-bottom: none;
		}
	}

	.colorItem {
		display: grid;
		grid-template-columns: 324px 180px min-content;
		align-items: center;
		gap: 10px;

		@include respond-to(mb) {
			grid-template-columns: 1fr 1fr;
			align-items: flex-end;
		}

		.box1 .mainColor,
		.box1 .accentColor {
			display: flex;
			align-items: center;
			margin-bottom: 10px;

			@include respond-to(mb) {
				align-items: stretch;
				flex-direction: column;
				margin-bottom: 5px;
			}

			& > span {
				width: 110px;
				margin-right: 10px;
				font-size: 15px;
				font-weight: 700;
				@include respond-to(mb) {
					width: auto;
					margin-right: 0;
					margin-bottom: 4px;
					font-size: 14px;
				}
			}

			.colorSelect {
				display: flex;
				align-items: center;
				justify-content: space-between;
				width: 203px;
				height: 40px;
				padding: 0 10px;
				background: #ffffff;
				border-radius: 5px;
				border: 1px solid #d9dbdd;
				color: #999999;
				cursor: pointer;

				@include respond-to(mb) {
					width: 100%;
					height: 30px;
				}

				.t {
					display: flex;
					align-items: center;
				}

				.currentColor {
					display: inline-block;
					width: 16px;
					height: 16px;
					background: #ffffff;
					border-radius: 50%;
					border: 1px solid #d9dbdd;
					margin-right: 10px;

					@include respond-to(mb) {
						margin-right: 4px;
					}
				}

				.name {
					font-weight: 400;
				}
			}
		}

		.box1 .accentColor {
			margin-bottom: 0;
		}

		.box2 {
			display: grid;
			grid-template-columns: 36px 1fr;
			gap: 10px;
			background: #ffffff;
			border-radius: 5px;
			border: 1px solid #d9dbdd;
			padding: 8px;

			@include respond-to(mb) {
				padding: 8px;
			}

			.selectColor {
				display: flex;
				flex-direction: column;

				.mainColor {
					width: 36px;
					flex: 1;
					background: #ffffff;
					border: 1px solid #d9dbdd;
				}

				.accentColor {
					width: 36px;
					flex: 1;
					background: #ffffff;
					border: 1px solid #d9dbdd;
					border-top: none;
				}
			}

			.inputNum {
				display: flex;
				flex-direction: column;
				color: #999999;
				font-size: 15px;

				@include respond-to(mb) {
					font-size: 12px;
				}

				input {
					width: 100%;
					height: 30px;
					padding: 0 10px;
					margin-top: 4px;
					background: #ffffff;
					border-radius: 5px;
					border: 1px solid #d9dbdd;
					color: #333333;

					&[disabled] {
						cursor: not-allowed;
					}
				}
			}
		}

		b.icon-a-icon-dzlj-addzhuanhuan,
		b.icon-a-icon-dzlj-delzhuanhuan {
			margin-right: 4px;
			color: $color-primary;
			font-size: 25px;
			cursor: pointer;
		}
	}
}

.colorPicker {
	.hover-type:hover {
		border-color: $color-primary !important;
	}

	.custom-shadow2 {
		position: relative;
		background: #fff;

		&::before,
		&::after {
			content: "";
			position: absolute;
			z-index: -1;
			bottom: 14px;
			left: 0px;
			width: 50%;
			height: 20%;
			box-shadow: 0 14px 7px #d9dbdd;
			transform: rotate(-3deg);
		}

		&::after {
			right: 0;
			left: auto;
			transform: rotate(3deg);
		}
	}

	::v-deep .chooseYourLanyardColors {
		border: 1px solid #d9dbdd;

		@media screen and (max-width: 768px) {
			padding: 0;
			border: none;
			margin-top: -15px;
		}

		.tabs {
			.box {
				margin: 0;
				display: grid;
				grid-template-columns: repeat(5, 1fr);
				// grid-template-rows: repeat(auto-fill, 82px);
				column-gap: 18px;
				row-gap: 10px;
				max-height: 480px;
				// overflow-y: scroll;
				overflow-x: hidden;
				padding: 5px 20px 20px 20px;
				justify-content: space-between;

				@media screen and (max-width: 1919px) {
					grid-template-columns: repeat(auto-fill, minmax(175px, 180px));
					column-gap: normal;
				}

				@media screen and (max-width: 1500px) {
					grid-template-columns: repeat(4, 1fr);
					column-gap: 10px;
					padding: 5px 10px 20px 10px;
				}

				@media screen and (max-width: 1279px) and (min-width: 611px) {
					grid-template-columns: repeat(auto-fill, minmax(150px, 180px));
					justify-content: space-between;
					column-gap: 10px;
					padding: 5px 15px 20px 15px;
				}

				@media screen and (max-width: 610px) {
					grid-template-columns: repeat(3, 1fr);
					justify-content: space-between;
					column-gap: 10px;
					padding: 0;
				}

				@media screen and (max-width: 510px) {
					grid-template-columns: repeat(2, 1fr);
					justify-content: space-between;
					column-gap: 10px;
					padding: 0;
				}

				.item {
					margin: 0;
					padding: 0;
					border: 1px solid #d9dbdd;
					border-radius: 4px;
					display: grid;
					grid-template-columns: repeat(3, 1fr);
					grid-template-rows: repeat(2, 45px);
					box-sizing: border-box;

					.p1 {
						grid-column: 1/2;
						grid-row: 1/3;
						padding: 8px;

						> div {
							width: 100%;
							height: 100%;
							border: 1px solid #d9dbdd;
							background-size: cover;
						}
					}

					.p2 {
						grid-column: 2/4;
						grid-row: 1/2;
						padding: 5px 12px 5px 5px;
						overflow: hidden;
						text-overflow: ellipsis;
						white-space: nowrap;
						font-weight: 400;
						color: #666666;

						@media screen and (max-width: 768px) {
							font-size: 12px;
							padding: 5px 5px 5px 5px;
							white-space: wrap;
						}
					}

					.p3 {
						grid-column: 2/4;
						grid-row: 2/3;
						display: flex;
						padding: 0 15px 0 5px;

						@media screen and (max-width: 768px) {
							padding: 0 5px;
						}
					}

					&:hover {
						.p2 {
							overflow: visible;
						}
					}

					&.active {
						border-color: $color-primary;
					}
				}

				.item.add-custom-color {
					grid-column: 1/2;
					grid-row: 1/2;
					cursor: pointer;

					> div:nth-child(1) {
						grid-column: 1/4;
						grid-row: 1/2;
						display: flex;
						justify-content: center;
						align-items: flex-end;
					}

					> div:nth-child(2) {
						grid-column: 1/4;
						grid-row: 2/3;
						display: flex;
						justify-content: center;
						align-items: center;
						font-size: 15px;
						font-weight: 400;
						color: #666666;
					}
				}

				.item.add-custom-color.change {
					grid-column: 1/2;
					grid-row: 1/3;
					display: block;
					padding: 5px;

					.el-form-item {
						margin-bottom: 14px;

						.el-form-item__label {
							font-size: 15px;
							font-weight: 400;
							color: #666666;
							margin-bottom: 0;
						}

						.el-input {
							height: 26px;
						}
					}

					.el-form-item.bottom {
						margin-bottom: 0;

						.el-form-item__content {
							display: grid;
							grid-template-columns: 1fr 1fr;
							gap: 5px;

							.el-button + .el-button {
								margin: 0;
							}

							button {
								font-size: 15px;
								font-weight: 400;
								border: 0;
							}

							button:nth-child(1) {
								background: #f3f3f4;
								color: #333333;
							}

							button:nth-child(2) {
								background: $color-primary;
								color: #ffffff;
							}
						}
					}
				}
			}

			.el-tabs__header {
				margin-bottom: 20px;
				padding-top: 20px;
				display: flex;
				justify-content: center;

				@media screen and (max-width: 768px) {
					padding-top: 0;

					::v-deep .el-tab-pane:nth-child(2) > ul {
						margin-top: 20px;
					}
				}

				.el-tabs__nav-scroll {
					display: flex;
					justify-content: center;

					.el-tabs__nav {
						position: relative;
					}

					.el-tabs__nav::before {
						position: absolute;
						content: "";
						left: 0;
						bottom: 0;
						width: 100%;
						height: 2px;
						background-color: var(--el-border-color-light);
						z-index: var(--el-index-normal);
					}

					.el-tabs__active-bar {
						background-color: $color-primary;
					}

					.el-tabs__item {
						font-size: 18px;
						font-weight: bold;
						color: #333333;
						padding: 0;
						width: 120px;
						text-align: center;

						@media screen and (max-width: 768px) {
							font-size: 14px;
							height: 30px;
							line-height: 30px;
						}
					}

					.el-tabs__item.is-active {
						color: $color-primary;
					}
				}
			}
		}

		.searchColor {
			position: absolute;
			right: 50px;
			padding-top: 20px;
			z-index: 1;
			@media screen and (max-width: 768px) {
				right: 11px;
				padding-top: 34px;
			}

			.el-input--prefix .el-input__inner {
				padding-left: 30px;
				background-color: #f3f3f4;
				border: 0;
				font-size: 16px;
			}
		}

		.myInput {
			text-align: left;
			-webkit-appearance: none;
			background-color: var(--el-input-bg-color, var(--el-color-white));
			border-radius: 5px !important;
			background-image: none;
			border: 1px solid #dcdfe6;
			box-sizing: border-box;
			color: var(--el-input-text-color, var(--el-text-color-regular));
			display: inline-block;
			font-size: 16px;
			height: 30px;
			line-height: 30px;
			margin-top: 4px;
			outline: 0;
			padding: 0 11px;
			transition: var(--el-transition-border);
			width: 100%;

			@media screen and (max-width: 768px) {
				height: 30px;
				line-height: 30px;
				font-size: 12px;
			}
		}
	}
}
</style>
<style>
.hoverImage div {
	width: 150px;
	height: 300px;
	background-size: contain;
	background-repeat: no-repeat;
	background-position: center;
}
</style>