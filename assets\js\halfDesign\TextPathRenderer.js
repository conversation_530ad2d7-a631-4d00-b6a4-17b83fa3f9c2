class TextPathRenderer {
	constructor(config={}) {
		// 允许用户配置字体大小范围
		this.MIN_FONT_SIZE=config.minFontSize||14;
		this.MAX_FONT_SIZE=config.maxFontSize||42;
		this.config={
			minSpacing: 0.05,  // 最小间距为字体大小的5%
			maxSpacing: 0.2,   // 最大间距为字体大小的20%
			pathPadding: 0.02  // 路径两端预留2%空间
		};
	}

	render(options, ctx=null, fabricInstance=null) {
		const { text='', style, points, pathType, maxChars, fabricOptions }=options;
		if (!points||points.length<2) return null;

		// 创建路径并保存
		this._currentPath=this._createPath(points, pathType);
		if (!this._currentPath) return null;

		// 如果是空字符串，返回空的文本组
		if (!text.trim()) {
			if (fabricInstance) {
				return new fabricInstance.Group([], {
					selectable: false,
					evented: false,
					...fabricOptions
				});
			}
			return [];
		}

		const pathLength=this._getPathLength(this._currentPath);
		if (pathLength===0) return null;

		// 获取字符数组
		let chars=text.split('');
		if (maxChars&&maxChars>0) {
			chars=chars.slice(0, maxChars);
		}

		// 计算字符度量
		const charMetrics=this._calculateCharMetrics(chars, style);

		// 计算布局
		const layout=this._calculateLayout(charMetrics, pathLength, style);

		// 根据不同的渲染目标返回结果
		if (ctx) {
			return this._renderToContext(layout, ctx);
		} else if (fabricInstance) {
			return this._createFabricGroup(layout, fabricInstance, fabricOptions);
		}

		return layout;
	}

	_calculateCharMetrics(chars, style) {
		const canvas=document.createElement('canvas');
		const ctx=canvas.getContext('2d');

		return chars.map(char => {
			const fontSize=style.fixedFontSize||style.fontSize||this.MAX_FONT_SIZE;
			ctx.font=`${style.italic? 'italic ':''}${style.bold? 'bold ':''}${fontSize}px ${style.fontFamily}`;
			const metrics=ctx.measureText(char);
			return {
				char,
				width: metrics.width,
				height: fontSize,
				isAscii: char.charCodeAt(0)<256
			};
		});
	}

	_calculateLayout(charMetrics, pathLength, style) {
		// 使用固定字体大小或计算自适应大小
		let fontSize=style.fixedFontSize||Math.min(style.fontSize, this.MAX_FONT_SIZE)||this.MAX_FONT_SIZE;
		let usableLength=pathLength*0.98; // 预留2%的空间

		// 判断是否为圆形路径
		const isCircularPath=this._isCircularPath(this._currentPath);
		if (isCircularPath) usableLength=pathLength

		// 如果没有固定字体大小，进行自适应计算
		if (!style.fixedFontSize) {
			while (this._calculateTotalWidth(charMetrics, fontSize)>usableLength&&fontSize>this.MIN_FONT_SIZE) {
				fontSize--;
			}
		}

		// 计算可以显示的字符数量和位置
		const { chars, totalWidth }=this._calculateVisibleChars(charMetrics, fontSize, usableLength);

		// 对于圆形路径，使用专门的布局方法
		if (isCircularPath) {
			return this._calculateCircularCharPositions(chars, fontSize, style);
		}

		// 计算起始偏移，确保文字居中
		const startOffset=(pathLength-totalWidth)/2;

		// 计算每个字符的具体位置
		return this._calculateCharPositions(chars, startOffset, pathLength, fontSize, style);
	}

	_calculateTotalWidth(charMetrics, fontSize) {
		const scale=fontSize/charMetrics[0].height;
		let totalWidth=0;
		let spacing=fontSize*this.config.minSpacing;

		charMetrics.forEach((metric, i) => {
			if (i>0) totalWidth+=spacing;
			totalWidth+=metric.width*scale;
		});

		return totalWidth;
	}

	_calculateVisibleChars(charMetrics, fontSize, usableLength) {
		const scale=fontSize/charMetrics[0].height;
		const spacing=fontSize*this.config.minSpacing;
		let totalWidth=0;
		const visibleChars=[];

		for (let i=0; i<charMetrics.length; i++) {
			const charWidth=charMetrics[i].width*scale;
			const newWidth=totalWidth+(i>0? spacing:0)+charWidth;

			if (newWidth>usableLength) break;

			visibleChars.push({
				...charMetrics[i],
				width: charWidth,
				spacing: i>0? spacing:0
			});

			totalWidth=newWidth;
		}

		return { chars: visibleChars, totalWidth };
	}

	_calculateCharPositions(chars, startOffset, pathLength, fontSize, style) {
		let currentOffset=startOffset;
		const positions=[];

		chars.forEach(char => {
			// 确保有足够空间容纳整个字符
			if (currentOffset+char.width<=pathLength) {
				const point=this._getPointAtLength(currentOffset+char.width/2);
				if (point) {
					positions.push({
						char: char.char,
						x: point.x,
						y: point.y,
						angle: point.angle,  // 保持原始角度
						fontSize,
						style
					});
				}
			}
			currentOffset+=char.width+char.spacing;
		});

		return positions;
	}

	_renderToContext(layout, ctx) {
		// 保存当前上下文状态
		ctx.save();

		if (this._currentPath) {
			ctx.save();
			ctx.strokeStyle='#ccc';
			ctx.lineWidth=1;
			const path=new Path2D(this._currentPath);
			ctx.stroke(path);
			ctx.restore();
		}

		// 绘制文字，使用与 fabric 相同的渲染参数
		layout.forEach(pos => {
			ctx.save();
			ctx.translate(pos.x, pos.y);
			ctx.rotate(pos.angle*Math.PI/180);

			// 使用与 fabric 相同的字体设置
			const fontStyle=pos.style.italic? 'italic ':'';
			const fontWeight=pos.style.bold? 'bold ':'';
			ctx.font=`${fontStyle}${fontWeight}${pos.fontSize}px ${pos.style.fontFamily}`;
			ctx.fillStyle=pos.style.fill||'#000000';

			// 设置文字对齐方式，与 fabric 保持一致
			ctx.textAlign='center';
			ctx.textBaseline='bottom';

			// 应用阴影效果（如果有）
			if (pos.style.shadow?.enabled) {
				ctx.shadowColor=pos.style.shadow.color;
				ctx.shadowBlur=pos.style.shadow.blur;
				ctx.shadowOffsetX=pos.style.shadow.offsetX;
				ctx.shadowOffsetY=pos.style.shadow.offsetY;
			}

			ctx.fillText(pos.char, 0, 0);
			ctx.restore();
		});

		// 恢复初始上下文状态
		ctx.restore();

		return layout;
	}

	_createFabricGroup(layout, fabricInstance, fabricOptions) {
		const group=new fabricInstance.Group([], {
			selectable: false,
			evented: false,
			slefType: 'textPathGroup',
			...fabricOptions
		});

		layout.forEach(char => {
			const text=new fabricInstance.Text(char.char, {
				left: char.x,
				top: char.y,
				fontSize: char.fontSize,
				fontFamily: char.style.fontFamily,
				fill: char.style.fill||'#000000',
				fontWeight: char.style.fontWeight=='bold'? 'bold':'normal',
				fontStyle: char.style.fontStyle=='italic'? 'italic':'normal',
				opacity: char.style.opacity? char.style.opacity:1,
				originX: 'center',
				originY: 'bottom',
				angle: char.angle,  // 使用原始角度
				shadow: char.style.shadow?.enabled? {
					color: char.style.shadow.color,
					blur: char.style.shadow.blur,
					offsetX: char.style.shadow.offsetX,
					offsetY: char.style.shadow.offsetY
				}:null
			});
			group.addWithUpdate(text);
		});

		return group;
	}

	_createPath(points, pathType) {
		switch (pathType) {
		case 'line':
			return this._createLinePath(points);
		case 'quadratic':
			return this._createQuadraticPath(points);
		case 'cubic':
			return this._createCubicPath(points);
		case 'circle':
			return this._createCirclePath(points);
		default:
			return null;
		}
	}

	_createLinePath(points) {
		if (points.length<2) return null;
		return `M ${points[0].x} ${points[0].y} L ${points[1].x} ${points[1].y}`;
	}

	_createQuadraticPath(points) {
		if (points.length<3) return null;
		return `M ${points[0].x} ${points[0].y} Q ${points[1].x} ${points[1].y}, ${points[2].x} ${points[2].y}`;
	}

	_createCubicPath(points) {
		if (points.length<4) return null;
		return `M ${points[0].x} ${points[0].y} C ${points[1].x} ${points[1].y}, ${points[2].x} ${points[2].y}, ${points[3].x} ${points[3].y}`;
	}

	_createCirclePath(points) {
		if (points.length<3) return null;

		// 通过三点确定圆的参数
		const circle=this._getCircleParams(points[0], points[1], points[2]);
		if (!circle) return null;

		const { centerX, centerY, radius }=circle;

		// 创建完整的圆形路径
		return `M ${centerX-radius} ${centerY} `+
			`A ${radius} ${radius} 0 1 1 ${centerX+radius} ${centerY} `+
			`A ${radius} ${radius} 0 1 1 ${centerX-radius} ${centerY}`;
	}

	_getCircleParams(p1, p2, p3) {
		// 计算三点确定的圆的参数
		const x1=p1.x, y1=p1.y;
		const x2=p2.x, y2=p2.y;
		const x3=p3.x, y3=p3.y;

		const temp=x2*x2+y2*y2;
		const bc=(x1*x1+y1*y1-temp)/2.0;
		const cd=(temp-x3*x3-y3*y3)/2.0;
		const det=(x1-x2)*(y2-y3)-(x2-x3)*(y1-y2);

		if (Math.abs(det)<1e-10) return null;

		const centerX=(bc*(y2-y3)-cd*(y1-y2))/det;
		const centerY=((x1-x2)*cd-(x2-x3)*bc)/det;
		const radius=Math.sqrt(Math.pow(x1-centerX, 2)+Math.pow(y1-centerY, 2));

		return { centerX, centerY, radius };
	}

	_getPathLength(path) {
		const tempPath=document.createElementNS('http://www.w3.org/2000/svg', 'path');
		tempPath.setAttribute('d', path);
		return tempPath.getTotalLength();
	}

	_getPointAtLength(length) {
		const tempPath=document.createElementNS('http://www.w3.org/2000/svg', 'path');
		tempPath.setAttribute('d', this._currentPath);
		const point=tempPath.getPointAtLength(length);
		const nextPoint=tempPath.getPointAtLength(length+1);

		return {
			x: point.x,
			y: point.y,
			angle: Math.atan2(nextPoint.y-point.y, nextPoint.x-point.x)*180/Math.PI
		};
	}

	_isCircularPath(path) {
		if (!path) return false;

		const tempPath=document.createElementNS('http://www.w3.org/2000/svg', 'path');
		tempPath.setAttribute('d', path);

		// 获取路径的起点和终点
		const start=tempPath.getPointAtLength(0);
		const end=tempPath.getPointAtLength(tempPath.getTotalLength());

		// 计算起点和终点的距离
		const distance=Math.sqrt(
			Math.pow(end.x-start.x, 2)+
			Math.pow(end.y-start.y, 2)
		);

		// 如果起点和终点距离很近（小于1像素），认为是闭合的圆形路径
		return distance<1;
	}

	// 新增：获取圆的中心点
	_getCircleCenter() {
		const tempPath=document.createElementNS('http://www.w3.org/2000/svg', 'path');
		tempPath.setAttribute('d', this._currentPath);
		const length=tempPath.getTotalLength();

		// 获取四个点来计算中心
		const point0=tempPath.getPointAtLength(0);
		const point90=tempPath.getPointAtLength(length/4);
		const point180=tempPath.getPointAtLength(length/2);
		const point270=tempPath.getPointAtLength(length*3/4);

		return {
			x: (point0.x+point180.x)/2,
			y: (point90.y+point270.y)/2
		};
	}

	// 新增：专门处理圆形路径的字符位置计算
	_calculateCircularCharPositions(chars, fontSize, style) {
		const positions=[];
		const center=this._getCircleCenter();
		const pathLength=this._getPathLength(this._currentPath);
		const radius=pathLength/(2*Math.PI);

		// 根据配置决定是否转换为大写
		if (style.uppercase) {
			chars=chars.map(char => ({
				...char,
				char: char.char.toUpperCase()
			}));
		}

		// 计算总字符数
		const totalChars=chars.length;

		// 计算字符间的固定间距（以像素为单位），并设置最大间距
		const baseSpacing=fontSize*0.02; // 基础间距为字体大小的2%
		const maxSpacing=5; // 最大间距为5像素
		const fixedSpacing=Math.min(baseSpacing, maxSpacing);

		// 计算每个字符的宽度和总宽度
		const totalWidth=chars.reduce((sum, char) => sum+char.width, 0);
		const totalSpacing=(totalChars-1)*fixedSpacing;
		const totalLength=totalWidth+totalSpacing;

		// 计算总角度（弧度）
		const totalRadian=totalLength/radius;
		// 转换为角度
		let totalAngle=(totalRadian*180)/Math.PI;

		// 自适应字体大小，如果文字太长
		let adjustedFontSize=fontSize;
		let visibleChars=chars;
		let adjustedSpacing=fixedSpacing;

		// 如果总角度接近但小于360度（例如在350-359度之间），稍微增加间距以填满整个圆
		if (totalAngle>320&&totalAngle<360) {
			// 降低阈值到320度，确保更多情况下能填满整个圆
			const extraSpacingTotal=(360-totalAngle)*(Math.PI*radius/180);
			adjustedSpacing=fixedSpacing+(extraSpacingTotal/(totalChars-1));
			totalAngle=360; // 设置为完整的圆
		}
		// 如果总角度超过360度，则需要调整
		else if (!style.fixedFontSize&&totalAngle>360) {
			// 计算需要的缩放比例
			const scale=360/totalAngle;
			adjustedFontSize=Math.max(this.MIN_FONT_SIZE, fontSize*scale);

			// 如果缩放后仍然小于最小字体，则需要截断文字
			if (adjustedFontSize<=this.MIN_FONT_SIZE) {
				// 重新计算可以显示的字符数量
				const maxAngle=360;
				let currentLength=0;
				let i=0;

				while (i<chars.length) {
					currentLength+=chars[i].width+(i>0? fixedSpacing:0);
					const currentAngle=(currentLength/radius)*(180/Math.PI);

					if (currentAngle>maxAngle) break;
					i++;
				}

				visibleChars=chars.slice(0, i);

				// 重新计算总宽度和角度
				const newTotalWidth=visibleChars.reduce((sum, char) => sum+char.width, 0);
				const newTotalSpacing=(visibleChars.length-1)*fixedSpacing;
				const newTotalLength=newTotalWidth+newTotalSpacing;

				totalAngle=(newTotalLength/radius)*(180/Math.PI);

				// 如果截断后的角度接近但小于360度，增加间距以填满整个圆
				if (totalAngle>320&&totalAngle<360) {
					const extraSpacingTotal=(360-totalAngle)*(Math.PI*radius/180);
					adjustedSpacing=fixedSpacing+(extraSpacingTotal/(visibleChars.length-1));
					totalAngle=360;
				}
			}
		}

		// 根据起始位置设置初始角度和文字旋转角度
		let baseAngle;
		let charRotation;
		switch (style.textPosition) {
		case 'bottom':
			baseAngle=90;
			charRotation=0;
			break;
		case 'left':
			baseAngle=180;
			charRotation=90;
			break;
		case 'right':
			baseAngle=0;
			charRotation=90;
			break;
		case 'top':
		default:
			baseAngle=-90;
			charRotation=90;
			break;
		}

		// 计算实际的起始角度
		const startAngle=baseAngle-(totalAngle/2);

		// 放置字符
		let currentAngle=startAngle;
		visibleChars.forEach((char, i) => {
			// 计算字符中心点的角度
			const charWidth=char.width*(adjustedFontSize/fontSize);
			const charAngle=(charWidth/(2*radius))*(180/Math.PI);

			// 更新到字符中心点的角度
			currentAngle+=charAngle;

			const radian=currentAngle*Math.PI/180;
			let x=center.x+radius*Math.cos(radian);
			let y=center.y+radius*Math.sin(radian);

			// 底部文字特殊处理
			if (style.textPosition==='bottom') {
				// 计算总角度范围内的中点
				const totalChars=visibleChars.length;
				const halfTotalAngle=totalAngle/2;

				// 从左到右排列，需要反转角度计算
				const adjustedCurrentAngle=baseAngle+halfTotalAngle-(currentAngle-startAngle);
				const radian=adjustedCurrentAngle*Math.PI/180;

				// 将文字放在圆的外部
				const extraRadius=radius+adjustedFontSize*1.2;
				x=center.x+extraRadius*Math.cos(radian);
				y=center.y+extraRadius*Math.sin(radian);

				positions.push({
					char: char.char,
					x: x,
					y: y,
					angle: adjustedCurrentAngle-90, // 使文字正向面对用户
					fontSize: adjustedFontSize,
					style
				});
			} else {
				positions.push({
					char: char.char,
					x: x,
					y: y,
					angle: currentAngle+charRotation,
					fontSize: adjustedFontSize,
					style
				});
			}

			// 使用原有的间距计算
			const spacingAngle=(adjustedSpacing*(adjustedFontSize/fontSize)/radius)*(180/Math.PI);
			currentAngle+=charAngle+spacingAngle;
		});

		return positions;
	}
}

export default TextPathRenderer;