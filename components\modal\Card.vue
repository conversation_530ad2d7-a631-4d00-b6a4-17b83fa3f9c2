<template>
	<div class="modal-box" :class="modal.class" :float="modal.float" :even="!(modal.list.length % 2)"
		:style="modal.style" @click.self="setModalType({}, [], 'card_list')">
		<b pointer class="icon-guanbi" @click="$store.commit('setMask', false)" v-if="modal.dialog"></b>
		<template v-for="(o, oi) in modal.outer">
			<div v-show="Object.keys(o || {}).length"
				:style="{ float: modal.float, width: modal.float ? (100 - modal.margin * (modal.column - 1)) / modal.column + '%' : false, 'margin-right': modal.float ? modal.margin + '%' : false, ...o.style }">
				<EditDiv v-if="o.title?.value" :tagName="o.title.tagName || 'h2'" v-model:content="o.title.value"
					:style="modal.titleStyle" @click="setModalType(o.title, modal.outer, 'text')" />

				<div class="sub-title" :pointer="o.subTitle?.event" :style="modal.subTitleStyle"
					:hidden="!o.title?.value && !o.subTitle?.value"
					@click="setModalType(o.subTitle, modal.outer, 'text', o.subTitle.event)">
					<EditDiv v-if="o.subTitle?.value" v-model:content="o.subTitle.value" />
				</div>

				<EditDiv v-if="o.text?.value" :style="modal.textStyle" v-model:content="o.text.value"
					@click="setModalType(o.text, modal.outer, 'text')" />

				<div flex class="tab-box" v-if="o.tabList" :style="modal.tabBoxStyle"
					@click="setModalType(o.tabList, modal.outer, 'nav_list')">
					<EditDiv flex pointer v-for="(t, ti) in o.tabList" v-model:content="t.value" :key="ti"
						:tagName="t.tagName || 'h3'" :primary="tabIndex == ti" @click="changeTab(t, ti)"
						:style="tabIndex == ti ? { ...modal.tabStyle, ...modal.tabSeleStyle } : modal.tabStyle" />
				</div>

				<div flex v-if="o.reviews" :pointer="o.reviews.url" class="reviews" :style="modal.reviewsStyle"
					@click="setModalType(o.reviews, modal.outer, 'reviews', o.reviews)">
					<span v-show="o.reviews.showScore && o.reviews.star > 1">{{ o.reviews.star }}</span>
					<div class="star-box" v-show="o.reviews.star > 1"
						:style="{ ...modal.reviewsStarStyle, backgroundImage: 'linear-gradient(90deg,' + (o.reviews.starColor || '#F96A00') + ' ' + o.reviews.star * 20 + '%,#999 0)' }">
						<b v-for="s in 5" :class="o.reviews.starIcon || 'icon-star'"></b>
					</div>
					<template v-if="o.reviews.number">
						<span :style="modal.reviewsExcellentStyle">{{ lang.excellent }}</span>
						<span :style="modal.reviewsReviewsStyle"
							v-html="o.reviews.number.toLocaleString() + '+ ' + lang.reviews + ' ' + lang.on"></span>
						<pic :style="modal.reviewsImgStyle" :alt="lang.shopperApproved"
							:src="o.reviews.img || 'https://sticker-static.oss-accelerate.aliyuncs.com/image/uploads/20230728/ShopperApproved.png'" />
					</template>
				</div>

				<div class="search-box" v-if="o.search" :style="modal.searchStyle">
					<input type="text" v-model="searchKeyword" :placeholder="o.search.value" />
					<b :class="o.search.icon"></b>
				</div>

				<div class="hover-tag" v-if="(modal.float || modal.position) && o.button" :style="modal.boxBtnStyle">
					<button :primary="!o.button.outline && o.button.value" :outline="o.button.outline"
						:title="o.button.alt" :style="{ ...modal.btnStyle, ...o.btnStyle }">
						<EditDiv v-show="o.button.value" tagName="label" v-model:content="o.button.value"
							@click="setModalType(o.button, modal.outer, 'button', o.button)" />
						<b :class="o.button.icon" v-show="o.button.icon"></b>
					</button>
				</div>
			</div>



			<b pointer v-if="modal.showArrow" :class="modal.showArrow" :disabled="scrollIndex == 0"
				:style="{ ...modal.outArrowStyle, ...modal.outArrow1Style }" @click.stop="scrollBox(-1)"></b>

			<div class="card-box" v-if="modal.repeat || !oi" :scrollbar="modal.scroll"
				:style="{ order: modal.boxStyle?.order, ...modal.cardBoxStyle }">
				<div flex
					:style="{ display: modal.float ? 'inline' : null, width: modal.scroll ? (modal.column * 100 + modal.margin * (modal.column - 1) + 3) + '%' : false, 'grid-gap': Math.min(modal.column, 1.5) + 'em ' + modal.margin + '%', ...modal.boxStyle }">
					<div flex class="card part2 hover-tag" v-for="(l, li) in modal.list" :childHoverIndex="li"
						:nowPage="l.button?.url == $store.state.pagePath" :cornerLabel="l.cornerLabelType || 'left'"
						:pointer="l.event && (modal.linkArea == 'card')" :mask="modal.mask"
						@click="setModalType($event.target, null, 'card', l.event)" :key="Math.random()"
						@touchstart="mousedown" @touchend="mouseup($event, l)"
						:hidden="(o.tabList?.length && l.tab != tabIndex) || (!isViewMore && !!o.button?.url && (typeof (Number(o.button?.url)) == 'number') && (li >= o.button?.url))"
						:style="{ float: modal.float ? 'left' : false, width: (100 - modal.margin * (modal.column - 1)) / modal.column + '%', marginTop: modal.float && (li > (modal.column - 2)) ? (9 - modal.column) / 3 + '%' : null, marginRight: modal.float && ((li + 2) % modal.column && li < modal.list.length - 1) ? modal.margin + '%' : null, ...modal.cardStyle, ...l.style }">

						<PicVideo v-if="l.img || l.video" :modal="modal" :data="l"
							:pic-style="{ ...modal.cardImgStyle, ...l.img?.style, ...l.imgStyle }"
							:video-box-style="{ ...modal.cardImgStyle, ...modal.cardVideoBoxStyle, ...l.videoBoxStyle }"
							:video-style="{ ...modal.cardVideoStyle, ...l.videoStyle }" @setModalType="setModalType" />

						<pic v-else-if="l.imgList?.length" :pointer="l.event && (modal.linkArea == 'img')"
							:src="l.imgList[l.imgIndex].value" :alt="l.imgList[l.imgIndex].alt"
							:style="{ ...modal.cardImgStyle, ...l.imgStyle }"
							@click="setModalType(null, modal.list, 'img', l.event)" />

						<b v-if="l.icon" class="hover-tag icon" :class="l.icon.value"
							:style="{ ...modal.cardImgStyle, ...l.icon.style, ...l.iconStyle }"
							@click="setModalType(l.icon, modal.list, 'icon')"></b>

						<span :style="{ ...modal.cardIndexStyle, ...modal.cardIndexNumStyle, ...l.indexNumStyle }"
							v-if="modal.showIndex" class="index">{{ li + 1 }}</span>
						<EditDiv class="index" v-if="l.index?.value" v-model:content="l.index.value"
							:tagName="l.index.tagName || 'div'" :style="{ ...modal.cardIndexStyle, ...l.indexStyle }"
							@click="setModalType(l.index, modal.list, 'text')" />

						<div v-if="l.cornerLabel?.id" flex class="corner-label" :onlyImg="!l.cornerLabel.value"
							:style="{ color: l.cornerLabel.color, backgroundImage: 'url(' + l.cornerLabel.bgImg + ')' }">
							<pic v-if="l.cornerLabel.icon" :src="l.cornerLabel.icon" :alt="l.cornerLabel.value" />
							<EditDiv v-if="l.cornerLabel?.value" tagName="label" v-model:content="l.cornerLabel.value"
								@click="setModalType(l.cornerLabel, modal.list, 'text')" />
						</div>

						<div class="content" :style="{ ...modal.cardContentStyle, ...l.contentStyle }">
							<EditDiv v-if="l.title?.value" class="card-title" v-model:content="l.title.value"
								:style="{ ...modal.cardTitleStyle, ...l.title.style, ...l.titleStyle }"
								@click="setModalType(l.title, modal.list, 'text')" :tagName="l.title.tagName || 'h3'" />

							<div v-if="l.imgList" class="img-list"
								:style="{ ...modal.cardImgListBoxStyle, ...l.imgListBoxStyle }">
								<b class="icon-qiehuan" :hidden="modal.hideImgListIcon" :style="modal.cardImgsArrowLast"
									@click="changeImgs(l, li, l.imgIndex > 0 ? l.imgIndex - 1 : l.imgList.length - 1)"></b>
								<div flex scrollbar class="list-box" :pointer="l.imgList.length > 1"
									:style="{ ...modal.cardImgsBoxStyle, ...l.imgsBoxStyle }">
									<template v-for="(p, pi) in l.imgList">
										<pic v-if="!p.value?.endsWith('.mp4') || p.poster" :src="p.poster || p.value"
											:sele="pi == l.imgIndex" @click="changeImgs(l, li, pi)" :alt="p.alt"
											:style="{ ...modal.cardImgListStyle, ...l.imgListStyle, ...p.style }" />
										<video v-else :poster="p.poster" :title="p.alt" :src="p.value"
											:sele="pi == l.imgIndex" @click="changeImgs(l, li, pi)"
											:style="{ ...modal.cardImgListStyle, ...l.imgListStyle }"></video>
									</template>
								</div>
								<b class="icon-qiehuan2" :hidden="modal.hideImgListIcon"
									:style="modal.cardImgsArrowNext"
									@click="changeImgs(l, li, (l.imgIndex < l.imgList.length - 1) ? l.imgIndex + 1 : 0)"></b>
							</div>

							<EditDiv v-if="l.subTitle?.value" :noEdit="modal.noEdit" class="card-subtitle"
								v-model:content="l.subTitle.value"
								:style="{ ...modal.cardSubTitleStyle, ...l.subTitle.style, ...l.subTitleStyle }"
								@click="setModalType(l.subTitle, modal.list, 'text')" />

							<b class="arrow" :style="{ ...modal.cardArrowStyle, ...l.arrowStyle }"
								v-if="modal.showExpand" :class="{ rotate: l.isExpand, [modal.showExpand]: true }"
								@click="l.isExpand = !l.isExpand, $forceUpdate()"></b>

							<div class="price-list" :style="{ ...modal.cardPriceListStyle, ...l.priceListStyle }"
								v-if="l.priceList" @click="setModalType(l.priceList, modal.list, 'price_list')">
								<template v-for="p in l.priceList">
									<CCYRate v-if="Number(p.value)" :price="p.value"
										:style="{ ...modal.cardPriceItemStyle, ...p.style }"></CCYRate>
									<pic v-else-if="p.value?.startsWith('https')" :src="p.value"
										:style="{ ...modal.cardPriceItemStyle, ...p.style }" />
									<span v-else v-html="p.value"
										:style="{ ...modal.cardPriceTextStyle, ...p.style }"></span>
								</template>
							</div>

							<div class="btn-list" v-if="l.btnList" :style="modal.cardBtnListStyle">
								<button v-for="b in l.btnList" :style="{...modal.cardBtnStyle, ...modal.cardBtnListItemStyle}"
									:primary="!b.outline" :outline="b.outline" :key="b.value" :title="b.value"
									@click="setModalType(l.btnList, modal.list, 'img_list', b)">
									<EditDiv v-show="b.value" tagName="label" v-model:content="b.value" />
									<b v-show="b.icon" :class="b.icon"></b>
								</button>
							</div>

							<div flex v-if="l.reviews" :pointer="l.reviews.url" class="reviews"
								:style="modal.reviewsStyle"
								@click="setModalType(l.reviews, modal.outer, 'reviews', l.reviews)">
								<span v-show="l.reviews.showScore && l.reviews.star > 1">{{ l.reviews.star }}</span>
								<div class="star-box" v-show="l.reviews.star > 1"
									:style="{ ...modal.reviewsStarStyle, backgroundImage: 'linear-gradient(90deg,' + (l.reviews.starColor || '#F96A00') + ' ' + l.reviews.star * 20 + '%,#999 0)' }">
									<b v-for="s in 5" :class="l.reviews.starIcon || 'icon-star'"></b>
								</div>
								<template v-if="l.reviews.number">
									<span :style="modal.reviewsExcellentStyle">{{ lang.excellent }}</span>
									<span :style="modal.reviewsReviewsStyle"
										v-html="l.reviews.number.toLocaleString() + '+ ' + lang.reviews + ' ' + lang.on"></span>
									<pic :style="modal.reviewsImgStyle" :alt="lang.shopperApproved"
										:src="l.reviews.img || 'https://sticker-static.oss-accelerate.aliyuncs.com/image/uploads/20230728/ShopperApproved.png'" />
								</template>
							</div>

							<EditDiv v-if="l.text" :noEdit="modal.noEdit" class="card-text"
								:noExpand="modal.showExpand && !l.isExpand"
								:style="{ ...modal.cardTextStyle, ...l.text.style, ...l.textStyle }"
								@click="setModalType(l.text, modal.list, 'text')" v-model:content="l.text.value" />
							<EditDiv v-if="l.price" class="card-price" v-model:content="l.price.value"
								:style="{ ...modal.cardPriceStyle, ...l.price.style, ...l.priceStyle }"
								@click="setModalType(l.price, modal.list, 'text')" />

							<div class="card-btn" :style="{ ...modal.cardBtnBoxStyle, ...l.btnBoxStyle }"
								v-if="l.button || l.button1">
								<button v-if="l.button?.value || l.button?.icon" class="hover-tag" :title="l.button.alt"
									:primary="!l.button.outline && l.button?.value" :outline="l.button.outline"
									:style="{ ...modal.cardBtnStyle, ...l.button.style, ...l.btnStyle }"
									@click="setModalType(l.button, modal.list, 'button', l.button, l.event)">
									<EditDiv v-if="l.button.value" tagName="label" v-model:content="l.button.value" />
									<b v-if="l.button.icon" :class="l.button.icon"
										:style="{ ...l.button.iconStyle, ...l.btnIconStyle }"></b>
								</button>

								<button v-if="l.button1?.value || l.button1?.icon" class="hover-tag"
									:title="l.button1.alt" :primary="!l.button1.outline" :outline="l.button1.outline"
									:style="{ ...modal.cardBtn1Style, ...l.button1.style, ...l.btn1Style }"
									@click="setModalType(l.button1, modal.list, 'button', l.button1, l.event)">
									<EditDiv v-if="l.button1.value" tagName="label" v-model:content="l.button1.value" />
									<b v-if="l.button1.icon" :class="l.button1.icon"
										:style="{ ...l.button1.iconStyle, ...l.btn1IconStyle }"></b>
								</button>
							</div>
						</div>
					</div>
				</div>
			</div>

			<b pointer v-if="modal.showArrow" :class="modal.showArrow" :disabled="scrollIndex == modal.column - 1"
				:style="{ ...modal.outArrowStyle, ...modal.outArrow2Style }" @click.stop="scrollBox(1)"></b>


			<div flex class="point-box" v-if="(modal.repeat || !oi) && modal.scroll && !modal.hidePoint"
				:style="modal.pointBoxStyle">
				<div v-for="(l, li) in modal.column" :class="{ select: scrollIndex == li }" :key="li"
					@click="scrollBox(li - scrollIndex)"></div>
			</div>



			<div class="btn-box" v-if="!modal.float && !modal.position && o.button?.value" :style="modal.boxBtnStyle">
				<button :title="o.button.alt" :style="{ ...modal.btnStyle, ...o.button.style, ...o.btnStyle }"
					:primary="!o.button.outline && o.button.value" :outline="o.button.outline"
					@click="setModalType(o.button, modal.outer, 'button', o.button)">
					<EditDiv tagName="label" v-if="o.button.value && !isViewMore" v-model:content="o.button.value" />
					<EditDiv tagName="label" v-else-if="o.button.viewLessValue"
						v-model:content="o.button.viewLessValue" />

					<b :class="o.button.icon" v-if="o.button.icon && !isViewMore" :style="o.button.iconStyle"></b>
					<b :class="o.button.viewLessIcon" v-else :style="o.button.viewLessIconStyle"></b>
				</button>
			</div>
		</template>
	</div>
</template>

<script>
export default {
	name: "modalCard",
	props: {
		data: {
			type: Object,
			default: {},
		}
	},
	data() {
		return {
			modal: {
				column: 4,
				margin: 1.5,
				type: {},
				style: {},
				outer: [{}],
				linkArea: 'img', // 默认img：图片和按钮；btn：仅按钮；card：整个卡片
				...this.data
			},
			scrollIndex: 0,
			tabIndex: 0,
			touchStartX: 0,
			isViewMore: false,
			searchKeyword: '',
		};
	},
	computed: {
		lang() {
			return this.$store.getters.lang.layout || {};
		},
		cardBoxDom() {
			return document.querySelector(`#${this.modal.id} .card-box${this.modal.scroll ? '' : '>div'}`);
		},
		videoList() {
			return document.querySelectorAll(`#${this.modal.id} video`) || [];
		},
	},
	watch: {
		modal: {
			handler(val) {
				if (process.env.isManage) this.$emit("update:data", val);
			},
			immediate: true,
			deep: true
		},
		scrollIndex(val) {
			if (val > this.modal.column - 1) this.scrollIndex = this.modal.column - 1;
			else if (val < 0) this.scrollIndex = 0;
		},
		searchKeyword(val) {
			const result = this.data.list.slice(1).filter(l => {
				return Object.values(l).some(i => !i.value?.startsWith('http') && i.value?.toLowerCase().includes(val.toLowerCase()));
			})
			this.modal.list = [this.data.list[0], ...result]
		}
	},
	created() {
		this.modal.list.map(l => {
			if (l.imgList && !l.imgIndex) l.imgIndex = 0;
			this.setEvent(l);
		});
	},
	mounted() {
		this.$nextTick(() => {
			// 默认视频播放方式为页面滑动到所在位置自动播放
			if (!this.modal.manualPlay && !this.modal.mousePlay && this.videoList?.length) document.addEventListener("scroll", () => {
				this.videoList.forEach(i => {
					if (i.play && i.getBoundingClientRect && (i.getBoundingClientRect().top < window.innerHeight)) i.play();
				});
			})

			// 作为滑动选项卡时计算滑动屏数
			if (this.cardBoxDom && this.$store.getters.isMobile && this.modal.scroll) this.cardBoxDom.addEventListener("scroll", e => setTimeout(() => {
				this.scrollIndex = Math.round((e.target.clientWidth + e.target.scrollLeft) / e.target.scrollWidth * this.modal.column - 1);
			}, 500))

			// content内无内容时不占grid-gap位
			document.querySelectorAll(`#${this.modal.id} .card .content`).forEach(i => {
				if (!i.children.length) i.style = "display:none";
			})
		});
	},
	methods: {
		setModalType(target, targetArray, clickType, event, other) {
			if (!process.env.isManage && clickType == 'card') {
				if (this.modal.linkArea == 'card' && target.localName != 'video' && target.localName != 'button' && target.parentElement?.localName != 'button') this.$setModal(this, null, null, '', event);
			} else if (!process.env.isManage && clickType == 'img' && this.modal.linkArea != 'img') return;
			else if ((clickType == 'button' || clickType == 'img') && !event.url && !(event.method && Object.keys(event.method).length)) this.$setModal(this, target, targetArray, clickType, other);
			else this.$setModal(this, target, targetArray, clickType, event, other);
		},
		setEvent(l) {
			if ((l.button?.value || l.button?.icon) && (l.button?.url || (l.button?.method && Object.keys(l.button.method).length))) l.event = l.button;
			else if (l.button1?.url || (l.button1?.method && Object.keys(l.button1.method).length)) l.event = l.button1;
			else if (l.imgList?.length > 1) l.event = { ...l.button, ...l.imgList[(l.imgList[l.imgIndex].url || (l.imgList[l.imgIndex].method && Object.keys(l.imgList[l.imgIndex].method).length) || (l.button?.method && Object.keys(l.button.method).length)) ? l.imgIndex : 0] };
			else if (l.img?.url || (l.img?.method && Object.keys(l.img.method).length)) l.event = { ...l.button, ...l.img };
			else if (l.imgList?.length && (l.imgList[0]?.url || (l.imgList[0]?.method && Object.keys(l.imgList[0].method).length))) l.event = { ...l.button, ...l.imgList[0] };
		},
		scrollBox(index) {
			// 点击滚动（父盒子总宽度 / 总屏数 * 滚几屏）
			this.scrollIndex += index;
			if (this.cardBoxDom) this.cardBoxDom.scrollLeft = (this.cardBoxDom.scrollWidth / this.modal.column + this.modal.margin) * this.scrollIndex;
		},
		mousedown(e) {
			this.touchStartX = e.clientX || e.changedTouches[0].clientX;
		},
		mouseup(e, l) {
			if (this.modal.class && (this.modal.class?.includes('hover-change-img'))) return;

			let touchEndX = e.clientX || e.changedTouches[0].clientX;
			if (l.imgList?.length > 1 && Math.abs(touchEndX - this.touchStartX) > 50) {
				if (touchEndX > this.touchStartX) {
					l.imgIndex = l.imgIndex ? l.imgIndex - 1 : l.imgList.length - 1; // 上一张
				} else l.imgIndex = l.imgIndex < l.imgList.length - 1 ? l.imgIndex + 1 : 0; // 下一张

				if (l.imgList[l.imgIndex].value?.endsWith('.mp4')) {
					l.video = l.imgList[l.imgIndex];
					l.img = null;
				} else l.img = l.imgList[l.imgIndex];
				this.$forceUpdate();
			}
		},
		changeTab(t, ti) {
			if (t.url) this.setModalType(null, null, null, t);
			else {
				if (t.column) this.modal.column = Number(t.column);
				if (t.margin) this.modal.margin = Number(t.margin);
				this.tabIndex = ti;
				this.scrollIndex = 0;
				if (this.cardBoxDom) this.cardBoxDom.scrollLeft = 0;
			}
		},
		changeImgs(l, li, pi) {
			if (process.env.isManage) this.setModalType(l.imgList, this.modal.list, 'img_list', l.imgList[pi], li);
			else if (this.modal.class?.includes('hover-change-img')) this.setModalType(null, this.modal.list, '', l.event, li);

			if (!l.imgList[pi] || l.imgList.length < 2) return;

			l.imgIndex = pi;
			this.setEvent(l);

			if (l.imgList[pi].value?.endsWith('.mp4')) {
				l.video = l.imgList[pi];
				l.img = null;
			} else {
				l.img = l.imgList[pi];
				l.video = null;
				// if (l.video) this.videoList.forEach(i => {
				// 	if (i.src == l.video.value && !i.paused) {
				// 		l.video.lastPalyTime = i.currentTime;
				// 		i.pause();
				// 		i.currentTime = 0;
				// 		l.hidePlay = false;
				// 	}
				// })
			}
			if (!this.modal.class?.includes('hover-change-img')) this.$forceUpdate();

			this.$nextTick(() => {
				let listBox = this.cardBoxDom.querySelector(`.card:nth-child(${li + 1}) .list-box`);
				if (listBox) listBox.scrollLeft = Math.max(listBox.scrollWidth / l.imgList.length * (pi - 2), 0);
			})
		}
	}
};
</script>


<style lang="scss" scoped>
.modal-box {
	&[float]:after {
		content: "";
		display: block;
		visibility: hidden;
		clear: both;
	}

	>b[class] {
		font-size: 1.2em;

		&:last-of-type::before {
			transform: rotate(180deg);
		}

		&[disabled] {
			opacity: 0.5;
		}
	}

	.tab-box {
		width: fit-content;
		margin: 0 auto 2em;
		text-align: center;
		border-radius: 5em;
		background-color: #EFEFEF;

		>* {
			height: 2.5rem;
			min-width: auto;
			border-radius: 3em;
			align-items: center;
			padding: 0.5rem 2rem;

			&[primary]:not([outline]) {
				border: none;
				height: 2.5rem;
			}
		}
	}
}

.reviews {
	grid-gap: 0.3em;
	flex-wrap: wrap;
	width: max-content;
	line-height: 1.5em;
	align-items: center;

	>span {
		line-height: 1em;
	}

	.star-box {
		font-size: 0.8em;
		background-clip: text;
		-webkit-background-clip: text;

		b {
			color: transparent;
			margin-right: 0.1vw;
		}
	}

	img {
		width: auto;
		height: 1.1em;
	}
}

.card-box>div {
	flex-wrap: wrap;
	scroll-behavior: smooth;
}

.card {
	overflow: hidden;
	transition: all 0.2s;
	position: relative;
	z-index: 1;

	&[mask] {
		.hover-tag:not(h3) {
			display: none;
		}

		&:hover {
			background: #F1F1F1;
			box-shadow: 0 2px 0.3vw rgba(0, 0, 0, 0.3);

			.hover-tag:not(h3) {
				display: flex;
			}
		}
	}

	.corner-label {
		grid-gap: 0.25em;
		align-items: center;
		padding: 0.3em 1em;
		font-size: calc(1rem - 3px);
		background-size: 100% 100%;
		position: absolute;
		top: 0.75em;

		img {
			width: auto;
			height: 1.75em;
		}

		&[onlyImg] {
			padding: 0;

			img {
				height: 2em;
			}
		}
	}

	&[cornerLabel~='left'] .corner-label {
		left: 1em;
	}

	&[cornerLabel~='right'] .corner-label {
		right: 1em;
	}

	&[cornerLabel~='edge'] {

		.corner-label {
			top: 0;
		}

		&[cornerLabel~='left'] .corner-label {
			left: 0;
		}

		&[cornerLabel~='right'] .corner-label {
			right: 0;
		}
	}

	&[cornerLabel~='out'] {
		overflow: unset;

		.corner-label {
			top: -0.5em;

			&[onlyImg] img {
				width: 4.8em;
				height: auto;
			}
		}

		&[cornerLabel~='left'] .corner-label {
			left: -0.5em;
		}

		&[cornerLabel~='right'] .corner-label {
			right: -0.5em;
		}
	}

	>.index {
		font-size: 8.5em;
		font-weight: bold;
		position: absolute;
		right: 1.5em;
	}

	>img {
		object-fit: contain;
	}

	>.icon {
		font-size: 3em;
		display: inline-block;
	}

	.content {
		flex: 1;
		display: flex;
		grid-gap: 0.5em;
		flex-direction: column;

		// >div:not(.card-btn) {
		// 	flex: 1;
		// }

		.card-text {
			opacity: 0.7;

			&[noExpand] {
				display: none !important;
			}
		}

		.card-price {
			opacity: 0.7;
		}

		.price-list {
			grid-gap: 0.25em;

			label:not(:last-of-type) {
				opacity: 0.7;
				text-decoration: line-through;
			}

			label:last-of-type {
				color: #EB1D07;
				font-weight: bold;
			}
		}
	}

	.img-list {
		display: flex;
		grid-gap: 1vw;
		align-items: center;

		>b {
			color: #888;
			cursor: pointer;
			font-size: 1.5em;
		}

		.list-box {
			grid-gap: 0.5em;

			>* {
				width: 4.2vw;
				height: 4.2vw;
				border-radius: 50%;
				border: 3px solid transparent;
			}
		}

		[sele] {
			border-color: $color-primary;
		}
	}
}

.point-box {
	order: 5;
	margin-top: 1em;
	justify-content: center;

	div {
		width: 0.5em;
		height: 0.5em;
		margin: 0 1.1vw;
		border-radius: 1em;
		background: #CCCCCC;

		&.select {
			background: #A6A6A6;
		}
	}
}

.btn-box {
	margin-top: 2.5em;
	text-align: center;
}

.search-box {
	display: flex;
	height: 2.5em;
	padding: 0 1em;
	width: 20.125em;
	align-items: center;
	border-radius: 1.25em;
	border: 1px solid #CBCBCB;

	input {
		flex: 1;
		font-size: calc(1em - 2px);
	}
}


[theme='4'] .card>.icon {
	color: $color-primary;
}

[theme]:not([theme='4']) .point-box .select {
	width: 1.3em;
	background: $color-primary;
}



.hover-big .card:hover {
	transform: scale(1.03);
}


.hover-bigger .card:hover {
	transform: scale(2);
	z-index: 2;
}


.hover-img-big .card>img:hover {
	transform: scale(1.05);
}


.hover-img-bigger .card {
	&:hover {
		z-index: 2;
	}

	img:hover,
	.video-box:hover {
		transform: scale(1.5);
	}
}


.hover-img-show .card {
	&:nth-child(even) {
		background-color: #F9F9F9;
	}

	&:hover {
		background-color: #FFF5F1;

		>img {
			display: block !important;
		}
	}
}


.hover-subtitle-show .card:not(:hover) .card-subtitle {
	display: none;
}


.hover-content .card {
	.content {
		width: 100%;
		padding: 1.2vw;
		text-align: center;
		position: absolute;
		z-index: 1;
		bottom: 0;

		&::before {
			content: "";
			width: 100%;
			height: 100%;
			opacity: 0.9;
			background-image: linear-gradient(90deg, $color-primary -10%, $btn-primary 140%);
			position: absolute;
			z-index: -1;
			left: 0;
			top: 0;
		}

		button {
			border: none;
			margin-top: 1.5em;
			font-weight: bold;
			background: white;
			color: $text-primary;
		}
	}

	&:not(:hover) {

		.card-subtitle,
		button {
			display: none;
		}
	}
}


.hover-content-mask .card:hover .content {
	background: rgba(0, 0, 0, 0.31);
}


.hover-content-gray .card:hover .content {
	background: #D0D0D0 !important;
}


.hover-card-btn-flex .card:hover .card-btn {
	display: flex !important;
}


.hover-card-flex2 .card:hover {
	flex: 2 !important;
	min-width: fit-content !important;
}


.hover-content-align .card:not(:hover) {
	text-align: center;
}


.hover-color-second .card:hover {
	background: $color-second !important;
}


.hover-lapel-pins-color .card:hover {
	background: #E4E7EF !important;
	box-shadow: 0 0 10px 0 #c9c9c9 !important;
}


.hover-color-white .card:hover {
	background: white !important;
}


.hover-border .card:hover {
	border: 1px solid var(--color-second);
}


.hover-border-second .card:hover {
	box-shadow: 0px 0 10px 0px rgba(0, 0, 0, 0.11);
	border: 1px solid var(--color-second) !important;
}


.hover-border-primary .card:hover {
	border-color: var(--btn-primary) !important;
}


.hover-font-white .card:hover {
	color: white;
}


.hover-show-text-price .card:not(:hover) {

	.card-text,
	.card-price,
	.price-list {
		display: none !important;
	}
}


.hover-text-btn .card:not(.hover-type) {
	&:hover .card-text {
		display: none;
	}

	&:not(:hover) .card-btn {
		display: none;
	}
}


.hover-title-btn .card:hover {
	.card-title {
		color: $color-primary;
	}

	button {
		color: white;
		transform: scale(1.1);
		background: radial-gradient($color-primary 66%, transparent 0);
	}
}

.hover-cardtitle-color .card:not(:first-child) .content:hover {
	background-color: #fff;
	color: #3c6987
}


.hover-btn-icon-value .card {
	button {
		position: relative;

		label {
			width: 20em;
			padding: 10px;
			transform: translateX(-50%);
			border: 1px solid var(--color-primary-lighten);
			background: white;
			position: absolute;
			left: 50%;
			top: 2.2em;
			text-align: left;
			border-radius: 8px;
		}

		b {
			padding: 0 4em;
			color: var(--color-primary-lighten);

			&::after {
				content: "";
				width: .75em;
				height: .75em;
				background-color: #fff;
				position: absolute;
				top: 1.86em;
				left: 50%;
				transform: translateX(-50%);
				border-top: 1px solid var(--color-primary-lighten);
				transform: translateX(-50%) rotate(45deg);
				border-left: 1px solid var(--color-primary-lighten);
			}
		}

		&:not(:hover) {
			label {
				display: none;
			}

			b::after {
				display: none;
			}
		}
	}

	&:hover {
		z-index: 5;
	}
}


.hover-btn-icon-value2 .card {
	.price-list {
		display: grid !important;

		&::after {
			content: "";
			width: 0.5em;
			height: 0.5em;
			background-color: #EEEEEE;
			position: absolute;
			bottom: -0.32em;
			left: 10%;
			transform: translateX(-50%);
			border-top: 1px solid #333;
			border-left: 1px solid #333;
			transform: rotate(225deg);
		}
	}

	.price-list:has(~ .card-text:not(:hover)) {
		display: none !important;
	}
}


.hover-btn-gray .card:not(:hover) button {
	border: none;
	color: #333;
	box-shadow: none;
	background: #F7F7F7 !important
}


.hover-btn-outline .card:not(:hover) button[primary]:first-child {
	box-shadow: none;
	background: none !important;
	color: var(--btn-primary) !important;
	border: 1px solid !important;
}


.hover-btn-outline-black .card:not(:hover) button:first-child {
	box-shadow: none;
	background: none !important;
	color: #333 !important;
	border: 1px solid !important;
}


.hover-btn-outline-gray .card:not(:hover) button:first-child {
	box-shadow: none;
	background: none !important;
	color: #333 !important;
	border: 1px solid #ccc !important;
}


.hover-change-img .card .content .img-list {
	order: -1;
	width: 100%;

	>b {
		display: none;
	}

	.list-box {
		width: 100%;

		>* {
			width: 100%;
			height: unset;
			border: unset;
			border-radius: unset;
		}
	}
}


.hover-card-shadow .card:hover {
	box-shadow: var(--box-shadow, 0px 1px 9px 0px rgba(0, 0, 0, 0.25));
}


.hover-card-shadow-inset-primary .card:hover {
	box-shadow: inset 0 0 1px 1px $color-primary, inset 0px -0.5em 0.5em -0.5em $color-primary;
}


.hover-border-pink .card:hover {
	border: 1px #F4B7B1 solid !important;
}


.hover-img-radius-shadow-primary .card:hover img {
	border-radius: 1em;
	box-shadow: 0 2px 10px -2px var(--color-primary);
}


.hover-color-line-title-btn .card:not(:hover) {
	color: inherit !important;
	font-weight: 400 !important;
	border-color: #E7E7E7 !important;

	[primary] {
		color: inherit;
		background: none;
		border: 1px solid;
	}
}


.hover-border .card-box .card>img {
	border: 2px solid transparent;

	&:hover {
		border-color: var(--btn-primary);
	}
}


.hover-arrow-primary>b:hover {
	color: white;
	background: $color-primary;
}


.hover-details-show .card:hover {
	.content {
		background-image: linear-gradient(rgba(41, 24, 6, .5) calc(100% - 9.15em), #fff calc(100% - 9.15em));
	}

	.card-subtitle {
		display: block !important;
	}

	.price-list {
		display: grid !important;
	}
}


.hover-bg-white .card:hover {
	background-color: #FFF !important;
}


.hover-img-bg-transparent .card:hover img {
	background-color: transparent !important;
}


.hover-linear-btn .card:hover button {
	color: #fff;
	background: linear-gradient(to right, var(--color-bright), var(--btn-primary));
}


.hover-bg-pink .card:hover {
	background: #FFF6F3;
	border-color: #FFDBCF !important;
}


.hover-img-background .card {
	&:hover {
		>img {
			background-image: linear-gradient(to top, #FFEEE2 calc(100% - 4.6em), #F3F3F3 calc(100% - 4.6em)) !important;
		}

		button {
			color: $color-primary !important;
		}
	}

	.corner-label {
		padding: 0.2em 0.6em;

		img {
			height: 1.08em;
		}
	}
}


.hover-content-flex .card:hover .content {
	display: flex !important;
}


.mini-expand .card-text[noExpand] {
	height: 2.75em !important;
	display: none !important;
}


.h2-line h2 {
	gap: 2vw;
	display: flex;
	align-items: center;
	justify-content: center;

	&::before,
	&::after {
		all: unset;
		content: "";
		flex: 1;
		border-top: 1px solid #DADADA;
	}
}


.transform-autoChange-imgList .card {
	@keyframes rotateAni {
		0% {
			transform: rotateY(0deg);
		}

		50% {
			transform: rotateY(180deg);
		}

		100% {
			transform: rotateY(360deg);
		}
	}

	@keyframes changeZIndex1 {
		0% {
			z-index: 2;
		}

		50% {
			z-index: 1;
		}

		100% {
			z-index: 2;
		}
	}

	@keyframes changeZIndex2 {
		0% {
			z-index: 1;

		}

		50% {
			z-index: 2;
		}

		100% {
			z-index: 1;
		}
	}

	.list-box {
		position: relative;
		animation-name: rotateAni;
		animation-duration: 6s;
		animation-timing-function: ease;
		animation-delay: 0s;
		animation-iteration-count: infinite;
		animation-direction: normal;
		animation-play-state: running;
		animation-fill-mode: none;
	}

	.list-box>*:first-child {
		position: absolute;
		z-index: 2;
		left: 0;
		top: 0;
		// background-color: #FFF;
		animation-name: changeZIndex1;
		animation-duration: 6s;
		animation-timing-function: ease;
		animation-delay: 0s;
		animation-iteration-count: infinite;
		animation-direction: normal;
		animation-play-state: running;
		animation-fill-mode: none;
	}

	.list-box>*:last-child {
		position: absolute;
		z-index: 1;
		left: 0;
		top: 0;
		// background-color: #FFF;
		transform: rotateY(180deg);
		animation-name: changeZIndex2;
		animation-duration: 6s;
		animation-timing-function: ease;
		animation-delay: 0s;
		animation-iteration-count: infinite;
		animation-direction: normal;
		animation-play-state: running;
		animation-fill-mode: none;
	}

	.content .img-list {
		order: -1;
		width: 100%;

		>b {
			display: none;
		}

		.list-box {
			width: 100%;

			>* {
				width: 100%;
				height: 100%;
				object-fit: contain;
				border: unset;
				border-radius: unset;
			}
		}
	}
}


.modal-box.h2-bg h2 {
	display: flex;
	align-items: flex-start;
	justify-content: center;
	column-gap: 0.45em;

	&::before,
	&::after {
		all: unset;
		content: "";
		display: block;
		width: 6.6em;
		height: 1em;
		background-size: contain;
		background-repeat: no-repeat;
	}

	&::before {
		background-image: var(--h2-bg-before, url('https://static-oss.gs-souvenir.com/web/quoteManage/20250304/Personalized_Cufflinks_for_Groom_20250304BD4bSR.png'));
	}

	&::after {
		background-image: var(--h2-bg-after, url('https://static-oss.gs-souvenir.com/web/quoteManage/20250304/Personalised_Cufflinks_Wedding_20250304jbdyWY.png'));
	}
}


.tab-line-bottom .tab-box [primary] {
	position: relative;

	&::after {
		content: '';
		width: 44%;
		border-bottom: 0.25em solid;
		position: absolute;
		bottom: 0;
	}
}


.tab-line-bottom2 .tab-box [primary] {
	&::after {
		width: 100%;
		border-bottom: 0.125em solid;
	}
}


.patches .card-box .card {
	background: url(https://sticker-static.oss-accelerate.aliyuncs.com/image/uploads/20230509/product_bg.png) no-repeat;

	&:nth-child(odd) .card-title {
		padding-left: 4vw;
	}

	&:nth-child(even) {
		background-image: url(https://sticker-static.oss-accelerate.aliyuncs.com/image/uploads/20230509/product_bg_even.png);

		>img {
			order: 1;
		}
	}

	.content {
		display: grid;
		grid-template-columns: auto 1fr;

		>*:not(.card-text):not(.card-price) {
			grid-column-start: span 2;
		}

		.img-list .list-box {
			max-width: 25em;
			padding: 0 0.2em 0.2em;

			img {
				box-shadow: 2px 1px 3px rgba(79, 111, 101, 0.3);

				&:not([sele]) {
					border-color: rgba(79, 111, 101, 0.4);
				}
			}
		}
	}
}


.detail-light .card-box .card:nth-child(odd)>img {
	order: 1;
}


.short-line .card-box .card {
	position: relative;

	.content::after {
		position: absolute;
		right: 0;
		top: 1em;
		content: "";
		height: 4.5em;
		width: 1px;
		background-color: #C2C2C2;
	}

	&:last-child .content::after {
		display: none;
	}
}


.story-button .card {
	button {
		display: none;
	}

	&:hover button {
		display: block;
	}
}


.view-more .btn-box {
	display: flex;
	margin-top: 3.125em;
	align-items: center;
	justify-content: center;

	&::before,
	&::after {
		content: "";
		border-top: 1px solid #DADADA;
		flex: 1;
	}

	button {
		height: 2.5em;
		margin: 0 1vw;
		padding: 0 6.2vw;
		line-height: 2.5em;
		font-size: 1em;
		text-align: center;
		background: #F1F3F4;
		color: #333333;
		border-radius: 1.25em;

		b {
			font-size: 12px;
			color: #70757A;
		}

	}
}


.noword-button .card-box .content>.card-btn {
	justify-content: center;
	display: flex;
	align-items: center;
	margin-top: 2em;

	&::before,
	&::after {
		content: "";
		height: 10px;
		width: 10px;
		background-color: #adadad;
		border-radius: 5px;
	}

	button {
		margin: 0 10px;
		height: 10px;
		width: 28px;
		padding: 0;
		font-size: 0;
		border-radius: 5px;
		background-color: #01487B
	}
}


.marquee-img-list .card-box .card {
	&:nth-child(odd) .list-box {
		animation: marqueeY 50s linear infinite;
	}

	&:nth-child(even) .list-box {
		animation: marqueeY 50s linear infinite reverse;
	}

	.img-list {
		height: 43.8em;

		>b {
			display: none;
		}

		.list-box {
			cursor: inherit;
			overflow: hidden;
			flex-direction: column;

			>* {
				margin: 0;
				width: 100%;
				height: auto;
				border: none;
				border-radius: 0;
				box-shadow: none;
				margin-bottom: 0.5vw;
			}
		}
	}
}


.teaCultureCard {
	.sub-title {
		position: relative;

		&::before,
		&::after {
			content: '\e6a9';
			position: absolute;
			font-family: "modalicon" !important;
			display: inline-block;
			left: -1em;
			top: -.2em;
			font-size: 3em;
			color: #c2a885;
		}

		&::after {
			content: "\e6d9";
			left: auto;
			top: auto;
			right: -1em;
			bottom: 0;
			transform: translateY(50%);
		}
	}

	.card:nth-child(even) {
		flex-direction: row-reverse;
	}
}


.navTab [nowPage] {
	background: rgba($color: #1B1F1D, $alpha: 0.7);
	box-shadow: inset 0 -2px 0 var(--color-primary-opacity);
	position: relative;

	img {
		box-shadow: 0 0 0 1px $color-primary;
	}

	&::after {
		content: '';
		width: 12px;
		height: 12px;
		background: $color-primary;
		transform: rotate(45deg);
		position: absolute;
		left: calc(50% - 5px);
		bottom: -4px;
	}
}


.east-tea-type .card:not(.card:first-of-type):hover {
	img {
		border-radius: 50%;
		border: 2px solid #1A7333 !important;
	}

	.content button {
		background-image: url(https://sticker-static.oss-accelerate.aliyuncs.com/image/uploads/20231116/东极茶叶选中边框.png) !important;
	}

	.content button label {
		color: #1A7333;
	}
}


.dongji-hover-mask .card-box .card:hover img {
	filter: brightness(0.4) contrast(.9);
}

.gsjj-hover-mask .card-box .card:hover img {
	background-color: #C1C1C1;
	filter: brightness(.7) contrast(.7);
}

.gsjj-hover-mask-opacity .card-box .card:hover {
	img {
		background-color: #302F2F;
		filter: brightness(.2) contrast(.7);
	}

	.card-title {
		opacity: 0.2;
	}

	.card-subtitle {
		opacity: 0.2;
	}

	.card-btn {
		display: flex !important;
	}
}


.subTitleBefo .sub-title::before {
	content: "";
	display: inline-block;
	width: 0.2em;
	height: 0.7em;
	border-radius: 0.15em;
	background: #F44E3D;
}


.malitary-line {
	.btn-box {
		button:hover {
			color: var(--color-primary) !important;
			transform: none;
			text-decoration: underline var(--color-primary) 2px !important;
		}
	}

	&>div h3 {
		display: flex;
		justify-content: space-between;
		align-items: center;
		justify-content: center;
		column-gap: .8em;

		&::before,
		&::after {
			content: "";
			width: 27.6vw;
			height: 1px;
			background: #585144;
		}
	}
}


.malitary-link button:hover {
	color: var(--color-primary);
	transform: none;
	text-decoration: underline var(--color-primary) 2px !important;
}


.button-hover-underline .card-btn button:hover {
	text-decoration: underline !important;
}


.malitary-tab .tab-box {
	height: 2.81em;
	margin-bottom: 2.6em;
	align-items: center;
	column-gap: 7.75vw;
	padding: 0 3.19em;

	h3 {
		height: 1em;
		padding: 0;
		line-height: 1em;
		font-size: 1.125em;
		border-radius: 0;
		font-weight: 400;
		background: none !important;
	}

	[primary] {
		border-bottom: 2px solid var(--color-primary) !important;
		color: var(--color-primary);
		font-weight: bold;
	}
}


.hollowing-out {
	.point-box {
		gap: 0 1.2em;
		padding: 0 1.5em;
		align-items: center;

		div {
			width: 0.625em;
			height: 0.625em;
			background: #949494;
			margin: 0;

			&.select {
				width: 1.125em !important;
				height: 1.125em !important;
				background: #6B29FD !important;
				opacity: .65;
				display: flex;
				align-items: center;
				justify-content: center;

				&::after {
					content: "";
					width: 0.625em;
					height: 0.625em;
					border-radius: .5em;
					background: #fff;
				}
			}
		}
	}

	>b:hover {
		color: #fff !important;
		background: linear-gradient(90deg, #20AEFF, #B61EE8) !important;
		border: none !important;
	}
}


.clickOpenDialog .video-box {
	.icon-jxsht-3d-dbf {
		display: none;
	}

	.icon-a-tgsc-add::before {
		content: "";
	}

	.icon-a-tgsc-add {
		opacity: .8;
		font-size: 2em;
		text-align: center;
		transform: translate(-50%, -50%);
		left: 50%;
		top: 50%;
		width: 5.375rem;
		height: 5.375rem;
		background: url(https://static-oss.gs-souvenir.com/web/20241111/2046363451icon_play.png) no-repeat;
		background-size: 100%;
	}
}


.expand-height .card .card-text[noExpand] {
	max-height: 2.6em;
	overflow: hidden;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	display: -webkit-box !important;
}


.sale-white .card .img-list [sele] {
	border-color: #fff;
}


.img-list-selected-border .card .img-list [sele] {
	border-color: var(--color-primary) !important;
}


.cufflinks-img-list-selected-border .card .img-list [sele] {
	box-shadow: 0 2px 10px 0 rgba(214, 65, 22, 0.3) !important;
	border-color: #FF5122 !important;
}


.sub-title-short-line .sub-title {
	display: flex;
	align-items: center;
	justify-content: center;

	&::before,
	&::after {
		content: "";
		margin: 0 0.625rem;
		width: 1.125rem;
		height: 3px;
		background-color: black;
	}
}


.gs-jj-stripe-table .card {
	&:nth-child(even) .price-list {
		background-color: var(--stripe-color, #F2F2F2) !important;
	}

	.price-list {
		>*:first-child {
			position: sticky;
			left: 0;
			z-index: 1;
			background-color: inherit;
		}

		label:not(:last-of-type) {
			opacity: unset;
			text-decoration: unset;
		}

		label:last-of-type {
			color: unset;
			font-weight: unset;
		}
	}
}

.even-row-reverse {
	.card-box>div>.card:nth-of-type(odd) {
		flex-direction: row-reverse;
	}
}

.even-text-left {
	.card-box>div>.card:nth-of-type(odd)>.content>.card-text {
		left: initial;
		right: 25%;
	}

	.card-box>div>.card:nth-of-type(even)>.content>.card-text {
		left: 25%;
		right: initial;
	}

	.card-box>div>.card:last-of-type {
		border-bottom: initial !important;

		&>.content>.card-text {
			display: none;
		}
	}
}



// pc跟ipad端样式
@media screen and (min-width: $mb-width) {
	[scrollbar]::-webkit-scrollbar {
		width: 0;
		height: 0;
	}

	.showScrollbar [scrollbar]::-webkit-scrollbar {
		height: 4px;
	}


	.hover-bg .card-box .card {

		button:nth-child(2) {
			background: none;
			color: inherit;
		}

		&:hover {
			background-image: linear-gradient(90deg, var(--color-primary), var(--btn-primary) 130%);

			.content {
				.card-title {
					color: #FFF;
				}

				button:nth-child(1) {
					border: none;
					background: #FFF;
					color: #333;
				}

				button:nth-child(2) {
					border-image: none;
					border: 2px solid #FFF;
					color: #FFF;
				}
			}
		}
	}


	.hover-show-btn .card:not(:hover) .content>.card-btn {
		display: none !important;
	}


	.hover-change-img .card {
		&:hover .list-box>*:first-child {
			display: none !important;
		}

		&:not(:hover) .list-box>*:last-child {
			display: none !important;
		}
	}


	.tea-hover .card {
		.content {
			&::before {
				background-image: none
			}
		}

		&:hover img {
			filter: brightness(0.3) contrast(0.5);
		}
	}


	.tea-pseudoelement .card {
		overflow: initial !important;

		&:not(:hover) {
			.content {
				background-image: none;

				&::after {
					background: none;
				}
			}

			.card-text,
			button {
				display: none;
			}
		}

		.content {
			width: 95.6%;
			height: 100%;
			text-align: center;
			position: absolute;
			z-index: 1;
			top: 0;
			right: 0;
			bottom: 0;
			left: 0;
			margin: auto auto 1.8em;
			background-image: linear-gradient(to bottom, transparent 65%, $bg-dark 65%);
			opacity: 0.95;
			border-radius: 0 0 1.3em 1.3em;

			&::after {
				content: "";
				width: 93%;
				height: 3em;
				position: absolute;
				bottom: -1.1em;
				background: $bg-dark;
				margin-left: 3.5%;
				border-radius: 0 0 1.5em 1.5em;
				z-index: -1;
			}
		}
	}
}







// ipad端样式
@media screen and (max-width: $pad-width) {
	.tea-hover .card {
		flex-direction: column;

		.content {
			overflow-y: auto;
		}
	}
}







@media screen and (max-width: $mb-width) {
	.modal-box {
		//padding-left: 0;
		//padding-right: 0;

		.tab-box {
			width: 90vw;
			background: none;
			grid-gap: 0.8em 0.5em;
			justify-content: center;

			>* {
				padding: 0.6em 1.5em;

				&:not([primary]) {
					background-color: #EFEFEF;
				}
			}
		}
	}

	.btn-box {
		margin-top: 1.5em;
	}


	.hover-btn-icon-value .card {
		&:nth-child(even) button label {
			transform: none;
			left: -16.5em;
		}

		button b {
			padding: 0 2em;
		}
	}


	.hover-change-img .card,
	.woven-labels .card {
		&:not(:hover) .list-box>*:first-child {
			display: none !important;
		}

		&:hover .list-box>*:last-child {
			display: none !important;
		}
	}


	.patches .card-box .card {
		background-image: url(https://sticker-static.oss-accelerate.aliyuncs.com/image/uploads/20230723/background-1.png);

		.img-list {
			grid-gap: 0.5em;
			position: absolute;
			transform: translateX(-50%);
			bottom: 6em;
			left: 50%;

			>b {
				display: none;
			}

			img {
				width: 0;
				height: 0;
				border-width: 4px;

				&:not([sele]) {
					border-color: #aaa;
				}
			}
		}

		&:nth-child(even) {
			background-image: url(https://sticker-static.oss-accelerate.aliyuncs.com/image/uploads/20230723/background-2.png);

			.card-title {
				text-align: right;
			}
		}
	}


	.waterfall {
		.card-box {
			>div::before {
				content: '';
				display: block;
				grid-row: auto / span 2;
				margin-bottom: 75%;
			}
		}

		.btn-box {
			width: 48%;
			position: absolute;
			bottom: 5em;
		}

		&[even] .btn-box {
			left: 50%;
		}
	}


	.waterfall2 {
		.card-box {
			>div::before {
				content: '';
				display: block;
				grid-row: auto / span 1;
				//margin-bottom: 35%;
				margin-bottom: var(--mb-waterfall2, 35%);
			}
		}

		.btn-box {
			width: 48%;
			position: absolute;
			bottom: 5em;
		}

		&[even] .btn-box {
			left: 50%;
		}
	}


	.detail-light .card-box .card {
		&:nth-child(even) {
			border-radius: 0 5px 5px 0 !important;
		}

		&:nth-child(odd) {
			border-radius: 5px 0 0 5px !important;
		}
	}


	.tea-hover .card {
		flex-direction: column;

		.content {
			position: initial;
			overflow-y: initial;

			&::before {
				background-image: none
			}
		}
	}


	.tea-pseudoelement .card .content {
		width: 100%;
		height: 100%;
		display: flex;
		flex-direction: column;

		.card-title {
			margin: 0 1em !important;
		}

		.card-subtitle {
			flex: 1;
		}

		.card-text {
			box-shadow: -5px 147px 249px 31px rgba(42, 42, 42, 0.1);
			background: rgba(44, 44, 44, 0.6);
			// border-radius: 5px;
		}

		.arrow {
			font-size: 2em;
			transform: none;
			color: #2C2C2C;
			text-align: right;
			margin: 0 0.5em;
			z-index: 1;

			&::before {
				border-radius: 50%;
				background: radial-gradient(white 30%, transparent 30%);
			}
		}
	}


	.short-line .card-box .card {
		&:nth-child(3) .content::after {
			display: none;
		}

		.content::after {
			top: .5em;
			height: 3.1em;
		}
	}


	.view-more .btn-box {
		margin-top: 2.5em;

		button {
			height: 2.08em;
			margin: 0;
			padding: 0 10vw;
			border-radius: 1.04em;

			b {
				font-size: 9px;
			}
		}
	}


	.noword-button .card-box .content>.card-btn {
		margin-top: 1.4em
	}


	.marquee-img-list .card-box .card {
		&:nth-of-type(-n+2) {
			height: 77.87vw;
		}

		>* {
			margin-bottom: 1.4vw;
		}
	}


	.navTab [nowPage] {
		box-shadow: none;

		&::after {
			all: unset;
			content: '\e7c1';
			font-family: "modalicon";
			position: absolute;
			bottom: -0.3vw;
		}
	}


	.malitary-line>div h3::before,
	.malitary-line>div h3::after {
		width: 7.8vw;
	}


	.malitary-tab .tab-box {
		height: 2.5em;
		padding: .75em 1.58em 0;
		margin-bottom: 2.2em;
		column-gap: 0;
		align-items: normal;
		background: #EBEBEB;

		h3 {
			height: 1.85em;
			width: fit-content;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: space-between;
			height: auto;
			font-size: 1em;
			border-bottom: none;
		}

		[primary] {
			border: none !important;
		}

		[primary]::after {
			content: "";
			width: 2.92em;
			height: 2px;
			background-color: var(--color-primary);
		}
	}

	.category-page {
		&.mask-content {
			width: 95vw;
		}

		>b {
			color: #fff;
		}

		.card-box>div {
			display: grid;
			grid-template-columns: repeat(2, 1fr);
			gap: 1em !important;
			padding-top: 2.5em !important;
			padding-bottom: 2.5em !important;

			.card {
				width: 100% !important;
			}
		}
	}

	.hover-bigger .card:hover {
		transform: none;
	}


	.pvc-keychains .card:nth-child(even) {

		.card-title,
		.card-text,
		.card-btn button {
			left: auto !important;
			right: 53%;
			text-align: right
		}
	}


	.hover-details-show .card:hover>b {
		transform: rotate(180deg);
	}



	.subTitleBefo .sub-title::before {
		width: 0.28em;
	}

	.hover-img-background .card:hover {
		>img {
			background-image: linear-gradient(to top, #FFEEE2 calc(100% - 5.3em), #F3F3F3 calc(100% - 5.3em)) !important;
		}
	}
}
</style>
