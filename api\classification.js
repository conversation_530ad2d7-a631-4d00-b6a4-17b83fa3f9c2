import {request} from "@/utils/request";

//获取模板列表
// export function getNeonTemplate(data) {
// 	return request({
// 		url: "/quote/params/getNeonTemplate",
// 		method: "get",
// 		params:data
// 	});
// }

//获取模板列表
export function getTemplates(data) {
	return request({
		url: "/quote/templates/getTemplates",
		method: "post",
		data
	});
}

//获取父子分类
export function getAppParamType(data) {
	return request({
		url: "/quote/templates/getAppParamType",
		method: "get",
		params:data
	});
}