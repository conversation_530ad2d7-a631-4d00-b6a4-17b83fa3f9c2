<template>
    <base-dialog :value="signUpDialog" @update="update" :width="device !== 'mb' ? '510px' : '90%'">
        <div class="design_dialog">
            <img src="https://oss-static-cn.liyi.co/web/quoteManage/20230821/icon-_Quit_20230821KB4RGW.png" width="182px"
                height="153px" />
            <p class="pp">{{ lang.areQuit }}</p>
            <div>{{ lang.designLost }}</div>
            <el-button type="primary" class="closeBtn" @click="leaveClick">{{ lang.leaveNow }}</el-button><br />
            <el-button class="closeBtn" @click="$store.commit('setLogin', 'login')">{{ lang.signUp }}</el-button>
        </div>
    </base-dialog>
</template>

<script>
import BaseDialog from "~/components/Quote/BaseDialog.vue";
export default {
    props: ['signUpDialog'],
    data() {
        return {

        }
    },
    computed: {
        device() {
            return this.$store.state.device;
        },
		lang() {
            return this.$store.getters.lang?.design;
        },
    },
    components: {
        BaseDialog
    },
    methods: {
        leaveClick() {
            this.$router.push({
                path: "/"
            })
        },

        update(val) {
            this.$emit('update:signUpDialog', val);
        },
    }

}
</script>

<style scoped lang="scss">
.design_dialog {
	text-align: center;
	padding: 30px;

	.close {
		text-align: right;
		padding: 0 10px 20px;

		b {
			font-weight: bold;
			font-size: 11px;
		}
	}

	.pp {
		font-size: 18px;
		font-family: Calibri;
		font-weight: bold;
		margin: 0;
	}


	.el-button {
		width: 180px;
		height: 40px;
		font-size: 16px;
		font-family: Calibri;
		font-weight: bold;
	}

	img {
		object-fit: contain;
	}

	.closeBtn {
		width: auto;
		margin-top: 15px;
	}

	@include respond-to(mb) {
		.pp {
			margin-bottom: 2.6667vw;
		}

		.closeBtn {
			margin-top: 1.2vw;
		}

		div {
			margin-bottom: 19px;
		}
	}
}
</style>