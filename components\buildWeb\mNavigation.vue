<template>
	<v-row style="height:100%;">
	  <v-col v-if="$store.getters.getBuildWebLoading" style="height:100%;" cols="12" md="12">
		<v-skeleton-loader v-bind="{ class: 'ma-3' }" type="paragraph,card"></v-skeleton-loader>
	  </v-col>
	  <v-col v-else style="height:100%;" cols="12" md="12">
		<v-navigation-drawer class="mNavigation pa-2" v-model="dialog1" :hide-overlay="true" right permanent width="100%">
			<!-- 解决需要点击触发的问题 -->
			<input type="file" id="uploads" ref="uploads" style="position: absolute; clip: rect(0 0 0 0)"
			@change="getFile($event)" :accept="computAccept" />
			<input style="display:none" type="file" id="uploads11" ref="uploads1" @change="getFile1($event)" accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel">
		  <v-card>
			<v-card flat>
			  <v-window :value="panels" class="elevation-1" v-if="panels">

				<v-window-item :value="isVideoImage" eager>
				  <v-card-text>
					<b class="mr-3 ml-1 icon-a-lrgl-imagezhuanhuan"></b>
					<span class="text-h7">Change Image/Video</span>
				  </v-card-text>
				  <v-card-text class="pt-0" v-if="modalType.customData">
					<p v-if="!manageMobile">Alt&Title</p>
					<v-text-field class="mb-3" v-if="!manageMobile" label="" v-model="modalType.customData.alt" outlined
					  dense hide-details></v-text-field>
					<p v-if="itemData.sampleData.cardImgStyle && !manageMobile">Object Fit</p>
					<v-select v-if="itemData.sampleData.cardImgStyle && !manageMobile" dense :items="imgItems"
					  v-model="itemData.sampleData.cardImgStyle['object-fit']" outlined></v-select>
					<section v-if="!manageMobile && checkFileType(modalType.customData.value) =='img'">
						<p >What do you want to link to?</p>
						<v-radio-group  v-model="modalType.customData.type" row dense
						class="myRadio mb-3 mt-1" hide-details>
							<div class="text-center">
								<v-radio v-for="(item, index) in btnUrl" :key="index" color="indigo" :label="item.label"
								:value="item.value" @change="$forceUpdate()"></v-radio>
							</div>
						</v-radio-group>
						<v-card flat >
							<v-list>
								<v-list-item class="px-0">
								<v-list-item-content>
									<v-row>
									<template v-if="modalType.customData.type == 'link'">
										<v-col :cols="12">Which Link?</v-col>
										<v-col :cols="12" class="pt-0">
										<v-combobox dense hide-details v-model.trim="modalType.customData.url" item-value="url"
											@input.native="modalType.customData.url = $event.target.value; hideDropdown = true;"
											:menu-props="{ ...(hideDropdown && { value: false }) }"
											@click="hideDropdown = !hideDropdown" item-text="pageName" :items="productData"
											:return-object="false">
										</v-combobox>
										</v-col>
									</template>
									<template v-else-if="modalType.customData.type == 'click'">
										<v-col :cols="12">Which Dialog?</v-col>
										<v-col :cols="12" class="pt-0 px-1">
										<v-card class="mx-1 mb-2 pa-2" flat :loading="loadDialog">
											<template slot="progress">
												<v-progress-linear color="deep-purple" height="10" indeterminate></v-progress-linear>
											</template>
											<v-select
												:value="dialogParam.type"
												:items="dialogType"
												label="Which DialogType?"
												:menu-props="{ 'content-class': 'templeteTagList' }"
												@change="changeDialogType"
											></v-select>
											<v-card-text>
												<v-row v-if="dialogParam.type ==1">
													<v-col cols="12" sm="12" style="padding-top:0px;padding-bottom:0px;">
														<v-combobox
															v-model="dialogParam.quoteUrl"
															:return-object="false"
															:items="dialogQuoteUrlList"
															:disabled="loadDialog"
															label="Change QuoteUrl"
															item-text="name"
															item-value="routingName"
														></v-combobox>
													</v-col>
													<v-col cols="12" sm="12" style="padding-top:0px;padding-bottom:0px;">
														<v-text-field label="quoteIframe" placeholder = "Format:?type=quoteIframe&id=1" v-model="dialogParam.OtherParams"></v-text-field>
													</v-col>
												</v-row>
												<v-row v-else-if="dialogParam.type ==2">
													<v-col cols="12" sm="12" style="padding-top:0px;padding-bottom:0px;">
														<v-combobox
															v-model="dialogParam.quoteUrl"
															:return-object="false"
															:disabled="loadDialog"
															:items="dialogQuoteUrlList"
															label="Change QuoteUrl"
															item-text="name"
															item-value="routingName"
														></v-combobox>
													</v-col>
												</v-row>
												<v-row v-else-if="dialogParam.type ==3">
													<v-col cols="12" sm="12" style="padding-top:0px;padding-bottom:0px;">
														<v-combobox
															v-model="dialogParam.parentCateId"
															:return-object="false"
															:items="parentQuoteUrlList"
															label="Change parentCateId"
															:disabled="loadDialog"
															item-text="name"
															item-value="id"
															@change="getAllQuoteSemiCateProductListByType()"
														></v-combobox>
													</v-col>
													<v-col cols="12" sm="12" style="padding-top:0px;padding-bottom:0px;">
														<v-combobox
															v-model="dialogParam.quoteUrl"
															:return-object="false"
															:items="dialogQuoteUrlList"
															:disabled="!dialogParam.parentCateId||loadDialog"
															label="Change QuoteUrl"
															item-text="name"
															item-value="routingName"
														></v-combobox>
													</v-col>
												</v-row>
												<div v-else>
													<v-row v-for="(item, index) in tempList" :key="index">
														<v-col cols="12" sm="6" style="padding-top:0px;padding-bottom:0px;"><v-text-field
															placeholder="key" v-model="item[0]" :disabled="item[0] == 'type'"></v-text-field></v-col>
															<!-- 禁止修改Type -->
														<v-col cols="12" sm="6" style="padding-top:0px;padding-bottom:0px;"><v-text-field
															placeholder="value" v-model="item[1]" :disabled="item[0] == 'type'"></v-text-field></v-col>
													</v-row>
												</div>
											</v-card-text>
											<div class="text-right">
											<v-btn v-if="dialogParam.type == 4" depressed x-small class="mt-1" color="blue" min-width="78" min-height="23px"
												@click="AddPropFn">
												Add
											</v-btn>
											<v-btn depressed x-small class="mt-1" color="success" min-width="78"
												min-height="23px" @click="SavePropFn">
												Save
											</v-btn>
											</div>
										</v-card>
										</v-col>
									</template>
									</v-row>
								</v-list-item-content>
								</v-list-item>
							</v-list>
						</v-card>
					</section>
					<p v-if="!manageMobile">Video Poster</p>
					<v-text-field class="mb-3" v-if="!manageMobile" label="" v-model="modalType.customData.poster" outlined
					dense hide-details></v-text-field>
					<p v-if="modalType.customData.value&&checkFileType(modalType.customData.value) =='img'">Width/Height {{ getImageWidthHeight(modalType.customData.value) }}</p>

					<v-text-field class="mb-3" v-model="modalType.customData.value" outlined dense hide-details label="Link" clearable @input.native="linkChangeFn(modalType.customData.value)" @clear="linkChangeFn(modalType.customData.value)"></v-text-field>
					<cutImage1 ref="cutImage1" v-model="modalType.customData.value" @getMsrc="getMsrcFun('5')">
					</cutImage1>
				  </v-card-text>
				</v-window-item>

					<!-- <v-window-item value="changeVideo">
				  <v-window :value="bannerPanels" class="elevation-1">
					<v-window-item value="pickBannerImage">
					  <v-card flat>
						<v-card-text>
						  <b class="mr-3 ml-1 icon-a-lrgl-imagezhuanhuan"></b>
						  <span class="text-h7">Change Video</span>
						</v-card-text>
						<template v-if="modalType.customData && manageMobile == 0">
						  <p class="px-4">Alt & Title</p>
						  <v-text-field label="" v-model="modalType.customData.alt" outlined dense hide-details
							class="mb-5 px-4"></v-text-field>
						</template>
						<template v-if="modalType.customData">
						  <p class="px-4">New Video Link</p>
						  <v-text-field label="" v-model="modalType.customData.value" outlined dense hide-details
							class="mb-5 px-4"></v-text-field>
						</template>
					  </v-card>
					</v-window-item>
				  </v-window>
				  <v-card flat>
					<v-card-text>
					  <b class="mr-3 ml-1 icon-a-lrgl-imagezhuanhuan"></b>
					  <span class="text-h7">Change Video Cover</span>
					</v-card-text>
				  </v-card>
				  <cutImage ref="cutVideoImage" v-model="videoCover" @getMsrc="getMsrcFun('55')" :key="cutImageKey">
				  </cutImage>
				  <v-container class="d-flex align-center justify-center">
					<v-btn min-width="150" color="red darken-1" small dark depressed @click="clearImgCover">
					  Clear Img Cover
					</v-btn>
				  </v-container>
				</v-window-item> -->

				<v-window-item value="changeBanner">
				  <v-window :value="bannerPanels" class="elevation-1">
					<v-window-item value="pickBannerImage">
					  <v-card flat>
						<v-card-text>
						  <b class="mr-3 ml-1 icon-a-lrgl-imagezhuanhuan"></b>
						  <span class="text-h7">Change Banner Image</span>
						</v-card-text>
						<template v-if="modalType.customData">
						  <p class="px-4">Alt & Title</p>
						  <v-text-field label="" v-model="modalType.customData.alt" outlined dense hide-details
							class="mb-5 px-4"></v-text-field>
						</template>

						<v-card-text class="pt-0">
						  <v-btn block small class="mb-5" @click="uploadBannerImageFun">UPLOAD</v-btn>
						  <v-radio-group hide-details active-class="m-radio-class" class="mt-0" @change="changeBannerFun">
							<template v-for="(item, index) in bannerArr">
							  <v-radio v-if="item.clickTarget == 'banner'" :value="item" class="hoverTag">
								<template v-slot:label>
								  <v-row no-gutters>
									<v-col cols="11">
									  <v-img max-height="70" max-width="330" height="70" cover :src="item.bannerUrl">
										<template v-slot:placeholder>
										  <v-row class="fill-height ma-0" align="center" justify="center">
											<v-progress-circular indeterminate color="grey lighten-5">
											</v-progress-circular>
										  </v-row>
										</template>
									  </v-img>
									</v-col>
									<v-col cols="1">
									  <v-btn v-if="item.proId" class="pa-0 btn" height="100%" width="100%" min-width="0"
										tile x-small depressed color="error" @click.stop="deleteDialog(item)">
										<v-icon dark> mdi-trash-can </v-icon>
									  </v-btn>
									</v-col>
								  </v-row>
								</template>
							  </v-radio>
							  <v-radio v-else-if="item.clickTarget == 'video'" :value="item">
								<template v-slot:label>
								  <v-row no-gutters>
									<v-col cols="11">
									  <video height="70" width="330" loop muted autoplay :src="item.videoUrl"></video>
									</v-col>
									<v-col cols="1"> </v-col>
								  </v-row>
								</template>
							  </v-radio>
							</template>
						  </v-radio-group>
						</v-card-text>
						<v-pagination @input="changeBannerPageFun" v-model="bannerImg_Video.page"
						  :length="bannerImg_Video.pageLength"></v-pagination>
					  </v-card>
					</v-window-item>
					<v-window-item value="uploadBannerImage">
					  <v-card flat>
						<v-card-text>
						  <b class="mr-3 ml-1 icon-a-lrgl-imagezhuanhuan"></b>
						  <span class="text-h7">Upload Banner Image</span>
						</v-card-text>
						<v-card-text class="pt-0">
						  <v-btn block small class="mb-5" @click="BackBannerImageFun">BACK</v-btn>
						  <v-item-group mandatory v-model="bannerInfo" @change="changeBannerObjectFun">
							<v-container>
							  <v-row>
								<v-col v-for="(n, i) in objectFitType" :key="i" cols="12" md="6">
								  <v-item v-slot="{ active, toggle }" :value="n">
									<div :style="'border: 2px solid ' + (active ? '#1e88e5' : 'transparent')">
									  <v-img :src="bannerImgUrl" height="150" class="text-right pa-2" @click="toggle"
										:contain="n == 'contain'">
									  </v-img>
									</div>
								  </v-item>
								  <div class="text-center">{{ n.name }}</div>
								</v-col>
							  </v-row>
							</v-container>
						  </v-item-group>
						</v-card-text>
					  </v-card>
					</v-window-item>
				  </v-window>
				</v-window-item>

				  <v-window-item value="LRPadding">
						<v-card-text>
							<v-icon class="mr-2">mdi-pan-horizontal</v-icon>
							<span class="text-h7">EDIT PADDING</span>
						</v-card-text>
						<v-card-text class="pt-0">
							<v-card class="my-1">
								<v-card-text>
									Choose Left And Right Padding
									<v-slider
										v-model="LRPadding"
										step="50"
										dense
										:max="2000"
										:min="1200"
										thumb-label="always"
										ticks="always"
										tick-size="4"
										@change="changeLRPadding"
									></v-slider>
								</v-card-text>
								<v-btn block small class="mb-5" @click="resetPadding" :color="paddingHasChange ? '#F44336':'normal'">Using template originData</v-btn>
							</v-card>
						</v-card-text>

						<v-card-text class="pt-0">
							<v-card class="my-1">
								<v-card-text>
									Choose Top Padding
									<v-slider
										v-model="TPadding"
										step="5"
										dense
										:max="100"
										:min="0"
										thumb-label="always"
										ticks="always"
										tick-size="4"
										@change="changeTPadding"
									></v-slider>
								</v-card-text>
							</v-card>
						</v-card-text>

						<v-card-text class="pt-0">
							<v-card class="my-1">
								<v-card-text>
									Choose Bottom Padding
									<v-slider
										v-model="BPadding"
										step="5"
										dense
										:max="100"
										:min="0"
										thumb-label="always"
										ticks="always"
										tick-size="4"
										@change="changeBPadding"
									></v-slider>
								</v-card-text>
							</v-card>
						</v-card-text>
				  </v-window-item>

				  <v-window-item value="HideSection">
					  <v-card-text>
						  <b class="icon-a-icon-eye2zhuanhuan mr-2"></b>
						  <span class="text-h7">Hide Section</span>
					  </v-card-text>
					  <v-card-text class="pt-0">
						  <v-card class="my-1">
							  <v-card-text>
								  <v-switch v-model="hideSectionObj.isHidden" label="isHidden" color="primary" hide-details class="ml-1 mb-2"></v-switch>
								  <v-switch v-model="hideSectionObj.isHiddenLang" label="isHiddenLang" color="primary" hide-details class="ml-1 mb-2"></v-switch>
								  <p class="mb-1">Hide this module in all languages?</p>
								  <v-radio-group v-model="hideSectionObj.isHiddenLangAll" row dense class="myRadio ml-1 mt-1" hide-details @change="changeIsHiddenLangAll">
									  <div class="text-center">
									  <v-radio v-for="(item, index) in hideSectionRadioList" :key="index" color="indigo" :label="item.label"
										  :value="item.value"></v-radio>
									  </div>
								  </v-radio-group>
								  <p class="mb-1 pt-1">Result of Combination</p>
								  <v-textarea
									  single-line
									  label="Result of Combination"
									  :value="hideSectionObjResult"
									  :readonly="true"
									  auto-grow
									  :rows="1"
									  dense
								  ></v-textarea>
							  </v-card-text>
							  <v-btn block small class="mb-5" @click="changeSectionState" >Save Your Setting</v-btn>
						  </v-card>
					  </v-card-text>
				  </v-window-item>

				<v-window-item value="addCornerLabel">
					<v-card-text>
					  <b class="mr-3 ml-1 icon-top"></b>
					  <span class="text-h7">Add Corner Marker</span>
					</v-card-text>
					<v-card-text class="pt-0 pb-0" v-if="panels == 'addCornerLabel'">
						<v-card flat :loading="loading">
							<template slot="progress">
								<v-progress-linear color="deep-purple" height="10" indeterminate></v-progress-linear>
							</template>
							<v-row dense v-show="!loading">
								<v-col cols="12" sm="6" class="chooseTemplateStyleMeneClass">
									<v-select
										:value="cornerLabelId"
										:items="cornerLabelList"
										label="Corner"
										:menu-props="{ 'content-class': 'templeteTagList' }"
										solo
										:return-object="true"
										item-text="name"
										item-value="id"
										dense
										@change="changeCornerLabel"
									>
									<template v-slot:item="{ item, index }">
										<div :style="{ backgroundSize: '100% 100%',color: item.color, backgroundImage: 'url(' + item.bgImg + ')',width:'100%' }">
											<div style="width:70px;display:inline-block" v-if="item.icon">
												<pic :src="item.icon" height="30" />
											</div>
											{{ item.id + ' ' + (item.name||'') }}
										</div>
									</template>
									<template v-slot:selection="{ item, index }">
										<div :style="{ backgroundSize: '100% 100%',color: item.color, backgroundImage: 'url(' + item.bgImg + ')',width:'100%' }">
											<div style="width:70px;display:inline-block" v-if="item.icon">
												<pic :src="item.icon" height="30" />
											</div>
											{{ item.id + ' ' + (item.name||'') }}
										</div>
									</template>
								</v-select>
								</v-col>
								<v-col cols="12" sm="6" class="chooseTemplateStyleMeneClass" v-if="itemData.sampleData?.list[childIndex]?.cornerLabelType">
									<v-select
									v-model="itemData.sampleData.list[childIndex]['cornerLabelType']"
									:items="cornerLabelTypeList"
									label="CornerLabelType"
									:menu-props="{ 'content-class': 'templeteTagList' }"
									solo
									dense
									@change="changeCornerLabelType"
									></v-select>
								</v-col>
							</v-row>
						  </v-card>
						</v-card-text>
				</v-window-item>

				<v-window-item value="uploadDownload">
					<v-card-text>
					  <b class="mr-3 ml-1 icon-Download"></b>
					  <span class="text-h7">Upload&Download</span>
					</v-card-text>
					<v-card-text class="pt-0 pb-2" v-if="panels == 'uploadDownload'">
						<v-card flat :loading="loading">
							<v-row>
								<v-col cols="6" style="text-align: center;">
									<v-btn depressed x-small class="mt-1" color="blue" min-width="78" min-height="23px" @click="uploadLogHandler">upload</v-btn>
								</v-col>
								<v-col cols="6" style="text-align: center;">
									<v-btn depressed x-small class="mt-1" color="success" min-width="78" min-height="23px" @click="downloadLogHandler">download</v-btn>
								</v-col>
							</v-row>
						</v-card>
					</v-card-text>
				</v-window-item>

				<v-window-item value="editText">
				  <v-card-text>
					<b class="mr-3 ml-1 icon-a-lrgl-textzhuanhuan"></b>
					<span class="text-h7">Edit Text</span>
				  </v-card-text>
				  <v-card-text class="pt-0" v-if="modalType.customData">
					<TinyEditor :inline="false" v-model:content="modalType.customData.value"></TinyEditor>
				  </v-card-text>
				</v-window-item>


				<v-window-item value="button">
				  <v-card-text>
					<b class="mr-3 ml-1 icon-a-lrgl-buttomzhuanhuan"></b>
					<span class="text-h7">Button</span>
				  </v-card-text>
				  <v-card-text class="pt-0" v-if="modalType.customData">
					<p class="mb-1">Button Description* :</p>
					<v-text-field label="" v-model="modalType.customData.value" outlined dense hide-details
					  class="mb-3"></v-text-field>
					<p class="mb-1">Title :</p>
					<v-text-field label="" v-model="modalType.customData.alt" outlined dense hide-details
					  class="mb-3"></v-text-field>
					<p class="mb-1">ICON :</p>
					<v-text-field label="" v-model="modalType.customData.icon" outlined dense hide-details
					  class="mb-3"></v-text-field>
					<p>What do you want to link to?</p>
					<v-radio-group v-model="modalType.customData.type" row dense class="myRadio mb-3 " hide-details>
					  <div class="text-center">
						<v-radio v-for="(item, index) in btnUrl" :key="index" color="indigo" :label="item.label"
						  :value="item.value" @change="$forceUpdate()"></v-radio>
					  </div>
					</v-radio-group>
					<v-card flat v-if="modalType.customData">
					  <v-list>
						<v-list-item class="px-0">
						  <v-list-item-content>
							<v-row>
							  <template v-if="modalType.customData.type == 'link'">
								<v-col :cols="12">Which Link?</v-col>
								<v-col :cols="12" class="pt-0">
								  <!-- <v-combobox dense hide-details v-model="modalType.customData.url" item-value="url"
									item-text="pageName" :items="productData" :return-object="false">
								  </v-combobox> -->
								  <!-- 解决v-combobox点击数据外侧才更新的BUG -->
								  <v-combobox dense hide-details v-model.trim="modalType.customData.url" item-value="url"
									@input.native="modalType.customData.url = $event.target.value; hideDropdown = true;"
									:menu-props="{ ...(hideDropdown && { value: false }) }"
									@click="hideDropdown = !hideDropdown" item-text="pageName" :items="productData"
									:return-object="false">
								  </v-combobox>
								</v-col>
							  </template>
							  <template v-else-if="modalType.customData.type == 'click'">
								<v-col :cols="12">Which Dialog?</v-col>
								<v-col :cols="12" class="pt-0 px-1">
								  <v-card class="mx-1 mb-2 pa-2" flat :loading="loadDialog">
									<template slot="progress">
										<v-progress-linear color="deep-purple" height="10" indeterminate></v-progress-linear>
									</template>
									<v-select
									    :value="dialogParam.type"
										:items="dialogType"
										label="Which DialogType?"
										:menu-props="{ 'content-class': 'templeteTagList' }"
										@change="changeDialogType"
									></v-select>
									<v-card-text>
										<v-row v-if="dialogParam.type ==1">
											<v-col cols="12" sm="12" style="padding-top:0px;padding-bottom:0px;">
												<v-combobox
													v-model="dialogParam.quoteUrl"
													:return-object="false"
													:items="dialogQuoteUrlList"
													:disabled="loadDialog"
													label="Change QuoteUrl"
													item-text="name"
										            item-value="routingName"
												></v-combobox>
											</v-col>
											<v-col cols="12" sm="12" style="padding-top:0px;padding-bottom:0px;">
												<v-text-field label="quoteIframe" placeholder = "Format:?type=quoteIframe&id=1" v-model="dialogParam.OtherParams"></v-text-field>
											</v-col>
										</v-row>
										<v-row v-else-if="dialogParam.type ==2">
											<v-col cols="12" sm="12" style="padding-top:0px;padding-bottom:0px;">
												<v-combobox
													v-model="dialogParam.quoteUrl"
													:return-object="false"
													:disabled="loadDialog"
													:items="dialogQuoteUrlList"
													label="Change QuoteUrl"
													item-text="name"
										            item-value="routingName"
												></v-combobox>
											</v-col>
											<!-- <v-col cols="12" sm="12" style="padding-top:0px;padding-bottom:0px;">
												<v-combobox
													v-model="dialogParam.componentName"
													:return-object="false"
													:items="[{text:'modalHalfList4',value:'modalHalfList4'}]"
													label="componentName"
													:menu-props="{ 'content-class': 'templeteTagList' }"
												>
											    </v-combobox>
											</v-col> -->
										</v-row>
										<v-row v-else-if="dialogParam.type ==3">
											<v-col cols="12" sm="12" style="padding-top:0px;padding-bottom:0px;">
												<v-combobox
												    v-model="dialogParam.parentCateId"
													:return-object="false"
													:items="parentQuoteUrlList"
													label="Change parentCateId"
													:disabled="loadDialog"
													item-text="name"
										            item-value="id"
													@change="getAllQuoteSemiCateProductListByType()"
												></v-combobox>
											</v-col>
											<v-col cols="12" sm="12" style="padding-top:0px;padding-bottom:0px;">
												<v-combobox
												    v-model="dialogParam.quoteUrl"
													:return-object="false"
													:items="dialogQuoteUrlList"
													:disabled="!dialogParam.parentCateId||loadDialog"
													label="Change QuoteUrl"
													item-text="name"
										            item-value="routingName"
												></v-combobox>
											</v-col>
										</v-row>
										<div v-else>
											<v-row v-for="(item, index) in tempList" :key="index">
												<v-col cols="12" sm="6" style="padding-top:0px;padding-bottom:0px;"><v-text-field
													placeholder="key" v-model="item[0]" :disabled="item[0] == 'type'"></v-text-field></v-col>
													<!-- 禁止修改Type -->
												<v-col cols="12" sm="6" style="padding-top:0px;padding-bottom:0px;"><v-text-field
													placeholder="value" v-model="item[1]" :disabled="item[0] == 'type'"></v-text-field></v-col>
											</v-row>
										</div>
									</v-card-text>
									<div class="text-right">
									  <v-btn v-if="dialogParam.type == 4" depressed x-small class="mt-1" color="blue" min-width="78" min-height="23px"
										@click="AddPropFn">
										Add
									  </v-btn>
									  <v-btn depressed x-small class="mt-1" color="success" min-width="78"
										min-height="23px" @click="SavePropFn">
										Save
									  </v-btn>
									</div>
								  </v-card>
								</v-col>
							  </template>
							</v-row>
						  </v-list-item-content>
						</v-list-item>
					  </v-list>
					  <v-divider></v-divider>
					  <v-list>
						<v-list-item class="px-0">
						  <v-list-item-content>
							<v-row>
							  <v-col :cols="12">How does it open?</v-col>
							  <v-col :cols="12" class="pt-0">
								<v-radio-group v-model="modalType.customData.target" class="mt-0" hide-details>
								  <v-radio label="New window" value="_blank"></v-radio>
								  <v-radio label="Current window" value="_self"></v-radio>
								</v-radio-group>
							  </v-col>
							</v-row>
						  </v-list-item-content>
						</v-list-item>
					  </v-list>
					</v-card>
					<p>Choose Button Style</p>
					<v-radio-group v-model="modalType.customData.style" column>
					  <v-row>
						<v-col v-for="(item, index) in buttonStyle" :key="index">
						  <v-radio label="type1" :value="item.value">
							<template v-slot:label>
							  <button :style="item.style">{{ item.type || "BUTTON" }}</button>
							</template>
						  </v-radio>
						</v-col>
					  </v-row>
					</v-radio-group>
				  </v-card-text>
				</v-window-item>






				<v-window-item value="changeImageForMB" eager>
				  <v-card-text>
					<b class="mr-3 ml-1 icon-a-lrgl-imagezhuanhuan"></b>
					<span class="text-h7">Change Image</span>
				  </v-card-text>
				  <p v-if="itemData.sampleData?.type?.customData?.img">Width/Height {{ getImageWidthHeight(modalType.customData.value) }}</p>
					<!-- <v-text-field class="mb-3" v-if="itemData.sampleData?.type?.customData?.img" label="" :value="getImageWidthHeight(modalType.customData.img.value)" outlined
					  dense hide-details disabled ></v-text-field> -->
				  <v-card-text class="pt-0" v-if="itemData.sampleData?.type?.customData?.img">
					<cutImage ref="cutImage5" v-model="modalType.customData.img.value" @getMsrc="getMsrcFun('8')">
					</cutImage>
				  </v-card-text>
				</v-window-item>


				<v-window-item value="socialMedia">
				  <v-card-text>
					<div class="d-flex justify-space-between">
					  <span class="d-flex align-center">
						<v-icon class="mr-2">mdi-web</v-icon>
						<span class="text-h7">Social Media</span>
					  </span>
					</div>
				  </v-card-text>
				  <template v-if="shareData">
					<v-card-text v-for="item in shareData" :key="item.id" class="pt-0">
					  <!-- <v-text-field
					class="pb-5 px-2"
					hide-details
					v-model="item.title.value"
					label="Title (alt text)"
				  ></v-text-field> -->
					  <div class="pb-3 px-2">
						<strong style="text-transform: capitalize">{{  item.title.value }}</strong>
					  </div>
					  <v-text-field class="pb-2 px-2" hide-details v-model="item.url" outlined></v-text-field>
					</v-card-text>
				  </template>
				</v-window-item>


				<v-window-item value="contactInformation">
				  <v-card-text>
					<v-icon class="mr-2">mdi-phone</v-icon>
					<span class="text-h7">Contact information</span>
				  </v-card-text>
				  <v-card-text class="pt-0">
					<v-text-field class="pb-5 px-2" hide-details v-model="sysProOwnerDTO.address"
					  @input="sysProOwnerDTOChangeFun" label="Address"></v-text-field>
					<v-text-field class="pb-5 px-2" hide-details v-model="sysProOwnerDTO.email"
					  @input="sysProOwnerDTOChangeFun" label="Email"></v-text-field>
					<v-text-field class="pb-2 px-2" hide-details v-model="sysProOwnerDTO.phone"
					  @input="sysProOwnerDTOChangeFun" label="Phone Number"></v-text-field>
				  </v-card-text>
				</v-window-item>


				<v-window-item value="icons">
				  <v-card-text>
					<v-icon class="mr-2">mdi-draw</v-icon>
					<span class="text-h7">Icons</span>
				  </v-card-text>
				  <v-card-text class="pt-0">
					<v-row>
					  <v-col v-for="(item, index) in iconsData" :key="index">
						<v-btn class="mx-2" depressed fab @click="modalType.customData.value = item.class">
						  <b style="font-size: 34px" :class="item.class"></b>
						</v-btn>
					  </v-col>
					</v-row>
				  </v-card-text>
				</v-window-item>


				<v-window-item value="navigation">
				  <v-card-text>
					<v-icon class="mr-2">mdi-trello</v-icon>
					<span class="text-h7">Header Edit</span>
				  </v-card-text>
				  <v-card-text class="pt-0">
					<v-window v-model="treeWindow">
					  <v-window-item :key="0">
						<v-btn block small class="mb-5" @click="newPageFun">NEW PAGE</v-btn>
						<v-draggable-treeview v-model="headerTreeItems" :open="initiallyOpen" @input="sortList"
						  activatable open-on-click dense>
						  <template v-slot:prepend="{ item, open }">
							<v-icon class="mr-2" v-if="item.name == 'Home'">mdi-home</v-icon>
							<v-icon class="mr-2" v-else>{{ open ? "mdi-folder-open" : "mdi-folder" }}</v-icon>
						  </template>
						  <template v-slot:append="{ item, open }">
							<v-menu offset-y :close-on-click="true" :close-on-content-click="true">
							  <template v-slot:activator="{ on, attrs }">
								<v-icon v-bind="attrs" v-on="on"> mdi-cog </v-icon>
							  </template>
							  <v-list>
								<v-list-item link v-for="(mitem, mindex) in menuItems" :key="mindex">
								  <v-list-item-title @click="treeSettingFun(mitem, item)">
									<v-icon>{{ mitem.icon }}</v-icon>
									{{ mitem.title }}
								  </v-list-item-title>
								</v-list-item>
							  </v-list>
							</v-menu>
						  </template>
						</v-draggable-treeview>
					  </v-window-item>
					  <v-window-item :key="1">
						<v-btn block small class="mb-5" @click="headerBackFun">BACK</v-btn>
						<v-form ref="form" v-model="valid">
						  <v-text-field v-model="headerFormData.name" :rules="nameRules" label="Name"
							required></v-text-field>

							<v-text-field v-model="headerFormData.nameColor" label="Name Color"><template
							  v-slot:append-outer>
							  <v-menu :close-on-content-click="false" offset-y>
								<template v-slot:activator="{ on, attrs }">
								  <v-btn icon v-bind="attrs" v-on="on">
									<v-icon> mdi-palette </v-icon>
								  </v-btn>
								</template>
								<v-card>
								  <v-card-text class="pa-6">
									<v-color-picker hide-mode-switch v-model="colorData1" dot-size="25"
									  swatches-max-height="200" @input="colorPickerFun1"></v-color-picker>
								  </v-card-text>
								</v-card>
							  </v-menu>
							</template>
						  </v-text-field>
						  <v-text-field v-model="headerFormData.labelText" label="Label"></v-text-field>

						  <v-text-field v-model="headerFormData.labelColor" label="Label Color"><template
							  v-slot:append-outer>
							  <v-menu :close-on-content-click="false" offset-y>
								<template v-slot:activator="{ on, attrs }">
								  <v-btn icon v-bind="attrs" v-on="on">
									<v-icon> mdi-palette </v-icon>
								  </v-btn>
								</template>
								<v-card>
								  <v-card-text class="pa-6">
									<v-color-picker hide-mode-switch v-model="colorData" dot-size="25"
									  swatches-max-height="200" @input="colorPickerFun"></v-color-picker>
								  </v-card-text>
								</v-card>
							  </v-menu>
							</template>
						  </v-text-field>

						  <v-text-field v-model="headerFormData.linkUrl" label="URL"></v-text-field>

						  <v-text-field v-model="headerFormData.linkUrlLang" label="URL Lang"></v-text-field>

						  <!-- <v-select v-model="headerFormData.pageId" :items="pageInfo.allPage" label="Page"
							item-text="pageName" item-value="id"></v-select> -->

						  <!-- 改成树状 可搜索 -->
						  <v-menu offset-y max-height="400" content-class="custom-scrollbar"
							:close-on-content-click="false" v-model="isOpenPageMenu">
							<template v-slot:activator="{ on, attrs }">
							  <v-text-field v-bind="attrs" v-on="on" v-model="chooseName" label="Page"></v-text-field>
							</template>
							<v-card>
							  <v-item-group mandatory>
								<v-container>
								  <v-row>
									<v-text-field label="Search Name" v-model="searchText" hide-details></v-text-field>
								  </v-row>
								  <v-row style="display:initial">
									<v-treeview class="" :transition="true" item-text="pageName" item-key="id"
									  :items="treeData" item-children="childList" activatable
									  :active="[this.headerFormData.pageId]" @update:active="choosePage"></v-treeview>
								  </v-row>
								</v-container>
							  </v-item-group>
							</v-card>
						  </v-menu>

						  <v-row>
							<v-col cols="6">
							  <v-switch v-model="headerFormData.isExpand" class="ml-3" :false-value="0" :true-value="1"
								label="Expand"></v-switch>
							</v-col>
							<v-col cols="6">
							  <v-switch v-model="headerFormData.isTranslate" class="ml-3" :false-value="0" :true-value="1"
								label="Translate(once)"></v-switch>
							</v-col>
						  </v-row>
						  <v-row>
							<v-col cols="6">
							  <v-switch v-model="headerFormData.isHot" class="ml-3" :false-value="0" :true-value="1"
								label="Hot"></v-switch>
							</v-col>
							<v-col cols="6" v-if="headerFormData.isHot">
							  <v-combobox dense hide-details v-model.trim="headerFormData.categoryId" item-value="id"
									@input.native="headerFormData.categoryId = $event.target.value; hideDropdown = true;"
									:menu-props="{ ...(hideDropdown && { value: false }) }"
									@click="hideDropdown = !hideDropdown" item-text="customRouting" :items="hotList"
									:return-object="false">
							  </v-combobox>
							</v-col>
							<v-col cols="6">
							  <v-switch v-model="headerFormData.isProducts" class="ml-3" :false-value="0" :true-value="1"
								label="Products"></v-switch>
							</v-col>
						  </v-row>

						  <v-checkbox v-model="headerFormData.isEnable" class="ml-3" label="Enable" :false-value="0"
							:true-value="1"></v-checkbox>
						  <v-btn color="blue darken-1" dark @click="newHeaderFun">SUBMIT</v-btn>
						</v-form>
					  </v-window-item>
					</v-window>
				  </v-card-text>
				</v-window-item>


				<v-window-item value="product_crowd" eager>
				  <v-card-text>
					<v-icon class="mr-2">mdi-wallet-giftcard</v-icon>
					<span class="text-h7">Products</span>
				  </v-card-text>
				  <v-card-text class="pt-0"
					v-if="modalType.clickType == 'product_crowd' || modalType.clickType == 'sticker_crowd'">
					<v-menu offset-y>
					  <template v-slot:activator="{ on, attrs }">
						<v-btn block small class="mb-5" v-bind="attrs" v-on="on" v-show="!modalType.customData">
						  NEW PRODUCT
						</v-btn>
					  </template>
					  <v-list>
						<v-list-item link v-for="(item, index) in productCateList" :key="index"
						  @click="getApiProductFun(item)">
						  <v-list-item-title>{{ item.cateName }}</v-list-item-title>
						</v-list-item>
					  </v-list>
					</v-menu>
					<div style="height: 500px;overflow-y: scroll;" v-show="modalType.customData || showForm">
					  <cutImage ref="cutImage3" class="mb-5" v-model="productForm.img" @getMsrc="getMsrcFun('6')"
						@updateLogo="updateLogo" logo>Image</cutImage>
					  <v-form ref="form" class="pa-1" v-model="productValid" lazy-validation>
						<v-text-field v-model="productForm.link" :rules="[(v) => !!v || 'Link is required']" label="Link"
						  required></v-text-field>

						<v-text-field v-model="productForm.title" :rules="[(v) => !!v || 'Title is required']"
						  label="Title" required></v-text-field>

						<v-text-field v-model="productForm.alt" label="Alt"></v-text-field>
						<v-expansion-panels flat accordion class="customPanels">
						  <v-expansion-panel>
							<v-expansion-panel-header>Additional Options</v-expansion-panel-header>
							<v-expansion-panel-content>
							  <v-text-field v-model="productForm.img2" label="Image 2"></v-text-field>
							  <v-text-field v-model="productForm.alt2" label="Alt 2"></v-text-field>
							  <v-text-field v-model="productForm.title2" label="Title 2"></v-text-field>
							</v-expansion-panel-content>
						  </v-expansion-panel>
						</v-expansion-panels>
						<v-text-field v-model="productForm.subTitle" label="SubTitle"></v-text-field>
						<v-text-field v-model="productForm.price" label="Price" type="number"></v-text-field>
						<v-text-field v-model="productForm.text" label="Text"></v-text-field>
						<v-text-field v-model="productForm.button" label="button"></v-text-field>
						<v-switch v-model="productForm.disabled" label="Disabled"></v-switch>
						<v-btn :disabled="!valid" color="success" class="mr-4" @click="updateProductForm">
						  update
						</v-btn>
					  </v-form>
					</div>
				  </v-card-text>
				</v-window-item>


				<v-window-item value="swiper" eager>
				  <v-card-text>
					<v-icon class="mr-2">mdi-book-multiple</v-icon>
					<span class="text-h7">Swiper</span>
				  </v-card-text>
				  <v-card-text class="pt-0 px-1" v-if="modalType.clickType == 'swiper'">
					<v-text-field v-model.number.trim="itemData.sampleData.autoplay" label="autoplay"  type="number" :rules="[autoplayRules]" min="0">
						<span  slot="append" class="mr-3">(sec)</span>
					</v-text-field>
					<v-responsive class="overflow-y-auto" max-height="650">
					  <v-responsive class="text-center">
						<v-hover v-slot="{ hover }" v-for="item, index in swiperList" :key="index">
						  <v-card class="mx-1 mb-2" >
							<!-- 有img和icon时使用img和icon里面的Value -->
							<v-img v-if="item.img || item.icon" style="height: 330px;" :src="item.img ? item.img.value : item.icon.value">
							  <v-expand-transition>
								<div v-if="hover"
								  class="d-flex transition-fast-in-fast-out white darken-2 v-card--reveal text-h2"
								  style="height: 100%;">
								  <v-card-text>
									<v-text-field hide-details v-model="item.img.value" v-if="item.img"
									  label="Image"></v-text-field>
									  <v-text-field hide-details v-model="item.icon.value" v-if="item.icon"
									  label="Icon"></v-text-field>
									<v-text-field hide-details v-model="item.img.alt" v-if="item.img"
									  label="ImgAlt"></v-text-field>
									<v-text-field hide-details v-model="item.icon.alt" v-if="item.icon"
									  label="IconAlt"></v-text-field>
									<v-text-field hide-details v-model="item.title.value" v-if="item.title"
									  label="Title"></v-text-field>
									<v-text-field hide-details v-model="item.subTitle.value" v-if="item.subTitle"
									  label="SubTitle"></v-text-field>
									<div class="text-right">
									  <v-btn depressed x-small class="mt-1" color="error" @click="deleteSwiperFun(index)">
										delete
									  </v-btn>
									</div>
								  </v-card-text>
								</div>
							  </v-expand-transition>
							</v-img>
							<v-card v-else >
							  <v-expand-transition>
								<div
								  class="d-flex transition-fast-in-fast-out white darken-2 v-card--reveal text-h2 "
								  style="height: 100%;color:unset">
								  <v-card-text>
									<v-text-field hide-details v-model="item.video.value" v-if="item.video"
									  label="Video"></v-text-field>
									<v-text-field hide-details v-model="item.video.alt" v-if="item.video"
									  label="Alt"></v-text-field>
									<v-text-field hide-details v-model="item.title.value" v-if="item.title"
									  label="Title"></v-text-field>
									<v-text-field hide-details v-model="item.subTitle.value" v-if="item.subTitle"
									  label="SubTitle"></v-text-field>

									<div class="text-right">
									  <v-btn depressed x-small class="mt-1" color="error" @click="deleteSwiperFun(index)">
										delete
									  </v-btn>
									</div>
								  </v-card-text>
								</div>
							  </v-expand-transition>
							</v-card>
						  </v-card>
						</v-hover>
					  </v-responsive>
					</v-responsive>
				  </v-card-text>
				  <v-btn block small class="mb-3" @click="newSwiperFun">New Item</v-btn>
				  <v-btn small block color="success" class="mb-2" @click="updateSwiperFun">Update</v-btn>
				</v-window-item>

				<v-window-item value="card_list" eager>
				  <v-card-text>
					<v-icon class="mr-2">mdi-book-multiple</v-icon>
					<span class="text-h7">CARD LIST</span>
				  </v-card-text>
				  <v-card-text class="pt-0 px-1" v-if="modalType.clickType == 'card_list'">
					<p class="pb-1">Choose a linkArea :</p>
					<v-row dense v-show="!loading">
						<v-col cols="12" class="chooseTemplateStyleMeneClass">
							<v-select label="Select a parent" solo dense :menu-props="{ 'content-class': 'templeteTagList' }"
							@change="handleChangeLinkArea" v-model="itemData.sampleData.linkArea"
							:items="cardListOption" >
							</v-select>
						</v-col>
					</v-row>
					<p class="pb-1 pt-1">Margin&Column:</p>
					<v-row dense v-show="!loading">
						<v-col cols="6" class="chooseTemplateStyleMeneClass">
							<v-text-field class="mb-3"  label="Margin" v-model="itemData.sampleData.margin" outlined title=""
						dense hide-details type="number" @input.native="changeMarginColumn($event,1)"></v-text-field>
						</v-col>
						<v-col cols="6" class="chooseTemplateStyleMeneClass" v-if="itemData.cardType !=3||itemData?.sample.cardType !=3">
							<v-text-field class="mb-3" label="Column" v-model="itemData.sampleData.column" outlined title=""
						dense hide-details type="number" @input.native="changeMarginColumn($event,2)"></v-text-field>
						</v-col>
					</v-row>
				  </v-card-text>
				</v-window-item>

				<v-window-item value="img_list" eager>
				  <v-card-text>
					<v-icon class="mr-2">mdi-book-multiple</v-icon>
					<span class="text-h7">IMG LIST</span>
				  </v-card-text>
				  <v-card-text class="pt-0 px-1" v-if="modalType.clickType == 'img_list'">
					<v-responsive class="overflow-y-auto" max-height="650">
					  <v-responsive class="text-center">
						<v-hover v-slot="{ hover }" v-for="item, index in imgList" :key="index">
						  <v-card class="mx-1 mb-2" v-if="isImgUrl(item.value)">
							<v-img style="height: 240px;" :src="item.value">
							  <v-expand-transition>
								<div v-if="hover"
								  class="d-flex transition-fast-in-fast-out white darken-2 v-card--reveal text-h2 "
								  style="height: 100%;">
								  <v-card-text>
									<v-text-field hide-details v-model="item.value"
									  label="Image URL/Video URL"></v-text-field>
									<v-text-field hide-details :value="getImageWidthHeight(item.value)"
									label="Width/Height" readonly></v-text-field>
									<v-text-field hide-details v-model="item.alt" label="Alt" v-if="!manageMobile"></v-text-field>
									<v-text-field hide-details v-model="item.url" :disabled="!!item.method" label="Link url" v-if="!manageMobile"></v-text-field>
									<div class="text-right" style="font-size: 22px;" v-if="!manageMobile">
										<b class="mr-3 mt-2 icon-shanchu3" style="cursor:pointer"  @click="deleteImgListFun(index)"></b>
										<b class="mr-3 mt-2 icon-tianjia" style="cursor:pointer" @click="addImgListFun(index)"></b>
										<b class="mr-3 mt-2 icon-shangjiantou" style="cursor:pointer" @click="upImgListFun(index)"></b>
										<b class="mr-3 mt-2 icon-xiajiantou" style="cursor:pointer" @click="downImgListFun(index)"></b>
									</div>
								  </v-card-text>
								</div>
							  </v-expand-transition>
							</v-img>
						  </v-card>
						  <v-card class="mx-1 mb-2 pa-2" v-else>
							<v-text-field hide-details v-model="item.value" label="Font"></v-text-field>
							<v-text-field hide-details v-model="item.alt" label="Alt" v-if="!manageMobile"></v-text-field>
							<v-text-field hide-details v-model="item.url" :disabled="!!item.method" label="URL" v-if="!manageMobile"></v-text-field>
							<div class="text-right" style="font-size: 22px;" v-if="!manageMobile">
								<b class="mr-3 mt-2 icon-shanchu3" style="cursor:pointer"  @click="deleteImgListFun(index)"></b>
								<b class="mr-3 mt-2 icon-tianjia" style="cursor:pointer" @click="addImgListFun(index)"></b>
								<b class="mr-3 mt-2 icon-shangjiantou" style="cursor:pointer" @click="upImgListFun(index)"></b>
								<b class="mr-3 mt-2 icon-xiajiantou" style="cursor:pointer" @click="downImgListFun(index)"></b>
							</div>
						  </v-card>
						</v-hover>
					  </v-responsive>
					</v-responsive>
				  </v-card-text>
				  <v-btn small block color="success" class="mb-2" @click="updateImgListFun">Update</v-btn>
				</v-window-item>


				<v-window-item value="nav_list" eager>
				  <v-card-text>
					<v-icon class="mr-2">mdi-book-multiple</v-icon>
					<span class="text-h7">NAV LIST</span>
				  </v-card-text>
				  <v-card-text class="pt-0 px-1">
					<v-card class="mx-1 mb-2 pa-2" v-for="item, index in navList" :key="index">
					  <v-text-field hide-details v-model="item.value" label="Font" v-if="!manageMobile"></v-text-field>
					  <v-text-field hide-details v-model="item.alt" label="Alt" v-if="!manageMobile"></v-text-field>
					  <v-text-field hide-details v-model="item.url" label="URL" v-if="!manageMobile"></v-text-field>
					  <v-text-field hide-details v-model="item.column" label="COLUMN"></v-text-field>
					  <v-text-field hide-details v-model="item.margin" label="MARGIN"></v-text-field>
					  <div class="text-right" v-if="!manageMobile" style="font-size: 22px;">
						<b class="mr-3 mt-2 icon-shanchu3" style="cursor:pointer"  @click="deleteNavFun(index)"></b>
						<b class="mr-3 mt-2 icon-tianjia" style="cursor:pointer" @click="addNavFun(index)"></b>
						<b class="mr-3 mt-2 icon-shangjiantou" style="cursor:pointer" @click="upNavFun(index)"></b>
						<b class="mr-3 mt-2 icon-xiajiantou" style="cursor:pointer" @click="downNavFun(index)"></b>
					  </div>
					</v-card>
				  </v-card-text>
				  <v-btn small block color="success" class="mb-2" @click="updateNavListFun">Update</v-btn>
				</v-window-item>


				<v-window-item value="price_list" eager>
				  <v-card-text>
					<v-icon class="mr-2">mdi-book-multiple</v-icon>
					<span class="text-h7">Price</span>
				  </v-card-text>
				  <v-card-text class="pt-0 px-1">
					<v-card class="mx-1 mb-2 pa-2" v-for="item, index in priceList" :key="index">
					  <v-text-field hide-details v-model="item.value" label="Font"></v-text-field>
					  <div class="text-right" style="font-size: 22px;">
							<b class="mr-3 mt-2 icon-shanchu3" style="cursor:pointer"  @click="deletePriceFun(index)"></b>
							<b class="mr-3 mt-2 icon-tianjia" style="cursor:pointer" @click="addPriceFun(index)"></b>
							<b class="mr-3 mt-2 icon-shangjiantou" style="cursor:pointer" @click="upPriceFun(index)"></b>
							<b class="mr-3 mt-2 icon-xiajiantou" style="cursor:pointer" @click="downPriceFun(index)"></b>
						</div>
					</v-card>
				  </v-card-text>
				  <v-btn small block color="success" class="mb-2" @click="updatePriceListFun">Update</v-btn>
				</v-window-item>


				<v-window-item value="modal_data" eager>
				  <v-card-text>
					<b class="mr-3 ml-1 icon-a-lrgl-textzhuanhuan"></b>
					<span class="text-h7">Modal</span>
				  </v-card-text>
				  <v-card-text class="pt-0" v-if="itemData.sampleData">
					<v-text-field label="sku :" v-model="itemData.sampleData.sku" outlined dense hide-details
					  class="mb-3"></v-text-field>
				  </v-card-text>
				</v-window-item>


				<v-window-item value="background" eager>
				  <v-card-text>
					<v-icon class="mr-2">mdi-image</v-icon>
					<span class="text-h7">Edit Background</span>
				  </v-card-text>
				  <v-card-text class="pt-0">
					<v-card class="mt-3 mb-5">
					  <cutImage ref="cutImage4" v-model="colorPickerValue.value3" @getMsrc="getMsrcFun('7')" logo>
						Image
					  </cutImage>
					  <v-btn block small color="error" @click="removeImageFun">
						REMOVE IMAGE
					  </v-btn>
					</v-card>

					<v-card class="my-1">
					  <v-card-text>
						Color 1
						<v-color-picker v-model="colorPickerValue.value1" v-if="panels == 'background'" hide-mode-switch
						  width="350px" mode="rgba" swatches-max-height="300px"></v-color-picker>
					  </v-card-text>
					</v-card>
					<v-switch v-model="twoColor" class="ml-1" label="Gradient color" @change="twoColorFun"></v-switch>
					<v-card class="my-1" v-if="twoColor">
					  <v-card-text>
						Color 2
						<v-color-picker v-model="colorPickerValue.value2" hide-mode-switch width="350px" mode="rgba"
						  swatches-max-height="300px"></v-color-picker>
					  </v-card-text>
					</v-card>

				  </v-card-text>
				</v-window-item>


				<v-window-item value="fontColor" eager>
				  <v-card-text>
					<v-icon class="mr-2">mdi-image</v-icon>
					<span class="text-h7">Edit Font Color</span>
				  </v-card-text>
				  <v-card-text class="pt-0">
					<v-card class="my-1">
					  <v-card-text>
						Font Color
						<v-color-picker v-model="fontColorValue.value" v-if="panels == 'fontColor'" hide-mode-switch
						  width="350px" mode="rgba" swatches-max-height="300px"></v-color-picker>
					  </v-card-text>
					</v-card>
				  </v-card-text>
				</v-window-item>


				<v-window-item value="custom_list" eager>
				  <v-card-text>
					<v-icon class="mr-2">mdi-wallet-giftcard</v-icon>
					<span class="text-h7">Custom Products </span>
				  </v-card-text>
				  <v-card-text class="pt-0 pb-0" v-if="modalType.clickType == 'custom_list'">
					<v-btn block small :disabled="loadingForHalfCustomList" :loading="loadingForHalfCustomList"
					  class="white--text mb-3" color="blue darken-2" @click="addHalfCustomListFun()">
					  ADD HALF CUSTOM PRODUCT
					</v-btn>
					<v-btn block small :disabled="loadingForCustomList" :loading="loadingForCustomList"
					  class="white--text" color="purple darken-2" @click="addCustomListFun()">
					  ADD CUSTOM PRODUCT
					</v-btn>
				  </v-card-text>
				  <v-card-text>
					<v-draggable-treeview v-model="tempAddCustomList" :open="initiallyOpen" @input="sortCustomListFun"
					  activatable open-on-click dense class="noLevel">
					  <template v-slot:prepend="{ item }">
						<v-icon>mdi-sort-variant</v-icon>
					  </template>
					  <template v-slot:label="{ item }">
						<span>{{ item.name || item.cateName }}</span>
					  </template>
					  <template v-slot:append="{ item, open }">
						<v-icon @click="deleteCustomList(item)">mdi-delete</v-icon>
					  </template>
					</v-draggable-treeview>
				  </v-card-text>
				</v-window-item>


			   <v-window-item value="tag_list" eager>
					  <v-card-text>
						<v-icon class="mr-2">mdi-wallet-giftcard</v-icon>
						<span class="text-h7">Cate Tags</span>
					  </v-card-text>
					  <v-card-text class="pt-0 pb-0" v-if="modalType.clickType == 'tag_list'">
						<v-card flat :loading="loading">
							  <template slot="progress">
								  <v-progress-linear color="deep-purple" height="10" indeterminate></v-progress-linear>
							  </template>
							  <section v-show="checkHalfDetail">
									  <v-row dense v-show="!loading" class="mb-1">
									  <v-col cols="12" sm="6" class="chooseTemplateStyleMeneClass">
										  <v-select
											  v-model="categoryId"
											  :items="halfCateList"
											  label="categoryId"
											  :menu-props="{ 'content-class': 'templeteTagList' }"
											  dense
											  @change="changeCategoryId"
										  ></v-select>
									  </v-col>
									  <v-col cols="12" sm="6" class="chooseTemplateStyleMeneClass">
										  <v-select
										  v-model="childCateId"
										  :items="halfChildrenList"
										  label="childCateId"
										  :menu-props="{ 'content-class': 'templeteTagList' }"
										  dense
										  item-text="name"
										  item-value="id"
										  @change="changeChildcateId"
										  :disabled="!halfCateMap.has(categoryId)"
										  ></v-select>
									  </v-col>
								  </v-row>
								  <v-row dense v-show="!loading">
									  <v-col cols="12" class="chooseTemplateStyleMeneClass" >
										  <v-select
											  v-model="productIdValue"
											  :items="productIdList"
											  label="Product"
											  :menu-props="{ 'content-class': 'templeteTagList' }"
											  item-text="productSku"
											  item-value="id"
											  @change="changeProductIdValue"
											  multiple
											  dense
											  :disabled="productIdList.length==0 || !childCateId"
											  >
											  <template v-slot:selection="{ item, index }">
												  <span v-if="index === 0">{{ item.productSku }}</span>
												  <span v-if="index === 1" class="grey--text text-caption">(+{{ productIdValue.length - 1 }} tags)</span>
											  </template>
										  </v-select>
									  </v-col>
								  </v-row>
							  </section>
							  <section v-show="!checkHalfDetail">
								  <v-row dense v-show="!loading" >
									  <v-switch v-model="itemData.sampleData.parentTemplate" label="Show Parent" color="primary"
									  hide-details class="ml-1"></v-switch>
								  </v-row>
								  <v-row dense v-show="!loading" class="mb-3" >
									  <v-switch v-model="itemData.sampleData.childTemplate" label="Show Children" color="primary"
									  hide-details class="ml-1"></v-switch>
								  </v-row>
								  <v-row dense v-show="!loading" class="mb-1">
									  <v-col cols="12" class="chooseTemplateStyleMeneClass">
										  <v-select label="Select a parent"  dense :menu-props="{ 'content-class': 'templeteTagList' }"
										  @change="handleChangeParentType" v-model="itemData.sampleData.parentIndex"
										  :items="parentSortList" item-value="id" item-text="templateTypeName">
										  </v-select>
									  </v-col>
								  </v-row>
								  <v-row dense v-show="!loading" class="mb-1">
									  <v-col cols="12" sm="6" class="chooseTemplateStyleMeneClass">
										  <v-select
											  v-model="ringCateId"
											  :items="ringCateIdList"
											  label="Cate"
											  :menu-props="{ 'content-class': 'templeteTagList' }"
											  dense
											  @change="changeRingCateId"
										  ></v-select>
									  </v-col>
									  <v-col cols="12" sm="6" class="chooseTemplateStyleMeneClass">
										  <v-select
										  v-model="linkType"
										  :items="linkTypeList"
										  label="Opening method"
										  :menu-props="{ 'content-class': 'templeteTagList' }"
										  dense
										  @change="changeLinkType"
										  ></v-select>
									  </v-col>
								  </v-row>
								  <v-row dense v-show="!loading && classHasTag" >
									  <v-col cols="12" class="chooseTemplateStyleMeneClass" >
										  <v-select
											  v-model="ringTagValue"
											  :items="ringTagList"
											  label="Tag"
											  :menu-props="{ 'content-class': 'templeteTagList' }"
											  item-text="ringTagName"
											  item-value="ringTagName"
											  @change="changeRingTagValue"
											  return-object
											  multiple
											  dense
											  :disabled="!ringCateTagMap.has(ringCateId)"
											  >
											  <template v-slot:selection="{ item, index }">
											  <span v-if="index === 0">{{ item.ringTagName }}</span>
											  <span v-if="index === 1" class="grey--text text-caption">(+{{ ringTagValue.length - 1 }} tags)</span>
											  </template>
										  </v-select>
									  </v-col>
								  </v-row>
							  </section>
						  </v-card>
						</v-card-text>
					  </v-window-item>

				<v-window-item value="reviews" eager>
				  <v-card-text>
					<v-icon class="mr-2">mdi-star</v-icon>
					<span class="text-h7">Reviews </span>
				  </v-card-text>
				  <v-card-text class="pt-0 pb-0" v-if="modalType.clickType == 'reviews'">
					<v-rating v-model.number="modalType.customData.star" color="yellow darken-3"
					  background-color="grey darken-1" empty-icon="$ratingFull" half-increments hover large></v-rating>
					<v-text-field label="Reviews" v-model.number="modalType.customData.number"></v-text-field>
					<v-text-field label="Image" hide-details v-model.number="modalType.customData.img"></v-text-field>
					<v-text-field label="Url" hide-details v-model.number="modalType.customData.url"></v-text-field>
					<v-list>
					  <v-list-item class="px-0">
						<v-list-item-content>
						  <v-row>
							<v-col :cols="12">How does it open?</v-col>
							<v-col :cols="12" class="pt-0">
							  <v-radio-group v-model="modalType.customData.target" class="mt-0" hide-details>
								<v-radio label="New window" value="_blank"></v-radio>
								<v-radio label="Current window" value="_self"></v-radio>
							  </v-radio-group>
							</v-col>
						  </v-row>
						</v-list-item-content>
					  </v-list-item>
					</v-list>
				  </v-card-text>
				</v-window-item>


				<v-window-item value="template_list" eager>
				  <v-card-text>
					<v-icon class="mr-2">mdi-view-dashboard-edit</v-icon>
					<span class="text-h7">Custom Template</span>
				  </v-card-text>
				  <v-card-text class="pt-0 pb-0" v-if="modalType.clickType == 'template_list'">
					<v-switch v-model="itemData.sampleData.parentTemplate" label="Show Parent" color="primary"
					  hide-details></v-switch>
					<v-switch v-model="itemData.sampleData.childTemplate" label="Show Children" color="primary"
					  hide-details></v-switch>
					<v-switch v-model="itemData.sampleData.viewMore" label="View More" color="primary"
					  hide-details></v-switch>
					<v-select label="Select a parent" class="mt-10" clearable @change="handleChangeParentType"
					  :items="parentSortList" item-value="id" item-text="templateTypeName"></v-select>
				  </v-card-text>
				</v-window-item>


				<v-window-item value="reviews_product" eager>
				  <v-card-text>
					<v-icon class="mr-2">mdi-wall</v-icon>
					<span class="text-h7">Reviews Category</span>
				  </v-card-text>
				  <v-card-text class="pt-0 pb-0" v-if="modalType.clickType == 'reviews_product'">
					<v-text-field v-model.number="itemData.sampleData.product" label="Reviews Product"></v-text-field>
					<v-text-field v-model.number="itemData.sampleData.countryCode" label="Reviews Country"></v-text-field>
				  </v-card-text>
				</v-window-item>

				<v-window-item value="quote_table" eager>
				  <v-card-text>
					<v-icon class="mr-2">mdi-wallet-giftcard</v-icon>
					<span class="text-h7">Quote Table<span v-if="isFDOrder">(FD)</span></span>
				  </v-card-text>
				  <v-card-text class="pt-0 pb-0" v-if="modalType.clickType == 'quote_table'">
					  <v-card flat :loading="loading">
					  <template slot="progress">
						<v-progress-linear color="deep-purple" height="10" indeterminate></v-progress-linear>
					  </template>
					  <v-row dense v-show="!loading && !isFDOrder" style="margin-bottom:10px">
						  <v-switch v-model="itemData.sampleData.isBannerQuote"
						  label="isBannerQuote" color="primary" @change="changeBannerQuoteHandler"
						  :true-value=1 :false-value="0" hide-details class="ml-1"></v-switch>
					  </v-row>
					  <v-row dense v-show="!loading && !isFDOrder" style="margin-bottom:10px">
						  <v-switch v-model="itemData.sampleData.isQuickQuote"
						  label="isQuickQuote" color="primary" @change="changeQuickQuoteHandler"
						  :true-value=1 :false-value="0" hide-details class="ml-1"></v-switch>
					  </v-row>
					  <v-row dense v-show="!loading">
						<v-col cols="12" sm="6" class="chooseTemplateStyleMeneClass">
						  <v-autocomplete
							  v-model="quoteValue"
							  :items="quotePidList"
							  :menu-props="{'content-class':'templeteTagList'}"
							  dense
							  solo
							  label="quotePid"
							  @change="changeQuotePid"
						  ></v-autocomplete>
						</v-col>
						<v-col cols="12" sm="6" class="chooseTemplateStyleMeneClass">
						  <v-select
							  v-model="quoteCateValue"
							  :items="quoteCateIdList"
							  label="quoteCateValue"
							  :menu-props="{'content-class':'templeteTagList'}"
							  solo
							  item-text = 'cateName'
							  item-value = 'id'
							  dense
							  @change="changeCateValue"
							  :disabled="!PidCateIdMap.has(quoteValue)"
						  ></v-select>
						</v-col>
					  </v-row>
					</v-card>
				  </v-card-text>
				</v-window-item>
			  </v-window>

			  <v-expansion-panels :value="expansionPanels" multiple @change="changeExpansionPanels">
				<v-expansion-panel>
				  <v-expansion-panel-header>
					<span class="d-flex align-center">
					  <b class="mr-3 ml-1 icon-a-lrgl-colorszhuanhuan"></b>
					  Theme Color
					</span>
				  </v-expansion-panel-header>
				  <v-expansion-panel-content>
					<p class="mb-0">Choose a theme color that matches your website style :</p>
					<v-chip-group :value="themeColorDraft" column class="m-chip-class" active-class="m-chip-acitve"
					  @change="changeThemeColor">
					  <v-chip v-for="item in listAllColorData" :key="item.id" :value="item"
						:style="{ backgroundColor: item.colorCode }">
						<v-icon class="m-icon" x-small dense color="white">mdi-check</v-icon>
					  </v-chip>
					</v-chip-group>
				  </v-expansion-panel-content>
				</v-expansion-panel>
				<v-expansion-panel>
				  <v-expansion-panel-header>
					<span class="d-flex align-center">
					  <b class="mr-3 ml-1 icon-a-lrgl-fontzhuanhuan"></b>
					  Font for the entire site
					</span>
				  </v-expansion-panel-header>
				  <v-expansion-panel-content>
					<v-btn-toggle style="display: grid; grid-template-columns: repeat(2, 1fr)" :value="font"
					  @change="fontsChange" color="primary" dense group>
					  <v-btn :style="{ fontFamily: item.name }" v-for="(item, index) in fontsData" :key="index"
						:value="item.name" text :elevation="1"
						:class="{ 'v-btn--active': font == item.name, 'v-item--active': font == item.name }">
						<span class="hidden-sm-and-down">{{ item.name }}</span>
					  </v-btn>
					</v-btn-toggle>
				  </v-expansion-panel-content>
				</v-expansion-panel>
				<v-expansion-panel>
				  <v-expansion-panel-header>
					<span class="d-flex align-center">
					  <v-icon class="mr-2">mdi-selection</v-icon>
					  Add Section
					</span>
				  </v-expansion-panel-header>
				  <v-expansion-panel-content>
					<v-card flat :loading="loading">
					  <template slot="progress">
						<v-progress-linear color="deep-purple" height="10" indeterminate></v-progress-linear>
					  </template>
					  <v-row dense v-show="!loading">
						<v-col cols="12" class="chooseTemplateStyleMeneClass">
						  <v-text-field
							  v-model = 'searchTemplateValue'
							  label="Template ID & Template Index"
							  append-icon="mdi-magnify"
							  clearable
							  @click:append="searchTemplateHandler"
							  @keyup.enter="searchTemplateHandler"
							  placeholder = "Format:ID & ID-Style-~ (E.G.,home_02-18-1)"
						  ></v-text-field>
						</v-col>
					  </v-row>
					  <v-row dense v-show="!loading">
						<v-col cols="12" class="chooseTemplateStyleMeneClass">
						  <v-menu offset-y max-height="400" content-class="custom-scrollbar">
							<template v-slot:activator="{ on, attrs }">
							  <v-btn text v-bind="attrs" v-on="on" :style="{width:isReplaceType =='newBanner'?'100%':''}"
								:class="{ resizeFontSize: resizeFontSizeList.includes(templateStyleSearchName) }">
								{{ templateStyleSearchName || "Choose Theme" }}
								<v-icon right>mdi-menu-down</v-icon>
							  </v-btn>
							</template>

							<v-card max-width="350">
							  <v-item-group mandatory>
								<v-container>
								  <v-row>
									<div v-for="item in templateStyleList" :key="item.id"
									  @click="chooseTemplateStyle(item)" class="templateDefaultStyle"
									  :class="{ 'templateActive': templateStyleSearchName == item.websiteStyleData }">
									  <span v-if="item.websiteStyleName">
										  {{ 'style_' +  item.websiteStyleName}}
									  </span>
									  <span style="flex: 1;text-align: center;">{{ item.websiteStyleData }}</span>
									</div>
								  </v-row>
								</v-container>
							  </v-item-group>
							</v-card>
						  </v-menu>
						  <v-menu offset-y max-height="400" content-class="custom-scrollbar" v-if="isReplaceType !='newBanner'">
							<template v-slot:activator="{ on, attrs }">
							  <v-btn text v-bind="attrs" v-on="on">
								{{ templateNameSearchName || "Choose Name" }}
								<v-icon right>mdi-menu-down</v-icon>
							  </v-btn>
							</template>
							<v-card max-width="350">
							  <v-item-group mandatory>
								<v-container>
								  <v-row>
									<div v-for="item in templateNameList" :key="item.name"
									  @click="chooseTemplateName(item)" class="templateDefaultStyle"
									  :class="{ 'templateActive': templateNameSearchName == item.name }">
									  <span style="flex: 1;text-align: center;">{{ item.name }}</span>
									</div>
								  </v-row>
								</v-container>
							  </v-item-group>
							</v-card>
						  </v-menu>
						</v-col>
						<v-col v-for="(item, index) in templateArrData" :key="item.id" cols="12">
						  <v-hover v-slot="{ hover }">
							<v-card @click="newItems(item)" class="richText" :elevation="hover ? 5 : 1">
							  <div class="NO_img" v-if="item.sortIndex" @click.stop="copySortIndex(item.sortIndex)">{{ "NO:" + item.sortIndex }}</div>
							  <v-img :src="item.previewImage" class="white--text align-end" height="150px">
							  </v-img>
							  <v-icon class="NO_Preview" v-if="item.previewImage" @click.stop="previewImage(item.previewImage)">mdi-eye</v-icon>
							</v-card>
						  </v-hover>
						</v-col>
						<v-col>
						  <v-pagination @input="changePageFun()" v-model="pageForm.page"
							:length="pageForm.pageLength"></v-pagination>
						  <!-- 跳页输入框 -->
						  <v-text-field label="Go to page" type="number" v-model="pageForm.keyWord"
							@keyup.enter="jumpToPage" @blur="jumpToPage"></v-text-field>
						</v-col>
					  </v-row>
					</v-card>
				  </v-expansion-panel-content>
				</v-expansion-panel>
				<v-expansion-panel>
				  <v-expansion-panel-header>
					<span class="d-flex align-center">
					  <v-icon class="mr-2">mdi-switch</v-icon>
					  Setting the Enable function
					</span>
				  </v-expansion-panel-header>
				  <v-expansion-panel-content>
					<v-row dense>
					  <v-switch :false-value="false" :true-value="true" :input-value="canEdit"
						:label="canEdit ? 'hidden' : 'show'" @change="changeCanEdit"></v-switch>
					</v-row>
				  </v-expansion-panel-content>
				</v-expansion-panel>
			  </v-expansion-panels>
			</v-card>
		  </v-card>

		  <v-dialog v-model="dialog" max-width="290">
			<v-card>
			  <v-card-title class="text-h5"> delete this image? </v-card-title>
			  <v-card-actions>
				<v-spacer></v-spacer>

				<v-btn color="green darken-1" text @click="dialog = false">No</v-btn>

				<v-btn color="error darken-1" text @click="delSampleBannerById">Yes</v-btn>
			  </v-card-actions>
			</v-card>
		  </v-dialog>

		  <v-dialog v-model="deleteHeaderDialog" max-width="500">
			<v-card>
			  <v-card-title class="text-h5">delete this page? </v-card-title>
			  <v-card-actions>
				<v-spacer></v-spacer>

				<v-btn color="#e6e6e6" depressed light @click="deleteHeaderDialog = false">Cancel</v-btn>

				<v-btn color="#1a73e8" depressed dark @click="delHeader">delete</v-btn>
			  </v-card-actions>
			</v-card>
		  </v-dialog>

		  <v-dialog v-model="dialogForHalfCustomList" persistent>
			<v-card :loading="loadingForHalfCustomList">
			  <v-card-title>
				<span class="text-h5">Half Custom Products List</span>
			  </v-card-title>
			  <v-card-text>
				<v-row class="pa-1">
				  <v-col v-for="item, index in originHalfCustomList" :key="index" cols="12" md="2">
					<v-hover v-slot="{ hover }">
					  <v-card class="mx-auto" @click="pickUpProductFun(originHalfCustomList, item, index)">
						<v-img v-if="item.imgJson" :src="JSON.parse(item.imgJson)[0].url" class="white--text align-end">
						  <v-expand-transition>
							<div v-if="hover" class="d-flex transition-fast-in-fast-out darken-2 v-card--reveal text-h2 "
							  style="height: 100%;">
							  <v-btn class="purple" block style="border-radius:0;">Add Product</v-btn>
							</div>
						  </v-expand-transition>
						</v-img>
						<v-card-title>{{ item.name }}</v-card-title>
					  </v-card>
					</v-hover>
				  </v-col>
				</v-row>
			  </v-card-text>

			  <v-card-actions style="position: sticky;bottom: 0;right: 0;background-color: white;">
				<v-pagination v-model="halfCustomListForm.page"  :total-visible="36" :length="halfCustomListForm.pageLength"
				  @input="getSemiProductList()"></v-pagination>
				<v-spacer></v-spacer>
				<v-btn color="blue darken-1" text @click="shutDialogForHalfCustomList">Close</v-btn>
			  </v-card-actions>
			</v-card>
		  </v-dialog>

		  <v-dialog v-model="dialogForCustomList" persistent>
			<v-card :loading="loadingForCustomList">
			  <v-card-title>
				<span class="text-h5">Custom Products List</span>
			  </v-card-title>
			  <v-card-text>
				<v-row class="pa-1">
				  <v-col v-for="item, index in originCustomList" :key="index" cols="12" md="2">
					<v-hover v-slot="{ hover }">
					  <v-card class="mx-auto" @click="pickUpProductFun(originCustomList, item, index)">
						<v-img v-if="item.imageJson" :src="JSON.parse(item.imageJson)[0].url"
						  class="white--text align-end">
						  <v-expand-transition>
							<div v-if="hover" class="d-flex transition-fast-in-fast-out darken-2 v-card--reveal text-h2 "
							  style="height: 100%;">
							  <v-btn class="blue" block style="border-radius:0;">Add Product</v-btn>
							</div>
						  </v-expand-transition>
						</v-img>
						<v-card-title>{{ item.cateName }}</v-card-title>
					  </v-card>
					</v-hover>
				  </v-col>
				</v-row>
			  </v-card-text>
			  <v-card-actions style="position: sticky;bottom: 0;right: 0;background-color: white;">
				<v-spacer></v-spacer>
				<v-btn color="purple darken-1" text @click="shutDialogForCustomList">Close</v-btn>
			  </v-card-actions>
			</v-card>
		  </v-dialog>
		</v-navigation-drawer>
	  </v-col>

	</v-row>
  </template>

  <script>
  import CropImg from "@/components/cropImg.vue";
  import cutImage from "@/components/buildWeb/cutImage.vue";
  import cutImage1 from "@/components/buildWeb/cutImage1.vue";
  import {getRetailerAllHalfCate} from '@/api/manage/commodityWarehouse'
  import { toReader } from "@/utils/buildWeb.js";
  import {checkFileType} from "@/utils/validate.js"
  import {
	listSample,
	uploadSampleBanner,
	delSampleBannerById,
	getHeaderList,
	getHeaderDetail,
	editHeader,
	delHeader,
	sortHeader,
	getProductCateList,
	getStickerProductCateList,
	getSemiProductList,
	getQuoteParentCateList,
	getSemiQuoteProductList,
	listWebsiteStyleBySample,
	getAllQuoteSemiCateProductList
  } from "@/api/manage/buildWeb";
  import {getAppRingCateAndTagList,getAll,getProductListByChildCateId,getProductRetailerAllHalfCate,getListCornerLabel} from '@/api/web'
  import {importPageRowDraftData} from '@/api/upload'

  import { getAppParamType } from "@/api/classification";

  // import { debounce } from "@/utils/debounce.js";

  import { buttonStyle } from "@/utils/buttonStyle";
  export default {
	name: "mNavigation",
	inject: ["stepRecord"],
	components: {
	  CropImg,
	  cutImage,
	  cutImage1
	},
	props: {
	  cateId: {
		type: Number,
	  },
	  cateType: {
		type: Number,
	  },
	  pageType: {
		type: Number,
	  },
	  audienceId: {
		type: Number,
	  },
	  tinyContent: {
		type: Object,
	  },
	  panels: {
		type: String,
	  },
	  clickTarget: {
		type: String,
	  },
	  clickPosition: {
		type: Object,
	  },
	  otherData: {
		type: Object,
	  },
	  pageInfo: {
		type: Object,
	  },
	  listAllColorData: {
		type: Array,
	  },
	  shareData: {
		type: Array,
	  },
	  themeColorDraft: {
		type: Object,
	  },
	  font: {
		type: String,
	  },
	  canVuex: {
		type: Boolean,
	  },
	  bannerArr: {
		type: Array,
	  },
	  expansionPanels: {
		type: Array,
	  },
	  canEdit: {
		type: Boolean,
	  },
	  pageName: {
		type: String,
	  },
	  bannerImg_Video: {
		type: Object,
	  },
	  loading: {
		type: Boolean,
	  },
	  itemData: {
		type: Object,
		default: {},
	  },
	  totalCustomList: {
		type: Array,
		default: [],
	  },
	},
	data() {
	  return {
		autoplayRules:v => {
			if (v === null || v === undefined || v === '') {
				return 'This field is required.Please enter a number';
			}
			if (isNaN(v) || v < 0) {
				return 'Please enter a number greater than or equal to 0.';
			}
			return true;
     	 }, // 自定义验证规则
		loadDialog:false,
		dialogQuoteUrlList:[],
		parentQuoteUrlList:[],
		dialogParam:{
			type:'4',
			parentCateId:'',
			quoteUrl:null,
			OtherParams:'?type=quoteIframe',
			componentName:null
		},
		dialogType:[
		  {text:'Full Custom',value:'1'},
		  {text:'Semi Custom List',value:'2'},
		  {text:'Semi Custom Product',value:'3'},
		  {text:'Customize',value:'4'}
		],
		videoCover: "",
		cutImageKey: 0,
		isOpenPageMenu: false,
		ringCateId:'',
		linkType:'',
		isReplaceType:null,
		LRPadding:1400,
		TPadding:50,
		BPadding:50,
		paddingHasChange:false,
		originPadding:null,
		hideSectionObj:{
			  isHidden:false,
			  isHiddenLang:false,
			  isHiddenLangAll:null
		  },
		classHasTag:false,
		checkHalfDetail:false,
		categoryId:'',
		childCateId:'',
		halfCateMap:new Map(),
		halfCateList:[],
		halfChildrenList:[],
		productIdList:[],
		productIdValue:[],
		ringCateIdList:[],
		linkTypeList:[{text:'Link Method',value:'Link'},{text:'Dialog Method',value:'dialog'}],
		cornerLabelTypeList:[{text:'Left',value:'left'},{text:'Left Edge',value:'left edge'},{text:'Left Out',value:'left out'},{text:'Right',value:'right'},{text:'Right Edge',value:'right edge'},{text:'Right Out',value:'right out'}],
		cornerLabelList:[],
		cornerLabelId:null,
		ringTagValue:[],
		ringTagList:[],
		ringCateTagMap:new Map(),
		quotePidList:[],
		quoteValue:'',
		quoteCateIdList:[],
		quoteCateValue:'',
		PidCateIdMap:new Map(),
		treeData: [],
		searchText: '',
		searchResults: [],
		chooseName: '',
		tempAddCustomList: [],
		dialogForCustomList: false,
		loadingForCustomList: false,
		dialogForHalfCustomList: false,
		loadingForHalfCustomList: false,
		imgItems: ["cover", "contain"],
		twoColor: false,
		colorPickerValue: {
		  value1: "",
		  value2: "",
		  value3: "",
		},
		tempList: [],
		fontColorValue: {
		  value: "",
		},
		imageValue: "",
		isActive: false,
		uploadType: "",
		colorData: {},
		colorData1:{},
		showForm: false,
		productCateListALL: [],
		productCateListALLSticker: [],
		productCateList: [],
		productCateListSticker: [],
		swiperList: [],
		imgList: [],
		navList: [],
		navListData: [],
		priceList: [],
		originHalfCustomList: [],
		sortHalfCustomList: [],
		originCustomList: [],
		sortCustomList: [],
		productForm: {
		  img: "",
		  link: "",
		  title: "",
		  alt: "",
		  disabled: false,
		  subTitle: "",
		  price: "",
		  text: "",
		  productType: "",
		  id: null,
		  button: "",
		  signId: "",
		  img2: "",
		  alt2: "",
		  title2: "",
		},
		productValid: true,
		deleteHeaderDialog: false,
		valid: true,
		headerFormData: {},
		menuItems: [
		  { title: "New Page", icon: "mdi-plus" },
		  { title: "Page Settings", icon: "mdi-cog" },
		  // { title: "Duplicate Page", icon: "mdi-file-multiple" },
		  { title: "Delete Page", icon: "mdi-delete" },
		],
		nameRules: [(v) => !!v || "Name is required"],

		nodeData: {},
		treeWindow: 0,
		initiallyOpen: ["public"],
		files: {},
		headerTreeModel: [],
		headerTreeItems: [],
		btnUrl: [
		  {
			label: "Link",
			value: "link",
		  },
		  {
			label: "Dialog",
			value: "click",
		  },
		  // {
		  //   label: "Other Page",
		  //   value: "Other Page",
		  // }
		],
		hideSectionRadioList:[
		  {
			  label:'NO Setting',
			  value:null
		  },
		  {
			  label:'Show All',
			  value:false
		  },
		  {
			  label:'Hide All',
			  value:true
		  }
		],
		buttonStyle: buttonStyle,
		dialog: false,
		bannerImgUrl: "",
		bannerInfo: {
		  objectFit: "cover",
		  name: "Fill",
		},
		objectFitType: [
		  {
			objectFit: "cover",
			name: "Fill",
		  },
		  {
			objectFit: "contain",
			name: "Fit",
		  },
		],
		bannerPanels: "pickBannerImage",
		currentFileName: "",
		sampleType: null,
		sampleNumber: null,
		sysProOwnerDTO: {
		  address: "",
		  email: "",
		  phone: "",
		  logo: "",
		  logoFooter: "",
		},
		customListForm: {
		  page: 1,
		  pageLength: 0,
		  pageSize: 20,
		  keyWord: "",
		},
		halfCustomListForm: {
		  page: 1,
		  pageLength: 0,
		  pageSize: 20,
		  keyWord: "",
		},
		pageForm: {
		  page: 1,
		  pageLength: 5,
		  keyWord: null,
		},
		bannerForm: {
		  page: 1,
		  pageLength: 5,
		  keyWord: "",
		},
		listAllColorModel: null,
		logoWindow: 0,
		title: null,
		items: [
		  {
			src: "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/u_file/2005/photo/lapel-pins(1).jpg",
		  },
		  {
			src: "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/u_file/2005/photo/baseball-pins-1.jpg",
		  },
		  {
			src: "https://gs-jj-us-static.oss-accelerate.aliyuncs.com/u_file/2005/photo/coins-2.jpg",
		  },
		],
		selected: [],
		colors: ["primary", "secondary", "yellow darken-2", "red"],
		templateArrData: [],
		bannerArrData: [],
		Square: "",
		realImg: "",
		img: "https://img1.wsimg.com/isteam/stock/uOkgbPQw3rhK40O8W/:/rs=w:100%25",
		ex7: null,
		radioGroup: null,
		menu: false,
		menu1: false,
		menu2: false,
		select: [],
		hideDropdown: true,
		productData: [
		  {
			pageName: "Lanyard Quote",
			url: "/quote/lanyard-quote",
		  },
		  {
			pageName: "Pins Quote",
			url: "/quote/pins-quote",
		  },
		  {
			pageName: "Belt Buckles Quote",
			url: "/quote/belt-buckles-quote",
		  },
		  {
			pageName: "Coins Quote",
			url: "/quote/coins-quote",
		  },
		  // {
		  //   pageName: "Die Cut Stickers Quote",
		  //   url: "/quote/die-cut-stickers",
		  // },
		  {
			pageName: "Medals Quote",
			url: "/quote/medals-quote",
		  },
		  {
			pageName: "Patches Quote",
			url: "/quote/patches-quote",
		  },
		  {
			pageName: "PVCpatches Quote",
			url: "/quote/PVCpatches-quote",
		  },
		  {
			pageName: "Tradingpins Quote",
			url: "/quote/tradingpins-quote",
		  },
		],
		fav: true,
		menu: false,
		message: false,
		hints: true,
		row: null,
		length: 3,
		dialog1: null,
		switch1: null,
		amenities: [1, 4],
		cards: [
		  {
			name: "Banner",
			type: "demo1",
			children: [
			  {
				url: "https://cdn.vuetifyjs.com/images/cards/house.jpg",
			  },
			],
		  },
		  {
			name: "CardImgLeft",
			type: "demo2",
			children: [
			  {
				url: "https://cdn.vuetifyjs.com/images/cards/road.jpg",
			  },
			],
		  },
		],
		deleteTarget: null,
		parentSortList: [],
		cardListOption:[
			{value:"img",text:"Image And Button"},
			{value:"btn",text:"Only Buton"},
			{value:"card",text:"The Whole Card"},
		],
		language: null,
		templateStyleList: [],
		hotList:[],
		templateNameList: [
		  { name: "All Templetes" },
		  { name: "Card Simple" },
		  { name: "Card Grid" },
		  { name: "Card Scroll"},
		  { name: "Card Tab"},
		  { name: "Card Pure"},
		  { name: "Custom" },
		  { name: "DiscountDialog" },
		  { name: "Faq" },
		  { name: "Form" },
		  { name: "Review" },
		  { name: "Summary" },
		  { name: "Summary Review" },
		  { name: "Swiper" },
		  { name: "Template" },
		  { name: "HalfList" },
		  {name:"Twins"}
		],
		resizeFontSizeList: [        //需要字体缩放的数据
		  "Common Templates",
		  "Stickersco Templates",
		  "Patchesco Templates",
		  "Keychains Templates",
		  "Neon Signs Templates",
		  "Chinese Tea Templates",
		  "Dongji Tea Templates",
		  "Lanyardsco Templates",
		  "Enamel Coins Templates",
		  "Custommedals Templates"
		],
		templateStyleSearchName: null,
		templateNameKeyWord: "",
		templateNameSearchName: null,
		websiteStyleId: null,
		isHeadBasicStyleChange: false,
		searchTemplateValue:'',
		hideSectionObjResult:"",
		isFDOrder:false,
		childIndex:null
	  };
	},
	computed: {
	  manageMobile() {
		return this.$store.getters.getManageMobile;
	  },
	  modalType() {
		return this.itemData.sampleData?.type || {}
	  },
	  modalStyle() {
		return this.itemData.sampleData?.style || {}
	  },
	  iconsData() {
		return require("@/assets/json/iconList.json");
	  },
	  fontsData() {
		return require("@/assets/json/fontList.json");
	  },
	  isVideoImage(){
		if(this.panels){
            if(['changeImage','changeVideo'].includes(this.panels)){
				return this.panels
			}else{
				return 'changeImage'
			}
		}else{
			return 'changeImage'
		}
	  },
	  computAccept(){
		if(this.panels =='changeImage'||this.panels =='changeVideo'){
			return 'image/png, image/jpeg, image/jpg, image/gif,video/mp4'
		}else{
			return 'image/png, image/jpeg, image/jpg, image/gif'
		}
	  }
	},
	watch: {
	  hideSectionObj:{
		  handler(val){
			 this.hideSectionObjResult = ''
			 if(val.isHidden === undefined) return
			 if(val.isHidden || val.isHiddenLang) this.hideSectionObjResult = 'Current Page:hide;'
			 else this.hideSectionObjResult = 'Current Page:show;'
			 if(val.isHiddenLangAll == true) this.hideSectionObjResult += '\nOthers Language:hide;'
			 if(val.isHiddenLangAll == false) this.hideSectionObjResult += '\nOthers Language:show;'
		  },
		  immediate: true,
		  deep:true
	  },
	  searchText(newText) {
		this.updateSearchResults();
		if (newText == "") this.treeData = this.pageInfo.allPage;
	  },
	  pageInfo: {
		handler(val) {
		  this.treeData = val.allPage;
		},
		immediate: true,
		deep: true,
	  },
	  canVuex: {
		handler(val) {
		  if (val) this.sysProOwnerDTO = { ...this.$store.state.proSystem };
		},
	  },
	  themeColorDraft: {
		handler(val) {
		  this.$emit("update:themeColorDraft", val);
		},
	  },
	  itemData: {
		handler(val) {
		  this.sampleType = val?.sample?.sampleType || val?.sampleType || 1;
		  // 当点击类型为button和img时，加上默认值
		  if (val?.sampleData?.type?.clickType == "button" || val?.sampleData?.type?.clickType == "img"||val?.sampleData?.type?.clickType == "video") {
			if (val?.sampleData?.type?.clickType != "button") {
			  this.videoCover = val.sampleData.type.customData.poster || ""
			  this.cutImageKey += 1
			}
			if(!val.sampleData.type?.customData?.type){
				if(val.sampleData.type?.customData?.method && Object.keys(val.sampleData.type?.customData?.method).length){
                    val.sampleData.type.customData.type = "click"
				}else {
					val.sampleData.type.customData.type = "link"
				}
			}
			let flag = val.sampleData.type?.customData?.method == undefined || Object.keys(val.sampleData.type?.customData?.method).length == 0
			if (flag) {
			  this.tempList = [["", '']]
			  this.dialogParam = {
					type:'4',
					parentCateId:'',
					quoteUrl:null,
					OtherParams:'?type=quoteIframe',
					componentName:null
				}
			} else {
			  this.tempList = []
			  let key = Object.keys(val.sampleData.type.customData.method)
			  key.forEach(item => {
				this.tempList.push([item, val.sampleData.type.customData.method[item]])
			  })
			  if(key.includes('type')){
				this.dialogParam.type = val.sampleData.type.customData.method['type']
			  }else {
				this.dialogParam.type = '4'
			  }
			  if(key.includes('quoteUrl')){
				 let flag = val.sampleData.type.customData.method['quoteUrl'].split('?')
				 if(flag&&flag.length>1){
					this.dialogParam.quoteUrl = flag[0]
					this.dialogParam.OtherParams = '?' + flag[1]
				 }else {
					this.dialogParam.OtherParams = '?type=quoteIframe'
				 }
			  }
			  if(key.includes('name'))this.dialogParam.quoteUrl = val.sampleData.type.customData.method['name']
			  if(key.includes('parentCateId'))this.dialogParam.parentCateId = val.sampleData.type.customData.method['parentCateId']
			  this.getAllQuoteSemiCateProductListByType(this.dialogParam.type)
			}
		  }
		  this.sampleNumber = val?.sample?.sampleNumber || val?.sampleNumber || null;
		  this.$emit("setItemData", { val, type: 'All' });
		},
		immediate: true,
		deep: true,
	  },
	  panels: {
		handler(val) {
		  if (!val) this.bannerPanels = "";
		  else if (val == "header") this.getHeaderList();
		  else if (val == "product_crowd") this.getProductCateList();
		  this.$emit("update:panels", val);
		},
	  },
	  colorPickerValue: {
		handler(val) {
		  if (this.twoColor) {
			let twoColorObj = {
			  "background-image": val.value3 ? `url(${val.value3})` : `linear-gradient(to bottom,${val.value1},${val.value2})`,
			  "background-color": "",
			  color1: val.value1,
			  color2: val.value2,
			  color3: val.value3
			}
			if (this.isHeadBasicStyleChange) {
			  if (this.itemData.sampleData.boxStyle) this.itemData.sampleData.boxStyle = {
				...this.itemData.sampleData.boxStyle,
				...twoColorObj
			  }
			  else this.itemData.sampleData.boxStyle = twoColorObj
			} else {
			  this.itemData.sampleData.style = {
				...this.modalStyle,
				...twoColorObj
			  };
			}
		  } else {
			let oneColorObj = {
			  "background-color": val.value1 ?  val.value1:null,
			  "background-image": val.value3 ? `url(${val.value3})` : "",
			  color1: val.value1 ? val.value1 : "",
			  color2: "",
			  color3: val.value3
			}
			if (this.isHeadBasicStyleChange) {
			  if (this.itemData.sampleData.boxStyle) this.itemData.sampleData.boxStyle = {
				...this.itemData.sampleData.boxStyle,
				...oneColorObj
			  }
			  else this.itemData.sampleData.boxStyle = oneColorObj
			} else {
			  this.itemData.sampleData.style = {
				...this.modalStyle,
				...oneColorObj
			  }
			}
		  }
		  this.$emit("setItemData", { val: this.itemData, type: this.isHeadBasicStyleChange ? 'BoxStyle' : 'Color' });
		},
		deep: true,
	  },
	  fontColorValue: {
		handler(val) {
		  if (this.isHeadBasicStyleChange) {
			if (this.itemData.sampleData.boxStyle) this.itemData.sampleData.boxStyle = {
			  ...this.itemData.sampleData.boxStyle,
			  color: val.value,
			  fontColor: val.value,
			}
			else this.itemData.sampleData.boxStyle = {
			  color: val.value,
			  fontColor: val.value,
			}
			this.$emit("setItemData", { val: this.itemData, type: 'BoxStyle' });
		  } else {
			this.itemData.sampleData.style = {
			  ...this.modalStyle,
			  color: val.value,
			  fontColor: val.value,
			};
			this.$emit("setItemData", { val: this.itemData, type: 'Color' });
		  }
		},
		deep: true,
	  },
	},
	created() {
	  this.getListWebsiteStyleBySample()
	},
	mounted() {
	  this.proId = this.$store.getters["manage/getProId"];
	  this.getHeaderList();
	},
	methods: {
		linkChangeFn(res){
			this.modalType.customData.value = res;
			this.$refs['cutImage1'].comeFromGF()
			if (this.manageMobile) this.$emit("getIframeMBDataFromNavigationFun", { type: "img", data: res })
		},
		//必须清空uploadType不然会因为异步操作造成编辑异常
		imgVideoHandler(){
			this.uploadType = '';
			this.$refs.uploads.value = "";
			this.$refs.cutImage1.comeFromGF();
		},
		checkFileType,
		// 点击切换dialogType
		changeDialogType(value){
			this.tempList.forEach((item,index)=>{
				if(item[0]=='type') this.$set(this.tempList,index,['type',value])
			})
			this.dialogParam.type =value
			this.dialogParam.quoteUrl = null
			this.getAllQuoteSemiCateProductListByType(value)
		},
		//  根据类型来调用接口
	    async getAllQuoteSemiCateProductListByType(type){
			if(type ==4) return
			this.loadDialog = true
			let params = {
				proId:this.proId,
				getShelf:1
			}
            if(type == 1){
                params.type = 1
			}else if(type ==2){
				params.type = 4
			}else if(type ==3){
				params.type = 5
			}else{
				if(this.dialogParam.parentCateId) params.parentCateId = this.dialogParam.parentCateId
				params.type = 7
			}
			const {data} = await getAllQuoteSemiCateProductList(params)
			if(type==3){
                this.parentQuoteUrlList = data
				if(this.dialogParam.parentCateId) this.getAllQuoteSemiCateProductListByType()
			}else if(type ==2){
				this.dialogQuoteUrlList = []
				data.forEach(item=>{
                    this.dialogQuoteUrlList.push(...[item,...item.childList])
				})
			}else{
				this.dialogQuoteUrlList = data
			}
			this.loadDialog = false
		},
	   getImageWidthHeight(url){
		const imageExtensions = /\.(jpg|jpeg|png|gif|webp)$/i;
        if(url&&(typeof url == 'string')&&url.startsWith("http")&&imageExtensions.test(url)){
			try {
				let img = new Image();
				img.src =url;
				if(img.width&&img.height){
					return img.width+ '/' + img.height
				}else{
					return ''
				}
			} catch (error) {
				return ''
			}
		}else return ''
	  },
	  uploadDownload(){
		this.$emit("update:panels", "")
		setTimeout(() => {
			this.$emit("update:panels", "uploadDownload")
		}, 200)
	  },
	  downloadLogHandler(){
		const a = document.createElement("a");
		a.href = process.env.baseUrl + `/feign/page/pageRowDraft/exportPageRowDraftData?prId=${this.itemData.id}`;
		a.click();
		a.remove();
	  },
	  uploadLogHandler(){
        this.$refs.uploads1.click()
	  },
	  // 新增模版点击搜索或者回车时操作
	  searchTemplateHandler(){
		  this.pageForm.page = 1
		  this.changePageFun()
	  },
	  // 编辑模块显示隐藏
	  hideSectionHandler(){
		  this.$emit("update:panels", "")
		  setTimeout(() => {
			  this.hideSectionObj.isHidden = this.itemData.isHidden
			  this.hideSectionObj.isHiddenLang = this.itemData.isHiddenLang
			  this.hideSectionObj.isHiddenLangAll = this.itemData.isHiddenLangAll
			  this.$emit("update:panels", "HideSection")
		  }, 200)
	  },
	  changeSectionState(){
		  this.itemData.isHidden = this.hideSectionObj.isHidden
		  this.itemData.isHiddenLang = this.hideSectionObj.isHiddenLang
		  this.itemData.isHiddenLangAll = this.hideSectionObj.isHiddenLangAll
		  this.$emit("update:panels", "")
		  this.$emit("setItemData", { val: this.itemData, type: 'HIDESECTION' });
	  },
	  // 修改全部语言的显示隐藏
	  changeIsHiddenLangAll(val){
		 if(val == null)return
		 else this.hideSectionObj.isHiddenLang = val
		 this.$forceUpdate()
	  },
	  //添加角标触发
	   addCornerLabel(v){
		this.childIndex = v.childIndex - 0
		this.cornerLabelId = null
		setTimeout(async () => {
		if(!this.itemData.sampleData.list[this.childIndex]) return
		if(!this.itemData.sampleData.list[this.childIndex]?.cornerLabelType){
			this.$set(this.itemData.sampleData.list[this.childIndex],'cornerLabelType','left')
		}
		// 没有cornerLabel不操作，没有cornerLabel.id不操作
		if(this.itemData.sampleData.list[this.childIndex]?.cornerLabel&&this.itemData.sampleData.list[this.childIndex]['cornerLabel']?.id){
			this.cornerLabelId = this.itemData.sampleData.list[this.childIndex]['cornerLabel'].id
        }
		const {data} = await getListCornerLabel()
		this.cornerLabelList = [...data,{id:"NO",name:'Setting'}]
		this.$emit("update:panels", "addCornerLabel")
		}, 200)
	  },
	  changeCornerLabelType(value){
		this.$set(this.itemData.sampleData.list[this.childIndex],'cornerLabelType',value)
	  },
	  changeCornerLabel(value){
		if(value.id =='NO'){
			this.cornerLabelId = value.id
			if(this.itemData.sampleData.list[this.childIndex]?.cornerLabel){
				this.$set(this.itemData.sampleData.list[this.childIndex],'cornerLabel',null)
			}
			return
		}
		this.cornerLabelId = value.id
		if(!this.itemData.sampleData.list[this.childIndex]?.cornerLabel){
			this.$set(this.itemData.sampleData.list[this.childIndex],'cornerLabel',{})
        }
		this.$set(this.itemData.sampleData.list[this.childIndex]['cornerLabel'],'id',value.id)
		this.$set(this.itemData.sampleData.list[this.childIndex]['cornerLabel'],'color',value.color?value.color:null)
		this.$set(this.itemData.sampleData.list[this.childIndex]['cornerLabel'],'value',value.name?value.name:null)
		this.$set(this.itemData.sampleData.list[this.childIndex]['cornerLabel'],'icon',value.icon?value.icon:null)
		this.$set(this.itemData.sampleData.list[this.childIndex]['cornerLabel'],'bgImg',value.bgImg?value.bgImg:null)
	  },
	  //模版编辑左右边距
	  editPadding(){
		  this.LRPadding = 1400
		  this.paddingHasChange = false
		  this.$emit("update:panels", "")
		  setTimeout(() => {
			  this.$emit("update:panels", "LRPadding")
			  this.originPadding = {}
			  // 有可能当前padding不是paddingstep里面的档位，所以需要存储最原始的padding给复原操作
			  if(this.itemData.sampleData.name == 'Banner'){
				  if(this.itemData.sampleData.contentStyle){
					  Object.keys(this.itemData.sampleData.contentStyle).forEach(item=>{
						  // right-padding,rightPadding,padding
						  if(item.includes('padding')||item.includes('Padding')) this.originPadding[item] = this.itemData.sampleData.contentStyle[item]
					  })
				  }
			  }else{
				  if(this.itemData.sampleData.style){
					  Object.keys(this.itemData.sampleData.style).forEach(item=>{
						  if(item.includes('padding')||item.includes('Padding')) this.originPadding[item] = this.itemData.sampleData.style[item]
					  })
				  }
			  }
		  }, 200)
	  },



	  // 改变左右边距
	  changeLRPadding(e){
		  let stepPadding = `max(calc(50% - 700px),3vw)`
		  if(e){
			stepPadding = `max(calc(50% - ${e/2}px),3vw)`
		  }else{
			stepPadding = `max(calc(50% - 700px),3vw)`
		  }
		  this.paddingHasChange = true
		  let type = this.itemData.sampleData.name == 'Banner' ? 'contentStyle' : 'style'
		  if(this.itemData.sampleData[type]){
			  this.itemData.sampleData[type] = {
				  ...this.itemData.sampleData[type],
				  'padding-left':stepPadding,
				  'padding-right':stepPadding
			  }
		  }else{
			  this.itemData.sampleData[type] = {
				  'padding-left':stepPadding,
				  'padding-right':stepPadding
			  }
		  }
		  this.$emit("setItemData", { val: this.itemData, type: 'LRPADDING' });
	  },
	  // 需要删除padding-left
	  resetPadding(){
		  this.paddingHasChange = false
		  let type = this.itemData.sampleData.name == 'Banner' ? 'contentStyle' : 'style'
		  if(this.itemData.sampleData[type]){
			  let temp = JSON.parse(JSON.stringify(this.itemData.sampleData[type]))
			  delete temp['padding-left']
			  delete temp['padding-right']
			  Object.keys(this.originPadding).forEach(item=>{
				  temp[item] = this.originPadding[item]
			  })
			  this.itemData.sampleData[type] = temp
			  this.$emit("setItemData", { val: this.itemData, type: 'LRPADDING' })
		  }
		  // this.LRPadding = 1400
	  },
	  // 改变上边距
	  changeTPadding(e){
		  let stepPadding = `5em`;
		  if(e) stepPadding = `${e/10}em`;
		  else stepPadding = `5em`;

		  this.paddingHasChange = true;
		  let type = this.itemData.sampleData.name == 'Banner' ? 'contentStyle' : 'style';
		  if(this.itemData.sampleData[type]) this.itemData.sampleData[type] = {
				  ...this.itemData.sampleData[type],
				  'padding-top':stepPadding
			  };
		  else this.itemData.sampleData[type] = {
				  'padding-top':stepPadding
		  };
		  this.$emit("setItemData", { val: this.itemData, type: 'LRPADDING' });
	  },
	  // 改变下边距
	  changeBPadding(e){
		  let stepPadding = `5em`;
		  if(e) stepPadding = `${e/10}em`;
		  else stepPadding = `5em`;

		  this.paddingHasChange = true;
		  let type = this.itemData.sampleData.name == 'Banner' ? 'contentStyle' : 'style';
		  if(this.itemData.sampleData[type]) this.itemData.sampleData[type] = {
				  ...this.itemData.sampleData[type],
				  'padding-bottom':stepPadding
			  };
		  else this.itemData.sampleData[type] = {
				  'padding-bottom':stepPadding
		  };
		  this.$emit("setItemData", { val: this.itemData, type: 'LRPADDING' });
	  },



	  //跳出弹框
		async ringCateAndTagList(){
		  // 回显,判断checkTag，和checkHalfDetail，false不展示多选
		  this.ringCateId = ''
		  this.ringTagValue = []
		  this.linkType = ''
		  this.classHasTag = false
		  this.checkHalfDetail =false
		  if(this.itemData.sampleData){
			  this.checkHalfDetail  = this.itemData.sampleData.checkHalfDetail || false
			  if(this.checkHalfDetail){
				  this.getAllProductListByFlag(this.itemData.sampleData)
			  }else{
				  const {data} = await getAppRingCateAndTagList()
				  this.ringCateIdList = []
				  this.ringCateTagMap = new Map()
				  if(data&&data.length < 1) return
				  data.forEach(item=>{
					  this.ringCateIdList.push({value:item.id,text:item.cateName})
					  this.ringCateTagMap.set(item.id,item.quoteRingTagList)
				  })
				  this.getParentSortList()
				  if(!this.itemData.sampleData.parentIndex) this.itemData.sampleData.parentIndex = ''
				  if(!this.itemData.sampleData.parentTemplate) this.itemData.sampleData.parentTemplate = false
				  if(!this.itemData.sampleData.childTemplate) this.itemData.sampleData.childTemplate = false
				  this.classHasTag = this.itemData.sampleData.checkTag || false
				  if(this.itemData.sampleData.cateId){
					  this.ringCateId = this.itemData.sampleData.cateId
					  if(this.classHasTag)this.ringTagList = this.ringCateTagMap.get(this.ringCateId) || []
				  }
				  if(this.itemData.sampleData.linkType) this.linkType = this.itemData.sampleData.linkType
				  if(!this.classHasTag) return
				  if(this.itemData.sampleData.tabList && this.itemData.sampleData.tabList.length>0) this.ringTagValue = this.itemData.sampleData.tabList
			  }
		  }
	  },
	  // 获取产品列表
	  async getAllProductListByFlag(val){
		  this.halfCateMap = new Map()
		  this.halfCateList = []
		  this.halfChildrenList = []
		  let params = {
			  language:'en-us',
			  proId:this.proId
		  }
		  // 获取父子产品列表
		  const {data} = await getProductRetailerAllHalfCate(params)
		  if(data&&data.length>0){
			  data.forEach(item =>{
				  this.halfCateList.push({value:item.id,text:item.name})
				  this.halfCateMap.set(item.id,item.childList)
			  })
		  }
		  // 回显操作
		  if(val.categoryId){
			  this.categoryId = val.categoryId
			  if(this.halfCateMap.get(val.categoryId)){
				  if(val.childCateId){
					  this.childCateId = val.childCateId
					  this.halfChildrenList = this.halfCateMap.get(val.categoryId)
					  await this.changeChildcateId(this.childCateId)
					  if(val.productIdList){
						  let temp = val.productIdList.split(',')
						  if(temp && temp.length>0){
							  temp.forEach(item=>{
								  this.productIdValue.push(item - 0)
							  })
						  }else{
							  this.productIdValue = []
						  }
					  }
				  }else{
					  this.childCateId = ''
					  this.productIdValue = []
				  }
			  }
		  }else{
			  this.categoryId = ''
			  this.childCateId = ''
			  this.productIdValue = []
		  }
	  },
	  //修改categoryId,childcateId和productIdValue都要置为空
	  changeCategoryId(value){
		  this.halfChildrenList = []
		  this.childCateId = ''
		  this.productIdValue = []
		  this.itemData.sampleData.categoryId = value
		  if(this.halfCateMap.has(value)) this.halfChildrenList = this.halfCateMap.get(value)
		  this.itemData.sampleData.childCateId = ''
		  this.itemData.sampleData.productIdList = ''
	  },
	  //修改childcateId,productIdValue置为空
	  async changeChildcateId(value){
		  this.productIdList = []
		  this.productIdValue = []
		  this.itemData.sampleData.childCateId = value
		  this.itemData.sampleData.productIdList = ''
		  let params = {
			  id:value,
			  language:'en-us',
			  proId:this.proId
		  }
		  getProductListByChildCateId(params).then(res=>{
			  if(res.code == 200){
				  const {data} = res
				  this.productIdList = data
			  }
		  })
	  },
	  changeProductIdValue(value){
		  if(Array.isArray(value)){
			  this.itemData.sampleData.productIdList = value.join(',')
		  }else{
			  this.itemData.sampleData.productIdList = ''
		  }
	  },
	  //修改页头打开方式
	  changeLinkType(value){
		  this.itemData.sampleData.linkType = value
	  },
	  // 修改CateId
	  changeRingCateId(value){
		  this.itemData.sampleData.cateId = value
		  if(!this.classHasTag) return
		  this.ringTagValue = []
		  this.itemData.sampleData.tabList = []
		  if(this.ringCateTagMap.has(value)) this.ringTagList = this.ringCateTagMap.get(value) || []
	  },
	  // 修改tagLIST
	  changeRingTagValue(value){
		  this.itemData.sampleData.tabList = value
	  },
	  // BannerQuote和QuickQuote互斥操作
	  changeBannerQuoteHandler(val){
		  val==1 ? this.itemData.sampleData.isQuickQuote = 0 : this.itemData.sampleData.isQuickQuote = 1
	  },
	  changeQuickQuoteHandler(val){
		  val==1 ? this.itemData.sampleData.isBannerQuote = 0 : this.itemData.sampleData.isBannerQuote = 1
	  },
	  // 修改quote_table，进行数据回显
	  async quoteTableEdit () {
		  this.quotePidList = []
		  this.PidCateIdMap = new Map()
		  this.isFDOrder = this.itemData.sampleData.isFDOrder ? true : false
		  if(this.isFDOrder){
			  const { data } = await getAll({isFd:1})
			  if (data && data.length < 1) return
			  data.forEach(item => {
				  this.quotePidList.push({ value: item.id, text: item.cateName })
				  this.PidCateIdMap.set(item.id, item.childList)
			  })
		  }else{
			  const { data } = await getAll()
			  if (data && data.length < 1) return
			  data.forEach(item => {
				  this.quotePidList.push({ value: item.id, text: item.cateName })
				  this.PidCateIdMap.set(item.id, item.childList)
			  })
		  }
		  //清空数据
		  this.quoteValue = ''
		  this.quoteCateValue = ''
		  this.quoteCateIdList = []
		  // 回显没有参数默认赋值isBannerQuote和isQuickQuote互斥，都有无值默认isBannerQuote为0
		  if (!this.itemData.sampleData.isBannerQuote || this.itemData.sampleData.isQuickQuote == 1) {
			  this.itemData.sampleData.isBannerQuote = 0
			  this.itemData.sampleData.isQuickQuote = 1
		  }
		  if (!this.itemData.sampleData.isQuickQuote || this.itemData.sampleData.isBannerQuote == 1) {
			  this.itemData.sampleData.isBannerQuote = 1
			  this.itemData.sampleData.isQuickQuote = 0
		  }
		  if (this.itemData.sampleData.quotePid) {
			  this.quoteValue = this.itemData.sampleData.quotePid
			  if (this.PidCateIdMap.has(this.quoteValue)) {
				  this.quoteCateIdList = this.PidCateIdMap.get(this.quoteValue) || []
				  if (this.itemData.sampleData.quoteCateId) this.quoteCateValue = this.itemData.sampleData.quoteCateId
			  }
		  }
	  },
	  //修改报价大分类
	  changeQuotePid(value){
		  this.itemData.sampleData.quotePid = value
		  this.quoteCateValue = ''
		  this.itemData.sampleData.quoteCateId = ''
		  if(this.PidCateIdMap.has(value)) this.quoteCateIdList = this.PidCateIdMap.get(value) || []
	  },
	  //修改报价子分类
	  changeCateValue(value){
		  this.itemData.sampleData.quoteCateId = value
	  },
	  // 添加tempList的method属性
	  AddPropFn() {
		this.$set(this.tempList, this.tempList.length, ["", ""])
	  },
	  // 保存tempList的method属性
	  SavePropFn() {
		let obj = {}
		obj.type = this.dialogParam.type
		if(!this.dialogParam.quoteUrl&&obj.type!=4) {
			this.$message.error("Error quoteUrl")
			return
		}
        if(this.dialogParam.type ==1){
			if(!this.dialogParam.OtherParams) this.dialogParam.OtherParams = '?type=quoteIframe'
            obj.modal = 'modalQuoteDialog'
			obj.quoteUrl = this.dialogParam.quoteUrl + this.dialogParam.OtherParams
		}else if(this.dialogParam.type==2){
            obj.modal = 'modalQuoteHalfDetail'
			obj.name = this.dialogParam.quoteUrl
			obj.componentName = "modalHalfList4"
		}else if(this.dialogParam.type ==3){
			obj.name = this.dialogParam.quoteUrl
            obj.modal = 'modalQuoteHalfDetail'
			obj.parentCateId = this.dialogParam.parentCateId
		}else if(this.dialogParam.type ==4){
			this.tempList.forEach(item => {if (item[0].trim().length != 0) obj[item[0]] = item[1]})
		}
		this.$emit("setItemData", { val: obj, type: 'dialog' })
		this.$emit("update:panels", "");
		this.dialogParam = {
			type:'4',
			parentCateId:'',
			quoteUrl:null,
			OtherParams:'?type=quoteIframe',
			componentName:null
		}
	  },
	  clearImgCover() {
		this.modalType.customData.poster = "";
		this.cutImageKey += 1
		this.$emit("refreshComponents")
	  },
	  isObjHasKey(obj, propertyName) {
		return obj?.hasOwnProperty(propertyName);
	  },
	  jumpToPage() {
		const page = Math.max(1, Math.min(this.pageForm.pageLength, Number(this.pageForm.keyWord)));
		this.pageForm.page = page;
		this.changePageFun()
		// this.pageForm.keyWord = null; // 清空输入框
	  },
	  // 校验是否是图片链接
	  isImgUrl(url) {
		const imageExtensions = ['png', 'jpg', 'jpeg', 'gif', 'webp', 'bmp', 'svg']
		const pattern = `^(http|https)?:\/\/.*\.(${imageExtensions.join('|')})$`
		const regex = new RegExp(pattern, 'i')
		return !url ? false : regex.test(url)
	  },
	  searchTree(node, targetName) {
		if (node.pageName.toLowerCase().includes(targetName)) {
		  this.searchResults.push(node);
		  this.treeData = this.searchResults
		}

		if (node.childList) {
		  for (const child of node.childList) {
			this.searchTree(child, targetName);
		  }
		}
	  },
	  updateSearchResults() {
		this.searchResults = [];
		const inputValue = this.searchText?.toLowerCase();
		for (const node of this.pageInfo.allPage) {
		  this.searchTree(node, inputValue);
		}
	  },
	  findObjectById(arr, id) {
		let result = null;

		arr.find(item => {
		  if (item.id === id) {
			result = item;
			return true;
		  }
		  if (item.childList) {
			result = this.findObjectById(item.childList, id);
			return result !== null;
		  }
		  return false;
		});

		return result;
	  },
	  choosePage(valArr) {
		this.headerFormData.pageId = valArr[0]
		// let chooseName = this.pageInfo.allPage.find(i=>i.id==this.headerFormData.pageId).pageName
		let findObj = this.findObjectById(this.pageInfo.allPage, valArr[0])
		this.chooseName = findObj?.pageName
		if (!this.chooseName) return //防止pageId为空的情况下，findObj找不到导致menu打不开
		this.isOpenPageMenu = false
	  },
	  // onIntersect(entries, observer) {
	  //   if (this.dialogForCustomList) {
	  //     this.customListForm.page += 1;
	  //     this.getQuoteParentCateList();
	  //   }
	  // },
	  removeImageFun() {
		this.colorPickerValue.value3 = "";
		this.$nextTick(this.$refs.cutImage4.comeFromGF);
	  },
	  twoColorFun(val) {
		if (!val) this.colorPickerValue.value2 = "";
	  },
	  cleanFontColorPickerFun(isHeadBasicStyleChange = false) {
		this.isHeadBasicStyleChange = isHeadBasicStyleChange
		this.$emit("update:panels", "");
		setTimeout(() => {
		  this.$emit("update:panels", "fontColor");
		  if (this.isHeadBasicStyleChange) {
			if (this.itemData.sampleData.boxStyle) {
			  this.fontColorValue.value = this.itemData.sampleData.boxStyle.fontColor || "#333333FF";
			} else this.fontColorValue.value = "#333333FF";
		  } else {
			if (Object.keys(this.modalStyle).length != 0) this.fontColorValue.value = this.modalStyle.fontColor || "#333333FF";
			else this.fontColorValue.value = "#333333FF";
		  }
		}, 200);
	  },
	  cleanColorPickerFun(isHeadBasicStyleChange = false) {
		this.isHeadBasicStyleChange = isHeadBasicStyleChange
		this.$emit("update:panels", "");
		setTimeout(() => {
		  this.$emit("update:panels", "background");
		  if (this.isHeadBasicStyleChange) {
			let params = !!this.itemData.sampleData.boxStyle ? this.itemData.sampleData.boxStyle : false
			this.getColorDefault(params)
		  } else {
			if (Object.keys(this.modalStyle).length != 0) {
			  this.getColorDefault(this.modalStyle)
			} else {
			  this.getColorDefault()
			}
		  }
		}, 200);
	  },
	  //根据条件获取颜色选择器的默认值
	  getColorDefault(obj = false) {
		if (obj) {
		  this.colorPickerValue.value1 = obj.color1 ? obj.color1 : "#FFFFFFFF";
		  this.colorPickerValue.value2 = obj.color2 ? obj.color2 : "";
		  if (obj.color3) {
			this.colorPickerValue.value3 = obj.color3;
			try {
			  this.$nextTick(this.$refs.cutImage4.comeFromGF);
			} catch (error) {
			  console.log(error);
			}
		  } else this.colorPickerValue.value3 = "";
		  if (obj.color2) this.twoColor = true;
		  else {
			this.twoColor = false;
			this.colorPickerValue.value2 = "";
		  }
		} else {
		  this.twoColor = false;
		  this.colorPickerValue.value1 = "#FFFFFFFF";
		  this.colorPickerValue.value2 = "";
		  this.colorPickerValue.value3 = "";
		}
	  },
	  getMsrcFun(num) {
		this.$refs['uploads'].click();
		this.uploadType = num;
	  },
	  async sortList(val) {
		let postData = await this.sortArr(val);
		sortHeader(postData).then((res) => { });
	  },
	  sortArr(arr) {
		return new Promise(async (resolve, reject) => {
		  arr.forEach((item, index) => {
			item.sort = index + 1;
			if (item.children.length > 0) this.sortArr(item.children);
		  });
		  resolve(arr);
		});
	  },
	  colorPickerFun(val) {
		this.headerFormData.labelColor = val.hex;
	  },
	  colorPickerFun1(val) {
		// this.headerFormData.nameColor = val.hex;
		this.$set(this.headerFormData,'nameColor',val.hex)
	  },
	  filterArr() {
		let arr1 = this.modalType.customDataParent;
		this.productCateList = this.productCateListALL.filter(v => arr1.every((e) => v.signId != e.product_crowd.signId?.value));
	  },
	  getProductCateList() {
		let groupCateId;
		if (this.pageType == 1 && this.cateType == 0) groupCateId = this.cateId;
		else groupCateId = 0;

		let postData = {
		  proId: this.proId,
		  audienceId: this.audienceId,
		  language: null,
		  groupCateId: groupCateId,
		},
		  itemData = this.modalType?.clickType;
		if (itemData == "product_crowd") getProductCateList(postData).then((res) => {
		  this.productCateListALL = res.data;
		  this.filterArr();
		});
		else if (itemData == "sticker_crowd") getStickerProductCateList(postData).then((res) => {
		  this.productCateListALL = res.data;
		  this.filterArr();
		});
	  },

	  filterTotalCustomList() {
		let temp = [];
		this.tempAddCustomList.map((x) => {
		  if (!x.cateType) temp.push(x.id + "-0");
		  else temp.push(x.id + "-1");
		});
		return temp;
	  },

	  deleteCustomList(item) {
		let index = this.tempAddCustomList.findIndex(x => x.id == item.id);
		this.tempAddCustomList.splice(index, 1);
		this.$emit("updateTotalCustomList", this.filterTotalCustomList());
	  },
	  sortCustomListFun(val) {
		this.tempAddCustomList = val;
		this.$emit("updateTotalCustomList", this.filterTotalCustomList());
	  },
	  pickUpProductFun(arr, item, index) {
		arr.splice(index, 1);
		this.tempAddCustomList.push(item);
		this.$emit("updateTotalCustomList", this.filterTotalCustomList());
	  },
	  shutDialogForCustomList() {
		this.dialogForCustomList = false;
		this.getQuoteParentCateList(false);
	  },
	  shutDialogForHalfCustomList() {
		this.dialogForHalfCustomList = false;
		this.getSemiProductList(false);
	  },
	  addHalfCustomListFun() {
		this.getSemiProductList();
	  },
	  addCustomListFun(item, index) {
		this.getQuoteParentCateList();
	  },
	  //已选数据
	  getSemiQuoteProductList() {
		let postData = {
		  proId: this.proId,
		  cateIdList: this.totalCustomList.join(),
		};
		getSemiQuoteProductList(postData).then((res) => {
		  this.tempAddCustomList = res.data.data;
		  this.filterTotalCustomList();
		});
	  },

	  //获取父子分类
	  async getParentSortList() {
		await getAppParamType({
		  paramType: "NEONSAMPLE",
		  // language: "en-us",
		}).then((res) => {
		  this.parentSortList = res.data.map((i) => {
			i.childList.forEach(child => child.bandParamIdListLength = child.bandParamIdList.split(",").length);
			return i;
		  });
		});
	  },

	  // 修改父分类
	  handleChangeParentType(id) {
		this.itemData.sampleData.parentIndex = id;
	  },

	  //半定制 1
	  getSemiProductList(open = true) {
		this.loadingForHalfCustomList = true;
		let temp = [];
		// this.addCustomList.map((x) => {
		//   temp.push(x.id);
		// });
		this.tempAddCustomList.map((x) => {
		  if (x.cateType) temp.push(x.id);
		});
		let postData = {
		  proId: this.proId,
		  type: 0,
		  productIdList: temp.join(),
		  page: this.halfCustomListForm.page,
		  pageSize: this.halfCustomListForm.pageSize,
		  keyWord: this.halfCustomListForm.keyWord,
		};
		getSemiProductList(postData).then((res) => {
		  this.originHalfCustomList = res.data.data.records;
		  this.halfCustomListForm.pageLength = res.data.data.pages;
		  this.originHalfCustomList.map((x) => {
			x.cateType = 1;
		  });
		  if (open) this.dialogForHalfCustomList = true;
		  this.loadingForHalfCustomList = false;
		});
	  },
	  //全定制 0
	  getQuoteParentCateList(open = true) {
		this.loadingForCustomList = true;
		let temp = [];
		// this.addHalfCustomList.map((x) => {
		//   temp.push(x.id);
		// });
		this.tempAddCustomList.map((x) => {
		  if (!x.cateType) temp.push(x.id);
		});
		let postData = {
		  proId: this.proId,
		  cateIdList: temp.join(),
		  keyWord: this.customListForm.keyWord,
		};
		getQuoteParentCateList(postData).then((res) => {
		  this.originCustomList = res.data.data;
		  this.originCustomList.map(x => x.cateType = 0);
		  if (open) this.dialogForCustomList = true;
		  this.loadingForCustomList = false;
		});
	  },
	  getApiProductFun(val) {
		this.productForm.link = val.url;
		this.productForm.productType = val.productType;
		this.productForm.img = val.imagePhoto;
		this.productForm.id = val.id;
		this.productForm.title = val.cateName;
		this.productForm.alt = val.alt;
		this.productForm.disabled = val.disabled;
		this.productForm.signId = val.signId;
		this.productForm.img2 = val.img2;
		this.productForm.alt2 = val.alt2;
		this.productForm.title2 = val.title2;
		this.showForm = true;
		this.$nextTick(this.$refs.cutImage3.comeFromGF);
	  },
	  cleanProductForm() {
		return new Promise((resolve, reject) => {
		  this.showForm = true;
		  this.$nextTick(() => {
			this.$refs.form?.reset();
			this.$refs.form?.resetValidation();
			resolve();
		  });
		});
	  },
	  //点击添加
	  async needNewProduct() {
		await this.cleanProductForm();
		this.showForm = false;
		this.filterArr();
		this.$nextTick(() => {
		  if (this.$refs.cutImage3) this.$refs.cutImage3.clean();
		});
	  },
	  //swiper列表
	  comeSwiperForm() {
		if(this.itemData&&this.itemData.sampleData&& !this.itemData.sampleData.autoplay){
			this.itemData.sampleData.autoplay = 0
		}
		this.swiperList = [...this.modalType.customDataParent];
		// 赋予默认值
		this.swiperList.forEach(item=>{
		  if(!item.title) item.title = {}
		  if(!item.subTitle) item.subTitle = {}
		})
	  },
	  newSwiperFun() {
		if(this.swiperList.length == 0){
            this.swiperList.push({
				img:{value:'',alt:''},
				title:{value:''},
				subTitle:{value:''}
			})
		}else{
			let obj = this.swiperList[this.swiperList.length - 1]
		    if(obj)this.swiperList.push(JSON.parse(JSON.stringify(obj)));
		}
	  },
	  deleteSwiperFun(index) {
		this.swiperList.splice(index, 1);
	  },
	  updateSwiperFun() {
		let tempArr = this.modalType.customDataParent;
		tempArr.splice(0, tempArr.length);
		this.swiperList.forEach(item => tempArr.push(item));
		this.$emit("update:panels", "");
	  },


	  //img列表
	  comeImgListForm() {
		this.imgList = [...this.modalType.customData];
	  },
	  // 删除imgList项目
	  deleteImgListFun(index) {
		this.imgList.splice(index, 1);
		if (this.itemData.sampleData.list[this.modalType.index]) this.itemData.sampleData.list[this.modalType.index].imgIndex = 0;
	  },
	  // 添加imglist该项
	  addImgListFun(index){
		let obj = JSON.parse(JSON.stringify(this.imgList[index]))
		this.imgList.splice(index + 1,0,obj)
	  },
	  // 将数据向上移动
	  upImgListFun(index){
		if(index ==0) return
        let obj1 = this.imgList[index]
        let obj2 = this.imgList[index -1]
		this.imgList.splice(index - 1,1,obj1)
		this.imgList.splice(index,1,obj2)
	  },
	  // 将数据向下移动
	  downImgListFun(index){
		if(index == this.imgList.length - 1) return
        let obj1 = this.imgList[index]
        let obj2 = this.imgList[index + 1]
		this.imgList.splice(index + 1,1,obj1)
		this.imgList.splice(index,1,obj2)
	  },
	  updateImgListFun() {
		let tempArr = this.modalType.customData;
		tempArr.splice(0, tempArr.length);
		this.imgList.forEach(item => tempArr.push(item));
		this.$emit("setItemData",{ val: this.itemData, type: 'MBImgList',list:tempArr})
		this.$emit("update:panels", "");
	  },
	  // 修改card_list
      cardListHandler(){
		if(this.itemData.sampleData){
			this.itemData.sampleData.linkArea?this.itemData.sampleData.linkArea:this.itemData.sampleData.linkArea = 'img'
		}
	  },
	  handleChangeLinkArea(val){
		this.itemData.sampleData.linkArea = val
	  },
	  changeMarginColumn(val,type){
		let number = Number(val.target.value || 0)
		if(number < 0 ) number = 0
		if(type == 1){
			this.$set(this.itemData.sampleData,"margin",number)
		}else{
			this.$set(this.itemData.sampleData,"column",number)
		}
	  },
	  //nav列表
	  comeNavForm() {
		this.navList = [...this.modalType.customData];
		this.navListData = JSON.parse(JSON.stringify(this.itemData.sampleData.list));
	  },
	  //navlist一定有值，如果list有值删值后续修改tab的索引，无值不操作。
	  deleteNavFun(index) {
		let navData = this.navListData.filter(item => item.tab != index);
		navData.forEach(item => {
		   if (item.tab > index) item.tab = item.tab - 1;
		})
		this.navListData = [...navData]
		this.navList.splice(index, 1);
	  },
	  // 添加navlist，同时需要将该tab的数据重新复制一份,后续小于该index的值全部加1
	  addNavFun(index){
		let navData = this.navListData.filter(item => item.tab == index);
		let navDataCopy = JSON.parse(JSON.stringify(navData));
		this.navListData.forEach(item=>{
			if(item.tab >= index) item.tab = item.tab + 1
		})
		this.navListData.push(...navDataCopy);
		let obj = JSON.parse(JSON.stringify(this.navList[index]))
		this.navList.splice(index + 1,0,obj)
	  },
	   // 向上移动该项，同时需要将该tab的数据重新复制一份
	  upNavFun(index){
		if(index == 0) return
		let filterData = this.navListData.filter(item => item.tab != index && item.tab != index-1);
		let navData = this.navListData.filter(item => item.tab == index);
		let navDataCopy = JSON.parse(JSON.stringify(navData))
		navDataCopy.forEach(item=>item.tab = item.tab - 1)
		let navData1 = this.navListData.filter(item => item.tab == index-1);
		let navDataCopy1 = JSON.parse(JSON.stringify(navData1))
		navDataCopy1.forEach(item=> item.tab = item.tab + 1)
		this.navListData = [...filterData,...navDataCopy,...navDataCopy1]
		let obj = this.navList[index]
		let obj1 = this.navList[index -1]
		this.navList.splice(index - 1,1,obj)
		this.navList.splice(index,1,obj1)
	  },
	    // 向下移动该项，同时需要将该tab的数据重新复制一份
	  downNavFun(index){
        if(index == this.navList.length-1)return
		let filterData = this.navListData.filter(item => item.tab != index && item.tab != index+1);
		let navData = this.navListData.filter(item => item.tab == index);
		let navDataCopy = JSON.parse(JSON.stringify(navData))
		navDataCopy.forEach(item=>item.tab = item.tab + 1)
		let navData1 = this.navListData.filter(item => item.tab == index+1);
		let navDataCopy1 = JSON.parse(JSON.stringify(navData1))
		navDataCopy1.forEach(item=> item.tab = item.tab - 1)
		this.navListData = [...filterData,...navDataCopy1,...navDataCopy]
		let obj = this.navList[index]
		let obj1 = this.navList[index + 1]
		this.navList.splice(index + 1,1,obj)
		this.navList.splice(index,1,obj1)
	  },
	  updateNavListFun() {
		let tempArr = this.modalType.customData;
		tempArr.splice(0, tempArr.length);
		this.navList.forEach(item => tempArr.push(item))
		this.itemData.sampleData.list = this.navListData
		this.$emit("setItemData", { val: this.itemData, type: 'MBNavList'});
		this.$emit("update:panels", "");
	  },


	  //price列表
	  comePriceForm() {
		this.priceList = [...this.modalType.customData];
	  },
	  // 删除pricelist该item项目
	  deletePriceFun(index) {
		this.priceList.splice(index, 1);
	  },
	  // 添加pricelist项目
	  addPriceFun(index){
		let obj = JSON.parse(JSON.stringify(this.priceList[index]))
		this.priceList.splice(index + 1,0,obj)
	  },
	  //向上移动pricelist项目
	  upPriceFun(index){
		if(index == 0) return
        let obj1 = this.priceList[index]
        let obj2 = this.priceList[index -1]
		this.priceList.splice(index - 1,1,obj1)
		this.priceList.splice(index,1,obj2)
	  },
	   //向下移动pricelist项目
	  downPriceFun(index){
		if(index == this.priceList.length - 1) return
        let obj1 = this.priceList[index]
        let obj2 = this.priceList[index + 1]
		this.priceList.splice(index + 1,1,obj1)
		this.priceList.splice(index,1,obj2)
	  },
	  updatePriceListFun() {
		let tempArr = this.modalType.customData;
		tempArr.splice(0, tempArr.length);
		this.priceList.forEach(item => tempArr.push(item));
		this.$emit("update:panels", "");
	  },





	  //点击修改 产品列表
	  async comeProductForm() {
		await this.cleanProductForm();
		this.showForm = true;
		let temp = this.modalType.customData;
		for (let key in temp) {
		  this.productForm[key] = temp[key]?.value;
		}
		this.$nextTick(this.$refs.cutImage3.comeFromGF);
	  },
	  async updateProductForm() {
		let control = this.modalType.customData ? "edit" : "add";
		let temp = this.modalType.customData || {
		  product_crowd: {
			disabled: {},
			img: {},
			link: {},
			price: {},
			subTitle: {},
			text: {},
			title: {},
			id: {},
		  },
		};
		for (let key in this.productForm) {
		  if (!temp[key]) temp[key] = {};
		  temp[key].value = this.productForm[key];
		}
		if (control === "add") {
		  this.modalType.customDataParent.push({ product_crowd: temp });
		}
		await this.cleanProductForm();
		this.$emit("update:panels", "");
	  },
	  newPageFun() {
		this.treeWindow = 1;
		this.headerFormData = {};
	  },
	  newHeaderFun() {
		if (this.$refs.form.validate()) this.editHeader();
	  },
	  headerBackFun() {
		this.treeWindow = 0;
		this.$refs.form.resetValidation();
		this.$refs.form.reset();
		this.nodeData = {};
	  },
	  async treeSettingFun(mitem, item) {
		this.nodeData = item;
		if (mitem.title == "Page Settings") {
		  this.treeWindow = 1;
		  this.headerFormData = await this.getHeaderDetail();
		  let findObj = this.findObjectById(this.pageInfo.allPage, this.headerFormData.pageId)
		  this.chooseName = findObj?.pageName
		} else if (mitem.title == "New Page") {
		  this.treeWindow = 1;
		  this.headerFormData = {};
		  this.headerFormData.parentId = item.id;
		} else if (mitem.title == "Delete Page") this.deleteHeaderDialog = true;
	  },
	  delHeader() {
		let postData = {
		  id: this.nodeData.id,
		};
		delHeader(postData).then((res) => {
		  this.nodeData = {};
		  this.getHeaderList();
		  this.deleteHeaderDialog = false;
		  if (res.code == 200) this.$toast.success(res.message);
		});
	  },
	  getHeaderDetail() {
		return new Promise((resolve, reject) => {
		  let postData = {
			id: this.nodeData.id,
		  };
		  getHeaderDetail(postData).then((res) => resolve(res.data));
		});
	  },
	  editHeader() {
		let postData = {
		  proId: this.proId,
		  ...this.headerFormData,
		};
		editHeader(postData).then((res) => {
		  this.headerBackFun();
		  this.getHeaderList();
		});
	  },
	  // 获取页头数据
	  getHeaderList() {
		let postData = {
		  proId: this.proId,
		};
		getHeaderList(postData).then((res) => this.headerTreeItems = res.data);
		let params = {
			  proId: this.proId,
			  language: 'en-us'
		 }
	   getRetailerAllHalfCate(params).then((res) => this.hotList = res.data || [])
	  },
	  deleteDialog(val) {
		this.deleteTarget = val;
		this.dialog = true;
	  },
	  delSampleBannerById() {
		let postData = {
		  id: this.deleteTarget.id,
		};
		delSampleBannerById(postData).then((res) => {
		  this.$emit("bannerBack");
		  this.dialog = false;
		  this.deleteTarge = null;
		});
	  },
	  uploadSampleBanner(url) {
		return new Promise((resolve, reject) => {
		  let postData = {
			bannerUrl: url,
			proId: this.proId,
		  };
		  uploadSampleBanner(postData).then((res) => resolve(res));
		});
	  },
	  changeBannerObjectFun(val) {
		let itemData = this.itemData;
		if (this.pageName == "home") itemData.sampleData.homeImgStyle["object-fit"] = val.objectFit;
		else itemData.sampleData.productImgStyle["object-fit"] = val.objectFit;
	  },
	  BackBannerImageFun() {
		this.bannerPanels = "pickBannerImage";
		this.bannerImgUrl = "";
		this.$refs.uploads.value = "";
		this.$emit("bannerBack");
	  },
	  uploadBannerImageFun() {
		this.$refs.uploads.click();
		this.uploadType = "4";
	  },
	  changeCanEdit(val) {
		this.$emit("update:canEdit", val);
	  },
	  changeExpansionPanels(val) {
		this.$emit("update:expansionPanels", val);
	  },
	  changeBannerFun(val) {
		if (!val) return;
		if (val.clickTarget == "banner") {
		  this.modalType.customData.value = val.bannerUrl;
		  this.bannerImgUrl = val.bannerUrl;
		  this.$emit("getIframeMBDataFromNavigationFun", {
			type: "banner",
			data: val.bannerUrl,
		  });
		} else if (val.clickTarget == "video") {
		  this.modalType.customData.value = val.videoUrl;
		  this.$emit("getIframeMBDataFromNavigationFun", {
			type: "video",
			data: val.videoUrl,
		  });
		}
		if (val.clickTarget == "banner") this.bannerPanels = "uploadBannerImage";

		this.stepRecord();
	  },
	  sysProOwnerDTOChangeFun() {
		this.$store.commit("setProSystem", this.sysProOwnerDTO);
	  },
	  fontsChange(val) {
		//避免重复渲染以及多次点击触发的移除样式效果
		if (val == this.font || val == undefined) return;
		this.$emit("update:font", val);
	  },
	  changeImageAndCleanFun() {
		this.otherData.dialog1 = true;
		this.$emit("cleanImg");
	  },
	  changeBannerPageFun() {
		this.$emit("changeBannerPageFun");
	  },
	  chooseTemplateStyle(item) {
		this.websiteStyleId = item.id,
		this.templateStyleSearchName = item.websiteStyleData
		this.pageForm.page = 1;
		this.changePageFun()
	  },
	  chooseTemplateName(item) {
		item.name == "All Templetes" ? this.templateNameKeyWord = "" : this.templateNameKeyWord = item.name
		this.templateNameSearchName = item.name
		this.pageForm.page = 1
		this.changePageFun()
	  },
	  async getListWebsiteStyleBySample() {
		let res = await listWebsiteStyleBySample({
		  proId: this.proId
		})
		this.templateStyleList = [{ id: null, websiteStyleData: "All Templates" }].concat(res.data)
	  },
	  changePageFun(flag) {
		if(flag) this.isReplaceType = flag
		this.pageForm.keyWord = this.pageForm.page //同步输入框的页码
		if(this.searchTemplateValue){
		  let arr = this.searchTemplateValue.split('-')
		  if(arr && arr.length>1){
			  let temp = this.templateStyleList.find(item => item.websiteStyleName == arr[1])
			  if(temp){
				  this.websiteStyleId = temp.id
				  this.templateStyleSearchName = temp.websiteStyleData
				  this.searchTemplateValue = arr[0]
			  }
		  }
		}
		let cardType = null
		if(this.templateNameKeyWord =='Card Simple'){
			cardType = 1
		}else if(this.templateNameKeyWord =='Card Scroll'){
			cardType = 2
		}else if(this.templateNameKeyWord =='Card Grid'){
			cardType = 3
		}else if(this.templateNameKeyWord =='Card Tab'){
			cardType = 4
		}else if(this.templateNameKeyWord =='Card Pure'){
			cardType = 5
		}
		let keyWord = (this.templateNameKeyWord&&this.templateNameKeyWord.includes("Card"))?"Card":this.templateNameKeyWord == "Summary Review"?"Banner":this.templateNameKeyWord
		let sampleType = this.sampleType == 1 ? (this.isReplaceType == "newBanner"?4:1) : this.sampleType
		listSample({
		  proId: this.proId,
		  page: this.pageForm.page,
		  pageSize: 5,
		  cardType,
		  keyWord:this.isReplaceType == "newBanner"?'':keyWord,
		  sampleType: sampleType,
		  sampleNumber: this.sampleNumber,
		  pageName: this.pageName,
		  websiteStyleId: this.websiteStyleId,
		  keyWordNumber:this.searchTemplateValue
		}).then((res) => {
		  this.templateArrData = res.data.records;
		  this.pageForm.pageLength = res.data.pages;
		  this.templateArrData.forEach((x) => {
			x.isHidden = false;
			x.sampleData = JSON.parse(x.sampleData);
			x.isReplace = this.isReplaceType == 'replace'?true:null  //表示是不是replace出来的数据true表示是
		  });
		  this.$emit("update:loading", false);
		});
	  },
	  changeThemeColor(val) {
		if (val) {
		  this.$store.getters.setTheme(JSON.parse(val.themeData));
		  this.$emit("update:themeColorDraft", val);
		}
	  },
	  updateLogo() {
		this.$store.commit("setProSystem", this.sysProOwnerDTO);
	  },
	  getFile1 (e) {
		let file = e.target.files[0];
		let formData = new FormData();
		formData.append("file", file);
		importPageRowDraftData(formData).then(({data})=>{
			if(data.code ==200){
				this.$message.success(data.message)
			}else{
				this.$message.error(data.message)
			}
			this.$refs.uploads1.value = ""
		}).catch(err=>{
			this.$message.error(err)
			this.$refs.uploads1.value = ""
		})
  	  },
	  getFile(e) {
		let file = toReader(e);
		this.$ossUpload(file).then(res => {
		  switch (this.uploadType) {
			case "3":
			  if (this.itemData.sampleData.name == 'Footer') this.sysProOwnerDTO.logoFooter = res;
			  else this.sysProOwnerDTO.logo = res;
			  this.$store.commit("setProSystem", this.sysProOwnerDTO);
			  this.$nextTick(() => this.$refs.cutImage2.comeFromGF());
			  this.stepRecord();
			  break;
			case "4":
			  this.uploadSampleBanner(res).then(ress => {
				if (ress.code == 200) {
				  this.changeBannerFun({ clickTarget: "banner", bannerUrl: res });
				  this.bannerImgUrl = res;
				}
			  });
			  break;
			case "5":
			  this.modalType.customData.value = res;
			  this.$nextTick(() => this.$refs['cutImage1'].comeFromGF());
			  if (this.manageMobile) this.$emit("getIframeMBDataFromNavigationFun", { type: "img", data: res });
			  else this.stepRecord();
			  break;
			case "55":
			  this.videoCover = res;
			  this.modalType.customData.poster = res;
			  this.$nextTick(() => this.$refs.cutVideoImage.comeFromGF());
			  this.$emit("refreshComponents")
			  // if (this.manageMobile) this.$emit("getIframeMBDataFromNavigationFun", { type: "img", data: res });//////////////
			  // else this.stepRecord();
			  break;
			case "6":
			  this.productForm.img = res;
			  this.$nextTick(() => this.$refs.cutImage3.comeFromGF());
			  break;
			case "7":
			  this.colorPickerValue.value3 = res;
			  this.$nextTick(() => this.$refs.cutImage4.comeFromGF());
			  break;
			case "8":
			  this.modalType.customData.img.value = res;
			  this.$nextTick(() => this.$refs.cutImage5.comeFromGF());
			  this.$emit("getIframeMBDataFromNavigationFun", { type: "img", data: res });
			  break;
		  }
		  this.$refs.uploads.value = "";
		});
	  },
	  newItems(card) {
		//   card.sampleData.theme = this.$store.state.proTheme;
		this.$emit("getNewItems", card);
	  },
	  copySortIndex(value){
		navigator.clipboard.writeText(value).then(() => {
			this.$message.success("TemplateIndex copied successfully")
		});
	  },
	  previewImage(url){
		this.$emit("previewImage", url);
	  }
	},
  };
  </script>
  <style scoped lang="scss">
.richText{
	position: relative;
    .NO_img{
		position: absolute;
		color: brown;
        top: 10px;
		left: 10px;
		z-index: 1;
        background-color: white;
	}
	.NO_Preview{
		position: absolute;
		top: 10px;
		right: 10px;
		color: brown;
	}
}
  ::v-deep .resizeFontSize>span {
	transform: scale(0.8);
  }

  .mNavigation {
	.border1 {
	  border: 1px solid #1a73e8 !important;
	}

	.customLabel {
	  padding: 0;

	  ::v-deep .v-btn__content {
		height: 100%;
		width: 100%;
	  }

	  label {
		display: flex;
		align-items: center;
		padding: 0 12.4444444444px;
		height: 100%;
	  }
	}

	.customPanels {
	  .v-expansion-panel-header {
		padding: 0;
	  }

	  ::v-deep .v-expansion-panel-content__wrap {
		padding: 0;
	  }
	}

	.cBtn {
	  position: absolute;
	  left: 0;
	  right: 0;
	  bottom: 0;
	  height: 50px;
	  display: flex;
	  align-items: center;
	  justify-content: center;
	  background-color: #cee3fb;
	}

	::v-deep .cPadding .v-expansion-panel-content__wrap {
	  padding: 0 5px 5px 5px;
	}

	.m-active-class {
	  .v-btn:before {
		opacity: 0;
	  }

	  .v-btn:after {
		background-color: #1a73e8;
		border-radius: inherit;
		bottom: -5px;
		color: inherit;
		content: "";
		pointer-events: none;
		position: absolute;
		width: 100%;
		height: 2px;
		transition: opacity 0.2s cubic-bezier(0.4, 0, 0.6, 1);
	  }
	}

	::v-deep .m-switch-class .v-input__control .v-input__slot {
	  display: flex;

	  .v-input--selection-controls__input {
		order: 2;
	  }

	  .v-label {
		order: 1;
	  }
	}

	::v-deep .m-chip-class {
	  .v-chip__content {
		justify-content: center;
	  }

	  .v-chip.v-size--default {
		width: 32px;
	  }

	  .v-chip:before {
		opacity: 0;
	  }

	  .v-icon {
		opacity: 0;
	  }

	  .m-chip-acitve .v-icon {
		opacity: 1;
	  }
	}

	::v-deep .btn-updown .v-btn__content {
	  flex-direction: column;
	}

	.hoverTag {
	  .btn {
		display: none;
	  }

	  &:hover .btn {
		display: block;
	  }
	}

	.noLevel ::v-deep .v-treeview-node__level {
	  width: 0;
	}
  }

  ::v-deep .chooseTemplateStyleMeneClass {
	display: flex;
	justify-content: space-evenly;

	.v-size--default {
	  background: #efefef;
	  width: 48%;
	}
  }

  .templateDefaultStyle {
	width: 100%;
	display: flex;
	align-items: center;
	padding: 1em;
	cursor: pointer;

	&:hover {
	  background: #e0e0e0;
	}

	&>span>img {
	  object-fit: contain;
	  height: 2.5em;
	  width: 6.25em;
	}
  }

  .templateActive {
	background: #efefef;
  }
  </style>
  <style>
  .templeteTagList{
	  pointer-events:auto!important;
  }
  </style>
