<!--by:cg-->
<template>
	<div class="quoteCustomCustomEarrings">
		<div class="main">
			<div class="custom-scrollbar mainL" id="mainL">
				<div v-if="isMobile" class="title">
					<div class="l">{{ baseModel.detailModel.cateName }}</div>
					<div class="l r">
						<!-- 	<b class="iconfont icon-shoucang" /> -->
					</div>
				</div>
				<!-- 	<div v-if="isMobile" class="item">{{ lang.nameBadges.step.sign }}: {{ baseModel.detailModel.productNumber }}</div> -->

				<!--轮播图-->
				<div class="swiper-area">
					<div class="myswiper2" ref="myswiper2">
						<div class="swiper" ref="swiper2">
							<div class="swiper-wrapper">
								<div class="swiper-slide" v-for="(item, index) in carouselModel.list" :key="index">
									<PicZoom :url="item.url" :alt="item.alt" :width="300" :scale="2" type="round"></PicZoom>
								</div>
							</div>
						</div>
						<div class="swiper-button-next">
							<img :src="ossModel.rightIcon" alt="rightBtn" />
						</div>
						<div class="swiper-button-prev">
							<img :src="ossModel.leftIcon" alt="leftBtn" />
						</div>
					</div>
					<div v-show="!isMobile" class="myswiper1">
						<div class="swiper" ref="swiper1">
							<div class="swiper-wrapper">
								<div class="swiper-slide" v-for="(item, index) in carouselModel.list" :key="index">
									<img :src="item.urlThumb" :alt="item.alt" :title="item.alt" />
								</div>
								<!-- <div class="swiperSlideMove" @click="openGallery">
									<div class="p1">{{ lang.nameBadges.swiper.viewMove }}</div>
									<div class="p1 p2">
										<b class="iconfont icon-shuangcengjiantou-shang" />
									</div>
								</div> -->
							</div>
						</div>
					</div>
				</div>
				<!--轮播图end-->

				<div v-if="!isMobile" id="swiperDesc" class="swiperDesc">
					<div class="rowOne">
						{{ lang.nameBadges.carousel.titleDesc }}
					</div>

					<div v-if="baseModel.byPIdModel.length > 0" class="rowTwo">
						<div v-html="getByPIdDom()"></div>
					</div>
				</div>
			</div>
			<div class="custom-scrollbar mainR">
				<div v-if="!isMobile" class="title">
					<div class="l">{{ baseModel.detailModel.cateName }}</div>
					<div class="l r">
						<!-- 	<b class="iconfont icon-shoucang" /> -->
					</div>
				</div>
				<!-- <div v-if="!isMobile" class="item">{{ lang.nameBadges.step.sign }}: {{ baseModel.detailModel.productNumber }}</div> -->

				<div class="lySetpTableList">
					<LySetpTableList ref="lySetpTableList"
						:model="lySetpTableListModel"
                                     :summaryModel="summaryModel"
                                     @change="lySetpTableListChange"
						@quantityInput="quantityInput"
						@textUploadImg="textUploadImg"
						@tTimeCommentInput="tTimeCommentInput"
						@tTimeCommentFiles="tTimeCommentFiles"
						@sizeChange="sizeChange"
					/>
				</div>
				<div v-if="isMobile" id="swiperDesc" class="swiperDesc">
					<div class="rowOne">
						{{ lang.nameBadges.carousel.titleDesc }}
					</div>
					<div v-if="baseModel.byPIdModel.length > 0" class="rowTwo">
						<div v-html="getByPIdDom()" class="li"></div>
					</div>
				</div>

				<div id="subtotal" class="subtotal">
					<div class="subDetail">
						<div class="l">
							<div class="item">
								<div style="font-weight: bold; font-size: 18px; color: rgb(51, 51, 51)">{{ lang.nameBadges.summary.title }}</div>
							</div>
							<div class="item">
								<div class="itemLeft">{{ lang.nameBadges.summary.quantity }}</div>
								<div class="itemRight">
									<span v-if="summaryModel.moneyModel.quantity">{{ summaryModel.moneyModel.quantity }} pcs</span>
								</div>
							</div>
							<div class="item">
								<div class="itemLeft">{{ lang.nameBadges.summary.unitPrice }}</div>
								<div class="itemRight"><CCYRate :price="summaryModel.moneyModel.unitPrice"></CCYRate></div>
							</div>
							<div class="item">
								<div class="itemLeft">{{ lang.nameBadges.summary.setupCharge }}</div>
								<div class="itemRight"><span v-if="summaryModel.moneyModel.toolingCharge">+</span><CCYRate :price="summaryModel.moneyModel.toolingCharge"></CCYRate></div>
							</div>
							<div class="item">
								<div class="itemLeft">{{ lang.discount }}:</div>
								<div class="itemRight" v-show="discountPrice"><span>{{ text2 }}</span><CCYRate :price="discountPrice"></CCYRate></div>
							</div>
						</div>
						<div class="r">
							<div class="totalPriceBox">
								<strong class="subTotalText">{{ lang.nameBadges.summary.subtotal }} </strong>
								<label class="finalPrice"> <CCYRate :price="summaryModel.moneyModel.subtotal"></CCYRate></label>
							</div>
							<div>
								<freeTip />
								<button v-if="!isMobile" type="button" id="addCartBtn" class="" @click="addCart()">
									{{ lang.nameBadges.summary.button }}
								</button>
								<button v-if="isMobile" type="button" id="addCartBtn" class="buttonM" @click="addCart()">Add to Cart</button>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<!--移动端悬浮框-->
		<!-- <div v-if="isMobile" class="float" id="float">Preview</div> -->
	</div>
</template>
<script>
import { generateUUID, scrollToViewTop, loadImage, analyzeImageColor, rgbToHex,round2 } from "@/utils/utils";
import { getCateParamRelationByCateId, getByPId, getInfo } from "@/api/web";
import { otoAddCart } from "@/api/pins";
import { DataProcessing } from "@/utils/dataProcessing";
import { Common } from "@/utils/common";
import { calculateFunc } from "@/utils/system";
import { canvasModel, selectModel, lySetpTableListModel, baseModel, carouselModel, dictModel, summaryModel, ossModel } from "./entity/_customCustomEarrings";
import LySetpTableList from "@/components/Quote/Ly/LySetpTableList";
import freeTip from "~/components/Quote/freeTip";
import {findEarringsSelectDiscount, getIsSmallQty} from "@/assets/js/quote/quotePublic";
let that;
export default {
	name: "",
	components: {
		LySetpTableList,
		freeTip,
	},
	head: {
		meta: [
			{
				name: "apple-mobile-web-app-capable",
				content: "yes",
			},
			{
				name: "viewport",
				content: "width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no",
			},
		],
	},
	data() {
		return {
			canvasModel: new canvasModel(),
			selectModel: new selectModel(),
			baseModel: new baseModel(),
			lySetpTableListModel: new lySetpTableListModel(),
			carouselModel: new carouselModel(),
			dictModel: new dictModel(),
			summaryModel: new summaryModel(),
			ossModel: new ossModel(),
		};
	},
	computed: {
		discountPrice(){
			let price = (this.summaryModel.moneyModel.unitPrice * this.summaryModel.moneyModel.quantity)+this.summaryModel.moneyModel.toolingCharge;
			return `${Math.abs(this.summaryModel.moneyModel.subtotal - price)}`;
		},
		text2() {
			let ac;
			if (this.summaryModel.moneyModel.discount > 1) {
				ac = "+";
			} else if (this.summaryModel.moneyModel.discount < 1) {
				ac = "-";
			} else {
				ac = "";
			}
			return ac;
		},
		lang() {
			return this.$store.getters.lang.quote || {};
		},
		isMobile() {
			return this.$store.getters.isMobile;
		},
		isLogin() {
			return this.$store.getters.isLogin;
		},
		userId() {
			return this.$store.state.userInfo?.id;
		},
		userUUID() {
			return this.$store.state.userUUID;
		},
		proId() {
			return this.$store.state.proId;
		},
		device() {
			return this.$store.state.device;
		},
		//获取国家语言
		currency() {
			return this.$store.state.currency;
		},
	},

	async mounted() {
		if (process.client) {
			Common.init();
		}

		this.init();
		this.$nextTick(() => {
			this.initSwiper();
		});
		that = this;
	},

	beforeDestroy() {},
	methods: {
		initCss() {},
		init() {
			this.getByPIdFunc();
			this.getInfoFunc();
			//this.initCss();
		},
		//图片上传回调
		//参数i: 当前上传步骤数据
		//参数j: 当前上传数组
		textUploadImg(i, j) {
			console.log("图片回调", i, j);
			i.children = [];
			i.children.push({
				checked: true,
			});
		},
		//Select Turnaround Time步骤评论
		tTimeCommentInput: Common.fd((i, j) => {
			that.lySetpTableListModel.list.map((n) => {
				if (n.name === "Select Turnaround Time" || n.name === "Turnaround Time") {
					n.children.map((m) => {
						if (m.checked) {
							m.remark = j;
						} else {
							m.remark = undefined;
						}
					});
				}
			});
		}, 500),
		//Select Turnaround Time步骤评论文件
		tTimeCommentFiles: Common.fd((i, j) => {
			that.lySetpTableListModel.list.map((n) => {
				if (n.name === "Select Turnaround Time" || n.name === "Turnaround Time") {
					n.children.map((m) => {
						if (m.checked) {
							m.files = j;
						} else {
							m.files = undefined;
						}
					});
				}
			});
		}, 500),
		//数量回调
		quantityInput: Common.fd((i, j) => {
			that.baseModel.calculateModel.quantity = j.quantity;
			if (that.baseModel.calculateModel.quantity == "") {
				that.baseModel.calculateModel.quantity = 0;
			}
			calculateFunc(that.baseModel.calculateModel, that.baseModel.calculateModel.object, that.baseModel.calculateModel.parentObject, (res, model) => {
				that.getSummaryModelFunc(res, model);
			});
		}, 500),
		//步骤回调
		lySetpTableListChange(i, j) {
			if (j.id) {
				this.calculate(i, j);
			}
		},
		//尺寸回调
		sizeChange(i,j){
			console.log(j,"12");
			if (j.id) {
				this.calculate(i, j);
			}
		},

		//价格接口
		//参数i：当前步骤对象
		//参数j: 当前步骤选中对象
		calculate(i, j) {
			console.log("八戒", i, j);
			//价格计算
			this.$nextTick(() => {
				this.baseModel.calculateModel.cateId = this.baseModel.cateId;
				this.baseModel.calculateModel.object = j;
				this.baseModel.calculateModel.parentObject = j.parentObject.childList;
				if (this.baseModel.calculateModel.quantity == "") {
					this.baseModel.calculateModel.quantity = 0;
				}
				if (this.baseModel.defaultSize?.object?.childList.length > 0) {
					let cList = this.baseModel.defaultSize.object.childList[0];
					this.baseModel.calculateModel.sizeId = cList.priceInfo.id;
				}
				calculateFunc(this.baseModel.calculateModel, j, j.parentObject.childList, (res, model) => {
					that.getSummaryModelFunc(res, model);
				});
			});
		},
		//获取价格
		getSummaryModelFunc(res, model) {
			this.baseModel.quotePriceParam = model;
			this.summaryModel.moneyModel = {
                ...res.data,
				quantity: res.data.totalQuantity,
				unitPrice: res.data.foundationUnitPrice,
				setupCharge: res.data.setupCharge,
				toolingCharge: res.data.toolingCharge,
				subtotal: res.data.totalPrice,
				unit: "$",
				discount: res.data.discount
			};
		},

		//轮播赋值
		//参数1： 轮播图json数组  []
		setSw(arr) {
			if (arr?.length > 0) {
				arr.map((item, index) => {
					if (item.urlThumb == "") {
						item.urlThumb = item.url;
					}
					if (item.url == "") {
						arr.splice(index, 1);
					}
				});
				this.carouselModel.list = arr;
				this.$forceUpdate();
			}
			return arr;
		},
		//轮播图更多按钮特效
		openGallery() {
			let data = {
				modal: "modalCustom",
				api: "listPageImgByPageId",
				showArrow: "icon-a-icon-jt4zhuanhuan",
				class: "img-swiper",
				showIndexPage: true,
				showTab: true,
				dialog: true,
				scroll: 1,
				apiParams: {
					id: 126880,
				},
			};
			this.$store.commit("setMask", data);
		},
		initSwiper() {
			this.myswiper1 = new Swiper(this.$refs.swiper1, {
				slidesPerView: 6,
				spaceBetween: 6,
				watchSlidesVisibility: true, //防止不可点击
				grabCursor: true,
			});
			this.myswiper2 = new Swiper(this.$refs.swiper2, {
				slidesPerView: 1,
				spaceBetween: 10,
				autoplay: false,
				grabCursor: true,
				observer: true,
				observeParents: true,
				thumbs: {
					swiper: this.myswiper1,
				},
				navigation: {
					nextEl: ".myswiper2 .swiper-button-next",
					prevEl: ".myswiper2 .swiper-button-prev",
				},
			});
		},

		//数据处理
		getByPIdHandle(res) {
			res.data.map((item, index) => {
				if (item.id == this.baseModel.cateId) {
					item.desc = item.cateName;
					item.img = item.imagePhoto;
					if (item.imageJson) {
						item.imageJsonJson = JSON.parse(item.imageJson);
						item.checked = true;
						this.setSw(item.imageJsonJson);
					}
				}
			});
		},
		getInfoFunc() {
			getInfo({ id: this.baseModel.pid }).then((res) => {
				if (res) {
					this.baseModel.detailModel = res.data;
				}
			});
		},

		getByPIdFunc() {
			getByPId({ pid: this.baseModel.pid }).then((res) => {
				if (res) {
					this.dictModel.routeName.map((i) => {
						if (this.$route.name === i.name) {
							this.baseModel.cateId = i.cateId;
							console.log("路西", this.baseModel.cateId);
						}
					});
					this.getByPIdHandle(res);
					if (res.data?.length > 0) {
						res.data.map((j) => {
							if (this.baseModel.cateId === j.id) {
								//轮播
								this.getCateParamRelationByCateIdFunc(j.id);
								this.baseModel.cateId = j.id;
								this.baseModel.cateIdModel = j;
								this.baseModel.byPIdModel = res.data || [];
							}
						});
					}
				}
			});
		},

		//购物车业务处理
		setAddCartData() {
			this.$gl.show();
			let model = {
				currency: this.currency.code,
				uuid: this.isLogin ? null : this.userUUID,
				userId: this.isLogin ? this.userId : null,
				buyType: 7, //7全定制   9半定制
				isMobile: this.device === "mb" ? 1 : 0,
				isDs: 0, // 是否设计系统  1||是   0||否
				isFastQuote: 0, //默认0
				quoteCateId: this.baseModel.pid,
				quantity: undefined, //数量
				quoteCateChildId: undefined, //报价分类id  如 第一步的子分类
				quotePriceParam: this.baseModel.quotePriceParam, //calculate接口提交参数
				quoteParam: {
					classificationData: {}, //第一步选中的item   getByPid 这个就是分类
					finaData: [], //步骤数组   删掉其他childList 只留选中的childList.   	过滤未加价的步骤.  如 Select Quantity   or  Add Texts & Upload Image
					textList: [], //画布文本编辑的数组
					//这里跟嫩白鸡有区分
					fontData: {
						fontImgCustom: [],
						comments: "",
					},
				},
                isSmallQty: getIsSmallQty(findEarringsSelectDiscount(this.lySetpTableListModel),this.$store.state.enableTurnaroundTimeCheck)
			};
			let finaDataArray = []; //临时数据
			let finaDataArrayEnd = []; //临时数据
			this.lySetpTableListModel.list.map((i) => {
				if (i.type === "QUANTITY") {
					model.quantity = i.quantity;
				}
				if (i.type === "TEXTUPLOAD") {
					let imgArr = [];
					i.textUploadModel.imgList.map((item) => {
						imgArr.push(item.url);
					});
					model.quoteParam.fontData.fontImgCustom = imgArr;
				}

				if (i.object) {
					if (!(i.object.paramName === "Select Quantity" || i.object.paramName === "Quantity")) {
						finaDataArray.push(i); //记录过滤
					}
				}
				if (i.name === "Select Turnaround Time" || i.name === "Turnaround Time") {
					finaDataArray.push(i); //记录过滤
				}
			});
			//获取子分类
			if (this.baseModel.cateIdModel) {
				model.quoteCateChildId = this.baseModel.cateId;
				model.quoteParam.classificationData = this.baseModel.cateIdModel;
			}
			finaDataArray.map((j, jIndex) => {
				let o = [];
				j.children.map((z, zIndex) => {
					if (z.checked) {
						o.push(z);
					}
				});
				if (o.length > 0) {
					finaDataArrayEnd.push({
						...j.object,
						childList: [o[0]],
					});
				}
			});
			//过滤第一步
			finaDataArrayEnd.map((n, nIndex) => {
				if (n.paramName == null && n.priceInfo == null) {
					finaDataArrayEnd.splice(nIndex, 1);
				}
			});

			//追加最后一步
			finaDataArrayEnd = DataProcessing.uniqueJSON(finaDataArrayEnd, "id");
			model.quoteParam.finaData = finaDataArrayEnd;
			let m = {
				...model,
			};
			console.log("购物车", m);

			if (model.quoteParam) {
				const seen = new WeakSet();
				model.quoteParam.textList.map((item, index) => {
					delete model.quoteParam.textList[index].bold;
					delete model.quoteParam.textList[index].id;
					delete model.quoteParam.textList[index].incline;
					delete model.quoteParam.textList[index].textHere;
					delete model.quoteParam.textList[index].code;
				});
				model.quoteParam.finaData.map((item, index) => {
					if (item.paramName === "Add Logo") {
						model.quoteParam.finaData.splice(index, 1);
					}

					if (item.paramName === "Select Turnaround Time" || item.paramName === "Turnaround Time") {
						let files = item.childList[0].files;
						if (files?.length > 0) {
							model.quoteParam.fontData.fontImgCustom = model.quoteParam.fontData.fontImgCustom.concat(files);
							model.quoteParam.fontData.comments = item.childList[0]?.remark;
						}
						item.childList[0].files = undefined;
						item.childList[0].remark = undefined;
					}
					//model.quoteParam.fontData.fontImgCustom = imgArr;
				});
				console.log("不来再来伤害我", model);
				m.quoteParam = JSON.stringify(model.quoteParam, (key, value) => {
					if (typeof value === "object" && value !== null) {
						if (seen.has(value)) {
							return; // 发现循环引用时返回 undefined
						}
						seen.add(value);
					}
					return value;
				});
			}
			console.log(this.baseModel.discountId,"this.baseModel.discountId");
			if (model.quotePriceParam) {
				//阿文说数据量太大 要删掉没用字段
				delete model.quotePriceParam.object;
				delete model.quotePriceParam.parentObject;
				delete model.quotePriceParam.paramIdMapping;
				delete model.quotePriceParam.packingIdListJson;
				model.quotePriceParam.discountId = this.baseModel?.quotePriceParam?.discountId;
				model.quotePriceParam.proId = this.proId;
				m.quotePriceParam = JSON.stringify(model.quotePriceParam);
			}
			console.log("去购物车", this.lySetpTableListModel);
			otoAddCart(m, this.baseModel.quotePriceParam).then((res) => {
				this.$gl.hide();
				if (res) {
					//return;
					this.$router.push({
						path: "/cart",
					});
				}
			});
		},

		//去购物车业务
		addCart() {
			if (this.$refs.lySetpTableList.checkedStep()) {
				//业务逻辑
				this.setAddCartData();
			}
		},
		//过滤第二步
		filterData(res) {
			// let op = false;
			// res.data.map((item) => {
			// 	if (item.paramName === "Select lmprint Method" || item.paramName === "lmprint Method") {
			// 		op = true;
			// 	}
			// });
			// if (op) {
			// 	return res;
			// } else {
			// 	/*  有的报价 没有第二步  第二步之后的步骤-1 */
			// 	res.data.map((item, index) => {
			// 		if ((index > 1 && item.paramName != "Select Plating Color") || item.paramName != "Plating Color" || item.paramName!='Size') {
			// 			console.log(item);
			// 			item.stepIndex -= 1;
			// 		}
			// 	});
			// 	return res;
			// }
			if (!res.data) {
				return false;
			}
			let data = res.data;
			let index = 0;
			data.forEach((item) => {
				if (item) {
					if (!item.noShowDetail) {
						index += 1;
					}
					item.stepIndex = index;
				}
			});
			return res;
		},
		//拼接实体类
		dataAppend(res) {
			this.lySetpTableListModel.list = new lySetpTableListModel().list;
			res.data.map((i, iIndex) => {
				let childrenArray = [];
				let obj = {
					steplabel: "STEP " + i.stepIndex,
					name: i.paramName,
					css: {
						rowColumns: 3,
					},
					mobileCss: {
						rowColumns: 3,
					},
					type: i.paramType,
				};
				if (i.paramType === "QUANTITY") {
					obj = {
						...obj,
						quantity: new baseModel().calculateModel.quantity,
						unitName: "Pair",
					};
				} else if (i.paramType === "DISCOUNT") {
				}

				if (i.paramName === "Add Texts & Upload Image" || i.paramName === "Add Logo") {
					obj = {
						...obj,
						type: "TEXTUPLOAD", //文本和上传textUpload
						isEmail: false,
						textUploadModel: {
							textList: [new canvasModel().textObject],
							imgList: [],
							isUpload: true,
						},
						hideCanvas: true,
					};
				}
				if (i.paramName === "Select Plating Color" || i.paramName === "Select Earring Accessories" || i.paramName === "Select Packaging" || i.paramName === "Plating Color" || i.paramName === "Earring Accessories" || i.paramName === "Packaging") {
					obj = {
						...obj,
						isPriceText: true,
					};
				}
				//拼title
				if (i.paramName === "Select Turnaround Time" || i.paramName === "Turnaround Time") {
					obj.title = this.lang.nameBadges.fontModel.title4;
					obj.isComment = true;
				}
				if (i.paramName === "Additional Upgrades (Optional)") {
					obj.title = this.lang.nameBadges.fontModel.title5;
					obj.isPriceText = true;
				}
				//拼children
				if (i.childList) {
					i.childList.map((j) => {
						let b = {};
						if (j.imageJson) {
							let imageJsonJson = JSON.parse(j.imageJson);
							if (imageJsonJson && imageJsonJson?.length > 0) {
								b = {
									...j,
								};
								b.img = imageJsonJson[0].url;
								b.desc = j.paramName;
								childrenArray.push(b);
							}
						} else {
							if (j.paramType === "DISCOUNT") {
								b = {
									...j,
								};
								b.tip = j.paramName;
								b.name = "Standard Free";
								childrenArray.push(b);
							}
							else if(j.paramType === "SIZE") {
								b = {
									...j,
								};
								// b.tip = j.paramName;
								// b.name = "Standard Free";
								childrenArray.push(b);
							}
						}
					});
					console.log(childrenArray,"childrenArray");
					obj.children = childrenArray;
				}
				obj.object = i;
				// if (obj.type !== "SIZE") {
					//i.paramType=='SIZE' 则不显示  参考原型https://lanhuapp.com/web/#/item/project/detailDetach?pid=2fc6ade4-c1cd-459c-85ad-b0c5aa19ab5c&project_id=2fc6ade4-c1cd-459c-85ad-b0c5aa19ab5c&image_id=2dbe9475-a097-4f7b-b4f3-d501df69d29b&fromEditor=true
					this.lySetpTableListModel.list.push(obj);
				// } else {
				// 	this.baseModel.defaultSize = obj;
				// }
			});
			console.log("启航'", this.lySetpTableListModel.list, res);
		},
		getCateParamRelationByCateIdFunc(id) {
			getCateParamRelationByCateId({ cateId: id }).then((res) => {
				if (res) {
					let sortFun = res.data.sort(DataProcessing.getSortFun("asc", "stepIndex"));
					console.log("排序", res.data);
					res = this.filterData(res);
					this.dataAppend(res);
					this.lySetpTableListModel.pid = this.baseModel.pid;
				}
			});
		},
		getByPIdDom() {
			let richText = "";
			this.baseModel.byPIdModel.map((i, iIndex) => {
				if (i.checked) {
					richText = i.richText;
				}
			});
			return richText;
		},
	},
};
</script>
<style scoped lang="scss">
$color-red: #d24600;

@mixin centerCenter {
	display: flex;
	justify-content: center;
	align-items: center;
}
.quoteCustomCustomEarrings {
	padding: 40px 10px 10px !important;
	margin: 0 auto;
	//font-family: Calibri, Calibri;
	font-size: 14px;
	font-family: "Calibri", "Graphik Webfont", -apple-system, BlinkMacSystemFont, "Roboto", "Droid Sans", "Segoe UI", "Helvetica", Arial, sans-serif;
	@include respond-to(mb) {
		font-family: Helvetica, Arial, Helvetica, sans-serif;
	}
	.main {
		display: flex;
		width: 80%;
		margin-left: 10%;
		.mainL {
			flex: 5;
			width: 0;
			height: 100vh;
			position: sticky;
			top: 0%;
			z-index: 111;
			background: #fff;
			//overflow: hidden;
			.swiper-area {
				margin: 1.25rem 0;
				width: 100%;

				.myswiper2 {
					width: 90%;
					margin: 0 auto;
					position: relative;

					@include respond-to(mb) {
						width: 100%;
						height: 350px;
						.swiper {
							height: 100%;
						}
					}

					.swiper-slide {
						display: flex;
						justify-content: center;
						align-items: center;
						border: 1px dashed transparent;

						::v-deep .pic-img .img-container {
							height: 20rem;

							@include respond-to(mb) {
								height: auto;
							}
						}
					}

					.swiper-button-next::after,
					.swiper-button-prev::after {
						display: none;
					}

					.swiper-button-next,
					.swiper-button-prev {
						width: 3rem;
						@include respond-to(mb) {
							width: 3.58rem;
							img {
								background: rgba(0, 0, 0, 0.4);
								border-radius: 50%;
							}
						}
					}
				}
				.myswiper2Off {
					opacity: 0;
				}

				.myswiper1 {
					margin: 1.25rem auto 0;
					width: 90%;
					img,
					video {
						width: 100%;
						object-fit:contain;
						vertical-align: middle;
					}
					.swiperSlideMove {
						display: flex;
						justify-content: center;
						align-items: center;
						border: 1px solid #f5f5f5;
						border-radius: 8px;
						background: #d17448;
						color: #fff;
						cursor: pointer;
						flex-wrap: wrap;
						.p1 {
							height: 50%;
							display: flex;
							align-items: center;
							font-size: 15px;
							width: 100%;
							justify-content: center;
							align-items: flex-end;
							font-size: 14px;
							//width: max-content;
						}
						.p2 {
							align-items: center;
							width: 100%;
						}
						b {
							font-weight: 700;
							transform: rotate(90deg);
						}
					}
					::v-deep .swiper-wrapper {
						justify-content: center;
						width: auto;
					}

					@include respond-to(mb) {
						padding: 0;
						width: 100%;
					}

					.swiper-slide {
						display: flex;
						justify-content: center;
						align-items: center;
						border-radius: 8px;
						//	border: 1px solid transparent;
						width: 100%;
						// padding: 0.625rem 0;
						box-sizing: border-box;
						cursor: pointer;
						height: 6em;
						margin-right: 8px;
						border: 1px solid #f5f5f5;
						img {
							height: -webkit-fill-available;
						}
						@include respond-to(mb) {
							background-color: rgba(255, 255, 255, 0.25);
						}

						&.swiper-slide-thumb-active {
							border: 1px solid $color-red;
						}
					}
				}
			}

			.swiperDesc {
				margin-top: 30px;
				background: #fcfcfc;
				padding: 28px 30px 20px 30px;
				width: calc(100% - 40px);
				border-radius: 10px;
				border: 1px solid #f5f5f5;
				.rowOne {
					color: #666666;
					//	font-size: 1em;
					font-size: 15px;
				}
				.rowTwo {
					margin-top: 20px;
					.title {
						color: #333333;
						font-weight: 700;
						font-size: 1.2em;
						margin-bottom: 10px;
					}
					.li {
						font-size: 1.1em;
						color: #333;
						margin-bottom: 5px;
					}
				}
			}

			@include respond-to(mb) {
				z-index: 1;
				height: auto !important;
				top: 0 !important;
				overflow: auto;
				padding-top: 0;
				.title {
					display: flex;
					color: #333333;
					margin-bottom: 10px;
					padding: 0 10px;
					.l {
						flex: 1;
						font-weight: 700;
						font-size: 1.8em;
					}
					.r {
						display: flex;
						justify-content: flex-end;
						font-weight: 400;
						font-size: 1.6em;
						align-items: center;
					}
				}
				.item {
					padding: 0 10px;
				}
			}
		}
		.mainR {
			flex: 5;
			padding: 0 5px;
			.title {
				display: flex;
				color: #333333;
				margin-bottom: 10px;
				padding: 0 15px;
				.l {
					flex: 1;
					font-weight: 700;
					font-size: 1.8em;
				}
				.r {
					display: flex;
					justify-content: flex-end;
					font-weight: 400;
					font-size: 1.6em;
					align-items: center;
				}
			}

			.item {
				font-size: 1.4em;
				color: #6292c1;
				font-weight: 700;
				padding: 0 15px;
			}
			.lySetpTableList {
				margin-top: 30px;
				::v-deep .lySetpTableList .main .step .stepContent {
					background: -o-linear-gradient(right, #c2392b, #d29014);
					background: -moz-linear-gradient(right, #c2392b, #d29014);
					background: linear-gradient(to right, #c2392b, #d29014);
				}
				::v-deep .lySetpTableList .main .step .stepContent::before {
					background: -o-linear-gradient(right, #c2392b, #d29014);
					background: -moz-linear-gradient(right, #c2392b, #d29014);
					background: linear-gradient(to right, #c2392b, #d29014);
					background: #d29014;
				}

				::v-deep .lySetpTableList .main .step .stepContent::after {
					border-left-color: #d29014;
				}
			}
			.swiperDesc {
				@include respond-to(mb) {
					margin-top: 20px;
					background: #f4f5f5;
					padding: 15px 10px 12px 10px;
					width: 100%;
					border-radius: 5px;
					.rowOne {
						color: #666666;
						font-size: 15px;
					}
					.rowTwo {
						margin-top: 15px;
						.title {
							color: #333333;
							font-weight: 700;
							font-size: 1.2em;
							margin-bottom: 10px;
						}
						.li {
							font-size: 1.1em;
							color: #333;
							margin-bottom: 5px;
						}
					}
				}
			}
			.subtotal {
				margin-top: 15px;
				font-size: 14px;
				padding: 0 15px;
				.subDetail {
					display: grid;
					grid-template-columns: repeat(2, 1fr);
					align-items: center;
					border-radius: 10px;
					padding: 20px;
					background: #f6f6f6;
					grid-gap: 20px;
					.l {
						.item {
							display: flex;
							align-items: center;
							margin-bottom: 10px;
							font-size: unset;
							color: #333;
							.itemLeft {
								flex-shrink: 0;
								white-space: normal;
								width: 110px;
								margin-right: 38px;
								font-size: 1em;
							}
						}
					}
					.r {
						text-align: center;
						.totalPriceBox {
							.subTotalText {
								font-weight: 400;
								color: #333;
								line-height: 36px;
							}
							.finalPrice {
								margin: 0 10px;
								font-size: 1.5em;
								font-weight: bold;
								color: #e6252e;
							}
						}
						.freeTip {
							display: flex;
							align-items: center;
							justify-content: flex-start;
							width: 100%;
							padding: 10px;
							b {
								color: #68bd2c;
								margin-right: 4px;
							}
							strong {
								margin-right: 5px;
							}
							@include respond-to(mb) {
								b {
									font-size: 18px;
									margin-top: -2px;
								}
							}
						}
						.freeTip > div {
							display: flex;
							justify-content: center;
							margin: 0 5px;
							flex: 1;
						}
						#addCartBtn {
							width: 100%;
							height: 3em;
							border: none;
							color: #fff;
							outline: none;
							min-width: 14em;
							line-height: 3em;
							font-weight: bold;
							font-size: 1.125em;
							border-radius: 6px;
							background: $color-red;
							text-transform: uppercase;
						}
						.buttonM {
							width: 70% !important;
							border-radius: 2px 2px 2px 2px !important;
							margin-top: 5px;
						}
					}
				}
				@include respond-to(mb) {
					width: 100vw;
					margin-left: -15px;
					border-radius: 0;
					padding: 0;
					.subDetail {
						background: #ebebeb;
						display: inline-block;
						width: 100%;
						border-radius: 0;
						.l {
							.item {
								.itemLeft {
									flex: 1;
								}
								.itemRight {
									flex: 1;
									display: flex;
									justify-content: flex-end;
								}
							}
						}
					}
				}
			}
			@include respond-to(mb) {
				height: auto;
				overflow: unset;
			}
		}

		@include respond-to(mb) {
			display: inline-block;
			margin-left: 0;
			width: calc(100% - 0px);
			.mainL {
				width: 100%;
				padding-top: 15px;
				position: sticky;
				top: 0;
				z-index: 1111;
				background: #fff;
				.title {
					margin-bottom: 5px;
					.l {
						font-size: 1.3em;
					}
					.r {
						b {
							font-size: 0.72em;
							font-weight: 700;
						}
					}
				}
				.item {
					font-size: 0.92em;
					color: #6292c1;
					font-weight: 700;
				}
			}
		}
	}
	@include respond-to(mb) {
		padding: 0 !important;
		.float {
			position: fixed;
			right: 15px;
			bottom: 30%;
			width: 80px;
			height: 80px;
			background: #eaf4fe;
			z-index: 1111;
			@include centerCenter;
			font-size: 1.2em;
			font-weight: 700;
			color: #98a0ab;
		}
	}

	::v-deep .lySetpTableList .textUpload {
		margin-top: 0;
	}
	::v-deep .lySetpTableList .textUpload .upload .imgArea {
		margin: 5px 0;
		background: #fff;
	}
	::v-deep .lySetpTableList .main {
		margin: 0px 0 10px 0;
	}
	::v-deep .lySetpTableList .lySetpTableListStep {
		padding: 15px 20px 18px 20px;
	}
	::v-deep .lySetpTableList .textUpload .upload .imgArea .iconWrap span {
		color: #d24600;
		font-size: 16px;
	}
	::v-deep .lySetpTableList .textUpload .upload .imgArea .iconWrap b {
		color: #ccc;
	}
}
</style>
