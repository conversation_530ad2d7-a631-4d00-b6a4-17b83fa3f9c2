<template>
	<div class="modal-box stock-detail">
		<GSJJBreadcrumb :items="breadcrumb" />

		<div class="stock-detail__main">
			<div class="left">
				<div class="prod_info_title_mb" v-if="isMobile">
					<h1>{{ productData.proName }}</h1>
				</div>

				<GSJJSwiper :imgList="imgList" />
			</div>

			<div class="right">
				<div class="prod_info_title">
					<h1>{{ productData.proName }}</h1>
				</div>
				<div class="prod_info_number">Item Code: {{ productData.skuNumber }}</div>
				<div class="prod_info_review">{{ productData.briefDescription }}</div>
				<div class="prod_info_price">
					<div>{{ lang.unitPrice }}:</div>
					<div class="prod_info_price__main">
						<div class="ccy-box">
							<div>
								<span>{{ $store.state.currency.code || "USD" }}</span>
								<b class="icon-right"></b>
							</div>

							<div scrollbar class="options">
								<div v-for="i in $store.state.currencyList" @click="$store.commit('setCurrency', i)">{{ i.code }}</div>
							</div>
						</div>
						<div class="cur-price"><CCYRate :price="minPrice" /> - <CCYRate :price="maxPrice" hideSymbol /></div>
						<div class="save-price">
							Save <CCYRate :price="savePrice" />
							<span style="padding-left: 4px; color: #f28810">({{ discount }}% Off)</span>
						</div>
					</div>
				</div>
				<div class="prod_info_price-table">
					<div>{{ lang.wholesalePrice }}:</div>
					<div class="prod_info_price-table__main">
						<div class="tr">
							<div class="td" v-for="[key, val] in priceTable">{{ key }}+</div>
						</div>
						<div class="tr">
							<div class="td" v-for="[key, val] in priceTable"><CCYRate :price="val" /></div>
						</div>
					</div>
				</div>
				<div class="prod_info_btns">
					<a href="mailto:<EMAIL>" title="CONTACT US">contact us</a>
				</div>
			</div>
		</div>

		<div class="stock-detail__description">
			<div class="pd_title">
				<span>{{ lang.description }}</span>
			</div>
			<div class="pd_content" v-html="productData.description"></div>
		</div>
	</div>
</template>

<script>
import Decimal from "decimal.js";
import CCYRate from "@/components/CCYRate.vue";
import GSJJBreadcrumb from "./components/-GSJJBreadcrumb.vue";
import GSJJSwiper from "./components/-GSJJSwiper.vue";
import { getStockDetail } from "@/api/web";

export default {
	components: { CCYRate, GSJJBreadcrumb, GSJJSwiper },

	async asyncData({ store, error, route }) {
		try {
			const { data } = await getStockDetail({
				pageUrl: route.params.cateName,
			});

			const imgList = [];
			const productData = data;
			const keyMap = ["picPathZero", "picPathOne", "picPathTwo", "picPathThree", "picPathFour"];
			keyMap.forEach((key) => {
				if (productData[key]) {
					imgList.push({
						url: productData[key],
						alt: productData.proName,
					});
				}
			});

			return {
				productData,
				imgList,
			};
		} catch (e) {
			return error({ statusCode: 404 });
		}
	},

	head() {
		let meta = [
			{
				hid: "description",
				name: "description",
				content: this.productData.seoDescription,
			},
			{
				hid: "keywords",
				name: "keywords",
				content: this.productData.seoKeyword,
			},
		];
		return {
			title: this.productData.seoTitle,
			meta: meta,
		};
	},

	computed: {
		lang() {
			return this.$store.getters.lang?.exhibition;
		},

		isMobile() {
			return this.$store.getters.isMobile;
		},

		breadcrumb() {
			const { stockCate } = this.productData;
			const list = [];

			// 添加基础导航项
			const baseNavItems = [
				{ text: "Home", link: "/" },
				{ text: "In stock", link: "/products" },
			];

			// 提取构建导航项URL的方法
			const buildCategoryUrl = (category) => `/${category.pageUrlPrefix}/stock/${category.pageUrl}`;

			// 添加分类导航项
			if (stockCate) {
				// 主分类
				list.push({
					text: stockCate.cateName,
					link: buildCategoryUrl(stockCate),
				});

				// 子分类（如果存在）
				const firstChild = stockCate.childList?.[0];
				if (firstChild) {
					list.push({
						text: firstChild.cateName,
						link: buildCategoryUrl(firstChild),
					});
				}
			}

			return [...baseNavItems, ...list];
		},

		// 解析批发价格数据
		wholesalePrices() {
			const { wholesale } = this.productData;
			if (!wholesale) return null;
			return JSON.parse(wholesale);
		},

		// 价格数量对照表
		priceTable() {
			return this.wholesalePrices ? Object.entries(this.wholesalePrices) : [];
		},

		// 所有价格列表
		prices() {
			return this.wholesalePrices ? Object.values(this.wholesalePrices) : [];
		},

		// 当前适用价格
		curPrice() {
			const { priceOne, moq } = this.productData;
			return this.priceTable.reduce((currentPrice, [quantity, price]) => {
				return moq >= quantity ? price : currentPrice;
			}, priceOne);
		},

		// 最低价格
		minPrice() {
			return this.prices.length ? Math.min(...this.prices) : 0;
		},

		// 最高价格
		maxPrice() {
			return this.prices.length ? Math.max(...this.prices) : 0;
		},

		// 节省金额
		savePrice() {
			const { priceZero } = this.productData;
			return new Decimal(priceZero || 0)
				.minus(this.curPrice || 0)
				.toDecimalPlaces(4)
				.toNumber();
		},

		// 折扣率
		discount() {
			const { priceZero = 1 } = this.productData;
			return new Decimal(priceZero || 1)
				.minus(this.curPrice || 0)
				.dividedBy(priceZero || 1)
				.times(100)
				.round()
				.toNumber();
		},
	},
};
</script>

<style lang="scss" scoped>
.stock-detail {
	padding-top: 1rem;
	z-index: auto;

	@include respond-to(mb) {
		background-color: #f2f2f2;
	}
}

.stock-detail__main {
	display: grid;
	grid-template-columns: 38% 54%;
	justify-content: space-evenly;

	.prod_info_title {
		h1 {
			padding: 5px 0;
			font-size: 1.25rem;
			font-weight: 700;
			color: #333;
		}
	}

	.prod_info_number {
		font-size: 12px;
		color: #999;
	}

	.prod_info_review {
		padding: 10px 0;
		font-size: 13px;
		border-bottom: 1px solid #e0e0e0;
	}

	.prod_info_price {
		margin-top: 10px;
		display: flex;
		gap: 4px;
		line-height: 24px;
		font-size: 13px;

		.ccy-box {
			position: absolute;
			padding-bottom: 2px;
			width: 58px;
			line-height: 24px;
			text-indent: 8px;
			border: 1px solid #fff;
			border-radius: 4px;
			cursor: pointer;
			> div:first-child {
				display: flex;
				align-items: center;
				justify-content: space-between;
				b {
					margin-right: -4px;
					margin-top: -16px;
					line-height: 1;
					font-size: 8px;
					transform: rotate(90deg);
				}
			}
			.options {
				display: none;
				flex-direction: column;

				background-color: #fff;
				> div {
					&:hover {
						color: #fff;
						background-color: #888;
					}
				}
			}
			&:hover {
				border-color: #b6b6b6;
				background-color: #fff;
				.options {
					display: flex;
				}
			}
		}

		.cur-price {
			padding-left: 62px;
		}

		.save-price {
			font-size: 12px;
			text-indent: 8px;
			color: #808080;
		}
	}

	.prod_info_price-table {
		margin-top: 40px;
		display: flex;
		flex-direction: column;
		gap: 4px;
		line-height: 24px;
		font-size: 13px;

		&__main {
			width: fit-content;
			border-left: 1px solid #ddd;
			border-top: 1px solid #ddd;
			border-radius: 4px;
			background-color: #fff;
		}
		.tr {
			display: flex;
			border-bottom: 1px solid #ddd;
		}
		.td {
			flex: 0 0 84px;
			line-height: 38px;
			text-align: center;
			border-right: 1px solid #ddd;
		}

		@include respond-to(mb) {
			&__main {
				width: 100%;
			}
			.td {
				flex: 0 0 20%;
			}
		}
	}

	.prod_info_title_mb {
		padding-bottom: 5px;
		h1 {
			font-size: 30px;
			text-align: center;
		}
	}

	.prod_info_btns {
		margin-top: 10px;
		display: flex;
		gap: 10px;
		flex-direction: column;

		a {
			width: fit-content;
			padding: 0 12px;
			height: 30px;
			line-height: 30px;
			font-size: 14px;
			color: #fff;
			text-align: center;
			border-radius: 4px;
			background-color: #ffc000;
			&:hover {
				text-decoration: underline;
			}
		}
	}

	@include respond-to(mb) {
		grid-template-columns: 100%;
	}
}

.left {
	background-color: #fff;
	@include respond-to(mb) {
		padding: 2vw;
	}
}

.right {
	@include respond-to(mb) {
		margin-top: 2vw;
	}
}

.stock-detail__description {
	margin-top: 2vw;
	background-color: #fff;

	.pd_title {
		display: flex;
		height: 36px;
		line-height: 36px;
		font-size: 14px;

		span {
			padding: 0 14px;
			border: 1px solid #ddd;
			border-bottom: 0;
			border-radius: 4px 4px 0 0;
			background-color: #fff;
		}

		&::after {
			flex: 1;
			content: "";
			border-bottom: 1px solid #ddd;
		}
	}

	.pd_content {
		padding: 5px;
	}

	@include respond-to(mb) {
		padding: 2vw;

		.pd_title {
			span {
				padding: 0;
				font-size: 18px;
				font-weight: 700;
				border: none;
			}

			&::after {
				border-bottom: none;
			}
		}

		.pd_content {
			padding: 0;
			font-size: 16px;
		}
	}
}
</style>
