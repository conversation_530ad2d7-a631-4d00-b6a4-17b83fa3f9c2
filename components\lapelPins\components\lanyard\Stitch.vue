<template>
	<div class="stitch">
		<div class="stitchGrid">
			<div class="stitchBox" v-for="(item, index) in itemData.childList" :key="index" :class="{ selected: item.selected }">
				<div class="imgWrap">
					<img :src="getImg(item)" alt="" class="img" />
				</div>
				<div class="stitchInfo">
					<div class="stitchName">{{ item.alias }}</div>
					<div class="optionPrice" :class="{ free:citem == 'Free' }" v-for="(citem,index) in getPrice(item)" :key="index">{{ citem }}</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	name: "Stitch",
	components: {},
	props: {
		itemData: {
			type: Object,
			default: () => {},
		},
	},
	data() {
		return {};
	},
	methods: {
		getImg(item) {
			if (item.imageJson) {
				try {
					let imgUrl = JSON.parse(item.imageJson);
					return imgUrl[0].url;
				} catch (e) {
					console.error("Error parsing imageJson:", e);
					return item.imageUrl || "";
				}
			} else {
				return item.imageUrl || "";
			}
		},
		getPrice(item) {
			if (item.priceInfo && item.priceInfo?.priceShow) {
				return item.priceInfo["priceShow"];
			}
			return "Free";
		},
		selectOption(item) {
			// 实现选择逻辑
			this.$emit("option-selected", item);
		},
	},
	created() {},
	mounted() {},
	computed: {},
	watch: {},
};
</script>
<style scoped lang="scss">
.stitch {
	width: 100%;

	.stitchGrid {
		width: 100%;
		display: grid;
		grid-template-columns: repeat(5, 1fr);
		gap: 20px;

		.stitchBox {
			position: relative;
			border-radius: 10px;
			display: flex;
			flex-direction: column;
			align-items: center;
			gap: 10px;
			padding-bottom: 10px;
			cursor: pointer;
			transition: all 0.3s ease;
			background: #ffffff;
			border: 1px solid #d9dbdd;

			&.selected {
				border: 2px solid #4a90e2;
			}
			.imgWrap {
				width: 100%;
				display: flex;
				align-items: center;
				justify-content: center;
				.img {
					aspect-ratio: 170/180;
					max-width: 100%;
					max-height: 100%;
					object-fit: contain !important;
				}
			}

			.stitchInfo {
				width: 100%;
				display: flex;
				flex-direction: column;
				align-items: center;
				gap: 5px;

				.stitchName {
					font-size: 18px;
					color: #333333;
					padding: 0 1em;
				}

				.stitchPrice {
					font-size: 16px;
					color: #666666;
					padding: 0 1em;
					&.free {
						color: #d81e06;
					}
				}
			}
		}
		@include respond-to(mb) {
			grid-template-columns: repeat(2, 1fr);
			gap: 10px;
			.stitchBox {
				gap: 0px;
				.imgWrap {
					.img {
						width: 80px;
						height: 90px;
					}
				}
				.stitchInfo {
					.stitchName {
						font-size: 14px;
					}
					.stitchPrice {
						font-size: 12px;
					}
				}
			}
		}
	}
}
</style>
