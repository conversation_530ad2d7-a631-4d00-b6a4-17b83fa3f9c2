<template>
	<span>
		{{ byPriceType(itemData) }}
	</span>
</template>

<script>
/**
 * 更多文档：https://www.yuque.com/chaojigang-eu86m/adyym3/vdi42zuvflgolpio
 */
import { exchangeRates } from "@/utils/utils";

export default {
	props: {
		itemData: {
			type: Object,
			default: () => {
				return {};
			},
		},
		textConfig: {
			type: Object,
			default: function (){
				return {
					freeText: this.$store.getters.lang.quote.pins.timeNo,
				};
			},
		}
	},
	data() {
		return {
			priceType: [
				{
					label: "单价",
					value: "1",
					key: "unitPrice",
				},
				{
					label: "总价",
					value: "2",
					key: "totalPrice",
				},
				{
					label: "单价百分比",
					value: "3",
					key: "unitPercent",
				},
				{
					label: "总价百分比",
					value: "4",
					key: "totalPercent",
				},
				{
					label: "递增价格",
					value: "5",
					key: "increasePrice",
				},
				{
					label: "组合单价",
					value: "6",
					key: "composeUnitPrice",
				},
				{
					label: "面积递增价格",
					value: "7",
				},
				{
					label: "重量递增价格",
					value: "10",
					key: "weightPrice",
				},
			],
		};
	},
	computed: {
		lang() {
			return this.$store.getters.lang.quote || {};
		},
		langSemi() {
			return this.$store.getters.lang.semiCustom || {};
		},
		langCart() {
			return this.$store.getters.lang.cart || {};
		},
	},
	methods: {
		byPriceType(citem) {
			let temp = this.priceType.find((x) => {
				return x.value == citem.priceInfo.priceType;
			}).key;
			if (citem.priceInfo.priceType === 3) {
				//加单价百分比
				if (citem.priceInfo[temp] === 0) {
					return this.textConfig.freeText;
				}
				if (citem.priceInfo[temp] >= 1) {
					let priceText = ((citem.priceInfo[temp] - 1) * 100).toFixed(0);
					return this.lang.RushAdd + " " + priceText + "%" + " " + this.langSemi.forUnitPrice;
				} else {
					let priceText = (citem.priceInfo[temp] * 100).toFixed(0);
					return priceText + "%" + " " + this.lang.discount;
				}
			} else if (citem.priceInfo.priceType === 2) {
				//加总价
				let priceText = exchangeRates(citem.priceInfo[temp], this.$store.state.currency.rate || 1).replace(/(\.0*|0+)$/, "");
				return this.lang.RushAdd + " " + this.$store.state.currency.symbol + priceText;
			} else if (citem.priceInfo.priceType === 10) {
				//重量加价
				let priceText = exchangeRates(citem.priceInfo.customWeightPrice || 0, this.$store.state.currency.rate || 1).replace(/(\.0*|0+)$/, "");
				return this.lang.RushAdd + " " + this.$store.state.currency.symbol + priceText;
			}
		},
	},
};
</script>

<style lang="scss" scoped>

</style>
