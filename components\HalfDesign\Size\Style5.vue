<template>
	<div class="newDesign-style5" :class="stepData.styleName" v-if="stepData.attributeFlag !== 'size'">
		<slot name="stepText"></slot>
		<div class="switch-box custom-scrollbar" ref="switchBoxSizeStyle5" :class="{ canScroll: hasScroll }">
			<div class="switch-item" :style="{ pointerEvents: isScroll ? 'none' : 'auto' }" @click="selectStep(item, index)" :class="{ active: index == selectIndex }" v-for="(item, index) in stepData.productParamList" :key="index">
				<span v-if="item.icon" class="iconImg">
					<img :src="item.icon" draggable="false" decoding="async" loading="lazy" />
				</span>
				{{ item.valueName }}
				<div class="questionMark" v-show="item.remark" @click.stop>
					<v-tooltip bottom>
						<template v-slot:activator="{ on, attrs }">
							<v-icon v-bind="attrs" v-on="on" size="18px"> mdi-help-circle-outline </v-icon>
						</template>
						<div class="text-center" style="display: flex; align-items: start">
							<div style="text-align: center; color: #fff; line-height: 1; font-size: 13px; word-break: break-word; white-space: normal; max-width: 250px">
								{{ item.remark }}
							</div>
						</div>
					</v-tooltip>
				</div>
			</div>
		</div>
		<div class="printMethodTips" v-show="selectItem">
			{{ selectItem?.tips }}
		</div>
		<div class="content" v-show="qtyPrice && qtyPrice.length > 0">
			<div class="content_head">
				<div class="head_item">
					{{ langSemiCustom.quantity + " " }}<span v-show="unit">( {{ unit }} )</span>
				</div>
				<div class="head_item" :class="{ text_left: productInfo.discount > 0 }">
					{{ langSemiCustom.unitPrice }}
				</div>
				<div class="head_item" :class="{ p_right: productInfo.discount > 0 }">
					{{ langSemiCustom.subTotal }}
				</div>
			</div>
			<div class="content_body" :class="{ hasDiscount: productInfo.discount > 0 }">
				<div class="body_item" :class="{ active: index == selectIndex2, onePens: onePens }" v-for="(item, index) in qtyPrice" :key="index" @click="selectSize(index, item)">
					<div class="body_item_content">
						<div class="con-radio">
							<label>
								<input type="radio" :checked="selectIndex2 == index" name="sizeNum" :value="index" v-model="selectIndex2" />
								<span class="custom-radio"></span>
							</label>
						</div>
						<span class="sizeNum">{{ item.quantity || 0 }}</span>
					</div>
					<div class="body_item_content">
						<span v-show="productInfo.discount == 0">
							<CCYRate :price="item.unitPrice"></CCYRate>
						</span>
						<span v-show="productInfo.discount > 0">
							<CCYRate :price="item.markupUnitPrice"></CCYRate>
						</span>
					</div>
					<div class="body_item_content wrapBox">
						<div class="priceBox">
							<span v-show="productInfo.discount == 0">
								<CCYRate :price="item.subtotal"></CCYRate>
							</span>
							<span v-show="productInfo.discount > 0">
								<CCYRate :price="item.markupSubtotal"></CCYRate>
							</span>
						</div>
						<div class="arrowBox show" style="display: flex; align-items: center; justify-content: center">
							<div class="newDiscount" :class="{ noShow: item.savePrice == 0 }">
								{{ langSemiCustom.save + " " + item.savePrice + "%" }}
							</div>
						</div>
					</div>
				</div>
				<div class="body_item" :class="{ active: -2 == selectIndex2 }" @click="selectSize(-2, 'item')">
					<div class="body_item_content">
						<div class="con-radio">
							<label>
								<input type="radio" :checked="selectIndex2 == -2" name="sizeNum" :value="-2" v-model="selectIndex2" />
								<span class="custom-radio"></span>
							</label>
						</div>
						<input type="text" id="myInputSize5" v-show="selectIndex2 == -2 && clicked" class="custom-input" :ref="`customInputDom`" @input="updatePrice" @blur="setClick" v-model="inputNum" autofocus />
						<span class="sizeNum" @click="changeInput" v-show="!clicked">
							{{ langQuote.customQuantity }}
						</span>
					</div>
					<div class="body_item_content">
						<span v-show="productInfo.discount == 0">
							<CCYRate :price="oldCustomUnitPrice"></CCYRate>
						</span>
						<span v-show="productInfo.discount > 0">
							<CCYRate :price="customUnitPrice"></CCYRate>
						</span>
					</div>
					<div class="body_item_content">
						<div class="priceBox">
							<span v-show="productInfo.discount == 0">
								<CCYRate :price="oldTotalPrice"></CCYRate>
							</span>
							<span v-show="productInfo.discount > 0">
								<CCYRate :price="totalPrice"></CCYRate>
							</span>
						</div>
						<div class="arrowBox" v-show="!isMobile">
							<div class="newDiscount">
								{{ langSemiCustom.save + " " + "00" + "%" }}
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="errorTip" ref="errorTip">
			<v-alert dense outlined type="error">
				{{ langSemiCustom.miniQty }}
				{{ productInfo.lowestPurchaseQuantity || 1 }}
			</v-alert>
		</div>
	</div>
</template>

<script>
import { addDragable, debounce } from "@/utils/utils";
import { Calculator } from "@/utils/decimal.js";
export default {
	name: "Style5",
	inject: ["getUnitPriceStep", "getCustomPriceData", "getProductInfo", "getUnit"],
	components: {},
	props: {
		stepData: {
			type: Object,
			default: () => ({}),
		},
	},
	data() {
		return {
			selectIndex: -1,
			selectIndex2: -1,
			copyIndex: -1,
			selectQuantity: {},
			selectItem: null,
			hasScroll: false,
			clicked: false,
			inputNum: "",
			totalPrice: 0,
			oldTotalPrice: 0,
			customUnitPrice: 0,
			oldCustomUnitPrice: 0,
			stock: -1,
			sizeParam: {},
			isScroll: false,
			sizeStockIndex: 0,
			nowSelectSize: {},
			debounceCalculatePrice: null,
			onePens: false,
			increasePrice: [],
		};
	},
	watch: {},
	computed: {
		langSemiCustom() {
			return this.$store.getters.lang?.semiCustom;
		},
		langQuote() {
			return this.$store.getters.lang.quote || {};
		},
		isMobile() {
			return this.$store.getters.isMobile;
		},
		customPriceData() {
			return this.getCustomPriceData();
		},
		productInfo() {
			return this.getProductInfo();
		},
		unitPriceStep() {
			return this.getUnitPriceStep();
		},
		qtyPrice() {
			if (!this.selectItem) {
				return [];
			}
			try {
				// let data = this.setPrice(this.productInfo.discount);
				let data = this.selectItem.markupIncreasePrice && JSON.parse(this.selectItem.markupIncreasePrice);
				console.log(data, "000");
				return data;
			} catch (e) {
				console.log(e, "22313");
				return [];
			}
		},
		unit() {
			return this.getUnit();
		},
	},
	methods: {
		updatePrice() {
			let check = this.formatNum();
			this.$emit("disabledNextBtn", !check);
			let sum = parseInt(this.inputNum);
			if (isNaN(sum)) {
				this.resetUnitPrice();
				this.debounceCalculatePrice(0);
				return;
			}
			if (sum > 0) {
				this.setUnitPrice();
			}
			this.debounceCalculatePrice(this.inputNum);
			// this.selectStep2(this.inputNum);
		},
		setUnitPrice() {
			if (!this.inputNum) return;
			let findPrice = this.findPrice({ quantity: this.inputNum }, this.qtyPrice);
			this.customUnitPrice = findPrice.markupUnitPrice;
			this.oldCustomUnitPrice = findPrice.unitPrice;
			this.totalPrice = this.customUnitPrice * this.inputNum;
			this.oldTotalPrice = this.oldCustomUnitPrice * this.inputNum;
		},
		findPrice(item, itemIncreasePrice) {
			let len = itemIncreasePrice.length,
				newNum = parseInt(item.quantity),
				findItem = itemIncreasePrice[0];
			for (let i = 0; i < len; i++) {
				let item = itemIncreasePrice[i],
					nextItem = itemIncreasePrice[i + 1];
				if (newNum >= item.quantity && (nextItem ? newNum < nextItem.quantity : true)) {
					findItem = item;
					break;
				}
			}
			return findItem;
		},
		setPrice(discount = 0) {
			try {
				let increasePrice = JSON.parse(this.unitPriceStep.increasePrice),
					sizeIncreasePrice = JSON.parse(this.nowSelectSize.increasePrice),
					itemIncreasePrice = JSON.parse(this.selectItem.increasePrice);
				const multiBasicUnitPrice = parseFloat(this.customPriceData.multiBasicUnitPrice);

				if (this.selectItem.priceType === 5) {
					increasePrice.forEach((item) => {
						// 获取价格
						const unitPriceValue = parseFloat(item.unitPrice);
						const increase1Value = parseFloat(this.findPrice(item, itemIncreasePrice)?.unitPrice);
						const increase2Value = parseFloat(this.findPrice(item, sizeIncreasePrice)?.unitPrice);
						// 创建 Calculator 实例
						const unitPrice = Calculator.create(unitPriceValue);
						const increase1 = Calculator.create(increase1Value).toNumber();
						const increase2 = Calculator.create(increase2Value).toNumber();
						// 计算总价
						let price = unitPrice.add(increase1).add(increase2);
						let price2 = price.multiply(multiBasicUnitPrice).round(2);
						// 计算折扣
						let price3;
						if (discount !== 0) {
							const discountValue = Math.abs(1 - discount);
							price3 = price2.multiply(discountValue).round(2);
							item.discountPrice = +price3.toNumber().toFixed(2);
						}
						item.unitPrice = +price2.toNumber().toFixed(2);
					});
					let priceKey = "unitPrice";
					if (discount !== 0) priceKey = "discountPrice";
					// 找出最高单价
					const maxUnitPrice = Math.max(...increasePrice.map((item) => item[priceKey]));
					increasePrice.forEach((item) => {
						const saving = ((maxUnitPrice - item[priceKey]) / maxUnitPrice) * 100;
						item.newDiscount = saving == 0 ? "00" : Math.round(saving);
					});
					this.$Bus.$emit("priceSection", increasePrice);
					this.increasePrice = JSON.parse(JSON.stringify(increasePrice));
					let isShowPrice = increasePrice.filter((item) => item.isShow !== 0);
					return isShowPrice || [];
				}
			} catch (error) {
				console.log(error, "setPrice");
			}
		},
		//切换打印方式
		selectStep(item, index, status = false) {
			this.selectIndex = index;
			this.selectItem = item;
			this.setUnitPrice();
			this.$emit("selectStep", {
				type: this.stepData.attributeFlag,
				data: item,
				parent: this.stepData,
				id: this.stepData.id,
				firstSelect: status,
			});
			if (item?.valueName) {
				if (index == 1 && item.valueName === "Blank - No Imprint") {
					/**
					 * 选项卡切换并清空
					 * 兄弟组件在 components/HalfDesign/ArtworkOptions/Style31 目录下
					 */
					this.$Bus.$emit("selectImgColorClear");
				} else if (index == 0 && item.valueName === "Silkscreen") {
					/**
					 * 加载输入框
					 * 兄弟组件在 components/HalfDesign/Design/style5.vue 目录下
					 */
					this.$Bus.$emit("inputTextLoading");
				}
			}
		},
		//设置size
		selectStep2(quantity) {
			if (Object.is(this.stock, null) || this.stock == 0) return;
			this.sizeParam.productParamList.forEach((item) => {
				item.inputNum = "";
			});
			this.sizeParam.productParamList[this.sizeStockIndex].inputNum = +quantity;
			this.$emit("selectStep", {
				type: this.sizeParam.attributeFlag,
				data: this.sizeParam.productParamList[this.sizeStockIndex],
				id: this.sizeParam.id,
			});
		},
		selectSize(index, data) {
			console.log(index, "22132132");
			this.noStock();
			if (index == -2 && this.selectIndex2 != -2) {
				this.changeInput();
				this.formatNum();
				this.resetUnitPrice();
				this.selectStep2(this.inputNum || 0);
			}
			this.selectIndex2 = index;
			this.copyIndex = this.selectIndex2;
			if (this.selectIndex2 != -2) {
				this.inputNum = "";
				this.clicked = false;
				this.selectQuantity = data;
				this.selectStep2(data.quantity);
				let errDom = this.$refs.errorTip;
				errDom.style.display = "none";
				this.resetUnitPrice();
				this.$emit("disabledNextBtn", false);
			}
		},
		decideScroll() {
			const container = this.$refs.switchBoxSizeStyle5;
			if (container?.scrollWidth > container?.clientWidth) {
				this.hasScroll = true;
				addDragable(
					container,
					() => {
						this.isScroll = true;
					},
					() => {
						this.isScroll = false;
					}
				);
			} else {
				this.hasScroll = false;
			}
		},
		changeInput() {
			this.clicked = true;
			this.selectIndex2 = -2;
			this.$nextTick(() => {
				this.$refs.customInputDom.focus();
			});
		},
		formatNum() {
			this.inputNum = (this.inputNum + "").replace(/[^\d]/g, "");
			if (this.stock && this.stock > 0 && this.inputNum > this.stock) {
				this.inputNum = String(this.stock);
			}
			if (this.stock <= 0) {
				this.inputNum = "";
			}
			let sum = parseInt(this.inputNum || 0);
			let errDom = this.$refs.errorTip;
			if (sum < this.productInfo.lowestPurchaseQuantity) {
				try {
					errDom.style.display = "block";
					return false;
				} catch (error) {}
			} else {
				try {
					errDom.style.display = "none";
					return true;
				} catch (e) {}
			}
		},
		setClick() {
			if (!this.inputNum) {
				this.clicked = false;
				if ((this.selectIndex2 = -2)) {
					this.formatNum();
				}
			}
		},
		selectDefault() {
			if (this.stepData.productParamList.length) {
				this.selectStep(this.stepData.productParamList[0], 0, true);
			}
		},
		//存储size 参数
		setSizeParam(data) {
			this.sizeParam = data;
			this.stock = this.sizeParam.productParamList[0].stock;
			this.nowSelectSize = this.sizeParam.productParamList[0];
		},
		setInputNum(num, type = false, noSelect = true) {
			this.noStock();
			if (type) {
				this.selectIndex2 = -2;
				this.clicked = true;
				this.inputNum = num;
				this.setUnitPrice();
				this.$nextTick(() => {
					if (noSelect) {
						this.onePens = true;
						document.querySelector("#myInputSize5").readOnly = true;
					}
				});
			} else {
				this.selectIndex2 = 0;
				this.selectQuantity = this.qtyPrice[0];
			}

			this.selectStep2(num);
			// this.$emit("updatePrice");
		},
		updateQty(type) {
			if (type == "addCart") return;
			if (this.sizeParam && this.sizeParam.productParamList.length > 0) {
				this.stock = this.sizeParam.productParamList[this.sizeStockIndex].stock;
				this.noStock();
				this.formatNum();
				this.selectSize(this.selectIndex2, this.selectQuantity);
			}
		},
		resetUnitPrice() {
			if (!this.inputNum) {
				this.totalPrice = 0;
				this.oldTotalPrice = 0;
				this.customUnitPrice = 0;
				this.oldCustomUnitPrice = 0;
			}
		},
		setSelectSize(data) {
			let index = this.sizeParam.productParamList.findIndex((item) => item.id == data.id);
			if (+index == +this.sizeStockIndex) return;
			this.sizeStockIndex = index;
			this.nowSelectSize = data;
			this.updateQty();
		},
		noStock() {
			if (!this.stock) {
				this.selectIndex2 = -1;
				this.selectQuantity = { quantity: 0 };
				this.sizeParam.productParamList.forEach((item) => {
					item.inputNum = "";
				});
				this.$emit("selectStep", {
					type: this.sizeParam.attributeFlag,
					parent: this.sizeParam,
					data: this.sizeParam.productParamList[this.sizeStockIndex],
					id: this.sizeParam.id,
				});
				this.$toast.error(this.langSemiCustom.productOutStock);
				return;
			}
		},
	},
	created() {
		this.debounceCalculatePrice = debounce(this.selectStep2, 500);
	},
	mounted() {
		if (this.stepData.attributeFlag == "size") return;
		this.$Bus.$on("updateQty", this.updateQty);
		this.$Bus.$on("selectDefaultPrintStep", this.selectDefault);
		this.$Bus.$on("setSizeParam", this.setSizeParam);
		this.$Bus.$on("selectDefaultSizeStep", this.setInputNum);
		this.$Bus.$on("uploadQty", this.setSelectSize);
		this.decideScroll();
	},
	beforeDestroy() {
		this.$Bus.$off("updateQty", this.updateQty);
		this.$Bus.$off("selectDefaultPrintStep", this.selectDefault);
		this.$Bus.$off("setSizeParam", this.setSizeParam);
		this.$Bus.$off("selectDefaultSizeStep", this.setInputNum);
		this.$Bus.$off("uploadQty", this.setSelectSize);
	},
};
</script>

<style scoped lang="scss">
@import "~assets/css/half.scss";

.con-radio {
	label {
		display: flex;
		align-items: center;
		flex-direction: row-reverse;
	}

	/* 未选中状态下的样式 */
	input[type="radio"] {
		/* 隐藏原始的单选按钮 */
		display: none;
	}

	/* 自定义样式 */
	.custom-radio {
		display: inline-block;
		width: 16px;
		height: 16px;
		border-radius: 50%;
		border: 1px solid #333;
		position: relative;
		cursor: pointer;
	}

	/* 选中状态下的样式 */
	.custom-radio:before {
		content: "";
		display: block;
		width: 0.5em;
		height: 0.5em;
		background-color: #fff;
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		border-radius: 50%;
	}

	/* 选中状态下的外圈样式 */
	input[type="radio"]:checked + .custom-radio {
		background-color: $color-primary !important;
		border: 1px solid $color-primary;
	}
}

.content {
	position: relative;
	width: 100%;
	overflow: hidden;

	.content_head {
		display: flex;
		align-items: center;
		justify-content: space-between;
		width: 100%;
		height: 40px;
		font-family: Roboto;
		font-weight: 400;
		font-size: 14px;
		color: #333333;
		border-bottom: 1px solid #f5f5f5;
		padding: 0 20px;

		.head_item {
			width: 33.3%;
			word-wrap: break-word;
			text-align: left;

			&:nth-child(2) {
				text-align: center;
			}

			&:nth-child(3) {
				text-align: right;
			}

			&.p_right {
				padding-right: 40px;
			}
		}
	}

	.content_body {
		.body_item {
			display: flex;
			align-items: center;
			justify-content: space-between;
			font-family: Roboto;
			font-weight: 400;
			font-size: 14px;
			color: #333333;
			border-bottom: 1px solid #f5f5f5;
			cursor: pointer;
			padding: 14px 20px;
			height: 44px;
			overflow: hidden;
			min-width: 0;

			&.active {
				background: #f5f5f5;
			}

			&.onePens {
				pointer-events: none;
				background: #f5f5f5;
			}

			&:hover {
				background: rgb(245, 245, 245, 0.9);
			}

			.body_item_content {
				width: 33.3%;
				overflow: hidden;
				display: flex;
				align-items: center;
				column-gap: 8px;
				font-family: Roboto;
				font-weight: 400;
				min-width: 120px;

				.priceBox {
					display: flex;
					align-items: center;
					column-gap: 8px;
				}

				&:nth-child(2) {
					justify-content: center;
				}

				&:nth-child(3) {
					justify-content: flex-end;
				}

				.custom-input {
					border: 1px solid #9c9c9c;
					width: 120px;
					height: 30px;
					padding: 0 4px;

					&:focus {
						border: 1px solid #9c9c9c;
					}

					&:focus::placeholder {
						opacity: 0;
					}

					&::placeholder {
						font-family: Roboto;
						font-weight: 400;
						font-size: 14px;
						color: #333333;
					}
				}

				.sizeNum {
					font-family: Roboto;
					font-weight: 400;
					font-size: 14px;
					color: #333333;
				}

				.grayFont {
					color: #999999;
					text-decoration-line: line-through;
				}

				.arrowBox {
					opacity: 0;
					//   color: red;
					color: green;
					&.show {
						opacity: 1;
					}
					.newDiscount {
						&.noShow {
							opacity: 0;
							@include respond-to(mb) {
								display: none;
							}
						}
					}

					.v-icon {
						font-size: 14px;
						color: red;
					}
				}
			}
		}
	}
}

.newDesign-style5 {
	.switch-box {
		margin: auto;
		width: fit-content;
		max-width: 100%;
		overflow-x: auto;
		display: flex;
		align-items: center;
		justify-content: flex-start;
		column-gap: 2px;
		background: #ebebeb;
		border: 2px solid #ebebeb;
		border-radius: 4px;

		// scrollbar-width: none;
		&::-webkit-scrollbar {
			/*滚动条整体样式*/
			width: 5px;
			height: 2px;
		}

		&.canScroll {
			justify-content: flex-start;
		}

		.switch-item {
			flex-shrink: 0;
			display: flex;
			align-items: center;
			justify-content: center;
			gap: 4px;
			width: fit-content;
			min-width: 100px;
			height: 36px;
			text-align: center;
			border-radius: 4px;
			padding: 0 10px;
			font-family: Roboto;
			font-weight: 400;
			font-size: 16px;
			cursor: pointer;
			transition: background 0.15s ease-in-out;
			user-select: none;
			.iconImg {
				height: 50%;
				aspect-ratio: 1;
				display: flex;
				align-items: center;
				justify-content: center;
				border-radius: 50%;
				overflow: hidden;
				img {
					width: 100%;
					height: 100%;
					object-fit: contain;
				}
			}

			.questionMark {
				display: flex;
				align-items: center;
				line-height: initial;
			}

			&.active {
				font-weight: bold;
				color: $color-primary;
				background: #ffffff;
				box-shadow: 1px 1px 4px 0px rgba(0, 0, 0, 0.15);
				.questionMark {
					.v-icon {
						color: $color-primary;
					}
				}
			}

			&:hover {
				background: #ffffff;
			}
		}
	}

	.printMethodTips {
		margin: 10px 20px 0;
		font-size: 14px;
	}
}

@include respond-to(mb) {
	.newDesign-style5 {
		.switch-box {
			justify-content: flex-start;

			.switch-item {
				min-width: 80px;
				height: 26px;
				font-size: 12px;
			}
		}

		.printMethodTips {
			margin: 10px 10px 0;
			font-size: 12px;
		}
	}

	.content {
		.content_head {
			padding: 0 10px;

			.head_item {
				&.text_left {
					text-align: left;
					padding-left: 10px;
				}

				&:nth-child(3) {
					text-align: center;
				}

				&.p_right {
					padding-right: 0;
				}
			}
		}

		.content_body {
			.body_item {
				padding: 4px 10px;
				font-size: 12px;

				.body_item_content {
					min-width: auto;
					word-break: break-word;
					word-wrap: break-word;
					width: 33.3%;

					.priceBox {
						justify-content: center;
					}

					&.wrapBox {
						text-align: center;
						display: block;
					}

					.sizeNum {
						font-size: 12px;
						word-wrap: break-word;
						word-break: break-word;
					}

					.custom-input {
						width: 100%;
					}
				}
			}

			&:not(.hasDiscount) .body_item {
				.body_item_content {
					&:nth-child(1) {
						justify-content: flex-start;
					}

					&:nth-child(2) {
						justify-content: center;
					}

					&:nth-child(3) {
						justify-content: center;
					}
				}
			}

			&.hasDiscount {
				.body_item {
					.body_item_content {
						&:nth-child(1) {
							width: 28%;
						}

						&:nth-child(2) {
							// flex: 1;
							width: 28%;
							justify-content: flex-start;
							margin-left: 10px;
						}

						&:nth-child(3) {
							--ceshi: 12px;
							flex: 1;
							justify-content: center;

							.priceBox {
								column-gap: 4px;
								margin-bottom: 4px;

								span {
									white-space: nowrap;
								}
							}
						}
					}
				}
			}
		}
	}
}
</style>
