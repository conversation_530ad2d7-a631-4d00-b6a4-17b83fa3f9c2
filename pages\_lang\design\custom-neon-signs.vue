<template>
  <client-only>
    <div id="dzApp"
      noDebounce>
      <div id="dsWrap">
        <div class="ds-content">
          <div class="left-area-wrap">
            <left-area v-if="show && !isMb"
              @showMyDesign="showMyDesign"></left-area>
          </div>

          <div class="ds-center-area"
            @click="showDefault">
            <div class="ds-right-content">
              <div class="tool-bar-wrap">
                <tool-bar v-if="show && !isMb && !showDesignImg"
                  :class="{ visibilityHidden: isDefault, isYd: editHelp }"
                  @saveClick="saveClick"
                  @shareClick="shareClick"></tool-bar>
                <div class="buttonGroup"
                  v-if="show && !isMb">
                  <button class="btn generate"
                    v-if="!showDesignImg"
                    @click="generate">
                    <b class="icon-shengcheng"></b> {{ lang.generate }}
                  </button>
                </div>

              </div>
              <div class="fbWrap"
                id="designBody"
                v-show="!showDesignImg"
                :class="{isSticker:canvasWorker.isSticker}">
                <!-- <div class="des"
                  :class="{ visibilityHidden: isDefault || !canvasWorker.templateName }">{{ canvasWorker.templateName }}
                </div> -->
                <div id="workspace"
                  v-loading="loading">
                  <canvas id="fabricCanvas"></canvas>
                  <mbTopTool class="mbTopTool"
                    v-if="show"></mbTopTool>
                  <!-- <mbRightTool class="mbRightTool"
                    v-if="show && !canvasWorker.isSticker"></mbRightTool> -->
                </div>
                <zoom class="zoom"></zoom>
                <mouseMenu v-if="show"></mouseMenu>
                <div class="stickerSize"
                  v-if="isMb && canvasWorker.isSticker">
                  <div>Size:</div>
                  <div class="size">
                    <el-popover placement="top-start"
                      popper-class="stickerSizeToolTip"
                      effect="light"
                      width="117"
                      trigger="manual"
                      content="This field is required"
                      v-model="sizeWError">
                      <input type="text"
                        slot="reference"
                        v-model="canvasWorker.stickerSize.w"
                        class="sizeInput"
                        :class="{isError:sizeWError}"
                        @input="changeSize('w')">
                    </el-popover>
                    <span class="c">X</span>
                    <el-popover placement="top-start"
                      popper-class="stickerSizeToolTip"
                      effect="light"
                      width="117"
                      trigger="manual"
                      content="This field is required"
                      v-model="sizeHError">
                      <input type="text"
                        slot="reference"
                        v-model="canvasWorker.stickerSize.h"
                        class="sizeInput"
                        :class="{isError:sizeHError}"
                        @input="changeSize('h')">
                    </el-popover>
                    <el-dropdown trigger="click"
                      @command="handleStickerSizeCommand">
                      <div class="sizeSelect">
                        <b class="icon-down"></b>
                      </div>
                      <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item :command="{w:1,h:1}">1” x 1”</el-dropdown-item>
                        <el-dropdown-item :command="{w:2,h:2}">2” x 2”</el-dropdown-item>
                        <el-dropdown-item :command="{w:3,h:3}">3” x 3”</el-dropdown-item>
                        <el-dropdown-item :command="{w:4,h:4}">4” x 4”</el-dropdown-item>
                        <el-dropdown-item :command="{w:5,h:5}">5” x 5”</el-dropdown-item>
                      </el-dropdown-menu>
                    </el-dropdown>
                  </div>
                </div>
                <div class="space"
                  v-if="isMb"></div>
                <div class="mb-control-area"
                  v-if="isMb">
                  <div class="space"></div>
                  <dzTips v-if="show"></dzTips>
                </div>
              </div>
            </div>

            <!-- generate之后的图 -->
            <div v-if="showDesignImg"
              class="predict">

              <img :src="currentDesign.predictImageUrl"
                alt="">

              <div class="designImgBtn">

                <div class="centerBtn">
                  <button class="download"
                    @click="download"><b class="icon-xiazai1"></b></button>
                  <button class="share"
                    @click="shareClick(currentDesign.predictImageUrl)"><b class="icon-fenxiang"></b></button>
                </div>

                <button class="btn backToDraw"
                  v-if="showDesignImg"
                  @click="backToDraw">
                  {{ lang.backToDraw }}
                </button>
              </div>

            </div>
            <!-- <div class="right-tool-wrap"
              v-if="!isMb">
              <right-tool v-if="show"
                :class="{ visibilityHidden: isDefault, isYd: editHelp }"
                @saveClick="saveClick"
                @shareClick="shareClick"></right-tool>
            </div> -->
            <!-- 模板列表-->
            <!-- <template-list v-if="show && !isMb"></template-list> -->
            <!-- 元素列表-->
            <design-list v-if="show && !isMb"
              ref="designListRef" @shareClick="shareClick" @updateCreation="getCreation"></design-list>
            <art-list v-if="show && !isMb"></art-list>
          </div>
          <div v-show="!showDesign && !showArt"
            class="ds-right-area">
            <div class="topBar">
              <strong class="title">{{ lang.myCreation }}</strong>
            </div>

            <div class="area-content"
              neonAiScrollBar
              :class="{'flexLayout': creationList.length == 0}">
              <div class="creationList"
                v-if="creationList.length > 0">
                <div class="creationItem"
                  v-for="item in creationList"
                  @click="selectCreation(item)"
                  :class="{'active': item.id == currentDesign.id}"
                  :key="item.id"
                  >
                  <img :src="item.creationUrl"
                    :alt="item.type + item.id">
                  <div class="oprate-box">
                    <div class="buttonGroup">
                      <button class="download" @click.stop="downloadItem(item)">
                        <b class="icon-xiazai"></b>
                      </button>
                      <button class="delete" @click.stop="deleteItem(item)">
                        <b class="icon-shanchu"></b>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              <p class="noWork"
                v-else>{{ lang.noWorkTips }}</p>
            </div>

            <div class="area-bottom">
              <button class="quoteBtn" @click="getFastQuote('single')">{{ lang.getFastQuote }}</button>
            </div>
          </div>

          <div class="bottomAreaWrap"
            v-if="!isMb">
            <div class="bottom-area"
              v-if="showDesign">
              <button class="quote-btn"
                @click="getFastQuote('mutiple')">
                {{ lang.getFastQuote }}
              </button>
            </div>
            <!-- <bottom-area v-if="showDesign" @next="next"></bottom-area> -->
          </div>
        </div>
      </div>
      <change-Element v-if="showReplaceDialog"
        :width="device !== 'mb' ? '710px' : '92%'">
      </change-Element>
      <tool-option v-if="show"
        ref="toolOption"></tool-option>
      <dzYd v-if="editHelp && show && !isMb"
        @closeEditHelp="closeEditHelp"></dzYd>
      <myDesign :myDesignDialog.sync="myDesignDialog"
        v-if="isLogin && myDesignDialog"></myDesign>
      <saveDialog :saveDialogDialog.sync="saveDialogDialog"
        :saveShow.sync="saveShow"></saveDialog>
      <preview></preview>
      <shareDialog :shareDialogDialog.sync="shareDialogDialog"
        :saveShow.sync="shareShow"
        :shareImg="shareImg"
        :shareLink="shareLink"></shareDialog>
      <platDialog ref="platDialog"
        :userUploadList="userUploadList"
        :platDialog.sync="platDialog"
        :previewImg.sync="previewImg"
        :originPreviewImg="originPreviewImg"
        v-if="show"></platDialog>
      <neon-loading v-if="neonLoading"
        imgSrc="https://oss-static-cn.liyi.co/web/quoteManage/20240424/neonloading_20240424PQzFNR.mp4"></neon-loading>
    </div>
  </client-only>
</template>
<script>
import { addDesignTemplateShare } from "~/api/newDzxt";
import shareDialog from "@/components/MyDzxt/shareDialog.vue";
import saveDialog from "@/components/NeonDzxt/saveDialog.vue";
import myDesign from "@/components/MyDzxt/myDesign.vue";
import "@/plugins/element";
import leftArea from "@/components/NeonDzxt/leftArea.vue";

import dzHead from "~/components/MyDzxt/dzHead.vue";
import ColorPickerItem from "@/components/MyDzxt/ColorPickerItem.vue";
import zoom from "@/components/MyDzxt/zoom.vue";
import side from "@/components/MyDzxt/side.vue";
import toolBar from "@/components/NeonDzxt/toolBar.vue";
import topBar from "@/components/MyDzxt/topBar.vue";
import rightTool from "@/components/MyDzxt/rightTool.vue";
import bottomArea from "@/components/MyDzxt/bottomArea.vue";
import templateList from "@/components/MyDzxt/templateList.vue";
import designList from "@/components/NeonDzxt/designList.vue";
import artList from "@/components/NeonDzxt/artList.vue";
import toolOption from "@/components/NeonDzxt/toolOption.vue";
import neonLoading from "@/components/NeonDzxt/loading.vue";

import {
  getAllColorCard,
  getAllColorType,
  getByUrlAndProId,
  geTemplateByRouteUrl,
  getFirstBlankByCategoryId,
  getUserTemplateById,
} from "@/api/newDzxt";

import { neonApi } from "@/api/neon/neon";

import changeElement from "@/components/NeonDzxt/changeElement.vue";
import canvas from "@/assets/js/fabricCore/dzPublic";
import dzYd from "@/components/MyDzxt/dzYd.vue";
import { dataURLtoFile, deepClone, downloadImage } from "@/utils/utils";
import {
  getDesignTemplateShare,
  addTemporaryUserTemplates,
} from "~/api/newDzxt";
import dzTips from "@/components/MyDzxt/dzTips.vue";
import controlBottomArea from "@/components/MyDzxt/control-bottom-area.vue";
import mbTopTool from "@/components/NeonDzxt/mbTopTool.vue";
import mbRightTool from "@/components/MyDzxt/mbRightTool.vue";
import preview from "@/components/MyDzxt/preview.vue";
// 右键菜单
import mouseMenu from "@/components/MyDzxt/contextMenu/index.vue";
import platDialog from "@/components/MyDzxt/platDialog.vue";
import { uploadFile } from "@/utils/oss";
import "@/assets/css/neon/neonFont.css"

import { medalsApi } from "@/api/medals/medals";

export default {
  head: {
    link: [
      {
        href: "//at.alicdn.com/t/c/font_3787566_z5sjjrn413.css", //黑
        rel: "stylesheet",
        body: true,
      },
      {
        href: "//at.alicdn.com/t/c/font_3787572_ovbvnsxlgfe.css", //彩
        rel: "stylesheet",
        body: true,
      },
    ],
    script: [
      {
        src: "https://cdn.jsdelivr.net/npm/pdfjs-dist@3.1.81/build/pdf.min.js",
        async: true,
        body: true,
      },
    ],
  },
  computed: {
    isLogin() {
      return this.$store.getters.isLogin;
    },
    isDefault() {
      return this.$store.state.design.isDefault;
    },
    loading() {
      return this.$store.state.design.loading;
    },
    device() {
      return this.$store.state.device;
    },
    langDesign() {
      return this.$store.getters.lang?.design;
    },
    isMb() {
      return this.$store.state.device === "mb";
    },
    showReplaceDialog() {
      return this.$store.state.design.showReplaceDialog;
    },
    templateId() {
      return this.$route.query.templateId;
    },
    showArt(){
      return this.$store.state.design.showArt
    },
    showDesign() {
      return this.$store.state.design.showDesign;
    },
    showDesignImg() {
      return this.$store.state.design.showMyDesignImg;
    },
    currentDesign() {
      return this.$store.state.design.currentDesign;
    }
  },

  data() {
    const lang = this.$store.getters.lang?.design;
    return {
      cateId: 176,
      pid: 67,
      lang: lang,
      userUploadList: [],
      sizeWError: false,
      sizeHError: false,
      previewImg: "",
      originPreviewImg: "",
      platDialog: false,
      shareShow: 1,
      shareImg:
        "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20220821/20220821rB4ZHrbJ.png",
      shareLink: "",
      shareDialogDialog: false,
      canvasWorker: canvas,
      editHelp: false,
      signUpDialog: false,
      canvas: null,
      hideTool: false,
      workspace: null,
      show: false,
      myDesignDialog: false,
      saveDialogDialog: false,
      saveShow: 1,
      selectArr: [],

      creationList: [],

      neonLoading: false,
    };
  },
  provide() {
    return {
      canvas: this.canvasWorker,
    };
  },
  watch:{
    isLogin(newV, oldV){
      newV && this.getCreation()
    }
  },
  components: {
    rightTool,
    toolBar,
    topBar,
    zoom,
    ColorPickerItem,
    dzHead,
    leftArea,
    side,
    bottomArea,
    templateList,
    designList,
    artList,
    toolOption,
    dzYd,
    changeElement,
    myDesign,
    saveDialog,
    dzTips,
    controlBottomArea,
    mbTopTool,
    mbRightTool,
    preview,
    mouseMenu,
    platDialog,
    shareDialog,
    neonLoading,
  },
  methods: {
    async generate() {
      const emptyFlag = canvas.isEmptyCanvas()
      if(emptyFlag) return
      // const noImg = await canvas.getImgUrlNoTemplate(this.canvas);
      this.previewImg = await canvas.getTemplatePicPath(1, true);

      let file = dataURLtoFile(this.previewImg, 1);
      this.neonLoading = true;
      await uploadFile(file).then((picPath) => {
        neonApi
          .neonPrediction({
            inputImageUrl: picPath,
            imageSize: file.size,
          })
          .then(async (res) => {

            await neonApi.addUserCreation({
              creationUrl: res.data.predictImageUrl,
            });
            await this.getCreation()
            const newDesign = this.creationList.find(x => x.predictImageUrl == res.data.predictImageUrl)
            this.$store.commit("design/set_currentDesign", newDesign);
            this.$store.commit("design/set_showMyDesignImg", true);
          })
          .finally(() => {
            this.neonLoading = false;
          });
      });
    },

    // 下载生成的效果图
    download() {
      downloadImage(
        this.currentDesign.predictImageUrl,
        this.currentDesign.clipartCode || 'MyDeisgn'
      );
    },

    // 下载一个creation
    downloadItem(val){
       downloadImage(
        val.predictImageUrl,
        (val?.type + val?.id) || 'MyDeisgn'
      );
    },

    // 移除一个creation
    deleteItem(val){
      this.$confirm(`<div class='d-flex-center flex-column'><b class='warningIcon icon-sanjiao-gantanhao'></b><p>${ this.lang.delTemplate }</p></div>`, this.lang.tips, {
        confirmButtonText: this.lang.comfirm,
        cancelButtonText: this.lang.cancel,
        dangerouslyUseHTMLString: true,
        customClass: 'warningCheckDialog'
      }).then(async () => {
        await neonApi.delUserCreation({ id: val.id });
        await this.getCreation();
      })
    },

    // 返回绘制页面
    backToDraw() {
      this.$store.commit("design/set_showMyDesignImg", false);
    },

    async getFastQuote(type) {
      let quoteUrl = this.$store.getters["design/getQuoteUrl"];
      if (!quoteUrl) return;
      let arr = []
      if(type=='mutiple'){
        arr = this.$refs.designListRef.getAllChecked();
      }else{
        let obj = {}
        if(this.showDesign){
            obj.original_filename = this.currentDesign.type + this.currentDesign.id,
            obj.secure_url = this.currentDesign.predictImageUrl
        }else{
          const emptyFlag = canvas.isEmptyCanvas()
          if(emptyFlag) return
          const imgSrc = await canvas.getTemplatePicPath(1, true);
          let file = dataURLtoFile(imgSrc, 1);
          let picPath = await uploadFile(file);
          obj.original_filename = 'MyDesign'
          obj.secure_url = picPath
        }
        arr = [obj]
      }
      let url = new URL(
        `${
          window.location.origin
        }${quoteUrl}?id=13729&uploadList=${JSON.stringify(arr)}`
      );
      window.removeEventListener("beforeunload", canvas.confirmExit);
      window.location.href = url.toString();
    },

    // 选择效果图
    selectCreation(val) {
      this.$Bus.$emit("hideTools");
      this.$store.commit("design/set_currentDesign", val);
      this.$store.commit("design/set_showMyDesignImg", true);
    },

    changeSize(type) {
      canvas.stickerSize[type] = canvas.stickerSize[type].replace(
        /[^0-9.]/g,
        ""
      );
      if (!canvas.checkStickerSize()) {
        return false;
      }
      canvas.initStickerShape();
    },
    handleStickerSizeCommand(command) {
      canvas.stickerSize.w = command.w.toString();
      this.changeSize("w");
      canvas.stickerSize.h = command.h.toString();
      this.changeSize("h");
    },
      // type == "creation" ? val.creationUrl : val.templateUrl


    async shareClick({ val, type }) {
      const loading = this.$loading({
        lock: true,
      });
      try {
        let specification = null;
        if (canvas.isSticker) {
          if (!canvas.checkStickerSize()) {
            loading.close();
            return false;
          }
          specification = JSON.stringify({
            width: canvas.stickerSize.w,
            height: canvas.stickerSize.h,
          });
        }
        let layersData = ''
        if(type == 'creation'){
          this.shareImg = val.creationUrl
        }else if(type == 'design'){
          this.shareImg = val.templateUrl
          layersData = JSON.parse(val.templateFile);
        }else{
          this.shareImg = await canvas.getTemplatePicPath();
          layersData = canvas.getTemplateData();
        }
        let res = await addDesignTemplateShare({
          productId: this.$store.state.design?.pageInfo?.id,
          imgUrl: this.shareImg,
          layersData: JSON.stringify(layersData),
          specification,
          routingName: this.$route.params.systemName,
        });
        this.shareShow = 1;
        this.shareDialogDialog = true;
        if(type == 'creation'){
          this.shareLink = this.shareImg
        }else{
          this.shareLink = `${window.location.origin}/us/design/custom-neon-signs?id=${res.data.secret}`;
        }
        loading.close();
      } catch (e) {
        loading.close();
      }
    },
    showDefault(e) {
      let target = e.target;
      if (target.tagName !== "CANVAS") {
        let topBar = document.querySelector(".tool-bar"); // 获取目标容器元素
        if (topBar && topBar.contains(target)) {
          return;
        }
        this.$Bus.$emit("showDefault");
        this.$Bus.$emit("hideTools");
        this.$nextTick(() => {
          this.canvas.discardActiveObject().requestRenderAll();
        });
      }
    },
    //保存弹窗
    saveClick() {
      if (canvas.isSticker) {
        if (!canvas.checkStickerSize()) {
          return false;
        }
      }
      this.saveShow = 1;
      this.saveDialogDialog = true;
    },
    //MyDesign弹窗
    showMyDesign() {
      this.myDesignDialog = true;
    },
    closeEditHelp() {
      this.editHelp = false;
    },
    //初始化画布
    initCanvas() {
      this.canvas = canvas.initCanvas("fabricCanvas",'blue',{ width: 900, height: 500 });
      this.canvas.on({
        "object:added": (e) => {
          if (e.target.id !== "workspace") {
            this.$store.commit("design/set_isDefault", false);
          }
        },
      });
    },
    handleResize() {
      window.requestAnimationFrame(() => {
        if (this.isMb) {
          let clientWidth = document.documentElement.clientWidth;
          let workspace = document.querySelector("#workspace");
          workspace.style.flex = `0 0 ${
            clientWidth < 500 ? clientWidth : 500
          }px`;
          canvas.hideGrid();
        }
        canvas.auto();
      });
    },
    getDesignTemplateShare() {
      return new Promise((resolve, reject) => {
        getDesignTemplateShare({
          secret: this.$route.query.id,
        })
          .then((res) => {
            //判断是否是sticker设计系统
            if (canvas.isSticker) {
              let specification = JSON.parse(res.data.specification);
              canvas.stickerSize = {
                w: specification.width,
                h: specification.height,
              };
            }
            resolve(res.data.layersData);
          })
          .catch((err) => {
            reject(err);
          });
      });
    },
    async loadDefaultTemplate(productType) {
      //判断是半定制还是全定制,1半定制,0全定制
      if (productType === 1) {
        //半定制暂时不做处理
      } else if (productType === 0) {
        //判断路由是否存在模板，如果不存在默认加载第一个光版模板，否则获取路由上的模板数据
        if (this.$route.params.templateName) {
          let data = await geTemplateByRouteUrl({
            routeUrl: this.$route.params.templateName,
          });
          if (data.data) {
            canvas.templateName = data.data.templateName;
            //判断是否是sticker设计系统
            if (canvas.isSticker) {
              let specification = JSON.parse(data.data.specification);
              canvas.stickerSize = {
                w: specification.width,
                h: specification.height,
              };
            }
            await canvas.loadTemplate(data.data.templateFile);
          }
        } else {
          let data = await getFirstBlankByCategoryId({
            categoryId: this.$store.state.design?.pageInfo?.id,
          });
          if (data.data) {
            canvas.templateName = data.data.templateName;
            //判断是否是sticker设计系统
            if (canvas.isSticker) {
              let specification = JSON.parse(data.data.specification);
              canvas.stickerSize = {
                w: specification.width,
                h: specification.height,
              };
            }
            await canvas.loadTemplate(data.data.templateFile);
          }
        }
      }
    },
    //根据id查询模版数据
    getUserTemplateById(templateId) {
      return new Promise((resolve) => {
        getUserTemplateById({
          id: templateId,
        }).then((res) => {
          //判断是否是sticker设计系统
          if (canvas.isSticker) {
            let specification = JSON.parse(res.data.specification);
            canvas.stickerSize = {
              w: specification.width,
              h: specification.height,
            };
          }
          resolve(res.data.templateFile);
        });
      });
    },

    getAllParam() {
      medalsApi.getCateParamRelationByCateId({ cateId: 176 }).then((res) => {
        const colors = res.data
          .find((x) => x.paramName == "Select Color")
          ?.childList.reduce((arr, x) => {
            const obj = {
              ...x,
              code: x.paramCode,
              id: x.id,
              pantone: x.alias,
              imgUrl: x.isNeonColorful
                ? "https://oss-static-cn.liyi.co/web/quoteManage/20240417/063a37accf1_20240417274mwc.gif"
                : "",
            };
            obj.paramName != "RGB Color" && arr.push(obj);
            return arr;
          }, []);
        const fonts = res.data
          .find((x) => x.paramName == "Select Font")
          ?.childList.map((x) => x.paramName);
        this.$store.commit("design/set_colorList", colors);
        this.$store.commit("design/set_fontList", fonts);
      });
    },


    // 获取最新的creation
    async getCreation(){
      await neonApi.getUserCreation().then((res) => {
      this.creationList = res.data.map((x) => {
        return {
          ...x,
          inputImageUrl: x.creationUrl,
          predictImageUrl: x.creationUrl
        };
      });
    });
    }
  },
  created() {
    this.getAllParam();
    getAllColorType().then((res) => {
      this.$store.commit("design/set_colorType", res.data);
    });
    this.getCreation()
  },
  async mounted() {
    await this.$nextTick();
    // 根据url获取信息
    let urlData = (
      await getByUrlAndProId({
        url: `/design/custom-neon-signs/`,
      })
    ).data;
    let positionConfigs = deepClone(urlData.positionConfigs);
    positionConfigs = [
      {
        id: null,
        categoryId: null,
        designPostion: 0,
        positionColorLimitation: null,
        positionCopywriting: "",
        url: "",
        templateData: null,
      },
    ];
    positionConfigs.forEach((item) => {
      item.url = "";
      item.templateData = null;
    });
    canvas.positionConfigs = positionConfigs;
    canvas.hasBg = urlData.hasBg;
    canvas.isSticker = urlData.isSticker;
    canvas.stickerEffectType = urlData.stickerEffectType;
    this.$store.commit("design/set_pageInfo", urlData);
    let templateId = this.$route.query.templateId;
    this.initCanvas();
    //是半定制还是全定制
    let productType = urlData.productType;
    if (productType === 1) {
      //半定制暂时不做处理
      canvas.designDes = "half";
    } else if (productType === 0) {
      canvas.designDes = "quote";
    }
    //判断是否是分享的模板
    if (this.$route.query.id) {
      //如果分享失效加载默认光板模板
      try {
        let layersData = await this.getDesignTemplateShare();
        await canvas.loadTemplate(layersData);
      } catch (e) {
        await this.loadDefaultTemplate(productType);
      }
    } else if (templateId) {
      let layersData = await this.getUserTemplateById(templateId);
      await canvas.loadTemplate(layersData);
    } else {
      await this.loadDefaultTemplate(productType);
    }
    window.addEventListener("beforeunload", canvas.confirmExit);
    window.addEventListener("resize", this.handleResize);
    this.handleResize();
    this.show = true;
    let val = this.$cookies.get("show_dz_yd");
    if (val === undefined && !this.isMb) {
      this.editHelp = true;
    }
    if (!this.isMb) {
      await canvas.showGrid();
    }
  },
  beforeDestroy() {
    this.canvas.off();
    window.removeEventListener("resize", this.handleResize);
    window.removeEventListener("beforeunload", canvas.confirmExit);
  },
};
</script>
<style lang="scss" scoped>

#dzApp {
  position: relative;
  font-family: Calibri, sans-serif;

  ::v-deep {
    .linear-gradient-1 {
      background: repeating-linear-gradient(
        -45deg,
        #f4f5f6,
        #f4f5f6 5px,
        #f8f9fa 0,
        #f8f9fa 40px
      ) !important;
      background-color: #f4f5f6;
    }

    .visibilityHidden {
      visibility: hidden;
    }

    .isYd.visibilityHidden {
      visibility: visible;
    }
  }
}

#dsWrap {
  overflow: auto;
  display: flex;
  flex-direction: column;
  position: relative;
  height: 100vh;
  user-select: none;
  font-size: 16px;

  @include respond-to(mb) {
    font-size: 14px;
    min-height: auto;
  }
}

.ds-content {
  overflow: auto;
  position: relative;
  display: flex;

  // grid-template-columns: 1fr 3fr 344px;

  &.disabledPage {
    pointer-events: none;
  }

  .left-area-wrap {
    height: 100vh;
    box-shadow: 0px 0px 10px 1px rgba(0, 0, 0, 0.16);
    z-index: 1;
  }

  .ds-center-area {
    flex: 1;
    overflow: hidden;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    background-color: #e6e6e6;
    // height: calc(100vh - 140px);
    height: 100vh;
    position: relative;
    //min-width: 950px;
    // padding: 0 2vw 20px;

    .sideWrap {
      width: 4.7vw;
      margin-top: 40px;

      &.isSticker {
        visibility: hidden;
      }
    }

    .ds-right-content {
      flex: 1;
      width: 0;
      max-width: 1000px;
      margin: 0 10px;
      display: flex;
      flex-direction: column;
      justify-content: flex-end;
      align-items: center;
      position: relative;
      height: 100%;
      padding-block: 30px;

      .tool-bar-wrap {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 60px;
        .buttonGroup {
          position: absolute;
          right: 0;
          .btn {
            height: 40px;
            width: 160px;
            color: #fff;
            background-color: #ed21d2;
            border-radius: 4px;
            font-family: Google Sans, Google Sans;
            font-size: 18px;
          }
        }
      }

      & > div {
        width: 100%;
      }

      .fbWrap {
        //width: 700px;
        display: flex;
        flex-direction: column;
        flex: 1;
        height: 0;
        position: relative;
        // background-color: #ffffff;
        padding: 0 10px 65px;
        // margin-top: 40px;
        @include respond-to(mb) {
          margin-top: 0;
        }

        &.isSticker {
          background-color: transparent;
        }

        .des {
          margin: 10px 0;
          font-weight: 700;
          position: absolute;
          top: -39px;
          left: -5vw;

          @include respond-to(mb) {
            position: relative;
            left: auto;
            top: auto;
          }
        }

        #workspace {
          flex: 1;
          height: 0;

          .eraser{
            position: absolute;
            width: 20px;
            height: 20px;
            border: 1px solid red;
            left: 0;
            top: 0;
            &:active{
              opacity: 1;
            }
          }

          .mbTopTool {
            display: none;
          }

          .mbRightTool {
            display: none;
          }

          ::v-deep .el-loading-mask {
            z-index: 1;
          }
        }

        .zoom {
          position: absolute;
          bottom: 10px;
          right: 70px;
          background-color: #5159c4;
          color: #fff;
        }

        .space {
          display: none;
          flex: 1;
        }

        .mb-control-area {
          display: none;
        }
      }
    }

    .right-tool-wrap {
      width: 4.2vw;
      margin-left: 1vw;
    }

    .bottomAreaWrap {
      height: 80px;
    }

    .predict {
      height: 100%;
      width: 100%;
      position: relative;
      .designImgBtn {
        position: absolute;
        width: 100%;
        left: 0;
        top: 0;
        padding-block: 20px;
        display: flex;
        justify-content: center;
        .centerBtn {
          button {
            margin-inline: 5px;
            height: 40px;
            width: 40px;
            border-radius: 4px;
            background-color: #fff;
          }
        }
        .btn {
          position: absolute;
          right: 220px;
          height: 40px;
          width: 160px;
          color: #fff;
          background-color: #ed21d2;
          border-radius: 4px;
          font-family: Google Sans, Google Sans;
          font-size: 18px;
        }
      }
    }
  }

  .ds-right-area {
    width: 344px;
    background-color: #fff;
    box-shadow: 0px 0px 10px 1px rgba(0, 0, 0, 0.16);
    display: flex;
    flex-direction: column;
    z-index: 1;
    .topBar {
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      font-size: 18px;
      height: 50px;
      font-family: Google Sans, Google Sans;
      border-bottom: 1px solid #ccc;
      .title {
        text-transform: uppercase;
        color: #3053e1;
      }
    }
    .area-content {
      font-family: "Google Sans";
      color: #b7b7b7;
      flex: 1;
      padding: 20px 25px;
      overflow-y: scroll;
      margin-right: 4px;
      &.flexLayout {
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .creationList {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 7px 9px;
        .creationItem {
          border-radius: 5px;
          overflow: hidden;
          cursor: pointer;
          border: 3px solid transparent;
          position: relative;
          .oprate-box{
              position: absolute;
              width: 100%;
              height: 100%;
              padding: 10px;
              left: 0;
              top: 0;
              opacity: 0;
              display: flex;
              align-items: flex-end;
              .buttonGroup{
                display: flex;
                width: 100%;
                justify-content: space-between;
                font-size: 18px;
                color: #fff;
              }
          }
          &.active{
            border-color: #ED21D2;
             &::before{
                content: '';
                position: absolute;
                left: 50%;
                top: 50%;
                transform: translateX(-50%) translateY(-50%) rotate(-45deg);
                width: 30px;
                height: 15px;
                border: 4px solid #ED21D2;
                border-right: none;
                border-top: none;
            }
            .oprate-box{
                background-color: rgba(0, 0, 0, .25);
            }
          }
          &:hover{
            border-color: #ED21D2;
            .oprate-box{
              background-color: rgba(0, 0, 0, .25);
              opacity: 1;
            }
          }
        }
      }
    }
    .area-bottom {
      height: 80px;
      background: linear-gradient(90deg, #dee3fa 0%, #fcdbf8 100%);
      display: flex;
      align-items: center;
      justify-content: right;
      padding: 0 25px;
      .quoteBtn {
        font-size: 16px;
        font-family: Google Sans;
        font-weight: 400;
        color: #fff;
        width: 100%;
        letter-spacing: 1px;
        border-radius: 10px;
        max-width: 180px;
        height: 48px;
        line-height: 48px;
        text-align: center;
        background: linear-gradient(90deg, #3053e1 0%, #ed21d2 100%);
      }
    }
  }

  .bottomAreaWrap {
    .bottom-area {
      position: absolute;
      left: 90px;
      right: 0;
      bottom: 0;
      background-color: #ffffff;
      height: 80px;
      box-shadow: 0px -4px 6px rgba(0, 0, 0, 0.15);
      z-index: 2;
      text-align: right;
      display: flex;
      align-items: center;
      .quote-btn {
        width: 180px;
        height: 48px;
        font-family: Google Sans;
        line-height: 48px;
        margin: 0 35px 0 auto;
        text-align: center;
        letter-spacing: 1px;
        color: #fff;
        background: linear-gradient(90deg, #3053e1 0%, #ed21d2 100%);
        border-radius: 10px;
      }
    }
  }

  .default {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  @include respond-to(mb) {
    display: block;

    .tool-bar-wrap {
      display: none !important;
    }

    .left-area-wrap {
      display: none;
    }

    .sideWrap {
      display: none;
    }

    .right-tool-wrap {
      display: none;
    }

    .bottomAreaWrap {
      display: none;
    }

    .ds-center-area {
      min-width: 100%;
      width: 100%;
      height: calc(100vh - 44px);
      padding: 0;

      .ds-right-content {
        width: 100%;
        margin: 0;
        padding: 0;
        .fbWrap {
          width: 100%;
          height: 100%;
          padding: 0;
          background: #ffffff
            url("https://static-oss.gs-souvenir.com/web/quoteManage/20231023/grid_2056nnW8sc.png")
            repeat left top;

          &.isSticker {
            background: #f2f2f2;
          }

          .des {
            margin: 5px 0;
            text-align: center;
            font-size: 12px;
          }

          #workspace {
            flex: 1;
            position: relative;

            .mbTopTool {
              display: flex;
              position: absolute;
              top: 0;
              left: 3.5rem;
            }

            .mbRightTool {
              display: block;
              position: absolute;
              top: 1rem;
              right: 0.5rem;
            }
          }

          .zoom {
            display: none;
          }

          .stickerSize {
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 10px 0;

            .size {
              margin-left: 10px;

              .c {
                margin: 0 10px;
              }

              .sizeSelect {
                display: flex;
                justify-content: center;
                align-items: center;
                width: 24px;
                height: 24px;
                background: #ebebeb;
                border-radius: 50%;
                margin-left: 14px;

                b {
                  font-weight: 700;
                }
              }
            }

            .sizeInput {
              width: 70px;
              height: 40px;
              background: #ffffff;
              border: 1px solid #dbdbdb;
              border-radius: 4px;
              padding: 0 10px;

              &.isError {
                border-color: red;
              }
            }
          }

          .space {
            display: block;
            flex: 1;
          }

          .mb-control-area {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            padding: 10px 0;
            height: 240px;

            .plating-area {
              position: relative;
              height: 80px;
              margin-bottom: 40px;
            }
          }
        }
      }
    }
  }
}
</style>

<style lang="scss">
.el-select-dropdown {
  border-color: #2a96fa;
}

.el-select-dropdown.el-popper .popper__arrow {
  border-bottom-color: #2a96fa;
}

.el-button--primary {
  color: #fff;
  background-color: #2a96fa;
  border-color: #2a96fa;
}

.el-select .el-input.is-focus .el-input__inner {
  border-color: #2a96fa;
}

.el-select-dropdown__item.selected {
  color: #2a96fa;
}

.editColor-colorPicker {
  overflow: hidden;
  display: flex;
  flex-direction: column;
  flex: 1;

  .editColor-colorPicker-title {
  }

  .editColor-colorPicker-selected {
    display: flex;
    align-items: center;
    margin: 12px 0;

    .color-name {
      margin-left: 8px;
    }
  }

  .editColor-colorPicker-colorContainer {
    flex: 1;
    overflow: hidden auto;

    .editColor-colorPicker-colors {
      display: grid;
      grid-template-columns: repeat(auto-fill, 2.875rem);
      grid-column-gap: 10px;
      grid-row-gap: 10px;
    }
  }

  @include respond-to(mb) {
    .editColor-colorPicker-colorContainer {
      .editColor-colorPicker-colors {
        display: grid;
        grid-template-columns: repeat(auto-fill, 34px);
        grid-column-gap: 6px;
        grid-row-gap: 10px;
      }
    }
  }
}

.more-popover {
  min-width: auto;
  user-select: none;

  .more-popover-item {
    margin-bottom: 6px;

    a {
      white-space: nowrap;
    }

    &:last-child {
      margin-bottom: 0;
    }

    b {
      margin-right: 8px;
    }

    &:hover {
      color: #2a96fa;
      text-decoration: underline;
    }
  }
}

.layer-popover {
  padding: 12px 0 0;
  user-select: none;

  .el-popover__title {
    padding: 0 12px;
  }

  .list .list-item {
    display: flex;
    align-items: center;
    height: 30px;
    padding: 0 12px;
    cursor: pointer;

    b {
      margin-right: 4px;
    }

    &:hover {
      background: #f4f5f6;
    }
  }
}

.pen-popover {
  .penWrap {
    .top {
      display: flex;
      align-items: center;

      .el-slider {
        flex: 1;
        margin: 0 20px;
      }

      .el-slider .el-slider__input {
        width: 70px;
      }

      .colorPicker {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-shrink: 0;
        width: 40px;
        height: 40px;
        background: #ffffff;
        border: 1px solid #e6e6e6;
        cursor: pointer;
        border-radius: 4px;

        .color-inner {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 34px;
          height: 34px;
          background: #da9f19;
          border-radius: 4px;

          b {
            font-size: 14px;
            color: #ffffff;
          }
        }
      }
    }

    .btnWrap {
      display: flex;
      justify-content: center;
      margin-top: 10px;

      .startBtn {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        margin-right: 20px;
        cursor: pointer;

        &:hover {
          color: #2a96fa;
        }
      }
    }
  }
}

.eraser-popover {
  .eraser-size {
    display: flex;
    align-items: center;
    .el-slider {
      flex: 1;
      margin: 0 20px;
    }
  }
}

.color-popover {
  width: 220px;

  .colorWrap {
    display: grid;
    grid-template-columns: repeat(6, 25px);
    grid-column-gap: 8px;
    grid-row-gap: 5px;
    overflow: hidden auto;
    max-height: 120px;
    .color-item {
      cursor: pointer;
      .color-ellipse {
        height: 25px;
        border-radius: 4px;
        background-color: #fff;
        border: 1px solid #b7b7b7;
        position: relative;
        &::before {
          content: "";
          position: absolute;
          background: var(--bg);
          width: calc(100% - 4px);
          height: calc(100% - 4px);
          border-radius: 3px;
          left: 2px;
          top: 2px;
        }
        &.active {
          border-width: 2px;
          border-color: #3053e1;
          &::after {
            content: "";
            position: absolute;
            height: 6px;
            width: 10px;
            border-bottom: 2px solid #fff;
            border-left: 2px solid #fff;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -70%) rotate(-45deg);
          }
        }
      }
    }
  }

  @include respond-to(mb) {
    width: 304px;
    padding: 10px;

    .colorWrap {
      grid-template-columns: repeat(auto-fill, 34px);
      grid-column-gap: 7px;
      grid-row-gap: 10px;
      max-height: 122px;
    }
  }
}

.img-color-popover {
  width: 480px;

  .color-item {
    width: 30px !important;
    height: 30px !important;
  }

  .editColor-colorPicker-selected {
    display: grid;
    grid-template-columns: repeat(auto-fill, 30px);
    grid-column-gap: 10px;
    grid-row-gap: 10px;
    max-height: 110px;
    overflow: hidden auto;
  }

  .editColor-colorPicker-colorContainer {
    margin-top: 10px;
  }

  .editColor-colorPicker-colors {
    grid-template-columns: repeat(auto-fill, 30px) !important;
    grid-column-gap: 10px;
    grid-row-gap: 10px;
    max-height: 110px;
    overflow: hidden auto;
  }

  .editImgColor-foot {
    padding: 1rem;
    display: none;
    justify-content: center;
    align-items: center;
  }
}

.text-color-popover {
  .colorWrap {
    display: grid;
    grid-template-columns: repeat(6, 25px);
    grid-column-gap: 10px;
    grid-row-gap: 10px;
    max-height: 110px;
    overflow: hidden auto;

    .color-item {
      cursor: pointer;
      &:nth-last-child(-n + 1) {
        grid-column-start: span 2;
        .color-ellipse {
          height: 25px;
        }
      }
      .color-ellipse {
        height: 25px;
        border-radius: 4px;
        background-color: #fff;
        border: 1px solid #b7b7b7;
        position: relative;
        &::before {
          content: "";
          position: absolute;
          background: var(--bg);
          width: calc(100% - 4px);
          height: calc(100% - 4px);
          border-radius: 3px;
          left: 2px;
          top: 2px;
        }
        &.active {
          border-width: 1px;
          border-color: #3053e1;
          &::after {
            content: "";
            position: absolute;
            height: 8px;
            width: 12px;
            border-bottom: 2px solid #fff;
            border-left: 2px solid #fff;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -70%) rotate(-45deg);
          }
        }
        &:hover{
          border-color: #3053e1;
        }
      }
    }

    @include respond-to(mb) {
      grid-template-columns: repeat(6, 1fr);
    }
  }
}

.el-slider {
  .el-slider__runway {
    .el-slider__bar{
      background-color: #3053E1;
    }
    .el-slider__button-wrapper{
      .el-slider__button{
        border-color: #3053E1;
      }
    }
    &.show-input {
      margin-right: 110px;
    }
  }

  .el-slider__input {
    width: 90px;
    border-radius: 6px;

    input {
      background: #f5f5f5;
    }
  }
}

.dzToolTip.el-tooltip__popper {
  border-color: #ffffff !important;
  background-color: #ffffff;
  box-shadow: 0px 1px 6px 0px rgba(179, 179, 179, 0.64);

  font-size: 16px;
  font-family: Calibri, Arial, serif;

  .popper__arrow {
    border-bottom-color: #ffffff !important;
    border-top-color: #ffffff !important;
  }
}

.stickerSizeToolTip {
  border-color: #ff4242 !important;
  background-color: #ff4242;
  box-shadow: 0px 1px 6px 0px rgba(179, 179, 179, 0.64);
  font-size: 12px;
  padding: 5px;
  color: #ffffff;
  max-width: 120px;
  min-width: auto;
  white-space: nowrap;
  text-align: center;
  font-family: Calibri, Arial, serif;

  .popper__arrow {
    border-bottom-color: #ff4242 !important;
    border-top-color: #ff4242 !important;

    &::after {
      border-top-color: #ff4242 !important;
    }
  }
}

.warningCheckDialog {
    .el-message-box__header {
        border-bottom: 1px solid #B7B7B7;
    }

    .el-message-box__btns {
        text-align: center;

        .el-button {
            border-radius: 20px;

            &.el-button--primary {
                background-color: #3053E1;
            }
        }
    }

    .warningIcon {
        color: #FF4346;
        font-size: 30px;
    }
}

@include respond-to(mb) {
  .el-message-box {
    width: 95%;
  }

  #comm100-container img {
    display: none;
  }
}
</style>


