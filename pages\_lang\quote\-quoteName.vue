<template>
	<div id="custom-cufflinks">
		<Preloader v-if="isLoading"></Preloader>
		<template v-else>
			<article class="content">
				<div class="daohang">
					<span style="color: #d24600"><a href="https://www.gs-jj.com/custom-medals-made-in-usa">All Products</a> </span>&nbsp;<span>> {{ titleName }}</span>
				</div>
				<!-- 报价导航 包含该大类的小分类 -->
				<QuoteNav :pid="pid" :cateList="cateList" title="We provide these Patch types for you."></QuoteNav>
				<div class="header">
					<h1>{{ titleName }}</h1>
				</div>
				<div class="center-content">
					<div class="leftArea" id="leftArea">
						<!-- 左上 <-> 右下 的消失动画 -->
						<transition enter-active-class="animate__animated animate__faster animate__fadeInBottomRight" leave-active-class="animate__animated animate__faster animate__fadeOutBottomRight">
							<SwiperDetail :imgList="carouselList" v-show="showRightArea" :selectedData="selectedData" :generalData="generalData" @toPosition="toPosition" :titleName="titleName" :showProfessional="true" :showViewMore="true" />
						</transition>

						<!-- 包含价格 数量 按钮的详情模块 不需要 -->
						<!-- <Detail class="type2" :key="3" :absoluteImg="comeFromDZ.absoluteImg" :selectedData="selectedData"
							:priceInfo="priceInfo" :customQty="customQty" :textInfo="textInfo" :generalData="generalData"
							@toPosition="toPosition" @addInquiry="debounceAddInquiry" @addCart="addCart"></Detail> -->
					</div>

					<div class="rightArea" id="rightAreaCustom">
						<div v-for="(item, index) in filterShowGeneralData" :key="item.id" class="kk" :class="{ type1: picDialog }">
							<!-- Select Size 尺寸 -->
							<div v-if="item.paramName == 'Medal Size'" class="part Select-Size" :id="item.paramName" :class="{ 'step-active': item.paramName == maskName }">
								<h3 class="step-title">
									<span>{{ item.customIndex }}</span
									>{{ lang.Select }}
									{{ item.alias ? item.alias : item.paramName }}
								</h3>
								<div class="boxContent">
									<MyCheckBox v-for="itemChild in item.childList" :key="itemChild.id" :bindValue="itemChild" :bindName="item.paramName" :titleValue="'paramName'" :imageValue="'imageJson'" :aspectRatio="'aspect-ratio: 315/281'" :likeModel="selectedData" @clickFun="selectQuoteParams(item, itemChild), quotationClick(item, itemChild)"> </MyCheckBox>
								</div>
								<i v-if="item.paramName == maskName" class="el-icon-close" @click="closeMask"></i>
							</div>

							<!-- 金属处理 -->
							<div v-if="item.paramName == 'Plating'" class="part Select-Medal-Finish" :id="item.paramName" :class="{ 'step-active': item.paramName == maskName }">
								<h3 class="step-title hasTips5">
									<span>{{ item.customIndex }} </span> {{ lang.Select }}
									{{ item.alias ? item.alias : item.paramName }}
								</h3>
								<div class="tips">{{ lang.medals.step5Text }}</div>
								<div class="quantity-div" v-show="priceList.length">
									<table>
										<tr>
											<td>{{ langsemiCustom.quantity }}</td>
											<td v-for="(item, index) in slicedPriceList" :key="index" :style="{ backgroundColor: index % 2 === 0 ? '#F9F9F9' : '#fff' }">
												{{ item.quantity }}
											</td>
										</tr>
										<tr>
											<td>{{ langsemiCustom.listPrice }}</td>
											<td v-for="(item, index) in slicedPriceList" :key="index" :style="{ backgroundColor: index % 2 === 0 ? '#F9F9F9' : '#fff' }">
												<CCYRate :price="item.unitPrice"></CCYRate>
											</td>
										</tr>
									</table>
									<table class="tab">
										<tr>
											<td style="width: 105px">{{ langsemiCustom.quantity }}</td>
											<td v-for="(item, index) in slicedPriceList2" :key="index" :style="{ backgroundColor: index % 2 === 0 ? '#F9F9F9' : '#fff' }" style="width: 85px">
												{{ item.quantity }}
											</td>
										</tr>
										<tr>
											<td>{{ langsemiCustom.listPrice }}</td>
											<td v-for="(item, index) in slicedPriceList2" :key="index" :style="{ backgroundColor: index % 2 === 0 ? '#F9F9F9' : '#fff' }">
												<CCYRate :price="item.unitPrice"></CCYRate>
											</td>
										</tr>
									</table>
								</div>
								<div class="boxContent" ref="Select-Medal-Finish">
									<div v-for="itemChild in item.childList" :key="itemChild.id">
										<MyCheckBox :childList="itemChild.childList.length > 0 ? true : false" :tipNum.sync="tipNum" :bindValue="itemChild" :bindName="item.paramName" :titleValue="'alias'" :imageValue="'imageJson'" :aspectRatio="'aspect-ratio: 1200/1200'" :likeModel="selectedData" :sizeValue="sizeValue" @clickFun="selectQuoteParams(item, itemChild)" @selectCurrentParams="selectCurrentParams(item, itemChild)" :showPrice="false" numberInput></MyCheckBox>
									</div>
								</div>
								<div class="text-center">
									<el-button
										class="myBtn"
										@click="showMaskFn(item.paramName)"
										:style="{
											opacity: selectedData[item.paramName].length > 0 ? 1 : 0.5,
										}"
										:disabled="!selectedData[item.paramName].length > 0"
									>
										{{ lang.next }}
									</el-button>
								</div>
								<i v-if="item.paramName == maskName" class="el-icon-close" @click="closeMask"></i>
							</div>

							<!-- Upload Artwork 上传 -->
							<div v-if="item.paramName === 'Upload Artwork & Comments'" class="Upload-Artwork">
								<StepTextUpload1 class="step-item step-upload" :class="{ mask: maskName === item.paramName, hideContent: $route.query.designPic }" :id="item.paramName" :index="item.customIndex" :itemData="item" :uploadList.sync="uploadArtworkList" :remark.sync="remark" :isUpload.sync="isUpload" @closeMask="closeMask" @showMaskFn="showMaskFn" :key="index"> </StepTextUpload1>
							</div>

							<!-- 背面选项 -->
							<div v-if="item.paramName == 'Back Side'" class="part Back-Side-Option" :id="item.paramName" :class="{ 'step-active': item.paramName == maskName }">
								<h3 class="step-title">
									<span>{{ item.customIndex }}</span>
									{{ item.alias ? item.alias : item.paramName }}
								</h3>
								<div class="boxContent">
									<MyCheckBox v-for="itemChild in item.childList" :key="itemChild.id" :bindValue="itemChild" :bindName="item.paramName" :titleValue="'alias'" :imageValue="'imageJson'" :aspectRatio="'aspect-ratio: 315/204'" :likeModel="selectedData" :sizeValue="sizeValue" @clickFun="selectQuoteParams(item, itemChild)" showPrice></MyCheckBox>
								</div>
								<div class="text-center">
									<el-button
										class="myBtn"
										@click="showMaskFn(item.paramName)"
										:style="{
											opacity: selectedData[item.paramName].length > 0 ? 1 : 0.5,
										}"
										:disabled="!selectedData[item.paramName].length > 0"
									>
										{{ lang.next }}
									</el-button>
								</div>
								<i v-if="item.paramName == maskName" class="el-icon-close" @click="closeMask"></i>
							</div>

							<!-- 织带 -->
							<div v-if="item.paramName == 'Select Ribbon'" class="part Select-Ribbon" :id="item.paramName" :class="{ 'step-active': item.paramName == maskName }">
								<h3 class="step-title">
									<span>{{ item.customIndex }}</span>
									{{ item.alias ? item.alias : item.paramName }}
								</h3>
								<div class="boxContent">
									<MyCheckBox v-for="itemChild in item.childList" :key="itemChild.id" :bindValue="itemChild" :bindName="item.paramName" :titleValue="'paramName'" :imageValue="'imageJson'" :aspectRatio="'aspect-ratio: 205/162'" :likeModel="selectedData" @clickFun="selectQuoteParams(item, itemChild)"> </MyCheckBox>
								</div>
								<i v-if="item.paramName == maskName" class="el-icon-close" @click="closeMask"></i>
							</div>
							<!-- 包装 -->
							<div v-if="item.paramName == 'Select Packaging Options'" class="part Select-Package" :id="item.paramName" :class="{ 'step-active': item.paramName == maskName }">
								<h3 class="step-title">
									<span>{{ item.customIndex }}</span>
									{{ item.alias ? item.alias : item.paramName }}
								</h3>
								<div class="boxContent">
									<MyCheckBox v-for="itemChild in item.childList" :key="itemChild.id" :bindValue="itemChild" :bindName="item.paramName" :titleValue="'paramName'" :imageValue="'imageJson'" :aspectRatio="'aspect-ratio: 316/204'" :likeModel="selectedData" @clickFun="selectQuoteParams(item, itemChild)"> </MyCheckBox>
								</div>
								<i v-if="item.paramName == maskName" class="el-icon-close" @click="closeMask"></i>
							</div>

							<!-- Turnaround Time 交期折扣 & 备注 -->
							<template v-if="item.paramName == 'Turnaround Time'">
								<div class="part Delivery-Date" :class="{ 'step-active': maskName === item.paramName }" :id="item.paramName">
									<h3 class="step-title">
										<span>{{ item.customIndex }}</span> {{ lang.Select }} {{ item.alias ? item.alias : item.paramName }}
									</h3>
									<div style="display: flex">
										<StepTime class="step-item step-date" :index="item.customIndex" :itemData="item" :priceInfo="priceInfo" :previewMode="previewMode" :selectedParams="selectedData" @showMaskFn="showMaskFn" @selectItem="selectQuoteParams($event.item, $event.citem)" @closeMask="closeMask" :key="index"></StepTime>
									</div>
									<i v-if="item.paramName == maskName" class="el-icon-close" @click="closeMask"></i>
								</div>
							</template>
						</div>
					</div>
				</div>
			</article>
			<div class="footer" id="foot">
				<Detail class="type2" :key="3" :absoluteImg="comeFromDZ.absoluteImg" :selectedData="selectedData" :priceInfo="priceInfo" :customQty="customQty" :textInfo="textInfo" :generalData="generalData" @toPosition="toPosition" @addInquiry="debounceAddInquiry" @addCart="addCart" :showTextTip="false"> </Detail>
			</div>
			<el-drawer :visible.sync="showArea" :with-header="true" custom-class="drawDialog" :size="device === 'ipad' ? '728px' : '85%'">
				<Detail class="type3" :key="2" :absoluteImg="comeFromDZ.absoluteImg" :selectedData="selectedData" :priceInfo="priceInfo" :customQty="customQty" :textInfo="textInfo" :generalData="generalData" @toPosition="toPosition" @addInquiry="debounceAddInquiry" @addCart="addCart"></Detail>
			</el-drawer>
			<!--    遮罩-->
			<myMask :maskName.sync="maskName"></myMask>
			<!-- 建议弹窗-->
			<RecomendDialog :commentsDialog.sync="commentsDialog" :paramValue="selectedParamsValue" @next="recomendNext" :showUpload="false"> </RecomendDialog>
			<!--  手机，ipad端预览-->
			<PreviewBtn :showArea.sync="showArea"></PreviewBtn>
			<BaseDialog v-model="showOtoDialog" :width="device != 'mb' ? '570px' : '90%'">
				<div class="otoWrap">
					<img src="@/assets/images/oto.png" alt="" />
					<p>{{ lang.p1 }}</p>
					<h3>{{ lang.p2 }}</h3>
					<p style="color: #666666">
						{{ lang.p3 }}<br />
						{{ lang.p4 }}.
					</p>

					<div class="box">
						<p class="t1">
							{{ lang.p5 }}<br />
							{{ lang.p6 }}!
						</p>
						<a href="https://www.o2o.co/previewWeb" style="text-decoration: none">
							<el-button type="primary">{{ lang.p7 }}</el-button>
						</a>
					</div>
				</div>
			</BaseDialog>
			<VideoPreviewDialog :pic-dialog.sync="picDialog" :zoom-pic="zoomPic" :temp-type="tempType" :selected-params-value="selectedParamsValue" @qtyAndBtnConfirm="qtyAndBtnConfirm" @qtyAndBtnNext="qtyAndBtnNext" @dialogNextStep="dialogNextStep"></VideoPreviewDialog>
			<BaseDialog v-model="upgradeDialog" :width="device != 'mb' ? '640px' : '320px'">
				<Upgrade @closeUpgrade="upgradeDialog = false" :tipArr="tipArr" :typeName="cateData.cateName" unit="pairs" @upgradeLevel="upgradeLevel"></Upgrade>
			</BaseDialog>
			<infoDialog :infoDialogVisible.sync="infoDialogVisible" @getValue="getValueFun" :otherUpload="uploadArtworkList" :recomendUpload="recomendUpload" :uploadList.sync="uploadList"></infoDialog>
			<BaseDialog v-model="noFileDialog" :width="device != 'mb' ? '485px' : '90%'" :model="false">
				<template #closeIcon>
					<div style="display: none"></div>
				</template>
				<infoUpload :infoUploadList="infoUploadList" @pushInfoList="pushInfoList" @delInfoList="delInfoList" @updateInquiry="updateInquiry" @closeInfoDialog="closeInfoDialog"> </infoUpload>
			</BaseDialog>
		</template>
	</div>
</template>
<script>
import "@/plugins/element";
import MyCheckBox from "@/components/Medals/MyCheckBox";
import MyCheckBox3 from "@/components/Medals/MyCheckBox3";
import BaseDialog from "@/components/Quote/BaseDialog";
import VideoPlayer from "@/components/Quote/VideoPlayer.vue";
import Upgrade from "@/components/Quote/Upgrade";
import DialogBM from "@/components/Medals/DialogBM";
import QtyAndBtn from "@/components/Medals/QtyAndBtn";
import Corner from "@/components/Medals/Corner";
import infoDialog from "@/components/Medals/infoDialog";
import QuoteNav from "@/components/Cufflinks/QuoteNav";
import QuoteBtn from "@/components/Quote/QuoteBtn";
import quoteMixin from "@/mixins/quote";
import quoteBanChoiceMixins from "@/mixins/quoteBanChoice";
import StepTime from "@/components/Quote/StepTime.vue";
import StepTextUpload1 from "@/components/Cufflinks/StepTextUpload1.vue";
import StepQty from "@/components/Cufflinks/StepQty.vue";
import RecomendDialog from "@/components/Quote/RecomendDialog.vue";
import myMask from "@/components/Quote/Mask.vue";
import Detail from "@/components/Quote/Detail.vue";
import SwiperDetail from "@/components/Cufflinks/SwiperDetail.vue";
import PreviewBtn from "@/components/Quote/PreviewBtn.vue";
import Preloader from "@/components/Quote/Preloader.vue";

import { isImageType } from "@/utils/utils";
import { medalsApi } from "@/api/medals/medals";
import { indexApi } from "@/api/lanyardQuote/index";
import VideoPreviewDialog from "@/components/Quote/VideoPreviewDialog.vue";

export default {
	head() {
		return {
			title: "Quote page",
		};
	},
	name: "cufflinks",
	components: {
		VideoPreviewDialog,
		Preloader,
		PreviewBtn,
		Detail,
		SwiperDetail,
		myMask,
		RecomendDialog,
		StepQty,
		StepTime,
		MyCheckBox,
		MyCheckBox3,
		BaseDialog,
		VideoPlayer,
		DialogBM,
		QtyAndBtn,
		Corner,
		infoDialog,
		Upgrade,
		QuoteNav,
		QuoteBtn,
		StepTextUpload1,
	},
	mixins: [quoteMixin, quoteBanChoiceMixins],
	data() {
		return {
			tipNum: 1,
			pid: 261,
			productsName: "custom embroidered patches",
			restaurants: [
				{ value: "10", address: "10" },
				{ value: "25", address: "25" },
				{ value: "50", address: "50" },
				{ value: "100", address: "100" },
				{ value: "200", address: "200" },
				{ value: "300", address: "300" },
				{ value: "500", address: "500" },
				{ value: "1000", address: "1000" },
				{ value: "3000", address: "3000" },
				{ value: "5000", address: "5000" },
				{ value: "10000", address: "10000" },
			],

			imgList: [
				{
					imgUrl: "https://oss-static-cn.liyi.co/web/quoteManage/20240310/Engraved_Cuffinks_20240310jaMsN7.jpg",
					url: "https://oss-static-cn.liyi.co/web/quoteManage/20240310/Engraved_Cuffinks_20240310jaMsN7.jpg",
					alt: "Engraved_Cuffinks",
				},
				{
					imgUrl: "https://oss-static-cn.liyi.co/web/quoteManage/20240310/Select_Attachment-Toggle_Closure_20240310nYaKJs.jpg",
					url: "https://oss-static-cn.liyi.co/web/quoteManage/20240310/Select_Attachment-Toggle_Closure_20240310nYaKJs.jpg",
					alt: "Select_Attachment-Toggle_Closure",
				},
				{
					imgUrl: "https://oss-static-cn.liyi.co/web/quoteManage/20240310/More_Options-Transparent_Color_20240310CitzZ7.jpg",
					url: "https://oss-static-cn.liyi.co/web/quoteManage/20240310/More_Options-Transparent_Color_20240310CitzZ7.jpg",
					alt: "More_Options-Transparent_Color",
				},
				{
					imgUrl: "https://oss-static-cn.liyi.co/web/quoteManage/20240310/Picture_Cuffinks_20240310bayAN4.jpg",
					url: "https://oss-static-cn.liyi.co/web/quoteManage/20240310/Picture_Cuffinks_20240310bayAN4.jpg",
					alt: "Picture_Cuffinks",
				},
			],
			cateList: [], //推荐分类数据
			showProfessional: false,
			uploadArea: true,
			priceList: [],
			qlist: [],
		};
	},
	computed: {
		//价格表--截取长度的前面的一半展示
		slicedPriceList() {
			// const halfLength = Math.ceil(this.priceList.length / 2);
			return this.priceList.slice(0, 6);
		},
		//价格表--截取长度的后面的一半展示
		slicedPriceList2() {
			// const halfLength = Math.ceil(this.priceList.length / 2);
			return this.priceList.slice(6, 12);
		},
		langsemiCustom() {
			return this.$store.getters.lang.semiCustom || {};
		},
	},
	watch: {
		routingId: {
			handler() {
				indexApi
					.quotationList({
						proId: this.proId,
						cateId: this.routingId,
						projectName: this.projectName,
					})
					.then((res) => {
						this.qlist = res.data;
						//默认选择2.5 Inches尺寸
						let find = this.qlist.find((item) => item.size === "2.5 Inches");
						if (find) {
							this.priceList = JSON.parse(find.priceJson);
						}
					});
			},
		},
	},
	methods: {
		isImageType,
		getAppRecommendCateList() {
			medalsApi.getAppRecommendCateList({ proId: this.proId, id: this.pid }).then((res) => {
				this.cateList = res.data;
				this.titleName = this.cateList[0]?.cateName;
			});
		},
		quotationClick(item, itemChild) {
			let find = this.qlist.find((r) => r.size === itemChild.paramName);
			if (find) {
				this.priceList = JSON.parse(find.priceJson);
			}
		},
	},
	created() {
		this.getAppRecommendCateList();
	},
};
</script>
<style scoped lang="scss">
#custom-cufflinks {
	color: #333;
	font-family: Calibri;

	::v-deep .el-input__inner:focus {
		border-color: $color-primary;
	}

	::v-deep video {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}

	::v-deep ul {
		margin-left: 0;
	}

	::v-deep .video-js,
	::v-deep .vjs-poster {
		background-color: white !important;
	}

	.viewMore {
		@media screen and (min-width: 768px) {
			display: none;
		}
	}

	::v-deep .StepBox {
		border-radius: 10px;
		overflow: hidden;
		border: 2px solid #ebebeb;
		transition: all 0.2s;

		@media screen and (max-width: 767px) {
			padding-bottom: 0 !important;
			border-width: 1px;
			border-radius: 6px;
		}

		&:hover {
			border-color: $color-primary !important;
		}

		&.active {
			border-color: $color-primary !important;

			.title {
				color: #333 !important;
			}
		}

		.se {
			align-items: start;

			.product-info {
				padding: 11px 0;
				margin-top: 0;
				align-items: start;

				@media screen and (max-width: 767px) {
					padding: 8px 0;
				}

				.radio-beauty {
					min-width: 20px;
					height: 20px;
					border-color: #cccccc;
					margin-left: 20px;
				}

				.radio-beauty::after {
					width: 10px;
					height: 10px;
					background-color: #fff;
				}

				@media screen and (max-width: 767px) {
					.radio-beauty {
						min-width: 12px;
						width: 12px;
						height: 12px;
						border-color: #cccccc;
						margin: 0 5px 0 12px;
					}

					.radio-beauty::after {
						width: 6px;
						height: 6px;
						background-color: #fff;
					}
				}
			}
		}

		@media (any-hover: hover) {
			&:hover {
				.zoomIcon {
					color: $color-primary;
				}

				.product-info {
					.radio-beauty {
						background-color: $color-primary;
						border-color: $color-primary;

						&::after {
							background-color: white;
						}
					}

					.title {
						// color: $color-primary;
					}
				}
			}
		}
	}

	.picWrap {
		display: flex;
		justify-content: center;
		align-items: center;
		flex-direction: column;
		width: 100%;
		padding: 0px !important;
	}

	.text-center {
		text-align: center;
	}

	.myBtn {
		background: $color-primary;
		border-radius: 10px;
		font-size: 18px;
		font-family: Calibri;
		font-weight: 400;
		color: #ffffff;
		padding: 10px 65px;
		margin-top: 30px;

		@media screen and (max-width: 767px) {
			width: 125px;
			height: 30px;
			background: $color-primary;
			border-radius: 5px;
			font-size: 12px;
			font-family: Arial;
			font-weight: 400;
			color: #ffffff;
			margin-top: 9.5px;
			padding: 0;
		}
	}

	.header {
		h1 {
			font-size: 30px;
			font-family: Calibri;
			font-weight: bold;
			color: #333;
			margin-bottom: 9px;
		}
	}

	::v-deep .content {
		width: 1200px;
		margin: 0 auto;
		padding-bottom: 20px;

		.daohang {
			margin-top: 11px;

			@media screen and (max-width: 767px) {
				margin-top: 0;
				padding-top: 15px;
			}
		}

		.cufflink-quote {
			padding: 24px 0;
		}

		.center-content {
			display: flex;
			justify-content: space-between;
		}

		.leftArea {
			width: 540px;
			margin-top: 20px;
			padding-bottom: 20px;

			.mask {
				z-index: 101;
				background-color: #fff;
			}

			@media screen and (max-width: 767px) {
				width: 100%;
			}
		}

		.rightArea {
			width: 620px;

			@media screen and (max-width: 767px) {
				width: 100%;
			}

			.Upload-Artwork {
				margin-bottom: 20px;
			}

			.Select-Size {
				.boxContent {
					grid-template-columns: repeat(2, 1fr) !important;
					column-gap: 20px;
					row-gap: 20px;

					@media screen and (max-width: 767px) {
						grid-template-columns: repeat(2, 1fr) !important;
					}
				}
			}

			.Back-Side-Option {
				.boxContent {
					grid-template-columns: repeat(2, 1fr) !important;
					column-gap: 20px;
					row-gap: 20px;
				}

				.se .product-info {
					padding: 11px 0 3px;
				}

				.PriceText {
					margin-left: 52px;
					margin-bottom: 10px;
				}
			}

			.Select-Package {
				.boxContent {
					grid-template-columns: repeat(2, 1fr) !important;
					column-gap: 20px;
					row-gap: 20px;
				}
			}

			.Select-Medal-Finish {
				.quantity-div {
					table {
						width: 100%;
						border-collapse: collapse;
						border-spacing: 0;
						text-align: center;
						margin-bottom: 10px;

						td {
							border: 1px solid #cccccc;
							padding: 5px 0;

							@media screen and (max-width: 767px) {
								padding: 1vw 0;
							}
						}
					}
					.tab {
						width: 89%;
					}
				}

				.boxContent {
					grid-template-columns: repeat(3, 1fr);
					column-gap: 20px;
					row-gap: 20px;

					@media screen and (max-width: 767px) {
						max-height: 475px;
						overflow: hidden;
					}

					.number-input {
						margin: 0 0 8px 0;
					}

					> div {
						overflow: hidden;
					}

					::v-deep .StepBox {
						padding-bottom: 0;
						border-radius: 10px;

						&.active {
							.el-image {
								border-color: $color-primary !important;
							}
						}

						.se {
							.el-image {
								border: 1px solid transparent;
								transition: all 0.2s;
								border-radius: 10px;

								@media (any-hover: hover) {
									&:hover {
										border-color: $color-primary !important;
										box-shadow: 0 3px 4px 0 #ccc;
									}
								}
							}
						}

						.product-price {
							margin-bottom: 5px;
						}
					}

					@media screen and (max-width: 1800px) {
						grid-template-columns: repeat(5, 1fr);
					}

					@media screen and (max-width: 1678px) {
						grid-template-columns: repeat(4, 1fr);
					}

					@media screen and (max-width: 767px) {
						grid-template-columns: repeat(2, 1fr);
						column-gap: 5.5px;
						row-gap: 5px;

						.StepBox {
							background: #f4f5f5;
							border-radius: 5px;
							display: flex;
							align-items: center;
							padding-bottom: 10px;
							height: 100%;

							.se {
								.product-info {
									justify-content: flex-start;
									margin-top: 0;
								}
							}

							::v-deep .product-price {
								margin-bottom: 5px;
							}
						}
					}
				}

				@media screen and (max-width: 767px) {
					.StepBox .number-input {
						width: 100px !important;
					}

					.el-input__inner {
						font-size: 13px !important;
					}
				}
			}

			.step-title {
				margin-bottom: 19px;
				font-size: 18px;
				font-weight: bold;
				background: #f9f9f9;
				line-height: 36px;

				@media screen and (max-width: 767px) {
					margin-bottom: 10px;
					font-size: 18px;
					line-height: 32px;
				}

				span {
					display: inline-block;
					width: 36px;
					height: 36px;
					margin-right: 12px;
					background: #333;
					border-radius: 4px;
					font-size: 24px;
					font-weight: 400;
					color: #fff;
					text-align: center;
				}
			}

			.tips {
				margin-bottom: 10px;

				@media screen and (max-width: 767px) {
					margin-bottom: 8.5px;
				}
			}

			.kk {
				.part {
					position: relative;
					z-index: 0;
					padding: 20px;
					background: #ffffff;

					@media screen and (max-width: 767px) {
						padding: 20px 7px;
						border-radius: 5px;
					}

					.boxContent {
						display: grid;
						justify-content: space-between;
						grid-template-columns: repeat(3, 1fr);
						column-gap: 13px;
						row-gap: 13px;

						@media screen and (max-width: 767px) {
							column-gap: 5px;
							row-gap: 5px;
						}
					}

					&.Select-Shapes {
						.boxContent {
							grid-template-columns: repeat(5, 1fr);

							@media screen and (max-width: 767px) {
								grid-template-columns: repeat(3, 1fr);
							}

							.StepBox {
								border-color: transparent;
								position: relative;
								border-radius: 8px;

								@media screen and (max-width: 767px) {
									border-radius: 5px;
									border: 1px solid #ebebeb;
								}

								.se {
									.product-info {
										display: none !important;
									}
								}

								// @media screen and (max-width: 767px) {
								// }

								// @media (any-hover: hover) {
								// 	&:hover {
								// 		border-color: $color-primary !important;
								// 	}
								// }
							}

							.StepBox.active ::after {
								content: "\e7b8";
								font-family: "modalicon";
								position: absolute;
								color: #fff;
								top: 0;
								right: 0;
								width: 36px;
								height: 20px;
								font-size: 12px;
								text-align: center;
								line-height: 20px;
								background: $color-primary;
								border-radius: 0px 6px 0px 10px;

								@media screen and (max-width: 767px) {
									width: 22px;
									height: 14px;
									line-height: 14px;
									border-radius: 0px 4px 0px 6px;
								}
							}
						}
					}

					&.Select-Plating {
						.number-input {
							display: none;
						}

						@media screen and (max-width: 767px) {
							.product-info {
								align-items: start;

								.title {
									min-height: 28px;
								}
							}
						}
					}

					&.Select-Engraving {
						.boxContent {
							grid-template-columns: repeat(2, 1fr);
						}
					}

					&.More-Options {
						.boxContent {
							@media screen and (max-width: 767px) {
								grid-template-columns: repeat(2, 1fr);
								row-gap: 5px;
							}

							.StepBox {
								padding-bottom: 11px;

								.product-info {
									padding-bottom: 0;
								}

								.PriceText {
									margin-left: 49px;
									color: $color-primary;

									.normal-text {
										font-size: 14px;
									}

									@media screen and (max-width: 767px) {
										margin-left: 29px;
										margin-bottom: 8px;
									}
								}

								.tip-text {
									color: $color-primary;
									font-size: 14px;
								}
							}
						}
					}

					&.packaging-options {
						.boxContent {
							grid-template-columns: repeat(2, 1fr);

							.StepBox {
								padding-bottom: 11px;

								.product-info {
									padding-bottom: 0;
								}

								.PriceText {
									margin-left: 49px;
									color: $color-primary;

									.normal-text {
										font-size: 14px;
									}

									.tip-text {
										color: $color-primary;
										font-size: 14px;
									}

									@media screen and (max-width: 767px) {
										margin-left: 29px;
										margin-bottom: 8px;
									}
								}
							}
						}
					}

					&.Select-Quantity {
						margin-bottom: 0;
					}

					&.Delivery-Date {
						> div {
							display: flex;
						}

						span.text {
							margin: 0 12px 0 20px;
							color: #9e9e9e;
						}

						.step-item {
							margin-bottom: 12px;

							.item-wrap {
								min-height: auto;
							}
						}

						.step-date {
							padding: 0;

							.step-title {
								display: none;
							}

							.step-item {
								margin-bottom: 12px;
							}

							.step-box {
								margin-left: 38px;
								// width: 474px;
								grid-template-columns: repeat(3, 1fr);
								column-gap: 14px;

								.top {
									.customCircle1 {
										width: 20px;
										height: 20px;

										&::after {
											width: 10px;
											height: 10px;
											background: #fff;
										}
									}
								}

								.bottom {
									margin-top: 0;
									font-size: 14px;
									color: #9e9e9e;
									margin-left: 30px;
								}
							}
						}

						.el-textarea {
							width: 474px;

							.el-textarea__inner:focus {
								border-color: $color-primary;
							}

							.el-textarea__inner {
								background-color: #f9f9f9;
								height: 130px !important;
								border-radius: 10px;
							}
						}

						@media screen and (max-width: 767px) {
							> div {
								flex-direction: column;
							}

							span.text {
								font-size: 14px;
								margin: 0 0 7px;
							}

							.step-date {
								.step-box {
									width: auto;
									margin-left: 0;
									grid-template-columns: 1fr;
									row-gap: 6px;
									margin-bottom: 3px;
								}

								.price-box {
									display: none;
								}

								.step-item {
									margin-bottom: 8px;
								}
							}

							.comments {
								display: block;

								.el-textarea {
									margin-top: 7px;
									width: 100%;

									.el-textarea__inner {
										background-color: #f9f9f9;
										height: 120px !important;
										border-radius: 5px;
									}
								}
							}
						}
					}

					&.step-active {
						position: relative;
						z-index: 100;
						border-radius: 10px;
					}
				}

				&:not(:last-child) {
					.part {
						// margin-bottom: 39px;

						@media screen and (max-width: 767px) {
							margin-bottom: 10px;
						}
					}
				}
			}

			.kk.type1 {
				.part {
					position: static;
				}
			}
		}
	}

	::v-deep .footer {
		display: grid;
		grid-template-columns: 700px;
		justify-content: center;
		padding: 20px;
		background: #eef2f5;

		.footBtn .sub {
			flex-direction: row;
			// justify-content: space-between;
			justify-content: center;
			width: 95%;
			align-items: center;
		}

		.topImg {
			display: none;
		}

		.con {
			.viewMore {
				display: none !important;
			}

			.scrollBar {
				overflow: visible;
				max-height: fit-content;
			}
		}

		@media screen and (max-width: 767px) {
			grid-template-columns: 1fr;
			padding: 10px;
			background-color: #e0e0e0;

			.con {
				.viewMore {
					display: block !important;
				}

				.scrollBar {
					overflow: hidden;
					max-height: 185px;
				}
			}
		}
	}

	.el-icon-close {
		display: flex;
		justify-content: center;
		align-items: center;
		font-weight: 700;
		position: absolute;
		top: 0;
		right: 0;
		transform: translate(50%, -50%);
		width: 40px;
		height: 40px;
		cursor: pointer;
		background: #ffffff;
		box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.3);
		border-radius: 50%;
		z-index: 10;

		@media screen and (max-width: 800px) {
			transform: translate(0, 0);
			box-shadow: none;
		}
	}

	.custom-shadow {
		position: relative;
		background: #fff;
		border: 1px solid #d9d9d9;
		border-radius: 10px;
		padding: 27px 17px;

		&::before {
			content: "";
			position: absolute;
			z-index: -1;
			bottom: 12px;
			left: 5px;
			width: 50%;
			height: 20%;
			box-shadow: 0 14px 7px #d9dbdd;
			transform: rotate(-3deg);
		}

		&::after {
			content: "";
			position: absolute;
			z-index: -1;
			bottom: 12px;
			width: 50%;
			height: 20%;
			box-shadow: 0 14px 7px #d9dbdd;
			right: 5px;
			left: auto;
			transform: rotate(3deg);
		}
	}
}

@media screen and (max-width: 767px) {
	#custom-cufflinks {
		background-color: #f2f5f7;
		font-size: 12px;

		.header {
			display: none;
		}

		.cufflink-quote {
			padding: 0 !important;
		}

		.content {
			width: 100%;
			padding: 0 15px 30px;

			.center-content {
				display: block;
				width: 100%;
			}
		}
	}
}
</style>