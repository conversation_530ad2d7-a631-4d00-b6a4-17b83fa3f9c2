<template>
  <div class="WristbandDetails"
    :class="{ footer: footer, noDetails: noDetails }"
    :style="{ top: cusTop + 'px' }"
    id="detailInfo">
    <div class="topImage">
      <div class="imgWrap">
        <slot></slot>
      </div>
    </div>
    <div class="con">
      <div>
        <p class="title">{{ lang.orderSummary }}</p>
      </div>
      <ul ref="detailList"
        class="scrollBar custom-scrollbar"
        :class="{ showMore: showMore }"
        :style="{ 'overflow-y': showMore ? 'hidden' : 'auto' }">
        <template v-for="(val, key, i) in selectedData">
          <li class="detail-item"
            :key="key"
            @click="jump(key)">
            <div class="left">
              <div class="f-left">{{ filter ? getName(key) : key }}<span style="color: red">*</span>:</div>
              <div class="f-right">
                <template v-if="val">
                  <template v-if="key == 'Wristband Style'">
                    <span v-for="(item, index) in val"
                      :key="item.id">
                      {{ item.cateNameQuote ? item.cateNameQuote : item.cateName }}
                      {{ index + 1 == val.length ? null : ", " }}
                    </span>
                  </template>
                  <template v-else-if="key == 'Band Color & Quantity'">
                    <p v-for="(item, index) in val"
                      :key="index"> {{ (item.typeAlias ? (item.typeAlias + '：'):'') +  getAlias(item) }}{{ getNums(item) }}{{ index + 1 == val.length ? null : ", " }}
                    </p>
                  </template>
                  <template v-else>
                    <span v-for="(item, index) in val"
                      :key="index"> {{ getAlias(item) }}{{ index + 1 == val.length ? null : ", " }} </span>
                  </template>
                </template>
              </div>
            </div>
            <span class="editIcon"
              v-show="key != 'Wristband Style'">{{ lang.Edit }}<b class="icon-edit--fill"></b></span>
          </li>
        </template>

        <!-- 文字 -->
        <div class="detail-item"
          v-show="copyCanvasData.messageStyle != 'around'"
          @click="jump('Design Your Band')">
          <div class="left">
            <div class="f-left">{{ lang.wristband.frontMessage }}:</div>
            <div class="f-right">{{ copyCanvasData.frontTextGroup[0].text }}</div>
          </div>
          <span class="editIcon">{{ lang.Edit }}<b class="icon-edit--fill"></b></span>
        </div>
        <div class="detail-item"
          v-show="copyCanvasData.messageStyle == 'frontback'"
          @click="jump('Design Your Band')">
          <div class="left">
            <div class="f-left">{{ lang.wristband.backMessage }}:</div>
            <div class="f-right">{{ copyCanvasData.backTextGroup[0].text }}</div>
          </div>
          <span class="editIcon">{{ lang.Edit }}<b class="icon-edit--fill"></b></span>
        </div>
        <div class="detail-item"
          v-show="copyCanvasData.messageStyle == 'around'"
          @click="jump('Design Your Band')">
          <div class="left">
            <div class="f-left">{{ lang.wristband.aroundMessage }}:</div>
            <div class="f-right">{{ copyCanvasData.aroundTextGroup[0].text }}</div>
          </div>
          <span class="editIcon">{{ lang.Edit }}<b class="icon-edit--fill"></b></span>
        </div>
        <div class="detail-item"
          v-if="copyCanvasData?.insideTextGroup && copyCanvasData.insideTextGroup.length"
          @click="jump('Design Your Band')">
          <div class="left">
            <div class="f-left">{{ lang.wristband.insideMessage }}:</div>
            <div class="f-right">{{ copyCanvasData?.insideTextGroup[0].text }}</div>
          </div>
          <span class="editIcon">{{ lang.Edit }}<b class="icon-edit--fill"></b></span>
        </div>

        <!-- 颜色 -->
        <div class="detail-item"
          v-show="copyCanvasData.messageStyle != 'around'"
          @click="jump('Design Your Band')">
          <div class="left">
            <div class="f-left">{{ 'Front Message Color' }}:</div>
            <div class="f-right">
              <div style="width:1em; height:1em"
                :style="{ backgroundColor: copyCanvasData.frontTextGroup[0].color }">
              </div>
            </div>
          </div>
          <span class="editIcon">{{ lang.Edit }}<b class="icon-edit--fill"></b></span>
        </div>
        <div class="detail-item"
          v-show="copyCanvasData.messageStyle == 'frontback'"
          @click="jump('Design Your Band')">
          <div class="left">
            <div class="f-left">{{ 'Back Message Color' }}:</div>
            <div class="f-right">
              <div style="width:1em; height:1em"
                :style="{ backgroundColor: copyCanvasData.backTextGroup[0].color }">
              </div>
            </div>
          </div>
          <span class="editIcon">{{ lang.Edit }}<b class="icon-edit--fill"></b></span>
        </div>
        <div class="detail-item"
          v-show="copyCanvasData.messageStyle == 'around'"
          @click="jump('Design Your Band')">
          <div class="left">
            <div class="f-left">{{ 'Wrap Around Message Color' }}:</div>
            <div class="f-right">
              <div style="width:1em; height:1em"
                :style="{ backgroundColor: copyCanvasData.aroundTextGroup[0].color }">
              </div>
            </div>
          </div>
          <span class="editIcon">{{ lang.Edit }}<b class="icon-edit--fill"></b></span>
        </div>
        <div class="detail-item"
          v-if="copyCanvasData?.insideTextGroup.length"
          @click="jump('Design Your Band')">
          <div class="left">
            <div class="f-left">{{ 'Inside Message Color' }}:</div>
            <div class="f-right">
              <div style="width:1em; height:1em"
                :style="{ backgroundColor: copyCanvasData.insideTextGroup[0].color }">
              </div>
            </div>
          </div>
          <span class="editIcon">{{ lang.Edit }}<b class="icon-edit--fill"></b></span>
        </div>

        <!-- 字体 -->
        <div class="detail-item"
          v-show="copyCanvasData.messageStyle != 'around'"
          @click="jump('Design Your Band')">
          <div class="left">
            <div class="f-left">{{ 'Front Message Font' }}:</div>
            <div class="f-right">{{ copyCanvasData.frontTextGroup[0].family }}</div>
          </div>
          <span class="editIcon">{{ lang.Edit }}<b class="icon-edit--fill"></b></span>
        </div>
        <div class="detail-item"
          v-show="copyCanvasData.messageStyle == 'frontback'"
          @click="jump('Design Your Band')">
          <div class="left">
            <div class="f-left">{{ 'Back Message Font' }}:</div>
            <div class="f-right">{{ copyCanvasData.backTextGroup[0].family }}</div>
          </div>
          <span class="editIcon">{{ lang.Edit }}<b class="icon-edit--fill"></b></span>
        </div>
        <div class="detail-item"
          v-show="copyCanvasData.messageStyle == 'around'"
          @click="jump('Design Your Band')">
          <div class="left">
            <div class="f-left">{{ 'Wrap Around Message Font' }}:</div>
            <div class="f-right">{{ copyCanvasData.aroundTextGroup[0].family }}</div>
          </div>
          <span class="editIcon">{{ lang.Edit }}<b class="icon-edit--fill"></b></span>
        </div>
        <div class="detail-item"
          v-if="copyCanvasData?.insideTextGroup.length"
          @click="jump('Design Your Band')">
          <div class="left">
            <div class="f-left">{{ 'Inside Message Font' }}:</div>
            <div class="f-right">{{ copyCanvasData?.insideTextGroup[0].family }}</div>
          </div>
          <span class="editIcon">{{ lang.Edit }}<b class="icon-edit--fill"></b></span>
        </div>

        <!-- 前后图标 -->
        <div class="detail-item"
          v-if="copyCanvasData?.frontStartImg"
          @click="jump('Design Your Band')">
          <div class="left">
            <div class="f-left">{{ lang.wristband.frontStartClipart }}:</div>
            <div class="f-right">
              <img :src="copyCanvasData.frontStartImg"
                style="width: 10% !important" />
            </div>
          </div>
          <span class="editIcon">{{ lang.Edit }}<b class="icon-edit--fill"></b></span>
        </div>
        <div class="detail-item"
          v-if="copyCanvasData?.frontEndImg"
          @click="jump('Design Your Band')">
          <div class="left">
            <div class="f-left">{{ lang.wristband.frontEndClipart }}:</div>
            <div class="f-right">
              <img :src="copyCanvasData.frontEndImg"
                style="width: 10% !important" />
            </div>
          </div>
          <span class="editIcon">{{ lang.Edit }}<b class="icon-edit--fill"></b></span>
        </div>

        <div class="detail-item"
          v-if="copyCanvasData?.backStartImg"
          @click="jump('Design Your Band')">
          <div class="left">
            <div class="f-left">{{ lang.wristband.backStartClipart }}:</div>
            <div class="f-right">
              <img :src="copyCanvasData.backStartImg"
                style="width: 10% !important" />
            </div>
          </div>
          <span class="editIcon">{{ lang.Edit }}<b class="icon-edit--fill"></b></span>
        </div>

        <div class="detail-item"
          v-if="copyCanvasData?.backEndImg"
          @click="jump('Design Your Band')">
          <div class="left">
            <div class="f-left">{{ lang.wristband.backEndClipart }}:</div>
            <div class="f-right">
              <img :src="copyCanvasData.backEndImg"
                style="width: 10% !important" />
            </div>
          </div>
          <span class="editIcon">{{ lang.Edit }}<b class="icon-edit--fill"></b></span>
        </div>

        <div class="detail-item"
          v-if="copyCanvasData?.aroundStartImg"
          @click="jump('Design Your Band')">
          <div class="left">
            <div class="f-left">{{ lang.wristband.aroundStartClipart }}:</div>
            <div class="f-right">
              <img :src="copyCanvasData.aroundStartImg"
                style="width: 10% !important" />
            </div>
          </div>
          <span class="editIcon">{{ lang.Edit }}<b class="icon-edit--fill"></b></span>
        </div>

        <div class="detail-item"
          v-if="copyCanvasData?.aroundEndImg"
          @click="jump('Design Your Band')">
          <div class="left">
            <div class="f-left">{{ lang.wristband.aroundEndClipart }}:</div>
            <div class="f-right">
              <img :src="copyCanvasData.aroundEndImg"
                style="width: 10% !important" />
            </div>
          </div>
          <span class="editIcon">{{ lang.Edit }}<b class="icon-edit--fill"></b></span>
        </div>

      </ul>
      <a href="javascript:;"
        @click="showMoreFun"
        class="more">
        {{ !showMore ? lang.ViewLess : lang.ViewMore }}<i :class="!showMore ? 'less' : 'more'"
          class="el-icon-d-arrow-right"></i>
      </a>
    </div>
    <div class="priceDetail">
      <ul>
        <!-- 总数 -->
        <li class="detail-item"
          @click="jump('Band Color & Quantity')"
          v-show="showQty">
          <div class="left">
            <div class="f-left">{{ lang.quantity }}:</div>
            <div class="f-right">
              <template v-if="calculateData.totalQuantity">
                <div>
                  {{ calculateData.totalQuantity }}&nbsp;
                </div>
              </template>
            </div>
          </div>
          <span class="editIcon">{{ lang.Edit }}<b class="icon-edit--fill "></b></span>
        </li>
        <li class="detail-item">
          <div class="left">
            <div class="f-left">{{ lang.unitPrice }}:</div>
            <div class="f-right">
              <CCYRate :price="calculateData.foundationUnitPrice"></CCYRate>
            </div>
          </div>
        </li>
        <li class="detail-item">
          <div class="left">
            <div class="f-left">{{ lang.optionsCharges }}:</div>
            <div class="f-right">
              <CCYRate :price="calculateData.toolingCharge"></CCYRate>
            </div>
          </div>
        </li>
        <li class="detail-item">
          <div class="left">
            <div class="f-left">{{ lang.subtotal }}:</div>
            <div class="f-right">
              <CCYRate
                :price="calculateData.foundationUnitPrice * calculateData.totalQuantity + calculateData.toolingCharge">
              </CCYRate>
            </div>
          </div>
        </li>
        <!-- 总数 -->
        <li class="detail-item"
          v-show="!calculateData.onlyAddInquiry && discountPrice != 0">
          <div class="left">
            <div class="f-left">
              {{ text1 }}
            </div>
            <div class="f-right"
              v-if="Object.keys(calculateData).length > 0">
              {{ text2 }}
              <CCYRate :price="discountPrice"></CCYRate>
            </div>
            <div class="f-right"
              v-else>
              {{ text2 }}
              <CCYRate price="0"></CCYRate>
            </div>
          </div>
        </li>
      </ul>
    </div>
    <div class="hr"></div>
    <div class="footBtn">
      <!-- 美国和澳大利亚不展示提前算税 -->
      <div
        :style="continentName == 'Europe' && textPrice > 0  ? 'display: flex;justify-content: space-between;width: 100%;padding: 0 10px;' : 'display: flex;justify-content: center;'">
        <div class="currencyText">
          {{ lang.Currency }}:
          <el-select v-model="currencyId"
            :placeholder="lang.PleaseSelect"
            @change="changeCurrency"
            size="small"
            style="width: 100px">
            <el-option v-for="item in currencyList"
              :key="item.id"
              :label="item.code"
              :value="item.id" />
          </el-select>
        </div>
        <div v-if="continentName == 'Europe' && textPrice > 0"
          :style="continentName == 'Europe' && textPrice > 0 ? 'display: flex;align-items: center;justify-content: center;' :'display:none'">
          <strong class="subTotalText">{{getTax(1)}}:&nbsp;</strong>
          <el-switch v-model="IncludingVAT"
            :active-value="1"
            :inactive-value="0"
            active-text="Yes"
            inactive-text="No"
            class="operation"></el-switch>
        </div>
      </div>

      <div class="sub">
        <div class="total-price">
          <span v-if="continentName != 'Europe'">{{ lang.total }}:</span>
          <span v-else>{{ IncludingVAT == 1 ? getTax(2) : getTax(3) }}:</span>
          <CCYRate :price="getTotalPrice(calculateData.totalPrice)"
            style="color: #de3500"></CCYRate>
        </div>
      </div>
      <freeTip></freeTip>
      <div class="btnGroup">

        <el-button class="submit btn"
          @click="submitFun('inquiry', $event)">{{ lang.submitInquiry }}
          <el-tooltip popper-class="cusToolTip"
            effect="light"
            :content="textInfo.inquiryTip"
            placement="top-start">
            <b class="icon-wenhao3 tip-icon"></b>
          </el-tooltip>
        </el-button>

        <el-button v-if="calculateData.onlyAddInquiry === 0 || !calculateData.onlyAddInquiry"
          class="toCart btn"
          @click="submitFun('cart', $event)">{{ this.$route.query.isBack ? lang.saveCart:lang.addToCart }}
          <el-tooltip popper-class="cusToolTip"
            effect="light"
            :content="textInfo.addCartTip"
            placement="top-start">
            <b class="icon-wenhao3 tip-icon"></b>
          </el-tooltip>
        </el-button>
      </div>
      <div class="btnGroup-text">
        <b class="icon-jingshi"></b>
        <div>{{ lang.prompter }} {{ this.$store.state.proSystem.email }}</div>
      </div>
    </div>
  </div>
</template>

<script>
import { getTaxByPrice } from "@/api/web";
import freeTip from "~/components/Quote/freeTip";
import { round2 } from "@/utils/utils";
export default {
  props: {
    giftQuantity1: {
      type: Number,
    },
    presentedQuantity: {
      type: Number,
    },
    showQty: {
      type: Boolean,
      default: false,
    },
    noText: {
      type: Boolean,
      default: false,
    },
    showPrice: {
      type: Boolean,
      default: false,
    },
    showMore: {
      type: Boolean,
      default: false,
    },
    selectedData: {
      type: Object,
    },
    calculateData: {
      type: Object,
    },
    defaultData: {
      type: Object,
    },
    textInfo: {
      type: Object,
      default: function () {
        return {};
      },
    },
    totalQuantity: {
      type: Number,
    },
    uploadList: {
      type: Array,
    },
    hasComment: {
      type: Boolean,
    },
    footer: {
      type: Boolean,
      default: false,
    },
    noDetails: {
      type: Boolean,
      default: false,
    },
    generalData: {
      type: Array,
    },
    filter: {
      type: Boolean,
    },
    copyCanvasData: {
      type: Object,
    },
  },
  data() {
    return {
      continentName: "",
      countryName: "",
      IncludingVAT: 1,
      textPrice: null,

      imageLoading: false,
      cusTop: 0,
      currencyId: "",
    };
  },
  components: {
    freeTip,
  },
  methods: {
    getNums(item) {
      const { adult, youth } = item; // 解构 adult 和 youth
      const labels = []; // 用于存储带标签的数量
      adult && labels.push(`${this.lang.wristband.adultQty}：${adult}`);
      youth && labels.push(`${this.lang.wristband.youthQty}：${youth}`);
      return `(${labels.join(", ")})`;
    },
    getAlias(item) {
      if (item.alias2) {
        return `${item.alias} + ${item.alias2}`;
      } else {
        return `${item.alias}`;
      }
    },
    getPantone(item) {
      if (item.isTwoToneCustom) {
        return `${item.mainColor.pantone} + ${item.accentColor.pantone}`;
      } else {
        return item.pantone;
      }
    },
    //欧洲国家文案
    getTax(type) {
      if (type == 1) {
        return "Including VAT";
      } else if (type == 2) {
        return "Subtotal incl. VAT";
      } else if (type == 3) {
        return "Subtotal excl. VAT";
      }
    },

    //税费开关开启，小计加上税费
    getTotalPrice(totalPrice) {
      if (this.IncludingVAT == 0 || this.continentName != "Europe") {
        return totalPrice;
      } else {
        let t = (Math.round(this.textPrice * totalPrice * 100) / 100).toFixed(
          2
        );
        return totalPrice + Number(t);
      }
    },
    getTop() {
      let el = document.getElementById("detailInfo");
      if (!el) {
        return false;
      }
      const { top, height } = el.getBoundingClientRect();
      // this.cusTop = (window.innerHeight - height) / 2;
      this.cusTop = (window.innerHeight - height) / 2 + 113 / 2 - 20;
    },
    getName(key) {
      let temp = this.generalData.find((x) => {
        return x.paramName == key;
      });
      if (key == "Wristband Style") {
        return this.lang.wristband.WristbandStyle;
      } else {
        return temp && temp.alias;
      }
    },
    showPopular(val) {
      this.$emit("currentPopularCardItem", val);
      this.$emit("tabsName", "POPULAR");
    },
    showCustom(val) {
      this.$emit("currentCustomCardItem", val);
      this.$emit("tabsName", "CUSTOM");
    },
    showInput(val) {
      val.show = true;
      this.$forceUpdate();
    },
    deleteInput(val, index, arr) {
      arr[index].quantity = undefined;
      arr.splice(index, 1);
    },
    inputChangeFun(val, index, arr) {
      if (!val || val == 0) {
        arr[index].quantity = undefined;
        arr.splice(index, 1);
      }
    },
    showMoreFun() {
      if (!this.showMore) {
        this.$refs.detailList.scrollTo(0, 0);
      }
      this.$emit("update:showMore", !this.showMore);
    },
    changeCurrency(val) {
      let findC = this.currencyList.find((item) => {
        return item.id === val;
      });
      if (findC) {
        this.$store.commit("setCurrency", findC);
      }
    },
    jump(val) {
      if (val == "Wristband Style") return;
      this.$emit("jump", val);
    },
    submitFun(type, e) {
      if (e.target.nodeName == "b") {
        return;
      }
      if (type == "inquiry") {
        this.$emit("submitInquiry");
      } else {
        this.$emit("addToCart");
      }
    },
  },
  computed: {
    lang() {
      return this.$store.getters.lang.quote || {};
    },
    proType() {
      return this.$store.state.proType;
    },
    subtotal() {
      return round2(
        round2(
          this.calculateData.foundationUnitPrice *
            this.calculateData.totalQuantity
        ) +
          this.calculateData.toolingCharge +
          (this.calculateData.setupCharge || 0)
      );
    },
    discountPrice() {
      if (this.calculateData) {
        return `${Math.abs(this.calculateData.totalPrice - this.subtotal)}`;
      } else {
        return 0;
      }
    },
    text1() {
      let discountName = "";
      //加急费，重量加价
      if (this.calculateData.discountPrice) {
        return this.lang.rushDelivery;
      }
      if (this.calculateData.discount > 1) {
        return this.lang.rushDelivery;
      } else if (this.calculateData.discount < 1) {
        discountName = this.lang.discount;
        return `${discountName} (${this.lang.Turnaround}: ${(
          Math.abs(1 - this.calculateData.discount) * 100
        ).toFixed(0)}% ${this.lang.p8}):`;
      }
    },
    text2() {
      let ac;
      if (this.calculateData.totalPrice > this.subtotal) {
        ac = "+";
      } else {
        ac = "-";
      }
      return ac;
    },

    currencyList() {
      return this.$store.state.currencyList;
    },
    canvasLoading() {
      return this.$store.state.canvasLoading;
    },
    device() {
      return this.$store.state.device;
    },
  },
  watch: {
    "$store.state.currency": {
      handler(newValue) {
        this.currencyId = newValue.id;
      },
      immediate: true,
    },
  },
  mounted() {
    window.addEventListener("scroll", () => {
      this.getTop();
    });
    setTimeout(() => {
      this.getTop();
    }, 1000);
  },
  created() {
    //提前算税费
    getTaxByPrice().then((res) => {
      this.textPrice = res.data.rate;
      this.countryName = res.data?.res?.country?.names.en;
      this.continentName = res.data?.res?.continent?.names.en; //洲
    });
  },
};
</script>

<style scoped lang="scss">
.el-icon-d-arrow-right.more {
  transform: rotate(90deg);
}

.el-icon-d-arrow-right.less {
  transform: rotate(270deg);
}

.WristbandDetails {
  position: relative;
  border-radius: 10px 0 0 0;
  top: auto !important;
  margin: 0 auto;
  font-size: 14px;
  align-items: flex-start;
  padding: 0 30px 20px 0;
  width: 520px;
  @media screen and (max-width: 767px) {
    grid-template-columns: 1fr;
    padding: 0;
    width: auto;
  }

  &.noDetails {
    @media screen and (max-width: 767px) {
      .con {
        display: none;
      }
    }
  }

  a.more {
    display: block;
    margin: 8px 0 0;
    text-decoration: none;
    font-size: 16px;
    font-weight: 400;
    color: #0066cc;
    text-align: center;

    @media screen and (max-width: 767px) {
      font-size: 12px;
    }
  }

  ::v-deep .el-input--small .el-input__inner {
    font-size: 15px;
  }

  ::v-deep .el-input-number {
    width: 100%;
  }

  .colorB3 {
    color: #b3b3b3;
  }

  .hr {
    background-color: #dcdfe6;
    position: relative;
    display: block;
    height: 1px;
    width: 100%;
    margin: 10px 0;
  }

  .topImage {
    grid-row: 1/6;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-bottom: 10px;
    padding: 10px;

    .imgWrap {
      display: flex;
      justify-content: center;
      align-items: center;
	  background-color: #fff;
    //   height: 200px;
      border-radius: 10px;
      // border: 1px dashed #d9dbdd;

      // img {
      // 	border-radius: 10px;
      // }
    }
  }

  .con {
    height: auto;

    .title {
      padding: 0 8px;
      font-size: 16px;
      font-weight: bold;
      color: #202428;
      line-height: 36px;
    }

    ul {
      min-height: 270px;
      height: 270px;
      overflow-y: auto;

      &.showMore {
        max-height: calc(100vh - 1000px);
      }
    }
  }

  ul {
    .detail-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 8px 10px;
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        background-color: #dddddd;
      }

      .l {
        font-size: 16px;
        color: rgb(85, 85, 85);

        @media screen and (max-width: 767px) {
          font-size: 12px;
        }
      }

      .left {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .f-left {
          // color: #222222;
          color: rgb(85, 85, 85);
          flex-grow: 1;
          flex-basis: 175px;
          flex-shrink: 0;
          font-size: 16px;

          span {
            display: none;
          }

          @media screen and (max-width: 767px) {
            font-size: 12px;
            font-family: Arial;
            color: #555;
          }
        }

        .f-right {
          margin-left: 10px;
          color: #222;
          word-break: break-word;
          text-align: right;
          font-size: 16px;
          line-height: normal;

          @media screen and (max-width: 768px) {
            font-size: 12px;
            font-family: Arial;
            color: #222;
          }
        }
      }

      .editIcon {
        flex-shrink: 0;
        margin-left: 10px;
        color: #b3b3b3;
        font-size: 12px;
      }

      .m {
        // width: 273px;
        text-align: right;
        color: #222;

        @media screen and (max-width: 767px) {
          width: 256px;
          color: #222;
        }
      }
    }

    .detail-item:nth-child(-n + 8) {
      .left {
        .f-left {
          display: flex;

          span {
            display: block;
          }
        }
      }
    }
  }

  .priceDetail {
    @media screen and (max-width: 767px) {
      margin-top: 0;
      margin-right: 0;
      line-height: 25px;
    }
  }

  .footBtn {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .sub {
      display: flex;
      align-items: flex-start;
      flex-direction: column;
      margin: 5px 0 12px;
      row-gap: 5px;

      .currencyText {
        font-size: 16px;
        color: #222222;
        font-weight: 400;
      }

      .total-price {
        // margin: 0 25px;
        font-size: 30px;
        color: #de3500;
        font-weight: 700;

        span {
          padding-top: 10px;
          font-size: 16px;
          color: #222222;
          font-weight: 400;

          @media screen and (max-width: 767px) {
            padding-top: 0;
          }
        }
      }
    }

    .btnGroup-text {
      background-color: #fff;
      margin: 20px 5px 0;
      border-radius: 4px;
      padding: 14px 15px 21px 22px;
      display: flex;
      align-items: center;

      b {
        font-size: 48px;
        color: #41a25e;
      }

      div {
        font-size: 16px;
        font-weight: 400;
        color: #333333;
        padding-left: 15px;
      }

      @media screen and (max-width: 767px) {
        padding: 10px 43px;

        div {
          font-size: 12px;
          font-family: Arial;
        }

        b {
          font-size: 24px;
        }
      }
    }

    .btnGroup {
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      flex-direction: column;

      @media screen and (max-width: 767px) {
        flex-direction: initial;

        .btn:first-child {
          margin-bottom: 0 !important;
        }

        .btn {
          font-weight: bold;
        }
      }

      .btn:first-child {
        margin-bottom: 10px;
      }

      .btn {
        flex: 1;
        margin: 0 5px;
        border-radius: 4px;
        background: transparent;
        border: none;
        font-size: 18px;
        font-family: Calibri;
        font-weight: 400;
        color: #ffffff;

        .tip-icon {
          width: 18px;
          height: 18px;
          margin-left: 10px;
          color: #fff;
        }
      }

      .submit {
        background-image: linear-gradient(
          to right,
          rgb(255, 65, 43) 0%,
          rgb(255, 119, 67) 100%
        );
      }

      .toCart {
        background: linear-gradient(to right, #0066cc 0%, #2fb6f5 100%);
      }
    }

    .tip {
      width: 100%;
      text-align: center;
      padding: 10px 0;
      cursor: pointer;
      font-size: 16px;
      color: #666666;
      text-decoration: none;

      .tip-icon {
        width: 22px;
        height: 22px;
        margin-right: 10px;
        vertical-align: middle;
      }

      span {
        max-width: 70%;
      }

      .text-mb {
        display: none;
      }
    }
  }

  @media screen and (max-width: 767px) {
    .topImage {
      .imgWrap {
      }
    }

    .con {
      .title {
        font-size: 14px;
      }

      .detail-item {
        padding: 5px 10px;
        font-size: 12px;
      }
    }

    .priceDetail {
      .detail-item {
        padding: 5px 10px;
        font-size: 12px;

        .left {
          display: grid;
          grid-template-columns: 1fr 1fr;

          .f-right {
            text-align: right;
          }
        }
      }
    }

    .footBtn {
      .sub {
        .currencyText {
          font-size: 12px;
        }

        .total-price {
          font-size: 21px;

          // margin: 0 15px;
          span {
            font-size: 12px;
          }
        }
      }

      .btnGroup {
        .submit {
          background-image: linear-gradient(
            to top,
            rgb(255, 65, 43) 0%,
            rgb(255, 119, 67) 100%
          );
          border-radius: 5px;
          font-size: 12px;
          font-family: Arial;
          font-weight: 700;
          color: #ffffff;
          padding: 12px;
          line-height: 13px;

          .tip-icon {
            width: 14px;
            height: 14px;
            font-size: 16px;
          }
        }

        .toCart {
          background: linear-gradient(0deg, #0066cc 0%, #2fb6f5 100%);
          border-radius: 5px;
          font-size: 12px;
          font-family: Arial;
          font-weight: 700;
          color: #ffffff;
          padding: 12px;
          line-height: 13px;

          .tip-icon {
            width: 14px;
            height: 14px;
            font-size: 16px;
          }
        }
      }

      .tip {
        background: #ebebeb;
        border-radius: 3px;

        .tip-icon {
          width: 14px;
          height: 14px;
        }

        span {
          font-size: 12px;
          font-family: Arial;
        }

        .text-pc {
          display: none;
        }

        .text-mb {
          display: inline-block;
        }
      }
    }
  }
}
::v-deep .el-switch__label * {
  line-height: 1;
  font-size: 13px;
  display: inline-block;
}

::v-deep .el-switch__label {
  position: absolute;
  display: none;
  color: #fff !important;
  //   font-size: 13px !important;
}

::v-deep .el-switch__label--right {
  z-index: 1;
  right: 20px !important;
  margin-left: 0px;
}

::v-deep .el-switch__label--left {
  z-index: 1;
  left: 20px !important;
  margin-right: 0px;
}

::v-deep .el-switch__label.is-active {
  display: block;
}

::v-deep .el-switch .el-switch__core,
.el-switch .el-switch__label {
  width: 50px !important;
}
</style>