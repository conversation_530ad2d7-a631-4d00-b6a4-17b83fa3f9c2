<template>
	<div class="pinsQuoteWrap">
		<Preloader v-if="isLoading"></Preloader>
		<div class="containerWrap" v-else>
			<!-- <QuoteTitle :h1-text="lang.pins.h1" :prompts-text="lang.pins.prompts"></QuoteTitle> -->

			<div class="content">
				<div class="leftArea">
					<p class="leftArea_title">{{ lang.MetalBusinessCards }}</p>
					<!-- 左上 <-> 右下 的消失动画 -->
					<transition enter-active-class="animate__animated animate__faster animate__fadeInBottomRight" leave-active-class="animate__animated animate__faster animate__fadeOutBottomRight">
						<SwiperDetail :imgList="carouselList" v-show="showRightArea" :selectedData="selectedData" :generalData="generalData" @toPosition="toPosition" :attachment="attachment" />
					</transition>
				</div>

				<div class="rightArea" id="rightAreaCustom">
					<template v-for="(item, index) in generalData">
						<template v-if="item.paramName === 'quoteCategory'">
							<!--步骤一-->
							<div class="step-item step-style" :class="{ mask: maskName === item.paramName, hideContent: $route.query.designPic }" :id="item.paramName">
								<div class="box-border">
									<i class="el-icon-close" @click="closeMask"></i>
								</div>
								<h3 class="step-title">
									<span>{{ lang.Step }} {{ item.customIndex }}</span>
									{{ lang.selectTypes }}
									<span v-if="$route.query.designPic" style="color: #999999">: {{ $route.query.techName }}</span>
								</h3>
								<div class="step-box step-style-box">
									<div class="item-wrap" v-for="citem in item.childList" :key="citem.id" @click="selectQuoteParams(item, citem)" :class="{ active: hasId(citem.id, selectedData[item.paramName]) }">
										<div class="item">
											<div class="imgWrap">
												<img :src="citem.imagePhoto" alt="" />
											</div>
											<!--                                            <i class="el-icon-zoom-in zoomIcon" @click.stop="viewVideo(item, citem, 'video')"></i>-->
										</div>
										<div class="textWrap">
											<div>
												<template>
													<p class="normal-text">{{ citem.cateNameQuote ? citem.cateNameQuote : citem.cateName }}</p>
												</template>
											</div>
										</div>
									</div>
								</div>
							</div>
						</template>

						<template v-if="item.paramName === 'Size'">
							<!--步骤二-->
							<div class="step-item step-quantity" :class="{ mask: maskName === item.paramName, hideContent: $route.query.designPic }" :id="item.paramName">
								<div class="box-border">
									<i class="el-icon-close" @click="closeMask"></i>
								</div>
								<h3 class="step-title">
									<span stepColor>{{ lang.Step }} {{ item.customIndex }}</span>
									<!-- {{ item.alias ? item.alias : item.paramName }} -->
									{{ lang.selectQuantity }}
									<span v-if="$route.query.designPic" style="color: #999999">: {{ $route.query.techName }}</span>
								</h3>
								<div class="step-box">
									<StepQtyCustom :options="tablePrice" :customQty.sync="customQty" :customQtySelected.sync="customQtySelected" @calcPrice="calcPrice" @updatePriceTable="updatePriceTable" />
								</div>
								<div class="confirmBtnWrap">
									<QuoteBtn @click.native="showMaskFn(item.paramName)">{{ lang.next }}</QuoteBtn>
								</div>
							</div>
						</template>

						<template v-if="item.paramName === 'Customize Your Business Cards'">
							<!--步骤三-->
							<div class="step-item step-cards" :class="{ mask: maskName === item.paramName, hideContent: $route.query.designPic }" :id="item.paramName">
								<div class="box-border">
									<i class="el-icon-close" @click="closeMask"></i>
								</div>
								<h3 class="step-title">
									<span stepColor>{{ lang.Step }} {{ item.customIndex }}</span>
									{{ item.alias ? item.alias : item.paramName }}
									<span v-if="$route.query.designPic" style="color: #999999">: {{ $route.query.techName }}</span>
								</h3>

								<div class="step-box step-cards-box">
									<StepUpload :id="item.paramName" ref="stepUpload" class="step-item step-upload" :index="item.customIndex" :class="{ mask: maskName === item.paramName, hideContent: $route.query.designPic }" :itemData="item" :uploadList.sync="uploadArtworkList" :isUpload.sync="isUpload" :maskName="maskName" :selectedData="selectedData" @selectedParam="selectQuoteParams($event.parent, $event.child, true)" :generalData="generalData" @closeMask="closeMask" @showMaskFn="showMaskFn" :key="index" :addTextShow="false"> </StepUpload>
								</div>
							</div>
						</template>

						<template v-if="item.paramName === 'Select Turnaround Time & Comments'">
							<!--步骤四-->
							<div class="step-item step-turnaround" :class="{ mask: maskName === item.paramName, hideContent: $route.query.designPic }" :id="item.paramName">
								<div class="box-border">
									<i class="el-icon-close" @click="closeMask"></i>
								</div>
								<h3 class="step-title">
									<span stepColor>{{ lang.Step }} {{ item.customIndex - 1 }}</span>
									{{ item.alias ? item.alias : item.paramName }}
									<span v-if="$route.query.designPic" style="color: #999999">: {{ $route.query.techName }}</span>
								</h3>
								<div class="step-box step-turnaround-box">
									<p class="step-subtitle">
										{{ lang.readytoShip }}
									</p>
									<div class="block-cont">
										<StepTime class="step-item step-date" :index="item.customIndex" :itemData="item" :priceInfo="priceInfo" :previewMode="previewMode" :selectedParams="selectedData" @showMaskFn="showMaskFn" @selectItem="selectQuoteParams($event.item, $event.citem)" @closeMask="closeMask" :key="index"></StepTime>
										<div>
											<el-input type="textarea" v-model="remark" resize="none" rows="5" :placeholder="lang.Comments"></el-input>
										</div>
									</div>
								</div>
							</div>
						</template>
					</template>

					<div class="detail">
						<div class="detial-cont">
							<div class="detail-left">
								<span class="title">
									{{ lang.orderSummary }}
								</span>
								<div class="cont">
									<div>
										<span>{{ lang.quantity }}:</span>
										<span>{{ priceInfo.totalQuantity }}</span>
									</div>
									<div>
										<span>{{ lang.unitPrice }}:</span>
										<CCYRate :price="priceInfo.foundationUnitPrice"></CCYRate>
									</div>
									<div>
										<span>{{ lang.discount }}:</span>
										<span>{{ priceInfo.discount ? (1 - priceInfo.discount).toFixed(2) * 100 + "%" : "" }}</span>
									</div>
								</div>
							</div>
							<div class="detail-right">
								<div class="item">
									<span>{{ lang.subtotal }}</span>
									<CCYRate :price="priceInfo.totalPrice" style="color: #de3500"></CCYRate>
								</div>
								<freeTip />
								<button class="addBuy" @click="addCart">{{ langSemi.addBuy }}</button>
							</div>
						</div>
					</div>

					<!-- <Detail class="type3" :key="2" :absoluteImg="comeFromDZ.absoluteImg" :selectedData="selectedData" :priceInfo="priceInfo" :customQty="customQty" :textInfo="textInfo" :generalData="generalData" :attachment="attachment" @toPosition="toPosition" @addInquiry="debounceAddInquiry" @addCart="addCart"></Detail> -->
				</div>
			</div>
			<!-- 建议弹窗-->
			<RecomendDialog :commentsDialog.sync="commentsDialog" :paramValue="selectedParamsValue" @next="recomendNext"></RecomendDialog>
			<!-- 预览弹窗-->
			<VideoPreviewDialog :pic-dialog.sync="picDialog" :zoom-pic="zoomPic" :temp-type="tempType" :selected-params-value="selectedParamsValue" @qtyAndBtnConfirm="qtyAndBtnConfirm" @qtyAndBtnNext="qtyAndBtnNext" @dialogNextStep="dialogNextStep"></VideoPreviewDialog>
			<!-- 升级数量弹窗-->
			<BaseDialog v-model="upgradeDialog" :width="device != 'mb' ? '640px' : '320px'">
				<Upgrade @closeUpgrade="upgradeDialog = false" :tipArr="tipArr" :typeName="cateData.cateName" @upgradeLevel="upgradeLevel"></Upgrade>
			</BaseDialog>
			<!-- o2o预览页面弹窗-->
			<BaseDialog v-model="showOtoDialog" :width="device != 'mb' ? '570px' : '90%'">
				<div class="otoWrap">
					<img src="@/assets/images/oto.png" alt="" />
					<p>{{ lang.p1 }}</p>
					<h3>{{ lang.p2 }}</h3>
					<p style="color: #666666">
						{{ lang.p3 }}<br />
						{{ lang.p4 }}.
					</p>

					<div class="box">
						<p class="t1">
							{{ lang.p5 }} <br />
							{{ lang.p6 }}!
						</p>
						<a href="https://www.o2o.co/previewWeb" style="text-decoration: none">
							<el-button type="primary">{{ lang.p7 }}</el-button>
						</a>
					</div>
				</div>
			</BaseDialog>
			<!--			询盘弹窗-->
			<infoDialog :infoDialogVisible.sync="infoDialogVisible" :uploadList.sync="uploadList" :otherUpload="uploadArtworkList" :recomendUpload="recomendUpload" @getValue="getValueFun"></infoDialog>
			<!--    遮罩-->
			<myMask :maskName.sync="maskName"></myMask>
			<!--  手机，ipad端预览-->
			<PreviewBtn :showArea.sync="showArea"></PreviewBtn>
			<BaseDialog v-model="noFileDialog" :width="device != 'mb' ? '485px' : '90%'" :model="false">
				<template #closeIcon>
					<div style="display: none"></div>
				</template>
				<infoUpload :infoUploadList="infoUploadList" @pushInfoList="pushInfoList" @delInfoList="delInfoList" @updateInquiry="updateInquiry" @closeInfoDialog="closeInfoDialog"> </infoUpload>
			</BaseDialog>
		</div>
	</div>
</template>

<script>
import "@/plugins/element";
import VideoPlayer from "@/components/Quote/VideoPlayer.vue";
import freeTip from "~/components/Quote/freeTip";
import QuoteBtn from "@/components/Quote/QuoteBtn";
import myMask from "@/components/Quote/Mask";
import Detail from "@/components/Quote/Detail";
import BaseDialog from "@/components/Quote/BaseDialog";
import Upgrade from "@/components/Quote/Upgrade";
import Preloader from "@/components/Quote/Preloader";
import PriceText from "@/components/Quote/PriceText";
import Star from "@/components/Quote/Star";
import StepTime from "@/components/Quote/StepTime";
import StepQtyCustom from "@/components/semiquote/StepQtyCustom";
import Corner from "@/components/Medals/Corner";
import Pimg from "@/components/Medals/Pimg";
import QtyAndBtn from "@/components/Medals/QtyAndBtn";
import DialogBM from "@/components/Medals/DialogBM";
import infoDialog from "@/components/Medals/infoDialog";
import RecomendDialog from "@/components/Quote/RecomendDialog.vue";
import quoteMixin from "@/mixins/quote";
import QuoteTitle from "@/components/Quote/QuoteTitle.vue";
import PreviewBtn from "@/components/Quote/PreviewBtn.vue";
import CustomCircle from "@/components/Quote/customCircle.vue";
import { isImageType } from "@/utils/utils";
import SwiperDetail from "@/components/Cufflinks/SwiperDetail.vue";
import StepUpload from "@/components/semiquote/StepUpload.vue";
import { getPriceTable } from "@/api/pins";
import VideoPreviewDialog from "@/components/Quote/VideoPreviewDialog.vue";
export default {
	head() {
		return {
			title: "Quote page",
		};
	},
	components: {
		VideoPreviewDialog,
		CustomCircle,
		PreviewBtn,
		QuoteTitle,
		DialogBM,
		QuoteBtn,
		Detail,
		BaseDialog,
		Upgrade,
		myMask,
		VideoPlayer,
		Preloader,
		PriceText,
		Star,
		StepUpload,
		StepQtyCustom,
		StepTime,
		Corner,
		Pimg,
		QtyAndBtn,
		infoDialog,
		RecomendDialog,
		SwiperDetail,
		freeTip,
	},
	mixins: [quoteMixin],
	name: "custom-SUNamel-pins",
	data() {
		return {
			pid: 300,
			cateId: "",
			sizeId: "",

			productsName: "Custom Metal Business Cards",
			restaurants: [
				{ value: "50", address: "50" },
				{ value: "100", address: "100" },
				{ value: "200", address: "200" },
				{ value: "300", address: "300" },
				{ value: "500", address: "500" },
				{ value: "1000", address: "1000" },
				{ value: "3000", address: "3000" },
				{ value: "5000", address: "5000" },
				{ value: "10000", address: "10000" },
			],
			dropActive: false,
			showUpload: false,

			customQtySelected: null,

			stepBg: "data:image/png;base64,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",
			cornerBg: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAACXBIWXMAAAsTAAALEwEAmpwYAAACDUlEQVRIic3WTUiUURTG8d/M+DV+DJaIfZjJVLpIKiSKPkCCFrbNlYugqIg2QlARtIhAMghaBC6KWrQJiaIQokUJkRBSVFjtLCJTo4wsqQyzeVuYZo5jJg70LM859/nfc++5L2+o82BtYG41iNs4gq7wHJtDDNtxH5XpAIypECfSCYCt6QbE0g3w/wNC4bDC6i2EQlPmMzCMrNmZRyypP6RwTY3c0gp9rWeTasJGH8a/m0cyLN1xVOGaGjDy5dOUdWG8SGWQSuHMbOW7jolVbQBvblzwrq0lJeDJ5GBmrMiKA83mr69NXpAdVb77uILKtQSBvtaz+u9cSb0ZtE0OLti2U05JmdK6BsU1dePxSE6e+J5G+ctWEwR6rzV73349pTlE9m9c3o0GEy76c9djuWWVsooWKqioFopkGOp7Kb6vSW5ZJUGg5+oZHzpuTms+BhhGHNVjweDHiI+d7aKL47KLS+XFqxStr5VVtFCQSOi5fNrAg1t/Nef3O2jC94mJYGTYq4uNPnbeHd1JboEgkfC65ZSBh0mnOm0HMIB8bPojGyQMPrsna16JnJIy3ZdO+vQLOFOFOg+OT0oUHViVXBUSXRQ31DvlRE+riZ+KIdShP6kqCGZlPhkAz1GLt7NymwEAHmEznqYLwGgn63DKpOmaKwB8w2GsxHl8nWvAmLqwFyWoxzmjfwz9ZtDdT0Zfh/5OpQ/VAAAAAElFTkSuQmCC",
		};
	},
	methods: {
		isImageType,
		delUploadImg(index) {
			this.uploadArtworkList.splice(index, 1);
		},
		linkTo(val) {
			this.$router.push({
				path: val.url,
			});
		},
		async updatePriceTable(val = 0) {
			const paramTable = {
				quantity: val,
				projectName: this.projectName,
				cateId: this.cateId,
				sizeId: this.sizeId,
			};
			const { data: tablePrice } = await getPriceTable(paramTable);
			this.tablePrice = tablePrice.map((x, i, arr) => {
				x.id = i;
				x.custom = i === arr.length - 1;
				return x;
			});
		},
	},
	computed: {},
	watch: {
		// isLoading(newVal){
		// 	}
	},
	mounted() {},
};
</script>

<style scoped lang="scss">
.tips.type2 {
	top: 0;
	transform: translateY(0);
	right: auto;
	left: 0;
}

.pinsQuoteWrap ::v-deep {
	font-family: Calibri, Arial, serif;
	@media screen and (max-width: 767px) {
		input,
		textarea {
			font-size: 16px !important;
		}
	}

	.viewMore {
		@media screen and (min-width: 768px) {
			display: none;
		}
	}

	.step-title {
		background-color: #f9f9f9;
		display: flex;
		align-items: center;
		> span {
			// color: #d17448;
			font-size: 18px;
			@media screen and(max-width: 768px) {
				font-size: 16px;
			}
			width: 100px;
			height: 36px;
			line-height: 36px;
			margin-right: 10px;
			text-align: left;
			text-indent: 1em;
			display: inline-block;
			background: url("data:image/png;base64,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")
				center center / 100% no-repeat;
			color: #fff;
			border-top-left-radius: 4px;
		}
	}

	ul {
		margin: 0;
	}

	img {
		border: none;
		vertical-align: middle;
		max-width: 100%;
		max-height: 100%;
	}

	.custom-shadow {
		position: relative;
		background-color: #fff;
	}

	.custom-shadow:after,
	.custom-shadow:before {
		content: "";
		position: absolute;
		z-index: -1;
		bottom: 12px;
		left: 5px;
		width: 50%;
		height: 20%;
		// box-shadow: 0 14px 7px #d9dbdd;//size阴影
		transform: rotate(-3deg);
	}

	.custom-shadow:after {
		right: 5px;
		left: auto;
		transform: rotate(3deg);
	}

	.video-js {
		overflow: hidden;
		border-radius: 6px 6px 0 0;
	}

	.containerWrap {
		background-color: #fff;
		font-size: 18px;
		padding-top: 20px;
		.content {
			// padding: 20px 11.5vw 0;
			padding: 3.8em max(calc(50% - 700px), 1.5vw) 4.2em;
		}
		.isComment {
			width: 100%;
			height: 100%;
			border: 1px solid #ccc;
			padding: 10px;
			border-radius: 4px;
			box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
			text-align: center;

			.textWrap {
				display: flex;
				align-items: center;
				justify-content: flex-start;
				margin-top: 0 !important;
			}

			.circle2 {
				border-color: #d17448;
				background-color: #d17448;

				&::after {
					background-color: #ffffff;
				}
			}

			.el-textarea {
				flex: 1;
				margin: 10px 0;

				textarea {
					height: 100%;
					background-color: #f5f5f5 !important;
				}
			}

			button {
				background-color: #d17448;
				color: #fff;
				padding: 4px;
				width: 100px;
				outline: none;
				border: none;
				border-radius: 4px;
				font-size: 14px;
				text-align: center;
			}
		}

		.content {
			position: relative;
			display: grid;
			grid-template-columns: repeat(48, 1fr);
			// grid-template-columns: 1fr 1fr;
			margin: 20px 0 0;
			.leftArea {
				grid-column: 2/24;
				.leftArea_title {
					font-size: 36px;
					text-align: center;
				}
				// .pic_vIew{
				// 	position:sticky;
				// 	&>p{
				// 		font-size: 36px;
				// 		text-align: center;
				// 	}
				// }
			}

			.rightArea {
				grid-column: 25/49;
				// margin-right: 10px;
				// grid-column: 20/48;

				// padding-right: 30px;
				.rightTop {
					padding: 0 20px;
					display: grid;
					grid-template-columns: 1fr 1fr;
					column-gap: 7%;
					.size-item {
						align-items: center;
						background: #f4f5f5;
						border: 1px solid transparent;
						border-radius: 10px;
						cursor: pointer;
						display: flex;
						height: 40px;
						padding: 0 4px 0 20px;
						transition: all 0.3s;
					}
					.size-item.active {
						border-color: #d17448;
						.circle2 {
							border-color: #d17448;
							background: #d17448;
							&::after {
								background-color: #fff;
							}
						}
					}
				}
				.mask {
					z-index: 101;
					background-color: #fff;
				}
				.detail {
					padding-inline: 20px;
					.detial-cont {
						display: flex;
						justify-content: space-between;
						border-radius: 9px;
						padding: 30px 20px;
						background-color: #f6f6f6;
						@media screen and(max-width: 768px) {
							flex-direction: column;
						}
						.detail-left {
							.title {
								font-weight: bold;
								font-size: 20px;
							}
							.cont {
								margin-top: 15px;
								div {
									display: flex;
									justify-content: space-between;
									min-width: 150px;
									&:not(:first-child) {
										margin-top: 8px;
									}
								}
							}
						}
						.detail-right {
							text-align: center;
							.addBuy {
								border-radius: 9px;
								padding-inline: 3em;
								text-transform: uppercase;
								height: 40px;
								line-height: 40px;
								text-align: center;
								font-weight: bold;
								background-color: #d07042;
								color: #fff;
								margin-top: 15px;
							}
						}
					}
				}
			}
		}

		.picWrap {
			display: flex;
			justify-content: center;
			align-items: center;
			flex-direction: column;

			img {
				width: 100%;
				height: 100%;
				object-fit: contain;
			}
		}

		.footer {
			display: grid;
			grid-template-columns: 700px;
			justify-content: center;
			padding: 20px;
			background: #eef2f5;
		}

		.small-title {
			position: relative;
			margin-bottom: 16px;
			font-size: 16px;
			color: #666666;
		}

		.d-flex-center {
			display: flex;
			justify-content: center;
			align-items: center;
		}

		.circle2 {
			display: flex;
			justify-content: center;
			align-items: center;
			width: 18px;
			height: 18px;
			min-width: 18px;
			border: 1px solid #afb1b3;
			border-radius: 50%;
			margin-right: 10px;
			background-color: #fff;
			transition: all 0.3s;

			&::after {
				content: "";
				width: 6px;
				height: 6px;
				min-width: 6px;
				background: #d4d7d9;
				border-radius: 50%;
			}
		}

		.circle {
			position: absolute;
			left: 50%;
			bottom: 0;
			transform: translate(-50%, 100%);
			width: 28px;
			height: 15px;
			border: 1px solid #e9ecf0;
			border-top: none;
			background: #edf1f5;
			border-radius: 0 0 16px 16px;
			z-index: 10;
			transition: all 0.3s;

			.inner-circle {
				position: absolute;
				left: 50%;
				top: 0;
				transform: translate(-50%, -50%);
				width: 18px;
				height: 18px;
				border-radius: 50%;
				background: #ffffff;
				border: 1px solid #aaaeb3;
				transition: all 0.3s;

				&:after {
					content: "";
					position: absolute;
					left: 50%;
					top: 50%;
					transform: translate(-50%, -50%);
					width: 6px;
					height: 6px;
					background-color: #aaaeb3;
					border-radius: 50%;
					transition: all 0.3s;
				}
			}
		}

		.circle3 {
			display: flex;
			justify-content: center;
			align-items: center;
			width: 18px;
			height: 18px;
			min-width: 18px;
			border: 1px solid #dae0e5;
			background-color: white;
			margin-right: 6.5px;
			transition: all 0.3s;
			position: relative;

			&::after {
				position: absolute;
				content: "";
				left: 50%;
				top: 50%;
				transform: translate(-50%, -50%);
				height: 6px;
				width: 6px;
				border-radius: 50%;
				background-color: #dae0e5;
			}

			/* .checkIcon {
        font-size: 16px;
        color: #ffffff;
      } */
		}

		.confirmBtnWrap {
			margin-top: 33px;
		}

		.drawDialog {
			.el-drawer__header {
				margin-bottom: 0;
				padding: 10px;
			}
		}

		@media screen and (max-width: 1500px) {
			.containerWrap {
				padding-top: 0;
			}
			.content {
				.leftArea {
					// display: none;
					grid-column: 2/48;
				}

				.rightArea {
					// display: none;
					grid-column: 2/48;
					// grid-column-start: span 2;
					.rightTop {
						margin-top: 30px;
					}
				}

				.rightArea.rightFixedArea {
					overflow: hidden auto;
					display: block;
					margin: 0;

					.detailList {
						position: relative;
						top: 0 !important;
						box-shadow: none;
					}
				}
			}
		}

		@media screen and (max-width: 767px) {
			background-color: #ebebeb;
			padding-top: 15px;
			.containerWrap {
				// background-color:#ebebeb !important;
			}
			.content {
				padding: 0;
				margin: 0;
				.leftArea {
					// .pic_vIew{
					// 	.title{
					// 		font-size: 28px;
					// 		margin-bottom: 10px;
					// 	}
					// }
					.leftArea_title {
						font-size: 28px;
						margin-bottom: 10px;
					}
					.myswiper2 {
						height: initial;
					}
					.myswiper1 {
						display: none;
					}
				}
				.rightArea {
					.rightTop {
						padding: 0;
						margin: 15px 0 !important;
					}
				}
			}

			.footer {
				grid-template-columns: 1fr;
				padding: 10px;
				background-color: #e0e0e0;
			}

			.small-title {
				font-size: 12px;
				margin-bottom: 15px;
			}

			.circle {
				width: 20px;
				height: 10px;

				.inner-circle {
					width: 14px;
					height: 14px;

					&:after {
						content: "";
						width: 5px;
						height: 5px;
					}
				}
			}

			.circle2 {
				width: 18px;
				height: 18px;
				margin-right: 5px;

				&::after {
					width: 5px;
					height: 5px;
				}
			}

			.confirmBtnWrap {
				margin-top: 20px;
			}
		}
	}

	.step-item {
		position: relative;
		// margin-bottom: 16px;
		background-color: #fff;
		box-sizing: content-box;
		// padding: 40px 30px;
		padding: 30px 20px;
		border-radius: 10px;

		&.hideContent {
			.step-box {
				display: none;
			}

			.step-title {
				margin-bottom: 0;
			}
		}

		.box-border {
			display: none;
			.el-icon-close {
				display: flex;
				justify-content: center;
				align-items: center;
				position: absolute;
				font-weight: 700;
				top: 0;
				right: 0;
				width: 40px;
				height: 40px;
				transform: translate(50%, -50%);
				cursor: pointer;
				background: #ffffff;
				border-radius: 50%;
				box-shadow: 1px 2px 20px 0 rgba(0, 0, 0, 0.2);
				z-index: 10;

				// 下一步右上角的关闭按钮 不需要了
				// display: none;
			}
		}

		&.mask {
			position: relative;
			z-index: 101;

			.confirmBtnWrap {
				position: relative;
			}

			// 下一步的
			.box-border {
				position: absolute;
				left: 0;
				right: 0;
				top: 0;
				bottom: 0;
				display: block;
				background-color: #fff;
				border: 1px solid #d9dbdd;
			}

			.step-title {
				position: relative;
				z-index: 9;
			}

			.step-box {
				position: relative;
			}
		}

		.step-title {
			font-size: 18px;
			font-weight: 700;
			color: #333333;
			margin-bottom: 23px;

			.step-title-icon {
				width: 21px;
				margin-left: 4px;
				cursor: pointer;
				vertical-align: middle;
			}
		}

		.step-title.title5 {
			margin-bottom: 4px;
		}

		@media screen and (min-width: 768px) and (max-width: 1499px) {
			padding: 30px 10px;

			.small-title {
				margin-bottom: 15px;
			}
		}

		@media screen and (max-width: 767px) {
			margin-bottom: 10px;
			background-color: #fff;
			border-radius: 5px;
			padding: 20px 7px;
			&.mask {
				.box-border {
					.el-icon-close {
						width: 30px;
						height: 30px;
						transform: translate(0, 0);
						box-shadow: none;
					}
				}
			}
			.step-title {
				margin-bottom: 10px;
				font-size: 14px;
				font-weight: bold;
				color: #171719;

				.step-title-icon {
					width: 17px;
					margin-left: 5px;
				}
			}
		}
	}

	.step-size .step-title {
		margin-bottom: 30px;
	}
	.step-item .step-box {
		display: grid;
		justify-content: space-between;
		column-gap: 25px;
		row-gap: 23px;

		.item-wrap {
			position: relative;
			// overflow: hidden;

			p.normal-text,
			p.t1 {
				transition: all 0.3s;
			}

			@media (any-hover: hover) {
				&:hover {
					p.normal-text {
						color: #d17448;

						span {
							color: #333333;
						}
					}

					p.t1 {
						color: #d17448;
					}

					.circle2 {
						border-color: #d17448;
						background: #d17448;

						&::after {
							background-color: #fff;
						}
					}

					.circle3 {
						border-color: #d17448;
						background-color: #d17448;

						&::after {
							background: white;
						}
					}

					.zoomIcon {
						color: #d17448 !important;
					}
				}
			}

			.item {
				position: relative;
				border-radius: 6px;
				display: flex;
				flex-direction: column;
				justify-content: space-between;
				align-items: center;
				cursor: pointer;
				transition: all 0.3s;
				border: 1px solid transparent;

				.imgWrap {
					display: flex;
					justify-content: center;
					align-items: center;
					height: 100%;
					width: 100%;

					img {
						object-fit: contain;
						border-radius: 6px 6px 0 0;
					}
				}

				// @media (any-hover: hover) {
				// 	&:hover {
				// 		border-color: #d17448;
				// 		box-shadow: 0 3px 4px 0 #cccccc;

				// 		.circle {
				// 			border-color: #d17448;
				// 		}
				// 	}
				// }
			}

			.item.linearBorder {
				background-image: linear-gradient(0deg, #edf1f5 0%, #f7f9fa 100%), linear-gradient(0deg, #e9ecf0, #f7f9fa);
				background-origin: border-box;
				background-clip: content-box, border-box;
				@media (any-hover: hover) {
					&:hover {
						background-image: linear-gradient(0deg, #edf1f5 0%, #f7f9fa 100%), linear-gradient(0deg, #d17448, #f7f9fa);
						box-shadow: 0 3px 4px 0 #cccccc;
					}
				}
			}

			.textWrap {
				.normal-text {
					font-size: 16px;
					color: #333333;
					transition: all 0.3s;
					text-align: left;
				}

				.tip-text {
					color: #de3500;
					font-size: 16px;
				}
			}

			&.active {
				.circle3 {
					border-color: #d17448;
					background-color: #d17448;

					.checkIcon {
						color: #ffffff;
					}
				}

				.item {
					border-color: #d17448;

					.circle {
						border-color: #d17448;

						.inner-circle {
							border-color: #d17448;

							&::after {
								background-color: #d17448;
							}
						}
					}
				}

				.item.linearBorder {
					background-image: linear-gradient(0deg, #edf1f5 0%, #f7f9fa 100%), linear-gradient(0deg, #d17448, #f7f9fa);
					box-shadow: 0 3px 4px 0 #cccccc;
				}

				.textWrap {
					.circle2 {
						border-color: #d17448;
						background: #d17448;

						&::after {
							background-color: #fff;
						}
					}
				}
			}

			& .textWrap {
				display: flex;
				justify-content: center;
				align-items: center;
				margin-top: 0;
				// flex-direction: column;
				// background: #f4f5f5;
				padding: 5px 0;
			}
		}

		@media screen and (min-width: 768px) and (max-width: 1499px) {
			column-gap: 12px;
			row-gap: 23px;
		}

		@media screen and (max-width: 767px) {
			column-gap: 5px;
			row-gap: 10px;

			.item-wrap {
				.item {
					border-radius: 5px;
					@media (any-hover: hover) {
						&:hover {
							background-image: none;
							box-shadow: none;
							border-color: #e6e6e6;

							.circle {
								border-color: transparent;
							}
						}
					}
				}

				.textWrap {
					padding-bottom: 0;

					.normal-text {
						font-size: 12px;
						text-align: center;
					}

					.tip-text {
						font-size: 12px;
					}
				}

				&.active {
					.item {
						border-color: #d17448;
						background-image: none;
						box-shadow: none;

						.circle {
							border-color: #d17448;

							.inner-circle {
								border-color: #d17448;

								&::after {
									background-color: #d17448;
								}
							}
						}
					}
				}

				& > .textWrap {
					display: flex;
				}
			}
		}
	}

	.step-packing.mask {
		.confirmBtnWrap {
			display: flex;
		}
	}

	.step-style {
		.step-box {
			grid-template-columns: repeat(4, 1fr);
			@media screen and(max-width: 768px) {
				grid-template-columns: repeat(2, 1fr);
			}
			column-gap: 20px;
			.item-wrap {
				border: 1px solid #959292;
				border-radius: 5px;
				padding: 15px 3px;
				font-size: 10px;
				cursor: pointer;
				.item {
					min-height: 100px;
				}
				.textWrap {
					.normal-text {
						font-size: 12px;
						color: #333333;
						transition: all 0.3s;
						text-align: left;
					}

					.tip-text {
						color: #de3500;
						font-size: 16px;
					}
				}

				&.active {
					border-color: #d17448;
					position: relative;
					.item {
						border: none;
					}
					&::before {
						position: absolute;
						content: "";
						right: 0;
						top: 0;
						width: 15px;
						height: 15px;
						background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAACXBIWXMAAAsTAAALEwEAmpwYAAACDUlEQVRIic3WTUiUURTG8d/M+DV+DJaIfZjJVLpIKiSKPkCCFrbNlYugqIg2QlARtIhAMghaBC6KWrQJiaIQokUJkRBSVFjtLCJTo4wsqQyzeVuYZo5jJg70LM859/nfc++5L2+o82BtYG41iNs4gq7wHJtDDNtxH5XpAIypECfSCYCt6QbE0g3w/wNC4bDC6i2EQlPmMzCMrNmZRyypP6RwTY3c0gp9rWeTasJGH8a/m0cyLN1xVOGaGjDy5dOUdWG8SGWQSuHMbOW7jolVbQBvblzwrq0lJeDJ5GBmrMiKA83mr69NXpAdVb77uILKtQSBvtaz+u9cSb0ZtE0OLti2U05JmdK6BsU1dePxSE6e+J5G+ctWEwR6rzV73349pTlE9m9c3o0GEy76c9djuWWVsooWKqioFopkGOp7Kb6vSW5ZJUGg5+oZHzpuTms+BhhGHNVjweDHiI+d7aKL47KLS+XFqxStr5VVtFCQSOi5fNrAg1t/Nef3O2jC94mJYGTYq4uNPnbeHd1JboEgkfC65ZSBh0mnOm0HMIB8bPojGyQMPrsna16JnJIy3ZdO+vQLOFOFOg+OT0oUHViVXBUSXRQ31DvlRE+riZ+KIdShP6kqCGZlPhkAz1GLt7NymwEAHmEznqYLwGgn63DKpOmaKwB8w2GsxHl8nWvAmLqwFyWoxzmjfwz9ZtDdT0Zfh/5OpQ/VAAAAAElFTkSuQmCC") center center / 100% no-repeat;
					}
				}

				&:hover {
					box-shadow: 2px 4px 4px #ccc;
				}
			}
		}
	}

	.step-quantity {
		.step-box {
			display: block;
		}
		.confirmBtnWrap {
			display: none;
			margin-top: 23px;
		}

		&.mask {
			.confirmBtnWrap {
				display: flex;
				justify-content: center;
				align-items: center;
				.gs-quote-button {
					background-color: #d17448;
				}
			}
		}
	}
	.step-cards {
		.step-box {
			display: block;
			.step-item {
				padding: 0;
			}
			// .step-item{
			// 	background-color: rgba(209, 116, 72, 0.2);
			// 	border-radius: 4px;
			// 	padding: 10px;
			// 	display: flex;
			// 	align-items: center;
			// 	cursor: pointer;
			// 	.square{
			// 		width: 14px;
			// 		height: 14px;
			// 		border: 1px solid #959292;
			// 		background-color: #fff;
			// 		position: relative;
			// 	}
			// 	span{
			// 		margin-left: 10px;
			// 	}
			// 	&:not(:first-child){
			// 		margin-top: 10px;
			// 	}
			// 	&.active .square{
			// 		&::before{
			// 			content: '';
			// 			position: absolute;
			// 			width: 8px;
			// 			height: 4px;
			// 			top: 15%;
			// 			left: 50%;
			// 			border-left: 2px solid #d17448;
			// 			border-bottom: 2px solid #d17448;
			// 			left: 50%;
			// 			transform: rotate(-45deg)  translate(-50%, -50%);
			// 		}
			// 	}
			// }

			// .upload-box {
			// 	display: flex;
			// 	flex-direction: column;
			// 	justify-content: space-between;
			// 	align-items: center;
			// 	margin: 14px 0 11px 50px;
			// 	padding: 10px;
			// 	background: #ffffff;
			// 	border: 1px dashed #e9ecf0;
			// 	border-radius: 10px;
			// 	cursor: pointer;
			// 	transition: all 0.3s;

			// 	.el-checkbox__label {
			// 		font-size: 18px;
			// 	}

			// 	@media (any-hover: hover) {
			// 		&:hover {
			// 			border-color: #d17448;
			// 		}
			// 	}

			// 	.uploadList {
			// 		width: 100%;
			// 		margin-bottom: 12px;
			// 		text-align: center;

			// 		.uploadIcon {
			// 			margin-top: 11px;
			// 			font-size: 32px;
			// 			color: #9e9e9e;
			// 		}

			// 		.uploadItem {
			// 			display: flex;
			// 			justify-content: space-between;
			// 			align-items: center;
			// 			padding: 5px;
			// 			font-size: 14px;
			// 		}

			// 		.myIcon {
			// 			margin: 0 4px;
			// 		}
			// 	}

			// 	.upload-btn {
			// 		display: flex;
			// 		flex-direction: column;
			// 		justify-content: center;
			// 		align-items: center;

			// 		button {
			// 			width: 172px;
			// 			height: 40px;
			// 			margin-bottom: 10px;
			// 			background: #d17448;
			// 			border-radius: 10px;
			// 			border: none;
			// 			color: #fff;
			// 			font-weight: bold;
			// 		}

			// 		.el-checkbox__label {
			// 			font-size: 16px;
			// 		}
			// 	}

			// 	.tips {
			// 		font-size: 15px;
			// 		color: #b3b3b3;
			// 		text-align: center;
			// 	}
			// }
		}
	}

	.step-turnaround {
		.step-box {
			.step-subtitle {
				font-weight: bold;
			}
			.block-cont {
				.step-date {
					padding: 0;
					background-color: #fff;

					.step-title {
						display: none;
					}

					.step-item {
						margin-bottom: 12px;
					}

					.step-box {
						width: 100%;
						grid-template-columns: 1fr 1fr;
						@media screen and(max-width: 768px) {
							grid-template-columns: 1fr;
						}

						.top {
							margin-left: 20px;
							.customCircle1 {
								display: none;
							}
						}

						.bottom {
							margin-top: 0;
							font-size: 14px;
							color: #9e9e9e;
							margin-left: 20px;
						}

						.item-wrap {
							background-color: #fff;
							border-radius: 4px;
							border: 1px solid #dcdfe6;
							padding: 0 10px 0 0;
							&.active {
								border-color: #d17448;
								&::before {
									position: absolute;
									content: "";
									right: 0;
									top: 0;
									width: 15px;
									height: 15px;
									background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAACXBIWXMAAAsTAAALEwEAmpwYAAACDUlEQVRIic3WTUiUURTG8d/M+DV+DJaIfZjJVLpIKiSKPkCCFrbNlYugqIg2QlARtIhAMghaBC6KWrQJiaIQokUJkRBSVFjtLCJTo4wsqQyzeVuYZo5jJg70LM859/nfc++5L2+o82BtYG41iNs4gq7wHJtDDNtxH5XpAIypECfSCYCt6QbE0g3w/wNC4bDC6i2EQlPmMzCMrNmZRyypP6RwTY3c0gp9rWeTasJGH8a/m0cyLN1xVOGaGjDy5dOUdWG8SGWQSuHMbOW7jolVbQBvblzwrq0lJeDJ5GBmrMiKA83mr69NXpAdVb77uILKtQSBvtaz+u9cSb0ZtE0OLti2U05JmdK6BsU1dePxSE6e+J5G+ctWEwR6rzV73349pTlE9m9c3o0GEy76c9djuWWVsooWKqioFopkGOp7Kb6vSW5ZJUGg5+oZHzpuTms+BhhGHNVjweDHiI+d7aKL47KLS+XFqxStr5VVtFCQSOi5fNrAg1t/Nef3O2jC94mJYGTYq4uNPnbeHd1JboEgkfC65ZSBh0mnOm0HMIB8bPojGyQMPrsna16JnJIy3ZdO+vQLOFOFOg+OT0oUHViVXBUSXRQ31DvlRE+riZ+KIdShP6kqCGZlPhkAz1GLt7NymwEAHmEznqYLwGgn63DKpOmaKwB8w2GsxHl8nWvAmLqwFyWoxzmjfwz9ZtDdT0Zfh/5OpQ/VAAAAAElFTkSuQmCC") center center / 100% no-repeat;
								}
							}
							&:hover {
								box-shadow: 2px 4px 4px #dcdfe6;
							}
						}
					}

					.box-border {
						display: none;
					}
				}
				.el-textarea {
					.el-textarea__inner:focus {
						border-color: #d17448;
					}
				}
			}
		}
	}

	.step-back {
		.step-box {
			grid-template-columns: repeat(3, 1fr);

			@media screen and (max-width: 767px) {
				grid-template-columns: repeat(2, 1fr);
			}
		}
	}

	.step-size {
		.tips.type2 {
			top: 50%;
			transform: translateY(-50%);
			// right: 1.3333vw;
			left: auto;
			font-size: 12px;
			right: 0.2vw;
		}

		.step-box {
			grid-template-columns: 2fr 1.4fr;

			.step-size-title {
				margin-bottom: 23px;
				margin-top: -25px;
				font-size: 16px;
			}

			.step-size-leftArea {
				.size-area {
					align-self: center;
					margin-bottom: 18px;

					.size-item-wrap {
						display: grid;
						grid-template-columns: repeat(2, 1fr);
						row-gap: 14px;
						column-gap: 10px;
						// grid-template-rows: 40px;
						// flex-wrap: wrap;
						font-size: 16px;

						.size-item {
							border: 1px solid transparent;
							position: relative;
							display: flex;
							// justify-content: center;
							align-items: center;
							// width: 130px;
							height: 40px;
							// margin: 0 15px 15px 0;
							background: #f4f5f5;
							// border: 1px solid #e1e3e6;
							border-radius: 10px;
							padding: 0 4px;
							// padding-left: 20px;
							padding-left: 7%;
							cursor: pointer;
							transition: all 0.3s;

							.normal-text {
								transition: all 0.3s;
								margin-bottom: 0;
								text-align: center;
							}

							.textWrap {
								display: flex;
								align-items: center;

								p {
									margin-right: 10px;
								}
							}

							@media (any-hover: hover) {
								&:hover {
									box-shadow: 0 3px 4px 0 #cccccc;
									border-color: #d17448;

									.circle2 {
										border-color: #d17448;
										background: #d17448;

										&::after {
											background-color: #fff;
										}
									}
								}
							}

							.tip-icon {
								color: #d17448;
							}
						}

						// .size-item.onlyInquiry {
						//   width: 216px;
						// }

						.size-item.active {
							border-color: #d17448;

							.circle2 {
								border-color: #d17448;
								background: #d17448;

								&::after {
									background-color: #fff;
								}
							}
						}
					}
				}

				.shape-area {
					position: relative;
					background-color: #fff;

					.shape-item-wrap {
						display: grid;
						grid-template-columns: repeat(5, 1fr);
						column-gap: 10px;
						justify-content: space-between;
						margin: 10px 0 0;

						.shape-item {
							position: relative;
							display: flex;
							flex-direction: column;
							justify-content: center;
							align-items: center;
							height: 85px;
							border: 1px solid #e6e6e6;
							border-radius: 10px;
							padding: 8px 0;
							cursor: pointer;
							@media (any-hover: hover) {
								&:hover {
									border-color: #d17448;

									.circle {
										border-color: #d17448;

										.inner-circle {
											border-color: #d17448;

											&::after {
												background-color: #d17448;
											}
										}
									}
								}
							}

							.shapeIcon {
								width: 48px;
								height: 32px;
								color: #d9d9d9;
							}

							.imgWrap {
								display: flex;
								justify-content: center;
								align-items: center;
							}

							span {
								margin-top: 4px;
								white-space: nowrap;
								font-size: 16px;
								text-transform: capitalize;
							}

							.circle {
								background: #ffffff;
							}
						}

						.shape-item.active {
							position: relative;
							border-color: #d17448;

							.shapeIcon {
								color: #d17448;
							}

							.circle {
								border-color: #d17448;

								.inner-circle {
									border-color: #d17448;

									&::after {
										background-color: #d17448;
									}
								}
							}
						}
					}
				}
			}

			.step-size-rightArea {
				display: flex;
				flex-direction: column;
				position: relative;
				padding: 20px;
				//background-color: #f6f6f6;
				@media screen and (max-width: 767px) {
					background-color: white;
					padding: 0;
				}

				&.forMB {
					display: none;
					@media screen and (max-width: 767px) {
						display: block;
					}
				}

				border-radius: 10px;

				&::before {
					position: absolute;
					left: -20px;
					top: 20px;
					content: "";
					width: 0;
					height: 0;
					border-width: 20px 0 20px 20px;
					border-style: solid;
					border-color: transparent transparent #f6f6f6;
					display: none;
					@media screen and (max-width: 767px) {
						display: none;
					}
				}

				.textWrap {
					text-align: left;

					.normal-text {
						margin-bottom: 8px;
						font-size: 18px;
						color: #333333;
						transition: all 0.3s;
						@media screen and (max-width: 767px) {
							margin-bottom: 0;
							font-size: 12px;
						}
					}

					.gray-text {
						margin-bottom: 12px;
						font-size: 14px;
						color: #9ca1a6;
					}
				}

				.shape-img {
					flex: 1;
					display: flex;
					justify-content: center;
					align-items: center;
					@media screen and (max-width: 767px) {
						margin-bottom: -20px;
					}

					img {
						height: 300px;
						object-fit: contain;
					}
				}
			}
		}
	}

	.step-metal {
		.step-box {
			grid-template-columns: repeat(3, 1fr);
			column-gap: 20px;
			row-gap: 20px;

			.item-wrap {
				.item {
					position: relative;
					display: flex;
					justify-content: center;
					align-items: center;
					// height: 118px;

					.imgWrap {
						img {
							object-fit: contain;
						}
					}
				}

				.circle {
					display: none;
				}

				.textWrap {
					margin-top: 5px;
					> div:last-of-type {
						> div > p {
							flex-direction: column;
						}
					}
				}
				@media screen and (max-width: 767px) {
					.textWrap {
						margin-top: 5px;
						> div:last-of-type {
							.PriceText {
								> .normal-text {
									display: flex;
									flex-direction: column;
								}
							}
						}
					}
				}
				//
			}
		}
	}

	.step-packing {
		.step-box {
			grid-template-columns: repeat(5, 1fr);
			column-gap: 20px;

			.item-wrap {
				.item {
					// height: 217px;
					height: 100%;

					.textWrap {
						flex-direction: column;
						justify-content: flex-start;
						height: 100%;
					}

					.imgWrap {
						height: auto;

						img {
							object-fit: cover !important;
						}
					}

					.normal-text {
						display: flex;
						flex-direction: column;
					}
				}

				.circle {
					display: none;
				}
			}
		}
	}

	@media screen and (min-width: 768px) and (max-width: 1499px) {
		.step-size {
			.step-box {
				.step-size-title {
					margin-bottom: 17px;
				}

				.step-size-leftArea {
					.size-area {
						.size-item-wrap {
							.size-item {
								// width: 120px;
								// margin: 0 10px 15px 0;
							}

							.size-item.onlyInquiry {
								// width: 196px;
							}
						}
					}
				}

				.step-size-rightArea {
					margin-left: 20px;
				}
			}
		}

		.step-metal {
			.step-box {
				column-gap: 10px;
				row-gap: 25px;

				.item-wrap {
					.item {
						height: 100px;
					}

					.textWrap {
						margin-top: 12px;
					}
				}
			}
		}

		.step-packing {
			.step-box {
				column-gap: 10px;

				.item-wrap {
					.item {
						// height: 188px;
					}
				}
			}
		}
	}

	@media screen and (max-width: 767px) {
		.step-size {
			.step-box {
				display: block;

				.step-size-title {
					margin-bottom: 8.5px;
					margin-top: -10px;
					font-size: 12px;
				}

				.step-size-leftArea {
					.size-area {
						background: #ffffff;
						border: 1px solid #d6d6d6;
						border-radius: 5px;
						padding: 12px 5px;
						@media screen and (max-width: 767px) {
							border: none;
							padding: 0;
						}

						.size-item-wrap {
							// display: flex;
							grid-template-columns: repeat(2, 1fr);
							row-gap: 0px;

							.size-item {
								justify-content: flex-start;
								flex-basis: calc(33.3% - 4px);
								height: 30px;
								margin: 0 2px 5px;
								font-size: 12px;
								background: #f4f5f5;
								border-radius: 5px;
								border-color: transparent;
								padding: 0 10px;

								p.normal-text {
									font-size: 12px;
								}
							}

							.size-item.onlyInquiry {
								flex-basis: 200px;
							}
						}
					}

					.shape-area {
						background: #ffffff;
						border: 1px solid #d6d6d6;
						border-radius: 5px;
						padding: 12px 5px;
						padding-bottom: 20px;

						.shape-item-wrap {
							grid-template-columns: repeat(3, 1fr);
							column-gap: 6.5px;
							row-gap: 15px;

							.shape-item {
								height: 66px;
								border-color: transparent;
								background: #f4f5f5;
								border-radius: 5px;

								.shapeIcon {
									width: 33px;
									height: 24px;
								}

								span {
									font-size: 12px;
								}

								.circle {
									background-color: #f4f5f5;
								}
							}
						}
					}
				}

				.step-size-rightArea {
					display: none;

					&.forMB {
						display: none;
						@media screen and (max-width: 767px) {
							display: block;
						}
					}
				}
			}
		}

		.step-metal {
			.step-box {
				grid-template-columns: repeat(3, 1fr);
				column-gap: 4px;
				row-gap: 10px;
				height: auto;

				.item-wrap {
					.item {
						height: 67px;

						&::after {
							height: 20px;
						}

						.imgWrap img {
							border-radius: 5px;
						}
					}

					.circle {
						display: block;
					}

					.textWrap {
						margin-top: 15px;
						text-align: center;
					}

					.circle2 {
						display: none;
					}
				}
			}
		}

		.step-packing {
			.step-box {
				grid-template-columns: repeat(2, 1fr);
				column-gap: 5px;
				row-gap: 13px;
				height: auto;

				.item-wrap {
					position: relative;

					.item {
						// height: 115px;
						padding: 0;
					}

					.textWrap {
						margin-top: 5px;

						.normal-text {
							text-align: center;
						}

						.tip-text {
							text-align: center;
						}
					}
				}
			}
		}

		.step-size .step-title {
			margin-bottom: 15px;
		}
	}

	.otoWrap {
		font-size: 16px;
		text-align: center;
		padding: 27px;

		h3 {
			font-size: 36px;
			font-weight: 700;
			margin-top: 10px;
			margin-bottom: 15px;
			line-height: normal;
		}

		.box {
			padding: 27px;
			background: #eff2f6;
			border-radius: 10px;
			margin-top: 33px;

			.t1 {
				font-size: 18px;
				font-weight: 700;
			}

			button {
				margin-top: 20px;
				width: 266px;
				height: 45px;
				background-color: #1a73e8;
				border-color: #1a73e8;
				font-size: 16px;
				@media (any-hover: hover) {
					&:hover {
						opacity: 0.8;
					}
				}
			}
		}
	}

	.rounded-circle {
		border-radius: 50%;
	}
}

.upload-box.dropActive {
	border-color: #d17448 !important;
}
//
// 隐藏组件里的step文字
::v-deep {
	.step-upload,
	.step-qty,
	.step-date {
		.step-title {
			> span:first-of-type,
			> span:last-of-type {
				display: none;
			}
		}
		button:focus {
			outline: none;
		}
	}
	.step-date {
		.step-box {
			grid-template-columns: repeat(2, 1fr);
		}
	}
}
//

.myswiper1 {
	.swiper-wrapper {
		justify-content: center;
		margin-top: 10px;
		margin-left: 10px;
	}
	.swiper-slide {
		display: flex;
		justify-content: center;
		align-items: center;
		overflow: hidden;
		width: 88px;
		height: 88px;
		border: 2px solid #eeeeee;
		border-radius: 10px;
		cursor: pointer;

		&.swiper-slide-thumb-active {
			border: 2px solid #d17448 !important;
		}
	}

	&.isEdit {
		.swiper-slide.swiper-slide-thumb-active {
			border: 2px solid #eeeeee;
		}
	}

	img {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}

	.playBtn {
		display: flex;
		justify-content: center;
		align-items: center;
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		width: 30px;
		height: 30px;
		background-color: rgba(0, 0, 0, 0.7);
		border-radius: 50%;

		svg {
			fill: #ffffff;
		}
	}
}

.swiper.myswiper2 {
	--swiper-navigation-color: #ffffff;
	/* 单独设置按钮颜色 */
	--swiper-navigation-size: 20px;

	/* 设置按钮大小 */
	.swiper-button-next,
	.swiper-button-prev {
		width: 46px;
		height: 46px;
		background: #000000;
		opacity: 0.4;
		border-radius: 50%;
		transform: translateY(-50%);
		margin-top: 0;
	}
}

.myswiper2 {
	// height: 100%;
	height: 500px;

	.zoom {
		position: absolute;
		top: 0;
		right: 0;
		cursor: pointer;
		z-index: 100;

		b {
			font-size: 22px;
			margin-right: 4px;
		}

		&:hover {
			b {
				color: #d17448;
			}
		}
	}

	.swiper-slide {
		display: flex;
		justify-content: center;
		align-items: center;

		.smallImg {
			position: relative;
			width: 500px;
			height: 100%;
			text-align: center;
		}
	}

	img {
		max-width: 100%;
		max-height: 100%;
		object-fit: contain;
	}
}
</style>