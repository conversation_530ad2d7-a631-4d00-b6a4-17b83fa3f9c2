<template>
	<Pic
		v-if="isPic"
		:src="coreData.value"
		:alt="coreData.alt"
		:pointer="data.event && modal.linkArea === 'img'"
		:style="wrapperStyle"
		@click="setModalType(coreData, modal[dataSource], 'img', coreData, data.event)"
	/>

	<div
		v-else-if="isYoutubeVideo"
		class="video-box"
		:pointer="data.event"
		:style="wrapperStyle"
		@click="setModalType(coreData, modal[dataSource], 'video', { type: 'click' })"
	>
		<YouTubeVideo v-bind="coreData" :style="videoStyle" />
	</div>

	<div
		v-else
		class="video-box"
		:pointer="data.event"
		:style="wrapperStyle"
		@mouseenter="setModalType(coreData, null, 'video', { type: 'enter' })"
		@mouseleave="setModalType(coreData, null, 'video', { type: 'leave' })"
		@click="setModalType(coreData, modal[dataSource], 'video', { type: 'click' })"
	>
		<video
			:src="coreData.value"
			:title="coreData.alt"
			loop
			muted
			playsinline
			controls
			ref="videoRef"
			:poster="coreData.poster"
			:preload="coreData.poster ? 'none' : 'metadata'"
			:style="videoStyle"
		></video>
		<b
			class="icon-jxsht-3d-dbf"
			v-if="modal.manualPlay || modal.mousePlay"
			v-show="!data.hidePlay"
			:style="modal.manualBtnStyle"
			@click.stop="setModalType(coreData, modal[dataSource], 'video', { type: 'click' })"
		></b>
		<b
			class="icon-a-tgsc-add"
			v-if="modal.manualPlay"
			:style="modal.zoomBtnStyle"
			@click.stop="setModalType(coreData, null, 'video', { type: 'zoom' })"
		></b>
	</div>
</template>

<script>
import { isImageType, isVideoType } from "@/utils/utils";

export default {
	name: "PicVideo",
	props: {
		/**
		 * 模板数据
		 */
		modal: { type: Object, default: () => ({}) },

		/**
		 * 模板 list 单项
		 * @example
		 * 		{
		 * 		 	"img": {
		 * 		 	  "value": "",
		 * 		 	  "alt": "",
		 * 		 	}
		 * 		 	...
		 * 		}
		 *
		 * 		{
		 * 		 	"video": {
		 * 		 	  "value": "",
		 * 		 	  "alt": "",
		 * 		 	}
		 * 		 	...
		 * 		}
		 */
		data: { type: Object, default: () => ({}) },

		/**
		 * data 来源
		 */
		dataSource: { type: String, default: "list", validator: (value) => ["list", "outer"].includes(value) },

		picStyle: { type: Object },
		videoBoxStyle: { type: Object },
		videoStyle: { type: Object },
	},
	computed: {
		coreData() {
			return this.data.img || this.data.video || {};
		},
		isPic() {
			return isImageType(this.coreData.value) || (!isVideoType(this.coreData.value) && !this.isYoutubeVideo);
		},
		isYoutubeVideo() {
			return this.coreData.value?.startsWith("https://www.youtube.com");
		},
		wrapperStyle() {
			return this.isPic ? this.picStyle || this.videoBoxStyle : this.videoBoxStyle || this.picStyle;
		},
	},
	methods: {
		setModalType(...rest) {
			this.$emit("setModalType", ...rest);
		},
	},
	mounted() {
		if (!this.modal.manualPlay && !this.modal.mousePlay && this.$refs.videoRef) {
			const observer = new IntersectionObserver(
				(entries) => {
					entries.forEach((entry) => {
						if (entry.isIntersecting && entry.target.play) {
							entry.target.play();
						}
					});
				},
				{
					threshold: 0.5,
				},
			);

			observer.observe(this.$refs.videoRef);

			this.$once("hook:beforeDestroy", () => {
				observer.disconnect();
			});
		}
	},
};
</script>

<style lang="scss">
.clickOpenDialog .video-box {
	.icon-jxsht-3d-dbf {
		display: none;
	}

	.icon-a-tgsc-add::before {
		content: "";
	}

	.icon-a-tgsc-add {
		opacity: 0.8;
		font-size: 2em;
		text-align: center;
		transform: translate(-50%, -50%);
		left: 50%;
		top: 50%;
		width: 5.375rem;
		height: 5.375rem;
		background: url(https://static-oss.gs-souvenir.com/web/20241111/2046363451icon_play.png) no-repeat;
		background-size: 100%;
	}
}
</style>
