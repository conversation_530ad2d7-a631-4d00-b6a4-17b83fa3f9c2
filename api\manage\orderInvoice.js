import {request} from '@/utils/request'

// 获取发票基础信息
export function getInvoiceBasicInfo(ordersId,id) {
	return request({
		url: '/invoice/getBaseInfo?ordersId='+ ordersId + '&proId=' + id,
		method: 'get',
	})
}

// 获取发票信息
export function getInvoiceInfo(id) {
	return request({
		url: '/invoice/getInvoiceByOrdersId?ordersId=' + id,
		method: 'get',
	})
}

// 新增发票信息
export function addInvoice(data) {
	return request({
		url: '/invoice/addInvoice',
		method: 'post',
		data: data,
	})
}

// 修改发票信息
export function updateInvoice(data) {
	return request({
		url: '/invoice/updateByOrdersId',
		method: 'post',
		data: data,
	})
}

// 获取发票打印信息
export function getInvoicePrintInfo(id) {
	return request({
		url: '/retailer/invoice/getPrintInfoByOrdersId?ordersId=' + id,
		method: 'get',

	})
}

// 获取发票产品信息
export function getInvoiceOrderInfo(ordersId, proId) {
	return request({
		url: '/retailer/invoice/getOrderInfoForInv?ordersId=' + ordersId + '&proId=' + proId,
		method: 'get',
	})
}

// 分页查询产品意见
export function findPageByProId(data) {
	return request({
		url: '/quote/product/comments/findPageByProId',
		method: 'post',
		data: data,
	})
}
// 获取订单产品信息
export function getOrderInfoList(ordersId, proId) {
	return request({
		url:
			"/invoice/getOrderInfo?ordersId=" + ordersId + "&proId=" + proId,
		method: "get"
	});
}
