<template>
  <div class="mb-4 style" :class="stepData.styleName">
    <half-design-litter-title
      :index="stepData.id"
      :selectedValue="shape"
      :data-name="`${stepData.styleClass + '_' + stepData.id}`"
      style="margin: 4px 0"
      :showTitle="false"
      :stepTitle="stepData.minStepTitle"
      v-show="filterStepItem.length && stepData.attributeTitle"
      >{{ stepData.attributeTitle }}
    </half-design-litter-title>
	<slot name="stepText"></slot>
    <div class="step-content">
      <div class="step-wrap">
        <div
          class="step-item"
          :class="{
            active: index === selectIndex,
            onePens: noDisdountSelect && step.unitPercent != 0,
          }"
          v-for="(step, index) in filterStepItem"
          :key="index"
          @click="selectStep(step, index)"
        >
          <div class="paramName text-truncate">
            {{ step.valueName }}
            <span
              class="deliveryFeeText"
              v-if="priceInfo.deliveryFee && step.unitPercent !== 0"
            >
              {{ langSemiCustom.add }}<CCYRate :price="priceInfo.deliveryFee"> </CCYRate>
            </span>
            <span v-else-if="step.priceType == 6 && step.weightPrice">
              <span>- {{ langSemiCustom.add }}</span
              ><CCYRate :price="step.weightPrice"></CCYRate>
            </span>
          </div>
          <div class="tip">
            {{ step.remark }}
          </div>
          <half-design-check-icon
            class="absolute-top-right2 check-icon"
          ></half-design-check-icon>
        </div>
      </div>
    </div>
    <div class="errorTip">
      <v-alert dense outlined type="error">
        {{ langSemiCustom.errorTip }}
      </v-alert>
    </div>
  </div>
</template>
<script>
import { halfDetailDiscount } from "@/mixins/halfDetailDiscount";
import { halfCalculate } from "@/api/web";
export default {
  mixins: [halfDetailDiscount],
  inject: ["getProductInfo", "getPriceInfo", "getNeedJudgeSmallWeight"],
  props: {
    stepData: {
      type: Object,
      default: {},
    },
  },
  data() {
    return {
      selectIndex: -1,
      selectItem: null,
    };
  },
  watch: {},
  computed: {
    langSemiCustom() {
      return this.$store.getters.lang?.semiCustom;
    },
    productInfo() {
      return this.getProductInfo();
    },
    priceInfo() {
      return this.getPriceInfo();
    },
    shape() {
      return this.selectItem?.valueName;
    },
    lang() {
      return this.$store.getters.lang.quote || {};
    },
  },
  methods: {
    selectStep(item, index) {
      this.selectIndex = index;
      this.selectItem = item;
      this.$emit("selectStep", {
        type: this.stepData.attributeFlag,
        data: item,
        parent: this.stepData,
        id: this.stepData.id,
      });
    },
    selectDefault() {
      if (this.filterStepItem.length) {
        this.selectStep(this.filterStepItem[0], 0);
      }
    },
    async getPriceFn(data) {
      let { data: result } = await halfCalculate(data);
      return result.deliveryFee;
    },
  },
  mounted() {
    this.$Bus.$on("selectDefaultDiscountStep", this.selectDefault);
  },
  beforeDestroy() {
    this.$Bus.$off("selectDefaultDiscountStep", this.selectDefault);
  },
};
</script>
<style scoped lang="scss">
@import "~assets/css/half.scss";

.hoursText {
  color: rgb(135 132 132);
  font-size: 16px;
  margin-bottom: 15px;

  @media screen and (max-width: 767px) {
    font-size: 14px;
  }
}

.style .step-content {
  display: grid;
  grid-template-columns: 1fr;
  grid-gap: 10px;

  .step-wrap {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-gap: 10px;

    .step-item {
      position: relative;
      // overflow: hidden;
      display: flex;
      flex-direction: column;
      min-width: 0;
      @include radius-response;
      border: 1px solid $border-color;
      padding: 2px;
      cursor: pointer;
      overflow: hidden;
      //background-color: #f6f6f6;

      &.onePens {
        pointer-events: none;
        background: #f5f5f5;
      }

      @media (any-hover: hover) {
        &:hover {
          padding: 1px;
          border-width: 2px;
          border-color: $color-primary;
        }
      }

      &.active {
        padding: 1px;
        border-width: 2px;
        border-color: $color-primary;

        .check-icon {
          display: flex;
        }
      }

      .check-icon {
        display: none;
      }

      .paramName {
        padding: 5px 10px;
        background-color: $background-color;
        text-align: left;
        font-weight: 600;
      }

      .tip {
        @include flex-center;
        justify-content: flex-start;
        flex: 1;
        word-break: break-word;
        padding: 10px;
        font-size: 14px;
      }
    }
  }

  @include respond-to(mb) {
    grid-template-columns: 1fr;
    grid-gap: 5px;

    .step-wrap {
      grid-template-columns: repeat(2, 1fr);
      gap: 8px;

      .step-item {
        padding: 0;

        .tip {
          font-size: 12px;
        }
      }
    }
  }
}

.style1 .step-content {
  .step-wrap {
    grid-template-columns: repeat(2, 1fr);

    .step-item {
      background: #f4f5f5;
      border-color: transparent;

      .paramName {
        padding: 10px 10px 0;
        background-color: transparent;

        .deliveryFeeText {
          color: $color-primary;
        }
      }

      .tip {
        padding: 5px 10px 10px;
      }
    }

    @include respond-to(mb) {
      grid-template-columns: repeat(2, 1fr);
    }
  }
}

.quantityBox {
  display: none !important;
}
</style>
