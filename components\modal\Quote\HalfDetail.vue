<!--
介绍: 半定制详情页面与列表页面弹窗模板
buildWeb配置
modal:"modalQuoteHalfDetail", //此组件名称
name: "/pens", //页面路由
componentName: "modalHalfList4" //动态组件 默认为半定制详情页面-modalHalfDetail
-->
<template>
	<div class="half-box">
		<b pointer class="icon-guanbi" @click="$store.commit('setMask', false)"></b>
		<v-app>
			<template v-if="!loadData">
				<div style="height: 100%" v-show="tab === 1">
                    <component :is="componentName" v-if="listName" class="custom-scrollbar" :halfCateDTO="halfCateDTO" :cateId="cateId" :parentCateId="parentCateId" :isStockPage="isStockPage" :data="componentModalData" :name="listName" :selectLabelData="selectLabelData" @changeCategory="changeCategory" @toDetail="toDetail"></component>
				</div>
				<div style="height: 100%" v-show="tab === 2">
					<modalHalfDetail v-if="detailName" :key="componentKey" class="custom-scrollbar" :isStockPage="isStockPage" :data="componentModalData" :name="detailName" :hideBack="hideBack" @back="back"></modalHalfDetail>
				</div>
			</template>
			<div v-else class="loadWrap">
				<Loading></Loading>
			</div>
		</v-app>
	</div>
</template>

<script>
import { getPageData } from "@/api/web";
import ModalHalfDetail from "@/components/modal/Half/Detail/index.vue";
import ModalHalfList4 from "@/components/modal/Half/List4.vue";
import ModalHalfList6 from "@/components/modal/Half/List6.vue";
import Loading from "@/components/Loading.vue";

export default {
	components: { Loading, ModalHalfDetail, ModalHalfList4,ModalHalfList6 },
	props: {
		data: {
			type: Object,
			default: {},
		},
	},
	data() {
		return {
            hideBack: false,
			loadData: true,
			componentName: "",
			componentModalData: {},
			halfCateDTO: "",
			cateId: "",
			parentCateId: "",
			isStockPage: 0,
			listName: "",
			selectLabelData:{},
			detailName: "",
			tab: 0,
			componentKey: 0,
		};
	},
	computed: {
		device() {
			return this.$store.state.device;
		},
	},
	methods: {
		async back(name) {
			//如果列表没有渲染，获取数据渲染列表
			if (!this.listName) {
				await this.getListData(name);
			}
			this.tab = 1;
		},
		toDetail(url) {
			this.detailName = url;
			this.tab = 2;
			//触发组件重新渲染
			this.componentKey += 1;
		},
		async changeCategory(path) {
			this.loadData = true;
            this.detailName = "";
			await this.getListData(path);
			this.loadData = false;
		},
		async getListData(path) {
			// 加载半定制列表弹窗时需要提前获取的数据
			let resData = await getPageData(path);
			let data = resData.data,
				pcItem = [],
				mbItem = [];
			//页面数据处理
			if (data && data.pageRowList) {
				data.pageRowList
					.filter((item) => !item.isHidden)
					.map((item) => {
						item.sampleData && pcItem.push(JSON.parse(item.sampleData));
						item.mbSampleData && mbItem.push(JSON.parse(item.mbSampleData));
					});
			}
			let componentModalData = this.device === "mb" ? this.$store.getters.deepMerge(pcItem, mbItem) : pcItem;
            //获取半定制模板数据
            componentModalData = componentModalData.filter(item=>item.name.indexOf("HalfList")>-1);
			this.componentModalData = componentModalData[0];
			this.halfCateDTO = data.halfCateDTO;
			this.cateId = data.cateId;
			this.parentCateId = data.parentCateId;
			this.isStockPage = data.isStockPage;
			this.listName = path;
		},
	},
	async mounted() {
		this.componentName = this.data.componentName;
		this.selectLabelData.parentId = this.data.parentId;
		this.selectLabelData.paramId = this.data.paramId;
		if (this.data.componentName === "modalHalfList4" || this.data.componentName==="modalHalfList6") {
			await this.getListData(this.data.name);
			this.tab = 1;
		} else {
            this.hideBack = true;
			this.tab = 2;
			this.detailName = this.data.name;
		}
        this.loadData = false;
	},
};
</script>

<style lang="scss" scoped>
@include respond-to(mb){
    .custom-scrollbar::-webkit-scrollbar {
        /*滚动条整体样式*/
        width: 1px;

        /*高宽分别对应横竖滚动条的尺寸*/
        height: 1px;
    }
}
#app{
    border-radius: 10px;
    overflow: hidden;
    width: 100%;
    height: 100%;
    @include respond-to(mb){
        border-top-left-radius: 10px;
        border-top-right-radius: 10px;
    }
}
.loadWrap {
	display: flex !important;
	justify-content: center;
	align-items: center;
	height: 95vh;
}

.half-box {
	position: relative;

	::v-deep .v-application--wrap {
		min-height: auto;

        @include respond-to(mb){
            height: 100%;
        }
	}

	.custom-scrollbar {
        height: 100%;
        overflow: auto;
	}
}
</style>
