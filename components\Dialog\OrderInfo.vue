<template>
	<v-dialog
		:value="orderInfoDialog"
		@input="$emit('update:orderInfoDialog',false)"
		width="690"
	>
		<v-card class="orderInfoCard" v-if="orderDetailInfo">
			<div class="top">
				<div class="d-flex align-center">
					<span class="mr-2" style="font-size: 16px;">Order ID: </span>
					<v-autocomplete
						v-model="selectedOrder"
						:items="selectedOrderList"
						hide-details
						dense
						rounded
						solo
						flat
						outlined
						item-value="id"
						item-text="oid"
						label="order id"
						@change="getOrderDetailInfo"
					>
					</v-autocomplete>
				</div>
				<div>
					<nuxt-link :to="'/manage/orders/'+orderDetailInfo.id">View More Details >></nuxt-link>
				</div>
			</div>
			<div class="content">
				<div class="left">
					<div class="mb-3">
						<strong>Order Information</strong>
					</div>
					<div>
						<div class="d-flex mb-3">
							<div class="label">
								Products:
							</div>
							<div class="con">
								{{ orderDetailInfo.productName }}
							</div>
						</div>
						<div class="d-flex mb-3">
							<div class="label">
								Quantity:
							</div>
							<div class="con">
								10
							</div>
						</div>
						<div class="d-flex mb-3">
							<div class="label">
								Order Time:
							</div>
							<div class="con">
								2022-07-12 22:33:33
							</div>
						</div>
						<div class="d-flex mb-3">
							<div class="label">
								Order Status:
							</div>
							<div class="con">
								Shipped
							</div>
						</div>
						<div class="d-flex mb-3">
							<div class="label">
								Payment Status:
							</div>
							<div class="con">
								Received Payment
							</div>
						</div>
						<div class="d-flex mb-3">
							<div class="label">
								Unit Price:
							</div>
							<div class="con">
								$2.22
							</div>
						</div>
						<div class="d-flex mb-3">
							<div class="label">
								Mode Free:
							</div>
							<div class="con">
								450
							</div>
						</div>
						<div class="d-flex mb-3">
							<div class="label">
								Tax Price:
							</div>
							<div class="con">
								$60
							</div>
						</div>
						<div class="d-flex mb-3">
							<div class="label">
								Subtotal:
							</div>
							<div class="con">
								$2800
							</div>
						</div>
						<div class="d-flex mb-3">
							<div class="label">
								User Discount:
							</div>
							<div class="con">
								(30% off for delivery Disc. for Unit price, 10% off for Extra Disc.)
							</div>
						</div>
						<div class="d-flex mb-3">
							<div class="label">
								Balance Due:
							</div>
							<div class="con">
								$45 (10% off for Extra Disc.)
							</div>
						</div>
					</div>
					<v-divider></v-divider>
					<div class="d-flex mt-3 align-center">
						<div class="label" style="font-size:14px;">
							Order Total:
						</div>
						<div class="con" style="font-size:18px;color:#EA4335;font-weight: 700">
							$1890
						</div>
					</div>
				</div>
				<div class="right">
					<div class="part" v-for="item in 3">
						<div class="mb-3">
							<strong>Delivery Address</strong>
						</div>
						<div>
							<div class="d-flex mb-1">
								<div class="label">
									Name:
								</div>
								<div class="con">
									Anna Kurnikova
								</div>
							</div>
							<div class="d-flex mb-1">
								<div class="label">
									Phone:
								</div>
								<div class="con">
									+33-5449031647-535
								</div>
							</div>
							<div class="d-flex">
								<div class="label">
									Address:
								</div>
								<div class="con">
									23333 Los Angeles ,Alabama, 9,
									23333 Los Angeles ,Alabama, 9
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</v-card>
	</v-dialog>
</template>

<script>
export default {
	props:['orderInfoDialog']
}
</script>

<style scoped lang="scss">
.orderInfoCard {
	font-size: 12px;

	.top {
		display: flex;
		height: 74px;
		padding: 0 20px;
		border-bottom: 1px solid #ccc;
		justify-content: space-between;
		align-items: center;

		a {
			font-size: 14px;
		}
	}

	.content {
		display: flex;
		padding: 20px 25px;

		.left {
			flex: 1;

			.label {
				flex-shrink: 0;
				width: 88px;
				margin-right: 36px;
			}
		}

		.right {
			flex: 1;
			margin-left: 16px;

			.part {
				margin-bottom: 10px;
				padding: 20px;
				background: #FAFAFA;
				border-radius: 6px;

				.label {
					flex-shrink: 0;
					width: 50px;
					margin-right: 10px;
				}

				.con {
					color: #999999;
				}
			}
		}
	}
}
</style>
