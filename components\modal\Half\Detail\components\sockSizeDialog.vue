<template>
	<div class="sockSizeBox">
		<BaseDialog v-model="showDialog" class="sockSizeDialog" :width="!isMobile ? '900px' : '90%'" :model="false">
			<div slot="closeIcon" @click="dialogClickSelf">
				<b class="icon-guanbi"></b>
			</div>
			<div class="size-guide">
				<div class="sizeTipHeader">
					{{ langSemiCustom.size }} & {{ langSemiCustom.fit }}
				</div>
				<div class="size-ranges">
					<div class="size-range" v-for="range in sizeRanges" :key="range.label">
						<span class="labelText">
							{{ range.label }}:
						</span>{{ range.value }}
					</div>
				</div>
				<div class="size-tip">
					<div class="sizeBox">
						{{ langSemiCustom.sockSizeTip }}
					</div>
					<div class="sizeTip">
						{{ langSemiCustom.sockSizeTipText }}
					</div>
				</div>
				<div class="size-tables">
					<!-- 男士尺码表 -->
					<div class="size-table men-table">
						<div class="table-header">{{ tables.men?.title }}</div>
						<table>
							<thead>
								<tr>
									<th>{{ tables.men?.headers.size }}</th>
									<th v-for="header in tables.men?.headers.columns" :key="header">
										{{ header }}
									</th>
								</tr>
							</thead>
							<tbody>
								<tr v-for="row in tables.men?.rows" :key="row.size">
									<td>{{ row.size }}</td>
									<td v-for="(value, index) in row.values" :key="index">
										{{ value }}
									</td>
								</tr>
							</tbody>
						</table>
					</div>

					<!-- 女士尺码表 -->
					<div class="size-table women-table">
						<div class="table-header">{{ tables.women?.title }}</div>
						<table>
							<thead>
								<tr>
									<th>{{ tables.women?.headers.size }}</th>
									<th v-for="header in tables.women?.headers.columns" :key="header">
										{{ header }}
									</th>
								</tr>
							</thead>
							<tbody>
								<tr v-for="row in tables.women?.rows" :key="row.size">
									<td>{{ row.size }}</td>
									<td v-for="(value, index) in row.values" :key="index">
										{{ value }}
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
			</div>
		</BaseDialog>
	</div>
</template>

<script>
import BaseDialog from "@/components/Quote/BaseDialog";
export default {
	name: 'sockSizeBox',
	components: { BaseDialog },
	props: {
		showDialog: {
			type: Boolean,
			default: false
		}
	},
	data() {
		return {
			sizeRanges: [],
			tables: {
				men: {},
				women: {}
			}
		}
	},
	watch: {},
	computed: {
		isMobile() {
			return this.$store.state.device === "mb";
		},
		langSemiCustom() {
			return this.$store.getters.lang?.semiCustom;
		},
	},
	methods: {
		dialogClickSelf() {
			this.$emit('update:showDialog', false)
		}
	},
	created() {
		this.sizeRanges = [
			{ label: this.langSemiCustom.Small, value: '(W 4-6 / M 5-7)' },
			{ label: this.langSemiCustom.Medium, value: '(W 6-12 / M 6-11)' },
			{ label: this.langSemiCustom.Large, value: '(W 11-14 / M 12-15)' }
		]
		this.tables = {
			men: {
				title: this.langSemiCustom.Mens + " " + this.langSemiCustom.Socks,
				headers: {
					size: this.langSemiCustom.Socks + " " + this.langSemiCustom.size,
					columns: ['SM', 'M / L', 'XL']
				},
				rows: [
					{
						size: 'US - ' + this.langSemiCustom.Shoe + " " + this.langSemiCustom.size, values: [this.langSemiCustom.upTo + ' 8.5', '7 - 12.5', '10.5 - 15']
					},
					{
						size: 'UK - ' + this.langSemiCustom.Shoe + " " + this.langSemiCustom.size, values: [this.langSemiCustom.upTo + ' 8', '6.5 - 12', '10 - 14.5']
					},
					{ size: 'EU - ' + this.langSemiCustom.Shoe + " " + this.langSemiCustom.size, values: ['39 - 41', '40 - 46', '44 - 49'] }
				]
			},
			women: {
				title: this.langSemiCustom.Womens + " " + this.langSemiCustom.Socks,
				headers: {
					size: this.langSemiCustom.Socks + " " + this.langSemiCustom.size,
					columns: ['SM', 'M / L', 'XL']
				},
				rows: [
					{ size: 'US - ' + this.langSemiCustom.Shoe + " " + this.langSemiCustom.size, values: ['5 - 10.5', '8.5 - 14', '12+'] },
					{ size: 'UK - ' + this.langSemiCustom.Shoe + " " + this.langSemiCustom.size, values: ['3 - 8', '6 - 10.5', '9.5+'] },
					{ size: 'EU - ' + this.langSemiCustom.Shoe + " " + this.langSemiCustom.size, values: ['35 - 42', '39 - 47', '45+'] }
				]
			}
		}
	},
	mounted() { },
}
</script>

<style scoped lang="scss">
@import "~assets/css/half.scss";

.sockSizeDialog {
	::v-deep .base-dialog-model-con {
		border-radius: 0;
		padding: 40px;

		.icon-guanbi {
			top: 30px;
			right: 30px;
			background-color: transparent;
		}

		@include respond-to(mb) {
			padding: 20px 12px;

			.icon-guanbi {
				top: 16px;
				right: 10px;
			}
		}
	}
}

.size-guide {

	.sizeTipHeader {
		font-weight: bold;
		font-size: 18px;
	}

	.size-ranges {
		display: flex;
		justify-content: space-between;
		margin: 20px 0;
		padding-top: 20px;
		border-top: 1px solid #eee;
	}

	.size-range {
		text-align: center;
		font-size: 16px;
	}

	.size-tip {
		background: #F6F6F6;
		text-align: center;
		padding: 14px 0;
		display: flex;
		align-items: center;
		flex-direction: column;
		gap: 6px;
		margin-bottom: 6px;

		.sizeBox {
			font-size: 16px;
		}

		.sizeTip {
			font-size: 14px;
			color: #999;
		}
	}

	.size-tables {
		display: flex;
		gap: 6px;

		.size-table {
			flex: 1;
			overflow: hidden;
		}

		.table-header {
			padding: 12px;
			text-align: center;
			font-weight: 700;
			font-size: 16px;
			border: 1px solid #E2E2E2;
		}

		.men-table {
			.table-header {
				background: #DBE9FF;
			}

			th {
				background: #F2F7FF;
				font-weight: 700;
				border-top: none;
			}
		}

		.women-table {
			.table-header {
				background: #FFDBDB;
			}

			th {
				background: #FFF2F2;
				font-weight: 700;
				border-top: none;
			}
		}
	}

	@include respond-to(mb) {
		.sizeTipHeader {
			font-size: 16px;
		}

		.size-ranges {
			flex-direction: column;
			gap: 10px;
			margin: 10px 0;
			padding-top: 10px;
			border-bottom: none;
		}

		.size-range {
			font-size: 14px;
			display: flex;
			justify-content: flex-start;
			text-align: left;

			.labelText {
				color: #666;
				min-width: 70px;
				max-width: 100px;
				word-break: break-word;
				margin-right: 0.4em;
			}
		}

		.size-tip {
			padding: 10px 0;

			.sizeBox {
				font-weight: bold;
				font-size: 14px;
			}

			.sizeTip {
				font-size: 12px;
			}
		}

		.size-tables {
			flex-direction: column;
		}

		.size-table {
			width: 100%;
		}

		.table-header {
			font-size: 14px !important;
		}

		th,
		td {
			max-width: 120px;
			padding: 10px 6px;
			overflow: hidden;
			// text-overflow: ellipsis;
			// white-space: nowrap;
			font-size: 12px;
			border: none;
			border-bottom: 1px solid #eee;
		}
	}
}

table {
	width: 100%;
	border-collapse: collapse;
}

th,
td {
	padding: 12px;
	text-align: center;
	border: 1px solid #E2E2E2;
	font-size: 14px;
	border-left: none;
	border-right: none;

	&:first-child {
		border-left: 1px solid #E2E2E2;
	}

	&:last-child {
		border-right: 1px solid #E2E2E2;
	}
}
</style>
