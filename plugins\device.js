export default function (context) {
	// 默认设备类型为PC
	let deviceType = 'pc';

	if (process.client) {
		// 客户端设备检测（基于窗口宽度）
		const detectDeviceType = () => {
			const bodyWidth = document.body.clientWidth;

			if (bodyWidth <= 1000) return 'mb';      // 移动设备
			else if (bodyWidth <= 1400) return 'ipad'; // 平板设备
			else return 'pc';                        // 桌面设备
		};

		// 初始检测
		deviceType = detectDeviceType();
		context.store.commit('SetDeviceType', deviceType);

		// 添加防抖的resize事件监听器
		let resizeTimer;
		window.addEventListener('resize', () => {
			clearTimeout(resizeTimer);
			resizeTimer = setTimeout(() => {
				const newDeviceType = detectDeviceType();
				if (newDeviceType !== deviceType) {
					deviceType = newDeviceType;
					context.store.commit('SetDeviceType', deviceType);
				}
			}, 200); // 200ms防抖间隔
		});

	} else if (process.server) {
		// 服务端设备检测（基于User-Agent）
		const userAgent = context.req.headers['user-agent'];
		const isMobile = /Mobile|Android|iP(hone|od|ad)|BlackBerry|IEMobile|Silk/i.test(userAgent);
		deviceType = isMobile ? 'mb' : 'pc';
		context.store.commit('SetDeviceType', deviceType);
	}
}