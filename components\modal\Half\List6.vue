<!--
    用途: https://www.custommedals.co/us/custom-medals
    预览图: https://static-oss.gs-souvenir.com/web/quoteManage/20250225/企业微信截图_17404674495788_20250225psGhsn.png
-->
<template>
	<div class="modalHalfListBox List6" style="position: relative;">
		<div class="wrap modal-box" :class="{ isDialog: isDialog }"
			:style="[modal.style, isManage ? {} : { 'padding-top': '0' }]">
			<template v-if="isManage">
				<v-card height="300">
					<v-row justify="center" align="center" style="width: 100%; height: 100%; display: flex; margin: 0">
						<div class="rightHeader">
							<div v-for="(l, li) in modal.list" :key="li" :style="{ ...modal.cardBoxStyle, ...l.style }">
								<EditDiv :tagName="l.title.tagName || 'h1'" v-model:content="l.title.value"
									@click="setModalType(l.title, modal.list, 'text')" v-if="l.title?.value"
									:style="{ ...modal.titleStyle }" />
								<EditDiv :tagName="l.subTitle.tagName || 'p'" v-model:content="l.subTitle.value"
									@click="setModalType(l.subTitle, modal.list, 'text')"
									:style="{ ...modal.subTitleStyle }" v-if="l.subTitle?.value" />
								<EditDiv class="domTextStyle" :style="modal.textStyle"
									@click="setModalType(l.text, modal.list, 'text')" v-model:content="l.text.value"
									v-if="l.text?.value"></EditDiv>
							</div>
						</div>
					</v-row>
				</v-card>
			</template>
			<template v-else>
				<div class="bps_container">
					<div class="content" id="proContent" :class="{ noFilter: !showFilter }">
						<leftBar6 ref="leftBar6"
							:style="{ maxHeight: isMobile ? `${screenHeight}px` : screenHeight - headerHeight + 'px' }"
							:fatherCateName="fatherCateName" :cateId="cateId" :parentCateId="parentCateId"
							:parentOneChildCateId="parentOneChildCateId" :fristWatch="fristWatch"
							:isStockPage="isStockPage" :labelData="labelData" :loadLabel="loadLabel" :isCoins="isCoins"
							:colorItem="colorItem" :priceRange.sync="priceRange" :showPriceRange="showPriceRange"
							:totalResult="totalResult" :activeNames.sync="activeNames" @toggleFilter="toggleFilter"
							@delAllTag="delAllTag" @sortProduct="sortProduct" @delColor="delColor"
							@clickTitle="clickTitle" @toCate="toCate" @newToCate="newToCate" @updateFn="updateFn"
							@toggleColor="toggleColor" @changeRange="changeRange" @clearRange="clearRange"
							v-show="showFilter">
						</leftBar6>
						<div class="rightBar" style="width: 100%; min-width: 0">
							<div class="rightHeader">
								<div v-for="(l, li) in modal.list" :key="li"
									:style="{ ...modal.cardBoxStyle, ...l.style }">
									<EditDiv :tagName="l.title.tagName || 'h1'" v-model:content="l.title.value"
										@click="setModalType(l.title, modal.list, 'text')" v-if="l.title?.value"
										:style="{ ...modal.titleStyle }" />
									<EditDiv :tagName="l.subTitle.tagName || 'p'" v-model:content="l.subTitle.value"
										@click="setModalType(l.subTitle, modal.list, 'text')"
										:style="{ ...modal.subTitleStyle }" v-if="l.subTitle?.value" />
									<EditDiv :style="modal.textStyle" @click="setModalType(l.text, modal.list, 'text')"
										v-model:content="l.text.value" v-if="l.text?.value"></EditDiv>
								</div>
							</div>
							<div class="top-right sticky">
								<div class="filterSearch" :class="{ textSearch: !isMobile }">
									<label class="search-area">
										<div class="inputBox">
											<span @click="changeKeyword"><v-icon>mdi-magnify</v-icon></span>
											<input v-model="keyword" @input="changeKeyword" type="text"
												:placeholder="langSemiCustom.ProductKeywords" />
										</div>
									</label>
								</div>
								<div class="filter-area">
									<div class="filter1" @click="toggleFilter">
										<b class="icon-a-HideFilters"></b>
										<span>
											<span v-show="!isMobile">{{ showFilter ? `${langSemiCustom.hide}` :
												`${langSemiCustom.show}` }}
											</span>{{ langSemiCustom.filters }}
										</span>
									</div>
									<div class="filter2">
										<span class="t1">{{ langSemiCustom.sort }}:</span>
										<v-menu offset-y>
											<template v-slot:activator="{ on, attrs }">
												<v-btn text v-bind="attrs" v-on="on" :small="isMobile"
													style="padding: 0; font-weight: 400; font-size: 16px; color: #333333; letter-spacing: normal">
                                                    {{ sortList[sorts] }}
													<b class="icon-Down litterArrow" style="font-size: 12px"></b>
												</v-btn>
											</template>
                                            <v-list>
                                                <v-list-item v-for="(item,key) in sortList" :key="key" @click="sortProduct(key)">
                                                    <v-list-item-title>{{ item }} </v-list-item-title>
                                                </v-list-item>
                                            </v-list>
										</v-menu>
									</div>
								</div>
							</div>
							<div class="borderBox" id="borderBoxSticky"></div>
							<div class="selectLabel" id="selectLabel" v-if="!isLuggageSite">
								<div class="filter">
									<div class="top">
										<div class="cateitemBox">
											<div class="category-item cateTag" v-for="(citem, cindex) in cateList"
												:class="{ active: activeCateName(citem) }" :key="citem.id"
												v-show="!isStockPage && cindex < showMoreIndex"
												@click="changeCategory(citem)">
												<span>{{ citem.name }}</span>
											</div>
											<div class="viewMore" v-show="tagViewStatus">
												<span @click="viewMoreFn(999, 'more')" v-if="showMoreIndex === 10">{{
													langQuote.ViewMore }}<b class="icon-Down litterArrow"></b></span>
												<span @click="viewMoreFn(10, 'less')" v-if="showMoreIndex === 999">{{
													langQuote.ViewLess }}<b class="icon-Up litterArrow"></b></span>
											</div>
										</div>
										<div class="viewMore" v-show="!tagViewStatus">
											<span @click="viewMoreFn(999, 'more')" v-if="showMoreIndex === 10">{{
												langQuote.ViewMore }}<b class="icon-Down litterArrow"></b></span>
											<span @click="viewMoreFn(10, 'less')" v-if="showMoreIndex === 999">{{
												langQuote.ViewLess }}<b class="icon-Up litterArrow"></b></span>
										</div>
									</div>
								</div>
							</div>
							<div class="rightPart" id="productList">
								<noFind :keyword="keyword" :cateName="cateName" :parentCateId="parentCateId"
									:cateId="cateId" :fatherCateName="fatherCateName" v-if="showNoFind"
									:isDialog="isDialog"></noFind>
								<div class="rightContent">
									<div class="productWrap">
										<div v-for="(item, index) in productList" :key="item.id">
											<uploadBox style="width: 100%;height: 100%;" v-if="item.id < 0"
												:uploadPic.sync="uploadPic" @mergeImgFn="nowMergeImg"
												@cancelMergeImg="cancelMergeImg">
											</uploadBox>
											<product6 v-else-if="isMedals" ref="productGoods" class="productGoods"
												:needMerge="needMerge" :class="{ inserted_element: item.booth == 2 }"
												:parentCateId="parentCateId" :cateId="cateId" :halfCateDTO="halfCateDTO"
												:productData="item" :itemIndex="index" :isStockPage="isStockPage"
												:adBoxHeight="adBoxHeight" :productList="productList" :isDiv="true"
												@toDetail="toDetail" @changeColor="changeColor">
											</product6>
											<product7 v-else="isCoins" ref="productGoods" class="productGoods"
												:needMerge="needMerge" :class="{ inserted_element: item.booth == 2 }"
												:parentCateId="parentCateId" :cateId="cateId" :halfCateDTO="halfCateDTO"
												:productData="item" :itemIndex="index" :isStockPage="isStockPage"
												:adBoxHeight="adBoxHeight" :productList="productList" :isDiv="true"
												@toDetail="toDetail" @changeColor="changeColor">
											</product7>
										</div>
									</div>
								</div>
							</div>
							<div class="loadProgress" v-show="loadingProduct">
								<Loading></Loading>
							</div>
						</div>
					</div>
					<div class="loadBtnMainBox" v-show="!loadingProduct && totalResult > 0">
						<div class="loadMoreBtn">
							<div class="loadMoreBtnText">
								<span>
									{{ `${langSemiCustom.viewed} ${productList.length - usePosition - 1}
									${langSemiCustom.of}
									${totalResult}
									${langSemiCustom.products}` }}
								</span>
							</div>
							<v-progress-linear height="6" background-color="#fff" rounded
								:value="numPercent"></v-progress-linear>
							<div class="loadBtnBox" v-show="showLoadBtn">
								<span class="loadBtn" @click="loadMoreData">{{ langSemiCustom.loadMore }}</span>
							</div>
						</div>
					</div>
				</div>
			</template>
		</div>
	</div>
</template>
<script>
import { getAdvertList, getAppLabelAttributeList, getNewLabelAttributeList, getProductList, listAllCategoryByParentId } from "@/api/web";
import { debounce, generateUUID } from "@/utils/utils";
import noFind from "@/components/modal/Half/List/noFind.vue";
import product6 from "@/components/modal/Half/List/product6.vue";
import product7 from "@/components/modal/Half/List/product7.vue";
import leftBar6 from "@/components/modal/Half/List/leftBar6.vue";
import uploadBox from "@/components/modal/Half/List/uploadBox.vue";
import Loading from "@/components/Loading.vue";


export default {
	props: ["cateId", "data", "parentOneChildCateId", "parentCateId", "halfCateDTO", "isStockPage", "name"],
	components: {
		Loading,
		noFind,
		leftBar6,
		product6,
		product7,
		uploadBox
	},
	provide() {
		return {
			isDialog: this.isDialog,
		};
	},
	data() {
		return {
			attrs: {
				class: "mb-6",
				boilerplate: true,
				elevation: 2,
			},
			showMoreIndex: 10,
			last_scroll: 0,
			hide_on_load: false,
			debounceSearchProduct: null,
			debounceLabel: null,
			modal: {
				style: {},
				type: {},
				...this.data,
			},
			showPriceRange: false,
			priceRange: [-990000, 990000],
			halfName: "",
			goodList: [],
			keyword: "",
			sorts: 2,
			labelData: [],
			cateList: [],
			activeNames: [],
			showFilter: true,
			page: 1,
			pages: 0,
			pageSize: 20 * 3,
			totalResult: 0,
			productList: [],
			colorItem: "",
			priceKey: 0,
			quantityKey: 0,
			screenHeight: 0,
			screenWidth: 0,
			loadingProduct: true,
			cateName: "",
			isManage: false,
			attributeValueIds: [],
			loadLabel: false,
			adData: null,
			advert: [],
			adNum: 0,
			useAdNum: 0,
			doubleAd: 0,
			customKeyword: "",
			usePosition: 0,
			fatherCateName: "",
			headerHeight: 0,
			selectLabelHeight: 0,
			nowTimeStamp: "",
			cancelTokenSource: null,
			firstCate: false,
			fristWatch: false,
			tagViewStatus: false,
			adBoxHeight: "",
			uploadPic: [],
			intersectionObserver: null,
			needMerge: false,
		};
	},
	async fetch() {
		if (process.env.isManage) {
			this.isManage = true;
			return false;
		}
		this.page = parseInt(this.$route.query.page) || 1;
		await this.loadThreeData(true);
	},
	watch: {
		modal: {
			handler(val) {
				if (process.env.isManage) this.$emit("update:data", val);
			},
			immediate: true,
			deep: true,
		},
		productList(newVal) {
			if (!this.isMedals) return
			this.$nextTick(() => {
				if (newVal && newVal.length <= 0) return
				if (this.intersectionObserver) {
					let productGoods = document.querySelectorAll(".productGoods");
					if (productGoods) {
						[...productGoods].forEach((element) => {
							this.intersectionObserver.unobserve(element);
						});
					}
				}
				this.initIntersectionObserver()
			})
		}
	},
	computed: {
        sortList(){
            return{
                1:this.langSemiCustom.newest,
                2:this.langSemiCustom.recommended,
                3:this.langSemiCustom.bestSeller,
                4:this.langSemiCustom.popular
            }
        },
		isLuggageSite() {
			return this.proId === 475
		},
		isDialog() {
			return !!this.name;
		},
		langQuote() {
			return this.$store.getters.lang.quote || {};
		},
		langSemiCustom() {
			return this.$store.getters.lang?.semiCustom;
		},
		langDesign() {
			return this.$store.getters.lang?.design;
		},
		userId() {
			return this.$store.state.userInfo?.id;
		},
		isLogin() {
			return this.$store.getters.isLogin;
		},
		isMobile() {
			return this.$store.state.device === "mb";
		},
		numPercent() {
			return Math.round(((this.productList.length - this.usePosition) / this.totalResult) * 100);
		},
		showLoadBtn() {
			return !(this.page == this.pages);
		},
		proId() {
			return this.$store.state.proId;
		},
		showNoFind() {
			return (this.productList.length === 0 && !this.loadingProduct) || this.productList.length == 1 && this.productList[0].id == -1
		},
		isMedals() {
			return this.halfCateDTO.categoryType == 1
		},
		isCoins() {
			return this.halfCateDTO.categoryType == 2
		}
	},
	methods: {
		toDetail(url) {
			this.$emit("toDetail", url);
		},

		viewMoreFn(num, type) {
			this.showMoreIndex = num;
			let cateitemBox = document.querySelector(".cateitemBox");
			if (type == "more") {
				this.tagViewStatus = true;
				cateitemBox.style.maxHeight = "none";
			} else {
				this.tagViewStatus = false;
				cateitemBox.style.maxHeight = "34px";
			}
		},
		updateLabel(tagData) {
			this.loadLabel = true;
			this.getAppLabelAttributeList(tagData)
				.then((res) => {
					let labelData = res.data;
					labelData.forEach((item) => {
						if(item.isMore) item.moreText = true;
						if (item.isExpand) this.activeNames.push(item.id);
					});
					this.labelData = labelData;
				})
				.finally(() => {
					this.listScrollTop();
					this.loadLabel = false;
				});
		},
		async updateLabel2(tagData) {
			this.loadLabel = true;
			let newLabelData = await this.getNewLabelAttributeList();
			newLabelData = newLabelData.data
			let newLabelItem = null;
			if (newLabelData && newLabelData.childList && newLabelData.childList.length > 0) {
				// 创建新的 children 数组并修改其属性
				const updatedChildren = newLabelData.childList.map((item, index) => ({
					...item,
					nameEn: item.categoryAlias || item.name,
					attributeList: item.childList ? [{
						id: item.id,
						parentId: item.id,
						topId: newLabelData.id,
						valueName: this.langDesign.all,
						customRouting: item.customRouting,
						productCount: item.productCount,
						shopRouting: item.shopRouting,
					}].concat(item.childList.map((citem) => ({
						...citem,
						parentId: item.id,
						topId: newLabelData.id,
						valueName: citem.categoryAlias || citem.name,
					}))) : [{
						id: item.id,
						parentId: item.id,
						topId: newLabelData.id,
						valueName: this.langDesign.all,
						customRouting: item.customRouting,
						productCount: item.productCount,
						shopRouting: item.shopRouting,
					}],
				}));
				newLabelItem = {
					id: newLabelData.id,
					isExpand: true,
					nameEn: newLabelData.categoryAlias || newLabelData.name,
					attributeType: 'cateList',
					attributeList: updatedChildren,
				};
			}
			this.getAppLabelAttributeList(tagData)
				.then((res) => {
					let labelData = res.data;
					if (newLabelItem) labelData.unshift(newLabelItem);
					labelData.forEach((item) => {
						if(item.isMore) item.moreText = true;
						if (item.isExpand) this.activeNames.push(item.id);
					});
					this.activeNames = [...new Set([...this.activeNames])];
					this.labelData = labelData;
				})
				.finally(() => {
					this.listScrollTop();
					this.loadLabel = false;
				});
		},
		getAppLabelAttributeList(tagData = "") {
			return new Promise((resolve) => {
				getAppLabelAttributeList(
					{
						categoryId: this.parentCateId || this.cateId,
						childCategoryId: this.customKeyword ? "" : this.parentCateId ? this.cateId : null,
						isLabel: 1,
						attributeValueIds: this.getLabelAttributeValueIds(),
						keyWord: this.keyword,
					},
					tagData
				).then((res) => {
					resolve(res);
				});
			});
		},
		getNewLabelAttributeList(tagData = "") {
			return new Promise((resolve) => {
				getNewLabelAttributeList(
					{
						categoryId: this.parentCateId || this.cateId,
						childCategoryId: this.customKeyword ? "" : this.parentCateId ? this.cateId : null,
						keyWord: this.keyword,
						attributeValueIds: this.getLabelAttributeValueIds(),
					},
					tagData
				).then((res) => {
					resolve(res);
				});
			});
		},
		toCate() {
			if (this.halfCateDTO) {
				let path = this.isStockPage == "1" ? this.halfCateDTO.shopRouting : this.halfCateDTO.customRouting;
				if (this.isDialog) {
					this.$emit("changeCategory", path);
				} else {
					this.$router.push({
						path: path,
					});
				}
			}
		},
		newToCate(data) {
			let path = this.isStockPage == "1" ? data.shopRouting : data.customRouting;
			if (this.isDialog) {
				this.$emit("changeCategory", path);
			} else {
				this.$router.push({
					path: path
				});
			}
		},
		changeRange(tagData) {
			this.showPriceRange = true;
			//记录产品价格区间
			try {
				if (window.$nuxt.$store.state.advertConfig.googleConfig && window.gtag) {
					gtag("event", "select_content", {
						content_type: "producPrice",
						content_value: this.priceRange,
					});
				}
			} catch (error) { }
			this.updateFn(tagData);
		},
		clearRange(data, productTag) {
			this.priceRange = data;
			this.showPriceRange = false;
			this.updateFn(productTag);
		},
		updateFn(productTag) {
			this.useAdNum = 0;
			this.doubleAd = 0;
			this.usePosition = 0;
			this.debounceLabel(productTag);
			this.searchProduct();
		},
		delColor(tagData) {
			this.colorItem = "";
			this.updateFn(tagData);
		},
		delAllTag() {
			this.colorItem = "";
			this.page = 1;
			this.keyword = "";
			this.priceRange = [-990000, 990000];
			this.showPriceRange = false;
		},
		changeKeyword(val) {
			this.customKeyword = !!val;
			this.useAdNum = 0;
			this.doubleAd = 0;
			this.usePosition = 0;
			this.debounceSearchProduct();
			this.debounceLabel();
		},
		toggleColor(item, tagData) {
			if (this.colorItem.id === item.id) {
				this.colorItem = "";
			} else {
				this.colorItem = item;
			}
			this.updateFn(tagData);
		},
		formatUrlName(str) {
			return str.substring(1, str.length);
		},
		changeCategory(item) {
			let path = this.isStockPage == "1" ? item.shopRouting : item.customRouting;
			if (this.isDialog) {
				this.$emit("changeCategory", path);
			} else {
				this.$router.push({
					path: path,
				});
			}
		},
		getLabelAttributeValueIds() {
			let labelIds = this.$refs.leftBar6?.getLabelIds() || []
			if (this.colorItem) {
				//google记录选择的颜色
				try {
					if (window.$nuxt.$store.state.advertConfig.googleConfig && window.gtag) {
						gtag("event", "select_content", {
							content_type: "productColor",
							content_value: this.colorItem.valueName,
						});
					}
				} catch (error) { }
				labelIds.push({
					parentId: this.colorItem.fatherId,
					childIds: [this.colorItem.id],
				});
			}
			return labelIds;
		},
		getAttributeValueIds() {
			let str = this.$refs.leftBar6?.getAttributeValueStr() || ""
			if (this.colorItem) {
				str += this.colorItem.id;
			}
			str = str.replace(/^-*|-*$/g, "");
			return str;
		},
		getProduct(customPage) {
			return new Promise((resolve) => {
				getProductList({
					categoryId: this.parentCateId || this.cateId,
					childCategoryId: this.customKeyword ? "" : this.parentCateId ? this.cateId : null,
					page: customPage || this.page,
					pageSize: this.pageSize,
					keyWord: this.keyword,
					attributeValueIds: this.getAttributeValueIds(),
					userId: this.isLogin ? this.userId : null,
					sorts: this.sorts,
					priceStart: this.priceRange[0],
					priceEnd: this.priceRange[1],
					productType: this.isStockPage,
				})
					.then((res) => {
						this.totalResult = res.data.total;
						this.pages = res.data.pages;
						this.page = res.data.current;
						this.firstCate = false;
						resolve(res);
					})
					.catch((error) => {
						if (error.name === "AbortError") {
							console.log("Request canceled:", error.message);
						}
					});
			});
		},
		searchProduct() {
			this.loadingProduct = true;
			this.page = 1;
			this.getProduct()
				.then((res) => {
					this.productList = this.setDefaultShowImg(res.data.records);
					this.setAllProductData()
					this.insertUploadBox()
					//插入广告
					this.insertAdPosition();
				})
				.finally(() => {
					this.loadingProduct = false;
				});
		},
		setDefaultShowImg(list) {
			if (!list) {
				return;
			}
			if (this.isStockPage && this.$store.state.proTheme == "11") {
				list.forEach((item) => {
					item.showMergeImg = false;
					item.showImgSrc = item.sceneImg;
					item.selectedColorIndex = -1;
				});
				return list;
			}
			let colorItem = this.colorItem,
				colorId;
			if (colorItem) {
				colorId = colorItem.id;
			}
			list.forEach((item) => {
				item.showMergeImg = false;
				if (item.productParamList && item.productParamList.length > 0) {
					let productParamList = item.productParamList;
					let findColorIndex = productParamList.findIndex((citem) => citem.attributeValueId === colorId);
					if (findColorIndex > -1) {
						try {
							item.showImgSrc = JSON.parse(productParamList[findColorIndex].imgJson)[0]?.url;
							item.selectedColorIndex = findColorIndex;
						} catch (e) { }
					} else {
						 if (item.sceneImg) {
							item.showImgSrc = item.sceneImg;
							item.selectedColorIndex = -1;
						} else {
							try {
								item.showImgSrc = JSON.parse(productParamList[0].imgJson)[0]?.url;
								item.selectedColorIndex = 0;
							} catch (e) { }
						}
						if (this.isCoins) {
							try {
								item.showImgSrc = JSON.parse(item.imgJson)[0]?.url;
								item.selectedColorIndex = 0;
							} catch (e) { }
						}
					}
				} else {
					item.showImgSrc = item.sceneImg;
					item.selectedColorIndex = -1;
				}
			});
			return list;
		},
		sortProduct(command) {
			this.sorts = command;
			//Google记录用户喜欢最新产品还是推荐产品排序
			try {
				if (window.$nuxt.$store.state.advertConfig.googleConfig && window.gtag) {
					gtag("event", "select_content", {
						content_type: "productSort",
						content_value: command == 1 ? "newest" : "recommend",
					});
				}
			} catch (error) { }
			this.useAdNum = 0;
			this.doubleAd = 0;
			this.usePosition = 0;
			this.searchProduct();
		},
		toggleFilter() {
			this.setAdHeight();
			this.showFilter = !this.showFilter;
			let loadBtnMainBox = document.querySelector(".loadBtnMainBox");
			if (loadBtnMainBox && !this.isMobile) {
				if (!this.showFilter) {
					loadBtnMainBox.style.width = "100%";
					loadBtnMainBox.style.marginLeft = "0";
				} else {
					loadBtnMainBox.style.width = "calc(100% - 265px)";
					loadBtnMainBox.style.marginLeft = "265px";
				}
			}
			if (this.isMobile) {
				let leftBar = document.querySelector(".leftBar");
				if (leftBar) leftBar.style.top = 0;
				let modalHalfListBox = document.querySelector(".modalHalfListBox");
				if (modalHalfListBox) {
					modalHalfListBox.style.zIndex = "auto";
					if (this.showFilter) modalHalfListBox.style.zIndex = "999";
				}
			}
		},
		clickTitle(val) {
			let findInd = this.activeNames.findIndex((item) => val.id === item);
			if (findInd >= 0) {
				this.activeNames.splice(findInd, 1);
			} else {
				this.activeNames.push(val.id);
			}
		},
		handleResize() {
			let header = document.querySelector("#modalHeader");
			this.headerHeight = typeof header?.offsetHeight === "number" ? header.offsetHeight : 0;
			if (this.isDialog) {
				this.headerHeight = 0;
			}
			this.setSticky();
			this.screenHeight = window.innerHeight;
			let needLoad = false
			if (this.screenWidth > window.innerWidth) {
				if (this.screenWidth > 1400 && window.innerWidth < 1400) {
					this.screenWidth = window.innerWidth
					needLoad = true
				}
			} else {
				if (this.screenWidth < 1400 && window.innerWidth > 1400) {
					this.screenWidth = window.innerWidth
					needLoad = true
				}
			}
			if (needLoad) {
				this.useAdNum = 0;
				this.doubleAd = 0;
				this.usePosition = 0;
				this.debounceSearchProduct();
			}
		},
		//根据行数选择插入的广告位置
		insertAdPosition() {
			if (!this.adData || !this.productList.length) {
				return false;
			}
			if(this.page == 1){
				this.useAdNum = 0;
				this.doubleAd = 0;
				this.usePosition = 0;
			}
			let type = this.$store.state.device === 'pc' ? "pc" : 'mb';

			// 计算当前页的起始位置和结束位置
			const startIndex = (this.page - 1) * this.pageSize + 1; // 起始位置从1开始
			let endIndex = this.page * this.pageSize;
			if (this.productList.length - this.useAdNum < endIndex) endIndex = this.productList.length - this.useAdNum;
			// 插入广告的位置数组，每个元素表示一个广告位的位置
			const adPositions = [];
			var innerIndex;
			// 循环遍历每个广告位
			for (let i = 1; i <= this.adNum; i++) {
				if (this.adNum > this.advert.length) {
					innerIndex = (i - 1) % this.advert.length;
				} else {
					innerIndex = this.usePosition;
				}
				if (i % 2 != 0) {
					let firstIndex;
					if (type == "pc") {
						firstIndex = 16 * ((i + 1) / 2) - 13;
					} else if (type == "mb") {
						firstIndex = 3 - 1 + ((i - 1) / 2) * 12;
					}
					if (firstIndex >= startIndex && firstIndex <= endIndex) {
						firstIndex = firstIndex - this.doubleAd;
						if (this.advert[innerIndex].booth == 2) {
							firstIndex--;
							this.doubleAd++;
						}

						adPositions.push(firstIndex);
						this.usePosition++;
					}
				} else {
					let secondIndex;
					if (type == "pc") {
						secondIndex = 16 * Math.floor((i - 1) / 2) + 9 - 1;
					} else if (type == "mb") {
						secondIndex = 7 - 1 + ((i - 2) / 2) * 12;
					}

					if (secondIndex >= startIndex && secondIndex <= endIndex) {
						secondIndex = secondIndex - this.doubleAd;
						if (this.advert[innerIndex].booth == 2) {
							this.doubleAd++;
						}
						adPositions.push(secondIndex);
						this.usePosition++;
					}
				}
			}
			if (adPositions.length != 0) {
				this.insertAd(adPositions);
			}
			this.setAdHeight()
		},
		//插入广告
		insertAd(indexArray) {
			if (this.productList.length != 0) {
				//广告条数少于广告位
				if (this.advert.length < this.adNum) {
					for (let i = 0; i < indexArray.length; i++) {
						if (this.useAdNum == this.advert.length) this.useAdNum = 0;
						if (i >= this.advert.length) {
							//当广告位多与广告时,循环增加广告到广告位
							const innerIndex = i % this.advert.length;
							let copyAd = JSON.parse(JSON.stringify(this.advert[innerIndex]));
							copyAd.id = generateUUID()
							this.productList.splice(indexArray[i], 0, copyAd);
						} else {
							let adUseList = this.advert.slice(this.useAdNum);
							this.productList.splice(indexArray[i], 0, adUseList[0]);
						}
						this.useAdNum++;
					}
				} else {
					//广告条数多于广告位
					for (let j = 0; j < indexArray.length; j++) {
						let adUseList = this.advert.slice(this.useAdNum);
						this.productList.splice(indexArray[j], 0, adUseList[0]);
						this.useAdNum++;
					}
				}
			}
		},
		//获取左边分类标签大类
		getLabel(fatherId) {
			return this.labelData.find((item) => item.id == fatherId).nameEn;
		},
		setSticky() {
			if (!this.isMobile) {
				let topRight = document.querySelector(".top-right");
				let leftBar = document.querySelector(".leftBar");
				this.selectLabelHeight = this.headerHeight;
				if (leftBar) {
					leftBar.style.top = this.headerHeight + "px";
				}
				if (topRight) {
					topRight.style.top = this.headerHeight + "px";
				}
			} else {
				let topRight = document.querySelector(".top-right");
				if (topRight) {
					topRight.style.top = this.headerHeight + "px";
				}
			}
			this.setAdHeight();
		},
        async loadMoreData(){
            await this.loadThreeData(false);
            this.insertAdPosition();
        },
        async loadThreeData(type = false) {
            let num = this.page;
            if (!type) {
                num = this.page + 1;
                if (this.totalResult === 0 || this.pages < num || this.page <= 0) {
                    return false;
                }
            }
            this.loadingProduct = true;
            let addProduct = await this.getProduct(num).then((res) => {
                return this.setDefaultShowImg(res.data.records);
            });
            this.productList = this.productList.concat(addProduct);
			this.insertUploadBox()
            this.loadingProduct = false;
        },
		insertUploadBox() {
			if (!this.isMedals) return
			//插入上传文件的子元素
			let uploadItem = {
				id: -1,
				name: "uploadBox"
			}
			if (this.productList.length == 0) return
			if (this.isMobile) {
				this.productList.splice(3, 0, uploadItem);
			} else {
				this.productList.unshift(uploadItem);
			}
		},
		listScrollTop() {
			try {
				window.scrollTo({
					top: 0,
					behavior: "smooth",
				});
			} catch (error) {
				console.log("找不到dom");
			}
		},
		activeCateName(item) {
			let data = this.isStockPage == "1" ? item.shopRouting : item.customRouting;
			return data && this.formatUrlName(data) === this.cateName;
		},
		setModalType(target, targetArray, clickType, event, other) {
			this.$setModal(this, target, targetArray, clickType, event, other);
		},
		setAdHeight() {
			this.adBoxHeight = 0;
			this.$nextTick(() => {
				try {
					let productGoods = document.querySelector(".productGoods");
					this.adBoxHeight = productGoods.getBoundingClientRect().height + "px";
				} catch (error) { }
			});
		},
		mergeImg(img1, img2, canvasData, options = {}) {
			if (!img1 || !img2) return Promise.resolve(false);
			if (!options) return
			// 图片源
			const copyimg1 = new Image(); // 圆形图片
			const copyimg2 = new Image(); // 背景图片
			copyimg1.crossOrigin = "Anonymous";
			copyimg2.crossOrigin = "Anonymous";
			// 设置图片的 src 属性
			copyimg1.src = img1;
			copyimg2.src = img2;
			let devicePixelRatio = window.devicePixelRatio || 1
			return new Promise((resolve, reject) => {
				Promise.all([this.loadImage(copyimg1), this.loadImage(copyimg2)]).then(() => {
					let canvas = document.createElement("canvas");
					let ctx = canvas.getContext("2d");
					canvas.width = canvasData.width * devicePixelRatio;
					canvas.height = canvasData.height * devicePixelRatio;
					ctx.clearRect(0, 0, canvas.width, canvas.height);
					// 绘制背景图片
					ctx.drawImage(copyimg2, 0, 0, canvas.width, canvas.height);
					// 计算绘制区域大小
					let scaleX = (copyimg2.naturalWidth - options.left - options.right) / copyimg2.naturalWidth;
					let scaleY = (copyimg2.naturalHeight - options.top - options.bottom) / copyimg2.naturalHeight;
					const drawAreaWidth = canvasData.width * scaleX;
					const drawAreaHeight = canvasData.height * scaleY;
					const drawAreaLeft = canvasData.width * (options.left / copyimg2.naturalWidth);
					const drawAreaTop = canvasData.height * (options.top / copyimg2.naturalHeight);
					const drawAreaMinDimension = Math.min(drawAreaWidth, drawAreaHeight); // 最短边
					// 圆心位置为绘制区域中心
					const x = drawAreaLeft + drawAreaWidth / 2;
					const y = drawAreaTop + drawAreaHeight / 2;
					// 圆的半径为绘制区域最短边的一半
					const radius = drawAreaMinDimension / 2 * devicePixelRatio;
					// 计算 img1 中心坐标以及最短边作为直径
					const img1MinDimension = Math.min(copyimg1.naturalWidth, copyimg1.naturalHeight);
					const img1CenterX = copyimg1.naturalWidth / 2;
					const img1CenterY = copyimg1.naturalHeight / 2;

					// 开始路径并创建圆形路径
					ctx.beginPath();
					ctx.arc(x, y, radius, 0, Math.PI * 2);
					ctx.closePath();
					ctx.clip();
					// 绘制圆形图片，调整大小以适应绘制区域
					ctx.drawImage(
						copyimg1,
						img1CenterX - img1MinDimension / 2, // 源起始x
						img1CenterY - img1MinDimension / 2, // 源起始y
						img1MinDimension,                   // 源宽度
						img1MinDimension,                   // 源高度
						x - radius,                         // 目标起始x
						y - radius,                         // 目标起始y
						drawAreaMinDimension,               // 目标宽度
						drawAreaMinDimension                // 目标高度
					);

					// 将 canvas 转换为 Blob 对象
					// canvas.toBlob(function (blob) {
					//   let url = URL.createObjectURL(blob);
					//   resolve(url);
					// }, 'image/png', 1.0);
					let mergedImgUrl = canvas.toDataURL('image/png');
					resolve(mergedImgUrl);
				}).catch(err => {
					console.log(err, 'error');
					reject(false);
				});
			})
		},
		async nowMergeImg(img1) {
			let productGoods = document.querySelectorAll(".productGoods");
			const promises = [];
			let ids = []
			if (productGoods) {
				[...productGoods].forEach(async (element, index) => {
					if (index >= 10) return
					promises.push((async () => {
						let { goods, img2, options, canvasData, id } = this.getProductImgData(element);
						let newImgUrl = null
						try {
							newImgUrl = await this.mergeImg(img1, img2, canvasData, options);
						} catch (error) {
							console.log(error, 'error');
						}
						if (!newImgUrl) return
						ids.push({ id: +id, canvasData: canvasData, options: options })
						goods.dataset.newImgUrl = newImgUrl;
						goods.dataset.used = true;
						this.setProductImgData(element, true);
					})())
				});
				await Promise.all(promises);
				this.setProductData(ids, true)
				this.needMerge = true
			}
		},
		async changeColor(id, img2, canvasData, options, type = false) {
			if (this.uploadPic.length > 0 && this.uploadPic[0].secure_url.length > 0) {
				let newImg = await this.mergeImg(this.uploadPic[0].secure_url, img2, JSON.parse(canvasData), JSON.parse(options))
				if (!newImg) return
				let goods = document.querySelector(`.goods${id}`);
				if (goods) {
					goods.dataset.newImgUrl = newImg
					if (type) {
						let picImg = document.querySelector(`.picImg${id}`);
						picImg.setAttribute("src", newImg);
					}
				}
				this.setProductData([{ id: +id, canvasData, options }], true)
			}
		},
		cancelMergeImg() {
			this.needMerge = false
			this.setProductData([], false, 'all')
			this.setAllProductData()
		},
		setAllProductData() {
			let productGoods = document.querySelectorAll(".productGoods");
			if (productGoods) {
				[...productGoods].forEach((element) => {
					let { goods } = this.getProductImgData(element);
					goods.dataset.used = false;
				})
			}
		},
		setProductData(arr, data, type = 'self') {
			if (type == 'all') {
				this.productList.forEach((item, index) => {
					item.showMergeImg = data
				})
				return
			}
			this.productList.forEach((item, index) => {
				let temp = arr.find(citem => citem.id == item.id)
				if (temp) {
					item.showMergeImg = data
					if (item.selectedColorIndex == -1 && data) {
						let optionData = this.parseJSON(item.productParamList?.[0].imgJson)[0]
						let showImgSrc = optionData?.url;
						let options = optionData ? JSON.stringify({ left: optionData.left, right: optionData.right, top: optionData.top, bottom: optionData.bottle }) : JSON.stringify({})
						item.showImgSrc = showImgSrc;
						item.selectedColorIndex = 0
						this.changeColor(item.id, showImgSrc, JSON.stringify(temp.canvasData), options, true)
					}
				}
			})
		},
		parseJSON(str) {
			return str ? JSON.parse(str) : [];
		},
		getProductImgData(element) {
			let goods = element.querySelector('.goods')
			let img2 = ''
			let options = {}
			let id = -99
			let used = false
			let canvasData = {}
			if (goods) {
				img2 = goods.dataset.imgurl;
				id = goods.dataset.imgid;
				options = JSON.parse(goods.dataset.options)
				used = goods.dataset.used
				canvasData = JSON.parse(goods.dataset.canvasdata)
			}
			return {
				goods: goods || null,
				img2,
				id,
				used,
				canvasData,
				options
			}
		},
		setProductImgData(element, data) {
			let goods = element.querySelector('.goods')
			if (goods) {
				goods.dataset.used = data
			}
		},
		// 加载图片的辅助函数
		loadImage(img) {
			return new Promise((resolve, reject) => {
				img.onload = resolve;
				img.onerror = reject;
			});
		},
		initIntersectionObserver() {
			if (!this.isMedals) return
			const observer = new IntersectionObserver((entries) => {
				entries.forEach(async (entry) => {
					if (entry.isIntersecting) {
						// 产品在可视区域内
						if (this.uploadPic.length > 0 && this.uploadPic[0].secure_url.length > 0) {
							let img1 = this.uploadPic[0].secure_url;
							let { goods, img2, options, canvasData, id, used } = this.getProductImgData(entry.target)
							if (used === 'true') return
							let newImgUrl = null
							try {
								newImgUrl = await this.mergeImg(img1, img2, canvasData, options);
							} catch (error) { }
							if (!newImgUrl) return
							goods.dataset.newImgUrl = newImgUrl;
							goods.dataset.used = true
							this.setProductData([{ id: +id, canvasData, options }], true)
						}
					}
				});
			}, {
				root: null,
				rootMargin: '0px',
				threshold: 0.1 // 当元素至少有 10% 可见时触发回调
			});
			let productGoods = document.querySelectorAll(".productGoods");
			if (productGoods) {
				[...productGoods].forEach((element) => {
					observer.observe(element);
				});
			}
			this.intersectionObserver = observer;
		},
	},
	mounted() {
		if (process.env.isManage) {
			this.isManage = true;
			return false;
		}
		if (this.isMobile) this.showFilter = false;
		let header = document.querySelector("#modalHeader");
		this.headerHeight = typeof header?.offsetHeight === "number" ? header.offsetHeight : 0;
		if (this.isDialog) {
			this.headerHeight = 0;
		}
		this.halfName = this.$route.params.halfDesign;
		this.debounceSearchProduct = debounce(this.searchProduct, 600);
		this.debounceLabel = debounce(this.updateLabel2, 600);
		this.screenHeight = window.innerHeight;
		this.screenWidth = window.innerWidth
		this.cateName = this.$route.params.pathMatch;
		if (this.isDialog) {
			this.cateName = this.formatUrlName(this.name);
		}
		window.onresize = this.handleResize;
		Promise.all([
			this.getAppLabelAttributeList(),
			listAllCategoryByParentId({
				id: this.parentCateId || this.cateId,
				onlyGetStockCate: this.isStockPage,
			}),
			this.getNewLabelAttributeList(),
            getAdvertList({ categoryId: this.parentCateId || this.cateId })
		]).then((result) => {
			let labelData = result[0].data,
				cateList = result[1].data,
				newLabelData = result[2].data;
			//添加三级展开列表
			let newLabelItem = null;
			if (newLabelData && newLabelData.childList && newLabelData.childList.length > 0) {
				// 创建新的 children 数组并修改其属性
				const updatedChildren = newLabelData.childList.map((item, index) => ({
					...item,
					nameEn: item.categoryAlias || item.name,
					attributeList: item.childList ? [{
						id: item.id,
						parentId: item.id,
						topId: newLabelData.id,
						valueName: this.langDesign.all,
						customRouting: item.customRouting,
						productCount: item.productCount,
						shopRouting: item.shopRouting,
					}].concat(item.childList.map((citem) => ({
						...citem,
						parentId: item.id,
						topId: newLabelData.id,
						valueName: citem.categoryAlias || citem.name,
					}))) : [{
						id: item.id,
						parentId: item.id,
						topId: newLabelData.id,
						valueName: this.langDesign.all,
						customRouting: item.customRouting,
						productCount: item.productCount,
						shopRouting: item.shopRouting,
					}],
				}));
				newLabelItem = {
					id: newLabelData.id,
					isExpand: true,
					nameEn: newLabelData.categoryAlias || newLabelData.name,
					attributeType: 'cateList',
					attributeList: updatedChildren,
				};
			}
			if (newLabelItem) labelData.unshift(newLabelItem);
			labelData.forEach((item) => {
				if(item.isMore) item.moreText = true;
				if (item.isExpand) this.activeNames.push(item.id);
			});
			this.activeNames.push(-1);
			this.labelData = labelData;
			//左侧选择栏没有数据关闭
			if (!this.labelData || this.labelData.length == 0) this.showFilter = false;
			this.cateList = cateList;
			//根据新的接口来获取名字
			//只有cateId 是一级  有cateId和parentCateId 是二级  cateId是二级id 有cateId和parentCateId和parentOneChildCateId  是三级 cateId是三级id
			let name = null
			if (newLabelData?.id == this.cateId) {
				name = newLabelData;
			}
			if (name) {
				//没有选择子类，不处理fastQuote
				this.$store.commit("halfDesign/setIsFastQuote", false);
				this.fatherCateName = name.name;
			} else {
				if (this.parentCateId) {
					let cateItem = null;
					// 根据是否有一级子类ID判断是二级还是三级
					const isSecondLevel = !this.parentOneChildCateId;
					if (newLabelData.childList && newLabelData.childList.length > 0) {
						// 二级分类查找
						if (isSecondLevel) {
							cateItem = newLabelData.childList.find(citem => citem.id === this.cateId);
						}
						// 三级分类查找
						const parentCateItem = newLabelData.childList.find(citem => citem.id === this.parentOneChildCateId);
						if (parentCateItem && parentCateItem.childList) {
							cateItem = parentCateItem.childList.find(ccitem => ccitem.id === this.cateId);
						}
						if(cateItem.attributeValueId){
							if (!this.labelData || this.labelData.length == 0) return
							let colorData=this.labelData.find((item) => {
								return item.attributeFlag=='color'
							})
							if(colorData){
								colorData?.attributeList.forEach((item) => {
									if(item.id==cateItem.attributeValueId) this.toggleColor(item)
								})
							}
						}
				        this.$store.commit("halfDesign/setIsFastQuote", Boolean(cateItem.isFastQuote));
					}
					this.fatherCateName = cateItem?.name || ''
				}
			}
            //获取广告数据
            let adData = result[3].data;
            if(adData.length && adData[0]?.advertiseList?.length){
                this.adData = adData[0];
                this.advert = adData[0].advertiseList.filter((obj) => obj.isEnable == 1);
                this.adNum = Math.floor((Number(adData[0].advertiseNum) + 1) / 2);
            }
            this.$nextTick(() => {
                this.fristWatch = true;
                //插入广告
                this.insertAdPosition();
                this.setSticky();
            });
		});
	},
	beforeDestroy() {
		window.onresize = null;
	},
};
</script>

<style scoped lang="scss">
::v-deep .custom-slider .v-range-slider__track-background {
	background-color: $color-second !important;
	/* 设置轨道背景颜色 */
}

// ::v-deep .custom-slider .v-slider__thumb {
// 	background-color: $color-second !important; /* 设置滑动块滑动后的颜色 */
// }
::v-deep .custom-slider .v-range-slider__track-fill {
	background-color: $color-primary !important;
	/* 设置你想要的背景颜色 */
}

.bps_container {
	@include respond-to(mb) {
		font-size: 12px;
	}
}

.loadFirstData {
	display: flex;
	justify-content: center;
	align-items: center;
	min-height: 300px;
}

.find-agent {
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	margin-bottom: 10px;
	width: 100%;
	height: 100%;
	padding: 20px;
	text-align: center;
	background: url(https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20221018/2050Ntxm6NPD.png) center/cover no-repeat;

	.c-title {
		font-size: 48px;
		color: #00156b;
	}

	.des {
		max-width: 680px;
		margin: 10px 0 20px;
		font-size: 32px;
		color: #00156b;
	}

	.v-btn {
		width: 200px;
		margin: 0 10px;
	}

	@media screen and (max-width: $mb-width) {
		.c-title {
			font-size: 24px;
		}

		.des {
			font-size: 16px;
		}

		.v-btn {
			width: 80%;
			margin-bottom: 10px;
		}
	}
}

.wrap {
	display: flex;
	flex-direction: column;
	background-color: #fff;

	@include respond-to(mb) {
		padding-bottom: 10px;
	}
}

.wrap.modal-box.isDialog {
	padding: 20px !important;

	@include respond-to(mb) {
		padding: 10px !important;
	}
}

img {
	width: auto;
	max-width: 100%;
	max-height: 100%;
	vertical-align: middle;
}



.borderBox {
	width: 100%;
	height: 0px;
	border: 1px solid #f0f0f0;
}

.litterArrow {
	margin-left: 2px;

	&::before {
		font-size: 8px;
	}
}

.rightHeader {
	display: flex;
	align-items: center;
	flex-wrap: wrap;
	width: fit-content;
	column-gap: 3vw;

	@include respond-to(mb) {
		gap: initial;
	}
}

.top-right {
	display: flex;
	align-items: center;
	justify-content: space-between;
	background: #f4f5f7;
	padding: 10px;
	margin: 20px 0 0;

	&.sticky {
		z-index: 10;
	}

	.filterSearch {
		flex: 1;
		display: flex;
		align-items: center;

		&.textSearch {}
	}

	.search-area {
		position: relative;

		input {
			min-width: 400px;
			background-color: #fff;
			height: 36px;
			line-height: 36px;
			outline: none;
			border: none;
			padding: 0 20px 0 20px;
		}

		i {
			position: absolute;
			right: 10px;
			top: 50%;
			transform: translateY(-50%);
			font-size: 26px;
			color: #9b9b9b;
		}
	}

	.filter-area {
		display: flex;
		justify-content: space-between;
		align-items: center;
		font-weight: 400;
		font-size: 16px;
		color: #333333;

		.filter1 {
			display: flex;
			align-items: center;
			cursor: pointer;
			margin: 0 20px;

			b {
				margin-right: 4px;
				font-size: 18px;
			}
		}

		.filter2 {
			display: flex;
			align-items: center;

			.t1 {
				margin-right: 4px;
			}

			b {
				font-size: 12px;
			}
		}

		.filter3 {
			display: flex;
			align-items: center;
			border: 2px solid $color-primary;
			border-radius: 4px;

			svg {
				color: $color-primary;
			}

			&>div {
				padding: 4px 8px;
				cursor: pointer;
			}

			&>div.active {
				background-color: $color-primary;

				svg {
					color: #ffffff;
				}
			}
		}

		.v-btn {
			text-transform: capitalize;
		}
	}

	@include respond-to(mb) {
		flex: 1;
		border-radius: 4px;
		width: 100%;

		.search-area {
			position: relative;
			width: 100%;
			overflow: hidden;

			.inputBox {
				margin: 0 auto;
				text-align: center;
			}

			input {
				min-width: 100%;
				background-color: #fff;
				border-radius: 2px;
				height: 30px;
				line-height: 30px;
				outline: none;
				border: none;
				padding: 0 30px 0 20px;
			}

			i {
				position: absolute;
				right: 10px;
				top: 50%;
				transform: translateY(-50%);
				font-size: 20px;
				color: #9b9b9b;
			}
		}

		.filter-area {
			font-size: 12px;
			width: 30%;

			.filter1 {
				margin: 0 0 0 auto;
			}

			.filter2 {
				display: none;
			}

			&>div {
				margin: 0 10px;
			}
		}
	}
}

#borderBoxSticky {
	background: #f4f5f7;
}

.content {
	position: relative;
	display: grid;
	align-items: flex-start;
	grid-column-gap: 20px;
	grid-template-columns: 265px 1fr;
	padding-bottom: 50px;

	.loadProgress {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        padding: 10px 0;
	}

	.load {
		display: flex;
		justify-content: center;
		align-items: center;
		min-height: 200px;
		padding: 20px;
	}

	&.noFilter {
		grid-template-columns: 1fr;
		grid-column-gap: 18px;
		grid-row-gap: 0px;
		padding-bottom: 50px;

		.rightPart {
			.rightContent {
				.good-item {
					.goods {
						grid-template-rows: auto 1fr;
					}
				}
			}
		}
	}


	.category-item {
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 14px;
		font-size: 14px;
		cursor: pointer;
		background: #e8e9eb;
		border: 1px solid transparent;
		flex-shrink: 0;

		&.cateTag {
			span {
				padding: 5px 15px;
			}
		}

		&.active {
			font-weight: bold;
			color: var(--tag-color, var(--color-primary));
			background-color: var(--tag-color-lighten, var(--color-second));
			border: 1px solid var(--color-primary-lighten);
		}

		&.tag {
			padding: 3px 10px;
			border-radius: 0;
			color: $color-primary;
			color: var(--tag-color, var(--color-primary));
			background-color: var(--tag-color-lighten, var(--color-second));

			.delIconBox {
				display: flex;
				align-items: center;
				justify-content: center;
			}

			&:hover {
				border: 1px solid var(--color-primary-lighten);

				.v-icon {
					color: #666666;
				}
			}
		}

		&:hover {
			color: var(--tag-color, var(--color-primary));
			background-color: var(--tag-color-lighten, var(--color-second));
			border: 1px solid var(--color-primary-lighten);

			.v-icon {
				color: #ffffff;
			}
		}

		span {
			b {
				font-size: 10px;
			}
		}

		@include respond-to(mb) {
			white-space: nowrap;
		}
	}

	.selectLabel {
		display: none;
		width: 100%;
		padding: 10px;
		margin-bottom: 10px;
		background: #f4f5f7;
		border-radius: 2px;
		box-shadow: 1px 1px 4px 0px rgba(0, 0, 0, 0.06);

		.filter {
			font-size: 14px;

			.top {
				display: flex;
				justify-content: space-between;

				.cateitemBox {
					flex: 1;
					width: fit-content;
					display: flex;
					flex-wrap: wrap;
					gap: 4px;
					max-height: 34px;
					overflow: hidden;
				}

				.total {
					color: #c4c4c4;
					font-style: italic;
					margin: 1em 0 0;
				}

				.viewMore {
					width: fit-content;
					display: flex;
					align-items: center;
					margin-left: auto;
					margin-right: -10px;
					padding: 5px 10px;
					cursor: pointer;
					color: $color-primary;
					flex-shrink: 0;
					white-space: nowrap;
					cursor: pointer;

					b {
						font-size: 10px;
					}

					&:hover {
						opacity: 0.8;
						text-decoration: underline;
					}
				}
			}
		}

		@include respond-to(mb) {
			margin-bottom: 10px;
			background: #f3f4f6;
			border-radius: 2px;
			// box-shadow: 1px 1px 4px 0px rgba(0, 0, 0, 0.06);

			.filter {
				font-size: 14px;

				.top {
					width: 100%;
					flex-wrap: nowrap;
					padding-bottom: 5px;
					overflow: hidden;

					.customLabel {
						height: 2em;
					}

					.label {
						padding-left: 0;
					}

					.viewMore {
						padding: 2px 6px;
						margin-right: 0;
					}
				}
			}
		}
	}

	.rightBar {
		.rightPart {
			position: relative;
			display: flex;
			flex-direction: column;
			height: 100%;
			padding: 2px;
			overflow: hidden;

			.rightContent {
				margin-top: 28px;
				flex: 1;
				width: 100%;
				height: 0;
				position: relative;

				.productWrap {
					display: grid;
					grid-template-columns: repeat(4, 1fr);
					grid-gap: 20px;

					@media screen and (min-width: 1401px) and (max-width: 1500px) {
						grid-template-columns: repeat(3, 1fr);
					}

					@include respond-to(pad) {
						grid-template-columns: repeat(3, 1fr);
						gap: 14px;
					}

					@include respond-to(mb) {
						grid-template-columns: repeat(1, 1fr);
						grid-auto-rows: 1fr;
						grid-gap: 10px;
					}
				}

				.inserted_element {
					grid-column: span 2;
					grid-row: span 1;

					@include respond-to(mb) {
						grid-column: span 1;
					}
				}

				@media (any-hover: hover) {
					.good-item:hover .good-back {
						background-color: var(--color-second);
					}
				}
			}
		}
	}

	@include respond-to(mb) {
		grid-template-columns: 1fr;
		padding: 0;
	}
}

.loadBtnMainBox {
	width: calc(100% - 265px);
	margin-left: 265px;

	@include respond-to(mb) {
		width: 100%;
		margin-left: 0;
	}
}

.loadMoreBtn {
	width: 30%;
	min-width: 350px;
	margin: 0 auto;
	margin-bottom: 50px;
	text-align: center;
	font-weight: 400;
	font-size: 22px;
	color: #333333;

	.loadMoreBtnText {
		margin-bottom: 20px;
	}

	.loadBtnBox {
		margin-top: 20px;
		color: $color-primary;
		cursor: pointer;
		font-size: 16px;

		.loadBtn {
			display: inline-block;
			border: 1px solid $color-primary;
			padding: 8px 40px;
			border-radius: 5px;
			transition: 0.3s;

			&:hover {
				background-color: $color-second;
			}
		}
	}

	@include respond-to(mb) {
		min-width: 300px;
		font-size: 18px;

		.loadMoreBtnText {
			margin-bottom: 10px;
		}

		.loadBtnBox {
			margin-top: 10px;
			font-size: 14px;
		}
	}
}



.sticky {
	position: sticky;
	top: 0;
	z-index: 1;
}

.litterMock::before {
	content: "";
	width: 100%;
	height: 10px;
	position: absolute;
	bottom: -10px;
	left: 0;
	background-color: #f2f2f2;
}

.litterMock::after {
	content: "";
	width: 100%;
	height: 10px;
	position: absolute;
	top: -10px;
	left: 0;
	background-color: #f2f2f2;
}

.changeLoad {
	position: absolute;
	inset: 0;
	width: 100%;
	// height: 100vh;
}



/* HTML: <div class="loader"></div> */
.loader {
	width: 50px;
	aspect-ratio: 1;
	display: grid;
	border: 4px solid #0000;
	border-radius: 50%;
	border-right-color: $color-primary;
	animation: l15 1s infinite linear;
}

.loader::before,
.loader::after {
	content: "";
	grid-area: 1/1;
	margin: 2px;
	border: inherit;
	border-radius: 50%;
	animation: l15 2s infinite;
}

.loader::after {
	margin: 8px;
	animation-duration: 3s;
}

@keyframes l15 {
	100% {
		transform: rotate(1turn);
	}
}
</style>