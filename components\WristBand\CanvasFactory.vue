<template>
  <div class="CanvasFactory">
    <article class="canvas-self">
      <canvas id="canvas"></canvas>
    </article>
  </div>
</template>

<script>
export default {
  props: {
    canvasData: {
      type: Object,
    },
  },
  data() {
    return {};
  },
  methods: {
    async canvasInit() {
      return new Promise(async (resolve, reject) => {
        try {
          this.$store.commit("setCanvasLoading", true);
          let canvasContainer = document.getElementById("canvas");
          let ctxContainer = canvasContainer.getContext("2d");

          // 手环
          let canvas = document.createElement("canvas");
          let ctx = canvas.getContext("2d");
          // 尺寸轴
          let canvasAxis = document.createElement("canvas");
          let ctxAxis = canvasAxis.getContext("2d");

          canvasContainer.width = this.canvasData.canvasWdith + 300;
          canvasContainer.height = this.canvasData.canvasHeight;
          canvas.width = this.canvasData.canvasWdith;
          canvas.height = this.canvasData.canvasHeight;
          canvasAxis.width = 300;
          canvasAxis.height = this.canvasData.canvasHeight;
          ctx.save();

          // 绘制颜色背景
          await this.buildCanvasFun({
            context: ctx,
            src: this.canvasData.colorImg,
            x: 0,
            y: 0,
            width: this.canvasData.canvasWdith,
            height: this.canvasData.canvasHeight,
          });

          // 绘制颜色
          await this.buildCanvasFun({
            context: ctx,
            src: this.canvasData.shadowImg,
            x: 0,
            y: 0,
            width: this.canvasData.canvasWdith,
            height: this.canvasData.canvasHeight,
            opacity: 0.1, // 不需要透明度
          });

          // 绘制环状底图
          await this.downFun(this.canvasData.baseImg, ctx);

          // 绘制文字
          await this.drawText(ctx, this.canvasData.clipPoints);

          // 绘制轴
          await this.drawSizeAxis(ctxAxis);

          // 将手环和轴绘制到总画布
          if (this.canvasData.colorStyle === "Glow") {
            // 添加发光效果
            ctx.filter = "drop-shadow(0 0 10px rgba(255, 255, 255, 0.8))"; // 白色发光
          }
          ctxContainer.drawImage(canvas, 300, 0);
          ctxContainer.drawImage(canvasAxis, 0, 0);

          this.canvasToImage = canvasContainer.toDataURL("image/png");
          this.$emit("canvasToImage", this.canvasToImage);
          this.$store.commit("setCanvasLoading", false);
          resolve("done");
        } catch (err) {}
      });
    },

    async drawText(ctx, clipath) {
      return new Promise(async (resolve) => {
        ctx.save();
        // 定义裁剪区域（使用 clipPoints 数组中的点）
        ctx.beginPath();
        clipath &&
          clipath.forEach((point, index) => {
            if (index === 0) {
              ctx.moveTo(point.x, point.y);
            } else {
              ctx.lineTo(point.x, point.y);
            }
          });
        ctx.closePath(); // 闭合路径
        ctx.clip(); // 设置裁剪区域

        let realFrontText = this.canvasData.frontTextGroup;
        let realBackText = this.canvasData.backTextGroup;
        let realInnerText = this.canvasData.insideTextGroup;
        let realAroundText = this.canvasData.aroundTextGroup;

        //判断
        let drawFront =
          this.canvasData.messageStyle != "around" &&
          !this.canvasData.isBack &&
          realFrontText &&
          realFrontText.length > 0 &&
          realFrontText[0].text.trim().length;
        let drawBack =
          this.canvasData.messageStyle != "around" &&
          this.canvasData.isBack &&
          realBackText &&
          realBackText.length > 0 &&
          realBackText[0].text.trim().length;
        let drawAround =
          this.canvasData.messageStyle == "around" &&
          realAroundText &&
          realAroundText.length > 0 &&
          realAroundText[0].text.trim().length;
        let drawInside =
          realInnerText &&
          realInnerText.length > 0 &&
          realInnerText[0].text.trim().length;

        // 绘制正面文字（凹形状）
        if (drawFront) {
          const frontCanvas = document.createElement("canvas");
          const frontCtx = frontCanvas.getContext("2d");
          frontCanvas.width = this.canvasData.canvasWdith;
          frontCanvas.height = this.canvasData.canvasHeight;
          const textItem = realFrontText[0];
          const textNums = textItem.text.length; // 文字数量
          const rotate = 0.025 * textNums;
          const fontSize =
            textNums <= 15
              ? this.canvasData.basefontSize
              : this.canvasData.basefontSize - (textNums - 15) * 10;
          const { scaledFontSize } = await this.drawTextItem(
            frontCtx,
            textItem,
            fontSize,
            {
              x: this.canvasData.frontTextPositionX,
              y: this.canvasData.frontTextPositionY, // 调整 Y 坐标，避免重叠
              isConcave: true, // 凹形状
            }
          );
          ctx.drawImage(frontCanvas, 0, 0, ctx.canvas.width, ctx.canvas.height);
          const [leftTop, rightTop] = this.getContentBounds(frontCtx);
          // 绘制文字前的图片;
          if (this.canvasData.frontStartImg) {
            await this.drawImageWithRotation(ctx, {
              src:
                this.canvasData.frontStartImg + "?temp=" + new Date().valueOf(),
              x: leftTop.x - 10 - scaledFontSize, // 图片放在文字左侧
              y: leftTop.y - 20,
              width: scaledFontSize, // 图片宽度
              height: scaledFontSize, // 图片高度
              rotation: rotate, // 图片旋转角度
              color: textItem.color || "#ffffff", // 指定 SVG 颜色
            });
          }

          // 绘制文字后的图片
          if (this.canvasData.frontEndImg) {
            await this.drawImageWithRotation(ctx, {
              src:
                this.canvasData.frontEndImg + "?temp=" + new Date().valueOf(),
              x: rightTop.x + 10, // 图片放在文字右侧
              y: rightTop.y - 20,
              width: scaledFontSize, // 图片宽度
              height: scaledFontSize, // 图片高度
              rotation: -rotate, // 图片旋转角度
              color: textItem.color || "#ffffff", // 指定 SVG 颜色
            });
          }
        }

        // 绘制反面文字（凹形状）
        if (drawBack) {
          const backCanvas = document.createElement("canvas");
          const backCtx = backCanvas.getContext("2d");
          backCanvas.width = this.canvasData.canvasWdith;
          backCanvas.height = this.canvasData.canvasHeight;
          const textItem = realBackText[0];
          const textNums = textItem.text.length; // 文字数量
          const rotate = 0.025 * textNums;
          const fontSize =
            textNums <= 15
              ? this.canvasData.basefontSize
              : this.canvasData.basefontSize - (textNums - 15) * 10;
          const { scaledFontSize } = await this.drawTextItem(
            backCtx,
            textItem,
            fontSize,
            {
              x: this.canvasData.backTextPositionX,
              y: this.canvasData.backTextPositionY, // 调整 Y 坐标，避免重叠
              isConcave: true, // 凹形状
            }
          );
          ctx.drawImage(backCanvas, 0, 0, ctx.canvas.width, ctx.canvas.height);
          const [leftTop, rightTop] = this.getContentBounds(backCtx);
          // 绘制文字前的图片
          if (this.canvasData.backStartImg) {
            await this.drawImageWithRotation(ctx, {
              src:
                this.canvasData.backStartImg + "?temp=" + new Date().valueOf(),
              x: leftTop.x - 10 - scaledFontSize, // 图片放在文字左侧
              y: leftTop.y - 20,
              width: scaledFontSize, // 图片宽度
              height: scaledFontSize, // 图片高度
              rotation: rotate, // 图片旋转角度
              color: textItem.color || "#ffffff", // 指定 SVG 颜色
            });
          }

          // 绘制文字后的图片
          if (this.canvasData.backEndImg) {
            await this.drawImageWithRotation(ctx, {
              src: this.canvasData.backEndImg + "?temp=" + new Date().valueOf(),
              x: rightTop.x + 10, // 图片放在文字右侧
              y: rightTop.y - 20,
              width: scaledFontSize, // 图片宽度
              height: scaledFontSize, // 图片高度
              rotation: -rotate, // 图片旋转角度
              color: textItem.color || "#ffffff", // 指定 SVG 颜色
            });
          }
        }

        // 绘制环绕文字（凹形状）
        if (drawAround) {
          const aroundCanvas = document.createElement("canvas");
          const aroundCtx = aroundCanvas.getContext("2d");
          aroundCanvas.width = this.canvasData.canvasWdith;
          aroundCanvas.height = this.canvasData.canvasHeight;
          const textItem = realAroundText[0];
          const textNums = textItem.text.length; // 文字数量
          const rotate = 0.025 * textNums;
          const fontSize =
            textNums <= 15
              ? this.canvasData.basefontSize
              : this.canvasData.basefontSize - (textNums - 15) * 10;
          const { scaledFontSize } = await this.drawTextItem(
            aroundCtx,
            textItem,
            fontSize,
            {
              x: this.canvasData.aroundTextPositionX,
              y: this.canvasData.aroundTextPositionY, // 调整 Y 坐标，避免重叠
              isConcave: true, // 凹形状
            }
          );
          ctx.drawImage(
            aroundCanvas,
            0,
            0,
            ctx.canvas.width,
            ctx.canvas.height
          );
          const [leftTop, rightTop] = this.getContentBounds(aroundCtx);
          // 绘制文字前的图片
          if (this.canvasData.aroundStartImg) {
            await this.drawImageWithRotation(ctx, {
              src:
                this.canvasData.aroundStartImg +
                "?temp=" +
                new Date().valueOf(),
              x: leftTop.x - 10 - scaledFontSize, // 图片放在文字左侧
              y: leftTop.y - 20,
              width: scaledFontSize, // 图片宽度
              height: scaledFontSize, // 图片高度
              rotation: rotate, // 图片旋转角度
              color: textItem.color || "#ffffff", // 指定 SVG 颜色
            });
          }

          // 绘制文字后的图片
          if (this.canvasData.aroundEndImg) {
            await this.drawImageWithRotation(ctx, {
              src:
                this.canvasData.aroundEndImg + "?temp=" + new Date().valueOf(),
              x: rightTop.x + 10, // 图片放在文字右侧
              y: rightTop.y - 20,
              width: scaledFontSize, // 图片宽度
              height: scaledFontSize, // 图片高度
              rotation: -rotate, // 图片旋转角度
              color: textItem.color || "#ffffff", // 指定 SVG 颜色
            });
          }
        }

        // 绘制内面文字（凸形状）
        if (drawInside) {
          const insideCanvas = document.createElement("canvas");
          const insideCtx = insideCanvas.getContext("2d");
          insideCanvas.width = this.canvasData.canvasWdith;
          insideCanvas.height = this.canvasData.canvasHeight;
          const textItem = realInnerText[0];
          const textNums = textItem.text.length; // 文字数量
          const rotate = 0.025 * textNums;
          const fontSize = this.canvasData.basefontSize - 20;
          const { scaledFontSize } = await this.drawTextItem(
            insideCtx,
            textItem,
            fontSize,
            {
              x: this.canvasData.insideTextPositionX,
              y: this.canvasData.insideTextPositionY, // 调整 Y 坐标，避免重叠
              isConcave: false, // 凸形状
            }
          );

          ctx.drawImage(
            insideCanvas,
            0,
            0,
            ctx.canvas.width,
            ctx.canvas.height
          );

          //   const offsetY =
          //     (textNums > 8 && textNums <= 12
          //       ? scaledFontSize / 3
          //       : textNums > 12 && textNums <= 16
          //       ? scaledFontSize / 2
          //       : textNums > 16 && textNums <= 26
          //       ? scaledFontSize
          //       : textNums > 26
          //       ? scaledFontSize * 1.5
          //       : 0) - 10;

          //   const [leftBottom, rightBottom] = this.getContentBounds(insideCtx);
          //   // 绘制文字前的图片
          //   if (this.canvasData.insideStartImg) {
          //     await this.drawImageWithRotation(ctx, {
          //       src:
          //         this.canvasData.insideStartImg +
          //         "?temp=" +
          //         new Date().valueOf(),
          //       x: leftBottom.x - 10 - scaledFontSize, // 图片放在文字左侧
          //       y: leftBottom.y + offsetY,
          //       width: scaledFontSize, // 图片宽度
          //       height: scaledFontSize, // 图片高度
          //       rotation: -rotate, // 图片旋转角度（与文字一致）
          //     });
          //   }

          //   // 绘制文字后的图片
          //   if (this.canvasData.insideEndImg) {
          //     await this.drawImageWithRotation(ctx, {
          //       src:
          //         this.canvasData.insideEndImg + "?temp=" + new Date().valueOf(),
          //       x: rightBottom.x + 10, // 图片放在文字右侧
          //       y: rightBottom.y + offsetY,
          //       width: scaledFontSize, // 图片宽度
          //       height: scaledFontSize, // 图片高度
          //       rotation: rotate, // 图片旋转角度（与文字一致）
          //     });
          //   }
        }

        ctx.restore(); // 恢复画布状态
        resolve();
      });
    },
    // 获取四个顶点位置
    getContentBounds(ctx) {
      const width = ctx.canvas.width;
      const height = ctx.canvas.height;
      const imageData = ctx.getImageData(0, 0, width, height).data;

      let minX = width,
        minY = height,
        maxX = 0,
        maxY = 0;

      for (let y = 0; y < height; y++) {
        for (let x = 0; x < width; x++) {
          const alpha = imageData[(y * width + x) * 4 + 3]; // 获取像素的 alpha 值
          if (alpha > 0) {
            // 非透明像素
            if (x < minX) minX = x;
            if (y < minY) minY = y;
            if (x > maxX) maxX = x;
            if (y > maxY) maxY = y;
          }
        }
      }

      // 返回包裹矩形的四个顶点
      return [
        { x: minX, y: minY }, // 左上角
        { x: maxX, y: minY }, // 右上角
        { x: maxX, y: maxY }, // 右下角
        { x: minX, y: maxY }, // 左下角
      ];
    },
    // 计算文字总宽度
    calculateTotalTextWidth(ctx, textGroup, fontSize) {
      let totalWidth = 0;
      ctx.font = `${fontSize}px Arial`; // 设置字体大小
      textGroup.forEach((textItem) => {
        totalWidth += ctx.measureText(textItem.text).width;
      });
      return totalWidth;
    },

    // 绘制图片（支持旋转）
    // async drawImageWithRotation(ctx, { src, x, y, width, height, rotation }) {
    //   return new Promise((resolve) => {
    //     ctx.save();

    //     // 加载图片
    //     const image = new Image();
    //     image.src = src;
    //     image.onload = () => {
    //       // 移动到图片中心
    //       ctx.translate(x + width / 2, y + height / 2);
    //       // 旋转图片
    //       ctx.rotate(rotation);
    //       // 绘制图片
    //       ctx.drawImage(image, -width / 2, -height / 2, width, height);
    //       ctx.restore();
    //       resolve();
    //     };
    //     image.onerror = () => {
    //       console.log("Image load failed:", src);
    //       ctx.restore();
    //       resolve();
    //     };
    //     image.setAttribute("crossOrigin", "anonymous");
    //   });
    // },
    async drawImageWithRotation(
      ctx,
      { src, x, y, width, height, rotation, color }
    ) {
      return new Promise((resolve) => {
        ctx.save();
        // 加载图片
        const image = new Image();
        image.src = src;
        image.onload = () => {
          // 移动到图片中心
          ctx.translate(x + width / 2, y + height / 2);
          // 旋转图片
          ctx.rotate(rotation);

          // 创建一个临时 canvas 来绘制
          const tempCanvas = document.createElement("canvas");
          const tempCtx = tempCanvas.getContext("2d");
          tempCanvas.width = width;
          tempCanvas.height = height;
          tempCtx.drawImage(image, 0, 0, width, height);
          tempCtx.globalCompositeOperation = "source-in";
          tempCtx.fillStyle = color;
          tempCtx.fillRect(0, 0, width, height);

          // 将修改后的图片绘制到主 canvas
          ctx.drawImage(tempCanvas, -width / 2, -height / 2, width, height);
          ctx.restore();
          resolve();
        };
        image.onerror = () => {
          console.log("Image load failed:", src);
          ctx.restore();
          resolve();
        };
        image.setAttribute("crossOrigin", "anonymous");
      });
    },

    async drawSizeAxis(ctx) {
      return new Promise((resolve) => {
        ctx.save();

        // 填充白色背景
        ctx.fillStyle = "#fff";
        ctx.fillRect(0, 0, ctx.canvas.width, ctx.canvas.height);

        // 轴的高度
        const axisHeight = this.canvasData.innerHeight;

        // 轴的宽度
        const axisWidth = 100; // 自定义宽度

        // 计算轴的起始 X 和 Y 坐标，使其水平和垂直居中
        const axisX = (ctx.canvas.width - axisWidth) / 2; // 水平居中
        const axisY = (ctx.canvas.height - axisHeight) / 2; // 垂直居中

        // 绘制轴的主体（垂直线）
        ctx.strokeStyle = "#DCDFE6"; // 轴的颜色
        ctx.lineWidth = 2; // 轴的宽度
        ctx.beginPath();
        ctx.moveTo(axisX + axisWidth / 2, axisY); // 起点
        ctx.lineTo(axisX + axisWidth / 2, axisY + axisHeight); // 终点
        ctx.stroke();

        // 绘制顶部的 T 形状
        const tLength = 40; // T 字的水平线长度
        ctx.beginPath();
        ctx.moveTo(axisX + axisWidth / 2 - tLength / 2, axisY); // 左端点
        ctx.lineTo(axisX + axisWidth / 2 + tLength / 2, axisY); // 右端点
        ctx.moveTo(axisX + axisWidth / 2, axisY); // 中心点
        ctx.lineTo(axisX + axisWidth / 2, axisY + tLength / 2); // 下端点（向内绘制，不出头）
        ctx.stroke();

        // 绘制底部的反向 T 形状
        ctx.beginPath();
        ctx.moveTo(axisX + axisWidth / 2 - tLength / 2, axisY + axisHeight); // 左端点
        ctx.lineTo(axisX + axisWidth / 2 + tLength / 2, axisY + axisHeight); // 右端点
        ctx.moveTo(axisX + axisWidth / 2, axisY + axisHeight); // 中心点
        ctx.lineTo(axisX + axisWidth / 2, axisY + axisHeight - tLength / 2); // 上端点（向内绘制，不出头）
        ctx.stroke();

        // 绘制中间部分的文案
        const text = this.canvasData.sizeName || "Size"; // 默认文案
        const fontSize = 30; // 字体大小
        const textPadding = 10; // 文字与轴线的空白间距
        const textX = axisX + axisWidth / 2; // 文案水平居中
        const textY = axisY + axisHeight / 2; // 文案垂直居中

        ctx.font = `${fontSize}px Arial`; // 设置字体
        ctx.fillStyle = "#666666"; // 文案颜色
        ctx.textAlign = "center"; // 文案水平居中
        ctx.textBaseline = "middle"; // 文案垂直居中

        // 在文字周围绘制一个白色矩形，预留空白
        const textWidth = ctx.measureText(text).width;
        ctx.fillStyle = "#FFFFFF"; // 背景颜色
        ctx.fillRect(
          textX - textWidth / 2 - textPadding, // 矩形起点 X
          textY - fontSize / 2 - textPadding, // 矩形起点 Y
          textWidth + textPadding * 2, // 矩形宽度
          fontSize + textPadding * 2 // 矩形高度
        );

        // 绘制文案
        ctx.fillStyle = "#000000"; // 恢复文案颜色
        ctx.fillText(text, textX, textY);

        ctx.restore();
        resolve();
      });
    },

    calculateFontSize(totalLines, baseFontSize) {
      // 根据最长行的字符数和总行数调整字体大小

      // const charScaleFactor = Math.min(1, maxCharsPerLine / maxLineLength);

      // 行数影响字体大小
      const lineScaleFactor = 1 / totalLines;
      // 计算最终的字体大小
      return baseFontSize * lineScaleFactor;
    },

    // 绘制文字（返回旋转角度和实际宽度）
    async drawTextItem(ctx, textItem, fontSize, position) {
      return new Promise((resolve) => {
        ctx.save();

        // 设置字体样式
        const fontWeight = textItem.weight ? "bold" : "normal";
        const fontStyle = textItem.italic ? "italic" : "normal";
        const fontFamily = textItem.family || "Arial"; // 默认字体

        // 动态计算字体大小
        const maxLineLength = textItem.text.length; // 当前行文字的字符数
        const maxCharsPerLine = 12; // 一行最多能输入 12 个字
        const minFontSize = 30; // 最小字体尺寸
        // 调整 scaleFactor 的计算方式
        const scaleFactor = Math.min(1, maxCharsPerLine / maxLineLength);
        const scaledFontSize = Math.max(minFontSize, fontSize * scaleFactor);
        const charSpacing = scaledFontSize * 0.8; // 字符间距为字体大小的 80%

        ctx.font = `${fontWeight} ${fontStyle} ${scaledFontSize}px ${fontFamily}`;
        ctx.fillStyle = textItem.color || "#ffffff"; // 默认白色

        // 设置文字居中对齐
        ctx.textAlign = "center";
        ctx.textBaseline = "middle";

        // 移动到文字位置
        ctx.translate(position.x, position.y);

        // 定义弧形路径参数
        const radius = 1800; // 弧形路径的半径（较大的半径使文字更接近直线）
        const text = textItem.text;
        const textLength = text.length;

        // 固定字符间距和旋转角度
        const rotationAngle = 0.005; // 每个字符的旋转角度（弧度制，较小值）

        // 计算总宽度
        const totalWidth = (textLength - 1) * charSpacing;

        // 绘制每个字符
        for (let i = 0; i < textLength; i++) {
          ctx.save();

          // 计算当前字符的 X 偏移
          const charX = -totalWidth / 2 + i * charSpacing;

          // 计算当前字符的角度
          const angle = charX / radius;

          // 计算字符的 Y 偏移
          const charY = radius * (1 - Math.cos(angle));

          // 根据凹凸效果调整 Y 偏移
          if (position.isConcave) {
            // 凹形状：向圆心方向偏移（Y 值减小）
            ctx.translate(charX, -charY * 1.5); // 向圆心方向偏移
          } else {
            // 凸形状：向圆心反方向偏移（Y 值增大）
            ctx.translate(charX, charY * 1.2); // 向圆心反方向偏移
          }

          // 旋转字符
          if (position.isConcave) {
            // 凹形状：字符朝向圆心
            ctx.rotate(-angle - i * rotationAngle);
          } else {
            // 凸形状：字符朝向圆心反方向
            ctx.rotate(angle + i * rotationAngle);
          }

          // 绘制字符
          ctx.fillText(text[i], 0, 0);

          ctx.restore();
        }

        ctx.restore();

        // 返回旋转角度和实际宽度
        resolve({
          rotation: position.isConcave ? -rotationAngle : rotationAngle,
          width: totalWidth,
          scaledFontSize,
        });
      });
    },
    // 绘制图片
    buildCanvasFun({
      context,
      src,
      sx,
      sy,
      sWidth,
      sHeight,
      x,
      y,
      width,
      height,
      type = "source-over",
      rotate,
      opacity,
    } = {}) {
      return new Promise((resolve, reject) => {
        this.imgOnloadFun(src).then((image) => {
          this.drawImageFun({
            context,
            image,
            sx,
            sy,
            sWidth,
            sHeight,
            x,
            y,
            width,
            height,
            type,
            rotate,
            opacity,
          }).then((res2) => {
            resolve();
          });
        });
      });
    },

    // 图片加载
    imgOnloadFun(url, type) {
      return new Promise((resolve) => {
        if (!url) {
          resolve();
          return;
        }
        let image = new Image();
        if (url.indexOf("data:") != -1 && url.indexOf("base64") != -1) {
          image.src = url;
        } else {
          image.src = url;
        }
        image.onload = () => {
          if (type) {
            resolve({
              name: type,
              img: image,
            });
          } else {
            resolve(image);
          }
        };
        image.onerror = () => {
          console.log(url, image, "image load failed");
          resolve();
        };
        image.setAttribute("crossOrigin", "anonymous");
      });
    },

    drawImageFun({
      context,
      image,
      sx,
      sy,
      sWidth,
      sHeight,
      x,
      y,
      width,
      height,
      type,
      rotate,
      opacity,
    } = {}) {
      return new Promise((resolve, reject) => {
        context.save();
        if (image) {
          if (rotate) {
            context.rotate((rotate * Math.PI) / 180);
          }
          context.globalCompositeOperation = type;

          // 设置不透明度
          if (opacity !== undefined) {
            context.globalAlpha = opacity;
          }

          if (sx || sy || sWidth || sHeight) {
            context.drawImage(
              image,
              sx,
              sy,
              sWidth,
              sHeight,
              x,
              y,
              width,
              height
            );
          } else {
            context.drawImage(image, x, y, width, height);
          }
          context.restore();
        }

        resolve();
      });
    },

    downFun(val, ctx) {
      return new Promise(async (resolve, reject) => {
        // 镂空模板
        await this.buildCanvasFun({
          context: ctx,
          src: val,
          x: 0,
          y: 0,
          width: this.canvasData.canvasWdith,
          height: this.canvasData.canvasHeight,
        });
        resolve();
      });
    },
  },
  computed: {
    lang() {
      return this.$store.getters.lang.quote || {};
    },
  },
  watch: {},
  created() {},
  mounted() {
    this.canvasInit(); // 初始化画布
  },
};
</script>

<style scoped lang="scss"></style>