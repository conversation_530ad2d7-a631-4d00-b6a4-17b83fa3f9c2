<template>
	<div class="StepBox" :class="{ 'custom-shadow': customShadow,yi: bindValue.paramName === 'No Upgrades' }">
		<div class="item pointer hover-type" :class="{ noBorder: noBorder, active: isActive }" :for="'chooseYourLanyardAttachmentLeft' + bindValue.id" @click="changeInput($event)">
			<i v-if="freeTitle" class="iconfont color-a-icon-freezhuanhuan" :class="bindValue && bindValue.priceInfo && bindValue.priceInfo.unitPrice ? 'noShow' : 'freeTitle'"></i>
			<div class="flex justify-content-center align-items-center">
				<div class="se">
					<div class="swiper flex justify-content-center align-items-center" style="flex-direction: column">
						<img :src="filterImage(bindValue.imageJson)" style="width: 100%" :style="aspectRatio" loading="lazy" />
						<p v-if="bindValue.paramName !== 'No Upgrades' && productsCategoriesParamName === 'Lanyards & Badge Holder'" style="font-size: 14px">
							{{ cardInfo }}
						</p>
					</div>
					<div class="rightInfo">
						<span class="product-info">
							<label :for="bindValue.cateName + bindValue.id" class="radio-beauty"> </label>
							<span class="bTitle">{{ bindValue.alias }}</span>
							<span @click.stop v-if="bindValue.tips">
								<el-tooltip popper-class="cusToolTip" effect="light" :content="bindValue.tips" placement="top-start">
									<b class="icon-wenhao1 tip-icon"></b>
								</el-tooltip>
							</span>
						</span>
						<el-form label-position="top" :model="formData" ref="ruleForm" label-width="100px" class="demo-ruleForm">
							<el-form-item
								:label="lang.Quantity"
								prop="quantity"
								style="margin-bottom: 0"
								:rules="[
									{
										required: true,
										message: lang.QTYisrequired,
										trigger: 'blur',
									},
								]"
							>
								<el-input-number placeholder="QTY" :precision="0" v-model="formData.quantity" :controls="false"></el-input-number>
							</el-form-item>
							<el-form-item
								v-if="productsCategoriesParamName !== 'Lanyards & Badge Holder'"
								:label="formData.paramName == 'Custom PVC Cards' ? lang.lanyard.PVCCardSize + ' ( W” x H” )' : lang.lanyard.PaperInsertSize + ' ( W” x H” )'"
								prop="selectedObj"
								:rules="[
									{
										required: true,
										message: lang.Sizeisrequired,
										trigger: 'change',
									},
								]"
							>
								<el-select v-model="formData.selectedObj" :placeholder="lang.PleaseSelect" value-key="id" @change="changeInput">
									<el-option v-for="item2 in bindValue.childList" :key="item2.id" :label="item2.paramName + ' (' + PVCCardSizePriceFun(formData.quantity, item2) + ')'" :value="JSON.parse(JSON.stringify(item2))"></el-option>
								</el-select>
							</el-form-item>
						</el-form>

						<!--    老弹窗方法 @click="selectYourCardUploadFun(bindValue)"-->
						<div v-show="isActive" class="uploadBtn">
							<div v-if="selectedData['Select Your Card'][0]?.uploadImg" class="upload-img">
								<img :src="selectedData['Select Your Card'][0].uploadImg" />
								<span class="icon-box">
									<b class="icon-check" style="color: #0cbd5f"></b>
									<b class="icon-shanchu2 pointer" style="color: #b6b0b0" @click.stop="removeFile"></b>
								</span>
							</div>
							<div class="uploadBtnWrap">
								<el-button
									class="custom-btn pointer"
									type="primary"
									@click="browseFun"
									:disabled="formData.disabled"
									:style="{
										opacity: formData.disabled ? 0.5 : 1,
									}"
									>{{ lang.UploadArtwork }}
								</el-button>

								<el-checkbox class="custom-checkbox" v-model="bindValue.later" :label="false" @change="changeLater" size="large">{{ lang.Uploadbyemaillater }} </el-checkbox>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<input type="file" :ref="`uploadCard${cardBoxId}`" :accept="acceptFileType" multiple @change="uploadPic" hidden />
	</div>
</template>
<script>
import VideoPlayer from "@/components/Quote/VideoPlayer.vue";
import { uploadFile } from "@/utils/oss";
import {checkFile,acceptFileType} from "@/utils/validate";

export default {
	name: "CardBox",
	components: {
		VideoPlayer,
	},
	props: {
		cardUploadList: {
			type: Array,
		},
		customShadow: {
			type: Boolean,
			default: false,
		},
		aspectRatio: {
			type: String,
		},
		//绑定值
		bindValue: {
			type: Object,
		},
		freeTitle: {
			type: Boolean,
			default: false,
		},
		noBorder: {
			type: Boolean,
			default: false,
		},
		bindName: {
			type: String,
		},
		selectedData: {
			type: [Array, Object],
		},
		cardBoxId: {
			type: [String, Number],
		},
	},
	data() {
		return {
            acceptFileType,
			formData: {
				disabled: false,
				quantity: undefined,
				selectedObj: null,
				...this.bindValue,
			},
			windowWidth: document.documentElement.clientWidth,
			picDialog: false,
			zoomPic: "",
		};
	},
	methods: {
		//
		setFormDataSelectedObj() {
			this.formData.selectedObj = this.bindName == "Select Your Card" ? this.selectedData["Select Your Card"][0]?.selectedObj : null;
		},

		replayUpload() {
			this.browseFun();
			this.$store.commit("setSizeDialog", false);
		},
		browseFun() {
			this.$refs["uploadCard" + this.cardBoxId].click();
		},
		//删除自定义图片
		removeFile() {
			this.selectedData["Select Your Card"][0].uploadImg = "";
			// this.$emit("update:fontImgCustom", this.fontImgCustom);
		},
		uploadPic(event, type = "upload") {
            this.$gl.show();
            let files = type === "upload" ? event.target.files : event;
            let fileResult = checkFile(Array.from(files), this.acceptFileType, 80);
            if (!fileResult) {
                this.$toast.error("File type error");
                this.$gl.hide();
                return false;
            }
			if (fileResult.nomalSize.length == 0) {
				this.$gl.hide();
				this.$store.commit("setSizeDialog", true);
				this.$store.commit("setInputRefName", `uploadCard${this.cardBoxId}`);
				this.$store.commit("setOverSizeList", fileResult.overSize);
				this.$refs["uploadCard" + this.cardBoxId].value = "";
				return false;
			}
			let uploadPromises = [];
			fileResult.nomalSize.forEach((file) => {
				let promise = uploadFile(file).then((res) => {
					this.$emit("updateUploadList", {
						original_filename: file.name,
						secure_url: res,
					});
				});
				uploadPromises.push(promise);
			});
			Promise.all(uploadPromises).then(() => {
				this.$gl.hide();
				if (fileResult.overSize.length > 0) {
					this.$store.commit("setSizeDialog", true);
					this.$store.commit("setInputRefName", `uploadCard${this.cardBoxId}`);
					this.$store.commit("setOverSizeList", fileResult.overSize);
				} else {
					this.$toast.success("success");
				}
				this.$refs["uploadCard" + this.cardBoxId].value = "";
			});
		},

		changeLater(val) {
			this.formData.disabled = val;
			if (val) {
				this.selectedData["Select Your Card"][0].uploadImg = "";
			}
		},
		selectYourCardUploadFun(val) {
			if (val.paramName == "Custom PVC Cards") {
				this.$emit("beforeAfterFun", 4);
			} else {
				this.$emit("beforeAfterFun", 5);
			}
			this.$emit("openWidget");
		},
		validForm(cb) {
			this.$refs["ruleForm"]?.validate((valid) => {
				if (valid) {
					cb(true);
				} else {
					console.log("error submit!!");
					cb(false);
				}
			});
		},
		clearValid() {
			//  this.$refs["ruleForm"]?.resetFields();
			this.$refs["ruleForm"]?.clearValidate();
		},

		// changeSelectFun(id) {
		//   this.bindValue.selectedObj = temp
		// },
		mathFun(val) {
			return Math.abs(val * this.currencyRate).toFixed(2);
		},
		//PVC Card Size递增价格
		PVCCardSizePriceFun(num, val) {
			num ? null : (num = 0);
			let arr = JSON.parse(val.priceInfo.increasePrice);
			let unitPrice = 0;
			if (num == 0) {
				unitPrice = Number(arr[0].unitPrice);
			} else {
				for (let i = arr.length - 1; i >= 0; i--) {
					if (num >= Number(arr[i].quantity)) {
						unitPrice = Number(arr[i].unitPrice);
						break;
					}
				}
			}
			return this.currencySymbol + this.mathFun(unitPrice);
		},
		parseJSON(str) {
			return str
				? JSON.parse(str)
				: [
						{
							url: "",
						},
				  ];
		},
		activeFun(val) {
			this.bindValue.paramName = val.paramName;
			this.bindValue.imageJson = val.imageJson;
		},
		isJSON(str) {
			if (typeof str == "string") {
				try {
					var obj = JSON.parse(str);
					if (typeof obj == "object" && obj) {
						return true;
					} else {
						return false;
					}
				} catch (e) {
					return false;
				}
			} else {
				return false;
			}
		},
		filterImage(val, hasSec = false) {
			if (this.isJSON(val)) {
				if (hasSec) {
					return JSON.parse(val)[1] ? JSON.parse(val)[1].url : JSON.parse(val)[0].url;
				} else {
					return JSON.parse(val)[0].url;
				}
			} else {
				return val.url;
			}
		},
		filterPrice(val) {
			if (!val.unitPrice && !val.moldPrice) {
				return "Free";
			} else if (!val.unitPrice && val.moldPrice) {
				return `+ Mold Fee: $${val.moldPrice.toFixed(0)}`;
			} else {
				return val.moldPrice ? `+ $${val.moldPrice.toFixed(0)} Setup,+ $${val.unitPrice.toFixed(2)}/pc` : `+ $${val.unitPrice.toFixed(2)}/pc`;
			}
		},
		changeInput(e, type) {
			this.$emit("clickFun", {
				key: this.bindName,
				value: this.formData,
			});
			this.$emit("picDialogFun", false);
		},
	},
	computed: {
		lang() {
			return this.$store.getters.lang.quote || {};
		},
		cardInfo() {
			return this.selectedData["Select Your Card"][0]?.selectedObj?.paramName || "";
		},
		productsCategoriesParamName() {
			return this.selectedData["Products Categories"][0]?.paramName;
		},
		isActive: function () {
			if (this.inputVal.length > 0) {
				return this.inputVal.some((item) => {
					return item.id == this.bindValue.id;
				});
			}
		},
		inputVal() {
			return this.selectedData[this.bindName] || [];
		},
		device() {
			return this.$store.state.device;
		},
		currencySymbol() {
			return this.$store.getters.currencySymbol;
		},
		currencyRate() {
			return this.$store.getters.currencyRate;
		},
	},
	watch: {
		isActive: {
			handler() {
				this.$refs.ruleForm?.clearValidate();
			},
		},
	},
	mounted() {
		// if (this.hasChildList) {
		//   let arr = JSON.parse(JSON.stringify(this.bindValue.childList[0]));
		//   for (var key in arr) {
		//     if (this.activeData.hasOwnProperty(key)) {
		//       this.activeData[key] = arr[key];
		//     }
		//   }
		// }
		this.$Bus.$on(`uploadCard${this.cardBoxId}`, this.replayUpload);
	},
	created() {},
	beforeDestroy() {
		this.$Bus.$off(`uploadCard${this.cardBoxId}`);
	},
};
</script>

<style scoped lang="scss">

.StepBox {
	&:nth-of-type(2),&:nth-of-type(3){
		grid-row: 2;
		@media screen and(max-width: 768px) {
			grid-row: auto;
		}
	}
}

.yi {
	.item{
		.se {
			display: grid;
			grid-template-columns: repeat(1, 1fr) !important;

			.rightInfo {
				.product-info {
					justify-content: center;
					margin-block: 0;
				}

				.el-form {
					display: none;
				}

				.uploadBtn {
					display: none;
				}
			}
		}
		img {
			display: none;
		}
	}
}

.upload-img {
	display: flex;
	justify-content: space-between;
	position: relative;
	bottom: 12px;

	img {
		width: 25px !important;
		object-fit: contain;
	}
}

.uploadBtnWrap {
	position: relative;
	bottom: 7px;
	font-size: 15px;

	input[type="file"] {
		position: absolute;
		left: 0;
		top: 0;
		right: 0;
		bottom: 0;
		opacity: 0;
	}

	button[type="uploadButton"] {
		width: 230px;
		height: 40px;
		color: #fff;
		margin-bottom: 3px;
		background: $color-primary !important;
	}

	.custom-checkbox {
		margin-top: 10px;
	}
}

.StepBox {
	::v-deep .el-image__inner {
		vertical-align: middle;
		border-radius: 10px 10px 0 0;
	}

	::v-deep .el-checkbox__input.is-checked + .el-checkbox__label {
		color: $color-primary;
	}

	::v-deep .el-checkbox__input.is-checked .el-checkbox__inner {
		background-color: $color-primary;
		border-color: $color-primary;
	}

	::v-deep .el-form-item__label {
		line-height: 20px;
		font-size: 16px;
	}

	::v-deep .el-input-number {
		width: 100%;

		input {
			text-align: left;
		}
	}

	::v-deep .el-select {
		width: 100%;
	}

	&.custom-shadow {
		position: relative;
		background: #fff;

		&::before,
		&::after {
			content: "";
			position: absolute;
			z-index: -1;
			bottom: 12px;
			left: 5px;
			width: 50%;
			height: 20%;
			box-shadow: 0 14px 7px #d9dbdd;
			transform: rotate(-3deg);
		}

		&::after {
			right: 5px;
			left: auto;
			transform: rotate(3deg);
		}
	}

	$bg: #afb1b3;
	$bgc: white;
	$bgc2: $color-primary;
	cursor: pointer;
	display: flex;
	flex-direction: column;
	align-items: center;
	position: relative;
	@media screen and (max-width: 767px) {
		padding: 0 0 5.5px 0;
	}

	.freeTitle {
		position: absolute;
		font-size: 39px;
		left: -2px;
		top: -2px;
		z-index: 2;
	}

	.noShow {
		overflow: hidden;
		width: 0;
		height: 0;
		position: absolute;
		opacity: 0;
	}

	.item {
		border-radius: 10px;
		padding: 0 0 10px 0;
		box-sizing: border-box;
		transition: all 0.2s;
		width: 100%;
		height: 100%;
		display: block;
		border: 1px solid #e6e6e6;

		&.noBorder {
			border: 1px solid transparent;
		}

		@media (any-hover: hover) {
			&:hover {
				border-color: $color-primary !important;
				box-shadow: 0 3px 4px 0 #ccc;
			}
		}
		@media screen and (max-width: 1921px) and (min-width: 1919px) {
		}

		@media screen and (max-width: 768px) {
			border-radius: 5px;
		}

		@media screen and (max-width: 500px) {
			padding: 5px 0;
		}

		position: relative;

		> div {
			> .el-image img {
				// height: 150px;
				width: 100%;
				height: 100%;
				object-fit: contain;
			}
		}

		&.active {
			.product-info .radio-beauty {
				background-color: $color-primary;
				border-color: $color-primary;

				&::after {
					background-color: $bgc;
				}
			}

			.product-info .bTitle {
				color: $color-primary;
			}

			border-color: $color-primary !important;
			box-shadow: 0 3px 4px 0 #ccc;
		}

		.custom-btn {
			align-items: center;
			background: $color-primary;
			border-color: $color-primary;
			border-radius: 2px;
			color: #fff;
			display: flex;
			font-size: 18px;
			font-weight: 700;
			justify-content: center;
			line-height: 15px;
			width: 100%;
		}

		.uploadBtn {
			position: relative;
			top: 10px;
			@media screen and (max-width: 767px) {
				margin-bottom: 10px;
			}
		}
	}

	.tooltip {
		position: absolute;
		top: 10px;
		right: 10px;
		z-index: 2;

		b {
			color: $color-primary;
		}
	}

	.number-input {
		margin-top: 0;
		width: 140px;
		// height: 34px;
		background: #ffffff;
		border-radius: 4px;
		@media screen and (max-width: 767px) {
			// height: 25px;
		}

		::v-deep .el-input-number.is-controls-right .el-input-number__decrease {
			// line-height: 15px;
			// bottom: -1px;
		}

		::v-deep .el-input-number__increase {
			// top: 3px;
			width: 24px;
			// line-height: 16px;
		}

		::v-deep .el-input-number__decrease {
			// bottom: -1px;
			width: 24px;
			// line-height: 15px;
		}

		::v-deep .el-input__inner {
			// height: 34px;
			// line-height: 34px;
			@media screen and (max-width: 767px) {
				// height: 25px;
				// line-height: 25px;
				text-align: center;
			}
		}
	}

	.se {
		border-radius: 6px;
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		justify-content: space-between;
		// padding: 10px 13px;
		// @media screen and (max-width: 767px) {
		//   padding: 0;
		// }
		.zoomIcon {
			position: absolute;
			top: 10px;
			right: 10px;
			color: #ffffff;
			font-size: 20px;
			z-index: 2;
			transition: font-size 0.2s;

			&:hover {
				font-size: 24px;
				color: $color-primary;
			}
		}

		.swiper {
			position: relative;
			width: 100%;

			img {
				border-radius: 10px 10px 0 0;
			}
		}

		.rightInfo {
			// padding: 15px 20px 35px 10px;
			display: flex;
			flex-direction: column;
			justify-content: center;
		}

		.product-info {
			display: flex;
			align-items: center;
			justify-content: flex-start;
			margin-top: 10px;
			margin-bottom: 20px;

			.radio-beauty {
				width: 18px;
				height: 18px;
				min-width: 18px;
				box-sizing: border-box;
				display: inline-block;
				border: 1px solid $bg;
				vertical-align: middle;
				margin: 0 12px 0 3px;
				border-radius: 50%;
				background-color: $bgc;
				background-clip: content-box;
				position: relative;
				cursor: pointer;

				&::after {
					content: "";
					position: absolute;
					border-radius: 50%;
					left: 50%;
					top: 50%;
					transform: translate(-50%, -50%);
					width: 6px;
					height: 6px;
					background-color: $bg;
				}

				@media screen and (max-width: 767px) {
					width: 16px;
					height: 16px;
					min-width: 16px;
					&::after {
						width: 5px;
						height: 5px;
					}
				}
			}

			.bTitle {
				white-space: nowrap;
				margin-right: 5px;
			}

			.type2-price {
				display: flex;
				flex-direction: column;
			}
		}
	}
}
</style>