<template>
	<header class="header">
		<div class="header-left">
			<div class="header-left-menu header-menu" @click="showMenu = true">
				<b class="icon-a-icon-menuzhuanhuan"></b>
				<span>Menu</span>
			</div>
			<div class="header-left-logo">
				<a href="/"> <img :src="this.$store.state.proSystem.logo" alt="logo"/></a>
			</div>
		</div>
		<div class="header-right">
			<div class="left">
				<div class="menu" @click="showMenu = true">
					<b class="icon-a-icon-menuzhuanhuan"></b>
				</div>

				<a href="mailto:<EMAIL>" class="email">
					<div class="left-icon">
						<b class="icon-email"></b>
					</div>
					<div class="textCon">
						<p>1-888-864-47XX</p>
						<p><EMAIL></p>
					</div>
				</a>
				<a href="tel:909-979-41XX" class="talk">
					<div class="left-icon">
						<b class="icon-a-icon-talkzhuanhuan"></b>
					</div>
					<div class="textCon">
						<p>Talk to A Real Person</p>
						<p>909-979-41XX</p>
					</div>
				</a>
			</div>
			<div class="right">
				<div id="chatComme100" class="chat" @click="triggerChat">
					<div class="left-icon">
						<b class="icon-a-icon-chatzhuanhuan"></b>
					</div>
					<div class="textCon">Chat Now</div>
				</div>
				<div class="cart" @click="toCart">
					<div class="left-icon">
						<b class="icon-a-icon-cartzhuanhuan">
							<span>{{ $store.state.cartQuantity || 0 }}</span>
						</b>
					</div>
					<div class="textCon">Cart</div>
				</div>
				<button v-if="!isLogin" type="button" class="loginBtn" @click="loginTo">Log in or Join Free</button>
				<div v-else class="userInfo" @mouseenter="flag=true" @mouseleave="flag=false">
					<div class="img">
						<b class="iconfont icon-touxiang"></b>
					</div>
					<span class="name">{{ userInfo.firstName }}{{ userInfo.lastName }}</span>
					<b class="iconfont" :class="flag ? 'icon-sanjiao2' : 'icon-sanjiao1'"></b>

					<div class="options" v-show="flag">
						<a href="/user/account/orders" class=""> My Orders </a>
						<a href="/user/account/profile" class=""> My Profile </a>
						<a href="/user/account/address" class=""> My Address </a>
						<a @click="Out">Log Out</a>
					</div>
				</div>
			</div>
			<div class="right-mb">
				<div class="header-menu" @click="showMyDesign">
					<b class="icon-a-icon-Designzhuanhuan"></b>
					<span>My Design</span>
				</div>
				<div class="header-menu" @click="toCart">
					<b class="icon-a-icon-cartzhuanhuan"></b>
					<span>Cart ({{ $store.state.cartQuantity || 0 }})</span>
				</div>
			</div>
		</div>
		<el-drawer :visible.sync="showMenu" direction="ltr" :with-header="false" :size="size" close-on-press-escape>
			<div class="sidebar">
				<div class="user-box" v-if="!isLogin">
					<img class="avatar"
							 src="https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/********/2061krnRYjXB.png"/>
					<button id="button" primary @click.self="loginTo">Sign in or Join Free</button>
				</div>
				<div class="user-box" v-else-if="isLogin">
					<img class="avatar"
							 :src="$store.state.userInfo.picPath || 'https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/********/2061krnRYjXB.png'"/>
					<div class="name">{{ userInfo.firstName }} {{ userInfo.lastName }}</div>
					<button id="button1" primary @click.self="Out">Log Out</button>
				</div>
				<div class="link-box" @click.self="showMenu = !showMenu">
					<n-link tag="a" class="padding" :class="{ select: pagePath == '' }" to="/">
						<b class="icon-a-jxsqd-sj-zyzhuanhuan"></b>
						<strong>Home</strong>
					</n-link>
					<a class="padding" @click.stop="showProduct = !showProduct" :class="{ select: pagePath == 'product' }">
						<b class="icon-a-jxsqd-sj-flzhuanhuan"></b>
						<strong>Products</strong>
						<b class="iconfont arrow" :class="showProduct ? 'icon-sanjiao1' : 'icon-sanjiao2'"></b>
					</a>
					<div class="options" v-if="showProduct">
						<n-link
							v-for="o in outer"
							:key="o.url"
							:to="o.url"
							:class="{ select: pagePath == o.url }"
						>
							<b>{{ o.title }}</b>
						</n-link>
					</div>
					<n-link tag="a" class="padding" to="/about-us" :class="{ select: pagePath == 'about-us' }">
						<b class="icon-a-jxsqd-sj-auzhuanhuan"></b>
						<strong>About us</strong>
					</n-link>
				</div>
			</div>
		</el-drawer>
	</header>
</template>

<script>

export default {
	data() {
		return {
			flag: false,
			showMenu: false,
			showProduct: false,
			address: "",
			outer: "",
		};
	},
	computed: {
		device() {
			return this.$store.state.device;
		},
		size() {
			return this.device !== "mb" ? "500px" : "300px";
		},
		pagePath() {
			return this.$store.state.pagePath;
		},
		userInfo() {
			return this.$store.state.userInfo;
		},
		isLogin() {
			return this.$store.getters.isLogin;
		}
	},
	mounted() {
		this.$store.dispatch("updateHeadFootPages").then((res) => {
			this.address = res.sysProOwnerDTO;
			this.outer = res.pageRowList.lastItem.normalData;
		});
	},
	methods: {
		showMyDesign() {
			this.$emit("showMyDesign");
		},
		toCart() {
			window.location.href = "/cart";
		},
		triggerChat() {
			Intercom("show");
		},
		loginTo() {
			this.showMenu = false;
			this.$refs.clears.clear();
		},
		Out() {
			this.$store.commit("logOut");
		},
	},
};
</script>

<style scoped lang="scss">
.header {
	display: grid;
	grid-template-columns: 130px 1fr;
	gap: 10px;
	height: 60px;
	background: #171719;
	color: #ffffff;
	font-size: 14px;

	.header-left {
		min-width: 0;
		display: flex;
		justify-content: center;
		align-items: center;

		.header-left-menu {
			display: none;
		}

		.header-left-logo{
			max-height: 50px;
			width: 130px;
		}
	}

	.header-right {
		display: flex;
		justify-content: space-between;

		b {
			font-size: 24px;
			cursor: pointer;
		}

		a {
			color: #ffffff;
			text-decoration: none;
		}

		.left {
			display: flex;
			align-items: center;
		}

		.right {
			display: flex;
			align-items: center;
		}

		.right-mb {
			display: none;
		}

		.email,
		.talk,
		.chat,
		.cart {
			display: flex;
			align-items: center;
			cursor: pointer;

			.left-icon {
				margin-right: 8px;

				b {
					position: relative;

					span {
						height: 18px;
						min-width: 18px;
						color: #fff;
						font-size: 12px;
						line-height: 18px;
						text-align: center;
						border-radius: 50%;
						background: #eb5757;
						position: absolute;
						right: -10px;
						top: -5px;
					}
				}
			}
		}

		.email,
		.talk,
		.cart {
			margin-left: 30px;
		}

		.loginBtn {
			width: 168px;
			height: 34px;
			border: 1px solid #ffffff;
			border-radius: 10px;
			margin: 0 30px;
			background-color: transparent;
			color: #ffffff;
		}

		.userInfo {
			padding: 0 1rem 0 30px;
			display: flex;
			align-items: center;
			height: 100%;
			position: relative;

			.img {
				// width: 34px;
				// height: 34px;
				border-radius: 50%;

				.icon-touxiang {
					font-size: 19px;
				}

				img {
					border-radius: 50%;
					object-fit: cover;
				}
			}

			.name {
				max-width: 100px;
				margin-left: 11px;
				font-size: 16px;
				font-weight: 400;
				color: #ffffff;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
			}

			.icon-sanjiao2,
			.icon-sanjiao1 {
				margin-left: 7px;
				font-size: 12px;
				color: #ffffff;
			}

			.options {
				display: block;
				border-radius: 5px;
				width: max-content;
				font-weight: normal;
				box-shadow: 0 0 10px -5px rgba(0, 0, 0, .25);
				position: absolute;
				z-index: 99;
				left: 14px;
				top: 4em;

				a {
					display: block;
					text-align: left;
					line-height: 1.5em;
					padding: 0.6em 1.2em;
					color: #333333;
					background: white;

					&:first-child {
						border-radius: 5px 5px 0 0;
					}

					&:last-child {
						border-radius: 0 0 5px 5px;
					}

					&:hover {
						background: #f2f2f2;
					}
				}

				&::before {
					content: "";
					width: 12px;
					height: 12px;
					background: white;
					box-shadow: -1px -1px 6px -3px $bg-mask;
					transform: scale(1, 1.3) rotate(45deg);
					position: absolute;
					z-index: -1;
					left: 20%;
					top: -6px;
				}
			}
		}
	}

	.sidebar {
		width: 100%;
		background-color: #fff;
		cursor: pointer;
		color: #171719;
		font-size: 14px;

		.user-box {
			display: flex;
			flex-direction: column;
			align-items: center;

			.avatar {
				width: 100px;
				height: 100px;
				border-radius: 50%;
				margin: 20px;
				border: 1px solid $color-primary;
			}

			.name {
				margin-bottom: 10px;
			}

			#button {
				height: 32px;
				min-width: 180px;
				border-radius: 4px;
			}

			#button1 {
				height: 32px;
				min-width: 180px;
				border-radius: 4px;
			}
		}

		.link-box {
			padding: 10px 20px;
			display: flex;
			flex-direction: column;
			align-items: flex-start;

			b {
				color: #171719;
				margin-right: 8px;
			}

			strong {
				color: #171719;
			}

			.padding {
				padding: 10px 5px;
				width: 100%;
			}

			.arrow {
				float: right;
			}

			.options {
				display: flex;
				flex-direction: column;
				font-weight: 400;

				a {
					margin-bottom: 4px;

					&:last-child {
						margin-bottom: 0;
					}
				}

				b {
					flex: 1;
					width: 15vw;
					padding: 10px 0 10px 32px;
				}
			}

			.select {
				border-radius: 1vw;
				background: $color-second;
			}

			a:hover {
				background-color: #fce0d4;
				background-position: -1px;
				border-radius: 1vw;
			}
		}

		.contact-box {
			display: flex;
			flex-direction: column;
			align-items: flex-start;
			padding: 15px 20px;

			strong {
				font-weight: 700;
			}

			p {
				color: #999;
				margin: 1vw 0 0;

				a {
					color: #999;
				}
			}

			b {
				margin-right: 8px;
			}
		}
	}
}

@media screen and(max-width: 768px) {
	.header {
		height: 44px;
		font-size: 12px;
		padding: 0 10px;
		background-color: #ffffff;
		color: #333333;

		b {
			font-size: 16px !important;
		}

		.header-menu {
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
		}

		.header-left {
			.header-left-menu {
				display: flex;
				margin-right: 16px;
			}

			img {
				max-height: 30px;
				cursor: pointer;
			}
		}

		.header-right {
			justify-content: flex-end;

			.left {
				display: none;
			}

			.right {
				display: none;
			}

			.right-mb {
				display: flex;

				.header-menu {
					margin-left: 30px;
				}
			}
		}

		.sidebar {
			font-size: 14px;

			b {
				font-size: 14px !important;
			}

			.link-box .options {
				width: 100%;
			}

			.link-box .options b {
				width: 100%;
			}
		}
	}
}
</style>
