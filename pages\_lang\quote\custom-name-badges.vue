<!--by:cg-->
<template>
	<div class="quoteCustomNameBadges">
		<div class="main">
			<div class="custom-scrollbar mainL" id="mainL">
				<div v-if="isMobile" class="title">
					<div class="l">{{ baseModel.detailModel.cateName }}</div>
					<div class="l r">
						<!-- 	<b class="iconfont icon-shoucang" /> -->
					</div>
				</div>
				<!-- 	<div v-if="isMobile" class="item">{{ lang.nameBadges.step.sign }}: {{ baseModel.detailModel.productNumber }}</div> -->

				<!--轮播图-->
				<div v-show="canvasModel.isShow" class="fabricCanvasDom">
					<div style="position: absolute; z-index: 111">
						<div class="fabricCanvas">
							<canvas id="fabricCanvas" :ref="refs" />
							<div class="x">{{ canvasModel.stickerSize.x }}</div>
							<div class="y">{{ canvasModel.stickerSize.y }}</div>
						</div>
						<!-- 	<button @click="addT('321321')">添加文字</button>
					<el-select v-model="canvasModel.stickerEffectType" placeholder="请选择" @change="stickerEffectTypeChange">
						<el-option v-for="item in selectModel.canvasTypeOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
					</el-select> -->
					</div>
				</div>
				<div class="swiper-area">
					<div class="myswiper2" :class="canvasModel.isShow ? 'myswiper2Off' : ''" ref="myswiper2" :style="canvasModel.isShow ? 'height:' + canvasModel.option.height + 'px' : ''">
						<div class="swiper" ref="swiper2">
							<div class="swiper-wrapper">
								<div class="swiper-slide" v-for="(item, index) in carouselModel.list" :key="index">
									<PicZoom :url="item.url" :alt="item.alt" :width="300" :scale="2" type="round"></PicZoom>
								</div>
							</div>
						</div>
						<div class="swiper-button-next">
							<img src="https://static-oss.gs-souvenir.com/web/quoteManage/20240905/Right_Toggle_Button_20240905KiJQ6t.png" alt="rightBtn" />
						</div>
						<div class="swiper-button-prev">
							<img src="https://static-oss.gs-souvenir.com/web/quoteManage/20240905/Left_Toggle_Button_20240905wjrTGT.png" alt="leftBtn" />
						</div>
					</div>
					<div v-show="!isMobile" class="myswiper1">
						<div class="swiper" ref="swiper1">
							<div class="swiper-wrapper">
								<div class="swiper-slide" v-for="(item, index) in carouselModel.list" :key="index" @click="swiperClick(item)">
									<img :src="item.urlThumb" :alt="item.alt" :title="item.alt" />
								</div>
							</div>
						</div>
					</div>
				</div>

				<!--轮播图end-->
				<div v-if="!isMobile" id="swiperDesc" class="swiperDesc">
					<div class="rowOne">
						{{ lang.nameBadges.carousel.titleDesc }}
					</div>

					<div v-if="baseModel.byPIdModel.length > 0" class="rowTwo">
						<div v-html="getByPIdDom()"></div>
					</div>
				</div>
			</div>
			<div class="custom-scrollbar mainR">
				<div v-if="!isMobile" class="title">
					<div class="l">{{ baseModel.detailModel.cateName }}</div>
					<div class="l r">
						<!-- 	<b class="iconfont icon-shoucang" /> -->
					</div>
				</div>
				<!-- <div v-if="!isMobile" class="item">{{ lang.nameBadges.step.sign }}: {{ baseModel.detailModel.productNumber }}</div> -->

				<div class="lySetpTableList">
					<LySetpTableList v-if="baseModel.loading" ref="lySetpTableList" :model="lySetpTableListModel" @change="lySetpTableListChange" @quantityInput="quantityInput" @textUploadDel="textUploadDel" @textUploadInput="textUploadInput" @textUploadImg="textUploadImg" @textUploadDelImg="textUploadDelImg" />
				</div>
				<div v-if="isMobile" id="swiperDesc" class="swiperDesc">
					<div class="rowOne">
						{{ lang.nameBadges.carousel.titleDesc }}
					</div>
					<div v-if="baseModel.byPIdModel.length > 0" class="rowTwo">
						<div v-html="getByPIdDom()" class="li"></div>
					</div>
				</div>

				<div id="subtotal" class="subtotal">
					<div class="subDetail">
						<div class="l">
							<div class="item">
								<div style="font-weight: bold; font-size: 18px; color: rgb(51, 51, 51)">{{ lang.nameBadges.summary.title }}</div>
							</div>
							<div class="item">
								<div class="itemLeft">{{ lang.nameBadges.summary.quantity }}</div>
								<div class="itemRight">
									<span v-if="summaryModel.moneyModel.quantity">{{ summaryModel.moneyModel.quantity }} pcs</span>
								</div>
							</div>
							<div class="item">
								<div class="itemLeft">{{ lang.nameBadges.summary.unitPrice }}</div>
								<div class="itemRight">
									<CCYRate :price="summaryModel.moneyModel.unitPrice"></CCYRate>
								</div>
							</div>
							<div class="item">
								<div class="itemLeft">{{ lang.nameBadges.summary.setupCharge }}</div>
								<div class="itemRight">
									<CCYRate :price="summaryModel.moneyModel.setupCharge"></CCYRate>
								</div>
							</div>
						</div>
						<div class="r">
							<div class="totalPriceBox">
								<strong class="subTotalText">{{ lang.nameBadges.summary.subtotal }} </strong>
								<label class="finalPrice">
									<CCYRate :price="summaryModel.moneyModel.subtotal"></CCYRate>
								</label>
							</div>
							<div>
								<freeTip />
								<button v-if="!isMobile" type="button" id="addCartBtn" class="" @click="addCart()">
									{{ lang.nameBadges.summary.button }}
								</button>
								<button v-if="isMobile" type="button" id="addCartBtn" class="buttonM" @click="addCart()">Add to Cart</button>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<!--移动端悬浮框-->
		<div v-if="isMobile" class="float" id="float" @click="canvasModel.isShow = false">Preview</div>
	</div>
</template>
<script>
import { initControlIcon } from "@/assets/js/fabricCore/initControlIcon";
import { initAligningGuidelines } from "@/assets/js/fabricCore/initAligningGuidelines.js";
import { initEraser } from "assets/js/fabricCore/eraser";
import initHotkeys from "assets/js/fabricCore/initHotKey";
import initCurvedText from "assets/js/fabricCore/initCurveText";
import { dieCut, ellipseTeXiao, rectTeXiao, roundedRectTeXiao, roundedSquareTeXiao, roundTeXiao, cust } from "assets/js/fabricCore/texiao1";
import { generateUUID, scrollToViewTop, loadImage, analyzeImageColor, rgbToHex } from "@/utils/utils";
import { getCateParamRelationByCateId, getByPId, getInfo } from "@/api/web";
import { otoAddCart } from "@/api/pins";

import { DataProcessing } from "@/utils/dataProcessing";
import { Common } from "@/utils/common";
import { calculateFunc } from "@/utils/system";

import { canvasModel, selectModel, lySetpTableListModel, baseModel, carouselModel, dictModel, summaryModel, colorModel } from "./entity/custom-name-badges";
import LySetpTableList from "@/components/Quote/Ly/LySetpTableList";
import { uploadFile } from "@/utils/oss";
import freeTip from "~/components/Quote/freeTip";
let fabric;
if (process.client) {
	fabric = require("fabric").fabric;
}
let that;

import publicCanvas from "@/assets/js/halfDesign/canvas";
export default {
	name: "",
	components: {
		LySetpTableList,
		freeTip,
	},
	head: {
		meta: [
			{
				name: "apple-mobile-web-app-capable",
				content: "yes",
			},
			{
				name: "viewport",
				content: "width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no",
			},
		],
	},
	data() {
		return {
			canvasModel: new canvasModel(),
			selectModel: new selectModel(),
			baseModel: new baseModel(),
			lySetpTableListModel: new lySetpTableListModel(),
			carouselModel: new carouselModel(),
			dictModel: new dictModel(),
			summaryModel: new summaryModel(),
			colorModel: new colorModel(),
			refs: "html2canvas",
		};
	},
	computed: {
		lang() {
			return this.$store.getters.lang.quote || {};
		},
		isMobile() {
			return this.$store.getters.isMobile;
		},
		isLogin() {
			return this.$store.getters.isLogin;
		},
		userId() {
			return this.$store.state.userInfo?.id;
		},
		userUUID() {
			return this.$store.state.userUUID;
		},
		proId() {
			return this.$store.state.proId;
		},
		device() {
			return this.$store.state.device;
		},
		//获取国家语言
		currency() {
			return this.$store.state.currency;
		},
	},

	async mounted() {
		this.isIframe = !!this.$route.query.type;
		if (process.client) {
			Common.init();
		}

		this.init();
		this.$nextTick(() => {
			this.initSwiper();
		});
		setTimeout(() => {
			this.baseModel.loading = true;
		}, 3000);
		that = this;
	},

	beforeDestroy() {
		if (this.baseModel.cssTime) {
			clearInterval(this.baseModel.cssTime); // 清除定时器
		}
	},
	methods: {
		//添加画布文字
		addT(val) {
			this.canvasModel.thisTextId = generateUUID();
			publicCanvas.addText(val, {
				id: this.canvasModel.thisTextId,
			});
			return this.canvasModel.thisTextId;
		},
		//添加画布图像
		addImg(val) {
			this.canvasModel.thisTextId = generateUUID();
			publicCanvas.addImg(val, {
				id: this.canvasModel.thisTextId,
				imgProperty: {
					/* left: 10,
					top: 10, */
				},
			});
			return this.canvasModel.thisTextId;
		},
		/**
		 * 画布文本修改
		 * 参数1：画布文字对象
		 * 参数2：要修改的字体对象
		 */
		editT(item, j) {
			publicCanvas.changeTextProperty({
				val: j.textHere,
				property: "text",
				item: item,
			});
			if (j.fontFamily) {
				publicCanvas.changeTextProperty({
					val: j.fontFamily,
					property: "fontFamily",
					item: item,
				});
			}
			if (j.bold) {
				publicCanvas.changeTextProperty({
					val: j.bold,
					property: "fontWeight",
					item: item,
				});
			}
			if (j.incline) {
				publicCanvas.changeTextProperty({
					val: j.incline,
					property: "fontStyle",
					item: item,
				});
			}
			if (j.color) {
				publicCanvas.changeTextProperty({
					val: j.color,
					property: "color",
					item: item,
				});
			}
			if (j.color) {
				j.code = j.color;
				publicCanvas.changeTextProperty({
					val: j,
					property: "fill",
					item: item,
				});
			}
		},
		//工作区域
		_initWorkspace(property) {
			const { width, height } = this.canvasModel.option;
			const workspace = new fabric.Rect({
				width: property?.width || width, // 优先使用传入的宽
				height: property?.height || height,
				strokeWidth: 1, // 设置边框宽度
				stroke: "red ", // 设置边框颜色
				id: "workspace",
				erasable: false,
			});
			workspace.set("selectable", false);
			workspace.set("hasControls", false);
			workspace.set("evented", false);
			this.canvasModel.c.add(workspace);
			this.canvasModel.c.renderAll();
			this.canvasModel.workspace = workspace;
		},
		_stickerShapeSendToBack() {
			const shape = this.getStickerShape();
			shape && shape.sendToBack();
		},
		_workspaceSendToBack() {
			const workspace = this.getWorkspace();
			workspace && workspace.sendToBack();
		},
		getWorkspace() {
			return this.canvasModel.c.getObjects().find((item) => item.id === "workspace");
		},
		getStickerShape() {
			return this.canvasModel.c.getObjects().find((item) => item.id === "editArea");
		},

		centerH(workspace, object) {
			return this.canvasModel.c._centerObject(object, new fabric.Point(workspace.getCenterPoint().x, object.getCenterPoint().y));
		},

		center(workspace, object) {
			const center = workspace.getCenterPoint();
			return this.canvasModel.c._centerObject(object, center);
		},

		centerV(workspace, object) {
			return this.canvasModel.c._centerObject(object, new fabric.Point(object.getCenterPoint().x, workspace.getCenterPoint().y));
		},
		//元素定位
		position(name) {
			const alignType = ["centerH", "center", "centerV"];
			const activeObject = this.canvasModel.c.getActiveObject();
			if (alignType.includes(name) && activeObject) {
				const defaultWorkspace = this.canvasModel.c.getObjects().find((item) => item.id === "workspace");
				if (defaultWorkspace) {
					this[name](defaultWorkspace, activeObject);
				}
				this.canvasModel.c.renderAll();
			}
		},
		//设置层级为最底部
		downTop() {
			const actives = this.canvasModel.c.getActiveObjects();
			if (actives && actives.length === 1) {
				const activeObject = this.canvasModel.c.getActiveObjects()[0];
				activeObject && activeObject.sendToBack();
				this.canvasModel.c.renderAll();
				this._stickerShapeSendToBack();
				this._workspaceSendToBack();
			}
		},
		//初始化贴纸形状
		async initStickerShape() {
			await loadImage(this.canvasModel.bgPath);
			let workspace = this.getWorkspace();
			const shape = this.getStickerShape();
			//删除原来的形状
			if (shape) {
				this.canvasModel.c.remove(shape);
			}
			//this.canvasModel.c.clear();

			let p = new fabric.Pattern({
				source: this.canvasModel.bgPath || "#fff",
				offset: { x: 0, y: 0 },
			});
			//canvasModel
			this.canvasModel.p = p;
			let type = this.canvasModel.stickerEffectType,
				shade,
				bgColor = "#fff",
				scale = this.canvasModel.stickerSize.w / this.canvasModel.stickerSize.h;
			new Promise((resolve) => {
				analyzeImageColor(this.canvasModel.bgPath, {
					ignoreArr: [],
					keepCount: 9,
					removeSimilarColors: true,
				}).then((r) => {
					r.map((item) => {
						item.hex = rgbToHex(item.color);
					});
					this.colorModel.list = r;
                    try {
                        this.$refs.lySetpTableList.filterColorList(this.colorModel.list);
                    }catch (e) {

                    }
					//1||矩形   2||椭圆  3||正方形  4||圆   5||自定义
					switch (type) {
						case "1":
							shade = roundedRectTeXiao(workspace, scale, bgColor, p);
							break;
						case "2":
							shade = ellipseTeXiao(workspace, scale, bgColor, p);
							break;
						case "3":
							shade = roundedSquareTeXiao(workspace, this.scaleFunc(this.canvasModel.stickerSize), bgColor, p);
							break;
						case "4":
							shade = roundTeXiao(workspace, this.scaleFunc(this.canvasModel.stickerSize), bgColor, p);
							break;
						case "5":
							let p1 = new fabric.Pattern({
								source: "https://static-oss.gs-souvenir.com/web/quoteManage/20240305/grid_2056nnW8sc_20240305jJ5d5T.png",
								offset: { x: 0, y: 0 },
								repeat: "repeat",
							});
							shade = cust(workspace, scale, bgColor, p1, p);
							break;
						/* case "6":
					shade = rectTeXiao(workspace, scale, bgColor, p);
					break; */
					}
					let clipPath = shade.group;
					this.canvasModel.c.add(clipPath);
					this.position("center");
					this.downTop();
					this.canvasModel.c.setActiveObject(clipPath);
					this.canvasModel.c.requestRenderAll(); //刷新
					this.canvasModel.c.discardActiveObject(); //取消反选
					this.$gl.hide();
					this.canvasUpdateLevel();
					clipPath.clone((cloned) => {
						//cloned.inverted = true;
						this.canvasModel.c.clipPath = cloned;
						setTimeout(() => {
							this.canvasModel.c.requestRenderAll();
							this.$gl.hide();
						}, 200);
					});
					resolve(r);
				});
			});
		},
		//干掉冗余图层
		canvasUpdateLevel() {
			let arr = this.canvasModel.c.getObjects();
			let objArr = [];
			arr.forEach((item, index) => {
				console.log("有多少对象", item, item.id);
				if (item.id != "workspace" && item.id != "editArea") {
					this.checkCanvasC((canvasC) => {
						canvasC.bringToFront(item);
					});
				} else {
					this.checkCanvasC((canvasC) => {
						canvasC.centerObject(item);
					});
				}
			});
		},
		//转换成比例
		scaleFunc(obj) {
			let { x } = obj;
			if (x.indexOf('"') > -1) {
				x = parseInt(x.slice(0, -1));
			}
			let b = x / 10;
			return 1 + b;
		},
		//初始化画布
		initCanvas() {
			let HideControls = {
				tl: false,
				tr: false,
				bl: false,
				br: false,
				ml: false,
				mt: false,
				mr: false,
				mb: false,
				mtr: false,
			};
			fabric.Object.prototype.set({
				borderColor: "#2C7DFA",
				borderDashArray: [0, 0],
				borderOpacityWhenMoving: 0.5,
				cornerSize: 25,
				padding: 0,
				objectCaching: false,
				statefullCache: false,
				centeredScaling: true,
			});
			fabric.Object.prototype.setControlsVisibility(HideControls);
			fabric.Canvas.prototype.getAbsoluteCoords = function (object) {
				var bound = object.getBoundingRect();
				return {
					left: bound.left + this._offset.left,
					top: bound.top + this._offset.top + bound.height,
				};
			};
			if (this.canvasModel.option.width == null) {
				return;
			}
			const canvas = new fabric.Canvas("fabricCanvas", {
				fill: "transparent",
				width: this.canvasModel.option.width,
				height: this.canvasModel.option.height,
				preserveObjectStacking: true,
				useWebGL: true,
				fireRightClick: true, // 启用右键，button的数字为3
				stopContextMenu: true, // 禁止默认右键菜单
				controlsAboveOverlay: true, // 超出clipPath后仍然展示控制条
			});
			canvas.selection = true; // 关闭选择框功能
			this.canvasModel.c = canvas;
			publicCanvas.c = canvas;
			initAligningGuidelines(canvas);
			initControlIcon(canvas);
			initEraser();
			initHotkeys(canvas);
			//初始化弯曲文本类
			initCurvedText(fabric);
			//初始化工作区域
			this._initWorkspace();
			//如果是sticker网站
			this.initStickerShape();
			//this.$gl.hide();
			/* fabricStage.on("object:moving", (e) => {
			});
			fabricStage.on("object:modified", (e) => {
			});
			fabricStage.on("object:scaling", (e) => {
			});
			fabricStage.on("object:removed", (e) => {
			}); */
			canvas.on("mouse:down", (e) => {
				setTimeout(() => {
					this.bindingData();
					this.bindingImg();
				}, 200);
			});
			canvas.on("before:selection:cleared", function (e) {
				var activeObject = canvas.getActiveObject();
				/* 	if (activeObject && activeObject.isEditing) {
					if (e) {
						e.preventDefault(); // 阻止选择框的出现
					}
				} */
			});

			return canvas;
		},
		//根据画布操作 回显绑定 图片
		bindingImg() {
			this.checkCanvasC((canvasC) => {
				let arr = canvasC.getObjects("image");
				let array = [];
				this.lySetpTableListModel.list.map((n) => {
					if (n.name === "Add Texts & Upload Image") {
						n.textUploadModel.imgList.map((m, mIndex) => {
							arr.map((o) => {
								if (o.id == m.id) {
									array.push(m);
								}
							});
						});
						if (array.length == 0) {
							array = [];
						}
						n.textUploadModel.imgList = array;
					}
				});
				this.$refs.lySetpTableList.refresh();
			});
		},
		//根据画布操作 回显绑定 文本上传步骤
		bindingData() {
			this.checkCanvasC((canvasC) => {
				let arr = canvasC.getObjects("i-text");
				let array = [];
				this.lySetpTableListModel.list.map((n) => {
					if (n.name === "Add Texts & Upload Image") {
						n.textUploadModel.textList.map((m, mIndex) => {
							arr.map((o) => {
								if (o.id == m.id) {
									array.push(m);
								}
							});
						});
						if (array.length == 0) {
							array.push(new canvasModel().textObject);
						}
						n.textUploadModel.textList = array;
					}
				});
				this.$refs.lySetpTableList.refresh();
			});
		},

		initCss() {
			this.$nextTick(() => {
				if (this.isMobile) {
					this.canvasModel.option = {
						width: 320,
						height: 320,
					};
				}
				this.initCanvas(); //动态自适应获取宽高. 万事俱备就初始化
				this.canvasModel.isShow = false;
			});
		},
		init() {
			this.$gl.show();
			this.baseModel.pid = 589;
			this.getByPIdFunc();
			this.getInfoFunc();
			this.initCss();
		},
		/************业务*******************/
		//计算校验 数量如果没有则 遮罩高亮数量步骤
		/* 	checkCalculate() {
			if (!this.baseModel.calculateModel?.quantity) {
				return false;
			}
			/* 	if (!this.baseModel.calculateModel?.sizeId) {
				console.log("引导size");
				return false;
			} //*

			return true;
		}, */
		//校验画布是否存在
		checkCanvasC(f) {
			let canvasC = this.canvasModel.c;
			if (canvasC) {
				if (f) {
					f(canvasC);
				}
			} else {
				this.initCss();
				setTimeout(() => {
					if (f) {
						f(this.canvasModel.c);
					}
				}, 500);
			}
		},
		//上传文本框删除事件
		textUploadDel(i, j) {
			this.checkCanvasC((canvasC) => {
				let arr = canvasC.getObjects("i-text");
				arr.map((n) => {
					if (n.id === j.id) {
						publicCanvas.delTextById(n.id);
					}
				});
			});
		},
		//图片上传--删除
		textUploadDelImg(i) {
			this.checkCanvasC((canvasC) => {
				let arr = canvasC.getObjects("image");
				if (arr.length != i.length) {
					arr.map((n) => {
						if (n.id == i.id) {
							publicCanvas.deImgById(n.id);
						}
					});
				}
			});
		},
		//图片上传回调
		//参数i: 当前上传步骤数据
		//参数j: 当前上传数组
		textUploadImg(i, j) {
			this.checkCanvasC((canvasC) => {
				if (j) {
					this.addImg(j.url);
					j.id = this.canvasModel.thisTextId;
				}
			});
		},
		//上传文本框输入事件
		textUploadInput(i, j) {
			this.checkCanvasC((canvasC) => {
				let arr = canvasC.getObjects("i-text");
				if (j.id) {
					arr.map((n) => {
						if (n.id === j.id) {
							this.editT(n, j);
							this.canvasUpdateLevel();
						}
					});
				} else {
					//添加逻辑

					if (i.length != arr.length) {
						j.id = this.addT(j.textHere);
					}
				}
			});
		},
		//监听数量.
		/* jiantingQuantity() {
			console.log("asd", this.summaryModel.moneyModel.subtotal);
			if (that.baseModel.calculateModel.quantity == "") {
				that.baseModel.calculateModel.quantity = 0;
			}
			if (that.summaryModel.moneyModel.subtotal == 0 && that.baseModel.calculateModel.quantity) {
				calculateFunc(that.baseModel.calculateModel, that.baseModel.calculateModel.object, that.baseModel.calculateModel.parentObject, (res, model) => {
					that.getSummaryModelFunc(res, model);
				});
			}
		}, */

		//数量回调
		quantityInput: Common.fd((i, j) => {
			that.baseModel.calculateModel.quantity = j.quantity;
			//	that.baseModel.calculateModel
			//if (that.baseModel.calculateModel.parentObject?.length > 0) {
			if (that.baseModel.calculateModel.quantity == "") {
				that.baseModel.calculateModel.quantity = 0;
			}
			calculateFunc(that.baseModel.calculateModel, that.baseModel.calculateModel.object, that.baseModel.calculateModel.parentObject, (res, model) => {
				that.getSummaryModelFunc(res, model);
			});
			//}
		}, 500),
		//轮播图缩略图点击事件
		swiperClick(item) {
			this.canvasModel.isShow = false;
		},
		//步骤回调
		lySetpTableListChange(i, j) {
			console.log("核心", i, j);
			//画布业务
			//显示画布
			if (i.name === "Select Material" || i.name === "Material" || i.name === "Select Shapes" || i.name === "Shapes" || i.name === "Select Size" || i.name === "Size" || i.name === "Select Color" || i.name === "Color") {
				//this.canvasModel.isShow = false;
				setTimeout(() => {
					if (i.name === "Select Material" || i.name === "Material") {
						this.canvasModel.isShow = false;
						this.baseModel.calculateModel = new baseModel().calculateModel; //选小分类时 重置之前保留的数据
						this.$nextTick(() => {
							this.initSwiper();
						});

						this.checkCanvasC((canvasC) => {
							publicCanvas.clearTextAndImg();
							canvasC.requestRenderAll();
						});
					}
					if (i.name === "Select Size" || i.name === "Size") {
						//this.canvasModel.isShow = true;
						if (j.paramCode) {
							let m = j.paramCode.split(":");
							if (m && m?.length > 1) {
								this.canvasModel.stickerSize.w = m[0];
								this.canvasModel.stickerSize.h = m[1];
							}
						}
						this.baseModel.cacheSize = {
							i,
							j,
						};
					}

					//第二步控制画布形状
					if (i.name === "Select Shapes" || i.name === "Shapes") {
						this.canvasModel.isShow = true;
						//1||矩形   2||椭圆  3||正方形  4||圆   5||自定义
						this.canvasModel.stickerSize = new canvasModel().stickerSize;
						if (j.paramCode == 3 || j.paramCode == 4) {
							this.canvasModel.stickerSize.w = 1;
							this.canvasModel.stickerSize.h = 1;
						}
						this.canvasModel.stickerEffectType = j.paramCode;
						//size归位选中第一条
						this.calculateBefore();
						//size归位选中第一条end

						this.lySetpTableListChange(this.baseModel.cacheSize.i, this.baseModel.cacheSize.j);
						//this.initStickerShape();
					}

					//回显x轴 y轴 //等接口查询完
					let sum = 0;
					let sumObj = {};
					this.lySetpTableListModel.list.map((i) => {
						if (i.name === "Select Size" || i.name === "Size") {
							i.children.map((j, jIndex) => {
								if (j.checked) {
									this.setXY(j.paramName);
									let str = j.paramName.split(" ");
									if (str.length > 0) {
										str = str[1];
										let str1 = str.split("×");
										if (str1.length > 0) {
											this.canvasModel.stickerSize.x = str1[1];
											this.canvasModel.stickerSize.y = str1[0];
										}
									}
									sum += 1;
								}
								if (jIndex == 0) {
									sumObj = j;
								}
							});
						}
					});
					if (sum == 0) {
						this.setXY(sumObj.paramName);
					}
					//回显x轴 y轴 end

					//第四步上颜色
					if (i.name === "Select Color" || i.name === "Color") {
						this.canvasModel.isShow = true;
						this.canvasModel.bgPath = new canvasModel().bgPath;
						let imgJson = JSON.parse(j.priceInfo.imagePath);
						if (imgJson?.length > 0) {
							let imgObj = imgJson[0];
							if (imgObj.path) {
								this.canvasModel.bgPath = imgObj.path;
							}
						}
						this.lySetpTableListChange(this.baseModel.cacheSize.i, this.baseModel.cacheSize.j);

						this.checkCanvasC((canvasC) => {
							/* publicCanvas.clearImgs();
							canvasC.requestRenderAll(); //刷新 */
						});
					}

					if (i.name !== "Select Material" || i.name !== "Material") {
						this.initStickerShape();
					} else {
						this.canvasModel.isShow = false;
					}
				}, 0);
			}
			//画布end

			//第一步轮播
			if (i.steplabel == "STEP 1") {
				if (this.baseModel.cateId == j.id) {
					return;
				}
				this.getCateParamRelationByCateIdFunc(j.id);
				this.baseModel.cateId = j.id;
				//轮播回显
				this.setSw(j.imageJsonJson);
			} else {
				//this.baseModel.calculateModel.cateId = n.priceInfo.id;
				this.calculate(i, j);
			}

			//第七步
			if (i.type == "TEXTUPLOAD") {
				//清空上传的图片和画布
				if (j.paramName === "Only Text") {
					i.textUploadModel.isUpload = false;
					i.textUploadModel.imgList = [];

					this.checkCanvasC((canvasC) => {
						publicCanvas.clearImgs();
						canvasC.requestRenderAll(); //刷新
					});
				} else {
					i.textUploadModel.isUpload = true;
				}
			}

			//第10步
			if (i.steplabel == "STEP 10") {
				this.baseModel.discountId = j.priceInfo?.id;
			}
		},
		setXY(s) {
			let str = s.split(" ");
			if (str.length > 0) {
				str = str[1];
				let str1 = str.split("×");
				if (str1.length > 0) {
					this.canvasModel.stickerSize.x = str1[1];
					this.canvasModel.stickerSize.y = str1[0];
				}
			}
		},

		//价格接口
		//参数i：当前步骤对象
		//参数j: 当前步骤选中对象
		calculate(i, j) {
			//size字典表获取
			this.getSize(i, j);
			//价格计算
			this.$nextTick(() => {
				//if (this.checkCalculate()) {
				if (j.parentObject && j.parentObject.paramType === "SIZE") {
					this.baseModel.calculateModel.sizeId = j.priceInfo.id;
				}
				if (!this.baseModel.calculateModel.sizeId) {
					//this.$toast.error(this.lang.nameBadges.error.size);
				}
				this.baseModel.calculateModel.cateId = this.baseModel.cateId;
				this.baseModel.calculateModel.object = j;
				if (j.parentObject) {
					this.baseModel.calculateModel.parentObject = j.parentObject.childList;
					if (this.baseModel.calculateModel.quantity == "") {
						this.baseModel.calculateModel.quantity = 0;
					}
					setTimeout(() => {
						calculateFunc(this.baseModel.calculateModel, j, j.parentObject.childList, (res, model) => {
							that.getSummaryModelFunc(res, model);
						});
					}, 200);
				}

				//}
			});
		},
		//价格计算之前
		calculateBefore() {
			let op = 0; //切形状能找到size       false||找不到size 则显示第一个
			let newID, oldId;
			this.lySetpTableListModel.list.map((i) => {
				if (i.name === "Select Size" || i.name === "Size") {
					i.children.map((j, jIndex) => {
						if (j.priceInfo.id != this.baseModel.calculateModel.sizeId) {
							op += 1;
						} else {
							oldId = j.priceInfo.id;
						}
						if (jIndex == 0) {
							newID = j.priceInfo.id;
						}
					});
					if (op == i.children.length) {
						this.baseModel.calculateModel.sizeId = newID;
					}
				}
			});

			this.lySetpTableListModel.list.map((i) => {
				if (i.name === "Select Size" || i.name === "Size") {
					i.children.map((j, jIndex) => {
						if (j.priceInfo.id == this.baseModel.calculateModel.sizeId) {
							j.checked = true;
							this.baseModel.cacheSize = {
								i: i,
								j: j,
							};
						} else {
							j.checked = false;
						}
					});
				}
			});
		},
		//获取价格
		getSummaryModelFunc(res, model) {
			this.baseModel.quotePriceParam = model;
			this.summaryModel.moneyModel = {
				quantity: res.data.totalQuantity,
				unitPrice: res.data.foundationUnitPrice,
				setupCharge: res.data.toolingCharge,
				subtotal: res.data.totalPrice,
				unit: "$",
			};
		},

		//根据字典表获取size
		getSize(i, j) {
			if (i.object.paramName === "Select Shapes" || i.object.paramName === "Shapes") {
				let thisArr = [];
				this.dictModel.sizeList.map((n) => {
					if (n.paramName === j.paramName) {
						thisArr = n.sizeList;
					}
				});

				let nEnd = [];
				let nEndIndex;
				this.lySetpTableListModel.list.map((n, nIndex) => {
					if (n.name === "Select Size" || n.name === "Size") {
						nEndIndex = nIndex;
						let nArr = n.object.childList;
						if (nArr?.length > 0) {
							nArr.map((m, mIndex) => {
								if (thisArr.includes(m.paramName)) {
									if (m.imageJson) {
										let imageJsonJson = JSON.parse(m.imageJson);
										if (imageJsonJson && imageJsonJson?.length > 0) {
											m.img = imageJsonJson[0].url;
											m.desc = m.paramName;
										}
									}
									nEnd.push(m);
								}
							});
						}
					}
				});
				if (nEnd.length > 0) {
					this.lySetpTableListModel.list[nEndIndex].children = nEnd;
				}
				this.$forceUpdate();
			}
		},

		//轮播赋值
		//参数1： 轮播图json数组  []
		setSw(arr) {
			if (arr?.length > 0) {
				arr.map((item) => {
					if (item.urlThumb == "") {
						item.urlThumb = item.url;
					}
				});
				this.carouselModel.list = arr;
				this.$forceUpdate();
			}
			return arr;
		},

		initSwiper() {
			this.myswiper1 = new Swiper(this.$refs.swiper1, {
				slidesPerView: 4,
				spaceBetween: 8,
				watchSlidesVisibility: true, //防止不可点击
				grabCursor: true,
			});
			this.myswiper2 = new Swiper(this.$refs.swiper2, {
				slidesPerView: 1,
				spaceBetween: 10,
				autoplay: false,
				grabCursor: true,
				observer: true,
				observeParents: true,
				thumbs: {
					swiper: this.myswiper1,
				},
				navigation: {
					nextEl: ".myswiper2 .swiper-button-next",
					prevEl: ".myswiper2 .swiper-button-prev",
				},
			});
		},

		//下拉
		stickerEffectTypeChange(e) {
			this.initStickerShape();
		},
		//数据处理
		getByPIdHandle(res) {
			res.data.map((item, index) => {
				item.desc = item.cateName;
				item.img = item.imagePhoto;

				if (item.imageJson) {
					item.imageJsonJson = JSON.parse(item.imageJson);
					if (index == 0) {
						item.checked = true;
						this.setSw(item.imageJsonJson);
					}
				}
			});
			//拼固定第一步
			this.lySetpTableListModel.listOne = {
				steplabel: "STEP 1",
				name: "Material",
				alias: "Material",
				css: {
					rowColumns: 3,
				},
				type: "NORMAL",
				mobileCss: {
					rowColumns: 2,
				},
				children: res.data,
			};
		},
		getInfoFunc() {
			getInfo({ id: this.baseModel.pid }).then((res) => {
				if (res) {
					this.baseModel.detailModel = res.data;
				}
			});
		},

		getByPIdFunc() {
			getByPId({ pid: this.baseModel.pid }).then((res) => {
				if (res) {
					this.getByPIdHandle(res);

					if (res.data?.length > 0) {
						this.getCateParamRelationByCateIdFunc(res.data[0].id);
						this.baseModel.cateId = res.data[0].id;
						this.baseModel.byPIdModel = res.data || [];
					}
				}
			});
		},
		//购物车业务处理
		setAddCartData() {
			this.$gl.show();
			let model = {
				currency: this.currency.code,
				uuid: this.isLogin ? null : this.userUUID,
				userId: this.isLogin ? this.userId : null,
				buyType: 7, //7全定制   9半定制
				isMobile: this.device === "mb" ? 1 : 0,
				isDs: 0, // 是否设计系统  1||是   0||否
				isFastQuote: 0, //默认0
				quoteCateId: this.baseModel.pid,
				quantity: undefined, //数量
				quoteCateChildId: undefined, //报价分类id  如 第一步的子分类
				quotePriceParam: this.baseModel.quotePriceParam, //calculate接口提交参数
				quoteParam: {
					classificationData: {}, //第一步选中的item   getByPid 这个就是分类
					fontData: {
						fontImgCustom: [
							//上传N张图片  + 1张画布base64生成的http格式图
							/* "https://static-oss.gs-souvenir.com/web/public/picStore/20241213/123_2049XpJs3e.png",
							"https://static-oss.gs-souvenir.com/web/public/picStore/20241213/66B257681074FEF18B8ED0ED3FBF931C_20497CCerB.gif"  */
						],
					},
					finaData: [], //步骤数组   删掉其他childList 只留选中的childList.   	过滤未加价的步骤.  如 Select Quantity   or  Add Texts & Upload Image
					textList: [], //画布文本编辑的数组
				},
			};
			let finaDataArray = []; //临时数据
			let finaDataArrayEnd = []; //临时数据
			this.lySetpTableListModel.list.map((i) => {
				if (i.type === "QUANTITY") {
					model.quantity = i.quantity;
				}
				if (i.name === "Select Material" || i.name === "Material") {
					i.children.map((j) => {
						if (j.checked) {
							model.quoteCateChildId = j.id;
							model.quoteParam.classificationData = j;
						}
					});
					finaDataArray.push(i); //记录过滤
				}
				if (i.type === "TEXTUPLOAD") {
					if (i.textUploadModel?.textList?.length > 0) {
						let obj = [];
						i.textUploadModel?.textList.map((j) => {
							j.fontWeight = j.bold;
							j.text = j.textHere;
							j.fontStyle = j.incline;
							if (j.id) {
								obj.push(j);
							}
						});
						model.quoteParam.textList = obj;
					}

					let imgArr = [];
					i.textUploadModel?.imgList.map((j) => {
						imgArr.push(j.url);
					});
					if (this.baseModel.canvasImg) {
						imgArr.push(this.baseModel.canvasImg);
					}
					model.quoteParam.fontData.fontImgCustom = imgArr;
				}
				if (i.object) {
					if (!(i.object.paramName === "Select Quantity" || i.object.paramName === "Quantity")) {
						finaDataArray.push(i); //记录过滤
					}
				}

				if (i.name === "Select Turnaround Time" || i.name === "Turnaround Time") {
					finaDataArray.push(i); //记录过滤
				}
			});
			finaDataArray.map((j, jIndex) => {
				let o = [];
				j.children.map((z, zIndex) => {
					if (z.checked) {
						o.push(z);
					}
				});
				if (o.length > 0) {
					finaDataArrayEnd.push({
						...j.object,
						childList: [o[0]],
					});
				}
			});
			//过滤第一步
			finaDataArrayEnd.map((n, nIndex) => {
				if (n.paramName == null && n.priceInfo == null) {
					finaDataArrayEnd.splice(nIndex, 1);
				}
			});

			//追加最后一步
			finaDataArrayEnd = DataProcessing.uniqueJSON(finaDataArrayEnd, "id");
			model.quoteParam.finaData = finaDataArrayEnd;
			console.log("不来再来伤害我", model);
			let m = {
				...model,
			};
			console.log("购物车", m);

			if (model.quoteParam) {
				const seen = new WeakSet();
				model.quoteParam.textList.map((item, index) => {
					delete model.quoteParam.textList[index].bold;
					delete model.quoteParam.textList[index].id;
					delete model.quoteParam.textList[index].incline;
					delete model.quoteParam.textList[index].textHere;
					delete model.quoteParam.textList[index].code;
				});

				m.quoteParam = JSON.stringify(model.quoteParam, (key, value) => {
					if (typeof value === "object" && value !== null) {
						if (seen.has(value)) {
							return; // 发现循环引用时返回 undefined
						}
						seen.add(value);
					}
					return value;
				});
			}

			if (model.quotePriceParam) {
				//阿文说数据量太大 要删掉没用字段
				delete model.quotePriceParam.object;
				delete model.quotePriceParam.parentObject;
				delete model.quotePriceParam.paramIdMapping;
				model.quotePriceParam.discountId = this.baseModel.discountId;
				model.quotePriceParam.proId = this.proId;
				m.quotePriceParam = JSON.stringify(model.quotePriceParam);
			}
			otoAddCart(m, this.baseModel.quotePriceParam).then((res) => {
				this.$gl.hide();
				if (res) {
					if (this.isIframe) {
						let targetWindow = window.opener || window.parent; // 目标窗口，可以是父窗口或打开当前窗口的窗口
						targetWindow.postMessage(
							{
								type: "toCart",
							},
							window.origin
						); // 发送消息
					} else {
						this.$router.push({
							path: "/cart",
						});
					}
				}
			});
		},

		//去购物车业务
		addCart() {
			if (this.$refs.lySetpTableList.checkedStep()) {
				this.checkCanvasC((canvasC) => {
					let arr = canvasC.getObjects("image");
					canvasC.discardActiveObject(); //取消反选
					canvasC.requestRenderAll(); //刷新
					//画布生成http图片
					this.cutpic();
					/* if (arr.length > 0) {
						//画布生成http图片
						this.cutpic();
					} else {
						//业务逻辑
						this.setAddCartData();
					} */
				});
			}
		},
		//拼接实体类后再拼css
		cateIdHandleCss(res) {
			this.lySetpTableListModel.list.map((i, iIndex) => {
				if (i.name === "Select Material" || i.name === "Material") {
					i.css.rowColumns = 3;
					i.mobileCss.rowColumns = 2;
				} else if (i.name === "Select Shapes" || i.name === "Shapes") {
					i.css.rowColumns = 5;
					i.mobileCss.rowColumns = 3;
				} else if (i.name === "Select Size" || i.name === "Size") {
					i.css.rowColumns = 3;
					i.mobileCss.rowColumns = 2;
				} else if (i.name === "Select Color" || i.name === "Color") {
					i.css.rowColumns = 5;
					i.mobileCss.rowColumns = 3;
				} else if (i.name === "Select Imprint Method" || i.name === "Imprint Method") {
					i.css.rowColumns = 3;
					i.mobileCss.rowColumns = 2;
				} else if (i.name === "Additional Upgrades (Optional)") {
					i.css.rowColumns = 3;
					i.mobileCss.rowColumns = 2;
				} else if (i.name === "Add Texts & Upload Image") {
					i.css.rowColumns = 3;
					i.mobileCss.rowColumns = 2;
				} else if (i.name === "Select Fastener Type" || i.name === "Fastener Type") {
					i.css.rowColumns = 3;
					i.mobileCss.rowColumns = 2;
				}
			});
			this.lySetpTableListModel.pid = this.baseModel.pid;
			/* setTimeout(() => {
				console.log("业务", this.lySetpTableListModel.list);
			}, 3000); */
		},
		//拼接实体类
		dataAppend(res) {
			this.lySetpTableListModel.list = new lySetpTableListModel().list;
			if (this.lySetpTableListModel.listOne) {
				this.lySetpTableListModel.list[0] = this.lySetpTableListModel.listOne;
			}
			res.data.map((i, iIndex) => {
				let childrenArray = [];
				let obj = {
					steplabel: "STEP " + i.stepIndex,
					name: i.paramName,
					alias: i.alias,
					css: {
						rowColumns: 5,
					},
					mobileCss: {
						rowColumns: 3,
					},
					type: i.paramType,
				};

				if (i.paramType === "NORMAL" || i.paramType === "SIZE") {
					obj = {
						...obj,
						//	children: i.childList,
					};
				} else if (i.paramType === "QUANTITY") {
					obj = {
						...obj,
						quantity: new baseModel().calculateModel.quantity,
					};
				} else if (i.paramType === "DISCOUNT") {
				}
				if (i.paramName === "Add Texts & Upload Image") {
					obj = {
						...obj,
						type: "TEXTUPLOAD", //文本和上传textUpload
						isEmail: false,
						textUploadModel: {
							textList: [new canvasModel().textObject],
							imgList: [],
							isUpload: true,
						},
					};
				}
				if (i.paramName === "Select Fastener Type" || i.paramName === "Fastener Type") {
					obj = {
						...obj,
						isPriceText: true,
					};
				}
				//拼title
				if (i.paramName === "Select Turnaround Time" || i.paramName === "Turnaround Time") {
					obj.title = this.lang.nameBadges.fontModel.title4;
				}
				if (i.paramName === "Additional Upgrades (Optional)") {
					obj.title = this.lang.nameBadges.fontModel.title5;
					obj.isPriceText = true;
				}
				//拼children
				if (i.childList) {
					i.childList.map((j) => {
						let b = {};
						if (j.imageJson) {
							let imageJsonJson = JSON.parse(j.imageJson);
							if (imageJsonJson && imageJsonJson?.length > 0) {
								b = {
									...j,
								};
								b.img = imageJsonJson[0].url;
								b.desc = j.paramName;
								childrenArray.push(b);
							}
						} else {
							if (j.paramType === "DISCOUNT") {
								b = {
									...j,
								};
								b.tip = j.paramName;
								b.name = "Standard Free";
								childrenArray.push(b);
							}
						}
					});
					obj.children = childrenArray;
				}
				obj.object = i;
				this.lySetpTableListModel.list.push(obj);
			});
		},
		getCateParamRelationByCateIdFunc(id) {
			getCateParamRelationByCateId({ cateId: id }).then((res) => {
				if (res) {
					let sortFun = res.data.sort(DataProcessing.getSortFun("asc", "stepIndex"));
					this.dataAppend(res);
					this.cateIdHandleCss(res);
					this.baseModel.loading = true;
					console.log("查询接口返回===================", res, this.lySetpTableListModel.list);
				}
			});
		},
		getByPIdDom() {
			let richText = "";
			this.baseModel.byPIdModel.map((i, iIndex) => {
				if (i.checked) {
					richText = i.richText;
				}
			});
			return richText;
		},
		/* 画布拼图片 */
		cutpic() {
			this.$gl.show();
			setTimeout(() => {
				let name = "demo" + +new Date();
				Common.cutpic(this, document, false, false, name, this.refs, (base64, blob) => {
					let file = Common.base64ToFile(base64, name + ".png");
					uploadFile(file).then((res) => {
						this.$gl.hide();
						this.baseModel.canvasImg = res;
						this.setAddCartData();
					});
				});
			}, 1000);
		},
	},
};
</script>
<style scoped lang="scss">
$color-red: #d24600;

@mixin centerCenter {
	display: flex;
	justify-content: center;
	align-items: center;
}
.quoteCustomNameBadges {
	padding: 40px 10px 10px !important;
	margin: 0 auto;
	//font-family: Calibri, Calibri;
	font-size: 14px;
	font-family: "Calibri", "Graphik Webfont", -apple-system, BlinkMacSystemFont, "Roboto", "Droid Sans", "Segoe UI", "Helvetica", Arial, sans-serif;
	@include respond-to(mb) {
		font-family: Helvetica, Arial, Helvetica, sans-serif;
	}
	.main {
		display: flex;
		width: 80%;
		margin-left: 10%;
		.mainL {
			flex: 5;
			width: 0;
			height: 100vh;
			position: sticky;
			top: 0%;
			z-index: 111;
			background: #fff;
			//overflow: hidden;
			.fabricCanvasDom {
				width: 100%;
				display: flex;
				justify-content: center;
			}
			.swiper-area {
				margin: 1.25rem 0;
				width: 100%;

				.myswiper2 {
					width: 90%;
					margin: 0 auto;
					position: relative;

					@include respond-to(mb) {
						width: 100%;
						height: 350px;
						.swiper {
							height: 100%;
						}
					}

					.swiper-slide {
						display: flex;
						justify-content: center;
						align-items: center;
						border: 1px dashed transparent;

						::v-deep .pic-img .img-container {
							height: 20rem;

							@include respond-to(mb) {
								height: auto;
							}
						}
					}

					.swiper-button-next::after,
					.swiper-button-prev::after {
						display: none;
					}

					.swiper-button-next,
					.swiper-button-prev {
						width: 3rem;
						@include respond-to(mb) {
							width: 3.58rem;
							img {
								background: rgba(0, 0, 0, 0.4);
								border-radius: 50%;
							}
						}
					}
				}
				.myswiper2Off {
					opacity: 0;
				}

				.myswiper1 {
					margin: 1.25rem auto 0;
					width: 70%;
					::v-deep .swiper-wrapper {
						justify-content: center;
						display: flex;
					}

					@include respond-to(mb) {
						padding: 0;
						width: 100%;
					}

					.swiper-slide {
						display: flex;
						justify-content: center;
						align-items: center;
						border-radius: 8px;
						//	border: 1px solid transparent;
						background-color: rgba(0, 0, 0, 0.25);
						box-sizing: border-box;
						cursor: pointer;
						width: 90.5px;
						margin-right: 8px;
						border: 1px dashed #f5f5f5;
						margin: 0;
						padding: 0;
						max-height: 80px;
						height: fit-content;

						@include respond-to(mb) {
							background-color: rgba(255, 255, 255, 0.25);
						}

						&.swiper-slide-thumb-active {
							border: 1px solid $color-red;
						}
					}
				}
			}

			.swiperDesc {
				margin-top: 30px;
				background: #fcfcfc;
				padding: 28px 30px 20px 30px;
				width: calc(100% - 40px);
				border-radius: 10px;
				.rowOne {
					color: #666666;
					//	font-size: 1em;
					font-size: 15px;
				}
				.rowTwo {
					margin-top: 20px;
					.title {
						color: #333333;
						font-weight: 700;
						font-size: 1.2em;
						margin-bottom: 10px;
					}
					.li {
						font-size: 1.1em;
						color: #333;
						margin-bottom: 5px;
					}
				}
			}

			@include respond-to(mb) {
				z-index: 1;
				height: auto !important;
				top: 0 !important;
				overflow: auto;
				padding-top: 0;
				.title {
					display: flex;
					color: #333333;
					margin-bottom: 10px;
					padding: 0 10px;
					.l {
						flex: 1;
						font-weight: 700;
						font-size: 1.8em;
					}
					.r {
						display: flex;
						justify-content: flex-end;
						font-weight: 400;
						font-size: 1.6em;
						align-items: center;
					}
				}
				.item {
					padding: 0 10px;
				}
			}
		}
		.mainR {
			flex: 5;
			padding: 0 5px;
			.title {
				display: flex;
				color: #333333;
				margin-bottom: 10px;
				padding: 0 15px;
				.l {
					flex: 1;
					font-weight: 700;
					font-size: 1.8em;
				}
				.r {
					display: flex;
					justify-content: flex-end;
					font-weight: 400;
					font-size: 1.6em;
					align-items: center;
				}
			}

			.item {
				font-size: 1.4em;
				color: #6292c1;
				font-weight: 700;
				padding: 0 15px;
			}
			.lySetpTableList {
				margin-top: 30px;
				::v-deep .lySetpTableList .main .step .stepContent {
					background: -o-linear-gradient(right, #c2392b, #d29014);
					background: -moz-linear-gradient(right, #c2392b, #d29014);
					background: linear-gradient(to right, #c2392b, #d29014);
				}
				::v-deep .lySetpTableList .main .step .stepContent::before {
					background: -o-linear-gradient(right, #c2392b, #d29014);
					background: -moz-linear-gradient(right, #c2392b, #d29014);
					background: linear-gradient(to right, #c2392b, #d29014);
					background: #d29014;
				}

				::v-deep .lySetpTableList .main .step .stepContent::after {
					border-left-color: #d29014;
				}
			}
			.swiperDesc {
				@include respond-to(mb) {
					margin-top: 20px;
					background: #f4f5f5;
					padding: 15px 10px 12px 10px;
					width: 100%;
					border-radius: 5px;
					.rowOne {
						color: #666666;
						font-size: 15px;
					}
					.rowTwo {
						margin-top: 15px;
						.title {
							color: #333333;
							font-weight: 700;
							font-size: 1.2em;
							margin-bottom: 10px;
						}
						.li {
							font-size: 1.1em;
							color: #333;
							margin-bottom: 5px;
						}
					}
				}
			}
			.subtotal {
				margin-top: 15px;
				font-size: 14px;
				padding: 0 15px;
				.subDetail {
					display: grid;
					grid-template-columns: repeat(2, 1fr);
					align-items: center;
					border-radius: 10px;
					padding: 20px;
					background: #f6f6f6;
					grid-gap: 20px;
					.l {
						.item {
							display: flex;
							align-items: center;
							margin-bottom: 10px;
							font-size: unset;
							color: #333;
							.itemLeft {
								flex-shrink: 0;
								white-space: normal;
								width: 110px;
								margin-right: 38px;
								font-size: 1em;
							}
						}
					}
					.r {
						text-align: center;
						.totalPriceBox {
							.subTotalText {
								font-weight: 400;
								color: #333;
								line-height: 36px;
							}
							.finalPrice {
								margin: 0 10px;
								font-size: 1.5em;
								font-weight: bold;
								color: #e6252e;
							}
						}
						.freeTip {
							display: flex;
							align-items: center;
							justify-content: flex-start;
							width: 100%;
							padding: 10px;
							b {
								color: #68bd2c;
								margin-right: 4px;
							}
							strong {
								margin-right: 5px;
							}
							@include respond-to(mb) {
								b {
									font-size: 18px;
									margin-top: -2px;
								}
							}
						}
						.freeTip > div {
							display: flex;
							justify-content: center;
							margin: 0 5px;
							flex: 1;
						}
						#addCartBtn {
							width: 100%;
							height: 3em;
							border: none;
							color: #fff;
							outline: none;
							min-width: 14em;
							line-height: 3em;
							font-weight: bold;
							font-size: 1.125em;
							border-radius: 6px;
							background: $color-red;
							text-transform: uppercase;
						}
						.buttonM {
							width: 70% !important;
							border-radius: 2px 2px 2px 2px !important;
							margin-top: 5px;
						}
					}
				}
				@include respond-to(mb) {
					width: 100vw;
					margin-left: -15px;
					border-radius: 0;
					padding: 0;
					.subDetail {
						background: #ebebeb;
						display: inline-block;
						width: 100%;
						border-radius: 0;
						.l {
							.item {
								.itemLeft {
									flex: 1;
								}
								.itemRight {
									flex: 1;
									display: flex;
									justify-content: flex-end;
								}
							}
						}
					}
				}
			}
			@include respond-to(mb) {
				height: auto;
				overflow: unset;
			}
		}

		@include respond-to(mb) {
			display: inline-block;
			margin-left: 0;
			width: calc(100% - 0px);
			.mainL {
				width: 100%;
				padding-top: 15px;
				position: sticky;
				top: 0;
				z-index: 1111;
				background: #fff;
				.title {
					margin-bottom: 5px;
					.l {
						font-size: 1.3em;
					}
					.r {
						b {
							font-size: 0.72em;
							font-weight: 700;
						}
					}
				}
				.item {
					font-size: 0.92em;
					color: #6292c1;
					font-weight: 700;
				}
			}
		}
	}
	@include respond-to(mb) {
		padding: 0 !important;
		.float {
			position: fixed;
			right: 15px;
			bottom: 30%;
			width: 80px;
			height: 80px;
			background: #eaf4fe;
			z-index: 1111;
			@include centerCenter;
			font-size: 1.2em;
			font-weight: 700;
			color: #98a0ab;
		}
	}
}
.fabricCanvas {
	position: relative;
	border-top: 2px solid #eee;
	border-left: 2px solid #eee;
	.x {
		position: absolute;
		bottom: 0;
		left: 0;
		width: 100%;
		height: 2px;
		background: #eee;
		display: flex;
		justify-content: center;
		line-height: 2;
	}
	.y {
		position: absolute;
		right: 0;
		top: 0;
		width: 2px;
		height: 100%;
		display: flex;
		align-items: center;
		padding-top: 5px;
		border-left: 2px solid #eee;
		padding-left: 0px;
	}
}
#fabricCanvas {
	//border: 1px solid yellow;
}
</style>
