<template>
	<div class="stepItem" :class="{ hasActive2: isActive2 }" :style="{ ...stepItemConfig?.stepItemStyle?.style, ...stepItemConfig?.stepItemStyle?.[`${nowDevice}Style`] }" @click="selectStep">
		<slot name="content">
			<div class="imgWrap" :style="{ ...stepItemConfig?.stepItemStyle?.stepImgWrapStyle?.style, ...stepItemConfig?.stepItemStyle?.[`${nowDevice}StepImgWrapStyle`]?.style }" :class="['imgBorderStyle' + stepItemConfig.imgBorderStyle]">
				<template v-if="hasVideo">
					<VideoPlayer :style="stepItemConfig.mediaConfig.style" ref="videos" :fit="stepItemConfig.videoFit" :options="getVideoOptions(isCateStep ? itemData.videoPath : itemData.priceInfo.videoPath, 4, isCateStep ? itemData.imagePhoto : itemData.priceInfo.imagePath)"> </VideoPlayer>
				</template>
				<template v-else>
					<img :style="stepItemConfig.mediaConfig.style" style="position: relative" loading="lazy" :src="imgUrl" :alt="imgAlt" />
				</template>
				<div class="zoomIcon extend-click-area" v-if="stepItemConfig.zoomIconConfig.showZoomIcon || hasVideo" :style="stepItemConfig.zoomIconConfig.style" @click.stop="zoom" @mouseover.prevent="handleHover" @mouseleave.prevent="handleMouseLeave">
					<i class="el-icon-zoom-in"></i>
				</div>
				<div class="playIcon" v-if="(stepItemConfig.playIconConfig.showPlayIcon || hasVideo) && isShowPlayIcon" :style="stepItemConfig.playIconConfig.style" @click.stop="zoom">
					<b class="icon-jxsht-3d-dbf"></b>
				</div>
			</div>
			<CustomCircle v-if="mbCustomCircle && !isActive2" :circle3Style="circle3Style" :circle-type="3"> </CustomCircle>
			<div class="textWrap" :style="{ ...stepItemConfig?.stepItemStyle?.stepTextWrapStyle?.style, ...stepItemConfig?.stepItemStyle?.[`${nowDevice}StepTextWrapStyle`]?.style }">
				<div class="ad" v-if="starJson.length && isCateStep">
					<template v-for="(ccitem, cindex) in starJson">
						<div class="ad-item" v-if="!(ccitem.key == 'Delivery' && device == 'mb')" :key="cindex">
							<span>{{ ccitem.key }}:</span>
							<Star v-model="ccitem.value"></Star>
							<strong>{{ ccitem.value }}</strong>
						</div>
					</template>
				</div>
				<div class="alias" :style="{ ...stepItemConfig?.stepItemStyle?.stepTextWrapStyle?.aliasStyle?.style, ...stepItemConfig?.stepItemStyle?.stepTextWrapStyle?.[`${nowDevice}AliasStyle`]?.style }">
					<CustomCircle v-if="!mbCustomCircle" :style="stepItemConfig?.stepItemStyle?.[`${nowDevice}StepTextWrapStyle`]?.customCircleStyle?.style" :circle-type="stepItemConfig?.stepItemStyle?.[`${nowDevice}StepTextWrapStyle`].customCircle || 1"> </CustomCircle>
					<span v-if="isCateStep">{{ itemData.cateNameQuote ? itemData.cateNameQuote : itemData.cateName }}</span>
					<span class="name" v-else>{{ itemData.alias ? itemData.alias : itemData.paramName }}</span>
					<ToolTip :itemData="itemData" ref="toolTip"></ToolTip>
				</div>
				<div class="price" v-if="showPriceText">
					<PriceText :paramData="itemData" :moldText="config.moldText" :css="config.priceTextCss"></PriceText>
				</div>
			</div>
			<CustomCircle v-if="mbCustomCircle && isActive2" :circle3Style="circle3Style" :circle-type="3"> </CustomCircle>
			<Corner v-if="itemData.labelText || isLabel(itemData)" :cssConfig="stepItemConfig.connerConfig" :backgroundColor="itemData.labelColor" type2 position="absolute">
				{{ itemData.labelText }}
			</Corner>
			<div class="previewImg_div" :class="(imgIndex + 1) % 3 === 0 ? 'previewImg_div2' : 'previewImg_div1'" v-if="showPreviewArea === true && previewImg">
				<div class="icon" @click.stop="showPreviewArea = false" v-if="device === 'mb'">
					<img src="https://static-oss.gs-souvenir.com/web/quoteManage/20250110/%E5%8F%89%E5%8F%B7_6_11_20250110p6emBe.png" style="width: 12px" alt="close" />
				</div>
				<img :style="stepItemConfig.mediaConfig.style" loading="lazy" :src="previewImg" alt="preview" />
			</div>
		</slot>
	</div>
</template>

<script>
import CustomCircle from "~/components/Quote/customCircle.vue";
import PriceText from "~/components/Quote/PriceText.vue";
import Corner from "~/components/Medals/Corner.vue";
import Star from "@/components/Quote/Star";
import VideoPlayer from "~/components/Quote/VideoPlayer.vue";
import ToolTip from "~/components/Quote/ToolTip.vue";
import { getPoster, getVideoUrl } from "assets/js/quote/quotePublic";
import {loadImage} from "@/utils/utils";

export default {
	data() {
		return {
			showPreviewArea: false,
		};
	},
	components: { ToolTip, VideoPlayer, Corner, Star, PriceText, CustomCircle },
	props: {
		type: {
			type: String,
			default: "step",
		},
		itemData: {
			type: Object,
			default: () => {
				return {};
			},
		},
		config: {
			type: Object,
			default: () => {
				return {};
			},
		},
		isShowPlayIcon: {
			type: Boolean,
			default: false,
		},
		imgIndex: {
			type: Number,
		},
	},
	computed: {
		previewImg() {
			try {
				return this.itemData.imageJson && this.parseJSON(this.itemData.imageJson)[1] ? this.parseJSON(this.itemData.imageJson)[1].url : "";
			} catch (e) {
				return "";
			}
		},
		previewModal() {
			//hover or click 默认是click
			return this.stepItemConfig.previewModal || "click";
		},
		showPriceText() {
			if (this.type === "step") {
				return this.stepItemConfig.showPriceText;
			} else if (this.type === "extendStep") {
				return this.stepItemConfig.showExtendStepPriceText;
			}
		},
		starJson() {
			let starJson = this.itemData.starJson;
			if (!starJson) {
				return [];
			}
			try {
				starJson = JSON.parse(this.itemData.starJson).filter((item) => item.value);
				return starJson;
			} catch (e) {
				return [];
			}
		},
		isCateStep() {
			return !!this.itemData.cateName;
		},
		hasVideo() {
			if (this.isCateStep) {
				let videoList = (this.itemData.videoPath && this.parseJSON(this.itemData.videoPath)) || [];
				videoList = videoList.filter((item) => item.path);
				return !!videoList.length;
			} else {
				let url = getVideoUrl(this.itemData.priceInfo.videoPath, this.proType);
				if (url === "[]") {
					return false;
				}
				return !!url;
			}
		},
		imgUrl() {
			if (this.isCateStep) {
				return this.itemData.imagePhoto;
			} else {
				return this.parseJSON(this.itemData.imageJson)[0].url;
			}
		},
		imgAlt() {
			if (this.isCateStep) {
				return this.itemData.cateName;
			} else {
				return this.parseJSON(this.itemData.imageJson)[0].alt;
			}
		},
		proType() {
			return this.$store.state.proType;
		},
		device() {
			return this.$store.state.device;
		},
		nowDevice() {
			return this.device === "mb" ? "mb" : "pc";
		},
		stepItemConfig() {
			return this.deepMerge(
				{
					imgBorderStyle: 1,
					showPriceText: true,
					mediaConfig: {
						style: {
							"object-fit": "contain",
						},
					},
					zoomIconConfig: {
						showZoomIcon: false,
						style: {
							color: "#ffffff",
						},
					},
					playIconConfig: {
						showPlayIcon: false,
						style: {
							color: "#ccc",
						},
					},
					connerConfig: {
						style: {
							top: 0,
							left: 0,
							right: "auto",
							transform: "translateY(0)",
						},
					},
					stepItemStyle: {
						style: {},
						pcStyle: {},
						mbStyle: {},
						stepImgWrapStyle: {
							style: {},
						},
						pcStepImgWrapStyle: {
							style: {},
						},
						mbStepImgWrapStyle: {
							style: {},
						},
						stepTextWrapStyle: {
							style: {},
						},
						pcStepTextWrapStyle: {
							style: {},
						},
						mbStepTextWrapStyle: {
							style: {},
						},
					},
				},
				this.config
			);
		},
		mbCustomCircle() {
			return this.stepItemConfig?.stepItemStyle?.[`${this.nowDevice}StepImgWrapStyle`]?.customCircle == 3;
		},
		circle3Style() {
			return this.stepItemConfig?.stepItemStyle?.[`${this.nowDevice}StepImgWrapStyle`]?.circle3Style;
		},
		isActive2() {
			return this.stepItemConfig?.[`${this.nowDevice}Active2`];
		},
	},
	methods: {
		handleHover(event) {
			if (this.previewModal === "click") {
				return;
			}
			event.stopPropagation();
			this.showPreviewArea = true;
		},
		handleMouseLeave(event) {
			if (this.previewModal === "click") {
				return;
			}
			event.stopPropagation();
			this.showPreviewArea = false;
		},
		zoom() {
			if (this.hasVideo) {
				console.log(321321)
				let videoType = this.isCateStep ? "video" : "additional";
				this.$emit("viewVideo", videoType);
			} else {
				if (this.previewModal === "hover") {
					return false;
				}
				this.$viewerApi({
					images: [this.imgUrl],
				});
			}
		},
		selectStep() {
			if (this.config?.clickStepShowTips) {
				let tipArr = document.querySelectorAll(".cusToolTip");
				tipArr.forEach((item) => {
					item.style.display = "none";
				});
				this.$refs.toolTip.showTooltip();
			}
			this.$emit("selectStep");
		},
		isLabel(j) {
			if (j.configJson) {
				j.configJsonJson = JSON.parse(j.configJson);
				j.labelColor = j.configJsonJson.tagTextColor2;
				j.labelText = j.configJsonJson.tagText_translate;
			}
			if (j.labelText) {
				return true;
			} else {
				return false;
			}
		},
		parseJSON(str) {
			return str
				? JSON.parse(str)
				: [
						{
							url: "",
						},
				  ];
		},
		getVideoOptions(path, type, poster) {
			let ph = getVideoUrl(path, this.proType);
			if (type === 1) {
				return {
					autoplay: true,
					controls: false,
					muted: true,
					loop: true,
					fill: true,
					sources: [
						{
							src: ph,
							type: "video/mp4",
						},
					],
				};
			} else if (type === 2) {
				return {
					autoplay: true,
					controls: true,
					muted: false,
					loop: true,
					fill: true,
					sources: [
						{
							src: ph,
							type: "video/mp4",
						},
					],
				};
			} else if (type === 3) {
				return {
					autoplay: true,
					controls: false,
					muted: true,
					loop: true,
					fill: true,
					sources: [
						{
							src: ph,
							type: "video/mp4",
						},
					],
				};
			} else if (type === 4) {
				let newPoster = getPoster(poster, this.proType);
				return {
					autoplay: false,
					controls: false,
					muted: true,
					loop: true,
					fill: true,
					sources: [
						{
							src: ph,
							type: "video/mp4",
						},
					],
					poster: newPoster,
				};
			}
		},
		deepMerge(target, source) {
			if (typeof source !== "object" || source === null) {
				return source;
			}
			if (typeof target !== "object" || target === null) {
				target = Array.isArray(source) ? [] : {};
			}
			if (Array.isArray(source)) {
				if (!Array.isArray(target)) {
					target = [];
				}
				source.forEach((sourceValue, index) => {
					const targetValue = target[index];
					if (sourceValue && typeof sourceValue === "object") {
						target[index] = this.deepMerge(targetValue, sourceValue);
					} else {
						target[index] = sourceValue;
					}
				});
			} else {
				Object.keys(source).forEach((key) => {
					const sourceValue = source[key];
					const targetValue = target[key];
					if (sourceValue && typeof sourceValue === "object") {
						target[key] = this.deepMerge(targetValue, sourceValue);
					} else {
						target[key] = sourceValue;
					}
				});
			}
			return target;
		},
	},
	mounted() {
		setTimeout(()=>{
			if(!this.previewImg) return;
			loadImage(this.previewImg);
		},3000)
	},
};
</script>

<style lang="scss" scoped>
.stepItem {
	position: relative;
	display: flex;
	flex-direction: column;
	justify-content: flex-start;
	align-items: center;
	cursor: pointer;
	min-width: 0;

	.previewImg_div {
		position: absolute;
		background: #fff;
		padding: 5px;
		border-radius: 10px;
		z-index: 2;
		box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.1);
		border: 1px solid #dbdbdb;
		width: 190px;
		height: 210px;

		img {
			border-radius: 6px;
		}

		&::before {
			content: "";
			width: 0;
			height: 0;
			transform: rotate(268deg);
			left: -21px;
			position: absolute;
			top: 50%;
			border: 10px transparent solid;
			border-bottom-color: #dbdbdb;
		}

		&::after {
			content: "";
			width: 0;
			height: 0;
			transform: rotate(268deg);
			left: -18px;
			position: absolute;
			top: 105px;
			border: 9px transparent solid;
			border-bottom-color: #fff;
		}
		@include respond-to(mb) {
			width: 100%;
			height: auto;

			&::after {
				top: 51%;
			}

			.icon {
				position: absolute;
				left: 85%;
				top: -6%;
				z-index: 4;
				padding: 4px 7px 5px 7px;
				border-radius: 50%;
				color: #fff;
				background-color: rgba(0, 0, 0, 0.5);
				width: 23%;
			}
		}
	}
	.previewImg_div2 {
		z-index: 3;
		left: -97%;

		&::before {
			transform: rotate(90deg);
			position: absolute;
			left: 100%;
			top: 50%;
		}

		&::after {
			transform: rotate(90deg);
			position: absolute;
			left: 187px;
			top: 105px;
		}
		@include respond-to(mb) {
			width: 100%;
			left: -108%;
			&::after {
				left: 99%;
				top: 51%;
			}
		}
	}
	.previewImg_div1 {
		right: -96%;
		@include respond-to(mb) {
			right: -108%;
			width: 100%;
		}
	}

	&.hasActive2 {
		.mbCustomCircle {
			position: absolute;
			bottom: -10px;
			left: 50%;
			transform: translate(-50%, 0);
		}
	}

	@media (any-hover: hover) {
		&:hover {
			.imgWrap {
				border-color: $color-primary !important;
			}

			.customCircle {
				border-color: $color-primary;
				background: $color-primary;

				&::after {
					background-color: #fff;
				}
			}

			.zoomIcon {
				color: $color-primary !important;
			}

			.alias {
				color: $color-primary;
			}
		}
	}

	&.active2Hover {
		border: 1px solid transparent;

		.imgWrap {
			border: none;
			border-color: transparent !important;
		}

		@media (any-hover: hover) {
			&:hover {
				border-color: $color-primary !important;

				.customCircle {
					border-color: $color-primary;
					background: $color-primary;

					&::after {
						background-color: #fff;
					}
				}

				.zoomIcon {
					color: $color-primary !important;
				}

				.alias {
					color: $color-primary;
				}
			}
		}
	}

	&.active {
		.imgWrap {
			border-color: $color-primary !important;
		}

		.customCircle {
			border-color: $color-primary;
			background: $color-primary;

			&::after {
				background-color: #fff;
			}
		}

		::v-deep .inner-circle {
			border-color: $color-primary;

			.customCircle.customCircle3 {
				background-color: #fff;
				border-color: $color-primary;

				&::after {
					background-color: $color-primary;
				}
			}
		}

		.zoomIcon {
			color: $color-primary !important;
		}

		.alias {
			color: $color-primary;
		}
	}

	&.active2 {
		border-color: $color-primary !important;

		.imgWrap {
			border: none;
		}

		.customCircle {
			border-color: $color-primary;
			background: $color-primary;

			&::after {
				background-color: #fff;
			}
		}

		::v-deep .inner-circle {
			border-color: $color-primary;

			.customCircle.customCircle3 {
				background-color: #fff;
				border-color: $color-primary;

				&::after {
					background-color: $color-primary;
				}
			}
		}

		.alias {
			color: $color-primary;
		}
	}

	.imgWrap {
		overflow: hidden;
		position: relative;
		width: 100%;
		border: 1px solid transparent;
		border-radius: 10px;
		transition: all 0.3s;

		@include respond-to(mb) {
			border-radius: 5px;
		}

		img {
			width: 100%;
			height: 100%;
			object-fit: contain;
		}

		video {
			width: 100%;
			height: 100%;
			object-fit: contain;
		}

		.zoomIcon {
			position: absolute;
			top: 5px;
			right: 5px;
			font-size: 24px;
			color: #ffffff;
			cursor: pointer;

			@include respond-to(mb) {
				font-size: 20px;
			}
		}

		.playIcon {
			position: absolute;
			bottom: 7px;
			right: 10px;
			font-size: 40px;
			color: #ffffff;
			cursor: pointer;

			@include respond-to(mb) {
				font-size: 20px;
			}
		}

		&:hover {
			.playIcon {
				display: none;
			}
		}
	}

	.imgWrap.imgBorderStyle2 {
		border-color: #afb1b3;
		border-style: dashed;
	}

	.textWrap {
		margin-top: 10px;

		@include respond-to(mb) {
			margin-top: 6px;
		}

		.ad {
			display: flex;
			align-items: flex-start;
			margin-bottom: 8px;

			@include respond-to(mb) {
				font-size: 12px;
				justify-content: center;
				gap: 5px;
			}

			.ad-item {
				display: flex;
				align-items: center;
				border-right: 1px solid #ccc;
				padding: 0 10px;

				@include respond-to(mb) {
					padding: 0 5px 0 0;
				}

				&:last-child {
					border-right: none;
				}
			}
		}

		.alias {
			text-align: center;

			.customCircle {
				display: inline-block;
				margin-right: 4px;
				vertical-align: -2px;
			}
		}
	}
}
</style>
