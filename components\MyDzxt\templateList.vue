<template>
	<div class="templateList" v-show="showTemplateList" @click.stop>
		<div class="content" scrollbar v-loadmore="loadTemplate" :infinite-scroll-disabled="disabledLoadTemplate">
			<div class="recent" v-if="recentTemplateList.length > 0">
				<div class="recent-title">
					{{ lang.recentlyUsed }}
				</div>
				<div class="listWrap recentList" :class="{ style5: styleConfiguration == 5, style3: styleConfiguration == 3 }">
					<div class="list-item" v-for="item in recentTemplateList" :key="item.id" :title="item.templateName">
						<templates :item="item" :templateImg="item.templateImg" :isFavourite="item.isFavourite" @selectTemplate="selectTemplateHistory" @toggleLoveTemplate="toggleLoveTemplate"> </templates>
					</div>
				</div>
			</div>
			<div class="listWrap" :class="{ noData: !templateList.length, style5: styleConfiguration == 5, style3: styleConfiguration == 3 }">
				<div class="list-item" v-for="item in templateList" :key="item.id" :title="item.templateName">
					<templates :item="item" :templateImg="item.templateImg" :isFavourite="item.isFavourite" @selectTemplate="selectTemplate" @toggleLoveTemplate="toggleLoveTemplate"> </templates>
				</div>
				<noResult v-if="!templateList.length && !loadingTemplate"></noResult>
			</div>
		</div>
		<div class="loadMore" v-show="loadingTemplate">{{ lang.loading }}...</div>
	</div>
</template>

<script>
import { addTemplateHistory, getTemplateHistory, getTemplateListFormApp, favoriteTemplate, getTemplateById } from "@/api/newDzxt";
import dzMixin from "@/mixins/dzMixin";
import templates from "@/components/MyDzxt/templates.vue";
import noResult from "@/components/MyDzxt/noResult.vue";

export default {
	mixins: [dzMixin],
	data() {
		return {
			selectNumber: 1,
			loadingTemplate: false,
			templateForm: {
				page: 1,
				pageSize: 60,
				templateTypeId: null,
				total: 0,
				pages: 1,
				searchWord: "",
			},
			templateList: [],
			recentTemplateList: [],
		};
	},
	components: {
		noResult,
		templates,
	},
	watch: {
		isLogin() {
			this.getTemplateList();
			this.getTemplateHistory();
		},
	},
	computed: {
		lang() {
			return this.$store.getters.lang?.design;
		},
		showTemplateList() {
			return this.$store.state.design.showTemplateList;
		},
		noMoreTemplate() {
			return this.templateForm.page >= this.templateForm.pages;
		},
		disabledLoadTemplate() {
			return this.loadingTemplate || this.noMoreTemplate;
		},
		status() {
			return this.$store.state.design.status;
		},
		isLogin() {
			return this.$store.getters.isLogin;
		},
		userId() {
			return this.$store.state.userInfo?.id;
		},
		styleConfiguration() {
			return this.$store.state.design.pageInfo.styleConfiguration;
		},
	},
	methods: {
		loadTemplate() {
			this.templateForm.page++;
			this.getTemplateList("scroll");
		},
		saveHistory(id) {
			return new Promise((resolve) => {
				addTemplateHistory({
					userId: this.userId,
					templateId: id,
					categoryId: this.$store.state.design?.pageInfo?.id,
				}).then(() => {
					resolve();
				});
			});
		},
		selectTemplateHistory(item) {
			this.selectTemplate({
				id: item.templateId,
				isHistory: true,
			});
		},
		async selectTemplate(item) {
			this.canvas.templateName = item.templateName;
			let fn = () => {
				if (!this.isLogin || item.isHistory) {
					return false;
				}
				this.saveHistory(item.id).then(() => {
					this.getTemplateHistory();
				});
			};
			let loadData = () => {
				this.$store.commit("design/set_loading", true);
				this.$Bus.$emit("showDefault");
				fn();
				getTemplateById({
					id: item.id,
				})
					.then(async (res) => {
						//判断是否是光板
						if (res.data.isPaintless === 1) {
							await this.canvas.replaceBase(res.data.templateFile);
							this.canvas.c.requestRenderAll();
						} else {
							if (this.canvas.isSticker) {
								let specification = JSON.parse(res.data.specification);
								this.canvas.stickerSize = {
									w: specification.width,
									h: specification.height,
								};
							}
							await this.canvas.loadTemplate(res.data.templateFile);
						}
						this.$store.commit("design/set_loading", false);
					})
					.catch((err) => {
						this.$store.commit("design/set_loading", false);
					});
			};
			if (this.selectNumber === 1) {
				loadData();
				this.selectNumber++;
				return;
			}

			if (this.canvas.isEmptyCanvas()) {
				loadData();
			} else {
				this.$confirm(this.lang.areTemplate, this.lang.hint, {
					confirmButtonText: this.lang.comfirm,
					cancelButtonText: this.lang.cancel,
					type: this.lang.warning,
				})
					.then(() => {
						loadData();
					})
					.catch(() => {});
			}
		},
		toggleLoveTemplate(item) {
			if (!this.isLogin) {
				this.$store.commit("setLogin", "login");
				return false;
			}
			let templatesId;
			if (item.types === null) {
				templatesId = item.templateId;
			} else {
				templatesId = item.id;
			}
			favoriteTemplate({
				categoryId: this.$store.state.design?.pageInfo?.id,
				templatesId: templatesId,
			}).then(() => {
				this.updateTemplateList({
					templatesId: templatesId,
				});
			});
		},
		getTemplateList(type = "select") {
			this.loadingTemplate = true;
			getTemplateListFormApp(
				Object.assign({}, this.templateForm, {
					categoryId: this.$store.state.design?.pageInfo?.id,
					quoteName: this.$store.state.design?.pageInfo?.quoteCateName,
					userId: this.userId,
				})
			)
				.then((res) => {
					if (res.data) {
						if (type === "scroll") {
							this.templateList = this.templateList.concat(res.data.records);
							this.templateForm.total = res.data.total;
							this.templateForm.pages = res.data.pages;
						} else {
							this.templateList = res.data.records;
							this.templateForm.total = res.data.total;
							this.templateForm.pages = res.data.pages;
						}
					} else {
						this.templateList = [];
						this.templateForm.total = 0;
						this.templateForm.pages = 0;
					}
				})
				.finally(() => {
					this.loadingTemplate = false;
				});
		},
		selectTemplateCategory(item) {
			this.templateList = [];
			this.templateForm.page = 1;
			this.templateForm.templateTypeId = item.id;
			this.templateForm.searchWord = "";
			this.getTemplateList();
		},
		getTemplateHistory() {
			if (!this.isLogin) {
				return false;
			}
			getTemplateHistory({
				userId: this.userId,
				categoryId: this.$store.state.design?.pageInfo?.id,
			}).then((res) => {
				this.recentTemplateList = res.data;
			});
		},
		searchTemplate(val) {
			this.templateList = [];
			this.templateForm.page = 1;
			this.templateForm.templateTypeId = null;
			this.templateForm.searchWord = val;
			this.getTemplateList();
		},
		updateTemplateList(data) {
			let findItem = this.templateList.find((item) => {
				return item.id === data.templatesId;
			});
			if (findItem) {
				findItem.isFavourite = !findItem.isFavourite;
			}
			this.getTemplateHistory();
		},
	},
	mounted() {
		this.getTemplateHistory();
		this.getTemplateList();
		this.$Bus.$on("updateTemplateList", this.updateTemplateList);
		this.$Bus.$on("selectTemplateCategory", this.selectTemplateCategory);
		this.$Bus.$on("searchTemplate", this.searchTemplate);
	},
	beforeDestroy() {
		this.$Bus.$off("updateTemplateList", this.updateTemplateList);
		this.$Bus.$off("selectTemplateCategory", this.selectTemplateCategory);
		this.$Bus.$off("searchTemplate", this.searchTemplate);
	},
};
</script>

<style scoped lang="scss">
@import "@/assets/css/dzxt_theme";

.templateList {
	display: flex;
	flex-direction: column;
	overflow: hidden;
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	padding: 10px 30px;
	background-color: #e6e6e6;
	z-index: 2;

	.content {
		display: flex;
		flex-direction: column;
		flex: 1;
		overflow: hidden auto;
	}

	.loadMore {
		position: absolute;
		left: 50%;
		bottom: 5px;
		transform: translateX(-50%);
		text-align: center;
	}

	.recent {
		margin-bottom: 10px;
		height: auto;
		background-color: #ffffff;

		.recent-title {
			padding: 10px 20px 0;
		}
	}

	.listWrap {
		display: grid;
		align-content: flex-start;
		padding: 20px;
		flex: 1;
		background-color: #ffffff;

		&.noData {
			display: flex;
			justify-content: center;
			align-items: center;
		}

		&.style5 {
			grid-template-columns: repeat(5, 1fr);

			.list-item {
				aspect-ratio: 1;
			}
		}

		&.style3 {
			grid-template-columns: repeat(3, 1fr);
		}
	}

	.listWrap.recentList {
		height: auto;
	}
}

.templateList {
	.listWrap {
		grid-template-columns: repeat(3, 1fr);
		grid-column-gap: 20px;
		grid-row-gap: 20px;

		.list-item {
			aspect-ratio: 382/215;
		}
	}
}

.listWrap2 {
	grid-template-columns: repeat(5, 1fr);
	grid-column-gap: 20px;
	grid-row-gap: 20px;

	.list-item {
		aspect-ratio: 382/215;
	}
}
</style>
