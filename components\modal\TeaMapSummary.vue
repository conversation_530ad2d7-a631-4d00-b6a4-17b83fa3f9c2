<template>
	<div class="modal-box summary-box" :class="modal.class" :style="modal.style">
		<div flex center v-for="(o, oi) in modal.outer" :key="oi" :style="modal.boxStyle"
			:class="{ position: modal.titlePosition }">

			<!-- mb -->
			<EditDiv :tagName="o.title.tagName || 'h2'" v-model:content="o.title.value" class="title"
				:style="{ ...modal.titleStyle, ...o.title.style }" @click="setModalType(o.title, modal.outer, 'text')"
				v-if="o.title?.value && (($store.getters.isMobile && !modal.titlePosition) || (!$store.getters.isMobile && modal.titlePosition))">
			</EditDiv>


			<pic class="pic" :src="o.img.value" :style="{ ...modal.imgStyle, ...o.img.style }" v-if="o.img"
				:alt="o.img.alt" @click="setModalType(o.img, modal.outer, 'img')" />

			<VideoPlayer class="pic" :videoUrl="o.video.value" v-if="modal.theme < 4 && o.video" :title="o.video?.alt"
				:style="{ ...modal.imgStyle, ...o.video.style }" @click="setModalType(o.video, modal.outer, 'video')">
			</VideoPlayer>

			<video class="pic" :src="o.video.value" muted loop playsinline controls v-else-if="o.video"
				:title="o.video?.alt" :style="{ ...modal.imgStyle, ...o.video.style }"
				@click="setModalType(o.video, modal.outer, 'video')">
			</video>


			<div class="content" :style="modal.contentStyle">
				<!-- pc -->
				<EditDiv :tagName="o.title.tagName || 'h2'" v-model:content="o.title.value"
					:style="{ ...modal.titleStyle, ...o.title.style }"
					@click="setModalType(o.title, modal.outer, 'text')"
					v-if="o.title?.value && ((!$store.getters.isMobile && !modal.titlePosition) || ($store.getters.isMobile && modal.titlePosition))">
				</EditDiv>

				<div class="tag-content" v-if="modal.tagOption.tagType == 'text'" :style="{ ...modal.tagContentStyle }">
					<div v-for="(l, li) in modal.list" @click="modal.tagOption.tagEvent && changeTag(l, li)" :key="li"
						style="position:absolute" :style="{ left: l.x, top: l.y, ...modal.tagOption.tagStyle }">
						<span v-if="l.subTitle">
							<EditDiv v-model:content="l.subTitle.value"
								@click="setModalType(l.subTitle, modal.list, 'text')" />
						</span>
						<span v-if="l.text">
							<EditDiv v-model:content="l.text.value"
								@click="setModalType(l.subTitle, modal.list, 'text')">
							</EditDiv>
						</span>
					</div>
				</div>
				<div class="tea-content" :style="{ ...modal.teaContentStyle }">
					<div v-if="modal.tagOption.tagType == 'icon'" :style="{ ...modal.tagContentStyle }">
						<b v-for="(l, li) in modal.list" :class="modal.tagOption.tagIcon" :key="li"
							style="position:absolute"
							:style="{ left: l.x, top: l.y, ...modal.tagOption.tagStyle, ...(currentIndex === li ? modal.tagOption.tagActStyle : {}) }"
							@click="changeTag(l, li)"></b>
					</div>
					<div :style="modal.teaCardContentStyle">
						<div class="tea-subTitle" :style="{ ...modal.subTitleStyle }"
							@click="setModalType(currentSubTitle, modal.list, 'text')">
							<b v-if="modal.subTitleIcon" :class="modal.subTitleIcon.icon"
								:style="modal.subTitleIcon.style"></b>
							<EditDiv v-model:content="currentSubTitle.value" />
						</div>
						<EditDiv v-model:content="currentText.value" class="tea-text" :style="{ ...modal.textStyle }"
							@click="setModalType(currentText, modal.list, 'text')" />
					</div>
				</div>

				<button v-if="o.button" :primary="o.button.value" :style="{ ...modal.btnStyle, ...o.button.style }"
					:title="o.button.alt">
					<EditDiv v-show="o.button.value" tagName="label" v-model:content="o.button.value"
						@click="setModalType(o.button, modal.outer, 'button', o.button)" />
					<b :class="o.button.icon" v-show="o.button.icon"></b>
				</button>
			</div>


		</div>
	</div>
</template>

<script>
export default {
	name: "modalTeaMapSummary",
	props: {
		data: {
			type: Object,
			default: {},
		}
	},
	data() {
		return {
			modal: {
				style: {},
				type: {},
				outer: [{}],
				...this.data
			},
			currentIndex: 0,
			currentSubTitle: {},
			currentText: {}
		};
	},
	watch: {
		modal: {
			handler(val) {
				if (process.env.isManage) this.$emit("update:data", val);
			},
			immediate: true,
			deep: true
		}
	},
	methods: {
		setModalType(target, targetArray, clickType, event, other) {
			this.$setModal(this, target, targetArray, clickType, event, other)
		},
		changeTag(val, index) {
			this.currentIndex = index,
				this.currentSubTitle = val.subTitle
			this.currentText = val.text
		}
	},
	beforeMount() {
		if (this.modal.tagOption && this.modal.tagOption.tagEvent) {
			this.currentIndex = this.modal.tagOption?.defActiveIndex
			this.currentSubTitle = this.modal.list[this.currentIndex].subTitle
			this.currentText = this.modal.list[this.currentIndex].text
		}

	},
	mounted() {
		this.$nextTick(() => {
			let viedo = document.querySelector(`#${this.modal.id} video`);
			if (viedo) document.addEventListener("scroll", () => {
				if (viedo.play && viedo.getBoundingClientRect && (viedo.getBoundingClientRect().top < window.innerHeight)) viedo.play();
			})
		});

	}
};
</script>

<style lang="scss" scoped>
.summary-box {
	// position: relative;
	z-index: 1;

	>.position {
		flex-wrap: wrap;

		.title {
			width: 100%;
			margin-bottom: 1em;
		}
	}

	.pic {
		width: 43%;
		overflow: hidden;
		border-radius: 10px;
	}

	.content {
		flex: 1;
		margin: 0 1.5vw;

		h1,
		h2 {
			text-align: inherit;
		}

		>div {
			margin-top: 20px;
			line-height: 1.4em;
		}

		button {
			margin-top: 2em;
		}
	}
}


.h2-line .content div:nth-child(1) {
	gap: 1.1vw;
	display: flex;
	align-items: center;
	justify-content: center;

	&::before,
	&::after {
		all: unset;
		content: "";
		flex: 1;
		border-top: 1px solid #333333;
	}
}


@media screen and (max-width: $mb-width) {
	.summary-box {
		width: 100vw;
		overflow: hidden;
		padding-left: 0 !important;
		padding-right: 0 !important;

		>div {
			display: block;
			text-align: center;
		}

		h1 {
			font-size: 5.6vw;
		}

		h1,
		h2 {
			margin-bottom: 1.4em;
		}

		.pic {
			width: 100%;
			border-radius: 0;
		}
	}

	[theme='4'] {
		z-index: auto;
	}
}

// ipad端样式
@media screen and (max-width: $pad-width) {
	.tea-hover .card {
		flex-direction: column;

		.content {
			overflow-y: auto;
		}
	}
}

@media screen and (max-width: $pad-width) and (min-width: $mb-width) {
	.tea-content {
		div {
			&:nth-child(1) {
				margin: 0 auto;
			}
		}

		flex-direction: column;
		gap: 0 !important;
		padding-left:0 !important
	}

	.teaCultureMap {
		.tea-content {
			.tea-subTitle {
				margin-top: 0% !important;
			}

			.tea-text {
				width: 60% !important;
			}
		}
	}

	.teaCultureBanner {
		.tag-content {
			left: 52% !important;
		}
	}
}


.tea-story>div {
	&:nth-child(even) {
		justify-content: flex-end;

		img {
			order: 1;
			margin-right: 0 !important;
		}
	}

	&:nth-child(odd) {
		.content {
			margin-right: 0 !important;
		}
	}
}
</style>
