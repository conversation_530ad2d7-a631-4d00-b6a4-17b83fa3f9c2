import {request} from '~/utils/request'

//获取token
export function getToken(data, headers) {
	return request({
		url: 'https://api-m.sandbox.paypal.com/v1/oauth2/token',
		method: 'post',
		data,
		headers: headers,
	})
}

//授权
export function getPermission(data, headers) {
	return request({
		url: 'https://api-m.sandbox.paypal.com/v2/customer/partner-referrals',
		method: 'post',
		data,
		headers: headers,
	})
}

//获取推荐数据
export function getData(url,headers) {
	return request({
		url,
		method: 'get',
		headers: headers,
	})
}

//获取token
export function getPaypalToken(data) {
	return request({
		url:'http://192.168.1.18:55501/gypayment/pay/paypal-token',
		method: 'post',
		data
	})
}


