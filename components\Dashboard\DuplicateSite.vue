<template>
	<v-dialog
		:value="value"
		@input="$emit('input',false)"
		max-width="523"
	>
		<v-card>
			<v-card-title class="text-h5">
				Duplicate Site
			</v-card-title>
			<div class="px-6">
				<div style="font-size:18px;">
					Would you like to make a copy of <strong>mable bb</strong>?
				</div>
				<div style="font-size: 15px;color:#999999">
					<div>
						Site name
					</div>
					<v-text-field
						class="mb-5 mt-3"
						solo
						flat
						outlined
						dense
						hide-details
						label="mable bb Copy"
					></v-text-field>
					<div>
						Note: When you duplicate a site, some business-related content such as store orders, contacts, invoices and third party app settings will not be included.
					</div>
				</div>
			</div>
			<v-card-actions class="justify-center pb-6">
				<v-spacer></v-spacer>
				<v-btn depressed color="primary" outlined width="183" @click="$emit('input',false)">
					Cancel
				</v-btn>

				<v-btn color="primary" depressed class="ml-3" width="183" @click="$emit('input',false)">
					Duplicate
				</v-btn>
			</v-card-actions>
		</v-card>
	</v-dialog>
</template>

<script>
export default {
	props: ['value'],
	model: {
		prop: "value",
		event: "input"
	},
}
</script>

<style scoped>

</style>
