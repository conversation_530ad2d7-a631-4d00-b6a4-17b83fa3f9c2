<template>
	<div class="modal-box" :class="modal.class" :style="modal.style">
		<template v-for="o in modal.outer">

			<EditDiv v-if="o.title?.value" tagName="h2" v-model:content="o.title.value" :style="modal.titleStyle"
				@click="setModalType(o.title, modal.outer, 'text')" />

			<div class="sub-title" :style="modal.subTitleStyle" :pointer="o.subTitle?.event"
				:hidden="!o.title?.value && !o.subTitle?.value">
				<EditDiv v-if="o.subTitle?.value" v-model:content="o.subTitle.value"
					@click="setModalType(o.subTitle, modal.outer, 'text', o.subTitle.event)" />
			</div>

			<EditDiv v-if="o.text?.value" v-model:content="o.text.value"
				:style="{ ...modal.textStyle, ...o.text.style, ...o.textStyle }"
				@click="setModalType(o.text, modal.outer, 'text')" />


			<div :style="modal.boxStyle">
				<PicVideo v-if="o.img?.value" :modal="modal" :data="o" data-source="outer"
					:pic-style="{ ...modal.imgStyle, ...o.img.style }" @setModalType="setModalType" />

				<div flex class="tab-box" :style="modal.tabBoxStyle" v-if="o.tabList"
					@click="setModalType(o.tabList, modal.outer, 'nav_list')">
					<h3 flex pointer v-for="(t, ti) in o.tabList" @click="tabIndex = ti" :primary="tabIndex == ti"
						:style="tabIndex == ti ? modal.tabSeleStyle : modal.tabStyle">
						<b :class="t.icon" :style="tabIndex == ti ? modal.tabIconSeleStyle : modal.tabIconStyle"></b>
						{{ t.value }}
					</h3>
				</div>

				<div class="card-box" :style="modal.cardStyle">
					<div class="card part2 hover-tag" v-for="(l, li) in modal.list" :key="li"
						:class="{ sele: modal.faqIndex == li }" v-show="!o.tabList || l.tab == tabIndex"
						:style="{ ...modal.contentStyle, ...l.contentStyle }" :childHoverIndex="li"
						@click="modal.faqIndex = (modal.faqIndex == li ? -1 : li)">

						<div flex class="content-title" :style="modal.cardTitleBoxStyle">
							<b primary class="icon" v-if="l.title?.icon || l.subTitle?.icon"
								:style="modal.cardTitleIconStyle">{{ l.title?.icon || l.subTitle?.icon }}</b>

							<EditDiv v-if="l.title?.value" :tagName="l.title.tagName || 'h3'"
								v-model:content="l.title.value" :style="modal.cardTitleStyle"
								@click="setModalType(l.title, modal.list, 'text')" />

							<EditDiv v-model:content="l.subTitle.value" :style="modal.cardSubTitleStyle"
								v-if="l.subTitle?.value" @click="setModalType(l.subTitle, modal.list, 'text')" />

							<b class="change" :hidden="modal.showAll" :style="modal.changeStyle"
								:class="modal.faqIndex == li ? modal.moreArrow || 'icon-jianshao' : modal.lessArrow || 'icon-jxsht-3d-tj'"></b>
						</div>


						<div flex class="content-text" v-show="modal.showAll || modal.faqIndex == li"
							:style="modal.cardTextBoxStyle">
							<span class="icon" v-if="l.text?.icon" :style="modal.cardTextIconStyle">
								{{ l.text?.icon }}</span>

							<EditDiv v-if="l.text?.value" :style="modal.cardTextStyle" v-model:content="l.text.value"
								@click.native="setModalType(l.text, modal.list, 'text')">
							</EditDiv>
						</div>


						<div v-if="l.imgList" class="img-list" :style="modal.cardImgListBoxStyle"
							v-show="modal.showAll || modal.faqIndex == li">
							<b class="icon-qiehuan" :hidden="modal.hideImgListIcon" :style="modal.cardImgsArrowLast"
								@click="changeImgList(l, li, l.imgIndex > 0 ? l.imgIndex - 1 : l.imgList.length - 1)"></b>
							<div pointer flex scrollbar class="list-box" :style="modal.cardImgsBoxStyle">
								<template v-for="(p, pi) in l.imgList">
									<pic v-if="!p.value?.endsWith('.mp4') || p.poster" :src="p.poster || p.value"
										:class="{ sele: pi == l.imgIndex }" :style="modal.cardImgListStyle"
										@click="changeImgList(l, li, pi)" :alt="p.alt" />
									<video v-else :poster="p.poster" :title="p.alt" :src="p.value"
										:class="{ sele: pi == l.imgIndex }" :style="modal.cardImgListStyle"
										@click="changeImgList(l, li, pi)"></video>
								</template>
							</div>
							<b class="icon-qiehuan2" :hidden="modal.hideImgListIcon" :style="modal.cardImgsArrowNext"
								@click="changeImgList(l, li, (l.imgIndex < l.imgList.length - 1) ? l.imgIndex + 1 : 0)"></b>
						</div>
					</div>
				</div>
			</div>



			<div class="hover-tag btn-box" v-if="o.button?.value" :style="modal.btnBoxStyle">
				<button v-if="o.button" :primary="!o.button.outline && o.button.value" :outline="o.button.outline"
					:style="{ ...modal.btnStyle, ...o.button.style }" :title="o.button.alt"
					@click="setModalType(o.button, modal.outer, 'button', o.button)">
					<EditDiv v-show="o.button.value" tagName="label" v-model:content="o.button.value" />
					<b :class="o.button.icon" v-show="o.button.icon" :style="modal.btnIconStyle"></b>
				</button>
			</div>

		</template>
	</div>
</template>


<script>
export default {
	name: "modalFaq",
	props: {
		data: {
			type: Object,
			default: {},
		}
	},
	data() {
		return {
			modal: {
				style: {},
				type: {},
				faqIndex: -1,
				outer: [{}],
				...this.data
			},
			tabIndex: 0
		};
	},
	watch: {
		modal: {
			handler(val) {
				if (process.env.isManage) this.$emit("update:data", val);
			},
			immediate: true,
			deep: true
		}
	},
	created() {
		if (process.browser) {
			document.querySelector('script#faqStruct')?.remove();

			let script = document.createElement('script');
			script.id = 'faqStruct';
			script.type = 'application/ld+json';
			script.text = JSON.stringify({
				"@context": "http://schema.org",
				"@id": `https://${this.$store.state.proUrl}/`,
				"@type": "FAQPage",
				"name": this.$store.state.proName,
				"url": `https://${this.$store.state.proUrl}/`,
				"mainEntity": this.data.list.map(i => {
					return {
						"@type": "Question",
						"name": (i.title?.value || '') + (i.subTitle?.value || ''),
						"acceptedAnswer": { "@type": "Answer", "text": i.text.value }
					}
				})
			});
			document.querySelector('head').appendChild(script);
		}
	},
	methods: {
		setModalType(target, targetArray, clickType, event, other) {
			this.$setModal(this, target, targetArray, clickType, event, other)
		},
		changeImgList(l, li, pi) {
			if (process.env.isManage) this.setModalType(l.imgList, this.modal.list, 'img_list', l.imgList[pi], li);
		}
	}
};
</script>


<style lang="scss" scoped>
.tab-box {
	background-color: #F5F5F5;

	>h3 {
		grid-gap: 0.5em;

		b:not([class]) {
			display: none;
		}
	}

	[primary] {
		border: none;
		min-width: auto;
	}
}

.card-box {
	display: grid;
	grid-gap: 1em;
	// border-bottom: thin solid;
}

.card {
	width: 100%;
	display: grid;
	grid-gap: 1em;
	padding-bottom: 1em;
	position: relative;

	&:not(:last-child) {
		border-bottom: thin solid;
	}

	.content-title {
		grid-gap: 0.5em;

		>*:not(b) {
			flex: 1;
		}

		.change {
			font-size: 10px;
			font-weight: bold;
			margin-right: 0.5vw;
		}
	}

	.content-text {
		grid-gap: 0.5em;
	}

	.icon {
		padding: 0;
		border: none;
		height: 1.4em;
		min-width: 1.4em;
		border-radius: 4px;

		&:not([primary]) {
			text-align: center;
			line-height: 1.4em;
			background-color: #EBEBEB;
		}
	}

	.img-list {
		display: flex;
		grid-gap: 1vw;
		align-items: center;

		>b {
			color: #888;
			cursor: pointer;
			font-size: 1.5em;
		}
	}
}



[theme='10'] .modal-box {
	background-image: url(https://sticker-static.oss-accelerate.aliyuncs.com/image/uploads/20231011/faq-background.jpg);
	background-size: 100%;

	.card {
		border-color: #DFDFDF;
	}
}



.icon-arrow {
	.icon-jianshao::before {
		content: "\e6cf";
	}

	.icon-jxsht-3d-tj::before {
		content: "\ebb4";
	}
}


.select-border .sele {
	background: none !important;
	border-color: #7C7C7C !important;
}


.standIcon {
	.card-box>.card {
		.icon-jxsht-3d-tj {
			transform: rotate(90deg)
		}

		.icon-jianshao {
			transform: rotate(180deg);
		}
	}
}

.woven-labels .card-box .sele {
	.content-title {
		color: #FCA454;
	}

	.change {
		color: #FCA454 !important;
		background: #F0F0F0 !important;
	}
}


@media screen and (min-device-width: $mb-width) {
	[theme='10'] .modal-box .card {
		padding: 2em 0;

		.content-title {
			font-size: 1.54em;

			.change {
				font-size: 1em;
			}
		}
	}
}



@media screen and (max-width: $mb-width) {

	.tab-box {
		width: 90vw;
		display: flex;
		flex-wrap: wrap;
		background: none;
		grid-gap: 0.5em 0.7em;
		// justify-content: space-between;

		>h3 {
			// padding: 0;
			// width: 44vw;
			background-color: #F5F5F5;
			// border-radius: 8px 8px 0 0
		}
	}

	.card-box .card {
		grid-gap: 0.5em;
	}

	[theme='10'] .modal-box {
		background-size: 140%;
		background-position: center 13vw;
	}
}
</style>
