<template>
  <div class="mb-4 style" :class="stepData.styleName">
    <half-design-litter-title
      :index="stepData.id"
      :selectedValue="shape"
      :data-name="`${stepData.styleClass + '_' + stepData.id}`"
      style="margin: 4px 0"
      :showTitle="false"
      :stepTitle="stepData.minStepTitle"
      v-show="filterStepItem.length && stepData.attributeTitle"
      >{{ stepData.attributeTitle }}
    </half-design-litter-title>
	<slot name="stepText"></slot>
    <div v-if="this.productInfo.isDevise == 1" class="step-content quantityBox">
      <div class="step-text grey--text">
        {{ langSemiCustom.quantity }}
      </div>
      <div class="sizeNum">
        <div class="inputBox">
          <button class="inputBtn" @click="subNum">—</button>
          <input
            class="priceInput"
            :placeholder="getPlaceholder()"
            type="text"
            v-model="inputNum"
            @keyup="formatNum"
            @change="updatePrice"
          />
          <button class="inputBtn" @click="addNum">+</button>
        </div>
      </div>
    </div>
    <div class="errorTip2" style="display: none; margin: 10px 0">
      <v-alert dense outlined type="error">
        {{ langSemiCustom.miniQty }}
        {{ productInfo.lowestPurchaseQuantity || 1 }}
      </v-alert>
    </div>
    <div class="hoursText">
      {{ lang.hourstext }}
    </div>
    <div class="step-content">
      <div class="step-text grey--text">
        {{ stepData.attributeTitle }}
      </div>
      <div class="step-wrap">
        <div
          class="step-item"
          :class="{
            active: index === selectIndex,
            onePens: noDisdountSelect && step.unitPercent != 0,
          }"
          v-for="(step, index) in filterStepItem"
          :key="index"
          @click="selectStep(step, index)"
        >
          <div class="paramName text-truncate">
            {{ step.valueName }}
            <span v-if="step.priceType == 6 && step.weightPrice">
              <span>- {{ langSemiCustom.add }}</span
              ><CCYRate :price="step.weightPrice"></CCYRate>
            </span>
          </div>
          <div class="tip">
            {{ step.remark }}
          </div>
          <half-design-check-icon2
            class="absolute-topOut-right"
            v-if="theme == 10 || theme == 11"
            iconStyle="noBack"
            iconName="icon-xuanzhong"
            iconSize="medium"
          ></half-design-check-icon2>
          <half-design-check-icon
            class="absolute-top-right"
            v-else
          ></half-design-check-icon>
        </div>
      </div>
    </div>
    <div class="errorTip">
      <v-alert dense outlined type="error">
        {{ langSemiCustom.errorTip }}
      </v-alert>
    </div>
  </div>
</template>
<script>
import { halfDetailDiscount } from "@/mixins/halfDetailDiscount";
import { halfCalculate } from "@/api/web";
export default {
  mixins: [halfDetailDiscount],
  inject: ["getProductInfo"],
  props: {
    stepData: {
      type: Object,
      default: {},
    },
  },
  data() {
    return {
      selectIndex: -1,
      selectItem: null,
      inputNum: "",
      stock: 9999,
      teaSize: {},
      theme: 0,
    };
  },
  watch: {},
  computed: {
    langSemiCustom() {
      return this.$store.getters.lang?.semiCustom;
    },
    productInfo() {
      return this.getProductInfo();
    },
    shape() {
      return this.selectItem?.valueName;
    },
    lang() {
      return this.$store.getters.lang.quote || {};
    },
  },
  methods: {
    selectStep(item, index) {
      this.selectIndex = index;
      this.selectItem = item;
      this.$emit("selectStep", {
        type: this.stepData.attributeFlag,
        data: item,
        parent: this.stepData,
        id: this.stepData.id,
      });
    },
    formatNum() {
      // if (!step.inputNum) {
      // 	this.isInput = false;
      // 	return undefined;
      // }
      this.inputNum = (this.inputNum + "").replace(/[^\d]/g, "");
      if (this.stock && this.stock > 0 && this.inputNum > this.stock) {
        this.inputNum = String(this.stock);
      }
      if (this.stock <= 0) {
        this.inputNum = "";
      }
      if (this.inputNum > 0) {
        // this.updatePrice();
        setTimeout(() => {
          this.updatePrice();
        }, 310);
      }
    },
    getPlaceholder() {
      if (this.stepData.styleName === "style1") {
        if (this.stock > 0) {
          return `${this.stock} in stock`;
        }
        if (this.stock == -1) {
          return;
        } else {
          return "Sold Out";
        }
      }
    },
    updatePrice() {
      let priceInputs = document.getElementsByClassName("priceInput");
      // let sum = Array.from(priceInputs).reduce((acc, input) => acc + parseInt(input.value || 0), 0);
      let sum = priceInputs[0].value;
      if (sum < this.productInfo.lowestPurchaseQuantity) {
        let errDom = document
          .getElementById(this.stepData.id)
          .getElementsByClassName("errorTip2")[0];
        errDom.style.display = "block";
      } else {
        let errDom = document
          .getElementById(this.stepData.id)
          .getElementsByClassName("errorTip2")[0];
        let errDom2 = document
          .getElementById(this.stepData.id)
          .getElementsByClassName("errorTip")[0];
        errDom.style.display = "none";
        if (this.selectIndex > 0) errDom2.style.display = "none";
      }
      if (this.inputNum <= 0) return;
      let teaData = {
        quantity: this.inputNum,
        paramType: "size",
        paramId: this.teaSize.id,
      };
      teaData = teaData.paramId ? teaData : null;
      console.log(teaData, this.teaSize, "5555555555");
      this.$emit("updatePrice", teaData);
    },
    subNum() {
      if (this.inputNum <= 0 || !this.inputNum) {
        this.inputNum = 0;
        return;
      }
      this.inputNum--;
      this.$nextTick(() => {
        this.updatePrice();
      });
    },
    addNum() {
      if (!this.inputNum) this.inputNum = 0;
      if (this.inputNum >= this.stock) return;
      this.inputNum++;
      this.$nextTick(() => {
        this.updatePrice();
      });
    },
    uploadQty(item) {
      this.teaSize = item;
      this.stock = item.stock;
      this.formatNum();
    },
    selectDefault() {
      if (this.filterStepItem.length) {
        this.selectStep(this.filterStepItem[0], 0);
      }
    },
    async getPriceFn(data) {
      let { data: result } = await halfCalculate(data);
      return result.deliveryFee;
    },
  },
  mounted() {
    try {
      const element = document.querySelector("#modalHeader");
      this.theme = element.getAttribute("theme");
    } catch (e) {}
    this.$Bus.$on("uploadQty", this.uploadQty);
    this.$Bus.$on("selectDefaultDiscountStep", this.selectDefault);
  },
  beforeDestroy() {
    this.$Bus.$off("uploadQty");
    this.$Bus.$off("selectDefaultDiscountStep");
  },
};
</script>
<style scoped lang="scss">
@import "~assets/css/half.scss";

.hoursText {
  color: rgb(135 132 132);
  font-size: 16px;
  margin-bottom: 15px;

  @media screen and (max-width: 767px) {
    font-size: 14px;
  }
}

.style .step-content {
  display: grid;
  grid-template-columns: 100px 1fr;
  grid-gap: 10px;

  .sizeNum {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-gap: 10px;

    @include respond-to(mb) {
      grid-template-columns: repeat(2, 1fr);
    }

    .inputBox {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 50px;
      overflow: hidden;
      border-radius: 6px;
      border: 2px solid #e2e2e2;

      .inputBtn {
        width: 14%;
        font-size: 24px;
        transform: scale(1.1);
        font-weight: 300;
      }

      input {
        height: 60%;
        width: 60%;
        line-height: 80%;
        flex-shrink: 0;
        margin: 0 3%;
        border: 1px solid #e2e2e2;
        border-radius: 6px;
        padding: 4px;
        font-size: 14px;
      }

      input::placeholder {
        font-size: 14px;
      }
    }
  }

  .step-wrap {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-gap: 10px;

    .step-item {
      position: relative;
      // overflow: hidden;
      display: flex;
      flex-direction: column;
      min-width: 0;
      @include radius-response;
      outline: 1px solid $border-color;
      padding: 0;
      cursor: pointer;
      //background-color: #f6f6f6;

      &.onePens {
        pointer-events: none;
        background: #f5f5f5;
      }

      @media (any-hover: hover) {
        &:hover {
          outline-width: 2px;
          outline-color: $color-primary;
        }
      }

      &.active {
        outline-width: 2px;
        outline-color: $color-primary;

        .checkIcon {
          display: flex;
        }
      }

      .checkIcon {
        display: none;
      }

      .paramName {
        padding: 5px 10px;
        background-color: $background-color;
        text-align: center;
        font-weight: 600;
      }

      .tip {
        @include flex-center;
        flex: 1;
        word-break: break-word;
        padding: 10px;
        font-size: 14px;
      }
    }
  }

  @include respond-to(mb) {
    grid-template-columns: 1fr;
    grid-gap: 5px;

    .step-wrap {
      grid-template-columns: repeat(2, 1fr);
      grid-gap: 5px;

      .step-item {
        padding: 0;

        .tip {
          font-size: 12px;
        }
      }
    }
  }
}

.style1 .step-content {
  .sizeNum {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-gap: 10px;

    @include respond-to(mb) {
      grid-template-columns: repeat(2, 1fr);
    }
  }
}

.style2 .step-content,
.style3 .step-content {
  .step-wrap {
    grid-template-columns: repeat(2, 1fr);

    @include respond-to(mb) {
      grid-template-columns: repeat(1, 1fr);
    }

    .step-item {
      .tip {
        text-align: center;
      }
    }
  }
}

.style3 .step-content {
  grid-template-columns: 1fr;

  .step-text {
    display: none;
  }

  .step-wrap {
    @include respond-to(mb) {
      grid-template-columns: repeat(2, 1fr);

      .step-item {
        .paramName {
          font-size: 12px;
          font-weight: 700;
          color: #333333;
          padding: 5px 10px !important;
          background-color: transparent;
          text-align: left;
        }

        .tip {
          color: #808080;
          padding: 5px 10px !important;
          justify-content: flex-start;
          text-align: left;
        }
      }
    }

    .step-item {
      font-family: Roboto;
      font-size: 16px;

      .paramName {
        font-weight: 700;
        color: #333333;
        padding: 10px 20px 0;
        background-color: transparent;
        text-align: left;
      }

      .tip {
        color: #808080;
        padding: 10px 20px;
        justify-content: flex-start;
        text-align: left;
      }
    }
  }
}

.quantityBox {
  display: none !important;
}

[theme="10"] {
  .quantityBox {
    display: none !important;
  }

  .step-wrap {
    grid-template-columns: repeat(2, 1fr) !important;
    grid-gap: 20px !important;
  }

  .step-item {
    &.active {
      outline-width: 2px !important;
      outline-color: $color-primary !important;
      color: var(--color-primary) !important;

      .paramName {
        background-color: $color-second !important;
      }
    }

    .paramName {
      padding: 10px !important;
      background-color: $bg-primary !important;
    }
  }
}

[theme="11"] {
  .grey--text {
    color: #333 !important;
  }

  .step-content {
    @include respond-to(mb) {
      grid-gap: 10px;
      grid-template-columns: 50px 1fr;
      align-items: center;

      .step-text {
        position: relative;

        &::after {
          content: ":";
          position: absolute;
          right: 0;
          top: 42%;
          transform: translateY(-50%);
        }
      }
    }
  }

  .step-wrap {
    grid-template-columns: repeat(2, 1fr) !important;
    grid-gap: 20px !important;
  }

  .step-item {
    outline: 1px solid #dcdcdc !important;

    &.active {
      outline-width: 2px !important;
      outline-color: $color-primary !important;
      color: var(--color-primary) !important;

      .paramName {
        background-color: $color-second !important;
      }
    }

    // .paramName {
    // 	padding: 10px !important;
    // 	background-color:#fff !important;
    // }
  }
}
</style>
