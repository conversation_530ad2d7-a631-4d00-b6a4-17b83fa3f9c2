<template>
	<v-dialog
		:value="value"
		@input="$emit('input',false)"
		max-width="523"
	>
		<v-card>
			<v-card-title class="text-h5">
				Connect Your Domain
			</v-card-title>
			<div class="px-6">
				<div class="mb-5" style="font-size: 18px;font-weight: 700">Upgrade your site to connect your own custom domain</div>
				<div class="mb-6 item">
					Get a new domain name like mablebb.com
					<b class="icon-right" style="color:#E6E6E6;"></b>
				</div>
				<div class="mb-6 item">
					Connect a domain you already own
					<b class="icon-right" style="color:#E6E6E6;"></b>
				</div>
			</div>
			<v-card-actions class="pb-6">
				<v-spacer></v-spacer>
				<v-btn color="primary" depressed width="183" @click="$emit('input',false)">
					Maybe Later
				</v-btn>
			</v-card-actions>
		</v-card>
	</v-dialog>
</template>

<script>
export default {
	props: ['value'],
	model: {
		prop: "value",
		event: "input"
	},
}
</script>

<style scoped lang="scss">
.item{
	display: flex;
	align-items: center;
	justify-content: space-between;
	height: 40px;
	background: #F5F6FA;
	border-radius: 6px;
	padding: 0 25px;
	cursor: pointer;
}

</style>
