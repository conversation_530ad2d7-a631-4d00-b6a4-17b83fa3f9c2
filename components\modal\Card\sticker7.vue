<template>
	<div class="summary-box" :class="modal.wrapClass" :style="modal.style">
		<div class="bps-container" v-for="(o, oi) in modal.outer" :key="oi">
			<EditDiv tag-name="h2" v-model:content="modal.outer[0].title.value" @click="setModalType(o.title,modal.outer,'text')" />
			<EditDiv class="des sticker_p" v-model:content="modal.outer[0].subTitle.value" @click="setModalType(o.subTitle,modal.outer,'text')" />
			<div class="size_bootom">
				<pic :src="modal.outer[0].img.value" :alt="modal.outer[0].img.alt" :title="modal.outer[0].img.alt" @click="setModalType(modal.outer[0].img,modal.outer,'img')" />
				<div class="Size_div">
					<p style="font-size: 18px;font-family: Roboto;font-weight: bold;margin-bottom: 30px">There are three areas for your artwork: </p>
					<div class="Size_p" v-for="(item,index) in o.list" :key="index">
						<EditDiv  tag-name="p" v-model:content="item.title.value" @click="setModalType(item.title,modal.outer,'text')" />
						<EditDiv  tag-name="p" v-model:content="item.subTitle.value" @click="setModalType(item.subTitle,modal.outer,'text')" />
					</div>
					<EditDiv tag-name="p" v-model:content="modal.outer[0].txtTitle.value" @click="setModalType(modal.outer[0].txtTitle.title,modal.outer,'text')" />

				</div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	props: {
		data: {
			type: Object,
			default: {},
		}
	},
	data() {
		return {
			modal: {
				style: {},
				type: {index: 0, clickPosition: 'outer'},
				...this.data
			}
		};
	},
	watch: {
		modal: {
			handler(val) {
				if (process.env.isManage) this.$emit("update:data", val);
			},
			immediate: true,
			deep: true
		}
	},
	methods:{
		setModalType(target, targetArray, clickType, event, other) {
			this.$setModal(this, target, targetArray, clickType, event, other)
		},
	}
};
</script>

<style lang="scss" scoped>
	.summary-box {
		margin-top: 80px;
		.bps-container {
			h2 {
				font-size: 42px;
				text-align: center;
			}
			.sticker_p {
				text-align: center;
				font-size: 18px;
				margin-top: 30px;
				padding: 0 20%;
			}
			.size_bootom {
				margin-top: 50px;
				img {
					width: 60%;
					display: inline-block;
				}
				.Size_div {
					display: inline-block;
					margin-left: 3%;
					vertical-align: middle;
					margin-bottom: 7%;
					.Size_p {
						p:nth-child(1) {
							font-weight: bold;
						}
						p {
							display: inline-block;
							font-size: 16px;
							margin-bottom: 7%;
						}
					}
				}
			}
		}
	}
	@media screen and (max-width: $mb-width) {
		.summary-box {
			margin-top: 0px;
			.bps-container {
				h2 {
					font-size: 21px;
					padding: 0 10%;
				}
				.sticker_p {
					font-size: 12px;
					margin-top: 20px;
					padding: 0 7%;
				}
				.size_bootom {
					margin-top: 35px;
					img {
						width: 100%;
					}
					.Size_div {
						width: 105%;
						margin-left: 0;
						.Size_p {
							margin-top: 15px;
							p {
								font-size: 12px;
								margin-bottom: 0;
								display: inline;
							}
						}
						p {
							font-size: 12px;
							margin-top: 15px;
						}
					}
				}
			}
		}
	}
</style>
