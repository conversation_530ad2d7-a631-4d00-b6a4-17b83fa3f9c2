<template>
	<div class="mb-4" :class="stepData.styleName">
		<half-design-select-tip :customText="stepData.attributeTitle" :selectedValue="shape"></half-design-select-tip>
		<slot name="stepText"></slot>
		<div class="swiper" ref="swiper">
			<div class="swiper-wrapper step-content">
				<div class="swiper-slide step-item" :class="{ active: index === selectIndex }" v-for="(step, index) in stepData.productParamList" :key="index" @click="selectStep(step, index)">
					<div class="imgWrap">
						<img :src="step.imgDetail" :alt="step.valueName" :title="step.valueName" />
					</div>
					<div class="text-center pa-1">
						<div class="text-truncate">
							{{ step.valueName }}
						</div>
					</div>
				</div>
			</div>
			<div class="swiper-button-prev"></div>
			<!--左箭头。如果放置在swiper外面，需要自定义样式。-->
			<div class="swiper-button-next"></div>
			<!--右箭头。如果放置在swiper外面，需要自定义样式。-->
		</div>
		<div class="errorTip">
			<v-alert dense outlined type="error">
				{{ langSemiCustom.errorTip }}
			</v-alert>
		</div>
	</div>
</template>
<script>
export default {
	props: {
		stepData: {
			type: Object,
			default: {},
		},
	},
	data() {
		return {
			selectIndex: -1,
			selectItem: null,
		};
	},
	computed: {
		shape() {
			return this.selectItem?.valueName;
		},
		isMobile() {
			return this.$store.state.device === "mb";
		},
		langSemiCustom() {
			return this.$store.getters.lang?.semiCustom;
		},
	},
	methods: {
		selectStep(item, index) {
			this.selectIndex = index;
			this.selectItem = item;
			this.$emit("selectStep", {
				type: this.stepData.attributeFlag,
				data: item,
				parent: this.stepData,
				id: this.stepData.id,
			});
		},
	},
	mounted() {
		this.myswiper1 = new Swiper(this.$refs.swiper, {
			slidesPerView: "5",
			spaceBetween: 10,
			watchSlidesVisibility: true, //防止不可点击
			grabCursor: true,
			navigation: {
				nextEl: ".swiper .swiper-button-next",
				prevEl: ".swiper .swiper-button-prev",
			},
			breakpoints: {
				// when window width is >= 320px
				320: {
					slidesPerView: 3,
					spaceBetween: 5,
				},
				750: {
					slidesPerView: 4,
					spaceBetween: 5,
				},
				1000: {
					slidesPerView: 4,
					spaceBetween: 10,
				},
				1400: {
					slidesPerView: 5,
					spaceBetween: 10,
				},
			},
		});
	},
};
</script>
<style scoped lang="scss">
@import "~assets/css/half.scss";

.swiper {
	--swiper-navigation-color: #333333;
	/* 单独设置按钮颜色 */
	--swiper-navigation-size: 14px;

	/* 设置按钮大小 */
	.swiper-button-next,
	.swiper-button-prev {
		width: 25px;
		height: 25px;
		background: #ffffff;
		opacity: 0.4;
		border-radius: 50%;
		top: 50px;
		margin-top: 0;
		border: 1px solid #cccccc;
	}
}

.style1 .step-content {
	.step-item {
		min-width: 0;
		cursor: pointer;
		@media (any-hover: hover) {
			&:hover {
				.imgWrap {
					border-color: $color-primary;
					background-color: $color-primary;
				}
			}
		}

		.imgWrap {
			@include radius-response;
			@include flex-center;
			height: 100px;
			background-color: $background-color;
		}
	}

	.step-item.active {
		.imgWrap {
			border-color: $color-primary;
			background-color: $color-primary;
		}
	}
}
</style>
