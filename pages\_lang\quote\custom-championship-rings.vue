<template>
	<div class="pinsQuoteWrap">
		<Preloader v-if="isLoading"></Preloader>
		<div class="containerWrap" v-else>
			<!-- <QuoteTitle :h1-text="lang.pins.h1" :prompts-text="lang.pins.prompts"></QuoteTitle> -->

			<div class="content">
				<div class="mbPriceTotal">
					<div class="priceLeft" @click="showTemplate">
						<span><i class="el-icon-sort"></i></span>
						<span>Change template</span>
					</div>
					<div class="priceRight">
						<div>
							<p>
								<CCYRate :price="priceInfo.totalPrice"></CCYRate>
							</p>
							<p class="through">
								<CCYRate :price="priceInfo.foundationUnitPrice * priceInfo.totalQuantity + priceInfo.toolingCharge"></CCYRate>
							</p>
							<div class="price_buttom">
								<span>{{ text1 }}</span>
								<span>off</span>
							</div>
						</div>
					</div>
				</div>
				<div class="leftArea">
					<img :src="this.quoteStyleData.childList[0].imagePhoto" alt="" style="object-fit: cover" />
				</div>
				<div class="rightArea" id="rightAreaCustom">
					<template>
						<template>
							<div class="championship">
								<el-collapse v-model="activeNames" class="custom-collapse" v-for="(item, index) in generalData" :key="index">
									<el-collapse-item :title="'STEP 1' + '   ' + item.paramName" name="1" v-if="item.paramName == 'Top Design'">
										<div class="step-item step-packing" :class="{ mask: maskName === item.paramName }" :id="item.paramName" :key="index">
											<div class="box-border">
												<i class="el-icon-close" @click="closeMask"></i>
											</div>
											<div v-if="onePage">
												<div class="championship_bottom">
													<div @click="stepOnePage(eitem, eindex)" v-for="(eitem, eindex) in item.childList" :key="eindex" class="bottom_div">
														<img :src="JSON.parse(eitem.imageJson)[0].url" alt="" style="aspect-ratio: 456/432" />
														<p>{{ eitem.alias }}</p>
														<PriceText :paramData="eitem" :sizeValue="sizeValue"></PriceText>
													</div>
												</div>
											</div>
											<div v-else>
												<div class="topDesign">
													<div class="designLeft">
														<img :src="JSON.parse(ringDesignJsonData.imageJson)[0].url" alt="" />
														<div>
															<p>{{ ringDesignJsonData.alias }}</p>
															<p>${{ ringDesignJsonData.priceInfo.unitPrice }}</p>
														</div>
													</div>
													<div class="designRight" @click="onePage = true">
														<p>
															<img style="width: 12px" src="https://static-oss.gs-souvenir.com/web/quoteManage/20240807/Vector_20240807mDJsmm.png" alt="" />
															Return
														</p>
													</div>
												</div>
												<el-divider><i class="el-icon-caret-bottom"></i></el-divider>
												<div class="topText">
													<p class="title">Top Text</p>
													<div class="demo-input-suffix">
														Text 1 :
														<el-input v-model="setpOneData.text1" v-if="JSON.parse(ringDesignJsonData.ringDesignJson).top.isUse == 1" style="display: inline-block; width: 90%; margin-left: 2%" :placeholder="JSON.parse(ringDesignJsonData.ringDesignJson).top.text">
															<b slot="suffix">
																<el-dropdown trigger="click" @visible-change="handleCommand" @command="changeMenu($event, 'text1')">
																	<span class="el-dropdown-link" v-if="!setpOneData.color1">
																		<b slot="suffix" class="icon-icon_color1" style="line-height: 2; font-size: 20px; cursor: pointer"></b>
																	</span>
																	<span class="el-dropdown-link" v-else :style="{ backgroundColor: colorStyle.color1 }" style="display: inline-block; width: 20px; height: 20px; border-radius: 50%; margin-top: 10px"></span>
																	<el-dropdown-menu slot="dropdown">
																		<el-dropdown-item v-for="(item, index) in colorData" :key="index" :command="item" :class="{ active: selectColor.color === index }">
																			<div style="width: 24px; height: 24px; border-radius: 50%; display: inline-block" :style="{ backgroundColor: item.code }"></div>
																		</el-dropdown-item>
																	</el-dropdown-menu>
																</el-dropdown>
															</b>
														</el-input>
													</div>
													<div class="demo-input-suffix" style="margin-top: 15px">
														Text 2:
														<el-input v-model="setpOneData.text2" v-if="JSON.parse(ringDesignJsonData.ringDesignJson).bottom.isUse == 1" style="display: inline-block; width: 90%; margin-left: 2%" :placeholder="JSON.parse(ringDesignJsonData.ringDesignJson).bottom.text">
															<b slot="suffix">
																<el-dropdown trigger="click" @visible-change="handleCommand" @command="changeMenu($event, 'text2')">
																	<span class="el-dropdown-link" v-if="!setpOneData.color2">
																		<b slot="suffix" class="icon-icon_color1" style="line-height: 2; font-size: 20px; cursor: pointer"></b>
																	</span>
																	<span class="el-dropdown-link" v-else :style="{ backgroundColor: colorStyle.color2 }" style="display: inline-block; width: 20px; height: 20px; border-radius: 50%; margin-top: 10px"></span>
																	<el-dropdown-menu slot="dropdown">
																		<el-dropdown-item v-for="(item, index) in colorData" :key="index" :command="item" :class="{ active: selectColor.color2 === index }">
																			<div style="width: 24px; height: 24px; border-radius: 50%; display: inline-block" :style="{ backgroundColor: item.code }"></div>
																		</el-dropdown-item>
																	</el-dropdown-menu>
																</el-dropdown>
															</b>
														</el-input>
													</div>
												</div>
												<div class="selectStoneColor" v-if="JSON.parse(ringDesignJsonData.ringDesignJson).left.isUse == 1">
													<p class="title">Select Stone Color</p>
													<div class="colorGrid">
														<!--													active-->
														<div v-for="(item, index) in ringDiamondData.childList" :key="index" :class="{ active: stone1Key === index }" @click="setp1Stone(item, index)">
															<img :src="JSON.parse(item.imageJson)[0].url" alt="" />
															<p>{{ item.alias }}</p>
														</div>
														<div :class="{ active: stone1Key === 8 }" @click="setp1Stone(item, 8)">
															<el-dropdown trigger="click" @visible-change="handleCommand" @command="changeMenu($event, 'color1')">
																<span class="el-dropdown-link" v-if="!colorStyle.color4">
																	<b slot="suffix" class="icon-icon_color1" style="line-height: 2; font-size: 20px; cursor: pointer"></b>
																	<p>More</p>
																</span>
																<span class="el-dropdown-link" v-else :style="{ backgroundColor: colorStyle.color4 }" style="display: inline-block; width: 20px; height: 20px; border-radius: 50%; margin-top: 10px"></span>
																<el-dropdown-menu slot="dropdown">
																	<el-dropdown-item v-for="(item, index) in colorData" :key="index" :command="item" :class="{ active: selectColor.color4 === index }">
																		<div style="width: 24px; height: 24px; border-radius: 50%; display: inline-block" :style="{ backgroundColor: item.code }"></div>
																	</el-dropdown-item>
																</el-dropdown-menu>
															</el-dropdown>
														</div>
													</div>
												</div>
												<div class="selectStoneColor" v-if="JSON.parse(ringDesignJsonData.ringDesignJson).right.isUse == 1" style="margin-top: 30px">
													<p class="title">Select Stone Color2</p>
													<div class="colorGrid">
														<div v-for="(item, index) in ringDiamondData.childList" :key="index" :class="{ active: stone2Key === index }" @click="setp1Sttwo(item, index)">
															<img :src="JSON.parse(item.imageJson)[0].url" alt="" />
															<p>{{ item.alias }}</p>
														</div>
														<div :class="{ active: stone2Key === 8 }" @click="setp1Sttwo(item, 8)">
															<el-dropdown trigger="click" @visible-change="handleCommand" @command="changeMenu($event, 'color2')">
																<span class="el-dropdown-link" v-if="!colorStyle.color5">
																	<b slot="suffix" class="icon-icon_color1" style="line-height: 2; font-size: 20px; cursor: pointer"></b>
																	<p>More</p>
																</span>
																<span class="el-dropdown-link" v-else :style="{ backgroundColor: colorStyle.color5 }" style="display: inline-block; width: 20px; height: 20px; border-radius: 50%; margin-top: 10px"></span>
																<el-dropdown-menu slot="dropdown">
																	<el-dropdown-item v-for="(item, index) in colorData" :key="index" :command="item" :class="{ active: selectColor.color5 === index }">
																		<div style="width: 24px; height: 24px; border-radius: 50%; display: inline-block" :style="{ backgroundColor: item.code }"></div>
																	</el-dropdown-item>
																</el-dropdown-menu>
															</el-dropdown>
														</div>
													</div>
												</div>
												<div class="loGo" v-if="JSON.parse(ringDesignJsonData.ringDesignJson).center.isUse == 1">
													<p class="title" v-if="!oneUpload">Logo</p>
													<div class="logoDiv" v-if="!oneUpload">
														<img class="logoDiv_img" src="https://oss-static-cn.liyi.co/web/quoteManage/20240606/Frame_20240606bQpFxk.png" alt="" />
														<div class="swiper_logo" style="width: 100%">
															<div class="swiper-container">
																<div class="swiper-wrapper">
																	<div class="swiper-slide" v-for="(item, index) in contentLogo.content" :key="index">
																		<img :src="item.iconUrl" alt="" />
																	</div>
																</div>
																<!-- Add Pagination -->
																<div class="swiper-pagination"></div>
															</div>
														</div>
													</div>
													<el-checkbox v-model="oneUpload" @change="swiperRenewal">Upload by email later </el-checkbox>
												</div>
												<div class="logoButton" @click="showMaskFn">Next Step</div>
											</div>
										</div>
									</el-collapse-item>
									<el-collapse-item :title="'STEP 2' + ' ' + item.paramName" name="2" v-if="item.paramName == 'Side Design'">
										<div class="step-item step-packing" :class="{ mask: maskName === item.paramName }" :id="item.paramName" :key="index">
											<div class="box-border">
												<i class="el-icon-close" @click="closeMask"></i>
											</div>
											<div class="championship_bottom">
												<div @click="stepTwoPage(eitem, eindex)" v-for="(eitem, eindex) in item.childList" :key="eindex" :class="{ active: setpTwoIndex === eindex }" class="bottom_div">
													<img :src="JSON.parse(eitem.imageJson)[0].url" alt="" style="aspect-ratio: 228/210" />
													<p>{{ eitem.alias }}</p>
													<PriceText :paramData="eitem" :sizeValue="sizeValue"></PriceText>
												</div>
											</div>
											<div v-if="twoPage">
												<el-divider content-position="right"> <i class="el-icon-caret-bottom"></i></el-divider>
												<div class="topText">
													<p class="title">Side text</p>
													<div class="demo-input-suffix">
														Text 1:
														<el-input style="display: inline-block; width: 90%; margin-left: 2%" v-model="setpTwoData.text1">
															<b slot="suffix">
																<el-dropdown trigger="click" @visible-change="handleCommand" @command="changeMenu($event, 'text3')">
																	<span class="el-dropdown-link" v-if="!setpTwoData.color1">
																		<b slot="suffix" class="icon-icon_color1" style="line-height: 2; font-size: 20px; cursor: pointer"></b>
																	</span>
																	<span class="el-dropdown-link" v-else :style="{ backgroundColor: colorStyle.color3 }" style="display: inline-block; width: 20px; height: 20px; border-radius: 50%; margin-top: 10px"></span>
																	<el-dropdown-menu slot="dropdown">
																		<el-dropdown-item v-for="(item, index) in colorData" :key="index" :command="item" :class="{ active: selectColor.color3 === index }">
																			<div style="width: 24px; height: 24px; border-radius: 50%; display: inline-block" :style="{ backgroundColor: item.code }"></div>
																		</el-dropdown-item>
																	</el-dropdown-menu>
																</el-dropdown>
															</b>
														</el-input>
													</div>
													<div class="loGo">
														<p class="title" v-if="!twoUpload" style="margin-top: 15px">Logo</p>
														<div class="logoDiv" v-if="!twoUpload">
															<img class="logoDiv_img" src="https://oss-static-cn.liyi.co/web/quoteManage/20240606/Frame_20240606bQpFxk.png" alt="" />
															<div class="logoButton" @click="drawerShow()">Change logo</div>
															<div class="swiper_logo">
																<div class="swiper-containerTwo">
																	<div class="swiper-wrapper">
																		<div class="swiper-slide" v-for="(item, index) in contentLogo.content" :key="index">
																			<img :src="item.iconUrl" alt="" />
																		</div>
																	</div>
																	<!-- Add Pagination -->
																	<div class="swiper-pagination"></div>
																</div>
															</div>
														</div>
														<el-checkbox v-model="twoUpload" @change="swiperRenewalTwo"> Upload by email later </el-checkbox>
													</div>
												</div>
												<div class="topText" style="margin-bottom: 0 !important">
													<p class="title">Any other requirements / notes</p>
													<el-input type="textarea" :rows="4" placeholder="Provide information about your artwork" v-model="setpTwoData.remarks"> </el-input>
													<p class="subTitle">Notes: We will email you a digital proof in 24 hours</p>
													<div class="logoButton" @click="showMaskFn">Next Step</div>
												</div>
											</div>
										</div>
									</el-collapse-item>
									<el-collapse-item :title="'STEP 3' + ' ' + item.paramName" name="3" v-if="item.paramName == 'Select Plating Color'">
										<div class="step-item step-attachment" :class="{ mask: maskName === item.paramName }" :id="item.paramName" :key="index">
											<div class="box-border">
												<i class="el-icon-close" @click="closeMask"></i>
											</div>
											<div class="step-box step-attachment-box" ref="step-box">
												<div
													class="item-wrap"
													v-for="citem in item.childList"
													:key="citem.id"
													@click="selectQuoteParams(item, citem)"
													:class="{
														active: hasId(citem.id, selectedData[item.paramName]),
													}"
												>
													<div class="item normalBorder">
														<div class="imgWrap">
															<img loading="lazy" :src="parseJSON(citem.imageJson)[0].url" :alt="parseJSON(citem.imageJson)[0].alt" style="aspect-ratio: 228/210" />
														</div>
														<div class="textWrap">
															<div flex>
																<p class="normal-text">
																	{{ citem.alias ? citem.alias : citem.paramName }}
																</p>
																<span @click.stop v-if="citem.tips"> </span>
															</div>
															<div>
																<PriceText :paramData="citem"></PriceText>
															</div>
														</div>
														<Corner v-if="citem.labelText" :backgroundColor="citem.labelColor" position="absolute">
															{{ citem.labelText }}
														</Corner>
													</div>
												</div>
											</div>
											<el-button class="viewMore" style="position: relative; left: 50%; transform: translateX(-50%)" type="text" @click="showMoreBtn($event, 'step-box')"> {{ lang.ViewMore }}<i style="transform: rotate(90deg)" class="el-icon-d-arrow-right"></i> </el-button>
										</div>
									</el-collapse-item>
									<el-collapse-item :title="'STEP 4' + ' ' + item.paramName" name="4" v-if="item.paramName == 'Addition Engraving'">
										<div class="step-item step-packing step_four" :class="{ mask: maskName === item.paramName }" :id="item.paramName" :key="index" style="position: relative">
											<div class="box-border">
												<i class="el-icon-close" @click="closeMask"></i>
											</div>
											<p style="position: absolute; top: -5px; left: 0; font-weight: 400; font-family: Kanit, Kanit; font-size: 12px">You can select more than one option.</p>
											<div class="step-box step-packing-box">
												<div
													class="item-wrap"
													v-for="(citem, cindex) in item.childList"
													@click="stepFourpage(item, citem, cindex)"
													:key="citem.id"
													:class="{
														active: hasId(citem.id, selectedData[item.paramName]),
													}"
												>
													<div class="item normalBorder">
														<div class="imgWrap">
															<img loading="lazy" :src="parseJSON(citem.imageJson)[0].url" :alt="parseJSON(citem.imageJson)[0].alt" style="aspect-ratio: 696/540" />
														</div>
														<Corner v-if="citem.labelText" :backgroundColor="citem.labelColor" type2 position="absolute">
															{{ citem.labelText }}
														</Corner>
														<div class="textWrap">
															<div flex>
																<p class="normal-text">
																	{{ citem.alias ? citem.alias : citem.paramName }}
																</p>
															</div>
															<div>
																<PriceText :paramData="citem" :sizeValue="sizeValue"></PriceText>
															</div>
														</div>
														<Corner v-if="citem.labelText" :backgroundColor="citem.labelColor" position="absolute">
															{{ citem.labelText }}
														</Corner>
													</div>
												</div>
											</div>
										</div>
									</el-collapse-item>
									<el-collapse-item :title="'STEP 5' + ' ' + item.paramName" name="5" v-if="item.paramName == 'Select Size'">
										<div class="step-item step-packing" :class="{ mask: maskName === item.paramName }" :id="item.paramName" :key="index">
											<div class="box-border">
												<i class="el-icon-close" @click="closeMask"></i>
											</div>
											<div class="stepFive">
												<p class="stepFive_p">If you are unsure about your finger size <span @click="stepFivePage">CLICK HERE</span> to print out our at home ring sizer.</p>
												<div id="bc" v-if="marks">
													<el-slider @change="sliderChange" :min="6" :max="14" v-model="value2" :marks="marks" :step="1" show-stops> </el-slider>
												</div>
												<div class="logoButton" @click="showMaskFn">Next Step</div>
											</div>
										</div>
									</el-collapse-item>
									<el-collapse-item :title="'STEP 6' + ' ' + item.paramName" name="6" v-if="item.paramName == 'Select Ring Chain'">
										<div class="step-item step-attachment" :class="{ mask: maskName === item.paramName }" :id="item.paramName" :key="index">
											<div class="box-border">
												<i class="el-icon-close" @click="closeMask"></i>
											</div>
											<div class="step-box step-attachment-box" ref="step-box">
												<div
													class="item-wrap"
													v-for="citem in item.childList"
													:key="citem.id"
													@click="selectQuoteParams(item, citem)"
													:class="{
														active: hasId(citem.id, selectedData[item.paramName]),
													}"
												>
													<div class="item normalBorder">
														<div class="imgWrap">
															<img loading="lazy" :src="parseJSON(citem.imageJson)[0].url" :alt="parseJSON(citem.imageJson)[0].alt" style="aspect-ratio: 228/210" />
														</div>
														<div class="textWrap">
															<div flex>
																<p class="normal-text">
																	{{ citem.alias ? citem.alias : citem.paramName }}
																</p>
																<span @click.stop v-if="citem.tips"> </span>
															</div>
															<div>
																<PriceText :paramData="citem"></PriceText>
															</div>
														</div>
														<Corner v-if="citem.labelText" :backgroundColor="citem.labelColor" position="absolute">
															{{ citem.labelText }}
														</Corner>
													</div>
												</div>
											</div>
											<el-button class="viewMore" style="position: relative; left: 50%; transform: translateX(-50%)" type="text" @click="showMoreBtn($event, 'step-box')"> {{ lang.ViewMore }}<i style="transform: rotate(90deg)" class="el-icon-d-arrow-right"></i> </el-button>
										</div>
									</el-collapse-item>
									<el-collapse-item :title="'STEP 7' + ' ' + item.paramName" name="7" v-if="item.paramName == 'Select Packaging'">
										<div class="step-item step-packing step_seven" :class="{ mask: maskName === item.paramName }" :id="item.paramName" :key="index">
											<div class="box-border">
												<i class="el-icon-close" @click="closeMask"></i>
											</div>
											<div class="step-box step-packing-box">
												<div
													class="item-wrap"
													v-for="citem in item.childList"
													:key="citem.id"
													@click="selectQuoteParams(item, citem)"
													:class="{
														active: hasId(citem.id, selectedData[item.paramName]),
													}"
												>
													<div class="item normalBorder">
														<div class="imgWrap">
															<img loading="lazy" :src="parseJSON(citem.imageJson)[0].url" :alt="parseJSON(citem.imageJson)[0].alt" style="aspect-ratio: 228/210" />
														</div>
														<Corner v-if="citem.labelText" :backgroundColor="citem.labelColor" type2 position="absolute">
															{{ citem.labelText }}
														</Corner>
														<div class="textWrap">
															<div flex>
																<p class="normal-text">
																	{{ citem.alias ? citem.alias : citem.paramName }}
																</p>
															</div>
															<div>
																<PriceText :paramData="citem" :sizeValue="sizeValue"></PriceText>
															</div>
														</div>
														<Corner v-if="citem.labelText" :backgroundColor="citem.labelColor" position="absolute">
															{{ citem.labelText }}
														</Corner>
													</div>
												</div>
											</div>
										</div>
									</el-collapse-item>
									<el-collapse-item :title="'STEP 8' + ' ' + item.paramName" name="8" v-if="item.paramName == 'Select Turnaround Time'">
										<StepTime class="step-item step-date step_eight" :class="{ mask: maskName === item.paramName }" :id="item.paramName" :index="item.customIndex" :itemData="item" :priceInfo="priceInfo" :previewMode="previewMode" :selectedParams="selectedData" @showMaskFn="showMaskFn" @selectItem="selectQuoteParams($event.item, $event.citem)" @closeMask="closeMask" :key="index"></StepTime>
									</el-collapse-item>
								</el-collapse>
							</div>
						</template>
					</template>
				</div>
			</div>
			<div class="ringsFoot">
				<div class="footLeft">
					<div @click="showTemplate">
						<i class="el-icon-sort"></i>
						<p>Change template</p>
					</div>
					<div>
						<p>
							<CCYRate :price="priceInfo.totalPrice"></CCYRate>
						</p>
						<p class="through">
							<CCYRate :price="priceInfo.foundationUnitPrice * priceInfo.totalQuantity + priceInfo.toolingCharge"></CCYRate>
						</p>
						<div class="price_buttom">
							<span>{{ text1 }}</span>
							<span>off</span>
						</div>
					</div>
				</div>
				<div class="footRight">
					<div @click="infoDialogVisible = true">Submit Inquiry</div>
					<div @click="addCart">Add To Cart</div>
				</div>
				<div class="generalView" v-if="!drawerFour">
					<div class="trapezium" @click="drawerFour = true">Order Summary</div>
				</div>
			</div>
			<el-drawer :visible.sync="showArea" :with-header="true" custom-class="drawDialog" :size="device === 'ipad' ? '728px' : '85%'">
				<Detail class="type3" :key="2" :absoluteImg="comeFromDZ.absoluteImg" :selectedData="selectedData" :priceInfo="priceInfo" :customQty="customQty" :textInfo="textInfo" :generalData="generalData" :attachment="attachment" @toPosition="toPosition" @addInquiry="debounceAddInquiry" @addCart="addCart"></Detail>
			</el-drawer>
			<!-- 建议弹窗-->
			<RecomendDialog :commentsDialog.sync="commentsDialog" :paramValue="selectedParamsValue" @next="recomendNext"></RecomendDialog>
			<!-- 预览弹窗-->
			<VideoPreviewDialog :pic-dialog.sync="picDialog" :zoom-pic="zoomPic" :temp-type="tempType" :selected-params-value="selectedParamsValue" @qtyAndBtnConfirm="qtyAndBtnConfirm" @qtyAndBtnNext="qtyAndBtnNext" @dialogNextStep="dialogNextStep"></VideoPreviewDialog>
			<!-- 升级数量弹窗-->
			<BaseDialog v-model="upgradeDialog" :width="device != 'mb' ? '640px' : '320px'">
				<Upgrade @closeUpgrade="upgradeDialog = false" :tipArr="tipArr" :typeName="cateData.cateName" @upgradeLevel="upgradeLevel"></Upgrade>
			</BaseDialog>
			<!-- o2o预览页面弹窗-->
			<BaseDialog v-model="showOtoDialog" :width="device != 'mb' ? '570px' : '90%'">
				<div class="otoWrap">
					<img src="@/assets/images/oto.png" alt="" />
					<p>{{ lang.p1 }}</p>
					<h3>{{ lang.p2 }}</h3>
					<p style="color: #666666">
						{{ lang.p3 }}<br />
						{{ lang.p4 }}.
					</p>

					<div class="box">
						<p class="t1">
							{{ lang.p5 }} <br />
							{{ lang.p6 }}!
						</p>
						<a href="https://www.o2o.co/previewWeb" style="text-decoration: none">
							<el-button type="primary">{{ lang.p7 }}</el-button>
						</a>
					</div>
				</div>
			</BaseDialog>
			<!--			询盘弹窗-->
			<infoDialog :infoDialogVisible.sync="infoDialogVisible" :uploadList.sync="uploadList" :otherUpload="uploadArtworkList" :recomendUpload="recomendUpload" @getValue="getValueFun"></infoDialog>
			<!--    遮罩-->
			<myMask :maskName.sync="maskName"></myMask>
			<!--  手机，ipad端预览-->
			<PreviewBtn :showArea.sync="showArea"></PreviewBtn>
			<BaseDialog v-model="noFileDialog" :width="device != 'mb' ? '485px' : '90%'" :model="false">
				<template #closeIcon>
					<div style="display: none"></div>
				</template>
				<infoUpload :infoUploadList="infoUploadList" @pushInfoList="pushInfoList" @delInfoList="delInfoList" @updateInquiry="updateInquiry" @closeInfoDialog="closeInfoDialog"> </infoUpload>
			</BaseDialog>
			<div class="drawer">
				<el-drawer title="Choose  Icon" :visible.sync="drawer" direction="rtl">
					<div class="drawer_top">
						<div class="left">
							<el-tabs v-model="activeName" @tab-click="handleClick">
								<el-tab-pane v-for="(item, index) in tableData" :label="item.ringIconTagName" :name="item.id.toString()" :key="index">
									<div v-if="radio">
										<div class="tabPaneTop">
											<div class="top_left">
												<div class="side_left">
													Side<br />
													Preview
												</div>
												<div class="side_right" style="width: 60%; text-align: right">
													<div>
														<img :src="imgJson.imgOne" alt="" />
													</div>
												</div>
											</div>
											<div class="top_right">
												<div class="upload-box">
													<div class="uploadList">
														<el-tooltip class="item" effect="dark" placement="top-start" style="z-index: 9999">
															<div slot="content">
																Maximum file size Allowed extensions:<br />
																JPG,JPEG,GIF,PNG,AI,BMP,TIF,EPS
															</div>
															<i class="el-icon-warning"></i>
														</el-tooltip>
														<template v-if="uploadList.length" style="position: relative">
															<ul>
																<li class="uploadItem">
																	<i class="el-icon-picture" style="display: block; font-size: 80px; margin-top: 30px"></i>
																	<span>{{ uploadList[uploadList.length - 1].original_filename }}1111</span>
																</li>
															</ul>
															<input type="file" ref="upload" :accept="acceptFileType" multiple @change="uploadPic($event, 'stepTwoImgOne')" />
															<el-button type="primary" style="background-color: #1e1e1e; border-color: #1e1e1e"> Browse </el-button>
															<p style="position: absolute; top: 5px; right: 15px; font-size: 30px; line-height: 1; cursor: pointer" @click="uploadList.splice(0, uploadList.length)">×</p>
														</template>
														<template v-else>
															<b class="icon-shangchuan uploadIcon"></b>
															<p>Upload your design</p>
															<input type="file" ref="upload" :accept="acceptFileType" multiple @change="uploadPic($event, 'stepTwoImgOne')" />
														</template>
													</div>
												</div>
											</div>
										</div>
										<div class="tabPaneBottom">
											<div v-for="(item, index) in iconData.content" :key="index" :class="{ active: tabPaneIndex === index }" @click="tabPaneClick(item, index)">
												<img :src="item.iconUrl" alt="" style="aspect-ratio: 348 / 348" />
											</div>
										</div>
									</div>
									<div class="pattern" v-else style="border-top: 1px solid #ccc">
										<div class="left_pattern">
											<p class="pattern_title">Left pattern</p>
											<div class="pattern_div">
												<div>
													Left side<br />
													Preview
												</div>
												<div>
													<img :src="imgJson.imgTwo" alt="" />
												</div>
											</div>
											<div class="pattern_bottom">
												<div>
													<div class="upload-box">
														<div class="uploadList">
															<el-tooltip class="item" effect="dark" content="Maximum file size Allowed extensions: JPG,JPEG,GIF,PNG,AI,BMP,TIF,EPS" placement="top-start" style="z-index: 9999">
																<i class="el-icon-warning"></i>
															</el-tooltip>
															<template v-if="uploadListTwo.length" style="position: relative">
																<ul>
																	<li class="uploadItem">
																		<i class="el-icon-picture" style="display: block; font-size: 80px; margin-top: 30px"></i>
																		<span>{{ uploadListTwo[uploadListTwo.length - 1].original_filename }}</span>
																	</li>
																</ul>
																<input type="file" ref="upload" :accept="acceptFileType" multiple @change="uploadPic($event, 'stepTwoImgTwo')" />
																<el-button type="primary" style="background-color: #1e1e1e; border-color: #1e1e1e"> Browse </el-button>
																<p style="position: absolute; top: 5px; right: 15px; font-size: 30px; line-height: 1; cursor: pointer" @click="uploadListTwo.splice(0, uploadListTwo.length)">×</p>
															</template>
															<template v-else>
																<b class="icon-shangchuan uploadIcon"></b>
																<p>Upload your design</p>
																<input type="file" ref="upload" :accept="acceptFileType" multiple @change="uploadPic($event, 'stepTwoImgTwo')" />
															</template>
														</div>
													</div>
												</div>
												<div v-for="(item, index) in iconData.content" :key="index" :class="{ active: tabPaneIndexTwo === index }" @click="tabPaneClickTwo(item, index)">
													<img :src="item.iconUrl" alt="" style="aspect-ratio: 348 / 348" />
												</div>
											</div>
										</div>
										<div class="right_pattern">
											<p class="pattern_title">Right pattern</p>
											<div class="pattern_div">
												<div>
													Right side<br />
													Preview
												</div>
												<div>
													<img :src="imgJson.imgThree" alt="" />
												</div>
											</div>
											<div class="pattern_bottom">
												<div>
													<div class="upload-box">
														<div class="uploadList">
															<el-tooltip class="item" effect="dark" content="Maximum file size Allowed extensions: JPG,JPEG,GIF,PNG,AI,BMP,TIF,EPS" placement="top-start" style="z-index: 9999">
																<i class="el-icon-warning"></i>
															</el-tooltip>
															<template v-if="uploadListThree.length" style="position: relative">
																<ul>
																	<li class="uploadItem">
																		<i class="el-icon-picture" style="display: block; font-size: 80px; margin-top: 30px"></i>
																		<span>{{ uploadListThree[uploadListThree.length - 1].original_filename }}</span>
																	</li>
																</ul>
																<input type="file" ref="upload" :accept="acceptFileType" multiple @change="uploadPic($event, 'stepTwoImgThree')" />
																<el-button type="primary" style="background-color: #1e1e1e; border-color: #1e1e1e"> Browse </el-button>
																<p style="position: absolute; top: 5px; right: 15px; font-size: 30px; line-height: 1; cursor: pointer" @click="uploadListThree.splice(0, uploadListThree.length)">×</p>
															</template>
															<template v-else>
																<b class="icon-shangchuan uploadIcon"></b>
																<p>Upload your design</p>
																<input type="file" ref="upload" :accept="acceptFileType" multiple @change="uploadPic($event, 'stepTwoImgThree')" />
															</template>
														</div>
													</div>
												</div>
												<div v-for="(item, index) in iconData.content" :key="index" :class="{ active: tabPaneIndexThree === index }" @click="tabPaneClickThree(item, index)">
													<img :src="item.iconUrl" alt="" />
												</div>
											</div>
										</div>
									</div>
								</el-tab-pane>
							</el-tabs>
						</div>
						<div class="right">
							<el-checkbox v-model="radio" style="margin-top: 22px">Same pattern on both sides </el-checkbox>
						</div>
					</div>
				</el-drawer>
				<el-drawer title="Addition Engraving" :visible.sync="drawerTwo" direction="rtl">
					<div class="stepFourImg">
						<img :src="stepFourData.imageJson ? JSON.parse(stepFourData.imageJson)[0].url : ''" alt="" />
					</div>
					<div class="stepFourText">
						<p>{{ stepFourData.paramName }} Text:</p>
						<el-input placeholder="Custom" v-model="fourOneData.remark"></el-input>
					</div>
					<div class="stepFourBottom">
						<div @click="drawerTwo = false">
							Continue Engraving
							<div @click.stop style="border: none; display: inline-block; vertical-align: middle; width: 1%; padding: 0">
								<el-tooltip class="item" effect="dark" placement="top-start">
									<div slot="content">You can select more than one <br />location. Press this button to select more.</div>
									<i class="el-icon-warning"></i>
								</el-tooltip>
							</div>
						</div>
						<div @click="showMaskFn">Next Step</div>
					</div>
				</el-drawer>
				<el-drawer title="Ring size" :visible.sync="drawerThree" direction="rtl">
					<div class="stepFourImg">
						<img src="https://oss-static-cn.liyi.co/web/quoteManage/********/ring_size_********dciSdX.jpg" alt="" />
						<div style="width: 100%; text-align: right; margin-top: 20px">
							<el-button @click="downloadImg('https://oss-static-cn.liyi.co/web/quoteManage/20240808/136/ring_size_.pdf', 'ring size')" type="primary" style="background: #1e1e1e; border-color: #1e1e1e"> Download Ring Size Guide </el-button>
						</div>
					</div>
				</el-drawer>
				<el-drawer title="Order Summary" :visible.sync="drawerFour" direction="rtl">
					<div>
						<div class="rightArea championshipArea" id="rightAreaCustom">
							<transition enter-active-class="animate__animated animate__faster animate__fadeInBottomLeft" leave-active-class="animate__animated animate__faster animate__fadeOutBottomLeft">
								<Detail v-show="showRightArea" class="type1" :class="{ mask: maskName }" :key="1" :absoluteImg="comeFromDZ.absoluteImg" :selectedData="selectedData" :priceInfo="priceInfo" :customQty="customQty" :textInfo="textInfo" :generalData="generalData" :attachment="attachment" @toPosition="toPosition" @addInquiry="debounceAddInquiry" @addCart="addCart"></Detail>
							</transition>
						</div>
					</div>
				</el-drawer>
			</div>
			<el-dialog class="templateClass" title="Change template" :visible.sync="dialogVisible" width="100%">
				<div style="display: flex; width: 100%">
					<div style="margin-right: auto">
						<el-select v-model="ringsValue" placeholder="请选择" @change="changeCate($event)">
							<el-option v-for="item in ringCateList" :key="item.id" :label="item.cateName" :value="item.id"> </el-option>
						</el-select>
					</div>
					<div style="margin-left: auto">
						<p class="active_p" v-for="(item, index) in ringTagList" :key="index" :class="{ activeTabClass: activeTab === index }" @click="activeTabClick(index, item)">
							{{ item.ringTagName }}
						</p>
					</div>
				</div>
				<div class="tab-content">
					<div v-for="(item, index) in ringTemplatesData" :key="index" @click="clickTemplates(item)">
						<img :src="item.imagePhoto" alt="" />
						<p>{{ item.cateName }}</p>
						<p>
							From
							<span style="display: inline-block; font-weight: bold">${{ Number(item.ringTemplatePrice) * (1 - Number(item.ringTemplateDiscount)) }}</span>
							<span style="text-decoration: line-through; display: inline-block">${{ item.ringTemplatePrice }}</span>
							<span style="padding: 5px; display: inline-block; border: 1px solid #ccc">-{{ (Number(item.ringTemplateDiscount) * 100).toFixed(0) }}%</span>
						</p>
					</div>
				</div>
			</el-dialog>
		</div>
	</div>
</template>

<script>
import "@/plugins/element";
import VideoPlayer from "@/components/Quote/VideoPlayer.vue";
import QuoteBtn from "@/components/Quote/QuoteBtn";
import myMask from "@/components/Quote/Mask";
import Detail from "@/components/Quote/Detail";
import BaseDialog from "@/components/Quote/BaseDialog";
import Upgrade from "@/components/Quote/Upgrade";
import Preloader from "@/components/Quote/Preloader";
import Star from "@/components/Quote/Star";
import StepUpload from "@/components/Quote/StepUpload";
import StepTime from "@/components/Quote/StepTime";
import StepQty from "@/components/Quote/StepQty";
import Corner from "@/components/Medals/Corner";
import Pimg from "@/components/Medals/Pimg";
import QtyAndBtn from "@/components/Medals/QtyAndBtn";
import DialogBM from "@/components/Medals/DialogBM";
import infoDialog from "@/components/Medals/infoDialog";
import RecomendDialog from "@/components/Quote/RecomendDialog.vue";
import quoteMixin from "@/mixins/quote";
import QuoteTitle from "@/components/Quote/QuoteTitle.vue";
import PreviewBtn from "@/components/Quote/PreviewBtn.vue";
import CustomCircle from "@/components/Quote/customCircle.vue";
import { isImageType } from "@/utils/utils";
import SwiperDetail from "@/components/Cufflinks/SwiperDetail.vue";
import { uploadFile } from "@/utils/oss";
import { getAppRingIconList, findAll, getAppRingIconTagList, getAppRingCateList, getAppRingTagList, getAppRingTemplatesList } from "@/api/ring";
import PriceText from "@/components/Quote/PriceText";
import { checkFile, acceptFileType } from "@/utils/validate";
import VideoPreviewDialog from "@/components/Quote/VideoPreviewDialog.vue";

export default {
	head() {
		return {
			title: "Quote page",
		};
	},
	components: {
		VideoPreviewDialog,
		CustomCircle,
		PreviewBtn,
		QuoteTitle,
		DialogBM,
		QuoteBtn,
		Detail,
		BaseDialog,
		Upgrade,
		myMask,
		VideoPlayer,
		Preloader,
		PriceText,
		Star,
		StepUpload,
		StepTime,
		StepQty,
		Corner,
		Pimg,
		QtyAndBtn,
		infoDialog,
		RecomendDialog,
		SwiperDetail,
		uploadList: Array,
	},
	mixins: [quoteMixin],
	name: "custom-championship-rings",
	data() {
		return {
			acceptFileType,
			activeNames: ["1", "2", "3", "4", "5", "6", "7", "8"],
			pid: 205,
			cateID: "",
			productsName: "Custom Shape SUNamel Pins",
			restaurants: [
				{ value: "50", address: "50" },
				{ value: "100", address: "100" },
				{ value: "200", address: "200" },
				{ value: "300", address: "300" },
				{ value: "500", address: "500" },
				{ value: "1000", address: "1000" },
				{ value: "3000", address: "3000" },
				{ value: "5000", address: "5000" },
				{ value: "10000", address: "10000" },
			],
			// cusTop: 0,
			changePageList: [
				{ name: "Custom Shape", value: 1, url: "/quote/custom-SUNamel-pins" },
				{ name: "Template Shape", value: 2, url: "/quote/template-SUNamel-pins" },
			],
			onePage: true,
			drawer: false,
			radio: true,
			ringDesignJsonData: [],
			contentLogo: [],
			colorData: [],
			tableData: [],
			activeName: "",
			iconData: [],
			twoPage: false,
			dialogVisible: false,
			ringCateList: [],
			ringsValue: "",
			ringTagList: [],
			activeTab: 0,
			ringTemplatesCateId: "",
			ringTemplatesRingTagId: "",
			ringTemplatesData: [],
			oneUpload: false,
			twoUpload: false,
			drawerTwo: false,
			stepFourData: [],
			drawerThree: false,
			drawerFour: false,
			Img: "https://oss-static-cn.liyi.co/web/quoteManage/********/ring_size_********dciSdX.jpg",
			value2: 1,
			setpOneData: {
				text1: "",
				text2: "",
				color1: "",
				color2: "",
				stone1: "",
				stone2: "",
				files: [],
			},
			setpTwoData: {
				text1: "",
				color1: "",
				remarks: "",
				files: [],
			},
			stepFourValue: "",
			stone1Key: 0,
			stone2Key: 0,
			tabPaneIndex: 0,
			tabPaneIndexTwo: 0,
			tabPaneIndexThree: 0,
			stepFourIndex: "",
			marks: {},
			stepFiveIndex: "",
			fourOneData: {},
			setpTwoIndex: null,
			uploadList: [],
			uploadListTwo: [],
			uploadListThree: [],
			imgJson: {
				imgOne: "https://oss-static-cn.liyi.co/web/quoteManage/20240611/custom_college_ring_20240611QGBwQP.png",
				imgTwo: "https://oss-static-cn.liyi.co/web/quoteManage/20240611/custom_college_ring_20240611QGBwQP.png",
				imgThree: "https://oss-static-cn.liyi.co/web/quoteManage/20240611/custom_college_ring_20240611QGBwQP.png",
			},
			colorStyle: {
				color1: "",
				color2: "",
				color3: "",
			},
			selectColor: {
				color: null,
				color2: null,
				color3: null,
				color4: null,
				color5: null,
			},
			templatesData: [],
		};
	},
	computed: {
		text1() {
			let discountName = "";
			//加急费，重量加价
			if (this.priceInfo.discountPrice) {
				return this.lang.rushDelivery;
			}
			if (this.discountPrice > 0) {
				discountName = this.lang.rushDelivery;
			} else if (this.discountPrice < 0) {
				discountName = this.lang.discount;
			}
			return discountName + ":";
		},
	},
	methods: {
		isImageType,
		// getTop() {
		// 	let el = document.getElementById("detailInfo");
		// 	if (!el) {
		// 		return false;
		// 	}
		// 	const { top, height } = el.getBoundingClientRect();
		// 	this.cusTop = (window.innerHeight - height) / 2 - 100;
		// 	// this.cusTop = (window.innerHeight - height) / 2 + 113 / 2 - 20;
		// },
		linkTo(val) {
			this.$router.push({
				path: val.url,
			});
		},
		swiperRenewal() {
			this.$nextTick(() => {
				this.swiper = new Swiper(".swiper-container", {
					initialSlide: 4,
					slidesPerView: window.innerWidth < 780 ? 3 : 5,
					spaceBetween: 10,
					centeredSlides: true,
					on: {
						slideChange: () => {
							let swiperIndex = this.swiper?.activeIndex;
							console.log(this.contentLogo.content[swiperIndex ? swiperIndex : 4].iconUrl);
							this.setpOneData.files = [this.contentLogo.content?.[swiperIndex ? swiperIndex : 4]?.iconUrl];
						},
						click: () => {
							let swiperIndex = this.swiper?.clickedIndex;
							this.swiper.slideTo(swiperIndex);
							this.setpOneData.files = [this.contentLogo.content?.[swiperIndex ? swiperIndex : 4]?.iconUrl];
						},
					},
				});
			});
		},
		swiperRenewalTwo() {
			this.$nextTick(() => {
				this.swiperTwo = new Swiper(".swiper-containerTwo", {
					initialSlide: 2,
					slidesPerView: window.innerWidth < 780 ? 3 : 5,
					spaceBetween: 10,
					centeredSlides: true,
					loop: false,
					on: {
						slideChange: () => {
							let swiperIndex = this.swiperTwo?.activeIndex;
							this.setpTwoData.files[0] = this.contentLogo.content?.[swiperIndex ? swiperIndex : 2]?.iconUrl;
						},
						click: () => {
							let swiperIndex = this.swiperTwo?.clickedIndex;
							this.swiperTwo.slideTo(swiperIndex);
							this.setpTwoData.files[0] = this.contentLogo.content?.[swiperIndex ? swiperIndex : 2]?.iconUrl;
						},
					},
				});
			});
		},

		stepOnePage(item, index) {
			this.onePage = false;
			this.ringDesignJsonData = item;
			this.ringDesignJsonData.index = index;
			this.selectedData["Top Design"] = [item];
			this.swiperRenewal();
		},
		stepTwoPage(item, index) {
			this.selectedData["Side Design"] = [item];
			this.setpTwoIndex = index;
			if (index == 2) {
				this.twoPage = true;
				this.swiperRenewalTwo();
			} else {
				this.twoPage = false;
				this.showMaskFn();
			}
		},
		getAppRingIconList() {
			let data = {
				cateId: this.pid,
				ringIconTagId: "",
				page: 1,
				pageSize: 999,
			};
			getAppRingIconList(data).then((res) => {
				if (res) {
					this.contentLogo = res.data;
					this.setpOneData.files = [res.data.content[4].iconUrl];
					this.setpTwoData.files[0] = res.data.content[2].iconUrl;
					console.log(this.setpOneData, this.setpTwoData);
				}
			});
		},
		handleCommand() {
			this.$nextTick(() => {
				this.findAll();
			});
		},
		findAll() {
			findAll().then((res) => {
				if (res) {
					this.colorData = res.data;
				}
			});
		},
		getAppRingIconTagList() {
			let data = {
				cateId: this.pid,
			};
			getAppRingIconTagList(data).then((res) => {
				if (res) {
					this.tableData = res.data;
					this.activeName = res.data[0].id.toString();
				}
			});
		},
		drawerShow() {
			this.drawer = true;
			this.getAppRingIconTagList();
			setTimeout(() => {
				let data = {
					cateId: this.pid,
					ringIconTagId: "",
					page: 1,
					pageSize: 999,
				};
				getAppRingIconList(data).then((res) => {
					if (res) {
						this.iconData = res.data;
					}
				});
			}, 500);
		},
		handleClick(tab, event) {
			let data = {
				cateId: this.pid,
				ringIconTagId: tab.name,
				page: 1,
				pageSize: 999,
			};
			getAppRingIconList(data).then((res) => {
				if (res) {
					this.iconData = res.data;
				}
			});
		},
		uploadPic(event, name, type = "upload") {
			this.$gl.show();
			let files = type === "upload" ? event.target.files : event;
			let fileResult = checkFile(Array.from(files), this.acceptFileType, 80);
			if (!fileResult) {
				this.$toast.error("File type error");
				this.$gl.hide();
				return false;
			}
			if (fileResult.nomalSize.length == 0) {
				this.$store.commit("setSizeDialog", true);
				this.$store.commit("setInputRefName", "replayUpload");
				this.$store.commit("setOverSizeList", fileResult.overSize);
				this.$refs.upload.value = "";
				return false;
			}
			let uploadPromises = [];
			fileResult.nomalSize.forEach((file) => {
				let promise = uploadFile(file).then((res) => {
					if (name == "stepTwoImgOne") {
						this.uploadList.push({
							original_filename: file.name,
							secure_url: res,
							size: (file.size / 1024).toFixed(1),
						});
						this.imgJson.imgOne = res;
					} else if (name == "stepTwoImgTwo") {
						this.uploadListTwo.push({
							original_filename: file.name,
							secure_url: res,
							size: (file.size / 1024).toFixed(1),
						});
						this.imgJson.imgTwo = res;
					} else if (name == "stepTwoImgThree") {
						this.uploadListThree.push({
							original_filename: file.name,
							secure_url: res,
							size: (file.size / 1024).toFixed(1),
						});
						this.imgJson.imgThree = res;
					}
				});
				uploadPromises.push(promise);
			});
			Promise.all(uploadPromises).then(() => {
				this.$refs.upload.value = "";
				if (fileResult.overSize.length > 0) {
					this.$store.commit("setSizeDialog", true);
					this.$store.commit("setInputRefName", "replayUpload");
					this.$store.commit("setOverSizeList", fileResult.overSize);
				}
			});
		},
		showTemplate() {
			this.dialogVisible = true;
			this.getAppRingCateList();
			this.getAppRingTemplatesList();
		},
		getAppRingCateList() {
			getAppRingCateList().then((res) => {
				if (res) {
					this.ringCateList = res.data;
					this.ringsValue = res.data[0].id;
					this.getAppRingTagList(res.data[0]);
				}
			});
		},
		getAppRingTagList(item) {
			let data = {
				cateId: item.id,
			};
			getAppRingTagList(data).then((res) => {
				if (res) {
					this.ringTagList = res.data;
				}
			});
		},
		changeCate(evn) {
			this.ringTemplatesCateId = evn;
		},
		activeTabClick(index, item) {
			this.activeTab = index;
			this.ringTemplatesRingTagId = item.id;
			this.getAppRingTemplatesList();
		},
		getAppRingTemplatesList() {
			let data = {
				cateId: this.pid,
				ringTagId: this.ringTemplatesRingTagId,
			};
			getAppRingTemplatesList(data).then((res) => {
				if (res) {
					this.ringTemplatesData = res.data.content;
					this.routingId = res.data.content[0].id;
				}
			});
		},
		stepFourpage(item, citem, index) {
			this.selectQuoteParams(item, citem);
			this.fourOneData = citem;
			// this.selectedData[item.paramName] = [citem];
			if (index != 0) {
				this.drawerTwo = true;
				this.$nextTick(() => {
					this.stepFourData = citem;
				});
			} else {
				this.stepFourIndex = index;
				this.showMaskFn();
				this.drawerTwo = false;
			}
		},
		stepFivePage() {
			this.drawerThree = true;
		},
		download() {
			let imgHeight = (592.28 / "525") * "741"; // 假设contentWidth和contentHeight是已知的
			let canvas = document.createElement("canvas"); // 创建一个Canvas元素
			let ctx = canvas.getContext("2d"); // 获取Canvas的2D渲染上下文
			// 在这里绘制图片到Canvas上...
			let pageData = canvas.toDataURL("https://oss-static-cn.liyi.co/web/quoteManage/********/ring_size_********dciSdX.jpg", 1.0); // 将Canvas内容转换为JPEG格式的图片
			let PDF = new jsPDF("p", "pt", "a4"); // 创建一个新的PDF文档对象
			// 根据图片尺寸和PDF页面尺寸计算需要分割的页数，‌并逐页添加到PDF中...
			PDF.save(filename + ".pdf"); // 保存PDF文件，‌filename是自定义的文件名
		},
		changeMenu(event, name) {
			if (name == "text1") {
				this.setpOneData.color1 = event.pantone;
				this.colorStyle.color1 = event.code;
				this.colorData.forEach((item, index) => {
					if (event.id == item.id) {
						this.selectColor.color = index;
					}
				});
			} else if (name == "text2") {
				this.setpOneData.color2 = event.pantone;
				this.colorStyle.color2 = event.code;
				this.colorData.forEach((item, index) => {
					if (event.id == item.id) {
						this.selectColor.color2 = index;
					}
				});
			} else if (name == "text3") {
				this.setpTwoData.color1 = event.pantone;
				this.colorStyle.color3 = event.code;
				this.colorData.forEach((item, index) => {
					if (event.id == item.id) {
						this.selectColor.color3 = index;
					}
				});
			} else if (name == "color1") {
				this.setpOneData.stone1 = event.pantone;
				this.colorStyle.color4 = event.code;
				this.colorData.forEach((item, index) => {
					if (event.id == item.id) {
						this.selectColor.color4 = index;
					}
				});
			} else if (name == "color2") {
				this.setpOneData.stone2 = event.pantone;
				this.colorStyle.color5 = event.code;
				this.colorData.forEach((item, index) => {
					if (event.id == item.id) {
						this.selectColor.color5 = index;
					}
				});
			}
		},
		setp1Stone(item, index) {
			this.colorStyle.color4 = "";
			this.stone1Key = index;
			this.setpOneData.stone1 = item ? item.alias : "";
		},
		setp1Sttwo(item, index) {
			this.colorStyle.color5 = "";
			this.stone2Key = index;
			this.setpOneData.stone2 = item ? item.alias : "";
		},
		tabPaneClick(item, index) {
			this.tabPaneIndex = index;
			this.imgJson.imgOne = item.iconUrl;
		},
		tabPaneClickTwo(item, index) {
			this.tabPaneIndexTwo = index;
			this.imgJson.imgTwo = item.iconUrl;
		},
		tabPaneClickThree(item, index) {
			this.tabPaneIndexThree = index;
			this.imgJson.imgThree = item.iconUrl;
		},
		sliderChange(evnt) {
			this.generalData.forEach((item) => {
				if (item.paramName == "Select Size") {
					item.childList.forEach((eitem, eindex) => {
						if (evnt == eitem.paramName) {
							this.selectedData[item.paramName] = [eitem];
						}
					});
				}
			});
		},
		clickTemplates(item) {
			this.templatesData = item;
			this.cateID = item.id;
			this.getCateParam(item.id);
		},
		selectColorClick(index) {
			console.log(index);
		},
		downloadImg(url, fileName) {
			const x = new window.XMLHttpRequest();
			x.open("GET", url, true);
			x.responseType = "blob";
			x.onload = () => {
				const url = window.URL.createObjectURL(x.response);
				const a = document.createElement("a");
				a.href = url;
				a.download = fileName;
				a.click();
			};
			x.send();
		},
	},
	watch: {
		// isLoading(newVal){

		// 	}
		getQuote: {
			immediate: true,
			handler(newVal, oldVal) {
				if (newVal == true) {
					let marks = {};
					this.generalData.forEach((item) => {
						if (item.paramName == "Select Size") {
							item.childList.forEach((eitem, eindex) => {
								// if (eindex == 0) {
								// 	this.selectedData[item.paramName] = [eitem];
								// }
								eitem.paramName = Number(eitem.paramName);
								marks[eitem.paramName] = {
									label: this.$createElement("div", {}, [this.$createElement("p", eitem.paramName), this.$createElement(`PriceText`, { props: { paramData: eitem } })]),
								};
								this.marks = marks;
							});
						}
					});
				}
			},
		},
		"imgJson.imgOne": {
			handler(newVal, oldVal) {
				if (newVal) {
					this.setpTwoData.files[0] = newVal;
				}
			},
			deep: true,
		},
		"imgJson.imgTwo": {
			handler(newVal, oldVal) {
				if (newVal) {
					this.setpTwoData.files[1] = newVal;
					this.setpTwoData.files[0] = "";
				}
			},
			deep: true,
		},
		"imgJson.imgThree": {
			handler(newVal, oldVal) {
				if (newVal) {
					this.setpTwoData.files[2] = newVal;
					this.setpTwoData.files[0] = "";
				}
			},
			deep: true,
		},
	},
	mounted() {
		this.getAppRingTemplatesList();
		this.getAppRingIconList();
	},
};
</script>

<style scoped lang="scss">
::v-deep .el-textarea__inner {
	background-color: #f2eee4 !important;
}

::v-deep .scrollBar {
	max-height: 300px !important;
}

::v-deep .el-divider__text.is-center {
	i {
		color: #b7ada1;
	}
}

::v-deep .el-divider__text.is-right {
	right: 85px;

	i {
		color: #b7ada1;
	}
}

::v-deep .drawer {
	.el-drawer {
		width: 45% !important;
		background: #f2eee4;
		padding: 21px 56px;
		@media screen and (max-width: 767px) {
			width: 100% !important;
			padding: 10px;
		}

		.el-drawer__header {
			border-bottom: 1px solid #7c7c7c;
			margin-bottom: 0 !important;

			span {
				font-family: Kanit, Kanit;
				font-weight: 600;
				font-size: 24px;
				margin-bottom: 21px;
				color: #1e1e1e;
			}
		}

		.el-drawer__body::-webkit-scrollbar {
			/*滚动条整体样式*/
			width: 5px;

			/*高宽分别对应横竖滚动条的尺寸*/
			height: 1px;
		}

		.el-drawer__body::-webkit-scrollbar {
			height: 50px;
		}

		.el-drawer__body::-webkit-scrollbar-thumb {
			/*滚动条里面小方块*/
			border-radius: 10px;
			background: #d3d3d3;
		}

		.el-drawer__body::-webkit-scrollbar-track {
			/*滚动条里面轨道*/
			border-radius: 10px;
		}

		.el-drawer__body {
			.left {
				.el-tabs__nav-wrap {
					.el-tabs__nav-scroll {
						.el-tabs__nav {
							.el-tabs__item {
								font-family: Kanit, Kanit;
								font-weight: 400;
								font-size: 14px;
								color: #666666;
							}

							.el-tabs__active-bar {
								display: none !important;
							}

							.is-active {
								color: #000;
								font-weight: bold;
							}
						}
					}

					&:after {
						content: none;
					}
				}
			}
		}
	}

	.drawer_top {
		position: relative;

		.upload-box {
			border: 1px dashed #ccc;
			height: 100%;
			text-align: center;
			line-height: 2.5;
			position: relative;

			.el-checkbox__label {
				font-size: 12px !important;
			}

			.uploadList {
				.uploadItem {
					font-size: 12px;
					list-style: none;
				}

				.el-tooltip {
					position: absolute;
					top: 16px;
					left: 16px;
					z-index: 97;
					font-size: 16px;
				}

				.uploadIcon {
					width: 51px;
					margin-top: 0;
					height: 30px;
					font-size: 48px !important;
				}

				p {
					font-family: Kanit, Kanit;
					font-weight: 400;
					font-size: 14px;
					color: #1e1e1e;
				}

				input {
					position: absolute;
					opacity: 0; /* 隐藏input元素 */
					top: 0;
					left: 0;
					right: 0;
					bottom: 0;
				}
			}

			.upload-btn button {
				width: 147px;
				margin-bottom: 10px;
				font-size: 14px;
				border-radius: 4px;
			}

			.tips {
				font-size: 12px;
			}
		}

		.left {
			@media screen and (max-width: 767px) {
				margin-top: 30px;
			}

			.tabPaneTop {
				font-size: 0;
				display: flex;
				@media screen and (max-width: 767px) {
					display: block;
				}

				.top_left {
					width: 65%;
					display: inline-block;
					background-color: #e8e1d2;
					padding: 12px 16px;
					@media screen and (max-width: 767px) {
						width: 100%;
					}

					.side_left {
						font-family: Kanit, Kanit;
						font-weight: 600;
						font-size: 24px;
						color: #1e1e1e;
						vertical-align: middle;
						display: inline-block;
						margin-right: 68px;
					}

					.side_right {
						vertical-align: middle;
						display: inline-block;
						@media screen and (max-width: 767px) {
							width: 50% !important;
						}

						div {
							display: inline-block;

							img {
								width: 140px;
								height: 130px;
							}

							&:nth-child(1) {
								margin-right: 40px;
								@media screen and (max-width: 767px) {
									margin-right: 0px;
								}
							}
						}
					}
				}

				.top_right {
					width: 30%;
					display: inline-block;
					margin-left: 5%;
					@media screen and (max-width: 767px) {
						width: 100%;
						margin-left: 0;
						margin-top: 10px;
					}
				}
			}

			.tabPaneBottom {
				margin-top: 16px;
				display: grid;
				grid-template-columns: repeat(4, 1fr);
				@media screen and (max-width: 767px) {
					grid-template-columns: repeat(3, 1fr);
					gap: 5px;
					div {
						background-color: #ede7db;
					}
				}
				margin-bottom: 100px;

				div {
					cursor: pointer;
				}

				.active {
					border: 1px solid #ccc;
					background-color: #ede7db;
				}
			}

			.pattern {
				position: relative;
				display: flex;

				.pattern_title {
					font-family: Kanit, Kanit;
					font-weight: 400;
					font-size: 14px;
					color: #1e1e1e;
				}

				.left_pattern {
					padding: 16px 20px 0 0;
					flex: 1;
				}

				.right_pattern {
					padding: 16px 0 0 20px;
					flex: 1;
				}

				.pattern_div {
					background-color: #e8e1d2;
					padding: 12px 16px;
					margin-top: 12px;
					display: flex;
					align-items: center;
					@media screen and (max-width: 767px) {
						display: block;
					}

					div {
						flex: 1;

						&:nth-child(1) {
							font-family: Kanit, Kanit;
							font-weight: 600;
							font-size: 24px;
							color: #1e1e1e;
							@media screen and (max-width: 767px) {
								font-size: 12px;
								text-align: center;
							}
						}

						&:nth-child(2) {
							text-align: right;

							img {
								width: 130px;
								height: 140px;
							}
						}
					}
				}

				.pattern_bottom {
					display: grid;
					grid-template-columns: repeat(2, 1fr);
					@media screen and (max-width: 767px) {
						grid-template-columns: repeat(1, 1fr);
						gap: 5px;
						div {
							background-color: #ede7db;
						}
					}
					margin-top: 16px;
					margin-bottom: 100px;

					.active {
						border: 1px solid #ccc;
						background-color: #ede7db;
					}
				}

				&::after {
					position: absolute;
					content: "";
					height: 100%;
					width: 1px;
					top: 0;
					left: 50%;
					transform: translateX(-50%);
					background-color: #ccc;
				}
			}
		}

		.right {
			position: absolute;
			right: 0px;
			top: 0px;
			@media screen and (max-width: 767px) {
				left: 0 !important;
				top: -50px;
			}

			.el-checkbox__label {
				font-family: Kanit, Kanit;
				font-weight: 400;
				font-size: 14px;
				color: #1e1e1e;
			}
		}
	}

	.stepFourImg {
		@media screen and (max-width: 767px) {
			width: auto;
			div {
				text-align: center;
			}
		}
		width: 527px;
		margin: auto;
		margin-bottom: 100px;

		img {
			border: 1px dashed #ccc;
		}
	}

	.stepFourText {
		p {
			margin-top: 18px;
			font-family: Kanit, Kanit;
			font-weight: 400;
			font-size: 16px;
			color: #1e1e1e;
			margin-bottom: 12px;
		}

		input {
			background: #f2eee4;
		}
	}

	.stepFourBottom {
		text-align: right;
		margin-top: 56px;
		margin-bottom: 100px;
		@media screen and (max-width: 767px) {
			font-size: 0;
		}

		div {
			@media screen and (max-width: 767px) {
				padding: 10px;
				width: 48%;
				display: inline-block;
				font-size: 12px;
				text-align: center;
			}
			padding: 12px 72px;
			font-family: Kanit, Kanit;
			font-weight: 400;
			font-size: 20px;
			color: #1e1e1e;
			display: inline-block;
			cursor: pointer;

			&:nth-child(1) {
				border: 2px solid #1e1e1e;
				background-color: #f2eee4;
			}

			&:nth-child(2) {
				border: 2px solid #1e1e1e;
				background-color: #1e1e1e;
				color: #f2eee4;
				margin-left: 20px;
				@media screen and (max-width: 767px) {
					margin-left: 4%;
				}
			}
		}
	}
}

.viewMore {
	@media screen and (min-width: 768px) {
		display: none !important;
	}
}

::v-deep .templateClass {
	.el-dialog {
		@media screen and (max-width: 767px) {
			padding: 0;
			position: fixed;
			bottom: 0;
			margin-bottom: 0;
			height: 80%;
		}
		padding: 20px 160px;
		background-color: #f2eee4;

		.el-dialog__header {
			border-bottom: 1px solid #7c7c7c;
			padding-bottom: 20px;

			span {
				font-family: Kanit, Kanit;
				font-weight: 600;
				font-size: 24px;
				color: #1e1e1e;
			}
		}

		.active_p {
			margin-right: 20px;
			font-family: Kanit, Kanit;
			font-weight: 400;
			font-size: 14px;
			color: #666666;
			cursor: pointer;
			display: inline-block;
		}

		.activeTabClass {
			font-family: Kanit, Kanit;
			font-weight: 400;
			font-size: 14px;
			color: #000000;
		}

		.tab-content {
			margin-top: 20px;
			display: grid;
			grid-template-columns: repeat(4, 1fr);
			@media screen and (max-width: 767px) {
				grid-template-columns: repeat(2, 1fr);
			}

			div {
				@media screen and (max-width: 767px) {
					padding: 10px;
				}
				padding: 20px 44px;
				cursor: pointer;

				p {
					text-align: center;
					font-family: Kanit, Kanit;
					font-weight: 400;
					font-size: 14px;
					color: #1e1e1e;
					margin-top: 10px;
				}
			}
		}

		.el-dialog__body {
			div {
				&:nth-child(1) {
					@media screen and (max-width: 767px) {
						display: block !important;
						div {
							&:nth-child(2) {
								margin-top: 15px;
							}
						}
					}
				}
			}
		}
	}
}

.tips.type2 {
	top: 0;
	transform: translateY(0);
	right: auto;
	left: 0;
}

.el-dropdown-menu {
	@media screen and (max-width: 767px) {
		width: 95%;
	}
	max-height: 382px !important;
	overflow: hidden;
	display: grid;
	width: 30%;
	grid-template-columns: repeat(7, 1fr);
	gap: 5px;
	left: 1200px;
	overflow-y: auto;

	li {
		padding: 0;
		justify-content: center;
		display: flex;
		align-items: center;
		padding: 5px 0;
		border-radius: 50%;
		width: 50%;
		text-align: center;
		margin-left: 20%;
	}

	.active {
		border: 1px solid #ccc;
	}
}

.pinsQuoteWrap ::v-deep {
	.step-item {
		padding: 25px 10px !important;
	}

	font-family: Calibri, Arial, serif;
	@media screen and (max-width: 767px) {
		input,
		textarea {
			font-size: 16px !important;
		}
		.step-item {
			background-color: #f2eee4 !important;

			.viewMore {
				display: none !important;
			}
		}
	}

	.viewMore {
		@media screen and (min-width: 768px) {
			display: none !important;
		}
	}

	.step-title {
		display: none;
		background-color: #f9f9f9;

		> span {
			// color: $color-primary;
			font-size: 24px;
			width: 36px;
			height: 36px;
			line-height: 36px;
			margin-right: 10px;
			text-align: center;
			display: inline-block;
			background: #333;
			color: #fff;
			border-radius: 4px;
		}
	}

	ul {
		margin: 0;
	}

	img {
		border: none;
		vertical-align: middle;
		max-width: 100%;
		max-height: 100%;
	}

	.custom-shadow {
		position: relative;
		background-color: #fff;
	}

	.custom-shadow:after,
	.custom-shadow:before {
		content: "";
		position: absolute;
		z-index: -1;
		bottom: 12px;
		left: 5px;
		width: 50%;
		height: 20%;
		box-shadow: 0 14px 7px #d9dbdd;
		transform: rotate(-3deg);
	}

	.custom-shadow:after {
		right: 5px;
		left: auto;
		transform: rotate(3deg);
	}

	.video-js {
		overflow: hidden;
		border-radius: 6px 6px 0 0;
	}

	.containerWrap {
		background-color: #fff;
		font-size: 18px;

		.content {
			// padding: 20px 11.5vw 0;
			//padding: 3.8em max(calc(50% - 700px), 1.5vw) 4.2em;
		}

		.isComment {
			width: 100%;
			height: 100%;
			border: 1px solid #ccc;
			padding: 10px;
			border-radius: 4px;
			box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
			text-align: center;

			.textWrap {
				//display: flex;
				align-items: center;
				justify-content: flex-start;
				margin-top: 0 !important;
			}

			.circle2 {
				border-color: $color-primary;
				background-color: $color-primary;

				&::after {
					background-color: #ffffff;
				}
			}

			.el-textarea {
				flex: 1;
				margin: 10px 0;

				textarea {
					height: 100%;
					background-color: #f5f5f5 !important;
				}
			}

			button {
				background-color: $color-primary;
				color: #fff;
				padding: 4px;
				width: 100px;
				outline: none;
				border: none;
				border-radius: 4px;
				font-size: 14px;
				text-align: center;
			}
		}

		.content {
			position: relative;
			display: grid;
			grid-template-columns: 1.28fr 1fr;

			.leftArea {
				background-color: #e8e1d2;
				padding: 130px 90px;
				@media screen and (max-width: 767px) {
					padding: 0px;
				}
				//grid-column: 2/24;
				// .pic_vIew{
				// 	position:sticky;
				// 	&>p{
				// 		font-size: 36px;
				// 		text-align: center;
				// 	}
				// }
				img {
					object-fit: none;
					width: auto;
					position: sticky;
					top: 0;
				}

				.leftArea_title {
					font-size: 36px;
					text-align: center;
				}
			}

			.mbPriceTotal {
				display: none;
				@media screen and (max-width: 767px) {
					padding: 15px;
					display: block;
					background-color: #e8e1d2;
					width: 100%;
					clear: both;
					.priceLeft {
						display: inline-block;

						span {
							font-weight: bold;
							font-size: 14px;
							vertical-align: middle;

							i {
								transform: rotate(90deg);
							}
						}
					}
					.priceRight {
						float: right;
						display: inline-block;

						div {
							display: inline-block;
							vertical-align: middle;

							i {
								display: inline-block;
								vertical-align: middle;
								transform: rotate(90deg);
								font-size: 14px;
							}

							p {
								display: inline-block;
								font-family: Kanit, Kanit;
								font-weight: 400;
								font-size: 14px;
								color: #1e1e1e;
								vertical-align: middle;
								margin-left: 4px;
							}

							.through {
								color: #666666;
								text-decoration: line-through;
							}

							.price_buttom {
								background: #1e1e1e;
								padding: 5px;
								margin-left: 6px;

								span {
									display: inline-block;

									&:nth-child(1) {
										font-family: Kanit, Kanit;
										font-weight: 600;
										font-size: 14px;
										color: #f2eee4;
									}

									&:nth-child(2) {
										font-family: Kanit, Kanit;
										font-weight: 400;
										font-size: 14px;
										color: #f2eee4;
										margin-left: 2px;
									}
								}
							}
						}
					}
				}
			}

			.rightArea {
				min-width: 0;
				background-color: #f2eee4;
				padding: 21px 42px;
				@media screen and (max-width: 767px) {
					padding: 10px;
				}
				//grid-column: 25/49;
				// margin-right: 10px;
				// grid-column: 20/48;

				// padding-right: 30px;
				.championship {
					.championship_bottom {
						display: grid;
						grid-template-columns: repeat(3, 1fr);
						@media screen and (max-width: 767px) {
							grid-template-columns: repeat(2, 1fr);
							column-gap: 5px;
							row-gap: 10px;
						}
						position: relative;

						.bottom_div {
							border: 1px solid transparent;
							@media screen and (max-width: 767px) {
								background-color: #ede7db;
							}

							&:hover {
								background-color: #e8e1d2;
								cursor: pointer;
								border: 1px solid #bab0a4;
							}
						}

						div {
							p {
								font-family: Kanit, Kanit;
								font-weight: 400;
								font-size: 14px;
								text-align: center;
								@media screen and (max-width: 767px) {
									font-size: 12px;
								}
							}

							&:nth-child(2) {
								font-family: Kanit, Kanit;
								font-weight: 500;
								font-size: 16px;
								color: #1e1e1e;
							}
						}

						.active {
							background-color: #e8e1d2;
							@media screen and (max-width: 767px) {
								border: 1px solid #b7ada1;
								border-color: #b7ada1 !important;
							}
						}
					}

					.custom-collapse {
						.el-collapse-item {
							border-bottom: 1px solid #7c7c7c;
						}

						.el-collapse-item__header {
							border-color: transparent;
						}

						.el-icon-arrow-right:before {
							content: "+";
							font-size: 40px;
						}

						.el-collapse-item__wrap {
							background-color: #f2eee4;
							overflow: initial;
						}

						.is-active {
							.el-collapse-item__arrow.is-active {
								transform: none;
							}

							.el-collapse-item__arrow.is-active::before {
								content: "-";
								font-size: 55px;
							}
						}

						.el-collapse-item__header {
							background-color: #f2eee4;
							border-color: #7c7c7c;
							font-family: Kanit, Kanit;
							font-weight: 500;
							font-size: 20px;
							border: none;
							background: url("https://static-oss.gs-souvenir.com/web/quoteManage/20240827/Group_3228_20240827zZAhbt.png") no-repeat;
							background-position: 0px 25px;
							background-size: 60px;
						}

						.el-collapse-item__content {
							background-color: #f2eee4;
						}

						.topDesign {
							background: #e8e1d2;
							width: 100%;
							padding: 8px 12px;
							border: 1px solid #c6bdb0;
							clear: both;
							overflow: hidden;
							margin-top: 25px;
							position: relative;
							z-index: 97;

							.designLeft {
								display: inline-block;

								img {
									width: 77px;
									height: 73px;
									vertical-align: middle;
								}

								div {
									display: inline-block;
									vertical-align: middle;

									p {
										font-family: Kanit, Kanit;
										font-weight: 400;
										font-size: 14px;
										color: #1e1e1e;
									}
								}
							}

							.designRight {
								float: right;
								vertical-align: middle;
								display: inline-block;
								line-height: 5;

								p {
									font-family: Kanit, Kanit;
									font-weight: 400;
									font-size: 14px;
									color: #1e1e1e;
									cursor: pointer;
								}
							}
						}

						.topText {
							margin-bottom: 32px;
							position: relative;

							.logoButton {
								width: 168px;
								height: 38px;
								text-align: center;
								background-color: #1e1e1e;
								line-height: 38px;
								cursor: pointer;
								font-family: Kanit, Kanit;
								font-weight: 500;
								font-size: 14px;
								color: #f2eee4;
								clip-path: polygon(10px 0, calc(100% - 10px) 0, 100% 10px, 100% calc(100% - 10px), calc(100% - 10px) 100%, 10px 100%, 0 calc(100% - 10px), 0 10px);
								margin: 20px auto;
								margin-bottom: 0 !important;
							}

							.title {
								font-family: Kanit, Kanit;
								font-weight: 500;
								font-size: 18px;
								color: #1e1e1e;
								margin-bottom: 15px;
								position: relative;
							}

							.demo-input-suffix {
								font-family: Kanit, Kanit;
								font-weight: 400;
								font-size: 16px;
								color: #1e1e1e;

								.el-input__inner {
									background-color: #f2eee4;
									border: 1px solid #b7ada1;
									border-color: #b7ada1 !important;
								}
							}

							.subTitle {
								font-family: Kanit, Kanit;
								font-weight: 400;
								font-size: 12px;
								color: #1e1e1e;
								margin-top: 8px;
							}

							.demo-input-suffix {
								div {
									@media screen and (max-width: 767px) {
										width: 83% !important;
									}
								}
							}
						}

						.selectStoneColor {
							position: relative;
							z-index: 97;
							margin-bottom: 32px;

							.title {
								font-family: Kanit, Kanit;
								font-weight: 500;
								font-size: 18px;
								color: #1e1e1e;
								margin-bottom: 15px;
							}

							.colorGrid {
								display: grid;
								grid-template-columns: repeat(9, 1fr);
								@media screen and (max-width: 767px) {
									column-gap: 5px;
									grid-template-columns: repeat(6, 1fr);
									row-gap: 40px;
								}
								gap: 35px;

								div {
									cursor: pointer;
									width: 40px;
									height: 40px;
									position: relative;
									text-align: center;

									img {
										transform: scale(1.4);
									}

									p {
										font-family: Kanit, Kanit;
										font-weight: 400;
										font-size: 14px;
										color: #666666;
										text-align: center;
										font-style: normal;
										text-transform: none;
										margin-top: 5px;
									}

									.el-dropdown {
										span {
											b {
												font-size: 35px !important;
												line-height: 1.1 !important;
											}

											p {
												margin-top: 0 !important;
											}
										}
									}

									&:last-child {
										padding-right: 2px;
									}
								}

								.active {
									border: 1px solid #000;
									border-radius: 50%;

									p {
										font-weight: bold;
									}
								}
							}
						}

						.logoButton {
							width: 168px;
							height: 38px;
							text-align: center;
							background-color: #1e1e1e;
							line-height: 38px;
							cursor: pointer;
							font-family: Kanit, Kanit;
							font-weight: 500;
							font-size: 14px;
							color: #f2eee4;
							clip-path: polygon(10px 0, calc(100% - 10px) 0, 100% 10px, 100% calc(100% - 10px), calc(100% - 10px) 100%, 10px 100%, 0 calc(100% - 10px), 0 10px);
							margin: 20px auto;
						}

						.loGo {
							.logoButton {
								width: 168px;
								height: 38px;
								text-align: center;
								background-color: #1e1e1e;
								line-height: 38px;
								cursor: pointer;
								font-family: Kanit, Kanit;
								font-weight: 500;
								font-size: 14px;
								color: #f2eee4;
								clip-path: polygon(10px 0, calc(100% - 10px) 0, 100% 10px, 100% calc(100% - 10px), calc(100% - 10px) 100%, 10px 100%, 0 calc(100% - 10px), 0 10px);
								margin: 20px auto;
							}

							.el-checkbox__label {
								font-family: Kanit, Kanit;
								font-weight: 400;
								font-size: 14px;
								color: #1e1e1e;
								margin-top: 20px;
							}

							.title {
								font-family: Kanit, Kanit;
								font-weight: 500;
								font-size: 18px;
								color: #1e1e1e;
								margin-bottom: 15px;
								position: relative;
							}

							.logoDiv {
								width: 100%;
								background: #e8e1d2;
								height: 138px;
								@media screen and (max-width: 767px) {
									height: 78px;
								}
								position: relative;

								.logoDiv_img {
									position: absolute;
									right: 0;
									bottom: 0;
									width: auto;
									height: 111px;
								}

								.logoButton {
									@media screen and (max-width: 767px) {
										width: 84px;
										height: 25px;
										line-height: 25px;
										font-size: 12px;
										right: 5px;
									}
									width: 168px;
									height: 38px;
									text-align: center;
									background-color: #1e1e1e;
									line-height: 38px;
									cursor: pointer;
									font-family: Kanit, Kanit;
									font-weight: 500;
									font-size: 14px;
									color: #f2eee4;
									clip-path: polygon(10px 0, calc(100% - 10px) 0, 100% 10px, 100% calc(100% - 10px), calc(100% - 10px) 100%, 10px 100%, 0 calc(100% - 10px), 0 10px);
									position: absolute;
									right: 40px;
									top: 50%;
									transform: translateY(-50%);
									margin-top: 0 !important;
								}

								.swiper_logo {
									position: relative;
									background: #e8e1d2;
									font-family: Helvetica Neue, Helvetica, Arial, sans-serif;
									font-size: 14px;
									color: #000;
									margin: 0;
									padding: 0;
									position: absolute;
									width: 70%;
									top: 50%;
									transform: translateY(-50%);
									overflow: hidden;

									.swiper-slide {
										background-color: #f2eee4;
										cursor: pointer;
										width: 90px;
										height: 90px;
										@media screen and (max-width: 767px) {
											height: 61px;
										}
										text-align: center;
										font-size: 18px;
										/* Center slide text vertically */
										display: -webkit-box;
										display: -ms-flexbox;
										display: -webkit-flex;
										display: flex;
										-webkit-box-pack: center;
										-ms-flex-pack: center;
										-webkit-justify-content: center;
										justify-content: center;
										-webkit-box-align: center;
										-ms-flex-align: center;
										-webkit-align-items: center;
										align-items: center;
										transition: 300ms;
										transform: scale(0.8);
										clip-path: polygon(10px 0, calc(100% - 10px) 0, 100% 10px, 100% calc(100% - 10px), calc(100% - 10px) 100%, 10px 100%, 0 calc(100% - 10px), 0 10px);
									}

									.swiper-slide-active,
									.swiper-slide-duplicate-active {
										transform: scale(1);
									}
								}
							}
						}

						.el-divider__text {
							background-color: #f2eee4;
						}
					}

					.stepFive {
						min-height: 130px;
						@media screen and (max-width: 767px) {
							padding: 0px;
						}

						.logoButton {
							width: 168px;
							height: 38px;
							text-align: center;
							background-color: #1e1e1e;
							line-height: 38px;
							cursor: pointer;
							font-family: Kanit, Kanit;
							font-weight: 500;
							font-size: 14px;
							color: #f2eee4;
							clip-path: polygon(10px 0, calc(100% - 10px) 0, 100% 10px, 100% calc(100% - 10px), calc(100% - 10px) 100%, 10px 100%, 0 calc(100% - 10px), 0 10px);
							margin: 20px auto;
							margin-bottom: 0 !important;
							margin-top: 90px;
						}

						.stepFive_p {
							font-family: Kanit, Kanit;
							font-weight: 400;
							font-size: 12px;
							color: #1e1e1e;
							@media screen and (max-width: 767px) {
								font-size: 12px;
							}

							span {
								background: rgba(217, 217, 217, 0);
								text-decoration: underline;
								color: #f79012;
								cursor: pointer;
							}
						}

						.stepFive_ul {
							display: flex;
							margin-top: 28px;

							li {
								position: relative;
								list-style: none;
								margin: 0;
								padding: 0;
								border-top: 2px solid #ccc;
								flex: 1;
								display: inline-block;
								font-size: 12px;

								&:first-child {
									.con {
										transform: translateX(0);
										text-align: left;
									}
								}

								&:last-child {
									flex: 0;
									border-top: none;

									.con {
										transform: translateX(-100%);
										text-align: right;
									}

									&:after {
										left: -2px;
									}
								}

								.con {
									display: flex;
									flex-direction: column;
									position: absolute;
									transform: translateX(-50%);
									text-align: center;
								}

								&:after {
									position: absolute;
									content: "";
									height: 10px;
									width: 2px;
									left: 0;
									top: -10px;
									background-color: #ccc;
								}
							}
						}

						#bc {
							position: relative;
							padding: 0 10px;
							margin-top: 20px;

							.el-slider__bar {
								background-color: #cccccc;
							}

							.el-slider .el-slider__button-wrapper {
								top: -10px !important;
								width: 20px;
								height: 20px;
								z-index: 888;
								background: url("https://oss-static-cn.liyi.co/web/quoteManage/20240705/Group_2219_20240705a4QJ37.png") center/cover no-repeat;

								.el-slider__button {
									position: absolute;
									clip: rect(0 0 0 0);
								}
							}

							.el-slider__runway {
								background-color: #ccc;
								border-radius: 0;
							}

							.hk {
								margin: -20px 0 0 -10px;
								position: absolute;
								width: 20px;
								height: 20px;
								cursor: pointer;
							}
						}

						.el-slider__marks-text {
							text-align: center;
						}
					}
				}

				.rightTop {
					padding: 0 20px;
					display: grid;
					grid-template-columns: 1fr 1fr;
					column-gap: 7%;

					.size-item {
						align-items: center;
						background: #f4f5f5;
						border: 1px solid transparent;
						border-radius: 10px;
						cursor: pointer;
						display: flex;
						height: 40px;
						padding: 0 4px 0 20px;
						transition: all 0.3s;
					}

					.size-item.active {
						border-color: $color-primary;

						.circle2 {
							border-color: $color-primary;
							background: $color-primary;

							&::after {
								background-color: #fff;
							}
						}
					}
				}

				.mask {
					z-index: 101;
					background-color: #fff;
				}
			}
		}

		.picWrap {
			display: flex;
			justify-content: center;
			align-items: center;
			flex-direction: column;

			img {
				width: 100%;
				height: 100%;
				object-fit: contain;
			}
		}

		.footer {
			display: grid;
			grid-template-columns: 700px;
			justify-content: center;
			padding: 20px;
			background: #eef2f5;
		}

		.ringsFoot {
			@media screen and (max-width: 767px) {
				display: none;
			}
			position: sticky;
			bottom: 0;
			width: 100%;
			background: #f2eee4;
			padding: 15px 121px;
			border-top: 1px solid #1e1e1e;
			font-size: 0;
			display: flex;
			justify-content: center;
			align-items: center;
			z-index: 2000;

			.footLeft {
				flex: 1.24;
				display: flex;
				align-items: center;
				justify-content: space-between;

				div {
					display: inline-block;
					vertical-align: middle;

					i {
						display: inline-block;
						vertical-align: middle;
						transform: rotate(90deg);
						font-size: 12px;
					}

					p {
						display: inline-block;
						font-family: Kanit, Kanit;
						font-weight: 400;
						font-size: 20px;
						color: #1e1e1e;
						vertical-align: middle;
						margin-left: 8px;
					}

					.through {
						color: #666666;
						text-decoration: line-through;
					}

					.price_buttom {
						background: #1e1e1e;
						padding: 9px 12px;
						margin-left: 13px;

						span {
							display: inline-block;

							&:nth-child(1) {
								font-family: Kanit, Kanit;
								font-weight: 600;
								font-size: 16px;
								color: #f2eee4;
							}

							&:nth-child(2) {
								font-family: Kanit, Kanit;
								font-weight: 400;
								font-size: 12px;
								color: #f2eee4;
								margin-left: 2px;
							}
						}
					}
				}
			}

			.footRight {
				flex: 1;
				text-align: right;

				div {
					padding: 12px 72px;
					font-family: Kanit, Kanit;
					font-weight: 400;
					font-size: 20px;
					color: #1e1e1e;
					display: inline-block;
					cursor: pointer;

					&:nth-child(1) {
						border: 2px solid #1e1e1e;
						background-color: #f2eee4;
					}

					&:nth-child(2) {
						border: 2px solid #1e1e1e;
						background-color: #1e1e1e;
						color: #f2eee4;
						margin-left: 20px;
					}
				}
			}

			.generalView {
				position: relative;

				.trapezium {
					position: absolute;
					width: 200px;
					height: 0;
					border-bottom: 35px solid #000; /* 定义梯形的底边 */
					border-left: 25px solid transparent; /* 定义梯形的左边 */
					border-right: 25px solid transparent; /* 定义梯形的右边 */
					color: #fff;
					right: 150px;
					top: -77px;
					font-size: 20px;
					line-height: 35px;
					text-align: center;
					cursor: pointer;
				}
			}
		}

		.small-title {
			position: relative;
			margin-bottom: 16px;
			font-size: 16px;
			color: #666666;
		}

		.d-flex-center {
			display: flex;
			justify-content: center;
			align-items: center;
		}

		.circle2 {
			display: flex;
			justify-content: center;
			align-items: center;
			width: 18px;
			height: 18px;
			min-width: 18px;
			border: 1px solid #afb1b3;
			border-radius: 50%;
			margin-right: 10px;
			background-color: #fff;
			transition: all 0.3s;

			&::after {
				content: "";
				width: 6px;
				height: 6px;
				min-width: 6px;
				background: #d4d7d9;
				border-radius: 50%;
			}
		}

		.circle {
			position: absolute;
			left: 50%;
			bottom: 0;
			transform: translate(-50%, 100%);
			width: 28px;
			height: 15px;
			border: 1px solid #e9ecf0;
			border-top: none;
			background: #edf1f5;
			border-radius: 0 0 16px 16px;
			z-index: 10;
			transition: all 0.3s;

			.inner-circle {
				position: absolute;
				left: 50%;
				top: 0;
				transform: translate(-50%, -50%);
				width: 18px;
				height: 18px;
				border-radius: 50%;
				background: #ffffff;
				border: 1px solid #aaaeb3;
				transition: all 0.3s;

				&:after {
					content: "";
					position: absolute;
					left: 50%;
					top: 50%;
					transform: translate(-50%, -50%);
					width: 6px;
					height: 6px;
					background-color: #aaaeb3;
					border-radius: 50%;
					transition: all 0.3s;
				}
			}
		}

		.circle3 {
			display: flex;
			justify-content: center;
			align-items: center;
			width: 18px;
			height: 18px;
			min-width: 18px;
			border: 1px solid #dae0e5;
			background-color: white;
			margin-right: 6.5px;
			transition: all 0.3s;
			position: relative;

			&::after {
				position: absolute;
				content: "";
				left: 50%;
				top: 50%;
				transform: translate(-50%, -50%);
				height: 6px;
				width: 6px;
				border-radius: 50%;
				background-color: #dae0e5;
			}

			/* .checkIcon {
        font-size: 16px;
        color: #ffffff;
      } */
		}

		.confirmBtnWrap {
			margin-top: 33px;
		}

		.drawDialog {
			.el-drawer__header {
				margin-bottom: 0;
				padding: 10px;
			}
		}

		@media screen and (max-width: 1500px) {
			.containerWrap {
				padding-top: 0;
			}
			.content {
				display: block;

				.leftArea {
					// display: none;
					grid-column: 2/48;
				}

				.rightArea {
					padding: 10px 5px;
					// display: none;
					grid-column: 2/48;
					// grid-column-start: span 2;
					.rightTop {
						margin-top: 30px;
					}
				}

				.rightArea.rightFixedArea {
					overflow: hidden auto;
					display: block;
					margin: 0;

					.detailList {
						position: relative;
						top: 0 !important;
						box-shadow: none;
					}
				}
			}
		}

		@media screen and (max-width: 767px) {
			background-color: #ebebeb;
			padding-top: 0px;
			// .containerWrap{
			// 	background-color:#ebebeb !important;
			// }
			.content {
				padding: 0;
				margin: 0;

				.leftArea {
					// .pic_vIew{
					// 	.title{
					// 		font-size: 28px;
					// 		margin-bottom: 10px;
					// 	}
					// }
					.leftArea_title {
						font-size: 28px;
						margin-bottom: 10px;
					}

					.myswiper2 {
						height: initial;
					}

					.myswiper1 {
						display: none;
					}
				}

				.rightArea {
					.rightTop {
						padding: 0;
						margin: 15px 0 !important;
					}
				}
			}

			.footer {
				grid-template-columns: 1fr;
				padding: 10px;
				background-color: #e0e0e0;
			}

			.small-title {
				font-size: 12px;
				margin-bottom: 15px;
			}

			.circle {
				width: 20px;
				height: 10px;

				.inner-circle {
					width: 14px;
					height: 14px;

					&:after {
						content: "";
						width: 5px;
						height: 5px;
					}
				}
			}

			.circle2 {
				width: 18px;
				height: 18px;
				margin-right: 5px;

				&::after {
					width: 5px;
					height: 5px;
				}
			}

			.confirmBtnWrap {
				margin-top: 20px;
			}
		}
	}

	.step-item {
		position: relative;
		margin-bottom: 16px;
		background-color: #f2eee4;
		padding: 40px 30px;
		border-radius: 10px;

		&.hideContent {
			.step-box {
				display: none;
			}

			.step-title {
				margin-bottom: 0;
			}
		}

		.box-border {
			display: none;

			.el-icon-close {
				display: flex;
				justify-content: center;
				align-items: center;
				position: absolute;
				font-weight: 700;
				top: -15px;
				right: -5px;
				width: 40px;
				height: 40px;
				transform: translate(50%, -50%);
				cursor: pointer;
				background: #ffffff;
				border-radius: 50%;
				box-shadow: 1px 2px 20px 0 rgba(0, 0, 0, 0.2);
				z-index: 10;

				// 下一步右上角的关闭按钮 不需要了
				// display: none;
			}
		}

		&.mask {
			position: relative;
			z-index: 101;

			.confirmBtnWrap {
				position: relative;
			}

			// 下一步的
			.box-border {
				position: absolute;
				left: 0;
				right: 0;
				top: 0;
				bottom: 0;
				display: block;
				background-color: #f2eee4;
				border: 1px solid #d9dbdd;
			}

			.step-title {
				position: relative;
			}

			.step-box {
				position: relative;
			}
		}

		.step-title {
			font-size: 18px;
			font-weight: 700;
			color: #333333;
			margin-bottom: 23px;

			.step-title-icon {
				width: 21px;
				margin-left: 4px;
				cursor: pointer;
				vertical-align: middle;
			}
		}

		.step-title.title5 {
			margin-bottom: 4px;
		}

		@media screen and (min-width: 768px) and (max-width: 1499px) {
			padding: 30px 10px;

			.small-title {
				margin-bottom: 15px;
			}
		}

		@media screen and (max-width: 767px) {
			margin-bottom: 10px;
			border-radius: 5px;
			padding: 5px !important;
			&.mask {
				.box-border {
					.el-icon-close {
						width: 30px;
						height: 30px;
						transform: translate(0, 0);
						box-shadow: none;
					}
				}
			}
			.step-title {
				margin-bottom: 10px;
				font-size: 14px;
				font-weight: bold;
				color: #171719;

				.step-title-icon {
					width: 17px;
					margin-left: 5px;
				}
			}
		}
	}

	.step-title {
		margin-bottom: 30px;
	}

	.step-item .step-box {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		@media screen and (max-width: 767px) {
			grid-template-columns: repeat(2, 1fr);
		}
		justify-content: space-between;

		.item-wrap {
			position: relative;
			@media screen and (max-width: 767px) {
				background-color: #ede7db;
			}
			// overflow: hidden;
			p.normal-text,
			p.t1 {
				transition: all 0.3s;
			}

			@media (any-hover: hover) {
				&:hover {
					p.normal-text {
						color: $color-primary;

						span {
							color: #333333;
						}
					}

					p.t1 {
						color: $color-primary;
					}

					.circle2 {
						border-color: $color-primary;
						background: $color-primary;

						&::after {
							background-color: #fff;
						}
					}

					.circle3 {
						border-color: $color-primary;
						background-color: $color-primary;

						&::after {
							background: white;
						}
					}

					.zoomIcon {
						color: $color-primary !important;
					}
				}
			}

			.item {
				position: relative;
				border-radius: 6px;
				display: flex;
				flex-direction: column;
				justify-content: space-between;
				align-items: center;
				cursor: pointer;
				transition: all 0.3s;
				border: 1px solid transparent;

				&:hover {
					box-shadow: none !important;
					border-radius: 0 !important;
					border: 1px solid #bab0a4;
				}

				.imgWrap {
					display: flex;
					justify-content: center;
					align-items: center;
					height: 100%;
					width: 100%;

					img {
						object-fit: contain;
						border-radius: 6px 6px 0 0;
					}
				}

				@media (any-hover: hover) {
					&:hover {
						background-color: #e8e1d2;
						box-shadow: 0 3px 4px 0 #cccccc;

						.circle {
							border-color: $color-primary;
						}
					}
				}
			}

			.item.linearBorder {
				background-image: linear-gradient(0deg, #edf1f5 0%, #f7f9fa 100%), linear-gradient(0deg, #e9ecf0, #f7f9fa);
				background-origin: border-box;
				background-clip: content-box, border-box;
				@media (any-hover: hover) {
					&:hover {
						background-image: linear-gradient(0deg, #edf1f5 0%, #f7f9fa 100%), linear-gradient(0deg, $color-primary, #f7f9fa);
						box-shadow: 0 3px 4px 0 #cccccc;
					}
				}
			}

			.textWrap {
				.normal-text {
					font-size: 16px;
					color: #333333;
					transition: all 0.3s;
					text-align: left;
				}

				.tip-text {
					color: #de3500;
					font-size: 16px;
				}
			}

			&.active {
				.circle3 {
					border-color: $color-primary;
					background-color: $color-primary;

					.checkIcon {
						color: #ffffff;
					}
				}

				.item {
					background-color: #e8e1d2;
					border-radius: 0;
					border-color: #bfb6a9;

					.circle {
						border-color: $color-primary;

						.inner-circle {
							border-color: $color-primary;

							&::after {
								background-color: $color-primary;
							}
						}
					}
				}

				.item.linearBorder {
					background-image: linear-gradient(0deg, #edf1f5 0%, #f7f9fa 100%), linear-gradient(0deg, $color-primary, #f7f9fa);
					box-shadow: 0 3px 4px 0 #cccccc;
				}

				.textWrap {
					.circle2 {
						border-color: $color-primary;
						background: $color-primary;

						&::after {
							background-color: #fff;
						}
					}
				}
			}

			& .textWrap {
				justify-content: center;
				align-items: center;
				margin-top: 0;
				// flex-direction: column;
				// background: #f4f5f5;
				padding: 5px 0;
			}
		}

		@media screen and (min-width: 768px) and (max-width: 1499px) {
			column-gap: 12px;
			row-gap: 23px;
		}

		@media screen and (max-width: 767px) {
			column-gap: 5px;
			row-gap: 10px;
			margin-top: 20px;
			.item-wrap {
				.item {
					border-radius: 5px;
					@media (any-hover: hover) {
						&:hover {
							background-image: none;
							box-shadow: none;
							border-color: #e6e6e6;

							.circle {
								border-color: transparent;
							}
						}
					}
				}

				.textWrap {
					padding-bottom: 0;

					.normal-text {
						font-size: 12px;
						text-align: center;
						font-weight: 400;
						font-family: Kanit, Kanit;
					}

					.tip-text {
						font-size: 12px;
					}
				}

				&.active {
					.item {
						border-color: #b7ada1;
						background-image: none;
						box-shadow: none;

						.circle {
							border-color: $color-primary;

							.inner-circle {
								border-color: $color-primary;

								&::after {
									background-color: $color-primary;
								}
							}
						}
					}
				}

				& > .textWrap {
					display: flex;
				}
			}
		}
	}

	.step-back {
		.step-box {
			grid-template-columns: repeat(3, 1fr);

			@media screen and (max-width: 767px) {
				grid-template-columns: repeat(2, 1fr);
			}
		}
	}

	.step-metal {
		.step-box {
			grid-template-columns: repeat(3, 1fr);
			column-gap: 20px;
			row-gap: 20px;

			.item-wrap {
				.item {
					position: relative;
					display: flex;
					justify-content: center;
					align-items: center;
					// height: 118px;

					.imgWrap {
						img {
							object-fit: contain;
						}
					}
				}

				.circle {
					display: none;
				}

				.textWrap {
					margin-top: 5px;
				}
			}
		}
	}

	@media screen and (min-width: 768px) and (max-width: 1499px) {
		.step-metal {
			.step-box {
				column-gap: 10px;
				row-gap: 25px;

				.item-wrap {
					.item {
						height: 100px;
					}

					.textWrap {
						margin-top: 12px;
					}
				}
			}
		}
	}

	@media screen and (max-width: 767px) {
		.step-metal {
			.step-box {
				grid-template-columns: repeat(3, 1fr);
				column-gap: 4px;
				row-gap: 10px;
				height: auto;

				.item-wrap {
					.item {
						height: 67px;

						&::after {
							height: 20px;
						}

						.imgWrap img {
							border-radius: 5px;
						}
					}

					.circle {
						display: block;
					}

					.textWrap {
						margin-top: 15px;
						text-align: center;
					}

					.circle2 {
						display: none;
					}
				}
			}
		}

		.step-title {
			margin-bottom: 15px;
		}
	}

	.otoWrap {
		font-size: 16px;
		text-align: center;
		padding: 27px;

		h3 {
			font-size: 36px;
			font-weight: 700;
			margin-top: 10px;
			margin-bottom: 15px;
			line-height: normal;
		}

		.box {
			padding: 27px;
			background: #eff2f6;
			border-radius: 10px;
			margin-top: 33px;

			.t1 {
				font-size: 18px;
				font-weight: 700;
			}

			button {
				margin-top: 20px;
				width: 266px;
				height: 45px;
				background-color: #1a73e8;
				border-color: #1a73e8;
				font-size: 16px;
				@media (any-hover: hover) {
					&:hover {
						opacity: 0.8;
					}
				}
			}
		}
	}

	.rounded-circle {
		border-radius: 50%;
	}
}

//
// 隐藏组件里的step文字
::v-deep {
	.step-upload,
	.step-qty,
	.step-date {
		.step-title {
			> span:first-of-type,
			> span:last-of-type {
				display: none;
			}
		}
	}

	.step-date {
		.step-box {
			grid-template-columns: repeat(2, 1fr);
		}
	}
}

//

.myswiper1 {
	.swiper-wrapper {
		justify-content: center;
		margin-top: 10px;
		margin-left: 10px;
	}

	.swiper-slide {
		display: flex;
		justify-content: center;
		align-items: center;
		overflow: hidden;
		width: 88px;
		height: 88px;
		border: 2px solid #eeeeee;
		border-radius: 10px;
		cursor: pointer;

		&.swiper-slide-thumb-active {
			border: 2px solid $color-primary !important;
		}
	}

	&.isEdit {
		.swiper-slide.swiper-slide-thumb-active {
			border: 2px solid #eeeeee;
		}
	}

	img {
		//width: 100%;
		height: 100%;
		//object-fit: cover;
	}

	.playBtn {
		display: flex;
		justify-content: center;
		align-items: center;
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		width: 30px;
		height: 30px;
		background-color: rgba(0, 0, 0, 0.7);
		border-radius: 50%;

		svg {
			fill: #ffffff;
		}
	}
}

.swiper.myswiper2 {
	--swiper-navigation-color: #ffffff;
	/* 单独设置按钮颜色 */
	--swiper-navigation-size: 20px;

	/* 设置按钮大小 */
	.swiper-button-next,
	.swiper-button-prev {
		width: 46px;
		height: 46px;
		background: #000000;
		opacity: 0.4;
		border-radius: 50%;
		transform: translateY(-50%);
		margin-top: 0;
	}
}

.myswiper2 {
	// height: 100%;
	height: 500px;

	.zoom {
		position: absolute;
		top: 0;
		right: 0;
		cursor: pointer;
		z-index: 100;

		b {
			font-size: 22px;
			margin-right: 4px;
		}

		&:hover {
			b {
				color: $color-primary;
			}
		}
	}

	.swiper-slide {
		display: flex;
		justify-content: center;
		align-items: center;

		.smallImg {
			position: relative;
			width: 500px;
			height: 100%;
			text-align: center;
		}
	}

	img {
		max-width: 100%;
		max-height: 100%;
		object-fit: contain;
	}
}

::v-deep .championshipArea {
	.detailList {
		background: #f2eee4 !important;

		.topImg {
			display: none !important;
		}

		.con {
			.title {
				display: none;
			}
		}

		.freeTip {
			display: none;
		}

		.btnGroup {
			display: none;
		}

		.footBtn {
			display: inline-block;
			overflow: hidden;
			width: 100%;

			div {
				&:nth-child(1) {
					display: inline-block !important;
				}
			}

			.sub {
				float: right;
			}
		}
	}
}

::v-deep .step_eight {
	.step-box {
		grid-template-columns: repeat(4, 1fr) !important;
		@media screen and (max-width: 767px) {
			grid-template-columns: repeat(2, 1fr) !important;
		}

		span {
			span {
				.el-popover__reference {
					.item-wrap {
						background-color: #f2eee4;
						border: 1px solid transparent;

						&:hover {
							border: 1px solid transparent;
							background-color: #e8e1d2;
							border-radius: 0;
						}

						@media screen and (max-width: 767px) {
							background-color: #ede7db;
						}
						text-align: center;

						.top {
							justify-content: center;

							.customCircle1 {
								display: none !important;
							}
						}

						span {
							font-size: 16px;
						}
					}

					.active {
						background-color: #e8e1d2;
						border: none;
						border-radius: 0 !important;
						@media screen and (max-width: 767px) {
							border: 1px solid #b7ada1;
							border-color: #b7ada1 !important;
						}
					}

					.bottom {
						margin-left: 0 !important;
						font-weight: 400;
					}
				}
			}
		}
	}
}

::v-deep .step_seven {
	.step-box {
		grid-template-columns: repeat(4, 1fr) !important;
		@media screen and (max-width: 767px) {
			grid-template-columns: repeat(2, 1fr) !important;
		}
	}
}
::v-deep .step_four {
	.step-box {
		gap: 5px;
	}
}
::v-deep .el-dialog__body {
	@media screen and (max-width: 767px) {
		padding: 10px;
	}
}
::v-deep .infoDialog {
	.el-dialog__wrapper {
		.el-dialog {
			background-color: #f8f6f0 !important;
			.el-dialog__body {
				@media screen and (max-width: 767px) {
					padding: 10px;
				}
				.el-form-item {
					.el-form-item__content {
						.btn-box {
							button {
								&:nth-child(1) {
									background-color: #000000;
									color: #fff;
									border-color: #000;
								}
								&:nth-child(1) {
									border-color: #000;
								}
							}
						}
					}
				}
			}
		}
	}
}
//
</style>