<template>
	<div class="luggagetagsListContent">
		<template v-if="isManage">
			<div class="luggagetagsList modal-box" :class="{ isDialog: isDialog }" :style="[modal.style]">
				<v-card height="300" style="background-color: #fff">
					<v-row justify="center" align="center" style="width: 100%; height: 100%; display: flex; margin: 0">
						<div class="rightHeader" style="padding: 0">
							<div v-for="(l, li) in modal.list" :key="li" :style="{ ...modal.cardBoxStyle, ...l.style }">
								<EditDiv :tagName="l.title.tagName || 'h1'" v-model:content="l.title.value" @click="setModalType(l.title, modal.list, 'text')" v-if="l.title?.value" :style="{ ...modal.titleStyle }" />
								<EditDiv :tagName="l.subTitle.tagName || 'p'" v-model:content="l.subTitle.value" @click="setModalType(l.subTitle, modal.list, 'text')" :style="{ ...modal.subTitleStyle }" v-if="l.subTitle?.value" />
							</div>
						</div>
					</v-row>
				</v-card>
			</div>
		</template>
		<template v-else>
			<div class="rightHeader">
				<div v-for="(l, li) in modal.list" :key="li" :style="{ ...modal.cardBoxStyle, ...l.style }">
					<EditDiv :tagName="l.title.tagName || 'h1'" v-model:content="l.title.value" @click="setModalType(l.title, modal.list, 'text')" v-if="l.title?.value" :style="{ ...modal.titleStyle }" />
					<EditDiv :tagName="l.subTitle.tagName || 'p'" v-model:content="l.subTitle.value" @click="setModalType(l.subTitle, modal.list, 'text')" :style="{ ...modal.subTitleStyle }" v-if="l.subTitle?.value" />
				</div>
			</div>
			<div class="borderBox" id="borderBoxSticky"></div>
			<div class="luggagetagsList modal-box" :class="{ isDialog: isDialog }" :style="[modal.style, isManage ? {} : { 'padding-top': '0' }]">
				<v-app>
					<div class="bps_container">
						<div class="content" id="proContent">
							<div class="top-right sticky" v-if="isMobile">
								<div class="filter-area">
									<div class="filter1" @click="toggleFilter">
										<b class="icon-a-HideFilters"></b>
										<span>
											{{ langSemiCustom.filters }} <span class="filterTotal">({{ totalResult }} {{ langSemiCustom.results }})</span>
										</span>
									</div>
								</div>
							</div>
							<leftBar2 :style="{ maxHeight: isMobile ? `${screenHeight}px` : screenHeight - headerHeight + 'px' }" :isStockPage="isStockPage" :labelData="labelData" :loadLabel="loadLabel" :colorItem="colorItem" :selectedParamsObj="selectedParamsObj" :selectedParams="selectedParams" :totalResult="totalResult" :activeNames="activeNames" :isLuggagetags="true" @toggleFilter="toggleFilter" @delAllTag="delAllTag" @delColor="delColor" @delTag="delTag" @updateTag="updateTag" @clickTitle="clickTitle" @toggleColor="toggleColor" v-show="showFilter"></leftBar2>
							<div class="rightBar" style="width: 100%; min-width: 0">
								<div class="rightPart" id="productList">
									<div class="rightContent">
										<div class="productWrap">
											<div class="luggageList" @click="openDetail(item, index)" v-for="(item, index) in productList" :key="item.id">
												<div class="dataBox">
													<div v-show="item.previewVideo || item.coverVideo" class="imgTriangle"></div>
													<img v-if="item.coverImg" :src="item.coverImg" loading="lazy" :alt="item.imgAlt" :title="item.imgTitle" />
													<video v-else :src="item.coverVideo"></video>
												</div>
											</div>
										</div>
									</div>
								</div>
								<!-- <div class="loadProgress" v-show="loadingProduct">
									<Loading></Loading>
								</div> -->
							</div>
							<div class="loadProgress" v-show="loadingProduct">
								<Loading></Loading>
							</div>
						</div>
						<div class="loadBtnMainBox" v-show="!loadingProduct && totalResult > 0">
							<div class="loadMoreBtn">
								<div class="loadMoreBtnText">
									<span>
										{{ `${langSemiCustom.viewed} ${productList.length - usePosition} ${langSemiCustom.of} ${totalResult} ${langSemiCustom.products}` }}
									</span>
								</div>
								<v-progress-linear height="6" background-color="#fff" rounded :value="numPercent"></v-progress-linear>
								<div class="loadBtnBox" v-show="showLoadBtn">
									<span class="loadBtn" primary @click="loadThreeData(false)">{{ langSemiCustom.viewMore }}</span>
								</div>
							</div>
						</div>
					</div>
				</v-app>
			</div>
		</template>
		<dialog ref="dialog" class="nenoDialog" closeable>
			<div class="mainContentModel">
				<div class="dialogContent" v-if="showContent">
					<NeonSwiper :imgList="productList" ref="swiperBox" :isLuggagetags="true" :width="!isMobile ? '800px' : '100%'" @close="handleClose"></NeonSwiper>
					<div @click="handleClose" class="closeIcon">
						<b class="icon-guanbi"></b>
					</div>
				</div>
			</div>
		</dialog>
	</div>
</template>

<script>
import { getAppLabelAttributeList, getProductList, listAllCategoryByParentId } from "@/api/web";
import { getTagsList, getImagesList } from "@/api/neon/neon";
import { debounce } from "@/utils/utils";
import noFind from "@/components/modal/Half/List/noFind.vue";
import product from "@/components/modal/Half/List/product.vue";
import leftBar2 from "@/components/modal/Half/List/leftBar2.vue";
import NeonSwiper from "@/components/modal/Half/NeonSwiper.vue";

export default {
	name: "luggagetagsList",
	props: ["cateId", "data", "parentCateId", "halfCateDTO", "isStockPage", "name"],
	components: {
		noFind,
		product,
		leftBar2,
		NeonSwiper,
	},
	data() {
		return {
			attrs: {
				class: "mb-6",
				boilerplate: true,
				elevation: 2,
			},
			showMoreIndex: 10,
			last_scroll: 0,
			hide_on_load: false,
			debounceSearchProduct: null,
			debounceLabel: null,
			snackbar: false,
			modal: {
				style: {},
				type: {},
				...this.data,
			},
			showPriceRange: false,
			priceRange: [-990000, 990000],
			halfName: "",
			goodList: [],
			keyword: "",
			sorts: 2,
			labelData: [],
			cateList: [],
			activeNames: [],
			selectedParams: [],
			showFilter: true,
			page: 1,
			pages: 0,
			pageSize: 9,
			totalResult: 0,
			productList: [],
			colorItem: "",
			priceKey: 0,
			quantityKey: 0,
			screenHeight: 0,
			loadingProduct: false,
			cateName: "",
			isManage: false,
			attributeValueIds: [],
			selectedParamsObj: {},
			loadLabel: false,
			adData: null,
			advert: [],
			adNum: 0,
			useAdNum: 0,
			doubleAd: 0,
			customKeyword: "",
			usePosition: 0,
			fatherCateName: "",
			headerHeight: 0,
			selectLabelHeight: 0,
			nowTimeStamp: "",
			cancelTokenSource: null,
			firstCate: false,
			fristWatch: false,
			tagViewStatus: false,
			adBoxHeight: "",
			addTag: false,
			productTag: "",
			tagsIdList: [],
			showContent: false,
		};
	},
	async fetch() {
		if (process.env.isManage) {
			this.isManage = true;
			return false;
		}
		this.page = parseInt(this.$route.query.page) || 1;
		await this.loadThreeData(true);
	},
	// fetchOnServer: false,
	watch: {
		modal: {
			handler(val) {
				if (process.env.isManage) this.$emit("update:data", val);
			},
			immediate: true,
			deep: true,
		},
		selectedParams() {
			if (!this.fristWatch) return;
			this.useAdNum = 0;
			this.doubleAd = 0;
			this.usePosition = 0;
			// this.updateLabel();
			this.searchProduct();
		},
		"$fetchState.pending"(newVal, oldVal) {
			if (!newVal && oldVal) {
				// 仅在从pending变为非pending时执行
				this.$nextTick(() => {
					// 这里可以安全地操作DOM或执行依赖于DOM的操作
					this.setSticky();
				});
			}
		},
	},
	computed: {
		isDialog() {
			return !!this.name;
		},
		langQuote() {
			return this.$store.getters.lang.quote || {};
		},
		langSemiCustom() {
			return this.$store.getters.lang?.semiCustom;
		},
		langDesign() {
			return this.$store.getters.lang?.design;
		},
		userId() {
			return this.$store.state.userInfo?.id;
		},
		isLogin() {
			return this.$store.getters.isLogin;
		},
		isMobile() {
			return this.$store.state.device === "mb";
		},
		numPercent() {
			return Math.round(((this.productList.length - this.usePosition) / this.totalResult) * 100);
		},
		showLoadBtn() {
			return !(this.page == this.pages);
		},
		proId() {
			return this.$store.state.proId;
		},
	},
	methods: {
		showMoreArr(item) {
			if (item.moreText && item.attributeList.length > 8) {
				return item.attributeList.slice(0, 8);
			} else {
				return item.attributeList;
			}
		},
		showMore(item) {
			item.moreText = !item.moreText;
		},
		viewMoreFn(num, type) {
			this.showMoreIndex = num;
			let cateitemBox = document.querySelector(".cateitemBox");
			if (type == "more") {
				this.tagViewStatus = true;
				cateitemBox.style.maxHeight = "none";
			} else {
				this.tagViewStatus = false;
				cateitemBox.style.maxHeight = "34px";
			}
		},
		updateTag(item) {
			this.loadLabel = true;
			let val = this.selectedParamsObj[item.parentId];
			if (!val) {
				this.$set(this.selectedParamsObj, item.parentId, [item]);
				// this.selectedParamsObj[item.fatherId] = [item];
				this.addTag = true;
				this.productTag = item.name;
				this.selectedParams.push(item);
			} else {
				let findIndex = val.findIndex((v) => {
					return v.id == item.id;
				});
				if (findIndex > -1) {
					//重复选择暂不处理
				} else {
					//选择新的替换掉旧的
					// this.selectedParamsObj[item.parentId] = [];
					// this.selectedParamsObj[item.parentId] = [item];
					this.$set(this.selectedParamsObj, item.parentId, []);
					this.$set(this.selectedParamsObj, item.parentId, [item]);
					this.selectedParams = this.selectedParams.filter((citem) => citem.parentId !== item.parentId);
					this.selectedParams.push(item);
					this.addTag = true;
					this.productTag = item.name;
				}
				if (val.length == 0) delete this.selectedParamsObj[item.parentId];
			}
		},
		updateLabel() {
			this.getAppLabelAttributeList()
				.then((res) => {
					let labelData = res.data;
					labelData.forEach((item) => {
						this.activeNames.push(item.id);
					});
					this.labelData = labelData;
				})
				.finally(() => {
					this.listScrollTop();
				});
		},
		getAppLabelAttributeList() {
			return new Promise((resolve) => {
				getTagsList().then((res) => {
					resolve(res);
				});
			});
		},

		goHome() {
			this.$router.push({
				path: "/",
			});
		},
		getLabelType(type) {
			//标签类型 1.单选颜色样式，2.多选一列样式，3.多选两列样式，4.仅勾选是/否样式，5.拖动条样式），如果类型是拖动样式，属性值名称是最小值，属性值备注是最小值
			const obj = {
				1: "color",
				2: "checkbox-style1",
				3: "checkbox-style2",
				4: "switch",
				5: "range",
			};
			return obj[type];
		},

		changeRange() {
			this.showPriceRange = true;
			this.useAdNum = 0;
			this.doubleAd = 0;
			this.usePosition = 0;
			//记录产品价格区间
			try {
				if (window.$nuxt.$store.state.advertConfig.googleConfig && window.gtag) {
					gtag("event", "select_content", {
						content_type: "producPrice",
						content_value: this.priceRange,
					});
				}
			} catch (error) {}
			this.searchProduct();
			this.updateLabel();
		},

		clearRange() {
			this.priceRange = [-10000, 10000];
			this.showPriceRange = false;
			this.useAdNum = 0;
			this.doubleAd = 0;
			this.usePosition = 0;
			this.searchProduct();
			this.updateLabel();
		},

		delColor() {
			this.colorItem = "";
			this.useAdNum = 0;
			this.doubleAd = 0;
			this.usePosition = 0;
			this.searchProduct();
		},

		delTag(item) {
			let ind = this.selectedParams.findIndex((citem) => citem.parentId === item.parentId);
			if (ind >= 0) {
				this.selectedParams.splice(ind, 1);
			}
			this.$set(this.selectedParamsObj, item.parentId, undefined);
			delete this.selectedParamsObj[item.parentId];
		},

		delAllTag() {
			this.selectedParams = [];
			this.selectedParamsObj = {};
			this.colorItem = "";
			this.page = 1;
			this.keyword = "";
			this.priceRange = [-990000, 990000];
			this.showPriceRange = false;
			this.addTag = false;
			this.productTag = "";
		},

		changeKeyword(val) {
			this.customKeyword = !!val;
			this.useAdNum = 0;
			this.doubleAd = 0;
			this.usePosition = 0;
			this.debounceSearchProduct();
			this.debounceLabel();
		},

		toggleColor(item) {
			this.loadLabel = true;
			if (this.colorItem.id === item.id) {
				this.colorItem = "";
			} else {
				this.colorItem = item;
			}
			this.useAdNum = 0;
			this.doubleAd = 0;
			this.usePosition = 0;
			this.searchProduct();
		},

		formatUrlName(str) {
			return str.substring(1, str.length);
		},

		changeCategory(item) {
			let path = this.isStockPage == "1" ? item.shopRouting : item.customRouting;
			if (this.isDialog) {
				this.$emit("changeCategory", path);
			} else {
				this.$router.push({
					path: path,
				});
			}
		},

		getLabelAttributeValueIds() {
			let arr = [];
			for (let i in this.selectedParamsObj) {
				arr.push({
					parentId: i,
					childIds: this.selectedParamsObj[i].map((item) => item.id),
				});
			}
			if (this.colorItem) {
				//google记录选择的颜色
				try {
					if (window.$nuxt.$store.state.advertConfig.googleConfig && window.gtag) {
						gtag("event", "select_content", {
							content_type: "productColor",
							content_value: this.colorItem.valueName,
						});
					}
				} catch (error) {}
				arr.push({
					parentId: this.colorItem.parentId,
					childIds: [this.colorItem.id],
				});
			}
			return arr;
		},

		getAttributeValueIds() {
			let tagData = [];
			for (let i in this.selectedParamsObj) {
				let val = this.selectedParamsObj[i].map((item) => item.id);
				if (val.length > 0) {
					tagData.push(...val);
				}
			}
			if (this.colorItem) {
				tagData.push(this.colorItem.id);
			}
			return tagData;
		},

		getProduct(customPage) {
			return new Promise((resolve) => {
				getImagesList({
					page: customPage || this.page,
					pageSize: this.pageSize,
					tagsIds: this.getAttributeValueIds(),
				})
					.then((res) => {
						this.totalResult = res.data.total;
						this.pages = res.data.pages;
						this.page = res.data.current;
						this.firstCate = false;
						resolve(res);
					})
					.catch((error) => {
						if (error.name === "AbortError") {
							console.log("Request canceled:", error.message);
						}
					});
			});
		},

		searchProduct() {
			this.loadingProduct = true;
			this.page = 1;
			this.getProduct()
				.then((res) => {
					this.productList = res.data.records;
				})
				.finally(() => {
					this.loadingProduct = false;
					this.loadLabel = false;
				});
		},

		setDefaultShowImg(list) {
			if (!list) {
				return;
			}
			if (this.isStockPage && this.$store.state.proTheme == "11") {
				list.forEach((item) => {
					item.showImgSrc = item.sceneImg;
					item.selectedColorIndex = -1;
				});
				return list;
			}
			let colorItem = this.colorItem,
				colorId;
			if (colorItem) {
				colorId = colorItem.id;
			}
			list.forEach((item) => {
				if (item.productParamList && item.productParamList.length > 0) {
					let productParamList = item.productParamList;
					let findColorIndex = productParamList.findIndex((citem) => citem.attributeValueId === colorId);
					if (findColorIndex > -1) {
						try {
							item.showImgSrc = JSON.parse(productParamList[findColorIndex].imgJson)[0]?.url;
							item.selectedColorIndex = findColorIndex;
						} catch (e) {}
					} else {
						if (item.sceneImg) {
							item.showImgSrc = item.sceneImg;
							item.selectedColorIndex = -1;
						} else {
							try {
								item.showImgSrc = JSON.parse(productParamList[0].imgJson)[0]?.url;
								item.selectedColorIndex = 0;
							} catch (e) {}
						}
					}
				} else {
					item.showImgSrc = item.sceneImg;
					item.selectedColorIndex = -1;
				}
			});
			return list;
		},

		sortProduct(command) {
			this.sorts = command;
			//Google记录用户喜欢最新产品还是推荐产品排序
			try {
				if (window.$nuxt.$store.state.advertConfig.googleConfig && window.gtag) {
					gtag("event", "select_content", {
						content_type: "productSort",
						content_value: command == 1 ? "newest" : "recommend",
					});
				}
			} catch (error) {}
			this.useAdNum = 0;
			this.doubleAd = 0;
			this.usePosition = 0;
			this.searchProduct();
		},

		toggleFilter() {
			this.showFilter = !this.showFilter;
			let loadBtnMainBox = document.querySelector(".loadBtnMainBox");
			if (loadBtnMainBox && !this.isMobile) {
				if (!this.showFilter) {
					loadBtnMainBox.style.width = "100%";
					loadBtnMainBox.style.marginLeft = "0";
				} else {
					loadBtnMainBox.style.width = "calc(100% - 265px)";
					loadBtnMainBox.style.marginLeft = "265px";
				}
			}
			if (this.isMobile) {
				const nuxtContent = document.querySelector(".nuxtContent");
				if (nuxtContent) {
					nuxtContent.style.zIndex = "999";
					if (!this.showFilter) {
						nuxtContent.style.zIndex = "auto";
					}
				}
				let leftBar = document.querySelector(".leftBar2");
				if (leftBar) leftBar.style.top = 0;

				let wrap = document.querySelector(".wrap");
				if (wrap) {
					wrap.style.zIndex = "auto";
					if (this.showFilter) wrap.style.zIndex = 999;
				}
			}
		},

		clickTitle(val) {
			let findInd = this.activeNames.findIndex((item) => val.id === item);
			if (findInd >= 0) {
				this.activeNames.splice(findInd, 1);
			} else {
				this.activeNames.push(val.id);
			}
		},

		handleResize() {
			let header = document.querySelector("#modalHeader");
			this.headerHeight = typeof header?.offsetHeight === "number" ? header.offsetHeight : 0;
			if (this.isDialog) {
				this.headerHeight = 0;
			}
			this.setSticky();
			const windowWidth = document.documentElement.clientWidth;
			this.screenHeight = window.innerHeight;
			if (windowWidth < 1400 && window.innerWidth >= 1400) {
				this.useAdNum = 0;
				this.doubleAd = 0;
				this.usePosition = 0;
				this.debounceSearchProduct();
			} else if (windowWidth >= 1400 && window.innerWidth < 1400) {
				this.useAdNum = 0;
				this.doubleAd = 0;
				this.usePosition = 0;
				this.debounceSearchProduct();
			}
		},

		setSticky() {
			if (!this.isMobile) {
				let leftBar = document.querySelector(".leftBar2");
				this.selectLabelHeight = this.headerHeight;
				if (leftBar) {
					leftBar.style.top = this.headerHeight + "px";
				}
			} else {
				let topRight = document.querySelector(".top-right");
				if (topRight) {
					topRight.style.top = this.headerHeight + "px";
				}
			}
		},
		async loadThreeData(type = false) {
			let num = this.page;
			if (!type) {
				num = this.page + 1;
				if (this.totalResult === 0 || this.pages < num || this.page <= 0) {
					return false;
				}
			}

			this.loadingProduct = true;
			const res = await this.getProduct(num);
			let addProduct = res.data.records;
			this.productList = this.productList.concat(addProduct);
			this.loadingProduct = false;
		},
		listScrollTop() {
			try {
				window.scrollTo({
					top: 0,
					behavior: "smooth",
				});
			} catch (error) {
				console.log("找不到dom");
			}
		},
		activeCateName(item) {
			let data = this.isStockPage == "1" ? item.shopRouting : item.customRouting;
			return data && this.formatUrlName(data) === this.cateName;
		},
		openSnackbar() {
			this.snackbar = true;
		},
		setModalType(target, targetArray, clickType, event, other) {
			this.$setModal(this, target, targetArray, clickType, event, other);
		},
		openDetail(item, index) {
			this.$refs.dialog.showModal();
			this.showContent = true;
			this.$nextTick(() => {
				const swiper = this.$refs.swiperBox;
				if (swiper) {
					swiper.setSwiperTo(index, item);
				}
			});
		},
		handleClose() {
			this.$refs.dialog.close();
			this.showContent = false;
		},
	},
	created() {},
	mounted() {
		if (process.env.isManage) {
			this.isManage = true;
			return false;
		}
		if (this.isMobile) this.showFilter = false;
		let header = document.querySelector("#modalHeader");
		this.headerHeight = typeof header?.offsetHeight === "number" ? header.offsetHeight : 0;
		if (this.isDialog) {
			this.headerHeight = 0;
		}
		this.halfName = this.$route.params.halfDesign;
		this.debounceSearchProduct = debounce(this.searchProduct, 600);
		this.debounceLabel = debounce(this.updateLabel, 600);
		this.screenHeight = window.innerHeight;
		this.cateName = this.$route.params.pathMatch;
		if (this.isDialog) {
			this.cateName = this.formatUrlName(this.name);
		}
		window.onresize = this.handleResize;
		Promise.all([
			this.getAppLabelAttributeList(),
			listAllCategoryByParentId({
				id: this.parentCateId || this.cateId,
				onlyGetStockCate: this.isStockPage,
			}),
		]).then((result) => {
			let labelData = result[0].data,
				cateList = result[1].data;
			labelData.forEach((item) => {
				this.activeNames.push(item.id);
			});
			this.activeNames.push(-1);
			this.labelData = labelData;
			//左侧选择栏没有数据关闭
			// if (!this.labelData || this.labelData.length == 0) this.showFilter = false;
			this.cateList = cateList;
			let name = this.cateList.find((item) => {
				return item.id == this.cateId;
			});
			if (name) this.fatherCateName = name.name;
			else this.fatherCateName = this.halfCateDTO?.name;
		});
		this.$nextTick(() => {
			this.fristWatch = true;
			this.setSticky();
		});
		let _this=this
		let dialog = document.querySelector("dialog[closeable]");
		if (dialog) {
			dialog.addEventListener("click", function (event) {
				if (event.target.classList.contains("mainContentModel")) {
					this.close();
					_this.handleClose();
				}
			});
		}
	},
	beforeDestroy() {
		window.onresize = null;
	},
};
</script>

<style scoped lang="scss">
::v-deep .custom-slider .v-range-slider__track-background {
	background-color: $color-second !important;
	/* 设置轨道背景颜色 */
}

// ::v-deep .custom-slider .v-slider__thumb {
// 	background-color: $color-second !important; /* 设置滑动块滑动后的颜色 */
// }
::v-deep .custom-slider .v-range-slider__track-fill {
	background-color: $color-primary !important;
	/* 设置你想要的背景颜色 */
}

.bps_container {
	background-color: #f9f9f9;
	@include respond-to(mb) {
		font-size: 12px;
	}
}

.loadFirstData {
	display: flex;
	justify-content: center;
	align-items: center;
	min-height: 300px;
}

.find-agent {
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	margin-bottom: 10px;
	width: 100%;
	height: 100%;
	padding: 20px;
	text-align: center;
	background: url(https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20221018/2050Ntxm6NPD.png) center/cover no-repeat;

	.c-title {
		font-size: 48px;
		color: #00156b;
	}

	.des {
		max-width: 680px;
		margin: 10px 0 20px;
		font-size: 32px;
		color: #00156b;
	}

	.v-btn {
		width: 200px;
		margin: 0 10px;
	}

	@media screen and (max-width: $mb-width) {
		.c-title {
			font-size: 24px;
		}

		.des {
			font-size: 16px;
		}

		.v-btn {
			width: 80%;
			margin-bottom: 10px;
		}
	}
}

.wrap {
	display: flex;
	flex-direction: column;
	background-color: #fff;
	@include respond-to(mb) {
		padding-bottom: 10px;
	}
}

.wrap.modal-box.isDialog {
	padding: 20px 30px 20px 20px !important;

	@include respond-to(mb) {
		padding: 10px 20px 10px 10px !important;
	}
}

.luggagetagsList {
	overflow: initial !important;
	background-color: #f9f9f9;
}

img {
	width: auto;
	max-width: 100%;
	max-height: 100%;
	vertical-align: middle;
}

.borderBox {
	width: 100%;
	height: 0px;
	border: 1px solid #f0f0f0;
}

.litterArrow {
	margin-left: 2px;

	&::before {
		font-size: 8px;
	}
}

.rightHeader {
	width: 100%;
	padding: 20px;
	display: flex;
	align-items: center;
	justify-content: center;
	flex-wrap: wrap;
	// width: fit-content;
	column-gap: 3vw;
	background-color: #fff;
	@include respond-to(mb) {
		gap: initial;
	}
}

.top-right {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 10px 0;

	&.sticky {
		z-index: 10;
	}

	.filterSearch {
		flex: 1;
		display: flex;
		align-items: center;

		&.textSearch {
		}
	}

	.search-area {
		position: relative;

		input {
			min-width: 400px;
			background-color: #fff;
			height: 36px;
			line-height: 36px;
			outline: none;
			border: none;
			padding: 0 20px 0 20px;
		}

		i {
			position: absolute;
			right: 10px;
			top: 50%;
			transform: translateY(-50%);
			font-size: 26px;
			color: #9b9b9b;
		}
	}

	.filter-area {
		display: flex;
		justify-content: space-between;
		align-items: center;
		font-weight: 400;
		font-size: 16px;
		color: #333333;

		.filter1 {
			display: flex;
			align-items: center;
			cursor: pointer;
			.filterTotal {
				font-weight: normal;
				font-size: 14px;
				color: #666666;
				margin-left: 6px;
			}

			b {
				margin-right: 4px;
				font-size: 18px;
			}
		}

		.filter2 {
			display: flex;
			align-items: center;

			.t1 {
				margin-right: 4px;
			}

			b {
				font-size: 12px;
			}
		}

		.filter3 {
			display: flex;
			align-items: center;
			border: 2px solid $color-primary;
			border-radius: 4px;

			svg {
				color: $color-primary;
			}

			& > div {
				padding: 4px 8px;
				cursor: pointer;
			}

			& > div.active {
				background-color: $color-primary;

				svg {
					color: #ffffff;
				}
			}
		}

		.v-btn {
			text-transform: capitalize;
		}
	}

	@include respond-to(mb) {
		flex: 1;
		border-radius: 4px;
		width: 100%;
		background-color: #fff;

		.search-area {
			position: relative;
			width: 100%;
			overflow: hidden;

			.inputBox {
				margin: 0 auto;
				text-align: center;
			}

			input {
				min-width: 100%;
				background-color: #fff;
				border-radius: 2px;
				height: 30px;
				line-height: 30px;
				outline: none;
				border: none;
				padding: 0 30px 0 20px;
			}

			i {
				position: absolute;
				right: 10px;
				top: 50%;
				transform: translateY(-50%);
				font-size: 20px;
				color: #9b9b9b;
			}
		}

		.filter-area {
			font-size: 12px;
			width: 100%;

			.filter1 {
				b {
					margin-right: 4px;
					font-size: 14px;
					font-weight: bold;
				}
				span {
					font-weight: bold;
					font-size: 16px;
					color: #333333;
				}
			}

			.filter2 {
				display: none;
			}

			& > div {
			}
		}
	}
}

#borderBoxSticky {
	background: #f4f5f7;
}

dialog {
	max-width: 100vw;
	max-height: 100%;
	border: 0;
	outline: none;

	.closeIcon {
		position: fixed;
		top: 1em;
		right: 1em;
		font-size: 14px;
		color: #fff;
		cursor: pointer;
		padding: 0.5em;
		z-index: 2001;
	}
}

dialog:-internal-dialog-in-top-layer::backdrop {
	background-color: rgba(0, 0, 0, 0.6);
	@include respond-to(mb) {
		background-color: rgba(0, 0, 0, 1);
	}
}

.nenoDialog {
	max-width: 100%;
	max-height: 100%;
	background-color: red;
	.mainContentModel {
		position: fixed;
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
		height: 100%;
		overflow: auto;
		white-space: nowrap;
		text-align: center;
		scrollbar-width: thin;
		background-color: rgba(0, 0, 0, 0.6);
		@include respond-to(mb) {
			background-color: rgba(0, 0, 0, 1);
		}
		&:after {
			content: "";
			display: inline-block;
			vertical-align: middle;
			height: 100%;
		}
		.dialogContent {
			max-width: 100%;
			position: relative;
			display: inline-block;
			vertical-align: middle;
			margin: 0 auto;
			text-align: left;
			white-space: normal;
		}
	}
}
.loadProgress {
	position: absolute;
	bottom: 0;
	left: 59%;
	transform: scale(1.2);
	z-index: 10;

	@include respond-to(mb) {
		left: 50%;
		transform: translateX(-50%);
	}
}

.loadBtnMainBox {
	width: calc(100% - 265px);
	margin-left: 265px;
	@include respond-to(mb) {
		width: 100%;
		margin-left: 0;
	}
}

.loadMoreBtn {
	width: 30%;
	min-width: 350px;
	margin: 0 auto;
	margin-bottom: 50px;
	text-align: center;
	font-weight: 400;
	font-size: 22px;
	color: #333333;

	.loadMoreBtnText {
		margin-bottom: 20px;
	}

	.loadBtnBox {
		margin-top: 20px;
		color: #fff;
		cursor: pointer;
		font-size: 16px;

		.loadBtn {
			// border: 1px solid $color-primary;
			border-radius: 5px;
			transition: 0.3s;

			&:hover {
				// background-color: $color-second;
			}
		}
	}

	@include respond-to(mb) {
		min-width: 300px;
		font-size: 18px;
		.loadMoreBtnText {
			margin-bottom: 10px;
		}
		.loadBtnBox {
			margin-top: 10px;
			font-size: 14px;
		}
	}
}

.content {
	padding-top: 20px;
	position: relative;
	display: grid;
	align-items: flex-start;
	grid-column-gap: 30px;
	grid-template-columns: 265px 1fr;
	padding-bottom: 50px;
	background-color: #f9f9f9;

	.load {
		display: flex;
		justify-content: center;
		align-items: center;
		min-height: 200px;
		padding: 20px;
	}

	.rightBar {
		padding-top: 10px;
		.rightPart {
			position: relative;
			display: flex;
			flex-direction: column;
			height: 100%;
			padding: 2px;
			overflow: hidden;

			.rightContent {
				flex: 1;
				width: 100%;
				height: 0;
				position: relative;

				.productWrap {
					// align-items: flex-start;
					display: grid;
					grid-template-columns: repeat(3, 1fr);
					grid-gap: 20px;

					@include respond-to(pad) {
						grid-template-columns: repeat(3, 1fr);
					}

					@include respond-to(mb) {
						grid-template-columns: repeat(2, 1fr);
						grid-gap: 10px;
					}
				}

				@media (any-hover: hover) {
					.good-item:hover .good-back {
						background-color: var(--color-second);
					}
				}
			}
		}
	}

	@include respond-to(mb) {
		grid-template-columns: 1fr;
		padding-bottom: 36px;
	}
}

.luggageList {
	cursor: pointer;
	.dataBox {
		position: relative;
		width: 100%;
		height: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
		// background: repeating-linear-gradient(45deg, rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0.1) 10px, #ffffff 10px, #ffffff 20px);
	}
	img {
		width: 100%;
		height: 100%;
		object-fit: contain;
	}
	.imgTriangle {
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		width: 65px;
		height: 65px;
		border-radius: 50%;
		background: #f4f0f5;
		cursor: pointer;
		z-index: 1;
		&::before {
			content: "";
			width: 30%;
			height: 36%;
			position: absolute;
			top: 50%;
			left: 50%; /* 调整位置以适应不同的容器大小 */
			transform: translate(-50%, -50%);
			background: linear-gradient(90deg, var(--color-bright), var(--btn-primary));
			clip-path: polygon(0 0, 100% 50%, 0 100%); /* 剪裁三角形形状 */
			border-radius: 3px;
		}
		@include respond-to(mb) {
			width: 50px;
			height: 50px;
		}
	}
	@media (any-hover: hover) {
		&:hover {
			box-shadow: 0px 0px 1px 1px var(--color-bright), -1px -1px 1px 1px var(--btn-primary);
		}
		.imgTriangle {
			&:hover {
				box-shadow: 0 4px 8px rgba(0, 0, 0, 0.6);
			}
		}
	}
}

.sticky {
	position: sticky;
	top: 0;
	z-index: 1;
}

.litterMock::before {
	content: "";
	width: 100%;
	height: 10px;
	position: absolute;
	bottom: -10px;
	left: 0;
	background-color: #f2f2f2;
}

.litterMock::after {
	content: "";
	width: 100%;
	height: 10px;
	position: absolute;
	top: -10px;
	left: 0;
	background-color: #f2f2f2;
}

.changeLoad {
	position: absolute;
	inset: 0;
	width: 100%;
	// height: 100vh;
}

.overlay {
	align-items: center;
	border-radius: inherit;
	display: flex;
	justify-content: center;
	pointer-events: auto;
	width: 100%;
	background-color: rgba(0, 0, 0, 0.05);
}

/* HTML: <div class="loader"></div> */
.loader {
	width: 50px;
	aspect-ratio: 1;
	display: grid;
	border: 4px solid #0000;
	border-radius: 50%;
	border-right-color: $color-primary;
	animation: l15 1s infinite linear;
}

.loader::before,
.loader::after {
	content: "";
	grid-area: 1/1;
	margin: 2px;
	border: inherit;
	border-radius: 50%;
	animation: l15 2s infinite;
}

.loader::after {
	margin: 8px;
	animation-duration: 3s;
}

@keyframes l15 {
	100% {
		transform: rotate(1turn);
	}
}
</style>
