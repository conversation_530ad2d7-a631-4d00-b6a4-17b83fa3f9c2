<template>
	<div class="bannerQuoteWrap">
		<div class="quoteBoxContent stepList">
			<template v-if="$store.getters.isManage"> Banner Quote Area</template>
			<template v-else>
				<template v-for="(item, index) in generalData">
					<div class="step-item step-size" :key="index" v-if="item.customStepName === 'size' && currentStep === 1">
						<StepTitle :title="getTitle(item)" :step="getStep(item)">
							<template #suffix>
								<a href="javascript:;" @click="sizeDialog = true" class="help">{{ lang.bannerQuote.sizeHelp }} <b class="icon-info"></b></a>
							</template>
						</StepTitle>
						<StepSize :itemData="item" :selectedData="selectedData" @showSizeDialog="showSizeDialog" @selectQuoteParams="selectQuoteParams($event.item,$event.citem)"></StepSize>
					</div>
					<div class="step-item step-qty" :key="index" v-if="item.customStepName === 'qty' && currentStep === 1">
						<StepTitle :title="getTitle(item)" :step="getStep(item)"></StepTitle>
						<StepQty :qtyList="qtyList" :hideSavePrice="device==='mb'" :isCustom.sync="isCustom" :selectedQtyInd.sync="selectedQtyInd" :customNumber.sync="customNumber" :customNumberUnitPrice="customNumberUnitPrice" :customNumberPrice="customNumberPrice" @calcPrice="debounceCalcPrice"></StepQty>
						<FreeTip></FreeTip>
						<div class="nextBtn" v-show="currentStep === 1">
							<button primary @click="nextStep" title="custom keychains" :disabled="customQty <= 0">{{ lang.continue }} <b class="icon-xiayige"></b></button>
							<p class="tip">{{ lang.bannerQuote.next }}</p>
						</div>
					</div>
				</template>
			</template>
		</div>
        <SizeDialog :size-dialog.sync="sizeDialog"></SizeDialog>
	</div>
</template>

<script>
import { calculate, calculateAll, getInfo, otoAddCart } from "@/api/pins";
import { debounce, deepClone } from "@/utils/utils";
import { getCateParamRelationByCateId } from "@/api/web";
import BaseDialog from "@/components/Quote/BaseDialog.vue";
import StepTitle from "@/components/modal/Quote/QuoteComponents/StepTitle.vue";
import StepSize from "@/components/modal/Quote/QuoteComponents/StepSize.vue";
import StepQty from "@/components/modal/Quote/QuoteComponents/StepQty.vue";
import FreeTip from "@/components/modal/Quote/QuoteComponents/FreeTip.vue";
import SizeDialog from "@/components/modal/Quote/QuoteComponents/SizeDialog.vue";

const setIndex = (data) => {
	if (!data) {
		return false;
	}
	let index = 0;
	data.forEach((item) => {
		if (item) {
			if (!item.noShowDetail) {
				index += 1;
			}
			item.customIndex = index;
		}
	});
	return data;
};
const sortByKey = (array, key) => {
	return array.sort(function (a, b) {
		let x = a.priceInfo[key]; //如果要从大到小,把x,y互换就好
		let y = b.priceInfo[key];
		return x < y ? -1 : x > y ? 1 : 0;
	});
};

const getMyCustomStepName = (item) => {
	let paramName = item.paramName,
		type = item.paramType;
	let qtyNameArr = ["qty", "Quantity"],
		attachNameArr = ["Ribbon"],
		uploadNameArr = ["Upload Artwork & Comments"];
	if (type === "SIZE") {
		return "size";
	}
	if (type === "COLOR") {
		return "color";
	}
	if (type === "DISCOUNT") {
		return "discount";
	}
	if (qtyNameArr.includes(paramName)) {
		return "qty";
	}
	if (attachNameArr.includes(paramName)) {
		return "attachment";
	}
	if (uploadNameArr.includes(paramName)) {
		return "upload";
	}
};
const addCustomProperty = (data) => {
	let handle = (list) => {
		for (let i = 0; i < list.length; i++) {
			let item = list[i];
			item.remark = "";
			item.noShowDetail = false;
			item.isHidden = false; //不可选属性
			item.files = [];
			item.inputNum = undefined;
			if (item.childList && item.childList.length) {
				handle(item.childList);
			}
			item.customStepName = getMyCustomStepName(item);
			//特殊处理
			if (item.paramName === "Upload Artwork & Comments") {
				item.alias = "Upload Your Artwork";
			}
		}
	};
	handle(data);
	return data;
};
export default {
	props: {
		cateId: {
			type: [String, Number],
			required: true,
		},
		pid: {
			type: [String, Number],
			required: true,
		},
	},
	data() {
		return {
			uploadArtworkList: [],
			sizeDialog: false,
			showQuote: false,
			selectedData: {},
			generalData: [],
			currentStep: 1,
			selectedParamsValueParent: {},
			selectedParamsValue: {},
			isCustom: false,
			selectedQtyInd: -1,
			qtyList: [],
			customNumber: "",
			debounceCalcPrice: null,
			priceInfo: {},
			cateInfo: {},
			loadAddCart: false,
			isLater: false,
			remark: "",
		};
	},
	components: {SizeDialog, FreeTip, StepQty, StepSize, StepTitle, BaseDialog },
	watch: {
		selectedData: {
			handler() {
				if (!this.showQuote) {
					return false;
				}
				this.debounceCalcPrice();
			},
			deep: true,
		},
	},
	computed: {
		previewMode() {
			return process.env.VUE_APP_MODE === "Preview";
		},
		projectName() {
			if (this.previewMode) {
				return "o2o-site-style";
			} else {
				return this.$store.state.proName;
			}
		},
		customNumberPrice() {
			return this.isCustom && this.customNumber ? this.priceInfo.totalPrice : 0;
		},
		customNumberUnitPrice() {
			return this.isCustom && this.customNumber ? this.priceInfo.foundationUnitPrice : 0;
		},
		lang() {
			return this.$store.getters.lang.quote || {};
		},
		device() {
			return this.$store.state.device;
		},
		customQty() {
			if (this.isCustom) {
				return parseInt(this.customNumber) || 0;
			} else {
				return parseInt((this.qtyList && this.qtyList.length && this.qtyList[this.selectedQtyInd]?.totalQuantity) || 0);
			}
		},
		proId() {
			if (this.previewMode) {
				return 148;
			} else {
				return this.$store.state.proId;
			}
		},
		userUUID() {
			return this.$store.state.userUUID;
		},
		userId() {
			return this.$store.state.userInfo?.id;
		},
		isLogin() {
			return this.$store.getters.isLogin;
		},
		userInfo() {
			return this.$store.state.userInfo;
		},
	},
	methods: {
        showSizeDialog(bool){
          this.sizeDialog = bool
        },
		getTitle(item) {
			return this.lang.Select + " " + item.alias;
		},
		getStep(item) {
			return this.lang.step + item.customIndex;
		},
		nextStep() {
			this.currentStep += 1;
			this.$emit("nextStep");
		},
		getQuoteParam(type = "inquiry") {
			let selectedData = deepClone(this.selectedData),
				finaData = [],
				generalData = deepClone(this.generalData);
			for (let i = 0; i < generalData.length; i++) {
				let item = generalData[i];
				if (item.customStepName === "qty") {
					continue;
				}
				if (item.customStepName === "upload") {
					item.childList = [];
					item.later = this.isLater;
					finaData.push(item);
					continue;
				}
				//将选中参数里面的childlist置空
				if (selectedData[item.paramName] && selectedData[item.paramName].length) {
					selectedData[item.paramName].forEach((c) => {
						c.childList = [];
						c.quantity = c.inputNum;
						if (!c.giftQuantity) {
							c.giftQuantity = 0;
						}
					});
				}
				item.childList = selectedData[item.paramName] || [];
				finaData.push(item);
			}
			let files = [];
			let artworkList = this.uploadArtworkList;
			if (artworkList.length > 0) {
				artworkList.forEach((item) => {
					item.secure_url && files.push(item.secure_url);
				});
			}
			return {
				classificationData: this.cateInfo,
				finaData: finaData,
				fontData: {
					fontImgCustom: files,
					comments: this.remark,
				},
				designCanvas: [],
			};
		},
		getPriceParam(qty) {
			const data = {
				cateId: this.cateId,
				discountId: "",
				packingIdList: [],
				paramIdList: [],
				projectName: this.projectName,
				quantity: "",
				sizeId: "",
				upgradesQtyDTO: [],
				qtyDetailDTOS: [],
			};
			let generalData = this.generalData,
				selectedData = this.selectedData;
			for (let i = 0; i < generalData.length; i++) {
				let item = generalData[i];
				let paramName = item.paramName;
				let selectedVal = selectedData[paramName];
				if (item.customStepName === "qty") {
					data.quantity = qty || Number(this.customQty);
					continue;
				}
				if (item.customStepName === "upload") {
					continue;
				}
				if (!selectedVal) {
					continue;
				}
				selectedVal.forEach((citem) => {
					let paramType = citem.paramType;
					switch (paramType) {
						case "NORMAL":
							data.paramIdList.push(citem?.priceInfo?.id);
							break;
						case "DISCOUNT":
							data.discountId = citem?.priceInfo?.id;
							break;
						case "SIZE":
							data.sizeId = citem?.priceInfo?.id;
							break;
						default:
							break;
					}
				});
			}
			if (!data.qtyDetailDTOS.length) {
				delete data.qtyDetailDTOS;
			}
			return data;
		},
		calcPrice() {
			let priceParam = this.getPriceParam();
			if (priceParam.sizeId && priceParam.cateId) {
				calculateAll(priceParam).then((res) => {
					let qtyList = res.data.filter((item) => item.isBannerQuote);
					let prevFoundationUnitPrice = qtyList[0]?.foundationUnitPrice;
					qtyList.forEach((item, index) => {
						if (index > 0) {
							item.save = (((prevFoundationUnitPrice - item.foundationUnitPrice) / prevFoundationUnitPrice) * 100).toFixed(0);
						}
					});
					this.qtyList = qtyList;
				});
			}
			if (priceParam.sizeId && priceParam.cateId && (priceParam.quantity || priceParam.qtyDetailDTOS?.length)) {
				calculate(priceParam).then((res) => {
					this.priceInfo = res.data;
				});
			}
		},
		//参数选中事件
		async selectQuoteParams(item, citem) {
			this.selectedParamsValueParent = item;
			this.selectedParamsValue = citem;
			this.$set(this.selectedData, item.paramName, [citem]);
		},
		async addCart() {
			//如果是临时项目
			if (this.$store.state.isTemporary) {
				this.$store.commit("setMask", "temporary");
				return false;
			}
			if (this.loadAddCart) {
				return false;
			}
			this.loadAddCart = true;
			let quoteParam = this.getQuoteParam("cart");
			let priceParam = this.getPriceParam();
			let data = {
				proId: this.proId,
				uuid: this.isLogin ? null : this.userUUID,
				userId: this.isLogin ? this.userId : null,
				buyType: 7,
				quoteCateId: this.pid,
				isMobile: this.device === "mb" ? 1 : 0,
				quoteCateChildId: priceParam.cateId,
				quantity: this.customQty,
				quotePriceParam: JSON.stringify(priceParam),
				quoteParam: JSON.stringify(quoteParam),
				isBannerQuote: 1,
			};
			otoAddCart(data)
				.then((res) => {
					this.$toast.success(res.message);
					this.$router.push({
						path: "/cart",
					});
				})
				.finally(() => {
					setTimeout(() => {
						this.loadAddCart = false;
					}, 1000);
				});
		},
		addToCart(data) {
			this.uploadArtworkList = data.uploadList;
			this.remark = data.remark;
			this.addCart();
		},
		skip() {
			this.isLater = true;
			this.addCart();
		},
		back() {
			this.currentStep = 1;
		},
	},
	async created() {
		this.debounceCalcPrice = debounce(this.calcPrice, 300);
	},
	async mounted() {
		if (this.$store.getters.isManage) {
			return false;
		}
		this.$Bus.$on("back", this.back);
		this.$Bus.$on("skip", this.skip);
		this.$Bus.$on("addToCart", this.addToCart);
		try {
			let result = await Promise.all([
				getInfo({ id: this.cateId }),
				getCateParamRelationByCateId({
					cateId: this.cateId,
					isBannerQuote: 1,
				}),
			]);
			this.cateInfo = result[0]?.data || {};
			let data1 = result[1]?.data;
			let selectedData = {},
				selectedQtyInd = -1,
				selectedQty = 0,
				generalData = setIndex(sortByKey(addCustomProperty(data1), "bannerQuoteStepIndex"));
			//默认选中参数
			if (generalData?.length) {
				generalData.forEach((item) => {
					let customStepName = item.customStepName;
					if (customStepName !== "qty" && customStepName !== "upload") {
						let findDefault = item.childList.find((c) => c.priceInfo.isBannerQuoteSelected);
						if (findDefault) {
							selectedData[item.paramName] = [findDefault];
							//选中尺寸下的默认数量
							if (customStepName === "size") {
								try {
									let priceInfo = findDefault.priceInfo;
									let increasePrice = JSON.parse(priceInfo.increasePrice).filter((item) => item.isBannerQuote);
									let defaultQtyIndex = increasePrice.findIndex((q) => q.isBannerQuoteSelected);
									if (defaultQtyIndex > -1) {
										selectedQtyInd = defaultQtyIndex;
										selectedQty = increasePrice[defaultQtyIndex].quantity;
									}
								} catch (e) {}
							}
						}
					}
				});
			}
			this.selectedData = selectedData;
			this.generalData = generalData;
			this.selectedQtyInd = selectedQtyInd;
			let priceParam = this.getPriceParam(selectedQty),
				priceResult;
			priceResult = await Promise.all([calculateAll(priceParam), calculate(priceParam)]);
			let qtyList = (priceResult && priceResult[0].data.filter((item) => item.isBannerQuote)) || [];
			let prevFoundationUnitPrice = qtyList[0]?.foundationUnitPrice;
			qtyList.forEach((item, index) => {
				if (index > 0) {
					item.save = (((prevFoundationUnitPrice - item.foundationUnitPrice) / prevFoundationUnitPrice) * 100).toFixed(0);
				}
			});
			this.qtyList = qtyList;
			this.priceInfo = (priceResult && priceResult[1].data) || {};
			this.showQuote = true;
		} catch (e) {
			console.log(e);
		}
	},
	beforeDestroy() {
		this.$Bus.$off("back");
		this.$Bus.$off("skip");
		this.$Bus.$off("addToCart");
	},
};
</script>

<style lang="scss" scoped>
.stepBar {
	width: fit-content;
	margin: 0 auto 0.44rem;

	.stepItem {
		b {
			color: #68bd2c;
		}

		.stepLine {
			width: 11rem;
			border: 2px solid #ffffff;

			&.active {
				border-color: #68bd2c;
			}
		}

		.stepNum {
			display: flex;
			justify-content: center;
			align-items: center;
			width: 1.06rem;
			height: 1.06rem;
			flex-shrink: 0;
			text-align: center;
			border-radius: 50%;
			background-color: #ffffff;
		}
	}
}

.stepList {
	display: grid;
	grid-template-columns: 1fr 2fr;
	grid-template-areas: "a b";
	gap: 1.2rem; /* 可选：设置行和列之间的间距 */

	@include respond-to(mb) {
		grid-template-columns: repeat(1, 1fr);
		grid-template-areas: none;
		gap: 0;
	}

	.step-item {
		font-size: 0.88rem;
		@include respond-to(mb) {
			font-size: 1rem;
			margin-bottom: 1.5rem;
		}
	}
}

.step-size {
	grid-area: a;

	.step-item-title .help {
		display: none;
	}

	@include respond-to(mb) {
		grid-area: auto;

		.step-item-title .help {
			flex: 1;
			display: block;
			text-align: right;
			font-size: 1rem;
		}
	}
}

.step-qty {
	grid-area: b;

	@include respond-to(mb) {
		grid-area: auto;
	}
}

.quoteBoxContent {
	min-width: 0;
	width: 47.5rem;
	height: 31rem;
	padding: 1.25rem 1.38rem;
	border-radius: 4px;
	background: #fff;
    border: 1px solid #DCDFE6;
	@include respond-to(mb) {
		width: 100%;
		height: 100%;
		padding: 1.2rem;
		background: #ffffff;
		border-radius: 0.42rem;
		border: 1px solid #d9d9d9;
	}
}

.nextBtn {
	display: flex;
	align-items: center;
	width: 100%;

	@include respond-to(mb) {
		flex-direction: column;
	}

	button {
		flex: 1;
		height: 3.13rem;
		margin-right: 1rem;
		@include respond-to(mb) {
			flex: auto;
			width: 100%;
			height: 2.5rem;
			margin-right: 0;
		}
	}

	.tip {
		white-space: nowrap;
		padding: 0.5rem;
		font-size: 1rem;
		color: #999999;
		text-align: center;
	}
}

.help {
	display: flex;
	align-items: center;
	font-size: 0.75rem;
	color: $color-primary;

	b {
		margin-left: 4px;
		font-size: 0.875rem;
		@include respond-to(mb) {
			font-size: 1rem;
		}
	}

	@include respond-to(mb) {
		display: none;
	}
}

button[disabled] {
	pointer-events: none;
	background: #e2e2e2;
}
</style>
