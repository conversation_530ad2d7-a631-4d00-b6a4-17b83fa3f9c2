<template>
	<div>
		<div class="table-container" v-if="type === 1 || type === 2">
			<table class="product-table" :class="`type${type}`">
				<caption>
					{{
						lang.carousel.proSpec
					}}
				</caption>
				<tbody>
					<tr v-for="(item, index) in newProductTableData" :key="index">
						<template v-for="citem in item">
							<th>{{ citem.key }}</th>
							<td>{{ citem.value }}</td>
						</template>
					</tr>
				</tbody>
			</table>
		</div>
		<div class="product-table" :class="`type${type}`" v-if="type === 3" @click="clickTable">
			<div class="product-table-item" v-for="(item, index) in newProductTableData" :key="index">
				<div v-for="(citem,cindex) in item" :key="cindex">
					<div class="item-key">{{ citem.key }}</div>
					<div class="item-value">
						<span v-for="(ccitem, ccindex) in citem.newValue" :key="ccindex" v-show="ccindex < 2"> {{ ccitem }}<span v-show="ccindex !== citem.newValue.length - 1">,</span> </span>
						<span v-show="citem.newValue.length > 2" style="color: #0066cc"> +{{ citem.newValue.length - 2 }} </span>
					</div>
				</div>
			</div>
            <div class="rightIcon">
                <b class="icon-right"></b>
            </div>
		</div>
	</div>
</template>

<script>
export default {
	props: {
		tableData: {
			type: Array,
			default: () => [],
		},
		type: {
			type: [Number],
			default: 1,
		},
	},
	computed: {
		lang() {
			return this.$store.getters.lang.quote.nameBadges;
		},
		newProductTableData() {
			let data = this.tableData,num,grouped = [];
            data.forEach((item) => {
                let key = Object.keys(item)[0];
                let value = String(item[key]);
                grouped.push({
                    key,
                    value
                })
            });
            let newGroup = []
            if (this.type === 2 || this.type === 3) {
                num = 1;
            } else {
                num = 2;
            }
            for (let i = 0; i < grouped.length; i += num) {
                newGroup.push(grouped.slice(i, i + num));
            }
			if (this.type === 3) {
                newGroup.forEach((item) => {
					item.forEach((citem) => {
                        citem.newValue = citem.value.split(",");
					});
				});
			}
			return newGroup;
		},
	},
	methods: {
		clickTable() {
			this.$emit("showTableDialog");
		},
	},
};
</script>

<style lang="scss" scoped>
.table-container {
	border-radius: 6px;
	border: 1px solid #f2f2f2;
	overflow: hidden;
}

.product-table.type1,
.product-table.type2 {
	width: calc(100% + 2px);
	margin: -1px;
	border-collapse: collapse;

	th,
	td {
        min-width: 80px;
		border: 1px solid #f2f2f2;
		padding: 8px;
        word-break: break-word;
        text-align: left;
	}

    th{
        width: 20%;
    }
    td{
        width: 30%;
    }

	caption {
		background-color: #f2f2f2;
		padding: 8px;
		font-weight: 700;
	}
}

.product-table.type3 {
    position: relative;
	overflow: hidden;
	display: flex;
	gap: 10px;
	padding: 10px 30px 10px 10px;
	background-color: #ffffff;

    .rightIcon{
        position: absolute;
        right: 0;
        top:0;
        bottom: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 5px;
    }

	.product-table-item {
		position: relative;
		display: flex;
		flex-direction: column;
		justify-content: center;
		flex-basis: 33.3%;
		flex-shrink: 0;


		&:nth-child(-n+2):after {
			content: "";
			position: absolute;
			right: 5%;
			top: 50%;
			transform: translateY(-50%);
			width: 1px;
			height: 20px;
			background-color: #eeeeee;
		}

		.item-value {
			color: #999999;
		}
	}
}
</style>
