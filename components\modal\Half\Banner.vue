<template>
	<!-- 模板外层的盒子上写theme属性，用来分别写每一套的样式，详见下方style -->

	<div class="modal-box" :class="modal.wrapClass" :style="modal.style">
		<div class="banner-box" v-for="(l, li) in modal.list" :key="li">
			<div class="imgWrap" @click="setModalType(l.banner, modal.list, 'banner')"
				v-if="l.banner && l.banner.value">
				<img :src="l.banner.value" :alt="l.banner.alt" :title="l.banner.alt"
					:style="{ ...modal.homeImgStyle, ...l.banner.style }" />
			</div>

			<div class="textContent" :style="modal.contentStyle">
				<EditDiv v-if="l.title" tagName="h1" v-model:content="l.title.value"
					@click.native="setModalType(l.title, modal.list, 'text')" />
				<EditDiv v-if="l.text" class="text" v-model:content="l.text.value" :style="modal.textStyle"
					@click.native="setModalType(l.text, modal.list, 'text')" />
				<div class="viewBtn" @click="viewMore" :style="modal.btnBoxStyle"
					v-show="parseFloat(containerHeight) > parseFloat(parsedLineHeight * 2) && containerHeight != 0 && $store.getters.isMobile">
					<button>{{ lang.ViewMore }}</button>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	name: "modalHalfBanner",
	props: { preview: { type: Boolean, default: false }, data: { type: Object, default: {} } },
	data() {
		return {
			modal: { style: {}, type: { index: 0, clickPosition: "outer", clickTarget: "" }, ...this.data },
			containerHeight: 0,
			parsedLineHeight: 0,
			imgWrap: "",
		};
	},
	watch: {
		modal: {
			handler(val) {
				if (process.env.isManage) this.$emit("update:data", val);
			},
			immediate: true,
			deep: true,
		},
	},
	computed: {
		lang() {
			return this.$store.getters.lang?.quote;
		}
	},
	methods: {
		setModalType(target, targetArray, clickType, event, other) {
			this.$setModal(this, target, targetArray, clickType, event, other)
		},
		viewMore() {
			const container = document.getElementsByClassName("text")[0];
			const viewBtn = document.getElementsByClassName("viewBtn")[0];

			if (container.classList.contains("moreText")) {
				container.style.transition = "0s";
				this.imgWrap.style.transition = "0s";
				container.style.height = `${2 * this.parsedLineHeight}px`;
				const textContent = document.getElementsByClassName("textContent")[0].clientHeight;
				this.imgWrap.style.height = parseInt(textContent) + 24 + "px";
				container.style.overflow = "hidden";
				container.style.transition = "0.3s";
				this.imgWrap.style.transition = "0.3s";
				viewBtn.textContent = this.lang.ViewMore;
			} else {
				container.style.transition = "0s";
				this.imgWrap.style.transition = "0s";
				container.style.height = `${this.containerHeight}px`;
				const textContent = document.getElementsByClassName("textContent")[0].clientHeight;
				this.imgWrap.style.height = parseInt(textContent) + 24 + "px";
				container.style.overflow = "visible";
				container.style.transition = "0.3s";
				this.imgWrap.style.transition = "0.3s";
				viewBtn.textContent = this.lang.ViewLess;
			}
			container.classList.toggle("moreText");
		},
		updateContainerHeight() {
			this.$nextTick(() => {
				const container = document.getElementsByClassName("text")[0];
				const lineHeight = window.getComputedStyle(container).getPropertyValue("line-height");
				this.parsedLineHeight = parseInt(lineHeight);
				document.documentElement.style.setProperty("--line-height", this.parsedLineHeight + "px");
				if (this.$store.getters.isMobile) {
					container.style.height = 2 * this.parsedLineHeight + "px";
				}
			});
		},
	},
	mounted() {
		this.updateContainerHeight();
		this.containerHeight = document.getElementsByClassName("text")[0].offsetHeight;
		this.imgWrap = document.getElementsByClassName("imgWrap")[0];
	},
};
</script>

<style lang="scss" scoped>
.modal-box.halfBanner {
	color: #ffffff;
	background-color: #f2f2f2;
	padding: 20px max(calc(50% - 700px), 1em) 1em !important;

	.banner-box {
		position: relative;
	}

	h1 {
		font-size: 22px;
		margin-bottom: 10px;
	}

	.imgWrap {
		// height: 100%;

		img,
		video {
			width: 100%;
			object-fit: cover;
			min-height: 12.5em;
		}
	}

	.btnWrap {
		color: #ffffff;
		display: inline-block;
	}

	.textContent {
		transform: translateY(-50%);
		position: absolute;
		right: 1em;
		left: 1em;
		top: 45%;

		.text {
			max-width: 50%;
			font-size: 14px;
			word-break: break-word;
			white-space: pre-wrap;
			// display: -webkit-box;
			// -webkit-line-clamp: 8; /* 指定文本显示的行数 */
			// -webkit-box-orient: vertical;
			// overflow: hidden;
		}
	}
}



@media screen and (max-width: $mb-width) {
	.modal-box.halfBanner {
		padding: 0;
		color: inherit;
		padding-top: 10px;
		position: static;

		.imgWrap img {
			height: 100%;
		}

		h1 {
			font-size: 1.25em;
		}

		.textContent {
			width: 90%;
			padding: 10px;
			min-height: 45%;
			overflow: hidden;
			border-radius: 10px;
			transform: translate(-50%, -50%);
			background-color: rgba(255, 255, 255, 0.8);
			position: absolute;
			left: 50%;
			top: 50%;

			.text {
				max-width: 100%;
				font-size: 12px;
				line-height: 1.3;
				height: calc(2 * var(--line-height));
				// display: -webkit-box;
				// -webkit-line-clamp: 8; /* 指定文本显示的行数 */
				// -webkit-box-orient: vertical;
				overflow: hidden;
				text-align: left;
				white-space: normal;
				word-break: break-word;
				// transition: 0.3s;
			}

			.viewBtn {
				padding: 4px;
				display: flex;
				margin: 0 10px;
				cursor: pointer;
				color: $color-primary;
				text-decoration: underline;
				justify-content: flex-end;
			}
		}
	}
}
</style>
