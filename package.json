{"name": "gs-product-web", "version": "1.0.1", "private": true, "scripts": {"dev:web": "cross-env MODE=dev_web nuxt --max_old_space_size=8192", "beta:web": "cross-env MODE=beta_web nuxt --max_old_space_size=8192", "prod:web": "cross-env MODE=prod_web nuxt --max_old_space_size=8192", "dev:manage": "cross-env MODE=dev_manage nuxt --max_old_space_size=8192", "beta:manage": "cross-env MODE=beta_manage nuxt --max_old_space_size=8192", "prod:manage": "cross-env MODE=prod_manage nuxt --max_old_space_size=8192", "build:web": "cross-env MODE=prod_web nuxt build --max_old_space_size=8192", "build:manage": "cross-env MODE=prod_manage nuxt build --max_old_space_size=8192", "build:beta:web": "cross-env MODE=beta_web nuxt build --max_old_space_size=8192", "build:beta:manage": "cross-env MODE=beta_manage nuxt build --max_old_space_size=8192", "start": "nuxt start", "generate:beta:web": "cross-env MODE=beta_web nuxt generate --max_old_space_size=8192", "generate:beta:manage": "cross-env MODE=beta_manage nuxt generate --max_old_space_size=8192", "generate:prod:web": "cross-env MODE=prod_web nuxt generate --max_old_space_size=8192", "generate:prod:manage": "cross-env MODE=prod_manage nuxt generate --max_old_space_size=8192", "generate:enamel": "cross-env MODE=enamel nuxt generate --max_old_space_size=8192", "upload:beta:web": "node sftp/upload.js betaWeb", "upload:beta:manage": "node sftp/upload.js betaManage", "upload:prod:web": "node sftp/upload.js proWeb", "upload:prod:manage": "node sftp/upload.js proManage"}, "dependencies": {"@nuxtjs/axios": "^5.13.6", "@nuxtjs/i18n": "^7.3.1", "@nuxtjs/style-resources": "^1.2.1", "@nuxtjs/toast": "^3.3.1", "@nuxtjs/vuetify": "^1.12.3", "@paypal/paypal-js": "^5.1.0", "@stripe/stripe-js": "^1.52.1", "@tinymce/tinymce-vue": "^3.2.8", "axios": "^0.21.4", "cookie-universal-nuxt": "^2.2.1", "core-js": "^3.15.1", "decimal.js": "^10.4.3", "echarts": "^5.3.3", "element-ui": "^2.15.9", "fabric": "^5.3.0", "html2canvas": "^1.4.1", "jspdf": "^2.5.2", "jszip": "^3.10.1", "nuxt": "^2.15.7", "qiankun": "^2.7.2", "qrcodejs2": "^0.0.2", "v-code-diff": "^1.12.1", "v-viewer": "^1.6.4", "vue": "^2.7.14", "vue-cropper": "^0.5.8", "vue-server-renderer": "^2.7.14", "vue-template-compiler": "^2.7.14", "vuedraggable": "^2.24.3", "vuetify-draggable-treeview": "^0.0.6"}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "babel-plugin-component": "^1.1.1", "babel-plugin-import": "^1.13.5", "cross-env": "^7.0.3", "sass": "^1.32.13", "sass-loader": "^10.2.0"}}