import { request } from '~/utils/request'

// 获取所有数据
export function getAllData(data) {
  return request({
    url: '/retailer/dashboard/all',
    method: 'get',
    params: data,
  })
}

// 筛选数据
export function filterData(data) {
  return request({
    url: '/retailer/dashboard/statistics',
    method: 'post',
    data: data,
  })
}

//获取步骤
export function getProSteps(data) {
	return request({
		url: '/retailer/proStep/getProSteps',
		method: 'post',
		data: data,
	})
}

//更新步骤
export function updateProSteps(data) {
	return request({
		url: '/retailer/proStep/updateProSteps',
		method: 'post',
		data: data,
	})
}

