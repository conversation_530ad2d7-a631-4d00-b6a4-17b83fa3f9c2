<!-- 霓虹灯快速报价 -->
<template>
  <article class="quick-neon-signs"
  	neonScrollBar
    v-loading="isLoading"
    id="quick-neon-signs">
    <section class="steps">
      <div class="step-box"
        v-for="item in generalData"
        :key="item.id"
        :class="{'step-active': item.paramName == currentStep }">

        <!-- Upload Or Design -->
        <section v-if="item.paramName == 'Your Text'"
          class="step upload-text">
          <TitlePine :title="lang.step + ' ' + item.priceInfo.fastQuoteStepIndex">
            <span v-if="tabName == 'Upload'"
              class="h">
              {{ lang.neon.uploadTitle }}</span>
            <span v-else
              class="h">{{ lang.Select + ' ' + item.alias }}</span>
          </TitlePine>

          <div class="step-content"
            v-show="tabName == 'Upload'">
            <div class="upload-inner">
              <NeonUpload ref="uploadCom"
                :uploadFileList="uploadFileList"
                @changeImgSrc="previewImgFromFiles"
                supportType="jpg,jpeg,gif,png,webp,svg,pdf,psd" />
            </div>
          </div>

          <div class="step-content"
            v-show="tabName != 'Upload'">
            <p>{{ lang.neon.enterYourText }}</p>
            <div class="design-input">
              <b class="icon-edit"
                v-show="!selectedData['Your Text'] && !spelling"></b>
              <el-input type="textarea"
                :autosize="{ minRows: 2, maxRows: 4 }"
                :placeholder="lang.neon.turnaroundTimeNoteTips"
                @compositionstart="spelling = true"
                @compositionend="spelling = false"
                @input="validateEmoji($event, 'design')"
                @change="changeFontOption"
                v-model="selectedData['Your Text']"
                resize="none">
              </el-input>
            </div>
          </div>
        </section>

        <!-- Select Color -->
        <section v-if="item.paramName == 'Select Color'"
          class="step select-color">
          <TitlePine :title="lang.step + ' ' + item.priceInfo.fastQuoteStepIndex">
            <span class="h">{{ (langObj.countryCode !== 'de' ? lang.Select:'') + ' ' + item.alias }}</span>
          </TitlePine>

          <div class="step-content">
            <div class="font-color-box">
              <div class="color-box">
                <el-popover popper-class="popo-row2"
                  placement="bottom"
                  ref="popover2"
                  :width="device == 'mb' ? '360':'485'"
                  @show="color_active = true"
                  @hide="color_active = false"
                  v-model="popover2Value"
                  trigger="click">
                  <SelectColor v-show="popover2Value"
                    v-model="selectedData['Select Color']"
                    @shutPopoverColor="popover2Value = false"
                    @change="changeFontOption"
                    :cols_num="device == 'pc' ? 7:4"
                    :color_list="item.childList" />
                  <div class="option-input color-input"
                    slot="reference"
                    :style="{'border-color':color_active ? 'var(--neon-light-color)':'#dbdbdb'}">
                    <span class="color-ball"
                      v-if="selectedData['Select Color']?.paramName != 'RGB Color' && !selectedData['Select Color']?.isNeonColorful"
                      :style="{ '--color':selectedData['Select Color']?.paramCode}"></span>
                    <span class="color-ball img-ball"
                      v-else
                      :style="{ backgroundImage: 'url('+filterImg(selectedData['Select Color'])?.url+')' }"></span>
                    <i class="el-icon-caret-top"
                      :class="{'icon-active':color_active}"></i>
                  </div>
                </el-popover>
              </div>
            </div>
            <div class="note-input">
              <b class="icon-edit"
                v-show="!colorTextLine && !spelling"></b>
              <el-input type="textarea"
                :placeholder="lang.neon.turnaroundTimeNoteTips"
                @compositionstart="spelling = true"
                @compositionend="spelling = false"
                @focus.self="clickNotes(item)"
                @input="validateEmoji($event, 'note')"
                v-model="colorTextLine"
                resize="none">
              </el-input>
            </div>
          </div>
        </section>

        <!-- Select Size -->
        <section v-if="item.paramName == 'Select Size'"
          class="step select-size">
          <TitlePine :title="lang.step + ' ' + item.priceInfo.fastQuoteStepIndex">
            <span class="h">{{ (langObj.countryCode !== 'de' ? lang.Select:'') + ' ' + item.alias }}</span>
          </TitlePine>

          <div class="step-content">
            <p class="upload-size-title">{{ lang.neon.customSize }}</p>
            <div class="size-input">
              <input v-if="!isCustomSizeInput"
                type="range"
                v-model="selectedData['Select Size'].width"
                @change="debounceLimitSize"
                name="customSize"
                :min="imgMinWidth"
                max="300"
                id="customSize"
                :style="{'--value': selectedData['Select Size'].width, '--min': imgMinWidth, '--max': 300 }">

              <el-input v-else
                ref="whatSize"
                size="mini"
                v-model="selectedData['Select Size'].width"
                type="number"
                @input="filterLeadingZero"
                @change="debounceLimitSize"
                :placeholder="lang.neon.Size || 'Size'">
              </el-input>
            </div>

            <span class="size-bottom">
              <span class="size-detail">
                <span v-show="selectedData['Select Size'].width">
                  <span>{{ computedArtwork }}</span> /
                  <span>{{ computedArtworkInfo }}</span>
                </span>
              </span>
              <span class="size-btn"
                @click="isCustomSizeInput = !isCustomSizeInput">
                {{ isCustomSizeInput ? lang.neon.back:lang.neon.enterSizes }}
              </span>
            </span>
          </div>
          <div class="sizeBottomTips">
			<p><i class="el-icon-warning-outline"></i>{{ lang.neon.minimumSize + '15" × NaN" / 38cm × NaNcm.'}}</p>
            <p>{{ lang.neon.neonCutTips }}</p>
          </div>
        </section>
      </div>
    </section>
    <section class="details">
      <div class="price">
        <span>{{ lang.subtotal }}:</span>
        <span class="cur-price">
          <CCY-rate :price="priceData.totalPrice?priceData.totalPrice:0"></CCY-rate>
        </span>
        <span class="raw-price">
          <CCY-rate :price="priceData.foundationUnitPrice?priceData.foundationUnitPrice:0"
            class="thrPrice"></CCY-rate>
        </span>
      </div>

      <div class="buttonGroup">
        <NeonButton v-if="!isInquiry"
          bg="linear-gradient(90deg, #48A2FF 0%, #ED12FB 99%)"
          @click="addToCart">
          {{ lang.fastQuote }}
        </NeonButton>
        <NeonButton v-else
          bg="linear-gradient(90deg, #06C0C1 0%, #23AADC 100%)"
          @click="addInquiry">
          {{ lang.neon.helpQuoteBtn }}
        </NeonButton>
      </div>
    </section>

    <InfoDialog :infoDialogVisible.sync="infoDialogVisible"
      :otherUpload="uploadFileList"
      v-model="customComment"
      :uploadList.sync="uploadList"
      noTitle
      @getValue="getValueFun">
      <template v-slot:custom>
        <el-form-item prop="subject"
          :label="lang.Comments">
          <el-input type="textarea"
            v-model="customComment"
            :placeholder="lang.instructions"></el-input>
        </el-form-item>
      </template>
    </InfoDialog>

    <!-- 预览图片弹窗 -->
    <el-dialog custom-class="dialogForUploadPreview"
      :visible.sync="preViewDialogImg"
      :lock-scroll="false"
      :width="device == 'mb' ? '80%':'30%'">
      <div class="previewPicWrap">
        <el-image :src="preViewImgSrc"
          fit="contain">
          <div slot="placeholder"
            class="image-slot">
            <span class="dot">...</span>
          </div>
        </el-image>
      </div>
    </el-dialog>

    <!-- 图片截取弹窗 -->
    <Crop :cropperDialog.sync="cropperDialog"
      :imageSrc="imageSrc.secure_url"
      @cropImage="cropImage"
      @cancelCrop="uploadFileList.pop()"
      width="50%">
      <div class="crop-tips">
        {{ lang.neon.neonCropTips }}
      </div>
    </Crop>

  </article>
</template>

<script>
import { Loading } from "element-ui";
import TitlePine from "@/components/Neon/TitlePine.vue";
import NeonUpload from "@/components/Neon/NeonUpload.vue";
import SelectColor from "@/components/Neon/SelectColor.vue";
import ChooseFont from "@/components/Neon/ChooseFont.vue";
import SampleImgCard from "@/components/Neon/SampleImgCard.vue";
import InfoDialog from "@/components/Medals/infoDialog.vue";
import Crop from "@/components/MyDzxt/Crop.vue";

import { medalsApi } from "@/api/medals/medals";
import { neonApi } from "@/api/neon/neon";
import { otoEditInquiry, otoAddCart, getInfo } from "@/api/pins";

import { debounce, dataURLtoFile, getImageSize, pdfToImg } from "@/utils/utils";

import { uploadFile } from "~/utils/oss";
import quotePrepare from "@/mixins/quotePrepare";
import { getIsSmallQty } from "@/assets/js/QuotePublic";

export default {
  head: {
    script: [
      {
        src: "https://cdn.jsdelivr.net/npm/pdfjs-dist@3.1.81/build/pdf.min.js",
        async: true,
        body: true,
      },
    ],
  },
  name: "QuickNeonSigns",
  components: {
    TitlePine,
    NeonUpload,
    SelectColor,
    ChooseFont,
    SampleImgCard,
    Crop,
    InfoDialog,
  },
  mixins: [quotePrepare],
  data() {
    return {
      generalData: [],
      tabName: "Upload",
      pid: 67,
      cateId: 176,
      platformProductId: "",
      imgMinWidth: 15,

      currentStep: null,

      uploadFileList: [],
      uploadList: [],

      selectedData: {},
      popover1Value: false,
      popover2Value: false,
      font_active: false,
      color_active: false,
      tube_active: false,
      spelling: false,

      colorTextLine: "",
      customComment: "",

      isCustomSizeInput: true,
      priceData: {},
      neonStyleData: {},

      imageSrc: "",
      artworkImg: {},
      // artworkInfo: {
      //   width: 15,
      //   height: 0,
      // },

      defTube: {
        url: "https://oss-static-cn.liyi.co/web/quoteManage/20231019/ffffff_2052taQmnT.png",
        alt: "White Tube",
        name: "darkWhite",
        color: "#ffffff",
      },
      // 灯皮
      tubeList: [],
      neonDiscountData: {},
      neonDiscount: null,

      preViewImgSrc: "",
      preViewDialogImg: false,
      cropperDialog: false,
      infoDialogVisible: false,

      isInquiry: false,
      loadingInstance: null,

      isLoading: true,
    };
  },
  computed: {
    lang() {
      return this.$store.getters.lang.quote || {};
    },
    device() {
      return this.$store.state.device;
    },
    computedArtwork() {
      return `${this.selectedData["Select Size"].width + '"'} × ${
        this.selectedData["Select Size"].height + '"'
      }`;
    },
    computedArtworkInfo() {
      return `${
        (Number(this.selectedData["Select Size"].width) / 0.3937).toFixed(0) +
        "cm"
      } × ${
        (Number(this.selectedData["Select Size"].height) / 0.3937).toFixed(0) +
        "cm"
      }`;
    },
    proId() {
      return this.$store.state.proId;
    },
    userUUID() {
      return this.$store.state.userUUID;
    },
    isLogin() {
      return this.$store.getters.isLogin;
    },
    userId() {
      return this.$store.state.userInfo?.id;
    },
    langObj() {
      return this.$store.state.language || {};
    },
  },
  watch: {
    "uploadFileList.length": {
      handler(newV, oldV) {
        // 添加上传文件
        if (newV > oldV && newV == 1) {
          this.imageSrc = this.uploadFileList[0];
          if (/\.(pdf)$/.test(this.imageSrc.original_filename.toLowerCase())) {
            let reader = new FileReader();
            reader.readAsDataURL(this.imageSrc.file);
            reader.onload = async (e) => {
              this.imageSrc.secure_url = await pdfToImg(e.target.result);
            };
          }
          if (/\.(psd)/.test(this.imageSrc.original_filename.toLowerCase())) {
            let reader = new FileReader();
            reader.readAsDataURL(this.imageSrc.file);
            reader.onload = async (e) => {
              let PSD = require("@/assets/js/psd");
              let psd = await PSD.fromURL(e.target.result);
              this.imageSrc.secure_url = psd.image.toBase64();
            };
          }
          this.cropperDialog = true;
        }
      },
    },
  },
  methods: {
    clickNotes() {},
    changeFontOption() {
      this.debounceCalcPrice();
    },

    getInfoByParamName(name) {
      return this.generalData.find((x) => x.paramName == name).childList;
    },

    getByPId() {
      medalsApi
        .getByPId({
          pid: this.pid,
        })
        .then(async (res) => {
          this.neonStyleData = res.data;
          this.cateId = this.neonStyleData[0].id;
          await this.getCateParamRelationByCateId();
          this.isLoading = false;
        });
    },

    // 获取霓虹灯快速报价参数
    async getCateParamRelationByCateId() {
      medalsApi
        .getCateParamRelationByCateId({ cateId: this.cateId, isFastQuote: 1 })
        .then((res) => {
          // 获取快速报价的参数数据
          this.generalData = res.data.sort(
            (a, b) =>
              a.priceInfo.fastQuoteStepIndex - b.priceInfo.fastQuoteStepIndex
          );

          const tempObj = {};

          // 默认颜色 和 字体
          this.generalData.forEach((x) => {
            if (x.paramName == "Select Color") {
              const def_color = x.childList.find(
                (y) => y.priceInfo.isFastQuoteSelected
              );
              tempObj[x.paramName] = def_color;
            } else if (x.paramName == "Select Turnaround Time") {
              const def_time = x.childList.find(
                (y) => y.priceInfo.isFastQuoteSelected
              );
              this.neonDiscountData = def_time;
              this.neonDiscount = def_time.priceInfo;
            } else if (x.paramName == "Select Size") {
              tempObj[x.paramName] = {
                width: 0,
                height: 0,
                str: "",
              };
            } else {
              tempObj[x.paramName] = null;
            }
          });

          this.selectedData = tempObj;
        });
    },

    // 获取报价参数
    async getQuoteParam() {
      return new Promise((resolve, reject) => {
        let copyGeneralData = JSON.parse(JSON.stringify(this.generalData));
        let copySelectedData = JSON.parse(JSON.stringify(this.selectedData));
        for (const key in copySelectedData) {
          copyGeneralData.forEach((item) => {
            if (item.paramName == "Your Text") {
              item.childList = [];
            } else if (item.paramName == "Select Color") {
              item.childList = [
                {
                  alias: copySelectedData[item.paramName].alias,
                  parentId: 1,
                },
              ];
            } else if (item.paramName == "Select Size") {
              item.childList = [
                {
                  alias: ` L (${(
                    (Number(copySelectedData[item.paramName].width) / 0.3937) *
                    this.unitConvertRatio
                  ).toFixed(0)})${this.unitSign} * H (${(
                    (Number(copySelectedData[item.paramName].height) / 0.3937) *
                    this.unitConvertRatio
                  ).toFixed(0)})${this.unitSign} `,
                  parentId: 1,
                },
              ];
            }
          });
        }

        const obj = {
          classificationData: this.neonStyleData[0],
          finaData: copyGeneralData,
          fontData: {
            fontImgCustom: [...this.uploadFileList, ...this.uploadList].map(
              (x) => x.secure_url
            ),
            comments: this.colorTextLine,
          },
        };
        resolve(obj);
      });
    },

    // 获取价格参数
    async getPriceParam() {
      return new Promise((resolve, reject) => {
        let paramIdList = [];

        for (const key in this.selectedData) {
          const item = this.selectedData[key];
          if (key == "Select Color") {
            paramIdList.push(item.priceInfo.id);
          }
        }

        let obj = {
          proId: this.proId,
          projectName: this.projectName,
          cateId: this.cateId,
          paramIdList: paramIdList,
          discountId: this.neonDiscount.id,
          isPicNeon: 1,
          quantity: 1,
        };

        const rectArr = [
          Number(this.selectedData["Select Size"].width),
          Number(this.selectedData["Select Size"].height),
        ].map((x) => x && Number(x.toFixed(0)));
        obj.neonCustomSize = {
          lengthInch: Math.max(...rectArr),
          widthInch: Math.min(...rectArr),
          isCustomSize: 1,
        };

        resolve(obj);
      });
    },

    async getValueFun({ subject, ...val }) {
      this.loadingInstance = Loading.service({
        lock: true,
        text: "Loading",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
        target: ".neon-box",
      });
      let quoteParam = await this.getQuoteParam();
      let priceParam = await this.getPriceParam();
      //询盘如果有期待时间，finaData手动添加数据
      if (val.expectTime) {
        quoteParam.finaData.push({
          parentId: 0,
          alias: "Expected Delivery Date",
          childList: [
            {
              parentId: 10000,
              alias: val.expectTime,
            },
          ],
        });
      }
      let data = {
        platformProductId: this.platformProductId,
        proId: this.proId,
        email: "",
        productsName: "Neon",
        quoteCateId: this.pid,
        quoteCateChildId: this.cateId,
        isMobile: this.device === "mb" ? 1 : 0,
        isPicNeon: 1,
        buyType: 7,
        quotePriceParam: JSON.stringify(priceParam),
        quoteParam: JSON.stringify(quoteParam),
        notes: "",
        ...val,
        subject: this.customComment,
        telephone: val.areaCode + "-" + val.telephone,
        isFastQuote: 1,
        isSmallQty: getIsSmallQty(
          this.neonDiscountData,
          this.$store.state.enableTurnaroundTimeCheck,
          this.priceData
        ),
      };
      this.otoEditInquiry(data);
    },

    // 计算价格
    async calculate() {
      let postData = await this.getPriceParam();
      neonApi.calculate(postData).then((res) => {
        this.priceData = res.data;
		this.isInquiry = !!res.data.onlyAddInquiry || !this.priceData.totalPrice;
      });
    },

    async addCart() {
      let quoteParam = await this.getQuoteParam();
      let priceParam = await this.getPriceParam();
      let data = {
        proId: this.proId,
        uuid: this.isLogin ? null : this.userUUID,
        userId: this.isLogin ? this.userId : null,
        buyType: 7,
        quoteCateId: this.pid,
        isPicNeon: 1,
        isMobile: this.device === "mb" ? 1 : 0,
        quoteCateChildId: this.cateId,
        quantity: 1,
        quotePriceParam: JSON.stringify(priceParam),
        quoteParam: JSON.stringify(quoteParam),
        isFastQuote: 1,
        isSmallQty: getIsSmallQty(
          this.neonDiscountData,
          this.$store.state.enableTurnaroundTimeCheck,
          this.priceData
        ),
      };

      otoAddCart(data, this.priceData).then((res) => {
        this.$toast.success(res.message);
        this.$nextTick(() => {
          this.loadingInstance.close();
        });
        this.$router.push({
          path: "/cart",
        });
      });
    },

    otoEditInquiry(data) {
      otoEditInquiry(data).then((res) => {
        this.loadingInstance.close();
        this.$confirm(this.lang.p22, this.lang.p21, {
          confirmButtonText: this.lang.Confirm,
          type: "success",
          showCancelButton: false,
          center: true,
        }).finally(() => {
          window.location.href = "/";
        });
      });
    },

    addToCart() {
      if (!this.checkSelectedForm()) return;
      this.loadingInstance = Loading.service({
        lock: true,
        text: "Loading",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
        target: ".neon-box",
      });
      setTimeout(() => {
        this.addCart();
      }, 50);
    },

    addInquiry() {
      if (!this.checkSelectedForm()) return;
      this.infoDialogVisible = true;
    },

    checkSelectedForm() {
      if (!this.uploadFileList.length) {
        this.$toast.error(this.lang.neon.noUploadFile);
        return false;
      }
      if (
        !this.selectedData["Select Size"].str ||
        !this.selectedData["Select Size"].width ||
        !this.selectedData["Select Size"].height
      ) {
        this.$toast.error(this.lang.neon.whatSizeYouLike);
        return false;
      }
      return true;
    },

    filterLeadingZero(val) {
      if (val && /^0\d+/.test(val)) {
        this.selectedData["Select Size"].width = val.replace(/^0+/, ""); // 替换掉前导零
      }
    },

    limitSizeInput() {
      if (this.selectedData["Select Size"].width < this.imgMinWidth)
        this.selectedData["Select Size"].width = this.imgMinWidth;
      else if (this.selectedData["Select Size"].width > 300)
        this.selectedData["Select Size"].width = 300;
      this.selectedData["Select Size"].height = (
        Number(this.selectedData["Select Size"].width) * this.artworkImg.ratio
      ).toFixed(0);
      this.selectedData["Select Size"].str =
        this.selectedData["Select Size"].width +
        this.unitSign +
        " / " +
        this.selectedData["Select Size"].height +
        this.unitSign;
      this.debounceCalcPrice();
    },

    // 裁剪完成
    async cropImage(data) {
      const changeImg = this.uploadFileList.find(
        (x) => x.original_filename == this.imageSrc.original_filename
      );
      // base64转url 先转文件 再上传图片
      const file = await dataURLtoFile(data, 1);
      const url = await uploadFile(file);
      changeImg.secure_url = url;
      const { width, height } = await getImageSize(data);
      changeImg.height = height;
      changeImg.width = width;
      changeImg.ratio = height / width;
      this.artworkImg = changeImg;
      this.debounceCalcPrice();
    },

    previewImgFromFiles(v) {
      this.preViewImgSrc = v;
      this.preViewDialogImg = true;
    },

    filterImg(val) {
      if (val.imageJson) {
        let arr = JSON.parse(val.imageJson);
        return arr.find((x) => {
          return x.name == "RGB Color";
        });
      }
    },

    validateEmoji(val, key) {
      if (key == "design") {
        this.selectedData["Your Text"] = val.replace(
          /[\uD83D\uD83C][\uDC00-\uDFFF]|\uD83D[\uDC00-\uDE4F]/g,
          ""
        );
      } else {
        this.colorTextLine = val.replace(
          /[\uD83D\uD83C][\uDC00-\uDFFF]|\uD83D[\uDC00-\uDE4F]/g,
          ""
        );
      }
    },
  },
  created() {
    getInfo({ id: this.pid }).then((res) => {
      this.platformProductId = res.data.platformProductId;
    });
  },
  mounted() {
    this.getByPId();
    this.debounceCalcPrice = debounce(this.calculate, 500);
    this.debounceLimitSize = debounce(this.limitSizeInput, 300);
  },
};
</script>
<style lang='scss' scoped>
.quick-neon-signs {
  --neon-light-color: #5d33fd;
  margin: 1em auto;
  display: flex;
  flex-direction: column;
  overflow-y: auto !important;
  .steps {
	flex: 1;
    padding: 0 1em;
    @media screen and (max-width: 1680px) {
      .step {
        padding-block: 0.5em !important;
      }
    }
    .step {
      padding-block: 1em;
      .step-content {
        margin-top: 1.15em;
        @media screen and (max-width: 1680px) {
          margin-top: 0.5em;
        }
      }

      &.upload-text {
        .upload-tabs {
          position: relative;
          width: fit-content;
          margin: 1em auto;
          display: grid;
          grid-template-columns: 1fr 1fr;
          column-gap: 1em;
          background: #f2f2f2;
          border-radius: calc(1em + 4px);
          overflow: hidden;
          &::before {
            content: "";
            position: absolute;
            left: 0;
            top: 0;
            width: 50%;
            height: 100%;
            z-index: 1;
            background: #5544ff;
            border-radius: calc(1em + 4px);
          }
          .item {
            color: #fff;
            padding: 0.5em 1.4em;
            text-align: center;
            cursor: pointer;
            z-index: 2;
            &.design {
              color: #333;
            }
          }

          &.is-deisgn {
            &::before {
              left: initial;
              right: 0;
            }
            .item {
              color: #333;
              &.design {
                color: #fff;
              }
            }
          }
        }
        .step-content {
          .upload-inner {
            .NeonUpload {
              .uploadList {
                ul {
                  min-height: 1.5vh;
                }
              }
              @media screen and(max-width: 768px) {
                background: #fafafa;
              }
            }
          }
          .design-input {
            position: relative;
            margin-top: 0.5em;
            b {
              display: block;
              top: 10px;
              font-size: 10px;
              left: 17px;
              position: absolute;
              color: #999999;
              z-index: 10;
            }

            .el-textarea {
              ::v-deep .el-textarea__inner {
                @media screen and(max-width: 768px) {
                  background: #fafafa;
                }
                &::placeholder {
                  text-indent: 1.3em;
                }
                &:focus {
                  border-color: var(--neon-light-color);
                }
              }
            }
          }
        }
      }

      &.select-color {
        .step-content {
          .font-color-box {
            display: flex;
            align-items: center;
            margin-bottom: 0.5em;
            .font-box {
              width: 8em;
            }
            .color-box {
              width: 6.25em;
              .option-input {
                height: 30px;
                border-radius: 6px;
                padding: 0 13px;
                color: #ccc;
                display: flex;
                justify-content: space-between;
                align-items: center;
                color: #000;
                border: 1px solid #dbdbdb;
                cursor: pointer;
                &.color-input {
                  padding: 0 6px;
                  @media screen and(max-width: 768px) {
                    background: #fafafa;
                  }
                }
                .color-ball {
                  height: 20px;
                  width: 20px;
                  border-radius: 3px;
                  object-fit: contain;
                  background-position: center;
                  background-color: var(--color);
                  &.img-ball {
                    width: 42px;
                    aspect-ratio: 48 / 14;
                    object-fit: contain;
                    background-size: cover;
                  }
                }
                i {
                  transition: transform 0.3s ease-in-out;
                  color: #282929;
                  &.icon-active {
                    transform: rotate(180deg);
                    transition: all 0.3s ease-in-out;
                  }
                }
              }
            }
          }
          .tube-box {
            width: 100%;
            .tube-input {
              width: 100%;
              display: flex;
              font-size: 14px;
              white-space: break-spaces;
              @media screen and(max-width:768px) {
                font-size: 12px;
              }
              span {
                cursor: pointer;
                color: #8d20fc;
                text-decoration: underline;
                i {
                  font-weight: 600;
                  transition: transform 0.3s ease-in-out;
                }
                &.icon-active {
                  i {
                    transform: rotate(180deg);
                    transition: all 0.3s ease-in-out;
                  }
                }
              }
            }
          }

          .note-input {
            position: relative;
            margin-top: 0.5em;
            b {
              display: block;
              top: 10px;
              font-size: 10px;
              left: 17px;
              position: absolute;
              color: #999999;
              z-index: 10;
            }

            .el-textarea {
              ::v-deep .el-textarea__inner {
                font-size: 1em;
                @media screen and (max-width: 1300px) {
                  font-size: calc(1em - 2px);
                  background: #fafafa;
                }
                &::placeholder {
                  text-indent: 1.3em;
                }
                &:focus {
                  border-color: var(--neon-light-color);
                }
              }
            }
          }
        }
      }

      &.select-size {
        .step-content {
          border-radius: 10px;
          padding: 1.5vh;
          border: 1px solid #dbdbdb;
          @media screen and(max-width: 768px) {
            background: #fafafa;
          }
          .size-input {
            .el-input {
              ::v-deep .el-input__inner {
                &:focus {
                  border-color: #5544ff;
                }
              }
            }
          }

          .size-bottom {
            display: flex;
            justify-content: space-between;
            font-size: calc(1em - 2px);
            margin-top: 0.5vh;
            .size-detail {
              color: #5544ff;
            }
            .size-btn {
              text-decoration: underline;
              cursor: pointer;
            }
          }
        }
        .sizeBottomTips {
          text-align: center;
          color: #ff0000;
          font-size: calc(1em - 2px);
          margin-top: 9px;
          @media screen and (max-width: 768px) {
            margin-bottom: 5.5px;
          }
        }
      }
    }
  }
  .details {
    padding-block: 1vh;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #ebebeb;
    padding-inline: 1em;
    .cur-price {
      font-size: 1.6em;
      color: #ff0000;
    }
    .raw-price {
      text-decoration: line-through;
    }
    .buttonGroup {
      display: flex;
      gap: 0.5em;
      .NeonButton {
        ::v-deep .button {
          padding-inline: 1em !important;
        }
      }
    }
  }
}
</style>

<style lang='scss'>
.toast-box {
  .toasted {
    width: fit-content !important;
    padding: 0 20px !important;
    margin: 0 auto !important;
  }
}
.cropDialog {
  .base-dialog-model-con {
    .crop-area {
      .cropper-btn {
        .el-button--primary {
          background: linear-gradient(
            90deg,
            rgb(74, 72, 255) 0%,
            rgb(176, 18, 251) 100%
          );
          border: none;
        }
      }
      .crop-tips {
        color: #ff0000;
        text-align: center;
      }
    }
  }
}
input[type="range"] {
  -webkit-appearance: none;
  width: 100%;
  height: 5px;
  background: #d3d3d3;
  outline: none;
  border-radius: 5px;
  position: relative;
  &::before {
    content: "";
    position: absolute;
    left: 0;
    width: calc(
      ((var(--value) - var(--min)) / (var(--max) - var(--min))) * 100%
    );
    height: 100%;
    background-color: #5544ff;
  }
}

input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 12px;
  height: 12px;
  background: #5544ff;
  cursor: pointer;
  border-radius: 50%;
}
</style>