<!-- 霓虹灯快速报价 -->
<template>
	<article class="quick-neon-signs" neonScrollBar v-loading="isLoading" id="quick-neon-signs" :style="{ maxHeight: this.device === 'mb' ? '' : '37vw'}">
		<section class="steps">
			<div class="step-box" v-for="item in generalData" :key="item.id">
				<!-- Upload Or Design -->
				<section v-if="item.paramName == 'Your Text'" class="step upload-text">
					<TitlePine :title="lang.step + ' ' + item.priceInfo.fastQuoteStepIndex">
						<span v-if="tabName == 'Upload'" class="h"> {{ lang.neon.uploadTitle }}</span>
						<span v-else class="h">{{ item.alias }}</span>
					</TitlePine>

					<div class="change-tabs">
						<div class="item" :class="{ 'is-active': tabName === 'Enter Your Text' }" @click="tabName = 'Enter Your Text'"><b class="icon-icon-text"></b> {{ lang.enterTextTab }}</div>
						<div class="item" :class="{ 'is-active': tabName === 'Upload' }" @click="tabName = 'Upload'"><b class="icon-icon-upload2"></b> {{ lang.uploadYourDesign }}</div>
					</div>

					<div class="step-content" v-show="tabName == 'Upload'">
						<div class="upload-inner">
							<NeonUpload ref="uploadCom" :uploadFileList="uploadFileList" @changeImgSrc="previewImgFromFiles" supportType="jpg,jpeg,gif,png,webp,svg,pdf,psd" />
						</div>
					</div>

					<div class="step-content" v-show="tabName != 'Upload'">
						<template v-for="(it, idx) in selectedFontData">
							<div class="fontOptionLine" :key="idx">
								<span>{{ lang.neon.enterYourText }}</span>
								<FontParam ref="fontParam" :offsetOptions="offsetOptions" v-model="selectedFontData[idx]" :textDesign="textDesign" :isText="true" :data_list="generalData" @setTextAlign="setTextAlign($event, idx)" @changeColor="changeColor" @changeFont="changeFont" @input="changeInput" @change="changeFontOption($event)" />
							</div>
						</template>
					</div>
				</section>

				<!-- Select Color -->
				<section v-if="item.paramName == 'Select Color' && tabName === 'Upload'" class="step select-color">
					<TitlePine :title="lang.step + ' ' + item.priceInfo.fastQuoteStepIndex">
						<span class="h">{{ (langObj.countryCode !== "de" ? lang.Select : "") + " " + item.alias }}</span>
					</TitlePine>

					<div class="step-content">
						<div class="font-color-box">
							<div class="color-box">
								<el-popover popper-class="popo-row2" placement="bottom" ref="popover2" :width="device == 'mb' ? '360' : '485'" @show="color_active = true" @hide="color_active = false" v-model="popover2Value" trigger="click">
									<SelectColor v-show="popover2Value" v-model="selectedData['Select Color']" @shutPopoverColor="popover2Value = false" @change="debounceCalcPrice" :cols_num="device == 'pc' ? 7 : 4" :color_list="item.childList" />
									<div class="option-input color-input" slot="reference" :style="{ 'border-color': color_active ? 'var(--neon-light-color)' : '#dbdbdb' }">
										<span class="color-ball" v-if="selectedData['Select Color']?.paramName != 'RGB Color' && !selectedData['Select Color']?.isNeonColorful" :style="{ '--color': selectedData['Select Color']?.paramCode }"></span>
										<span class="color-ball img-ball" v-else :style="{ backgroundImage: 'url(' + filterImg(selectedData['Select Color'])?.url + ')' }"></span>
										<i class="el-icon-caret-top" :class="{ 'icon-active': color_active }"></i>
									</div>
								</el-popover>
							</div>
						</div>
						<div class="note-input">
							<b class="icon-edit" v-show="!colorTextLine && !spelling"></b>
							<el-input type="textarea" :placeholder="lang.neon.turnaroundTimeNoteTips" @compositionstart="spelling = true" @compositionend="spelling = false" @input="validateEmoji($event, 'note')" v-model="colorTextLine" resize="none"> </el-input>
						</div>
					</div>
				</section>

				<!-- Select Size (Text)-->
				<section v-if="item.paramName == 'Select Size' && tabName === 'Enter Your Text'" class="step selectSize">
					<div class="title-line">
						<TitlePine :title="lang.step + ' ' + item.priceInfo.fastQuoteStepIndex" :tips="item.tips">
							<span>{{ lang.Select + " " + item.alias }}</span>
						</TitlePine>
					</div>
					<SelectSize ref="SelectSize" v-model="selectedData[item.paramName]" @change="clickFunForSize($event, item)" :size_list="item.childList" :customSize="customSize" :currentFontSize="currentFontSize" :tabName="tabName" :selectedFontData="selectedFontData" :zoneRatio="zoneRatio" :isText="true" :neonSampleData="neonSampleData" :neonDiscount="neonDiscount" :currentTemplate="currentTemplate" :unitConvertRatio="unitConvertRatio" :unitSign="unitSign" :sliderOptions="sliderOptions" @changeCustomSize="changeCustomSize($event)"></SelectSize>
				</section>

				<!-- What Size Would You Like? (Upload)-->
				<section v-if="item.paramName == 'What Size Would You Like?' && tabName === 'Upload'" class="step select-size">
					<TitlePine :title="lang.step + ' ' + item.priceInfo.fastQuoteStepIndex">
						<span class="h">{{ item.alias }}</span>
					</TitlePine>

					<div class="step-content">
						<p class="upload-size-title">{{ lang.neon.customSize }}</p>
						<div class="size-input">
							<input v-if="!isCustomSizeInput" type="range" v-model="selectedData['What Size Would You Like?'].width" @change="debounceLimitSize" name="customSize" :min="imgMinWidth" max="300" id="customSize" :style="{ '--value': selectedData['What Size Would You Like?'].width, '--min': imgMinWidth, '--max': 300 }" />

							<el-input v-else ref="whatSize" size="mini" v-model="selectedData['What Size Would You Like?'].width" type="number" @input="filterLeadingZero" @change="debounceLimitSize" :placeholder="lang.neon.Size || 'Size'"> </el-input>
						</div>

						<span class="size-bottom">
							<span class="size-detail">
								<span v-show="selectedData['What Size Would You Like?'].width">
									<span>{{ computedArtwork }}</span> /
									<span>{{ computedArtworkInfo }}</span>
								</span>
							</span>
							<span class="size-btn" @click="isCustomSizeInput = !isCustomSizeInput">
								{{ isCustomSizeInput ? lang.neon.back : lang.neon.enterSizes }}
							</span>
						</span>
					</div>
					<div class="sizeBottomTips">
						<p><i class="el-icon-warning-outline"></i>{{ lang.neon.minimumSize + '15" × NaN" / 38cm × NaNcm.' }}</p>
						<p>{{ lang.neon.neonCutTips }}</p>
					</div>
				</section>

				<!-- 室内室外 -->
				<section v-if="item.paramName == 'Indoor or Outdoor'" id="anchor-point" class="step indoororOutdoor">
					<div class="title-line">
						<TitlePine :title="lang.step + ' ' + (tabName == 'Upload' ? item.priceInfo.fastQuoteStepIndex : 3)" :tips="item.tips">
							<span>{{ formatStepTitle(item.paramName, item.alias) }}</span>
						</TitlePine>
					</div>
					<HasimgCard :columns="3" :columnsMb="2" :reverse="true" :iconTips="true" :bindName="item.paramName" :data_list="item.childList" v-model="selectedData[item.paramName]"> </HasimgCard>
				</section>

				<!-- 交期选择 -->
				<section v-if="item.paramName == 'Select Turnaround Time'" class="step turnaroundTime">
					<div class="title-line">
						<TitlePine :title="lang.step + ' ' + (tabName == 'Upload' ? item.priceInfo.fastQuoteStepIndex : 4)">
							<span>{{ formatStepTitle(item.paramName, item.alias) }}</span>
						</TitlePine>
					</div>
					<div style="color: rgb(135 132 132); font-size: calc(1em - 1px); margin-bottom: 15px">
						{{ lang.hourstext }}
					</div>
					<NeonDateBox v-model="selectedData[item.paramName]" @change="changeTurnaroundTime($event,item.paramName)" :data_list="item.childList" :priceInfo="priceData"></NeonDateBox>
				</section>
			</div>
		</section>
		<section class="details">
			<div class="price">
				<span>{{ lang.subtotal }}:</span>
				<span class="cur-price">
					<CCY-rate :price="priceData.totalPrice ? priceData.totalPrice : 0"></CCY-rate>
				</span>
				<span class="raw-price">
					<CCY-rate :price="priceData.foundationUnitPrice ? priceData.foundationUnitPrice : 0" class="thrPrice"></CCY-rate>
				</span>
			</div>

			<div class="buttonGroup">
				<NeonButton v-if="!isInquiry" bg="linear-gradient(90deg, #48A2FF 0%, #ED12FB 99%)" @click="addToCart">
					{{ lang.fastQuote }}
				</NeonButton>
				<NeonButton v-else bg="linear-gradient(90deg, #06C0C1 0%, #23AADC 100%)" @click="addInquiry">
					{{ lang.neon.helpQuoteBtn }}
				</NeonButton>
			</div>
		</section>

		<TextDraggableCanvas v-show="false" ref="textDraggableCanvas" @getSize="getSize" :currentFontSize="currentFontSize" :priceData="priceData" :tabName="tabName" :currentTextareaSize="currentTextareaSize" :selectedFontData="selectedFontData" :currentSampleData="currentSampleData" :neonSampleData="neonSampleData" :needCanvas="true" :hiddenPrice="false" :isText="true" :textDesign="textDesign" :zoneRatio.sync="zoneRatio" :neonDiscount="neonDiscount" :unitConvertRatio="unitConvertRatio" :unitSign="unitSign" :customSizeFlag="customSizeFlag" :uploadArtwork="artworkImg" :artworkInfo="artworkInfo" @getImgUrl="(v) => (canvasImgUrl = v)" @getFinaSize="getFinaSize"> </TextDraggableCanvas>

		<InfoDialog :infoDialogVisible.sync="infoDialogVisible" :otherUpload="uploadFileList" v-model="customComment" :uploadList.sync="uploadList" noTitle @getValue="getValueFun">
			<template v-slot:custom>
				<el-form-item prop="subject" :label="lang.Comments">
					<el-input type="textarea" v-model="customComment" :placeholder="lang.instructions"></el-input>
				</el-form-item>
			</template>
		</InfoDialog>

		<!-- 预览图片弹窗 -->
		<el-dialog custom-class="dialogForUploadPreview" :visible.sync="preViewDialogImg" :lock-scroll="false" :width="device == 'mb' ? '80%' : '30%'">
			<div class="previewPicWrap">
				<el-image :src="preViewImgSrc" fit="contain">
					<div slot="placeholder" class="image-slot">
						<span class="dot">...</span>
					</div>
				</el-image>
			</div>
		</el-dialog>

		<!-- 图片截取弹窗 -->
		<Crop :cropperDialog.sync="cropperDialog" :imageSrc="imageSrc.secure_url" @cropImage="cropImage" @cancelCrop="uploadFileList.pop()" width="50%">
			<div class="crop-tips">
				{{ lang.neon.neonCropTips }}
			</div>
		</Crop>
	</article>
</template>

<script>
import { Loading } from "element-ui";
import TitlePine from "@/components/Neon/TitlePine.vue";
import NeonUpload from "@/components/Neon/NeonUpload.vue";
import SelectColor from "@/components/Neon/SelectColor.vue";
import SelectSize from "@/components/Neon/SelectSize.vue";
import ChooseFont from "@/components/Neon/ChooseFont.vue";
import SampleImgCard from "@/components/Neon/SampleImgCard.vue";
import InfoDialog from "@/components/Medals/infoDialog.vue";
import FontParam from "@/components/Neon/FontParam.vue";
import HasimgCard from "@/components/Neon/HasimgCard.vue";
import NeonDateBox from "@/components/Neon/NeonDateBox";
import TextDraggableCanvas from "@/components/Neon/TextDraggableCanvas";

import Crop from "@/components/MyDzxt/Crop.vue";

import { medalsApi } from "@/api/medals/medals";
import { neonApi } from "@/api/neon/neon";
import { otoEditInquiry, otoAddCart, getInfo, getPriceData } from "@/api/pins";

import { debounce, dataURLtoFile, getImageSize, pdfToImg, urlAddVersion } from "@/utils/utils";

import { uploadFile } from "~/utils/oss";
import quotePrepare from "@/mixins/quotePrepare";
import { getIsSmallQty } from "@/assets/js/QuotePublic";

export default {
	head: {
		script: [
			{
				src: "https://cdn.jsdelivr.net/npm/pdfjs-dist@3.1.81/build/pdf.min.js",
				async: true,
				body: true,
			},
		],
	},
	name: "QuickNeonSigns",
	components: {
		TitlePine,
		NeonUpload,
		SelectColor,
		ChooseFont,
		SampleImgCard,
		Crop,
		InfoDialog,
		FontParam,
		NeonDateBox,
		HasimgCard,
		SelectSize,
		TextDraggableCanvas,
	},
	mixins: [quotePrepare],
	data() {
		return {
			offsetOptions: {
				font: 200,
				color: 100,
			},
			currentSampleData: null,
			customSizeFlag: false,
			textDesign: true,
			currentTextareaSize: {
				width: 0,
				height: 0,
			},
			canNotParamComposeList: [],
			noChoiceData: [],
			zoneRatio: null,
			sliderOptions: {
				min: 0,
				max: 150,
				part: 5,
				mesh: 5,
			},
			finaSize: {
				width: 0,
				height: 0,
			},
			// 可以做的尺寸列表（包含了尺寸信息) 用于定制尺寸需要的参数
			ableSizeListDetail: [],
			generalData: [],
			tabName: "Enter Your Text",
			selectedFontData: [],
			currentTemplate: null,
			currentFontSize: null,
			neonSampleData: {},
			pid: 67,
			cateId: 176,
			platformProductId: "",
			imgMinWidth: 15,
			customSize: 0,

			uploadFileList: [],
			uploadList: [],

			selectedData: {},
			popover1Value: false,
			popover2Value: false,
			font_active: false,
			color_active: false,
			tube_active: false,
			spelling: false,

			colorTextLine: "",
			customComment: "",

			isCustomSizeInput: true,
			priceData: {},
			neonStyleData: {},

			imageSrc: "",
			artworkImg: {},
			artworkInfo: {
				width: 0,
				height: 0,
			},

			defTube: {
				url: "https://oss-static-cn.liyi.co/web/quoteManage/20231019/ffffff_2052taQmnT.png",
				alt: "White Tube",
				name: "darkWhite",
				color: "#ffffff",
			},
			// 灯皮
			tubeList: [],
			neonDiscountData: {},
			neonDiscount: null,

			preViewImgSrc: "",
			preViewDialogImg: false,
			cropperDialog: false,
			infoDialogVisible: false,

			isInquiry: false,
			loadingInstance: null,

			isLoading: true,
			contentLoadingInstance: null,
		};
	},
	computed: {
		hasChinese() {
			const rule = /[\u4E00-\u9FFF\u3040-\u30FF\u31F0-\u31FF\uFF00-\uFFEF\uAC00-\uD7AF\u1100-\u11FF\u3130-\u318F\uA960-\uA97F\uD7B0-\uD7FF]/;
			return this.selectedFontData.some((x) => rule.test(x["Your Text"]));
		},
		lang() {
			return this.$store.getters.lang.quote || {};
		},
		device() {
			return this.$store.state.device;
		},
		computedArtwork() {
			return `${this.selectedData["What Size Would You Like?"].width + '"'} × ${this.selectedData["What Size Would You Like?"].height + '"'}`;
		},
		computedArtworkInfo() {
			return `${(Number(this.selectedData["What Size Would You Like?"].width) / 0.3937).toFixed(0) + "cm"} × ${(Number(this.selectedData["What Size Would You Like?"].height) / 0.3937).toFixed(0) + "cm"}`;
		},
		proId() {
			return this.$store.state.proId;
		},
		userUUID() {
			return this.$store.state.userUUID;
		},
		isLogin() {
			return this.$store.getters.isLogin;
		},
		userId() {
			return this.$store.state.userInfo?.id;
		},
		langObj() {
			return this.$store.state.language || {};
		},
	},
	watch: {
		"uploadFileList.length": {
			handler(newV, oldV) {
				// 添加上传文件
				if (newV > oldV && newV == 1) {
					this.imageSrc = this.uploadFileList[0];
					if (/\.(pdf)$/.test(this.imageSrc.original_filename.toLowerCase())) {
						let reader = new FileReader();
						reader.readAsDataURL(this.imageSrc.file);
						reader.onload = async (e) => {
							this.imageSrc.secure_url = await pdfToImg(e.target.result);
						};
					}
					if (/\.(psd)/.test(this.imageSrc.original_filename.toLowerCase())) {
						let reader = new FileReader();
						reader.readAsDataURL(this.imageSrc.file);
						reader.onload = async (e) => {
							let PSD = require("@/assets/js/psd");
							let psd = await PSD.fromURL(e.target.result);
							this.imageSrc.secure_url = psd.image.toBase64();
						};
					}
					this.cropperDialog = true;
				}
			},
		},
		noChoiceData: {
			handler(newValue, oldValue) {
				this.generalData.forEach((x) => {
					x.childList = x.childList.map((y) => {
						y.isHidden = 0;
						newValue.forEach((z) => {
							if (y.priceInfo.id == z) {
								if (y.paramName == this.selectedData[x.paramName]?.paramName) {
									this.selectedData[x.paramName] = null;
								}
								y.isHidden = 1;
							}
						});
						return y;
					});
				});
			},
			immediate: true,
		},
		hasChinese: {
			handler(newValue) {
				this.selectedData["Select Size"] = null;
			},
			immediate: true,
		},
		tabName: {
			handler() {
				this.contentLoadingInstance = Loading.service({
					lock: true,
					target: ".steps",
					customClass: 'custom-loading'
				});
				this.debounceCalcPrice();
			},
		},
	},
	methods: {
		changeTurnaroundTime(val) {
			this.neonDiscountData = val;
			this.neonDiscount = val.priceInfo;
			this.clickFun();
		},
		changeCustomSize(val) {
			this.customSize = val;
			const index = this.textCheckKey("Select Color", "isNeonColorful");

			setTimeout(() => {
				if (!this.isImgTemplate && this.sizeOverSixNine("inch") && index > -1) {
					this.selectedData["Your Text"][index]["Select Color"] = null;
				}
				const overFlag = this.sizeOverSixNine("cm");
				if (overFlag && this.selectedData["Hanging Options"]?.paramName === "3M Command Strips") {
					this.selectedData["Hanging Options"] = null;
				}
			}, 400);
			this.$refs.textDraggableCanvas.changeCustomSize(val);
			// this.selectedData["What Size Would You Like?"] = val;
			console.log('输入')
			this.debounceCalcPrice();
		},
		getAll() {
			neonApi.getAll({ cateId: 176 }).then(async (res) => {
				this.canNotParamComposeList = res.data;
			});
		},
		//不可选参数过滤
		filterStepsFun() {
			return new Promise((resolve, reject) => {
				let tempArr = [],
					result = [];
				tempArr.push(this.currentTemplate);
				for (let key in this.selectedData) {
					if (this.selectedData[key] && this.selectedData[key] instanceof Array) tempArr = [...tempArr, ...this.selectedData[key]];
					else if (this.selectedData[key]) tempArr.push(this.selectedData[key]);
				}
				this.canNotParamComposeList.map((x) => {
					let cArr = x.canNotParamCompose.split(","),
						bArr = cArr;
					for (let i = 0; i < cArr.length; i++) {
						if (bArr.length == 1) {
							result = result.concat(bArr);
							break;
						} else {
							tempArr.map((z) => {
								if (z instanceof Array) {
									z.forEach((zItem) => {
										for (let i in zItem) {
											if (i == "Select Color" || i == "Select Font") {
												bArr = bArr.filter((y) => {
													return zItem[i]?.priceInfo.id != y;
												});
											}
										}
									});
								} else {
									bArr = bArr.filter((y) => {
										return z.priceInfo?.id != y;
									});
								}
							});
						}
					}
				});
				resolve(result);
			});
		},
		clickFun() {
			this.filterStepsFun().then((res) => {
				this.noChoiceData = res;
				this.debounceCalcPrice();
			});
		},
		clickFunForSize(val, item) {
			this.ableSizeListDetail = this.$refs.SelectSize[0].getAllAbleSizeDetail();
			if (val.paramName == "Custom Size") {
				this.customSizeFlag = true;
				if (this.unitSign == "cm") {
					this.sliderOptions.min = Number((this.getMinSize().width * 0.3937).toFixed(0));
				} else {
					this.sliderOptions.min = this.getMinSize().width;
				}
				return;
			} else {
				this.customSize = 0;
				this.customSizeFlag = false;
			}
			this.currentFontSize = val;
			this.neonSampleData = JSON.parse(this.currentTemplate.neonSampleData).find((x) => {
				return x.size == this.currentFontSize.paramName;
			});

			if (!this.isImgTemplate) {
				try {
					this.neonSampleData.fontPosttion.forEach((item, index) => {
						item.fontFamily = this.selectedData["Your Text"][index]["Select Font"].paramName;
						item.color = this.selectedData["Your Text"][index]["Select Color"].paramCode;
					});
				} catch (error) {
					console.log(error);
				}
			}

			if (this.currentSampleData) {
				let temp = this.neonSampleData.color.find((x) => {
					return x.name == this.currentSampleData.name;
				});
				this.currentSampleData.img = temp?.img;
			}

			this.clickFun(item);
		},
		getSize(size) {
			this.currentTextareaSize = size;
		},
		getFinaSize(val) {
			this.finaSize.width = val.finaWidth;
			this.finaSize.height = val.finaHeight;
		},
		formatStepTitle(name, alias) {
			const noSelect = ["Indoor or Outdoor"];
			let titleStr = "";
			titleStr = this.lang.Select + " " + alias;
			if (noSelect.includes(name) && this.langObj.countryCode == "de") {
				titleStr = alias;
			}
			return titleStr;
		},
		getMinSize() {
			const dom = document.querySelector(".select-size");
			if (!dom) return { width: 0, height: 0 };
			const cardOuters = Array.from(dom.children);
			const minSizeDom = cardOuters.find((x) => {
				return x.style["0"] != "display" && !Array.from(x.classList).includes("custom-size") && !Array.from(x.classList).includes("tips");
			});
			const sizeSplit = minSizeDom.children[0].children[1].innerText.split(" ");

			return {
				width: parseFloat(sizeSplit[1]),
				height: parseFloat(sizeSplit[4]),
			};
		},
		getInfo(name) {
			return this.generalData.find((x) => {
				return x.paramName == name;
			});
		},
		// 正确计算字符数量（文字间的多空格算一个字符，文字间的回车，有一个算一个）
		countCharacterNums(obj) {
			let count = 0;
			let text = (obj["Your Text"] || obj.placeholder).trim();
			let strSplit = text.split("\n");
			for (let j = 0; j < strSplit.length; j++) {
				let strSplitUnit = strSplit[j].trim().replace(/[ \t\v]+/g, " ");
				if (strSplitUnit.length != 0) count += strSplitUnit.length;
			}
			count += strSplit.length - 1;
			return count;
		},
		//计算尺寸价格
		getNeonFontSizePrice() {
			console.log("获取尺寸价格");
			let temp = [];
			if (this.isImgTemplate) {
				temp = null;
			} else {
				this.selectedFontData.forEach((item) => {
					temp.push({
						neonFontId: item["Select Font"]?.priceInfo.id,
						charQuantity: this.countCharacterNums(item),
					});
				});
			}
			let obj = {
				cateId: this.cateId,
				neonSampleId: this.currentTemplate.priceInfo.id,
				neonTemplateQuantityList: temp,
			};

			obj.neonSizeList = this.ableSizeListDetail.map((x) => {
				const rectArr = Object.values(x.rectInfo);
				return {
					neonLeftSizeId: x.priceInfo.id,
					lengthInch: Math.max(...rectArr),
					widthInch: Math.min(...rectArr),
				};
			});

			neonApi.getNeonFontSizePrice(obj).then(async (res) => {
				this.fontSizePriceData = res.data;
				let target = this.getInfo("Select Size");
				target.childList = target.childList.map((x) => {
					this.fontSizePriceData.forEach((y) => {
						if (x.priceInfo.id == y.sizeParamPriceId) {
							x.unitPrice = y.unitPrice;
						}
					});
					return x;
				});
			});
		},
		// 判断尺寸有没有超过 60 * 90 或者最长的大于90 最小的大于23
		sizeOverSixNine(type = "inch") {
			const width = Number(this.finaSize.width);
			const height = Number(this.finaSize.height);
			if (type == "cm") {
				return width * height > 23 * 35 || Math.max(width, height) > 35 || Math.min(width, height) > 23;
			} else {
				return width * height > 47 * 94 || Math.max(width, height) > 94 || Math.min(width, height) > 47;
			}
		},
		// 改变文字
		changeInput(val) {
			setTimeout(() => {
				if (this.sizeOverSixNine("inch")) {
					this.canvasTipsShow = true;
				} else {
					this.canvasTipsShow = false;
				}
			}, 400);

			this.$nextTick(() => {
				if (this.unitSign == "cm") {
					this.sliderOptions.min = Number((this.getMinSize().width * 0.3937).toFixed(0));
				} else {
					this.sliderOptions.min = this.getMinSize().width;
				}
				if (this.customSizeFlag) {
					this.customSize = 0;
					this.$refs.SelectSize[0].resetProgress();
					this.$refs.textDraggableCanvas.changeCustomSize(this.customSize);
					// this.$refs.SelectSize[0].$refs.neonProgressSelect[0].clickWrapper({
					//   offsetX: 0,
					// });
				}
				this.ableSizeListDetail = this.$refs?.SelectSize[0].getAllAbleSizeDetail();
			});
		},
		// 当前文字编辑参数类型
		changeColor(val) {
			const specialColor = ["Gradient Changing Color", "RGB Color"];
			const hasIndex = specialColor.indexOf(val.paramName);
			if (hasIndex > -1) {
				this.specialColorFlag = true;
				this.specialColorCur = val.paramName;
				this.selectedFontData.forEach((x) => {
					x["Select Color"] = val;
				});
				if (hasIndex == 0) {
					if (this.finaSize.width > 94 || this.finaSize.height > 47) this.selectedData["Select Size"] = null;
				}
			} else {
				// 将其他所有选择了特殊颜色的改成普通颜色
				const index = this.selectedFontData.findIndex((x) => {
					return specialColor.includes(x["Select Color"]?.paramName);
				});
				if (index > -1) {
					this.specialColorFlag = false;
					this.selectedFontData.forEach((x) => (x["Select Color"] = val));
				}
				this.specialColorCur = null;
			}
		},
		// 改变字体
		changeFont(val) {
			setTimeout(() => {
				this.currentFontSize = this.ableSizeListDetail[0];
				this.selectedData["Select Size"] = null;
				const arr = JSON.parse(val.neonSampleData);
				this.neonSampleData = arr.find((x) => {
					return x.size == largeSizeItem.paramName;
				});
			}, 0);
		},
		// 文字参数发生改变
		changeFontOption(val, item) {
			console.log("changeFontOption");
			// 如果选了炫彩色 而且尺寸已经大于47了 取消Select Size选中
			if (this.selectedFontData[0]?.["Select Color"]?.paramName == "Gradient Changing Color" && (this.finaSize.width > 94 || this.finaSize.height > 47)) {
				this.selectedData["Select Size"] = null;
			}

			this.selectedData["Your Text"] = this.selectedFontData;
			this.filterStepsFun().then((res) => {
				this.noChoiceData = res;
			});

			setTimeout(() => {
				this.ableSizeListDetail = this.$refs?.SelectSize[0].getAllAbleSizeDetail();
				if (this.textCheckKey("Select Color", "isNeonColorful") > -1) {
					this.sliderOptions.max = this.ableSizeListDetail[this.ableSizeListDetail.length - 1].rectInfo?.width;
					this.canvasTipsShow = false;
				} else {
					this.sliderOptions.max = 150;
				}
				this.debounceFontSizePrice();
				this.debounceCalcPrice();
			}, 500);
		},
		textCheckKey(pKey, key) {
			return this.selectedFontData.findIndex((x) => {
				return x[pKey]?.[key];
			});
		},
		setTextAlign(val, idx) {
			this.selectedFontData[idx].textAlign = val;
		},
		getInfoByParamName(name) {
			return this.generalData.find((x) => x.paramName == name).childList;
		},

		getByPId() {
			medalsApi
				.getByPId({
					pid: this.pid,
				})
				.then(async (res) => {
					this.neonStyleData = res.data;
					this.cateId = this.neonStyleData[0].id;
					await this.getCateParamRelationByCateId();
					this.isLoading = false;
				});
		},

		// 获取霓虹灯快速报价参数
		async getCateParamRelationByCateId() {
			medalsApi.getCateParamRelationByCateId({ cateId: this.cateId, isFastQuote: 1 }).then((res) => {
				// 获取快速报价的参数数据
				this.generalData = res.data.sort((a, b) => a.priceInfo.fastQuoteStepIndex - b.priceInfo.fastQuoteStepIndex);
				this.selectedFontData = [];
				const selectedFontObj = {
					tubeColor: {
						alt: this.lang.neon.defTubeAlt,
						color: "#ffffff",
						name: "darkWhite",
						url: "https://oss-static-cn.liyi.co/web/quoteManage/20231019/ffffff_2052taQmnT.png",
					},
					textAlign: "left",
					"Your Text": "HI",
					placeholder: "HI",
					textCount: 9999,
				};
				const tempObj = {};

				// 默认参数
				this.generalData.forEach((x) => {
					const def_param = x.childList.find((y) => y.priceInfo.isFastQuoteSelected);
					tempObj[x.paramName] = def_param || x.childList[0];

					if (x.paramName == "Select Turnaround Time") {
						this.neonDiscountData = def_param;
						this.neonDiscount = def_param.priceInfo;
					}
					if (x.paramName === "Select Size") {
						this.currentFontSize = def_param;
					}
					if (x.paramName === "Designs Or Upload Your Artwork") {
						this.currentTemplate = def_param;
					}
					if (x.paramName == "What Size Would You Like?") {
						tempObj[x.paramName] = {
							width: 0,
							height: 0,
							str: "",
						};
					}
					if (x.paramName == "Select Color") {
						selectedFontObj["Select Color"] = def_param;
					}
					if (x.paramName == "Select Font") {
						selectedFontObj["Select Font"] = def_param;
					}
				});

				this.neonDiscount = this.neonDiscountData.priceInfo;
				this.neonSampleData = JSON.parse(this.currentTemplate.neonSampleData).find((x) => x.size == this.currentFontSize.paramName);
				this.selectedFontData = [selectedFontObj];
				this.selectedData = tempObj;
				this.selectedData["Your Text"] = this.selectedFontData;
				this.$nextTick(() => {
					this.ableSizeListDetail = this.$refs.SelectSize[0].getAllAbleSizeDetail();
					this.debounceFontSizePrice();
					this.debounceCalcPrice();
				});
			});
		},

		loadImage(url) {
			return new Promise((resolve, reject) => {
				let img = new Image();
				img.onload = () => resolve(img);
				img.onerror = reject;
				img.src = urlAddVersion(url);
				img.crossOrigin = "Anonymous";
			});
		},

		uploadCanvas(val) {
			return new Promise((resolve, reject) => {
				let file = dataURLtoFile(val, 1);
				uploadFile(file).then(async (url) => {
					let img = await this.loadImage(url);
					resolve(img);
				});
			});
		},

		// 获取报价参数
		getQuoteParam() {
			return new Promise(async (resolve, reject) => {
				let copyGeneralData = JSON.parse(JSON.stringify(this.generalData));
				let copySelectedData = JSON.parse(JSON.stringify(this.selectedData));
				const keyMap = {
					"Enter Your Text": ["Select Size", "Indoor or Outdoor", "Select Turnaround Time"],
					Upload: ["What Size Would You Like?", "Select Color", "Indoor or Outdoor", "Select Turnaround Time"],
				};

				for (const key in copySelectedData) {
					copyGeneralData.forEach((item) => {
						if (keyMap[this.tabName].includes(item.paramName)) {
							if (item.paramName === "What Size Would You Like?") {
								item.childList = [
									{
										alias: ` L (${((Number(copySelectedData[item.paramName].width) / 0.3937) * this.unitConvertRatio).toFixed(0)})${this.unitSign} * H (${((Number(copySelectedData[item.paramName].height) / 0.3937) * this.unitConvertRatio).toFixed(0)})${this.unitSign} `,
										parentId: 1,
									},
								];
							} else if (item.paramName === "Select Size") {
								item.childList = [
									{
										alias: copySelectedData[item.paramName].alias + ` L (${((Number(this.finaSize.width) / 0.3937) * this.unitConvertRatio).toFixed(0)})${this.unitSign} * H (${((Number(this.finaSize.height) / 0.3937) * this.unitConvertRatio).toFixed(0)})${this.unitSign} `,
										parentId: 1,
									},
								];
							} else {
								item.childList = [
									{
										alias: copySelectedData[item.paramName].alias,
										parentId: 1,
									},
								];
							}
						} else {
							item.childList = [];
						}
					});
				}

				let fontData = [];
				if (this.tabName === 'Enter Your Text' && copySelectedData["Your Text"] && copySelectedData["Your Text"].length > 0) {
					const copySelectedFontData = copySelectedData["Your Text"];
					// 删除原来的避免重复展示
					const idx = copyGeneralData.findIndex((x) => {
						return x.paramName == "Your Text";
					});
					if (idx > -1) copyGeneralData.splice(idx, 1);
					const sortCopySelectedData = copySelectedFontData.map((item) => {
						return {
							"Your Text": item["Your Text"] || item.placeholder,
							"Select Font": item["Select Font"],
							"Select Color": item["Select Color"],
							tubeColor: item["tubeColor"],
						};
					});
					const def = copyGeneralData[0];
					const defC = sortCopySelectedData[0]["Select Font"];
					sortCopySelectedData.forEach((obj, index) => {
						for (const key in obj) {
							const val = obj[key];
							if (key == "Your Text") {
								fontData.push({
									...def,
									alias: `${this.lang.neon.detailTextLine} ${index + 1}`,
									childList:
										this.tabName == "Enter Your Text"
											? [
													{
														...defC,
														alias: val.replace(/[\r\n]/g, " "),
													},
											  ]
											: [],
								});
							}
							if (key == "Select Font") {
								fontData.push({
									...def,
									alias: `${this.lang.neon.detailLine} ${index + 1} ${this.lang.neon.detailFontColor}`,
									childList: [
										{
											...defC,
											parentId: 1,
											alias: val?.alias ? `${val?.alias};` : "",
										},
									],
								});
							}
							if (key == "Select Color") {
								let reaIndex = index <= 0 ? 1 : index * 2 + 1;
								fontData[reaIndex].childList[0].alias = fontData[reaIndex].childList[0].alias + (val?.alias ? `${val?.alias};` : "");
							}
							if (key == "tubeColor") {
								let reaIndex = index <= 0 ? 1 : index * 2 + 1;
								fontData[reaIndex].childList[0].alias = fontData[reaIndex].childList[0].alias + (val?.alt ? `${val?.alt}` : "");
							}
						}
					});
					copyGeneralData = fontData.concat(copyGeneralData);
				}


				const obj = {
					classificationData: this.neonStyleData[0],
					finaData: copyGeneralData,
					fontData: {
						fontImgCustom: [...this.uploadFileList, ...this.uploadList].map((x) => x.secure_url),
						comments: this.colorTextLine,
					},
				};

				if (this.tabName == "Enter Your Text") {
					let canvasImg = document.querySelector(".canvasEle").getAttribute("data-src");
					let temp = await this.uploadCanvas(canvasImg);
					let img = temp.getAttribute("src");
					obj.canvasData = {};
					obj.fontData.dzImage = img;
					obj.canvasData.url = img;
				}
				resolve(obj);
			});
		},

		// 获取价格参数
		async getPriceParam() {
			return new Promise((resolve, reject) => {
				let paramIdList = [];

				for (const key in this.selectedData) {
					const item = this.selectedData[key];
					if (key == "Select Color") {
						paramIdList.push(item.priceInfo.id);
					} else if (key == "Select Size") {
						paramIdList.push(item.priceInfo.id);
					}
				}

				let obj = {
					proId: this.proId,
					projectName: this.projectName,
					cateId: this.cateId,
					paramIdList,
					discountId: this.neonDiscount.id,
					isPicNeon: this.tabName === "Upload" ? 1 : 0,
					neonSampleId: this.currentTemplate.priceInfo.id,
					quantity: 1,
				};
				let rectArr = [];
				if (this.tabName === "Enter Your Text") {
					obj.neonTemplateQuantityList = this.selectedData["Your Text"].map((item) => {
						return {
							neonFontId: item["Select Font"]?.priceInfo.id,
							charQuantity: this.countCharacterNums(item),
							neonColorId: item["Select Color"]?.priceInfo.id,
						};
					});
					obj.neonFontId = this.selectedData["Select Font"]?.priceInfo.id;
					obj.neonSizeId = this.selectedData["Select Size"]?.priceInfo.id || this.currentFontSize?.priceInfo.id;
					rectArr = [Number(this.finaSize.width), Number(this.finaSize.height)].map((x) => x && Number(x.toFixed(0)));
				} else {
					rectArr = [Number(this.selectedData["What Size Would You Like?"].width), Number(this.selectedData["What Size Would You Like?"].height)].map((x) => x && Number(x.toFixed(0)));
				}
				obj.neonCustomSize = {
					lengthInch: Math.max(...rectArr),
					widthInch: Math.min(...rectArr),
					isCustomSize: Number(this.customSizeFlag),
				};

				if (this.customSizeFlag) {
					const range = this.findSizeRange(
						this.ableSizeListDetail,
						this.customSize
					);
					obj.neonCustomSize.neonLeftSizeId = range.lower?.priceInfo.id;
					obj.neonCustomSize.neonRightSizeId = range.upper?.priceInfo.id;
					obj.neonCustomSize.leftLength = Math.max(
						range.lower?.rectInfo.width,
						range.lower?.rectInfo.height
					);
					obj.neonCustomSize.rightLength = Math.max(
						range.upper?.rectInfo.width,
						range.upper?.rectInfo.height
					);
				}

				resolve(obj);
			});
		},

		findSizeRange(arr, size) {
			// 返回第一个大于 size 的上界
			let upperIndex = arr.findIndex((x) => x.rectInfo.width > size);
			if (upperIndex === -1) {
				return {
					lower: arr[arr.length - 1],
					upper: null,
				};
			}
			return {
				lower: arr[upperIndex - 1],
				upper: arr[upperIndex],
			};
		},

		async getValueFun({ subject, ...val }) {
			this.loadingInstance = Loading.service({
				lock: true,
				text: "Loading",
				spinner: "el-icon-loading",
				background: "rgba(0, 0, 0, 0.7)",
				target: ".neon-box",
			});
			let quoteParam = await this.getQuoteParam();
			let priceParam = await this.getPriceParam();
			//询盘如果有期待时间，finaData手动添加数据
			if (val.expectTime) {
				quoteParam.finaData.push({
					parentId: 0,
					alias: "Expected Delivery Date",
					childList: [
						{
							parentId: 10000,
							alias: val.expectTime,
						},
					],
				});
			}
			let data = {
				platformProductId: this.platformProductId,
				proId: this.proId,
				email: "",
				productsName: "Neon",
				quoteCateId: this.pid,
				quoteCateChildId: this.cateId,
				isMobile: this.device === "mb" ? 1 : 0,
				isPicNeon: this.tabName === "Upload" ? 1 : 0,
				buyType: 7,
				quotePriceParam: JSON.stringify(priceParam),
				quoteParam: JSON.stringify(quoteParam),
				notes: "",
				...val,
				subject: this.customComment,
				telephone: val.areaCode + "-" + val.telephone,
				isFastQuote: 1,
				isSmallQty: getIsSmallQty(this.neonDiscountData, this.$store.state.enableTurnaroundTimeCheck, this.priceData),
			};
			this.otoEditInquiry(data);
		},

		// 计算价格
		async calculate() {
			let postData = await this.getPriceParam();
			neonApi.calculate(postData).then((res) => {
				this.priceData = res.data;
				this.isInquiry = !!res.data.onlyAddInquiry || !this.priceData.totalPrice;
				this.$nextTick(() => {
					this.contentLoadingInstance && this.contentLoadingInstance.close();
				});
			});
		},

		async addCart() {
			let quoteParam = await this.getQuoteParam();
			let priceParam = await this.getPriceParam();
			let data = {
				proId: this.proId,
				uuid: this.isLogin ? null : this.userUUID,
				userId: this.isLogin ? this.userId : null,
				buyType: 7,
				quoteCateId: this.pid,
				isPicNeon: this.tabName === "Upload" ? 1 : 0,
				isMobile: this.device === "mb" ? 1 : 0,
				quoteCateChildId: this.cateId,
				quantity: 1,
				quotePriceParam: JSON.stringify(priceParam),
				quoteParam: JSON.stringify(quoteParam),
				isFastQuote: 1,
				isSmallQty: getIsSmallQty(this.neonDiscountData, this.$store.state.enableTurnaroundTimeCheck, this.priceData),
			};
			otoAddCart(data, this.priceData).then((res) => {
				this.$toast.success(res.message);
				this.$nextTick(() => {
					this.loadingInstance.close();
				});
				this.$router.push({
					path: "/cart",
				});
			});
		},

		otoEditInquiry(data) {
			otoEditInquiry(data).then((res) => {
				this.loadingInstance.close();
				this.$confirm(this.lang.p22, this.lang.p21, {
					confirmButtonText: this.lang.Confirm,
					type: "success",
					showCancelButton: false,
					center: true,
				}).finally(() => {
					window.location.href = "/";
				});
			});
		},

		addToCart() {
			if (!this.checkSelectedForm()) return;
			this.loadingInstance = Loading.service({
				lock: true,
				text: "Loading",
				spinner: "el-icon-loading",
				background: "rgba(0, 0, 0, 0.7)",
				target: ".neon-box",
			});
			setTimeout(() => {
				this.addCart();
			}, 50);
		},

		addInquiry() {
			if (!this.checkSelectedForm()) return;
			this.infoDialogVisible = true;
		},

		checkSelectedForm() {
			if (this.tabName === "Enter Your Text") {
				if (!this.selectedData["Your Text"][0]["Your Text"].trim()) {
					this.$toast.error(this.lang.neon.notInputText);
					return false;
				}
			} else {
				if (!this.uploadFileList.length) {
					this.$toast.error(this.lang.neon.noUploadFile);
					return false;
				}
				if (!this.selectedData["What Size Would You Like?"].str || !this.selectedData["What Size Would You Like?"].width || !this.selectedData["What Size Would You Like?"].height) {
					this.$toast.error(this.lang.neon.whatSizeYouLike);
					return false;
				}
			}
			return true;
		},

		filterLeadingZero(val) {
			if (val && /^0\d+/.test(val)) {
				this.selectedData["What Size Would You Like?"].width = val.replace(/^0+/, ""); // 替换掉前导零
			}
		},

		limitSizeInput() {
			if (this.selectedData["What Size Would You Like?"].width < this.imgMinWidth) this.selectedData["What Size Would You Like?"].width = this.imgMinWidth;
			else if (this.selectedData["What Size Would You Like?"].width > 300) this.selectedData["What Size Would You Like?"].width = 300;
			this.selectedData["What Size Would You Like?"].height = (Number(this.selectedData["What Size Would You Like?"].width) * this.artworkImg.ratio).toFixed(0);
			this.selectedData["What Size Would You Like?"].str = this.selectedData["What Size Would You Like?"].width + this.unitSign + " / " + this.selectedData["What Size Would You Like?"].height + this.unitSign;
			this.debounceCalcPrice();
		},

		// 裁剪完成
		async cropImage(data) {
			const changeImg = this.uploadFileList.find((x) => x.original_filename == this.imageSrc.original_filename);
			// base64转url 先转文件 再上传图片
			const file = await dataURLtoFile(data, 1);
			const url = await uploadFile(file);
			changeImg.secure_url = url;
			const { width, height } = await getImageSize(data);
			changeImg.height = height;
			changeImg.width = width;
			changeImg.ratio = height / width;
			this.artworkImg = changeImg;
			this.debounceCalcPrice();
		},

		previewImgFromFiles(v) {
			this.preViewImgSrc = v;
			this.preViewDialogImg = true;
		},

		filterImg(val) {
			if (val.imageJson) {
				let arr = JSON.parse(val.imageJson);
				return arr.find((x) => {
					return x.name == "RGB Color";
				});
			}
		},

		validateEmoji(val, key) {
			if (key == "design") {
				this.selectedData["Your Text"] = val.replace(/[\uD83D\uD83C][\uDC00-\uDFFF]|\uD83D[\uDC00-\uDE4F]/g, "");
			} else {
				this.colorTextLine = val.replace(/[\uD83D\uD83C][\uDC00-\uDFFF]|\uD83D[\uDC00-\uDE4F]/g, "");
			}
		},
	},
	created() {
		getInfo({ id: this.pid }).then((res) => {
			this.platformProductId = res.data.platformProductId;
		});
		getPriceData({buyType: 7, productCateId: this.pid}).then((res) => {
			this.$store.commit("setMorePriceData", res.data);
		});
	},
	mounted() {
		this.getByPId();
		this.debounceFontSizePrice = debounce(this.getNeonFontSizePrice, 300);
		this.debounceCalcPrice = debounce(this.calculate, 500);
		this.debounceLimitSize = debounce(this.limitSizeInput, 300);
	},
};
</script>
<style lang="scss" scoped>
.quick-neon-signs {
	--neon-light-color: #5d33fd;
	margin: 1em auto;
	display: flex;
	flex-direction: column;
	overflow-y: auto !important;
	z-index: 1;
	.steps {
		flex: 1;
		padding: 0 1em;
		@media screen and (max-width: 1680px) {
			.step {
				padding-block: 0.5em !important;
			}
		}
		.step {
			padding-block: 1em;
			.step-content {
				margin-top: 1.15em;
				@media screen and (max-width: 1680px) {
					margin-top: 0.5em;
				}
			}

			.title-pine {
				margin-bottom: 1em;
			}

			&.upload-text {
				.change-tabs {
					margin: 0 auto;
					width: fit-content;
					background: #ebebeb;
					border-radius: 999999px;
					display: flex;

					.item {
						border-radius: 999999px;
						padding: calc(0.5em - 3px) 2.25em;
						color: #333333;
						cursor: pointer;
						&.is-active {
							background: #5544ff;
							color: #fff;
						}
					}
				}
				.step-content {
					.upload-inner {
						.NeonUpload {
							.uploadList {
								ul {
									min-height: 1.5vh;
								}
							}
							@media screen and(max-width: 768px) {
								background: #fafafa;
							}
						}
					}
					.design-input {
						position: relative;
						margin-top: 0.5em;
						b {
							display: block;
							top: 10px;
							font-size: 10px;
							left: 17px;
							position: absolute;
							color: #999999;
							z-index: 10;
						}

						.el-textarea {
							::v-deep .el-textarea__inner {
								@media screen and(max-width: 768px) {
									background: #fafafa;
								}
								&::placeholder {
									text-indent: 1.3em;
								}
								&:focus {
									border-color: var(--neon-light-color);
								}
							}
						}
					}
				}
			}

			&.select-color {
				.step-content {
					.font-color-box {
						display: flex;
						align-items: center;
						margin-bottom: 0.5em;
						.font-box {
							width: 8em;
						}
						.color-box {
							width: 6.25em;
							.option-input {
								height: 30px;
								border-radius: 6px;
								padding: 0 13px;
								color: #ccc;
								display: flex;
								justify-content: space-between;
								align-items: center;
								color: #000;
								border: 1px solid #dbdbdb;
								cursor: pointer;
								&.color-input {
									padding: 0 6px;
									@media screen and(max-width: 768px) {
										background: #fafafa;
									}
								}
								.color-ball {
									height: 20px;
									width: 20px;
									border-radius: 3px;
									object-fit: contain;
									background-position: center;
									background-color: var(--color);
									&.img-ball {
										width: 42px;
										aspect-ratio: 48 / 14;
										object-fit: contain;
										background-size: cover;
									}
								}
								i {
									transition: transform 0.3s ease-in-out;
									color: #282929;
									&.icon-active {
										transform: rotate(180deg);
										transition: all 0.3s ease-in-out;
									}
								}
							}
						}
					}
					.tube-box {
						width: 100%;
						.tube-input {
							width: 100%;
							display: flex;
							font-size: 14px;
							white-space: break-spaces;
							@media screen and(max-width:768px) {
								font-size: 12px;
							}
							span {
								cursor: pointer;
								color: #8d20fc;
								text-decoration: underline;
								i {
									font-weight: 600;
									transition: transform 0.3s ease-in-out;
								}
								&.icon-active {
									i {
										transform: rotate(180deg);
										transition: all 0.3s ease-in-out;
									}
								}
							}
						}
					}

					.note-input {
						position: relative;
						margin-top: 0.5em;
						b {
							display: block;
							top: 10px;
							font-size: 10px;
							left: 17px;
							position: absolute;
							color: #999999;
							z-index: 10;
						}

						.el-textarea {
							::v-deep .el-textarea__inner {
								font-size: 1em;
								@media screen and (max-width: 1300px) {
									font-size: calc(1em - 2px);
									background: #fafafa;
								}
								&::placeholder {
									text-indent: 1.3em;
								}
								&:focus {
									border-color: var(--neon-light-color);
								}
							}
						}
					}
				}
			}

			&.select-size {
				.step-content {
					border-radius: 10px;
					padding: 1.5vh;
					border: 1px solid #dbdbdb;
					@media screen and(max-width: 768px) {
						background: #fafafa;
					}
					.size-input {
						.el-input {
							::v-deep .el-input__inner {
								&:focus {
									border-color: #5544ff;
								}
							}
						}
					}

					.size-bottom {
						display: flex;
						justify-content: space-between;
						font-size: calc(1em - 2px);
						margin-top: 0.5vh;
						.size-detail {
							color: #5544ff;
						}
						.size-btn {
							text-decoration: underline;
							cursor: pointer;
						}
					}
				}
				.sizeBottomTips {
					text-align: center;
					color: #ff0000;
					font-size: calc(1em - 2px);
					margin-top: 9px;
					@media screen and (max-width: 768px) {
						margin-bottom: 5.5px;
					}
				}
			}

			&.turnaroundTime {
				.NeonDateBox {
					grid-template-columns: 1fr 1fr;
				}
			}
		}
	}
	.details {
		padding-block: 1vh;
		display: flex;
		justify-content: space-between;
		align-items: center;
		background: #ebebeb;
		padding-inline: 1em;
		position: sticky;
		bottom: 0;
		z-index: 2;
		.cur-price {
			font-size: 1.6em;
			color: #ff0000;
		}
		.raw-price {
			text-decoration: line-through;
		}
		.buttonGroup {
			display: flex;
			gap: 0.5em;
			.NeonButton {
				::v-deep .button {
					padding-inline: 1em !important;
				}
			}
		}
	}
}
</style>

<style lang="scss">
.custom-loading .el-loading-spinner{
	top: 20em !important;
}
.toast-box {
	.toasted {
		width: fit-content !important;
		padding: 0 20px !important;
		margin: 0 auto !important;
	}
}
.cropDialog {
	.base-dialog-model-con {
		.crop-area {
			.cropper-btn {
				.el-button--primary {
					background: linear-gradient(90deg, rgb(74, 72, 255) 0%, rgb(176, 18, 251) 100%);
					border: none;
				}
			}
			.crop-tips {
				color: #ff0000;
				text-align: center;
			}
		}
	}
}
input[type="range"] {
	-webkit-appearance: none;
	width: 100%;
	height: 5px;
	background: #d3d3d3;
	outline: none;
	border-radius: 5px;
	position: relative;
	&::before {
		content: "";
		position: absolute;
		left: 0;
		width: calc(((var(--value) - var(--min)) / (var(--max) - var(--min))) * 100%);
		height: 100%;
		background-color: #5544ff;
	}
}

input[type="range"]::-webkit-slider-thumb {
	-webkit-appearance: none;
	width: 12px;
	height: 12px;
	background: #5544ff;
	cursor: pointer;
	border-radius: 50%;
}
</style>
