<template>
	<div flex class="stepBar">
		<div flex center class="stepItem" v-for="(item, index) in step" :key="item" :class="{ active: activeStep > index }">
			<div class="stepLine" v-if="index > 0" :style="{ borderColor: color }"></div>
			<template v-if="index === 0">
				<b class="icon-xuanze_xuanzhong"></b>
			</template>
			<template v-else>
				<span class="stepNum" :style="{ backgroundColor: color }">{{ index + 1 }}</span>
			</template>
			<div class="stepLine" v-if="index !== step - 1" :style="{ borderColor: color }"></div>
		</div>
	</div>
</template>

<script>
export default {
	props: {
		step: {
			type: [Number],
			required: true,
		},
		activeStep: {
			type: [String, Number],
			required: true,
			default: 1,
		},
		color: {
			type: [String],
			default: "#ffffff",
		},
	},
};
</script>

<style lang="scss" scoped>
.stepBar {
	width: fit-content;
	margin: 0 auto 0.44rem;
    font-size: 12px;

	.stepItem {
		b {
			font-size: 1.25rem;
			color: #68bd2c;
		}

		&.active {
			.stepLine {
				border-color: #68bd2c !important;
			}
		}

		.stepLine {
			width: 5.5rem;
			border: 2px solid transparent;

            @include respond-to(mb){
                width: 4rem;
            }
		}

		.stepNum {
			display: flex;
			justify-content: center;
			align-items: center;
			width: 1.06rem;
			height: 1.06rem;
			flex-shrink: 0;
			text-align: center;
			border-radius: 50%;
			background-color: #ffffff;
		}
	}
}
</style>
