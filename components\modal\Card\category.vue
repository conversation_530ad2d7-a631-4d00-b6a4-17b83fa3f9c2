<template>
	<div class="productStyle" :class="modal.wrapClass" @click.self="setModalType({},modal.list,'product_crowd')" :style="modal.style">
		<div class="bps-container">
			<div class="title">
				<b :class="modal.outer[0].icon.value" v-if="modal.outer[0].icon"
					 @click="setModalType(modal.outer[0].icon,'','icon')"></b>
				<EditDiv tagName="h2" v-model:content="modal.outer[0].title.value"
								 @click="setModalType(modal.outer[0].title,'','text')" />
			</div>
			<div class="content" @click.self="setModalType({},modal.list,'product_crowd')">
				<div style="position:relative" class="part2" v-for="(l, li) in modal.list" :key="li" :childHoverIndex="li"
						 @click="setModalType(l.product_crowd,modal.list,'product_crowd')">
					<div class="item pointer" @click="linkPage(l)">
						<div class="imgWrap" v-if="l.product_crowd">
							<pic v-if="l.product_crowd.img" :src="l.product_crowd.img.value"
									 :alt="l.product_crowd.alt&&l.product_crowd.alt.value" />
							<div class="desText" :class="{isDisabled:l.product_crowd.disabled.value}">
								<template v-if="!l.product_crowd.disabled.value">
									<div class="price" v-if="l.product_crowd.subTitle">{{ l.product_crowd.subTitle.value }}
										<CCYRate class="priceText" :price="l.product_crowd.price.value"></CCYRate>
									</div>
									<div class="customNow" v-if="l.product_crowd.text">{{ l.product_crowd.text.value }} <b class="icon-jiantou icon"></b></div>
								</template>
								<template v-else>
									<div class="comingSoon">{{lang.ct}}</div>
								</template>
							</div>
						</div>
						<div class="smallTitle" v-if="l.showDisabledText">
							{{lang.st}}
						</div>
						<h3 class="smallTitle" v-if="l.product_crowd.title && !l.showDisabledText">
							{{ l.product_crowd.title.value }}
						</h3>
						<div class="desText" v-if="l.product_crowd.subTitle && l.product_crowd.price">
							<div class="price">{{ l.product_crowd.subTitle.value }}
								<CCYRate class="priceText" :price="l.product_crowd.price.value"></CCYRate>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	props: {
		data: {
			type: Object,
			default: {},
		}
	},
	computed:{
		lang(){
			return this.$store.getters.lang.home
		}
	},
	data() {
		return {
			showDisabledText:false,
			modal: {
				style: {},
				type: {},
				...this.data
			}
		};
	},
	watch: {
		modal: {
			handler(val) {
				if (process.env.isManage) this.$emit("update:data", val);
			},
			immediate: true,
			deep: true
		}
	},
	methods: {
		setModalType(target, targetArray, clickType, event, other) {
			this.$setModal(this, target, targetArray, clickType, event, other)
		},
		linkPage(l){
			if(process.env.isManage){
				return false;
			}
			if(l.product_crowd.disabled&&l.product_crowd.disabled.value){
				l.showDisabledText = !l.showDisabledText;
				this.$forceUpdate();
				return
			}
			if ((l.product_crowd.link.value).substring(0,5) == 'https') {
				window.open(l.product_crowd.link.value, '_blank');
			}else {
				this.$router.push({
					path: `${l.product_crowd.link.value}`
				})
			}
		}
	}
};
</script>

<style scoped lang="scss">
.productStyle {
	.bps-container {
		.viewMore {
			display: flex;
			justify-content: center;
			align-items: center;
			margin-top: 34px;

			a {
				display: flex;
				justify-content: center;
				align-items: center;
				padding: 10px 20px;
				background: #EEEEEE;
				border-radius: 19px;
				text-align: center;

				b {
					margin-left: 10px;
					font-size: 12px;
				}
			}
		}

		& > .title {
			display: flex;
			align-items: center;
			margin-bottom: 25px;

			b {
				margin-right: 18px;
				font-size: 26px;
				color: $color-primary;
			}

			h2 {
				font-size: 24px;
				font-weight: bold;
				color: #333333;
			}
		}

		.content{
			min-height: 200px;
		}
	}
}

.productStyle1 {
	padding: 47px 0 65px;

	.bps-container {
		.content {
			display: grid;
			grid-template-columns: repeat(4, 1fr);
			grid-column-gap: 30px;
			grid-row-gap: 48px;

			.item {
				position: relative;
				cursor: pointer;
				.desText{
					display: none;
				}
				.imgWrap {
					aspect-ratio: 376/350;
					img{
						width: 100%;
						height: 100%;
						object-fit: cover;
					}
				}

				.smallTitle {
					position: absolute;
					bottom: 0;
					left: 50%;
					transform: translate(-50%, 50%);
					display: flex;
					justify-content: center;
					align-items: center;
					width: 200px;
					height: 36px;
					background: #e6e6e6;
					border-radius: 18px;
					font-size: 14px;
					cursor: pointer;
					transition: 0.1s;
					font-weight: 400;
				}

				@media (any-hover: hover) {
					&:hover {
						.smallTitle{
							box-shadow: 0px 0px 21px 3px rgba(88, 88, 88, 0.65);
						}
					}
				}
			}
		}
	}
}

.productStyle2 {
	padding: 47px 0 0;

	.bps-container {
		.content {
			column-gap: 30px;
			row-gap: 20px;
			column-count: 4;
			column-fill: balance;

			.item {
				display: inline-block;
				width: 100%;
				position: relative;
				margin-bottom: 20px;

				.desText{
					display: none;
				}

				.smallTitle {
					position: absolute;
					bottom: 10px;
					left: 50%;
					transform: translate(-50%);
					display: flex;
					justify-content: center;
					align-items: center;
					width: 200px;
					height: 36px;
					background: #e6e6e6;
					border-radius: 18px;
					font-size: 14px;
					text-transform: uppercase;
					cursor: pointer;
					transition: 0.1s;
					font-weight: 400;

					h3 {
						font-weight: 400;
					}
				}

				@media (any-hover: hover) {
					&:hover {
						.smallTitle{
							box-shadow: 0px 0px 21px 3px rgba(88, 88, 88, 0.65);
						}
					}
				}
			}
		}
	}
}

.productStyle3 {
	padding: 53px 0 80px;

	.bps-container {
		max-width: 1200px;

		.viewMore {
			display: flex;
			justify-content: center;
			align-items: center;
			margin-top: 34px;

			a {
				display: flex;
				justify-content: center;
				align-items: center;
				padding: 10px 20px;
				background: #EEEEEE;
				border-radius: 19px;
				text-align: center;

				b {
					margin-left: 10px;
					font-size: 12px;
				}
			}
		}

		& > .title {
			justify-content: center;

			h2 {
				width: 567px;
				font-size: 30px;
				font-weight: bold;
				text-align: center;
			}
		}

		.content {
			display: grid;
			grid-template-columns: repeat(4, 1fr);
			grid-gap: 30px 13px;

			.item {
				overflow: hidden;
				& > .desText {
					display: none;

					.priceText {
						color: rgba(245, 70, 47, 1);
					}
				}
			}

			.imgWrap {
				overflow: hidden;
				position: relative;
				aspect-ratio: 290/380;

				img {
					width: 100%;
					height: 100%;
					object-fit: cover;
				}

				@media (any-hover: hover) {
					&:hover {
						.desText {
							bottom: 0;
						}
					}
				}

				.desText {
					position: absolute;
					left: 0;
					bottom: -100%;
					right: 0;
					padding: 20px;
					background-color: var(--color-primary-opacity);
					color: #ffffff;
					transition: all .3s;

					.price {
						margin-bottom: 10px;
						font-size: 14px;
					}

					.customNow {
						display: flex;
						align-items: center;
						justify-content: space-between;
						font-size: 24px;
						line-height: 1;
						font-weight: bold;
						text-transform: uppercase;
						.icon{
							font-size: 12px;
						}
					}

					.comingSoon {
						font-size: 18px;
					}
				}

				.desText.isDisabled {
					background-color: rgba(187, 187, 187, .8);
					text-align: center;
					text-transform: uppercase;
					line-height: 1.4;
				}
			}

			.smallTitle {
				padding: 10px;
				text-align: center;
				font-size: 18px;
				font-weight: 600;
			}
		}
	}
}

@media screen and (max-width: $mb-width) {
	.productStyle {
		.bps-container {
			.viewMore {
				margin-top: 20px;
				padding: 8px 10px;
				font-size: 12px;

				a {
					b {
						margin-left: 10px;
						font-size: 12px;
					}
				}
			}

			& > .title {
				margin-bottom: 20px;

				b {
					margin-right: 8px;
					font-size: 17px;
				}

				h2 {
					font-size: 18px;
				}
			}
		}
	}
	.productStyle1 {
		padding: 54px 0 27px;

		.bps-container {
			.content {
				grid-template-columns: repeat(2, 1fr);
				grid-gap: 13px 16px;

				.item {
					position: relative;

					img {
						width: 100%;
						aspect-ratio: 165/154;
					}

					.smallTitle {
						position: static;
						bottom: auto;
						left: auto;
						transform: none;
						width: 100%;
						height: 29px;
						border-radius: 0px 0px 5px 5px;
						font-size: 12px;
					}
				}
			}
		}
	}
	.productStyle2 {
		padding: 27px 0 0;

		.bps-container {
			.content {
				column-gap: 13px;
				row-gap: 10px;
				column-count: 2;
				column-fill: balance;

				.item {
					position: relative;

					img {
						width: 100%;
					}

					.smallTitle {
						position: static;
						bottom: auto;
						left: auto;
						transform: none;
						width: 100%;
						height: 29px;
						border-radius: 0;
						font-size: 12px;
					}
				}
			}
		}
	}
	.productStyle3 {
		padding: 15px 0;

		.bps-container {
			max-width: 100%;

			h2 {
				width: 100%;
				margin: 0 auto 15px;
				font-size: 18px;
			}

			.content {
				grid-template-columns: repeat(2, 1fr);
				grid-gap: 15px 10px;

				.item {
					& > .desText {
						display: block;
						font-size: 12px;
						text-align: center;
					}
				}

				.imgWrap {
					aspect-ratio: 168/220;

					img {
						width: 100%;
						height: 100%;
						object-fit: cover;
					}

					.desText:not(.isDisabled) {
						display: none;
					}

					.desText.isDisabled {
						font-size: 12px;
						bottom: 0;
						padding: 5px;

						span {
							white-space: nowrap;
						}

						.comingSoon {
							font-size: 12px;
						}
					}
				}

				.smallTitle {
					padding: 10px 5px 5px;
					font-size: 12px;
				}
			}
		}
	}
}

@media screen and (min-width: 1600px) {
	.productStyle1, .productStyle2 {
		.bps-container {
			width: 1600px;
			max-width: 1600px;
		}
	}
}
</style>
