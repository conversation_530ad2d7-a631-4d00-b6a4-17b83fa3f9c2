<template>
	<div id="custom-cufflinks">
		<Preloader v-if="isLoading"></Preloader>
		<template v-else>
			<article class="content">
				<div class="daohang">
					<span style="color: #d24600"><a href="https://www.gs-jj.com/baseball-pins-made-in-usa">All Products</a> </span>&nbsp;<span>> {{ titleName }}</span>
				</div>
				<!-- 报价导航 包含该大类的小分类 -->
				<!-- <QuoteNav :pid="pid" :cateList="cateList" title="We provide these Patch types for you."></QuoteNav> -->
				<div class="header">
					<h1>{{ titleName }}</h1>
				</div>
				<div class="center-content">
					<div class="leftArea" id="leftArea">
						<!-- 左上 <-> 右下 的消失动画 -->
						<transition enter-active-class="animate__animated animate__faster animate__fadeInBottomRight" leave-active-class="animate__animated animate__faster animate__fadeOutBottomRight">
							<SwiperDetail :imgList="carouselList" v-show="showRightArea" :selectedData="selectedData" :generalData="generalData" @toPosition="toPosition" :titleName="titleName" :showProfessional="true" :showViewMore="true" />
						</transition>

						<!-- 包含价格 数量 按钮的详情模块 不需要 -->
						<!-- <Detail class="type2" :key="3" :absoluteImg="comeFromDZ.absoluteImg" :selectedData="selectedData"
							:priceInfo="priceInfo" :customQty="customQty" :textInfo="textInfo" :generalData="generalData"
							@toPosition="toPosition" @addInquiry="debounceAddInquiry" @addCart="addCart"></Detail> -->
					</div>

					<div class="rightArea" id="rightAreaCustom">
						<div v-if="isFullReductionActivity && proId === 1" style="margin-bottom: 10px">
							<img src="https://static-oss.gs-souvenir.com/web/quoteManage/20250312/Trading_Pins_20250312QrjGxF.jpg" v-if="device == 'pc'" />
							<img src="https://static-oss.gs-souvenir.com/web/quoteManage/20250312/MB-Trading_Pins_20250312n26tPx.jpg" v-else />
						</div>
						<div v-for="(item, index) in filterShowGeneralData" :key="item.id" class="kk" :class="{ type1: picDialog }">
							<!-- 步骤1 -->
							<template v-if="item.paramName === 'Pins Size'">
								<div class="select-size" :class="{ mask: maskName === item.paramName }" :id="item.paramName" :key="index">
									<h3 class="step-title">
										<span stepColor> {{ item.customIndex }}</span>
										{{ lang.Select }}
										{{ item.alias ? item.alias : item.paramName }}
									</h3>
									<div class="box-border">
										<i class="el-icon-close" @click="closeMask"></i>
									</div>
									<div class="step-box step-size-box">
										<div class="step-size-leftArea">
											<div class="size-area">
												<div class="size-item-wrap">
													<div
														class="size-item item"
														v-for="(citem, cindex) in item.childList"
														:key="citem.id"
														@click="selectQuoteParams(item, citem), quotationClick(item, citem)"
														:class="{
															active: hasId(citem.id, selectedData[item.paramName]),
															onlyInquiry: citem.onlyAddInquiry === 1,
														}"
													>
														<div class="circle2"></div>
														<div class="textWrap">
															<p class="normal-text">
																{{ citem.alias ? citem.alias : citem.paramName }}
															</p>
															<span @click.stop v-if="citem.tips">
																<el-tooltip popper-class="cusToolTip" effect="light" :content="citem.tips" placement="top-start">
																	<b class="icon-wenhao1 tip-icon"></b>
																</el-tooltip>
															</span>
														</div>
														<Corner v-if="citem.labelText" :backgroundColor="citem.labelColor" type2 position="absolute">
															{{ citem.labelText }}
														</Corner>
													</div>
												</div>
											</div>
											<div class="step-size-rightArea forMB">
												<div class="textWrap">
													<p class="normal-text">{{ lang.pins.p1 }}</p>
												</div>
												<div class="shape-img">
													<img :src="shapeImg" alt="" />
												</div>
											</div>
											<div class="d-flex-center confirmBtnWrap">
												<QuoteBtn
													@click.native="showMaskFn(item.paramName)"
													:style="{
														opacity: selectedData[item.paramName].length > 0 ? 1 : 0.5,
													}"
													:disabled="!selectedData[item.paramName].length > 0"
													>{{ lang.next }}
												</QuoteBtn>
											</div>
										</div>
										<div class="step-size-rightArea">
											<div class="textWrap">
												<p class="normal-text">{{ lang.pins.p1 }}</p>
											</div>
											<div class="shape-img">
												<img :src="shapeImg" alt="" />
											</div>
										</div>
									</div>
								</div>
							</template>
							<!-- 步骤2 -->
							<div v-if="item.paramName === 'Upload Artwork & Comments'" class="Upload-Artwork">
								<StepTextUpload1 class="step-item step-upload" :class="{ mask: maskName === item.paramName, hideContent: $route.query.designPic }" :id="item.paramName" :index="item.customIndex" :itemData="item" :uploadList.sync="uploadArtworkList" :remark.sync="remark" :isUpload.sync="isUpload" @closeMask="closeMask" @showMaskFn="showMaskFn" :key="index"> </StepTextUpload1>
								<!-- <div class="d-flex-center confirmBtnWrap">
                                    <QuoteBtn @click.native="showMaskFn(item.paramName)" :style="{
                                        opacity: selectedData[item.paramName].length > 0 ? 1 : 0.5,
                                    }" :disabled="!selectedData[item.paramName].length > 0">{{ lang.next }}
                                    </QuoteBtn>
                                </div> -->
							</div>
							<!-- 步骤3 -->
							<div v-if="item.paramName == 'Plating'" class="part Select-Medal-Finish" :id="item.paramName" :class="{ 'step-active': item.paramName == maskName }">
								<h3 class="step-title hasTips5">
									<span>{{ item.customIndex }} </span> {{ lang.Select }}
									{{ item.alias ? item.alias : item.paramName }}
								</h3>
								<div class="tips">
									<div class="text">{{ lang.medals.step5Text }}</div>
									<div class="arrow" v-show="priceList.length">
										<b @click="handleScroll(-1)" class="iconfont icon-shangyige"></b>
										<b @click="handleScroll(1)" class="iconfont icon-xiayige"></b>
									</div>
								</div>
								<div class="quantity-div" v-show="priceList.length">
									<table class="table-price">
										<tr>
											<td class="table-title">{{ langsemiCustom.quantity }}</td>
											<td class="table-scroll-td" v-for="(item, index) in priceList" :key="index">
												{{ item.quantity }}
											</td>
										</tr>
										<tr>
											<td class="table-title">{{ langsemiCustom.listPrice }}</td>
											<td v-for="(item, index) in priceList" :key="index">
												<CCYRate :price="item.unitPrice"></CCYRate>
											</td>
										</tr>
									</table>
								</div>
								<div class="boxContent" ref="Select-Medal-Finish">
									<div v-for="itemChild in item.childList" :key="itemChild.id">
										<MyCheckBox :childList="itemChild.childList.length > 0 ? true : false" :tipNum.sync="tipNum" :bindValue="itemChild" :bindName="item.paramName" :titleValue="'alias'" :imageValue="'imageJson'" :aspectRatio="'aspect-ratio: 1200/1200'" :likeModel="selectedData" :sizeValue="sizeValue" @clickFun="selectQuoteParams(item, itemChild)" @selectCurrentParams="selectCurrentParams(item, itemChild)" :showPrice="false" numberInput></MyCheckBox>
									</div>
								</div>

								<div class="activity" :style="totalQuantity >= satisfiedQuantity ? 'background-color: #E9F5FF;color:#333333' : 'background-color: #EBEBEB;'" v-if="isFullReductionActivity && proId === 1">
									<div class="activity-text">
										<div class="activity-text-1">
											<el-checkbox v-model="checked" :disabled="totalQuantity >= satisfiedQuantity ? false : true" @change="handleCheckboxChange"></el-checkbox>
											<div>
												<span>*&nbsp;{{ lang.lanyard.FullCopy2 }} {{ giftQuantity1 }} Extra Free Trading Pins</span> {{ lang.lanyard.FullCopy4 }}
											</div>
										</div>
										<div class="activity-text-2" v-show="!checked && totalQuantity <= satisfiedQuantity">
											<!-- {{ lang.lanyard.FullCopy5 }} <span>{{ lang.lanyard.FullCopy6 }}</span> -->
											Order 300 pcs Trading Pins or more, confirm extra Trading Pins color, and get an additional 30 pcs for FREE!
										</div>
									</div>
									<div v-show="checked && totalQuantity >= satisfiedQuantity" style="margin-top: 15px">
										<div style="font-weight: 400; color: #333333; font-size: 15px">
											{{ lang.lanyard.FullCopy14 }}
											<span style="color: red">Free Trading Pins</span>
											{{ lang.lanyard.FullCopy9 }}:
										</div>
										<div class="box custom-scrollbar">
											<div v-for="(i, idx) in tradingPinsGiftList" :key="idx" class="activity-list custom-shadow2" :class="{ active: i.giftQuantity && i.giftQuantity > 0 }">
												<div :class="{ active2: i.giftQuantity && i.giftQuantity > 0 }" class="xuanzhong">√</div>
												<div class="activity-img">
													<img :src="getImg(i.imageJson)" style="width: 50px" />
												</div>
												<div class="p2">
													{{ i.alias ? i.alias : i.pantone }}
												</div>
												<div class="p3">
													<input :controls="false" class="myInput" onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode)))" type="number" v-model.number="i.giftQuantity" placeholder="QTY" @input="i.giftQuantity = $event.target.value" @change="presentedQuantityChange(i.giftQuantity)" />
												</div>
											</div>
										</div>

										<!-- <div style="margin: 15px 0 10px; font-size: 15px">{{ lang.lanyard.FullCopy10 }}</div> -->
									</div>
								</div>
								<div style="background-color: rgb(254, 240, 240); color: rgb(245, 108, 108); margin-top: 10px; padding: 5px; border-radius: 3px" v-show="presentedQuantity > 30">Note: The giveaway related quantity exceeds 30 pcs, please edit it again. Thank you!</div>

								<div class="text-center">
									<el-button
										class="myBtn"
										@click="showMaskFn(item.paramName)"
										:style="{
											opacity: selectedData[item.paramName].length > 0 ? 1 : 0.5,
										}"
										:disabled="!selectedData[item.paramName].length > 0"
									>
										{{ lang.next }}
									</el-button>
								</div>
								<i v-if="item.paramName == maskName" class="el-icon-close" @click="closeMask"></i>
							</div>
							<!-- 步骤4 -->
							<div v-if="item.paramName == 'Select Packaging'" class="part Select-Package" :id="item.paramName" :class="{ 'step-active': item.paramName == maskName }">
								<h3 class="step-title">
									<span>{{ item.customIndex }}</span>
									{{ lang.Select }} {{ item.alias ? item.alias : item.paramName }}
								</h3>
								<div class="boxContent">
									<MyCheckBox v-for="itemChild in item.childList" :key="itemChild.id" :bindValue="itemChild" :bindName="item.paramName" :titleValue="'paramName'" :imageValue="'imageJson'" :aspectRatio="'aspect-ratio: 316/204'" :showPrice="true" :sizeValue="sizeValue" :likeModel="selectedData" @clickFun="selectQuoteParams(item, itemChild)"> </MyCheckBox>
								</div>
								<i v-if="item.paramName == maskName" class="el-icon-close" @click="closeMask"></i>
							</div>

							<!-- 步骤5 -->
							<template v-if="item.paramName == 'Turnaround Time'">
								<div class="part Delivery-Date" :class="{ 'step-active': maskName === item.paramName }" :id="item.paramName">
									<h3 class="step-title">
										<span>{{ item.customIndex }}</span
										>{{ item.alias ? item.alias : item.paramName }}
									</h3>
									<div style="display: flex">
										<StepTime class="step-item step-date" :index="item.customIndex" :itemData="item" :priceInfo="priceInfo" :previewMode="previewMode" :selectedParams="selectedData" @showMaskFn="showMaskFn" @selectItem="selectQuoteParams($event.item, $event.citem)" @closeMask="closeMask" :key="index"></StepTime>
									</div>
									<i v-if="item.paramName == maskName" class="el-icon-close" @click="closeMask"></i>
								</div>
							</template>
						</div>
					</div>
				</div>
			</article>
			<div class="footer" id="foot">
				<Detail class="type2" :key="3" :absoluteImg="comeFromDZ.absoluteImg" :selectedData="selectedData" :priceInfo="priceInfo" :customQty="customQty" :textInfo="textInfo" :generalData="generalData" @toPosition="toPosition" @addInquiry="debounceAddInquiry" @addCart="addCart" :showTextTip="false"> </Detail>
			</div>
			<el-drawer :visible.sync="showArea" :with-header="true" custom-class="drawDialog" :size="device === 'ipad' ? '728px' : '85%'">
				<Detail class="type3" :key="2" :absoluteImg="comeFromDZ.absoluteImg" :selectedData="selectedData" :priceInfo="priceInfo" :customQty="customQty" :textInfo="textInfo" :generalData="generalData" @toPosition="toPosition" @addInquiry="debounceAddInquiry" @addCart="addCart"></Detail>
			</el-drawer>
			<!--    遮罩-->
			<myMask :maskName.sync="maskName"></myMask>
			<!-- 建议弹窗-->
			<RecomendDialog :commentsDialog.sync="commentsDialog" :paramValue="selectedParamsValue" @next="recomendNext" :showUpload="false"> </RecomendDialog>
			<!--  手机，ipad端预览-->
			<PreviewBtn :showArea.sync="showArea"></PreviewBtn>
			<BaseDialog v-model="showOtoDialog" :width="device != 'mb' ? '570px' : '90%'">
				<div class="otoWrap">
					<img src="@/assets/images/oto.png" alt="" />
					<p>{{ lang.p1 }}</p>
					<h3>{{ lang.p2 }}</h3>
					<p style="color: #666666">
						{{ lang.p3 }}<br />
						{{ lang.p4 }}.
					</p>

					<div class="box">
						<p class="t1">
							{{ lang.p5 }}<br />
							{{ lang.p6 }}!
						</p>
						<a href="https://www.o2o.co/previewWeb" style="text-decoration: none">
							<el-button type="primary">{{ lang.p7 }}</el-button>
						</a>
					</div>
				</div>
			</BaseDialog>
			<VideoPreviewDialog :pic-dialog.sync="picDialog" :zoom-pic="zoomPic" :temp-type="tempType" :selected-params-value="selectedParamsValue" @qtyAndBtnConfirm="qtyAndBtnConfirm" @qtyAndBtnNext="qtyAndBtnNext" @dialogNextStep="dialogNextStep"></VideoPreviewDialog>
			<BaseDialog v-model="upgradeDialog" :width="device != 'mb' ? '640px' : '320px'">
				<Upgrade @closeUpgrade="upgradeDialog = false" :tipArr="tipArr" :typeName="cateData.cateName" unit="pairs" @upgradeLevel="upgradeLevel"></Upgrade>
			</BaseDialog>
			<infoDialog :infoDialogVisible.sync="infoDialogVisible" @getValue="getValueFun" :otherUpload="uploadArtworkList" :recomendUpload="recomendUpload" :uploadList.sync="uploadList"></infoDialog>
			<BaseDialog v-model="noFileDialog" :width="device != 'mb' ? '485px' : '90%'" :model="false">
				<template #closeIcon>
					<div style="display: none"></div>
				</template>
				<infoUpload :infoUploadList="infoUploadList" @pushInfoList="pushInfoList" @delInfoList="delInfoList" @updateInquiry="updateInquiry" @closeInfoDialog="closeInfoDialog"> </infoUpload>
			</BaseDialog>
		</template>
	</div>
</template>
<script>
import "@/plugins/element";
import MyCheckBox from "@/components/Medals/MyCheckBox";
import MyCheckBox3 from "@/components/Medals/MyCheckBox3";
import BaseDialog from "@/components/Quote/BaseDialog";
import VideoPlayer from "@/components/Quote/VideoPlayer.vue";
import Upgrade from "@/components/Quote/Upgrade";
import DialogBM from "@/components/Medals/DialogBM";
import QtyAndBtn from "@/components/Medals/QtyAndBtn";
import Corner from "@/components/Medals/Corner";
import infoDialog from "@/components/Medals/infoDialog";
import QuoteNav from "@/components/Cufflinks/QuoteNav";
import QuoteBtn from "@/components/Quote/QuoteBtn";
import quoteMixin from "@/mixins/quote";
import quoteBanChoiceMixins from "@/mixins/quoteBanChoice";
import StepTime from "@/components/Quote/StepTime.vue";
import StepTextUpload1 from "@/components/Cufflinks/StepTextUpload1.vue";
import StepQty from "@/components/Cufflinks/StepQty.vue";
import RecomendDialog from "@/components/Quote/RecomendDialog.vue";
import myMask from "@/components/Quote/Mask.vue";
import Detail from "@/components/Quote/Detail.vue";
import SwiperDetail from "@/components/Cufflinks/SwiperDetail.vue";
import PreviewBtn from "@/components/Quote/PreviewBtn.vue";
import Preloader from "@/components/Quote/Preloader.vue";

import { isImageType } from "@/utils/utils";
import { medalsApi } from "@/api/medals/medals";
import { indexApi } from "@/api/lanyardQuote/index";
import VideoPreviewDialog from "@/components/Quote/VideoPreviewDialog.vue";

export default {
	head() {
		return {
			title: "Quote page",
		};
	},
	name: "cufflinks",
	components: {
		VideoPreviewDialog,
		Preloader,
		PreviewBtn,
		Detail,
		SwiperDetail,
		myMask,
		RecomendDialog,
		StepQty,
		StepTime,
		MyCheckBox,
		MyCheckBox3,
		BaseDialog,
		VideoPlayer,
		DialogBM,
		QtyAndBtn,
		Corner,
		infoDialog,
		Upgrade,
		QuoteNav,
		QuoteBtn,
		StepTextUpload1,
	},
	mixins: [quoteMixin, quoteBanChoiceMixins],
	data() {
		return {
			tipNum: 1,
			pid: 286,
			productsName: "custom embroidered patches",

			tablePageLeft: [],
			tableCurPage: 0,
			checked: false,

			restaurants: [
				{ value: "10", address: "10" },
				{ value: "25", address: "25" },
				{ value: "50", address: "50" },
				{ value: "100", address: "100" },
				{ value: "200", address: "200" },
				{ value: "300", address: "300" },
				{ value: "500", address: "500" },
				{ value: "1000", address: "1000" },
				{ value: "3000", address: "3000" },
				{ value: "5000", address: "5000" },
				{ value: "10000", address: "10000" },
			],

			imgList: [
				{
					imgUrl: "https://oss-static-cn.liyi.co/web/quoteManage/20240310/Engraved_Cuffinks_20240310jaMsN7.jpg",
					url: "https://oss-static-cn.liyi.co/web/quoteManage/20240310/Engraved_Cuffinks_20240310jaMsN7.jpg",
					alt: "Engraved_Cuffinks",
				},
				{
					imgUrl: "https://oss-static-cn.liyi.co/web/quoteManage/20240310/Select_Attachment-Toggle_Closure_20240310nYaKJs.jpg",
					url: "https://oss-static-cn.liyi.co/web/quoteManage/20240310/Select_Attachment-Toggle_Closure_20240310nYaKJs.jpg",
					alt: "Select_Attachment-Toggle_Closure",
				},
				{
					imgUrl: "https://oss-static-cn.liyi.co/web/quoteManage/20240310/More_Options-Transparent_Color_20240310CitzZ7.jpg",
					url: "https://oss-static-cn.liyi.co/web/quoteManage/20240310/More_Options-Transparent_Color_20240310CitzZ7.jpg",
					alt: "More_Options-Transparent_Color",
				},
				{
					imgUrl: "https://oss-static-cn.liyi.co/web/quoteManage/20240310/Picture_Cuffinks_20240310bayAN4.jpg",
					url: "https://oss-static-cn.liyi.co/web/quoteManage/20240310/Picture_Cuffinks_20240310bayAN4.jpg",
					alt: "Picture_Cuffinks",
				},
			],
			cateList: [], //推荐分类数据
			showProfessional: false,
			uploadArea: true,
			priceList: [],
			qlist: [],
		};
	},
	computed: {
		totalQuantity() {
			let total = 0;
			let findPlating = this.filterShowGeneralData.find((item) => {
				return item.paramName === "Plating";
			});

			if (findPlating) {
				findPlating.childList.forEach((f) => {
					if (f.inputNum) {
						total += f.inputNum || 0;
					}
				});
			}
			return total;
		},
		langsemiCustom() {
			return this.$store.getters.lang.semiCustom || {};
		},
	},
	watch: {
		totalQuantity(val) {
			console.log(val, "val");
			let findPlating = this.filterShowGeneralData.find((item) => {
				return item.paramName === "Plating";
			});
			if (!findPlating) {
				return false;
			}
			let filterPlatingArr = findPlating.childList.filter((item) => item.inputNum && item.inputNum > 0);
			if (val >= this.satisfiedQuantity) {
				this.checked = true;
			} else {
				this.checked = false;
				filterPlatingArr.forEach((i) => {
					i.giftQuantity = null;
				});
			}
			this.tradingPinsGiftList = filterPlatingArr;
		},
		routingId: {
			handler() {
				indexApi
					.quotationList({
						proId: this.proId,
						cateId: this.routingId,
						projectName: this.projectName,
					})
					.then((res) => {
						this.qlist = res.data;
						let find = this.qlist.find((item) => item.size === "1.75 Inches");
						if (find) {
							this.priceList = JSON.parse(find.priceJson);
						}
					});
			},
		},
	},
	methods: {
		handleCheckboxChange() {
			if (this.totalQuantity >= this.satisfiedQuantity) {
				this.checked = true; // 强制恢复为 true，防止取消
			}
		},
		presentedQuantityChange(giftQuantity) {
			this.presentedQuantity = this.tradingPinsGiftList.reduce((sum, item) => {
				let qua = item.giftQuantity ? parseInt(item.giftQuantity) : 0;
				return sum + qua;
			}, 0);
		},
		getImg(img) {
			return JSON.parse(img)[0].url;
		},
		isImageType,
		getAppRecommendCateList() {
			medalsApi.getAppRecommendCateList({ proId: this.proId, id: this.pid }).then((res) => {
				this.cateList = res.data;
				this.titleName = this.cateList[0]?.cateName;
			});
		},
		quotationClick(item, itemChild) {
			let find = this.qlist.find((r) => r.size === itemChild.paramName);
			if (find) {
				this.priceList = JSON.parse(find.priceJson);
			}
		},
		groupAndSum(widths, containerWidth, titleWidth) {
			let groups = [];
			let currentSum = 0;
			for (let width of widths) {
				if (titleWidth + currentSum + width <= containerWidth) {
					currentSum += width;
				} else {
					groups.push(currentSum);
					currentSum = width;
				}
			}

			// Append the last group
			if (currentSum > 0) {
				groups.push(currentSum);
			}

			return groups;
		},
		async initScrollTable() {
			this.$nextTick(() => {
				const tableScrollTd = document.querySelectorAll(".table-scroll-td");
				const list = [];
				// 获取所有小格子的宽度
				tableScrollTd.forEach((x) => {
					const { width } = x.getBoundingClientRect();
					list.push(width);
				});
				// 获取展示区域的宽度
				const parentWidth = document.querySelector(".step-title").offsetWidth;
				const titleWidth = document.querySelector(".table-title").offsetWidth;
				this.tablePageLeft = this.groupAndSum(list, parentWidth, titleWidth);
			});
		},

		async handleScroll(val) {
			if (!this.tablePageLeft.length) await this.initScrollTable();
			this.tableCurPage += val;
			if (this.tableCurPage > this.tablePageLeft.length) this.tableCurPage = 1;
			if (this.tableCurPage < 1) this.tableCurPage = this.tablePageLeft.length;
			// 获取left
			let left = 0;
			if (this.tableCurPage === 1) {
				left = this.tablePageLeft[0];
			} else if (this.tableCurPage === this.tablePageLeft.length) {
				left = this.tablePageLeft[this.tableCurPage];
			} else {
				left = this.tablePageLeft.slice(0, this.tableCurPage).reduce((pre = 0, cur = 0) => (pre += cur));
			}
			document.querySelector(".quantity-div").scrollLeft = left;
		},

		// scrollRight(){
		//     this.initScrollTable()
		//     // let width=document.querySelector(".step-title").offsetWidth;
		//     let widthTable=document.querySelector(".table-price").offsetWidth;
		//     document.querySelector(".quantity-div").scrollLeft+=widthTable
		// },
		// scrollLeft(){
		//     // let width=document.querySelector(".step-title").offsetWidth;
		//     let widthTable=document.querySelector(".table-price").offsetWidth;
		//     document.querySelector(".quantity-div").scrollLeft-=widthTable
		// }
	},
	created() {
		this.getAppRecommendCateList();
	},
};
</script>
<style scoped lang="scss">
#custom-cufflinks {
	color: #333;
	font-family: Calibri;

	::v-deep .el-input__inner:focus {
		border-color: $color-primary;
	}

	::v-deep video {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}

	::v-deep ul {
		margin-left: 0;
	}

	::v-deep .video-js,
	::v-deep .vjs-poster {
		background-color: white !important;
	}

	.box-border {
		display: none;

		.el-icon-close {
			display: flex;
			justify-content: center;
			align-items: center;
			position: absolute;
			font-weight: 700;
			top: 0;
			right: 0;
			width: 40px;
			height: 40px;
			transform: translate(50%, -50%);
			cursor: pointer;
			background: #ffffff;
			border-radius: 50%;
			box-shadow: 1px 2px 20px 0 rgba(0, 0, 0, 0.2);
			z-index: 10;
		}
	}

	.viewMore {
		@media screen and (min-width: 768px) {
			display: none;
		}
	}

	::v-deep .StepBox {
		border-radius: 10px;
		overflow: hidden;
		border: 2px solid #ebebeb;
		transition: all 0.2s;

		@media screen and (max-width: 767px) {
			padding-bottom: 0 !important;
			border-width: 1px;
			border-radius: 6px;
		}

		&:hover {
			border-color: $color-primary !important;
		}

		&.active {
			border-color: $color-primary !important;

			.title {
				color: #333 !important;
			}
		}

		.se {
			align-items: start;

			.product-info {
				padding: 11px 0;
				margin-top: 0;
				align-items: start;

				@media screen and (max-width: 767px) {
					padding: 8px 0;
				}

				.radio-beauty {
					min-width: 20px;
					height: 20px;
					border-color: #cccccc;
					margin-left: 20px;
				}

				.radio-beauty::after {
					width: 10px;
					height: 10px;
					background-color: #fff;
				}

				@media screen and (max-width: 767px) {
					.radio-beauty {
						min-width: 12px;
						width: 12px;
						height: 12px;
						border-color: #cccccc;
						margin: 0 5px 0 12px;
					}

					.radio-beauty::after {
						width: 6px;
						height: 6px;
						background-color: #fff;
					}
				}
			}
		}

		@media (any-hover: hover) {
			&:hover {
				.zoomIcon {
					color: $color-primary;
				}

				.product-info {
					.radio-beauty {
						background-color: $color-primary;
						border-color: $color-primary;

						&::after {
							background-color: white;
						}
					}

					.title {
						// color: $color-primary;
					}
				}
			}
		}
	}

	.picWrap {
		display: flex;
		justify-content: center;
		align-items: center;
		flex-direction: column;
		width: 100%;
		padding: 0px !important;
	}

	.text-center {
		text-align: center;
	}

	.myBtn {
		background: $color-primary;
		border-radius: 10px;
		font-size: 18px;
		// font-family: Calibri;
		font-weight: 400;
		color: #ffffff;
		padding: 10px 65px;
		margin-top: 30px;

		@media screen and (max-width: 767px) {
			width: 125px;
			height: 30px;
			background: $color-primary;
			border-radius: 5px;
			font-size: 12px;
			// font-family: Arial;
			font-weight: 400;
			color: #ffffff;
			margin-top: 9.5px;
			padding: 0;
		}
	}

	.header {
		h1 {
			font-size: 30px;
			// font-family: Calibri;
			font-weight: bold;
			color: #333;
			margin-bottom: 9px;
		}
	}

	::v-deep .content {
		// width: 1200px;
		// margin: 0 auto;
		padding-bottom: 20px;
		display: grid;
		grid-template-columns: repeat(48, 1fr);
		min-width: 1100px;

		.daohang {
			padding-left: 20px;
			margin-top: 15px;
			grid-column: 8/42;

			@media screen and (max-width: 767px) {
				margin-top: 0;
				padding-top: 15px;
			}
		}
		.header {
			padding: 35px 0 0 42px;
			grid-column: 8/42;
		}

		.cufflink-quote {
			padding: 24px 0;
		}

		.center-content {
			// display: grid;
			// grid-template-columns: 1fr 1fr;
			// column-gap:30px;
			display: flex;
			justify-content: space-between;
			grid-column: 8/42;
			column-gap: 10px;
		}

		.leftArea {
			width: 50%;
			// width: 540px;
			padding: 20px;
			padding-right: 28px;
			.myswiper1 {
				.swiper-wrapper {
					margin-top: 15px;
					.swiper-slide {
						width: 100px;
						height: 100px;
						border-radius: 5px;
					}
				}
			}
			.con {
				margin-top: 20px;
				.viewMore {
					display: none !important;
				}
			}
			.mask {
				z-index: 101;
				background-color: #fff;
			}
			@media screen and (max-width: 767px) {
				width: 100%;
			}
		}

		.rightArea {
			width: 50%;
			@media screen and (max-width: 767px) {
				width: 100%;
			}

			// .Upload-Artwork {
			//     margin-bottom: 20px;
			// }

			// .Select-Size {
			//     .boxContent {
			//         grid-template-columns: repeat(2, 1fr) !important;
			//         column-gap: 20px;
			//         row-gap: 20px;

			//         @media screen and (max-width: 767px) {
			//             grid-template-columns: repeat(2, 1fr) !important;
			//         }
			//     }
			// }
			.select-size {
				margin-bottom: 30px;
				padding: 20px;

				.step-box {
					display: flex;
					column-gap: 40px;

					.step-size-leftArea {
						display: flex;
						flex-direction: column;
						justify-content: space-between;

						.size-area {
							.size-item-wrap {
								display: flex;
								flex-direction: column;
								row-gap: 15px;

								.textWrap {
									display: flex;
									align-items: center;
									column-gap: 8px;
								}

								.size-item {
									display: flex;
									align-items: center;
									column-gap: 10px;
									width: 238px;
									padding: 8px 20px;
									background: #f6f6f6;
									border: 2px solid #ebebeb;
									border-radius: 10px;
									cursor: pointer;

									.circle2 {
										width: 20px;
										height: 20px;
										border-radius: 10px;
										background-color: #fff;
										border: 1px solid #cccccc;
										display: flex;
										justify-content: center;
										align-items: center;
									}

									.circle2::after {
										content: "";
										width: 10px;
										height: 10px;
										border-radius: 5px;
										background: #fff;
									}

									&:hover {
										border-color: $color-primary;

										.circle2 {
											background: $color-primary;
											border-color: $color-primary;
										}
									}
								}

								.size-item.active {
									border-color: $color-primary;

									.circle2 {
										background: $color-primary;
										border-color: $color-primary;
									}
								}
							}
						}

						.forMB {
							display: none;
						}

						.confirmBtnWrap {
							button {
								width: 238px;
								font-size: 16px;
							}
						}
					}

					.step-size-rightArea {
						.textWrap {
							margin-bottom: 20px;
						}
					}
				}
				&.mask {
					position: relative;
					z-index: 101;

					.confirmBtnWrap {
						position: relative;
					}

					.box-border {
						position: absolute;
						left: 0;
						right: 0;
						top: 0;
						bottom: 0;
						display: block;
						background-color: #fff;
						border: 1px solid #d9dbdd;
					}

					.step-title {
						position: relative;
						z-index: 10;
					}

					.step-box {
						position: relative;
					}
				}
				@media screen and (max-width: 767px) {
					background-color: #fff;
					border-radius: 5px;
					margin: 10px 0;
					padding: 20px 8px;
					&.mask {
						.box-border {
							.el-icon-close {
								width: 30px;
								height: 30px;
								transform: translate(0, 0);
								box-shadow: none;
							}
						}
					}
					.step-box {
						.step-size-leftArea {
							.size-area {
								.size-item-wrap {
									display: grid;
									grid-template-columns: 1fr 1fr;
									gap: 8px;
									margin-bottom: 22px;
									.size-item {
										width: 100%;
										padding: 10px;
										border: 1px solid #ebebeb;
										border-radius: 5px;
										.circle2 {
											width: 16px;
											height: 16px;
										}
										.circle2::after {
											width: 8px;
											height: 8px;
										}
									}
								}
							}
							.confirmBtnWrap {
								button {
									margin-top: 15px;
									width: 125px !important;
									font-size: 12px !important;
								}
							}
							.forMB {
								display: block;
								.textWrap {
									margin-bottom: 10px;
								}
								.shape-img {
									padding: 0 10vw;
								}
							}
						}
						.step-size-rightArea {
							display: none;
						}
					}
				}
			}
			.Upload-Artwork {
				.step-upload {
					padding: 20px;
					margin-bottom: 30px;

					.step-box {
						grid-template-columns: 1.1fr 1fr;
						column-gap: 15px;

						.uploadArea {
							.t1 {
								margin-bottom: 3px;
							}

							.upload-box {
								height: 150px;
								padding: 0 0 6px;
								margin-left: 30px;

								.uploadList {
									margin-bottom: 0;

									.uploadIcon {
										height: 40px;
										font-size: 31px;
										color: #9e9e9e;
									}
								}

								.upload-btn {
									button {
										width: 138px;
										font-size: 16px;
									}
								}

								> div:last-child {
									display: flex;
									flex-direction: column;
									align-items: center;
								}
							}

							> div:last-child {
								display: none;
							}

							.later-text {
								column-gap: 3px;
							}
							.circle2 {
								width: 20px;
								height: 20px;
								border-radius: 10px;
								background-color: #fff;
								border: 1px solid #cccccc;
								display: flex;
								justify-content: center;
								align-items: center;
							}

							.circle2::after {
								content: "";
								width: 10px;
								height: 10px;
								border-radius: 5px;
								background: #fff;
							}
						}

						.editArea {
							textarea {
								resize: none;
								border-radius: 10px;
								background: #f9f9f9;
								border: 1px solid #ebebeb;
							}
						}
					}
					@media screen and (max-width: 767px) {
						padding: 20px 8px;
						margin-bottom: 10px;
						.step-box {
							display: block;
							.uploadArea {
								.t1,
								.later {
									font-size: 14px;
									margin-bottom: 3px;
									.uploadIcon {
										margin-right: 2px;
									}
								}
								.later {
									padding-top: 15px;
								}
								.t2 {
									padding-left: 17px;
								}
								.upload-box {
									margin-left: 0;
									padding: 10px 0 4px;
									.upload-btn {
										button {
											font-size: 14px;
										}
									}
								}
								.circle2 {
									margin-right: 5px;
									width: 12px;
									height: 12px;
								}

								.circle2::after {
									width: 6px;
									height: 6px;
								}
							}

							.editArea {
								.t1 {
									font-size: 14px;
								}
								textarea {
									height: 107px;
									padding: 4px 10px;
									border-radius: 5px;
								}
								textarea::placeholder {
									font-size: 12px;
								}
							}
						}
					}
				}
			}

			.Select-Package {
				margin-bottom: 30px;
				.se {
					.product-info {
						padding: 16px 20px 0 0;
					}
					.PriceText {
						padding: 0 20px 14px 52px;
					}
				}

				.boxContent {
					grid-template-columns: repeat(2, 1fr) !important;
					column-gap: 20px;
					row-gap: 20px;
				}
				@media screen and (max-width: 767px) {
					margin-bottom: 10px;
					.se {
						.product-info {
							padding: 10px 12px 0 0;
						}
						.PriceText {
							padding: 0 12px 10px 29px;
						}
					}
				}
			}

			.Select-Medal-Finish {
				margin-bottom: 30px;

				.activity {
					padding: 7px 0 15px 20px;
					color: #858282;
					margin-top: 10px;

					.activity-text {
						line-height: 30px;

						.activity-text-1 {
							display: flex;
							font-size: 16px;
							font-weight: bold;

							span {
								color: #ff0000;
								margin-left: 5px;
							}

							::v-deep .el-checkbox__inner {
								border-color: #c4c7cd;
							}
						}

						.activity-text-2 {
							margin-left: 20px;
							color: #858282;
							line-height: 20px;

							span {
								color: #ff0000;
							}

							@media screen and (max-width: 768px) {
								line-height: 16px;
							}
						}
					}

					.activity-list {
						margin-right: 10px;
						width: 173px;
						border: 1px solid #d9dbdd;
						margin-top: 10px;
						border-radius: 5px;
						display: grid;
						grid-template-columns: repeat(3, 1fr);
						grid-template-rows: repeat(2, 40px);

						.activity-img {
							grid-column: 1/2;
							grid-row: 1/3;
							padding: 11px 10.7px;

							div {
								width: 35px;
								height: 60px;
								border: 1px solid #d9dbdd;
							}
						}

						.myInput {
							width: auto;
							line-height: 1em;
							text-align: left;
							-webkit-appearance: none;
							background-color: var(--el-input-bg-color, var(--el-color-white));
							border-radius: 5px !important;
							background-image: none;
							border: 1px solid #dcdfe6;
							box-sizing: border-box;
							color: var(--el-input-text-color, var(--el-text-color-regular));
							display: inline-block;
							font-size: 16px;
							height: 30px;
							line-height: 30px;
							outline: 0;
							padding: 0 11px;
							transition: var(--el-transition-border);
							width: 100%;
							font-family: Calibri;

							@media screen and (max-width: 768px) {
								height: 30px;
								line-height: 30px;
								font-size: 12px;
							}
						}

						.p2 {
							grid-column: 2/4;
							grid-row: 1/2;
							padding: 5px 12px 5px 5px;
							overflow: hidden;
							text-overflow: ellipsis;
							white-space: nowrap;
							font-size: 15px;
							font-weight: 400;
							color: #666666;
							line-height: 2em;

							@media screen and (max-width: 768px) {
								font-size: 12px;
								font-family: Arial;
								line-height: 2.5em;
							}

							&:hover {
								overflow: visible;
							}
						}

						.p3 {
							grid-column: 2/4;
							grid-row: 2/3;
							display: flex;
							padding: 0 15px 0 5px;

							@media screen and (max-width: 768px) {
								padding: 0 5px;
							}
						}

						.xuanzhong {
							position: absolute;
							right: 0px;
							top: -1px;
							font-size: 11px;
							display: none;

							&.active2 {
								display: block;
								background-color: var(--color-primary);
								color: #fff;
								padding: 0 3px;
								border-radius: 0 4px 0 0;
								@media screen and (max-width: 768px) {
									padding: 1px 3px;
								}
							}
						}

						&.active {
							border-color: $color-primary;
						}
					}

					.custom-shadow2 {
						position: relative;
						background: #fff;

						&::before,
						&::after {
							content: "";
							position: absolute;
							z-index: -1;
							bottom: 14px;
							left: 0px;
							width: 50%;
							height: 20%;
							box-shadow: 0 14px 7px #d9dbdd;
							transform: rotate(-3deg);
						}

						&::after {
							right: 0;
							left: auto;
							transform: rotate(3deg);
						}
					}

					.custom-scrollbar {
						grid-template-columns: repeat(5, 1fr);
						display: grid;
						position: relative;
						z-index: 0;
					}

					@media screen and (max-width: 767px) {
						padding: 7px 0 7px 10px;
						margin-top: 10px;
						.activity-text {
							align-items: center;

							div {
								font-size: 3.3vw;
							}
						}
						.custom-scrollbar {
							grid-template-columns: repeat(2, 1fr);
						}

						.activity-list {
							margin-right: 0;
							width: 39.5333vw;
						}
					}
				}
				.tips {
					display: flex;
					justify-content: space-between;
					align-items: center;
					margin-bottom: 12px;
					column-gap: 12%;
					// .text {
					//     width:70%;
					// }
					.arrow {
						display: flex;
						column-gap: 25px;
						width: 60px;
						b {
							padding: 5px 0;
							font-weight: bold;
							font-size: 18px;
							cursor: pointer;
							color: #757575;
						}
						b:hover {
							color: $color-primary;
						}
					}
				}

				.quantity-div {
					margin-bottom: 20px;
					// overflow-x:scroll;
					scroll-behavior: smooth;
					overflow: hidden;
					&::-webkit-scrollbar-thumb {
						border-radius: 10px;
						box-shadow: inset 0 0 0px rgba(240, 240, 240, 0.5);
						background-color: red;
					}
					.table-price {
						width: 100%;
						border-collapse: collapse;
						border-spacing: 0;
						text-align: center;
						// margin-bottom: 20px;
						td.table-title {
							position: sticky;
							left: 0;
							font-weight: bold;
							white-space: nowrap;
							background: #fff3ed;
							padding: 12px 15px;
							border: none;
						}
						td {
							min-width: 65px;
							background: #f9f9f9;
							padding: 12px 10px;
							border-right: 1px solid #ebebeb;
						}
						tr:last-child {
							td.table-title {
								color: #333;
							}
							td {
								font-weight: bold;
								color: $color-primary;
							}
						}
					}
				}

				.boxContent {
					grid-template-columns: repeat(3, 1fr);
					column-gap: 20px;
					row-gap: 20px;

					@media screen and (max-width: 767px) {
						max-height: 475px;
						overflow: hidden;
					}

					.number-input {
						margin: 0 0 8px 0;
					}

					> div {
						overflow: hidden;
					}

					::v-deep .StepBox {
						padding-bottom: 0;
						border-radius: 10px;

						&.active {
							.el-image {
								border-color: $color-primary !important;
							}
						}

						.se {
							.el-image {
								border: 1px solid transparent;
								transition: all 0.2s;
								border-radius: 10px;

								@media (any-hover: hover) {
									&:hover {
										border-color: $color-primary !important;
										box-shadow: 0 3px 4px 0 #ccc;
									}
								}
							}
						}

						.product-price {
							margin-bottom: 5px;
						}
					}

					@media screen and (max-width: 1800px) {
						grid-template-columns: repeat(5, 1fr);
					}

					@media screen and (max-width: 1678px) {
						grid-template-columns: repeat(4, 1fr);
					}

					@media screen and (max-width: 767px) {
						grid-template-columns: repeat(2, 1fr);
						column-gap: 5.5px;
						row-gap: 5px;

						.StepBox {
							background: #f4f5f5;
							border-radius: 5px;
							display: flex;
							align-items: center;
							padding-bottom: 10px;
							height: 100%;

							.se {
								.product-info {
									justify-content: flex-start;
									margin-top: 0;
								}
							}

							::v-deep .product-price {
								margin-bottom: 5px;
							}
						}
					}
				}
				@media screen and (max-width: 767px) {
					.tips {
						margin-bottom: 6px;
						column-gap: 9%;
						.arrow {
							// width: 60px;
							b {
								padding: 5px 0;
								font-size: 16px;
							}
						}
					}
					.quantity-div {
						margin-bottom: 10px;
						.table-price {
							td.table-title {
								padding: 6px 5px;
							}
							td {
								min-width: 48px;
								padding: 6px 5px;
							}
						}
					}
					.StepBox .number-input {
						width: 100px !important;
					}

					.el-input__inner {
						font-size: 13px !important;
					}
				}
			}

			.step-title {
				margin-bottom: 19px;
				font-size: 18px;
				font-weight: bold;
				background: #f9f9f9;
				line-height: 36px;

				@media screen and (max-width: 767px) {
					margin-bottom: 10px;
					font-size: 18px;
					line-height: 32px;
				}

				span {
					display: inline-block;
					width: 36px;
					height: 36px;
					margin-right: 12px;
					background: #333;
					border-radius: 4px;
					font-size: 24px;
					font-weight: 400;
					color: #fff;
					text-align: center;
				}
			}

			.tips {
				margin-bottom: 10px;

				@media screen and (max-width: 767px) {
					margin-bottom: 8.5px;
				}
			}

			.kk {
				// margin-bottom: 30px;

				.part {
					position: relative;
					z-index: 0;
					padding: 20px;
					background: #ffffff;

					@media screen and (max-width: 767px) {
						padding: 20px 7px;
						border-radius: 5px;
					}

					.boxContent {
						display: grid;
						justify-content: space-between;
						grid-template-columns: repeat(3, 1fr);
						column-gap: 13px;
						row-gap: 13px;

						@media screen and (max-width: 767px) {
							column-gap: 5px;
							row-gap: 5px;
						}
					}

					&.Select-Shapes {
						.boxContent {
							grid-template-columns: repeat(5, 1fr);

							@media screen and (max-width: 767px) {
								grid-template-columns: repeat(3, 1fr);
							}

							.StepBox {
								border-color: transparent;
								position: relative;
								border-radius: 8px;

								@media screen and (max-width: 767px) {
									border-radius: 5px;
									border: 1px solid #ebebeb;
								}

								.se {
									.product-info {
										display: none !important;
									}
								}

								// @media screen and (max-width: 767px) {
								// }

								// @media (any-hover: hover) {
								// 	&:hover {
								// 		border-color: $color-primary !important;
								// 	}
								// }
							}

							.StepBox.active ::after {
								content: "\e7b8";
								font-family: "modalicon";
								position: absolute;
								color: #fff;
								top: 0;
								right: 0;
								width: 36px;
								height: 20px;
								font-size: 12px;
								text-align: center;
								line-height: 20px;
								background: $color-primary;
								border-radius: 0px 6px 0px 10px;

								@media screen and (max-width: 767px) {
									width: 22px;
									height: 14px;
									line-height: 14px;
									border-radius: 0px 4px 0px 6px;
								}
							}
						}
					}

					&.Select-Plating {
						.number-input {
							display: none;
						}

						@media screen and (max-width: 767px) {
							.product-info {
								align-items: start;

								.title {
									min-height: 28px;
								}
							}
						}
					}

					&.Select-Engraving {
						.boxContent {
							grid-template-columns: repeat(2, 1fr);
						}
					}

					&.More-Options {
						.boxContent {
							@media screen and (max-width: 767px) {
								grid-template-columns: repeat(2, 1fr);
								row-gap: 5px;
							}

							.StepBox {
								padding-bottom: 11px;

								.product-info {
									padding-bottom: 0;
								}

								.PriceText {
									margin-left: 49px;
									color: $color-primary;

									.normal-text {
										font-size: 14px;
									}

									@media screen and (max-width: 767px) {
										margin-left: 29px;
										margin-bottom: 8px;
									}
								}

								.tip-text {
									color: $color-primary;
									font-size: 14px;
								}
							}
						}
					}

					&.packaging-options {
						.boxContent {
							grid-template-columns: repeat(2, 1fr);

							.StepBox {
								padding-bottom: 11px;

								.product-info {
									padding-bottom: 0;
								}

								.PriceText {
									margin-left: 49px;
									color: $color-primary;

									.normal-text {
										font-size: 14px;
									}

									.tip-text {
										color: $color-primary;
										font-size: 14px;
									}

									@media screen and (max-width: 767px) {
										margin-left: 29px;
										margin-bottom: 8px;
									}
								}
							}
						}
					}

					&.Select-Quantity {
						margin-bottom: 0;
					}

					&.Delivery-Date {
						> div {
							display: flex;
						}

						span.text {
							margin: 0 12px 0 20px;
							color: #9e9e9e;
						}

						.step-item {
							margin-bottom: 12px;

							.item-wrap {
								min-height: auto;
							}
						}

						.step-date {
							padding: 0;

							.step-title {
								display: none;
							}

							.step-item {
								margin-bottom: 12px;
							}

							.step-box {
								// margin-left: 38px;
								// width: 474px;
								grid-template-columns: repeat(3, 1fr);
								column-gap: 14px;

								.top {
									.customCircle1 {
										width: 20px;
										height: 20px;

										&::after {
											width: 10px;
											height: 10px;
											background: #fff;
										}
									}
								}

								.bottom {
									margin-top: 0;
									font-size: 14px;
									color: #9e9e9e;
									margin-left: 30px;
								}
							}
						}

						.el-textarea {
							width: 474px;

							.el-textarea__inner:focus {
								border-color: $color-primary;
							}

							.el-textarea__inner {
								background-color: #f9f9f9;
								height: 130px !important;
								border-radius: 10px;
							}
						}

						@media screen and (max-width: 767px) {
							> div {
								flex-direction: column;
							}

							span.text {
								font-size: 14px;
								margin: 0 0 7px;
							}

							.step-date {
								margin-bottom: 0;
								.step-box {
									width: auto;
									margin-left: 0;
									grid-template-columns: 1fr;
									row-gap: 6px;
									margin-bottom: 3px;
									.bottom {
										margin-left: 17px;
										font-size: 12px;
									}
									.item-wrap {
										.customCircle1 {
											margin-right: 5px;
											width: 12px;
											height: 12px;
											min-width: 12px;
											&::after {
												width: 6px;
												height: 6px;
												min-width: 6px;
											}
										}
									}
								}

								.price-box {
									display: none;
								}

								.step-item {
									margin-bottom: 8px;
								}
							}

							.comments {
								display: block;

								.el-textarea {
									margin-top: 7px;
									width: 100%;

									.el-textarea__inner {
										background-color: #f9f9f9;
										height: 120px !important;
										border-radius: 5px;
									}
								}
							}
						}
					}

					&.step-active {
						position: relative;
						z-index: 100;
						border-radius: 10px;
					}
				}

				&:not(:last-child) {
					.part {
						// margin-bottom: 39px;

						@media screen and (max-width: 767px) {
							margin-bottom: 10px;
						}
					}
				}
			}

			.kk.type1 {
				.part {
					position: static;
				}
			}
		}
		@media screen and (max-width: 767px) {
			display: block;
			min-width: 300px;
			.daohang {
				padding: 8px 0 12px;
				font-size: 14px;
			}
			.header {
				padding: 0;
			}
			.center-content {
				display: block;
				.leftArea {
					padding: 0;
					width: 100%;
					.title-name {
						text-align: left;
					}
					.myswiper2 {
						.swiper-slide {
							.smallImg {
								height: 240px;
							}
						}
						.swiper-button-prev,
						.swiper-button-next {
							width: 35px;
							height: 35px;
						}
					}
				}
			}
		}
	}

	::v-deep .footer {
		display: grid;
		grid-template-columns: 700px;
		justify-content: center;
		padding: 20px;
		background: #eef2f5;

		.footBtn .sub {
			flex-direction: row;
			justify-content: space-between;
			width: 95%;
			align-items: center;
		}

		.topImg {
			display: none;
		}

		.con {
			.viewMore {
				display: none !important;
			}

			.scrollBar {
				overflow: visible;
				max-height: fit-content;
			}
		}

		@media screen and (max-width: 767px) {
			grid-template-columns: 1fr;
			padding: 10px;
			background-color: #e0e0e0;

			.con {
				// .viewMore {
				//     display: block !important;
				// }

				.scrollBar {
					overflow: hidden;
					max-height: 185px;
				}
			}
		}
	}
	.el-icon-close {
		display: flex;
		justify-content: center;
		align-items: center;
		font-weight: 700;
		position: absolute;
		top: 0;
		right: 0;
		transform: translate(50%, -50%);
		width: 40px;
		height: 40px;
		cursor: pointer;
		background: #ffffff;
		box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.3);
		border-radius: 50%;
		z-index: 10;

		@media screen and (max-width: 800px) {
			transform: translate(0, 0);
			box-shadow: none;
		}
	}
	.custom-shadow {
		position: relative;
		background: #fff;
		border: 1px solid #d9d9d9;
		border-radius: 10px;
		padding: 27px 17px;

		&::before {
			content: "";
			position: absolute;
			z-index: -1;
			bottom: 12px;
			left: 5px;
			width: 50%;
			height: 20%;
			box-shadow: 0 14px 7px #d9dbdd;
			transform: rotate(-3deg);
		}

		&::after {
			content: "";
			position: absolute;
			z-index: -1;
			bottom: 12px;
			width: 50%;
			height: 20%;
			box-shadow: 0 14px 7px #d9dbdd;
			right: 5px;
			left: auto;
			transform: rotate(3deg);
		}
	}
}

@media screen and (max-width: 767px) {
	#custom-cufflinks {
		background-color: #f2f5f7;
		font-size: 12px;

		.header {
			display: none;
		}

		.cufflink-quote {
			padding: 0 !important;
		}

		.content {
			width: 100%;
			padding: 0 15px 30px;

			.center-content {
				display: block;
				width: 100%;
			}
		}
	}
}
</style>