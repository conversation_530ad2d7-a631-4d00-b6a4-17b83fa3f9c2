<template>
	<!-- 模板外层的盒子上写theme属性，用来分别写每一套的样式，详见下方style -->
	<div class="banner-box" :class="modal.wrapClass">
		<div style="height: 100%;" v-for="(l, li) in modal.list" :style="modal.style" :key="li">
			<div class="imgWrap" @click="setModalType(l.banner, modal.list, 'banner')" v-if="l.banner && l.banner.value">
				<pic :src="l.banner.value" :alt="l.banner.alt" :title="l.banner.alt"
					:style="{ ...modal.homeImgStyle, ...l.banner.style }" />
			</div>
			<div class="imgWrap" @click="setModalType(l.video, modal.list, 'video')" v-else-if="l.video && l.video.value">
				<video :src="l.video.value" autoplay loop muted playsinline :title="l.video.alt"
					:style="{ ...l.video.style }"></video>
			</div>
			<div class="bps-container">
				<EditDiv v-if="l.title" tagName="h1" v-model:content="l.title.value"
					@click="setModalType(l.title, modal.list, 'text')" />
				<EditDiv v-if="l.subTitle" class="des" v-model:content="l.subTitle.value"
					@click="setModalType(l.subTitle, modal.list, 'text')" />
				<EditDiv v-if="l.text" class="text" v-model:content="l.text.value"
					@click="setModalType(l.text, modal.list, 'text')" />

				<div class="hover-tag" v-if="l.button?.value">
					<button :title="l.button.alt" :style="{ ...modal.btnStyle, ...l.btnStyle }"
						:primary="!l.button.outline && l.button.value" :outline="l.button.outline"
						@click="setModalType(l.button, modal.list, 'button', l.button)">
						<EditDiv tagName="label" v-if="l.button.value" v-model:content="l.button.value" />

						<b class="icon-bps-sanjiao"></b>
					</button>
				</div>
			</div>
		</div>
	</div>
</template>

<script>

export default {
	name: "Banner",
	props: {
		preview: {
			type: Boolean,
			default: false,
		},
		data: {
			type: Object,
			default: {},
		}
	},
	data() {
		return {
			modal: {
				style: {},
				type: { index: 0, clickPosition: 'outer' },
				...this.data
			},
		};
	},
	watch: {
		modal: {
			handler(val) {
				if (process.env.isManage) this.$emit("update:data", val);
			},
			immediate: true,
			deep: true
		}
	},
	methods: {
		setModalType(target, targetArray, clickType, event, other) {
			this.$setModal(this, target, targetArray, clickType, event, other)
		},
	}
};
</script>

<style lang="scss" scoped>
.imgWrap {
	height: 100%;

	img,
	video {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}
}

.icon-bps-sanjiao {
	font-size: 0.7em;
}

.homeBanner {
	position: relative;
	height: 655px;

	.bps-container {
		display: flex;
		color: #ffffff;
		flex-direction: column;
		align-items: flex-start;
		transform: translate(0, -50%);
		position: absolute;
		top: 50%;

		h1 {
			width: 696px;
			font-size: 48px;
			text-shadow: 0px 9px 24px rgba(122, 99, 58, 0.35);
		}

		.des {
			width: 434px;
			margin: 26px 0 50px;
			text-shadow: 0px 9px 24px rgba(122, 99, 58, 0.35);
		}
	}
}

.termsBanner {
	padding-top: 0 !important;
	padding-bottom: 0 !important;
	position: relative;

	// .imgWrap {
	// 	margin: 1em 3vw 0;
	// }

	.bps-container {
		padding: 10px 3.75em;
		transform: translate(0, -50%);
		position: absolute;
		top: 50%;

		h1 {
			font-size: 36px;
		}
	}
}

.policeBanner {
	position: relative;
	height: 420px;

	.bps-container {
		display: flex;
		color: #ffffff;
		flex-direction: column;
		align-items: flex-start;
		transform: translate(0, -50%);
		position: absolute;
		top: 50%;

		.text {
			order: -1;
			margin-bottom: 20px;
			font-size: 24px;
		}

		h1 {
			width: 510px;
			font-size: 48px;
			font-weight: bold;
		}

		.des {
			width: 400px;
			margin: 20px 0 30px;
			text-shadow: 0px 9px 24px rgba(122, 99, 58, 0.35);
		}
	}
}

.aboutBanner {
	height: 26.1458vw;
	position: relative;

	.bps-container {
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		transform: translate(0, -50%);
		position: absolute;
		top: 50%;

		h1 {
			font-size: 2.5vw;
			font-weight: bold;
		}

		.des {
			width: 40%;
			margin: 1.3542vw 0 2.6042vw;
		}
	}
}

.contactBanner {
	position: relative;
	height: 21.875vw;

	.imgWrap {
		height: 100%;

		img {
			width: 100%;
			height: 100%;
			object-fit: cover;
		}
	}

	.bps-container {
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		transform: translate(0, -50%);
		position: absolute;
		top: 50%;

		h1 {
			color: #333333;
			font-size: 2.5vw
		}

		.des {
			width: 434px;
			color: #333;
			margin: .2083vw 0 2.6042vw;
		}
	}
}


@media screen and (min-width: $mb-width) and (max-width: $pad-width) {
	.homeBanner {
		position: relative;
		height: 655px;

		.bps-container {
			h1 {
				width: 522px;
				font-size: 36px;
			}

			.des {
				width: 415px;
				margin: 18px 0 28px;
			}
		}
	}
}


// pc 和 ipad
@media screen and (min-device-width: $mb-width) {
	.des {
		font-size: 1.5em;
	}
}


@media screen and (max-width: $mb-width) {
	.homeBanner {
		height: auto;
		padding: 10px 17px;

		.imgWrap {
			overflow: hidden;
			position: relative;
			height: 225px;
			border-radius: 10px;

			img {
				object-position: right top;
			}
		}

		.bps-container {
			position: static;
			left: auto;
			top: auto;
			transform: none;
			padding: 0;

			h1 {
				width: 100%;
				font-size: 21px;
				text-shadow: none;
				color: #333333;
				margin-top: 30px;
			}

			.des {
				width: auto;
				color: #333333;
				text-shadow: none;
				margin: 20px 0 17px;
			}
		}
	}

	.policeBanner {
		height: auto;
		padding: 10px 17px;

		.imgWrap {
			overflow: hidden;
			position: relative;
			height: 150px;
			border-radius: 10px;

			img {
				object-position: right top;
			}
		}

		.bps-container {
			position: static;
			left: auto;
			top: auto;
			transform: none;
			padding: 0;
			color: #333333;

			.text {
				margin: 15px 0 10px;
				font-size: 12px;
				color: #666666;
			}

			h1 {
				width: 100%;
				font-size: 21px;
				text-shadow: none;
				margin-top: 20px;
			}

			.des {
				width: auto;
				margin: 10px 0;
				color: #666666;
				text-shadow: none;
			}
		}
	}

	.termsBanner {
		height: 4.5em;
		max-width: 100%;
		margin-top: 10px;

		.bps-container h1 {
			font-size: 21px;
		}
	}

	.aboutBanner {
		height: auto;
		padding: 2.9333vw 4.5333vw;

		.imgWrap {
			overflow: hidden;
			position: relative;
			height: 51.6vw;
			border-radius: 1.3333vw;

			img {
				object-position: 75% 100%;
			}
		}

		.bps-container {
			position: static;
			left: auto;
			top: auto;
			transform: none;
			padding: 0;
			width: 100%;

			.bps-button {
				height: 9.0667vw;
				border-radius: .8vw;
				font-size: 3.2vw;

				b.icon-bps-sanjiao {
					margin-left: 1.3333vw;
					font-size: 3.2vw;
				}
			}

			h1 {
				width: 100%;
				font-size: 5.6vw;
				text-shadow: none;
				color: #333333;
				margin-top: 4vw;
			}

			.des {
				width: auto;
				color: #333333;
				text-shadow: none;
				margin: 2.6667vw 0 5.6vw;
			}
		}
	}

	.contactBanner {
		height: auto;
		padding: 10px 17px;

		.imgWrap {
			overflow: hidden;
			position: relative;
			height: 225px;
			border-radius: 10px;

			img {
				object-position: right top;
			}
		}

		.bps-container {
			position: static;
			left: auto;
			top: auto;
			transform: none;
			padding: 0;
			width: 100%;

			h1 {
				width: 100%;
				font-size: 5.6vw;
				text-shadow: none;
				color: #333333;
				margin-top: 4vw;
			}

			.des {
				width: auto;
				color: #333333;
				text-shadow: none;
				margin: 2.6667vw 0 2.2667vw;
			}
		}
	}
}
</style>
