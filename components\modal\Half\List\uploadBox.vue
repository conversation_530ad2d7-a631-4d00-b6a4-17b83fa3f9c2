<template>
  <div class="uploadBox">
    <div class="iconBox" v-show="uploadPic.length == 0" @click="clickUpload">
      <b class="icon-shangchuan uploadIcon"></b>
      <div class="uploadText">Got a design?</div>
      <div class="uploadText2">Upload it to see it on our products</div>
    </div>
    <div class="uploadedPic" v-if="uploadPic.length > 0">
      <pic :src="uploadPic[0].secure_url" alt="uploadedPic"></pic>
    </div>
    <span class="uploadText3">Only you can see the preview</span>
    <div class="uploadBtnBox">
      <div class="uploadBtn" v-if="uploadPic.length == 0" @click="clickUpload">Upload Now</div>
      <div class="removeBtn" v-else @click="delUploadImg">Remove</div>
    </div>
    <input type="file" ref="productUpload1" :accept="acceptFileType" @change="uploadPicFn" />
  </div>
</template>

<script>
import { checkFile } from "@/utils/validate";
import { uploadFile } from "@/utils/oss";
import { generateUUID } from "@/utils/utils"
export default {
  name: 'uploadBox',
  props: {
    uploadPic: {
      type: Array,
      default: () => [],
    },
  },
  components: {},
  data() {
    return {
      acceptFileType: ".JPG,.JPEG,.jpe,.jif,.jfif,.jfi,.GIF,.PNG,.BMP,.WEBP",
    }
  },
  watch: {},
  computed: {},
  methods: {
    infoUpload() {
      this.clickUpload();
      this.$store.commit("setSizeDialog", false);
    },
    clickUpload() {
      this.$refs.productUpload1.click();
    },
    uploadPicFn(event, type = "upload") {
      this.$gl.show();
      let files = type === "upload" ? event.target.files : event;
      let fileResult = checkFile(Array.from(files), this.acceptFileType, 80);
      if (!fileResult) {
        this.$toast.error("File type error");
        this.$gl.hide();
        this.$refs.productUpload1.value = "";
        return false;
      }
      let file = files[0];
      uploadFile(file).then((res) => {
        let temp = {
          id: generateUUID(),
          original_filename: file.name,
          secure_url: res,
          iconUrl: res,
        };
        this.$refs.productUpload1.value = "";
        this.$emit("update:uploadPic", [temp]);
        this.$emit('mergeImgFn', temp.secure_url);
        //存入上传的图片
        this.$store.commit("halfDesign/setMedalsUploadFile", [temp]);
        this.$gl.hide();
      });
    },
    delUploadImg() {
      this.$emit("update:uploadPic", []);
      this.$store.commit("halfDesign/setMedalsUploadFile", []);
      this.$emit('cancelMergeImg', false)
    },
  },
  created() { },
  mounted() {
  },
  beforeDestroy() {
  },
}
</script>

<style scoped lang="scss">
input[type="file"] {
  display: none;
}

.uploadBox {
  min-width: 0;
  position: relative;
  border: 1px solid #f0f0f0;
  border-radius: 2px;
  background-color: #fff;
  transition: all 0.3s;
  overflow: hidden;
  user-select: text;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 4px;

  @media (any-hover: hover) {
    &:hover {
      --hoverBack: #e6e6e6;
      box-shadow: 0 6px 5px var(--hoverBack), 0 -1px 3px var(--hoverBack);
    }
  }

  b {
    font-size: 40px;
    color: #E86F6F;
  }

  .iconBox {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  .uploadedPic {
    width: 50%;
    aspect-ratio: 1/1;
    border-radius: 50%;
    overflow: hidden;
    margin-bottom: 1em;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(0, 0, 0, 0.4);

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .uploadBtnBox {
    margin-top: 1em;
    font-size: 14px;

    .uploadBtn,
    .removeBtn {
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      background-color: #AE1E1E;
      border: 1px solid #AE1E1E;
      border-radius: 10px;
      padding: 0.8em 4em;
      cursor: pointer;
    }

    .removeBtn {
      color: #AE1E1E;
      background-color: #fff;
    }
  }

  .uploadText {
    font-weight: bold;
    font-size: 16px;
  }

  .uploadText2 {
    font-size: 14px;
  }

  .uploadText3 {
    font-size: 12px;
    color: #666666;
  }



  @include respond-to(mb) {
    padding: 10px 0;

    .uploadedPic {
      width: 30%;
      margin-bottom: 0.5em;
    }

  }
}
</style>
