import { request } from "@/utils/request";

// 根据字体priceInfo的id获取尺寸价格列表
const getNeonFontSizePrice = function (data) {
    return request({
        url: '/quote/quoteCalculate/getNeonFontSizePrice',
        method: 'post',
        data: data,
    })
};

// 获取下一步不可选参数
const noChoiceParam = function (data) {
    return request({
        url: '/quote/noParam/noChoiceParam',
        method: 'post',
        data: data,
    })
};
//报价计算
const calculate = function (data) {
    return request({
        url: '/quote/quoteCalculate/calculate',
        method: 'post',
        data: data,
    })
};
//查询所有不可选参数列表
const getAll = function (data) {
    return request({
        url: '/quote/noParam/getAll',
        method: 'get',
        params: data,
    })
}
//分页获取neon模板
const getTemplates = function (data) {
    return request({
        url: '/quote/templates/getTemplates',
        method: 'post',
        data: data,
    })
}

//获取所有参数类型列表
const getAppParamType = function (data) {
    return request({
        url: '/quote/templates/getAppParamType',
        method: 'get',
        params: data,
    })
}

//获取neon推荐模板
const getNeonRecommendTemplate = function (data) {
    return request({
        url: '/quote/templates/getNeonRecommendTemplate',
        method: 'get',
        params: data,
    })
}

//获取neonAI预测模型
const neonPrediction = function (data) {
    return request({
        url: '/image/neonPrediction',
        method: 'post',
        data: data
    })
}

//保存用户的creation
const addUserCreation = function (data) {
    return request({
        url: '/app/designUser/addUserCreation',
        method: 'post', 
        data: data
    })
}

//获取用户的creation
const getUserCreation = function (data) {
    return request({
        url: '/app/designUser/getUserCreation',
        params: data
    })
}

//删除用户的creation
const delUserCreation = function (data) {
    return request({
        url: '/app/designUser/delById',
        params: data
    })
}

//获取neon gallery页面tag 列表
export function getTagsList(data) {
	return request({
		url: "/quote/gallery/appImages/getTagsList",
		method: "get",
		params: data,
	});
}

//获取neon gallery页面 图片列表
export function getImagesList(data) {
	return request({
		url: "/quote/gallery/appImages/getImagesList",
		method: "post",
		data: data,
	});
}




export const neonApi = {
    getNeonFontSizePrice,
    noChoiceParam,
    calculate,
    getAll,
    getTemplates,
    getAppParamType,
    getNeonRecommendTemplate,
    neonPrediction,
    addUserCreation,
    getUserCreation,
    delUserCreation
};
