const { resolve } = require("path");
export default [
	//新袖扣报价
	{
		path: "/:lang?/quote/custom-3d-cufflinks",
		name: "lang-quote-custom-3d-cufflinks",
		component: resolve(__dirname, "../pages/_lang/quote/_cufflinks"),
	},
	{
		path: "/:lang?/quote/custom-cutout-cufflinks",
		name: "lang-quote-custom-cutout-cufflinks",
		component: resolve(__dirname, "../pages/_lang/quote/_cufflinks"),
	},
	{
		path: "/:lang?/quote/custom-engraved-cufflinks",
		name: "lang-quote-custom-engraved-cufflinks",
		component: resolve(__dirname, "../pages/_lang/quote/_cufflinks"),
	},
	{
		path: "/:lang?/quote/custom-hard-enamel-cufflinks",
		name: "lang-quote-custom-hard-enamel-cufflinks",
		component: resolve(__dirname, "../pages/_lang/quote/_cufflinks"),
	},
	{
		path: "/:lang?/quote/custom-picture-cufflinks",
		name: "lang-quote-custom-picture-cufflinks",
		component: resolve(__dirname, "../pages/_lang/quote/_cufflinks"),
	},
	{
		path: "/:lang?/quote/custom-soft-enamel-cufflinks",
		name: "lang-quote-custom-soft-enamel-cufflinks",
		component: resolve(__dirname, "../pages/_lang/quote/_cufflinks"),
	},
	//新织带报价
	{
		path: "/:lang?/quote/two-tone-lanyards",
		name: "lang-quote-two-tone-lanyards",
		component: resolve(__dirname, "../pages/_lang/quote/_lanyardQuote"),
	},
	{
		path: "/:lang?/quote/light-up-lanyards",
		name: "lang-quote-light-up-lanyards",
		component: resolve(__dirname, "../pages/_lang/quote/_lanyardQuote"),
	},
	{
		path: "/:lang?/quote/glitter-lanyards",
		name: "lang-quote-glitter-lanyards",
		component: resolve(__dirname, "../pages/_lang/quote/_lanyardQuote"),
	},
	{
		path: "/:lang?/quote/3d-silicone-printing-lanyards",
		name: "lang-quote-3d-silicone-printing-lanyards",
		component: resolve(__dirname, "../pages/_lang/quote/_lanyardQuote"),
	},
	{
		path: "/:lang?/quote/dye-sublimated-lanyards",
		name: "lang-quote-dye-sublimated-lanyards",
		component: resolve(__dirname, "../pages/_lang/quote/_lanyardQuote"),
	},
	{
		path: "/:lang?/quote/nylon-lanyards",
		name: "lang-quote-nylon-lanyards",
		component: resolve(__dirname, "../pages/_lang/quote/_lanyardQuote"),
	},
	{
		path: "/:lang?/quote/tubular-lanyards",
		name: "lang-quote-tubular-lanyards",
		component: resolve(__dirname, "../pages/_lang/quote/_lanyardQuote"),
	},
	{
		path: "/:lang?/quote/polyester-lanyards",
		name: "lang-quote-polyester-lanyards",
		component: resolve(__dirname, "../pages/_lang/quote/_lanyardQuote"),
	},
	{
		path: "/:lang?/quote/woven-lanyards",
		name: "lang-quote-woven-lanyards",
		component: resolve(__dirname, "../pages/_lang/quote/_lanyardQuote"),
	},
	{
		path: "/:lang?/quote/blank-lanyards",
		name: "lang-quote-blank-lanyards",
		component: resolve(__dirname, "../pages/_lang/quote/_lanyardQuote"),
	},
	{
		path: "/:lang?/quote/short-wrist-lanyards",
		name: "lang-quote-short-wrist-lanyards",
		component: resolve(__dirname, "../pages/_lang/quote/_lanyardQuote"),
	},
	{
		path: "/:lang?/quote/cord-lanyards",
		name: "lang-quote-cord-lanyards",
		component: resolve(__dirname, "../pages/_lang/quote/_lanyardQuote"),
	},
	{
		path: "/:lang?/quote/reflective-lanyards",
		name: "lang-quote-reflective-lanyards",
		component: resolve(__dirname, "../pages/_lang/quote/_lanyardQuote"),
	},
	{
		path: "/:lang?/quote/neoprene-lanyards",
		name: "lang-quote-neoprene-lanyards",
		component: resolve(__dirname, "../pages/_lang/quote/_lanyardQuote"),
	},
	{
		path: "/:lang?/quote/phone-lanyards",
		name: "lang-quote-phone-lanyards",
		component: resolve(__dirname, "../pages/_lang/quote/_lanyardQuote"),
	},
	//checkout新链接
	{
		path: "/:lang?/checkout/:oid",
		name: "lang-checkout-oid",
		component: resolve(__dirname, "../pages/_lang/checkout.vue"),
	},
	{
		path: "/:lang?/Exhibition",
		name: "lang-Exhibition",
		component: resolve(__dirname, "../pages/_lang/enamelpinsProductList/index.vue"),
	},
	{
		path: "/:lang?/challenge-coin-gallery",
		name: "lang-challenge-coin-gallery",
		component: resolve(__dirname, "../pages/_lang/enamelcoinsProductList/index.vue"),
	},
	{
		path: "/:lang?/s/:cateName",
		name: "lang-s-cateName",
		component: resolve(__dirname, "../pages/_lang/enamelpinsProductList/index.vue"),
	},
	{
		path: "/:lang?/design/custom-neon-signs/:templateName?",
		name: "lang-design-designName-customNeonSigns",
		component: resolve(__dirname, "../pages/_lang/design/custom-neon-signs"),
	},
	{
		path: "/:lang?/design/:designName?/:templateName?",
		name: "lang-design-designName-templateName",
		component: resolve(__dirname, "../pages/_lang/design/_systemName"),
	},
	{
		path: "/:lang?/quote/round-flag-medal",
		name: "lang-quote-round-flag-medal",
		component: resolve(__dirname, "../pages/_lang/quote/_quoteName"),
	},
	{
		path: "/:lang?/quote/round-wheat-medal",
		name: "lang-quote-round-wheat-medal",
		component: resolve(__dirname, "../pages/_lang/quote/_quoteName"),
	},
	{
		path: "/:lang?/quote/star-cut-out-medal",
		name: "lang-quote-star-cut-out-medal",
		component: resolve(__dirname, "../pages/_lang/quote/_quoteName"),
	},
	{
		path: "/:lang?/quote/shield-flag-medal",
		name: "lang-quote-shield-flag-medal",
		component: resolve(__dirname, "../pages/_lang/quote/_quoteName"),
	},
	{
		path: "/:lang?/quote/round-metal-medal",
		name: "lang-quote-round-metal-medal",
		component: resolve(__dirname, "../pages/_lang/quote/_quoteName"),
	},
	{
		path: "/:lang?/quote/round-wreath-medal",
		name: "lang-quote-round-wreath-medal",
		component: resolve(__dirname, "../pages/_lang/quote/_quoteName"),
	},
	{
		path: "/:lang?/quote/wreath-metal-medal",
		name: "lang-quote-wreath-metal-medal",
		component: resolve(__dirname, "../pages/_lang/quote/_quoteName"),
	},
	{
		path: "/:lang?/quote/star-cluster-medal",
		name: "lang-quote-star-cluster-medal",
		component: resolve(__dirname, "../pages/_lang/quote/_quoteName"),
	},
	{
		path: "/:lang?/quote/pb-0020-g",
		name: "lang-quote-pb-0020-g",
		component: resolve(__dirname, "../pages/_lang/quote/_quoteName2"),
	},
	{
		path: "/:lang?/quote/pb-0021-g",
		name: "lang-quote-pb-0021-g",
		component: resolve(__dirname, "../pages/_lang/quote/_quoteName2"),
	},
	{
		path: "/:lang?/quote/st-0089-g",
		name: "lang-quote-st-0089-g",
		component: resolve(__dirname, "../pages/_lang/quote/_quoteName2"),
	},
	{
		path: "/:lang?/quote/st-0090-g",
		name: "lang-quote-st-0090-g",
		component: resolve(__dirname, "../pages/_lang/quote/_quoteName2"),
	},
	{
		path: "/:lang?/quote/st-0091-g",
		name: "lang-quote-st-0091-g",
		component: resolve(__dirname, "../pages/_lang/quote/_quoteName2"),
	},
	{
		path: "/:lang?/quote/st-0092-g",
		name: "lang-quote-st-0092-g",
		component: resolve(__dirname, "../pages/_lang/quote/_quoteName2"),
	},
	{
		path: "/:lang?/quote/st-0093-g",
		name: "lang-quote-st-0093-g",
		component: resolve(__dirname, "../pages/_lang/quote/_quoteName2"),
	},
	{
		path: "/:lang?/quote/st-0094-g",
		name: "lang-quote-st-0094-g",
		component: resolve(__dirname, "../pages/_lang/quote/_quoteName2"),
	},
	{
		path: "/:lang?/quote/custom-lake-loons-metal-sign",
		name: "lang-quote-custom-lake-loons-metal-sign",
		component: resolve(__dirname, "../pages/_lang/quote/_metalSigns"),
	},
	{
		path: "/:lang?/quote/personalized-metal-fire-pit-sign-camping-decor",
		name: "lang-quote-personalized-metal-fire-pit-sign-camping-decor",
		component: resolve(__dirname, "../pages/_lang/quote/_metalSigns"),
	},
	{
		path: "/:lang?/quote/metal-camping-sign-fire-pit-sign",
		name: "lang-quote-metal-camping-sign-fire-pit-sign",
		component: resolve(__dirname, "../pages/_lang/quote/_metalSigns"),
	},
	{
		path: "/:lang?/quote/custom-fly-fishing-outdoor-sign",
		name: "lang-quote-custom-fly-fishing-outdoor-sign",
		component: resolve(__dirname, "../pages/_lang/quote/_metalSigns"),
	},
	{
		path: "/:lang?/quote/personalized-deer-metal-cabin-sign-outdoor",
		name: "lang-quote-personalized-deer-metal-cabin-sign-outdoor",
		component: resolve(__dirname, "../pages/_lang/quote/_metalSigns"),
	},
	{
		path: "/:lang?/quote/personalized-bear-metal-sign",
		name: "lang-quote-personalized-bear-metal-sign",
		component: resolve(__dirname, "../pages/_lang/quote/_metalSigns"),
	},
	{
		path: "/:lang?/quote/custom-metal-cow-farm-sign",
		name: "lang-quote-custom-metal-cow-farm-sign",
		component: resolve(__dirname, "../pages/_lang/quote/_metalSigns"),
	},
	{
		path: "/:lang?/quote/custom-metal-outdoor-palm-tree-hammock-sign",
		name: "lang-quote-custom-metal-outdoor-palm-tree-hammock-sign",
		component: resolve(__dirname, "../pages/_lang/quote/_metalSigns"),
	},
	{
		path: "/:lang?/quote/custom-cut-metal-sign",
		name: "lang-quote-custom-cut-metal-sign",
		component: resolve(__dirname, "../pages/_lang/quote/_metalSigns"),
	},
	{
		path: "/:lang?/quote/custom-metal-business-logo-or-artwork",
		name: "lang-quote-custom-metal-business-logo-or-artwork",
		component: resolve(__dirname, "../pages/_lang/quote/_metalSigns"),
	},
	{
		path: "/:lang?/quote/metal-hair-stylist-sign",
		name: "lang-quote-metal-hair-stylist-sign",
		component: resolve(__dirname, "../pages/_lang/quote/_metalSigns"),
	},
	{
		path: "/:lang?/quote/custom-metal-workshop-garage-sign",
		name: "lang-quote-custom-metal-workshop-garage-sign",
		component: resolve(__dirname, "../pages/_lang/quote/_metalSigns"),
	},
	{
		path: "/:lang?/quote/metal-garage-wall-decor",
		name: "lang-quote-metal-garage-wall-decor",
		component: resolve(__dirname, "../pages/_lang/quote/_metalSigns"),
	},
	{
		path: "/:lang?/quote/luxury-personalized-gym-metal-sign",
		name: "lang-quote-luxury-personalized-gym-metal-sign",
		component: resolve(__dirname, "../pages/_lang/quote/_metalSigns"),
	},
	{
		path: "/:lang?/quote/coffee-bar-decor-sign",
		name: "lang-quote-coffee-bar-decor-sign",
		component: resolve(__dirname, "../pages/_lang/quote/_metalSigns"),
	},
	{
		path: "/:lang?/quote/custom-coffee-metal-wall-art-sign",
		name: "lang-quote-custom-coffee-metal-wall-art-sign",
		component: resolve(__dirname, "../pages/_lang/quote/_metalSigns"),
	},
	{
		path: "/:lang?/quote/personalized-address-number-yard-metal-sign",
		name: "lang-quote-personalized-address-number-yard-metal-sign",
		component: resolve(__dirname, "../pages/_lang/quote/_metalSigns"),
	},
	{
		path: "/:lang?/quote/round-personalized-outdoor-metal-address-sign",
		name: "lang-quote-round-personalized-outdoor-metal-address-sign",
		component: resolve(__dirname, "../pages/_lang/quote/_metalSigns"),
	},
	{
		path: "/:lang?/quote/custom-address-metal-sign",
		name: "lang-quote-custom-address-metal-sign",
		component: resolve(__dirname, "../pages/_lang/quote/_metalSigns"),
	},
	{
		path: "/:lang?/quote/customized-eagle-address-signs",
		name: "lang-quote-customized-eagle-address-signs",
		component: resolve(__dirname, "../pages/_lang/quote/_metalSigns"),
	},
	{
		path: "/:lang?/quote/deer-address-sign-with-text",
		name: "lang-quote-deer-address-sign-with-text",
		component: resolve(__dirname, "../pages/_lang/quote/_metalSigns"),
	},
	{
		path: "/:lang?/quote/california-style-address-metal-sign",
		name: "lang-quote-california-style-address-metal-sign",
		component: resolve(__dirname, "../pages/_lang/quote/_metalSigns"),
	},
	{
		path: "/:lang?/quote/squirrel-personalized-metal-sign",
		name: "lang-quote-squirrel-personalized-metal-sign",
		component: resolve(__dirname, "../pages/_lang/quote/_metalSigns"),
	},
	{
		path: "/:lang?/quote/dachshund-personalized-metal-address-sign",
		name: "lang-quote-dachshund-personalized-metal-address-sign",
		component: resolve(__dirname, "../pages/_lang/quote/_metalSigns"),
	},
	{
		path: "/:lang?/quote/personalized-tree-of-life-metal-sign",
		name: "lang-quote-personalized-tree-of-life-metal-sign",
		component: resolve(__dirname, "../pages/_lang/quote/_metalSigns"),
	},
	{
		path: "/:lang?/quote/custom-motorcycle-metal-wall-art",
		name: "lang-quote-custom-motorcycle-metal-wall-art",
		component: resolve(__dirname, "../pages/_lang/quote/_metalSigns"),
	},
	{
		path: "/:lang?/quote/personalized-welcome-sign-for-front-porch",
		name: "lang-quote-personalized-welcome-sign-for-front-porch",
		component: resolve(__dirname, "../pages/_lang/quote/_metalSigns"),
	},
	{
		path: "/:lang?/quote/-office-entry-metal-wall-art",
		name: "lang-quote--office-entry-metal-wall-art",
		component: resolve(__dirname, "../pages/_lang/quote/_metalSigns"),
	},
	{
		path: "/:lang?/quote/custom-kitchen-metal-sign",
		name: "lang-quote-custom-kitchen-metal-sign",
		component: resolve(__dirname, "../pages/_lang/quote/_metalSigns"),
	},
	{
		path: "/:lang?/quote/-kitchen-sign-wall-decor",
		name: "lang-quote--kitchen-sign-wall-decor",
		component: resolve(__dirname, "../pages/_lang/quote/_metalSigns"),
	},
	{
		path: "/:lang?/quote/personalized-deer-metal-cabin-sign",
		name: "lang-quote-personalized-deer-metal-cabin-sign",
		component: resolve(__dirname, "../pages/_lang/quote/_metalSigns"),
	},
	{
		path: "/:lang?/quote/tree-of-life-with-personalized-family-name-sign",
		name: "lang-quote-tree-of-life-with-personalized-family-name-sign",
		component: resolve(__dirname, "../pages/_lang/quote/_metalSigns"),
	},
	{
		path: "/:lang?/quote/unique-round-ornate-split-letter-name-sign",
		name: "lang-quote-unique-round-ornate-split-letter-name-sign",
		component: resolve(__dirname, "../pages/_lang/quote/_metalSigns"),
	},
	{
		path: "/:lang?/quote/couples-wedding-gifts-metal-sign",
		name: "lang-quote-couples-wedding-gifts-metal-sign",
		component: resolve(__dirname, "../pages/_lang/quote/_metalSigns"),
	},
	{
		path: "/:lang?/quote/split-monogram-sign",
		name: "lang-quote-split-monogram-sign",
		component: resolve(__dirname, "../pages/_lang/quote/_metalSigns"),
	},
	{
		path: "/:lang?/quote/custom-dog-with-bone-name",
		name: "lang-quote-custom-dog-with-bone-name",
		component: resolve(__dirname, "../pages/_lang/quote/_metalSigns"),
	},
	{
		path: "/:lang?/quote/cats-distracted-custom-metal-sign",
		name: "lang-quote-cats-distracted-custom-metal-sign",
		component: resolve(__dirname, "../pages/_lang/quote/_metalSigns"),
	},
	{
		path: "/:lang?/quote/eagle-custom-sign",
		name: "lang-quote-eagle-custom-sign",
		component: resolve(__dirname, "../pages/_lang/quote/_metalSigns"),
	},
	{
		path: "/:lang?/quote/custom-last-name-sign",
		name: "lang-quote-custom-last-name-sign",
		component: resolve(__dirname, "../pages/_lang/quote/_metalSigns"),
	},
	{
		path: "/:lang?/quote/customized-metal-dog-art-signage",
		name: "lang-quote-customized-metal-dog-art-signage",
		component: resolve(__dirname, "../pages/_lang/quote/_metalSigns"),
	},
	{
		path: "/:lang?/quote/upload-your-artwork",
		name: "lang-quote-upload-your-artwork",
		component: resolve(__dirname, "../pages/_lang/quote/_metalSigns"),
	},
	{
		path: "/:lang?/quote/1st-place-super-cup-soccer-medals",
		name: "lang-quote-1st-place-super-cup-soccer-medals",
		component: resolve(__dirname, "../pages/_lang/quote/_semiMedals"),
	},
	{
		path: "/:lang?/quote/champion-custom-award-medals",
		name: "lang-quote-champion-custom-award-medals",
		component: resolve(__dirname, "../pages/_lang/quote/_semiMedals"),
	},
	{
		path: "/:lang?/quote/glitter-award-medals",
		name: "lang-quote-glitter-award-medals",
		component: resolve(__dirname, "../pages/_lang/quote/_semiMedals"),
	},
	{
		path: "/:lang?/quote/glow-in-the-dark-5k-medals",
		name: "lang-quote-glow-in-the-dark-5k-medals",
		component: resolve(__dirname, "../pages/_lang/quote/_semiMedals"),
	},
	{
		path: "/:lang?/quote/glitter-5k-model-medals",
		name: "lang-quote-glitter-5k-model-medals",
		component: resolve(__dirname, "../pages/_lang/quote/_semiMedals"),
	},
	{
		path: "/:lang?/quote/trefoil-walking-race-5k-medals",
		name: "lang-quote-trefoil-walking-race-5k-medals",
		component: resolve(__dirname, "../pages/_lang/quote/_semiMedals"),
	},
	{
		path: "/:lang?/quote/personalized-rhinestone-5k-medals",
		name: "lang-quote-personalized-rhinestone-5k-medals",
		component: resolve(__dirname, "../pages/_lang/quote/_semiMedals"),
	},
	{
		path: "/:lang?/quote/3d-soccer-custom-medals",
		name: "lang-quote-3d-soccer-custom-medals",
		component: resolve(__dirname, "../pages/_lang/quote/_semiMedals"),
	},
	{
		path: "/:lang?/quote/hexagon-sports-custom-spinner-medals",
		name: "lang-quote-hexagon-sports-custom-spinner-medals",
		component: resolve(__dirname, "../pages/_lang/quote/_semiMedals"),
	},
	{
		path: "/:lang?/quote/glitter-star-openwork-finisher-medals",
		name: "lang-quote-glitter-star-openwork-finisher-medals",
		component: resolve(__dirname, "../pages/_lang/quote/_semiMedals"),
	},
	{
		path: "/:lang?/quote/customized-america-sports-medals",
		name: "lang-quote-customized-america-sports-medals",
		component: resolve(__dirname, "../pages/_lang/quote/_semiMedals"),
	},
	{
		path: "/:lang?/quote/custom-competitive-gear-medals",
		name: "lang-quote-custom-competitive-gear-medals",
		component: resolve(__dirname, "../pages/_lang/quote/_semiMedals"),
	},
	{
		path: "/:lang?/quote/custom-basketball-3d-medals",
		name: "lang-quote-custom-basketball-3d-medals",
		component: resolve(__dirname, "../pages/_lang/quote/_semiMedals"),
	},
	{
		path: "/:lang?/quote/unique-custom-3d-soccer-medals",
		name: "lang-quote-unique-custom-3d-soccer-medals",
		component: resolve(__dirname, "../pages/_lang/quote/_semiMedals"),
	},
	{
		path: "/:lang?/quote/star-custom-rhinestone-soccer-medals",
		name: "lang-quote-star-custom-rhinestone-soccer-medals",
		component: resolve(__dirname, "../pages/_lang/quote/_semiMedals"),
	},
	{
		path: "/:lang?/quote/dazzling-polished-custom-soccer-medal",
		name: "lang-quote-dazzling-polished-custom-soccer-medal",
		component: resolve(__dirname, "../pages/_lang/quote/_semiMedals"),
	},
	{
		path: "/:lang?/quote/custom-trail-running-medals",
		name: "lang-quote-custom-trail-running-medals",
		component: resolve(__dirname, "../pages/_lang/quote/_semiMedals"),
	},
	{
		path: "/:lang?/quote/flag-round-running-medals",
		name: "lang-quote-flag-round-running-medals",
		component: resolve(__dirname, "../pages/_lang/quote/_semiMedals"),
	},
	{
		path: "/:lang?/quote/petal-shaped-running-medals",
		name: "lang-quote-petal-shaped-running-medals",
		component: resolve(__dirname, "../pages/_lang/quote/_semiMedals"),
	},
	{
		path: "/:lang?/quote/custom-soccer-club-medals",
		name: "lang-quote-custom-soccer-club-medals",
		component: resolve(__dirname, "../pages/_lang/quote/_semiMedals"),
	},
	{
		path: "/:lang?/quote/octagonal-customized-soccer-medals",
		name: "lang-quote-octagonal-customized-soccer-medals",
		component: resolve(__dirname, "../pages/_lang/quote/_semiMedals"),
	},
	{
		path: "/:lang?/quote/stars-soccer-game-custom-medals",
		name: "lang-quote-stars-soccer-game-custom-medals",
		component: resolve(__dirname, "../pages/_lang/quote/_semiMedals"),
	},
	{
		path: "/:lang?/quote/trail-running-custom-medals",
		name: "lang-quote-trail-running-custom-medals",
		component: resolve(__dirname, "../pages/_lang/quote/_semiMedals"),
	},
	{
		path: "/:lang?/quote/shield-flag-volleyball-medals",
		name: "lang-quote-shield-flag-volleyball-medals",
		component: resolve(__dirname, "../pages/_lang/quote/_semiMedals"),
	},
	{
		path: "/:lang?/quote/star-cluster-after-school-soccer-medals",
		name: "lang-quote-star-cluster-after-school-soccer-medals",
		component: resolve(__dirname, "../pages/_lang/quote/_semiMedals"),
	},
	{
		path: "/:lang?/quote/wreath-award-metal-medals",
		name: "lang-quote-wreath-award-metal-medals",
		component: resolve(__dirname, "../pages/_lang/quote/_semiMedals"),
	},
	{
		path: "/:lang?/quote/award-3d-leaf-edge-custom-medals",
		name: "lang-quote-award-3d-leaf-edge-custom-medals",
		component: resolve(__dirname, "../pages/_lang/quote/_semiMedals"),
	},
	{
		path: "/:lang?/quote/custom-uv-printed-round-medals",
		name: "lang-quote-custom-uv-printed-round-medals",
		component: resolve(__dirname, "../pages/_lang/quote/_semiMedals"),
	},
	{
		path: "/:lang?/quote/custom-round-wheat-medals",
		name: "lang-quote-custom-round-wheat-medals",
		component: resolve(__dirname, "../pages/_lang/quote/_semiMedals"),
	},
	{
		path: "/:lang?/quote/custom-star-cut-out-medal",
		name: "lang-quote-custom-star-cut-out-medal",
		component: resolve(__dirname, "../pages/_lang/quote/_semiMedals"),
	},
	{
		path: "/:lang?/quote/custom-halloween-run-glitter-medal",
		name: "lang-quote-custom-halloween-run-glitter-medal",
		component: resolve(__dirname, "../pages/_lang/quote/_semiMedals"),
	},
	{
		path: "/:lang?/quote/custom-round-uv-printed-medals",
		name: "lang-quote-custom-round-uv-printed-medals",
		component: resolve(__dirname, "../pages/_lang/quote/_semiMedals"),
	},
	{
		path: "/:lang?/quote/halloween-custom-spinner-running-medal",
		name: "lang-quote-halloween-custom-spinner-running-medal",
		component: resolve(__dirname, "../pages/_lang/quote/_semiMedals"),
	},
	{
		path: "/:lang?/quote/first-place-star-polygon-medals",
		name: "lang-quote-first-place-star-polygon-medals",
		component: resolve(__dirname, "../pages/_lang/quote/_semiMedals"),
	},
	{
		path: "/:lang?/quote/torch-banner-with-ring-medal",
		name: "lang-quote-torch-banner-with-ring-medal",
		component: resolve(__dirname, "../pages/_lang/quote/_semiMedals"),
	},
	{
		path: "/:lang?/quote/torch-branch-honours-graduation-medal",
		name: "lang-quote-torch-branch-honours-graduation-medal",
		component: resolve(__dirname, "../pages/_lang/quote/_semiMedals"),
	},
	{
		path: "/:lang?/quote/science-shield-honour-medal",
		name: "lang-quote-science-shield-honour-medal",
		component: resolve(__dirname, "../pages/_lang/quote/_semiMedals"),
	},
	{
		path: "/:lang?/quote/book-banner-medal",
		name: "lang-quote-book-banner-medal",
		component: resolve(__dirname, "../pages/_lang/quote/_semiMedals"),
	},
	{
		path: "/:lang?/quote/star-book-rush-medal",
		name: "lang-quote-star-book-rush-medal",
		component: resolve(__dirname, "../pages/_lang/quote/_semiMedals"),
	},
	{
		path: "/:lang?/quote/rotating-honours-graduation-medal",
		name: "lang-quote-rotating-honours-graduation-medal",
		component: resolve(__dirname, "../pages/_lang/quote/_semiMedals"),
	},
	{
		path: "/:lang?/quote/ranches-stars-universal-medal",
		name: "lang-quote-ranches-stars-universal-medal",
		component: resolve(__dirname, "../pages/_lang/quote/_semiMedals"),
	},
	{
		path: "/:lang?/quote/1st-rhinestone-star-torch-medal",
		name: "lang-quote-1st-rhinestone-star-torch-medal",
		component: resolve(__dirname, "../pages/_lang/quote/_semiMedals"),
	},
	{
		path: "/:lang?/quote/ten-stars-first-place-medal",
		name: "lang-quote-ten-stars-first-place-medal",
		component: resolve(__dirname, "../pages/_lang/quote/_semiMedals"),
	},
	{
		path: "/:lang?/quote/star-leaf-trimmed-1st-place-medal",
		name: "lang-quote-star-leaf-trimmed-1st-place-medal",
		component: resolve(__dirname, "../pages/_lang/quote/_semiMedals"),
	},
	{
		path: "/:lang?/quote/2nd-place-super-basketball-medals",
		name: "lang-quote-2nd-place-super-basketball-medals",
		component: resolve(__dirname, "../pages/_lang/quote/_semiMedals"),
	},
	{
		path: "/:lang?/quote/3rd-place-super-baseball-medal",
		name: "lang-quote-3rd-place-super-baseball-medal",
		component: resolve(__dirname, "../pages/_lang/quote/_semiMedals"),
	},

	// 金属卡报价
	{
		path: "/:lang?/quote/standard-stainless-steel-business-cards",
		name: "lang-quote-standard-stainless-steel-business-cards",
		component: resolve(__dirname, "../pages/_lang/quote/_businessCards"),
	},
	{
		path: "/:lang?/quote/standard-black-metal-business-cards",
		name: "lang-quote-standard-black-metal-business-cards",
		component: resolve(__dirname, "../pages/_lang/quote/_businessCards"),
	},
	{
		path: "/:lang?/quote/standard-gold-metal-business-cards",
		name: "lang-quote-standard-gold-metal-business-cards",
		component: resolve(__dirname, "../pages/_lang/quote/_businessCards"),
	},
	{
		path: "/:lang?/quote/standard-rose-gold-metal-business-cards",
		name: "lang-quote-standard-rose-gold-metal-business-cards",
		component: resolve(__dirname, "../pages/_lang/quote/_businessCards"),
	},
	{
		path: "/:lang?/quote/metal-nfc-business-cards",
		name: "lang-quote-metal-nfc-business-cards",
		component: resolve(__dirname, "../pages/_lang/quote/_businessCards"),
	},
	{
		path: "/:lang?/quote/square-business-cards",
		name: "lang-quote-square-business-cards",
		component: resolve(__dirname, "../pages/_lang/quote/_businessCards"),
	},
	{
		path: "/:lang?/quote/mini-business-cards",
		name: "lang-quote-mini-business-cards",
		component: resolve(__dirname, "../pages/_lang/quote/_businessCards"),
	},
	{
		path: "/:lang?/quote/metal-bottle-opener-business-cards",
		name: "lang-quote-metal-bottle-opener-business-cards",
		component: resolve(__dirname, "../pages/_lang/quote/_businessCards"),
	},
	{
		path: "/:lang?/quote/grinder-business-cards",
		name: "lang-quote-grinder-business-cards",
		component: resolve(__dirname, "../pages/_lang/quote/_businessCards"),
	},
	{
		path: "/:lang?/quote/standard-copper-metal-business-cards",
		name: "lang-quote-standard-copper-metal-business-cards",
		component: resolve(__dirname, "../pages/_lang/quote/_businessCards"),
	},

	// coins报价
	{
		path: "/:lang?/quote/coins-quote",
		name: "lang-quote-coins-quote",
		component: resolve(__dirname, "../pages/_lang/quote/_coinsQuote"),
	},
	{
		path: "/:lang?/quote/uv-printed-3D-challenge-coins",
		name: "lang-quote-uv-printed-3D-challenge-coins",
		component: resolve(__dirname, "../pages/_lang/quote/_coinsQuote"),
	},
	{
		path: "/:lang?/quote/bottle-opener-coins",
		name: "lang-quote-bottle-opener-coins",
		component: resolve(__dirname, "../pages/_lang/quote/_coinsQuote"),
	},
	{
		path: "/:lang?/quote/spinner-coins",
		name: "lang-quote-spinner-coins",
		component: resolve(__dirname, "../pages/_lang/quote/_coinsQuote"),
	},
	{
		path: "/:lang?/quote/single-sided-challenge-coins",
		name: "lang-quote-single-sided-challenge-coins",
		component: resolve(__dirname, "../pages/_lang/quote/_coinsQuote"),
	},
	{
		path: "/:lang?/quote/3D-coins",
		name: "lang-quote-3D-coins",
		component: resolve(__dirname, "../pages/_lang/quote/_coinsQuote"),
	},
	{
		path: "/:lang?/quote/mint-coins",
		name: "lang-quote-mint-coins",
		component: resolve(__dirname, "../pages/_lang/quote/_coinsQuote"),
	},
	{
		path: "/:lang?/quote/mint-coins1",
		name: "lang-quote-mint-coins1",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/flock-patches",
		name: "lang-quote-flock-patches",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},

	{
		path: "/:lang?/quote/Economy-Soft-Enamel-Pins",
		name: "lang-Economy-Soft-Enamel-Pins",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/Brass-Soft-Enamel-Pins",
		name: "lang-Brass-Soft-Enamel-Pins",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/Hard-Enamel-Pins",
		name: "lang-Hard-Enamel-Pins",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/Die-Struck-Pins",
		name: "lang-Die-Struck-Pins",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/3D-Pins",
		name: "lang-3D-Pins",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/Brass-Offset-Printed-Pins",
		name: "lang-Brass-Offset-Printed-Pins",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/Stainless-Steel-Offset-Printed-Pins",
		name: "lang-Stainless-Steel-Offset-Printed-Pins",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/Acrylic-Pins",
		name: "lang-Acrylic-Pins",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/UV-Printed-Pins",
		name: "lang-UV-Printed-Pins",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/Flag-Pins",
		name: "lang-Flag-Pins",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/Crossed-Flag-Pins",
		name: "lang-Crossed-Flag-Pins",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/Awareness-Pins-OptionA",
		name: "lang-Awareness-Pins-OptionA",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/Awareness-Pins-OptionB",
		name: "lang-Awareness-Pins-OptionB",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/Soft-Enamel-Trading-Pins",
		name: "lang-Soft-Enamel-Trading-Pins",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/Offset-Printed-Trading-Pins",
		name: "lang-Offset-Printed-Trading-Pins",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/UV-Printed-Trading-Pins",
		name: "lang-UV-Printed-Trading-Pins",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},

	{
		path: "/:lang?/quote/3d-uv-printed-pins",
		name: "lang-3d-uv-printed-pins",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/acrylic-pins",
		name: "lang-acrylic-pins",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/wood-pins",
		name: "lang-wood-pins",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/pvc-pins",
		name: "lang-pvc-pins",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/printed-trading-pins",
		name: "lang-printed-trading-pins",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/die-struck-coins",
		name: "lang-die-struck-coins",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/uv-printed-coins",
		name: "lang-uv-printed-coins",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/3d-uv-printed-coins",
		name: "lang-3d-uv-printed-coins",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/3d-mint-coins",
		name: "lang-3d-mint-coins",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/hard-enamel-coins",
		name: "lang-hard-enamel-coins",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/soft-enamel-coins",
		name: "lang-soft-enamel-coins",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/mint-coins",
		name: "lang-mint-coins",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/antique-medals",
		name: "lang-antique-medals",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/3d-uv-printed-medals",
		name: "lang-3d-uv-printed-medals",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/hard-enamel-medals",
		name: "lang-hard-enamel-medals",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/soft-enamel-medals",
		name: "lang-soft-enamel-medals",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/sandblast-medals",
		name: "lang-sandblast-medals",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/uv-printed-medals",
		name: "lang-uv-printed-medals",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},

	{
		path: "/:lang?/quote/75-embroidered-patches",
		name: "lang-75-embroidered-patches",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/2d-pvc-patches",
		name: "lang-2d-pvc-patches",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/leather-patches",
		name: "lang-leather-patches",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/chenille-patches",
		name: "lang-chenille-patches",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/50-embroidered-patches",
		name: "lang-50-embroidered-patches",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/woven-patches",
		name: "lang-woven-patches",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/100-embroidered-patches",
		name: "lang-100-embroidered-patches",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/bullion-patches",
		name: "lang-bullion-patches",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/die-cut-stickers",
		name: "lang-die-cut-stickers",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/kiss-cut-stickers",
		name: "lang-kiss-cut-stickers",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/bumper-stickers",
		name: "lang-bumper-stickers",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/clear-stickers",
		name: "lang-clear-stickers",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/transfer-stickers",
		name: "lang-transfer-stickers",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/sticker-sheet",
		name: "lang-sticker-sheet",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/front-adhesive-stickers",
		name: "lang-front-adhesive-stickers",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/static-cling-decals",
		name: "lang-static-cling-decals",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/holographic-stickers",
		name: "lang-holographic-stickers",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/glitter-sticker",
		name: "lang-glitter-sticker",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/metallic-stickers",
		name: "lang-metallic-stickers",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/custom-belt-buckle",
		name: "lang-custom-belt-buckle",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	//cg
	{
		path: "/:lang?/quote/sequin-patches",
		name: "lang-sequin-patches",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/reflective-patches",
		name: "lang-reflective-patches",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/dye-sublimated-printed-patches",
		name: "lang-dye-sublimated-printed-patches",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},

	{
		path: "/:lang?/quote/flex-patches",
		name: "lang-flex-patches",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/blank-patches",
		name: "lang-blank-patches",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/one-color-flex-patches",
		name: "lang-one-color-flex-patches",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/woven-labels",
		name: "lang-woven-labels",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/embroidered-printed-patches",
		name: "lang-embroidered-printed-patches",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/denim-patches",
		name: "lang-denim-patches",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/printed-care-labels",
		name: "lang-printed-care-labels",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/flock-patches",
		name: "lang-flock-patches",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/tatami-fabric-silicone-patches",
		name: "lang-tatami-fabric-silicone-patches",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/metal-patches",
		name: "lang-metal-patches",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/3d-embroidered-patch",
		name: "lang-3d-embroidered-patch",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/heat-transfer-patch",
		name: "lang-heat-transfer-patch",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/3d-silicone-patch",
		name: "lang-3d-silicone-patch",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/2d-pvc-luggage-tags",
		name: "lang-2d-pvc-luggage-tags",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/polyester-lanyard",
		name: "lang-polyester-lanyard",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/nylon-lanyard",
		name: "lang-nylon-lanyard",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/dye-sublimated-lanyard",
		name: "lang-dye-sublimated-lanyard",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/woven-lanyard",
		name: "lang-woven-lanyard",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/tubular-lanyard",
		name: "lang-tubular-lanyard",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/die-struck-keychains-no-color",
		name: "lang-die-struck-keychains-no-color",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/soft-enamel-keychains",
		name: "lang-soft-enamel-keychains",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/hard-enamel-keychains",
		name: "lang-hard-enamel-keychains",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/2d-pvc-keychains",
		name: "lang-2d-pvc-keychains",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/3d-pvc-keychains",
		name: "lang-3d-pvc-keychains",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/acrylic-keychains",
		name: "lang-acrylic-keychains",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/50-embroidered-keychains",
		name: "lang-50-embroidered-keychains",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/75-embroidered-keychains",
		name: "lang-75-embroidered-keychains",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/100-embroidered-keychains",
		name: "lang-100-embroidered-keychains",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/chenille-keychains",
		name: "lang-chenille-keychains",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/woven-keychains",
		name: "lang-woven-keychains",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/printed-keychains",
		name: "lang-printed-keychains",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/metal-printing-keychains",
		name: "lang-metal-printing-keychains",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},

	{
		path: "/:lang?/quote/stock-sticker-insert-medals",
		name: "lang-stock-sticker-insert-medals",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/zinc-alloy-metal-patches",
		name: "lang-zinc-alloy-metal-patches",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/silver-laser-cut-ornaments",
		name: "lang-silver-laser-cut-ornaments",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/gold-laser-cut-ornaments",
		name: "lang-gold-laser-cut-ornaments",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/silver-etched-ornaments",
		name: "lang-silver-etched-ornaments",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/gold-etched-ornaments",
		name: "lang-gold-etched-ornaments",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/gold-3d-rotation-ornaments",
		name: "lang-gold-3d-rotation-ornaments",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/sliver-3d-rotation-ornaments",
		name: "lang-sliver-3d-rotation-ornaments",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/stock-snokflake-ornaments",
		name: "lang-stock-snokflake-ornaments",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/stock-uv-holiday-ornaments",
		name: "lang-stock-uv-holiday-ornaments",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/die-cast-ornaments",
		name: "lang-die-cast-ornaments",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/acylic-ornaments",
		name: "lang-acylic-ornaments",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/printed-silicone-wristbands",
		name: "lang-printed-silicone-wristbands",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/soft-enamel-wine-charm",
		name: "lang-soft-enamel-wine-charm",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/hard-enamel-wine-charm",
		name: "lang-hard-enamel-wine-charm",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/antique-wine-charm",
		name: "lang-antique-wine-charm",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/sandblast-wine-charm",
		name: "lang-sandblast-wine-charm",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/die-cast-wine-charm",
		name: "lang-die-cast-wine-charm",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/offset-printed-wine-charm",
		name: "lang-offset-printed-wine-charm",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/picture-wine-charm",
		name: "lang-picture-wine-charm",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/acrylic-ornaments",
		name: "lang-acrylic-ornaments",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},

	{
		path: "/:lang?/quote/die-struck-fidget-spinners",
		name: "lang-die-struck-fidget-spinners",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/enamel-fidget-spinners",
		name: "lang-enamel-fidget-spinners",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/rhinestone-fidget-spinners",
		name: "lang-rhinestone-fidget-spinners",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/glow-in-the-dark-fidget-spinners",
		name: "lang-glow-in-the-dark-fidget-spinners",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},
	{
		path: "/:lang?/quote/3d-fidget-spinners",
		name: "lang-3d-fidget-spinners",
		component: resolve(__dirname, "../pages/_lang/quote/_fdLapelpin"),
	},

	//fd表格详情
	{
		path: "/:lang?/quote/fd-soft-enamel-lapelpin-detail",
		name: "lang-fd-soft-enamel-lapelpin-detail",
		component: resolve(__dirname, "../pages/_lang/quote/_fdSoftEnamelDetail"),
	},
	{
		path: "/:lang?/quote/fd-neon-sign-detail",
		name: "lang-fd-neon-sign-detail",
		component: resolve(__dirname, "../pages/_lang/quote/_fdSoftEnamelDetail"),
	},
	//fd霓虹灯
	{
		path: "/:lang?/quote/static-color-custom-neon-signs",
		name: "lang-static-color-custom-neon-signs",
		component: resolve(__dirname, "../pages/_lang/quote/neon-signs"),
	},
	{
		path: "/:lang?/quote/custom-rgb-changing-7-colors-neon-signs",
		name: "lang-custom-rgb-changing-7-colors-neon-signs",
		component: resolve(__dirname, "../pages/_lang/quote/neon-signs"),
	},
	{
		path: "/:lang?/quote/rgb-gradient-changing-color-neon-signs",
		name: "lang-rgb-gradient-changing-color-neon-signs",
		component: resolve(__dirname, "../pages/_lang/quote/neon-signs"),
	},
	//耳环报价 _customCustomEarrings
	{
		path: "/:lang?/quote/custom-engraved-earrings",
		name: "lang-custom-engraved-earrings",
		component: resolve(__dirname, "../pages/_lang/quote/_customCustomEarrings"),
	},
	{
		path: "/:lang?/quote/personalised-photo-earrings",
		name: "lang-personalised-photo-earrings",
		component: resolve(__dirname, "../pages/_lang/quote/_customCustomEarrings"),
	},
	{
		path: "/:lang?/quote/custom-glow-in-the-dark-earrings",
		name: "lang-custom-glow-in-the-dark-earrings",
		component: resolve(__dirname, "../pages/_lang/quote/_customCustomEarrings"),
	},
	{
		path: "/:lang?/quote/custom-rhinestone-earrings",
		name: "lang-custom-rhinestone-earrings",
		component: resolve(__dirname, "../pages/_lang/quote/_customCustomEarrings"),
	},
	{
		path: "/:lang?/quote/custom-glitter-earrings",
		name: "lang-custom-glitter-earrings",
		component: resolve(__dirname, "../pages/_lang/quote/_customCustomEarrings"),
	},
	{
		path: "/:lang?/quote/custom-enamel-earrings",
		name: "lang-custom-enamel-earrings",
		component: resolve(__dirname, "../pages/_lang/quote/_customCustomEarrings"),
	},
	{
		path: "/:lang?/quote/3d-custom-earrings",
		name: "lang-3d-custom-earrings",
		component: resolve(__dirname, "../pages/_lang/quote/_customCustomEarrings"),
	},
	//pins 报价
	{
		path: "/:lang?/quote/custom-enamel-pins",
		name: "lang-quote-custom-enamel-pins",
		component: resolve(__dirname, "../pages/_lang/quote/_pinsQuote"),
	},
	{
		path: "/:lang?/quote/custom-die-struck-pins",
		name: "lang-quote-custom-die-struck-pins",
		component: resolve(__dirname, "../pages/_lang/quote/_pinsQuote"),
	},
	{
		path: "/:lang?/quote/custom-3D-pins",
		name: "lang-quote-custom-3D-pins",
		component: resolve(__dirname, "../pages/_lang/quote/_pinsQuote"),
	},
	{
		path: "/:lang?/quote/custom-uv-printed-pins",
		name: "lang-quote-custom-uv-printed-pins",
		component: resolve(__dirname, "../pages/_lang/quote/_pinsQuote"),
	},
	{
		path: "/:lang?/quote/custom-offset-pins",
		name: "lang-quote-custom-offset-pins",
		component: resolve(__dirname, "../pages/_lang/quote/_pinsQuote"),
	},
	{
		path: "/:lang?/quote/custom-rush-pins",
		name: "lang-quote-custom-rush-pins",
		component: resolve(__dirname, "../pages/_lang/quote/_pinsQuote"),
	},
	{
		path: "/:lang?/quote/custom-acrylic-pins",
		name: "lang-quote-custom-acrylic-pins",
		component: resolve(__dirname, "../pages/_lang/quote/_pinsQuote"),
	},
	{
		path: "/:lang?/quote/custom-cut-out-pins",
		name: "lang-quote-custom-cut-out-pins",
		component: resolve(__dirname, "../pages/_lang/quote/_pinsQuote"),
	},
	{
		path: "/:lang?/quote/hatpins-quote",
		name: "lang-quote-hatpins-quote",
		component: resolve(__dirname, "../pages/_lang/quote/_pinsQuote"),
	},
	//pinBadges 报价
	{
		path: "/:lang?/quote/custom-cut-out-pin-badges",
		name: "lang-quote-custom-cut-out-pin-badges",
		component: resolve(__dirname, "../pages/_lang/quote/_pinBadgesQuote"),
	},
	{
		path: "/:lang?/quote/custom-enamel-pin-badges",
		name: "lang-quote-custom-enamel-pin-badges",
		component: resolve(__dirname, "../pages/_lang/quote/_pinBadgesQuote"),
	},
	{
		path: "/:lang?/quote/custom-die-struck-pin-badges",
		name: "lang-quote-custom-die-struck-pin-badges",
		component: resolve(__dirname, "../pages/_lang/quote/_pinBadgesQuote"),
	},
	{
		path: "/:lang?/quote/custom-3D-pin-badges",
		name: "lang-quote-custom-3D-pin-badges",
		component: resolve(__dirname, "../pages/_lang/quote/_pinBadgesQuote"),
	},
	{
		path: "/:lang?/quote/custom-uv-printed-pin-badges",
		name: "lang-quote-custom-uv-printed-pin-badges",
		component: resolve(__dirname, "../pages/_lang/quote/_pinBadgesQuote"),
	},
	{
		path: "/:lang?/quote/custom-offset-pin-badges",
		name: "lang-quote-custom-offset-pin-badges",
		component: resolve(__dirname, "../pages/_lang/quote/_pinBadgesQuote"),
	},
	{
		path: "/:lang?/quote/custom-rush-pin-badges",
		name: "lang-quote-custom-rush-pin-badges",
		component: resolve(__dirname, "../pages/_lang/quote/_pinBadgesQuote"),
	},
	// keychains报价
	{
		path: "/:lang?/quote/cut-out-keychains",
		name: "lang-quote-cut-out-keychains",
		component: resolve(__dirname, "../pages/_lang/quote/_keychains"),
	},
	{
		path: "/:lang?/quote/custom-rush-printed-keychains",
		name: "lang-quote-custom-rush-printed-keychains",
		component: resolve(__dirname, "../pages/_lang/quote/_keychains"),
	},
	{
		path: "/:lang?/quote/metal-couples-keychains",
		name: "lang-quote-metal-couples-keychains",
		component: resolve(__dirname, "../pages/_lang/quote/_keychains"),
	},
	{
		path: "/:lang?/quote/custom-acrylic-couples-keychains",
		name: "lang-quote-custom-acrylic-couples-keychains",
		component: resolve(__dirname, "../pages/_lang/quote/_keychains"),
	},
	{
		path: "/:lang?/quote/custom-acrylic-keychains",
		name: "lang-quote-custom-acrylic-keychains",
		component: resolve(__dirname, "../pages/_lang/quote/_keychains"),
	},
	{
		path: "/:lang?/quote/custom-embroidered-keychains",
		name: "lang-quote-custom-embroidered-keychains",
		component: resolve(__dirname, "../pages/_lang/quote/_keychains"),
	},
	{
		path: "/:lang?/quote/custom-metal-keychains",
		name: "lang-quote-custom-metal-keychains",
		component: resolve(__dirname, "../pages/_lang/quote/_keychains"),
	},
	{
		path: "/:lang?/quote/custom-metal-printing-keychains",
		name: "lang-quote-custom-metal-printing-keychains",
		component: resolve(__dirname, "../pages/_lang/quote/_keychains"),
	},
	{
		path: "/:lang?/quote/custom-pvc-keychains",
		name: "lang-quote-custom-pvc-keychains",
		component: resolve(__dirname, "../pages/_lang/quote/_keychains"),
	},
	//Ball Marker报价
	{
		path: "/:lang?/quote/custom-ball-marker",
		name: "lang-quote-custom-ball-marker",
		component: resolve(__dirname, "../pages/_lang/quote/_ballMarker"),
	},
	{
		path: "/:lang?/quote/custom-ball-marker-with-hat-clips",
		name: "lang-quote-custom-ball-marker-with-hat-clips",
		component: resolve(__dirname, "../pages/_lang/quote/_ballMarker"),
	},
	{
		path: "/:lang?/quote/custom-magnetic-ball-markers",
		name: "lang-quote-custom-magnetic-ball-markers",
		component: resolve(__dirname, "../pages/_lang/quote/_ballMarker"),
	},
	{
		path: "/:lang?/quote/custom-divot-tools",
		name: "lang-quote-custom-divot-tools",
		component: resolve(__dirname, "../pages/_lang/quote/_ballMarker"),
	},
	//手环报价
	{
		path: "/:lang?/quote/printed-wristbands",
		name: "lang-quote-printed-wristbands",
		component: resolve(__dirname, "../pages/_lang/quote/_wristbands"),
	},
	//快速报价弹窗路由
	{
		path: "/:lang?/quote/custom-24-hours-printed-medals",
		name: "lang-quote-custom-24-hours-printed-medals",
		component: resolve(__dirname, "../pages/_lang/quote/_quickDialog"),
	},
	{
		path: "/:lang?/quote/48-hours-printed-medals",
		name: "lang-quote-48-hours-printed-medals",
		component: resolve(__dirname, "../pages/_lang/quote/_quickDialog"),
	},
	//patches类别报价
	{
		path: "/:lang?/quote/custom-embroidered-patches",
		name: "lang-quote-custom-embroidered-patches",
		component: resolve(__dirname, "../pages/_lang/quote/_patches"),
	},
	{
		path: "/:lang?/quote/3d-embroidered-patches",
		name: "lang-quote-3d-embroidered-patches",
		component: resolve(__dirname, "../pages/_lang/quote/_patches"),
	},
	{
		path: "/:lang?/quote/custom-woven-patches",
		name: "lang-quote-custom-woven-patches",
		component: resolve(__dirname, "../pages/_lang/quote/_patches"),
	},
	{
		path: "/:lang?/quote/custom-printed-patches",
		name: "lang-quote-custom-printed-patches",
		component: resolve(__dirname, "../pages/_lang/quote/_patches"),
	},
	{
		path: "/:lang?/quote/custom-flex-patches",
		name: "lang-quote-custom-flex-patches",
		component: resolve(__dirname, "../pages/_lang/quote/_patches"),
	},
	{
		path: "/:lang?/quote/one-color-tpu-patches",
		name: "lang-quote-one-color-tpu-patches",
		component: resolve(__dirname, "../pages/_lang/quote/_patches"),
	},
	{
		path: "/:lang?/quote/custom-leather-patches",
		name: "lang-quote-custom-leather-patches",
		component: resolve(__dirname, "../pages/_lang/quote/_patches"),
	},
	{
		path: "/:lang?/quote/custom-chenille-patches",
		name: "lang-quote-custom-chenille-patches",
		component: resolve(__dirname, "../pages/_lang/quote/_patches"),
	},
	{
		path: "/:lang?/quote/custom-sequin-patches",
		name: "lang-quote-custom-sequin-patches",
		component: resolve(__dirname, "../pages/_lang/quote/_patches"),
	},
	{
		path: "/:lang?/quote/custom-denim-patches",
		name: "lang-quote-custom-denim-patches",
		component: resolve(__dirname, "../pages/_lang/quote/_patches"),
	},
	{
		path: "/:lang?/quote/custom-full-embroidered-full-printed-patches",
		name: "lang-quote-custom-embroidered-printed-patches",
		component: resolve(__dirname, "../pages/_lang/quote/_patches"),
	},
	{
		path: "/:lang?/quote/heat-transfer-patches",
		name: "lang-quote-heat-transfer-patches",
		component: resolve(__dirname, "../pages/_lang/quote/_patches"),
	},
	{
		path: "/:lang?/quote/3d-silicone-patches",
		name: "lang-quote-3d-silicone-patches",
		component: resolve(__dirname, "../pages/_lang/quote/_patches"),
	},
	{
		path: "/:lang?/quote/custom-tatami-fabric-silicone-patches",
		name: "lang-quote-custom-tatami-fabric-silicone-patches",
		component: resolve(__dirname, "../pages/_lang/quote/_patches"),
	},
	{
		path: "/:lang?/quote/PVCpatches-quote",
		name: "lang-quote-PVCpatches-quote",
		component: resolve(__dirname, "../pages/_lang/quote/_patches"),
	},
	{
		path: "/:lang?/quote/custom-blank-patches",
		name: "lang-quote-custom-blank-patches",
		component: resolve(__dirname, "../pages/_lang/quote/_patches"),
	},
	{
		path: "/:lang?/quote/custom-flock-patches",
		name: "lang-quote-custom-flock-patches",
		component: resolve(__dirname, "../pages/_lang/quote/_patches"),
	},
	{
		path: "/:lang?/quote/custom-woven-labels",
		name: "lang-quote-custom-woven-labels",
		component: resolve(__dirname, "../pages/_lang/quote/_patches"),
	},
	{
		path: "/:lang?/quote/custom-printed-care-labels",
		name: "lang-quote-custom-printed-care-labels",
		component: resolve(__dirname, "../pages/_lang/quote/_patches"),
	},

	// ornaments报价
	{
		path: "/:lang?/quote/custom-stainless-steel-ornaments",
		name: "lang-quote-custom-stainless-steel-ornaments",
		component: resolve(__dirname, "../pages/_lang/quote/_ornaments"),
	},
	{
		path: "/:lang?/quote/custom-gold-brass-ornaments",
		name: "lang-quote-custom-gold-brass-ornaments",
		component: resolve(__dirname, "../pages/_lang/quote/_ornaments"),
	},
	{
		path: "/:lang?/quote/custom-zinc-die-casting-ornaments",
		name: "lang-quote-custom-zinc-die-casting-ornaments",
		component: resolve(__dirname, "../pages/_lang/quote/_ornaments"),
	},
	{
		path: "/:lang?/quote/custom-spinning-ornaments",
		name: "lang-quote-custom-spinning-ornaments",
		component: resolve(__dirname, "../pages/_lang/quote/_ornaments"),
	},
	{
		path: "/:lang?/quote/custom-acrylic-ornaments",
		name: "lang-quote-custom-acrylic-ornaments",
		component: resolve(__dirname, "../pages/_lang/quote/_ornaments"),
	},
	{
		path: "/:lang?/quote/custom-laser-cut-ornaments",
		name: "lang-quote-custom-laser-cut-ornaments",
		component: resolve(__dirname, "../pages/_lang/quote/_ornaments"),
	},
	{
		path: "/:lang?/quote/3d-fidget-spinner-quote",
		name: "lang-quote-3d-fidget-spinner-quote",
		component: resolve(__dirname, "../pages/_lang/quote/_fidgetSpinner"),
	},
	{
		path: "/:lang?/quote/die-struck-fidget-spinner-quote",
		name: "lang-quote-die-struck-fidget-spinner-quote",
		component: resolve(__dirname, "../pages/_lang/quote/_fidgetSpinner"),
	},
	{
		path: "/:lang?/quote/enamel-fidget-spinner-quote",
		name: "lang-quote-enamel-fidget-spinner-quote",
		component: resolve(__dirname, "../pages/_lang/quote/_fidgetSpinner"),
	},
	{
		path: "/:lang?/quote/glow-in-the-dark-fidget-spinner-quote",
		name: "lang-quote-glow-in-the-dark-fidget-spinner-quote",
		component: resolve(__dirname, "../pages/_lang/quote/_fidgetSpinner"),
	},
	{
		path: "/:lang?/quote/rhinestone-fidget-spinner-quote",
		name: "lang-quote-rhinestone-fidget-spinner-quote",
		component: resolve(__dirname, "../pages/_lang/quote/_fidgetSpinner"),
	},
];
