<template>
	<div class="transparent-badge">
		<div class="badge-styles">
			<div class="style-section" v-for="(style, styleIndex) in itemData.childList" :key="styleIndex">
				<div class="style-grid">
					<div class="style-left">
						<h4 class="style-title">{{ style.alias2 || style.alias }}</h4>
						<div class="item-image">
							<img :src="getImg(style)" alt="" class="img" />
							<b class="icon-fangda4" @click="fangdaImg(styleIndex)"></b>
						</div>
					</div>
					<div class="style-right">
						<div class="scrollBox custom-scrollbar" :class="{ hasOther: hasOther(style, 'sizeTable', 'priceTable') }">
							<div class="size-table">
								<div class="table-header">
									<div class="header-cell" v-for="(row, index) in filterTable(style, 'sizeTable', true)" :key="index">{{ row }}</div>
								</div>
								<div class="table-row" v-for="(row, index) in filterTable(style, 'sizeTable')" :key="index">
									<div class="cell" v-for="(row2, index2) in row" :key="index2">{{ row2 }}</div>
								</div>
							</div>
						</div>
						<div class="scrollBox custom-scrollbar priceTableBox">
							<div class="price-table" :class="{ hasOnly: hasOnly(style, 'priceTable') && !hasOther(style, 'sizeTable', 'priceTable') }">
								<div class="table-header">
									<div class="header-cell" v-for="(row, index) in filterTable(style, 'priceTable', true)" :key="index">{{ row }}</div>
								</div>
								<div class="table-row" v-for="(row, index) in filterTable(style, 'priceTable')" :key="index">
									<div class="cell" v-for="(row2, index2) in row" :key="index2">{{ row2 }}</div>
								</div>
							</div>
						</div>
						<div class="discountCode" v-show="style.priceTableDiscountCode">
							{{ "( " + style.priceTableDiscountCode + " )" }}
						</div>
					</div>
					<div class="mbPriceTableBox">
						<div class="scrollBox custom-scrollbar">
							<div class="price-table" :class="{ hasOnly: hasOnly(style, 'priceTable') && !hasOther(style, 'sizeTable', 'priceTable') }">
								<div class="table-header">
									<div class="header-cell" v-for="(row, index) in filterTable(style, 'priceTable', true)" :key="index">{{ row }}</div>
								</div>
								<div class="table-row" v-for="(row, index) in filterTable(style, 'priceTable')" :key="index">
									<div class="cell" v-for="(row2, index2) in row" :key="index2">{{ row2 }}</div>
								</div>
							</div>
						</div>
						<div class="discountCode" v-show="style.priceTableDiscountCode">
							{{ "( " + style.priceTableDiscountCode + " )" }}
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	name: "TransparentBadge",
	components: {},
	props: {
		itemData: {
			type: Object,
			default: () => {},
		},
	},
	data() {
		return {};
	},
	computed: {
		isMobile() {
			return this.$store.getters.isMobile;
		},
		viewImgList() {
			return this.itemData.childList.map((item) => {
				return this.getImg(item);
			});
		},
	},
	watch: {},
	methods: {
		getImg(item) {
			if (item.imageJson) {
				try {
					let imgUrl = JSON.parse(item.imageJson);
					return imgUrl[0].url;
				} catch (e) {
					console.error("Error parsing imageJson:", e);
					return item.imageUrl || "";
				}
			} else {
				return item.imageUrl || "";
			}
		},
		filterTable(data, type, one = false) {
			if (data[type] && data[type].length > 0) {
				const sizeTableData = JSON.parse(data[type]);
				if (!Array.isArray(sizeTableData) || sizeTableData.length <= 0) return [];
				if (one) {
					return sizeTableData[0] || [];
				}
				return sizeTableData.slice(1) || [];
			}
			return [];
		},
		hasOther(data, type1, type2) {
			return this.filterTable(data, type1, true).length > 0 && this.filterTable(data, type2, true).length > 0;
		},
		hasOnly(data, type) {
			return this.filterTable(data, type, true).length > 0;
		},
		fangdaImg(index) {
			let zoomNum = 2.2;
			if (this.isMobile) zoomNum = 1;
			const $viewer = this.$viewerApi({
				images: this.viewImgList,
				options: {
					initialViewIndex: index,
					initialCoverage: 0,
					viewed: function () {
						$viewer.zoomTo(zoomNum);
					},
				},
			});
			return $viewer;
		},
	},
	created() {},
	mounted() {},
};
</script>
<style scoped lang="scss">
.custom-scrollbar::-webkit-scrollbar {
	/*滚动条整体样式*/
	width: 5px;
	/*高宽分别对应横竖滚动条的尺寸*/
	height: 6px;
	border-radius: 10px;
	border: 1px solid #dfdfdf;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
	/*滚动条里面小方块*/
	border-radius: 10px;
	background: #8e9dc2;
}

.custom-scrollbar::-webkit-scrollbar-track {
	/*滚动条里面轨道*/
	border-radius: 10px;
}
.transparent-badge {
	width: 100%;

	.badge-styles {
		margin-bottom: 30px;

		.style-section {
			margin-bottom: 20px;
			background: #ffffff;
			border-radius: 10px;
			border: 1px solid #dfdfdf;
			padding: 10px;

			.style-grid {
				display: grid;
				grid-template-columns: 30% 1fr;
				gap: 10px;

				.style-left {
					.style-title {
						font-size: 18px;
						font-weight: bold;
						margin-bottom: 0.5em;
						color: #333;
						text-align: center;
					}
					.item-image {
						width: 100%;
						display: flex;
						align-items: center;
						justify-content: center;
						background-color: #f5f5f5;
						border-radius: 10px;
						overflow: hidden;
						position: relative;

						.img {
							aspect-ratio: 1;
							max-width: 100%;
							max-height: 100%;
							object-fit: contain !important;
						}
						b {
							cursor: pointer;
							position: absolute;
							top: 10px;
							right: 10px;
							color: #ccc;
							font-size: 16px;
						}
					}
				}

				.style-right {
					width: 100%;
					position: relative;
					height: fit-content;
					padding-bottom: 20px;
					overflow: hidden;
				}
				.scrollBox {
					width: 100%;
					padding-bottom: 10px;
					overflow: auto hidden;
					&.hasOther {
						margin-bottom: 30px;
						.table-header {
							border-radius: 0px 8px 0px 0px;
						}
					}
				}
				.mbPriceTableBox {
					position: relative;
					display: none;
				}
			}

			@include respond-to(mb) {
				margin-bottom: 10px;
				padding: 8px;
				.style-grid {
					grid-template-columns: 1fr 1fr;
					.style-left {
						.item-image {
							b {
								top: 5px;
								right: 5px;
								font-size: 12px;
							}
						}
					}
					.style-right {
						padding-bottom: 0px;
						.scrollBox {
							&.hasOther {
								margin-bottom: 10px;
							}
						}
						.priceTableBox {
							display: none;
						}
						.discountCode {
							display: none;
						}
					}
					.mbPriceTableBox {
						display: block !important;
						padding-bottom: 30px;
						grid-column: span 2;
						overflow: hidden;
					}
				}
			}
		}
	}

	.size-table {
		width: 100%;
		min-width: fit-content;
		.table-header {
			width: fit-content;
			max-width: 300%;
			min-width: 100%;
			display: grid;
			grid-template-rows: 1fr;
			grid-template-columns: 2fr 1fr 1fr;
			font-weight: bold;
			background: #969eb5;
			color: #fff;
			padding: 0 30px;

			.header-cell {
				padding: 16px 10px;
				text-align: center;
				font-size: 14px;
				white-space: nowrap;
				min-width: 100px;
				&:first-child {
					text-align: left;
					padding-left: 40px;
				}
			}
		}

		.table-row {
			width: fit-content;
			max-width: 300%;
			min-width: 100%;
			display: grid;
			grid-template-columns: 2fr 1fr 1fr;
			background-color: #fff;
			padding: 0 30px;
			border-bottom: 1px solid #dfdfdf;

			&:nth-child(odd) {
				background-color: #f6f6f6;
			}

			.cell {
				padding: 10px 8px;
				text-align: center;
				font-size: 14px;
				color: #333;
				white-space: nowrap;
				min-width: 100px;
				&:first-child {
					text-align: left;
					padding-left: 40px;
				}
			}
		}

		@include respond-to(mb) {
			.table-header {
				padding: 0 20px;
				.header-cell {
					font-size: 12px;
					padding: 10px 4px;
					&:first-child {
						text-align: left;
						padding-left: 0;
					}
				}
			}
			.table-row {
				padding: 0 20px;
				.cell {
					padding: 10px 4px;
					font-size: 12px;
					&:first-child {
						text-align: left;
						padding-left: 0;
					}
				}
			}
		}
	}

	.price-table {
		width: 100%;

		.table-header {
			width: fit-content;
			max-width: 300%;
			min-width: 100%;
			display: grid;
			grid-template-rows: 1fr;
			grid-template-columns: repeat(1, minmax(200px, 3fr)) repeat(auto-fit, minmax(60px, 1fr));
			font-weight: bold;
			background: #969eb5;
			color: #fff;

			.header-cell {
				padding: 16px 10px;
				text-align: center;
				font-size: 14px;
				white-space: nowrap;
				&:first-child {
					text-align: left;
					padding-left: 60px;
				}
			}
		}

		.table-row {
			width: fit-content;
			max-width: 300%;
			min-width: 100%;
			display: grid;
			grid-template-columns: repeat(1, minmax(200px, 3fr)) repeat(auto-fit, minmax(60px, 1fr));
			background-color: #fff;

			&:nth-child(odd) {
				background-color: #f6f6f6;
			}
			&:last-child {
				.cell {
					border: none;
				}
			}
			.cell {
				padding: 10px 8px;
				text-align: center;
				border-bottom: 1px solid #dfdfdf;
				font-size: 14px;
				color: #333;
				white-space: nowrap;
				&:first-child {
					text-align: left;
					padding-left: 40px;
				}
			}
		}
		@include respond-to(mb) {
			.table-header {
				grid-template-columns: repeat(1, minmax(160px, 3fr)) repeat(auto-fit, minmax(60px, 1fr));
				.header-cell {
					font-size: 12px;
					padding: 10px 4px;
					&:first-child {
						text-align: left;
						padding-left: 10px;
					}
				}
			}
			.table-row {
				grid-template-columns: repeat(1, minmax(160px, 3fr)) repeat(auto-fit, minmax(60px, 1fr));
				.cell {
					font-size: 12px;
					padding: 10px 4px;
					&:first-child {
						text-align: left;
						padding-left: 10px;
					}
				}
			}
		}
	}

	.discountCode {
		position: absolute;
		bottom: 0;
		right: 0;
		display: flex;
		align-items: center;
		justify-content: space-between;
		font-size: 16px;
		color: #333333;
		@include respond-to(mb) {
			font-size: 14px;
		}
	}
}
</style>
