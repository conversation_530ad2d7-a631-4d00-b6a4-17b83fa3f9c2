<template>
	<div class="fontSize">
		<div class="fontSizeGrid" :class="itemData.styleName">
			<div class="fontSizeBox" v-for="(item, index) in itemData.childList" :key="index" :class="{ selected: item.selected }">
				<div class="imgWrap">
					<img :src="getImg(item)" :style="{ aspectRatio: aspectRatio + '', ...imgStyleObj }" alt="" class="img" />
					<b class="icon-fangda4" @click="fangdaImg(index)"></b>
				</div>
				<div class="fontSizeInfo" v-show="false">
					<div class="fontSizeName">{{ item.alias2 || item.alias }}</div>
					<div class="fontSizePrice" :class="{ free: citem == 'Free' }" v-for="(citem, index) in getPrice(item)" :key="index">{{ citem }}</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	name: "fontSize",
	components: {},
	props: {
		itemData: {
			type: Object,
			default: () => {},
		},
		aspectRatio: {
			type: [Number, String],
			default: "",
		},
		imgStyleObj: {
			type: Object,
			default: () => {},
		},
		className: {
			type: String,
			default: "",
		},
	},
	data() {
		return {};
	},
	computed: {
		isMobile() {
			return this.$store.getters.isMobile;
		},
		viewImgList() {
			return this.itemData.childList.map((item) => {
				return this.getImg(item);
			});
		},
	},
	watch: {},
	methods: {
		getImg(item) {
			if (item.imageJson) {
				try {
					let imgUrl = JSON.parse(item.imageJson);
					return imgUrl[0].url;
				} catch (e) {
					console.error("Error parsing imageJson:", e);
					return item.imageUrl || "";
				}
			} else {
				return item.imageUrl || "";
			}
		},
		getPrice(item) {
			if (item.priceInfo && item.priceInfo?.priceShow) {
				return item.priceInfo["priceShow"];
			}
			return "Free";
		},
		selectOption(item) {
			// 实现选择逻辑
			this.$emit("option-selected", item);
		},
		fangdaImg(index) {
			let zoomNum = 1.2;
			if (this.isMobile) zoomNum = 0.75;
			const $viewer = this.$viewerApi({
				images: this.viewImgList,
				options: {
					initialViewIndex: index,
					initialCoverage: 0,
					viewed: function () {
						$viewer.zoomTo(zoomNum);
					},
				},
			});
			return $viewer;
		},
	},
	created() {},
	mounted() {},
};
</script>
<style scoped lang="scss">
.fontSize {
	width: 100%;

	.fontSizeGrid {
		width: 100%;
		display: grid;
		grid-template-columns: repeat(5, 1fr);
		gap: 20px;
		.fontSizeBox {
			position: relative;
			background: #ffffff;
			border: 1px solid #f0f0f0;
			border-radius: 8px;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			gap: 10px;
			cursor: pointer;
			transition: all 0.3s ease;
			padding: 4% 10%;

			&.selected {
				border: 2px solid #4a90e2;
			}

			.imgWrap {
				width: 100%;
				display: flex;
				align-items: center;
				justify-content: center;
				border-radius: 8px 8px 0 0;
				overflow: hidden;

				.img {
					max-width: 100%;
					max-height: 100%;
					object-fit: contain !important;
				}
				b {
					cursor: pointer;
					position: absolute;
					top: 10px;
					right: 10px;
					color: #ccc;
					font-size: 16px;
				}
			}

			.fontSizeInfo {
				width: 100%;
				display: flex;
				flex-direction: column;
				align-items: center;
				gap: 5px;
				padding: 0 4px;

				.fontSizeName {
					font-size: 18px;
					color: #333333;
					text-align: center;
				}

				.fontSizePrice {
					font-size: 16px;
					color: #666666;
					text-align: center;

					&.free {
						color: #d81e06;
					}
				}
			}
		}

		@include respond-to(mb) {
			grid-template-columns: repeat(3, 1fr);
			gap: 10px;
			.fontSizeBox {
				gap: 6px;
				padding-bottom: 6px;
				.imgWrap {
					b {
						top: 5px;
						right: 5px;
						font-size: 12px;
					}
				}
				.fontSizeInfo {
					text-align: center;
					.fontSizeName {
						font-size: 14px;
						font-weight: bold;
						color: #333333;
						text-align: center;
					}

					.fontSizePrice {
						font-size: 12px;
						font-weight: bold;
						color: #666666;

						&.free {
							color: #d81e06;
						}
					}
				}
			}
		}
	}
}
</style>
