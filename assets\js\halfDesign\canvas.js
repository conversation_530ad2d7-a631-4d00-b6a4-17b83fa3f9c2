import { generateUUID, loadImage, urlAddVersion } from "@/utils/utils";
import { BezierText } from "assets/js/halfDesign/BezierText";
import TextPathRenderer from "assets/js/halfDesign/TextPathRenderer";

import Vue from "vue";
// 创建一个仅包含 loadImgState 的响应式对象
const imgState = Vue.observable({ loadImgState: true, loadBackImgState: true });

const canvas = {
	c: "",
	clipPath: "",
	bgScale: 1,
	isBig: false,
	textClass: new BezierText(),
	textClass2: new TextPathRenderer(),
	pathType: {
		2: "line",
		3: "quadratic",
		4: "cubic",
	},
	get loadImgState() {
		return imgState.loadImgState;
	},
	set loadImgState(val) {
		imgState.loadImgState = val;
	},
	get loadBackImgState() {
		return imgState.loadBackImgState;
	},
	set loadBackImgState(val) {
		imgState.loadBackImgState = val;
	},
	needAddX: false,
	needAddY: false,
	addRecord: 0,
	// 辅助函数：计算最小包围矩形
	calculateBoundingBox(points, scaleFactor, bgScale) {
		let minX = Infinity,
			minY = Infinity;
		let maxX = -Infinity,
			maxY = -Infinity;
		points.forEach((point) => {
			let { x, y } = point;
			let nowX = +((x / scaleFactor) * bgScale).toFixed(2);
			let nowY = +((y / scaleFactor) * bgScale).toFixed(2);
			if (nowX < minX) minX = nowX;
			if (nowY < minY) minY = nowY;
			if (nowX > maxX) maxX = nowX;
			if (nowY > maxY) maxY = nowY;
		});

		return {
			left: minX,
			top: minY,
			width: maxX - minX,
			height: maxY - minY,
		};
	},
	nowpoints(points, scaleFactor = 1) {
		return points.map((point) => {
			let { x, y } = point;
			let nowX = +((x / scaleFactor) * this.bgScale).toFixed(2);
			let nowY = +((y / scaleFactor) * this.bgScale).toFixed(2);
			if (this.needAddX) {
				return {
					x: nowX + this.addRecord,
					y: nowY,
				};
			}
			if (this.needAddY) {
				return {
					x: nowX,
					y: nowY + this.addRecord,
				};
			}
			return {
				x: nowX,
				y: nowY,
			};
		});
	},
	setEditArea(data, options, type = "default") {
		return new Promise((resolve, reject) => {
			try {
				let fabricStage = this.c;
				if (!fabricStage) {
					return;
				}
				if (!fabricStage || !(fabricStage instanceof fabric.Canvas)) {
					console.error("Fabric canvas is not initialized.");
					return reject("Fabric canvas is not initialized.");
				}
				let bg = fabricStage.backgroundImage;

				this.bgScale = bg.scaleX;
				let position = {
					top: data.top || 0,
					left: data.left || 0,
					right: data.right || 0,
					bottom: data.bottle || 0,
				};
				if (options.isClip) {
					let ratio = (options.imgWidth || 5) / (options.imgHeight || 1);
					let w = 1000,
						h = w / ratio,
						x = 0,
						y = (w - h) / 2;
					position.top -= y;
					position.bottom -= y;
				}
				let xian = null;
				if (!data.selectedStyle) {
					// 添加圆形边框
					if (data.circle && data.circle.radius && +data.circle.radius > 0) {
						xian = this.drawCircle(data);
					} else {
						//矩形区域
						xian = this.drawReact(bg, position);
					}
				} else {
					// 添加圆形边框
					if (data.circle && data.circle.radius && +data.circle.radius > 0 && data.selectedStyle == 2) {
						xian = this.drawCircle(data);
					} else if (data.selectedStyle == 1) {
						//矩形区域
						xian = this.drawReact(bg, position);
					} else {
						xian = this.drawReact(bg, position);
					}
				}

				if (!xian) {
					console.error("xian is null or invalid.");
					return reject("xian is null or invalid.");
				}
				//文本曲线------
				let textReactArr = [];
				if (Array.isArray(data.curves) && (type == "coins" || type == "buckles")) {
					const objects = fabricStage.getObjects();
					const toBeRemoved = objects.filter((obj) => obj.id === "BezierCurvePath");
					toBeRemoved.forEach((obj) => fabricStage.remove(obj));
					let config = {
						fill: "",
						visible: false,
						stroke: "#000",
						strokeWidth: 1,
						id: "BezierCurvePath",
					};
					let defaultTextArr = data.curves || [];
					if (Array.isArray(data.roundText)) {
						defaultTextArr = data.roundText.concat(defaultTextArr);
					}
					defaultTextArr.forEach((item) => {
						let newPoints = this.nowpoints(item.points, item.scaleFactor);
						if (type == "coins") {
							let textReact = this.textClass.createBezierCurvePath(newPoints, config);
							textReactArr.push(textReact);
						}
						if (type == "buckles") {
							let pathType = this.pathType[newPoints.length];
							if (item.type == "round") pathType = "circle";
							const path = new fabric.Path(this.textClass2._createPath(newPoints, pathType), config);
							textReactArr.push(path);
						}
					});
				}
				if (textReactArr.length > 0) {
					textReactArr.forEach((item) => {
						fabricStage.add(item);
					});
				}
				fabricStage.add(xian);
				// 添加 after:render 事件监听器
				const handleAfterRender = () => {
					fabricStage.off("after:render", handleAfterRender); // 移除监听器以避免重复触发
					resolve();
				};
				fabricStage.on("after:render", handleAfterRender);
				fabricStage.requestRenderAll();
			} catch (error) {
				console.log(error, "setEditArea");
				reject(error);
			}
		});
	},
	drawCircle(data) {
		let [{ x, y }] = this.nowpoints([{ x: data.circle.x, y: data.circle.y }], data.scaleFactor);
		let radius = (data.circle.radius / data.scaleFactor) * this.bgScale;
		this.clipPath = new fabric.Circle({
			left: x - radius,
			top: y - radius,
			radius: radius,
			absolutePositioned: true,
		});
		return new fabric.Circle({
			left: x - radius,
			top: y - radius,
			radius: radius,
			stroke: "#e2e2e2",
			strokeWidth: 2,
			fill: "transparent",
			selectable: false,
			evented: false,
			id: "editArea",
			visible: false,
			isEditArea: true,
			excludeFromExport: true,
		});
	},
	drawReact(bg, position) {
		let top = bg.top + position.top * this.bgScale,
			left = bg.left + position.left * this.bgScale,
			clipWidth = bg.getScaledWidth() - position.left * this.bgScale - position.right * this.bgScale,
			clipHeight = bg.getScaledHeight() - position.top * this.bgScale - position.bottom * this.bgScale;
		// 创建主编辑区域边界
		let createRect = ({ visible, id, ...rest }) =>
			new fabric.Rect({
				visible,
				...rest,
				selectable: false,
				evented: false,
				fill: "transparent",
				stroke: "#e2e2e2",
				strokeWidth: 1,
				strokeUniform: true,
				id,
				isEditArea: true,
				excludeFromExport: true,
			});
		this.clipPath = new fabric.Rect({
			left: left,
			top: top,
			width: clipWidth - 1,
			height: clipHeight - 1,
			absolutePositioned: true,
		});
		return createRect({
			visible: false,
			id: "editArea",
			left,
			top,
			width: clipWidth - 1,
			height: clipHeight - 1,
		});
	},
	setCanvasBg(src, options) {
		return new Promise(async (resolve, reject) => {
			try {
				this.loadBackImgState = false;
				let fabricStage = this.c;
				if (options.isClip) {
					let ratio = (options.imgWidth || 5) / (options.imgHeight || 1);
					let w = 1000,
						h = w / ratio,
						x = 0,
						y = (w - h) / 2;
					src += `?x-oss-process=image/crop,x_${x},y_${y},w_${w},h_${h}`;
				}
				let bgImg = await loadImage(urlAddVersion(src));
				let fabricWidth = this.c.getWidth(),
					fabricHeight = this.c.getHeight();
				let scaleX = fabricWidth / bgImg.width;
				let scaleY = fabricHeight / bgImg.height;
				let scale = Math.min(scaleX, scaleY);
				this.needAddX = false;
				this.needAddY = false;
				this.addRecord = 0;
				//如果传入的不是正方形，需要补高度或者宽度
				if (scaleX != scaleY) {
					// 谁大补谁
					if (scaleX > scaleY) {
						this.needAddX = true;
						this.addRecord = (fabricWidth - bgImg.width * scale) / 2;
					} else {
						this.needAddY = true;
						this.addRecord = (fabricHeight - bgImg.height * scale) / 2;
					}
				}
				this.bgScale = scale;
				let imgInstance = new fabric.Image(bgImg, {
					scaleY: scale,
					scaleX: scale,
					left: fabricWidth / 2 - (bgImg.width * scale) / 2,
					top: fabricHeight / 2 - (bgImg.height * scale) / 2,
				});
				let isResolved = false;
				// 添加 after:render 事件监听器
				const handleAfterRender = () => {
					if (!isResolved) {
						fabricStage.off("after:render", handleAfterRender); // 移除监听器以避免重复触发
						isResolved = true;
						this.loadBackImgState = true;
						resolve();
					}
				};
				// 监听图像加载完成
				this.c.setBackgroundImage(imgInstance, () => {
					fabricStage.on("after:render", handleAfterRender);
					this.c.requestRenderAll();
				});
			} catch (error) {
				console.log(error, "setCanvasBg", src);
				this.loadBackImgState = true;
				reject(error);
			}
		});
	},
	getClipPath() {
		return this.c.getObjects().filter((o) => o.id === "editArea")[0];
	},
	getElementByType(type) {
		return this.c.getObjects(type);
	},
	async replaceImg(src, selectedEle, property = {}) {
		if (!this.c) {
			return false;
		}
		this.loadImgState = false;
		let img = await loadImage(src),
			sw = selectedEle.width * selectedEle.scaleX,
			sh = selectedEle.height * selectedEle.scaleY,
			ow = img.width,
			oh = img.height,
			scale,
			top,
			left;
		scale = Math.sqrt((sw * sw + sh * sh) / (ow * ow + oh * oh));
		top = sh - oh * scale;
		left = sw - ow * scale;
		selectedEle.setElement(img);
		selectedEle
			.set({
				scaleX: scale,
				scaleY: scale,
				top: selectedEle.top + top / 2,
				left: selectedEle.left + left / 2,
				id: property?.id || "",
				customData: {
					...property,
				},
			})
			.setCoords();
		this.loadImgState = true;
		this.c.requestRenderAll();
		return selectedEle;
	},
	repeatImg(src, property) {
		this.deEleById("tileImg");
		// 创建一个图片元素
		return new Promise(async (resolve) => {
			let img = await loadImage(urlAddVersion(src));
			// 创建多个矩形对象，并应用随机填充图片样式和位置
			let positionMap = {
				0: {
					left: 290,
					top: 100,
					angle: 0,
				},
				1: {
					left: 345,
					top: 100,
					angle: 0,
				},
				2: {
					left: 320,
					top: 150,
					angle: 0,
				},
				3: {
					left: 300,
					top: 220,
					angle: 0,
				},
				4: {
					left: 250,
					top: 300,
					angle: 0,
				},
				5: {
					left: 300,
					top: 280,
					angle: 0,
				},
				6: {
					left: 200,
					top: 340,
					angle: 0,
				},
				7: {
					left: 125,
					top: 380,
					angle: 0,
				},
				8: {
					left: 180,
					top: 390,
					angle: 0,
				},
				9: {
					left: 280,
					top: 340,
					angle: 0,
				},
				10: {
					left: 350,
					top: 260,
					angle: 0,
				},
			};
			for (let i = 0; i < 11; i++) {
				if (!positionMap[i]) {
					continue;
				}
				let imgInstance = new fabric.Image(img, {
					...property,
					selectable: false,
					evented: false,
					...positionMap[i],
					id: "tileImg",
				});
				let scale = (this.c.width > this.c.height ? this.c.height / img.height : this.c.width / img.width) * 0.08;
				imgInstance.scale(scale).setCoords();
				this.c.add(imgInstance);
			}
			resolve();
		});
	},
	addImg(src, property) {
		return new Promise(async (resolve) => {
			const { imgProperty, ...otherProperty } = property;
			console.log(imgProperty, "imgProperty");
			this.loadImgState = false;
			let img = await loadImage(urlAddVersion(src));
			let getClipPath = this.getClipPath();
			let imgInstance = new fabric.Image(img, {
				...otherProperty,
			});
			let scale = (getClipPath.width > getClipPath.height ? getClipPath.height / img.height : getClipPath.width / img.width) * 0.8;
			imgInstance.scale(scale).setCoords();
			let groupWidth = getClipPath.getScaledWidth();
			let groupHeight = getClipPath.getScaledHeight();
			let imgWidth = imgInstance.getScaledWidth(),
				imgHeight = imgInstance.getScaledHeight();
			let left = (groupWidth - imgWidth) / 2 + getClipPath.left;
			let top = (groupHeight - imgHeight) / 2 + getClipPath.top;
			imgInstance
				.set({
					left: left,
					top: top,
					visible: !this.isMask,
					...imgProperty,
					left: imgProperty.left || left,
					top: imgProperty.top || top,
				})
				.setCoords();
			if (this.clipPath) {
				imgInstance.clipPath = this.clipPath;
			}
			this.loadImgState = true;
			this.c.add(imgInstance);
			this.c.requestRenderAll();
			resolve(imgInstance);
		});
	},
	addImg2(src, property, userDragOption={}, noCircle) {
		return new Promise(async (resolve) => {
			const dragOption={ ...{ selectable: false, useSelfClipPath: false }, ...userDragOption };
			this.loadImgState = false;
			let img = await loadImage(urlAddVersion(src));
			let getClipPath = this.getClipPath();

			// 创建圆形剪辑路径，直径为最短边
			let minDimension=Math.min(img.width,img.height);
			if (noCircle){
				minDimension=Math.sqrt(img.width*img.width+img.height*img.height);
			}
			let clipCircle=new fabric.Circle({
				radius: minDimension/2,
				originX: "center",
				originY: "center",
			});
			let { textTop, imgProperty, ...otherProperty } = property;
			let selectableObj={ selectable: dragOption.selectable, evented: dragOption.selectable };
			// 创建并配置图像实例
			let imgInstance = new fabric.Image(img, {
				...otherProperty,
				clipPath: clipCircle,
				visible: !this.isMask,
				...selectableObj, // 如果不需要选择该图像，可以设置为false
			});
			let drawAreaMinDimension=Math.min(getClipPath.width, getClipPath.height);
			let scale=drawAreaMinDimension/minDimension;
			if (noCircle){
				scale=scale*0.6
			}
			imgInstance.scale(scale).setCoords();
			let groupWidth = getClipPath.getScaledWidth();
			let groupHeight = getClipPath.getScaledHeight();
			let imgWidth = imgInstance.getScaledWidth(),
				imgHeight = imgInstance.getScaledHeight();
			let left = (groupWidth - imgWidth) / 2 + getClipPath.left;
			let top = (groupHeight - imgHeight) / 2 + getClipPath.top;
			imgInstance
				.set({
					left: left,
					top: top,
					...imgProperty,
					left: imgProperty?.left || left,
					top: imgProperty?.top || top,
				})
				.setCoords();
			if (dragOption.useSelfClipPath && this.clipPath) {
				imgInstance.clipPath = this.clipPath;
			}
			this.c.add(imgInstance);
			if (textTop) {
				//将文本组设置到最高图层
				let textArrDom = canvas.getElementByType("group");
				textArrDom.forEach((group) => {
					if (group.slefType == "textPathGroup") {
						group.bringToFront();
					}
				});
			}
			this.loadImgState = true;
			let arr = this.c.getObjects("image");
			resolve(imgInstance);
		});
	},
	addImg3(src, property) {
		return new Promise(async (resolve) => {
			try {
				if (!src){
					console.log('addImg3 没有 src');
					return false;
				}
				this.loadImgState=false;
				let img=await loadImage(urlAddVersion(src));
				let getClipPath=this.getClipPath();
				// 创建并配置图像实例
				let imgInstance=new fabric.Image(img, {
					...property,
					visible: !this.isMask,
					selectable: false, // 如果不需要选择该图像，可以设置为false
					evented: false,
				});
				let scale=(getClipPath.width>getClipPath.height? getClipPath.height/img.height:getClipPath.width/img.width)*0.95;
				imgInstance.scale(scale).setCoords();
				let groupWidth=getClipPath.getScaledWidth();0
				let groupHeight=getClipPath.getScaledHeight();
				let imgWidth=imgInstance.getScaledWidth(),
					imgHeight=imgInstance.getScaledHeight();
				let left=(groupWidth-imgWidth)/2+getClipPath.left;
				let top=(groupHeight-imgHeight)/2+getClipPath.top;
				imgInstance
					.set({
						left: left,
						top: top,
					})
					.setCoords();

				this.c.add(imgInstance);
				this.loadImgState=true;
				let arr=this.c.getObjects("image");
				resolve(imgInstance);
			} catch (error) {
				console.log(error,'addImg3');
			}
		});
	},
	addText(val = "CustomText", property = {}) {
		let groupObj = this.getClipPath();
		if (!groupObj) {
			return false;
		}
		let fill = "#ffffff";
		let colorName = "white";
		let Text = new fabric.IText(val, {
			fontSize: 18, // 字体大小
			fontWeight: "normal", // 字体粗细
			fontFamily: "Arial",
			fill,
			colorName: colorName,
			...property,
		});
		let textWidth = Text.width;
		let textHeight = Text.height;
		let groupWidth = groupObj.getScaledWidth();
		let groupHeight = groupObj.getScaledHeight();
		let left = (groupWidth - textWidth) / 2 + groupObj.left;
		let top = (groupHeight - textHeight) / 2 + groupObj.top;
		Text.set({
			left: left,
			top: top,
			visible: !this.isMask,
			/* originX: "center",
			originY: "center", */
		});
		if (this.clipPath) {
			Text.clipPath = this.clipPath;
		}
		this.c.add(Text);
		return Text;
	},
	addText2(val, points, options, scaleFactor, classOption) {
		let newPoints = this.nowpoints(points, scaleFactor);
		if (classOption) {
			this.textClass.setClassOption(classOption);
		}
		let textArr = this.textClass.addTextToCanvas(val, newPoints, options, {
			selectable: false,
			evented: false,
			id: options.id,
		});
		if (textArr) {
			this.c.add(textArr);
			this.c.requestRenderAll();
		}
	},
	//皮带扣版本的文字渲染
	addText3(val, points, options, scaleFactor) {
		let newPoints = this.nowpoints(points, scaleFactor);
		const { id, maxChars, pathType, ...otherOptions } = options;
		const nowOptions = {
			text: val,
			style: {
				...otherOptions,
			},
			points: newPoints,
			pathType: pathType == "round" ? "circle" : this.pathType[newPoints.length],
			maxChars,
			fabricOptions: {
				id: id,
			},
		};
		let textArr = this.textClass2.render(nowOptions, null, fabric);

		if (textArr) {
			this.c.add(textArr);
			this.c.requestRenderAll();
		}
	},
	updateTextArrById(id, val, points, options, scaleFactor, classOption) {
		let newPoints = this.nowpoints(points, scaleFactor);
		if (classOption) {
			this.textClass.setClassOption(classOption);
		}
		let textArr = this.textClass.addTextToCanvas(val, newPoints, options, {
			selectable: false,
			evented: false,
			id: id,
		});
		let textArrDom = canvas.getElementByType("group");
		let findGroup = textArrDom.find((c) => c.id === id);
		if (findGroup) {
			this.c.remove(findGroup);
			this.c.add(textArr);
			this.c.requestRenderAll();
		}
		return { textArr };
	},
	updateTextArrById2(id, val, points, options, scaleFactor) {
		let newPoints = this.nowpoints(points, scaleFactor);
		const { maxChars, pathType, ...otherOptions } = options;
		const nowOptions = {
			text: val,
			style: {
				...otherOptions,
			},
			points: newPoints,
			pathType: pathType == "round" ? "circle" : this.pathType[newPoints.length],
			maxChars,
			fabricOptions: {
				id: id,
			},
		};

		let textArr = this.textClass2.render(nowOptions, null, fabric);
		let textArrDom = canvas.getElementByType("group");
		let findGroup = textArrDom.find((c) => c.id === id);
		if (findGroup) {
			this.c.remove(findGroup);
			this.c.add(textArr);
		} else {
			this.c.add(textArr);
		}
		this.c.requestRenderAll();
		return { textArr };
	},
	delTextById(id) {
		let canvasC = this.c;
		let arr = canvasC.getObjects("i-text");
		let findText = arr.find((c) => c.id === id);
		if (findText) {
			canvasC.remove(findText);
			canvasC.requestRenderAll();
		}
	},
	deEleById(id) {
		let canvasC = this.c;
		let arr = canvasC.getObjects();
		arr.forEach((item) => {
			if (item.id === id) {
				canvasC.remove(item);
				canvasC.requestRenderAll();
			}
		});
	},
	deImgById(id) {
		let canvasC = this.c;
		let arr = canvasC.getObjects("image");
		let findImg = arr.find((c) => c.id === id);
		if (findImg) {
			canvasC.remove(findImg);
			canvasC.requestRenderAll();
		}
	},
	deImgById2(id) {
		return new Promise((resolve, reject) => {
			let canvasC = this.c;
			let arr = canvasC.getObjects("image");
			if (!arr.length) {
				resolve();
			}
			let findImg = arr.find((c) => c.id === id);
			if (findImg) {
				canvasC.remove(findImg);
				// 添加 after:render 事件监听器
				const handleAfterRender = () => {
					canvasC.off("after:render", handleAfterRender); // 移除监听器以避免重复触发
					resolve();
				};
				canvasC.on("after:render", handleAfterRender);
				canvasC.requestRenderAll();
			} else {
				resolve();
			}
		});
	},
	changeElementPosition(data) {
		let { item, position } = data,
			groupObj = this.getClipPath();
		if (position === "left") {
			// 获取参考元素和旋转元素的边界框信息
			let referenceRect = groupObj.getBoundingRect();
			let rotatedRect = item.getBoundingRect();
			let offsetX = referenceRect.left - rotatedRect.left;
			item.set({
				left: item.left + offsetX,
			});
			item.setCoords();
		} else if (position === "center") {
			this.c._centerObject(item, new fabric.Point(groupObj.getCenterPoint().x, item.getCenterPoint().y));
		} else {
			let referenceRect = groupObj.getBoundingRect();
			let rotatedRect = item.getBoundingRect();
			let offsetX = referenceRect.left + referenceRect.width - (rotatedRect.left + rotatedRect.width);
			item.set({
				left: item.left + offsetX,
			});
			item.setCoords();
		}
		this.c.renderAll();
	},

	getTextProperty(ele) {
		if (!ele) {
			return;
		}
		let obj = {};
		console.log(ele.get("text"));
		obj.type = ele.type;
		obj.text = ele.get("text");
		obj.textAlign = ele.get("textAlign");
		obj.angle = Math.round(ele.get("angle"));
		obj.scale = parseInt(parseInt(ele.get("scaleX")).toFixed(2));
		obj.strokeWidth = ele.get("strokeWidth");
		obj.charSpacing = ele.get("charSpacing");
		obj.lineHeight = ele.get("lineHeight");
		obj.fill = ele.get("fill");
		obj.colorName = ele.get("colorName");
		obj.stroke = ele.get("stroke");
		obj.fontSize = ele.get("fontSize");
		obj.fontWeight = ele.get("fontWeight");
		obj.fontStyle = ele.get("fontStyle");
		obj.underline = ele.get("underline");
		obj.fontFamily = ele.get("fontFamily");
		if (ele.type === "curved-text") {
			obj.flipped = ele.get("flipped");
			obj.radian = ele.get("radian");
		}
		obj.id = ele.get("id");
		return obj;
	},
	getImageProperty(ele) {
		if (!ele) {
			return;
		}
		let obj = {};
		obj.IsMain = parseInt(ele.IsMain) || 0;
		obj.angle = Math.round(ele.get("angle"));
		obj.scale = parseInt(ele.get("scaleX").toFixed(3));
		obj.picPath = obj.secure_url = ele.getSrc();
		obj.id = ele.get("id");
		let customData = ele.get("customData");
		if (customData) {
			obj = Object.assign({}, customData, obj);
		}
		return obj;
	},
	changeTextProperty(data) {
		let { val, property, item } = data;
		if (val === undefined) {
			return false;
		}
		if (!this.c) {
			return false;
		}
		if (!item) {
			return false;
		}
		if (property === "angle") {
			item.rotate(val).setCoords();
		} else if (property === "fill") {
			item.set("fill", val.code);
			item.set("colorName", val.pantone);
		} else {
			item.set(property, val);
		}
		this.c.requestRenderAll();
	},
	getJson() {
		return this.c.toJSON(["id", "customData", "selectable", "evented"]);
	},
	big() {
		let zoomRatio = this.c.getZoom();
		zoomRatio += 0.3;
		const center = this.c.getCenter();
		this.c.zoomToPoint(new fabric.Point(center.left, center.top), zoomRatio);
		this.isBig = true;
	},
	// 缩小
	small() {
		let zoomRatio = this.c.getZoom();
		zoomRatio -= 0.3;
		const center = this.c.getCenter();
		this.c.zoomToPoint(new fabric.Point(center.left, center.top), zoomRatio < 0 ? 0.01 : zoomRatio);
		this.isBig = false;
	},
	centerH(workspace, object) {
		return this.c._centerObject(object, new fabric.Point(workspace.getCenterPoint().x, object.getCenterPoint().y));
	},
	center(workspace, object) {
		const center = workspace.getCenterPoint();
		return this.c._centerObject(object, center);
	},
	centerV(workspace, object) {
		return this.c._centerObject(object, new fabric.Point(object.getCenterPoint().x, workspace.getCenterPoint().y));
	},
	autoPosition(position, elementArr) {
		this.c.discardActiveObject();
		let textArr = this.getElementByType("i-text"),
			imgArr = elementArr || this.getElementByType("image"),
			group = this.getClipPath();
		//先定位图片的位置，在根据图片位置定位文本
		let arr = imgArr.concat(textArr),
			picOffsetX = 0,
			picOffsetY = 0;
		arr.forEach((item) => {
			let type = item.type;
			item.rotate(0).setCoords();
			this.center(group, item);
			switch (position) {
				case "right":
					if (type === "image") {
						this.changeElementPosition({
							item,
							position: "left",
						});
						picOffsetX = Math.max(picOffsetX, item.left + item.getScaledWidth());
					}
					if (type === "i-text") {
						item.set({
							left: picOffsetX,
						});
					}
					break;
				case "left":
					if (type === "image") {
						this.changeElementPosition({
							item,
							position: "right",
						});
						if (picOffsetX === 0) {
							picOffsetX = Math.max(picOffsetX, item.left);
						} else {
							picOffsetX = Math.min(picOffsetX, item.left);
						}
					}
					if (type === "i-text") {
						item.set({
							left: picOffsetX - item.getScaledWidth(),
						});
					}
					break;
				case "down":
					if (type === "image") {
						item.set({
							top: group.top,
						});
						picOffsetY = Math.max(picOffsetY, item.top + item.getScaledHeight());
					}
					if (type === "i-text") {
						item.set({
							top: picOffsetY,
						});
					}
					break;
				case "up":
					if (type === "image") {
						item.set({
							top: group.top + group.getScaledHeight() - item.getScaledHeight(),
						});
						if (picOffsetY === 0) {
							picOffsetY = Math.max(picOffsetY, item.top);
						} else {
							picOffsetY = Math.min(picOffsetY, item.top);
						}
					}
					if (type === "i-text") {
						item.set({
							top: picOffsetY - item.getScaledHeight(),
						});
					}
					break;
			}
		});
		this.c.setActiveObject(new fabric.ActiveSelection(arr, { canvas: this.c }));
		//自适应比例
		let newGroup = this.group();
		let newGroupW = newGroup.getScaledWidth(),
			newGroupH = newGroup.getScaledHeight(),
			groupW = group.getScaledWidth(),
			groupH = group.getScaledHeight();
		if (newGroupW > groupW || newGroupH > groupH) {
			let scaleX = groupW / newGroupW,
				scaleY = groupH / newGroupH;
			let scale = Math.min(scaleX, scaleY);
			newGroup.scaleX = scale;
			newGroup.scaleY = scale;
		}
		// 将选中的元素居中
		this.center(group, newGroup);
		this.unGroup();
		this.c.discardActiveObject();
		this.c.requestRenderAll();
	},
	activeElementById(id) {
		let arr = this.c.getObjects();
		let find = arr.find((c) => c.id === id);
		if (find) {
			this.c.setActiveObject(find);
			this.c.requestRenderAll();
		}
	},
	unGroup() {
		let canvas = this.c;
		if (!canvas.getActiveObject()) {
			return;
		}
		if (canvas.getActiveObject().type !== "group") {
			return;
		}
		canvas.getActiveObject().toActiveSelection();
		canvas.discardActiveObject().requestRenderAll();
	},
	group() {
		let canvas = this.c;
		if (!canvas.getActiveObject()) {
			return;
		}
		if (canvas.getActiveObject().type !== "activeSelection") {
			return;
		}
		const containGroup = (arr) => {
			if (arr.find((item) => item.type === "group")) {
				return true;
			}
		};
		if (containGroup(canvas.getActiveObject().getObjects())) {
			return;
		}
		let newgroup = canvas.getActiveObject().toGroup();
		newgroup.set("id", generateUUID());
		canvas.discardActiveObject().requestRenderAll();
		canvas.setActiveObject(newgroup).requestRenderAll();
		return newgroup;
	},

	clearTextAndImg() {
		let textArr = this.getElementByType("i-text"),
			imgArr = this.getElementByType("image"),
			arr = imgArr.concat(textArr);
		if (arr.length) {
			arr.forEach((item) => {
				this.c.remove(item);
			});
			this.c.requestRenderAll();
		}
	},
	//删除所有文字
	clearTexts() {
		let arr = this.getElementByType("i-text");
		if (arr.length) {
			arr.forEach((item) => {
				this.c.remove(item);
			});
			this.c.requestRenderAll();
		}
	},
	//删除所有图片
	clearImgs() {
		let arr = this.getElementByType("image");
		if (arr.length) {
			arr.forEach((item) => {
				this.c.remove(item);
			});
			this.c.requestRenderAll();
		}
	},
};
export default canvas;

/**
 * 画布canvas组件
 * demo:
 * import publicCanvas from "@/assets/js/halfDesign/canvas";
 //删除canvas里面的 文字和图片.
 publicCanvas.clearTextAndImg();
 */
