import {request} from '~/utils/request'

//获取角色列表
export function getRoleList(data) {
	return request({
		url: '/retailer/role/list',
		method: 'get',
		params: data,
	})
}

//根据角色id获取用户
export function getUserListByRoleId(data) {
	return request({
		url: '/retailer/user/getUserListByRoleId',
		method: 'get',
		params: data,
	})
}

//添加角色
export function addRole(data) {
	return request({
		url: '/retailer/role/add',
		method: 'post',
		data,
	})
}

//编辑角色
export function updateRole(data) {
	return request({
		url: '/retailer/role/update',
		method: 'post',
		data,
	})
}

//删除角色
export function delRoleById(data) {
	return request({
		url: '/retailer/role/delById',
		method: 'get',
		params: data,
	})
}

//用户获取权限列表
export function getPermissionByUserAndPro(data) {
	return request({
		url: '/retailer/permission/getPermissionByUserAndPro',
		method: 'post',
		data
	})
}

//获取所有权限
export function getPermissionList(data) {
	return request({
		url: '/retailer/permission/list',
		method: 'get',
		params: data
	})
}

//角色绑定权限
export function roleAndPermissionBinding(data) {
	return request({
		url: '/retailer/permission/roleAndPermissionBinding',
		method: 'post',
		data: data
	})
}

//添加用户
export function addUser(data) {
	return request({
		url: '/retailer/user/addUser',
		method: 'post',
		data: data
	})
}

//经销商用户登录
export function login(data) {
	return request({
		url: '/retailer/user/login',
		method: 'post',
		data: data
	})
}

//用户绑定角色
export function userRoleBinding(data) {
	return request({
		url: '/retailer/role/userRoleBinding',
		method: 'post',
		data: data
	})
}

//发送重置密码验证码
export function sendResetCode(data) {
	return request({
		url: '/retailer/user/sendResetCode',
		method: 'post',
		data: data
	})
}

//重置密码
export function resetPasswordByCode(data) {
	return request({
		url: '/retailer/user/resetPasswordByCode',
		method: 'post',
		data: data
	})
}

//验证是否有邀请权限
export function confirmHaveUser(data) {
	return request({
		url: '/retailer/user/confirmHaveUser',
		method: 'get',
		params: data
	})
}

//获取角色详情
export function getRoleInfoById(data) {
	return request({
		url: '/retailer/role/getById',
		method: 'get',
		params: data
	})
}

//获取用户列表
export function getUserListByProId(data) {
	return request({
		url: '/retailer/user/getUserListByProId',
		method: 'get',
		params: data
	})
}

//获取用户信息
export function getUserInfoByIdAndProId(data) {
	return request({
		url: '/retailer/user/getUserInfoByIdAndProId',
		method: 'get',
		params: data
	})
}

//用户绑定多个角色
export function userRoleListBind(data) {
	return request({
		url: '/retailer/role/userRoleListBind',
		method: 'post',
		data: data
	})
}

//删除用户
export function batchDeleteProUser(data) {
	return request({
		url: '/retailer/user/batchDeleteProUser',
		method: 'post',
		data: data
	})
}

//获取支付列表
export function getPayList(data) {
	return request({
		url: '/retailer/pay/pay-config/list',
		method: 'get',
		params: data
	})
}

//更新支付信息
export function updatePayParams(data) {
	return request({
		url: '/retailer/pay/pay-config/update',
		method: 'post',
		data: data
	})
}

//获取token
export function getPaypalToken(data) {
	return request({
		url: '/pay/paypal-token',
		method: 'post',
		data: data
	})
}

//前台获取支付配置
export function getPayConfig(data) {
	return request({
		url: '/app/pay/paypal-token-config',
		method: 'post',
		data: data
	})
}

//获取所有者信息
export function getSysProOwnerByProId(data) {
	return request({
		url: '/retailer/projectOwner/getSysProOwnerByProId',
		method: 'get',
		params: data
	})
}

//获取国家列表
export function getCountryList(data) {
	return request({
		url: '/retailer/address/getCountryList',
		method: 'get',
		params: data
	})
}

//获取州
export function getStateList(data) {
	return request({
		url: '/retailer/address/getState',
		method: 'post',
		data
	})
}

//编辑所有者信息
export function editSysProOwner(data) {
	return request({
		url: '/retailer/projectOwner/editSysProOwner',
		method: 'post',
		data
	})
}

//编辑项目
export function editProject(data) {
	return request({
		url: '/retailer/webSite/editProject',
		method: 'post',
		data
	})
}

//添加成员
export function getProIsCanHaveNewManage(data) {
	return request({
		url: '/retailer/user/proIsCanHaveNewManage',
		method: 'get',
		params: data,
	})
}

// 经销商入职stripe多方支付
export function newStripeAccount(id) {
	return request({
		url: '/retailer/pay/stripe/createAccount?proId='+id,
		method: "get",
	})
}

// 更新经销商网站收款配置
export function stripePayUpdate(data) {
	return request({
		url: '/retailer/pay/pay-config/update',
		method: "post",
		data: data,
	})
}

//获取省份
export function getState(data) {
	return request({
		url: '/address/country/getState',
		method: 'post',
		data: data,
	})
}
//获取所有语言
export function getLanguageByProId (params) {
	return request({
		url: '/retailer/language/getLanguageByProId',
		params,
	})
}
//设置默认国家
export function setIsCountry (data) {
	return request({
		url: '/retailer/language/setIsCountry',
		data,
		method:'post'
	})
}
//设置默认语言
export function setDefaultLanguage (data) {
	return request({
		url: '/retailer/language/setDefaultLanguage',
		data,
		method:'post'
	})
}
//启用，禁用国家语言
export function updateProjectStatus (data) {
	return request({
		url: '/retailer/language/updateProjectStatus',
		data,
		method:'post'
	})
}
//获取经销商未绑定语言
export function getProjectNotLanguage (params) {
	return request({
		url: '/retailer/language/getProjectNotLanguage',
		params,
	})
}

//保存
export function batchSaveProductLanguage (data) {
	return request({
		url: '/retailer/language/batchSaveProductLanguage',
		data,
		method:'post'
	})
}


//保存域名
export function updateCountryDomainUrl (data) {
	return request({
		url: '/retailer/language/updateCountryDomainUrl',
		data,
		method:'post'
	})
}
