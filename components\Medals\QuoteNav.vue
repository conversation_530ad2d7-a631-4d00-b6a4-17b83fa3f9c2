<template>
	<article class="quoteNav" v-if="this.cateList.length && !$route.query.isBack">
		<p class="title">{{ title }}</p>
		<div class="mySwiper1">
			<div class="swiper" ref="mySwiper1">
				<div class="swiper-wrapper">
					<div class="swiper-slide" v-for="item in cateList" :key="item.id" :disabled="item.id === pid ? true : false">
						<div class="quoteCard" :class="[item.id === pid ? 'cardActiveMB' : '', noPreview ? '' : 'preview-nav']" @click="linkTo(item)">
							<div class="biaoqian">
								<div class="biaoqian_text" :style="{background:getClassifyTagTextColor(item)}">{{ getTranslate(item) }}</div>
							</div>
							<div class="card-box">
								<img :src="item.id === pid ? item.recommendPhoto3 : item.recommendPhoto"  alt=""/>
								<div class="stitle" :class="item.id === pid ? 'stitleActive' : ''">
									<strong>{{ item.cateName }}</strong>
								</div>
							</div>
							<div class="card-hover" v-if="item.recommendPhoto2">
								<img :src="item.recommendPhoto2"  alt=""/>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="swiper-button-prev pagingIcon" ></div>
			<div class="swiper-button-next pagingIcon" ></div>
		</div>
	</article>
</template>
<script>
import { medalsApi } from "@/api/medals/medals";

export default {
	data() {
		return {
			cateList: [],
			swiper1: null,
		};
	},
	props: {
		pid: {
			type: [Number, String],
		},
		title: {
			type: String,
		},
		customData: {
			type: Object,
			default: () => {
				return {};
			},
		},
		noPreview: {
			type: Boolean,
			default: false,
		},
		config: {
			type: Object,
			default: () => {},
		},
	},
	computed: {
		lang() {
			return this.$store.getters.lang.quote || {};
		},
		proId() {
			return this.$store.state.proId;
		},
		device() {
			return this.$store.state.device;
		},
		isMobile() {
			return this.$store.getters.isMobile;
		},
	},
	methods: {
		getTranslate(item) {
			if(item.configJson){
				return JSON.parse(item.configJson)?.classifyTagText_translate
			}else{
				return ""
			}
		},
		getClassifyTagTextColor(item) {
			if(item.configJson){
				return JSON.parse(item.configJson)?.classifyTagTextColor1
			}else{
				return ""
			}
		},

		linkTo(val) {
			if (val.id == "-1") {
				window.open(val.customHref);
				return;
			}
			this.$router.push({
				path: val.quoteRoutingName,
				query: this.$route.query,
			});
		},
		getAppRecommendCateList() {
			medalsApi.getAppRecommendCateList({ proId: this.proId, id: this.pid }).then((res) => {
				this.cateList = res.data;
				if (Object.keys(this.customData).length > 0) {
					this.cateList.push(this.customData);
				}
				this.swiperInit();
			});
		},
		swiperInit() {
			this.$nextTick(() => {
				this.swiper1 = new Swiper(this.$refs.mySwiper1, {
					slidesPerView: 8,
					spaceBetween: 6,
					slidesPerGroup: 3,
					loop: false,
					loopFillGroupWithBlank: true,
					watchSlidesVisibility: true,
					navigation: {
						nextEl: ".mySwiper1 .swiper-button-next",
						prevEl: ".mySwiper1 .swiper-button-prev",
					},
					...this.config,
					breakpoints: {
						300: {
							slidesPerView: 3,
						},
                        750: {
                            slidesPerView: 4,
                        },
                        1000: {
                            slidesPerView: this.config?.slidesPerView || 6,
                        },
                        1400: {
                            slidesPerView: this.config?.slidesPerView || 8,
                        },
					},
				});
			});
		},
	},
	mounted() {
		this.getAppRecommendCateList();
	},
};
</script>
<style lang="scss">
[disabled] {
	pointer-events: none;

	button {
		opacity: 0.5;
	}
}

.stitleActive {
	color: #537cf2;
}

.cardActiveMB {
	text-align: center;
	position: relative;
}

.quoteNav {
	grid-column: 1/49;
	padding-bottom: 20px;
	@include respond-to(mb) {
		padding-bottom: 0;
	}

	.quoteCard {
		position: relative;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		cursor: pointer;

		.biaoqian{
			position: relative;
			width: 100%;

			.biaoqian_text{
				position: absolute;
				left: 0;
				font-size: 14px;
				color: rgba(255, 255, 255, 1);
				line-height: 22px;
				padding: 0 8px;
    			border-radius: 8px 0 8px 0;

				@include respond-to(mb) {
					position: absolute;
					font-size: 11px;
					color: white;
					line-height: 18px;

				}
			}
		}

		.stitle {
            text-align: center;
			font-weight: bold;
			@include respond-to(mb) {
                font-size: 12px;
			}
		}

		.card-box {
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
		}

		.card-hover {
            position: absolute;
            inset: 0;
			display: none;
            img{
                width: 100%;
                height: 100%;
                object-fit: cover;
            }
		}

		img {
			width: 100% !important;
			height: 224px;
			object-fit: contain;
            @include respond-to(mb){
                height: auto;
            }
		}

		button {
			border: 1px solid $color-primary;
			color: #333333;
			padding: 5px 35px;
			background: #f2f5f7;
			@media screen and (max-width: 767px) {
				font-size: 12px;
				padding: 5px 15px;
			}
		}

		@media (any-hover: hover) {
			&.preview-nav:hover {
				.card-hover {
					display: block;
				}

				button {
					color: white;
					border-color: transparent;
					border-radius: 6px;
					background: $color-primary;
				}
			}
		}
	}

	.title {
		margin-bottom: 20px;
		font-size: 20px;
		font-weight: bold;
		text-align: center;
		position: relative;

        @include respond-to(mb){
            font-size: 16px;
        }

		&::after {
			background-color: #333;
			bottom: -10px;
			content: "";
			height: 2px;
			position: absolute;
			width: 124px;
			left: 50%;
			transform: translate(-50%, 0);
		}
	}

	.mySwiper1 {
		position: relative;
		padding: 0 100px;
		@include respond-to(mb) {
			padding: 0 35px;
		}
	}

	.pagingIcon {
		z-index: 10;
		width: 3.5em;
		height: 3.5em;
		line-height: 3.5em;
		text-align: center;
		background: #ffffff;
		box-shadow: 0 2px 6px 0 rgba(23, 29, 67, 0.3);
		border-radius: 50%;
		border: 1px solid #dbdbdb;
		position: absolute;
		top: 40%;
		color: #000;
		@include respond-to(mb) {
			width: 24px;
			height: 24px;
			z-index: 10;
			cursor: pointer;
			display: flex;
			align-items: center;
			justify-content: center;
			color: #ffffff;
			background-color: rgba(0, 0, 0, 0.5);
			box-shadow: none;
			border: none;
			padding: 2px;
		}
	}

	.pagingIcon::after {
		font-size: 1.5em;
		font-weight: 800;
		@include respond-to(mb) {
			font-size: 1em;
		}
	}

	.swiper-button-prev {
		left: 25px;
		@include respond-to(mb) {
			left: 5px;
		}
	}
	.swiper-button-next {
		right: 25px!important;
		@include respond-to(mb) {
			right: 5px!important;
		}
	}
}
</style>
