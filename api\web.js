import { request } from "@/utils/request";

//获取proId
export function getProId(url, routingName) {
	if (process.env.NODE_ENV === "development")
		switch (process.env.MODE) {
			case "dev_web":
				url = "bps.a1gifts.biz";
				break;
			case "beta_web":
				url = "bps.a1gifts.biz";
				break;
			default:
			url= "www.luggagetags.com";
		}

	return request({
		url: "/app/systemProject/getProByUrlAndOwner",
		method: "get",
		params: { url, routingName },
	});
}

//获取uuid
export function getUuid() {
	return request({
		url: "/app/cart/getCartUUID",
		method: "post",
	});
}

//获取页头页尾数据
export function getLayoutData(data) {
	return request({
		url: "/app/retailer/page/getHeadFootPages",
		method: "post",
		data,
	});
}

//获取导航列表
export function getNavList(data) {
	return request({
		url: "/app/header/getHeaderList",
		method: "get",
		params: data,
	});
}

//获取定制类产品的modal-custom模板的数据
export function getCustomProductList(data) {
	return request({
		url: "/quote/cate/getSemiQuoteProductList",
		method: "get",
		params: data,
	});
}
//获取爆款产品的modal-custom模板的数据
export function getHotProductList(data) {
	return request({
		url: "/app/products/getHotProductList",
		method: "get",
		params: data,
	});
}
// 获取爆款产品的modal-custom模板的数据,带有收藏与角标
export function getHotProductListTwo(data) {
	return request({
		url: "/app/products/getProductListByChildCateId",
		method: "post",
		data,
	});
}
//获取半定制父分类及其所有子类列表
export function getProductRetailerAllHalfCate(params) {
	return request({
		url: "/retailer/product/getRetailerAllHalfCateAndChildCate",
		method: "get",
		params,
	});
}
//获取所有有该子类标签的上架产品列表
export function getProductListByChildCateId(params) {
	return request({
		url: "/retailer/product/getProductListByChildCateId",
		method: "get",
		params,
	});
}
//获取产品及其标签列表
export function getAppRingCateAndTagList(data) {
	return request({
		url: "/quote/ring/templates/getAppRingCateAndTagList",
		method: "get",
		params: data,
	});
}
//通过产品和标签获取模板的数据
export function getAppRingTemplatesList(data) {
	return request({
		url: "/quote/ring/templates/getAppRingTemplatesList",
		method: "post",
		data,
	});
}

//获取半定制 medals产品模板列表
export function getMedalTemplatesList(data) {
	return request({
		url: "/app/products/productThemeList",
		method: "post",
		data,
	});
}

//获取启用的戒指模板标签列表
export function getAppRingTagList(params) {
	return request({
		url: "/quote/ring/templates/getAppRingTagList",
		method: "get",
		params,
	});
}

//根据页面获取产品图片展示列表
export function listPageImgByPageId(data) {
	return request({
		url: "/app/page/pageImg/listPageImgByPageId",
		method: "post",
		data: { ...data, pageName: window.$nuxt.$store.state.pagePath },
	});
}
//根据产品类型获取产品图片展示列表(带链接)
export function getGalleryByCateId(params) {
	return request({
		url: "/app/gallery/gallery/getGalleryByCateId",
		method: "get",
		params,
	});
}

//获取评论的modal-review模板的数据
export function getProCommentAggregates(params) {
	return request({
		url: "/app/systemProject/getProCommentAggregates",
		method: "get",
		params,
	});
}
//获取评论的modal-review模板的列表
export function getSiteComments(data) {
	return request({
		url: "/app/systemProject/getSiteComments",
		method: "post",
		data,
	});
}

//第三方登录
export function thirdPartyLogin(data) {
	return request({
		url: "/app/member/retailer/thirdPartyLogin",
		method: "post",
		data,
	});
}

//登录
export function login(data) {
	return request({
		url: "/app/member/loginByPassword",
		method: "post",
		data,
	});
}

//注册
export function register(data) {
	try {
		if (window.$nuxt.$store.state.advertConfig.googleConfig && window.gtag) {
			gtag("event", "select_content", {
				content_id: data.email,
				content_type: "register",
				content_value: data.firstName + " " + data.lastName,
			});
		}
	} catch (e) { }

	return request({
		url: "/app/member/retailer/register",
		method: "post",
		data,
	});
}

//是否登录
export function isLogin() {
	return request({
		url: "/app/member/isLogin",
		method: "post",
	});
}

//获取页面数据
export function getPageData(pageName) {
	return request({
		url: "/app/retailer/page/getPagesByPageName",
		method: "post",
		data: {
			pageName,
		},
	});
}

//保存访问记录
export function saveVisitRecord(data) {
	return request({
		url: "/app/datacollect/visits/saveVisitRecord",
		method: "post",
		data,
	});
}

//获取图稿确认数据
export function getConfirmData(data) {
	return request({
		url: "/app/customLinks/getByUrl",
		method: "post",
		data,
	});
}

//图稿删除
export function deleteDrawings(data) {
	return request({
		url: "/app/order/user/deleteDrawings",
		method: "post",
		data,
	});
}

//图稿确认
export function setConfirmData(data) {
	return request({
		url: "/app/order/user/drawingsConfirm",
		method: "post",
		data,
	});
}

//图稿下单
export function orderConfirmData(data) {
	return request({
		url: "/app/inquiry/changeInquiryStatus",
		method: "post",
		data,
	});
}

//获取购物车列表
export function getCartList(data) {
	return request({
		url: "/app/cart/getCartList",
		method: "post",
		data,
	});
}

//购物车点击产品去详情
export function goProDeatil(data) {
	return request({
		url: "/app/products/getProductById",
		method: "get",
		params: data,
	});
}

//删除购物车
export function delCartByIds(data) {
	return request({
		url: "/app/cart/delCartByIds",
		method: "post",
		data,
	});
}

//修改购物车评论
export function editCartComments(data) {
	return request({
		url: "/app/cart/editCartComments",
		method: "post",
		data,
	});
}

//合并购物车
export function cartMerge(data) {
	return request({
		url: "/app/cart/cartMerge",
		method: "post",
		data,
	});
}

//获取支付配置
export function getPaymentConfig(data) {
	return request({
		url: "/app/pay/paypal-token-config",
		method: "post",
		data,
	});
}

//未登录算价格
export function checkoutPriceNotLogin(data) {
	return request({
		url: "/app/order/user/checkoutNotLogin",
		method: "post",
		data,
	});
}

//登录算价格
export function checkoutPrice(data) {
	return request({
		url: "/app/order/user/checkout",
		method: "post",
		data,
	});
}

//登录添加订单
export function addOrder(data) {
	return request({
		url: "/app/order/user/addOrder",
		method: "post",
		data,
	});
}

//未登录下单
export function addOrderNotLogin(data) {
	return request({
		url: "/app/order/user/addOrderNotLogin",
		method: "post",
		data,
	});
}

//下单后计算税费
export function modifyTax(data) {
	return request({
		url: "/app/order/user/modifyTax",
		method: "post",
		data: data,
	});
}

//支付
export function paymentOfSchrodinger(data) {
	return request({
		url: "/app/order/user/paymentOfSchrodinger",
		method: "post",
		data: data,
	});
}

//验证支付
export function autoVerifyPayment(data) {
	return request({
		url: "/app/order/user/autoVerifyPayment",
		method: "post",
		data: data,
	});
}

//记录用户支付失败的次数
export function orderPayFail(data) {
	return request({
		url: "/app/order/user/addPaymentErrorTime",
		method: "post",
		data: data,
	});
}

//支付错误记录日志
export function paymentErrorLogs(data) {
	return request({
		url: "/app/pay/paymentErrorLogs/add",
		method: "post",
		data: data,
	});
}

//根据邮箱判断用户是否注册
export function isOldUser(data) {
	return request({
		url: "/app/member/user/isOldUser",
		method: "post",
		data: data,
	});
}

//获取地址列表
export function getAddressList(data) {
	return request({
		url: "/app/member/getAllAddressList",
		method: "get",
		params: data,
	});
}

//获取订单详情
export function getOrderInfo(data) {
	return request({
		url: "/app/order/user/getOrderInfo",
		method: "post",
		data: data,
	});
}

//获取
export function getLowestPrice(data) {
	return request({
		url: "/app/order/user/getLowestPrice",
		method: "post",
		data: data,
	});
}

//获取订单图稿确认数据
export function getOrderDrawing(data) {
	return request({
		url: "/app/order/user/getOrderDrawing",
		method: "post",
		data: data,
	});
}
export function getOrderDrawingByLink(data) {
	return request({
		url: "/app/order/user/getOrderDrawingByLink",
		method: "post",
		data: data,
	});
}

//订单图稿校验邮箱
export function checkDrawingLinkEmail(data) {
	return request({
		url: "/app/order/user/checkDrawingLinkEmail",
		method: "post",
		data: data,
	});
}

//订单图稿确认
export function drawingsConfirm(data) {
	return request({
		url: "/app/order/user/drawingsConfirm",
		method: "post",
		data: data,
	});
}

//修改订单支付方式
export function modifyPaymentMethod(data) {
	return request({
		url: "/app/order/user/modifyPaymentMethod",
		method: "post",
		data: data,
	});
}

//获取支付方式列表
export function getPayTypeList(data) {
	return request({
		url: "/app/pay/pay-type-list",
		method: "get",
		params: data,
	});
}

//信用卡支付
export function authorizeCredit(data) {
	return request({
		url: "/app/pay/authorize/credit",
		method: "post",
		data: data,
	});
}

export function memberCenterLogin(data) {
	return request({
		url: "https://enamelpins-dev.gs-souvenir.com/memberCenter/isLogin",
		method: "post",
		data: data,
	});
}

//stripe预支付获取支付密钥
export function paymentIntent(data) {
	return request({
		url: "/app/pay/stripe/create/paymentIntent",
		method: "post",
		data: data,
	});
}

//stripe预支付获取支付密钥  子订单
export function childPaymentIntent(data) {
	return request({
		url: "/app/pay/stripe/create/child/paymentIntent",
		method: "post",
		data: data,
	});
}

// 最小金额测试接口
export function minAmountIntent(data) {
	return request({
		url: "/app/pay/stripe/create/platform/paymentIntent",
		method: "post",
		data: data,
	});
}
export function getAccountId(data) {
	return request({
		url: "/app/pay/stripe/getAccountId",
		method: "get",
		params: data,
	});
}

// export function getALLHalfLabelList(data) {
// 	return request({
// 		url: "/app/label/getALLHalfLabelList",
// 		method: "get",
// 		params: data,
// 	});
// }

//获取类型下所有标签
export function getAppLabelAttributeList(data, tagData) {
	try {
		if (tagData && window.$nuxt.$store.state.advertConfig.googleConfig && window.gtag) {
			gtag("event", "select_content", {
				content_type: "productTag",
				content_value: tagData,
			});
		}
	} catch (error) { }
	return request({
		url: "/app/atrribute/getAppLabelAttributeList",
		method: "post",
		data: data,
	});
}

//medals 获取类型下所有标签(有三级)
export function getNewLabelAttributeList(data, tagData) {
	try {
		if (tagData && window.$nuxt.$store.state.advertConfig.googleConfig && window.gtag) {
			gtag("event", "select_content", {
				content_type: "productTag",
				content_value: tagData,
			});
		}
	} catch (error) { }
	return request({
		url: "/app/category/getCategoryParentChildList",
		method: "post",
		data: data,
	});
}

//查询产品详情页富文本
export function getProdctRichText(data) {
	return request({
		url: "/app/richText/getProdctRichText",
		method: "get",
		params: data,
	});
}

//相关推荐
export function findRelatedRecom(data) {
	return request({
		url: "/app/products/getRecommendProduct",
		method: "post",
		data,
	});
}

//半定制获取推荐产品
export function getSellingProduct(data) {
	return request({
		url: "/app/products/getSellingProduct",
		method: "post",
		data,
	});
}

//收藏
export function addCollection(data, productData) {
	//收藏成功之后 google记录
	try {
		if (window.$nuxt.$store.state.advertConfig.googleConfig && window.gtag && productData) {
			gtag("event", "add_to_wishlist", {
				currency: window.$nuxt.$store.state.currency.code,
				value: productData.value || 0,
				items: [productData.data],
			});
		}
	} catch (error) { }
	return request({
		url: "/app/collections/add",
		method: "post",
		data,
	});
}

//取消收藏
export function deleteConllectionByUserId(data) {
	return request({
		url: "/app/collections/deleteConllectionByUserId",
		method: "get",
		params: data,
	});
}

//查询用户所有收藏
export function findCollectionByUser(data) {
	return request({
		url: "/app/collections/findCollectionByUser",
		method: "get",
		params: data,
	});
}

//获取产品列表
export function getProductList(data) {
	return request({
		url: "/app/products/getProductList",
		method: "post",
		data: data,
	});
}

//获取广告列表
export function getAdvertList(data) {
	return request({
		url: "app/advertise/getAdvertisByCategoryIdAndProId",
		method: "get",
		params: data,
	});
}

//产品分类参数集合
export function getProductInfoAndParamRelation(data) {
	return request({
		url: "/app/products/getProductInfoAndParamRelation",
		method: "post",
		data: data,
	});
}

//价格计算
export function calculate(data) {
	return request({
		url: "/app/product/halfProductCalculate/calculate",
		method: "post",
		data: data,
	});
}

//半定制获取色卡
export function getAllColorCard(data) {
	return request({
		url: "/app/color/halfColorCard/getAllColorCard",
		method: "post",
		data: data,
	});
}

//修改订单地址
export function editOrderAddress(data) {
	return request({
		url: "/app/order/user/editOrderAddress",
		method: "post",
		data: data,
	});
}

//获取产品分类列表
export function getAllProduct(data) {
	return request({
		url: "/quote/cate/getAllProduct",
		method: "get",
		params: data,
	});
}

//获取报价分类列表
export function getAll(data) {
	return request({
		url: "/quote/cate/getAll",
		method: "post",
		data: data,
	});
}

//添加订阅
export function addSubscribes(data) {
	return request({
		url: "/app/member/addSubscribes",
		method: "post",
		data: data,
	});
}

//联系我们
export function addContactUs(data) {
	return request({
		url: "/app/member/addContactUs",
		method: "post",
		data: data,
	});
}

//下样品单
export function addSampleOrder(data) {
	return request({
		url: "/app/order/user/addSampleOrder",
		method: "post",
		data: data,
	});
}

//合作
export function influencersProgram(data) {
	return request({
		url: "/app/new/influencersProgram/add",
		method: "post",
		data: data,
	});
}

//getStripeKeyByProId
export function getStripeKey(data) {
	return request({
		url: "/app/pay/stripe/getStripeKeyByProId",
		method: "get",
		data: data,
	});
}

export function getProductParam(data) {
	return request({
		url: "/api/product/getProductParam",
		method: "post",
		data: data,
	});
}

export function getPageInfo(data) {
	return request({
		url: "/api/page/getPageInfo",
		method: "post",
		data: data,
	});
}

//报价参数
export function getProductConfig(data) {
	return request({
		url: "/api/product/getProductConfig",
		method: "post",
		data: data,
	});
}

export function getCateParamRelationByCateId(data) {
	return request({
		url: "/quote/cate/getCateParamRelationByCateId",
		method: "get",
		data: data,
	});
}

export function getByPId(data) {
	return request({
		url: "/quote/cate/getByPId",
		method: "get",
		data: data,
	});
}
//获取大类信息
export function getInfo(data) {
	return request({
		url: "/quote/cate/getInfo",
		method: "get",
		data: data,
	});
}

export function getStickerPriceTable(data) {
	return request({
		url: "/quote/quoteCalculate/getStickerPriceTable",
		method: "post",
		data: data,
	});
}

//未登录修改订单支付方式
export function modifyPaymentMethodNotLogin(data) {
	return request({
		url: "/app/order/user/modifyPaymentMethodNotLogin",
		method: "post",
		data: data,
	});
}

//未登录获取订单信息
export function getOrderNotLogin(data) {
	return request({
		url: "/app/order/user/getOrderNotLogin",
		method: "post",
		data: data,
	});
}

//未登录修改订单地址
export function editOrderAddressNotLogin(data) {
	return request({
		url: "/app/order/user/editOrderAddressNotLogin",
		method: "post",
		data: data,
	});
}

//获取所有子分类列表
export function listAllCategoryByParentId(data) {
	return request({
		url: "/app/category/listAllCategoryByParentId",
		method: "post",
		data: data,
	});
}

export function halfCalculate(data) {
	return request({
		url: "/app/products/halfProductCalculate/calculate",
		method: "post",
		data: data,
	});
}

// 半定制获取产品详情和价格列表
export function getProductSizePriceTableBySku(params) {
	return request({
		url: "/app/products/getProductSizePriceTableBySku",
		method: "get",
		params,
	});
}

//半定制添加询盘
export function getSemiInquiry(data) {
	return request({
		url: "/app/cart/getSemiCartParam",
		method: "post",
		data: data,
	});
}

//半定制添加购物车
export function addCart(data, productData) {
	return request({
		url: "/app/cart/addCart",
		method: "post",
		data: data,
	}).then((res) => {
		//加购物车成功之后，google记录
		try {
			if (window.$nuxt.$store.state.advertConfig.googleConfig && window.gtag && productData) {
				let type = "add_to_cart"
				let product = {
					currency: window.$nuxt.$store.state.currency.code,
					value: productData.value || 0,
					items: [productData.data],
				}
				if (productData.orderNow == 1) {
					type = "select_content"
					product.content_type = "buy_now"
				}
				gtag("event", type, product);
				let googleAddCartConfig = window.$nuxt.$store.getters.getAdvertisementConfig("googleAddCartConfig");
				if (googleAddCartConfig) {
					gtag("event", "conversion", {
						send_to: googleAddCartConfig.value,
						transport_type: "beacon" // 关键参数
					});
				}
			}
			return res;
		} catch (error) {
			return res;
		}
	})
}

//获取导航列表
export function getLanguageLink(routingName, currLanguage, currCountry) {
	return request({
		url: "/app/language/getLanguageLink",
		method: "get",
		params: {
			routingName,
			currLanguage,
			currCountry,
		},
	});
}

//半定制获取价格分层
export function getPriceLayered(data) {
	return request({
		url: "/app/products/halfProductCalculate/getPriceLayered",
		method: "get",
		params: data,
	});
}

//点击再次购买按钮
export function getBuyAgain(data) {
	return request({
		url: "/app/order/user/repurchase",
		method: "post",
		data: data,
	});
}

//编辑购物车图稿
export function editCartImageJson(data) {
	return request({
		url: "/app/cart/editCartImageJson",
		method: "post",
		data: data,
	});
}

//获取国家列表
export function getCountryList(data) {
	return request({
		url: "/app/address/country/getCountryList",
		method: "get",
		params: data,
	});
}

//获取州
export function getStateList(data) {
	return request({
		url: "/app/address/country/getState",
		method: "post",
		data,
	});
}

//优惠卷
export function editOrderDiscount(data) {
	return request({
		url: "/app/order/user/editOrderDiscount",
		method: "post",
		data: data,
	});
}

export function changeCartQuantity(data) {
	return request({
		url: "/app/cart/changeCartQuantity",
		method: "post",
		data,
	});
}

//获取区号
export function getCodeByIp(data) {
	return request({
		url: "/app/address/country/getCodeByIp",
		method: "post",
		data,
	});
}

//获取第三方代码
export function getThirdContent(data) {
	return request({
		url: "/app/systemProject/getThirdContent",
		method: "get",
		params: data,
	});
}

//地址校验
export function googleVerifyAddress(data) {
	return request({
		url: "/app/language/googleVerifyAddress",
		method: "post",
		data,
	});
}

//pens弹窗登录成功跳转路由
export function getPensLogin(data) {
	return request({
		url: "/app/products/getActivityProductRoutingName",
		method: "get",
		params: data,
	});
}

//购物车和报价提前算税费
export function getTaxByPrice(data) {
	return request({
		url: "/app/order/user/getTaxByPrice",
		method: "post",
		data,
	});
}

// 半定制列表页获取结构化数据
export function googleDataStructural(data) {
	return request({
		url: "/app/products/googleDataStructural",
		method: "post",
		data,
	});
}

// 半定制详情页获取结构化数据
export function getGoogleProductStructure(data) {
	return request({
		url: "/app/products/getGoogleProductStructure",
		method: "post",
		data,
	});
}

// 获取短信订阅状态(未登录)
export function getSmsSubscriptionByMail(data) {
	return request({
		url: "/app/member/getSmsSubscriptionByMail",
		method: "get",
		params: data,
	});
}

// 获取短信订阅状态(登录状态)
export function getSmsSubscriptionState(data) {
	return request({
		url: "/app/member/getSmsSubscriptionState",
		method: "get",
		params: data,
	});
}

// enamelpins 获取产品
export function getProductByUrl(data) {
	return request({
		url: "/app/page/gallery/getProductByUrl",
		method: "get",
		params: data,
	});
}

export function getCateList(data) {
	return request({
		url: "/app/page/gallery/getCateList",
		method: "get",
		params: data,
	});
}

export function getProductGalleryList(data) {
	return request({
		url: "/app/page/gallery/getProductGalleryList",
		method: "get",
		params: data,
	});
}
export function cancelSubscribes(data) {
	return request({
		url: "/cancelSubscribes/cancelSubscribes/add",
		method: "get",
		params: data,
	});
}

export function getNotLoginUserCouponListByEmail(data) {
	return request({
		url: "/app/coupon/getNotLoginUserCouponListByEmail",
		method: "post",
		data,
	});
}

//霓虹灯网站赠品列表
export function getFreebieList(data) {
	return request({
		url: "/app/freebie/getFreebieList",
		method: "post",
		data,
	});
}

//获取设计师产品详情
export function getProductByRoutingName(data) {
	return request({
		url: "/app/designer/product/getProductByRoutingName",
		method: "post",
		data,
	});
}

export function designerAddCollection(data) {
	return request({
		url: "/app/designer/collection/addCollection",
		method: "post",
		data,
	});
}

export function removeCollectionById(data) {
	return request({
		url: "/app/designer/collection/removeCollectionById",
		method: "get",
		params: data,
	});
}

export function addShoppingCart(data) {
	return request({
		url: "/app/designerShoppingCart/addShoppingCart",
		method: "post",
		data,
	});
}
export function addShoppingCartNotLogin(data) {
	return request({
		url: "/app/designerShoppingCart/addShoppingCartNotLogin",
		method: "post",
		data,
	});
}

//获取设计师左侧-上2
export function productGetAllTags(data) {
	return request({
		url: "/app/designer/product/getAllTags",
		method: "get",
		params: data,
	});
}

//获取设计师标签产品-下2
export function tagsGetTagsProductCount(data) {
	return request({
		url: "/app/tags/getTagsProductCount",
		method: "post",
		data,
	});
}

//前台获取产品列表
export function productGetProductList(data) {
	return request({
		url: "/app/designer/product/getProductList",
		method: "post",
		data,
	});
}

//获取霓虹灯赠品颜色
export function getFreebieNeonColor(data) {
	return request({
		url: "/quote/cate/getFreebieNeonColor",
		method: "get",
		params: data,
	});
}

//查询用户是否已经取消订阅
export function getCheck(data) {
	return request({
		url: "/cancelSubscribes/cancelSubscribes/check",
		method: "get",
		params: data,
	});
}

//获取霓虹灯赠品字体
export function getFreebieNeonFont(data) {
	return request({
		url: "/quote/cate/getFreebieNeonFont",
		method: "get",
		params: data,
	});
}

//获取角标数据
export function getListCornerLabel(data) {
	return request({
		url: "/app/page/cornerLabel/listCornerLabel",
		method: "post",
		data,
	});
}

//获取小重量交期运输时间判断开关
export function turnaroundTimeCheckStatus(data) {
	return request({
		url: "/quote/cate/turnaroundTimeCheckStatus",
		method: "get",
		params: data,
	});
}


// 获取中间页数据
export function getFdMiddlePageData(data) {
	return request({
		url: "/quote/cate/getFdMiddlePageData",
		method: "get",
		params: data,
	});
}

//获取socialCode用来回填
export function getSocialCodeByEmail(data) {
	return request({
		url: "/app/member/getSocialCodeByEmail",
		method: "post",
		data,
	});
}

//获取特殊色卡列表
export function getSpecialColorList(data) {
	return request({
		url: "/quote/specialColor/getSpecialColorList",
		method: "get",
		params: data,
	});
}

//获取项目结构化数据
export function getProjectStructuringByProId(data) {
	return request({
		url: "/app/page/projectStructuring/getProjectStructuringByProId",
		method: "get",
		params: data,
	});
}

// 获取所有背板，以及背板下的材料
export function getBackingList() {
	return request({
		url: "/app/backing/getBackingList",
		method: "get"
	});
}

// 搜索材料及对应背板
export function getFabricList(params) {
	return request({
		url: "/app/backing/getFabricList",
		method: "get",
		params
	});
}

// 获取色卡模板数据列表
export function getColorSampleList(params) {
	return request({
		url: "/app/sample/colorSample/listColorSample",
		method: "get",
		params
	});
}

/**
 * 获取 Gallery 列表
 * @description 目前用于 GS-JJ https://www.gs-jj.com/Exhibition/
 */
export function getGalleryList(params) {
	return request({
		url: "/app/gallery/gallery/getGallery",
		method: "get",
		params
	});
}

/**
 * 获取 Gallery 详情
 * @description 目前用于 GS-JJ https://www.gs-jj.com/belt-buckles/exhibit/custom-3d-eagle-belt-buckles
 */
export function getGalleryDetail(params) {
	return request({
		url: "/app/gallery/gallery/getGalleryByPageUrl",
		method: "get",
		params
	});
}

/**
 * 获取 Stock 列表
 * @description 目前用于 GS-JJ https://www.gs-jj.com/products/
 */
export function getStockList(params) {
	return request({
		url: "/app/stockpro/getStockProducts",
		method: "get",
		params
	});
}

/**
 * 获取 Stock 详情
 * @description 目前用于 GS-JJ https://www.gs-jj.com/lapel-pins/shop/picasso-cat-enamel-pins
 */
export function getStockDetail(params) {
	return request({
		url: "/app/stockpro/getStockProByPageUrl",
		method: "get",
		params
	});
}

/**
 * 获取设计系统模板列表
 * @description
 * 		1. 参考网页：https://www.gs-jj.com/custom-pins-24-hours
 * 		2. 相关参数：php代码中获取
 * @param params.page 页码
 * @param params.pageSize 每页显示条数
 * @param params.categoryId 类目Id
 * @param params.templateTypeId 模板类型Id
 * @returns {*}
 */
export function getTemplateListFormApp(params) {
	return request({
		url: "/app/design/template/getTemplateListFormApp",
		method: "get",
		params
	});
}