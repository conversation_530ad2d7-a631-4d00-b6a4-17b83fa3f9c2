<template>
	<div id="briefly-show">
		<div class="icon" :style="iconStyle()">
			<!-- <v-icon color="white" x-large> mdi-{{ iconName }} </v-icon> -->
			<b :class="iconName" style="font-size: 26px; color: #ffff;"></b>
		</div>
		<div class="strip1">{{ type }}</div>
		<div class="strip2">{{ total }}</div>
		<v-tooltip bottom color="#FFFFFF" max-width="286px">
			<template v-slot:activator="{ on, attrs }">
				<div class="hint" v-if="isHint" v-bind="attrs" v-on="on">
					<v-icon dense color="#C8C8C8">mdi-help-circle</v-icon>
					<!-- <div class="bubble-box">The sum of revenue from purchases, subscriptions,and advertising (Purchase revenue plus Subscription revenue plus Adrevenue).</div> -->
				</div>
				<!-- <v-btn color="primary" dark v-bind="attrs" v-on="on"> Button </v-btn> -->
			</template>
			<span style="color:#999999;text-align:center;font-size: 15px;">The sum of revenue from purchases,
				subscriptions,and advertising (Purchase revenue plus Subscription revenue plus Adrevenue).</span>
		</v-tooltip>
	</div>
</template>

<script>
export default {
	name: "BrieflyShow",
	props: {
		bgc: {
			type: String,
			default: "pink",
		},
		type: {
			type: String,
			default: "undefined",
		},
		total: {
			type: [Number, String],
			default: "0",
		},
		iconName: {
			type: String,
			default: "icon-jsxht-tjfx-jg",
		},
		isHint: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {};
	},
	methods: {
		iconStyle() {
			return { backgroundColor: this.bgc };
		},
	},
};
</script>

<style lang="scss" scoped>
#briefly-show {
	position: relative;
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
	border-radius: 0.5208vw;
	background-color: #fff;

	.icon {
		display: flex;
		justify-content: center;
		align-items: center;
		width: 3.2292vw;
		height: 3.2292vw;
		border-radius: 1.6146vw;
		margin-top: 1.0417vw;
		margin-bottom: 0.5208vw;
		opacity: 0.5;
	}

	.strip1,
	.strip2 {
		width: 4.3229vw;
		height: 1.0417vw;
		border-radius: 0.3125vw;
		text-align: center;
	}

	.strip1 {
		margin-bottom: 0.2083vw;
		color: #999999;
	}

	.strip2 {
		color: #333333;
		font-weight: bold;
	}

	.hint {
		position: absolute;
		right: 1.25vw;
		top: 0.8854vw;

		&:hover .bubble-box {
			display: block;
		}

		.bubble-box {
			display: none;
			position: absolute;

			left: -130px;
			top: 32px;
			width: 286px;
			height: 112px;
			border: 1px solid #e5e5e5;
			border-radius: 10px;
			background: #ffffff;
			z-index: 1;
			font-size: 15px;
			padding: 0.6604vw;
			color: #999999;

			&::after {
				content: "";
				position: absolute;
				top: -20px;
				left: 130px;
				width: 0;
				height: 0;
				/* background-color: pink; */
				border: 10px solid transparent;
				border-bottom-color: #e5e5e5;
			}
		}
	}
}
</style>
