<template>
	<div class="my-textarea">
		<div class="textareaBox">
			<span class="textLabel" @click.stop v-show="textLabel">{{ textLabel }}</span>
			<textarea class="myTextarea" ref="myTextarea" :value="value" @input="validateComment" v-bind="$attrs" v-on="$listeners"></textarea>
		</div>

		<div class="errorContainer" ref="errorContainer"></div>
		<div class="des" v-show="uploadFile">
			<span v-show="!uploadList.length">{{ langSemiCustom.noattached }}</span> <a href="javascript:;" @click="openUpload"> + {{ langSemiCustom.attachFile }}</a>
		</div>
		<div class="fileWrap">
			<div class="file-item" v-for="(item, index) in uploadList" :key="item.secure_url" @click="zoomPic(item.secure_url)">
				<div class="fileInfo d-flex align-items-center">
					<v-icon class="mr-1">mdi-file</v-icon>
					<span class="fileName">{{ item.original_filename }}</span>
				</div>
				<div class="control">
					<v-btn small icon>
						<v-icon>mdi-check</v-icon>
					</v-btn>
					<v-btn small icon @click.stop="deletePic(index)">
						<v-icon>mdi-trash-can</v-icon>
					</v-btn>
				</div>
			</div>
		</div>
		<input type="file" ref="upload" @change="uploadPic" />
	</div>
</template>
<script>
import { uploadFile } from "@/utils/oss";
import { isImageType } from "@/utils/utils";
export default {
	props: {
		value: {
			type: [String, Number],
		},
		textLabel: {
			type: String,
			default: "",
		},
		uploadList: {
			type: Array,
			default: () => [],
		},
		uploadFile: {
			type: Boolean,
			default: true,
		},
	},
	model: {
		prop: "value",
		event: "myTextareaUpdate",
	},
	inheritAttrs: false,
	data() {
		return {
			copyUploadList: [],
			myTextarea: null,
			errorContainer: null,
			colorPrimary: "",
		};
	},
	computed: {
		langSemiCustom() {
			return this.$store.getters.lang?.semiCustom;
		},
	},
	methods: {
		validateComment(e) {
			this.$emit("myTextareaUpdate", e.target.value);
			if (+this.myTextarea.scrollHeight > 130) {
				this.myTextarea.style.height = "auto";
				const newHeight = this.myTextarea.scrollHeight;
				if (+newHeight > 240) {
					this.myTextarea.style.overflow = "auto";
				} else {
					this.myTextarea.style.overflow = "hidden";
				}
				this.myTextarea.style.height = `${newHeight}px`;
			} else {
				this.myTextarea.style.overflow = "hidden";
				this.myTextarea.style.height = "130px";
			}
		},

		zoomPic(img) {
			if (isImageType(img)) {
				this.$viewerApi({
					images: [img],
				});
			}
		},
		openUpload() {
			this.$refs.upload.click();
		},
		deletePic(index) {
			this.copyUploadList.splice(index, 1);
			this.$emit("update:uploadList", this.copyUploadList);
		},
		uploadPic(event) {
			let files = event.target.files,
				size = files[0].size;
			if (size / 1020 / 1024 > 80) {
				this.$toast.error("File size cannot exceed 80m.");
				this.$refs.upload.value = "";
				return;
			}
			uploadFile(files[0]).then((res) => {
				this.copyUploadList.push({
					original_filename: files[0].name,
					secure_url: res,
				});
				this.$emit("update:uploadList", this.copyUploadList);
				this.$refs.upload.value = "";
			});
		},
	},
	mounted() {
		this.errorContainer = this.$refs.errorContainer;
		this.myTextarea = this.$refs.myTextarea;
		let borderColor2 = "--bg-dark";
		const colorBgDark = window.getComputedStyle(document.documentElement).getPropertyValue(borderColor2);
		this.myTextarea.addEventListener("blur", () => {
			this.myTextarea.style.setProperty("border-color", `${colorBgDark}`);
		});
	},
};
</script>
<style scoped lang="scss">
.my-textarea {
	max-width: 100%;
	.textareaBox {
		position: relative;
		display: flex;
		flex-direction: column;
		font-size: 16px;
		color: #333;
		.textLabel {
			margin-bottom: 4px;
		}

		.myTextarea {
			box-sizing: border-box;
			width: 100%;
			min-height: 130px;
			max-height: 240px;
			overflow: hidden;
			padding: 10px;
			border-radius: 6px;
			border: 2px solid #ede9e9;
			background: #f5f5f5;
			font-size: 14px;
			scrollbar-width: none;
			&:focus {
				border: 2px solid $color-primary;
			}
			&::placeholder {
				font-size: 14px;
				color: #d8d8d8;
			}
		}

		@include respond-to(mb) {
			font-size: 12px;
			.myTextarea {
				font-size: 12px;
			}
		}
	}

	.errorContainer {
		font-size: 12px;
		color: red;
	}

	.fileWrap {
		margin-top: 10px;
		background: #f5f5f5;
		.file-item {
			display: flex;
			justify-content: space-between;

			.control {
				margin-left: 40px;

				i {
					margin: 0 2px;
					cursor: pointer;
				}
			}
		}
	}

	.des {
		color: #999999;
		margin-top: 10px;
		font-size: 14px;

		a {
			color: $color-primary;
		}
	}

	input[type="file"] {
		position: absolute;
		z-index: -1;
		opacity: 0;
	}

	@include respond-to(mb) {
		.step-content {
			grid-template-columns: 1fr;
			grid-gap: 5px;

			.v-textarea {
				font-size: 14px;
			}

			.fileWrap {
				margin-top: 10px;

				.file-item {
					font-size: 12px;
					.v-icon {
						font-size: 18px;
					}
				}
			}

			.des {
				font-size: 12px;
			}
		}
	}
}
</style>
