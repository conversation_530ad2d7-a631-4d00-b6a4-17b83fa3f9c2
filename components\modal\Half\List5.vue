<!--
    用途: keychains网站 pens网站
    预览图:https://static-oss.gs-souvenir.com/web/quoteManage/20250225/企业微信截图_17404674495788_20250225psGhsn.png
-->
<template>
	<div class="modalHalfListBox List5" style="position: relative;">
		<div class="wrap modal-box" :style="[modal.style, isManage ? {} : { 'padding-top': '0' }]">
			<template v-if="isManage">
				<v-card height="300">
					<v-row justify="center" align="center" style="width: 100%; height: 100%; display: flex">
						<div class="rightHeader">
							<div v-for="(l, li) in modal.list" :key="li" :style="{ ...modal.cardBoxStyle, ...l.style }">
								<EditDiv :tagName="l.title.tagName || 'h1'" v-model:content="l.title.value"
									@click="setModalType(l.title, modal.list, 'text')" v-if="l.title?.value"
									:style="{ ...modal.titleStyle }" />
								<EditDiv :tagName="l.subTitle.tagName || 'p'" v-model:content="l.subTitle.value"
									@click="setModalType(l.subTitle, modal.list, 'text')" :style="{ ...modal.subTitleStyle }"
									v-if="l.subTitle?.value" />
								<EditDiv :style="modal.textStyle" @click="setModalType(l.text, modal.list, 'text')"
									v-model:content="l.text.value" v-if="l.text?.value"> </EditDiv>
							</div>
						</div>
					</v-row>
				</v-card>
			</template>
			<div v-else>
				<div class="bps_container">
					<div class="content" id="proContent" :class="{ noFilter: !showFilter }">
						<div class="leftBar"
							:style="{ maxHeight: isMobile ? `${screenHeight}px` : screenHeight - selectLabelHeight + 'px' }"
							:disabled="loadLabel" ref="leftBar" v-show="showFilter">
							<div class="leftBarHeader">
								<div class="breadCrumbs" v-if="!isMobile">
									<div class="bread-item is-link" @click="toCate">
										<span style="font-size: 15px">{{ langSemiCustom.allProducts }}</span>
										<b class="icon-right"></b>
									</div>
									<div class="bread-item">
										<span class="bread-item-span" style="font-size: 13px; font-weight: 400">{{ fatherCateName }}</span>
										<b class="icon-right"></b>
									</div>
								</div>
								<div class="filterBox">
									<div class="filterHeader">
										<div class="filterTitle">
											{{ langSemiCustom.filters }}
											<span class="filterTotal">({{ totalResult }} {{ langSemiCustom.results }})</span>
										</div>
										<div v-if="isMobile" @click="toggleFilter"><b class="iconfont icon-hxsht-xp-gb"></b></div>
									</div>
									<div class="filterContent" v-show="Object.keys(selectedParamsObj).length > 0 || colorItem">
										<template v-if="colorItem">
											<div class="category-item tag">
												<span>{{ colorItem.valueName }}</span>
												<!-- <span v-show="isStockPage && $store.state.proTheme == '11'"> 包装形态:&nbsp;{{ colorItem.valueName }}</span> -->
												<div @click="delColor(colorItem)" class="delIconBox">
													<v-icon small style="margin-left: 4px">mdi-close</v-icon>
												</div>
											</div>
										</template>
										<div v-for="(item, key, index) in selectedParamsObj" :key="key">
											<div class="category-item tag" v-for="citem in item" :key="citem.id">
												<!-- {{ getLabel(citem["fatherId"]) }}:&nbsp; -->
												<span>{{ citem.valueName }}</span>
												<div @click="delTag(citem)" class="delIconBox">
													<v-icon small style="margin-left: 4px">mdi-close</v-icon>
												</div>
											</div>
										</div>
									</div>
									<div class="clearFilter" @click="delAllTag"
										v-show="Object.keys(selectedParamsObj).length > 0 || colorItem">
										<v-icon style="font-size: 20px">mdi-trash-can-outline</v-icon>
										<a href="javascript:;">
											{{ langDesign.clearAll }}
										</a>
									</div>
								</div>
							</div>
							<div class="overlay changeLoad" v-show="loadLabel" style="z-index: 1">
								<div class="loader"></div>
							</div>
							<div class="collapse">
								<div class="filter-mb" v-if="isMobile && !isStockPage">
									<div class="filter-mb-title" @click="clickTitle({ id: -1 })">
										<strong>{{ langSemiCustom.sort }}:</strong>
										<b class="icon-Down" :class="{ active: activeNames.includes(-1) }"></b>
									</div>
									<div class="con" v-show="activeNames.includes(-1)">
                                        <div class="con-radio" v-for="(item,key) in sortList" :key="key" @click.stop="sortProduct(key)">
                                            <label>
                                                <input type="radio" name="sort" checked />
                                                {{ item }}
                                                <span class="custom-radio"></span>
                                            </label>
                                        </div>
									</div>
								</div>
								<div class="collapse-item" v-for="(item, index) in labelData" :key="item.id">
									<div class="collapse-item-title" @click="clickTitle(item)">
										<strong class="text-truncate">{{ item.nameEn }}</strong>
										<b class="icon-Down" :class="{ active: activeNames.includes(item.id) }"></b>
									</div>
									<v-expand-transition>
										<div class="con" v-show="activeNames.includes(item.id)">
											<template v-if="getLabelType(item.attributeType) === 'range'">
												<template v-for="citem in item.attributeList">
													<div class="price-range-box">
														<v-btn icon v-show="showPriceRange">
															<v-icon @click="clearRange"> mdi-trash-can</v-icon>
														</v-btn>
														<v-range-slider class="custom-slider" @change="changeRange" v-model="priceRange" thumb-label
															:min="citem.valueName" :max="citem.remark" hide-details :step="0.01"></v-range-slider>
													</div>
													<div class="price-des">
														<div>
															<CCYRate :price="citem.valueName"></CCYRate>
														</div>
														<div>
															<CCYRate :price="citem.remark"></CCYRate>
														</div>
													</div>
												</template>
												<div class="borderBox" v-show="index !== labelData.length - 1" style="margin-top: 20px"></div>
											</template>
											<template v-else-if="getLabelType(item.attributeType) === 'color'">
												<div class="box color-box">
													<div class="color-wrap">
														<template v-for="citem in item.attributeList">
															<v-tooltip bottom :disabled="isMobile">
																<template v-slot:activator="{ on, attrs }">
																	<div v-bind="attrs" v-on="on" class="color-item"
																		:class="{ active: citem.id === colorItem.id }" @click="toggleColor(citem)">
																		<img v-if="citem.imgUrl" :src="citem.imgUrl" :alt="citem.valueName"
																			:title="citem.valueName" />
																		<span v-else :style="{ backgroundColor: citem.valueName }"></span>
																	</div>
																</template>
																<span style="font-size: 12px">{{ citem.remark }} ({{ citem.productCount }})</span>
															</v-tooltip>
														</template>
													</div>
													<div class="borderBox" v-show="index !== labelData.length - 1" style="margin-top: 20px"></div>
												</div>
											</template>
											<template
												v-else-if="getLabelType(item.attributeType) === 'checkbox-style1' || getLabelType(item.attributeType) === 'checkbox-style2'">
												<div :class="getLabelType(item.attributeType)"
													v-if="isMobile && isStockPage && $store.state.proTheme == '11'">
													<div class="teaMobileBox">
														<div class="teaMobileStyle" v-for="citem in showMoreArr(item)" :key="citem.id"
															:class="{ active: teaMobileActive(citem.id) }">
															<div></div>
															<input type="checkbox" class="teaCheckbox" v-model="selectedParams"
																@change="updateTag(citem)" :value="citem.id" :id="'checkbox-' + citem.id" />
															<label :for="'checkbox-' + citem.id">
																{{ `${citem.valueName}(${citem.productCount})` }}
															</label>
														</div>
													</div>
													<div class="showBtn" ref="showBtn" v-show="item.isMore && item.attributeList.length > 8"
														@click="showMore(item)">
														{{ item.moreText ? langSemiCustom.ShowMore : langSemiCustom.ShowLess }}
													</div>
													<div class="borderBox" v-show="index !== labelData.length - 1" style="margin-top: 20px"></div>
												</div>
												<div :class="getLabelType(item.attributeType)" v-else>
													<v-checkbox v-model="selectedParams" @change="updateTag(citem)"
														:label="`${citem.valueName}(${citem.productCount})`" :value="citem.id"
														v-for="citem in showMoreArr(item)" :key="citem.id" hide-details dense></v-checkbox>
													<div class="showBtn" ref="showBtn" v-show="item.isMore && item.attributeList.length > 8"
														@click="showMore(item)">
														{{ item.moreText ? langSemiCustom.ShowMore : langSemiCustom.ShowLess }}
													</div>
													<div class="borderBox" v-show="index !== labelData.length - 1" style="margin-top: 20px"></div>
												</div>
											</template>
											<template v-else-if="getLabelType(item.attributeType) === 'switch'">
												<v-switch v-model="selectedParams" @change="updateTag(citem)"
													:label="`${citem.valueName}(${citem.productCount})`" :value="citem.id"
													v-for="citem in item.attributeList" :key="citem.id" hide-details dense></v-switch>
												<div class="borderBox" v-show="index !== labelData.length - 1" style="margin-top: 20px"></div>
											</template>
										</div>
									</v-expand-transition>
								</div>
							</div>
						</div>
						<div class="rightBar" style="width: 100%; min-width: 0">
							<div class="rightHeader">
								<div v-for="(l, li) in modal.list" :key="li" :style="{ ...modal.cardBoxStyle, ...l.style }">
									<EditDiv :tagName="l.title.tagName || 'h1'" v-model:content="l.title.value"
										@click="setModalType(l.title, modal.list, 'text')" v-if="l.title?.value"
										:style="{ ...modal.titleStyle }" />
									<EditDiv :tagName="l.subTitle.tagName || 'p'" v-model:content="l.subTitle.value"
										@click="setModalType(l.subTitle, modal.list, 'text')" :style="{ ...modal.subTitleStyle }"
										v-if="l.subTitle?.value" />
									<EditDiv :style="modal.textStyle" @click="setModalType(l.text, modal.list, 'text')"
										v-model:content="l.text.value" v-if="l.text?.value"> </EditDiv>
								</div>
							</div>
							<div class="top-right sticky" style="display: none">
								<div class="filterSearch" :class="{ textSearch: !isMobile }">
									<label class="search-area">
										<div class="inputBox">
											<span @click="changeKeyword"><v-icon>mdi-magnify</v-icon></span>
											<input v-model="keyword" @input="changeKeyword" type="text"
												:placeholder="langSemiCustom.ProductKeywords" />
										</div>
									</label>
								</div>
								<div class="filter-area">
									<div class="filter1" @click="toggleFilter">
										<b class="icon-a-HideFilters"></b>
										<span>
											<span v-show="!isMobile">{{ showFilter ? `${langSemiCustom.hide}` : `${langSemiCustom.show}` }}
											</span>{{ langSemiCustom.filters }}
										</span>
									</div>
									<div class="filter2">
										<span class="t1">{{ langSemiCustom.sort }}:</span>
										<v-menu offset-y>
											<template v-slot:activator="{ on, attrs }">
												<v-btn text v-bind="attrs" v-on="on" :small="isMobile"
													style="padding: 0; font-weight: 400; font-size: 16px; color: #333333; letter-spacing: normal">
                                                    {{ sortList[sorts] }}
													<b class="icon-Down litterArrow" style="font-size: 12px"></b>
												</v-btn>
											</template>
                                            <v-list>
                                                <v-list-item v-for="(item,key) in sortList" :key="key" @click="sortProduct(key)">
                                                    <v-list-item-title>{{ item }} </v-list-item-title>
                                                </v-list-item>
                                            </v-list>
										</v-menu>
									</div>
								</div>
							</div>
							<div class="selectLabel" id="selectLabel" style="display: none">
								<div class="filter">
									<div class="top">
										<div class="cateitemBox">
											<div class="category-item" v-for="(citem, cindex) in cateList" :key="citem.id"
												:class="{ active: activeCateName(citem) }" v-show="!isStockPage && cindex < showMoreIndex"
												@click="changeCategory(citem)">
												<span>{{ citem.name }}</span>
											</div>
											<div class="viewMore" v-show="tagViewStatus">
												<span @click="viewMoreFn(999, 'more')" v-if="showMoreIndex === 10">{{ langQuote.ViewMore }}<b
														class="icon-Down litterArrow"></b></span>
												<span @click="viewMoreFn(10, 'less')" v-if="showMoreIndex === 999">{{ langQuote.ViewLess }}<b
														class="icon-Up litterArrow"></b></span>
											</div>
										</div>
										<div class="viewMore" v-show="!tagViewStatus">
											<span @click="viewMoreFn(999, 'more')" v-if="showMoreIndex === 10">{{ langQuote.ViewMore }}<b
													class="icon-Down litterArrow"></b></span>
											<span @click="viewMoreFn(10, 'less')" v-if="showMoreIndex === 999">{{ langQuote.ViewLess }}<b
													class="icon-Up litterArrow"></b></span>
										</div>
									</div>
								</div>
							</div>
							<div class="rightPart" id="productList">
								<noFind :keyword="keyword" :cateName="cateName" :parentCateId="parentCateId" :cateId="cateId"
									:fatherCateName="fatherCateName" v-if="productList.length === 0 && !loadingProduct"></noFind>
								<div class="rightContent">
									<div class="productWrap">
										<product class="productGoods" v-for="(item, index) in productList" :isDiv="getProductIsDiv" :key="item.id"
											:class="{ inserted_element: item.booth == 2 }" :parentCateId="parentCateId" :cateId="cateId"
											:productData="item" :adBoxHeight="adBoxHeight" :itemIndex="index" :isStockPage="isStockPage"
											:productList="productList"></product>
									</div>
								</div>
							</div>
							<div class="loadProgress" v-if="loadingProduct">
								<Loading></Loading>
							</div>
						</div>
					</div>
					<div class="loadBtnMainBox" v-show="!loadingProduct && totalResult > 0">
						<div class="loadMoreBtn">
							<div class="loadMoreBtnText">
								<span>
									{{ `${langSemiCustom.viewed} ${productList.length - usePosition} ${langSemiCustom.of} ${totalResult}
									${langSemiCustom.products}` }}
								</span>
							</div>
							<v-progress-linear height="6" background-color="#fff" rounded :value="numPercent"></v-progress-linear>
							<div class="loadBtnBox" v-show="showLoadBtn">
								<span class="loadBtn" @click="loadMoreData">{{ langSemiCustom.loadMore }}</span>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import { getAdvertList, getAppLabelAttributeList, getProductList, listAllCategoryByParentId } from "@/api/web";
import { debounce, generateUUID } from "@/utils/utils";
import noFind from "@/components/modal/Half/List/noFind.vue";
import product from "@/components/modal/Half/List/product.vue";
import { halfListMixins } from "@/mixins/halfList";

export default {
	mixins: [halfListMixins],
	props: ["cateId", "data", "parentCateId", "halfCateDTO", "isStockPage","selectLabelData"],
	components: {
		noFind,
		product,
	},
	provide() {
		return {
			isDialog: this.isDialog,
		};
	},
	data() {
		return {
			showMoreIndex: 10,
			last_scroll: 0,
			hide_on_load: false,
			debounceSearchProduct: null,
			debounceLabel: null,
			modal: {
				style: {},
				type: {},
				...this.data,
			},
			showPriceRange: false,
			priceRange: [-990000, 990000],
			halfName: "",
			goodList: [],
			keyword: "",
			sorts: 2,
			labelData: [],
			cateList: [],
			activeNames: [],
			selectedParams: [],
			showFilter: false,
			page: 1,
			pages: 0,
			pageSize: 20 * 3,
			totalResult: 0,
			productList: [],
			colorItem: "",
			priceKey: 0,
			quantityKey: 0,
			screenHeight: 0,
			screenWidth: 0,
			loadingProduct: true,
			cateName: "",
			isManage: false,
			attributeValueIds: [],
			selectedParamsObj: {},
			loadLabel: false,
			adData: null,
			advert: [],
			adNum: 0,
			useAdNum: 0,
			doubleAd: 0,
			customKeyword: "",
			usePosition: 0,
			fatherCateName: "",
			headerHeight: 0,
			selectLabelHeight: 0,
			nowTimeStamp: "",
			cancelTokenSource: null,
			firstCate: false,
			fristWatch: false,
			tagViewStatus: false,
			adBoxHeight: "",
		};
	},
	async fetch() {
		if (process.env.isManage) {
			this.isManage = true;
			return false;
		}
		this.page = parseInt(this.$route.query.page) || 1;
		await this.loadThreeData(true);
	},
	watch: {
		modal: {
			handler(val) {
				if (process.env.isManage) this.$emit("update:data", val);
			},
			immediate: true,
			deep: true,
		},
		selectedParams(oldVal, newVal) {
			if (!this.fristWatch) return;
			this.useAdNum = 0;
			this.doubleAd = 0;
			this.usePosition = 0;
			this.updateLabel();
			this.searchProduct();
		},
	},
	computed: {
        sortList(){
            return{
                1:this.langSemiCustom.newest,
                2:this.langSemiCustom.recommended,
                3:this.langSemiCustom.bestSeller,
                4:this.langSemiCustom.popular
            }
        },
        getProductIsDiv() {
			return (!this.isKeychainsCo || this.isDialog) && !this.isLuggageSite;
		},
		isLuggageSite() {
			return this.proId === 475;
		},
        isKeychainsCo() {
            return this.proId === 354;
        },
		isDialog() {
			return !!this.name;
		},
		langQuote() {
			return this.$store.getters.lang.quote || {};
		},
		langSemiCustom() {
			return this.$store.getters.lang?.semiCustom;
		},
		langDesign() {
			return this.$store.getters.lang?.design;
		},
		userId() {
			return this.$store.state.userInfo?.id;
		},
		isLogin() {
			return this.$store.getters.isLogin;
		},
		isMobile() {
			return this.$store.state.device === "mb";
		},
		numPercent() {
			return Math.round(((this.productList.length - this.usePosition) / this.totalResult) * 100);
		},
		showLoadBtn() {
			return !(this.page == this.pages);
		},
		proId() {
			return this.$store.state.proId;
		},
	},
	methods: {
		showMoreArr(item) {
			if (item.moreText && item.attributeList.length > 8) {
				return item.attributeList.slice(0, 8);
			} else {
				return item.attributeList;
			}
		},
		showMore(item) {
			item.moreText = !item.moreText;
		},
		viewMoreFn(num, type) {
			this.showMoreIndex = num;
			let cateitemBox = document.querySelector(".cateitemBox");
			if (type == "more") {
				this.tagViewStatus = true;
				cateitemBox.style.maxHeight = "none";
			} else {
				this.tagViewStatus = false;
				cateitemBox.style.maxHeight = "34px";
			}
		},
		updateTag(item) {
			let val = this.selectedParamsObj[item.fatherId];
			if (!val) {
				this.selectedParamsObj[item.fatherId] = [item];
			} else {
				let findIndex = val.findIndex((v) => {
					return v.id == item.id;
				});
				if (findIndex > -1) {
					val.splice(findIndex, 1);
				} else {
					val.push(item);
				}

				if (val.length == 0) delete this.selectedParamsObj[item.fatherId];
			}
		},
		updateLabel() {
			this.loadLabel = true;
			this.getAppLabelAttributeList()
				.then((res) => {
					let labelData = res.data;
					labelData.forEach((item) => {
						if(item.isMore) item.moreText = true;
						if (item.isExpand) this.activeNames.push(item.id);
					});
					this.activeNames = [...new Set([...this.activeNames])];
					this.labelData = labelData;
				})
				.finally(() => {
					this.listScrollTop();
					this.loadLabel = false;
				});
		},
		getAppLabelAttributeList() {
			return new Promise((resolve) => {
				getAppLabelAttributeList({
					categoryId: this.parentCateId || this.cateId,
					childCategoryId: this.customKeyword ? "" : this.parentCateId ? this.cateId : null,
					isLabel: 1,
					attributeValueIds: this.getLabelAttributeValueIds(),
					keyWord: this.keyword,
				}).then((res) => {
					resolve(res);
				});
			});
		},
		toCate() {
			if (this.halfCateDTO) {
				let path = this.isStockPage == "1" ? this.halfCateDTO.shopRouting : this.halfCateDTO.customRouting;
				this.$router.push({
					path: path,
				});
			}
		},
		preLoadImg(arr) {
			arr.forEach((item) => {
				let img = new Image();
				img.src = item;
			});
		},
		getLabelType(type) {
			//标签类型 1.单选颜色样式，2.多选一列样式，3.多选两列样式，4.仅勾选是/否样式，5.拖动条样式），如果类型是拖动样式，属性值名称是最小值，属性值备注是最小值
			const obj = {
				1: "color",
				2: "checkbox-style1",
				3: "checkbox-style2",
				4: "switch",
				5: "range",
			};
			return obj[type];
		},

		changeRange() {
			this.showPriceRange = true;
			this.useAdNum = 0;
			this.doubleAd = 0;
			this.usePosition = 0;
			this.searchProduct();
			this.updateLabel();
		},

		clearRange() {
			this.priceRange = [-10000, 10000];
			this.showPriceRange = false;
			this.useAdNum = 0;
			this.doubleAd = 0;
			this.usePosition = 0;
			this.searchProduct();
			this.updateLabel();
		},

		delColor() {
			this.colorItem = "";
			this.useAdNum = 0;
			this.doubleAd = 0;
			this.usePosition = 0;
			this.searchProduct();
			this.updateLabel();
		},

		delTag(item) {
			let id = item.id;
			let ind = this.selectedParams.findIndex((item) => item === id);
			if (ind >= 0) {
				this.selectedParams.splice(ind, 1);
			}
			this.updateTag(item);
		},

		delAllTag() {
			this.selectedParams = [];
			this.selectedParamsObj = {};
			this.colorItem = "";
			this.page = 1;
			this.keyword = "";
			this.priceRange = [-990000, 990000];
			this.showPriceRange = false;
		},

		changeKeyword(val) {
			this.customKeyword = !!val;
			this.useAdNum = 0;
			this.doubleAd = 0;
			this.usePosition = 0;
			this.debounceSearchProduct();
			this.debounceLabel();
		},

		toggleColor(item) {
			if (this.colorItem.id === item.id) {
				this.colorItem = "";
			} else {
				this.colorItem = item;
			}
			this.useAdNum = 0;
			this.doubleAd = 0;
			this.usePosition = 0;
			this.searchProduct();
			this.updateLabel();
		},

		formatUrlName(str) {
			return str.substring(1, str.length);
		},

		changeCategory(item) {
			let path = this.isStockPage == "1" ? item.shopRouting : item.customRouting;
			this.$router.push({
				path: path,
			});
		},

		getLabelAttributeValueIds() {
			let arr = [];
			for (let i in this.selectedParamsObj) {
				arr.push({
					parentId: i,
					childIds: this.selectedParamsObj[i].map((item) => item.id),
				});
			}
			if (this.colorItem) {
				arr.push({
					parentId: this.colorItem.fatherId,
					childIds: [this.colorItem.id],
				});
			}
			return arr;
		},

		getAttributeValueIds() {
			let str = "";
			for (let i in this.selectedParamsObj) {
				let val = this.selectedParamsObj[i].map((item) => item.id);
				if (val.length > 0) {
					str += val.join(",") + "-";
				}
			}
			if (this.colorItem) {
				str += this.colorItem.id;
			}
			str = str.replace(/^-*|-*$/g, "");
			return str;
		},

		getProduct(customPage) {
			return new Promise((resolve) => {
				getProductList({
					categoryId: this.parentCateId || this.cateId,
					childCategoryId: this.customKeyword ? "" : this.parentCateId ? this.cateId : null,
					page: customPage || this.page,
					pageSize: this.pageSize,
					keyWord: this.keyword,
					attributeValueIds: this.getAttributeValueIds(),
					userId: this.isLogin ? this.userId : null,
					sorts: this.sorts,
					priceStart: this.priceRange[0],
					priceEnd: this.priceRange[1],
					productType: this.isStockPage,
				})
					.then((res) => {
						this.totalResult = res.data.total;
						this.pages = res.data.pages;
						this.page = res.data.current;
						this.firstCate = false;
						resolve(res);
					})
					.catch((error) => {
						if (error.name === "AbortError") {
							console.log("Request canceled:", error.message);
						}
					});
			});
		},

		searchProduct() {
			this.loadingProduct = true;
			this.page = 1;
			this.next_data_page = 2;
			this.getProduct()
				.then((res) => {
					this.productList = this.setDefaultShowImg(res.data.records);
					//插入广告
					this.insertAdPosition();
				})
				.finally(() => {
					this.loadingProduct = false;
				});
		},

		setDefaultShowImg(list) {
			if (!list) {
				return;
			}
			if (this.isStockPage && this.$store.state.proTheme == "11") {
				list.forEach((item) => {
					item.showImgSrc = item.sceneImg;
					item.selectedColorIndex = -1;
				});
				return list;
			}
			let colorItem = this.colorItem,
				colorId;
			if (colorItem) {
				colorId = colorItem.id;
			}
			list.forEach((item) => {
				if (item.productParamList && item.productParamList.length > 0) {
					let productParamList = item.productParamList;
					let findColorIndex = productParamList.findIndex((citem) => citem.attributeValueId === colorId);
					if (findColorIndex > -1) {
						try {
							item.showImgSrc = JSON.parse(productParamList[findColorIndex].imgJson)[0]?.url;
							item.selectedColorIndex = findColorIndex;
						} catch (e) { }
					} else {
						if (item.sceneImg) {
							item.showImgSrc = item.sceneImg;
							item.selectedColorIndex = -1;
						} else {
							try {
								item.showImgSrc = JSON.parse(productParamList[0].imgJson)[0]?.url;
								item.selectedColorIndex = 0;
							} catch (e) { }
						}
					}
				} else {
					item.showImgSrc = item.sceneImg;
					item.selectedColorIndex = -1;
				}
			});
			return list;
		},

		sortProduct(command) {
			this.sorts = command;
			this.useAdNum = 0;
			this.doubleAd = 0;
			this.usePosition = 0;
			this.searchProduct();
		},

		toggleFilter() {
			this.showFilter = !this.showFilter;
			let loadBtnMainBox = document.querySelector(".loadBtnMainBox");
			if (loadBtnMainBox && !this.isMobile) {
				if (!this.showFilter) {
					loadBtnMainBox.style.width = "100%";
					loadBtnMainBox.style.marginLeft = "0";
				} else {
					loadBtnMainBox.style.width = "calc(100% - 265px)";
					loadBtnMainBox.style.marginLeft = "265px";
				}
			}
			if (this.isMobile) {
				let leftBar = document.querySelector(".leftBar");
				leftBar.style.top = 0;
				let modalHalfListBox = document.querySelector(".modalHalfListBox");
				if (modalHalfListBox) {
					modalHalfListBox.style.zIndex = "auto";
					if (this.showFilter) modalHalfListBox.style.zIndex = "999";
				}
			}
			this.setAdHeight();
		},

		clickTitle(val) {
			let findInd = this.activeNames.findIndex((item) => val.id === item);
			if (findInd >= 0) {
				this.activeNames.splice(findInd, 1);
			} else {
				this.activeNames.push(val.id);
			}
		},

		handleResize() {
			let header = document.querySelector("#modalHeader");
			this.headerHeight = typeof header?.offsetHeight === "number" ? header.offsetHeight : 0;
			this.setSticky();
			this.screenHeight = window.innerHeight;
			let needLoad = false
			if (this.screenWidth > window.innerWidth) {
				if (this.screenWidth > 1400 && window.innerWidth < 1400) {
					this.screenWidth = window.innerWidth
					needLoad = true
				}
			} else {
				if (this.screenWidth < 1400 && window.innerWidth > 1400) {
					this.screenWidth = window.innerWidth
					needLoad = true
				}
			}
			if (needLoad) {
				this.useAdNum = 0;
				this.doubleAd = 0;
				this.usePosition = 0;
				this.debounceSearchProduct();
			}
		},

		//根据行数选择插入的广告位置
		insertAdPosition() {
			if (!this.adData || !this.productList.length) {
				return false;
			}
			if(this.page == 1){
				this.useAdNum = 0;
				this.doubleAd = 0;
				this.usePosition = 0;
			}
			let type = this.$store.getters.isMobile ? "mb" : "pc";

			// 计算当前页的起始位置和结束位置
			const startIndex = (this.page - 1) * this.pageSize + 1; // 起始位置从1开始
			let endIndex = this.page * this.pageSize;
			if (this.productList.length - this.useAdNum < endIndex) endIndex = this.productList.length - this.useAdNum;
			// 插入广告的位置数组，每个元素表示一个广告位的位置
			const adPositions = [];
			var innerIndex;
			// 循环遍历每个广告位
			for (let i = 1; i <= this.adNum; i++) {
				if (this.adNum > this.advert.length) {
					innerIndex = (i - 1) % this.advert.length;
				} else {
					innerIndex = this.usePosition;
				}
				if (i % 2 != 0) {
					let firstIndex;
					if (type == "pc") {
						firstIndex = 16 * ((i + 1) / 2) - 13;
					} else if (type == "mb") {
						firstIndex = 3 - 1 + ((i - 1) / 2) * 12;
					}
					if (firstIndex >= startIndex && firstIndex <= endIndex) {
						firstIndex = firstIndex - this.doubleAd;
						if (this.advert[innerIndex].booth == 2) {
							firstIndex--;
							this.doubleAd++;
						}

						adPositions.push(firstIndex);
						this.usePosition++;
					}
				} else {
					let secondIndex;
					if (type == "pc") {
						secondIndex = 16 * Math.floor((i - 1) / 2) + 9 - 1;
					} else if (type == "mb") {
						secondIndex = 7 - 1 + ((i - 2) / 2) * 12;
					}

					if (secondIndex >= startIndex && secondIndex <= endIndex) {
						secondIndex = secondIndex - this.doubleAd;
						if (this.advert[innerIndex].booth == 2) {
							this.doubleAd++;
						}
						adPositions.push(secondIndex);
						this.usePosition++;
					}
				}
			}
			if (adPositions.length != 0) {
				this.insertAd(adPositions);
			}
			this.setAdHeight();
		},
		//插入广告
		insertAd(indexArray) {
			if (this.productList.length != 0) {
				//广告条数少于广告位
				if (this.advert.length < this.adNum) {
					for (let i = 0; i < indexArray.length; i++) {
						if (this.useAdNum == this.advert.length) this.useAdNum = 0;
						if (i >= this.advert.length) {
							//当广告位多与广告时,循环增加广告到广告位
							const innerIndex = i % this.advert.length;
							let copyAd = JSON.parse(JSON.stringify(this.advert[innerIndex]));
							copyAd.id = generateUUID()
							this.productList.splice(indexArray[i], 0, copyAd);
						} else {
							let adUseList = this.advert.slice(this.useAdNum);
							this.productList.splice(indexArray[i], 0, adUseList[0]);
						}
						this.useAdNum++;
					}
				} else {
					//广告条数多于广告位
					for (let j = 0; j < indexArray.length; j++) {
						let adUseList = this.advert.slice(this.useAdNum);
						this.productList.splice(indexArray[j], 0, adUseList[0]);
						this.useAdNum++;
					}
				}
			}
		},
		//获取左边分类标签大类
		getLabel(fatherId) {
			return this.labelData.find((item) => item.id == fatherId).nameEn;
		},
		teaMobileActive(id) {
			let index = this.selectedParams.indexOf(id);
			if (index >= 0) {
				return true;
			}
			return false;
		},
		setSticky() {
			if (!this.isMobile) {
				let topRight = document.querySelector(".top-right");
				let leftBar = document.querySelector(".leftBar");
				this.selectLabelHeight = this.headerHeight;
				if (leftBar) {
					leftBar.style.top = this.headerHeight + "px";
				}
				if (topRight) {
					topRight.style.top = this.headerHeight + "px";
				}
			} else {
				let topRight = document.querySelector(".top-right");
				if (topRight) {
					topRight.style.top = this.headerHeight + "px";
				}
			}
			this.setAdHeight();
		},
        async loadMoreData(){
            await this.loadThreeData(false);
            this.insertAdPosition();
        },
        async loadThreeData(type = false) {
            let num = this.page;
            if (!type) {
                num = this.page + 1;
                if (this.totalResult === 0 || this.pages < num || this.page <= 0) {
                    return false;
                }
            }
            this.loadingProduct = true;
            let addProduct = await this.getProduct(num).then((res) => {
                return this.setDefaultShowImg(res.data.records);
            });
            this.productList = this.productList.concat(addProduct);
            this.loadingProduct = false;
        },
		listScrollTop() {
			try {
				window.scrollTo({
					top: 0,
					behavior: "smooth",
				});
			} catch (error) {
				console.log("找不到dom");
			}
		},
		activeCateName(item) {
			let data = this.isStockPage == "1" ? item.shopRouting : item.customRouting;
			return data && this.formatUrlName(data) === this.cateName;
		},
		setModalType(target, targetArray, clickType, event, other) {
			this.$setModal(this, target, targetArray, clickType, event, other);
		},
		setAdHeight() {
			this.adBoxHeight = 0;
			this.$nextTick(() => {
				try {
					let productGoods = document.querySelector(".productGoods");
					this.adBoxHeight = productGoods.getBoundingClientRect().height + "px";
				} catch (error) { }
			});
		},
	},
	async mounted() {
		if (process.env.isManage) {
			this.isManage = true;
			return false;
		}
		if (this.isMobile) this.showFilter = false;
		let header = document.querySelector("#modalHeader");
		// this.headerHeight = typeof header?.offsetHeight === "number" ? header.offsetHeight : 0;
		this.headerHeight = header.getBoundingClientRect().height;
		this.halfName = this.$route.params.halfDesign;
		this.debounceSearchProduct = debounce(this.searchProduct, 600);
		this.debounceLabel = debounce(this.updateLabel, 600);
		this.screenHeight = window.innerHeight;
		this.screenWidth = window.innerWidth;
		this.cateName = this.$route.params.pathMatch;
		window.onresize = this.handleResize;
		Promise.all([
			this.getAppLabelAttributeList(),
			listAllCategoryByParentId({
				id: this.parentCateId || this.cateId,
				onlyGetStockCate: this.isStockPage,
			}),
            getAdvertList({ categoryId: this.parentCateId || this.cateId })
		]).then((result) => {
			let labelData = result[0].data,
				cateList = result[1].data;
			labelData.forEach((item) => {
				if(item.isMore) item.moreText = true;
				if (item.isExpand) this.activeNames.push(item.id);
			});
			this.activeNames.push(-1);
			this.activeNames = [...new Set([...this.activeNames])];
			this.labelData = labelData;
			//左侧选择栏没有数据关闭
			if (!this.labelData || this.labelData.length == 0) this.showFilter = false;
			this.cateList = cateList;
			let name = this.cateList.find((item) => {
				return item.id == this.cateId;
			});
			if (name) {
				this.$store.commit("halfDesign/setIsFastQuote", Boolean(name.isFastQuote));
				this.fatherCateName = name.name;
				if(name.attributeValueId){
					if (!this.labelData || this.labelData.length == 0) return
					let colorData=this.labelData.find((item) => {
						return item.attributeFlag=='color'
					})
					if(colorData){
						colorData?.attributeList.forEach((item) => {
							if(item.id==name.attributeValueId) this.toggleColor(item)
						})

					}
				}
			} else {
				this.$store.commit("halfDesign/setIsFastQuote", false);
				this.fatherCateName = this.halfCateDTO?.name;
			}
            //获取广告数据
            let adData = result[2].data;
            if(adData.length && adData[0]?.advertiseList?.length){
                this.adData = adData[0];
                this.advert = adData[0].advertiseList.filter((obj) => obj.isEnable == 1);
                this.adNum = Math.floor((Number(adData[0].advertiseNum) + 1) / 2);
            }
            this.$nextTick(() => {
                this.fristWatch = true;
                //插入广告
                this.insertAdPosition();
                this.setSticky();
            });
			this.callFn1()
		});
	},
	beforeDestroy() {
		window.onresize = null;
	},
};
</script>

<style scoped lang="scss">
::v-deep .custom-slider .v-range-slider__track-background {
	background-color: $color-second !important;
	/* 设置轨道背景颜色 */
}

// ::v-deep .custom-slider .v-slider__thumb {
// 	background-color: $color-second !important; /* 设置滑动块滑动后的颜色 */
// }
::v-deep .custom-slider .v-range-slider__track-fill {
	background-color: $color-primary !important;
	/* 设置你想要的背景颜色 */
}

.bps_container {
	@include respond-to(mb) {
		font-size: 12px;
	}
}

.loadFirstData {
	display: flex;
	justify-content: center;
	align-items: center;
	min-height: 300px;
}

.find-agent {
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	margin-bottom: 10px;
	width: 100%;
	height: 100%;
	padding: 20px;
	text-align: center;
	background: url(https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20221018/2050Ntxm6NPD.png) center/cover no-repeat;

	.c-title {
		font-size: 48px;
		color: #00156b;
	}

	.des {
		max-width: 680px;
		margin: 10px 0 20px;
		font-size: 32px;
		color: #00156b;
	}

	.v-btn {
		width: 200px;
		margin: 0 10px;
	}

	@media screen and (max-width: $mb-width) {
		.c-title {
			font-size: 24px;
		}

		.des {
			font-size: 16px;
		}

		.v-btn {
			width: 80%;
			margin-bottom: 10px;
		}
	}
}

.wrap {
	display: flex;
	flex-direction: column;
	background-color: #fff;

	@include respond-to(mb) {
		padding-bottom: 10px;
	}
}

img {
	width: auto;
	max-width: 100%;
	max-height: 100%;
	vertical-align: middle;
}

.leftBarHeader {
	padding: 10px 0;

	.breadCrumbs {
		display: flex;
		margin: 0px 0 10px;

		b {
			margin: 0 4px;
			font-size: 12px;
		}

		.bread-item {
			display: flex;
			align-items: center;
			cursor: pointer;

			.bread-item-span {
				font-size: 1em;
				font-weight: 400;
				cursor: auto;
			}
		}

		.bread-item.is-link span {
			color: $color-primary;
		}

		.bread-item:last-child b {
			display: none;
		}

		@include respond-to(mb) {
			margin: 10px 0;
			font-size: 14px;

			.bread-item-span {
				font-size: 12px !important;
			}
		}
	}

	.filterBox {
		display: flex;
		flex-direction: column;

		.filterHeader {
			display: flex;
			align-items: center;
			justify-content: space-between;

			.filterTitle {
				font-weight: 700;
				font-size: 20px;
				color: #000000;

				.filterTotal {
					font-weight: normal;
					font-style: italic;
					font-size: 16px;
					color: #666666;
					margin-left: 10px;
				}
			}

			.icon-hxsht-xp-gb {
				font-size: 18px;
			}
		}

		.filterContent {
			margin: 10px 0;
			overflow: hidden;
			display: flex;
			flex-wrap: wrap;
			gap: 4px;
		}

		.clearFilter {
			display: flex;
			align-items: center;
			font-weight: 400;
			font-size: 14px;
			color: #333333;

			::v-deep .v-icon {
				margin-left: -4px;
			}

			a {
				line-height: normal;
			}

			@include respond-to(mb) {}
		}
	}

	@include respond-to(mb) {
		padding-top: 50px;
	}
}

.borderBox {
	width: 100%;
	height: 0px;
	border: 1px solid #f0f0f0;
}

.litterArrow {
	margin-left: 2px;

	&::before {
		font-size: 8px;
	}
}

.rightHeader {
	display: flex;
	align-items: center;
	flex-wrap: wrap;
	width: fit-content;
	column-gap: 3vw;

	@include respond-to(mb) {
		gap: initial;
	}
}

.top-right {
	display: flex;
	align-items: center;
	justify-content: space-between;
	background: #f4f5f7;
	padding: 10px;
	margin: 20px 0 10px;

	&.sticky {
		z-index: 10;
	}

	.filterSearch {
		flex: 1;
		display: flex;
		align-items: center;

		&.textSearch {}
	}

	.search-area {
		position: relative;

		input {
			min-width: 400px;
			background-color: #fff;
			height: 36px;
			line-height: 36px;
			outline: none;
			border: none;
			padding: 0 20px 0 20px;
		}

		i {
			position: absolute;
			right: 10px;
			top: 50%;
			transform: translateY(-50%);
			font-size: 26px;
			color: #9b9b9b;
		}
	}

	.filter-area {
		display: flex;
		justify-content: space-between;
		align-items: center;
		font-weight: 400;
		font-size: 16px;
		color: #333333;

		.filter1 {
			display: flex;
			align-items: center;
			cursor: pointer;
			margin: 0 20px;

			b {
				margin-right: 4px;
				font-size: 18px;
			}
		}

		.filter2 {
			display: flex;
			align-items: center;

			.t1 {
				margin-right: 4px;
			}

			b {
				font-size: 12px;
			}
		}

		.filter3 {
			display: flex;
			align-items: center;
			border: 2px solid $color-primary;
			border-radius: 4px;

			svg {
				color: $color-primary;
			}

			&>div {
				padding: 4px 8px;
				cursor: pointer;
			}

			&>div.active {
				background-color: $color-primary;

				svg {
					color: #ffffff;
				}
			}
		}

		.v-btn {
			text-transform: capitalize;
		}
	}

	@include respond-to(mb) {
		flex: 1;
		border-radius: 4px;
		width: 100%;

		.search-area {
			position: relative;
			width: 100%;
			overflow: hidden;

			.inputBox {
				margin: 0 auto;
				text-align: center;
			}

			input {
				min-width: 100%;
				background-color: #fff;
				border-radius: 2px;
				height: 30px;
				line-height: 30px;
				outline: none;
				border: none;
				padding: 0 30px 0 20px;
			}

			i {
				position: absolute;
				right: 10px;
				top: 50%;
				transform: translateY(-50%);
				font-size: 20px;
				color: #9b9b9b;
			}
		}

		.filter-area {
			font-size: 12px;
			width: 30%;

			.filter1 {
				margin: 0 0 0 auto;
			}

			.filter2 {
				display: none;
			}

			&>div {
				margin: 0 10px;
			}
		}
	}
}

#borderBoxSticky {
	background: #f4f5f7;
}

.content {
	position: relative;
	display: grid;
	align-items: flex-start;
	grid-column-gap: 20px;
	grid-template-columns: 265px 1fr;
	padding-bottom: 50px;

	.loadProgress {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        padding: 10px 0;
	}

	.load {
		display: flex;
		justify-content: center;
		align-items: center;
		min-height: 200px;
		padding: 20px;
	}

	&.noFilter {
		grid-template-columns: 1fr;
		grid-column-gap: 18px;
		grid-row-gap: 0px;
		padding-bottom: 50px;

		.rightPart {
			.rightContent {
				.good-item {
					.goods {
						grid-template-rows: auto 1fr;
					}
				}
			}
		}
	}

	.leftBar {
		position: sticky;
		top: 0;
		overflow: hidden;
		// overflow: hidden auto;
		max-height: 800px;
		display: flex;
		flex-direction: column;
		width: 265px;
		padding: 10px 0px 40px;
		// border-radius: 4px;
		background-color: #ffffff;

		&::-webkit-scrollbar {
			display: none;
		}

		.filter {
			font-size: 14px;

			.top {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 10px;

				a {
					color: $color-primary;
					text-decoration: underline;
				}

				span {
					color: #c4c4c4;
					font-style: italic;
				}
			}
		}

		.collapse {
			flex: 1;
			overflow: hidden auto;
			padding: 10px 0px 40px;
			scrollbar-width: none;

			&::-webkit-scrollbar {
				display: none;
			}

			.collapse-item {
				.sizeScroll {
					max-height: 145px;
					overflow-y: auto;
				}
			}

			.collapse-item-title {
				display: flex;
				justify-content: space-between;
				align-items: center;
				height: 50px;
				font-size: 16px;
				cursor: pointer;
				text-transform: uppercase;

				b {
					font-size: 12px;
				}

				.active {
					transform: rotate(-180deg);
				}
			}

			.con {
				::v-deep .v-label {
					font-size: 14px;
				}

				.showBtn {
					font-size: 14px;
					color: $color-primary;
					margin: 5px 0;
					text-decoration: underline;
					cursor: pointer;
				}

				.price-range-box {
					padding: 0 10px;
					text-align: right;

					i {
						font-size: 16px;
						color: #666666;
						margin-right: -8px;
						cursor: pointer;
					}
				}

				.price-des {
					display: flex;
					justify-content: space-between;
					align-items: center;
					font-size: 14px;
				}

				.color-box {
					.color-wrap {
						display: grid;
						grid-template-columns: repeat(6, calc((100% - (6 - 1) * 4px) / 6));
						grid-column-gap: 4px;
						grid-row-gap: 4px;

						.color-item {
							display: flex;
							justify-content: center;
							align-items: center;
							width: 100%;
							height: 100%;
							aspect-ratio: 1/1;
							background: #ffffff;
							// border: 1px solid #cccccc;
							border: 2px solid #f2f2f2;
							border-radius: 50%;
							cursor: pointer;

							&:hover {
								border-color: $color-primary;
								border-width: 2px;
							}

							&.active {
								border-color: $color-primary;
								border-width: 2px;
							}

							span {
								display: inline-block;
								border-radius: 50%;
								border: 2px solid #f2f2f2;
							}

							img {
								border-radius: 50%;
							}
						}
					}
				}

				.checkbox-style2 ::v-deep {
					display: grid;
					grid-template-columns: repeat(2, 1fr);
				}
			}
		}
	}

	.leftBar[disabled] {
		pointer-events: none;
	}

	.category-item {
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 14px;
		font-size: 14px;
		cursor: pointer;
		background: #e8e9eb;
		border: 1px solid transparent;
		flex-shrink: 0;

		span {
			padding: 5px 15px;
		}

		&.active {
			font-weight: bold;
			color: var(--tag-color, var(--color-primary));
			background-color: var(--tag-color-lighten, var(--color-second));
			border: 1px solid var(--color-primary-lighten);
		}

		&.tag {
			padding: 5px 10px;
			border-radius: 0;
			color: $color-primary;
			color: var(--tag-color, var(--color-primary));
			background-color: var(--tag-color-lighten, var(--color-second));

			.delIconBox {
				display: flex;
				align-items: center;
				justify-content: center;
			}

			&:hover {
				border: 1px solid var(--color-primary-lighten);

				.v-icon {
					color: #666666;
				}
			}
		}

		&:hover {
			color: var(--tag-color, var(--color-primary));
			background-color: var(--tag-color-lighten, var(--color-second));
			border: 1px solid var(--color-primary-lighten);

			.v-icon {
				color: #ffffff;
			}
		}

		span {
			b {
				font-size: 10px;
			}
		}

		@include respond-to(mb) {
			white-space: nowrap;
			padding: 5px;
		}
	}

	.selectLabel {
		width: 100%;
		padding: 10px;
		margin-bottom: 10px;
		background: #f4f5f7;
		border-radius: 2px;
		box-shadow: 1px 1px 4px 0px rgba(0, 0, 0, 0.06);

		.filter {
			font-size: 14px;

			.top {
				display: flex;
				justify-content: space-between;

				.cateitemBox {
					flex: 1;
					width: fit-content;
					display: flex;
					flex-wrap: wrap;
					gap: 4px;
					max-height: 30px;
					overflow: hidden;
					/*category-item*/
				}

				.total {
					color: #c4c4c4;
					font-style: italic;
					margin: 1em 0 0;
				}

				.viewMore {
					width: fit-content;
					display: flex;
					align-items: center;
					margin-left: auto;
					margin-right: -10px;
					padding: 5px 10px;
					cursor: pointer;
					color: $color-primary;
					flex-shrink: 0;
					white-space: nowrap;
					cursor: pointer;

					b {
						font-size: 10px;
					}

					&:hover {
						opacity: 0.8;
						text-decoration: underline;
					}
				}
			}
		}

		@include respond-to(mb) {
			margin-bottom: 10px;
			background: #f3f4f6;
			border-radius: 2px;
			// box-shadow: 1px 1px 4px 0px rgba(0, 0, 0, 0.06);

			.filter {
				font-size: 14px;

				.top {
					width: 100%;
					flex-wrap: nowrap;
					padding-bottom: 5px;
					overflow: hidden;

					.customLabel {
						height: 2em;
					}

					.label {
						padding-left: 0;
					}

					.viewMore {
						padding: 2px 6px;
						margin-right: 0;
					}
				}
			}
		}
	}

	.rightBar {
		.rightPart {
			position: relative;
			display: flex;
			flex-direction: column;
			height: 100%;
			padding: 2px;
			overflow: hidden;
			margin-top: 10px;

			.rightContent {
				flex: 1;
				width: 100%;
				height: 0;
				position: relative;

				.productWrap {
					// align-items: flex-start;
					display: grid;
					grid-template-columns: repeat(4, 1fr);
					grid-gap: 20px;

					@include respond-to(pad) {
						grid-template-columns: repeat(3, 1fr);
					}

					@include respond-to(mb) {
						grid-template-columns: repeat(1, 1fr);
						grid-gap: 10px;
					}
				}

				.inserted_element {
					grid-column: span 2;
					grid-row: span 1;

					@include respond-to(mb) {
						grid-column: span 1;
					}
				}

				@media (any-hover: hover) {
					.good-item:hover .good-back {
						background-color: var(--color-second);
					}
				}
			}
		}
	}

	@include respond-to(mb) {
		grid-template-columns: 1fr;
		padding: 0;

		.leftBar {
			position: fixed;
			width: 100%;
			height: 100%;
			top: 0;
			left: 0;
			z-index: 100000;
			padding: 0 10px 40px;
			// max-height: 400px !important;

			.leftBar-header {
				position: sticky;
				top: 0;
				margin-left: -10px;
				width: calc(100% + 20px);
				height: 50px;
				background-color: $color-primary;
				color: #fff;
				z-index: 99;

				.filter {
					text-align: center;
					font-weight: bold;
					font-size: 1.5em;
					line-height: 50px;
					margin: 0;
				}

				b {
					position: absolute;
					top: 0;
					right: 14px;
					line-height: 50px;
					z-index: 2;
					font-size: 14px;
				}
			}

			.filter-mb {
				.filter-mb-title {
					height: 50px;
					display: flex;
					justify-content: space-between;
					align-items: center;
					cursor: pointer;
					text-transform: uppercase;
					font-size: 16px;
					color: #333;
					line-height: 50px;

					b {
						font-weight: 400;
						font-size: 12px;
					}

					.active {
						transform: rotate(-180deg);
					}
				}

				.con {
					display: flex;
					align-items: flex-start;
					gap: 10px;
					flex-direction: column;
					font-size: 14px;

					.con-radio {}

					label {
						display: flex;
						align-items: center;
						flex-direction: row-reverse;

						span {
							margin-right: 10px;
						}
					}

					/* 未选中状态下的样式 */
					input[type="radio"] {
						/* 隐藏原始的单选按钮 */
						display: none;
					}

					/* 自定义样式 */
					.custom-radio {
						display: inline-block;
						width: 14px;
						height: 14px;
						border-radius: 50%;
						border: 1px solid #333;
						position: relative;
						cursor: pointer;
					}

					/* 选中状态下的样式 */
					.custom-radio:before {
						content: "";
						display: block;
						width: 6px;
						height: 6px;
						background-color: #fff;
						position: absolute;
						top: 50%;
						left: 50%;
						transform: translate(-50%, -50%);
						border-radius: 50%;
					}

					/* 选中状态下的外圈样式 */
					input[type="radio"]:checked+.custom-radio {
						background-color: $color-primary !important;
						border: 1px solid $color-primary;
					}
				}
			}

			.collapse {
				.collapse-item {
					.collapse-item-title {
						font-size: 14px;
					}

					.con {
						.color-box {
							.color-wrap {
								align-items: center;
								justify-items: self-start;
							}

							.color-item {
								max-width: 32px;
								max-height: 32px;
							}
						}
					}
				}
			}
		}
	}
}

.loadBtnMainBox {
	@include respond-to(mb) {
		width: 100%;
		margin-left: 0;
	}
}

.loadMoreBtn {
	width: 30%;
	min-width: 350px;
	margin: 0 auto;
	margin-bottom: 50px;
	text-align: center;
	font-weight: 400;
	font-size: 22px;
	color: #333333;

	.loadMoreBtnText {
		margin-bottom: 20px;
	}

	.loadBtnBox {
		margin-top: 20px;
		color: $color-primary;
		cursor: pointer;
		font-size: 16px;

		.loadBtn {
			display: inline-block;
			border: 1px solid $color-primary;
			padding: 8px 40px;
			border-radius: 5px;
			transition: 0.3s;

			&:hover {
				background-color: $color-second;
			}
		}
	}

	@include respond-to(mb) {
		min-width: 300px;
		font-size: 18px;

		.loadMoreBtnText {
			margin-bottom: 10px;
		}

		.loadBtnBox {
			margin-top: 10px;
			font-size: 14px;
		}
	}
}

.sticky {
	position: sticky;
	top: 0;
	z-index: 1;
}

.litterMock::before {
	content: "";
	width: 100%;
	height: 10px;
	position: absolute;
	bottom: -10px;
	left: 0;
	background-color: #f2f2f2;
}

.litterMock::after {
	content: "";
	width: 100%;
	height: 10px;
	position: absolute;
	top: -10px;
	left: 0;
	background-color: #f2f2f2;
}

.changeLoad {
	position: absolute;
	inset: 0;
	width: 100%;
	// height: 100vh;
}

.overlay {
	align-items: center;
	border-radius: inherit;
	display: flex;
	justify-content: center;
	pointer-events: auto;
	width: 100%;
	background-color: rgba(0, 0, 0, 0.05);
}

/* HTML: <div class="loader"></div> */
.loader {
	width: 50px;
	aspect-ratio: 1;
	display: grid;
	border: 4px solid #0000;
	border-radius: 50%;
	border-right-color: $color-primary;
	animation: l15 1s infinite linear;
}

.loader::before,
.loader::after {
	content: "";
	grid-area: 1/1;
	margin: 2px;
	border: inherit;
	border-radius: 50%;
	animation: l15 2s infinite;
}

.loader::after {
	margin: 8px;
	animation-duration: 3s;
}

@keyframes l15 {
	100% {
		transform: rotate(1turn);
	}
}
</style>
