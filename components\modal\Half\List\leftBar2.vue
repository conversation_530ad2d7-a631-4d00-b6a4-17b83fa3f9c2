<template>
	<div class="leftBar2" :disabled="loadLabel" ref="leftBar2">
		<div class="leftBarHeader">
			<div class="filterBox">
				<div class="filterHeader">
					<div class="filterTitle">
						{{ langSemiCustom.filters }}
						<span class="filterTotal">({{ totalResult }} {{ langSemiCustom.results }})</span>
					</div>
					<div v-if="isMobile" @click="toggleFilter">
						<b class="iconfont icon-hxsht-xp-gb"></b>
					</div>
				</div>
				<div class="filterContent" v-show="Object.keys(selectedParamsObj).length > 0 || colorItem">
					<template v-if="colorItem">
						<div class="category-item tag" :class="{ isLuggagetags: isLuggagetags }">
							<span class="colorPointer" :style="{ backgroundColor: colorItem.colorCode }"></span>
							<span class="tagName">{{ colorItem.name }}</span>
							<div @click="delColor" class="delIconBox">
								<v-icon small style="margin-left: 4px">mdi-close</v-icon>
							</div>
						</div>
					</template>
					<div class="tagBox" v-for="(item, key, index) in selectedParamsObj" :key="key">
						<div class="category-item tag" :class="{ isLuggagetags: isLuggagetags }" v-for="citem in item" :key="citem.id">
							<span class="tagName">{{ citem.name }}</span>
							<div @click="delTag(citem, key)" class="delIconBox">
								<v-icon small style="margin-left: 4px">mdi-close</v-icon>
							</div>
						</div>
					</div>
				</div>
				<div class="clearFilter" @click="delAllTag" v-show="Object.keys(selectedParamsObj).length > 0 || colorItem">
					<v-icon style="font-size: 20px">mdi-trash-can-outline</v-icon>
					<a href="javascript:;">
						{{ langDesign.clearAll }}
					</a>
				</div>
			</div>
		</div>
		<div class="overlay changeLoad" v-show="loadLabel" style="z-index: 1">
			<div class="loader"></div>
		</div>
		<div class="collapse">
			<div class="collapse-item" v-for="(item, index) in labelData" :key="item.id">
				<div class="collapse-item-title" @click="clickTitle(item)">
					<strong class="text-truncate">{{ item.name }}</strong>
					<b class="icon-Down" :class="{ active: activeNames.includes(item.id) }"></b>
				</div>
				<v-expand-transition>
					<div class="con" :class="{ isLuggagetags: isLuggagetags }" v-show="activeNames.includes(item.id)">
						<template v-if="item.flag == 2">
							<div>
								<v-radio-group v-model="item.radio">
									<v-radio v-for="citem in item.childList" :key="citem.id" @change="updateTag(citem)" :label="`${citem.name}`" :value="citem.id"></v-radio>
								</v-radio-group>
								<div class="borderBox" v-show="index !== labelData.length - 1" style="margin-top: 20px"></div>
							</div>
						</template>
						<template v-else="item.flag == 1">
							<div class="box color-box">
								<div class="color-wrap">
									<div v-for="citem in item.childList" :key="citem.id" class="color-wrap-item" :class="{ hasImg: citem.images && citem.images.length > 0 }">
										<v-tooltip top :disabled="isMobile" :content-class="contentClass">
											<template v-slot:activator="{ on, attrs }">
												<div v-bind="attrs" v-on="on" class="color-item" :class="{ active: colorItem && citem.id === colorItem.id }" @click="toggleColor(citem)">
													<img v-if="citem.images" :src="citem.images" :alt="citem.name" :title="citem.name" />
													<span v-else :style="{ backgroundColor: citem.colorCode }"></span>
												</div>
											</template>
											<span style="font-size: 12px" v-show="citem.name">{{ citem.name }} </span>
										</v-tooltip>
									</div>
								</div>
								<div class="borderBox" v-show="index !== labelData.length - 1" style="margin-top: 20px"></div>
							</div>
						</template>
					</div>
				</v-expand-transition>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	name: "leftBar2",
	props: {
		isStockPage: {},
		loadLabel: {
			type: Boolean,
			default: false,
		},
		labelData: {
			type: Array,
			default: () => [],
		},
		totalResult: {
			type: [Number, String],
			default: 0,
		},
		selectedParamsObj: {
			type: Object,
			default: () => ({}),
		},
		selectedParams: {
			type: Array,
			default: () => [],
		},
		colorItem: {},
		activeNames: {
			type: Array,
			default: () => [],
		},
		isLuggagetags: {
			type: Boolean,
			default: false,
		},
	},
	components: {},
	data() {
		return {};
	},
	watch: {},
	computed: {
		isMobile() {
			return this.$store.state.device === "mb";
		},
		isDialog() {
			return !!this.name;
		},
		langSemiCustom() {
			return this.$store.getters.lang?.semiCustom;
		},
		langDesign() {
			return this.$store.getters.lang?.design;
		},
		contentClass() {
			return this.isLuggagetags ? "contentClass" : "color-tooltip-item";
		},
	},
	methods: {
		toggleFilter() {
			this.$emit("toggleFilter");
		},
		delAllTag() {
			this.labelData.forEach((item) => (item.radio = ""));
			this.$emit("delAllTag");
		},
		delColor() {
			this.$emit("delColor");
		},
		delTag(item, id) {
			let data = this.labelData.find((item) => item.id == id);
			if (data) data.radio = "";
			this.$emit("delTag", item);
		},
		clickTitle(id) {
			this.$emit("clickTitle", id);
		},
		toggleColor(item) {
			this.$emit("toggleColor", item);
		},
		updateTag(item) {
			this.$emit("updateTag", item);
		},
	},
	created() {},
	mounted() {},
};
</script>

<style scoped lang="scss">
.loader {
	width: 50px;
	aspect-ratio: 1;
	display: grid;
	border: 4px solid #0000;
	border-radius: 50%;
	border-right-color: $color-primary;
	animation: l15 1s infinite linear;
}

.loader::before,
.loader::after {
	content: "";
	grid-area: 1/1;
	margin: 2px;
	border: inherit;
	border-radius: 50%;
	animation: l15 2s infinite;
}
.loader::after {
	margin: 8px;
	animation-duration: 3s;
}
@keyframes l15 {
	100% {
		transform: rotate(1turn);
	}
}

.changeLoad {
	position: absolute;
	inset: 0;
	width: 100%;
	// height: 100vh;
}

.overlay {
	align-items: center;
	border-radius: inherit;
	display: flex;
	justify-content: center;
	pointer-events: auto;
	width: 100%;
	background-color: rgba(0, 0, 0, 0.05);
}

.color-tooltip-item.v-tooltip__content {
	background: linear-gradient(90deg, rgba(49, 83, 225, 1), rgba(237, 34, 210, 1));
}
.contentClass.v-tooltip__content {
	background: linear-gradient(90deg, var(--color-bright), var(--btn-primary));
}

.category-item {
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 14px;
	font-size: 14px;
	cursor: pointer;
	background: #e8e9eb;
	border: 1px solid transparent;
	flex-shrink: 0;
	&.isLuggagetags {
		&.active {
			border-image: linear-gradient(90deg, var(--color-bright), var(--btn-primary)) 1 1;
		}
		&.tag {
			.tagName {
				background-image: linear-gradient(90deg, var(--color-bright), var(--btn-primary));
			}

			&::after {
				background-image: linear-gradient(90deg, var(--color-bright), var(--btn-primary));
			}
		}
		@include respond-to(mb) {
			white-space: nowrap;
			&.tag {
				border: 1px solid var(--btn-primary);
				.tagName {
					background:initial;
				}
			}
		}
	}

	&.cateTag {
		span {
			padding: 5px 15px;
		}
	}

	&.active {
		font-weight: bold;
		color: var(--tag-color, var(--color-primary));
		background-color: var(--tag-color-lighten, var(--color-second));
		border: 1px solid var(--color-primary-lighten);
		border-image: linear-gradient(90deg, rgba(49, 83, 225, 1), rgba(237, 34, 210, 1)) 1 1;
	}

	&.tag {
		position: relative;
		padding: 3px 10px;
		border-radius: 20px;
		background-color: #f9f9f9;
		// color: transparent;
		// background-image: linear-gradient(90deg, rgba(49, 83, 225, 1), rgba(237, 34, 210, 1));
		// background-clip: text;
		// border: 1px solid transparent;
		border: 0;
		.delIconBox {
			display: flex;
			align-items: center;
			justify-content: center;
		}
		.tagName {
			position: relative;
			display: block;
			color: transparent;
			background-image: linear-gradient(90deg, #3153e1, #ed22d2);
			background-clip: text;
			z-index: 3;
		}

		// &::before {
		// 	content: "";
		// 	position: absolute;
		// 	inset: 0;
		// 	background-color: #f9f9f9;
		// 	border-radius: 20px;
		// 	z-index: -1;
		// }

		&::after {
			content: "";
			position: absolute;
			left: 50%;
			top: 50%;
			transform: translate(-50%, -50%);
			width: calc(100% + 2px);
			height: calc(100% + 2px);
			padding: 1px;
			background-image: linear-gradient(90deg, rgba(49, 83, 225, 1), rgba(237, 34, 210, 1));
			mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0) border-box;
			mask-composite: exclude;
			-webkit-mask-composite: xor;
			border-radius: 20px;
			z-index: -1;
		}

		&:hover {
			// border: 1px solid var(--color-primary-lighten);
			// border-image: linear-gradient(0deg, #3253e1 0%, #f05ade 100%) 1 1;

			.v-icon {
				color: #666666;
			}
		}
	}

	&:hover {
		.v-icon {
			color: #ffffff;
		}
	}

	.colorPointer {
		width: 15px;
		height: 15px;
		border-radius: 50%;
		border: 1px solid #f2f2f2;
		margin-right: 4px;
	}

	span {
		b {
			font-size: 10px;
		}
	}

	@include respond-to(mb) {
		white-space: nowrap;
		&.tag {
			border: 1px solid rgba(49, 83, 225, 1);
			&::after {
				all: unset;
			}
			.tagName {
				background: initial;
				color: #333;
				font-weight: bold;
			}
		}
	}
}

.borderBox {
	width: 100%;
	height: 0px;
	border: 1px solid #f0f0f0;
}

.leftBar2[disabled] {
	pointer-events: none;
}

.leftBar2 {
	position: sticky;
	top: 0;
	overflow: hidden;
	max-height: 800px;
	display: flex;
	flex-direction: column;
	width: 100%;
	padding: 10px 0px 40px;
	background-color: #f9f9f9;

	&::-webkit-scrollbar {
		display: none;
	}

	.filter {
		font-size: 14px;

		.top {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 10px;

			a {
				color: $color-primary;
				text-decoration: underline;
			}

			span {
				color: #c4c4c4;
				font-style: italic;
			}
		}
	}

	.collapse {
		flex: 1;
		overflow: hidden auto;
		padding: 10px 0px 40px;
		padding-right: 10px;
		scrollbar-width: none;
		border-right: 1px solid #efefef;

		&::-webkit-scrollbar {
			display: none;
		}

		.collapse-item {
			.sizeScroll {
				max-height: 145px;
				overflow-y: auto;
			}
		}

		.collapse-item-title {
			display: flex;
			justify-content: space-between;
			align-items: center;
			height: 50px;
			font-size: 16px;
			cursor: pointer;
			text-transform: uppercase;

			b {
				font-size: 12px;
			}

			.active {
				transform: rotate(-180deg);
			}
		}

		.con {
			&.isLuggagetags {
				::v-deep .v-radio {
					.primary--text {
						background-image: linear-gradient(90deg, var(--color-bright), var(--btn-primary)) !important;
					}
				}
				.color-box {
					.color-wrap {
						.color-item {
							&:hover {
								border-image: linear-gradient(90deg, var(--color-bright), var(--btn-primary)) 1 1;
							}

							&.active {
								border-image: linear-gradient(90deg, var(--color-bright), var(--btn-primary)) 1 1;
							}
						}
					}
				}
			}

			::v-deep .v-label {
				font-size: 14px;
			}

			::v-deep .v-input--selection-controls {
				margin-top: 4px;
				padding-top: 0px;
			}

			::v-deep .v-input__slot {
				margin-bottom: 0px;
			}

			::v-deep .v-messages {
				display: none;
			}

			::v-deep .v-radio {
				.primary--text {
					background-image: linear-gradient(90deg, rgba(49, 83, 225, 1), rgba(237, 34, 210, 1)) !important;
					-webkit-background-clip: text;
					background-clip: text;
					color: transparent !important;
				}
				.v-input--selection-controls__input {
					margin-left: 4px;
					width: 20px;
					height: 20px;
					.v-icon.v-icon {
						font-size: 20px;
					}
					.v-input--selection-controls__ripple {
						top: 50%;
						left: 50%;
						transform: translate(-50%, -50%);
						height: 22px;
						width: 22px;
						margin: 0;
					}
				}
			}

			.showBtn {
				font-size: 14px;
				color: $color-primary;
				margin: 5px 0;
				text-decoration: underline;
				cursor: pointer;
			}

			.price-range-box {
				padding: 0 10px;
				text-align: right;

				i {
					font-size: 16px;
					color: #666666;
					margin-right: -8px;
					cursor: pointer;
				}
			}

			.price-des {
				display: flex;
				justify-content: space-between;
				align-items: center;
				font-size: 14px;
			}

			.color-box {
				.color-wrap {
					margin-top: 4px;
					display: grid;
					grid-template-columns: repeat(6, calc((100% - (6 - 1) * 10px) / 6));
					grid-column-gap: 10px;
					grid-row-gap: 10px;
					.hasImg {
						grid-column: span 3;
						.color-item {
							aspect-ratio: initial;
							img {
								object-fit: contain;
							}
						}
					}
					.color-wrap-item {
						width: 100%;
						height: 100%;
					}
					.color-item {
						display: flex;
						justify-content: center;
						align-items: center;
						width: 100%;
						height: 100%;
						aspect-ratio: 1/1;
						background: #ffffff;
						// border: 1px solid #cccccc;
						border: 1px solid #f2f2f2;
						border-radius: 4px;
						cursor: pointer;

						&:hover {
							// border-color: $color-primary;
							border-image: linear-gradient(90deg, rgba(49, 83, 225, 1), rgba(237, 34, 210, 1)) 1 1;
							border-width: 1px;
						}

						&.active {
							// border-color: $color-primary;
							border-image: linear-gradient(90deg, rgba(49, 83, 225, 1), rgba(237, 34, 210, 1)) 1 1;
							border-width: 1px;
						}

						span {
							width: 100%;
							height: 100%;
							display: inline-block;
							border: 1px solid #f2f2f2;
						}

						img {
							object-fit: contain;
						}
					}
				}
			}

			.checkbox-style2 ::v-deep {
				display: grid;
				grid-template-columns: repeat(2, 1fr);
			}
		}
	}
	@include respond-to(mb) {
		position: fixed;
		width: 100%;
		height: 100%;
		top: 0;
		left: 0;
		z-index: 100000;
		padding: 0 20px 40px;
		// max-height: 400px !important;

		.leftBar-header {
			position: sticky;
			top: 0;
			margin-left: -10px;
			width: calc(100% + 20px);
			height: 50px;
			background-color: $color-primary;
			color: #fff;
			z-index: 99;

			.filter {
				text-align: center;
				font-weight: bold;
				font-size: 1.5em;
				line-height: 50px;
				margin: 0;
			}

			b {
				position: absolute;
				top: 0;
				right: 14px;
				line-height: 50px;
				z-index: 2;
				font-size: 14px;
			}
		}

		.filter-mb {
			.filter-mb-title {
				height: 50px;
				display: flex;
				justify-content: space-between;
				align-items: center;
				cursor: pointer;
				text-transform: uppercase;
				font-size: 16px;
				color: #333;
				line-height: 50px;

				b {
					font-weight: 400;
					font-size: 12px;
				}

				.active {
					transform: rotate(-180deg);
				}
			}

			.con {
				display: flex;
				align-items: flex-start;
				gap: 10px;
				flex-direction: column;
				font-size: 14px;

				.con-radio {
				}

				label {
					display: flex;
					align-items: center;
					flex-direction: row-reverse;

					span {
						margin-right: 10px;
					}
				}

				/* 未选中状态下的样式 */
				input[type="radio"] {
					/* 隐藏原始的单选按钮 */
					display: none;
				}

				/* 自定义样式 */
				.custom-radio {
					display: inline-block;
					width: 14px;
					height: 14px;
					border-radius: 50%;
					border: 1px solid #333;
					position: relative;
					cursor: pointer;
				}

				/* 选中状态下的样式 */
				.custom-radio:before {
					content: "";
					display: block;
					width: 6px;
					height: 6px;
					background-color: #fff;
					position: absolute;
					top: 50%;
					left: 50%;
					transform: translate(-50%, -50%);
					border-radius: 50%;
				}

				/* 选中状态下的外圈样式 */
				input[type="radio"]:checked + .custom-radio {
					background-color: $color-primary !important;
					border: 1px solid $color-primary;
				}
			}
		}

		.collapse {
			border: 0;
			.collapse-item {
				.collapse-item-title {
					font-size: 14px;
				}

				.con {
					.color-box {
						.color-wrap {
							align-items: center;
							justify-items: self-start;
							.color-wrap-item.hasImg {
								grid-column: span 2;
								.color-item {
									max-width: 100%;
									max-height: none;
								}
							}
						}

						.color-item {
							max-width: 32px;
							max-height: 32px;
						}
					}
				}
			}
		}
	}
}

.leftBarHeader {
	padding: 0px 0 10px;
	padding-right: 10px;
	border-right: 1px solid #efefef;
	.filterBox {
		display: flex;
		flex-direction: column;

		.filterHeader {
			display: flex;
			align-items: center;
			justify-content: space-between;

			.filterTitle {
				font-weight: 700;
				font-size: 20px;
				color: #000000;

				.filterTotal {
					font-weight: normal;
					font-style: italic;
					font-size: 16px;
					color: #666666;
					margin-left: 10px;
				}
			}

			.icon-hxsht-xp-gb {
				font-size: 18px;
			}
		}

		.filterContent {
			padding: 2px;
			margin: 10px 0;
			overflow: hidden;
			display: flex;
			flex-wrap: wrap;
			gap: 4px;

			.tagBox {
				display: flex;
				flex-wrap: wrap;
				gap: 4px;
			}
		}

		.clearFilter {
			display: flex;
			align-items: center;
			font-weight: 400;
			font-size: 14px;
			color: #333333;

			::v-deep .v-icon {
				margin-left: -4px;
			}

			a {
				line-height: normal;
			}

			@include respond-to(mb) {
			}
		}
	}

	@include respond-to(mb) {
		padding-top: 30px;
		border: 0;
	}
}
</style>
