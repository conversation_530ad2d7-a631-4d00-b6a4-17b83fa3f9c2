<template>
	<PublicStep :stepData="stepData" :maskName="maskName" :selectedData="selectedData" :showNextBtn="true" @showMaskFn="$emit('showMaskFn', $event)" @selfCloseMask="selfCloseMask">
		<template #title>
			<b style="display: block; height: 1px"></b>
		</template>
		<template #content>
			<div class="sizeBox">
				<div class="step-size">
					<div class="step-size-box">
						<div class="step-size-leftArea">
							<div class="size-area custom-shadow">
								<h3 class="step-title">
									<span stepColor>{{ lang.Step }} {{ stepData.customIndex }}: </span>
									{{ lang.Select }}
									{{ stepData.alias ? stepData.alias : stepData.paramName }}
								</h3>
								<p class="step-size-title">
									{{ smallTitle }}
								</p>
								<div class="size-item-wrap">
									<div
										class="size-item item"
										v-for="(citem, cindex) in stepData.childList"
										:key="citem.id"
										@click="selectStep(citem)"
										:class="{
											active: hasId(citem.id, selectedData[stepData.paramName]),
											onlyInquiry: citem.onlyAddInquiry === 1,
										}"
									>
										<CustomCircle circle-type="1"></CustomCircle>
										<div class="textWrap">
											<p class="normal-text">
												{{ citem.alias ? citem.alias : citem.paramName }}
											</p>
											<ToolTip :item-data="citem"></ToolTip>
										</div>
										<Corner v-if="citem.labelText" :backgroundColor="citem.labelColor" type2 position="absolute">
											{{ citem.labelText }}
										</Corner>
									</div>
								</div>
							</div>
							<div class="step-size-rightArea forMB" :class="{ colorImg: showMbColorImg }">
								<div class="textWrap">
                                    <p>{{ sizeImgP1 }}</p>
									<b class="icon-xialajiantou" @click="showSizeImg = !showSizeImg" 
									:style="showSizeImg ? 'transform: rotate(180deg);' : ''"></b>
								</div>
								<div class="shape-img" v-if="showSizeImg">
									<img :src="shapeImgCopy || shapeImg" alt="" />
								</div>
							</div>
							<div class="d-flex-center confirmBtnWrap">
								<QuoteBtn
									@click.native="$emit('showMaskFn', stepData.paramName)"
									:style="{
										opacity: selectedData[stepData.paramName].length > 0 ? 1 : 0.5,
									}"
									:disabled="!selectedData[stepData.paramName].length > 0"
									>{{ lang.next }}
								</QuoteBtn>
							</div>
						</div>
						<div class="step-size-rightArea">
							<div class="textWrap">
								<p class="normal-text">{{ sizeImgP1 }}</p>
							</div>
							<div class="shape-img" :style="pid === 275 ? 'width: 308px;': ''">
								<img :src="shapeImgCopy || shapeImg" alt="" />
							</div>
						</div>
					</div>
				</div>
				<!--触发刷新-->
				<span v-show="false">{{ shapeImg }}</span>
			</div>
		</template>
		<template #footer>
			<b style="display: none"></b>
		</template>
	</PublicStep>
</template>

<script>
import PublicStep from "~/components/Quote/PublicStep/index.vue";
import ToolTip from "~/components/Quote/ToolTip.vue";
import Corner from "~/components/Medals/Corner.vue";
import CustomCircle from "~/components/Quote/customCircle.vue";

export default {
	components: { CustomCircle, Corner, ToolTip, PublicStep },
	props: {
		stepData: {
			type: Object,
			default: () => {
				return {};
			},
		},
		maskName: {
			type: [String, Boolean],
		},
		smallTitle: {
			type: String,
		},
		sizeImgP1: {
			type: String,
		},
		showMbColorImg: {
			type: Boolean,
			default: false,
		},
		selectedData: {
			type: Object,
		},
		generalData: {
			type: Array,
			default: () => [],
		},

		//Size默认图展示下标 所对应的图
		findSizeModel: {
			type: Object,
			default: () => {
				return {
					//当前选中的size
					index: 0, //默认
					/**
					  this.findSizeModel = item; //当前选中对象
						this.findSizeModel.index = nIndex;//当前选中对象size下标
					 **/
				};
			},
		},

		//购物车回填
		shoppingModel: {
			type: Object,
			default: () => {
				return {
					//moke数据
					/* alias: "Height: 4.5 Inches - Width: 5 Inches",id: 12833,sizeAlias: "Height: 4.5 Inches - Width: 5 Inches" */
				};
			},
		},
	},
	data() {
		return {
			shapeImgCopy: undefined, //备份 赋值用.
			showSizeImg:false,
			pid:0,
		};
	},
	watch: {},
	computed: {
		lang() {
			return this.$store.getters.lang.quote || {};
		},
		shapeImg() {
			let findSize = this.generalData.find((item) => {
				return item.paramType === "SIZE";
			});
			this.pid = findSize.cateId;
			this.shapeImgCopy = undefined;
			if (!findSize) {
				return "";
			}
			let defaultUrl;

			if (this.findSizeModel && this.findSizeModel.index) {
				defaultUrl = this.findSizeModel.imageJsonJson[this.findSizeModel.index].url;
				return defaultUrl;
			} else {
				if (findSize.childList?.length > 0) {
					defaultUrl = JSON.parse(findSize.childList[0].imageJson)[0].url;
				}
				let sizeItem = this.selectedData[findSize.paramName][0];
				if (sizeItem) {
					return JSON.parse(sizeItem.imageJson)[0].url;
				} else {
					return defaultUrl;
				}
			}
		},
	},
	created() {
		//购物车回填
		this.shoppingFunc();
	},
	methods: {
		//初始化size默认图片 取当前size childList下第一个对象  如： { alt:"5",name: "",url:"xxx"}
		initShapeImg() {
			let copy = "";
			let findSize = this.generalData.find((item) => {
				return item.paramType === "SIZE";
			});
			this.pid = findSize.cateId;
			this.shapeImgCopy = undefined;
			if (!findSize) {
				return "";
			}
			let defaultUrl;
			defaultUrl = JSON.parse(findSize.childList[0].imageJson)[0].url;
			let sizeItem = this.selectedData[findSize.paramName][0];
			if (sizeItem) {
				copy = JSON.parse(sizeItem.imageJson)[0].url;
			} else {
				copy = defaultUrl;
			}
			this.shapeImgCopy = copy;
			this.$forceUpdate();
		},
		//购物车回填
		shoppingFunc() {
			if (this.shoppingModel?.id) {
				//console.log("我是谁哈哈哈", this.shoppingModel, this.stepData);
				this.selectStep(this.shoppingModel);
			}
		},

		//判断是否选中
		hasId(id, arr) {
			if (!arr) {
				return false;
			}
			return !!arr.find((item) => {
				return item.id === id;
			});
		},
		selectStep(item) {
			//定制checked选中字段
			this.stepData.childList.map((n) => {
				n.checked = false;
			});
			item.checked = true;
			this.showSizeImg = true;
			this.$emit("clickFun", item);
		},
		selfCloseMask() {
			this.$emit("closeMask");
		},
	},
};
</script>

<style lang="scss" scoped>
.sizeBox {
	.step-title {
		font-size: 24px;
		font-weight: 700;
		color: #333333;
		margin-bottom: 23px;

		> span {
			color: $color-primary;
		}
	}

	.confirmBtnWrap {
		margin-top: 33px;

		@include respond-to(mb) {
			margin-top: 20px;
		}
	}

	.step-size {
		.step-title {
			margin-bottom: 30px;
			@media screen and (max-width: 767px) {
				margin-bottom: 20px;
			}
		}

		.tips.type2 {
			top: 50%;
			transform: translateY(-50%);
			right: 1.3333vw;
			left: auto;
		}

		.step-size-box {
			display: grid;
			grid-template-columns: 2fr 1.4fr;
			justify-content: space-between;
			column-gap: 25px;
			row-gap: 23px;

			.step-size-title {
				margin-bottom: 23px;
				margin-top: -25px;
				font-size: 16px;
			}

			.step-size-leftArea {
				.size-area {
					align-self: center;
					margin-bottom: 18px;

					.size-item-wrap {
						display: grid;
						grid-template-columns: repeat(3, 1fr);
						row-gap: 14px;
						column-gap: 5px;
						// grid-template-rows: 40px;
						// flex-wrap: wrap;
						font-size: 16px;

						.size-item {
							border: 1px solid transparent;
							position: relative;
							display: flex;
							// justify-content: center;
							align-items: center;
							// width: 130px;
							height: 40px;
							// margin: 0 15px 15px 0;
							background: #f4f5f5;
							// border: 1px solid #e1e3e6;
							border-radius: 10px;
							padding: 0 4px;
							padding-left: 12px;
							cursor: pointer;
							transition: all 0.3s;

							::v-deep .customCircle {
								margin-right: 5px;
							}

							.normal-text {
								transition: all 0.3s;
								margin-bottom: 0;
								text-align: center;
							}

							.textWrap {
								display: flex;
								align-items: center;

								p {
									margin-right: 10px;
								}
							}

							@media (any-hover: hover) {
								&:hover {
									box-shadow: 0 3px 4px 0 #cccccc;
									border-color: $color-primary;

									.customCircle {
										border-color: $color-primary;
										background: $color-primary;

										&::after {
											background-color: #fff;
										}
									}
								}
							}

							.tip-icon {
								color: $color-primary;
							}
						}

						// .size-item.onlyInquiry {
						//   width: 216px;
						// }

						.size-item.active {
							border-color: $color-primary;

							.customCircle {
								border-color: $color-primary;
								background: $color-primary;

								&::after {
									background-color: #fff;
								}
							}
						}
					}
				}

				.shape-area {
					position: relative;
					background-color: #fff;

					.shape-item-wrap {
						display: grid;
						grid-template-columns: repeat(5, 1fr);
						column-gap: 10px;
						justify-content: space-between;
						margin: 10px 0 0;

						.shape-item {
							position: relative;
							display: flex;
							flex-direction: column;
							justify-content: center;
							align-items: center;
							height: 85px;
							border: 1px solid #e6e6e6;
							border-radius: 10px;
							padding: 8px 0;
							cursor: pointer;

							@media (any-hover: hover) {
								&:hover {
									border-color: $color-primary;

									.circle {
										border-color: $color-primary;

										.inner-circle {
											border-color: $color-primary;

											&::after {
												background-color: $color-primary;
											}
										}
									}
								}
							}

							.shapeIcon {
								width: 48px;
								height: 32px;
								color: #d9d9d9;
							}

							.imgWrap {
								display: flex;
								justify-content: center;
								align-items: center;
							}

							span {
								margin-top: 4px;
								white-space: nowrap;
								font-size: 16px;
								text-transform: capitalize;
							}

							.circle {
								background: #ffffff;
							}
						}

						.shape-item.active {
							position: relative;
							border-color: $color-primary;

							.shapeIcon {
								color: $color-primary;
							}

							.circle {
								border-color: $color-primary;

								.inner-circle {
									border-color: $color-primary;

									&::after {
										background-color: $color-primary;
									}
								}
							}
						}
					}
				}
			}

			.step-size-rightArea {
				display: flex;
				flex-direction: column;
				position: relative;
				padding: 20px;
				background-color: #f6f6f6;

				@media screen and (max-width: 767px) {
					background-color: white;
					padding: 0;
				}

				&.forMB {
					display: none;

					@media screen and (max-width: 767px) {
						display: block;
					}
				}

				border-radius: 10px;

				&::before {
					position: absolute;
					left: -20px;
					top: 20px;
					content: "";
					width: 0;
					height: 0;
					border-width: 20px 0 20px 20px;
					border-style: solid;
					border-color: transparent transparent #f6f6f6;

					@media screen and (max-width: 767px) {
						display: none;
					}
				}

				.textWrap {
					text-align: left;
					display: flex;
					align-items: center;
					gap:5px;
					margin-bottom: 10px;
					
					.normal-text {
						margin-bottom: 8px;
						font-size: 18px;
						color: #333333;
						transition: all 0.3s;

						@media screen and (max-width: 767px) {
							font-size: 3.2vw;
						}
					}

					@media screen and (max-width: 767px) {
						font-size: 3.2vw;
					}

					.gray-text {
						margin-bottom: 12px;
						font-size: 14px;
						color: #9ca1a6;
					}
				}

				.shape-img {
					flex: 1;
					display: flex;
					justify-content: center;
					align-items: center;

					img {
						object-fit: contain;
					}
				}
			}
		}
	}

	@include respond-to(ipad) {
		padding: 30px 10px;

		.small-title {
			margin-bottom: 15px;
		}

		.step-size {
			.step-size-box {
				.step-size-title {
					margin-bottom: 17px;
				}

				.step-size-leftArea {
					.size-area {
					}
				}

				.step-size-rightArea {
					margin-left: 20px;
				}
			}
		}
	}

	@include respond-to(mb) {
		// margin-bottom: 10px;
		background-color: #fff;
		border-radius: 5px;

		.step-size {
			.step-size-box {
				grid-template-columns: 1fr;
			}
		}

		.step-title {
			margin-bottom: 10px;
			font-size: 14px;
			font-weight: bold;
			color: #171719;
		}

		.step-size {
			.step-size-box {
				.step-size-title {
					margin-bottom: 8.5px;
					margin-top: -10px;
					font-size: 12px;
				}

				.step-size-leftArea {
					.size-area {
						background: #ffffff;
						border: 1px solid #d6d6d6;
						border-radius: 5px;
						padding: 12px 5px;

						@include respond-to(mb) {
							border-top: 0;
							border-left: 0;
							border-right: 0;
							border-bottom: 1px solid #d6d6d6;
							border-radius: 5px 5px 0 0;
							padding: 0 0 8px;
							margin-bottom: 15px;
						}

						.size-item-wrap {
							grid-template-columns: repeat(2, 1fr);
							row-gap: 0px;

							.size-item {
								justify-content: flex-start;
								flex-basis: calc(33.3% - 4px);
								height: 30px;
								margin: 0 2px 5px;
								font-size: 12px;
								background: #f4f5f5;
								border-radius: 5px;
								border-color: transparent;
								padding: 0 10px;

								p.normal-text {
									font-size: 12px;
								}
							}

							.size-item.onlyInquiry {
								flex-basis: 200px;
							}
						}
					}

					.shape-area {
						background: #ffffff;
						border: 1px solid #d6d6d6;
						border-radius: 5px;
						padding: 12px 5px;
						padding-bottom: 20px;

						.shape-item-wrap {
							grid-template-columns: repeat(3, 1fr);
							column-gap: 6.5px;
							row-gap: 15px;

							.shape-item {
								height: 66px;
								border-color: transparent;
								background: #f4f5f5;
								border-radius: 5px;

								.shapeIcon {
									width: 33px;
									height: 24px;
								}

								span {
									font-size: 12px;
								}

								.circle {
									background-color: #f4f5f5;
								}
							}
						}
					}
				}

				.step-size-rightArea {
					display: none;

					&.forMB {
						display: none;

						@include respond-to(mb) {
							display: block;

							&.colorImg {
								// background-color: #f6f6f6;
								border-radius: 10px;
								display: flex;
								flex-direction: column;
								// padding: 20px;
								position: relative;

								img {
									height: 260px;
								}

								// &::after {
								// 	content: "";
								// 	width: 0;
								// 	height: 0;
								// 	position: absolute;
								// 	left: -20px;
								// 	top: 20px;
								// 	border-color: transparent transparent #f6f6f6;
								// 	border-style: solid;
								// 	border-width: 20px 0 20px 20px;
								// }
							}
						}
					}
				}
			}
		}
	}
}
</style>
