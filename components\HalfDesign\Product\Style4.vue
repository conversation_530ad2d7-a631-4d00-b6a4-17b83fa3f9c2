<template>
	<div class="mb-4" :class="stepData.styleName">
		<div class="tips">
			{{ stepData.attributeTitle }}
		</div>
		<slot name="stepText"></slot>
		<div class="tab">
			<div class="tab-nav-item" :class="{ active: index === tabIndex }" v-for="(item, index) in tabContent" @click="selectTab(index)">
				<div class="nav-title">
					{{ item.paramName }}
				</div>
				<div class="nav-des">{{ langSemiCustom.selectOption }}</div>
			</div>
		</div>
		<template v-for="(item, index) in tabContent">
			<div class="tab-content custom-scrollbar" v-if="index === tabIndex && item.list.length > 0">
				<div class="tab-item" v-for="citem in item.list">
					<div class="tab-item-title">
						{{ citem.paramName }}
					</div>
					<div class="tab-item-content">
						<template v-for="colorItem in citem.list">
							<v-tooltip top>
								<template v-slot:activator="{ on, attrs }">
									<div class="tab-item-color" v-bind="attrs" v-on="on" :style="customColorStyle(colorItem)"></div>
								</template>
								<span>{{ colorItem.codeName }}</span>
							</v-tooltip>
						</template>
					</div>
				</div>
			</div>
		</template>
	</div>
</template>
<script>
export default {
	props: {
		stepData: {
			type: Object,
			default: {},
		},
	},
	data() {
		return {
			selectIndex: -1,
			selectItem: null,
			tabIndex: 0,
			tabContent: [
				{
					paramName: "FRONT",
					list: [
						{
							paramName: "MATTE",
							list: [
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
							],
						},
						{
							paramName: "GLOSSY",
							list: [
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
							],
						},
					],
				},
				{
					paramName: "LEFT",
					list: [
						{
							paramName: "MATTE",
							list: [
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
							],
						},
						{
							paramName: "GLOSSY",
							list: [
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
							],
						},
					],
				},
				{
					paramName: "RIGHT",
					list: [
						{
							paramName: "MATTE",
							list: [
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
							],
						},
						{
							paramName: "GLOSSY",
							list: [
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
							],
						},
					],
				},
				{
					paramName: "LENS",
					list: [
						{
							paramName: "MATTE",
							list: [
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
							],
						},
						{
							paramName: "GLOSSY",
							list: [
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
							],
						},
					],
				},
				{
					paramName: "K LOGO",
					list: [
						{
							paramName: "MATTE",
							list: [
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
							],
						},
						{
							paramName: "GLOSSY",
							list: [
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
								{
									code: "#ffe450",
									codeName: "blue",
									codeImg: "https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230116/20230116YEC4e3BF.jpg",
								},
							],
						},
					],
				},
			],
		};
	},
	computed: {
		shape() {
			return this.selectItem?.valueName;
		},
		langSemiCustom() {
			return this.$store.getters.lang?.semiCustom;
		},
	},
	methods: {
		selectStep(item, index) {
			this.selectIndex = index;
			this.selectItem = item;
			this.$emit("selectStep", {
				type: this.stepData.attributeFlag,
				data: item,
				parent: this.stepData,
				id: this.stepData.id,
			});
		},
		selectTab(index) {
			this.tabIndex = index;
		},
		customColorStyle(colorItem) {
			return {
				backgroundColor: colorItem.code,
			};
		},
	},
	mounted() {},
};
</script>
<style scoped lang="scss">
@import "~assets/css/half.scss";

.tips {
	margin-bottom: 10px;
	color: $gray-text;
}

.tab {
	display: grid;
	grid-template-columns: repeat(5, 1fr);
	margin-bottom: 20px;

	.tab-nav-item {
		position: relative;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		padding: 10px;
		border: 1px solid $border-color;
		margin-right: -1px;
		cursor: pointer;

		.nav-des {
			color: $gray-text;
			margin-top: 4px;
			font-size: 12px;
		}

		&:first-child {
			border-top-left-radius: $step-border-radius;
			border-bottom-left-radius: $step-border-radius;
		}

		&:last-child {
			border-top-right-radius: $step-border-radius;
			border-bottom-right-radius: $step-border-radius;
		}

		&:hover {
			border-color: $color-primary;
			z-index: 1;
		}

		&.active {
			border-color: $color-primary;
			z-index: 1;

			&::after {
				display: block;
			}
		}

		&::after {
			display: none;
			content: "";
			position: absolute;
			bottom: -10px;
			left: 50%;
			transform: translateX(-50%);
			width: 0;
			height: 0;
			border-width: 10px 10px 0;
			border-style: solid;
			border-color: $color-primary transparent transparent;
		}
	}
}

.tab-content {
	overflow: hidden auto;
	max-height: 270px;

	.tab-item {
		.tab-item-title {
			margin-bottom: 4px;
		}

		.tab-item-content {
			display: grid;
			grid-template-columns: repeat(15, 1fr);
			grid-gap: 10px;
			margin-bottom: 10px;

			.tab-item-color {
				position: relative;
				border: 1px solid $border-color;
				cursor: pointer;
				aspect-ratio: 1;

				&::after {
					display: none;
					content: "";
					position: absolute;
					bottom: 0;
					left: 50%;
					transform: translateX(-50%);
					width: 0;
					height: 0;
					border-width: 0 5px 5px;
					border-style: solid;
					border-color: transparent transparent $color-primary;
				}

				@media (any-hover: hover) {
					&:hover {
						border-color: $color-primary;
						border-width: 2px;
					}
				}

				&.active {
					border-color: $color-primary;
					border-width: 2px;

					&::after {
						display: block;
					}
				}
			}
		}
	}
}
</style>
