<template>
	<div class="enamelpinsProductListWrap modal-box">
		<h1>Enamel Pin Gallery</h1>
		<div class="filter">
			<div>
				Filters
				<span style="color: #999999">({{ total }} Results)</span>
			</div>
			<div class="dropdown" v-click-outside="() => (showCateExtend = false)">
				<div class="dropdownTrigger" @click="showExtend" :class="{ active: showCateExtend }">Pin Types <b
					class="icon-Down"></b></div>
				<div class="dropdownList" v-show="showCateExtend">
					<div class="dropdownItem" :class="{ active: formData.cateId == 0 }" @click="
						changeCate({
							cateId: 0,
						})
						">
						All
					</div>
					<div class="dropdownItem" :class="{ active: item.id == formData.cateId }" v-for="item in cateList"
						 :key="item.id" @click="changeCate(item)">
						{{ item.categoryEn }}
					</div>
				</div>
			</div>
		</div>
		<div class="productList">
			<div class="productItem" v-for="(item, index) in productList" :key="index" @click="showPrev(index)">
				<Pic :src="item.picPathA" :alt="item.nameEn" :title="item.nameEn" />
			</div>
		</div>
		<div class="loadMore" v-if="formData.page < pages">
			<button primary plain @click="viewMore" title="View More Enamel Pins">View more</button>
		</div>
		<base-dialog persistent v-model="dialog" class="productDialog" width="auto">
			<div class="content">
				<div class="swiper myswiper1" ref="swiper1">
					<div class="swiper-wrapper" style="background-color: #fff;">
						<div class="swiper-slide" v-for="(item, index) in productList" :key="index">
							<img :src="item.picPathA" :alt="item.nameEn" :title="item.nameEn" />
						</div>
					</div>
				</div>
				<div class="swiper-button-next"></div>
				<div class="swiper-button-prev"></div>
				<div class="textContent">
					<h2>{{ currentProductItem.nameEn }}</h2>
					<p>{{ currentProductItem.briefDescriptionEn }}</p>
					<div class="btnGroup">
						<button primary plain class="viewMore" title="View Details of Custom Enamel Pins" @click="toDetail">View
							more</button>
						<button primary class="quote" title="order custom enamel pins" @click="openQuote">Quote for Similar
							Design</button>
					</div>
				</div>
			</div>
		</base-dialog>
	</div>
</template>

<script>
import { getProductGalleryList } from "@/api/web";
import Pic from "@/components/pic.vue";
import BaseDialog from "@/components/Quote/BaseDialog.vue";

export default {
	name: "EnamelPinsExhibition",

	components: { BaseDialog, Pic },

	props: ['productList', 'total', 'pages', 'formData', 'cateList', 'currentCate'],

	data() {
		return {
			mySwiper: null,
			dialog: false,
			showCateExtend: false,
			currentProductIndex: 0,
			currentProductItem: {},
		};
	},

	methods: {
		openQuote() {
			this.$store.commit("setMask", {
				modal: "modalQuoteDialog",
				quoteUrl: "/quote/custom-enamel-pins?type=quoteIframe",
			});
		},
		toDetail() {
			this.$router.push({
				path: `/products/${this.currentProductItem.pageUrl}`,
			});
		},
		initSwiper() {
			let that = this;
			this.mySwiper = new Swiper(this.$refs.swiper1, {
				slidesPerView: 1,
				spaceBetween: 8,
				watchSlidesVisibility: true, //防止不可点击
				grabCursor: true,
				on: {
					slideChangeTransitionEnd: function (value) {
						that.currentProductItem = that.productList[value.activeIndex];
					},
				},
				navigation: {
					nextEl: ".content .swiper-button-next",
					prevEl: ".content .swiper-button-prev",
				},
			});
			this.mySwiper.slideTo(this.currentProductIndex);
		},
		showPrev(index) {
			this.dialog = true;
			this.currentProductIndex = index;
			this.currentProductItem = this.productList[index];
			this.$nextTick(() => {
				this.initSwiper();
			});
		},
		changeCate(item) {
			if (item.cateId === 0) {
				this.$router.push({
					path: `/Exhibition`,
				});
				return;
			}
			let name = item.categoryEn.replace(/\s+/g, "-").toLowerCase();
			this.$router.push({
				path: `/s/${name}`
			});
		},
		showExtend() {
			this.showCateExtend = !this.showCateExtend;
		},
		async viewMore() {
			let formData = this.formData;
			if (formData.page >= this.pages) {
				return;
			}
			formData.page += 1;
			let listData = await getProductGalleryList(formData);
			this.productList = this.productList.concat(listData.data.records);
		},
	}
};
</script>

<style lang="scss" scoped>
button[primary][plain] {
	background-image: none;
	background-color: #ffffff;
	color: $color-primary;
	border: 1px solid $color-primary;
}

.enamelpinsProductListWrap {
	z-index: auto;
	height: 100%;
	padding-top: 1.5rem !important;
	padding-bottom: 5rem !important;

	h1 {
		margin-bottom: 1.8rem;
		text-align: center;
	}

	.filter {
		display: flex;
		gap: 1.88rem;
		height: 3.13rem;
		background-color: #F4F5F7;
		align-items: center;
		padding: 0 1.19rem;
		margin-bottom: 1.25rem;

		@include respond-to(mb) {
			background-color: transparent;
			height: auto;
			padding: 0;
			gap: 1.25rem;
		}

		.dropdown {
			cursor: pointer;

			@include respond-to(mb) {
				padding: 0.5rem 1rem;
				background: #f4f5f7;
				border-radius: 1.25rem;
			}

			.dropdownTrigger.active {
				color: $color-primary;

				b.icon-Down {
					transform: rotate(180deg);
				}
			}

			.dropdownList {
				position: absolute;
				width: 10.5rem;
				background: #ffffff;
				border-radius: 4px;
				border: 1px solid #ccc;
				padding: 0.5rem 0;
				margin-left: -2rem;
				margin-top: 0.5rem;
				box-shadow: 0 1rem 1rem 0 rgba(0, 0, 0, 0.44);

				.dropdownItem {
					padding: 0.5rem 1rem;

					&:hover {
						background-color: #f2f2f2;
					}

					&.active {
						color: $color-primary;
					}
				}
			}
		}

		.icon-Down {
			font-size: 0.875rem;
		}
	}

	.productList {
		display: grid;
		grid-template-columns: repeat(4, 1fr);
		gap: 1.25rem;

		@include respond-to(mb) {
			grid-template-columns: repeat(2, 1fr);
			gap: 0.7rem;
		}

		.productItem {
			aspect-ratio: 1;
			cursor: pointer;
		}
	}

	.loadMore {
		display: flex;
		justify-content: center;
		align-items: center;
		margin-top: 2rem;

		button {
			width: 13.75rem;
		}
	}
}

.productDialog {
	z-index: 1000;

	::v-deep .base-dialog-model-con {
		background-color: transparent;
	}

	::v-deep .icon-guanbi {
		position: absolute;
		top: -50px !important;
		right: -50px !important;

		@include respond-to(mb) {
			right: 0 !important;
			top: -4rem !important;
		}
	}

	.content {
		width: 31.25rem;
		background-color: transparent;
		border-radius: 0.38rem;
		text-align: center;

		@include respond-to(mb) {
			width: 30rem;
		}

		.swiper-button-next,
		.swiper-button-prev {
			display: flex;
			justify-content: center;
			align-items: center;
			background-color: rgba(255, 255, 255, 0.5);
			border-radius: 50%;
			width: 3.38rem;
			height: 3.38rem;
			color: #ffffff;
		}

		.textContent {
			background-color: #fff;
		}

		.swiper-button-next {
			top: 40%;
			right: -80px;

			@include respond-to(mb) {
				top: auto;
				bottom: -5rem;
				right: 30%;
			}
		}

		.swiper-button-prev {
			top: 40%;
			left: -80px;

			@include respond-to(mb) {
				top: auto;
				bottom: -5rem;
				left: 30%;
			}
		}

		.swiper-button-next::after {
			content: "";
			font-family: "modalicon";
			font-size: 1rem;
		}

		.swiper-button-prev::after {
			content: "";
			font-family: "modalicon";
			font-size: 1rem;
		}

		h2 {
			margin: 0;
			padding: 1.81rem 0 1.08rem;
			font-size: 1.13rem;
		}

		p {
			padding: 0 0.5rem;
		}

		.btnGroup {
			display: flex;
			justify-content: center;
			align-items: center;
			gap: 1rem;
			margin-top: 1.63rem;
			padding-bottom: 1rem;

			button {
				min-width: auto;
				font-size: 1rem;
				height: 2.5rem;
				padding: 0;
			}

			.viewMore {
				width: 11.25rem;
			}

			.quote {
				width: 15.63rem;
			}
		}
	}
}
</style>
