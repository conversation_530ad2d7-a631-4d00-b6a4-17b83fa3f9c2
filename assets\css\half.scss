$border-color: #e2e2e2;
$background-color: #f9f9f9;
$background-color2: #ebebeb;
$step-border-radius: 6px;
$circle-border-color: #dddddd;
$circle-bg-color: #dddddd;
$color-red: #de3500;
$gray-text: #999999;

.errorTip {
	display: none;
	margin: 10px 0;
}

.nextBtn {
	display: none;
	padding: 10px 0;
	width: 150px;
	margin: 24px auto;
	background-color: $color-primary;
	color: #ffffff;
	border-radius: 4px;
	text-align: center;

	@include respond-to(mb) {
		display: block;
	}
}

.nextBtn2 {
	display: none;
	padding: 5px;
	width: 150px;
	margin: 10px auto;
	background: linear-gradient(135deg, var(--bg-primary) -120%, var(--bg-dark));
	color: #ffffff;
	border-radius: 4px;
	text-align: center;
	box-shadow: 0px 3px 4px 1px var(--bg-dark);

	@include respond-to(mb) {
		display: block;
	}
}

.noNextBtn {
	display: none;

	@include respond-to(mb) {
		display: none;
	}
}

@mixin radius-response {
	border-radius: 6px;

	@include respond-to(mb) {
		border-radius: 4px;
	}
}

@mixin flex-center {
	display: flex;
	justify-content: center;
	align-items: center;
}

@mixin border-1 {
	border: 1px solid $border-color;
}

@mixin step-default {
	@include border-1;
	padding: 10px;
	@include radius-response;

	&.active {
		border-width: 2px;
		border-color: $color-primary;
		color: $color-primary;
		padding: 9px;
	}

	@media (any-hover: hover) {
		&:hover {
			border-width: 2px;
			border-color: $color-primary;
			color: $color-primary;
			padding: 9px;
		}
	}
}

.step-content {
	.step-item {
		font-size: 14px;
	}

	@include respond-to(mb) {
		.step-item {
			font-size: 12px;
		}
	}
}

img {
	width: auto;
	max-width: 100%;
	max-height: 100%;
	object-fit: contain;
}

.absolute-top-right {
	position: absolute;
	right: 0;
	top: 0;
}

.absolute-top-right2 {
	position: absolute;
	right: 0;
	top: 0;
	--border-radius: 0.8em;

	&.check-icon ::v-deep {
		display: flex;
		width: 18px;
		height: 18px;

		.v-icon {
			font-size: 14px !important;
		}
	}

	@include respond-to(mb) {
		&.check-icon ::v-deep {
			display: flex;
			width: 16px;
			height: 16px;

			.v-icon {
				font-size: 12px !important;
			}
		}
	}
}

.absolute-topOut-right {
	position: absolute;
	top: 0;
	right: 0;
	transform: translate(50%, -50%);
}

.color-picker-wrap {
	overflow: hidden auto;
	max-height: 300px;
}

.color-picker-title {
	position: relative;
	padding: 10px;
	text-align: center;

	.done {
		position: absolute;
		right: 10px;
		top: 50%;
		transform: translateY(-50%);
		color: $color-primary;
		cursor: pointer;
	}
}

.color-picker-list {
	display: grid;
	grid-template-columns: repeat(6, 1fr);
	grid-gap: 20px;
	padding: 10px;

	.color-item {
		position: relative;
		@include flex-center;
		aspect-ratio: 1;
		border-radius: 50%;
		cursor: pointer;
		border: 1px solid $border-color;

		.v-icon {
			display: none;
		}

		&::before {
			content: "";
			position: absolute;
			left: -4px;
			top: -4px;
			right: -4px;
			bottom: -4px;
			border-radius: 50%;
			border: 1px solid $border-color;
		}

		@media (any-hover: hover) {
			&:hover {
				&::before {
					border-color: $color-primary;
					;
				}

				.v-icon {
					display: inline-block;
				}
			}
		}
	}

	.color-item.active {

		&::before {
			display: block;
			border-color: $color-primary;
		}

		.v-icon {
			display: inline-block;
		}
	}
}

.colorCircle {
	border: 1px solid #CCCCCC;
}