<template>
	<div class="modal-box" :class="modal.class" :style="modal.style">
		<template v-for="(o, oi) in modal.outer">
			<div class="clear-fix" @click="modal.type.index = oi; modal.type.clickPosition = 'outer'">
				<EditDiv tagName="h2" v-model:content="o.title.value" :style="modal.titleStyle" v-if="o.title"
					@click="setModalType(o.title, modal.outer, 'text')" />

				<div class="sub-title" :style="modal.subTitleStyle" :hidden="!o.title && !o.subTitle"
					@click="setModalType(o.subTitle, modal.outer, 'text')">
					<EditDiv v-model:content="o.subTitle.value" v-if="o.subTitle" />
				</div>
			</div>


			<div class="card-box photo" :class="{ noScroll: modal.noScroll }" :style="{ order: modal.boxStyle?.order }">
				<div class="clear-fix" :style="{ ...modal.boxStyle, order: false }">
					<div class="card part2 hover-tag" :class="{ mask: modal.mask }" v-for="(l, li) in modal.list"
						:key="li" :childHoverIndex="li" :style="{ ...modal.cardStyle, ...l.style }">
						<pic :src="l.img.value" :alt="l.img.alt" :title="l.img.alt" v-if="l.img"
							:style="{ ...modal.cardImgStyle, ...l.img.style }"
							@click="setModalType(l.img, modal.list, 'img')" />

						<div :style="{ ...modal.cardContentStyle, ...l.contentStyle }">
							<EditDiv tagName="h3" v-model:content="l.title.value" :style="modal.cardTitleStyle"
								v-if="l.title" @click="setModalType(l.title, modal.list, 'text')" />

							<EditDiv v-model:content="l.subTitle.value" :style="modal.cardSubTitleStyle" v-if="l.subTitle"
								@click="setModalType(l.subTitle, modal.list, 'text')" />
						</div>
					</div>
				</div>
			</div>

			<div flex class="point-box" v-if="$store.getters.isMobile && !modal.noScroll">
				<div :class="{ select: !scroll }"></div>
				<div :class="{ select: scroll }"></div>
			</div>


			<div class="hover-tag router-btn" v-if="o.button">
				<button :primary="o.button.value" :style="{ ...modal.btnStyle, ...o.button.style }" :title="o.button.alt">
					<EditDiv v-show="o.button.value" tagName="label" v-model:content="o.button.value"
						@click="setModalType(o.button, modal.outer, 'button', o.button)" />
					<b :class="o.button.icon" v-show="o.button.icon"></b>
				</button>
			</div>
		</template>
	</div>
</template>

<script>
export default {
	name: "modalPhoto",
	props: {
		data: {
			type: Object,
			default: {},
		}
	},
	data() {
		return {
			modal: {
				style: {},
				type: {},
				outer: [{}],
				...this.data
			},
			scroll: false
		};
	},
	mounted() {
		if (this.$store.getters.isMobile && !this.modal.noScroll && document.querySelector(`#${this.modal.id} .card-box`)) this.$nextTick(() => {
			document.querySelector(`#${this.modal.id} .card-box`).addEventListener("scroll", e => {
				this.scroll = e.target.scrollLeft > (e.target.scrollWidth - e.target.clientWidth) / 3;
			})
		})
	},
	methods: {
		setModalType(target, targetArray, clickType, event, other) {
			this.$setModal(this, target, targetArray, clickType, event, other)
		},
	},
	watch: {
		modal: {
			handler(val) {
				if (process.env.isManage) this.$emit("update:data", val);
			},
			immediate: true,
			deep: true
		}
	}
};
</script>

<style lang="scss" scoped>
.clear-fix:after {
	content: "";
	display: block;
	visibility: hidden;
	clear: both;
}

.card-box .card {
	float: left;
	text-align: center;
	margin-bottom: 1vmax;
	position: relative;

	img {
		height: 100%;
	}

	h3 {
		padding: 0.7em 0px;
		position: absolute;
		bottom: 0;
		right: 0;
		left: 0;
	}
}

[theme='5'] .card-box .card {
	transition: all .3s;

	&:hover {
		box-shadow: 8px 7px 44px 5px rgba(163, 163, 163, 0.82);
		border-radius: 0.5208vw;
	}
}

.router-btn {
	margin-top: 2.5em;
	text-align: center;
}



@media screen and (max-width: $mb-width) {
	h3 {
		font-weight: normal;
	}

	.card-box:not(.noScroll) {
		overflow-x: auto;

		.clear-fix {
			width: 100vmax;
		}

		&::-webkit-scrollbar {
			width: 0;
		}
	}

	.point-box {
		order: 5;
		justify-content: center;

		div {
			width: 0.5em;
			height: 0.5em;
			border-radius: 1em;
			margin: 0 1.1vw 1vw;
			background: #CCCCCC;

			&.select {
				background: #A6A6A6;
			}
		}
	}

	[theme]:not([theme='4']) .point-box {
		font-size: 0.8em;

		.select {
			width: 1.3em;
			background: $color-primary;
		}
	}
}


.h2-line .clear-fix h2 {
	gap: 2vw;
	display: flex;
	align-items: center;
	justify-content: center;

	&::before,
	&::after {
		all: unset;
		content: "";
		flex: 1;
		border-top: 1px solid #DADADA;
	}
}
</style>
