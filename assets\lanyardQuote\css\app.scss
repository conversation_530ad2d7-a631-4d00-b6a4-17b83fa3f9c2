:root {
	--inputFontFamily: "<PERSON><PERSON><PERSON>";
	--inputFontFamily2: "<PERSON><PERSON><PERSON>";
}

$font_family: var(--inputFontFamily);
$font_family2: var(--inputFontFamily2);

body {
	padding: 0;
	margin: 0;
	font-family: <PERSON><PERSON><PERSON>;

	@media screen and (max-width: 768px) {
		font-family: Arial;
	}
}

.lanyardQuote {
	.content {
		box-sizing: content-box;
	}

	::v-deep .swiperDialog {
		.el-dialog__body {
			padding: 15px;
		}

		.el-dialog__header {
			padding: 0;
		}

		.swiper-container-wrap .top {
			height: 348px;
		}
	}

	img {
		vertical-align: inherit;
	}

	* {
		font-family: Calibri;
	}

	.position-relative {
		position: relative;
	}

	.pointer {
		cursor: pointer;
	}

	.flex {
		display: flex !important;
	}

	.el-radio {
		height: auto;
	}

	.justify-content-center {
		justify-content: center;
	}

	.justify-content-end {
		justify-content: flex-end;
	}

	.align-items-center {
		align-items: center;
	}

	.justify-content-between {
		justify-content: space-between;
	}

	.justify-content-end {
		justify-content: flex-end;
	}

	.text-center {
		text-align: center;
	}

	.flex-column {
		flex-direction: column;
	}

	.border1 {
		border: 1px solid #d9dbdd;
	}

	.radius6 {
		border-radius: 6px;
	}

	.colorB3 {
		color: #b3b3b3;
	}

	.f16 {
		font-size: 16px;
	}

	.m-0 {
		margin: 0;
	}

	.mt-0 {
		margin-top: 0px !important;
	}

	.mt-5 {
		margin-top: 5px;
	}

	.mt-10 {
		margin-top: 10px;
	}

	.mt-15 {
		margin-top: 15px;
	}

	.mt-20 {
		margin-top: 20px;
	}

	.mr-5 {
		margin-right: 5px;
	}

	.mr-20 {
		margin-right: 20px;
	}

	.ml-5 {
		margin-left: 5px;
	}

	.ml-10 {
		margin-left: 10px;
	}

	.ml-15 {
		margin-left: 15px;
	}

	.mt-70 {
		margin-top: 70px !important;
	}

	.mt-60 {
		margin-top: 60px !important;

		@media screen and (max-width: 768px) {
			margin-top: 40px !important;
		}
	}

	.mb-20 {
		margin-bottom: 20px;
	}

	.mb-15 {
		margin-bottom: 15px;
	}

	.ml-10 {
		margin-left: 10px;
	}

	.pb-15 {
		padding-bottom: 15px;
	}

	.p-35 {
		padding: 35px;
	}

	.pl-15 {
		padding-left: 15px;
	}

	.pl-10 {
		padding-left: 10px;
	}

	.pl-0 {
		padding-left: 0;
	}

	.ml-10 {
		margin-left: 10px;
	}

	.line-height1em {
		line-height: 1em;
	}

	.w-100 {
		width: 100%;
	}

	.h-100 {
		height: 100%;
	}

	.bg-1 {
		background-color: #fff;
	}

	.red1 {
		color: #de3500;
	}

	.el-input__inner {
		height: 100%;
		border-radius: 4px;
	}

	.searchColor {
		position: absolute;
		right: 20px;
		height: 40px;
		padding-top: 20px;
		z-index: 1;

		@media screen and (max-width: 610px) {
			right: 5px;
			padding-top: 35px;
		}

		> {
			::v-deep .el-input {
				height: 100%;

				.el-input__inner {
					height: 100%;
					border-radius: 4px;
					border: 0;
					background-color: #f3f3f4;
					font-size: 16px;
					font-family: Calibri;
					font-weight: 400;
					color: #a6a6a6;

					@media screen and (max-width: 610px) {
						height: 30px;
						line-height: 30px;
						font-size: 12px;
					}
				}

				.el-input__prefix {
					font-size: 18px;
					left: 12px;
				}
			}
		}
	}

	.custom-shadow {
		position: relative;
		background: #fff;

		&::before,
		&::after {
			content: "";
			position: absolute;
			z-index: -1;
			bottom: 12px;
			left: 5px;
			width: 50%;
			height: 20%;
			box-shadow: 0 14px 7px #d9dbdd;
			transform: rotate(-3deg);
		}

		&::after {
			right: 5px;
			left: auto;
			transform: rotate(3deg);
		}
	}

	.custom-shadow2 {
		position: relative;
		background: #fff;

		&::before,
		&::after {
			content: "";
			position: absolute;
			z-index: -1;
			bottom: 14px;
			left: 0px;
			width: 50%;
			height: 20%;
			box-shadow: 0 14px 7px #d9dbdd;
			transform: rotate(-3deg);
		}

		&::after {
			right: 0;
			left: auto;
			transform: rotate(3deg);
		}
	}

	.el-drawer {
		background-color: #f3f4f5;

		.el-drawer__header {
			margin-bottom: 0;
		}

		.el-drawer__body {
			padding: 0;
			text-align: center;
		}

		.canvas-zone.drawer {
			position: relative;
			padding: 10px;
			display: grid;
			grid-template-columns: 1fr 1fr;

			@media screen and (max-width: 768px) {
				grid-template-columns: 1fr;
			}

			.canvas {
				img {
					border: 1px dashed #d9dbdd;
					border-radius: 10px;
					width: 100%;
				}
			}

			.info {
				padding: 10px;
				max-height: calc(100vh - 40px);
				overflow-y: auto;
				box-sizing: border-box;

				>div {
					@media screen and (max-width: 768px) {
						display: grid;
						grid-auto-columns: repeat(2, 1fr);
						gap: 5px;

						>div {
							margin: 0;
						}
					}

					.title {
						font-size: 18px;
						font-weight: bold;
						color: #333333;
						text-align: center;
						margin-bottom: 7px;
					}

					.box {
						display: grid;
						grid-template-columns: 1fr;
						// grid-auto-rows: 80px;
						border: 1px solid #d9dbdd;
						border-radius: 4px;
						box-sizing: border-box;

						overflow-y: auto;
						max-height: 170px;

						.item {
							display: grid;
							grid-template-columns: 39px 1fr 1fr 1fr;
							padding: 0 15px;
							grid-auto-rows: auto;
							box-sizing: border-box;
							height: 80px;
							column-gap: 10px;
							transition: all 0.3s;

							>div:nth-child(1) {
								grid-column: 1/2;
								grid-row: 1/3;

								>div {
									border: 1px solid #d9dbdd;
								}
							}

							>div:nth-child(2) {
								grid-column: 2/5;
								grid-row: 1/2;
								display: flex;
								align-items: center;
								font-size: 16px;
								font-weight: 400;
								padding-top: 5px;
								color: #666666;
							}

							>div:nth-child(3) {
								grid-column: 2/5;
								grid-row: 2/3;
								display: flex;
								align-items: center;
								justify-content: space-between;
								padding-bottom: 10px;

								>div:nth-child(1) {
									>div:nth-child(1) {
										font-size: 16px;
									}
								}

								.el-input {
									height: 100%;
									max-width: 96px;
									width: 100%;
									height: 26px;

									@media screen and (max-width: 768px) {
										font-size: 12px;
										font-family: Arial;
									}

									.el-input__inner {
										text-align: left;
									}
								}
							}
						}

						.item:hover {
							background-color: #f3f4f5;
						}

						.item:not(:last-child) {
							border-bottom: 1px dashed #e6e6e6;
						}

						.item.sp {
							>div:nth-child(2) {
								grid-row: 1/3;
								padding-top: 0;
							}

							>div:nth-child(3) {
								grid-row: 1/3;
								grid-column: 4/5;
								padding-bottom: 0;

								@media screen and (max-width: 768px) {
									justify-content: flex-end;
								}
							}
						}
					}
				}

				.price-info {
					display: grid;
					grid-template-columns: repeat(3, 1fr);
					grid-auto-rows: auto;
					border-bottom: 1px solid #d9d9d9;
					padding: 14px 10px;
					row-gap: 15px;
					margin-bottom: 20px;

					>div {
						font-size: 16px;
						font-weight: 400;
						color: #666666;

						@media screen and (max-width: 768px) {
							font-size: 12px;
							font-family: Arial;
							font-weight: bold;
						}
					}

					>div:nth-child(2n-1) {
						grid-column: 1/2;
					}

					>div:nth-child(2n) {
						grid-column: 2/4;
					}

					.title {
						color: #222222;
						margin-bottom: 0;
						text-align: left;
					}
				}

				.subtotal {
					display: grid;
					grid-template-columns: repeat(3, 1fr);
					padding: 0 10px 10px 10px;

					@media screen and (max-width: 768px) {
						padding: 0 50px;
					}

					>div {
						display: flex;
						align-items: flex-end;
					}

					>div:nth-child(1) {
						font-size: 16px;
						font-weight: 400;
						color: #222222;
						padding-bottom: 5px;

						@media screen and (max-width: 768px) {
							font-size: 12px;
							font-family: Arial;
							font-weight: bold;
						}
					}

					>div:nth-child(2) {
						font-size: 30px;
						color: #de3500;
						font-weight: bold;
					}

					>div:nth-child(3) {
						.el-select {
							width: 70px;
							height: 36px;

							>div {
								width: 100%;
								height: 100%;

								.el-input {
									width: 100%;
									height: 100%;

									.el-input__inner {
										font-size: 15px;
										font-family: Roboto;
										font-weight: 400;
										color: #333333;
										padding-right: 25px;

										@media screen and (max-width: 768px) {
											font-size: 12px;
											font-family: Arial;
										}
									}
								}
							}
						}
					}
				}

				.submit {
					button {
						width: 100%;
						height: 40px;
						font-size: 18px;
						font-weight: 400;
						color: #ffffff;
						font-family: inherit;

						@media screen and (max-width: 768px) {
							font-size: 12px;
							font-family: Arial;
							font-weight: bold;
						}
					}

					>div:nth-child(1) {
						button {
							background: linear-gradient(90deg, #0066cc 0%, #2fb6f5 100%);
							border-radius: 4px;
						}
					}

					>div:nth-child(2) {
						button {
							background: linear-gradient(90deg, #ff412b 0%, #ff7743 100%);
							border-radius: 4px;

							.icon {
								display: inline-block;
								width: 18px;
								height: 18px;
								border-radius: 50%;
								background-color: #fff;
								font-size: 16px;
								font-family: Calibri;
								font-weight: bold;
								color: #333333;
								line-height: 19px;
								margin-left: 10px;
							}
						}
					}
				}

				.help {
					background: #ffffff;
					border-radius: 4px;
					padding: 14px 15px 21px 22px;
					margin-bottom: 0;
					display: flex;
					text-decoration: none;

					>div:first-child {
						display: flex;
						justify-content: center;
						align-items: center;

						i {
							font-size: 48px;

							@media screen and (max-width: 768px) {
								font-size: 24px;
							}
						}
					}

					.help-content {
						font-size: 16px;
						font-weight: 400;
						color: #333333;
						padding-left: 20px;
						display: flex;
						text-align: left;
						align-items: center;

						@media screen and (max-width: 768px) {
							font-size: 12px;
							font-family: Arial;
							font-weight: bold;
						}
					}
				}
			}
		}
	}

	.canvas-self {
		position: absolute;
		left: -100%;
		top: -100%;
		z-index: 0;
		opacity: 0;
	}

	.title {
		h1 {
			font-size: 48px;
			color: #333333;
			text-align: center;
			margin: 20px 0;

			@media screen and (max-width: 768px) {
				font-size: 24px;
				font-family: Arial;
				font-weight: 400;
				color: #333333;
			}
		}
	}

	.faqBorder {
		box-shadow: 0px 3px 10px 0px rgb(0 0 0 / 20%);
		padding: 10px;

		@media screen and (max-width: 768px) {
			max-height: 300px;
			overflow-y: auto;
		}

		.QAPart {
			.Q {
				font-size: 15px;
				font-weight: bold;
				color: #333333;
			}

			.A {
				font-size: 13px;
				font-weight: 400;
				color: #333333;
				margin-bottom: 0;
			}
		}

		.QAPart:not(:last-child) {
			margin-bottom: 30px;
		}
	}

	::v-deep .form {
		display: grid;
		grid-template-columns: repeat(48, 1fr);

		.el-tabs__header {
			margin: 0;
		}

		.el-tabs__content {
			overflow: inherit;
		}

		.el-tabs__item {
			border-radius: 4px 4px 0 0;
			padding: 0;
			color: #333333;
			width: 200px;
			height: 50px;
			background-color: #d9dbdd;
			font-size: 24px;
			font-weight: bold;
			margin-right: 5px;
			cursor: pointer;
			text-align: center;
			line-height: 50px;

			@media screen and (max-width: 768px) {
				font-size: 14px;
				font-family: Arial;
				font-weight: 400;
				color: #ffffff;
				width: 146px;
				height: 35px;
				background: #d9dbdd;
				line-height: 40px;
				border-radius: 5px 5px 0px 0px;
			}
		}

		.el-tabs__item.is-active {
			background-color: #0066cc;
			color: white;
		}

		.el-tabs__active-bar {
			display: none;
		}

		.el-tabs {
			grid-column: 2/49;
		}

		.table-responsive1 {
			box-shadow: 0px 3px 10px 0px rgb(0 0 0 / 20%);
			overflow: auto;
			font-size: 16px;
			border: 10px solid #fff;

			.gridBox12 {
				th:nth-child(1) {
					@media screen and (max-width: 768px) {
						width: auto;
						display: block;
						position: sticky;
						left: 0;
						max-width: 70px;
						border-right: 0;
					}
				}
			}

			thead,
			tbody,
			tfoot,
			tr,
			td,
			th {
				border-color: inherit;
				border-style: solid;
				border-width: 0;
				white-space: nowrap;
			}

			th {
				background-color: #f6f6f6;
				font-size: 18px;
				font-weight: 400;

				@media screen and (max-width: 768px) {
					font-size: 12px;
					font-family: Arial;
				}
			}

			td {
				@media screen and (max-width: 768px) {
					font-size: 12px;
					font-family: Arial;
				}
			}

			th:hover,
			td:hover {
				background-color: #ebebeb;
				color: #0066cc;
			}

			table {
				caption-side: bottom;
				border-collapse: collapse;
			}

			.table {
				--bs-table-bg: transparent;
				--bs-table-accent-bg: transparent;
				--bs-table-striped-color: #212529;
				--bs-table-striped-bg: rgba(0, 0, 0, 0.05);
				--bs-table-active-color: #212529;
				--bs-table-active-bg: rgba(0, 0, 0, 0.1);
				--bs-table-hover-color: #212529;
				--bs-table-hover-bg: rgba(0, 0, 0, 0.075);
				width: 100%;
				color: #212529;
				vertical-align: top;
				border-color: #dee2e6;
			}

			.table> :not(caption)>*>* {
				padding: 0.5rem 0.5rem;
				border-bottom-width: 1px;
				box-shadow: inset 0 0 0 9999px var(--bs-table-accent-bg);
				text-align: center;
			}

			.table>tbody {
				vertical-align: inherit;
			}

			.table>thead {
				vertical-align: bottom;
			}

			.table-bordered> :not(caption)>* {
				border-width: 1px 0;
			}

			.table-bordered> :not(caption)>*>* {
				border-width: 0 1px;
				vertical-align: middle;
			}

			.table-hover>tbody>tr:hover>* {
				--bs-table-accent-bg: var(--bs-table-hover-bg);
				color: var(--bs-table-hover-color);
			}
		}
	}

	.content {
		display: grid;
		grid-template-columns: repeat(48, 1fr);
		margin-top: 50px;
		margin-bottom: 50px;

		@media screen and (max-width: 768px) {
			margin-top: 34px;
			margin-bottom: 20px;
		}

		.steps {
			grid-column: 2/28;

			@media screen and (max-width: 1650px) and (min-width: 1280px) {
				grid-column: 2/27;
			}

			@media screen and (max-width: 1279px) {
				grid-column: 2/48;
			}

			@media screen and (max-width: 768px) {
				>div>div:not(:last-child) {
					position: relative;

					&::after {
						position: absolute;
						content: "";
						right: 0;
						left: -20px;
						bottom: -20px;
						height: 5px;
						background-color: #f3f4f5;
					}
				}
			}

			.step-title {
				font-size: 27px;
				font-weight: bold;
				color: #202428;
				margin-bottom: 30px;

				@media screen and (max-width: 768px) {
					font-size: 16px;
					font-family: Arial;
					font-weight: bold;
					color: #202428;
					margin-bottom: 25px;
				}
			}

			.selectLanyardType {
				.box {
					display: grid;
					grid-template-columns: repeat(4, 1fr);
					justify-content: space-between;
					gap: 22px;

					@media screen and (max-width: 1919px) {
						grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
					}

					@media screen and (max-width: 1279px) {
						grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
						gap: 10px;
					}

					@media screen and (max-width: 768px) {
						grid-template-columns: repeat(2, minmax(115px, 1fr));
						justify-content: space-between;
						gap: 10px;
					}

					label {
						font-size: 18px;
						font-weight: 400;
						color: #333333;

						@media screen and (max-width: 768px) {
							font-size: 12px;
							font-family: Arial;
							color: #333333;
						}

						>div {}
					}

					.el-image {
						max-height: 330px;
						border-radius: 6px;
					}
				}
			}

			.selectLanyardStyle {
				width: 100%;

				.box {
					display: grid;
					grid-template-columns: repeat(3, 1fr);
					justify-content: space-between;
					column-gap: 18px;
					row-gap: 20px;

					@media screen and (max-width: 1919px) and (min-width: 1280px) {
						grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
					}

					@media screen and (max-width: 1279px) {
						grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
					}

					@media screen and (max-width: 768px) {
						grid-template-columns: repeat(2, 1fr);
						column-gap: 5px;
						row-gap: 5px;
					}

					.acc {
						display: block;

						.bg {
							width: 100%;
							background-color: #f5f5f5;
							display: flex;
							align-items: center;
							justify-content: center;
							border-radius: 10px;
							padding-top: 30px;
						}

						label {
							font-size: 18px;
							font-weight: 400;
							color: #333333;

							@media screen and (max-width: 768px) {
								padding: 4px;
								font-size: 12px;
								font-family: Arial;
								color: #333333;
							}
						}
					}

					.el-image {
						height: 215px;

						@media screen and (max-width: 768px) {
							height: 100%;
						}
					}

					.padding {
						padding: 10px;
					}
				}
			}

			.selectLanyardSize {
				display: grid;
				grid-template-columns: repeat(2, 1fr);
				column-gap: 40px;
				row-gap: 40px;
				justify-content: space-between;

				@media screen and (max-width: 1919px) {
					grid-template-columns: 1fr 1fr;
				}

				@media screen and (max-width: 1651px) {
					grid-template-columns: 1fr;
				}

				@media screen and (max-width: 1279px) {
					grid-template-columns: repeat(auto-fill, minmax(500px, 1fr));
				}

				@media screen and (max-width: 1024px) {
					grid-template-columns: repeat(2, 1fr);
					column-gap: 10px;
				}

				@media screen and (max-width: 850px) {
					grid-template-columns: 1fr;
					row-gap: 10px;
				}

				>div {
					margin-top: -10px;

					@media screen and (max-width: 768px) {
						margin-top: 0;
					}
				}

				>div:nth-child(2) {
					@media screen and (max-width: 768px) {
						margin-top: 15px;
					}
				}

				.el-image {
					height: 200px;

					@media screen and (max-width: 768px) {
						width: 116px;
						height: 150px;
					}
				}

				.myH {
					.el-form-item__label {
						height: 27px;
					}
				}

				.sp-box {}

				.info {
					display: grid;
					grid-template-columns: 140px 1fr;
					column-gap: 20px;
					font-size: 18px;
					font-family: Calibri;
					font-weight: 400;
					color: #333333;
					position: relative;
					padding: 10px 0 0 10px;

					@media screen and (max-width: 768px) {
						grid-template-columns: auto 1fr;
					}

					.red1 {
						font-size: 18px;
						font-family: Calibri;

						@media screen and (max-width: 768px) {
							font-size: 12px;
							font-family: Arial;
							display: flex;
							align-items: flex-end;
							line-height: 20px;
						}
					}

					.icon {
						>span:nth-child(1) {
							font-size: 18px;
							font-family: Calibri;
							font-weight: 400;
							color: #333333;

							@media screen and (max-width: 768px) {
								font-size: 12px;
								font-family: Arial;
							}
						}

						>span:nth-child(2) {
							color: #999999;
							border: 1px solid #999999;
							border-radius: 50%;
							font-family: arial;
							width: 12px;
							height: 12px;
							display: inline-block;
							font-size: 12px;
							text-align: center;
							margin-left: 5px;
						}
					}
				}

				.left,
				.right {
					display: grid;
					grid-template-columns: 140px 1fr;
					column-gap: 10px;

					>div:nth-child(2) {
						padding: 10px;

						@media screen and (max-width: 768px) {
							padding: 5px;
						}
					}

					@media screen and (max-width: 768px) {
						grid-template-columns: auto 1fr;
						text-align: center;
						height: 188px;

						// > div:nth-child(1) {
						//   grid-row: 2/3;
						// }
						// > div:nth-child(2) {
						//   grid-row: 1/2;
						//   margin-bottom: 15px;
						// }
					}

					.el-select {
						width: 100%;

						.el-input {
							height: 40px;

							@media screen and (max-width: 1279px) {
								height: 30px;
							}

							.el-input__inner {
								@media screen and (max-width: 768px) {
									font-size: 12px;
								}
							}
						}
					}

					.title {
						font-size: 18px;
						font-weight: 400;
						color: #333333;

						@media screen and (max-width: 768px) {
							font-size: 12px;
							font-family: Arial;
							text-align: left;
						}
					}

					::v-deep .custom-radio {
						display: grid;
						grid-template-columns: 1fr;
						grid-auto-rows: 40px;
						row-gap: 6px;
						height: 100%;

						.el-radio__input {
							display: flex;
						}

						label {
							border: 1px solid transparent;
							border-radius: 10px;
							margin: 0;
							padding: 0 20px;
							background-color: #f7f9fa;
							display: flex;
							align-items: center;
						}

						.el-radio__label {
							font-size: 18px;

							@media screen and (max-width: 768px) {
								font-size: 12px;
								font-family: Arial;
							}
						}

						.el-radio__inner {
							border-width: 2px;
						}

						.el-radio__inner::after {
							background-color: #d9d9d9;
							transform: translate(-50%, -50%) scale(1);
							width: 5px;
							height: 5px;
						}

						.el-radio__input.is-checked .el-radio__inner::after {
							background-color: #0066cc;
						}

						.el-radio__input.is-checked .el-radio__inner {
							background: transparent;
							border-color: #0066cc;
						}

						.el-radio__input.is-checked+.el-radio__label {
							color: inherit;
						}
					}

					.ul {
						font-size: 14px;
						font-weight: 400;
						color: #999999;
						line-height: 1.5em;
						margin-top: 10px;

						ul {
							display: inline-block;
							padding-left: 17px;
							margin: 0;

							li {
								line-height: 1.5em;
								list-style-type: disc !important;
							}
						}
					}
				}

				.padding {
					padding: 14px 10px;

					@media screen and (max-width: 768px) {
						padding: 5px;
					}
				}
			}

			::v-deep .chooseYourLanyardColors {
				border: 1px solid #d9dbdd;

				@media screen and (max-width: 768px) {
					padding: 0;
					border: none;
					margin-top: -15px;
				}

				.tabs {
					@media screen and (max-width: 610px) {
						.el-tab-pane:nth-child(2) {
							>ul {
								margin-top: 20px;
							}
						}
					}

					.box {
						margin: 0;
						display: grid;
						grid-template-columns: repeat(5, 1fr);
						// grid-template-rows: repeat(auto-fill, 82px);
						column-gap: 18px;
						row-gap: 10px;
						max-height: 430px;
						// overflow-y: scroll;
						overflow-x: hidden;
						padding: 5px 20px 20px 20px;
						justify-content: space-between;

						@media screen and (max-width: 1919px) {
							grid-template-columns: repeat(auto-fill, minmax(175px, 180px));
							column-gap: normal;
						}

						@media screen and (max-width: 1500px) {
							grid-template-columns: repeat(4, 1fr);
							column-gap: 10px;
							padding: 5px 10px 20px 10px;
						}

						@media screen and (max-width: 1279px) and (min-width: 611px) {
							grid-template-columns: repeat(auto-fill, minmax(150px, 180px));
							justify-content: space-between;
							column-gap: 10px;
							padding: 5px 15px 20px 15px;
						}

						@media screen and (max-width: 610px) {
							grid-template-columns: repeat(3, 1fr);
							justify-content: space-between;
							column-gap: 10px;
							padding: 0;
						}

						@media screen and (max-width: 510px) {
							grid-template-columns: repeat(2, 1fr);
							justify-content: space-between;
							column-gap: 10px;
							padding: 0;

						}

						.item {
							margin: 0;
							padding: 0;
							border: 1px solid #d9dbdd;
							border-radius: 4px;
							display: grid;
							grid-template-columns: repeat(3, 1fr);
							grid-template-rows: repeat(2, 40px);
							box-sizing: border-box;

							.p1 {
								grid-column: 1/2;
								grid-row: 1/3;
								padding: 11px 10.7px;

								>div {
									width: 100%;
									height: 100%;
									border: 1px solid #d9dbdd;
									background-size: cover;
								}
							}

							.p2 {
								grid-column: 2/4;
								grid-row: 1/2;
								padding: 5px 15px 5px 5px;
								overflow: hidden;
								text-overflow: ellipsis;
								white-space: nowrap;
								font-size: 15px;
								font-weight: 400;
								color: #666666;
								line-height: 2em;

								@media screen and (max-width: 768px) {
									font-size: 12px;
									font-family: Arial;
									line-height: 2.5em;
								}
							}

							.p3 {
								grid-column: 2/4;
								grid-row: 2/3;
								display: flex;
								padding: 0 15px 0 5px;

								@media screen and (max-width: 768px) {
									padding: 0 5px;
								}

								.myInput {
									@media screen and (max-width: 768px) {
										height: 26px;
										line-height: 26px;
									}
								}
							}

							&:hover {
								.p2 {
									overflow: visible;
								}
							}
						}

						.item.add-custom-color {
							grid-column: 1/2;
							grid-row: 1/2;
							cursor: pointer;

							>div:nth-child(1) {
								grid-column: 1/4;
								grid-row: 1/2;
								display: flex;
								justify-content: center;
								align-items: flex-end;
							}

							>div:nth-child(2) {
								grid-column: 1/4;
								grid-row: 2/3;
								display: flex;
								justify-content: center;
								align-items: center;
								font-size: 15px;
								font-family: Calibri;
								font-weight: 400;
								color: #666666;
							}
						}

						.item.add-custom-color.change {
							grid-column: 1/2;
							grid-row: 1/3;
							display: block;
							padding: 5px;

							.el-form-item {
								margin-bottom: 14px;

								.el-form-item__label {
									font-size: 15px;
									font-family: Calibri;
									font-weight: 400;
									color: #666666;
									margin-bottom: 0;
								}

								.el-input {
									height: 26px;
								}
							}

							.el-form-item.bottom {
								margin-bottom: 0;

								.el-form-item__content {
									display: grid;
									grid-template-columns: 1fr 1fr;
									gap: 5px;

									.el-button+.el-button {
										margin: 0;
									}

									button {
										font-size: 15px;
										font-family: Calibri;
										font-weight: 400;
										border: 0;
									}

									button:nth-child(1) {
										background: #f3f3f4;
										color: #333333;
									}

									button:nth-child(2) {
										background: #0066cc;
										color: #ffffff;
									}
								}
							}
						}
					}

					.el-tabs__header {
						margin-bottom: 20px;
						padding-top: 20px;
						display: flex;
						justify-content: center;

						@media screen and (max-width: 768px) {
							padding-top: 0;
						}

						.el-tabs__nav-scroll {
							.el-tabs__nav {
								position: relative;
							}

							.el-tabs__nav::before {
								position: absolute;
								content: "";
								left: 0;
								bottom: 0;
								width: 100%;
								height: 2px;
								background-color: #e4e7ed;
								z-index: var(--el-index-normal);
							}

							.el-tabs__active-bar {
								background-color: #0066cc;
							}

							.el-tabs__item {
								font-size: 18px;
								font-weight: bold;
								color: #333333;
								padding: 0;
								width: 120px;
								text-align: center;

								@media screen and (max-width: 768px) {
									font-size: 14px;
									height: 30px;
									line-height: 30px;
									font-family: Arial;
								}
							}

							.el-tabs__item.is-active {
								color: #0066cc;
							}
						}
					}
				}
			}

			::v-deep .chooseYourLanyardAttachment {
				width: 100%;

				.item {
					background-color: #f4f6f9;
					border-radius: 4px;
					padding: 0 0 10px 0;
					border: 1px solid transparent;
					box-sizing: border-box;

					@media screen and (max-width: 1921px) and (min-width: 1919px) {}

					@media screen and (max-width: 500px) {
						padding: 5px 0;
					}

					position: relative;

					>div {
						>.el-image img {
							// height: 150px;
							width: 100%;
							height: 100%;
							object-fit: contain;
						}
					}
				}

				.el-tabs__nav {
					display: grid;
					grid-template-columns: repeat(2, 1fr);

					// grid-auto-rows: 192px;
					column-gap: 11px;
					width: 100%;
					border: none;

					@media screen and (max-width: 768px) {
						grid-auto-rows: auto;
					}

					.el-tabs__item {
						font-size: 18px;
						font-weight: 400;
						color: #333333;
						text-align: center;
						height: 100%;
						background: #f5f6f7;
						border: 1px solid #f0f1f2;
						border-radius: 10px 10px 0px 0px;
						display: flex;
						flex-direction: column;
						justify-content: center;
						padding: 20px 0;
						line-height: 1em;

						// padding-top: 5px;
						@media screen and (max-width: 1280px) and (min-width: 769px) {
							padding: 20px 0;
						}

						@media screen and (max-width: 768px) {
							font-size: 12px;
							font-family: Arial;
							line-height: 2em;
							padding: 10px 0;
						}

						span {
							padding-top: 5px;
						}

						.el-image {
							max-width: 321px;
							max-height: 111px;
							margin-bottom: 20px;

							// height: 106px;
							@media screen and (max-width: 1280px) and (min-width: 769px) {
								max-height: 106px;
								margin-bottom: 5px;
							}

							@media screen and (max-width: 768px) {
								max-height: 50px;
								margin-bottom: 5px;
							}
						}
					}

					.el-tabs__item:hover {
						border-color: #0066cc;
					}

					.el-tabs__item:nth-child(1) {
						margin-right: -1px;
					}

					.el-tabs__item:nth-child(2) {
						margin-left: 2px;
					}
				}

				.el-tabs__content {
					border: 1px solid #ebebeb;
					border-top: none;
				}

				.el-tabs--card>.el-tabs__header .el-tabs__item.is-active {
					background: #ffffff;
					border-color: #0066cc;
					border-bottom: transparent;

					&::before {
						position: absolute;
						content: url("~/assets/lanyardQuote/svg/bgh_Selected.svg");
						left: 0;
						top: 0;
						width: 50px;
						height: 50px;
					}

					&::after {
						position: absolute;
						content: url("~/assets/lanyardQuote/svg/Selected.svg");
						left: 8px;
						top: 5px;
						width: 19px;
						height: 20px;
						display: flex;
						align-items: center;
					}
				}

				.custom-radio {
					.label {
						font-size: 16px;
						font-weight: 400;
						color: #333333;

						@media screen and (max-width: 768px) {
							font-size: 12px;
							font-family: Arial;
						}
					}
				}

				.price {
					line-height: 1em;
					font-size: 16px;
					font-weight: 400;

					@media screen and (max-width: 768px) {
						font-size: 12px;
						font-family: Arial;
						overflow: hidden;
						text-overflow: ellipsis;
						white-space: nowrap;
						max-width: 78.34px;
						margin-top: 5px;
					}
				}

				.left {
					.box {
						display: grid;
						grid-template-columns: repeat(5, 1fr);

						// grid-template-rows: repeat(auto-fill,180px);
						justify-content: space-between;
						gap: 17px;
						padding: 17px 20px;

						@media screen and (max-width: 1919px) {
							grid-template-columns: repeat(auto-fill, 175px);
							column-gap: 15px;
						}

						@media screen and (max-width: 1679px) {
							grid-template-columns: repeat(4, 1fr);
							column-gap: 10px;
							padding: 15px;
						}

						@media screen and (max-width: 1280px) and (min-width: 769px) {
							grid-template-columns: repeat(auto-fill, 175px);
							column-gap: normal;
						}

						@media screen and (max-width: 768px) {
							grid-template-columns: repeat(4, 1fr);
							justify-content: space-between;
							gap: 8px;
							padding: 10px;
						}

						@media screen and (max-width: 500px) {
							grid-template-columns: repeat(3, 1fr);
							justify-content: space-between;
							gap: 8px;
							padding: 5px;
						}
					}
				}

				.right {
					.custom-checkbox {
						>* {
							flex: 1;
							display: block;
							text-align: center;
						}

						padding: 5px 0 5px 0;
						border-bottom: 1px solid #f5f5f5;
						display: flex;
						justify-content: flex-end;

						@media screen and (max-width: 768px) {
							padding: 0;
							padding-left: 14px;
							justify-content: flex-start;
							margin: 10px 0;
							padding-bottom: 10px;
						}

						label {
							@media screen and (max-width: 768px) {
								padding: 0;
								right: 0;
							}
						}

						// .el-checkbox__input.is-checked .el-checkbox__inner {
						//   background-color: #0d6efd;
						//   border-color: #0d6efd;
						// }
						// .el-checkbox__input.is-checked + .el-checkbox__label {
						//   color: #0d6efd;
						// }
						.el-checkbox__label {
							font-size: 16px;
							font-weight: 400;
							color: #333333;

							@media screen and (max-width: 768px) {
								font-size: 12px;
							}
						}
					}

					.box {
						display: grid;
						grid-template-columns: repeat(5, 1fr);

						// grid-template-rows: repeat(auto-fill,180px);
						justify-content: space-between;
						gap: 17px;
						padding: 17px 20px;

						@media screen and (max-width: 1919px) {
							grid-template-columns: repeat(auto-fill, 175px);
							column-gap: 15px;
						}

						@media screen and (max-width: 1679px) {
							grid-template-columns: repeat(4, 1fr);
							column-gap: 10px;
							padding: 15px;
						}

						@media screen and (max-width: 1280px) and (min-width: 769px) {
							grid-template-columns: repeat(auto-fill, 175px);
							column-gap: normal;
						}

						@media screen and (max-width: 768px) {
							grid-template-columns: repeat(4, 1fr);
							justify-content: space-between;
							gap: 8px;
							padding: 10px;
						}

						@media screen and (max-width: 500px) {
							grid-template-columns: repeat(3, 1fr);
							justify-content: space-between;
							gap: 8px;
							padding: 5px;
						}

						.item {
							.el-image {
								@media screen and (max-width: 1280px) and (min-width: 769px) {
									height: 165px;
								}
							}
						}
					}

					.double-box .left {
						&::after {
							position: absolute;
							content: "";
							width: 1px;
							height: 100%;
							right: 0;
							top: 0;
							background-color: #f5f5f5;
						}
					}

					.double-box {
						display: grid;
						grid-template-columns: repeat(2, 1fr);

						.left,
						.right {
							position: relative;

							.title {
								font-size: 15px;
								font-family: Arial;
								font-weight: bold;
								color: #333333;
								margin-left: 20px;
								margin-top: 10px;
							}

							.box2 {
								display: grid;
								grid-template-columns: repeat(3, 1fr);

								// grid-template-rows: repeat(auto-fill,180px);
								justify-content: space-between;
								-moz-column-gap: 5px;
								column-gap: 10px;
								row-gap: 10px;
								max-height: 450px;
								overflow-y: scroll;
								padding: 12px 15px;

								@media screen and (max-width: 1919px) {
									grid-template-columns: repeat(2, 1fr);
								}

								@media screen and (max-width: 1280px) and (min-width: 1246px) {
									grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
								}

								@media screen and (max-width: 1245px) and (min-width: 972px) {
									grid-template-columns: repeat(3, 1fr);
								}

								@media screen and (max-width: 971px) {
									grid-template-columns: repeat(2, 1fr);

									.title {
										font-size: 12px;
										font-family: Arial;
										margin-left: 10px;
									}
								}

								@media screen and (max-width: 500px) {
									.title {
										font-size: 12px;
										font-family: Arial;
										margin-left: 10px;
									}

									grid-template-columns: repeat(1, 1fr);
								}

								.item {
									height: auto;
								}
							}
						}
					}
				}
			}

			.chooseLanyardStitchStyle {
				width: 100%;

				.el-image {
					width: 100%;
					height: 186px;

					@media screen and (max-width: 768px) {
						width: 100%;
						height: 90px;
					}
				}

				.box {
					display: grid;
					grid-template-columns: repeat(5, 1fr);
					justify-content: space-between;
					gap: 22px;

					@media screen and (max-width: 1730px) {
						grid-template-columns: repeat(3, 1fr);
					}

					@media screen and (max-width: 1280px) and (min-width: 769px) {
						gap: 13px;
					}

					@media screen and (max-width: 768px) {
						grid-template-columns: repeat(3, 1fr);
						column-gap: 5px;
						row-gap: 10px;
					}

					.item {
						border: 1px solid #e6e6e6;
						border-radius: 6px;
						padding-bottom: 17px;
						display: block;

						@media screen and (max-width: 768px) {
							border: none;
							padding-bottom: 0;
						}

						>div:nth-child(1) {
							@media screen and (max-width: 768px) {
								border: 1px solid #e6e6e6;
								border-radius: 6px;
								margin-bottom: 20px;
							}
						}

						.custom-radio {
							.label {
								font-size: 16px;
								font-weight: 400;
								color: #333333;

								@media screen and (max-width: 768px) {
									font-size: 12px;
									font-family: Arial;
									line-height: 1.5em;
								}
							}
						}
					}
				}

				.price {
					font-size: 16px;
					font-weight: 400;

					@media screen and (max-width: 768px) {
						font-size: 12px;
						font-family: Arial;
						line-height: 1.5em;
					}
				}
			}

			.additionalOptions,
			.selectPackaging {
				width: 100%;

				.box {
					display: grid;
					grid-template-columns: repeat(3, 1fr);
					justify-content: space-between;
					gap: 20px;

					@media screen and (max-width: 1650px) {
						grid-template-columns: repeat(2, 1fr);
					}

					@media screen and (max-width: 1280px) and (min-width: 769px) {
						grid-template-columns: repeat(3, 1fr);
					}

					@media screen and (max-width: 768px) {
						grid-template-columns: repeat(2, 1fr);
						gap: 5px;
					}

					label {
						font-size: 16px;
						font-weight: 400;
						color: #333333;

						@media screen and (max-width: 768px) {
							font-size: 12px;
							font-family: Arial;
							line-height: 1.5em;
						}

						.label {
							@media screen and (max-width: 768px) {
								font-size: 12px;
								font-family: Arial;
							}
						}
					}

					.item {
						border: 1px solid #e6e6e6;
						border-radius: 6px;
						padding: 18px;

						@media screen and (max-width: 768px) {
							padding: 5px;
							border: none;

							>div:nth-child(1) {
								height: 112px;
								border: 1px solid #e6e6e6;
								border-radius: 6px;
								margin-bottom: 20px;
							}
						}

						.el-image {
							height: 163px;

							@media screen and (max-width: 768px) {
								height: 90px;
							}
						}
					}

					.item:nth-child(1) {
						.el-image {
							height: 163px;

							@media screen and (max-width: 768px) {
								height: 50px;
							}
						}
					}

					@media screen and (max-width: 768px) {
						.item {
							grid-column: auto !important;
						}
					}
				}

				.price {
					line-height: 1em;
					margin-top: 5px;

					@media screen and (max-width: 768px) {
						font-size: 12px;
						font-family: Arial;
					}
				}
			}

			.selectYourCard {
				width: 100%;

				.el-checkbox.el-checkbox--large {
					height: auto;
				}

				.el-image {
					@media screen and (max-width: 768px) {
						height: 170px;
					}
				}

				.box {
					display: grid;
					grid-template-columns: repeat(2, 1fr);
					grid-auto-rows: auto;
					column-gap: 24px;

					@media screen and (max-width: 1650px) and (min-width: 1280px) {
						grid-template-columns: 1fr;
						row-gap: 20px;
					}

					@media screen and (max-width: 768px) {
						column-gap: 5px;
					}

					.item {
						border: 1px solid #e6e6e6;
						border-radius: 6px;
						display: grid;
						grid-template-columns: repeat(2, 1fr);
						justify-content: space-between;
						padding: 15px;

						@media screen and (max-width: 768px) {
							grid-template-columns: 1fr;
							column-gap: 5px;
							padding: 5px;

							.checkboxbox {
								.el-checkbox__inner {
									width: 13px;
									height: 13px;

									&::after {
										width: 5px;
										height: 5px;
										left: 2px;
										top: 2px;
									}
								}

								.el-checkbox__label {
									font-size: 12px;
									font-family: Arial;
								}
							}
						}

						.custom-checkbox {
							padding-left: 23px;
							justify-content: flex-start;

							.label {
								font-size: 16px;

								@media screen and (max-width: 768px) {
									font-size: 13px;
									font-family: Arial;

									&::before {
										top: 2px;
									}

									&::after {
										top: 5px;
									}
								}
							}
						}

						.title {
							font-size: 16px;
							font-family: Calibri;
							font-weight: 400;
							color: #666666;
							margin: 20px 0 10px 0;

							@media screen and (max-width: 768px) {
								font-size: 12px;
								font-family: Arial;
							}
						}

						.input {
							.myInput {
								height: 26px;
							}
						}

						>div:nth-child(2) {
							padding: 15px 20px 35px 10px;

							@media screen and (max-width: 1730px) and (min-width: 1559px) {
								padding: 0;
							}

							@media screen and (max-width: 768px) {
								padding: 0;
							}
						}

						.el-input__inner {
							height: 26px;
							font-family: Calibri;
						}

						.el-select {
							height: 100%;
							width: 100%;

							>div {
								height: 100%;
							}
						}

						.uploadSuccess {
							padding: 0 10px;
							height: 20px;
							margin-top: 5px;

							>div {
								display: flex;
								justify-content: space-between;

								i {
									color: #39c464;
								}
							}
						}

						.btn {
							margin-top: 5px;
							border: 1px solid transparent;

							@media screen and (max-width: 768px) {
								margin-top: 10px;
								height: 32px;
							}

							.custom-btn {
								font-family: Calibri;
								width: 100%;
								height: 100%;
								background: #0066cc;
								border-color: #0066cc;
								border-radius: 2px;
								font-size: 18px;
								font-weight: 700;
								color: #fff;
								line-height: 22px;
								display: flex;
								align-items: center;
								justify-content: center;

								@media screen and (max-width: 768px) {
									font-size: 12px;
									font-family: Arial;
									line-height: inherit;
								}
							}

							.custom-btn.is-disabled {
								opacity: 0.5;
							}
						}

						.el-checkbox__label {
							color: #333333;
							font-size: 16px;
							font-weight: 400;

							@media screen and (max-width: 768px) {
								font-size: 12px;
								font-family: Arial;
							}
						}
					}
				}

				// .el-checkbox__inner {
				//   border: 2px solid #d9d9d9;
				//   width: 16px;
				//   height: 16px;
				//   border-radius: 50%;
				//   transition: none;
				// }
				// .el-checkbox__inner::after {
				//   border: none;
				//   border-radius: 50%;
				//   background-color: #d9d9d9;
				//   width: 6px;
				//   height: 6px;
				//   left: 3px;
				//   top: 3px;
				//   transition: none;
				//   transform: none;
				// }
				.el-checkbox.el-checkbox--large .el-checkbox__inner {
					width: 20px;
					height: 20px;
				}

				.el-checkbox__input.is-checked .el-checkbox__inner::after {
					border-color: #0066cc;
					top: 1px;
					left: 6px;
					height: 11px;
					width: 6px;
				}

				.el-checkbox__input.is-checked .el-checkbox__inner {
					background-color: #ffffff;
					border-color: #0066cc;
				}

				.el-checkbox__input.is-checked+.el-checkbox__label {
					color: #0066cc;
				}

				.el-form-item__content {
					height: 26px;
					margin-top: 9px;

					.el-input {
						height: 100%;
					}
				}

				.el-form-item.btn {
					margin-top: 30px !important;
					display: flex;

					.el-form-item__content {
						margin: 0;
						height: 40px;
						width: 196px;

						@media screen and (max-width: 768px) {
							width: 100%;
						}

						label {
							width: 100%;
							height: 100%;
							cursor: pointer;

							.custom-btn {
								width: 100%;
								height: 100%;
								background: #0066cc;
								border-color: #0066cc;
								border-radius: 2px;
								font-size: 18px;
								font-weight: bold;
								color: #ffffff;
								text-align: center;
								line-height: 40px;
							}
						}
					}
				}
			}

			.lanyardBadgeHolderOptions {
				width: 100%;

				.swiper-button-prev {
					left: 0;
				}

				.swiper-button-next {
					right: 0;
				}

				.swiper-button-prev,
				.swiper-button-next {
					top: 0;
					margin-top: 0;
					height: 100%;
				}

				.swiper-pagination {
					display: none;
				}

				.top-item {
					cursor: pointer;
					text-align: center;
					background: #f5f6f7;
					border: 1px solid #f0f1f2;
					border-radius: 10px 10px 0px 0px;
					box-sizing: content-box;

					>div {
						padding: 10px 0 20px 0;
						height: 100%;

						@media screen and (max-width: 768px) {
							padding: 10px 0;
						}
					}

					.title {
						line-height: 1em;
						font-size: 16px;
						font-family: Calibri;
						font-weight: 400;
						color: #333333;
						white-space: nowrap;
						overflow: hidden;
						text-overflow: ellipsis;

						@media screen and (max-width: 768px) {
							font-size: 12px;
							height: 24px;
							font-family: Arial;
							white-space: normal;
							overflow: inherit;
							text-overflow: inherit;
						}
					}

					// .title.no {
					//   height: 32px;
					//   @media screen and (max-width: 768px) {
					//     height: 41px;
					//   }
					// }
					.price {
						line-height: 1em;
						font-size: 16px;
						text-align: center;

						@media screen and (max-width: 768px) {
							font-size: 12px;
							font-family: Arial;
							margin-top: 5px;
						}
					}

					.el-image {
						height: 240px;

						@media screen and (max-width: 768px) {
							height: 96px;
							padding: 10px 0;
						}
					}

					.no {
						.el-image {
							height: 115px;
							padding: 62.5px 0;

							@media screen and (max-width: 768px) {
								height: 96px;
								padding: 10px 0;
							}
						}
					}
				}

				.border-bottom-0 {
					border-bottom-color: transparent;
				}

				.top-item:first-child {
					// height: 307px;
				}

				.box {
					display: grid;
					box-sizing: border-box;
					grid-template-columns: repeat(4, 1fr);
					justify-content: space-between;
					gap: 24px;
					border: 1px solid #ebebeb;
					padding: 30px 13px;

					// height: 335px;
					overflow-y: auto;
					overflow-x: hidden;
					border-top: 0;

					@media screen and (max-width: 1680px) {
						padding: 15px;
						grid-template-columns: repeat(3, 1fr);
					}

					@media screen and (max-width: 1280px) {
						padding: 15px;
						grid-template-columns: repeat(5, 1fr);
					}

					@media screen and (max-width: 768px) {
						grid-template-columns: repeat(2, 1fr);
						padding: 10px;
						gap: 10px;
					}

					.bottom-item {
						text-align: center;
						border: 1px solid #f5f5f5;
						border-radius: 4px;
						padding: 10px 39px 16px 39px;

						@media screen and (max-width: 1680px) {
							padding: 10px;
						}

						.price {
							font-size: 16px;
							font-weight: 400;
							font-family: Calibri;
							margin-top: 5px;

							@media screen and (max-width: 768px) {
								font-size: 12px;
								font-family: Arial;
							}
						}

						.inputbox {
							margin-top: 5px;
							align-items: center;
							display: flex;

							@media screen and (max-width: 768px) {
								flex-direction: column;
								justify-content: center;
							}

							>div:nth-child(1) {
								line-height: 1em;
								font-size: 16px;
								white-space: nowrap;

								@media screen and (max-width: 768px) {
									font-size: 12px;
									font-family: Arial;
								}
							}

							.myInput {
								margin-left: 10px;
								margin-top: 5px;

								@media screen and (max-width: 768px) {
									margin-left: 0;
								}

								&:focus {
									border: 1px solid #0066cc;
								}
							}
						}

						.el-input__inner {
							height: 30px;
							text-align: left;
							margin-top: 5px;

							@media screen and (max-width: 768px) {
								height: 26px;
							}
						}

						// &:hover {
						//   border: 1px solid #0066cc;
						// }
					}
				}
			}

			.badgeReel {
				width: 100%;

				.swiper-button-prev {
					left: 0;
				}

				.swiper-button-next {
					right: 0;
				}

				.swiper-button-prev,
				.swiper-button-next {
					top: 0;
					margin-top: 0;
					height: 100%;
				}

				.swiper-pagination {
					display: none;
				}

				.top-item {
					cursor: pointer;
					text-align: center;
					background: #f5f6f7;
					border: 1px solid #f0f1f2;
					border-radius: 10px 10px 0px 0px;
					box-sizing: content-box;

					>div {
						padding: 10px 0 20px 0;
						height: 100%;

						@media screen and (max-width: 768px) {
							padding: 10px 0;
						}
					}

					.title {
						line-height: 1em;
						font-size: 16px;
						font-family: Calibri;
						font-weight: 400;
						color: #333333;
						white-space: nowrap;
						overflow: hidden;
						text-overflow: ellipsis;

						@media screen and (max-width: 768px) {
							font-size: 12px;
							height: 24px;
							font-family: Arial;
							white-space: normal;
							overflow: inherit;
							text-overflow: inherit;
						}
					}

					.title.no {
						@media screen and (max-width: 768px) {
							height: 24px;
						}
					}

					.price {
						line-height: 1em;
						font-size: 16px;
						text-align: center;

						@media screen and (max-width: 768px) {
							font-size: 12px;
							font-family: Arial;
							margin-top: 5px;
						}
					}

					.no {
						.el-image {
							height: 115px;
							padding: 62.5px 0;

							@media screen and (max-width: 768px) {
								height: 96px;
								padding: 10px 0;
							}
						}
					}

					.el-image {
						height: 240px;

						@media screen and (max-width: 768px) {
							height: 96px;
							padding: 10px 0;
						}
					}
				}

				.border-bottom-0 {
					border-bottom-color: transparent;
				}

				.top-item:first-child {}

				.box {
					display: grid;
					grid-template-columns: repeat(6, 1fr);
					box-sizing: border-box;
					justify-content: space-between;
					gap: 15px;
					border: 1px solid #ebebeb;
					padding: 30px 13px;
					overflow-y: auto;
					overflow-x: hidden;
					border-top: 0;

					@media screen and (max-width: 1680px) {
						padding: 15px;
						grid-template-columns: repeat(4, 1fr);
					}

					@media screen and (max-width: 1280px) {
						padding: 15px;
						grid-template-columns: repeat(6, 1fr);
					}

					@media screen and (max-width: 768px) {
						padding: 10px;
						gap: 10px;
						grid-template-columns: repeat(3, 1fr);
					}

					.bottom-item {
						text-align: center;
						border: 1px solid #f5f5f5;
						border-radius: 4px;
						padding: 12px;

						@media screen and (max-width: 768px) {
							padding: 10px;
						}

						.el-image {
							@media screen and (max-width: 768px) {
								height: 96px;
							}
						}

						.el-input__inner {
							height: 30px;
							text-align: left;

							@media screen and (max-width: 768px) {
								height: 26px;
							}
						}

						.price {
							font-size: 16px;
							font-weight: 400;
							font-family: Calibri;
							margin-top: 5px;

							@media screen and (max-width: 768px) {
								font-size: 12px;
								font-family: Arial;
							}
						}

						.title {
							font-size: 16px;
							margin-top: 10px;
							line-height: 1em;

							@media screen and (max-width: 768px) {
								font-size: 12px;
							}
						}

						.input {
							margin-top: 5px;
							display: flex;

							.myInput {
								&:focus {
									border: 1px solid #0066cc;
								}
							}
						}

						// &:hover {
						//   border: 1px solid #0066cc;
						// }
					}
				}
			}

			.selectDeliveryDate {
				.title {
					font-size: 16px;
					font-weight: 400;
					color: #666666;
					text-align: left;
					margin-bottom: 20px;

					@media screen and (max-width: 768px) {
						font-size: 12px;
						font-family: Arial;
						font-weight: 400;
						color: #999999;
					}
				}

				.box {
					display: grid;
					grid-template-columns: repeat(4, 1fr);
					gap: 10px;

					@media screen and (max-width: 768px) {
						grid-template-columns: repeat(2, 1fr);
					}

					::v-deep .item {
						padding: 25px;
						border: 1px solid #e9ecf0;
						border-radius: 10px;
						display: flex;

						@media screen and (max-width: 768px) {
							align-items: flex-start;
							padding: 15px;
						}

						>.se {
							.radio-check {
								padding-left: 0;
								margin-bottom: 15px;
								width: 100%;

								>.label {
									text-align: left;
									padding-left: 30px;
									font-size: 18px;
									font-weight: bold;
									color: #333333;
									width: 100%;
									display: block;

									@media screen and (max-width: 768px) {
										padding-left: 20px;
									}
								}

								>.label:before {
									left: 0;
									top: 50%;
									transform: translateY(-50%);
								}

								>.label:after {
									left: 5px;
									top: 50%;
									transform: translateY(-50%);
								}

								@media screen and (max-width: 768px) {
									>.label {
										font-size: 14px;
										font-family: Arial;
										font-weight: bold;
									}

									// > .label:before {
									//   left: 6px;
									//   top: 0;
									// }
									>.label:after {
										left: 4px;
									}
								}
							}

							.info {
								font-size: 16px;
								font-family: Calibri;
								font-weight: 400;
								color: #666666;
								line-height: 1.5em;

								@media screen and (max-width: 768px) {
									font-size: 12px;
									font-family: Arial;
								}
							}
						}
					}

					::v-deep .active {
						background: #0066cc;
						border: 1px solid #0066cc !important;
						color: #ffffff;

						.label {
							color: #ffffff !important;
						}

						.info {
							color: #ffffff !important;
						}

						input[type="radio"]:checked+.label:before {
							border-color: #ffffff;
						}

						input[type="radio"]:checked+.label:after {
							background-color: #ffffff;
						}
					}
				}

				.el-form-item__label {
					margin-bottom: 15px !important;
				}
			}

			::v-deep .customizeYourLanyards {
				width: 100%;

				.el-select .el-input .el-select__caret {
					color: black;
				}

				.el-tabs__nav-scroll {
					overflow-x: auto;
				}

				.el-tabs__content {
					overflow: visible;
				}

				.el-image {
					width: 113px;
					height: 152px;
					transition: all 0.3s;

					@media screen and (max-width: 768px) {
						width: 100%;
						height: 100%;
					}
				}

				.el-image.isPC:hover {
					transform: scale(1.5);
					background-color: #fff;
					z-index: 2;
				}

				.el-tabs__header {
					background: #f5f6f7;
					height: 120px;
					display: flex;
					align-items: center;
					padding: 5px 10px 0 10px;

					@media screen and (max-width: 768px) {
						height: auto;
					}
				}

				.el-tabs__nav-wrap {
					width: 100%;
					margin-bottom: -2px;
				}

				.el-tabs__nav {
					display: grid;
					width: 100%;
					grid-template-columns: repeat(5, 1fr);

					@media screen and (max-width: 620px) {
						grid-template-columns: repeat(5, 155px);
					}

					.el-tabs__item {
						padding: 10px;
						font-size: 18px;
						font-weight: 400;
						color: #999999;
						text-align: center;
						line-height: normal;
						display: flex;
						flex-direction: column;
						height: 100%;
						border-radius: 10px 10px 0 0;
						position: relative;
						border: 1px solid transparent;

						@media screen and (max-width: 768px) {
							font-size: 12px;
							font-family: Arial;
						}

						.custom-tabs-label {
							display: flex;
							flex-direction: column;
							justify-content: space-between;
							height: 100%;

							img {
								height: 75px;
								object-fit: contain;

								@media screen and (max-width: 768px) {
									height: 50px;
								}
							}

							.iconfont {
								font-size: 34px;
								flex: 1;
								display: flex;
								align-items: center;
								justify-content: center;
							}
						}

						&:not(:last-child):after {
							position: absolute;
							content: "";
							right: -3px;
							top: 50%;
							transform: translateY(-50%);
							width: 1px;
							height: 30%;
							background-color: #d2d5d9;
						}
					}

					.el-tabs__item:hover {
						border-color: #0066cc;
					}

					.el-tabs__item.is-active {
						background-color: #fff;
						font-weight: bold;
						color: #0066cc;

						@media screen and (max-width: 768px) {
							font-weight: normal;
						}
					}

					.el-tabs__item.is-active::after {
						display: none;
					}

					.el-tabs__active-bar {
						display: none;
					}
				}

				.t-radio {
					display: grid;
					grid-template-columns: repeat(3, 1fr);
					column-gap: 20px;
					padding: 20px 10px;
					border-left: 1px solid #e6e6e6;
					border-right: 1px solid #e6e6e6;

					@media screen and (max-width: 1730px) and (min-width: 1559px) {
						column-gap: 10px;
					}

					@media screen and (max-width: 768px) {
						column-gap: 5px;
						margin-top: 10px;

						.custom-checkbox {
							padding-left: 0;
						}

						.label:before {
							width: 9px;
							height: 9px;
							transform: translateX(-50%);
							left: 50%;
							top: -14px;
						}

						.label:after {
							width: 5px;
							height: 5px;
							transform: translateX(-50%);
							left: 50%;
							top: -10px;
						}
					}

					.label {
						font-size: 16px;
						font-weight: 400;
						color: #333333;

						@media screen and (max-width: 768px) {
							font-size: 12px;
							text-align: center;
							font-family: Arial;
						}
					}

					.item {
						position: relative;
						display: flex;
						flex-direction: column;
						justify-content: center;
						align-items: center;
						border: 1px solid #e6e6e6;
						border-radius: 6px;
						padding: 10px 0 16px 0;
						transition: all 0.3s;

						@media screen and (max-width: 768px) {
							padding: 6px 0;
						}

						.el-image {
							margin-bottom: 15px;

							@media screen and (max-width: 768px) {
								margin-bottom: 20px;
								height: 76px;
							}
						}

						input[type="radio"]:checked+.label {
							color: #0066cc;
						}

						.hover-box {
							position: absolute;
							display: none;
							z-index: 2;
							right: -150px;
							top: 5px;
							background: #f5f6f7;
							box-shadow: 0px 2px 5px 0px rgb(0 0 0 / 10%);
							width: 222px;
							height: 271px;
							justify-content: center;
							align-items: center;

							.el-image {
								width: 100%;
								height: 100%;
								margin-bottom: 0;
							}

							&::before {
								position: absolute;
								content: "";
								width: 0;
								height: 0;
								border-top: 10px solid transparent;
								border-right: 15px solid #f5f6f7;
								border-bottom: 10px solid transparent;
								left: -15px;
								top: 20px;
							}
						}
					}

					.item:hover {
						.hover-box {
							display: flex;
						}
					}
				}

				.t-radio.bottom {
					border-bottom: 1px solid #e6e6e6;
				}

				.onlyText {
					.cyl-content {
						border: 1px solid #e6e6e6;
						border-top: none;
						padding: 23px;

						@media screen and (max-width: 1650px) {
							padding: 10px;
						}

						@media screen and (max-width: 768px) {
							padding: 20px 10px;
						}

						.el-form:nth-child(1) {
							margin-bottom: 15px;
						}

						.el-form {
							display: grid;
							grid-template-columns: repeat(48, 1fr);
							justify-content: space-between;

							@media screen and (max-width: 768px) {
								column-gap: 0;
								row-gap: 15px;
							}

							.cyl-title {
								font-size: 18px;
								font-weight: 400;
								font-family: Calibri;
								color: #666666;
								margin-bottom: 6.5px;

								@media screen and (max-width: 1650px) {
									font-size: 12px;
									font-family: Arial;
								}
							}

							>div:nth-child(1) {
								grid-column: 1/15;
								padding-right: 10px;

								@media screen and (max-width: 1650px) {
									grid-column: 1/14;
								}

								@media screen and (max-width: 768px) {
									grid-column: 1/24;
								}
							}

							>div:nth-child(2) {
								grid-column: 15/29;
								padding-right: 10px;

								@media screen and (max-width: 1650px) {
									grid-column: 14/28;
								}

								@media screen and (max-width: 768px) {
									grid-column: 26/49;
								}

								.el-select {
									width: 100%;
								}
							}

							>div:nth-child(3) {
								grid-column: 29/43;

								@media screen and (max-width: 1650px) {
									grid-column: 28/42;
								}

								@media screen and (max-width: 768px) {
									grid-column: 1/23;
									grid-row: 2/3;
								}
							}

							>div:nth-child(4) {
								grid-column: 44/49;

								@media screen and (max-width: 1650px) {
									grid-column: 42/49;
								}

								@media screen and (max-width: 768px) {
									grid-column: 26/38;
									grid-row: 2/3;
								}

								>div {
									display: flex;

									button {
										height: 40px;
										background-color: #f5f6f7;
										border-color: transparent;
										font-family: Calibri;
										font-size: 20px;

										@media screen and (max-width: 768px) {
											height: 30px;
											font-size: 12px;
										}
									}

									.el-button+.el-button {
										margin-left: 5px;
									}
								}
							}

							.el-form-item__label {
								font-size: 18px !important;
								font-weight: 400 !important;
								color: #666666 !important;
								margin: 0 !important;
								padding: 0 !important;
								padding-bottom: 5px;
								line-height: 2em;
							}

							.customFontFamily {
								.el-input__inner {
									font-family: $font_family;
								}
							}

							.customFontFamily2 {
								.el-input__inner {
									font-family: $font_family2;
								}
							}

							.el-input__inner {
								height: 40px;
								background-color: #f5f6f7;
								font-size: 16px;
								font-family: Calibri;

								@media screen and (max-width: 768px) {
									height: 30px;
									font-size: 12px;
									font-family: Arial;

									// padding-right: 60px;
								}
							}
						}

						.el-form.mob {
							margin-top: 15px;

							@media screen and (max-width: 768px) {
								justify-content: space-between;

								>div:nth-child(1) {
									// margin-right: 15px;
								}
							}

							.el-form-item--default {
								margin-bottom: 0;
							}
						}

						.el-form {
							>div:nth-child(1) {
								grid-column: 1/15;

								@media screen and (max-width: 1650px) {
									grid-column: 1/14;
								}

								@media screen and (max-width: 768px) {
									grid-column: 1/25;
								}
							}

							>div:nth-child(2) {
								grid-column: 15/29;

								@media screen and (max-width: 1650px) {
									grid-column: 14/28;
								}

								@media screen and (max-width: 768px) {
									grid-column: 26/49;
								}
							}

							.fontBox {
								width: 100%;
								position: relative;

								.fontImg {
									position: absolute;
									width: 25px;
									height: 25px;
									right: 30px;
									top: 50%;
									transform: translateY(-50%);
									cursor: pointer;

									img {
										width: 100%;
										height: 100%;
										object-fit: contain;
									}
								}

								.el-select {
									width: 100%;
								}
							}
						}

						.comments {
							margin-top: 30px;

							@media screen and (max-width: 768px) {
								margin-top: 20px;
							}

							>div:nth-child(1) {
								font-size: 18px;
								font-weight: 400;
								color: #333333;
								line-height: 1em;
								margin-bottom: 10px;

								@media screen and (max-width: 768px) {
									font-size: 12px;
									font-family: Arial;
									color: #666666;
								}
							}

							>div:nth-child(2) {
								font-size: 14px;
								font-weight: 400;
								color: #999999;
								margin-bottom: 20px;
								line-height: 1em;

								@media screen and (max-width: 768px) {
									font-size: 12px;
									font-family: Arial;
									color: #999999;
									margin-bottom: 10px;
								}
							}
						}

						.inputStyleLike {
							display: flex;
							justify-content: space-between;
							align-items: center;
							background: #f5f6f7;
							border-radius: 4px;
							padding: 0 13px;
							height: 38px;
							border: 1px solid transparent;
							font-size: 16px;

							@media screen and (max-width: 768px) {
								height: 28px;
								font-size: 12px;
							}

							>div:nth-child(1) {
								display: flex;
								align-items: center;

								.showColor {
									width: 22px;
									height: 22px;
									border-radius: 2px;
									margin-right: 5px;
								}
							}

							>div:nth-child(2) {
								display: flex;
								align-items: center;
							}
						}

						.fullColor {

							// padding: 0 7px;
							.el-checkbox__label {
								font-size: 18px !important;
							}
						}
					}
				}

				.upload-box {
					border: 1px solid #e6e6e6;
					border-top: none;
					padding: 23px;

					@media screen and (max-width: 768px) {
						padding: 10px;
					}

					>div:nth-child(1) {
						font-size: 18px;
						font-weight: 400;
						color: #333333;
						line-height: 1em;
					}

					>div:nth-child(2) {
						font-size: 14px;
						font-weight: 400;
						color: #999999;
						line-height: 2em;
					}

					.upload-window {
						border: 2px dashed #d9dbdd;
						padding: 0 1em;

						ul {
							padding: 0;

							li {
								list-style-type: none;
								display: flex;
								justify-content: space-between;
								margin: 0.4em 0;
								font-size: 16px;

								.icon-box {
									>i:nth-child(1) {
										margin-right: 15px;
										color: #39c464;
									}

									>i:nth-child(2) {
										margin-right: 15px;
										color: #cccccc;
									}
								}
							}
						}

						.uploadLabel {
							display: flex;
							flex-direction: column;
							justify-content: center;
							align-items: center;
							height: 100%;
							border-radius: 4px;
							padding: 30px 0;
							margin-top: 10px;

							@media screen and (max-width: 768px) {
								padding: 6.5px 0;
							}

							i {
								font-size: 45px;
								color: gainsboro;
								display: flex;
								width: 68px;
								height: 55px;
								justify-content: center;
							}

							.el-button {
								width: 182px;
								height: 40px;
								font-size: 18px;
								color: #fff;
								background-color: #0d6efd;
								border-color: #0d6efd;

								@media screen and (max-width: 768px) {
									font-size: 13.5px;
									font-family: Arial;
									height: 30px;
								}
							}

							.el-button.is-disabled {
								opacity: 0.5;
							}

							.info {
								font-size: 16px;
								color: #999999;
								text-align: center;
								margin: 5px 0;

								@media screen and (max-width: 768px) {
									font-size: 12px;
									font-family: Arial;
									margin-top: 10px;
								}
							}

							.el-checkbox__inner {
								width: 16px;
								height: 16px;
							}

							.el-checkbox__inner::after {
								height: 9px;
								left: 5px;
							}

							.el-checkbox__label {
								font-size: 16px;
							}
						}
					}

					.comments {
						margin-top: 30px;

						@media screen and (max-width: 768px) {
							margin-top: 20px;
						}

						>div:nth-child(1) {
							font-size: 18px;
							font-weight: 400;
							color: #333333;
							line-height: 1em;
							margin-bottom: 10px;

							@media screen and (max-width: 768px) {
								font-size: 12px;
								font-family: Arial;
								color: #666666;
							}
						}

						>div:nth-child(2) {
							font-size: 14px;
							font-weight: 400;
							color: #999999;
							margin-bottom: 20px;
							line-height: 1em;

							@media screen and (max-width: 768px) {
								font-size: 12px;
								font-family: Arial;
								color: #999999;
								margin-bottom: 10px;
							}
						}
					}
				}
			}

			.el-form--label-top .el-form-item {
				margin-top: 0;
			}

			.el-form--default.el-form--label-top .el-form-item .el-form-item__label {
				font-size: 27px;
				font-weight: bold;
				color: #202428;
				margin-bottom: 30px;
				padding-bottom: 0.2em;
				white-space: nowrap;

				@media screen and (max-width: 768px) {
					font-size: 16px;
					font-family: Arial;
					font-weight: bold;
					color: #202428;
					margin-bottom: 14px;
				}
			}
		}

		#steps12 {
			>label {
				margin-bottom: 15px !important;
			}
		}

		.canvas-zone {
			grid-column: 30/49;
			position: relative;

			// transform: translate(-600px, 900px);
			@media screen and (max-width: 1650px) and (min-width: 1280px) {
				grid-column: 28/49;
			}

			@media screen and (max-width: 1279px) {
				display: none;
			}

			>div {
				opacity: 1;
				transform: translate(0, 0);
				transition: all 0.5s ease-in;
				padding: 10px;
				background-color: #f3f4f5;
				border-radius: 10px 0 0 0;
				position: sticky;
				display: grid;
				grid-template-columns: minmax(300px, auto) 1fr;
				top: 10px;

				.canvas {
					img {
						height: calc(100vh - 40px);
						object-fit: contain;
						image-rendering: -moz-crisp-edges;
						image-rendering: -o-crisp-edges;
						image-rendering: -webkit-optimize-contrast;
						image-rendering: crisp-edges;
						-ms-interpolation-mode: nearest-neighbor;
					}

					#canvas {
						vertical-align: middle;
					}
				}

				.info {
					padding: 30px 30px 20px 30px;
					max-height: 893px;
					overflow-y: auto;
					box-sizing: border-box;

					@media screen and (max-width: 1800px) {
						padding: 1em;
					}

					>div {
						margin-bottom: 20px;

						.title {
							font-size: 18px;
							font-weight: bold;
							color: #333333;
							text-align: center;
							margin-bottom: 7px;
						}

						.box {
							display: grid;
							grid-template-columns: 1fr;

							// grid-auto-rows: 80px;
							border: 1px solid #d9dbdd;
							border-radius: 4px;
							box-sizing: border-box;

							overflow-y: auto;
							max-height: 170px;

							.item {
								display: grid;
								grid-template-columns: 39px 1fr 1fr 1fr;
								padding: 0 15px;
								grid-auto-rows: auto;
								box-sizing: border-box;
								height: 80px;
								column-gap: 10px;
								transition: all 0.3s;

								>div:nth-child(1) {
									grid-column: 1/2;
									grid-row: 1/3;

									>div {
										background-size: cover;
										border: 1px solid #d9dbdd;
									}
								}

								>div:nth-child(2) {
									grid-column: 2/5;
									grid-row: 1/2;
									display: flex;
									align-items: center;
									font-size: 16px;
									font-weight: 400;
									padding-top: 5px;
									color: #666666;
								}

								>div:nth-child(3) {
									grid-column: 2/5;
									grid-row: 2/3;
									display: flex;
									align-items: center;
									justify-content: space-between;
									padding-bottom: 10px;

									>div:nth-child(1) {
										>div:nth-child(1) {
											font-size: 16px;
										}
									}

									::v-deep .el-input {
										height: 100%;
										max-width: 96px;
										width: 100%;
										height: 26px;

										.el-input__inner {
											height: 100%;
											text-align: left;
										}
									}
								}
							}

							.item:hover {
								background-color: #f3f4f5;
							}

							.item:not(:last-child) {
								border-bottom: 1px dashed #e6e6e6;
							}

							.item.sp {
								>div:nth-child(1) {
									>div {
										border: 1px solid #e6e6e6;
									}
								}

								>div:nth-child(2) {
									grid-row: 1/3;
									grid-column: 2/4;
									padding-top: 0;
								}

								>div:nth-child(3) {
									grid-row: 1/3;
									grid-column: 4/5;
									padding-bottom: 0;
									justify-content: flex-end;
								}
							}
						}
					}

					::v-deep .price-info {
						display: grid;
						grid-template-columns: repeat(4, 1fr);
						grid-auto-rows: auto;
						border-bottom: 1px solid #d9d9d9;
						padding: 14px 0 14px 10px;
						row-gap: 15px;
						column-gap: 10px;
						margin-bottom: 20px;

						>div {
							font-size: 16px;
							font-weight: 400;
							color: #666666;

							@media screen and (max-width: 768px) {
								font-size: 12px;
								font-family: Arial;
								font-weight: bold;
							}
						}

						>div:nth-child(2n-1) {
							grid-column: 1/3;
						}

						>div:nth-child(2n) {
							grid-column: 3/5;
						}

						.title {
							color: #222222;
							margin-bottom: 0;
							text-align: left;
						}
					}

					.subtotal {
						display: grid;
						grid-template-columns: repeat(3, 1fr);
						padding: 0 10px;

						>div:nth-child(1) {
							font-size: 16px;
							font-weight: 400;
							color: #222222;
							display: flex;
							align-items: flex-end;
							padding-bottom: 5px;

							@media screen and (max-width: 768px) {
								font-size: 12px;
								font-family: Arial;
								font-weight: bold;
							}
						}

						>div:nth-child(2) {
							font-size: 30px;
							color: #de3500;
							font-weight: bold;
						}

						>div:nth-child(3) {
							.el-select {
								width: 70px;
								height: 36px;

								>div {
									width: 100%;
									height: 100%;

									.el-input {
										width: 100%;
										height: 100%;

										.el-input__inner {
											font-size: 15px;
											font-family: Roboto;
											font-weight: 400;
											color: #333333;
											padding-right: 25px;

											@media screen and (max-width: 768px) {
												font-size: 12px;
												font-family: Arial;
											}
										}
									}
								}
							}
						}
					}

					.submit {
						button {
							width: 100%;
							height: 40px;
							font-size: 18px;
							font-weight: 400;
							color: #ffffff;
							font-family: inherit;

							@media screen and (max-width: 768px) {
								font-size: 12px;
								font-family: Arial;
								font-weight: bold;
							}
						}

						>div:nth-child(1) {
							button {
								background: linear-gradient(90deg, #0066cc 0%, #2fb6f5 100%);
								border-radius: 4px;
							}
						}

						>div:nth-child(2) {
							button {
								background: linear-gradient(90deg, #ff412b 0%, #ff7743 100%);
								border-radius: 4px;

								.icon {
									display: inline-block;
									width: 18px;
									height: 18px;
									border-radius: 50%;
									background-color: #fff;
									font-size: 16px;
									font-family: Calibri;
									font-weight: bold;
									color: #333333;
									line-height: 19px;
									margin-left: 10px;
								}
							}
						}
					}

					.help {
						background: #ffffff;
						border-radius: 4px;
						padding: 14px 15px 21px 22px;
						margin-bottom: 0;
						display: flex;
						text-decoration: none;

						>div:first-child {
							display: flex;
							justify-content: center;
							align-items: center;

							i {
								font-size: 48px;

								@media screen and (max-width: 768px) {
									font-size: 24px;
								}
							}
						}

						.help-content {
							font-size: 16px;
							font-weight: 400;
							color: #333333;
							padding-left: 20px;
							display: flex;
							text-align: left;
							align-items: center;

							@media screen and (max-width: 768px) {
								font-size: 12px;
								font-family: Arial;
								font-weight: bold;
							}
						}
					}
				}
			}

			>div.obj-mask {
				top: 10px;
			}
		}

		.canvas-zone {
			>div.canvasMove {
				transform: translate(-600px, 900px);
				opacity: 0;
				z-index: -1;
			}
		}
	}

	.allInfo {
		display: flex;
		justify-content: center;
		background-color: #f3f4f5;

		.canvasT {
			display: flex;
			max-width: 850px;
			margin: 0 auto;
			background-color: #f3f4f5;
			padding: 20px 0.5rem 20px 0.5rem;
			opacity: 0;
			transition: all 0.5s;

			@media screen and (max-width: 768px) {
				padding: 20px 0.5rem 0 0.5rem;
			}

			.left {
				flex: 1;

				@media screen and (max-width: 768px) {
					display: none;
				}
			}

			.right {
				display: grid;
				grid-template-columns: 1fr;
				height: 100%;
				margin-left: 40px;
				flex: 1;

				@media screen and (max-width: 768px) {
					margin-left: 0;
				}

				.title {
					font-size: 27px;
					font-weight: bold;
					color: #333333;

					// margin-bottom: 32px;
					@media screen and (max-width: 768px) {
						font-size: 14px;
						font-family: Arial;
						margin-bottom: 10px;
					}
				}

				.box {
					display: grid;
					grid-template-columns: 1fr;
					max-height: 380px;
					overflow: hidden;

					.item {
						display: grid;
						grid-template-columns: 1fr 1fr 20px;

						// grid-auto-rows: 34px;
						gap: 5px;
						padding: 0 5px;
						line-height: 2em;
						margin-bottom: 5px;
						cursor: pointer;
						align-items: baseline;

						&:hover {
							background-color: #ffffff;
						}

						@media screen and (max-width: 768px) {
							padding: 0;
						}

						// * {
						//   overflow: hidden;
						//   text-overflow: ellipsis;
						//   white-space: nowrap;
						// }

						.l {
							font-size: 16px;
							font-weight: 400;
							color: #555555;
							display: flex;
							align-items: center;

							@media screen and (max-width: 768px) {
								font-size: 12px;
								font-family: Arial;
							}
						}

						.m {
							display: flex;
							font-size: 16px;
							font-weight: 400;
							color: #222222;
							flex-direction: column;
							align-self: center;
							justify-content: center;

							@media screen and (max-width: 768px) {
								font-size: 12px;
								font-family: Arial;
							}

							>span {
								font-size: 16px;
								font-weight: 400;
								color: #222222;

								@media screen and (max-width: 768px) {
									font-size: 12px;
									font-family: Arial;
								}
							}
						}

						.r {
							font-size: 14px;
							color: #b3b3b3;
							line-height: 33px;

							@media screen and (max-width: 768px) {
								font-size: 12px;
								font-family: Arial;
							}
						}

						.fontImg {
							width: 25px;
							height: 25px;
							margin-right: 5px;

							img {
								width: 100%;
								height: 100%;
								-o-object-fit: contain;
								object-fit: contain;
							}
						}
					}
				}

				.viewMore {
					text-align: center;
					padding-bottom: 19px;
					border-bottom: 1px solid #d9d9d9;

					@media screen and (max-width: 768px) {
						// padding-bottom: 0;
						// margin-top:25px;
					}

					span {
						font-size: 16px;
						font-family: Calibri;
						font-weight: 400;
						text-decoration: underline;
						color: #0066cc;

						@media screen and (max-width: 768px) {
							font-size: 12px;
							font-family: Arial;
						}
					}
				}

				::v-deep .price-info {
					display: grid;
					grid-template-columns: 1fr 1fr 20px;
					grid-auto-rows: auto;
					border-bottom: 1px solid #d9d9d9;
					padding: 14px 0 14px 10px;
					row-gap: 15px;
					column-gap: 5px;
					margin-bottom: 20px;

					@media screen and (max-width: 768px) {
						grid-template-columns: 1fr 1fr;
					}

					>div {
						font-size: 16px;
						font-weight: 400;
						color: #666666;

						@media screen and (max-width: 768px) {
							font-size: 12px;
							font-family: Arial;
							color: #333333;
							text-align: right;
						}
					}

					>div:nth-child(2n-1) {
						grid-column: 1/2;
					}

					>div:nth-child(2n) {
						grid-column: 2/4;
					}

					.title {
						color: #222222;
						margin-bottom: 0;
						text-align: left;

						@media screen and (max-width: 768px) {
							font-size: 12px;
							font-family: Arial;
							color: #555555;
						}
					}
				}

				.subtotal {
					display: flex;
					justify-content: center;
					padding: 0 10px 10px 10px;

					@media screen and (max-width: 768px) {
						padding: 15px;
					}

					>div {
						display: flex;
						align-items: flex-end;
					}

					>div:nth-child(1) {
						font-size: 16px;
						font-weight: 400;
						color: #222222;
						display: flex;
						align-items: flex-end;
						padding-bottom: 5px;

						@media screen and (max-width: 768px) {
							font-size: 12px;
							font-family: Arial;
							padding-bottom: 2px;
						}
					}

					>div:nth-child(2) {
						font-size: 30px;
						color: #de3500;
						font-weight: bold;
						padding: 0 15px;

						@media screen and (max-width: 768px) {
							font-size: 21px;
							font-family: Arial;
							display: flex;
							align-items: flex-end;
						}
					}

					>div:nth-child(3) {
						.el-select {
							width: 70px;
							height: 36px;

							@media screen and (max-width: 768px) {
								width: 65px;
								height: 30px;
							}

							>div {
								width: 100%;
								height: 100%;

								.el-input {
									width: 100%;
									height: 100%;

									.el-input__inner {
										font-size: 15px;
										font-family: Roboto;
										font-weight: 400;
										color: #333333;
										padding-right: 25px;

										@media screen and (max-width: 768px) {
											font-size: 12px;
											font-family: Arial;
										}
									}
								}
							}
						}
					}
				}

				::v-deep .submit {
					display: grid;
					grid-template-columns: repeat(2, 1fr);
					column-gap: 20px;
					margin-bottom: 10px;

					@media screen and (max-width: 768px) {
						column-gap: 5px;
					}

					button {
						width: 100%;
						height: 40px;
						font-size: 18px;
						font-weight: 400;
						color: #ffffff;
						font-family: inherit;
						display: flex;
						align-items: center;
						justify-content: center;

						@media screen and (max-width: 768px) {
							font-size: 12px;
							font-family: Arial;
							font-weight: bold;
						}
					}

					>div:nth-child(1) {
						button {
							background: linear-gradient(90deg, #0066cc 0%, #2fb6f5 100%);
							border-radius: 4px;
						}
					}

					>div:nth-child(2) {
						button {
							background: linear-gradient(90deg, #ff412b 0%, #ff7743 100%);
							border-radius: 4px;

							.icon {
								display: inline-block;
								width: 18px;
								height: 18px;
								border-radius: 50%;
								background-color: #fff;
								font-size: 16px;
								font-family: Calibri;
								font-weight: bold;
								color: #333333;
								line-height: 19px;
								margin-left: 10px;
							}
						}
					}
				}

				.help {
					background: #ffffff;
					border-radius: 4px;
					padding: 10px 43px;
					margin-bottom: 0;
					display: flex;
					text-decoration: none;

					>div:first-child {
						display: flex;
						justify-content: center;
						align-items: center;

						i {
							font-size: 48px;

							@media screen and (max-width: 768px) {
								font-size: 24px;
							}
						}
					}

					.help-content {
						font-size: 16px;
						font-weight: 400;
						color: #333333;
						padding-left: 20px;
						display: flex;
						align-items: center;
						text-align: left;

						@media screen and (max-width: 768px) {
							font-size: 12px;
							font-family: Arial;
						}
					}
				}
			}
		}

		.canvasT.canvasMove {
			opacity: 1;
		}
	}

	.previewDialog {
		position: fixed;
		top: 30%;
		right: 0;
		display: flex;
		justify-content: flex-end;
		padding: 0.5rem;
		background: #e8f4ff;
		border-radius: 10px 0 0 10px;
		font-size: 18px;
		font-family: Arial;
		font-weight: 400;
		color: #738494;
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 10;

		>div {
			display: flex;
			justify-content: center;
			flex-direction: column;

			@media screen and (max-width: 768px) {
				font-size: 12px;
				font-family: Arial;
			}

			i {
				font-size: 20px;
			}
		}
	}
}

.el-notification.right {
	right: 50%;
	transform: translateX(50%);
}

.previewDialog2 {
	background: #0066cc;
	border-radius: 2px;
	color: white;
	padding: 10px;
	font-size: 12px;
}

.confirmBtn {
	display: flex;
	justify-content: center;
	align-items: center;

	.el-button {
		height: 40px;
		width: 150px;
		background: linear-gradient(90deg, #0066cc 0%, #2fb6f5 100%);
		border-radius: 4px;
		font-size: 18px;
		font-weight: 400;
		color: #ffffff;
		font-family: Calibri;
	}
}

.allImgDialogBox {
	display: grid;
	grid-template-columns: repeat(auto-fill, 150px);
	grid-auto-rows: 150px;
	gap: 10px;
	margin-top: 1rem;
	justify-content: space-around;

	@media screen and (max-width: 768px) {
		grid-template-columns: repeat(2, 1fr);
		justify-content: center;
		grid-auto-rows: auto;
	}

	.item {
		border: 1px solid #e6e6e6;
		border-radius: 6px;
		cursor: pointer;
		position: relative;
		background: url(data:image/png;base64,UklGRl4AAABXRUJQVlA4TFIAAAAvI8AIABcgEEj8YRthDoFs4g/7EJpANvGHfQht/gMrP7CKbKvNSS8SkIBKJERCb0cnf0RARP9Da1Yj/AdOe5LC61z2zLVmNUAeyy4GgNeZpP8B);
		box-shadow: 0 2px 4px rgb(0 0 0 / 12%), 0 0 6px rgb(0 0 0 / 4%);

		img {
			width: 100%;
			height: 100%;
			object-fit: contain;
		}

		.btn {
			button {
				width: 40px;
				height: 40px;
			}

			position: absolute;
			display: flex;
			left: 0;
			top: 0;
			bottom: 0;
			right: 0;
			background-color: rgba(0, 0, 0, 0.4);
			justify-content: center;
			align-items: center;
			opacity: 0;
			transition: all 0.3s;

			svg {
				width: 15px;
			}
		}
	}

	.item:hover {
		.btn {
			opacity: 1;
		}
	}
}

.fontBox {
	position: relative;

	.fontImg {
		position: absolute;
	}
}

.custom-checkbox {
	display: flex;
	justify-content: center;
	align-items: center;
	padding-left: 35px;
	cursor: pointer;
	position: relative;

	>input {
		visibility: hidden;
		width: 0;
		height: 0;
		opacity: 0;
		margin: 0;
		padding: 0;
		position: absolute;
		left: 0;
	}

	>.label {
		position: relative;
		cursor: pointer;
		line-height: initial;
	}

	>.label:before {
		content: "";
		position: absolute;
		left: -22px;
		top: 2px;
		border-radius: 50%;
		display: inline-block;
		vertical-align: top;
		width: 12px;
		height: 12px;
		border: 2px solid #d9d9d9;
		outline: none;
		transition: all 0.3s ease;
		-webkit-transition: all 0.3s ease;
		-moz-transition: all 0.3s ease;

		@media screen and (max-width: 768px) {
			width: 8px;
			height: 8px;
			left: -20px;
			top: 4px;
		}
	}

	>.label:after {
		content: "";
		position: absolute;
		width: 6px;
		height: 6px;
		display: inline-block;
		left: -17px;
		border-radius: 50%;
		background-color: #d9d9d9;
		transition: all 0.3s ease;
		-webkit-transition: all 0.3s ease;
		-moz-transition: all 0.3s ease;
		top: 7px;
	}

	input[type="radio"]:checked+.label:before {
		border-color: #0066cc;
	}

	input[type="radio"]:checked+.label:after {
		background: #0066cc;
	}

	input[type="checkbox"]:checked+.label:before {
		border-color: #0066cc;
	}

	input[type="checkbox"]:checked+.label:after {
		background: #0066cc;
	}
}

.colorPickerGridBox {
	display: grid;
	grid-template-columns: repeat(auto-fill, 24px);
	grid-auto-rows: 24px;
	gap: 5px;
	justify-content: space-between;
	max-height: 268px;
	overflow-y: auto;

	>div {
		border: 1px solid transparent;
		cursor: pointer;

		&:hover {
			border: 1px solid #0066cc;
		}
	}
}

.closeBtn {
	position: relative;

	.btn-close {
		font-size: 12px;
		position: absolute;
		right: -15px;
		top: -15px;
		left: auto;
		border-radius: 50%;
		box-shadow: 0 0.5rem 1rem rgb(0 0 0 / 15%);
		width: 36px;
		height: 36px;
		background: transparent url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23000'%3e%3cpath d='M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z'/%3e%3c/svg%3e") (center / 1em) auto no-repeat;
		background-color: #fff;
		background-size: 0.7em;
		opacity: 1;
		z-index: 2;

		@media screen and (max-width: 768px) {
			right: -5px;
		}
	}
}

.btn-close {
	position: absolute;
	height: 0;
	width: 0;
	opacity: 0;
	left: -100vw;
	padding: 0;
	border: none;
}

.colorPopper {
	.btn-close {
		right: -30px;
		top: -30px;
	}
}

.hoverImage {
	div {
		width: 150px;
		height: 300px;
		background-size: contain;
		background-repeat: no-repeat;
		background-position: center;
	}
}

::v-deep .el-popper.is-light .el-popper__arrow::before {
	border: 1px solid #0066cc;
}

.obj-mask {
	z-index: 101;
	position: relative;
	background-color: #fff;
	margin: -30px;
	padding: 40px;

	@media screen and (max-width: 1919px) {
		margin: -15px;
		padding: 15px;
	}

	@media screen and (max-width: 1280px) and (min-width: 769px) {
		margin: 0 25px 0 0px;
		padding: 25px;
	}

	@media screen and (max-width: 768px) {
		margin: 0px;
		padding: 10px;
	}
}

.mask {
	position: fixed;
	z-index: 100;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgb(0 0 0 / 20%);
}

.noShow {
	overflow: hidden;
	width: 0;
	height: 0;
	position: absolute;
	opacity: 0;
}

::v-deep .el-select-dropdown__item.selected {
	color: #666666;
	font-size: 16px;
	font-family: Calibri;
	font-weight: 400;
}

::v-deep .el-select__popper.el-popper[role="tooltip"] {
	border-color: #d9dbdd;
	box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.2);
	max-width: none !important;
}

::v-deep .el-select-dropdown__item.hover {
	background-color: #f6f6f6;
}

::v-deep .el-select .el-input.is-focus .el-input__inner {
	border-color: #0066cc !important;
}

::v-deep .el-tabs__nav-wrap::after {
	display: none;
}

::v-deep .el-tabs__header {
	margin: 0;
}

::v-deep .el-checkbox {
	height: initial;
}

.corImg {
	position: relative;
}

.corImg::after {
	content: "";
	position: absolute;
	top: -10px;
	left: 50%;
	transform: translateX(-50%);
	background-image: url("/assets/lanyardQuote/images/1aaa.png");
	background-repeat: no-repeat;
	width: 162px;
	height: 20px;
	background-color: #fff;
}

.clickType {
	position: relative;
	background-color: #ffffff !important;
	border-top-left-radius: 10px !important;

	@media screen and (max-width: 768px) {
		// border-color: #0066cc !important;
	}

	figure figcaption {
		color: #0066cc;
	}

	&::before {
		position: absolute;
		content: url("~/assets/lanyardQuote/svg/bgh_Selected.svg");
		left: 0;
		top: 0;
		width: 50px;
		height: 50px;
		z-index: 1;
	}

	&::after {
		position: absolute;
		content: url("~/assets/lanyardQuote/svg/Selected.svg");
		left: 8px;
		top: 5px;
		width: 19px;
		height: 16px;
		z-index: 1;
	}
}

.custom-scrollbar::-webkit-scrollbar {
	/*滚动条整体样式*/
	width: 5px;

	/*高宽分别对应横竖滚动条的尺寸*/
	height: 1px;
}

.custom-scrollbar.csu1::-webkit-scrollbar {
	height: 50px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
	/*滚动条里面小方块*/
	border-radius: 10px;
	background: #d3d3d3;
}

.custom-scrollbar::-webkit-scrollbar-track {
	/*滚动条里面轨道*/
	border-radius: 10px;
}

img[src=""],
img:not([src]) {
	opacity: 0;
}

.hover-type:hover {
	border-color: #0066cc !important;
}

.eye {
	position: absolute;
	right: 15px;
	top: 15px;
	z-index: 1;
	color: #cccccc;

	@media screen and (max-width: 768px) {
		right: 5px;
		top: 5px;
	}
}

.submitInquiryPopper,
.uploadArtworkPopper,
.selectLanyardSizePopper {
	max-width: 200px;
}

.swiper-button-prev:after {
	width: 28px;
	height: 28px;
	content: "";
	background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBzdGFuZGFsb25lPSJubyI/PjwhRE9DVFlQRSBzdmcgUFVCTElDICItLy9XM0MvL0RURCBTVkcgMS4xLy9FTiIgImh0dHA6Ly93d3cudzMub3JnL0dyYXBoaWNzL1NWRy8xLjEvRFREL3N2ZzExLmR0ZCI+PHN2ZyB0PSIxNjQ3MDQ5NjY0MTA3IiBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHAtaWQ9IjM1NzMiIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PGRlZnM+PHN0eWxlIHR5cGU9InRleHQvY3NzIj48L3N0eWxlPjwvZGVmcz48cGF0aCBkPSJNNTEyIDEwMjRBNTEyIDUxMiAwIDEgMCAwIDUxMmE1MTIgNTEyIDAgMCAwIDUxMiA1MTJ6IiBmaWxsPSIjQjhCQUJDIiBwLWlkPSIzNTc0Ij48L3BhdGg+PHBhdGggZD0iTTM1NS40NzQyODYgNTM1LjA0TDMyOS4xNDI4NTcgNTEybDI2OS41MzE0MjktMjQ2LjQ5MTQyOWEzNi41NzE0MjkgMzYuNTcxNDI5IDAgMCAxIDQ5LjM3MTQyOCAwIDI5LjYyMjg1NyAyOS42MjI4NTcgMCAwIDEgMCA0NC42MTcxNDNsLTIxOS40Mjg1NzEgMjAxLjUwODU3MiAyMTkuNDI4NTcxIDIwMS41MDg1NzFhMjkuOTg4NTcxIDI5Ljk4ODU3MSAwIDAgMSAwIDQ0Ljk4Mjg1NyAzNi41NzE0MjkgMzYuNTcxNDI5IDAgMCAxLTQ5LjM3MTQyOCAwbC0yNDMuNTY1NzE1LTIyMy4wODU3MTR6IiBmaWxsPSIjRkZGRkZGIiBwLWlkPSIzNTc1Ij48L3BhdGg+PC9zdmc+);
	background-repeat: no-repeat;
	background-size: contain;
	border-radius: 50%;
}

.swiper-button-next:after {
	width: 28px;
	height: 28px;
	content: "";
	background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBzdGFuZGFsb25lPSJubyI/PjwhRE9DVFlQRSBzdmcgUFVCTElDICItLy9XM0MvL0RURCBTVkcgMS4xLy9FTiIgImh0dHA6Ly93d3cudzMub3JnL0dyYXBoaWNzL1NWRy8xLjEvRFREL3N2ZzExLmR0ZCI+PHN2ZyB0PSIxNjQ3MDQ5NjU4MDU4IiBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHAtaWQ9IjM0NDIiIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PGRlZnM+PHN0eWxlIHR5cGU9InRleHQvY3NzIj48L3N0eWxlPjwvZGVmcz48cGF0aCBkPSJNNTEyIDEwMjRhNTEyIDUxMiAwIDEgMSA1MTItNTEyIDUxMiA1MTIgMCAwIDEtNTEyIDUxMnoiIGZpbGw9IiNCOEJBQkMiIHAtaWQ9IjM0NDMiPjwvcGF0aD48cGF0aCBkPSJNNjY4LjUyNTcxNCA1MzUuMDRMNjk0Ljg1NzE0MyA1MTJsLTI2OS41MzE0MjktMjQ2LjQ5MTQyOWEzNi41NzE0MjkgMzYuNTcxNDI5IDAgMCAwLTQ5LjM3MTQyOCAwIDI5LjYyMjg1NyAyOS42MjI4NTcgMCAwIDAgMCA0NC42MTcxNDNsMjE5LjQyODU3MSAyMDEuNTA4NTcyLTIxOS40Mjg1NzEgMjAxLjUwODU3MWEyOS45ODg1NzEgMjkuOTg4NTcxIDAgMCAwIDAgNDQuOTgyODU3IDM2LjU3MTQyOSAzNi41NzE0MjkgMCAwIDAgNDkuMzcxNDI4IDBsMjQzLjU2NTcxNS0yMjMuMDg1NzE0eiIgZmlsbD0iI0ZGRkZGRiIgcC1pZD0iMzQ0NCI+PC9wYXRoPjwvc3ZnPg==);
	background-repeat: no-repeat;
	background-size: contain;
	border-radius: 50%;
}

.el-button:active {
	border-color: transparent;
}

.selected-border {
	.el-input__inner {
		border-color: #0066cc !important;
	}

	.el-textarea__inner {
		border-color: #0066cc !important;
	}
}

.selected-border2 {
	border: 1px solid #0066cc !important;
}

.selected-border.inputStyleLike,
.selected-border.el-button,
.selected-border.item {
	border: 1px solid #0066cc !important;
}

.mySwiper2 {
	height: 80%;
	width: 100%;

	img {
		display: block;
		width: 100%;
		height: 100%;
		object-fit: cover;
	}
}

.mySwiper {
	height: 20%;
	box-sizing: border-box;

	.swiper-slide {
		width: 25%;
		height: 100%;

		img {
			display: block;
			width: 100%;
			height: 100%;
			object-fit: cover;
		}
	}
}

.max-height {
	max-height: 100% !important;
}

.border-red {
	border-color: #de3500 !important;

	.el-input__inner {
		border-color: #de3500 !important;
	}
}

.el-select-dropdown__item,
.el-select-dropdown__item.selected {
	@media screen and (max-width: 768px) {
		font-size: 12px;
		font-family: Arial;
		font-weight: 400;
		color: #666666;
	}
}

.el-drawer__header {
	padding-top: 5px;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
	-webkit-appearance: none;
}

input[type="number"] {
	-moz-appearance: textfield;
}

.priceDialog,
.swiperDialog {
	border-radius: 10px;

	.el-dialog__header {
		padding: 0;
	}

	.price-info {
		display: grid;
		grid-template-columns: 1fr 1fr 20px;
		grid-auto-rows: auto;
		border-bottom: 1px solid #d9d9d9;
		padding: 14px 0 14px 10px;
		row-gap: 15px;
		column-gap: 5px;
		margin-bottom: 20px;

		@media screen and (max-width: 768px) {
			grid-template-columns: 1fr 1fr;
		}

		>div {
			font-size: 16px;
			font-weight: 400;
			color: #666666;

			@media screen and (max-width: 768px) {
				font-size: 12px;
				font-family: Arial;
				color: #333333;
				text-align: right;
			}
		}

		>div:nth-child(2n-1) {
			grid-column: 1/2;
		}

		>div:nth-child(2n) {
			grid-column: 2/4;
		}

		.title {
			color: #222222;
			margin-bottom: 0;
			text-align: left;

			@media screen and (max-width: 768px) {
				font-size: 12px;
				font-family: Arial;
				color: #555555;
			}
		}
	}

	.subtotal {
		display: flex;
		padding: 10px;
		padding-bottom: 0;

		@media screen and (max-width: 768px) {
			padding: 15px;
		}

		>div {
			display: flex;
			align-items: flex-end;
		}

		>div:nth-child(1) {
			font-size: 16px;
			font-weight: 400;
			color: #222222;
			display: flex;
			align-items: flex-end;
			padding-bottom: 5px;

			@media screen and (max-width: 768px) {
				font-size: 12px;
				font-family: Arial;
				padding-bottom: 2px;
			}
		}

		>div:nth-child(2) {
			font-size: 30px;
			color: #de3500;
			font-weight: bold;
			padding: 0 15px;

			@media screen and (max-width: 768px) {
				font-size: 21px;
				font-family: Arial;
				display: flex;
				align-items: flex-end;
			}
		}

		>div:nth-child(3) {
			.el-select {
				width: 70px;
				height: 36px;

				@media screen and (max-width: 768px) {
					width: 65px;
					height: 30px;
				}

				>div {
					width: 100%;
					height: 100%;

					.el-input {
						width: 100%;
						height: 100%;

						.el-input__inner {
							font-size: 15px;
							font-family: Roboto;
							font-weight: 400;
							color: #333333;
							padding-right: 25px;

							@media screen and (max-width: 768px) {
								font-size: 12px;
								font-family: Arial;
							}
						}
					}
				}
			}
		}
	}

	.descriptionUl {
		margin: 0;
		padding-bottom: 20px;
		padding-left: 40px;

		@media screen and (max-width: 768px) {
			padding-left: 25px;
			padding-bottom: 10px;
		}

		li {
			font-size: 18px;
			font-family: Calibri;
			font-weight: 400;
			color: #333333;
			list-style-type: disc;

			@media screen and (max-width: 768px) {
				font-size: 12px;
				font-family: Arial;
			}
		}
	}
}

.priceDialog {
	--el-dialog-margin-top: 5vh;
}

.swiperDialog {
	.el-dialog__body {
		padding: 0;
	}
}

.myInput {
	-webkit-appearance: none;
	background-color: var(--el-input-bg-color, var(--el-color-white));
	background-image: none;
	border-radius: var(--el-input-border-radius, var(--el-border-radius-base)) !important;
	border: 1px solid rgb(220, 223, 230);
	box-sizing: border-box;
	color: var(--el-input-text-color, var(--el-text-color-regular));
	display: inline-block;
	font-size: 16px;
	height: 30px;
	line-height: 30px;
	outline: 0;
	padding: 0 11px;
	transition: var(--el-transition-border);
	width: 100%;
	font-family: Calibri;

	@media screen and (max-width: 768px) {
		height: 30px;
		line-height: 30px;
		font-size: 12px;
	}
}

.shadowBG1 {
	position: relative;
	opacity: 0.8;

	&::after {
		position: absolute;
		content: "";
		left: 0;
		top: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.3);
	}
}

.top_fix {
	font-family: Helvetica, Arial, Helvetica, sans-serif;

	@media screen and (min-width: 767px) {
		font-family: "Calibri", "Graphik Webfont", -apple-system, BlinkMacSystemFont, "Roboto", "Droid Sans", "Segoe UI", "Helvetica", Arial, sans-serif;
	}
}

.el-dialog__body {
	@media screen and (max-width: 768px) {
		padding: 0;
	}
}

@keyframes translateImage {
	0% {
		transform: rotateY(0deg);
	}

	50% {
		transform: rotateY(90deg);
	}

	100% {
		transform: rotateY(180deg);
	}
}

@keyframes zIndexChange1 {
	0% {
		z-index: 2;
		opacity: 1;
	}

	50% {
		z-index: 2;
		opacity: 1;
	}

	100% {
		z-index: 1;
		opacity: 0;
	}
}

@keyframes zIndexChange2 {
	0% {
		z-index: 1;
		opacity: 0;
	}

	50% {
		z-index: 1;
		opacity: 0;
	}

	100% {
		z-index: 2;
		opacity: 1;
	}
}

.rotateYAnimation {
	>div:nth-child(1) {
		position: relative;

		.el-image {
			img {
				backface-visibility: hidden;
			}
		}

		.el-image:nth-child(1) {
			z-index: 1;
			opacity: 1;
		}

		.el-image:nth-child(2) {
			position: absolute;
			left: 0;
			top: 0;
			right: 0;
			bottom: 0;
			opacity: 0;

			img {
				transform: translateY(180deg);
			}
		}
	}

	&:hover {
		>div:nth-child(1) {
			animation-name: translateImage;
			animation-timing-function: linear;
			animation-duration: 0.5s;
			animation-fill-mode: forwards;

			.el-image:nth-child(1) {
				animation-name: zIndexChange1;
				animation-timing-function: linear;
				animation-duration: 0.5s;
				animation-fill-mode: forwards;
			}

			.el-image:nth-child(2) {
				animation-name: zIndexChange2;
				animation-timing-function: linear;
				animation-duration: 0.5s;
				animation-fill-mode: forwards;
			}
		}
	}
}

.titleCard {
	position: absolute;
	left: 11px;
	top: 11px;
	background-color: #12c1e6;
	border-radius: 10px 0px 8px 0px;
	z-index: 1;
	font-size: 16px;
	font-family: Adobe Heiti Std;
	font-weight: normal;
	color: #ffffff;
	padding: 5px 10px;

	@media screen and (max-width: 768px) {
		font-size: 12px;
		font-family: Arial;
	}
}

.customPopover {
	font-size: 18px !important;
	font-family: Calibri;

	.subtotal>div:nth-child(2) {
		color: #de3500;
	}

	@media screen and (max-width: 768px) {
		font-size: 12px !important;
		font-family: Arial;
	}
}

.el-message {
	* {
		font-size: 20px !important;
	}
}

.afterBottomWhite {
	position: relative;
}

.afterBottomWhite::after {
	position: absolute;
	content: "";
	bottom: -1px;
	right: -11px;
	background-color: #f0f1f2;
	width: 10px;
	height: 1px;
}

.freeTitle {
	position: absolute;
	font-size: 39px;
	left: -2px;
	top: -2px;
	z-index: 1;
}

.title1 {
	display: flex;
	align-items: center;
	margin-bottom: 6.5px;
	margin-left: 5px;
	border-radius: 50%;
	border: 1px solid black;
	width: 8px;
	padding: 2px;
	height: 8px;
	font-size: 12px;
	justify-content: center;
	font-family: Calibri;
	cursor: pointer;
}