import { initializeAxios } from "@/utils/request";
const https = require('https');

// 创建可复用的 HTTPS Agent
const httpsAgent = new https.Agent({
	keepAlive: true,
	maxSockets: 20
});

// 常量定义
const TOKEN_KEY = process.env.tokenKey;
const ERROR_WHITELIST = [
	"/app/cart/changeCartQuantity",
	"/app/designerShoppingCart/addShoppingCartNotLogin",
	"/app/designerShoppingCart/addShoppingCart",
	"/aicenter/neonsign/previewImg"
];

export default ({ req, res, store, app, redirect, $toast }, inject) => {
	// 创建axios实例
	const http = app.$axios.create({
		baseURL: process.env.baseUrl,
		timeout: 1000 * 300, // 5分钟超时
		httpsAgent,
		headers: {
			'Content-Type': 'application/json;charset=utf-8'
		}
	});

	// request拦截器
	http.onRequest(
		async (config) => {
			let manageLang;
			if (process.browser) {
				document.body.style.cursor = 'progress';
				config = await handleBrowserSpecificConfig(config, store, app);
			}

			// 添加token
			const token = app.$cookies.get(TOKEN_KEY);
			if (token) {
				config.headers.token = token;
			}

			// 处理请求数据
			config = normalizeRequestData(config, store, app);

			return config;
		},
		(error) => {
			return Promise.reject(error);
		}
	);

	// 响应拦截器
	http.onResponse(
		(response) => {
			if (process.client) {
				setTimeout(() => (document.body.style.cursor = ''), 100);
			}

			// 白名单直接返回
			if (ERROR_WHITELIST.includes(response.config.url)) {
				return response.data || response;
			}

			// 处理业务逻辑
			return handleBusinessResponse(response, { app, store, redirect, $toast });
		}
	);
	inject('http',http)
	// 基本配置
	initializeAxios(http);
};

// 辅助函数: 处理浏览器特定配置
async function handleBrowserSpecificConfig(config, store, app) {
	if (process.env.isManage && window.location.href.includes('/buildWeb')) {
		const langParam = getLanguageParam(window.location);
		config.headers['Accept-Language'] = langParam || 'en-us';
	}
	return config;
}

// 辅助函数: 获取语言参数
function getLanguageParam(location) {
	if (location.search.includes('language')) {
		return location.search.substr(-5);
	}
	const bwLng = JSON.parse(localStorage.getItem("bwLng") || '{}');
	return `${bwLng.language || 'en'}-${bwLng.countryCode || 'us'}`;
}

// 辅助函数: 标准化请求数据
function normalizeRequestData(config, store, app) {
	let data = config.method === 'get' ? config.params : config.data;

	if (process.client && data instanceof FormData) {
		data.append("proId", getProjectId(store, app));
		data.append("language", getLanguage(store, app));
	} else if(!data || !Array.isArray(data)) {
		data = {
			proId: getProjectId(store, app),
			language: getLanguage(store, app),
			...(config.data || {}),
			...(config.params || {})
		};

		// 参数去空
		for (let key in data) {
			if (!data[key] && data[key] !== 0 && data[key] !== false && data[key] !== '') delete data[key];
		}
	}

	if (config.method === 'get') {
		config.params = data;
	} else {
		config.data = data;
	}

	return config;
}

// 辅助函数: 获取项目ID
function getProjectId(store, app) {
	return process.env.isManage
		? store.getters['manage/getProId']
		: (store.state.proId || app.$cookies.get('site_proId'));
}

// 辅助函数: 获取语言设置
function getLanguage(store, app) {
	if (process.env.isManage && process.client) {
		return getLanguageParam(window.location);
	}
	return `${store.state.language.language || 'en'}-${store.state.language.countryCode || 'us'}`;
}

// 辅助函数: 处理业务响应
function handleBusinessResponse(response, { app, store, redirect, $toast }) {
	const { data } = response;

	if (data.code === 200) {
		return data;
	}

	// 401 未授权处理
	if (data.code === 401) {
		return handleUnauthorized(response, { app, store, redirect, $toast });
	}

	// 其他错误处理
	return handleOtherErrors(response, { app, store, $toast });
}

// 辅助函数: 处理未授权
function handleUnauthorized(response, { app, store, redirect, $toast }) {
	app.$cookies.remove(TOKEN_KEY);
	app.$cookies.remove('userInfo');

	if (process.env.isManage) {
		app.$cookies.remove(TOKEN_KEY, { domain: process.env.domain, path: '/' });
		redirect(302, process.env.redirectWebUrl);
	} else {
		store.commit('setLogin', 'login');
	}

	return Promise.reject(store.getters.lang.default.loginExpired);
}

// 辅助函数: 处理其他错误
function handleOtherErrors(response, { app, store, $toast }) {
	const { config, data } = response;
	const NO_ERROR_MESSAGE_LINKS = [
		`/app/member/refer/getUserReferPointsInfo?proId=${store.state.proId}`
	];

	console.log(config.url, config[config.method === 'get' ? 'params' : 'data'], data);

	if (process.env.isManage) {
		app.$message.error(data.message);
	} else if (process.client && !NO_ERROR_MESSAGE_LINKS.includes(config.url)) {
		$toast.error(data.message || data.error);
	}

	return Promise.reject(data);
}