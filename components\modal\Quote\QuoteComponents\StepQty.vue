<template>
	<div class="step-item-params step-qty-params" :class="{largeGap:largeGap}">
		<div class="param-item t" :class="{hideSavePrice,hideMoldPrice}" v-if="!hideMoldPrice">
			<div>{{ lang.quantity }}</div>
            <span class="totalPrice">{{ lang.subtotal }}</span>
			<span>{{ lang.unitPrice }}</span>
            <span v-show="!hideMoldPrice">{{ lang.moldPrice }}</span>
		</div>
		<div class="param-item" :class="{ active: !isCustom && selectedQtyInd === index,hideSavePrice,hideMoldPrice }" v-for="(item, index) in qtyList" :key="index" @click="selectQtyList(index)">
			<div>
				<CustomCircle :circleType="2" :isActive="!isCustom && selectedQtyInd === index"></CustomCircle>
				<span>{{ item.totalQuantity }}</span>
			</div>
            <div  class="totalPrice">
                <CCYRate :price="item.totalPrice"></CCYRate>
<!--                <span style="color: #e34048;margin-left: 4px;white-space: nowrap" v-if="index > 0 && !hideSavePrice">Save {{ item.save }}%</span>-->
            </div>
			<span class="unitPrice">/ <CCYRate :price="item.foundationUnitPrice"></CCYRate> <span class="each">{{ lang.each }}</span></span>
            <CCYRate class="moldPrice" :price="item.toolingCharge" v-show="!hideMoldPrice"></CCYRate>

		</div>
		<div class="param-item custom-qty" :class="{ active: isCustom,hideSavePrice,hideMoldPrice }" @click="selectQty">
			<div>
				<CustomCircle :circleType="2" :isActive="isCustom"></CustomCircle>
				<input type="text" :placeholder="lang.enterQty" :value="customNumber" @input="filterCustomNumber" />
			</div>
            <CCYRate class="totalPrice" :price="customNumberPrice" ></CCYRate>
			<span class="unitPrice">/ <CCYRate :price="customNumberUnitPrice"></CCYRate> {{ lang.each }}</span>
            <CCYRate class="moldPrice" :price="customNumberMoldPrice" v-show="!hideMoldPrice"></CCYRate>
		</div>
	</div>
</template>

<script>
import CustomCircle from "@/components/Quote/customCircle.vue";
import CCYRate from "@/components/CCYRate.vue";

export default {
	props: {
        hideSavePrice:{
            type: Boolean,
            default: false
        },
		qtyList: {
			type: Array,
			default: () => {
				return [];
			},
		},
		isCustom: {
			type: Boolean,
		},
		selectedQtyInd: {
			type: Number,
		},
		customNumber: {
			type: [Number, String],
		},
		customNumberUnitPrice: {
			type: Number,
		},
        customNumberMoldPrice:{
            type: Number,
        },
		customNumberPrice: {
			type: Number,
		},
        largeGap:{
            type: Boolean
        }
	},
	components: { CCYRate, CustomCircle },
	computed: {
		lang() {
			return this.$store.getters.lang.quote || {};
		},
        hideMoldPrice(){
            if(!this.qtyList.length || this.$store.state.device==="mb"){
                return true
            }
            return this.qtyList[0]?.toolingCharge == 0
        }
	},
	methods: {
		selectQtyList(index) {
			this.$emit("update:isCustom", false);
			this.$emit("update:selectedQtyInd", index);
			this.$emit("update:customNumber", "");
            this.$emit("selectQtyList");
            this.$emit("calcPrice");
		},
		selectQty() {
			this.$emit("update:isCustom", true);
			this.$emit("update:selectedQtyInd", -1);
		},
		filterCustomNumber(event) {
			this.$emit("update:customNumber", event.target.value.replace(/\D/g, ""));
            this.$emit("filterCustomNumber");
            this.$emit("calcPrice");
		},
	},
};
</script>

<style lang="scss" scoped>
.step-item-params {
	display: grid;
	gap: 0.8em;
	padding: 1em 1.3em;
	background-color: #fafafa;
	border-radius: 10px;

    &.largeGap{
        gap: 1.25em;
    }

	.t {
		font-weight: 700;
	}

	@include respond-to(mb) {
		grid-gap: 1.3em;
		padding: 0;
		background-color: transparent;
	}

	.param-item {
		display: grid;
		grid-template-columns: 1fr 1fr 1fr 1fr;
		align-items: center;
		cursor: pointer;

        &.hideMoldPrice{
            grid-template-columns: 1.3fr 1fr 1fr;
        }

        @include respond-to(mb){
            grid-template-columns: 1fr 1fr 1fr 1fr;
        }

		& > div {
			display: flex;
			align-items: center;
		}
	}
}

.custom-qty {
	input {
		width: 5.63em;
		height: 2.13em;
		background: #ffffff;
		padding: 0 0.4em;
		margin-left: -0.3em;
		border: 1px solid #d9d9d9;
		@include respond-to(mb) {
			width: 5.5em;
			height: 1.79em;
			background-color: #f5f5f5;
		}
	}
}
</style>
