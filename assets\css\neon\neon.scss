.neon {
	--neon-light-color: #5d33fd;
	font-family: Google Sans;

	::placeholder {
		color: #c3c7cf;
	}

	position: relative;
	max-height: calc(100vh - var(--headH));
	overflow-y: scroll;
	scroll-behavior: smooth;
	overflow-y: scroll !important;

	.el-textarea__inner:focus,
	.el-input__inner:focus {
		border-color: var(--neon-light-color);
	}

	.justForMB {
		display: none;
	}

	.mask {
		display: none;
		width: 100%;
		height: 100%;
		opacity: 0;

		&.show {
			display: block;
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
		}
	}

	@media screen and(max-width:768px) {
		max-height: 100%;

		&::-webkit-scrollbar {
			display: none;
		}

		.justForMB {
			display: block;
		}

		.mask.show {
			display: none;
		}
	}

	.stickyBtns {
		position: sticky;
		bottom: 0;
		right: 0;
		padding: 0;
		width: 100%;
		z-index: 1;

		.detailsBox {
			.trapezoid {
				cursor: pointer;
				padding: 0;
				height: 30px;
				left: 50%;
				position: absolute;
				top: -29px;
				width: 200px;
				background: linear-gradient(90deg, #330F64, #380f69 44%, #610E76);
				animation: none;
				display: flex;
				justify-content: center;
				align-items: center;
				color: white;
				font-size: 0.875em;
				font-weight: 400;
				border-top-right-radius: 10px;
				border-top-left-radius: 10px;
				transform: translateX(-50%) perspective(50px) rotateX(8deg);

				span {
					transform: perspective(50px) rotateX(-8deg);
				}

				.icon-Up,
				.icon-Down {
					font-size: 12px;
					margin-left: 5px;
				}
			}

			bottom: 0;
			right: 0;
			left: 0;
			background-color: #f6f6f6;
			transition: all 0.5s;
			height: 0;
			overflow: hidden;

			.detailsBtns {
				bottom: 0;
				right: 0;
				z-index: 1;
				padding: 0;

				@media screen and (max-width: 768px) {
					padding: 12.5px 9.5px;
				}
			}

			&.showDetails {
				background-color: #f2f2f2;
				height: 100vh;
				@media screen and(max-width: 768px) {
					height: 100%;
				}
				overflow: visible;
				top: 0;
				display: block;
			}
		}

		@media screen and (max-width: 768px) {
			display: block;
			position: relative;
			margin-top: 20px;
		}

		&.showDetails {
			.detailsBox {
				overflow-y: scroll;
				max-height: calc(100vh - var(--topH) - var(--headH) - 83px);

				@media screen and (max-width: 768px) {
					max-height: none;
					overflow-y: visible;
					&.isUpload {
						.mbBottomCanvas {
							display: none;
						}
					}
				}
			}
		}
	}

	.text-area {
		border-radius: 10px;
		padding: 0.9em;
		resize: none;
		box-shadow: 0 0 1px 1px #dbdbdb;
		width: 100%;
		font-size: 14px;
		height: 90px;
		font-weight: 400;
		margin-top: 100px;
	}

	.content {
		display: grid;
		grid-auto-rows: auto 1fr;
		grid-template-columns: minmax(700px, 1fr) 573px;
		position: relative;


		@media screen and(max-width: 1273px) {
			grid-template-columns: minmax(700px, 1fr) 375px;
		}

		@media screen and(max-width: 1075px) {
			grid-template-columns: 1fr 375px;
		}

		@media screen and(max-width: 768px) {
			display: flex;
			flex-direction: column;
		}

		.top {
			padding: 20px 40px;
			text-align: center;
			position: sticky;
			top: 0;

			@media screen and (max-width:768px) {
				position: unset;
			}

			.grid-box {
				display: grid;
				grid-column: 1/2;
				grid-template-columns: repeat(4, 1fr);

				@media screen and (max-width:768px) {
					grid-template-columns: repeat(2, 1fr);
				}
			}

			.custom_title {
				font-size: 2.25em;
				font-weight: bold;
				text-align: center;

				@media screen and (max-width:768px) {
					font-size: 20px;
				}
			}

			.part {
				display: flex;
				align-items: center;
				justify-content: center;
				font-size: 14px;
				color: #333333;

				.stars {
					margin-right: 10px;

					.icon-star {
						color: #eeaf19;
						font-size: 16px;
					}
				}

				.pl {
					margin-right: 5px;
					font-size: 16px;
				}
			}

			@media screen and (max-width:768px) {
				grid-column-start: 1;
				grid-column-end: 3;
				text-align: left;
				padding: 11.5px 26px;
				grid-template-columns: repeat(2, 1fr);

				.part {
					justify-content: unset;
				}
			}
		}

		.left {
			grid-column: 1/2;
			transition: all .5s ease-in-out;
			position: sticky;
			top: var(--topH);
			max-height: calc(100vh - var(--headH) - var(--topH));
			z-index: 11;

			@media screen and(max-width: 768px) {
				position: unset;
				grid-column: 1/3;
				transition: all 0.3s ease-out;
				&.isFlow {
					position: sticky;
					top: 0;
					z-index: 999;
					transition: all 0.3s ease-out;
				}
			}

			.sticky {
				text-align: center;
				height: 100%;
				position: relative;

				@media screen and (max-width: 768px) {
					height: auto
				}

				&.showMask {
					z-index: 100;
				}



				.over-tips {
					position: absolute;
					text-align: left;
					gap: 0.5em;
					left: 56%;
					max-width: 450px;
					font-weight: 400;
					background-color: #fff;
					z-index: 999;
					display: flex;
					flex-direction: column;

					.closebox {
						padding: 10px 10px 0 0;
						text-align: right;
						font-size: 14px;
						cursor: pointer;
					}

					.txtbox {
						padding: 0px 31px 30px 21px;
					}
				}



			}
		}

		.right {
			grid-column: 2/3;
			grid-row: 1/3;
			position: relative;
			padding-top: var(--topH);

			.backToDesign {
				font-size: 16px;
				position: absolute;
				right: 20px;
				top: 23px;
				cursor: pointer;

				i {
					font-weight: bold;
					color: #333638;
				}
			}

			.mbBottomCanvas {
				display: none;

				@media screen and(max-width: 768px) {
					display: block;
					position: relative;
				}
			}

			@media screen and(max-width: 768px) {
				padding-top: 0;
				grid-column: 1/3;
				padding-top: 0;

				.backToDesign {
					font-size: 16px;
					top: 90px;
					right: 10px;
				}
			}

			.right_scroll {
				height: calc(100vh - var(--headH) - 83px);
				overflow-y: scroll;
				padding-top: var(--topH);
			}

			.opts {
				width: 100%;
				padding: 0 29px;

				@media screen and(max-width: 1273px) {
					padding: 0 10px;
				}

				@media screen and (max-width: 768px) {
					padding: 0;
				}

				.tips {
					display: grid;
					grid-template-columns: 1fr auto;
					justify-content: space-between;
					align-items: center;
					gap: 10px;
					padding: 0.5em;
					width: 100%;
					font-size: 0.875em;
					background-color: #E8F7FA;
					font-weight: 400;
					color: #333333;

					p {

						@media screen and (max-width: 1273px) {
							font-size: 12px;
						}
					}

					.btn {
						width: 100px;
						height: 40px;
						background: #767CFF;
						border-radius: 20px;
						font-size: 16px;
						color: #fff;

						@media screen and (max-width:1273px) {
							font-size: 14px;
						}
					}
				}

				.hr {
					margin-top: 10px;
					margin-bottom: 20px;
					border-bottom: 1px dashed #adadad;
					@media screen and(max-width: 768px) {
						margin-bottom: 0;
					}
				}
			}

			.steps {
				padding-bottom: 20px;

				@media screen and(max-width: 1273px) {
					padding-bottom: 80px;
				}

				@media screen and(max-width: 768px) {
					padding-bottom: 22px;
				}

				&.showDetails {
					overflow: hidden;
					opacity: 0;
					padding-bottom: 0;

					@media screen and (max-width: 768px) {
						overflow: hidden;
						opacity: 1 !important;
						padding-bottom: 0;
					}
				}

				.step-header {
					padding-bottom: 39px;
				}

				.stepsBox {
					padding-inline: 29px;
					padding-block: 20px;
					background-color: white;

					&:first-child {
						padding-top: 20px;
						@media screen and(min-width: 768px) {
							padding-block: 0px !important;
							&.step-active {
								z-index: 99;
								padding-bottom: 20px !important;
							}
						}
					}
					&.step-active {
						position: relative;

						.step {
							@media screen and(max-width: 768px) {
								overflow-y: scroll;
							}

							.title-line {
								.title-pine {
									.stepTitle {
										.close {
											visibility: visible;
										}
									}
								}
							}

							&::-webkit-scrollbar {
								width: 4px;
							}

							&::-webkit-scrollbar-track {
								border-radius: 10px;
							}

							&::-webkit-scrollbar-thumb {
								background: rgb(192, 192, 192);
								border-radius: 10px;
							}

							&::-webkit-scrollbar-thumb:hover {
								background: rgb(100, 100, 100);
								border-radius: 10px;
							}

							&::-webkit-scrollbar-thumb:active {
								background: rgb(68, 68, 68);
								border-radius: 10px;
							}
						}

						@media screen and(min-width: 768px) {
							z-index: 99;
							padding-top: 20px !important;
						}

					}

					@media screen and(max-width: 1273px) {
						padding-inline: 10px;
						padding-bottom: 5px;
					}

					@media screen and(max-width: 768px) {
						padding-inline: 10px;
						padding-bottom: 10px;
					}

					.step {

						.title-line {
							padding-bottom: 21px;

							@media screen and(max-width: 768px) {
								padding-bottom: 15px;
							}
						}

						.subtitle-line {
							margin-bottom: 21px;

							&.greeting {
								margin-bottom: 10px;
							}

							&.powerAdapter {
								margin-bottom: 10px;
							}

							&.confirmation {
								margin-bottom: 10px;
							}
						}

						.NeonButton {
							.button {
								max-width: 140px;
							}
							@media screen and(max-width: 768px) {
								display: none;
							}
						}

						.enterOrUpload {
							display: grid;
							grid-template-columns: 1fr 1fr;
							padding: 0 17px 20px;
							gap: 10px;
							align-items: center;
							text-align: center;

							@media screen and(max-width: 768px) {
								padding: 0 0 20px;
							}
						}

						.small-title {
							font-size: 14px;
						}

						.chooseTemplates {
							::v-deep .NeonSwiper {
								margin-bottom: 20px;
							}

							h5 {
								font-size: 14px;
								font-weight: 400;
								color: #333333;
							}

							.fontOptionBottom {
								text-align: center;
							}

							.templateImg {
								height: 200px;
								margin-bottom: 20px;
								border-radius: 10px;

								img {
									height: 100%;
									width: 100%;
									object-fit: cover;
									border-radius: 10px;
								}
							}



							.fontOptionLine {
								span {
									font-size: 1em;
								}

								&:not(:last-child) {
									margin-bottom: 20px;
								}
							}

						}

						.color-view {
							.sampleColorView {
								font-size: 14px;
								font-weight: 400;
								margin-bottom: 14px;
								font-weight: bold;
							}

							.tips {
								font-size: 14px;
								font-weight: 400;
								text-align: center;

								@media screen and(max-width: 768px) {
									display: none;
								}
							}

							.dtips {
								font-size: 14px;
								font-weight: 400;
								text-align: center;
								margin-top: 14px;

								@media screen and(max-width: 768px) {
									display: none;
								}
							}
						}

						.greetingComments {
							margin-top: 20px;
							position: relative;

							.arrow-top {
								position: absolute;
								left: var(--offsetLeft);
								top: -6px;
								width: 13px;
								height: 13px;
								border-width: 1px;
								border-style: solid;
								border-color: var(--borderColor) transparent transparent var(--borderColor);
								transform: rotate(45deg) skew(10deg, 10deg);
								transition: .2s ease-in-out;
								background-color: #fafafa;
								z-index: 1;
							}

							.order-box {
								position: relative;

								.el-textarea {
									@media screen and(max-width: 768px) {
										font-size: 16px;
									}

									.el-textarea__inner {
										background-color: #FAFAFA;
										border: 1px solid var(--borderColor);
										border-radius: 6px;
									}
								}
							}
						}

						&.yourText {
							.uploadTips {
								color: #FF0000;
								font-size: 14px;
							}
						}


						&.designsOrUploadYourArtwork {
							.templateColorLine {
								.optionContent {
									span {
										display: block;
										margin-bottom: 10px;

										@media screen and (max-width: 768px) {
											margin-bottom: 6px;
										}
									}
								}

								.templateColor {
									padding-top: 0;
									margin-bottom: 20px;
									font-size: 14px;

									@media screen and (max-width:768px) {
										margin-bottom: 15.5px;
									}
								}

								.notes {
									font-size: 14px;
									font-weight: 400;
									color: #B2B2B2;
									font-size: 14px;
									margin-top: 9px;
								}
							}

							.specialNotes {
								.el-textarea .el-textarea__inner {
									min-height: 80px !important;
								}

								.order-box {
									position: relative;

									@media screen and(max-width: 768px) {}

									b {
										position: absolute;
										color: #999;
										display: block;
										font-size: 10px;
										left: 16.5px;
										top: 8px;
										z-index: 1;

										@media screen and (max-width:768px) {
											transform: scale(.8);
										}
									}

									.el-textarea__inner {
										margin: 0;
										font-size: 16px;

										&::placeholder {
											text-indent: 1.2em;
										}

										@media screen and(max-width: 768px) {
											font-size: 12px;
										}
									}
								}

								span {
									font-size: 14px;
								}
							}
						}

						&.artwork {}

						&.powerAdapte {
							.noimg-card {
								grid-template-columns: 1fr 1fr;

								@media screen and(max-width:768px) {
									grid-template-columns: 1fr 1fr 1fr
								}

								.card {
									font-size: 14px;
								}

								.title {
									padding-bottom: 0;
								}
							}
						}

						&.turnaroundTime {
							.NeonDateBox {
								grid-template-columns: 1fr 1fr 1fr;
							}

							.noteBox {
								margin-top: 12px;

								.el-textarea__inner {
									border-color: #DBDBDB;
									border-radius: 6px;

									@media screen and(max-width: 768px) {
										font-size: 16px;
									}
								}
							}

							.priceBox {
								margin-top: 10px;
								width: 100%;
								border-top: 1px dashed #dbdbdb;

								div {
									grid-row-gap: 10px;
									display: grid;
									font-family: Arial;
									font-size: 12px;
									grid-template-columns: repeat(2, 1fr);
									padding-top: 10px;
									position: relative;
									row-gap: 10px;

									&.final {
										.value {
											color: red;
										}
									}

									.value {
										text-align: right;
									}
								}
							}
						}

						&.confirmation {
							.noimg-card {
								grid-template-columns: 1fr 1fr;

								.card {
									justify-content: left;
								}
							}
						}

						&.sizeLikeStep {
							.sizeBottomTips {
								text-align: center;
								color: #FF0000;
								font-size: 0.875em;
								margin-top: 9px;
								margin-bottom: 13px;

								@media screen and (max-width: 768px) {
									margin-bottom: 5.5px
								}
							}

							.upload-size {
								padding: 16px 11px;
								border: 1px solid #DBDBDB;
								border-radius: 10px;

								.upload-size-title {
									font-size: 1.125em;
								}

								.size-tabs {
									margin-top: 1.125em;
								}

								.size-bottom {
									display: flex;
									justify-content: space-between;
									font-size: 0.875em;
									margin-top: 10px;

									.size-detail {
										color: #5544FF;
									}

									.size-btn {
										text-decoration: underline;
										cursor: pointer;
									}
								}
							}
						}
					}

				}
			}

			.uploadTabs {
				flex: 1;

				.NeonUpload {
					margin-bottom: 20px;

					@media screen and(max-width: 768px) {
						margin-bottom: 20px;
					}
				}

				.indoorOutdoor {
					margin-bottom: 20px;
				}

				.orderComments {
					margin: 20px 0;
					font-size: 14px;

					@media screen and(max-width: 768px) {
						font-size: 12px;
					}

					ul {
						margin-bottom: 10px;
					}

					.order-box {
						position: relative;

						@media screen and(max-width: 768px) {}

						b {
							position: absolute;
							color: #999;
							display: block;
							font-size: 10px;
							left: 16.5px;
							top: 8px;
							z-index: 1;

							@media screen and (max-width:768px) {
								transform: scale(.8);
							}
						}

						.el-textarea__inner {
							margin: 0;
							font-size: 14px;

							&::placeholder {
								text-indent: 1.2em;
							}

							@media screen and(max-width: 768px) {
								font-size: 12px;
							}
						}
					}

					.notes {
						font-weight: 400;
						color: #b2b2b2;
						margin-top: 9px;
					}
				}


			}

			::v-deep .PriceText {

				.tip-text,
				.normal-text {
					font-size: 14px;
				}
			}
		}

		.overTips {
			min-height: 34px;
			background: rgb(255, 255, 255, .8);
			border-radius: 17px;
			padding: 0 10px;
			position: absolute;
			left: 50%;
			top: 75%;
			transform: translateX(-50%);
			display: flex;
			justify-content: center;
			align-items: center;
			gap: 1em;

			@media screen and(max-width: 768px) {
				min-width: 80%;
			}

			i {
				cursor: pointer;
			}
		}

		.normalTips {
			position: absolute;
			right: 2.5%;
			top: 22%;
			min-height: 5.5em;
			display: flex;
			justify-content: center;
			align-items: center;
			text-align: center;
			border-radius: 20px;
			max-width: 20em;
			padding: 0 0.5em;
			color: #fff;
			box-shadow: -2px -2px 2px #77777785, 2px 2px 2px #77777785;
			background: #26262685;
			font-size: .8em;

			&.is-design {
				top: initial;
				bottom: 4%;
			}

			@media screen and(min-width: 768px) and (max-width: 900px) {
				&.is-design {
					bottom: 16%;
				}
			}

			@media screen and (max-width: 768px) {
				top: initial;
				bottom: 3%;
				max-width: calc(100% - 4em);
				right: 2em;
				min-height: 3.5em;
			}

		}

		.detailsBtnsForMB {
			bottom: 0;
			grid-column: 1/3;
			position: sticky;
			right: 0;
			z-index: 1;
			padding: 0;

			&.showDetails {
				display: none;
			}
		}
	}

	.preview {
		display: none;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		position: fixed;
		right: 0;
		top: 30%;
		transform: translateY(-30%);
		opacity: 0.9;
		z-index: 1000;
		border-radius: 10px 0 0 10px;
		background-color: #e8f4ff;
		cursor: pointer;
		-webkit-user-select: none;
		-moz-user-select: none;
		user-select: none;
		padding: 7.5px;
		font-size: 21px;
		background: linear-gradient(90deg,
				var(--color-primary),
				var(--btn-primary) 150%);

		@media screen and (max-width: 768px) {
			display: flex;
		}

		.previewIcon {
			color: #fff;
		}

		span {
			margin-top: 5px;
			font-size: 12px;
			color: #fff;
			font-family: Arial;
		}
	}

	::v-deep .drawDialog {
		.detailContent {
			padding-top: 0;
		}

		.el-drawer__header {
			margin-bottom: 20px;
			font-size: 16px;
			font-weight: 400;
			color: #333333;
			text-align: center;
		}
	}

	.dialogForUpload {
		.base-dialog-model-con {
			padding: 1em;
		}

		.uploadTitle {
			font-weight: bold;
		}
	}
}

.fdNeon{
	.content{
		.left{
			.sticky{
				.TextDraggable{
					.switch{
						.shopAll{
							display: none;
						}
					}
					.size-card{
						.disTitle, .part1{
							display: none;
						}
					}
				}
				.normalTips{
					display: none;
				}
			}
		}
		.right{
			.stickyBtns{
				.button-group{
					padding: 22px 30px;
					background: linear-gradient(90deg,#04103a,#380f69 44%,#990b86);
				}
			}
		}
	}
}

.previewDialog {
	.base-dialog-model-con {
		padding-bottom: 15px;

		.picWrap {
			.preview-title {
				width: fit-content;
				margin: 10px auto;
				text-align: center;
				color: #b012fb;
				display: flex;
				gap: 8px;

				.ring {
					width: 18px;
					height: 18px;
					border-radius: 50%;
					background-image: linear-gradient(90deg, #4a48ff, #b012fb);
					position: relative;

					&::before {
						content: '';
						position: absolute;
						width: 6px;
						height: 6px;
						left: 6px;
						top: 6px;
						background-color: #fff;
						border-radius: 50%;
					}
				}
			}
		}
	}

	&.sample {

		.base-dialog-model-con .picWrap .preview-title,
		.base-dialog-model-con .picWrap button {
			display: none;
		}

		img {
			width: 100%;
			margin: auto;
			object-fit: contain;
		}
	}
}

.cusToolTip {
	z-index: 9999 !important;
}

.dialogForUploadPreview .previewPicWrap {
	display: flex;
	justify-content: center;
	align-items: center;
}
