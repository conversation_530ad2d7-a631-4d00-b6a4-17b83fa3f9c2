<template>
  <v-app-bar class="mHeader" color="white" dense flat height="68px">
    <v-row>
      <v-col v-if="buildWebLoading" cols="12" md="12">
        <v-skeleton-loader type="list-item-avatar">
        </v-skeleton-loader>
      </v-col>
      <v-col v-else cols="12" md="12" style="align-items: center;display: flex;">
        <v-btn small :elevation="0" height="50px" @click="backFun">
          <v-icon dark> mdi-chevron-left </v-icon>
        </v-btn>
        <v-img class="mx-2" max-height="50" max-width="50"
          src="https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManageQuotationSystem/20220718/20220718jFNBkSFb.png"></v-img>

        <v-divider vertical class="mx-1"></v-divider>
        <v-menu offset-y>
          <template v-slot:activator="{ on, attrs }">
            <v-btn text v-bind="attrs" v-on="on">
              {{ pageInfo.layOutModel ? pageInfo.layOutModel.websiteStyleName : null
              }}<v-icon right>mdi-menu-down</v-icon>
            </v-btn>
          </template>

          <v-card max-width="330">
            <v-item-group mandatory @change="changeLayOut">
              <v-container>
                <v-row>
                  <v-col v-for="(item, index) in pageInfo.layoutList" :key="item.id" cols="12">
                    <v-item v-slot="{ active, toggle }">
                      <v-card class="d-flex align-center" @click="toggle" flat>
                        <div class="d-flex flex-no-wrap justify-space-between" @click="layOutFun(item)">
                          <v-avatar size="100" tile>
                            <v-img :src="item.previewImage"></v-img>
                          </v-avatar>
                          <div class="d-flex align-center justify-content-center">
                            <v-card-subtitle v-text="item.websiteStyleName"></v-card-subtitle>
                            <v-icon v-if="item.id == pageInfo.websiteStyleIdDraft">mdi-check</v-icon>
                          </div>
                        </div>
                      </v-card>
                    </v-item>
                  </v-col>
                </v-row>
              </v-container>
            </v-item-group>
          </v-card>
        </v-menu>
        <v-divider vertical class="mx-1"></v-divider>
        <v-menu offset-y max-height="400" content-class="custom-scrollbar" :close-on-content-click="false" v-model="menu">
          <template v-slot:activator="{ on, attrs }">
            <v-btn text max-width="300" v-bind="attrs" v-on="on">
              <p class="overP">{{ pageInfo.pageModel ? pageInfo.pageModel.pageName : null
              }}</p>
              <v-icon right>mdi-menu-down</v-icon>
            </v-btn>
          </template>
          <v-card min-width="200">
            <v-item-group mandatory>
              <v-container>
                <!-- <v-row>
                  <template v-for="(item, index) in pageInfo.allPage">
                    <v-col v-if="item.isPublish"
                      :key="item.id"
                      cols="12">
                      <v-item v-slot="{ active, toggle }"
                        :value="item.id">
                        <v-card class="d-flex align-center"
                          @click="toggle"
                          flat>
                          <div style="width: 100%"
                            @click.stop="changePage(item.id)">
                            <div style="grid-template-columns: 1fr 40px; display: grid">
                              <v-card-subtitle v-text="item.pageName"></v-card-subtitle>
                              <v-icon v-if="pageInfo.pageModel.id == item.id">mdi-check</v-icon>
                            </div>
                          </div>
                        </v-card>
                      </v-item>
                    </v-col>
                  </template>
                </v-row> -->


                <v-row>
                  <!-- <v-text-field label="input" v-model.trim="searchText"></v-text-field> -->
                  <v-text-field label="Search Name" v-model="searchText" hide-details></v-text-field>
                </v-row>
                <v-row>
                  <v-treeview :transition="true" item-text="pageName" item-key="id" :items="treeData"
                    item-children="childList" activatable @update:active="choosePage"></v-treeview>
                </v-row>

                <!-- <v-row>
                     <v-btn text @click="menu = false" >Close</v-btn>
                 </v-row> -->

              </v-container>
            </v-item-group>
          </v-card>
        </v-menu>
        <v-divider vertical class="mx-1"></v-divider>
        <v-menu offset-y max-height="400" content-class="custom-scrollbar">
          <template v-slot:activator="{ on, attrs }">
            <v-btn text v-bind="attrs" v-on="on">
              {{ computedLanguage }}
              <v-icon right>mdi-menu-down</v-icon>
            </v-btn>
          </template>

          <v-card max-width="215">
            <v-item-group mandatory>
              <v-container>
                <v-row v-cloak>
                  <v-col v-for="(item, index) in pageInfo.languageList" :key="index" cols="12">
                    <v-item v-slot="{ active, toggle }">
                      <v-card class="d-flex" @click="toggle" flat>
                        <div class="d-flex align-center flex-no-wrap justify-space-between" @click="changeLanguage(item)">
                          <v-avatar size="35" class="my-3" tile>
                            <v-img :src="item.img"></v-img>
                          </v-avatar>
                          <div class="d-flex align-center justify-content-center">
                            <v-card-subtitle>
                              <p class="font-weight-bold" style="margin-bottom:0">{{ item.country }}</p>
                              <p class="font-weight-regular" style="margin-bottom:0">{{ item.remarks }}</p>
                            </v-card-subtitle>
                            <v-icon v-if="item.id == pageInfo.websiteStyleIdDraft">mdi-check</v-icon>
                          </div>
                        </div>
                      </v-card>
                    </v-item>
                  </v-col>
                </v-row>
              </v-container>
            </v-item-group>
          </v-card>
        </v-menu>

        <v-divider vertical class="mx-1"></v-divider>
        <v-btn-toggle v-model="windowSize" tile group @change="changeWindowSize">
          <v-btn value="xl" @click="pcmbFun(0)">
            <v-icon>mdi-desktop-classic</v-icon>
          </v-btn>
          <v-btn value="md" disabled>
            <v-icon>mdi-laptop</v-icon>
          </v-btn>
          <v-btn value="xs" @click="pcmbFun(1)">
            <v-icon>mdi-cellphone</v-icon>
          </v-btn>
        </v-btn-toggle>
        <v-text-field v-model="theTitle" background-color="grey lighten-4" solo dense :hide-details="true" flat disabled
          class="rounded-lg mr-4" prepend-inner-icon="mdi-lock" @click.native="copyUrl(theTitle)"></v-text-field>
        <v-divider vertical class="mx-1"></v-divider>
        <v-btn-toggle tile group>
          <v-btn value="left" @click="undo">
            <v-icon>mdi-arrow-u-left-top</v-icon>
          </v-btn>
          <v-btn value="right" @click="redo">
            <v-icon>mdi-arrow-u-right-top</v-icon>
          </v-btn>
        </v-btn-toggle>
        <v-divider vertical class="mx-1"></v-divider>
        <v-btn-toggle tile group>
          <v-btn value="left" disabled>
            <v-icon>mdi-minus-circle-outline</v-icon>
          </v-btn>
          <v-card flat min-width="25" class="d-flex align-center"> {{ text2 }}% </v-card>
          <v-btn value="right" disabled>
            <v-icon>mdi-plus-circle-outline</v-icon>
          </v-btn>
        </v-btn-toggle>
        <v-divider vertical class="mx-1"></v-divider>
        <v-card max-width="400" min-width="400" flat class="d-flex justify-center">
          <v-btn @click="preview" value="left" min-width="160" depressed class="mx-2">
            <v-icon v-if="windowPreview == 0">mdi-eye</v-icon>
            &nbsp;{{ windowPreview == 0 ? "Preview" : "Back to Editor" }}
          </v-btn>
          <v-btn value="right" min-width="160" color="blue darken-1" dark depressed class="mx-2" @click="SAVE">
            SAVE
          </v-btn>
        </v-card>
      </v-col>
    </v-row>

  </v-app-bar>
</template>

<script>
import { editPageThemeFont } from "@/api/manage/buildWeb";

export default {
  name: "mHeader",
  data: () => ({
    theTitle: "",
    currentSite: {},
    items: ["Foo", "Bar", "Fizz", "Buzz"],
    text: "",
    text2: 100,
    windowSize: null,
    dialog: false,
    ttss: 1, //全局
    pageData: [],
    headerId: null,
    // language: null,
    computedLanguage: null,
    menu: false,

    treeData: [],
    searchText: '',
    searchResults: []
  }),
  props: {
    windowPreview: {
      type: Number,
      default: 0,
    },
    pageInfo: {
      type: Object,
      default: {},
    },
	language:{
	  type: Object,
      default: {},
	}
    // queryId: {
    //   type: Number | String,
    //   default: null,
    // },
  },
  methods: {
	// 复制链接地址
	copyUrl(value){
		navigator.clipboard.writeText(value).then(() => {
			this.$message.success("Address copied successfully")
		});
	},
    searchTree(node, targetName) {
      if (node.pageName.toLowerCase().includes(targetName)) {
        this.searchResults.push(node);
        this.treeData = this.searchResults
      }

      if (node.childList) {
        for (const child of node.childList) {
          this.searchTree(child, targetName);
        }
      }
    },
    updateSearchResults() {
      this.searchResults = [];

      const inputValue = this.searchText.toLowerCase();
      // if (inputValue.trim() !== '') {
      for (const node of this.pageInfo.allPage) {
        this.searchTree(node, inputValue);
        // }
      }
    },
    choosePage(valArr) {
      if (valArr.length == 0) return
      else if (valArr[0] > 1000000000) return
      this.menu = false
      let val = valArr[0]
      // console.log("val", val);
      this.headerId = val;
	  let languageCode
	  if(this.language && this.language.countryCode !== undefined){
        languageCode = this.language.language + '-' + this.language.countryCode
	  }else{
		languageCode = "en-us"
	  }
      this.$router.push({
        path: "/manage/siteEdit/buildWeb/",
        query: {
          id: val,
          type: 1,
		  language : languageCode
        },
      });
    },
    pcmbFun(num) {
      this.$store.commit("setManageMobile", num);
    },
    undo() {
      this.$emit("undo");
    },
    redo() {
      this.$emit("redo");
    },
    globalFun(val) {
      this.$emit("isGlobal", val);
    },
    whichPageFun(val) {
      this.$emit("witchPageForHeader", val);
    },
    changeLayOut(val) { },
    changePage(val) {
      this.headerId = val;
	  let languageCode
	  if(this.language && this.language.countryCode !== undefined){
        languageCode = this.language.language + '-' + this.language.countryCode
	  }else{
		languageCode = "en-us"
	  }
      this.$router.push({
        path: "/manage/siteEdit/buildWeb/",
        query: {
          id: val,
          type: 1,
		  language : languageCode
        },
      });
    },
    // forMat(val) {
    //   const store = localStorage.getItem("bwLng");
    //   let lngCom, tempLanguage;
    //   if (store) {
    //     const lng = JSON.parse(store);
    //     lngCom = lng.language + "-" + lng.countryCode;
    //     setTimeout(() => {
    //       tempLanguage = this.pageInfo.languageList.find((x) => {
    //         const combo =
    //           x.language + "-" + x.countryCode;
    //         return combo == lngCom;
    //       });
    //       if (tempLanguage) localStorage.setItem('bwLng', JSON.stringify(tempLanguage))
    //       this.computedLanguage = tempLanguage
    //         ? tempLanguage?.country + " " + tempLanguage.remarks
    //         : "";
    //     }, 0);
    //   } else {
    //     setTimeout(() => {
    //       tempLanguage = this.pageInfo.languageList.find((x) => {
    //         const combo = x.language + "-" + x.countryCode;
    //         return combo == val;
    //       });
    //       if (tempLanguage) localStorage.setItem('bwLng', JSON.stringify(tempLanguage))
    //       this.computedLanguage = tempLanguage
    //         ? tempLanguage?.country + " " + tempLanguage.remarks
    //         : "";
    //     }, 300);
    //   }
    // },
    changeLanguage(val) {
	//   this.$emit("update:language", val);
      localStorage.setItem("bwLng", JSON.stringify(val));
      this.$emit("changeLng", val);  // 修改language并且重新加载页面
    },
    backFun() {
      this.$emit("backFun");
    },
    editPageThemeFont() {
      let pageIdList = [];
      this.pageData.forEach((item) => {
        pageIdList.push(item.id);
      });
      let postData = {
        proId: this.$store.getters["manage/getProId"],
        pageIdList: pageIdList,
        websiteStyleIdDraft: 1,
        fontDraft: "",
        themeColorDraft: "",
        isGlobal: 1,
      };
      editPageThemeFont(postData).then((res) => {
        this.$message.success(res.message);
      });
    },
    // doneFun() {
    //   this.editPageThemeFont();
    //   this.dialog = false;
    // },
    // pageDataFun(){
    //   this.$emit('pageDataFun',this.pageData)
    // },
    // listAllPages() {
    //   let postData = {
    //     proId: 1,
    //     isPageSample: 0,
    //   };
    //   listAllPages(postData).then((res) => {
    //     this.allPage = res.data;
    //     this.pageModel = res.data[0];
    //   });
    // },
    layOutFun(item) {
      this.dialog = true;
      this.pageInfo.layOutModel = item;
      this.pageInfo.websiteStyleIdDraft = item.id;
      this.$emit("update:pageInfo", val);
      this.$emit("layOut", item.id);
      // this.pageData = [];
      // this.pageData.push(this.pageInfo.pageModel);
    },
    preview() {
      this.$emit("preview", this.windowPreview);
    },
    // listAllStyle() {
    //   listAllStyle({ isPublish: 1 }).then((res) => {
    //     this.layoutList = res.data;
    //     this.layOutModel = res.data[0];
    //   });
    // },
    SAVE() {
      this.$emit("SAVE");
    },
    changeWindowSize(val) {
      this.$emit("windowSizeChange", val);
    },
    // onResize() {
    //   let size = window.innerWidth;
    //   if (size > 1264) {
    //     this.windowSize = "xl";
    //   } else if (size > 600 && size <= 1264) {
    //     this.windowSize = "md";
    //   } else {
    //     this.windowSize = "xs";
    //   }
    // },
  },
  computed: {
    defaultLang() {
      return this.pageInfo?.pageModel?.language;
    },
    buildWebLoading() {
      return this.$store.getters.getBuildWebLoading;
    },
    // computedLanguage() {
    //   return this.language ? (this.language.country + " " + this.language.remarks) : "";
    // },
  },
  watch: {
    searchText(newText) {
      this.updateSearchResults();
      if (newText == "") {
        this.treeData = this.pageInfo.allPage;
      }
    },
	pageInfo: {
		async handler (val) {
			this.treeData = val.allPage;
			if (val && val.pageModel && val.pageModel.routingName) {
				// if (this.defaultLang != val.pageModel.language) this.forMat(val.pageModel.language);
				this.theTitle = val.pageModel.routingName == '/'?this.currentSite.url:(this.currentSite.url + val.pageModel.routingName)
			}
		},
		immediate: true,
		deep: true,
	},
	language: {
		handler (val) {
			if (val && val.country) this.computedLanguage = val ? (val.country + " " + val?.remarks) : "";
		},
		immediate: true,
		deep: true,
	},
    // defaultLang: {
    //   handler(val) {
    //     this.forMat(val);
    //   },
    // },
    // selectedValue1(newVal){
    //   let find =this.pageInfo?.allPage.find(i=>i.id == newVal)
    //   if (find && find.childList) {
    //     this.option2 = find.childList
    //     this.disOption2 = false
    //   }
    // },
    // selectedValue2(newVal){
    //   let find1 = this.pageInfo?.allPage.find(i=>i.id == this.selectedValue1)
    //   let find2 =  find1?.childList
    //   if(!find2) return
    //   let find3 = find2.find(i=>i.pageName == newVal)
    //   if (find3 && find3.childList) {
    //     this.option3 = find3.childList
    //     this.disOption3 = false
    //   }
    // },
  },
  mounted() {
    // this.onResize();
	// this.$nextTick(()=>{
	// 	this.language = JSON.parse(localStorage.getItem("bwLng"))||{};
	// })
    let temp = this.$store.getters.getManageMobile;
    if (temp == "0") {
      this.windowSize = "xl";
    } else if (temp == "1") {
      this.windowSize = "xs";
    }
    // window.addEventListener("resize", this.onResize, { passive: true });
  },
  created() {
    this.currentSite = this.$store.state.manage.currentSite;
  },
  beforeDestroy() {
    if (typeof window === "undefined") return;
    // window.removeEventListener("resize", this.onResize, { passive: true });
  },
};
</script>
<style scoped lang="scss">
.mHeader {
  ::v-deep .v-toolbar__content {
    border-bottom: 1px solid #d9d9d9;
  }

  ::v-deep .overP {
    min-width: 80px;
    max-width: 50%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>
