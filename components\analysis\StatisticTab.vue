<template>
	<div id="StatisticTab">
		<v-tabs v-model="tab" background-color="#F5F6FA" color="#333333 " @change="tabChange">
		 <v-tabs-slider color="transparent"></v-tabs-slider>
			<v-tab v-for="item in statistics" :key="item">
				{{ item }}
			</v-tab>
		</v-tabs>

		<!-- <v-tabs-items v-model="tab">
			<v-tab-item v-for="item in content" :key="item">
				<div class="echart">{{ item }}</div>
			</v-tab-item>
		</v-tabs-items> -->
	</div>
</template>

<script>
export default {
	name: "StatisticTab",
	data() {
		return {
			tab: null,
			statistics: ["Inquiries Statistics", "Orders Statistics", "Revenue Statistics", "Customer Statistics"],
			content: [1, 2, 3, 4],
		};
	},
	methods: {
		tabChange(e) {
			// console.log(e);
			// this.$emit("tabChange", e);
		},
	},
};
</script>

<style lang="scss" scoped>
.v-tabs {
	 width:  40.675vw;;
	height: 1.8229vw;
	border-radius: 0.3125vw;
	text-transform: none;
}
.v-tab {
	/* width: 165px; */
	height: 1.8229vw;
	font-size: .8333vw;
	height: 2.1875vw;
		text-transform: none;
}
.v-tab--active {
	background-color: #fff;
	font-weight: bold;
	font-size: .8333vw;
	border-top-right-radius: .3125vw;
	border-top-left-radius: .3125vw;
}
.v-tabs-items {
	height: 100%;
}
.v-tabs-items {
	height: 23.9583vw;
}
</style>
