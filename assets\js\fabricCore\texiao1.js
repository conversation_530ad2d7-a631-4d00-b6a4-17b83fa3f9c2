//模切
const shadeScale = 0.9;
export function dieCut(workspace, scale) {
	let w = workspace.width;
	let wrap, xian, wrap_w, wrap_h, xian_w, xian_h;
	if (scale >= 1) {
		wrap_w = w * shadeScale;
		wrap_h = (w * shadeScale) / scale;
		xian_w = w * shadeScale - 20;
		xian_h = (w * shadeScale) / scale - 20;
	} else {
		wrap_w = w * shadeScale * scale;
		wrap_h = w * shadeScale;
		xian_w = w * shadeScale * scale - 20;
		xian_h = w * shadeScale - 20;
	}

	wrap = new fabric.Rect({
		width: wrap_w,
		height: wrap_h,
		fill: "#fff",
		shadow: "rgba(0,0,0,0.3) 0px 0px 10px",
	});

	xian = new fabric.Rect({
		left: 10,
		top: 10,
		width: xian_w,
		height: xian_h,
		fill: "transparent",
		stroke: "#ccc",
		strokeDashArray: [10],
		strokeWidth: 2,
		strokeUniform: true,
	});

	let groupArr = [wrap];
	let group = new fabric.Group(groupArr, {
		id: "editArea",
		perPixelTargetFind: true,
		selectable: false,
		evented: false,
		isShade: true,
		excludeFromExport: true, //不导出到序列化
	});
	return {
		group,
	};
}

//椭圆
export function ellipseTeXiao(workspace, scale, bgColor, p) {
	let w = workspace.width;
	let wrap, xian, wrap_rx, wrap_ry, xian_rx, xian_ry;
	console.log("我是椭圆", p, p.source);
	if (scale >= 1) {
		wrap_rx = (w * shadeScale) / 2;
		wrap_ry = (w * shadeScale) / 2 / scale;
		xian_rx = (w * shadeScale - 20) / 2;
		xian_ry = (w * shadeScale - 20) / 2 / scale;
	} else {
		wrap_rx = ((w * shadeScale) / 2) * scale;
		wrap_ry = (w * shadeScale) / 2;
		xian_rx = ((w * shadeScale - 20) / 2) * scale;
		xian_ry = (w * shadeScale - 20) / 2;
	}

	wrap = new fabric.Ellipse({
		rx: wrap_rx,
		ry: wrap_ry,
		fill: p || "#fff",
		shadow: "rgba(255,255,255,0.3) 0px 0px 10px",
	});

	/* 	xian = new fabric.Ellipse({
		left: wrap_rx - xian_rx,
		top: wrap_ry - xian_ry,
		rx: xian_rx,
		ry: xian_ry,
		fill: p,
		stroke: "#ccc",
		strokeDashArray: [10],
		strokeWidth: 2,
		strokeUniform: true,
	}); */
	let groupArr = [wrap]; //xian
	let group = new fabric.Group(groupArr, {
		id: "editArea",
		perPixelTargetFind: true,
		selectable: false,
		evented: false,
		isShade: true,
		excludeFromExport: true, //不导出到序列化
	});
	return {
		group,
	};
}

//圆
export function roundTeXiao(workspace, scale, bgColor, p) {
	console.log("进来了吗", p, scale);
	let w = workspace.width;
	let wrap, xian, wrap_rx, wrap_ry, xian_rx, xian_ry;
	wrap_rx = ((w * 0.5) / 2) * scale;
	wrap_ry = wrap_rx; //(w * shadeScale) / 2;
	xian_rx = ((w * 0.5 - 20) / 2) * scale;
	xian_ry = xian_rx; //(w * shadeScale - 20) / 2;

	wrap = new fabric.Ellipse({
		rx: wrap_rx,
		ry: wrap_ry,
		fill: p,
		shadow: "rgba(255,255,255,0.3) 0px 0px 10px",
	});

	/* 	xian = new fabric.Ellipse({
		left: wrap_rx - xian_rx,
		top: wrap_ry - xian_ry,
		rx: xian_rx,
		ry: xian_ry,
		fill: p || "#fff",
		stroke: "#ccc",
		strokeDashArray: [10],
		strokeWidth: 2,
		strokeUniform: true,
	}); */
	let groupArr = [wrap]; //xian
	let group = new fabric.Group(groupArr, {
		id: "editArea",
		perPixelTargetFind: true,
		selectable: false,
		evented: false,
		isShade: true,
		excludeFromExport: true, //不导出到序列化
	});
	return {
		group,
	};
}

//矩形
export function rectTeXiao(workspace, scale) {
	let w = workspace.width;
	let wrap, xian, wrap_w, wrap_h, xian_w, xian_h;
	if (scale >= 1) {
		wrap_w = w * shadeScale;
		wrap_h = (w * shadeScale) / scale;
		xian_w = w * shadeScale - 20;
		xian_h = (w * shadeScale) / scale - 20;
	} else {
		wrap_w = w * shadeScale * scale;
		wrap_h = w * shadeScale;
		xian_w = w * shadeScale * scale - 20;
		xian_h = w * shadeScale - 20;
	}

	wrap = new fabric.Rect({
		width: wrap_w,
		height: wrap_h,
		fill: "#ffffff",
		shadow: "rgba(0,0,0,0.3) 0px 0px 10px",
	});

	xian = new fabric.Rect({
		left: (wrap_w - xian_w) / 2,
		top: (wrap_h - xian_h) / 2,
		width: xian_w,
		height: xian_h,
		fill: "transparent",
		stroke: "#ccc",
		strokeDashArray: [10],
		strokeWidth: 2,
		strokeUniform: true,
	});

	let groupArr = [wrap, xian];
	let group = new fabric.Group(groupArr, {
		id: "editArea",
		perPixelTargetFind: true,
		selectable: false,
		evented: false,
		isShade: true,
		excludeFromExport: true, //不导出到序列化
	});
	return {
		group,
	};
}

//圆角正方形
export function roundedSquareTeXiao(workspace, scale, bgColor, p) {
	console.log("3", workspace, scale, bgColor, p);
	let w = workspace.width; //* scale; //;
	let wrap,
		xian,
		wrap_w,
		wrap_h,
		xian_w,
		xian_h,
		r = w / 10;
	wrap_w = w * 0.5 * scale;
	wrap_h = wrap_w;
	xian_w = w * 0.5 * scale - 20;
	xian_h = w * 0.5 * scale - 20;

	wrap = new fabric.Rect({
		width: wrap_w,
		height: wrap_h,
		fill: p,
		rx: r, //圆角半径
		ry: r, //圆角半径
		shadow: "rgba(255,255,255,0.3) 0px 0px 10px",
	});

	/* 	xian = new fabric.Rect({
		left: (wrap_w - xian_w) / 2,
		top: (wrap_h - xian_h) / 2,
		width: xian_w,
		height: xian_h,
		fill: "transparent",
		stroke: "#ccc",
		strokeDashArray: [10],
		strokeWidth: 2,
		strokeUniform: true,
		rx: r, //圆角半径
		ry: r, //圆角半径
	}); */

	let groupArr = [wrap]; //xian
	let group = new fabric.Group(groupArr, {
		id: "editArea",
		perPixelTargetFind: true,
		selectable: false,
		evented: false,
		isShade: true,
		excludeFromExport: true, //不导出到序列化
	});
	return {
		group,
	};
}

//圆角矩形
export function roundedRectTeXiao(workspace, scale, bgColor, p) {
	/* 	console.log("11111", workspace, scale, p.source, bgColor); */
	let w = workspace.width;
	let wrap,
		xian,
		wrap_w,
		wrap_h,
		xian_w,
		xian_h,
		r = w / 10;
	if (scale >= 1) {
		wrap_w = w * shadeScale;
		wrap_h = (w * shadeScale) / scale;
		xian_w = w * shadeScale - 20;
		xian_h = (w * shadeScale) / scale - 20;
	} else {
		wrap_w = w * shadeScale * scale;
		wrap_h = w * shadeScale;
		xian_w = w * shadeScale * scale - 20;
		xian_h = w * shadeScale - 20;
	}
	wrap = new fabric.Rect({
		width: wrap_w,
		height: wrap_h,
		fill: p,
		//fill: "#E4B38A",
		rx: r, //圆角半径
		ry: r, //圆角半径
		//shadow: "rgba(255,255,255,1) 0px 0px 10px",
	});

	/* 	xian = new fabric.Rect({
		left: (wrap_w - xian_w) / 2,
		top: (wrap_h - xian_h) / 2,
		width: xian_w,
		height: xian_h,
		fill: "transparent",
		stroke: "#ccc",
		strokeDashArray: [10],
		strokeWidth: 2,
		strokeUniform: true,
		rx: r, //圆角半径
		ry: r, //圆角半径
	}); */

	let groupArr = [wrap]; //xian
	let group = new fabric.Group(groupArr, {
		id: "editArea",
		perPixelTargetFind: true,
		selectable: false,
		evented: false,
		isShade: true,
		excludeFromExport: true, //不导出到序列化
	});

	return {
		group,
	};
}

//自定义--方块平铺
export function cust(workspace, scale, bgColor, p1, p) {
	let w = workspace.width;
	let wrap,
		xian,
		wrap_w,
		wrap_h,
		xian_w,
		xian_h,
		r = w / 10;
	if (scale >= 1) {
		wrap_w = w * shadeScale;
		wrap_h = (w * shadeScale) / scale;
		xian_w = w * shadeScale; //- 20;
		xian_h = (w * shadeScale) / scale; // - 20;
	} else {
		wrap_w = w * shadeScale * scale;
		wrap_h = w * shadeScale;
		xian_w = w * shadeScale * scale; // - 20;
		xian_h = w * shadeScale; // - 20;
	}

	wrap = new fabric.Rect({
		width: wrap_w,
		height: wrap_h,
		fill: p,
		shadow: "rgba(255,255,255,0.3) 0px 0px 10px",
	});

	xian = new fabric.Rect({
		left: (wrap_w - xian_w) / 2,
		top: (wrap_h - xian_h) / 2,
		width: xian_w,
		height: xian_h,
		fill: p1 || "transparent",
		stroke: "#ccc",
	});

	let groupArr = [wrap, xian];
	let group = new fabric.Group(groupArr, {
		id: "editArea",
		perPixelTargetFind: true,
		selectable: false,
		evented: false,
		isShade: true,
		excludeFromExport: true, //不导出到序列化
	});

	return {
		group,
	};
}
