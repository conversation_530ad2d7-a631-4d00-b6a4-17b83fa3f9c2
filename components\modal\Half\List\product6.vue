<template>
  <div class="good-item">
    <div class="adBox" v-if="productData.isEnable" @click="goAdLink(productData.advertLink)"
      :style="{ height: adBoxHeight }">
      <div class="adVideo" v-if="productData.type == 1">
        <div style="width: 100%; height: 100%">
          <video width="100%" height="100%" id="video" autoplay="autoplay" muted="muted" loop="loop"
            webkit-playsinline="true" playsinline="true" x5-video-player-type="h5-page"
            x5-video-orientation="landscape|portrait">
            <source :src="isMobile ? productData.mbImg : productData.pcImg" />
          </video>
        </div>
      </div>
      <div class="adImg" v-else>
        <pic width="100%" height="100%" contain :src="isMobile ? productData.mbImg : productData.pcImg"
          :alt="productData.content" :title="productData.title" />
      </div>
    </div>
    <div style="position: relative; width: 100%; height: 100%" v-else>
      <productHead style="height: 100%;" :isDiv="isDiv" :productData="productData" @toDetail="toDetail">
        <div class="goods" ref="goods" :class="`goods${productData.id}`" data-newImgUrl=""
          :data-imgurl="getSrc2(productData)" :data-imgid="productData.id" :data-options="getImgOptions(productData)"
          :data-canvasData="canvasData">
          <div class="good-img">
            <div class="imgWrap">
              <pic width="100%" height="100%" :class="`picImg${productData.id}`" :src="getSrc(productData)"
                :alt="productData.name" :title="productData.name" />
              <div class="priceBtn" v-show="productData.discount">
                <div class="priceBtnText">
                  <span>{{ langSemiCustom.save }}</span>
                  {{ (productData.discount * 100).toFixed(0) + " %" }}
                </div>
              </div>
            </div>
          </div>
          <div class="good-back">
            <div class="good-color"
              v-if="productData.productParamList && productData.productParamList.length && productData.isDevise != 1">
              <div class="colorBox">
                <div class="good-color-item" v-show="productData.sceneImg"
                  :class="{ active: productData.selectedColorIndex === -1 }" title="Gallery"
                  @click.stop="toSceneImg($event, productData)">
                  <span class="color-circle"></span>
                </div>
                <div class="good-color-item" :class="{ active: productData.selectedColorIndex === cindex }"
                  @click="selectGoodColor($event, itemIndex, cindex, citem)" v-show="cindex < showColorNum"
                  :title="citem.colorAlias" v-for="(citem, cindex) in productData.productParamList" :key="cindex">
                  <span class="color-circle"
                    :style="{ background: citem.colorSecondary ? `linear-gradient(-45deg, ${citem.colorCode} 50%, ${citem.colorSecondary} 50%)` : citem.colorCode }"></span>
                </div>
                <span v-if="productData.productParamList && productData.productParamList.length - showColorNum > 0">+{{
                  productData.productParamList.length - showColorNum }}</span>
              </div>
              <div v-if="productData.isDevise != 1" @click.stop class="pcItemNo">
                {{ productData.productSku }}
              </div>
            </div>
            <div class="good-info">
              <div class="productTitle clearfix">
                <h3 :data-name="productData.name" :title="productData.name">
                  {{ productData.name }}
                </h3>
              </div>
              <div class="priceBox">
                <div class="discountPrice">
                  <div class="nowPrice">
                    <span class="priceText">Low As</span>
                    <label>
                      <CCYRate :price="productData.lowestDiscountPrice"></CCYRate>
                    </label>
                  </div>
                </div>
                <div class="xin" style="flex-shrink: 0;">
                  <template v-if="productData.commentLevel > 0">
                    <v-rating :value="productData.commentLevel" background-color="#cccccc" size="18" color="#EB7100"
                      half-increments readonly dense length="5"></v-rating>
                    <span class="xinCommentLevel" v-if="productData.commentLevel">{{ productData.commentLevel }}</span>
                    <span class="xinCommentNum" v-if="productData.commentNum" style="color: rgb(182, 177, 177)"> ({{
                      productData.commentNum }}) </span>
                  </template>
                </div>
              </div>
              <div class="productTag" v-show="tagList.length > 0">
                <div class="tag" v-for="(item, index) in tagList" :key="index">{{ item }}</div>
              </div>
              <div class="minQuantity">
                {{ langSemiCustom.MiniQty + " " + productData.lowestPurchaseQuantity }}
              </div>
            </div>
            <div class="good-collection" @click.stop="goCollection($event, productData)">
              <v-icon class="collectionXin" v-if="!productData.isCollection">mdi-heart-outline</v-icon>
              <v-icon class="collectionXin isActive" v-else>mdi-heart</v-icon>
            </div>
          </div>
          <div class="frameBox"
            v-show="isMobile && productData.productParamList && productData.productParamList.length && productData.isDevise != 1">
            <div class="mb-good-color"
              v-if="productData.productParamList && productData.productParamList.length && productData.isDevise != 1">
              <div class="colorBox">
                <div class="good-color-item" v-show="productData.sceneImg"
                  :class="{ active: productData.selectedColorIndex === -1 }" title="Gallery"
                  @click.stop="toSceneImg($event, productData)">
                  <span class="color-circle"></span>
                </div>
                <div class="good-color-item" :class="{ active: productData.selectedColorIndex === cindex }"
                  @click="selectGoodColor($event, itemIndex, cindex, citem)" v-show="cindex < showColorNum"
                  :title="citem.colorAlias" v-for="(citem, cindex) in productData.productParamList" :key="cindex">
                  <span class="color-circle"
                    :style="{ background: citem.colorSecondary ? `linear-gradient(-45deg, ${citem.colorCode} 50%, ${citem.colorSecondary} 50%)` : citem.colorCode }"></span>
                </div>
                <span v-if="productData.productParamList && productData.productParamList.length - showColorNum > 0">+{{
                  productData.productParamList.length - showColorNum }}</span>
              </div>
              <div v-if="productData.isDevise != 1" @click.stop class="pcItemNo">
                {{ productData.productSku }}
              </div>
            </div>
          </div>
        </div>
      </productHead>
    </div>
  </div>
</template>

<script>
import { addCollection, deleteConllectionByUserId } from "@/api/web";
import productHead from "@/components/modal/Half/List/productHead.vue";
import { debounce } from "@/utils/utils";
export default {
  name: "goodItem",
  components: { productHead },
  props: {
    productData: {
      type: Object,
      default: () => ({}),
    },
    itemIndex: {
      type: [Number, String],
    },
    isStockPage: {},
    parentCateId: {},
    cateId: {},
    productList: {
      type: Array,
      default: () => [],
    },
    isDiv: {
      type: Boolean,
      default: false,
    },
    adBoxHeight: {
      type: [Number, String],
    },
    needMerge: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      showColorNum: 5,
      firstLoad: true,
      options: {},
      canvasData: {},
      debounceGetWidth: null
    };
  },
  watch: {},
  computed: {
    langSemiCustom() {
      return this.$store.getters.lang?.semiCustom;
    },
    isMobile() {
      return this.$store.state.device === "mb";
    },
    isLogin() {
      return this.$store.getters.isLogin;
    },
    userId() {
      return this.$store.state.userInfo?.id;
    },
    tagList() {
      if (!this.productData.productLabel || this.productData.productLabel.length <= 0) return [];
      return this.productData.productLabel.split(",");
    },
  },
  methods: {
    toDetail(url) {
      this.$emit('toDetail', url)
    },
    //点击广告跳转链接
    goAdLink(url) {
      window.open(url);
    },
    getSrc(item) {
      if (this.needMerge && this.productData.showMergeImg) {
        let goods = document.querySelector(`.goods${this.productData.id}`);
        if (goods) {
          this.$nextTick(() => {
            let newImgUrl = goods.dataset.newImgUrl;
            if (newImgUrl) {
              this.setPicImg(newImgUrl)
              return newImgUrl
            }
          })
        }
      }
      if (item.selectedColorIndex > -1) {
        if (!this.firstLoad) this.setPicImg(item.showImgSrc + "?x-oss-process=image/resize,p_40")
        return item.showImgSrc + "?x-oss-process=image/resize,p_40";
      } else {
        if (!this.firstLoad) this.setPicImg(item.showImgSrc)
        return item.showImgSrc;
      }
    },
    getSrc2(item) {
      if (item.selectedColorIndex > -1) {
        return item.showImgSrc;
      } else {
        return item.showImgSrc;
      }
    },
    getImgOptions(item) {
      if (item.selectedColorIndex > -1) {
        let optionData = this.parseJSON(item.productParamList[item.selectedColorIndex].imgJson)[0];
        this.options = JSON.stringify({ left: optionData.left, right: optionData.right, top: optionData.top, bottom: optionData.bottle })
        return this.options
      }
      return JSON.stringify({})
    },
    getImgWidth() {
      const img = document.querySelector(`.picImg${this.productData.id}`)
      if (!img) return
      const imgData = img.getBoundingClientRect()
      this.canvasData = JSON.stringify({
        width: imgData.width,
        height: imgData.height
      })
    },
    toSceneImg(e, item) {
      e.stopPropagation();
      e.preventDefault();
      this.productData.showMergeImg = false;
      item.showImgSrc = item.sceneImg;
      item.selectedColorIndex = -1;
    },
    setPicImg(url) {
      let picImg = document.querySelector(`.picImg${this.productData.id}`);
      picImg.setAttribute("src", url);
    },
    parseJSON(str) {
      return str ? JSON.parse(str) : [];
    },
    selectGoodColor(e, ind1, ind2, citem) {
      e.stopPropagation();
      e.preventDefault();
      this.productData.showMergeImg = false;
      this.$set(this.productList[ind1], "selectedColorIndex", ind2);
      this.$set(this.productList[ind1], "showImgSrc", this.parseJSON(citem.imgJson)[0]?.url);
      this.$nextTick(() => {
        this.$emit("changeColor", this.productData.id, this.productData.showImgSrc, this.canvasData, this.options)
      })
    },
    toDetail2(item) {
      const routeOptions = {
        path: item.productRouting,
      };
      const url = this.$router.resolve(routeOptions).href;
      window.open(url, "_blank");
    },
    goCollection(e, item) {
      e.stopPropagation();
      e.preventDefault();
      let isCollection = item.isCollection;
      if (!this.isLogin) {
        this.$store.commit("setLogin", "login");
        return;
      }
      if (isCollection) {
        deleteConllectionByUserId({
          userId: this.userId,
          productId: item.id,
        }).then((res) => {
          item.isCollection = false;
          this.$forceUpdate();
        });
      } else {
        let productData = {
          data: { item_id: this.productData.productSku, item_name: this.productData.name },
          value: 0
        }
        addCollection({
          userId: this.userId,
          website: 1,
          cateId: this.parentCateId || this.cateId,
          productId: item.id,
        }, productData).then((res) => {
          item.isCollection = true;
          this.$forceUpdate();
        });
      }
    },
  },
  created() { },
  mounted() {
    this.firstLoad = false
    this.debounceGetWidth = debounce(this.getImgWidth, 300);
    this.$nextTick(() => {
      this.getImgWidth();
    });
    window.addEventListener("resize", this.debounceGetWidth);
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.debounceGetWidth);
  }
};
</script>

<style scoped lang="scss">
.good-item {
  min-width: 0;
  position: relative;
  border: 1px solid #f0f0f0;
  border-radius: 2px;
  background-color: #fff;
  transition: all 0.3s;
  overflow: hidden;
  user-select: text;
  cursor: pointer;

  @media (any-hover: hover) {
    &:hover {
      --hoverBack: #e6e6e6;
      box-shadow: 0 6px 5px var(--hoverBack), 0 -1px 3px var(--hoverBack);
    }
  }

  a {
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;
  }

  .adBox {
    height: 0;

    img {
      object-fit: fill;
    }

    @include respond-to(pad) {}
  }

  .adImg,
  .adVideo {
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 5px;
    height: 100%;
  }

  .adVideo {
    video {
      height: 100%;
      width: 100%;
      object-fit: contain;
    }
  }

  @include respond-to(pad) {
    // max-height: 474px;
  }

  @include respond-to(mb) {
    &.inserted_element {
      grid-row: span 2;
    }
  }


  .priceBtn {
    display: flex;
    align-items: center;
    justify-content: space-evenly;
    flex-shrink: 0;
    background: linear-gradient(90deg, #EA4116 0%, #F2A91C 100%);
    border-radius: 0px 6px 0px 6px;
    font-size: 12px;
    font-weight: bold;
    color: #fff;
    width: fit-content;
    min-width: 0;
    max-width: 120px;
    padding: 4px 8px;
    column-gap: 8px;

    .priceBtnText {
      word-break: break-word;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .circle {
      flex-shrink: 0;
      position: relative;
      width: 20px;
      height: 20px;
      border-radius: 50%;
      background-color: #ffffff;

      &::before {
        content: "%";
        font-size: 12px;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: $color-primary;
      }
    }
  }

  .good-color,
  .mb-good-color {
    display: flex;
    align-items: center;
    justify-content: space-between;
    column-gap: 6px;

    .colorBox {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      justify-content: flex-start;
      gap: 2px;
      padding: 10px 0;
    }

    .pcItemNo {
      padding: 10px;
      flex-shrink: 0;
      font-weight: 400;
      font-size: 12px;
      color: #999999;
      line-height: 14px;
      text-align: left;
      text-transform: none;
      cursor: auto;
      user-select: text;
      z-index: 1;
      border-radius: 2px;

      @include respond-to(mb) {
        font-size: 14px;
        padding: 0;
      }

    }

    .good-color-item {
      display: flex;
      justify-content: center;
      align-items: center;
      border: 1px solid transparent;
      padding: 2px;
      border-radius: 50%;
      transition: all 0.3s;

      &:hover {
        border-width: 1px;
        border-color: $color-primary;
      }

      .color-circle {
        display: inline-block;
        width: 14px;
        height: 14px;
        background: url("https://oss-static-cn.liyi.co/web/quoteManage/20231021/20230619xcxWJWKD_2054DQFx75.png") center/contain no-repeat;
        border-radius: 50%;
        outline: 1px solid #ccc;
      }
    }

    .good-color-item.active {
      border-color: $color-primary;
    }

    @include respond-to(mb) {
      display: none;
      bottom: -2px;
      left: 8px;
      min-width: 320px;
      position: absolute;
      width: 100%;
      justify-content: flex-start;
      column-gap: 12px;
      font-size: 14px;

      .good-color-item {
        &:hover {
          background-color: transparent;
        }

        .color-circle {
          width: 20px;
          height: 20px;
        }
      }
    }
  }

  .goods {
    min-width: 0;
    height: 100%;
    display: grid;
    grid-template-columns: 1fr;
    grid-template-rows: auto 1fr;
    position: relative;
    border-radius: 4px;
    background-color: #fff;
    transition: all 0.3s;
    cursor: pointer;

    .good-collection {
      position: absolute;
      top: 10px;
      left: 10px;

      b {
        color: #999999;
        font-size: 24px;
      }

      .collectionXin {
        &.isActive {
          color: $color-primary;
        }
      }

      @include respond-to(mb) {
        b {
          font-size: 20px;
        }
      }
    }

    .good-img {
      overflow: hidden;
      position: relative;
      aspect-ratio: 1;

      .imgWrap {
        width: 100%;
        height: 100%;
        position: relative;
        overflow: hidden;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 5px;

        img {
          aspect-ratio: 1/1;
        }

        .priceBtn {
          position: absolute;
          right: 0;
          top: 0;
          border-radius: 0px 2px 0px 6px;
        }

        ::v-deep .v-image__image--cover {
          background-size: contain;
        }
      }
    }

    .good-back {
      display: flex;
      flex-direction: column;
      padding: 0 12px;

      .good-info {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        padding: 0 0 15px;
        transition: all 0.3s;

        .productTitle {
          font-size: 16px;
          height: 3em;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          word-break: break-word;
          text-overflow: ellipsis;
          overflow: hidden;

          .priceBtn {
            float: left;
            margin-right: 4px;
          }

          h3 {
            display: inline;
            font-size: 16px;
            line-height: 1.5;
            word-break: break-word;
            text-overflow: ellipsis;
            // display: -webkit-box;
            // -webkit-box-orient: vertical;
            // -webkit-line-clamp: 2;
            /* 这里是超出几行省略 */
            overflow: hidden;
            user-select: auto !important;
            -moz-user-select: auto !important;
            -webkit-user-select: auto !important;
            -ms-user-select: auto !important;
            pointer-events: auto !important;
            cursor: auto;
          }
        }

        .priceBox {
          flex: 1;
          display: flex;
          align-items: flex-start;
          justify-content: space-between;
          gap: 4px;

          .discountPrice {
            display: flex;
            align-items: center;
            line-height: normal;
            column-gap: 4px;
            font-size: 16px;
            height: 28px;

            .nowPrice {
              flex-shrink: 0;
            }

            span {
              color: $color-primary;
              font-weight: bold;
            }

            label {
              color: $color-primary;
              font-size: inherit;
              font-weight: bold;
            }
          }
        }

        .productTag {
          flex: 1;
          display: flex;
          flex-wrap: wrap;
          align-items: flex-start;
          gap: 6px;
          margin: 6px 0;

          .tag {
            //flex-shrink: 0;
            font-style: italic;
            font-size: 14px;
            color: var(--tag-color, var(--color-primary));
            background-color: var(--tag-color-lighten, var(--color-second));
            border-radius: 4px;
            padding: 4px 6px;
          }

          @media screen and (min-width: 750px) and (max-width: 1024px) {
            flex-grow: 0;
          }
        }

        .minQuantity {
          font-size: 14px;
          color: #666666;
        }

        .des {
          color: #999999;
          font-size: 12px;
          word-break: break-word;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 1;
          /* 这里是超出几行省略 */
          overflow: hidden;
          line-height: 2;
        }

        .xin {
          display: flex;
          align-items: center;
          height: 24px;
          margin-left: -2px;

          span {
            margin-left: 4px;
            font-size: 14px;
            padding-top: 4px;
          }

          @include respond-to(mb) {
            margin: 2px 0;

            ::v-deep .v-rating .v-icon {
              font-size: 20px;
            }

            span {
              font-size: 12px;
            }
          }
        }
      }
    }

    @include respond-to(mb) {
      display: grid;
      grid-template-rows: 1fr auto;
      grid-template-columns: 40% 60%;

      .good-img {
        max-height: 280px;

        .imgWrap {
          .priceBtn {
            border-radius: 0 10px 0 10px;

            .circle {
              display: block;
            }
          }
        }

      }

      .good-back {
        padding: 0;

        .good-info {
          padding: 5px;

          .priceBox {
            flex-direction: column;
            justify-content: flex-start;
            gap: 0;
            flex-grow: 0;

            .price {
              font-size: 16px;

              label {
                font-size: 14px;
              }
            }

            .discountPrice {
              margin: 0;
              font-size: 16px;

              label {
                font-size: 16px;
              }
            }
          }

          .productTitle {
            font-size: 16px;

            h3 {
              font-size: 16px;
            }
          }

          .minQuantity {
            font-size: 14px;
          }

          .productTag {
            flex-grow: 0;

            .tag {
              font-size: 14px;
            }
          }
        }
      }

      .frameBox {
        grid-column: span 2;
        padding: 0 10px 8px;

        .mb-good-color {
          display: flex;
          padding: 0;
          height: 100%;
          position: relative;
          top: 0;
          left: 0;
          right: auto;
          bottom: auto;
        }
      }
    }

  }
}
</style>
