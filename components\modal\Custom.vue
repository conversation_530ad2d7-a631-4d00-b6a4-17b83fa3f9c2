<template>
	<div class="modal-box" :class="modal.class" :style="modal.style"
		@click.self="setModalType({}, {}, modal.class?.split(' ')[0] + '_list')">

		<b v-if="modal.dialog && list.length" pointer class="icon-guanbi" @click="$store.commit('setMask', false)"></b>

		<modalSwiper v-if="modal.dialog && list.length" :id="modal.modal" :data="{ id: modal.modal, ...modal, list }">
		</modalSwiper>

		<PatchesBacking v-else-if="modal.api === 'getBackingList' && list.length" :modal="modal" :list="list" />

		<CustomColorSample v-else-if="modal.api === 'getColorSampleList'" :modal="modal" :list="list" />

		<CustomDesignSample v-else-if="modal.api === 'getTemplateListFormApp'" :modal="modal" :page="page" :data="apiResponseData" @refresh="onRefresh" />

		<template v-else v-for="(o, oi) in modal.outer">
			<EditDiv v-if="o.title?.value" v-model:content="o.title.value" :tagName="o.title.tagName || 'h2'"
				:style="modal.titleStyle" @click="setModalType(o.title, modal.outer, 'text')" />

			<div class="sub-title" :style="modal.subTitleStyle" :hidden="!o.title && !o.subTitle"
				@click="setModalType(o.subTitle, modal.outer, 'text')">
				<b v-if="o.subTitle?.icon" :class="o.subTitle.icon"></b>
				<EditDiv tagName="span" v-model:content="o.subTitle.value" v-if="o.subTitle" />
			</div>

			<div flex class="tab-box" :style="modal.tabBoxStyle" v-if="modal.tabList">
				<div flex pointer v-for="t in modal.tabList" :class="{ sele: modal.parentIndex == t.id }"
					:style="modal.parentIndex == t.id ? modal.tabSeleStyle : modal.tabStyle" :key="t.id"
					@click="modal.parentIndex = t.id">
					<div class="tab-img" v-if="t.ringTagImage">
						<pic :src="t.ringTagImage && JSON.parse(t.ringTagImage)[0]" :alt="t.ringTagName"></pic>
					</div>
					<EditDiv v-if="t.ringTagName" class="tab-title" v-model:content="t.ringTagName"
						:tagName="t.ringTagName?.tagName || 'h3'" />
				</div>
			</div>

			<div v-if="modal.getTagList && tagList.length">
				<div flex class="search-box" :style="modal.searchBoxStyle">
					<div class="now">
						<span @click="modal.parentIndex = 0">{{ langTemplate.templates }}</span>
						<span> > {{ parentTag.ringTagName }}</span>
					</div>
					<div class="search" v-if="o.search">
						<input type="text" v-model="searchKeyword" :placeholder="o.search.value" />
						<b :class="o.search.icon"></b>
					</div>
				</div>
				<div flex class="tag-box" v-show="modal.childTemplate" :style="modal.tabBoxStyle">
					<strong pointer @click="modal.parentIndex = 0" v-show="modal.parentTemplate">
						{{ langTemplate.templates }}</strong>
					<div pointer v-for="t in tagList" :class="{ sele: modal.parentIndex == t.id }"
						:style="modal.parentIndex == t.id ? modal.tabSeleStyle : modal.tabStyle" :key="t.id"
						@click="modal.parentIndex = t.id">{{ t.ringTagName }}
					</div>
				</div>
			</div>


			<b :class="modal.showArrow" :style="{ ...modal.outArrowStyle, ...modal.outArrow1Style }"
				@click="scrollCard('l')" :disabled="scrollIndex == 0"></b>

			<div class="card-box" :scrollbar="modal.scroll" :style="modal.cardBoxStyle">
				<div flex :style="{ 'grid-gap': '1.5em ' + modal.margin + '%', ...modal.boxStyle }">
					<div flex class="card" v-for="(l, li) in list" :key="li" :pointer="l.button?.url"
						:cornerLabel="l.cornerPosition || 'left'"
						:hidden="(!isViewMore && o.buttonOuter?.url && (typeof (Number(o.buttonOuter?.url)) == 'number') && (li >= o.buttonOuter?.url))"
						@click="setModalType({}, {}, null, { ...o.button, ...o.button1, ...l.button })"
						:style="{ width: (100 - modal.margin * (modal.column - 1)) / modal.column + '%', ...modal.cardStyle }">

						<div class="card-img" v-if="l.productVideo" :style="modal.cardImgBoxStyle">
							<b pointer class="icon-jxsht-3d-bf" @click.stop="videoPlay(l)" v-show="!l.imgIndex"></b>
							<video :src="l.productVideo" :poster="l.productVideoImg" :style="modal.cardImgStyle" loop
								:id="modal.id + l.id" @click.stop="videoPause(l)"></video>
						</div>
						<div class="card-img" v-else-if="l.imagePhoto" :style="modal.cardImgBoxStyle">
							<pic :src="l.imagePhoto" :alt="l.cateName || l.alt" :style="modal.cardImgStyle" />
						</div>
						<div class="card-img" v-else-if="l.imgList?.length" :style="modal.cardImgBoxStyle">
							<pic v-for="(p, pi) in l.imgList" :src="p.url" :alt="p.alt || l.name"
								:key="p.url + '-' + pi" :style="modal.cardImgStyle" v-show="pi == l.imgIndex" />
						</div>

						<div class="custom-box" v-if="!l.cateType && o.custom">
							{{ o.custom.value }} <b v-if="o.custom.icon" :class="o.custom.icon"></b>
						</div>

						<div class="hot-box" v-show="modal.isHot"><b class="icon-huoyan"></b>{{ lang.hot }}</div>

						<Favorite v-if="modal.showFavorite" :product-info="l" :style="modal.cardFavoriteStyle" />

						<div v-if="l.cornerLabel?.id" flex class="corner-label" :onlyImg="!l.cornerLabel.name"
							:style="{ color: l.cornerLabel.color, backgroundImage: 'url(' + l.cornerLabel.bgImg + ')' }">
							<pic v-if="l.cornerLabel.icon" :src="l.cornerLabel.icon" :alt="l.cornerLabel.name" />
							{{ l.cornerLabel.name }}
						</div>

						<div class="content" :style="{ ...modal.cardContentStyle, ...l.contentStyle }">
							<div v-if="modal.api == 'getHotProductList'" class="content-title"
								:style="modal.cardTitleStyle">{{ l.name }}</div>
							<EditDiv v-else-if="l.name" v-model:content="l.name" :style="modal.cardTitleStyle"
								:tagName="o.cardTitle?.tagName || (modal.tabList ? 'div' : 'h3')" />

							<div flex v-if="o.reviews" class="reviews-box" :style="modal.reviewsStyle">
								<div class="star-box"
									:style="{ ...modal.reviewsStarStyle, backgroundImage: 'linear-gradient(90deg,' + (o.reviews.starColor || '#F96A00') + ' ' + l.commentLevel * 19 + '%,#999 0)' }">
									<b v-for="s in 5" :class="o.reviews.starIcon || 'icon-star'"></b>
								</div>
								<span>{{ l.commentLevel }}</span>
								<label v-if="l.commentNum">({{ l.commentNum }})</label>
							</div>

							<div v-if="l.cateType || o.colorCodeList" flex pointer center class="content-custom"
								:style="modal.cardCustomStyle">
								<div v-for="(p, pi) in l.imgList" :key="p.value" :style="'background:' + p.code"
									:class="{ sele: pi == l.imgIndex, all: !pi && !p.code }"
									@click.stop="(l.imgIndex = pi), $forceUpdate()"></div>
							</div>
							<div v-else-if="o.custom" class="content-custom custom-box">
								{{ o.custom.value }}
								<b v-if="o.custom.icon" :class="o.custom.icon"></b>
							</div>

							<div v-if="l.description" class="content-sub-title" :style="modal.cardSubTitleStyle">
								{{ l.description }}
							</div>

							<div class="price-list" :style="{ ...modal.cardPriceListStyle, ...l.priceListStyle }"
								v-if="l.priceList">
								<template v-for="p in l.priceList">
									<b v-if="modal.priceListIcon" :class="modal.priceListIcon"
										:style="modal.priceListIconStyle"></b>
									<CCYRate v-if="Number(p.value)" :style="modal.priceValueStyle" :price="p.value">
									</CCYRate>
									<span v-else :style="modal.priceValueStyle">{{ p.value }}</span>
								</template>
							</div>

							<div v-if="l.productSku" class="half-detail-id" :style="modal.cardSkuStyle">{{ l.productSku
							}}</div>

							<template v-if="modal.class?.includes('half-detail')">
								<div class="half-detail-price">
									<CCYRate :price="l.lowestDiscountPrice"></CCYRate> -
									<CCYRate :price="l.highestDiscountPrice"></CCYRate>
								</div>
							</template>
							<div v-else-if="l.lowPrice" class="content-price" :style="modal.cardPriceStyle">
								<span :style="o.lowPricePrefix?.style">{{ o.lowPricePrefix?.value ?? lang.as }}</span>
								<CCYRate :price="l.lowPrice"></CCYRate>
								<span :style="o.lowPriceSuffix?.style" v-if="!modal.hideEa">{{ o.lowPriceSuffix?.value
									?? lang.ea }}</span>
							</div>

							<label v-if="o.quantity" :style="modal.cardQuantityStyle">
								<EditDiv tagName="span" v-model:content="o.quantity.value"></EditDiv>:
								{{ l.lowestPurchaseQuantity }}
							</label>

							<div v-if="o.tips?.value" class="content-tips" :style="o.tips?.style">{{ o.tips?.value }}
							</div>

							<div v-if="l.ringTemplatePrice" class="content-discount" :style="modal.cardDiscountStyle">
								<span>{{ lang.from }}</span>
								<!-- ringTemplateDiscount小于1就是折扣，大于1就是加价，加价时只显示原价 -->
								<CCYRate :decimal="modal.hideDiscount ? 2 : 0"
									:price="l.ringTemplatePrice * (1 - (l.ringTemplateDiscount < 1 ? l.ringTemplateDiscount : 0))">
								</CCYRate>
								<del v-show="l.ringTemplateDiscount < 1">
									<CCYRate :price="l.ringTemplatePrice" :decimal="modal.hideDiscount ? 2 : 0">
									</CCYRate>
								</del>
								<span :hidden="modal.hideDiscount">
									({{ l.ringTemplateDiscount * 100 }}% {{ lang.off }})</span>
							</div>

							<div flex v-if="l.metalSignColor" class="content-color" :style="modal.cardColorStyle">
								<div v-for="c in l.metalSignColor.split(',')" :style="{ background: c }" :key="c"></div>
							</div>

							<button v-if="l.cateType && o.button?.value" :style="modal.cardBtnStyle"
								:title="o.button.alt" :primary="!o.button.outline" :outline="o.button.outline"
								@click.stop="setModalType(o.button, modal.outer, 'button', { ...o.button, ...l.button })">
								<EditDiv tagName="label" v-model:content="o.button.value"></EditDiv>
								<b :class="o.button.icon" v-show="o.button.icon"></b>
							</button>

							<button v-else-if="o.button1?.value" :style="modal.cardBtn1Style" :title="o.button1.alt"
								:primary="!o.button1.outline" :outline="o.button1.outline"
								@click.stop="setModalType(o.button1, modal.outer, 'button', { ...o.button1, ...l.button })">
								<EditDiv tagName="label" v-model:content="o.button1.value"></EditDiv>
								<b :class="o.button1.icon" v-show="o.button1.icon"></b>
							</button>
						</div>
					</div>
				</div>
			</div>

			<b :class="modal.showArrow" :style="{ ...modal.outArrowStyle, ...modal.outArrow2Style }"
				@click="scrollCard('r')" :disabled="scrollIndex >= (pageNum - 1)"></b>

			<div flex class="point-box" v-if="(modal.repeat || !oi) && modal.scroll && modal.showPoint"
				:style="modal.pointBoxStyle">
				<div v-for="(l, li) in pageNum" :class="{ select: scrollIndex == li }" :key="li" @click="scrollBox(li)">
				</div>
			</div>

			<div v-if="o.buttonOuter?.value && !showViewMoreBtn" class="hover-tag btn-box" :style="modal.boxBtnStyle">
				<button v-if="o.buttonOuter?.value" :style="modal.btnStyle" :title="o.buttonOuter.alt"
					:primary="!o.buttonOuter.outline" :outline="o.buttonOuter.outline"
					@click="setModalType(o.buttonOuter, modal.outer, 'button', o.buttonOuter)">
					<EditDiv tagName="label" v-if="o.buttonOuter.value && !isViewMore"
						v-model:content="o.buttonOuter.value">
					</EditDiv>
					<EditDiv tagName="label" v-else-if="o.buttonOuter.viewLessValue"
						v-model:content="o.buttonOuter.viewLessValue" />

					<b :class="o.buttonOuter.icon" v-show="o.buttonOuter.icon"></b>
				</button>
			</div>

			<div v-if="showViewMoreBtn" class="hover-tag btn-box" :style="modal.boxBtnStyle">
				<button v-if="o.buttonViewMore?.value" :style="modal.btnStyle" :title="o.buttonViewMore.alt"
					:primary="!o.buttonViewMore.outline" :outline="o.buttonViewMore.outline"
					@click.stop="getPage(false)">
					<EditDiv tagName="label" v-model:content="o.buttonViewMore.value"></EditDiv>
					<b :class="o.buttonViewMore.icon" v-show="o.buttonViewMore.icon"></b>
				</button>
			</div>
		</template>
	</div>
</template>



<script>
import * as apiData from "@/api/web.js";

export default {
	name: "modalCustom",
	props: {
		data: {
			type: Object,
			default: {},
		},
	},
	data() {
		return {
			modal: {
				column: 4,
				margin: 1.5,
				parentIndex: 0,
				childIndex: 0,
				type: {},
				style: {},
				outer: [{}],
				...this.data,
			},
			list: [],
			tagList: [],
			searchKeyword: '',
			scrollIndex: 0,
			page: 1,
			totalPages: 0,
			getPageing: false,
			cardBoxDom: undefined,
			pageNum: 2,
			isViewMore: false,
			apiResponseData: null,
		};
	},
	computed: {
		apiParams() {
			let params = {
				page: this.page,
				pageSize: this.modal.pageSize,
				userIdStr: this.$store.state.userInfo?.id,
				keyword: this.searchKeyword,
			}

			switch (this.modal.api) {
				case "getProductList":
					params.categoryId = this.modal.categoryId;
					params.childCategoryId = this.modal.childCateId || this.modal.parentIndex || '';
					break;
				case "getHotProductList":
					params.categoryId = this.modal.categoryId;
					params.childCateId = this.modal.childCateId;
					params.productIdList = this.modal.productIdList;
					break;
				case "getHotProductListTwo":
					params.categoryId = this.modal.categoryId;
					params.childCateId = this.modal.childCateId;
					params.productIdsStr = this.modal.productIdList;
					break;
				case "getCustomProductList":
					params.cateIdList = this.modal.customList && this.modal.customList.join();
					break;
				case "getAppRingTemplatesList":
					params.cateId = this.modal.cateId;
					params.ringTagId = this.modal.parentIndex || '';
					break;
				case "getGalleryByCateId":
					params.pageUrl = this.modal.pageUrl;
					params.limitNum = this.modal.limitNum || 50;
					break;
				case "getFdMiddlePageData":
					params.categoryId = this.modal.categoryId;
					params.quoteId = this.$route.query.quoteId;
					break;
			}

			return { ...params, ...(this.modal.apiParams ?? {}) };
		},
		lang() {
			return this.$store.getters.lang?.semiCustom || {};
		},
		langTemplate() {
			return this.$store.getters.lang?.template || {};
		},
		parentTag() {
			return this.tagList.find(i => i.id == this.modal.parentIndex) || { id: 0, ringTagName: this.langTemplate.all };
		},
		showViewMoreBtn() {
			const { viewMore, outer } = this.modal
			return !!viewMore && !!outer?.[0].buttonViewMore?.value && this.page < this.totalPages
		}
	},
	watch: {
		modal: {
			async handler(val) {
				this.page = 1;
				await this.getData();

				if (process.env.isManage) this.$emit("update:data", val);
			},
			immediate: true,
			deep: true,
		},
		searchKeyword() {
			this.page = 1;
			this.getData();
		}
	},
	mounted() {
		if (process.browser) {
			this.cardBoxDom = document?.querySelector(`#${this.modal.id} .card-box`);
			if (this.cardBoxDom) this.pageNum = Math.round(this.cardBoxDom.scrollWidth / this.scrollWidth());
			if (!this.modal.viewMore) document?.addEventListener("scroll", this.getPage);
		}

		if (this.$route.query.parentIndex) this.modal.parentIndex = this.$route.query.parentIndex;
		if (this.$route.query.childIndex) this.modal.childIndex = this.$route.query.childIndex;

		if (this.modal.getTagList) apiData['getAppRingTagList']({ cateId: this.modal.cateId }).then(res => {
			this.tagList = res.data;
			this.getData();
		});
	},
	methods: {
		setModalType(target, targetArray, clickType, event, other) {
			this.$setModal(this, target, targetArray, clickType, event, other)
		},
		scrollWidth() {
			return this.cardBoxDom?.clientWidth + (window?.getComputedStyle(this.cardBoxDom?.children[0]).columnGap.endsWith('px') ? Number(window?.getComputedStyle(this.cardBoxDom?.children[0]).columnGap?.slice(0, -2) || 0) : (this.cardBoxDom.scrollWidth * this.modal.margin / 100));
		},
		async getData() {
			try {
				let { data } = await apiData[this.modal.api](this.apiParams);
				this.apiResponseData = data;

				if (this.modal.api === "getFdMiddlePageData") {
					const midData = JSON.parse(JSON.stringify(data))
					data = this.$route.query.quoteId ? midData.quoteList : midData.halfQuoteList
				}

				let resList = this.modal.api === 'getAppRingTemplatesList' ? data.content : data
				if (this.modal.apiResListField) resList = data[this.modal.apiResListField];

				let list = resList.map(i => {
					let url = i.quoteRoutingName; // 报价

					let imgList = this.modal.class?.includes('half-detail') ? [{ url: i.sceneImg }, ...i.productParamList.map(l => {
						return {
							code: l.colorCode,
							...(l.imgJson && JSON.parse(l.imgJson)[0])
						}
					})] : (i.imgList || (i.imgJson && JSON.parse(i.imgJson)));

					switch (this.modal.api) {
						case 'getHotProductList': url = i.productRouting; break; // 半定制详情页
						case 'getCustomProductList': url = i.routingName; break; // 全定制列表页
						case 'listPageImgByPageId':  // 图片列表弹窗(by页面)
							i.img = i.icon = { value: i.url, alt: i.alt };
							i.title = { value: i.contentTitle };
							i.subTitle = { value: i.contentSubtitle };
							i.imagePhoto = i.url;
							break;
						case 'getGalleryByCateId':  // 图片列表弹窗(by产品)
							url = `/${i.pageUrlPrefix}/exhibit/${i.pageUrl}/`;
							i.imagePhoto = i.picPathZero;
							i.img = i.icon = { value: i.picPathZero, alt: i.galleryName, url };
							i.title = { value: i.galleryName };
							i.subTitle = { value: i.briefDescription };
							break;
						case 'getFdMiddlePageData':
							i.priceList = i.productFeature && JSON.parse(i.productFeature).map(x => {
								return { value: x.value }
							})
							url = i.subclassRouting
							i.description = i.mainFeature
							i.productSku = i.productNumber
							break;
						case 'getHotProductListTwo':
							url = i.productRouting;
							imgList = [
								{ url: i.sceneImg },
								...i.productParamList.map((l) => {
									return {
										code: l.colorCode,
										...(l.imgJson && JSON.parse(l.imgJson)[0]),
									};
								}),
							];
							if (this.modal.class && this.modal.class.includes('hover-change-img')) {
								imgList = [i.imgJson && JSON.parse(i.imgJson)[0], { url: i.sceneImg }]
							}
							break;
						case 'getProductList':
							url = i.productRouting;
							imgList = [
								{ url: i.sceneImg },
								...i.productParamList.map((l) => {
									return {
										code: l.colorCode,
										...(l.imgJson && JSON.parse(l.imgJson)[0]),
									};
								}),
							];
							break;
					}

					let button = this.modal.linkType == 'dialog' ? { // 弹窗
						method: this.modal.api === 'listPageImgByPageId' ? {
							modal: "modalCustom",
							api: this.modal.api,
							showArrow: "icon-a-icon-jt4zhuanhuan",
							class: "img-swiper",
							showIndexPage: true,
							showTab: true,
							dialog: true,
						} : {
							modal: ["getProductList", "getHotProductList", "getHotProductListTwo"].includes(this.modal.api) ? 'modalQuoteHalfDetail' : 'modalQuoteDialog',
							name: url,
							quoteUrl: url + '?type=quoteIframe'
						}
					} : { url }; // 链接


					return {
						...i,
						imgIndex: i.imgIndex || 0, // 多图时控制显示第几张，视频时控制播放键显示
						name: i.name || i.cateName,
						lowPrice: i.lowPrice || i.lowestDiscountPrice || i.lowestPrice || i.fdLowestPrice,
						imgList,
						button
					};
				});



				if (this.page == 1) this.list = list;
				else this.list.push(...list);

				this.totalPages = data.totalPages;

				if (!process.browser) return
				this.$nextTick(() => setTimeout(() => {
					this.cardBoxDom = document?.querySelector(`#${this.modal.id} .card-box`);
					if (this.cardBoxDom) this.pageNum = Math.round(this.cardBoxDom.scrollWidth / this.scrollWidth());

					if (this.modal.scroll && this.cardBoxDom) this.cardBoxDom.addEventListener("scroll", e => {
						this.scrollIndex = Math.round((e.target.scrollLeft || 1) / e.target.clientWidth); // 监听计算当前在第几屏
					})
				}, 1000));
			} catch (error) {

			} finally {
				this.getPageing = false;
			}
		},
		getPage(isScroll) {
			let flag = this.page < this.totalPages && !this.getPageing
			if (!!isScroll) {
				flag = flag && (document.body.clientHeight - window.scrollY - window.innerHeight) < document.querySelector('footer').offsetHeight + 50
			}
			if (flag) {
				this.getPageing = true;
				this.page++;
				this.getData();
			}
		},
		scrollBox(index) {
			this.scrollIndex = index - 1
			this.scrollCard()
		},
		scrollCard(position) {
			// 点击滚动一屏（父盒子一屏宽度 + 距离下一屏的margin）* 滚到第几屏
			if (this.cardBoxDom) this.cardBoxDom.scrollLeft = Math.round(this.scrollWidth()) * (this.scrollIndex + (position == 'l' ? -1 : 1));
		},
		videoPlay(item) {
			item.imgIndex = !item.imgIndex;
			document.getElementById(this.modal.id + item.id).play();
		},
		videoPause(item) {
			item.imgIndex = !item.imgIndex;
			document.getElementById(this.modal.id + item.id).pause();
		},
		onRefresh(params) {
			Object.keys(params).forEach(key => {
				if (this.$data.hasOwnProperty(key)) {
					this[key] = params[key];
				}
			})
			this.getData();
		}
	},
	beforeDestroy() {
		document.removeEventListener("scroll", this.getPage);
	},
};
</script>



<style lang="scss" scoped>
.modal-box>b {
	&:not([class]) {
		display: none;
	}

	&[class] {
		font-size: 1.2em;

		&:last-of-type::before {
			transform: rotate(180deg) translateY(0.05em);
		}

		&[disabled] {
			opacity: 0.5;
		}
	}
}

.tab-box>div:not(.sele) .tab-title {
	opacity: 0.7;
}

.search-box {
	margin-bottom: 1em;
	align-items: flex-end;
	padding-bottom: 0.75em;
	justify-content: flex-end;
	border-bottom: 1px solid #DCDCDC;

	.now {
		flex: 1;
	}

	.search {
		border-radius: 3em;
		padding: 0.414em 1em;
		background: #F5F5F5;

		input {
			font-size: calc(1em - 2px);
		}
	}
}

.tag-box>strong {
	padding-right: 1em;
	border-right: 1px solid #D0D0D0;
}

.card-box>div {
	flex-wrap: wrap;
}

.card {
	grid-gap: 1em;
	flex-direction: column;
	position: relative;
	z-index: 1;

	.card-img {
		height: 0;
		padding-bottom: 100%;
		position: relative;

		>video,
		>img {
			height: 100%;
			position: absolute;
			left: 0;
			top: 0;
		}

		b {
			width: 3.3em;
			font-size: 1.6em;
			text-align: center;
			line-height: 3.3em;
			border-radius: 50%;
			padding-left: 0.2em;
			background-color: white;
			transform: translate(-50%, -50%);
			position: absolute;
			z-index: 9;
			left: 50%;
			top: 50%;
		}
	}

	.custom-box {
		display: flex;
		font-weight: bold;
		align-items: center;
		font-size: calc(1em - 2px);

		b {
			margin-left: 7px;
			font-size: 1.3em;
		}
	}

	>.custom-box {
		padding: 0.1em 0.3em 0;
		border-radius: $radius-btn;
		background: white;
		position: absolute;
		right: 1em;
		top: 1em;
	}

	.hot-box {
		color: white;
		font-size: 0.86em;
		text-align: center;
		padding: 0.4em 0.4em 0.2em;
		border-radius: 0 0 0.7em 0.7em;
		background-color: $color-primary;
		position: absolute;
		left: 5%;

		b {
			display: block;
			font-size: 1.2em;
		}
	}

	.corner-label {
		grid-gap: 0.25em;
		align-items: center;
		padding: 0.3em 1em;
		font-size: calc(1rem - 2px);
		background-size: 100% 100%;
		position: absolute;
		top: 0.75em;

		img {
			width: auto;
			height: 1.75em;
		}

		&[onlyImg] {
			padding: 0;

			img {
				height: 2em;
			}
		}
	}

	&[cornerLabel~='left'] .corner-label {
		left: 1em;
	}

	&[cornerLabel~='right'] .corner-label {
		right: 1em;
	}

	&[cornerLabel~='edge'] {

		.corner-label {
			top: 0;
		}

		&[cornerLabel~='left'] .corner-label {
			left: 0;
		}

		&[cornerLabel~='right'] .corner-label {
			right: 0;
		}
	}

	&[cornerLabel~='out'] {
		overflow: unset;

		.corner-label {
			top: -0.5em;

			&[onlyImg] img {
				width: 4.8em;
				height: auto;
			}
		}

		&[cornerLabel~='left'] .corner-label {
			left: -0.5em;
		}

		&[cornerLabel~='right'] .corner-label {
			right: -0.5em;
		}
	}

	.content {
		flex: 1;
		display: flex;
		grid-gap: 0.5em;
		flex-direction: column;
	}

	.reviews-box {
		grid-gap: 0.3em;

		.star-box {
			font-size: 0.95em;
			width: fit-content;
			background-clip: text;
			-webkit-background-clip: text;

			b {
				color: transparent;
				margin-right: 0.2vw;
			}
		}
	}

	.content-title {
		font-weight: 900;
		font-size: 1.125em;
	}

	.content-custom {
		margin-top: 0.8vmax;
		justify-content: center;

		div {
			padding: 0.4em 0.43em;
			margin: 0 0.3em 0.5em;
			border: 1px solid #ddd;

			&.all {
				background: url(https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20230619/20230619xcxWJWKD.png) 50% / contain no-repeat;
			}

			&.sele {
				box-shadow: 0 0 1px 1px $color-primary;
			}
		}
	}

	.content-sub-title {
		overflow: hidden;
		min-height: 4.75em;
		margin-top: 0.8vmax;
		display: -webkit-box;
		-webkit-line-clamp: 4;
		text-overflow: ellipsis;
		-webkit-box-orient: vertical;
	}

	.content-color div {
		width: 1em;
		height: 1em;
		border-radius: 50%;
	}

	button {
		margin-top: 2em;
		padding: 0.2em 1.8em;
	}
}

.btn-box {
	margin-top: 2.5em;
	text-align: center;
}



.modal-box.custom {
	.sub-title b {
		font-weight: bold;
		margin-right: 0.3em;
		color: $color-primary;
	}

	.card {
		&:hover {
			background-color: $color-second;

			.content .content-sub-title {
				display: none;
			}

			button {
				display: inline-flex !important;
			}
		}

		.content-price {
			span {
				opacity: 0.8;
			}

			label {
				font-weight: bold;
				font-size: calc(1em + 2px);
			}
		}
	}
}


.modal-box.hover-color {
	>b:not([disabled]) {
		color: white;
		cursor: pointer;
		background: $color-primary;
		border-color: $color-primary;
	}

	>b:first-of-type {
		justify-self: flex-end;
	}

	.card:hover {
		.content {
			color: $color-primary;
		}

		.card-img img,
		.card-img video {
			box-shadow: 0 0 0 2px $color-primary !important;
		}
	}
}


.modal-box.tag {
	font-size: 1em !important;

	.tab-box .sele {
		position: relative;
		z-index: 1;

		.tab-img {
			overflow: hidden;
			border-radius: 0.85vmax;
			border: 0.125em solid black;
			position: relative;

			img {
				border-radius: 0.8vmax;
				border: 0.125em solid white;
			}

			&::before {
				content: "✓";
				display: block;
				color: white;
				background: black;
				border-radius: 100% 50%;
				border: 0.15em solid white;
				padding: 0.75em 2em 1.25em 1em;
				position: absolute;
				bottom: -1.25em;
				right: -1.75em;
			}
		}

		&::after {
			content: "▼";
			background: #fff;
			border-radius: 1em;
			padding: 0.3em 1em 0.2em;
			transform: translateX(-50%) scaleY(0.6);
			position: absolute;
			bottom: -1.6em;
			left: 50%;
		}
	}

	.tab-box::after {
		content: "";
		width: 100%;
		border-bottom: 1px solid #CDCDCD;
		position: absolute;
		bottom: 0.5em;
	}

	.card:hover {
		// box-shadow: 0 1px .5em -0.25em black;
		box-shadow: 0.5em 0.5em 1em 0 rgba(0, 0, 0, 0.18);

		button {
			color: white !important;
			background: $color-primary;
		}
	}

	&:not(.get-data) .content-discount {
		>label {
			color: #FF2B2B;
			font-weight: 900;
		}

		>*:not(label) {
			opacity: 0.7;
		}
	}

	&.get-data .content-discount del {
		opacity: 0.7;
		font-size: calc(1em - 2px);
	}
}


.modal-box.img-swiper.mask-content {
	padding: 0;
	width: auto;
	height: auto;
	color: white;

	.icon-guanbi {
		transform: none;
		background: none;
		right: 1vw;
		top: 1em;
	}

	>.card-box {
		height: 0px;
	}
}


.modal-box.half-detail .card {
	&:hover {
		box-shadow: 0 0 .3em -0.2em black;
	}

	.content-custom {
		order: -1;
		margin-top: 0;
		justify-content: flex-start;

		>div {
			border-radius: 50%;
		}
	}

	label:not(.half-detail-price label) {
		opacity: 0.7;
	}

	.half-detail-id {
		color: white;
		margin-top: -4em;
		border-radius: 0.2em;
		padding: 0.3em 0.3em 0.2em;
		font-size: calc(1em - 3px);
		background: rgba(100, 100, 100, 0.6);
		position: absolute;
		right: 0.75em;
	}

	.half-detail-price label {
		font-weight: 900;
		font-size: 1.125em;
		color: $color-primary;
	}
}

.modal-box.mid-page .card {
	transition: all .3s ease-in-out;

	.content {
		.content-sub-title {
			background-color: $color-second;
		}
	}

	&:hover {
		border-color: $color-primary !important;
		transform: scale(1.03);
		box-shadow: 1px 1px 1px $color-primary, -1px -1px 1px $color-primary;
	}
}

.modal-box.mid-page-full .card {
	transition: all .3s ease-in-out;

	&:hover {
		border-color: var(--mid-page-border-primary) !important;
		background-color: var(--mid-page-bg-primary);
	}
}

.point-box {
	order: 5;
	margin-top: 1em;
	column-gap: 0.75em;
	justify-content: center;

	div {
		width: 0.5em;
		height: 0.5em;
		border-radius: 1em;
		background: #C9C9C9;

		&.select {
			width: 1.3em;
			background: var(--color-primary);
		}
	}
}

.modal-box.color-code-rounded-full .content-custom>div {
	border-radius: 50%;
}

.hover-change-img .card {
	&:hover .card-img>*:last-child {
		display: block !important;
	}
}

.hover-btn-outline .card:not(:hover) button {
	background: none !important;
	color: var(--btn-primary) !important;
	border: 1px solid var(--btn-primary) !important;
}

.hover-content-bg-second .card:hover .content {
	background: var(--color-second) !important;
}



// pc跟ipad端样式
@media screen and (min-width: $mb-width) {
	[scrollbar]::-webkit-scrollbar {
		width: 0;
		height: 0;
	}
}



@media screen and (max-width: $mb-width) {
	.tag-box {
		grid-gap: 0.5em;
	}


	.modal-box.tag .tab-box:after {
		width: 150%;
	}
}
</style>
