<template>
	<div id="pie-chart-show-item">
		<div class="pie-chart-container"></div>
		<div class="content">
			<div class="top1"></div>
			<div class="top2"></div>
			<div class="top-l-1">
				<div class="l1"></div>
				<div class="l2"></div>
				<div class="l3"></div>
				<div class="l4"></div>
			</div>
			<div class="top-l-1">
				<div class="l1"></div>
				<div class="l2"></div>
				<div class="l3"></div>
				<div class="l4"></div>
			</div>
			<div class="top-l-1">
				<div class="l1"></div>
				<div class="l2"></div>
				<div class="l3"></div>
				<div class="l4"></div>
			</div>
			<div class="top-l-1">
				<div class="l1"></div>
				<div class="l2"></div>
				<div class="l3"></div>
				<div class="l4"></div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	name: "PieChartShowItem",
	data() {
		return {};
	},
	mounted() {
		let myChart = this.$echarts.init(document.querySelector(".pie-chart-container"));
		// 开始渲染
		myChart.setOption({
			title: {
				text: "Referer of a Website",
				subtext: "Fake Data",
				left: "center",
			},
			tooltip: {
				trigger: "item",
			},
			legend: {
				orient: "vertical",
				left: "left",
			},
			series: [
				{
					name: "Access From",
					type: "pie",
					radius: "50%",
					data: [
						{value: 1048, name: "Search Engine"},
						{value: 735, name: "Direct"},
						{value: 580, name: "Email"},
						{value: 484, name: "Union Ads"},
						{value: 300, name: "Video Ads"},
					],
					emphasis: {
						itemStyle: {
							shadowBlur: 10,
							shadowOffsetX: 0,
							shadowColor: "rgba(0, 0, 0, 0.5)",
						},
					},
				},
			],
		});
	},
};
</script>

<style lang="scss" scoped>
#pie-chart-show-item {
	display: flex;
	width: 100%;
	height: 100%;
}

.pie-chart-container {
	width: 10vw;
	height: 10vw;
	margin-right: 2.6563vw;
	background: #e5e5e5;
	border-radius: 50%;
}

.content {
	.top1,
	.top2 {
		width: 4.3229vw;
		height: 1.0417vw;
		border-radius: 0.3125vw;
	}

	.top1 {
		margin-bottom: 0.2083vw;
		background: #d9d9d9;
	}

	.top2 {
		background: #e5e5e5;
		width: 4.3229vw;
		height: 1.0417vw;
		margin-bottom: 0.9896vw;
	}

	.top-l-1 {
		margin-bottom: 0.625vw;
		display: flex;

		.l1,
		.l2,
		.l3,
		.l4 {
			background: #e5e5e5;
			height: 1.0417vw;
		}

		.l2,
		.l3,
		.l4 {
			border-radius: 0.3125vw;
		}

		.l1 {
			width: 0.7292vw;
			height: 0.7292vw;
			margin-right: 0.7813vw;
		}

		.l2 {
			width: 6.875vw;
			margin-right: 0.3646vw;
		}

		.l3 {
			width: 2.3958vw;
			margin-right: 1.3021vw;
		}

		.l4 {
			width: 5.4167vw;
		}
	}
}
</style>
