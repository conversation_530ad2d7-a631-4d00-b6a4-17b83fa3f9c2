<!--
子标题
参数stepTitle: 黑色加粗标题
参数selectedValue：颜色描述||不加粗标题
参数selectedValue2: 价格描述标题
参数isTitleContent：是否显示颜色and价格  true||显示   false||隐藏
demo:
<half-design-litter-title :index="stepData.id" :selectedValue="shape" :isTitleContent="true" :selectedValue2="shape2" :data-name="`${stepData.styleClass + '_' + stepData.id}`" style="margin: 10px 0" :stepTitle="stepData.minStepTitle" v-show="stepData.attributeTitle">{{ stepData.attributeTitle }} </half-design-litter-title>
-->
<template>
	<div class="litterTitle" :ref="`litterTitle${index}`">
		<span v-show="!noShowTitle && showTitle && stepTitle && stepTitle.length > 0" class="litter-title">{{ stepTitle }}</span>
		<div class="titleContent">
			<slot></slot>
			<span v-show="isTitleContent">
				<span class="litterPlugText" v-show="selectedValue || plugName">{{ ": " + selectedValue || plugName }}</span>
				<span class="priceText redText" v-show="selectedValue2">&nbsp;({{ selectedValue2 || plugName2 }})</span>
			</span>
		</div>
	</div>
</template>

<script>
export default {
	name: "litterTitle",
	components: {},
	props: {
		index: {},
		stepTitle: {
			type: String,
			default: "",
		},
		showTitle: {
			type: Boolean,
			default: true,
		},
		isTitleContent: {
			type: Boolean,
			default: true,
		},
		selectedValue: {
			type: String,
			default: "",
		},
		selectedValue2: {
			type: String,
			default: "",
		},
		showFree: {
			type: Boolean,
			default: true,
		},
	},
	data() {
		return {
			noShowTitle: false,
			plugName: "",
			plugName2: "",
			emitShowFree: false,
		};
	},
	watch: {},
	computed: {},
	methods: {
		setTitlePlug(data) {
			this.emitShowFree = data.emitShowFree;
			this.noShowTitle = data.noShowTitle;
			this.plugName = data.plugName;
			this.plugName2 = data.plugName2;
		},
	},
	created() {},
	async mounted() {
		let onName = this.$refs[`litterTitle${this.index}`].getAttribute("data-name");
		if (onName) this.$Bus.$on(onName, this.setTitlePlug);
	},
	beforeDestroy() {
		let onName = this.$refs[`litterTitle${this.index}`].getAttribute("data-name");
		if (onName) this.$Bus.$off(onName, this.setTitlePlug);
	},
};
</script>

<style scoped lang="scss">
@import "~assets/css/half.scss";

.litterTitle {
	display: flex;
	align-items: center;
	font-weight: bold;
	font-size: 16px;

	.litter-title {
		color: $color-primary;
		margin-right: 6px;
		flex-shrink: 0;
	}

	.titleContent {
		display: flex;
		align-items: center;
		color: #333333;

		.litterPlugText {
			font-weight: 400;
		}

		.priceText {
			font-weight: 400;

			&.redText {
				color: #de3500;
			}
		}
	}

	@include respond-to(mb) {
		font-size: 12px;
	}
}
</style>
