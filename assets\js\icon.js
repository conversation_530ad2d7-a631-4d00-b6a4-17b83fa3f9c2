export const initControlIcon = function () {
	let that = this;
	let dataImage = [require("@/assets/images/controlIcon/zoom.png") /*scale*/, require("@/assets/images/controlIcon/del.png") /*delete*/, require("@/assets/images/controlIcon/rotate.png") /*rotate*/, require("@/assets/images/controlIcon/edit.png") /*move*/];
	let scaleImg = document.createElement("img");
	scaleImg.src = dataImage[0];
	var deleteImg = document.createElement("img");
	deleteImg.src = dataImage[1];
	let rotateImg = document.createElement("img");
	rotateImg.src = dataImage[2];
	let moveImg = document.createElement("img");
	moveImg.src = dataImage[3];

	fabric.Object.prototype.controls.deleteControl = new fabric.Control({
		x: -0.5,
		y: -0.5,
		cursorStyle: "pointer",
		mouseUpHandler: deleteObject,
		render: renderIcon(deleteImg),
		cornerSize: 24,
	});

	fabric.Object.prototype.controls.rotateControl = new fabric.Control({
		x: 0.5,
		y: -0.5,
		cursorStyle: "crosshair",
		actionHandler: fabric.controlsUtils.rotationWithSnapping,
		actionName: "rotate",
		render: renderIcon(rotateImg),
		cornerSize: 24,
		withConnection: true,
	});

	fabric.Object.prototype.controls.scaleControl = new fabric.Control({
		x: 0.5,
		y: 0.5,
		cursorStyle: "se-resize",
		actionHandler: fabric.controlsUtils.scalingEqually,
		actionName: "scale",
		render: renderIcon(scaleImg),
		cornerSize: 24,
		withConnection: true,
	});

	fabric.Object.prototype.controls.moveControl = new fabric.Control({
		x: -0.5,
		y: 0.5,
		cursorStyle: "pointer",
		mouseUpHandler: this.handleEdit,
		render: renderIcon(moveImg),
		cornerSize: 24,
	});

	function deleteObject(eventData, transform) {
		let canvas = transform.target.canvas;
		console.log("work up", canvas);
		if (canvas == null) {
			return;
		}
		let selectedEle = canvas.getActiveObject();

		if (selectedEle.type === "activeSelection") {
			selectedEle.canvas = canvas;
			selectedEle.forEachObject(function (obj) {
				let type = obj.type,
					id = obj.id;
				if (type === "i-text") {
					that.$store.commit("halfDesign/delTextLineById", id);
					//兄弟组件在 components/HalfDesign/ArtworkOptions/Style31 目录下
					that.$Bus.$emit("selectImgColorDelete", {
						input: {
							id,
						},
						upload: {},
					});
				} else if (type === "image") {
					that.$store.commit("halfDesign/delImgLineById", id);
					//兄弟组件在 components/HalfDesign/ArtworkOptions/Style31 目录下
					that.$Bus.$emit("selectImgColorDelete", {
						input: {},
						upload: {
							id,
						},
					});
					//兄弟组件在 components/HalfDesign/customCoins/upload/stepUpload
					that.$Bus.$emit("deleteArtworkImg", id);
				}
				canvas.remove(obj);
			});
			selectedEle.setCoords();
		} else {
			let type = selectedEle.type,
				id = selectedEle.id;
			if (type === "i-text") {
				that.$store.commit("halfDesign/delTextLineById", id);
				//兄弟组件在 components/HalfDesign/ArtworkOptions/Style31 目录下
				that.$Bus.$emit("selectImgColorDelete", {
					input: {
						id,
					},
					upload: {},
				});
			} else if (type === "image") {
				that.$store.commit("halfDesign/delImgLineById", id);
				//兄弟组件在 components/HalfDesign/ArtworkOptions/Style31 目录下
				that.$Bus.$emit("selectImgColorDelete", {
					input: {},
					upload: {
						id,
					},
				});
				console.log("我是删除上传图片", id);
				//兄弟组件在 components/HalfDesign/customCoins/upload/stepUpload
				that.$Bus.$emit("deleteArtworkImg", id);
			}
			canvas.remove(selectedEle);
		}

		canvas.discardActiveObject();
		canvas.requestRenderAll();
	}

	function renderIcon(icon) {
		return function renderIcon(ctx, left, top, styleOverride, fabricObject) {
			var size = this.cornerSize;
			ctx.save();
			ctx.translate(left, top);
			ctx.rotate(fabric.util.degreesToRadians(fabricObject.angle));
			ctx.drawImage(icon, -size / 2, -size / 2, size, size);
			ctx.restore();
		};
	}
};
