<template>
  <div class="mb-4" :class="stepData.styleName">
    <half-design-litter-title
      :index="stepData.id"
      :data-name="`${stepData.styleClass + '_' + stepData.id}`"
      style="margin: 4px 0"
      :stepTitle="stepData.minStepTitle"
      v-show="stepData.attributeTitle"
      >{{ stepData.attributeTitle }}
    </half-design-litter-title>
	<slot name="stepText"></slot>
    <div
      class="step-content"
      v-if="
        !stepData.isHideStep ||
        (stepData.isHideStep && stepData.productParamList.length > 1)
      "
    >
      <div
        class="step-item"
        :class="{ active: index === selectIndex }"
        v-for="(step, index) in stepData.productParamList"
        :key="index"
        @click="selectStep(step, index)"
      >
        <div class="imgWrap" v-show="getImg(step)">
          <labelText
            :labelText="step.labelName"
            :style="{ backgroundColor: step.backgroundColor || '' }"
          >
          </labelText>
          <ToolTip
            class="imgTip"
            :contentClass="contentClass(index)"
            :selfIcon="'icon-fangda4'"
            :area="(index + 1) % 3 == 0 ? 'left' : 'right'"
            :textColor="'rgb(102, 102, 102)'"
            :itemData="{ imgDetail: step.imgUrl }"
          >
          </ToolTip>
          <img :src="getImg(step)" :alt="step.valueName" :title="step.valueName" />
        </div>
        <div class="d-flex-center text-center name">
          <circleBox :active="index === selectIndex"></circleBox>
          <div class="text-truncate">
            <div class="nameText">
              <span class="valueNameText">{{ step[nameProperty] }} </span>
              <div class="questionMark" v-show="step.remark">
                <v-tooltip bottom>
                  <template v-slot:activator="{ on, attrs }">
                    <v-icon v-bind="attrs" v-on="on" size="18px">
                      mdi-help-circle-outline
                    </v-icon>
                  </template>
                  <div class="text-center" style="display: flex; align-items: start">
                    <div
                      style="
                        text-align: center;
                        color: #fff;
                        line-height: 1;
                        font-size: 13px;
                        word-break: break-word;
                        white-space: normal;
                        max-width: 250px;
                      "
                    >
                      {{ step.remark }}
                    </div>
                  </div>
                </v-tooltip>
              </div>
            </div>
            <div class="priceText selfPrice1" :class="{ free: step.freePrice }">
			  <halfPriceBox  :priceData="step" :showUnit="true"></halfPriceBox>
            </div>
            <div class="priceText selfPrice2" :class="{ free: step.freePrice }">
			  <halfPriceBox  :priceData="step" ></halfPriceBox>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="errorTip">
      <v-alert dense outlined type="error">
        {{ langSemiCustom.errorTip }}
      </v-alert>
    </div>
  </div>
</template>

<script>
import { round2 } from "@/utils/utils";
import circleBox from "@/components/HalfDesign/common/circleBox";
import labelText from "@/components/HalfDesign/customMedals/common/labelText";
import ToolTip from "@/components/HalfDesign/customMedals/common/ToolTip";
export default {
  name: "Style1",
  inject: ["getUnit"],
  props: {
    stepData: {
      type: Object,
      default: () => ({}),
    },
  },
  components: { circleBox, labelText, ToolTip },
  data() {
    return {
      selectIndex: -1,
      selectItem: null,
    };
  },
  watch: {},
  computed: {
    nameProperty() {
      let attributeFlag = this.attributeFlag,
        key;
      switch (attributeFlag) {
        case "color":
          key = "colorAlias";
          break;
        default:
          key = "valueName";
          break;
      }
      return key;
    },
    attributeFlag() {
      return this.stepData.attributeFlag;
    },
    shape() {
      return this.selectItem?.valueName;
    },
    langSemiCustom() {
      return this.$store.getters.lang?.semiCustom;
    },
    langQuote() {
      return this.$store.getters.lang.quote || {};
    },
    unit() {
      return this.getUnit();
    },
    symbolCode() {
      return this.$store.state.currency.symbol;
    },
    rate() {
      return this.$store.state.currency.rate;
    },
    proId() {
      return this.$store.state.proId;
    },
  },
  methods: {
    contentClass(index) {
      if ((index + 1) % 3 == 0) return "packImgTip threeBox";
      return "packImgTip";
    },
    getImg(step) {
      let key = "imgDetail";
      return step[key];
    },
    selectStep(item, index = -1, state = false) {
      this.selectIndex = index;
      this.selectItem = item;
      this.$emit("selectStep", {
        type: this.stepData.attributeFlag,
        data: item,
        parent: this.stepData,
        id: this.stepData.id,
        firstSelect: state,
      });
    },
    selectDefault() {
      if (this.stepData.productParamList.length) {
        this.selectStep(this.stepData.productParamList[0], 0, true);
      }
    },
    getPrice(step) {
      let stepCopy = JSON.parse(JSON.stringify(step));
      let priceType = stepCopy.priceType,
        code = this.symbolCode,
        price = 0,
        rate = this.rate;
      if (priceType === 1) {
        price = round2(stepCopy.unitPrice * rate);
        return {
          t: `+${code}${price}/${this.unit}`,
          t2: `+${code}${price}`,
          show: price && price > 0,
        };
      } else {
        return {
          show: false,
        };
      }
    },
    setItemSelfPrice() {
      this.stepData.productParamList.forEach((item) => {
        item.selfPrice = this.getPrice(item)["show"]
          ? this.getPrice(item)["t"]
          : this.langQuote.free;
        item.selfPrice2 = this.getPrice(item)["show"]
          ? this.getPrice(item)["t2"]
          : this.langQuote.free;
        if (!this.getPrice(item)["show"]) item.freePrice = true;
      });
    },
  },
  created() {
    this.setItemSelfPrice();
  },
  mounted() {
    if (this.attributeFlag === "color") {
      this.$Bus.$on("selectDefaultColorStep", this.selectDefault);
    }
    if (this.attributeFlag === "other") {
      this.$Bus.$on("selectDefaultOtherStep", this.selectDefault);
    }
    if (this.attributeFlag === "printMethod") {
      this.$Bus.$on("selectDefaultPrintStep", this.selectDefault);
    }
	if (this.attributeFlag === "beltBuckles") {
      this.$Bus.$on("selectDefaultBeltBucklesStep", this.selectDefault);
    }
  },
  beforeDestroy() {
    this.$Bus.$off("selectDefaultOtherStep", this.selectDefault);
    this.$Bus.$off("selectDefaultColorStep", this.selectDefault);
    this.$Bus.$off("selectDefaultPrintStep", this.selectDefault);
	 this.$Bus.$off("selectDefaultBeltBucklesStep", this.selectDefault);
  },
};
</script>

<style>
.packImgTip {
  transform: translateY(67px);

  /* &.threeBox {
		transform: translate(-180px, 67px);

		@media screen and (max-width: 768px) {
			transform: translate(-80px, 67px);
		}
	} */
}
</style>
<style scoped lang="scss">
@import "~assets/css/half.scss";

.step-content {
  display: grid;

  .step-item {
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    @include step-default;
    min-width: 0;
    cursor: pointer;
    color: #333333;
    overflow: hidden;
    background: #fafafa;
    border-radius: 6px;

    .text-truncate {
      display: flex;
      align-items: center;
      font-weight: 400;
      font-size: 14px;

      .nameText {
        display: flex;
        align-items: center;
        overflow: hidden;
        max-width: 100%;
      }

      .valueNameText {
        text-align: left;
        white-space: normal;
        word-wrap: break-word;
      }
    }

    .imgWrap {
      .imgTip {
        display: none;
        position: absolute;
        top: 0.4em;
        right: 0.4em;
        font-size: 18px;
      }

      .labelText {
        position: absolute;
        top: 0.4em;
        left: 0.4em;
        border-radius: 6px 0px 6px 0px;
      }
    }

    .name {
      margin-top: 4px;
      gap: 8px;
    }

    .questionMark {
      flex-shrink: 0;
      display: none;
    }

    .priceText.selfPrice1 {
      display: none;
    }

    .priceText.selfPrice2 {
      display: none;
    }

    .check-icon {
      display: none;
    }

    .zoom-icon {
      display: none;
    }
  }

  .step-item-bg {
    background-color: #f6f6f6;
  }
}

.style1 .step-content {
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;

  .step-item {
    border-color: transparent;
    background-color: #fafafa;

    @media (any-hover: hover) {
      &:hover {
        color: #333;
        border-color: $color-primary;
      }
    }

    &.active {
      color: #333;
      border-color: $color-primary;

      .check-icon {
        display: flex;
      }
    }

    .imgWrap {
      @include flex-center;

      img {
        aspect-ratio: 200/100;
      }
    }

    .text-truncate {
      flex-direction: column;
      align-items: flex-start;
      justify-content: center;

      .priceText.selfPrice1 {
        display: flex;

        &.free {
          color: #de3500;
        }

        &.redText {
          color: #de3500;
        }
      }
    }
  }
}

.style2 .step-content,
.style2_1 .step-content {
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;

  .step-item {
    border-color: transparent;
    background-color: #fafafa;

    @media (any-hover: hover) {
      &:hover {
        color: #333;
        border-color: $color-primary;
      }
    }

    &.active {
      color: #333;
      border-color: $color-primary;

      .check-icon {
        display: flex;
      }

      .imgTip {
        color: $color-primary;
      }
    }

    .imgWrap {
      @include flex-center;

      .imgTip {
        display: flex;
      }
    }

    .text-truncate {
      flex-direction: column;
      align-items: flex-start;
      justify-content: center;

      .priceText.selfPrice1 {
        display: flex;

        &.free {
          color: #de3500;
        }

        &.redText {
          color: #de3500;
        }
      }
    }
  }
}

.style2_1 .step-content {
  grid-template-columns: repeat(2, 1fr);
  .step-item {
    background: #fff;
    border: 1px solid #dfe5eb;
    @media (any-hover: hover) {
      &:hover {
        padding: 10px;
        border-color: $color-primary;
      }
    }
    .text-truncate {
      .priceText.selfPrice1 {
        display: none;
      }
      .priceText.selfPrice2 {
        display: flex;
      }
    }
  }
}

.style3 .step-content {
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;

  .step-item {
    border-color: transparent;
    background-color: #fafafa;

    @media (any-hover: hover) {
      &:hover {
        color: #333;
        border-color: $color-primary;
      }
    }

    &.active {
      color: #333;
      border-color: $color-primary;

      .check-icon {
        display: flex;
      }
    }

    .imgWrap {
      @include flex-center;
    }

    .text-truncate {
      flex-direction: column;
      align-items: flex-start;
      justify-content: center;

      .priceText.selfPrice1 {
        display: flex;

        &.free {
          color: #de3500;
        }

        &.redText {
          color: #de3500;
        }
      }
    }
  }
}

@include respond-to(mb) {
  .step-content {
    gap: 5px !important;

    .step-item {
      padding: 6px;

      &.active {
        padding: 5px;
      }

      @media (any-hover: hover) {
        &:hover {
          padding: 5px;
        }
      }

      .text-truncate {
        font-size: 12px;
        row-gap: 2px !important;
        white-space: normal;

        .valueNameText {
          white-space: normal;
          word-wrap: break-word;
        }
      }

      .labelText {
        top: 0.2em;
        left: 0.2em;
      }
    }
  }

  .style1 .step-content {
    grid-template-columns: repeat(2, 1fr);

    .step-item {
      padding: 10px;
      flex-direction: row-reverse;
      min-height: 86px;

      &.active {
        padding: 9px;
      }

      @media (any-hover: hover) {
        &:hover {
          padding: 9px;
        }
      }
    }
  }

  .style2 .step-content,
  .style2_1 .step-content {
    .step-item {
      padding: 10px;

      &.active {
        padding: 9px;
      }

      .imgTip ::v-deep {
        top: 0.2em;
        right: 0.2em;

        b,
        i {
          font-size: 14px !important;
        }
      }

      .labelText {
        top: 0.2em;
        right: 0.2em;
      }

      @media (any-hover: hover) {
        &:hover {
          padding: 9px;
        }
      }
    }
  }

  .style3 .step-content {
    .step-item {
      padding: 10px;

      &.active {
        padding: 9px;
      }

      .labelText {
        top: 0.2em;
        right: 0.2em;
      }

      @media (any-hover: hover) {
        &:hover {
          padding: 9px;
        }
      }
    }
  }
}
</style>
