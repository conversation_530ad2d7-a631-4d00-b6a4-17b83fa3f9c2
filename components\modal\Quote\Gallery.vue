<template>
	<div class="galleryProductListWrap modal-box">
		<EditDiv v-for="(item, index) in modal.list" :key="index" :tagName="item.title.tagName || 'h1'"
			v-model:content="item.title.value" v-if="item.title?.value"
			@click="setModalType(item.title, modal.list, 'text')" />
		<div class="filter">
			<div>
				{{ langSemiCustom.filters }}
				<span style="color: #999999;font-style: italic;">({{ total }} {{ langSemiCustom.results }})</span>
			</div>
			<div class="dropdown" v-for="(item, index) in cateList" :key="item.id">
				<div class="dropdownTrigger" @click.stop="showExtend(item.id)"
					:class="{ active: showCateExtend.includes(item.id) || canShowCateFatherId.includes(item.id) }">
					{{ item.name }}<b class="icon-Down"></b>
				</div>
				<div class="dropdownList custom-scrollbar" v-show="showCateExtend.includes(item.id)">
					<div class="dropdownItem" :class="{ active: hasActive(citem.id) }" v-for="citem in item.childList"
						:key="citem.id" @click.stop="updateTag(citem)">
						{{ citem.name }}
					</div>
				</div>
			</div>
		</div>
		<div class="productList">
			<div class="productItem" v-for="(item, index) in productList" :key="index" @click="showPrev(index)">
				<Pic :src="item.coverImg" :alt="item.title" :title="item.title" />
			</div>
		</div>
		<div class="noDataImg" v-show="productList?.length <= 0">
			<div class="showNodataPic" v-show="showNodataPic">
				<Pic
					src="https://static-oss.gs-souvenir.com/web/quoteManage/20241125/6a4905d3-f989-4fcc-8245-faeedbf967aa_2060SbFQHm.png" />
				<div class="noDataText">
					<p>{{ langQuote.coins.gallery.noResults }}</p>
					<p>{{ langQuote.coins.gallery.pleaseTry }}</p>
				</div>
			</div>
			<div class="showLoading" v-show="!showNodataPic">
				<Loading></Loading>
			</div>
		</div>
		<div class="loadProgress" v-show="loadingProduct">
			<Loading></Loading>
		</div>
		<div class="loadMore" v-show="!loadingProduct && formData?.page < pages">
			<button primary plain @click="viewMore" title="View More Enamel Pins">{{ langSemiCustom.loadMore }}</button>
		</div>
		<base-dialog persistent v-model="dialog" class="productDialog" width="auto">
			<div class="content">
				<div class="swiper myswiper1" ref="swiper1">
					<div class="swiper-wrapper" style="align-items: center;background-color: #fff;">
						<div class="swiper-slide" v-for="(item, index) in productList" :key="index">
							<img :src="item.previewImg" :alt="item.title" :title="item.title" />
						</div>
					</div>
				</div>
				<div class="swiper-button-next"></div>
				<div class="swiper-button-prev"></div>
				<div class="textContent">
					<h2>{{ currentProductItem.title }}</h2>
					<p>{{ currentProductItem.description }}</p>
					<div class="btnGroup">
						<!-- <button primary plain class="viewMore" title="View Details of Custom Enamel Pins" @click="toDetail">View
            more</button> -->
						<button primary class="quote" title="order custom enamel pins" @click="openQuote">{{
							langQuote.coins.gallery.quoteDesgin }}</button>
					</div>
				</div>
			</div>
		</base-dialog>
	</div>
</template>

<script>
import { getCateList, getProductGalleryList } from "@/api/web";
import { getTagsList, getImagesList } from "@/api/neon/neon";
import Pic from "@/components/pic.vue";
import BaseDialog from "@/components/Quote/BaseDialog.vue";

class FormModal {
	constructor(data) {
		let defaultData = {
			page: 1,
			pageSize: 20,
			cateId: 0,
		};
		let newData = Object.assign({}, defaultData, data);
		this.page = newData.page;
		this.pageSize = newData.pageSize;
		this.cateId = newData.cateId;
	}

	toJSON() {
		return {
			page: this.page,
			pageSize: this.pageSize,
			cateId: this.cateId,
		};
	}
}

export default {
	components: { BaseDialog, Pic },
	async fetch() {
	},
	data() {
		return {
			isManage: false,
			mySwiper: null,
			dialog: false,
			loadingProduct: false,
			showCateExtend: [],
			canShowCateFatherId: [],
			currentProductIndex: 0,
			currentProductItem: {},
			selectedParamsObj: {},
			modal: {
				style: {},
				type: {},
				...this.data,
			},
			formData: {},
			cateList: [],
			productList: [],
			total: 0,
			pages: 1,
			showNodataPic: false
		};
	},
	props: {
		data: {
			type: Object,
			default: {},
		},
	},
	watch: {
		modal: {
			handler(val) {
				if (process.env.isManage) this.$emit("update:data", val);
			},
			immediate: true,
			deep: true,
		},
	},
	computed: {
		langQuote() {
			return this.$store.getters.lang.quote || {};
		},
		langSemiCustom() {
			return this.$store.getters.lang?.semiCustom;
		},
		langDesign() {
			return this.$store.getters.lang?.design;
		},
	},
	methods: {
		openQuote() {
			let url = "/quote/business-cards?paramId=674&type=quoteIframe"
			if (this.currentProductItem.quoteCateRoutingName) {
				const galleryPic = [
					this.currentProductItem.coverImg,
					this.currentProductItem.backImg
				].filter(Boolean);
				const connector = this.currentProductItem.quoteCateRoutingName.includes('?') ? '&' : '?';
				const params = new URLSearchParams({
					type: 'quoteIframe',
					galleryPic: JSON.stringify(galleryPic)
				});
				url = `${this.currentProductItem.quoteCateRoutingName}${connector}${params.toString()}`
			}
			this.$store.commit("setMask", {
				modal: "modalQuoteDialog",
				quoteUrl: url,
			});
		},
		// toDetail() {
		//   this.$router.push({
		//     path: `/products/${this.currentProductItem.pageUrl}`,
		//   });
		// },
		initSwiper() {
			let that = this;
			this.mySwiper = new Swiper(this.$refs.swiper1, {
				slidesPerView: 1,
				spaceBetween: 8,
				watchSlidesVisibility: true, //防止不可点击
				grabCursor: true,
				on: {
					slideChangeTransitionEnd: function (value) {
						that.currentProductItem = that.productList[value.activeIndex];
					},
				},
				navigation: {
					nextEl: ".content .swiper-button-next",
					prevEl: ".content .swiper-button-prev",
				},
			});
			this.mySwiper.slideTo(this.currentProductIndex);
		},
		showPrev(index) {
			this.dialog = true;
			this.currentProductIndex = index;
			this.currentProductItem = this.productList[index];
			this.$nextTick(() => {
				this.initSwiper();
			});
		},
		async updateTag(item) {
			let val = this.selectedParamsObj[item.parentId];
			if (!val) {
				this.$set(this.selectedParamsObj, item.parentId, [item]);
				// this.selectedParamsObj[item.fatherId] = [item];
			} else {
				let findIndex = val.findIndex((v) => {
					return v.id == item.id;
				});
				if (findIndex > -1) {
					//重复选择暂不处理
				} else {
					//选择新的替换掉旧的
					// this.selectedParamsObj[item.parentId] = [];
					// this.selectedParamsObj[item.parentId] = [item];
					this.$set(this.selectedParamsObj, item.parentId, []);
					this.$set(this.selectedParamsObj, item.parentId, [item]);
				}
			}
			if (item.id >= 0) this.canShowCateFatherId.push(item.parentId);
			if (item.id < 0) this.canShowCateFatherId = this.canShowCateFatherId.filter(citem => citem != item.parentId);
			this.closeBox()
			let listData = await this.getProduct(1);
			this.productList = listData.data.records || []
		},
		showExtend(id) {
			if (this.showCateExtend.length > 0) {
				if (this.showCateExtend.includes(id)) {
					this.closeBox()
					return
				}
			}
			this.closeBox()
			this.showCateExtend.push(id)
		},
		hasActive(id) {
			let allCateIds = this.getAttributeValueIds()
			return allCateIds.includes(id)
		},
		async viewMore() {
			let formData = this.formData;
			if (formData.page >= this.pages) {
				return;
			}
			formData.page += 1;
			let listData = await this.getProduct();
			this.productList = this.productList.concat(listData.data.records);
		},
		closeBox(e) {
			this.showCateExtend = []
		},
		getAttributeValueIds() {
			let tagData = [];
			for (let key in this.selectedParamsObj) {
				if (this.selectedParamsObj.hasOwnProperty(key)) {
					let val = this.selectedParamsObj[key].map((item) => item.id);
					if (val && val.length > 0) {
						tagData.push(...val);
					}
				}
			}
			return tagData;
		},
		getProduct(customPage) {
			return new Promise((resolve) => {
				this.loadingProduct = true;
				let ids = this.getAttributeValueIds().filter(item => item >= 0);
				getImagesList({
					page: customPage || this.formData?.page,
					pageSize: this.formData.pageSize,
					tagsIds: ids,
				})
					.then((res) => {
						this.loadingProduct = false;
						this.total = res.data.total;
						this.pages = res.data.pages;
						this.formData.page = res.data.current;
						resolve(res);
					})
					.catch((error) => {
						if (error.name === "AbortError") {
							console.log("Request canceled:", error.message);
						}
					}).finally(() => {
						this.loadingProduct = false;
					});
			});
		},
		/**
		 * 监听点击事件，当点击发生在指定元素之外时调用回调函数。
		 *
		 * @param {Element|Element[]} elements - 单个元素或元素数组。
		 * @param {Function} callback - 当点击发生在元素之外时调用的回调函数。
		 * @param {string} [eventType='click'] - 监听的事件类型，默认为 'click'。
		 * @returns {Function} - 用于移除事件监听器的函数。
		 */
		clickOutside(elements, callback, eventType = 'click') {
			// 确保 elements 是一个数组
			if (!Array.isArray(elements)) {
				elements = [elements];
			}
			// 确保 elements 中的每个元素都是有效的 DOM 元素
			elements = elements.filter(element => element instanceof Element);
			function handleClickOutside(event) {
				// 检查事件目标是否在元素外部
				if (event.target && !elements.some(element => element.contains(event.target))) {
					callback(event);
				}
			}
			// 添加事件监听器
			document.addEventListener(eventType, handleClickOutside);
			// 返回一个函数，用于移除事件监听器
			return function removeClickListener() {
				document.removeEventListener(eventType, handleClickOutside);
			}
		},
		setModalType(target, targetArray, clickType, event, other) {
			this.$setModal(this, target, targetArray, clickType, event, other);
		},
	},
	async mounted() {
		try {
			if (process.env.isManage) {
				this.isManage = true;
				return false;
			}
			let cateListData = await getTagsList();
			this.cateList = cateListData.data || []
			this.cateList = this.cateList.map((item, index) => {
				if (Array.isArray(item.childList) && item.childList?.length > 0) {
					let customId = -1 - index;
					let newItem = Object.assign({}, item.childList[0], { id: customId, name: this.langDesign.all });
					item.childList.unshift(newItem)
				}
				return item
			});
			let formData = new FormModal();
			let listData = await getImagesList(formData);
			this.formData = formData
			this.showNodataPic = true
			this.productList = listData.data.records || [];
			this.total = listData.data.total || 0
			this.pages = listData.data.pages || 1
		} catch (e) {
			console.log(e, '出错');
		}
		let dropdowns = document.querySelectorAll('.dropdown')
		if (dropdowns && dropdowns?.length > 0) {
			this.clickOutside([...dropdowns], this.closeBox);
		}
	},
};
</script>

<style lang="scss" scoped>
button[primary][plain] {
	background-image: none;
	background-color: #ffffff;
	color: $color-primary;
	border: 1px solid $color-primary;
}

.galleryProductListWrap {
	z-index: auto;
	height: 100%;
	padding-top: 1.5rem !important;
	padding-bottom: 5rem !important;

	h1 {
		margin-bottom: 1.8rem;
		text-align: center;
	}

	.filter {
		display: flex;
		flex-wrap: wrap;
		gap: 1.88rem;
		height: 3.13rem;
		background-color: #F4F5F7;
		align-items: center;
		padding: 0 1.19rem;
		margin-bottom: 1.25rem;

		@include respond-to(mb) {
			background-color: transparent;
			height: auto;
			padding: 0;
			gap: 1.25rem;
		}

		.dropdown {
			cursor: pointer;

			@include respond-to(mb) {
				padding: 0.5rem 1rem;
				background: #f4f5f7;
				border-radius: 1.25rem;
			}

			.dropdownTrigger {
				display: flex;
				align-items: center;
				justify-content: center;
				gap: 0.5em;
			}

			.dropdownTrigger.active {
				color: $color-primary;

				b.icon-Down {
					transform: rotate(180deg);
				}
			}

			.dropdownList {
				position: absolute;
				width: fit-content;
				min-width: 10.5rem;
				max-width: 15rem;
				background: #ffffff;
				border-radius: 4px;
				border: 1px solid #ccc;
				padding: 0.5rem 0;
				margin-left: -2rem;
				margin-top: 0.5rem;
				box-shadow: 0 1rem 1rem 0 rgba(0, 0, 0, 0.44);
				overflow: hidden auto;
				max-height: 460px;

				@include respond-to(mb) {
					max-height: 290px;
				}

				.dropdownItem {
					padding: 0.5rem 1rem;

					&:hover {
						background-color: #f2f2f2;
					}

					&.active {
						color: $color-primary;
					}
				}
			}
		}

		.icon-Down {
			font-size: 0.5rem;
			line-height: initial;
		}
	}

	.productList {
		display: grid;
		grid-template-columns: repeat(4, 1fr);
		gap: 1.25rem;

		@include respond-to(mb) {
			grid-template-columns: repeat(2, 1fr);
			gap: 0.7rem;
		}

		.productItem {
			aspect-ratio: 1;
			cursor: pointer;
			min-width: 0;
			border: 1px solid #C7C7C7;
		}
	}

	.noDataImg {
		width: 25rem;
		margin: 2em auto 4em;
		text-align: center;
		font-size: 16px;
		position: relative;

		.noDataText {
			margin-top: 2em;
		}

		@include respond-to(mb) {
			font-size: 12px;
			width: 64vw;
		}
	}

	.loadMore {
		display: flex;
		justify-content: center;
		align-items: center;
		margin-top: 2rem;

		button {
			width: 13.75rem;
		}
	}
}

.productDialog {
	z-index: 1000;

	::v-deep .base-dialog-model-con {
		background-color: transparent;
	}

	::v-deep .icon-guanbi {
		position: absolute;
		top: -50px !important;
		right: -50px !important;

		@include respond-to(mb) {
			right: 0 !important;
			top: -4rem !important;
		}
	}

	.content {
		width: 31.25rem;
		background-color: transparent;
		border-radius: 0.38rem;
		text-align: center;

		.textContent {
			background-color: #fff;

			h2 {
				&::before {
					display: none;
				}

				&::after {
					display: none;
				}
			}
		}

		@include respond-to(mb) {
			width: 30rem;
		}


		.swiper-button-next,
		.swiper-button-prev {
			display: flex;
			justify-content: center;
			align-items: center;
			background-color: rgba(255, 255, 255, 0.5);
			border-radius: 50%;
			width: 3.38rem;
			height: 3.38rem;
			color: #ffffff;
		}

		.swiper-button-next {
			top: 40%;
			right: -80px;

			@include respond-to(mb) {
				top: auto;
				bottom: -5rem;
				right: 30%;
			}
		}

		.swiper-button-prev {
			top: 40%;
			left: -80px;

			@include respond-to(mb) {
				top: auto;
				bottom: -5rem;
				left: 30%;
			}
		}

		.swiper-button-next::after {
			content: "";
			font-family: "modalicon";
			font-size: 1rem;
		}

		.swiper-button-prev::after {
			content: "";
			font-family: "modalicon";
			font-size: 1rem;
		}

		h2 {
			margin: 0;
			padding: 1.81rem 0 1.08rem;
			font-size: 1.13rem;
		}

		p {
			padding: 0 0.5rem;
		}

		.btnGroup {
			display: flex;
			justify-content: center;
			align-items: center;
			gap: 1rem;
			margin-top: 1.63rem;
			padding-bottom: 1rem;

			button {
				min-width: auto;
				font-size: 1rem;
				height: 2.5rem;
				padding: 0;
			}

			.viewMore {
				width: 11.25rem;
			}

			.quote {
				width: 15.63rem;
			}
		}
	}
}

.loadProgress,
.showLoading {
	position: absolute;
	bottom: 16px;
	left: 50%;
	transform: translateX(-50%) scale(1.2);
	z-index: 10;

	@include respond-to(mb) {
		left: 50%;
		transform: translateX(-50%);
	}
}

.showLoading {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
}

.custom-scrollbar::-webkit-scrollbar {
	/*滚动条整体样式*/
	width: 5px;

	/*高宽分别对应横竖滚动条的尺寸*/
	height: 1px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
	/*滚动条里面小方块*/
	border-radius: 10px;
	background: #d3d3d3;
}

.custom-scrollbar::-webkit-scrollbar-track {
	/*滚动条里面轨道*/
	border-radius: 10px;
}
</style>
