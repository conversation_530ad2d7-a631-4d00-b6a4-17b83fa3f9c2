<template>
	<div class="mb-4" :class="stepData.styleName">
		<div class="step-content">
			<div v-for="(step, index) in stepData.productParamList" :key="index">
				<div class="step-item" :class="{ active: index === selectIndex }" @click="selectStep(step, index)">
					<div class="prepend">{{ step.valueName }}</div>
					<!-- <v-icon color="#ffffff" small> mdi-check </v-icon> -->
				</div>
			</div>
		</div>
		<div class="errorTip">
			<v-alert dense outlined type="error">
				{{ langSemiCustom.errorTip }}
			</v-alert>
		</div>
	</div>
</template>
<script>
export default {
	props: {
		stepData: {
			type: Object,
			default: {},
		},
	},
	data() {
		return {
			selectIndex: -1,
			selectItem: null,
		};
	},
	computed: {
		langSemiCustom() {
			return this.$store.getters.lang?.semiCustom;
		},
	},
	methods: {
		selectStep(item, index, state = false) {
			this.selectIndex = index;
			this.selectItem = item;
			this.$emit("selectStep", {
				type: this.stepData.attributeFlag,
				data: item,
				parent: this.stepData,
				id: this.stepData.id,
				firstSelect: state,
			});
		},
		selectDefault() {
			if (this.selectIndex <= -1) {
				this.selectStep(this.stepData.productParamList[0], 0, true);
			}
		},
		uploadQty() {
			if (this.stepData && this.stepData.productParamList.length > 0) {
				let list = this.stepData.productParamList;
				if (this.selectIndex < 0) return;
				list.map((item) => {
					if (item.id == this.selectItem.id) {
						this.$Bus.$emit("uploadQty", item);
					}
				});
			}
		},
	},
	mounted() {
		this.$Bus.$on("selectDefaultColorStep", this.selectDefault);
		this.$Bus.$on("updateQty", this.uploadQty);
	},
	watch: {},
	beforeDestroy() {
		this.$Bus.$off("selectDefaultColorStep");
		this.$Bus.$off("updateQty");
	},
};
</script>
<style scoped lang="scss">
@import "~assets/css/half.scss";

.style1 .step-content {
	display: grid;
	grid-template-columns: repeat(auto-fill, 45px);
	grid-gap: 20px;

	.step-item {
		min-width: 0;
		position: relative;
		@include flex-center;
		border-radius: 50%;
		cursor: pointer;
		aspect-ratio: 1;
		border: 1px solid #ccc;
		.prepend {
			font-size: 12px;
		}
		.v-icon {
			display: none;
		}

		&::before {
			display: none;
			content: "";
			position: absolute;
			left: -6px;
			top: -6px;
			right: -6px;
			bottom: -6px;
			border-radius: 50%;
			border: 2px solid $color-primary;
		}

		@media (any-hover: hover) {
			&:hover {
				&::before {
					display: block;
				}

				.v-icon {
					display: block;
				}
			}
		}
	}

	.step-item.active {
		&::before {
			display: block;
		}

		.v-icon {
			display: block;
		}
	}
}

@include respond-to(mb) {
	.style1 .step-content {
		grid-template-columns: repeat(auto-fill, 45px);
	}
}
</style>
