<template>
	<div class="lanyardProDetail">
		<h2 class="lanyardProDetailTitle">Fabrication Options for Custom Lanyards</h2>
		<div class="detailInfoBox" v-for="(detail, ind) in detailInfoListArr" :key="ind">
			<div class="switchBox">
				<div class="switchItem" @click="selectItemFn(ind, item)" :class="{ active: selectItem[ind]?.id == item.id }" v-for="(item, index) in detail" :key="item.id">
					<div class="infoTitle">{{ item.alias }}</div>
				</div>
			</div>
			<div class="contentBox" v-show="selectItem[ind]">
				<component :is="selectItem[ind]?.styleClass" :itemData="selectItem[ind]"></component>
			</div>
		</div>
		<div class="accessoryParam" v-show="accessoryQuoteParam && accessoryQuoteParam.length > 0">
			<div class="switchBox">
				<div class="switchItem" @click="selectItemFn2(index)" :class="{ active: selectItem22?.id == item.id }" v-for="(item, index) in accessoryQuoteParam" :key="item.id">
					<div class="infoTitle">{{ item.alias }}</div>
				</div>
			</div>
			<div class="contentBox" v-if="selectItem22">
				<component :is="selectItem22?.styleClass" :itemData="selectItem22"></component>
			</div>
		</div>
	</div>
</template>

<script>
import Size from "@/components/lapelPins/components/lanyard/Sizes.vue";
import Colors from "@/components/lapelPins/components/lanyard/Colors.vue";
import Attachment from "@/components/lapelPins/components/lanyard/Attachment.vue";
import Stitch from "@/components/lapelPins/components/lanyard/Stitch.vue";
import Options from "@/components/lapelPins/components/lanyard/Options.vue";
import Packaging from "@/components/lapelPins/components/lanyard/Packaging.vue";
import TransparentBadge from "@/components/lapelPins/components/lanyard/TransparentBadge.vue";
import PremiumLeather from "@/components/lapelPins/components/lanyard/PremiumLeather.vue";
import { getLanyardsAccessoryQuoteParam } from "@/api/quote/fdCommon.js";

export default {
	name: "lanyardProDetail",
	components: { Size, Colors, Attachment, Stitch, Options, Packaging, TransparentBadge, PremiumLeather },
	props: {
		detailInfoList: {
			type: Array,
			default: () => [],
		},
		quotePid: {
			type: [String, Number],
			default: "",
		},
		quoteCateId: {
			type: [String, Number],
			default: "",
		},
	},
	data() {
		return {
			accessoryQuoteParam: null,
			selectItem: {},
			selectItem22: null,
			selectIndex22: -1,
		};
	},
	watch: {
		detailInfoListArr(newVal) {
			if (newVal) {
				this.detailInfoListArr.forEach((item, index) => {
					this.selectItem[index] = item[0];
				});
			}
		},
	},
	created() {},
	mounted() {
		this.getLanyardsAccessoryQuoteParamFn();
	},
	methods: {
		selectItemFn(ind, item) {
			if (!this.detailInfoList.length) return;
			this.selectItem[ind] = item;
			this.$set(this.selectItem, ind, item);
			this.$forceUpdate();
		},
		selectItemFn2(index) {
			if (!this.accessoryQuoteParam.length) return;
			this.selectItem22 = this.accessoryQuoteParam[index];
			this.selectIndex22 = index;
		},
		getLanyardsAccessoryQuoteParamFn() {
			let styleMap = {
				"Transparent Badge": "TransparentBadge",
				"Premium Leather": "PremiumLeather",
				"Silicone Holder": "PremiumLeather",
				"PVC Card": "PremiumLeather",
				"Badge Reel": "PremiumLeather",
			};
			getLanyardsAccessoryQuoteParam({ cateId: this.quoteCateId, currency: this.$store.state.currency.code }).then((res) => {
				this.accessoryQuoteParam = res.data.map((item) => {
					for (let key in styleMap) {
						if (item.paramName && item.paramName.includes(key)) {
							item.styleClass = styleMap[key];
							break;
						}
					}
					return item;
				});
				this.selectItemFn2(0);
			});
		},
		getThreeItem(arr) {
			let temp = arr.reduce((acc, item, index) => {
				const chunkIndex = Math.floor(index / 3);
				if (!acc[chunkIndex]) {
					acc[chunkIndex] = [];
					this.$set(this.selectItem, chunkIndex, null);
				}
				acc[chunkIndex].push(item);
				return acc;
			}, []);
			return temp;
		},
	},
	computed: {
		isMobile() {
			return this.$store.getters.isMobile;
		},
		detailInfoListArr() {
			if (!this.isMobile) {
				return [this.detailInfoList];
			} else {
				return this.getThreeItem(this.detailInfoList);
			}
		},
	},
};
</script>
<style scoped lang="scss">
.lanyardProDetail {
	.lanyardProDetailTitle {
		font-weight: bold;
		font-size: 32px;
		color: #333333;
		margin-bottom: 20px;
		text-transform: none;
	}
	.switchBox {
		position: relative;
		height: 50px;
		border-radius: 25px;
		background-color: #f6f6f6;
		display: flex;
		align-items: center;
		justify-content: space-around;
		overflow: auto hidden;
		padding-bottom: 6px;
		.switchItem {
			width: fit-content;
			height: 100%;
			position: relative;
			color: #333;
			font-weight: bold;
			font-size: 18px;
			cursor: pointer;
			display: flex;
			align-items: center;
			&.active {
				color: #ff764a;
				&::before {
					display: block;
				}
				&::after {
					display: block;
				}
			}
			&::after {
				display: none;
				position: absolute;
				left: 0;
				bottom: 1px;
				content: "";
				width: 100%;
				height: 3px;
				border-radius: 2px;
				background: #ff764a;
			}
			@include respond-to(mb) {
				font-size: 13px;
				padding: 0 6px;
				text-align: center;
				&::before {
					display: none;
					position: absolute;
					left: 50%;
					bottom: -4px;
					transform: translateX(-50%);
					content: "";
					width: 0;
					height: 0;
					border-left: 8px solid transparent;
					border-right: 8px solid transparent;
					border-top: 6px solid #ff764a;
					z-index: 1;
				}
			}
		}
	}
	.contentBox {
		width: 100%;
		overflow: hidden;
		margin: 20px 0 50px;
	}
	@include respond-to(mb) {
		margin: 0 -3vw;
		.lanyardProDetailTitle {
			font-size: 16px;
			padding: 0 3vw;
			width: 100%;
		}
		.detailInfoBox {
			padding: 0 3vw;
			width: 100%;
			.switchBox {
				background-color: #fff !important;
				border-radius: 0;
				&::after {
					content: "";
					position: absolute;
					left: 0;
					bottom: 6px;
					width: 100%;
					height: 1px;
					background-color: #dfdfdf;
				}
			}
		}

		.accessoryParam {
			width: 100%;
			padding: 10px 3vw 0;
			background-color: #f7f7f7;
			.switchBox {
				background-color: #f7f7f7 !important;
				border-radius: 0;
				&::after {
					content: "";
					position: absolute;
					left: 0;
					bottom: 6px;
					width: 100%;
					height: 1px;
					background-color: #dfdfdf;
				}
			}
		}

		.contentBox {
			margin: 10px 0 20px;
		}
	}
}
</style>
