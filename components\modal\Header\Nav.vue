<template>
	<div class="nav-link" v-if="!nav.linkUrl" :title="nav.name" :style="{ color: nav.nameColor }">
		<span v-if="nav.name">{{ nav.name }}</span>
		<pic v-if="nav.imgUrl" :src="nav.imgUrl" :alt="nav.imgAlt"></pic>
		<p v-if="nav.labelText" :style="{ background: nav.labelColor }">{{ nav.labelText }}</p>
	</div>

	<n-link class="nav-link" v-else-if="nav.linkUrl.startsWith('/')" :title="nav.name" :style="{ color: nav.nameColor }"
		tag="a" :to="(lang ? lang + '/' : '') + nav.linkUrl">
		<span v-if="nav.name">{{ nav.name }}</span>
		<pic v-if="nav.imgUrl" :src="nav.imgUrl" :alt="nav.imgAlt"></pic>
		<p v-if="nav.labelText" :style="{ background: nav.labelColor }">{{ nav.labelText }}</p>
	</n-link>

	<a class="nav-link" v-else :href="nav.linkUrl || ''" :title="nav.name" :style="{ color: nav.nameColor }"
		:target="regex.test(nav.linkUrl) ? '_blank' : ''">
		<span v-if="nav.name">{{ nav.name }}</span>
		<pic v-if="nav.imgUrl" :src="nav.imgUrl" :alt="nav.imgAlt"></pic>
		<p v-if="nav.labelText" :style="{ background: nav.labelColor }">{{ nav.labelText }}</p>
	</a>
</template>


<script>
export default {
	name: "Nav",
	props: {
		nav: {
			type: Object,
			default() {
				return {}
			}
		}
	},
	data() {
		return {
			regex: /^(http|https):\/\/[\w\-]+(\.[\w\-]+)+([\w\-\.,@?^=%&:/~\+#]*)?$/
		}
	},
	computed: {
		lang() {
			return this.$store.state.language.lang
		}
	}
};
</script>
