<template>
	<div id="table-show-item">
		<div class="item item-1"></div>
		<div class="item-2">
			<div class="icon"></div>
			<span class="content"></span>
		</div>
		<div class="item item-3"></div>
		<div class="item item-4"></div>
		<div class="item item-5"></div>
		<div class="item item-6"></div>
		<div class="item item-7"></div>
	</div>
</template>

<script>
export default {
	name: "TableShowItem",
	data() {
		return {};
	},
};
</script>

<style lang="scss" scoped>
#table-show-item {
	width: 100%;
	display: flex;
	align-items: center;
}
.item {
	background: #e5e5e5;
	border-radius: 0.3125vw;
	height: 1.5625vw;
}
.item-1 {
	width: 3.0729vw;
	margin-right: 3.0729vw;
}
.item-2 {
	width: 8.0208vw;
	margin-right: 2.9688vw;
	padding-left: 0.4688vw;
	display: flex;
	justify-content: space-between;
	align-items: center;
	.icon {
		width: 2.3958vw;
		height: 2.3958vw;
		background: #e5e5e5;
		border-radius: 50%;
	}
	.content {
		width: 4.4792vw;
		height: 1.5625vw;
		background: #e5e5e5;
		border-radius: 0.3125vw;
	}
}

.item-3 {
	width: 5.5208vw;
	margin-right: 1.7188vw;
}
.item-4 {
	width: 4.9479vw;
	margin-right: 1.5625vw;
}
.item-5 {
	width: 6.5625vw;
	margin-right: 1.875vw;
}
.item-6 {
	width: 5.1563vw;
	margin-right: 51px;
}
.item-7 {
	width: 7.0833vw;
}
</style>
