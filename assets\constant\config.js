export const fullWebsiteList = [
	{
		iconPath: "icon-a-nave-Dashboardzhuanhuan",
		urlTitle: "Dashboard",
		permissionName: "manage-dashboard",
	},
	{
		iconPath: "icon-a-nave-Orderszhuanhuan",
		urlTitle: "Orders",
		permissionName: "manage-orders",
	},
	{
		iconPath: "icon-a-nave-Inquirieszhuanhuan",
		urlTitle: "Inquiries",
		permissionName: "manage-inquires"
	},
	{
		iconPath: "icon-a-nave-customermanagementzhuanhuan",
		urlTitle: "Customers",
		permissionName: "manage-customersManagement",
	},
	{
		iconPath: "icon-a-icon-nave1zhuanhuan",
		urlTitle: "Procurement",
		permissionName: "manage-procurement",
		childList:[
			{
				urlTitle: "Billing",
				permissionName: "manage-procurement-billing"
			},
			{
				urlTitle: "3D Modeling",
				permissionName: "manage-procurement-threeDModule",
			}
		]
	},
	{
		iconPath: "icon-a-nave-SiteEditzhuanhuan",
		urlTitle: "Site Edit",
		permissionName: "manage-siteEdit",
		childList:[
			{
				urlTitle: "Product Edit",
				permissionName: "manage-siteEdit-productEdit",
			},
			{
				urlTitle: "Content Edit",
				permissionName: "manage-siteEdit-contentEdit",
			},
			{
				urlTitle: "Commodity Warehouse",
				permissionName: "manage-siteEdit-commodityWarehouse"
			},
			{
				urlTitle: "Edit Log",
				permissionName: "manage-siteEdit-contentEditLog",
			},
			// {
			// 	urlTitle: "logImportExport",
			// 	type: 1,
			// 	permissionName: "logImportExport",
			// }
		]
	},
	{
		iconPath: "icon-a-nave-StatisticalAnalysiszhuanhuan",
		urlTitle: "Analysis",
		permissionName: "manage-analysis",
	},
	{
		iconPath: "icon-a-nave-BasicSettingszhuanhuan",
		urlTitle: "Basic Settings",
		permissionName: "manage-set"
	}
]
export const fullAccountWebsiteList = [
	{
		// iconPath: "icon-a-nave-Dashboardzhuanhuan",
		urlTitle: "Account Setting",
		permissionName: "manage-account-accountSetting",
	},
	{
		// iconPath: "icon-a-nave-Orderszhuanhuan",
		urlTitle: "Billing History",
		permissionName: "manage-account-billingHistory",
	},
	{
		// iconPath: "icon-a-nave-Inquirieszhuanhuan",
		urlTitle: "Payment Methods",
		permissionName: "manage-account-paymentMethods"
	},
	{
		// iconPath: "icon-a-nave-customermanagementzhuanhuan",
		urlTitle: "Premium Subscriptions",
		permissionName: "manage-account-premiumSubscriptions",
	},
	{
		// iconPath: "icon-a-nave-customermanagementzhuanhuan",
		urlTitle: "Premium Plan",
		permissionName: "manage-account-premiumPlan",
	},
	{
		// iconPath: "icon-a-nave-customermanagementzhuanhuan",
		urlTitle: "3D Product Modeling",
		permissionName: "manage-account-3dProductMolding",
	}
	]
// dashboard页面相关数据

export const InquiriesStatistics = {
	grid: {
		width: '',
		left: '0',
		bottom: '0',
		containLabel: true,
		trigger: 'axis',
		tooltip: {
			formatter: (data) => {
				// console.log('this', this, data);
				let res = `<div style="width: 209px;background: rgba(255,255,255,0.9000);border-radius: 10px;">
                <div>${data.name} 2022</div>`;
				let dimensionNames = data.dimensionNames;
				for (let i = 1; i < dimensionNames.length; i++) {
					res += `<div style="margin-top:10px;">
              <div style="display: flex;justify-content: space-between">
                <div>
                  <span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:${InquiriesStatistics.series[i - 1].itemStyle.color}"></span>
                  <span>${data.dimensionNames[i]}</span>
                </div>
                <span>${data.data[i]}</span>
            </div>
          <div>`
				}
				return res + '</div>'
			}
		}
	},
	legend: {
		left: '0',
		itemWidth: 10,
		itemHeight: 10,
		itemGap: 30,
		icon: 'circle',
		formatter: [
			'{a|{name}}'
		].join('\n'),
		textStyle: {
			height: 12,
			rich: {
				a: {
					verticalAlign: 'middle',
					fontSize: 16,
					color: '#999999',
					fontFamily: 'Roboto'
				},
			}
		},
		data: [
			{
				name: 'All Inquiries',
				itemStyle: {
					color: '#235DE6'
				},
			},
			{
				name: 'New Inquiries',
				itemStyle: {
					color: '#4F7DEB'
				},
			},
			{
				name: 'Artwork Confirmed',
				itemStyle: {
					color: '#7A9EF0'
				},
			},
			{
				name: 'Ordered',
				itemStyle: {
					color: '#A6BFF5'
				},
			},
			{
				name: 'Cancelled',
				itemStyle: {
					color: '#FF727E'
				},
			},
			{
				name: 'Others',
				itemStyle: {
					color: '#FFB172'
				},
			}
		]
	},
	tooltip: {},
	dataset: {
		// dimensions: ['status', 'All Inquiries', 'New Inquiries', 'Quoted', 'Ordered', 'Shipped', 'Refund'],
		// source: [
		// 	['lan', 43.3, 85.8, 93.7, 85.8, 93.7, 93.7],
		// 	['Feb', 83.1, 73.4, 55.1, 85.8, 93.7, 93.7],
		// 	['Mar', 86.4, 65.2, 82.5, 85.8, 93.7, 93.7],
		// 	['Apr', 72.4, 53.9, 39.1, 85.8, 93.7, 93.7],
		// 	['May', 72.4, 53.9, 39.1, 85.8, 93.7, 93.7],
		// 	['Jun', 72.4, 53.9, 39.1, 85.8, 93.7, 93.7],
		// 	['Jul', 72.4, 53.9, 39.1, 85.8, 93.7, 93.7],
		// 	['Aug', 72.4, 53.9, 39.1, 85.8, 93.7, 93.7],
		// 	['Sep', 72.4, 53.9, 39.1, 85.8, 93.7, 93.7],
		// 	['oct', 72.4, 53.9, 39.1, 85.8, 93.7, 93.7],
		// 	['Nov', 72.4, 53.9, 39.1, 85.8, 93.7, 93.7],
		// 	['Dec', 72.4, 53.9, 39.1, 85.8, 93.7, 93.7],
		// ]
	},
	xAxis: {
		type: 'category',
		// data: ['lan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Des']
	},
	yAxis: {
		type: 'value',
	},
	// Declare several bar series, each will be mapped
	// to a column of dataset.source by default.
	series: [{
		type: 'bar',
		name: 'All Inquiries',
		// data: [10, 52, 200, 334, 390, 330, 220, 52, 200, 334, 390, 330, 220],
		itemStyle: {
			color: '#235DE6',
			borderRadius: [3, 3, 0, 0],
		},
	}, {
		type: 'bar',
		name: 'New Inquiries',
		// data: [10, 52, 200, 334, 390, 330, 220, 52, 200, 334, 390, 330, 220],
		itemStyle: {
			color: '#4F7DEB',
			borderRadius: [3, 3, 0, 0],
		}
	}, {
		type: 'bar',
		name: 'Artwork Confirmed',
		// data: [10, 52, 200, 334, 390, 330, 220, 52, 200, 334, 390, 330, 220],
		itemStyle: {
			color: '#7A9EF0',
			borderRadius: [3, 3, 0, 0],
		}
	}, {
		type: 'bar',
		name: 'Ordered',
		// data: [10, 52, 200, 334, 390, 330, 220, 52, 200, 334, 390, 330, 220],
		itemStyle: {
			color: '#A6BFF5',
			borderRadius: [3, 3, 0, 0],
		}
	}, {
		type: 'bar',
		name: 'Cancelled',
		// data: [10, 52, 200, 334, 390, 330, 220, 52, 200, 334, 390, 330, 220],
		itemStyle: {
			color: '#FF727E',
			borderRadius: [3, 3, 0, 0],
		}
	}, {
		type: 'bar',
		name: 'Others',
		// data: [10, 52, 200, 334, 390, 330, 220, 52, 200, 334, 390, 330, 220],
		itemStyle: {
			color: '#FFB172',
			borderRadius: [3, 3, 0, 0],
		}
	}]
}

export const OrdersStatistics = {
	grid: {
		width: '',
		left: '0',
		bottom: '0',
		containLabel: true,
		trigger: 'axis',
		tooltip: {
			formatter: (data) => {
				// console.log('tooltipdata', data, this, OrdersStatistics);
				let res = `<div style="width: 209px;background: rgba(255,255,255,0.9000);border-radius: 10px;">
                <div>${data.name} 2022</div>`;
				let dimensionNames = data.dimensionNames;
				for (let i = 1; i < dimensionNames.length; i++) {
					res += `<div style="margin-top:10px;">
              <div style="display: flex;justify-content: space-between">
                <div>
                  <span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:${OrdersStatistics.series[i - 1].itemStyle.color}"></span>
                  <span>${data.dimensionNames[i]}</span>
                </div>
                <span>${data.data[i]}</span>
            </div>
          <div>`
				}
				return res + '</div>'
			}
		}
	},
	legend: {
		left: '0',
		itemWidth: 10,
		itemHeight: 10,
		itemGap: 30,
		icon: 'circle',
		formatter: [
			'{a|{name}}'
		].join('\n'),
		textStyle: {
			height: 12,
			rich: {
				a: {
					verticalAlign: 'middle',
					fontSize: 16,
					color: '#999999',
					fontFamily: 'Roboto'
				},
			}
		},
		data: [
			{
				name: 'All Orders',
				itemStyle: {
					color: '#235DE6'
				},
			},
			{
				name: 'New Order',
				itemStyle: {
					color: '#4F7DEB'
				},
			},
			{
				name: 'In Production',
				itemStyle: {
					color: '#7A9EF0'
				},
			},
			{
				name: 'Awaiting for Shipping',
				itemStyle: {
					color: '#A6BFF5'
				},
			},
			{
				name: 'Shipped',
				itemStyle: {
					color: '#D4DFFA'
				},
			},
			{
				name: 'Received',
				itemStyle: {
					color: '#CAC6F9'
				},

			},
			{
				name: 'Cancelled',
				itemStyle: {
					color: '#FF727E'
				},

			},
			{
				name: 'Refund',
				itemStyle: {
					color: '#FF939C'
				},

			},
			{
				name: 'Others',
				itemStyle: {
					color: '#FFB172'
				},

			}
		]
	},
	tooltip: {},
	dataset: {
		// dimensions: ['status', 'All Inquiries', 'New Inquiries', 'Quoted', 'Ordered', 'Shipped', 'Refund'],
		// source: [
		// 	['lan', 43.3, 85.8, 93.7, 85.8, 93.7, 93.7],
		// 	['Feb', 83.1, 73.4, 55.1, 85.8, 93.7, 93.7],
		// 	['Mar', 86.4, 65.2, 82.5, 85.8, 93.7, 93.7],
		// 	['Apr', 72.4, 53.9, 39.1, 85.8, 93.7, 93.7],
		// 	['May', 72.4, 53.9, 39.1, 85.8, 93.7, 93.7],
		// 	['Jun', 72.4, 53.9, 39.1, 85.8, 93.7, 93.7],
		// 	['Jul', 72.4, 53.9, 39.1, 85.8, 93.7, 93.7],
		// 	['Aug', 72.4, 53.9, 39.1, 85.8, 93.7, 93.7],
		// 	['Sep', 72.4, 53.9, 39.1, 85.8, 93.7, 93.7],
		// 	['oct', 72.4, 53.9, 39.1, 85.8, 93.7, 93.7],
		// 	['Nov', 72.4, 53.9, 39.1, 85.8, 93.7, 93.7],
		// 	['Dec', 72.4, 53.9, 39.1, 85.8, 93.7, 93.7],
		// ]
	},
	xAxis: {
		type: 'category',
		// data: ['lan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Des']
	},
	yAxis: {
		type: 'value',
	},
	// Declare several bar series, each will be mapped
	// to a column of dataset.source by default.
	series: [
		{
			type: 'bar',
			name: 'All Orders',
			// data: [10, 52, 200, 334, 390, 330, 220, 52, 200, 334, 390, 330, 220],
			itemStyle: {
				color: '#235DE6',
				borderRadius: [3, 3, 0, 0],
			},
		},
		{
			type: 'bar',
			name: 'New Order',
			// data: [10, 52, 200, 334, 390, 330, 220, 52, 200, 334, 390, 330, 220],
			itemStyle: {
				color: '#4F7DEB',
				borderRadius: [3, 3, 0, 0],
			}
		},
		{
			type: 'bar',
			name: 'In Production',
			// data: [10, 52, 200, 334, 390, 330, 220, 52, 200, 334, 390, 330, 220],
			itemStyle: {
				color: '#7A9EF0',
				borderRadius: [3, 3, 0, 0],
			}
		},
		{
			type: 'bar',
			name: 'Awaiting for Shipping',
			// data: [10, 52, 200, 334, 390, 330, 220, 52, 200, 334, 390, 330, 220],
			itemStyle: {
				color: '#A6BFF5',
				borderRadius: [3, 3, 0, 0],
			}
		},
		{
			type: 'bar',
			name: 'Shipped',
			// data: [10, 52, 200, 334, 390, 330, 220, 52, 200, 334, 390, 330, 220],
			itemStyle: {
				color: '#D4DFFA',
				borderRadius: [3, 3, 0, 0],
			}
		},
		{
			type: 'bar',
			name: 'Received',
			// data: [10, 52, 200, 334, 390, 330, 220, 52, 200, 334, 390, 330, 220],
			itemStyle: {
				color: '#CAC6F9',
				borderRadius: [3, 3, 0, 0],
			}
		},
		{
			type: 'bar',
			name: 'Cancelled',
			// data: [10, 52, 200, 334, 390, 330, 220, 52, 200, 334, 390, 330, 220],
			itemStyle: {
				color: '#FF727E',
				borderRadius: [3, 3, 0, 0],
			}
		},
		{
			type: 'bar',
			name: 'Refund',
			// data: [10, 52, 200, 334, 390, 330, 220, 52, 200, 334, 390, 330, 220],
			itemStyle: {
				color: '#FF939C',
				borderRadius: [3, 3, 0, 0],
			}
		},
		{
			type: 'bar',
			name: 'Others',
			// data: [10, 52, 200, 334, 390, 330, 220, 52, 200, 334, 390, 330, 220],
			itemStyle: {
				color: '#FFB172',
				borderRadius: [3, 3, 0, 0],
			}
		}
	]
}

export const RevenueStatistics = {
	grid: {
		width: '',
		left: '0',
		bottom: '0',
		containLabel: true,
		trigger: 'axis',
		tooltip: {
			formatter: (data) => {
				let res = `<div style="width: 209px;background: rgba(255,255,255,0.9000);border-radius: 10px;">
                <div>${data.name} 2022</div>`;
				let dimensionNames = data.dimensionNames;
				for (let i = 1; i < dimensionNames.length; i++) {
					res += `<div style="margin-top:10px;">
              <div style="display: flex;justify-content: space-between">
                <div>
                  <span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:${RevenueStatistics.series[i - 1].itemStyle.color}"></span>
                  <span>${data.dimensionNames[i]}</span>
                </div>
                <span>${data.data[i]}</span>
            </div>
          <div>`
				}
				return res + '</div>'
			}
		}
	},
	legend: {
		left: '0',
		itemWidth: 10,
		itemHeight: 10,
		itemGap: 30,
		icon: 'circle',
		formatter: [
			'{a|{name}}'
		].join('\n'),
		textStyle: {
			height: 12,
			rich: {
				a: {
					verticalAlign: 'middle',
					fontSize: 16,
					color: '#999999',
					fontFamily: 'Roboto'
				},
			}
		},
		data: [
			{
				name: 'Total Amount',
				itemStyle: {
					color: '#235DE6'
				},
			},
			{
				name: 'Awaiting Payment',
				itemStyle: {
					color: '#4F7DEB'
				},
			},
			{
				name: 'Received Payment',
				itemStyle: {
					color: '#7A9EF0'
				},
			},
			{
				name: 'Pay Later',
				itemStyle: {
					color: '#A6BFF5'
				},
			},
			{
				name: 'Refunded',
				itemStyle: {
					color: '#FF939C'
				},
			},

		]
	},
	tooltip: {},
	dataset: {
		// dimensions: ['status', 'All Inquiries', 'New Inquiries', 'Quoted', 'Ordered', 'Shipped', 'Refund'],
		// source: [
		// 	['lan', 43.3, 85.8, 93.7, 85.8, 93.7, 93.7],
		// 	['Feb', 83.1, 73.4, 55.1, 85.8, 93.7, 93.7],
		// 	['Mar', 86.4, 65.2, 82.5, 85.8, 93.7, 93.7],
		// 	['Apr', 72.4, 53.9, 39.1, 85.8, 93.7, 93.7],
		// 	['May', 72.4, 53.9, 39.1, 85.8, 93.7, 93.7],
		// 	['Jun', 72.4, 53.9, 39.1, 85.8, 93.7, 93.7],
		// 	['Jul', 72.4, 53.9, 39.1, 85.8, 93.7, 93.7],
		// 	['Aug', 72.4, 53.9, 39.1, 85.8, 93.7, 93.7],
		// 	['Sep', 72.4, 53.9, 39.1, 85.8, 93.7, 93.7],
		// 	['oct', 72.4, 53.9, 39.1, 85.8, 93.7, 93.7],
		// 	['Nov', 72.4, 53.9, 39.1, 85.8, 93.7, 93.7],
		// 	['Dec', 72.4, 53.9, 39.1, 85.8, 93.7, 93.7],
		// ]
	},
	xAxis: {
		type: 'category',
		// data: ['lan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Des']
	},
	yAxis: {
		type: 'value',
	},
	// Declare several bar series, each will be mapped
	// to a column of dataset.source by default.
	series: [
		{
			type: 'bar',
			name: 'Total Amount',
			// data: [10, 52, 200, 334, 390, 330, 220, 52, 200, 334, 390, 330, 220],
			itemStyle: {
				color: '#235DE6',
				borderRadius: [3, 3, 0, 0],
			},
		},
		{
			type: 'bar',
			name: 'Awaiting Payment',
			// data: [10, 52, 200, 334, 390, 330, 220, 52, 200, 334, 390, 330, 220],
			itemStyle: {
				color: '#4F7DEB',
				borderRadius: [3, 3, 0, 0],
			}
		},
		{
			type: 'bar',
			name: 'Received Payment',
			// data: [10, 52, 200, 334, 390, 330, 220, 52, 200, 334, 390, 330, 220],
			itemStyle: {
				color: '#7A9EF0',
				borderRadius: [3, 3, 0, 0],
			}
		},
		{
			type: 'bar',
			name: 'Pay Later',
			// data: [10, 52, 200, 334, 390, 330, 220, 52, 200, 334, 390, 330, 220],
			itemStyle: {
				color: '#A6BFF5',
				borderRadius: [3, 3, 0, 0],
			}
		},
		{
			type: 'bar',
			name: 'Refunded',
			// data: [10, 52, 200, 334, 390, 330, 220, 52, 200, 334, 390, 330, 220],
			itemStyle: {
				color: '#FF939C',
				borderRadius: [3, 3, 0, 0],
			}
		},

	]
}

export const CustomersStatistics = {
	grid: {
		width: '',
		left: '0',
		bottom: '0',
		containLabel: true,
		trigger: 'axis',
		tooltip: {
			formatter: (data) => {
				let res = `<div style="width: 209px;background: rgba(255,255,255,0.9000);border-radius: 10px;">
                <div>${data.name} 2022</div>`;
				let dimensionNames = data.dimensionNames;
				for (let i = 1; i < dimensionNames.length; i++) {
					res += `<div style="margin-top:10px;">
              <div style="display: flex;justify-content: space-between">
                <div>
                  <span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:${CustomersStatistics.series[i - 1].itemStyle.color}"></span>
                  <span>${data.dimensionNames[i]}</span>
                </div>
                <span>${data.data[i]}</span>
            </div>
          <div>`
				}
				return res + '</div>'
			}
		}
	},
	legend: {
		left: '0',
		itemWidth: 10,
		itemHeight: 10,
		itemGap: 30,
		icon: 'circle',
		formatter: [
			'{a|{name}}'
		].join('\n'),
		textStyle: {
			height: 12,
			rich: {
				a: {
					verticalAlign: 'middle',
					fontSize: 16,
					color: '#999999',
					fontFamily: 'Roboto'
				},
			}
		},
		data: [
			{
				name: 'All Customers',
				itemStyle: {
					color: '#235DE6'
				},
			},
			{
				name: 'New Customers',
				itemStyle: {
					color: '#4F7DEB'
				},
			},
			{
				name: 'Old Customers',
				itemStyle: {
					color: '#7A9EF0'
				},
			},
		]
	},
	tooltip: {},
	dataset: {
		// dimensions: ['status', 'All Inquiries', 'New Inquiries', 'Quoted', 'Ordered', 'Shipped', 'Refund'],
		// source: [
		// 	['lan', 43.3, 85.8, 93.7, 85.8, 93.7, 93.7],
		// 	['Feb', 83.1, 73.4, 55.1, 85.8, 93.7, 93.7],
		// 	['Mar', 86.4, 65.2, 82.5, 85.8, 93.7, 93.7],
		// 	['Apr', 72.4, 53.9, 39.1, 85.8, 93.7, 93.7],
		// 	['May', 72.4, 53.9, 39.1, 85.8, 93.7, 93.7],
		// 	['Jun', 72.4, 53.9, 39.1, 85.8, 93.7, 93.7],
		// 	['Jul', 72.4, 53.9, 39.1, 85.8, 93.7, 93.7],
		// 	['Aug', 72.4, 53.9, 39.1, 85.8, 93.7, 93.7],
		// 	['Sep', 72.4, 53.9, 39.1, 85.8, 93.7, 93.7],
		// 	['oct', 72.4, 53.9, 39.1, 85.8, 93.7, 93.7],
		// 	['Nov', 72.4, 53.9, 39.1, 85.8, 93.7, 93.7],
		// 	['Dec', 72.4, 53.9, 39.1, 85.8, 93.7, 93.7],
		// ]
	},
	xAxis: {
		type: 'category',
		// data: ['lan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Des']
	},
	yAxis: {
		type: 'value',
	},
	// Declare several bar series, each will be mapped
	// to a column of dataset.source by default.
	series: [
		{
			type: 'bar',
			name: 'All Customers',
			// data: [10, 52, 200, 334, 390, 330, 220, 52, 200, 334, 390, 330, 220],
			itemStyle: {
				color: '#235DE6',
				borderRadius: [3, 3, 0, 0],
			}
		},
		{
			type: 'bar',
			name: 'New Customers',
			// data: [10, 52, 200, 334, 390, 330, 220, 52, 200, 334, 390, 330, 220],
			itemStyle: {
				color: '#4F7DEB',
				borderRadius: [3, 3, 0, 0],
			}
		},
		{
			type: 'bar',
			name: 'Old Customers',
			// data: [10, 52, 200, 334, 390, 330, 220, 52, 200, 334, 390, 330, 220],
			itemStyle: {
				color: '#7A9EF0',
				borderRadius: [3, 3, 0, 0],
			}
		}
	]
}
