<template>
	<div class="mb-4" :class="stepData.styleName">
	    <slot name="stepText"></slot>
		<div class="step-content">
			<template v-for="(step, index) in stepData.productParamList">
				<v-tooltip top>
					<template v-slot:activator="{ on, attrs }">
						<div class="step-item" v-bind="attrs" v-on="on" :class="{ active: index === selectIndex }" :style="{ backgroundColor: step.colorCode }" :key="index" @click="selectStep(step, index)">
							<v-icon color="#ffffff" small> mdi-check </v-icon>
						</div>
					</template>
					<span>{{ step.colorAlias }}</span>
				</v-tooltip>
			</template>
		</div>
		<div class="errorTip">
			<v-alert dense outlined type="error">
				{{ langSemiCustom.errorTip }}
			</v-alert>
		</div>
	</div>
</template>
<script>
export default {
	props: {
		stepData: {
			type: Object,
			default: ()=>({}),
		},
	},
	data() {
		return {
			selectIndex: -1,
			selectItem: null,
		};
	},
	computed: {
		langSemiCustom() {
			return this.$store.getters.lang?.semiCustom;
		},
	},
	methods: {
		selectStep(item, index, state = false) {
			this.selectIndex = index;
			this.selectItem = item;
			this.$emit("selectStep", {
				type: this.stepData.attributeFlag,
				data: item,
				parent: this.stepData,
				id: this.stepData.id,
				firstSelect: state,
				colorIndex: index
			});
		},
		selectDefault() {
			if (this.selectIndex <= -1) {
				this.selectStep(this.stepData.productParamList[0], 0, true);
			}
		},
		setActiveProductColor() {
			let newColorList = this.stepData.productParamList.filter(color => {
				return color.isActivity == 1
			})
			if (newColorList.length == 0) {
				return
			}
			this.stepData.productParamList = newColorList
		}
	},
	mounted() {
		this.$Bus.$on("selectDefaultColorStep", this.selectDefault);
		this.$Bus.$on("setActiveProductColor", this.setActiveProductColor);
	},
	beforeDestroy() {
		this.$Bus.$off("selectDefaultColorStep", this.selectDefault);
		this.$Bus.$off("setActiveProductColor", this.setActiveProductColor);
	},
};
</script>
<style scoped lang="scss">
@import "~assets/css/half.scss";

.style1 .step-content {
	display: grid;
	grid-template-columns: repeat(auto-fill, 30px);
	grid-gap: 20px;

	.step-item {
		min-width: 0;
		position: relative;
		@include flex-center;
		border-radius: 50%;
		cursor: pointer;
		aspect-ratio: 1;
		height: 30px;
		border: 1px solid #ccc;

		.v-icon {
			display: none;
		}

		&::before {
			display: none;
			content: "";
			position: absolute;
			left: -6px;
			top: -6px;
			right: -6px;
			bottom: -6px;
			border-radius: 50%;
			border: 2px solid $color-primary;
		}

		@media (any-hover: hover) {
			&:hover {
				&::before {
					display: block;
				}

				.v-icon {
					display: block;
				}
			}
		}
	}

	.step-item.active {
		&::before {
			display: block;
		}

		.v-icon {
			display: block;
		}
	}
}

@include respond-to(mb) {
	.style1 .step-content {
		grid-template-columns: repeat(7, 1fr);
	}
}
</style>
