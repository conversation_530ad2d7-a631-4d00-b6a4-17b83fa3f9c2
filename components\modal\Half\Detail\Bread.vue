<script>
export default {
	props: {
		breadList: {
			type: Array,
			required: true,
		},
	},
	methods: {
		clickItem(item) {
			if (item.link) {
				this.$router.push(item.link);
			}
		},
	},
};
</script>

<template>
	<div class="breadCrumbs">
		<div class="bread-item" :class="{ isLink: item.link }" v-for="item in breadList" :key="item.name" @click="clickItem(item)">
			<span>{{ item.name }}</span>
			<b class="icon-right"></b>
		</div>
	</div>
</template>

<style scoped lang="scss">
.breadCrumbs {
	display: flex;
	padding: 20px 0;
	@include respond-to(mb) {
		padding: 10px;
	}

	b {
		margin: 0 4px;
		font-size: 12px;
	}

	.bread-item {
		display: flex;
		align-items: center;

		&.isLink {
			cursor: pointer;

            span{
                white-space: nowrap;
            }
		}

        span{
            white-space: normal;
        }
	}

	.bread-item.isLink span {
		color: $color-primary;
	}

	.bread-item:last-child b {
		display: none;
	}
}
</style>
