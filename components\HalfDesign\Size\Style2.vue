<template>
	<div class="mb-4" :class="stepData.styleName">
	    <slot name="stepText"></slot>
		<div class="step-content">
			<div class="step-text grey--text">
				{{ stepData.attributeTitle }}
			</div>
			<div class="step-wrap">
				<div class="step-item" :class="{ active: index === selectIndex }" v-for="(step, index) in stepData.productParamList" :key="index">
					<div class="input-area">
						<input :disabled="step.stock <= 0" type="text" id="myInputSize2" :placeholder="getPlaceholder(step)" v-model="step.inputNum" @focus="focusFn(index)" @blur="blurFn" @keyup="formatNum(step)" @change="updatePrice" />
					</div>
					<div class="hint-text">unit</div>
				</div>
			</div>
		</div>
		<div class="errorTip" ref="errorTip">
			<v-alert dense outlined type="error"> {{ langSemiCustom.miniQty }} {{ productInfo.lowestPurchaseQuantity || 1 }} </v-alert>
		</div>
	</div>
</template>
<script>
import { round2 } from "@/utils/utils";

export default {
	inject: ["getProductInfo", "getUnit"],
	props: {
		stepData: {
			type: Object,
			default: () => ({}),
		},
	},
	data() {
		return {
			selectIndex: -1,
			selectItem: null,
			inputNum: "",
			isInput: false,
			customInput: false,
		};
	},
	computed: {
		langSemiCustom() {
			return this.$store.getters.lang?.semiCustom;
		},
		unit() {
			return this.getUnit();
		},
		symbolCode() {
			return this.$store.state.currency.symbol;
		},
		rate() {
			return this.$store.state.currency.rate;
		},
		productInfo() {
			return this.getProductInfo();
		},
	},
	methods: {
		updatePrice(val) {
			try {
                let errDom = this.$refs.errorTip;
                if (val.target.value < this.productInfo.lowestPurchaseQuantity) {
                    errDom.style.display = "block";
                } else {
                    errDom.style.display = "none";
                }
            }catch (e) {
            }
			this.$emit("updatePrice");
		},
		formatNum(step) {
			this.customInput = false;
			this.isInput = true;
			if (!step.inputNum) {
				this.isInput = false;
				return undefined;
			}
			step.inputNum = step.inputNum.replace(/[^\d]/g, "");
			if (step.stock && step.stock > 0 && step.inputNum > step.stock) {
				step.inputNum = String(step.stock);
			}
			if (step.stock <= 0) {
				step.inputNum = "";
			}
			this.isInput = false;
		},
		getPlaceholder(step) {
			if (this.stepData.styleName === "style1") {
				if (step.stock && step.stock > 0) {
					// return `${step.stock} in stock`;
					return "";
				} else {
					return "Sold Out";
				}
			}
		},
		selectStep(item, index) {
			if (this.isInput || item.stock <= 0) {
				return;
			}
			this.selectIndex = index;
			this.selectItem = item;
			this.$emit("selectStep", {
				type: this.stepData.attributeFlag,
				data: item,
				id: this.stepData.id,
			});
		},
		getIncreasePrice(num = 0, increase) {
			let newNum = parseInt(num);
			if (newNum <= 0 || isNaN(newNum)) {
				return 0;
			}
			if (!increase) {
				return 0;
			}
			let increaseArr = JSON.parse(increase),
				len = increaseArr.length,
				findItem = increaseArr[0];
			if (len === 1) {
				return increaseArr[0].unitPrice;
			}
			for (let i = 0; i < len; i++) {
				let item = increaseArr[i],
					nextItem = increaseArr[i + 1];
				if (newNum > item.quantity && (nextItem ? newNum <= nextItem.quantity : true)) {
					findItem = item;
					break;
				}
			}
			return findItem.unitPrice;
		},
		getPrice(step) {
			let priceType = step.priceType,
				code = this.symbolCode,
				price = 0,
				rate = this.rate;
			if (priceType === 1) {
				price = round2(step.unitPrice * rate);
				return {
					t: `+${code}${price}`,
					show: price && price > 0,
				};
			} else if (priceType === 2) {
				price = round2(step.totalPrice * rate);
				return {
					t: `+${code}${price}`,
					show: price && price > 0,
				};
			} else if (priceType === 3) {
				return {
					t: `+${step.unitPercent}%`,
					show: step.unitPercent && step.unitPercent > 0,
				};
			} else if (priceType === 4) {
				return {
					t: `+${step.totalPercent}%`,
					show: step.totalPercent && step.totalPercent > 0,
				};
			} else if (priceType === 5) {
				price = round2(this.getIncreasePrice(step.inputNum, step.increasePrice) * rate);
				return {
					t: `+${code}${price}`,
					show: price && price > 0,
				};
			} else {
				return {
					show: false,
				};
			}
		},
		updateQty(type) {
			if (this.stepData && this.stepData.productParamList.length > 0) {
				let list = this.stepData.productParamList;
				list.forEach((item) => {
					if (this.customInput && type != "addCart") {
						item.inputNum = "";
					} else {
						this.formatNum(item);
					}
					this.$forceUpdate();
				});
			}
		},
		setInputNum(num, type = false,noSelect=true) {
			let once = true;
			this.stepData.productParamList.forEach((item, index) => {
				if (once) {
					if (item.stock && item.stock > 0) {
						item.inputNum = num + "";
						once = false;
						this.formatNum(item);
						this.customInput = true;
					}
				}
			});
			if (type) {
				this.$nextTick(() => {
					if (noSelect) {
						document.querySelector("#myInputSize1").readOnly = true;
						this.customInput = false;
					}
				});
			}
			this.$forceUpdate();
			this.$emit("updatePrice");
		},
		focusFn(index){
			this.selectIndex = index;
		},
		blurFn(){
			this.selectIndex = -1
		}
	},
	mounted() {
		this.$Bus.$on("updateQty", this.updateQty);
		this.$Bus.$on("selectDefaultSizeStep", this.setInputNum);
	},
	beforeDestroy() {
		this.$Bus.$off("updateQty", this.updateQty);
		this.$Bus.$off("selectDefaultSizeStep", this.setInputNum);
	},
};
</script>
<style scoped lang="scss">
@import "~assets/css/half.scss";

input[disabled] {
	background: rgba(0, 0, 0, 0.03);
}

.style1 .step-content {
	display: grid;
	grid-template-columns: 100px 1fr;
	grid-gap: 10px;

	.step-wrap {
		display: grid;
		grid-template-columns: repeat(1, 1fr);
		grid-gap: 10px;

		.step-item {
			display: flex;
			align-items: center;

			&.active {
				input {
					border-width: 2px !important;
					border-color: $color-primary !important;
					padding: 9px;
				}
			}
		}

		.input-area {
			flex: 0 0 122px;

			input {
				height: 40px;
				line-height: 20px;
				padding: 8px;
				width: 100%;
				@include step-default;
				border: 1px solid $border-color !important;

				&:hover {
					padding: 10px;
					color: inherit;
				}
			}
		}

		.hint-text {
			flex: 1;
			margin-left: 10px;
			font-size: 14px;
		}
	}

	@include respond-to(mb) {
		grid-template-columns: 1fr;
		grid-gap: 5px;

		.step-wrap {
			grid-template-columns: repeat(1, 1fr);
			grid-gap: 5px;

			.input-area {
				flex: 1;
			}

			.hint-text {
				flex: 0 0 50px;
				font-size: 12px;
			}
		}
	}
}
</style>
