<template>
    <div class="step-item-title">
        <div class="title1">
            <span>{{ step }}:</span>
            <span>{{ title }}</span>
            <slot name="suffix"></slot>
        </div>
        <div class="title2" v-if="smallTitle">
            {{smallTitle}}
        </div>
    </div>
</template>

<script>
export default {
    props: {
        step: {
            type: String,
            default: "1",
            required: true,
        },
        title: {
            type: String,
            default: "",
            required: true,
        },
        smallTitle:{
            type: String
        }
    },
};
</script>

<style lang="scss" scoped>
.step-item-title {
    font-size: 24px;
    font-weight: 700;
    color: #333333;
    margin-bottom: 20px;

    @include respond-to(mb) {
        font-size: 14px;
        margin-bottom: 10px;
    }

    .title1 > span:first-child {
        margin-right: 4px;
        color: $color-primary;
        font-weight: 700;
        text-transform: uppercase;
    }

    .title1 > span:nth-child(2) {
        margin-right: 4px;
    }

    .title2{
        margin: 5px 0;
        font-size: 16px;
        font-weight: 400;
        @include respond-to(mb){
            font-size: 12px;
        }
    }
}
</style>
