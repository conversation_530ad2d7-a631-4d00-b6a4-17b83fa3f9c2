<template>
    <div class="myTools">
			<div ref="childTag" :class="{ 'edit-zone': true, noChildTools }" :style="forHidden ? 'background-color: #34A752' : ''">
            <template v-if="leftRight&& !isSwiper">
                <v-btn depressed dark icon @click.stop="leftCardIndex">
                    <b class="icon-left"></b>
                </v-btn>
                <v-btn depressed dark icon @click.stop="rightCardIndex">
                    <b class="icon-right"></b>
                </v-btn>
            </template>


            <template v-if="forHidden">
                <span class="pl-3" style="color: white">Hidden Section</span>
            </template>
            <template v-if="minusZone">
                <v-btn depressed dark icon @click.stop="minusCardIndex">
                    <b class="icon-Up"></b>
                </v-btn>
            </template>
            <template v-if="plusZone">
                <v-btn depressed dark icon @click.stop="plusCardIndex">
                    <b class="icon-Down"></b>
                </v-btn>
            </template>
			<template v-if="logImportExport">
                <v-btn depressed dark icon @click.stop="uploadDownload">
                    <b class="icon-Download" style="font-size: 1.4em;"></b>
                </v-btn>
            </template>
            <template v-if="delChildCardZone && !isCard&& !isSwiper">
                <v-btn depressed dark icon @click.stop="delChildCardIndex">
                    <b class="icon-jxsht-ybp-sc"></b>
                </v-btn>
            </template>
			<template v-if="isCard||isSwiper">
				<v-menu offset-y light left offset-x :attach="targetDom" nudge-width="180">
                    <template v-slot:activator="{ on, attrs }">
                        <v-btn depressed light icon v-bind="attrs" v-on="on">
                            <b class="icon-gengduo"></b>
                        </v-btn>
                    </template>
                    <v-list class="pa-0" >
                        <v-list-item-group color="primary">
                            <v-list-item>
								<v-list-item-title @click.stop="addCornerLabel"><b class="icon-top mr-3"></b>
								Add Corner Marker</v-list-item-title>
							</v-list-item>
                        </v-list-item-group>
						<v-list-item-group color="primary" v-if="isCard">
                            <v-list-item>
								<v-list-item-title  @click.stop="newChildCardIndex"><b class="icon-jxsht-3d-tj mr-3"></b>
								Add This Item</v-list-item-title>
                            </v-list-item>
                        </v-list-item-group>
						<v-list-item-group color="primary" v-if="isCard">
                            <v-list-item>
                                <v-list-item-title  @click.stop="delChildCardIndex"><b class="icon-jxsht-ybp-sc mr-3"></b>
								Delete This Item</v-list-item-title>
                            </v-list-item>
                        </v-list-item-group>
                    </v-list>
                </v-menu>
            </template>
            <template v-if="newChildCardZone && !isCard&& !isSwiper">
                <v-btn depressed dark icon @click.stop="newChildCardIndex">
                    <b class="icon-jxsht-3d-tj"></b>
                </v-btn>
            </template>
			<!-- <template v-if="templateList">
                <v-btn depressed dark icon @click.stop="newChildCardIndex">
                    <b class="icon-NEW"></b>
                </v-btn>
            </template> -->

            <template v-if="moreOptions">
                <v-menu offset-y light left offset-x :attach="attachClass" nudge-width="160">
                    <template v-slot:activator="{ on, attrs }">
                        <v-btn depressed light icon v-bind="attrs" v-on="on">
                            <b class="icon-gengduo"></b>
                        </v-btn>
                    </template>
                    <v-list class="pa-0">
                        <v-list-item-group color="primary">
                            <v-list-item v-for="(item, index) in MenuList" :key="index"
                                @click.stop="changeSelectedItem(item)">
                                <v-list-item-title><b :class="item.icon" class="mr-3"></b>
                                    {{ item.title }}
                                </v-list-item-title>
                            </v-list-item>
                        </v-list-item-group>
                    </v-list>
                </v-menu>
            </template>


            <template v-if="forHidden">
                <v-menu offset-overflow offset-y light left offset-x :attach="attachClass" nudge-width="140">
                    <template v-slot:activator="{ on, attrs }">
                        <v-btn depressed light icon v-bind="attrs" v-on="on">
                            <b class="icon-gengduo"></b>
                        </v-btn>
                    </template>
                    <v-list>
                        <v-list-item-group color="primary">
                            <v-list-item v-for="(item, index) in hideList" :key="index" @change="showFun(item)">
                                <v-list-item-title> {{ item.title }}</v-list-item-title>
                            </v-list-item>
                        </v-list-item-group>
                    </v-list>
                </v-menu>
            </template>
        </div>



        <div class="btn-before" v-if="before">
            <v-btn class="mx-2" fat dark color="blue lighten-1" @click.stop="newCardBeforeFun">
                <b class="icon-jxsht-3d-tj"></b>
                <span class="font">Add Section</span>
            </v-btn>
        </div>
        <div class="btn-after" v-if="after">
            <v-btn class="mx-2" fat dark color="blue lighten-1" @click.stop="newCardAfterFun">
                <b class="icon-jxsht-3d-tj"></b>
                <span class="font">Add Section</span>
            </v-btn>
        </div>
    </div>
</template>
<script>
export default {
    name: "myTools",
    props: {
		isBpsFaq:{
			type:Boolean,
			default:false
		},
		manageMobile:{
			type: Boolean,
            default: false,
		},
        layout: {
            type: Boolean,
            default: false,
        },
        header: {
            type: Boolean,
            default: false,
        },
        noChildTools: {
            type: Boolean,
            default: true
        },
        before: {
            type: Boolean,
            default: false,
        },
        after: {
            type: Boolean,
            default: false,
        },
        leftRight: {
            type: Boolean,
            default: false,
        },
        forHidden: {
            Type: Boolean,
            default: false,
        },
        minusZone: {
            type: Boolean,
            default: false,
        },
        plusZone: {
            type: Boolean,
            default: false,
        },
        delChildCardZone: {
            type: Boolean,
            default: false,
        },
        moreOptions: {
            type: Boolean,
            default: false,
        },
        newChildCardZone: {
            type: Boolean,
            default: false,
        },
		Footer:{
			type: Boolean,
            default: false,
		},
		templateList:{
			type: Boolean,
            default: false,
		},
		isCard:{
			type: Boolean,
            default: false,
		},
		logImportExport:{
			type: Boolean,
            default: false,
		},
		isSwiper:{
			type: Boolean,
            default: false,
		}
    },
    data() {
        return {
			targetDom:null,
            dialog: false,
            attachClass: ".noChildTools",
            defaultList: [
                { title: "Duplicate Section", icon: "icon-a-lrgl-DuplicateSectionzhuanhuan" },
                { title: "Move Section", icon: "icon-a-lrgl-Movezhuanhuan" },
                { title: "Hide Section", icon: "icon-a-icon-eye2zhuanhuan" },
                { title: "Delete Section", icon: "icon-a-lrgl-dezhuanhuan" },
                { title: "Replace Section", icon: "icon-a-lrgl-chagezhuanhuan" },
                { title: "Edit Background", icon: "icon-a-lrgl-imagezhuanhuan" },
                { title: "Edit Font Color", icon: "icon-a-lrgl-fontzhuanhuan" },
				{ title: "Edit Padding", icon: "icon-padding" },
            ],
            headerList: [
                { title: "Edit Background", icon: "icon-a-lrgl-imagezhuanhuan" },
                { title: "Edit Font Color", icon: "icon-a-lrgl-fontzhuanhuan" },
                { title: "Edit Tip Background", icon: "icon-a-16millioncolors1" },
                { title: "Edit Tip Font Color", icon: "icon-jxs-yishu" }
            ],
            layoutList: [
                { title: "Edit Background", icon: "icon-a-lrgl-imagezhuanhuan" },
                { title: "Edit Font Color", icon: "icon-a-lrgl-fontzhuanhuan" }
            ],
            hideList: [{ title: "Show" }, { title: "Delete" }],
        }
    },
    computed: {
        MenuList() {
			if(this.header) return this.headerList;
			else if(this.layout) {
				if(this.manageMobile){
					return [...this.layoutList,{ title: "Edit Padding", icon: "icon-padding" }];
				}else{
					return this.layoutList;
				}
			}
			else if(this.Footer) return [...this.layoutList,{ title: "Edit Padding", icon: "icon-padding" }];
			else if(this.templateList) return [{ title: "Delete Section", icon: "icon-a-lrgl-dezhuanhuan" },{icon:"icon-NEW",title:"New Banner"}];
			else return this.defaultList;
        },
    },
    methods: {
        minusCardIndex(e) {
            let index = $(Array.from($(e.target).parents(".item"))[0]).attr("index");
            this.$Bus.$emit("minusCardIndex", index);
        },
        plusCardIndex(e) {
            let index = $(Array.from($(e.target).parents(".item"))[0]).attr("index");
            this.$Bus.$emit("plusCardIndex", index);
        },
		uploadDownload(e){
			let index = $(Array.from($(e.target).parents(".item"))[0]).attr("index");
			this.$Bus.$emit("uploadDownload", index);
		},
        leftCardIndex(e) {
            let t = {
                childIndex: $(e.target).parents(".part2").attr("childhoverindex"),
                parentIndex: $(e.target).parents(".part").attr("index"),
            };
            this.$Bus.$emit("leftCardIndex", t);
        },
        rightCardIndex(e) {
            let t = {
                childIndex: $(e.target).parents(".part2").attr("childhoverindex"),
                parentIndex: $(e.target).parents(".part").attr("index"),
            };
            this.$Bus.$emit("rightCardIndex", t);
        },
		addCornerLabel(e){
			let t = {
                childIndex: $(e.target).parents(".part2").attr("childhoverindex"),
                parentIndex: $(e.target).parents(".part").attr("index"),
            };
            this.$Bus.$emit("addCornerLabel", t);
		},
        newChildCardIndex(e) {
			let t = {
                childIndex: $(e.target).parents(".part2").attr("childhoverindex"),
                parentIndex: $(e.target).parents(".part").attr("index"),
            };
			if(this.isBpsFaq) this.$Bus.$emit("BpsFaqAdd", t);
			else this.$Bus.$emit("newChildCardIndex", t);
        },
        delChildCardIndex(e) {
			let t = {
                childIndex: $(e.target).parents(".part2").attr("childhoverindex"),
                parentIndex: $(e.target).parents(".part").attr("index"),
            };
			this.$Bus.$emit("delChildCardIndex", t);
        },
        newCardBeforeFun(e) {
            let index = $(Array.from($(e.target).parents(".item"))[0]).attr("index");
            //将点击的index 传递给父组件
            this.$Bus.$emit("newCardBeforeFun", index);
        },
        newCardAfterFun(e) {
            let index = $(Array.from($(e.target).parents(".item"))[0]).attr("index");
            this.$Bus.$emit("newCardAfterFun", index);
        },
        changeSelectedItem(e) {
            this.$Bus.$emit("changeSelectedItem", e);
        },
        showFun(e) {
            this.$Bus.$emit("showFun", e);
        }
    },
    mounted() {
		if(this.isCard||this.isSwiper){
			let dom = this.$refs['childTag']
			if(dom){
				this.targetDom = dom
			}
		}
        this.$nextTick(() => {
            // 移动端时，无法通过document.querySelector来拿到attach的dom，需要使用attach的dom传值方式
            if (window.frames.iframeMB && window.frames.iframeMB.$) {
                // 当window.frames.iframeMB.$('.noChildTools')[0]有值时为移动端
                if (window.frames.iframeMB.$('.noChildTools')[0]) {
                    //attach参数为字符串或者dom元素，在移动端时使用dom元素给attach赋值
                    this.attachClass = window.frames.iframeMB.$('.noChildTools')[0]
                } else {
                    //当存在childTools时attach='.edit-zone'会存在多个值，造成菜单显示位置错乱，根据noChildTools让菜单固定显示在右上角
                    this.attachClass = '.noChildTools'
                }
            }
        })
    },
};
</script>
<style scoped lang="scss">
.myTools {

    .btn-before,
    .btn-after {
        opacity: 0;
        cursor: pointer;
        transition: all 0.3s;
        transform: translateX(-50%);
        position: absolute;
        z-index: 0;
        left: 50%;

        .v-btn {
            width: 3em;
            height: 3em;
        }

        &:hover {
            >.v-btn {
                width: 210px;
                border-radius: 15px;
            }

            .font {
                display: inline;
                transform: scale(1);
            }
        }
    }

    .btn-before {
        top: -1.35em;
    }

    .btn-after {
        bottom: -1.35em;
    }

    .edit-zone {
        opacity: 0;
        display: flex;
        align-items: center;
        transition: all 0.3s;
        padding: 0.55em 0.15em 0.45em;
        background-color: #1a73e8;
        position: absolute;
        right: -1px;
        top: 0;

        button {
            width: auto;
            height: auto;
            padding: 0 0.2em;
        }

        .icon-gengduo {
            color: white;
            font-size: 1.8em;
        }

        .icon-jxsht-3d-tj {
            padding: 0 0.2em;
        }
    }

    .v-btn {
        display: flex;
        min-width: auto;
        border-radius: 50%;
        transition: all 0.3s;

        .font {
            display: none;
            transform: scale(0);
            transition: all 0.3s;
        }
    }
}

.hover-type .myTools {

    .edit-zone,
    .btn-before,
    .btn-after {
        opacity: 1;
        z-index: 8;
    }
}
</style>
