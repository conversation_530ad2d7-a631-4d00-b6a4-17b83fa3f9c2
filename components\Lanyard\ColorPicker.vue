<template>
  <div class="colorPicker">
    <div class="chooseYourLanyardColors position-relative">
      <div class="searchColor" v-if="tabsName == 'CUSTOM'">
        <el-input
          v-model="keyWord"
          @input="debounceFindAll"
          size="large"
          placeholder="Search Color"
          prefix-icon="el-icon-search"
        />
      </div>
      <el-tabs v-model="tabsName" class="tabs" @tab-click="changeTabs">
        <el-tab-pane :label="lang.POPULAR" name="POPULAR">
          <ul class="box custom-scrollbar">
            <li
              class="item custom-shadow2 hover-type"
              :class="{ active: item.quantity && item.quantity > 0 }"
              v-for="item in bindValue.childList"
              :key="item.id"
            >
              <div class="p1">
                <el-tooltip
                  popper-class="hoverImage"
                  :enterable="true"
                  effect="light"
                  :placement="item.left > 200 ? 'left' : 'right'"
                  trigger="hover"
                  :disabled="JSON.parse(item.imageJson)[2] ? false : true"
                >
                  <template #content>
                    <div
                      :style="{
                        backgroundImage: JSON.parse(item.imageJson)[2]
                          ? 'url(' + JSON.parse(item.imageJson)[2].url + ')'
                          : '',
                      }"
                    ></div>
                  </template>
                  <div
                    @mouseover="getLeftFun($event, item)"
                    :style="{
                      backgroundImage:
                        'url(' + JSON.parse(item.imageJson)[0].url + ')',
                    }"
                  ></div>
                </el-tooltip>
              </div>
              <div class="p2">
                {{ item.alias }}
              </div>
              <div class="p3">
                <input
                  :controls="false"
                  class="myInput"
                  onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode)))"
                  type="number"
                  v-model.number="item.quantity"
                  placeholder="QTY"
                  @change="popularInputChange(item, 'Lanyard Popular Colors')"
                />
              </div>
            </li>
            <li
              class="item custom-shadow2 hover-type"
              :class="{ active: item.quantity && item.quantity > 0 }"
              v-for="item in bindValueDesign?.childList"
              :key="item.id"
            >
              <div class="p1">
                <el-tooltip
                  popper-class="hoverImage"
                  :enterable="false"
                  effect="light"
                  :placement="item.left > 200 ? 'left' : 'right'"
                  trigger="hover"
                  :disabled="JSON.parse(item.imageJson)[2] ? false : true"
                >
                  <template #content>
                    <div
                      :style="{
                        backgroundImage: JSON.parse(item.imageJson)[2]
                          ? 'url(' + JSON.parse(item.imageJson)[2].url + ')'
                          : '',
                      }"
                    ></div>
                  </template>
                  <div
                    @mouseover="getLeftFun($event, item)"
                    :style="{
                      backgroundImage:
                        'url(' + JSON.parse(item.imageJson)[0].url + ')',
                    }"
                  ></div>
                </el-tooltip>
              </div>
              <div class="p2">
                {{ item.alias }}
              </div>
              <div class="p3">
                <input
                  :controls="false"
                  class="myInput"
                  onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode)))"
                  type="number"
                  v-model.number="item.quantity"
                  placeholder="QTY"
                  @change="popularInputChange(item, 'Lanyard Design Colors')"
                />
              </div>
            </li>
          </ul>
        </el-tab-pane>
        <el-tab-pane :label="lang.CUSTOM" name="CUSTOM" v-if="!noCustomTab">
          <ul class="box custom-scrollbar custom-color-box">
            <template v-if="allColor.length">
              <li
                :class="{ active: item.quantity && item.quantity > 0 }"
                class="item custom-shadow2 hover-type"
                v-for="item in allColor"
                :key="item.id"
              >
                <div class="p1">
                  <div
                    :style="{
                      backgroundColor: item.code,
                    }"
                  ></div>
                </div>
                <div class="p2">
                  {{ item.pantone }}
                </div>
                <div class="p3">
                  <input
                    :controls="false"
                    class="myInput"
                    onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode)))"
                    type="number"
                    v-model.number="item.quantity"
                    placeholder="QTY"
                    @input="customInputChange(item)"
                  />
                </div>
              </li>
            </template>
            <template v-else>
              <div style="min-height: 80px"></div>
            </template>
          </ul>
        </el-tab-pane>
        <el-tab-pane :label="lang.DESIGN" name="DESIGN" v-if="!noDesignTab">
          <ul class="box custom-scrollbar" v-if="bindValueDesign">
            <li
              class="item custom-shadow2 hover-type"
              :class="{ active: item.quantity && item.quantity > 0 }"
              v-for="item in bindValueDesign?.childList"
              :key="item.id"
            >
              <div class="p1">
                <el-tooltip
                  popper-class="hoverImage"
                  :enterable="false"
                  effect="light"
                  :placement="item.left > 200 ? 'left' : 'right'"
                  trigger="hover"
                  :disabled="JSON.parse(item.imageJson)[2] ? false : true"
                >
                  <template #content>
                    <div
                      :style="{
                        backgroundImage: JSON.parse(item.imageJson)[2]
                          ? 'url(' + JSON.parse(item.imageJson)[2].url + ')'
                          : '',
                      }"
                    ></div>
                  </template>
                  <div
                    @mouseover="getLeftFun($event, item)"
                    :style="{
                      backgroundImage:
                        'url(' + JSON.parse(item.imageJson)[0].url + ')',
                    }"
                  ></div>
                </el-tooltip>
              </div>
              <div class="p2">
                {{ item.alias }}
              </div>
              <div class="p3">
                <input
                  :controls="false"
                  class="myInput"
                  onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode)))"
                  type="number"
                  v-model.number="item.quantity"
                  placeholder="QTY"
                  @change="popularInputChange(item, 'Lanyard Design Colors')"
                />
              </div>
            </li>
          </ul>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>
<script>
import { indexApi } from "@/api/lanyardQuote/index"
export default {
  props: {
    bindValue: {
      type: Object,
      default: {},
    },
    bindValueDesign: {
      type: Object,
      default: null,
    },
    selectedData: {
      type: Object,
    },
    noDesignTab:{
      type: Boolean
    },
      noCustomTab:{
          type: Boolean
      }
  },
  data() {
    return {
      debounceFindAll: null,
      keyWord: "",
      timed3: null,
      allColor: [],
      copyAllColor: [],
      tabsName: "POPULAR",
      currentCustomCardItem: {},
      currentPopularCardItem: {},
    }
  },
  methods: {
    popularInputChange(val, name) {
      this.currentPopularCardItem = val
      let tempList = this.selectedData[name]
      let tempArr = [
        ...(this.selectedData["Lanyard Popular Colors"]
          ? [...this.selectedData["Lanyard Popular Colors"]]
          : [...[]]),
        ...(this.selectedData["Lanyard Design Colors"]
          ? [...this.selectedData["Lanyard Design Colors"]]
          : [...[]]),
      ]
      let index = tempList.findIndex((x) => {
        return x.id == val.id
      })
      if (index >= 0) {
        if (val.quantity && val.quantity > 0) {
          tempList.splice(index, 1, val)
        } else {
          tempList.splice(index, 1)
          if (tempList.length) {
            this.currentPopularCardItem = tempArr[tempList.length - 1]
          } else {
            this.currentPopularCardItem = null
          }
        }
      } else {
        tempList.push(val)
      }
    },
    customInputChange(val) {
      this.currentCustomCardItem = val
      let tempList = this.selectedData["color card"]

      let index = tempList.findIndex((x) => {
        return x.id == val.id
      })
      if (index >= 0) {
        if (val.quantity && val.quantity > 0) {
          tempList.splice(index, 1, val)
        } else {
          tempList.splice(index, 1)
          if (tempList.length) {
            this.currentCustomCardItem = tempList[tempList.length - 1]
          } else {
            this.currentCustomCardItem = {
              code: "#C8102E",
            }
          }
        }
      } else {
        tempList.push(val)
      }
    },
    findAll() {
      //   const loadingInstance = ElLoading.service({
      //     target: document.querySelector(".custom-color-box"),
      //   });
      return new Promise((resolve, reject) => {
        this.allColor = []
        this.partColor = []
        this.copyAllColor = []
        let postData = {
          keyWord: this.keyWord,
          quoteColorCardList: this.selectedData["color card"],
        }
        indexApi.findAll(postData).then((res) => {
          this.allColor = res.data;
          //去除末尾黑白两色
          if(res.data.length>200){
              this.allColor.splice(-2);
          }
          this.allColor.forEach((item) => {
            if (!item.quantity) {
              item.quantity = undefined
            }
          })
          this.copyAllColor = JSON.parse(JSON.stringify(this.allColor))
          resolve()
        })
      })
    },
    inputColorFun(item, type, isDesigen) {},
    //距离左边距离
    getLeftFun(e, val) {
      val.left = e.target.getBoundingClientRect().left
    },
    changeTabs(targetName) {
      if (targetName._props.label == "CUSTOM") {
        this.findAll()
      }
      this.$emit("update:tabsName", this.tabsName)
    },
    debounce(func, delay = 1000, immediate = false) {
      //闭包
      let timer = null
      //不能用箭头函数
      return function () {
        if (timer) {
          clearTimeout(timer)
        }
        if (immediate && !timer) {
          func.apply(this, arguments)
        }
        timer = setTimeout(() => {
          func.apply(this, arguments)
        }, delay)
      }
    },
  },
  computed: {
    lang() {
      return this.$store.getters.lang.quote || {}
    },
  },
  watch: {
    currentCustomCardItem: {
      handler(val) {
        console.log('val',val)
        this.$emit("currentCustomCardItem", val)
      },
      deep: true,
    },
    currentPopularCardItem: {
      handler(val) {
		console.log('currentPopularCardItem', val)
        this.$emit("currentPopularCardItem", val)
      },
      deep: true,
    },
    noDesignTab:{
      handler(val) {
        if(val == true) {
          this.tabsName = 'POPULAR'
        }
      }
    }

    // quoteCustomCardList: {
    //   handler(val) {
    //     this.debounceFindAll()
    //     this.$emit("quoteCustomCardList", val)
    //   },
    // },
    // currentCustomCardItem: {
    //   handler(val) {
    //     this.$emit("currentCustomCardItem", val)
    //   },
    // },
  },
  created() {
    this.debounceFindAll = this.debounce(this.findAll, 1000, false)
  },
  mounted() {},
}
</script>

<style scoped lang="scss">
.colorPicker {

  .hover-type:hover {
      border-color: $color-primary !important;
  }

  .custom-shadow2 {
        position: relative;
        background: #fff;

        &::before,
        &::after {
            content: "";
            position: absolute;
            z-index: -1;
            bottom: 14px;
            left: 0px;
            width: 50%;
            height: 20%;
            box-shadow: 0 14px 7px #d9dbdd;
            transform: rotate(-3deg);
        }

        &::after {
            right: 0;
            left: auto;
            transform: rotate(3deg);
        }
    }

  ::v-deep .chooseYourLanyardColors {
    border: 1px solid #d9dbdd;

    @media screen and (max-width: 768px) {
      padding: 0;
      border: none;
      margin-top: -15px;
    }

    .tabs {
      .box {
        margin: 0;
        display: grid;
        grid-template-columns: repeat(5, 1fr);
        // grid-template-rows: repeat(auto-fill, 82px);
        column-gap: 18px;
        row-gap: 10px;
        max-height: 480px;
        // overflow-y: scroll;
        overflow-x: hidden;
        padding: 5px 20px 20px 20px;
        justify-content: space-between;

        @media screen and (max-width: 1919px) {
          grid-template-columns: repeat(auto-fill, minmax(175px, 180px));
          column-gap: normal;
        }

        @media screen and (max-width: 1500px) {
          grid-template-columns: repeat(4, 1fr);
          column-gap: 8px;
          padding: 5px 10px 20px 10px;
        }

        @media screen and (max-width: 1279px) and (min-width: 611px) {
          grid-template-columns: repeat(auto-fill, minmax(150px, 180px));
          justify-content: space-between;
          column-gap: 8px;
          padding: 5px 15px 20px 15px;
        }

        @media screen and (max-width: 610px) {
          grid-template-columns: repeat(3, 1fr);
          justify-content: space-between;
          column-gap: 8px;
          padding: 0;
        }

        @media screen and (max-width: 510px) {
          grid-template-columns: repeat(2, 1fr);
          justify-content: space-between;
          column-gap: 10px;
          padding: 0;
        }

        .item {
          margin: 0;
          padding: 0;
          border: 1px solid #d9dbdd;
          border-radius: 4px;
          display: grid;
          grid-template-columns: repeat(3, 1fr);
          grid-template-rows: repeat(2, 40px);
          box-sizing: border-box;

          .p1 {
            grid-column: 1/2;
            grid-row: 1/3;
            padding: 11px 10.7px;

            > div {
              width: 100%;
              height: 100%;
              border: 1px solid #d9dbdd;
              background-size: cover;
            }
          }

          .p2 {
            grid-column: 2/4;
            grid-row: 1/2;
            padding: 5px 12px 5px 5px;
            font-size: 15px;
            font-weight: 400;
            color: #666666;

            @media screen and (max-width: 768px) {
              font-size: 12px;
            }
          }

          .p3 {
            grid-column: 2/4;
            grid-row: 2/3;
            display: flex;
            padding: 0 15px 0 5px;
			margin-top: .3em;

            @media screen and (max-width: 768px) {
              padding: 0 5px;
			  margin-top: 0;
            }
          }

          &:hover {
            .p2 {
              overflow: visible;
            }
          }
          &.active {
            border-color: $color-primary;
          }
        }

        .item.add-custom-color {
          grid-column: 1/2;
          grid-row: 1/2;
          cursor: pointer;

          > div:nth-child(1) {
            grid-column: 1/4;
            grid-row: 1/2;
            display: flex;
            justify-content: center;
            align-items: flex-end;
          }

          > div:nth-child(2) {
            grid-column: 1/4;
            grid-row: 2/3;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 15px;
            font-weight: 400;
            color: #666666;
          }
        }

        .item.add-custom-color.change {
          grid-column: 1/2;
          grid-row: 1/3;
          display: block;
          padding: 5px;

          .el-form-item {
            margin-bottom: 14px;
            .el-form-item__label {
              font-size: 15px;
              font-weight: 400;
              color: #666666;
              margin-bottom: 0;
            }

            .el-input {
              height: 26px;
            }
          }

          .el-form-item.bottom {
            margin-bottom: 0;

            .el-form-item__content {
              display: grid;
              grid-template-columns: 1fr 1fr;
              gap: 5px;

              .el-button + .el-button {
                margin: 0;
              }

              button {
                font-size: 15px;
                font-weight: 400;
                border: 0;
              }

              button:nth-child(1) {
                background: #f3f3f4;
                color: #333333;
              }

              button:nth-child(2) {
                background: $color-primary;
                color: #ffffff;
              }
            }
          }
        }
      }

      .el-tabs__header {
        margin-bottom: 20px;
        padding-top: 20px;
        display: flex;
        justify-content: center;

        @media screen and (max-width: 768px) {
          padding-top: 0;

          ::v-deep .el-tab-pane:nth-child(2) > ul{
            margin-top: 20px;
          }
        }

        .el-tabs__nav-scroll {
          display: flex;
          justify-content: center;

          .el-tabs__nav {
            position: relative;
          }

          .el-tabs__nav::before {
            position: absolute;
            content: "";
            left: 0;
            bottom: 0;
            width: 100%;
            height: 2px;
            background-color: var(--el-border-color-light);
            z-index: var(--el-index-normal);
          }

          .el-tabs__active-bar {
            background-color: $color-primary;
          }

          .el-tabs__item {
            font-size: 18px;
            font-weight: bold;
            color: #333333;
            padding: 0;
            width: 100px;
            text-align: center;

            @media screen and (max-width: 768px) {
              font-size: 14px;
              height: 30px;
              line-height: 30px;
            }
          }

          .el-tabs__item.is-active {
            color: $color-primary;
          }
        }
      }
    }
    .searchColor {
      position: absolute;
      right: 50px;
      padding-top: 20px;
      z-index: 1;
      @media screen and (max-width: 768px) {
        right: 11px;
        padding-top: 34px;
      }

      .el-input--prefix .el-input__inner {
          padding-left: 30px;
          background-color: #f3f3f4;
          border: 0;
          font-size: 16px;
      }
    }
    .myInput {
      width: auto;
      line-height: 1em;
      text-align: left;
      -webkit-appearance: none;
      background-color: var(--el-input-bg-color, var(--el-color-white));
      border-radius:5px !important;
      background-image: none;
      border: 1px solid #dcdfe6;
      box-sizing: border-box;
      color: var(--el-input-text-color, var(--el-text-color-regular));
      display: inline-block;
      font-size: 16px;
      height: 30px;
      line-height: 30px;
      outline: 0;
      padding: 0 11px;
      transition: var(--el-transition-border);
      width: 100%;

      @media screen and (max-width: 768px) {
        height: 30px;
        line-height: 30px;
        font-size: 12px;
      }
    }
  }
}
</style>
<style>
.hoverImage div {
  width: 150px;
  height: 300px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}
</style>
