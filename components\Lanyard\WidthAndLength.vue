<template>
  <div class="WidthAndLength">
    <div class="border custom-shadow">
      <div class="info">
        <span class="icon">
          <span>{{ bindValue.alias }}:</span>
        </span>
        <div class="red1">{{ lang.Step }} {{ num }}</div>
      </div>
      <div class="left padding radius6">
        <div>
          <el-image
            :src="
              radio
                ? JSON.parse(radio.imageJson)[0].url
                : JSON.parse(bindValue.childList[0].imageJson)[0].url
            "
            lazy
          ></el-image>
        </div>
        <div>
          <div class="custom-radio">
            <div
              class="StepBox"
              v-for="item in bindValue.childList"
              :key="item.id"
            >
              <label
                :class="{ active: isActive(item) }"
                class="item pointer hover-type"
                :for="bindValue.paramName + item.id"
              >
                <div class="se" @click.stop="changeInput(item)">
                  <span class="product-info">
                    <div
                      :for="bindValue.paramName + item.id"
                      class="radio-beauty"
                    ></div>
                    <span class="bTitle">{{ item.alias }}</span>
                  </span>
                </div>
              </label>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    bindValue: {
      type: Object,
    },
    num: {
      type: String,
    },
    selectedData: {
      type: [Array, Object],
    },
    bindName: {
      type: String,
    },
  },
  components: {},
  data() {
    return {
      radio: null,
    }
  },
  methods: {
    changeInput(val) {
        this.radio = val;
      this.$emit("clickFun", {
        key: this.bindName,
        value: val,
      })
      this.$emit("picDialogFun", false)
    },
  },
  computed: {
    lang() {
      return this.$store.getters.lang.quote || {}
    },
    isActive() {
      return function (val) {
        if (this.inputVal.length > 0) {
          return this.inputVal.some((item) => {
            return item.id == val.id
          })
        }
      }
    },
    inputVal() {
      return this.selectedData[this.bindName] || []
    },
  },
  watch: {},
  created() {},
  mounted() {},
}
</script>

<style scoped lang="scss">
.WidthAndLength {
  .custom-shadow {
        position: relative;
        background: #fff;

        &::before,
        &::after {
          content: "";
          position: absolute;
          z-index: -1;
          bottom: 12px;
          left: 5px;
          width: 50%;
          height: 20%;
          box-shadow: 0 14px 7px #d9dbdd;
          transform: rotate(-3deg);
        }

        &::after {
          right: 5px;
          left: auto;
          transform: rotate(3deg);
        }
      }
  .border {
    border: 1px solid #d9dbdd;
    border-radius: 6px;
    padding: 10px;
    height: 100%;


  }
  .info {
    display: grid;
    grid-template-columns: 140px 1fr;
    column-gap: 20px;
    font-size: 18px;
    font-weight: 400;
    color: #333333;
    position: relative;

    @media screen and (max-width: 768px) {
      grid-template-columns: auto 1fr;
    }

    .red1 {
      font-size: 18px;
      color: #de3500;

      @media screen and (max-width: 768px) {
        font-size: 12px;
        display: flex;
        align-items: flex-end;
        line-height: 20px;
      }
    }

    .icon {
      > span:nth-child(1) {
        font-size: 18px;
        font-weight: 400;
        color: #333333;

        @media screen and (max-width: 768px) {
          font-size: 12px;
        }
      }

      > span:nth-child(2) {
        color: #999999;
        border: 1px solid #999999;
        border-radius: 50%;
        width: 12px;
        height: 12px;
        display: inline-block;
        font-size: 12px;
        text-align: center;
        margin-left: 5px;
      }
    }
  }

  .left {
    display: grid;
    grid-template-columns: 140px 1fr;
    column-gap: 10px;
    padding-top: 10px;
    .el-image {
      height: 200px;
      @media screen and (max-width: 768px) {
        height: 150px;
        width: 100%;
        ::v-deep .el-image__inner{
          object-fit: contain !important;
        }
      }
      img {
        object-fit: contain;
      }
    }
    > div:nth-child(2) {
      padding: 10px;

      @media screen and (max-width: 768px) {
        padding: 5px;
      }
    }

    @media screen and (max-width: 768px) {
      grid-template-columns: 1fr minmax(210px, 1fr);
      text-align: center;
      width: 100%;
    }

    .el-select {
      width: 100%;

      .el-input {
        height: 40px;

        @media screen and (max-width: 1279px) {
          height: 30px;
        }

        .el-input__inner {
          @media screen and (max-width: 768px) {
            font-size: 12px;
          }
        }
      }
    }

    .title {
      font-size: 18px;
      font-weight: 400;
      color: #333333;

      @media screen and (max-width: 768px) {
        font-size: 12px;
        text-align: left;
      }
    }

    ::v-deep .custom-radio {
      display: grid;
      grid-template-columns: 1fr;
      grid-auto-rows: 40px;
      row-gap: 6px;
      height: 100%;

      .el-radio__input {
        display: flex;
      }

      label {
        border: 1px solid transparent;
        border-radius: 10px;
        margin: 0;
        padding: 0 20px;
        background-color: #f7f9fa;
        display: flex;
        align-items: center;
      }

      .el-radio__label {
        font-size: 18px;

        @media screen and (max-width: 768px) {
          font-size: 12px;
        }
      }

      .el-radio__inner {
        border-width: 2px;
      }

      .el-radio__inner::after {
        background-color: #d9d9d9;
        transform: translate(-50%, -50%) scale(1);
        width: 5px;
        height: 5px;
      }

      .el-radio__input.is-checked .el-radio__inner::after {
        background-color: $color-primary;
      }

      .el-radio__input.is-checked .el-radio__inner {
        background: transparent;
        border-color: $color-primary;
      }

      .el-radio__input.is-checked + .el-radio__label {
        color: inherit;
      }
    }

    .ul {
      font-size: 14px;
      font-weight: 400;
      color: #999999;
      line-height: 1.5em;
      margin-top: 10px;

      ul {
        display: inline-block;
        padding-left: 17px;
        margin: 0;

        li {
          line-height: 1.5em;
          list-style-type: disc !important;
        }
      }
    }
    .StepBox {
      ::v-deep .el-image__inner {
        vertical-align: middle;
        border-radius: 10px 10px 0 0;
      }
      .custom-shadow {
        position: relative;
        background: #fff;

        &::before,
        &::after {
          content: "";
          position: absolute;
          z-index: -1;
          bottom: 12px;
          left: 5px;
          width: 50%;
          height: 20%;
          box-shadow: 0 14px 7px #d9dbdd;
          transform: rotate(-3deg);
        }

        &::after {
          right: 5px;
          left: auto;
          transform: rotate(3deg);
        }
      }
      $bg: #afb1b3;
      $bgc: white;
      $bgc2: $color-primary;
      cursor: pointer;
      display: flex;
      flex-direction: column;
      align-items: center;
      position: relative;
      @media screen and (max-width: 767px) {
        // padding: 0 0 5.5px 0;
      }
      .item {
        border-radius: 10px;
        box-sizing: border-box;
        transition: all 0.2s;
        width: 100%;
        height: 100%;
        display: block;
        border: 1px solid transparent;
        &.noBorder {
          border: 1px solid transparent;
        }
        @media (any-hover: hover) {
          &:hover {
            border-color: $color-primary !important;
            box-shadow: 0 3px 4px 0 #ccc;
          }
        }
        @media screen and (max-width: 1921px) and (min-width: 1919px) {
        }

        @media screen and (max-width: 500px) {
          padding: 5px;
        }

        position: relative;
        &.active {
          border-color: $color-primary !important;
          box-shadow: 0 3px 4px 0 #ccc;
          .product-info .radio-beauty {
            background-color: $color-primary;
            border-color: $color-primary;
            &::after {
              background-color: white;
            }
          }
          .product-info .bTitle {
            color: $color-primary;
          }
        }
        > div {
          > .el-image img {
            // height: 150px;
            width: 100%;
            height: 100%;
            object-fit: contain;
          }
        }
      }
      .tooltip {
        position: absolute;
        top: 10px;
        right: 10px;
        z-index: 2;
        svg {
          fill: $color-primary;
        }
      }

      .number-input {
        margin-top: 0;
        width: 140px;
        // height: 34px;
        background: #ffffff;
        border-radius: 4px;
        @media screen and (max-width: 767px) {
          // height: 25px;
        }
        ::v-deep .el-input-number.is-controls-right .el-input-number__decrease {
          // line-height: 15px;
          // bottom: -1px;
        }
        ::v-deep .el-input-number__increase {
          // top: 3px;
          width: 24px;
          // line-height: 16px;
        }
        ::v-deep .el-input-number__decrease {
          // bottom: -1px;
          width: 24px;
          // line-height: 15px;
        }
        ::v-deep .el-input__inner {
          // height: 34px;
          // line-height: 34px;
          @media screen and (max-width: 767px) {
            // height: 25px;
            // line-height: 25px;
            text-align: center;
          }
        }
      }
      .se {
        display: flex;
        height: 100%;
        .zoomIcon {
          position: absolute;
          top: 10px;
          right: 10px;
          color: #ffffff;
          font-size: 20px;
          z-index: 2;
          transition: font-size 0.2s;
          &:hover {
            font-size: 24px;
            color: $color-primary;
          }
        }
        .swiper {
          position: relative;
          width: 100%;
          img {
            border-radius: 10px 10px 0 0;
          }
        }
        .product-info {
          display: flex;
          align-items: center;
          justify-content: center;
          .radio-beauty {
            width: 14px;
            height: 14px;
            box-sizing: border-box;
            display: inline-block;
            border: 1px solid #afb1b3;
            vertical-align: middle;
            margin: 0 12px 0 3px;
            border-radius: 50%;
            background-color: white;
            background-clip: content-box;
            position: relative;
            cursor: pointer;
            &::after {
              content: "";
              position: absolute;
              border-radius: 50%;
              left: 50%;
              top: 50%;
              transform: translate(-50%, -50%);
              width: 6px;
              height: 6px;
              background-color: #afb1b3;
            }
            @media screen and (max-width: 767px) {
              width: 14px;
              height: 14px;
              min-width: 14px;
              margin-right: 5px;
              &::after {
                width: 5px;
                height: 5px;
              }
            }
          }
          .bTitle {
            white-space: nowrap;
            font-size: 18px;
            color:#606266;
            @media screen and (max-width: 767px) {
              font-size: 12px;
            }
          }
          .type2-price {
            display: flex;
            flex-direction: column;
          }
        }
      }
    }
  }
}
</style>
