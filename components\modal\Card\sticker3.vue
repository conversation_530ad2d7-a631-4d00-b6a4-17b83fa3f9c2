<template>
	<div class="summary-box" :class="modal.wrapClass" :style="modal.style">
		<div class="bps-container" v-for="(o, oi) in modal.outer" :key="oi">
			<pic class="pc-show mb-none" :src="modal.outer[0].img.value" :title="modal.outer[0].img.alt" :alt="modal.outer[0].img.alt" @click="setModalType(modal.outer[0].img,modal.outer,'img')"/>
			<div class="content">
				<EditDiv tag-name="h2" v-model:content="modal.outer[0].title.value" @click="setModalType(o.title,modal.outer,'text')" />
				<EditDiv class="des" v-model:content="modal.outer[0].subTitle.value" @click="setModalType(o.subTitle,modal.outer,'text')" />
				<EditDiv class="des font-bold" v-model:content="modal.outer[0].text1.value" @click="setModalType(o.text1,modal.outer,'text')" />
				<div>
					<EditDiv tag-name="span" class="font-bold" v-model:content="modal.outer[0].text2.value" @click="setModalType(o.text2,modal.outer,'text')" />
					<EditDiv tag-name="span" v-model:content="modal.outer[0].text3.value" @click="setModalType(o.text3,modal.outer,'text')" />
				</div>
				<div>
					<EditDiv tag-name="span" class="font-bold" v-model:content="modal.outer[0].text4.value" @click="setModalType(o.text4,modal.outer,'text')" />
					<EditDiv tag-name="span" v-model:content="modal.outer[0].text5.value" @click="setModalType(o.text5,modal.outer,'text')" />
				</div>
				<div>
					<EditDiv tag-name="span" class="font-bold" v-model:content="modal.outer[0].text6.value" @click="setModalType(o.text6,modal.outer,'text')" />
					<EditDiv tag-name="span" v-model:content="modal.outer[0].text7.value" @click="setModalType(o.text7,modal.outer,'text')" />
				</div>
				<EditDiv class="des" v-model:content="modal.outer[0].text8.value" @click="setModalType(o.text8,modal.outer,'text')" />
			</div>
			<pic class="pc-none mb-show" style="margin-top: 35px" :alt="modal.outer[0].img.alt" :src="modal.outer[0].img.value" :title="modal.outer[0].img.alt" @click="setModalType(modal.outer[0].img,modal.outer,'img')"/>
		</div>
	</div>
</template>

<script>
export default {
	props: {
		data: {
			type: Object,
			default: {},
		}
	},
	data() {
		return {
			modal: {
				style: {},
				type: {index: 0, clickPosition: 'outer'},
				...this.data
			}
		};
	},
	watch: {
		modal: {
			handler(val) {
				if (process.env.isManage) this.$emit("update:data", val);
			},
			immediate: true,
			deep: true
		}
	},
	methods:{
		setModalType(target, targetArray, clickType, event, other) {
			this.$setModal(this, target, targetArray, clickType, event, other)
		},
	}
};
</script>

<style lang="scss" scoped>
.summary-box {
	.font-bold {
		font-weight: bold;
		margin-bottom: 5px !important;
		display: inline-block;
	}
	.pc-show {
		display: block;
	}
	.pc-none {
		display: none;
	}
	padding: 70px 0;
	.sticker_two {
		margin-top: 41px;
		position: relative;
		padding: 46px 0 61px 0;
		.pic {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: auto;
			-o-object-fit: cover;
			object-fit: cover;
		}
		.bps-container {
			display: block;

			div {
				display: inline-block;
				position: relative;

			}
		}
	}
	.bps-container {
		display: grid;
		align-items: center;
		position: relative;
		grid-template-columns: repeat(2, 1fr);
		grid-gap: 20px;
		.content {
			margin-left: 25%;
			.des {
				color: #000 !important;
				margin: 12px 0 20px 0;
			}
		}
		.btnWrap {
			display: inline-block;
			color: #ffffff;
		}

		&>.pic {
			border-radius: 10px;
		}

		.content {
			.contentGrid {
				display: grid;
				grid-template-columns: repeat(2, 1fr);
				grid-column-gap: 46px;
				div {
					img {
						width: auto;
						vertical-align: middle;
					}
					div {
						vertical-align: middle;
						display: inline-block;
						width: 60%;
						margin-left: 19px;
					}
				}
			}
			h1,
			h2 {
				font-size: 36px;
				text-align: left;
			}
			&>.pic{
				display: none;
			}

			.des {
				line-height: 24px;
				color: #666666;
			}
			.conter {
				display: grid;
				grid-template-columns: repeat(2, 1fr);
				grid-column-gap: 30px;
				grid-row-gap: 10px;
				font-size: 14px;
				font-weight: 400;
				color: #333;
				.item {
					span {
						display: inline-block;
						width: 8px;
						height: 8px;
						background: #939393;
						border-radius: 50%;
						margin-right: 20px;
					}
				}
			}
		}
	}
}

.home-part3 {
	background-color: #F8F8F8;

	.bps-container {
		position: relative;
		grid-template-columns: 1fr 1.2fr;
		grid-gap: 70px;
	}
}

.about-part {
	padding: .7813vw 0 2.6042vw;
	background-color: #FFF;

	.bps-container {
		max-width: 74.4792vw;
		position: relative;
		grid-template-columns: 1fr 1.7fr;
		grid-gap: 1.5vw;
		.content {
			margin-left: 0;
		}
	}
	.bps-button{
		margin-top: 0;
	}

	.content{
		margin-left: 1.3021vw;
	}
}

@media screen and (max-width: $mb-width) {
	.summary-box {
		.mb-none {
			display: none;
		}
		.mb-show {
			display: block;
		}
		padding: 27px 0;
		background-color: #ffffff;
		.bps-container {
			.content {
				margin-left: 0%;
				div {
					font-size: 12px;
				}
				.des {
					color: #000 !important;
					margin: 0px 0 0px 0;
					font-size: 12px;
				}
			}
			display: block;
			h2,.btnWrap {
				display: inline-block;
				vertical-align: middle
			}
			h2 {
				width: 100%;
				font-size: 16px !important;
			}
			.btnWrap {
				a {
					border-radius: 25px;
				}
			}
			.des {
				margin-top: 16px;
				font-size: 12px;
				font-family: Arial;
				font-weight: 400;
				color: #666666;
			}
			.containerFlex {
				margin-top: 22px;
				display: grid;
				grid-template-areas:
					'b a'
					'c a';
				grid-column-gap: 11px;
				div {
					position: relative;
					img {
						height: 100%;
					}
					&:nth-child(3) {
						grid-area: a;
					}
					&:nth-child(2) {
						margin-top: 10px;
					}
					.des {
						position: absolute;
						text-align: center;
						width: 80%;
						left: 50%;
						transform: translateX(-50%);
						bottom: 0px;
						font-size: 12px;
						font-family: Arial;
						font-weight: 400;
						color: #333333;
					}
				}
			}
		}
	}
}
</style>
