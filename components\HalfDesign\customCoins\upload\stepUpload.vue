<template>
  <div class="step-upload mb-4" :id="`copy${stepData.id}`">
    <div class="box-border">
      <i class="el-icon-close" @click="closeMask"></i>
    </div>
    <half-design-litter-title
      :index="stepData.id"
      :data-name="`${stepData.styleClass + '_' + stepData.id}`"
      style="margin: 4px 0"
      :stepTitle="stepData.minStepTitle"
      :showTitle="false"
      v-show="stepData.attributeTitle"
      >{{ stepData.attributeTitle }}
    </half-design-litter-title>
	<slot name="stepText"></slot>
    <div class="step-box">
      <div class="uploadArea" style="cursor: pointer">
        <div class="upload-box" :class="{ dropActive: dropActive, greyBox: isUpload }">
          <loader :loadState="!loadImgState" :style="{ zIndex: 10 }"></loader>
          <div class="uploadList">
            <div class="swiper myswiperMedals1" ref="myswiperMedals1">
              <div class="swiper-wrapper">
                <div
                  class="swiper-slide pointer"
                  :class="{
                    active: isActive(item.id),
                    moreUpload: moreUpload,
                  }"
                  @click="onSlideClick(item, 'clicked', true)"
                  v-for="(item, index) in showCopyMedalsIconList"
                  :key="index"
                >
                  <b class="icon-shanchu2 shanchuIcon" @click.stop="delImg2(item.id)"></b>
                  <half-design-check-icon
                    class="absolute-top-right2 check-icon"
                  ></half-design-check-icon>
                  <v-tooltip
                    bottom
                    max-width="300"
                    color="#FFFFFF"
                    content-class="absoluteImgBox"
                  >
                    <template v-slot:activator="{ on, attrs }">
                      <img
                        v-bind="attrs"
                        v-on="on"
                        style="aspect-ratio: 1/1; object-fit: cover; width: 100%"
                        :data-id="item.id"
                        :src="item.iconUrl"
                      />
                    </template>
                    <div class="absoluteImg" v-show="item.sideImgUrl">
                      <img
                        :src="item.sideImgUrl"
                        style="aspect-ratio: 1/1; object-fit: cover; width: 100%"
                        alt=""
                      />
                    </div>
                  </v-tooltip>
                </div>
              </div>
            </div>
            <div class="swiper-button-prev medalsUploadBtn" @click.stop></div>
            <div class="swiper-button-next medalsUploadBtn" @click.stop></div>
          </div>
          <div class="upload-btn" style="text-align: center">
            <span
              class="uploadText"
              :class="{ isDisabled: isUpload }"
              @click="openUploadBox"
              >{{
                coinsAreaUpload?.files.length > 0
                  ? langSemi.changeLogo
                  : langSemi.chooseLogo
              }}</span
            >
            <button
              :disabled="isUpload"
              :class="{ isDisabled: isUpload }"
              @click="$refs.setpUpload.click()"
            >
              <span>{{ lang.Upload }} </span>
              <ToolTip
                class="uploadTip"
                :selfIcon="'icon-wenhao1'"
                :titleContent="'Allowed extensions: EPS, AI, PDF, JPG, JPEG, GIF, PNG, BMP, TIF, SVG, WORD, EXCEL, TXT, WEBP.'"
              >
              </ToolTip>
            </button>
          </div>
        </div>
      </div>
    </div>
    <input
      type="file"
      ref="setpUpload"
      :accept="acceptFileType"
      :multiple="moreUpload"
      @change="uploadPic"
    />
    <BaseDialog v-model="showUpload" :model="false" :width="!isMobile ? '700px' : '90%'">
	  <div class="UploadDrawerColse" slot="closeIcon" @click="closeFn">
		<b class="icon-guanbi"></b>
	  </div>
      <UploadDrawer
        v-if="showUploadDrawer"
        :style="{ width: !isMobile ? '700px' : '100%' }"
        :showMedalsId.sync="showMedalsId"
        :bindValue="stepData"
        :moreUpload="moreUpload"
        :medalsIconList="medalsIconList"
        :medalsIconTagList="medalsIconTagList"
        :clickImgId.sync="coinsAreaUpload.clickImgId"
        :uploadList.sync="coinsAreaUpload.files"
        :needOpenDialog="needUploadDialog"
        @addImg="addImg"
        @moreAddImgFn="moreAddImgFn"
        @delImg2="delImg2"
        @delAllImg="delAllImg"
        @nextStep="nextStep"
        @uploadChangeArea="uploadChangeArea"
        @setOtherFiles="setOtherFiles"
      >
      </UploadDrawer>
    </BaseDialog>
    <BaseDialog
      v-model="showSelectAreaDialog"
      class="selectAreaDialog"
      :beforeClose="cancel"
      :persistent="true"
      :width="!isMobile ? '50%' : '90%'"
    >
      <areaSelectDialog
        @selectArea="selectArea"
        :areaIndex.sync="areaIndex"
      ></areaSelectDialog>
    </BaseDialog>
    <div class="errorTip">
      <v-alert dense outlined type="error">
        {{ langSemi.errorTip }}
      </v-alert>
    </div>
  </div>
</template>

<script>
import PriceText from "@/components/Quote/PriceText";
import QuoteBtn from "@/components/Quote/QuoteBtn";
import { getMedalTemplatesList } from "@/api/web";
import UploadDrawer from "@/components/HalfDesign/customCoins/upload/UploadDrawer";
import loader from "@/components/HalfDesign/customMedals/common/loader.vue";
import BaseDialog from "@/components/Quote/BaseDialog.vue";
import ToolTip from "@/components/HalfDesign/customMedals/common/ToolTip";
import { generateUUID } from "@/utils/utils";
import areaSelectDialog from "@/components/modal/Half/Detail/components/areaSelectDialog.vue";
import { checkFile } from "@/utils/validate";
import { uploadFile } from "@/utils/oss";

export default {
  inject: [
    "getCustomProductId",
    "getCustomCategoryId",
    "canvas",
    "getAreaUploadList",
    "getAreaList",
    "getAreaIndex",
  ],
  props: {
    stepData: {
      type: Object,
      default: {},
    },
    moreUpload: {
      type: Boolean,
      default: false,
    },
    needUploadDialog: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      acceptFileType: ".JPG,.JPEG,.jpe,.jif,.jfif,.jfi,.GIF,.PNG,.BMP,.WEBP",
      selectItem: null,
      showUpload: false,
      dropActive: false,
      myswiper1: null,
      showMedalsId: -1,
      showMedalsIconList: [],
      medalsIconList: [],
      medalsIconTagList: [],
      showUploadName: "",
      firstArea: true,
      showSelectAreaDialog: false,
      canAdd: false,
      tempData: {},
      nowUpload: false,
      isCircle: false,
    };
  },
  watch: {
    isUpload(val) {
      if (val) {
        this.stepData.noShow = true;
      } else {
        this.stepData.noShow = false;
      }
    },
    showCopyMedalsIconList(val) {
      this.swiperUpdate();
    },
    showMedalsId(newVal) {
      this.medalsIconList.forEach((item) => {
        if (!item.copyThemeLogoImgList) item.copyThemeLogoImgList = [];
        if (newVal == item.id) {
          this.showMedalsIconList = item.copyThemeLogoImgList;
        }
      });
    },
  },
  components: {
    PriceText,
    QuoteBtn,
    BaseDialog,
    loader,
    UploadDrawer,
    ToolTip,
    areaSelectDialog,
  },
  methods: {
    isActive(id) {
      let arr = this.coinsAreaUpload?.clickImgId || [];
      return arr.some((item) => item.id == id);
    },
    upload() {
      this.$emit("drawerUpload", { data: this.stepData, type: "open" });
    },
    updateCheckBox(e) {
      e.preventDefault();
      this.$store.commit("SET_UploadSwitch", !this.isUpload);
      this.$emit("update:isUpload", !this.isUpload);
    },
    closeMask() {
      this.$emit("closeMask");
    },
    showMaskFn(name) {
      this.$emit("showMaskFn", name);
    },
    swiperUpdate() {
      this.$nextTick(() => {
        if (this.myswiperMedals1) this.myswiperMedals1.update();
      });
    },
    initSwiper() {
      this.myswiperMedals1 = new Swiper(this.$refs.myswiperMedals1, {
        slidesPerView: 4.5,
        spaceBetween: 10,
        navigation: {
          nextEl: ".medalsUploadBtn.swiper-button-next",
          prevEl: ".medalsUploadBtn.swiper-button-prev",
        },
        breakpoints: {
          0: {
            slidesPerView: 2.5, // 小屏幕设备
            spaceBetween: 10,
          },
          1000: {
            slidesPerView: 4.5,
            spaceBetween: 10,
          },
          1500: {
            slidesPerView: 4.5,
            spaceBetween: 10,
          },
        },
        watchSlidesVisibility: true, //防止不可点击
        on: {
          // click: this.onSlideClick,
        },
      });
    },
    moreAddImgFn(item, type) {
      let findData = this.coinsAreaUpload.clickImgId.find((citem) => citem.id == item.id);
      if (findData) return;
      this.coinsAreaUpload.clickImgId.push({
        id: item.id,
        iconUrl: item.iconUrl,
        imgProperty: item.imgProperty || {},
        type,
      });
      let imgItem = {
        secure_url: item.iconUrl,
        iconUrl: item.iconUrl,
        original_filename: "logo",
        size: 1024,
        id: item.id,
        active: false,
        imgProperty: item.imgProperty || {},
      };
      this.coinsAreaUpload.files.push(imgItem);
      this.$nextTick(() => {
        this.showMedalsId = this.showMedalsIconList.length
          ? this.showMedalsIconList[0].themeId
          : -1;
        this.canAdd = false;
        this.moreAddImg(imgItem);
      });
    },
    async onSlideClick(item, type, first, addImg) {
      //   if (this.needUploadDialog && !this.canAdd && !this.moreUpload && first) {
      //     this.tempData = {
      //       item: item,
      //       type: type,
      //     };
      //     this.showSelectAreaDialog = true;
      //     return;
      //   }
      if (this.moreUpload) {
        this.moreAddImgFn(item, type);
        return;
      }
      await this.delImg();
      this.$nextTick(() => {
        this.coinsAreaUpload.clickImgId = [];
        this.coinsAreaUpload.clickImgId.push({
          id: item.id,
          iconUrl: item.iconUrl,
          type,
          imgProperty: item.imgProperty || {},
        });
        this.coinsAreaUpload.files = [
          {
            secure_url: item.iconUrl,
            iconUrl: item.iconUrl,
            original_filename: "logo",
            size: 1024,
            id: item.id,
            active: false,
            imgProperty: item.imgProperty || {},
          },
        ];
        this.showMedalsId = this.showMedalsIconList.length
          ? this.showMedalsIconList[0].themeId
          : -1;
        this.canAdd = false;
        if (!addImg) this.addImg();
      });
    },
    addImg(type) {
      this.$nextTick(() => {
        if (this.coinsAreaUpload.files.length == 0) return;
        //调用绘制图片方法
        //筹码圆形绘制，如果不是圆形，就缩小0.6
        this.$emit("newAddImg3", {
          copyId: "copy" + this.stepData.id,
          imgItem: this.coinsAreaUpload.files[0],
          isCircle: this.isCircle,
        });
        if (type == "upload") {
          this.closeFn();
        }
      });
    },
    moreAddImg(imgItem) {
      this.$nextTick(() => {
        this.$emit("newAddImg", {
          copyId: "copy" + this.stepData.id,
          imgItem: imgItem,
		  isCircle: this.isCircle,
        });
      });
    },
    async delImg() {
		console.log(this.coinsAreaUpload.files,'988888');
      if (this.coinsAreaUpload?.files.length > 0) {
        await this.canvas.deImgById2(this.coinsAreaUpload.files?.[0].id);
      }
    },
    async delImg2(id) {
      if (this.coinsAreaUpload.files.length > 0) {
        this.coinsAreaUpload.files = this.coinsAreaUpload.files.filter(
          (item) => item.id != id
        );
        this.coinsAreaUpload.clickImgId = this.coinsAreaUpload.clickImgId.filter(
          (item) => item.id != id
        );
        await this.canvas.deImgById2(id);
      }
    },
    delAllImg() {
      this.$emit("delAllImg");
    },
    delUploadImg() {
      this.delImg();
      this.coinsAreaUpload.clickImgId = [
        {
          id: -1,
          iconUrl: "",
        },
      ];
      this.coinsAreaUpload.files = [];
    },
    openUploadBox() {
      this.showUpload = true;
    },
    closeFn() {
      this.showUpload = false;
    },
    nextStep() {
      this.closeFn();
    },
    selectDefault() {
      if (this.stepData.productParamList.length) {
        this.selectItem = this.stepData.productParamList[0];
        this.$emit("selectStep", {
          type: this.stepData.attributeFlag,
          data: this.selectItem,
          parent: this.stepData,
          id: this.stepData.id,
          copyId: "copy" + this.stepData.id,
          firstSelect: true,
        });
        if (this.listUploadFile.length > 0) {
          this.onSlideClick(this.listUploadFile[0], "clicked");
          this.showUpload = false;
          this.showMedalsId = this.listUploadFile[0].themeId;
        }
      }
    },
    setShowMedalsIconList(imgUploadData) {
      //自己上传的图片,通过themeId来找到需要展示的列表
      if (imgUploadData.iconUrl.length > 0 && imgUploadData.type == "upload") {
        this.medalsIconList.forEach((item) => {
          if (!item.copyThemeLogoImgList) item.copyThemeLogoImgList = [];
          item.copyThemeLogoImgList.forEach((citem) => {
            if (citem.id == imgUploadData.showId) {
              imgUploadData.themeId = citem.themeId;
              this.showMedalsIconList =
                [imgUploadData].concat(item.copyThemeLogoImgList) || [];
            }
          });
        });
        this.onSlideClick(imgUploadData, "upload");
        return;
      }
      this.medalsIconList.forEach((item) => {
        if (!item.copyThemeLogoImgList) item.copyThemeLogoImgList = [];
        item.copyThemeLogoImgList.forEach((citem) => {
          if (citem.id == imgUploadData.id) {
			imgUploadData.iconUrl = citem.imgUrl;
            this.showMedalsIconList =
              [citem].concat(
                item.copyThemeLogoImgList.filter((ccitem) => {
                  return ccitem.id !== imgUploadData.id;
                })
              ) || [];
            setTimeout(() => {
              //默认选的白板  先不添加默认图片
              if (this.firstArea) return;
              this.onSlideClick(imgUploadData, "clicked");
            }, 500);
          }
        });
      });
    },
    setDefaultImg(imgUploadData) {
      this.medalsIconList.forEach((item) => {
        if (!item.copyThemeLogoImgList) item.copyThemeLogoImgList = [];
        item.copyThemeLogoImgList.forEach((citem) => {
          if (citem.id == imgUploadData.id) {
            this.showMedalsIconList =
              [citem].concat(
                item.copyThemeLogoImgList.filter((ccitem) => {
                  return ccitem.id !== imgUploadData.id;
                })
              ) || [];
            setTimeout(() => {
              this.onSlideClick(citem, "clicked");
            }, 500);
          }
        });
      });
    },
    getThemeData(imgUploadData, type = "clicked") {
      if (this.medalsIconList.length > 0) return;
      getMedalTemplatesList({
        productId: this.productId,
        categoryId: this.categoryId,
      }).then((res) => {
        this.medalsIconList = res.data.map((item) => {
          if (
            item.themeLogoList &&
            item.themeLogoList?.length > 0 &&
            Array.isArray(item.themeLogoList)
          ) {
            item.copyThemeLogoImgList = item.themeLogoList.map((citem) => {
              return {
                ...citem,
                iconUrl: citem.imgUrl,
              };
            });
          }
          return item;
        });
        this.showMedalsIconList = this.medalsIconList[0]?.copyThemeLogoImgList || [];
        if (imgUploadData && imgUploadData.id && !this.moreUpload) {
          this.setShowMedalsIconList(imgUploadData);
        } else if (imgUploadData && imgUploadData.id && this.moreUpload) {
          this.setDefaultImg(imgUploadData);
        }
        this.medalsIconTagList = [{ id: -1, tagName: this.langDesign.all }].concat(
          res.data.map((item) => ({ id: item.id, tagName: item.name }))
        );
      });
    },
    showUploadBox(data, frist = false, colorIndex = 0) {
      this.isCircle = false;
      if (data.circle && data.circle.radius && +data.circle.radius > 0) {
        this.isCircle = true;
      }
      this.stepData.noShow = false;
      this.firstArea = frist;
      this.showUploadName = data.valueName;
      if (data.left == 0 && data.right == 0 && data.top == 0 && data.bottle == 0) {
        //没有logo 区域
        this.stepData.noShow = true;
        return;
      }
      this.$forceUpdate();
      if (this.moreUpload) {
        let imgUploadData = { id: data.themeId, iconUrl: "", type: "clicked" };
        if (!this.medalsIconList.length) {
          this.getThemeData(imgUploadData);
          return;
        }
        let imgDataArr =
          this.coinsAreaUpload.clickImgId.filter((item) => item.id != -1) || [];
        this.coinsAreaUpload.clickImgId = [
          {
            id: -1,
            iconUrl: "",
          },
        ];
        this.coinsAreaUpload.files = [];
        if (imgDataArr.length > 0) {
          setTimeout(() => {
            imgDataArr.forEach((item) => {
              if (item.type == "upload") {
                item.themeId = data.themeId || -1;
              }
              this.moreAddImgFn(item, item.type);
            });
          }, 1200);
        } else {
          this.setDefaultImg(imgUploadData);
        }
      } else {
        let imgData = this.coinsAreaUpload.clickImgId[0];
        let imgUploadData = { id: data.themeId, iconUrl: "", type: "clicked" };
        if (imgData && imgData.iconUrl.length > 0) {
          if (imgData.type == "upload") {
            imgData.showId = data.themeId;
          }
          imgUploadData = imgData;
        }
        if (!this.medalsIconList.length) {
          this.getThemeData(imgUploadData);
        } else {
          this.setShowMedalsIconList(imgUploadData);
        }
      }
    },
    async selectArea(needChange, index) {
      this.showSelectAreaDialog = false;
      this.canAdd = true;
      if (needChange) {
        //只有单纯和当前方位不同才会触发，所以取第一个就行
        this.showUploadName = this.areaList[index[0]].valueName;
        if (this.nowUpload) {
          this.$gl.show();
          await uploadFile(this.tempData).then((res) => {
            let upDataId = generateUUID();
            let data = {
              id: upDataId,
              iconUrl: res,
            };
            this.tempData = {
              item: data,
              type: "upload",
            };
          });
          this.$gl.hide();
        }
        this.$nextTick(async () => {
          await this.onSlideClick(this.tempData.item, this.tempData.type, false, true);
          this.$Bus.$emit("clearArea", false, index[0]);
          this.tempData = {};
          this.nowUpload = false;
        });
      } else {
        if (this.nowUpload) {
          this.$gl.show();
          await uploadFile(this.tempData).then((res) => {
            let upDataId = generateUUID();
            let data = {
              id: upDataId,
              iconUrl: res,
            };
            this.tempData = {
              item: data,
              type: "upload",
            };
          });
          this.$gl.hide();
        }
        await this.onSlideClick(this.tempData.item, this.tempData.type);
        if (index.length > 1) {
          //同时选中了多个方位，但是不需要切换方位
          index.forEach((item, index) => {
            if (item !== this.areaIndex) {
              this.setOtherFiles(item, this.tempData);
            }
          });
        }
        this.tempData = {};
        this.nowUpload = false;
      }
    },
    setOtherFiles(index, tempData) {
      let otherAreaName = this.areaList[index].valueName;
      let otherArea = this.getAreaUploadList()[otherAreaName];
      otherArea.clickImgId = [
        {
          id: tempData.item.id,
          iconUrl: tempData.item.iconUrl,
          type: tempData.type,
        },
      ];
      otherArea.files = [
        {
          secure_url: tempData.item.iconUrl,
          iconUrl: tempData.iconUrl,
          original_filename: "logo",
          size: 1024,
          id: tempData.item.id,
          active: false,
        },
      ];
    },
    uploadChangeArea(data, type, index) {
      this.showUploadName = this.areaList[index].valueName;
      this.$nextTick(async () => {
        await this.onSlideClick(data, type, false, true);
        this.$Bus.$emit("clearArea", false, index);
        if (type == "upload") this.closeFn();
      });
    },
    uploadPic(event, type = "upload") {
      let files = type === "upload" ? event.target.files : event;
      //判断是否弹出选择框
      if (this.needUploadDialog && !this.moreUpload && !this.canAdd) {
        let fileResult = checkFile(Array.from(files), this.acceptFileType, 80);
        if (!fileResult) {
          this.$toast.error("File type error");
          return false;
        }
        if (fileResult.nomalSize.length == 0) {
          this.$store.commit("setSizeDialog", true);
          this.$store.commit("setInputRefName", "replayUpload");
          this.$store.commit("setOverSizeList", file.overSize);
          return false;
        }
        this.tempData = fileResult.nomalSize[0];
        this.showSelectAreaDialog = true;
        this.nowUpload = true;
        this.$refs.setpUpload.value = "";
        return;
      }
      this.$gl.show();
      let fileResult = checkFile(Array.from(files), this.acceptFileType, 80);

      if (!fileResult) {
        this.$toast.error("File type error");
        this.$gl.hide();
        return false;
      }
      if (fileResult.nomalSize.length == 0) {
        this.$gl.hide();
        this.$store.commit("setSizeDialog", true);
        this.$store.commit("setInputRefName", "replayUpload");
        this.$store.commit("setOverSizeList", fileResult.overSize);
        this.$refs.setpUpload.value = "";
        return false;
      }
      let uploadPromises = [];
      fileResult.nomalSize.forEach((file) => {
        let promise = uploadFile(file).then((res) => {
          let upDataId = generateUUID();
          if (this.moreUpload) {
            let data = {
              id: upDataId,
              iconUrl: res,
            };
            this.moreAddImgFn(data, "upload");
            this.showMedalsId = -1;
          } else {
            this.delAllImg();
            this.coinsAreaUpload.clickImgId = [
              {
                id: upDataId,
                iconUrl: res,
                type: "upload",
              },
            ];
            this.coinsAreaUpload.files = [];
            this.coinsAreaUpload.files.push({
              id: upDataId,
              original_filename: file.name,
              secure_url: res,
              iconUrl: res,
              size: (file.size / 1024).toFixed(1),
            });
          }
        });
        uploadPromises.push(promise);
      });

      Promise.all(uploadPromises).then(() => {
        if (this.moreUpload) {
        } else {
          this.addImg("upload");
        }
        this.$gl.hide();
        this.$refs.setpUpload.value = "";
        if (fileResult.overSize.length > 0) {
          this.$store.commit("setSizeDialog", true);
          this.$store.commit("setInputRefName", "replayUpload");
          this.$store.commit("setOverSizeList", fileResult.overSize);
        }
      });
    },
    cancel() {
      this.needUpload = false;
      this.canAdd = false;
    },
  },
  computed: {
    lang() {
      return this.$store.getters.lang.quote || {};
    },
    langSemi() {
      return this.$store.getters.lang.semiCustom || {};
    },
    langDesign() {
      return this.$store.getters.lang?.design || {};
    },
    isMobile() {
      return this.$store.state.device === "mb";
    },
    productId() {
      return this.getCustomProductId();
    },
    categoryId() {
      return this.getCustomCategoryId();
    },
    isUpload() {
      return this.$store.state.halfDesign.medalsLaterUpload;
    },
    listUploadFile() {
      return this.$store.state.halfDesign.uploadPic;
    },
    loadImgState() {
      return this.canvas?.loadImgState;
    },
    loadBackImgState() {
      return this.canvas?.loadBackImgState;
    },
    coinsAreaUpload() {
      let data = this.getAreaUploadList();
      return data[this.showUploadName];
    },
    areaIndex() {
      return this.getAreaIndex();
    },
    areaList() {
      return this.getAreaList();
    },
    showUploadDrawer() {
      return this.coinsAreaUpload && Object.keys(this.coinsAreaUpload).length > 0;
    },
    showCopyMedalsIconList() {
      let data = this.showMedalsIconList;
      if (this.coinsAreaUpload && Object.keys(this.coinsAreaUpload).length > 0) {
        let files = this.coinsAreaUpload.files;
        if (files.length > 0) {
          let hasData = [];
          files.forEach((file) => {
            let hasId = this.showMedalsIconList.find((item) => item.id == file.id);
            if (hasId) {
              hasData.push(hasId);
            } else {
              hasData.push(file);
            }
          });
          if (hasData.length == 0) {
            data = this.coinsAreaUpload.files.concat(this.showMedalsIconList);
          } else {
            data = hasData.concat(
              this.showMedalsIconList.filter((item) => {
                return !hasData.find((file) => file.id == item.id);
              })
            );
          }
        }
      }
      return data;
    },
  },
  mounted() {
    this.$Bus.$on("selectDefaultArtworkStep", this.selectDefault);
    this.getThemeData();
    this.$nextTick(() => {
      this.initSwiper();
    });
    this.$Bus.$on("currentArea", this.showUploadBox);
    //canvas 操作框点击删除
    this.$Bus.$on("deleteArtworkImg", this.delImg2);
  },
  beforeDestroy() {
    this.$Bus.$off("selectDefaultArtworkStep", this.selectDefault);
    this.$Bus.$off("currentArea", this.showUploadBox);
    this.$Bus.$off("deleteArtworkImg", this.delImg2);
  },
};
</script>

<style lang="scss">
.absoluteImgBox {
  z-index: 1000000 !important;
  background-color: transparent !important;
  opacity: 1 !important;

  @media screen and (max-width: 758px) {
    display: none !important;
  }

  .absoluteImg {
    width: 160px;
    height: 160px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10px;
    background: #ffffff;
    box-shadow: 0px 2px 12px 0px rgba(0, 0, 0, 0.3);
    border-radius: 10px;
  }
}
</style>
<style scoped lang="scss">
@import "~assets/css/half.scss";

.step-upload {
  .confirmBtnWrap {
    display: none;
    margin-top: 33px;

    @include respond-to(mb) {
      margin-top: 20px;
    }
  }

  &.mask {
    .confirmBtnWrap {
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}

.upload-box.dropActive {
  border-color: $color-primary !important;
}

.circle2 {
  display: flex;
  justify-content: center;
  align-items: center;
  border-color: $color-primary;
  background-color: $color-primary;
  flex-shrink: 0;
  width: 18px;
  height: 18px;
  border: 1px solid #afb1b3;
  border-radius: 50%;
  margin-right: 10px;
  background-color: #fff;

  &::after {
    background-color: #ffffff;
    content: "";
    width: 6px;
    height: 6px;
    background: #d4d7d9;
    border-radius: 50%;
  }

  @include respond-to(mb) {
    width: 15px;
    height: 15px;

    &::after {
      width: 5px;
      height: 5px;
    }
  }

  &.active {
    background-color: $color-primary !important;
    border-color: $color-primary !important;

    &::after {
      background-color: #ffffff !important;
    }
  }
}

.t1 {
  display: flex;
  align-items: center;
  font-weight: bold;
  font-size: 16px;
}

.t2 {
  color: #333333;
  font-size: 14px;
  margin-bottom: 10px;
  padding-left: 29px;

  @include respond-to(mb) {
    padding-left: 23px;
  }
}

.noteText {
  margin-top: 10px;
  font-size: 14px;
}

.click_text {
  color: #007aff;
  text-decoration: underline;
  cursor: pointer;
}

.step-upload {
  position: relative;
  background-color: #fff;
  // padding: 40px 0;
  border-radius: 10px;

  &.errorStep {
    .upload-box {
      --error-color: #ff0000;
      border-color: var(--error-color) !important;
    }
  }

  .step-box {
    .t1 {
      margin-bottom: 10px;
      font-size: 16px;
      color: #333333;
      font-weight: bold;

      .icon-shangchuan {
        color: #9e9e9e;
      }
    }

    .t2 {
      margin-bottom: 10px;
      font-size: 14px;
      color: #333333;
    }

    .upload-box {
      display: flex;
      justify-content: space-between;
      align-items: center;
      background: #fafafa;
      border: 1px solid #dbdbdb;
      border-radius: 4px;
      cursor: pointer;
      transition: all 0.3s;
      position: relative;

      &.greyBox {
        pointer-events: none;
        user-select: none;

        .swiper-slide:hover {
          border-color: transparent;
          background-color: transparent;
        }
      }

      .uploadList {
        position: relative;
        width: 100%;
        overflow: auto;
        text-align: center;
        padding: 10px 22px;
        background-color: #ffffff;
        border-right: 1px solid #dbdbdb;

        /* 设置按钮大小 */
        .swiper-button-next,
        .swiper-button-prev {
          width: 20px;
          height: 40px;
          background: #c0c4cc;
          color: #333333;
          opacity: 1;
          transform: translateY(-50%);
          margin-top: 0;

          &::after {
            font-size: 14px;
          }

          &.swiper-button-disabled {
            opacity: 0.3;
          }
        }

        .swiper-button-prev {
          left: 0;
        }

        .swiper-button-next {
          right: 0;
        }

        .uploadIcon {
          width: 68px;
          height: 55px;
          margin-top: 15px;
          font-size: 52px;
          color: #ccc;
        }

        .selectedImgBox {
          position: relative;
          border: 2px solid $color-primary;
          aspect-ratio: 1 / 1;
          border-radius: 6px;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 120px;

          img {
            width: 100%;
            aspect-ratio: 1 / 1;
            object-fit: contain;
          }

          .myIconBox {
            pointer-events: all;
            color: $color-primary;
            position: absolute;
            right: 0;
            top: 0;

            .icon-shanchu2 {
            }
          }
        }

        .uploadItem {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 5px;
          font-size: 14px;
        }

        .myIcon {
          margin: 0 4px;
        }
      }

      .upload-btn {
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: 0.6em;
        font-size: 16px;
        padding: 10px 20px;

        .uploadText {
          color: $color-primary;
          text-decoration: underline;

          &.isDisabled {
            color: #cccccc !important;
          }
        }

        button {
          padding: 0.4em 2em;
          background: var(--color-primary);
          border-radius: 5px;
          border: none;
          color: #fff;
          font-size: 18px;
          display: flex;
          align-items: center;
          justify-content: center;

          ::v-deep .uploadTip {
            .v-icon.v-icon {
              font-size: 16px;
              color: #fff;
            }

            b {
              font-size: 14px;
              color: #fff;
            }
          }
        }

        button.isDisabled {
          background: #cccccc !important;
        }
      }

      .tips {
        font-size: 15px;
        color: #b3b3b3;
        text-align: center;
      }
    }

    @media screen and (min-width: 768px) and (max-width: 1499px) {
      grid-template-columns: 2fr 1.4fr;
      column-gap: 20px;
    }

    @include respond-to(mb) {
      grid-template-columns: repeat(1, 1fr);
      height: auto;

      .t1 {
        margin-bottom: 5px;
        font-size: 12px;
      }

      .t2 {
        margin-bottom: 5px;
        font-size: 12px;
      }

      .upload-box {
        .uploadList {
          .selectedImgBox {
            width: 80px;
          }

          .uploadItem {
            font-size: 12px;
          }

          .uploadIcon {
            width: 51px;
            height: 41px;
            margin-top: 0;
            font-size: 48px !important;
          }
        }

        .upload-btn button {
          font-size: 14px;
          border-radius: 4px;
          padding: 0.4em 1em;

          ::v-deep .uploadTip {
            .v-icon.v-icon {
              font-size: 12px;
              color: #fff;
            }

            b {
              font-size: 12px;
              color: #fff;
            }
          }
        }

        .upload-btn {
          padding: 10px;
          font-size: 12px;
        }

        .tips {
          font-size: 12px;
        }
      }
    }
  }

  .box-border {
    display: none;

    .el-icon-close {
      display: flex;
      justify-content: center;
      align-items: center;
      position: absolute;
      font-weight: 700;
      top: 0;
      right: 0;
      width: 40px;
      height: 40px;
      transform: translate(50%, -50%);
      cursor: pointer;
      background: #ffffff;
      border-radius: 50%;
      box-shadow: 1px 2px 20px 0 rgba(0, 0, 0, 0.2);
      z-index: 10;
    }
  }

  &.mask {
    position: relative;
    z-index: 101;

    .confirmBtnWrap {
      position: relative;
    }

    .box-border {
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      display: block;
      background-color: #fff;
      border: 1px solid #d9dbdd;
    }

    .step-box {
      position: relative;
    }
  }

  @include respond-to(mb) {
    background-color: #fff;
    border-radius: 5px;
    padding: 0px;

    &.mask {
      .box-border {
        .el-icon-close {
          width: 30px;
          height: 30px;
          transform: translate(0, 0);
          box-shadow: none;
        }
      }
    }
  }
}

.swiper {
  height: 100%;

  .swiper-slide {
    overflow: hidden;
    border-radius: 6px;
    cursor: pointer;
    border: 2px solid transparent;
    // background-color: #e9e9e9;
    align-items: center;
    display: flex;
    justify-content: center;
    box-sizing: border-box;
    user-select: none;
    position: relative;

    .check-icon {
      display: none;
    }

    .shanchuIcon {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      font-size: 20px;
      color: $color-primary;
      cursor: pointer;
      display: none;
    }

    &.active {
      border-color: $color-primary;
      background-color: #fff;

      .check-icon {
        display: flex;
      }
    }

    @media (any-hover: hover) {
      &:hover {
        border-color: $color-primary;
        background-color: #fff;
        &.active {
          &::after {
            content: "";
            position: absolute;
            inset: 0;
            background-color: rgba(0, 0, 0, 0.6);
            z-index: 1;
            pointer-events: none;
            user-select: none;
          }
          .shanchuIcon {
            display: flex;
            z-index: 2;
          }
        }
      }
    }
  }

  .swiper-slide-thumb-active {
    border-color: #0066cc;
  }

  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}

.UploadDrawerColse {
	cursor: pointer;
	position: absolute;
	top: 20px;
	right: 16px;
	.v-icon {
		&::before {
			font-size: 18px;
			font-weight: 700;
			color: #333;
		}
	}

	@include respond-to(mb) {
		top: 10px;
		right: 10px;
	}
}

.selectAreaDialog ::v-deep {
  .base-dialog-model-con {
    max-width: 750px;
    border-radius: 0;
  }
  .icon-guanbi {
    top: 10px !important;
  }
  @include respond-to(mb) {
    .icon-guanbi {
      top: 12px !important;
    }
  }
}
input[type="file"] {
  display: none;
  position: absolute;
  left: 0;
  top: 0;
  width: 0;
  opacity: 0;
  clip: rect(0, 0, 0, 0);
}
</style>
