<template>
  <div class="select-size">
    <div class="tips custom">
      <b class="icon-icon_Preview"></b>
      <p>
        {{ lang.quote.neon.overSizeTipsHead + ' ' + (unitSign == "cm" ? 114:45)  + unitSign + ' ' + lang.quote.neon.overSizeTipsTail }}
      </p>
    </div>
    <template v-for="item in filterList">
      <div class="size-card custom-size"
        v-if="item.paramName == 'Custom Size'"
        :key="item.id"
        @click="handleClick(item)"
        :class="{'is-active':item.id === value?.id,'upload':tabName == 'Upload','noDiscount':neonDiscount?.unitPercent == 0 || neonDiscount?.unitPercent >= 1}">
        <div class="custom-size-header">
          <span class="alias-name">{{ item.alias }}</span>
          <span class="input-type"
            @click.stop="isRangeInput = !isRangeInput">{{ isRangeInput ? lang.quote.neon.textInputChange:lang.quote.neon.rangeInputChange }}</span>
        </div>
        <div class="custom-size-cont">
          <ProgressSelect v-show="isRangeInput"
            :value="customSize"
            :options="sliderOptions"
            :tagSign="unitSign == defSign ? 'IN': unitSign"
            :unitConvertRatio="unitConvertRatio"
            :zoneRatio="zoneRatio"
            @input="inputCustomSize($event, item)"
            @changePercent="changePercentage" />
          <div class="text-input-line"
            v-show="!isRangeInput">
            <div class="custom-input">
              <label for="width">
                {{ lang.order.selectSize.width }}:
              </label>
              <input id="width"
                type="number"
                :value="customSize"
                @input="({target}) => inputCustomSize(target.value, item)"
                @blur="({target}) => blurCustomSize(target.value, item)">
            </div>
            <div class="divider"></div>
            <div class="custom-input">
              <label for="height">
                {{ lang.order.selectSize.height }}:
              </label>
              <input type="text"
                :value="computedHeight"
                disabled>
            </div>
            <span style="margin-left: .4em">Inch</span>
          </div>
        </div>
        <Corner :active="item.id === value?.id"
          radius="0px 3px 0px 10px"></Corner>
      </div>

      <div class="card-outer"
        :key="item.id"
        v-else
        v-show="!item.isHidden">
        <div class="size-card"
          @click="handleClick(item)"
          :class="{'is-active':item.id === value?.id,'upload':tabName == 'Upload','noDiscount':neonDiscount?.unitPercent == 0 || neonDiscount?.unitPercent >= 1}">
          <!-- 第一行 -->
          <span class="card-line-one">
            <span class="alias-name">
              {{item.alias}}
            </span>
            <template v-if="!item.onlyAddInquiry">
              <span class="price-part" v-if="neonDiscount?.unitPercent != 0 && neonDiscount?.unitPercent < 1">
                <span class="price">
                  <!-- 现价 -->
                  +<CCY-rate :decimal="2"
                    :price="item.unitPrice ? item.unitPrice * morePriceData.multiBasicUnitPrice * (1-neonDiscount?.unitPercent):0"></CCY-rate>
                </span>
                <span class="raw-price"
                  :class="{noDiscount:neonDiscount?.unitPercent == 0 || neonDiscount?.unitPercent >= 1}">
                  <!-- 原价 -->
                  +<CCY-rate :decimal="2"
                    :price="item.unitPrice?item.unitPrice * morePriceData.multiBasicUnitPrice:0"></CCY-rate>
                </span>
              </span>
            </template>
          </span>
          <div class="card-line-two"
            v-if="!item.onlyAddInquiry">
            <template v-if="isText">
              L: {{ (Number(fontWidth(item) / 0.3937 * unitConvertRatio)).toFixed(0)  + unitSign}} * H:
              {{ (Number(fontheight(item) / 0.3937 * unitConvertRatio)).toFixed(0) + unitSign}}
            </template>
            <template v-else>
              {{filterSize(item).str}}
            </template>
          </div>
          <Corner :active="item.id === value?.id"
            radius="0px 3px 0px 10px"></Corner>
        </div>

        <!-- <div class="tips"
          v-show="showTips(item)">
          <b class="icon-icon_Preview"></b>
          <p>
            {{ lang.quote.neon.overSizeTipsHead + ' ' + (unitSign == "cm" ? 114:45)  + unitSign + ' ' + lang.quote.neon.overSizeTipsTail }}
          </p>
        </div> -->
      </div>

    </template>

  </div>

</template>

<script>
import Corner from "@/components/Neon/Corner";
// import NeonProgressSelect from "./NeonProgressSelect";
import ProgressSelect from "@/components/Neon/ProgressSelect.vue";

export default {
  name: "SelectSize",
  model: {
    prop: "value",
    event: "change",
  },
  props: {
    tabName: {
      type: String,
    },
    value: {
      type: Object,
      default: () => {},
    },
    customSize: {
      type: Number,
      default: 0,
    },
    size_list: {
      type: Array,
    },
    dText: {
      type: String,
      default: () => "",
    },
    selectedFontData: {
      type: Array,
    },
    zoneRatio: {
      type: Number,
    },
    isText: {
      type: Boolean,
    },
    neonSampleData: {
      type: Object,
    },
    currentTemplate: {
      type: Object,
    },
    neonDiscount: {
      type: Object,
    },
    sliderOptions: {
      type: Object,
      default: () => {},
    },
    unitConvertRatio: {
      type: Number,
      default: 0.3937,
    },
    unitSign: {
      type: String,
      default: '"',
    },
    sizeRatio: {
      type: Number,
      default: 1,
    },
  },
  components: {
    Corner,
    ProgressSelect,
  },
  data() {
    return {
      finaHeight: 0,
      finaWidth: 0,
      customSizeMin: 0,
      offsetLeft: 0,
      defSign: '"',
      updateKey: 1,
      isRangeInput: true,
    };
  },

  // mounted(){
  //   console.log(this.neonDiscount)
  // },
  computed: {
    showTips() {
      let flag = false;
      if (this.isText) {
        if (this.value?.paramName === "Custom Size") {
          flag = this.customSize > 45;
        } else {
          flag =
            this.fontWidth(this.value) > 45 || this.fontheight(this.value) > 45;
        }
      } else {
        flag =
          this.filterSize(this.value).data.width > 45 ||
          this.filterSize(this.value).data.height > 45;
      }
      return flag;
    },
    morePriceData() {
      return (
        this.$store.getters.morePriceData || {
          multiAdditionalItemPrice: 1,
          multiBasicGrindingPrice: 1,
          multiEntiretyPrice: 1,
          multiBasicUnitPrice: 1,
          plusAdditionalitemPrice: 0,
          plusBasicGrindingPrice: 0,
          plusBasicUnitPrice: 0,
          plusEntiretyPrice: 0,
        }
      ); //价格分层
    },
    computedHeight() {
      return this.customSize
        ? (this.customSize * this.zoneRatio).toFixed(0)
        : "";
    },
    device() {
      return this.$store.state.device;
    },
    filterList() {
      let list = [];
      if (this.tabName == "Upload") {
        list = this.size_list.filter((x) => x.onlyAddInquiry == 1);
      } else if (this.isText) {
        list = this.size_list.filter(
          (x) => x.onlyAddInquiry == 0 || x.paramName == "Custom Size"
        );
      } else {
        list = this.size_list.filter(
          (x) => x.onlyAddInquiry == 0 && x.paramName != "Custom Size"
        );
      }
      if (
        this.selectedFontData[0] &&
        this.selectedFontData[0]["Select Color"]?.paramName ==
          "Gradient Changing Color"
      ) {
        list = list.filter(
          (x) => this.fontWidth(x) <= 94 && this.fontheight(x) <= 47
        );
      }
      return list;
    },
    country() {
      // 需要适配分隔符的国家标识 德国
      let code = this.$store.state.language.language;
      return ["de"].includes(code) ? code : "en";
    },
    lang() {
      return this.$store.getters.lang || {};
    },
  },
  methods: {
    resetProgress() {
      this.updateKey++;
    },
    // showTips(item) {
    //   let idEq = item.id == this.value?.id;
    //   let flag = false;
    //   if (this.isText) {
    //     flag = this.fontWidth(item) > 45 || this.fontheight(item) > 45;
    //   } else {
    //     flag =
    //       this.filterSize(item).data.width > 45 ||
    //       this.filterSize(item).data.height > 45;
    //   }
    //   return idEq && flag;
    // },
    filterSize(val) {
      let temp, target;
      let res = {
        str: 'L: 0" * H: 0"',
        data: { width: 0, height: 0 },
      };
      if (this.currentTemplate && this.currentTemplate.neonSampleData) {
        temp = JSON.parse(this.currentTemplate.neonSampleData);
        if (temp) {
          target = temp.find((x) => {
            return x.size == val.paramName;
          });
          if (target) {
            res.str = `L: ${
              ((Number(target.width) / 0.3937) * this.unitConvertRatio).toFixed(
                0
              ) + this.unitSign
            } * H: ${
              (
                (Number(target.height) / 0.3937) *
                this.unitConvertRatio
              ).toFixed(0) + this.unitSign
            }`;
            res.data.width = target.width;
            res.data.height = target.height;
          }
        }
      }
      return res;
    },
    findLongestText(arr) {
      let maxLength = 0;
      let longestText = "";
      for (let i = 0; i < arr.length; i++) {
        const text = arr[i]["Your Text"] || arr[i].placeholder;
        if (text.trim().length > maxLength) {
          maxLength = text.length;
          longestText = text;
        }
      }
      return longestText;
    },
    fontWidth(val) {
      let t =
        this.selectedFontData[0] &&
        (this.selectedFontData[0]["Your Text"] ||
          this.selectedFontData[0].placeholder);
      if (val?.sizeDefaultWidth && this.selectedFontData[0] && t) {
        this.selectedFontData[0]["Your Text"];
        const maxLongLine = this.findLongestText(this.selectedFontData);
        let tempList = maxLongLine.split("\n").sort((a, b) => {
          return b.trim().length - a.trim().length;
        });
        return (
          val.sizeDefaultWidth *
          tempList[0].trim().length *
          this.sizeRatio *
          0.3937
        ).toFixed(0);
      } else {
        return 0.0;
      }
    },
    fontheight(val) {
      if (this.zoneRatio) {
        return (this.fontWidth(val) * this.zoneRatio).toFixed(0);
      } else {
        return 0.0;
      }
    },
    changePercentage(v) {
      this.offsetLeft = v;
    },
    handleClick(v) {
      if (v.paramName != this.value?.paramName) {
        this.$emit("change", v);
      }
    },
    getAllAbleSizeDetail() {
      let list;
      list = this.filterList
        .filter((q) => !q.isHidden && q.paramName != "Custom Size")
        .map((x) => {
          let width = Number(this.fontWidth(x));
          let height = Number(this.fontheight(x));
          return {
            ...x,
            rectInfo: {
              width,
              height,
            },
          };
        });
      return list;
    },
    inputCustomSize(v, item) {
      let val = Number(((v * 0.3937) / this.unitConvertRatio).toFixed(4));
      this.$emit("changeCustomSize", val);
	  console.log('item', item)
	  this.handleClick(item)
    },
    blurCustomSize(val, item) {
      let nVal = Number(val);
      let { min, max } = this.sliderOptions;
      nVal < min && this.inputCustomSize(min, item);
      nVal > max && this.inputCustomSize(max, item);
    },
  },
};
</script>

<style lang="scss" scoped>
.select-size {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  column-gap: 11px;
  row-gap: 11px;
  @media screen and(max-width: 1273px) {
    gap: 10px;
  }
  .size-card {
    border: 1px solid #ccc;
    padding: 0.5em;
    border-radius: 0.3em;
    cursor: pointer;
    text-align: center;
    column-gap: 0.5em;
    row-gap: 0.625em;
    position: relative;

    &:hover {
      box-shadow: 0 0 5px 0 #ccc;
    }
    .card-line-one {
      .alias-name {
        font-size: 0.875em;
        font-family: Google Sans;
        color: #333;
        font-weight: bold;
      }
      .price-part {
        color: #333;
        font-family: Google Sans;
        font-weight: 400;
        .price {
          font-size: 1em;
          font-weight: 400;
        }
        .raw-price {
          font-size: 0.875em;
          text-decoration: line-through;
          @media screen and(max-width: 1273px) {
            font-size: 10px;
          }
          @media screen and(max-width: 768px) {
            display: none;
          }
          &.noDiscount {
            text-align: left;
            font-size: 1.125em;
            font-weight: 400;
            text-decoration: none;
            @media screen and(max-width: 1273px) {
              font-size: 15px;
            }
          }
        }
      }
    }

    .card-line-two {
      color: #666;
      margin-top: 10px;
    }

    &.is-active {
      border: 1px solid var(--neon-light-color);
      .corner {
        opacity: 1;
      }
    }
    &.upload {
      grid-template-columns: repeat(1, 1fr);
      padding-block: 14px;
      .alias-name {
        text-align: center;
      }
    }
    &.noDiscount {
      grid-template-columns: 1fr 1fr;
    }
  }
  .custom-size {
    grid-column-start: 1;
    justify-content: center;
    align-items: center;
    height: auto;
    grid-column-end: span 2;
    grid-template-columns: 1fr 5fr;
    padding-inline: 1.3em;
    display: flex;
    flex-direction: column;
    .custom-size-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      .alias-name {
        text-align: center;
        font-weight: bold;
      }
      .input-type {
        text-decoration: underline;
        color: #333;
        cursor: pointer;
        user-select: none;
      }
    }
    .custom-size-cont {
      width: 100%;
      .text-input-line {
        display: flex;
        align-items: center;
        .custom-input {
          display: flex;
          border-radius: 6px;
          padding: 10px;
          background-color: #ebebeb;
          input {
            width: 80px;
            text-align: center;
            @media screen and(max-width: 768px) {
              font-size: 16px;
            }
          }
        }
        .divider {
          width: 0.6em;
          height: 2px;
          background-color: #ebebeb;
          margin-inline: 0.4em;
        }
      }
    }
    @media screen and(max-width:768px) {
      height: 110px;
    }
  }
  .tips {
    position: relative;
    display: flex;
    grid-column-start: 1;
    grid-column-end: span 2;
    justify-content: center;
    align-items: center;
    padding: 0 10px;
    gap: 5px;
    font-size: 16px;
    font-weight: 400;
    color: #333333;
    &.custom {
      justify-content: left;
      border-radius: 8px;
    }
    @media screen and(max-width:768px) {
	  font-size: 1em;
    }
    b {
      color: #333333;
    }
    p {
      width: fit-content;
      font-family: Google Sans;
    }
  }
}
</style>
