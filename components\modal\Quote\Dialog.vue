<!--
介绍: 弹窗报价模板
buildWeb配置项: modal quoteUrl
-->
<template>
	<div class="lanyard-box" id="quote" noDebounce>
        <div class="close-icon extend-click-area" v-show="showCloseIcon" @click="closeDialog">
            <b  pointer class="icon-guanbi"></b>
        </div>
<!--        <div class="loading-box" v-if="isLoading">-->
<!--            <Loading></Loading>-->
<!--        </div>-->
		<iframe id="quoteIframe" :src="getLink" frameborder="0" width="100%" height="100%"></iframe>
	</div>
</template>

<script>
import { getUrlParams } from "@/utils/utils";
import Loading from "@/components/Loading.vue";

export default {
	name: "modalQuoteDialog",
	props: {
		data: {
			type: Object,
			default: {},
		},
	},
    components:{
        Loading
    },
	computed: {
		getLink() {
			return this.$store.state.language.lang ? window.origin + this.$store.state.language.lang + this.modal.quoteUrl : window.origin + this.modal.quoteUrl;
		},
	},
	data() {
		return {
            isLoading: false,
			modal: {
				style: {},
				quoteUrl: "/quote/lanyard-quote?type=quoteIframe",
				...this.data,
			},
			showCloseIcon: true,
		};
	},
	watch: {
		modal: {
			handler(val) {
				if (process.env.isManage) this.$emit("update:data", val);
			},
			immediate: true,
			deep: true,
		},
	},
	methods: {
		closeDialog() {
			let urlParams = getUrlParams(this.getLink);
			if (urlParams?.isBack) {
				let quoteIframe = document.getElementById("quoteIframe");
				quoteIframe.contentWindow.postMessage(
					{
						type: "closeDialog",
					},
					window.origin
				);
			} else {
				this.$store.commit("setMask", false);
			}
		},
		selectStyle() {
			let quoteIframe = document.getElementById("quoteIframe");
			quoteIframe.contentWindow.postMessage(
				{
					type: "selectStyle",
					value: this.modal.id,
				},
				window.origin
			);
		},
	},
	mounted() {
        this.isLoading = true;
        const iframe = document.getElementById('quoteIframe');
        console.log(iframe)
        iframe.onload = ()=>{
            console.log('iframe内容加载完成');
            this.isLoading = false;
        };
		let that = this;
		window.addEventListener("message", function (event) {
			if (event.origin === window.origin) {
				// 验证消息来源
				let data = event.data;
				if (data.type === "toHome") {
					window.location.href = "/";
				} else if (data.type === "toCart") {
					window.location.href = "/cart";
				} else if (data.type === "changeUrl") {
					if (data.target === "_black") {
						window.open(data.url);
					} else {
						window.location.href = data.url;
					}
				} else if (data.type === "changeDialogUrl") {
					that.modal.quoteUrl = data.url;
				} else if (data.type === "selectStyle") {
					that.selectStyle();
				} else if (data.type === "closeDialog") {
					if (data?.data?.isUpdate) {
						//通知购物车刷新列表
						//alert("娃哈哈呀娃哈哈");
						that.$Bus.$emit("updateCartList");
						that.$store.commit("setMask", false);
					} else {
						that.$store.commit("setMask", false);
					}
				} else if (data.type === "showCloseIcon") {
					that.showCloseIcon = data.showCloseIcon;
				}
			}
		});
	},
};
</script>

<style lang="scss" scoped>
.lanyard-box {
	background: white;
	position: relative;
    border-radius: 0.5em 0.5em 0 0;

    .loading-box{
        display: flex;
        justify-content: center;
        align-items: center;
        position: absolute;
        inset: 0;
    }

    #quoteIframe{
        border-top-left-radius: 0.5em;
        border-top-right-radius: 0.5em;
    }

	::v-deep #lanyard {
		height: 100%;
	}
}
</style>
