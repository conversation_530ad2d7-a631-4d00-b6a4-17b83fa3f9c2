<script>
import ProductTable from "@/components/modal/Half/Detail/ProductDes/ProductTable.vue";
import BaseDialog from "@/components/Quote/BaseDialog.vue";

export default {
	props: {
		productInfo: {
			type: Object,
		},
        isCoins:{
            type: Boolean
        }
	},
	components: {
		BaseDialog,
		ProductTable,
	},
	computed: {
		list() {
            let list = [
                {
                    url: "https://static-oss.gs-souvenir.com/web/quoteManage/20250418/Free-Artwork_20250418PbMTHr.png",
                    alt: this.langSemiCustom.ast,
                },
                {
                    url: "https://static-oss.gs-souvenir.com/web/quoteManage/20250418/Free-Worldwide-Shipping_20250418Dih2BH.png",
                    alt: this.langSemiCustom.fws,
                },
                {
                    url: "https://static-oss.gs-souvenir.com/web/quoteManage/20250418/Expert-Support_2025041827GRZ8.png",
                    alt: this.langSemiCustom.eso,
                },
                {
                    url: "https://static-oss.gs-souvenir.com/web/quoteManage/20250418/100-Satisfaction_20250418epBprs.png",
                    alt: this.langSemiCustom.sg
                },
            ]
			if (!this.$store.getters.isFreeCountry) {
                list[1].alt = this.langSemiCustom.fastWs
			}
            return list
		},
		langSemiCustom() {
			return this.$store.getters.lang?.semiCustom;
		},
		proId() {
			return this.$store.state.proId;
		},
		proSystem() {
			return this.$store.state.proSystem;
		},
		isMobile() {
			return this.$store.state.device === "mb";
		},
		productTableType() {
			if (this.isMobile) {
				return 3;
			} else {
				return 1;
			}
		},
		productTableData() {
            return this.productInfo?.productSpecTag ? JSON.parse(this.productInfo.productSpecTag) : [];
		},
	},
	data() {
		return {
			productTableDialog: false,
		};
	},
	methods: {
		createQRcode(logoSrc, logoSize = 60) {
			if (this.isMobile) logoSize = 40;
			//二维码 尝试
			let QRCode = require("qrcodejs2");
			const ele = this.$refs.qrcode;
			const styles = window.getComputedStyle(ele);
			const width = styles.getPropertyValue("width");
			const height = styles.getPropertyValue("height");
			const qrcode = new QRCode(ele, {
				text: window.location.href,
				width: parseInt(width),
				height: parseInt(height),
				colorDark: "#000000",
				colorLight: "#ffffff",
				correctLevel: QRCode.CorrectLevel.H,
			});
			if (!logoSrc) return;
			const logoImg = new Image();
			logoImg.setAttribute("crossOrigin", "anonymous");
			logoImg.src = logoSrc;
			logoImg.onload = () => {
				const canvas = qrcode._el.getElementsByTagName("canvas")[0];
				const qrcodeImg = ele.querySelector("img");
				const context = canvas.getContext("2d");
				const x = (canvas.width - logoSize) / 2;
				const y = (canvas.height - logoSize) / 2;
				context.drawImage(logoImg, x, y, logoSize, logoSize);
				const imageUrl = canvas.toDataURL("image/png");
				qrcodeImg.src = imageUrl;
				qrcode.im = imageUrl;
			};
		},
	},
	mounted() {
		if (this.proId === 446) {
			let logoSrc = this.proSystem.logo;
			this.createQRcode(logoSrc);
		}
	},
};
</script>

<template>
	<div class="product-des">
		<div class="des-tip" v-if="$store.state.proTheme == '11'">产品信息</div>
		<div class="free-tip">
			<template v-for="(item, index) in list">
				<div class="tip-item" :key="index">
					<img width="36" :src="item.url" :alt="item.alt" :title="item.alt" />
					<span>{{ item.alt }}</span>
				</div>
				<div class="tip-divider" :class="`tip-divider${index}`"></div>
			</template>
		</div>
		<div class="des-box">
			<ProductTable v-if="productTableData && productTableData.length" :table-data="productTableData" :type="productTableType" @showTableDialog="productTableDialog = true"></ProductTable>
			<div class="des" v-else v-html="productInfo.richDescription"></div>
            <div class="ts">
				<span>
					<b class="icon-tishi1"></b>
				</span>
				<span v-if="!isCoins">{{ langSemiCustom.halfDetailTip }}</span>
                <span v-else>{{ langSemiCustom.coins.noteText }}</span>
			</div>
			<div ref="qrcode" id="qrcode" v-if="proId === 446" style="width: 150px; height: 150px"></div>
		</div>

		<BaseDialog v-model="productTableDialog" width="100%" :hideClose="true" position="bottom">
			<div class="productTableDialogContent">
				<ProductTable :table-data="productTableData" :type="2"></ProductTable>
				<div class="btnWrap">
					<button primary @click="productTableDialog = false">Got It</button>
				</div>
			</div>
		</BaseDialog>
	</div>
</template>

<style scoped lang="scss">
.product-des {
	overflow: hidden;
	font-size: 12px;

	.des-box {

        .des {
            position: relative;
            overflow: hidden;
            padding: 4px 10px;
            background-color: #ffffff;
            line-height: 2em;
            word-break: break-word;

            ::v-deep p {
                margin-bottom: 0;
                line-height: 1.4;
                font-family: inherit;
                font-size: 14px;
            }
        }

		.ts {
			display: block;
			padding: 0 4px;
			margin-top: 10px;

			b {
				margin-right: 4px;
				color: red;
			}

			@include respond-to(mb) {
				display: flex;
				padding: 4px 10px;
				margin: 10px 0 0;
				background-color: #ffffff;
			}
		}

		@include respond-to(mb) {
			#qrcode2 {
				margin: 0px 0px 24px 24px;
			}
		}
	}

	.free-tip {
		display: flex;
		justify-content: space-between;
		align-items: center;
		gap: 3%;
		margin: 20px 0;

		@include respond-to(mb) {
			flex-wrap: wrap;
			padding: 4px 10px;
			margin: 10px 0;
            gap: 10px;
			background-color: #ffffff;
		}

		.tip-item {
			position: relative;
			display: flex;
			justify-content: flex-end;
			align-items: center;

			@include respond-to(mb) {
				flex:0 0 45%;
				justify-content: flex-start;
			}

			img {
				margin-right: 4px;
				width: 40px;
			}

            span{
                word-break: break-word;
            }
		}

		.tip-divider {
			height: 20px;
			width: 1px;
			margin: 0 2px;
			background-color: #e4e4e4;

			&:last-child {
				display: none;
			}

			@include respond-to(mb) {
				&.tip-divider1 {
					display: none;
				}
			}
		}
	}

	@include respond-to(mb) {
		margin: 0 -10px;
		background-color: #eeeeee;
		.des-tip {
			margin-top: 20px;
			font-size: 16px;
		}
	}
}

[theme="11"] .product-des {
	.des-tip {
		margin-top: 30px;
		background: $color-dark;
		border-radius: 20px 0 20px 0;
		font-size: 18px;
		padding-left: 24px;
		font-weight: 500;
		color: #fffcf1;
	}

	.des-box {
		margin-top: 30px;
		border-radius: 10px;
		overflow: hidden;

		#qrcode {
			margin-top: 10px;
		}
	}
}

.productTableDialogContent {
	padding: 10px;
	border-top-left-radius: 6px;
	border-top-right-radius: 6px;
	background-color: #ffffff;

	.btnWrap {
		padding: 10px 10px 0;
		text-align: center;
	}
}
</style>
