<template>
	<div id="revenue-wrapper">
		<div id="revenue-pie-show" style="margin-right: 10px;"></div>
		<div class="pie-right">
			<div class="item" v-for="item in pieRightData" :key="item.name">
				<span>{{ item.percentage }}%</span>
				<span>${{ item.value }}</span>
			</div>
		</div>
	</div>
</template>

<script>
import {format} from '@/utils/analysismain'

export default {
	name: "PieShow",
	props: {
		filtByTime: {
			type: [Number, String]
		},
		revenueData: {
			type: Array,
			default() {
				return []
			}
		}
	},
	watch: {
		filtByTime: {
			handler(newValue) {
				console.log('newValue', newValue);
			}
		},
		revenueData(newValue) {
			let resultArr = []
			let Total = newValue.reduce((previousValue, currentValue) => previousValue + Number(currentValue.value), 0)
			// console.log(Total, 'Total');
			let placeholderObject = {
				value: Total,
				// name: "placeholder",
				itemStyle: {
					color: "transparent",
				},
				tooltip: {
					show: false
				}
			}
			resultArr = [...newValue, placeholderObject]
			this.option.series[0].data = resultArr

			let pieRightData = format(newValue);
			console.log('pieRightData', pieRightData);
			this.pieRightData = pieRightData
			this.myChartRender()
		}
	},
	data() {
		return {
			pieRightData: [],
			chartDom: '',
			option: {
				graphic: {
					type: "text",
					left: "20%",
					top: "48%",
					z: 994,
					style: {
						text: "Revenue",
						fill: "#333",
						fontSize: 16,
					},
				},
				color: ["#F4B427", "#4D3FD2", "#49C9C9", "#DCE4E9", "#D2691E", "#F08080"],
				title: {
					text: "Revenue",
					subtext: "by category",
					left: "270",
					top: "25",
				},
				tooltip: {
					trigger: "item",
					formatter: "{b} {d}% {c} {a}",
				},
				legend: {
					orient: "vertical",
					left: "270",
					top: "85",
					itemGap: 10,
					itemWidth: 14,
					itemHeight: 14,
				},
				series: [
					{
						label: {
							show: false,
							position: "inside",
							formatter: "{d}%",
							color: "#ffff",
						},
						itemStyle: {
							borderWidth: 3, //边框的宽度
							borderColor: "#fff", //边框的颜色
						},
						top: "8%",
						left: "-210",
						name: "Revenue",
						type: "pie",
						radius: ["50%", "75%"],
						startAngle: 180,
						data: [
							{value: 1048, name: "Lapel Pins"},
							{value: 735, name: "Stickers"},
							{value: 580, name: "Lanyards"},
							{value: 484, name: "Challenge Coins"},
							{
								value: 1048 + 735 + 580 + 484,
								name: "placeholder",
								itemStyle: {
									color: "transparent",
								},
								tooltip: {
									show: false
								}
							},
						],
						emphasis: {
							disabled: true,
							itemStyle: {
								shadowBlur: 10,
								shadowOffsetX: 0,
								shadowColor: "rgba(0, 0, 0, 0.5)",
								borderWidth: 0,
							},
						},
					},
				],
			}
		};
	},
	mounted() {
		this.chartDom = document.getElementById("revenue-pie-show");
	},
	methods: {
		myChartRender() {
			this.$echarts.init(this.chartDom).setOption(this.option);
		}
	}
};
</script>

<style lang="scss" scoped>
#revenue-wrapper {
	display: flex;
	width: 100%;
	height: 100%;
	box-shadow: 1px 1px 6px #d6d6d6;
	border-radius: 0.3125vw;

	#revenue-pie-show {
		width: 65%;
		height: 100%;
		background: #ffffff;
	}

	.pie-right {
		width: 35%;
		height: 100%;
		padding-top: 87px;
		background: #ffffff;
		font-size: 14px;

		.item {
			margin-bottom: 5px;
			margin-bottom: 5px;
			display: flex;
			justify-content: space-between;
			padding-right: 45px;
		}
	}
}
</style>
