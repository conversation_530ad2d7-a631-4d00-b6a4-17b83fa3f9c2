<template>
	<div class="comment">
		<textarea class="myTextarea222" :style="customStyle" :placeholder="commentPlaceholder" ref="myTextarea222"
			v-model="commentObj.comments"></textarea>
		<div class="des" v-show="canUploadFile && commentObj.uploadList.length == 0">
			<b class="icon-shangchuan myIcon"></b><a href="javascript:;" @click="openUpload">{{ langQuote.uf
				}}</a>
		</div>
		<div class="fileWrap">
			<div class="file-item" v-for="(item, index) in commentObj.uploadList" :key="item.secure_url"
				@click="zoomPic(item.secure_url)">
				<div class="fileInfo d-flex align-items-center">
					<v-icon class="mr-1">mdi-file</v-icon>
					<span class="fileName">{{ item.original_filename }}</span>
				</div>
				<div class="control">
					<v-btn small icon>
						<v-icon>mdi-check</v-icon>
					</v-btn>
					<v-btn small icon @click.stop="deletePic(index)">
						<v-icon>mdi-trash-can</v-icon>
					</v-btn>
				</div>
			</div>
		</div>
		<input type="file" ref="upload222" @change="uploadPic" />
	</div>
</template>

<script>
import { uploadFile } from "@/utils/oss";
import { isImageType } from "@/utils/utils";
export default {
	name: 'comment',
	props: {
		commentObj: {
			type: Object,
			default: () => ({
				comments: "",
				uploadList: [],
			})
		},
		customStyle: {
			type: Object,
			default: () => ({})
		},
		canUploadFile: {
			type: Boolean,
			default: true
		},
		placeholder: {
			type: String,
			default: ""
		}
	},
	components: {},
	data() {
		return {

		}
	},
	watch: {
		commentObj: {
			handler(val) {
				this.$emit('uploadNowData', val)
			},
			deep: true
		}
	},
	computed: {
		langQuote() {
			return this.$store.getters.lang?.quote;
		},
		commentPlaceholder() {
			return this.placeholder || 'Provide information about your artwork'
		},
	},
	methods: {
		openUpload() {
			this.$refs.upload222.click();
		},
		uploadPic(event) {
			this.$gl.show()
			let files = event.target.files,
				size = files[0].size;
			if (size / 1020 / 1024 > 80) {
				this.$toast.error("File size cannot exceed 80m.");
				this.$refs.upload222.value = "";
				return;
			}
			uploadFile(files[0]).then((res) => {
				this.commentObj.uploadList.push({
					original_filename: files[0].name,
					secure_url: res,
				});
				this.$gl.hide();
				this.$refs.upload222.value = "";
			}).finally(() => {
				this.$gl.hide();
			});
		},
		deletePic(index) {
			this.commentObj.uploadList.splice(index, 1);
		},
		zoomPic(img) {
			if (isImageType(img)) {
				this.$viewerApi({
					images: [img],
				});
			}
		},
	},
	created() { },
	mounted() { },
}
</script>

<style scoped lang="scss">
.comment {
	.myTextarea222 {
		box-sizing: border-box;
		width: 100%;
		min-height: 120px;
		max-height: 240px;
		// overflow: hidden;
		scrollbar-width: thin;
		padding: 10px;
		border-radius: 6px;
		background-color: #fafafa;
		border: 1px solid #DBDBDB;
		font-size: 14px;

		&:focus {
			border: 2px solid $color-primary;
		}

		&::placeholder {
			font-weight: 400;
			font-size: 14px;
			color: #999999;
		}
	}

	.fileWrap {
		margin-top: 10px;

		.file-item {
			display: flex;
			justify-content: space-between;

			.control {
				margin-left: 40px;
				flex-shrink: 0;

				b {
					margin: 0 2px;
					cursor: pointer;
				}
			}
		}
	}

	.des {
		color: $color-primary;
		margin-top: 4px;
		font-size: 14px;

		b {
			margin-right: 6px;
		}
	}

	@include respond-to(mb) {
		.myTextarea222 {
			min-height: 90px;
		}

		.fileWrap {
			.file-item {
				font-size: 12px;

				.v-icon {
					font-size: 18px;
				}
			}
		}

		.des {
			font-size: 12px;
		}
	}

	input[type="file"] {
		display: none;
		position: absolute;
		z-index: -1;
		opacity: 0;
	}
}
</style>
