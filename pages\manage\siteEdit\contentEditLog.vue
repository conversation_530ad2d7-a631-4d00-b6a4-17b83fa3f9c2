<template>
	<div class="content-edit-log">
		<v-form>
			<v-container>
				<v-row>
					<v-col>
						<v-autocomplete
							offset-y
							v-model="retailerUserId"
							:items="userList"
							item-value="id"
							label="User"
							:item-text="getTextForItem"
							dense outlined hide-details clearable return-object
						>
						</v-autocomplete>
					</v-col>

					<v-col>
						<v-autocomplete
							v-model="queryForm.pageId"
							:items="pageList"
							item-text="pageName"
							item-value="id"
							label="Page Name"
							dense outlined hide-details clearable />
					</v-col>

					<v-col>
						<v-select v-model="queryForm.isMobile" :items="pageTypes" label="Page Type" item-text="text" item-value="value" dense outlined hide-details clearable />
					</v-col>

					<v-col>
						<v-select v-model="queryForm.type" :items="logTypeOptions" label="Log Type" item-text="text" item-value="value" dense outlined hide-details clearable />
					</v-col>

					<v-col>
						<v-menu ref="menu" v-model="queryDatesMenu" :close-on-content-click="false" transition="scale-transition" min-width="auto" offset-y>
							<template v-slot:activator="{ on, attrs }">
								<v-text-field v-model="queryDatesText" v-bind="attrs" v-on="on" label="Date Range" readonly dense outlined hide-details clearable></v-text-field>
							</template>
							<v-date-picker v-model="queryDates" range no-title scrollable>
								<v-spacer></v-spacer>
								<v-btn text color="primary" @click="queryDatesMenu = false"> Cancel</v-btn>
								<v-btn text color="primary" @click="$refs.menu.save(queryDates)"> OK</v-btn>
							</v-date-picker>
						</v-menu>
					</v-col>

					<v-col>
						<v-text-field v-model="queryForm.keyWord" label="Search..." prepend-inner-icon="mdi-magnify" dense outlined hide-details clearable />
					</v-col>

					<v-col>
						<v-btn color="primary" @click="searchApply">Apply</v-btn>
					</v-col>
				</v-row>
			</v-container>
		</v-form>

		<v-container>
			<v-data-table :headers="headers" :items="records" :loading="loading" hide-default-footer>
				<template v-slot:item.isHeadFoot="{ item }">
					{{ !!item.isHeadFoot ? "YES" : "NO" }}
				</template>
				<template v-slot:item.isMobile="{ item }">
					{{ !!item.isMobile ? "YES" : "NO" }}
				</template>
				<template v-slot:item.pageType="{ item }">
					{{ findType(item.isMobile,"pageTypes") }}
				</template>
				<template v-slot:item.userName="{ item }">
					{{ findUserName(item.retailerUserId) }}
				</template>
				<template v-slot:item.logType="{ item }">
					{{ findType(item.type,"logTypeOptions") }}
				</template>
				<!-- <template v-slot:item.beforeData="{ item }">
					<div class="text-ellipsis">{{ item.beforeData }}</div>
				</template>
				<template v-slot:item.afterData="{ item }">
					<div class="text-ellipsis">{{ item.afterData }}</div>
				</template> -->
				<template v-slot:item.logDetails="{ item }">
					<v-btn @click="clickRow(item)">log</v-btn>
				</template>
			</v-data-table>

			<div class="text-right mt-2">
				<Pagination ref="pagination" :length="pages" @changePages="changePages"></Pagination>
			</div>
		</v-container>

		<v-dialog v-model="dialog" width="80%" scrollable>
			<v-card>
				<v-card-title class="text-h5 grey lighten-2">Diff Details</v-card-title>
				<v-card-text style="padding-top: 20px">
					<v-row v-for="diff in diffDetails" :key="diff.id">
						<v-col cols="2">
							<v-select :value="diff.language" :items="diff.languages" label="Pick Language" dense outlined hide-details @change="diffLanguageChange(diff, $event)" />
						</v-col>
						<v-col cols="10">
							<CodeDiff :old-string="diff.beforeStr" :new-string="diff.afterStr" diff-style="word" language="json" output-format="side-by-side" hide-header />
						</v-col>
					</v-row>
				</v-card-text>

				<v-divider></v-divider>

				<v-card-actions>
					<v-spacer></v-spacer>
					<v-btn color="primary" text @click="dialog = false"> OK </v-btn>
				</v-card-actions>
			</v-card>
		</v-dialog>
	</div>
</template>

<script>
import { getPageList, getPageRowLogList, getUserListByProId } from "@/api/manage/siteEditLog";
import { CodeDiff } from "v-code-diff";
import Pagination from "@/components/Pagination.vue";

export default {
	name: "contentEditLog",

	components: { Pagination, CodeDiff },

	data() {
		return {
			userList: [],
			pageList: [],
			pageTypes: [
				{ text: "PC Page", value: 0 },
				{ text: "Mobile Page", value: 1 },
			],
			logTypeOptions: [
				{ text: "Page", text1: "页面", value: 1 },
				{ text: "GlobalSetting", text1: "全局设置", value: 2 },
				{ text: "Header", text1: "页头", value: 3 },
				{ text: "PageLine", text1: "页面行", value: 4 },
				// { text: "ThemeColor", text1: "主题色", value: 5 },
				{ text: "ImportedPageLineTranslationData", text1: "页面行翻译数据导入", value: 5 },
				{ text: "ProductShelf", text1: "产品上架", value: 6 },
				{ text: "PageCopy", text1: "页面复制", value: 7},
				{text:"Image Edit" ,text1:"图片编辑",value:8}
			],

			queryDatesMenu: false,
			queryDates: [],

			queryForm: {
				pageId: "", // 页面id
				isMobile: "", // 页面类型 0:pc 1:mb

				keyWord: "",
				type: "",
			},
			retailerUserId: "", // 经销商用户id
			page: 1,
			pages: 1,
			pageSize: 10,

			loading: false,
			headers: [
				{ text: "Id", value: "id", sortable: false, width: 50 },
				{ text: "User Id", value: "retailerUserId", sortable: false, width: 100 },
				{ text: "User Name", value: "userName", sortable: false, width: 100 },
				{ text: "Project Id", value: "proId", sortable: false, width: 100 },
				{ text: "Page Id", value: "pageId", sortable: false, width: 100 },
				{ text: "Page Name", value: "pageName", sortable: false, width: 100 },
				{ text: "Language", value: "language", sortable: false, width: 100 },
				{ text: "Is HeadFoot", value: "isHeadFoot", sortable: false, width: 100 },
				{ text: "Is Mobile", value: "isMobile", sortable: false, width: 100 },
				{ text: "Log Type", value: "logType", sortable: false, width: 100 },
				{ text: "Operation time", value: "createTime", sortable: false, width: 170 },
				{ text: "Log Details", value: "logDetails", sortable: false, width: 80 },
				// { text: "Before Data", value: "beforeData", sortable: false },
				// { text: "After Data", value: "afterData", sortable: false },
			],
			records: [], // 接口返回数据
			diffDetails: [], // Dialog 展示的比对详情

			dialog: false,
		};
	},

	computed: {
		queryDatesText: {
			get() {
				return this.queryDates.join(" ~ ");
			},

			set(value) {
				if (!value) this.queryDates = [];
			},
		},
	},

	methods: {
		async loadData() {
			this.loading = true;
			getPageRowLogList({
				...this.queryForm,
				retailerUserId:this.retailerUserId.id,
				startTime: this.queryDates[0] || "",
				endTime: this.queryDates[1] || "",
				page: this.page,
				pageSize: this.pageSize,
			})
				.then((resp) => {
					this.records = resp.data?.records;
					this.pages = resp.data?.pages;
				})
				.finally(() => {
					this.loading = false;
				});
		},

		changePages(page) {
			if (this.page === page) return;
			this.page = page;
			this.loadData();
		},

		findType(flag,options) {
			const page = this[options].find((item) => item.value === flag);
			if (!page) return "";
			return page.text;
		},

		findUserName(id) {
			const user = this.userList.find((item) => item.id === id);
			if (!user) return "";
			return user.userName?user.userName:user.email;
		},

		searchApply() {
			this.page = 1;
			this.$refs.pagination.page = 1;
			this.loadData();
		},

		handleDiffDetailByLanguage(row, language) {
			const before = row.itemLeft?.pageRowDraftTranslatesList?.find((item) => item.language === language);
			const after = row.itemRight?.pageRowDraftTranslatesList?.find((item) => item.language === language);
			let beforeStr = row.isMobile ? before?.mbSampleData : before?.sampleData;
			let afterStr = row.isMobile ? after?.mbSampleData : after?.sampleData;
			if(!row.itemLeft?.pageRowDraftTranslatesList && !row.itemRight?.pageRowDraftTranslatesList ){ //不是页面的
				beforeStr = JSON.stringify(row.itemLeft)
				afterStr = JSON.stringify(row.itemRight)
			}
			return {
				beforeStr: beforeStr ? JSON.stringify(JSON.parse(beforeStr), null, 2) : "",
				afterStr: afterStr ? JSON.stringify(JSON.parse(afterStr), null, 2) : "",
			};
		},

		handleDiffDetails(record) {
			// const beforeData = JSON.parse(record.beforeData);
			// const afterData = JSON.parse(record.afterData);
			const beforeData =  Array.isArray(JSON.parse(record.beforeData)) ? JSON.parse(record.beforeData) : [{...JSON.parse(record.beforeData)}];
			const afterData =  Array.isArray(JSON.parse(record.afterData)) ? JSON.parse(record.afterData) : [{...JSON.parse(record.afterData)}];

			// console.log("AAA",beforeData);
			// console.log("BBB",afterData);
			const list = [];

			for (let i = 0; i < beforeData.length; i++) {
				const itemLeft = beforeData[i];
				let itemRight
				if(itemLeft.pageRowDraftTranslatesList){
					itemRight = afterData.find((a) => a.id === itemLeft.id) ?? {};
				}else{
					// itemRight = afterData.find((a) => a.websiteStyleIdDraft === itemLeft.websiteStyleIdDraft) ?? {};
					itemRight = afterData[i]
				}
				const data = {
					id: record.id + "-" + itemLeft.id,
					isMobile: record.isMobile,
					language: record.language,
					languages: itemLeft.pageRowDraftTranslatesList?.map((item) => item.language),
					itemLeft,
					itemRight,
				};

				list.push(Object.assign({}, data, this.handleDiffDetailByLanguage(data, record.language)));
			}

			return list;
		},

		diffLanguageChange(row, language) {
			Object.assign(row, this.handleDiffDetailByLanguage(row, language), { language });
		},

		clickRow(row) {
			this.diffDetails = this.handleDiffDetails(row);
			this.dialog = true;
		},
		getTextForItem(item){
			return item.userName?item.userName:item.email
		},
	},

	mounted() {
		Promise.all([getUserListByProId(), getPageList()])
			.then(([userListResp, pageListResp]) => {
				this.userList = userListResp.data;
				this.pageList = pageListResp.data;
			})
			.then(() => {
				this.loadData();
			});
	},
};
</script>

<style scoped lang="scss">
.content-edit-log {
	.text-ellipsis {
		height: 3em;
		overflow: hidden;
		word-break: break-all; /* 内容自动换行 */
	}

	.diff-details {
		padding: 16px 24px 10px;
	}
}
</style>
