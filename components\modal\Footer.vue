<template>
	<div scrollbar :theme="modal.theme" id="modalFooter" :class="modal.class" :style="modal.style">
		<footer flex class="modal-box" :style="modal.boxStyle">
			<div v-for="(o, oi) in modal.outer" class="hover-tag part2" :childHoverIndex="oi" :key="oi"
				:class="{ [o.class || '']: o.class == 'nav' ? modal.outer.length > 1 : true, expand: modal.expand ? o.expand : navIndex == oi }"
				:style="{ ...modal.cardStyle, ...o.style }">

				<div v-if="o.logo" pointer class="logo-box" :style="o.logoStyle"
					@click="setModalType({}, modal.outer, '', { url: '/' })">
					<pic :src="proSystem.logoFooter || proSystem.logo"
						:alt="$store.state.logoAltTitle || $store.state.proName" />
				</div>


				<div flex v-if="o.title" class="title" :style="{ ...modal.titleStyle, ...o.titleStyle }"
					@click="modal.expand ? (o.expand = !o.expand) : (navIndex = oi)">
					<b v-if="o.title.icon" :class="o.title.icon"></b>
					<EditDiv :tagName="o.title.tagName" v-model:content="o.title.value"
						@click="setModalType(o.title, modal.outer, 'text')" />
					<b hidden class="icon-Up arrow" :class="{ rotate: modal.expand ? !o.expand : navIndex != oi }"></b>
				</div>
				<EditDiv v-if="o.text" class="text" v-model:content="o.text.value" :style="o.textStyle"
					@click="setModalType(o.text, modal.outer, 'text')" />


				<div flex :pointer="o.reviews.url" class="reviews" :style="modal.reviewsStyle" v-if="o.reviews"
					@click="setModalType(o.reviews, modal.outer, 'reviews', o.reviews)">
					<span v-show="o.reviews.showScore && o.reviews.star > 1">{{ o.reviews.star }}</span>
					<div class="star-box" v-show="o.reviews.star > 1"
						:style="{ ...modal.reviewsStarStyle, backgroundImage: 'linear-gradient(90deg,' + (o.reviews.starColor || '#F96A00') + ' ' + o.reviews.star * 20 + '%,#999 0)' }">
						<b v-for="s in 5" :class="o.reviews.starIcon || 'icon-star'"></b>
					</div>
					<template v-if="o.reviews.number">
						<span :style="modal.reviewsExcellentStyle">{{ lang.excellent }}</span>
						<span :style="modal.reviewsReviewsStyle"
							v-html="o.reviews.number.toLocaleString() + '+ ' + lang.reviews + ' ' + lang.on"></span>
						<pic :style="modal.reviewsImgStyle" :alt="lang.shopperApproved"
							:src="o.reviews.img || 'https://sticker-static.oss-accelerate.aliyuncs.com/image/uploads/20230728/ShopperApproved.png'" />
					</template>
				</div>


				<div v-if="o.map" :style="o.mapStyle">
					<div id="map" style="width:100%;height:100%;"></div>
					<script>
						function initMap(){let addressLatLng = {lat: Number(window.$nuxt?.$store.state.country?.latitude||0), lng: Number(window.$nuxt?.$store.state.country?.longitude||0)},map = new google.maps.Map(document.getElementById('map'), {center: addressLatLng, zoom: 8}),marker = new google.maps.Marker({position: addressLatLng, map: map, title: window.$nuxt?.$store.state.proName})}
					</script>
				</div>


				<label v-if="o.subscribeButton" flex class="subscribe-box" :style="o.subscribeStyle">
					<input v-model="email" type="text" :placeholder="o.subscribeInput?.value" />
					<button v-throttle :primary="o.subscribeButton.value" :style="o.subscribeButton.style"
						@click="subscribe">
						{{ o.subscribeButton.value }}
						<b v-show="o.subscribeButton.icon" :class="o.subscribeButton.icon"></b>
					</button>
				</label>


				<div v-if="o.navList" class="link-box" :style="{ ...modal.navListStyle, ...o.navListStyle }"
					@click="setModalType(o.navList, modal.outer, 'img_list')">
					<div v-if="o.agree" class="agree-box" @click="agree = !agree">
						<b v-show="agree" pointer class="icon-cc-yes-crude"></b>
					</div>
					<div flex v-for="n in o.navList" :title="n.value" :style="{ ...modal.navStyle, ...o.navStyle }"
						:select="$store.state.pagePath == n.url?.split('#')[0]" :key="n.value">
						<b :class="n.icon" v-if="n.icon"></b>
						<label v-if="!n.url" v-html="n.value"></label>
						<n-link tag="a" v-else-if="n.url.startsWith('/')" :to="n.url" v-html="n.value" pointer></n-link>
						<a v-else @click="setModalType(null, null, '', n)" v-html="n.value"></a>
					</div>
				</div>


				<div v-if="o.imgList" flex class="img-box" :style="o.imgListStyle"
					@click="setModalType(o.imgList, modal.outer, 'img_list', null, oi)">
					<template v-for="p in o.imgList">
						<a v-if="p.url" :href="p.url" target="_blank">
							<pic :src="p.value" :alt="p.alt" :style="o.imgStyle" />
						</a>
						<span v-else>
							<pic :src="p.value" :alt="p.alt" :style="o.imgStyle" />
						</span>
					</template>
				</div>


				<div v-if="o.contact" flex class="contact-box" :style="o.contactStyle">
					<div flex v-if="o.contact.address">
						<b :class="o.contact.address.icon" v-if="o.contact.address.icon"></b>
						<EditDiv v-model:content="o.contact.address.value"
							@click="setModalType(o.contact.address, modal.outer, 'text')" />
					</div>
					<div flex v-if="o.contact.phone" @click="setModalType(o.contact.phone, modal.outer, 'text')">
						<b :class="o.contact.phone.icon" v-if="o.contact.phone.icon"></b>
						<EditDiv tagName="a" :href="'tel:' + o.contact.phone.value.match(phoneRegex)"
							v-model:content="o.contact.phone.value" />
					</div>
					<div flex v-if="o.contact.email" @click="setModalType(o.contact.email, modal.outer, 'text')">
						<b :class="o.contact.email.icon" v-if="o.contact.email.icon"></b>
						<EditDiv tagName="a" :href="'mailto:' + o.contact.email.value.match(emailRegex)[0]"
							v-model:content="o.contact.email.value" />
					</div>
				</div>


				<div v-if="o.right" class="right-box" :style="l.style" v-for="l in modal.list" :key="l.title?.value">
					<span>{{ lang.copyright }} © {{ new Date().getFullYear() }}</span>
					<EditDiv tagName="span" v-model:content="l.title.value" v-if="l.title"
						@click="setModalType(l.title, modal.list, 'text')">
					</EditDiv>
				</div>
			</div>
		</footer>



		<div class="modal-box copyright" v-for="(l, li) in modal.list" :style="{ ...modal.rightStyle, ...l.style }"
			:key="li" :hidden="modal.outer.find(o => o.right)">
			<a v-if="$store.state.proId == 446" flex class="right-zh" rel="noreferrer"
				href="https://beian.mps.gov.cn/#/query/webSearch?code=32058302004869" target="_blank">
				<img
					src="https://static-oss.gs-souvenir.com/web/2024/other/20241210/75%E5%A4%87%E6%A1%88%E5%9B%BE%E6%A0%87.png"></img>
				苏公网安备32058302004869
			</a>
			<span v-else-if="modal.theme > 4">{{ lang.copyright }} © {{ new Date().getFullYear() }}</span>
			<EditDiv tagName="span" v-model:content="l.title.value" v-if="l.title"
				@click="setModalType(l.title, modal.list, 'text')">
			</EditDiv>

			<template v-if="modal.theme < 5">
				<EditDiv tagName="span" v-model:content="l.subTitle.value" v-if="l.subTitle"
					@click="setModalType(l.subTitle, modal.list, 'text')">
				</EditDiv>
				<EditDiv tagName="span" v-model:content="l.text.value" v-if="l.text"
					@click="setModalType(l.text, modal.list, 'text')">
				</EditDiv>
				<span>{{ $store.state.proUrl }}</span>
			</template>
		</div>
	</div>
</template>


<script>
import { addSubscribes } from '@/api/web';
export default {
	name: "modalFooter",
	props: {
		data: {
			type: Object,
			default: () => {
				return {}
			}
		}
	},
	data() {
		return {
			modal: {
				class: '',
				type: {},
				style: {},
				...this.data
			},
			email: '',
			agree: false,
			navIndex: this.data.outer && this.data.outer.findIndex(i => i.class == 'nav') || 0,
			phoneRegex: /(?:[-+() ]*\d){10,13}/g,
			emailRegex: /(?:[a-z0-9+!#$%&'*+/=?^_"{|}~-]+(?:\.[a-z0-9!#$%&'*+/=?^_"{|}~-]+)*|"(?:[\x01-\x08\x0b\x0c\x0e-\x1f\x21\x23-\x5b\x5d-\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])*")@(?:(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?|\[(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?|[a-z0-9-]*[a-z0-9]:(?:[\x01-\x08\x0b\x0c\x0e-\x1f\x21-\x5a\x53-\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])+)\])/gi
		};
	},
	computed: {
		lang() {
			return this.$store.getters.lang.layout || {};
		},
		proSystem() {
			return this.$store.state.proSystem
		}
	},
	watch: {
		modal: {
			handler(val) {
				if (process.env.isManage) this.$emit("update:data", val);
			},
			immediate: true,
			deep: true
		}
	},
	mounted() {
		if (document.querySelector('#map')) this.$nextTick(() => {
			let js = document.createElement('script');
			js.type = 'text/javascript';
			js.src = `https://maps.googleapis.com/maps/api/js?key=AIzaSyBdKgeN4PiMcFRLbw6NzdOCzEixXQp9l4s&callback=initMap`;

			document.getElementsByTagName("head")[0].appendChild(js);
		})
	},
	methods: {
		setModalType(target, targetArray, clickType, event, other) {
			this.$setModal(this, target, targetArray, clickType, event, other)
		},
		subscribe() {
			if (this.modal.theme == 8 && !this.agree) return this.$toast.error(this.lang.agreeTip);
			// let re = /^[A-Za-z0-9-_\u4e00-\u9fa5]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/;
			// let result = re.exec(this.email);
			if (!/.+@.+\..+/.test(this.email)) return this.$toast.error(this.lang.emailTip);

			addSubscribes({ email: this.email }).then(res => this.$toast.success(res.message)).finally(() => this.email = "");
		},
	}
};
</script>


<style lang="scss" scoped>
footer {
	flex-wrap: wrap;
	justify-content: space-between;

	.modal-box {
		padding-top: 3.5vw;
		padding-bottom: 2.5vw;
	}

	>div {
		display: flex;
		grid-gap: 0.8em;
		flex-direction: column;
	}

	.logo-box {
		height: 3.5em;

		img {
			width: auto;
			height: 100%;
			max-width: 400px;
			object-fit: contain;
		}
	}

	.title {
		align-items: center;

		b:first-child {
			margin-right: 0.5em;
		}

		div {
			flex: 1;
		}
	}

	.reviews {
		grid-gap: 0.3em;
		flex-wrap: wrap;
		width: max-content;
		line-height: 1.5em;
		align-items: center;

		>span {
			line-height: 1em;
		}

		.star-box {
			font-size: 0.8em;
			background-clip: text;
			-webkit-background-clip: text;

			b {
				color: transparent;
				margin-right: 0.1vw;
			}
		}

		img {
			width: auto;
			height: 1.1em;
		}
	}

	.subscribe-box {
		background: white;
		color: $text-primary;
		border-radius: $radius-btn;
		justify-content: space-between;

		input {
			flex: 1;
			padding: 0 1em;
		}

		button {
			height: 100%;
			max-height: 100%;
			padding: 0 1.5em;

			&[primary] {
				height: 100%;
				min-width: auto;
				margin-right: -1px;
				border-top-left-radius: 0;
				border-bottom-left-radius: 0;
			}
		}
	}

	.subscribe .link-box {
		padding-left: 1.8em;
		position: relative;

		.agree-box {
			position: absolute;
			top: 0.1em;
			left: 0;
		}
	}

	.agree-box {
		width: 1.2em;
		height: 1.2em;
		text-align: center;
		border-radius: 3px;
		background: white;
		color: $color-primary;
	}

	.nav {
		.title .arrow {
			display: block;
		}

		&:not(.expand)>*:not(.title) {
			display: none;
		}
	}

	.link-box {
		display: grid;
		grid-gap: 0.8em;

		>div:not(.agree-box) b {
			min-width: 1.5em;
		}
	}

	.nav-border .link-box a {
		border-bottom: 1px solid;
	}

	.img-box {
		grid-gap: 0.7em;
		align-items: center;

		a:not([href]) {
			cursor: unset;
		}

		img {
			max-height: 100%;
		}
	}

	.contact-box {
		grid-gap: 0.8em 1.5em;
		flex-direction: column;

		>div b {
			min-width: 1.5em;
		}
	}
}

.modal-box.copyright {
	padding-top: 1em;
	text-align: center;
	padding-bottom: 1em;
	font-size: calc(1em - 2px);

	.right-zh {
		order: 1;
		grid-gap: 0.3em;
		align-items: center;

		img {
			width: auto;
			height: 1.125em;
		}
	}
}

.nav-hover-border .link-box [href]:hover {
	text-decoration: underline;
}

.text-inline .title div {
	display: inline;
}

[theme='8'] {
	.contact-box>div {
		align-items: center;

		&:first-child {
			order: 1;
		}
	}

	// .img-box a {
	// 	&:nth-child(-n + 6) {
	// 		margin: 5px 8px 1.5vmax 0;
	// 	}

	// 	&:nth-child(6) {
	// 		margin-right: calc(100% - 250px);
	// 	}

	// 	img {
	// 		width: auto;
	// 		object-fit: contain;
	// 	}
	// }

	// .subscribe .link-box a {
	// 	border-bottom: 1px solid;
	// }

	footer>div:nth-child(-n+2) b {
		margin-right: 0.4em;
	}

	footer>div:nth-child(n+5):not(.right) .link-box div::before {
		content: "\e668";
		margin-right: 0.3em;
		transform: scale(0.5);
		font-family: "modalicon";
	}

	// .right .link-box a {
	// 	border-bottom: 1px solid;
	// }
}


[theme='10'] {
	.link-box a:hover {
		color: $color-primary;
	}

	.img-box img {
		width: auto;
	}

	.copyright ::v-deep a:hover {
		border-bottom: 1px solid;
	}
}


[theme='11'] .link-box a:hover {
	color: $color-primary;
}

[theme='21'] {
	.link-box a:hover {
		color: #fff;
		font-weight: bold;
	}

	.contact-box>div {
		align-items: center;
		column-gap: 0.3em;

		b {
			font-size: 1.3em;
		}
	}
}





// // ipad mb
// @media screen and (max-width: $pad-width) {
// 	[theme='8'] {
// 		.img-box {
// 			max-width: 90% !important;
// 		}
// 	}
// }





// mb
@media screen and (max-width: $mb-width) {
	footer {
		.modal-box {
			padding: 2vw 3vw 2em;
		}

		>div {
			width: 100%;

			&:not(:first-child) {
				padding-top: 2em;
			}
		}

		.nav .link-box {
			grid-template-columns: 1fr 1fr;
		}
	}


	.modal-box.copyright {
		font-size: 1em;
	}


	.nav-border-bottom .link-box>div {
		padding-bottom: 0.8em;
		border-bottom: 1px solid #282828;
	}



	[theme='5'] .payment .img-box img {
		max-height: 3em;
	}

	[theme='6'] {
		.link-box a {
			border: none;
		}

		.subscribe .title,
		.contact ::v-deep br {
			display: none;
		}
	}

	[theme='9'] .link-box ::v-deep br {
		display: none;
	}
}
</style>
