<template>
  <div
    class="halfDetail"
    :class="{ isDialog: type === 'dialog' }"
    :style="modal.style"
    noDebounce
  >
    <v-card height="300" v-if="$store.getters.isManage">
      <v-row justify="center">
        <v-overlay :absolute="true" :value="true">
          <v-chip>半定制产品模板</v-chip>
        </v-overlay>
      </v-row>
    </v-card>
    <div class="modal-box half-container" v-else>
      <!--面包屑-->
      <Bread v-if="showBread" :breadList="breadList"></Bread>
      <Info
        :product-info="productInfo"
        :hideBack="hideBack"
        :showXin="!isCoins"
        v-if="!isMedals && isMobile && currentStep == 1"
        @goCollection="goCollection"
        @back="back"
      ></Info>
      <Info2
        :product-info="productInfo"
        :hideBack="hideBack"
        :showPriceTable="showPriceTable"
        v-if="isMedals && isMobile && currentStep == 1"
        @goCollection="goCollection"
        @back="back"
        @showPriceTableFn="showPriceTableFn"
      ></Info2>
      <half-design-custom-medals-printMethod-priceTable
        v-if="isMedals && isMobile && showPriceTable"
        :stepData="medalsPrintMethod"
      ></half-design-custom-medals-printMethod-priceTable>
      <div class="pdt" id="pdt">
        <div class="pdt-left" id="pdt-left" ref="pdtLeft">
          <div class="close-icon" @click="switchMask">
            <b></b><b class="icon-guanbi"></b>
          </div>
          <SwiperArea
            :showThreeD="showThreeD"
            :product-info="productInfo"
            :mobileCanvasWidth="mobileCanvasWidth"
            :editStatus="editStatus"
            :showDesignTool="!isMedals && !isCoins && !isBeltBuckles"
            :noShowBack="isMedals || isCoins || isMbNext"
            :isPens="isPens"
			:isLuggagetags="isLuggagetags"
            :imgList="imgList"
            :loadTempData="loadTempData"
            :firstImg="firstImg"
            :isMask="isMask"
            :isCoins="isCoins"
            :currentStep="currentStep"
            :isBig="isBig"
            :maskContent="maskContent"
            @toPrev="toPrev"
            @zoom="zoom"
            @beginEdit="beginEdit"
            @closeEdit="editStatus = false"
            @toggleThreeD="toggleThreeD"
          >
          </SwiperArea>
          <ProductDes
            v-if="!isMobile || isBeltBuckles || isPokerChips"
            :product-info="productInfo"
          ></ProductDes>
        </div>
        <div class="pdt-right">
          <Info
            :product-info="productInfo"
            :hideBack="hideBack"
            :showXin="!isCoins"
            v-if="!isMedals && !isMobile"
            @goCollection="goCollection"
            @back="back"
          ></Info>
          <Info2
            :product-info="productInfo"
            :hideBack="hideBack"
            v-if="isMedals && !isMobile"
            @goCollection="goCollection"
            @back="back"
          ></Info2>
          <div v-for="(step, index) in stepArr" :key="index" v-show="showStep(step)">
            <halfDesignStep
              ref="designStep"
              @hook:mounted="getDom"
              :stepData="step"
              :isMedals="Boolean(isMedals)"
              :isCoins="Boolean(isCoins)"
              :isSocks="Boolean(isSocks)"
              :isBeltBuckles="Boolean(isBeltBuckles)"
              :isPokerChips="Boolean(isPokerChips)"
              :isMbNext="isMbNext"
              :paramsArr="paramsArr"
              :needNextStep="needNextStep"
              :needNextStepObj="needNextStepObj"
              :errStep="errStep"
              @cancelNoAndNext="cancelNoAndNext"
              @selectStep="selectStep"
              @addTextBefore="addTextBefore"
              @checkParam="checkParams(paramsArr, false)"
              @newAddTextBefore="newAddTextBefore"
              @newAddTextBefore2="newAddTextBefore2"
              @changeTextPropertyBefore="changeTextPropertyBefore"
              @changeTextPropertyBefore2="changeTextPropertyBefore2"
              @addImgBefore="addImgBefore"
              @updatePrice="calculatePrices"
              @toNext="toNext"
              @replaceImg="replaceImg"
              @toPrev="toPrev"
              @newAddImg="newAddImg"
              @newAddImg2="newAddImg2"
              @newAddImg3="newAddImg3"
              @delImg="delImg"
              @delAllImg="delAllImg"
              @selectLanyardsTypeFn="selectLanyardsTypeFn"
              @selectRadioFn="selectRadioFn"
              @closeMask="switchMask"
              @nextStep="nextStep"
              @repeatImg="repeatImg"
            >
            </halfDesignStep>
          </div>
          <Subtotal
            v-if="showSubtotal()"
            :isStockPage="isStockPage"
            :productInfo="productInfo"
            :priceInfo="priceInfo"
            :noInquiry="!isInquiry || isCoins"
            :halfCateDTO="halfCateDTO"
            :isLuggagetags="isLuggagetags"
            :isCufflinks="false"
            :selectOne="selectOne"
            @addCart="addCart"
            @totalPricee="handleTotalPrice"
            @openInquiryBox="openInquiryBox"
          >
          </Subtotal>
          <ProductDes
            v-if="isMobile && !isBeltBuckles && !isPokerChips"
            :product-info="productInfo"
          ></ProductDes>
        </div>
      </div>
      <oneDialog2
        class="myOneDialog2"
        :oneDialog.sync="oneDialog2"
        :productSku="productInfo.productSku"
        @oneSelect="oneSelected"
      ></oneDialog2>
      <!-- 操作悬浮框 -->
      <div
        id="tool-option"
        ref="toolOption"
        v-show="!hideTool && (showTextTool || showImgTool)"
      >
        <div class="text-tool" v-show="showTextTool">
          <div class="text-tool-top">
            <v-select
              style="z-index: 5000"
              v-model="textProperty.fontFamily"
              :menu-props="{ bottom: true, offsetY: true }"
              solo
              flat
              outlined
              dense
              hide-details
              :items="fontsData"
              item-text="name"
              item-value="name"
              label="Font Family"
              @change="changeTextProperty($event, 'fontFamily')"
            >
              <template #item="{ item }">
                <span :style="{ fontFamily: item.name }">{{ item.name }}</span>
              </template>
            </v-select>
            <v-menu
              z-index="5000"
              :close-on-content-click="false"
              offset-y
              left
              min-width="300"
              ref="myMenu"
            >
              <template v-slot:activator="{ on, attrs }">
                <div class="color-picker" v-bind="attrs" v-on="on">
                  <div class="append">
                    <img
                      src="~/static/img/icon_color.png"
                      alt="color"
                      title="color"
                      style="width: 25px; height: 25px"
                    />
                  </div>
                </div>
              </template>
              <v-card class="color-picker-wrap" color="#ffffff">
                <div class="color-picker-title">Edit Colors</div>
                <div class="color-picker-list">
                  <div
                    class="color-item"
                    :class="{ active: item.code === textProperty.fill }"
                    v-for="item in colorList"
                    :key="item.id"
                    :style="{ backgroundColor: item.code }"
                    :title="item.pantone"
                    @click="changeTextProperty(item, 'fill')"
                  >
                    <v-icon color="#ffffff" small>mdi-check</v-icon>
                  </div>
                </div>
              </v-card>
            </v-menu>
          </div>
          <div class="text-tool-bottom">
            <v-text-field
              v-model="textProperty.text"
              solo
              flat
              outlined
              dense
              hide-details
              label="Add Text"
              @input="changeTextVal"
            ></v-text-field>
            <div
              class="font-bold"
              :class="{ active: textProperty.fontWeight === 'bold' }"
              @click="
                changeTextProperty(
                  textProperty.fontWeight === 'normal' || textProperty.fontWeight === 400
                    ? 'bold'
                    : 'normal',
                  'fontWeight'
                )
              "
            >
              B
            </div>
            <div
              class="font-style"
              :class="{ active: textProperty.fontStyle === 'italic' }"
              @click="
                changeTextProperty(
                  textProperty.fontStyle === 'normal' || !textProperty.fontStyle
                    ? 'italic'
                    : 'normal',
                  'fontStyle'
                )
              "
            >
              I
            </div>
          </div>
        </div>
        <div class="img-tool" v-show="showImgTool">
          <div class="group" @click="triggerUpload('change')">
            <div class="iconWrap">
              <v-icon size="40">mdi-cloud-upload</v-icon>
            </div>
            <span>Change Logo</span>
            <input
              type="file"
              accept=".jpg,.jpeg,.png,.svg,.gif,.bmp"
              ref="upload"
              @click.stop
              @change="uploadPic"
            />
          </div>
        </div>
        <div class="close">
          <v-btn fab x-small @click="hideTool = true">
            <v-icon>mdi-window-close</v-icon>
          </v-btn>
        </div>
      </div>
      <oneDialog
        :oneDialog.sync="oneDialog"
        :productSku="productInfo.productSku"
        @oneSelect="oneSelected"
      >
      </oneDialog>
      <EditLogo v-if="editLogoDialog" :editLogoDialog="editLogoDialog"></EditLogo>
      <inquiryBox
        :priceInfo="priceInfo"
        :productSku="productInfo.productSku"
        :inquiryBoxVisible.sync="inquiryBoxVisible"
        :productInfo="productInfo"
        :type="type"
        @addInquiry="addInquiry"
      >
      </inquiryBox>
      <PriceBar
        v-if="!isMobile && !$store.getters.isManage"
        :subShow="subShow"
        :priceInfo="priceInfo"
        :productInfo="productInfo"
        :isLuggagetags="isLuggagetags"
        :isCufflinks="false"
        :selectOne="selectOne"
        :discountPriceArr="printMethodPriceArr"
        @addCart="addCart"
        @openInquiryBox="openInquiryBox"
        :totalPriceFromSubtotal="totalPricee"
        :IncludingVAT="IncludingVAT"
        :countryName="countryName"
      >
      </PriceBar>
      <BaseDialog
        v-model="showNextStepDialog"
        class="nextStepDialog"
        :beforeClose="dialogClickSelf"
      >
        <div slot="closeIcon">
          <b class="icon-guanbi" style="height: 0; display: none"></b>
        </div>
      </BaseDialog>
      <BaseDialog
        v-model="showAddCartDialog"
        :model="false"
        :width="!isMobile ? '30%' : '90%'"
        :minWidth="!isMobile ? '576px' : '90%'"
        class="addCartDialog"
      >
        <div class="selectCartBox">
          <img
            src="https://static-oss.gs-souvenir.com/web/quoteManage/20250329/add_to_cart_20250329ETnXi2.png"
            alt=""
          />
          <div class="cardBtnBox" >
            <b class="iconfont icon-sp successAddIcon"></b>
            <div class="addCartSuceessText">{{ langSemiCustom.successfullyAdd }}</div>
            <div class="cartBtns" :class="{ luggagetags: isLuggagetags }">
              <div class="checkOutBtn" @click="goCheckout">
                {{ langSemiCustom.proceedToCheckout }}
              </div>
              <div class="goToCartBtn" @click="goCart">{{ langSemiCustom.goToCart }}</div>
            </div>
          </div>
          <b class="icon-guanbi addToCartCloseIcon" @click="cancelCartDialog"></b>
        </div>
        <div slot="closeIcon">
          <b class="icon-guanbi" style="height: 0; display: none"></b>
        </div>
      </BaseDialog>
    </div>
    <div class="back" @click="back" v-if="!hideBack">
      <b class="icon-back" style="color: rgb(203, 203, 203); font-size: 20px"></b>
      <span>Back</span>
    </div>
  </div>
</template>

<script>
import {
  checkImgSize,
  checkImgType,
  concurRequest,
  dataURLtoFile,
  debounce,
  deepClone,
  domScrollFn,
  isBase64,
  readFileAsBase64,
  scrollToViewCenter,
  scrollToViewTop,
  urlAddVersion,
  loadImage,
  generateUUID,
} from "@/utils/utils";
import {
  initAligningGuidelines,
  initCenteringGuidelines,
} from "@/assets/js/fabricCore/initAligningGuidelines.js";
import VideoPlayer from "@/components/HalfDesign/VideoPlayer.vue";
import {
  addCart,
  getSemiInquiry,
  addCollection,
  deleteConllectionByUserId,
  getPriceLayered,
  getProductInfoAndParamRelation,
  halfCalculate,
} from "@/api/web";
import { getUserCouponList } from "@/api/coupon";
import { initControlIcon } from "@/assets/js/icon";
import { uploadFile } from "@/utils/oss";
import freeTip from "~/components/Quote/freeTip";
import oneDialog from "@/components/HalfDesign/oneDialog/oneDialog.vue";
import oneDialog2 from "@/components/HalfDesign/oneDialog/oneDialog2.vue";
import canvas from "@/assets/js/halfDesign/canvas";
import EditLogo from "@/components/HalfDesign/EditLogo.vue";
import halfDesignStep from "@/components/HalfDesign/Step.vue";
import Bread from "@/components/modal/Half/Detail/Bread.vue";
import Info from "@/components/modal/Half/Detail/Info.vue";
import Info2 from "@/components/modal/Half/Detail/info2.vue";
import SwiperArea from "@/components/modal/Half/Detail/SwiperArea.vue";
import ProductDes from "@/components/modal/Half/Detail/ProductDes.vue";
import Subtotal from "@/components/modal/Half/Detail/Subtotal.vue";
import PriceBar from "@/components/modal/Half/Detail/PriceBar.vue";
import inquiryBox from "@/components/modal/Half/Detail/inquiryBox.vue";
import BaseDialog from "@/components/Quote/BaseDialog";

let fabric;
if (process.client) {
  fabric = require("fabric").fabric;
}

export default {
  name: "modalHalfDetail",
  props: {
    isStockPage: {
      type: [Number, String],
    },
    name: {
      type: String,
    },
    hideBack: {
      type: Boolean,
      default: false,
    },
  },
  components: {
    EditLogo,
    VideoPlayer,
    freeTip,
    oneDialog,
    oneDialog2,
    BaseDialog,
    halfDesignStep,
    Bread,
    Info,
    Info2,
    SwiperArea,
    ProductDes,
    Subtotal,
    PriceBar,
    inquiryBox,
  },
  provide() {
    return {
      textProperty: () => this.textProperty,
      clearImg: this.clearImg,
      cusUploadImg: () => this.uploadImg,
      getCommentObj: () => this.commentObj,
      getAreaUploadList: () => this.areaUploadList,
      getAreaIndex: () => this.areaIndex,
      getAreaList: () => this.areaList,
      getUnit: () => this.productInfo?.category?.unit,
      getProductInfo: () => this.productInfo,
      getPriceInfo: () => this.priceInfo,
      getQuotePriceParamsFn: () => this.getQuotePriceParams,
      getUnitPriceStep: () => this.unitPriceStep,
      getEmailLater: () => this.emailLater,
      getStep3Show: () => this.step3Show,
      canvas,
      fabric,
      getCustomPriceData: () => this.customPriceData,
      getCurrentPrintMethod: () => this.currentPrintMethod,
      getPrintStyleClass: () => this.printStyleClass,
      getPrintColorStyleClass: () => this.printColorStyleClass,
      getCustomProductId: () => this.productId,
      getCustomCategoryId: () => this.categoryId,
      getNeedJudgeSmallWeight: () => this.needJudgeSmallWeight,
    };
  },
  data() {
    return {
      countryName: "",
      IncludingVAT: "",
      totalPricee: 0,
      halfCateDTO: {},
      step3Show: false,
      emailLater: false,
      modal: {
        style: {},
        type: {},
        ...this.data,
      },
      otherLogo: "",
      otherText: [],
      orderDetailStep: 0,
      currentStep: 1,
      debounceCalculatePrice: null,
      isNoPrint: false,
      paramsArr: [],
      commentObj: {
        comments: "",
        uploadList: [],
      },
      productId: "",
      categoryId: "",
      recommendProduct: [],
      stepArr: [],
      fabricStage: "",
      subShow: false,
      productInfo: {
        category: {},
      },
      hideTool: true,
      editStatus: false,
      showThreeD: false,
      firstImg: "",
      imgList: [],
      selectedElement: "",
      textProperty: {},
      uploadImg: {
        url: "",
        name: "",
        type: "",
      },
      textArr: [
        {
          textVal: "",
        },
      ],
      fbDataObj: {},
      prevAreaName: "",
      loadTempData: false,
      priceInfo: {
        discount: 1,
      },
      unitPriceStep: "",
      priceArr: [],
      areaList: [],
      customPriceData: {
        multiBasicUnitPrice: 1,
      },
      teaDatas: {},
      selectSize: false,
      selectSizeId: 0,
      colorCode: "",
      discountPriceArr: [],
      isMask: false,
      maskContent: "",
      areaIndex: 0,
      printColorIndex: 0,
      colorIndex: -1,
      isAutoPlay: false,
      needCyclicQuery: false,
      noAddCart: false,
      oneDialog: false,
      oneDialog2: false,
      selectOne: "",
      hasOneDollarPens: false,
      newOneDollarPens: false,
      isLuggagetags: false,
      mobileWidth: 341,
      mobileHeight: 341,
      isBig: false,
      canShow: false,
      skipSizeVerify: false,
      currentPrintMethod: {},
      time: null,
      useMobileStyle: false,
      printStyleClass: null,
      printColorStyleClass: null,
      isBlankPrintMethod: false,
      inquiryBoxVisible: false,
      needOpenInquiryBoxVisible: false,
      showNextStepDialog: false,
      showAddCartDialog: false,
      nowMaskId: 0,
      medalsPrintMethod: null,
      canvasPosition: null,
      showPriceTable: false,
      selectLanyardsType: -1,
      selectRidio: -1,
      LanyardColorFile: false,
      needNextStep: [],
      needNextStepObj: {},
      copyNeedNextStepObj: {},
      needUpload: true,
      artworkErrId: 0,
      LanyardColorErrId: 0,
      noNextStepAndNext: {
        parentId: 0,
        id: 0,
      },
      errStep: [],
      headerHeight: 0,
      pdtSub: 0,
      LanyardColorImg: "",
      nowRibbonColorId: 0,
      noRibbonBackImg: "",
      areaColorRibbonImgs: [],
      areaUploadList: {},
      nowAreaName: "",
      nowScrollY: 0,
      smallWeightBoundaryLine: 0,
      needJudgeSmallWeight: false,
      needAddBorderId: [],
      areaColorIndexObj: {},
      nowReplaceImgId: -99,
      outerRingImg: "",
      tempCart: null,
    };
  },
  computed: {
    type() {
      // 详情页面模式
      return this.name ? "dialog" : "";
    },
    showBread() {
      return (
        this.type != "dialog" && (this.productInfo.isDevise != 1 || this.theme == "11")
      );
    },
    breadList() {
      return [
        {
          name: this.langSemiCustom.allProducts,
          link:
            this.isStockPage == "1"
              ? this.halfCateDTO.shopRouting
              : this.halfCateDTO.customRouting,
        },
        {
          name: this.productInfo.name,
        },
      ];
    },
    editLogoDialog() {
      return this.$store.state.halfDesign.editLogoDialog;
    },
    theme() {
      return this.$store.state.proTheme;
    },
    langSemiCustom() {
      return this.$store.getters.lang?.semiCustom;
    },
    proSystem() {
      return this.$store.state.proSystem;
    },
    proId() {
      return this.$store.state.proId;
    },
    fontsData() {
      return require("@/assets/json/fontList.json");
    },
    routingName() {
      if (this.type === "dialog") {
        return this.name;
      } else {
        return this.$store.state.pagePath;
      }
    },
    groupObj() {
      if (!this.fabricStage) {
        return;
      }
      return this.fabricStage.getObjects().filter((o) => o.id === "editArea")[0];
    },
    groupObj2() {
      if (!this.fabricStage) {
        return;
      }
      return this.fabricStage
        .getObjects()
        .filter((o) => o.id === "editArea" || o.id === "BezierCurvePath");
    },
    langCart() {
      return this.$store.getters.lang?.cart;
    },
    userId() {
      return this.$store.state.userInfo?.id;
    },
    userUUID() {
      return this.$store.state.userUUID;
    },
    isLogin() {
      return this.$store.getters.isLogin;
    },
    isMobile() {
      return this.$store.state.device === "mb";
    },
    proType() {
      return this.$store.state.proType;
    },
    showTextTool() {
      let selectedElement = this.selectedElement;
      return selectedElement && selectedElement.type === "i-text";
    },
    showImgTool() {
      let selectedElement = this.selectedElement;
      return selectedElement && selectedElement.type === "image";
    },
    colorList() {
      return this.$store.state.colorList;
    },

    isColorStock() {
      return this.halfCateDTO.isColorStock == 1;
    },
    mobileCanvasWidth() {
      if (!this.isMobile || !this.useMobileStyle) return {};
      return {
        width: this.mobileWidth + "px",
        height: this.mobileHeight + "px",
      };
    },
    topMobileCanvasHeight() {
      if (this.editStatus) {
        return this.mobileHeight;
      } else {
        let height = this.mobileHeight;
        let pdt = document.querySelector("#pdt-left");
        if (pdt) height = pdt.getBoundingClientRect().height;
        return height;
      }
    },
    modeType() {
      return this.$store.state.halfDesign.modeType;
    },
    isMedals() {
      return this.halfCateDTO?.categoryType == 1;
    },
    isCoins() {
      return this.halfCateDTO?.categoryType == 2;
    },
    country() {
      return this.$store.state.language.countryCode;
    },
    showSmallWeightDelivery() {
      return this.$store.state.halfDesign.showSmallWeightDelivery;
    },
    isOrnaments() {
      return this.halfCateDTO?.categoryType == 3;
    },
    isPens() {
      return this.halfCateDTO?.categoryType == 4;
    },
    isSocks() {
      return this.halfCateDTO?.categoryType == 5;
    },
    isBeltBuckles() {
      return this.halfCateDTO?.categoryType == 6;
    },
    isPokerChips() {
      return this.halfCateDTO?.categoryType == 7;
    },
    isCufflinks() {
      return this.halfCateDTO?.categoryType == 8;
    },
    textArr1() {
      return this.$store.state.halfDesign.textArr;
    },
    isInquiry() {
      return this.halfCateDTO?.isInquiry == 1;
    },
    isMbNext() {
      return this.halfCateDTO?.isMbNext == 1;
    },
    isFd() {
      return this.proId == 9;
    },
    enableTurnaroundTimeCheck() {
      return this.$store.state.enableTurnaroundTimeCheck;
    },
    areaIsHideStep() {
      return this.$store.state.halfDesign.areaIsHideStep;
    },
    canShowPrintColorPrice() {
      return this.$store.state.halfDesign.canShowPrintColorPrice;
    },
	printMethodPriceArr(){
		if(this.currentPrintMethod?.markupIncreasePrice){
			return JSON.parse(this.currentPrintMethod?.markupIncreasePrice)||[]
		}
		return []
	},
  },

  methods: {
    back() {
      this.$emit(
        "back",
        this.isStockPage == "1"
          ? this.halfCateDTO.shopRouting
          : this.halfCateDTO.customRouting
      );
    },
    handleTotalPrice(totalPrice, IncludingVAT, countryName) {
      this.totalPricee = totalPrice;
      this.IncludingVAT = IncludingVAT;
      this.countryName = countryName;
    },
    getDom() {
      if (this.time) {
        clearTimeout(this.time);
      }
      this.time = setTimeout(async () => {
        // if (this.$route.query.fromCheckout) {
        //   try {
        //     let detailData = window.localStorage.getItem("tempDetailData");
        //     if (detailData) {
        //       let detailDataObj = JSON.parse(detailData);
        //       detailDataObj.quoteParam.finaData.forEach((item, index) => {});
        //     }
        //   } catch (error) {}
        // }
		if (this.isMedals) {
        //medals 只默认选中打印方式，方位，email,artwork以及size(size 用于设置库存)
          let size = this.findParams(this.stepArr, "attributeFlag", "size");
          //size步骤在打印方式中选中
          this.$Bus.$emit("selectDefaultPrintStep", size);
          this.$Bus.$emit("selectDefaultArtworkStep");
          this.$Bus.$emit("selectDefaultEmailStep");
          this.$Bus.$emit("selectDefaultAreaStep", true, 0);
          return;
        } else {
          //参数默认选中第一个
          let size = this.findParams(this.stepArr, "attributeFlag", "size");
          this.$Bus.$emit("setSizeParam", size);
          this.$Bus.$emit("selectDefaultPrintStep", size);
          this.$Bus.$emit("selectDefaultArtworkStep");
          this.$Bus.$emit("selectDefaultColorStep");
          this.$Bus.$emit("selectDefaultInkColorStep");
          this.$Bus.$emit("selectDefaultEmailStep");
          this.$Bus.$emit("selectDefaultPrintColorStep");
          this.$Bus.$emit("selectDefaultDiscountStep");
          this.$Bus.$emit("selectDefaultAreaStep", true, 0);
          this.$Bus.$emit("selectDefaultOtherStep");
		  this.$Bus.$emit("selectDefaultBeltBucklesStep");
          if (this.modeType == 1 || this.modeType == 2) {
            if (this.printStyleClass)
              this.$Bus.$emit(this.printStyleClass, { noShowTitle: true });
            if (this.printColorStyleClass)
              this.$Bus.$emit(this.printColorStyleClass, { noShowTitle: true });
          }
        }
        //获取用户优惠券
        if (this.isLogin) {
          await this.getUserCoupon();
        }
      }, 1000);
    },
    showStep(step) {
      if (this.theme == "11" && this.isStockPage) {
        return !step.noShow;
      }
      if (this.isMedals || this.isCoins || this.isMbNext) {
        //medals 详情
        return !step.noShow;
      }
      return (
        (!step.noShow && !this.isMobile) ||
        (this.isMobile && !step.noShow && this.currentStep == step.sortNum)
      );
    },
    showSubtotal() {
      if (
        (this.theme == "11" && this.isStockPage) ||
        this.isMedals ||
        this.isCoins ||
        this.isMbNext
      ) {
        return true;
      }
      return (
        !this.isMobile || (this.isMobile && this.currentStep == this.orderDetailStep - 1)
      );
    },
    calculatePrices(teaData) {
      if (this.needJudgeSmallWeight) {
        this.setSmallWeightFn();
      }
      this.$Bus.$emit("calculateWeightPrice");
      debounce(this.calculatePrice, 300)(teaData);
    },
    triggerUpload(type) {
      this.uploadImg.type = type;
      this.$refs.upload.click();
    },
    uploadPic(event) {
      let file = event.target.files[0];
      if (!checkImgType(file.name)) {
        this.$toast.error("File type error");
        this.$refs.upload.value = "";
        return;
      }
      if (!checkImgSize(file.size)) {
        this.$toast.error("File size cannot exceed 80m.");
        this.$refs.upload.value = "";
        return;
      }
      //皮带扣操作菜单换logo
      if (this.isBeltBuckles || this.isPokerChips) {
        this.uploadPic2(file);
        return;
      }
      readFileAsBase64(file).then(async (res) => {
        this.uploadImg.url = res;
        this.uploadImg.name = file.name;
        this.$refs.upload.value = "";
        await canvas.replaceImg(res, canvas.c.getActiveObject(), {
          original_filename: file.name,
          secure_url: res,
          size: (file.size / 1024).toFixed(1),
        });
        this.$store.commit(
          "halfDesign/updateImgItem",
          canvas.getImageProperty(canvas.c.getActiveObject())
        );
      });
    },
    uploadPic2(file) {
      this.loadTempData = true;
      uploadFile(file)
        .then(async (res) => {
          let upDataId = generateUUID();
          await canvas.replaceImg(res, canvas.c.getActiveObject(), {
            original_filename: file?.name || "logo",
            secure_url: res,
            size: (file?.size / 1024).toFixed(1),
            id: upDataId,
          });
          let currentArea = this.areaUploadList[this.nowAreaName];
          currentArea.clickImgId = currentArea.clickImgId.filter(
            (item) => item.id != this.nowReplaceImgId
          );
          if (this.isPokerChips) {
            currentArea.clickImgId = [];
            currentArea.files = [];
          }
          currentArea.clickImgId.push({
            id: upDataId,
            iconUrl: res,
            type: "upload",
          });
          currentArea.files = currentArea.files.filter(
            (item) => item.id != this.nowReplaceImgId
          );
          currentArea.files.push({
            id: upDataId,
            original_filename: file.name,
            secure_url: res,
            iconUrl: res,
            size: (file.size / 1024).toFixed(1),
          });
          this.nowReplaceImgId = upDataId;
          this.$refs.upload.value = "";
          this.$forceUpdate();
        })
        .finally(() => {
          this.loadTempData = false;
          this.$refs.upload.value = "";
        });
    },
    //删除canvas
    clearCanvas() {
      this.$store.commit("halfDesign/setUploadImgList", []);
      this.$store.commit("halfDesign/resetTextArr");
      canvas.c.clear();
      canvas.c.renderAll();
    },
    findParams(list, key, val) {
      for (let i = 0; i < list.length; i++) {
        let item = list[i];
        if (item.attributeCategoryList && item.attributeCategoryList.length > 0) {
          for (let j = 0; j < item.attributeCategoryList.length; j++) {
            let item2 = item.attributeCategoryList[j];
            if (item2[key] == val) {
              return item2;
            }
            if (item2.productParamList && item2.productParamList.length > 0) {
              for (let k = 0; k < item2.productParamList.length; k++) {
                let item3 = item2.productParamList[k];
                if (item3[key] == val) {
                  return item3;
                }
              }
            }
          }
        }
      }
    },
    setStockByColorOrSize(item, type = "color") {
      if (!item) {
        let findItem = this.findParams(this.stepArr, "attributeFlag", type);
        if (!findItem) {
          return;
        }
        item = findItem.productParamList[0];
      }
      if (!item) {
        return;
      }
      let stock = item.productStock;
      if (!stock || !stock.length) {
        return;
      }
      stock.forEach((item) => {
        let findItem = this.findParams(this.stepArr, "id", item.sizeParamId);
        if (findItem) {
          findItem.stock = item.stock;
          findItem.stockParamId = item.id;
        }
      });
    },
    setAreaByColor(colorItem) {
      if (!colorItem) {
        let findItem = this.findParams(this.stepArr, "attributeFlag", "color");
        if (!findItem) {
          return;
        }
        colorItem = findItem.productParamList[0];
        this.colorIndex = 0;
      }
      this.colorCode = colorItem.colorCode;

      //位置信息
      let area = JSON.parse(colorItem.imgJson);
      if (!area || !area.length) {
        return;
      }
      //方位
      let areaParams = this.findParams(this.stepArr, "attributeFlag", "area");
      if (areaParams && areaParams.productParamList.length > 0) {
        area.forEach((item) => {
          let find = areaParams.productParamList.find(
            (citem) => citem.attributeValueId === item.attributeValueId
          );
          let findCopy = JSON.parse(JSON.stringify(find));
          if (find) {
            findCopy.imgDetail = item.url;
            findCopy.showEdit = item.showEdit;
            findCopy.alt = item.alt;
            findCopy.bottle = item.bottle;
            findCopy.top = item.top;
            findCopy.left = item.left;
            findCopy.right = item.right;
            findCopy.circle = item.circle;
			findCopy.selectedStyle = item.selectedStyle;
            findCopy.curves =
              (item.curves &&
                item.curves?.map((citem) => {
                  return { scaleFactor: item.scaleFactor, ...citem };
                })) ||
              [];
            findCopy.scaleFactor = item.scaleFactor || 1;
            if (this.isMedals) {
              findCopy.ribbonImg = colorItem.ribbonImg;
            }
            Object.assign(item, findCopy);
          }
        });
        areaParams.productParamList = area;

        if (this.isCoins || this.isBeltBuckles || this.isPokerChips) {
          areaParams.productParamList.forEach((area) => {
            let areaKey = area.valueName;
            if (!this.areaUploadList[areaKey]) {
              this.$set(this.areaUploadList, areaKey, {
                clickImgId: [
                  {
                    id: -1,
                    iconUrl: "",
                  },
                ],
                files: [],
                textArr: [],
                areaCanvas: null,
              });
            }
            if (Array.isArray(area.curves) || Array.isArray(area.roundText)) {
              let defaultTextArr = area.curves || [];
              if (Array.isArray(area.roundText)) {
                defaultTextArr = area.roundText.concat(defaultTextArr);
              }
              if (!this.areaUploadList[areaKey].textArr.length) {
                defaultTextArr.map((citem) => {
                  let areaData = {
                    text: "",
                    fontFamily: "Times New Roman",
                    fontWeight: "",
                    fontStyle: "",
                    fontSize: "",
                    fill: "",
                    id: generateUUID(),
                  };
                  areaData.points = citem.points;
                  areaData.scaleFactor = area.scaleFactor;
                  areaData.fill = citem.textColor ? citem.textColor : "#fff";
                  areaData.defaultText = citem.defaultText;
                  areaData.pathType = citem.type;
                  let allAreaData = areaData;
                  if (citem.textData) {
                    allAreaData = Object.assign(areaData, citem.textData);
                  }
                  allAreaData.opacity = allAreaData.opacity ? allAreaData.opacity : 1;
                  this.areaUploadList[areaKey].textArr.push(allAreaData);
                });
              }
            }
          });
        }
        this.areaList = area;
      }
    },
    //获取画布的截图
    getPic(canvas, pixe = 2) {
      return canvas.toDataURL({
        multiplier: pixe,
      });
    },
    //获取带虚线框的截图
    getPic2(canvas, pixe = 2) {
      try {
        this.groupObj.visible = true;
        let url = canvas.toDataURL({
          multiplier: pixe,
        });
        this.groupObj.visible = false;
        return url;
      } catch (error) {
        return "";
      }
    },
    //获取多方位带虚线框的截图
    getPic3(canvas, pixe = 2) {
      try {
        this.groupObj2.forEach((item) => (item.visible = true));
        let url = canvas.toDataURL({
          multiplier: pixe,
        });
        this.groupObj2.forEach((item) => (item.visible = false));
        return url;
      } catch (error) {
        return "";
      }
    },
    async selectStep(item) {
      let { type, data, parent, id, copyId, colorIndex } = item,
        params;
      console.log(type, "type");
      //item.cancel 默认是undefined
      let cancel = item.cancel ? false : true;
      let nowId = copyId ? copyId : id;
      if (cancel) this.toggleTips(nowId, 0);
      let findParamsIndex = this.paramsArr.findIndex((item) => {
        return item.id === id;
      });
      if (findParamsIndex < 0) {
        return;
      }
      params = this.paramsArr[findParamsIndex];
      params.val = data;
      params.copyId = copyId;
      params.parent = parent;
      //将Back 里面有isRemark 需要next Step
      if (this.isMedals) {
        if (data?.isRemark) {
          if (!this.needNextStep.includes(+params.parentId)) {
            this.needNextStep.push(+params.parentId);
            //Back 里面开启isRemark 需要next Step 需要判断是的有备注或者上传文件
            this.noNextStepAndNext = {
              parentId: +params.parentId,
              errId: id,
              id: +data.id,
            };
            this.needNextStepObj[params.parentId] = [id];
          }
        } else {
          if (params.parentId == this.noNextStepAndNext?.parentId) {
            this.needNextStep = this.needNextStep.filter(
              (item) => item != this.noNextStepAndNext.parentId
            );
            this.needNextStepObj[this.noNextStepAndNext.parentId] = [];
          }
        }
      }
      switch (type) {
        case "color":
          this.colorIndex = colorIndex;
          if (!this.isColorStock) this.setStockByColorOrSize(data);
          if (this.isColorStock) this.setStockByColorOrSize(data, "size");
          this.setAreaByColor(data);
          this.$nextTick(() => {
            if (item.firstSelect) {
            }
            // 切换颜色，清空保存的设计,保留画布内容
            this.fbDataObj = {};
            if (!item.firstSelect) {
              this.$Bus.$emit("updateQty");
              this.firstImg = "";
              this.$Bus.$emit("clearArea", item.firstSelect, this.areaIndex);
              this.$Bus.$emit("printColor", item.firstSelect, this.printColorIndex);
            }
          });
          break;
        case "printMethod":
          //切换打印模式，如果有选择产品数量应该更新产品价格
          let sizeParam = this.getSizeParam();
          if (sizeParam.sizeParam.length != 0) {
            let quotePriceParams = this.getQuotePriceParams(
              sizeParam.sizeParam[0]["quantity"]
            );
            if (!quotePriceParams.unitPriceId) {
              break;
            }
            halfCalculate(quotePriceParams).then((res) => {
              this.priceInfo = res.data;
            });
          }
          this.currentPrintMethod = data;
          //将当前选中的打印方式数据传递出去
          this.$Bus.$emit("currentPrintMethod", data);
          //记录打印方式
          this.isBlankPrintMethod = !!data.isBlank;
          this.$store.commit("halfDesign/setPrintColorPriceShow", !!data.isMarkup);
          if (!this.isBlankPrintMethod) {
            // 非白板印刷方式--打印颜色是否加价：0.否  1.是
            // 控制打印颜色小标签排序
            this.stepSub(!!data.isMarkup);
          }
          if (this.isMobile && !item.firstSelect && this.productInfo.isLevitate) {
            let pdtLeft = this.$refs.pdtLeft;
            if (pdtLeft) {
              if (this.isBlankPrintMethod) {
                pdtLeft.classList.remove("isSticky");
              } else {
                pdtLeft.classList.add("isSticky");
              }
            }
          }

          //如果选择非压印模式，去除掉设计步骤
          let findDesignIndex = this.stepArr.findIndex((item) => item.isDevise === 1);
          if (data.isBlank === 1) {
            if (findDesignIndex >= 0 && !this.stepArr[findDesignIndex].noShow) {
              let designStep = this.stepArr[findDesignIndex];
              designStep.noShow = true;
              //排序在设计步骤后的减一
              this.stepArr.map((item, index) => {
                if (index > findDesignIndex) {
                  item.sortNum -= 1;
                }
              });
              this.orderDetailStep -= 1;
              this.$store.commit("halfDesign/setLastStep", this.orderDetailStep - 1);
              //禁止编辑画布
              this.isNoPrint = true;
              //清空画布
              this.clearCanvas();
              this.editStatus = false;
            }
          } else {
            if (findDesignIndex >= 0 && this.stepArr[findDesignIndex].noShow) {
              let designStep = this.stepArr[findDesignIndex];
              designStep.noShow = false;
              //排序在设计步骤后的加一
              this.stepArr.map((item, index) => {
                if (index > findDesignIndex) {
                  item.sortNum += 1;
                }
              });
              this.orderDetailStep += 1;
              this.$store.commit("halfDesign/setLastStep", this.orderDetailStep - 1);
              //允许编辑画布
              this.isNoPrint = false;
            }
          }
          this.$Bus.$emit("oneColor", data.isOpen);
          if (!item.firstSelect) {
            //白板模式不进入画板 有打印方式图展示这个
            if (data.isBlank == 1) {
              if (!data.img || !Array.isArray(JSON.parse(data.img))) {
                break;
              }
              this.editStatus = true;
              this.firstImg = JSON.parse(data.img)[0];
              break;
            }
            this.firstImg = "";
            if (this.colorIndex > -1) {
              this.$Bus.$emit("printColor", item.firstSelect, this.printColorIndex);
              this.$Bus.$emit(
                "clearArea",
                item.firstSelect,
                this.areaIndex,
                "printMethod"
              );
              break;
            }
            if (!data.img || !Array.isArray(JSON.parse(data.img))) {
              break;
            }
            this.editStatus = true;
            this.firstImg = JSON.parse(data.img)[0];
          } else {
            this.$Bus.$emit(
              "selectDefaultSizeStep",
              this.productInfo.lowestPurchaseQuantity
            );
            if (!data.img || !Array.isArray(JSON.parse(data.img))) {
              break;
            }
            this.editStatus = true;
            // 如果有设置打印方式封面图展示这个
            this.firstImg = JSON.parse(data.img)[0];
            //不设置画布大小
            this.$nextTick(() => (this.useMobileStyle = false));
          }

          this.$forceUpdate();
          break;
        case "area":
          this.nowAreaName = data.valueName;
          let area = this.findParams(this.stepArr, "attributeFlag", "area");
          this.areaIndex = area.productParamList.findIndex((item) => {
            return item.id == data.id;
          });
          this.areaColorIndexObj[data.valueName] = this.colorIndex;
          //记录当前方位是否应该隐藏步骤
          this.$store.commit("halfDesign/setAreaHideStep", data.isHideStep);
          if (data.isMask && !item.firstSelect) {
            this.isMask = true;
            this.maskContent = data.content;
          } else {
            this.isMask = false;
            this.maskContent = "";
          }
          if (!item.firstSelect) {
            this.firstImg = "";
            this.$store.commit("halfDesign/setCanClickBtn", false);
          }

          //保存上一个方位的模板数据
          this.fbDataObj[this.prevAreaName || data.valueName] = canvas.getJson();
          // 皮带扣存当前图片信息
          if (this.isBeltBuckles || this.isPokerChips) {
            let canvasImgData =
              this.fabricStage.getObjects().filter((item) => item.type == "image") || [];
            this.areaUploadList[this.prevAreaName || data.valueName].clickImgId.forEach(
              (file) => {
                let findImg = canvasImgData.find((item) => item.id == file.id);
                if (findImg) {
                  file.imgProperty = {
                    left: findImg.left,
                    top: findImg.top,
                    scaleX: findImg.scaleX,
                    scaleY: findImg.scaleY,
                    angle: findImg.angle,
                    opacity: findImg.opacity,
                    zoomX: findImg.zoomX,
                    zoomY: findImg.zoomY,
                  };
                }
              }
            );
          }
          //清空画布
          this.clearCanvas();
          if (!this.isCoins && !this.isBeltBuckles && !this.isPokerChips) {
            //加载之前保存的方位数据,并更新元素裁剪区域
            if (this.fbDataObj[data.valueName]) {
              this.loadTempData = true;
              this.fabricStage.loadFromJSON(this.fbDataObj[data.valueName], () => {
                //同步画布中的文字和图片到设计步骤
                this.$store.commit("halfDesign/syncCanvasTextAndImg");
                let img = this.getImg();
                if (!img) {
                } else {
                  this.uploadImg = {
                    type: "",
                    url: img.getSrc(),
                    name: "",
                  };
                }
              });
            } else {
              //同步画布中的文字和图片到设计步骤
              this.$store.commit("halfDesign/syncCanvasTextAndImg");
            }
          } else {
            this.loadTempData = true;
          }
          setTimeout(() => {
            this.loadTempData = false;
            this.$store.commit("halfDesign/setCanClickBtn", true);
          }, 1500);
          //保留当前背景图，在选择织带的时候合并
          this.noRibbonBackImg = data.imgDetail;
          let newUrl = data.imgDetail;
          //加载背景图
          if (this.isMedals) {
            this.areaColorRibbonImgs =
              data.ribbonImg &&
              typeof data.ribbonImg === "string" &&
              data.ribbonImg !== "null"
                ? JSON.parse(data.ribbonImg)
                : [];
            if (this.selectLanyardsType == 1 && this.LanyardColorImg) {
              newUrl = await this.mergeBackImg(data.imgDetail, this.LanyardColorImg);
            }
          } else if (this.isPokerChips) {
            if (this.outerRingImg) {
              newUrl = await this.mergeBackImg(this.outerRingImg, newUrl);
            }
          }

          await canvas.setCanvasBg(newUrl, {
            isClip: this.productInfo.isClip,
            imgWidth: this.productInfo.imgWidth,
            imgHeight: this.productInfo.imgHeight,
          });
          //保留画布位置信息
          this.canvasPosition = data;
          //加载边框---绘制文字曲线
          let type = "default";
          if (this.isBeltBuckles || this.isPokerChips) type = "buckles";
          if (this.isCoins) type = "coins";
          await canvas.setEditArea(
            data,
            {
              isClip: this.productInfo.isClip,
              imgWidth: this.productInfo.imgWidth,
              imgHeight: this.productInfo.imgHeight,
            },
            type
          );
          //更新画布元素裁剪路径
          let elArr = this.fabricStage.getObjects().filter((item) => {
            return item.type === "i-text" || item.type === "image";
          });
          elArr.forEach((item) => {
            //medals 不保留正方形路径 筹码不保留正方形路径
            if (!this.isMedals) item.clipPath = canvas.clipPath;
            if (item.type === "i-text") {
              item.on("editing:exited", () => {
                let textProperty = this.getTextProperty(item);
                this.$store.commit("halfDesign/updateTextItem", textProperty);
              });
            }
          });

          this.firstImg = "";
          this.editStatus = true;
          if (item.firstSelect) {
            this.editStatus = false;
          }
          //coins 情况下 非白板印刷方式需要再次加载选择的图片---假定切换白板
        //   if (
        //     !item.firstSelect &&
        //     !this.isBlankPrintMethod &&
        //     item.stepType == "printMethod" &&
        //     this.isCoins
        //   ) {
        //     if (!Object.keys(this.areaUploadList).length) return;
        //     console.log("printMethod and area", "312312312");
        //     let areaData = this.areaUploadList[data.valueName];
        //     if (!areaData) return;
        //     //图片
        //     if (areaData.files && areaData.files.length > 0) {
        //       let file = this.areaUploadList[data.valueName]?.files[0];
        //       setTimeout(() => {
        //         this.newAddImg3({
        //           imgItem: file,
        //         });
        //       }, 1200);
        //     }
        //     //文字
        //     if (areaData.textArr && areaData.textArr.length > 0) {
        //       setTimeout(() => {
        //         areaData.textArr.forEach((item) => {
        //           this.newAddTextBefore2({
        //             text: item.text.trim(),
        //             textItem: item,
        //           });
        //         });
        //       }, 1200);
        //     }
        //   }
          //给当前选中的方位赋值给DesignText ---- 默认选中
          this.$Bus.$emit("currentArea", data, item.firstSelect, this.colorIndex);
          this.prevAreaName = data.valueName;
          //存入方位canvas url
          if (this.isCoins || this.isBeltBuckles || this.isPokerChips) {
            this.areaUploadList[data.valueName].areaCanvas = this.getPic3(
              this.fabricStage
            );
          }
          break;
        case "email":
          let isBlank = data.isBlank;
          this.emailLater = !!isBlank;
          this.needUpload = !this.emailLater;
          if (this.emailLater) {
            if (this.artworkErrId) {
              this.toggleTips(this.artworkErrId, 0);
              this.artworkErrId = 0;
            }
            //不处理之前的逻辑
            if (this.isMobile && !this.isMedals) {
              domScrollFn(`#mobileNextBtn${this.currentStep}`, "end", 20);
            }
            // this.editStatus = false;
            //清空画布和多色备注
            this.$store.commit("halfDesign/updateColorRemark");
            // this.clearCanvas();
            break;
          }
          this.step3Show = true;
          if (this.colorIndex == -1) {
            if (!this.isMedals) this.$Bus.$emit("selectDefaultColorStep");
          }
          // if(!item.firstSelect)  this.$Bus.$emit("clearArea", item.firstSelect, this.areaIndex);
          this.$Bus.$emit("printColor", item.firstSelect, this.printColorIndex);
          break;
        case "printColor":
          let printColor = this.findParams(this.stepArr, "attributeFlag", "printColor");
          this.printColorIndex = printColor.productParamList.findIndex((item) => {
            return item.id == data.id;
          });
          if (this.emailLater) this.$store.commit("halfDesign/setMoreColor", false);
          break;
        case "size":
          if (this.isColorStock) this.setStockByColorOrSize(data, "size");
          this.selectSize = true;
          this.selectSizeId = data.id;
          this.$Bus.$emit("uploadQty", data);
          break;
        case "LanyardType":
          if (data.lanyardsType == 1) {
            //判断是否有选择织带颜色，有的话
            await this.getRibbonColorImg(this.nowRibbonColorId);
          }
          if (data.lanyardsType == 2 || data.lanyardsType == 3) {
            this.setAreaData(this.noRibbonBackImg);
          }
          break;
        case "LanyardColor":
          if (this.selectLanyardsType == 1) {
            //需要合并织带和奖章
            this.nowRibbonColorId = data.attributeValueId;
            await this.getRibbonColorImg(this.nowRibbonColorId);
          }
          break;
        case "quantity":
          if (!item.firstSelect) this.$Bus.$emit("quantityUpdateQty", data);
          return;
        case "outerRing":
          this.outerRingImg = data.sizeImg;
          if (this.noRibbonBackImg) {
            let allUrl = await this.mergeBackImg(this.outerRingImg, this.noRibbonBackImg);
            await canvas.setCanvasBg(allUrl, {
              isClip: this.productInfo.isClip,
              imgWidth: this.productInfo.imgWidth,
              imgHeight: this.productInfo.imgHeight,
            });
          }
      }
      //判断是否为小重量交期
      if (this.needJudgeSmallWeight) {
		//计算交期重量价格
        this.$Bus.$emit("calculateWeightPrice");
		if((type == "size" || (type == "color" && this.isColorStock)))  this.setSmallWeightFn();
      }
      if (this.productInfo.isDevise != 1) {
        this.debounceCalculatePrice();
      }
      if (this.isMedals && type !== "area") {
        //选中颜色会选择方位，不执行下面逻辑
        let needNextStep = this.needNextStep.includes(params.parentId);
        this.checkParams(this.paramsArr, false);
        if (this.errStep.length > 0 && !item.firstSelect) {
          if (needNextStep) {
            //需要点击下一步的 自己打开mask
            this.switchMask(+params.parentId, true);
            this.nowMaskId = +params.parentId;
            this.setMaskDialog();
          } else {
            //打开最新一个参数不全的mask
            this.nextStep();
          }
        } else {
          if (!item.firstSelect && !needNextStep) {
            this.nextEnd();
          }
        }
      }
    },
    setSmallWeightFn() {
      let sizeData = this.findParams(this.stepArr, "attributeFlag", "size");
      let colorData = this.findParams(this.stepArr, "attributeFlag", "color");
      let newObj = {};
      if (this.isColorStock) {
        newObj = JSON.parse(JSON.stringify(colorData));
      } else {
        newObj = JSON.parse(JSON.stringify(sizeData));
      }
      //判断尺寸重量是否为小重量交期，多个尺寸取最重的重量
      let sizeWeight = 0;
      let allNum = 0;
      if (newObj.productParamList && newObj.productParamList.length > 0) {
        allNum = newObj.productParamList.reduce((pre, cur) => {
          let inputNum = parseInt(cur.inputNum) || 0;
          if (inputNum > 0) {
            pre = pre + inputNum;
            if (cur.sizeWeight && !this.isColorStock) {
              sizeWeight = Math.max(sizeWeight, +cur.sizeWeight);
            }
          }
          return pre;
        }, 0);
        //颜色交期 只有一个尺寸
        if (this.isColorStock) {
          sizeData.productParamList.forEach((item) => {
            if (item.sizeWeight && item.id == this.selectSizeId)
              sizeWeight = Math.max(sizeWeight, +item.sizeWeight);
          });
        }
        if (
          this.smallWeightBoundaryLine >= sizeWeight * allNum &&
          sizeWeight * allNum > 0
        ) {
          this.$store.commit("halfDesign/setSmallWeightDelivery", true);
        } else {
          this.$store.commit("halfDesign/setSmallWeightDelivery", false);
        }
      }
    },
    mergeBackImg(img1, img2) {
      if (!img1 || !img2) return Promise.resolve(false);
      return new Promise((resolve, reject) => {
        Promise.all([loadImage(urlAddVersion(img1)), loadImage(urlAddVersion(img2))])
          .then((result) => {
            let canvas = document.createElement("canvas"),
              ctx = canvas.getContext("2d"),
              back1 = result[0],
              back2 = result[1];
            canvas.width = back1.width;
            canvas.height = back1.height;
            ctx.drawImage(back1, 0, 0, back1.width, back1.height);
            ctx.drawImage(back2, 0, 0, back2.width, back2.height);
            const mergedImageDataUrl = canvas.toDataURL("image/png");
            resolve(mergedImageDataUrl);
          })
          .catch((err) => {
            console.log(err);
          });
      });
    },
    async getRibbonColorImg(id) {
      let copyId = id || 0;
      let areaColorImg = this.areaColorRibbonImgs.find((citem) => {
        return citem.attributeValueId == copyId;
      });
      this.LanyardColorImg = areaColorImg ? areaColorImg.src : "";
      //将初始默认选中的无织带背景和织带合并在一起
      let allUrl = await this.mergeBackImg(this.noRibbonBackImg, this.LanyardColorImg);
      if (!allUrl) allUrl = this.noRibbonBackImg;
      await this.setAreaData(allUrl);
    },
    async setAreaData(url) {
      let area = this.findParams(this.stepArr, "attributeFlag", "area");
      //默认第一个方位
      let data = area.productParamList[0];
      if (data.isMask) {
        this.isMask = true;
        this.maskContent = data.content;
      } else {
        this.isMask = false;
        this.maskContent = "";
      }
      this.firstImg = "";
      this.$store.commit("halfDesign/setCanClickBtn", false);
      //保存上一个方位的模板数据
      this.fbDataObj[this.prevAreaName || data.valueName] = canvas.getJson();
      //清空画布
      this.clearCanvas();
      //加载之前保存的方位数据,并更新元素裁剪区域
      if (this.fbDataObj[data.valueName]) {
        this.loadTempData = true;
        this.fabricStage.loadFromJSON(this.fbDataObj[data.valueName], () => {
          //同步画布中的文字和图片到设计步骤
          this.$store.commit("halfDesign/syncCanvasTextAndImg");
          let img = this.getImg();
          if (!img) {
          } else {
            this.uploadImg = {
              type: "",
              url: img.getSrc(),
              name: "",
            };
          }
        });
      } else {
        //同步画布中的文字和图片到设计步骤
        this.$store.commit("halfDesign/syncCanvasTextAndImg");
      }
      setTimeout(() => {
        this.loadTempData = false;
        this.$store.commit("halfDesign/setCanClickBtn", true);
      }, 1000);
      //加载背景图
      await canvas.setCanvasBg(url, {
        isClip: this.productInfo.isClip,
        imgWidth: this.productInfo.imgWidth,
        imgHeight: this.productInfo.imgHeight,
      });
      //保留画布位置信息
      this.canvasPosition = data;
      //加载边框
      await canvas.setEditArea(data, {
        isClip: this.productInfo.isClip,
        imgWidth: this.productInfo.imgWidth,
        imgHeight: this.productInfo.imgHeight,
      });
      //更新画布元素裁剪路径
      let elArr = this.fabricStage.getObjects().filter((item) => {
        return item.type === "i-text" || item.type === "image";
      });
      elArr.forEach((item) => {
        //medals 不保留正方形路径
        if (!this.isMedals) item.clipPath = canvas.clipPath;
        if (item.type === "i-text") {
          item.on("editing:exited", () => {
            let textProperty = this.getTextProperty(item);
            this.$store.commit("halfDesign/updateTextItem", textProperty);
          });
        }
      });
      this.prevAreaName = data.valueName;
      this.firstImg = "";
      this.editStatus = true;
    },
    cancelNoAndNext(type) {
      this.errStep.push(-1);
      this.needNextStepObj[this.noNextStepAndNext.parentId] = [
        this.noNextStepAndNext.errId,
      ];
      if (type) {
        this.needNextStepObj[this.noNextStepAndNext.parentId] = [];
      }
      this.errStep.pop();
    },
    switchMask(id, data) {
      this.stepArr.forEach((item) => {
        item.showMask = false;
        if (item.id == id) item.showMask = data;
      });
      this.openCanvas(false);
      this.showNextStepDialog = data;
    },
    dialogClickSelf() {
      this.switchMask(this.nowMaskId, false);
    },
    nextStep() {
      this.checkParams(this.paramsArr, false);
      if (this.errStep.length == 0) {
        this.nextEnd();
        return;
      }
      this.chanceIdAndNext();
    },
    openCanvas(status) {
      if (!this.isMedals) return;
      this.$nextTick(() => {
        let pdtLeft = document.querySelector(".pdt-left");
        this.setPdtLeft(-this.headerHeight + 20);
        if (status) {
          pdtLeft.classList.add("isFixed");
        } else {
          pdtLeft.classList.remove("isFixed");
        }
      });
    },
    chanceIdAndNext() {
      let errStepId = ("" + this.errStep[0]).replace(/copy/g, "");
      //需要找到当前参数不全步骤的父级
      let errStepParent = this.stepArr.find((item) => {
        return (
          item.attributeCategoryList &&
          item.attributeCategoryList.length &&
          item.attributeCategoryList.some((citem) => citem.id === +errStepId)
        );
      });
      if (errStepParent) {
        this.switchMask(+errStepParent.id, true);
        this.nowMaskId = +errStepParent.id;
      }
      this.setMaskDialog();
    },
    setMaskDialog() {
      this.$nextTick(() => {
        if (this.isMobile) {
          if (this.errStep.length > 0) this.openCanvas(true);
          if (this.type === "dialog") {
            this.nowScrollY = domScrollFn(
              `#${this.nowMaskId}`,
              "start",
              -40 - this.topMobileCanvasHeight,
              false,
              ".halfDetail"
            );
          } else {
            this.nowScrollY = domScrollFn(
              `#${this.nowMaskId}`,
              "start",
              -18 - this.topMobileCanvasHeight
            );
          }
        } else {
          if (this.type === "dialog") {
            domScrollFn(
              `#${this.nowMaskId}`,
              "start",
              -this.headerHeight - 20,
              false,
              ".halfDetail"
            );
          } else {
            domScrollFn(`#${this.nowMaskId}`, "start", -this.headerHeight - 40);
          }
        }
      });
    },
    nextEnd() {
      this.switchMask(this.nowMaskId, false);
      if (this.type === "dialog") {
        domScrollFn(`.subtotal`, "end", this.pdtSub + 20, false, ".halfDetail");
      } else {
        domScrollFn(`.subtotal`, "end", this.pdtSub + 20);
      }
    },
    toNext(data) {
      let arr = [];
      data.attributeCategoryList.forEach((item) => {
        let find = this.paramsArr.find((pitem) => pitem.type === item.attributeFlag);
        if (find) {
          arr.push(find);
        }
      });
      if (this.checkParams(arr)) {
        this.currentStep += 1;
        if (!this.emailLater) {
          //chonese tea 东极茶
          if (this.proId == 427 || this.proId == 446) return;
          if (this.isBlankPrintMethod) {
            this.editStatus = false;
          } else {
            this.firstImg = "";
            this.editStatus = true;
          }
        }
        this.$nextTick(() => {
          if (!this.productInfo.isLevitate) return;
          //第二步之后 以设计图定位
          let pdtLeft = document.querySelector(".pdt-left");
          if (this.editStatus) {
            pdtLeft.classList.add("isSticky");
            this.setPdtLeft(0);
            if (this.type === "dialog") {
              let halfDetail = document.querySelector(".halfDetail");
              if (halfDetail) {
                halfDetail.scrollTo({
                  top: 0,
                  behavior: "smooth",
                });
              }
            } else {
              let breadCrumbsHeight = document.querySelector(".breadCrumbs").offsetHeight;
              let modalHeader = document.querySelector("#modalHeader").offsetHeight;
              domScrollFn(".half-container", "start", breadCrumbsHeight - modalHeader);
            }
          } else {
            pdtLeft.classList.remove("isSticky");
          }
          // domScrollFn(`#mobileNextBtn${this.currentStep}`, "end", 20);
          // if (this.currentStep == this.orderDetailStep - 1) {
          // 	//最后一步
          // 	domScrollFn(`#addCartBtn`, "end", 20);
          // }
        });
      }
    },
    toPrev() {
      if (this.currentStep == 1) return;
      this.currentStep -= 1;
    },
    zoom() {
      if (canvas.isBig) {
        canvas.small();
        this.isBig = false;
      } else {
        canvas.big();
        this.isBig = true;
      }
    },
    toCate() {
      let path =
        this.isStockPage == "1"
          ? this.halfCateDTO.shopRouting
          : this.halfCateDTO.customRouting;
      this.$router.push({
        path: path,
      });
    },
    changeTextVal(val) {
      let activeEle = this.fabricStage.getActiveObject();
      if (!activeEle) {
        return;
      }
      activeEle.set("text", val);
      this.fabricStage.requestRenderAll();
      this.$store.commit("halfDesign/updateTextItem", canvas.getTextProperty(activeEle));
      this.$nextTick(() => {
        this.locationToolBar(activeEle);
      });
    },
    changeTextProperty(val, property, item) {
      if (val === undefined) {
        return false;
      }
      if (!canvas.c) {
        return false;
      }
      let activeEle = item || canvas.c.getActiveObject();
      if (!activeEle) {
        this.$toast.error(this.langSemiCustom.colorSelectTip);
        return false;
      }
      canvas.changeTextProperty({
        val,
        property,
        item: activeEle,
      });
      this.$store.commit("halfDesign/updateTextItem", canvas.getTextProperty(activeEle));
      this.getTextProperty(activeEle);
      this.$nextTick(() => {
        this.locationToolBar(activeEle);
      });
    },
    toggleTips(id, type) {
      try {
        let errStep = document.getElementById(id);
        let errDom = document.getElementById(id).getElementsByClassName("errorTip")[0];
        let errDom2 = document.getElementById(id).getElementsByClassName("errorTip2")[0];
        if (errStep) {
          type === 1
            ? errStep.classList.add("errorStep")
            : errStep.classList.remove("errorStep");
        }
        if (
          errDom2 &&
          JSON.stringify(this.teaDatas) == "{}" &&
          this.productInfo.isDevise == 1
        ) {
          errDom2.style.display = type === 1 ? "block" : "none";
          return;
        }
        if (errDom) {
          errDom.style.display = type === 1 ? "block" : "none";
        }
      } catch (e) {
        console.log(e, "id", id);
      }
    },
    //克隆画布
    cloneFabric(fb) {
      return new Promise((resolve) => {
        fb.clone((cloneObj) => {
          resolve(cloneObj);
        });
      });
    },
    async getAllAreaImg() {
      let findCurrentArea = this.paramsArr.find((item) => item.type === "area"),
        areaList = this.areaList;
      if (findCurrentArea) {
        let fbData = this.fbDataObj,
          picArr = [];
        let cloneFabric = await this.cloneFabric(this.fabricStage);
        let loadData = function (data) {
          return new Promise((resolve) => {
            cloneFabric.loadFromJSON(data, () => {
              resolve(cloneFabric);
            });
          });
        };
        //遍历方位列表
        for (const area of areaList) {
          let name = area.valueName;
          if (name !== findCurrentArea.val.valueName) {
            if (fbData[name]) {
              let newCloneFabric = await loadData(fbData[name]);
              picArr.push(this.getPic(newCloneFabric));

              let images = newCloneFabric.getObjects("image");
              let canvasLogo = images.map((item) => item.getSrc())[0] || "";
              if (canvasLogo && isBase64(canvasLogo)) {
                this.otherLogo = await uploadFile(dataURLtoFile(canvasLogo, 1));
              } else {
                this.otherLogo = canvasLogo;
              }
              let textArr = newCloneFabric.getObjects("i-text");
              textArr.map((item) => {
                let sizeData = {
                  color: item.fill,
                  fontFamily: item.fontFamily,
                  fontSize: item.fontSize,
                  fontStyle: item.fontStyle ? item.fontStyle : "normal",
                  fontWeight: item.fontWeight,
                  text: item.text,
                };
                this.otherText.push(sizeData);
              });
            } else {
              picArr.push(area.imgDetail);
            }
          }
        }
        return picArr;
      } else {
        return [];
      }
    },
    getQuotePriceParams(teaData) {
      let paramsArr = this.paramsArr,
        specialArr = ["discount", "size", "printMethod"],
        paramIdList = [],
        qtyDetailDTOS = [],
        isNoPrint = this.isNoPrint;
      //气球
      if (this.isColorStock) specialArr = ["discount", "color", "size", "printMethod"];
      let findDiscount = paramsArr.find((item) => {
        return item.type === "discount";
      });
      let findPrintMethod = paramsArr.find((item) => {
        return item.type === "printMethod";
      });
      let findSizeId = paramsArr.find((item) => {
        return item.type === "size";
      });
      paramsArr.forEach((item) => {
        if (isNoPrint) {
          if (item.val && !item.isDesignStep && !specialArr.includes(item.type)) {
            paramIdList.push(item.val.id);
          }
        } else {
          if (item.val && !specialArr.includes(item.type)) {
            paramIdList.push(item.val.id);
          }
        }
      });
      let size = this.findParams(this.stepArr, "attributeFlag", "size");
      //茶叶价格计算
      if (this.productInfo.isDevise == 1) {
        if (teaData) {
          this.teaDatas = teaData;
        }
        if (JSON.stringify(this.teaDatas) != "{}") qtyDetailDTOS.push(this.teaDatas);
      } else {
        if (this.isColorStock) {
          //气球
          size = this.findParams(this.stepArr, "attributeFlag", "color");
        }
        size.productParamList.forEach((item) => {
          let inputNum = parseInt(item.inputNum);
          if (inputNum > 0) {
            //如果是pens 一美金活动的新券(默认是1美金抵20只笔)数量少于等于20 只能默认选free的交期，行李牌是2个
            if (
              this.productInfo.isActivity &&
              this.hasOneDollarPens &&
              this.newOneDollarPens
            ) {
			  let num=20;
			  if(this.isLuggagetags) num=2
              this.$store.commit("halfDesign/setDisdountSelect", false);
              if (inputNum <= num)
                this.$store.commit("halfDesign/setDisdountSelect", true);
            }
            qtyDetailDTOS.push({
              quantity: inputNum,
              paramType: "size",
              paramId: item.id,
            });
          }
        });
      }

      return {
        projectName: this.$store.state.proName,
        cateId: this.productInfo.categoryId,
        productId: this.productInfo.id,
        paramIdList: paramIdList,
        discountId: findDiscount.val.id,
        unitPriceId: this.unitPriceStep.id,
        qtyDetailDTOS: qtyDetailDTOS,
        printMethodId: findPrintMethod ? findPrintMethod.val.id : "",
        sizeId: findSizeId ? findSizeId.val.id : "",
        isPirceLayered: this.productInfo.isDevise == 1 ? 0 : 1,
      };
    },
    async getQuoteParams() {
      let paramsArr = JSON.parse(JSON.stringify(this.paramsArr)),
        productInfo = JSON.parse(JSON.stringify(this.productInfo)),
        canvas = this.fabricStage,
        base64 = this.getPic(canvas),
        base64Two = this.getPic2(canvas),
        file = dataURLtoFile(base64, 1),
        file2 = base64Two && dataURLtoFile(base64Two, 1),
        isNoPrint = this.isNoPrint,
        url,
        printAreaCanvas,
        printAreaCanvasArr = [],
        areaUrl = [];
      let areaObj = {};
      delete productInfo.attributeCategoryStepList;
      delete productInfo.category;
      let uploadList = this.commentObj.uploadList.map((item) => item.secure_url);
      if (isNoPrint) {
        let findColor = paramsArr.find((item) => item.type === "color");
        if (findColor) {
          url = JSON.parse(findColor.val.imgJson)[0].url;
        }
      } else {
        url = await uploadFile(file);
        if (file2) {
          printAreaCanvas = await uploadFile(file2);
        }
        let areaPicArr = await this.getAllAreaImg(),
          arr1 = [],
          arr2 = [];
        areaPicArr.forEach((item) => {
          if (isBase64(item)) {
            arr2.push(dataURLtoFile(item, 1));
          } else {
            arr1.push(item);
          }
        });
        let result = await concurRequest(arr2, 10, uploadFile);
        areaUrl = result.concat(arr1);
        areaUrl.forEach((item, index) => {
          areaObj[`url${index + 2}`] = item;
        });
      }
      //获取画布中的logo图片
      let logoData = [];
      let textList = [];
      if (this.isCoins || this.isBeltBuckles || this.isPokerChips) {
        let fileArr = [];
        for (let [key, value] of Object.entries(this.areaColorIndexObj)) {
          let arr = this.areaUploadList[key];
          if (!arr) continue;
          if (arr.areaCanvas) {
            let canvasUrl = await uploadFile(dataURLtoFile(arr.areaCanvas, key));
            printAreaCanvasArr.push(canvasUrl);
          }
          arr.files.map((item) => {
            fileArr.push(item.secure_url);
          });
          //文字
          arr.textArr.map((item) => {
            if (item.text.trim().length > 0) {
              let sizeData = {
                area: key,
                color: item.fill,
                opacity: item.opacity,
                fontSize: item.fontSize,
                fixedFontSize: item.fixedFontSize,
                fontFamily: item.fontFamily,
                fontStyle: item.fontStyle ? item.fontStyle : "normal",
                fontWeight: item.fontWeight,
                text: item.text,
                maxChars: item.maxChars,
              };
              let textObj = JSON.parse(JSON.stringify(sizeData));
              if (item.shadow?.enabled) {
                textObj = Object.assign({}, sizeData, item.shadow);
                delete textObj["enabled"];
              }
              for (let key in textObj) {
                if (
                  textObj[key] === null ||
                  textObj[key] === undefined ||
                  (typeof textObj[key] === "string" && textObj[key].trim() === "") ||
                  (Array.isArray(textObj[key]) && textObj[key].length === 0)
                ) {
                  delete textObj[key];
                }
              }
              textList.push(textObj);
            }
          });
        }
        logoData = fileArr;
      } else {
        let images = canvas.getObjects("image");
        let canvasLogo = images.map((item) => item.getSrc()) || "";
        if (canvasLogo.length) {
          //如果是袜子repeat模式，只需要上传一张，因为都是重复的
          if (this.$store.state.halfDesign.tile) {
            canvasLogo = [canvasLogo[0]];
          }
          let arr = await Promise.all(
            canvasLogo.map((item) => {
              if (isBase64(item)) {
                return uploadFile(dataURLtoFile(item, 1));
              } else {
                return item;
              }
            })
          );
          logoData.push(...arr);
        }
        if (this.otherLogo) logoData.push(this.otherLogo);
        // 获取画布中的文字信息
        let textArr = this.fabricStage.getObjects("i-text");
        textArr.map((item) => {
          let sizeData = {
            color: item.fill,
            fontFamily: item.fontFamily,
            // fontSize: item.fontSize,
            fontStyle: item.fontStyle ? item.fontStyle : "normal",
            fontWeight: item.fontWeight,
            text: item.text,
          };
          textList.push(sizeData);
        });
      }
      console.log(logoData, "logoData");
      if (productInfo.isDevise == 1) {
        url = JSON.parse(productInfo.imgJson)[0].url;
      }

      let data = {
        classificationData: productInfo,
        fontData: {
          comments: this.commentObj.comments,
          colorRemark: this.$store.state.halfDesign.colorRemark,
        },
        finaData: [],
        // canvasData: {
        // 	url,
        // },
        canvasData: {
          url: url,
        },
        halfCanvas: uploadList ? uploadList : [],
        logoCanvas: logoData,
        textList: textList.concat(this.otherText),
        printAreaCanvas,
        printAreaCanvasArr,
      };
      Object.assign(data.canvasData, areaObj);
      paramsArr.forEach((item) => {
        if (item.parent && item.type !== "artwork" && item.type != "printing") {
          //不展示印刷颜色就不带去购物车
          if (!this.canShowPrintColorPrice && item.type == "printColor") return;
          let obj = item.parent;
          obj.productParamList = [item.val];
          data.finaData.push(obj);
        }
      });
      //根据织带步骤第一步的type 来选择参数
      if (this.selectLanyardsType == 2) {
        data.finaData.forEach((item) => {
          if (item.isLanyards == "LanyardColor") {
            item.productParamList = [];
          }
        });
      } else if (this.selectLanyardsType == 3) {
        data.finaData.forEach((item) => {
          if (item.isLanyards == "LanyardSize" || item.isLanyards == "LanyardColor") {
            item.productParamList = [];
          }
        });
      }
      let sizeData = this.findParams(this.stepArr, "attributeFlag", "size");
      if (this.isColorStock) {
        //气球
        sizeData = this.findParams(this.stepArr, "attributeFlag", "color");
      }
      if (sizeData) {
        let newObj = JSON.parse(JSON.stringify(sizeData));
        newObj.productParamList = [];
        sizeData.productParamList.forEach((item) => {
          let inputNum = parseInt(item.inputNum);
          if (inputNum > 0) {
            let valueName = item.valueName;
            let inkColor = this.findParams(this.stepArr, "attributeFlag", "ink");
            if (inkColor) {
              valueName = paramsArr.filter((item) => {
                return item.type == "ink";
              })[0].val.valueName;
            }
            newObj.productParamList.push({
              valueName: valueName,
              quantity: item.inputNum,
            });
          }
        });
        //在颜色库存的情况下，将原来存的颜色的去掉
        if (this.isColorStock) {
          data.finaData = data.finaData.filter((item) => {
            return item.id != sizeData.id && item.attributeFlag != "color";
          });
        } else {
          // 将原来存的size的去掉
          data.finaData = data.finaData.filter((item) => {
            return item.id != sizeData.id && item.attributeFlag != "size";
          });
        }
        data.finaData.push(newObj);
      }
      // debugger
      return data;
    },
    checkSize() {
      this.$Bus.$emit("updateQty", "addCart");
      if (this.selectOne == 2) return true;
      let totalQty = 0;
      let lowestPurchaseQuantity = parseInt(this.productInfo.lowestPurchaseQuantity) || 1;
      let sizeData = {};
      if (this.isColorStock) {
        // 气球
        sizeData = this.findParams(this.stepArr, "attributeFlag", "color");
      } else {
        sizeData = this.findParams(this.stepArr, "attributeFlag", "size");
      }
      sizeData.productParamList.forEach((item) => {
        let inputNum = parseInt(item.inputNum);
        if (inputNum > 0) {
          totalQty += inputNum;
        }
      });
      return totalQty >= lowestPurchaseQuantity;
    },
    checkParams(params, type = true) {
      let len = params.length,
        errStep = [],
        isNoPrint = this.isNoPrint,
        checkArr = ["comments", "printing", "designText"];
      if (!this.needUpload || this.isCoins) {
        checkArr.push("artwork");
      }
      if (this.productInfo.isDevise == 1) {
        if (JSON.stringify(this.teaDatas) == "{}") {
          for (let i = 0; i < len; i++) {
            let item = params[i];
            if (this.theme == "10" || this.theme == "11") {
              if (item.type === "size") {
                errStep.push(item.id);
                break;
              }
            } else {
              if (!this.selectSize) {
                if (item.type === "size") {
                  errStep.push(item.id);
                }
              } else {
                if (item.type === "discount") {
                  errStep.push(item.id);
                }
              }
            }
          }
        }
      }
      for (let i = 0; i < len; i++) {
        let item = params[i];
        if (!checkArr.includes(item.type)) {
          //判断是否是需要校验的步骤
          //如果是非压印，设计参数跳过验证
          if (isNoPrint) {
            if (item.isDesignStep) {
              continue;
            }
          }
          //判断是否是尺寸类型
          if (item.type === "size") {
            if (this.productInfo.isDevise == 1) {
              if (!this.selectSize) errStep.push({ id: item.id, stepName: "size1" });
            } else {
              if (this.isColorStock) {
                //判断是否有值
                if (
                  Object.prototype.toString.call(item.val) !== "[object Object]" ||
                  !item.val ||
                  item.val === "null"
                ) {
                  errStep.push(item.id);
                }
                continue;
              }
              if (!this.checkSize()) {
                errStep.push(item.id);
              }
            }
          } else if (item.type === "color" && this.isColorStock) {
            if (
              Object.prototype.toString.call(item.val) !== "[object Object]" ||
              !item.val ||
              item.val === "null"
            ) {
              errStep.push(item.id);
              continue;
            }
            if (!this.checkSize()) {
              errStep.push(item.id);
            }
          } else if (item.isLanyard) {
            //medals 织带步骤
            if (this.selectLanyardsType == 1 || this.selectLanyardsType < 0) {
              //判断是否有值
              if (
                Object.prototype.toString.call(item.val) !== "[object Object]" ||
                !item.val ||
                item.val === "null"
              ) {
                if (item.isLanyard == "LanyardSize" || item.isLanyard == "LanyardColor") {
                  errStep.push(item.copyId);
                } else {
                  errStep.push(item.id);
                }
              }
            }
            //"LanyardColor" 的步骤不展示
            if (this.selectLanyardsType == 2) {
              if (item.isLanyard == "LanyardColor") {
                if (this.selectRidio == 1 || this.selectRidio == -1) {
                  //0emailLater 1选择文件 -1未选择
                  if (!this.LanyardColorFile) {
                    this.needNextStepObj = deepClone(this.copyNeedNextStepObj);
                    errStep.push(item.copyId);
                    this.LanyardColorErrId = item.copyId;
                  }
                } else {
                  //去除needNextStepObj存的id
                  this.outId(item.id);
                }
                continue;
              }
              if (
                Object.prototype.toString.call(item.val) !== "[object Object]" ||
                !item.val ||
                item.val === "null"
              ) {
                if (item.isLanyard == "LanyardSize") {
                  errStep.push(item.copyId);
                } else {
                  errStep.push(item.id);
                }
              }
            }
            if (this.selectLanyardsType == 3) {
              if (item.isLanyard == "LanyardSize" || item.isLanyard == "LanyardColor") {
                continue;
              }
            }
          } else if (item.type === "artwork") {
            //皮带扣背面要隐藏
            if (this.areaIsHideStep && item.isHide) {
              continue;
            }
            if (this.isBeltBuckles || this.isPokerChips) {
              let hadFile = false;
              Object.entries(this.areaColorIndexObj).forEach(([key, value]) => {
                let arr = this.areaUploadList[key];
                if (arr) {
                  if (arr.files.length > 0) hadFile = true;
                }
              });
              if (!hadFile) {
                errStep.push(item.copyId);
                this.artworkErrId = item.copyId;
              }
              continue;
            }
            //需要判断files 是否有值---medals 咬版有使用
            if (
              Object.prototype.toString.call(item.val) !== "[object Object]" ||
              !item.val ||
              item.val === "null"
            ) {
              errStep.push(item.copyId);
              this.artworkErrId = item.copyId;
            } else {
              if (item.val?.files.length == 0) {
                //文字
                let text = this.textArr1.some((item) => {
                  return item.text.length > 0;
                });
                if (!text) {
                  errStep.push(item.copyId);
                  this.artworkErrId = item.copyId;
                }
              }
            }
          } else if (
            this.noNextStepAndNext.parentId == item.parentId &&
            this.noNextStepAndNext.id == item.val?.id
          ) {
            if (
              Object.prototype.toString.call(item.val) !== "[object Object]" ||
              !item.val ||
              item.val === "null"
            ) {
              errStep.push(item.id);
            } else {
              //大步骤没有配置nextStep 但是里面有参数要下一步，判断是否有备注或者上传文件
              // if (item.val.files.length == 0 && !item.val.remarks) {
              // 	errStep.push(item.id);
              // }
            }
          } else {
            //判断是否有值
            if (
              Object.prototype.toString.call(item.val) !== "[object Object]" ||
              !item.val ||
              item.val === "null"
            ) {
              errStep.push(item.id);
            }
          }
        }
      }
      if (errStep.length > 0) {
        this.errStep = errStep;
        if (type) {
          this.toggleTips(errStep[0], 1);
          if (!this.isMedals) {
            if (!this.isMobile || (this.isMobile && this.isMbNext)) {
              // scrollToViewTop(document.getElementById(errStep[0]));
              if (this.type === "dialog") {
                domScrollFn(
                  `#${errStep[0]}`,
                  "start",
                  -this.headerHeight - 40,
                  false,
                  ".halfDetail"
                );
              } else {
                domScrollFn(`#${errStep[0]}`, "start", -this.headerHeight - 40);
              }
            }
          } else {
            this.chanceIdAndNext();
          }
        }
        return false;
      }
      this.errStep = [];
      return true;
    },
    outId(id) {
      for (const [key, value] of Object.entries(this.needNextStepObj)) {
        if (value.includes(+id)) {
          this.needNextStepObj[key] = value.filter((item) => item !== +id);
          return;
        }
      }
    },
    getSizeParam() {
      let obj = { sizeParam: [] };
      //气球size 在color
      if (this.isColorStock) {
        let colorSize = this.findParams(this.stepArr, "attributeFlag", "color");
        colorSize.productParamList.forEach((item) => {
          if (parseInt(item.inputNum) > 0) {
            obj.sizeParam.push({
              quantity: item.inputNum,
              stockParamId: item.stockParamId,
            });
          }
        });
      } else {
        let size = this.findParams(this.stepArr, "attributeFlag", "size");
        if (this.productInfo.isDevise == 1) {
          obj.sizeParam.push({
            quantity: this.teaDatas.quantity,
            stockParamId: size.productParamList.find(
              (item) => item.id == this.teaDatas.paramId
            ).stockParamId,
          });
        }
        size.productParamList.forEach((item) => {
          if (parseInt(item.inputNum) > 0) {
            obj.sizeParam.push({
              quantity: item.inputNum,
              stockParamId: item.stockParamId,
            });
          }
        });
      }

      return obj;
    },
    async addInquiry(expectTime) {
      this.$gl.show();
      let quotePriceParam = this.getQuotePriceParams();
      let quoteParam = await this.getQuoteParams();
      if (this.isFd) {
        if (quoteParam.fontData.comments && quoteParam.fontData.comments.length > 0) {
          quoteParam.fontData.comments =
            "FDCate:" +
            this.productInfo.category.name +
            ",UserComments:" +
            quoteParam.fontData.comments;
        } else {
          quoteParam.fontData.comments =
            "FDCate:" + this.productInfo.category.name + ",UserComments:" + "";
        }
      }
      let sizeParam = this.getSizeParam();
      if (expectTime) {
        quoteParam.finaData?.push(expectTime);
      }
      let postData = {
        uuid: this.isLogin ? null : this.userUUID,
        userId: this.isLogin ? this.userId : null,
        buyType: 9,
        quoteCateId: this.productInfo.categoryId,
        isMobile: this.isMobile,
        quoteCateChildId: this.productInfo.id,
        quotePriceParam: JSON.stringify(quotePriceParam),
        quoteParam: JSON.stringify(quoteParam),
        sizeParam: JSON.stringify(sizeParam),
      };
      let hasActivity = 0;
      //小重量交期产品
      if (this.needJudgeSmallWeight) {
        postData.isSmallQty = 0;
        //小重量
        if (this.showSmallWeightDelivery) {
          let discount = this.paramsArr.find((item) => item.type == "discount");
          if (discount && discount.val) {
            //查询是否需要判断交期时间，交期属性值deliveryDay大于7才算小重量
            try {
              if (this.enableTurnaroundTimeCheck >= 1) {
                if (discount.val?.deliveryDay && discount.val?.deliveryDay >= 7)
                  postData.isSmallQty = 1;
              } else {
                //小重量交期加价不算小重量,0和减价算(暂时只判断百分比的)大于1加价，小于1减价。
                if (discount.val.unitPercent <= 1) {
                  postData.isSmallQty = 1;
                }
              }
            } catch (error) {}
          }
        }
      }
      if (this.hasOneDollarPens && !this.newOneDollarPens) {
        //只有选20只笔活动才是1
        if (this.productInfo.isActivity && this.selectOne == 2) {
          hasActivity = 1;
        }
      }
      postData.isOneDollarPens = hasActivity;
      if (this.productInfo.isActivity && this.hasOneDollarPens)
        postData.isNewCoupon = this.newOneDollarPens ? 1 : 0;
      getSemiInquiry(postData)
        .then((res) => {
          let inquiryData = {
            cateName: res.data.cateName,
            quoteParam: res.data.quoteParam,
            quotePriceParam: res.data.quotePriceParam,
            quotePriceData: res.data.quotePriceData,
            params: res.data.params,
            comments: res.data.comments,
            isOneDollarPens: res.data.isOneDollarPens,
            isSmallQty: res.data.isSmallQty,
            weightParamId: res.data.weightParamId,
            productWeight: res.data.productWeight,
          };
          this.$Bus.$emit("sendInquiryData", inquiryData);
        })
        .finally(() => {
          this.$gl.hide();
        });
    },
    async addCart(type) {
      if (
        !this.newOneDollarPens &&
        !this.selectOne &&
        this.productInfo.isActivity &&
        this.hasOneDollarPens
      ) {
        if (this.type === "dialog") {
          if (this.oneDialog2 == true) {
            this.$toast.info("Please select a coupon option");
          }
          this.oneDialog2 = true;
        } else {
          this.oneDialog = true;
        }
        return;
      }
      if (this.noAddCart) return;
      if (!this.checkParams(this.paramsArr)) {
        return;
      }
      this.$gl.show();
      let quotePriceParam = this.getQuotePriceParams();
      let quoteParam = await this.getQuoteParams();
      let sizeParam = this.getSizeParam();
      let postData = {
        uuid: this.isLogin ? null : this.userUUID,
        userId: this.isLogin ? this.userId : null,
        buyType: 9,
        quoteCateId: this.productInfo.categoryId,
        isMobile: this.isMobile,
        quoteCateChildId: this.productInfo.id,
        quotePriceParam: JSON.stringify(quotePriceParam),
        quoteParam: JSON.stringify(quoteParam),
        sizeParam: JSON.stringify(sizeParam),
      };
      let hasActivity = 0;
      if (this.hasOneDollarPens && !this.newOneDollarPens) {
        //只有选20只笔活动才是1
        if (this.productInfo.isActivity && this.selectOne == 2) {
          hasActivity = 1;
        }
      }
      if (
        this.$route.query.orderNow == 1 ||
        type == "checkOut" ||
        type == "addAndNoCheckOut"
      ) {
        postData.orderNow = 1;
      }
      //小重量交期产品
      if (this.needJudgeSmallWeight) {
        postData.isSmallQty = 0;
        //小重量
        if (this.showSmallWeightDelivery) {
          let discount = this.paramsArr.find((item) => item.type == "discount");
          if (discount && discount.val) {
            //查询是否需要判断交期时间，交期属性值deliveryDay大于7才算小重量
            try {
              if (this.enableTurnaroundTimeCheck >= 1) {
                if (discount.val?.deliveryDay && discount.val?.deliveryDay >= 7)
                  postData.isSmallQty = 1;
              } else {
                //小重量交期加价不算小重量,0和减价算(暂时只判断百分比的)大于1加价，小于1减价。
                if (discount.val.unitPercent <= 1) {
                  postData.isSmallQty = 1;
                }
              }
            } catch (error) {}
          }
        }
      }
      postData.isOneDollarPens = hasActivity;
      if (this.productInfo.isActivity && this.hasOneDollarPens)
        postData.isNewCoupon = this.newOneDollarPens ? 1 : 0;
      let productData = {
        data: { item_id: this.productInfo.productSku, item_name: this.productInfo.name },
        value: this.priceInfo.totalPrice || 0,
      };
      if (type == "checkOut") {
		productData.orderNow = 1;
        //直接去结算，存画布数据
        // let copyDetailData = {};
        // copyDetailData.quoteParam = quoteParam;
        // if (this.isBeltBuckles || this.isPokerChips || this.isCoins) {
        //   copyDetailData.areaUploadList = JSON.parse(JSON.stringify(this.areaUploadList));
        // } else {
        //   copyDetailData.fbDataObj = JSON.parse(JSON.stringify(this.fbDataObj));
        // }
        // window.localStorage.setItem("tempDetailData", JSON.stringify(copyDetailData));
      }
      addCart(postData, productData)
        .then((res) => {
          if (type == "addAndNoCheckOut") {
            this.$gl.hide();
            this.showAddCartDialog = true;
            this.$store.dispatch("updateHeadFootPages");
            this.tempCart = res.data;
            return;
          }
          this.$store.commit("halfDesign/updateColorRemark");
          this.$store.commit("setMask", false);
          this.$nextTick(() => {
            //20只笔的活动
            if (
              !this.newOneDollarPens &&
              this.hasOneDollarPens &&
              this.selectOne == 2 &&
              this.productInfo.isActivity
            ) {
              let cartList = res.data;
              cartList.forEach((item, index) => {
                // 是否显示满赠数量
                // 判断报价是否带来赠送数量
                item.hasGiftQty = item.quantityList.find((ele) => ele.isQuoteQuantity)
                  ? true
                  : false;
                // 暂存编辑的数量 防止改数量引起rushFee负数
                item.quantityEdit = item.quantity;
                //另存数据便于取消更改时数量复原显示
                item.quantityListEdit = JSON.parse(JSON.stringify(item.quantityList));
                let obj = {};
                item.propsData = {
                  id: item.id,
                  proId: item.proId,
                  isSemiStock: item.isSemiStock,
                  isBlank: item.isBlank,
                  isEmailLater: item.isEmailLater,
                  isSemiProduct: item.buyType === 9 ? true : false, // 半定制产品,
                  closeTips:
                    this.closeIndexList && this.closeIndexList.some((i) => i == item.id)
                      ? true
                      : false,
                };
                item.select = true;
                if (item.isStock == 1) item.select = false;
                item.viewMore = false;
              });
              localStorage.setItem("tempCart", JSON.stringify(cartList));
              this.$router.push({
                path: "/checkout",
              });
            } else if (this.$route.query.orderNow == 1) {
              let cartList = res.data;
              localStorage.setItem("tempCart", JSON.stringify(cartList));
              this.$router.push({
                path: "/checkout",
              });
            } else if (type == "checkOut") {
              let cartList = res.data;
              localStorage.setItem("tempCart", JSON.stringify(cartList));
              this.$router.push({
                path: `/checkout?fromDetail=${res.data[0].quoteCateChildId}`,
              });
			  this.$store.dispatch("updateHeadFootPages");
            } else {
              this.$router.push({
                path: "/cart",
              });
            }
          });
        })
        .finally(() => {
          setTimeout(() => {
            this.$gl.hide();
          }, 1000);
        });
    },
    goCart() {
      this.$router.push({
        path: "/cart",
      });
      this.showAddCartDialog = false;
      this.tempCart = null;
    },
    goCheckout() {
      localStorage.setItem("tempCart", JSON.stringify(this.tempCart));
      this.$router.push({
        path: "/checkout",
      });
      this.showAddCartDialog = false;
      this.tempCart = null;
    },
    cancelCartDialog() {
      this.showAddCartDialog = false;
      this.tempCart = null;
    },
    calculatePrice(teaData) {
      let quotePriceParams = this.getQuotePriceParams(teaData);
      if (!quotePriceParams.unitPriceId) {
        return false;
      }
      halfCalculate(quotePriceParams).then((res) => {
        this.priceInfo = res.data;
      });
      if (quotePriceParams.qtyDetailDTOS.length != 0) {
        if (this.productInfo.isDevise == 1) return;
        // 如果是需要多次查询的size样式
        if (!this.needCyclicQuery) return;
        Promise.all(
          this.priceArr.map((item) => {
            // let newquotePriceParams = deepClone(quotePriceParams);
            let newquotePriceParams = JSON.parse(JSON.stringify(quotePriceParams));
            newquotePriceParams.qtyDetailDTOS[0].quantity = item;
            return this.wrappedHalfCalculate(newquotePriceParams);
          })
        )
          .then((results) => {
            // 按顺序打印多次调用的结果
            this.$Bus.$emit("priceNum", results);
          })
          .catch((error) => {
            console.error(error); // 处理错误
          });
      }
    },
    // 定义一个 halfCalculate 函数的包装器，将其返回值封装成 Promise
    wrappedHalfCalculate(param) {
      return new Promise((resolve, reject) => {
        // 调用原始的 halfCalculate 函数，并在回调中解析结果
        halfCalculate(param).then((result) => {
          let data = {
            quantity: result.data.qtyDetailDTOS[0].quantity,
            unitPrice: result.data.foundationUnitPrice,
          };
          resolve(data);
        });
      });
    },
    goCollection(item) {
      let isCollection = item.isCollection;
      if (!this.isLogin) {
        this.$store.commit("setMask", "login");
        return;
      }
      if (isCollection) {
        deleteConllectionByUserId({
          userId: this.userId,
          productId: item.id,
        }).then((res) => {
          this.getAllParams();
          this.$Bus.$emit("collectionProduct", {
            productId: item.id,
            type: "cancelCollection",
          });
        });
      } else {
        let productData = {
          data: {
            item_id: this.productInfo.productSku,
            item_name: this.productInfo.name,
          },
          value: this.priceInfo.totalPrice || 0,
        };
        addCollection(
          {
            userId: this.userId,
            website: 1,
            cateId: this.productInfo.categoryId,
            productId: item.id,
          },
          productData
        ).then((res) => {
          this.getAllParams();
          this.$Bus.$emit("collectionProduct", {
            productId: item.id,
            type: "collection",
          });
        });
      }
    },
    getTextProperty(ele) {
      if (!ele) {
        return;
      }
      let obj = canvas.getTextProperty(ele);
      this.textProperty = Object.assign({}, this.textProperty, obj);
      return deepClone(this.textProperty);
    },
    activeItem(item) {
      if (item.type === "i-text") {
        this.getTextProperty(item);
      }
      this.fabricStage.setActiveObject(item);
      this.selectedElement = item;
      this.fabricStage.requestRenderAll();
    },
    discardActiveObject() {
      if (!this.fabricStage) {
        return false;
      }
      this.selectedImgColor = "";
      this.selectedImgReplaceColor = "";
      this.selectedElement = "";
      this.fabricStage.discardActiveObject();
      this.fabricStage.requestRenderAll();
    },
    locationToolBar(obj) {
      if (!obj || this.hideTool) {
        return false;
      }
      let tool = this.$refs["toolOption"],
        designBody = document.querySelector("#designBody"),
        box1 = tool.getBoundingClientRect(),
        box2 = designBody.getBoundingClientRect();
      var bound = obj.getBoundingRect();
      let absCoords = this.fabricStage.getAbsoluteCoords(obj);
      let left = absCoords.left - (box1.width - bound.width) / 2;
      let top = absCoords.top + 20 - window.scrollY;

      if (left < box2.left) {
        left = box2.left;
      } else {
        if (left > box2.width + box2.left - box1.width) {
          left = box2.width + box2.left - box1.width;
        }
      }
      tool.style.left = left + "px";
      tool.style.top = top + "px";
    },
    toggleThreeD(bool) {
      this.showThreeD = bool;
    },
    beginEdit() {
      if (!canvas.c || !canvas.getClipPath()) {
        return false;
      }
      if (this.isMedals || this.isCoins || this.isBeltBuckles || this.isPokerChips) {
        if (this.emailLater) {
          //取消掉勾选emailLater
          this.$Bus.$emit("cancelEmailLater");
        }
        if (this.isNoPrint) {
          this.$toast.error(
            "Please choose a printing method that is available for design."
          );
          return false;
        }
        this.editStatus = true;
        this.firstImg = "";
        return;
      }
      if (this.isNoPrint) {
        this.$toast.error(
          "Please choose a printing method that is available for design."
        );
        return false;
      }
      let design = this.findParams(this.stepArr, "attributeFlag", "printing");
      if (this.emailLater) {
        //取消掉勾选emailLater
        this.$Bus.$emit("cancelEmailLater");
      }
      //如果是袜子的repeat模式 不添加文本
      if (this.$store.state.halfDesign.tile) {
        this.editStatus = true;
        this.firstImg = "";
        return false;
      }
      this.$nextTick(() => {
        if (design) {
          if (this.type !== "dialog") {
            scrollToViewCenter(document.getElementById(design.id));
          }
          this.$Bus.$emit("focusDesignInput");
        }
        let textArr = canvas.getElementByType("i-text");
        if (textArr.length > 0) {
          this.activeItem(textArr[0]);
        } else {
          let Text = this.newAddTextBefore({
            val: "Add Text",
            textItem: this.$store.state.halfDesign.textArr[0],
          });
          this.$store.commit("halfDesign/updateTextItem", canvas.getTextProperty(Text));
        }
      });
      this.editStatus = true;
      this.firstImg = "";
    },
    handlerScroll() {
      if (this.isMobile && this.isMedals) {
        if (this.showNextStepDialog) {
          this.setPdtLeft(-this.headerHeight + 20 - window.scrollY + this.nowScrollY);
        }
      }
      if (this.type !== "dialog") {
        let footer = document.getElementsByTagName("footer")[0];
        this.subShow =
          window.scrollY > 100 && window.innerHeight + window.scrollY < footer.offsetTop;
      }
    },
    addImgBefore({ data, cb }) {
      if (this.uploadImg.type === "upload") {
        this.addImg(data, cb);
      } else {
        this.replaceImg(data);
      }
    },
    changeTextPropertyBefore(data) {
      if (data.textItem) {
        //半定制改版新增逻辑
        let arr = this.fabricStage.getObjects().filter((o) => {
          return o.type === "i-text";
        });
        let findText = arr.find((c) => c.id === data.textItem.id);
        if (findText) {
          this.changeTextProperty(data.val, data.property, findText);
          this.$store.commit(
            "halfDesign/updateTextItem",
            canvas.getTextProperty(findText)
          );
        }
      } else {
        this.changeTextProperty(data.val, data.property);
      }
      this.editStatus = true;
      this.firstImg = "";
    },
    changeTextPropertyBefore2(data) {
      if (data.textItem) {
        let arr = this.fabricStage.getObjects().filter((o) => {
          return o.type === "group";
        });
        let findText = arr.find((c) => c.id === data.textItem.id);
        if (findText) {
          this.changeTextProperty2(data.val, data.property, data.textItem);
        }
      } else {
      }
    },
    changeTextProperty2(val, property, item) {
      if (val === undefined) {
        return false;
      }
      if (!canvas.c) {
        return false;
      }
      let activeEle = item || canvas.c.getActiveObject();
      if (!activeEle) {
        this.$toast.error(this.langSemiCustom.colorSelectTip);
        return false;
      }
      let { points, text, scaleFactor, ...textOptions } = item;
      let propertyData =
        typeof val === "object" ? { [property]: val.code } : { [property]: val };
      let options = {
        evented: false,
        selectable: false,
        ...textOptions,
        ...propertyData,
        id: item.id,
      };
      let newTextArrData = canvas.updateTextArrById2(
        item.id,
        text,
        points,
        options,
        scaleFactor
      );
      let Text = newTextArrData?.textArr
        ?.getObjects()
        .find((obj) => obj instanceof fabric.Text);
      let textProperty = {};
      if (Text) {
        textProperty = this.getTextProperty(Text);
      }
      this.setNowTextData({ ...textProperty, text: item.text, id: item.id });
      this.editStatus = true;
      this.firstImg = "";
    },
    addTextBefore(val) {
      if (this.selectedElement && this.selectedElement.type === "i-text") {
        this.changeTextVal(val);
      } else {
        this.addText(val);
      }
    },
    newAddTextBefore(data) {
      if (this.artworkErrId) {
        this.toggleTips(this.artworkErrId, 0);
        this.artworkErrId = 0;
      }
      if (this.isOrnaments && this.emailLater) {
        this.$Bus.$emit("cancelEmailLater");
      }
      let textArr = canvas.getElementByType("i-text");
      let findText = textArr.find((c) => c.id === data.textItem.id);
      if (findText) {
        findText.set("text", data.val);
        canvas.c.requestRenderAll();
        this.$store.commit("halfDesign/updateTextItem", this.getTextProperty(findText));
        this.editStatus = true;
        this.firstImg = "";
        return findText;
      } else {
        return this.addText(data.val, {
          id: data.textItem.id,
        });
      }
    },
    /*添加文字*/
    addText(val = "CustomText", property = {}) {
      if (!canvas.c) {
        return false;
      }
      let fill = "#ffffff";
      let colorName = "white";
      if (this.isWhite(this.colorCode)) {
        fill = "#000000";
        colorName = "black";
      }
      let Text = canvas.addText(val, {
        fill,
        colorName,
        ...property,
      });
      console.log("输入框:", Text);
      Text.on("editing:exited", () => {
        let textProperty = this.getTextProperty(Text);
        this.$store.commit("halfDesign/updateTextItem", textProperty);
      });
      this.activeItem(Text);
      this.editStatus = true;
      this.firstImg = "";
      if (this.isMask) {
        canvas.c.discardActiveObject();
      }
      return Text;
    },
    //coins 添加文字组
    newAddTextBefore2(data) {
      //白板模式不进入画板，绘制文字
      if (this.isBlankPrintMethod) {
        let { points, text, defaultText, scaleFactor, ...textOptions } = data.textItem;
        this.setNowTextData({ ...textOptions, text: text, id: data.textItem.id });
        return;
      }
      console.log("data:", data);
      let textArr = canvas.getElementByType("group");
      let findGroup = textArr.find((c) => c.id === data.textItem.id);
      if (findGroup && findGroup instanceof fabric.Group) {
        let {
          points,
          text,
          defaultText,
          fontSize,
          scaleFactor,
          ...textOptions
        } = data.textItem;
        let options = {
          evented: false,
          selectable: false,
          ...textOptions,
          id: data.textItem.id,
        };
        let newTextArrData = null;
        if (this.isBeltBuckles || this.isPokerChips) {
          newTextArrData = canvas.updateTextArrById2(
            data.textItem.id,
            data.text,
            points,
            options,
            scaleFactor
          );
        } else {
          let classData = {};
          if (this.isMobile) classData = { maxFontSize: 25 };
          newTextArrData = canvas.updateTextArrById(
            data.textItem.id,
            data.text,
            points,
            options,
            scaleFactor,
            classData
          );
        }
        let Text = newTextArrData?.textArr
          ?.getObjects()
          .find((obj) => obj instanceof fabric.Text);
        let textProperty = {};
        if (Text) {
          textProperty = this.getTextProperty(Text);
        }
        this.setNowTextData({ ...textProperty, text: data.text, id: data.textItem.id });
        this.editStatus = true;
        this.firstImg = "";
        if (this.isMask) {
          canvas.c.discardActiveObject();
        }
      } else {
        return this.addText2(data, {
          id: data.textItem.id,
        });
      }
    },
    /*添加文字*/
    addText2(data, property = {}) {
      if (!canvas.c) {
        return false;
      }
      let { points, text, scaleFactor, fontSize, ...textOptions } = data.textItem;
      let options = {
        evented: false,
        selectable: false,
        ...textOptions,
        ...property,
      };
      options.fontWeight = options.fontWeight || "normal";
      options.fontFamily = options.fontFamily || "Arial";
      //保存coins文字属性
      this.setNowTextData({ id: data.textItem.id, text: data.text, ...options });
      if (this.isBeltBuckles || this.isPokerChips) {
        canvas.addText3(data.text, points, options, scaleFactor);
      } else {
        let classData = {};
        if (this.isMobile) classData = { maxFontSize: 25 };
        let Text = canvas.addText2(data.text, points, options, scaleFactor, classData);
      }
      this.editStatus = true;
      this.firstImg = "";
      if (this.isMask) {
        canvas.c.discardActiveObject();
      }
    },
    //判断是否为白色
    isWhite(value) {
      const whiteReg = /^(#ffffff|white|rgb\(\s*255\s*,\s*255\s*,\s*255\s*\)|rgba\(\s*255\s*,\s*255\s*,\s*255\s*,\s*0\.?\d*\s*\)|rgba\(\s*255\s*,\s*255\s*,\s*255\s*,\s*1\s*\))$/i;
      return whiteReg.test(value);
    },
    async replaceImg(src) {
      if (!this.fabricStage || !this.groupObj) {
        return false;
      }
      let selectedEle = await canvas.replaceImg(src, this.getImg());
      this.activeItem(selectedEle);
      if (!this.isMask) this.activeItem(selectedEle);
      this.editStatus = true;
      this.firstImg = "";
    },
    repeatImg(data) {
      let { imgItem } = data;
      canvas
        .repeatImg(imgItem.secure_url, {
          id: imgItem.id,
          customData: imgItem,
        })
        .then((imgInstance) => {
          if (this.isOrnaments && this.emailLater) {
            this.$Bus.$emit("cancelEmailLater");
          }
          this.editStatus = true;
          this.firstImg = "";
        });
    },
    newAddImg(data) {
      let { imgItem, copyId,isCircle } = data;
	  let addImgKey="addImg"
	  if(this.isPokerChips) addImgKey="addImg2"
      canvas[addImgKey](imgItem.secure_url, {
          id: imgItem.id,
          customData: imgItem,
          imgProperty: imgItem.imgProperty || {},
        },{
		  selectable:true,
		  useSelfClipPath:true
		},!isCircle)
        .then((imgInstance) => {
          if (!this.isMask) this.activeItem(imgInstance);
          if (this.isOrnaments && this.emailLater) {
            this.$Bus.$emit("cancelEmailLater");
          }
          this.editStatus = true;
          this.firstImg = "";
          if (copyId) this.toggleTips(copyId, 0);
        });
    },
    //medals 添加图片
    newAddImg2(data) {
      let { imgItem,isCircle } = data;
      canvas
        .addImg2(imgItem.secure_url, {
          id: imgItem.id,
          customData: imgItem,
        },
		{
		  selectable:true,
		  useSelfClipPath:true
		},
		!isCircle)
        .then((imgInstance) => {
          this.editStatus = true;
          this.firstImg = "";
        });
    },
    //coins 添加图片
    newAddImg3(data) {
      let { imgItem, copyId, isCircle } = data;
      //筹码圆形图片
      if (this.isPokerChips) {
        canvas
          .addImg2(
            imgItem.secure_url,
            {
              id: imgItem.id,
              customData: imgItem,
              textTop: true,
              imgProperty: imgItem.imgProperty || {},
            },
			{
			  selectable:true,
			  useSelfClipPath:true
		    },
            !isCircle
          )
          .then((imgInstance) => {
            if (!this.isMask) this.activeItem(imgInstance);
            this.editStatus = true;
            this.firstImg = "";
            this.toggleTips(copyId, 0);
          });
      } else {
        canvas
          .addImg3(imgItem.secure_url, {
            id: imgItem.id,
            customData: imgItem,
          })
          .then((imgInstance) => {
            this.editStatus = true;
            this.firstImg = "";
            this.toggleTips(copyId, 0);
          });
      }
    },
    //medals 删除图片
    delImg(id) {
      if (!isNaN(id) && +id < 0) return;
      canvas.deImgById(id);
    },
    //medals 删除所有图片
    delAllImg() {
      canvas.clearImgs();
    },
    addImg(src, cb) {
      canvas.addImg(src).then((imgInstance) => {
        if (!this.isMask) this.activeItem(imgInstance);
        this.editStatus = true;
        this.firstImg = "";
        if (typeof cb === "function") {
          cb();
        }
      });
    },
    handleEdit(eventData, transform) {
      let canvas = transform.target.canvas;
      if (canvas) {
        let selectedEle = canvas.getActiveObject();
        this.nowReplaceImgId = selectedEle.id;
      }
      this.hideTool = !this.hideTool;
      this.$nextTick(() => {
        this.locationToolBar(this.selectedElement);
      });
    },
    clearText() {
      this.textProperty = {};
    },
    getImg() {
      return this.fabricStage.getObjects("image")[0];
    },
    clearImg() {
      try {
        this.uploadImg = {
          url: "",
          name: "",
        };
      } catch (error) {}
    },
    //初始化画布
    async initFabricStage(id) {
      let HideControls = {
        tl: false,
        tr: false,
        bl: false,
        br: false,
        ml: false,
        mt: false,
        mr: false,
        mb: false,
        mtr: false,
      };
      fabric.Object.prototype.set({
        borderColor: "#666666",
        borderOpacityWhenMoving: 0.5,
        cornerSize: 25,
        padding: 5,
        objectCaching: false,
        centeredScaling: true,
      });
      fabric.Object.prototype.setControlsVisibility(HideControls);
      fabric.Canvas.prototype.getAbsoluteCoords = function (object) {
        var bound = object.getBoundingRect();
        return {
          left: bound.left + this._offset.left,
          top: bound.top + this._offset.top + bound.height,
        };
      };

      let fabricStage = new fabric.Canvas(id, {
        width: this.isMobile ? this.mobileWidth : 500,
        height: this.isMobile ? this.mobileHeight : 500,
        preserveObjectStacking: true,
      });
      //初始化控制图标
      initControlIcon.apply(this);
      //添加元素辅助线 会出现无法导出图片的bug 可以创建一个副本再导出图片
      initAligningGuidelines(fabricStage);
      initCenteringGuidelines(fabricStage);
      fabricStage.on("object:moving", (e) => {
        this.groupObj.visible = true;
        let type = e.target.type;
        if (type === "i-text" || type === "image") {
          this.locationToolBar(e.target);
        }
      });
      fabricStage.on("object:modified", (e) => {
        this.groupObj.visible = false;
        this.$nextTick(() => {
          let type = e.target.type;
          if (type === "i-text" || type === "image") {
            this.locationToolBar(e.target);
          }
        });
      });
      fabricStage.on("object:scaling", (e) => {
        this.groupObj.visible = true;
      });
      fabricStage.on("object:removed", (e) => {
        this.discardActiveObject();
        let type = e.target.type,
          id = e.target.id;

        if (type === "i-text") {
          this.clearText();
          // this.$store.commit('halfDesign/delTextLineById',id)
        } else if (type === "image") {
          this.clearImg();
          // this.$store.commit('halfDesign/delImgLineById',id)
        } else {
          this.clearText();
          this.clearImg();
        }
      });
      fabricStage.on("mouse:down", (e) => {
        if (e.target) {
          let type = e.target.type;
          this.activeItem(e.target);
          this.$nextTick(() => {
            if (type === "i-text" || type === "image") {
              this.locationToolBar(e.target);
            }
          });
        } else {
          this.hideTool = true;
          if (this.$refs.myMenu.isActive) {
            this.$refs.myMenu.isActive = false; // 关闭菜单
          }
          this.discardActiveObject();
          this.clearText();
        }
      });
      return fabricStage;
    },

    setParamsArr() {
      let stepArr = this.stepArr,
        arr = [];
      stepArr.forEach((item) => {
        item.attributeCategoryList.forEach((citem) => {
          let obj = {
            isLanyard: citem.isLanyards,
            type: citem.attributeFlag,
            copyId: citem.copyId,
            parentId: item.id,
            isNextStep: item.isNextStep,
            id: citem.id,
            val: "",
            parent: "",
            isDesignStep: item.isDevise,
          };
          arr.push(obj);
        });
      });
      this.paramsArr = arr;
    },
    getAllParams() {
      return new Promise((resolve) => {
        getProductInfoAndParamRelation({
          routingName: this.routingName,
          userId: this.isLogin ? this.userId : null,
          productParamId: this.$route.query.id,
        }).then((res) => {
          let stepArr;
          this.productInfo = res.data || {};
          //判断是否展示步骤下面的文案
          this.$store.commit(
            "halfDesign/setShowStepText",
            !!this.productInfo.isShowPrompt
          );
          this.halfCateDTO = res.data.category;
          //判断是否是小重量交期
          if (this.halfCateDTO.smallWeightDeliverySwitch) {
            //判断国家是否支持小重量交期
            if (this.halfCateDTO.countryCode && this.halfCateDTO.countryCode.length > 0) {
              if (
                JSON.parse(this.halfCateDTO.countryCode).includes(
                  this.country.toUpperCase()
                )
              ) {
                //小重量分界线
                this.smallWeightBoundaryLine =
                  this.halfCateDTO.smallWeightBoundaryLine * 1000;
                this.needJudgeSmallWeight = true;
              }
            }
          }
          //判断是否是新年交期
          if (this.halfCateDTO.isNewYearDiscount) {
            this.$store.commit("halfDesign/setNewYearDiscount", true);
          } else {
            this.$store.commit("halfDesign/setNewYearDiscount", false);
          }
          let defaultModeType = this.productInfo.modeType || this.halfCateDTO.modeType;
          let changeModeType = undefined;
          if (this.productInfo.modeType) {
            changeModeType = this.productInfo.changeModeType;
          } else if (this.halfCateDTO.modeType) {
            changeModeType = this.halfCateDTO.changeModeType;
          }
          // 设计步骤模式存储
          this.$store.commit("halfDesign/setProDefaultModeType", defaultModeType);
          this.$store.commit("halfDesign/setProModeType", defaultModeType);
          this.$store.commit("halfDesign/setProChangeModeType", changeModeType);
          this.imgList = JSON.parse(res.data.imgJson).filter((item) => item.url);
          if (res.data.productVideo) {
            this.imgList = [
              {
                url: res.data.productVideo,
                imgUrl: res.data.productVideoImg,
              },
            ].concat(this.imgList);
          }
          stepArr = res.data.attributeCategoryStepList;
          let isLanyard = false;
          let LanyardData = {};
          let LanyardKey = ["LanyardType", "LanyardSize", "LanyardColor"];
          stepArr.forEach((item) => {
            //如果是medals 则不显示设计步骤Step1 后续的步骤减1
            if (this.isMedals) {
              item.sortNum = +item.sortNum - 1;
              //控制是否展示弹窗
              item.showMask = false;
              if (item.attributeCategoryList?.[0].attributeFlag == "printMethod") {
                this.medalsPrintMethod = item.attributeCategoryList[0];
                if (this.isMobile) item.noShow = true;
                item.sortNum = -1;
              }
            }
            if (item.isNextStep) {
              this.needNextStep.push(item.id);
              this.needNextStepObj[item.id] = [];
            }
            //皮带扣 设计布局样式需要调整，可以根据isDevise来判断是否是设计步骤
            if (item.isDevise) {
              let printingStepIndex = undefined;
              if (this.isBeltBuckles) {
                item.attributeCategoryList.forEach((citem, index) => {
                  if (citem.attributeFlag === "printing") {
                    printingStepIndex = index;
                    citem.nowShow = true;
                    this.needAddBorderId.push({ id: citem.id, index: 1 });
                  }
                  if (citem.attributeFlag === "area") {
                    citem.nowShow = true;
                    this.needAddBorderId.push({ id: citem.id, index: 0 });
                  }
                  if (citem.attributeFlag === "color") {
                    this.needAddBorderId.push({ id: citem.id, index: 2 });
                  }
                  if (citem.attributeFlag === "artwork") {
                    citem.nowShow = true;
                    this.needAddBorderId.push({ id: citem.id, index: 3 });
                  }
                  if (citem.attributeFlag === "size") {
                    //皮带扣size8 有个弹窗
                    citem.showDialog = true;
                  }
                });
              }

              if (this.isPokerChips) {
                item.attributeCategoryList.forEach((citem, index) => {
                  if (citem.attributeFlag === "printing") {
                    printingStepIndex = index;
                    citem.nowShow = true;
                    this.needAddBorderId.push({ id: citem.id, index: 1 });
                  }
                  if (citem.attributeFlag === "area") {
                    citem.nowShow = true;
                    this.needAddBorderId.push({ id: citem.id, index: 0 });
                  }
                  if (citem.attributeFlag === "artwork") {
                    citem.nowShow = true;
                    this.needAddBorderId.push({ id: citem.id, index: 2 });
                  }
                });
              }

              if (printingStepIndex > -1) {
                const [printingStep] = item.attributeCategoryList.splice(
                  printingStepIndex,
                  1
                );
                const insertPosition = Math.min(2, item.attributeCategoryList.length);
                //皮带扣不隐藏设计布局和方位
                item.attributeCategoryList.splice(insertPosition, 0, printingStep);
              }
            }
            item.attributeCategoryList.forEach((citem, index) => {
              //存入应该nextStep的子属性
              if (this.needNextStepObj.hasOwnProperty(item.id)) {
                if (
                  citem.attributeFlag !== "comments" &&
                  citem.attributeFlag !== "area" &&
                  citem.attributeFlag !== "email"
                ) {
                  this.needNextStepObj[item.id].push(citem.id);
                }
              }
              //coins隐藏步骤
              if (this.isCoins) {
                if (citem.attributeFlag == "color" || citem.attributeFlag == "printing") {
                  citem.noShow = true;
                }
              }
              if (isLanyard) {
                citem.isLanyards = LanyardKey[index];
                citem.copyId = "copy" + citem.id;
                citem.noShow = true;
                LanyardData[citem.attributeName] = citem;
              }
              //如果是medals 将Ribbon步骤隐藏
              if (citem.productParamList.length) {
                if (citem.productParamList[0].isLanyards) {
                  //当前是织带的样式
                  citem.isLanyards = LanyardKey[index];
                  isLanyard = true;
                }
              }

              let style = citem.styleClass.split("-");
              citem.styleClass = style[0];
              citem.styleName = style[1];
              if (
                style[0] == "HalfDesignSizeStyle3" ||
                style[0] == "HalfDesignSizeStyle4"
              ) {
                this.needCyclicQuery = true;
              }
              if (citem.attributeFlag == "printMethod") {
                if (style[0] == "HalfDesignSizeStyle5") this.skipSizeVerify = true;
              }
              //存储printing 的styleClass 用于设置litterTitle的$emit
              if (citem.attributeFlag == "printing") {
                this.printStyleClass = style[0] + "_" + citem.id;
              }
              //存储printColor 的styleClass 用于设置litterTitle的$emit
              if (citem.attributeFlag == "printColor") {
                this.printColorStyleClass = style[0] + "_" + citem.id;
              }

              //判断area 步骤下面是否有打印颜色，
              //没有的话就将下面的步骤的小标题的step减1 -- 暂时用于pens
              if (citem.attributeFlag == "area" && this.productInfo.categoryId == 8) {
                //判断是否有打印颜色步骤
                let hasProductColor = item.attributeCategoryList.some(
                  (stepItem) => stepItem.attributeFlag == "printColor"
                );
                if (citem.minStepTitle && !hasProductColor) {
                  //获取当前area 的step
                  let areaIndex = citem.minStepTitle.lastIndexOf("-") + 1;
                  let areaNum = +citem.minStepTitle.substring(areaIndex);
                  for (let i = index + 1; i < item.attributeCategoryList.length; i++) {
                    let temp = item.attributeCategoryList[i];
                    if (temp.minStepTitle) {
                      let tempIndex = temp.minStepTitle.lastIndexOf("-") + 1;
                      let tempNum = +temp.minStepTitle.substring(tempIndex);
                      if (tempNum - areaNum == 1) {
                        break;
                      }
                      temp.minStepTitle =
                        temp.minStepTitle.slice(0, tempIndex) + (tempNum - 1) + "";
                    }
                  }
                }
              }
              //在这配置如果步骤下面只有一个小步骤以及小步骤只有一个选项
              //将这个选择配置到大标题上
              let onlyOneChild = undefined;
              citem.productParamList.forEach((citem2, index) => {
                citem2.inputNum = "";
                onlyOneChild = "";
                if (index == 0) {
                  onlyOneChild = citem2.valueName;
                }
                citem2.remarks = "";
                citem2.files = [];
              });
              //步骤没开启隐藏开关，不进行单参数隐藏
              if (citem.isHideStep)
                item.onlyOneChild = citem.attributeTitle ? "" : onlyOneChild;
            });
            isLanyard = false;
          });
          if (this.modeType == 1 || this.modeType == 2) {
            if (this.printStyleClass)
              this.$Bus.$emit(this.printStyleClass, { noShowTitle: true });
            if (this.printColorStyleClass)
              this.$Bus.$emit(this.printColorStyleClass, { noShowTitle: true });
          }
          //移除最后一步的价格，不展示在页面上，只用于计算
          try {
            let lastStep = stepArr[stepArr.length - 1];
            if (lastStep.attributeCategoryList[0].attributeFlag === "unitprice") {
              this.unitPriceStep = stepArr.splice(
                stepArr.length - 1,
                1
              )[0].attributeCategoryList[0].productParamList[0];
              this.priceArr = JSON.parse(this.unitPriceStep.increasePrice).map((item) => {
                return item.quantity;
              });
            }
          } catch (e) {}
          //store 存入medals 织带隐藏的步骤数据
          this.$store.commit("halfDesign/setMedalsLanyardData", LanyardData);
          this.stepArr = stepArr;
          this.copyNeedNextStepObj = deepClone(this.needNextStepObj);
          //初始化选中参数列表
          this.setParamsArr();
          //根据颜色设置方位信息
          this.setAreaByColor();
          this.orderDetailStep = stepArr[stepArr.length - 1].sortNum + 1;
          this.$store.commit("halfDesign/setLastStep", this.orderDetailStep - 1);
          this.categoryId = res.data.categoryId;
          this.productId = res.data.id;
          this.$nextTick(() => {
            resolve(res.data);
          });
        });
      });
    },
    oneSelected(data) {
      this.selectOne = data;
      if (data == 2) {
        this.$Bus.$emit("selectDefaultSizeStep", 20, true);
        //选择20只笔的时候，需要控制笔的颜色
        this.$Bus.$emit("setActiveProductColor");
      }
      if (this.needOpenInquiryBoxVisible) this.inquiryBoxVisible = true;
      this.needOpenInquiryBoxVisible = false;
    },
    getMobileShape() {
      this.mobileWidth = document.querySelector("#pdt").offsetWidth;
      this.mobileHeight = this.mobileWidth;
      let imgWidth = this.halfCateDTO.imgWidth;
      let imgHeight = this.halfCateDTO.imgHeight;
      if (!imgWidth || !imgHeight) {
        return;
      }
      this.mobileHeight = parseInt((imgHeight / imgWidth) * this.mobileWidth);
    },
    async getUserCoupon() {
      try {
        const res = await getUserCouponList({ type: 1, cash: 0 });
        let couponData = res.data.find((item) => {
          return item.couponType == 4;
        });
        if (!couponData) {
          this.hasOneDollarPens = false;
          return;
        } else {
          if (couponData.isNewCoupon) {
            this.newOneDollarPens = true;
            if (this.productInfo.isActivity) {
				if(this.isPens){
					this.$Bus.$emit("selectDefaultSizeStep", 20, true, false);
					//选择20只笔的时候，需要控制笔的颜色
					this.$Bus.$emit("setActiveProductColor");
				}else if(this.isLuggagetags){
					this.$Bus.$emit("selectDefaultSizeStep", 2, true, false);
				}
                this.$store.commit("halfDesign/setDisdountSelect", true);
            }
          }
          this.hasOneDollarPens = true;
        }
        if (this.newOneDollarPens) return;
        if (this.productInfo.isActivity && this.hasOneDollarPens) {
          if (this.type === "dialog") {
            this.oneDialog2 = true;
          } else {
            this.oneDialog = true;
          }
        }
      } catch (error) {}
    },
    setPdtLeft(offset = 10) {
      try {
        if (this.type === "dialog") {
          if (this.isMedals && this.isMobile) {
            let pdtLeft = document.querySelector(".pdt-left");
            pdtLeft.style.top = this.headerHeight + offset + "px";
          }
          return;
        }
        let header = document.querySelector("#modalHeader");
        let pdtLeft = document.querySelector(".pdt-left");
        pdtLeft.style.top = header.offsetHeight + offset + "px";
      } catch (error) {}
    },
    openInquiryBox() {
      if (!this.checkParams(this.paramsArr)) {
        return;
      }
      this.needOpenInquiryBoxVisible = true;
      if (
        !this.newOneDollarPens &&
        !this.selectOne &&
        this.productInfo.isActivity &&
        this.hasOneDollarPens
      ) {
        if (this.type === "dialog") {
          if (this.oneDialog2 == true) {
            this.$toast.info("Please select a coupon option");
          }
          this.oneDialog2 = true;
        } else {
          this.oneDialog = true;
        }
        return;
      }
      this.inquiryBoxVisible = true;
    },
    init() {
      /* 监听color状态 */
      this.getCanvasClear();
      /* 监听color状态end */
    },
    /**
     * 加载输入框
     * 兄弟组件在 components/HalfDesign/emailLater/Style1.vue 目录下
     */
    getCanvasClear() {
      this.$Bus.$off("canvasClear");
      this.$Bus.$on("canvasClear", () => {
        //删除左侧canvas里面的 文字和图片.
        canvas.clearTextAndImg();

        //删除输入框和图片上传
        this.$store.commit("halfDesign/setUploadImgList", []);
        this.$store.commit("halfDesign/resetTextArr");
      });
    },
    stepSub(needAdd) {
      this.stepArr.forEach((item) => {
        item.attributeCategoryList.forEach((citem, index) => {
          //判断area 步骤下面是否有打印颜色，
          //没有的话就将下面的步骤的小标题的step减1 -- 暂时用于pens
          // 另加 根据印刷方式上的isMarkup 字段来判断是否展示printColor   打印颜色是否加价：0.否  1.是
          if (citem.attributeFlag == "area" && citem.minStepTitle) {
            //获取当前area 的step
            let areaIndex = citem.minStepTitle.lastIndexOf("-") + 1;
            let areaNum = +citem.minStepTitle.substring(areaIndex);
            if (needAdd) {
              //不管有没有打印颜色，都加1回来
              let addNum = 1;
              for (let i = index + 1; i < item.attributeCategoryList.length; i++) {
                let temp = item.attributeCategoryList[i];
                if (temp.minStepTitle) {
                  let tempIndex = temp.minStepTitle.lastIndexOf("-") + 1;
                  temp.minStepTitle =
                    temp.minStepTitle.slice(0, tempIndex) + (areaNum + addNum) + "";
                  addNum++;
                }
              }
              return;
            }
            //判断是否有打印颜色步骤
            let hasProductColor = item.attributeCategoryList.some(
              (stepItem) => stepItem.attributeFlag == "printColor"
            );

            if (citem.minStepTitle && (!hasProductColor || !needAdd)) {
              for (let i = index + 1; i < item.attributeCategoryList.length; i++) {
                let temp = item.attributeCategoryList[i];
                if (temp.minStepTitle) {
                  let tempIndex = temp.minStepTitle.lastIndexOf("-") + 1;
                  let tempNum = +temp.minStepTitle.substring(tempIndex);
                  if (tempNum - areaNum == 1 && temp.attributeFlag != "printColor") {
                    break;
                  }
                  temp.minStepTitle =
                    temp.minStepTitle.slice(0, tempIndex) + (tempNum - 1) + "";
                }
              }
            }
          }
        });
      });
    },
    showPriceTableFn(data) {
      this.showPriceTable = data;
      if (this.showPriceTable) {
        this.$nextTick(() => {
          let size = this.findParams(this.stepArr, "attributeFlag", "size");
          //传递给 medals 打印方式 size参数 设置尺寸数量价格关系表
          this.$Bus.$emit("selectShowPrintStep", size);
        });
      }
    },
    selectLanyardsTypeFn(data, id) {
      //medals 织带步骤第一步
      this.selectLanyardsType = data;
      let findParamsIndex = this.paramsArr.findIndex((item) => {
        return item.id === +id;
      });
      if (findParamsIndex < 0) {
        return;
      }
      let params = this.paramsArr[findParamsIndex];
      if (+data == 3) {
        this.needNextStep = this.needNextStep.filter((item) => item != params.parentId);
      } else {
        if (params.isNextStep) {
          this.needNextStep.push(params.parentId);
        }
      }
    },
    selectRadioFn(data, boolean) {
      //medals 织带步骤第一步（2） 第三步的选择值
      this.selectRidio = data;
      this.LanyardColorFile = boolean;
      if (this.LanyardColorErrId) {
        this.toggleTips(this.LanyardColorErrId, 0);
        this.LanyardColorErrId = 0;
      }
    },
    setNowTextData(data) {
      let nowTextData = this.areaUploadList[this.nowAreaName]?.textArr || [];
      for (let index = 0; index < nowTextData.length; index++) {
        let item = nowTextData[index];
        if (item.id === data.id) {
          for (let key in data) {
            if (item.hasOwnProperty(key)) {
              item[key] = data[key];
            }
          }
          item.defaultText = data.text;
          break;
        }
      }
    },
    reorganizeDOMStructure(needAddBorderId) {
      let addedDom = document.querySelector(".add_border_box");
      if (addedDom) return;
      const areaElement = needAddBorderId.find((item) => item.index === 0);
      if (!areaElement) return;
      const areaDOM = document.getElementById(areaElement.id);
      if (!areaDOM) return;
      const newContainer = document.createElement("div");
      newContainer.className = "add_border_box";
      newContainer.style.padding = "14px 10px 4px";
      newContainer.style.border = "1px solid #DFE5EB";
      newContainer.style.borderRadius = "6px";
      newContainer.style.marginBottom = "16px";
      [1, 2, 3].forEach((index) => {
        const item = needAddBorderId.find((item) => item.index === index);
        if (item) {
          const element = document.getElementById(item.id);
          if (element) {
            newContainer.appendChild(element);
          }
        }
      });
      if (newContainer.children.length > 0) {
        areaDOM.parentNode.insertBefore(newContainer, areaDOM.nextSibling);
      }
    },
  },
  async fetch() {
    if (this.$store.getters.isManage) {
      return false;
    }
    let result = await getProductInfoAndParamRelation({
      routingName: this.routingName,
      userId: this.isLogin ? this.userId : null,
      productParamId: this.$route.query.id,
    });
    this.productInfo = result.data || {};
		//判断网站是否是行李牌
    this.isLuggagetags = this.productInfo.categoryId == 275;
    this.$store.commit("halfDesign/setLuggagetags", this.isLuggagetags);
  },
  async mounted() {
    if (this.$store.getters.isManage) {
      return false;
    }
    this.debounceCalculatePrice = debounce(this.calculatePrice, 300);
    this.$store.dispatch("getColorCode");
    this.init();
    this.$store.commit("halfDesign/setTile", false);
    //medals emailLater 初始赋值为false
    this.$store.commit("halfDesign/setMedalsLaterUpload", false);
    let header = document.querySelector("#modalHeader");
    this.headerHeight =
      typeof header?.offsetHeight === "number" ? header.offsetHeight : 0;
    let pdtSubDom = document.querySelector(".pdt-sub");
    this.pdtSub =
      typeof pdtSubDom?.offsetHeight === "number" ? pdtSubDom.offsetHeight : 0;
    if (this.isDialog) {
      this.headerHeight = 0;
    }
    this.$gl.show();
    //获取步骤参数信息
    await this.getAllParams();
    //获取价格分层
    getPriceLayered({
      cateId: this.productInfo.categoryId,
    }).then((priceResult) => {
      this.customPriceData = priceResult.data;
    });
    //设置pdt-left top
    this.setPdtLeft();
    if (this.isMobile) {
      this.getMobileShape();
    }
    //初始化画布
    let fabricStage = await this.initFabricStage("fabricCanvas");
    this.fabricStage = fabricStage;
    canvas.c = fabricStage;
    //size设置库存数量
    if (this.isColorStock) {
      this.setStockByColorOrSize(null, "size");
    } else {
      this.setStockByColorOrSize();
    }
    this.$Bus.$on("priceSection", (data) => {
      this.discountPriceArr = data;
    });
    this.$Bus.$on("noStock", () => {
      this.noAddCart = true;
      setTimeout(() => {
        this.priceInfo = {
          discount: 1,
        };
        this.teaDatas = {};
        this.noAddCart = false;
      }, 1200);
    });
    //监听页面滚动事件
    if (this.type !== "dialog") {
      window.onscroll = this.handlerScroll;
    } else {
      if (this.isMedals) {
        let halfDetail = document.querySelector(".halfDetail");
        if (halfDetail) {
          halfDetail.addEventListener("scroll", () => {
            if (this.isMobile) {
              if (this.showNextStepDialog) {
                this.setPdtLeft(
                  -this.headerHeight + 20 - halfDetail.scrollTop + this.nowScrollY
                );
              }
            }
          });
        }
      }
    }
    this.reorganizeDOMStructure(this.needAddBorderId);
    this.$gl.hide();
  },
  watch: {
    colorCode(newVal) {
      if (!this.fabricStage) {
        return;
      }
      if (newVal) {
        let textArr = this.fabricStage.getObjects("i-text");
        if (textArr.length != 0) {
          textArr.forEach((item) => {
            if (this.isWhite(newVal)) {
              item.set("fill", "#000000");
            } else {
              item.set("fill", "#ffffff");
            }
          });
          this.fabricStage.requestRenderAll();
        }
      }
    },
    isLogin() {
      this.getUserCoupon();
    },
    editStatus(newVal) {
      if (this.isMobile) {
        this.useMobileStyle = newVal;
      }
    },
  },
  beforeDestroy() {
    window.onscroll = null;
    this.$Bus.$off("priceSection");
    this.$Bus.$off("noStock");
    this.$Bus.$off("canvasClear");
  },
};
</script>

<style>
.border_top_top {
  padding-top: 10px !important;
  border-top: 2px solid #dfe5eb !important;
  border-radius: 6px 6px 0 0;
}

.border_middle_middle {
  padding: 0 10px;
  border: 2px solid #dfe5eb;
  border-top: none;
  border-bottom: none;
}

.border_bottom_bottom {
  padding-bottom: 10px !important;
  border-bottom: 2px solid #dfe5eb !important;
  border-radius: 0 0 6px 6px;
}
</style>
<style scoped lang="scss">
@import "~assets/css/half.scss";

.halfDetail {
  ::v-deep .v-rating--dense .v-icon {
    padding: 0;
  }

  & > .back {
    display: none;
    position: absolute;
    left: 10px;
    top: 10px;
    cursor: pointer;
  }

  .pdt {
    grid-gap: 5%;
    display: grid;
    overflow: visible;
    align-items: flex-start;
    grid-template-columns: 1fr 1fr;

    .pdt-left {
      min-width: 500px;
      position: sticky;
      z-index: 1;
      top: 10px;

      .close-icon {
        display: none;
        width: 100%;
        height: 2.4em;
        font-size: 12px;
        aspect-ratio: 1/1;
        align-items: center;
        justify-content: space-between;
        padding-right: 1em;
        border-radius: 6px 6px 0 0;
        z-index: 10;
        cursor: pointer;
        overflow: hidden;
      }
    }

    .pdt-right {
      min-width: 0;
      position: relative;

      ::v-deep {
        .v-alert {
          border: none !important;
          font-size: 12px;
          padding: 4px;
          margin-bottom: 0px;

          .v-icon {
            font-size: 16px;
            margin-right: 4px;
          }
        }

        .errorStep {
          .step-item {
            --error-color: #ff0000;
            border-color: var(--error-color);
          }
        }
      }
    }

    @include respond-to(mb) {
      grid-template-columns: 1fr;
      grid-gap: 20px;
      padding: 15px 0;

      .pdt-left {
        min-width: 300px;
        position: static;

        &.isSticky {
          position: sticky;
          top: 0;
          background-color: #fff;
          z-index: 2;
          margin: 0 -1em;
          padding: 0 1em;
        }

        &.isFixed {
          position: sticky;
          top: 0;
          background: #fff;
          z-index: 100002;
          border-radius: 6px 6px 0 0;

          &::after {
            content: "";
            position: absolute;
            bottom: -40px;
            left: 0;
            width: 100%;
            height: 40px;
            background-color: #fff;
            border: none;
          }

          .close-icon {
            display: flex;
          }

          .swiper-container-wrap {
            width: 100%;

            ::v-deep .borderBox {
              display: none;
            }
          }
        }
      }
    }
  }

  #tool-option {
    position: fixed;
    background: #fff;
    font-size: 14px;
    z-index: 1;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
    border: 1px solid #ccc;
    border-radius: 4px;
    user-select: none;

    .close {
      position: absolute;
      top: 0;
      right: 0;
      transform: translate(50%, -50%);
    }

    .text-tool {
      width: 300px;

      .text-tool-top {
        display: grid;
        grid-template-columns: 1fr 25px;
        grid-gap: 20px;
        align-items: center;
        padding: 0.5rem 0.5rem 0.25rem;
      }

      .text-tool-bottom {
        display: grid;
        grid-template-columns: 1fr 40px 40px;
        align-items: center;
        grid-gap: 20px;
        padding: 0.25rem 0.5rem 0.5rem;

        .font-bold,
        .font-style {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 40px;
          height: 40px;
          background: #fff;
          border: 1px solid rgba(0, 0, 0, 0.38);
          border-radius: 5px;
          margin-right: 10px;
          cursor: pointer;
          transition-duration: 0.15s;
          transition-property: color;
          transition-timing-function: cubic-bezier(0.25, 0.8, 0.25, 1);

          &:hover {
            border-color: #333333;
          }

          &.active {
            border-color: $color-primary;
          }
        }
      }
    }

    .img-tool {
      display: grid;
      grid-template-columns: 100px;

      .group {
        position: relative;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        cursor: pointer;

        input[type="file"] {
          position: absolute;
          top: 0;
          left: 0;
          z-index: -1;
          opacity: 0;
          height: 0;
        }

        .iconWrap {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 40px;
          height: 40px;
          padding: 5px;
        }
      }

      .group1 {
        border-right: 1px solid #cccccc;
      }
    }
  }
}

.halfDetail.isDialog ::v-deep {
  padding: 10px 10px 100px !important;

  .half-container {
    margin-bottom: 2em !important;
  }

  & > .back {
    display: block;
  }

  .pdt-sub {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
  }

  .myOneDialog2 {
    bottom: 0;
    left: 0;
    position: absolute;
    right: 0;
  }

  @include respond-to(mb) {
    padding: 10px !important;

    & > .back {
      display: none;
    }

    .pdtToolBar {
      padding-right: 20px;
    }

    .pdt-toolBar-left .back {
      display: block !important;
    }

    .half-container {
      margin-bottom: 1em !important;
      padding: 0 !important;
    }
  }
}

.half-container {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
  position: static;
  margin-bottom: 4em;

  @include respond-to(mb) {
    margin-bottom: 2em;
  }
}

.nextStepDialog {
  width: 100%;
  height: 100%;
  z-index: 100001;
}
::v-deep .addCartDialog {
  background-color: rgba(0, 0, 0, 0.7) !important;
  .base-dialog-model-con {
    background: transparent !important;
    box-shadow: none !important;
  }
  .addToCartCloseIcon.icon-guanbi {
    position: absolute !important;
    top: 1em !important;
    right: -3em !important;
    color: #fff !important;
    font-size: 16px !important;
    background: transparent !important;
    font-weight: 400 !important;
  }
  .selectCartBox {
    position: relative;
    img {
      width: 100%;
      object-fit: contain;
    }

    .cardBtnBox {
      width: 100%;
      height: 100%;
      position: absolute;
      left: 50%;
      bottom: 0;
      transform: translateX(-50%);
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      .successAddIcon {
        margin-top: 3.2%;
        font-size: 80px;
        color: #3ba43b;
      }
      .addCartSuceessText {
        margin-top: 4%;
        margin-bottom: 6%;
        font-weight: 700;
        font-size: 18px;
      }
      .cartBtns {
        width: 100%;
        margin-bottom: auto;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 2%;
        font-weight: 700;
		&.luggagetags{
			.goToCartBtn{
			    background: linear-gradient(90deg, #f8430d, #fea33a) !important;
			}
		}
        .checkOutBtn {
          font-size: 16px;
          color: $color-primary;
          border: 1px solid $color-primary;
          border-radius: 6px;
          padding: 0.6em 3em;
          cursor: pointer;
        }
        .goToCartBtn {
          font-size: 16px;
          color: #fff;
          background: $color-primary;
          border: 1px solid $color-primary;
          border-radius: 6px;
          padding: 0.6em 3em;
          cursor: pointer;
        }
      }
    }
  }
  @include respond-to(mb) {
    .addToCartCloseIcon.icon-guanbi {
      right: 1em !important;
    }
    .selectCartBox {
      .cardBtnBox {
        .successAddIcon {
          margin-top: 3.8%;
          font-size: 50px;
        }
        .addCartSuceessText {
          margin-top: 2%;
          margin-bottom: 5%;
          font-size: 14px;
        }
        .cartBtns {
          gap: initial;
          .checkOutBtn {
            font-size: 12px;
            border-radius: 6px 0 0 6px;
            padding: 0.6em 1.5em;
            border-right: none;
          }
          .goToCartBtn {
            font-size: 12px;
            border-radius: 0 6px 6px 0;
            padding: 0.6em 1.5em;
          }
        }
      }
    }
  }
}

[theme] h2 {
  all: unset;
}

[theme="10"] .halfDetail {
  padding-top: 7.5vmax !important;
}

[theme="10"] ::v-deep {
  &[noDebounce] {
    background: white
      url(https://oss-static-cn.liyi.co/web/quoteManage/20230921/%E7%BB%84_10_20230921sy5TAX.png)
      top no-repeat;
    background-size: 100% auto;
  }

  .oldPrice {
    display: none;
  }

  h2 {
    all: unset;

    &::before {
      display: none !important;
    }
  }
}

[theme="11"] ::v-deep {
  &[noDebounce] {
    background: white
      url(https://oss-static-cn.liyi.co/web/quoteManage/20231117/%E5%BA%95%E5%9B%BE_2051SbWBS5.png)
      top no-repeat;
    background-size: 100% 100%;

    @include respond-to(mb) {
      background-image: none;
    }
  }

  .oldPrice {
    font-size: 16px !important;
  }

  .step-title h2 {
    align-items: flex-start !important;
  }

  h2 {
    all: unset;

    &::before {
      display: none !important;
    }
  }
}
</style>
