<template>
	<div class="drawerContent templateCategoryContent">
		<topBar :title="templateCateName" :show-back-icon="showTemplateBackIcon" @back="templateCategoryBack" @close="closeDraw"></topBar>
		<div style="padding: 10px">
			<search-input v-model="searchTemplateValue" :extend-list="templateState ? templateRecordList : templateExtendList" @input="inputTemplateValue" @search="searchTemplate" @focus="templateExtend"></search-input>
		</div>
		<div class="categoryWrap" v-show="templateStatus === 1 || templateStatus === 2">
			<div class="main">
				<div class="box-item" :class="['box-item-style' + templateDisplayControl]" @click="selectTemplateCategory({ id: null, name: lang.all })">
					<div class="imgWrap">
						<b class="icon-a-T-Allzhuanhuan"></b>
					</div>
					<span class="item-name">{{ lang.all }}</span>
				</div>
				<div class="box-item" :class="['box-item-style' + item.displayControl]" v-for="item in templateCategoryList" :key="item.id" @click="selectTemplateCategory(item)">
					<div class="imgWrap">
						<img v-if="item.displayControl === 1" loading="lazy" :src="item.icon" :alt="item.templateTypeName" />
						<img v-if="item.displayControl === 2" loading="lazy" :src="item.displayDrawing" :alt="item.templateTypeName" />
					</div>
					<span class="item-name">{{ item.templateTypeName }}</span>
				</div>
			</div>
		</div>
		<!-- 模板列表-->
		<div class="templateWrap" v-show="templateStatus === 3" v-loadmore="loadTemplate" :infinite-scroll-disabled="disabledLoadTemplate">
			<div class="main" :class="{ noData: !templateList.length }">
				<div class="list-item" v-for="item in templateList" :key="item.id">
					<templates :item="item" :templateImg="item.templateImg" :isFavourite="item.isFavourite" @selectTemplate="selectTemplate" @toggleLoveTemplate="toggleLoveTemplate"> </templates>
				</div>
				<noResult v-if="!templateList.length && !loadingTemplate"></noResult>
			</div>
			<div class="loadMore" v-show="loadingTemplate">{{ lang.loading }}...</div>
		</div>
	</div>
</template>
<script>
import templates from "@/components/MyDzxt/templates.vue";
import searchInput from "@/components/MyDzxt/searchInput.vue";
import { addTemplateHistory, favoriteTemplate, getAllTemplateNameAndTypeName, getTemplateById, getTemplateHistory, getTemplateListFormApp, getTemplateTypeListFromApp } from "@/api/newDzxt";
import topBar from "@/components/MyDzxt/topBar.vue";
import dzMixin from "@/mixins/dzMixin";
import noResult from "@/components/MyDzxt/noResult.vue";
export default {
	mixins: [dzMixin],
	components: {
		noResult,
		topBar,
		searchInput,
		templates,
	},
	data() {
		const lang = this.$store.getters.lang?.design;
		return {
            selectNumber: 1,
			lang: lang,
			templateTagList: [],
			searchTemplateValue: "",
			templateCateId: null,
			templateCategoryDrawer: false,
			status: -1,
			list: [],
			show: false,
			showTips: false,
			templateCategoryList: [],
			templateCategoryListCopy: [],
			templateDisplayControl: 0,
			templateExtendList: [],
			templateRecordList:[],
			templateState:false,
			templateCateName: lang.templateCateName,
			templateStatus: 1,
			loadingTemplate: false,
			templateForm: {
				page: 1,
				pageSize: 60,
				templateTypeId: null,
				total: 0,
				pages: 1,
				searchWord: "",
			},
			templateList: [],
		};
	},
	computed: {
		noMoreTemplate() {
			return this.templateForm.page >= this.templateForm.pages;
		},
		disabledLoadTemplate() {
			return this.loadingTemplate || this.noMoreTemplate;
		},
		isLogin() {
			return this.$store.getters.isLogin;
		},
		userId() {
			return this.$store.state.userInfo?.id;
		},
		showTemplateBackIcon() {
			let obj = {
				1: false,
				2: true,
				3: true,
			};
			return obj[this.templateStatus];
		},
	},
	methods: {
		closeDraw() {
			this.$emit("closeDraw");
			this.templateCateId = null;
			this.templateStatus = 1;
			this.templateForm.searchWord = "";
			this.searchTemplateValue = "";
			this.templateCateName = this.lang.templateCateName;
			this.templateCategoryList = this.templateCategoryListCopy;
		},
		loadTemplate() {
			this.templateForm.page++;
			this.getTemplateList("scroll");
		},
		saveHistory(id) {
			return new Promise((resolve) => {
				addTemplateHistory({
					userId: this.userId,
					templateId: id,
					categoryId: this.$store.state.design?.pageInfo?.id,
				}).then(() => {
					resolve();
				});
			});
		},
		selectTemplateHistory(item) {
			this.selectTemplate({
				id: item.templateId,
				isHistory: true,
			});
		},
		getTemplateHistory() {
			getTemplateHistory({
				userId: this.userId,
			}).then((res) => {
				this.recentTemplateList = res.data;
			});
		},
		async selectTemplate(item) {
			this.$store.commit("design/set_loading", true);
			this.canvas.templateName = item.templateName;
			let fn = () => {
				if (!this.isLogin || item.isHistory) {
					return false;
				}
				this.saveHistory(item.id).then(() => {
					this.getTemplateHistory();
				});
			};
			let loadData = () => {
				this.closeDraw();
				fn();
				getTemplateById({
					id: item.id,
				})
					.then(async (res) => {
						//判断是否是光板
						if (res.data.isPaintless === 1) {
							await this.canvas.replaceBase(res.data.templateFile);
							this.canvas.c.requestRenderAll();
						} else {
							if(this.canvas.isSticker){
								let specification = JSON.parse(res.data.specification)
								this.canvas.stickerSize = {
									w:specification.width,
									h:specification.height
								}
							}
							await this.canvas.loadTemplate(res.data.templateFile);
						}
						this.$store.commit("design/set_loading", false);
					})
					.catch((err) => {
						this.$store.commit("design/set_loading", false);
					});
			};
            if (this.selectNumber === 1) {
                loadData();
                this.selectNumber++;
                return;
            }
			if (this.canvas.isEmptyCanvas()) {
				loadData();
			} else {
				this.$confirm(this.lang.areTemplate, this.lang.hint, {
					confirmButtonText: this.lang.comfirm,
					cancelButtonText: this.lang.cancel,
					type: this.lang.warning,
				})
					.then(() => {
						loadData();
					})
					.catch(() => {});
			}
		},
		toggleLoveTemplate(item) {
			if (!this.isLogin) {
				this.$store.commit("setLogin", "login");
				return false;
			}
			favoriteTemplate({
				categoryId: this.$store.state.design?.pageInfo?.id,
				templatesId: item.id,
			}).then(() => {
				item.isFavourite = item.isFavourite ? false : true;
			});
		},
		templateCategoryBack() {
			this.templateStatus = 1;
			this.templateCateName = this.lang.templateCateName;
			this.templateCategoryList = this.templateCategoryListCopy;
		},
		inputTemplateValue(val) {
			if (val && this.templateTagList.length > 0) {
				if (val.trim().length == 1) {
					this.templateExtend(true);
					return;
				}
				const regex = new RegExp(`^${val}`, "gi");
				this.templateExtendList = this.templateTagList
					.filter((item) => item.match(regex))
					.map((item) => {
						return {
							value: item,
							icon: false,
						};
					});
				this.templateState = false;
			} else {
				this.templateExtend(true);
				this.searchTemplate(val);
				this.templateExtendList = [];
			}
		},
		searchTemplate(val) {
			this.setLocalStorage("templateRecord", val);
			this.templateCateId = "";
			this.templateList = [];
			this.templateForm.page = 1;
			this.templateForm.templateTypeId = null;
			this.templateForm.searchWord = val;
			this.templateStatus = 3;
			this.templateCateName = this.lang.templateCateName;
			this.getTemplateList();
		},
		getTemplateList(type = "select") {
			this.loadingTemplate = true;
			getTemplateListFormApp(
				Object.assign({}, this.templateForm, {
					categoryId: this.$store.state.design?.pageInfo?.id,
					quoteName:this.$store.state.design?.pageInfo?.quoteCateName,
					userId: this.userId,
				})
			)
				.then((res) => {
					if (res.data) {
						if (type === "scroll") {
							this.templateList = this.templateList.concat(res.data.records);
							this.templateForm.total = res.data.total;
							this.templateForm.pages = res.data.pages;
						} else {
							this.templateList = res.data.records;
							this.templateForm.total = res.data.total;
							this.templateForm.pages = res.data.pages;
						}
					} else {
						this.templateList = [];
						this.templateForm.total = 0;
						this.templateForm.pages = 0;
					}
				})
				.finally(() => {
					this.loadingTemplate = false;
				});
		},
		selectTemplateCategory(item) {
			this.searchTemplateValue = "";
			this.templateExtendList = [];
			this.templateCateId = item.id;
			this.templateCateName = item.templateTypeName || this.lang.allTemplates;
			if (item.children && item.children.length) {
				this.templateStatus = 2;
				this.templateCategoryList = item.children;
			} else {
				this.templateStatus = 3;
				this.templateList = [];
				this.templateForm.page = 1;
				this.templateForm.templateTypeId = item.id;
				this.templateForm.searchWord = "";
				this.getTemplateList();
				//获取模板列表
			}
		},
		getTemplateTypeListFromApp() {
			return new Promise(resolve => {
                getTemplateTypeListFromApp({
                    categoryId: this.$store.state.design?.pageInfo?.id,
                }).then((res) => {
                    let list = res.data;
                    this.templateCategoryList = this.templateCategoryListCopy = list;
                    this.templateDisplayControl = list.length > 0 && list[0].displayControl;
                    resolve(list);
                });
            })
		},
	},
	mounted() {
		this.getTemplateTypeListFromApp().then(list=>{
            if(this.$route.query.cid){
                let findCate = list.find(item=>{
                    return item.id == this.$route.query.cid
                })
                findCate && this.selectTemplateCategory(findCate);
            }
        });
		getAllTemplateNameAndTypeName({
			categoryId: this.$store.state.design?.pageInfo?.id,
		}).then((res) => {
			this.templateTagList = res.data;
		});
	},
};
</script>

<style scoped lang="scss">
.templateCategoryContent {
	display: flex;
	flex-direction: column;
	height: 100%;

	.topBar {
		border-bottom: none;
	}

	.header {
		position: relative;
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 1rem 10px;
		font-weight: 700;

		.close-icon {
			position: absolute;
			right: 10px;
			top: 50%;
			transform: translateY(-50%);
		}

		b {
			font-weight: 400;
		}
	}

	.header.twoIcon {
		.close-icon {
			position: absolute;
			left: 1rem;
			right: auto;
			top: 50%;
			transform: translateY(-50%);
		}

		.yes-icon {
			position: absolute;
			right: 1rem;
			top: 50%;
			transform: translateY(-50%);
		}
	}

	.categoryWrap {
		overflow: hidden auto;
		flex: 1;
		height: 0;

		.main {
			display: grid;
			align-content: flex-start;
			justify-content: center;
			grid-template-columns: repeat(3, 1fr);
			grid-column-gap: 10px;
			grid-row-gap: 10px;
			padding: 10px;

			.box-item {
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				background: #f5f5f5;
				border-radius: 5px;

				.imgWrap {
					display: flex;
					justify-content: center;
					align-items: center;
					width: 2.8rem;
					height: 2.8rem;
					background: linear-gradient(0deg, #f8fcff 0%, #eff7ff 100%);
					border-radius: 20px;

					img {
						width: 2rem;
						height: 2rem;
						object-fit: contain;
					}
				}

				b {
					color: #2a96fa;
					font-size: 17px;
				}

				.item-name {
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
					max-width: 100px;
					margin-top: 8px;
					font-size: 12px;
				}
			}

			.box-item.box-item-style0 {
				aspect-ratio: 156/60;

				b {
					display: none;
				}

				.imgWrap {
					display: none;
				}

				.item-name {
					margin-top: 0;
				}
			}
		}
	}

	.templateWrap {
		overflow: hidden auto;
		flex: 1;
		height: 0;
		padding: 0 12px;
		.main {
			display: grid;
			grid-template-columns: repeat(2, 1fr);
			grid-column-gap: 5px;
			grid-row-gap: 5px;
			align-content: flex-start;

			&.noData {
				display: flex;
				justify-content: center;
				align-items: center;
			}
		}
	}
}
</style>
