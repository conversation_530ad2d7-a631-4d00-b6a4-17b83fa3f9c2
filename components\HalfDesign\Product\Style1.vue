<template>
	<div class="mb-4" :class="stepData.styleName">
		<half-design-select-tip :customText="stepData.attributeTitle" :selectedValue="shape"></half-design-select-tip>
		<slot name="stepText"></slot>
		<div class="step-content">
			<div class="step-item" :class="{active:index===selectIndex}" v-for="(step,index) in stepData.productParamList"
					 :key="index"
					 @click="selectStep(step,index)">
				<div class="imgWrap" :class="{active:index===selectIndex}">
					<img :src="step.imgDetail" :alt="step.valueName" :title="step.valueName">
					<half-design-check-icon class="check-icon absolute-top-right"></half-design-check-icon>
				</div>
				<div class="d-flex-center text-center pa-1">
					<half-design-check-box class="check-box mr-1"></half-design-check-box>
					<div class="text-truncate">
						{{ step.valueName }}
					</div>
				</div>
			</div>
		</div>
		<div class="errorTip">
			<v-alert
				dense
				outlined
				type="error"
			>
				{{ langSemiCustom.errorTip }}
			</v-alert>
		</div>
	</div>
</template>
<script>
export default {
	props: {
		stepData: {
			type: Object,
			default: {}
		}
	},
	data() {
		return {
			selectIndex: -1,
			selectItem: null
		}
	},
	computed: {
		shape() {
			return this.selectItem?.valueName
		},
		langSemiCustom() {
			return this.$store.getters.lang?.semiCustom;
		}
	},
	methods: {
		selectStep(item, index) {
			this.selectIndex = index;
			this.selectItem = item;
			this.$emit('selectStep', {
				type: this.stepData.attributeFlag,
				data: item,
				parent: this.stepData,
				id: this.stepData.id
			})
		},
	},
	mounted() {

	}
}
</script>
<style scoped lang="scss">
@import "~assets/css/half.scss";

.step-content {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	grid-gap: 10px;

	.step-item {
		min-width: 0;
		cursor: pointer;
		@media (any-hover: hover) {
			&:hover {
				::v-deep .check-box {
					border-color: $color-primary;

					.check-box-inner {
						background-color: $color-primary;
					}
				}
			}
		}

		.imgWrap {
			position: relative;
			@include step-default;
			@include flex-center;
			height: 150px;
			background-color: $background-color;
		}
	}

	.step-item.active {
		::v-deep .check-box {
			border-color: $color-primary;

			.check-box-inner {
				background-color: $color-primary;
			}
		}
	}
}

.style1 .step-content {
	.check-icon {
		display: none;
	}
}

.style2 .step-content {
	.check-box {
		display: none;
	}

	.check-icon {
		display: none;
	}

	.step-item.active {
		.check-icon {
			display: flex;
		}
	}
}

@include respond-to(mb){
	.step-content {
		grid-gap: 5px;

		.step-item {
			.imgWrap {
				height: 110px;
			}
		}
	}
}
</style>
