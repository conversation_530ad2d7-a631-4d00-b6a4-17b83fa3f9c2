// 尺寸变量
$pad-width: 1400px;
$mb-width: 1000px;

// 通用变量
$color-yellow: #ff9000;
$color-green: #699f4c;
$color-blue: #0066CC;
$color-red: #FF2B2B;

$line-primary: #EBEBEB;
$line-second: #DDDDDD;

$bg-mask: var(--bg-mask);
$bg-shadow: var(--bg-shadow);

//报价主题色
$quote-color-primary: #D17448;

$color-primary: var(--color-primary);
$color-second: var(--color-second);
$color-bright: var(--color-bright);
$color-dark: var(--color-dark);
$btn-primary: var(--btn-primary);
$btn-second: var(--btn-second);

$bg-page: var(--bg-page);
$bg-primary: var(--bg-primary, #EBEBEB);
$bg-second: var(--bg-second, #F7F8FC);
$bg-dark: var(--bg-dark, #242D36);

$text-primary: var(--text-primary);
$text-family: var(--text-family, Roboto);

$radius-btn: var(--radius-btn, 0.5em);

//媒体查询写法简化
$breakpoints: (
	'mb':(320px, 1000px),
	'pad':(1001px, 1400px),
	'pc':1401px
);

@mixin respond-to($breakpoint) {
	$bp: map-get($breakpoints, $breakpoint);

	@if type-of($bp)=='list' {
		$min: nth($bp, 1);
		$max: nth($bp, 2);

		@media screen and (min-width: $min) and (max-width: $max) {
			@content;
		}
	}

	@else {
		@media screen and (min-width: $bp) {
			@content;
		}
	}
}