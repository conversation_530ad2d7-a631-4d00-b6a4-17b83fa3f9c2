<template>
	<div style="overflow:hidden auto" class="kuang">
		<v-list class="drawer_list">
			<v-list-item class="list_title">Edit Customer Details</v-list-item>
		</v-list>
		<v-form ref="form" class="form" v-model="editForm" :disabled="disabledBasicForm" :readonly="disabledBasicForm">
			<ul>
				<li><span class="avatar" style="color:#666666 ">Avatar</span>
					<v-input v-model="iconVal" @click="triggerUpload">
						<div class="uploadBox">
							<template v-if="basicForm.icon">
								<div class="imgWrap">
									<img height="60" width="60" :src="basicForm.icon" style="border-radius: 50%" />
									<v-btn icon small class="closeIcon" color="primary"
										@click.stop="basicForm.icon = ''; iconVal = ''">
										<v-icon>mdi-close</v-icon>
									</v-btn>
								</div>
							</template>
							<template v-else>
								<span class="jia">+</span>
								<span class="input_upload">Upload</span>
								<input type="file" ref="icon" accept="image/*" @change="uploadIcon">
							</template>
						</div>
					</v-input>
				</li>
				<li><span class="red--text">*</span>First Name
					<v-text-field :rules="rules.name" v-model="firstName" solo flat dense label="Enter First Name"
						single-line :outlined="!disabledBasicForm">
					</v-text-field>
				</li>
				<li><span class="red--text">*</span>Last Name
					<v-text-field :rules="rules.name" v-model="lastName" solo flat dense label="Enter Last Name"
						single-line :outlined="!disabledBasicForm">
					</v-text-field>
				</li>
			</ul>
			<ul class="form_ul">
				<li><span class="red--text">*</span>Email
					<v-text-field :rules="rules.email" v-model="email" solo flat dense label="Enter email Address"
						single-line :outlined="!disabledBasicForm">
					</v-text-field>
				</li>
				<li><span class="red--text">*</span>Password
					<v-text-field @click:append="passwordShow = !passwordShow" v-model="password"
						:type="passwordShow ? 'text' : 'password'"
						:append-icon="passwordShow ? 'mdi-eye' : 'mdi-eye-off'" :rules="rules.password"
						label="Enter Initial Password" single-line :outlined="!disabledBasicForm" dense solo flat>
					</v-text-field>
				</li>
				<li><span class="red--text">*</span>Confirm Password
					<v-text-field @click:append="conPasswordShow = !conPasswordShow" v-model="confirmPassword"
						:type="conPasswordShow ? 'text' : 'password'"
						:append-icon="conPasswordShow ? 'mdi-eye' : 'mdi-eye-off'" :rules="rules.confirmPassword"
						label="Enter Password" single-line :outlined="!disabledBasicForm" dense solo flat>
					</v-text-field>
				</li>
			</ul>
			<ul>
				<li class="form_li">Phone
					<v-text-field v-model="phone" label="Enter Phone Number" single-line :outlined="!disabledBasicForm"
						dense solo flat></v-text-field>
				</li>
				<li class="form_li">Location
					<v-text-field v-model="location" label="Enter Location" single-line :outlined="!disabledBasicForm"
						dense solo flat></v-text-field>
				</li>
				<li class="form_li">Job
					<v-autocomplete v-model="jobId" :items="itemsJob" item-text="name" item-value="id"
						:outlined="!disabledBasicForm" dense chips small-chips solo flat label="Please select">
					</v-autocomplete>
				</li>
			</ul>
			<ul>
				<li class="form_li">Customer Level
					<v-tooltip top color="#FFFFFF">
						<template v-slot:activator="{ on, attrs }">
							<span class="wenhao">
								<v-icon v-bind="attrs" v-on="on" style="font-size:14px">mdi-help-circle-outline</v-icon>
							</span>
						</template>
						<span style="color:black">The discount will be applied to all<br />
							the orders of the customer</span>
					</v-tooltip>
					<v-autocomplete v-model="isReputable" :items="customer" item-text="name" item-value="id"
						allow-overflowdisable-lookup :outlined="!disabledBasicForm" dense chips small-chips solo flat
						label="Plese select">
						<i class="el-icon-question"></i>
						<template v-slot:selection="data">
							<div :input-value="data.selected" close @click="data.select" class="cusSelection">
								<b v-if="isReputable === 2" class="icon-a-jxs-djszhuanhuan"></b>
								{{ data.item.name }}
							</div>
						</template>
					</v-autocomplete>
				</li>
				<li class="form_li">Discount
					<v-tooltip top color="#FFFFFF">
						<template v-slot:activator="{ on, attrs }">
							<span class="wenhao">
								<v-icon v-bind="attrs" v-on="on" style="font-size:14px">mdi-help-circle-outline</v-icon>
							</span>
						</template>
						<span style="color:black">The discount will be applied to all<br />
							the orders of the customer</span>
					</v-tooltip>
					<v-autocomplete v-model="retailerDiscount" :items="discountList" item-text="text" item-value="value"
						:outlined="!disabledBasicForm" dense chips small-chips solo flat label="Enter Discount">
						<template v-slot:item="{ item }">
							<div v-if="item.value === 'custom'" class="d-flex justify-space-between align-center">
								<div @click.stop>
									<v-text-field v-model="item.inputText">
										<template v-slot:append-outer>%OFF</template>
									</v-text-field>
								</div>
								<a href="javascript:;" style="margin-left: 20px;"
									@click="confirmDiscount($event, item)">ok</a>
							</div>
							<div v-else>
								{{ item.text }}
							</div>
						</template>
					</v-autocomplete>
				</li>
			</ul>
			<ul>
				<li class="remark">Remark
					<v-text-field v-model="customerNotes" dense solo flat label="Enter customer Remark..." single-line
						:outlined="!disabledBasicForm">
					</v-text-field>
				</li>
			</ul>
		</v-form>
		<v-simple-table class="list">
			<tr>
				<td style="padding-right:30px;">Spent:</td>
				<td style="width:130px">Order:</td>
				<td style="width:183px">Product Type:</td>
				<td style="width:206px">Registration Time:</td>
				<td style="border:0">Last login time:</td>
			</tr>
			<tr class="list_item">
				<td style="padding-right:30px;">{{ consumption }}</td>
				<td>{{ orderNum }}</td>
				<td>{{ productType }}</td>
				<td>{{ regTime }}</td>
				<td style="border:0">{{ lastLoginTime }}</td>
			</tr>
		</v-simple-table>
		<div class="btn_div">
			<template v-if="disabledBasicForm">
				<v-btn class="list_edit" @click="disabledBasicForm = false">
					<b class="icon-a-icon-editzhuanhuan"></b>
					Edit
				</v-btn>
			</template>
			<template class="drawer_btn" v-else>
				<v-btn class="drawer_btn1" @click="getEdit()">Save</v-btn>
				<v-btn class="drawer_btn2" @click="disabledBasicForm = true">Cancel</v-btn>
			</template>
		</div>
		<v-divider></v-divider>
		<div class="drawer_label">
			<div class="drawer_label_title">Order History</div>
			<v-data-table :headers="drawerHeaders" :items="drawerDesserts" hide-default-footer
				class="elevation-1 drawer_table">
				<template v-slot:header.id="{ header }">
					<div>
						<span>{{ header.text }}</span>
						<v-icon style="font-size:15px">mdi-help-circle-outline</v-icon>
					</div>
				</template>
				<template v-slot:header.discount="{ header }">
					<div>
						<span>{{ header.text }}</span>
						<v-icon style="font-size:15px">mdi-help-circle-outline</v-icon>
					</div>
				</template>
				<template v-slot:header.paymentStatus="{ header }">
					<div class="drawer_pay">
						<div>{{ header.text }}</div>
						<v-icon style="font-size:15px">mdi-help-circle-outline</v-icon>
					</div>
				</template>
				<template v-slot:header.orderStatus="{ header }">
					<div>
						<span>{{ header.text }}</span>
						<v-icon style="font-size:15px">mdi-help-circle-outline</v-icon>
					</div>
				</template>
				<template v-slot:item.id="{ item }">
					<div style="font-size:8px;width:180px">
						<span>OID:{{ item.oid }}</span>
						<span>PID:{{ item.userId }}</span>
					</div>
				</template>
				<template v-slot:item.createTime="{ item }">
					<div class="drawer_time">
						{{ item.createTime }}
					</div>
				</template>
				<template v-slot:item.productName="{ item }">
					<div style="font-size:12px">
						{{ item.productName }}
					</div>
				</template>
				<template v-slot:item.totalPrice="{ item }">
					<div style="font-size:12px;">
						${{ item.totalPrice.toFixed(2) }}
					</div>
				</template>
				<template v-slot:item.discount="{ item }">
					<div class="d-flex drawer_discount" style="font-size:12px;">
						<span>{{ (item.discount) * 100 }}%OFF</span>
					</div>
				</template>
				<template v-slot:item.paymentStatus="{ item }">
					<div class="drawer_payment" :style="{ color: getColorPayment(item.paymentStatus) }">
						{{ getPaymentText(item.paymentStatus) }}
					</div>
				</template>
				<template v-slot:item.orderStatus="{ item }">
					<div class="d-flex orderStatus">
						<span style="font-size:12px">{{ getOrderText(item.orderStatus) }}</span>
					</div>
				</template>
				<template v-slot:item.icon="{ item }">
					<div class="d-flex">
						<span style="color:#1A73E8 " @click="getOrderDetails(item)">>></span>
					</div>
				</template>
			</v-data-table>
			<div class="text-right mt-2" style="margin-right: 10px;" v-if="this.searchForm.pages > 1">
				<pagination :length="searchForm.pages" @changePages="changePages"></pagination>
			</div>
		</div>
	</div>
</template>
<script>
import { uploadFile } from "~/utils/oss";
import { getCustomersEdit, getUserOrder } from '@/api/manage/customers';
export default {
	props: {
		editList: {
			type: Object,
			default: () => { }
		}
	},
	data() {
		let isPassword = (value) => {
			let reg = /^(?=.*[0-9])(?=.*[a-zA-Z])(?=.*[0-9A-Za-z]).{6,30}$/;
			return reg.test(value);
		};
		let isConfirmPassword = (value) => {
			return this.password === value;
		};
		return {
			judgePW: '',
			isUpdating: false,
			editForm: false,
			iconVal: "",
			tableId: "",
			jobId: "",
			firstName: "",
			lastName: "",
			password: "",
			confirmPassword: "",
			email: "",
			phone: "",
			country: "",
			location: "",
			isReputable: "",
			retailerDiscount: 20,
			customerNotes: "",
			consumption: "",
			orderNum: "",
			productType: "",
			regTime: "",
			lastLoginTime: "",

			createTime: "",
			discount: "",
			id: "",
			orderStatus: "",
			paymentStatus: "",
			productName: "",
			totalPrice: "",

			searchForm: {
				dates: [],
				page: 1,
				pageSize: 5,
				status: 0,
				pages: 0,
				proId: this.$store.getters["manage/getProId"],
			},
			basicForm: {
				icon: '',
				webSiteUrl: 'https://www.appoto.com/',
				siteName: '',
				language: 1,
				delegated: 1,
				timeZone: '(GMT-08:00) Pacific Time'
			},
			disabledBasicForm: true,
			basicFormRules: {
				siteNameRules: [
					v => !!v || 'Site Name is required',
				],
				languageRules: [
					v => !!v || 'Site language is required',
				],
				timeZoneRules: [
					v => !!v || 'Time zone is required',
				]
			},
			drawerHeaders: [
				{ text: 'Order Id', value: 'id', align: 'start', sortable: false,},
				{ text: 'Time', value: 'createTime', align: 'center', sortable: false, },
				{ text: 'Products', value: 'productName', align: 'center', sortable: false,},
				{ text: 'Total', value: 'totalPrice', align: 'center', sortable: false, },
				{ text: 'Discount', value: 'discount', align: 'center', sortable: false, },
				{ text: 'Payment Status', value: 'paymentStatus', align: 'center', sortable: false,},
				{ text: 'Order Status', value: 'orderStatus', align: 'center', sortable: false, },
				{ text: '', value: 'icon', align: 'center', sortable: false,},
			],
			drawerDesserts: [],
			drawer: false,
			valid: true,
			rules: {
				notNullRules: [(v) => !!v || "password is required"],
				password: [(v) => isPassword(v) || "The password must contain at least six characters, including letters, digits, and special characters"],
				confirmPassword: [(v) => isConfirmPassword(v) || "The new password is inconsistent with the confirm password"],
				name: [v => !!v || 'Name is required'],
				email: [v => !!v || 'E-mail is required',
				v => /.+@.+\..+/.test(v) || 'E-mail must be valid'
				]
			},
			passwordShow: false,
			conPasswordShow: false,
			values: 20,
			itemsJob: [
				{
					name: 'Art design',
					id: 0,
				},
				{
					name: 'Accounting and finance',
					id: 1,
				},
				{
					name: 'Business / management',
					id: 2,
				},
				{
					name: 'Customer service',
					id: 3,
				},
				{
					name: 'Ecommeces / network',
					id: 4,
				},
				{
					name: 'Education and school',
					id: 5,
				},
				{
					name: 'Government',
					id: 6,
				},
				{
					name: 'Human resources',
					id: 7,
				},
				{
					name: 'Legal / paralegal',
					id: 8,
				},
				{
					name: 'Marketing / advertising agencies',
					id: 9,
				},
				{
					name: 'Medical / health',
					id: 10,
				},
				{
					name: 'Nonprofit organizations',
					id: 11,
				},
				{
					name: 'Police and security',
					id: 12,
				},
				{
					name: 'Real Estate',
					id: 13,
				},
				{
					name: 'Retail / wholesale',
					id: 14,
				},
				{
					name: 'School and Education',
					id: 15,
				},
				{
					name: 'Sports organizations',
					id: 16,
				},
				{
					name: 'Web / info design',
					id: 17,
				},
				{
					name: 'Manufacturing',
					id: 18,
				}
			],
			customer: [
				{
					name: 'VIP & Loyal Level',
					avatar: 'https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fpic.51yuansu.com%2Fpic3%2Fcover%2F03%2F16%2F31%2F5b5ad48f3f58a_610.jpg&refer=http%3A%2F%2Fpic.51yuansu.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=**********&t=5c9ba73dd78ec1b6162ad21742baeb9d',
					id: 2,
				},
				{
					name: 'General Level',
					id: 1,
				},
			],
			discountList: [
				{
					text: '',
					inputText: '',
					value: 'custom'
				},
				{
					text: '20 %OFF',
					value: 20
				},
				{
					text: '30 %OFF',
					value: 30
				},
				{
					text: '40 %OFF',
					value: 40
				},
				{
					text: '50 %OFF',
					value: 50
				}],
		}
	},
	computed: {
		 orderStatusList() {
			return this.$store.state.manage.paymentStatus
		},
	},
	watch: {
		editList(val) {
			// console.log(this.discountList.length);
			if (this.discountList.length > 5) this.discountList.pop()
			this.tableId = val.id;
			this.firstName = val.firstName;
			this.lastName = val.lastName;
			this.email = val.email;
			// this.password = val.password;
			// this.confirmPassword = val.password;
			this.phone = val.telephone;
			this.location = val.location;
			this.jobId = val.jobId;
			this.isReputable = val.isReputable;
			this.retailerDiscount = val.retailerDiscount;

			if (this.retailerDiscount != null) {
				this.discountList.push({
					text: `${(this.retailerDiscount*100).toFixed(0)} %OFF`,
					value: this.retailerDiscount
				})
			}

			this.basicForm.icon = val.picPath || '';
			this.consumption = val.consumption;
			this.orderNum = val.orderNum;
			this.productType = val.productType;
			this.regTime = val.regTime;
			this.customerNotes = val.customerNotes;
			this.lastLoginTime = val.lastLoginTime;
			this.judgePW = val.password
			if (this.basicForm.icon) {
				this.iconVal = "flies"
			}
			this.getEditAdd();
		}
	},
	methods: {
		getOrderText(status) {
			return this.$store.state.manage.orderStatus.find(iem => iem.status ===
				status)?.text
		},
		isPasswordM(value) {
			let reg = /^(?=.*[0-9])(?=.*[a-zA-Z])(?=.*[0-9A-Za-z]).{6,30}$/;
			return reg.test(value);
		},
		isConfirmPasswordM(value) {
			return this.password === value;
		},
		getOrderDetails(item) {
			this.$router.push({
				name: 'manage-orders-id',
				params: {
					id: item.id
				}
			})
		},

		getPaymentText(status) {
			let findItem = this.orderStatusList.find(item => item.status === status);
			return findItem?.text
		},

		// 编辑
		async getEdit() {
			if (!this.password) {
				this.rules.password = []
				this.rules.confirmPassword = []
			} else {
				this.rules.password = [(v) => this.isPasswordM(v) || "The password must contain at least six characters, including letters, digits, and special characters"]
				this.rules.confirmPassword = [(v) => this.isConfirmPasswordM(v) || "The new password is inconsistent with the confirm password"]
			}
			let valid = this.$refs['form'].validate();

			if (valid) {
				const params = {
					id: this.tableId,
					jobId: this.jobId,
					firstName: this.firstName,
					lastName: this.lastName,
					email: this.email,
					password: this.confirmPassword,
					telephone: this.phone,
					country: this.country.country,
					location: this.location,
					picPath: this.basicForm.icon,
					isReputable: this.isReputable,
					retailerDiscount: this.param,
					customerNotes: this.customerNotes,
				}
				let param;
				if(param === "custom"){
					let findItem = this.discountList.find(item => item.value === params.retailerDiscount)
					params.retailerDiscount = findItem.inputText / 100
				}else{
					params.retailerDiscount = this.retailerDiscount /100
				}
				if (this.judgePW == this.password) {
					params.password = null
				}
				else {
					params.password = this.password
				}
				await getCustomersEdit(params).then(res => {
					if (res.code === 200) {
						this.$message.success(res.message);
						this.basicForm.icon = '';
						this.$refs['form'].reset();
						this.$emit('editSaveCallBack');
						this.disabledBasicForm = true;
						console.log(this.retailerDiscount);
					} else {
						this.$message.error(res.message)
					}
				})
			}
		},

		//查询
		getEditAdd() {
			const params = {
				proId: this.$store.getters["manage/getProId"],
				page: this.searchForm.page,
				pageSize: this.searchForm.pageSize,
				userId: this.tableId,
			}
			getUserOrder(params).then(res => {
				this.drawerDesserts = res.data.orderList;
				this.searchForm.pages = res.data.pages;
				console.log(res)
			})
		},

		//分页
		changePages(val) {
			this.searchForm.page = val;
			this.getEditAdd();
		},

		getColorPayment(paymentStatus) {
			if (paymentStatus == 'Received Payment') return '#333333'
			else return 'red'
		},
		triggerUpload() {
			if (this.disabledBasicForm || this.basicForm.icon) {
				return false;
			}
			this.$refs.icon.click();
		},
		async uploadIcon() {
			const files = Array.from(this.$refs.icon.files);
			this.iconVal = 'file'
			this.basicForm.icon = await uploadFile(files[0]);
			this.$refs.icon.value = null;
		},
		confirmDiscount(e, item) {
			item.text = item.inputText + '%OFF';
			this.values = item.value;
		},
	},
}
</script>
<style lang="scss" scoped>
.list_x {
	display: flex;
	justify-content: flex-end;
	font-size: 20px;
	padding-right: 10px;
}

.cusSelection b {
	margin-right: 7px;
	color: #FBB204;
}

.v-avatar--left {
	height: 30px !important;
	min-width: 30px !important;
	width: 30px !important;
}

.v-autocomplete.v-select.v-input--is-focused input {
	min-width: 0px !important;
}

.v-application .elevation-1 {
	box-shadow: none !important;
}

.kuang {
	height: 90vh;
	position: fixed;
	width: 100%;
}

.avatar {
	color: black;
	position: relative;
	top: 45px;
}

.uploadBox {
	position: relative;
	left: 64px;
	height: 60px;
	width: 60px;
	background: #FFFFFF;
	border: 1px solid #E6E6E6;
	border-radius: 50%;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	transition: all .3s;
	cursor: pointer;

	.jia {
		font-size: 22px;
		margin: 6px auto;
		color: #B3B3B3;
		font-weight: 400;
	}

	.input_upload {
		color: #666666;
		font-size: 12px;
		position: absolute;
		top: 32px;
		left: 11px;
	}

	&.disabled:hover {
		border-color: #E6E6E6;
	}

	.imgWrap {
		display: flex;
		justify-content: center;
		align-items: center;
		width: 60px;
		height: 60px;
		border-radius: 50%;
		position: relative;
		background-color: #EEEEEE;

		.closeIcon {
			position: absolute;
			top: 0;
			right: 0;
			transform: translate(50%, -50%);
		}
	}

	&:hover {
		border-color: var(--v-primary-base);
	}

	input {
		opacity: 0;
		cursor: pointer;
		z-index: -1;
	}
}

.v-navigation-drawer {
	.drawer_label {
		.drawer_table {
			font-size: 12px;
			width: 96%;

			::v-deep {
				.v-data-table-header {
					background-color: #F5F6FA;
					height: 50px;

					tr th {
						color: #333333;
					}
				}
			}

			.drawer_pay {
				display: flex;
			}

			.drawer_time {
				width: 61px;
				font-size: 12px;
				color: #333333;
				position: relative;
				left: 5px;
			}

			.drawer_discount {
				display: flex;
				justify-content: center;
			}

			.drawer_payment {
				font-size: 12px;
			}

			.orderStatus {
				display: flex;
				justify-content: center;
			}

			.drawer_icon {
				width: 8px;
				height: 7px;
				color: #1A73E8;
				position: relative;
				left: 1.3021vw;
			}
		}

		.v-data-table {
			margin: 0 21px;
			border-bottom: 1px solid #E6E6E6;
			border-radius: 0px;
		}

		.drawer_label_title {
			font-size: 18px;
			font-family: Roboto;
			font-weight: bold;
			color: #333333;
			margin-top: 40px;
			margin-left: 22px;
		}
	}

	.list {
		border-top: 1px dashed #E6E6E6;
		border-bottom: 1px dashed #E6E6E6;
		margin: 0 20px;
		text-align: center;
		font-size: 12px;
		padding: 20px 0;

		td {
			border-right: 1px solid #E6E6E6;
		}

		.list_item {
			color: #999999;

			td {
				padding-top: 5px;
			}
		}
	}

	.drawer_list .list_title {
		font-size: 18px;
		font-weight: bold;
		color: #333333;
		line-height: 72px;
		margin-left: 6px;
	}
}

.form {
	padding: 0;
	color: #666666;

	ul {
		padding: 0;
		display: flex;
		margin-bottom: -15px;

		li {
			list-style: none;
			width: 250px;
			padding: 0px;
			margin-right: 20px;
			font-size: 14px;
		}
	}

	.form_li {
		margin-top: 12px;
	}

	.wenhao {
		.v-icon:hover {
			color: #1A73E8;
		}
	}

	.remark {
		width: 790px;
		margin-top: 12px;
	}
}

.v-divider {
	position: relative;
	top: 20px;
}

.btn_div {
	display: flex;
	justify-content: center;
	margin-top: 15px;

	.list_edit {
		width: 80px;
		height: 30px;
		background: #1A73E8;
		font-weight: 400;
		color: white;
		position: absolute;
		right: 57px;
		top: 24px;

		img {
			width: 16px;
		}
	}

	.drawer_btn1 {
		width: 80px;
		height: 30px;
		background: #1A73E8;
		border-radius: 8px;
		color: white;
		margin-right: 11px;
	}

	.drawer_btn2 {
		width: 80px;
		height: 30px;
		background: #E6E6E6;
		border-radius: 8px;
	}
}
</style>
