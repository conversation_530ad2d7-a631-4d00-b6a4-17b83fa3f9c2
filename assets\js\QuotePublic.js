export function getQuoteTime(list, priceInfo, proType) {
	let arr = [],
		dateList,
		newShowSmallPrice = false;
	/*
        2025-01-25 新增新年交期逻辑
        判断是否是开启新年交期开
        如果是新年交期，从交期列表获取开启了新年交期开关的交期
     */
	if (priceInfo?.isNewYearDiscount) {
		dateList = list.filter((x) => x.isNewYearDiscount == 1);
	} else {
		if (priceInfo?.isSmallWeight) {
			dateList = list.filter((x) => x.isSmallQty == 1);
			newShowSmallPrice = true;
		} else {
			dateList = list.filter((x) => x.isSmallQty == 0 || !x.isSmallQty);
			newShowSmallPrice = false;
		}
	}
	//如果交期为空，走一遍获取正常交期的逻辑,兜底交期不能为空
	if(!dateList.length){
		dateList = list.filter((x) => x.isSmallQty == 0 || !x.isSmallQty);
		newShowSmallPrice = false;
	}
	if (proType !== 0) {
		dateList.forEach((item) => {
			if (item.isRetailerPro === 2 || item.isRetailerPro === 3) {
				arr.push(item);
			}
		});
	} else {
		dateList.forEach((item) => {
			if (item.isRetailerPro === 1 || item.isRetailerPro === 3) {
				arr.push(item);
			}
		});
	}
	return {
		arr,
		newShowSmallPrice,
	};
}

export function findEarringsSelectDiscount(data){
	let list = data.list,discount;
	let findDiscount = list.find(item=>{
		return item.type === 'DISCOUNT';
	})
	if(findDiscount){
		discount = findDiscount.object.childList.find(item=>{
			return item.checked
		})
		if(discount){
			return discount
		}
	}
}

export function findSelectDiscount(data){
	let discount = {};
	for (let i in data){
		let selectVal = data[i];
		discount = selectVal.find(item=>{
			return item?.paramType == 'DISCOUNT'
		})
		if(discount){
			break;
		}
	}
	return discount
}

export function getIsSmallQty(discount,enableTurnaroundTimeCheck,priceData){
	//小重量交期群发标识
	let isSmallQty = 0;
	if(!discount){
		return isSmallQty
	}
	if(priceData && !priceData?.isSmallWeight){
		return isSmallQty
	}
	//判断是否开启了小重量交期运输时间的判断
	if(enableTurnaroundTimeCheck==1){
		//继续判断交期参数的shippingDay，若shippingDay>=7，则为小重量，否则即便重量满足小重量的要求，也不算小重量
		if(discount.isSmallQty && discount.shippingDay>=7){
			isSmallQty = 1;
		}else{
			isSmallQty = 0;
		}
	}else{
		//折扣为0 或者交期为打折的情况才加群发标识
		if (discount.isSmallQty && discount.priceInfo.priceType === 3 && (!discount.priceInfo.unitPercent || discount.priceInfo.unitPercent<=1)) {
			isSmallQty = 1;
		}
	}
	return isSmallQty
}
