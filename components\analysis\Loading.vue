<template>
  <div id="loading">
    <div class="header">
      <div class="breadcrumb-nav">
        <v-breadcrumbs :items="breadItems" divider=">" large class="pa-0">
          <template v-slot:item="{ item }">
            <v-breadcrumbs-item :href="item.href" :disabled="item.disabled">
              {{ item.text }}
            </v-breadcrumbs-item>
          </template>
        </v-breadcrumbs>
      </div>
      <div class="other">
        <div class="time-filter">time-filter</div>
        <div class="apply">Apply</div>
        <div class="print">
          <v-icon color="#1A73E8">mdi-printer-outline</v-icon>
        </div>
        <div class="download">
          <v-icon color="#1A73E8">mdi-tray-arrow-down</v-icon>
        </div>
      </div>
    </div>
    <div class="top">
      <div class="left">
        <!-- 当前Tab发生一个事件，tabChange 可以获取到当前的tabIndex -->
        <analysis-StatisticTab />
      </div>
      <div class="right">
        <analysis-BrieflyShowEmpty
          v-for="item in 6"
          :key="item"
          :haveData="false"
        />
      </div>
    </div>
    <div class="bottom">
      <div class="left">
        <analysis-PieChartShow />
      </div>
      <div class="right">
        <analysis-TableShow />
      </div>
    </div>
    <div class="bottom-bottom">
      <div></div>
    </div>
  </div>
</template>

<script>
export default {
  name: "Loading",
  data() {
    return {
      breadItems: [
        {
          text: "Dashboard",
          disabled: false,
          href: "/manage/dashboard",
        },
        {
          text: "Statistical Analysis",
          disabled: true,
          href: "/manage/analysis",
        },
      ],
    };
  },
};
</script>

<style lang="scss" scoped>
.header {
  display: flex;
  justify-content: space-between;
  height: 2.0833vw;
  margin-bottom: 0.5208vw;
  .other {
    display: flex;
    .time-filter {
      margin-right: 0.8333vw;
    }
    .apply {
      width: 4.2188vw;
      height: 1.4583vw;
      line-height: 1.4583vw;
      text-align: center;
      color: #fff;
      background: #1a73e8;
      border-radius: 0.2083vw;
      margin-right: 1.25vw;
    }
    .print,
    .download {
      width: 2.1354vw;
      height: 1.4583vw;
      background: #ffffff;
      border-radius: 0.2083vw;
      text-align: center;
    }
    .print {
      margin-right: 0.5729vw;
    }
  }
}
.top {
  display: flex;
  .left {
    width: 71.6146vw;
    height: 26.0417vw;
    padding-top: 1.3021vw;
    margin-right: 1.0417vw;
    background-color: #fff;
    border-radius: 0.5208vw;
  }
  .right {
    width: 23.0729vw;
    height: 26.0417vw;
    display: grid;
    grid-template-columns: 10.9375vw 10.9375vw;
    grid-template-rows: 8.125vw 8.125vw;
    column-gap: 1.1458vw;
    row-gap: 0.8854vw;
  }
}
.bottom {
  display: flex;
  padding-top: 1.3542vw;
  .left {
    width: 37.3958vw;
    margin-right: 1.0417vw;
  }
  .right {
    width: 100%;
  }
}
.bottom-bottom {
  height: 3.125vw;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  div {
    width: 552px;
    height: 30px;
    background: #e5e5e5;
    border-radius: 6px;
    margin-right: 30px;
  }
}
</style>
