<template>
	<div class="beltBuckleSizeBox">
		<BaseDialog v-model="showDialog" class="beltBuckleSizeDialog" :width="!isMobile ? '700px' : '90%'"
			:model="false">
			<div slot="closeIcon" @click="dialogClickSelf">
				<b class="icon-guanbi"></b>
			</div>
			<div class="contentBox">
				<div class="headTitle">{{langSemiCustom.buckleSizeChart}}</div>
				<div class="introText">{{langSemiCustom.clickSize}}</div>
				<div class="sizeBox">
					<div class="step-wrap">
						<div class="step-item" :class="{ active: index == selectIndex }"
							v-for="(step, index) in stepData.productParamList" :key="index"
							@click="selectStep(step, index)">
							<div class="sizeContent">
								<div class="prepend">{{ step.valueName }}</div>
							</div>
						</div>
					</div>
					<div class="sizeImg" v-show="showImg">
						<img :src="showImg" alt="beltBuckleSize" title="beltBuckleSize" />
					</div>
				</div>
				<div class="confirmBtn" @click="confirmFn">
					{{ langSemiCustom.confirm + ' & ' + lang.next }}
				</div>
			</div>
		</BaseDialog>
	</div>
</template>

<script>
import BaseDialog from "@/components/Quote/BaseDialog";
export default {
	name: 'beltBuckleSizeDialog',
	components: { BaseDialog },
	props: {
		showDialog: {
			type: Boolean,
			default: false
		},
		stepData: {
			type: Object,
			default: () => ({})
		},
		nowSelectIndex: {
			type: [Number, String],
			default: -1
		},
	},
	data() {
		return {
			selectIndex: this.nowSelectIndex,
			selectItem: null
		}
	},
	watch: {
	},
	computed: {
		isMobile() {
			return this.$store.state.device==="mb";
		},
		lang() {
			return this.$store.getters.lang.quote||{};
		},
		langSemiCustom() {
			return this.$store.getters.lang?.semiCustom;
		},
		showImg() {
			return +this.selectIndex>=0? this.stepData.productParamList[this.selectIndex].imgDetail:null;
		}
	},
	methods: {
		dialogClickSelf() {
			this.$emit('update:showDialog', false)
		},
		selectStep(step, index) {
			this.selectIndex=index
			this.selectItem=step
		},
		confirmFn() {
			this.$emit('selectSize', this.selectItem, this.selectIndex)
			this.$emit('update:showDialog', false)
		}
	},
	created() {

	},
	mounted() {
	},
}
</script>

<style scoped lang="scss">
@import "~assets/css/half.scss";

.beltBuckleSizeDialog {
	::v-deep .base-dialog-model-con {
		border-radius: 0;
		padding: 40px;

		.icon-guanbi {
			top: 5px;
			right: 5px;
			background-color: transparent;
		}

		@include respond-to(mb) {
			padding: 20px 12px;

			.icon-guanbi {
				top: 5px;
				right: 5px;
			}
		}
	}

	.contentBox {
		.headTitle {
			font-weight: bold;
			font-size: 24px;
			color: #000000;
			line-height: 28px;
			text-align: center;
			margin-bottom: 1em;
		}

		.introText {
			font-weight: 400;
			font-size: 16px;
			color: #000000;
			line-height: 19px;
			text-align: justified;
			margin-bottom: 20px;
		}

		.sizeBox {
			display: flex;
			align-items: flex-start;
			gap: 10%;

			.step-wrap {
				width: 18%;

				.step-item {
					margin-bottom: 10px;

					&.active {
						.sizeContent {
							.prepend {
								border: 1px solid $color-primary;
								background-color: #fff;
							}
						}
					}

					.sizeContent {
						.prepend {
							cursor: pointer;
							padding: 0.6em;
							font-weight: 400;
							font-size: 16px;
							color: #000000;
							text-align: center;
							background: #E2E2E2;
							border: 1px solid #E2E2E2;
							border-radius: 4px;
						}
					}


				}
			}

			.sizeImg {
				flex: 1;

				img {
					width: 100%;
					object-fit: contain;
				}
			}

		}

		.confirmBtn {
			width: fit-content;
			margin: 20px auto 0;
			text-align: center;
			color: #fff;
			background-color: $color-primary;
			border-radius: 6px;
			padding: 12px 20px;
			cursor: pointer;
		}

		@include respond-to(mb) {
			.headTitle {
				font-size: 16px;
				margin-bottom: 0.4em;
			}

			.introText {
				font-size: 14px;
				margin-bottom: 10px;
			}

			.sizeBox {
				flex-direction: column;
				gap: 6%;

				.step-wrap {
					padding: 0 10px;
					width: 100%;
					display: grid;
					grid-template-columns: repeat(3, 1fr);
					gap: 6px;

					.step-item {
						margin-bottom: 0;
					}
				}
			}
		}
	}
}
</style>
