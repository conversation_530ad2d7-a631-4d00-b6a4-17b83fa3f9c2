import { getLanguageLink } from "@/api/web.js";

export default async ({ isHMR, params, store, route, redirect }) => {
	if (isHMR) return
	let path = route.path, langData = store.state.language,proId = Number(store.state.proId);
	// 矫正路由多语言
	switch (path.replace(langData.lang + '/', '')) {
		case 'lapel-pins':
			path = ![512, 491, 9, 1].includes(proId) ? langData.lang + '/custom/lapel-pins' : path;
			break;
		case 'lanyards':
			path = proId!==491 ? langData.lang + '/custom/lanyards' : path;
			break;
		case 'coins':
			path = proId!==491 ? langData.lang + '/custom/coins' : path;
			break;
		case 'medals':
			if(proId!==491){
				path = langData.lang + '/custom/medals'
			}else{
				path = langData.lang + '/custom-medals'
			}
			// path = proId!==491 ? langData.lang + '/custom/medals' : path;
			break;
		case 'trading-pins':
			path = proId!==491 ? langData.lang + '/custom/trading-pins' : path;
			break;
		case 'belt-buckles':
			path = proId!==491 ? langData.lang + '/custom/belt-buckles' : path;
			break;
		case 'patches':
			path = proId!==491 ? langData.lang + '/custom/patches' : path;
			break;
		case 'pvc-patches':
			path = proId!==491 ? langData.lang + '/custom/pvc-patches' : path;
			break;
		case 'create-your-own-stickers/vinyl-lettering':
			path = '/design/transfer-stickers';
			break;
		case 'Vinyl-lettering':
			path = '/custom-transfer-stickers';
			break;
		case 'Wall-graphics':
			path = '/custom-transfer-stickers';
			break;
		case 'make-your-own-stickers':
			path = '/make-your-own';
			break;
		case 'make-your-own-labels':
			path = '/make-your-own';
			break;
		case 'make-your-own-buttons':
			path = '/make-your-own';
			break;
		case 'one-dollar-deal':
			path = '/';
			break;
		case 'custom/metal-enamel-keychains':
			path = '/metal-enamel-keychains';
			break;
		case 'custom/pvc-keychains':
			path = '/pvc-keychains';
			break;
		case 'custom/acrylic-keychains':
			path = '/acrylic-keychains';
			break;
		case 'custom/embroidered-keychains':
			path = '/embroidered-keychains';
			break;
		case 'quote/custom-embroidered-printed-patches':
			path = '/quote/custom-full-embroidered-full-printed-patches';
			break;
		case 'custom/custom-embroidered-printed-patches':
			path = '/custom/custom-full-embroidered-full-printed-patches';
			break;
		case 'decoration':
			path = '/custom-medals';
			break;
		case 'one-color-tpu-patches':
			path = '/one-color-flex-patches';
			break;
		case 'custom/custom-flexpatches':
			path = '/custom/custom-full-color-flex-patches';
			break;
		case 'Custom-3d-or-cutout-pins':
			path = '/Custom-3d-pins';
			break;
		default: {
			const domainPublic = store.state.domainFull.includes(store.state.proUrl),
				lang = params.lang || path.split('/')[1] || '',
				country = lang && store.state.countryList.find(c => c.countryCode == lang.split('-')[0]),
				language = !domainPublic && lang?.length < 3 && store.state.country.childList.find(l => l.language == lang);


			// 若仅国家-语言标识被作为路由且没有结束斜杠，则加上结束斜杠
			if (path == langData.lang) path += '/'; // path.split('/').length == 2

			if (path.includes('/user/') || !route.name.includes('lang-')) {
				if (!path.startsWith('/user/') || (country && domainPublic) || language) path = path.replace('/' + lang, '');
			}
			// 是公共域名 且 国家标识正确
			else if (country && domainPublic) {
				// 若单语言网站，则不展示国家-语言标识
				if (store.state.countryList.length == 1 && country.childList.length == 1) path.replace('/' + lang, '');

				// 若国家-语言标识与当前语言不符，则以国家-语言标识为准改变当前语言
				if (lang != langData.lang?.replace('/', '')) store.commit('setLanguage', country.childList.find(l => l.language == lang.split('-')[domainPublic ? 1 : 0]) || country.childList.find(l => l.isDefault) || country.childList[0]);

				// 若国家-语言标识与当前语言不符，则以当前语言为准改变国家-语言标识
				// if (('/' + lang) != langData.lang) path = path.replace(('/' + lang), langData.lang);
			}
			// 国家标识不存在或不正确，但是是公共域名，则加上国家-语言标识
			else if (domainPublic) path = (langData.lang || '') + path;
			// 是非公共域名 且语言标识符合长度
			else if (lang.length < 3 && lang != langData.lang?.replace('/', '')) {
				// 语言标识正确，则以语言标识为准改变当前语言
				if (language) store.commit('setLanguage', language);
				// 语言标识不存在或不正确，则加上语言标识
				else path = (langData.lang || '') + path;
			}

			path = path.replace(/\/\//g, '/');

			// console.log('path_g: ', lang, domainPublic, !!country, !!language)
			// console.log('langCode_g: ', langData.lang, lang)
		}
	}
	try {
		//多语言link
		await getLanguageLink(path.replace(langData.lang, ''), langData.language, langData.countryCode).then(({ data }) => store.commit('setLanguageLink', data));
	}catch (e) {
		console.log('获取多语言link失败')
	}

	store.commit('setPagePath', path); // 当前页面
	if (route.path != path) return redirect(route.fullPath.replace(route.path, path));
};
