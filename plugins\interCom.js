export default function ({ store }) {
	let searchParams = new URLSearchParams(window.location.search);
	let type = searchParams.get("type");
	if (type !== "iframe" && type !== "quoteIframe") {
		const source = window.location.hostname;
		let app_id = process.env.app_id,
			secret = process.env.secret,
			userInfo = store.state.userInfo;
		if (userInfo.email) {
			let hash = CryptoJS.HmacSHA256(userInfo.email, secret);
			let intercomUserHash = CryptoJS.enc.Hex.stringify(hash);
			window.intercomSettings = {
				api_base: "https://api-iam.intercom.io",
				app_id: app_id,
				utm_source: source,
				email: userInfo.email,
				name: userInfo.firstName + " " + userInfo.lastName,
				user_hash: intercomUserHash,
			};
		} else {
			window.intercomSettings = {
				api_base: "https://api-iam.intercom.io",
				app_id: app_id,
				utm_source: source,
			};
		}
		(function () {
			var w = window;
			var ic = w.Intercom;
			if (typeof ic === "function") {
				ic("reattach_activator");
				ic("update", w.intercomSettings);
			} else {
				var d = document;
				var i = function () {
					i.c(arguments);
				};
				i.q = [];
				i.c = function (args) {
					i.q.push(args);
				};
				w.Intercom = i;
				var l = function () {
					var s = d.createElement("script");
					s.type = "text/javascript";
					s.async = true;
					s.src = `https://widget.intercom.io/widget/${app_id}`;
					var x = d.getElementsByTagName("script")[0];
					x.parentNode.insertBefore(s, x);
				};
				if (document.readyState === "complete") {
					l();
				} else if (w.attachEvent) {
					w.attachEvent("onload", l);
				} else {
					w.addEventListener("load", l, false);
				}
			}
		})();
	}
}
