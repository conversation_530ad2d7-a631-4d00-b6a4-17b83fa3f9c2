<template>
  <div class="mySwitch">
    <input type="checkbox" class="switch-button" :class="{ active: active }" id="switch-button" />
    <label for="switch-button"></label>
  </div>
</template>
<script>
export default {
  props: {
    active: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {};
  },
  methods: {},
  computed: {},
  watch: {},
};
</script>
<style scoped lang="scss">
.mySwitch {
  display: flex;
  align-items: center;

  .switch-button {
    display: none;
    /*隐藏表单元素*/
  }

  .switch-button+label {
    /*+选择器选择紧跟“+”左边选择器的第一个元素*/
    display: inline-block;
    position: relative;
    transition: all 0.3s;
    width: 40px;
    height: 20px;
    border-radius: 15px;
    background-color: #ccc;
  }

  .switch-button.active+label {
    /*选中表单后的样式，:checked表示checkbox被选中后的状态*/
    background-color: $color-primary;
  }

  .switch-button+label::before {
    /*使用伪元素生成一个按钮*/
    content: "";
    display: block;
    height: 16px;
    width: 16px;
    position: absolute;
    border-radius: 25px;
    left: 2px;
    top: 2px;
    background-color: #fff;
    transition: all 0.3s;
  }

  .switch-button.active+label::before {
    /*checkbox选中时按钮的样式*/
    left: 22px;
    transition: all 0.2s linear;
  }
}
</style>