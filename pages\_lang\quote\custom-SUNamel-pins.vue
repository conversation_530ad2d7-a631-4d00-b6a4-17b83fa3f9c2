<template>
	<div class="pinsQuoteWrap">
		<Preloader v-if="isLoading"></Preloader>
		<div class="containerWrap" v-else>
			<!-- <QuoteTitle :h1-text="lang.pins.h1" :prompts-text="lang.pins.prompts"></QuoteTitle> -->

			<div class="content">
				<div class="leftArea">
					<p class="leftArea_title">{{ lang.SUNamelCustomShape }}</p>
					<!-- 左上 <-> 右下 的消失动画 -->
					<transition enter-active-class="animate__animated animate__faster animate__fadeInBottomRight" leave-active-class="animate__animated animate__faster animate__fadeOutBottomRight">
						<SwiperDetail :imgList="carouselList" v-show="showRightArea" :selectedData="selectedData" :generalData="generalData" @toPosition="toPosition" :attachment="attachment" />
					</transition>
				</div>

				<div class="rightArea" id="rightAreaCustom">
					<div class="rightTop">
						<div class="size-item item" v-for="item in changePageList" :key="item.value" :class="{ active: item.value == 1 }" @click="linkTo(item)">
							<div class="circle2"></div>
							<div class="textWrap">
								<p class="normal-text">
									{{ item.name }}
								</p>
							</div>
						</div>
					</div>

					<template v-for="(item, index) in generalData">
						<!--   步骤1  -->
						<template v-if="item.paramName === 'Custom Shape Pins Size'">
							<div class="step-item step-size" :class="{ mask: maskName === item.paramName }" :id="item.paramName" :key="index">
								<h3 class="step-title">
									<span stepColor> {{ item.customIndex }}</span>
									{{ lang.Select }}
									{{ item.alias ? item.alias : item.paramName }}
								</h3>
								<div class="box-border">
									<i class="el-icon-close" @click="closeMask"></i>
								</div>
								<div class="step-box step-size-box">
									<div class="step-size-leftArea">
										<div class="size-area custom-shadow">
											<!-- <p class="step-size-title">
												{{ lang.pins.stepSizeTitle }}
											</p> -->
											<div class="size-item-wrap">
												<div
													class="size-item item"
													v-for="(citem, cindex) in item.childList"
													:key="citem.id"
													@click="selectQuoteParams(item, citem)"
													:class="{
														active: hasId(citem.id, selectedData[item.paramName]),
														onlyInquiry: citem.onlyAddInquiry === 1,
													}"
												>
													<div class="circle2"></div>
													<div class="textWrap">
														<p class="normal-text">
															{{ citem.alias ? citem.alias : citem.paramName }}
														</p>
														<span @click.stop v-if="citem.tips">
															<el-tooltip popper-class="cusToolTip" effect="light" :content="citem.tips" placement="top-start">
																<b class="icon-wenhao1 tip-icon"></b>
															</el-tooltip>
														</span>
													</div>
													<Corner v-if="citem.labelText" :backgroundColor="citem.labelColor" type2 position="absolute">
														{{ citem.labelText }}
													</Corner>
												</div>
											</div>
										</div>
										<div class="step-size-rightArea forMB">
											<div class="textWrap">
												<p class="normal-text">{{ lang.pins.p1 }}</p>
											</div>
											<div class="shape-img">
												<img :src="shapeImg" alt="" />
											</div>
										</div>
										<div class="d-flex-center confirmBtnWrap">
											<QuoteBtn
												@click.native="showMaskFn(item.paramName)"
												:style="{
													opacity: selectedData[item.paramName].length > 0 ? 1 : 0.5,
												}"
												:disabled="!selectedData[item.paramName].length > 0"
												>{{ lang.next }}
											</QuoteBtn>
										</div>
									</div>
									<div class="step-size-rightArea">
										<div class="textWrap">
											<p class="normal-text">{{ lang.pins.p1 }}</p>
										</div>
										<div class="shape-img">
											<img :src="shapeImg" alt="" />
										</div>
									</div>
								</div>
							</div>
						</template>

						<!--  步骤2  -->
						<template v-if="item.paramName === 'Select Metal Finish'">
							<div class="step-item step-metal" :class="{ mask: maskName === item.paramName, hideContent: $route.query.designPic }" :id="item.paramName" :key="index">
								<div class="box-border">
									<i class="el-icon-close" @click="closeMask"></i>
								</div>
								<h3 class="step-title">
									<span stepColor> {{ item.customIndex }} </span>
									{{ lang.Select }}
									{{ item.alias ? item.alias : item.paramName }}
									<span v-if="$route.query.designPic" style="color: #999999">: {{ $route.query.quotePlatingName }}</span>
								</h3>
								<div class="step-box step-metal-box">
									<div class="item-wrap" v-for="citem in item.childList" :key="citem.id" @click="selectQuoteParams(item, citem)" :class="{ active: hasId(citem.id, selectedData[item.paramName]) }">
										<div class="item normalBorder">
											<div class="imgWrap">
												<img loading="lazy" :src="parseJSON(citem.imageJson)[0].url" :alt="parseJSON(citem.imageJson)[0].alt" style="aspect-ratio: 232/116" />
											</div>
											<Corner v-if="citem.labelText" :backgroundColor="citem.labelColor" type2 position="absolute">
												{{ citem.labelText }}
											</Corner>
											<div class="circle">
												<div class="inner-circle"></div>
											</div>
										</div>
										<div class="textWrap">
											<div class="circle2"></div>
											<div>
												<p class="normal-text">
													{{ citem.alias ? citem.alias : citem.paramName }}
												</p>
												<PriceText :paramData="citem"></PriceText>
											</div>
										</div>
									</div>
								</div>
							</div>
						</template>

						<!--  步骤3  -->
						<template v-if="item.paramName === 'Back Side Option'">
							<div class="step-item step-back" :class="{ mask: maskName === item.paramName }" :id="item.paramName" :key="index">
								<div class="box-border">
									<i class="el-icon-close" @click="closeMask"></i>
								</div>
								<h3 class="step-title">
									<span stepColor> {{ item.customIndex }} </span>
									{{ lang.Select }}
									{{ item.alias ? item.alias : item.paramName }}
								</h3>
								<div class="step-box step-color-box">
									<div
										class="item-wrap"
										v-for="citem in item.childList"
										:key="citem.id"
										@click="selectQuoteParams(item, citem)"
										:class="{
											active: hasId(citem.id, selectedData[item.paramName]),
										}"
									>
										<div class="item normalBorder">
											<div class="imgWrap">
												<!-- <img loading="lazy" :src="parseJSON(citem.imageJson)[0].url" :alt="parseJSON(citem.imageJson)[0].alt" style="aspect-ratio: 372/220" /> -->
												<img loading="lazy" :src="parseJSON(citem.imageJson)[0].url" :alt="parseJSON(citem.imageJson)[0].alt" style="aspect-ratio: 1500/1500" />
											</div>
											<Corner v-if="citem.labelText" :backgroundColor="citem.labelColor" type2 position="absolute">
												{{ citem.labelText }}
											</Corner>
										</div>
										<div class="textWrap">
											<div class="circle2"></div>
											<div>
												<div flex>
													<p class="normal-text">
														{{ citem.alias ? citem.alias : citem.paramName }}
													</p>
													<span @click.stop v-if="citem.tips" style="margin-left: 10px">
														<el-tooltip popper-class="cusToolTip" effect="light" :content="citem.tips" placement="top-start">
															<b class="icon-wenhao1 tip-icon"></b>
														</el-tooltip>
													</span>
												</div>
												<PriceText :paramData="citem"></PriceText>
											</div>
										</div>
									</div>
								</div>
							</div>
						</template>

						<!--  步骤4  -->
						<template v-if="item.paramName === 'Select Packing Options'">
							<div class="step-item step-packing" :class="{ mask: maskName === item.paramName }" :id="item.paramName" :key="index">
								<div class="box-border">
									<i class="el-icon-close" @click="closeMask"></i>
								</div>
								<h3 class="step-title">
									<span stepColor> {{ item.customIndex }} </span>
									{{ lang.Select }}
									{{ item.alias ? item.alias : item.paramName }}
								</h3>
								<div class="step-box step-packing-box">
									<div
										class="item-wrap"
										v-for="citem in item.childList"
										:key="citem.id"
										@click="selectQuoteParams(item, citem)"
										:class="{
											active: hasId(citem.id, selectedData[item.paramName]),
										}"
									>
										<div class="item normalBorder">
											<div class="imgWrap">
												<img loading="lazy" :src="parseJSON(citem.imageJson)[0].url" :alt="parseJSON(citem.imageJson)[0].alt" style="aspect-ratio: 696/540" />
											</div>
											<Corner v-if="citem.labelText" :backgroundColor="citem.labelColor" type2 position="absolute">
												{{ citem.labelText }}
											</Corner>
											<div class="textWrap">
												<div flex>
													<div class="circle2"></div>
													<p class="normal-text">
														{{ citem.alias ? citem.alias : citem.paramName }}
													</p>
												</div>
												<div>
													<PriceText :paramData="citem" :sizeValue="sizeValue"></PriceText>
												</div>
											</div>
											<Corner v-if="citem.labelText" :backgroundColor="citem.labelColor" position="absolute">
												{{ citem.labelText }}
											</Corner>
										</div>
									</div>
								</div>
							</div>
						</template>
						<!--   步骤5  -->
						<template v-if="item.paramName === 'Upload Artwork & Comments'">
							<StepUpload class="step-item step-upload" :class="{ mask: maskName === item.paramName }" :id="item.paramName" :index="item.customIndex" :itemData="item" :uploadList.sync="uploadArtworkList" :remark.sync="remark" :isUpload.sync="isUpload" @closeMask="closeMask" @showMaskFn="showMaskFn" :key="index"></StepUpload>
						</template>
						<!--   步骤6  -->
						<template v-if="item.paramName === 'Quantity'">
							<StepQty class="step-item step-qty" :class="{ mask: maskName === item.paramName }" :id="item.paramName" :index="item.customIndex" :itemData="item" :customQty.sync="customQty" :restaurants="restaurants" @closeMask="closeMask" @showMaskFn="showMaskFn" @calcPrice="debounceCalcPrice" @changeQty="changeQty" :key="index"></StepQty>
						</template>
						<!--   步骤7!!!-->
						<template v-if="item.paramName === 'Select Turnaround Time'">
							<StepTime class="step-item step-date" :class="{ mask: maskName === item.paramName }" :id="item.paramName" :index="item.customIndex" :cateData="cateData" :pid="pid" :customQty="customQty" :itemData="item" :priceInfo="priceInfo" :previewMode="previewMode" :selectedParams="selectedData" @showMaskFn="showMaskFn" @selectItem="selectQuoteParams($event.item, $event.citem)" @closeMask="closeMask" :key="index"></StepTime>
						</template>
					</template>
				</div>
			</div>
			<div class="footer" id="foot">
				<Detail class="type2" :key="3" :absoluteImg="comeFromDZ.absoluteImg" :selectedData="selectedData" :priceInfo="priceInfo" :customQty="customQty" :textInfo="textInfo" :generalData="generalData" :attachment="attachment" @toPosition="toPosition" @addInquiry="debounceAddInquiry" @addCart="addCart"></Detail>
			</div>
			<el-drawer :visible.sync="showArea" :with-header="true" custom-class="drawDialog" :size="device === 'ipad' ? '728px' : '85%'">
				<Detail class="type3" :key="2" :absoluteImg="comeFromDZ.absoluteImg" :selectedData="selectedData" :priceInfo="priceInfo" :customQty="customQty" :textInfo="textInfo" :generalData="generalData" :attachment="attachment" @toPosition="toPosition" @addInquiry="debounceAddInquiry" @addCart="addCart"></Detail>
			</el-drawer>
			<!-- 建议弹窗-->
			<RecomendDialog :commentsDialog.sync="commentsDialog" :paramValue="selectedParamsValue" @next="recomendNext"></RecomendDialog>
			<!-- 预览弹窗-->
			<VideoPreviewDialog :pic-dialog.sync="picDialog" :zoom-pic="zoomPic" :temp-type="tempType" :selected-params-value="selectedParamsValue" @qtyAndBtnConfirm="qtyAndBtnConfirm" @qtyAndBtnNext="qtyAndBtnNext" @dialogNextStep="dialogNextStep"></VideoPreviewDialog>
			<!-- 升级数量弹窗-->
			<BaseDialog v-model="upgradeDialog" :width="device != 'mb' ? '640px' : '320px'">
				<Upgrade @closeUpgrade="upgradeDialog = false" :tipArr="tipArr" :typeName="cateData.cateName" @upgradeLevel="upgradeLevel"></Upgrade>
			</BaseDialog>
			<!-- o2o预览页面弹窗-->
			<BaseDialog v-model="showOtoDialog" :width="device != 'mb' ? '570px' : '90%'">
				<div class="otoWrap">
					<img src="@/assets/images/oto.png" alt="" />
					<p>{{ lang.p1 }}</p>
					<h3>{{ lang.p2 }}</h3>
					<p style="color: #666666">
						{{ lang.p3 }}<br />
						{{ lang.p4 }}.
					</p>

					<div class="box">
						<p class="t1">
							{{ lang.p5 }} <br />
							{{ lang.p6 }}!
						</p>
						<a href="https://www.o2o.co/previewWeb" style="text-decoration: none">
							<el-button type="primary">{{ lang.p7 }}</el-button>
						</a>
					</div>
				</div>
			</BaseDialog>
			<!--			询盘弹窗-->
			<infoDialog :infoDialogVisible.sync="infoDialogVisible" :uploadList.sync="uploadList" :otherUpload="uploadArtworkList" :recomendUpload="recomendUpload" @getValue="getValueFun"></infoDialog>
			<!--    遮罩-->
			<myMask :maskName.sync="maskName"></myMask>
			<!--  手机，ipad端预览-->
			<PreviewBtn :showArea.sync="showArea"></PreviewBtn>
			<BaseDialog v-model="noFileDialog" :width="device != 'mb' ? '485px' : '90%'" :model="false">
				<template #closeIcon>
					<div style="display: none"></div>
				</template>
				<infoUpload :infoUploadList="infoUploadList" @pushInfoList="pushInfoList" @delInfoList="delInfoList" @updateInquiry="updateInquiry" @closeInfoDialog="closeInfoDialog"> </infoUpload>
			</BaseDialog>
		</div>
	</div>
</template>

<script>
import "@/plugins/element";
import VideoPlayer from "@/components/Quote/VideoPlayer.vue";
import QuoteBtn from "@/components/Quote/QuoteBtn";
import myMask from "@/components/Quote/Mask";
import Detail from "@/components/Quote/Detail";
import BaseDialog from "@/components/Quote/BaseDialog";
import Upgrade from "@/components/Quote/Upgrade";
import Preloader from "@/components/Quote/Preloader";
import PriceText from "@/components/Quote/PriceText";
import Star from "@/components/Quote/Star";
import StepUpload from "@/components/Quote/StepUpload";
import StepTime from "@/components/Quote/StepTime";
import StepQty from "@/components/Quote/StepQty";
import Corner from "@/components/Medals/Corner";
import Pimg from "@/components/Medals/Pimg";
import QtyAndBtn from "@/components/Medals/QtyAndBtn";
import DialogBM from "@/components/Medals/DialogBM";
import infoDialog from "@/components/Medals/infoDialog";
import RecomendDialog from "@/components/Quote/RecomendDialog.vue";
import quoteMixin from "@/mixins/quote";
import QuoteTitle from "@/components/Quote/QuoteTitle.vue";
import PreviewBtn from "@/components/Quote/PreviewBtn.vue";
import CustomCircle from "@/components/Quote/customCircle.vue";
import { isImageType } from "@/utils/utils";
import SwiperDetail from "@/components/Cufflinks/SwiperDetail.vue";
import VideoPreviewDialog from "@/components/Quote/VideoPreviewDialog.vue";
export default {
	head() {
		return {
			title: "Quote page",
		};
	},
	components: {
		VideoPreviewDialog,
		CustomCircle,
		PreviewBtn,
		QuoteTitle,
		DialogBM,
		QuoteBtn,
		Detail,
		BaseDialog,
		Upgrade,
		myMask,
		VideoPlayer,
		Preloader,
		PriceText,
		Star,
		StepUpload,
		StepTime,
		StepQty,
		Corner,
		Pimg,
		QtyAndBtn,
		infoDialog,
		RecomendDialog,
		SwiperDetail,
	},
	mixins: [quoteMixin],
	name: "custom-SUNamel-pins",
	data() {
		return {
			pid: 237,
			productsName: "Custom Shape SUNamel Pins",
			restaurants: [
				{ value: "50", address: "50" },
				{ value: "100", address: "100" },
				{ value: "200", address: "200" },
				{ value: "300", address: "300" },
				{ value: "500", address: "500" },
				{ value: "1000", address: "1000" },
				{ value: "3000", address: "3000" },
				{ value: "5000", address: "5000" },
				{ value: "10000", address: "10000" },
			],
			// cusTop: 0,
			changePageList: [
				{ name: "Custom Shape", value: 1, url: "/quote/custom-SUNamel-pins" },
				{ name: "Template Shape", value: 2, url: "/quote/template-SUNamel-pins" },
			],
		};
	},
	methods: {
		isImageType,
		// getTop() {
		// 	let el = document.getElementById("detailInfo");
		// 	if (!el) {
		// 		return false;
		// 	}
		// 	const { top, height } = el.getBoundingClientRect();
		// 	this.cusTop = (window.innerHeight - height) / 2 - 100;
		// 	// this.cusTop = (window.innerHeight - height) / 2 + 113 / 2 - 20;
		// },
		linkTo(val) {
			this.$router.push({
				path: val.url,
			});
		},
	},
	watch: {
		// isLoading(newVal){
		// 	}
	},
	mounted() {},
};
</script>

<style scoped lang="scss">
.tips.type2 {
	top: 0;
	transform: translateY(0);
	right: auto;
	left: 0;
}

.pinsQuoteWrap ::v-deep {
	font-family: Calibri, Arial, serif;
	@media screen and (max-width: 767px) {
		input,
		textarea {
			font-size: 16px !important;
		}
	}

	.viewMore {
		@media screen and (min-width: 768px) {
			display: none;
		}
	}

	.step-title {
		background-color: #f9f9f9;
		> span {
			// color: $color-primary;
			font-size: 24px;
			width: 36px;
			height: 36px;
			line-height: 36px;
			margin-right: 10px;
			text-align: center;
			display: inline-block;
			background: #333;
			color: #fff;
			border-radius: 4px;
		}
	}

	ul {
		margin: 0;
	}

	img {
		border: none;
		vertical-align: middle;
		max-width: 100%;
		max-height: 100%;
	}

	.custom-shadow {
		position: relative;
		background-color: #fff;
	}

	.custom-shadow:after,
	.custom-shadow:before {
		content: "";
		position: absolute;
		z-index: -1;
		bottom: 12px;
		left: 5px;
		width: 50%;
		height: 20%;
		// box-shadow: 0 14px 7px #d9dbdd;//size阴影
		transform: rotate(-3deg);
	}

	.custom-shadow:after {
		right: 5px;
		left: auto;
		transform: rotate(3deg);
	}

	.video-js {
		overflow: hidden;
		border-radius: 6px 6px 0 0;
	}

	.containerWrap {
		background-color: #fff;
		font-size: 18px;
		padding-top: 20px;
		.content {
			// padding: 20px 11.5vw 0;
			padding: 3.8em max(calc(50% - 700px), 1.5vw) 4.2em;
		}
		.isComment {
			width: 100%;
			height: 100%;
			border: 1px solid #ccc;
			padding: 10px;
			border-radius: 4px;
			box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
			text-align: center;

			.textWrap {
				display: flex;
				align-items: center;
				justify-content: flex-start;
				margin-top: 0 !important;
			}

			.circle2 {
				border-color: $color-primary;
				background-color: $color-primary;

				&::after {
					background-color: #ffffff;
				}
			}

			.el-textarea {
				flex: 1;
				margin: 10px 0;

				textarea {
					height: 100%;
					background-color: #f5f5f5 !important;
				}
			}

			button {
				background-color: $color-primary;
				color: #fff;
				padding: 4px;
				width: 100px;
				outline: none;
				border: none;
				border-radius: 4px;
				font-size: 14px;
				text-align: center;
			}
		}

		.content {
			position: relative;
			display: grid;
			grid-template-columns: repeat(48, 1fr);
			// grid-template-columns: 1fr 1fr;
			margin: 20px 0 0;
			.leftArea {
				grid-column: 2/24;
				.leftArea_title {
					font-size: 36px;
					text-align: center;
				}
				// .pic_vIew{
				// 	position:sticky;
				// 	&>p{
				// 		font-size: 36px;
				// 		text-align: center;
				// 	}
				// }
			}

			.rightArea {
				grid-column: 25/49;
				// margin-right: 10px;
				// grid-column: 20/48;

				// padding-right: 30px;
				.rightTop {
					padding: 0 20px;
					display: grid;
					grid-template-columns: 1fr 1fr;
					column-gap: 7%;
					.size-item {
						align-items: center;
						background: #f4f5f5;
						border: 1px solid transparent;
						border-radius: 10px;
						cursor: pointer;
						display: flex;
						height: 40px;
						padding: 0 4px 0 20px;
						transition: all 0.3s;
					}
					.size-item.active {
						border-color: $color-primary;
						.circle2 {
							border-color: $color-primary;
							background: $color-primary;
							&::after {
								background-color: #fff;
							}
						}
					}
				}
				.mask {
					z-index: 101;
					background-color: #fff;
				}
			}
		}

		.picWrap {
			display: flex;
			justify-content: center;
			align-items: center;
			flex-direction: column;

			img {
				width: 100%;
				height: 100%;
				object-fit: contain;
			}
		}

		.footer {
			display: grid;
			grid-template-columns: 700px;
			justify-content: center;
			padding: 20px;
			background: #eef2f5;
		}

		.small-title {
			position: relative;
			margin-bottom: 16px;
			font-size: 16px;
			color: #666666;
		}

		.d-flex-center {
			display: flex;
			justify-content: center;
			align-items: center;
		}

		.circle2 {
			display: flex;
			justify-content: center;
			align-items: center;
			width: 18px;
			height: 18px;
			min-width: 18px;
			border: 1px solid #afb1b3;
			border-radius: 50%;
			margin-right: 10px;
			background-color: #fff;
			transition: all 0.3s;

			&::after {
				content: "";
				width: 6px;
				height: 6px;
				min-width: 6px;
				background: #d4d7d9;
				border-radius: 50%;
			}
		}

		.circle {
			position: absolute;
			left: 50%;
			bottom: 0;
			transform: translate(-50%, 100%);
			width: 28px;
			height: 15px;
			border: 1px solid #e9ecf0;
			border-top: none;
			background: #edf1f5;
			border-radius: 0 0 16px 16px;
			z-index: 10;
			transition: all 0.3s;

			.inner-circle {
				position: absolute;
				left: 50%;
				top: 0;
				transform: translate(-50%, -50%);
				width: 18px;
				height: 18px;
				border-radius: 50%;
				background: #ffffff;
				border: 1px solid #aaaeb3;
				transition: all 0.3s;

				&:after {
					content: "";
					position: absolute;
					left: 50%;
					top: 50%;
					transform: translate(-50%, -50%);
					width: 6px;
					height: 6px;
					background-color: #aaaeb3;
					border-radius: 50%;
					transition: all 0.3s;
				}
			}
		}

		.circle3 {
			display: flex;
			justify-content: center;
			align-items: center;
			width: 18px;
			height: 18px;
			min-width: 18px;
			border: 1px solid #dae0e5;
			background-color: white;
			margin-right: 6.5px;
			transition: all 0.3s;
			position: relative;

			&::after {
				position: absolute;
				content: "";
				left: 50%;
				top: 50%;
				transform: translate(-50%, -50%);
				height: 6px;
				width: 6px;
				border-radius: 50%;
				background-color: #dae0e5;
			}

			/* .checkIcon {
        font-size: 16px;
        color: #ffffff;
      } */
		}

		.confirmBtnWrap {
			margin-top: 33px;
		}

		.drawDialog {
			.el-drawer__header {
				margin-bottom: 0;
				padding: 10px;
			}
		}

		@media screen and (max-width: 1500px) {
			.containerWrap {
				padding-top: 0;
			}
			.content {
				.leftArea {
					// display: none;
					grid-column: 2/48;
				}

				.rightArea {
					// display: none;
					grid-column: 2/48;
					// grid-column-start: span 2;
					.rightTop {
						margin-top: 30px;
					}
				}

				.rightArea.rightFixedArea {
					overflow: hidden auto;
					display: block;
					margin: 0;

					.detailList {
						position: relative;
						top: 0 !important;
						box-shadow: none;
					}
				}
			}
		}

		@media screen and (max-width: 767px) {
			background-color: #ebebeb;
			padding-top: 15px;
			.containerWrap {
				// background-color:#ebebeb !important;
			}
			.content {
				padding: 0;
				margin: 0;
				.leftArea {
					// .pic_vIew{
					// 	.title{
					// 		font-size: 28px;
					// 		margin-bottom: 10px;
					// 	}
					// }
					.leftArea_title {
						font-size: 28px;
						margin-bottom: 10px;
					}
					.myswiper2 {
						height: initial;
					}
					.myswiper1 {
						display: none;
					}
				}
				.rightArea {
					.rightTop {
						padding: 0;
						margin: 15px 0 !important;
					}
				}
			}

			.footer {
				grid-template-columns: 1fr;
				padding: 10px;
				background-color: #e0e0e0;
			}

			.small-title {
				font-size: 12px;
				margin-bottom: 15px;
			}

			.circle {
				width: 20px;
				height: 10px;

				.inner-circle {
					width: 14px;
					height: 14px;

					&:after {
						content: "";
						width: 5px;
						height: 5px;
					}
				}
			}

			.circle2 {
				width: 18px;
				height: 18px;
				margin-right: 5px;

				&::after {
					width: 5px;
					height: 5px;
				}
			}

			.confirmBtnWrap {
				margin-top: 20px;
			}
		}
	}

	.step-item {
		position: relative;
		// margin-bottom: 16px;
		background-color: #fff;
		box-sizing: content-box;
		// padding: 40px 30px;
		padding: 30px 20px;
		border-radius: 10px;

		&.hideContent {
			.step-box {
				display: none;
			}

			.step-title {
				margin-bottom: 0;
			}
		}

		.box-border {
			display: none;
			.el-icon-close {
				display: flex;
				justify-content: center;
				align-items: center;
				position: absolute;
				font-weight: 700;
				top: 0;
				right: 0;
				width: 40px;
				height: 40px;
				transform: translate(50%, -50%);
				cursor: pointer;
				background: #ffffff;
				border-radius: 50%;
				box-shadow: 1px 2px 20px 0 rgba(0, 0, 0, 0.2);
				z-index: 10;

				// 下一步右上角的关闭按钮 不需要了
				// display: none;
			}
		}

		&.mask {
			position: relative;
			z-index: 101;

			.confirmBtnWrap {
				position: relative;
			}

			// 下一步的
			.box-border {
				position: absolute;
				left: 0;
				right: 0;
				top: 0;
				bottom: 0;
				display: block;
				background-color: #fff;
				border: 1px solid #d9dbdd;
			}

			.step-title {
				position: relative;
				z-index: 9;
			}

			.step-box {
				position: relative;
			}
		}

		.step-title {
			font-size: 18px;
			font-weight: 700;
			color: #333333;
			margin-bottom: 23px;

			.step-title-icon {
				width: 21px;
				margin-left: 4px;
				cursor: pointer;
				vertical-align: middle;
			}
		}

		.step-title.title5 {
			margin-bottom: 4px;
		}

		@media screen and (min-width: 768px) and (max-width: 1499px) {
			padding: 30px 10px;

			.small-title {
				margin-bottom: 15px;
			}
		}

		@media screen and (max-width: 767px) {
			margin-bottom: 10px;
			background-color: #fff;
			border-radius: 5px;
			padding: 20px 7px;
			&.mask {
				.box-border {
					.el-icon-close {
						width: 30px;
						height: 30px;
						transform: translate(0, 0);
						box-shadow: none;
					}
				}
			}
			.step-title {
				margin-bottom: 10px;
				font-size: 14px;
				font-weight: bold;
				color: #171719;

				.step-title-icon {
					width: 17px;
					margin-left: 5px;
				}
			}
		}
	}

	.step-size .step-title {
		margin-bottom: 30px;
	}

	.step-item .step-box {
		display: grid;
		justify-content: space-between;
		column-gap: 25px;
		row-gap: 23px;

		.item-wrap {
			position: relative;
			// overflow: hidden;

			p.normal-text,
			p.t1 {
				transition: all 0.3s;
			}

			@media (any-hover: hover) {
				&:hover {
					p.normal-text {
						color: $color-primary;

						span {
							color: #333333;
						}
					}

					p.t1 {
						color: $color-primary;
					}

					.circle2 {
						border-color: $color-primary;
						background: $color-primary;

						&::after {
							background-color: #fff;
						}
					}

					.circle3 {
						border-color: $color-primary;
						background-color: $color-primary;

						&::after {
							background: white;
						}
					}

					.zoomIcon {
						color: $color-primary !important;
					}
				}
			}

			.item {
				position: relative;
				border-radius: 6px;
				display: flex;
				flex-direction: column;
				justify-content: space-between;
				align-items: center;
				cursor: pointer;
				transition: all 0.3s;
				border: 1px solid transparent;

				.imgWrap {
					display: flex;
					justify-content: center;
					align-items: center;
					height: 100%;
					width: 100%;

					img {
						object-fit: contain;
						border-radius: 6px 6px 0 0;
					}
				}

				@media (any-hover: hover) {
					&:hover {
						border-color: $color-primary;
						box-shadow: 0 3px 4px 0 #cccccc;

						.circle {
							border-color: $color-primary;
						}
					}
				}
			}

			.item.linearBorder {
				background-image: linear-gradient(0deg, #edf1f5 0%, #f7f9fa 100%), linear-gradient(0deg, #e9ecf0, #f7f9fa);
				background-origin: border-box;
				background-clip: content-box, border-box;
				@media (any-hover: hover) {
					&:hover {
						background-image: linear-gradient(0deg, #edf1f5 0%, #f7f9fa 100%), linear-gradient(0deg, $color-primary, #f7f9fa);
						box-shadow: 0 3px 4px 0 #cccccc;
					}
				}
			}

			.textWrap {
				.normal-text {
					font-size: 16px;
					color: #333333;
					transition: all 0.3s;
					text-align: left;
				}

				.tip-text {
					color: #de3500;
					font-size: 16px;
				}
			}

			&.active {
				.circle3 {
					border-color: $color-primary;
					background-color: $color-primary;

					.checkIcon {
						color: #ffffff;
					}
				}

				.item {
					border-color: $color-primary;

					.circle {
						border-color: $color-primary;

						.inner-circle {
							border-color: $color-primary;

							&::after {
								background-color: $color-primary;
							}
						}
					}
				}

				.item.linearBorder {
					background-image: linear-gradient(0deg, #edf1f5 0%, #f7f9fa 100%), linear-gradient(0deg, $color-primary, #f7f9fa);
					box-shadow: 0 3px 4px 0 #cccccc;
				}

				.textWrap {
					.circle2 {
						border-color: $color-primary;
						background: $color-primary;

						&::after {
							background-color: #fff;
						}
					}
				}
			}

			& .textWrap {
				display: flex;
				justify-content: center;
				align-items: center;
				margin-top: 0;
				// flex-direction: column;
				// background: #f4f5f5;
				padding: 5px 0;
			}
		}

		@media screen and (min-width: 768px) and (max-width: 1499px) {
			column-gap: 12px;
			row-gap: 23px;
		}

		@media screen and (max-width: 767px) {
			column-gap: 5px;
			row-gap: 10px;

			.item-wrap {
				.item {
					border-radius: 5px;
					@media (any-hover: hover) {
						&:hover {
							background-image: none;
							box-shadow: none;
							border-color: #e6e6e6;

							.circle {
								border-color: transparent;
							}
						}
					}
				}

				.textWrap {
					padding-bottom: 0;

					.normal-text {
						font-size: 12px;
						text-align: center;
					}

					.tip-text {
						font-size: 12px;
					}
				}

				&.active {
					.item {
						border-color: $color-primary;
						background-image: none;
						box-shadow: none;

						.circle {
							border-color: $color-primary;

							.inner-circle {
								border-color: $color-primary;

								&::after {
									background-color: $color-primary;
								}
							}
						}
					}
				}

				& > .textWrap {
					display: flex;
				}
			}
		}
	}

	.step-packing.mask {
		.confirmBtnWrap {
			display: flex;
		}
	}

	.step-back {
		.step-box {
			grid-template-columns: repeat(3, 1fr);

			@media screen and (max-width: 767px) {
				grid-template-columns: repeat(2, 1fr);
			}
		}
	}

	.step-size {
		.tips.type2 {
			top: 50%;
			transform: translateY(-50%);
			// right: 1.3333vw;
			left: auto;
			font-size: 12px;
			right: 0.2vw;
		}

		.step-box {
			grid-template-columns: 2fr 1.4fr;

			.step-size-title {
				margin-bottom: 23px;
				margin-top: -25px;
				font-size: 16px;
			}

			.step-size-leftArea {
				.size-area {
					align-self: center;
					margin-bottom: 18px;

					.size-item-wrap {
						display: grid;
						grid-template-columns: repeat(2, 1fr);
						row-gap: 14px;
						column-gap: 10px;
						// grid-template-rows: 40px;
						// flex-wrap: wrap;
						font-size: 16px;

						.size-item {
							border: 1px solid transparent;
							position: relative;
							display: flex;
							// justify-content: center;
							align-items: center;
							// width: 130px;
							height: 40px;
							// margin: 0 15px 15px 0;
							background: #f4f5f5;
							// border: 1px solid #e1e3e6;
							border-radius: 10px;
							padding: 0 4px;
							// padding-left: 20px;
							padding-left: 7%;
							cursor: pointer;
							transition: all 0.3s;

							.normal-text {
								transition: all 0.3s;
								margin-bottom: 0;
								text-align: center;
							}

							.textWrap {
								display: flex;
								align-items: center;

								p {
									margin-right: 10px;
								}
							}

							@media (any-hover: hover) {
								&:hover {
									box-shadow: 0 3px 4px 0 #cccccc;
									border-color: $color-primary;

									.circle2 {
										border-color: $color-primary;
										background: $color-primary;

										&::after {
											background-color: #fff;
										}
									}
								}
							}

							.tip-icon {
								color: $color-primary;
							}
						}

						// .size-item.onlyInquiry {
						//   width: 216px;
						// }

						.size-item.active {
							border-color: $color-primary;

							.circle2 {
								border-color: $color-primary;
								background: $color-primary;

								&::after {
									background-color: #fff;
								}
							}
						}
					}
				}

				.shape-area {
					position: relative;
					background-color: #fff;

					.shape-item-wrap {
						display: grid;
						grid-template-columns: repeat(5, 1fr);
						column-gap: 10px;
						justify-content: space-between;
						margin: 10px 0 0;

						.shape-item {
							position: relative;
							display: flex;
							flex-direction: column;
							justify-content: center;
							align-items: center;
							height: 85px;
							border: 1px solid #e6e6e6;
							border-radius: 10px;
							padding: 8px 0;
							cursor: pointer;
							@media (any-hover: hover) {
								&:hover {
									border-color: $color-primary;

									.circle {
										border-color: $color-primary;

										.inner-circle {
											border-color: $color-primary;

											&::after {
												background-color: $color-primary;
											}
										}
									}
								}
							}

							.shapeIcon {
								width: 48px;
								height: 32px;
								color: #d9d9d9;
							}

							.imgWrap {
								display: flex;
								justify-content: center;
								align-items: center;
							}

							span {
								margin-top: 4px;
								white-space: nowrap;
								font-size: 16px;
								text-transform: capitalize;
							}

							.circle {
								background: #ffffff;
							}
						}

						.shape-item.active {
							position: relative;
							border-color: $color-primary;

							.shapeIcon {
								color: $color-primary;
							}

							.circle {
								border-color: $color-primary;

								.inner-circle {
									border-color: $color-primary;

									&::after {
										background-color: $color-primary;
									}
								}
							}
						}
					}
				}
			}

			.step-size-rightArea {
				display: flex;
				flex-direction: column;
				position: relative;
				padding: 20px;
				//background-color: #f6f6f6;
				@media screen and (max-width: 767px) {
					background-color: white;
					padding: 0;
				}

				&.forMB {
					display: none;
					@media screen and (max-width: 767px) {
						display: block;
					}
				}

				border-radius: 10px;

				&::before {
					position: absolute;
					left: -20px;
					top: 20px;
					content: "";
					width: 0;
					height: 0;
					border-width: 20px 0 20px 20px;
					border-style: solid;
					border-color: transparent transparent #f6f6f6;
					display: none;
					@media screen and (max-width: 767px) {
						display: none;
					}
				}

				.textWrap {
					text-align: left;

					.normal-text {
						margin-bottom: 8px;
						font-size: 18px;
						color: #333333;
						transition: all 0.3s;
						@media screen and (max-width: 767px) {
							margin-bottom: 0;
							font-size: 12px;
						}
					}

					.gray-text {
						margin-bottom: 12px;
						font-size: 14px;
						color: #9ca1a6;
					}
				}

				.shape-img {
					flex: 1;
					display: flex;
					justify-content: center;
					align-items: center;
					@media screen and (max-width: 767px) {
						margin-bottom: -20px;
					}

					img {
						height: 300px;
						object-fit: contain;
					}
				}
			}
		}
	}

	.step-metal {
		.step-box {
			grid-template-columns: repeat(3, 1fr);
			column-gap: 20px;
			row-gap: 20px;

			.item-wrap {
				.item {
					position: relative;
					display: flex;
					justify-content: center;
					align-items: center;
					// height: 118px;

					.imgWrap {
						img {
							object-fit: contain;
						}
					}
				}

				.circle {
					display: none;
				}

				.textWrap {
					margin-top: 5px;
					> div:last-of-type {
						> div > p {
							flex-direction: column;
						}
					}
				}
				@media screen and (max-width: 767px) {
					.textWrap {
						margin-top: 5px;
						> div:last-of-type {
							.PriceText {
								> .normal-text {
									display: flex;
									flex-direction: column;
								}
							}
						}
					}
				}
				//
			}
		}
	}

	.step-packing {
		.step-box {
			grid-template-columns: repeat(5, 1fr);
			column-gap: 20px;

			.item-wrap {
				.item {
					// height: 217px;
					height: 100%;

					.textWrap {
						flex-direction: column;
						justify-content: flex-start;
						height: 100%;
					}

					.imgWrap {
						height: auto;

						img {
							object-fit: cover !important;
						}
					}

					.normal-text {
						display: flex;
						flex-direction: column;
					}
				}

				.circle {
					display: none;
				}
			}
		}
	}

	@media screen and (min-width: 768px) and (max-width: 1499px) {
		.step-size {
			.step-box {
				.step-size-title {
					margin-bottom: 17px;
				}

				.step-size-leftArea {
					.size-area {
						.size-item-wrap {
							.size-item {
								// width: 120px;
								// margin: 0 10px 15px 0;
							}

							.size-item.onlyInquiry {
								// width: 196px;
							}
						}
					}
				}

				.step-size-rightArea {
					margin-left: 20px;
				}
			}
		}

		.step-metal {
			.step-box {
				column-gap: 10px;
				row-gap: 25px;

				.item-wrap {
					.item {
						height: 100px;
					}

					.textWrap {
						margin-top: 12px;
					}
				}
			}
		}

		.step-packing {
			.step-box {
				column-gap: 10px;

				.item-wrap {
					.item {
						// height: 188px;
					}
				}
			}
		}
	}

	@media screen and (max-width: 767px) {
		.step-size {
			.step-box {
				display: block;

				.step-size-title {
					margin-bottom: 8.5px;
					margin-top: -10px;
					font-size: 12px;
				}

				.step-size-leftArea {
					.size-area {
						background: #ffffff;
						border: 1px solid #d6d6d6;
						border-radius: 5px;
						padding: 12px 5px;
						@media screen and (max-width: 767px) {
							border: none;
							padding: 0;
						}

						.size-item-wrap {
							// display: flex;
							grid-template-columns: repeat(2, 1fr);
							row-gap: 0px;

							.size-item {
								justify-content: flex-start;
								flex-basis: calc(33.3% - 4px);
								height: 30px;
								margin: 0 2px 5px;
								font-size: 12px;
								background: #f4f5f5;
								border-radius: 5px;
								border-color: transparent;
								padding: 0 10px;

								p.normal-text {
									font-size: 12px;
								}
							}

							.size-item.onlyInquiry {
								flex-basis: 200px;
							}
						}
					}

					.shape-area {
						background: #ffffff;
						border: 1px solid #d6d6d6;
						border-radius: 5px;
						padding: 12px 5px;
						padding-bottom: 20px;

						.shape-item-wrap {
							grid-template-columns: repeat(3, 1fr);
							column-gap: 6.5px;
							row-gap: 15px;

							.shape-item {
								height: 66px;
								border-color: transparent;
								background: #f4f5f5;
								border-radius: 5px;

								.shapeIcon {
									width: 33px;
									height: 24px;
								}

								span {
									font-size: 12px;
								}

								.circle {
									background-color: #f4f5f5;
								}
							}
						}
					}
				}

				.step-size-rightArea {
					display: none;

					&.forMB {
						display: none;
						@media screen and (max-width: 767px) {
							display: block;
						}
					}
				}
			}
		}

		.step-metal {
			.step-box {
				grid-template-columns: repeat(3, 1fr);
				column-gap: 4px;
				row-gap: 10px;
				height: auto;

				.item-wrap {
					.item {
						height: 67px;

						&::after {
							height: 20px;
						}

						.imgWrap img {
							border-radius: 5px;
						}
					}

					.circle {
						display: block;
					}

					.textWrap {
						margin-top: 15px;
						text-align: center;
					}

					.circle2 {
						display: none;
					}
				}
			}
		}

		.step-packing {
			.step-box {
				grid-template-columns: repeat(2, 1fr);
				column-gap: 5px;
				row-gap: 13px;
				height: auto;

				.item-wrap {
					position: relative;

					.item {
						// height: 115px;
						padding: 0;
					}

					.textWrap {
						margin-top: 5px;

						.normal-text {
							text-align: center;
						}

						.tip-text {
							text-align: center;
						}
					}
				}
			}
		}

		.step-size .step-title {
			margin-bottom: 15px;
		}
	}

	.otoWrap {
		font-size: 16px;
		text-align: center;
		padding: 27px;

		h3 {
			font-size: 36px;
			font-weight: 700;
			margin-top: 10px;
			margin-bottom: 15px;
			line-height: normal;
		}

		.box {
			padding: 27px;
			background: #eff2f6;
			border-radius: 10px;
			margin-top: 33px;

			.t1 {
				font-size: 18px;
				font-weight: 700;
			}

			button {
				margin-top: 20px;
				width: 266px;
				height: 45px;
				background-color: #1a73e8;
				border-color: #1a73e8;
				font-size: 16px;
				@media (any-hover: hover) {
					&:hover {
						opacity: 0.8;
					}
				}
			}
		}
	}

	.rounded-circle {
		border-radius: 50%;
	}
}
//
// 隐藏组件里的step文字
::v-deep {
	.step-upload,
	.step-qty,
	.step-date {
		.step-title {
			> span:first-of-type,
			> span:last-of-type {
				display: none;
			}
		}
		button:focus {
			outline: none;
		}
	}
	.step-date {
		.step-box {
			grid-template-columns: repeat(2, 1fr);
		}
	}
}
//

.myswiper1 {
	.swiper-wrapper {
		justify-content: center;
		margin-top: 10px;
		margin-left: 10px;
	}
	.swiper-slide {
		display: flex;
		justify-content: center;
		align-items: center;
		overflow: hidden;
		width: 88px;
		height: 88px;
		border: 2px solid #eeeeee;
		border-radius: 10px;
		cursor: pointer;

		&.swiper-slide-thumb-active {
			border: 2px solid $color-primary !important;
		}
	}

	&.isEdit {
		.swiper-slide.swiper-slide-thumb-active {
			border: 2px solid #eeeeee;
		}
	}

	img {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}

	.playBtn {
		display: flex;
		justify-content: center;
		align-items: center;
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		width: 30px;
		height: 30px;
		background-color: rgba(0, 0, 0, 0.7);
		border-radius: 50%;

		svg {
			fill: #ffffff;
		}
	}
}

.swiper.myswiper2 {
	--swiper-navigation-color: #ffffff;
	/* 单独设置按钮颜色 */
	--swiper-navigation-size: 20px;

	/* 设置按钮大小 */
	.swiper-button-next,
	.swiper-button-prev {
		width: 46px;
		height: 46px;
		background: #000000;
		opacity: 0.4;
		border-radius: 50%;
		transform: translateY(-50%);
		margin-top: 0;
	}
}

.myswiper2 {
	// height: 100%;
	height: 500px;

	.zoom {
		position: absolute;
		top: 0;
		right: 0;
		cursor: pointer;
		z-index: 100;

		b {
			font-size: 22px;
			margin-right: 4px;
		}

		&:hover {
			b {
				color: $color-primary;
			}
		}
	}

	.swiper-slide {
		display: flex;
		justify-content: center;
		align-items: center;

		.smallImg {
			position: relative;
			width: 500px;
			height: 100%;
			text-align: center;
		}
	}

	img {
		max-width: 100%;
		max-height: 100%;
		object-fit: contain;
	}
}

//
</style>