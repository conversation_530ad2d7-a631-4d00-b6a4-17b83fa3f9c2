<template>
	<div class="modal-box" :style="modal.style" :class="modal.class">
		<template v-for="(o, oi) in modal.outer">

			<EditDiv v-if="o.title?.value" :tagName="o.title.tagName || 'h2'" v-model:content="o.title.value"
				:style="{ ...modal.titleStyle, ...o.title.style }" @click="setModalType(o.title, modal.outer, 'text')">
			</EditDiv>

			<div v-if="o.priceList?.length" class="price-list" :style="modal.priceListBoxStyle"
				@click="setModalType(o.priceList, modal.list, 'price_list')">
				<template v-for="p in o.priceList">
					<span v-if="!Number(p.value)" :style="{ ...modal.priceListStyle, ...p.style }">{{ p.value }}</span>
					<CCYRate v-else :price="p.value" :style="{ ...modal.priceListStyle, ...p.style }"></CCYRate>
				</template>
			</div>

			<div class="sub-title" :style="{ ...modal.subTitleStyle, ...o.subTitle?.style }"
				:hidden="!o.title && !o.subTitle" @click="setModalType(o.subTitle, modal.outer, 'text')">
				<EditDiv v-if="o.subTitle?.value" v-model:content="o.subTitle.value" />
			</div>

			<EditDiv v-if="o.text" v-model:content="o.text.value" :style="{ ...modal.textStyle, ...o.text.style }"
				@click="setModalType(o.text, modal.outer, 'text')" />

			<div flex class="tab-box" v-if="o.tabList" :style="modal.tabBoxStyle"
				@click="setModalType(o.tabList, modal.outer, 'nav_list')">
				<EditDiv flex pointer v-for="(t, ti) in o.tabList" v-model:content="t.value" :key="ti"
					:tagName="t.tagName || 'h3'" :primary="tabIndex == ti" @click="changeTab(t, ti)"
					:style="tabIndex == ti ? { ...modal.tabStyle, ...modal.tabSeleStyle } : modal.tabStyle" />
			</div>

			<div v-if="o.priceTable" class="price-table" :style="modal.priceTableBoxStyle"
				@click="setModalType(o.priceTable, modal.list, 'price_list')">
				<template v-for="p in o.priceTable">
					<span v-if="!Number(p.value)" :style="{ ...modal.priceTableStyle, ...p.style }">{{ p.value }}</span>
					<CCYRate v-else :price="p.value" :style="{ ...modal.priceTableStyle, ...p.style }"></CCYRate>
				</template>
			</div>


			<div v-if="modal.showTab" flex scrollbar class="icons hover-tag" :style="modal.tabStyle"
				@click="setModalType(modal.list, modal.list, 'swiper')">
				<div v-for="(l, li) in modal.list" pointer :sele="swiperIndex == li" @click="swiperIndex = li" :key="li"
					:style="swiperIndex == li ? { ...modal.iconStyle, ...modal.iconSeleStyle } : modal.iconStyle">
					<b :class="l.icon.icon" v-if="l.icon?.icon"></b>
					<pic :src="l.icon.value" :alt="l.icon.alt" v-if="l.icon?.value?.startsWith('http')"
						@click="setModalType(l.icon, modal.list, 'img', l.icon)" />

					<template v-else-if="modal.contentIntoTab">
						<div class="content" :style="{ ...modal.cardContentStyle, ...l.contentStyle }">
							<EditDiv v-if="l.title?.value" class="content-title" v-model:content="l.title.value"
								:tagName="l.title.tagName || 'h3'" :data-content="li + 1"
								:style="{ ...modal.cardTitleStyle, ...l.title.style }"
								@click="setModalType(l.title, modal.list, 'text')">
							</EditDiv>

							<EditDiv v-if="l.subTitle?.value" class="content-sub-title" :style="modal.cardSubTitleStyle"
								@click="setModalType(l.subTitle, modal.list, 'text')"
								v-model:content="l.subTitle.value">
							</EditDiv>

							<div flex :pointer="l.reviews.url" class="reviews" :style="modal.cardReviewsStyle"
								v-if="l.reviews" @click="setModalType(l.reviews, modal.list, 'reviews', l.reviews)">
								<span v-show="l.reviews.showScore && l.reviews.star > 1">{{ l.reviews.star }}</span>
								<div class="star-box" v-show="l.reviews.star > 1"
									:style="{ ...modal.cardReviewsStarStyle, backgroundImage: 'linear-gradient(90deg,' + (l.reviews.starColor || '#F96A00') + ' ' + l.reviews.star * 20 + '%,#999 0)' }">
									<b v-for="s in 5" :class="l.reviews.starIcon || 'icon-star'"></b>
								</div>
								<template v-if="l.reviews.number">
									<span :style="modal.cardReviewsExcellentStyle">{{ lang.excellent }}</span>
									<span :style="modal.cardReviewsReviewsStyle"
										v-html="l.reviews.number.toLocaleString() + '+ ' + lang.reviews + ' ' + lang.on"></span>
									<pic :style="modal.cardReviewsImgStyle" :alt="lang.shopperApproved"
										:src="l.reviews.img || 'https://sticker-static.oss-accelerate.aliyuncs.com/image/uploads/20230728/ShopperApproved.png'" />
								</template>
							</div>

							<EditDiv v-if="l.text?.value" class="content-text" :style="modal.cardTextStyle"
								@click="setModalType(l.text, modal.list, 'text')" v-model:content="l.text.value">
							</EditDiv>

							<div class="price-list" :style="{ ...modal.cardPriceListStyle, ...l.priceListStyle }"
								v-if="l.priceList" @click="setModalType(l.priceList, modal.list, 'price_list')">
								<template v-for="p in l.priceList">
									<CCYRate v-if="Number(p.value)" :price="p.value" :style="p.style"></CCYRate>
									<span v-else :style="p.style">{{ p.value }}</span>
								</template>
							</div>

							<div :style="modal.cardBtnBoxStyle" v-if="l.button || l.button1">
								<button v-if="l.button?.value" class="hover-tag"
									:primary="!l.button.outline && l.button.value" :outline="l.button.outline"
									:title="l.button.alt" :style="{ ...modal.cardBtnStyle, ...l.btnStyle }"
									@click="setModalType(l.button, modal.list, 'button', l.button)">
									<EditDiv v-show="l.button.value" tagName="label" v-model:content="l.button.value" />
									<b :class="l.button.icon" v-show="l.button.icon"></b>
								</button>

								<button v-if="l.button1?.value" class="hover-tag" :primary="!l.button1.outline"
									:outline="l.button1.outline" :title="l.button1.alt"
									:style="{ ...modal.cardBtn1Style, ...l.btn1Style }"
									@click="setModalType(l.button1, modal.list, 'button', l.button1)">
									<EditDiv v-show="l.button1.value" tagName="label"
										v-model:content="l.button1.value" />
									<b :class="l.button1.icon" v-show="l.button1.icon"></b>
								</button>
							</div>
						</div>
					</template>

					<template v-else>
						<pic :src="l.icon.pic" :alt="l.icon.alt" v-if="l.icon.pic" />
						<EditDiv v-model:content="l.icon.value" v-if="l.icon.value"
							@click="setModalType(l.icon, modal.list, 'text')" />
					</template>
				</div>
			</div>


			<b pointer v-if="modal.showStep" :class="modal.showArrow" @click="swiperIndex -= 1"
				:style="{ ...modal.arrowStyle, ...modal.arrowLastStyle }"></b>
			<ul v-if="list?.length" flex class="swipers hover-tag" :fiveItem="modal.fiveItem" :style="modal.boxStyle"
				@click.self="setModalType(modal.list, modal.list, 'swiper')" @mouseover="mouseInCard = true"
				@mouseout="mouseInCard = false">
				<b pointer v-if="!modal.showStep" :class="modal.showArrow" @click="swiperIndex -= 1"
					:style="{ ...modal.arrowStyle, ...modal.arrowLastStyle }"></b>
				<li v-for="(l, li) in list" :style="{ ...modal.cardStyle, ...l.style }" :key="li"
					:secondLast="list.length > 3 && ((swiperIndex == li + 2) || (swiperIndex == 1 && li == list.length - 1) || (swiperIndex == 0 && li == list.length - 2))"
					:last="(swiperIndex == li + 1) || (swiperIndex == 0 && li == list.length - 1)"
					:now="swiperIndex == li" :cornerLabel="l.cornerLabelType || 'left'"
					:next="(swiperIndex == li - 1) || (swiperIndex == list.length - 1 && li == 0)"
					:secondNext="list.length > 3 && ((swiperIndex == li - 2) || (swiperIndex == list.length - 1 && li == 1) || (swiperIndex == list.length - 2 && li == 0))"
					@pointerdown="mousedown" @mouseup="mouseup($event, li, 'mouse')"
					@touchend="mouseup($event, li, 'touch')" class="part2 isSwiper" :childHoverIndex="li">

					<PicVideo v-if="l.video || (l.img && ((li < swiperIndex + 9) || (li > modal.list.length - 3)))"
						:modal="modal" :data="l" :pic-style="{ ...modal.cardImgStyle }"
						:video-box-style="{ ...modal.cardVideoBoxStyle, ...l.videoBoxStyle }"
						:video-style="{ ...modal.cardVideoStyle, ...l.videoStyle }" @load.native="picLoad(li)"
						@setModalType="setModalType" />

					<div v-if="l.cornerLabel?.id" flex class="corner-label" :onlyImg="!l.cornerLabel.value"
						:style="{ color: l.cornerLabel.color, backgroundImage: 'url(' + l.cornerLabel.bgImg + ')' }">
						<pic v-if="l.cornerLabel.icon" :src="l.cornerLabel.icon" :alt="l.cornerLabel.value" />
						<EditDiv v-if="l.cornerLabel?.value" tagName="label" v-model:content="l.cornerLabel.value"
							@click="setModalType(l.cornerLabel, modal.list, 'text')" />
					</div>

					<ActivityCountDown v-if="l.countdown?.value" :countdown="l.countdown?.value"
						:countdown-box-style="{ ...modal.cardCountdownBoxStyle, ...l.countdownBoxStyle }"
						:countdown-style="{ ...modal.cardCountdownStyle, ...l.countdownStyle }"
						@click="setModalType(l.countdown, modal.list, 'text')" />

					<div class="content" v-if="!modal.contentIntoTab"
						:style="{ ...modal.cardContentStyle, ...l.contentStyle }">
						<EditDiv v-if="l.title?.value" class="content-title" v-model:content="l.title.value"
							:tagName="l.title.tagName || 'h3'" :data-content="li + 1"
							:style="{ ...modal.cardTitleStyle, ...l.title.style }"
							@click="setModalType(l.title, modal.list, 'text')">
						</EditDiv>

						<EditDiv v-if="l.subTitle?.value" class="content-sub-title" :style="modal.cardSubTitleStyle"
							@click="setModalType(l.subTitle, modal.list, 'text')" v-model:content="l.subTitle.value">
						</EditDiv>

						<div flex :pointer="l.reviews.url" class="reviews" :style="modal.cardReviewsStyle"
							v-if="l.reviews" @click="setModalType(l.reviews, modal.list, 'reviews', l.reviews)">
							<span v-show="l.reviews.showScore && l.reviews.star > 1">{{ l.reviews.star }}</span>
							<div class="star-box" v-show="l.reviews.star > 1"
								:style="{ ...modal.cardReviewsStarStyle, backgroundImage: 'linear-gradient(90deg,' + (l.reviews.starColor || '#F96A00') + ' ' + l.reviews.star * 20 + '%,#999 0)' }">
								<b v-for="s in 5" :class="l.reviews.starIcon || 'icon-star'"></b>
							</div>
							<template v-if="l.reviews.number">
								<span :style="modal.cardReviewsExcellentStyle">{{ lang.excellent }}</span>
								<span :style="modal.cardReviewsReviewsStyle"
									v-html="l.reviews.number.toLocaleString() + '+ ' + lang.reviews + ' ' + lang.on"></span>
								<pic :style="modal.cardReviewsImgStyle" :alt="lang.shopperApproved"
									:src="l.reviews.img || 'https://sticker-static.oss-accelerate.aliyuncs.com/image/uploads/20230728/ShopperApproved.png'" />
							</template>
						</div>

						<EditDiv v-if="l.text?.value" class="content-text" :style="modal.cardTextStyle"
							@click="setModalType(l.text, modal.list, 'text')" v-model:content="l.text.value">
						</EditDiv>

						<div class="price-list" :style="{ ...modal.cardPriceListStyle, ...l.priceListStyle }"
							v-if="l.priceList" @click="setModalType(l.priceList, modal.list, 'price_list')">
							<template v-for="p in l.priceList">
								<CCYRate v-if="Number(p.value)" :price="p.value" :style="p.style"></CCYRate>
								<span v-else :style="p.style">{{ p.value }}</span>
							</template>
						</div>

						<div :style="modal.cardBtnBoxStyle" v-if="l.button || l.button1">
							<button v-if="l.button?.value" class="hover-tag"
								:primary="!l.button.outline && l.button.value" :outline="l.button.outline"
								:title="l.button.alt" :style="{ ...modal.cardBtnStyle, ...l.btnStyle }"
								@click="setModalType(l.button, modal.list, 'button', l.button)">
								<EditDiv v-show="l.button.value" tagName="label" v-model:content="l.button.value" />
								<b :class="l.button.icon" v-show="l.button.icon"></b>
							</button>

							<button v-if="l.button1?.value" class="hover-tag" :primary="!l.button1.outline"
								:outline="l.button1.outline" :title="l.button1.alt"
								:style="{ ...modal.cardBtn1Style, ...l.btn1Style }"
								@click="setModalType(l.button1, modal.list, 'button', l.button1)">
								<EditDiv v-show="l.button1.value" tagName="label" v-model:content="l.button1.value" />
								<b :class="l.button1.icon" v-show="l.button1.icon"></b>
							</button>
						</div>
					</div>
				</li>
				<b pointer v-if="!modal.showStep" :class="modal.showArrow" @click="swiperIndex += 1"
					:style="{ ...modal.arrowStyle, ...modal.arrowNextStyle }"></b>
				<div class="index" v-if="modal.showIndexPage">
					<span>{{ swiperIndex + 1 }}</span>/{{ modal.list?.length }}
				</div>
			</ul>
			<b pointer v-if="modal.showStep" :class="modal.showArrow" @click="swiperIndex += 1"
				:style="{ ...modal.arrowStyle, ...modal.arrowNextStyle }"></b>


			<div class="video-box" v-if="o.video" :style="{ ...modal.videoBoxStyle, ...o.videoBoxStyle }"
				@mouseenter="setModalType(o.video, null, 'video', { type: 'enter' })"
				@mouseleave="setModalType(o.video, null, 'video', { type: 'leave' })" pointer>
				<video :src="o.video.value" :style="modal.videoStyle" :poster="o.video.poster" :title="o.video.alt"
					muted loop playsinline controls :preload="o.video.poster ? 'none' : 'metadata'"
					@click.prevent="setModalType(o.video, modal.outer, 'video', { type: 'click' })"></video>
				<b class="icon-jxsht-3d-dbf" :style="modal.manualBtnStyle" v-if="modal.manualPlay || modal.mousePlay"
					@click="setModalType(o.video, modal.outer, 'video', { type: 'click' })"></b>
				<b class="icon-a-tgsc-add" :style="modal.zoomBtnStyle" v-if="modal.manualPlay"
					@click="setModalType(o.video, null, 'video', { type: 'zoom' })"></b>
			</div>

			<div flex class="point-box" v-if="modal.showPoint || modal.showStep || modal.showIndex"
				:style="modal.pointBoxStyle">
				<div v-for="(l, li) in list" @click="swiperIndex = li" :key="li"
					:select="(swiperIndex == li) || (modal.showStep && swiperIndex > li)"
					:[String(modal.showPoint)]="typeof(modal.showPoint) =='string' && swiperIndex == li"
					:style="swiperIndex == li ? { ...modal.pointStyle, ...modal.pointSeleStyle } : modal.pointStyle">
					{{ modal.showIndex ? li + 1 : '●' }}
				</div>
			</div>

			<button v-if="o.button" :primary="o.button.value" :title="o.button.alt" :style="modal.btnStyle"
				@click="setModalType(o.button, modal.outer, 'button', o.button)">
				<EditDiv v-show="o.button.value" tagName="label" v-model:content="o.button.value" />
				<b :class="o.button.icon" v-show="o.button.icon"></b>
			</button>
		</template>
	</div>
</template>



<script>
export default {
	name: "modalSwiper",
	props: {
		data: {
			type: Object,
			default: {},
		}
	},
	data() {
		return {
			modal: {
				style: {},
				type: {},
				outer: [{}],
				...this.data,
				boxStyle: {
					...this.data.boxStyle,
					'min-height': false
				}
			},
			tabIndex: 0,
			swiperIndex: this.data.swiperIndex || 0,
			touchStartX: 0,
			mouseInCard: false
		};
	},
	computed: {
		lang() {
			return this.$store.getters.lang.layout || {};
		},
		videoList() {
			return document.querySelectorAll(`#${this.modal.id} video`) || [];
		},
		list() {
			const o = this.modal.outer[0]
			if (o.tabList?.length) {
				return this.modal.list.filter(item => item.tab === this.tabIndex)
			} else {
				return this.modal.list
			}
		}
	},
	watch: {
		modal: {
			handler(val) {
				if (process.env.isManage) this.$emit("update:data", val);
			},
			immediate: true,
			deep: true
		},
		swiperIndex(val) {
			if (val > this.list.length - 1) this.swiperIndex = 0;
			else if (val < 0) this.swiperIndex = this.list.length - 1;

			this.$nextTick(() => {
				// 切换时，icon列表滑动到当前icon位置
				let iconsBox = document.querySelector(`#${this.modal.id} .icons`);
				if (iconsBox) iconsBox.scrollLeft = Math.max(iconsBox.scrollWidth / this.list?.length * (val - (this.$store.getters.isMobile ? 2 : 3)), 0);

				// 标准流状态下，切换时，swiper列表滑动到当前swiper位置
				let swipersBox = document.querySelector(`#${this.modal.id} .swipers`);
				if (swipersBox && this.modal.scroll === true) swipersBox.style.transform = `translateX(-${Math.min(Math.max(swipersBox.scrollWidth / this.list?.length * (this.swiperIndex - 1 / this.list?.length), 0), swipersBox.scrollWidth - swipersBox.offsetWidth)}px)`
			})
		}
	},
	mounted() {
		if (process.browser) this.$nextTick(() => {
			// 自动切换滑动
			if (this.modal.autoplay) setInterval(() => this.swiperIndex += (this.mouseInCard ? 0 : 1), this.modal.autoplay * 1000);

			// 默认视频播放方式为页面滑动到所在位置自动播放
			if (!this.modal.manualPlay && !this.modal.mousePlay && this.videoList?.length) document.addEventListener("scroll", () => {
				this.videoList.forEach(i => {
					if (i.play && i.getBoundingClientRect && (i.getBoundingClientRect().top < window.innerHeight)) i.play();
				});
			})
		})
	},
	methods: {
		picLoad(index) {
			if (index !== this.swiperIndex) return

			// 防止图片切换时高度抖动
			setTimeout(() => {
				let swipersBox = document.querySelector(`#${this.modal.id} .swipers`);
				if ((swipersBox?.clientWidth || 0) < document.body.clientWidth) {
					this.modal.boxStyle = { ...this.modal.boxStyle, 'min-height': swipersBox.clientHeight + 2 + 'px' };
				}
			}, 10)
		},
		setModalType(target, targetArray, clickType, event, other) {
			if (process.env.isManage || clickType != 'img' || target.value == this.list[this.swiperIndex].img?.value)
				this.$setModal(this, target, targetArray, clickType, event, other)
		},
		mousedown(e) {
			this.touchStartX = e.clientX || e.changedTouches[0].clientX;
		},
		mouseup(e, index, type) {
			let touchEndX = e.clientX || e.changedTouches[0].clientX;
			if (Math.abs(touchEndX - this.touchStartX) > 50) {
				if (touchEndX > this.touchStartX) this.swiperIndex -= 1;
				else this.swiperIndex += 1;
			} else if (!this.$store.getters.isMobile || type == 'touch') setTimeout(() => this.swiperIndex = index, 100);
		},
		changeTab(t, ti) {
			this.tabIndex = ti;
			this.swiperIndex = 0;
		},
	}
};
</script>



<style lang="scss" scoped>
.modal-box>[primary] {
	display: flex;
}

.tab-box {
	margin: 0 auto 2rem;
	width: fit-content;

	>* {
		min-width: auto;
		height: 2.5rem;
		align-items: center;
		justify-content: center;
	}
}

.price-list {
	label:not(:last-of-type) {
		opacity: 0.7;
		padding: 0 0.2em;
		text-decoration: line-through;
	}

	label:last-of-type {
		color: #EB1D07;
		padding: 0 0.2em;
		font-weight: bold;
	}
}

.price-table {
	display: grid;
	text-align: center;
	grid-auto-flow: column;
	grid-template-rows: repeat(2, auto);
}

.icons {
	justify-content: space-between;

	>div {
		max-width: calc(7vw + 2em);

		img {
			height: 100%;
			object-fit: contain;
		}

		&:not([sele]) {
			filter: saturate(30%) grayscale(70%) contrast(0.5);

			b {
				display: none;
			}
		}

		&[sele] {
			border: 0px solid transparent;
		}
	}
}

.modal-box>b,
.swipers>b {
	&:not([class*="icon"]) {
		display: none;
	}

	&[class] {
		z-index: 10;
		line-height: 0.95em;
	}

	&:last-of-type::before {
		transform: rotate(180deg)
	}
}

.swipers {
	width: 100%;
	color: white;
	margin: 2em 0 0;
	perspective: 500px;
	align-items: center;
	justify-content: space-between;
	position: relative;

	.index {
		color: #6C655E;
		position: absolute;
		z-index: 12;
		right: 1em;
		bottom: 5.5em;

		span {
			color: #333;
			font-size: 1.5em;
			font-weight: 600;
		}
	}

	li {
		width: 100%;
		overflow: hidden;
		list-style: none;
		transition: all 0.3s ease;
		transform-style: preserve-3d;
		position: absolute;

		.corner-label {
			grid-gap: 0.25em;
			align-items: center;
			padding: 0.3em 1em;
			font-size: calc(1rem - 2px);
			background-size: 100% 100%;
			position: absolute;
			top: 0.75em;

			img {
				width: auto;
				height: 1.75em;
			}

			&[onlyImg] {
				padding: 0;

				img {
					height: 2em;
				}
			}
		}

		&[cornerLabel~='left'] .corner-label {
			left: 1em;
		}

		&[cornerLabel~='right'] .corner-label {
			right: 1em;
		}

		&[cornerLabel~='edge'] {

			.corner-label {
				top: 0;
			}

			&[cornerLabel~='left'] .corner-label {
				left: 0;
			}

			&[cornerLabel~='right'] .corner-label {
				right: 0;
			}
		}

		&[cornerLabel~='out'] {
			overflow: unset;

			.corner-label {
				top: -0.5em;

				&[onlyImg] img {
					width: 4.8em;
					height: auto;
				}
			}

			&[cornerLabel~='left'] .corner-label {
				left: -0.5em;
			}

			&[cornerLabel~='right'] .corner-label {
				right: -0.5em;
			}
		}

		&:not([now]):not([last]):not([next]):not([secondLast]):not([secondNext]) {
			width: 0 !important;
			transform: translateZ(-20vw) !important;
		}

		img {
			height: 100%;
			border-color: transparent;
			-webkit-user-drag: none;
			-webkit-user-select: none;
		}

		.content {
			padding: 1.5em 2.5vw;
			position: absolute;
			bottom: 0;
			right: 0;
			left: 0;

			.content-title {
				font-size: 1.3em;
			}

			.content-sub-title,
			.content-text {
				margin-top: 0.7em;
			}
		}

		&:not([now]) .content,
		&:not([now]) .content-sub-title,
		&:not([now]) .content-text {
			display: none;
		}
	}

	[now] {
		height: 100%;
		margin: 0 auto;
		transform: none !important;
		position: relative;
		right: auto !important;
		left: auto !important;
		z-index: 10;

		.content,
		.content-sub-title,
		.content-text {
			display: block;
		}
	}

	[last] {
		transform: rotateY(10deg) translateZ(-10vw);
		right: auto !important;
		left: -7%;
	}

	[next] {
		transform: rotateY(-10deg) translateZ(-10vw);
		left: auto !important;
		right: -7%;
	}

	&:not([fiveItem]) [secondLast],
	&:not([fiveItem]) [secondNext] {
		opacity: 0;
	}

	&[fiveItem] {
		[secondLast] {
			transform: translateZ(-42.19vw);
			right: auto !important;
			left: 53% !important;
		}

		[secondNext] {
			transform: translateZ(-42.19vw);
			left: auto !important;
			right: 53% !important;
		}
	}
}

.video-box {
	height: 100%;
}

.point-box {
	grid-gap: 1em;
	font-size: 0.9em;
	line-height: 1em;
	margin-top: 1.5em;
	align-items: center;
	justify-content: center;

	div:not([select]) {
		opacity: 0.2;
	}

	// 椭圆
	[primary] {
		--radius-btn: 3em;
		padding: 0;
		height: 0.8em;
		min-height: auto;
		color: transparent;
		margin-bottom: 0.1em;
		min-width: 1.5em !important;
	}

	// 空心点
	[circle] {
		color: white;
		font-size: 1.125em;
		-webkit-text-stroke: 0.25em $color-primary;
	}

	// 实心点
	[cylinder] {
		transform: scaleX(1.25);
		text-shadow: 0.2em 0px 0px white, -0.2em 0px 0px white;
	}

	// 圈
	[ring] {
		background: radial-gradient($color-primary 33%, transparent 39%, $color-primary 80%) !important;
	}
}





.fluctuation .swipers {

	[next] {
		top: calc(-1.5em - 5vw);
		right: -60%;
		z-index: 3;
	}

	[secondnext] {
		right: -150% !important;
		z-index: 2;
	}

	[last] {
		top: calc(-1.5em - 5vw);
		left: 200% !important;
		z-index: 1;
	}
}


.hover-arrow .swipers>b:hover {
	color: white !important;
	border-radius: 1em;
	transform: scale(1.14);
	background: linear-gradient(to right, transparent 8%, var(--color-primary-lighten) 0, var(--color-primary-lighten) 92%, transparent 0);
}


.hover-show-arrow:not(:hover) .swipers>b {
	display: none;
}


.five-item .swipers {

	[last],
	[next] {
		opacity: 0.8;
	}

	[secondLast],
	[secondNext] {
		opacity: 0.5;
		top: -32vw;
		transform: translateZ(-42.19vw) !important;
	}
}


.five-item2 {
	.swipers {
		[now] {
			background-image: url(https://customed-center.oss-us-west-1.aliyuncs.com/web/2024/other/20240422/317Phone-Background.png);
			background-position: center;
			// min-height: 44em;
			padding: 3em 2.3em;
			padding-top: 4.7em;

			>img {
				border-radius: 0.85em;
			}

			>.content {
				.content-title {
					text-align: center;
					position: relative;
					width: fit-content;
					margin: auto;
					padding-left: 1em;

					&::after {
						content: attr(data-content);
						position: absolute;
						color: rgb(255, 255, 255);
						width: 1.375em;
						height: 1.375em;
						line-height: 1.375em;
						border-radius: 50%;
						background-image: linear-gradient(to right, rgb(82, 70, 255), rgb(178, 37, 251));
						box-shadow: rgb(168, 168, 168) 2px 0px 0.6em 0.1em;
						font-size: 0.8em;
						top: 0px;
						left: -0.5em;
						z-index: -1;
						margin-top: 3px;
						font-weight: initial;
					}
				}
			}
		}

		[secondLast] {
			top: 0;
			left: 0 !important;
			right: initial !important;
			// transform: rotateY(10deg) translateZ(-10vw) !important;
			transform: scale(0.25, 0.65) rotateY(26deg) !important;
		}

		[secondNext] {
			top: 0;
			left: initial !important;
			right: 0 !important;
			// transform: rotateY(-10deg) translateZ(-10vw) !important;
			transform: scale(0.25, 0.65) rotateY(-26deg) !important;
		}

		[last],
		[next] {
			top: 1em;
			z-index: 9;
		}

		[last] {
			left: 15%;
			transform: scale(0.5, 0.65) rotateY(26deg) !important;
		}

		[next] {
			right: 15%;
			transform: scale(0.5, 0.65) rotateY(-26deg) !important;
		}

		[next],
		[secondNext],
		[secondLast],
		[last] {
			height: auto;
			overflow: initial;

			&::before {
				content: '';
				position: absolute;
				bottom: 0;
				z-index: -1;
				width: 100%;
				height: 15%;
				display: inline-block;
				transform-origin: right bottom;
				// transform: rotateX(180deg) rotateZ(147deg) rotateY(180deg) scaleX(1);
				background: linear-gradient(to top, #00000076, transparent);
			}

		}

		[secondLast]::before {
			transform: rotateX(154deg) rotateZ(-180deg) rotateY(180deg) scaleX(1);
		}

		[last]::before {
			transform: rotateX(147deg) rotateZ(-180deg) rotateY(180deg) scaleX(1);
		}

		[next]::before {
			height: 11%;
			transform: rotateX(180deg) rotateZ(147deg) rotateY(180deg) scaleX(1);
		}

		[secondNext]::before {
			transform: rotateX(180deg) rotateZ(147deg) rotateY(180deg) scaleX(1);
		}
	}

	.point-box {
		>div {
			opacity: 1;
			color: #EBEBEB;
			cursor: pointer;
		}

		opacity: 1;

		[select] {
			color: #C2C2C2;
		}
	}
}


.h2-bg h2 {
	display: flex;
	align-items: flex-start;
	justify-content: center;
	column-gap: 0.45em;

	&::before,
	&::after {
		all: unset;
		content: "";
		display: block;
		width: 6.6em;
		height: 1em;
		background-size: contain;
		background-repeat: no-repeat;
	}

	&::before {
		background-image: url('https://static-oss.gs-souvenir.com/web/quoteManage/20250304/Personalized_Cufflinks_for_Groom_20250304BD4bSR.png');
	}

	&::after {
		background-image: url('https://static-oss.gs-souvenir.com/web/quoteManage/20250304/Personalised_Cufflinks_Wedding_20250304jbdyWY.png');
	}
}


.swiperPoint1 .point-box {
	gap: 0.5rem;

	div {
		margin-bottom: 0;
		min-width: 1.75rem;
		height: 0.875rem !important;

		&:not([select]) {
			font-size: 0;
			margin-bottom: 0;
			min-width: auto;
			width: 0.875rem;
			height: 0.875rem !important;
			cursor: pointer;
			border-radius: 50%;
			background-color: var(--text-primary);
		}
	}
}


.swiperPoint_E0E0E0 .point-box {
	div {
		&:not([select]) {
			background-color: #E0E0E0;
			opacity: 1;
		}
	}
}


.check-color-primary [now] {
	color: var(--color-primary);

	img {
		border-color: var(--color-primary);
	}
}


.modal-box.img-swiper {
	display: flex;
	padding: 2.5em;
	align-items: center;
	justify-content: center;
	flex-direction: column-reverse;


	.icons {
		display: flex;
		overflow: auto;
		grid-gap: 0.5em;
		max-width: 63.5em;

		>div {
			height: 7.5em;
			min-width: 7.5em;
			border-radius: 0.8em;

			img {
				object-fit: cover;
				border-radius: 0.8em;
			}
		}
	}

	.swipers {
		margin-top: 0;
		text-align: center;

		>b {
			width: 2.4em;
			font-size: 1.2em;
			border-radius: 50%;
			line-height: 2.35em;
			background: rgba(196, 196, 196, 0.43);
		}

		.index {
			display: none;
		}

		li {
			width: 37.5em;
			min-height: 43.5em;

			img {
				height: auto;
				border-radius: 1.25em;
			}

			.content {
				font-size: calc(1em - 2px);
				position: static;
			}
		}

		[last] {
			transform: rotateY(-20deg) translateZ(-6.5em) scale(0.6, 1.05);
			left: -12em;
			top: 0.25%;

			img {
				border-radius: 2em;
			}
		}

		[next] {
			transform: rotateY(20deg) translateZ(-6.5em) scale(0.6, 1.05);
			right: -12em;
			top: 0.25%;

			img {
				border-radius: 2em;
			}
		}

		[secondLast] {
			left: 0 !important;
			transform: translateZ(-5em) !important;
		}

		[secondNext] {
			right: 0 !important;
			transform: translateZ(-5em) !important;
		}
	}
}


.stickers .icons {
	width: 56em;
	padding: 2em;
	margin: 0 auto;
	border-radius: 5em;
	justify-content: space-around;
	background: var(--color-primary);
	box-shadow: 0.1em 0.4em var(--color-primary-darken);
	position: relative;
	z-index: 1;

	>div:not([sele]) {
		filter: brightness(10%) invert(1);
	}
}


.neon {
	.swipers>b {
		border: none;
		color: #DBDBDB;
		font-size: calc(1em + 1vw);
		position: absolute;
		bottom: -2.95em;

		&::before {
			content: "\e6a9";
		}

		&.icon-jiantou1 {
			transform: translateX(-6em) rotate(90deg);
		}

		&.icon-jiantou2 {
			transform: translateX(6em) rotate(-90deg);
		}
	}

	.point-box {
		font-size: calc(1em + 1vw);
	}
}


.neonLeft .icons {
	b {
		font-size: 1.2em;
		font-weight: bold !important;
	}

	&:not([sele]) {
		filter: opacity(70%) saturate(0) !important;
	}

	&[sele] {
		opacity: 0.5;
		color: var(--color-primary);
		background-clip: padding-box, border-box;
		background-origin: padding-box, border-box;
		background-image: linear-gradient(90deg, #fafafa, #fafafa), linear-gradient(90deg, var(--color-bright) -40%, var(--color-primary), var(--btn-primary) 110%);
	}
}


.bpsPolice .icons>div {
	&:nth-child(3) {
		b {
			font-size: 1.3em;
		}
	}

	b {
		font-size: 1.6em;
	}

	&[sele] {
		color: white;
		background-color: var(--btn-primary);
	}
}


.bpsHomeLeft .icons>div[sele] {
	color: white !important;
	background-color: var(--color-primary) !important;

	&::after {
		content: "\e77f";
		display: block;
		font-size: 0.7em;
		font-family: "modalicon";
		transform: rotate(-45deg);
	}
}


.patchesHomeLeft .icons>div[sele]::after {
	content: ">";
	display: block;
	margin-left: 0.25em;
}


.text-tab .icons>div[sele] {
	position: relative;

	&::after {
		// content: '▼';
		content: '';
		width: 1em;
		border-top: 1em solid #515151;
		border-right: 1em solid transparent;
		border-left: 1em solid transparent;
		position: absolute;
		bottom: -20%;
		left: 0;
		right: 0;
		margin: auto;
		z-index: 99;
	}
}


.nylon-lanyards {
	.swipers {

		[last],
		[next] {
			transform: rotateY(0deg) translateZ(0vw) !important;
			width: 21.75em !important;
			height: 31.25em;
			opacity: .87;
			filter: brightness(0.7) sepia(0.3);

			img {
				height: 100%;
				border-radius: 8px
			}
		}

		.icon-shangyige::before {
			font-size: 1.4em;
		}

		.icon-shangyige:first-child {
			left: 5.875em;
		}

		.icon-shangyige:last-child {
			right: 5.875em;
		}

		.icon-shangyige:first-child::after {
			content: "PREV";
			font-size: 1.5em;
		}

		.icon-shangyige:last-child::after {
			content: "NEXT";
			order: -1;
			font-size: 1.5em;
		}
	}

	.point-box {
		div {
			&:not([select]) {
				color: #6E8CC4 !important;
			}
		}

		[primary] {
			background-image: none;
			background-color: #fff !important;
		}
	}
}


.polyester-lanyards {
	.swipers {

		[last],
		[next] {
			width: 29em !important;
			height: auto;
			top: .6em;
			opacity: 1;
			filter: none;

			img {
				height: 100%;
				border-radius: none
			}
		}

		[secondLast] {
			left: 50% !important;
			top: -12.3vw !important;
			transform: translateZ(-12.65vw) translateX(-50%) !important;
			opacity: 1;
		}

		.icon-shangyige:first-child {
			left: 5.4em;
		}

		.icon-shangyige:last-child {
			right: 5.4em;
		}
	}
}


.five-flat-item {
	.swipers[fiveItem] {
		[now] {
			width: 36.75em !important;
			left: 0 !important;
			right: 0 !important;
			margin: auto;
		}

		[next],
		[last] {
			width: 29em !important;
			// transform: rotateY(0deg) translateZ(-5vw) !important;
			transform: initial !important;
			top: 10% !important;
			z-index: 9;
		}

		[next] {
			right: 13% !important;
		}

		[last] {
			left: 13% !important;
		}

		[secondNext],
		[secondLast] {
			width: 25.75em !important;
			transform: initial !important;
			top: 15%;
			z-index: 8;
		}

		[secondNext] {
			left: initial !important;
			right: 0 !important;
		}

		[secondLast] {
			left: -9em !important;
		}

	}

	.icon-shangyige:first-child {
		left: 35%;
	}

	.icon-shangyige:last-child {
		right: 35%;
	}

	.icon-shangyige:first-child::after {
		content: "PREV";
		font-size: 1.5em;
	}

	.icon-shangyige:last-child::after {
		content: "NEXT";
		order: -1;
		font-size: 1.5em;
	}
}


.five-flat-item-mask {
	.swipers[fiveItem] {
		[now] {
			width: 28.32em !important;

			img {
				opacity: 1 !important;
			}
		}

		[next],
		[last] {
			top: auto !important;
			width: 20.3em !important;
		}

		[secondNext],
		[secondLast] {
			top: auto !important;
			width: 13.69em !important;
		}

		[secondLast] {
			left: 0 !important;
		}
	}

	.point-box [primary] {
		margin: 0.16em 0 0;
		min-width: 0.79em !important;
		height: 0.5em;
	}
}


.five-flat-lanyards {
	.swipers[fiveItem] {
		[now] {
			width: 46em !important;
		}

		[next],
		[last] {
			top: auto !important;
			width: 36em !important;
		}

		[next] {
			right: 14.1% !important;
		}

		[last] {
			left: 14.1% !important;
		}

		[secondNext],
		[secondLast] {
			top: auto !important;
			width: 26.4em !important;
		}

		[secondNext] {
			right: 4.125em !important;
		}

		[secondLast] {
			left: 4.125em !important;
		}
	}
}


.five-flat-ornaments {
	.swipers[fiveItem] {
		[now] {
			width: 49.75rem !important;
		}

		[next],
		[last] {
			top: auto !important;
			width: 37.75rem !important;
		}

		[next] {
			right: 13.875% !important;
		}

		[last] {
			left: 13.875% !important;
		}

		[secondNext],
		[secondLast] {
			top: auto !important;
			width: 30.125rem !important;
		}

		[secondNext] {
			right: 4.75rem !important;
		}

		[secondLast] {
			left: 4.75rem !important;
		}
	}
}


.five-item-gs-hats {
	.swipers[fiveItem] {
		li {
			width: auto;
		}

		[next],
		[last] {
			z-index: 9;
			transform: scale(0.8) !important;
		}

		[next] {
			right: 13.875% !important;
			transform-origin: right center;
		}

		[last] {
			left: 13.875% !important;
			transform-origin: left center;
		}

		[secondNext],
		[secondLast] {
			z-index: 8;
			transform: scale(0.6) !important;
		}

		[secondNext] {
			right: 0 !important;
			transform-origin: right center;
		}

		[secondLast] {
			left: 0 !important;
			transform-origin: left center;
		}

		@include respond-to(mb) {
			li {
				width: 57vw;
			}

			[next],
			[last] {
				transform: scale(0.7) !important;
			}

			[next] {
				right: 0 !important;
			}

			[last] {
				left: 0 !important;
			}

			[secondNext],
			[secondLast] {
				pointer-events: none;
			}
		}
	}

	b:hover {
		color: #fff !important;
		opacity: 1 !important;
		background-color: var(--color-primary) !important;
	}

	.point-box [select] {
		border: 2.5px solid var(--color-primary);
		background-color: #fff !important;
		box-sizing: initial !important;
	}
}


.five-flat-pathes {
	.swipers[fiveItem] {
		[now] {
			width: 37.3em !important;
		}

		[next],
		[last] {
			top: auto !important;
			width: 23.9em !important;
		}

		[next] {
			right: 0 !important;
		}

		[last] {
			left: 0 !important;
		}

		[secondNext],
		[secondLast] {
			top: -0.5em !important;
			width: 19.9em !important;
			-webkit-filter: blur(0.2em);
			filter: blur(0.2em);
		}

		[secondNext] {
			right: 12.8em !important;
		}

		[secondLast] {
			left: 12.8em !important;
		}
	}

}


.pathes-point-box .point-box [primary] {
	margin: 0.16em 0 0;
	min-width: 0.84em !important;
	height: 0.5em;
}


.wedding-cufflinks {
	.swipers {
		&>b:first-child {
			left: -1%;
		}

		&>b:last-child {
			right: -1%;
		}
	}

	[last],
	[next] {
		top: 4.5%;
		width: 19.4% !important;
		transform: rotateY(0) translateZ(0);
	}

	[last] {
		left: 18.67%;
	}

	[next] {
		right: 18.67%;
	}

	[secondNext],
	[secondLast] {
		top: 9%;
		width: 17.57% !important;
		transform: translateZ(0) !important;
	}

	[secondLast] {
		left: 0 !important;
	}

	[secondNext] {
		left: auto !important;
		right: 0 !important;
	}

	.content button b {
		order: -1;
		font-size: 0.7em
	}
}


.five-item-parallel .swipers {
	li {
		width: 18.75%;
	}

	[now] {
		width: 24.27%;
	}

	[secondLast] {
		left: -1.5% !important;
		right: auto !important;
	}

	[last],
	[next] {
		left: 18.2%;
		right: 18.2%;
	}

	[secondNext] {
		left: auto !important;
		right: -1.5% !important;
	}
}


.hover-arrow-primary .swipers>b:hover {
	padding-top: 1px;
	border-radius: 50%;
	color: white !important;
	box-shadow: 0 0 1px var(--color-primary);
	background: linear-gradient(90deg, var(--color-primary), var(--btn-primary) 150%);
}


.hover-arrow-primary-ornaments .swipers>b:hover {
	color: var(--color-primary) !important;
}


.onlyShowNow .swipers>li:not([now]) {
	transform: scale(0)
}


.modal-box .reviews {
	grid-gap: 0.3em;
	flex-wrap: wrap;
	width: max-content;
	line-height: 1.5em;
	align-items: center;
	max-width: 100%;

	>span {
		line-height: 1em;
	}

	.star-box {
		font-size: 1.2em;
		background-clip: text;
		-webkit-background-clip: text;

		b {
			color: transparent;
			margin-right: 0.1vw;
		}
	}

	img {
		width: auto;
		height: 1.5em;
	}
}


.neonsigns-point .point-box {
	position: absolute;
	bottom: 1em;
	left: 50%;
	z-index: 10;
	grid-gap: 0.5em;
	transform: translateX(-50%);

	div {
		opacity: 1;
		width: 1.3rem;
		height: 1.3rem;
		cursor: pointer;
		color: transparent;
		border-radius: 50%;
		background: radial-gradient(white 33%, transparent 39%);

		&[select] {
			background: radial-gradient($color-primary 33%, transparent 39%, $color-primary 80%);
		}
	}

	@include respond-to(mb) {
		font-size: 0.8em;
	}
}


.no-transform .swipers {

	[last],
	[next],
	[secondNext],
	[secondLast] {
		transform: rotateY(0) translateZ(0);
	}
}


.patches-right .swipers {

	[last],
	[next] {
		width: 30% !important;
	}

	[last] {
		left: 0;
		mask-image: linear-gradient(to right, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.8) 135%);
	}

	[next] {
		right: 0;
		mask-image: linear-gradient(to left, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.8) 135%);
	}
}


.five-flat-gap {
	.swipers[fiveItem] {

		.icon-shangyige:first-child::after,
		.icon-shangyige:last-child::after {
			content: "";
		}

		b:hover {
			background: var(--btn-primary);
			color: #fff !important;
		}

		[now] {
			width: 35.69em !important;
		}

		[next],
		[last] {
			width: 30em !important;
			top: auto !important;
		}

		[secondNext],
		[secondLast] {
			width: 26em !important;
			top: auto !important;
		}

		[next] {
			right: 3.8% !important;
		}

		[last] {
			left: 3.8% !important;
		}

		[secondNext] {
			right: -23.2em !important;
		}

		[secondLast] {
			left: -23.2em !important;
		}
	}

	.point-box {
		div {
			width: 2.8em;
			background: #DADADA;
			border-radius: 1em;
			opacity: 1;
		}

		[primary] {
			width: 4.76em;
			background: #626262;
		}
	}
}


.content-collapse-flat li {
	&:not([now]) {
		.content {
			width: 0 ! important;
			height: 0 ! important;
			padding: 0 ! important;
			display: block ! important
		}
	}

	&:not([now]):not([last]):not([next]):not([secondLast]):not([secondNext]) {
		display: none ! important;
	}
}


.just-now-visible li:not([now]) {
	display: none ! important
}


.tab-swiper {
	.icons {
		img {
			border-radius: 0.875em;
		}

		[sele]::before {
			content: "";
			display: block;
			width: 100%;
			height: 100%;
			position: absolute;
			background: url(https://static-oss.gs-souvenir.com/web/quoteManage/20241230/CHOOSE_2066yS8REx.png) center rgba(0, 0, 0, 0.62) no-repeat;
			border-radius: 0.875em;
		}
	}
}


.six-grid-tab {
	.icons {
		--bg: #fff;
		--sele-bg: #f3f4f5;
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		grid-template-rows: repeat(2, 1fr);
		grid-auto-rows: 1fr;
		padding: 3.75rem 3.75rem 3.75rem 6.5rem;
		border-radius: 1.25rem;
		background-color: var(--bg);

		@include respond-to(mb) {
			grid-template-columns: repeat(2, 1fr);
			grid-template-rows: repeat(3, 1fr);
			padding: 0;
		}
	}

	.icons>div {
		position: relative;
		max-width: unset;
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 0.75rem;
		border-right: 1px solid #b5b5b5;
		border-bottom: 1px solid #b5b5b5;
		filter: unset;

		&::after {
			content: '';
			position: absolute;
			top: 100%;
			left: 100%;
			width: 2.5rem;
			height: 2.5rem;
			z-index: 1;
			border-radius: 50%;
			background-color: var(--bg);
			transform: translate(-50%, -50%);

			@include respond-to(mb) {
				width: 1rem;
				height: 1rem;
			}
		}

		@include respond-to(pc) {
			&:nth-child(3n) {
				border-right: none;

				&:after {
					display: none
				}
			}

			&:nth-child(n+4) {
				border-bottom: none;

				&:after {
					display: none
				}
			}
		}

		@include respond-to(ipad) {
			&:nth-child(3n) {
				border-right: none;

				&:after {
					display: none
				}
			}

			&:nth-child(n+4) {
				border-bottom: none;

				&:after {
					display: none
				}
			}
		}

		@include respond-to(mb) {
			&:nth-child(2n) {
				border-right: none;

				&:after {
					display: none
				}
			}

			&:nth-child(n+5) {
				border-bottom: none;

				&:after {
					display: none
				}
			}
		}
	}

	.icons>div:not([sele]) img {
		opacity: 0.3;
	}

	.icons>div[sele] img {
		border-radius: 0.75rem;
		background-color: var(--sele-bg);
	}

	.swipers {
		li {
			opacity: 0.3;
			height: unset;
			transform: unset;
			transition: unset;
			left: auto !important;
			right: auto !important;
		}

		[now] {
			opacity: 1;
			left: 2.75rem !important;
			top: 2.75rem !important;

			@include respond-to(mb) {
				left: 1rem !important;
				top: 1rem !important;
			}
		}
	}
}





// pc跟ipad端样式
@media screen and (min-width: $mb-width) {
	.modal-box.img-swiper {
		width: 87em;
		max-width: 97vw;
		background: #111;
		border-radius: 1.5em;

		.icons>div {
			img {
				padding: 0.1em;
			}

			&:not([sele]) {
				filter: none;
			}

			&[sele] {
				border: 1px solid $color-primary;
			}
		}
	}
}







// mb端样式
@media screen and (max-width: $mb-width) {
	.icons>div b {
		display: none;
	}

	.swipers {
		[last] {
			transform: rotateY(30deg) translateZ(-20vw) scale(0.66, 0.9);
		}

		[next] {
			transform: rotateY(-30deg) translateZ(-20vw) scale(0.66, 0.9);
		}
	}

	.point-box {
		margin-top: 1em;
	}


	.five-item2 .swipers {
		[now] {
			padding: 1em;
			background-size: contain;

			>.content .content-title::after {
				margin-top: 1px;
			}
		}

		[secondLast] {
			left: -2em !important;
			transform: scale(0.45, 0.7) rotateY(43deg) !important;
		}

		[secondNext] {
			right: -2em !important;
			transform: scale(0.45, 0.7) rotateY(-43deg) !important;
		}

		[last],
		[next] {
			top: 0;
		}

		[last] {
			left: 9%;
			transform: scale(0.6, 0.7) rotateY(40deg) !important;
		}

		[next] {
			right: 9%;
			transform: scale(0.6, 0.7) rotateY(-40deg) !important;
		}

		// 阴影
		[secondLast]::before {
			transform: rotateX(154deg) rotateZ(-180deg) rotateY(180deg) scaleX(1);
			transform: rotateX(-92deg) rotate(-168deg) rotateY(183deg) scaleX(1.05);
			height: 29%;
		}

		[last]::before {
			transform: rotateX(147deg) rotateZ(-180deg) rotateY(180deg) scaleX(1);
			transform: rotateX(-89deg) rotate(-180deg) rotateY(180deg) scaleX(1);
			height: 24%;
		}

		[next],
		[secondNext] {
			&::before {
				transform: rotateX(257deg) rotate(147deg) rotateY(180deg) scaleX(1);
			}
		}
	}


	.modal-box.img-swiper {
		padding: 0;
		width: 94vw;
		grid-gap: 3vw;

		.icons {
			grid-gap: 1vw;
			max-width: 100%;

			>div {
				height: 4.62em;
				min-width: 4.62em;
			}
		}

		.swipers {
			padding: 0;
			text-align: center;

			>b {
				font-size: 1em;
				background: rgba(0, 0, 0, 0.43);
				position: absolute;
				z-index: 15;
				left: 2vw;

				&:last-of-type {
					left: auto;
					right: 2vw;
				}
			}

			.index {
				display: block;
			}

			li {
				width: 100%;
				min-height: auto;
				border-radius: 0.8em;
				background: #333333;

				img {
					height: 93vw;
					border-radius: 0.8em;
				}

				.content {
					padding: 1em;

					.content-sub-title {
						margin-top: 0.3em;
					}
				}

				&:not([now]) {
					display: none;
				}
			}
		}
	}


	.stickers {
		.icons {
			width: 68vw;
			height: 11vw;
			font-size: 1.5vw;

			>div {
				max-width: 10vw;
			}
		}

		.swipers>b[class] {
			border: none;
			position: absolute;
			bottom: -30%;

			&:first-child {
				left: 27%;
			}

			&:last-child {
				right: 27%;
			}
		}
	}


	.neonLeft .icons>div:not([sele]) {
		border: 1px solid !important;
	}


	.bpsPolice .icons>div:not([sele]) {
		filter: none;
		color: #666666;
		background-color: #F5F5F5;
	}


	.bpsHomeLeft .icons [sele]::after {
		display: none;
	}


	.nylon-lanyards .swipers {

		[last],
		[next] {
			transform: none;
			width: 65.3vw !important;
			height: auto;
			filter: none;
			opacity: 1;
		}

		[now] {
			left: 50% !important;
			transform: translateX(-50%) !important;
		}

		[secondLast] {
			left: auto !important;
		}
	}

	.three-flat-enforcement {
		.swipers {
			[now] {
				width: 83% !important;
				top: 0 !important;
				left: 0 !important;
				margin-left: 0;
			}

			[next] {
				transform: translateZ(0vw) !important;
				width: 83% !important;
				left: 88% !important;
				opacity: .3;
			}

			[last] {
				display: none;
			}
		}
	}


	.five-flat-item {
		.swipers[fiveItem] {

			[now],
			[next],
			[last] {
				width: 24.5em !important;
				top: 0 !important;
			}

			[secondNext],
			[secondLast] {
				display: none;
			}

			[next] {
				right: -72% !important;
			}

			[last] {
				left: -72% !important;
			}
		}
	}


	.five-flat-item-mask {
		.swipers[fiveItem] {
			[now] {
				width: 70.4vw !important;
			}

			[next],
			[last] {
				top: auto !important;
				width: 39.47vw !important;
			}

			[next] {
				right: 0 !important;
			}

			[last] {
				left: 0 !important;
			}
		}
	}


	.five-flat-lanyards {
		.swipers[fiveItem] {
			[now] {
				width: 72vw !important;
			}

			[next],
			[last] {
				top: auto !important;
				width: 44.82vw !important;
			}

			[next] {
				right: 0.5em !important;
			}

			[last] {
				left: 0.5em !important;
			}
		}
	}


	.five-flat-ornaments {
		.swipers[fiveItem] {
			[now] {
				width: 70.67vw !important;
			}

			[next],
			[last] {
				top: auto !important;
				width: 53.87vw !important;
			}

			[next] {
				right: 0.5em !important;
			}

			[last] {
				left: 0.5em !important;
			}
		}
	}


	.wedding-cufflinks {

		[last],
		[next] {
			width: 44.4% !important;
		}

		[last] {
			left: -22.2%;
		}

		[next] {
			right: -22.2%;
		}

		[secondNext],
		[secondLast] {
			width: 0 !important;

			.content {
				display: none !important;
			}
		}

		.content button b {
			font-size: 0.8em
		}
	}


	.modal-box .reviews {
		font-size: 2.6vw;

		.star-box {
			font-size: 1em;
		}
	}


	.patches-right .swipers {

		[last],
		[next] {
			width: 34% !important;
		}

		[last] {
			left: -8%;
		}

		[next] {
			right: -8%;
		}
	}


	.five-flat-gap {
		.swipers[fiveItem] {

			[now],
			[next],
			[last] {
				top: auto !important;
				width: 20.92em !important;
			}

			[next] {
				right: -59.8% !important;
			}

			[last] {
				left: -59.8% !important;
			}
		}

		.point-box {
			div {
				width: 1.85em;
			}

			[primary] {
				height: 0.6em;
				width: 3.06em;
			}
		}
	}


	.tab-swiper {
		.icons {
			img {
				border-radius: 0.58em;
			}

			[sele]::before {
				background-size: 2.5em auto;
				border-radius: 0.58em;
			}
		}
	}


	.five-flat-pathes {
		.swipers[fiveItem] {
			[now] {
				width: 85% !important;
			}

			[next],
			[last] {
				top: auto !important;
				width: 16em !important;
			}

			[next] {
				right: -14.5em !important;
			}

			[last] {
				left: -14.5em !important;
			}

			[secondNext],
			[secondLast] {
				display: block;
				width: 13.3em !important;
			}

			[secondNext] {
				right: -5em !important;
			}

			[secondLast] {
				left: -5em !important;
			}
		}

	}


	.text-tab .icons {
		>div>img {
			border: 1px solid transparent;
		}

		>div[sele] {
			>img {
				border: 1px solid var(--btn-second);
				border-radius: 0.5em;
				background-color: #FFF !important;
			}
		}
	}
}
</style>
