<template>
	<div id="StatisticTab">
		<div class="tab-container">
			<div class="tab">
				<v-tabs v-model="tab" background-color="#F5F6FA" color="#333333">
					<v-tab v-for="(item, index) in statistics" v-permission="`${item.permission}`" :key="item.name"
						   :ripple="false" class="tab-item-title"
						   @click="tabChange(index)">
						{{ item.name }}
						<div class="circle"></div>
					</v-tab>
				</v-tabs>
				<!-- <v-tabs background-color="#F5F6FA" height="40" v-model="tabs" @change="changeTab">
							<v-tab :ripple="false" class="tab-item-title" v-for="item in statistics" :key="item.title">
								{{ item.title }}
							</v-tab>
						</v-tabs> -->
			</div>

			<!-- 筛选按钮 -->
			<div class="filter-area">
				<div class="select-container-1">
					<v-select :items="filterByProduct" v-model="productTypeDefault" dense solo label="Product Type" flat
							  @change="changeFiltconditionType">
					</v-select>
				</div>
				<div class="select-container-2">
					<v-select :items="filterByTime" v-model="timeDefault" dense solo label="Time" flat
							  @change="changeFiltcondition"></v-select>
				</div>
			</div>
		</div>

		<!-- 条形图挂载区域 -->
		<div class="bar-show"></div>
		<!-- <v-tabs-items v-model="tab">
				<v-tab-item v-for="item in content" :key="item">
					<div class="echart">{{ item }}</div>
				</v-tab-item>
			</v-tabs-items> -->
	</div>
</template>

<script>
import {getAnalysisPageDataByCondition, getProductTypePullDown} from '@/api/manage/analysis'
import {CustomersStatistics, RevenueStatistics, OrdersStatistics, InquiriesStatistics} from '@/assets/constant/config'

export default {
	name: "StatisticTab",
	props: {
		barData: {
			type: Object,
			default() {
				return {}
			}
		},
		dates: {
			type: Array,
			default() {
				return []
			}
		}
	},
	watch: {
		barData: {
			handler(newValue) {
				this.options.dataset = newValue
				this.echartsInit()
			},

		},
		dates: {
			handler(newValue) {
				console.log('current date', newValue);
				this.startTime = newValue[0]
				this.endTime = newValue[1]
			},
			immediate: true,
		}
	},
	created() {
		getProductTypePullDown({
			proId: this.$store.getters['manage/getProId']
		})
			.then(res => {
				// console.log(res);
				let typeArr = res.data.map(item => {
					return item.name
				})
				// console.log(typeArr);
				this.filterByProduct = typeArr
				this.productTypeDefault = typeArr[0]
			})
	},
	data() {
		return {
			startTime: '',
			endTime: '',
			statisticsType: 1,
			timeCondition: 1,
			productType: '',
			tab: null,
			myChart: '',
			statistics: [
				{
					name:'Inquiries Statistics',
					permission:'p-12'
				},
				{
					name:'Orders Statistics',
					permission:'p-13'
				},
				{
					name:'Revenue Statistics',
					permission:'p-14'
				},
				{
					name:'Customer Statistics',
					permission:'p-15'
				}
			],
			content: [1, 2, 3, 4],
			timeDefault: 'Daily',
			filterByTime: ["Daily", "Weekly", "Monthly", "Yearly"],
			// filterByProduct: ["Type1", "Type2"],
			productTypeDefault: '',
			filterByProduct: [],
			options: {
				grid: {
					width: "",
					left: "0",
					bottom: "0",
					containLabel: true,
					trigger: "axis",
					tooltip: {
						formatter: (data) => {
							let res = `<div style="width: 209px;background: rgba(255,255,255,0.9000);border-radius: 10px;">
											<div>${data.name} 2022</div>`;
							let dimensionNames = data.dimensionNames;
							for (let i = 1; i < dimensionNames.length; i++) {
								res += `<div style="margin-top:10px;">
										<div style="display: flex;justify-content: space-between">
											<div>
												<span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:${this.options.series[i - 1].itemStyle.color
								}"></span>
												<span>${data.dimensionNames[i]}</span>
											</div>
											<span>${data.data[i]}</span>
									</div>
								<div>`;
							}
							return res + "</div>";
						},
					},
				},
				legend: {
					left: "0",
					itemWidth: 10,
					itemHeight: 10,
					itemGap: 30,
					icon: "circle",
					formatter: ["{a|{name}}"].join("\n"),
					textStyle: {
						height: 12,
						rich: {
							a: {
								verticalAlign: "middle",
								fontSize: 16,
								color: "#999999",
								fontFamily: "Roboto",
							},
						},
					},
					data: [
						{
							name: "All Inquiries",
							itemStyle: {
								color: "#235DE6",
							},
						},
						{
							name: "New Inquiries",
							itemStyle: {
								color: "#4F7DEB",
							},
						},
						{
							name: "Artwork Confirmed",
							itemStyle: {
								color: "#7A9EF0",
							},
						},
						{
							name: "Ordered",
							itemStyle: {
								color: "#A6BFF5",
							},
						},
						{
							name: "Cancelled",
							itemStyle: {
								color: "#FF727E",
							},
						},
						{
							name: "Others",
							itemStyle: {
								color: "#FFB172",
							},
						},
					],
				},
				tooltip: {},
				dataset: {
					// dimensions: [
					//   "status",
					//   "All Inquiries",
					//   "New Inquiries",
					//   "Artwork Confirmed",
					//   "Ordered",
					//   "Cancelled",
					//   "Others",
					// ],
					// source: [
					//   ["lan", 43.3, 85.8, 93.7, 85.8, 93.7, 93.7],
					//   ["Feb", 83.1, 73.4, 55.1, 85.8, 93.7, 93.7],
					//   ["Mar", 86.4, 65.2, 82.5, 85.8, 93.7, 93.7],
					//   ["Apr", 72.4, 53.9, 39.1, 85.8, 93.7, 93.7],
					//   ["May", 72.4, 53.9, 39.1, 85.8, 93.7, 93.7],
					//   ["Jun", 72.4, 53.9, 39.1, 85.8, 93.7, 93.7],
					//   ["Jul", 72.4, 53.9, 39.1, 85.8, 93.7, 93.7],
					//   ["Aug", 72.4, 53.9, 39.1, 85.8, 93.7, 93.7],
					//   ["Sep", 72.4, 53.9, 39.1, 85.8, 93.7, 93.7],
					//   ["oct", 72.4, 53.9, 39.1, 85.8, 93.7, 93.7],
					//   ["Nov", 72.4, 53.9, 39.1, 85.8, 93.7, 93.7],
					//   ["Dec", 72.4, 53.9, 39.1, 85.8, 93.7, 93.7],
					// ],
				},
				xAxis: {
					type: "category",
					// data: ['lan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Des']
				},
				yAxis: {
					type: "value",
				},
				// Declare several bar series, each will be mapped
				// to a column of dataset.source by default.
				series: [
					{
						type: "bar",
						name: "All Inquiries",
						// data: [10, 52, 200, 334, 390, 330, 220, 52, 200, 334, 390, 330, 220],
						itemStyle: {
							color: "#235DE6",
							borderRadius: [3, 3, 0, 0],
						},
					},
					{
						type: "bar",
						name: "New Inquiries",
						// data: [10, 52, 200, 334, 390, 330, 220, 52, 200, 334, 390, 330, 220],
						itemStyle: {
							color: "#4F7DEB",
							borderRadius: [3, 3, 0, 0],
						},
					},
					{
						type: "bar",
						name: "Artwork Confirmed",
						// data: [10, 52, 200, 334, 390, 330, 220, 52, 200, 334, 390, 330, 220],
						itemStyle: {
							color: "#7A9EF0",
							borderRadius: [3, 3, 0, 0],
						},
					},
					{
						type: "bar",
						name: "Ordered",
						// data: [10, 52, 200, 334, 390, 330, 220, 52, 200, 334, 390, 330, 220],
						itemStyle: {
							color: "#A6BFF5",
							borderRadius: [3, 3, 0, 0],
						},
					},
					{
						type: "bar",
						name: "Cancelled",
						// data: [10, 52, 200, 334, 390, 330, 220, 52, 200, 334, 390, 330, 220],
						itemStyle: {
							color: "#FF727E",
							borderRadius: [3, 3, 0, 0],
						},
					},
					{
						type: "bar",
						name: "Others",
						// data: [10, 52, 200, 334, 390, 330, 220, 52, 200, 334, 390, 330, 220],
						itemStyle: {
							color: "#FFB172",
							borderRadius: [3, 3, 0, 0],
						},
					},
				],
			},
		};
	},
	mounted() {
		// const myChart = this.echartsInit();
		// window.onresize = function () {
		//   myChart.resize();
		// };
		// 找到容器
		let myChart = this.$echarts.init(
			document.querySelector(".bar-show"),
			null,
			{renderer: "svg"}
		);
		this.myChart = myChart
		// console.log(myChart);
	},
	methods: {
		changeFiltconditionType(value) {
			// 通过产品类型筛选
			console.log(value);
			this.productType = value;
			this.filterData()
		},
		changeFiltcondition(condition) {
			// 通过时间筛选
			condition = this.filterByTime.findIndex(item => item == condition) + 1
			console.log(condition);
			this.timeCondition = condition;
			this.filterData()
		},
		tabChange(index) {
			console.log('tabChange', index);
			this.statisticsType = index + 1
			this.filterData()
		},
		echartsInit() {
			// 开始渲染
			this.myChart.setOption(this.options, {notMerge: true});

		},

		// -------------------------------------------------   函数封装区  --------------------
		filterData() {

			getAnalysisPageDataByCondition({
				proId: this.$store.getters['manage/getProId'],
				type: this.statisticsType,
				period: this.timeCondition,
				productType: this.productType,
				startTime: this.startTime,
				endTime: this.endTime,
			})
				.then(res => {
					console.log('type', this.statisticsType);
					if (this.statisticsType == 1) {
						InquiriesStatistics.dataset.dimensions = res.data.dimensions
						InquiriesStatistics.dataset.source = res.data.source
						this.options = InquiriesStatistics
					} else if (this.statisticsType == 2) {
						OrdersStatistics.dataset.dimensions = res.data.dimensions
						OrdersStatistics.dataset.source = res.data.source
						this.options = OrdersStatistics

					} else if (this.statisticsType == 3) {
						RevenueStatistics.dataset.dimensions = res.data.dimensions
						RevenueStatistics.dataset.source = res.data.source
						this.options = RevenueStatistics
					} else if (this.statisticsType == 4) {
						CustomersStatistics.dataset.dimensions = res.data.dimensions
						CustomersStatistics.dataset.source = res.data.source
						this.options = CustomersStatistics
					}
					console.log('changeOptions', this.options);
					this.echartsInit()
				})
		}
	},
};
</script>

<style lang="scss" scoped>
.tab-container {
	display: flex;
	justify-content: space-between;
	overflow: hidden;

	.tab {
		.tab-item-title {
			position: relative;

			&.v-tab--active {
				background-color: #fff;
				font-weight: 700;
				border-top-left-radius: 10px;
				border-top-right-radius: 10px;

				.circle {
					display: block;
				}
			}
		}

		.circle {
			display: none;

			&::before {
				content: '';
				position: absolute;
				width: 10px;
				height: 10px;
				left: -10px;
				bottom: 0;
				background: radial-gradient(circle at top left,
					transparent 10px,
					#fff 0) top left;
			}

			&::after {
				content: "";
				position: absolute;
				width: 10px;
				height: 10px;
				right: -10px;
				bottom: 0;
				background: radial-gradient(circle at top right,
					transparent 10px,
					#fff 0) top right;
			}
		}
	}

	.filter-area {
		display: flex;
		/* width: 13.9583vw; */

		/* overflow: hidden; */
		/* height: 1.8229vw; */
		.select-container-1,
		.select-container-2 {
			border: 0.0521vw solid #e5e5e5;
			position: relative;
			z-index: 1;
			margin-right: 1.1458vw;
			border-radius: 0.2083vw;
		}

		.select-container-1 {
			width: 9.9042vw;
			flex-grow: 0;
			height: 2.1875vw;
		}

		.select-container-2 {
			width: 5.7292vw;
			height: 2.1875vw;
		}
	}
}

.bar-show {
	height: 20.9583vw;
	background-color: #fff;
	border-radius: 0.3125vw;
	width: 94%;
	left: 1.5625vw;
	top: 1.0417vw;
}

.v-tabs {
	width: 40.675vw;
	height: 1.8229vw;
	border-radius: 0.3125vw;
	text-transform: none;
}

.v-tab {
	/* width: 165px; */
	height: 1.8229vw;
	font-size: 0.8333vw;
	height: 2.1875vw;
	text-transform: none;
}

.v-tab--active {
	background-color: #fff;
	font-weight: bold;
	font-size: 0.8333vw;
	border-top-right-radius: 0.3125vw;
	border-top-left-radius: 0.3125vw;
}

.v-tabs-items {
	/* height: 23.9583vw; */
}
</style>
