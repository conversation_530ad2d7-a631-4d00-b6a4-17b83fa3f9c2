<template>
	<div class="custom-medals" :theme="$store.state.proTheme" noDebounce>
		<template v-if="isManage">
			<v-card height="300">
				<v-row justify="center">
					<v-overlay :absolute="true" :value="true">
						<v-chip>semiMedals报价系统</v-chip>
					</v-overlay>
				</v-row>
			</v-card>
		</template>
		<template v-else>
			<div class="fillBox" v-show="isLoading">
				<div class="loadProgress">
					<Loading></Loading>
				</div>
			</div>
			<template v-if="!isLoading">
				<article class="content">
					<div class="center-content">
						<div class="leftArea" id="leftArea" :style="{ top: leftSticyHeight }">
							<div class="daohang" v-if="!isGsPageQuote">
								<span
									><a href="https://www.custommedals.co/medals">{{ langSemi.allProducts }}</a> </span
								>&nbsp;<span>> {{ titleName }}</span>
							</div>
							<div class="rightArea-title" v-if="device == 'mb'">
								<h1>{{ titleName }}</h1>
								<div class="rightArea-title-div">
									<span><CCYRate :price="nowTotalPrice" class="nowTotalPrice"></CCYRate></span>
								</div>
							</div>
							<!-- 左上 <-> 右下 的消失动画 -->
							<transition enter-active-class="animate__animated animate__faster animate__fadeInBottomRight" leave-active-class="animate__animated animate__faster animate__fadeOutBottomRight">
								<SwiperDetail ref="'swiperKey'" :imgList="carouselList" v-show="showRightArea" :selectedData="selectedData" :generalData="generalData" @toPosition="toPosition" :titleName="titleName" @openTemplate="showDialog" loop :showProfessional="false" :showViewMore="true" :showTemplate="!isGsPageQuote" />
							</transition>
						</div>

						<div class="rightArea" id="rightAreaCustom">
							<div class="rightArea-title" v-if="device != 'mb'">
								<h1>{{ titleName }}</h1>
								<div class="rightArea-title-div">
									<span><CCYRate :price="nowTotalPrice" class="nowTotalPrice"></CCYRate></span>
								</div>
							</div>
							<div v-for="(item, index) in filterShowGeneralData" :key="item.id" class="kk" :class="{ type1: picDialog }">
								<!-- Select Size 尺寸 -->
								<div v-if="item.paramName == 'Medal Size'" class="part Select-Size" :id="item.paramName" :class="{ 'step-active': item.paramName == maskName }">
									<div class="left">
										<div>
											<h3 class="step-title">
												<div class="stepIndex">
													<span>{{ lang.step }} {{ item.customIndex }}</span>
												</div>
												{{ lang.Select }}
												{{ item.alias ? item.alias : item.paramName }}
											</h3>
											<div class="steop-title-bottom">
												<div class="steop-title-bottom-to"></div>
											</div>
										</div>
										<div class="left-img">
											<img style="aspect-ratio: 735/295; width: 100%; object-fit: contain" :src="shapeImg" />
										</div>
										<div class="de">
											<div class="de-c">
												<div class="boxContent">
													<MyCheckBox v-for="itemChild in item.childList" class="sizeBtn" :key="itemChild.id" :bindValue="itemChild" :bindName="item.paramName" :titleValue="'alias'" :likeModel="selectedData" type2 hot @clickFun="selectQuoteParams(item, itemChild)" :isPageQuote="isPageQuote"></MyCheckBox>
												</div>
												<div class="text-center">
													<el-button
														v-if="windowWidth > 376"
														class="myBtn"
														:style="{
															opacity: selectedData[item.paramName].length > 0 ? 1 : 0.5,
														}"
														@click="showMaskFn(item.paramName)"
														:disabled="!selectedData[item.paramName].length > 0"
														>{{ lang.next }}
													</el-button>
												</div>
											</div>
										</div>
									</div>

									<div style="text-align: center">
										<el-button
											v-if="windowWidth <= 376"
											class="myBtn"
											@click="showMaskFn(item.paramName)"
											:style="{
												opacity: selectedData[item.paramName].length > 0 ? 1 : 0.5,
											}"
											:disabled="!selectedData[item.paramName].length > 0"
											>{{ lang.next }}
										</el-button>
									</div>
									<i v-if="item.paramName == maskName" class="el-icon-close" @click="closeMask"></i>
								</div>

								<!-- 金属处理 -->
								<div v-if="item.paramName == 'Plating'" class="part Select-Medal-Finish" :id="item.paramName" :class="{ 'step-active': item.paramName == maskName }">
									<div>
										<h3 class="step-title hasTips5">
											<div class="stepIndex">
												<span>{{ lang.step }} {{ item.customIndex }}</span>
											</div>
											{{ lang.Select }}
											{{ item.alias ? item.alias : item.paramName }}
										</h3>
										<div class="steop-title-bottom">
											<div class="steop-title-bottom-to"></div>
										</div>
									</div>
									<div class="tips">* {{ lang.medals.step5Text }}</div>

									<div class="boxContent" ref="Select-Medal-Finish">
										<div v-for="itemChild in item.childList" :key="itemChild.id">
											<MyCheckBox :childList="itemChild.childList.length > 0 ? true : false" :tipNum.sync="tipNum" :bindValue="itemChild" :bindName="item.paramName" :titleValue="'alias'" :imageValue="'imageJson'" :aspectRatio="'aspect-ratio: 176/154'" :likeModel="selectedData" :sizeValue="sizeValue" @clickFun="selectQuoteParams(item, itemChild)" @selectCurrentParams="selectCurrentParams(item, itemChild)" :showPrice="false" :isPageQuote="isPageQuote" :restaurants="restaurants" autocompleteInput></MyCheckBox>
										</div>
									</div>
									<div class="text-center">
										<el-button
											class="myBtn"
											@click="showMaskFn(item.paramName)"
											:style="{
												opacity: selectedData[item.paramName].length > 0 ? 1 : 0.5,
											}"
											:disabled="!selectedData[item.paramName].length > 0"
										>
											{{ lang.next }}
										</el-button>
									</div>
									<i v-if="item.paramName == maskName" class="el-icon-close" @click="closeMask"></i>
								</div>

								<!-- Upload Artwork 上传 -->
								<div v-if="item.paramName === 'Upload Artwork & Comments'" class="part Upload-Artwork" :id="item.paramName" :class="{ 'step-active': item.paramName == maskName }">
									<SemiMedalsStepUpload class="step-item step-upload" :class="{ hideContent: $route.query.designPic }" :id="item.paramName" :index="item.customIndex" :itemData="item" :uploadList.sync="uploadArtworkList" :remark.sync="remark" :isUpload.sync="isUpload" @closeMask="closeMask" @showMaskFn="showMaskFn" :key="index" @drawerUpload="drawerUpload" :initData="initData" :semiMedalsIconList="semiMedalsIconList"></SemiMedalsStepUpload>
									<div class="text-center">
										<el-button
											class="myBtn"
											@click="showMaskFn(item.paramName)"
											:style="{
												opacity: uploadArtworkList.length > 0 || isUpload ? 1 : 0.5,
											}"
											:disabled="!(isUpload || uploadArtworkList.length > 0)"
										>
											{{ lang.next }}
										</el-button>
									</div>
									<i v-if="item.paramName == maskName" class="el-icon-close" @click="closeMask"></i>
								</div>

								<!-- 背面选项 -->
								<div v-if="item.paramName == 'Back Side'" class="part Back-Side-Option" :id="item.paramName" :class="{ 'step-active': item.paramName == maskName }">
									<div>
										<h3 class="step-title">
											<div class="stepIndex">
												<span>{{ lang.step }} {{ item.customIndex }}</span>
											</div>
											{{ lang.Select }}
											{{ item.alias ? item.alias : item.paramName }}
										</h3>
										<div class="steop-title-bottom">
											<div class="steop-title-bottom-to"></div>
										</div>
									</div>
									<div class="boxContent">
										<MyCheckBox v-for="itemChild in item.childList" :key="itemChild.id" :bindValue="itemChild" :bindName="item.paramName" :titleValue="'alias'" :imageValue="'imageJson'" :aspectRatio="'aspect-ratio: 315/204'" :likeModel="selectedData" :sizeValue="sizeValue" @clickFun="selectQuoteParams(item, itemChild)" showPrice :isPageQuote="isPageQuote"></MyCheckBox>
									</div>
									<i v-if="item.paramName == maskName" class="el-icon-close" @click="closeMask"></i>
								</div>

								<!-- 织带 -->
								<div v-if="item.paramName == 'Ribbon'" class="part Select-Ribbon" :id="item.paramName" :class="{ 'step-active': item.paramName == maskName }">
									<div>
										<h3 class="step-title">
											<div class="stepIndex">
												<span>{{ lang.step }} {{ item.customIndex }}</span>
											</div>
											{{ lang.Select }}
											{{ item.alias ? item.alias : item.paramName }}
										</h3>
										<div class="steop-title-bottom">
											<div class="steop-title-bottom-to"></div>
										</div>
									</div>
									<div class="boxContent">
										<MyCheckBox v-for="itemChild in item.childList" :key="itemChild.id" :bindValue="itemChild" :bindName="item.paramName" :titleValue="'alias'" :imageValue="'imageJson'" :aspectRatio="'aspect-ratio: 205/162'" :likeModel="selectedData" @clickFun="selectQuoteParams(item, itemChild)" :isPageQuote="isPageQuote"></MyCheckBox>
									</div>
									<RibbonOptinos class="medals-RibbonOptinos" v-if="selectedData[item.paramName].length && selectedData[item.paramName][0].paramName !== 'No Ribbon'" :List="selectedData[item.paramName][0].childList" :parentName="selectedData[item.paramName][0].paramName" :bindValue="RibbonBindValue" @nextStepFun="showMaskFn(item.paramName)" @update="updateFun"></RibbonOptinos>
									<i v-if="item.paramName == maskName" class="el-icon-close" @click="closeMask"></i>
								</div>

								<!-- 包装 -->
								<div v-if="item.paramName == 'Packaging Options'" class="part Select-Package" :id="item.paramName" :class="{ 'step-active': item.paramName == maskName }">
									<div>
										<h3 class="step-title">
											<div class="stepIndex">
												<span>{{ lang.step }} {{ item.customIndex }}</span>
											</div>
											{{ lang.Select }}
											{{ item.alias ? item.alias : item.paramName }}
										</h3>
										<div class="steop-title-bottom">
											<div class="steop-title-bottom-to"></div>
										</div>
									</div>
									<div class="boxContent">
										<MyCheckBox v-for="itemChild in item.childList" :key="itemChild.id" :bindValue="itemChild" :bindName="item.paramName" :titleValue="'paramName'" :imageValue="'imageJson'" :aspectRatio="'aspect-ratio: 316/204'" :likeModel="selectedData" :sizeValue="sizeValue" @clickFun="selectQuoteParams(item, itemChild)" :isPageQuote="isPageQuote" showPrice></MyCheckBox>
									</div>
									<i v-if="item.paramName == maskName" class="el-icon-close" @click="closeMask"></i>
								</div>

								<!-- Turnaround Time 交期折扣 & 备注 -->
								<template v-if="item.paramName == 'Turnaround Time'">
									<div class="part Delivery-Date" :class="{ 'step-active': maskName === item.paramName }" :id="item.paramName">
										<div style="display: block">
											<h3 class="step-title">
												<div class="stepIndex">
													<span>{{ lang.step }} {{ item.customIndex }}</span>
												</div>
												{{ lang.Select }}
												{{ item.alias ? item.alias : item.paramName }}
											</h3>
											<div class="steop-title-bottom">
												<div class="steop-title-bottom-to"></div>
											</div>
										</div>
										<!-- <div style="display: flex">
                                        <StepTime class="step-item step-date" :index="item.customIndex" :itemData="item" :priceInfo="priceInfo" :previewMode="previewMode" :selectedParams="selectedData" @showMaskFn="showMaskFn" @selectItem="selectQuoteParams($event.item, $event.citem)" @closeMask="closeMask" :key="index" :isPageQuote="isPageQuote"></StepTime>
                                    </div> -->
										<div class="hoursText">
											{{ lang.hourstext }}
										</div>
										<div class="step-item-params">
											<div
												class="param-item-wrap"
												v-for="citem in getNewDiscountList(item)"
												:key="citem.id"
												:class="{
													active: hasId(citem.id, selectedData[item.paramName]),
												}"
												@click="selectQuoteParams(item, citem)"
											>
												<div class="param-item">
													<DiscountText :itemData="citem"></DiscountText>
												</div>
												<div class="des">{{ citem.alias }}</div>
											</div>
										</div>
										<i v-if="item.paramName == maskName" class="el-icon-close" @click="closeMask"></i>
									</div>
								</template>
							</div>
							<div class="footer" id="foot">
								<div class="fonterOrderSummary" @click="showSummaryFn">
									<span>{{ lang.orderSummary }} <i :class="{ 'el-icon-arrow-up': !showSummary, 'el-icon-arrow-down': showSummary }" style="align-self: flex-end"></i> </span>
								</div>
								<Detail :class="{ showMore: showSummary }" class="type2 foldStyle" :key="3" :absoluteImg="comeFromDZ.absoluteImg" :selectedData="selectedData" :priceInfo="priceInfo" :customQty="customQty" :textInfo="textInfo" :generalData="generalData" @toPosition="toPosition" @addInquiry="debounceAddInquiry" @addCart="addCart" :showTextTip="false"></Detail>
								<div class="fonterBtnGrap">
									<div class="btnGroup">
										<QuoteBtn bgColor="linear-gradient(to top, #C73743 0%, #C73743 100%)" borderColor="#ffffff" @click.native="addInquiry"
											>{{ lang.submitInquiry }}
											<span @click.stop>
												<el-tooltip popper-class="cusToolTip" effect="light" :content="textInfo.inquiryTip" placement="top-start">
													<b class="icon-wenhao3 tip-icon"></b>
												</el-tooltip>
											</span>
										</QuoteBtn>
										<QuoteBtn bgColor="linear-gradient(to top, #ffffff 0%, #ffffff 100%)" borderColor="#ffffff" v-if="onlyAddInquiry === 0 || !onlyAddInquiry" @click.native="addCart"
											>{{ lang.addToCart }}
											<span @click.stop>
												<el-tooltip popper-class="cusToolTip" effect="light" :content="textInfo.addCartTip" placement="top-start">
													<b class="icon-wenhao3 tip-icon2"></b>
												</el-tooltip>
											</span>
										</QuoteBtn>
									</div>
								</div>
							</div>
						</div>
					</div>
				</article>

				<el-drawer :visible.sync="showArea" :with-header="true" custom-class="drawDialog" :size="device === 'ipad' ? '728px' : '85%'">
					<Detail class="type3" :key="2" :absoluteImg="comeFromDZ.absoluteImg" :selectedData="selectedData" :priceInfo="priceInfo" :customQty="customQty" :textInfo="textInfo" :generalData="generalData" @toPosition="toPosition" @addInquiry="debounceAddInquiry" @addCart="addCart"></Detail>
				</el-drawer>
				<!--    遮罩-->
				<myMask class="myMaskMedals" :maskName.sync="maskName"></myMask>
				<!-- 建议弹窗-->
				<RecomendDialog leftDrawer :commentsDialog.sync="commentsDialog" :paramValue="selectedParamsValue" :paramItem="selectedParamsValueParent" @next="recomendNext" :showUpload="true" :model="false"></RecomendDialog>
				<!--  手机，ipad端预览-->
				<PreviewBtn :showArea.sync="showArea"></PreviewBtn>
				<BaseDialog v-model="showOtoDialog" :width="device != 'mb' ? '570px' : '90%'">
					<div class="otoWrap">
						<img src="@/assets/images/oto.png" alt="" />
						<p>{{ lang.p1 }}</p>
						<h3>{{ lang.p2 }}</h3>
						<p style="color: #666666">
							{{ lang.p3 }}<br />
							{{ lang.p4 }}.
						</p>

						<div class="box">
							<p class="t1">
								{{ lang.p5 }}<br />
								{{ lang.p6 }}!
							</p>
							<a href="https://www.o2o.co/previewWeb" style="text-decoration: none">
								<el-button type="primary">{{ lang.p7 }}</el-button>
							</a>
						</div>
					</div>
				</BaseDialog>
				<VideoPreviewDialog :pic-dialog.sync="picDialog" :zoom-pic="zoomPic" :temp-type="tempType" :selected-params-value="selectedParamsValue" @qtyAndBtnConfirm="qtyAndBtnConfirm" @qtyAndBtnNext="qtyAndBtnNext" @dialogNextStep="dialogNextStep"></VideoPreviewDialog>
				<BaseDialog v-model="upgradeDialog" :width="device != 'mb' ? '640px' : '320px'">
					<Upgrade @closeUpgrade="upgradeDialog = false" :tipArr="tipArr" :typeName="cateData.cateName" unit="pairs" @upgradeLevel="upgradeLevel"></Upgrade>
				</BaseDialog>
				<infoDialog :infoDialogVisible.sync="infoDialogVisible" @getValue="getValueFun" :otherUpload="uploadArtworkList" :recomendUpload="recomendUpload" :uploadList.sync="uploadList"></infoDialog>
				<BaseDialog v-model="noFileDialog" :width="device != 'mb' ? '485px' : '90%'" :model="false">
					<template #closeIcon>
						<div style="display: none"></div>
					</template>
					<infoUpload :infoUploadList="infoUploadList" @pushInfoList="pushInfoList" @delInfoList="delInfoList" @updateInquiry="updateInquiry" @closeInfoDialog="closeInfoDialog"></infoUpload>
				</BaseDialog>
			</template>
			<!-- 步骤三上传弹窗 -->
			<BaseDialog v-model="drawer" :width="device != 'mb' ? '47vw' : '90%'" class="eldrawer" :class="{ pcLeftDialog: device != 'mb' }">
				<template>
					<UploadDrawer :semiMedalsIconList="semiMedalsIconList" :semiMedalsIconTagList="semiMedalsIconTagList" @tagNameClick="tagNameClick" :bindValue="uploadBindValue" :imgClickData="imgClickData" :uploadList.sync="uploadArtworkList" :clickImgId.sync="clickImgId" @uploadToNext="uploadToNext"></UploadDrawer>
				</template>
				<div class="close-icon" slot="closeIcon" @click="closeDrawer(false)">
					<i class="el-icon-close leftDrawer"></i>
				</div>
			</BaseDialog>

			<!--小类别标签弹窗-->
			<DialogForShop :priceTitle="'As Low As'" :shopDialog.sync="shopDialog" :tagList="tagList" :shopList="shopList" @changeTag="getAppRingTemplatesList" @selectProduct="selectProduct"></DialogForShop>
		</template>
	</div>
</template>
<script>
import "@/plugins/element";
import MyCheckBox from "@/components/Medals/MyCheckBox";
import MyCheckBox3 from "@/components/Medals/MyCheckBox3";
import BaseDialog from "@/components/Quote/BaseDialog";
import VideoPlayer from "@/components/Quote/VideoPlayer.vue";
import Upgrade from "@/components/Quote/Upgrade";
import DialogBM from "@/components/Medals/DialogBM";
import QtyAndBtn from "@/components/Medals/QtyAndBtn";
import Corner from "@/components/Medals/Corner";
import infoDialog from "@/components/Medals/infoDialog";
import QuoteNav from "@/components/Cufflinks/QuoteNav";
import QuoteBtn from "@/components/Quote/QuoteBtn";
import quoteMixin from "@/mixins/quote";
import quoteBanChoiceMixins from "@/mixins/quoteBanChoice";
import StepTime from "@/components/Quote/StepTime.vue";
import StepQty from "@/components/Cufflinks/StepQty.vue";
import RecomendDialog from "@/components/Quote/RecomendDialog.vue";
import myMask from "@/components/Quote/Mask.vue";
import Detail from "@/components/Quote/Detail.vue";
import SwiperDetail from "@/components/Cufflinks/SwiperDetail.vue";
import PreviewBtn from "@/components/Quote/PreviewBtn.vue";
import Preloader from "@/components/Quote/Preloader.vue";
import SemiMedalsStepUpload from "@/components/semiMedals/SemiMedalsStepUpload.vue";
import UploadDrawer from "@/components/semiMedals/UploadDrawer.vue";
import RibbonOptinos from "@/components/Medals/RibbonOptinos2.vue";
import DialogForShop from "@/components/Quote/MetalSigns/DialogForShop.vue";

import { isImageType, domScrollFn } from "@/utils/utils";
import { medalsApi } from "@/api/medals/medals";
import { getAppRingTagList, getAppRingTemplatesList } from "@/api/web";
import DiscountText from "@/components/modal/Quote/QuoteComponents/DicountText.vue";
import Loading from "@/components/Loading.vue";
import { getQuoteTime } from "@/assets/js/quote/quotePublic";
import VideoPreviewDialog from "@/components/Quote/VideoPreviewDialog.vue";

export default {
	props: {
		data: {
			type: Object,
			default: {},
		},
		isGsPageQuote: {
			type: Boolean,
			default: false,
		},
	},
	components: {
		VideoPreviewDialog,
		Loading,
		DiscountText,
		Preloader,
		PreviewBtn,
		Detail,
		SwiperDetail,
		myMask,
		RecomendDialog,
		StepQty,
		StepTime,
		MyCheckBox,
		MyCheckBox3,
		BaseDialog,
		VideoPlayer,
		DialogBM,
		QtyAndBtn,
		Corner,
		infoDialog,
		Upgrade,
		QuoteNav,
		QuoteBtn,
		SemiMedalsStepUpload,
		UploadDrawer,
		RibbonOptinos,
		DialogForShop,
	},
	mixins: [quoteMixin, quoteBanChoiceMixins],
	data() {
		return {
			isManage: false,
			drawer: false,
			direction: "rtl",
			isPageQuote: true,
			tipNum: 1,
			pid: this.data.quotePid,
			productsName: "custom embroidered patches",
			restaurants: [
				{ value: "5", address: "5" },
				{ value: "10", address: "10" },
				{ value: "25", address: "25" },
				{ value: "50", address: "50" },
				{ value: "100", address: "100" },
				{ value: "200", address: "200" },
				{ value: "300", address: "300" },
				{ value: "500", address: "500" },
				{ value: "1000", address: "1000" },
			],
			cateList: [], //推荐分类数据
			showProfessional: false,
			uploadArea: true,
			priceList: [],
			qlist: [],
			modal: {
				...this.data,
			},
			update: false,
			showSummary: false,
			leftSticyHeight: 0,
			shopDialog: false,
			shopList: [],
			tagList: [],
			uploadBindValue: {},
			imgClickData: {},
			clickImgId: [],
			firstSelect: false,
			nowPrice: 0,
		};
	},
	computed: {
		quoteCateId() {
			return this.modal.quoteCateId;
		},
		quotePid() {
			return this.modal.quotePid;
		},
		langsemiCustom() {
			return this.$store.getters.lang.semiCustom || {};
		},
		quoteTotalPrice() {
			return this.$store.state.quoteTotalPrice || 0;
		},
		nowTotalPrice() {
			if (this.quoteTotalPrice > 0) {
				return this.quoteTotalPrice;
			}
			let findSize = this.generalData.find((item) => {
				return item.paramType === "SIZE";
			});
			let selectSize = this.selectedData[findSize.paramName];
			if (!selectSize) {
				return;
			}
			let priceList = JSON.parse(selectSize[0].priceInfo.increasePrice);
			return priceList[0].unitPrice;
		},
		onlyAddInquiry() {
			let onlyAddInquiry = false;
			for (const stepData in this.selectedData) {
				let data = this.selectedData[stepData];
				if (data[0]) {
					if (data[0].onlyAddInquiry && data[0].onlyAddInquiry != 0) {
						onlyAddInquiry = true;
						this.viewMore = true;
					}
				}
			}
			return onlyAddInquiry;
		},
	},
	watch: {
		filterShowGeneralData: {
			immediate: true,
			handler(newVal) {
				if (newVal && newVal.length > 0 && !this.firstSelect) {
					//默认选中size 第一步
					let sizeData = this.filterShowGeneralData.find((item) => {
						return item.paramName == "Medal Size";
					});
					if (sizeData) {
						if (sizeData.childList && sizeData.childList?.length > 0) {
							this.selectQuoteParams(sizeData, sizeData.childList[0], false);
						}
					}
					this.firstSelect = true;
				}
			},
		},
	},
	methods: {
		//标签
		tagNameClick(item) {
			this.tagNameId = item.id;
			this.getSemiMedalsIconList();
		},
		//步骤三上传弹窗
		async drawerUpload(imgData) {
			await this.getAppRingIconTagList();
			this.getSemiMedalsIconList();
			this.$nextTick(() => {
				if (imgData.type == "open") {
					this.uploadBindValue = imgData.data;
					this.imgClickData = null;
				}
				if (imgData.type == "imgOpen") {
					this.imgClickData = imgData.data;
				}
				this.drawer = true;
			});
		},

		closeDrawer() {
			this.drawer = false;
		},

		isImageType,
		getAppRecommendCateList() {
			medalsApi.getAppRecommendCateList({ proId: this.proId, id: this.quotePid }).then((res) => {
				this.cateList = res.data;
				this.titleName = this.cateList[0]?.cateName;
			});
		},
		uploadToNext() {
			this.drawer = false;
			// this.showMaskFn("Upload Artwork & Comments");
			this.maskName = "Upload Artwork & Comments";
			this.goStep();
		},
		updateFun() {
			this.update = !this.update;
		},
		clearField(e = "DISCOUNT") {
			let findDiscount = this.generalData.find((item) => item.paramType === e);
			if (findDiscount) {
				let name = findDiscount.paramName;
				this.selectedData[name] = [];
			}
		},
		getNewDiscountList(itemData) {
			let result = getQuoteTime(itemData.childList, this.priceInfo, this.proType),
				originShowSmallPrice = this.$store.state.showSmallPrice;
			this.$store.commit("setShowSmallPrice", result.newShowSmallPrice);
			if (result.newShowSmallPrice !== originShowSmallPrice) {
				this.clearField("DISCOUNT");
			}
			return result.arr;
		},
		//判断是否选中
		hasId(id, arr) {
			if (!arr) {
				return false;
			}
			return !!arr.find((item) => {
				return item.id === id;
			});
		},
		getAppRingTemplatesList(id) {
			getAppRingTemplatesList({
				cateId: this.pid,
				ringTagId: id,
				page: 1,
				pageSize: 10000,
			}).then((res) => {
				this.shopList = res.data.content;
			});
		},
		showDialog() {
			this.shopDialog = true;
			getAppRingTagList({
				cateId: this.pid,
			}).then((res) => {
				this.tagList = res.data;
				this.getAppRingTemplatesList();
			});
		},
		async selectProduct(item) {
			if (this.isIframe) {
				this.$router.push({
					path: item.quoteRoutingName,
					query: {
						type: "quoteIframe",
					},
				});
			} else {
				this.$router.push(`/medals/${item.cateName.replace(/ /g, "-").toLowerCase()}`);
			}
		},
		showSummaryFn() {
			this.showSummary = !this.showSummary;
			this.$nextTick(() => {
				setTimeout(() => {
					domScrollFn(".fonterBtnGrap", "end");
				}, 350);
			});
		},
	},
	created() {
		this.getAppRecommendCateList();
	},
	mounted() {
		if (process.env.isManage) {
			this.isManage = true;
			return false;
		}
		let headerHeight = document.querySelector("#modalHeader");
		this.leftSticyHeight = (typeof headerHeight?.offsetHeight === "number" ? headerHeight.offsetHeight : 0) + "px";
	},
};
</script>
<style scoped lang="scss">
$color-primary: #ae1e1e;
@mixin selectedStyle {
	border-color: $color-primary;
	&::after {
		content: "\e82c";
		position: absolute;
		width: 1.38rem;
		height: 1.38rem;
		line-height: 1.38rem;
		right: 0;
		top: 0;
		border-bottom-left-radius: 50%;
		text-align: center;
		color: #ffffff;
		background-color: $color-primary;
		font-family: "modalicon";
	}
}
.hoursText {
	color: rgb(135 132 132);
	font-size: 16px;
	margin-bottom: 15px;
	position: relative;
	@media screen and (max-width: 767px) {
		font-size: 14px;
	}
}

.custom-medals {
	position: relative;
	color: #333;
	font-family: Calibri;

	.fillBox {
		position: relative;
		margin: auto;
		width: 100vw;
		height: 100vh;
		.loadProgress {
			position: absolute;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%) scale(1.2);
			z-index: 10;

			@include respond-to(mb) {
				left: 50%;
				transform: translateX(-50%);
			}
		}
	}

	.eldrawer.pcLeftDialog ::v-deep .base-dialog-model-con {
		height: 100vh;
		position: fixed !important;
		top: 0;
		right: 0;
		display: flex;
		flex-direction: column;
		border-radius: initial;
	}

	.eldrawer {
		.close-icon {
			.el-icon-close {
				&.leftDrawer {
					all: initial;
					display: flex;
					justify-content: center;
					align-items: center;
					font-weight: 700;
					cursor: pointer;
					position: absolute;
					top: 20px;
					right: 40px;
				}
			}
		}
	}

	::v-deep .RibbonOptinos {
		@include respond-to(mb) {
			padding: 10px;

			.tec {
				font-size: 12px;
			}

			.gridBox {
				grid-template-columns: 1fr;
				gap: 0;
				row-gap: 15px;
			}

			.topBox {
				.shape-box {
					grid-template-columns: repeat(2, 1fr);
				}
			}

			.UploadBox {
				grid-column: 1/3;

				.area {
					.left {
						.uploadList {
							height: auto;

							ul {
								.uploadItem {
									font-size: 12px;
								}
							}
						}

						.uploadBtn {
							font-size: 12px;
						}
					}
				}
			}

			.commentBox {
				grid-column: 1/3;
				grid-row: 3/4;
			}
		}
	}

	::v-deep .el-input__inner:focus {
		border-color: $color-primary;
	}

	::v-deep video {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}

	::v-deep ul {
		margin-left: 0;
	}

	::v-deep .video-js,
	::v-deep .vjs-poster {
		background-color: white !important;
	}

	.viewMore {
		@media screen and (min-width: 768px) {
			display: none;
		}
	}

	::v-deep .StepBox {
		border-radius: 4px;
		overflow: hidden;
		border: 2px solid #ebebeb;
		transition: all 0.2s;

		@include respond-to(mb) {
			padding-bottom: 0 !important;
			border-width: 1px;
			border-radius: 6px;
		}

		&:hover {
			border-color: $color-primary !important;
		}

		&.active {
			border-color: $color-primary !important;

			.title {
				color: #333 !important;
			}
		}

		.se {
			align-items: start;

			.product-info {
				padding: 11px 0;
				margin-top: 0;
				align-items: start;

				@include respond-to(mb) {
					padding: 8px 0;
				}

				.radio-beauty {
					min-width: 20px;
					height: 20px;
					border-color: #cccccc;
					margin-left: 20px;
				}

				.radio-beauty::after {
					width: 10px;
					height: 10px;
					background-color: #fff;
				}

				@include respond-to(mb) {
					.radio-beauty {
						min-width: 12px;
						width: 12px;
						height: 12px;
						border-color: #cccccc;
						margin: 0 5px 0 12px;
					}

					.radio-beauty::after {
						width: 6px;
						height: 6px;
						background-color: #fff;
					}
				}
			}
		}

		@media (any-hover: hover) {
			&:hover {
				.zoomIcon {
					color: $color-primary;
				}

				.product-info {
					.radio-beauty {
						background-color: $color-primary;
						border-color: $color-primary;

						&::after {
							background-color: white;
						}
					}

					.title {
						// color: $color-primary;
					}
				}
			}
		}
	}

	.picWrap {
		display: flex;
		justify-content: center;
		align-items: center;
		flex-direction: column;
		width: 100%;
		padding: 0px !important;
	}

	.text-center {
		text-align: center;
	}

	.myBtn {
		background: $color-primary;
		border-radius: 10px;
		font-size: 18px;
		font-family: Calibri;
		font-weight: 400;
		color: #ffffff;
		padding: 10px 65px;
		margin-top: 30px;

		@include respond-to(mb) {
			width: 125px;
			height: 30px;
			background: $color-primary;
			border-radius: 5px;
			font-size: 12px;
			font-family: Arial;
			font-weight: 400;
			color: #ffffff;
			margin-top: 9.5px;
			padding: 0;
		}
	}

	.header {
		h1 {
			font-size: 30px;
			font-family: Calibri;
			font-weight: bold;
			color: #333;
			margin-bottom: 9px;
		}
	}

	::v-deep .dialogForShopContent {
		.b .breadcrumb .rp {
			b.active {
				background-image: linear-gradient(90deg, $color-primary, $color-primary 120%) !important;
			}
		}

		.items-cont {
			.swiper-slide {
				&.active,
				&:hover {
					color: white;
					background: $color-primary !important;
				}
			}
		}

		.swiper-pagination {
			top: 50px;
			left: 50%;
			transform: translateX(-50%);

			.swiper-pagination-bullet {
				&.swiper-pagination-bullet-active {
					background-image: initial !important;
					background-color: $color-primary !important;
				}

				@include respond-to(mb) {
					width: 12px !important;
					height: 12px !important;
				}
			}
		}
	}

	::v-deep .content {
		width: 83.34vw;
		margin: 0 auto;
		padding: 20px 0;

		.daohang {
			font-size: 14px;
			margin-top: 10px;
			margin-bottom: 20px;

			a {
				color: $color-primary;
			}

			@include respond-to(mb) {
				display: none;
			}
		}

		.cufflink-quote {
			padding: 24px 0;
		}

		.center-content {
			display: flex;
			align-items: flex-start;
			// justify-content: space-between;
		}

		.leftArea {
			width: 42.9688vw;
			padding-bottom: 20px;
			margin-right: 2.0833vw;
			position: sticky;

			.rightArea-title {
				display: none;
				padding-left: 10px;
				margin-bottom: 10px;

				h1 {
					text-align: left;
					font-size: 24px;
					font-weight: 700;
					color: #222222;
				}

				.rightArea-title-div {
					span:nth-of-type(1) {
						font-weight: bold;
						font-size: 36px;
						color: $color-primary;
					}

					span:nth-of-type(2) {
						color: #999999;
						text-decoration-line: line-through;
						font-size: 16px;
						padding-left: 10px;
					}

					.nowTotalPrice {
						color: $color-primary;
					}
				}
			}

			.mask {
				z-index: 101;
				background-color: #fff;
			}

			@include respond-to(mb) {
				width: 100%;
				position: initial;
				.rightArea-title {
					display: block;

					h2 {
						font-size: 18px;
					}

					.rightArea-title-div {
						span:nth-of-type(1) {
							font-weight: bold;
							font-size: 24px;
							color: $color-primary;
						}
					}
				}
			}
		}

		.rightArea {
			width: 38.2813vw;
			padding-top: 40px;
			position: relative;

			.rightArea-title {
				padding-left: 10px;

				h2 {
					text-align: left;
					font-size: 24px;
					font-weight: 700;
					color: #222222;
				}

				.rightArea-title-div {
					span:nth-of-type(1) {
						font-weight: bold;
						font-size: 36px;
						color: $color-primary;
					}

					span:nth-of-type(2) {
						color: #999999;
						text-decoration-line: line-through;
						font-size: 16px;
						padding-left: 10px;
					}

					.nowTotalPrice {
						color: $color-primary;
					}
				}
			}

			@include respond-to(mb) {
				width: 100%;
				padding-top: 0;
				.rightArea-title {
					display: none;
				}
			}

			.Upload-Artwork {
				.step-box {
					display: block;
				}

				@include respond-to(mb) {
					.upload-box {
						display: block;
					}
				}
			}

			.Select-Size {
				.m-se {
					font-size: 16px;
					height: 50px;

					.product-info {
						line-height: 50px;
					}

					@include respond-to(mb) {
						height: 35px;
						.product-info {
							line-height: 35px;
							font-size: 15px;
						}
					}
				}

				.left-img {
					width: 100%;
					text-align: center;
					margin-bottom: 20px;
				}

				.boxContent {
					grid-template-columns: repeat(3, 1fr) !important;
					column-gap: 20px;
					row-gap: 20px;

					.StepBox.sizeBtn {
						border: 2px solid #f2f2f2;

						.product-info {
							margin: 0 auto;

							.radio-beauty {
								display: none;
							}
						}
					}

					@include respond-to(mb) {
						grid-template-columns: repeat(2, 1fr) !important;
					}
				}
			}

			.Back-Side-Option {
				.boxContent {
					grid-template-columns: repeat(3, 1fr) !important;
					column-gap: 20px;
					row-gap: 20px;
					@include respond-to(mb) {
						grid-template-columns: repeat(2, 1fr) !important;
					}
				}

				.radio-beauty {
					display: none;
				}

				.m-se .product-info {
					// padding: 11px 0 3px;
					line-height: 30px;
				}

				.PriceText {
					text-align: center;
					margin-bottom: 20px;
				}
			}

			.Select-Ribbon {
				.product-info {
					line-height: 30px;
					margin-bottom: 15px;
				}

				.radio-beauty {
					display: none;
				}

				// .medals-RibbonOptinos {
				// 	padding: 10px;
				// 	.topBox {
				// 		.shape-box {
				// 			grid-template-columns: repeat(2, 1fr);
				// 		}
				// 	}
				// 	.gridBox {
				// 		display: flex;
				// 		align-items: center;
				// 		flex-direction: column;
				// 		.topBox {
				// 			display: grid;
				// 			grid-template-columns: repeat(3, 1fr);
				// 			.shape-box {
				// 				grid-template-columns: repeat(1, 1fr);
				// 				row-gap: 20px;
				// 			}
				// 		}
				// 		.UploadBox {
				// 			.area {
				// 				gap: 10px;
				// 			}
				// 		}
				// 	}
				// }
			}

			.Select-Package {
				.radio-beauty {
					display: none;
				}

				.boxContent {
					grid-template-columns: repeat(4, 1fr) !important;
					column-gap: 20px;
					row-gap: 20px;
					@include respond-to(mb) {
						grid-template-columns: repeat(2, 1fr) !important;
					}

					.m-se {
						.el-image {
							border-radius: 0;
						}
					}
				}

				.product-info {
					padding: 0;
					line-height: 30px;
				}

				.PriceText {
					margin-bottom: 15px;
				}
			}

			.Select-Medal-Finish {
				.m-se {
					height: 100%;
				}

				.product-info {
					margin-top: 0;
				}

				.quantity-div {
					table {
						width: 100%;
						border-collapse: collapse;
						border-spacing: 0;
						text-align: center;
						margin-bottom: 10px;

						td {
							border: 1px solid #cccccc;
							padding: 5px 0;

							@include respond-to(mb) {
								padding: 1vw 0;
							}
						}
					}

					.tab {
						width: 89%;
					}
				}

				.boxContent {
					grid-template-columns: repeat(3, 1fr) !important;
					column-gap: 20px;
					row-gap: 20px;

					@include respond-to(mb) {
						grid-template-columns: repeat(3, 1fr) !important;
						max-height: 475px;
						overflow: hidden;
					}

					.radio-beauty {
						display: none;
					}

					.number-input {
						margin: 0 0 8px 0;
					}

					> div {
						overflow: hidden;
					}

					.StepBox {
						padding-bottom: 0;
						background-color: #f9f9f9;

						&.active {
							background-color: #fff;

							.el-image {
								border-color: $color-primary !important;
							}
						}

						.se {
							.el-image {
								border: 1px solid transparent;
								transition: all 0.2s;
								border-radius: 10px;

								@media (any-hover: hover) {
									&:hover {
										border-color: $color-primary !important;
										box-shadow: 0 3px 4px 0 #ccc;
									}
								}
							}
						}

						.product-price {
							margin-bottom: 5px;
						}
					}

					@media screen and (max-width: 1800px) {
						grid-template-columns: repeat(5, 1fr);
					}

					@media screen and (max-width: 1678px) {
						grid-template-columns: repeat(4, 1fr);
					}

					@include respond-to(mb) {
						grid-template-columns: repeat(2, 1fr);
						column-gap: 5.5px;
						row-gap: 5px;

						.StepBox {
							background: #f4f5f5;
							border-radius: 5px;
							display: flex;
							align-items: center;
							padding-bottom: 10px;
							height: 100%;

							.se {
								.product-info {
									justify-content: flex-start;
									margin-top: 0;
								}
							}

							::v-deep .product-price {
								margin-bottom: 5px;
							}
						}
					}
				}

				@include respond-to(mb) {
					.StepBox .number-input {
						width: 100px !important;
					}

					.el-input__inner {
						font-size: 13px !important;
					}
				}
			}

			.step-title {
				font-size: 22px;
				font-weight: bold;
				display: flex;
				align-items: center;
				margin-bottom: 5px;

				@include respond-to(mb) {
					// margin-bottom: 10px;
					font-size: 18px;
					line-height: 18px;
				}

				.stepIndex {
					flex-shrink: 0;
					height: 28px;
					width: 120px;
					background: url("https://static-oss.gs-souvenir.com/web/quoteManage/20240816/bt_20240816RMkahR.png") no-repeat;
					line-height: 28px;

					span {
						display: inline-block;
						width: 87px;
						height: 28px;
						margin-right: 12px;
						border-radius: 4px;
						font-size: 22px;
						font-weight: bold;
						color: #fff;
						text-align: center;
					}
				}
			}

			.steop-title-bottom {
				background-color: #f5dadd;
				height: 2px;
				margin-bottom: 10px;

				.steop-title-bottom-to {
					background-color: #ba222f;
					width: 100px;
					height: 2px;
				}
			}

			.tips {
				margin-bottom: 10px;

				@include respond-to(mb) {
					margin-bottom: 8.5px;
				}
			}

			.kk {
				.part {
					z-index: 0;
					padding: 20px 10px;
					background: #ffffff;

					@include respond-to(mb) {
						padding: 20px 7px;
						border-radius: 5px;
					}

					.boxContent {
						display: grid;
						justify-content: space-between;
						grid-template-columns: repeat(3, 1fr);
						column-gap: 13px;
						row-gap: 13px;

						@include respond-to(mb) {
							column-gap: 5px;
							row-gap: 5px;
						}
					}

					&.Select-Shapes {
						.boxContent {
							grid-template-columns: repeat(5, 1fr);

							@include respond-to(mb) {
								grid-template-columns: repeat(3, 1fr);
							}

							.StepBox {
								border-color: transparent;
								position: relative;
								border-radius: 8px;

								@include respond-to(mb) {
									border-radius: 5px;
									border: 1px solid #ebebeb;
								}

								.se {
									.product-info {
										display: none !important;
									}
								}

								// @include respond-to(mb) {
								// }

								// @media (any-hover: hover) {
								// 	&:hover {
								// 		border-color: $color-primary !important;
								// 	}
								// }
							}

							.StepBox.active ::after {
								content: "\e7b8";
								font-family: "modalicon";
								position: absolute;
								color: #fff;
								top: 0;
								right: 0;
								width: 36px;
								height: 20px;
								font-size: 12px;
								text-align: center;
								line-height: 20px;
								background: $color-primary;
								border-radius: 0px 6px 0px 10px;

								@include respond-to(mb) {
									width: 22px;
									height: 14px;
									line-height: 14px;
									border-radius: 0px 4px 0px 6px;
								}
							}
						}
					}

					&.Select-Plating {
						.number-input {
							display: none;
						}

						@include respond-to(mb) {
							.product-info {
								align-items: start;

								.title {
									min-height: 28px;
								}
							}
						}
					}

					&.Select-Engraving {
						.boxContent {
							grid-template-columns: repeat(2, 1fr);
						}
					}

					&.More-Options {
						.boxContent {
							@include respond-to(mb) {
								grid-template-columns: repeat(2, 1fr);
								row-gap: 5px;
							}

							.StepBox {
								padding-bottom: 11px;

								.product-info {
									padding-bottom: 0;
								}

								.PriceText {
									margin-left: 49px;
									color: $color-primary;

									.normal-text {
										font-size: 14px;
									}

									@include respond-to(mb) {
										margin-left: 29px;
										margin-bottom: 8px;
									}
								}

								.tip-text {
									color: $color-primary;
									font-size: 14px;
								}
							}
						}
					}

					&.packaging-options {
						.boxContent {
							grid-template-columns: repeat(2, 1fr);

							.StepBox {
								padding-bottom: 11px;

								.product-info {
									padding-bottom: 0;
								}

								.PriceText {
									margin-left: 49px;
									color: $color-primary;

									.normal-text {
										font-size: 14px;
									}

									.tip-text {
										color: $color-primary;
										font-size: 14px;
									}

									@include respond-to(mb) {
										margin-left: 29px;
										margin-bottom: 8px;
									}
								}
							}
						}
					}

					&.Select-Quantity {
						margin-bottom: 0;
					}

					&.Delivery-Date {
						.discount-item0,
						.discount-item1,
						.discount-item2 {
							background-color: #fff;
						}

						> div {
							display: flex;
						}

						span.text {
							margin: 0 12px 0 20px;
							color: #9e9e9e;
						}

						.step-item {
							margin-bottom: 12px;

							.item-wrap {
								min-height: auto;
								border-color: #ccc;
							}
						}

						.step-date {
							padding: 0;

							.step-title {
								display: none;
							}

							.step-item {
								margin-bottom: 12px;
							}

							.step-box {
								// margin-left: 38px;
								// width: 474px;
								grid-template-columns: repeat(2, 1fr);
								column-gap: 14px;

								.top {
									.customCircle1 {
										display: none;
									}
								}

								.bottom {
									margin-top: 0;
									font-size: 14px;
									color: #9e9e9e;
									margin-left: 0 !important;
								}

								.item-wrap {
									border-radius: 5px;
								}
							}
						}

						.el-textarea {
							width: 474px;

							.el-textarea__inner:focus {
								border-color: $color-primary;
							}

							.el-textarea__inner {
								background-color: #f9f9f9;
								height: 130px !important;
								border-radius: 10px;
							}
						}

						@include respond-to(mb) {
							> div {
								flex-direction: column;
							}

							span.text {
								font-size: 14px;
								margin: 0 0 7px;
							}

							.step-date {
								.step-box {
									width: auto;
									margin-left: 0;
									grid-template-columns: 1fr;
									row-gap: 6px;
									margin-bottom: 3px;
									grid-template-columns: repeat(2, 1fr);
									column-gap: 14px;
								}

								.price-box {
									display: none;
								}

								.step-item {
									margin-bottom: 8px;
								}
							}

							.comments {
								display: block;

								.el-textarea {
									margin-top: 7px;
									width: 100%;

									.el-textarea__inner {
										background-color: #f9f9f9;
										height: 120px !important;
										border-radius: 5px;
									}
								}
							}
						}

						.step-item-params {
							display: grid;
							grid-template-columns: repeat(2, 1fr);
							grid-gap: 0.63rem;

							@include respond-to(mb) {
								grid-template-columns: repeat(2, 1fr);
								grid-gap: 0.42rem;
							}

							.param-item-wrap {
								overflow: hidden;
								position: relative;
								border: 2px solid transparent;
								border-radius: 6px;
								padding: 0.63rem;
								background-color: #f7f7f7;
								box-sizing: border-box;
								cursor: pointer;

								&.active {
									@include selectedStyle;
								}

								&:hover {
									border-color: $color-primary;
								}

								.param-item {
									position: relative;
									min-width: 0;
									display: flex;
									justify-content: flex-start;
									align-items: center;
									margin-bottom: 0.2rem;
									font-weight: 700;
								}
							}
						}
					}

					&.step-active {
						position: relative;
						z-index: 1000;
						border-radius: 10px;
					}
				}

				&:not(:last-child) {
					.part {
						// margin-bottom: 39px;

						@include respond-to(mb) {
							margin-bottom: 10px;
						}
					}
				}
			}

			.kk.type1 {
				.part {
					position: static;
				}
			}
		}
	}

	::v-deep .footer {
		// margin-top: 20px;
		display: grid;
		grid-template-columns: 1fr;
		justify-content: center;
		padding: 10px 0;
		z-index: 1;
		// .fonterOrderSummary {
		// 	position: relative;
		// 	display: flex;
		// 	align-items: center;
		// 	justify-content: center;
		// 	column-gap: 4px;
		// 	text-align: center;
		// 	width: 30%;
		// 	margin: 0 auto;
		// 	font-size: 14px;
		// 	color: #ffffff;
		// 	background-color: $color-primary;
		// 	border-radius: 11px 11px 0px 0px;
		// 	z-index: 1;
		// 	padding: 2px 0;
		// 	cursor: pointer;
		// 	&::before,
		// 	&::after {
		// 		--width: 0.4em;
		// 		width: var(--width);
		// 		height: 75%;
		// 		content: "";
		// 		position: absolute;
		// 		background-color: inherit;
		// 		border-color: inherit;
		// 		z-index: -1;
		// 	}

		// 	&::before {
		// 		left: calc(-1 * var(--width) + 0.1em);
		// 		bottom: 0;
		// 		clip-path: polygon(100% 0, 100% 100%, 0 100%);
		// 	}

		// 	&::after {
		// 		right: calc(-1 * var(--width) + 0.1em);
		// 		bottom: 0;
		// 		clip-path: polygon(0 0, 100% 100%, 0 100%);
		// 	}
		// }

		.fonterOrderSummary {
			user-select: none;
			cursor: pointer;
			padding: 0;
			height: 30px;
			width: 30%;
			margin: 0 auto;
			background-color: $color-primary;
			animation: none;
			display: flex;
			justify-content: center;
			align-items: center;
			color: white;
			font-size: 16px;
			font-weight: 400;
			border-top-right-radius: 10px;
			border-top-left-radius: 10px;
			transform: perspective(50px) rotateX(8deg);

			span {
				transform: perspective(50px) rotateX(-8deg);
			}

			i {
				font-size: 14px;
				margin-left: 5px;
			}
		}

		.fonterBtnGrap {
			position: relative;
			background: $color-primary;
			border-radius: 0px 0px 4px 4px;

			.btnGroup {
				width: 100%;
				padding: 20px 4vw;
				display: flex;
				flex-wrap: wrap;
				justify-content: center;
				align-items: center;
				column-gap: 15px;

				.tip-icon,
				.tip-icon2 {
					width: 18px;
					height: 18px;
					margin-left: 10px;
					color: #fff;
				}

				.tip-icon2 {
					color: $color-primary;
				}

				.gs-quote-button {
					flex: 1;
					height: 50px;

					&:hover {
						transform: scale(1.06, 1.1);
					}
				}

				.gs-quote-button:nth-child(2) {
					color: $color-primary;
				}
			}
		}

		@include respond-to(mb) {
			.fonterOrderSummary {
				width: 40%;
				height: 22px;
				font-size: 14px;
			}

			.fonterBtnGrap {
				.btnGroup {
					padding: 20px 10px;

					.gs-quote-button {
						height: 35px;
					}
				}

				button {
					flex: 1;
					border-radius: 4px;
					background: transparent;
					border: none;
				}
			}
		}

		.detailList.foldStyle {
			width: 100%;
			background: #f7f7f7;
			transition: all 0.3s cubic-bezier(0.42, 0, 0.58, 1);
			max-height: 0;
			overflow: hidden;

			&.showMore {
				max-height: 750px;
			}

			.topImg {
				display: none;
			}

			.detail-item,
			.price-item {
				&:hover {
					position: relative;
					background-color: initial;

					&::before {
						content: "";
						position: absolute;
						top: 0;
						left: 0;
						right: 0;
						bottom: 0;
						background-color: $color-primary;
						opacity: 0.1; // 背景的透明度
					}
				}
			}

			.footBtn {
				.sub {
					flex-direction: row;
					align-items: center;
					justify-content: center;
					width: 100%;
					margin: 5px 0px 12px;

					.total-price {
						width: 158px;
					}
				}

				.btnGroup {
					display: none;
				}
			}
		}

		.con {
			.viewMore {
				display: none !important;
			}

			.scrollBar {
				overflow: visible;
				max-height: fit-content;
			}
		}

		@include respond-to(mb) {
			grid-template-columns: 1fr;

			.con {
				.viewMore {
					display: block !important;
				}

				.scrollBar {
					overflow: hidden;
					max-height: 185px;
				}
			}
		}
	}

	::v-deep .myMaskMedals {
		z-index: 999 !important;
	}

	.el-icon-close {
		display: flex;
		justify-content: center;
		align-items: center;
		font-weight: 700;
		position: absolute;
		top: 0;
		right: 0;
		transform: translate(50%, -50%);
		width: 40px;
		height: 40px;
		cursor: pointer;
		background: #ffffff;
		box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.3);
		border-radius: 50%;
		z-index: 10;

		@include respond-to(mb) {
			transform: translate(0, 0);
			box-shadow: none;
		}
	}

	.custom-shadow {
		position: relative;
		background: #fff;
		border: 1px solid #d9d9d9;
		border-radius: 10px;
		padding: 27px 17px;

		&::before {
			content: "";
			position: absolute;
			z-index: -1;
			bottom: 12px;
			left: 5px;
			width: 50%;
			height: 20%;
			box-shadow: 0 14px 7px #d9dbdd;
			transform: rotate(-3deg);
		}

		&::after {
			content: "";
			position: absolute;
			z-index: -1;
			bottom: 12px;
			width: 50%;
			height: 20%;
			box-shadow: 0 14px 7px #d9dbdd;
			right: 5px;
			left: auto;
			transform: rotate(3deg);
		}
	}
}

@include respond-to(mb) {
	.custom-medals {
		background-color: #f2f5f7;
		font-size: 12px;

		.header {
			display: none;
		}

		.cufflink-quote {
			padding: 0 !important;
		}

		.content {
			width: 100%;
			padding: 0 15px 30px;

			.center-content {
				display: block;
				width: 100%;
			}
		}
	}
}
</style>