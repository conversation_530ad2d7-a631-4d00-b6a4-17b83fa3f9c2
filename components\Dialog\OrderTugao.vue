<template>
	<v-dialog
		:value="tugaoDialog"
		@input="$emit('update:tugaoDialog',false)"
		width="1600"
	>
		<div class="main d-flex" v-show="productList.length>0">
			<div class="menu">
				<div class="index"><span>{{ productNum + 1 }}</span>/{{ productList.length }}</div>
				<div flex scrollbar class="list-box" ref="productList">
					<div v-for="(i,index) in productList" :class="{ select: productNum == index }" :key="index"
							 @click="(productNum = index)">
						<div pointer>Proof #{{ index + 1 }}</div>
						<video v-if="i.picPath[0].url" :src="i.picPath[0].url" controls style="pointer-events: none;cursor:pointer;"
									 :poster="i.picPath[0].url" width="562" height="290"></video>
						<img v-else pointer :src="i.picPath[0].picLink" :alt="i.productName" :title="i.productName"/>
						<b :class="{ 'icon-gou': i.confirm == 1, 'icon-edit': i.confirm == 2 }"></b>
						<b :class="{ 'icon-Approved': i.confirm == 1, 'icon-Modify': i.confirm == 2 }"
							 :style="{'color:#1A73E8':i.confirm == 1}"></b>
					</div>
				</div>
			</div>

			<div class="product-box">
				<div flex center class="justify-space-between">
					<div flex center class="name">
						<strong>Proof #{{ productNum + 1 }}: {{ product.productName }}</strong>
						<!--            <label>Item Code: {{ product.itemCode }}</label>-->
						<b :class="{ 'icon-Approved': product.confirm == 1, 'icon-Modify': product.confirm == 2 }"></b>
					</div>
					<div class="status">
						<span>Artwork status:</span>
						<span v-if="product.confirm===1">Approved</span>
						<span v-if="product.confirm===2">Modify</span>
						<span v-if="product.confirm===0">Pending Artwork</span>
					</div>
				</div>

				<div flex center class="product">
					<b class="icon-left pointer" @click="artworkIndex -= 1"
						 v-show="product.picPath && product.picPath.length > 1"></b>
					<img v-show="product.picPath[artworkIndex].picLink" :src="product.picPath[artworkIndex].picLink"
							 :title="product.picPath[artworkIndex].draftType" @click="artworkView"/>
					<video v-show="product.picPath[artworkIndex].url" :src="product.picPath[artworkIndex].url" autoplay
								 :title="product.picPath[artworkIndex].draftType" controls></video>
					<b class="icon-right pointer" @click="artworkIndex += 1" v-show="product.picPath.length > 1"></b>
				</div>

				<div flex center class="system">
					<b flex pointer class="icon-a-tgsc-addzhuanhuan" @click="artworkView"
						 v-show="product.picPath[artworkIndex].picLink">View</b>
					<b flex pointer class="icon-a-tgsc-Downloadzhuanhuan" @click="artworkDownload(null)">Download</b>
					<!--          <b flex pointer class="icon-a-lrgl-dezhuanhuan" @click="artworkRemove"-->
					<!--             v-show="orderStatus == 3 && product.confirm != 1">Remove Product</b>-->
				</div>

				<div scrollbar1 flex class="list-box" v-show="product.picPath.length > 1">
					<div pointer v-for="(i, index) in product.picPath" :class="{ select: index == artworkIndex }"
							 @click="artworkIndex = index" :key="index">
						<img v-show="i.picLink" :src="i.picLink" :title="i.draftType"/>
						<video v-show="i.url" :src="i.url" :title="i.draftType"></video>
						<p>{{
								i.draftType || i.picName && i.picName.substring(0,
									i.picName.lastIndexOf(".")).replace(/-/g, " ")
							}}</p>
					</div>
				</div>

				<div class="pagenation" v-show="product.picPath.length>1">
					<div class="item-dot" :class="{active: i === artworkIndex}" v-for="(e,i) in product.picPath"
							 @click="showItem(product,i)" :title="i+1"></div>
				</div>

			</div>

			<div class="info-box">
				<v-hover v-slot="{ hover }" style="min-height: 28px">
					<div class="mt-3 d-flex pr-4">
						<span gray class="flex-shrink-0 mr-4">Payment Link:</span>
						<a v-if="tugaoInfo.drawingLink" :href="'https://'+tugaoInfo.drawingLink"
							 target="_blank"
							 class="mr-4 text-truncate"
							 style="outline: none;word-break: break-word">{{ tugaoInfo.drawingLink }}</a>
						<div style="min-width: 50px">
							<a href="javascript:;"
								 v-if="tugaoInfo.drawingLink && hover"
								 class="primary--text d-flex align-center pointer" style="font-size: 14px">
              <span @click="copyLink('https://'+tugaoInfo.drawingLink)" class="d-flex">
                <v-icon small color="primary" class="pa-0 ma-0">mdi-content-copy</v-icon>Copy</span>
							</a>
						</div>
					</div>
				</v-hover>

				<div class="message pt-8" v-if="product.messageLists && product.messageLists.length>0">
					<strong>Message of customer:
					</strong>
					<div class="con dark">
						<!--                  {{currentMessage}}-->
						<div v-for="(item,index) in product.messageLists" :key="index" class="mt-3" style="border-bottom: 1px solid #E6E7EB;">
							<div>{{ item.createTime }}</div>
							<div gray class="mt-3">{{ item.message }}</div>
							<div class="d-flex justify-space-between mt-2" v-for="(ele,idx) in JSON.parse(item.picPath)" :key="idx"
									 style="margin-right: 80px;">
								<div gray>{{ ele.imgName }}</div>
								<a href="javascript:;" @click="artworkDownload(ele.imgLink)">
									<b gray class="icon-jxsht-3d-xz" title="download"></b>
								</a>
							</div>
						</div>
					</div>
					<!-- <div>
						 <div v-for="(item,index) in product.messageLists" :key="index">
							 <div class="d-flex justify-space-between mt-2" v-for="(ele,idx) in JSON.parse(item.picPath)" :key="idx"
										style="margin-right: 80px;">
								 <div gray>{{ ele.imgName }}</div>
								 <a href="javascript:;" @click="artworkDownload(ele.imgLink)">
									 <b gray class="icon-jxsht-3d-xz" title="download"></b>
								 </a>
							 </div>
						 </div>
					 </div>-->
				</div>

				<div
					class="total-box                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           ">
					<strong>Products Details</strong>
					<div scrollbar class="view-box" ref="viewBox">
						<div class="details">
							<label v-for="(i,key) in product.productParam" :key="key">
								<span>{{ i.key }}: </span>
								<span>{{ i.value }}</span>
							</label>
							<label v-if="product.remark">
								<span>Notes:</span>
								<span>{{ product.remark }}</span>
							</label>
						</div>
					</div>

					<label class="view-more" @click="viewInfo" v-show="showViewMore">
						<strong>View More</strong> <b class="icon-Down"></b>
					</label>

					<label><span>Sub Total:</span>
						<label>
							<CCY-rate :price="product.quantity * (product.unitPrice.toFixed(2))"></CCY-rate>
							<label v-show="product.discount">
								(+{{ product.discount < 0 ? "+" + Math.abs(product.discount) : product.discount }}%
								{{ product.discount < 0 ? "rush fee for unit price" : "discount for unit price only" }})
							</label>
						</label>
					</label>
					<label><span>Quantity:</span>{{ product.quantity }}</label>
					<label><span>Unit Price:</span>
						<CCY-rate :price="product.unitPrice"></CCY-rate>
					</label>
					<label><span>Mold Fee:</span>
						<CCY-rate :price="product.mouldPrice"></CCY-rate>
					</label>
					<label><span>Extra Fee:</span>
						<CCY-rate :price="product.extraPrice"></CCY-rate>
					</label>
					<label><span>Final Price:</span>
						<CCY-rate class="price" :price="product.totalPrice"></CCY-rate>
					</label>
				</div>
			</div>
		</div>
	</v-dialog>
</template>

<script>
import {copyContent} from "@/utils/utils";
import CCYRate from "@/components/CCYRate";

export default {
	props: ["tugaoDialog", "tugaoInfo"],
	name: "TuGaoDialog",
	data() {
		return {
			productNum: 0,
			productIndex: 0,
			artworkIndex: 0,

			showModify: false,
			showHistory: false,
			showViewMore: false,

			// showMenu: !this.$store.getters.isMobile,
		};
	},
	components: {
		CCYRate
	},
	computed: {
		product() {
			return this.productList[this.productNum] || this.productList[0] || {};
			// return this.productList.find((i,index) => index == this.productNum) || this.productList[0] || {}
		},
		productList() {
			if (!this.tugaoInfo) {
				return {};
			}
			let arr = []
			let childList = this.tugaoInfo.childOrder;
			childList.forEach(item=>{
				// 获取所有子订单下的每条产品ordersProducts
				arr = [...arr,...item.ordersProducts]
			})
			this.tugaoInfo.ordersProducts = [...this.tugaoInfo.ordersProducts,...arr]

			let data = this.tugaoInfo.ordersProducts
			data.forEach(e => {
				/*if (typeof e.picPath === "string") {
					e.picPath = JSON.parse(e.picPath);
					e.picPath.forEach(i => {
						let name = i.picName;
						let index = name.lastIndexOf("\-");
						name = name.substring(0, index);
						i.picName = name;
					});
				}*/
				let str = e.picPath;
				if (str.indexOf("{") > -1) {
					e.picPath = JSON.parse(e.picPath);
				} else {
					// 设计系统过来的picPath为图片链接的字符串形式，需重新构造数组格式
					let arr = [];
					let urlName = "";
					let index = str.lastIndexOf("\/");
					urlName = str.substring(index + 1, str.length);
					arr.push({
						picName: urlName,
						picLink: e.picPath,
					});
					e.picPath = arr;
				}
				if (e.messageLists && e.messageLists.length > 0) {
					e.messageLists.forEach(e => {
						e.createTime = e.createTime.split(" ");
						let yearDate = e.createTime[0];
						yearDate = yearDate.split("-");
						e.createTime = yearDate[2] + "-" + yearDate[1] + "-" + yearDate[0];
					});
				}
				if (e.productParam.indexOf("[{") > -1) {
					e.productParam = JSON.parse(e.productParam);
				}
			});
			return data;
		},
	},
	watch: {
		tugaoInfo(val) {
			if (val) {
				// this.productNum = this.productList.find(i => !this.confirm) ? this.productList.find(i => !this.confirm).index : 0;
				setTimeout(() => {
					this.checkShowViewMore();
				}, 1000);
			}
		},
		productNum(val) {
			if (!this.productList[val]) return this.productNum = 0;
			this.artworkIndex = 0;

			this.checkShowViewMore();
			this.productIndex = this.productList.findIndex((i, index) => this.productNum == index);

			if (this.$refs.productList) {
				this.$refs.productList.scrollTo({top: (this.productIndex - 1) * window.innerWidth * 0.06, behavior: "smooth"});
			}
		},
		artworkIndex(val) {
			if (val < 0) this.artworkIndex = this.product.picPath.length - 1;
			if (val >= this.product.picPath.length) this.artworkIndex = 0;
		}
	},
	methods: {
		showItem(item, i) {
			this.artworkIndex = i;
			this.$forceUpdate();
		},
		copyLink(text) {
			copyContent(text);
			this.$message.success("copy success");
		},
		artworkView() {
			this.$viewerApi({
				images: this.product.picPath,
				options: {
					url: "picLink",
					initialViewIndex: this.artworkIndex
				}
			});
		},
		artworkDownload(url) {
			if (url) {
				window.open(url);
			} else {
				window.open(this.product.picPath[this.artworkIndex].picLink || this.product.picPath[this.artworkIndex].url);
			}
		},
		checkShowViewMore() {
			if (!this.$refs.viewBox) {
				return;
			}
			this.$nextTick(() => this.showViewMore = this.$refs.viewBox.scrollHeight > 240);
		},
		viewInfo() {
			this.showViewMore = false;
			this.$refs.viewBox.scrollTo({top: this.$refs.viewBox.scrollHeight, behavior: "smooth"});
		},
	},
	created() {
	},
	mounted() {

	}
};
</script>

<style scoped lang="scss">
::v-deep {
	.v-dialog {
		height: 890px;
	}
}

[pointer] {
	cursor: pointer;
}

[gray] {
	color: #858585;
}


[scrollbar] {
	overflow-y: scroll;
	max-height: 90vh;
	padding-bottom: 30px;
}

//[scrollbar] {
//  overflow: hidden;
//  //max-height: 90vh;
//  padding-bottom: 30px;
//}


[scrollbar1] {
	overflow: auto;

	&::-webkit-scrollbar {
		width: 3px;
		height: 3px;
	}

	&::-webkit-scrollbar-thumb {
		border-radius: 10px;
		background-color: rgba($color: #000000, $alpha: 0.1);
	}

	// &::-webkit-scrollbar-track {
	// background-color: rgba($color: #000000, $alpha: 0.05);
	// }
}

[flex] {
	display: flex;
}

[center] {
	align-items: center;
}

.main {
	height: 100%;
	background: #ffffff;
}

button {
	padding: 0 1em;
	transform: none;
	border-radius: 5px;
	position: relative;

	b {
		margin-left: 10px;

		&:hover .tip {
			display: block;
			text-align: left;
			padding: 1em 1.5em;
			border: #E6E7EB;
			color: #333333;
			background: white;
			box-shadow: 0 0 10px -5px rgba(0, 0, 0, 0.5);
			position: absolute;
			right: -3vw;
			left: -3vw;
			top: 4em;

			&::before {
				content: "";
				width: 0.75em;
				height: 0.75em;
				background: white;
				box-shadow: -3px -3px 6px -4px rgba(0, 0, 0, 0.5);
				transform: scale(1, 1.3) rotate(45deg);
				position: absolute;
				left: calc(50% - 0.375em);
				top: -0.375em;
			}
		}
	}
}

.menu {
	padding-left: 0.4vmax;
	background: #F5F7F9;

	.index {
		font-size: 14px;
		padding: 0 1vmax 5px;

		span {
			color: #0066CC;
		}
	}

	.list-box {
		padding-bottom: 3vmax;
		flex-direction: column;
		height: 700px;
		overflow: auto;
		font-size: 14px;

		> div {
			padding: 0.5vmax 1.2vmax 0.55vmax 0.7vmax;

			div {
				padding: 0.3em 0.8em 0.2em;
			}

			b {
				position: absolute;

				&.icon-gou,
				&.icon-edit {
					width: 16px;
					line-height: 16px;
					text-align: center;
					border-radius: 4px;
					background: #ADADAD;
					right: 1.2vmax;

					&::before {
						font-size: 10px;
						color: white;
						transform: scale(0.6);
					}
				}

				&.icon-gou {
					background: #0066CC;
				}

				&.icon-Approved,
				&.icon-Modify {
					font-size: 2.6em;
					color: #B2B2B2;
					bottom: 0.6vmax;
					right: 1.6vmax;
				}

				&.icon-Approved {
					color: #0066CC;
				}
			}
		}
	}

}

.menu::-webkit-scrollbar {
	width: 6px;
	height: 100%;
}

.menu::-webkit-scrollbar-thumb {
	width: 6px;
	height: 73px;
	background: #DBDBDB;
	border-radius: 3px;
}


.list-box > div {
	position: relative;

	img,
	video {
		object-fit: contain;
		width: 6.67vw;
		height: 4.69vw;
		border: 2px solid #F5F5F5;
		border-radius: 0px 1vh 1vh 1vh;
	}

	&.select {
		background: white;
		border-radius: 1vh 0px 0 1vh;

		div {
			width: 6em;
			color: white;
			min-width: fit-content;
			background: #0066CC;
			border-radius: 1vh 0.3em 0 0;
			position: relative;

			&::before {
				content: "";
				border-style: solid;
				border-width: 2em 0.7em 0 0;
				border-color: transparent #fff transparent transparent;
				position: absolute;
				right: -0.22em;
				bottom: 0;
			}
		}

		img,
		video {
			border-color: #0066CC;
			box-shadow: 0px 2px 5px 0px rgba(0, 0, 0, 0.4);
		}
	}
}

.product-box {
	position: relative;
	flex: 2;
	padding-left: 3vmax;
	padding-bottom: 2vmax;
	max-width: calc(66% - 1.5vmax);

	.name {
		height: 4em;

		strong {
			font-size: 18px;
		}

		label {
			font-size: 16px;
			margin: 0 1em;
			color: #858585;
		}

		b {
			font-size: 2.5em;
			color: #B2B2B2;

			&.icon-Approved {
				color: #1A73E8;
			}
		}
	}

	.status {
		position: absolute;
		left: 104%;
		min-width: 240px;

		:first-child {
			color: #858585;
			font-size: 14px;
			margin-right: 10px;
		}

		> span {
			color: #0066CC;
			font-size: 16px;
		}
	}

	.product {
		height: 26vmax;
		padding: 1.5vmax 0;
		justify-content: center;
		border-top: 1px solid #EBEBEB;
		position: relative;

		img,
		video {
			width: auto;
			height: 100%;
			object-fit: contain;
			max-width: calc(100% - 4em);
		}

		> b {
			width: 2em;
			height: 2em;
			font-size: 1.5em;
			line-height: 2em;
			color: white;
			font-weight: bold;
			text-align: center;
			border-radius: 50%;
			background: rgba(0, 0, 0, 0.5);
			position: absolute;

			&.icon-left {
				left: 0;
			}

			&.icon-right {
				right: 0;
			}
		}
	}

	.system {
		justify-content: center;

		b {
			font-size: 14px;
			margin: 0 2em;
			line-height: 2em;

			&::before {
				font-size: 1.8em;
				margin-right: 0.2em;
			}
		}
	}

	.list-box {
		padding-bottom: 1em;
		//overflow: hidden;
		width: 30vw;
		margin: 0 auto;
		justify-content: left;

		> div {
			min-height: 4.69vw;
			max-height: 86px;
			margin: 1vmax 0.5vmax 0;

			img,
			video {
				min-width: 6.27vw;
				border-radius: 1vh;
			}

			p {
				color: white;
				text-align: center;
				border-radius: 0 0 0.4vmax 0.4vmax;
				background: rgba(0, 0, 0, 0.5);
				position: absolute;
				bottom: 0.11em;
				right: 0.11em;
				left: 0.11em;
				position: absolute;
				margin-bottom: 0;
				bottom: 0;
			}
		}
	}

	.bottom-list {

	}

	.pagenation {
		display: flex;
		justify-content: center;
		margin-top: 1em;

		.item-dot {
			width: 10px;
			height: 10px;
			border-radius: 50%;
			background: lightgray;
			margin-right: 5px;

			&.active {
				background: #999999;
			}
		}
	}
}

.info-box {
	font-size: 14px;
	flex: 1;
	margin-top: 4.6em;
	padding-left: 2vmax;
	max-width: calc(33% - 2em);
	border-top: 1px solid #EBEBEB;
	position: relative;

	.details,
	.message,
	> div {
		//padding: 1.5em 0;

		> strong {
			font-size: 16px;
			display: block;
			padding-bottom: 1em;
		}

		> label {
			display: flex;
			margin-bottom: 7px;
			align-items: center;

			> span {
				min-width: 8em;
				margin-right: 0.5em;
				color: #858585;
			}

			.price {
				color: #EB5757;
				font-weight: bold;
				font-size: calc(1em + 4px);
			}
		}

		button {
			margin-bottom: 10px;

			&[primary] {
				margin-top: 1vmax;
			}

			b {
				margin-left: 5px;
			}
		}
	}

	.message {
		height: 215px;
		overflow-y: auto;

		> strong {
			font-size: 16px;
			display: flex;
			justify-content: space-between;

			b {
				margin-right: 1.2em;
				color: #EBEBEB;

				&::before {
					font-size: 1.2em;
					margin-right: 5px;
				}
			}
		}

		> label {
			span {
				width: 100%;
			}

			a {
				width: 26em;

				&:hover {
					color: #EBEBEB;
				}
			}

			b {
				color: #999999;

				&.icon-gou {
					margin-right: 1em;
					color: #699f4c;
				}
			}
		}
	}

	.message::-webkit-scrollbar {
		width: 6px;
		height: 100%;
	}

	.message::-webkit-scrollbar-thumb {
		width: 6px;
		height: 73px;
		background: #DBDBDB;
		border-radius: 3px;
	}


	.total-box {
		position: relative;
		margin-top: 1.25em;
		padding-top: 1.25em;
		border-top: 1px solid #E6E7EB;

		/*.view-more {
			//line-height: 3em;
			//background: white;
			//position: absolute;
			//top: calc(35vh - 3em + 1vmax);
			//left: 3vmax;
			//right: 0;

			strong {
				color: #0066CC;
			}

			b {
				color: #B0B2B5;
				transform: scale(0.5);
			}
		}

		.view-box {
			max-height: 188px;
			overflow: hidden;
			&.more{
				overflow: auto;
			}
		}
		.view-box::-webkit-scrollbar{
			width: 6px;
			height: 100%;
			//background: #DBDBDB;
			//border-radius: 3px;
		}
		.view-box::-webkit-scrollbar-thumb{
			width: 6px;
			height: 73px;
			background: #DBDBDB;
			border-radius: 3px;
		}*/


		.view-more {
			line-height: 3em;
			background: white;
			position: absolute;
			top: calc(30vh - 3em + 1vmax);
			left: 0;
			right: 0;

			strong {
				color: #2E62E2;
				font-size: 16px;
			}

			b {
				color: #B0B2B5;
				transform: scale(0.5);
			}
		}

		.view-box {
			//max-height: 35vh;
			max-height: 26vh;
		}

		.view-box::-webkit-scrollbar {
			width: 6px;
			height: 100%;
		}

		.view-box::-webkit-scrollbar-thumb {
			width: 6px;
			height: 73px;
			background: #DBDBDB;
			border-radius: 3px;
		}

		.details,
		.message,
		> div {
			//padding: 1vmax 0;

			> strong {
				font-size: 16px;
				display: block;
				padding-bottom: 1em;
			}

			> label {
				display: flex;
				margin-bottom: 7px;
				align-items: center;

				> span {
					min-width: 8em;
					margin-right: 0.5em;
					color: #858585;

					&:last-child {
						color: #333333;
					}
				}

				.price {
					color: #EB5757;
					font-weight: bold;
					font-size: calc(1em + 4px);
				}
			}

			button {
				margin-bottom: 10px;

				&[primary] {
					margin-top: 1vmax;
				}

				b {
					margin-left: 5px;
				}
			}
		}
	}

	.modifying {
		.contact {
			color: #999;
			margin: 1.5em 0;
		}

		b.icon-a-icon-web3zhuanhuan {
			padding: 1em;
			color: #999;
			border-radius: 0.5em;
			background: #F7F7F7;

			&::before {
				margin-right: 0.5em;
				color: #699f4c;
			}
		}
	}
}
</style>
