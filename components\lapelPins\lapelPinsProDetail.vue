<template>
	<div class="lanyardProDetail">
		<h2 class="lanyardProDetailTitle">Fabrication Options for Custom Lanyards</h2>
		<div class="detailInfoBox" v-for="(detail, ind) in detailInfoListArr" :key="ind">
			<div class="switchBox">
				<div class="switchItem" @click="selectItemFn(ind, item)" :class="{ active: selectItem[ind]?.id == item.id }" v-for="(item, index) in detail" :key="item.id">
					<div class="infoTitle">{{ item.alias }}</div>
				</div>
			</div>
			<div class="contentBox" v-show="selectItem[ind]">
				<component :is="selectItem[ind]?.styleClass" :itemData="selectItem[ind]"></component>
			</div>
		</div>
	</div>
</template>

<script>

export default {
	name: "lanyardProDetail",
	components: {  },
	props: {
		detailInfoList: {
			type: Array,
			default: () => [],
		},
		quotePid: {
			type: [String, Number],
			default: "",
		},
		quoteCateId: {
			type: [String, Number],
			default: "",
		},
	},
	data() {
		return {
			selectItem: {},
			selectIndex: -1,
		};
	},
	watch: {
		detailInfoListArr(newVal) {
			if (newVal) {
				this.detailInfoListArr.forEach((item, index) => {
					this.selectItem[index] = item[0];
				});
			}
		},
	},
	created() {},
	mounted() {
	},
	methods: {
		selectItemFn(ind, item) {
			if (!this.detailInfoList.length) return;
			this.selectItem[ind] = item;
			this.$set(this.selectItem, ind, item);
			this.$forceUpdate();
		},
		selectItemFn2(index) {
			if (!this.accessoryQuoteParam.length) return;
			this.selectItem22 = this.accessoryQuoteParam[index];
			this.selectIndex22 = index;
		},
		getThreeItem(arr) {
			let temp = arr.reduce((acc, item, index) => {
				const chunkIndex = Math.floor(index / 3);
				if (!acc[chunkIndex]) {
					acc[chunkIndex] = [];
					this.$set(this.selectItem, chunkIndex, null);
				}
				acc[chunkIndex].push(item);
				return acc;
			}, []);
			return temp;
		},
	},
	computed: {
		isMobile() {
			return this.$store.getters.isMobile;
		},
		detailInfoListArr() {
			if (!this.isMobile) {
				return [this.detailInfoList];
			} else {
				return this.getThreeItem(this.detailInfoList);
			}
		},
	},
};
</script>
<style scoped lang="scss">
.lanyardProDetail {
	.lanyardProDetailTitle {
		font-weight: bold;
		font-size: 32px;
		color: #333333;
		margin-bottom: 20px;
		text-transform: none;
	}
	.switchBox {
		position: relative;
		height: 50px;
		border-radius: 25px;
		background-color: #f6f6f6;
		display: flex;
		align-items: center;
		justify-content: space-around;
		overflow: auto hidden;
		padding-bottom: 6px;
		.switchItem {
			width: fit-content;
			height: 100%;
			position: relative;
			color: #333;
			font-weight: bold;
			font-size: 18px;
			cursor: pointer;
			display: flex;
			align-items: center;
			&.active {
				color: #ff764a;
				&::before {
					display: block;
				}
				&::after {
					display: block;
				}
			}
			&::after {
				display: none;
				position: absolute;
				left: 0;
				bottom: 1px;
				content: "";
				width: 100%;
				height: 3px;
				border-radius: 2px;
				background: #ff764a;
			}
			@include respond-to(mb) {
				font-size: 13px;
				padding: 0 6px;
				text-align: center;
				&::before {
					display: none;
					position: absolute;
					left: 50%;
					bottom: -4px;
					transform: translateX(-50%);
					content: "";
					width: 0;
					height: 0;
					border-left: 8px solid transparent;
					border-right: 8px solid transparent;
					border-top: 6px solid #ff764a;
					z-index: 1;
				}
			}
		}
	}
	.contentBox {
		width: 100%;
		overflow: hidden;
		margin: 20px 0 50px;
	}
	@include respond-to(mb) {
		margin: 0 -3vw;
		.lanyardProDetailTitle {
			font-size: 16px;
			padding: 0 3vw;
			width: 100%;
		}
		.detailInfoBox {
			padding: 0 3vw;
			width: 100%;
			.switchBox {
				background-color: #fff !important;
				border-radius: 0;
				&::after {
					content: "";
					position: absolute;
					left: 0;
					bottom: 6px;
					width: 100%;
					height: 1px;
					background-color: #dfdfdf;
				}
			}
		}

		.contentBox {
			margin: 10px 0 20px;
		}
	}
}
</style>
