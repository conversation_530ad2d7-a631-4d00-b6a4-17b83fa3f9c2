<template>
	<div class="modal-box iframe-box" :style="modal.style">
		<iframe :src="modal.iframeUrl" width="100%" height="100%"></iframe>
	</div>
</template>

<script>
export default {
	name: "modalIframe",

	props: {
		data: { type: Object, default: {} },
	},

	data() {
		return {
			modal: {
				...this.data,
			},
		};
	},
};
</script>

<style lang="scss" scoped>
.iframe-box {
	iframe {
		min-height: inherit;
		border: none;
	}
}
</style>
