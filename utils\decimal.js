import Decimal from "decimal.js";

const MAX_SAFE_INTEGER=Number.MAX_SAFE_INTEGER;

export class Calculator {
	constructor(value) {
		if (this.isValidNumber(value)) {
			this.decimal=new Decimal(value);
		} else {
			console.warn("请输入合理数字！", value, "类型:", typeof value);
			throw new Error("Invalid input");
		}
	}

	isValidNumber(value) {
		// 如果是 Calculator 实例，提取其值
		if (value instanceof Calculator) {
			value=value.toNumber();
		}
		// 清理字符串输入
		if (typeof value==="string") {
			value=value.trim();
			value=Number(value);
		}
		return typeof value==="number"&&!isNaN(value)&&isFinite(value)&&Math.abs(value)<=MAX_SAFE_INTEGER;
	}

	static create(value) {
		try {
			return new Calculator(value);
		} catch (error) {
			console.error("Calculator.create 错误:", error);
			throw error;
		}
	}

	add(num) {
		return this.operate("add", num);
	}

	subtract(num) {
		return this.operate("sub", num);
	}

	multiply(num) {
		// 处理 Calculator 实例或字符串
		const number=num instanceof Calculator? num.toNumber():(typeof num==="string"? Number(num.trim()):num);
		if (!this.isValidNumber(number)) {
			console.warn("multiply 无效输入:", number);
			throw new Error("Invalid input for multiply");
		}
		return this.operate("mul", number);
	}

	divide(num) {
		const number=num instanceof Calculator? num.toNumber():(typeof num==="string"? Number(num.trim()):num);
		if (number===0) throw new Error("Division by zero is not allowed.");
		return this.operate("div", number);
	}

	operate(operation, num) {
		let operand;
		try {
			// 处理 Calculator 实例或字符串
			const cleanedNum=num instanceof Calculator? num.toNumber():(typeof num==="string"? num.trim():num);
			operand=new Decimal(cleanedNum);
		} catch (error) {
			console.warn("Decimal 构造失败:", { num, error: error.message });
			throw new Error(`[DecimalError] Invalid argument: ${num}`);
		}
		let result;
		try {
			result=this.decimal[operation](operand).toNumber();
		} catch (error) {
			console.warn("operate 计算失败:", { operation, error: error.message });
			throw new Error(`[DecimalError] Operation failed: ${operation}`);
		}
		return Calculator.create(result);
	}

	round(dp=0) {
		return Calculator.create(this.decimal.toDecimalPlaces(dp, Decimal.ROUND_HALF_UP).toNumber());
	}

	toNumber() {
		return this.decimal.toNumber();
	}

	toString() {
		return this.decimal.toString();
	}
}