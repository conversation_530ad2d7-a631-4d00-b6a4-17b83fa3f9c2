<template>
    <div class="neon-box" id="quote" noDebounce>
        <b pointer class="icon-guanbi" @click="$store.commit('setMask', false)"></b>
        <template v-if="isManage">
            <v-card height="300">
                <v-row justify="center">
                    <v-overlay :absolute="true" :value="true">
                        <v-chip> {{ lang.neon.neonQuote }} </v-chip>
                    </v-overlay>
                </v-row>
            </v-card>
        </template>
        <NeonSigns v-else pageQuoteDialog :customTitle="data.customTitle" :isInquiry="data.isInquiry" :queryId="data.queryId" :parentCateId="data.parentCateId" :class="{ titleCenter: data.isTitleCenter }">
        </NeonSigns>
    </div>
</template>

<script>
import NeonSigns from "@/components/Neon/NeonSigns.vue";
export default {
    name: "NeonSignsQuoteDialog",
    props: {
        data: {
            type: Object,
            default: {},
        },
    },
    components: {
        NeonSigns,
    },
    data() {
        return {
            modal: {
                style: {},
                ...this.data,
            },
            isManage: false,
        };
    },
    computed: {
        lang() {
            return this.$store.getters.lang.quote || {};
        },
    },
    watch: {
        modal: {
            handler(val) {
                if (process.env.isManage) this.$emit("update:data", val);
            },
            immediate: true,
            deep: true,
        },
    },
    created() {
        if (!this.data?.customTitle) {
            this.data["customTitle"] = this.lang.neon.customTitle
        }
        if (process.env.isManage) {
            this.isManage = true;
        }
    },
};
</script>

<style lang="scss" scoped>
.neon-box {
    position: relative;
    border: 10px;
    background-color: white;
	align-self: end;
	width: 100vw !important;
	max-width: none !important;
	.icon-guanbi{
		transform: translate(0, -50%) !important;
	}
	@include respond-to(mb){
		.icon-guanbi{
			transform: translate(0, 15%) !important;
		}
    }

}



::v-deep .titleCenter .content #neonTop {
    padding-left: 45% !important;
}
</style>