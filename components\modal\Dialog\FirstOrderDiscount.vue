<!--  -->
<template>
	<div class='discountBox'>
         <b pointer class="icon-guanbi" @click="$store.commit('setMask', false)"></b>
         <p class="title">{{ lang.title }}</p>
		 
		 <div class="text_box">
			<p class="text text1">
				{{ lang.text1 }}
			</p>

			<p class="text text2">
				{{ lang.text2 }}
			</p>

			<p class="text text3">
				{{ lang.text3 }}
			</p>

			<p class="text text4">
				{{ lang.text4 }}
			</p>
		 </div>


		<button class="box_btn" @click="copyCode">
			<span>{{ lang.btnText }}</span>
			<b class="icon-a-fuzhi1"></b>
			<span style="text-decoration: underline;">PENS20</span>
		</button>

		<p class="subTitle">
			{{ lang.subTitle }}
		</p>

    </div>
</template>

<script>
	export default {
		name: 'FirstOrderDiscount',
		components: {},
		data() {
			return {

			};
		},
		computed: {
			lang() {
				return this.$store.getters.lang.firstOrderDiscountTemporary || {};
			},
		},
		watch: {},
		methods: {
			copyCode(){
				const textarea = document.createElement('textarea');
				textarea.value = "PENS20";
				document.body.appendChild(textarea);
				textarea.select();
				document.execCommand('copy');
				document.body.removeChild(textarea);
				this.$toast.success("Code copied!")
			}
		},
		created() {

		},
		mounted() {

		},
	}

</script>
<style lang='scss' scoped>
.discountBox{
    // width: 39vw;
    width:  46.75em !important;
    height:  34.5em !important;
    background-image: url("https://static-oss.gs-souvenir.com/web/quoteManage/20240927/Group_32817_20240927etJBWY.jpg");
	background-size: cover;
    color: #666666;
	text-align: center;
	padding-top: 11%;
	display: flex;
    flex-direction: column;
    align-items: center;
	row-gap: 1em;
	user-select: none;
	.icon-guanbi{
		color: #000;
	}
	.title{
		font-size:1.5em;
		color: #333333;
		font-weight: bold;
	}
	.subTitle{
		font-size:1em;
		color: #333333;
		font-weight: bold;
	}
	.text{
		font-size:1em
	}
	.text2{
		margin: 1em;
	}
	.text3,.text4{
		padding: 0 4em;
	}
	.box_btn{
		background: #000;
		color: #FFF;
		border-radius: 0.25em;
		padding: 0.75em 1.5em;
		margin-top: 1em;
	}
	.box_btn:hover{
		transform: scale(1.05);
	}
}

// ipad端样式
@media screen and (max-width: $pad-width) {
}

// 手机
@media screen and (max-width: $mb-width) {
	.discountBox{
		width: 89.33vw !important;
		height: 105.47vw !important;
		padding-top: 28%;
		background-image: url("https://static-oss.gs-souvenir.com/web/quoteManage/20240927/pens_sale_202409272sWhd2.jpg");
		.title{
			font-size:calc(1em + 2px);
		}
		.text_box{
			display: flex;
			flex-direction: column;
			line-height: 1.5;
			padding: 0 10%;
		}
		.text2,.text3,.text4{
			all: unset;
		}
		.box_btn{
			margin-top: 0.5em;
		}
	}
}
</style>
