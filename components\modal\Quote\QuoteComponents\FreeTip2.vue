<template>
	<div class="freeTip">
		<div v-if="['ca', 'au', 'nz'].includes(this.countryCode)">
            <b class="icon-jxsht-ybp-dg"></b>
            Factory Direct Deal
        </div>
		<div v-else>
			<b class="icon-jxsht-ybp-dg"></b><strong>{{ lang.free }} {{ lang.bannerQuote.shipping }}</strong>
		</div>
		<div>
			<b class="icon-jxsht-ybp-dg"></b><strong>{{ lang.free }} {{ lang.bannerQuote.dp }}</strong>
		</div>
		<div>
			<b class="icon-jxsht-ybp-dg"></b><strong>{{ lang.free }} {{ lang.bannerQuote.ds }}</strong>
		</div>
		<div>
			<b class="icon-jxsht-ybp-dg"></b><strong>{{ lang.free }} {{ lang.bannerQuote.turnaround }}</strong>
		</div>
	</div>
</template>

<script>
export default {
	computed: {
		countryCode() {
			return this.$store.state.country.countryCode;
		},
		lang() {
			return this.$store.getters.lang.quote || {};
		},
	},
};
</script>

<style lang="scss" scoped>
.freeTip {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 0.8rem;
	margin: 1rem 0;

	b {
		color: #68bd2c;
		margin-right: 4px;
	}
}
</style>
