<template>
	<div class="mb-4" :class="stepData.styleName">
		<half-design-select-tip :customText="stepData.attributeTitle" :selectedValue="shape"></half-design-select-tip>
		<slot name="stepText"></slot>
		<div class="textWrap">
			<div>{{ langSemiCustom.AText }}</div>
			<div class="text-item-list">
				<div class="text-item">
					<div class="text-input">
						<v-text-field id="myInput" :value="myTextProperty.text" solo flat outlined dense hide-details label="Add Text" @input="addText">
							<template #append>
								<v-menu :close-on-content-click="false" offset-y left min-width="300">
									<template v-slot:activator="{ on, attrs }">
										<div v-bind="attrs" v-on="on">
											<img src="~/static/img/icon_color.png" alt="Color Wheel" title="Color Wheel" style="width: 25px; height: 25px" />
										</div>
									</template>
									<v-card class="color-picker-wrap" color="#ffffff">
										<div class="color-picker-title">Edit Colors</div>
										<div class="color-picker-list">
											<div class="color-item" :class="{ active: item.code === myTextProperty.fill }" v-for="item in colorList" :key="item.id" :style="{ backgroundColor: item.code }" :title="item.pantone" @click="changeTextProperty(item, 'fill')">
												<v-icon color="#ffffff" small> mdi-check</v-icon>
											</div>
										</div>
									</v-card>
								</v-menu>
							</template>
						</v-text-field>
					</div>
					<div class="font-bold" :class="{ active: myTextProperty.fontWeight === 'bold' }" @click="changeTextProperty(myTextProperty.fontWeight === 'normal' || myTextProperty.fontWeight === 400 ? 'bold' : 'normal', 'fontWeight')">B</div>
					<div class="font-style" :class="{ active: myTextProperty.fontStyle === 'italic' }" @click="changeTextProperty(myTextProperty.fontStyle === 'normal' || !myTextProperty.fontStyle ? 'italic' : 'normal', 'fontStyle')">I</div>
					<div class="fontFamilyWrap">
						<v-select :value="myTextProperty.fontFamily" :menu-props="{ bottom: true, offsetY: true }" solo flat outlined dense hide-details :items="fontsData" item-text="name" item-value="name" label="Font Family" @change="changeTextProperty($event, 'fontFamily')">
							<template #item="{ item }">
								<span :style="{ fontFamily: item.name }">{{ item.name }}</span>
							</template>
						</v-select>
					</div>
				</div>
			</div>
		</div>
		<div class="imageWrap">
			<div class="mb-2">{{ langSemiCustom.BText }}</div>
			<div class="box">
				<div class="box-con1" :class="{ active: uploadImg.url }" @click="triggerUpload('upload')">
					<half-design-check-box></half-design-check-box>
					<div class="box-con1-text">
						<div class="t1">{{ langSemiCustom.uploadOrText }}</div>
						<div class="t2">{{ langSemiCustom.previewLogoOrText }}</div>
					</div>
					<input type="file" accept=".jpg,.jpeg,.png,.gif,.bmp" ref="upload" @click.stop @change="uploadPic" />
				</div>
				<div class="box-img" v-show="uploadImg.url">
					<div class="text-center">
						<div class="uploadImg">
							<img :src="uploadImg.url" alt="upload" title="upload" />
							<v-icon class="close" small @click="delImg">mdi-close-circle</v-icon>
						</div>
						<div class="imgName pa-1">
							{{ uploadImg.name }}
						</div>
					</div>
					<div class="changeImg" @click="triggerUpload('change')">
						<v-icon color="primary" class="mr-2">mdi-plus-circle</v-icon>
						Change Logo
					</div>
				</div>
			</div>
		</div>
		<pic-color-change :oneColor.sync="oneColor" :filterColor="filterColor" :uploadImg="uploadImg" :picColorList="picColorList" :oldColor="oldColor" :newColor="newColor" :colorList="colorList" :copyPicColorList="copyPicColorList" @changeOneColor="changeOneColor" @selectPicColor="selectPicColor" @changePicColor="changePicColor" @filterPicColorBefore="filterPicColorBefore"> </pic-color-change>
	</div>
</template>
<script>
import { designMixin } from "@/mixins/halfDesign";

export default {
	mixins: [designMixin],
	computed: {
		fontsData() {
			return require("@/assets/json/fontList.json");
		}
	},
	mounted() {
		this.$Bus.$on("focusDesignInput", this.focusInput);
	},
	methods: {
		focusInput() {
			let input = document.getElementById("myInput");
			if (input) {
				input.focus();
			}
		},
	},
	beforeDestroy() {
		this.$Bus.$off("focusDesignInput");
	},
};
</script>
<style scoped lang="scss">
@import "~assets/css/half.scss";

.colorCircle {
	width: 25px;
	height: 25px;
	border-radius: 50%;
	@media screen and (max-width: $mb-width) {
		width: 20px;
		height: 20px;
	}
}

.textWrap {
	display: grid;
	grid-template-columns: auto 1fr;
	grid-gap: 10px;
	margin-bottom: 20px;

	.text-item {
		display: flex;
		align-items: center;
		margin-bottom: 10px;

		.fontFamilyWrap {
			flex: 0 0 250px;
		}

		&:last-child {
			margin-bottom: 0;
		}

		& > div {
			margin-right: 10px;

			&:last-child {
				margin-right: 0;
			}
		}
	}

	.font-bold,
	.font-style {
		flex-shrink: 0;
		display: flex;
		justify-content: center;
		align-items: center;
		width: 40px;
		height: 40px;
		background: #fff;
		border: 1px solid rgba(0, 0, 0, 0.38);
		border-radius: 5px;
		cursor: pointer;
		transition-duration: 0.15s;
		transition-property: color;
		transition-timing-function: cubic-bezier(0.25, 0.8, 0.25, 1);

		&:hover {
			border-color: #333333;
		}

		&.active {
			border-color: $color-primary;
			border-width: 2px;
			color: $color-primary;
		}
	}
	.font-style.active {
		font-style: italic;
	}
}

.box {
	padding: 0 10px;
	@include radius-response;
	background-color: $background-color;

	.box-con1 {
		position: relative;
		display: grid;
		align-items: center;
		grid-template-columns: auto 1fr;
		grid-gap: 10px;
		text-align: left;
		cursor: pointer;

		input[type="file"] {
			position: absolute;
			top: 0;
			left: 0;
			z-index: -1;
			opacity: 0;
			height: 0;
		}

		.box-con1-text {
			padding: 10px 0;

			.t1 {
				font-weight: 700;
			}
		}

		&:hover {
			::v-deep .check-box {
				border-color: $color-primary;

				.check-box-inner {
					background-color: $color-primary;
				}
			}
		}
	}

	.box-con1.active {
		::v-deep .check-box {
			border-color: $color-primary;

			.check-box-inner {
				background-color: $color-primary;
			}
		}

		.box-con1-text {
			border-bottom: 1px solid $border-color;
		}
	}

	.box-img {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20px;

		.uploadImg {
			position: relative;
			width: 150px;
			height: 100px;
			text-align: center;
			background-color: $background-color2;

			img {
				width: 100%;
				height: 100%;
				object-fit: contain;
			}

			.close {
				position: absolute;
				top: 0;
				right: 0;
				transform: translate(50%, -50%);
				cursor: pointer;
			}
		}

		.changeImg {
			@include flex-center;
			@include step-default;
			border-radius: 30px;
			cursor: pointer;
		}
	}
}

@include respond-to(mb) {
	.textWrap {
		grid-template-columns: 1fr;
		grid-gap: 5px;
		margin-bottom: 10px;

		.text-item {
			flex-wrap: wrap;
			margin-bottom: 10px;

			.text-input {
				margin-right: 0;
				width: 100%;
				margin-bottom: 10px;
			}

			.fontFamilyWrap {
				width: 150px;
				max-width: 150px;
			}

			& > div {
				margin-right: 5px;
			}
		}
	}
	.box {
		padding: 0 10px;
		background-color: $background-color;

		.box-img {
			padding: 10px;

			.uploadImg {
				width: 80px;
				height: 80px;
			}

			.changeImg {
				border-radius: 30px;
				font-size: 12px;
			}
		}
	}
}
</style>
