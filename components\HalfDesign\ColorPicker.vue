<template>
	<v-card color="color-picker-wrap">
		<div class="color-picker-title">{{ langSemiCustom.editColors }}</div>
		<div class="color-picker-list">
			<div class="color-item" :class="{ active: item.code === textProperty.fill }" v-for="item in colorList" :key="item.id" :style="{ backgroundColor: item.code }" :title="item.pantone" @click="selectColor(item, 'fill')">
				<v-icon color="#ffffff" small>mdi-check</v-icon>
			</div>
		</div>
	</v-card>
</template>
<script>
export default {
	props: ["textProperty", "colorList"],
	methods: {
		selectColor(item, property) {
			this.$emit("selectColor", {
				data: item,
				property,
			});
		},
	},
	computed: {
		langSemiCustom() {
			return this.$store.getters.lang?.semiCustom;
		},
	},
};
</script>

<style scoped lang="scss">
@import "~assets/css/half.scss";
</style>
