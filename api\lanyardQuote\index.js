import { request } from "@/utils/request";

// pid 	父id
const getByPId = function (data) {
  return request({
    url: '/quote/cate/getByPId',
    method: 'get',
    params: data,
  })
};

//cateId
const getCateParamRelationByCateId = function (data) {
  return request({
    url: '/quote/cate/getCateParamRelationByCateId',
    method: 'get',
    params: data,
  })
};

const findAll = function (data) {
  return request({
    url: '/quote/colors/findAll',
    method: 'post',
    data: data,
  })
};
//图库
const getAll = function () {
  return request({
    url: '/quote/icons/getAll',
    method: 'post',
  })
};
//根据类型获取图库
const getAllByType = function (type) {
	return request({
	  url: '/quote/icons/getAll?type=' + type,
	})
  };
//获取货币类型
const getCurrency = function () {
  return request({
    url: '/quote/currency/getAllCurrency',
    method: 'get',
  })
};
//价格计算
// {
//   "QtyDetailDTO":[
//       {"quantity":1,"paramType":5,"paramId":5,"paramValue":"#ffffff"},
//       {"quantity":1,"paramType":5,"paramId":5,"paramValue":"#ffffff"},
//       {"quantity":1,"paramType":5,"paramId":5,"paramValue":"#ffffff"}
//       ],
//   "AccessoriesDetailDTO":[
//       {"quantity":1,"paramId":5},
//       {"quantity":1,"paramId":5},
//       {"quantity":1,"paramId":5}
//   ],
//   "cateId":2,
//   "paramIdList":[1,2,3],
//   "projectName":"lanyard",
//   "sizeId":15
// }
const calculate = function (data) {
  return request({
    url: '/quote/quoteCalculate/calculate',
    method: 'post',
    data: data,
  })
};

//价格表
const quotationList = function (data) {
  return request({
    url: '/quote/quoteCalculate/quotationList',
    method: 'post',
    data: data,
  })
};
export const indexApi = {
  getByPId,
  getCateParamRelationByCateId,
  findAll,
  calculate,
  getAll,
  quotationList,
  getCurrency,
  getAllByType
};
