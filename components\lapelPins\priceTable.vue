<template>
	<div class="priceTable">
		<div class="tableBox">
			<div class="tableMain custom-scrollbar" v-if="tableData.length > 0">
				<table cellspacing="0" cellpadding="0">
					<thead>
						<tr class="flex-Boxs">
							<th class="noWrap" :class="{ oneArrange: !isMobile }">{{ langCart.itemNo }}.</th>
							<th class="noWrap2">{{ lang.neon.Size }} (inch)</th>
							<th class="noWrap3" v-show="fdEnableThick">{{ lang.fdPins.thickness }} (mm)</th>
							<th v-for="(number, index) in tableData[0].quantityPrice" :key="index">{{ number.totalQuantity }}</th>
							<th class="lastNoWrap" :class="oneArrange" v-if="fdEnableMoldPrice">{{ lang.fdPins.moldCharge }}</th>
							<th class="lastNoWrap" :class="{ oneArrange: !isMobile }" v-if="fdEnableSetUpPrice">{{ lang.fdPins.setupFee }}</th>
						</tr>
					</thead>
					<tbody>
						<tr class="flex-Boxs tableItem" v-for="(item, index) in tableData" :key="index">
							<td class="noWrap normal" :class="{ oneArrange: !isMobile }">{{ item.itemNo }}</td>
							<td class="noWrap2 normal">{{ item.sizeName }}</td>
							<td class="noWrap3 normal" v-show="fdEnableThick">{{ item.thickness }}</td>
							<td class="headItem" :class="{ hovered: arrData[index][index2] }" v-for="(item2, index2) in item.quantityPrice" :key="index2 + '_' + index">
								<el-popover popper-class="selfLapelPinsPopper" placement="bottom" width="245" :trigger="isMobile ? 'manual' : 'hover'" :offset="-115" :close-delay="0" :value="arrData[index][index2]" :visible-arrow="false" :transition="null" @mouseenter="handleReferenceMouseEnter(index, index2)" @mouseleave="handleReferenceMouseLeave(index, index2)">
									<div class="abso" @mouseenter="handleReferenceMouseEnter(index, index2)" @mouseleave="handleReferenceMouseLeave(index, index2)">
										<div class="absoCloseBtn" @click="handleReferenceClose" v-show="isMobile">
											<b class="icon-guanbi"></b>
										</div>
										<div class="absoBox">
											<div class="absoOne">
												<div class="absoOneMain">
													<span style="font-weight: 700">{{ cateName }}</span
													><br />
													{{ langCart.itemNo }}: {{ item.itemNo }}<br />
													{{ lang.neon.Size }}: {{ item.alias }}<br />
													{{ lang.qty }}: {{ item2.totalQuantity }} pcs<br />
													<div class="diff1" @click="copyBtn1(item, item2)"></div>
													<div class="diff2" @click="copyBtn2(item, item2)"></div>
												</div>
											</div>
											<div class="absoSecond">
												<div class="absoSecondMain">
													{{ langCart.unitPrice }}:
													<span class="currency_data" style="color: #ff6600">
														<span v-show="!item.isQur"> <CCYRate :price="item2.foundationUnitPrice"></CCYRate> ({{ item2.fdUnitPriceDiscountCode }}) </span>
														<span v-show="item.isQur">QUR</span> </span
													><br />
													<span v-if="fdEnableMoldPrice">{{ lang.fdPins.moldCharge }}: </span
													><span v-if="fdEnableMoldPrice" class="currency_data" style="color: #ff6600">
														<span v-show="!item.isQur"> <CCYRate :price="item.moldCharge"></CCYRate> ({{ item.moldChargeDiscountCode }}) </span>
														<span v-show="item.isQur">QUR</span>
														<br />
													</span>
													<span v-if="fdEnableSetUpPrice">{{ lang.fdPins.setupFee }}: </span
													><span v-if="fdEnableSetUpPrice" class="currency_data" style="color: #ff6600">
														<span v-show="!item.isQur"> <CCYRate :price="item.setupFee"></CCYRate> ({{ item.setupFeeDiscountCode }}) </span>
														<span v-show="item.isQur">QUR</span>
														<br />
													</span>
													{{ lang.fdPins.totalPrice }}:
													<span class="currency_data" style="color: #ff6600; font-size: 18px; font-weight: bold">
														<span v-show="!item.isQur">
															<CCYRate :price="item2.totalPrice"></CCYRate>
														</span>
														<span v-show="item.isQur">QUR</span> </span
													><br />
												</div>
											</div>
											<div class="absoBoxBtn">
												<div class="copyBtn" @click="copyQuote(item, item2)">
													{{ lang.fdPins.copyQuote }}
													<div class="hoverTip" :class="{ noSetUpAndMold: !fdEnableMoldPrice && !fdEnableSetUpPrice, hasSetUpAndMold: fdEnableMoldPrice && fdEnableSetUpPrice, isQur: item.isQur }" @click.stop>
														<div class="tipHead">Instant Quote Overview</div>
														<div class="tipContent">
															<span style="font-weight: 700">{{ cateName }}</span>
															<p>Price Includes:</p>
															<p>- Up to 5 Color Fills</p>
															<p>- Polished Surface</p>
															<p>- One Military or Rubber Clutch</p>
															<p>- Finish Options: Brass/Nickel/Copper/Black Nickel/Dye Color</p>
															<p>- Individually Polybagged</p>
															<p>- Free Proof （Please send your artwork in vector format ASAP for a free mockup）</p>
															<p>- Free Shipping within the US or Canada</p>
															<p>- Delivery including transit: Approximately 3 to 3.5 weeks from proof approval and order placement</p>
															<div style="margin-bottom: 1.2em"></div>
															<p>Product Item No.: {{ item.itemNo }}</p>
															<p>Size: {{ item.alias }}</p>
															<p>Quantity: {{ item2.totalQuantity }} pcs</p>
															<p>------------------------------</p>
															<p>
																List Price: <span v-show="!item.isQur">{{ getNowPrice(item2.foundationUnitPrice, 2, true) }}</span
																><span v-show="item.isQur">QUR</span>
															</p>
															<p v-show="fdEnableMoldPrice">
																Mold Charge:
																<span v-show="!item.isQur">
																	{{ getNowPrice(item.moldCharge, 2, true) }}
																</span>
																<span v-show="item.isQur"> QUR </span>
															</p>
															<p v-show="fdEnableSetUpPrice">
																Setup Fee: <span v-show="!item.isQur">{{ getNowPrice(item.setupFee, 2, true) }}</span>
																<span v-show="item.isQur">QUR</span>
															</p>
															<p>
																Total Price: <span v-show="!item.isQur">{{ getNowPrice(item2.totalPrice, 2, true) }}</span>
																<span v-show="item.isQur">QUR</span>
															</p>
															<div style="margin-bottom: 1.2em"></div>
															<p>Please note that this quote is preliminary and subject to your artwork.</p>
															<p>If you require a faster production time, please let us know. Urgent requests may incur a rush fee.</p>
														</div>
													</div>
												</div>
												<div class="printBtn" @click="printQuote(item, item2)">
													{{ lang.fdPins.printQuote }}
												</div>
											</div>
											<div class="addBox" v-show="!item.isQur"></div>
											<div class="priceNum" v-show="!item.isQur">
												{{ getNowPrice(item2.fdNetUnitPrice, 2) + "." + getNowPrice(item.netMoldCharge, 2) + "." + getNowPrice(item2.fdNetTotalPrice, 2) }}
											</div>
										</div>
									</div>
									<div slot="reference" @click="isMobile ? handleReferenceClick(index, index2) : ''">
										<span v-show="item.isQur">QUR</span>
										<span v-show="!item.isQur">
											<CCYRate :price="item2.foundationUnitPrice"></CCYRate>
										</span>
									</div>
								</el-popover>
							</td>
							<td class="lastNoWrap normal" :class="oneArrange" v-show="fdEnableMoldPrice">
								<span v-show="item.isQur">QUR</span>
								<span v-show="!item.isQur">
									<CCYRate :price="item.moldCharge"></CCYRate>
								</span>
							</td>
							<td class="lastNoWrap normal" :class="{ oneArrange: !isMobile }" v-show="fdEnableSetUpPrice">
								<span v-show="item.isQur">QUR</span>
								<span v-show="!item.isQur">
									<CCYRate :price="item.setupFee"></CCYRate>
								</span>
							</td>
						</tr>
					</tbody>
				</table>
			</div>
			<div class="subscript">
				<span></span><span>{{ discountCode }}</span>
			</div>
		</div>
	</div>
</template>

<script>
import { copyContent } from "@/utils/utils";
import { downloadFdPdf } from "@/api/quote/fdCommon.js";
import "@/plugins/element";
export default {
	name: "priceTable",
	props: {
		discountCode: {
			type: String,
		},
		tableData: {
			type: Array,
			default: () => [],
		},
		arrData: {
			type: Array,
			default: () => [],
		},
		cateName: {
			type: String,
		},
	},
	components: {},
	data() {
		return {
			html2canvas: null,
		};
	},
	watch: {},
	computed: {
		lang() {
			return this.$store.getters.lang.quote || {};
		},
		langCart() {
			return this.$store.getters.lang.cart || {};
		},
		isMobile() {
			return this.$store.getters.isMobile;
		},
		code() {
			return this.$store.state.currency.code;
		},
		country() {
			return this.$store.state.language.language;
		},
		symbol() {
			return this.country == "zh" ? "" : this.$store.state.currency.symbol || "$";
		},
		rate() {
			return Number(this.$store.state.currency.rate || 1) * 100;
		},
		fdEnableThick() {
			if (!this.tableData || !this.tableData.length) return false;
			return this.tableData[0]?.fdEnableThick;
		},
		fdEnableMoldPrice() {
			return this.tableData && this.tableData.length > 0 && this.tableData[0].fdEnableMoldPrice;
		},
		fdEnableSetUpPrice() {
			return this.tableData && this.tableData.length > 0 && this.tableData[0].fdEnableSetUpPrice;
		},
		oneArrange() {
			if (!this.isMobile) {
				if (this.fdEnableSetUpPrice) {
					return "";
				} else {
					return "oneArrange";
				}
			}
		},
	},
	methods: {
		copyQuote(data1, data2) {
			let lines = [this.cateName, "", "Price Includes:", "- Up to 5 Color Fills", "- Polished Surface", "- One Military or Rubber Clutch", "- Finish Options: Brass/Nickel/Copper/Black Nickel/Dye Color", "- Individually Polybagged", "- Free Proof （Please send your artwork in vector format ASAP for a free mockup）", "- Free Shipping within the US or Canada", "- Delivery including transit: Approximately 3 to 3.5 weeks from proof approval and order placement", "", `Product Item No.: ${data1.itemNo}`, `Size: ${data1.alias}`, `Quantity: ${data2.totalQuantity} pcs`, "------------------------------"];
			if (data1.isQur) {
				lines.push(`List Price: QUR`);
			} else {
				lines.push(`List Price: ${this.getNowPrice(data2.foundationUnitPrice, 2, true)}`);
			}

			if (this.fdEnableMoldPrice) {
				// 如果有模具费用，并且费用大于0，则添加到信息中
				if (data1.isQur) {
					lines.push(`Mold Charge: QUR`);
				} else {
					lines.push(`Mold Charge: ${this.getNowPrice(data1.moldCharge, 2, true)}`);
				}
			}

			if (this.fdEnableSetUpPrice) {
				// 如果有模具费用，并且费用大于0，则添加到信息中
				if (data1.isQur) {
					lines.push(`Setup Fee: QUR`);
				} else {
					lines.push(`Setup Fee: ${this.getNowPrice(data1.setupFee, 2, true)}`);
				}
			}
			if (data1.isQur) {
				lines.push(`Total Price: QUR`);
			} else {
				lines.push(`Total Price: ${this.getNowPrice(data2.totalPrice, 2, true)}`);
			}
			lines.push("", "Please note that this quote is budgetary pending your artwork.", "If you need it sooner, let us know; we may accommodate this at no extra cost.", "Urgent requests may incur a rush fee. Please call me if you have any questions.");

			let val = lines.join("\n");
			val = val.replace(/^[ \t]+/gm, "");
			copyContent(val);
			this.$toast.success("Quick Quote has been successfully copied!");
		},
		copyBtn1(data1, data2) {
			let lines = ["Dear Sir/Madam,", "", "Thank you for your interest in our products.", "", "Please note that this quote is BUDGETARY as you have not provided the artwork yet.", "", `${this.cateName}`, "Price Includes:", "● Up to 5 Color Fills", "● Polished Surface", "● One Military or Rubber Clutch", "● Choice of Finishes: Brass/Nickel/Copper/Black Nickel/Dye Color", "● Individually Polybag", "● Free Proof", "● Free Shipping to one address in US or Canada", "", `${data1.itemNo}`, `Size: ${data1.alias}`, `Quantity: ${data2.totalQuantity} pcs`, ""];
			if (data1.isQur) {
				lines.push(`Unit Price: QUR`);
			} else {
				lines.push(`Unit Price: ${this.getNowPrice(data2.fdNetUnitPrice, 2, true)}`);
			}

			if (this.fdEnableMoldPrice) {
				if (data1.isQur) {
					lines.push(`Mold Charge: QUR`);
				} else {
					lines.push(`Mold Charge: ${this.getNowPrice(data1.netMoldCharge, 2, true)}`);
				}
			}

			if (this.fdEnableSetUpPrice) {
				if (data1.isQur) {
					lines.push(`Setup Fee: QUR`);
				} else {
					lines.push(`Setup Fee: ${this.getNowPrice(data1.netSetupFee, 2, true)}`);
				}
			}
			if (data1.isQur) {
				lines.push(`Total Price: QUR`);
			} else {
				lines.push(`Total Price: ${this.getNowPrice(data2.fdNetTotalPrice, 2, true)}`);
			}
			lines.push("", "FREE PROOF (send us art in vector format ASAP so we can provide you a mockup/proof for free)", "FREE SHIPPING (to one address in North America)", "", "DELIVERY including transit: About 3 to 3-1/2 weeks from approval of proof and placement of order.", "If you need it sooner, let us know. We may be able to arrange for that for free.", "If you need it real fast, let us know also but there may be a RUSH fee.", "", "Please call me if you have any questions.", "", "Full Designs Team", "888 655 7789");

			let val = lines.join("\n");
			val = val.replace(/^[ \t]+/gm, "");
			copyContent(val);
		},
		copyBtn2(data1, data2) {
			let priceDetails = [`Qty: ${data2.totalQuantity} pcs`];
			if (data1.isQur) {
				priceDetails.push(`Unit Price: QUR`);
			} else {
				priceDetails.push(`Unit Price: ${this.getNowPrice(data2.fdNetUnitPrice, 2, true)}`);
			}
			if (this.fdEnableMoldPrice) {
				if (data1.isQur) {
					priceDetails.push(`Mold Charge: QUR`);
				} else {
					priceDetails.push(`Mold Charge: ${this.getNowPrice(data1.netMoldCharge, 2, true)}`);
				}
			}
			if (this.fdEnableSetUpPrice) {
				if (data1.isQur) {
					priceDetails.push(`Setup Fee: QUR`);
				} else {
					priceDetails.push(`Setup Fee: ${this.getNowPrice(data1.netSetupFee, 2, true)}`);
				}
			}
			if (data1.isQur) {
				priceDetails.push(`Total Price: QUR`);
			} else {
				priceDetails.push(`Total Price: ${this.getNowPrice(data2.fdNetTotalPrice, 2, true)}`);
			}
			let val = priceDetails.join("\n");
			val = val.replace(/^[ \t]+/gm, "");
			copyContent(val);
		},
		printQuote(data1, data2) {
			this.$emit("switchLoading", "open");
			let lines = [`${this.cateName}`, "", "Price Includes:", "- Up to 5 Color Fills", "- Polished Surface", "- One Military or Rubber Clutch", "- Finish Options: Brass/Nickel/Copper/Black Nickel/Dye Color", "- Individually Polybagged", "- Free Proof (Please send your artwork in vector format ASAP for a free mockup)", "- Free Shipping within the US or Canada", "- Delivery including transit: Approximately 3 to 3.5 weeks from proof approval and order placement", "", `Product Item No.: ${data1.itemNo}`, `Size: ${data1.alias}`, `Quantity: ${data2.totalQuantity} pcs`, "------------------------------"];
			if (data1.isQur) {
				lines.push(`List Price: QUR`);
			} else {
				lines.push(`List Price: ${this.getNowPrice(data2.foundationUnitPrice, 2, true)}`);
			}
			if (this.fdEnableMoldPrice) {
				if (data1.isQur) {
					lines.push(`Mold Charge: QUR`);
				} else {
					lines.push(`Mold Charge: ${this.getNowPrice(data1.moldCharge, 2, true)}`);
				}
			}
			if (this.fdEnableSetUpPrice) {
				if (data1.isQur) {
					lines.push(`Setup Fee: QUR`);
				} else {
					lines.push(`Setup Fee: ${this.getNowPrice(data1.setupFee, 2, true)}`);
				}
			}
			if (data1.isQur) {
				lines.push(`Total Price: QUR`);
			} else {
				lines.push(`Total Price: ${this.getNowPrice(data2.totalPrice, 2, true)}`);
			}
			lines.push("", "Please note that this quote is budgetary pending your artwork.", "If you need it sooner, let us know; we may accommodate this at no extra cost.", "Urgent requests may incur a rush fee. Please call me if you have any questions.");
			let val = lines.join("\n");
			let dom = document.createElement("div");
			dom.style.cssText = "width:800px;background: #ffffff;padding:20px;display: flex;justify-content: center;align-items: center;font-size: 16px;text-align:left; white-space: pre-wrap;word-break:break-word";
			val = val.replace(/^[ \t]+/gm, "");
			dom.innerHTML = val;
			document.body.appendChild(dom);
			this.$nextTick(() => {
				setTimeout(() => {
					if (!this.html2canvas) return;
					this.html2canvas(dom).then((canvas) => {
						let dataURL = canvas.toDataURL("image/png");
						document.body.removeChild(dom);
						downloadFdPdf({ fileName: `${data1.itemNo}_${data1.sizeCode}_${data2.totalQuantity}.png`, base64Str: dataURL.split(";base64,")[1] }).then((res) => {
							if (res.data) window.open(res.data);
							this.$emit("switchLoading", "close");
						});
					});
				}, 0);
			});
		},
		handleReferenceMouseEnter(index, index2) {
			this.$emit("setArrData", index, index2, true);
		},
		handleReferenceMouseLeave(index, index2) {
			this.$emit("setArrData", index, index2, false);
		},
		handleReferenceClick(index, index2) {
			this.$emit("setArrData", index, index2, true, "clearOther");
		},
		handleReferenceClose() {
			this.$emit("closeArrData");
		},
		getNowPrice(price, decimal = 2, showSymbol = false, removeZero = false) {
			let newPrice = price && !isNaN(price) ? (Number(Number(price).toFixed(decimal)) * this.rate) / 100 : 0;
			// 需要适配分隔符的国家标识 德国
			newPrice = newPrice.toLocaleString(["de"].includes(this.country) ? this.country : "en", { minimumFractionDigits: decimal, maximumFractionDigits: decimal });
			let firstSymbol = "";
			if (showSymbol) firstSymbol = this.code === "USD" ? "US" + this.symbol : this.symbol;
			if (removeZero) {
				return firstSymbol + newPrice.replace(/(\.0*|0+)$/, "") + (this.country == "zh" ? "元" : "");
			} else {
				return firstSymbol + newPrice + (this.country == "zh" ? "元" : "");
			}
		},
	},
	created() {},
	async mounted() {
		if (process.client) {
			this.html2canvas = (await import("html2canvas")).default;
		}
	},
};
</script>

<style lang="scss">
.selfLapelPinsPopper * {
	font-family: Calibri;
}

.selfLapelPinsPopper {
	transition: none;
	margin-top: -4px !important;
	margin-bottom: 0 !important;
	padding: 0 !important;
	font-family: Calibri !important;

	.abso {
		position: relative;
		padding: 12px;
		width: 100%;
		line-height: 28px;
		background: rgba(255, 255, 255, 0.9);
		color: #000;
		font-weight: 700;
		text-align: left;
		z-index: 10;
		font-size: 12px;
		border-radius: 10px;

		// box-shadow: 2px 2px 10px #999;
		.absoBox {
			display: flex;
			justify-content: space-between;
			flex-direction: column;
			row-gap: 10px;

			.absoOne {
				width: 100%;
				display: flex;
				flex-direction: column;

				.absoOneMain {
					position: relative;
					background: #f8f8f8;
					border-radius: 6px;
					padding: 10px;
					height: 100%;
					font-size: 16px;
					font-family: Calibri;
					font-weight: normal;

					.diff1 {
						width: 20px;
						height: 20px;
						position: absolute;
						top: 0;
						right: 0;
					}

					.diff2 {
						width: 20px;
						height: 20px;
						position: absolute;
						bottom: 0;
						right: 0;
					}
				}
			}

			.absoSecond {
				width: 100%;
				display: flex;
				flex-direction: column;

				.absoSecondMain {
					background: #f8f8f8;
					border-radius: 6px;
					padding: 10px;
					height: 100%;
					font-size: 16px;
					font-family: Calibri;
					font-weight: normal;
				}
			}

			.absoBoxBtn {
				display: flex;
				align-items: center;
				justify-content: space-between;
				cursor: pointer;

				.copyBtn {
					width: 99px;
					height: 30px;
					background: #ffffff;
					border-radius: 8px;
					border: 1px solid #ff6600;
					font-weight: bold;
					font-size: 12px;
					color: #ff6600;
					line-height: 30px;
					text-align: center;
					position: relative;

					.hoverTip {
						display: none;
						width: 460px;
						position: absolute;
						right: calc(100% + 38px);
						top: -345px;
						background: #fff;
						border-radius: 8px;
						color: #333;
						font-size: 14px;
						padding: 15px 15px 20px;
						box-shadow: 0px 0px 8px 0px rgba(126, 125, 125, 0.13);

						&.noSetUpAndMold {
							top: -333px;
						}

						&.hasSetUpAndMold {
							top: -356px;
						}

						&::after {
							content: "";
							position: absolute;
							top: 71%;
							right: -23px;
							border-left: 24px solid #fff;
							border-top: 18px solid transparent;
							border-bottom: 18px solid transparent;
							filter: drop-shadow(0px 2px 5px rgba(4, 0, 0, 0.1));
							z-index: -1;
						}

						&::before {
							content: "";
							position: absolute;
							top: 71%;
							right: -40px;
							width: 50px;
							height: 35px;
							background-color: transparent;
							z-index: -1;
						}

						.tipHead {
							text-align: center;
							font-weight: bold;
							font-size: 16px;
						}

						.tipContent {
							text-align: left;
							font-weight: 400;
							font-size: 14px;

							p {
								word-break: break-word;
								line-height: 1.2;
							}
						}
					}

					@media (any-hover: hover) {
						&:hover {
							.hoverTip {
								display: block;
							}
						}
					}
				}

				.printBtn {
					width: 99px;
					height: 30px;
					background: #ff6600;
					border-radius: 8px;
					font-weight: bold;
					font-size: 12px;
					color: #ffffff;
					line-height: 30px;
					text-align: center;
				}
			}

			.addBox {
				height: 1px;
			}

			.priceNum {
				position: absolute;
				left: 14px;
				bottom: 2px;
				font-weight: 400;
				font-size: 10px;
				line-height: initial;
				color: #333;
			}
		}

		.absoCloseBtn {
			position: absolute;
			left: 50%;
			bottom: -30px;
			transform: translateX(-50%);
			display: flex;
			align-items: center;
			justify-content: center;
			background-color: transparent;
			width: 24px;
			height: 24px;
			border-radius: 50%;
			border: 1px solid #666;

			b {
				font-size: 10px;
				color: #666;
			}
		}
	}
}
</style>

<style scoped lang="scss">
.custom-scrollbar::-webkit-scrollbar {
	/*滚动条整体样式*/
	width: 5px;
	/*高宽分别对应横竖滚动条的尺寸*/
	height: 6px;
	border-radius: 10px;
	border: 1px solid #dfdfdf;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
	/*滚动条里面小方块*/
	border-radius: 10px;
	background: #8e9dc2;
}

.custom-scrollbar::-webkit-scrollbar-track {
	/*滚动条里面轨道*/
	border-radius: 10px;
}

.flex-center {
	display: flex;
	align-items: center;
	justify-content: center;
}

.priceTable * {
	font-family: Calibri;
}

.priceTable {
	width: 100%;
	display: flex;
	align-items: center;
	flex-direction: column;
	color: #333333;
	font-weight: 500;
	font-family: Calibri;

	.tableBox {
		width: 100%;
		position: relative;

		.flex-Boxs {
			.headItem {
				position: relative;

				label {
					padding: 0 4px;
				}

				span {
					&:first-child {
						display: inline-block;
						width: 100%;
						height: 100%;
						text-align: center;

						::v-deep .el-popover__reference-wrapper {
							width: 100%;
							height: 100%;
							display: inline-block;

							.el-popover__reference {
								display: flex;
								align-items: center;
								justify-content: center;
								width: 100%;
								height: 100%;
								text-align: center;
								span {
									width: 100%;
									text-align: center;
								}
								&:hover {
									background: #fe9412;
								}
							}
						}
					}
				}

				&:hover {
					background: #fe9412;
				}

				&.hovered {
					background: #fe9412;
				}
			}

			.normal {
				height: 100%;
				@media (any-hover: hover) {
					&:hover {
						background: #fe9412;
					}
				}
			}

			.noWrap {
				padding: 0 4px;
				min-width: 80px;
				text-align: center;
				&.oneArrange {
					width: 140px;
					text-align: center;
					// padding-right: 20px;
				}
			}

			.noWrap2 {
				padding: 0 4px;
				width: 0;
				min-width: 140px;
				max-width: 200px;
				text-align: center;
				white-space: normal;
				@include respond-to(mb) {
					min-width: 92px;
				}
			}

			.noWrap3 {
				width: 0;
				min-width: 100px;
				max-width: 140px;
				text-align: center;
				@include respond-to(mb) {
					min-width: 92px;
				}
			}

			.lastNoWrap {
				width: 0;
				min-width: 100px;
				max-width: 140px;
				@include respond-to(mb) {
					min-width: 90px;
				}
				&.oneArrange {
					width: 160px;
					text-align: center;
					// padding-left: 20px;
				}
			}
		}

		.tableMain {
			max-width: 100%;
			overflow: auto hidden;
			width: 100%;
			font-size: 14px;
			padding-bottom: 36px;

			table {
				min-width: 100%;
				width: fit-content;

				thead {
					height: 50px;
					background: #969eb5;
					font-size: 14px;
					font-weight: bold;
					color: #ffffff;
				}

				tr {
					th,
					td {
						min-width: 42px;
						text-align: center;
						border: 0;
					}

					td {
						height: 42px;
						white-space: nowrap;
						border-bottom: 1px solid #dfdfdf;
					}
				}
			}

			.tableItem {
				height: 42px;

				&:nth-child(odd) {
					background-color: #ffffff;
				}

				&:nth-child(even) {
					background-color: #f7f7f7;
				}

				&:last-child {
					// border-bottom: 1px solid #dfdfdf;
				}
			}
		}

		.subscript {
			position: absolute;
			bottom: 10px;
			right: 0;
			display: flex;
			align-items: center;
			justify-content: space-between;
			font-weight: bold;
			font-size: 16px;
			color: #333333;
		}

		@include respond-to(mb) {
			.tableMain {
				padding-bottom: 30px;
			}

			.subscript {
				bottom: 10px;
			}
		}
	}
}
</style>
