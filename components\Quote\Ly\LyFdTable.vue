<template>
	<div class="lyFdTable">
		<div class="table">
			<div class="th">
				<div v-for="(item, index) in model.field" :key="index" class="td">
					<div v-if="index == 0" class="tdLeft">{{ item.value }}</div>
					<div v-if="index != 0 && index != model.field.length - 1">{{ item.value }}</div>
					<div v-if="index == model.field.length - 1" class="tdRight">{{ item.value }}</div>
				</div>
			</div>
			<div>
				<!-- foundationUnitPrice totalPrice this.$emit("input", this.customModel); -->
				<div class="tr tr0" :class="baseModel.radio == 0 ? 'trOn' : ''" @click="radioChange(customModel, 0)">
					<div class="td tdLeft">
						<el-radio v-model="baseModel.radio" :label="0" />
						<input class="tdInput" v-model="customModel[model.field[0].key]" type="number" oninput="if(value.length>5)value=value.slice(0,5)" placeholder="Enter Qty" @input.stop="handleInput" />
					</div>
					<div class="td">
						<CCYRate :price="customModel[model.field[1].key]" />
						each ({{ baseModel.discount || "C" }})
					</div>
					<div class="td tdRight">
						<CCYRate :price="customModel[model.field[2].key]" />
					</div>
				</div>
				<div v-for="(item, index) in model.list" :key="index" class="tr" :class="baseModel.radio == index + 1 ? 'trOn' : ''" @click="radioChangeTwo(item, index + 1)">
					<div v-if="model.field?.length > 0" class="td tdLeft">
						<el-radio v-model="baseModel.radio" :label="index + 1" />
						<div>{{ item[model.field[0].key] }}</div>
					</div>
					<div v-if="model.field?.length > 1" class="td">
						<CCYRate :price="item[model.field[1].key]" style="margin-right: 2px" />
						each ({{ item.fdUnitPriceDiscountCode || "C" }})
					</div>
					<div v-if="model.field?.length > 1" class="td tdRight">
						<!-- 如果传了总价就取 如果没传就 自己计算 单价*数量 -->
						<CCYRate :price="item[model.field[2].key] || getTotal(item)" />

						<div v-if="item.labelText" class="hot" :style="'background:' + item.labelColor">{{ item.labelText }}</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
/*
Fd自定义数量表格
更多文档：https://www.yuque.com/chaojigang-eu86m/adyym3/kg333tzs4d93copf
语雀密码：itv8
*/
import { customModel, baseModel } from "@/assets/quote/entity/LyFdTable";
import { DataProcessing } from "@/utils/dataProcessing";
import CCYRate from "@/components/CCYRate";
import { Common } from "@/utils/common";
export default {
	components: {
		CCYRate,
	},
	props: {
		model: {
			default: {
				list: [],
				initModel: {},
				field: [],
			},
			type: Object,
		},
	},
	watch: {
		"$store.state.currency": {
			handler(newValue) {
				this.baseModel.symbol = newValue.symbol;
			},
			immediate: true,
		},
	},
	data() {
		return {
			baseModel: new baseModel(),
			customModel: new customModel(),
		};
	},
	mounted() {},
	created() {
		this.init();
		setTimeout(() => {
			this.getC();
		}, 1000);
	},
	methods: {
		handleInput: Common.fd(function () {
			let key0 = this.model.field[0].key;
			if (this.customModel[key0] == "" || this.customModel[key0] == null) {
				this.customModel = new customModel();
				return;
			}
			this.customModel[key0] = this.customModel[key0].replace(/\D/g, "");
			this.$emit("input", this.customModel);
		}, 500),
		//不绑定第一行
		radioChangeTwo(item, index) {
			this.baseModel.radio = index;
			this.$emit("change", item, index);
		},
		//绑定第一行
		radioChange(item, index) {
			this.baseModel.radio = index;
			//	this.customModel = new customModel();
			this.customModel[this.model.field[0].key] = item.totalQuantity;
			this.customModel[this.model.field[1].key] = item.foundationUnitPrice;
			this.customModel[this.model.field[2].key] = item.totalPrice;
			this.$forceUpdate();
			this.$emit("change", item, index);
		},
		//获取总价
		getTotal(item) {
			return DataProcessing.floatRide(item[this.model.field[0].key] || 0, item[this.model.field[1].key] || 0) || "0.00";
		},
		init(e) {
			this.customModel = this.model.initModel;
			console.log("开摆", this.customModel, this.model);
			//	this.customModel = new customModel();
			//默认选中第一个
			if (!this.baseModel.initData) {
				this.baseModel.initData = true;
				this.setRowOne();
			}

			this.$forceUpdate();
		},
		//默认选中第一个
		setRowOne() {
			if (this.model?.list?.length > 0) {
				this.radioChange(this.model.list[0], 1); //只执行一次
				this.customModel = {
					...this.customModel,
					totalQuantity: undefined,
					foundationUnitPrice: 0,
					totalPrice: 0,
				};
			} else {
				setTimeout(() => {
					this.setRowOne();
				}, 50);
			}
		},
		getC() {
			if (this.model.list?.length > 0) {
				this.baseModel.discount = this.model.list[0].fdUnitPriceDiscountCode;
			} else {
				this.baseModel.discount = "C";
			}
			this.$forceUpdate();
		},
		//保留N位小数.  默认||保留2位小数
		getToFixed(key, num = 2) {
			let a = parseFloat(this.customModel[key]);
			if (a) {
				let r = 1;
				if (this.model?.currency?.rate) {
					r = this.model.currency.rate;
				}
				let s = DataProcessing.floatRide(a, r);
				console.log("价格", a, this.model);
				var result = parseFloat(s).toFixed(num);
				return result || "0.00";
			} else {
				return "0.00";
			}
		},
		getToFixed1(key) {
			let a = parseFloat(this.customModel[key]);
			if (a) {
			}
		},
	},
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
$borderColor: 1px solid #dfdfdf; //边框色
$bigBigHeight: 50px; //特大号高度
$bigHeight: 44px; //大号高度
$smallHeight: 32px; //小号高度

//垂直水平居中
@mixin centerCenter {
	display: flex;
	justify-content: center;
	align-items: center;
}
//背景色
@mixin bgColor {
	background: #f8f8f8;
}
//hot圆圈
@mixin hot {
	width: 36px;
	height: 16px;
	border-radius: 8px;
	background: -o-linear-gradient(right, #fd1e02, #fe621d);
	background: -moz-linear-gradient(right, #fd1e02, #fe621d);
	background: linear-gradient(to right, #fd1e02, #fe621d);
	color: #fff;
	font-size: 10px;
}

.lyFdTable {
	position: relative;
	z-index: 1;
	.table {
		width: 100%;
		border: $borderColor;
		.th,
		.tr {
			display: flex;
			cursor: pointer;
			font-size: 13px;
			.td {
				position: relative;
				flex: 1;
				@include centerCenter;
				@include bgColor;
			}
			.tdLeft {
				justify-content: flex-start;
				padding: 0 0 0 30px;
			}
		}
		.th {
			border-bottom: $borderColor;
			font-size: 14px;
			color: #333;
			font-weight: bold;
			.td {
				height: $bigHeight;
			}
		}
		.tr {
			.td {
				height: $smallHeight;
				.hot {
					position: absolute;
					right: 8px;
					top: calc(50% - 9px);
					@include hot;
					@include centerCenter;
				}
			}
		}
		.tr0 {
			.td {
				height: $bigBigHeight;
			}
			.tdInput {
				width: 75%;
				background: #ffffff;
				border: 1px solid #d7d7d7;
				border-radius: 4px;
				height: $smallHeight;
				padding: 0 10px;
				font-size: 14px;
			}
		}
		.trOn {
			.td {
				background: #d5e6f6 !important;
			}
		}
		.tr:hover {
			.td {
				background: #d5e6f6 !important;
				opacity: 0.8;
			}
		}
	}
}
@media screen and (max-width: 1000px) {
	.lyFdTable {
		.table {
			.th,
			.tr {
				.tdLeft {
					padding: 0 0 0 10px;
				}
				.tdRight {
					justify-content: flex-start;
					padding: 0 0 0 10px;
				}
			}

			.th .td {
				font-size: 13px;
			}
			.tr .td {
				font-size: 12px;
			}
			.tr0 .tdInput {
				height: 25px;
				font-size: 12px;
				width: 80%;
			}
		}
	}
}
</style>
<style rel="stylesheet/scss" lang="scss">
/**非业务样式**/
.lyFdTable {
	.table {
		.td {
			.el-radio__label {
				display: none;
			}
			.el-radio {
				margin-right: 5px;
				.el-radio__inner {
					width: 19px;
					height: 19px;
				}
			}
		}
	}
}
@media screen and (max-width: 1000px) {
	.lyFdTable {
		.table {
			.td {
				.el-radio {
					.el-radio__inner {
						width: 14px;
						height: 14px;
					}
				}
			}
		}
	}
}
</style>
