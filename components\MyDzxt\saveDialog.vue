<template>
	<div>
		<base-dialog :value="saveDialogDialog" @update="update" persistent :width="device !== 'mb' ? '510px' : '90%'">
			<div class="design" v-show="saveShow === 1">
				<b class="icon-icon-save"></b>
				<p>{{ lang.saveYourDesign }}</p>
				<el-input v-model="filename" :placeholder="lang.enterName" @input="handleInputChange"></el-input>
				<el-button type="primary" :disabled="disabled" @click="saveDesign">{{ lang.saveDesign }}</el-button>
				<div class="design-text">
					{{ lang.designText }} <a href="/info/privacy-policy" target="_blank">{{ lang.privacy }}</a
					>.
				</div>
			</div>
			<div class="design" v-show="saveShow === 2">
				<b class="icon-icon-save"></b>
				<p>{{ lang.saveChanges }} "{{ filename }}"</p>
				<div class="text">{{ lang.emailAddress }}</div>
				<div class="radio">
					<el-radio-group v-model="radio">
						<el-radio :label="0">{{ lang.update }}</el-radio>
						<el-radio :label="1">{{ lang.saveNew }}</el-radio>
					</el-radio-group>
				</div>
				<el-input style="margin-top: 10px" v-model="filename2" :placeholder="lang.enterName" v-show="radio === 1"></el-input>
				<el-button type="primary" @click="continueClick">{{ lang.continue }}</el-button>
			</div>
			<div class="design" v-show="saveShow === 3">
				<b class="icon-icon-save"></b>
				<p>{{ lang.continueSave }} "{{ filename }}"</p>
				<el-form :model="emailForm" ref="emailForm" @submit.native.prevent>
					<el-form-item
						prop="email"
						:rules="[
							{ required: true, message: '', trigger: 'blur' },
							{ type: 'email', message: '', trigger: ['blur'] },
						]"
					>
						<el-input v-model="emailForm.email" :placeholder="lang.enterEmailAddress"></el-input>
					</el-form-item>
				</el-form>
				<div class="emailText">{{ lang.yourEmail }}</div>
				<el-button type="primary" @click="saveDesignBefore('emailForm')">{{ lang.saveDesign }}</el-button>
			</div>
		</base-dialog>
	</div>
</template>

<script>
import { editUserTemplates, templateNameExist, editUserTemplatesUnLogin } from "@/api/newDzxt";
import BaseDialog from "~/components/Quote/BaseDialog.vue";
import dzMixin from "@/mixins/dzMixin";

export default {
	props: ["saveDialogDialog", "saveShow"],
	mixins: [dzMixin],
	data() {
		return {
			emailForm: {
                email: "",
			},
			filename: "",
			filename2: "",
			disabled: true,
			radio: 0,
			templatesId: null,
		};
	},
	components: {
		BaseDialog,
	},
	watch: {
		isLogin(val) {
			if (val) {
				//判断是否在等待登录
				if (this.waitLogin) {
					this.templateExist();
				}
			}
		},
	},
	computed: {
		isLogin() {
			return this.$store.getters.isLogin;
		},
		device() {
			return this.$store.state.device;
		},
		lang() {
			return this.$store.getters.lang.design;
		},
	},

	methods: {
		saveDesignBefore(formName) {
			this.$refs[formName].validate((valid) => {
				if (valid) {
                    this.saveTemplates()
				} else {
					console.log("error submit!!");
					return false;
				}
			});
		},

		//按钮状态
		handleInputChange(val) {
			this.disabled = !val;
		},

		//保存
		saveDesign() {
			if (this.isLogin) {
				this.templateExist();
			} else {
				this.$emit("update:saveShow", 3);
			}
		},

		//查找保存的模版名是否存在
		templateExist() {
			this.$gl.show();
			templateNameExist({
				name: this.filename,
			}).then((res) => {
                this.$gl.hide();
				if (res.data === null) {
					this.saveTemplates();
				} else {
					this.templatesId = res.data;
					this.$emit("update:saveShow", 2);
				}
			})
		},

		continueClick() {
			this.saveTemplates();
		},

		//上传、更新模版
		async saveTemplates() {
            this.$gl.show();
            setTimeout(() => {
                this.$gl.hide();
            }, 3000);
			let specification = null;
			if (this.canvas.isSticker) {
				if (!this.canvas.checkStickerSize()) {
					return false;
				}
				specification = JSON.stringify({
					width: this.canvas.stickerSize.w,
					height: this.canvas.stickerSize.h,
				});
			}
			let templateFile = this.canvas.getTemplateData();
			let templateUrl = await this.canvas.getTemplatePicPath();
			let params = {
				categoryId: this.$store.state.design?.pageInfo?.id,
				templateName: this.radio === 0 ? this.filename : this.filename2,
				templateFile: JSON.stringify(templateFile),
				templateUrl: templateUrl,
				specification,
                userEmail: this.emailForm.email
			};
			if (this.radio === 0) {
				if (this.templatesId) params["id"] = this.templatesId;
			}
			this.$emit("update:saveDialogDialog", false);
            if(this.isLogin){
                await editUserTemplates(params);
            }else{
                await editUserTemplatesUnLogin(params);
            }
			this.filename = "";
			this.filename2 = "";
			this.radio = 0;
			this.waitLogin = false;
			this.templatesId = null;
			this.$toast.success("success");
		},
		update(val) {
			this.$emit("update:saveDialogDialog", val);
		},
	},
};
</script>

<style scoped lang="scss">
@import "@/assets/css/dzxt_theme";

.design {
	text-align: center;
	padding: 30px;

	.close {
		text-align: right;
		padding: 0 10px 20px;

		b {
			font-weight: bold;
			font-size: 11px;
		}
	}

	b {
		font-size: 52px;
	}

	p {
		font-size: 18px;
		font-weight: bold;
		margin: 19px 0;
	}

	.pp {
		font-size: 18px;
		font-weight: bold;
		margin: 0;
	}

	.el-button {
		width: 180px;
		height: 40px;
        margin-top: 20px;
		font-size: 16px;
		font-weight: bold;
	}

	.text {
		margin-bottom: 30px;
	}

	.emailText {
		text-align: left;
	}

	img {
		object-fit: contain;
	}

	.design-text {
		padding: 0 50px;
		margin-top: 23px;

		a {
			color: #2996fb;
		}
	}

	.closeBtn {
		width: auto;
		margin-top: 15px;
	}
}

@include respond-to(mb) {
	.design-text {
		padding: 0 35px;
		margin-top: 23px;

		a {
			color: #2996fb;
		}
	}

	.design {
        padding: 10px;
		.text {
			margin: 0 20px 30px;
		}
	}
}
</style>
