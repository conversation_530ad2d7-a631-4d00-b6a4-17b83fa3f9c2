<!--
介绍: 快速弹窗报价模板
buildWeb配置项: modal pid cateId
-->
<template>
    <div class="quick-quote-dialog-wrap" noDebounce>
        <div class="close-icon extend-click-area" pointer @click="closeDialog">
            <b  pointer class="icon-guanbi"></b>
        </div>
        <newQuoteStep ref="quoteStep" :pid="modal.pid" :cate-id="modal.cateId" @loadComplete="loadComplete"></newQuoteStep>
    </div>
</template>

<script>
import newQuoteStep from "@/components/Quote/QuickQuote/NewQuoteStep.vue"

export default {
    name: "modalQuoteDialog",
    props: {
        data: {
            type: Object,
            default: {},
        },
    },
    data() {
        return {
            quickLoadStatus: false,
            modal: {
                ...this.data,
            }
        };
    },
    watch:{
        modal:{
            handler(val){
                if(val.paramId){
                    let time = setInterval(()=>{
                        if(this.quickLoadStatus){
                            clearInterval(time)
                            this.$refs.quoteStep.initQuote({
                                shapeId: val.paramId
                            })
                        }
                    },1000)
                }
            },
            deep: true,
            immediate: true
        }
    },
    components:{
        newQuoteStep
    },
    methods:{
        loadComplete(val){
              this.quickLoadStatus = true;
        },
        closeDialog(){
            this.$store.commit("setMask", false);
        }
    }
};
</script>

<style lang="scss" scoped>
.quick-quote-dialog-wrap {
    background: white;
    position: relative;
    border-radius: 0.5em;
    width: 680px;

    ::v-deep .quoteBox{
        overflow: auto;
        max-height: 96vh;
    }

    .close-icon{
        position: absolute;
        right: 10px;
        top: 10px;
        z-index: 1;
    }
}
</style>
