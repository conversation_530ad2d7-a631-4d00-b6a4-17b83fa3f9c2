
export class dictModel {
	// 材质
	sizeList = [
		{
			sizeName: '1/4 Inch',
			rowsLimit: 1,
			frontTextPositionX: 300,
			frontTextPositionY: 400,
			insideTextPositionX: 300,
			insideTextPositionY: 450,
			basefontSize: 60,
			innerHeight: 160
		},
		{
			sizeName: '1/2 Inch',
			rowsLimit: 1,
			frontTextPositionX: 300,
			frontTextPositionY: 400,
			insideTextPositionX: 300,
			insideTextPositionY: 450,
			basefontSize: 60,
			innerHeight: 160
		},
		{
			sizeName: '3/4 Inch',
			rowsLimit: 2,
			frontTextPositionX: 400,
			frontTextPositionY: 700,
			insideTextPositionX: 300,
			insideTextPositionY: 450,
			basefontSize: 60,
			innerHeight: 160
		},
		{
			sizeName: '1 Inch',
			rowsLimit: 2,
			frontTextPositionX: 200,
			frontTextPositionY: 400,
			insideTextPositionX: 300,
			insideTextPositionY: 450,
			basefontSize: 60,
			innerHeight: 160
		},
		{
			sizeName: '1.5 Inch',
			rowsLimit: 3,
			frontTextPositionX: 200,
			frontTextPositionY: 200,
			insideTextPositionX: 300,
			insideTextPositionY: 450,
			basefontSize: 60,
			innerHeight: 160
		},
		{
			sizeName: '2 Inch',
			rowsLimit: 4,
			frontTextPositionX: 200,
			frontTextPositionY: 200,
			insideTextPositionX: 300,
			insideTextPositionY: 450,
			basefontSize: 60,
			innerHeight: 160
		}
	]
	defCanvasFont = {
		frontTextGroup: [
			{
				id: Date.now(),
				text: "",
				family: "",
				weight: false,
				italic: false,
				color: "",
				colorShow: false,
			},
		],
		insideTextGroup: [
			{
				id: Date.now() + 1,
				text: "",
				family: "",
				weight: false,
				italic: false,
				color: "",
				colorShow: false,
			},
		],
		frontTextPositionX: 500,
		frontTextPositionY: 420,
		insideTextPositionX: 500,
		insideTextPositionY: 220
	}
}

//基础类
export class baseModel { }
