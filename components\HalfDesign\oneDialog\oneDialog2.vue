<template>
  <div class="oneDialog2" v-if="oneDialog">
    <div class="oneDialog2Box">
      <div class="dialogTextBox">
        <div class="oneDialogTitle">
          <span>{{ langSemiCustom.oneDialogTitle }}</span>
        </div>
        <div class="dialogText">
          <span class="oneText">{{ langSemiCustom.oneDialogText }}</span>
          <span class="oneText">{{ langSemiCustom.oneDialogText2 }}</span>
        </div>
      </div>
      <div class="dialogRadioBox">
        <v-radio-group v-model="radio">
          <div class="clickBtn" @click="handleConfirm">
            <v-radio :value="1">
              <template #label>
                <span class="radioText" :title="langSemiCustom.selectPenRadio" :class="{ active: radio == 1 }">
                  {{ langSemiCustom.selectPenRadio }}
                </span>
              </template>
            </v-radio>
          </div>
          <div class="clickBtn" @click="handleConfirm">
            <v-radio :value="2">
              <template #label>
                <span class="radioText" :title="langSemiCustom.selectPenRadio2 + productSku"
                  :class="{ active: radio == 2 }">
                  {{ langSemiCustom.selectPenRadio2 + productSku }}
                </span>
              </template>
            </v-radio>
          </div>
        </v-radio-group>
      </div>
    </div>
    <div @click="handleClose" class="closeIcon1">
      <!-- <i class="el-icon-close"></i> -->
      <b class="icon-guanbi"></b>
    </div>
  </div>
</template>

<script>

export default {
  name: 'oneDialog2',
  components: {},
  props: {
    oneDialog: {
      type: Boolean,
      default: false
    },
    productSku: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      radio: -1
    }
  },
  watch: {

  },
  computed: {
    langSemiCustom() {
      return this.$store.getters.lang?.semiCustom;
    },
    isMobile() {
      return this.$store.getters.isMobile;
    },
    dialogWidth() {
      let width = this.isMobile ? '90%' : '450px'
      return width
    }
  },
  methods: {
    handleClose() {
      this.$emit('update:oneDialog', false)
    },
    handleConfirm() {
      this.$emit('update:oneDialog', false);
      this.$emit('oneSelect', this.radio);
    }
  },
  created() { },
  mounted() { },
}
</script>

<style scoped lang="scss">
.oneDialog2 {
  width: 100%;
  height: 90px;
  z-index: 1000;
  background: $color-primary;
  padding: 0 100px;
  overflow: hidden;

  .oneDialog2Box {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .dialogTextBox {
      width: 40%;
      flex-shrink: 0;
      font-family: Arial, Arial;
      color: #FFFFFF;

      .oneDialogTitle {
        font-weight: bold;
        font-size: 20px;
        margin-bottom: 10px;
      }

      .dialogText {
        font-weight: 400;
        font-size: 14px;
      }
    }

    .dialogRadioBox {
      width: 40%;
      flex-shrink: 0;

      ::v-deep .v-input--radio-group__input {
        display: flex;
        justify-content: center;
        align-items: center;
        column-gap: 20px;
        flex-direction: row;
      }

      ::v-deep .v-input--selection-controls__input {
        margin-right: 0;

        .v-icon {
          font-size: 20px;
        }
      }

      .clickBtn {
        width: 50%;
        font-family: Arial, Arial;
        font-weight: 400;
        font-size: 14px;
        color: #333;
        background: #fff;
        background-color: rgb(255, 255, 255);
        border: 1px solid #dcdfe6;
        border-radius: 4px;
        box-sizing: border-box;
        cursor: pointer;
        display: inline-block;
        font-weight: 500;
        line-height: 1;
        margin: 0;
        margin-left: 0px;
        outline: 0;
        padding: 12px 20px;
        text-align: center;
        transition: .1s;
        white-space: nowrap;
        max-width: 250px;

        ::v-deep .v-label {
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          max-width: 178px;
          display: inline-block;
        }

        & .radioText {
          &.active {
            color: $color-primary;
          }

        }
      }
    }

  }

  .closeIcon1 {
    padding: 5px;
    position: absolute;
    top: 4px;
    right: 14px;
    cursor: pointer;
    

    b {
      color: #fff;
    }

    b::before {
      font-size: 14px;
    }
  }


  @media screen and (max-width: $mb-width) {
    height: 204px;
    padding: 10px;

    .oneDialog2Box {
      flex-direction: column;
      justify-content: center;

      .dialogTextBox {
        width: 100%;

        .oneDialogTitle {
          font-size: 14px;
        }

        .dialogText {
          font-size: 12px;
        }
      }

      .dialogRadioBox {
        width: 100%;
        overflow: hidden;

        ::v-deep .v-input--radio-group__input {
          display: flex;
          justify-content: center;
          align-items: center;
          column-gap: 8px;
          flex-direction: row;
        }

        ::v-deep .v-input--selection-controls__input {
          margin-right: 0;

          .v-icon {
            font-size: 14px;
          }
        }

        .clickBtn {
          padding: 6px 2px;

          .radioText {
            font-size: 12px;
          }

          ::v-deep .v-label {
            max-width: none;
          }
        }
      }
    }
  }
}
</style>
