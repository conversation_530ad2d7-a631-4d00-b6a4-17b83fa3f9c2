import { request } from '~/utils/request'

//获取3d建模列表
export function getThreeDList(data) {
	return request({
		url: '/retailer/modeling/order/list',
		method: 'post',
		data,
	})
}

//获取已有订单的业务员列表
export function getSalesmanList(data) {
	return request({
		url: '/retailer/modeling/salesman/list',
		method: 'get',
		params:data,
	})
}

//获取次数
export function getTimes(data) {
	return request({
		url: '/retailer/modeling/times',
		method: 'get',
		params:data,
	})
}

//获取订单详情
export function getDetail(data) {
	return request({
		url: '/retailer/modeling/order/detail',
		method: 'get',
		params:data,
	})
}

//获取额外服务
export function getExtraServiceInfo(data) {
	return request({
		url: '/retailer/webSiteServe/get/getExtraServiceInfo',
		method: 'get',
		params:data,
	})
}




