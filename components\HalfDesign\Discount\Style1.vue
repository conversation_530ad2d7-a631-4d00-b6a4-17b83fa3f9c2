<template>
  <div class="mb-4" :class="stepData.styleName">
    <slot name="stepText"></slot>
    <div v-if="this.productInfo.isDevise == 1" class="step-content quantityBox">
      <div class="step-text grey--text">
        {{ langSemiCustom.quantity }}
      </div>
      <div class="sizeNum">
        <div class="inputBox">
          <button class="inputBtn" @click="subNum">—</button>
          <input
            class="priceInput"
            :placeholder="getPlaceholder()"
            type="text"
            v-model="inputNum"
            @keyup="formatNum"
            @change="updatePrice"
          />
          <button class="inputBtn" @click="addNum">+</button>
        </div>
      </div>
    </div>
    <div class="errorTip2" style="display: none; margin: 10px 0">
      <v-alert dense outlined type="error">
        {{ langSemiCustom.miniQty }}
        {{ productInfo.lowestPurchaseQuantity || 1 }}
      </v-alert>
    </div>
    <div class="hoursText">
      {{ lang.hourstext }}
    </div>
    <div class="step-content">
      <div class="step-text grey--text">
        {{ stepData.attributeTitle }}
      </div>

      <div class="step-wrap">
        <div
          class="step-item"
          :class="{
            active: index === selectIndex,
            onePens: noDisdountSelect && step.unitPercent != 0,
          }"
          v-for="(step, index) in filterStepItem"
          :key="index"
          @click="selectStep(step, index)"
        >
          <half-design-check-box class="mr-3 mt-1"></half-design-check-box>
          <div class="name">
            <div class="text-truncate">
              {{ step.valueName }}
              <span v-if="step.priceType == 6 && step.weightPrice">
                <span>- {{ langSemiCustom.add }}</span
                ><CCYRate :price="step.weightPrice"></CCYRate>
              </span>
            </div>
            <div class="tip">
              {{ step.remark }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="errorTip">
      <v-alert dense outlined type="error">
        {{ langSemiCustom.errorTip }}
      </v-alert>
    </div>
  </div>
</template>
<script>
import { halfDetailDiscount } from "@/mixins/halfDetailDiscount";
import { halfCalculate } from "@/api/web";
export default {
  mixins: [halfDetailDiscount],
  inject: ["getProductInfo", "getNeedJudgeSmallWeight"],
  props: {
    stepData: {
      type: Object,
      default: {},
    },
  },
  data() {
    return {
      selectIndex: -1,
      selectItem: null,
      inputNum: "",
      stock: 9999,
      teaSize: {},
    };
  },
  watch: {},
  computed: {
    langSemiCustom() {
      return this.$store.getters.lang?.semiCustom;
    },
    productInfo() {
      return this.getProductInfo();
    },
    lang() {
      return this.$store.getters.lang.quote || {};
    },
  },
  methods: {
    selectStep(item, index) {
      this.selectIndex = index;
      this.selectItem = item;
      this.$emit("selectStep", {
        type: this.stepData.attributeFlag,
        data: item,
        parent: this.stepData,
        id: this.stepData.id,
      });
    },
    formatNum() {
      // if (!step.inputNum) {
      // 	this.isInput = false;
      // 	return undefined;
      // }
      this.inputNum = (this.inputNum + "").replace(/[^\d]/g, "");
      if (this.stock && this.stock > 0 && this.inputNum > this.stock) {
        this.inputNum = String(this.stock);
      }
      if (this.stock <= 0) {
        this.inputNum = "";
      }
      if (this.inputNum > 0) {
        // this.updatePrice();
        setTimeout(() => {
          this.updatePrice();
        }, 310);
      }
    },
    getPlaceholder() {
      if (this.stepData.styleName === "style1") {
        if (this.stock > 0) {
          return `${this.stock} in stock`;
        }
        if (this.stock == -1) {
          return;
        } else {
          return "Sold Out";
        }
      }
    },
    updatePrice() {
      let priceInputs = document.getElementsByClassName("priceInput");
      // let sum = Array.from(priceInputs).reduce((acc, input) => acc + parseInt(input.value || 0), 0);
      let sum = priceInputs[0].value;
      if (sum < this.productInfo.lowestPurchaseQuantity) {
        let errDom = document
          .getElementById(this.stepData.id)
          .getElementsByClassName("errorTip2")[0];
        errDom.style.display = "block";
      } else {
        let errDom = document
          .getElementById(this.stepData.id)
          .getElementsByClassName("errorTip2")[0];
        let errDom2 = document
          .getElementById(this.stepData.id)
          .getElementsByClassName("errorTip")[0];
        errDom.style.display = "none";
        if (this.selectIndex > 0) errDom2.style.display = "none";
      }
      if (this.inputNum <= 0) return;
      let teaData = {
        quantity: this.inputNum,
        paramType: "size",
        paramId: this.teaSize.id,
      };
      teaData = teaData.paramId ? teaData : null;
      this.$emit("updatePrice", teaData);
    },
    subNum() {
      if (this.inputNum <= 0 || !this.inputNum) {
        this.inputNum = 0;
        return;
      }
      this.inputNum--;
      this.$nextTick(() => {
        this.updatePrice();
      });
    },
    addNum() {
      if (!this.inputNum) this.inputNum = 0;
      if (this.inputNum >= this.stock) return;
      this.inputNum++;
      this.$nextTick(() => {
        this.updatePrice();
      });
    },
    uploadQty(item) {
      this.teaSize = item;
      this.stock = item.stock;
      this.formatNum();
    },
    selectDefault() {
      if (this.filterStepItem.length) {
        this.selectStep(this.filterStepItem[0], 0);
      }
    },
    async getPriceFn(data) {
      let { data: result } = await halfCalculate(data);
      return result.deliveryFee;
    },
  },
  mounted() {
    this.$Bus.$on("uploadQty", this.uploadQty);
    this.$Bus.$on("selectDefaultDiscountStep", this.selectDefault);
  },
  beforeDestroy() {
    this.$Bus.$off("uploadQty", this.uploadQty);
    this.$Bus.$off("selectDefaultDiscountStep", this.selectDefault);
  },
};
</script>
<style scoped lang="scss">
@import "~assets/css/half.scss";

.hoursText {
  color: rgb(135 132 132);
  font-size: 16px;
  margin-bottom: 15px;

  @media screen and (max-width: 767px) {
    font-size: 14px;
  }
}

.quantityBox {
  display: none !important;
}

.style1 .step-content {
  display: grid;
  grid-template-columns: 100px 1fr;
  grid-gap: 10px;
  margin-bottom: 10px;

  .sizeNum {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-gap: 10px;

    @include respond-to(mb) {
      grid-template-columns: repeat(2, 1fr);
    }

    .inputBox {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 50px;
      overflow: hidden;
      border-radius: 6px;
      border: 2px solid #e2e2e2;

      .inputBtn {
        width: 14%;
        font-size: 24px;
        transform: scale(1.1);
        font-weight: 300;
      }

      input {
        height: 60%;
        width: 60%;
        line-height: 80%;
        flex-shrink: 0;
        margin: 0 3%;
        border: 1px solid #e2e2e2;
        border-radius: 6px;
        padding: 4px;
        font-size: 14px;
      }

      input::placeholder {
        font-size: 14px;
      }
    }
  }

  .step-wrap {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-gap: 10px;

    .step-item {
      min-width: 0;
      @include step-default;
      cursor: pointer;
      display: flex;

      &.onePens {
        pointer-events: none;
        background: #f5f5f5;
      }

      .tip {
        font-size: 14px;
      }

      @media (any-hover: hover) {
        &:hover {
          border-color: $color-primary;
          background-color: $color-primary;
          color: #ffffff;

          ::v-deep .check-box {
            border-color: #ffffff;

            .check-box-inner {
              background-color: #ffffff;
            }
          }
        }
      }

      .name {
        min-width: 0;
        word-break: break-word;

        & > div:first-child {
          white-space: normal !important;
          margin-bottom: 10px;
          line-height: normal;
        }
      }
    }

    .step-item.active {
      border-color: $color-primary;
      background-color: $color-primary;
      color: #ffffff;

      ::v-deep .check-box {
        border-color: #ffffff;

        .check-box-inner {
          background-color: #ffffff;
        }
      }
    }
  }

  @include respond-to(mb) {
    grid-template-columns: 1fr;
    grid-gap: 5px;

    .step-wrap {
      display: grid;
      grid-template-columns: repeat(1, 1fr);
      grid-gap: 5px;

      .step-item {
        .name > div:first-child {
          white-space: normal !important;
          margin-bottom: 5px;
          font-size: 14px;
        }

        .tip {
          font-size: 12px;
        }
      }
    }
  }
}
</style>
