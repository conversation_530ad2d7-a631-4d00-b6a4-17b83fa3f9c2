<template>
	<div class="Pimg" :style="{ 'padding-bottom': paddingBottom }">
		<img loading="lazy" :src="url" :data-src="data - src" :alt="alt" />
	</div>
</template>

<script>
export default {
	name: "Pimg",
	props: ["src", "data-src", "alt"],
	data() {
		return {
			paddingBottom: 0,
		};
	},
	computed: {},
	watch: {
		src: {
			handler(newValue) {
				console.log(newValue);
			},
		},
	},
};
</script>

<style scoped lang="scss">
.Pimg {
	position: relative;

	> img {
		position: absolute;
		left: 0;
		top: 0;
		width: 100%;
		height: 100%;
	}
}
</style>