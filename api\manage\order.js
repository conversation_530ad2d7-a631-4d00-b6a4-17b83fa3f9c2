import {request} from '~/utils/request'

//获取订单列表
export function getOrderList(data) {
	return request({
		url: '/retailer/order/getOrderList',
		method: 'post',
		data,
	})
}

//获取订单详情
export function getOrderInfo(data) {
	return request({
		url: '/retailer/order/getOrderInfo',
		method: 'post',
		data,
	})
}

//扭转订单状态
export function orderStatusFlow(data) {
	return request({
		url: '/retailer/order/orderStatusFlow',
		method: 'post',
		data,
	})
}

//获取各状态数量
export function getOrderQtyByStatus(data) {
	return request({
		url: '/retailer/order/getOrderQtyByStatus',
		method: 'get',
		params:data,
	})
}

//删除订单
export function orderDelete(data) {
	return request({
		url: '/retailer/order/orderDelete',
		method: 'post',
		data:data,
	})
}

//修改订单折扣
export function modifyOrderDiscount(data) {
	return request({
		url: '/retailer/order/modifyOrderDiscount',
		method: 'post',
		data:data,
	})
}

//修改订单备注
export function editOrderRemark(data) {
	return request({
		url: '/retailer/order/editOrderRemark',
		method: 'post',
		data:data,
	})
}

//获取订单消息列表
export function getMessageListByOrderId(data) {
	return request({
		url: '/retailer/order/getMessageListByOrderId',
		method: 'post',
		data:data,
	})
}

//扭转支付
export function paymentStatusFlow(data) {
	return request({
		url: '/retailer/order/paymentStatusFlow',
		method: 'post',
		data:data,
	})
}

//修改订单地址
export function retailerEditOrderAddress(data) {
	return request({
		url: '/retailer/order/retailerEditOrderAddress',
		method: 'post',
		data:data,
	})
}

// 获取发票打印信息
export function getInvoicePrintInfo(data) {
	return request({
		url: '/retailer/order/getPrintInfoByOrdersId',
		method: 'get',
		params:data
	})
}

//获取快递方式
export function getShippingList(data) {
	return request({
		url: '/shipping/list',
		method: 'get',
		params:data,
	})
}

// 编辑订单业务员
export function editOrderSalesman(data) {
	return request({
		url: '/retailer/order/editOrderSalesman',
		method: 'post',
		data:data
	})
}

// 上传图稿
export function uploadToDraft(data) {
	return request({
		url: '/order/manage/uploadTheDrawings',
		method: 'post',
		data: data,
	})
}


export function getAllQuoteCate(data) {
	return request({
		url: '/quote/cate/getAllQuoteCateAndHalfProductCate',
		method: 'post',
		data:data,
	})
}


//删除子订单
export function subOrderDelete(data) {
	return request({
		url: '/order/manage/subOrderDelete',
		method: 'post',
		data:data,
	})
}


//编辑订单业务员
export function editOrderManageSalesman(data) {
	return request({
		url: '/order/manage/editOrderManageSalesman',
		method: 'post',
		data:data,
	})
}

//获取getOfficeAddress
export function getOfficeAddress(data) {
	return request({
		url: '/app/systemProject/getOfficeAddress',
		method: 'post',
		params:data,
	})
}

