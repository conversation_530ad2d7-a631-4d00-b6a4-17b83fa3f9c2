<template>
  <section class="ButtonGroup"
    style="animation:none">
    <slot></slot>
    <div class="button-group">
      <NeonButton
        v-show="onlyInquiry"
        :class="{isUpload:tabName=='Upload',showDetails:showDetails}"
        bg="linear-gradient(90deg, #48A2FF 0%, #ED12FB 99%)"
        :tips="lang.neon.inquiryBtnTips"
        @click="submitInquiry">
        <span class="btn2">{{ lang.neon.helpQuoteBtn }}</span>
      </NeonButton>
      <NeonButton style="animation:none"
        v-show="!onlyInquiry"
        @click="addToCart"
        :tips="lang.neon.addCardBtnTips"
        bg="linear-gradient(90deg, #06C0C1 0%, #23AADC 100%)">
        <span>{{ this.$route.query.isBack ? lang.saveCart:lang.neon.addCartBtn }}</span>
      </NeonButton>
    </div>
  </section>
</template>
<script>
import NeonButton from "@/components/Neon/NeonButton.vue";

export default {
	components: {NeonButton},
  props: {
    tabName: {
      type: String,
    },
    showDetails: {
      type: Boolean,
    },
    onlyInquiry: {
      type: Boolean,
      default: false
    },
  },

  data() {
    return {};
  },
  methods: {
    submitInquiry() {
      this.$emit("submitInquiry");
    },
    addToCart() {
      this.$emit("addToCart");
    },
  },
  computed: {
    lang() {
      return this.$store.getters.lang.quote || {};
    },
  },
  watch: {},
  mounted() {},
};
</script>
<style lang="scss" scoped>
.ButtonGroup{
  width: 100%;
}
.button-group {
  bottom: 0;
  display: grid;
  gap: 20px;
  padding: 22px 30px;
  right: 0;
  position: relative;
  z-index: 2;
  border-image: none;
  clip-path: none;
  border-radius: 0;
  border: none;
  width: 100%;
  max-height: 83px;
  background: linear-gradient(90deg, #04103a 0%, #380f69 44%, #990b86 100%);
  // grid-template-columns: repeat(var(--column), 1fr);
  @media screen and(max-width: 1273px) {
    padding: 10px;
  }
  @media screen and(max-width: 768px) {
    padding: 10px 5px;
    gap: 10px;
  }

}
</style>
