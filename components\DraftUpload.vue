<template>
	<div class="content">
		<div class="top-tips" v-if="(showTips && listType !== 2) || (showTips && listType == 2 && dataList.length == 0)">
			<div class="close" @click="clickCloseTips">×</div>
			{{ langCart.uploadTips[0] }} <span @click="clickUploadDraft">{{ langCart.uploadTips[1] }}</span>
			{{ langCart.uploadTips[2] }}
			<a :href="`mailto:${siteEmail}`">{{ siteEmail }}</a>
			{{ langCart.uploadTips[3] }}
		</div>
		<div class="swiper__content">
			<div class="icon preIcon" v-show="productImgList.length > 1 && listType !== 2">
				<b class="icon-jxsht-jcsz-jr" @click="toPrevEle(showIndex)"></b>
			</div>
			<div class="icon nextIcon" v-show="productImgList.length > 1 && listType !== 2">
				<b class="icon-jxsht-jcsz-jr" @click="toNextEle(showIndex)"></b>
			</div>
			<div class="showImg" v-if="productImgList.length > 0">
				<b v-if="(!propsData.isSemiProduct || propsData.isSemiProduct) && productImgList[showIndex].isDel == 1 && listType !== 2 && !productImgList[showIndex].isToUpload" class="icon-a-icon-dzlj-delzhuanhuan" @click="deleteObj(showIndex)"></b>
				<!-- 放大镜 -->
				<img src="https://sticker-static.oss-accelerate.aliyuncs.com/image/uploads/20220915/big.png" class="loupe" @click="zoomPic(productImgList[showIndex].url)"/>

				<div class="uploadImgMb" v-if="productImgList[showIndex].isToUpload">
					<b class="icon-a-icon-dzlj-delzhuanhuan"></b>
					{{ langCart.upload }}
					<input type="file" :accept="acceptFileType" ref="picUpload" @change="uploadDraft" multiple />
				</div>

				<CustomImage v-else :url="productImgList[showIndex].url" :show-file-name="true" :preview="true"></CustomImage>
			</div>

			<div class="uploadImg" v-else>
				<b class="icon-a-icon-dzlj-delzhuanhuan"></b>
				{{ langCart.upload }}
				<input type="file" :accept="acceptFileType" :ref="`picUpload${uploadId}`" @change="uploadDraft" multiple />
			</div>
		</div>
		<div v-if="((!propsData.isSemiProduct && productImgList.length > 0 && listType !== 2) || (propsData.isSemiProduct && productImgList.length > 0 && !propsData.isBlank && listType !== 2))" class="drawings__list d-flex mt-1">
			<div class="icon preIcon" v-show="productImgList.length > 1">
				<b class="icon-jxsht-jcsz-jr" @click="toPrevEle(showIndex)"></b>
			</div>
			<div class="drawing-box" :class="{ widthLess: productImgList.length < 2 }">
				<div class="list-item d-flex align-center" :style="{ left: productImgList.length < 3 ? 0 : scrollX + 'px' }">
					<div v-for="(item, idx) in productImgList" :key="idx" :class="{ active: showIndex == idx }">
						<!--						item..isSemiLogo logo不展示-->
						<div class="imgWrap" :ref="'imgItem_' + idx" @click="changeShowDraft(item, idx)">
							<CustomImage :url="item.url" :show-file-name="false" :preview="false" :controls="false"></CustomImage>
						</div>
					</div>
				</div>
			</div>
			<div class="upload-item d-flex align-center">
				<b class="icon-a-icon-dzlj-delzhuanhuan"></b>
				<div>{{ langCart.upload }}</div>
				<input type="file" :accept="acceptFileType" :ref="`picUpload${uploadId}`" @change="uploadDraft" multiple />
			</div>
			<div class="icon nextIcon" v-show="productImgList.length > 1">
				<b class="icon-jxsht-jcsz-jr" @click="toNextEle(showIndex)"></b>
			</div>
		</div>
	</div>
</template>

<script>
import { isImageType, getFileSuffix } from "@/utils/utils";
import { editCartImageJson } from "~/api/web";

import { uploadFile } from "@/utils/oss";
import CustomImage from "@/components/CustomImage";
import { checkFile, acceptFileType } from "@/utils/validate";

export default {
	name: "DraftUpload",
	components: { CustomImage },
	props: {
		propsData: {
			default: "",
		},
		dataList: {
			default: [],
		},
		// 列表类型  极简模式（2）不展示其他小图。
		listType: {
			default: -1,
		},
		index: {
			default: "",
		},
		uploadId: {
			type: [String, Number],
			default: "",
		}
	},
	computed: {
		productImgList() {
			let list = this.dataList.filter((ele) => {
				return ele.isSemiLogo === 0;
			});
			if (this.mbType) {
				list.push({
					isToUpload: true,
				});
			}
			return list;
		},
		mbType() {
			return this.$store.state.device === "mb";
		},
		langCart() {
			return this.$store.getters.lang?.cart;
		},
		siteEmail() {
			return this.$store.state.proSystem.email;
		},
		hasCartDraft() {
			// 是否存在购物车上传
			let obj = this.dataList.some((item) => {
				return item.isDel == 1;
			});
			return obj;
		},
		closeList() {
			return JSON.parse(localStorage.getItem("closeTips")) || [];
		},
	},
	watch: {
		mbType(val) {
			console.log(val);
		},
		screenWidth(val) {},
	},
	data() {
		const hasDel = this.dataList.some((item) => {
			return item.isDel == 1;
		});
		return {
			acceptFileType,
			showDraft: {
				url: this.dataList[0]?.url,
				index: this.dataList[0] ? 0 : "",
			},
			showIndex: 0,
			scrollX: 0,
			// 全定制(!this.propsData.isSemiProduct)提示文案是否显示：imageJson文件数量 为0显示 不为0不显示
			// 半定制(this.propsData.isSemiProduct)提示文案是否显示：isBlank=0 并且 isEmailLater =1
			showTips: ((!this.propsData.isSemiProduct && this.dataList.length == 0) || (this.propsData.isSemiProduct && this.propsData.isBlank == 0 && this.propsData.isEmailLater == 1)) && !this.propsData.closeTips && !hasDel ? true : false,
		};
	},
	methods: {
		zoomPic(img) {
			if(!this.preview){
				return
			}
			if (isImageType(img)) {
				this.$viewerApi({
					images: [img],
				});
			} else {
				window.open(img);
			}
		},

		getFileName(url) {
			// https://static-oss.gs-souvenir.com/web/public/picStore/20230826/PDF_20230826XaBMrT.png
			let lastIndex = url.lastIndexOf("/");
			url = url.substring(lastIndex + 1, url.length); // PDF_20230826XaBMrT.png
			let s = url.lastIndexOf("_");
			let e = url.lastIndexOf(".");
			let n = url.substring(s, e); // _20230826XaBMrT
			let name = url.replace(n, "");
			return name;
		},
		getFileTypeIsPic(url) {
			/*	let lastIndex = url.lastIndexOf('.')
                let type = url.substring(lastIndex+1,url.length)*/
			let type = getFileSuffix(url).toLowerCase();
			if (type === "jpg" || type === "jpeg" || type === "png" || type === ".gif") {
				return true;
			} else {
				return false;
			}
		},
		// 获取特殊格式封面图
		// getImgUrl(url) {
		// 	console.log(111,url);
		// 	if(!url){ return }
		// 	let type = getFileSuffix(url);
		// 	let pic = this.fileCover.find(item => {
		// 		return item.type === type.toLowerCase();
		// 	});
		// 	if(pic) return pic.url
		// 	return '';
		// },
		clickCloseTips() {
			console.log(this.propsData.id, "ttgfhsdfh");
			this.showTips = false;
			this.closeList.push(this.propsData.id);
			localStorage.setItem("closeTips", JSON.stringify(this.closeList));
		},
		clickUploadDraft() {
			console.log(123321, this.$refs, this.$refs.picUpload);
			this.$refs["picUpload" + this.uploadId].click();
		},
		replayUpload() {
			this.clickUploadDraft();
			this.$store.commit("setSizeDialog", false);
		},
		// 上传大项Item
		async uploadDraft(event, type = "upload") {
			this.$gl.show();
			let files = type === "upload" ? event.target.files : event;
			let fileResult = checkFile(Array.from(files), this.acceptFileType, 80);
			if (!fileResult) {
				this.$toast.error("File type error");
				this.$gl.hide();
				return false;
			}
			if (fileResult.nomalSize.length == 0) {
				this.$gl.hide();
				this.$store.commit("setSizeDialog", true);
				this.$store.commit("setInputRefName", `cartReplayUpload${this.uploadId}`);
				this.$store.commit("setOverSizeList", fileResult.overSize);
				this.$refs["picUpload" + this.uploadId].value = null;
				return false;
			}
			for (let i = 0; i < fileResult.nomalSize.length; i++) {
				let pic = await uploadFile(fileResult.nomalSize[i]);
				let arr = {
					isDel: 1,
					url: pic,
					isSemiLogo: 0,
				};
				this.productImgList.push(arr);
				let data = {
					isSemiLogo: 0,
					isDelete: 0,
					cartId: this.propsData.id,
					proId: this.propsData.proId,
					imageJson: JSON.stringify(this.productImgList),
				};
				editCartImageJson(data).then((res) => {
					this.$gl.hide();
					this.showTips = false;
					this.$toast.success(res.message);
					this.$emit("updateCartList", this.productImgList);
				});
				arr = {};
				this.$forceUpdate();
			}
			this.$refs["picUpload" + this.uploadId].value = null;
			if (fileResult.overSize.length > 0) {
				this.$store.commit("setSizeDialog", true);
				this.$store.commit("setInputRefName", `cartReplayUpload${this.uploadId}`);
				this.$store.commit("setOverSizeList", fileResult.overSize);
			}
		},

		deleteObj(showIndex) {
			let list = JSON.parse(JSON.stringify(this.productImgList));
			list.splice(showIndex, 1);
			// console.log(obj, this.showTips, 1111111, this.productImgList);
			let data = {
				isDelete: 1,
				cartId: this.propsData.id,
				proId: this.propsData.proId,
				imageJson: JSON.stringify(list),
			};
			editCartImageJson(data).then((res) => {
				this.productImgList.splice(showIndex, 1);
				this.showIndex = 0;
				this.scrollX = 0;
				let obj = this.productImgList.find((item) => {
					return item.isDel === 1;
				});
				if (!obj) {
					this.showTips = true;
				}
				this.$forceUpdate();
				this.$toast.success(res.message);
				this.$emit("updateCartList", this.productImgList);
			});
		},

		zoomPic(img) {
			// img = replacePicPrefix(img)
			if (isImageType(img)) {
				this.$viewerApi({
					images: [img],
				});
			} else {
				window.open(img);
			}
		},
		// 切换展示大图
		changeShowDraft(item, idx) {
			this.showIndex = idx;
		},

		//防抖
		debounce(fn) {
			let that = this;
			if (typeof fn != "function") {
				return;
			}
			return function () {
				if (that.timed) {
					clearTimeout(that.timed);
				}
				that.timed = setTimeout(() => {
					fn();
				}, 1000);
			};
		},

		toPrevEle(showIndex) {
			if (showIndex === 0) {
				this.showIndex = this.productImgList.length - 1;
			} else {
				this.showIndex -= 1;
			}
			if (this.productImgList.length > 2) {
				this.scrollX = -58 * this.showIndex;
			} else {
				this.scrollX = 0;
			}
			if (this.showIndex == this.productImgList.length - 1) {
				this.scrollX = -58 * (this.productImgList.length - 2);
			}
		},

		toNextEle(showIndex) {
			if (showIndex === this.productImgList.length - 1) {
				this.showIndex = 0;
			} else {
				this.showIndex += 1;

				console.log(11, showIndex);
			}

			if (this.productImgList.length > 2) {
				this.scrollX = -59 * this.showIndex;
			} else {
				this.scrollX = 0;
			}
			if (this.showIndex == this.productImgList.length - 1) {
				this.scrollX = 0;
			}
			if (this.showIndex == this.productImgList.length - 1) {
				this.scrollX = -59 * (this.productImgList.length - 2);
			}
			console.log(22, this.scrollX);
		},
	},
	mounted() {
		this.$Bus.$on(`cartReplayUpload${this.uploadId}`, this.replayUpload);
	},
	beforeDestroy() {
		this.$Bus.$off(`cartReplayUpload${this.uploadId}`);
	},
};
</script>

<style scoped lang="scss">
.content {
	position: relative;

	.top-tips {
		position: absolute;
		top: -58px;
		left: 0;
		width: 410px;
		//height: 54px;
		background: #fff6f6;
		border: 1px solid #ff4242;
		border-radius: 4px;
		padding: 10px 5px;
		font-size: 12px;
		color: #333333;
		text-align: left;
		z-index: 4;

		> span,
		a {
			color: #0066cc;
			cursor: pointer;
			text-decoration: underline;
		}

		&::after {
			content: "";
			position: absolute;
			bottom: -19px; /* 箭头的位置 */
			left: 18%; /* 箭头的位置 */
			border: 10px solid transparent; /* 边框 */
			border-top-color: #fff6f6; /* 上边框的颜色 */
		}

		&::before {
			content: "";
			left: 18%;
			position: absolute;
			bottom: -20px;
			border: 10px transparent solid;
			border-top-color: #ff4242;
		}

		.close {
			position: absolute;
			right: 2px;
			top: -6%;
			color: #333638;
			font-size: 18px;
			cursor: pointer;
		}
	}

	input[type="file"] {
		position: absolute;
		left: 0;
		top: 0;
		right: 0;
		bottom: 0;
		opacity: 0;
		width: 100%;
		height: 100%;
		cursor: pointer;
	}

	.uploadImg {
		position: relative;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		color: #0066cc;
		cursor: pointer;
		font-size: 14px;
		width: 100%;
		height: 100%;

		> b {
			font-size: 17px;
			transform: rotate(45deg);
		}
	}

	.swiper__content {
		position: relative;

		display: flex;
		align-items: center;
		justify-content: center;
		margin: 10px 0;
		width: 170px;
		height: 130px;
		border: 1px solid #e6e6e6;
		border-radius: 4px;
		//overflow: hidden;

		.icon {
			display: none;
		}

		.showImg {
			height: 100%;

			.loupe{
				width: 15px;
				height: 15px;
				position: absolute;
				left: 6px;
				top: 6px;
				filter: invert(60%);
			}

			.uploadImgMb {
				display: none;
			}

			> b {
				position: absolute;
				right: 5px;
				top: 5px;
				color: #999999;
				cursor: pointer;
				font-size: 16px;
			}

			> div .img {
				max-height: 130px;
				width: 100%;
				object-fit: contain;
				cursor: pointer;
			}

			.fileName {
				position: absolute;
				top: 80%;
				left: 50%;
				transform: translateX(-50%);
				font-size: 14px;
				color: #333333;
				max-width: 150px;
				text-overflow: ellipsis;
				overflow: hidden;
				white-space: pre;
			}
		}
	}

	.drawings__list {
		width: 170px;
		display: flex;
		align-items: center;
		//justify-content: space-between;
		position: relative;

		.icon {
			position: absolute;
			width: 19px;
			height: 19px;
			background: #ffffff;
			border-radius: 50%;
			box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.2);
			z-index: 1;
			display: flex;
			justify-content: center;
			align-items: center;

			> b {
				font-size: 12px;
				//position: absolute;
				cursor: pointer;
				color: #666666;
			}
		}

		.preIcon {
			left: -10px;

			b {
				transform: rotate(180deg);
			}
		}

		.nextIcon {
			right: -10px;
		}

		.drawing-box {
			display: flex;
			align-items: center;
			position: relative;
			height: 40px;
			width: 118px;
			overflow: hidden;
			z-index: 0;
			overflow: hidden;

			&.widthLess {
				width: 58px;
			}

			.list-item {
				position: absolute;
				left: 0;

				> div {
					margin-right: 7px;
					width: 52px;
					height: 39px;
					min-width: 52px;
					background: #ffffff;
					border: 1px solid #e6e6e6;
					border-radius: 4px;

					&.active {
						border: 1px solid #0066cc;
					}

					&:hover {
						border-color: #0066cc;
					}

					.imgWrap {
						position: relative;
						width: 100%;
						height: 100%;
						border-radius: inherit;
						cursor: pointer;
						display: flex;
						align-items: center;

						img {
							width: 100%;
							height: 30px;
							object-fit: contain;
							border-radius: inherit;

							&.file {
								max-height: 24px;
							}
						}

						.type__list {
							position: absolute;
							bottom: 0;
							left: 0;
							right: 0;
							height: 22px;
							background: rgba(0, 0, 0, 0.4);
							cursor: pointer;
							border-radius: 0 0 6px 6px;

							input {
								width: 100%;
								text-align: center;
								color: #ffffff;
								font-size: 12px;
								border: none;
								cursor: pointer;
								padding-left: 4px;
							}

							input:focus {
								outline: none;
							}

							ul {
								position: absolute;
								top: 110%;
								left: 0;
								width: 100%;
								padding: 0;
								font-size: 12px;
								background: #ffffff;
								border: 1px solid #dbdbdb;
								box-shadow: 1px 1px 8px 0px rgba(2, 2, 2, 0.2);
								z-index: 9;

								li {
									padding: 3px 15px;

									&:hover {
										color: #0066cc;
										background: #e9f3fc;
									}
								}
							}

							&:after {
								position: absolute;
								right: 5%;
								top: 50%;
								transform: translateY(-50%);
								content: "";
								width: 5px;
								height: 5px;
								border-top: 5px solid #ffffff;
								border-left: 5px solid transparent;
								border-right: 5px solid transparent;
							}
						}
					}
				}

				.last {
					font-size: 12px;

					b {
						color: #06c;
						font-size: 12px;
					}
				}

				.imgWrap:hover .mask {
					background-color: #333333;
					opacity: 0.5;
				}

				.imgWrap:hover .el-icon-delete,
				.imgWrap:hover .el-icon-zoom-in {
					display: block;
					z-index: 1000;
				}
			}
		}

		.upload-item {
			position: relative;
			color: #06c;
			height: 39px;
			min-width: 52px;
			background: #ffffff;
			border: 1px solid #e6e6e6;
			border-radius: 4px;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			font-size: 12px;

			b {
				color: #06c;
				font-size: 12px;
				transform: rotate(45deg);
			}
		}

		.upload-area {
			position: absolute;
			width: 52px;
			height: 39px;
			background: #ffffff;
			border: 1px solid #e6e6e6;
			border-radius: 4px;
		}
	}
}

@media screen and (max-width: $mb-width) {
	.content {
		.top-tips {
			top: -61%;
			left: -6.5%;
			width: 96vw;
			text-align: left;
		}

		.swiper__content {
			//width: 30vw;
			//height: 23vw;
			width: 100%;
			height: 22.5vw;

			position: relative;

			.showImg {
				input[type="file"] {
					position: absolute;
					left: 0;
					top: 0;
					right: 0;
					bottom: 0;
					opacity: 0;
					width: 100%;
					height: 100%;
					cursor: pointer;
				}

				.uploadImgMb {
					position: relative;
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;
					color: #0066cc;
					cursor: pointer;
					font-size: 14px;
					width: 100%;
					height: 100%;

					> b {
						font-size: 17px;
						transform: rotate(45deg);
					}
				}
			}

			.icon {
				position: absolute;
				width: 19px;
				height: 19px;
				background: #ffffff;
				border-radius: 50%;
				box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.2);
				z-index: 1;
				display: flex;
				justify-content: center;
				align-items: center;

				> b {
					font-size: 12px;
					//position: absolute;
					cursor: pointer;
					color: #666666;
				}
			}

			.preIcon {
				left: -10px;

				b {
					transform: rotate(180deg);
				}
			}

			.nextIcon {
				right: -10px;
			}
		}

		.drawings__list {
			display: none !important;
		}
	}
}
</style>
