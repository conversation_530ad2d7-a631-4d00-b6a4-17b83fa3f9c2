<template>
	<div class="summary-box" :class="modal.wrapClass" :style="modal.style">
		<div class="bps-container" v-for="(o, oi) in modal.outer" :key="oi">
			<EditDiv v-if="modal.outer[0].title" tag-name="h2" v-model:content="modal.outer[0].title.value" @click="setModalType(modal.outer[0].title,modal.outer,'text')" />
			<div class="containerGrid">
				<div v-for="(item,index) in modal.outer[0].list">
					<EditDiv v-if="modal.outer[0].list[index].title" tag-name="h2" v-model:content="modal.outer[0].list[index].title.value" @click="setModalType(o.list[index].title,modal.outer,'text')" />
					<EditDiv v-if="modal.outer[0].list[index].subTitle" class="des" v-model:content="modal.outer[0].list[index].subTitle.value" @click="setModalType(o.list[index].subTitle,modal.outer,'text')" />
					<div class="btnWrap" v-if="modal.outer[0].list[index].button" @click="setModalType(modal.outer[0].list[index].button,modal.outer,'button')">
						<a :href="modal.outer[0].list[index].button.url" :title="modal.outer[0].list[index].button.alt" :target="modal.outer[0].list[index].button.target || '_self'"
							 class="default-button bps-button" :style="{...modal.outer[0].list[index].button.style }">
							{{ modal.outer[0].list[index].button.value }}
						</a>
					</div>
					<div v-if="modal.outer[0].list[index].href">
						<pic class="pic" :title="modal.outer[0].list[index].img.alt" :alt="modal.outer[0].list[index].img.alt" :src="modal.outer[0].list[index].img.value" v-if="modal.outer[0].list[index].img"  @click="setModalType(modal.outer[0].list[index].img,modal.outer,'img')"/>
						<p v-if="modal.outer[0].list[index].title2" @click="setModalType(modal.outer[0].list[index].title2,modal.outer,'text')">
							<a :href="modal.outer[0].list[index].href.value">
								{{modal.outer[0].list[index].title2.value}}
								<b class="icon-jxsht-jcsz-jr pc-none mb-block" style="width: 15px;height: 15px;border-radius: 50%;background-color: #D97A4D;color: #fff;display: inline-block;line-height: 15px"></b>
							</a>
						</p>
						<p v-if="modal.outer[0].list[index].subTitle2" @click="setModalType(modal.outer[0].list[index].subTitle2,modal.outer,'text')">
							<a :href="modal.outer[0].list[index].href.value">
								{{modal.outer[0].list[index].subTitle2.value}}
							</a>
						</p>
						<p style="display: inline-block" v-if="modal.outer[0].list[index].text" @click="setModalType(modal.outer[0].list[index].text,modal.outer,'text')">
							<a :href="modal.outer[0].list[index].href.value">
								{{modal.outer[0].list[index].text.value}}
							</a>
						</p>
						<p style="display: inline-block" v-if="modal.outer[0].list[index].price" @click="setModalType(modal.outer[0].list[index].price,modal.outer,'text')">
							<a :href="modal.outer[0].list[index].href.value">
								<CCYRate :price="modal.outer[0].list[index].price.value"></CCYRate> ea.
							</a>
						</p>
					</div>
					<template v-else>
						<pic class="pic" :title="modal.outer[0].list[index].img.alt" :alt="modal.outer[0].list[index].img.alt" :src="modal.outer[0].list[index].img.value" v-if="modal.outer[0].list[index].img"  @click="setModalType(modal.outer[0].list[index].img,modal.outer,'img')"/>
						<EditDiv tag-name="p" v-model:content="modal.outer[0].list[index].text.value" v-if="modal.outer[0].list[index].text" @click="setModalType(modal.outer[0].list[index].text,modal.outer,'text')" />
					</template>
				</div>
			</div>
		</div>
		<div class="bps-container sticker" v-for="(l, li) in modal.list" :key="li">
			<EditDiv v-if="modal.list[0].title" tag-name="h2" v-model:content="modal.list[0].title.value" @click="setModalType(modal.outer[0].list,modal.outer,'text')" />
			<div class="sticker-container">
					<div v-for="(item,index) in modal.list[0].icon" class="sticker_div">
						<div>
							<pic class="pic" :src="item.img.value" :title="item.img.alt" :alt="item.img.alt" @click="setModalType(l.icon[index].img,modal.outer,'img')"/>
							<div v-if="modal.list[0].Switch" class="switch">
								<p>{{index + 1}}</p>
							</div>
							<div>
								<EditDiv tag-name="p" v-model:content="item.title.value" @click="setModalType(l.icon[index].title,modal.outer,'text')" />
								<EditDiv tag-name="span" v-model:content="item.subTitle.value" @click="setModalType(l.icon[index].subTitle,modal.outer,'text')" />
							</div>
						</div>
					</div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	props: {
		data: {
			type: Object,
			default: {},
		}
	},
	data() {
		return {
			modal: {
				style: {},
				type: {index: 0, clickPosition: 'outer'},
				...this.data
			}
		};
	},
	watch: {
		modal: {
			handler(val) {
				if (process.env.isManage) this.$emit("update:data", val);
			},
			immediate: true,
			deep: true
		}
	},
	methods:{
		setModalType(target, targetArray, clickType, event, other) {
			this.$setModal(this, target, targetArray, clickType, event, other)
		},
	}
};
</script>

<style lang="scss" scoped>
	.newSticker4 {
		background-color: #fff !important;
		padding-bottom: 0 !important;
		.bps-container {
			.sticker-container {
				.sticker_div {
					&:nth-child(2) {
						position: relative;
						&::before,&::after {
							content: "";
							position: absolute;
							height: 1px;
							background: #333333;
							width: 80%;
							left: 0;
							top: 69%;
						}
						&::before {
							left: 67%;
						}
						&::after {
							left:-45%;
						}
					}
					div {
						img {
							width: auto;
							height: 293px;
						}
						.switch {
							min-height: auto !important;
							padding-bottom: 0 !important;
							p {
								width: 44px;
								height: 44px;
								border: 1px solid #333333;
								border-radius: 50%;
								text-align: center;
								line-height: 44px;
								margin: 0 auto;
							}
						}
					}
				}
			}
		}
	}
	.pc-none {
		display: none !important;
	}
	.sticker-part4 {
		padding: 70px 0;
		background: #F7F7F8;
		.sticker {
			h2 {
				text-align: center !important;
				font-size: 42px;
			}
			.sticker-container {
				margin-top: 40px;
				display: grid;
				grid-template-columns: repeat(3,1fr);
				grid-column-gap: 32px;
				div {
					div {
						text-align: center;
						div {
							padding: 21px 11px;
							min-height: 125px;
							p {
								font-size: 20px;
								font-family: Roboto;
								font-weight: bold;
								color: #333333;
								margin-bottom: 15px;
							}
							span {
								font-size: 16px;
								font-family: Roboto;
								font-weight: 400;
								color: #333333;
							}
						}
					}
				}
			}
		}
	}
	.home-part4 {
		padding: 70px 0;

		.bps-container {

			.containerGrid {
				display: grid;
				grid-template-columns: repeat(3,1fr);
				grid-row-gap: 20px;
				grid-column-gap: 20px;
				div {
					&:nth-child(1) {
						padding-right: 10%;
						padding-top: 5%;
						h2 {
							font-size: 36px;
						}
						.btnWrap {
							width: 50%;
						}
					}
					position: relative;
					.des {
						margin: 11px  0 35px 0;
					}
					p {
						position: absolute;
						left: 50%;
						transform: translateX(-50%);
						bottom: 18px;
					}
				}
			}
		}
	}
	.home-part6 {
		background-color: #F7F7F8;
		padding: 70px 0;
		.bps-container {
			h2 {
				text-align: center;
				font-size: 32px;
				margin-bottom: 55px;
			}
			.containerGrid {
				display: grid;
				grid-template-columns: repeat(4,1fr);
				grid-column-gap: 26px;
				div {
					background-color: #fff;
					text-align: center;
					p {
						margin-top: 15px;
						text-align: center;
						&:nth-child(2) {
							font-weight: bold;
							font-size: 18px;
						}
						&:nth-child(3) {
							font-size: 14px;
						}
						&:nth-child(4) {
							font-size: 16px;
							margin-bottom: 20px;
						}
					}
				}
			}
		}
	}

@media screen and (max-width: $mb-width) {
	.newSticker4 {
		.bps-container {
			.sticker-container {
				display: block;
				.sticker_div {
					&:nth-child(2) {
						position: relative;
						&::before,&::after {
							content: none;
						}
					}
				}
			}
		}
	}
		.mb-block {
			display: inline-block !important;
		}
		.home-part4 {
			padding: 27px 0;
			.bps-container {
				.containerGrid {
					grid-template-columns: repeat(2,1fr);
					grid-row-gap: 12px;
					grid-column-gap: 12px;
					div {
						&:nth-child(1) {
							padding: 0 !important;
							.btnWrap {
								width: 100% !important;
							}
						}
						h2 {
							font-size: 16px !important;
						}
						.des {
							font-size: 12px;
							margin: 7px 0 15px 0;
						}
						img {
							min-height: 190px;
						}
						.btnWrap {
							a {
								width: 50%;
								border-radius: 25px;
							}
						}
						p {
							font-size: 12px;
							width: 100%;
							bottom: 5px;
							text-align: center;
						}
					}
				}
			}
		}
	.home-part6 {
		padding: 27px 0;
		.bps-container {
			h2 {
				text-align: left;
				font-size: 16px;
				margin-bottom: 12px;
			}
			.containerGrid {
				display: grid;
				grid-template-columns: repeat(2,1fr);
				grid-column-gap: 10px;
				grid-row-gap: 12px;
				div {
					p {
						margin-top: 10px;
						text-align: center;
						font-size: 12px !important;
					}
				}
			}
		}
	}
}
</style>
