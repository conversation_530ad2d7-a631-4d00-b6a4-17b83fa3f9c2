<template>
	<div class="CanvasFactory">
		<article class="canvas-self">
			<canvas id="canvas"></canvas>
		</article>
	</div>
</template>
<script>
export default {
	props: {
		printingData: {
			type: Object,
		},
        lanyardType:{
            type: [String, Boolean],
            default: false,
        },
		tabsName: {
			type: String,
		},
		canvasData: {
			type: Object,
		},
		tampInfo: {
			type: Array,
		},
		fugaiImage: {
			type: String,
		},
		fugaiImage2: {
			type: String,
		},
		fugaiImage3: {
			type: String,
		},
		danImage: {
			type: String,
		},
		danImageB: {
			type: String,
		},
		shuangeImage: {
			type: String,
		},
	},
	data() {
		return {
            stopPlay: false,
			name1: "Two Lines of Words",
			name2: "Two lines words & started, end clipart",
			name3: "Two lines words & started clipart",
			name4: "Two lines words & end clipart",
			copyRopeText1: "Text Here",
			copyRopeText2: "Text Here",
			copyRopeFontFillStyle1: "#ffffff",
			copyRopeFontFillStyle2: "#EAAA00",
			copyBeforeTempLogo: "https://res.cloudinary.com/gs-jj-cloud/image/upload/v1677139759/gs-jj/soikei6sst8uo5r6cvbl.png",
			copyAfterTempLogo: "https://res.cloudinary.com/gs-jj-cloud/image/upload/v1677139759/gs-jj/soikei6sst8uo5r6cvbl.png",
			fontImageHeight: 35, //文字图片高度
			fontImageWidth: 35, //文字图片宽度
			fontImageWidth2: 35, //文字图片宽度2
			canvasToImage: "",
			logo: "https://res.cloudinary.com/gs-jj-cloud/image/upload/v1677139759/gs-jj/soikei6sst8uo5r6cvbl.png", //logo
		};
	},
	methods: {
		async canvasInit() {
			return new Promise(async (resolve, reject) => {
				this.$store.commit("setCanvasLoading", true);
				let canvas = document.getElementById("canvas");
				let ctx = canvas.getContext("2d");

				canvas.width = this.canvasData.canvasWdith;
				canvas.height = this.canvasData.canvasHeight;
				ctx.save();
                //light Up织带不用执行这些步骤
                if(this.lanyardType!=='lightUpLanyards'){
                    if(this.lanyardType==='twoToneLanyards'){
                        //铺底色
                        await this.buildCanvasFun({
                            context: ctx,
                            src: this.canvasData.popularImg,
                            x: 0,
                            y: 0,
                            width: this.canvasData.canvasWdith,
                            height: this.canvasData.canvasHeight,
                        });
                    }else{
                        if (this.tabsName == "POPULAR" || this.tabsName == "DESIGN") {
                            //铺底色
                            await this.buildCanvasFun({
                                context: ctx,
                                src: this.canvasData.popularImg,
                                x: 0,
                                y: 0,
                                width: this.canvasData.canvasWdith,
                                height: this.canvasData.canvasHeight,
                            });
                        } else if (this.tabsName == "CUSTOM") {
                            ctx.fillStyle = this.canvasData.defaultFillStyle;
                            ctx.fillRect(0, 0, this.canvasData.canvasWdith, this.canvasData.canvasHeight);
                        }
                    }
                    if ((this.lanyardType==='glitterLanyards' || this.lanyardType === 'reflectiveLanyards') && this.canvasData.extraImg.length){
                        await this.buildCanvasFun({
                            context: ctx,
                            src: !this.canvasData.ropeCross ? this.canvasData.extraImg[2].path:(this.canvasData.buckleB1 ? this.canvasData.extraImg[1].path : this.canvasData.extraImg[0].path),
                            x: 0,
                            y: 0,
                            width: this.canvasData.canvasWdith,
                            height: this.canvasData.canvasHeight,
                        });
                    }
                    if (this.canvasData.ropeCross) {
                        //Desigen图块无需纹理
                        if (!this.isDesigen) {
                            //铺纹理
                            await this.buildCanvasFun({
                                context: ctx,
                                src: this.canvasData.buckleB1 ? this.fugaiImage2 : this.fugaiImage,
                                x: 0,
                                y: 0,
                                width: this.canvasData.canvasWdith,
                                height: this.canvasData.canvasHeight,
                            });
                        }
						if(this.lanyardType!=='blankLanyards'){
							if(this.lanyardType !== 'phoneLanyards' && this.lanyardType !== 'shortWristLanyards'){
								await this.drawRope({
									ctx,
									lRo: this.canvasData.buckleB1 ? this.canvasData.lRo1B : this.canvasData.lRo1,
									lX: this.canvasData.buckleB1 ? this.canvasData.lX1B : this.canvasData.lX1,
									lY: this.canvasData.buckleB1 ? this.canvasData.lY1B : this.canvasData.lY1,
									rRo: this.canvasData.buckleB1 ? this.canvasData.rRo1B : this.canvasData.rRo1,
									rX: this.canvasData.buckleB1 ? this.canvasData.rX1B : this.canvasData.rX1,
									rY: this.canvasData.buckleB1 ? this.canvasData.rY1B : this.canvasData.rY1,
								});
							}else{
								await this.drawRope({
									ctx,
									lRo: this.canvasData.lRo3,
									lX: this.canvasData.lX3,
									lY: this.canvasData.lY3,
									rRo: this.canvasData.rRo3,
									rX: this.canvasData.rX3,
									rY: this.canvasData.rY3,
								});
							}
						}
                    } else {
                        if (!this.isDesigen) {
                            await this.buildCanvasFun({
                                context: ctx,
                                src: this.fugaiImage3,
                                x: 0,
                                y: 0,
                                width: this.canvasData.canvasWdith,
                                height: this.canvasData.canvasHeight,
                            });
                        }
						if(this.lanyardType!=='blankLanyards'){
							await this.drawRope({
								ctx,
								lRo: this.canvasData.lRo2,
								lX: this.canvasData.lX2,
								lY: this.canvasData.lY2 - 13,
								rRo: this.canvasData.rRo2,
								rX: this.canvasData.rX2,
								rY: this.canvasData.rY2,
							});
						}
                    }
                }
                if (this.canvasData.ropeCross) {
                    await this.downFun(this.canvasData.buckleB1 ? this.danImageB : this.danImage, ctx);
                } else {
                    await this.downFun(this.shuangeImage, ctx);
                }
				this.canvasToImage = canvas.toDataURL("image/png", 1.0);
				this.showPreview = true;
				this.$emit("canvasToImage", this.canvasToImage);
				this.$store.commit("setCanvasLoading", false);
				console.log("end");
				resolve("done");
			});
		},

		drawRope({ ctx, lRo, lX, lY, rRo, rX, rY, ropeCross } = {}) {
			return new Promise(async (resolve, reject) => {
				//用于文字
				let LT_canvas = document.createElement("canvas");
				let LT_ctx = LT_canvas.getContext("2d");

				let LB_canvas = document.createElement("canvas");
				let LB_ctx = LB_canvas.getContext("2d");

				let RT_canvas = document.createElement("canvas");
				let RT_ctx = RT_canvas.getContext("2d");

				let RB_canvas = document.createElement("canvas");
				let RB_ctx = RB_canvas.getContext("2d");

				let TT_canvas = document.createElement("canvas");
				let TT_ctx = TT_canvas.getContext("2d");

				let TB_canvas = document.createElement("canvas");
				let TB_ctx = TB_canvas.getContext("2d");

				// let rope_canvas3 = document.createElement("canvas");
				// let rope_ctx3 = rope_canvas3.getContext("2d");

				let times = null; //绘制次数
				let textWidth = null; //宽度
				let textWidth2 = null;
				let theRopeWidth;
				let theRopeHeight;
				//挂绳参数
				if (this.canvasData.ropeCross) {
					if (this.canvasData.buckleB1) {
						theRopeWidth = this.canvasData.ropeWidthType1B;
						theRopeHeight = this.canvasData.ropeHeightType1B;
					} else {
						theRopeWidth = this.canvasData.ropeWidthType1;
						theRopeHeight = this.canvasData.ropeHeightType1;
					}
				} else {
					//根据是否有配件B来决定文字绘制长度 以及坐标
					if (this.canvasData.buckleB1) {
						theRopeWidth = this.canvasData.ropeWidthType2 - 215;
						theRopeHeight = this.canvasData.ropeHeightType2;
					} else {
						theRopeWidth = this.canvasData.ropeWidthType2 - 106;
						theRopeHeight = this.canvasData.ropeHeightType2;
						rX = rX - 106;
					}
				}
				LT_canvas.width = theRopeWidth;
				LT_canvas.height = theRopeHeight;
				LB_canvas.width = theRopeWidth;
				LB_canvas.height = theRopeHeight;

				//顶部文字
				TT_canvas.width = this.canvasData.ropeWidthTypeT;
				TT_canvas.height = theRopeHeight;
				TB_canvas.width = this.canvasData.ropeWidthTypeT;
				TB_canvas.height = theRopeHeight;

				RT_canvas.width = theRopeWidth;
				RT_canvas.height = theRopeHeight;
				RB_canvas.width = theRopeWidth;
				RB_canvas.height = theRopeHeight;

				let fix1 = null;
				let fix2 = null;
				let textHeight = null;
				let textHeight2 = null;

				ctx.save();
				//默认文字
				LT_ctx.font = (this.canvasData.ropeFontStyle1 ? "normal" : "normal") + " " + (this.canvasData.ropeFontWeight1 ? "normal" : "normal") + " " + this.canvasData.ropeFontSize1 + "px" + " " + (this.canvasData.ropFontFamily1 ? this.canvasData.ropFontFamily1 : "Arial");
				RT_ctx.font = (this.canvasData.ropeFontStyle1 ? "normal" : "normal") + " " + (this.canvasData.ropeFontWeight1 ? "normal" : "normal") + " " + this.canvasData.ropeFontSize1 + "px" + " " + (this.canvasData.ropFontFamily1 ? this.canvasData.ropFontFamily1 : "Arial");

				TT_ctx.font = (this.canvasData.ropeFontStyle1 ? "normal" : "normal") + " " + (this.canvasData.ropeFontWeight1 ? "normal" : "normal") + " " + this.canvasData.ropeFontSize1 + "px" + " " + (this.canvasData.ropFontFamily1 ? this.canvasData.ropFontFamily1 : "Arial");
				TB_ctx.font = (this.canvasData.ropeFontStyle1 ? "normal" : "normal") + " " + (this.canvasData.ropeFontWeight1 ? "normal" : "normal") + " " + this.canvasData.ropeFontSize1 + "px" + " " + (this.canvasData.ropFontFamily1 ? this.canvasData.ropFontFamily1 : "Arial");

				LB_ctx.font = (this.canvasData.ropeFontStyle2 ? "normal" : "normal") + " " + (this.canvasData.ropeFontWeight2 ? "normal" : "normal") + " " + this.canvasData.ropeFontSize2 + "px" + " " + (this.canvasData.ropFontFamily2 ? this.canvasData.ropFontFamily2 : "Arial");
				RB_ctx.font = (this.canvasData.ropeFontStyle2 ? "normal" : "normal") + " " + (this.canvasData.ropeFontWeight2 ? "normal" : "normal") + " " + this.canvasData.ropeFontSize2 + "px" + " " + (this.canvasData.ropFontFamily2 ? this.canvasData.ropFontFamily2 : "Arial");

				//前后
				if (this.printingData && this.printingData.value.paramName == "A Line of Words 2") {
					fix1 = 1;
					fix2 = 1;
					LT_ctx.textBaseline = "middle";
					TT_ctx.textBaseline = "middle";
					RT_ctx.textBaseline = "middle";
					LB_ctx.textBaseline = "middle";
					TB_ctx.textBaseline = "middle";
					LB_ctx.font = (this.canvasData.ropeFontStyle2 ? "oblique" : "normal") + " " + (this.canvasData.ropeFontWeight2 ? "bold" : "normal") + " " + this.canvasData.ropeFontSize1 + "px" + " " + (this.canvasData.ropFontFamily2 ? this.canvasData.ropFontFamily2 : "Arial");
					TB_ctx.font = (this.canvasData.ropeFontStyle2 ? "oblique" : "normal") + " " + (this.canvasData.ropeFontWeight2 ? "bold" : "normal") + " " + this.canvasData.ropeFontSize1 + "px" + " " + (this.canvasData.ropFontFamily2 ? this.canvasData.ropFontFamily2 : "Arial");
					RB_ctx.textBaseline = "middle";
					RB_ctx.font = (this.canvasData.ropeFontStyle2 ? "oblique" : "normal") + " " + (this.canvasData.ropeFontWeight2 ? "bold" : "normal") + " " + this.canvasData.ropeFontSize1 + "px" + " " + (this.canvasData.ropFontFamily2 ? this.canvasData.ropFontFamily2 : "Arial");
					// 上下
				} else if (this.twoLine) {
					fix1 = 3;
					fix2 = 5;
					LT_ctx.textBaseline = "alphabetic";
					TT_ctx.textBaseline = "alphabetic";
					RT_ctx.textBaseline = "alphabetic";
					LB_ctx.textBaseline = "top";
					TB_ctx.textBaseline = "top";
					LB_ctx.font = (this.canvasData.ropeFontStyle2 ? "oblique" : "normal") + " " + (this.canvasData.ropeFontWeight2 ? "bold" : "normal") + " " + this.canvasData.ropeFontSize2 + "px" + " " + (this.canvasData.ropFontFamily2 ? this.canvasData.ropFontFamily2 : "Arial");
					TB_ctx.font = (this.canvasData.ropeFontStyle2 ? "oblique" : "normal") + " " + (this.canvasData.ropeFontWeight2 ? "bold" : "normal") + " " + this.canvasData.ropeFontSize2 + "px" + " " + (this.canvasData.ropFontFamily2 ? this.canvasData.ropFontFamily2 : "Arial");
					RB_ctx.textBaseline = "top";
					RB_ctx.font = (this.canvasData.ropeFontStyle2 ? "oblique" : "normal") + " " + (this.canvasData.ropeFontWeight2 ? "bold" : "normal") + " " + this.canvasData.ropeFontSize2 + "px" + " " + (this.canvasData.ropFontFamily2 ? this.canvasData.ropFontFamily2 : "Arial");
				} else {
					fix1 = 1;
					fix2 = 1;
					LT_ctx.textBaseline = "middle";
					TT_ctx.textBaseline = "middle";
					RT_ctx.textBaseline = "middle";
				}
				//高度修正参数
				textHeight = (theRopeHeight - this.canvasData.ropeFontSize1) / 2 + this.canvasData.ropeFontSize1 / 2 + fix1;
				textHeight2 = (theRopeHeight - this.canvasData.ropeFontSize1) / 2 + this.canvasData.ropeFontSize1 / 2 + fix2;
				//文字1
				LT_ctx.fillStyle = this.ropeFontFillStyle1;
				LT_ctx.font = (this.canvasData.ropeFontStyle1 ? "oblique" : "normal") + " " + (this.canvasData.ropeFontWeight1 ? "bold" : "normal") + " " + this.canvasData.ropeFontSize1 + "px" + " " + (this.canvasData.ropFontFamily1 ? this.canvasData.ropFontFamily1 : "Arial");
				TT_ctx.fillStyle = this.ropeFontFillStyle1;
				TT_ctx.font = (this.canvasData.ropeFontStyle1 ? "oblique" : "normal") + " " + (this.canvasData.ropeFontWeight1 ? "bold" : "normal") + " " + this.canvasData.ropeFontSize1 + "px" + " " + (this.canvasData.ropFontFamily1 ? this.canvasData.ropFontFamily1 : "Arial");
				RT_ctx.fillStyle = this.ropeFontFillStyle1;
				RT_ctx.font = (this.canvasData.ropeFontStyle1 ? "oblique" : "normal") + " " + (this.canvasData.ropeFontWeight1 ? "bold" : "normal") + " " + this.canvasData.ropeFontSize1 + "px" + " " + (this.canvasData.ropFontFamily1 ? this.canvasData.ropFontFamily1 : "Arial");
				//文字2
				LB_ctx.fillStyle = this.ropeFontFillStyle2;
				TB_ctx.fillStyle = this.ropeFontFillStyle2;
				RB_ctx.fillStyle = this.ropeFontFillStyle2;

				//根据文字长度计算绘制次数
				textWidth = this.ropeText1 ? Math.ceil(LT_ctx.measureText(this.ropeText1).width) : 0;

				textWidth2 = this.ropeText2 ? Math.ceil(LT_ctx.measureText(this.ropeText2).width) : 0;
				//左绳文字遮挡效果
				if (this.canvasData.ropeCross) {
					if (this.canvasData.buckleB1) {
						//左上
						await this.clipFun({
							context: LT_ctx,
							a1: 0,
							a2: 0,
							b1: theRopeWidth - 80,
							b2: 0,
							c1: theRopeWidth + 15,
							c2: theRopeHeight,
							d1: 0,
							d2: theRopeHeight,
							e1: 0,
							e2: 0,
						});
						//左下
						await this.clipFun({
							context: LB_ctx,
							a1: 0,
							a2: 0,
							b1: theRopeWidth - 80,
							b2: 0,
							c1: theRopeWidth + 15,
							c2: theRopeHeight,
							d1: 0,
							d2: theRopeHeight,
							e1: 0,
							e2: 0,
						});
						await this.clipFun({
							context: TT_ctx,
							a1: lX + 10,
							a2: lY,
							b1: this.canvasData.ropeWidthTypeT / 2 - 68,
							b2: lY,
							c1: this.canvasData.ropeWidthTypeT / 2 - 68 - 15,
							c2: theRopeHeight,
							d1: lX + 10 + 15,
							d2: theRopeHeight,
							e1: lX + 10,
							e2: lY,
						});
						//上下
						await this.clipFun({
							context: TB_ctx,
							a1: lX + 10,
							a2: lY,
							b1: this.canvasData.ropeWidthTypeT / 2 - 68,
							b2: lY,
							c1: this.canvasData.ropeWidthTypeT / 2 - 68 - 15,
							c2: theRopeHeight,
							d1: lX + 10 + 15,
							d2: theRopeHeight,
							e1: lX + 10,
							e2: lY,
						});
					} else {
						//左上
						if(this.lanyardType === 'phoneLanyards' || this.lanyardType === 'shortWristLanyards') {
							await this.clipFun({
								context: LT_ctx,
								a1: 0,
								a2: 0,
								b1: theRopeWidth - 170,
								b2: 0,
								c1: theRopeWidth - 70,
								c2: theRopeHeight,
								d1: 0,
								d2: theRopeHeight,
								e1: 0,
								e2: 0,
							});
						}else{
							await this.clipFun({
								context: LT_ctx,
								a1: 0,
								a2: 0,
								b1: theRopeWidth - 100,
								b2: 0,
								c1: theRopeWidth,
								c2: theRopeHeight,
								d1: 0,
								d2: theRopeHeight,
								e1: 0,
								e2: 0,
							});
						}
						await this.clipFun({
							context: LB_ctx,
							a1: 0,
							a2: 0,
							b1: theRopeWidth - 100,
							b2: 0,
							c1: theRopeWidth,
							c2: theRopeHeight,
							d1: 0,
							d2: theRopeHeight,
							e1: 0,
							e2: 0,
						});
					}
					//上上
					await this.clipFun({
						context: TT_ctx,
						a1: lX + 10,
						a2: lY,
						b1: this.canvasData.ropeWidthTypeT / 2 - 65,
						b2: lY,
						c1: this.canvasData.ropeWidthTypeT / 2 - 65 - 15,
						c2: theRopeHeight,
						d1: lX + 10 + 15,
						d2: theRopeHeight,
						e1: lX + 10,
						e2: lY,
					});
					//上下
					await this.clipFun({
						context: TB_ctx,
						a1: lX + 10,
						a2: lY,
						b1: this.canvasData.ropeWidthTypeT / 2 - 65,
						b2: lY,
						c1: this.canvasData.ropeWidthTypeT / 2 - 65 - 15,
						c2: theRopeHeight,
						d1: lX + 10 + 15,
						d2: theRopeHeight,
						e1: lX + 10,
						e2: lY,
					});
				} else {
					//上上
					await this.clipFun({
						context: TT_ctx,
						a1: lX + 55,
						a2: lY,
						b1: this.canvasData.ropeWidthTypeT / 2 - 100,
						b2: lY,
						c1: this.canvasData.ropeWidthTypeT / 2 - 100 - 15,
						c2: theRopeHeight,
						d1: lX + 55 + 15,
						d2: theRopeHeight,
						e1: lX + 55,
						e2: lY,
					});
					//上下
					await this.clipFun({
						context: TB_ctx,
						a1: lX + 55,
						a2: lY,
						b1: this.canvasData.ropeWidthTypeT / 2 - 100,
						b2: lY,
						c1: this.canvasData.ropeWidthTypeT / 2 - 100 - 15,
						c2: theRopeHeight,
						d1: lX + 55 + 15,
						d2: theRopeHeight,
						e1: lX + 55,
						e2: lY,
					});
				}
				let temp = 0;
				let temp2 = 0;
				//是否有文字图片
				if (this.canvasData.fontBeforeImg || this.canvasData.fontAfterImg || this.beforeTempLogo || this.afterTempLogo) {
					let rrWidth = 0; //文字+图片宽度
					let img1 = this.canvasData.fontBeforeImg ? await this.imgOnloadFun(this.canvasData.fontBeforeImg + "?temp=" + new Date().valueOf()) : this.beforeTempLogo ? await this.imgOnloadFun(this.beforeTempLogo + "?temp=" + new Date().valueOf()) : "";
					let img2 = this.canvasData.fontAfterImg ? await this.imgOnloadFun(this.canvasData.fontAfterImg + "?temp=" + new Date().valueOf()) : this.afterTempLogo ? await this.imgOnloadFun(this.afterTempLogo + "?temp=" + new Date().valueOf()) : "";
					let scale;
					let scale2;

					if (img1) {
						scale = this.fontImageHeight / img1.height; //缩放比例
						this.fontImageWidth = scale * img1.width;
					}
					if (img2) {
						scale2 = this.fontImageHeight / img2.height; //缩放比例
						this.fontImageWidth2 = scale2 * img2.width;
					}

					rrWidth = textWidth + this.fontImageWidth + this.fontImageWidth2 + this.canvasData.spacing;
					times = Math.ceil(theRopeWidth / rrWidth) + 1;
					let imageX = 0; //a
					temp = imageX + this.fontImageWidth + 7; //text
					let imageX2 = temp + textWidth + 7; //c
					for (let i = 0; i < times; i++) {
						if (img1) {
							LT_ctx.drawImage(img1, imageX, (theRopeHeight - this.fontImageHeight) / 2, this.fontImageWidth, this.fontImageHeight);
							TT_ctx.drawImage(img1, imageX, (theRopeHeight - this.fontImageHeight) / 2, this.fontImageWidth, this.fontImageHeight);
							RT_ctx.drawImage(img1, imageX, (theRopeHeight - this.fontImageHeight) / 2, this.fontImageWidth, this.fontImageHeight);
						}
						if (img2) {
							LT_ctx.drawImage(img2, imageX2, (theRopeHeight - this.fontImageHeight) / 2, this.fontImageWidth2, this.fontImageHeight);
							TT_ctx.drawImage(img2, imageX2, (theRopeHeight - this.fontImageHeight) / 2, this.fontImageWidth2, this.fontImageHeight);
							RT_ctx.drawImage(img2, imageX2, (theRopeHeight - this.fontImageHeight) / 2, this.fontImageWidth2, this.fontImageHeight);
						}
						if (this.ropeText1) {
							LT_ctx.fillText(this.ropeText1, temp, textHeight);
							TT_ctx.fillText(this.ropeText1, temp, textHeight);
							RT_ctx.fillText(this.ropeText1, temp, textHeight);
						}
						if (this.ropeText2) {
							LB_ctx.fillText(this.ropeText2, temp, textHeight2);
							RB_ctx.fillText(this.ropeText2, temp, textHeight2);
							TB_ctx.fillText(this.ropeText2, temp, textHeight2);
						}
						imageX = imageX2 + this.fontImageWidth2 + 30;
						temp = imageX + this.fontImageWidth + 7;
						imageX2 = temp + textWidth + 7;
					}
					//左绳
					ctx.rotate((lRo * Math.PI) / 180);
					ctx.drawImage(LT_canvas, lX, lY);
					if (this.ropeText2 && this.twoLine) {
						ctx.drawImage(LB_canvas, lX, lY);
					}
					ctx.restore();
					//右绳
					ctx.rotate((rRo * Math.PI) / 180);
					ctx.drawImage(RT_canvas, rX, rY);
					if (this.ropeText2 && this.twoLine) {
						ctx.drawImage(RB_canvas, rX, rY);
					}
					ctx.restore();
					if (this.canvasData && this.canvasData.doubleFont && this.canvasData.doubleFont.paramName == "Both Side") {
						ctx.rotate(0);
						ctx.drawImage(TT_canvas, 0, 32);
						if (this.ropeText2 && this.twoLine) {
							ctx.drawImage(TB_canvas, 0, 32);
						}
					}
				} else {
					if (this.printingData && this.printingData.value.paramName == "A Line of Words 2") {
						// ropetextTimesFun(theRopeWidth, textWidth + 20 + textWidth2).then((res) => {
						//   console.log(res);
						// });
						times = textWidth && textWidth2 ? Math.ceil(theRopeWidth / (textWidth + textWidth2)) : 0;
					} else {
						times = textWidth ? Math.ceil(theRopeWidth / textWidth) : 0;
					}
					for (let i = 0; i < times; i++) {
						if (this.printingData && this.printingData.value.paramName == "A Line of Words 2") {
							if (this.ropeText1) {
								LT_ctx.fillText(this.ropeText1, temp, textHeight);
								RT_ctx.fillText(this.ropeText1, temp, textHeight);
								TT_ctx.fillText(this.ropeText1, temp, textHeight);
							}
							if (this.ropeText2) {
								LB_ctx.fillText(this.ropeText2, temp2 + 5 + textWidth, textHeight2);
								RB_ctx.fillText(this.ropeText2, temp2 + 5 + textWidth, textHeight2);
								TB_ctx.fillText(this.ropeText2, temp2 + 5 + textWidth, textHeight2);
							}
							if (this.canvasData.doubleFont) {
							}
						} else {
							if (this.ropeText1) {
								LT_ctx.fillText(this.ropeText1, temp, textHeight);
								RT_ctx.fillText(this.ropeText1, temp, textHeight);
								TT_ctx.fillText(this.ropeText1, temp, textHeight);
							}
							if (this.ropeText2) {
								LB_ctx.fillText(this.ropeText2, temp, textHeight2);
								RB_ctx.fillText(this.ropeText2, temp, textHeight2);
								TB_ctx.fillText(this.ropeText2, temp, textHeight2);
							}
						}
						if (this.printingData && this.printingData.value.paramName == "A Line of Words 2") {
							temp += textWidth + 20 + textWidth2;
							temp2 += textWidth + 20 + textWidth2;
						} else {
							temp += textWidth + 20;
						}
					}
					//左绳
					ctx.rotate((lRo * Math.PI) / 180);
					ctx.drawImage(LT_canvas, lX, lY);
					if ((this.ropeText2 && this.printingData && this.printingData.value.paramName == "A Line of Words 2") || (this.ropeText2 && this.twoLine)) {
						ctx.drawImage(LB_canvas, lX, lY);
					}
					ctx.restore();

					//右绳
					LT_ctx.restore();
					ctx.rotate((rRo * Math.PI) / 180);
					ctx.drawImage(RT_canvas, rX, rY);

					if ((this.ropeText2 && this.printingData && this.printingData.value.paramName == "A Line of Words 2") || (this.ropeText2 && this.twoLine)) {
						ctx.drawImage(RB_canvas, rX, rY);
					}
					ctx.restore();
					if (this.canvasData && this.canvasData.doubleFont && this.canvasData.doubleFont.paramName == "Both Side") {
						ctx.rotate(0);
						ctx.drawImage(TT_canvas, 0, 32);
						if ((this.ropeText2 && this.printingData && this.printingData.value.paramName == "A Line of Words 2") || (this.ropeText2 && this.twoLine)) {
							ctx.drawImage(TB_canvas, 0, 32);
						}
					}
				}
				resolve();
			});
		},
		//裁剪
		clipFun({ context, a1, a2, b1, b2, c1, c2, d1, d2, e1, e2 }) {
			return new Promise((resolve, reject) => {
				context.save();
				context.beginPath();
				context.moveTo(0, 0);
				context.lineTo(a1, a2);
				context.lineTo(b1, b2);
				context.lineTo(c1, c2);
				context.lineTo(d1, d2);
				context.lineTo(e1, e2);
				context.clip();
				resolve();
			});
		},
		//绘制图片
		buildCanvasFun({ context, src, sx, sy, sWidth, sHeight, x, y, width, height, type = "source-over", rotate } = {}) {
			return new Promise((resolve, reject) => {
				this.imgOnloadFun(src).then((image) => {
					this.drawImageFun({
						context,
						image,
						sx,
						sy,
						sWidth,
						sHeight,
						x,
						y,
						width,
						height,
						type,
						rotate,
					}).then((res2) => {
						resolve();
					});
				});
			});
		},
		//图片加载
		imgOnloadFun(url, type) {
			return new Promise((resolve) => {
				if (!url) {
					resolve();
					return;
				}
				let image = new Image();
				if (url.indexOf("data:") != -1 && url.indexOf("base64") != -1) {
					image.src = url;
				} else {
					image.src = url;
				}
				image.onload = () => {
					if (type) {
						resolve({
							name: type,
							img: image,
						});
					} else {
						resolve(image);
					}
				};
				image.onerror = () => {
					console.log(url, image, "image load failed");
					// this.$toast.error("image load failed");
					resolve();
				};
				image.setAttribute("crossOrigin", "anonymous");
			});
		},
		//图片插入canvas0
		drawImageFun({ context, image, sx, sy, sWidth, sHeight, x, y, width, height, type, rotate } = {}) {
			return new Promise((resolve, reject) => {
				context.save();
				if (image) {
					if (rotate) {
						context.rotate((rotate * Math.PI) / 180);
					}
					context.globalCompositeOperation = type;
					if (sx || sy || sWidth || sHeight) {
						context.drawImage(image, sx, sy, sWidth, sHeight, x, y, width, height);
					} else {
						context.drawImage(image, x, y, width, height);
					}
					context.restore();
				}

				resolve();
			});
		},
		convertCanvasToImage(canvas) {
			return new Promise((resolve, reject) => {
				let userName = "canvasImg";
				let fileName = userName + ".jpg"; //vm.addUserName
				let firstName = fileName.charAt(0);
				let dataurl = canvas.toDataURL("image/png", 1.0);
				let arr = dataurl.split(","),
					mime = arr[0].match(/:(.*?);/)[1],
					bstr = atob(arr[1]),
					n = bstr.length,
					u8arr = new Uint8Array(n);
				while (n--) {
					u8arr[n] = bstr.charCodeAt(n);
				}
				let file = new File([u8arr], fileName, {
					type: mime,
				});
				// file.lastModifiedDate = new Date();
				uploadFile(file).then((res) => {
					resolve(res);
				});
			});
		},
		downFun(val, ctx) {
			return new Promise(async (resolve, reject) => {
				//镂空模板
				await this.buildCanvasFun({
					context: ctx,
					src: val,
					x: 0,
					y: 0,
					width: this.canvasData.canvasWdith,
					height: this.canvasData.canvasHeight,
				});
				//是否交叉挂绳
				if (this.canvasData.ropeCross) {
					//是否有配件B
					if (this.canvasData.buckleB1) {
						await this.buildCanvasFun({
							context: ctx,
							src: this.canvasData.buckleB1,
							x: (this.canvasData.canvasWdith - this.canvasData.buckleB1Height) / 2 - 34 + 34,
							y: this.canvasData.ropeHeight - 60,
							width: this.canvasData.buckleB1Height,
							height: this.canvasData.buckleB1Width,
						});
					}
				} else {
					if (this.canvasData.buckleB1) {
						//左配件b
						await this.buildCanvasFun({
							context: ctx,
							src: this.canvasData.buckleB1,
							x: this.canvasData.lX2 + 18,
							y: this.canvasData.ropeHeight - 70,
							width: this.canvasData.buckleB1Height,
							height: this.canvasData.buckleB1Width,
						});
						//右配件b
						await this.buildCanvasFun({
							context: ctx,
							src: this.canvasData.buckleB1,
							x: this.canvasData.rY2 - 9,
							y: this.canvasData.ropeHeight - 70,
							width: this.canvasData.buckleB1Height,
							height: this.canvasData.buckleB1Width,
						});
					}
				}
				//配件C
				if (this.canvasData.ropeCross) {
					if (this.canvasData.buckleC1) {
						await this.buildCanvasFun({
							context: ctx,
							src: this.canvasData.buckleC1,
							x: this.canvasData.buckleB1 ? (this.canvasData.canvasWdith - this.canvasData.buckleB1Height) / 2 - 45 + 34 : (this.canvasData.canvasWdith - this.canvasData.buckleB1Height) / 2 - 42.8 + 32, //修正参数
							y: this.canvasData.canvasHeight - this.canvasData.buckleC1Height - this.canvasData.defautlPaddingTop, //修正参数
							width: this.canvasData.buckleC1Width,
							height: this.canvasData.buckleC1Height,
						});
					}
				} else {
					if (this.canvasData.buckleC1_1) {
						await this.buildCanvasFun({
							context: ctx,
							src: this.canvasData.buckleC1_1,
							x: this.canvasData.lX2 + 7.5, //修正参数
							y: this.canvasData.ropeWidthType2 - this.canvasData.buckleC1Width + 30, //15修正参数
							width: this.canvasData.buckleC1Width,
							height: this.canvasData.buckleC1Height,
						});
					}
					if (this.canvasData.buckleC1_2) {
						await this.buildCanvasFun({
							context: ctx,
							src: this.canvasData.buckleC1_2,
							x: this.canvasData.lX2 + 169.5, //修正参数
							y: this.canvasData.ropeWidthType2 - this.canvasData.buckleC1Width + 30, //15修正参数
							width: this.canvasData.buckleC1Width,
							height: this.canvasData.buckleC1Height,
						});
					}
				}
				//配件A
				if (this.canvasData.buckleA1) {
					await this.buildCanvasFun({
						context: ctx,
						src: this.canvasData.buckleA1,
						x: 0 + 32,
						y: 0,
						width: this.canvasData.buckleA1Width,
						height: this.canvasData.buckleA1Height,
					});
				}
                //led织带
                if(this.canvasData.lightUpLanyard){
                    if(Array.isArray(this.canvasData.lightUpLanyard)){
                        clearTimeout(this.timed)
                        this.stopPlay = false;
                        let index = 0;
                        const drawImage = async (src)=>{
                            if(this.stopPlay){
                                return
                            }
                            await this.buildCanvasFun({
                                context: ctx,
                                src: src,
                                x: 52 + 32,
                                y: 52,
                                width: 223,
                                height: 664,
                            });
                            this.canvasToImage = document.getElementById("canvas").toDataURL("image/png", 1.0);
                            this.$emit("canvasToImage", this.canvasToImage);
                            index++;
                            if(index>this.canvasData.lightUpLanyard.length-1){
                                index = 0;
                            }
                            this.timed = setTimeout(()=>{
                                drawImage(this.canvasData.lightUpLanyard[index])
                            },1000)
                        }
                        await drawImage(this.canvasData.lightUpLanyard[0]);
                    }else{
                        this.stopPlay = true;
                        await this.buildCanvasFun({
                            context: ctx,
                            src: this.canvasData.lightUpLanyard,
                            x: 52 + 32,
                            y: 52,
                            width: 223,
                            height: 664,
                        });
                    }
                }
				resolve();
			});
		},
		//文字加粗
		BFun(type) {
			if (type == 1) {
				this.canvasData.ropeFontWeight1 = !this.canvasData.ropeFontWeight1;
			} else if (type == 2) {
				this.canvasData.ropeFontWeight2 = !this.canvasData.ropeFontWeight2;
			}
		},
		// 斜体
		obliqueFun(type) {
			if (type == 1) {
				this.canvasData.ropeFontStyle1 = !this.canvasData.ropeFontStyle1;
			} else if (type == 2) {
				this.canvasData.ropeFontStyle2 = !this.canvasData.ropeFontStyle2;
			}
		},
	},
	computed: {
		lang() {
			return this.$store.getters.lang.quote || {};
		},
		ropeText1() {
			if (this.tampInfo && this.tampInfo[0]?.paramName == "Only Logo") {
				return "";
			} else {
				return this.canvasData.ropeText1 ? this.canvasData.ropeText1 : this.copyRopeText1;
			}
		},
		ropeText2() {
			if (this.tampInfo && this.tampInfo[0]?.paramName == "Only Logo") {
				return "";
			} else {
				return this.canvasData.ropeText2 ? this.canvasData.ropeText2 : this.copyRopeText2;
			}
		},
		ropeFontFillStyle1() {
			return this.canvasData.ropeFontFillStyle1 ? this.canvasData.ropeFontFillStyle1 : this.copyRopeFontFillStyle1;
		},
		ropeFontFillStyle2() {
			return this.canvasData.ropeFontFillStyle2 ? this.canvasData.ropeFontFillStyle2 : this.copyRopeFontFillStyle2;
		},
		beforeTempLogo() {
			return this.canvasData.beforeTempLogo;
		},
		afterTempLogo() {
			return this.canvasData.afterTempLogo;
		},
		twoLine() {
			let temp = this.printingData;
			if (!temp) {
				return false;
			}
			return this.printingData.value.paramName == this.name1 || this.printingData.value.paramName == this.name2 || this.printingData.value.paramName == this.name3 || this.printingData.value.paramName == this.name4;
		},
	},
	watch: {},
	created() {},
	mounted() {},
};
</script>

<style scoped lang="scss">
.CanvasFactory {
}
</style>