<template>
	<div class="cate_item" :class="{ 'is-active': isActive || hasActiveChild }">
		<nuxt-link :to="link">{{ data.cateName }}</nuxt-link>

		<div class="cate_item__children" v-if="hasChildren">
			<GSJJMenuItem v-for="item in data.childList" :data="item" :key="item.id" />
		</div>
	</div>
</template>

<script>
const EXCLUDED_PVC_URLS = [
	"/pvc-coasters/s/pvc-coasters",
	"/pvc-lapel-pins/s/pvc-lapel-pins",
	"/pvc-labels/s/pvc-labels",
	"/pvc-zipper-pulls/s/pvc-zipper-pulls",
];

export default {
	name: "GSJJMenuItem",

	inject: ["staticPath"],

	props: {
		data: { type: Object, default: () => ({}) },
	},

	computed: {
		hasChildren() {
			return this.data.childList && this.data.childList.length > 0;
		},

		link() {
			return this.generateLink(this.data);
		},

		isActive() {
			const currentCategoryName = this.$route.params.cateName;
			const currentPageUrl = this.data.pageUrl;

			// 如果当前链接在排除列表中，直接返回false
			if (EXCLUDED_PVC_URLS.includes(this.link)) {
				return false;
			}

			// 判断当前分类名称是否匹配页面URL
			return currentCategoryName === currentPageUrl;
		},

		hasActiveChild() {
			if (!this.hasChildren) return false;

			const {
				params: { cateName },
			} = this.$route;

			return this.data.childList.some((child) => {
				const directMatch = cateName === child.pageUrl;

				if (child.childList && child.childList.length > 0) {
					return (
						directMatch ||
						child.childList.some((grandChild) => {
							return cateName === grandChild.pageUrl;
						})
					);
				}

				return directMatch;
			});
		},
	},

	methods: {
		generateLink(cate) {
			const { pageUrlPrefix, pageUrl } = cate;
			return `/${pageUrlPrefix}/${this.staticPath}/${pageUrl}`;
		},
	},
};
</script>

<style lang="scss" scoped>
.cate_item {
	a {
		display: block;
		padding-left: 12px;
		font-size: 12px;
		line-height: 26px;
	}

	a:hover {
		text-decoration: underline;
	}

	&__children {
		display: none;
		padding-left: 12px;
	}

	&.first > a {
		line-height: 34px;
		font-size: 13px;
		font-weight: 700;
	}

	&.first:not(:last-child) > a {
		border-bottom: 1px dotted #d5d5d5;
	}

	&.is-active {
		> a {
			font-weight: 700;
			color: #f60;
			background: url(https://www.gs-jj.com/static/theme/default/images/cate_menu.png) no-repeat 3px 9px;
		}

		> .cate_item__children {
			display: block;
		}

		&.first {
			> a {
				background-position: 3px 13px;
			}
		}
	}
}
</style>
