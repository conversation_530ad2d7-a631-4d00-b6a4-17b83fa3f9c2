<template>
	<div class="mb-4" :class="stepData.styleName">
	    <slot name="stepText"></slot>
		<div class="step-content">
			<div class="step-text grey--text">{{ stepData.attributeTitle }}</div>
			<div class="sizeBox">
				<div class="inputContainer">
					<!-- 自己输入价格 -->
					<div class="inputContent" :class="{ active: selectIndex == -1 }">
						<div class="customInput2">
							<div class="inputMain">
								<button @click="subNum" class="subBtn"><v-icon>mdi-minus</v-icon></button>
								<div class="inputBox" @click="selectStep2(-1)" :class="{ active: isFocused }"><input class="priceInput" @focus="isFocused = true" @blur="isFocused = false" :disabled="!stock" type="text" :placeholder="stock > 0 ? 'custom qty' : 'Sold Out'" v-model="inputNum" @keyup="getInputPrice" @change="updatePrice" /></div>
								<button @click="addNum" class="addBtn"><v-icon>mdi-plus</v-icon></button>
							</div>
							<div class="prepend2"><CCYRate :price="inputPrice"></CCYRate>&nbsp;{{ "/ " + langSemiCustom.unit }}</div>
						</div>
					</div>
				</div>
				<div class="errorTip" ref="errorTip">
					<v-alert dense outlined type="error">{{  langSemiCustom.miniQty}} {{ productInfo.lowestPurchaseQuantity || 1 }} </v-alert>
				</div>
				<div class="price-range-box">
					<v-btn icon v-show="showPriceRange">
						<v-icon @click="clearRange"> mdi-trash-can </v-icon>
					</v-btn>
					<v-slider @change="changeRange" :disabled="stock <= 0" class="custom-slider" v-model="sliderNum" thumb-label :min="0" :max="maxRange" hide-details :step="1"></v-slider>
				</div>
				<div class="price-des">
					<div><span></span>{{ minRange + " " + langSemiCustom.units }}</div>
					<div><span></span>{{ maxRange + " + " + langSemiCustom.units }}</div>
				</div>
				<div class="wrapMain">
					<div class="wrap-button-prev" @click="prevBtn">
						<v-icon>mdi-chevron-left</v-icon>
					</div>
					<div class="step-wrap">
						<div class="step-item" v-for="(step, index) in quantitySection" :key="index" :class="{ active: index === selectIndex, grayBack: stock == null || stock == 0 || parseInt(step.quantity) > parseInt(stock) }" @click="selectStep(step, index)">
							<div class="customInput">
								<div class="inputContent">
									<div class="yeah">
										<v-icon>mdi-check</v-icon>
									</div>
									<div><CCYRate :price="step.discountPrice ? step.discountPrice : step.unitPrice"></CCYRate>&nbsp;{{ langSemiCustom.each }}</div>
								</div>
								<div class="prepend">{{ step.quantitySection }}</div>
							</div>
						</div>
					</div>
					<div class="wrap-button-next" @click="nextBtn">
						<v-icon>mdi-chevron-right</v-icon>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import { debounce } from "@/utils/utils";
export default {
	inject: ["getProductInfo", "getUnitPriceStep","getCurrentPrintMethod"],
	props: {
		stepData: {
			type: Object,
			default: () => ({}),
		},
	},
	data() {
		return {
			selectIndex: -1,
			selectItem: null,
			isInput: false,
			inputNum: "",
			sliderNum: 0,
			inputPrice: "0.00",
			priceArr: [],
			// stock: this.stepData ? this.stepData.productParamList[0].stock : 9999,
			stock: -1,
			showPriceRange: false,
			minRange: 0,
			maxRange: "",
			debounceCalculatePrice: false,
			isFocused: false,
			customInput: false
		};
	},
	watch: {
		stepData: {
			deep: true,
			handler(newVal) {
				if (newVal && this.stock == -1) {
					this.stock = this.stepData.productParamList[0].stock;
				}
			},
		},
		currentPrintMethod:{
			handler(newVal,oldVal){
				this.unitPriceStep();
			}
		},
		inputNum() {
			this.getQuantityIndex(this.priceArr);
			this.getPrice(this.priceArr);
		},
	},
	computed: {
		productInfo() {
			return this.getProductInfo();
		},

		langSemiCustom() {
			return this.$store.getters.lang?.semiCustom;
		},
		quantitySection() {
			this.getPrice(this.priceArr);
			let priceArr2 = JSON.parse(JSON.stringify(this.priceArr)).map((item, index) => {
				if (index == this.priceArr.length - 1) {
					item.quantitySection = item.quantity + " +";
				} else {
					item.quantitySection = item.quantity + " - " + parseFloat(this.priceArr[index + 1].quantity - 1);
				}
				return item;
			});

			return priceArr2;
		},
		currentPrintMethod(){
			return this.getCurrentPrintMethod()
		}
	},
	methods: {
		subNum() {
			const inputBox = document.getElementsByClassName("inputBox")[0];
			const subBtn = document.getElementsByClassName("subBtn")[0];
			subBtn.classList.add("active");
			inputBox.classList.add("active");
			setTimeout(() => {
				subBtn.classList.remove("active");
				inputBox.classList.remove("active");
			}, 500);
			if (this.inputNum <= 0 || !this.inputNum) {
				this.inputNum = 0;
				return;
			}
			this.inputNum--;
			this.sliderNum = this.inputNum;
			this.updatePrice();
		},
		addNum() {
			const inputBox = document.getElementsByClassName("inputBox")[0];
			const addBtn = document.getElementsByClassName("addBtn")[0];
			addBtn.classList.add("active");
			inputBox.classList.add("active");
			setTimeout(() => {
				addBtn.classList.remove("active");
				inputBox.classList.remove("active");
			}, 500);
			if (!this.inputNum && this.inputNum != 0) this.inputNum = 0;
			if (this.inputNum >= this.stock) return;
			this.inputNum++;
			this.sliderNum = this.inputNum;
			if (parseInt(this.inputNum) > parseInt(this.maxRange)) this.maxRange = this.inputNum;
			this.updatePrice();
		},
		getInputPrice() {
			this.customInput = false;
			if (!this.inputNum || this.inputNum == 0) {
				this.inputPrice = "0.00";
				return this.inputPrice;
			}
			this.sliderNum = this.inputNum;
			if (parseInt(this.inputNum) > parseInt(this.maxRange)) this.maxRange = this.inputNum;
			this.formatNum();
			this.getPrice(this.priceArr);
		},
		getPrice(arr) {
			let len = arr.length;
			if(len == 0) return;
			let inputNum = parseInt(this.inputNum);
			if (inputNum > 0 && inputNum <= parseInt(arr[0].quantity)) {
				this.inputPrice = arr[0].discountPrice ? parseFloat(arr[0].discountPrice) : parseFloat(arr[0].unitPrice);
				return this.inputPrice;
			}
			if (inputNum >= parseInt(arr[len - 1].quantity)) {
				this.inputPrice = arr[len - 1].discountPrice ? parseFloat(arr[len - 1].discountPrice) : parseFloat(arr[len - 1].unitPrice);
				return this.inputPrice;
			}
			for (let i = 0; i < len; i++) {
				let num = arr[i];
				let num2 = arr[i + 1];
				if (!num2) break;
				if (inputNum >= parseInt(num.quantity) && inputNum < parseInt(num2.quantity)) {
					this.inputPrice = num.discountPrice ? parseFloat(num.discountPrice) : parseFloat(num.unitPrice);
					break;
				}
			}

			return this.inputPrice;
		},

		getQuantityIndex(arr) {
			let len = arr.length;
			let inputNum = parseInt(this.inputNum);

			if (inputNum > 0 && inputNum <= parseInt(arr[0].quantity)) {
				this.selectIndex = 0;
				return;
			}
			if (inputNum == 0) {
				this.selectIndex = -1;
				return;
			}
			if (inputNum == parseInt(arr[len - 1].quantity) || inputNum > parseInt(arr[len - 1].quantity)) {
				this.selectIndex = len - 1;
			}
			for (let i = 0; i < len; i++) {
				let num = arr[i];
				let num2 = arr[i + 1];
				if (!num2) break;
				if (inputNum >= parseInt(num.quantity) && inputNum < parseInt(num2.quantity)) {
					this.selectIndex = i;
					break;
				}
			}
		},

		changeRange() {
			this.inputNum = this.sliderNum;
			this.showPriceRange = true;
			this.debounceCalculatePrice();
		},

		clearRange() {
			this.sliderNum = 0;
			this.inputNum = 0;
			this.selectIndex = -1;
			this.showPriceRange = false;
		},

		updatePrice() {
			let priceInputs = document.getElementsByClassName("priceInput");
			let sum = Array.from(priceInputs).reduce((acc, input) => acc + parseInt(input.value || 0), 0);
            let errDom = this.$refs.errorTip;
			if (sum < this.productInfo.lowestPurchaseQuantity) {
				errDom.style.display = "block";
			} else {
				errDom.style.display = "none";
			}
			this.stepData.productParamList[0].inputNum = this.inputNum;
			this.$emit("updatePrice");
		},
		formatNum() {
			this.inputNum = (this.inputNum + "").replace(/[^\d]/g, "");
			if (this.stock && this.stock > 0 && this.inputNum > this.stock) {
				this.inputNum = String(this.stock);
			}
			if (this.stock <= 0) {
				this.inputNum = "";
			}
			// if (this.inputNum > 0 && this.inputNum >= this.productInfo.lowestPurchaseQuantity) {
			// 	// this.updatePrice();
			// 	setTimeout(() => {
			// 		this.updatePrice();
			// 	}, 310);
			// }
		},
		prevBtn() {
			const inputBox = document.getElementsByClassName("wrap-button-prev")[0];
			inputBox.classList.add("active");
			setTimeout(() => {
				inputBox.classList.remove("active");
			}, 500);
			let index;
			const priceArr = this.priceArr;
			if (this.selectIndex == 0 || parseInt(this.selectIndex) < 0) {
				index = priceArr.length - 1;
				this.selectStep(priceArr[index], index);
				return;
			}
			index = parseInt(this.selectIndex);
			index--;
			this.selectStep(priceArr[index], index);
		},
		nextBtn() {
			const inputBox = document.getElementsByClassName("wrap-button-next")[0];
			inputBox.classList.add("active");
			setTimeout(() => {
				inputBox.classList.remove("active");
			}, 500);
			let index;
			const priceArr = this.priceArr;
			if (this.selectIndex == priceArr.length - 1 || parseInt(this.selectIndex) < 0) {
				index = 0;
				this.selectStep(priceArr[index], index);
				return;
			}
			index = parseInt(this.selectIndex);
			index++;
			this.selectStep(priceArr[index], index);
		},
		selectStep(item, index) {
			// if (this.isInput || item.stock <= 0) {
			// 	return;
			// }
			if (Object.is(this.stock, null) || this.stock == 0 || parseInt(item.quantity) > this.stock) return;
			this.selectIndex = index;
			this.selectItem = item;
			this.inputNum = item.quantity;
			this.sliderNum = item.quantity;
			this.stepData.productParamList[0].inputNum = item.quantity;
			this.$emit("selectStep", {
				type: this.stepData.attributeFlag,
				data: this.stepData.productParamList[0],
				id: this.stepData.id,
			});
		},
		selectStep2(index) {
			// if (this.isInput || item.stock <= 0) {
			// 	return;
			// }
			this.selectIndex = index;
			if (Object.is(this.stock, null) || this.stock == 0 || this.inputNum <= 0 || !this.inputNum) return;
			this.stepData.productParamList[0].inputNum = this.inputNum;
			this.$emit("selectStep", {
				type: this.stepData.attributeFlag,
				data: this.stepData.productParamList[0],
				id: this.stepData.id,
			});
		},
		getPriceSection(price) {
			this.priceArr = price;
		},
		updateQty(type) {
			if (this.stepData && this.stepData.productParamList.length > 0 && this.stepData.productParamList[0].stock) {
				this.stock = this.stepData.productParamList[0].stock;
				if (this.customInput && type != 'addCart') this.inputNum = ''
				this.formatNum();
			}
		},
		unitPriceStep() {
			try {
				this.priceArr = this.currentPrintMethod&&JSON.parse(this.currentPrintMethod?.markupIncreasePrice);
				this.minRange = this.priceArr[0].quantity;
				this.maxRange = this.priceArr[this.priceArr.length - 1].quantity;
			} catch (error) {
				console.log(error,'style4');
			}
		},
		setPriceArr(data){
     	this.priceArr = data;
    },
		setInputNum(num) {
			this.inputNum = num
			this.customInput = true
			this.formatNum()
			this.$forceUpdate()
			this.stepData.productParamList[0].inputNum = this.inputNum;
			this.$emit("updatePrice");
		}
	},
	created() {
	},
	mounted() {
		this.debounceCalculatePrice = debounce(() => {
			this.stepData.productParamList[0].inputNum = this.inputNum;
			this.$emit("selectStep", {
				type: this.stepData.attributeFlag,
				data: this.stepData.productParamList[0],
				id: this.stepData.id,
			});
		}, 500);
		this.$Bus.$on("updateQty", this.updateQty);
		this.$Bus.$on("priceSection", this.getPriceSection);
		this.$Bus.$on("priceNum", this.setPriceArr);
		this.$Bus.$on("selectDefaultSizeStep",this.setInputNum)
	},
	beforeDestroy() {
		this.$Bus.$off("updateQty", this.updateQty);
		this.$Bus.$off("priceSection", this.getPriceSection);
		this.$Bus.$off("priceNum", this.setPriceArr);
		this.$Bus.$off("selectDefaultSizeStep",this.setInputNum)
	},
};
</script>

<style scoped lang="scss">
@import "~assets/css/half.scss";
input[disabled] {
	background: rgba(0, 0, 0, 0.03);
}

.grayBack {
	background-color: #a7a7a7;
	filter: grayscale(1);
	border-radius: 6px;
}
.step-content {
	display: grid;
	grid-template-columns: 100px 1fr;
	grid-gap: 10px;
	.sizeBox {
		.inputContainer {
			margin-bottom: 10px;
			.inputContent {
				.customInput2 {
					display: grid;
					grid-template-columns: 195px 1fr;
					align-items: center;
					grid-gap: 10px;
					.inputMain {
						display: flex;
						align-items: center;
						justify-content: space-between;
						.subBtn {
							padding: 6px;
							cursor: pointer;
						}
						.addBtn {
							padding: 6px;
							cursor: pointer;
						}
						.subBtn.active,
						.addBtn.active {
							.v-icon {
								color: $color-primary;
							}
						}
						.inputBox {
							border: 1px solid #c8c3c3;
							margin: 0 10px;
							font-size: 14px;
							&.active {
								border: 2px solid $color-primary;
							}
							input {
								line-height: 20px;
								padding: 8px 6px;
								max-width: 100%;
								min-width: 0;
								width: 100%;
								text-align: center;
							}
							input::placeholder {
								font-size: 12px;
							}
						}
					}
					.prepend2 {
						align-self: end;
						font-size: 12px;
					}
				}
			}
		}
		.price-range-box {
			padding: 0 10px;
			text-align: right;

			i {
				font-size: 16px;
				color: #666666;
				margin-right: -8px;
				cursor: pointer;
			}

			::v-deep .custom-slider .v-slider__track-background {
				background-color: $color-second !important; /* 设置轨道背景颜色 */
			}

			// ::v-deep .custom-slider .v-slider__thumb {
			// 	background-color: $color-second !important; /* 设置滑动块滑动后的颜色 */
			// }
			::v-deep .custom-slider .v-slider__track-fill {
				background-color: $color-primary !important; /* 设置你想要的背景颜色 */
			}

			::v-deep .custom-slider .v-slider__track-container {
				height: 6px; /* 设置轨道线的高度 */
				border-radius: 10px !important;
			}
		}

		.price-des {
			display: flex;
			justify-content: space-between;
			align-items: center;
			font-size: 14px;
			padding: 0 18px;
			margin-bottom: 10px;
		}

		.wrapMain {
			display: flex;
			align-items: center;
			justify-content: space-between;
			.wrap-button-prev {
				cursor: pointer;
				padding: 4px;
				margin-right: 4px;
				&.active {
					.v-icon {
						color: $color-primary;
					}
				}
				.v-icon {
					font-size: 40px;
				}
			}
			.wrap-button-next {
				cursor: pointer;
				padding: 4px;
				margin-left: 4px;
				&.active {
					.v-icon {
						color: $color-primary;
					}
				}
				.v-icon {
					font-size: 40px;
				}
			}
			.step-wrap {
				display: grid;
				grid-gap: 10px;
				width: 100%;
				.step-item {
					overflow: hidden;
				}
			}
		}
	}

	@include respond-to(mb) {
		grid-template-columns: 1fr;
		grid-gap: 5px;

		.step-wrap {
			grid-gap: 5px;

			.step-item {
				overflow: hidden;
			}
		}
	}
}

.style1 .step-content {
	.step-wrap {
		grid-template-columns: repeat(4, 1fr);
		@include respond-to(pad) {
			grid-template-columns: repeat(3, 1fr);
		}
		@include respond-to(mb) {
			grid-template-columns: repeat(3, 1fr);
		}
		.step-item.active {
			.customInput {
				.inputContent {
					position: relative;
					color: $color-primary;
					border-color: $color-primary;
					border-width: 2px;
					.yeah {
						display: block;
						position: absolute;
						right: 0;
						top: 0;
						width: 14px;
						height: 14px;
						border-radius: 2px;
						background-color: $color-primary;
						.v-icon {
							font-size: 12px;
							color: #fff;
							position: absolute;
							top: 50%;
							left: 50%;
							transform: translate(-50%, -50%);
						}
					}
				}
				.prepend {
					color: $color-primary;
					// background-color: $color-primary;
					border-color: $color-primary;
					border-width: 2px;
				}
			}
		}

		.customInput {
			display: flex;
			flex-direction: column;

			.inputContent {
				display: flex;
				justify-content: center;
				align-items: flex-end;
				height: 33px;
				border-radius: 6px 6px 0 0;
				border: 1px solid #cccccc;
				border-bottom: 0;
				.yeah {
					display: none;
				}
			}

			.prepend {
				display: flex;
				justify-content: center;
				align-items: flex-start;
				flex: 0 0 auto;
				// width: 180px;
				word-break: break-word;
				padding: 6px;
				// background-color: $background-color2;
				transition: background-color 0.3s;
				text-align: center;
				border-bottom: 1px solid #cccccc;
				border-left: 1px solid #cccccc;
				border-right: 1px solid #cccccc;
				border-top: none;
				border-radius: 0 0 6px 6px;
				// color: $color-primary;
			}

			.hint-text {
				margin-top: 4px;
				color: $gray-text;
				text-align: right;
				font-size: 14px;
			}
		}
	}
	@include respond-to(mb) {
		.step-wrap {
			.customInput {
				.inputContent {
					.prepend {
						flex: 0 0 70px;
						word-break: break-word;
						padding: 0;
					}

					input {
						flex: 1 1 auto;
						line-height: 20px;
						padding: 8px;
						max-width: 100%;
						min-width: 0;
						width: 100%;
					}
				}

				.hint-text {
					color: $gray-text;
					text-align: right;
					font-size: 14px;
				}
			}
		}
	}
}
</style>
