<template>
	<div class="summary-box" :class="modal.wrapClass" :style="modal.style"
		@click.self="setModalType({}, modal.list, 'sticker_crowd')">
		<div class="bps-container">
			<EditDiv tag-name="h1" v-model:content="modal.outer.title.value"
				@click="setModalType(modal.outer.title, '', 'text')" />
			<div class="content">
				<div class="item" v-for="(item, index) in modal.list" :key="index"
					@click="setModalType(item.product_crowd, modal.list, 'sticker_crowd')">
					<div @click="linkPage(item)">
						<div class="item_img" v-if="item.product_crowd.img.value">
							<pic class="img1" :src="item.product_crowd.img.value" :alt="item.product_crowd.alt.value" />
							<pic class="img2" :src="item.product_crowd.img2.value" :alt="item.product_crowd.alt2.value" />
						</div>
						<div class="item_div">
							<div>
								<p>{{ item.product_crowd.title.value }}</p>
								<p>{{ item.product_crowd.text.value }}</p>
								<div class="btnWrap">
									<a href="javascript:;" class="default-button bps-button">
										{{ modal.list[index].product_crowd.button.value }}
									</a>
								</div>
								<div class="price">
									<span>{{ item.product_crowd.subTitle.value }}</span>
									<strong>
										<CCYRate :price="item.product_crowd.price.value"></CCYRate>
									</strong>
									<span>ea.</span>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	props: {
		data: {
			type: Object,
			default: {},
		}
	},
	data() {
		return {
			modal: {
				style: {},
				type: { index: 0, clickPosition: 'outer' },
				...this.data
			}
		};
	},
	watch: {
		modal: {
			handler(val) {
				if (process.env.isManage) this.$emit("update:data", val);
			},
			immediate: true,
			deep: true
		}
	},
	methods: {
		setModalType(target, targetArray, clickType, event, other) {
			this.$setModal(this, target, targetArray, clickType, event, other)
		},
		linkPage(l) {
			if (process.env.isManage) {
				return false;
			}
			this.$router.push({
				path: `${l.product_crowd.link.value}`
			})
		}
	}
};
</script>

<style lang="scss" scoped>
.stickerList {
	padding: 70px 0;

	h1 {
		width: 100%;
		text-align: center;
		margin-bottom: 55px;
		font-size: 36px;
		font-weight: bold;
		color: #333333;
	}

	.bps-container {
		.content {
			display: grid;
			grid-template-columns: repeat(4, 1fr);
			grid-row-gap: 50px;
			grid-column-gap: 42px;
		}

		.item {
			cursor: pointer;
			text-align: center;

			.item_img {
				width: 100%;
				height: 280px;

				img {
					width: 100%;
					height: 100%;
					object-fit: cover;
				}

				.img2 {
					display: none;
				}
			}

			.item_div {
				padding: 10px;
			}

			.btnWrap {
				display: none;
				margin: 0 auto;
				width: 200px;
				height: 35px;

				a {
					border-radius: 18px;
					height: 35px;
				}
			}

			.price {
				height: 35px;
				font-size: 14px;

				strong {
					font-size: 18px;
					font-weight: 700;
				}
			}

			@media (any-hover: hover) {
				&:hover {
					.item_div {
						background-color: #E8F4FF;
					}

					.img1 {
						display: none;
					}

					.img2 {
						display: inline-block;
					}

					.btnWrap {
						display: block;
					}

					.price {
						display: none;
					}
				}
			}
		}

		div {
			div {
				p {
					text-align: center;

					&:nth-child(1) {
						font-size: 18px;
						font-weight: bold;
					}

					&:nth-child(2) {
						font-size: 14px;
						font-weight: 400;
						color: #999999;
						margin: 9px 0 20px 0;
						min-height: 55px;
					}
				}
			}
		}
	}
}

@media screen and (max-width: $mb-width) {
	.stickerList {
		padding: 27px 0;

		h1 {
			font-size: 21px;
		}

		.bps-container {
			.content {
				grid-template-columns: repeat(2, 1fr) !important;
				grid-gap: 10px;
			}

			.item {
				.item_img {
					width: 100%;
					height: 150px;

					img {
						object-fit: contain;
					}
				}

				.price {
					height: auto;
					font-size: 12px;

					strong {
						font-size: 14px;
					}
				}
			}

			div {
				div {
					p {
						text-align: center;

						&:nth-child(1) {
							font-size: 14px;
							font-weight: bold;
						}

						&:nth-child(2) {
							font-size: 12px;
							color: #999999;
							margin: 10px 0;
						}
					}
				}
			}
		}
	}
}
</style>
