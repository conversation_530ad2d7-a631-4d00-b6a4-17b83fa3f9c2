<template>
	<div class="summary-box" :class="modal.wrapClass">
		<div class="sticker-container" v-for="(o, oi) in modal.outer" :key="oi">
			<EditDiv tag-name="h2" v-model:content="modal.outer[0].title.value" @click="setModalType(o.title,modal.outer,'text')" />
			<EditDiv class="des sticker_p" v-model:content="modal.outer[0].subTitle.value" @click="setModalType(o.subTitle,modal.outer,'text')" />
			<div class="video" v-if="modal.outer[0].video">
				<video :src="modal.outer[0].video.value"  autoplay loop muted playsinline></video>
			</div>
			<div class="video" v-else>
				<pic :src="modal.outer[0].img.value" :alt="modal.outer[0].img.alt"  :title="modal.outer[0].img.alt" @click="setModalType(modal.outer[0].img,modal.outer,'img')" />
			</div>
			<div class="sticker2_bottom">
				<div class="border"></div>
				<div class="bottom">
					<div class="bottom_grid">
						<div v-for="(item,index) in o.list" :key="index">
							<pic :src="item.img.value" :alt="item.img.alt"  :title="item.img.alt" @click="setModalType(item.img.value,modal.outer,'img')" />
							<EditDiv tag-name="h3" v-model:content="item.title.value" @click="setModalType(item.title,modal.outer,'text')" />
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	props: {
		data: {
			type: Object,
			default: {},
		}
	},
	data() {
		return {
			modal: {
				style: {},
				type: {index: 0, clickPosition: 'outer'},
				...this.data
			}
		};
	},
	watch: {
		modal: {
			handler(val) {
				if (process.env.isManage) this.$emit("update:data", val);
			},
			immediate: true,
			deep: true
		}
	},
	methods:{
		setModalType(target, targetArray, clickType, event, other) {
			this.$setModal(this, target, targetArray, clickType, event, other)
		},
	}
};
</script>

<style lang="scss" scoped>
	.summary-box {
		clear: both;
		overflow: hidden;
		.sticker-container {
			width: 90%;
			float: right;
			text-align: center;
			position: relative;
			padding-bottom: 300px;
			h2 {
				font-size: 42px;
			}
			.sticker_p {
				font-size: 18px;
				margin-top: 26px;
			}
			.video {
				position: relative;
				video,img {
					width: 1024px;
					height: 576px;
					margin-top: 56px;
					position: relative;
					z-index: 11;
				}
				&::after,&::before {
					z-index: 10;
					position: absolute;
					content: "";
					bottom: 20px;
					left: 25%;
					width: 50%;
					top: 80%;
					max-width:300px;
					background: #777;
					-webkit-box-shadow: 0 35px 20px #777;
					-moz-box-shadow: 0 35px 20px #777;
					box-shadow: 0 35px 20px #777;
					-webkit-transform: rotate(-8deg);
					-moz-transform: rotate(-8deg);
					-o-transform: rotate(-8deg);
					-ms-transform: rotate(-8deg);
					transform: rotate(-8deg);
				}
				&::after {
					-webkit-transform: rotate(8deg);
					-moz-transform: rotate(8deg);
					-o-transform: rotate(8deg);
					-ms-transform: rotate(8deg);
					transform: rotate(8deg);
					right: 25%;
					left: auto;
				}
			}
			.sticker2_bottom {
				width: 100%;
				position: absolute;
				bottom: 4%;
				.border {
					height: 150px;
					background: #F7F7F7;
					border-bottom: 3px solid #F7F7F7;
					border-right: 20px solid #F7F7F7;
					transform-origin: bottom left;
					-ms-transform: skew(-30deg, 0deg);
					-webkit-transform: skew(-30deg, 0deg);
					transform: skew(-60deg, 0deg);
				}
				.bottom {
					width: 100%;
					height: 196px;
					background: linear-gradient(90deg, #F0F0F0 0%, #E7E7E7 100%);
					.bottom_grid {
						width: 70%;
						margin: auto;
						display: grid;
						grid-template-columns: repeat(3, 1fr);
						div {
							margin-top: 40px;
							text-align: left;
							img {
								width: 35px;
								height: 35px;
								display: inline-block;
							}
							h3 {
								display: inline-block;
								font-size: 16px;
								font-weight: 400;
								text-align: left;
								margin-left: 10px;
								vertical-align: middle;
							}
						}
					}
				}
			}
		}
	}
	@media screen and (max-width: $mb-width) {
		.summary-box {
			.sticker-container {
				width: 100%;
				padding-bottom: 0;
				h2 {
					font-size: 21px;
					padding:0 15%;
				}
				.sticker_p {
					font-size: 12px;
					padding: 0 5%;
				}
				.video {
					padding: 0 10px;
					video,img {
						width: 100% !important;
						height: 200px;
						margin-top: 28px;
					}
					&::after,&::before {
						bottom: 35px;
					}
					&::after {
					  right: 5% !important;
					}
					&::before {
						left: 5% !important;
					}
				}
				.sticker2_bottom {
					position: inherit;
					.border {
						display: none;
					}
					.bottom {
						margin-top: 25px;
						height: auto;
						padding-bottom: 20px;
						.bottom_grid {
							width: 90% !important;
							div {
								margin-top: 20px;
								text-align: center;
								h3 {
									width: auto;
									font-size: 12px;
									margin-left: 0;
									text-align: center;
									display: block;
									margin-top: 10px;
									vertical-align: middle;
								}
								img {
									width: 30px;
									height: 30px;
									vertical-align:middle;
								}
							}
						}
					}
				}
			}
		}
	}
</style>
