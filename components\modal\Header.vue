<template>
	<div :theme="modal.theme" :class="modal.class" id="modalHeader">
		<div flex class="modal-box tips" v-for="(o, oi) in modal.outer" :key="oi"
			:style="{ ...modal.boxStyle, ...o.style }">

			<div flex v-if="o.navList" class="link-box" :style="{ ...modal.navListStyle, ...o.navListStyle }"
				@click="setModalType(o.navList, modal.outer, 'img_list')">
				<div flex v-for="n in o.navList" :title="n.value" :key="n.value">
					<b :class="n.icon" v-if="n.icon"></b>
					<label v-if="!n.url" v-html="n.value"></label>
					<n-link tag="a" v-else-if="n.url.startsWith('/')" :to="n.url" v-html="n.value" pointer></n-link>
					<a v-else :href="n.url" :target="n.target" v-html="n.value"></a>
				</div>
			</div>

			<div v-if="o.logo" pointer class="logo-box" :style="modal.logoBoxStyle"
				@click="setModalType({}, {}, '', { url: '/' })">
				<pic :src="proSystem.logo" :alt="$store.state.logoAltTitle || $store.state.proName"
					:style="modal.logoStyle" />
			</div>

			<div v-if="o.phone?.value" pointer flex class="phone" :title="o.phone.alt" :style="modal.phoneStyle"
				@click="setModalType(o.phone, modal.outer, 'text', { url: 'tel:' + phoneReg(o.phone.value) })">
				<b :class="o.phone.icon" v-show="o.phone.icon"></b><span>{{ lang.phonePc }}:</span>
				<!-- <a :href="'tel:' + phoneReg(o.phone.value)"> -->
				<strong hidden>{{ lang.phoneMb }}</strong> <span v-html="o.phone.value"></span>
				<!-- </a> -->
			</div>
			<div v-if="o.email?.value" pointer flex class="email" :title="o.email.alt" :style="modal.emailStyle"
				@click="setModalType(o.email, modal.outer, 'text', { url: 'mailto:' + emailReg(o.email.value) })">
				<b :class="o.email.icon" v-show="o.email.icon"></b><span>{{ lang.emailPc }}:</span>
				<!-- <a :href="'mailto:' + emailReg(o.email.value)"> -->
				<strong hidden>{{ lang.emailMb }}</strong><span v-html="o.email.value"></span>
				<!-- </a> -->
			</div>

			<pic v-if="o.img" :src="o.img.value" :alt="o.img.alt" :style="modal.imgStyle"
				@click="setModalType(o.img, modal.outer, 'img')" />

			<EditDiv v-if="o.title?.value" v-model:content="o.title.value" :style="modal.titleStyle"
				@click="setModalType(o.title, modal.outer, 'text')" />


			<div v-if="o.system" flex center class="system-box" :style="modal.systemStyle">
				<template v-if="o.login">
					<div class="user-box options-box" v-if="$store.state.token" :title="lang.userCenter">
						<b v-if="o.user" :class="o.user"></b>
						<span>
							<template v-if="o.login.welcome">{{ lang.welcome }}</template>
							<template v-if="o.login.firstName">{{ $store.state.userInfo.firstName }}</template>
						</span>
						<b v-if="o.login.arrow" :class="o.login.arrow" class="arrow"></b>

						<div class="options">
							<strong class="user-title" v-show="o.user">
								{{ lang.hi }}, {{ $store.state.userInfo.firstName }}
							</strong>

							<div class="user-sub-title" v-show="o.user">{{ lang.track }}</div>
							<a href="/user/account/orders" :select="path == '/user/account/orders'">
								<b class="icon-bps-MyOrder" v-show="o.user"></b>
								{{ lang.orders }}
							</a>
							<a href="/user/account/favorite" :select="path == '/user/account/favorite'">
								<b class="icon-shoucang" v-show="o.user"></b>
								{{ lang.favorite }}
							</a>
							<a href="/user/account/profile" :select="path == '/user/account/profile'">
								<b class="icon-bps-MyProfile" v-show="o.user"></b>
								{{ lang.profile }}
							</a>
							<a href="/user/account/address" :select="path == '/user/account/address'">
								<b class="icon-bps-MyAddress" v-show="o.user"></b>
								{{ lang.address }}
							</a>
							<a href="/user/account/coupons-vouchers" :select="path == '/user/account/coupons-vouchers'">
								<b class="icon-jxsqt-cpy-jgpy" v-show="o.user"></b>
								{{ lang.coupons }}
							</a>
							<button class="logout" outline v-if="o.user" @click="logOut">{{ lang.logOut }}</button>
							<a v-else @click="logOut">{{ lang.logOut }}</a>
						</div>
					</div>

					<div v-else pointer class="login-box" :title="lang.login" :style="o.loginStyle"
						@click="$store.commit('setMask', 'login')">
						<b :class="o.login.icon"></b> <span>{{ o.login.value }}</span>
					</div>
				</template>

				<div v-if="o.lang" class="lang-box options-box" @click="showLang = !showLang">
					<pic pointer class="lang-img" :src="$store.state.language.img"
						:alt="$store.state.language.countryCode" />
					<span>{{ $store.state.language.language || 'EN' }}</span>
					<Language class="options" :hidden="!showLang"></Language>
				</div>

				<div v-if="o.ccy" class="ccy-box options-box" :style="modal.ccyStyle">
					<span v-show="o.ccySymbol">{{ $store.state.currency.symbol || '$' }}</span>
					<span>{{ $store.state.currency.code || 'USD' }}</span>
					<div scrollbar class="options">
						<a v-for="i in $store.state.currencyList" :select="i.id == $store.state.currency.id"
							@click="$store.commit('setCurrency', i)">{{ i.code }}</a>
					</div>
				</div>

				<n-link tag="a" v-if="o.cart" class="cart-box" to="/cart" :title="lang.cartTitle">
					<b :class="o.cart">
						<label v-show="$store.state.cartQuantity">{{ $store.state.cartQuantity }}</label>
					</b>
					<CCYRate v-show="o.cartAmount" :price="$store.state.cartTotalAmount"></CCYRate>
				</n-link>
			</div>

			<button v-if="o.button?.value" :title="o.button.alt" :style="modal.btnStyle">
				<EditDiv v-show="o.button.value" tagName="label" v-model:content="o.button.value"
					@click="setModalType(o.button, modal.outer, 'button', o.button)" />
				<b :class="o.button.icon" v-if="o.button.icon"></b>
			</button>
		</div>



		<header flex class="modal-box nav" :style="modal.style" v-for="(l, li) in modal.list" :key="li + 9">
			<div v-if="l.menu" class="menu-box" hidden @click="showMenu = !showMenu">
				<b :class="l.menu?.icon">{{ l.menu?.value }}</b>
			</div>

			<div v-if="l.logo" pointer class="logo-box" :style="modal.logoBoxStyle"
				@click="setModalType({}, {}, '', { url: '/' })">
				<pic :src="proSystem.logo" :alt="$store.state.logoAltTitle || $store.state.proName"
					:style="modal.logoStyle" />
			</div>

			<div flex class="nav-box" @click="setModalType({}, {}, 'navigation')" :style="modal.navBoxStyle">
				<div v-for="a in navList" class="options-box" :key="a.id" :select="path == a.linkUrl"
					:class="{ hot: a.isHot, product: a.isProducts || a.children.length > 10, category: a.children.find(i => i.children.length) }"
					:expand="!a.isEnable || !!a.isExpand">
					<n-link tag="a" :title="a.name" :to="$rectifyLink(a.linkUrl) || ''">{{ a.name }}</n-link>
					<b class="icon-Down arrow" v-show="a.children.length"
						@click.self="a.isExpand ? a.isExpand = false : a.isEnable = !a.isEnable"></b>

					<div class="options" v-show="a.children.length || a.isHot">
						<modalCustom v-if="a.isHot"
							:data="{ ...a, id: 'hot', api: 'getHotProductList', pageSize: 5, column: 5, linkType: 'dialog' }">
						</modalCustom>

						<div v-for="b in a.children" class="options-box" :key="b.id" :select="path == b.linkUrl"
							:expand="!b.isEnable">
							<n-link tag="a" :to="$rectifyLink(b.linkUrl) || ''" :title="b.name"
								:style="{ color: b.nameColor }">{{ b.name }}
								<span :style="{ background: b.labelColor }">{{ b.labelText }}</span>
							</n-link>
							<b class="icon-Down arrow" v-show="b.children.length"
								@click.self="b.isEnable = !b.isEnable"></b>

							<div class="options" v-show="b.children.length">
								<div v-for="c in b.children" class="options-box" :key="c.id" :select="path == c.linkUrl"
									:expand="!c.isEnable">
									<n-link tag="a" :to="$rectifyLink(c.linkUrl) || ''" :title="c.name">{{ c.name }}
										<span :style="{ background: c.labelColor }">{{ c.labelText }}</span>
									</n-link>
									<b class="icon-Down arrow" v-show="c.children.length"
										@click.self="c.isEnable = !c.isEnable"></b>

									<div class="options" v-show="c.children.length">
										<div v-for="d in c.children" class="options-box" :key="d.id"
											:select="path == d.linkUrl" :expand="!d.isEnable">
											<n-link tag="a" :to="$rectifyLink(d.linkUrl) || ''" :title="d.name">
												{{ d.name }}
												<span :style="{ background: d.labelColor }">{{ d.labelText }}</span>
											</n-link>
											<b class="icon-Down arrow" v-show="d.children.length"
												@click.self="d.isEnable = !d.isEnable"></b>

											<div class="options" v-show="d.children.length">
												<div v-for="e in d.children" class="options-box" :key="e.id"
													:select="path == e.linkUrl" :title="e.name">
													<n-link tag="a" :to="$rectifyLink(e.linkUrl) || ''">{{ e.name }}
														<span :style="{ background: e.labelColor }">
															{{ e.labelText }}</span>
													</n-link>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>


			<button primary v-if="l.button?.value" :title="l.button.alt" :style="modal.btnStyle">
				<EditDiv v-show="l.button.value" tagName="label" v-model:content="l.button.value"
					@click="setModalType(l.button, modal.list, 'button', l.button)" />
				<b :class="l.button.icon" v-if="l.button.icon" :style="l.button.iconStyle"></b>
			</button>


			<div v-if="l.system" flex center class="system-box" :style="modal.systemStyle">
				<template v-if="l.login">
					<div class="user-box options-box" v-if="$store.state.token" :title="lang.userCenter"
						:style="modal.loginStyle">
						<b v-if="l.user" :class="l.user"></b>
						<template v-else>
							<span>{{ lang.welcome }} {{ $store.state.userInfo.firstName }}</span>
							<b class="icon-Down arrow"></b>
						</template>

						<div class="options">
							<strong class="user-title" v-show="l.user">
								{{ lang.hi }}, {{ $store.state.userInfo.firstName }}
							</strong>

							<div class="user-sub-title" v-show="l.user">{{ lang.track }}</div>
							<a href="/user/account/orders" :select="path == '/user/account/orders'">
								<b class="icon-bps-MyOrder" v-show="l.user"></b>
								{{ lang.orders }}
							</a>
							<a href="/user/account/favorite" :select="path == '/user/account/favorite'">
								<b class="icon-shoucang" v-show="l.user"></b>
								{{ lang.favorite }}
							</a>
							<a href="/user/account/profile" :select="path == '/user/account/profile'">
								<b class="icon-bps-MyProfile" v-show="l.user"></b>
								{{ lang.profile }}
							</a>
							<a href="/user/account/address" :select="path == '/user/account/address'">
								<b class="icon-bps-MyAddress" v-show="l.user"></b>
								{{ lang.address }}
							</a>
							<a href="/user/account/coupons-vouchers" :select="path == '/user/account/coupons-vouchers'">
								<b class="icon-jxsqt-cpy-jgpy" v-show="l.user"></b>
								{{ lang.coupons }}
							</a>
							<button class="logout" outline v-if="l.user" @click="logOut">{{ lang.logOut }}</button>
							<a v-else @click="logOut">{{ lang.logOut }}</a>
						</div>
					</div>

					<div v-else pointer class="login-box" :title="lang.login" @click="$store.commit('setMask', 'login')"
						:style="{ ...modal.loginStyle, ...l.loginStyle, ...l.login.style }">
						<b :class="l.login.icon"></b> <span>{{ l.login.value || '' }}</span>
					</div>
				</template>


				<div v-if="l.lang" class="lang-box options-box" @click="showLang = !showLang">
					<pic pointer class="lang-img" :src="$store.state.language.img"
						:alt="$store.state.language.countryCode" />
					<span>{{ $store.state.language.language || 'en' }}</span>
					<Language class="options" :hidden="!showLang"></Language>
				</div>


				<div v-if="l.ccy" class="ccy-box options-box" :style="modal.ccyStyle">
					<span v-show="l.ccySymbol">{{ $store.state.currency.symbol || '$' }}</span>
					<span>{{ $store.state.currency.code || 'USD' }}</span>
					<div scrollbar class="options">
						<span v-for="i in $store.state.currencyList" :select="i.id == $store.state.currency.id"
							@click="$store.commit('setCurrency', i)">{{ i.code }}</span>
					</div>
				</div>


				<n-link tag="a" v-if="l.cart" class="cart-box" to="/cart" :title="lang.cartTitle">
					<b :class="l.cart">
						<label v-show="$store.state.cartQuantity">{{ $store.state.cartQuantity }}</label>
					</b>
					<CCYRate v-show="l.cartAmount" :price="$store.state.cartTotalAmount"></CCYRate>
				</n-link>
			</div>


			<div v-if="l.img" class="float-btn">
				<pic pointer @click="setModalType(l.img, modal.list, 'img', { type: 'showTipImg' })" :src="l.img.value"
					:alt="l.img.alt" />
			</div>
		</header>



		<div hidden v-show="showMenu" class="menu" @click.self="showMenu = !showMenu">
			<div flex class="contact-box"></div>
			<div flex class="system-box">
				<div class="ccy-box options-box">
					<!-- <b class="icon-bps-guojia"></b> -->
					{{ $store.state.currency.code }} ({{ $store.state.currency.symbol }})
					<b class="icon-Down arrow"></b>

					<div scrollbar class="options">
						<span v-for="i in $store.state.currencyList" :select="i.id == $store.state.currency.id"
							@click="$store.commit('setCurrency', i)">{{ i.code }}</span>
					</div>
				</div>

				<div class="lang-box options-box">
					<pic class="lang-img" :src="$store.state.language.img" :alt="$store.state.language.countryCode" />
					{{ $store.state.language.language }}
					<Language class="options"></Language>
				</div>
			</div>


			<div class="user-box" v-if="$store.state.token">
				<pic class="avatar" :src="$store.state.userInfo.picPath || '/img/avatar.png'" />
				<div>{{ lang.welcome }} {{ $store.state.userInfo.firstName }} {{ $store.state.userInfo.lastName }}</div>
				<button primary @click="logOut">{{ lang.logOut }}</button>
			</div>
			<div class="user-box" v-else>
				<pic class="avatar" src="/img/avatar.png" :alt="lang.userCenter" :title="lang.userCenter" />
				<button primary @click="$store.commit('setMask', 'login')">
					{{ lang.signOrJoin }}
				</button>
			</div>
		</div>
	</div>
</template>



<script>
import { getNavList } from "@/api/web.js";
export default {
	name: "modalHeader",
	props: {
		data: {
			type: Object,
			default: () => {
				return {}
			}
		}
	},
	async fetch() {
		let res = await getNavList();
		this.navList = res.data;
	},
	data() {
		return {
			modal: {
				type: {},
				outer: [],
				list: [{}],
				...this.data,
			},
			navList: [],
			showMenu: false,
			showProduct: true,
			showLang: false,
			phoneRegex: /(?:[-+() ]*\d){10,13}/g,
			emailRegex: /(?:[a-z0-9+!#$%&'*+/=?^_"{|}~-]+(?:\.[a-z0-9!#$%&'*+/=?^_"{|}~-]+)*|"(?:[\x01-\x08\x0b\x0c\x0e-\x1f\x21\x23-\x5b\x5d-\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])*")@(?:(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?|\[(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?|[a-z0-9-]*[a-z0-9]:(?:[\x01-\x08\x0b\x0c\x0e-\x1f\x21-\x5a\x53-\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])+)\])/gi
		};
	},
	computed: {
		lang() {
			return this.$store.getters.lang.layout || {};
		},
		path() {
			return this.$store.state.pagePath;
		},
		proSystem() {
			return this.$store.state.proSystem;
		},
	},
	watch: {
		modal: {
			handler(val) {
				if (process.env.isManage) this.$emit("update:data", val);
				if (val.theme) this.$store.commit('setProSystem', { proTheme: val.theme })
			},
			immediate: true,
			deep: true
		},
		path() {
			this.showMenu = false;
		}
	},
	mounted() {
		if (process.browser) {
			// 手机端把电话邮箱和导航移到侧边栏
			if (this.$store.getters.isMobile && this.modal.list[0]?.menu) {
				// if (document.querySelector('.contact-box')?.childElementCount < 1 && document.querySelector('.tips .phone') && document.querySelector('.tips .email')) {
				// 	document.querySelector('.contact-box').appendChild(document.querySelector('.tips .phone'));
				// 	document.querySelector('.contact-box').appendChild(document.querySelector('.tips .email'));
				// }
				if (document.querySelector('.menu')?.childElementCount < 4 && document.querySelector('.nav .nav-box')) {
					document.querySelector('.menu').appendChild(document.querySelector('.nav .nav-box'));
				}
			}

			// 点击页面关闭语言框
			document.addEventListener('click', ({ target }) => {
				if (!target && !target.className && !target.className.includes('lang-box') && !target.parentElement.className.includes('lang-box')) return (this.showLang = false)
			});

			// 悬浮折扣图进入页面2秒后隐藏
			if (this.modal.class?.includes('img-float')) setTimeout(() => {
				document.querySelector('.tips').style.display = 'flex';
				document.querySelector('.float-btn').style.display = 'none';
			}, 2000);


			if (this.modal.class?.includes('img-float') || this.modal.class?.includes('scroll-bg-white')) document.addEventListener('scroll', () => {
				// 悬浮折扣图滚动时显示隐藏
				if (this.modal.class?.includes('img-float')) {
					document.querySelector('.tips').style.display = 'none';
					document.querySelector('.float-btn').style.display = 'block';
				}

				// 透明页头滚动时增加背景色
				if (this.modal.class?.includes('scroll-bg-white')) document.querySelector('.scroll-bg-white').style.background = 'rgba(255,255,255,' + (window.scrollY / 300) + ')';
			})
		}
	},
	methods: {
		// 处理邮箱字符串
		emailReg(str) {
			if (!str) return '';
			let emailStr = str.replace(/<\/?[^>]*>/g, "").replace(/&nbsp;/ig, '').replace(/\s+/g, ''); //去掉标签 去掉&nbsp 去掉所有空格
			return emailStr.match(this.emailRegex) ? emailStr.match(this.emailRegex)[0] : "";
		},
		// 处理手机字符串
		phoneReg(str) {
			if (!str) return '';
			let phoneStr = str.replace(/<\/?[^>]*>/g, "").replace(/&nbsp;/ig, ''); //去掉标签 去掉&nbsp；
			return phoneStr.match(this.phoneRegex) ? phoneStr.match(this.phoneRegex)[0] : "";
		},
		setModalType(target, targetArray, clickType, event, other) {
			this.$setModal(this, target, targetArray, clickType, event, other)
		},
		logOut() {
			this.$toast.show(this.lang.confirmLogOut, {
				containerClass: "toast-action-box",
				className: "toast-action",
				duration: null, // 不自动关闭弹窗
				action: [
					{
						text: this.lang.sure,
						onClick: (e, toastObject) => {
							this.$store.commit("logOut");
							toastObject.goAway(0);
						},
					},
					{
						text: this.lang.cancel,
						onClick: (e, toastObject) => toastObject.goAway(0), // 关闭弹窗
					},
				],
			});
		}
	},
};
</script>



<style lang="scss" scoped>
[primary],
[outline] {
	min-width: auto !important;
}

.options-box {
	display: flex;
	position: relative;
	align-items: center;

	&:not(.lang-box) .options {
		display: none;
	}

	&[select]>*:not(.options),
	[select] {
		font-weight: bold;
		color: $color-primary;
	}

	&:hover {

		&:not(.system-box>.options-box)>*:not(.options),
		a:hover {
			color: $color-primary;
		}

		>b.icon-Down::before {
			transform: rotate(-180deg);
		}

		>.options {
			display: grid;
		}
	}

	a {
		padding: 0.5em 1em;

		>span[style] {
			color: white;
			font-size: 10px;
			padding: 1px 6px;
			margin-left: 0.5em;
			display: inline-block;
			border-radius: 0 1em 1em 1em;
		}
	}

	.options {
		padding: 1em;
		font-size: 1rem;
		text-align: left;
		width: max-content;
		border-radius: 5px;
		grid-row-gap: 0.5em;
		grid-column-gap: 1em;
		color: $text-primary;
		background-color: white;
		transform: translate(-50%);
		box-shadow: 0 0 10px -5px $bg-mask;
		position: absolute;
		z-index: 99;
		left: 50%;
		top: 95%;

		.options {
			transform: none;
			left: 100%;
			top: 0.5em;
		}

		&::before {
			content: '';
			width: 10px;
			height: 10px;
			background: white;
			box-shadow: -3px -3px 6px -4px $bg-mask;
			transform: scale(1, 1.3) rotate(45deg);
			position: absolute;
			left: calc(50% - 5px);
			z-index: -1;
			top: -5px;
		}
	}
}

.modal-box.tips {
	color: white;
	padding-top: 0;
	padding-bottom: 0;
	font-size: 0.875em;
	text-align: center;
	align-items: center;
	grid-column-gap: 3%;
	justify-content: center;
	background-color: $color-primary;
	z-index: 2;

	>div:not(.link-box):not(.logo-box):not(.system-box) {
		margin: 0.8em 0;
		align-items: center;
	}

	.link-box {
		align-self: stretch;

		>div {
			padding: 0.8em 0;
			align-items: center;

			&:first-child {
				color: white;
				justify-content: center;
				// font-weight: bold;
				padding-left: 1em;
				padding-right: 1em;
				background: #333;
				min-width: 8.57em;
			}

			a:hover {
				text-decoration: underline;
			}
		}
	}

	.phone,
	.email {
		grid-gap: 0.2em;
		white-space: nowrap;

		b {
			line-height: 1;
			font-size: 1.1em;
		}
	}

	.email {
		flex: 1;
	}

	button {
		border-bottom: 1px solid;
	}
}

.nav {
	height: 70px;
	align-items: center;
	padding-top: 0 !important;
	padding-bottom: 0 !important;

	.nav-box {
		flex: 1;
		grid-gap: min(3vw, 4em);

		>* {

			>a {
				padding: 1.5em 0;
				position: relative;

				&::after {
					content: '';
					width: 0;
					border-bottom: 1px solid;
					transition: all 0.2s ease;
					position: absolute;
					bottom: 22%;
					left: 0;
				}
			}

			>b {
				display: none;
			}

			&:hover>a::after,
			&[select]>a::after {
				width: 100% !important;
			}

			&[select]>a::after {
				border-bottom: 3px solid;
			}
		}
	}

	.product:not(.category)>.options {
		padding: 2em 3em;
		grid-template-columns: 1fr 1fr 1fr;
	}

	.category {
		position: static;

		>.options {
			padding-top: 2em;
			grid-column-gap: 0;
			font-size: calc(1em - 2px);
			grid-template-columns: auto 1fr 1fr;
			top: 68px;

			>.options-box {
				display: block;

				>a {
					color: initial;
					cursor: default;
					line-height: 2em;
					font-weight: bold;
					font-size: calc(1em + 2px);
					padding-left: calc(2em - 4px);
				}

				>b {
					display: none;
				}

				&:first-child>.options {
					padding-right: 0;
					grid-template-columns: 1fr 1fr 1fr;
				}

				&:not(:first-child) {
					padding-left: 1em;
					border-left: 1px solid #EFEFEF;
				}

				>.options {
					display: grid;
					transform: none;
					box-shadow: none;
					position: static;
				}
			}
		}
	}

	.hot .options {
		padding: 1em 2em 2em;
		font-size: calc(1em - 2px);

		::v-deep .modal-box {
			padding: 0;
			width: 59vmax;

			.card {
				background: var(--color-primary);

				.content {
					color: white;
					padding: 0 10px 10px;

					.content-title {
						flex: 1;
					}

					.content-sub-title {
						display: none;
					}
				}
			}
		}

		.options-box {
			padding-top: 1.5em;
			justify-content: center;

			a {
				padding: 0;
				border-bottom: 1px solid;
			}
		}
	}

	.system-box>* {
		padding: 1.25rem 0.25rem;
	}
}

.logo-box img {
	width: auto;
	height: 42px;
	max-width: 16.5vw;
	object-fit: contain;
}

.system-box {
	grid-gap: 1em;
	align-items: center;
	justify-content: flex-end;

	>* {
		display: flex;
		grid-gap: 0.3em;
		align-items: center;
		padding: 0.6em 0.4em;
	}

	b::before {
		font-size: 1.25rem;
		font-weight: normal;
	}
}

.cart-box {
	b {
		position: relative;

		label {
			height: 18px;
			color: white;
			font-size: 10px;
			min-width: 18px;
			line-height: 18px;
			text-align: center;
			border-radius: 50%;
			background: $color-red;
			position: absolute;
			right: -12px;
			top: -5px;
		}
	}

	>label {
		// font-weight: bold;
		// margin-left: 0.3vw;
		font-size: calc(1em - 2px);
	}
}

.ccy-box .options {
	max-height: 16em;

	>span {
		cursor: pointer;
		padding: 0.5em 1em;
	}
}

.lang-box {
	text-transform: uppercase;

	.lang-img {
		width: 1.35rem;
		height: 1.35rem;
		border-radius: 50%;
	}

	.options {
		transform: none;
		grid-row-gap: 0;
		right: -1em;
		left: auto;
	}
}



.img-float {
	.tips:first-of-type {
		width: 100%;
		display: none;
		position: absolute;
		z-index: 5;
	}

	.nav .float-btn {
		order: 10;
		height: 100%;
		position: relative;

		img {
			width: auto;
			height: 3.25em;
			transform: translateY(120%);
			position: absolute;
			right: -0.75em;
			bottom: 0;
		}
	}
}



[theme]:not([theme='4']) {
	.user-box {
		>span {
			padding-left: 0.5em;
		}

		>.icon-Down {
			transform: scale(0.7);
			font-size: 0.7em !important;
		}

		.user-title {
			padding: 0.8em;
			font-size: 1.286em;
			border-bottom: 1px solid #F6F6F6;
		}

		.user-sub-title {
			padding: 0.2em 1em 0.8em;
		}

		a {
			&:nth-child(-n + 5) {
				margin: 0 1em;
				padding: 0.2em 0 0.7em;
				border-bottom: 1px solid #EDEDED;
			}

			b {
				width: 1.5em;
			}
		}

		.logout {
			height: 2.5em;
			margin: 1em 2.2em;
		}
	}

	.cart-box b label {
		background: $color-primary;
	}
}


[theme='4'] .nav {
	.nav-box {
		order: -1;

		>:first-child {
			margin-left: 0;
		}
	}

	.system-box {
		flex: 1;
	}

	.product>.options {
		transform: translate(-15%);

		&::before {
			left: calc(15% - 5px);
		}
	}
}


[theme='5'] {
	.nav {
		.nav-box {
			padding-left: 2em;
		}

		.options::before {
			display: none;
		}
	}
}


[theme='6'] .cart-box b {
	font-size: 1.1em;
}

[theme='7'] {
	.nav-box .options-box {
		a span {
			font-weight: 400;
			border-radius: 0.4em 0.4em 0.4em 0;
		}
	}
}

[theme='8'],
[theme='23'],
[theme='24'] {

	.tips .phone,
	.tips .email {
		>span:nth-child(2) {
			display: none;
		}
	}
}


.nav-line-white .nav>div>.options-box,
[theme='9'] .nav>div>.options-box,
[theme='12'] .nav>div>.options-box,
[theme='14'] .nav>div>.options-box,
[theme='16'] .nav>div>.options-box,
[theme='18'] .nav>div>.options-box {
	&[select]>*:not(.options) {
		font-weight: bold;
		color: inherit !important;
	}

	&:hover>*:not(.options) {
		color: inherit !important;
	}
}


[theme='10'][id] {
	opacity: 0.9;
	border-radius: 0.5em;
	background: $bg-primary;
	width: min(1620px, 97vw);
	margin: 2.5em max(calc(50% - 810px), 1.5vw) 1em;
	border: thin solid $bg-dark;
	position: relative;

	&::before {
		content: "";
		height: 80%;
		border-radius: 1em;
		background: $bg-primary;
		width: calc(100% + 1.5em);
		border-left: 1px solid $bg-dark;
		border-right: 1px solid $bg-dark;
		position: absolute;
		left: -0.75em;
		top: 10%;
	}

	&::after {
		content: "";
		width: 95%;
		border-bottom: thin solid $bg-dark !important;
		position: absolute;
		bottom: 0.3vmax;
		left: 2.5%;
	}

	.nav {
		.logo-box {
			transform: translate(-50%, -50%);
			position: absolute;
			z-index: 1;
			left: 50%;
			top: 50%;

			img {
				height: 7.5em;
				border-radius: 50%;
				box-shadow: 0px 0px 1em 0px rgba(155, 144, 131, 0.3);
			}
		}

		.nav-box {
			order: -1;
			align-items: center;

			>.options-box {
				margin-left: min(3vw, 3em);
				border: 1px dashed transparent;

				&:first-child {
					margin-left: 0;
				}

				&:nth-child(3) {
					margin-left: 17vw;
				}

				>a {
					padding: 0.5em 1vw;
					font-size: calc(1em + 0.21vw);

					&::after {
						display: none;
					}
				}

				&[select] {
					padding: 0.3em;
					border-color: $color-primary;

					a {
						font-weight: normal;
						color: white !important;
						background: $color-primary;
					}
				}
			}
		}

		// .lang-box {
		// 	font-size: 1em;
		// }
	}

	&.sticky {
		position: fixed;
	}
}


[theme='11'] .nav .cart-box {
	order: -1;
}


[theme='13'] .nav-box b {
	display: block;
}


[theme='17'].sticky {
	width: 100vw;
	background: none;
	position: fixed;

	.tips .lang-box>img {
		display: none;
	}
}

[theme='20'] {
	.modal-box.tips {
		>div:not(.link-box):not(.logo-box):not(.system-box) {
			margin: 0.6em 0;
		}

		.link-box>div {
			padding: 0.6em 0;
		}
	}

	.system-box>* {
		padding-top: 0.3em;
		padding-bottom: 0.3em;
	}
}

[theme='21'] .email {

	span,
	strong {
		display: none;
	}
}

// pc跟ipad端样式
@media screen and (min-width: $mb-width) {
	.nav {
		grid-gap: min(3vw, 3em);
	}

	[theme='10'] .system-box>*>b::before,
	[theme='11'] .system-box>*>b::before {
		font-size: 1.4em !important;
	}

	[theme='15'] {

		.options-box:hover:not(.system-box>.options-box)>*:not(.options),
		.options-box a:hover,
		::v-deep .lang-box .language-box.sele {
			color: #545454;
		}

		.nav {
			.nav-box>.options-box {
				>a {
					padding: 0.7em 1.25em;

					&::after {
						border-bottom: none !important;
					}
				}

				&[select] {
					background: linear-gradient(135deg, transparent 7px, $color-primary 7px) 0px 0px,
						linear-gradient(-135deg, transparent 7px, $color-primary 7px) 100% 0px,
						linear-gradient(-45deg, transparent 7px, $color-primary 7px) 100% 100%,
						linear-gradient(45deg, transparent 7px, $color-primary 7px) 0px 100%;
					background-repeat: no-repeat;
					background-size: 52% 52%;

					>a {
						font-weight: 400;
						color: white !important;
					}
				}
			}

			.system-box {
				font-size: 1.25em;

				>* {
					padding: 0.4em 0.6em;

					&:hover {
						background: linear-gradient(135deg, transparent 7px, $bg-primary 7px) 0px 0px,
							linear-gradient(-135deg, transparent 7px, $bg-primary 7px) 100% 0px,
							linear-gradient(-45deg, transparent 7px, $bg-primary 7px) 100% 100%,
							linear-gradient(45deg, transparent 7px, $bg-primary 7px) 0px 100%;
						background-repeat: no-repeat;
						background-size: 52% 52%;
					}
				}
			}
		}

		.modal-box.tips .system-box>* {
			padding: 0.6em;

			&:hover {
				background: linear-gradient(135deg, transparent 7px, var(--color-primary-darken) 7px) 0px 0px,
					linear-gradient(-135deg, transparent 7px, var(--color-primary-darken) 7px) 100% 0px,
					linear-gradient(-45deg, transparent 7px, var(--color-primary-darken) 7px) 100% 100%,
					linear-gradient(45deg, transparent 7px, var(--color-primary-darken) 7px) 0px 100%;
				background-repeat: no-repeat;
				background-size: 50% 50%;
			}
		}
	}

	[theme='21'] {
		.nav-box {
			.options-box {
				>.options {
					margin-top: 0.1em;
					padding: 0.75em 1.625em 1.5em;
					gap: 0 2.5em;

					&::before {
						display: none
					}

					.options-box {
						>a {
							padding-bottom: 0.25em;
							font-size: calc(1em - 2px);

							span {
								border-radius: 0.25em;
							}
						}
					}
				}
			}

			.category {
				>.options {
					padding-left: 3.125em;
					padding-right: 3.125em;
					gap: 0.1em 3.125em;

					>.options-box {
						padding-left: 0;
						display: flex;
						flex-direction: column;
						align-items: start;

						>a {
							width: 100%;
							min-width: 11.25em;
							padding-bottom: 0;
							border-bottom: 1px solid #f2f2f2;
							font-size: 1em;
						}

						a {
							padding-left: 0;
						}

						&:first-child {
							>.options {
								grid-template-columns: 1fr;
							}
						}

						&:not(:first-child) {
							border-left: none
						}

						.options {
							padding: 0;
						}
					}
				}
			}

			.product {
				>.options {
					grid-template-columns: 1fr 1fr auto 1fr;

					>.options-box {
						&:first-child {
							grid-row-start: span 2;
						}

						&:nth-child(4) {
							grid-row-start: span 2;
						}

						&:nth-child(3n) {
							.options {
								grid-template-columns: 1fr 1fr;
							}
						}
					}
				}
			}
		}
	}

	[theme='7'] {
		.nav-box {
			.options-box .options {
				gap: 0 1em;

				.options-box[select],
				a:hover {
					font-weight: bold !important;
					text-decoration: underline;
				}
			}

			>.options-box:not(.category) .options {
				font-weight: bold;
			}

			.product {
				>.options {
					grid-template-columns: auto auto auto;
				}
			}

			.category {
				>.options {
					grid-template-columns: auto auto auto auto;
					gap: 0.5em 0;

					>.options-box:not(:first-child) {
						border: none;
					}

					>.options-box:first-child {
						grid-row-start: span 2;
					}

					>.options-box {
						>a {
							padding-bottom: 0.2em;
						}

						>a:hover {
							text-decoration: none;
						}

						>.options {
							padding-bottom: 0;
							padding-top: 0;
							grid-template-columns: 1fr;
							font-size: 1em;
						}
					}
				}
			}
		}
	}

}



// mb端样式
@media screen and (max-width: $mb-width) {
	.modal-box.tips {
		font-size: 0.92em;

		.phone,
		.email {
			display: none;
			height: 0.75em;

			>span,
			b {
				display: none;
			}

			span:last-child {
				display: block;
			}
		}

		.link-box {
			align-items: center;

			a {
				text-decoration: underline;
			}

			>div {
				height: 0.75em;
				padding: 0 2vw 0 0;

				&:first-child {
					display: none;
				}

				&:not(:last-child) {
					border-right: 1px solid #585858;
				}
			}

		}
	}

	.neonsigns .link-box>div:not(:last-child) {
		border-right: 1px solid #fff !important;
	}

	.menu {
		width: 100vw;
		height: 100vh;
		display: flex;
		overflow-y: auto;
		flex-direction: column;
		padding: 0 20% 0 0 !important;
		background: linear-gradient(to right, white 80%, $bg-mask 80%, transparent 120%) !important;
		position: fixed;
		z-index: 990;
		left: 0;
		top: 0;

		.contact-box {
			color: white;
			padding: 0 1.6em;
			justify-content: space-between;
			background-color: $color-primary;

			.phone,
			.email {
				grid-gap: 0.5em;
				padding: 0.8em 0;
				align-items: center;
				transform: scale(0.85);

				>span {
					display: none;
				}

				b {
					font-size: 2em;
				}

				strong {
					display: block;
				}
			}

			.phone b::before {
				content: "\e621";
			}

			.email b::before {
				content: "\e69f";
			}
		}

		.system-box {
			padding: 0 2em;
			justify-content: space-between;
			border-bottom: 1px solid #ebebeb;
			z-index: 99;

			>div {
				padding: 1.5em 0;
			}

			.ccy-box {
				.icon-bps-guojia {
					font-size: 1.3em;
					margin-right: 0.3em;
					color: $color-primary;
				}

				.options {
					transform: translate(-2em);

					&::before {
						left: 1.5em;
					}
				}
			}
		}

		.user-box {
			text-align: center;
			margin-bottom: 1em;

			.avatar {
				width: 10vw;
				height: 10vw;
				border-radius: 50%;
				margin: 6.5vw 29vw 2vw;
				border: 1px solid $color-primary;
			}

			button {
				height: 30px;
				margin-top: 3vw;
				border-radius: 5px;
			}
		}

		.nav-box {
			flex: 1;
			display: block;
			padding: 1em 1em 2em;

			.options-box {
				flex-wrap: wrap;

				>a {
					flex: 1;
					padding: 1em 0;
					margin-left: 1.5em;
					min-width: fit-content;
				}

				>b.icon-Down {
					width: 5em;
					text-align: right;
				}

				&[select] {
					border-radius: 1em;
					background: $color-second;
				}

				&:hover {
					>b.icon-Down::before {
						transform: none;
					}

					.options {
						display: none;
					}
				}

				&[expand] {
					>b.icon-Down::before {
						transform: rotate(180deg);
					}

					>.options {
						display: block;
					}
				}

				.options {
					padding: 0;
					width: 100%;
					transform: none;
					box-shadow: none;
					border-radius: 0;
					position: static;

					&::before {
						display: none;
					}
				}

				>.options>.options-box {
					a {
						margin-left: 2.5em;
					}

					>.options>.options-box {
						a {
							margin-left: 3.5em;
						}

						>.options>.options-box {
							a {
								margin-left: 4.5em;
							}

							>.options>.options-box {
								a {
									margin-left: 5.5em;
								}
							}
						}
					}
				}
			}

			.options-box.hot {
				::v-deep .modal-box {
					padding: 0 0 0 1.5em;

					.card {
						flex-direction: row;
						width: 100% !important;

						.card-img {
							width: 22vw;
							height: 22vw;
							padding-bottom: 0;

							b {
								font-size: 1em;
							}
						}

						.hot-box {
							transform: translateY(-25%) scale(0.5);
							left: -0.5em;
						}

						.content {
							text-align: left;
							padding: 0.5em 0;
							justify-content: space-evenly;

							>label {
								flex: 0;
							}
						}

						.content-title {
							font-size: 1em;
							font-weight: normal;
						}

						.content-sub-title {
							display: none;
						}

						.content-price {
							color: #666;
						}
					}
				}


				>.options>.options-box {
					color: #999;
					margin: 1em 0;
					background: none;
					justify-content: center;

					a {
						flex: none;
						margin-left: 0;
						padding-bottom: 0;
						border-bottom: 1px solid;
					}
				}
			}
		}
	}

	.nav {
		height: 4.5em;
		padding: 0 3vw !important;
		justify-content: space-between;

		.menu-box {
			flex: 1;
			display: block;

			b {
				display: flex;
				grid-gap: 0.2em;
				width: fit-content;
				align-items: center;
				flex-direction: column;

				&::before {
					font-size: 1.25rem;
				}
			}
		}

		.logo-box img {
			height: 3em;
			margin: 0 1em;
			max-width: 58vw;
		}

		.nav-box {
			display: none;
		}

		.system-box {
			flex: 1;
			min-width: fit-content;

			.user-box .options {
				transform: translate(-88%);

				&::before {
					left: calc(88% - 5px);
				}
			}

			.cart-box b label {
				transform: scale(0.7);
			}

			.ccy-box,
			.lang-box {
				display: none;
			}
		}
	}

	.system-box {
		grid-gap: 0.5em;
	}


	[theme='5'] .nav-box {
		padding: 1em 0;

		.options-box:not(:first-child)>*:not(.options) {
			border-top: 1px solid $bg-shadow;
		}

		.options-box {
			b.icon-Down {
				padding: 1em 0;
				margin-right: 1.5em;
				transform: scale(1);

				&::before {
					transform: none;
					content: "\e666";
				}
			}

			&[select] {
				background: none;
				border-radius: 0;
			}

			&[expand] {
				&+.options-box>*:not(.options) {
					border-top-color: transparent;
				}

				>b.icon-Down::before {
					content: "\e681";
				}
			}

			.options {
				padding: 0;
				background: $bg-shadow;
			}
		}

		.options-box.hot>.options>.options-box a {
			border-top: none;
		}
	}

	[theme='6'] .nav {
		.login-box span {
			display: none;
		}

		// .cart-box {
		// 	order: -1;
		// }
	}


	[theme='8'] .menu .nav-box,
	[theme='9'] .menu .nav-box,
	[theme='10'] .menu .nav-box {
		padding: 1em 0 5em;

		.options-box {
			border-top: thin solid #ECECEC;

			&[select],
			&[select]+.options-box {
				border-radius: 0;
				border-top-color: transparent;
			}

			a {
				padding: 1.2em 0;
			}
		}

		.options-box.hot>.options>.options-box {
			border-top: none;
		}
	}


	[theme='10'][id] {
		opacity: 1;
		width: 92vw;
		margin: 1em 4vw 0;

		&::before {
			border-radius: 0.5em;
			width: calc(100% + 1em);
			left: -0.5em;
		}

		.nav {
			.logo-box img {
				height: 2.65em;
				border: thin solid $bg-dark;
			}

			b:not(.icon-Down)::before {
				font-size: 0.8em !important;
			}
		}
	}


	[theme='11'] .nav .system-box {
		flex: none;
	}

	[theme] {
		.system-box {
			b::before {
				font-size: 1.59rem;
			}
		}
	}

	[theme='21'] {
		.modal-box.tips .email {
			b {
				font-size: 1.38em;
				display: block;
			}

			>span:last-child {
				display: block;
				border-bottom: 1px solid #fff;
			}
		}

		.menu-box {
			flex: none
		}
	}
}
</style>
