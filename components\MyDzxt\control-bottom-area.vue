<template>
	<div class="add-element-area" ref="moreList">
		<div class="add" @click="showMore">
			<b class="icon-plus"></b>
		</div>
		<div class="add-element-btn">
			<div class="save" @click="saveClick">
				<button>{{ lang.save }}</button>
			</div>
			<div class="getPrice" @click="next">
				<button>
					{{ $store.state.design.pageInfo.isMetal == "1" ? lang.next : lang.getPrice }}
					<template v-if="$store.state.design.pageInfo.isMetal === 0">
						<el-tooltip class="item" effect="light" popper-class="dzToolTip" :content="lang.getPriceTip" placement="top">
							<b @click.stop class="iconfont icon-wenhao" style="font-size: 18px; color: #ffffff; margin-left: 2px"></b>
						</el-tooltip>
					</template>
				</button>
			</div>
		</div>
		<div class="moreList" v-show="show">
			<div class="tab-item tab-template" :class="{ active: status === 1 }" @click="showTab(1)">
				<div class="tab-icon">
					<b class="icon-a-icon-Templateszhuanhuan"></b>
				</div>
				<div class="tab-text">{{ lang.templates }}</div>
			</div>
			<div class="tab-item" :class="{ active: status === 2 }" @click="showTab(2)">
				<div class="tab-icon">
					<b class="icon-a-icon-Textzhuanhuan"></b>
				</div>
				<div class="tab-text">{{ lang.addText }}</div>
			</div>
			<div class="tab-item" :class="{ active: status === 3 }" @click="showTab(3)">
				<div class="tab-icon">
					<b class="icon-a-icon-Artzhuanhuan"></b>
				</div>
				<div class="tab-text">{{ lang.addCliparts }}</div>
			</div>
			<div class="tab-item" :class="{ active: status === 4 }" @click="showTab(4)">
				<div class="tab-icon">
					<b class="icon-a-icon-Uploadzhuanhuan"></b>
				</div>
				<div class="tab-text">{{ lang.upload }}</div>
			</div>
		</div>
		<!-- 手机端模板分类 -->
		<el-drawer :visible.sync="templateCategoryDrawer" custom-class="templateCategoryDrawerWrap drawerWrap" :with-header="false" size="90%" direction="btt" @close="closeTab">
			<templateListMb @closeDraw="closeTemplateDraw"></templateListMb>
		</el-drawer>
		<!-- 手机端添加文字 -->
		<el-drawer :visible.sync="addTextDrawer" custom-class="addTextDrawerWrap drawerWrap" :with-header="false" direction="btt" size="300px" @close="closeTab">
			<div class="drawerContent">
				<topBar :title="lang.addText" :show-back-icon="false" @close="closeTextDraw"></topBar>
				<div class="main">
					<label class="textLabel">
						<textarea v-model="inputText" rows="4" :placeholder="lang.enterText"></textarea>
					</label>
				</div>
				<div class="footer">
					<el-button type="primary" @click="addCurveText(inputText)">{{ lang.addCurvedToDesign }}</el-button>
					<el-button type="primary" @click="addText(inputText)">{{ lang.addToDesign }}</el-button>
				</div>
			</div>
		</el-drawer>
		<!-- 手机端元素分类 -->
		<artClipartsListMB :artClipartDrawer.sync="artClipartDrawer" @closeDraw="closeClipartsDraw" @close="closeTab"></artClipartsListMB>
		<!-- 手机端添加图片 -->
		<el-drawer :visible.sync="addImageDrawer" custom-class="addImageDrawerWrap drawerWrap" :with-header="false" direction="btt" size="400px" @close="closeTab">
			<div class="drawerContent">
				<topBar :title="lang.upload" :show-back-icon="false" @close="closeImageDraw"></topBar>
				<div class="main">
					<div class="topArea">
						<div>{{ lang.chooseFile }}</div>
						<div class="upload-area" v-loading="uploadLoading" id="uploadBox" @click="triggerUpload">
							<div class="upload-icon">
								<b class="icon-a-icon-Uploadzhuanhuan"></b>
							</div>
							<div class="upload-text">
								<span>{{ lang.Browse }}</span
								><br />
								{{ lang.maxFile }}
							</div>
							<div class="upload-tips">{{ lang.imgJPG }}</div>
							<input type="file" ref="upload1" :accept="acceptFileType" @change="uploadOnChange" />
						</div>
						<div class="tips">{{ lang.remark }}</div>
					</div>
				</div>
			</div>
		</el-drawer>
		<Crop :cropperDialog.sync="cropperDialog" :imageSrc="imageSrc" @cropImage="cropImage" width="90%" style="z-index: 2000000"></Crop>
	</div>
</template>

<script>
import BaseDialog from "~/components/Quote/BaseDialog.vue";
import artClipartsListMB from "@/components/MyDzxt/artClipartsListMB.vue";
import searchInput from "@/components/MyDzxt/searchInput.vue";
import templateListMb from "@/components/MyDzxt/templateListMb.vue";
import topBar from "@/components/MyDzxt/topBar.vue";
import dzMixin from "@/mixins/dzMixin";
import { uploadFile } from "@/utils/oss";
import { dataURLtoFile, pdfToImg } from "@/utils/utils";
import { addUploadHistory, getUploadHistory } from "@/api/newDzxt";
import Crop from "@/components/MyDzxt/Crop.vue";
import { checkFile, designSystemFileType } from "@/utils/validate";

export default {
	mixins: [dzMixin],
	components: {
		Crop,
		topBar,
		searchInput,
		templateListMb,
		artClipartsListMB,
		BaseDialog,
	},
	data() {
		return {
			acceptFileType: designSystemFileType,
			imageSrc: "",
			cropperDialog: false,
			saveDialogDialog: false,
			uploadLoading: false,
			addImageDrawer: false,
			artClipartDrawer: false,
			inputText: "",
			addTextDrawer: false,
			artTagList: [],
			templateCategoryDrawer: false,
			list: [],
			status: 1,
			show: false,
		};
	},
	computed: {
		lang() {
			return this.$store.getters.lang?.design;
		},
		isLogin() {
			return this.$store.getters.isLogin;
		},
		userId() {
			return this.$store.state.userInfo?.id;
		},
	},
	methods: {
		saveClick() {
            this.$emit("saveClick")
		},
		next() {
			if (this.canvas.isEmptyCanvas()) {
				this.$toast.info("The canvas is empty");
				return false;
			}
			this.$emit("next");
		},
		async addImg(src, property) {
			this.$store.commit("design/set_loading", true);
			await this.canvas.addImg(src, property);
			this.closeImageDraw();
			this.$store.commit("design/set_loading", false);
		},
		getUploadHistory() {
			getUploadHistory({
				userId: this.userId,
			}).then((res) => {
				this.uploadHistoryList = res.data;
			});
		},
		addToUploadHistory(urlArr) {
			if (!urlArr.length || !this.isLogin) {
				return false;
			}
			if (this.isLogin) {
				addUploadHistory({
					userId: this.userId,
					picPath: urlArr[0].picPath,
				}).then((res) => {
					this.getUploadHistory();
				});
			}
		},
		uploadFileToDesign(file) {
			this.uploadLoading = true;
			uploadFile(file)
				.then((res) => {
					this.addImg(res, { isUserUpload: 1 });
					this.addToUploadHistory([
						{
							picPath: res,
						},
					]);
				})
				.finally(() => {
					this.$refs["upload1"].value = "";
					this.uploadLoading = false;
				});
		},

		//图片放大缩小
		changeScale(num) {
			num = num || 1;
			this.$refs.cropper.changeScale(num);
		},

		cropImage(data) {
			this.$store.commit("design/set_loading", true);
			let file = dataURLtoFile(data, 1);
			this.uploadFileToDesign(file);
			this.$store.commit("design/set_loading", false);
		},

		uploadOnChange(e) {
			let file = e.target.files[0],
				name = file.name;
			let checkResult = checkFile([file], this.acceptFileType, 10);
			if (!checkResult) {
				this.$refs["upload1"].value = "";
				this.$toast.error(this.lang.imgError);
				return false;
			}
			if (!checkResult.nomalSize.length) {
				this.$refs["upload1"].value = "";
				this.$toast.error(this.lang.sizeError);
				return false;
			}
			if (/\.(pdf)$/.test(name.toLowerCase())) {
				let reader = new FileReader();
				reader.readAsDataURL(file);
				reader.onload = async (e) => {
					this.imageSrc = await pdfToImg(e.target.result);
				};
				this.cropperDialog = true;
			} else if (/\.(psd)/.test(name.toLowerCase())) {
				let reader = new FileReader();
				reader.readAsDataURL(file);
				reader.onload = async (e) => {
					let PSD = require("@/assets/js/psd");
					let psd = await PSD.fromURL(e.target.result);
					this.imageSrc = psd.image.toBase64();
				};
				this.cropperDialog = true;
			} else {
				let reader = new FileReader();
				reader.readAsDataURL(file);
				reader.onload = async (e) => {
					this.imageSrc = e.target.result;
				};
				this.cropperDialog = true;
			}
			this.$refs["upload1"].value = "";
		},

		triggerUpload() {
			this.$refs["upload1"].click();
		},
		addCurveText(val, property) {
			this.canvas.addCurveText(val, property);
			this.inputText = "";
			this.closeTextDraw();
		},
		addText(val) {
			this.canvas.addText(val);
			this.inputText = "";
			this.closeTextDraw();
		},
        showTab(status) {
			this.status = status;
			this.addTextDrawer = false;
			this.artClipartDrawer = false;
			this.templateCategoryDrawer = false;
			this.addImageDrawer = false;
            setTimeout(()=>{
                this.show = true;
            },0)
			switch (status) {
				case 1:
					this.templateCategoryDrawer = true;
					break;
				case 2:
					this.addTextDrawer = true;
					break;
				case 3:
					this.artClipartDrawer = true;
					break;
				case 4:
					this.addImageDrawer = true;
					break;
			}
		},
		showMore() {
			this.show = true;
			this.showTab(this.status);
		},
		closeTab() {
			this.show = false;
		},
		closeTemplateDraw() {
			this.templateCategoryDrawer = false;
			this.closeTab();
		},
		closeClipartsDraw() {
			this.artClipartDrawer = false;
			this.closeTab();
		},
		closeTextDraw() {
			this.addTextDrawer = false;
			this.closeTab();
		},
		closeImageDraw() {
			this.addImageDrawer = false;
			this.closeTab();
		},
	},
	mounted() {
		if (this.$route.query.cid) {
			this.showMore();
		}
	},
};
</script>

<style scoped lang="scss">
::v-deep .base-dialog-model-con {
	position: relative;

	.button {
		text-align: center;
	}

	.title {
		font-size: 20px;
		border-bottom: 1px solid;
		padding: 5px 15px;
		font-weight: bold;
	}

	.cropper-icon {
		display: flex;
		position: absolute !important;
		z-index: 10 !important;
		top: 60px;
		right: 25px;

		.el-button {
			border-radius: 50%;
			background-color: #909399;
			width: auto;
			color: white;
			display: flex;
			justify-content: center;
			border: 1px solid #909399;
			padding: 5px;
			margin-left: 5px;
		}
	}

	.cropper-btn {
		text-align: right;
		padding: 0 20px 20px;
	}

	.cropper {
		height: 310px;
		padding: 20px;

		.vue-cropper {
			background-repeat: initial !important;
		}
	}
}

.add-element-area {
	position: relative;
	display: flex;
	justify-content: space-between;
	align-items: center;
	height: 50px;
	padding: 5px 10px 0;

	.moreList {
		display: flex;
		align-items: center;
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		height: 60px;
		padding: 4px;
		background-color: #171719;
		z-index: 99999;

		.tab-item {
			flex: 0 0 25%;
			display: flex;
			padding: 5px 0;
			flex-direction: column;
			color: #ffffff;
			text-align: center;
			font-size: 12px;

			b {
				margin-bottom: 5px;
				font-size: 17px;
			}

			&.active {
				background: #2996fb;
				border-radius: 3px;
			}
		}
	}

	.add {
		display: flex;
		justify-content: center;
		align-items: center;
		width: 40px;
		height: 40px;
		background: #2996fb;
		border-radius: 50%;

		b {
			color: #ffffff;
			font-size: 16px;
		}
	}

	.add-element-btn {
		flex: 1;
		display: flex;
		justify-content: right;

		.getPrice button {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 110px;
			height: 40px;
			background: #008027;
			border-radius: 5px;
			font-size: 15px;
			color: #ffffff;
		}

		.save button {
			width: 80px;
			height: 40px;
			background: #fff;
			border: 1px solid #2996fb;
			border-radius: 5px;
			font-size: 15px;
			color: #2996fb;
			margin-right: 6px;
		}
	}
}

::v-deep {
	.drawerWrap {
		height: auto;
		max-height: 100%;
		font-size: 14px;
		border-top-left-radius: 10px;
		border-top-right-radius: 10px;

		.el-drawer__body {
			padding-bottom: 60px;
		}
	}

	.addTextDrawerWrap {
		.topBar {
			border-bottom: none;
		}

		.main {
			padding: 10px;

			label.textLabel {
				display: block;
				background: #f5f5f5;
				border-radius: 10px;
				width: 100%;

				textarea {
					border: 0;
					color: inherit;
					outline-width: 0;
					-webkit-overflow-scrolling: touch;
					padding: 1em;
					resize: none;
					white-space: pre;
					width: 100%;
					background: #f5f5f5;
					border-radius: 10px;
				}
			}
		}

		.footer {
			display: flex;
			justify-content: center;
			align-items: center;
			padding: 10px;
		}
	}

	.addImageDrawerWrap {
		.topBar {
			border-bottom: none;
		}

		.main {
			padding: 10px;
		}

		.topArea {
			margin-bottom: 10px;

			.upload-area {
				overflow: hidden;
				position: relative;
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				aspect-ratio: 509/224;
				background: #fafbfc;
				border: 1px solid #dadcdf;
				border-radius: 10px;
				margin: 14px 0 22px 0;
				text-align: center;
				cursor: pointer;
				transition: all 0.3s;

				&:hover {
					border-color: #2a96fa;
				}

				&.dropActive {
					border-color: #2a96fa;
				}

				input[type="file"] {
					position: absolute;
					opacity: 0;
					z-index: -1;
				}

				.upload-icon {
					font-size: 50px;
					color: #cccccc;
				}

				.upload-text {
					font-size: 15px;
					font-family: Calibri;
					margin-top: 10px;
					line-height: 1.4;

					span {
						font-weight: bold;
						color: #2a96fa;
					}
				}

				.upload-tips {
					color: #999999;
					font-size: 14px;
				}
			}

			.tips {
				font-size: 14px;
				color: #999999;
			}
		}

		.tips {
			font-size: 14px;
			color: #999999;
		}
	}
}
</style>