<template>
	<div class="modal-box searContainer" v-if="childCategoryRoutingNameList.length > 0">
		<h2 v-if="childCategoryRoutingNameList.length > 0">{{ langSemiCustom.relateSearch }}</h2>
		<div class="sear-wrap">
			<div class="sear-item" v-for="(item, index) in childCategoryRoutingNameList" :key="index"
				@click="toCate(item.customRouting)">
				<div class="fangdajing"><v-icon>mdi-magnify</v-icon></div>
				<span :title="item.name"><a href="javaScript:;">{{ item.name }}</a></span>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	name: "HalfRelatedSearch",
	components: {},
	props: ["childCategoryRoutingNameList"],
	data() {
		return {};
	},
	watch: {},
	computed: {
		langSemiCustom() {
			return this.$store.getters.lang?.semiCustom;
		},
	},
	methods: {
		toCate(routeName) {
			this.$router.push({
				path: routeName,
			});
		},
	}
};
</script>

<style scoped lang="scss">
.searContainer {
	padding-bottom: 2.5em;

	h2 {
		font-size: 36px;
		font-weight: 700;
		margin-bottom: 1em;
		text-align: center;
	}

	.sear-wrap {
		grid-gap: 1%;
		display: grid;
		grid-template-columns: repeat(6, 1fr);

		.sear-item {
			display: flex;
			cursor: pointer;
			align-items: center;
			justify-content: center;
			border: 1px solid #ccc;
			font-size: 14px;
			border-radius: 20px;
			line-height: 36px;
			min-width: 0;
			position: relative;

			span {
				max-width: 80%;
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
			}

			.fangdajing {
				position: absolute;
				left: 10px;
				top: 50%;
				transform: translateY(-50%);
			}

			@media (any-hover: hover) {
				&:hover {
					background-color: $color-primary;
					color: #fff;

					.fangdajing .v-icon {
						color: #fff;
					}
				}
			}
		}
	}

	@include respond-to(mb) {
		h2 {
			font-size: 21px;
			margin-bottom: 10px;
		}

		.sear-wrap {
			grid-gap: 0.5em;
			grid-template-columns: repeat(2, 1fr);

			.sear-item {
				font-size: 12px;
				justify-content: flex-start;
				padding-left: 20px;

				.fangdajing {
					position: relative;
					top: 0;
					left: 0;
					transform: translateY(0);
				}

				a:active {
					color: $color-primary;
					text-decoration: underline;
				}

				&:active {
					.fangdajing .v-icon {
						color: $color-primary;
					}
				}
			}
		}
	}
}
</style>
