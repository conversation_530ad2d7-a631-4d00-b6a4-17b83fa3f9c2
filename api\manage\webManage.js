import { request } from '~/utils/request'

//获取可用网站列表
export function getUsefulWebSite(data) {
	return request({
		url: '/retailer/webSite/getUsefulWebSite',
		method: 'post',
		data: data,
	})
}


//获取经销商详情
export function getRetailerUserInfo(data) {
	return request({
		url: '/retailer/user/getRetailerUserInfo',
		method: 'get',
		params: data,
	})
}


//获取用户在此项目权限
export function getRetailerUserPermission(data) {
	return request({
		url: '/retailer/user/getRetailerUserPermission',
		method: 'get',
		params: data,
	})
}

