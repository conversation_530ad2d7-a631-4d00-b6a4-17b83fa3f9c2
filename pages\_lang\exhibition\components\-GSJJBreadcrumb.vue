<template>
	<div class="breadcrumb">
		<template v-for="(item, index) in items">
			<div v-if="item.link">
				<nuxt-link :to="item.link">{{ item.text }}</nuxt-link>
			</div>
			<div v-else>{{ item.text }}</div>

			<div class="breadcrumb__divider" v-if="index !== items.length - 1">&gt;</div>
		</template>
	</div>
</template>

<script>
export default {
	name: "GSJJBreadcrumb",

	props: {
		items: Array,
	},
};
</script>

<style lang="scss" scoped>
.breadcrumb {
	margin-bottom: 12px;
	display: flex;
	align-items: center;
	line-height: 26px;
	font-size: 12px;
	a:hover {
		text-decoration: underline;
	}
	&__divider {
		margin: 0 5px;
		color: #999;
	}
}
</style>
