import { request } from "@/utils/request";

//获取所有网站风格
let listAllWebsiteStyle = function () {
    return request({
        url: '/app/retailer/websiteStyle/listAllWebsiteStyle',
        method: 'post',
    })
}

//获取国家列表
let getCountryList = function () {
    return request({
        url: '/retailer/address/getCountryList',
        method: 'get',
    })
}

//过滤名字
let isExistPro = function (name) {
    return request({
        url: '/app/systemProject/isExistPro?proName=' + name,
        method: 'get',
    })
}

//分页获取logo
let listLogo = function (data) {
    return request({
        url: '/app/retailer/sample/logo/listLogo',
        method: 'post',
        data: data
    })
}

export const previewWeb = {
    listAllWebsiteStyle,
    getCountryList,
    isExistPro,
    listLogo
}