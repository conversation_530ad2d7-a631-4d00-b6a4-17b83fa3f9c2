<template>
	<div class="fileCommon">
		<!-- My Creation -->
		<div class="wrap_Likes">
			<!-- Cliparts -->
			<div class="myPic" scrollbar>
				<div class="myUpload" scrollbar>
					<div class="upload-area" @click="uploadDialog = true">
						<div class="upload-img" v-if="device !== 'mb'">
							<div class="upload-icon">
								<img loading="lazy"
									src="https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20221129/2062bkrBZJcG.png"
									alt="" />
							</div>
							<div class="upload-text">
								<!-- {{ lang.uploadCliparts }}.<br />
								{{ lang.drag }} <span style="color: #2a96fa">{{ lang.browse }}.</span> -->
								{{ lang.upload }}
							</div>
						</div>
						<div class="upload-img" @click="uploadDialog = true" v-else>
							<div class="upload-icon">
								<b class="icon-a-icon-addzhuanhuan"></b>
							</div>
							<div class="upload-text">{{ lang.upAndRep }}</div>
						</div>
						<div style="height: 40px"></div>
					</div>
					<!-- 我的收藏 -->
					<div class="upload-item">
						<div class="top" @click="viewMyFavorite" :class="{ noImg: favoriteElementList.length < 4 }">
							<template v-if="favoriteElementList.length > 3">
								<div class="imgWrap linear-gradient-1" v-show="index < 4"
									v-for="(citem, index) in favoriteElementList" :key="index">
									<img loading="lazy" :src="citem.clipartUrl" :title="citem.clipartName"
										:alt="citem.clipartName" />
								</div>
							</template>
							<template v-else>
								<div class="imgWrap linear-gradient-1">
									<img src="https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20221209/203409BDN6TfQ4.png"
										alt="" />
								</div>
							</template>
						</div>
						<div class="bottom">
							<div class="folder-name">
								<div>
									{{ lang.myFavorite }}
								</div>
							</div>
						</div>
					</div>

					<div class="upload-item" v-for="(item, index) in userArtCategoryList" :key="index">
						<div class="top" @click="viewMyUpload(item)" :class="{ noImg: item.clipartList.length < 4 }">
							<template v-if="item.clipartList.length > 3">
								<div class="imgWrap linear-gradient-1" v-show="index < 4"
									v-for="(citem, index) in item.clipartList" :key="index">
									<img loading="lazy" :src="citem.clipartUrl" alt="" />
								</div>
							</template>
							<template v-else>
								<div class="imgWrap linear-gradient-1">
									<img src="https://customed-center.oss-accelerate.aliyuncs.com/web/quoteManage/20221209/203409BDN6TfQ4.png"
										alt="" />
								</div>
							</template>
						</div>
						<div class="bottom">
							<div class="folder-name" :title="item.categoryName">
								<div v-show="fileId !== item.id || firstMatchId === item.id">
									{{ item.categoryName }}
								</div>
								<el-input v-model="editFileName" v-show="fileId === item.id && firstMatchId !== item.id"
									@blur="filenameBlur" :class="'renameFocus' + index"> </el-input>
							</div>
							<div class="more" @click.stop="gengduoClick(index)" v-show="index !== -1 && index !== 0">
								<el-popover popper-class="more-popover" placement="bottom" trigger="click">
									<b class="icon-gengduo" slot="reference"
										:style="showGengduo === index ? 'color:#2A96FA' : '#999999'"></b>
									<div>
										<div class="more-popover-item" @click="renameFolder(item, index)">
											<a href="javascript:;"><b class="icon-a-T-editzhuanhuan"></b>{{ lang.rename
											}}</a>
										</div>
										<div class="more-popover-item" @click="deleteFolder(item)">
											<a href="javascript:;"><b class="icon-a-icon-deletezhuanhuan"></b>{{ lang.delete
											}}</a>
										</div>
									</div>
								</el-popover>
							</div>
						</div>
					</div>
				</div>
			</div>

			<!-- 我的收藏，我的上传 -->
			<div class="myCustomUploadBox" v-show="myCustomUploadStatus === 1">
				<div class="topBar">
					<strong class="title">{{ isFavorite ? lang.myFavorite : userArtFolder.categoryName }}</strong>
					<!-- <div class="close-icon" @click="showDefault">
						<b class="icon-a-icon-qxzhuanhuan"></b>
					</div> -->
					<div class="back-icon" @click="uploadStatus">
						<b class="icon-back"></b>
					</div>
				</div>
				<div class="con">
					<div class="controlTab" v-show="!isFavorite">
						<div class="left">
							<el-checkbox v-model="checkedAll" @change="toggleSelectAllUserPic">{{ lang.all }}</el-checkbox>
						</div>
						<div class="right">
							<div class="right-del" @click="delElement" v-show="showDel">
								<b class="icon-shanchu"></b>
							</div>
							<span class="moveTo">{{ lang.moveTo }}&nbsp;</span>
							<el-dropdown placement="bottom" @visible-change="changeDropDown" trigger="click"
								@command="handleFolderCommand">
								<div class="el-dropdown-link" :class="{ showExtend: showExtend }">
									<el-input readonly v-model="selectCategoryName" placeholder="Public Safety"
										suffix-icon="el-icon-arrow-down"> </el-input>
								</div>
								<el-dropdown-menu slot="dropdown" class="myDropDown1 myDropDown2">
									<el-dropdown-item :command="{ name: 'Create', id: -10 }">
										<div class="select-item isCreate">
											<div class="circle">
												<b class="icon-plus"></b>
											</div>
											<span>{{ lang.create }}</span>
										</div>
									</el-dropdown-item>
									<el-dropdown-item :command="item" v-for="item in userArtCategoryList" :key="item.id"
										v-show="userArtFolder.userCateId !== item.id">
										<div class="select-item" :class="{ selected: selectCategory === item.id }"
											@click="moveClick(item)">
											<div class="circle"></div>
											<span>{{ item.categoryName }}</span>
										</div>
									</el-dropdown-item>
								</el-dropdown-menu>
							</el-dropdown>
						</div>
					</div>
					<div class="boxWrap" scrollbar>
						<div class="box-item linear-gradient-1" v-for="(citem, index) in myUploadList" :key="index">
							<cliparts :item="citem" :clipartId="isFavorite ? citem.clipartId : citem.id"
								:clipartImage="citem.clipartUrl" :checked="isFavorite === true ? 0 : 1" :isCollection="1"
								:clipartName="citem.clipartName" @addImg="replaceImg"
								@addArtFavorite="addArtFavorite($event, 2)" @clipartChecked="clipartChecked"></cliparts>
						</div>
					</div>
				</div>
			</div>
		</div>

		<!-- 上传弹窗 -->
		<base-dialog v-model="uploadDialog" :width="device !== 'mb' ? '710px' : '92%'">
			<div class="uploadDialogCon">
				<div>
					<div class="top">
						<div><span v-show="device == 'mb'" style="color: red">*</span>{{ lang.uploadCliparts }}</div>
					</div>
					<div class="left">
						<div class="des">{{ lang.onlyUploaded }}</div>
					</div>
				</div>
				<div class="con" scrollbar>
					<div class="upload-box">
						<div class="plus">
							<b class="icon-a-icon-addzhuanhuan"></b>
						</div>
						<span>{{ lang.upload }}</span>
						<input type="file" ref="userFileInput" multiple accept="image/jpeg,image/png"
							@change="handleUserFileInput" />
					</div>
					<div class="upload-item" v-for="(item, index) in waitUploadFileList" :key="index">
						<div class="imgWrap linear-gradient-1">
							<img :src="item.imgUrl" alt="" />
						</div>
						<div class="close-icon" @click="deleteWaitUploadFile(index)">
							<b class="icon-a-icon-qxzhuanhuan"></b>
						</div>
						<div class="nameWrap">
							<span class="name" :title="item.imgName" v-show="!item.isImgName">{{ item.imgName }}</span>
							<el-input v-model="imgName2" v-show="item.isImgName" @blur="editImgNameBlur(item)"></el-input>
							<span class="edit" @click="editImgName(item)"><b class="icon-edit"></b></span>
						</div>
					</div>
				</div>
				<div class="category">
					<div class="left">{{ lang.category }}</div>
					<div class="right">
						<el-dropdown placement="bottom-start" trigger="click" @command="handleFolderCategory"
							:hide-on-click="hideOnClick">
							<div class="el-dropdown-link">
								<el-input readonly v-model="selectCategoryName"
									placeholder="Save to [My Upload]], or create your own category."></el-input>
							</div>
							<el-dropdown-menu class="myDropDown1">
								<el-dropdown-item :command="{ name: 'Create', id: -10 }">
									<div class="select-item isCreate" v-show="isCreate === false" @click="createClick">
										<div class="circle">
											<b class="icon-plus"></b>
										</div>
										<span>{{ lang.create }}</span>
									</div>
									<div class="select-item" v-show="isCreate === true">
										<el-input v-model="filename"></el-input>
										<el-button type="text" @click.stop="confirmCreateFolder">{{ lang.ok }}</el-button>
										<el-button type="text" @click="cancelCreateFolder">{{ lang.cancel }}</el-button>
									</div>
								</el-dropdown-item>
								<el-dropdown-item :command="item" v-for="item in userArtCategoryList" :key="item.id">
									<div class="select-item" :class="{ selected: selectCategory === item.id }">
										<div class="circle"></div>
										<span>{{ item.categoryName }}</span>
									</div>
								</el-dropdown-item>
							</el-dropdown-menu>
						</el-dropdown>
					</div>
				</div>
				<div class="btnGroup">
					<el-button @click="resetUserUploadForm">{{ lang.cancel }}</el-button>
					<el-button type="primary" @click="confirmUploadUserFile">{{ lang.comfirm }}</el-button>
				</div>
			</div>
		</base-dialog>

		<!--创建文件夹-->
		<base-dialog v-model="showCreateFolder" :width="device !== 'mb' ? '710px' : '92%'">
			<div class="createUploadFolderCon">
				<div>
					<div>{{ lang.categoryText }}</div>
					<input type="text" v-model="filename" />
				</div>
				<div class="btnGroup">
					<el-button @click="cancelCreateFolder">{{ lang.cancel }}</el-button>
					<el-button type="primary" @click="confirmCreateFolder" v-throttle>{{ lang.comfirm }}</el-button>
				</div>
			</div>
		</base-dialog>
	</div>
</template>

<script>
import cliparts from "@/components/MyDzxt/cliparts.vue";
import { getClipartCategoryList, favoriteClipart, editUserClipartCategory, getUserUploadClipartList, uploadUserClipart, deleteUserClipartCategory, getDefaultClipartList, getFavoriteClipartList, moveUploadClipart, deleteUserClipart } from "@/api/newDzxt";
import BaseDialog from "~/components/Quote/BaseDialog.vue";
import { uploadFile } from "~/utils/oss";
import dzMixin from "@/mixins/dzMixin";
export default {
	name: "fileCommon",
	components: {
		BaseDialog,
		cliparts,
	},
	mixins: [dzMixin],
	data() {
		return {
			userArtCategoryList: [],
			uploadDialog: false,
			userArtFolder: [],
			myUploadList: [], //获取上传元素列表
			checkedAll: false,
			cateId: 0,
			myCustomUploadStatus: 0,
			fileId: null,
			firstMatchId: null,
			editFileName: "",
			isFavorite: false,
			showExtend: false,
			selectCategoryName: "",
			fileListId: 0, //文件id
			elementListId: [], //元素id
			waitUploadFileList: [],
			imgName2: "",
			// selectFolderItem: "",
			filename: "",
			hideOnClick: true,
			isCreate: false,
			showCreateFolder: false,
			isImgName: 0,
			templatesName2: "",
			selectCategory: 0,
			name: "Favorites",
			editCitem: {},
			favoriteElementList: [], //收藏元素列表
			showDel: false,
			showGengduo: false
		};
	},
	computed: {
		lang() {
			return this.$store.getters.lang.design;
		},
		device() {
			return this.$store.state.device;
		},
		isLogin() {
			return this.$store.getters.isLogin;
		},
	},
	methods: {
		//删除上传元素
		delElement() {
			this.$confirm(this.lang.delUploadedElement, this.lang.tips, {
				confirmButtonText: this.lang.comfirm,
				cancelButtonText: this.lang.cancel,
				type: this.lang.warning,
			})
				.then(() => {
					this.myUploadList.forEach((item) => {
						if (item.checked === true) {
							this.elementListId.push(item.id);
						}
					});
					deleteUserClipart({
						idList: this.elementListId,
					}).then((res) => {
						this.checkedAll = false;
						this.showDel = false;
						this.getUserArtList();
					});
				})
				.catch(() => { });
		},
		gengduoClick(index) {
			this.showGengduo = index;
		},
		//获取所有元素文件夹
		getClipartList() {
			getClipartCategoryList().then((res) => {
				this.userArtCategoryList = res.data;
			});
		},
		//获取收藏元素列表
		getFavoriteClipartList() {
			return new Promise((resolve) => {
				getFavoriteClipartList({
					page: 1,
					pageSize: 99999,
				}).then((res) => {
					let favoriteElementList = res.data.records;
					favoriteElementList.forEach((item) => {
						item.clipartUrl = item.clipartUrl ? item.clipartUrl : "";
						item.isCollection = true;
					});
					this.favoriteElementList = favoriteElementList;
					resolve();
				});
			});
		},
		// 	//我的元素
		changeDropDown(val) {
			this.showExtend = val;
		},
		logoNameClick(e, citem) {
			this.editCitem = citem;
			this.templatesName2 = citem.templateName;
			citem.islogo = !citem.islogo;

			//获取焦点
			this.$nextTick(() => {
				let findParent = this.getCustomParentNode(e.target, "item-sign");
				let input = findParent.querySelector(".inner").querySelector("input");
				if (input) {
					input.focus();
				}
			});
		},
		// //上传文件夹元素全选
		toggleSelectAllUserPic(val) {
			this.myUploadList.forEach((item) => {
				item.checked = val;
			});
			if (val) {
				this.showDel = true;
			} else {
				this.showDel = false;
			}
		},
		clipartChecked() {
			this.checkedAll = this.myUploadList.every((checkbox) => checkbox.checked);
			let find = this.myUploadList.find((item) => item.checked === true);
			if (find) {
				this.showDel = true;
			} else {
				this.showDel = false;
			}
		},
		// //关闭弹窗
		showDefault() {
			this.status = 0;
			this.$emit("update:myDesignDialog", false);
			this.discardActiveObject();
		},
		discardActiveObject() {
			if (!this.fabricStage) {
				return false;
			}
			this.selectedImgColor = "";
			this.selectedImgReplaceColor = "";
			this.selectedElement = "";
			this.showImgColorPopover = false;
			this.fabricStage.discardActiveObject();
			this.fabricStage.requestRenderAll();
		},
		//点元素文件夹
		viewMyUpload(item) {
			this.isFavorite = false;
			this.userArtFolder = item;
			this.myUploadList = [];
			this.checkedAll = false;
			this.cateId = item.id;
			this.getUserArtList();
			this.resetUserUploadForm();
			this.myCustomUploadStatus = 1;
		},
		// 点我的收藏
		viewMyFavorite() {
			this.isFavorite = true;
			this.myUploadList = [];
			this.checkedAll = false;
			this.getFavoriteClipartList();
			this.myUploadList = this.favoriteElementList;
			this.resetUserUploadForm();
			this.myCustomUploadStatus = 1;
		},
		// //上传文件夹里面的元素
		getUserArtList() {
			if (this.cateId == null) {
				getDefaultClipartList({
					page: 1,
					pageSize: 999,
				}).then((res) => {
					let uploadList = res.data.records;
					uploadList.forEach((item) => {
						item.checked = false;
					});
					this.myUploadList = uploadList;
				});
			} else {
				getUserUploadClipartList({
					page: 1,
					pageSize: 999,
					cateId: this.cateId,
				}).then((res) => {
					let uploadList = res.data.records;
					uploadList.forEach((item) => {
						item.checked = false;
					});
					this.myUploadList = uploadList;
				});
			}
		},
		//rename
		renameFolder(item, index) {
			this.fileId = item.id;
			this.editFileName = item.categoryName;
			this.$nextTick(() => {
				const inputElement = document.querySelector(".renameFocus" + index).querySelector("input");
				if (inputElement) {
					inputElement.focus();
				}
			});
		},
		// //修改文件名
		filenameBlur() {
			if (this.editFileName === undefined) {
				this.fileId = -1;
				return;
			}
			editUserClipartCategory({
				id: this.fileId,
				categoryName: this.editFileName,
			}).then((res) => {
				this.fileId = -1;
				this.getClipartList();
			});
		},
		// //My Creation 删除文件夹
		deleteFolder(item) {
			this.$confirm(this.lang.delFolder, this.lang.tips, {
				confirmButtonText: this.lang.comfirm,
				cancelButtonText: this.lang.cancel,
				type: this.lang.warning,
			})
				.then(() => {
					deleteUserClipartCategory({
						id: item.id,
					}).then((res) => {
						this.getClipartList();
					});
				})
				.catch(() => { });
		},
		uploadStatus() {
			this.myCustomUploadStatus = 0;
			this.getClipartList();
		},
		//创建文件夹
		confirmCreateFolder() {
			editUserClipartCategory({
				categoryName: this.filename,
			}).then((res) => {
				this.showCreateFolder = false;
				this.getClipartList();
				//获取选中元素
				let checkedList = [];
				this.myUploadList.forEach((item) => {
					if (item.checked) {
						checkedList.push(item.id);
					}
				});
				if (checkedList.length > 0) {
					this.moveUploadClipart(checkedList, res.data);
				}
			});
			this.hideOnClick = !this.hideOnClick;
			this.isCreate = false;
		},
		// //取消
		cancelCreateFolder() {
            this.isCreate = false;
			this.showCreateFolder = false;
			this.hideOnClick = true;
			this.filename = "";
		},
		createClick() {
			this.isCreate = !this.isCreate;
			this.hideOnClick = false;
		},
		// //重置
		resetUserUploadForm() {
			this.waitUploadFileList = [];
			this.selectCategoryName = "";
			this.selectCategory = "";
		},
		// //上传弹窗编辑元素名
		editImgName(e, item) {
			this.imgName2 = item.imgName;
			item.isImgName = !item.isImgName;
			this.$nextTick(() => {
				let findParent = this.getCustomParentNode(e.target, "nameWrap");
				let input = findParent.querySelector(".inputFocus").querySelector("input");
				if (input) {
					input.focus();
				}
			});
		},
		editImgNameBlur(item) {
			item.imgName = this.imgName2;
			item.isImgName = !item.isImgName;
		},
		// //刪除上传的元素
		deleteWaitUploadFile(index) {
			this.waitUploadFileList.splice(index, 1);
		},
		//上传更新用户元素
		confirmUploadUserFile() {
			let categoryId;
			let find = this.userArtCategoryList.find((i) => i.categoryName === this.selectCategoryName);
			if (find) {
				categoryId = find.id;
			}
			this.concurRequest(
				this.waitUploadFileList.map((item) => item.imgFile),
				10
			)
				.then((res) => {
					let list = [];
					res.forEach((item, index) => {
						list.push({
							clipartName: this.waitUploadFileList[index].imgName,
							clipartUrl: item,
						});
					});
					return uploadUserClipart({
						clipartId: 0,
						cliparts: list,
						categoryId: categoryId ? categoryId : null
					});
				})
				.then((res) => {
					this.uploadDialog = false;
					this.resetUserUploadForm();
					this.getClipartList();
				});
		},
		beforeUpload(file) {
			const isLt10M = file.size / 1024 / 1024 < 10;
			const isUploadType = /\.(jpg|jpeg|png)$/.test(file.name.toLowerCase());
			if (!isLt10M) {
				this.$toast.error(this.lang.sizeError);
				return isLt10M;
			}
			if (!isUploadType) {
				this.$toast.error(this.lang.imgErrorDesign);
				return isUploadType;
			}
			return true;
		},
		handleUserFileInput(event) {
			let fileArr = Array.from(event.target.files);
			let checkFormat = [];
			//校验每一个文件的格式和大小
			fileArr.forEach(item => {
				checkFormat.push(this.beforeUpload(item))
			})
			//判断校验是否全部通过，不通过返回false
			if (!checkFormat.every(item => item)) {
				this.$refs.userFileInput.value = "";
				return false;
			}
			let arr = this.waitUploadFileList;
			for (let i = 0; i < fileArr.length; i++) {
				let imgUrl = this.getObjectURL(fileArr[i]),
					imgName = getFileSuffix(fileArr[i].name),
					imgFile = fileArr[i];
				arr.push({
					imgUrl,
					imgName,
					imgFile,
					isImgName: false
				});
			}
			this.$refs.userFileInput.value = "";
		},
		getObjectURL(file) {
			let url = null;
			if (window.createObjectURL !== undefined) {
				// basic
				url = window.createObjectURL(file);
			} else if (window.URL !== undefined) {
				// mozilla(firefox)
				url = window.URL.createObjectURL(file);
			} else if (window.webkitURL !== undefined) {
				// webkit or chrome
				url = window.webkitURL.createObjectURL(file);
			}
			return url;
		},
		concurRequest(urls, maxNum) {
			return new Promise((resolve) => {
				if (urls.length === 0) {
					resolve([]);
					return [];
				}
				const results = [];
				let index = 0;
				let count = 0;

				async function request() {
					if (index === urls.length) {
						return;
					}
					const i = index;
					const url = urls[index];
					index++;
					try {
						results[i] = await uploadFile(url);
					} catch (err) {
						results[i] = err;
					} finally {
						//判断所有请求是否已经完成
						count++;
						if (count === urls.length) {
							resolve(results);
						}
						request();
					}
				}
				const times = Math.min(maxNum, urls.length);

				for (let i = 0; i < times; i++) {
					request();
				}
			});
		},
		// //上传Category
		handleFolderCategory(item) {
			if (item.id === -10) {
				// this.selectFolderItem = "";
				this.filename = "";
				this.hideOnClick = false;
				return false;
			}
			this.selectCategoryName = item.categoryName;
			this.selectCategory = item.id;
			this.hideOnClick = true;
		},

		//元素文件夹Move to
		handleFolderCommand(item) {
			if (item.id === -10) {
				// this.selectFolderItem = "";
				this.filename = "";
				this.showCreateFolder = true;
				return false;
			}
			this.selectCategory = item.id;
		},
		moveUploadClipart(idList, cateId) {
			moveUploadClipart({
				idList, //选择的元素id
				cateId, //移动的文件夹id
			}).then(() => {
				this.getUserArtList();
				this.fileId = -1;
				this.checkedAll = false;
			});
		},
		//移动文件夹
		moveClick(item) {
			this.fileListId = item.id || null;
			this.myUploadList.forEach((item) => {
				if (item.checked === true) {
					this.elementListId.push(item.id);
				}
			});
			if (!this.elementListId.length) {
				alert(this.lang.please);
				return;
			}
			this.selectCategoryName = item.categoryName;
			this.moveUploadClipart(this.elementListId, this.fileListId);
		},
		//收藏元素
		addArtFavorite(item, type = 1) {
			if (!this.isLogin) {
				this.$store.commit("setLogin", "login");
				return false;
			}
			let clipartId;
			clipartId = item.id;
			if (type == 2) {
				clipartId = item.clipartId;
			}
			favoriteClipart({
				clipartId: clipartId,
			}).then(async (res) => {
				item.isCollection = item.isCollection ? false : true;
				if (type == 2) {
					await this.getFavoriteClipartList();
					this.myUploadList = this.favoriteElementList;
					this.$toast.success(res.message);
					this.$Bus.$emit('updateArtList', item);
				}
			});
		},
		async replaceImg({ src, property }) {
			this.$store.commit("design/set_loading", true);
			this.$store.commit("design/set_Replace", false);
			let selectedEle = await this.canvas.replaceImg(src, { artId: property });
			this.$Bus.$emit("triggerActiveItem", selectedEle);
			this.canvas.saveStateToHistory();
			this.$store.commit("design/set_loading", false);
		},
		update(val) {
			this.$emit("update:myDesignDialog", val);
		},
	},
	created() {
		this.getClipartList();
		this.getFavoriteClipartList();
	},
	mounted() { },
};
</script>

<style scoped lang="scss">
.wrap_Likes {
	text-align: center;
	font-size: 18px;
	padding: 10px;

	.subTitle_div2 {
		padding: 6px 35px 5px;
		font-size: 16px;
		font-family: Calibri;
	}

	.myPic {
		padding: 0 10px;
		max-height: 580px;

		.myUpload {
			display: grid;
			grid-template-columns: repeat(3, 1fr);
			grid-column-gap: 24px;
			grid-row-gap: 25px;

			.upload-area {
				min-width: 0;
				display: flex;
				flex-direction: column;

				.upload-img {
					border: 1px dashed #dadcdf;
					border-radius: 6px;
					text-align: center;
					flex-basis: calc(50% - 4px);
					flex-shrink: 0;
					flex-grow: 1;
					display: flex;
					align-items: center;
					flex-direction: column;
					justify-content: center;

					.upload-icon {
						img {
							width: 88px;
							height: 69px;
							object-fit: cover;
						}
					}

					.upload-text {
						font-size: 20px;

						@include respond-to(mb) {
							font-size: 16px;
						}
					}
				}
			}

			.upload-item {
				min-width: 0;
				display: flex;
				flex-direction: column;
				cursor: pointer;

				.top {
					flex: 1;
					position: relative;
					display: flex;
					flex-wrap: wrap;
					justify-content: center;
					align-items: center;
					aspect-ratio: 1;
					border: 1px solid #e6e6e6;
					border-radius: 6px;
					padding: 5px;

					.imgWrap {
						flex-basis: calc(50% - 4px);
						flex-shrink: 0;
						flex-grow: 0;
						margin: 2px;
						aspect-ratio: 1;
						display: flex;
						justify-content: center;
						align-items: center;

						img {
							height: 100%;
							width: 100%;
							object-fit: contain;
						}
					}
				}

				.top.noImg {
					.imgWrap {
						width: 100%;
						height: 100%;

						img {
							width: 100px;
							height: 100px;
						}
					}
				}

				.bottom {
					display: flex;
					justify-content: space-between;
					align-items: center;
					height: 40px;

					.folder-name {
						flex: 1;
						overflow: hidden;
						text-overflow: ellipsis;
						white-space: nowrap;
					}

					.more {
						flex-shrink: 0;
						padding: 4px;
						margin-left: 10px;
						background: #edf1f5;
						border-radius: 8px;
						cursor: pointer;
					}
				}
			}
		}
	}

	.templates_Cliparts {
		display: grid;
		grid-template-columns: repeat(5, 1fr);
		grid-column-gap: 14px;
		grid-row-gap: 15px;
	}
}

.uploadDialogCon {
	padding: 15px 30px 30px;

	.top {
		display: flex;
		justify-content: center;
		align-items: center;
		padding-bottom: 14px;
		border-bottom: 1px solid #e6e6e6;
		margin-bottom: 20px;

		b {
			flex: 0.7;
		}

		div {
			font-size: 18px;
			font-family: Calibri;
			font-weight: bold;
			color: #333333;
		}
	}

	.left {
		.title {
			margin-bottom: 10px;
			margin-left: -14px;
			font-weight: 700;
			font-size: 18px;

			span:first-child {
				color: #de3500;
			}
		}

		.des {
			color: #666666;
		}
	}

	.right {
		flex-shrink: 0;
		margin-left: 10px;

		.el-dropdown-link {
			background: #f2f2f2;
			padding: 4px;
			width: 80%;
			border-radius: 10px;

			::v-deep .el-input__inner {
				border-radius: 10px;
			}
		}

		span {
			margin-right: 10px;
		}
	}

	.con {
		overflow: hidden auto;
		display: grid;
		align-items: flex-start;
		grid-template-columns: repeat(4, 1fr);
		grid-column-gap: 10px;
		grid-row-gap: 10px;
		margin: 20px 0;
		max-height: 368px;

		.upload-box {
			position: relative;
			overflow: hidden;
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			aspect-ratio: 1;
			background: #ffffff;
			border: 1px dashed #cccccc;

			&:hover {
				border-color: #2a96fa;
			}

			input {
				position: absolute;
				left: 0;
				top: 0;
				right: 0;
				bottom: 0;
				opacity: 0;
				cursor: pointer;
			}

			.plus {
				font-size: 38px;
			}
		}

		.upload-item {
			overflow: hidden;
			position: relative;

			.imgWrap {
				display: flex;
				justify-content: center;
				align-items: center;
				aspect-ratio: 1;
			}

			.nameWrap {
				display: flex;
				margin-top: 10px;

				.name {
					overflow: hidden;
					flex: 1;
					margin-right: 10px;
					text-overflow: ellipsis;
					white-space: nowrap;
				}

				.edit {
					flex-shrink: 0;
					color: #666666;
					cursor: pointer;
				}
			}

			.close-icon {
				display: flex;
				justify-content: center;
				align-items: center;
				position: absolute;
				top: 0;
				right: 0;
				width: 24px;
				height: 24px;
				background: rgba(0, 0, 0, 0.4);
				border-radius: 0px 0px 0px 10px;
				cursor: pointer;

				b {
					color: #ffffff;
				}
			}
		}
	}

	.category {
		display: flex;
		align-items: center;

		.left {
			margin-right: 10px;
			font-weight: 700;
			font-size: 18px;
		}

		.right {
			flex: 1;

			.el-dropdown {
				width: 100%;
			}
		}
	}

	.btnGroup {
		display: flex;
		justify-content: center;
		align-items: center;
		margin: 30px 0 0;

		button {
			width: 130px;
		}
	}

	@include respond-to(mb) {
		padding: 17px 15px 20px;
		font-size: 12px;
		font-family: Arial;

		.top {
			padding-bottom: 0;
			border-bottom: 0;
			margin-bottom: 11px;
		}

		.con {
			grid-template-columns: repeat(3, 1fr);
			grid-column-gap: 6px;
			grid-row-gap: 21px;

			.upload-box {
				span {
					font-size: 15px;
					margin-top: 5px;
				}

				.plus {
					font-size: 24px;
				}
			}

			.upload-item {
				overflow: hidden;
				position: relative;

				.imgWrap {
					display: flex;
					justify-content: center;
					align-items: center;
					aspect-ratio: 1;
				}

				.nameWrap {
					align-items: center;
					margin-top: 6px;

					::v-deep .el-input__inner {
						height: 30px;
					}

					.edit {
						font-size: 15px;
					}
				}

				.close-icon {
					width: 14px;
					height: 14px;
					font-size: 12px;
					border-radius: 0px 0px 0px 5px;

					b {
						transform: scale(0.5);
					}
				}
			}
		}

		.category {
			display: flex;
			flex-direction: column;
			align-items: flex-start;

			.left {
				margin-bottom: 7px;
				font-weight: 700;
				font-size: 12px;
			}

			.right {
				width: 95%;
				margin-left: 0;

				.el-input {
					font-size: 12px;
				}

				.el-dropdown-link {
					width: 100%;
					border-radius: 5px;

					::v-deep .el-input__inner {
						border-radius: 5px;
					}
				}
			}
		}

		.btnGroup {
			margin: 15px 0 0;
		}
	}
}

.myDropDown2 {
	width: 9% !important;
}

.myDropDown1 {
	width: 29%;
	z-index: 6000 !important;

	.select-item {
		display: flex;
		align-items: center;

		&>span {
			flex: 1;
			overflow: hidden;
			white-space: nowrap;
			text-overflow: ellipsis;
		}

		.el-button:nth-of-type(2) {
			color: #666666;
		}

		.el-input {
			margin-right: 10px;
		}
	}

	.select-item.isCreate {
		.circle {
			background: #2a96fa;
			border-color: #2a96fa;
			color: #ffffff;
		}
	}

	.select-item.selected:not(.isCreate) .circle {
		background: #2a96fa;
		border-color: #2a96fa;

		&::after {
			content: "";
			width: 6px;
			height: 6px;
			background: #ffffff;
			position: absolute;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
			border-radius: 50%;
		}
	}

	.circle {
		flex-shrink: 0;
		position: relative;
		display: flex;
		justify-content: center;
		align-items: center;
		width: 14px;
		height: 14px;
		background: #ffffff;
		border: 1px solid #cccccc;
		border-radius: 50%;
		margin-right: 10px;
		font-size: 12px;
	}
}

.createUploadFolderCon {
	padding: 1rem;

	input {
		width: 100%;
		height: 40px;
		padding: 0 1rem;
		background: #ffffff;
		border: 1px solid #e6e6e6;
		border-radius: 10px;
		margin: 10px 0;
	}

	.btnGroup {
		display: flex;
		justify-content: center;
		align-items: center;
	}
}

.myCustomUploadBox {
	display: flex;
	flex-direction: column;
	overflow: hidden;
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: #ffffff;
	z-index: 2;

	.topBar {
		display: flex;
		align-items: center;
		justify-content: center;
		position: relative;
		font-size: 18px;
		height: 50px;
		border-bottom: 1px solid #ccc;

		.close-icon,
		.back-icon {
			position: absolute;
			top: 50%;
			right: 10px;
			transform: translateY(-50%);
			cursor: pointer;

			&::before {
				content: "";
				position: absolute;
				top: -10px;
				right: -10px;
				bottom: -10px;
				left: -10px;
			}

			b {
				font-size: 18px;
				font-weight: 700 !important;
				cursor: pointer;
			}
		}

		.back-icon {
			right: auto;
			left: 10px;
		}
	}

	.con {
		display: flex;
		flex-direction: column;
		overflow: hidden;
		flex: 1;
		padding: 10px;

		.controlTab {
			display: flex;
			justify-content: space-between;
			align-items: center;
			height: 40px;
			margin-bottom: 10px;

			.right {
				display: flex;
				align-items: center;

				.right-del {
					border-right: 1px solid #adadad;
					margin-right: 21px;
					padding-right: 19px;

					b {
						color: #adadad;
					}
				}

				.moveTo {
					margin: 0 5px;
				}

				@include respond-to(mb) {
					.right-del {
						margin-right: 8px;
						padding-right: 10px;

						b {
							font-size: 16px;
						}
					}

					.moveTo {
						font-size: 16px;
					}
				}
			}
		}

		.boxWrap {
			overflow: hidden auto;
			flex: 1;
			display: grid;
			align-content: flex-start;
			grid-template-columns: repeat(5, 1fr);
			grid-column-gap: 10px;
			grid-row-gap: 10px;

			.box-item {
				display: flex;
				justify-content: center;
				align-items: center;
				position: relative;
				cursor: pointer;
				aspect-ratio: 1;

				img {
					width: 112px;
					height: 112px;
					object-fit: contain;
				}

				.list-item-btn {
					display: flex;
					justify-content: center;
					align-items: center;
					opacity: 0;
					content: "";
					position: absolute;
					left: 0;
					right: 0;
					top: 0;
					bottom: 0;
					background-color: rgba(0, 0, 0, 0.5);
					transition: all 0.3s;

					&>div {
						display: flex;
						justify-content: center;
						align-items: center;
						margin: 0 4px;
						width: 48px;
						height: 48px;
						background: #2a96fa;
						border-radius: 50%;
						border: none;

						b {
							color: #ffffff;
							font-size: 24px;
						}
					}

					.love {
						background-color: #ce3d3a;
					}

					.elementName {
						position: absolute;
						top: 80%;
						left: 50%;
						transform: translate(-50%, -50%);
						font-size: 14px;
						background: #ffffff;
						// border: 1px solid rgba(0, 0, 0, 0.25);
						border-radius: 4px;
						padding: 3px 10px;
						max-width: 80%;
						text-overflow: ellipsis;
						overflow: hidden;
						white-space: nowrap;
					}
				}

				.clipart-img-icon {
					text-align: right;
					margin-right: 5px;
					margin-top: 5px;
					position: absolute;
					right: 0;
					top: 0;

					.icon-love {
						color: red;
					}
				}

				.checkbox {
					position: absolute;
					top: 10px;
					right: 10px;
				}

				@media (any-hover: hover) {
					&:hover {
						.list-item-btn {
							opacity: 1;
						}
					}
				}
			}
		}

		.btnWrap {
			padding: 1rem;
			display: flex;
			justify-content: flex-end;
			align-items: center;
		}
	}
}

.myCustomUploadBox.isFavorite {
	.con {
		.controlTab {
			display: none;
		}

		.checkbox {
			display: none;
		}
	}
}

::v-deep .el-drawer__header {
	margin-bottom: 0;
	padding: 0;
	position: absolute;
	right: 15px;
	top: 14px;
}

@include respond-to(mb) {
	.myDropDown2 {
		width: 40% !important;
	}

	::v-deep .el-input__icon {
		line-height: 33px;
	}

	.wrap_Likes {
		.myPic {
			padding: 5px;
			height: 380px;

			.myUpload {
				display: grid;
				grid-template-columns: repeat(2, 1fr);
				grid-column-gap: 10px;
				grid-row-gap: 15px;

				.upload-area {
					.upload-img {
						border: 1px dashed #dadcdf;
						border-radius: 6px;
						text-align: center;
						flex-basis: calc(50% - 4px);
						flex-shrink: 0;
						flex-grow: 1;
						display: flex;
						align-items: center;
						flex-direction: column;
						justify-content: center;

						.upload-icon {
							color: #999999;
							font-size: 35px;
						}
					}
				}

				.upload-item {
					min-width: 0;
					display: flex;
					flex-direction: column;
					cursor: pointer;

					.top {
						flex: 1;
						position: relative;
						display: flex;
						flex-wrap: wrap;
						justify-content: center;
						align-items: center;
						aspect-ratio: 1;
						border: 1px solid #e6e6e6;
						border-radius: 6px;
						padding: 5px;

						.imgWrap {
							flex-basis: calc(50% - 4px);
							flex-shrink: 0;
							flex-grow: 0;
							margin: 2px;
							aspect-ratio: 1;
							display: flex;
							justify-content: center;
							align-items: center;

							img {
								object-fit: contain;
							}
						}
					}

					.top.noImg {
						.imgWrap {
							width: 100%;
							height: 100%;

							img {
								width: 100px;
								height: 100px;
							}
						}
					}

					.bottom {
						display: flex;
						justify-content: space-between;
						align-items: center;
						height: 40px;

						.folder-name {
							flex: 1;
							overflow: hidden;
							text-overflow: ellipsis;
							white-space: nowrap;

							.el-input {
								margin-top: 1px;
							}

							::v-deep .el-input__inner {
								height: 30px;
							}
						}

						.more {
							flex-shrink: 0;
							padding: 4px;
							margin-left: 10px;
							background: #edf1f5;
							border-radius: 8px;
							cursor: pointer;
						}
					}
				}
			}
		}

		.templates_Cliparts {
			display: grid;
			grid-template-columns: repeat(3, 1fr);
		}
	}

	.myCustomUploadBox {
		.boxWrap {
			display: grid;
			grid-template-columns: repeat(3, 1fr) !important;
			grid-column-gap: 5px;
			grid-row-gap: 5px;
		}

		b {
			font-size: 12px;
		}

		::v-deep .el-input__inner {
			height: 30px;
		}
	}

	::v-deep .base-dialog .base-dialog-model-con {
		border-radius: 10px;

		.createUploadFolderCon {
			.categoryFolderCon {
				display: flex;
				justify-content: space-between;

				div:nth-of-type(1) {
					font-size: 20px;
					font-family: Arial;
					font-weight: bold;
					margin-bottom: 18px;
				}

				b {
					font-size: 10px;
				}
			}

			.el-button {
				width: 110px;
				margin-top: 5px;
			}
		}
	}

	.myDropDown1 {
		width: 84%;
	}
}</style>
