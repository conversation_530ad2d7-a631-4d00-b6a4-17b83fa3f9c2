<template>
	<div class="lyCardListTabs">
		<div class="ul" :class="isBracelet ? 'ulBracelet' : ''">
			<div v-for="(item, index) in model.list" :key="'lyCardListTabsUlDiv' + index" class="li" :class="baseModel.radio == index ? 'liOn' : ''" @click="cardTabs(index)">
				<div class="rowOne" :class="isBracelet ? 'rowOneBracelet' : ''">
					<el-radio-group v-model="baseModel.radio">
						<el-radio :label="index" />
					</el-radio-group>
					<div class="p1">{{ item[model.field.title] }}</div>
				</div>
				<div class="rowTwo">{{ item[model.field.desc] }}</div>

				<div v-if="item.labelText" class="hot" :style="'background:' + item.labelColor">{{ item.labelText }}</div>
			</div>
		</div>
	</div>
</template>
<script>
/*
卡片列表选项卡
更多文档：https://www.yuque.com/chaojigang-eu86m/adyym3/ch59hfcdbsfv9lcs
语雀密码：itv8
*/
import { baseModel } from "@/assets/quote/entity/LyCardListTabs";

import { Common } from "@/utils/common";
export default {
	components: {},
	props: {
		model: {
			default: {
				list: [], //卡片列表
				//映射字段
				field: {
					title: "title",
					desc: "desc",
				},
			},
			type: Object,
		},
	},
	data() {
		return {
			baseModel: new baseModel(),
			isBracelet: false, //是否是手环报价    默认false  true||是手环 样式发生变化
		};
	},
	mounted() {
		if (this.model.tabs != null) {
			this.cardTabs(this.model.tabs);
		}
		this.initCss();
	},
	created() {},
	methods: {
		//初始化样式
		initCss() {
			if (this.$route.path.indexOf("printed-silicone-wristbands") > -1) {
				this.isBracelet = true;
			} else {
				this.isBracelet = false;
			}
		},
		cardTabs(index) {
			if (Common.stopClick(200)) {
				this.baseModel.radio = index;
				this.$forceUpdate();
				this.$emit("change", this.model.list[index], index);
			}
		},
		refresh() {},
	},
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
$color-blue: #0066cc;
//垂直水平居中
@mixin centerCenter {
	display: flex;
	justify-content: center;
	align-items: center;
}
//三个点
@mixin diandiandian {
	display: -webkit-box;
	overflow: hidden;
	white-space: inherit;
	text-overflow: ellipsis;
	-webkit-line-clamp: 3;
	-webkit-box-orient: vertical;
	word-break: break-all;
}
//hot圆圈
@mixin hot {
	min-width: 36px;
	height: 16px;
	border-radius: 8px;
	background: -o-linear-gradient(right, #fd1e02, #fe621d);
	background: -moz-linear-gradient(right, #fd1e02, #fe621d);
	background: linear-gradient(to right, #fd1e02, #fe621d);
	color: #fff;
	font-size: 10px;
	padding: 0 8px;
}

.lyCardListTabs {
	.ul {
		display: grid;
		grid-template-columns: repeat(3, minmax(50px, 1fr));
		flex-wrap: wrap;
		.li {
			background: #f8f8f8;
			border-radius: 4px;
			margin: 0 15px 8px 0;
			border: 1px solid #e4e4e4;
			padding: 8px;
			cursor: pointer;
			position: relative;
			.rowOne {
				display: flex;
				color: #333333;
				font-size: 16px;
				font-weight: Bold;
				width: 80%;

				.p1 {
					@include diandiandian;
				}
			}
			.rowOneBracelet {
				width: 100%;
			}

			.rowTwo {
				padding-left: 28px;
				font-size: 14px;
				color: #666666;
				width: 80%;
			}
			.hot {
				position: absolute;
				right: 15px;
				top: calc(50% - 10px);
				@include hot;
				@include centerCenter;
			}
			@include respond-to(ipad) {
				.rowOne {
					width: auto;
				}
				.hot {
					right: -5px;
					top: -9px;
				}
			}
			@media (max-width: 1700px) {
				.rowOne {
					width: auto;
				}
				.hot {
					right: -5px;
					top: -9px;
				}
			}
		}
		.liOn,
		.li:hover {
			background: #d5e6f6;
			border: 1px solid $color-blue;
			.rowOne {
				color: $color-blue;
			}
			.rowTwo {
				color: $color-blue;
			}
		}
	}
	.ulBracelet {
		grid-template-columns: repeat(2, minmax(50px, 1fr));
	}
}

@media screen and (max-width: 1000px) {
	.lyCardListTabs .ul .li {
		margin: 0 5px 5px 0;
		padding: 8px 2px 8px 5px;
		.hot {
			right: 2px;
			top: 2px;
			height: 14px;
			font-size: 8px;
			padding: 0 0px;
			min-width: 32px;
		}
		.rowOne {
			font-size: 13px;
			width: 100%;
			.p1 {
				font-size: 13px;
			}
		}
		.rowTwo {
			font-size: 12px;
			width: 100%;
			padding-left: 18px;
		}
	}
}
</style>
<style rel="stylesheet/scss" lang="scss">
/**非业务样式**/
.lyCardListTabs {
	.li {
		.rowOne {
			.el-radio {
				margin-right: 0;
			}
			.el-radio__label,
			.el-radio__input.is-checked + .el-radio__label {
				display: none;
			}
			.el-radio-group {
				display: flex;
				align-items: center;
				margin-right: 8px;
				.el-radio__inner {
					width: 19px;
					height: 19px;
				}
				.el-radio__inner::after {
					width: 6px;
					height: 6px;
				}
			}
		}
	}
}
@media screen and (max-width: 1000px) {
	.lyCardListTabs .li {
		.rowOne {
			.el-radio-group {
				.el-radio__inner {
					width: 12px;
					height: 12px;
				}
			}
		}
	}
}
</style>
