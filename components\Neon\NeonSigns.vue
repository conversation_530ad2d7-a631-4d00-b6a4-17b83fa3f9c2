<template>
	<article class="neon"
			 id="neon"
			 :neonScrollBar="!pageQuote"
			 :style="{'--headH': isIframe ? '0px':4+'vh','--topH':topH+'px'}">
		<section class="content">
			<section class="top"
					 id="neonTop">
				<div class="grid-box"
					 v-if="!customTitle">
					<div class="part">
						<div class="stars">
							<b v-for="item in 5"
							   :key="item"
							   class="icon-star"></b>
						</div>
						<div class="num">4.9</div>
					</div>
					<div class="part">
						<div class="pl">
							<b class="icon-a-icon-FreeFastShippingzhuanhuan"></b>
						</div>
						<div class="num">
							{{ lang.neon.fea2 }}
						</div>
					</div>
					<div class="part">
						<div class="pl">
							<b class="icon-a-icon-FullWarrantyzhuanhuan"></b>
						</div>
						<div class="num">
							{{ lang.neon.fea3 }}
						</div>
					</div>
					<div class="part">
						<div class="pl">
							<b class="icon-a-icon-100EcoFriendlyzhuanhuan"></b>
						</div>
						<div class="num">
							{{ lang.neon.fea4 }}
						</div>
					</div>
				</div>
				<div class="custom_title"
					 v-else>
					<span>{{ customTitle }}</span>
				</div>
			</section>
			<template name="fade">
				<section class="left"
						 id="animation-box"
						 ref="csbTarget"
						 :class="{'isFlow': isFlow }">
					<div class="sticky"
						 :class="{ showMask: checking }">
						<TextDraggableCanvas ref="textDraggableCanvas"
											 :currentFont="currentFont"
											 :currentFontSize="currentFontSize"
											 :priceData="priceData"
											 :currentRound="currentRound"
											 :selectedFontData="selectedFontData"
											 :currentSampleData="currentSampleData"
											 :neonSampleData="neonSampleData"
											 :needCanvas="needCanvas"
											 :hiddenPrice="false"
											 :isText="isText"
											 :textDesign="textDesign"
											 :zoneRatio.sync="zoneRatio"
											 :selectParamId.sync="selectParamId"
											 :paramIdList.sync="paramIdList"
											 :isImgTemplate="isImgTemplate"
											 :neonDiscount="neonDiscountData?.priceInfo"
											 :unitConvertRatio="unitConvertRatio"
											 :unitSign="unitSign"
											 :customSizeFlag="customSizeFlag"
											 :nowNeonAddFreightVisable.sync="nowNeonAddFreightVisable"
											 :uploadArtwork="artworkImg"
											 :artworkInfo="artworkInfo"
											 :tabName="tabName"
											 :isBack="isBack"
											 @getImgUrl="(v) => canvasImgUrl = v"
											 @currentTemplate="currentTemplateFun"
											 @shopAllFun="shopAllFun"
											 @getFinaSize="getFinaSize">
						</TextDraggableCanvas>
						<div class="normalTips"
							 :class="{'is-design': tabName != 'Upload'}">
							<p>
								{{ tabName == 'Upload' ? lang.neon.canvasNormalTips2 : lang.neon.canvasNormalTips }}
							</p>
						</div>
					</div>
				</section>

			</template>
			<section neonScrollBar
					 class="right">
				<div class="backToDesign"
					 @click="backToDesign"
					 v-if="tabName == 'Upload'">
					<i class="el-icon-close"></i>
				</div>
				<div class="opts"
					 v-show="optsShow()">
					<div class="tips">
						<p v-if="langObj.countryCode !== 'de'">
							<strong>{{ '"' + lang.neon.tips1 + '"' }}</strong>{{ lang.neon.tips2 }}<strong>{{ '"' + lang.neon.tips3 + '",' }}</strong>{{ ' ' + lang.neon.tips4 }}
						</p>
						<p v-else>
							{{ "Eigenes Design? Laden Sie dieses einfach hoch! Falls Sie kein eigenes Design haben, dann starten Sie mit einer unserer Vorlagen." }}
						</p>
						<button class="btn"
								@click="uploadTo">
							{{ lang.Upload }}
						</button>
					</div>
					<div class="hr"></div>
				</div>
				<div class="steps"
					 :class="{ showDetails: showDetails }">
					<div v-for="(item, index) in filterGeneralData(generalData)"
						 :key="item.id"
						 class="stepsBox"
						 :id="item.paramName"
						 :ref="`${filterRef(item.paramName)}`"
						 :class="{ 'step-active': item.paramName == currentStep }"
						 :style="tabName == 'Upload' && item.paramName == 'Your Text'
                ? 'padding-bottom:0'
                : ''
              ">
						<!-- 字体 -->
						<section v-if="item.paramName == 'Your Text' && tabName == 'Enter Your Text'"
								 class="step artwork yourText">
							<div class="title-line"
								 v-if="!isImgTemplate || tabName == 'Upload'">
								<TitlePine @closeActive="shutMask"
										   :title="formatStepIndex(index + 1)">
                  <span v-if="tabName == 'Upload'"
						class="h">{{
						  lang.neon.uploadTitle
					  }}</span>
									<span v-else
										  class="h">{{ item.alias }}</span>
								</TitlePine>
							</div>
							<div class="chooseTemplates">
								<template v-for="(it, idx) in selectedFontData">
									<div class="fontOptionLine"
										 :key="idx">
										<span v-if="!fdNeonType">{{ lang.neon.changeTxt }} {{ idx + 1 }}</span>
										<span v-else>{{ '1-1 ' + lang.neon.enterYourCustomText }}</span>
										<FontParam ref="fontParam"
												   v-model="selectedFontData[idx]"
												   :isText="isText"
												   :textDesign="textDesign"
												   :data_list="generalData"
												   :fdNeonType="fdNeonType"
												   @setTextAlign="setTextAlign($event,idx)"
												   @click.native="clickTextFun(item)"
												   @changeColor="changeColor"
												   @changeFont="changeFont"
												   @input="changeInput"
												   @change="changeFontOption($event)"/>
									</div>
								</template>
								<NeonButton v-if="item.paramName == currentStep"
											style="margin-top: 10px"
											small
											:disabled="disabledNext(item)"
											@click="nextStepFun(item)">
									<span>{{ lang.next }}</span>
								</NeonButton>
							</div>
						</section>
						<!-- 上传 -->
						<section v-show="item.paramName == 'Your Text' && tabName == 'Upload'"
								 class="step yourText">
							<div class="title-line"
								 v-if="!isImgTemplate || tabName == 'Upload'">
								<TitlePine @closeActive="shutMask"
										   :title="formatStepIndex(index + 1)">
                  <span v-if="tabName == 'Upload'"
						class="h">{{
						  lang.neon.uploadTitle
					  }}</span>
									<span v-else
										  class="h">{{ item.alias }}</span>
								</TitlePine>
							</div>
							<div class="uploadTips">
								{{ lang.neon.uploadTips }}
							</div>
							<div class="uploadTabs"
								 @click="clickFontParamUpload(item)">
								<NeonUpload ref="uploadCom"
											:uploadFileList="uploadFileList"
											@changeImgSrc="previewImgFromFiles"
											@getFile="getFile">
								</NeonUpload>
								<NeonButton v-if="item.paramName == currentStep"
											style="margin-top: 10px"
											small
											:disabled="disabledNext(item)"
											@click="nextStepFun(item)">
									<span>{{ lang.next }}</span>
								</NeonButton>
							</div>
						</section>
						<!-- 模板颜色 -->
						<section v-if="item.paramName == 'Designs Or Upload Your Artwork' && tabName !== 'Upload'"
								 class="step designsOrUploadYourArtwork">
							<div class="title-line"
								 v-if="isImgTemplate">
								<TitlePine @closeActive="shutMask"
										   :title="formatStepIndex(index + 1)">
                  <span v-if="tabName == 'Upload'"
						class="h">{{
						  lang.neon.uploadTitle
					  }}</span>
									<span v-else
										  class="h">{{ item.alias }}</span>
								</TitlePine>
							</div>
							<div class="templateColorLine">
								<div class="optionContent templateColor"
									 v-if="!isText">
									<span>{{ lang.neon.tempColor }}</span>
									<HasimgCardType2 :data_list="neonSampleData"
													 v-model="selectedData['Designs Or Upload Your Artwork']"
													 :showText="false"
													 :columns="3"
													 @change="changeTemplateColor($event, item)"></HasimgCardType2>
								</div>
								<NeonButton v-if="item.paramName == currentStep"
											style="margin-top: 10px"
											small
											:disabled="disabledNext(item)"
											@click="nextStepFun(item)">
									<span>{{ lang.next }}</span>
								</NeonButton>
							</div>
						</section>
						<section :class="{ 'step-active': item.paramName == currentStep }"
								 v-if="item.paramName == 'Select Type'"
								 id="anchor-point"
								 class="step selectType">
							<div class="title-line">
								<TitlePine @closeActive="shutMask"
										   :title="formatStepIndex(index + stepNum)"
										   :tips="item.tips">
									<span>{{ formatStepTitle(item.paramName, item.alias) }}</span>
								</TitlePine>
							</div>
							<HasimgCardMultiple :columns="3"
												:columnsMb="2"
												:reverse="true"
												:iconTips="true"
												:bindName="item.paramName"
												:data_list="item.childList.filter(z => !z.isHiddenType)"
												v-model="selectedData[item.paramName]"
												@change="noValueFun($event,item.paramName)">
							</HasimgCardMultiple>

							<NeonButton v-if="item.paramName == currentStep"
										style="margin-top: 10px"
										small
										:disabled="disabledNext(item)"
										@click="nextStepFun(item)">
								<span>{{ lang.next }}</span>
							</NeonButton>
						</section>
						<section :class="{ 'step-active': item.paramName == currentStep }"
								 v-if="item.paramName == 'What Size Would You Like?'"
								 class="step sizeLikeStep">
							<div class="title-line">
								<TitlePine @closeActive="shutMask"
										   :title="formatStepIndex(index + stepNum)"
										   :tips="item.tips">
									<span>{{ item.alias }}</span>
								</TitlePine>
							</div>
							<div class="upload-size">
								<p class="upload-size-title">{{ lang.neon.customSize }}</p>
								<div class="size-tabs">
									<div v-if="!isCustomSizeInput">
										<input type="range"
											   v-model="artworkInfo.width"
											   @input="whatSizeWouldYouLikeFun(item, 'range', $event)"
											   name="customSize"
											   :min="imgMinWidth"
											   max="300"
											   id="customSize"
											   :style="{'--value': artworkInfo.width, '--min': imgMinWidth, '--max': 300 }">
									</div>

									<el-input v-else
											  ref="whatSize"
											  v-model="artworkInfo.width"
											  type="number"
											  @input="debounceLimitSize"
											  @focus="whatSizeWouldYouLikeFun(item)"
											  :placeholder="lang.neon.Size || 'Size'">
									</el-input>
								</div>
								<span class="size-bottom">
                  <span class="size-detail">
                    <span v-show="selectedData['What Size Would You Like?']">
                      <span>{{ computedArtwork }}</span> /
                      <span>{{ computedArtworkInfo }}</span>
                    </span>
					  <!-- {{ artworkInfo.width + ' / ' +  artworkInfo.height  + ' '}} -->
                  </span>
                  <span class="size-btn"
						@click="isCustomSizeInput = !isCustomSizeInput">
                    {{ isCustomSizeInput ? lang.neon.back : lang.neon.enterSizes }}
                  </span>
                </span>
							</div>
							<div class="sizeBottomTips">
								<p><i class="el-icon-warning-outline"></i>{{ lang.neon.minimumSize + '15" × NaN" / 38cm × NaNcm.' }}</p>
								<p v-show="neonNeedCrop"><i class="el-icon-warning-outline"></i>
									{{ lang.neon.neonCutTips }}</p>
							</div>
							<NeonButton v-if="item.paramName == currentStep"
										style="margin-top: 10px"
										small
										:disabled="disabledNext(item)"
										@click="nextStepFun(item)">
								<span>{{ lang.next }}</span>
							</NeonButton>
						</section>
						<section :class="{ 'step-active': item.paramName == currentStep }"
								 v-if="item.paramName == 'Budget Idea'"
								 class="step">
							<div class="title-line">
								<TitlePine @closeActive="shutMask"
										   :title="formatStepIndex(index + stepNum)"
										   :tips="item.tips">
									<span>{{ item.alias }}</span>
								</TitlePine>
							</div>
							<NeonProgressSelect ref="ProgressSelect"
												:minMax="[200, 2000]"
												:part="4"
												:mesh="5"
												mbWidth="90%"
												:tagSign="currentCurrency.symbol"
												@changeValue="(v) => selectedData['Budget Idea'] = v"></NeonProgressSelect>
							<NeonButton v-if="item.paramName == currentStep"
										style="margin-top: 10px"
										small
										:disabled="disabledNext(item)"
										@click="nextStepFun(item)">
								<span>{{ lang.next }}</span>
							</NeonButton>
						</section>
						<section :class="{ 'step-active': item.paramName == currentStep }"
								 v-if="item.paramName == 'Select Size'"
								 class="step selectSize">
							<div class="title-line">
								<TitlePine @closeActive="shutMask"
										   :title="formatStepIndex(index + stepNum)"
										   :tips="item.tips">
									<span>{{ lang.Select + ' ' + item.alias }}</span>
								</TitlePine>
							</div>
							<SelectSize ref="SelectSize"
										v-model="selectedData[item.paramName]"
										:customSize="customSize"
										:size_list="item.childList"
										:currentFontSize="currentFontSize"
										:tabName="tabName"
										:selectedFontData="selectedFontData"
										:zoneRatio="zoneRatio"
										:isText="isText"
										:neonSampleData="neonSampleData"
										:neonDiscount="neonDiscountData?.priceInfo"
										:currentTemplate="currentTemplate"
										:unitConvertRatio="unitConvertRatio"
										:unitSign="unitSign"
										:sliderOptions="sliderOptions"
										@change="clickFunForSize($event, item)"
										@changeCustomSize="changeCustomSize($event)"></SelectSize>
							<NeonButton v-if="item.paramName == currentStep"
										style="margin-top: 10px"
										small
										:disabled="disabledNext(item)"
										@click="nextStepFun(item)">
								<span>{{ lang.next }}</span>
							</NeonButton>
						</section>
						<section :class="{ 'step-active': item.paramName == currentStep }"
								 v-if="item.paramName == 'Indoor or Outdoor'"
								 id="anchor-point"
								 class="step indoororOutdoor">
							<div class="title-line">
								<TitlePine @closeActive="shutMask"
										   :title="formatStepIndex(index + stepNum)"
										   :tips="item.tips">
									<span>{{ formatStepTitle(item.paramName, item.alias) }}</span>
								</TitlePine>
							</div>
							<HasimgCard :columns="3"
										:columnsMb="2"
										:reverse="true"
										:iconTips="true"
										:bindName="item.paramName"
										:data_list="item.childList"
										v-model="selectedData[item.paramName]"
										@change="noValueFun">
							</HasimgCard>
						</section>
						<section :class="{ 'step-active': item.paramName == currentStep }"
								 v-if="item.paramName == 'Power Adapter'"
								 class="step powerAdapte">
							<div class="title-line">
								<TitlePine @closeActive="shutMask"
										   :title="formatStepIndex(index + stepNum)">
									<span>{{ formatStepTitle(item.paramName, item.alias) }}</span>
								</TitlePine>
							</div>
							<div class="subtitle-line powerAdapter">
								<span>{{ item.tips }}</span>
							</div>
							<NoimgCard v-model="selectedData[item.paramName]"
									   :data_list="item.childList"
									   @change="noValueFun"
									   align="center"></NoimgCard>
						</section>
						<section :class="{ 'step-active': item.paramName == currentStep }"
								 v-if="item.paramName == 'Select Your Backboard Color'"
								 class="step chooseYourBackboardColor">
							<div class="title-line">
								<TitlePine @closeActive="shutMask"
										   :title="formatStepIndex(index + stepNum)">
									<span>{{ formatStepTitle(item.paramName, item.alias) }}</span>
								</TitlePine>
							</div>
							<HasimgCard :bindName="item.paramName"
										:data_list="item.childList"
										:showTag="true"
										v-model="selectedData[item.paramName]"
										@change="noValueFun">
							</HasimgCard>
						</section>
						<section :class="{ 'step-active': item.paramName == currentStep }"
								 v-if="item.paramName == 'Select Backboard'"
								 class="step selectBackboard">
							<div class="title-line">
								<TitlePine @closeActive="shutMask"
										   :title="formatStepIndex(index + stepNum)">
									<span>{{ formatStepTitle(item.paramName, item.alias) }}</span>
								</TitlePine>
							</div>
							<HasimgCard :bindName="item.paramName"
										aspect="242/182"
										:showTag="true"
										:data_list="item.childList.filter(x => !x.isHidden2)"
										v-model="selectedData[item.paramName]"
										@picDialogFun="picDialogFun"
										@zoomPicFun="zoomPicNeon($event,'video')"
										@change="noValueFun($event,item.paramName)">
							</HasimgCard>
						</section>
						<section :class="{ 'step-active': item.paramName == currentStep }"
								 v-if="item.paramName == 'Select Remote and Dimmer'"
								 class="step remoteAndDimmer">
							<div class="title-line">
								<TitlePine @closeActive="shutMask"
										   :title="formatStepIndex(index + stepNum)">
									<span>{{ formatStepTitle(item.paramName, item.alias) }}</span>
								</TitlePine>
							</div>
							<HasimgCard :bindName="item.paramName"
										:columns="3"
										:columnsMb="2"
										:data_list="item.childList"
										v-model="selectedData[item.paramName]"
										@change="noValueFun">
							</HasimgCard>
						</section>
						<section :class="{ 'step-active': item.paramName == currentStep }"
								 v-if="item.paramName == 'Greeting Card'"
								 class="step remoteAndDimmer">
							<div class="title-line">
								<TitlePine @closeActive="shutMask"
										   :title="formatStepIndex(index + stepNum)">
									<span>{{ formatStepTitle(item.paramName, item.alias) }}</span>
								</TitlePine>
							</div>
							<div class="subtitle-line greeting">
								<span>{{ item.tips }}</span>
							</div>

							<HasimgCard class="greetingCard"
										:bindName="item.paramName"
										aspect="215/150"
										:columns="3"
										:data_list="item.childList"
										v-model="selectedData[item.paramName]"
										rotateYAnimation
										@change="noValueFun($event,item.paramName)">
							</HasimgCard>

							<div class="greetingComments"
								 v-show="showGreetingCard"
								 :style="{'--borderColor': greetingNoPassFlag && !greetingTextLine ? '#ee4866' : '#DBDBDB'}">
								<div class="arrow-top"
									 :style="{'left': offsetLeftForCustomCard }"></div>
								<div class="order-box">
									<el-input type="textarea"
											  :autosize="{ minRows: 3, maxRows: 5 }"
											  :placeholder="lang.neon.greetingNoteTips"
											  @focus.self="clickNotes(item)"
											  @input="validateEmoji($event, 'greetingTextLine')"
											  v-model="greetingTextLine"
											  resize="none">
									</el-input>
								</div>

								<NeonButton style="margin-top: 10px"
											small
											:disabled="disabledNext(item)"
											@click="nextStepFun(item)">
									<span>{{ lang.next }}</span>
								</NeonButton>
							</div>

						</section>
						<section :class="{ 'step-active': item.paramName == currentStep }"
								 v-if="item.paramName == 'Hanging Options'"
								 class="step hangingOptions">
							<div class="title-line">
								<TitlePine @closeActive="shutMask"
										   :title="formatStepIndex(index + stepNum)">
									<span>{{ formatStepTitle(item.paramName, item.alias) }}</span>
								</TitlePine>
							</div>
							<HasimgCard :columns="3"
										:showPreviewIcon="true"
										:bindName="item.paramName"
										:data_list="filterHangingOption(item.childList)"
										v-model="selectedData[item.paramName]"
										@change="noValueFun">
							</HasimgCard>
						</section>
						<section :class="{ 'step-active': item.paramName == currentStep }"
								 v-if="item.paramName == 'Outdoor Usage'"
								 class="step outdoorUsage">
							<div class="title-line">
								<TitlePine @closeActive="shutMask"
										   :title="formatStepIndex(index + stepNum)">
									<span>{{ item.alias }}</span>
								</TitlePine>
							</div>
							<HasimgCard :bindName="item.paramName"
										:data_list="item.childList"
										v-model="selectedData[item.paramName]"
										@change="noValueFun">
							</HasimgCard>
						</section>
						<section :class="{ 'step-active': item.paramName == currentStep }"
								 v-if="item.paramName == 'Select Turnaround Time'"
								 class="step turnaroundTime">
							<div class="title-line">
								<TitlePine @closeActive="shutMask"
										   :title="formatStepIndex(index + stepNum)">
									<span>{{ formatStepTitle(item.paramName, item.alias) }}</span>
								</TitlePine>
							</div>
							<div class="subtitle-line turnaroundTime">
								{{ lang.hourstext }}
							</div>
							<NeonDateBox v-model="selectedData[item.paramName]"
										 :data_list="item.childList"
										 :priceInfo="priceData"
										 @change="noValueFun($event,item.paramName)">
							</NeonDateBox>

							<div class="noteBox">
								<el-input type="textarea"
										  :autosize="{ minRows: 3, maxRows: 5 }"
										  :placeholder="lang.neon.turnaroundTimeNoteTips"
										  @focus.self="clickNotes(item)"
										  @input="validateEmoji($event, 'customComment')"
										  v-model="customComment"
										  resize="none">
								</el-input>
							</div>

							<div class="priceBox"
								 v-if="tabName != 'Upload' && device != 'pc'">
								<div class="original">
                  <span class="label">
                    {{ lang.originalAmount }}
                  </span>
									<span class="value">
                    <CCY-rate :decimal="2"
							  :price="priceData.totalPrice?priceData.totalPrice / (1-neonDiscountData?.priceInfo?.unitPercent):0"></CCY-rate>

                  </span>
								</div>
								<div class="final">
                  <span class="label">
                    {{ lang.amountAfterDiscount }}
                  </span>

									<span class="value">
                    <CCY-rate :decimal="2"
							  :price="priceData.totalPrice?priceData.totalPrice:0"></CCY-rate>
                    <span
						style="color: black">{{ '(' + neonDiscountData?.priceInfo?.unitPercent * 100 + '%' + ' Off)' }}</span>
                  </span>
								</div>
							</div>

							<NeonButton v-if="item.paramName == currentStep"
										style="margin-top: 10px"
										small
										:disabled="disabledNext(item)"
										@click="nextStepFun(item)">
								<span>{{ lang.next }}</span>
							</NeonButton>
						</section>
						<section :class="{ 'step-active': item.paramName == currentStep }"
								 v-if="item.paramName == 'Confirmation Of Order'"
								 class="step confirmation">
							<div class="title-line">
								<TitlePine @closeActive="shutMask"
										   :title="formatStepIndex(index + stepNum)">
									<span>{{ formatStepTitle(item.paramName, item.alias) }}</span>
								</TitlePine>
							</div>
							<div class="subtitle-line confirmation">
								<span>{{ item.tips }}</span>
							</div>
							<NoimgCard v-model="selectedData[item.paramName]"
									   showKey="tips"
									   :noValue="true"
									   :data_list="item.childList"
									   @change="noValueFun"
									   align="center"></NoimgCard>
						</section>
					</div>
				</div>

				<section class="stickyBtns"
						 :class="{ showDetails: showDetails }">
					<section class="detailsBox scrollBar custom-scrollbar"
							 :class="{
            isUpload: tabName == 'Upload',
            showDetails: showDetails,
          }">
						<div class="trapezoid"
							 @click="changeTrapezoid">
              <span> {{
					  lang.orderSummary
				  }}<b :class="showDetails ? 'icon-Down' : 'icon-Up'"></b></span>
						</div>
						<div class="mbBottomCanvas">
							<TextDraggableCanvas :currentFont="currentFont"
												 :currentFontSize="currentFontSize"
												 :priceData="priceData"
												 :currentRound="currentRound"
												 :selectedFontData="selectedFontData"
												 :currentSampleData="currentSampleData"
												 :neonSampleData="neonSampleData"
												 :needCanvas="needCanvas"
												 :isText="isText"
												 :hiddenPrice="false"
												 :zoneRatio="zoneRatio"
												 :unitConvertRatio="unitConvertRatio"
												 :unitSign="unitSign"
												 :textDesign="textDesign"
												 :selectParamId.sync="selectParamId"
												 :paramIdList.sync="paramIdList"
												 :isImgTemplate="isImgTemplate"
												 :neonDiscount="neonDiscountData?.priceInfo"
												 :isImg="true"
												 :canvasImgUrl="canvasImgUrl">
							</TextDraggableCanvas>
							<div class="normalTips"
								 :class="{'is-design': tabName != 'Upload' }">
								<p>
									{{ tabName == 'Upload' ? lang.neon.canvasNormalTips2 : lang.neon.canvasNormalTips }}
								</p>
							</div>
						</div>
						<DetailBoard @jump="jump"
									 :list="selectedData"
									 :arr="generalData"
									 :priceData="priceData"
									 :showDetails.sync="showDetails"
									 :finaWidth="finaSize.width"
									 :finaHeight="finaSize.height"
									 :hiddenSize="false"
									 :neonDiscount="neonDiscountData?.priceInfo"
									 :tabName="tabName"
									 :onlyInquiry="onlyInquiry"
									 :unitSign="unitSign"
									 :unitConvertRatio="unitConvertRatio"
									 :isImgTemplate="isImgTemplate">
						</DetailBoard>
					</section>
					<ButtonGroup :tabName="tabName"
								 :showDetails="showDetails"
								 :onlyInquiry="onlyInquiry"
								 @submitInquiry="submitInquiry"
								 @addToCart="addToCart"></ButtonGroup>
				</section>
				<section class="mask"
						 :class="{ show: showDetails }"></section>
			</section>
		</section>
		<div id="theLast"></div>
		<NMask v-if="device != 'mb'"
			   :top="scrollTop"
			   v-model="checking"
			   @change="shutMask"></NMask>
		<InfoDialog :infoDialogVisible.sync="infoDialogVisible"
					:otherUpload="uploadFileList"
					:uploadList.sync="uploadList"
					noTitle
					@getValue="getValueFun">
			<template v-slot:custom>
				<el-form-item prop="subject"
							  :label="lang.Comments">
					<el-input type="textarea"
							  v-model="customComment"
							  :placeholder="lang.instructions"></el-input>
				</el-form-item>
			</template>
		</InfoDialog>
		<BaseDialog class="dialogForUpload"
					v-model="dialogForUpload"
					:width="device == 'mb' ? '80%':'30%'">
			<p class="uploadTitle">
				{{ lang.neon.uploadTitle }}
			</p>
			<NeonUpload ref="uploadCom2"
						style2
						uploadId="2222"
						:uploadFileList="uploadFileList"
						@getFile="getFile"></NeonUpload>
		</BaseDialog>
		<DialogForShop :dialogVisible.sync="dialogVisible"
					   :queryId="queryIdCur"
					   :currentTemplate="currentTemplate"
					   :parentCateId="parentCateId"
					   :isInquery="isInquiryCur"
					   :paramIdList.sync="paramIdList"
					   @currentTemplate="currentTemplateFun"
					   @showDialogForUpload="showDialogForUploadFun"
					   :neonDiscount="neonDiscountData?.priceInfo"></DialogForShop>
		<!-- 预览弹窗-->
		<BaseDialog v-model="preViewDialog"
					class="previewDialog"
					:class="{'sample': !dialogItem.alias}"
					:width="device == 'ipad' ? '400px' : device != 'mb' ? '600px' : '90%'">
			<div class="picWrap"
				 :style="{ 'aspect-ratio': zoomAspectRatio }">
				<VideoPlayer v-if="getFileSuffix(zoomPic) === '.mp4'"
							 disabledMouse
							 :options="getVideoOptions(zoomPic)"></VideoPlayer>
				<img v-else
					 :src="zoomPic"/>
				<div class="preview-title">
					<span class="ring"></span>
					<span>{{ dialogItem.alias }}</span>
				</div>
				<NeonButton style="margin-top: 10px;margin-bottom: 10px"
							small
							@click="nextStepFun(dialogItem,'dialog')">
					<span>{{ lang.sns }}</span>
				</NeonButton>
			</div>
		</BaseDialog>
		<!-- 图片截取弹窗 -->
		<Crop :cropperDialog.sync="cropperDialog"
			  :imageSrc="imageSrc.secure_url"
			  @cropImage="cropImage"
			  @cancelCrop="uploadFileList.pop()"
			  width="50%">
			<div class="crop-tips">
				{{ lang.neon.neonCropTips }}
			</div>
		</Crop>
		<!-- 图片预览弹窗 -->
		<NeonPreviewImg :previewImgDialog="previewImgDialog"
						:previewImgData="previewImgData"
						@closePreviewImg="$store.commit('setNeonPreview', { paramName: '', alias: '', show: false })"></NeonPreviewImg>
	</article>
</template>
<script>
import "@/plugins/element";
import DateBox from "@/components/Medals/DateBox";
import InfoDialog from "@/components/Medals/infoDialog";
import infoUpload from "@/components/Medals/infoUpload";

import TextDraggableCanvas from "@/components/Neon/TextDraggableCanvas";
import DetailBoard from "@/components/Neon/DetailBoard";
import ButtonGroup from "@/components/Neon/ButtonGroup";
import NMask from "@/components/Neon/NMask";

import BaseDialog from "@/components/Quote/BaseDialog";
import VideoPlayer from "@/components/Quote/VideoPlayer.vue";
import NeonButton from "@/components/Neon/NeonButton";
import NeonUpload from "@/components/Neon/NeonUpload";
import NeonDateBox from "@/components/Neon/NeonDateBox";
import FontParam from "@/components/Neon/FontParam.vue";
import NeonProgressSelect from "@/components/Neon/NeonProgressSelect.vue";

import SummaryCard from "@/components/Neon/SummaryCard.vue";
import TitlePine from "@/components/Neon/TitlePine.vue";
import SampleView from "@/components/Neon/SampleView.vue";
import SelectSize from "@/components/Neon/SelectSize.vue";
import NoimgCard from "@/components/Neon/NoimgCard.vue";
import HasimgCard from "@/components/Neon/HasimgCard.vue";
import HasimgCardMultiple from "@/components/Neon/HasimgCardMultiple.vue";
import HasimgCardType2 from "@/components/Neon/HasimgCardType2.vue";
import DialogForShop from "@/components/Neon/DialogForShop.vue";
import Crop from "@/components/MyDzxt/Crop.vue";

import {medalsApi} from "@/api/medals/medals";
import {neonApi} from "@/api/neon/neon";
import {
	otoEditInquiry,
	otoAddCart,
	otoEditCart,
	getInfo,
	getPriceData,
} from "@/api/pins";
import {
	debounce,
	urlAddVersion,
	dataURLtoFile,
	getFileSuffix,
	getImageSize,
	pdfToImg,
} from "@/utils/utils";

import {uploadFile} from "~/utils/oss";
import quotePrepare from "@/mixins/quotePrepare";
import "@/assets/css/neon/neon.scss";
import "@/assets/css/neon/neonFont.css"
import {getIsSmallQty, getQuoteTime} from "@/assets/js/quote/quotePublic";
import NeonPreviewImg from "@/components/Neon/NeonPreviewImg.vue";

export default {
	head: {
		script: [
			{
				src: "https://at.alicdn.com/t/font_2914910_9bvahwtmso.js",
			},
			{
				src: "https://cdn.jsdelivr.net/npm/pdfjs-dist@3.1.81/build/pdf.min.js",
				async: true,
				body: true,
			},
		],
	},
	mixins: [quotePrepare],
	props: {
		customTitle: {
			type: String,
			default: "",
		},
		pageQuote: {
			type: Boolean,
			default: false,
		},
		pageQuoteDialog: {
			type: Boolean,
			default: false,
		},
		isInquiry: {
			type: Boolean,
			default: false,
		},
		queryId: {
			type: [Number, String],
			default: 0,
		},
		parentCateId: {
			type: [Number, String],
			default: 0,
		},
	},
	components: {
		NeonPreviewImg,
		BaseDialog,
		VideoPlayer,
		TextDraggableCanvas,
		DetailBoard,
		ButtonGroup,
		NMask,
		NeonButton,
		NeonUpload,
		TitlePine,
		SelectSize,
		HasimgCard,
		SampleView,
		SummaryCard,
		NoimgCard,
		DateBox,
		InfoDialog,
		infoUpload,
		NeonDateBox,
		DialogForShop,
		FontParam,
		NeonProgressSelect,
		HasimgCardType2,
		HasimgCardMultiple,
		Crop,
	},
	data() {
		return {
			isIframe: false,
			fdNeonType: '',
			offsetBottom: 0,
			cllientHeight: 0,
			isFlow: false,
			supportCropType: [
				"jpg",
				"jpeg",
				"gif",
				"png",
				"svg",
				"webp",
				"pdf",
				"psd",
			],
			sliderOptions: {
				min: 0,
				max: 150,
				part: 5,
				mesh: 5,
			},
			isCropFile: true,
			cropperDialog: false,
			imageSrc: "",
			artworkImg: {},
			artworkInfo: {
				width: 0,
				height: 0,
			},
			isCustomSizeInput: true,
			// 回填状态
			isBack: false,
			imgMinWidth: 15,
			platformProductId: "",
			fromSmallNeonText: "",
			scrollTop: 0,
			topH: 0,
			headH: 0,
			isImgTemplate: 0, //纯图片模板
			isInquiryCur: 0,
			finaSize: {
				width: 0,
				height: 0,
			},
			paramIdList: "",
			selectParamId: 0,
			zoneRatio: null,
			needCanvas: true,
			stepNum: 0,
			isText: false,
			queryIdCur: null,
			currentTemplateChild: {},
			currentTemplate: null,
			currentSampleData: null,
			neonSampleData: {},
			dialogVisible: false,
			showTrapezoid: false,
			onlyInquiry: false,
			currentRound: {},
			roundList: [],
			canNotParamComposeList: [],
			infoDialogVisible: false,
			dialogForUpload: false,
			absoluteImg: "",
			uploadFileList: [],
			priceData: {},
			showDetails: false,
			noChoiceData: [],
			currentFont: {},
			currentFontSize: null,
			checking: false,
			currentStep: null,
			customComment: "", //输入的文本
			greetingTextLine: "",
			generalData: [], //总纲
			neonStyleData: [],
			cateId: 176,
			pid: 67,
			selectedData: {},
			uploadFormData: {
				uploadFile: [],
				comments: "",
			},
			tabName: "Enter Your Text",
			selectedFontData: [],
			// 折扣
			neonDiscountData: null,
			// 最后一次滑动位置
			lastScrollTop: 0,
			// 正在输入
			spelling: false,
			// 超出尺寸 (canvas提示）
			canvasTipsShow: false,
			customSize: 0,
			// 自定义尺寸最大值
			customMax: 150,
			// 最小值
			customMin: 0,
			// 纯文字设计
			textDesign: false,
			// 选了变色或者炫彩
			specialColorFlag: false,
			// 当前选的变色还是炫彩
			specialColorCur: null,
			// 自定义尺寸标识
			customSizeFlag: false,
			// 可以做的尺寸列表（包含了尺寸信息) 用于定制尺寸需要的参数
			ableSizeListDetail: [],
			// 运费提示弹窗
			nowNeonAddFreight: false,
			nowNeonAddFreightVisable: false,
			// 显示贺卡文本框
			showGreetingCard: false,
			// 预览弹窗相关参数
			preViewDialog: false,
			// 图片预览弹框
			preViewDialogImg: false,
			// 预览图片链接
			preViewImgSrc: "",
			// 隐藏价格
			hiddenPrice: false,
			zoomPic: "",
			zoomAspectRatio: 1,
			dialogItem: {},
			// 修改了模板
			changeTemplateFlag: false,
			// custom card的左偏移
			offsetLeftForCustomCard: 0,
			// custom card文字校验是否提示
			greetingNoPassFlag: false,
			fixedMatchs: [
				{
					name: "Your Text",
					dom: "yourText",
				},
				{
					name: "Designs Or Upload Your Artwork",
					dom: "designsOrUploadYourArtwork",
				},
				{
					name: "Select Size",
					dom: "selectSize",
				},
			],
			uploadFontAndColor: [
				{
					"Select Font": undefined,
					"Select Color": undefined,
					tubeColor: undefined,
				},
			],
			canvasImgUrl: "",
			uploadList: [],
			// 是否从设计系统跳转到报价
			fromDesignFlag: false,
			// neonAI传入的提示词
			txtFromAi: "",
		};
	},
	methods: {
		getFileSuffix,
		clearDiscount() {
			if (this.isBack) {
				return false;
			}
			let findDiscount = this.generalData.find(
				(item) => item.paramType === "DISCOUNT"
			);
			if (findDiscount) {
				let name = findDiscount.paramName;
				this.selectedData[name] = null;
				this.neonDiscountData = null;
				this.debounceCalcPrice();
			}
		},
		validateEmoji(val, key = "greetingTextLine") {
			if (key == "greetingTextLine") this.greetingNoPassFlag = false;
			this[key] = val.replace(
				/[\uD83D\uD83C][\uDC00-\uDFFF]|\uD83D[\uDC00-\uDE4F]/g,
				""
			);
		},
		// 解析回填参数
		analysisBackQuoteParam(data) {
			return new Promise((resolve, reject) => {
				let selectedData = {};
				let fontData = this.getInfo("Select Font").childList;
				let colorData = this.getInfo("Select Color").childList;
				this.generalData.forEach((x) => {
					for (const key in data) {
						let item = data[key];
						if (key === x.paramName) {
							if (key === "Your Text") {
								selectedData[key] = item.map((y) => {
									return {
										...y,
										"Select Font": fontData.find(
											(z) => z.id === y["Select Font"]
										),
										"Select Color": colorData.find(
											(z) => z.id === y["Select Color"]
										),
									};
								});
							} else if (key === "Select Type") {
								selectedData[key] = item.map((y) =>
									x.childList.find((z) => z.id === y)
								);
							} else if (
								key === "Designs Or Upload Your Artwork" ||
								key === "What Size Would You Like?"
							) {
								selectedData[key] = item;
							} else if (
								key === "Greeting Card" ||
								key === "Select Turnaround Time"
							) {
								let selectedItem = x.childList.find((y) => y.id === item.id);
								selectedData[key] = {...selectedItem, remark: item.remark};
							} else {
								let selectedItem = x.childList.find((y) => y.id === item);
								selectedData[key] = selectedItem;
							}
						}
					}
				});
				resolve(selectedData);
			});
		},
		async backQuoteParam(quoteParam) {
			try {
				const {
					backFillParam: {
						currentTemplateId,
						tabName,
						backFillSelectedData,
						artworkInfo,
						artworkImg,
					},
					comments,
					fontData: {fontImgCustom},
				} = JSON.parse(quoteParam);

				// 解析并回填报价参数
				const selectedData = await this.analysisBackQuoteParam(
					backFillSelectedData
				);
				this.selectedData = selectedData;

				// 更新字体和尺寸相关属性
				const {
					"Select Size": selectSize,
					"Your Text": yourText,
					"Designs Or Upload Your Artwork": designs,
					"Select Turnaround Time": turnaroundTime,
					"Greeting Card": greetingCard,
					"What Size Would You Like?": customSize,
				} = selectedData;
				this.currentFontSize =
					selectSize?.paramName !== "Custom Size" ? selectSize : undefined;
				this.selectedFontData = yourText || [];
				this.currentSampleData = designs;
				this.neonDiscountData = turnaroundTime;
				this.greetingTextLine = greetingCard.remark;
				this.showGreetingCard = Boolean(this.greetingTextLine);
				this.tabName = tabName;
				this.customComment = turnaroundTime.remark || comments || "";
				this.queryIdCur = currentTemplateId || this.queryIdCur;

				// 处理自定义尺寸
				if (selectSize?.paramName === "Custom Size") {
					this.$nextTick(() => {
						this.sliderOptions.min =
							this.unitSign === "cm"
								? Number((this.getMinSize().width * 0.3937).toFixed(0))
								: this.getMinSize().width;
						this.customSizeFlag = true;
						this.customSize = customSize;
						setTimeout(
							() =>
								this.$refs.textDraggableCanvas.changeCustomSize(
									this.customSize
								),
							2000
						);
					});
				}

				// 处理上传文件
				if (tabName === "Upload") {
					this.needCanvas = false;
					setTimeout(() => {
						this.artworkImg = artworkImg;
						this.artworkInfo = artworkInfo;
						this.uploadFileList = fontImgCustom.map((x) => ({
							original_filename: x.split("/").pop(),
							secure_url: x,
						}));
						this.finaSize = {...this.artworkInfo};
						this.debounceCalcPrice();
					}, 1500);
				}

				this.isBack = true;
			} catch (err) {
			}
		},
		limitSizeInput(val) {
			if (val < this.imgMinWidth) this.artworkInfo.width = this.imgMinWidth;
			else if (val > 300) this.artworkInfo.width = 300;
			else this.artworkInfo.width = val;
			this.artworkInfo.height = (
				Number(this.artworkInfo.width) * this.artworkImg.ratio
			).toFixed(0);
			this.selectedData[
				"What Size Would You Like?"
				] = `L  (${this.artworkInfo.width})${this.unitSign} * H (${this.artworkInfo.height})${this.unitSign}`;
			this.finaSize = {...this.artworkInfo};
			this.debounceCalcPrice();
		},

		formatStepIndex(i) {
			return this.lang.step + " " + i;
		},

		formatStepTitle(name, alias) {
			const noSelect = [
				"Indoor or Outdoor",
				"Hanging Options",
				"Select Remote and Dimmer",
				"Select Type",
				"Select Your Backboard Color",
				"Select Turnaround Time",
				"Select Backboard",
				"Greeting Card",
			];
			let titleStr = "";
			titleStr = this.lang.Select + " " + alias;
			if (noSelect.includes(name) && this.langObj.countryCode == "de") {
				titleStr = alias;
			}
			return titleStr;
		},

		// 判断尺寸有没有超过 60 * 90 或者最长的大于90 最小的大于23
		sizeOverSixNine(type = "inch") {
			const width = Number(this.finaSize.width);
			const height = Number(this.finaSize.height);
			if (type == "cm") {
				return (
					width * height > 23 * 35 ||
					Math.max(width, height) > 35 ||
					Math.min(width, height) > 23
				);
			} else {
				return (
					width * height > 47 * 94 ||
					Math.max(width, height) > 94 ||
					Math.min(width, height) > 47
				);
			}
		},

		// 过滤Hanging Option步骤种的 3M Command Strips参数
		filterHangingOption(arr) {
			if (this.sizeOverSixNine("cm")) {
				return arr.filter((x) => x.paramName != "3M Command Strips");
			}
			return arr;
		},

		getVideoOptions(path) {
			let ph;
			if (!this.isJsonString(path)) {
				ph = path;
			} else {
				let tempPath = JSON.parse(path).find((x) => {
					return x.proType == this.proType;
				});
				ph = tempPath ? tempPath.path : JSON.parse(path)[0].path;
			}
			return {
				autoplay: true,
				controls: this.device !== "mb",
				muted: false,
				loop: true,
				fill: true,
				sources: [
					{
						src: ph,
						type: "video/mp4",
					},
				],
			};
		},

		isJsonString(str) {
			try {
				const toObj = JSON.parse(str); // json字符串转对象
				/*
				  判断条件
             1. 排除null可能性
						 2. 确保数据是对象或数组
			  */
				if (toObj && typeof toObj === "object") {
					return true;
				}
			} catch {
			}
			return false;
		},

		zoomPicNeon(e, type) {
			this.dialogItem = e.value;
			this.previewKey = e.key;
			const mediaData = JSON.parse(e.value.imageJson);
			this.zoomPic = mediaData[1].url;
		},

		previewImgFromFiles(v) {
			this.dialogItem = {};
			this.zoomPic = v;
			this.preViewDialog = true;
			this.shutMask();
		},

		picDialogFun(val) {
			this.preViewDialog = val;
			this.shutMask();
		},

		setTextAlign(val, idx) {
			this.selectedFontData[idx].textAlign = val;
		},
		changeTrapezoid() {
			this.showDetails = !this.showDetails;
			if (this.showDetails && this.device == "mb") {
				if (document.getElementById("theLast")) {
					setTimeout(() => {
						let parentTop = document.getElementById("neon");
						let objTop = document.getElementById("theLast")?.offsetTop;
						parentTop.scroll({
							top: objTop,
							left: 0,
							behavior: "smooth",
						});
					}, 300);

					setTimeout(() => {
						const detailBox = document.querySelector(".detailBoard");
						detailBox.scrollIntoView({
							block: "center",
							behavior: "smooth",
						});
					}, 300);
				}
			}
		},

		// 裁剪完成
		async cropImage(data) {
			const changeImg = this.uploadFileList.find(
				(x) => x.original_filename == this.imageSrc.original_filename
			);
			// base64转url 先转文件 再上传图片
			const file = await dataURLtoFile(data, 1);
			const url = await uploadFile(file);
			changeImg.secure_url = url;
			const {width, height} = await getImageSize(data);
			changeImg.height = height;
			changeImg.width = width;
			changeImg.ratio = height / width;
			// this.imgMinWidth = Math.max(width, height) > 200 ? Number((Math.max(width, height) / 40).toFixed(0)) : 10
			this.artworkInfo.width = this.imgMinWidth;
			this.artworkInfo.height = (this.imgMinWidth * changeImg.ratio).toFixed(2);
			this.finaSize = {...this.artworkInfo};
			this.artworkImg = changeImg;
			this.debounceCalcPrice();
		},

		changeCustomSize(val) {
			this.customSize = val;
			const index = this.textCheckKey("Select Color", "isNeonColorful");
			setTimeout(() => {
				if (!this.isImgTemplate && this.sizeOverSixNine("inch") && index > -1) {
					this.selectedData["Your Text"][index]["Select Color"] = null;
					this.canvasTipsShow = true;
				} else {
					this.canvasTipsShow = false;
				}
				const overFlag = this.sizeOverSixNine("cm");
				if (
					overFlag &&
					this.selectedData["Hanging Options"]?.paramName ===
					"3M Command Strips"
				) {
					this.selectedData["Hanging Options"] = null;
				}
			}, 400);

			this.$refs.textDraggableCanvas.changeCustomSize(val);
			this.selectedData["What Size Would You Like?"] = val;
			this.debounceCalcPrice();
		},
		backToDesign() {
			this.tabName = "Enter Your Text";
			this.dialogVisible = true;
			this.queryIdCur = null;
		},
		showDialogForUploadFun() {
			if (this.isInquiryCur) {
				this.dialogForUpload = true;
			}
		},
		getFinaSize(val) {
			this.finaSize.width = val.finaWidth;
			this.finaSize.height = val.finaHeight;
		},
		filterGeneralData(list) {
			let fList = list;
			const filterParam = ["Acrglic Stand", "Wall-Mounted Open Box"];
			const filterParamB = ["Cut To Letters"];
			if (
				filterParam.includes(this.selectedData["Select Backboard"]?.paramName)
			) {
				fList = list.filter((x) => x.paramName != "Hanging Options");
			} else if (
				filterParamB.includes(this.selectedData["Select Backboard"]?.paramName)
			) {
				fList = list.filter(
					(x) => x.paramName != "Select Your Backboard Color"
				);
			}
			if (this.tabName == "Upload") {
				this.stepNum = 1;
				return fList.filter((x) => {
					return (
						x.paramName != "Select Font" &&
						x.paramName != "Select Color" &&
						x.paramName != "Designs Or Upload Your Artwork" &&
						x.paramName != "Select Size"
					);
				});
			} else {
				if (this.isImgTemplate) {
					this.stepNum = 1;
					return fList.filter((x) => {
						return (
							x.paramName != "Your Text" &&
							x.paramName != "Select Font" &&
							x.paramName != "Select Color" &&
							x.paramName != "What Size Would You Like?" &&
							x.paramName != "Select Type"
						);
					});
				} else if (this.isText) {
					this.stepNum = 1;
					return fList.filter((x) => {
						return (
							x.paramName != "Select Font" &&
							x.paramName != "Select Color" &&
							x.paramName != "Designs Or Upload Your Artwork" &&
							x.paramName != "What Size Would You Like?" &&
							x.paramName != "Select Type"
						);
					});
				} else {
					this.stepNum = 0;
					return fList.filter((x) => {
						return (
							x.paramName != "Select Font" &&
							x.paramName != "Select Color" &&
							x.paramName != "What Size Would You Like?" &&
							x.paramName != "Select Type"
						);
					});
				}
			}
		},
		shopAllFun() {
			this.dialogVisible = true;
			this.queryIdCur = null;
		},
		uploadTo() {
			this.$nextTick(() => {
				this.$refs.uploadCom[0].$refs.upload.click();
			});
		},
		textCheckKey(pKey, key) {
			return this.selectedFontData.findIndex((x) => {
				return x[pKey]?.[key];
			});
		},
		// 文字参数发生改变
		changeFontOption(val, item) {
			// 如果选了炫彩色 而且尺寸已经大于47了 取消Select Size选中
			if (
				this.selectedFontData[0]?.["Select Color"]?.paramName ==
				"Gradient Changing Color" &&
				(this.finaSize.width > 94 || this.finaSize.height > 47)
			) {
				this.selectedData["Select Size"] = null;
			}

			this.selectedData["Your Text"] = this.selectedFontData;
			this.filterStepsFun().then((res) => {
				this.noChoiceData = res;
			});

			setTimeout(() => {
				this.ableSizeListDetail =
					this.$refs?.SelectSize[0].getAllAbleSizeDetail();
				if (
					this.isText &&
					this.textCheckKey("Select Color", "isNeonColorful") > -1
				) {
					this.sliderOptions.max =
						this.ableSizeListDetail[
						this.ableSizeListDetail.length - 1
							].rectInfo?.width;
					this.canvasTipsShow = false;
				} else {
					this.sliderOptions.max = 150;
				}
				this.debounceFontSizePrice();
				this.debounceCalcPrice();
			}, 500);
		},
		// 当前文字编辑参数类型
		changeColor(val) {
			if (!this.isText) return;
			const specialColor = ["Gradient Changing Color", "RGB Color"];
			const hasIndex = specialColor.indexOf(val.paramName);
			if (hasIndex > -1) {
				this.specialColorFlag = true;
				this.specialColorCur = val.paramName;
				this.selectedFontData.forEach((x) => {
					x["Select Color"] = val;
				});
				if (hasIndex == 0) {
					if (this.finaSize.width > 94 || this.finaSize.height > 47)
						this.selectedData["Select Size"] = null;
				}
			} else {
				// 将其他所有选择了特殊颜色的改成普通颜色
				const index = this.selectedFontData.findIndex((x) => {
					return specialColor.includes(x["Select Color"]?.paramName);
				});
				if (index > -1) {
					this.specialColorFlag = false;
					this.selectedFontData.forEach((x) => (x["Select Color"] = val));
				}
				this.specialColorCur = null;
			}
		},
		// 改变字体
		changeFont(val) {
			setTimeout(() => {
				this.largeSizeItem =
					this.ableSizeListDetail.find((x) => x.paramName == "Large") ||
					this.ableSizeListDetail[this.ableSizeListDetail.length - 2];
				this.currentFontSize = this.ableSizeListDetail[0];
				this.selectedData["Select Size"] = null;
				// const existFont = this.selectedFontData.findIndex(x=>x.paramName == this.selectedData['Select Size']?.paramName)
				// if(existFont > -1) this.selectedData['Select Size'] = this.currentFontSize
				const arr = JSON.parse(val.neonSampleData);
				this.neonSampleData = arr.find((x) => {
					return x.size == largeSizeItem.paramName;
				});
			}, 0);
		},
		// 输入文字
		changeInput(val) {
			setTimeout(() => {
				if (this.sizeOverSixNine("inch")) {
					this.canvasTipsShow = true;
				} else {
					this.canvasTipsShow = false;
				}
			}, 400);

			this.$nextTick(() => {
				if (this.unitSign == "cm") {
					this.sliderOptions.min = Number(
						(this.getMinSize().width * 0.3937).toFixed(0)
					);
				} else {
					this.sliderOptions.min = this.getMinSize().width;
				}
				if (this.customSizeFlag) {
					this.customSize = 0;
					this.$refs.SelectSize[0].resetProgress();
					this.$refs.textDraggableCanvas.changeCustomSize(this.customSize);
					// this.$refs.SelectSize[0].$refs.neonProgressSelect[0].clickWrapper({
					//   offsetX: 0,
					// });
				}
				this.ableSizeListDetail =
					this.$refs?.SelectSize[0].getAllAbleSizeDetail();
			});
		},
		jump(val) {
			if (this.device == "pc") this.showDetails = false;
			this.scrollFun(val, true);
			this.currentStep = val;
			this.checking = true;
		},
		errorMessage(name) {
			let message = "";
			const isInput = name.includes("Text");
			// 一些特殊提示
			if (name == "Designs Or Upload Your Artwork") {
				message = this.lang.neon.notChooseTemplateColor;
			} else if (
				name == "Greeting Card" &&
				this.selectedData[name]?.paramCode == "custom" &&
				this.greetingTextLine.trim().length == 0
			) {
				this.greetingNoPassFlag = true;
				message = this.lang.neon.inputGreetingError;
			} else if (name == "Your Text") {
				if (this.tabName != "Upload") {
					message = this.lang.neon.notInputText;
				} else {
					message = this.lang.neon.noUploadFile;
				}
			} else if (name == "Select Size") {
				message = this.lang.neon.selectSize;
			} else if (name == "Select Turnaround Time") {
				message = this.lang.neon.selectTime;
			} else if (name == "Greeting Card") {
				message = this.lang.neon.inputCardText;
			} else if (name == "Select Type") {
				message = this.lang.neon.pleaseSelectType;
			} else {
				const noPassStep = this.generalData.find((x) => x.paramName == name);
				message = `${this.lang.neon.errorHead1} ${
					isInput ? this.lang.neon.errorHead2 : ""
				}${noPassStep.alias}`;
			}
			this.$toast.error(message, {
				position: "top-right",
			});
		},
		loadImage(url) {
			return new Promise((resolve, reject) => {
				let img = new Image();
				img.onload = () => resolve(img);
				img.onerror = reject;
				img.src = urlAddVersion(url);
				img.crossOrigin = "Anonymous";
			});
		},
		uploadCanvas(val) {
			return new Promise((resolve, reject) => {
				let file = dataURLtoFile(val, 1);
				uploadFile(file).then(async (url) => {
					let img = await this.loadImage(url);
					resolve(img);
				});
			});
		},
		whatSizeWouldYouLikeFun(val, type, e) {
			if (type == "range") this.debounceLimitSize(e.target.value);
			this.checking = true;
			this.currentStep = val.paramName;
			this.scrollFun(val.paramName);
			this.clickFun(val);
		},
		noValueFun(val, key) {
			if (key == "Select Turnaround Time") {
				this.changeTurnaroundTime(val);
			} else if (key == "Select Backboard") {
				this.changeBackboard(val);
			} else if (key == "Greeting Card") {
				this.changeGreetingCard(val);
			} else if (key == "Select Type") {
				this.changeSelectType(val, key);
			} else {
				this.clickFun();
			}
		},
		// 修改交期
		changeTurnaroundTime(val) {
			this.neonDiscountData = val;
			this.clickFun(val);
		},
		// 选择背板
		changeBackboard(val) {
			if (
				val?.paramName == "Acrglic Stand" ||
				val?.paramName == "Wall-Mounted Open Box"
			) {
				delete this.selectedData["Hanging Options"];
			} else {
				this.selectedData["Hanging Options"] = null;
			}

			if (val?.paramName == "Cut To Letters") {
				delete this.selectedData["Select Your Backboard Color"];
			} else {
				this.selectedData["Select Your Backboard Color"] = null;
			}
			this.clickFun();
		},

		// 选择贺卡
		changeGreetingCard(val) {
			this.greetingNoPassFlag = false;
			if (val.paramCode == "custom") {
				const arr = [16, 50, 83];
				const index = this.getInfo("Greeting Card").childList.findIndex(
					(x) => x.id == val.id
				);
				this.offsetLeftForCustomCard = arr[index % 3] + "%";
				this.showGreetingCard = true;
			} else {
				this.showGreetingCard = false;
				this.clickFun();
			}
		},

		// 选择颜色类型
		changeSelectType(val, key) {
			this.currentStep = key;
			this.checking = true;
			this.filterStepsFun().then((res) => {
				this.noChoiceData = res;
			});

			this.scrollFun(key);
			this.debounceCalcPrice();
		},

		// 切换模板重置的数据
		resetForNewTemplate() {
			this.uploadFileList = [];
			this.priceData = {};
			this.currentFontSize = null;
			this.customSizeFlag = false;
			this.uploadFontAndColor = [
				{
					"Select Font": undefined,
					"Select Color": undefined,
					tubeColor: undefined,
				},
			];
			this.specialColorFlag = false;
			this.currentSampleData = null;

			for (let it in this.selectedData) {
				this.selectedData[it] = null;
			}

			this.selectedData["What Size Would You Like?"] = 0;
		},

		currentTemplateFun({type, val}) {
			if (this.tabName == "Upload" && !type) {
				const neonDom = document.querySelector(".neon");
				neonDom.scrollTo({
					top: 0,
				});
				this.showDetails = false;
				this.tabName = "Enter Your Text";
			}

			this.$refs.textDraggableCanvas.$refs.canvasFactory.changeTemplate();

			this.selectParamId = val.id;
			this.shutMask();

			!type && !this.isBack && this.resetForNewTemplate();
			this.currentTemplate = val;
			this.currentTemplateChild = this.getInfo(
				"Designs Or Upload Your Artwork"
			).childList?.find((x) => {
				return val.id == x.id;
			});
			this.$nextTick(() => {
				this.filterStepsFun().then((res) => {
					this.noChoiceData = res;
				});
			});

			if (this.currentFontSize) {
				this.neonSampleData = JSON.parse(
					this.currentTemplateChild.neonSampleData
				).find((x) => {
					return x.size == this.currentFontSize.paramName;
				});
			} else {
				if (!val.isTextTemplate) {
					setTimeout(() => {
						let filterSizeList = this.getInfo("Select Size").childList;
						this.currentFontSize = filterSizeList.find(
							(x) => !x.isHidden && !x.onlyAddInquiry
						);
						const obj = JSON.parse(val.neonSampleData);
						this.neonSampleData = obj.find(
							(x) => x.size == this.currentFontSize.paramName
						);
					}, 0);
				}
			}
			this.changeTemplateFlag = true;
			setTimeout(() => {
				this.isBack = false;
				if (this.tabName == "Upload") return;
				this.ableSizeListDetail =
					this.$refs.SelectSize[0].getAllAbleSizeDetail();
				if (val.isTextTemplate) {
					const largeSizeItem =
						this.ableSizeListDetail.find((x) => x.paramName == "Large") ||
						this.ableSizeListDetail[this.ableSizeListDetail.length - 2];
					this.currentFontSize = this.ableSizeListDetail[0];
					const arr = JSON.parse(val.neonSampleData);
					this.neonSampleData = arr.find(
						(x) => x.size == largeSizeItem.paramName
					);
				}
				this.debounceFontSizePrice();
				this.debounceCalcPrice();
			}, 500);
			this.$forceUpdate();
		},
		changeTemplateColor(val, item) {
			this.currentSampleData = val;
			this.currentStep = item.paramName;
			this.checking = true;
			this.scrollFun(item.paramName);
			this.clickFun(item);
		},
		clickTextFun(item) {
			this.currentStep = item.paramName;
			this.checking = true;
			this.isFlow = true;
			this.scrollFun(item.paramName);
		},
		clickNotes(item) {
			this.checking = true;
			this.currentStep = item.paramName;
			this.scrollFun(item.paramName);
		},
		clickFontParamUpload(item) {
			this.currentStep = item.paramName;
			this.checking = true;
			this.scrollFun(item.paramName);
		},
		clickTurnaround(item) {
			this.clickFun(item);
		},
		// 获取最小尺寸
		getMinSize() {
			const dom = document.querySelector(".select-size");
			if (!dom) return {width: 0, height: 0};
			const cardOuters = Array.from(dom.children)
			const minSizeDom = cardOuters.find((x) => {
				return (
					x.style["0"] != "display" &&
					!Array.from(x.classList).includes("custom-size") &&
					!Array.from(x.classList).includes("tips")
				);
			});
			const sizeSplit = minSizeDom.children[0].children[1].innerText.split(" ");

			return {
				width: parseFloat(sizeSplit[1]),
				height: parseFloat(sizeSplit[4]),
			};
		},
		findSizeRange(arr, size) {
			// 返回第一个大于 size 的上界
			let upperIndex = arr.findIndex((x) => x.rectInfo.width > size);
			if (upperIndex === -1) {
				return {
					lower: arr[arr.length - 1],
					upper: null,
				};
			}
			return {
				lower: arr[upperIndex - 1],
				upper: arr[upperIndex],
			};
		},

		clickFunForSize(val, item) {
			this.ableSizeListDetail = this.$refs.SelectSize[0].getAllAbleSizeDetail();
			// this.canvasTipsShow = false;
			setTimeout(() => {
				if (this.sizeOverSixNine("inch")) {
					this.canvasTipsShow = true;
				} else {
					this.canvasTipsShow = false;
				}
				const overFlag = this.sizeOverSixNine("cm");
				if (
					overFlag &&
					this.selectedData["Hanging Options"]?.paramName ===
					"3M Command Strips"
				) {
					this.selectedData["Hanging Options"] = null;
				}
			}, 400);
			if (val.paramName == "Custom Size") {
				this.checking = true;
				this.customSizeFlag = true;
				if (this.unitSign == "cm") {
					this.sliderOptions.min = Number(
						(this.getMinSize().width * 0.3937).toFixed(0)
					);
				} else {
					this.sliderOptions.min = this.getMinSize().width;
				}
				this.currentStep = item.paramName;
				this.selectedData["What Size Would You Like?"] =
					this.selectedData["What Size Would You Like?"] || null;
				return;
			} else {
				this.customSize = 0;
				this.customSizeFlag = false;
			}
			this.checking = true;
			this.isFlow = true;
			this.currentFontSize = val;
			delete this.selectedData["What Size Would You Like?"];

			this.neonSampleData = JSON.parse(
				this.currentTemplateChild.neonSampleData
			).find((x) => {
				return x.size == this.currentFontSize.paramName;
			});

			if (!this.isImgTemplate) {
				try {
					this.neonSampleData.fontPosttion.forEach((item, index) => {
						item.fontFamily =
							this.selectedData["Your Text"][index]["Select Font"].paramName;
						item.color =
							this.selectedData["Your Text"][index]["Select Color"].paramCode;
					});
				} catch (error) {
					console.log(error);
				}
			}

			if (this.currentSampleData) {
				let temp = this.neonSampleData.color.find((x) => {
					return x.name == this.currentSampleData.name;
				});
				this.currentSampleData.img = temp?.img;
			}
			this.currentStep = item.paramName;
			this.clickFun(item);
		},
		nextStepFun(item, type) {
			if (type == "dialog") {
				this.preViewDialog = false;
				this.selectedData[this.previewKey] = item;
				this.noValueFun();
			}
			if (this.checkForm()) {
				this.checking = false;
				this.scrollFun("theLast");
				this.currentStep = null;
				this.showDetails = true;
				this.showTrapezoid = true;
			}

			if (this.currentStep == item.paramName) {
				this.errorMessage(item.paramName);
			}
		},
		clickFun(item) {
			this.filterStepsFun().then((res) => {
				if (item) {
					if (
						item.paramName == "Your Text" ||
						item.paramName == "Select Size" ||
						item.paramName == "Select Turnaround Time"
					) {
						if (this.checkForm(item.paramName)) {
							this.checking = false;
							this.scrollFun("theLast");
							this.currentStep = null;
							if (item.paramName != "Your Text") {
								this.showDetails = true;
							}
							this.showTrapezoid = true;
						} else {
							this.scrollFun(item.paramName);
						}
					}
				} else {
					if (this.checkForm()) {
						this.checking = false;
						this.scrollFun("theLast");
						this.currentStep = null;
						this.showDetails = true;
						this.showTrapezoid = true;
					}
				}
				this.noChoiceData = res;
				console.log(321321321);
				this.debounceCalcPrice();
			});
		},
		shutMask() {
			// 点击遮罩关闭文字项弹框
			const dom = this.$refs.fontParam;
			if (dom) dom.forEach((x) => x.closeAll());
			this.checking = false;
			this.currentStep = null;
		},
		getInfo(name) {
			return this.generalData.find((x) => {
				return x.paramName == name;
			});
		},
		filterRef(val) {
			return val.split(" ").join("_");
		},
		//滚动
		scrollFun(target, force = false) {
			if (this.device == "mb" && !force) return;
			try {
				let parentTop = (this.pageQuoteDialog || this.isIframe)
					? document.getElementById("neon")
					: window; // o2o弹窗与页面的区别
				requestAnimationFrame(() => {
					let objTop = document.getElementById(target)?.offsetTop;
					parentTop.scroll({
						top: objTop - this.topH,
						left: 0,
						behavior: "smooth",
					});
				});
			} catch (err) {
			}
		},
		// 按钮是否灰色0
		disabledNext(val, idx) {
			let flag = false;
			if (val.paramName != "Your Text") {
				if (
					val.paramName == "Select Size" &&
					this.selectedData["Select Size"]?.paramName == "Custom Size"
				) {
					flag = this.customSize <= 0;
				} else if (
					val.paramName == "Greeting Card" &&
					this.selectedData["Greeting Card"]?.paramCode == "custom"
				) {
					flag = !this.greetingTextLine.trim().length > 0;
				} else if (val.paramName == "Select Type") {
					flag = this.selectedData["Select Type"].length == 0;
				} else if (val.paramName == "What Size Would You Like?") {
					flag = this.artworkInfo.width <= 4;
				} else {
					flag = !this.selectedData[val.paramName];
				}
			} else {
				if (this.isText) {
					if (this.tabName == "Upload") {
						flag = this.uploadFileList.length ? false : true;
						// flag = !this.checkUploadFontColor(this.uploadFontAndColor);
					} else {
						if (
							this.textDesign &&
							!this.checkFormAboutFont(this.selectedFontData)
						) {
							flag = true;
						} else {
							flag = !this.onlyEmptyAboutFont(this.selectedFontData);
						}
					}
				} else if (this.tabName == "Upload") {
					flag = this.uploadFileList.length ? false : true;
				} else {
					if (
						!this.checkFormAboutFont(this.selectedFontData) &&
						!this.onlyEmptyAboutFont(this.selectedFontData)
					) {
						flag = true;
					} else {
						flag = !this.onlyEmptyAboutFont(this.selectedFontData);
					}
				}
			}
			return flag;
		},
		// 校验询盘的颜色和字体
		checkUploadFontColor(val) {
			let flag = false;
			if (val && val.length > 0) {
				if (val[0]["Select Font"] && val[0]["Select Color"]) {
					flag = true;
				}
			}
			return flag;
		},
		// 校验非纯文字
		onlyEmptyAboutFont(val) {
			let flag = true;
			if (!val) return flag;
			val.forEach((obj) => {
				for (const key in obj) {
					if (key == "Your Text") {
						if (obj[key]?.length > 0 && obj[key].trim() == "") {
							flag = false;
						}
					} else {
						if (!obj[key]) {
							flag = false;
						}
					}
				}
			});
			return flag;
		},
		// 校验
		checkForm(name) {
			let boo = true;
			let that = this;
			let noPass = function (name, key) {
				that.checking = true;
				if (!name) {
					that.scrollFun(key); //根据key滚动
				}
				that.currentStep = name ? name : key; //下一步步骤名
				return false;
			};

			for (let key in this.selectedData) {
				if (key === "Your Text") {
					if (this.isImgTemplate) {
					} else if (this.tabName == "Upload") {
						if (!this.uploadFileList.length) {
							boo = noPass(name, key);
							break;
						}
					} else if (
						this.isText &&
						!this.textDesign &&
						!this.onlyEmptyAboutFont(this.selectedData[key])
					) {
						boo = noPass(name, key);
						break;
					} else if (
						!this.isText &&
						!this.textDesign &&
						!this.onlyEmptyAboutFont(this.selectedData[key])
					) {
						boo = noPass(name, key);
						break;
					} else if (
						this.isText &&
						this.textDesign &&
						!this.onlyEmptyAboutFont(this.selectedData[key])
					) {
						boo = noPass(name, key);
						break;
					} else if (
						this.isText &&
						this.textDesign &&
						!this.checkFormAboutFont(this.selectedData[key]) &&
						this.onlyEmptyAboutFont(this.selectedData[key])
					) {
						boo = noPass(name, key);
						break;
					} else {
					}
				} else if (
					key == "Designs Or Upload Your Artwork" &&
					(this.isText || this.tabName == "Upload")
				) {
				} else if (
					(key == "What Size Would You Like?" || key == "Select Type") &&
					this.tabName != "Upload"
				) {
				} else if (key == "Select Type" && this.selectedData[key].length == 0) {
					boo = noPass(name, key);
					break;
				} else if (
					key == "What Size Would You Like?" &&
					this.artworkInfo.width < this.imgMinWidth
				) {
					boo = noPass(name, key);
					break;
				} else if (
					key == "Greeting Card" &&
					this.selectedData[key]?.paramCode == "custom" &&
					this.greetingTextLine.trim().length == 0
				) {
					boo = noPass(name, key);
					break;
				} else if (
					key == "Select Size" &&
					this.selectedData[key]?.paramName == "Custom Size" &&
					this.customSize <= 0
				) {
					boo = noPass(name, key);
					break;
				} else if (key == "Select Size" && this.tabName == "Upload") {
				} else if (!this.selectedData[key]) {
					boo = noPass(name, key);
					break;
				}
			}
			return boo;
		},

		/**
		 * @param {*} arr 校验数组
		 * @param {*} accurate 是否精确返回没通过校验的字段
		 */
		checkFormAboutFont(arr, accurate) {
			if (!arr) return;
			if (accurate) {
				const invalidKeys = [];
				for (const obj of arr) {
					for (const key in obj) {
						if (!obj[key]) {
							invalidKeys.push(key);
						}
					}
				}
				return invalidKeys;
			} else {
				return arr.every((o) => {
					return Object.values(o).every((v) => v);
				});
			}
		},

		async getByPId() {
			await medalsApi
				.getByPId({
					pid: this.pid,
				})
				.then(async (res) => {
					this.neonStyleData = res.data;
					this.cateId = this.neonStyleData[0].id;
					if (this.neonStyleData[0].imageJson) {
						this.roundList = JSON.parse(this.neonStyleData[0].imageJson);
						this.currentRound = this.roundList[0];
					}
					// this.defaultData = this.medalStyleData.childList[0];
					await this.getCateParamRelationByCateId();
				});
		},
		getCateParamRelationByCateId() {
			return new Promise((resolve, reject) => {
				medalsApi
					.getCateParamRelationByCateId({
						cateId: this.cateId,
					})
					.then((res) => {
						this.generalData = res.data;
						this.generalData.sort((a, b) => {
							return a.stepIndex - b.stepIndex;
						});
						//副本

						let selectedData = JSON.parse(JSON.stringify(this.selectedData));
						let tempObj = {};
						this.generalData.forEach((item) => {
							item.childList.map((x) => {
								x.isHidden = false;
							});
							if (item.paramName == "Select Type") tempObj[item.paramName] = [];
							else tempObj[item.paramName] = null;
						});
						this.currentFont = this.getInfo("Select Font").childList[0];
						for (let item in tempObj) {
							if (selectedData[item]) {
								tempObj[item] = selectedData[item];
							}
						}

						//获取默认折扣(取最小的)
						let disObj = {};
						this.generalData.forEach((x) => {
							if (x.paramName === "Select Turnaround Time") {
								x.childList = x.childList.sort((a, b) => {
									return b.priceInfo.unitPercent - a.priceInfo.unitPercent;
								});
								disObj = x;
							} else if (
								x.paramName === "Select Your Backboard Color" &&
								this.tabName === "Upload"
							) {
								// 上传图片过滤镜面选项
								const fs = ["Stunning Gold Acrylic", "Classy Silver Acrylic"];
								x.childList = x.childList.filter(
									(x) => !fs.includes(x.paramName)
								);
							}
						});
						this.neonDiscountData = disObj.childList[0];
						//副本赋值
						delete tempObj["Select Font"];
						delete tempObj["Select Color"];
						this.selectedData = tempObj;
						if (this.isInquiryCur && this.tabName != "Upload") {
							let postData = {
								cateId: 67,
								proId: this.proId,
								keyword: "",
								paramType: "NEONSAMPLE",
								page: 1,
								pageSize: 99999,
								isQuote: 1,
							};
							neonApi.getTemplates(postData).then((res) => {
								let temp = res.data.content[0];
								this.currentTemplateFun({
									type: 1,
									val: temp,
								});
							});
						} else {
							if (!this.currentTemplate) {
								this.dialogVisible = true;
							}
						}
						resolve(res);
					});
			});
		},
		// 正确计算字符数量（文字间的多空格算一个字符，文字间的回车，有一个算一个）
		countCharacterNums(obj) {
			let count = 0;
			let text = (obj["Your Text"] || obj.placeholder).trim();
			let strSplit = text.split("\n");
			for (let j = 0; j < strSplit.length; j++) {
				let strSplitUnit = strSplit[j].trim().replace(/[ \t\v]+/g, " ");
				if (strSplitUnit.length != 0) count += strSplitUnit.length;
			}
			count += strSplit.length - 1;
			return count;
		},

		//计算尺寸价格
		getNeonFontSizePrice() {
			// if (this.selectedData["Select Font"]) {
			let temp = [];
			if (this.isImgTemplate) {
				temp = null;
			} else {
				this.selectedFontData.forEach((item) => {
					temp.push({
						neonFontId: item["Select Font"]?.priceInfo.id,
						charQuantity: this.countCharacterNums(item),
					});
				});
			}
			let obj = {
				cateId: this.cateId,
				neonSampleId: this.currentTemplateChild.priceInfo.id,
				neonTemplateQuantityList: temp,
			};

			if (this.isText) {
				obj.neonSizeList = this.ableSizeListDetail.map((x) => {
					let rectArr = Object.values(x.rectInfo);
					return {
						neonLeftSizeId: x.priceInfo.id,
						lengthInch: Math.max(...rectArr),
						widthInch: Math.min(...rectArr),
					};
				});
			}

			neonApi.getNeonFontSizePrice(obj).then(async (res) => {
				let fontSizePriceData = res.data;
				let target = this.getInfo("Select Size");
				target.childList = target.childList.map((x) => {
					fontSizePriceData.forEach((y) => {
						if (x.priceInfo.id == y.sizeParamPriceId) {
							x.unitPrice = y.unitPrice;
						}
					});
					return x;
				});
			});
		},

		//算价格
		async calculate() {
			// 选择了Custom Size不计算价格
			if (this.currentFontSize && this.currentTemplate) {
				let postData = await this.getPriceParam();
				neonApi.calculate(postData).then((res) => {
					this.priceData = res.data;
					this.onlyInquiry = (!this.isCropFile || !!res.data.onlyAddInquiry) || !this.priceData.totalPrice;
					this.$nextTick(() => (this.isBack = false));
				});
			}
		},
		// 价格数据
		getPriceParam() {
			return new Promise((resolve, reject) => {
				let paramIdList = [],
					neonTemplateQuantityList = [];
				for (let key in this.selectedData) {
					if (key == "Your Text" && this.specialColorFlag) {
						paramIdList.push(
							this.selectedData[key][0]["Select Color"]?.priceInfo.id
						);
					} else if (key == "Designs Or Upload Your Artwork") {
					} else if (key == "Select Type" && this.tabName == "Upload") {
						this.selectedData[key].forEach((x) => {
							paramIdList.push(x.priceInfo.id);
						});
					} else if (
						this.selectedData[key]?.paramType == "NORMAL" ||
						this.selectedData[key]?.paramType == "COLOR"
					) {
						paramIdList.push(this.selectedData[key]?.priceInfo.id);
					}
				}
				if (!this.isImgTemplate && this.tabName != "Upload") {
					neonTemplateQuantityList = this.selectedData["Your Text"].map(
						(item) => {
							return {
								neonFontId: item["Select Font"]?.priceInfo.id,
								charQuantity: this.countCharacterNums(item),
								neonColorId: item["Select Color"]?.priceInfo.id,
							};
						}
					);
				}
				let obj = {
					projectName: this.projectName,
					cateId: this.cateId,
					paramIdList: paramIdList,
					discountId: this.neonDiscountData?.priceInfo?.id,
					neonFontId: this.selectedData["Select Font"]?.priceInfo.id,
					neonSizeId:
						this.selectedData["Select Size"]?.priceInfo.id ||
						this.currentFontSize?.priceInfo.id,
					isPicNeon: this.tabName == "Upload" ? 1 : 0,
					neonSampleId: this.currentTemplateChild.priceInfo.id,
					neonTemplateQuantityList: neonTemplateQuantityList,
					quantity: 1,
				};

				const rectArr = [
					Number(this.finaSize.width),
					Number(this.finaSize.height),
				].map((x) => x && Number(x.toFixed(0)));
				obj.neonCustomSize = {
					lengthInch: Math.max(...rectArr),
					widthInch: Math.min(...rectArr),
					isCustomSize: this.customSizeFlag ? 1 : 0,
				};

				if (this.customSizeFlag) {
					const range = this.findSizeRange(
						this.ableSizeListDetail,
						this.customSize
					);
					obj.neonCustomSize.neonLeftSizeId = range.lower?.priceInfo.id;
					obj.neonCustomSize.neonRightSizeId = range.upper?.priceInfo.id;
					obj.neonCustomSize.leftLength = Math.max(
						range.lower?.rectInfo.width,
						range.lower?.rectInfo.height
					);
					obj.neonCustomSize.rightLength = Math.max(
						range.upper?.rectInfo.width,
						range.upper?.rectInfo.height
					);
				}
				resolve(obj);
			});
		},
		//询盘数据
		getQuoteParam() {
			return new Promise(async (resolve, reject) => {
				let copyGeneralData = JSON.parse(JSON.stringify(this.generalData));
				let copySelectedData = JSON.parse(JSON.stringify(this.selectedData));
				for (let x in copySelectedData) {
					copyGeneralData.forEach((item) => {
						if (item.paramName == "Designs Or Upload Your Artwork") {
							// item.childList = this.queryTemplate ? [this.queryTemplate] : [];
							item.childList = [];
						} else if (
							item.paramName == "Select Color" ||
							item.paramName == "Select Font"
						) {
							item.childList = [];
						} else if (
							item.paramName == "Hanging Options" &&
							!copySelectedData["Hanging Options"]
						) {
							item.childList = [];
						} else if (
							item.paramName == "Select Your Backboard Color" &&
							!copySelectedData["Select Your Backboard Color"]
						) {
							item.childList = [];
						} else if (
							item.paramName == "Select Type" &&
							!copySelectedData["Select Type"]
						) {
							item.childList = [];
						} else if (
							item.paramName == "What Size Would You Like?" &&
							this.tabName == "Enter Your Text"
						) {
							item.childList = [];
						} else if (item.paramName == "Your Text") {
							if (this.tabName == "Upload" || this.isImgTemplate) {
								item.childList = [];
							} else {
								copySelectedData[item.paramName].forEach((item2, index) => {
									if (item.childList[index]) {
										item.childList[index].alias =
											copySelectedData[item.paramName][index]["Your Text"] ||
											copySelectedData[item.paramName][index].placeholder +
											" " +
											copySelectedData[item.paramName][index]["Select Font"]
												.paramName +
											" " +
											copySelectedData[item.paramName][index]["Select Color"]
												.alias;
									} else {
										let temp = JSON.parse(JSON.stringify(item.childList[0]));
										item.childList[index] = temp;
										item.childList[index].alias =
											copySelectedData[item.paramName][index]["Your Text"] ||
											copySelectedData[item.paramName][index].placeholder +
											" " +
											copySelectedData[item.paramName][index]["Select Font"]
												.paramName +
											" " +
											copySelectedData[item.paramName][index]["Select Color"]
												.alias;
									}
								});
							}
						} else if (
							item.paramName == "What Size Would You Like?" &&
							this.tabName == "Upload"
						) {
							this.$nextTick(() => {
								item.childList = [
									{
										alias: ` L (${(
											(Number(this.finaSize.width) / 0.3937) *
											this.unitConvertRatio
										).toFixed(0)})${this.unitSign} * H (${(
											(Number(this.finaSize.height) / 0.3937) *
											this.unitConvertRatio
										).toFixed(0)})${this.unitSign} `,
										parentId: 1,
									},
								];
							});
						} else if (
							item.paramName == "Greeting Card" &&
							copySelectedData[item.paramName]?.paramCode == "custom"
						) {
							item.childList = [
								{
									alias: copySelectedData[item.paramName].alias,
									remark: this.greetingTextLine,
									parentId: 1,
								},
							];
						} else if (
							item.paramName == "Budget Idea" &&
							this.tabName == "Upload"
						) {
							item.childList = [
								{
									alias:
										copySelectedData[item.paramName] +
										this.currentCurrency.symbol,
									parentId: 1,
								},
							];
						} else if (item.paramName == x) {
							if (item.paramName == "Select Size" && copySelectedData[x]) {
								copySelectedData[x].priceInfo.quoteNeonFontSizeList = [];
							}
							if (item.paramName == "Select Type") {
								item.childList = copySelectedData[x];
							} else if (copySelectedData[x]) {
								item.childList = [copySelectedData[x]];
							} else {
								item.childList = [];
							}
						}
					});
				}

				let fontData = [];
				if (
					copySelectedData["Your Text"] &&
					copySelectedData["Your Text"].length > 0
				) {
					const copySelectedFontData = copySelectedData["Your Text"];
					// 删除原来的避免重复展示
					const idx = copyGeneralData.findIndex((x) => {
						return x.paramName == "Your Text";
					});
					if (idx > -1) copyGeneralData.splice(idx, 1);
					const sortCopySelectedData = copySelectedFontData.map((item) => {
						return {
							"Your Text": item["Your Text"] || item.placeholder,
							"Select Font": item["Select Font"],
							"Select Color": item["Select Color"],
							tubeColor: item["tubeColor"],
						};
					});
					const def = copyGeneralData[0];
					const defC = sortCopySelectedData[0]["Select Font"];
					await sortCopySelectedData.forEach((obj, index) => {
						for (const key in obj) {
							const val = obj[key];
							if (key == "Your Text") {
								fontData.push({
									...def,
									alias: `${this.lang.neon.detailTextLine} ${index + 1}`,
									childList:
										this.tabName == "Enter Your Text"
											? [
												{
													...defC,
													alias: val.replace(/[\r\n]/g, " "),
												},
											]
											: [],
								});
							}
							if (key == "Select Font") {
								fontData.push({
									...def,
									alias: `${this.lang.neon.detailLine} ${index + 1} ${
										this.lang.neon.detailFontColor
									}`,
									childList: [
										{
											...defC,
											parentId: 1,
											alias: val?.alias ? `${val?.alias};` : "",
										},
									],
								});
							}
							if (key == "Select Color") {
								let reaIndex = index <= 0 ? 1 : index * 2 + 1;
								fontData[reaIndex].childList[0].alias =
									fontData[reaIndex].childList[0].alias +
									(val?.alias ? `${val?.alias};` : "");
							}
							if (key == "tubeColor") {
								let reaIndex = index <= 0 ? 1 : index * 2 + 1;
								fontData[reaIndex].childList[0].alias =
									fontData[reaIndex].childList[0].alias +
									(val?.alt ? `${val?.alt}` : "");
							}
						}
					});
					copyGeneralData = fontData.concat(copyGeneralData);
				}
				if (this.tabName != "Upload") {
					let ktt = copyGeneralData.find((x) => {
						return x.paramName == "Select Size";
					});
					if (ktt) {
						this.$nextTick(() => {
							ktt.childList[0].alias =
								ktt.childList[0].alias +
								` L (${(
									(Number(this.finaSize.width) / 0.3937) *
									this.unitConvertRatio
								).toFixed(0)})${this.unitSign} * H (${(
									(Number(this.finaSize.height) / 0.3937) *
									this.unitConvertRatio
								).toFixed(0)})${this.unitSign} `;
						});
					}
				} else {
					let targetIndex = copyGeneralData.findIndex(
						(x) => x.paramName == "Select Size"
					);
					copyGeneralData.splice(targetIndex, 1);
				}
				let files = [],
					comments =
						(this.txtFromAi ? `${this.txtFromAi}.` : "") + this.customComment,
					canvasImg = document
						.querySelector(".canvasEle")
						.getAttribute("data-src");

				files = [...this.uploadFileList, ...this.uploadList].map((x) => {
					return x.secure_url;
				});
				// 用于回填的报价参数
				let backFillSelectedData = {};
				for (const key in this.selectedData) {
					const item = this.selectedData[key];
					if (item instanceof Array) {
						if (key == "Your Text") {
							backFillSelectedData[key] = item.map((x) => {
								return {
									...x,
									"Select Color": x["Select Color"].id,
									"Select Font": x["Select Font"].id,
								};
							});
						} else {
							backFillSelectedData[key] = item.map((x) => x.id);
						}
					} else if (item) {
						if (
							key == "Designs Or Upload Your Artwork" ||
							key == "What Size Would You Like?"
						) {
							backFillSelectedData[key] = item;
						} else if (key == "Greeting Card") {
							backFillSelectedData[key] = {
								id: item.id,
								remark: this.greetingTextLine,
							};
						} else if (key == "Select Turnaround Time") {
							backFillSelectedData[key] = {
								id: item.id,
								remark: this.customComment,
							};
						} else {
							backFillSelectedData[key] = item.id;
						}
					}
				}
				const obj = {
					classificationData: this.neonStyleData[0],
					finaData: copyGeneralData,
					backFillParam: {
						currentTemplateId: this.currentTemplate.id,
						tabName: this.tabName,
						backFillSelectedData,
						artworkInfo: this.artworkInfo,
						artworkImg: this.artworkImg,
					},
					fontData: {
						fontImgCustom: files,
						comments: comments,
					},
				};

				if (this.tabName == "Enter Your Text") {
					let temp = await this.uploadCanvas(canvasImg);
					this.absoluteImg = temp.getAttribute("src");
					obj.canvasData = {};
					obj.fontData.dzImage = this.absoluteImg;
					obj.canvasData.url = this.absoluteImg;
				} else this.absoluteImg = "";
				resolve(obj);
			});
		},

		//询盘
		submitInquiry() {
			if (this.tabName == "Upload") {
				if (this.checkForm()) {
					this.addInquiry();
				} else {
					this.errorMessage(this.currentStep);
				}
			} else {
				if (this.checkForm()) {
					this.addInquiry();
				} else {
					this.errorMessage(this.currentStep);
				}
			}
		},
		//购物车
		addToCart() {
			if (this.tabName == "Upload" && this.uploadFileList.length == 0) {
				this.$toast.error(this.lang.neon.noUploadFile);
				this.currentStep = "Your Text";
				this.scrollFun("Your Text");
				this.checking = true;
				this.showDetails = false;
				return;
			}
			if (this.checkForm()) {
				this.$gl.show();
				this.$refs.textDraggableCanvas.$refs.canvasFactory.drawAnimatStatic();
				setTimeout(() => {
					this.addCart();
				}, 300);
			} else {
				this.errorMessage(this.currentStep);
			}
		},
		//添加询盘
		async addInquiry() {
			if (this.tabName == "Upload" && this.uploadFileList.length == 0) {
				this.$toast.error(this.lang.neon.noUploadFile);
				this.currentStep = "Your Text";
				this.scrollFun("Your Text");
				this.checking = true;
				this.showDetails = false;
			}
			//如果是临时项目
			this.$refs.textDraggableCanvas.$refs.canvasFactory.drawAnimatStatic();
			if (this.$store.state.isTemporary) {
				this.$store.commit("setMask", "temporary");
				return false;
			}
			this.infoDialogVisible = true;
		},
		async getValueFun({subject, ...val}) {
			this.$gl.show();
			let quoteParam = await this.getQuoteParam();
			let priceParam = await this.getPriceParam();

			//询盘如果有期待时间，finaData手动添加数据
			if (val.expectTime) {
				quoteParam.finaData.push({
					parentId: 0,
					alias: "Expected Delivery Date",
					childList: [
						{
							parentId: 10000,
							alias: val.expectTime,
						},
					],
				});
			}
			let data = {
				platformProductId: this.platformProductId,
				proId: this.proId,
				email: "",
				productsName: "Neon",
				quoteCateId: this.pid,
				quoteCateChildId: this.cateId,
				isMobile: this.device === "mb" ? 1 : 0,
				isPicNeon: this.tabName == "Upload" ? 1 : 0,
				buyType: 7,
				quotePriceParam: JSON.stringify(priceParam),
				quoteParam: JSON.stringify(quoteParam),
				notes: "",
				...val,
				subject: this.customComment,
				telephone: val.areaCode + "-" + val.telephone,
				isAi: !!this.$route.query.aiText ? 1 : 0,
				isSmallQty: getIsSmallQty(
					this.neonDiscountData,
					this.$store.state.enableTurnaroundTimeCheck
				),
			};
			this.pageQuoteDialog && this.$store.commit("setMask", false);
			this.otoEditInquiry(data);
		},
		otoEditInquiry(data) {
			otoEditInquiry(data).then((res) => {
				// this.inquiryId = res.data;
				this.$gl.hide();
				this.$confirm(this.lang.p22, this.lang.p21, {
					confirmButtonText: this.lang.Confirm,
					type: "success",
					showCancelButton: false,
					center: true,
				}).finally(() => {
					if (this.isIframe) {
						let targetWindow = window.opener || window.parent; // 目标窗口，可以是父窗口或打开当前窗口的窗口
						const message = {
							type: this.$route.query.isBack ? "toCart" : "toHome",
						};
						if (this.$route.query.isBack) message.data = {isUpdate: 1};
						targetWindow.postMessage(message, window.origin); // 发送消息
					} else {
						window.location.href = "/";
					}
				});
			});
		},
		//购物车
		async addCart() {
			let quoteParam = await this.getQuoteParam();
			let priceParam = await this.getPriceParam();
			let data = {
				proId: this.proId,
				uuid: this.isLogin ? null : this.userUUID,
				userId: this.isLogin ? this.userId : null,
				buyType: 7,
				quoteCateId: this.pid,
				isPicNeon: this.tabName == "Upload" ? 1 : 0,
				isMobile: this.device === "mb" ? 1 : 0,
				quoteCateChildId: this.cateId,
				quantity: 1,
				quotePriceParam: JSON.stringify(priceParam),
				quoteParam: JSON.stringify(quoteParam),
				isAi: !!this.$route.query.aiText ? 1 : 0,
				isSmallQty: getIsSmallQty(
					this.neonDiscountData,
					this.$store.state.enableTurnaroundTimeCheck
				),
			};
			if (this.$route.query.isBack) {
				data.id = this.$route.query.id;
				//	alert(JSON.stringify(data));
				otoEditCart(data)
					.then((res) => {
						this.$toast.success(res.message);
						let targetWindow = window.opener || window.parent;
						targetWindow.postMessage(
							{
								type: "closeDialog",
								data: {
									isUpdate: 1,
								},
							},
							window.origin
						);
					})
					.finally(() => {
						setTimeout(() => {
							this.loadAddCart = false;
						}, 1000);
					});
			} else {
				otoAddCart(data, this.priceData).then((res) => {
					this.$toast.success(res.message);
					this.pageQuoteDialog && this.$store.commit("setMask", false);
					if (this.isIframe) {
						let targetWindow = window.opener || window.parent; // 目标窗口，可以是父窗口或打开当前窗口的窗口
						const message = {type: "toCart"};
						targetWindow.postMessage(message, window.origin);
					} else {
						this.$router.push({
							path: "/cart",
						});
					}
					this.$gl.hide();
				});
			}
		},
		getAll() {
			neonApi.getAll({cateId: 176}).then(async (res) => {
				this.canNotParamComposeList = res.data;
			});
		},
		//不可选参数过滤
		filterStepsFun() {
			return new Promise((resolve, reject) => {
				let tempArr = [],
					result = [];
				tempArr.push(this.currentTemplateChild);
				for (let key in this.selectedData) {
					if (this.selectedData[key] && this.selectedData[key] instanceof Array)
						tempArr = [...tempArr, this.selectedData[key]];
					else if (this.selectedData[key]) tempArr.push(this.selectedData[key]);
				}
				this.canNotParamComposeList.map((x) => {
					let cArr = x.canNotParamCompose.split(","),
						bArr = cArr;
					for (let i = 0; i < cArr.length; i++) {
						if (bArr.length == 1) {
							result = result.concat(bArr);
							break;
						} else {
							tempArr.map((z) => {
								if (z instanceof Array) {
									z.forEach((zItem) => {
										for (let i in zItem) {
											if (i == "Select Color" || i == "Select Font") {
												bArr = bArr.filter((y) => {
													return zItem[i]?.priceInfo.id != y;
												});
											}
										}
									});
								} else {
									bArr = bArr.filter((y) => {
										return z.priceInfo?.id != y;
									});
								}
							});
						}
					}
				});
				resolve(result);
			});
		},

		getFile(val) {
			this.uploadFileList = val;
		},
		needBanScroll(val) {
			if (this.checking && this.device == "mb") {
				return this.fixedMatchs.find((x) => {
					return x.name == val;
				});
			}
		},
		debounce(func, delay = 1000, immediate = false) {
			//闭包
			let timer = null;
			//不能用箭头函数
			return function () {
				if (timer) {
					clearTimeout(timer);
				}
				if (immediate && !timer) {
					func.apply(this, arguments);
				}
				timer = setTimeout(() => {
					func.apply(this, arguments);
				}, delay);
			};
		},
		listenScroll(e) {
			const scrollDom = document.getElementById("neon");
			// 手机端一次划完特点
			const height = document.getElementById("neonTop").offsetHeight;
			if (
				scrollDom.scrollTop > this.lastScrollTop &&
				scrollDom.scrollTop < height
			) {
				this.$nextTick(async () => {
					scrollDom.scrollTo({
						top: document.getElementById("neonTop").offsetHeight,
					});
				});
			}
			this.lastScrollTop = scrollDom.scrollTop;
		},
		// 监听selectSizeDom
		observeSelectSize() {
			const selectSize = document.querySelector(".selectSize");
			const observer = new IntersectionObserver(
				(entries) => {
					entries.forEach((entry) => {
						// 当元素完全不可见时
						if (entry.intersectionRatio === 0 && this.isFlow === true) {
							this.isFlow = false;
						}
					});
				},
				{
					threshold: 0, // 完全不可见时触发
				}
			);

			if (selectSize) {
				observer.observe(selectSize);
			}
		},
		optsShow() {
			if (this.device != "pc") return true;
			else return !this.showDetails && this.tabName !== "Upload";
		},

		showAcrglicBackboard(obj) {
			// 将字符串转为数字
			let width = Number(obj.width);
			let height = Number(obj.height);

			// 确保 width 是较大的，height 是较小的
			if (width < height) {
				[width, height] = [height, width];
			}
			// 判断条件
			return width < 78 && height < 23;
		},
	},
	computed: {
		previewImgData() {
			return this.$store.state.neonPreview;
		},
		previewImgDialog() {
			return this.$store.state.neonPreview.show;
		},
		neonNeedCrop() {
			return this.artworkInfo.width > 45 || this.artworkInfo.height > 45;
		},
		computedArtwork() {
			return `${this.artworkInfo.width + '"'} × ${
				this.artworkInfo.height + '"'
			}`;
		},
		computedArtworkInfo() {
			return `${
				(Number(this.artworkInfo.width) / 0.3937).toFixed(0) + "cm"
			} × ${(Number(this.artworkInfo.height) / 0.3937).toFixed(0) + "cm"}`;
		},
		hasChinese() {
			const rule =
				/[\u4E00-\u9FFF\u3040-\u30FF\u31F0-\u31FF\uFF00-\uFFEF\uAC00-\uD7AF\u1100-\u11FF\u3130-\u318F\uA960-\uA97F\uD7B0-\uD7FF]/;
			return this.selectedFontData.some((x) => rule.test(x["Your Text"]));
		},
		langObj() {
			return this.$store.state.language || {};
		},
		lang() {
			return this.$store.getters.lang.quote || {};
		},
		proType() {
			return this.$store.state.proType;
		},
		proId() {
			return this.$store.state.proId;
		},
		userUUID() {
			return this.$store.state.userUUID;
		},
		projectName() {
			return this.$store.state.proName;
		},
		userId() {
			return this.$store.state.userInfo?.id;
		},
		isLogin() {
			return this.$store.getters.isLogin;
		},
		userInfo() {
			return this.$store.state.userInfo;
		},
		device() {
			return this.$store.state.device;
		},
		onlyUploadAndMB() {
			return this.device == "mb" && this.tabName == "Upload";
		},
		queryChildId() {
			return this.$route.query.childId;
		},
		queryParentId() {
			return this.$route.query.parentId;
		},
		distanceToBottom() {
			return window.innerHeight - 360 - 68 - 40;
		},
		currentCurrency() {
			return this.$store.state.currency;
		},
	},
	watch: {
		neonNeedCrop: {
			handler(newV) {
				const selectType = this.getInfo("Select Type");
				const needHidden = ["Gradient"];
				selectType.childList = selectType.childList.map((y) => {
					let isHiddenType = 0;
					if (needHidden.indexOf(y.paramName) > -1) isHiddenType = Number(newV);
					return {...y, isHiddenType};
				});
			},
		},
		selectedFontData: {
			handler(newV) {
				if (this.onlyEmptyAboutFont(newV)) {
					localStorage.setItem("neonBackFillText", JSON.stringify(newV));
				}
			},
			deep: true,
		},
		tabName: {
			handler(newValue, oldValue) {
				if (this.isBack) return;
				if (newValue == "Upload") {
					this.needCanvas = false;
					this.selectedData = {};
					this.getByPId();
				} else {
					this.selectedData = {};
					this.artworkImg = {};
					this.isCropFile = true;
					this.getAll();
					this.getByPId();
					this.needCanvas = true;
					const store = JSON.parse(localStorage.getItem("neonBackFillText"));
					if (store) {
						this.selectedFontData["Your Text"] = store;
						this.selectedData["Your Text"] = store;
					}


				}
			},
			deep: true,
		},
		"uploadFileList.length": {
			handler(newV, oldV) {
				// 添加上传文件
				if (newV > oldV && newV == 1 && !this.isBack) {
					this.imageSrc = this.uploadFileList[0];
					const extension = this.imageSrc.secure_url
						.substring(this.imageSrc.secure_url.lastIndexOf(".") + 1)
						.toLowerCase();
					if (extension == "pdf") {
						let reader = new FileReader();
						reader.readAsDataURL(this.imageSrc.file);
						reader.onload = async (e) => {
							this.imageSrc.secure_url = await pdfToImg(e.target.result);
						};
					}
					if (extension == "psd") {
						let reader = new FileReader();
						reader.readAsDataURL(this.imageSrc.file);
						reader.onload = async (e) => {
							let PSD = require("@/assets/js/psd");
							let psd = await PSD.fromURL(e.target.result);
							this.imageSrc.secure_url = psd.image.toBase64();
						};
					}
					this.isCropFile = this.supportCropType.includes(extension);
					if (this.isCropFile) this.cropperDialog = true;
					else {
						this.artworkImg = "noImg";
						this.onlyInquiry = true;
						this.debounceCalcPrice();
					}

					this.isCustomSizeInput = true;
					this.tabName = "Upload";
					this.dialogForUpload = false;
				} else {
					if (!newV) {
						this.artworkImg = "noImg";
					}
				}
			},
		},
		currentTemplate: {
			handler(newValue, oldValue) {
				this.textDesign = false;
				if (newValue.isImgTemplate) {
					this.isImgTemplate = 1;
					this.isText = false;
				} else {
					this.isImgTemplate = 0;
					if (newValue.isTextTemplate) {
						if (newValue.paramName == "Custom Text Signs") {
							this.textDesign = true;
						}
						this.isText = true;
					} else {
						this.isText = false;
					}
				}
			},
		},
		neonSampleData: {
			handler(newValue, oldValue) {
				if (newValue) {
					let temp = [];
					this.selectedFontData.forEach((item) => {
						this.textDesign
							? temp.push(item["Your Text"])
							: temp.push(item["Your Text"]?.replace(/\n/g, ""));
					});
					let allColor = this.getInfo("Select Color").childList;
					let allFamily = this.getInfo("Select Font").childList;
					let getChild = function (list, key, target, targetKey) {
						return list.find((x) => {
							return x[key] == target[targetKey];
						});
					};
					if (newValue.fontPosttion.length) {
						if (this.changeTemplateFlag && !this.isBack) {
							let tubeColor = {
								alt: this.lang.neon.defTubeAlt,
								color: "#ffffff",
								name: "darkWhite",
								url: "https://oss-static-cn.liyi.co/web/quoteManage/20231019/ffffff_2052taQmnT.png",
							};
							this.selectedFontData = [];
							let fdDefColor = this.fdNeonType === 'rgbChanging' ? allColor.find(x => x.paramName === 'RGB Color'):(this.fdNeonType === 'rgbGradient' ? allColor.find(x => x.isNeonColorful): '')
							newValue.fontPosttion.forEach((item, index) => {
								this.selectedFontData.push({
									"Select Font": getChild(
										allFamily,
										"paramName",
										item,
										"fontFamily"
									),
									"Select Color": fdDefColor || getChild(
										allColor,
										"paramCode",
										item,
										"color"
									),
									"Your Text": temp[index],
									placeholder: item.text,
									textCount: item.textCount ? item.textCount : 9999,
									textAlign: "left",
									tubeColor,
								});
							});
							this.changeTemplateFlag = false;
						}
					}
					if (this.textDesign && this.fromSmallNeonText)
						this.selectedFontData[0]["Your Text"] = this.fromSmallNeonText;
					this.selectedData["Your Text"] = this.selectedFontData;
				}
			},
		},
		//不可选参数
		noChoiceData: {
			handler(newValue, oldValue) {
				this.generalData.forEach((x) => {
					x.childList = x.childList.map((y) => {
						y.isHidden = 0;
						newValue.forEach((z) => {
							if (y.priceInfo.id == z) {
								if (y.paramName == this.selectedData[x.paramName]?.paramName) {
									this.selectedData[x.paramName] = null;
								}
								y.isHidden = 1;
								// this.$set(y, "isHidden", true);
							}
						});
						return y;
					});
				});
			},
			immediate: true,
		},

		// dialogVisible: {
		//     handler(newValue, oldValue) {
		//         this.$nextTick(() => {
		//             const dom = document.querySelector("#o2oApp");
		//             if (!dom) return;
		//             if (newValue) {
		//                 this.shutMask();
		//                 dom.style.height = "100vh";
		//                 dom.style.overflowY = "hidden";
		//             } else {
		//                 dom.style.height = "";
		//                 dom.style.overflowY = "scroll";
		//             }
		//         });
		//     },
		// },
		currentStep: {
			handler(newValue) {
				if (this.tabName == "Upload") return;
				if (newValue === "Your Text" || newValue === "Select Size")
					this.isFlow = true;
				else this.isFlow = false;
			},
		},
		hasChinese: {
			handler(newValue) {
				this.selectedData["Select Size"] = null;
			},
			immediate: true,
		},
		finaSize: {
			handler(newValue) {
				let backboard = this.getInfo("Select Backboard");
				if (backboard) {
					let flag = this.showAcrglicBackboard(newValue);
					backboard.childList.forEach((x) => {
						if (x.paramName == "Acrglic Stand") {
							x.isHidden2 = !flag;
						}
					});
					if (
						!flag &&
						this.selectedData["Select Backboard"]?.paramName == "Acrglic Stand"
					)
						this.selectedData["Select Backboard"] = null;
				}
			},
			deep: true,
		},
		async zoomPic(val) {
			let getVideoMsg = (url) => {
				return new Promise((resolve) => {
					let videoElement = document.createElement("video");
					videoElement.src = url;
					videoElement.addEventListener("loadedmetadata", function () {
						resolve({
							duration: videoElement.duration,
							height: videoElement.videoHeight,
							width: videoElement.videoWidth,
						});
					});
				});
			};
			let getImgInfo = (url) => {
				return new Promise((resolve) => {
					let img = new Image();
					img.src = url;
					img.onload = function () {
						resolve({
							height: img.width,
							width: img.height,
						});
					};
				});
			};
			if (getFileSuffix(this.zoomPic) === ".mp4") {
				let videoInfo = await getVideoMsg(val);
				this.zoomAspectRatio = videoInfo.width / videoInfo.height;
			} else {
				let msgInfo = await getImgInfo(val);
				this.zoomAspectRatio = msgInfo.height / msgInfo.height;
			}
		},
	},
	created() {
		this.queryIdCur = Number(this.$route.query.id) || Number(this.queryId);
		this.isInquiryCur = this.$route.query.isInquiry || this.isInquiry;
		getInfo({id: this.pid}).then((res) => {
			this.platformProductId = res.data.platformProductId;
		});
		getPriceData({buyType: 7, productCateId: this.pid}).then((res) => {
			this.$store.commit("setMorePriceData", res.data);
		});
	},
	//   677 760
	async mounted() {
		this.$gl.show();
		this.$Bus.$on("clearDiscount", this.clearDiscount);
		this.topH = document.querySelector("#neonTop")?.offsetHeight;
		// 获取基础参数
		let _this = this;
		this.isIframe = !!this.$route.query.type;
		this.fdNeonType = this.$route.query.fdType;
		let fromSmallNeonText = this.$route.query.selectedText;
		let uploadList = this.$route.query.uploadList;
		let aiText = this.$route.query.aiText;
		this.getAll();
		await this.getByPId();
		this.debounceCalcPrice = debounce(this.calculate, 600);
		this.debounceFontSizePrice = debounce(this.getNeonFontSizePrice, 300);
		this.debounceLimitSize = debounce(this.limitSizeInput, 1000);

		// 获取回填参数
		const quoteParam = sessionStorage.getItem("quoteBackParam");
		if (quoteParam) {
			await this.backQuoteParam(quoteParam);
			sessionStorage.removeItem("quoteBackParam");
			setTimeout(async () => {
				/**
				 * 处理切换语言后的交期回填逻辑
				 * 判断当前选中的交期是否在展示的交期列表内，如果没找到，清空当前选中的交互，让用户重新选择
				 */
					//获取交期列表(经过小重量判断之后的)
				let priceParam = await this.getPriceParam();
				let discountParams = this.generalData.find((item) => {
					return item.paramType === "DISCOUNT";
				});
				let list = discountParams?.childList,
					name = discountParams?.paramName;
				let result = await neonApi.calculate(priceParam);
				//当前展示的交期列表
				let timeList = getQuoteTime(list, result.data, this.proType);
				let find = timeList.arr.find((item) => {
					return item.id === this.neonDiscountData.id;
				});
				if (!find) {
					//清空交期
					if (name) {
						this.selectedData[name] = null;
						this.neonDiscountData = null;
					}
				}
				this.debounceCalcPrice()
			}, 3000);
		} else {
			if (this.tabName == "Enter Your Text") {
				const store = JSON.parse(localStorage.getItem("neonBackFillText"));
				if (fromSmallNeonText) {
					this.fromSmallNeonText = decodeURIComponent(fromSmallNeonText);
				} else {
					if (store) {
						this.selectedFontData = store;
						this.selectedData["Your Text"] = store;
					}
				}
			}
			if (uploadList) {
				const uploadListArr = JSON.parse(uploadList);
				uploadListArr.forEach((item) => {
					this.uploadFileList.push(item);
				});
			}
			if (aiText) {
				this.txtFromAi = aiText;
			}
		}

		let scrollTimer;
		let lastScrollTop = 0;

		let scrollDiv = document.querySelector("#neon");

		if (this.device === "mb") {
			scrollDiv.addEventListener("scroll", this.listenScroll);
			this.observeSelectSize();
		}

		scrollDiv.addEventListener("scroll", function (e) {
			const scrollTop = e.target.scrollTop;
			const scrollDirection = scrollTop > lastScrollTop ? "down" : "up";
			const {height} = document
				.querySelector("#neonTop")
				.getBoundingClientRect();
			_this.scrollTop = scrollTop;
			clearTimeout(scrollTimer);
			scrollTimer = setTimeout(function () {
				// 在滚动停止后执行的操作
				if (
					scrollDirection === "down" &&
					scrollTop < height &&
					_this.device != "pc"
				) {
					scrollDiv.scrollTop = height;
				}
			}, 100); // 设置延迟时间，单位为毫秒
			lastScrollTop = scrollTop;
		});

		// 针对iframe弹框报价
		window.addEventListener("message", function (event) {
			if (event.origin === window.origin) {
				// 验证消息来源
				let data = event.data;
				if (data.type === "closeDialog") {
					_this
						.$confirm(_this.lang.tip, _this.lang.hint, {
							confirmButtonText: _this.lang.Confirm,
							cancelButtonText: _this.lang.Cancel,
							type: "warning",
						})
						.then(() => {
							let targetWindow = window.opener || window.parent;
							targetWindow.postMessage(
								{
									type: "closeDialog",
								},
								window.origin
							);
						})
						.catch(() => {
						});
				}
			}
		});
		this.showDialogForUploadFun();
		this.$gl.hide();
	},
	beforeDestroy() {
		this.$Bus.$off("clearDiscount");
	},
};
</script>
<style lang="scss">
.toast-box {
	.toasted {
		width: fit-content !important;
		padding: 0 20px !important;
		margin: 0 auto !important;
	}
}

.cropDialog {
	.base-dialog-model-con {
		@media screen and(max-width: 768px) {
			height: 80% !important;
		}

		.crop-area {
			.cropper-btn {
				.el-button--primary {
					background: linear-gradient(
							90deg,
							rgb(74, 72, 255) 0%,
							rgb(176, 18, 251) 100%
					);
					border: none;
				}
			}

			.crop-tips {
				color: #ff0000;
				width: 80%;
				margin-inline: auto;
			}
		}
	}
}

input[type="range"] {
	-webkit-appearance: none;
	width: 100%;
	height: 5px;
	background: #d3d3d3;
	outline: none;
	border-radius: 5px;
	position: relative;

	&::before {
		content: "";
		position: absolute;
		left: 0;
		width: calc(
			((var(--value) - var(--min)) / (var(--max) - var(--min))) * 100%
		);
		height: 100%;
		background-color: #5544ff;
	}
}

input[type="range"]::-webkit-slider-thumb {
	-webkit-appearance: none;
	width: 12px;
	height: 12px;
	background: #5544ff;
	cursor: pointer;
	border-radius: 50%;
}

/* Slide-fade animation */
.slide-fade-enter-active {
	transition: all 0.3s ease-out;
}

.slide-fade-leave-active {
	transition: all 0.3s cubic-bezier(1, 0.5, 0.8, 1);
}

.slide-fade-enter-from,
.slide-fade-leave-to {
	transform: translateY(-20px);
	opacity: 0;
}
</style>
